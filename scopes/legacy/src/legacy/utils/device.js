import lodashGet from 'lodash/get';

import { formatPointValueText, generatePointValueMapping } from '@manyun/monitoring.model.point';

import { DIErrorTooltip } from '@manyun/dc-brain.legacy.components';
import { STATUS_COLOR__MAP } from '@manyun/dc-brain.legacy.components/status-tag';
import { BLANK_PLACEHOLDER } from '@manyun/dc-brain.legacy.constants';
import {
  DEVICE_ON_OFFLINE_STATUS_KEY_MAP,
  DEVICE_STATUS_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/enums/device';
import { STATUS_MAP } from '@manyun/dc-brain.legacy.constants/statusColor';
import { getStatusByAlarmType } from '@manyun/dc-brain.legacy.utils/statusColor';

/**
 * Given a `deviceType` and a `pointType`, return `pointCode` and `pointAttrs`.
 *
 * @deprecated Migrated to `@manyun/monitoring.util.get-monitoring-data`
 *
 * @param {string} deviceType Original or customized `deviceType`.
 * @param {string} pointType Customized `pointType` in the front-end.
 * @param {string} hardCodedPointCode Hard-coded `pointCode`.
 * @param {object} pointsDefinitionMap
 * @returns {{ pointCode: null | string; pointAttrs: null | object }} `pointCode` and `pointAttrs`
 */
function getPointByType(
  deviceType,
  pointType,
  hardCodedPointCode = null,
  pointsDefinitionMap,
  concretePointCodeMappings
) {
  if (hardCodedPointCode) {
    return {
      pointCode: hardCodedPointCode,
      pointAttrs: lodashGet(pointsDefinitionMap, [deviceType, hardCodedPointCode], null),
    };
  }

  if (pointType && concretePointCodeMappings) {
    const pointCode = lodashGet(concretePointCodeMappings, [deviceType, pointType], null);

    return {
      pointCode,
      pointAttrs: lodashGet(pointsDefinitionMap, [deviceType, pointCode], null),
    };
  }

  return {
    pointCode: null,
    pointAttrs: null,
  };
}

/**
 * @typedef {object} Device
 * @property {string} deviceGuid
 * @property {string} deviceType
 *
 * @typedef {object} Options
 * @property {string} [hardCodedPointCode] Hard-coded `pointCode`.(If you exactly know what you are doing, you can specify a `pointCode` instead of a `pointType`.)
 * @property {string} [pointType]
 * @property {boolean} [reflected=false]
 * @property {boolean} [formatted=false]
 * @property {string[]} [validLimits]
 * @property {boolean} [valueMappingIncluded=false]
 * @property {object} [defaults={}]
 *
 * @typedef {object} DiPointValue
 * @property {string} NAME
 * @property {string} [STATUS_CODE]
 * @property {"on" | "off"} [ON_OFF_STATE]
 *
 * @typedef {object} PointData
 * @property {string} deviceGuid
 * @property {string} deviceType
 * @property {string} name
 * @property {string} pointCode
 * @property {string | number | DiPointValue} value
 * @property {number} originalValue
 * @property {string} formattedText
 * @property {string} unit
 * @property {boolean} disabled
 * @property {string} status
 * @property {ReturnType<generatePointValueMapping>} [valueMapping]
 */

/**
 * @deprecated Migrated to `@manyun/monitoring.util.get-monitoring-data`
 *
 * @param {object} monitoringData 实时数据
 * @param {object} alarmData 告警数据
 * @param {object} pointsDefinitionMap 测点定义（按设备类型）
 * @param {object} [concretePointCodeMappings] 具象测点配置（来自租户配置）
 * @returns { (device: Device, opts: Options) => PointData }
 */
export function generateGetDeviceMonitoringData(
  monitoringData = {},
  alarmData = {},
  pointsDefinitionMap,
  concretePointCodeMappings
) {
  const getAlarmStatus = generateGetDevicePointAlarmStatus(
    alarmData,
    pointsDefinitionMap,
    concretePointCodeMappings
  );

  return (
    { deviceGuid, deviceType },
    {
      hardCodedPointCode,
      pointType,
      reflected = false,
      formatted = false,
      validLimits: defaultValidLimits,
      valueMappingIncluded = false,
      defaults = {},
    }
  ) => {
    const defaultValue = {
      deviceGuid,
      deviceType,
      pointCode: null,
      value: BLANK_PLACEHOLDER,
      formattedText: BLANK_PLACEHOLDER,
      unit: null,
      disabled: true,
      ...defaults,
    };

    defaultValue.status = getAlarmStatus(
      { deviceGuid, deviceType },
      { pointType, hardCodedPointCode, defaultStatus: defaults.status }
    );

    if (reflected) {
      defaultValue.value = {
        NAME: BLANK_PLACEHOLDER,
      };
    }

    const { pointCode, pointAttrs } = getPointByType(
      deviceType,
      pointType,
      hardCodedPointCode,
      pointsDefinitionMap,
      concretePointCodeMappings
    );

    if (pointCode === null) {
      console.error(
        'Point code not found(deviceType=%s, predefinedPointCode=%s)',
        deviceType,
        pointType
      );
      return defaultValue;
    }

    defaultValue.pointCode = pointCode;

    /**
     * @type {DiPointValue|undefined}
     */
    let valueMapping = generatePointValueMapping(defaultValidLimits || []);
    if (pointAttrs) {
      const { name, unit, validLimits, statusCodes, onOffStateMappings } = pointAttrs;
      defaultValue.name = name;
      defaultValue.unit = unit;
      if (validLimits) {
        valueMapping = generatePointValueMapping(validLimits, statusCodes, onOffStateMappings);
        if (valueMappingIncluded) {
          defaultValue.valueMapping = valueMapping;
        }
      }
    }

    const deviceData = lodashGet(monitoringData, deviceGuid);
    if (deviceData === undefined) {
      return defaultValue;
    }

    const { pointValueMap } = deviceData;
    const pointData = lodashGet(pointValueMap, pointCode);

    // 注意：测点值一般是数字，特殊情况下可能是 undefined / null，不能是 ""
    if (
      pointData === undefined ||
      pointData.value === undefined ||
      pointData.value === null ||
      Number.isNaN(pointData.value)
    ) {
      if (formatted) {
        return {
          ...defaultValue,
          formattedText: formatPointValueText(defaultValue),
        };
      }

      return defaultValue;
    }

    const basePointData = {
      ...defaultValue,
      disabled: false,
      ...pointData,
    };

    if (formatted) {
      return {
        ...basePointData,
        formattedText: formatPointValueText(basePointData),
      };
    }

    if (!reflected) {
      return basePointData;
    }

    if (!valueMapping) {
      console.error(
        `No 'VALUE_MAPPING' during reflecting point(${pointCode}) data value of device model(${deviceType}).`
      );
      return basePointData;
    }

    let value = valueMapping[pointData.value];
    if (value === undefined) {
      console.warn(
        `No 'VALUE_MAPPING' of point value(${pointData.value}) of device(${deviceGuid}).`
      );
      value = {
        NAME: <DIErrorTooltip title={pointData.value} />,
      };
    }

    return {
      ...basePointData,
      value,
      originalValue: pointData.value,
    };
  };
}

function generateGetDevicePointAlarmStatus(
  alarmsData = {},
  pointsDefinitionMap = {},
  concretePointCodeMappings = {}
) {
  return (
    { deviceGuid, deviceType },
    { pointType, hardCodedPointCode = null, defaultStatus = STATUS_MAP.DEFAULT } = {}
  ) => {
    const { pointCode } = getPointByType(
      deviceType,
      pointType,
      hardCodedPointCode,
      pointsDefinitionMap,
      concretePointCodeMappings
    );
    if (pointCode === null) {
      return defaultStatus;
    }
    const pointAlarmData = lodashGet(alarmsData, [deviceGuid, 'pointsCount', pointCode], null);

    if (pointAlarmData === null) {
      return defaultStatus;
    }

    const { WARN, ERROR } = pointAlarmData;
    if (ERROR) {
      return getStatusByAlarmType('ERROR');
    }
    if (WARN) {
      return getStatusByAlarmType('WARN');
    }

    return defaultStatus;
  };
}

/**
 * @deprecated Use `@manyun/monitoring.util.get-monitoring-data` instead
 * @param {*} alarmData
 * @returns
 */
export function generateGetDeviceAlarmStatus(alarmData) {
  return (deviceGuid, { defaultStatus = STATUS_MAP.DEFAULT } = {}) => {
    if (!deviceGuid) {
      return defaultStatus;
    }

    const deviceAlarms = alarmData?.[deviceGuid];
    if (!deviceAlarms) {
      return defaultStatus;
    }

    if (deviceAlarms.count.ERROR > 0) {
      return STATUS_MAP.ALARM;
    }
    if (deviceAlarms.count.WARN > 0) {
      return STATUS_MAP.WARNING;
    }

    return defaultStatus;
  };
}

/**
 * 根据设备在CMDB中的状态及告警数据来计算出当前应该优先展示的状态
 * 维修 ->未启用 ->离线 ->告警 ->预警 ->正常
 * @param {object} device
 * @param {STATUS_MAP} alarmStatus
 */
export function getCurrentVisibleDeviceStatus(device, alarmStatus) {
  const status = lodashGet(device, ['operationStatus', 'code']);
  const onOfflineStatus = lodashGet(device, ['onlineStatus', 'code']);

  if (status === DEVICE_STATUS_KEY_MAP.REPAIR) {
    return {
      theme: STATUS_COLOR__MAP.DARK_GREY,
      text: '维修',
    };
  }
  if (status === DEVICE_STATUS_KEY_MAP.OFF) {
    return {
      theme: STATUS_COLOR__MAP.WHITE30,
      text: '未启用',
    };
  }
  if (onOfflineStatus === DEVICE_ON_OFFLINE_STATUS_KEY_MAP.OFF) {
    return {
      theme: STATUS_COLOR__MAP.GREY30,
      text: '离线',
    };
  }
  if (alarmStatus === STATUS_MAP.ALARM) {
    return {
      theme: STATUS_COLOR__MAP.RED,
      text: '告警',
    };
  }
  if (alarmStatus === STATUS_MAP.WARNING) {
    return {
      theme: STATUS_COLOR__MAP.ORANGE,
      text: '预警',
    };
  }
  return {
    theme: STATUS_COLOR__MAP.GREEN,
    text: '正常',
  };
}
