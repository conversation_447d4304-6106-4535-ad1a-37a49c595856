import split from 'lodash/split';

import {
  DEVICE_TYPE_META_TYPE_KEY_MAP,
  SPACE_DEVICE_TYPE_KEY_MAP,
} from '@manyun/dc-brain.legacy.constants/enums/deviceType';
import { SPACE_DIMENSION_KEY_MAP } from '@manyun/dc-brain.legacy.constants/enums/space';

export function oneOfVirtualDeviceType(deviceType) {
  return [
    SPACE_DEVICE_TYPE_KEY_MAP.IDC,
    SPACE_DEVICE_TYPE_KEY_MAP.BLOCK,
    SPACE_DEVICE_TYPE_KEY_MAP.ROOM,
    SPACE_DEVICE_TYPE_KEY_MAP.COLUMN,
    SPACE_DEVICE_TYPE_KEY_MAP.GRID,
  ].includes(deviceType);
}

/**
 * Given a space dimension, return the default `deviceType` if dimension is `DEVICE`
 * or return the corresponding `space deviceType`.
 * @param {SPACE_DIMENSION_KEY_MAP} dimension space dimension
 * @param {string} deviceType default deviceType
 */
export function getSpaceDeviceTypeIfNeeded(dimension, deviceType) {
  if (dimension === SPACE_DIMENSION_KEY_MAP.DEVICE) {
    return deviceType;
  }

  switch (dimension) {
    case SPACE_DIMENSION_KEY_MAP.IDC:
      return SPACE_DEVICE_TYPE_KEY_MAP.IDC;
    case SPACE_DIMENSION_KEY_MAP.BLOCK:
      return SPACE_DEVICE_TYPE_KEY_MAP.BLOCK;
    case SPACE_DIMENSION_KEY_MAP.ROOM:
      return SPACE_DEVICE_TYPE_KEY_MAP.ROOM;
    case SPACE_DIMENSION_KEY_MAP.COLUMN:
      return SPACE_DEVICE_TYPE_KEY_MAP.COLUMN;
    case SPACE_DIMENSION_KEY_MAP.GRID:
      return SPACE_DEVICE_TYPE_KEY_MAP.GRID;
    default:
      return deviceType;
  }
}

export function getDeviceTypeFullMetaName(deviceType, normalizedList, symbol = '>') {
  const code = normalizedList[deviceType]?.parentCode;
  const sec = split(code, DEVICE_TYPE_META_TYPE_KEY_MAP.C1)[1];
  const code2 = normalizedList[sec]?.parentCode;
  const top = split(code2, DEVICE_TYPE_META_TYPE_KEY_MAP.C0)[1];
  const label =
    (normalizedList[top]?.metaName ?? '未知') +
    symbol +
    (normalizedList[sec]?.metaName ?? '未知') +
    symbol +
    (normalizedList[deviceType]?.metaName ?? '未知');
  return label;
}
