const getImagePropsAsync = src =>
  new Promise(resolve => {
    let width = 0;
    let height = 0;

    const img = new Image();
    img.onload = function () {
      width = this.width;
      height = this.height;

      resolve({ width, height });
    };
    img.onerror = function (error) {
      console.error(error);
      resolve({ width, height });
    };
    img.src = src;
  });

export default getImagePropsAsync;
