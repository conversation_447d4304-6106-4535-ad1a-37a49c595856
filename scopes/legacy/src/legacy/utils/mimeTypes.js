import * as mimeTypes from '@manyun/dc-brain.legacy.constants/mimeTypes';

/**
 * Guess file MIME type from a file extension.
 * @param {string} fileExt File extension
 */
export function guessMimeType(fileExt) {
  if (mimeTypes.IMAGE_TYPE_FILE_EXTS.includes(fileExt)) {
    return mimeTypes.IMAGE_TYPE;
  }
  if (mimeTypes.VIDEIO_TYPE_FILE_EXTS.includes(fileExt)) {
    return mimeTypes.VIDEO_TYPE;
  }

  return mimeTypes.UNKNOW_BINARY_FILE;
}
