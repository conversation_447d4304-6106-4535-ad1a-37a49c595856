// 紧急度
const CHANGE_EMERGENCY_LEVEL_KEY_MAP = {
  HIGH: 'HIGH',
  MIDDLE: 'MIDDLE',
  LOW: 'LOW',
};

export const CHANGE_EMERGENCY_LEVEL_TEXT_MAP = {
  [CHANGE_EMERGENCY_LEVEL_KEY_MAP.HIGH]: '高',
  [CHANGE_EMERGENCY_LEVEL_KEY_MAP.MIDDLE]: '中',
  [CHANGE_EMERGENCY_LEVEL_KEY_MAP.LOW]: '低',
};

export const CHANGE_RISK_LEVEL_KEY_MAP = {
  NORMAL_FIRST: 'NORMAL_FIRST',
  NORMAL_SECOND: 'NORMAL_SECOND',
  NORMAL_THIRD: 'NORMAL_THIRD',
  STANDARD: 'STANDARD',
  URGENT: 'URGENT',
};

export const CHANGE_RISK_LEVEL_TEXT_MAP = {
  [CHANGE_RISK_LEVEL_KEY_MAP.NORMAL_FIRST]: '一般变更(一级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.NORMAL_SECOND]: '一般变更(二级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.NORMAL_THIRD]: ' 一般变更(三级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.STANDARD]: '标准变更',
  [CHANGE_RISK_LEVEL_KEY_MAP.URGENT]: '紧急变更',
};

/**
 * 变更单状态
 * @deprecated Use `ChangeTicketState` instead
 */
export const CHANGE_TICKET_STATUS_KEY_MAP = {
  DRAFT: 'DRAFT',
  APPROVING: 'APPROVING',
  WAITING_CHANGE: 'WAITING_CHANGE',
  CHANGING: 'CHANGING',
  IN_SUMMARY: 'IN_SUMMARY',
  WAITING_CLOSE: 'WAITING_CLOSE',
  FINISH: 'FINISH',
  SUMMARY_APPROVING: 'SUMMARY_APPROVING',
  // TERMINATION: 'TERMINATION',
};

export const CHANGE_TICKET_STATUS_TEXT_MAP = {
  [CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]: '草稿',
  [CHANGE_TICKET_STATUS_KEY_MAP.APPROVING]: '申请审批中',
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]: '待变更',
  [CHANGE_TICKET_STATUS_KEY_MAP.CHANGING]: '变更中', //变更执行
  [CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]: '总结',
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]: '待关闭',
  [CHANGE_TICKET_STATUS_KEY_MAP.FINISH]: '结束',
  [CHANGE_TICKET_STATUS_KEY_MAP.SUMMARY_APPROVING]: '总结审批中',
  // [CHANGE_TICKET_STATUS_KEY_MAP.TERMINATION]: '终止', // 不是一个状态了去掉
};
export const CHANGE_TICKET_STATUS_STEP = {
  [CHANGE_TICKET_STATUS_KEY_MAP.DRAFT]: 0,
  [CHANGE_TICKET_STATUS_KEY_MAP.APPROVING]: 1,
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CHANGE]: 2,
  [CHANGE_TICKET_STATUS_KEY_MAP.CHANGING]: 3,
  [CHANGE_TICKET_STATUS_KEY_MAP.IN_SUMMARY]: 4,
  [CHANGE_TICKET_STATUS_KEY_MAP.WAITING_CLOSE]: 5,
  [CHANGE_TICKET_STATUS_KEY_MAP.FINISH]: 6,
  // [CHANGE_TICKET_STATUS_KEY_MAP.TERMINATION]: 3,
};
