import {
  USERS_CREATOR_ROUTE_PATH,
  USERS_ROUTE_PATH,
  USER_ROUTE_PATH,
} from '@manyun/auth-hub.route.auth-routes';
import { EDIT_BPM_ROUTE_PATH, NEW_BPM_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { ON_SITE_MESSAGE_ROUTE_PATH } from '@manyun/notification-hub.route.notification-routes';
import { BASIC_RESOURCES_IDC as IDCS_ROUTE_PATH } from '@manyun/resource-hub.route.resource-routes';

export const LOGIN = '/login';

//#region 运营后台

//新建流程模型
export const BPM_CREATE = NEW_BPM_ROUTE_PATH;
//编辑流程模型
export const BPM_EDIT = EDIT_BPM_ROUTE_PATH;

// 用户列表
export const USER_MANAGE_LIST = USERS_ROUTE_PATH;

// 新建用户
export const USER_MANAGE_NEW = USERS_CREATOR_ROUTE_PATH;

// 查看某个用户的详细信息
export const USER_MANAGE_DETAIL = USER_ROUTE_PATH;

// 用户组列表
export const USER_GROUP_MANAGE_LIST = '/page/auth/user-group/list';

// 查看某个用户组的详细信息
export const USER_GROUP_MANAGE_DETAIL = '/page/auth/user-group/:id';

// 权限列表
export const POWER_LIST = '/page/auth/list';

// 角色列表
export const ROLE_LIST = '/page/auth/role/list';

// 查看某个角色的详细信息
export const ROLE_ITEM_CONFIG = '/page/auth/role/detail';

//#endregion

//#region 配置管理

// 机房
/** @deprecated */
export const BASIC_RESOURCES_IDC = IDCS_ROUTE_PATH;

// 楼栋管理
/**
 * @deprecated Use `@manyun/resource-hub.route.resource-routes` instead
 */
export const BASIC_RESOURCES_BUILDING = '/page/infrastructure/block/list';

// 包间列表
export const ROOM_LIST = '/page/infrastructure/room/list';

// 机柜列表
export const CABINET_LIST = '/page/infrastructure/cabinet/list';

// 导入机柜
export const SPECIFIC_CREATE_CABINET_ITEM = '/page/infrastructure/cabinet/new';

// 设备列表
export const EQUIPMENT_LIST = '/page/infrastructure/device/list';

// 新建设备
// export const SPECIFIC_CREATE_EQUIPMENT_ITEM = '/page/infrastructure/equipment-manage/create';
export const SPECIFIC_CREATE_EQUIPMENT_ITEM = '/page/infrastructure/device/new';

// 编辑设备
export const SPECIFIC_EDIT_EQUIPMENT_ITEM = '/page/infrastructure/device/:id/edit';

// 设备列表
/** @deprecated */
export const SPARE_LIST = '/page/infrastructure/spare/list';

// 新建耗材
export const SPECIFIC_CREATE_SPARE_ITEM = '/page/infrastructure/spare/new';

// 编辑耗材
export const SPECIFIC_EDIT_SPARE_ITEM = '/page/infrastructure/spare/:id/edit';

// 测点列表
// export const DOPOINT_LIST = '/page/dopoint-manage/list';
export const DOPOINT_LIST = '/page/infrastructure/point/list';

//规格管理
export const SPEC_LIST = '/page/infrastructure/spec/list';

//设备类型管理
export const DEVICE_TYPE_LIST = '/page/resoure-configuration/device-type/list';

// 查看某个设备类型的详细信息
export const DEVICE_TYPE_DETAIL = '/page/resoure-configuration/device-type/:id';

//#endregion

// 监控项配置列表
export const MONITOR_ITEM_CONFIG_LIST = '/page/monitor-item-config/list';

// 某个 ID 的监控项配置详情
export const SPECIFIC_MONITOR_ITEM_CONFIG = '/page/monitor-item-config/:configId';

// 机房视角的（电力/暖通）视图
export const IDC_PERSPECTIVE_ON_TOPOLOGY = '/page/topology/:topologyType/:idc';

// 预览某个 ID 的（电力/暖通）视图
export const PREVIEW_SPECIFIC_TOPOLOGY = '/page/boundary-graph-share/:boundary/:idc/:block';

/**
 * 新建电力/暖通拓扑 V2
 */
export const TOPOLOGY_GRAPHIX = '/page/graphix/topology/:topologyType/:idc/:block/:mode';

// #endregion

// 模板组列表
export const TEMPLATE_GROUP_VIEW_LIST = '/page/template-group-view/list';

// 新建模板组
export const CREATE_TEMPLATE_GROUP_CONFIG = '/page/template-group-view/create';

// 编辑模板组配置
export const EDIT_TEMPLATE_GROUP_CONFIG = '/page/template-group-view/:id/edit';

// 生效域配置
export const TEMPLATE_AREA_CONFIG = '/page/template-group-view/area-config';

// 查看模板组配置
export const VIEW_TEMPLATE_GROUP_CONFIG = '/page/template-group-view/:id/view';

// 告警模板编辑页面
export const ALARM_CONFIGURATION_TEMPLATE_EDIT =
  '/page/monitoring/alarm-configuration-template/:id/edit';

// 告警模板详情页面
export const ALARM_CONFIGURATION_TEMPLATE_DETAIL =
  '/page/monitoring/alarm-configuration-template/:id/detail';

// 告警模板列表
export const ALARM_CONFIGURATION_TEMPLATE_LIST =
  '/page/monitoring/alarm-configuration-template/list';

// 告警模板新建
export const ALARM_CONFIGURATION_TEMPLATE_NEW = '/page/monitoring/alarm-configuration-template/new';

//公告通知列表
export const NOTICE_MANAGE_LIST = '/page/notice/list';

//公告通知详情
export const NOTICE_MANAGE_DETAIL = '/page/notice/:id';

// 收敛配置列表
export const MERGED_MONITOR_CONFIG_LIST = '/page/merged-monitor-config/list';

// 新建收敛配置
export const NEW_MERGED_MONITOR_CONFIG = '/page/merged-monitor-config/new';

// 查看某个收敛配置
export const SPECIFIC_MERGED_MONITOR_CONFIG = '/page/merged-monitor-config/:id';

// 编辑某个收敛配置
export const EDIT_SPECIFIC_MERGED_MONITOR_CONFIG = '/page/merged-monitor-config/:id/edit';

// 事件新增
export const EVENT_CENTER_NEW = '/page/event-center/new';

// 事件列表
export const EVENT_CENTER_LIST = '/page/event-center/list';

// 事件详情
export const EVENT_CENTER_DETAIL = '/page/event-center/:id';

// 事件编辑
export const EVENT_CENTER_EDIT = '/page/event-center/:id/edit';

// 元数据配置
/** @deprecated */
export const METADATA_CONFIGURATION = '/page/metadata-configuration';

//#region 变更

export const CHANGE_TEMPLATE_NEW = '/page/change/template/new';

export const CHANGE_TEMPLATE_DETAIL = '/page/change/template/detail/:id';

export const CHANGE_TEMPLATE_EDIT = '/page/change/template/edit/:id';

export const CHANGE_TEMPLATE_LIST = '/page/change/template/list';

export const CHANGE_TICKET_LIST = '/page/change/ticket/list';

export const CHANGE_TICKET_NEW = '/page/change/ticket/new';

export const CHANGE_TICKET_EDIT = '/page/change/ticket/edit/:id';

export const CHANGE_TICKET_CONFIG = '/page/change/ticket/:id/view';

//#endregion

//#region  聚合加工点位配置

/** @deprecated */
export const MERGES_PROCESSED_POINT_CONFIG = '/page/agg-n-cal-point/new';
/** @deprecated */
export const MERGES_PROCESSED_POINT_DETAIL = '/page/agg-n-cal-point';
/** @deprecated */
export const MERGES_PROCESSED_POINT_EDIT = '/page/agg-n-cal-point/:deviceType/:pointCode/edit';

//#endregion

//#region  班次管理

// @deprecated use hrm.route SHIFTS_ROUTE_PATH
export const SHIFT_MANAGEMENT_LIST = '/page/staff-shift-arrangement/shifts';

//#endregion

//#region  班制管理

/** @deprecated */
export const SHIFT_SYS_MANAGEMENT_LIST = '/page/staff-shift-arrangement/shift-systems';

//#endregion

//#region  班组管理

export const DUTY_GROUP_MANAGEMENT_LIST = '/page/staff-shift-arrangement/teams';

//#endregion

//#region  排班规则

export const GROUP_SCHEDULE_MANAGEMENT_LIST = '/page/staff-shift-arrangement/shift-rules';

//#endregion

//#region  考勤组

export const ATT_GROUP_MANAGEMENT_LIST = '/page/staff-shift-arrangement/attendance-groups';

export const ATT_GROUP_MANAGEMENT_CREATE = '/page/staff-shift-arrangement/attendance-groups/new';

export const ATT_GROUP_MANAGEMENT_EDIT = '/page/staff-shift-arrangement/attendance-group/:id/edit';

export const ATT_GROUP_MANAGEMENT_SCHEDULE =
  '/page/staff-shift-arrangement/attendance-groups/:id/shift-schedule/new';

export const ATT_GROUP_MANAGEMENT_SCHEDULE_DETAIL =
  '/page/staff-shift-arrangement/attendance-groups/:id/shift-schedule';

export const ATT_GROUP_MANAGEMENT_SCHEDULE_EDIT =
  '/page/staff-shift-arrangement/attendance-groups/:id/shift-schedule/edit';
//#endregion

//#region  考勤统计-月度汇总， 考勤统计- 每日统计
export const ATTENDANCE_STATISTICS_MONTHLY_AND_DAILY =
  '/page/staff-shift-arrangement/staff/attendance-records/:byFunc';
//#endregion

//#endregion

//#region  考勤统计- 原始记录

/** @deprecated */
export const ATT_STATISTICS_RECORD_LIST =
  '/page/staff-shift-arrangement/staff/clock-on-off-records/history';

//#endregion

export const LOG_LIST = '/page/log-center/operational-log';

//#region  供应商管理

// 厂商管理
export const VENDOR_LIST = '/page/supplier/vendor/list';
// 厂商详情
export const VENDOR_DETAIL = '/page/supplier/vendor/:id';

//型号管理
export const MODEL_LIST = '/page/supplier/model/list';
//新建型号
export const MODEL_NEW = '/page/supplier/model/new';
//#endregion

//#region  巡检配置

// 巡检项配置列表
export const INSPECTION_CONFIG_LIST = '/page/inspection-config/list';

// 巡检项新建
export const INSPECTION_CONFIG_CREATE = '/page/inspection-config/create';

// 巡检项详情
export const INSPECTION_CONFIG_DETAIL = '/page/inspection-config/:id/detail';

// 巡检项编辑
export const INSPECTION_CONFIG_EDIT = '/page/inspection-config/:id/edit';

//#endregion

//#region

// 工单列表
export const TICKET_LIST = '/page/tickets/:type';

// 工单详情
export const TICKET_VIEW = '/page/tickets/:type/:id';

// 工单新建
export const TICKET_CREATE = '/page/tickets/:type/new';

// 工单编辑
export const TICKET_EDIT = '/page/tickets/:type/edit/:id';

//#endregion

//#region  任务中心
export const TASK_CENTER_LIST = '/page/task-center/list';

export const TASK_CENTER_DETAIL = '/page/task-center/:id';

export const TASK_CENTER_CREATE = '/page/task-center/create';

export const TASK_CENTER_EDIT = '/page/task-center/:id/edit';

export const TASK_CENTER_SCHEDULE = '/page/task-center/schedule';

//#endregion

//#region  盘点配置

export const INVENTORY_CONFIG_LIST = '/page/inventory-config/list';

export const INVENTORY_CONFIG_CREATE = '/page/inventory-config/create';

export const INVENTORY_CONFIG_EDIT = '/page/inventory-config/:id/edit';

export const INVENTORY_CONFIG_DETAIL = '/page/inventory-config/:id';

//#endregion

//#region 盘点分析
export const INVENTORY_ANALYTICS_LIST = '/page/tickets/inventory-analytics';
//#endregion

//#region  盘点配置

export const OPERATION_CHECK_CONFIG_LIST = '/page/operation-check-config/list';

export const OPERATION_CHECK_CONFIG_DETAIL = '/page/operation-check-config/detail/:id';

export const OPERATION_CHECK_CONFIG_CREATE = '/page/operation-check-config/create';

export const OPERATION_CHECK_CONFIG_EDIT = '/page/operation-check-config/edit/:id';

//#endregion

//#region  维护配置

// 列表
export const MAINTAIN_CONFIG_LIST = '/page/maintain-config/list';

// 新增
export const MAINTAIN_CONFIG_CREATE = '/page/maintain-config/create';

// 详情
export const MAINTAIN_CONFIG_DETAIL = '/page/maintain-config/detail/:id';

// 编辑
export const MAINTAIN_CONFIG_EDIT = '/page/maintain-config/edit/:id';

// 版本管理
export const VERSION_MANAGE_DEATAIL = '/page/version-manage/detail';

//#endregion

//#region 节点管理
export const SERVER_NODE_LIST = '/page/server-node/list';

/**
 * 采集节点详情
 * @deprecated import it from `@manyun/dc-brain.route.admin-routes` instead.
 */
export const SERVER_NODE_DETAIL = '/page/server-node/detail/:id';

//#endregion

//#region 北向用户管理
export const NORTH_USER_LIST = '/page/north-user/list';

export const NORTH_USER_DETAIL = '/page/north-user/detail/:id';

export const NORTH_USER_NEW = '/page/north-user/new';

//#endregion

// helloworld
export const ALTER_INFO = '/page/alter-info/list';
export const ALTER_INFO_NEW = '/page/alter-info/create';
export const ALTER_INFO_DETAIL = '/page/alter-info/:bizId/detail';

// @deprecated scope/hrm routes SUPPLY_CHECKS_ROUTE_PATH
export const SUPPLY_CHECK = '/page/supply-check/list';

// @deprecated scope/hrm routes SUPPLY_CHECK_DETAIL_ROUTE_PATH
export const SUPPLY_CHECK_DETAIL = '/page/supply-check/:bizId/detail';

// @deprecated use hrm/route SUPPLY_CHECK_NEW_ROUTE_PATH
export const SUPPLY_CHECK_NEW = '/page/supply-check/create';
//#region 待操作工单
export const MINE_TICKET = '/page/mine-tickets';
//#endregion

//#region 工作日配置
export const WORKDAY_MANAGE_LIST = '/page/workday-manage/list';

export const WORKDAY_CREATE = '/page/workday-manage/create';

export const WORKDAY_DETAIL = '/page/workday-manage/detail/:id';

export const WORKDAY_EDIT = '/page/workday-manage/edit/:id';
//#endregion
// 生效域
export const AREA_CONNECT_TEMPLATE = '/page/area-connect-template';

//#region 参观大屏
export const VISITOR_SCREEN = '/page/visitor-screen/:idc';

//#region 人员入室申请管理

export const VISITOR_RECORD_NEW = '/page/visitor-manager/new';

export const VISITOR_BLACKLIST = '/page/visitor-blacklist/list';
//#endregion

//#region
export const APPROVE_CENTER_LIST = '/page/approve-center/list';

//#endregion

//#region 审批
//export const APPROVAL_PROCESS_CONFIG_LIST = '/page/approval-config-process/list';

export const APPROVAL_PROCESS_CONFIG_CREATE = '/page/approval-config-process/create';

export const APPROVAL_PROCESS_CONFIG_DETAIL = '/page/approval-config-process/detail/:processCode';

export const APPROVAL_PROCESS_CONFIG_EDIT = '/page/approval-config-process/edit/:processCode';

export const APPROVAL_SCENES_LIST = '/page/approval-scenes/list';

//#endregion

// 电池组
export const BATTERY_PACK = '/page/battery-pack/:idc';

//#region 采集通道配置
export const CHANNEL_CONFIG_LIST = '/page/channel-config/list';

/**
 * 采集通道详情
 * @deprecated import it from `@manyun/dc-brain.route.admin-routes` instead.
 */
export const CHANNEL_CONFIG_DETAIL = '/page/channel-config/detail/:id';

/**
 * @deprecated import it from `@manyun/monitoring.route.monitoring-routes` instead.
 */
export const CHANNEL_CONFIG_NEW = '/page/channel-config/new';

/**
 * @deprecated import it from `@manyun/monitoring.route.monitoring-routes` instead.
 */
export const CHANNEL_CONFIG_EDIT = '/page/channel-config/edit/:id';

/**
 * @deprecated import it from `@manyun/monitoring.route.monitoring-routes` instead.
 */
export const CHANNEL_CONFIG_ASSOCIATE = '/page/channel-config/associate/:id';

/**
 * @deprecated import it from `@manyun/monitoring.route.monitoring-routes` instead.
 */
export const CHANNEL_CONFIG_IMPORT = '/page/channel-config/import/:id';

//#endregion

// 全部消息
export const INSIDE_NOTICE_LIST = ON_SITE_MESSAGE_ROUTE_PATH;

//#region
// 维保订单编辑页面
export const WARRANTY_MANAGE_EDIT = '/page/warranty-manage/:id/edit';

// 维保订单详情页面
export const WARRANTY_MANAGE_DETAIL = '/page/warranty-manage/:id';

// 维保订单列表
export const WARRANTY_MANAGE_LIST = '/page/warranty-manage/list';

// 维保订单新建
export const WARRANTY_MANAGE_NEW = '/page/warranty-manage/new';

//预维保池列表
export const WARRANTY_POOL_LIST = '/page/warranty-pool/list';
//#endregion

//#region
// 预到货池列表
export const ARRIVAL_ASSET_LIST = '/page/arrival-asset/list';

// 预到货池新建页
export const ARRIVAL_ASSET_NEW = '/page/arrival-asset/new';

// 告警传输记录
export const ALARM_TRANSMISSION_RECORD_LIST = '/page/alarm-transmission-record/list';

//#endregion
