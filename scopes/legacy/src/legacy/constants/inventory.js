import {
  InventoryAnalyticsAllOutlined,
  InventoryAnalyticsExceedOutlined,
  InventoryAnalyticsFailOutlined,
  InventoryAnalyticsInfoErrorOutlined,
  InventoryAnalyticsLossOutlined,
  InventoryAnalyticsNormalOutlined,
  InventoryAnalyticsUnInventoryOutlined,
} from '@manyun/base-ui.icons';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';

// 盘点方式
export const INVENTORY_TYPE_KEY_MAP = {
  AUTO_INVENTORY: 'AUTO_INVENTORY', // 自动盘点
  MANUAL_INVENTORY: 'MANUAL_INVENTORY', // 人工盘点
};
export const INVENTORY_TYPE_TEXT_MAP = {
  [INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY]: '自动盘点',
  [INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY]: '人工盘点',
};
export const INVENTORY_TYPE_OPTIONS = [
  {
    value: INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY,
    label: INVENTORY_TYPE_TEXT_MAP[INVENTORY_TYPE_KEY_MAP.AUTO_INVENTORY],
  },
  {
    value: INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY,
    label: INVENTORY_TYPE_TEXT_MAP[INVENTORY_TYPE_KEY_MAP.MANUAL_INVENTORY],
  },
];

// 盘点子方式
export const INVENTORY_SUB_TYPE_KEY_MAP = {
  DISPLAY: 'DISPLAY', // 明盘
  IMPLICIT: 'IMPLICIT', // 盲盘
};
export const INVENTORY_SUB_TYPE_TEXT_MAP = {
  [INVENTORY_SUB_TYPE_KEY_MAP.DISPLAY]: '明盘',
  [INVENTORY_SUB_TYPE_KEY_MAP.IMPLICIT]: '盲盘',
};
export const INVENTORY_SUB_TYPE_OPTIONS = [
  {
    value: INVENTORY_SUB_TYPE_KEY_MAP.DISPLAY,
    label: INVENTORY_SUB_TYPE_TEXT_MAP[INVENTORY_SUB_TYPE_KEY_MAP.DISPLAY],
  },
  {
    value: INVENTORY_SUB_TYPE_KEY_MAP.IMPLICIT,
    label: INVENTORY_SUB_TYPE_TEXT_MAP[INVENTORY_SUB_TYPE_KEY_MAP.IMPLICIT],
  },
];

// 盘点目标类型
export const INVENTORY_TARGET_TYPE_KEY_MAP = {
  DEVICE_TYPE: 'DEVICE_TYPE', // 设备类型
  ASSET_NO: 'ASSET_NO', // 资产编号
};

// 盘点状态
export const INVENTORY_STATUS_TYPE_KEY_MAP = {
  UN_INVENTORY: 'UN_INVENTORY', //待盘点
  INVENTORY_LOSS: 'INVENTORY_LOSS', //盘亏
  INVENTORY_EXCEED: 'INVENTORY_EXCEED', //盘盈
  INVENTORY_ERROR: 'INVENTORY_ERROR', //属性错误
  INVENTORY_FAIL: 'INVENTORY_FAIL', //盘点失败
  INVENTORY_NORMAL: 'INVENTORY_NORMAL', //正常
};
export const INVENTORY_STATUS_TYPE_VALUE_MAP = {
  [INVENTORY_STATUS_TYPE_KEY_MAP.UN_INVENTORY]: {
    label: '待盘点',
    icon: InventoryAnalyticsUnInventoryOutlined,
  },
  [INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_LOSS]: {
    label: '盘亏',
    icon: InventoryAnalyticsLossOutlined,
  },
  [INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_EXCEED]: {
    label: '盘盈',
    icon: InventoryAnalyticsExceedOutlined,
  },
  [INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_ERROR]: {
    label: '属性错误',
    icon: InventoryAnalyticsInfoErrorOutlined,
  },
  [INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_FAIL]: {
    label: '盘点失败',
    icon: InventoryAnalyticsLossOutlined,
  },
  [INVENTORY_STATUS_TYPE_KEY_MAP.INVENTORY_NORMAL]: {
    label: '正常',
    icon: InventoryAnalyticsNormalOutlined,
  },
};

//盘点统计状态
export const INVENTORY_COUNT_STATUS_KEY_MAP = {
  UN_INVENTORY: 'UN_INVENTORY',
  INVENTORY_LOSS: 'INVENTORY_LOSS',
  INVENTORY_EXCEED: 'INVENTORY_EXCEED',
  INVENTORY_ERROR: 'INVENTORY_ERROR',
  INVENTORY_FAIL: 'INVENTORY_FAIL',
  INVENTORY_NORMAL: 'INVENTORY_NORMAL',
  ALL: 'ALL',
};
export const INVENTORY_COUNT_STATUS_VALUE_MAP = {
  [INVENTORY_COUNT_STATUS_KEY_MAP.ALL]: {
    label: '盘点总量',
    icon: InventoryAnalyticsAllOutlined,
    count: 'inventoryCount',
    type: ['snDevice', 'noSnDevice'],
  },
  [INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_NORMAL]: {
    label: '盘点正常',
    icon: InventoryAnalyticsNormalOutlined,
    count: 'normalCount',
    type: ['snDevice', 'noSnDevice'],
    color: `var(--${prefixCls}-success-color)`,
  },
  [INVENTORY_COUNT_STATUS_KEY_MAP.UN_INVENTORY]: {
    label: '待盘点',
    icon: InventoryAnalyticsUnInventoryOutlined,
    count: 'unInventoryCount',
    type: ['snDevice', 'noSnDevice'],
    color: `var(--${prefixCls}-warning-color)`,
  },
  [INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_FAIL]: {
    label: '盘点失败',
    icon: InventoryAnalyticsFailOutlined,
    count: 'failCount',
    color: `var(--${prefixCls}-error-color)`,
    type: ['snDevice'],
  },
  [INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_EXCEED]: {
    label: '盘盈',
    icon: InventoryAnalyticsExceedOutlined,
    count: 'exceedCount',
    color: `var(--${prefixCls}-error-color)`,
    type: ['snDevice', 'noSnDevice'],
  },
  [INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_LOSS]: {
    label: '盘亏',
    icon: InventoryAnalyticsLossOutlined,
    count: 'lossCount',
    color: `var(--${prefixCls}-error-color)`,
    type: ['snDevice', 'noSnDevice'],
  },
  [INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_ERROR]: {
    label: '属性错误',
    icon: InventoryAnalyticsInfoErrorOutlined,
    count: 'infoErrorCount',
    color: `var(--${prefixCls}-error-color)`,
    type: ['snDevice'],
  },
};
export const INVENTORY_COUNT_STATUS_OPTIONS = [
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.ALL,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.ALL],
  },
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_NORMAL,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_NORMAL],
  },
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.UN_INVENTORY,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.UN_INVENTORY],
  },
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_FAIL,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_FAIL],
  },
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_EXCEED,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_EXCEED],
  },
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_LOSS,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_LOSS],
  },
  {
    key: INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_ERROR,
    value: INVENTORY_COUNT_STATUS_VALUE_MAP[INVENTORY_COUNT_STATUS_KEY_MAP.INVENTORY_ERROR],
  },
];
