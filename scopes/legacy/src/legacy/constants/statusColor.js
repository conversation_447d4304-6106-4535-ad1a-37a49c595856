import { ALARM_TYPE } from '@manyun/dc-brain.legacy.constants';

/**
 * @deprecated Don<PERSON> use this constant anymore, consider using `monitoring/ui/point-renderer` instead
 *
 * @enum {'normal' | 'warning' | 'alarm' | 'alarm-level-1' | 'alarm-level-2' | 'reference' | 'default' | 'no-data' | 'disabled'}
 */
export const STATUS_MAP = {
  NORMAL: 'normal',
  WARNING: 'warning',
  ALARM: 'alarm',
  ALARM_LEVEL_1: 'alarm-level-1',
  ALARM_LEVEL_2: 'alarm-level-2',
  REFERENCE: 'reference',
  DEFAULT: 'default',
  NODATA: 'no-data',
  DISABLED: 'disabled',
  // UNKNOW: 'unknow',
};

/**
 * Mappings of `alarmType` & `status`.
 */
export const ALARM_TYPE_STATUS_MAP = {
  [ALARM_TYPE.WARN]: STATUS_MAP.WARNING,
  [ALARM_TYPE.ERROR]: STATUS_MAP.ALARM,
};

/**
 * @deprecated Donot use this constant anymore, consider using `monitoring/ui/point-renderer` instead
 *
 * color variables are referenced from `src/styles/variables.css`.
 */
export const COLOR_MAP = {
  [STATUS_MAP.NORMAL]: 'success',
  [STATUS_MAP.WARNING]: 'warning',
  [STATUS_MAP.ALARM]: 'error',
  [STATUS_MAP.ALARM_LEVEL_1]: 'red',
  [STATUS_MAP.ALARM_LEVEL_2]: 'magenta',
  [STATUS_MAP.REFERENCE]: 'processing',
  [STATUS_MAP.DEFAULT]: 'unset',
  [STATUS_MAP.NODATA]: 'default',
  [STATUS_MAP.DISABLED]: 'default',

  // See `monitoring/ui/point-renderer`
  'out-of-range': 'secondary',
  'comms-interrupted': 'secondary',
};
