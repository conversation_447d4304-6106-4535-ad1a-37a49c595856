export const IMAGE_TYPE = 'image/*';

export const AUDIO_TYPE = 'audio/*';

export const VIDEO_TYPE = 'video/*';

/**
 * {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types#applicationoctet-stream MDN:application/octet-stream}
 */
export const UNKNOW_BINARY_FILE = 'application/octet-stream';

/**
 * {@link https://developer.mozilla.org/en-US/docs/Web/HTTP/Basics_of_HTTP/MIME_types#Image_types MDN:Image_types}
 */
export const IMAGE_TYPE_FILE_EXTS = [
  '.apng',
  '.bmp',
  '.git',
  '.ico',
  '.cur',
  '.jpg',
  '.jpeg',
  '.jfif',
  '.pjpeg',
  '.pjp',
  '.png',
  '.svg',
  '.webp',
];

/**
 * {@link https://developer.mozilla.org/en-US/docs/Web/Media/Formats/Video_codecs#Recommendations_for_everyday_videos MDN:Recommendations for everyday videos}
 */
export const VIDEIO_TYPE_FILE_EXTS = ['.mp4', '.webm'];
