export const POINT_TYPE_CODE_MAP = {
  // 原始点位
  ORI: 'ORI',
  // 加工点位（空间）
  CAL_SPACE: 'CAL_SPACE',
  // 聚合点位（空间）
  AGG_SPACE: 'AGG_SPACE',
  // 加工点位（设备内）
  CAL_DEVICE: 'CAL_DEVICE',
  // 聚合点位（设备内）
  AGG_DEVICE: 'AGG_DEVICE',
  // 自定义点位
  CUSTOM: 'CUSTOM',
};

export const POINT_TYPES = [
  {
    key: POINT_TYPE_CODE_MAP.ORI,
    value: '原始测点',
  },
  {
    key: POINT_TYPE_CODE_MAP.CAL_SPACE,
    value: '跨设备类型加工',
  },
  {
    key: POINT_TYPE_CODE_MAP.AGG_SPACE,
    value: '跨设备类型聚合',
  },
  {
    key: POINT_TYPE_CODE_MAP.CAL_DEVICE,
    value: '设备类型内加工',
  },
  {
    key: POINT_TYPE_CODE_MAP.AGG_DEVICE,
    value: '设备类型内聚合',
  },
];

export const POINT_DATA_TYPE_CODE_MAP = {
  AI: 'AI',
  AO: 'AO',
  DI: 'DI',
  DO: 'DO',
  ALARM: 'ALARM',
};

export const POINT_PRECISION = [1, 0.1, 0.01, 0.001, 0.0001];
