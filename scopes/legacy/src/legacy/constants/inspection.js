// 巡检类型
export const INSPECTION_TYPE_KEY_MAP = {
  DEVICE: 'DEVICE', // 设备巡检
  ENVIRONMENT: 'ENVIRONMENT', // 空间巡检
};
export const INSPECTION_TYPE_TEXT_MAP = {
  [INSPECTION_TYPE_KEY_MAP.DEVICE]: '设备',
  [INSPECTION_TYPE_KEY_MAP.ENVIRONMENT]: '空间',
};
export const INSPECTION_TYPE_OPTIONS = [
  {
    value: INSPECTION_TYPE_KEY_MAP.DEVICE,
    label: INSPECTION_TYPE_TEXT_MAP[INSPECTION_TYPE_KEY_MAP.DEVICE],
  },
  {
    value: INSPECTION_TYPE_KEY_MAP.ENVIRONMENT,
    label: INSPECTION_TYPE_TEXT_MAP[INSPECTION_TYPE_KEY_MAP.ENVIRONMENT],
  },
];

// 巡检项类型
export const INSPECTION_CONFIG_TYPE_KEY_MAP = {
  POINT: 'POINT', // 测点巡检项
  CUSTOM: 'CUSTOM', // 自定义巡检项
};
export const INSPECTION_CONFIG_TYPE_TEXT_MAP = {
  [INSPECTION_CONFIG_TYPE_KEY_MAP.POINT]: '测点巡检项',
  [INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM]: '自定义巡检项',
};

export const INSPECTION_CONFIG_TYPE_OPTIONS = [
  {
    value: INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM,
    label: INSPECTION_CONFIG_TYPE_TEXT_MAP[INSPECTION_CONFIG_TYPE_KEY_MAP.CUSTOM],
  },
  {
    value: INSPECTION_CONFIG_TYPE_KEY_MAP.POINT,
    label: INSPECTION_CONFIG_TYPE_TEXT_MAP[INSPECTION_CONFIG_TYPE_KEY_MAP.POINT],
  },
];
