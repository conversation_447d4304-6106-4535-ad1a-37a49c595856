import { REQUEST_RNTRY_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import {
  CUSTOMERS_ROUTE_PATH,
  CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH,
  SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH,
} from '@manyun/crm.route.crm-routes';
import { COURSES_ROUTE_PATH } from '@manyun/knowledge-hub.page.courses';
import { COURSEWARES_ROUTE_PATH } from '@manyun/knowledge-hub.page.coursewares';
import { EXAMS_ROUTE_PATH } from '@manyun/knowledge-hub.page.exams';
import { MY_COURSES_ROUTE_PATH } from '@manyun/knowledge-hub.page.my-courses';
import { MY_EXAMS_PAGE_URL } from '@manyun/knowledge-hub.page.my-exams';
import { PAPERS_ROUTE_PATH } from '@manyun/knowledge-hub.page.papers';
import { PENDING_MARK_EXAMS_ROUTE_PATH } from '@manyun/knowledge-hub.page.pending-mark-exams';
import { QUESTIONS_ROUTE_PATH } from '@manyun/knowledge-hub.page.questions';
import { SKILLS_PAGE_URL } from '@manyun/knowledge-hub.page.skills';
import { WIKIS_ROUTE_PATH } from '@manyun/knowledge-hub.page.wikis';
import { TRAIN_PLANS_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH } from '@manyun/notification-hub.page.notification-channels-config';
import { generateOnsiteMessageUrl } from '@manyun/notification-hub.route.notification-routes';
import { VISITS_RECORDS_ROUTE_PATH } from '@manyun/sentry.route.routes';

import * as urls from '@manyun/dc-brain.legacy.constants/urls';
import * as urlsUtil from '@manyun/dc-brain.legacy.utils/urls';

export const ANY_REPORTS_MENU_CODE_PREFIX = 'menu_reports--';
export const ANY_REPORTS_MENU_CODE = ANY_REPORTS_MENU_CODE_PREFIX + '*';

/**
 * 菜单配置
 *
 * `{ [menuId: string]: { icon: string|null; link: string|null }}`
 */
export const MENU_CONFIG_MAP = {
  lord: {
    icon: 'key',
  },
  menu_roles: {
    link: urls.ROLE_LIST,
  },
  'menu_user-groups': {
    link: urls.USER_GROUP_MANAGE_LIST,
  },
  menu_users: {
    link: urls.USER_MANAGE_LIST,
  },
  menu_permissions: {
    link: urls.POWER_LIST,
  },
  resource_center: {
    icon: 'petals',
  },
  basic_resource_idc_management: {
    link: urls.BASIC_RESOURCES_IDC,
  },
  basic_resource_building_management: {
    link: urls.BASIC_RESOURCES_BUILDING,
  },
  basic_resource_room_management: {
    link: urls.ROOM_LIST,
  },
  basic_resource_cabinet_management: {
    link: urls.CABINET_LIST,
  },
  basic_resource_equipment_management: {
    link: urls.EQUIPMENT_LIST,
  },
  infrastructure_point: {
    link: urls.DOPOINT_LIST,
  },
  mergerd_processed_point: {
    link: urls.MERGES_PROCESSED_POINT_DETAIL,
  },
  spec_management: {
    link: urls.SPEC_LIST,
  },
  'operation-center': {
    icon: 'processing',
  },
  'change_template--list': {
    link: '/page/change/template/list',
  },
  'change_ticket--list': {
    link: '/page/change/ticket/list',
  },
  menu_events: {
    link: '/page/event-center/list',
  },
  configuration_center: {
    icon: 'cards',
  },
  metadata_configuration: {
    link: urls.METADATA_CONFIGURATION,
  },

  inspection_configuration: {
    link: urls.INSPECTION_CONFIG_LIST,
  },
  inventory_configuration: {
    link: urls.INVENTORY_CONFIG_LIST,
  },
  operation_check_configuration: {
    link: urls.OPERATION_CHECK_CONFIG_LIST,
  },
  maintain_configuration: {
    link: urls.MAINTAIN_CONFIG_LIST,
  },
  monitoring_configuration: {
    icon: 'gear',
  },
  alarm_template_configuration: {
    link: urls.ALARM_CONFIGURATION_TEMPLATE_LIST,
  },
  alarm_template_group_configuration: {
    link: urls.TEMPLATE_GROUP_VIEW_LIST,
  },
  convergence_configuration: {
    link: urls.MERGED_MONITOR_CONFIG_LIST,
  },
  device_type_management: {
    link: urls.DEVICE_TYPE_LIST,
  },
  model_management: {
    link: urls.MODEL_LIST,
  },
  personnel_management: {
    icon: 'userConfig',
  },
  shift_management: {
    link: urls.SHIFT_MANAGEMENT_LIST,
  },
  shift_sys_management: {
    link: urls.SHIFT_SYS_MANAGEMENT_LIST,
  },
  duty_group_management: {
    link: urls.DUTY_GROUP_MANAGEMENT_LIST,
  },
  group_schedule_management: {
    link: urls.GROUP_SCHEDULE_MANAGEMENT_LIST,
  },
  att_group_management: {
    link: urls.ATT_GROUP_MANAGEMENT_LIST,
  },
  att_statistics_byMonth: {
    link: urlsUtil.generateAttendanceStatisticsLocation({ byFunc: 'byMonth' }),
  },
  att_statistics_byDay: {
    link: urlsUtil.generateAttendanceStatisticsLocation({ byFunc: 'byDay' }),
  },
  record_management: {
    link: urls.ATT_STATISTICS_RECORD_LIST,
  },
  'notification-center': {
    icon: 'notifications',
  },
  notice: {
    link: urls.NOTICE_MANAGE_LIST,
  },
  'log-center': {
    icon: 'Logs',
  },
  'menu_operational-log': {
    link: urls.LOG_LIST,
  },
  'menu_supplier-center': {
    icon: 'shop-config',
  },
  vendor_management: {
    link: urls.VENDOR_LIST,
  },
  'ticket-center': {
    icon: 'file-done',
  },
  'ticket-center_chores--repair': {
    link: urlsUtil.generateTicketListUrl({ type: 'repair' }),
  },
  'ticket-center_chores--inspection': {
    link: urlsUtil.generateTicketListUrl({ type: 'inspection' }),
  },
  'ticket-center_chores--visitor': {
    link: urlsUtil.generateTicketListUrl({ type: 'visitor' }),
  },
  'ticket-center_assets--warehouse': {
    link: urlsUtil.generateTicketListUrl({ type: 'warehouse' }),
  },
  'ticket-center_assets--inventory': {
    link: urlsUtil.generateTicketListUrl({ type: 'inventory' }),
  },
  'ticket-center_assets--inventory_analysis': {
    link: urls.INVENTORY_ANALYTICS_LIST,
  },
  'ticket-center_devices--power': {
    link: urlsUtil.generateTicketListUrl({ type: 'power' }),
  },
  'ticket-center_devices--on_off': {
    link: urlsUtil.generateTicketListUrl({ type: 'on_off' }),
  },
  'ticket-center_devices--maintenance': {
    link: urlsUtil.generateTicketListUrl({ type: 'maintenance' }),
  },
  'ticket-center_currency--access_card_auth': {
    link: urlsUtil.generateTicketListUrl({ type: 'access_card_auth' }),
  },

  task_center: {
    icon: 'closed-cycle',
  },
  task_list: {
    link: urls.TASK_CENTER_LIST,
  },
  task_schedule: {
    link: urls.TASK_CENTER_SCHEDULE,
  },
  page_customer_service: {
    link: CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH,
  },
  'page_srm_service-entries': {
    link: SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH,
  },
  'ticket-center_assets--access': {
    link: urlsUtil.generateTicketListUrl({ type: 'access' }),
  },
  'schedule-alter': {
    link: urls.ALTER_INFO,
  },
  'supply-check': {
    link: urls.SUPPLY_CHECK,
  },
  version_manage: {
    link: urls.VERSION_MANAGE_DEATAIL,
  },
  mine_ticket: {
    link: urls.MINE_TICKET,
  },
  work_manage_list: {
    link: urls.WORKDAY_MANAGE_LIST,
  },
  area_connect_template: {
    link: urls.AREA_CONNECT_TEMPLATE,
  },
  'security-center': {
    icon: 'security',
  },
  'security-center_chores--visitor_record': {
    link: VISITS_RECORDS_ROUTE_PATH,
  },
  'security-center_chores--visitor_blacklist': {
    link: urls.VISITOR_BLACKLIST,
  },
  service_center: {
    icon: 'globe',
  },
  approve_center: {
    icon: 'stamp',
  },
  approve_center_mine_process_list: {
    link: urls.APPROVE_CENTER_LIST,
  },

  spare_management: {
    link: urls.SPARE_LIST,
  },

  // process_config: {
  //   link: urls.APPROVAL_PROCESS_CONFIG_LIST,
  // },

  biz_scenes: {
    link: urls.APPROVAL_SCENES_LIST,
  },
  sys_manager: {
    icon: 'cards',
  },
  'menu_sys-config': {
    icon: 'shop-config',
  },
  ecc_screen: {
    link: urls.VISITOR_SCREEN,
  },
  battery_unit_view: {
    link: urls.BATTERY_PACK,
  },
  server_node_list: {
    link: urls.SERVER_NODE_LIST,
  },
  north_user_list: {
    link: urls.NORTH_USER_LIST,
  },
  channel_config_list: {
    link: urls.CHANNEL_CONFIG_LIST,
  },
  contract_center: {
    icon: 'Lease',
  },
  'menu_cost-center': {
    icon: 'Cost',
  },
  page_inside_notice_list: {
    link: generateOnsiteMessageUrl({ state: 'unread', type: 'all' }),
  },
  'page_warranty-order': {
    link: urls.WARRANTY_MANAGE_LIST,
  },
  'page_warranty-pool': {
    link: urls.WARRANTY_POOL_LIST,
  },
  'menu_finance-cost-center': {
    icon: 'CostCenter',
  },

  'ticket-center_devices--device_general': {
    link: urlsUtil.generateTicketListUrl({ type: 'device_general' }),
  },
  'ticket-center_assets--accept': {
    link: urlsUtil.generateTicketListUrl({ type: 'accept' }),
  },
  page_arrival_asset: {
    link: urls.ARRIVAL_ASSET_LIST,
  },
  page_alarm_transmission_record: {
    link: urls.ALARM_TRANSMISSION_RECORD_LIST,
  },

  /** 报表中心 */
  'menu_reports-hub': {
    icon: 'line-chart',
  },

  /** 知识中心 */
  menu_knowledge_center: {
    icon: 'open-book',
  },

  /**题库管理 */
  'page_knowledge_hub-questions': {
    link: QUESTIONS_ROUTE_PATH,
  },

  /**试卷管理 */
  'page_knowledge_hub-papers': {
    link: PAPERS_ROUTE_PATH,
  },

  /**考试管理 */
  'page_knowledge_hub-exams': {
    link: EXAMS_ROUTE_PATH,
  },

  /**可判卷列表 */
  'page_knowledge_hub-pending-mark-exams': {
    link: PENDING_MARK_EXAMS_ROUTE_PATH,
  },

  /**我的考试 */
  'page_knowledge_hub-my-exams': {
    link: MY_EXAMS_PAGE_URL,
  },

  /**课程列表 */
  'page_knowledge_hub-courses': {
    link: COURSES_ROUTE_PATH,
  },
  /**培训计划管理 */
  'menu_knowledge_hub-train-plan-manage': {
    link: TRAIN_PLANS_ROUTE_PATH,
  },
  /**我的课程 */
  'page_knowledge_hub-my-courses': {
    link: MY_COURSES_ROUTE_PATH,
  },
  /**课件列表 */
  'page_knowledge_hub-coursewares': {
    link: COURSEWARES_ROUTE_PATH,
  },
  /**知识库 */
  'page_knowledge_hub-wikis': {
    link: WIKIS_ROUTE_PATH,
  },
  /** 技能列表 */
  'page_knowledge_hub-skills': {
    link: SKILLS_PAGE_URL,
  },
  [ANY_REPORTS_MENU_CODE]: {
    // TODO @Jerry 使用 `redash dashboard page route`
    link: ({ dashboardId }) => '/page/reports/dashboard/:id'.replace(':id', dashboardId),
  },
  /** 客户中心 */
  menu_crm: {
    icon: 'Customers',
  },

  /** 客户列表 */
  page_crm_customers: {
    link: CUSTOMERS_ROUTE_PATH,
  },

  /** 消息接收配置 */
  'page_notification-hub_notification-channels-config': {
    link: NOTIFICATION_CHANNEL_CONFIG_ROUTE_PATH,
  },

  /**通用审批 */
  'page_bpm-requests-entry': {
    link: REQUEST_RNTRY_ROUTE_PATH,
  },
};
