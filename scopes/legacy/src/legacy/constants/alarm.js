export const ALARM_LEVEL = [
  {
    key: 1,
    value: 1,
  },
  {
    key: 2,
    value: 2,
  },
  {
    key: 3,
    value: 3,
  },
  {
    key: 4,
    value: 4,
  },
  {
    key: 5,
    value: 5,
  },
];

export const AVAI_LABLE = [
  {
    key: true,
    value: '启用',
  },
  {
    key: false,
    value: '停用',
  },
];

export const TREE_NODE_POINT_TYPE = 'NAMENODE';
export const TREE_EQUIPMENTNODE_POINT_TYPE = 'EQUIPMENTNODE';
export const TREE_NODE_TEMPLATE_NAME = 'TEMPLATENAME';
export const TREE_NODE_TEMPLATE_LIST = 'TEMPLATELIST';

/**
 * @enum {'NULL'|'gt'|'ge'|'lt'|'le'}
 */
export const LIMIT_VALUE_MAP = {
  NULL: 'NULL',
  GREATER_THAN: 'gt',
  GREATER_THAN_OR_EQUALS: 'ge',
  LESS_THAN: 'lt',
  LESS_THAN_OR_EQUALS: 'le',
};

export const LIMIT_TEXT_MAP = {
  [LIMIT_VALUE_MAP.NULL]: '无',
  [LIMIT_VALUE_MAP.GREATER_THAN]: '>',
  [LIMIT_VALUE_MAP.GREATER_THAN_OR_EQUALS]: '≥',
  [LIMIT_VALUE_MAP.LESS_THAN]: '<',
  [LIMIT_VALUE_MAP.LESS_THAN_OR_EQUALS]: '≤',
};

export const BOTTOM_LIMMIT = [
  {
    label: '无',
    value: 'NULL',
  },
  {
    label: '>',
    value: 'gt',
  },
  {
    label: '≥',
    value: 'ge',
  },
];

export const TOP_LIMMIT = [
  {
    label: '无',
    value: 'NULL',
  },
  {
    label: '<',
    value: 'lt',
  },
  {
    label: '≤',
    value: 'le',
  },
];

export const DRAG_TAG_TYPE = 'DRAGTAGTYPE';

export const TEMPLATE_VIEW_TREE = 'TEMPLATEVIEWTREE';

export const TEMPLATE_VIEW_MODE_TYPE = {
  PREVIEW: 'preview',
  CUSTOM_VIEW: 'customView',
  NEW: 'new',
  EDIT: 'edit',
  CUSTOM_NEW: 'customNew',
  CUSTOM_EDIT: 'customEdit',
};

export const ALARM_TRANSMISSION_RECORD_STATUS_MAPPINGS = {
  open: '触发',
  'in progress': '处理中',
  resolved: '恢复',
};
