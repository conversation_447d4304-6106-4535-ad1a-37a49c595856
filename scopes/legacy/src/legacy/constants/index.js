export const BLANK_PLACEHOLDER = '--';

// 服务端定义的告警类型
export const ALARM_TYPE = {
  WARN: 'WARN', // 预警
  ERROR: 'ERROR', // 告警
};

// 服务端定义的收敛范围
export const MERGE_SCOPE_KEY_MAP = {
  WITHIN: 'WITHIN',
  DOWN: 'DOWN',
  ALL: 'ALL',
};

export const MERGE_SCOPE_TEXT_MAP = {
  [MERGE_SCOPE_KEY_MAP.WITHIN]: '本设备内',
  [MERGE_SCOPE_KEY_MAP.DOWN]: '下联关系',
  [MERGE_SCOPE_KEY_MAP.ALL]: '不限制',
};

export const MERGE_SCOPE_OPTIONS = [
  {
    label: MERGE_SCOPE_TEXT_MAP[MERGE_SCOPE_KEY_MAP.ALL],
    value: MERGE_SCOPE_KEY_MAP.ALL,
  },
  {
    label: MERGE_SCOPE_TEXT_MAP[MERGE_SCOPE_KEY_MAP.WITHIN],
    value: MERGE_SCOPE_KEY_MAP.WITHIN,
  },
  {
    label: MERGE_SCOPE_TEXT_MAP[MERGE_SCOPE_KEY_MAP.DOWN],
    value: MERGE_SCOPE_KEY_MAP.DOWN,
  },
];

// 告警文案配置-自定义
export const CUSTOMIZED_NOTIFICATION_TEXT = 'CUSTOMIZED_TEXT';

export const YES_OR_NO_KEY_MAP = {
  YES: 'alwaysTrue',
  NO: '',
};

export const YES_OR_NO_TEXT_MAP = {
  YES: '需要',
  NO: '不需要',
};

export const ENABLED_OR_DISABLED_TEXT_MAP = {
  ENABLED: '启用',
  DISABLED: '停用',
};

// 可用于 Select, RadioGroup, CheckboxGroup
export const YES_OR_NO_OPTIONS = [
  {
    label: YES_OR_NO_TEXT_MAP.YES,
    value: YES_OR_NO_KEY_MAP.YES,
  },
  {
    label: YES_OR_NO_TEXT_MAP.NO,
    value: YES_OR_NO_KEY_MAP.NO,
  },
];

export const ENABLED_OR_DISABLED_OPTIONS = [
  {
    label: ENABLED_OR_DISABLED_TEXT_MAP.ENABLED,
    value: YES_OR_NO_KEY_MAP.YES,
  },
  {
    label: ENABLED_OR_DISABLED_TEXT_MAP.DISABLED,
    value: YES_OR_NO_KEY_MAP.NO,
  },
];

/** @deprecated Replaced by `type TopologyType` from package `@manyun/dc-brain.config.base` */
export const TOPOLOGY_TYPE_KEY_MAP = {
  /** 电力拓扑 */
  ELECTRIC_POWER: 'ELECTRIC_POWER',
  /** 暖通拓扑 */
  HVAC: 'HVAC',
  /** 设施包间拓扑 */
  ROOM_FACILITY: 'ROOM_FACILITY',
  /**
   * 燃油系统图
   */
  FUEL_SYSTEM: 'FUEL_SYSTEM',
};

// 订阅类型
export const SUBSCRIPTION_TYPE = {
  ALARM_SUBSCRIBE: 'ALARM_SUBSCRIBE',
  EVENT_SUBSCRIBE: 'EVENT_SUBSCRIBE',
};

export const METADATA_TYPE = {
  EVENT_TOP_CATEGORY: 'EVENT_TOP_CATEGORY', //事件大类
  EVENT_SECOND_CATEGORY: 'EVENT_SECOND_CATEGORY', // 事件小类
  REASON_TYPE: 'REASON_TYPE', //原因类型
  RESPONSIBLE_SECTOR: 'RESPONSIBLE_SECTOR', // 责任部门
  EVENT_SOURCE: 'EVENT_SOURCE', //事件来源
  SPEC_NAME: 'SPEC_NAME', //设备规格名称
  SPEC_UNIT: 'SPEC_UNIT', //设备规格单位
  CHANGE: 'CHANGE', //变更类型
  CHANGE_REASON: 'CHANGE_REASON', //变更专业
  IN_WAREHOUSE_REASON: 'IN_WAREHOUSE_REASON', // 入库原因
  EX_WAREHOUSE_REASON: 'EX_WAREHOUSE_REASON', //出库原因
  ROOM_TYPE: 'ROOM_TYPE', // 包间类型
  ADJUST_TYPE: 'ADJUST_TYPE', //对账类型
  DEVICE_GENERAL: 'DEVICE_GENERAL',
  VISITOR: 'VISITOR',
  ACCESS_CARD_TOP_CATEGORY: 'ACCESS_CARD_TOP_CATEGORY',
  ACCESS_CARD_SECOND_CATEGORY: 'ACCESS_CARD_SECOND_CATEGORY',
  CUSTOMER_LEVEL: 'CUSTOMER_LEVEL',
  INDUSTRY: 'INDUSTRY',
  JOB_LABEL: 'JOB_LABEL',
  POSITION_YG: 'POSITION_YG',
};

export const SUBSCRIBE_CHANNELS_LABEL_KEY = {
  VOICE: '电话',
  SMS: '短信',
  // APP: 'APP',
  WEB_HOOK: 'Webhook',
  EMAIL: '邮件',
  INTERNAL_MESSAGE: '站内信',
};
