/**
 * @deprecated 需要重构使用 `redux store` 中的租户配置
 */
export const SPACE_DEVICE_TYPE_KEY_MAP = {
  IDC: '90101',
  BLOCK: '90102',
  ROOM: '90103',
  COLUMN: '90104',
  GRID: '90105',
};

export const DIMENSION_TYPE = getDimensionType();

/**
 * @enum {'C0'|'C1'|'C2'}
 */
export const DEVICE_TYPE_META_TYPE_KEY_MAP = {
  C0: 'C0', // 一级
  C1: 'C1', // 二级
  C2: 'C2', // 三级
};

/**
 * @enum {'SPACE'|'DEVICE'}
 */
export const DEVICE_SPACE_TYPE_KEY_MAP = {
  SPACE: 'SPACE',
  DEVICE: 'DEVICE',
  IT: 'IT',
};

function getDimensionType() {
  const params = {};
  for (const [name, value] of Object.entries(SPACE_DEVICE_TYPE_KEY_MAP)) {
    params[value] = name;
  }
  return params;
}
