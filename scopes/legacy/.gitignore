# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# testing
/coverage

# production
**/.bit/*
!**/.bit/scope.json
/build
cloud-ui.tar.gz
docker/legacy-app
dc-brain-web.tar

# misc
.DS_Store
!docker/.env
!docker/.env.dev
!docker/.env.test
.env.local
.env.development.local
.env.test.local
.env.production
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*
debug.log*
