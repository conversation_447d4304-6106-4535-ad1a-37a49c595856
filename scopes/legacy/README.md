# [LEGACY] Deprecated Warning

# DC BRAIN WEB

缦云 `DC BRAIN` 项目的 `WEB` 端工程。

### 设置本地调试环境变量

在根目录设置 `.env.local` 文件，参考 `.env` 文件

## 测试环境

- [开发环境](http://default.manyun-inc.com)
- [测试环境](http://default.manyun-local.com)
- [预发环境](https://pre.dcbase.cn:8443)

## 构建时所需的环境变量

默认环境变量维护在 `.env` 文件中。构建时可用 `.env.production` 文件覆盖默认值。

## 使用 Debugger for Chrome 在 VSCode 中调试代码

### macOS

- In a terminal, execute `/Applications/Google\ Chrome.app/Contents/MacOS/Google\ Chrome --remote-debugging-port=9222`

### Windows

- Right click the Chrome shortcut, and select properties
- In the "target" field, append `--remote-debugging-port=9222` Or in a command prompt, execute `<path to chrome>/chrome.exe --remote-debugging-port=9222` 例如：`C:\\Program Files (x86)\\Google\\Chrome\\Application\\chrome.exe --remote-debugging-port=9222`
