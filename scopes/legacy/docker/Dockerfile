FROM nginx:stable-alpine AS base
RUN apk add --no-cache tzdata bash vim
ENV TZ=Asia/Shanghai
RUN echo "alias ll='ls -lt'" >> ~/.bashrc \
  && echo "alias la='ls -la'" >> ~/.bashrc \
  && echo "alias grep='grep --color=auto'" >> ~/.bashrc \
  && echo "alias cp='cp -i'" >> ~/.bashrc \
  && echo "alias mv='mv -i'" >> ~/.bashrc \
  && echo "alias rm='rm -i'" >> ~/.bashrc \
  && echo "alias mkdir='mkdir -p'" >> ~/.bashrc \
  && echo "alias df='df -h'" >> ~/.bashrc \
  && echo "alias du='du -sh'" >> ~/.bashrc \
  && echo "alias ps='ps aux'" >> ~/.bashrc \
  && echo "alias p='ps aux | grep'" >> ~/.bashrc
RUN echo "source ~/.bashrc" >> ~/.bash_profile
RUN ln -sf /usr/bin/vim /usr/bin/vi
COPY entrypoint/21-envsubst-on-html.sh /docker-entrypoint.d/

FROM base AS legacy
COPY nginx/legacy-app.conf /etc/nginx/conf.d/default.conf
COPY nginx/apps/legacy-app-routes.conf /etc/nginx/conf.d/apps/
COPY apps/legacy /usr/share/nginx/html/