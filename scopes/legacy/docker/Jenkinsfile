pipeline {
  agent any

  parameters {
    gitParameter branch: '', branchFilter: 'origin/.*', defaultValue: 'origin/20250626', name: '<PERSON><PERSON><PERSON>', quickFilterEnabled: false, selectedValue: 'DEFAULT', sortMode: 'ASCENDING_SMART', tagFilter: '*', type: 'GitParameterDefinition'
    string description: '目标环境（仅供定时构建插件使用），请留空！！！', name: 'target'
    choice choices: ['7080', '7001', '7002', '7003', '7004', '7005', '7006', '7007', '7008', '7009'], description: '端口', name: 'PORT'
    booleanParam description: '是否跳过构建', name: 'ARG_SKIP_BUILD'
    booleanParam defaultValue: true, description: '是否部署 dc-brain-legacy 构建产物至 OSS', name: 'ARG_DEPLOY_MF_DC_BRAIN_LEGACY'
    booleanParam defaultValue: true, description: '是否部署 dc-brain-web NGINX server', name: 'ARG_DEPLOY_DC_BRAIN_SERVER'
    choice choices: ['compile', 'skip'], description: '准备工作：“compile”: 编译所选 scopes；“skip”：跳过所有 scopes 的编译', name: 'ARG_PREPARE_STEPS'
    booleanParam defaultValue: false, description: '编译所选 scopes 时是否先删除 node_modules', name: 'ARG_CLEAN_INSTALL'
    booleanParam defaultValue: false, description: '是否构建 MF Redash APP（会执行MF部署操作）', name: 'ARG_BUILD_MF_REDASH'
    booleanParam defaultValue: false, description: '是否部署 MF Redash APP 构建产物至 OSS', name: 'ARG_DEPLOY_MF_REDASH'
    string(name: 'FORCE_VERSION', description: '覆盖 Jenkinsfile 中内置的 VERSION')
    choice choices: ['@manyun/dc-brain.config.goc@1.1.11', '@manyun/dc-brain.config.sxdtyg@1.1.11', ''], description: '租户配置包', name: 'CONFIGS_PACKAGE_NAME'
    choice choices: ['on', 'off'], description: '水印', name: 'WATERMARK'
  }

  stages {
    stage('Verify tooling') {
      steps {
        sh '''
          # docker version
          # docker info
          # docker compose version
          node -v
          npm -v
          pnpm -v
          bit -v
          jq --version
        '''
      }
    }

    stage('Select the Environment') {
      when {
        beforeInput true
        not {
          triggeredBy 'ParameterizedTimerTriggerCause'
        }
      }
      input {
        message "Select the environment:"
        parameters {
          choice(name: 'ENVIRONMENT', choices: ['DEV', 'TEST', 'TESTING_YANG_GAO', 'STAGING', 'STAGING_YANG_GAO'])
        }
      }
      steps {
        script {
          env.target = "${ENVIRONMENT}"
        }
        echo "Selected environment: ${ENVIRONMENT}"
      }
    }

    stage('Setup envs') {
      steps {
        script {
          env.REGISTRY = "registry.manyun-inc.com"
          env.SERVICE_NAME = "dc-brain-web"
          env.TAG = "latest"
          env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"
          env.SUBNET = "************/24"
          env.REMOTE_SERVER = ""
          env.ENV_FILE = ""
          env.SSH_CREDENTIALS = ""
          env.NODE_ENV = "development"
          env.BUILD_ENV = "development"
          env.RSA_ENCRYPTION_PUBLIC_KEY = ""
          env.APP_NAME = "GLP DCBASE"
          env.VERSION = params.FORCE_VERSION ? params.FORCE_VERSION : "20250626"
          env.STATIC_DEPLOY_REMOTE = "http://static.manyun-inc.com:9100/oss/graphql"
          env.ZIP_NAME = "dc-brain-legacy"
          env.ZIP_NAME_REDASH = 'redash'

          if (env.target == "DEV") {
            if (params.PORT == "7080") {
              env.PORT = "7010";
            }

            env.TAG = "dev"
            env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"
            env.REMOTE_SERVER = "***********"
            env.ENV_FILE = ".env.dev"
            env.SSH_CREDENTIALS = "dev41_jerry"
          } else if (env.target == "TEST") {
            env.TAG = "test"
            env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"
            env.ENV_FILE = ".env.test"
            env.REMOTE_SERVER = "***********"
            env.SSH_CREDENTIALS = "dev15_jerry"

            env.STATIC_DEPLOY_REMOTE = "http://static.manyun-local.com:9100/oss/graphql"

            env.NODE_ENV = "production"
            env.BUILD_ENV = "production"
          } else if (env.target == "TESTING_YANG_GAO") {
             if (params.PORT == "7080") {
              env.PORT = "7010";
            }
            env.SUBNET = "192.168.69.0/24"

            env.TAG = "test-yg"
            env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"
            env.ENV_FILE = ".env.test-yg"
            env.REMOTE_SERVER = "***********"
            env.SSH_CREDENTIALS = "dev15_jerry"

            env.STATIC_DEPLOY_REMOTE = "http://static.manyun-local.com:9100/oss/graphql"
            env.VERSION = env.VERSION + "-yg"

            env.NODE_ENV = "production"
            env.BUILD_ENV = "production"
            env.APP_NAME = "泰山系统"
          } else if (env.target == "STAGING") {
            
            env.REGISTRY = "registry.manyun-goc.com"
            env.TAG = "1.0"
            env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"

            env.NODE_ENV = "production"
            env.BUILD_ENV = "production"
            env.GENERATE_SOURCEMAP = "false"
          } else if (env.target == "STAGING_YANG_GAO") {
            env.ZIP_NAME = "YG-" + env.ZIP_NAME
            env.ZIP_NAME_REDASH = 'YG-' + env.ZIP_NAME_REDASH
            env.REGISTRY = "harbor.dcbase.cn:8443"
            env.SERVICE_NAME = "frontend/dc-brain-web"

            env.TAG = "1.0"
            env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"
            env.VERSION = env.VERSION + "-yg"
            
            env.NODE_ENV = "production"
            env.BUILD_ENV = "production"
            env.GENERATE_SOURCEMAP = "false"
            env.APP_NAME = "泰山系统"
          }

          if (env.PORT != "80" && env.target != "STAGING" && env.target != "STAGING_YANG_GAO") {
            env.TAG = "${TAG}-${PORT}"
            env.IMAGE_NAME = "${REGISTRY}/${SERVICE_NAME}:${TAG}"
          }

          if (env.target == "DEV" || env.target == "TEST") {
            switch(env.PORT) {
              case "7001":
                env.SUBNET = "************/24"
              break
              case "7002":
                env.SUBNET = "************/24"
              break
              case "7003":
                env.SUBNET = "192.168.53.0/24"
              break
              case "7004":
                env.SUBNET = "192.168.54.0/24"
              break
              case "7005":
                env.SUBNET = "192.168.55.0/24"
              break
              case "7006":
                env.SUBNET = "192.168.56.0/24"
              break
              case "7007":
                env.SUBNET = "192.168.57.0/24"
              break
              case "7008":
                env.SUBNET = "19**********/24"
              break
              case "7009":
                env.SUBNET = "************/24"
              break
            }
          }

          env.PUBLIC_URL = "/oss/dc-brain-legacy/" + env.VERSION + "/"
          env.APP_REDASH_MF_PUBLIC_PATH = "/oss/redash/" + env.VERSION + "/"
        }

        sh '''
          echo "REGISTRY = ${REGISTRY}"
          echo "REMOTE_SERVER = ${REMOTE_SERVER}"
          echo "TAG = ${TAG}"
          echo "ENV_FILE = ${ENV_FILE}"
          echo "ARG_SKIP_BUILD = ${ARG_SKIP_BUILD}"
          echo "ARG_DEPLOY_MF_DC_BRAIN_LEGACY = ${ARG_DEPLOY_MF_DC_BRAIN_LEGACY-}"
          echo "ARG_DEPLOY_DC_BRAIN_SERVER = ${ARG_DEPLOY_DC_BRAIN_SERVER}"
          echo "ARG_PREPARE_STEPS = ${ARG_PREPARE_STEPS}"
          echo "ARG_CLEAN_INSTALL = ${ARG_CLEAN_INSTALL}"
          echo "ARG_BUILD_MF_REDASH = ${ARG_BUILD_MF_REDASH}"
          echo "ARG_DEPLOY_MF_REDASH = ${ARG_DEPLOY_MF_REDASH}"
          echo
          echo "NODE_ENV = ${NODE_ENV}"
          echo "BUILD_ENV = ${BUILD_ENV}"
        '''

        script {
          if (env.target.contains("YANG_GAO") && !env.CONFIGS_PACKAGE_NAME.contains('sxdtyg')) {
            throw new IllegalArgumentException("Incorrect tenant configs package detected, select package '@manyun/dc-brain.config.sxdtyg' please.")
          }
        }
      }
    }

    stage('Git Checkout') {
      steps {
        checkout poll: false, scm: [$class: 'GitSCM', branches: [[name: "${params.BRANCH}"]], extensions: [], userRemoteConfigs: [[url: 'ssh://******************:8022/frontend/dc-brain.git']]]
      }
    }

    stage('Build legacy app') {
      agent {
        docker {
          image 'custom-bitsrc/stable:1.8.68'
          args '-v /home/<USER>/Library/Caches/Bit/config:/home/<USER>/Library/Caches/Bit/config -v /home/<USER>/Library/Caches/Jerry/app_dc-brain-legacy/Bit/capsules:/home/<USER>/Library/Caches/Bit/capsules -v /home/<USER>/.npmrc:/home/<USER>/.npmrc -v /home/<USER>/.local/share/pnpm/store:/home/<USER>/.cache/pnpm/store'
          reuseNode true
        }
      }
      steps {
        sh '''
          copyArtifacts() {
            if [ -d scopes/legacy/build ]; then
              echo "Copying artifacts..."

              rm -rf scopes/legacy/docker/apps/legacy/**
              cp -r scopes/legacy/build/* scopes/legacy/docker/apps/legacy/

              if [[ ${target} == "STAGING" || ${target} == "STAGING_YANG_GAO" ]]; then
                cd scopes/legacy/docker/apps/legacy/static/js
                ls | grep -E "*\\.map" | xargs rm -f
              fi
            else
              echo "Legacy app artifacts not exists!"
              exit 1
            fi
          }
          if [[ "${ARG_SKIP_BUILD}" == true ]]; then
            echo "Skipping step: build..."
            copyArtifacts
            pnpm exec nap sanitize scopes/legacy/docker/apps/legacy
          else
            chmod +x scripts/build.sh && ./scripts/build.sh
            copyArtifacts
            pnpm exec nap sanitize scopes/legacy/docker/apps/legacy
          fi
        '''
      }
    }

    stage('Build redash app') {
      agent {
        docker {
          image 'custom-bitsrc/stable:1.8.68'
          args '-v /home/<USER>/Library/Caches/Bit/config:/home/<USER>/Library/Caches/Bit/config -v /home/<USER>/Library/Caches/Jerry/app_dc-brain-legacy/Bit/capsules:/home/<USER>/Library/Caches/Bit/capsules -v /home/<USER>/.npmrc:/home/<USER>/.npmrc -v /home/<USER>/.local/share/pnpm/store:/home/<USER>/.cache/pnpm/store'
          reuseNode true
        }
      }
      steps {
        sh '''
          cd scopes/redash
          build() {
            bit install
            bit compile app/redash
            bit build --tasks "CoreExporter,TypescriptCompile,build_application" app/redash
          }

          copyArtifacts() {
            local capsule_dir=`bit capsule list --json | grep -v 'Warning' | jq -r '.workspaceCapsulesRootDir'`
            local app_redash_capsule_dir=$capsule_dir/manyun.redash_app_redash

            if [ -d $app_redash_capsule_dir/artifacts/app-bundle/public ]; then
              echo "Copying artifacts..."

              rm -rf ../legacy/docker/apps/redash/**
              cp -r $app_redash_capsule_dir/artifacts/app-bundle/public/* ../legacy/docker/apps/redash/
            else
              echo "Redash MF app artifacts not exists!"
              exit 1
            fi
          }

          if [[ "${ARG_BUILD_MF_REDASH}" == true ]]; then
            build
            copyArtifacts
            cd ../.. && pnpm exec nap sanitize scopes/legacy/docker/apps/redash/
          else
            echo "Skipping step: build..."
            copyArtifacts
            cd ../.. && pnpm exec nap sanitize scopes/legacy/docker/apps/redash/
          fi
        '''
      }
    }

    stage('Build image') {
      steps {
        sh '''
          cd scopes/legacy/docker
          docker build -t $IMAGE_NAME .
        '''
      }
    }

    stage('Push image') {
      steps {
        sh '''
          docker push $IMAGE_NAME
        '''
      }
    }

    stage('Staging') {
      when {
        anyOf {
            environment name: 'target', value: 'STAGING'
            environment name: 'target', value: 'STAGING_YANG_GAO'
        }
      }

      steps {
        sh '''
          dest=~/.oss/dc-brain-legacy/artifacts/${VERSION}
          rm -rf $dest
          mkdir -p $dest
          cp -r scopes/legacy/build/* $dest/
          cd $dest && cd ..
          find ${VERSION} \\( -name "*.js.map" -o -name "*.css.map" \\) -type f -delete
          zip -r ${ZIP_NAME}.${VERSION}.zip ${VERSION}
        '''
        echo "构建产物已打包至 /home/<USER>/.oss/dc-brain-legacy/artifacts/" + ZIP_NAME + "." + VERSION + ".zip" + "，请联系 @王常俭 进行后续部署操作 🤝🤝🤝 ..."

        script {
          if (env.ARG_BUILD_MF_REDASH == "true" || env.ARG_DEPLOY_MF_REDASH == "true") {
            sh '''
              dest=~/.oss/redash/artifacts/${VERSION}
              rm -rf $dest
              mkdir -p $dest
              cp -r scopes/legacy/docker/apps/redash/* $dest/
              cd $dest && cd ..
              find ${VERSION} \\( -name "*.js.map" -o -name "*.css.map" \\) -type f -delete
              zip -r ${ZIP_NAME_REDASH}.${VERSION}.zip ${VERSION}
            '''
            echo "构建产物已打包至 /home/<USER>/.oss/redash/artifacts/" + ZIP_NAME_REDASH + "." + VERSION + ".zip" + "，请联系 @王常俭 进行后续部署操作 🤝🤝🤝 ..."
          }
        }

        echo "镜像已推送到预发环境，请联系 @王常俭 进行后续部署操作 🤝🤝🤝 ..."
      }
    }
        
    stage('Deploy') {
      when {
        allOf {
          not {
            environment name: 'target', value: 'STAGING'
          }
          not {
            environment name: 'target', value: 'STAGING_YANG_GAO'
          }
        }
      }
      steps {
        sh '''
          if [[ "${ARG_DEPLOY_MF_DC_BRAIN_LEGACY}" == true ]]; then
            nap deploy dc-brain-legacy --dir scopes/legacy/build/ --ver "${VERSION}" --remote "${STATIC_DEPLOY_REMOTE}"
          fi

          if [[ "${ARG_BUILD_MF_REDASH}" == true || "${ARG_DEPLOY_MF_REDASH}" == true ]]; then
            nap deploy redash --dir scopes/legacy/docker/apps/redash/ --ver "${VERSION}" --remote "${STATIC_DEPLOY_REMOTE}"
          fi
        '''

        script {
          if (env.ARG_DEPLOY_DC_BRAIN_SERVER) {
            sshagent(credentials: ["${SSH_CREDENTIALS}"]) {
              timeout(time: 2, unit: 'MINUTES') {
                // https://stackoverflow.com/a/69759828
                sh '''
                    # [ -d ~/.ssh ] || mkdir ~/.ssh && chmod 0700 ~/.ssh
                    # ssh-keyscan -t rsa,dsa ${REMOTE_SERVER} >> ~/.ssh/known_hosts
                    cd scopes/legacy/docker
                    sed "1,3d" ${ENV_FILE} > .env
                    echo >> .env
                    echo "TAG=${TAG}" >> .env
                    echo "PORT=${PORT}" >> .env
                    echo "SUBNET=${SUBNET}" >> .env
                    echo "MF_REMOTES_DCBRAIN_LEGACY=/oss/dc-brain-legacy/${VERSION}/" >> .env
                    find . -name ".env" -o -name "*.yml" -o -name "templates" | tar -zcvf dc-brain-web.tar --files-from -
                    scp dc-brain-web.tar jerry@${REMOTE_SERVER}:/home/<USER>/
                    ssh jerry@${REMOTE_SERVER} << ENDSSH
                      set -ex
                      [[ ! -d dc-brain-web_pipeline/${TAG} ]] && mkdir -p dc-brain-web_pipeline/${TAG}
                      touch dc-brain-web_pipeline/${TAG}/remove.me && rm -rf dc-brain-web_pipeline/${TAG}/**
                      tar -zxvf dc-brain-web.tar -C dc-brain-web_pipeline/${TAG}
                      cd dc-brain-web_pipeline/${TAG}
                      docker compose pull
                      docker compose up -d --wait
                      docker compose ps
ENDSSH
                 '''
              }
            }
          }
        }
      }
    }
  }
}
