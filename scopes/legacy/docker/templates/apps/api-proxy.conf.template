proxy_set_header Host $http_host;
proxy_set_header Cookie $http_cookie;
proxy_set_header X-Real-IP $remote_addr;
proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
proxy_set_header X-Forwarded-Proto $scheme;

location /api {
  proxy_pass ${UPSTREAM_API_PROXY_URL}/api;
}

location /ws {
  proxy_set_header Upgrade $http_upgrade;
  proxy_set_header Connection "upgrade";
  proxy_pass ${UPSTREAM_API_PROXY_URL}/ws;
}

location /logout {
  proxy_pass ${UPSTREAM_API_PROXY_URL}/logout;
}

location /graphql {
  proxy_pass ${UPSTREAM_API_PROXY_URL}/graphql;
}

location /oss {
  proxy_pass ${UPSTREAM_API_PROXY_URL}/oss;
}
