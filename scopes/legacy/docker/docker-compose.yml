networks:
  web:
    ipam:
      config:
        - subnet: '${SUBNET}'

services:
  web:
    image: 'registry.manyun-inc.com/dc-brain-web:${TAG}'
    volumes:
      - ./templates:/etc/nginx/templates
      - ../3D:/usr/share/nginx/html/3D
    ports:
      - '${PORT}:8787'
    healthcheck:
      test: ['CMD-SHELL', 'curl -f http://127.0.0.1:8787/healthcheck || exit 1']
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 30s
    networks:
      - web
    env_file:
      - .env
