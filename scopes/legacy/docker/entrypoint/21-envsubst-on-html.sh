#!/bin/sh

set -e

auto_envsubst() {
  cp /usr/share/nginx/html/index.html /usr/share/nginx/html/index.html.template
  envsubst '${RSA_ENCRYPTION_PUBLIC_KEY} ${MF_REMOTES_DCBRAIN_LEGACY} ${MF_REMOTES_REDASH} ${APP_SALES} ${APP_MONITORING} ${APP_FINANCES} ${MF_REMOTES_RESOURCES} ${MF_REMOTES_DCBRAIN} ${MF_REMOTES_MONITORING} ${MF_REMOTES_REPORT} ${MF_REMOTES_DASHBOARD}' < /usr/share/nginx/html/index.html.template > /usr/share/nginx/html/index.html

}

auto_envsubst

exit 0
