importScripts('/<EMAIL>');

class Request {
  // constructor() {}

  _abortController = new AbortController();

  abort() {
    this._abortController.abort();

    // using a new controller for the next request
    this._abortController = new AbortController();
  }

  async json(url, { method, headers, body } = {}) {
    const signal = this._abortController.signal;

    return await fetch(url, { signal, method, headers, body })
      .then(response => {
        if (response.ok) {
          return response;
        }
        throw new Error(response.status);
      })
      .then(blob => blob.json())
      .catch(console.error);
  }
}

Comlink.expose(Request);
