<!DOCTYPE html>
<html lang="zh">

<head>
  <meta charset="utf-8" />
  <link rel="icon" href="%STATIC_URL%/icons/dc-base/favicon.ico" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <meta name="description" content="%APP_NAME%" />
  <link rel="apple-touch-icon" href="%PUBLIC_URL%/logo192.png" />
  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link rel="manifest" href="%PUBLIC_URL%/manifest.json" />
  <title>%APP_NAME%</title>
  <script type="text/javascript">
    window.envs = {
      RSA_ENCRYPTION_PUBLIC_KEY: `%RSA_ENCRYPTION_PUBLIC_KEY%`,
    };
    window.apps = {
      sales: `%APP_SALES%`,
      monitoring: `%APP_MONITORING%`,
      finances: `%APP_FINANCES%`,
    };
    window.mfDCBrainLegacy = `%MF_REMOTES_DCBRAIN_LEGACY%`;
    window.mfRedashUrl = `%MF_REMOTES_REDASH%`;
    window.mfResourcesUrl = `%MF_REMOTES_RESOURCES%`;
    window.mfDCBrainUrl = `%MF_REMOTES_DCBRAIN%`;
    window.mfMonitoringUrl = `%MF_REMOTES_MONITORING%`;
    window.mfReportUrl = `%MF_REMOTES_REPORT%`;
    window.mfDashboardUrl = `%MF_REMOTES_DASHBOARD%`;
  </script>
  <script defer src="%MF_REMOTES_DCBRAIN_LEGACY%main.js"></script>
</head>

<body data-theme="light">
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
</body>

</html>