/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "hook/use-customers": {
        "name": "hook/use-customers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "hook/use-customers",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "model/customer": {
        "name": "model/customer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "model/customer",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "page/bill-detail": {
        "name": "page/bill-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "page/bill-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/bills": {
        "name": "page/bills",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "page/bills",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/customers": {
        "name": "page/customers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "page/customers",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/customers-service-entries": {
        "name": "page/customers-service-entries",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "page/customers-service-entries",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/supplier-service-entries": {
        "name": "page/supplier-service-entries",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "page/supplier-service-entries",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/crm-routes": {
        "name": "route/crm-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "route/crm-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/create-models": {
        "name": "service/create-models",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "service/create-models",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-customer": {
        "name": "service/delete-customer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-customer",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-customer-tins": {
        "name": "service/fetch-customer-tins",
        "scope": "manyun.crm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-customer-tins",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-customers": {
        "name": "service/fetch-customers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-customers",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-models": {
        "name": "service/fetch-models",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-models",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-vendors": {
        "name": "service/fetch-paged-vendors",
        "scope": "manyun.crm",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-vendors",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-vendor-model-tree": {
        "name": "service/fetch-vendor-model-tree",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-vendor-model-tree",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-customer": {
        "name": "service/mutate-customer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-customer",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/customers": {
        "name": "state/customers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "state/customers",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "ui/customer-deleter": {
        "name": "ui/customer-deleter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/customer-deleter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customer-mutator": {
        "name": "ui/customer-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/customer-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customers-data-table": {
        "name": "ui/customers-data-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/customers-data-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customers-filters": {
        "name": "ui/customers-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/customers-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customers-level": {
        "name": "ui/customers-level",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/customers-level",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customers-select": {
        "name": "ui/customers-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/customers-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/industry-taxonomy": {
        "name": "ui/industry-taxonomy",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/industry-taxonomy",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/model-select": {
        "name": "ui/model-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/model-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/service-entries": {
        "name": "ui/service-entries",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/service-entries",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/tin-select": {
        "name": "ui/tin-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/tin-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/vendor-model-cascader": {
        "name": "ui/vendor-model-cascader",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/vendor-model-cascader",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/vendor-model-select": {
        "name": "ui/vendor-model-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/vendor-model-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/vendor-select": {
        "name": "ui/vendor-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/vendor-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/vendor-text": {
        "name": "ui/vendor-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.crm",
        "mainFile": "index.ts",
        "rootDir": "ui/vendor-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "$schema-version": "17.0.0"
}