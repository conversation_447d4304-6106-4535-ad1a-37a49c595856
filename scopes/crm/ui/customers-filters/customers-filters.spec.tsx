import React from 'react';

import { render, screen } from '@testing-library/react';

import { useRemoteMock } from '@manyun/service.request';

import { BasicCustomersFilters } from './customers-filters.composition';

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

it('customers filters ', () => {
  render(<BasicCustomersFilters />);
  const name = screen.queryByText('客户名称');
  const code = screen.queryByText('客户简称');
  const level = screen.queryByText('客户等级');
  const industry = screen.queryByText('行业分类');
  const owner = screen.queryByText('归属人');

  expect(name).toBeInTheDocument();
  expect(code).toBeInTheDocument();
  expect(level).toBeInTheDocument();
  expect(industry).toBeInTheDocument();
  expect(owner).toBeInTheDocument();

  const nameFiled = screen.getByLabelText('input: name');
  const codeFiled = screen.getByLabelText('input: code');

  expect(nameFiled).toBeInTheDocument();
  expect(codeFiled).toBeInTheDocument();
});
