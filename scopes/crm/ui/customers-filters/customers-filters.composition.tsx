import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { CustomersFilters } from './customers-filters';

export const BasicCustomersFilters = () => {
  const [form] = Form.useForm();
  return (
    <ConfigProvider>
      <FakeStore>
        <CustomersFilters
          form={form}
          loading={false}
          onSearch={() => new Promise(() => console)}
          onReset={console.log}
        />
      </FakeStore>
    </ConfigProvider>
  );
};
