import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { CustomersLevelSelect } from '@manyun/crm.ui.customers-level';
import { IndustryTaxonomySelect } from '@manyun/crm.ui.industry-taxonomy';

export type CustomersFiltersFormValues = {
  /** 客户名称 */
  name?: string;
  /** 客户简称 */
  code?: string;
  /** 客户等级 CODE */
  level?: string;
  /** 行业 CODE */
  industry?: string;
  /** 归属人 `<UserSelect labelInValue />` */
  owner?: { key: number; value: number; label: string };
};

export type CustomersFiltersProps = {
  form: FormInstance<CustomersFiltersFormValues>;
  initialValues?: CustomersFiltersFormValues;
  onSearch: (values: CustomersFiltersFormValues) => Promise<void>;
  onReset: () => void;
};

export function CustomersFilters({
  form,
  initialValues,
  onSearch,
  onReset,
}: CustomersFiltersProps) {
  return (
    <Card bordered={false}>
      <QueryFilter
        form={form}
        items={[
          {
            label: '客户名称',
            name: 'name',
            control: <Input allowClear />,
          },
          {
            label: '客户简称',
            name: 'code',
            control: <Input allowClear />,
          },
          {
            label: '客户等级',
            name: 'level',
            control: <CustomersLevelSelect showSearch allowClear />,
          },
          {
            label: '行业分类',
            name: 'industry',
            control: <IndustryTaxonomySelect showSearch allowClear />,
          },
          {
            label: '归属人',
            name: 'owner',
            control: <UserSelect allowClear placeholder="请根据名称或邮箱搜索" />,
          },
        ]}
        initialValues={initialValues}
        onSearch={onSearch}
        onReset={onReset}
      />
    </Card>
  );
}
