import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Spin } from '@manyun/base-ui.ui.spin';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type IndustryTaxonomyTextProps = {
  code: string;
  style?: React.CSSProperties;
};

export function IndustryTaxonomyText({ code, style }: IndustryTaxonomyTextProps) {
  const [
    {
      data: { entities, loading },
    },
    { readMetaData },
  ] = useMetaData(MetaType.INDUSTRY);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getTxt = () => {
    if (entities[code]) {
      return entities[code].name;
    }
    if (code) {
      return '未知';
    }
    return null;
  };

  return (
    <Spin style={style} spinning={loading}>
      {getTxt()}
    </Spin>
  );
}

export type IndustryTaxonomySelectProps = {
  trigger?: 'onDidMount' | 'onFocus';
} & SelectProps<RefSelectProps>;

export const IndustryTaxonomySelect = React.forwardRef(
  (
    { trigger = 'onFocus', onFocus, ...props }: IndustryTaxonomySelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ data }, { readMetaData }] = useMetaData(MetaType.INDUSTRY);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        readMetaData();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [trigger]);

    return (
      <Select
        ref={ref}
        {...props}
        options={data.data}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readMetaData();
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);
