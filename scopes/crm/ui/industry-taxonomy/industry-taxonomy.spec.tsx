import React from 'react';

import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useRemoteMock } from '@manyun/service.request';

import { BasicIndustryTaxonomy } from './industry-taxonomy.composition';

// let mockOff: ReturnType<typeof useRemoteMock>;
// beforeAll(() => {
//   mockOff = useRemoteMock('web');
// });
// afterAll(() => {
//   mockOff();
// });

// it('customer industry', async () => {
//   const { container } = render(<BasicIndustryTaxonomy />);
//   const rendered = fireEvent.focus(container);
//   expect(rendered).toBeTruthy();
// });
