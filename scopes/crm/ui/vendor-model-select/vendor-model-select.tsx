import React from 'react';

import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import type { BackendModel } from '@manyun/crm.service.fetch-models';
import type { VendorModel } from '@manyun/crm.service.fetch-paged-vendors';
import { ModelSelect } from '@manyun/crm.ui.model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';

export type Infos =
  | {
      type: 'vendor';
      option: VendorModel | VendorModel[];
    }
  | {
      type: 'model';
      option: BackendModel | BackendModel[];
    };

export type VendorModelSelectProps = {
  value?: [string | undefined, string | undefined];
  numbered?: boolean | null;
  deviceType?: string;
  vendorPlaceholder?: string;
  modelPlaceholder?: string;
  onChange?: (value: (string | undefined)[] | undefined, infos: Infos) => void;
} & Pick<SelectProps, 'allowClear' | 'disabled' | 'style'>;

export const VendorModelSelect = React.forwardRef(
  ({
    value = [undefined, undefined],
    numbered,
    deviceType,
    onChange,
    disabled,
    vendorPlaceholder,
    modelPlaceholder,
    ...rest
  }: VendorModelSelectProps) => {
    const [vendorCode, modelCode] = value;

    return (
      <Space.Compact style={{ width: '100%' }}>
        <VendorSelect
          deviceType={deviceType}
          value={vendorCode}
          disabled={disabled}
          placeholder={vendorPlaceholder}
          {...rest}
          onChange={(vendor, option) => {
            onChange && onChange(vendor ? [vendor] : undefined, { type: 'vendor', option });
          }}
        />
        <ModelSelect
          deviceTypes={deviceType ? [deviceType] : undefined}
          vendorCode={vendorCode}
          value={modelCode}
          numbered={numbered}
          disabled={!vendorCode || disabled}
          placeholder={modelPlaceholder}
          {...rest}
          onChange={(model, option) => {
            onChange &&
              onChange(model ? [vendorCode, model] : [vendorCode], { type: 'model', option });
          }}
        />
      </Space.Compact>
    );
  }
);

VendorModelSelect.displayName = 'VendorModelSelect';
