import React, { useEffect, useState } from 'react';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps } from '@manyun/base-ui.ui.cascader';

import { Model, Vendor, fetchVendorModelTree } from '@manyun/crm.service.fetch-vendor-model-tree';

export type VendorModelCascaderProps<VT = any> = {
  // 设备类型
  deviceType?: string;
  // 厂商简称
  vendorCode?: string;
  // 不传，全部，true：查询有资产，false：查询无资产
  numbered?: boolean;
  // 筛掉没有型号的厂商
  filterVendor?: boolean;
} & CascaderProps<VT>;

export function VendorModelCascader({
  deviceType,
  numbered,
  vendorCode,
  filterVendor = false,
  ...rest
}: VendorModelCascaderProps) {
  const [treeList, setTreeList] = useState<any>([]);
  useEffect(() => {
    const fetchTreeList = async () => {
      const { data } = await fetchVendorModelTree({ deviceType, numbered, vendorCode });
      if (data.data.length) {
        if (filterVendor) {
          setTreeList(getOptionNode(filterTreeList(data.data)));
        } else {
          setTreeList(getOptionNode(data.data));
        }
      } else {
        setTreeList([]);
      }
    };
    fetchTreeList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [deviceType]);
  // 改变option格式
  const getOptionNode = (treeList: any[]) => {
    return treeList.map((vendor: any) => {
      if (!vendor.children.length) {
        return {
          label: vendor.metaCode,
          value: vendor.metaCode,
          children: undefined,
        };
      } else {
        const model = vendor.children.map((model: any) => {
          if (!model.children.length) {
            return {
              label: model.metaCode,
              value: model.metaCode,
              children: undefined,
            };
          }
          return model;
        });
        return {
          label: vendor.metaCode,
          value: vendor.metaCode,
          children: model,
        };
      }
    });
  };

  // 筛选掉型号为空的厂商
  const filterTreeList = (treeList: Vendor[]) => {
    return treeList.map((vendor: Vendor) => {
      if (!vendor.children.length) {
        return {
          ...vendor,
          children: null,
        };
      }
      const model = vendor.children.map((model: Model) => {
        if (!model.children.length) {
          return {
            ...model,
            children: null,
          };
        }
        return model;
      });
      return {
        ...vendor,
        children: model,
      };
    });
  };
  return <Cascader placeholder="" options={treeList} changeOnSelect {...rest} />;
}
