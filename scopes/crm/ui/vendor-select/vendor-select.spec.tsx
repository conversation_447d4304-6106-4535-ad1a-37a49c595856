import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useRemoteMock } from '@manyun/service.request';

import { BasicVendorSelect } from './vendor-select.composition';

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

it('vendor select', async () => {
  const { getByRole } = render(<BasicVendorSelect />);
  userEvent.click(getByRole('combobox'));
  await waitFor(() => {
    expect(screen.getByText('暂无数据')).toBeInTheDocument();
  });
});
