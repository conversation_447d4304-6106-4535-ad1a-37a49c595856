import React from 'react';

import debounce from 'lodash.debounce';
import merge from 'lodash.merge';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchPagedVendors } from '@manyun/crm.service.fetch-paged-vendors';
import type { VendorModel } from '@manyun/crm.service.fetch-paged-vendors';

export type Trigger = 'onSearch' | 'onDidMount';
export type KeywordType = 'label' | 'value';

export type VendorSelectProps = {
  trigger?: Trigger;
  fieldNames?: { label?: string; value?: string };
  keywordProperty?: {
    onSearch?: KeywordType;
    onDidMount?: KeywordType;
  };
  deviceType?: string;
} & Omit<
  SelectProps<string, VendorModel>,
  | 'fieldNames'
  | 'showSearch'
  | 'filterOption'
  | 'optionLabelProp'
  | 'loading'
  | 'onSearch'
  | 'options'
>;

export const VendorSelect = React.forwardRef(
  (
    {
      value,
      onFocus,
      trigger = 'onSearch',
      fieldNames,
      keywordProperty,
      deviceType,
      placeholder,
      ...selectProps
    }: VendorSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [loading, setLoading] = React.useState(false);
    const [options, setOptions] = React.useState<VendorModel[]>([]);
    const mergedFieldNames = useDeepCompareMemo(
      () =>
        merge(
          {
            label: 'vendorCode',
            value: 'vendorCode',
          },
          fieldNames
        ),
      [fieldNames]
    );
    const mergedKeywordProperty = useDeepCompareMemo(
      () =>
        merge(
          {
            onSearch: 'value',
            onDidMount: 'value',
          },
          keywordProperty
        ),
      [keywordProperty]
    );

    const searchHandler = React.useCallback(
      async (keywordType: KeywordType, keyword?: string) => {
        const params = {
          pageNum: 1,
          pageSize: 30,
          [mergedFieldNames[keywordType]]: keyword,
          deviceType,
        };
        setLoading(true);
        const { data, error } = await fetchPagedVendors(params);
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setOptions(data.data ?? []);
      },
      [mergedFieldNames, deviceType]
    );

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        searchHandler(mergedKeywordProperty['onDidMount'], value ?? undefined);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const onSearch = React.useMemo(
      () => debounce(val => searchHandler(mergedKeywordProperty['onSearch'], val), 200),
      [searchHandler, mergedKeywordProperty]
    );

    return (
      <Select<string, VendorModel>
        ref={ref}
        fieldNames={mergedFieldNames}
        placeholder={placeholder ?? '请输入关键字搜索'}
        getPopupContainer={trigger => trigger.parentNode.parentNode}
        {...selectProps}
        showSearch
        filterOption={false}
        optionLabelProp={mergedFieldNames.label}
        loading={loading}
        value={value}
        options={options}
        onSearch={onSearch}
        onFocus={evt => {
          searchHandler(mergedKeywordProperty['onSearch']);
          onFocus?.(evt);
        }}
      />
    );
  }
);

VendorSelect.displayName = 'VendorSelect';
