import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';

export type CustomerDeleterProps = {
  /** 客户 ID */
  id: number;
  onDelete?: (data: { id: number }) => void;
};

export function CustomerDeleter({ id, onDelete }: CustomerDeleterProps) {
  return (
    <DeleteConfirm
      variant="popconfirm"
      targetName="该客户"
      title="确认删除该客户吗"
      onOk={() => {
        if (onDelete && typeof onDelete === 'function') {
          onDelete({ id });
        }
        return Promise.resolve(true);
      }}
    >
      <Button aria-label="button: delete" type="link" style={{ height: 'auto', padding: 0 }}>
        删除
      </Button>
    </DeleteConfirm>
  );
}
