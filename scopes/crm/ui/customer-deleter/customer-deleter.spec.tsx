import React from 'react';

import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicCustomerDeleter } from './customer-deleter.composition';

const DELEE_TIIP = '你确定要删除 该客户 吗？';

it('delete customer', async () => {
  render(<BasicCustomerDeleter />);
  const deleteBtn = screen.getByLabelText('button: delete');
  // await userEvent.click(deleteBtn);
  // await waitFor(async () => {
  //   const deleteTip = await screen.findByText(DELEE_TIIP);
  //   expect(deleteTip).toBeInTheDocument();
  // });
});
