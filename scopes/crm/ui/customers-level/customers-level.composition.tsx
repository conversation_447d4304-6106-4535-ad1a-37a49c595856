import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock, webRequest } from '@manyun/service.request';

import { CustomersLevelSelect } from './customers-level';

export const BasicCustomersLevel = () => {
  const [ready, setReady] = React.useState(false);
  const [form] = Form.useForm();

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  // React.useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   setReady(true);
  // }, []);

  if (!ready) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Form form={form} onValuesChange={console.log}>
          <Form.Item label="客户等级" name="ids">
            <CustomersLevelSelect style={{ width: 200 }} />
          </Form.Item>
        </Form>
      </FakeStore>
    </ConfigProvider>
  );
};
