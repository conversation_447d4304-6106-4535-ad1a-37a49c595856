import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Spin } from '@manyun/base-ui.ui.spin';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type CustomersLevelTextProps = {
  code: string;
  style?: React.CSSProperties;
};

export function CustomersLevelText({ code, style }: CustomersLevelTextProps) {
  const [
    {
      data: { entities, loading },
    },
    { readMetaData },
  ] = useMetaData(MetaType.CUSTOMER_LEVEL);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getTxt = () => {
    if (entities[code]) {
      return entities[code].name;
    }
    if (code) {
      return '未知';
    }
    return null;
  };

  return (
    <Spin style={style} spinning={loading}>
      {getTxt()}
    </Spin>
  );
}

export type CustomersLevelSelectProps = {
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps<RefSelectProps>;

export const CustomersLevelSelect = React.forwardRef(
  (
    { trigger = 'onFocus', onFocus, ...props }: CustomersLevelSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ data }, { readMetaData }] = useMetaData(MetaType.CUSTOMER_LEVEL);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        readMetaData();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [trigger]);

    return (
      <Select
        ref={ref}
        {...props}
        options={data.data}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readMetaData();
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);
