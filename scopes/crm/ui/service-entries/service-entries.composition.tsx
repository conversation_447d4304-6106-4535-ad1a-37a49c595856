import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { destroyMock, useRemoteMock, webRequest } from '@manyun/service.request';

import { ServiceEntries } from './service-entries';

export const BasicSupplierServiceEntries = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    // webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    const mockOff = useRemoteMock('web');
    update(true);
    return () => {
      mockOff();
    };
  }, []);
  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <ServiceEntries />
    </ConfigProvider>
  );
};
