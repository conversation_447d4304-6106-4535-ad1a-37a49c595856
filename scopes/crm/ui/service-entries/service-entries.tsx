import React from 'react';

import { GlobalOutlined, SafetyCertificateOutlined } from '@ant-design/icons';

import { FileEditOutlined } from '@manyun/base-ui-web.icon.outlined.file-edit';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { message } from '@manyun/base-ui.ui.message';

import { CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { RequestEntryCard } from '@manyun/bpm.ui.request-entry-card';
import { fetchTreeModeMetaData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import type { BackendData } from '@manyun/resource-hub.service.fetch-tree-mode-meta-data';
import { generateTicketCreateLocation } from '@manyun/ticket.route.ticket-routes';

import styles from './service-entries.module.less';

export type ServiceEntriesProps = {
  filter?: (x: BackendData) => boolean;
};

export type Data = Omit<BackendData, 'children'> & { show: boolean; children: Data[] };

export function ServiceEntries({ filter }: ServiceEntriesProps) {
  const [serviceData, setServiceData] = React.useState<Data[]>([]);
  const [activeKey, setActiveKey] = React.useState<string[]>([]);

  const judgeIsShow = (data: BackendData[]): Data[] => {
    return data.map(item => {
      if (item.children && item.children.length) {
        return {
          ...item,
          children: judgeIsShow(item.children as BackendData[]),
          show: true,
        };
      }
      return {
        ...item,
        show: typeof filter === 'function' ? filter(item) : true,
      };
    }) as Data[];
  };

  React.useEffect(() => {
    (async () => {
      const { error, data } = await fetchTreeModeMetaData({
        topCategory: 'OPERATION_SERVICE',
        secondCategory: 'SERVICE_SUB_TYPE',
      });
      if (error) {
        message.error(error.message);
      }
      const newData: Data[] = judgeIsShow(data.data);
      setServiceData(newData);
      setActiveKey(newData.map(({ metaCode }) => metaCode));
    })();
    // eslint-disable-next-line
  }, []);

  return (
    <div>
      <Collapse
        bordered={false}
        activeKey={activeKey}
        onChange={key => setActiveKey(key as string[])}
      >
        {serviceData.map(item => {
          const ticket = item.children;
          const count = ticket.filter(({ show }) => show).length;
          if (!count) {
            return null;
          }
          return (
            <Collapse.Panel
              header={<div>{`${item.metaName}（${count}）`}</div>}
              key={item.metaCode}
            >
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill,minmax(185px,1fr))',
                  gridRowGap: '8px',
                  gridColumnGap: '8px',
                }}
              >
                {item.children.map(item => {
                  if (!(item as Data).show) {
                    return;
                  }
                  let path = '';
                  let CardIcon = FileEditOutlined; // 图标显示按照一级菜单
                  if (item.metaCode !== 'CONSTRUCTION_APPLY') {
                    path = generateTicketCreateLocation({
                      ticketType: item.metaCode.toLowerCase(),
                    });
                  }
                  if (item.metaCode === 'CONSTRUCTION_APPLY') {
                    path = CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH;
                    CardIcon = GlobalOutlined as any;
                  }

                  if (item.metaCode === 'VISITOR' || item.metaCode === 'ACCESS_CARD_AUTH') {
                    CardIcon = SafetyCertificateOutlined as any;
                  }

                  return (
                    <RequestEntryCard
                      key={item.id}
                      title={item.metaName}
                      linkTo={path}
                      icon={<IconContainer children={<CardIcon />} />}
                    />
                  );
                })}
              </div>
            </Collapse.Panel>
          );
        })}
      </Collapse>
    </div>
  );
}

export function IconContainer({ children }: { children: React.ReactNode }) {
  return (
    <div className={styles.iconContainer} style={{ fontSize: '16px' }}>
      {children}
    </div>
  );
}
