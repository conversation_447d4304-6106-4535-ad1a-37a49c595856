import React from 'react';

import get from 'lodash.get';

import { message } from '@manyun/base-ui.ui.message';

import { fetchPagedVendors } from '@manyun/crm.service.fetch-paged-vendors';

export type VendorTextProps = {
  code: string;
};

export function VendorText({ code }: VendorTextProps) {
  const [text, setText] = React.useState<string>();

  const fetchVendor = React.useCallback(async () => {
    const { error, data } = await fetchPagedVendors({
      pageNum: 1,
      pageSize: 1,
      vendorCode: code,
    });
    if (error) {
      message.error(error.message);
    }
    setText(get(data, ['data', 0, 'vendorName'], '未知'));
  }, [code]);

  React.useEffect(() => {
    fetchVendor();
  }, [fetchVendor]);

  return <>{text}</>;
}
