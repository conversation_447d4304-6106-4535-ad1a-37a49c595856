import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchCustomerTins } from '@manyun/crm.service.fetch-customer-tins';

type RefSelectProps = {
  focus: () => void;
  blur: () => void;
};

export type TinSelectProps = {
  trigger?: 'onDidMount' | 'onFocus';
  customerNo: string;
  selectDefaultTin?: boolean; // 为true 时，此时不管外部传入的onDidMount的值，内部将默认trigger为onDidMount
} & SelectProps<any>;

export const TinSelect = React.forwardRef(
  (
    {
      customerNo,
      onFocus,
      onChange,
      selectDefaultTin,
      trigger = 'onFocus',
      ...props
    }: TinSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [titles, setTitles] = React.useState<{ label: string; value: string }[]>([]);
    const [loading, setLoading] = React.useState<boolean>(false);
    const _trigger = selectDefaultTin ? 'onDidMount' : trigger;

    const getData = async () => {
      setLoading(true);
      const { data, error } = await fetchCustomerTins(customerNo);
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      const newData = data.data.map(tin => ({
        ...tin,
        value: tin.taxpayerIdentityNo,
        label: tin.invoiceTitle,
      }));
      if (selectDefaultTin) {
        const defauleTitle = newData.find(({ defaultTitle }) => defaultTitle);
        if (defauleTitle && typeof onChange === 'function') {
          onChange(defauleTitle.taxpayerIdentityNo, defauleTitle);
        }
      }
      setTitles(newData);
    };

    React.useEffect(() => {
      if (_trigger === 'onDidMount') {
        getData();
      }
    }, [_trigger]);

    return (
      <Select
        ref={ref}
        {...props}
        options={titles as any[]}
        onFocus={evt => {
          if (_trigger === 'onFocus') {
            getData();
          }
          onFocus?.(evt);
        }}
        showSearch
        loading={loading}
        onChange={onChange}
      />
    );
  }
);
