import React from 'react';

import { Form } from '@manyun/base-ui.ui.form';

import { TinSelect } from './tin-select';

export const BasicTinSelect = () => {
  const [form] = Form.useForm();

  return (
    <Form form={form} onValuesChange={console.log}>
      <Form.Item label="发票抬头" name="ids">
        <TinSelect customerNo="字节跳动" selectDefaultTin={true} style={{ width: 200 }} />
      </Form.Item>
    </Form>
  );
};
