import React from 'react';

import debounce from 'lodash.debounce';
import groupBy from 'lodash.groupby';
import uniqBy from 'lodash.uniqby';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchModels } from '@manyun/crm.service.fetch-models';
import type { BackendModel } from '@manyun/crm.service.fetch-models';

export type ModelSelectProps = {
  vendorCode?: string;
  numbered?: boolean | null;
  deviceTypes?: string[];
  trigger?: 'onSearch' | 'onDidMount';
  onChange?: (
    value: string,
    option: BackendModel | BackendModel[],
    mapper: Record<string, BackendModel[]>
  ) => void;
  disabledOptions?: string[];
} & Omit<
  SelectProps<string, BackendModel>,
  'showSearch' | 'filterOption' | 'loading' | 'onSearch' | 'options' | 'onChange'
>;

export const ModelSelect = React.forwardRef(
  (
    {
      vendorCode,
      numbered,
      deviceTypes,
      value,
      trigger = 'onSearch',
      onFocus,
      onChange,
      labelInValue,
      disabledOptions = [],
      placeholder,
      ...selectProps
    }: ModelSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [loading, setLoading] = React.useState(false);
    const [options, setOptions] = React.useState<BackendModel[]>([]);
    const [originMapper, setOriginMapper] = React.useState<Record<string, BackendModel[]>>({});
    const deviceTypeList = useDeepCompareMemo(() => deviceTypes, [deviceTypes]);
    const disabledOptionList = useDeepCompareMemo(() => disabledOptions, [disabledOptions]);

    const searchHandler = React.useCallback(
      async (keyword?: string) => {
        setLoading(true);
        const { data, error } = await fetchModels({
          modelCode: keyword,
          vendorCode,
          numbered,
          deviceTypeList,
          page: 1,
          pageSize: vendorCode ? 30 : 2000,
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setOriginMapper(groupBy(data.data, 'modelCode'));
        const models = vendorCode ? data.data : uniqBy(data.data, 'modelCode');
        setOptions(
          models.map(model => ({
            ...model,
            disabled: disabledOptionList.includes(model.modelCode),
          }))
        );
      },
      [deviceTypeList, numbered, vendorCode, disabledOptionList]
    );

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        searchHandler(Array.isArray(value) ? undefined : value ?? undefined);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value]);

    const onSearch = React.useMemo(() => debounce(searchHandler, 200), [searchHandler]);

    return (
      <Select
        ref={ref}
        placeholder={placeholder ?? '请输入关键字搜索'}
        fieldNames={{ label: 'modelCode', value: 'modelCode' }}
        {...selectProps}
        showSearch
        filterOption={false}
        loading={loading}
        value={value}
        options={options}
        onSearch={onSearch}
        onFocus={evt => {
          !labelInValue && searchHandler();
          onFocus?.(evt);
        }}
        onChange={(value, option) => onChange?.(value, option, originMapper)}
      />
    );
  }
);

ModelSelect.displayName = 'ModelSelect';
