import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { CustomerMutator } from './customer-mutator';

export const BasicCustomerMutator = () => {
  const [form] = Form.useForm();
  return (
    <ConfigProvider>
      <FakeStore>
        <CustomerMutator form={form} mode="create" onSubmit={console.log} />
      </FakeStore>
    </ConfigProvider>
  );
};
