import React from 'react';

import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicCustomerMutator } from './customer-mutator.composition';

const NAME_TIP = '请输入客户名称';
const CODE_TIP = '请输入客户编码';
const LEVEL_TIP = '请选择客户等级';
const OWER_TIP = '请选择归属人';

it('customer edit,add', async () => {
  await act(async () => {
    await render(<BasicCustomerMutator />);
    const createBtn = screen.getByLabelText('button: create');
    await userEvent.click(createBtn);
    const createDrawer = screen.getByLabelText('drawer: create');
    expect(createDrawer).toBeInTheDocument();
    const submitBtn = screen.getByLabelText('button: submit');
    await userEvent.click(submitBtn);
    const nameTip1 = await screen.findByText(NAME_TIP);
    const codeTip1 = await screen.findByText(CODE_TIP);
    const levelTip1 = await screen.findByText(LEVEL_TIP);
    const ownerTip1 = await screen.findByText(OWER_TIP);

    await waitFor(async () => {
      await expect(nameTip1).toBeInTheDocument();
      await expect(codeTip1).toBeInTheDocument();
      await expect(levelTip1).toBeInTheDocument();
      await expect(ownerTip1).toBeInTheDocument();
    });

    const nameInput = await screen.findByRole('textbox', { name: 'input: name' });
    await userEvent.type(nameInput, '平多多');
    await waitFor(async () => {
      expect(screen.queryByText(NAME_TIP)).toBeNull();
    });
  });
});
