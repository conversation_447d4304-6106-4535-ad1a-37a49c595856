import React, { useState } from 'react';

import Drawer from 'antd/es/drawer';

import { Button } from '@manyun/base-ui.ui.button';
import { ButtonProps } from '@manyun/base-ui.ui.button';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { CustomersLevelSelect } from '@manyun/crm.ui.customers-level';
import { IndustryTaxonomySelect } from '@manyun/crm.ui.industry-taxonomy';

export type CustomerMutatorFormValues = {
  /** 客户 CODE */
  code: string;
  /** 客户名称 */
  name: string;
  /** 客户等级 CODE */
  level: string;
  /** 行业 CODE */
  industry: string | undefined;
  /** 归属人 */
  owner: { key: number; value: number; label: string };
  /** 备注 */
  description: string | undefined;
};

export type CustomerMutatorProps = {
  form: FormInstance<CustomerMutatorFormValues>;
  initialValues?: CustomerMutatorFormValues;
  btnProps?: {
    txt: string;
    [x: string]: any;
  } & ButtonProps;
  mode?: 'create' | 'edit';
  onSubmit: (data: any) => void;
};

export function CustomerMutator({
  form,
  initialValues,
  btnProps = {
    txt: '新建',
    type: 'primary',
  },
  mode = 'create',
  onSubmit,
}: CustomerMutatorProps) {
  const [visible, setVisible] = useState(false);
  const title = mode === 'create' ? '新建客户' : '编辑客户';
  return (
    <>
      <Button
        {...btnProps}
        onClick={() => {
          setVisible(true);
        }}
        aria-label={`button: ${mode}`}
      >
        {btnProps.txt}
      </Button>
      {visible && (
        <Drawer
          aria-label="drawer: create"
          visible={visible}
          onClose={() => setVisible(false)}
          title={title}
          footer={
            <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Space>
                <Button onClick={() => setVisible(false)}>取消</Button>
                <Button
                  type="primary"
                  onClick={() => {
                    form
                      .validateFields()
                      .then(values => {
                        onSubmit({
                          formValues: values,
                          callback: () => {
                            setVisible(false);
                          },
                        });
                      })
                      .catch(err => {});
                  }}
                  aria-label="button: submit"
                >
                  提交
                </Button>
              </Space>
            </div>
          }
        >
          <Form form={form} layout="vertical" colon={true} initialValues={initialValues}>
            <Form.Item
              label="客户名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '请输入客户名称',
                },
                {
                  max: 32,
                  message: '最多输入 32 个字符！',
                },
              ]}
            >
              <Input allowClear style={{ width: 200 }} aria-label="input: name" />
            </Form.Item>
            <Form.Item
              label="客户简称"
              name="code"
              rules={[
                {
                  required: true,
                  message: '请输入客户简称',
                },
                {
                  max: 32,
                  message: '最多输入 32 个字符！',
                },
              ]}
            >
              <Input
                allowClear
                disabled={mode === 'edit'}
                style={{ width: 200 }}
                aria-label="input: code"
              />
            </Form.Item>
            <Form.Item
              label="客户等级"
              name="level"
              rules={[
                {
                  required: true,
                  message: '请选择客户等级',
                },
              ]}
            >
              <CustomersLevelSelect style={{ width: 200 }} showSearch allowClear />
            </Form.Item>
            <Form.Item
              label="归属人"
              name="owner"
              rules={[
                {
                  required: true,
                  message: '请选择归属人，为系统用户',
                },
              ]}
            >
              <UserSelect allowClear style={{ width: 200 }} />
            </Form.Item>
            <Form.Item label="行业分类" name="industry">
              <IndustryTaxonomySelect style={{ width: 200 }} showSearch allowClear />
            </Form.Item>
            <Form.Item
              label="备注"
              name="description"
              rules={[
                {
                  max: 120,
                  message: '最多输入 120 个字符！',
                },
              ]}
            >
              <Input.TextArea style={{ width: 200 }} />
            </Form.Item>
          </Form>
        </Drawer>
      )}
    </>
  );
}
