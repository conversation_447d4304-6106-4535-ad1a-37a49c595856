import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useRemoteMock } from '@manyun/service.request';

import { BasicCustomersSelect } from './customers-select.composition';

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

it('customer select', async () => {
  const { getByRole, getByText } = render(<BasicCustomersSelect />);
  userEvent.click(getByRole('combobox'));
  await waitFor(() => {
    expect(screen.getByText('暂无数据')).toBeInTheDocument();
  });
});
