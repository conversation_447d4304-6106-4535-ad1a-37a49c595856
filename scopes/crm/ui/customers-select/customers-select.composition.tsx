import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock, webRequest } from '@manyun/service.request';

import { CustomersSelect } from './customers-select';

function CustomersSelectWrapper() {
  const [form] = Form.useForm();
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  // React.useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   setReady(true);
  // }, []);

  if (!ready) {
    return null;
  }

  return (
    <Form form={form} onValuesChange={console.log}>
      <Form.Item label="客户" name="ids">
        <CustomersSelect style={{ width: 200 }} />
      </Form.Item>
    </Form>
  );
}

export const BasicCustomersSelect = () => {
  return (
    <ConfigProvider>
      <FakeStore>
        <CustomersSelectWrapper />
      </FakeStore>
    </ConfigProvider>
  );
};
