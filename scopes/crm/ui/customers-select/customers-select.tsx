import React from 'react';

import debounce from 'lodash.debounce';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { useCustomers } from '@manyun/crm.hook.use-customers';
import type { Customer } from '@manyun/crm.model.customer';

export type CustomersSelectProps = Omit<
  SelectProps<string, Customer>,
  'showSearch' | 'filterOption' | 'loading' | 'onSearch' | 'options'
>;

const defaultFieldNames = { label: 'code', value: 'code' };

export const CustomersSelect = React.forwardRef<RefSelectProps, CustomersSelectProps>(
  ({ fieldNames, ...selectProps }, ref) => {
    const [{ loading, entities }, { readCustomers }] = useCustomers();

    const searchHandler = React.useCallback(
      (keyword?: string) => {
        readCustomers({ [fieldNames?.value ?? 'code']: keyword, pageSize: 5 });
      },
      [fieldNames?.value, readCustomers]
    );

    React.useEffect(() => {
      searchHandler();
    }, [searchHandler]);

    const onSearch = React.useMemo(() => debounce(searchHandler, 200), [searchHandler]);

    const options = entities || [];

    return (
      <Select
        ref={ref}
        fieldNames={{ ...defaultFieldNames, ...fieldNames }}
        {...selectProps}
        showSearch
        filterOption={false}
        loading={loading}
        onSearch={onSearch}
        options={options}
      />
    );
  }
);
