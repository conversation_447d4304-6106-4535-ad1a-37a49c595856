import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { CustomersDataTable } from './customers-data-table';

export const BasicCustomersDataTable = () => {
  const [initialized, setInitilized] = React.useState(false);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    setInitilized(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router>
          <CustomersDataTable
            data={[
              {
                id: 0,
                code: '211',
                name: 'name',
                level: 'level',
                gmtCreate: Date.now(),
                gmtModified: Date.now(),
                owner: {
                  id: 1,
                  name: 'admin',
                },
                industry: null,
                description: null,
              },
            ]}
            total={0}
            page={1}
            pageSize={10}
            onChange={({ current, pageSize }) => {}}
            createCustomer={() => {}}
            updateCustomer={() => {}}
            deleteCustomer={() => {}}
            readCustomers={() => {}}
            searchFilters={{}}
          />
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
