import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { TablePaginationConfig } from '@manyun/base-ui.ui.table';

import { User } from '@manyun/auth-hub.ui.user';
import { UserCompanyConnector } from '@manyun/auth-hub.ui.user-company-connector';
import type { Customer } from '@manyun/crm.model.customer';
import { CustomersLevelText } from '@manyun/crm.ui.customers-level';
import { IndustryTaxonomyText } from '@manyun/crm.ui.industry-taxonomy';

export type CustomersDataTableProps = {
  data: Customer[];
  total: number;
  page: number;
  pageSize: number;
  onChange?: (pagination: TablePaginationConfig) => void;
};

export function CustomersDataTable({
  data = [],
  total = 0,
  page = 1,
  pageSize = 10,
  onChange,
}: CustomersDataTableProps) {
  return (
    <Card bordered={false}>
      <Space direction="vertical" size="large" style={{ width: '100%' }}>
        <Table
          rowKey="id"
          dataSource={data}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '客户名称',
              ellipsis: true,
              dataIndex: 'name',
            },
            {
              title: '客户简称',
              ellipsis: true,
              dataIndex: 'code',
            },
            {
              title: '客户等级',
              dataIndex: 'level',
              render: code => <CustomersLevelText code={code} />,
            },
            {
              title: '行业分类',
              dataIndex: 'industry',
              render: code => <IndustryTaxonomyText code={code} />,
            },
            {
              title: '归属人',
              dataIndex: 'owner',
              render: owner => <User.Link id={owner?.id} name={owner?.name} />,
            },
            {
              title: '操作',
              fixed: 'right',
              dataIndex: '_actions',
              render: (_, record) => {
                return <UserCompanyConnector company={record.code} userType="CUSTOMER" />;
              },
            },
          ]}
          pagination={{
            total: total,
            current: page,
            pageSize: pageSize,
          }}
          onChange={pagination => {
            if (onChange && typeof onChange === 'function') {
              onChange(pagination);
            }
          }}
        />
      </Space>
    </Card>
  );
}
