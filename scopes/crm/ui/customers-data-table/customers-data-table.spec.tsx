import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useRemoteMock } from '@manyun/service.request';

import { BasicCustomersDataTable } from './customers-data-table.composition';

// let mockOff: ReturnType<typeof useRemoteMock>;
// beforeAll(() => {
//   mockOff = useRemoteMock('web');
// });
// afterAll(() => {
//   mockOff();
// });

// it('customer table', async () => {
//   render(<BasicCustomersDataTable />);
//   const name1 = screen.queryByText('name');
//   expect(name1).toBeInTheDocument();
//   const createBtn = screen.getByLabelText('button: create');
//   await userEvent.click(createBtn);
//   const createDrawer = screen.getByLabelText('drawer: create');
//   expect(createDrawer).toBeInTheDocument();
// });
