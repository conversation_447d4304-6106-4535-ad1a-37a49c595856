import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { Customer } from '@manyun/crm.model.customer';
import { deleteCustomerWeb } from '@manyun/crm.service.delete-customer';
import { fetchCustomersWeb } from '@manyun/crm.service.fetch-customers';
import { mutateCustomerWeb } from '@manyun/crm.service.mutate-customer';

import {
  customersSliceActions,
  deleteCustomerAction,
  getCustomersAction,
  mutateCustomerAction,
} from './customers.action';

// import { selectCustomersEntities } from './customers.selector';

/** Workers */

export function* getCustomersSaga({
  payload: { params, callback },
}: ReturnType<typeof getCustomersAction>) {
  const { error, data }: SagaReturnType<typeof fetchCustomersWeb> = yield call(
    fetchCustomersWeb,
    params
  );
  if (error) {
    message.error(error.message);
    yield put(customersSliceActions.fetchCustomersError());
    return;
  }
  const ids = data.data.map(({ id }) => id);
  callback(ids, data.total);
  yield put(customersSliceActions.setCustomers({ customers: data.data, total: data.total }));
}

export function* mutateCustomerSaga({
  payload: { params, callback },
}: ReturnType<typeof mutateCustomerAction>) {
  const { error, data }: SagaReturnType<typeof mutateCustomerWeb> = yield call(
    mutateCustomerWeb,
    params
  );
  if (error) {
    message.error(error.message);
    return;
  }
  const { id }: any = params;
  if (id) {
    message.success('更新成功');
  } else {
    message.success('新建成功');
  }
  if (callback && typeof callback === 'function') {
    callback();
  }
  yield put(customersSliceActions.mutateCustomerSuccess({ data: data as Customer }));
}

export function* deleteCustomerSaga({
  payload: { params, callback },
}: ReturnType<typeof deleteCustomerAction>) {
  const { error, data }: SagaReturnType<typeof deleteCustomerWeb> = yield call(
    deleteCustomerWeb,
    params
  );
  if (error) {
    message.error(error.message);
    return;
  }
  message.success('删除成功');
  if (callback && typeof callback === 'function') {
    callback();
  }
  yield put(customersSliceActions.deleteCustomerSuccess({ id: data }));
}

/** Watchers */

function* watchGetCustomers() {
  yield takeLatest(getCustomersAction.type, getCustomersSaga);
}

function* watchMutateCustomer() {
  yield takeLatest(mutateCustomerAction.type, mutateCustomerSaga);
}

function* watchDeleteCustomer() {
  yield takeLatest(deleteCustomerAction.type, deleteCustomerSaga);
}

export default [fork(watchGetCustomers), fork(watchMutateCustomer), fork(watchDeleteCustomer)];
