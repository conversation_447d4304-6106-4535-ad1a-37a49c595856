import { createAction } from '@reduxjs/toolkit';

import type { ServiceD as DeleteCustomerApiQ } from '@manyun/crm.service.delete-customer';
import type { ApiQ as FetchCustomersApiQ } from '@manyun/crm.service.fetch-customers';
import type { CreateSvcQuery, UpdateSvcQuery } from '@manyun/crm.service.mutate-customer';

import { customersSlice } from './customers.slice';

const prefix = customersSlice.name;

export const customersSliceActions = customersSlice.actions;

export const getCustomersAction = createAction<{
  params: FetchCustomersApiQ;
  callback: (ids: number[], total: number) => void;
}>(prefix + '/GET_CUSTOMERS');

export const mutateCustomerAction = createAction<{
  params: CreateSvcQuery | UpdateSvcQuery;
  callback: () => void;
}>(prefix + '/MUTATE_CUSTOMERS');

export const deleteCustomerAction = createAction<{
  params: DeleteCustomerApiQ;
  callback?: () => void;
}>(prefix + '/DELETE_CUSTOMERS');
