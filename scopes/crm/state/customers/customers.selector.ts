import { Customer } from '@manyun/crm.model.customer';

import { customersSlice } from './customers.slice';
import type { CustomersSliceState } from './customers.slice';

type StoreState = {
  [customersSlice.name]: CustomersSliceState;
};

export const selectCustomersEntitiesByIds = (ids?: number[]) => (storeState: StoreState) => {
  const { entities } = storeState[customersSlice.name];
  if (ids === undefined) {
    return Object.keys(entities).map(id => entities[id as any]) as Customer[];
  }
  return ids.map(id => entities[id] || null).filter(Boolean) as Customer[];
};

export const selectCustomersState = () => (storeState: StoreState) => {
  const { entities, ids, loading } = storeState[customersSlice.name];

  return { entities, ids, loading };
};

export const selectCustomersFilters = () => (storeState: StoreState) => {
  const { filters } = storeState[customersSlice.name];

  return filters;
};

export const selectCustomersPagination = () => (storeState: StoreState) => {
  const { pagination } = storeState[customersSlice.name];

  return pagination;
};
