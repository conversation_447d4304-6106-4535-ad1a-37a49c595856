import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import uniq from 'lodash.uniq';

import { Customer } from '@manyun/crm.model.customer';
import { ApiQ as FetchCustomersWebApiQ } from '@manyun/crm.service.fetch-customers';
import type { CustomersFiltersFormValues } from '@manyun/crm.ui.customers-filters';

export type CustomersSliceState = {
  entities: Record<number, Customer | undefined>;
  ids: number[];
  loading: boolean;
  filters: any;
  pagination: {
    pageNum: number;
    pageSize: number;
  };
};

type CustomersSliceCaseReducers = {
  fetchCustomersStart: CaseReducer<CustomersSliceState, PayloadAction<FetchCustomersWebApiQ>>;
  setCustomers: CaseReducer<
    CustomersSliceState,
    PayloadAction<{ customers: Customer[]; total: number }>
  >;
  fetchCustomersError: CaseReducer<CustomersSliceState>;
  mutateCustomerSuccess: CaseReducer<CustomersSliceState, PayloadAction<{ data: Customer }>>;
  deleteCustomerSuccess: CaseReducer<CustomersSliceState, PayloadAction<{ id: number }>>;
  updateFilters: CaseReducer<CustomersSliceState, PayloadAction<CustomersFiltersFormValues>>;
  updatePagination: CaseReducer<
    CustomersSliceState,
    PayloadAction<{ pageNum?: number; pageSize?: number }>
  >;
};

export const customersSlice = createSlice<
  CustomersSliceState,
  CustomersSliceCaseReducers,
  'customers'
>({
  name: 'customers',
  initialState: {
    entities: {},
    ids: [],
    loading: false,
    filters: {},
    pagination: {
      pageNum: 1,
      pageSize: 10,
    },
  },
  reducers: {
    fetchCustomersStart(sliceState) {
      sliceState.loading = true;
    },
    setCustomers(sliceState, { payload: { customers } }) {
      const ids: number[] = [];
      customers.forEach(customer => {
        ids.push(customer.id);

        // cache update performance optimization
        const existingCustomers = sliceState.entities[customer.id];
        if (existingCustomers && existingCustomers.gmtModified === customer.gmtModified) {
          return;
        }
        sliceState.entities[customer.id] = customer;
      });
      sliceState.ids = uniq([...sliceState.ids, ...ids]);
      sliceState.loading = false;
    },
    fetchCustomersError(sliceState) {
      sliceState.loading = false;
    },
    mutateCustomerSuccess(sliceState, { payload: { data } }) {
      const id = data.id;
      if (sliceState.entities[id]) {
        sliceState.entities[id] = data;
        return;
      }
      sliceState.entities = {
        ...sliceState.entities,
        [id]: data,
      };
      sliceState.ids = [...sliceState.ids, id];
    },
    deleteCustomerSuccess(sliceState, { payload: { id } }) {
      delete sliceState.entities[id];
      sliceState.ids = sliceState.ids.filter(item => item !== id);
    },
    updateFilters(sliceState, { payload }) {
      sliceState.filters = { ...sliceState.filters, ...payload };
    },
    updatePagination(sliceState, { payload }) {
      sliceState.pagination = {
        ...sliceState.pagination,
        ...payload,
      };
    },
  },
});
