---
description: 'Customers redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import customersSliceReducer from '@manyun/[scope].state.customers';

const rootReducer = {
  ...customersSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { customersWatchers } from '@manyun/[scope].state.customers';

const function* rootSaga() {
  yield all(
    ...customersWatchers,
    // other sagas...
  );
};
```