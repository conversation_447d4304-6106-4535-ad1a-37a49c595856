import React, { useCallback, useEffect, useState } from 'react';
import { useLocation, useParams } from 'react-router-dom';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useLazyBill, useLazyBillTaxes } from '@manyun/crm.gql.client.bill';
import type { FeeLocation } from '@manyun/crm.model.bill';
import type { BillDetailParams } from '@manyun/crm.route.routes';
import { BillActionButtons } from '@manyun/crm.ui.bill-action-buttons';
import { BillHeader } from '@manyun/crm.ui.bill-header';
import { BillLogTable } from '@manyun/crm.ui.bill-log-table';
import { SecondFeeAdjustments } from '@manyun/crm.ui.second-fee-adjustments';
import { SecondFeeCollapse } from '@manyun/crm.ui.second-fee-collapse';
import { SecondFeeObjections } from '@manyun/crm.ui.second-fee-objections';

export type BillDetailProps = {
  feeLocation?: FeeLocation;
};

export function BillDetail({ feeLocation = 'operation' }: BillDetailProps) {
  const [getBill, { loading, data }] = useLazyBill();
  const { id } = useParams<BillDetailParams>();
  const { search } = useLocation();
  const { feeId } = getLocationSearchMap<{ feeId?: number }>(search);
  const bill = data?.bill.billDetail;
  const [statisticMap, setStatisticMap] = useState<{
    approvalCount: number;
    confirmCount: number;
    exportCount: number;
  }>({
    approvalCount: 0,
    confirmCount: 0,
    exportCount: 0,
  });
  const accountNo = bill?.accountNo;
  const [getBillTaxes, { data: billTaxesResult }] = useLazyBillTaxes();
  const billTaxes = useDeepCompareMemo(
    () => billTaxesResult?.billTaxes?.data ?? [],
    [billTaxesResult]
  );

  const fetchBill = useCallback(
    (transMonth?: string) => {
      if (!id) {
        return;
      }
      getBill({ variables: { billNo: id, feeId: feeId, transMonth } }).then(({ error, data }) => {
        if (error) {
          message.error(error.message);
        }
        const secondFees = data?.bill.secondFees?.data;
        if (secondFees && !transMonth) {
          const approvalCount = secondFees.filter(fee => fee.approvalInstId).length;
          setStatisticMap({
            approvalCount,
            confirmCount: secondFees.filter(
              ({ feeStatus: { code }, approvalType }) =>
                (code === 'TO_BE_CONFIRMED' || code === 'RE_CONFIRM') && !approvalType
            ).length,
            exportCount:
              secondFees.length -
              secondFees.filter(fee => fee.feeStatus.code === 'DISCARD').length -
              approvalCount,
          });
        }
      });
    },
    [id, feeId, getBill]
  );

  useEffect(() => {
    fetchBill();
  }, [fetchBill]);

  useEffect(() => {
    if (!accountNo) {
      return;
    }
    getBillTaxes({ variables: { accountNo } });
  }, [accountNo, getBillTaxes]);

  if (!id || !bill) {
    return <></>;
  }

  return loading ? (
    <Skeleton active />
  ) : (
    <Card bordered={false}>
      <Space style={{ width: '100%', marginBottom: 64 }} direction="vertical">
        <BillHeader bill={bill} feeLocation={feeLocation} />
        <Tabs
          destroyInactiveTabPane
          items={[
            {
              label: '详情信息',
              key: 'fees',
              children: (
                <SecondFeeCollapse
                  billNo={id}
                  feeId={feeId}
                  feeLocation={feeLocation}
                  billTaxes={billTaxes}
                  onSuccess={fetchBill}
                />
              ),
            },
            {
              label: '调整明细',
              key: 'adjustments',
              children: <SecondFeeAdjustments billNo={id} feeLocation={feeLocation} />,
            },
            { label: '异议记录', key: 'objections', children: <SecondFeeObjections billNo={id} /> },
            { label: '操作日志', key: 'logs', children: <BillLogTable billNo={id} /> },
          ]}
        />
        <BillActionButtons
          approvalCount={statisticMap.approvalCount}
          confirmCount={statisticMap.confirmCount}
          exportCount={statisticMap.exportCount}
          bill={bill}
          feeLocation={feeLocation}
          billTaxes={billTaxes}
          onSuccess={fetchBill}
        />
      </Space>
    </Card>
  );
}
