import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroyMock, useRemoteMock, webRequest } from '@manyun/service.request';

import { CustomersServiceEntries } from './customers-service-entries';

export const BasicCustomersServiceEntries = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    // webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    const mockOff = useRemoteMock('web');
    update(true);
    return () => {
      mockOff();
    };
  }, []);
  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <CustomersServiceEntries />
      </FakeStore>
    </ConfigProvider>
  );
};
