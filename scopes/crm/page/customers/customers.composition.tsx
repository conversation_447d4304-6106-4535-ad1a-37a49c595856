import React from 'react';
import { Link, Route, MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { CUSTOMERS_ROUTE_PATH } from '@manyun/crm.route.crm-routes';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock, webRequest } from '@manyun/service.request';

import { Customers } from './customers';

export const BasicCustomers = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  // React.useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   setReady(true);
  // }, []);

  if (!ready) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[CUSTOMERS_ROUTE_PATH]}>
          <Route path={CUSTOMERS_ROUTE_PATH}>
            <Customers />
          </Route>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
