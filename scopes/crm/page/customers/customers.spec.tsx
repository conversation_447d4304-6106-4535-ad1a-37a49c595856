import React from 'react';

import { act, render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { useRemoteMock } from '@manyun/service.request';

import { BasicCustomers } from './customers.composition';

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('customers', async () => {
  render(<BasicCustomers />);

  const nameFiled = await screen.findByRole('textbox', { name: 'input: name' });
  await userEvent.type(nameFiled, 'yuyu');

  await act(async () => {
    const searchBtn = screen.getByLabelText('button: search');
    userEvent.click(searchBtn);
    expect(screen.getByText('暂无数据')).toBeInTheDocument();
    const resetBtn = screen.getByLabelText('button: reset');
    userEvent.click(resetBtn);
    expect(screen.queryByText('yuyu')).toBeNull();
  });
});
