import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import omit from 'lodash.omit';

import { Form } from '@manyun/base-ui.ui.form';
import { Space } from '@manyun/base-ui.ui.space';

import { useCustomers } from '@manyun/crm.hook.use-customers';
import {
  customersSliceActions,
  selectCustomersFilters,
  selectCustomersPagination,
} from '@manyun/crm.state.customers';
import { CustomersDataTable } from '@manyun/crm.ui.customers-data-table';
import { CustomersFilters } from '@manyun/crm.ui.customers-filters';

export function Customers() {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [{ total, entities }, { readCustomers }] = useCustomers();
  const filterValues = useSelector(selectCustomersFilters());
  const pagination = useSelector(selectCustomersPagination());
  const searchFilterValues = {
    ...omit(filterValues, ['owner']),
    ownerId: filterValues.owner?.value,
  };

  React.useEffect(() => {
    form.setFieldsValue(filterValues);
    readCustomers({ ...pagination, ...searchFilterValues });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space size="large" style={{ display: 'flex' }} direction="vertical">
      <CustomersFilters
        form={form}
        onReset={() => {
          form.resetFields();
          const tmp = {
            name: undefined,
            level: undefined,
            industry: undefined,
            owner: undefined,
            code: undefined,
          };
          const pagination = {
            pageNum: 1,
            pageSize: 10,
          };
          dispatch(customersSliceActions.updateFilters(tmp));
          dispatch(customersSliceActions.updatePagination(pagination));
          readCustomers(pagination);
        }}
        onSearch={values => {
          const tmp = {
            ...omit(values, ['owner']),
            ownerId: values.owner?.value,
          };
          dispatch(customersSliceActions.updateFilters(values));
          dispatch(customersSliceActions.updatePagination({ pageNum: 1 }));
          return Promise.resolve(readCustomers({ ...tmp, ...pagination, pageNum: 1 }));
        }}
      />
      <CustomersDataTable
        data={entities}
        total={total}
        page={pagination.pageNum}
        pageSize={pagination.pageSize}
        onChange={pagination => {
          const { current, pageSize } = pagination;
          dispatch(customersSliceActions.updatePagination({ pageNum: current, pageSize }));
          readCustomers({ pageNum: current, pageSize, ...searchFilterValues });
        }}
      />
    </Space>
  );
}
