import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Space } from '@manyun/base-ui.ui.space';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { BillFilter } from '@manyun/crm.ui.bill-filter';
import type { FilteredValueMapper, FiltersFormValues } from '@manyun/crm.ui.bill-filter';
import { BillTable } from '@manyun/crm.ui.bill-table';

export function Bills() {
  const [form] = Form.useForm<FiltersFormValues>();
  const { search } = useLocation();
  const defaultFields = getLocationSearchMap<FilteredValueMapper>(search, {
    arrayKeys: ['billStatusList', 'todoTypeList', 'location'],
  });
  const [params, setParams] = useState<FilteredValueMapper>({
    ...defaultFields,
    page: Number(defaultFields.page ?? 1),
    pageSize: Number(defaultFields.pageSize ?? 10),
  });

  useShallowCompareEffect(() => {
    setLocationSearch(params);
  }, [params]);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card bordered={false}>
        <BillFilter
          feeLocation="operation"
          form={form}
          initialValues={params}
          onChange={values => setParams(values)}
        />
      </Card>
      <Card bordered={false}>
        <BillTable
          showEditColumns
          showColumns={[
            'billNo',
            'idcTag',
            'customerNo',
            'billTag',
            'billStatus',
            'waitHandleNum',
            'customerShortName',
          ]}
          filteredValueMapper={params}
          onParamsChange={values => setParams(values)}
        />
      </Card>
    </Space>
  );
}
