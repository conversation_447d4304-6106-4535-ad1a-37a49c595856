/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-customer-tins';
import type { CustomerNo, SvcRespData } from './fetch-customer-tins.type';

const executor = getExecutor(webRequest);

/**
 * @param customerNo
 * @returns
 */
export function fetchCustomerTins(
  customerNo: CustomerNo
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(customerNo);
}
