---
description: 'A fetchCustomerTins HTTP API service.'
labels: ['service', 'http', fetch-customer-tins]
---

## 概述

根据客户简称请求客户的发票信息

## 使用

### Browser

```ts
import { fetchCustomerTins } from '@manyun/crm.service.fetch-customer-tins';

const { error, data } = await fetchCustomerTins('拼多多');
const { error, data } = await fetchCustomerTins('ppp');
```

### Node

```ts
import { fetchCustomerTins } from '@manyun/crm.service.fetch-customer-tins/dist/index.node';

const { data } = await fetchCustomerTins('拼多多');

try {
  const { data } = await fetchCustomerTins('ppp');
} catch (error) {
  // ...
}
```
