/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, CustomerNo, RequestRespData, SvcRespData } from './fetch-customer-tins.type';

const endpoint = '/dctrans/bill/invoice/title/list';

/**
 * 客户抬头
 *
 * @see [Doc](YAPI http://172.16.0.17:13000/project/33/interface/api/5848)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (customerNo: CustomerNo): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { customerNo };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
