/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendInviceTitle = {
  id: number;
  invoiceTitle: string;
  taxpayerIdentityNo: string;
  phoneNo: string;
  bankName: string;
  bankAccount: string;
  companyAddress: string;
  modifierName: string;
  modifierId: number;
  gmtModified: number;
  defaultTitle: boolean;
  customerNo: string;
};

export type CustomerNo = string;

export type SvcRespData = {
  data: BackendInviceTitle[];
  total: number;
};

export type RequestRespData = {
  data: BackendInviceTitle[] | null;
  total: number;
} | null;

export type ApiQ = {
  customerNo: string;
};
