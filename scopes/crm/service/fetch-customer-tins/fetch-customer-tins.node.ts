/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-customer-tins';
import type { CustomerNo, SvcRespData } from './fetch-customer-tins.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchCustomerTins(
  variant: CustomerNo
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
