/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import omit from 'lodash.omit';

import { Customer } from '@manyun/crm.model.customer';
import type { BackendCustomer } from '@manyun/crm.model.customer';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { createEndpoint, updateEndpoint } from './mutate-customer';
import type {
  CreateApiQ,
  CreateSvcQuery,
  RequestRespData,
  SvcRespData,
  UpdateApiQ,
  UpdateSvcQuery,
} from './mutate-customer.type';

/**
 * 新增用户，修改客户
 *
 * @see [Doc](http://172.16.0.17:13000/project/33/interface/api/451，http://172.16.0.17:13000/project/33/interface/api/447)
 *
 * @param query Service query object
 * @returns
 */
export async function mutateCustomer(
  customer: CreateSvcQuery | UpdateSvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  if ('id' in customer) {
    const apiQ: UpdateApiQ = {
      ...omit(customer, ['owner']),
      ownerId: customer.owner?.id,
      ownerName: customer.owner?.name,
    };
    const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, UpdateApiQ>(
      updateEndpoint,
      apiQ
    );
    if (error) {
      return {
        error,
        data: null,
        ...rest,
      };
    }
    return {
      error,
      data: Customer.fromApiObject(data as BackendCustomer),
      ...rest,
    };
  }

  const apiQ: CreateApiQ = {
    ...omit(customer, ['owner']),
    ownerId: customer.owner?.id,
    ownerName: customer.owner?.name,
  };
  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, CreateApiQ>(
    createEndpoint,
    apiQ
  );
  if (error) {
    return {
      error,
      data: null,
      ...rest,
    };
  }
  return {
    error,
    data: Customer.fromApiObject(data as BackendCustomer),
    ...rest,
  };
}
