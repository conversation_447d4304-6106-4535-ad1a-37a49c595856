/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateCustomer as webService } from './mutate-customer.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('test mutate customer error', async () => {
  const tmp = {
    name: '[q7C[m2',
    code: '9Q#9',
    level: '1',
    description: 'IcT7vZ',
    id: 1,
    owner: {
      id: 1,
      name: '[NpW$1',
    },
  };
  const { data, error } = await webService(tmp);
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('test mutate customer success', async () => {
  const tmp = {
    name: '[q7C[m2',
    code: '9Q#9',
    level: '1',
    description: 'IcT7vZ',
    id: 2,
    owner: {
      id: 1,
      name: '[NpW$1',
    },
  };
  const { data, error } = await webService(tmp);
  expect(typeof data!.id).toBe('number');
  expect(error).toBe(undefined);
});
