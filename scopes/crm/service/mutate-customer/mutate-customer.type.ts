/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { Customer } from '@manyun/crm.model.customer';
import type { BackendCustomer } from '@manyun/crm.model.customer';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type UpdateSvcQuery = {
  id: number;
  code: string;
  level: string;
  name: string;
  owner: {
    id: number;
    name: string;
  };
  industry?: string;
  description?: string;
};

export type UpdateApiQ = {
  id: number;
  code: string;
  level: string;
  name: string;
  ownerId: number;
  ownerName: string;
  industry?: string;
  description?: string;
};

export type CreateSvcQuery = Omit<UpdateSvcQuery, 'id'>;

export type CreateApiQ = Omit<UpdateApiQ, 'id'>;

export type SvcRespData = Customer | null;

export type RequestRespData = BackendCustomer | null;

export type ApiR = Response<RequestRespData>;
