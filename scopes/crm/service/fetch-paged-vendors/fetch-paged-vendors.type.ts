/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-31
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  contactMobile?: number;
  contactName?: string;
  createTimeEnd?: string;
  createTimeStart?: string;
  operatorId?: number;
  pageNum: number;
  pageSize: number;
  vendorCode?: string;
  vendorName?: string;
  vendorLevel?: string;
  deviceType?: string;
};

export type VendorModel = {
  contactEmail: string;
  contactMobile: string;
  contactName: string;
  gmtCreate: string;
  gmtModified: string;
  id: number;
  operatorId: number;
  operatorName: string;
  vendorCode: string;
  vendorLevel: string;
  vendorName: string;
  description?: string;
};

export type SvcRespData = {
  data: VendorModel[];
  total: number;
};

export type RequestRespData = {
  data: VendorModel[] | null;
  total: number;
};

export type ApiQ = {
  contactMobile?: number;
  contactName?: string;
  createTimeEnd?: string;
  createTimeStart?: string;
  operatorId?: number;
  pageNum: number;
  pageSize: number;
  vendorCode?: string;
  vendorLevel?: string;
};

export type ApiR = ListResponse<RequestRespData['data']>;
