/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-31
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-vendors';
import type { RequestRespData, SvcQuery } from './fetch-paged-vendors.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPagedVendors(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<RequestRespData>> {
  return executor(svcQuery);
}
