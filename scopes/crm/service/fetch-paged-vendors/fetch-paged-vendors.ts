/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-31
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-paged-vendors.type';

const endpoint = '/dccm/vendor/query';

/**
 * @see [查询厂商列表](http://172.16.0.17:13000/project/100/interface/api/2816)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (query: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = query;

    const {
      error,
      data: { data, total },
      ...rest
    } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total,
        },
      };
    }

    return { error, data: { data, total }, ...rest };
  };
}
