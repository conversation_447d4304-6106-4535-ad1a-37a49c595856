/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  createTimeRange?: [number, number];
  deviceTypeList?: string[];
  modelCode?: string;
  operator?: string;
  vendorCode?: string;
  numbered?: boolean | null;
  page?: number;
  pageSize?: number;
};

export type ApiQ = {
  createTimeEnd?: number;
  createTimeStart?: number;
  deviceTypeList?: string[];
  modelCode?: string;
  operatorId?: string;
  numbered?: boolean | null;
  pageNum?: number;
  pageSize?: number;
  vendorCode?: string;
};

export type BackendModel = {
  description: string | null;
  deviceNum: number | null;
  deviceType: string;
  gmtCreate: string;
  gmtModified: string;
  id: number;
  numbered: boolean | null;
  modelCode: string;
  operatorId: number;
  operatorName: string;
  specNum: number | null;
  vendorCode: string;
};

export type SvcRespData = {
  data: BackendModel[];
  total: number;
};

export type RequestRespData = {
  data: BackendModel[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendModel[] | null>;
