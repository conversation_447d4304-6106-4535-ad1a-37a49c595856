/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-models';
import type { SvcQuery, SvcRespData } from './fetch-models.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchModels(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
