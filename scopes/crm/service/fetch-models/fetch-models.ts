/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import dayjs from 'dayjs';

import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-models.type';

const endpoint = '/dccm/model/query';

/**
 * @see [查询型号列表](http://172.16.0.17:13000/project/136/interface/api/7904)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    createTimeRange,
    operator,
    page,
    pageSize,
    ...others
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      createTimeEnd: createTimeRange ? Number(dayjs(createTimeRange[1]).unix() + '000') : undefined,
      createTimeStart: createTimeRange
        ? Number(dayjs(createTimeRange[0]).unix() + '000')
        : undefined,
      operatorId: operator,
      pageNum: page ?? 1,
      pageSize: pageSize ?? 10,
      ...others,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
