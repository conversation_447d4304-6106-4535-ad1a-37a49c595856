/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './delete-customer';
import type { ApiQ, ServiceD, ServiceR, ServiceRD } from './delete-customer.type';

/**
 * 删除客户
 *
 * @see [Doc](http://172.16.0.17:13000/project/33/interface/api/455)
 *
 * @param query Service query object
 * @returns
 */
export async function deleteCustomer(query: ServiceD): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = {
    id: query,
  };

  const { data, ...rest } = await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);

  return { ...rest, data: query };
}
