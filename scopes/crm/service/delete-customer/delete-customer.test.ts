/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteCustomer as webService } from './delete-customer.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('test delete customer success', async () => {
  const { data, error } = await webService(1);
  expect(error).toBe(undefined);
  expect(typeof data).toBe('number');
});

test('test delete customer error', async () => {
  const { data, error } = await webService(2);
  console.log('error', error);
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
  expect(typeof data).toBe('number');
});
