/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { Customer } from '@manyun/crm.model.customer';
import type { BackendCustomer } from '@manyun/crm.model.customer';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type RequestRD = {
  data: BackendCustomer[] | null;
  total: number;
} | null;

export type ServiceRD = {
  data: Customer[];
  total: number;
};

/** ServiceQ 也使用 ApiQ */
export type ApiQ = {
  code?: string | null;
  level?: string | null;
  name?: string | null;
  ownerId?: number | null;
  industry?: string | null;
  pageNum?: number;
  pageSize?: number;
};

export type ApiR = ListResponse<RequestRD>;
