/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCustomers as webService } from './fetch-customers.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('test fetch customers success', async () => {
  const { data, error } = await webService({ pageNum: 1, pageSize: 10 });
  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('test fetch customers error', async () => {
  const { data, error } = await webService({ pageNum: 2, pageSize: 10 });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
