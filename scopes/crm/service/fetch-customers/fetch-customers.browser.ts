/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-23
 *
 * @packageDocumentation
 */
import { Customer } from '@manyun/crm.model.customer';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-customers';
import type { ApiQ, RequestRD, ServiceRD } from './fetch-customers.type';

/**
 * 查询客户
 *
 * @see [Doc](http://172.16.0.17:13000/project/33/interface/api/443)
 *
 * @param query Service query object
 * @returns
 */
export async function fetchCustomers(query: ApiQ): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = query;

  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null || data.data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }
  return {
    ...rest,
    error,
    data: {
      total: data.total,
      data: data.data.map(Customer.fromApiObject),
    },
  };
}
