/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-vendor-model-tree';
import type { ApiQ, SvcRespData } from './fetch-vendor-model-tree.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchVendorModelTree(variant?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
