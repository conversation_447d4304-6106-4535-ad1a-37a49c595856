/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchVendorModelTree as webService } from './fetch-vendor-model-tree.browser';

// import { fetchVendorModelTree as nodeService } from './fetch-vendor-model-tree.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    deviceType: '10901',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    deviceType: 'aaaa',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  // const { error, data } = await nodeService('success');
  // expect(error).toBe(undefined);
  // expect(data.data).toHaveProperty('length');
  // expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  // const { error, data } = await nodeService('error');
  // expect(typeof error!.code).toBe('string');
  // expect(typeof error!.message).toBe('string');
  // expect(data.data).toHaveProperty('length');
  // expect(typeof data.total).toBe('number');
});
