---
description: 'A fetchVendorModelTree HTTP API service.'
labels: ['service', 'http', fetch-vendor-model-tree]
---

## 概述

获取厂商型号树

## 使用

### Browser

```ts
import { fetchVendorModelTree } from '@manyun/crm.service.fetch-vendor-model-tree';

const { error, data } = await fetchVendorModelTree({
  deviceType: '10901',
});
```

### Node

```ts
import { fetchVendorModelTree } from '@manyun/crm.service.fetch-vendor-model-tree/dist/index.node';

const { data } = await fetchVendorModelTree({
  deviceType: '10901',
});
```
