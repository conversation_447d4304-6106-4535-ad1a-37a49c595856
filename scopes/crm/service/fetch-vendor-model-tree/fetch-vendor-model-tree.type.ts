/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-15
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

// 厂商类型
export type Vendor = {
  metaType: string;
  metaCode: string;
  metaName: string;
  parentCode: string;
  children: Model[] | [];
};
// 型号类型
export type Model = {
  metaType: string;
  metaCode: string;
  metaName: string;
  parentCode: string;
  children: Model[] | [];
};

export type SvcRespData = {
  data: Vendor[];
  total: number;
};

export type RequestRespData = {
  data: Vendor[] | null;
  total: number;
} | null;

// SvcQuery 和 apiQ相同
export type ApiQ = {
  // 设备类型
  deviceType?: string;
  // 厂商简称
  vendorCode?: string;
  // 不传，全部，true：查询有资产，false：查询无资产
  numbered?: boolean;
};

export type ApiR = ListResponse<Vendor[] | null>;
