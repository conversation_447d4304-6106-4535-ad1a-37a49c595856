/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-models';
import type { SvcQuery, SvcRespData } from './create-models.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function createModels(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
