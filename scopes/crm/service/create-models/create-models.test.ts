/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createModels as webService } from './create-models.browser';
import { createModels as nodeService } from './create-models.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    productModels: [
      {
        deviceType: '10101',
        modelCode: '111',
        vendorCode: '222',
      },
    ],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    productModels: [
      {
        deviceType: '10102',
        modelCode: '111',
        vendorCode: '222',
      },
    ],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    productModels: [
      {
        deviceType: '10101',
        modelCode: '111',
        vendorCode: '222',
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    productModels: [
      {
        deviceType: '10102',
        modelCode: '111',
        vendorCode: '222',
      },
    ],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
