/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ProductModel = {
  description?: string;
  deviceType: string;
  modelCode: string;
  vendorCode: string;
  specModels?: SpecModel[];
};
export type SpecModel = {
  specId: number;
  modelCode: string;
  specName: string;
  specValue?: string | number;
};

export type SvcQuery = {
  productModels: ProductModel[];
};

export type SvcRespData = boolean;

export type ApiR = WriteResponse;
