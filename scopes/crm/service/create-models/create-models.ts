/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery, SvcRespData } from './create-models.type';

const endpoint = '/dccm/model/batch/add';

/**
 * @see [Doc](http://172.16.0.17:13000/project/140/interface/api/14928)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<SvcRespData, SvcQuery>(endpoint, params);
  };
}
