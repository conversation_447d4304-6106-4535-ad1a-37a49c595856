import { useCallback, useReducer } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Customer } from '@manyun/crm.model.customer';
import type { ApiQ as FetchCustomersApiQ } from '@manyun/crm.service.fetch-customers';
import {
  deleteCustomerAction,
  getCustomersAction,
  mutateCustomerAction,
  selectCustomersEntitiesByIds,
} from '@manyun/crm.state.customers';

export type CustomersData = {
  entities: Record<number, Customer | undefined>;
  ids: number[];
  total: number;
  loading: boolean;
};

export type Callbacks = {
  createCustomer: (customerLevel: Omit<Customer, 'id'>) => Promise<Customer>;
  readCustomers: () => Promise<CustomersData>;
  updateCustomer: (customerLevel: Customer) => Promise<Customer>;
  deleteCustomer: (id: number) => Promise<number>;
};

export type UseCustomersState = {
  loading: boolean;
  ids: number[];
  total: number;
};

export type GetCustomersStartAction = {
  type: 'GET_CUSTOMERS_START';
  payload: any;
};

export type SetCustomersAction = {
  type: 'SET_CUSTOMERS';
  payload: {
    ids: number[];
    total: number;
  };
};

export type UseCustomersActions = GetCustomersStartAction | SetCustomersAction;

const reducers: React.Reducer<UseCustomersState, UseCustomersActions> = (state, action) => {
  switch (action.type) {
    case 'GET_CUSTOMERS_START':
      return {
        ...state,
        loading: true,
      };
    case 'SET_CUSTOMERS':
      return {
        ...state,
        loading: false,
        ids: action.payload.ids,
        total: action.payload.total,
      };
  }
};

export function useCustomers() {
  const initialState = { loading: false, ids: [], total: 0 };
  const dispatch = useDispatch();
  const [state, selfDispatch] = useReducer(reducers, initialState);
  const entities = useSelector(selectCustomersEntitiesByIds(state.ids));

  const readCustomers = useCallback(
    (params: FetchCustomersApiQ) => {
      selfDispatch({ type: 'GET_CUSTOMERS_START', payload: {} });
      dispatch(
        getCustomersAction({
          params,
          callback: (ids, total) => {
            selfDispatch({ type: 'SET_CUSTOMERS', payload: { ids, total } });
          },
        })
      );
    },
    [dispatch]
  );

  const createCustomer = useCallback(
    ({ params, callback }) => {
      dispatch(
        mutateCustomerAction({
          params,
          callback: () => {
            callback();
          },
        })
      );
    },
    [dispatch]
  );

  const updateCustomer = useCallback(
    ({ params, callback }) => {
      dispatch(
        mutateCustomerAction({
          params,
          callback: () => {
            callback();
          },
        })
      );
    },
    [dispatch]
  );

  const deleteCustomer = useCallback(
    ({ params, callback }) => {
      dispatch(
        deleteCustomerAction({
          params,
          callback: () => {
            callback();
          },
        })
      );
    },
    [dispatch]
  );

  return [
    { ...state, entities },
    { readCustomers, createCustomer, updateCustomer, deleteCustomer },
  ] as const;
}
