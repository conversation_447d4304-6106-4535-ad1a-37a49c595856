import { act, renderHook } from '@testing-library/react-hooks';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { useCustomers } from './use-customers';

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

it('useCustomers', () => {
  const { result } = renderHook(() => useCustomers(), { wrapper: FakeStore });
  act(() => {
    result.current[1].readCustomers({ pageNum: 1, pageSize: 10 });
  });
  expect(typeof result.current[0].total).toBe('number');
});
