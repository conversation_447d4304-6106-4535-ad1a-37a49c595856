import React from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { useCustomers } from './use-customers';

function Customers() {
  const [{ loading, total }, { readCustomers }] = useCustomers();
  const [ready, setReady] = React.useState(false);

  // React.useEffect(() => {
  //   webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
  //   setReady(true);
  // }, []);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <>loading</>;
  }

  return (
    <>
      <h1>The loading is {loading}</h1>
      <h1>The total is {total}</h1>
      <button onClick={() => readCustomers({ pageNum: 1, pageSize: 10 })}>readCustomers</button>
    </>
  );
}

export const BasicuseCustomers = () => {
  return (
    <FakeStore>
      <Customers />
    </FakeStore>
  );
};
