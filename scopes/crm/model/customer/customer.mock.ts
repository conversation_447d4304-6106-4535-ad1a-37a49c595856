import type { BackendCustomer } from './index';

export const mockList: BackendCustomer[] = [
  {
    id: 0,
    code: '211',
    name: 'name',
    level: 'level',
    gmtCreate: Date.now(),
    gmtModified: Date.now(),
    ownerId: 1,
    ownerName: 'admin',
    industry: null,
    description: null,
  },
];

export function getRandomMockCustomer() {
  const userIdx = Math.floor(Math.random() * mockList.length);

  return mockList[userIdx];
}
