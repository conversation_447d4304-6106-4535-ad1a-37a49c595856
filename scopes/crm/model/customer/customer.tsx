export type BackendCustomer = {
  id: number;
  code: string;
  name: string;
  level: string;
  gmtCreate: number;
  gmtModified: number;
  ownerId: number;
  ownerName: string;
  industry: string | null;
  description: string | null;
};

export class Customer {
  constructor(
    public id: number,
    public code: string,
    public name: string,
    public level: string,
    public owner: {
      id: number;
      name: string;
    },
    public gmtCreate: number,
    public gmtModified: number,
    public industry: string | null,
    public description: string | null
  ) {}

  static fromApiObject(object: BackendCustomer) {
    return new Customer(
      object.id,
      object.code,
      object.name,
      object.level,
      {
        id: object.ownerId,
        name: object.ownerName,
      },
      object.gmtCreate,
      object.gmtModified,
      object.industry,
      object.description
    );
  }
}
