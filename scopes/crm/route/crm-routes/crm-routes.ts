import { utilities } from '@manyun/dc-brain.navigation.link';

const { generatePath } = utilities;

export const CUSTOMERS_ROUTE_PATH = '/page/crm/customers';
export const CUSTOMERS_SERVICE_ENTRIES_ROUTE_PATH = '/page/crm/customers-service-entries';
export const SUPPLIER_SERVICE_ENTRIES_ROUTE_PATH = '/page/crm/supplier-service-entries';
export const VENDORS_ROUTE_PATH = '/page/supplier/vendor/list';
export const VENDOR_ROUTE_PATH = '/page/supplier/vendor/:id';
export const MODELS_ROUTE_PATH = '/page/supplier/model/list';
export const NEW_MODEL_ROUTE_PATH = '/page/supplier/model/new';
/**账单列表 */
export const BILLS_ROUTE_PATH = '/page/bill/list';
/**账单详情 */
export const BILL_ROUTE_PATH = '/page/bill/:id/detail';

export type SpecialBillParams = {
  id: string;
};

/**跳转账单详情 */
export const generateBillDetailUrl = (params: SpecialBillParams) =>
  generatePath(BILL_ROUTE_PATH, params);
