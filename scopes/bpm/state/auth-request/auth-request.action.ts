import { createAction } from '@reduxjs/toolkit';

import type { CreateSvcD } from '@manyun/bpm.service.create-bpm-instance';

import { authRequestSlice } from './auth-rquest.slice';

const prefix = authRequestSlice.name;

export const authRequestSliceActions = authRequestSlice.actions;

type CreateAuthRequestActionPayload = CreateSvcD & { callback?: (result: string | null) => void };

export const createAuthRequestAction = createAction<CreateAuthRequestActionPayload>(
  prefix + '/CREATE_AUTH_REQUEST'
);
