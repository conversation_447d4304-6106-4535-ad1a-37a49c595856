/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-02
 *
 * @packageDocumentation
 */
import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import uniq from 'lodash.uniq';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

export type AuthRequestCreate = {
  requester?: {
    id: number;
    name: string;
  };
  title?: string; //申请标题
  descriptions?: string; //申请说明
  attachments?: (McUploadFile & { uid: string })[]; //附件
  userGroupCodes?: string[];
  loading: boolean;
};

export type SetCreateAuthRequestPayload = Partial<Omit<AuthRequestCreate, 'loading'>>;

export type AuthRequestSliceState = {
  create: AuthRequestCreate;
};

type AuthRequestSliceCaseReducers = {
  setCreateAuthRequest: CaseReducer<
    AuthRequestSliceState,
    PayloadAction<SetCreateAuthRequestPayload>
  >;
  resetCreateAuthRequest: CaseReducer<AuthRequestSliceState, PayloadAction<undefined>>;
  createAuthRequestStart: CaseReducer<AuthRequestSliceState, PayloadAction<undefined>>;
  createAuthRequestEnd: CaseReducer<AuthRequestSliceState, PayloadAction<undefined>>;
};

export const authRequestSlice = createSlice<
  AuthRequestSliceState,
  AuthRequestSliceCaseReducers,
  'bpm.auth-request'
>({
  name: 'bpm.auth-request',
  initialState: {
    create: getInitialAuthCreate(),
  },
  reducers: {
    setCreateAuthRequest(sliceState, { payload: { ...params } }) {
      if (params.userGroupCodes != undefined) {
        params.userGroupCodes = uniq(params.userGroupCodes);
      }
      sliceState.create = { ...sliceState.create, ...params };
    },

    resetCreateAuthRequest(sliceState, payload) {
      sliceState.create = getInitialAuthCreate();
    },

    createAuthRequestStart(sliceState, payload) {
      sliceState.create.loading = true;
    },

    createAuthRequestEnd(sliceState, payload) {
      sliceState.create.loading = false;
    },
  },
});

export function getInitialAuthCreate(): AuthRequestCreate {
  return {
    loading: false,
    requester: undefined,
    userGroupCodes: undefined,
    attachments: undefined,
    // [
    //   {
    //     ext: '.png',
    //     name: '冬天.png',
    //     size: 42258,
    //     type: 'image/png',
    //     uid: 'group1/M00/00/04/rBAAD2Gp726AUEeSAAClEvYXlwA983.png',
    //     patialPath: 'group1/M00/00/04/rBAAD2Gp726AUEeSAAClEvYXlwA983.png',
    //     uploadedAt: 1638521845352,
    //     uploadUser: {
    //       id: 24,
    //       name: '王彦苏',
    //     },
    //     src: '/api/dcom/file/download?filePath=group1/M00/00/04/rBAAD2Gp726AUEeSAAClEvYXlwA983.png',
    //   },
    // ],
    descriptions: undefined,
    title: undefined,
  };
}
