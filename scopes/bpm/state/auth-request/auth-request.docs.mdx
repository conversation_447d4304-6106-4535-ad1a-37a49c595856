---
description: 'Roles redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import rolesSliceReducer from '@manyun/[scope].state.roles';

const rootReducer = {
  ...rolesSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { rolesWatchers } from '@manyun/[scope].state.roles';

const function* rootSaga() {
  yield all(
    ...rolesWatchers,
    // other sagas...
  );
};
```
