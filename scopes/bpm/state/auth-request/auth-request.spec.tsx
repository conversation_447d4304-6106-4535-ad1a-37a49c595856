import { authRequestSliceActions, createAuthRequestAction } from './auth-request.action';
import { authRequestSlice } from './auth-rquest.slice';
import type { AuthRequestCreate, AuthRequestSliceState } from './auth-rquest.slice';

test('should return the initial state', () => {
  expect(authRequestSlice.reducer(undefined, {} as any)).toEqual<AuthRequestSliceState>({
    create: {
      loading: false,
    },
  });
});

test('sholue put a `setCreateAuthRequest` action', () => {
  const initialState = authRequestSlice.reducer(undefined, {} as any);
  const changeState = authRequestSlice.reducer(
    initialState,
    authRequestSlice.actions.setCreateAuthRequest({
      descriptions: '流程创建',
    })
  );
  expect(changeState.create).toHaveProperty('descriptions');
  expect(changeState.create.descriptions).toBe('流程创建');
});
