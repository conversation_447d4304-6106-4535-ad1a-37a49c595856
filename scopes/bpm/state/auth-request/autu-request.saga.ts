import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { createBpmInstance } from '@manyun/bpm.service.create-bpm-instance';

import { authRequestSliceActions, createAuthRequestAction } from './auth-request.action';

/** Workers */

export function* createAuthRequestSaga({
  payload: { callback, ...rest },
}: ReturnType<typeof createAuthRequestAction>) {
  yield put(authRequestSliceActions.createAuthRequestStart());

  const { error, data }: SagaReturnType<typeof createBpmInstance> = yield call<
    typeof createBpmInstance
  >(createBpmInstance, { ...rest });

  if (error) {
    message.error(error.message);
    callback && callback(null);
    yield put(authRequestSliceActions.createAuthRequestEnd());
    return;
  }

  yield put(authRequestSliceActions.createAuthRequestEnd());
  yield put(authRequestSliceActions.resetCreateAuthRequest());
  callback && callback(data);
}

/** Watchers */

function* watchCreateAuthRequest() {
  yield takeLatest(createAuthRequestAction.type, createAuthRequestSaga);
}

export default [fork(watchCreateAuthRequest)];
