import approvalCenterWatchers from './approval-center.saga';
import { approvalCenterSlice } from './approval-center.slice';

export type { ApprovalCenterSliceState, ApprovalCenterSearchValues } from './approval-center.slice';
export * from './approval-center.action';
export * from './approval-center.selector';
export { approvalCenterWatchers };
export default {
  [approvalCenterSlice.name]: approvalCenterSlice.reducer,
};

// dev use only
// ------
export { createCompositionWrapper } from './approval-center.composition-wrapper.creator';
