---
description: 'ApprovalCenter redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import approvalCenterSliceReducer from '@manyun/bpm.state.approval-center';

const rootReducer = {
  ...approvalCenterSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { approvalCenterWatchers } from '@manyun/bpm.state.approval-center';

const function* rootSaga() {
  yield all(
    ...approvalCenterWatchers,
    // other sagas...
  );
};
```
