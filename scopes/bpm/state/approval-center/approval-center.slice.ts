import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';

const initialState = {
  searchValues: {},
  type: 'WAIT',
  pagination: {
    pageNum: 1,
    pageSize: 10,
  },
  data: [],
  total: 0,
  isRead: '0',
  loading: false,
};
export type ApprovalCenterSearchValues = {
  instId?: string;
  title?: string;
  bizType?: string;
  bizId?: string;
  blockGuid?: string;
  startTime?: string[];
  applicantId?: string;
};
export type ApprovalCenterSliceState = {
  searchValues: ApprovalCenterSearchValues;
  type: string;
  pagination: {
    pageNum: number;
    pageSize: number;
  };
  isRead: string;
  data: BpmInstance[];
  total: number;
  loading: boolean;
};

type ApprovalCenterSliceCaseReducers = {
  fetchApprovalCenterStart: CaseReducer<ApprovalCenterSliceState, PayloadAction<undefined>>;

  //inherit from old slice, should confirm payload type ,todo
  updateSearchValues: CaseReducer<
    ApprovalCenterSliceState,
    PayloadAction<ApprovalCenterSearchValues>
  >;
  setSearchValuesAndPagination: CaseReducer<
    ApprovalCenterSliceState,
    PayloadAction<ApprovalCenterSearchValues>
  >;
  setType: CaseReducer<ApprovalCenterSliceState, PayloadAction<string>>;

  setPagination: CaseReducer<
    ApprovalCenterSliceState,
    PayloadAction<{
      pageNum: number;
      pageSize: number;
    }>
  >;
  setReadStatus: CaseReducer<ApprovalCenterSliceState, PayloadAction<{ isRead: string }>>;

  setDataAndTotal: CaseReducer<
    ApprovalCenterSliceState,
    PayloadAction<{
      data: BpmInstance[];
      total: number;
    }>
  >;
  resetPageNum: CaseReducer<
    ApprovalCenterSliceState,
    PayloadAction<{ pagination: { pageNum: number; pageSize: number } }>
  >;

  resetSearchValuesAndPagination: CaseReducer<ApprovalCenterSliceState, PayloadAction<undefined>>;

  fetchApprovalCenterError: CaseReducer<ApprovalCenterSliceState, PayloadAction<undefined>>;
};

export const approvalCenterSlice = createSlice<
  ApprovalCenterSliceState,
  ApprovalCenterSliceCaseReducers,
  'approval-center'
>({
  name: 'approval-center',
  initialState: initialState,
  reducers: {
    fetchApprovalCenterStart(sliceState) {
      sliceState.loading = true;
    },

    updateSearchValues(sliceState, { payload }) {
      sliceState.searchValues = {
        ...sliceState.searchValues,
        ...payload,
      };
    },
    resetSearchValuesAndPagination(sliceState) {
      sliceState.searchValues = {};
      sliceState.pagination = { ...initialState.pagination };
    },
    setSearchValuesAndPagination(sliceState, { payload }) {
      sliceState.searchValues = {
        ...sliceState.searchValues,
        ...payload,
      };
      sliceState.pagination = { ...initialState.pagination };
    },
    setPagination(sliceState, { payload }) {
      sliceState.pagination = payload;
    },
    setDataAndTotal(sliceState, { payload: { data, total } }) {
      sliceState.data = data;
      sliceState.total = total;
      sliceState.loading = false;
    },
    resetPageNum({ pagination }) {
      pagination.pageNum = 1;
    },
    setType(sliceState, { payload }) {
      sliceState.type = payload;
    },
    setReadStatus(sliceState, { payload }) {
      sliceState.isRead = payload.isRead;
    },
    fetchApprovalCenterError(sliceState) {
      sliceState.loading = false;
    },
  },
});
