import React from 'react';

import { configureStore } from '@reduxjs/toolkit';
import type { ReducersMapObject } from 'redux';
import createSagaMiddleware from 'redux-saga';
import { all } from 'redux-saga/effects';
import type { ForkEffect } from 'redux-saga/effects';

import { StoreProvider } from '@manyun/base-ui-web.context.store-context';

import approvalCenterSliceReducer, { approvalCenterWatchers } from '.';

export function createCompositionWrapper({
  extraReducers,
  extraSagas,
}: {
  extraReducers?: ReducersMapObject;
  extraSagas?: ForkEffect<void>[];
} = {}) {
  const sagaMiddleware = createSagaMiddleware();
  const store = configureStore({
    reducer: {
      ...approvalCenterSliceReducer,
      ...extraReducers,
    },
    middleware: [sagaMiddleware],
  });
  function* rootSaga() {
    yield all([...approvalCenterWatchers, ...(extraSagas ?? [])]);
  }
  sagaMiddleware.run(rootSaga);

  const CompositionWrapper: React.FC = ({ children }) => (
    <StoreProvider store={store}>{children}</StoreProvider>
  );

  return CompositionWrapper;
}
