import { createAction } from '@reduxjs/toolkit';

import { approvalCenterSlice } from './approval-center.slice';

const prefix = approvalCenterSlice.name;

export const approvalCenterSliceActions = approvalCenterSlice.actions;

export const getApprovalCenterAction = createAction<{ shouldResetPageNum: boolean }>(
  prefix + '/GET_APPROVAL_CENTER'
);

export const setPaginationThenGetDataActionCreator = createAction<{
  pageNum: number;
  pageSize: number;
}>(prefix + 'SET_PAGINATION_THEN_GET_DATA');

export const setReadStatusAction = createAction<{
  isRead: string;
}>(prefix + 'SET_READ_STATUS');

export const resetSearchValuesActionCreator = createAction<undefined>(
  prefix + 'RESET_SEARCH_VALUES'
);
export const setSearchValuesActionCreator = createAction<any>(prefix + 'SET_SEARCH_VALUES');
