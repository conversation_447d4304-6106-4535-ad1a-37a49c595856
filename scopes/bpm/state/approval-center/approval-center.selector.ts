import { approvalCenterSlice } from './approval-center.slice';
import type { ApprovalCenterSliceState } from './approval-center.slice';

export const selectSearchValuesAndPagination = (storeState: {
  [approvalCenterSlice.name]: ApprovalCenterSliceState;
}) => {
  const { searchValues, pagination, type, isRead } = storeState[approvalCenterSlice.name];
  return {
    searchValues,
    pagination,
    type,
    isRead,
  };
};

export const selectApprovalCenter = (storeState: {
  [approvalCenterSlice.name]: ApprovalCenterSliceState;
}) => {
  const { data, total, pagination, loading, isRead } = storeState[approvalCenterSlice.name];
  return { data, total, pagination, loading, isRead };
};
