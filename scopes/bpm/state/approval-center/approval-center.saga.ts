import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { fetchCarbonCopyList } from '@manyun/bpm.service.fetch-carbon-copy-list';

import {
  approvalCenterSliceActions,
  getApprovalCenterAction,
  resetSearchValuesActionCreator,
  setPaginationThenGetDataActionCreator,
  setReadStatusAction,
  setSearchValuesActionCreator,
} from './approval-center.action';
import { selectSearchValuesAndPagination } from './approval-center.selector';
import type { ApprovalCenterSearchValues } from './approval-center.slice';

function generateQueryParams(
  pagination: {
    pageNum: number;
    pageSize: number;
  },
  searchValues: ApprovalCenterSearchValues
) {
  const { blockGuid, startTime, ...rest } = searchValues;
  return {
    ...pagination,
    ...rest,
    createTimeStart: startTime?.[0],
    createTimeEnd: startTime?.[1],
    blockGuid: blockGuid?.split('.')?.length === 2 ? blockGuid : undefined,
    idcTag: blockGuid?.split('.')?.length !== 2 ? blockGuid : undefined,
  };
}

/** Workers */

export function* getApprovalCenterSaga({ payload }: ReturnType<typeof getApprovalCenterAction>) {
  const { shouldResetPageNum = true } = payload;
  yield put(approvalCenterSliceActions.fetchApprovalCenterStart());
  if (shouldResetPageNum) {
    yield put(approvalCenterSliceActions.resetSearchValuesAndPagination());
  }
  // type 字段暂时没有，这里只对抄送类型使用重构的redux代码，todo（剩余类型：待我审批、我已审批、我发起的）
  // 因为剩余的类型在重构时还得重构3个service并且考虑影响面

  const { searchValues, pagination } = yield select(selectSearchValuesAndPagination);
  const queryParams = generateQueryParams(pagination, searchValues);

  const { error, data }: SagaReturnType<typeof fetchCarbonCopyList> = yield call(
    fetchCarbonCopyList,
    {
      ...queryParams,
    }
  );

  if (error) {
    message.error(error.message);
    yield put(approvalCenterSliceActions.fetchApprovalCenterError());
    return;
  }

  yield put(approvalCenterSliceActions.setDataAndTotal(data));
}

function* setPagination({ payload }: ReturnType<typeof setPaginationThenGetDataActionCreator>) {
  yield put(approvalCenterSliceActions.setPagination(payload));
  yield put(getApprovalCenterAction({ shouldResetPageNum: false }));
}

function* resetSearchValues() {
  yield put(approvalCenterSliceActions.resetSearchValuesAndPagination());
  yield put(getApprovalCenterAction({ shouldResetPageNum: false }));
}

function* setSearchValues({ payload }: ReturnType<typeof setSearchValuesActionCreator>) {
  yield put(approvalCenterSliceActions.setSearchValuesAndPagination(payload));
  yield put(getApprovalCenterAction({ shouldResetPageNum: false }));
}

function* setReadStatus({ payload }: ReturnType<typeof setReadStatusAction>) {
  yield put(approvalCenterSliceActions.setReadStatus(payload));
  yield put(getApprovalCenterAction({ shouldResetPageNum: true }));
}

/** Watchers */

function* watchGetApprovalCenter() {
  yield takeLatest(getApprovalCenterAction.type, getApprovalCenterSaga);
}
function* watchSetPagination() {
  yield takeLatest(setPaginationThenGetDataActionCreator.type, setPagination);
}

function* watchSetReadStatus() {
  yield takeLatest(setReadStatusAction.type, setReadStatus);
}

function* watchResetSearchValues() {
  yield takeLatest(resetSearchValuesActionCreator.type, resetSearchValues);
}

function* watchSetSearchValues() {
  yield takeLatest(setSearchValuesActionCreator.type, setSearchValues);
}
export default [
  fork(watchGetApprovalCenter),
  fork(watchSetPagination),
  fork(watchSetReadStatus),
  fork(watchResetSearchValues),
  fork(watchSetSearchValues),
];
