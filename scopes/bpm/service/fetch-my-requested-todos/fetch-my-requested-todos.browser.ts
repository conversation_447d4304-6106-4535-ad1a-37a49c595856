/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-my-requested-todos';
import type { SvcQuery, SvcRespData } from './fetch-my-requested-todos.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchMyRequestedTodos(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
