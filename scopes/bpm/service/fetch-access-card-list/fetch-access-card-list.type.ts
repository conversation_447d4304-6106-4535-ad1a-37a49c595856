/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-2
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AccessCardUser = {
  applyId: number;
  applyName: string;
  /** 人员角色 */
  applyRole: string;
  dept: string;
  company: string;
  phone: string;
  email: string;
  cardType: string;
  cardNo: string;
  blockTagList: string[];
};

export type SvcRespData = {
  data: AccessCardUser[];
  total: number;
};

export type RequestRespData = {
  data: AccessCardUser[] | null;
  total: number;
} | null;
// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  instId: string;
  pageNum: number;
  pageSize: number;
};

export type ApiR = ListResponse<AccessCardUser[] | null>;
