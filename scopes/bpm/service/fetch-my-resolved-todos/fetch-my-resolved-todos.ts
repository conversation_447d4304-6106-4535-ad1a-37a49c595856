/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-my-resolved-todos.type';

const endpoint = '/workflow/instance/already/handle';

/**
 * @see [我已审批列表](http://172.16.0.17:13000/project/51/interface/api/6688)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => BpmInstance.fromApiObject(d)), total: data.total },
      ...rest,
    };
  };
}
