/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { BackendBpmInstance, BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  instId?: string;
  instStatusList?: string[];
  title?: string;
  bizType?: string;
  bizId?: string;
  blockGuid?: string;
  idcTag?: string;
  roomTag?: string;
  applicantId?: string;
  creatorId?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  finishTimeStart?: string;
  finishTimeEnd?: string;
  pageNum: number;
  pageSize: number;
};

export type SvcRespData = {
  data: BpmInstance[];
  total: number;
};

export type RequestRespData = {
  data: BackendBpmInstance[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendBpmInstance[] | null>;
