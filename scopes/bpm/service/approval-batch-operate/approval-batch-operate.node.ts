/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-9
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './approval-batch-operate';
import type { ApiQ, SvcRespData } from './approval-batch-operate.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function approvalBatchOperate(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
