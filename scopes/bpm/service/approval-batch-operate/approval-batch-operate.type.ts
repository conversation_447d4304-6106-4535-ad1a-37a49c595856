/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-9
 *
 * @packageDocumentation
 */
import type { BatchOperationResult } from '@manyun/bpm.model.bpm-instance';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type TaskApprovalInfo = {
  instId: string;
  taskId: string;
};

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;
// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  /** 1 是拒绝 ， 0 是同意 */
  result: BatchOperationResult;
  taskApprovalInfoList: TaskApprovalInfo[];
  comment?: string;
};

export type ApiR = WriteResponse;
