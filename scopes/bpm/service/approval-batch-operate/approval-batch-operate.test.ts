/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { approvalBatchOperate as webService } from './approval-batch-operate.browser';
import { approvalBatchOperate as nodeService } from './approval-batch-operate.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ result: 1, taskApprovalInfoList: [] });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ result: 0, taskApprovalInfoList: [] });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ result: 1, taskApprovalInfoList: [] });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ result: 0, taskApprovalInfoList: [] });

  expect(typeof error).toBe('object');
});
