/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-bpm-refused-records';
import type { ApiQ, PagedRefusedRecord } from './fetch-bpm-refused-records.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchBpmRefusedRecords(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<PagedRefusedRecord>> {
  return executor(svcQuery);
}
