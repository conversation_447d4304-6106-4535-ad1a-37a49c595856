/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-19
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type RefusedRecord = {
  instId: string;
  refuseReason: string;
  refuseNode: string;
  userId: number;
  userName: string;
  refuseTime: string;
};

export type PagedRefusedRecord = {
  data: RefusedRecord[];
  total: number;
};
// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  bizId: string;
  bizType: string;
};

export type ApiR = ListResponse<RefusedRecord[] | null>;
