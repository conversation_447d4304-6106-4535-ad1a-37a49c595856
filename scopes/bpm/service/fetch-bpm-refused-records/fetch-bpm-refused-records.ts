/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, PagedRefusedRecord } from './fetch-bpm-refused-records.type';

const endpoint = '/workflow/process/refuse/list';

/**
 * @see [Doc](http://172.16.0.17:13000/project/51/interface/api/24141)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<PagedRefusedRecord>> => {
    const { error, data, ...rest } = await request.tryPost<PagedRefusedRecord | null, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
