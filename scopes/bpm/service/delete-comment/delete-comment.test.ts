/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteComment as webService } from './delete-comment.browser';
import { deleteComment as nodeService } from './delete-comment.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ instId: 'SUCCESS_ID', commentId: 0 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ instId: 'FAIL_ID', commentId: 0 });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ instId: 'SUCCESS_ID', commentId: 0 });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ instId: 'FAIL_ID', commentId: 0 });

  expect(typeof error).toBe('object');
});
