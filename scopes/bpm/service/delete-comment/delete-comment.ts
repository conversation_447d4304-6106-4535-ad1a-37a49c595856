/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-20
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './delete-comment.type';

const endpoint = '/workflow/process/delete/comment';

/**
 * @see [Doc]( http://172.16.0.17:13000/project/51/interface/api/20298)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }
    return { error, data, ...rest };
  };
}
