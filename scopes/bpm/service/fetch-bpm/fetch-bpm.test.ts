/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchBpm as webService } from './fetch-bpm.browser';
import { fetchBpm as nodeService } from './fetch-bpm.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ code: '11111' });
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('name');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ code: '' });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBeNull();
});
