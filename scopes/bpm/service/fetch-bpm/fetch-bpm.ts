/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-bpm.type';

const endpoint = '/workflow/process/info/detail';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/51/interface/api/5696)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = { processCode: variant.code };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: {
        xml: data.processXml,
        name: data.processName,
        code: data.processCode,
        autoApprovalType: data.autoApprovalType,
      },
      ...rest,
    };
  };
}
