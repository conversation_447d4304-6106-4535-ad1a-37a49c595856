/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-bpm';
import type { SvcQuery, SvcRespData } from './fetch-bpm.type';

const executor = getExecutor(webRequest);

/**
 * @param query
 * @returns
 */
export function fetchBpm(query: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(query);
}
