---
description: 'A fetchBpm HTTP API service.'
labels: ['service', 'http', fetch-bpm]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchBpm } from '@manyun/bpm.service.fetch-bpm';

const { error, data } = await fetchBpm({ code: '11111' });
```

### Node

```ts
import { fetchBpm } from '@manyun/bpm.service.fetch-bpm/dist/index.node';

const { data } = await fetchBpm({ code: '11111' });

try {
  const { data } = await fetchBpm({ code: '' });
} catch (error) {
  // ...
}
```
