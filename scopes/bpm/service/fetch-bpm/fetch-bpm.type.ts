/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type RequestRespData = {
  processXml: string;
  processName: string;
  processCode: string;
  autoApprovalType: number;
} | null;

export type SvcQuery = {
  code: string;
};

export type SvcRespData = {
  xml: string;
  name: string;
  code: string;
  autoApprovalType: number;
} | null;

export type ApiQ = {
  processCode: string;
};

export type ApiR = Response<RequestRespData>;
