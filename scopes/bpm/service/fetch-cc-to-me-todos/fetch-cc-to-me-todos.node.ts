/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-cc-to-me-todos';
import type { SvcQuery, SvcRespData } from './fetch-cc-to-me-todos.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchCcToMeTodos(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
