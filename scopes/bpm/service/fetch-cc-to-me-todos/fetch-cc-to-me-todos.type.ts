/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-18
 *
 * @packageDocumentation
 */
import type { BackendBpmInstance, BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  pageNum: number;
  pageSize: number;
  isRead?: boolean;
};

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  isRead?: string;
};

export type SvcRespData = {
  data: BpmInstance[];
  total: number;
};

export type RequestRespData = {
  data: BackendBpmInstance[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendBpmInstance[] | null>;
