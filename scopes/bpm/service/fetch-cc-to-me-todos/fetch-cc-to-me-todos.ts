/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-18
 *
 * @packageDocumentation
 */
import { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-cc-to-me-todos.type';

const endpoint = '/workflow/instance/cc';

/**
 * @see [抄送我的审批](http://172.16.0.17:13000/project/51/interface/api/6744)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    pageNum,
    pageSize,
    isRead,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum,
      pageSize,
      isRead: typeof isRead === 'boolean' ? (isRead ? '1' : '0') : isRead,
    };
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => BpmInstance.fromApiObject(d)), total: data.total },
      ...rest,
    };
  };
}
