/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './resolve-todo.type';

const endpoint = '/workflow/task/approval';

/**
 * @see [任务审批](http://172.16.0.17:13000/project/51/interface/api/6712)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { fileInfoList, ...rest } = params;
    return await request.tryPost<SvcRespData, ApiQ>(endpoint, {
      ...rest,
      fileInfoList: fileInfoList?.map(file => McUploadFile.fromJSON(file).toApiObject()),
    });
  };
}
