/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Resolve = {
  instId: string;
  taskId: string;
  result: number;
  comment?: string;
  isCountersignTask?: boolean;
  commentAssigner?: string;
  fileInfoList?: McUploadFileJSON[];
};
export type SvcQuery = Resolve;

export type SvcRespData = boolean;
export type ApiQ = { fileInfoList?: BackendMcUploadFile[] } & Omit<Resolve, 'fileInfoList'>;

export type ApiR = WriteResponse;
