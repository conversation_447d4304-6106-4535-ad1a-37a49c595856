/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import type { BackendBpmInstance, BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = {
  data: BpmInstance[];
  total: number;
};

export type RequestRespData = {
  data: BackendBpmInstance[] | null;
  total: number;
} | null;
// Apiq 与SvcQuery相同
export type ApiQ = {
  instId?: string;
  /** 审批状态数组 */
  instStatusList?: string[];
  title?: string;
  /** 审批类型 */
  bizType?: string;
  /** 对应单号 */
  bizId?: string;
  blockGuid?: string;
  idcTag?: string;
  roomTag?: string;
  applicantId?: string;
  creatorId?: string;
  createTimeStart?: string;
  createTimeEnd?: string;
  finishTimeStart?: string;
  finishTimeEnd?: string;
  pageNum: number;
  pageSize: number;
};
export type ApiR = ListResponse<BackendBpmInstance[] | null>;
