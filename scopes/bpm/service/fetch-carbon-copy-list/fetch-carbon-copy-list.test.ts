/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCarbonCopyList as webService } from './fetch-carbon-copy-list.browser';
import { fetchCarbonCopyList as nodeService } from './fetch-carbon-copy-list.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ instId: 'SUCCESS_ID' });

  expect(error).toBe(undefined);
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ instId: 'SUCCESS_ID' });

  expect(error).toBe(undefined);
});
