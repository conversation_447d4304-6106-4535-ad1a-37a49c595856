/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-carbon-copy-list.type';

const endpoint = '/workflow/instance/cc/for/me/list';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/51/interface/api/18834)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (queryParams: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      queryParams
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => BpmInstance.fromApiObject(d)), total: data.total },
      ...rest,
    };
  };
}
