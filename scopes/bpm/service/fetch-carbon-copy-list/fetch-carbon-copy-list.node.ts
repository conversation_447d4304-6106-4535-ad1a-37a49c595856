/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-carbon-copy-list';
import type { ApiQ, SvcRespData } from './fetch-carbon-copy-list.type';

const executor = getExecutor(nodeRequest);

/**
 * @param queryParams
 * @returns
 */
export function fetchCarbonCopyList(
  queryParams: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(queryParams);
}
