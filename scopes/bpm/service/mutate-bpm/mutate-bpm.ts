/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  CreateApiQ,
  CreateServiceQ,
  RequestRespData,
  SvcRespData,
  UpdateApiQ,
  UpdateServiceQ,
} from './mutate-bpm.type';

const updateEndpoint = '/workflow/process/info/edit';
const createEndpoint = '/workflow/process/info/create';

/**
 * @see [Doc](create YAPI http://172.16.0.17:13000/project/51/interface/api/5680)
 *@see [Doc](update YAPI http://172.16.0.17:13000/project/51/interface/api/5688)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async <T2 extends UpdateServiceQ | CreateServiceQ>(
    bpm: T2
  ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    if ('code' in bpm && bpm.code) {
      const apiQ: UpdateApiQ = {
        processXml: bpm.xml,
        processName: bpm.name,
        processCode: bpm.code,
        autoApprovalType: bpm.autoApprovalType as any,
      };
      // 返回string类型的data
      const { error, data, ...rest } = await request.tryPost<RequestRespData, UpdateApiQ>(
        updateEndpoint,
        apiQ
      );

      if (error || data === null) {
        return {
          ...rest,
          error,
          data: null,
        };
      }

      return {
        error,
        data: data,
        ...rest,
      };
    }

    const apiQ: CreateApiQ = {
      processXml: bpm.xml,
      processName: bpm.name,
    };
    // 返回string类型的data，即新建后的流程对应的编码
    const { error, data, ...rest } = await request.tryPost<RequestRespData, CreateApiQ>(
      createEndpoint,
      apiQ
    );
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: data,
      ...rest,
    };
  };
}
