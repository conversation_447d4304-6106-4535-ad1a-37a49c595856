/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateBpm as webService } from './mutate-bpm.browser';
import { mutateBpm as nodeService } from './mutate-bpm.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve create success response', async () => {
  const { error, data } = await webService({
    name: 'test process',
    xml: '',
  });

  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('[web] should resolve update success response', async () => {
  const { error, data } = await webService({
    name: 'test process',
    xml: '',
    code: '11111',
  });
  expect(error).toBe(undefined);
});
