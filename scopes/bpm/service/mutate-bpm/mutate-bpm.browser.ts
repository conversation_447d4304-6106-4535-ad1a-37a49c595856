/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './mutate-bpm';
import type { CreateServiceQ, SvcRespData, UpdateServiceQ } from './mutate-bpm.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function mutateBpm<T extends UpdateServiceQ | CreateServiceQ>(
  variant: T
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
