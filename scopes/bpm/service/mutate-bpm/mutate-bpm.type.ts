/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = string | null;
export type UpdateServiceQ = {
  // 流程定义编码
  code?: string;
  // 流程名称 最大20字
  name: string;
  // 流程xml
  xml: string;
  //自动审批类型
  autoApprovalType?: number;
};
export type RequestRespData = string | null;

export type UpdateApiQ = {
  processXml: string;
  processName: string;
  processCode: string;
  autoApprovalType: number;
};

export type CreateServiceQ = Omit<UpdateServiceQ, 'code'>;
export type CreateApiQ = Omit<UpdateApiQ, 'processCode'>;
export type ApiR = Response<RequestRespData>;
