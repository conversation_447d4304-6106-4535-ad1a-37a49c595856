/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchScenes as webService } from './fetch-scenes.browser';
import { fetchScenes as nodeService } from './fetch-scenes.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ subBizScenes: 'SUCCESS-BIZ-SCENES' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ subBizScenes: 'FAILURE-BIZ-SCENES' });

  expect(data.total).toBe(0);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ subBizScenes: 'SUCCESS-BIZ-SCENES' });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ subBizScenes: 'FAILURE-BIZ-SCENES' });

  expect(data.total).toBe(0);
});
