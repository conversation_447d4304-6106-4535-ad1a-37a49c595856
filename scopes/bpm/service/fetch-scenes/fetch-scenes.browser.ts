/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-scenes';
import type { ApiQ, SvcRespData } from './fetch-scenes.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchScenes(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
