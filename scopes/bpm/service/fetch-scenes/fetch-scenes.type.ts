/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BizScene = {
  bizScenes: string;
  bizSubScenesVoList: BizSubScene[];
};
export type BizSubScene = {
  bizScenes: string;
  subBizScenes: string;
  usable: boolean;
  remark: string;
  gmtModified: number;
  relateProcessCount: number;
  processCode: string;
  processName: string | null;
};

export type SvcRespData = {
  data: BizScene[];
  total: number;
};

export type RequestRespData = {
  data: BizScene[] | null;
  total: number;
} | null;

export type ApiQ = {
  subBizScenes?: string;
};

export type ApiR = ListResponse<BizScene[] | null>;
