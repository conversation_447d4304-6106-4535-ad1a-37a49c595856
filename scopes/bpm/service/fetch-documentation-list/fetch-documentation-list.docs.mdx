---
description: 'A fetchDocumentationList HTTP API service.'
labels: ['service', 'http']
---

查询业务流场景下节点

## Usage

### Browser

```ts
import { fetchDocumentationList } from '@manyun/bpm.service.fetch-documentation-list';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchDocumentationListService } from '@manyun/bpm.service.fetch-documentation-list/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchDocumentationListService.from(nodeRequest);
```
