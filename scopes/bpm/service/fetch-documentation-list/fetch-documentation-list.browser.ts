/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-11-14
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-documentation-list.js';
import type { ApiArgs, ApiResponse } from './fetch-documentation-list.type.js';

/**
 * @param args
 * @returns
 */
export function fetchDocumentationList(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
