/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-11-14
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 业务场景
   */
  bizScenes: string;
  /**
   * 业务子场景
   */
  subBizScenes: string;
};

export type UnknownType = {
  /**
   * 节点code
   */
  node: string;
  /**
   * 节点名称
   */
  nodeName: string;
};

export type ApiResponse = ListBackendResponse<UnknownType[]>;
