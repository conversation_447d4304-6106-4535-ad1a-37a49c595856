/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-11-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-documentation-list.type.js';

const endpoint = '/workflow/scenes/process/node/query';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/51/interface/api/29471)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data'>>> => {
    const { error, data, ...rest } = await request.tryPost<
      Pick<ApiResponse, 'data'> | null,
      ApiArgs
    >(endpoint, args);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
        },
      };
    }

    return { error, data: { data: data.data }, ...rest };
  };
}
