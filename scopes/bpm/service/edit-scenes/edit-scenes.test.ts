/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { editScenes as webService } from './edit-scenes.browser';
import { editScenes as nodeService } from './edit-scenes.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    usable: true,
    remark: 'SUCCESS-REMARK',
    processCode: 'SUCCESS-CODE',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({
    usable: true,
    remark: 'FAILURE-REMARK',
    processCode: 'FAILURE-CODE',
  });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    usable: true,
    remark: 'SUCCESS-REMARK',
    processCode: 'SUCCESS-CODE',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({
    usable: true,
    remark: 'FAILURE-REMARK',
    processCode: 'FAILURE-CODE',
  });

  expect(data).toBe(null);
});
