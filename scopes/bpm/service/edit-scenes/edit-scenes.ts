/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './edit-scenes.type';

const endpoint = '/workflow/scenes/edit';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/51/interface/api/18120)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: data,
      ...rest,
    };
  };
}
