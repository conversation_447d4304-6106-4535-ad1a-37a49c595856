/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './edit-scenes';
import type { SvcQuery, SvcRespData } from './edit-scenes.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function editScenes(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
