/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-2
 *
 * @packageDocumentation
 */
import { UserGroup } from '@manyun/auth-hub.model.user-group';
import { useRemoteMock } from '@manyun/service.request';

import { fetchBpmInstance as webService } from './fetch-bpm-instance.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ code: '116484675265888256' });
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('formJson');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ code: '1' });
  expect(error).toHaveProperty('code');
  expect(data).toBeNull();
});

test('should resolve success response and formJson is not null', async () => {
  const { error, data } = await webService<UserGroup[]>({ code: '123074345344958464' });
  expect(error).toBeUndefined();
  expect(data).toHaveProperty('formJson');
  expect(data).toHaveProperty('bizType');
  expect(data?.formJson).toHaveLength(1);
});
