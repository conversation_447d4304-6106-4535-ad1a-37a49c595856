/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-2
 *
 * @packageDocumentation
 */
import type { BackendBpmInstance, BpmInstance } from '@manyun/bpm.model.bpm-instance';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  code: string;
  relateCode?: string;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type SvcRespData<T = any> = BpmInstance<T> | null;

export type ApiQ = {
  processInstanceCode: string;
  relateProcessInstanceCode?: string;
};

export type RequestRespData = BackendBpmInstance | null;

export type ApiR = ListResponse<RequestRespData>;
