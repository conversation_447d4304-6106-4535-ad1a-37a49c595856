/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-2
 *
 * @packageDocumentation
 */
import { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-bpm-instance';
import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-bpm-instance.type';

/**
 * 获取审批单详情
 *
 * @see [Doc](http://***********:13000/project/51/interface/api/2488)
 *
 * @param variant
 * @returns
 */
// eslint-disable-next-line @typescript-eslint/no-explicit-any
export async function fetchBpmInstance<T = any>({
  code,
  relateCode,
}: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData<T>>> {
  const apiQ: ApiQ = { processInstanceCode: code, relateProcessInstanceCode: relateCode };

  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

  if (error || data == null) {
    return {
      ...rest,
      error,
      data: null,
    };
  }

  return { error, data: BpmInstance.fromApiObject<T>(data), ...rest };
}
