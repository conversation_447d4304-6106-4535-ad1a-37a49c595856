/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-my-unresolved-todos';
import type { SvcQuery, SvcRespData } from './fetch-my-unresolved-todos.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchMyUnresolvedTodos(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
