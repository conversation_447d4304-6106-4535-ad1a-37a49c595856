/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchMyUnresolvedTodos as webService } from './fetch-my-unresolved-todos.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ title: 'SUCCESS_ID', pageNum: 1, pageSize: 10 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
