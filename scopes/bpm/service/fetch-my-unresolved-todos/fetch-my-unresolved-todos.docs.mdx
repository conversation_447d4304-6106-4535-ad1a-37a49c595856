---
description: 'A fetchMyUnresolvedTodos HTTP API service.'
labels: ['service', 'http', fetch-my-unresolved-todos]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchMyUnresolvedTodos } from '@manyun/bpm.service.fetch-my-unresolved-todos';

const { error, data } = await fetchMyUnresolvedTodos('success');
const { error, data } = await fetchMyUnresolvedTodos('error');
```

### Node

```ts
import { fetchMyUnresolvedTodos } from '@manyun/bpm.service.fetch-my-unresolved-todos/dist/index.node';

const { data } = await fetchMyUnresolvedTodos('success');

try {
  const { data } = await fetchMyUnresolvedTodos('error');
} catch (error) {
  // ...
}
```
