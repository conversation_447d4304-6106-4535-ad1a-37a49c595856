/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './withdraw-todo';
import type { SvcQuery, SvcRespData } from './withdraw-todo.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function withdrawTodo(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
