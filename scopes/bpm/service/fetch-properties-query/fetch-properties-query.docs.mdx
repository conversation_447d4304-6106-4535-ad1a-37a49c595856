---
description: 'A fetchPropertiesQuery HTTP API service.'
labels: ['service', 'http']
---

查询场景流程下属性

## Usage

### Browser

```ts
import { fetchPropertiesQuery } from '@manyun/bpm.service.fetch-properties-query';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchPropertiesQueryService } from '@manyun/bpm.service.fetch-properties-query/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchPropertiesQueryService.from(nodeRequest);
```
