/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-11-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-properties-query.type.js';

const endpoint = '/workflow/scenes/process/properties/query';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/51/interface/api/29466)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<Record<string, any>> => {
    const { error, data, ...rest } = await request.tryPost(endpoint, args);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data, ...rest };
  };
}
