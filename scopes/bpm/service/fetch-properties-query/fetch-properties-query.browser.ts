/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-11-11
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-properties-query.js';
import type { ApiArgs, ApiResponse } from './fetch-properties-query.type.js';

/**
 * @param args
 * @returns
 */
export function fetchPropertiesQuery(args: ApiArgs): Promise<Record<string, any>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
