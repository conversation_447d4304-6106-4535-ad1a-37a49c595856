/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-11-11
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 业务场景
   */
  bizScenes: string;
  /**
   * 业务子场景
   */
  subBizScenes: string;
};

export type ResponseParamsType = {
  /**
   * id
   */
  id: number;
  /**
   * 属性code
   */
  propertiesCode: string;
  /**
   * 属性名称
   */
  propertiesName: string;
  /**
   * 单位
   */
  propertiesUnit?: string | null;
  /**
   * 流程子场景
   */
  subBizScenes: string;
  /**
   * 流程场景
   */
  bizScenes: string;
  /**
   * 属性类型:NUMBER:数值 、CHARACTER:文本、ARRAY:数组
   */
  propertiesType: string;
  /**
   * 属性值类型:NUMBER : 数字、CHARACTER:文本、PROPERTIES:属性
   */
  valueType: string;
  /**
   * 描述
   */
  des?: string | null;
  /**
   * 输入方式:INPUT/OPT/COMPONENT
   */
  inputWay: string;
  /**
   * 选项
   */
  options: any;
};

export type ApiResponse = ResponseParamsType[];
