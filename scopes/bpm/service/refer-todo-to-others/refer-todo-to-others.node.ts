/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './refer-todo-to-others';
import type { SvcQuery, SvcRespData } from './refer-todo-to-others.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function referTodoToOthers(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
