/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery, SvcRespData } from './refer-todo-to-others.type';

const endpoint = '/workflow/task/redirect';

/**
 * @see [任务转交]](http://172.16.0.17:13000/project/51/interface/api/6720)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<SvcRespData, SvcQuery>(endpoint, params);
  };
}
