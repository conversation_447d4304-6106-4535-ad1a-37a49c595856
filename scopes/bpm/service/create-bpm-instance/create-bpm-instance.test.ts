/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-2
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import type { CreateSvcD } from './index.js';
import { ProcessType, createBpmInstance as webService } from './index.js';

let mockOff: () => void;

beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const params: CreateSvcD = {
    type: ProcessType.AUTH_APPLY,
    title: '流程创建',
    applyId: 31,
    applyName: 'Jerry',
    formJSON: {
      applyId: 1,
      site: 'DcBase',
      siteName: 'MY DC BASE',
      roleCode: '1',
      roleName: '测试',
      expireTime: '',
      resourceCodes: [],
      resourceNames: [],
    },
    startProcessParams: {
      roleApproveId: '1',
      applyRoleCode: 'ADMIN',
    },
  };

  const { error } = await webService(params);

  expect(error).toBe(undefined);
});

test('should resolve error response', async () => {
  const params: CreateSvcD = {
    type: ProcessType.AUTH_APPLY,
    title: '',
    applyId: 31,
    applyName: 'Jerry',
    formJSON: {
      applyId: 1,
      site: 'DcBase',
      siteName: 'MY DC BASE',
      roleCode: '1',
      roleName: '测试',
      expireTime: '',
      resourceCodes: [],
      resourceNames: [],
    },
    startProcessParams: {
      roleApproveId: '1',
      applyRoleCode: 'ADMIN',
    },
  };

  const { error, data } = await webService(params);
  if (error) {
    expect(error).toHaveProperty('code');
    expect(error).toHaveProperty('message');
  } else {
    console.error('error is undefined or null');
  }

  if (data) {
    expect(data).toMatchObject({});
  } else {
    console.error('data is undefined or null');
  }
});
