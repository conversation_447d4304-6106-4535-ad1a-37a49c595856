/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-2
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './create-bpm-instance.js';
import { ProcessType } from './create-bpm-instance.type.js';
import type {
  BaseCreateApiD,
  CreateApiD,
  CreateSvcD,
  ServiceRD,
} from './create-bpm-instance.type.js';

/**
 *
 *  权限申请流程创建
 * @see [Doc](http://yapi.manyun-local.com/project/51/interface/api/467)
 *
 * @param variant
 * @returns
 */
export async function createBpmInstance(
  createSvcD: CreateSvcD
): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const baseApiD: BaseCreateApiD = {
    title: createSvcD.title,
    fileInfoList: createSvcD.attachments?.map(obj => McUploadFile.toApiObject(obj)),
    idcTag: createSvcD.idcTag,
    blockGuid: createSvcD.blockGuid,
    reason: createSvcD.reason,
  };
  let apiD: CreateApiD | null = null;

  switch (createSvcD.type) {
    case ProcessType.AUTH_APPLY:
      apiD = {
        ...baseApiD,
        processType: ProcessType.AUTH_APPLY,
        detailParam: {
          apply: createSvcD.applyName,
          siteName: createSvcD.formJSON.siteName,
          roleName: createSvcD.formJSON.roleName,
          // createSvcD.formJSON.expireTime 为 YYYY-MM-DD 格式的
          expireTime: `${createSvcD.formJSON.expireTime} 00:00:00`,
          resourceNames:
            createSvcD.formJSON.resourceNames.length > 0
              ? createSvcD.formJSON.resourceNames.join(',')
              : '--',
          addteam: createSvcD.addteam,
        },
        formJson: JSON.stringify([createSvcD.formJSON]),
        startProcessParams: createSvcD.startProcessParams,
        bizBlockGuidList: createSvcD.bizBlockGuidList,
      };
      break;
    case ProcessType.CONSTRUCTION_APPLY:
      const { date, time } = createSvcD.detailParam;
      apiD = {
        ...baseApiD,
        processType: ProcessType.CONSTRUCTION_APPLY,
        detailParam: {
          apply: createSvcD.applyName,
          ...createSvcD.detailParam,
          date: date.join('~'),
          time: time.join('-'),
        },
        formJson: JSON.stringify(createSvcD.formJSON),
      };
      break;
    case ProcessType.CHANGE_OFFLINE:
      apiD = {
        ...baseApiD,
        processType: ProcessType.CHANGE_OFFLINE,
        detailParam: createSvcD.detailParam,
      };
      break;
    case ProcessType.COMMON_APPROVAL:
      apiD = {
        ...baseApiD,
        processType: ProcessType.COMMON_APPROVAL,
        detailParam: {
          applyType: createSvcD.applyType.name,
        },
        startProcessParams: {
          applyType: createSvcD.applyType.code,
        },
      };
      break;
    case ProcessType.OUT_TO_REGULAR:
      apiD = {
        ...baseApiD,
        processType: ProcessType.OUT_TO_REGULAR,
        formJson: JSON.stringify(createSvcD.formJSON),
        detailParam: {
          outUserName: createSvcD.detailParam.outUserName,
          domainAccount: createSvcD.detailParam.domainAccount,
          transferTime: createSvcD.detailParam.transferTime,
        },
      };
      break;
    default:
      //nothing
      break;
  }

  const { error, data, ...rest } = await webRequest.tryPost<ServiceRD, CreateApiD>(endpoint, apiD!);

  return { error, data, ...rest };
}
