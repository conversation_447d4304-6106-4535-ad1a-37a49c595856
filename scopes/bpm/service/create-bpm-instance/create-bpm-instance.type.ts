/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-2
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common.js';

/** 流程类型 **/
export enum ProcessType {
  AUTH_APPLY = 'AUTH_APPLY',
  CONSTRUCTION_APPLY = 'CONSTRUCTION_APPLY',
  CHANGE_OFFLINE = 'CHANGE_OFFLINE',
  /** 日常通用审批 */
  COMMON_APPROVAL = 'COMMON_APPROVAL',
  OUT_TO_REGULAR = 'OUT_TO_REGULAR',
}

export type AuthApplyDetailParam = {
  apply: string; //申请人
  siteName?: string;
  roleName: string;
  expireTime: string;
  resourceNames: string;
  addteam: 'yes' | 'no';
};

export type CommonApprovalDetailParam = {
  /** 申请类型 传 metaName */
  applyType: string;
};

export type OutToRegularDetailParam = {
  outUserName: string;
  domainAccount: string;
  transferTime: string;
};

export type ConstructionRequestDetailParam = {
  apply: string; //申请人
  project: string; // 施工项目
  company: string; // 施工单位
  peopleNum: number; // 施工人数
  tools: string; // 施工工具
  charge: string; //施工负责人
  phone: string; //联系方式
  date: string; //施工日期
  time: string; // 施工时间
  area: string; // 施工位置
};

/** 流程创建表单 Api base type **/
export type BaseCreateApiD = {
  title: string; //标题
  fileInfoList?: BackendMcUploadFile[]; //附件信息
  idcTag?: string; //机房
  blockGuid?: string; //楼
  reason?: string; //原因
};

/**流程创建表单 Api**/
export type CreateApiD = BaseCreateApiD &
  (
    | {
        processType: ProcessType.AUTH_APPLY; //流程类型
        detailParam: AuthApplyDetailParam;
        formJson: string; // json string
        startProcessParams: {
          roleApproveId?: string;
        };
        bizBlockGuidList?: string[];
      }
    | {
        processType: ProcessType.CONSTRUCTION_APPLY; //流程类型
        detailParam: ConstructionRequestDetailParam;
        formJson: string; // json string
      }
    | {
        processType: ProcessType.CHANGE_OFFLINE; //流程类型
        detailParam: ChangeOfflineContent;
      }
    | {
        processType: ProcessType.COMMON_APPROVAL; //流程类型
        detailParam: CommonApprovalDetailParam;
        startProcessParams: {
          /** 申请类型 传metaCode */
          applyType: string;
        };
      }
    | {
        processType: ProcessType.OUT_TO_REGULAR;
        detailParam: OutToRegularDetailParam;
        formJson: string;
      }
  );

/**流程创建 base type**/
export type BaseCreateSvcD = {
  type: ProcessType;
  title: string /**流程标题 */;
  applyId: number; //申请人ID
  applyName: string; //申请人Name
  idcTag?: string;
  blockGuid?: string;
  reason?: string;
  attachments?: McUploadFile[];
};

export type CreateAuthApplySvcD = BaseCreateSvcD & {
  type: ProcessType.AUTH_APPLY;
  formJSON: {
    applyId: number;
    roleCode: string;
    roleName: string;
    expireTime: string;
    siteName?: string;
    site?: string;
    resourceType?: string;
    resourceCodes: string[];
    resourceNames: string[];
  };
  startProcessParams: {
    roleApproveId?: string;
    /**申请的角色CODE */
    applyRoleCode: string;
  };
  /**申请多楼栋资源时使用 */
  bizBlockGuidList?: string[];
  /**后端区分特殊角色逻辑 默认为 no*/
  addteam: 'yes' | 'no';
};

type TicketContent = {
  project: string;
  company: string;
  peopleNum: number;
  tools: string;
  charge: string;
  phone: string;
  date: string[];
  time: string[];
  area: string;
  describe: string;
};

export type ChangeOfflineContent = {
  // urgency: string;
  riskLevel: string;
  changeType: string;
  exeUserGroupName: string;
  planStartTime: string;
  planEndTime: string;
};

export type ConstructionFormJSON = {
  hazardousOptTip?: string;
  constructionUsers: {
    name: string;
    idNo: string;
    work: string[];
  }[];
  ticketContent: TicketContent;
};

export type CreateConstructionSvcD = BaseCreateSvcD & {
  type: ProcessType.CONSTRUCTION_APPLY;
  formJSON: ConstructionFormJSON;
  detailParam: TicketContent;
};

export type CreateChangeOfflineSvcD = BaseCreateSvcD & {
  type: ProcessType.CHANGE_OFFLINE;
  detailParam: ChangeOfflineContent;
  formJSON: null;
};

export type CreateCommonApprovalSvcD = BaseCreateSvcD & {
  type: ProcessType.COMMON_APPROVAL;
  idcTag: string;
  blockGuid: string;
  reason: string;
  applyType: {
    name: string;
    code: string;
  };
};

export type CreateOutToRegularSvcD = Omit<BaseCreateSvcD, 'applyId' | 'applyName'> & {
  type: ProcessType.OUT_TO_REGULAR;
  idcTag: string;
  blockGuid: string;
  reason: string;
  detailParam: OutToRegularDetailParam;
  formJSON: {
    domainAccount: string;
    transferTime: string;
    outUserId: number;
  };
};

export type CreateSvcD =
  | CreateAuthApplySvcD
  | CreateConstructionSvcD
  | CreateChangeOfflineSvcD
  | CreateCommonApprovalSvcD
  | CreateOutToRegularSvcD;

export type CreateApiR = Response<string>;

export type ServiceRD = string;
