import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectMe } from '@manyun/auth-hub.state.user';
import { REQUEST_RNTRY_ROUTE_PATH, generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ProcessType, createBpmInstance } from '@manyun/bpm.service.create-bpm-instance';
import { CustomApprovalTypesSelect } from '@manyun/bpm.ui.custom-approval-types-select';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import styles from './common-request-creator.module.less';

type FormValues = {
  type: {
    value: string;
    label: string;
  };
  blockGuid: string[];
  reason: string;
  fileList?: MixedUploadFile[];
};

export function CommonRequestCreator() {
  const [form] = Form.useForm<FormValues>();

  const history = useHistory();

  const { userId, name } = useSelector(selectMe);

  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const submit = async () => {
    const values = await form.validateFields();
    setSubmitLoading(true);
    const { error, data } = await createBpmInstance({
      type: ProcessType.COMMON_APPROVAL,
      title: `${name}发起${values.type.label}`,
      applyId: userId!,
      applyName: name!,
      applyType: {
        name: values.type.label,
        code: values.type.value,
      },
      idcTag: values.blockGuid[0],
      blockGuid: values.blockGuid[1],
      reason: values.reason,
      attachments: values.fileList,
      formJSON: null,
    });
    setSubmitLoading(false);
    if (error) {
      message.error(error.message);
    } else {
      message.success('创建申请成功！');
      history.push(generateBPMRoutePath({ id: data }));
    }
  };
  return (
    <Space direction="vertical" className={styles.wrapper}>
      <Typography.Title level={5}>基本信息</Typography.Title>
      <Form form={form} className={styles.form} labelCol={{ span: 4 }} wrapperCol={{ span: 12 }}>
        <Form.Item
          label="申请类型"
          name="type"
          required
          rules={[
            {
              required: true,
              message: '请选择',
            },
          ]}
        >
          <CustomApprovalTypesSelect labelInValue />
        </Form.Item>
        <Form.Item
          label="机房楼栋"
          name="blockGuid"
          required
          rules={[
            {
              required: true,
              message: '请选择',
            },
            {
              type: 'array',
              len: 2,
              message: '必须选择到楼栋',
            },
          ]}
        >
          <LocationCascader allowClear authorizedOnly />
        </Form.Item>
        <Form.Item
          label="说明"
          name="reason"
          required
          wrapperCol={{ span: 20 }}
          rules={[
            {
              required: true,
              message: '请输入',
            },
          ]}
        >
          <Input.TextArea maxLength={120} showCount />
        </Form.Item>
        <Form.Item
          label="附件"
          name="fileList"
          wrapperCol={{ span: 20 }}
          getValueFromEvent={({ fileList }: { fileList: MixedUploadFile[] }) => {
            if (fileList.filter(file => file.status === 'uploading').length) {
              setSubmitLoading(true);
            } else {
              setSubmitLoading(false);
            }
            return fileList;
          }}
        >
          <McUpload
            showAccept
            accept=".png,.jpg,.pdf,.doc,.docx,.xls,.xlsx"
            showUploadList
            allowDelete
            maxCount={5}
          >
            <Button icon={<UploadOutlined />}>上传</Button>
          </McUpload>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
          <Space size="middle">
            <Button type="primary" loading={submitLoading} onClick={() => submit()}>
              提交
            </Button>
            <Button onClick={() => history.push(REQUEST_RNTRY_ROUTE_PATH)}>取消</Button>
          </Space>
        </Form.Item>
      </Form>
    </Space>
  );
}
