import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ProcessType, createBpmInstance } from '@manyun/bpm.service.create-bpm-instance';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { fetchPagedDutyGroup } from '@manyun/hrm.service.fetch-paged-duty-group';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

export function OutToRegularRequest() {
  const [loading, setLoading] = useState(false);

  const [submitLoading, setSubmitLoading] = useState(false);
  const [userName, setUserName] = useState<string>();
  const history = useHistory();

  const [form] = Form.useForm();
  const attachments: McUploadFile[] | undefined = Form.useWatch('attachments', form);

  const submit = async () => {
    const values = await form.validateFields();

    const { outUser, blockGuid, domainAccount, transferTime, reason, attachments } = values;

    setLoading(true);
    const { data, error } = await createBpmInstance({
      type: ProcessType.OUT_TO_REGULAR,
      title: `${outUser.name}的外包转正申请`,
      idcTag: blockGuid[0],
      blockGuid: blockGuid[1],
      reason,
      attachments,
      detailParam: {
        outUserName: outUser.name,
        domainAccount,
        transferTime: dayjs(transferTime).format('YYYY-MM-DD'),
      },
      formJSON: {
        domainAccount,
        transferTime: dayjs(transferTime).format('YYYY-MM-DD'),
        outUserId: outUser.id,
      },
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }

    message.success('创建申请成功！');
    history.push(generateBPMRoutePath({ id: data }));
  };

  const onUserSelectChange = async (user?: { login: string; id: number }) => {
    if (!user) {
      form.setFieldsValue({ blockGuid: undefined });
      setUserName(undefined);
      return;
    }
    const { data, error } = await fetchPagedDutyGroup({
      pageNum: 1,
      pageSize: 1,
      staffId: user.id,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    setUserName(user.login);
    if (data.data.length) {
      const { idcTag, blockTag } = data.data[0];
      form.setFieldsValue({ blockGuid: [idcTag, blockTag] });
    } else {
      form.setFieldsValue({ blockGuid: undefined });
    }
  };

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
        <Form form={form} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
          <Form.Item
            label="转正人员"
            name="outUser"
            required
            rules={[
              {
                required: true,
                message: '请输入转正人员',
              },
            ]}
          >
            <UserSelect
              style={{ width: 260 }}
              userType="OUTSOURCE_STAFF"
              userState="in-service"
              allowClear
              onClear={() => form.setFieldsValue({ blockGuid: undefined })}
              onChange={onUserSelectChange}
            />
          </Form.Item>
          <Form.Item
            label="位置"
            name="blockGuid"
            rules={[
              {
                required: true,
                message: '请选择员工的机房楼栋',
              },
              {
                type: 'array',
                len: 2,
                message: '必须选择到楼栋',
              },
            ]}
          >
            <LocationCascader
              style={{ width: 260 }}
              authorizedOnly
              disabled={!userName}
              allowClear
              userName={userName}
            />
          </Form.Item>
          <Form.Item
            label="员工域账号"
            name="domainAccount"
            tooltip={{
              title: '域账号即员工集团邮箱的前缀，例如：*****************的域账号为：szhang',
              icon: <ExclamationCircleOutlined />,
            }}
            rules={[
              {
                required: true,
                message: '请输入员工域账号',
              },
              {
                pattern: /^[a-zA-Z0-9_-]+$/,
                message: '员工域账号必须是英文、数字、“-”或“_”',
              },
              {
                max: 15,
                type: 'string',
                message: '上限15个字符',
              },
            ]}
          >
            <Input style={{ width: 260 }} allowClear />
          </Form.Item>
          <Form.Item
            label="转正日期"
            name="transferTime"
            rules={[
              {
                required: true,
                message: '请输入员工的转正日期',
              },
            ]}
          >
            <DatePicker style={{ width: 260 }} allowClear={false} format="YYYY-MM-DD" />
          </Form.Item>
          <Form.Item
            label="原因"
            name="reason"
            required
            rules={[
              {
                required: true,
                message: '请输入员工的转正原因',
              },
              {
                max: 200,
                type: 'string',
                message: '上限200个字符',
              },
            ]}
          >
            <Input.TextArea style={{ width: 395 }} />
          </Form.Item>
          <Form.Item
            label="附件"
            name="attachments"
            getValueFromEvent={({ fileList }: { fileList: MixedUploadFile[] }) => {
              if (fileList.filter(file => file.status === 'uploading').length) {
                setSubmitLoading(true);
              } else {
                setSubmitLoading(false);
              }
              return fileList;
            }}
          >
            <McUpload showAccept accept=".png,.jpg,.jpeg" showUploadList allowDelete maxCount={5}>
              <Button
                loading={submitLoading}
                disabled={(attachments ?? []).length >= 5}
                icon={<UploadOutlined />}
              >
                上传
              </Button>
            </McUpload>
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 2, span: 20 }}>
            <Space size="middle">
              <Button type="primary" loading={loading} onClick={submit}>
                提交
              </Button>
              <Button onClick={() => history.goBack()}>取消</Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>
    </Card>
  );
}
