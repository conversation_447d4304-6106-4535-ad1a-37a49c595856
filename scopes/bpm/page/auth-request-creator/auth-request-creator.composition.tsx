import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroyMock, useRemoteMock, webRequest } from '@manyun/service.request';

import { AuthRequestCreator } from './auth-request-creator';

export const BasicAuthRequestCreator = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    // const mockOff = useRemoteMock('web', { adapter: 'xhr' });
    update(true);
    return () => {
      // mockOff();
    };
  }, []);
  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <AuthRequestCreator />
      </FakeStore>
    </ConfigProvider>
  );
};
