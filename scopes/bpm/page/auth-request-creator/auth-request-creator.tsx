import { UploadOutlined } from '@ant-design/icons';
import debounce from 'lodash.debounce';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import type { AuthResourceNodeType, Resource } from '@manyun/auth-hub.gql.client.resources';
import { useGetRoles } from '@manyun/auth-hub.gql.client.roles';
import type { Role } from '@manyun/auth-hub.gql.client.roles';
import { selectMe } from '@manyun/auth-hub.state.user';
import { ResourceCascader } from '@manyun/auth-hub.ui.gql.resource-cascader';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ProcessType, createBpmInstance } from '@manyun/bpm.service.create-bpm-instance';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { WebsiteCode } from '@manyun/dc-brain.model.website';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { getAuthResourceLocales } from '@manyun/iam.model.auth-resource';
import type { BackendResourceType } from '@manyun/iam.model.auth-resource';
import { RolesSelect } from '@manyun/iam.ui.roles-select';

import styles from './auth-request-creator.module.less';

type LabeledValue<Value> = {
  label: string;
  value: Value;
};

type FormValue = {
  applicant: LabeledValue<number>;
  title: string;
  website: LabeledValue<WebsiteCode>;
  role: LabeledValue<string> & {
    approveId: number | null;
    resourceType: BackendResourceType | null;
  };
  expiredAt: Moment;
  resources: string[];
  description: string;
  attachments?: (McUploadFile & { uid: string })[];
};

export function AuthRequestCreator() {
  const history = useHistory();
  const { search } = useLocation();
  const searchParams = getLocationSearchMap<{
    website?: WebsiteCode;
    roleCode?: string;
    roleName?: string;
    resourceType?: string;
  }>(search);
  // 处理特殊角色
  const [configUtil] = useConfigUtil();
  // @ts-ignore
  const legacyScopeCommonConfigs = configUtil.getScopeCommonConfigs('legacy');
  // @ts-ignore
  const specialRoleCodes: string[] = legacyScopeCommonConfigs?.specialRole?.codes || [];
  const [initialRole, setInitialRole] = useState<Role>();
  const { data, refetch } = useGetRoles({ website: WebsiteCode.DcBase, authorized: false });
  const roles = React.useMemo(() => data?.roles.data || [], [data?.roles.data]);

  const { userId, name } = useSelector(
    selectMe,
    (left, right) => left.userId === right.userId && left.name === right.name
  );

  const [form] = Form.useForm<FormValue>();
  const [loading, setLoading] = useState(false);

  const [resources, setResources] = React.useState<{ [key: string]: Resource[] | [] }>({});
  const [searchText, setSearchText] = React.useState<{ [key: string]: string }>({});
  const treeDataRef = React.useRef<Resource[] | []>([]);
  const onSubmit = useCallback(() => {
    form.validateFields().then(async values => {
      /** 从其他页面跳转，并且选中的角色与初始化角色相同 */
      const isRoleSameWithOtherPage = values.role.value === initialRole?.code;
      if (isRoleSameWithOtherPage && !values.role.approveId && !initialRole?.approveId) {
        message.error('当前选择的角色没有角色负责人，请重新选择！');
        return;
      }

      if (!isRoleSameWithOtherPage && !values.role.approveId) {
        message.error('当前选择的角色没有角色负责人，请重新选择！');
        return;
      }

      if (
        (!resources ||
          !Object.values(resources).some(value => Array.isArray(value) && value.length > 0)) &&
        role.resourceType !== null
      ) {
        message.error('请至少填写一种申请项');
        return;
      }
      //是否有机房楼栋资源
      const isResources = values.resources;
      const _resources = Object.keys(resources)
        .map(type => resources[type])
        .flat();
      setLoading(true);
      const _params = {
        type: ProcessType.AUTH_APPLY as ProcessType.AUTH_APPLY,
        title: values.title.trim(),
        reason: values.description.trim(),
        applyId: values.applicant.value,
        applyName: values.applicant.label,
        formJSON: {
          applyId: values.applicant.value,
          roleCode: values.role.value,
          roleName: values.role.label,
          expireTime: values.expiredAt.format('YYYY-MM-DD'),
          resourceType: Object.keys(resources).join(','),
          resourceCodes: _resources.map(resource => resource.value),
          resourceNames: _resources.map(resource => resource.displayText),
        },
        startProcessParams: {
          roleApproveId:
            values.role.approveId !== null && values.role.approveId !== undefined
              ? `${values.role.approveId}`
              : `${initialRole!.approveId}`,
          applyRoleCode: values.role.value,
        },
        attachments: values.attachments,
        bizBlockGuidList: isResources ? _resources.map(resource => resource.value) : undefined,
        addteam: (specialRoleCodes.includes(role.value) ? 'yes' : 'no') as 'yes' | 'no',
      };
      const { error, data: id } = await createBpmInstance(_params);
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('权限申请成功！');
      history.push(generateBPMRoutePath({ id }));
    });
  }, [form, history, resources, initialRole]);

  const role = Form.useWatch('role', form);

  useEffect(() => {
    if (searchParams.website && searchParams.roleCode) {
      refetch({ website: searchParams.website, authorized: false });
    }
  }, [searchParams.website, searchParams.roleCode, refetch]);
  useEffect(() => {
    if (roles && searchParams.roleCode) {
      setInitialRole(roles.find(item => item.code === searchParams.roleCode));
    }
  }, [roles, searchParams.roleCode]);
  useEffect(() => {
    // 角色发生改变时，还原过期时间、资源初始值
    if (role) {
      if (!searchParams.roleCode) {
        form.setFieldValue('expiredAt', moment().add(1, 'year'));
      }
      role.resourceType
        ?.replace('BUILDING', 'BLOCK')
        ?.split(',')
        ?.map(type => {
          form.setFieldValue(`resources_${type}`, undefined);
        });
      form.setFieldValue('resources_TEAM', undefined);
      setResources({});
    }
  }, [role, form, searchParams.roleCode]);

  const handleResourcesChange = React.useCallback(
    (_: (string | number)[], options: Resource[][], type: string) => {
      /** 获取是否应该更新数据 */
      function getShouldUpdate(resources: Resource[]) {
        const len = resources.length;
        if (len === 0) {
          return true;
        }
        const resource = resources[len - 1];
        // 仅当为部门层级或者叶子节点时，才应更新数据
        return (
          role?.resourceType === 'DEPT' ||
          resource.children === null ||
          resource.children.length === 0
        );
      }
      if (type === 'AREA' && options.length === 1 && options[0].length === 1) {
        setResources({
          ...resources,
          // @ts-ignore
          [type]: [{ ...options[0][0], children: null }],
        });
        return;
      }
      if (options.every(resources => getShouldUpdate(resources))) {
        const flatOptions = options.flat() || [];
        if (
          type === 'PROJECT_COMPANY' &&
          flatOptions.find(option => option.value === '_PROJECT_COMPANY_ALL_')
        ) {
          setResources({
            ...resources,
            [type]: treeDataRef.current,
          });

          return;
        }
        setResources({
          ...resources,
          [type]: options.reduce((acc, cur) => [...acc, cur[cur.length - 1]], []),
        });
      }
    },
    [role?.resourceType, resources]
  );

  return (
    <Space className={styles.wrapper} direction="vertical">
      <Typography.Title showBadge level={5}>
        基本信息
      </Typography.Title>
      <Form
        className={styles.form}
        form={form}
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 8 }}
      >
        <Form.Item
          name="applicant"
          label="申请人"
          initialValue={userId && name ? { label: name, value: userId } : undefined}
          rules={[
            {
              required: true,
              message: '申请人必选',
            },
          ]}
        >
          <UserSelect
            onChange={() => {
              // 申请人发生改变时，清除已选站点
              form.setFieldValue('website', undefined);
            }}
          />
        </Form.Item>
        <Form.Item
          name="title"
          label="申请标题"
          rules={[
            {
              required: true,
              whitespace: true,
              message: '申请标题必填',
            },
            {
              type: 'string',
              max: 20,
              message: '最多输入 20 个字符！',
            },
          ]}
        >
          <Input placeholder="输入申请标题" />
        </Form.Item>

        <Form.Item
          name="role"
          label="申请角色"
          initialValue={
            searchParams.roleCode && searchParams.roleName
              ? {
                  value: searchParams.roleCode,
                  label: searchParams.roleName,
                  resourceType: searchParams.resourceType,
                }
              : undefined
          }
          rules={[
            {
              required: true,
              message: '申请角色必选',
            },
          ]}
          getValueFromEvent={(value, _option) => {
            return {
              value,
              key: value,
              label: _option.name,
              approveId: _option.approveId,
              resourceType: _option.resourceType,
            };
          }}
        >
          <RolesSelect />
        </Form.Item>

        {form.getFieldValue('role')?.resourceType?.split(',')?.length >= 2 && (
          <Typography.Text
            style={{
              position: 'relative',
              top: '-22px',
              left: '98px',
              fontSize: '12px',
              whiteSpace: 'nowrap',
              color: 'rgba(0,0,0,0.45)',
            }}
          >
            您所申请的多种资源类型会取所有可能配对的集合（笛卡尔积），若不满足诉求请申请多个单子
          </Typography.Text>
        )}

        {role && (
          <>
            {role.resourceType &&
              role.resourceType
                .replace('BUILDING', 'BLOCK')
                .split(',')
                .map(type => {
                  const isProjecctCompany = type === 'PROJECT_COMPANY';
                  const isShowSearch = type === 'PROJECT_COMPANY' || type === 'CUSTOMER';
                  return (
                    <div key={type} className={isProjecctCompany ? styles.cascaderDropdown : ''}>
                      <Form.Item
                        name={`resources_${type}`}
                        // @ts-ignore
                        label={`申请${getAuthResourceLocales().resourceType[type] ?? ''}${isProjecctCompany ? '' : '资源'}`}
                      >
                        <ResourceCascader
                          getPopupContainer={trigger => trigger.parentNode}
                          roleCode={role.value}
                          resourceType={type as AuthResourceNodeType}
                          multiple
                          maxTagCount="responsive"
                          showCheckedStrategy={
                            type === 'DEPT' || type === 'AREA' ? 'SHOW_PARENT' : 'SHOW_CHILD'
                          }
                          value={resources[type]?.map(resource => resource?.value)}
                          showSearch={isShowSearch}
                          fuzzyQuery={isShowSearch}
                          resourceName={isShowSearch ? searchText[type] : undefined}
                          onChange={(_, values) => {
                            // @ts-ignore
                            handleResourcesChange(_, values, type) as unknown;
                          }}
                          onSearch={debounce(v => {
                            setSearchText({ ...searchText, [type]: v });
                            const projectCompanyValues =
                              form.getFieldValue(`resources_${type}`)?.flat() || [];
                            if (isProjecctCompany) {
                              setResources({
                                ...resources,
                                [type]: projectCompanyValues.some(
                                  (code: string | number) => code === '_PROJECT_COMPANY_ALL_'
                                )
                                  ? []
                                  : resources[type],
                              });
                              form.setFieldValue(
                                `resources_${type}`,
                                projectCompanyValues
                                  .filter(
                                    (code: string | number) => code !== '_PROJECT_COMPANY_ALL_'
                                  )
                                  .map((code: string | number) => [code])
                              );
                            }
                          }, 300)}
                          onTreeDataChange={data => {
                            if (isShowSearch) {
                              treeDataRef.current = data;
                            }
                          }}
                        />
                      </Form.Item>
                    </div>
                  );
                })}
            {specialRoleCodes.includes(role.value) && (
              // 针对 区域销售 和 战略销售角色
              <div>
                <Form.Item
                  name="resources_TEAM"
                  label="申请加入团队"
                  rules={[
                    {
                      required: true,
                      message: '申请加入团队必选',
                    },
                  ]}
                >
                  <ResourceCascader
                    getPopupContainer={trigger => trigger.parentNode}
                    roleCode={role.value}
                    // @ts-ignore
                    resourceType="TEAM"
                    maxTagCount="responsive"
                    showCheckedStrategy={
                      role.resourceType === 'DEPT' ? 'SHOW_PARENT' : 'SHOW_CHILD'
                    }
                    value={resources.TEAM?.map(resource => resource?.value)}
                    onChange={(_, values) => {
                      if (!values) {
                        setResources({
                          ...resources,
                          TEAM: [],
                        });
                        return;
                      }
                      if (values.length) {
                        setResources({
                          ...resources,
                          // @ts-ignore
                          TEAM: values?.at(-1) ? [values.at(-1)] : [],
                        });
                      }
                    }}
                  />
                </Form.Item>
              </div>
            )}
            <Form.Item
              name="expiredAt"
              label="授权过期时间"
              rules={[
                {
                  required: true,
                  message: '授权过期时间必选',
                },
              ]}
            >
              <DatePicker
                style={{ width: '100%' }}
                disabledDate={current =>
                  current &&
                  (current <= moment().endOf('day') ||
                    current > moment().add(1, 'year').endOf('day'))
                }
                showToday={false}
                format="YYYY-MM-DD 00:00:00"
                renderExtraFooter={() => (
                  <Space className={styles.datePickerExtraFooter}>
                    <Tag
                      color="processing"
                      onClick={() => form.setFieldValue('expiredAt', moment().add(1, 'day'))}
                    >
                      明天
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() => form.setFieldValue('expiredAt', moment().add(7, 'day'))}
                    >
                      7天
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() => form.setFieldValue('expiredAt', moment().add(1, 'month'))}
                    >
                      1月
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() => form.setFieldValue('expiredAt', moment().add(6, 'month'))}
                    >
                      半年
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() => form.setFieldValue('expiredAt', moment().add(1, 'year'))}
                    >
                      一年
                    </Tag>
                  </Space>
                )}
              />
            </Form.Item>
          </>
        )}
        <Form.Item
          name="description"
          label="申请说明"
          wrapperCol={{ span: 16 }}
          rules={[
            {
              required: true,
              whitespace: true,
              message: '申请说明必填',
            },
            {
              type: 'string',
              max: 120,
              message: '最多输入 120 个字符！',
            },
          ]}
        >
          <Input.TextArea placeholder="输入申请说明" />
        </Form.Item>
        <Form.Item
          name="attachments"
          label="附件"
          wrapperCol={{ span: 16 }}
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
        >
          <Upload
            accept=".jpg,.jpeg,.png"
            showUploadList
            allowDelete
            maxFileSize={20}
            maxCount={5}
            showAccept
          >
            <Button icon={<UploadOutlined />}>上传</Button>
          </Upload>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 4, span: 16 }}>
          <Space>
            <Button type="primary" loading={loading} onClick={onSubmit}>
              提交
            </Button>
            <Button
              disabled={loading}
              onClick={() => {
                history.goBack();
              }}
            >
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Space>
  );
}
