import React, { useState } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { useUserGroups } from '@manyun/auth-hub.hook.use-user-groups';
import { UserGroup } from '@manyun/auth-hub.model.user-group';
import { selectUserGroupsByCodes } from '@manyun/auth-hub.state.user-groups';
import { UserGroupsDataTable } from '@manyun/auth-hub.ui.user-groups-data-table';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

export type UserGorupsSelectorProps = {
  selectedCode?: string[];
  onSelect?: (userGroupsCode: string[]) => void;
};

export function UserGorupsSelector({ onSelect, selectedCode }: UserGorupsSelectorProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([]);
  const [{ loading, entities }, getUserGroups] = useUserGroups();
  const [visibleCodes, setVisibleCodes] = useState<string[] | undefined>(undefined);
  const visibleData = useSelector(selectUserGroupsByCodes(visibleCodes));

  const onClickVisible = () => {
    setVisible(true);
    form.resetFields();
    getUserGroups();
    setSelectedRowKeys([]);
    setVisibleCodes(undefined);
  };

  const onClickSearch = () => {
    const locations = form.getFieldValue('loaction');
    if (!locations || (Array.isArray(locations) && locations.length === 0)) {
      setVisibleCodes(undefined);
      return;
    }

    let filterCodes: string[] = [];

    if (Array.isArray(locations)) {
      Object.keys(entities).forEach(key => {
        const entity = entities[key];
        if (locations.some(code => entity.resourceCodes?.includes(code))) {
          filterCodes.push(entity.code);
        }
      });
    } else if (typeof locations == 'string') {
      Object.keys(entities).forEach(key => {
        const entity = entities[key];
        if (entity.resourceCodes?.includes(locations)) {
          filterCodes.push(entity.code);
        }
      });
    }
    setSelectedRowKeys([]);
    setVisibleCodes(filterCodes);
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (_: React.Key[], selectedRows: UserGroup[]) => {
      setSelectedRowKeys(selectedRows.map(row => row.code));
    },
  };

  return (
    <div>
      <Button type="primary" onClick={onClickVisible}>
        添加
      </Button>
      <Modal
        title="添加"
        visible={visible}
        destroyOnClose
        width={850}
        bodyStyle={{ maxHeight: `calc(80vh - 109px)`, overflowY: 'auto' }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={() => {
          if (onSelect) {
            onSelect(selectedRowKeys);
          }
          setVisible(false);
        }}
      >
        <Space direction="vertical" size="middle">
          <Form
            layout="inline"
            form={form}
            colon={false}
            style={{ display: 'flex', justifyContent: 'space-between' }}
          >
            <Form.Item name="loaction" label="资源位置">
              <LocationTreeSelect
                style={{ width: 330 }}
                allowClear
                multiple
                disabledTypes={['IDC']}
              />
            </Form.Item>
            <Form.Item style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    onClickSearch();
                  }}
                >
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setVisibleCodes(undefined);
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <UserGroupsDataTable data={visibleData} rowSelection={rowSelection} loading={loading} />
        </Space>
      </Modal>
    </div>
  );
}
