---
description: 'A BpmEditor component.'
labels: ['label1', 'label2', 'label3']
---

import { BpmEditor } from './bpm-editor';

<h1 align="center">Bpmn Process Designer</h1>

### 项目简介

一个基于 `bpmn.js`，`React` 和 `base-ui` 开发的流程设计器。

## 1.功能说明

1. 工具栏：包含常见操作，比如撤回、恢复、导出，保存等，位于画板正上方。
2. 用户任务节点：id、名称、审批方式（或签、会签）、审批人类型（用户、角色）
3. 抄送节点：id、名称、抄送节点类型（审批结束抄送、审批中抄送），抄送人类型（用户、角色、变量），其中，抄送人类型可以配置多个，但每种类型仅限配置一次。
4. 顺序流节点：id、名称、顺序流类型（普通顺序流、默认顺序流、条件顺序流）
5. 内置`activiti` 解析文件
6. 内置`customTranslate`中译英翻译文件
7. 自定义左侧元素栏 `platte` 与弹出菜单 `contentPad` 示例模块
8. 自定义右侧抽屉面板 `panel-view`

## 2.绘图原理

### 1.引入 bpmnjs，并初始化建模器

```
const bpmModeler = new BpmnModeler({
        container: '#canvas', // 这里为dom元素列表中的第一个元素
      })
```

#### 可以在源文件 Modeler.js，找到初始化时的具体参数

```
this.bpmnModeler = new BpmnModeler(options: Options）;

interface Options {
	container: DomElement; // 渲染容器
	width：string | number；// 查看器宽度
	height: string | number； // 查看器高度
	moddleExtensions： object；// 需要用的扩展包
	modules：<didi.Module>[]; // 自定义且需要覆盖默认扩展包的模块列表
	additionalModules: <didi.Module>[]; // 自定义且与默认扩展包一起使用的模块列表
}

```

#### 初始化完成之后，在控制台打印`bpmnModeler`，可以发现`BpmnModeler`类继承了多个基础类。

```
Modeler
	-> BaseModeler
		-> BaseViewer
			-> Diagram
				-> Object

```

### 2.核心模块，`EventBus`事件总线

核心模块之一，通用事件总线， 该组件用于跨图实例进行通信。 图的其他部分可以使用它来侦听和广播事件。在 DC Base 系统中，画布上生成用户节点时，会带有默认的节点名称“审批”，这个赋予节点默认名称的操作，就是通过监听`shape.added`事件实现的。

#### 系统中所监听的事件类型：

| 事件名           | 说明                                                | callback 参数                           |
| ---------------- | --------------------------------------------------- | --------------------------------------- |
| shape.added      | 已更新到 xml 内，触发渲染方法，返回值为插入的新元素 | event:InternalEvent, element: Element   |
| shape.removed    | 形状移除完成，返回值为被移除元素                    | event:InternalEvent, element: Element   |
| elements.changed | 元素发生改变并更改完成                              | event: InternalEvent, element: Elements |
| element.click    | 元素单击事件                                        | event: InternalEvent, element: Elements |
| element.dbclick  | 元素双击事件                                        | event: InternalEvent, element: Elements |

### 3.核心模块，`Moddles`，修改节点属性（如：配置审批人、配置事件监听）的基础。

#### 系统中应用的部分代码片段

```
bpmnModeler.get('moddle').create(`${prefix}:${isTask ? 'TaskListener' : 'ExecutionListener'}`, listenerObj);

bpmnModeler.get('modeling').updateProperties(element, {
        extensionElements: null,
      });

```

#### 1） ElementFactory Diagram 元素工厂

用于创建各种 Diagram（djs.model）元素，并赋予各种属性。也就是说，流程可以配置的用户任务节点（bpmn：usertask）和抄送节点（bpmn：servicetask）和顺序流节点（bpmn：sequence），都是来自于该元素工厂的

```
ElementFactory.createRoot(attrs);
ElementFactory.createLabel(attrs);
ElementFactory.createConnection(attrs);//connection代表了顺序流
ElementFactory.createShape(attrs);//shape包含了各种节点

```

#### 2）Canvas 画布

顾名思义，处理所有的元素绘制与显示。注入了`canvas.config`, `EventBus`, `GraphicsFactory`, `ElementRegistry`。

## 3.文档说明

### bpm-editor

#### panel-view

##### methods

| Method Name             | Parameters               | Description                              |
| ----------------------- | ------------------------ | ---------------------------------------- |
| initializeCcPeopleData  | --                       | 初始化抄送节点数据                       |
| initializeUserTaskData  | --                       | 初始化用户任务节点数据                   |
| getCcNodePeopleInfoData | ccNodeExpression: string | 根据表达式正则筛选出该节点的抄送对象列表 |
| 单元格                  | 单元格                   | 单元格                                   |
