import { QuestionCircleOutlined } from '@ant-design/icons';
import React, { useMemo } from 'react';

import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
//@ts-ignore old service
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { TaskPeople } from '@manyun/bpm.model.bpm-instance';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { DirectorSelect } from '@manyun/bpm.ui.director-select';
import { VariableSelect } from '@manyun/bpm.ui.variable-select';

import styles from '../bpm-editor.module.less';

export type NodeUser = { label: string; value: number; key: number; id: number };

export type UserTaskFormValues = {
  isCounterSignedNode: string;
  taskPeopleType: TaskPeople | null;
  candidateUsers: NodeUser[];
  recommendCandidateUsers: NodeUser[];
  candidateGroups: string[] | null | undefined;
  recommendCandidateGroups: string[] | null | undefined;
  shiftUser: string | null;
  recommendNeeded: string | null;
};

const samePersonOption = [
  {
    label: '由提交人对自己审批',
    value: 1,
  },
  {
    label: (
      <>
        自动跳过
        <Tooltip title="如果当前节点还有其他审批人，则交由其他审批人进行审批；如果当前节点没有其他审批人，则该节点自动通过">
          <QuestionCircleOutlined
            style={{
              color: '#00000073',
              marginLeft: '4px',
            }}
          />
        </Tooltip>
      </>
    ),
    value: 2,
  },
  {
    label: (
      <>
        转交给直属上级审批
        <Tooltip title="若直属上级为空，则自动通过">
          <QuestionCircleOutlined
            style={{
              color: '#00000073',
              marginLeft: '4px',
            }}
          />
        </Tooltip>
      </>
    ),
    value: 3,
  },
];

// 或签时的用户节点面板---用户任务节点
export const OrSignedPanel = ({
  form,
  bizFlowType,
}: {
  form: FormInstance<UserTaskFormValues>;
  bizFlowType: string;
}) => {
  const formTaskPeopleType = Form.useWatch('taskPeopleType', form);
  const recommendNeeded = Form.useWatch('recommendNeeded', form);
  const candidateGroups = Form.useWatch('candidateGroups', form) ?? [];
  const recommendCandidateGroups = Form.useWatch('recommendCandidateGroups', form) ?? [];
  const candidateUsers = Form.useWatch('candidateUsers', form) ?? [];
  const recommendCandidateUsers = Form.useWatch('recommendCandidateUsers', form) ?? [];
  const isCounterSignedNode = Form.useWatch('isCounterSignedNode', form);
  const taskPeopleType = Form.useWatch('taskPeopleType', form);

  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const listenerOption = [
    {
      label: locales.taskListener.frontListener, //前置监听器
      value: 'systemTaskStartListener',
    },
    {
      label: locales.taskListener.backListener, //后置监听器
      value: 'systemTaskCompleteListener',
    },
  ];

  return (
    <Space direction="vertical" size={16} style={{ width: '100% ' }}>
      <>
        <div>
          <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
            {locales.multipleApprovalType}
          </Typography.Title>
          <Form.Item
            label={locales.eventType.__self}
            rules={[{ required: true, message: locales.eventTypePlaceholder }]}
            name="isCounterSignedNode"
          >
            <Select
              placeholder={locales.eventTypePlaceholder}
              style={{ width: '100%' }}
              onChange={() => {
                form.setFieldsValue({ taskPeopleType: null, recommendNeeded: 'unneeded' });
              }}
            >
              <Select.Option value="COUNTER_SIGN">{locales.eventType.COUNTER_SIGN}</Select.Option>
              <Select.Option value="OR_SIGN">{locales.eventType.OR_SIGN}</Select.Option>
            </Select>
          </Form.Item>
        </div>

        <div>
          <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
            {`${locales.setPrefix}${locales.taskPeopleType.__self}`}
          </Typography.Title>
          <Form.Item
            label={locales.taskPeopleType.__self}
            rules={[{ required: true, message: locales.taskPeopleTypePlaceholder }]}
            name="taskPeopleType"
          >
            <Select
              placeholder={locales.taskPeopleTypePlaceholder}
              style={{ width: '100%' }}
              options={[
                { value: 'ASSIGNEE', label: locales.taskPeopleType.ASSIGNEE },
                { value: 'GROUPS', label: locales.taskPeopleType.GROUPS },
                { value: 'VARIABLE', label: locales.taskPeopleType.VARIABLE },
                { value: 'APPLICANT_DIRECTOR', label: locales.taskPeopleType.APPLICANT_DIRECTOR },
              ]}
            />
          </Form.Item>
          {isCounterSignedNode === 'OR_SIGN' &&
            (taskPeopleType === TaskPeople.Groups || taskPeopleType === TaskPeople.Assignee) && (
              <Form.Item
                name="recommendNeeded"
                label="发起人是否自选"
                rules={[{ required: true }]}
                initialValue="unneeded"
              >
                <Radio.Group
                  options={[
                    { label: '是', value: 'needed' },
                    { label: '否', value: 'unneeded' },
                  ]}
                  onChange={() => {
                    form.setFieldsValue({
                      candidateGroups: [],
                      candidateUsers: [],
                      recommendCandidateGroups: [],
                      recommendCandidateUsers: [],
                    });
                  }}
                />
              </Form.Item>
            )}
          {formTaskPeopleType === TaskPeople.Groups && (
            <>
              <Form.Item
                label={locales.role}
                rules={[{ required: true, message: locales.rolePlaceholder }]}
                name="candidateGroups"
                dependencies={['recommendCandidateGroups']}
              >
                <RoleSelect
                  style={{ width: '100%' }}
                  fieldNames={{ value: 'code', label: 'name' }}
                  trigger="onDidMount"
                  mode="multiple"
                  disabledKeys={recommendCandidateGroups}
                  onChange={() => {
                    form.setFieldsValue({ candidateUsers: [], recommendCandidateUsers: [] });
                    form.setFieldsValue({ shiftUser: null });
                  }}
                />
              </Form.Item>
              {recommendNeeded === 'needed' && (
                <Form.Item
                  label={`推荐${locales.role}`}
                  name="recommendCandidateGroups"
                  dependencies={['candidateGroups']}
                >
                  <RoleSelect
                    style={{ width: '100%' }}
                    fieldNames={{ value: 'code', label: 'name' }}
                    trigger="onDidMount"
                    mode="multiple"
                    disabledKeys={candidateGroups}
                    onChange={() => {
                      form.setFieldsValue({ candidateUsers: [], recommendCandidateUsers: [] });
                      form.setFieldsValue({ shiftUser: null });
                    }}
                  />
                </Form.Item>
              )}
            </>
          )}
          {formTaskPeopleType === TaskPeople.Assignee && (
            <>
              <Form.Item
                label={locales.user}
                rules={[{ required: true, message: locales.userPlaceholder }]}
                name="candidateUsers"
                dependencies={['recommendCandidateUsers']}
              >
                <UserSelect
                  style={{ width: '100%' }}
                  userState="in-service"
                  labelInValue
                  maxTagCount={6}
                  mode="multiple"
                  disabledKeys={recommendCandidateUsers.map(({ value }) => value)}
                  onChange={() => {
                    form.setFieldsValue({
                      candidateGroups: undefined,
                      recommendCandidateGroups: undefined,
                    });
                    form.setFieldsValue({ shiftUser: null });
                  }}
                />
              </Form.Item>
              {recommendNeeded === 'needed' && (
                <Form.Item
                  label={`推荐${locales.user}`}
                  name="recommendCandidateUsers"
                  dependencies={['candidateUsers']}
                >
                  <UserSelect
                    style={{ width: '100%' }}
                    userState="in-service"
                    labelInValue
                    maxTagCount={3}
                    mode="multiple"
                    disabledKeys={candidateUsers.map(({ value }) => value)}
                    onChange={() => {
                      form.setFieldsValue({
                        candidateGroups: undefined,
                        recommendCandidateGroups: undefined,
                      });
                      form.setFieldsValue({ shiftUser: null });
                    }}
                  />
                </Form.Item>
              )}
            </>
          )}
          {formTaskPeopleType === TaskPeople.Variable && (
            <Form.Item
              label={`${locales.viriable}${locales.title}`}
              rules={[{ required: true, message: locales.viriablePlaceholder }]}
              name="shiftUser"
            >
              <VariableSelect
                style={{ width: '100%' }}
                onChange={() => {
                  form.setFieldsValue({
                    candidateGroups: undefined,
                    recommendCandidateGroups: undefined,
                  });
                  form.setFieldsValue({ candidateUsers: [], recommendCandidateUsers: [] });
                }}
              />
            </Form.Item>
          )}

          {formTaskPeopleType === TaskPeople.Supervisor && (
            <Form.Item label={locales.director} rules={[{ required: true }]} name="director">
              <DirectorSelect style={{ width: '100%' }} />
            </Form.Item>
          )}
        </div>
        {(bizFlowType === 'SERVICE' || bizFlowType === 'MIX') && (
          <div>
            <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
              {locales.taskListener.__self}
            </Typography.Title>

            <Form.Item name="taskListener">
              <Checkbox.Group
                options={listenerOption}
                disabled={bizFlowType === 'SERVICE' ? true : false}
              />
            </Form.Item>
          </div>
        )}
        <div>
          <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
            审批人与提交人为同一人时
          </Typography.Title>

          <Form.Item name="samePersonHandleType">
            <Radio.Group className={styles.samePersonHandleType} options={samePersonOption} />
          </Form.Item>
        </div>
      </>
    </Space>
  );
};
