import { PlusOutlined } from '@ant-design/icons';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';

import style from './style.module.less';

const AddBtn = ({ title, onClick }: Record<string, any>) => {
  return (
    <div className={style.btnContent} onClick={onClick}>
      <Button type="text">
        <PlusOutlined />
        {title}
      </Button>
    </div>
  );
};

export default AddBtn;
