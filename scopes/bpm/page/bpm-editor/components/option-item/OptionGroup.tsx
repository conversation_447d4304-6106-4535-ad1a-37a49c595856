import { DeleteOutlined } from '@ant-design/icons';
import React, { useCallback, useContext, useMemo } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';

import AddBtn from './AddBtn';
import Option from './Option';
import { GroupContext } from './index';
import style from './style.module.less';

const OptionGroup = ({ groupIndex, onDelete }: Record<string, any>) => {
  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const { groupData, setGroupData } = useContext(GroupContext);

  const handleAddOption = useCallback(() => {
    const val = [
      ...groupData[groupIndex],
      {
        condition: undefined,
        propertiesCode: undefined,
        value: undefined,
        valType: 'values',
      },
    ];
    const obj = [...groupData];
    obj[groupIndex] = val;

    setGroupData(obj);
  }, [JSON.stringify(groupData)]);

  const handleDeleteOption = (index: number) => {
    const obj = [...groupData];
    const currentGroup = [...groupData[groupIndex]];
    currentGroup.splice(index, 1);
    obj[groupIndex] = currentGroup;

    setGroupData(obj);
  };

  return (
    <div>
      {groupIndex > 0 && <div className={style.optionGroupTitle}>或</div>}
      <Card
        key={`group${Math.round(Math.random() * 10).toString()}`}
        title={`${locales.expressionManualInfo.option}${groupIndex + 1}`}
        extra={
          groupData.length !== 1 && (
            <DeleteOutlined className={style.deleteGroupBtn} onClick={onDelete} />
          )
        }
        style={{ width: '100%' }}
      >
        {groupData[groupIndex]?.map((item: Record<string, any>, index: number) => (
          <Option
            data={item}
            index={index}
            groupIndex={groupIndex}
            onDelete={() => handleDeleteOption(index)}
          ></Option>
        ))}
        <AddBtn title={locales.expressionManualInfo.addOption} onClick={handleAddOption}></AddBtn>
      </Card>
    </div>
  );
};

export default OptionGroup;
