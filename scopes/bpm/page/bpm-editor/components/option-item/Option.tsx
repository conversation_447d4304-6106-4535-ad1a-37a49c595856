import { DeleteOutlined } from '@ant-design/icons';
import React, { useContext, useMemo } from 'react';

import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';

import OptionFormItem from './FormItem';
import { GroupContext } from './index';
import style from './style.module.less';

export type SelectMode = 'signle' | 'multiple' | 'tags';

const Option = ({ data, index, groupIndex, onDelete }: Record<string, any>) => {
  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const { groupData } = useContext(GroupContext);

  return (
    <div key={`option${Math.round(Math.random() * 10).toString()}`}>
      <div className={style.optionTitle}>
        {index === 0 ? locales.expressionManualInfo.as : locales.expressionManualInfo.add}
        {groupData[groupIndex]?.length !== 1 && (
          <DeleteOutlined className={style.deleteBtn} onClick={onDelete} />
        )}
      </div>
      <OptionFormItem groupIndex={groupIndex} optionIndex={index} data={data}></OptionFormItem>
    </div>
  );
};

export default Option;
