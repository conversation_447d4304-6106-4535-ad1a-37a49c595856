import flatten from 'lodash/flatten';
import React, { createContext, useCallback, useEffect, useMemo, useState } from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';

import AddBtn from './AddBtn';
import OptionGroup from './OptionGroup';
import {
  formateContainsValue,
  formateExpression,
  getCommonOptionGroupArr,
  getPropertyOrUser,
} from './utils';

export const GroupContext = createContext<{
  form: Record<string, any>;
  resource: Record<string, any>[];
  groupData: Record<string, any>[][];
  setGroupData: (val: any) => void;
}>({
  form: {},
  resource: [],
  groupData: [],
  setGroupData: val => {}, // 修改这里
});

export default function OptionItem({ form, resource }: Record<string, any>) {
  const [groupData, setGroupData] = useState<Record<string, any>[][]>([]);
  const locales = useMemo(() => getBpmInstanceLocales(), []);

  useEffect(() => {
    //不管表单怎么变，都在修改 expression
    const expression = form.getFieldValue('expression');

    if (!expression) {
      setGroupData([
        ...groupData,
        [
          {
            condition: undefined,
            propertiesCode: undefined,
            value: undefined,
          },
        ],
      ]);
      return;
    }

    const optionGroup = formateExpression(expression);

    if (!optionGroup) {
      return;
    }

    const optionGroupArr = optionGroup?.map((item: string) => {
      if (item.includes('&&')) {
        //处理并且--包含和不包含需要特殊处理--只有包含和不包含才存在小括号，其他不存在
        if (item.includes('&&(') || item.includes('))&&') || item.includes('))')) {
          const arr = item?.split('&&(').map(val => val.split('))&&'));
          const andArr = flatten(arr);

          return andArr?.map((val: string, index: number) => {
            //看val是否包含 &&，如果包含就先将他们处理成一条
            if (val.includes('&&')) {
              const valArr = val.split('&&');
              const expressionArr = valArr.map((v, i) => formateContainsValue(v, resource));

              return {
                condition: expressionArr[0].condition,
                propertiesCode: expressionArr[0].propertiesCode,
                value: expressionArr.map((v: any) => {
                  return v?.value;
                }),
              };
            }

            return getCommonOptionGroupArr(val, resource, 'and') as Record<string, any>;
          });
        } else {
          const andArr = item?.split('&&');

          return andArr?.map((val: string) => {
            return getCommonOptionGroupArr(val, resource, 'and') as Record<string, any>;
          });
        }
      } else {
        //处理或者
        return getCommonOptionGroupArr(item, resource, 'or') as any;
      }
    });

    //为初始化的值里面添加额外属性
    const newOptionGroupArr = optionGroupArr.map((item: Record<string, any>[]) => {
      return item.map(val => {
        const resData = resource?.find(
          (item: Record<string, any>) => item?.propertiesCode === val?.propertiesCode
        );
        const optionData =
          resData?.inputWay === 'COMPONENT' && resData?.options && JSON.parse(resData?.options);
        //数组的时候要额外处理
        const i = getPropertyOrUser(resData, val?.value);
        val.valueType = i ? resData?.valueType.split(',')[i] : resData?.valueType;
        val.components = optionData?.components;
        val.valType = i === 0 ? 'properties' : 'values';
        if (i === 1) {
          val.value = val.value ? Number(val.value) : val.value;
        }
        return val;
      });
    });

    setGroupData(newOptionGroupArr);
  }, [resource]);

  useEffect(() => {
    form.setFieldValue('group', groupData);
  }, [JSON.stringify(groupData)]);

  const handleAddOptionGroup = useCallback(() => {
    setGroupData([
      ...groupData,
      [
        {
          condition: undefined,
          propertiesCode: undefined,
          value: undefined,
          valType: 'values',
        },
      ],
    ]);
  }, [JSON.stringify(groupData)]);

  const handleDeleteClick = (index: number) => {
    groupData.splice(index, 1);
    setGroupData([...groupData]);
  };

  return (
    <GroupContext.Provider value={{ form, resource, groupData, setGroupData }}>
      <Form.Item name="group">
        {groupData.map((item, index) => {
          return (
            <OptionGroup key={index} groupIndex={index} onDelete={() => handleDeleteClick(index)} />
          );
        })}
        <AddBtn
          title={locales.expressionManualInfo.addOptionGroup}
          onClick={handleAddOptionGroup}
        ></AddBtn>
      </Form.Item>
    </GroupContext.Provider>
  );
}
