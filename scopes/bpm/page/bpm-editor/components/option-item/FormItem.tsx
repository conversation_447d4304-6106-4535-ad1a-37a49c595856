import debounce from 'lodash/debounce';
import React, { useCallback, useContext, useEffect, useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { useLazyMetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

import { ARRAY_RESOURCE, NUMBER_RESOURCE, STRING_RESOURCE } from '../constants';
import { GroupContext } from './index';

function getOptions(options: string) {
  const optionsArr = options?.split(',');
  return optionsArr?.map(option => ({ label: option, value: option }));
}

const getPlaceholder = (condition?: string, text?: string, type?: string) => {
  if (condition === 'include' || condition === 'notInclude') {
    return '选择多个值则多个值之间为且的关系';
  }

  if (condition === 'equal' || condition === 'noEqual') {
    return type === 'UserSelect'
      ? '请根据用户id,用户名或邮箱搜索，如若不选择定义为空值'
      : '不选择定义为空值';
  }

  return text;
};

function getCustomComponent(
  optComponent: Record<string, any>,
  handleValueChange: (val?: any, type?: string) => void,
  handleInputValueChange: (val?: any, type?: string) => void,
  selectMode?: string,
  condition?: string,
  locales?: any
) {
  switch (optComponent.components) {
    case 'location':
      return (
        <LocationCascader
          allowClear
          style={{ width: '100%' }}
          nodeTypes={optComponent?.nodes}
          authorizedOnly={
            Array.isArray(optComponent?.componentProps) &&
            optComponent?.componentProps.indexOf('authorized') > -1
          }
          includeVirtualBlocks={
            Array.isArray(optComponent?.componentProps) &&
            optComponent?.componentProps.indexOf('virtual') > -1
          }
          changeOnSelect={false}
          multiple={selectMode === '1'}
          onChange={val => handleValueChange(val)}
          placeholder={getPlaceholder(
            condition,
            locales.expressionManualInfo.valueSelectPlaceHolder
          )}
        />
      );
    case 'metaData':
      return (
        <MetaTypeSelect
          allowClear
          style={{ width: '100%' }}
          metaType={optComponent?.componentProps}
          mode={selectMode === '1' ? 'multiple' : undefined} //只处理multiple,其他类型不需要
          labelInValue={optComponent?.valueType === 'name' ? true : false}
          onChange={(value: Record<string, any>) => {
            if (optComponent?.valueType === 'name') {
              handleValueChange(value?.label);
              return;
            }

            handleValueChange(value);
          }}
          placeholder={getPlaceholder(
            condition,
            locales.expressionManualInfo.valueSelectPlaceHolder
          )}
        />
      );

    case 'time':
      return (
        <TimePicker
          format="HH:mm"
          style={{ width: '100%' }}
          onChange={handleValueChange}
          allowClear
          placeholder={getPlaceholder(
            condition,
            locales.expressionManualInfo.valueSelectPlaceHolder
          )}
        />
      );
    case 'date':
      return (
        <DatePicker
          showTime
          format="YYYY-MM-DD HH:mm:ss"
          style={{ width: '100%' }}
          onChange={handleValueChange}
          allowClear
          placeholder={getPlaceholder(
            condition,
            locales.expressionManualInfo.valueSelectPlaceHolder
          )}
        />
      );
    default:
      return (
        <Input
          allowClear
          placeholder={getPlaceholder(condition, locales.expressionManualInfo.valuePlaceHolder)}
          onChange={handleInputValueChange}
        />
      );
  }
}

function getComponent({
  propertiesResource,
  item,
  locales,
  valType,
  condition,
  handleValueChange,
  handleInputValueChange,
}: Record<string, any>) {
  if (!item) {
    return;
  }

  const newItem = { ...item };
  const optComponent =
    newItem?.inputWay === 'COMPONENT' && newItem?.options && JSON.parse(newItem?.options);
  const arrOption =
    newItem?.valueType?.includes(',') && newItem?.options && JSON.parse(newItem?.options);
  let optResource: Record<string, any>[] = [];

  //处理当属性选择数值和选择属性
  if (newItem?.valueType?.includes(',')) {
    let index = arrOption[0].type === valType ? 0 : 1;

    newItem.inputWay = newItem.inputWay?.split(',')[index];
    newItem.valueType = newItem.valueType?.split(',')[index];
  }

  if (newItem?.valueType === 'PROPERTIES') {
    const option = arrOption.find((val: Record<string, any>) => val?.type === 'properties').value;
    const optionsArr = option.split(',');

    optResource = optionsArr.map((val: string) => {
      return propertiesResource.find((result: Record<string, any>) => result.value === val);
    });
  } else {
    optResource = getOptions(newItem.options!);
  }

  switch (newItem.inputWay) {
    case 'INPUT':
      return newItem.valueType === 'NUMBER' ? (
        <InputNumber
          style={{ width: '100%' }}
          min={0}
          max={999999}
          precision={0}
          onChange={handleInputValueChange}
          placeholder={getPlaceholder(condition, locales.expressionManualInfo.valuePlaceHolder)}
        />
      ) : (
        <Input
          allowClear
          addonAfter={newItem?.propertiesUnit}
          onChange={handleInputValueChange}
          placeholder={getPlaceholder(condition, locales.expressionManualInfo.valuePlaceHolder)}
        />
      );
    case 'OPT':
      //数据源做处理
      return (
        <Select
          style={{ width: '100%' }}
          allowClear
          options={optResource}
          mode={newItem?.optionType === '1' ? 'multiple' : undefined}
          onChange={handleValueChange}
          placeholder={getPlaceholder(
            condition,
            locales.expressionManualInfo.valueSelectPlaceHolder
          )}
        />
      );
    case 'USERSELECT':
      return (
        <UserSelect
          style={{ width: '100%' }}
          labelInValue={false}
          allowClear
          onChange={val => handleValueChange(val, newItem)}
          placeholder={getPlaceholder(
            condition,
            locales.expressionManualInfo.valueUserSelectPlaceHolder,
            'UserSelect'
          )}
          userState="in-service"
        />
      );
    case 'COMPONENT':
      return getCustomComponent(
        optComponent,
        handleValueChange,
        handleInputValueChange,
        newItem?.optionType,
        condition,
        locales
      );
    default:
      return (
        <Input
          allowClear
          placeholder={getPlaceholder(condition, locales.expressionManualInfo.valuePlaceHolder)}
          onChange={handleInputValueChange}
        />
      );
  }
}

const OptionFormItem = ({ groupIndex, optionIndex, data }: Record<string, any>) => {
  const name = `manual.${groupIndex}.${optionIndex}`;
  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const { groupData, setGroupData, resource, form } = useContext(GroupContext);
  const [option, setOption] = useState<Record<string, any>[]>([]); //第二行数据源
  const [itemSource, setItemSource] = useState<Record<string, any>>();
  const propertiesResource = resource.map((item: Record<string, any>) => ({
    label: item?.propertiesName,
    value: item?.propertiesCode,
  }));
  const resData = resource?.find(
    (item: Record<string, any>) => item?.propertiesCode === data?.propertiesCode
  );
  const optComponent =
    resData?.inputWay === 'COMPONENT' && resData?.options && JSON.parse(resData?.options);

  const [getMetaData, { data: metaData }] = useLazyMetadataTree({
    variables: {
      type: optComponent?.componentProps,
    },
  });

  const getOptionResource = useCallback(
    (value: string) => {
      const resData = resource?.find((item: Record<string, any>) => item?.propertiesCode === value);
      setItemSource({ ...resData }); //当前项的数据源

      // 第二个行
      if (resData?.propertiesType === 'NUMBER' || resData?.propertiesType === 'DATE') {
        setOption(NUMBER_RESOURCE);
      } else if (resData?.propertiesType === 'ARRAY') {
        setOption(ARRAY_RESOURCE);
      } else {
        setOption(STRING_RESOURCE);
      }
    },
    [resource]
  );

  useEffect(() => {
    if (optComponent?.valueType === 'name') {
      getMetaData();
    }

    getOptionResource(data.propertiesCode);
  }, []);

  useEffect(() => {
    let value = data?.value;

    form.setFieldsValue({
      [`${name}.propertiesCode`]: data?.propertiesCode,
      [`${name}.condition`]: data?.condition,
      [`${name}.value`]: value,
      [`${name}.valType`]: data?.valType,
    });

    if (optComponent?.valueType === 'name' && metaData?.metadataTree) {
      const val = metaData?.metadataTree?.find((item: Record<string, any>) => item.name === value);
      let resValue = undefined;

      if (val) {
        resValue = { ...val, value: val?.code, label: val?.name };
      }

      const obj = form.getFieldValue('group');
      obj[groupIndex][optionIndex].value = resValue;

      form.setFieldsValue({
        [`${name}.value`]: resValue,
        group: obj,
      });
    }
  }, [JSON.stringify(resource), metaData]);

  //矫正value
  const handlePropertiesChange = (value: string) => {
    getOptionResource(value);

    const resData = resource?.find((item: Record<string, any>) => item?.propertiesCode === value);
    const optionData =
      resData?.inputWay === 'COMPONENT' && resData?.options && JSON.parse(resData?.options);

    const obj = [...groupData];
    obj[groupIndex][optionIndex].propertiesCode = value;
    obj[groupIndex][optionIndex].components = optionData?.components;
    obj[groupIndex][optionIndex].valueType = resData?.valueType;
    obj[groupIndex][optionIndex].valType = 'values';
    obj[groupIndex][optionIndex].value = undefined;
    obj[groupIndex][optionIndex].condition = undefined;

    form.setFieldsValue({
      [`${name}.valType`]: 'values',
      [`${name}.condition`]: undefined,
      [`${name}.value`]: undefined,
    });

    setGroupData(obj);
  };

  const handleValueChange = (value: any, item?: Record<string, any>) => {
    const obj = [...groupData];
    obj[groupIndex][optionIndex].value = value;

    if (item?.valueType) {
      obj[groupIndex][optionIndex].valueType = item?.valueType;
    }

    setGroupData(obj);
  };

  const handleInputValueChange = debounce((value: any) => {
    const obj = [...groupData];
    obj[groupIndex][optionIndex].value = typeof value === 'object' ? value.target.value : value;

    setGroupData(obj);
  }, 500);

  return (
    <>
      <Form.Item
        name={`${name}.propertiesCode`}
        rules={[{ required: true, message: locales.expressionManualInfo.propertyRequired }]}
      >
        <Select
          allowClear
          placeholder={locales.expressionManualInfo.propertyPlaceHolder}
          style={{ width: '100%' }}
          options={propertiesResource || []}
          onChange={handlePropertiesChange}
        />
      </Form.Item>
      <Form.Item
        name={`${name}.condition`}
        rules={[{ required: true, message: locales.expressionManualInfo.optionRequired }]}
      >
        <Select
          allowClear
          placeholder={locales.expressionManualInfo.optionPlaceHolder}
          style={{ width: '100%' }}
          options={option || []}
          onChange={value => {
            const obj = [...groupData];
            obj[groupIndex][optionIndex].condition = value;
            setGroupData(obj);
          }}
        />
      </Form.Item>
      <Form.Item
        name={`${name}.valType`}
        style={{ display: !resData?.valueType.includes(',') ? 'none' : 'block' }}
      >
        <Radio.Group
          options={[
            {
              label: '数值',
              value: 'values',
            },
            {
              label: '属性',
              value: 'properties',
            },
          ]}
          onChange={val => {
            const obj = [...groupData];
            const newItem = { ...itemSource };
            const arrOption =
              newItem?.valueType?.includes(',') && newItem?.options && JSON.parse(newItem?.options);
            let index;
            if (newItem?.valueType?.includes(',')) {
              index = arrOption[0].type === val ? 0 : 1;
            }

            obj[groupIndex][optionIndex].valType = val.target.value;
            obj[groupIndex][optionIndex].valueType = index
              ? newItem?.valueType.split(',')[index]
              : newItem?.valueType;
            obj[groupIndex][optionIndex].value = undefined;

            form.setFieldsValue({
              [`${name}.valType`]: val.target.value,
              [`${name}.valueType`]: index
                ? newItem?.valueType.split(',')[index]
                : newItem?.valueType,
              [`${name}.value`]: undefined,
            });
            setGroupData(obj); //表达式和手工要转化，所以必须实时存储
          }}
        />
      </Form.Item>
      <Form.Item
        name={`${name}.value`}
        rules={[
          {
            required:
              form.getFieldValue(`${name}.condition`) !== 'equal' &&
              form.getFieldValue(`${name}.condition`) !== 'noEqual',
            message: locales.expressionManualInfo.valueRequired,
          },
        ]}
      >
        {getComponent({
          condition: form.getFieldValue(`${name}.condition`),
          valType: form.getFieldValue(`${name}.valType`),
          item: itemSource,
          locales,
          propertiesResource,
          handleValueChange,
          handleInputValueChange,
        })}
      </Form.Item>
    </>
  );
};

export default OptionFormItem;
