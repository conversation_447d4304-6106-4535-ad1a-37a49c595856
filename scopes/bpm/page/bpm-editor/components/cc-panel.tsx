import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import React, { useEffect, useMemo, useState } from 'react';

import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
//@ts-ignore old service
import { Typography } from '@manyun/base-ui.ui.typography';
import { TaskPeople } from '@manyun/bpm.model.bpm-instance';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { DirectorSelect } from '@manyun/bpm.ui.director-select';
import { VariableSelect } from '@manyun/bpm.ui.variable-select';

export type CarbonCopyFormValues = {
  ccApprovalType: 'APPROVAL_CC' | 'APPROVAL_PASS_CC';
  ccPeoples: { type: string; codeList?: { key: number; label: string; value: number }[] }[];
};

const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 16, offset: 6 },
  },
};

// 抄送节点面板
export const CcPanel = ({ form }: { form: FormInstance<CarbonCopyFormValues> }) => {
  const locales = useMemo(() => getBpmInstanceLocales(), []);

  const formCcPeoples = Form.useWatch('ccPeoples', form);
  const taskPeopleOptions = [
    { label: locales.taskPeopleType.ASSIGNEE, value: 'ASSIGNEE' },
    { label: locales.taskPeopleType.GROUPS, value: 'GROUPS' },
    { label: locales.taskPeopleType.VARIABLE, value: 'VARIABLE' },
    { label: locales.taskPeopleType.APPLICANT_DIRECTOR, value: 'APPLICANT_DIRECTOR' },
  ];
  const [ccPeopleTypeList, setCcPeopleList] =
    useState<{ label: string; value: string }[]>(taskPeopleOptions);

  useEffect(() => {
    if (formCcPeoples?.length) {
      const selectOptions = taskPeopleOptions;
      const formCcPeopleTypes = formCcPeoples.map((data: { type: string }) => data?.type);

      setCcPeopleList(
        selectOptions.map(option => {
          if (formCcPeopleTypes.includes(option.value)) {
            return { ...option, disabled: true };
          } else {
            return option;
          }
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formCcPeoples]);

  return (
    <Space direction="vertical" size={16} style={{ width: '100% ' }}>
      <div>
        <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
          {`${locales.setPrefix}${locales.carbonCopyNode.__self}`}
        </Typography.Title>

        <Form.Item
          label={locales.carbonCopyNode.__self}
          rules={[{ required: true, message: locales.carbonCopyNode.placeholder }]}
          name="ccApprovalType"
        >
          <Select
            placeholder={locales.carbonCopyNode.placeholder}
            style={{ width: '100%' }}
            options={[
              { label: locales.carbonCopyNode.APPROVAL_CC, value: 'APPROVAL_CC' },
              { label: locales.carbonCopyNode.APPROVAL_PASS_CC, value: 'APPROVAL_PASS_CC' },
            ]}
          />
        </Form.Item>
      </div>
      <div>
        <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
          {`${locales.setPrefix}${locales.carbonCopyPeople.__self}`}
        </Typography.Title>
        <Form.List
          name="ccPeoples"
          rules={[
            {
              validator: async (_, ccPeoples) => {
                if (!ccPeoples || ccPeoples.length < 1 || !ccPeoples.length) {
                  return Promise.reject(new Error(locales.carbonCopyPeople.isNotNull));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field, index) => (
                <div key={index}>
                  <Form.Item
                    label={locales.carbonCopyPeople.type}
                    rules={[{ required: true, message: locales.carbonCopyPeople.placeholder }]}
                    {...field}
                    name={[field.name, 'type']}
                    key={`${field.name}type`}
                  >
                    <Select
                      placeholder={locales.carbonCopyPeople.placeholder}
                      style={{ width: '100%' }}
                      onChange={value => {
                        const tempFormCcPeoples = formCcPeoples;

                        if (tempFormCcPeoples[field.name]?.type) {
                          tempFormCcPeoples[field.name].type = value;
                        }

                        if (!tempFormCcPeoples[field.name]?.type) {
                          tempFormCcPeoples[field.name] = { type: value };
                        }

                        if (tempFormCcPeoples[field.name]?.codeList) {
                          tempFormCcPeoples[field.name].codeList = [];
                        }
                        form.setFieldsValue({ ccPeoples: tempFormCcPeoples });
                      }}
                      options={ccPeopleTypeList}
                    ></Select>
                  </Form.Item>
                  {formCcPeoples?.length > 0 &&
                    formCcPeoples[field.name]?.type === TaskPeople.Groups && (
                      <Form.Item
                        label={locales.role}
                        rules={[{ required: true, message: locales.rolePlaceholder }]}
                        {...field}
                        name={[field.name, 'codeList']}
                        key={`${field.name}codeList`}
                      >
                        <RoleSelect
                          style={{ width: '100%' }}
                          mode="multiple"
                          fieldNames={{ value: 'code', label: 'name' }}
                          trigger="onDidMount"
                        />
                      </Form.Item>
                    )}
                  {formCcPeoples?.length > 0 &&
                    formCcPeoples[field.name]?.type === TaskPeople.Assignee && (
                      <Form.Item
                        label={locales.user}
                        rules={[{ required: true, message: locales.userPlaceholder }]}
                        {...field}
                        name={[field.name, 'codeList']}
                        key={`${field.name}codeList`}
                      >
                        <UserSelect
                          style={{ width: '100%' }}
                          labelInValue
                          maxTagCount={3}
                          mode="multiple"
                          userState="in-service"
                        />
                      </Form.Item>
                    )}
                  {formCcPeoples?.length > 0 &&
                    formCcPeoples[field.name]?.type === TaskPeople.Variable && (
                      <Form.Item
                        label={`${locales.viriable}${locales.title}`}
                        rules={[{ required: true, message: locales.viriablePlaceholder }]}
                        {...field}
                        name={[field.name, 'codeList']}
                        key={`${field.name}codeList`}
                      >
                        <VariableSelect mode="multiple" style={{ width: '100%' }} />
                      </Form.Item>
                    )}
                  {formCcPeoples?.length > 0 &&
                    formCcPeoples[field.name]?.type === TaskPeople.Supervisor && (
                      <Form.Item
                        label={locales.director}
                        rules={[{ required: true }]}
                        {...field}
                        name={[field.name, 'codeList']}
                        key={`${field.name}codeList`}
                      >
                        <DirectorSelect style={{ width: '100%' }} mode="multiple" />
                      </Form.Item>
                    )}
                  {fields.length > 1 ? (
                    <Form.Item {...formItemLayoutWithOutLabel} key={`${field.name}Button`}>
                      <Button
                        type="dashed"
                        danger
                        onClick={() => {
                          remove(field.name);
                        }}
                        style={{
                          width: 170,
                        }}
                        icon={<MinusCircleOutlined />}
                      >
                        删除抄送人
                      </Button>
                    </Form.Item>
                  ) : null}
                </div>
              ))}

              <Form.Item {...formItemLayoutWithOutLabel}>
                <Button
                  type="dashed"
                  onClick={() => {
                    if (fields.length === 3) {
                      message.warning('最多支持添加3种类型的抄送人');
                      return;
                    }
                    add();
                  }}
                  style={{
                    width: 170,
                  }}
                  icon={<PlusOutlined />}
                >
                  添加抄送人
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>
      </div>
    </Space>
  );
};
