import React, { useEffect, useMemo, useState } from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
//@ts-ignore old service
import { Typography } from '@manyun/base-ui.ui.typography';
import { Sequence } from '@manyun/bpm.model.bpm-instance';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { fetchPropertiesQuery } from '@manyun/bpm.service.fetch-properties-query';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';

import OptionItem from './option-item';

export type SequenceFormValues = {
  sequenceType: Sequence;
  expression: string;
};

// 顺序流面板
export const SequencePanel = ({
  form,
  element,
  historyParams,
  operationType,
  setOperationType,
  resource,
  setResource,
  formateExpression,
}: {
  form: FormInstance<SequenceFormValues>;
  historyParams: Record<string, string>;
  /** 需要.d.ts */
  element: any;
  operationType: boolean;
  setOperationType: (val: boolean) => void;
  resource: Record<string, any>[];
  setResource: (val: Record<string, any>[]) => void;
  formateExpression: (val: Record<string, any>) => string;
}) => {
  // 由于有部分组件是根据sequenceType来决定是否渲染的，因此需要watch。
  const formSequenceType = Form.useWatch('sequenceType', form);
  const formExpression = Form.useWatch('expression', form);
  const [conditionStatus, setConditionStatus] = useState<'success' | 'error'>('success');
  const [conditionHelper, setConditionHelper] = useState('');
  const [disabledOption, setDisabledOption] = useState<boolean>(false);
  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const [, { checkCode }] = useAuthorized();
  const isOperation = checkCode('element_manually_switch_expressions');

  const initFormateExpression = (expression: string) => {
    const resultString = expression?.replace(/\$\{|}|\(|\)/g, '');
    const orArr = resultString?.split('||');

    const allExpression = orArr.flatMap(item => (item.includes('&&') ? item.split('&&') : [item]));

    const signArr = ['==', '!=', '&gt;=', '&lt;=', '&gt;', '&lt;', '.', '>', '>=', '<', '<='];

    const result = allExpression.map(item => {
      const sign = signArr.find(v => item.includes(v));
      const val = sign ? item.split(sign)[0].trim() : item.trim();
      return val.replace(/!/g, '');
    });

    // 给result 去重
    return [...new Set(result)];
  };

  // 小括号和值不对是不能手工操作的
  const isManualDisable = (expression: string, data: Record<string, any>[]) => {
    if (!expression) {
      setDisabledOption(false);

      return;
    }

    const expArr = initFormateExpression(expression); //表达式
    const resourceName = data?.map((item: Record<string, any>) => item.propertiesCode); //数据源

    let disable = false;
    for (let item of expArr) {
      const val = resourceName.find((res: string) => res === item);
      if (!val) {
        disable = true;
        break;
      }
    }

    setDisabledOption(disable);
  };

  useEffect(() => {
    fetchPropertiesQuery({
      bizScenes: historyParams?.bizScenes,
      subBizScenes: historyParams?.subBizScenes,
    }).then(({ data }) => {
      setResource(data);

      const expression = form.getFieldValue('expression');
      //校验是否可用手工
      //isManualDisable(expression, data);
    });
  }, []);

  useEffect(() => {
    const checkExpression = () => {
      const reg = /\$\{(.+?)}/;
      if (reg.test(formExpression)) {
        setConditionStatus('success');
        setConditionHelper('');
      } else if (!reg.test(formExpression) && formExpression) {
        setConditionStatus('error');
        setConditionHelper(locales.expression.isErrorFormat);
      } else {
        setConditionStatus('error');
        setConditionHelper(locales.expression.isNotNull);
      }
    };
    checkExpression();
  }, [element, formExpression, locales.expression.isErrorFormat, locales.expression.isNotNull]);
  const getSequenceTypeOptions = (isTargetNodeNotUserTask: boolean) => {
    const sequenceTypeOptions = [
      { label: locales.sequenceType.DEFAULT, value: 'DEFAULT' },
      { label: locales.sequenceType.CONDITIONAL, value: 'CONDITIONAL' },
      { label: locales.sequenceType.NORMAL, value: 'NORMAL' },
    ];
    return isTargetNodeNotUserTask
      ? sequenceTypeOptions
      : sequenceTypeOptions.filter(option => option.value !== 'DEFAULT');
  };

  return (
    <div>
      <Typography.Title showBadge level={5} style={{ marginBottom: '24px' }}>
        {`${locales.setPrefix}${locales.nodeType.SEQUENCE}`}
      </Typography.Title>
      <Form.Item label={locales.sequenceType.__self} name="sequenceType">
        <Select
          placeholder={locales.sequenceType.placeholder}
          style={{ width: '100%' }}
          options={getSequenceTypeOptions(
            element?.businessObject?.sourceRef?.$type !== 'bpmn:UserTask'
          )}
        />
      </Form.Item>
      {formSequenceType === Sequence.Conditional &&
        element?.businessObject?.sourceRef?.$type !== 'bpmn:UserTask' &&
        historyParams?.bizScenes &&
        historyParams?.subBizScenes &&
        historyParams?.bizFlowType && (
          <Form.Item label={locales.optionType.__self} name="optionType">
            <Radio.Group
              disabled={disabledOption}
              options={
                isOperation
                  ? [
                      { label: locales.optionType.MANUAL, value: 'manual' },
                      { label: locales.optionType.CONDITION, value: 'condition' },
                    ]
                  : [{ label: locales.optionType.MANUAL, value: 'manual' }]
              }
              onChange={value => {
                const formData: Record<string, any> = form.getFieldsValue();
                const groupVal = formData.group;

                if (
                  groupVal &&
                  groupVal.length === 1 &&
                  !groupVal[0][0].condition &&
                  !groupVal[0][0].propertiesCode &&
                  !groupVal[0][0].value &&
                  !groupVal[0][0].valueType &&
                  !groupVal[0][0].components
                ) {
                  setOperationType(false);
                  form.setFieldValue('optionType', value.target.value);
                  return;
                }

                form.setFieldValue('optionType', 'manual');

                form.validateFields().then(val => {
                  form.setFieldValue('optionType', value.target.value);
                  if (value.target.value === 'manual') {
                    setOperationType(true);
                  } else {
                    setOperationType(false);
                    const expression = formateExpression(val);
                    form.setFieldValue('expression', expression);
                    if (value.target.value === 'condition') {
                      isManualDisable(expression, resource);
                    }
                  }
                });
              }}
            />
          </Form.Item>
        )}
      {formSequenceType === Sequence.Conditional &&
        element?.businessObject?.sourceRef?.$type === 'bpmn:UserTask' &&
        operationType === false && (
          <Form.Item label={locales.conditionalSequenceType.__self} name="expression">
            <Select
              placeholder={locales.conditionalSequenceType.placeholder}
              style={{ width: '100%' }}
              options={[
                // eslint-disable-next-line no-template-curly-in-string
                { label: locales.conditionalSequenceType.AGREE_BRANCH, value: '${result == 0}' },
                // eslint-disable-next-line no-template-curly-in-string
                { label: locales.conditionalSequenceType.REFUSE_BRANCH, value: '${result == 1}' },
              ]}
            />
          </Form.Item>
        )}
      {formSequenceType === Sequence.Conditional &&
        element?.businessObject?.sourceRef?.$type !== 'bpmn:UserTask' &&
        operationType === false && (
          <Form.Item
            validateStatus={conditionStatus}
            help={conditionHelper}
            label={locales.expression.__self}
            name="expression"
            extra={
              disabledOption ? (
                <div style={{ color: 'var(--manyun-error-color)' }}>{locales.expressionExtra}</div>
              ) : null
            }
          >
            <Input
              placeholder={locales.expression.placeholder}
              style={{ width: '100%' }}
              onChange={e => {
                const val = e.target.value;
                //当expression不对的时候，不能手工操作
                isManualDisable(val, resource);
              }}
            ></Input>
          </Form.Item>
        )}
      {operationType && formSequenceType === Sequence.Conditional && (
        <OptionItem form={form} resource={resource}></OptionItem>
      )}
    </div>
  );
};
