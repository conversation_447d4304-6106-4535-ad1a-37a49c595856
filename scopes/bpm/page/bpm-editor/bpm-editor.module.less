@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

#editorPage {
  width: 100%;
  height: 100%;
  position: relative;
}
.container {
  width: 100%;
  height: inherit;
  background: @component-background;
  :global {
    .bjs-powered-by {
      visibility: hidden;
    }
  }
}

#buttonGroup {
  position: absolute;
  top: 20px;
  right: 20px;
  border: 1px solid @border-color-split;
  display: flex;
  align-items: center;
  width: 410px;
  height: 40px;
}

#processName {
  position: absolute;
  left: 100px;
  top: 20px;
  width: auto;
  display: flex;
  align-items: center;
  height: 40px;
}

.setting-approval-drawer {
  .title {
    margin-bottom: 8px;
  }

  .title-extra {
    opacity: 0.65;
  }

  .icon {
    opacity: 0.45;
    font-size: 14px;
    margin-left: 4px;
  }
}

.samePersonHandleType {
  label {
    margin-right: 16px;
  }
}
