import React from 'react';

import { fireEvent, render, screen } from '@testing-library/react';

// import userEvent from '@testing-library/user-event';
import { BasicConstructionRequest } from './construction-request.composition';

it('test construction-request', async () => {
  const { getByText } = render(<BasicConstructionRequest />);
  const addBtn = getByText('提 交');
  await fireEvent.click(addBtn);
  const tips = await screen.findAllByText('请输入');
  expect(tips[0]).toBeInTheDocument();
  // const projectInput = await screen.findByLabelText('project: input');
  // await userEvent.type(projectInput, 'yyy');
});
