import React, { useState } from 'react';

import { Input } from '@manyun/base-ui.ui.input';

import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

export type LocationValue = {
  idc?: string;
  block?: string;
};
export type LocationProps = {
  value?: LocationValue;
  onChange?: (value: LocationValue) => void;
};

type RefProps = {};

export const Location = React.forwardRef((props: LocationProps, ref?: React.Ref<RefProps>) => {
  const [idc, setIdc] = useState<string | undefined>(undefined);
  const [block, setBlock] = useState<string | undefined>(undefined);

  const { onChange } = props;

  return (
    <Input.Group>
      <LocationCascader
        style={{
          width: '60%',
        }}
        nodeTypes={['IDC']}
        value={idc ? [idc] : undefined}
        authorizedOnly
        onChange={value => {
          setIdc(value ? (value[0] as string) : undefined);
          setBlock(undefined);
          if (typeof onChange === 'function') {
            onChange({
              idc: value ? (value[0] as string) : undefined,
              block: undefined,
            });
          }
        }}
      />
      <LocationCascader
        style={{
          width: '40%',
        }}
        nodeTypes={['BLOCK']}
        disabled={!idc}
        idc={idc}
        value={block ? [block] : undefined}
        authorizedOnly
        onChange={value => {
          setBlock(value ? (value[0] as string) : undefined);
          if (typeof onChange === 'function') {
            onChange({
              idc,
              block: value ? (value[0] as string) : undefined,
            });
          }
        }}
      />
    </Input.Group>
  );
});

Location.displayName = 'Location';
