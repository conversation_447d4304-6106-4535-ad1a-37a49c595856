@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.constructionRequestForm {
  width: 710px;
  :global(.@{prefixCls}-form-item-label) {
    width: 100px;
  }
}
.constructionRequestTextArea {
  > :global(.@{prefixCls}-input) {
    height: 300px;
  }
}
:global(.@{prefixCls}-space).constructionContainer {
  height: 100%;
  display: flex;

  > :global(.@{prefixCls}-space-item) {
    &:first-child {
      height: 100%;
      overflow-y: auto;
      display: flex;
    }
  }
  .contentDiv {
    height: 100%;
    width: 100%;
    overflow-y: auto;
  }
}
