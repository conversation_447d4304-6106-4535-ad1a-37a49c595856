import template from 'lodash.template';

import type { ConstructionFormJSON } from '@manyun/bpm.service.create-bpm-instance';

const example = {
  ticketContent: {
    area: 'sxdtyg',
    date: ['2021-12-21', '2021-12-22'],
    time: ['07:00', '14:00'],
    phone: '13613765432',
    tools: '什么工具',
    charge: 'yu yu',
    company: '漫云',
    project: '项目名称4',
    peopleNum: 2,
    describe: '33344555',
  },
  hazardousOptTip:
    '1.施工人员施工方须持有标准的施工单方可进入项目园区施工，即该施工单须将施工人员姓名、身份证号码、所属公司、施工地点等真实规范填写，经各部门负责人审核签字后生效。\n  2.施工人员进入园区时必须出示身份证。\n  3.施工人员进入机房楼动力区须戴鞋套，并按要求戴安全帽。\n  4.严禁施工人员携带食品、饮料等进入施工现场。\n  5.严禁施工人员携带与施工项目无关的杂物进入施工现场。\n  6.严禁施工人员在整改施工过程中吸烟、吐痰、乱扔垃圾。\n  7.严禁施工人员在整改施工过程中懒散、娱乐、嬉闹，故意拖延施工进度。\n  8.严禁施工人员在项目园区内随意走动、闲聊。\n  9.严禁施工人员触摸、移动项目园区内各项与施工无关的设备设施，若必须进行移动的，须向跟进人员主管申请，经同意后方可执行。\n  10.施工人员禁止在项目园区内拍照。\n  11.施工人员应当按时完成施工整改，若无法当日完成，则当施工人员需再次施工时，须重新开具并填写标准的施工单，方可进入施工现场。\n  12.施工整改时，须将施工单上的施工内容与现场施工情况进行核实，若有不符，则立刻上报。\n  13.施工人员在施工过程中，须做到成品保护，即不得破坏与施工区域无关的已完善成品区域。\n  14.整改施工完毕后，施工人员须进行完全撤离，将一切无关物品带离现场。\n  15.整改施工完毕后，施工现场不得遗留任何建筑垃圾，并清扫现场卫生。\n  16.整改施工完毕后，由施工方提出施工验收申请，运维部进行施工现场验收，直至合格。\n  17.违反以上条例对施工单内施工负责人一次性罚款 2000 元，并承担对甲方造成的一切相关损失。\n  18.2 米以上高处作业，要求配有安全带、佩戴安全帽。\n  19.施工区域严禁私自进入，允许工作区域活动，进入生产区域需有随工陪同。',
  constructionUsers: [
    {
      id: 'Lt88xtwNs',
      idNo: '110101199003074418',
      name: 'y2',
      work: ['消防系统停运', '能源切断（含水、电、气、空调、网络等）'],
    },
    {
      id: 'Bobno6tTi',
      idNo: '110101199003077635',
      name: 'y3',
      work: ['消防系统停运'],
    },
  ],
  approveRecords: [
    {
      operationName: '测试',
      remark: '统一意见',
    },
  ],
};

export function getPrintTempates({
  data = example,
}: {
  data?: ConstructionFormJSON & {
    approveRecords: {
      operationName: string;
      remark: string;
    }[];
  };
}) {
  if (!data) {
    return ' ';
  }
  const obj = template(
    `<style>
      table {
          text-align: center;
          width: 1000px
      }
      thead > tr {
          height: 55px
      }
      tr > :first-child {
          width: 200px
      }
      .tableName {
          width: 100px
      }
      .hazardousOptTip {
        text-align: left;
      }
  </style>
  <table border="1" cellspacing="0" cellpadding="0" align="center">
      <thead>
          <tr>
              <th colspan="4">数据中心施工申请单</th>
          </tr>
      </thead>
      <tbody>
          <tr>
              <td colspan="1">施工项目</td>
              <td colspan="3"><%- ticketContent.project %></td>
          <tr>
          <tr>
              <td>施工单位</td>
              <td><%- ticketContent.company %></td>
              <td class='tableName'>施工人数</td>
              <td><%- ticketContent.peopleNum %></td>
          </tr>
          <tr>
              <td>施工负责人</td>
              <td><%- ticketContent.charge %></td>
              <td class='tableName'>联系方式</td>
              <td><%- ticketContent.phone %></td>
          </tr>
          <tr>
              <td>施工日期</td>
              <td><%- ticketContent.date[0] %>~<%- ticketContent.date[1] %></td>
              <td class='tableName'>施工时间</td>
              <td>
                  <%- ticketContent.time[0] %>-<%- ticketContent.time[1] %>
              </td>
          </tr>
          <tr>
              <td>施工工具</td>
              <td>
                  <%- ticketContent.tools %>
              </td>
              <td class='tableName'>施工位置</td>
              <td>
                  <%- ticketContent.area %>
              </td>
          </tr>
          <tr>
              <td colspan="4">施工人员姓名及联系方式</td>
          </tr>
          <tr>
              <td>姓名</td>
              <td>身份证号</td>
              <td colspan="2">危险作业</td>
          </tr>
          <% constructionUsers.map((item)=> { %>
              <tr>
                  <td>
                      <%- item.name %>
                  </td>
                  <td>
                      <%- item.idNo %>
                  </td>
                  <td colspan="2">
                      <% if(item.work&&Array.isArray(item.work)){%><%- item.work.join(',') %><%} else  {%><%}  %>
                  </td>
              </tr>
              <% }); %>
                  <tr>
                      <td>说明（施工主要内容）</td>
                      <td colspan="3">
                          <%- ticketContent.describe %>
                      </td>
                  </tr>
                  <% approveRecords.map((item) => { %>
                    <tr>
                    <td><%- item.operationName %></td>
                    <td colspan="3"><%- item.remark %></td>
                </tr>
                    <% }); %>
                  <tr>
                      <td colspan="4">施工注意事项</td>
                  </tr>
                  <tr>
                  <td colspan="4" class='hazardousOptTip'>
                    <% hazardousOptTip.map((item)=> { %>
                        <p><%- item %></p>
                        <% }); %>
                    </td>
                  </tr>
                  <tr>
                      <td></td>
                      <td></td>
                      <td class='tableName'>施工负责人</td>
                      <td>
                      <%- ticketContent.charge %>
                      </td>
                  </tr>
      </tbody>
  </table>
  `
  );
  const content = {
    ...data,
    hazardousOptTip: data.hazardousOptTip?.split('\n').map(item => item.trim()),
  };
  const str = obj(content);
  return str;
}
