import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { QuestionCircleOutlined } from '@ant-design/icons';
import type moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { selectMe } from '@manyun/auth-hub.state.user';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ProcessType } from '@manyun/bpm.service.create-bpm-instance';
import { createAuthRequestAction, selectCreateAuthRequest } from '@manyun/bpm.state.auth-request';
import { ConstructionUsers } from '@manyun/bpm.ui.construction-users';
import type { ConstructionUser } from '@manyun/bpm.ui.construction-users';

import { Announcements, DEFAULT_NOTES } from './construction-request-announcements';
import { Location } from './construction-request-location';
import styles from './construction-request.module.less';

export type DataSource = {
  name: string;
  idNo: string;
  work: string[];
  id: string | number;
};

export function ConstructionRequest() {
  const [dataSource, setDataSource] = React.useState<ConstructionUser[]>([]);
  const [editingRowKey, setEditingRowKey] = React.useState<React.Key[]>([]);
  const [form] = Form.useForm();
  const history = useHistory();
  const dispatch = useDispatch();
  const { userId, name } = useSelector(selectMe);
  const { loading } = useSelector(selectCreateAuthRequest);

  React.useEffect(() => {
    form.setFieldsValue({
      notes: DEFAULT_NOTES,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleSubmit = useCallback(() => {
    form
      .validateFields()
      .then(values => {
        if (!dataSource.length) {
          message.error('请添加施工人员！');
          return;
        }
        if (editingRowKey.length > 0) {
          message.error('请先保存施工人员数据！');
          return;
        }

        const { project, company, tools, charge, phone, date, time, area, notes, describe } =
          values;

        const params = {
          project: project.trim(),
          company: company.trim(),
          tools: tools.trim(),
          charge: charge.trim(),
          phone: phone.trim(),
          date: [date[0].format('YYYY-MM-DD'), date[1].format('YYYY-MM-DD')] as [string, string],
          time: [time[0].format('HH:mm'), time[1].format('HH:mm')] as [string, string],
          area: area.block,
          peopleNum: dataSource.length,
          describe: describe.trim(),
        };

        dispatch(
          createAuthRequestAction({
            type: ProcessType.CONSTRUCTION_APPLY,
            title: `${name}发起施工作业申请`,
            applyId: userId!,
            applyName: name!,
            detailParam: params,
            formJSON: {
              hazardousOptTip: notes?.trim(),
              constructionUsers: dataSource,
              ticketContent: params,
            },
            idcTag: area.idc,
            blockGuid: area.block,
            reason: params.describe,
            callback: res => {
              if (res) {
                message.success('申请成功！');
                history.push(generateBPMRoutePath({ id: res }));
              }
            },
          })
        );
      })
      .catch(err => {
        form.scrollToField(err.errorFields[0].name.toString());
      });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, form, dataSource, editingRowKey]);

  return (
    <Space direction="vertical" className={styles.constructionContainer}>
      <Space direction="vertical" className={styles.contentDiv}>
        <Card title="基本信息">
          <Form form={form} colon={false} className={styles.constructionRequestForm}>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="施工项目"
                  name="project"
                  required
                  rules={[
                    { required: true, whitespace: true, message: '请输入' },
                    { max: 40, message: '最多输入 40 个字符！' },
                  ]}
                >
                  <Input aria-label="project: input" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="施工单位"
                  name="company"
                  required
                  rules={[
                    { required: true, whitespace: true, message: '请输入' },
                    { max: 20, message: '最多输入 20 个字符！' },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="施工工具"
                  name="tools"
                  required
                  rules={[
                    { required: true, whitespace: true, message: '请输入' },
                    { max: 20, message: '最多输入 20 个字符！' },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="施工负责人"
                  name="charge"
                  required
                  rules={[
                    { required: true, whitespace: true, message: '请输入' },
                    { max: 10, message: '最多输入 10 个字符！' },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="联系方式"
                  name="phone"
                  required
                  rules={[
                    { required: true, whitespace: true, message: '请输入' },
                    {
                      pattern: /^1[3456789]\d{9}$/,
                      message: '请输入正确联系方式',
                    },
                    { max: 20, message: '最多输入 20 个字符！' },
                  ]}
                >
                  <Input />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={
                    <>
                      施工日期&nbsp;
                      <Tooltip title="同一区域施工单最长有效时间为3天">
                        <QuestionCircleOutlined />
                      </Tooltip>
                    </>
                  }
                  name="date"
                  required
                  rules={[
                    () => ({
                      validator(_, value) {
                        if (!value) {
                          return Promise.reject('请选择');
                        }
                        const rangeDate = value.map((m: moment.Moment) =>
                          m.clone().startOf('day').valueOf()
                        );
                        if (rangeDate[1] - rangeDate[0] > 3 * 24 * 60 * 60 * 1000) {
                          return Promise.reject('请选择正确的施工日期');
                        }
                        return Promise.resolve();
                      },
                    }),
                  ]}
                >
                  <DatePicker.RangePicker />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="施工时间"
                  name="time"
                  required
                  rules={[{ required: true, message: '请选择' }]}
                >
                  <TimePicker.RangePicker format="HH:mm" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="施工位置"
                  name="area"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || !value?.idc) {
                          return Promise.reject('请先选择机房');
                        }
                        if (!value.block) {
                          return Promise.reject('请选择楼栋');
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Location />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="说明"
                  name="describe"
                  required
                  rules={[
                    { required: true, whitespace: true, message: '请输入' },
                    {
                      type: 'string',
                      max: 100,
                      message: '最多输入 100 个字符！',
                    },
                  ]}
                >
                  <Input.TextArea placeholder="输入施工维修整改主要内容" />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="注意事项" name="notes">
                  <Announcements />
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </Card>
        <Card title="施工人员">
          <Space direction="vertical" style={{ width: '100%' }}>
            <ConstructionUsers
              mode="edit"
              value={dataSource}
              editableKeys={editingRowKey}
              setEditableRowKeys={setEditingRowKey}
              onChange={setDataSource}
            />
          </Space>
        </Card>
      </Space>
      <Card bordered={false} bodyStyle={{ padding: '15px', textAlign: 'center' }}>
        <Space direction="horizontal" align="center">
          <Button
            type="primary"
            loading={loading}
            onClick={() => {
              handleSubmit();
            }}
          >
            提交
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </Card>
    </Space>
  );
}
