import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { getPrintTempates } from './construction-print-template';
import { ConstructionRequest } from './construction-request';

export const BasicConstructionRequest = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // destroyMock();
    // webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    const mockOff = useRemoteMock('web');
    update(true);
    return () => {
      mockOff();
    };
  }, []);
  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <ConstructionRequest />
      </FakeStore>
    </ConfigProvider>
  );
};

export function PrintFile() {
  return (
    <Button
      onClick={() => {
        const contents = getPrintTempates({});
        const a = window.open('', '');
        a?.document.write(contents);
        a?.document.close();
        a?.print();
      }}
      type="primary"
    >
      打印
    </Button>
  );
}
