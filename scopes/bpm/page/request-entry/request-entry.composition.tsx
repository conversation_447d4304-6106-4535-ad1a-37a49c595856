import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';

import {
  AUTH_REQUEST_CREATOR_ROUTE_PATH,
  REQUEST_RNTRY_ROUTE_PATH,
} from '@manyun/bpm.route.bpm-routes';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  ALTER_INFO_NEW_ROUTE_PATH,
  SUPPLY_CHECK_NEW_ROUTE_PATH,
} from '@manyun/hrm.route.hrm-routes';

import { RequestEntry } from './request-entry';

const MockPage = () => {
  const history = useHistory();
  return (
    <>
      <span>排班调整页面</span>
      <Button
        onClick={() => {
          history.goBack();
        }}
      >
        Go Back
      </Button>
    </>
  );
};

const MockPage2 = () => {
  const history = useHistory();
  return (
    <>
      <span>考勤补卡页面</span>
      <Button
        onClick={() => {
          history.goBack();
        }}
      >
        Go Back
      </Button>
    </>
  );
};

const MockPage3 = () => {
  const history = useHistory();
  return (
    <>
      <span>权限申请页面</span>
      <Button
        onClick={() => {
          history.goBack();
        }}
      >
        Go Back
      </Button>
    </>
  );
};

export const BasicRequestEntry = () => {
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[REQUEST_RNTRY_ROUTE_PATH]}>
          <Switch>
            <Route path={REQUEST_RNTRY_ROUTE_PATH}>
              <RequestEntry />
            </Route>
            <Route path={ALTER_INFO_NEW_ROUTE_PATH}>
              <MockPage />
            </Route>
            <Route path={SUPPLY_CHECK_NEW_ROUTE_PATH}>
              <MockPage2 />
            </Route>
            <Route path={AUTH_REQUEST_CREATOR_ROUTE_PATH}>
              <MockPage3 />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
