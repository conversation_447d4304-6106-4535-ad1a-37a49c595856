import React from 'react';

import { fireEvent, render } from '@testing-library/react';

import { BasicRequestEntry } from './request-entry.composition';

it('should render with the correct text', () => {
  const { getByText } = render(<BasicRequestEntry />);
  const rendered = getByText('排班调整申请');
  expect(rendered).toBeTruthy();
});

it('should render with the correct text by click 排班调整申请', () => {
  const { getByText } = render(<BasicRequestEntry />);
  const rendered = getByText('考勤(2)');
  expect(rendered).toBeTruthy();
  const btn1 = getByText('排班调整申请');

  expect(btn1).toBeInTheDocument();
  fireEvent.click(btn1);

  const gobackBtn = getByText('Go Back');
  expect(gobackBtn).toBeInTheDocument();
  expect(getByText('排班调整页面')).toBeInTheDocument();
});

it('should render with the correct text by click 考勤补卡申请', () => {
  const { getByText } = render(<BasicRequestEntry />);
  const rendered = getByText('考勤(2)');
  expect(rendered).toBeTruthy();
  const btn1 = getByText('考勤补卡申请');

  expect(btn1).toBeInTheDocument();
  fireEvent.click(btn1);

  const gobackBtn = getByText('Go Back');
  expect(gobackBtn).toBeInTheDocument();
  expect(getByText('考勤补卡页面')).toBeInTheDocument();
});

it('should render with the correct text by click 权限申请', () => {
  const { getByText } = render(<BasicRequestEntry />);
  const rendered = getByText('权限(1)');
  expect(rendered).toBeTruthy();
  const btn1 = getByText('权限申请');

  expect(btn1).toBeInTheDocument();
  fireEvent.click(btn1);

  const gobackBtn = getByText('Go Back');
  expect(gobackBtn).toBeInTheDocument();

  expect(getByText('权限申请页面')).toBeInTheDocument();
});
