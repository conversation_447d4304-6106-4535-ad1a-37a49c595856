import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import {
  CostCenterOutlined,
  GlobeOutlined,
  KeyOutlined,
  UserSettingsOutlined,
} from '@manyun/base-ui.icons';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { message } from '@manyun/base-ui.ui.message';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import {
  AUTH_REQUEST_CREATOR_ROUTE_PATH,
  CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH,
  NEW_COMMON_REQUEST_ROUTE_PATH,
  OUT_TO_REGULAR_REQUEST_ROUTE_PATH,
} from '@manyun/bpm.route.bpm-routes';
import { RequestEntryCard } from '@manyun/bpm.ui.request-entry-card';
import {
  ALTER_INFO_NEW_ROUTE_PATH,
  GO_OUT_REQUEST_CREATOR_ROUTE_PATH,
  SUPPLY_CHECK_NEW_ROUTE_PATH,
} from '@manyun/hrm.route.hrm-routes';
import { fetchUserAttendanceGroups } from '@manyun/hrm.service.fetch-user-attendance-groups';

import styles from './request-entry.module.less';

export type RequestEntryProps = {};

export type Request = {
  id: string | number;
  name: string;
  link?: string;
  icon?: React.ReactNode;
  /**是否有权限 */
  check?: boolean;
  children?: Request[];
  /**是否可点击*/
  linkable?: () => Promise<boolean>;
  /**跳转目标是否在当前站点外 */
  isNotNative?: boolean;
};

export function RequestEntry() {
  const [, { checkCode }] = useAuthorized();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const requestEntryMapData: Request[] = useMemo(
    () => [
      {
        id: '1',
        name: '考勤',
        children: [
          {
            id: 1,
            name: '排班调整申请',
            link: ALTER_INFO_NEW_ROUTE_PATH,
            icon: <IconContainer children={<UserSettingsOutlined />} />,
            check: true,
            linkable: () => {
              return isSupportAttStatisticPermission(userId, '排班调整');
            },
          },
          {
            id: 2,
            name: '考勤补卡申请',
            link: SUPPLY_CHECK_NEW_ROUTE_PATH,
            icon: <IconContainer children={<UserSettingsOutlined />} />,
            check: true,
            linkable: () => {
              return isSupportAttStatisticPermission(userId, '补卡申请');
            },
          },
          {
            id: 3,
            name: '外勤申请',
            link: GO_OUT_REQUEST_CREATOR_ROUTE_PATH,
            icon: <IconContainer children={<UserSettingsOutlined />} />,
            check: true,
            linkable: () => {
              return isSupportAttStatisticPermission(userId, '外勤申请');
            },
          },
        ],
      },
      {
        id: '2',
        name: '管理',
        children: [
          {
            id: 4,
            name: '施工作业申请',
            link: CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH,
            icon: (
              <IconContainer
                background={`var(--${prefixCls}-success-color-active)`}
                children={<GlobeOutlined />}
              />
            ),
            check: checkCode('element_construction-request--card'),
          },
          {
            id: 5,
            name: '日常通用申请',
            link: NEW_COMMON_REQUEST_ROUTE_PATH,
            icon: (
              <IconContainer
                background={`var(--${prefixCls}-success-color-active)`}
                children={<GlobeOutlined />}
              />
            ),
            check: checkCode('element_new-common-request'),
          },
          {
            id: 6,
            name: '外包转正申请',
            link: OUT_TO_REGULAR_REQUEST_ROUTE_PATH,
            icon: (
              <IconContainer
                background={`var(--${prefixCls}-success-color-active)`}
                children={<GlobeOutlined />}
              />
            ),
            check: true,
          },
        ],
      },
      {
        id: '3',
        name: '权限',
        children: [
          {
            id: 3,
            name: '权限申请',
            link: AUTH_REQUEST_CREATOR_ROUTE_PATH,
            icon: (
              <IconContainer
                background={`var(--${prefixCls}-warning-color)`}
                children={<KeyOutlined />}
              />
            ),
            check: true,
          },
        ],
      },
      {
        id: '4',
        name: '销售',
        children: [
          {
            id: 1,
            name: '报价申请',
            link: 'page/crm/sales-quote/create',
            icon: (
              <IconContainer
                background={`var(--${prefixCls}-primary-7)`}
                children={<CostCenterOutlined />}
              />
            ),
            check: checkCode('element_sale-quote-entry'),
            isNotNative: true,
          },
        ],
      },
    ],
    [checkCode, userId]
  );

  const [activeKey, setActiveKey] = useState<string[] | string>(
    requestEntryMapData.map(entry => entry.id) as string[]
  );

  return (
    <div className={styles.requestEntryContainer}>
      <Collapse
        bordered={false}
        activeKey={activeKey}
        expandIconPosition="left"
        onChange={key => setActiveKey(key)}
      >
        {requestEntryMapData.map(entry => {
          const children = (entry?.children ?? []).filter(child => child.check === true);
          if (children.length > 0) {
            return (
              <Collapse.Panel
                key={entry.id}
                style={{ paddingTop: 0, paddingBottom: 0 }}
                header={
                  <div style={{ display: 'flex', alignItems: 'center' }}>
                    <div className={styles.collaspeTitle} />
                    {entry.name}({children.length})
                  </div>
                }
              >
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill,minmax(185px,1fr))',
                    gridRowGap: '8px',
                    gridColumnGap: '8px',
                  }}
                >
                  {children.map(child => (
                    <div key={child.name}>
                      <RequestEntryCard
                        key={child.name}
                        title={child.name}
                        linkTo={child.link ?? ''}
                        icon={child.icon}
                        linkable={child?.linkable}
                        isNotNative={child.isNotNative}
                      />
                    </div>
                  ))}
                </div>
              </Collapse.Panel>
            );
          }
          return <></>;
        })}
      </Collapse>
    </div>
  );
}

function IconContainer({
  background,
  children,
}: {
  background?: string;
  children: React.ReactNode;
}) {
  return (
    <div className={styles.iconContainer} style={{ background: background, fontSize: '16px' }}>
      {children}
    </div>
  );
}

async function isSupportAttStatisticPermission(userId: number | null, title: string) {
  if (!userId) {
    return false;
  }
  const { error, data } = await fetchUserAttendanceGroups({ staffId: userId! });
  if (error) {
    message.error(error?.message);
    return false;
  }
  if (data === null) {
    message.warning(`您不在任何考勤组中，无法提交${title}`);
    return false;
  }
  return true;
}
