import { qs } from '@manyun/base-ui.util.query-string';

export type ApprovalListRouteParams = {
  /** 审批类型 */
  bizType?: string;
  /** 表示审批状态 */
  status?: 'ALL' | 'COMPLETE' | 'APPROVING' | 'REVOKE' | 'PASS';
  /** 分别代表审批列表的tab页：我发起的、我收到的、我已处理、待我审批 */
  type?: 'MINE' | 'CC' | 'ALREADY' | 'WAIT';
};

export const BPM_INSTANCES_ROUTE_PATH = '/page/approve-center/list';
export const generateApprovalListRoutePath = (params: ApprovalListRouteParams) => {
  return `${BPM_INSTANCES_ROUTE_PATH}?${qs.stringify(params)}`;
};
/** 废弃的原审批详情链接 */
export const DEPRECATED_BPM_INSTANCE_ROUTE_PATH = '/page/approve-center/detail/:id';
export const BPM_INSTANCE_ROUTE_PATH = '/page/approve-center/detail';
export type BpmInstanceDetailRouteParams = {
  /** 当前审批单号 */
  id: string;
  /** 关联审批单号 */
  relateId?: string;
};
export const generateBPMRoutePath = (params: BpmInstanceDetailRouteParams) =>
  `${BPM_INSTANCE_ROUTE_PATH}?${qs.stringify(params)}`;
/** 权限申请 **/
export const AUTH_REQUEST_CREATOR_ROUTE_PATH = '/page/bpm/auth-request-creator';
/** 通用审批 */
export const REQUEST_RNTRY_ROUTE_PATH = '/page/bpm/request-entry';
/** 施工作业申请 */
export const CONSTRUCTION_REQUEST_CREATOR_ROUTE_PATH = '/page/bpm/construction-request-creator';
/** 日常通用申请 */
export const NEW_COMMON_REQUEST_ROUTE_PATH = '/page/bpm/common-request/new';
export const NEW_COMMON_REQUEST_ROUTE_AUTH_CODE = 'page_bpm_new-common-request';

/** 外包转正申请 */
export const OUT_TO_REGULAR_REQUEST_ROUTE_PATH = '/page/bpm/out-to-regular-request/new';

export const OUT_TO_REGULAR_REQUEST_ROUTE_AUTH_CODE = 'page_new_out-to-regular-request';

/** 新增 BPM */
export const NEW_BPM_ROUTE_PATH = '/page/bpm/new';
export const EDIT_BPM_ROUTE_PATH = '/page/bpm/:code/edit';
//export const BPMS_ROUTE_PATH = '/page/approval-config-process/list';
export const BPM_ROUTE_PATH = '/page/approval-config-process/detail/:processCode';
export const BPM_RELATIONSHIPS_ROUTE_PATH = '/page/approval-scenes/list';
export const APPROVAL_PROCESS_CONFIG_DETAIL = '/page/approval-config-process/detail/:processCode';
export const APPROVAL_SCENES_LIST = '/page/approval-scenes/list';

export const generateBpmEditUrl = ({ processCode }: { processCode: string }) =>
  EDIT_BPM_ROUTE_PATH.replace(':code', processCode);
export const generateApprovalProcessConfigDetailUrl = ({ processCode }: { processCode: string }) =>
  APPROVAL_PROCESS_CONFIG_DETAIL.replace(':processCode', processCode);
export const generateSceneConfigListUrl = ({ subBizScenes }: { subBizScenes: string }) => ({
  pathname: APPROVAL_SCENES_LIST,
  search: `?subBizScenes=${subBizScenes}`,
});
