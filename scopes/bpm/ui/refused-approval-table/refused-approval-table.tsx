import React from 'react';
import { Link } from 'react-router-dom';

import dayjs from 'dayjs';

import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import type { RefusedRecord } from '@manyun/bpm.service.fetch-bpm-refused-records';

export type RefusedApprovalTableProps = Omit<
  TableProps<RefusedRecord>,
  'columns' | 'dataSource'
> & {
  dataSource: RefusedRecord[];
  parentId: string;
};

export function RefusedApprovalTable({ dataSource, parentId, ...rest }: RefusedApprovalTableProps) {
  const columns: Array<ColumnType<RefusedRecord>> = [
    {
      title: '审批ID',
      width: 264,
      dataIndex: 'instId',
      render: (_, { instId }) => (
        <Link target="_blank" to={generateBPMRoutePath({ id: instId, relateId: parentId })}>
          {instId}
        </Link>
      ),
    },
    {
      title: '驳回原因',
      width: 627,
      dataIndex: 'refuseReason',
      render: (_, { refuseReason }) => (
        <Typography.Text ellipsis={{ tooltip: true }}>{refuseReason ?? '--'}</Typography.Text>
      ),
    },
    {
      title: '驳回节点',
      width: 217,
      dataIndex: 'refuseNode',
      render: (_, { refuseNode }) => (
        <Typography.Text ellipsis={{ tooltip: true }}>{refuseNode ?? '--'}</Typography.Text>
      ),
    },
    {
      title: '审批人',
      width: 181,
      dataIndex: 'userId',
      render: (_, { userId, userName }) => (
        <UserLink target="_blank" userId={userId} userName={userName} />
      ),
    },
    {
      title: '驳回时间',
      width: 236,
      dataIndex: 'refuseTime',
      render: (_, { refuseTime }) => dayjs(refuseTime).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];
  return (
    <Table
      rowKey="instId"
      style={{ width: '100%' }}
      scroll={{ y: 260 }}
      columns={columns}
      dataSource={dataSource}
      pagination={false}
      {...rest}
    />
  );
}
