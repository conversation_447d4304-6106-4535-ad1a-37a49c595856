import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { RequestEntryCard } from './request-entry-card';

export const BasicRequestEntryCard = () => (
  <ConfigProvider>
    <Router initialEntries={['']}>
      <Switch>
        <Route>
          <RequestEntryCard title="排班调整申请审批" icon={<span>图标</span>} linkTo="123" />
        </Route>
      </Switch>
    </Router>
  </ConfigProvider>
);
