import React from 'react';
import { useHistory } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { useApps } from '@manyun/dc-brain.context.apps';

export type RequestEntryCardProps = {
  title: string;
  icon: React.ReactNode;
  linkTo: string;
  linkable?: () => Promise<boolean> | boolean;
  isNotNative?: boolean;
};

export function RequestEntryCard({
  title,
  icon,
  linkTo,
  linkable,
  isNotNative,
}: RequestEntryCardProps) {
  const history = useHistory();
  // @ts-ignore: because of incorrect TS types
  const { sales } = useApps();

  return (
    <Card
      bordered={false}
      bodyStyle={{
        padding: '12px',
        background: 'var(--manyun-bg-light)',
        borderRadius: 'var(--manyun-border-radius-base)',
        display: 'flex',
        alignItems: 'center',
        cursor: 'pointer',
      }}
      onClick={async () => {
        let hasPermission: boolean = true;
        if (linkable) {
          hasPermission = await linkable();
        }
        if (hasPermission) {
          if (isNotNative) {
            window.location.assign(`${sales.baseURL}${linkTo}`);
          } else {
            history.push(linkTo);
          }
        }
      }}
    >
      <Space direction="horizontal">
        <div>{icon}</div>
        <div>{title}</div>
      </Space>
    </Card>
  );
}
