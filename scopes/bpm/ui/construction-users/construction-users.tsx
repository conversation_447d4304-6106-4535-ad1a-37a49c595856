import React, { useMemo, useRef } from 'react';

import { nanoid } from 'nanoid';

import { Button } from '@manyun/base-ui.ui.button';
import type { ActionType, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Form } from '@manyun/base-ui.ui.form';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import { isIdCard } from './util';

const HAZARDOUS_OPERATION = [
  '危险化学品使用（油漆及重气味作业）',
  '动土作业',
  '吊装作业',
  '密闭空间作业',
  '动火作业',
  '高空作业（≥2 米）',
  '能源切断（含水、电、气、空调、网络等）',
  '消防系统停运',
  '临时用电申请',
  '防水作业',
];

export type ConstructionUser = {
  name: string;
  idNo: string;
  work: string[];
  id: string | number;
};

export type ConstructionUsersProps = {
  mode?: 'edit' | 'preview';
  value?: ConstructionUser[];
  onChange?: (dataSource: ConstructionUser[]) => void;
  editableKeys?: React.Key[];
  setEditableRowKeys?: (keys: React.Key[]) => void;
  encryptIdNumber?: boolean;
};

export function ConstructionUsers({
  mode = 'preview',
  value,
  onChange,
  editableKeys,
  setEditableRowKeys,
  encryptIdNumber = false,
}: ConstructionUsersProps) {
  const actionRef = useRef<ActionType>();
  const [form] = Form.useForm();

  const columns = useMemo(() => {
    const baiscColumns: ProColumns<ConstructionUser>[] = [
      {
        width: 300,
        title: '姓名',
        dataIndex: 'name',
        formItemProps: {
          rules: [
            { required: true, message: '请输入', whitespace: true },
            { max: 10, message: '最多输入 10 个字符！' },
          ],
        },
      },
      {
        width: 300,
        title: '身份证号',
        dataIndex: 'idNo',
        formItemProps: {
          rules: [
            {
              whitespace: true,

              validator: async (_, val) => {
                if (!isIdCard(val)) {
                  return Promise.reject(new Error('请输入正确身份证号'));
                }
                return Promise.resolve();
              },
            },
          ],
        },
        render: (_, { idNo }) => {
          return encryptIdNumber ? idNo.slice(0, 3) + '***********' + idNo.slice(-4) : idNo;
        },
      },
      {
        title: (
          <Explanation
            iconType="question"
            tooltip={{ title: '请选择涉及临时用电、动火等的危险作业。' }}
          >
            危险作业
          </Explanation>
        ),
        dataIndex: 'work',
        renderFormItem: () => (
          <Select
            mode="multiple"
            options={HAZARDOUS_OPERATION.map(opt => ({ label: opt, value: opt }))}
          />
        ),
        render: (_, { work }) => (work ?? []).join('、'),
      },
    ];
    if (mode === 'edit') {
      baiscColumns.push({
        title: '操作',
        valueType: 'option',
        width: 140,
        fixed: 'right',
        render: (text, record, _, action) => [
          <Button
            key="editable"
            type="link"
            compact
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </Button>,
          <Button
            key="delete"
            type="link"
            compact
            onClick={() => {
              onChange?.((value ?? []).filter(item => item.id !== record.id));
            }}
          >
            删除
          </Button>,
        ],
      });
    }
    return baiscColumns;
  }, [encryptIdNumber, mode, onChange, value]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {mode === 'edit' && (
        <Button
          type="primary"
          onClick={() => {
            actionRef.current?.addEditRecord?.({
              id: nanoid(),
            });
          }}
        >
          添加
        </Button>
      )}
      <EditableProTable
        actionRef={actionRef}
        rowKey="id"
        size="small"
        scroll={{ x: 'max-content' }}
        recordCreatorProps={false}
        columns={columns}
        value={value}
        editable={{
          form,
          editableKeys: editableKeys,
          onChange: setEditableRowKeys,
          actionRender: (_, __, dom) => [dom.save, dom.cancel],
        }}
        pagination={mode === 'preview' ? { pageSize: 10, current: 1 } : false}
        onChange={onChange}
      />
    </Space>
  );
}
