import React, { useCallback, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BpmProcessUserInfo } from '@manyun/bpm.gql.client.approval';

type FiltersFormValues = { searchName?: string };

export function BpmUserSelect({
  userList,
  selectedKeys,
  onChange,
}: {
  userList: BpmProcessUserInfo[];
  selectedKeys: React.Key[];
  onChange: (selectedKeys: React.Key[]) => void;
}) {
  const [tableData, setTableData] = useState(userList);
  const [form] = Form.useForm<FiltersFormValues>();
  const [visible, setVisible] = useState(false);
  const [{ page, pageSize }, setPagination] = useState({ page: 1, pageSize: 10 });
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>(selectedKeys);

  useShallowCompareEffect(() => {
    setSelectedRowKeys(selectedKeys);
  }, [selectedKeys]);

  const handleVisibleChange = useCallback(() => {
    setVisible(prev => !prev);
  }, []);

  return (
    <>
      <Modal
        width={480}
        title="添加审批人"
        open={visible}
        okText="提交"
        onOk={() => {
          handleVisibleChange();
          onChange(selectedRowKeys);
        }}
        onCancel={handleVisibleChange}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <Form form={form}>
            <Form.Item name="searchName" noStyle>
              <Input.Search
                style={{ width: 216 }}
                onSearch={value => {
                  setPagination({ page: 1, pageSize: 10 });
                  setTableData(
                    userList.filter(({ userName }) =>
                      value ? userName.includes(value.trim()) : true
                    )
                  );
                }}
              />
            </Form.Item>
          </Form>
          <Table<BpmProcessUserInfo>
            rowKey="userId"
            tableLayout="fixed"
            scroll={{ x: 'max-content' }}
            size="small"
            columns={[
              {
                title: '人员',
                dataIndex: 'userName',
                render: (userName, { recommend, isDuty }) => {
                  return (
                    <>
                      <Typography.Text style={{ paddingRight: 8 }}>{userName}</Typography.Text>
                      {recommend && <Tag color="warning">推荐</Tag>}
                      {isDuty && <Tag color="success">在岗</Tag>}
                    </>
                  );
                },
              },
            ]}
            dataSource={tableData}
            rowSelection={{
              selectedRowKeys: selectedRowKeys,
              onChange: selectedRowKeys => {
                setSelectedRowKeys(keys => [
                  ...(keys ?? []).filter(key => !tableData.find(({ userId }) => userId === key)),
                  ...selectedRowKeys,
                ]);
              },
            }}
            pagination={{
              current: page,
              pageSize,
              showSizeChanger: false,
              onChange(page, pageSize) {
                setPagination({ page, pageSize });
              },
            }}
          />
        </Space>
      </Modal>
      <Button
        type="dashed"
        onClick={() => {
          handleVisibleChange();
        }}
      >
        添加审批人
      </Button>
    </>
  );
}
