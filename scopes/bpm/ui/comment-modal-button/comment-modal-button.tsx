import { UploadOutlined } from '@ant-design/icons';
import React, { useMemo, useRef, useState } from 'react';

import { UserMentions } from '@manyun/auth-hub.ui.user-mentions';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { addComment } from '@manyun/bpm.service.add-comment';
import { useApps } from '@manyun/dc-brain.context.apps';
import { Link } from '@manyun/dc-brain.navigation.link';
import { McUpload } from '@manyun/dc-brain.ui.upload';

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

export type CommentModalButtonProps = {
  /** 审批Id */
  instId: string;
  /** 发起人Id */
  applyUserId: number;
  /** 回调函数 */
  onSuccess?: () => void;
  style?: React.CSSProperties;
  /** 按钮使用的场景 */
  useCase?: 'default' | 'businessOrder';
  // 操作成功时额外部分的提示文案
  extraSuccessMessage?: string;
  /** 按钮使用场景是否需要标注仅对当前审批生效 */
  isOuterApprovalSingle?: boolean;
  onClick?: () => void;
} & Omit<ButtonProps, 'onClick'>;

export type FormType = {
  content: string;
  fileInfoList: Record<string, any>[];
};

type CommentData = {
  comment: string;
  users: { id: number; name: string }[];
};

export function CommentModalButton({
  instId,
  applyUserId,
  onSuccess,
  style,
  useCase = 'default',
  extraSuccessMessage,
  isOuterApprovalSingle,
  onClick,
  ...rest
}: CommentModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const locales = useMemo(() => getBpmInstanceLocales(), []);
  const usersRef = useRef<CommentData>({ users: [], comment: '' });
  const [form] = Form.useForm<FormType>();
  const { dcbase } = useApps();
  const handleSubmit = () => {
    form.validateFields().then(async value => {
      const { comment, users } = usersRef.current;
      const fileInfoList = value?.fileInfoList;

      const { error } = await addComment({
        instId,
        content: comment,
        // 如果没有艾特人，通知发给申请人
        ccUserIdList: users.length > 0 ? users.map(user => user.id) : [applyUserId],
        fileInfoList: fileInfoList?.map((value: Record<string, any>) => ({
          fileName: value?.name,
          filePath: value?.patialPath,
          fileFormat: value?.ext,
          uploadTime: value?.lastModified,
        })),
      });

      setLoading(false);
      if (error) {
        message.error(`评论提交失败,${error.message}`);
        return;
      }
      message.success(extraSuccessMessage ? `评论成功，${extraSuccessMessage}` : '评论成功');
      setVisible(false);
      onSuccess && onSuccess();
    });
  };
  return (
    <>
      <Button
        style={style}
        {...rest}
        onClick={() => {
          onClick?.();
          setVisible(true);
        }}
      >
        {useCase === 'default' ? locales.comment.__self : locales.comment.inBusinessOrder}
      </Button>
      <Modal
        title={useCase === 'default' ? locales.comment.__self : locales.comment.inBusinessOrder}
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        confirmLoading={loading}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          {typeof isOuterApprovalSingle === 'boolean' && !isOuterApprovalSingle && (
            <Alert
              showIcon
              type="info"
              banner
              message={
                <Typography.Text>
                  评论仅对最新审批单
                  <Link
                    target="_blank"
                    external
                    href={`${dcbase.baseURL}${generateBPMRoutePath({ id: instId }).replace(/^\/+/, '')}`}
                  >
                    {instId}
                  </Link>
                  生效
                </Typography.Text>
              }
            />
          )}
          <Form form={form} {...layout}>
            <Form.Item
              name="content"
              label="评论"
              rules={[
                {
                  required: true,
                  message: '评论内容必填',
                },
                {
                  max: 500,
                  message: '最多输入 500 个字符！',
                },
              ]}
            >
              <UserMentions
                style={{ height: 98 }}
                placeholder={locales.comment.placeholder}
                onChange={(_value, valueForDisplay, users) => {
                  usersRef.current = {
                    comment: valueForDisplay,
                    users: users,
                  };
                }}
              />
            </Form.Item>
            <Form.Item
              label="附件"
              name="fileInfoList"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
              extra="支持扩展名：.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx"
            >
              <McUpload key="upload" accept="image/*,.xls,.xlsx,.doc,.docx" maxFileSize={50}>
                <Button icon={<UploadOutlined />}>上传附件</Button>
              </McUpload>
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
}
