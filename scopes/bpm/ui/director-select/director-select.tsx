import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { Metadata } from '@manyun/resource-hub.model.metadata';

export type DirectorSelectProps<VT = any> = {
  trigger?: 'onFocus' | 'onDidMount';
  onDidReceiveData?: (data: Metadata[]) => void;
} & SelectProps<VT>;

export const DirectorSelect = React.forwardRef(
  (
    { trigger, onDidReceiveData, ...selectProps }: DirectorSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ data }, { readMetaData }] = useMetaData(MetaType.APPROVAL_DIRECTOR);
    React.useEffect(() => {
      readMetaData({ callback: onDidReceiveData });
    }, [onDidReceiveData, readMetaData]);

    const variableSelectData = useMemo(() => {
      return data?.data?.map(item => ({ value: item.value, label: item.label }));
    }, [data]);

    return (
      <Select
        ref={ref}
        showSearch
        optionLabelProp="label"
        optionFilterProp="label"
        {...selectProps}
        options={variableSelectData}
      />
    );
  }
);

DirectorSelect.displayName = 'DirectorSelect';
