import React, { useMemo } from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';

import { ApprovalStatus, getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';

export type ApprovalStatusTextProps = {
  approveStatus: ApprovalStatus;
};
export function ApprovalStatusText({ approveStatus }: ApprovalStatusTextProps) {
  const locales = useMemo(() => getBpmInstanceLocales(), []);

  const type = useMemo(() => {
    switch (approveStatus) {
      case ApprovalStatus.Approving:
        return 'processing';
      case ApprovalStatus.Pass:
        return 'success';

      case ApprovalStatus.Refuse:
        return 'error';
      default:
        return undefined;
    }
  }, [approveStatus]);

  return <Tag color={type}>{locales.approvalStatus[approveStatus]}</Tag>;
}
