import React from 'react';

import { BpmViewer } from './bpm-viewer';

const xml = `<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<definitions xmlns="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:activiti="http://activiti.org/bpmn" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:tns="http://www.activiti.org/testm1642385801933" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" expressionLanguage="http://www.w3.org/1999/XPath" id="m1642385801933" name="" targetNamespace="http://www.activiti.org/testm1642385801933" typeLanguage="http://www.w3.org/2001/XMLSchema">
  <process id="WF1000000500703034" isClosed="false" isExecutable="true" name="testGateway" processType="None">
    <startEvent id="_2" name="StartEvent"/>
    <userTask activiti:assignee="1" activiti:exclusive="true" id="_3" name="A楼楼长审批">
      <extensionElements>
        <activiti:taskListener event="create" delegateExpression="\${taskStartListener}"></activiti:taskListener>
      </extensionElements>
    </userTask>
    <exclusiveGateway gatewayDirection="Unspecified" id="_4" name="ExclusiveGateway"/>
    <userTask activiti:assignee="5" activiti:exclusive="true" id="_5" name="其它人员审批">
     <extensionElements>
        <activiti:taskListener event="create" delegateExpression="\${taskStartListener}"></activiti:taskListener>
      </extensionElements>
    </userTask>
    <endEvent id="_6" name="EndEvent">
      <extensionElements>
        <activiti:executionListener delegateExpression="\${processFinishListener}" event="start"/>
      </extensionElements>
    </endEvent>
    <sequenceFlow id="_7" name="A楼楼长审批" sourceRef="_4" targetRef="_3">
      <conditionExpression xsi:type="tFormalExpression"><![CDATA[\${
        block == "EC01.A"
      }]]></conditionExpression>
    </sequenceFlow>
    <sequenceFlow id="_8" name="default" sourceRef="_4" targetRef="_5"/>
    <sequenceFlow id="_9" sourceRef="_2" targetRef="_4"/>
    <sequenceFlow id="_10" sourceRef="_5" targetRef="_6"/>
    <sequenceFlow id="_11" sourceRef="_3" targetRef="_6"/>
  </process>
  <bpmndi:BPMNDiagram documentation="background=#3C3F41;count=1;horizontalcount=1;orientation=0;width=842.4;height=1195.2;imageableWidth=832.4;imageableHeight=1185.2;imageableX=5.0;imageableY=5.0" id="Diagram-_1" name="New Diagram">
    <bpmndi:BPMNPlane bpmnElement="myProcess_1">
      <bpmndi:BPMNShape bpmnElement="_2" id="Shape-_2">
        <dc:Bounds height="32.0" width="32.0" x="130.0" y="225.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_3" id="Shape-_3">
        <dc:Bounds height="55.0" width="85.0" x="350.0" y="110.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_4" id="Shape-_4" isMarkerVisible="false">
        <dc:Bounds height="32.0" width="32.0" x="260.0" y="235.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.0" width="32.0" x="1.0" y="4.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_5" id="Shape-_5">
        <dc:Bounds height="55.0" width="85.0" x="350.0" y="275.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="55.0" width="85.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape bpmnElement="_6" id="Shape-_6">
        <dc:Bounds height="32.0" width="32.0" x="520.0" y="210.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="32.0" width="32.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge bpmnElement="_7" id="BPMNEdge__7" sourceElement="_4" targetElement="_3">
        <di:waypoint x="292.0" y="251.0"/>
        <di:waypoint x="350.0" y="137.5"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_8" id="BPMNEdge__8" sourceElement="_4" targetElement="_5">
        <di:waypoint x="292.0" y="251.0"/>
        <di:waypoint x="350.0" y="302.5"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_9" id="BPMNEdge__9" sourceElement="_2" targetElement="_4">
        <di:waypoint x="162.0" y="241.0"/>
        <di:waypoint x="260.0" y="251.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_11" id="BPMNEdge__11" sourceElement="_3" targetElement="_6">
        <di:waypoint x="435.0" y="137.5"/>
        <di:waypoint x="520.0" y="226.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge bpmnElement="_10" id="BPMNEdge__10" sourceElement="_5" targetElement="_6">
        <di:waypoint x="435.0" y="302.5"/>
        <di:waypoint x="520.0" y="226.0"/>
        <bpmndi:BPMNLabel>
          <dc:Bounds height="0.0" width="0.0" x="0.0" y="0.0"/>
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</definitions>
`;
export const BasicBpmViewer = () => (
  <BpmViewer
    xml={xml}
    name="process"
    bpmInstance={{
      operationRecords: [
        {
          date: '2022-01-06T07:44:16.000+0000',
          operationResult: null,
          operationType: 'NOT_EXECUTE',
          remark: null,
          userList: [
            {
              id: '1',
              userName: 'admin',
              jobNo: null,
              mobile: null,
              email: null,
            },
          ],
          operationName: 'StartEvent',
          taskId: null,
          nodeId: '_2',
        },
        {
          date: null,
          operationResult: '待审批',
          operationType: 'NOT_EXECUTE',
          remark: null,
          userList: null,
          operationName: 'A楼楼长审批',
          taskId: null,
          nodeId: '_3',
        },
        {
          date: null,
          operationResult: '待审批',
          operationType: 'NOT_EXECUTE',
          remark: null,
          userList: null,
          operationName: 'ExclusiveGateway',
          taskId: null,
          nodeId: '_4',
        },
        {
          date: null,
          operationResult: '待审批',
          operationType: 'NOT_EXECUTE',
          remark: null,
          userList: null,
          operationName: '其它人员审批',
          taskId: null,
          nodeId: '_5',
        },
        {
          date: null,
          operationResult: '待审批',
          operationType: 'NOT_EXECUTE',
          remark: null,
          userList: null,
          operationName: 'EndEvent',
          taskId: null,
          nodeId: '_6',
        },
      ],
    }}
  />
);
