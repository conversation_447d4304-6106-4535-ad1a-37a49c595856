@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.bpmViewer {
  width: 100%;
  height: 70vh;
}

.container {
  width: 100%;
  height: 100%;
  /* background-image: linear-gradient(
        90deg,
        rgba(220, 220, 220, 0.5) 6%,
        transparent 0
      ),
      linear-gradient(rgba(192, 192, 192, 0.5) 6%, transparent 0);
    background-size: 12px 12px;
    -webkit-tap-highlight-color: rgba(255, 255, 255, 0); */
  :global {
    .bjs-powered-by {
      visibility: hidden;
    }
    // 节点的样式
    .highlight-orange .djs-visual > :nth-child(1) {
      // fill: rgba(23, 125, 220, 0.3) !important;
      stroke: @blue-6 !important;
    }
    .highlight-orange .djs-visual > :nth-child(2) {
      fill: @blue-6 !important;
    }

    .highlight-green .djs-visual > :nth-child(1) {
      stroke: @blue-6 !important;
      fill: transparent !important;
    }
    .highlight-green .djs-visual > :nth-child(2) {
      fill: @blue-6 !important;
    }

    .highlight-red .djs-visual > :nth-child(1) {
      fill: transparent !important;
      stroke: @disabled-color-dark !important;
    }
    .highlight-red .djs-visual > :nth-child(2) {
      fill: @disabled-color-dark !important;
    }

    .highlight-gray .djs-visual > :nth-child(1) {
      // fill: #e84749rgba(232, 71, 73, 0.25) !important;
      stroke: @red-5 !important;
    }
    .highlight-gray .djs-visual > :nth-child(2) {
      fill: @red-5 !important;
    }

    // 条件顺序流和普通顺序流的样式

    .highlight-orange-line .djs-visual > :nth-child(1) {
      stroke: @blue-6 !important;
      marker-end: url('#sequenceflow-arrow-already') !important;
    }

    .highlight-green-line .djs-visual > :nth-child(1) {
      stroke: @blue-6 !important;
      marker-end: url('#sequenceflow-arrow-already') !important;
    }

    .highlight-gray-line .djs-visual > :nth-child(1) {
      stroke: @red-5 !important;
      marker-end: url('#sequenceflow-arrow-refuse') !important;
    }
    .highlight-red-line .djs-visual > :nth-child(1) {
      stroke: @disabled-color-dark !important;
      marker-end: url('#sequenceflow-arrow-no-execute') !important;
    }
    // 默认顺序流的样式

    .highlight-orange-default-line .djs-visual > :nth-child(1) {
      stroke: @blue-6 !important;
      marker-end: url('#sequenceflow-arrow-already') !important;
      marker-start: url('#conditional-default-flow-marker-already') !important;
    }

    .highlight-green-default-line .djs-visual > :nth-child(1) {
      stroke: @blue-6 !important;
      marker-end: url('#sequenceflow-arrow-already') !important;
      marker-start: url('#conditional-default-flow-marker-already') !important;
    }

    .highlight-red-default-line .djs-visual > :nth-child(1) {
      stroke: @disabled-color-dark !important;
      marker-end: url('#sequenceflow-arrow-no-execute') !important;
      marker-start: url('#conditional-default-flow-marker-no-execute') !important;
    }

    .highlight-gray-default-line .djs-visual > :nth-child(1) {
      stroke: @red-5 !important;
      marker-end: url('#sequenceflow-arrow-refuse') !important;
      marker-start: url('#conditional-default-flow-marker-refuse') !important;
    }

    // 文字框中文字的样式
    .highlight-orange-text .djs-visual > :nth-child(1) {
      fill: @blue-6 !important;
    }
    .highlight-green-text .djs-visual > :nth-child(1) {
      fill: @blue-6 !important;
    }

    .highlight-gray-text .djs-visual > :nth-child(1) {
      fill: @red-5 !important;
    }

    .highlight-red-text .djs-visual > :nth-child(1) {
      fill: @disabled-color-dark !important;
    }
  }
}

// 悬浮窗样式

// .bpm-popover {
//   box-sizing: border-box;
//   margin: 0;
//   padding: 0;
//   // background: black;
//   color: white;
//   font-size: 14px;
//   font-variant: tabular-nums;
//   line-height: 1.5715;
//   list-style: none;
//   font-feature-settings: 'tnum';
//   position: absolute;
//   top: 0;
//   left: 0;
//   z-index: 1030;
//   font-weight: 400;
//   white-space: normal;
//   text-align: left;
//   cursor: auto;
//   -webkit-user-select: text;
//   -moz-user-select: text;
//   -ms-user-select: text;
//   user-select: text;
//   padding-top: 10px;
// }

// .bpm-popover-arrow {
//   position: absolute;
//   display: block;
//   width: 8.48528137px;
//   height: 8.48528137px;
//   overflow: hidden;
//   background: 0 0;
//   pointer-events: none;
//   top: 1.5px;
//   left: 16px;
// }

// .bpm-popover-arrow-content {
//   position: absolute;
//   inset: 0;
//   display: block;
//   width: 6px;
//   height: 6px;
//   margin: auto;
//   background-color: black;
//   content: '';
//   pointer-events: auto;
//   box-shadow: -2px -2px 5px #0000000f;
//   transform: translateY(4.24264069px) rotate(45deg);
// }
// .bpm-popover-inner {
//   background-color: black;
//   background-clip: padding-box;
//   border-radius: 2px;
//   box-shadow: 0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d;
// }

// .bpm-popover-title {
//   min-width: 177px;
//   min-height: 32px;
//   margin: 0;
//   padding: 5px 16px 4px;
//   color: white;
//   font-weight: 500;
//   border-bottom: 1px solid #f0f0f0;
// }

// .bpm-popover-inner-content {
//   padding: 12px 16px;
//   // color: #000000d9;
// }
