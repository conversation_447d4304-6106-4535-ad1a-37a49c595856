import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-codes.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css';
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn.css';
//@ts-ignore
import BpmnViewer from 'bpmn-js/lib/Viewer';
//@ts-ignore
import camundaModdleDescriptor from 'camunda-bpmn-moddle/resources/camunda';
import dayjs from 'dayjs';
import { query as domQuery } from 'min-dom';
import React, { useEffect, useState } from 'react';
import { append as svgAppend, attr as svgAttr, create as svgCreate } from 'tiny-svg';

import styles from './bpm-viewer.module.less';
import customTranslate from './customTranslate/customTranslate';

export type BpmViewerProps = {
  // 流程模型的基本属性
  xml: string;
  name: string;
  // 基于流程模型生成的实例属性，包含各节点具体信息
  bpmInstance?: any;
};

export function BpmViewer({ xml, name, bpmInstance }: BpmViewerProps) {
  // 汉化
  const customTranslateModule = { translate: ['value', customTranslate] };
  const [bpmnViewer, setBpmnViewer] = useState<BpmnViewer | null>(null);
  // const bpmnViewer = useRef({});

  // let bpmnViewer = null as any;
  // // const bpmnViewer = useRef(
  // //   new BpmnViewer({
  // //     container: '#canvas',
  // //     height: '100vh',
  // //     additionalModules: [
  // //       // 汉化模块
  // //       customTranslateModule,
  // //     ],
  // //     moddleExtensions: {
  // //       camunda: camundaModdleDescriptor,
  // //     },
  // //   })
  // // ) as any;

  useEffect(() => {
    initBpmn();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  useEffect(() => {
    if (bpmnViewer) {
      const string = JSON.parse(JSON.stringify(xml));

      const xml2 = string.replace(new RegExp('activiti:', 'g'), 'camunda:');
      // importxml是异步的，所以后续的操作得写进回调函数中

      bpmnViewer.importXML(xml2).then(() => {
        bpmnViewer.get('canvas').zoom('fit-viewport');

        const currentViewbox = bpmnViewer.get('canvas').viewbox();
        bpmnViewer.get('canvas').zoom('fit-viewport', 'auto');
        const currentViewbox2 = bpmnViewer.get('canvas').viewbox();

        bpmnViewer.get('canvas').viewbox({
          x: currentViewbox2.x,
          y: currentViewbox.y,
          width: currentViewbox.width,
          height: currentViewbox.height,
        });
        // 当展示流程实例详情时，改变节点颜色。
        if (bpmInstance) {
          initArrowColor();
          setColor(bpmInstance.operationRecords);
          addEventBusListener(bpmInstance.operationRecords);
        } else {
          initArrowColor();
          setColor();
        }
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [bpmnViewer, bpmInstance, xml]);
  // 初始化流程图
  const initBpmn = () => {
    setBpmnViewer(
      new BpmnViewer({
        container: '#canvas',
        additionalModules: [
          // 汉化模块
          customTranslateModule,
        ],
        moddleExtensions: {
          camunda: camundaModdleDescriptor,
        },
      })
    );
  };
  // 为canvas配置箭头、默认顺序流的符号样式
  const initArrowColor = () => {
    const marker = svgCreate('marker');

    svgAttr(marker, {
      id: 'sequenceflow-arrow-already',
      viewBox: '0 0 20 20',
      refX: '11',
      refY: '10',
      markerWidth: '10',
      markerHeight: '10',
      orient: 'auto',
    });

    const path = svgCreate('path');

    svgAttr(path, {
      d: 'M 1 5 L 11 10 L 1 15 Z',
      style:
        ' stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke:#177ddc;fill:#177ddc',
    });
    const defs = domQuery('defs') as any;
    svgAppend(marker, path);
    svgAppend(defs, marker);

    const marker2 = svgCreate('marker');
    svgAttr(marker2, {
      id: 'sequenceflow-arrow-no-execute',
      viewBox: '0 0 20 20',
      refX: '11',
      refY: '10',
      markerWidth: '10',
      markerHeight: '10',
      orient: 'auto',
    });

    const path2 = svgCreate('path');

    svgAttr(path2, {
      d: 'M 1 5 L 11 10 L 1 15 Z',
      style:
        ' stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke:#1f3857;fill:#1f3857',
    });

    svgAppend(marker2, path2);
    svgAppend(defs, marker2);

    const marker3 = svgCreate('marker');
    svgAttr(marker3, {
      id: 'conditional-default-flow-marker-already',
      viewBox: '0 0 20 20',
      refX: '0',
      refY: '10',
      markerWidth: '10',
      markerHeight: '10',
      orient: 'auto',
    });

    const path3 = svgCreate('path');

    svgAttr(path3, {
      d: 'M 6 4 L 10 16',
      style:
        'fill: #177ddc; stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke: #177ddc; ',
    });
    svgAppend(marker3, path3);
    svgAppend(defs, marker3);
    const marker4 = svgCreate('marker');
    svgAttr(marker4, {
      id: 'conditional-default-flow-marker-no-execute',
      viewBox: '0 0 20 20',
      refX: '0',
      refY: '10',
      markerWidth: '10',
      markerHeight: '10',
      orient: 'auto',
    });
    const path4 = svgCreate('path');

    svgAttr(path4, {
      d: 'M 6 4 L 10 16',
      style:
        'fill: #1f3857; stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke: #1f3857; ',
    });
    svgAppend(marker4, path4);
    svgAppend(defs, marker4);

    const marker5 = svgCreate('marker');

    svgAttr(marker5, {
      id: 'sequenceflow-arrow-refuse',
      viewBox: '0 0 20 20',
      refX: '11',
      refY: '10',
      markerWidth: '10',
      markerHeight: '10',
      orient: 'auto',
    });

    const path5 = svgCreate('path');

    svgAttr(path5, {
      d: 'M 1 5 L 11 10 L 1 15 Z',
      style:
        ' stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke:#e84749;fill:#e84749',
    });
    svgAppend(marker5, path5);
    svgAppend(defs, marker5);
    const marker6 = svgCreate('marker');
    svgAttr(marker6, {
      id: 'conditional-default-flow-marker-refuse',
      viewBox: '0 0 20 20',
      refX: '0',
      refY: '10',
      markerWidth: '10',
      markerHeight: '10',
      orient: 'auto',
    });
    const path6 = svgCreate('path');

    svgAttr(path6, {
      d: 'M 6 4 L 10 16',
      style:
        'fill: #e84749; stroke-width: 1px; stroke-linecap: round; stroke-dasharray: 10000, 1; stroke: #e84749; ',
    });
    svgAppend(marker6, path6);
    svgAppend(defs, marker6);
  };

  const setColor = (operationRecords?: any) => {
    // 获取到全部节点
    const canvas = bpmnViewer.get('canvas');
    // 获取所有节点
    const allShapes = bpmnViewer.get('elementRegistry').getAll();
    if (operationRecords) {
      // 获取所有默认顺序流,id
      const defaultLineList = findDefaultLine(allShapes);
      // 设置所有默认顺序流颜色
      setDefaultLineColor(canvas, defaultLineList, operationRecords);
      // 获取所有从用户节点出发的条件顺序流
      const taskConditionalLineList = findTaskConditionalLineList(allShapes);

      // 设置所有从用户节点出发的条件顺序流颜色
      setTaskConditionalLineColor(canvas, taskConditionalLineList, operationRecords);
      // 获取所有其他顺序流
      const otherLineList = findOtherLine(allShapes, defaultLineList, taskConditionalLineList);
      // 设置所有其他顺序流颜色
      setOtherLineColor(canvas, otherLineList, operationRecords);

      // 获取所有label标签
      const labelList = findLabelList(allShapes);
      // 设置所有label标签的颜色
      setLabelColor(canvas, labelList, defaultLineList, taskConditionalLineList, operationRecords);
      // 获取所有事件节点
      const eventNodeList = findEventNode(allShapes);
      // 设置所有事件节点颜色
      setEventNodeColor(canvas, eventNodeList, operationRecords);
    } else {
      // 设置默认颜色
      setDefaultNodeColor(canvas, allShapes);
    }
  };
  // 监听 element
  const addEventBusListener = (operationRecords: any) => {
    const eventBus = bpmnViewer.get('eventBus'); // 需要使用 eventBus
    const eventType = ['element.hover']; // 需要监听的事件集合
    let popId = '';

    eventType.forEach(eventType => {
      eventBus.on(eventType, (e: any) => {
        if (
          e.type === 'element.hover' &&
          (e.element.type === 'bpmn:UserTask' || e.element.type === 'bpmn:StartEvent')
        ) {
          let overlays = bpmnViewer.get('overlays');
          overlays.remove({ element: popId });

          popId = e.element.id;

          const pad = operationRecords?.find((item: any) => item.nodeId === popId);
          if (pad.operationType !== 'NOT_EXECUTE') {
            let str = '';
            if (pad.userList) {
              for (let i = 0; i < pad.userList.length; i++) {
                str += pad.userList[i].userName + ',';
              }
              str = str.substring(0, str.length - 1);
            }

            const padHtml = generatePopover(e.element.type, pad, str);
            overlays.add(e.element.id, {
              html: padHtml,
              position: {
                right: 0,
                bottom: 0,
              },
            });
          }
        } else {
          let overlays = bpmnViewer.get('overlays');
          if (popId) {
            overlays.remove({ element: popId });
          }
        }
      });
    });
  };
  // 渲染默认节点展示颜色
  const setDefaultNodeColor = (
    canvas: { addMarker: (id: string, className: string) => void },
    allShapes: [{ id: string; type: string }]
  ) => {
    for (const item of allShapes) {
      const defaultLineList = findDefaultLine(allShapes).map(item => item.id);
      const shapeId = item.id;
      switch (item.type) {
        case 'label':
          canvas.addMarker(shapeId, 'highlight-green-text');
          break;
        case 'bpmn:SequenceFlow':
          if (defaultLineList.indexOf(item.id) !== -1) {
            canvas.addMarker(shapeId, 'highlight-green-default-line');
          } else {
            canvas.addMarker(shapeId, 'highlight-green-line');
          }
          break;
        default:
          canvas.addMarker(shapeId, 'highlight-green');
          break;
      }
    }
  };
  //渲染各类型节点颜色
  const findEventNode = (shapeLists: any) => {
    const eventNodeList = [];
    for (const item of shapeLists) {
      const shapeType = item.type;
      if (
        shapeType !== 'bpmn:SequenceFlow' &&
        shapeType !== 'bpmn:Process' &&
        shapeType !== 'label'
      ) {
        eventNodeList.push(item);
      }
    }
    return eventNodeList;
  };

  const setEventNodeColor = (canvas: any, list: any, operationRecords: any) => {
    for (const item of list) {
      const shapeType = item.type;
      const shapeId = item.id;

      if (shapeType === 'bpmn:StartEvent') {
        canvas.addMarker(
          shapeId,
          pickColor(operationRecords.find((item: any) => item.nodeId === shapeId))
        );
      } else if (shapeType === 'bpmn:EndEvent') {
        canvas.addMarker(
          shapeId,
          pickColor(operationRecords.find((item: any) => item.nodeId === shapeId))
        );
      } else {
        canvas.addMarker(
          shapeId,
          pickColor(operationRecords.find((item: any) => item.nodeId === shapeId))
        );
      }
    }
  };

  const findTaskConditionalLineList = (shapeLists: any) => {
    const taskLineList = [];
    for (const item of shapeLists) {
      if (
        item?.businessObject?.conditionExpression &&
        item.type === 'bpmn:SequenceFlow' &&
        item?.businessObject?.sourceRef.$type === 'bpmn:UserTask'
      ) {
        taskLineList.push(item);
      }
    }

    return taskLineList;
  };
  const setTaskConditionalLineColor = (canvas: any, list: any, operationRecords: any) => {
    for (const element of list) {
      const conditionExpression = element?.businessObject?.conditionExpression.body;
      const sourceRef = element.businessObject.sourceRef;
      const node = operationRecords.find((e: any) => e.nodeId === sourceRef.id);

      if (
        // eslint-disable-next-line no-template-curly-in-string
        (conditionExpression === '${result == 1}' && node.operationType === 'REFUSE') ||
        // eslint-disable-next-line no-template-curly-in-string
        (conditionExpression === '${result == 0}' && node.operationType === 'ALREADY_EXECUTE')
      ) {
        // eslint-disable-next-line no-template-curly-in-string
        if (conditionExpression === '${result == 0}') {
          canvas.addMarker(element.id, pickLineColor({ operationType: 'ALREADY_EXECUTE' }));
        } else {
          canvas.addMarker(element.id, pickLineColor({ operationType: 'REFUSE' }));
        }
      } else {
        canvas.addMarker(element.id, pickLineColor({ operationType: 'NOT_EXECUTE' }));
      }
    }
  };
  const findDefaultLine = (shapeLists: any) => {
    const defaultList = [];
    for (const item of shapeLists) {
      if (item?.businessObject?.default) {
        defaultList.push(item.businessObject.default);
      }
    }

    return defaultList;
  };
  const setDefaultLineColor = (canvas: any, list: any, operationRecords: any) => {
    for (const item of list) {
      const shapeTargetRef = item.targetRef;

      canvas.addMarker(
        item.id,
        pickDefaultLineColor(operationRecords.find((e: any) => e.nodeId === shapeTargetRef.id))
      );
    }
  };

  const findOtherLine = (shapeLists: any, defaultLineList: any, taskConditionalLineList: any) => {
    const otherlineList = [];
    for (const item of shapeLists) {
      if (item.type === 'bpmn:SequenceFlow') {
        otherlineList.push(item.businessObject);
      }
    }

    return otherlineList.filter(item => {
      return (
        defaultLineList.indexOf(item) === -1 &&
        taskConditionalLineList
          .map((item: { businessObject: any }) => item.businessObject)
          .indexOf(item) === -1
      );
    });
  };

  const setOtherLineColor = (canvas: any, list: any, operationRecords: any) => {
    for (const element of list) {
      const shapeTargetRef = element.targetRef;
      const shapeId = element.id;

      if (
        shapeTargetRef.$type !== 'bpmn:ExclusiveGateway' &&
        shapeTargetRef.$type !== 'bpmn:EndEvent'
      ) {
        canvas.addMarker(
          shapeId,
          pickLineColor(operationRecords.find((item: any) => item.nodeId === shapeTargetRef.id))
        );
      } else {
        const shapeSoureceRef = element.sourceRef;
        if (shapeSoureceRef.$type === 'bpmn:Start') {
          canvas.addMarker(shapeId, pickLineColor({ operationType: 'ALREADY_EXECUTE' }));
        } else if (
          shapeSoureceRef.$type === 'bpmn:UserTask' &&
          shapeTargetRef.$type === 'bpmn:EndEvent'
        ) {
          if (
            pickLineColor(
              operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id)
            ) !== 'highlight-green-line'
          ) {
            canvas.addMarker(
              shapeId,
              pickLineColor(
                operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id)
              )
            );
          } else {
            canvas.addMarker(
              shapeId,
              pickLineColor(
                operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id)
              )
            );
          }
        } else {
          canvas.addMarker(
            shapeId,
            pickLineColor(operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id))
          );
        }
      }
    }
  };

  const findLabelList = (shapeLists: any) => {
    const labelList = [];
    for (const item of shapeLists) {
      if (item?.type === 'label') {
        labelList.push(item.businessObject);
      }
    }

    return labelList;
  };
  const setLabelColor = (
    canvas: any,
    list: any,
    defaultLineList: any,
    taskConditionalLineList: any,
    operationRecords: any
  ) => {
    for (const element of list) {
      const shapeId = element.id;
      const shapeType = element.$type;

      if (shapeType === 'bpmn:SequenceFlow') {
        const shapeTargetRef = element.targetRef;

        if (defaultLineList.indexOf(element) !== -1) {
          canvas.addMarker(
            shapeId + '_label',
            pickTextColor(operationRecords.find((item: any) => item.nodeId === shapeTargetRef.id))
          );
        } else if (
          taskConditionalLineList.map((item: any) => item.businessObject).indexOf(element) !== -1
        ) {
          const conditionExpression = element?.conditionExpression.body;
          const sourceRef = element.sourceRef;
          const node = operationRecords.find((e: any) => e.nodeId === sourceRef.id);

          if (
            // eslint-disable-next-line no-template-curly-in-string
            (conditionExpression === '${result == 1}' && node.operationType === 'REFUSE') ||
            // eslint-disable-next-line no-template-curly-in-string
            (conditionExpression === '${result == 0}' && node.operationType === 'ALREADY_EXECUTE')
          ) {
            // eslint-disable-next-line no-template-curly-in-string
            if (conditionExpression === '${result == 0}') {
              canvas.addMarker(
                element.id + '_label',
                pickLineColor({ operationType: 'ALREADY_EXECUTE' })
              );
            } else {
              canvas.addMarker(element.id + '_label', pickLineColor({ operationType: 'REFUSE' }));
            }
          } else {
            canvas.addMarker(
              element.id + '_label',
              pickLineColor({ operationType: 'NOT_EXECUTE' })
            );
          }
        } else {
          if (
            shapeTargetRef.$type !== 'bpmn:ExclusiveGateway' &&
            shapeTargetRef.$type !== 'bpmn:EndEvent'
          ) {
            canvas.addMarker(
              shapeId + '_label',
              pickTextColor(operationRecords.find((item: any) => item.nodeId === shapeTargetRef.id))
            );
          } else {
            const shapeSoureceRef = element.sourceRef;
            if (shapeSoureceRef.$type === 'bpmn:Start') {
              canvas.addMarker(
                shapeId + '_label',
                pickTextColor({ operationType: 'ALREADY_EXECUTE' })
              );
            } else if (
              shapeSoureceRef.$type === 'bpmn:UserTask' &&
              shapeTargetRef.$type === 'bpmn:EndEvent'
            ) {
              if (
                pickLineColor(
                  operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id)
                ) !== 'highlight-green-line'
              ) {
                canvas.addMarker(
                  shapeId + '_label',
                  pickTextColor({ operationType: 'NOT_EXECUTE' })
                );
              } else {
                canvas.addMarker(
                  shapeId + '_label',
                  pickTextColor(
                    operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id)
                  )
                );
              }
            } else {
              canvas.addMarker(
                shapeId + '_label',

                pickTextColor(
                  operationRecords.find((item: any) => item.nodeId === shapeSoureceRef.id)
                )
              );
            }
          }
        }
      } else {
        if (shapeType === 'bpmn:StartEvent') {
          canvas.addMarker(
            shapeId + '_label',
            pickTextColor(operationRecords.find((item: any) => item.nodeId === shapeId))
          );
        } else if (shapeType === 'bpmn:EndEvent') {
          canvas.addMarker(
            shapeId + '_label',
            pickTextColor(operationRecords.find((item: any) => item.nodeId === shapeId))
          );
        }
      }
    }
  };

  const pickColor = (type: any) => {
    let color = '';
    switch (type.operationType) {
      case 'ALREADY_EXECUTE':
        color = 'highlight-green';
        break;
      case 'EXECUTE':
        color = 'highlight-orange';
        break;
      case 'NOT_EXECUTE':
        color = 'highlight-red';
        break;
      case 'REFUSE':
        color = 'highlight-gray';
        break;
    }
    return color;
  };
  const pickLineColor = (type: any) => {
    let color = '';
    switch (type.operationType) {
      case 'ALREADY_EXECUTE':
        color = 'highlight-green-line';
        break;
      case 'EXECUTE':
        color = 'highlight-orange-line';
        break;
      case 'NOT_EXECUTE':
        color = 'highlight-red-line';
        break;
      case 'REFUSE':
        color = 'highlight-gray-line';
        break;
    }
    return color;
  };

  const pickDefaultLineColor = (type: any) => {
    let color = '';
    switch (type.operationType) {
      case 'ALREADY_EXECUTE':
        color = 'highlight-green-default-line';
        break;
      case 'EXECUTE':
        color = 'highlight-orange-default-line';
        break;
      case 'NOT_EXECUTE':
        color = 'highlight-red-default-line';
        break;
      case 'REFUSE':
        color = 'highlight-gray-default-line';
        break;
    }
    return color;
  };

  const pickTextColor = (type: any) => {
    let color = '';
    switch (type.operationType) {
      case 'ALREADY_EXECUTE':
        color = 'highlight-green-text';
        break;
      case 'EXECUTE':
        color = 'highlight-orange-text';
        break;
      case 'NOT_EXECUTE':
        color = 'highlight-red-text';
        break;
      case 'REFUSE':
        color = 'highlight-gray-text';
        break;
    }
    return color;
  };
  // 生成 悬浮窗
  const generatePopover = (type: string, pad: any, str: string) => {
    if (type === 'bpmn:StartEvent') {
      return `<div class="manyun-popover">
       <div class="manyun-popover-content">
         <div class="manyun-popover-arrow">
           <span class="manyun-popover-arrow-content"></span>
         </div>
         <div class="manyun-popover-inner">
           <div class="manyun-popover-title">
             <span>相关信息</span>
           </div>
           <div class="manyun-popover-inner-content">
             <div>
               <p>发起时间: ${pad.date ? dayjs(pad.date).format('YYYY-MM-DD HH:mm:ss') : '--'}</p>
               

             </div>
           </div>
         </div>
       </div>
     </div>`;
    }
    return `<div class="manyun-popover">
      <div class="manyun-popover-content">
        <div class="manyun-popover-arrow">
          <span class="manyun-popover-arrow-content"></span>
        </div>
        <div class="manyun-popover-inner">
          <div class="manyun-popover-title">
            <span>相关信息</span>
          </div>
          <div class="manyun-popover-inner-content">
            <div>
              <p>完成时间: ${pad.date ? dayjs(pad.date).format('YYYY-MM-DD HH:mm:ss') : '--'}</p>
              <p>状态: ${pad.operationResult ?? '--'}</p>
              <p>相关人员: ${str !== '' ? str : '--'}</p>
              <p>备注: ${pad.remark ?? '--'}</p>

            </div>
          </div>
        </div>
      </div>
    </div>`;
  };

  return (
    <div className={styles.bpmViewer}>
      <div id="canvas" className={styles.container}></div>
    </div>
  );
}
