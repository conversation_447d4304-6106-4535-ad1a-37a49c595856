import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type VariableSelectProps<VT = any> = {
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps<VT>;

export const VariableSelect = React.forwardRef(
  ({ trigger, ...selectProps }: VariableSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const [{ data }, { readMetaData }] = useMetaData(MetaType.APPROVAL_VARIABLE);
    React.useEffect(() => {
      readMetaData();
    }, [readMetaData]);

    const variableSelectData = useMemo(() => {
      return data?.data?.map(item => ({ value: item.value, label: item.label }));
    }, [data]);

    return (
      <Select
        ref={ref}
        showSearch
        optionLabelProp="label"
        optionFilterProp="label"
        {...selectProps}
        options={variableSelectData}
      />
    );
  }
);

VariableSelect.displayName = 'VariableSelect';
