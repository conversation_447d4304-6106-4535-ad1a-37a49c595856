import uniqBy from 'lodash/uniqBy';
import { useMemo, useState } from 'react';

export type UseTaskDataProps = {
  operationRecords: any[];
  userId: string;
  processChargeUser?: string;
  processChargeUserName?: string;
};

export function useTaskData({
  operationRecords,
  userId,
  processChargeUser,
  processChargeUserName,
}: UseTaskDataProps) {
  const [taskData, setTaskData] = useState<any>({});
  const getTaskId = (currentUserId: string) => {
    const currentRecord = operationRecords.filter(
      (item: any) => item.operationType === 'EXECUTE' && item?.operationTasks
    )[0];

    let curTask = currentRecord?.operationTasks;

    if (curTask) {
      //还要处理下审批人异常的问题
      curTask = curTask?.map((item: Record<string, any>) => {
        let userList: Record<string, any>[] = [];
        const missUserInfo = item?.missUserInfo?.filter(
          (val: Record<string, any>) => val?.type === 'ROLE_NO_USER'
        );

        if (item?.missUser && missUserInfo?.length !== item?.missUserInfo?.length) {
          userList.push({
            id: processChargeUser || '14129',
            userName: processChargeUserName || '白雪锋',
            taskResult: '待审批',
            whetherEnable: true,
            type: 'PROCESS_CHARGER',
          });
        }

        return {
          ...item,
          userList: uniqBy([...item.userList, ...userList], 'id'), //去重
        };
      });

      // 或签
      if (curTask?.length === 1) {
        return {
          taskId: curTask[0].taskId,
          userList: curTask[0].userList,
          taskResult: curTask[0].taskResult === '待审批' ? true : false,
        };
      }
      // 会签
      else {
        //有两种情况，一种是当前用户的是正常的审批人，还有一种是用户是负责人，优先处理审批人
        let currentTaskArr = curTask?.filter(
          (item: any) =>
            item.userList &&
            item.userList.filter(
              (user: any) => user.id === currentUserId && user?.type !== 'PROCESS_CHARGER'
            ).length > 0
        );

        if (!currentTaskArr.length) {
          currentTaskArr = curTask?.filter(
            (item: any) =>
              item.userList &&
              item.userList.filter((user: any) => user.id === currentUserId).length > 0
          );
        }

        const currentTask = currentTaskArr[0];
        return {
          taskId: currentTask?.taskId,
          taskResult: currentTask?.taskResult === '待审批' ? true : false,
          userList: currentTask?.userList,
        };
      }
    } else {
      return { taskId: '', taskResult: '', userList: [] };
    }
  };
  useMemo(() => {
    if (operationRecords.length) {
      setTaskData(getTaskId(userId));
    }

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [userId, operationRecords]);

  return [{ taskData }] as const;
}
