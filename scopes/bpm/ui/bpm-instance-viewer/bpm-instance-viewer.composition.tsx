import React from 'react';

import { DarkTheme } from '@manyun/base-ui.h5.theme.dark-theme';
import { LightTheme } from '@manyun/base-ui.h5.theme.light-theme';

import { getBPMInstance } from '@manyun/bpm.model.bpm-instance';
import { useRemoteMock as remoteMock } from '@manyun/service.request';

import { BpmInstanceViewer } from './bpm-instance-viewer';
import { useTaskData } from './use-task-data';

export const BasicBpmViewer = () => {
  const [initialized, setInitilized] = React.useState(false);
  // const [taskData, setTaskData] = React.useState<any>({});
  let records = getBPMInstance().operationRecords;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [{ taskData }] = useTaskData({ operationRecords: records, userId: '1' });
  // console.log('taskData', taskData);

  React.useEffect(() => {
    const mockOff = remoteMock('web');
    setInitilized(true);

    return () => {
      mockOff();
    };
  }, []);
  // React.useEffect(() => {
  //   // console.log('taskData', taskData);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [JSON.stringify(taskData)]);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <LightTheme>
      <BpmInstanceViewer bpmInstance={getBPMInstance()} />
    </LightTheme>
  );
};

export const DarkBpmViewer = () => {
  const [initialized, setInitilized] = React.useState(false);
  // const [taskData, setTaskData] = React.useState<any>({});
  let records = getBPMInstance().operationRecords;
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [{ taskData }] = useTaskData({ operationRecords: records, userId: '1' });
  // console.log('taskData', taskData);

  React.useEffect(() => {
    const mockOff = remoteMock('web');
    setInitilized(true);

    return () => {
      mockOff();
    };
  }, []);
  // React.useEffect(() => {
  //   // console.log('taskData', taskData);
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [JSON.stringify(taskData)]);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <DarkTheme>
      <BpmInstanceViewer bpmInstance={getBPMInstance()} isDarked />
    </DarkTheme>
  );
};
