import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { NodeStatus } from '@manyun/bpm.model.bpm-instance';

export function generateUserStatusInAlert(taskResult: string | null, tasks?: (string | null)[]) {
  switch (taskResult) {
    case '通过':
      return '已同意';
    case '拒绝':
      return '已拒绝';
    case '转交':
      return '已转交';
    case '待审批':
      return '等待审批';
    case '无':
      return '';
    case NodeStatus.Refuse:
      return '已拒绝';
    case 'PASS':
      return '已同意';
    case 'APPROVING':
      return '待审批';
    case NodeStatus.AlreadyExecute:
      if (tasks?.includes('拒绝')) {
        return '已拒绝';
      } else {
        return '已同意';
      }
    case 'EXECUTE':
      return '等待审批';

    default:
      return '';
  }
}

export function getUserList(userExecuteListArr: Record<string, any>[]) {
  const missUserArr = userExecuteListArr.filter(
    item => item.taskResult === '待审批' && !item.whetherEnable
  ); //离职的
  const normalArr = userExecuteListArr.filter(
    item => item.taskResult === '待审批' && item.whetherEnable
  ); //在职的
  const chargeUserArr = userExecuteListArr.filter(item => item.taskResult === '负责人'); //负责人

  return [...missUserArr, ...normalArr, ...chargeUserArr];
}

export function getStatusTag(status: string, bordered = true, fontSize = 14) {
  switch (status) {
    case '(已同意)':
    case '已同意':
      return (
        <Tag style={{ fontSize: fontSize }} color="success">
          已同意
        </Tag>
      );
    case '(已拒绝)':
    case '已拒绝':
      return (
        <Tag style={{ fontSize: fontSize }} color="error">
          已拒绝
        </Tag>
      );

    case '(已转交)':
    case '已转交':
      return (
        <Tag
          style={{
            fontSize: fontSize,
            border: '1px solid #91D5FF',
            color: 'var(--manyun-primary-color)',
            backgroundColor: 'var(--manyun-primary-color-light)',
          }}
        >
          已转交
        </Tag>
      );
    case '(等待审批)':
    case '等待审批':
      return (
        <Tag style={{ fontSize: fontSize }} color="warning">
          等待审批
        </Tag>
      );
    case '(未处理)':
    case '未处理':
      return '未处理';
    default:
      return '';
  }
}

export function getStatus(remark: any) {
  if (
    typeof remark === 'string' &&
    (remark?.includes('多人会签') || remark?.includes('多人或签'))
  ) {
    const content = remark.match(/\((.*?)\)/);
    const remarkStatus = content && content[0] ? content[0] : '';
    const remarKText = remark?.includes('多人会签') ? '多人会签' : '多人或签';

    return {
      remarkStatus,
      remarKText,
    };
  }

  return {
    remarkStatus: '',
    remarKText: remark,
  };
}
