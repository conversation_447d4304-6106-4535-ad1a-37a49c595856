import React from 'react';

import {
  ImageColorful,
  PdfColorful,
  UnknownFileColorful,
  VideoColorful,
} from '@manyun/base-ui.icons';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';

function FileIcon({ file }: { file: Record<string, any> }) {
  switch (file.ext) {
    case '.jpg':
    case '.jpeg':
    case '.png':
      return <ImageColorful style={{ fontSize: '32px' }} />;
    case '.mp4':
      return <VideoColorful style={{ fontSize: '32px' }} />;
    case '.pdf':
      return <PdfColorful style={{ fontSize: '32px' }} />;
    default:
      return <UnknownFileColorful style={{ fontSize: '32px' }} />;
  }
}

export function FileShow({
  fileInfoList,
  style = {},
}: {
  fileInfoList?: Record<string, any>[];
  style?: Record<string, any>;
}) {
  return fileInfoList && Array.isArray(fileInfoList) ? (
    <McUpload
      showUploadList={{ showRemoveIcon: false }}
      // @ts-expect-error: TS2339 because type can not suit
      fileList={fileInfoList.map(file => McUploadFile.fromApiObject(file))}
      itemRender={(originNode, file, fileList) => {
        return (
          <Space style={{ width: '100%', ...style }} size={12}>
            <FileIcon file={file} />
            <div>
              <Typography.Text style={{ display: 'block' }}>{file?.name}</Typography.Text>
              <Typography.Text type="secondary" style={{ display: 'block', fontSize: 10 }}>
                {(Number(file.size ?? 0) / 1024).toFixed(1)}KB
              </Typography.Text>
            </div>
          </Space>
        );
      }}
    />
  ) : (
    <></>
  );
}
