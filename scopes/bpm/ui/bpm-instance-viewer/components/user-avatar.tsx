import React from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import {
  ApprovalPeoplesTwoTone,
  CheckCircleTwoTone,
  CloseCircleTwoTone,
  ExclamationCircleTwoTone,
  MessageCircleTwoTone,
  ProcessingCircleTwoTone,
  RedirectTwoTone,
  RevokeColorful,
} from '@manyun/base-ui.icons';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { NodeType } from '@manyun/bpm.model.bpm-instance';

import styles from '../bpm-instance-viewer.module.less';
import CommanderIcon from './commander-icon.js';
import DisabledIcon from './disabled-icon.js';
import { useUser } from './user-hook';

interface IProps {
  user: {
    // 用户任务状态
    taskResult?: string | null;
    // 节点状态
    operationType?: string | undefined;
    // 区分是用户任务还是整个节点
    isStep?: boolean;
    // 区分是否是collapse中alert的头像
    isCollapseAvatar?: boolean;
    // 区分是否是抄送节点
    isCc?: boolean;
    isSingleAwait?: boolean;
    // 区分是否是多人拒绝
    isMultipleRefuse?: boolean;
    // 区分是否是多人同意
    isMultiplePass?: boolean;
    // 区分是否是collapse中待审批队列中的头像
    isCollapseExecuteAvatar?: boolean;
    userName?: string;
    userAvatarId?: number;
    //是都离职false为离职了
    whetherEnable?: boolean;
    applyUser: number;
  };
}

const ApprovalPeoplesTwoToneIcon = ({ width, style }: Record<string, any>) => {
  return (
    <div style={{ width, height: width, overflow: 'hidden', borderRadius: '50%' }}>
      <ApprovalPeoplesTwoTone style={style} />
    </div>
  );
};

// 生成用户头像和icon的组合的组件
export function UserAvatar({ user }: IProps) {
  const {
    taskResult,
    operationType,
    isStep = false,
    isCollapseAvatar,
    isCollapseExecuteAvatar,
    isCc,
    isSingleAwait,
    isMultipleRefuse,
    isMultiplePass,
    userAvatarId,
    whetherEnable,
    applyUser,
  } = user;

  let userName = user?.userName;
  // 求userName
  if (userAvatarId && !userName) {
    const [user] = useUser(userAvatarId);
    userName = user?.name;
  }

  const avatarSize = isCollapseAvatar ? 22 : 32;
  if (!isStep && userAvatarId) {
    switch (taskResult) {
      case '通过':
        return (
          <div className={styles.avatar}>
            <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
            <CheckCircleTwoTone className={styles.avatarIcon} />
          </div>
        );
      case '拒绝':
        return (
          <div className={styles.avatar}>
            <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
            <CloseCircleTwoTone className={styles.avatarIconRefuse} />
          </div>
        );
      case '转交':
        return (
          <>
            <Space direction="vertical" align="center">
              <div className={styles.avatar}>
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
                <RedirectTwoTone className={styles.avatarIcon} />
              </div>
              {isCollapseExecuteAvatar && (
                <Typography.Text
                  type="secondary"
                  style={{
                    fontSize: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {userName ?? ''}
                </Typography.Text>
              )}
            </Space>
          </>
        );
      case '待审批':
        return (
          <>
            <Space direction="vertical" align="center">
              <div className={styles.avatar}>
                <User
                  id={userAvatarId}
                  name={userName}
                  size={avatarSize}
                  showName={false}
                  disabled={!whetherEnable}
                />
                {whetherEnable ? (
                  <ProcessingCircleTwoTone className={styles.avatarIcon} />
                ) : (
                  <DisabledIcon className={styles.commanderIcon} />
                )}
              </div>

              {isCollapseExecuteAvatar && (
                <Typography.Text
                  type="secondary"
                  style={{
                    fontSize: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {userName ?? ''}
                </Typography.Text>
              )}
            </Space>
          </>
        );
      case '已读':
        return (
          <>
            <Space direction="vertical" align="center">
              <div className={styles.avatar}>
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
                <CheckCircleTwoTone className={styles.avatarIcon} />
              </div>

              {isCollapseExecuteAvatar && (
                <Typography.Text
                  type="secondary"
                  style={{
                    fontSize: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {userName ?? ''}
                </Typography.Text>
              )}
            </Space>
          </>
        );
      case '负责人':
        return (
          <>
            <Space direction="vertical" align="center">
              <div className={styles.avatar}>
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
                <CommanderIcon className={styles.commanderIcon} />
              </div>

              {isCollapseExecuteAvatar && (
                <Typography.Text
                  type="secondary"
                  style={{
                    fontSize: '10px',
                    display: 'flex',
                    justifyContent: 'center',
                  }}
                >
                  {userName ?? ''}
                </Typography.Text>
              )}
            </Space>
          </>
        );
      case '无':
        return (
          <>
            <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
          </>
        );
      default:
        return (
          <>
            <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
            <CheckCircleTwoTone className={styles.avatarIcon} />
          </>
        );
    }
  } else {
    if (isStep) {
      switch (operationType) {
        case 'ALREADY_EXECUTE':
          return (
            <div className={styles.avatar}>
              {userAvatarId && !(isCc || isMultiplePass) ? (
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
              ) : (
                <ApprovalPeoplesTwoToneIcon style={{ fontSize: `${avatarSize}px` }} />
              )}
              <CheckCircleTwoTone className={styles.avatarStepIcon} />
            </div>
          );
        case 'MISS_USER':
          return (
            <div className={styles.avatar}>
              {userAvatarId && !(isCc || isMultiplePass) ? (
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
              ) : (
                <ApprovalPeoplesTwoToneIcon style={{ fontSize: `${avatarSize}px` }} />
              )}
              <ExclamationCircleTwoTone className={styles.avatarIcon} />
            </div>
          );
        case 'REFUSE':
          return (
            <div className={styles.avatar}>
              {userAvatarId && !isMultipleRefuse ? (
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
              ) : (
                <ApprovalPeoplesTwoToneIcon style={{ fontSize: `${avatarSize}px` }} />
              )}
              <CloseCircleTwoTone className={styles.avatarStepIconRefuse} />
            </div>
          );
        case 'EXECUTE':
          return (
            <div className={styles.avatar}>
              {userAvatarId && isSingleAwait ? (
                <User id={userAvatarId} name={userName} size={avatarSize} showName={false} />
              ) : (
                <ApprovalPeoplesTwoToneIcon style={{ fontSize: `${avatarSize}px` }} />
              )}
              <ProcessingCircleTwoTone className={styles.avatarStepIcon} />
            </div>
          );

        case 'REVOKE':
          return (
            <div className={styles.avatar}>
              <User id={applyUser} name={userName} size={avatarSize} showName={false} />
              <RevokeColorful className={styles.avatarStepIconRefuse} />
            </div>
          );

        case 'REDIRECT':
          return (
            <div className={styles.avatar}>
              <User id={userAvatarId!} size={avatarSize} showName={false} />
              <RedirectTwoTone className={styles.avatarStepIcon} />
            </div>
          );
        case NodeType.CommentNode:
          return (
            <div className={styles.avatar}>
              <User id={userAvatarId!} name={userName} size={avatarSize} showName={false} />
              <MessageCircleTwoTone className={styles.avatarCommentIcon} />
            </div>
          );
        default:
          return <></>;
      }
    }
    return (
      <div className={styles.avatar}>
        <User id={userAvatarId!} size={avatarSize} name={userName} showName={false} />
        <CheckCircleTwoTone className={styles.avatarStepIcon} />
      </div>
    );
  }
}
