import { DeleteOutlined } from '@ant-design/icons';
import React from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { deleteComment } from '@manyun/bpm.service.delete-comment';

export function DeleteCommentIcon({
  instId,
  personId,
  commentId,
  onSuccess,
}: {
  instId: string;
  personId: number;
  commentId: number;
  onSuccess?: () => void;
}) {
  const [, { checkUserId }] = useAuthorized() as any;
  const isCommentOwner = checkUserId(personId);

  return isCommentOwner ? (
    <Popconfirm
      zIndex={1051}
      title={
        <Space direction="vertical">
          <>是否确定删除评论</>
          <>删除评论会通知相关人员，且被@的人仍有权限浏览该审批</>
        </Space>
      }
      onConfirm={async () => {
        const { error } = await deleteComment({ instId, commentId });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功');
        onSuccess && onSuccess();
      }}
    >
      <DeleteOutlined style={{ position: 'absolute', bottom: 4, right: 4 }} />
    </Popconfirm>
  ) : null;
}
