import React from 'react';

import { fetchUsersByIds } from '@manyun/auth-hub.service.fetch-users-by-ids';
import type { SimpleUser } from '@manyun/auth-hub.service.fetch-users-by-ids';

/**
 * @deprecated TODO: should use GraphQL
 * @param userId
 * @returns
 */
export function useUser(userId: number) {
  const [user, setUser] = React.useState<SimpleUser | null>(null);

  React.useEffect(() => {
    (async () => {
      const { error, data } = await fetchUsersByIds({ ids: [userId] });
      if (error) {
        return;
      }
      setUser(data[0]);
    })();
  }, [userId]);

  return [user] as const;
}
