import dayjs from 'dayjs';
import React from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { TaskRedirectInfo } from '@manyun/bpm.model.bpm-instance';

import styles from '../bpm-instance-viewer.module.less';
import { generateUserStatusInAlert, getStatusTag } from '../utils.js';
import { NoteInfo } from './note-info.js';
import { UserAvatar } from './user-avatar.js';

interface RedirectAlertProps {
  index: number;
  redirectInfo: TaskRedirectInfo;
  applyUser: number;
}

export function RedirectAlert({ index, redirectInfo, applyUser }: RedirectAlertProps) {
  return (
    <Alert
      style={{ width: '100%', background: '#fafafa', border: 0, padding: '8px 16px' }}
      key={index}
      description={
        <div>
          <Space style={{ justifyContent: 'space-between', width: '100%', marginBottom: 8 }}>
            <Space size={8}>
              <UserAvatar
                user={{
                  taskResult: '转交',
                  isStep: false,
                  isCollapseAvatar: true,
                  userAvatarId: redirectInfo.sourceUserId,
                  applyUser,
                  whetherEnable: true,
                }}
              />
              <User.Link id={redirectInfo.sourceUserId} className={styles.userLink} />
              <Typography.Text type="secondary">
                {generateUserStatusInAlert('转交')
                  ? getStatusTag(generateUserStatusInAlert('转交'), false, 12)
                  : ''}
              </Typography.Text>
              <Typography.Text type="secondary" style={{ marginLeft: '-8px' }}>
                已转交给
                <User.Link
                  id={redirectInfo.targetUserId}
                  className={styles.userLink}
                  style={{ marginLeft: 4 }}
                />
              </Typography.Text>
            </Space>
            <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
              {redirectInfo.redirectTime && dayjs(redirectInfo.redirectTime).format('MM.DD HH:mm')}
            </Typography.Text>
          </Space>
          {redirectInfo?.desc ? <NoteInfo info={redirectInfo?.desc}></NoteInfo> : null}
        </div>
      }
    />
  );
}
