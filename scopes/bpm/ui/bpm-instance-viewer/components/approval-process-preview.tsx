import React from 'react';

import { ApprovalPreviewOutlined } from '@manyun/base-ui.icons';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

export type ApprovalProcessPreviewProps = {
  processId: string;
  fontSize?: number;
};
export function ApprovalProcessPreview({ processId, fontSize = 14 }: ApprovalProcessPreviewProps) {
  return (
    <FilePreviewWithContainer
      file={{
        ext: '.png',
        src: `/api/workflow/process/export/img?workflowId=${processId}`,
        name: '审批链.png',
      }}
    >
      <Tooltip title="审批链">
        <ApprovalPreviewOutlined style={{ fontSize, color: `var(--${prefixCls}-primary-color)` }} />
      </Tooltip>
    </FilePreviewWithContainer>
  );
}
