import { BellOutlined } from '@ant-design/icons';
import flatten from 'lodash/flatten';
import React from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
//@ts-ignore
import { fetchNoticeManager } from '@manyun/bpm.service.fetch-notice-manager';
import { useLoggedInUser } from '@manyun/iam.context.logged-in-user';

interface INoticeBtnProps {
  handleNoticeClick: (e: any) => void;
}

function NoticeBtn({ handleNoticeClick }: INoticeBtnProps) {
  return (
    <Button size="small" style={{ margin: '0 4px' }} onClick={handleNoticeClick}>
      <BellOutlined style={{ fontSize: '12px' }} />
      发送通知
    </Button>
  );
}

export function ErrorMark({
  recordItem,
  processChargeUser,
  processChargeUserName,
  processInstanceCode,
  applyUserId,
}: Record<string, any>) {
  const processChargeUserId = Number(processChargeUser); //流程负责人
  let allTask = recordItem?.operationTasks;
  let errorTask = allTask?.filter((item: Record<string, any>) => item.missUser); //异常角色
  const { user } = useLoggedInUser();
  const handleNoticeClick = (e: any) => {
    e.stopPropagation();

    fetchNoticeManager({
      instId: processInstanceCode || '',
      userId: processChargeUser || '',
      userName: processChargeUserName || '',
    })
      .then((data: Record<string, any>) => {
        if (!data?.data) {
          message.success('发送失败');
          return;
        }
        message.success(`通知已发送给${processChargeUserName}`);
      })
      .catch(() => {
        message.success('发送失败');
      });
  };

  //会签和或签的数据结构是不一样的，会签有多个task，而或签只有一个task，为了方便，把或签的数据结构处理成会签的
  const isCountersignNode = recordItem?.isCountersignNode;
  if (!isCountersignNode && errorTask.length) {
    //人员遵守一个人一个任务
    const errorType = allTask[0]?.missUserInfo.filter(
      (item: Record<string, any>) => item?.type === 'MISS_USER'
    );
    if (errorType.length) {
      allTask = allTask[0].userList.map((item: Record<string, any>) => {
        if (item?.whetherEnable) {
          return { ...allTask[0], missUser: false, missUserInfo: null, userList: [{ ...item }] };
        } else {
          return { ...allTask[0], userList: [{ ...item }] };
        }
      });
    } else {
      //角色遵守一个角色一个任务
      const arr =
        allTask[0]?.missUserInfo?.map((item: Record<string, any>) => {
          return { ...allTask[0], userList: [], missUserInfo: [{ ...item }] };
        }) || [];

      const firstEle =
        allTask[0].userList && allTask[0].userList.length > 0
          ? { ...allTask[0], missUser: false, missUserInfo: null }
          : null;

      allTask = [firstEle, ...arr].filter(_ => _);
    }
  }
  errorTask = allTask?.filter((item: Record<string, any>) => item.missUser); //异常角色
  //所有的异常信息
  const missUserInfos = flatten(
    errorTask?.map((item: Record<string, any>) => item?.missUserInfo)
  ).filter(_ => _) as Record<string, any>[];

  // 多个角色，其中一个角色没有人
  const uniqErrorArr = [...new Set(missUserInfos?.map((item: any) => item?.type))];

  if (
    allTask.length === errorTask.length &&
    uniqErrorArr.length === 1 &&
    uniqErrorArr[0] === 'NO_ROLE'
  ) {
    // 全部角色不存在
    return (
      <>
        流程配置的角色异常(请流程负责人
        <UserLink
          userId={processChargeUserId}
          userName={processChargeUserName}
          style={{ fontSize: '14px' }}
        />
        {applyUserId === user?.id ? <NoticeBtn handleNoticeClick={handleNoticeClick} /> : null}
        更新流程配置)
      </>
    );
  } else if (
    allTask.length !== errorTask.length &&
    uniqErrorArr.length === 1 &&
    uniqErrorArr[0] === 'NO_ROLE'
  ) {
    //部分角色不存在
    return (
      <>
        流程配置的部分角色不存在(请流程负责人
        <UserLink
          userId={processChargeUserId}
          userName={processChargeUserName}
          style={{ fontSize: '14px' }}
        />
        {applyUserId === user?.id ? <NoticeBtn handleNoticeClick={handleNoticeClick} /> : null}
        更新流程配置)
      </>
    );
  } else if (
    allTask.length === errorTask.length &&
    uniqErrorArr.length === 1 &&
    uniqErrorArr[0] === 'MISS_USER'
  ) {
    // 全部审批人不存在
    return (
      <>
        流程配置的审批人异常(请流程负责人
        <UserLink
          userId={processChargeUserId}
          userName={processChargeUserName}
          style={{ fontSize: '14px' }}
        />
        {applyUserId === user?.id ? <NoticeBtn handleNoticeClick={handleNoticeClick} /> : null}
        转交给代处理人并更新流程配置)
      </>
    );
  } else if (
    allTask.length !== errorTask.length &&
    uniqErrorArr.length === 1 &&
    uniqErrorArr[0] === 'MISS_USER'
  ) {
    // 全部审批人不存在
    return (
      <>
        流程配置的部分审批人异常(请流程负责人
        <UserLink
          userId={processChargeUserId}
          userName={processChargeUserName}
          style={{ fontSize: '14px' }}
        />
        {applyUserId === user?.id ? <NoticeBtn handleNoticeClick={handleNoticeClick} /> : null}
        转交给代处理人并更新流程配置)
      </>
    );
  } else if (uniqErrorArr.length === 1 && uniqErrorArr[0] === 'ROLE_NO_USER') {
    //全部角色存在，但有些角色没有审批人
    const str = getPartApprovalUser(missUserInfos);
    return (
      <>
        流程配置的角色下无审批人(请角色负责人
        {str.map((item, index) => {
          return index === 0 ? item : <>，{item}</>;
        })}
        )
      </>
    );
  } else if (uniqErrorArr.includes('NO_ROLE') && uniqErrorArr.includes('ROLE_NO_USER')) {
    const roleNoUserArr = missUserInfos.filter(item => item?.type === 'ROLE_NO_USER');
    const str = getPartApprovalUser(roleNoUserArr);

    return (
      <>
        流程配置的部分角色异常 & 部分角色下无审批人(请流程负责人
        <UserLink userId={processChargeUserId} userName={processChargeUserName} />
        {applyUserId === user?.id ? <NoticeBtn handleNoticeClick={handleNoticeClick} /> : null}
        更新流程配置，角色负责人
        {str.map((item, index) => {
          return index === 0 ? item : <>,{item}</>;
        })}
        )
      </>
    );
  } else {
    return <></>;
  }
}

function getPartApprovalUser(missUserInfos: Record<string, any>[]) {
  return missUserInfos.map(item => {
    if (item.resourceName) {
      return (
        <>
          <UserLink userId={item?.currId} />
          维护{item.resourceName}的{item.roleName}
        </>
      );
    } else {
      return (
        <>
          <UserLink userId={item?.currId} />
          维护{item.roleName}
        </>
      );
    }
  });
}
