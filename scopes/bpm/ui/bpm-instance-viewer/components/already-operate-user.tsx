import dayjs from 'dayjs';
import React from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { ProcessUser, TaskRedirectInfo } from '@manyun/bpm.model.bpm-instance';

import styles from '../bpm-instance-viewer.module.less';
import { generateUserStatusInAlert, getStatusTag } from '../utils.js';
import { FileShow } from './file-show.js';
import { NoteInfo } from './note-info.js';
import { PartUser } from './part-user.js';
import { UserAvatar } from './user-avatar.js';

const DEFAULT_MAX_TIME_VALUE = 9999999999999;

interface GenerateAlreadyUserParams {
  alreadyOperateUsers: {
    userData: ProcessUser & Record<string, any>;
    type: string;
    redirectAlreadyUsers: TaskRedirectInfo[];
  }[];
  applyUser: number;
}

// 已处理人员列表
export function AlreadyOperateUser({ alreadyOperateUsers, applyUser }: GenerateAlreadyUserParams) {
  return (
    <Space direction="vertical" style={{ width: '100%' }} size={12}>
      {alreadyOperateUsers
        .sort((pre: any, pro: any) => {
          let preNumber = 0;
          let proNumber = 0;
          preNumber = dayjs(pre.userData.date).valueOf();
          proNumber = dayjs(pro.userData.date).valueOf();
          if (pre.type === 'AwaitExecute') {
            preNumber =
              pre.userData.date === 'MAX' ? DEFAULT_MAX_TIME_VALUE : Number(pre.userData.date);
          }
          if (pro.type === 'AwaitExecute') {
            proNumber =
              pro.userData.date === 'MAX' ? DEFAULT_MAX_TIME_VALUE : Number(pre.userData.date);
          }

          return preNumber - proNumber;
        })
        .map(operateUser => {
          const { userData, type } = operateUser;

          return type === 'AlreadyExecute' ? (
            <Space
              key={`${userData.id}${userData.date}`}
              direction="vertical"
              style={{ width: '100%' }}
            >
              {type === 'AlreadyExecute' && (
                <Alert
                  key={`${userData.id}${userData.date}`}
                  style={{ width: '100%', background: '#fafafa', border: 0, padding: '8px 16px' }}
                  description={
                    <div>
                      <div
                        style={{
                          width: '100%',
                          display: 'flex',
                          justifyContent: 'space-between',
                          alignContent: 'center',
                          marginBottom: 8,
                        }}
                      >
                        <Space style={{ flex: 1 }} size={8}>
                          <UserAvatar
                            user={{
                              taskResult: userData?.taskResult,
                              isStep: false,
                              isCollapseAvatar: true,
                              userAvatarId: Number(userData!.id),
                              applyUser,
                              whetherEnable: true,
                            }}
                          />
                          <Space size={8}>
                            <User.Link id={Number(userData.id)} className={styles.userLink} />
                            {generateUserStatusInAlert(userData.taskResult)
                              ? getStatusTag(
                                  generateUserStatusInAlert(userData.taskResult),
                                  false,
                                  12
                                )
                              : ''}
                          </Space>
                        </Space>
                        <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                          {userData.date && dayjs(userData.date).format('MM.DD HH:mm')}
                        </Typography.Text>
                      </div>

                      <Space
                        direction="vertical"
                        align="start"
                        style={{
                          fontSize: '14px',
                          width: '100%',
                          minWidth: '250px',
                        }}
                      >
                        {userData.remark ? <NoteInfo info={userData.remark}></NoteInfo> : null}
                        {Array.isArray(userData?.taskFileInfoList) &&
                        userData?.taskFileInfoList?.length ? (
                          <FileShow
                            fileInfoList={userData.taskFileInfoList}
                            style={{ marginBottom: 8 }}
                          />
                        ) : null}
                      </Space>
                      {userData?.commentAssigner ? (
                        <PartUser commentAssigner={userData?.commentAssigner} />
                      ) : null}
                    </div>
                  }
                />
              )}
            </Space>
          ) : null;
        })}
    </Space>
  );
}
