import React from 'react';

export default ({ className }: Record<string, any>) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className={className}
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8 16C12.4183 16 16 12.4183 16 8C16 3.58167 12.4183 -2.98023e-07 8 -2.98023e-07C3.58167 -2.98023e-07 0 3.58167 0 8C0 12.4183 3.58167 16 8 16Z"
      fill="white"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M8.00043 15.309C12.0373 15.309 15.3097 12.0366 15.3097 7.9997C15.3097 3.96285 12.0373 0.69043 8.00043 0.69043C3.96358 0.69043 0.691162 3.96285 0.691162 7.9997C0.691162 12.0366 3.96358 15.309 8.00043 15.309Z"
      fill="#8C8C8C"
    />
    <rect
      x="12.7664"
      y="2.23608"
      width="1.4"
      height="14.9021"
      transform="rotate(45 12.7664 2.23608)"
      fill="white"
    />
  </svg>
);
