import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import type { BpmInstance, ProcessRecord } from '@manyun/bpm.model.bpm-instance';
import { ApprovalStatus, NodeStatus, NodeType } from '@manyun/bpm.model.bpm-instance';

export type AwaitOperationPeopleTagProps = {
  bpmInstance: BpmInstance;
};

export function AwaitOperationPeopleTag({ bpmInstance }: AwaitOperationPeopleTagProps) {
  const filterAndSortRecords = (operationRecords: ProcessRecord[]) => {
    return operationRecords
      .filter(item => item.nodeId.indexOf('Gateway') === -1 && item.operationType !== 'NOT_EXECUTE')
      .map(item => {
        if (item.nodeType === NodeType.CommentNode) {
          return {
            ...item,
            type: NodeType.CommentNode,
          };
        }
        if (item.nodeId.indexOf('CC_') === -1) {
          return {
            ...item,
            type: NodeType.UserTask,
            operationTasks: item!.operationTasks!.filter(item => item.userList),
          };
        } else {
          return { ...item, type: NodeType.CcTask };
        }
      });
  };
  const getText = () => {
    if (bpmInstance.operationRecords && bpmInstance.operationRecords.length > 0) {
      const sortedRecords = filterAndSortRecords(bpmInstance.operationRecords).filter(
        recordItem =>
          recordItem.type === NodeType.UserTask && recordItem.operationType === NodeStatus.Execute
      );
      if (sortedRecords.length > 0 && sortedRecords[0].isCountersignNode) {
        const { operationTasks } = sortedRecords[0];
        if (operationTasks && operationTasks.length >= 1) {
          const { missUser, missUserInfo } = operationTasks[0];
          if (missUser) {
            return (
              <Typography.Text>
                待<UserLink userId={Number(missUserInfo[0].currId)} />
                审批
              </Typography.Text>
            );
          }
          return '待会签审批';
        }
      } else {
        if (sortedRecords.length > 0) {
          const { operationTasks } = sortedRecords[0];
          if (operationTasks?.length === 1 && operationTasks[0]?.userList?.length === 1) {
            const { missUser, missUserInfo } = operationTasks[0];
            if (missUser) {
              return (
                <Typography.Text>
                  待<UserLink userId={Number(missUserInfo[0].currId)} />
                  审批
                </Typography.Text>
              );
            } else {
              return `待${operationTasks[0].userList[0].userName}审批`;
            }
          } else {
            return '待或签审批';
          }
        }
      }
    }
    return '--';
  };
  return bpmInstance.status === ApprovalStatus.Approving ? (
    <Tag color="warning">{getText()}</Tag>
  ) : null;
}
