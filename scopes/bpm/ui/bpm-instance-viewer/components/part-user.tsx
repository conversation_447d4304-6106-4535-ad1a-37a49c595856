import { DownOutlined, RightOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Space } from '@manyun/base-ui.ui.space';

interface IPartUserProps {
  commentAssigner: string;
}

export function PartUser({ commentAssigner }: IPartUserProps) {
  const commentUsers = commentAssigner?.split(',') || [];
  const [v, setV] = useState(false);

  const handleShowClick = () => {
    setV(!v);
  };

  return (
    <div style={{ width: '100%', marginTop: '8px' }}>
      <div>
        <span style={{ color: 'rgba(0,0,0,0.65)', fontSize: '14px' }}>仅部分人可见</span>
        {v ? (
          <DownOutlined style={{ opacity: 0.45, marginLeft: '4px' }} onClick={handleShowClick} />
        ) : (
          <RightOutlined style={{ opacity: 0.45, marginLeft: '4px' }} onClick={handleShowClick} />
        )}
      </div>
      {v ? (
        <Space
          style={{
            width: '100%',
            background: '#fafafa',
            padding: '10px 16px',
            marginTop: '4px',
            gap: '16px',
            flexWrap: 'wrap',
          }}
        >
          {commentUsers.map(item => (
            <User
              id={Number(item)}
              size={32}
              showName={true}
              nameType="secondary"
              nameStyle={{ fontSize: '10px', display: 'flex', justifyContent: 'center' }}
            />
          ))}
        </Space>
      ) : null}
    </div>
  );
}
