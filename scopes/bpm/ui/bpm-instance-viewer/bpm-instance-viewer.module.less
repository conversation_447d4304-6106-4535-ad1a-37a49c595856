@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.bpmViewer {
  background: var(--viewer-bg);
  border-radius: 8px;
  padding: 16px 0 0px 0;
}

.collapseTitle {
  display: flex;
  flex-direction: column;
}

.collapseTitle :nth-child(1) {
  font-size: 16px;
}
.collapseTitle :nth-child(2) {
  font-size: 14px;
}

.avatar {
  position: relative;
}

.commanderIcon {
  position: absolute;
  right: -8.53px;
  bottom: -4.53px;
  color: ~'var(--@{prefixCls}-success-color)';
}

:global(.anticon).avatarIcon {
  position: absolute;
  right: -8.53px;
  bottom: -4.53px;
  color: ~'var(--manyun-primary-color)';
}

:global(.anticon).avatarIconRefuse {
  position: absolute;
  right: -3.53px;
  bottom: -3.53px;
  color: ~'var(--@{prefixCls}-error-color)';
}

:global(.anticon).avatarStepIcon {
  position: absolute;
  right: -3.53px;
  bottom: -3.53px;
  width: 16px;
  height: 16px;
  color: ~'var(--@{prefixCls}-success-color)';
}
:global(.anticon).avatarStepIconRefuse {
  position: absolute;
  right: -3.53px;
  bottom: -3.53px;
  width: 16px;
  height: 16px;
  color: ~'var(--@{prefixCls}-error-color)';
}
:global(.anticon).avatarCommentIcon {
  position: absolute;
  right: -3.53px;
  bottom: -3.53px;
  width: 16px;
  height: 16px;
  color: ~'var(--@{prefixCls}-primary-color)';
}

.userLink {
  color: rgba(0, 0, 0, 0.85) !important;

  &:hover {
    color: var(--manyun-info-color) !important;
  }
}

.bpmCollapse {
  width: '100%';
  margin-bottom: 12px;

  .bpmCollapsePanel {
    & > div {
      padding: 0 !important;

      & > div {
        padding: 0 !important;
      }
    }
  }
}
