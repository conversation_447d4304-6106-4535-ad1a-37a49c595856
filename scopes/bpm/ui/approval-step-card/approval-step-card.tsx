/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-18
 *
 * @packageDocumentation
 */
import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';

import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalProcessPreview } from '@manyun/bpm.ui.bpm-instance-viewer';
import { BpmInstanceViewer } from '@manyun/ticket.ui.bpm-instance-viewer';

export type ApprovalFilePreviewWithContainerProps = {
  code: string;
  title: React.ReactNode;
};

export function ApprovalFilePreviewWithContainer({
  code,
  title,
}: ApprovalFilePreviewWithContainerProps) {
  return (
    <FilePreviewWithContainer
      file={{
        ext: '.png',
        src: `/api/workflow/process/export/img?workflowId=${code}`,
        name: '审批链.png',
      }}
    >
      <div>{title}</div>
    </FilePreviewWithContainer>
  );
}

export type ApprovalStepCardProps = {
  baseInfo: BpmInstance;
  title?: React.ReactNode;
  showFirstStep?: boolean;
  extraClickEvent?: () => void;
  onSuccess?: () => void;
};

export function ApprovalStepCard({
  title = '流程日志',
  baseInfo,
  showFirstStep = true,
  extraClickEvent,
  onSuccess,
}: ApprovalStepCardProps) {
  return (
    <Card
      title={title}
      extra={
        <div
          onClick={() => {
            extraClickEvent && extraClickEvent();
          }}
        >
          <ApprovalProcessPreview processId={baseInfo.code} />
        </div>
      }
    >
      <BpmInstanceViewer
        showFirstStep={showFirstStep}
        bpmInstance={baseInfo}
        showCarbonCopyStep={false}
        onSuccess={onSuccess}
      />
    </Card>
  );
}
