import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type CustomApprovalTypesSelectProps = Pick<
  SelectProps,
  | 'mode'
  | 'allowClear'
  | 'labelInValue'
  | 'className'
  | 'placement'
  | 'size'
  | 'disabled'
  | 'style'
  | 'className'
>;

export const CustomApprovalTypesSelect = React.forwardRef<
  RefSelectProps,
  CustomApprovalTypesSelectProps
>((props, ref) => {
  const [{ data, loading }, { readMetaData }] = useMetaData(MetaType.COMMON_APPROVAL_TYPE);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return <Select ref={ref} {...props} loading={loading} options={data.data} />;
});

CustomApprovalTypesSelect.displayName = 'CustomApprovalTypesSelect';
