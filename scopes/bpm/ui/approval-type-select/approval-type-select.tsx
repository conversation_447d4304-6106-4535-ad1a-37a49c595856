import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useApprovalTypes } from '@manyun/bpm.hook.use-approval-types';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';

export type ApprovalTypeSelectProps = SelectProps;

export const ApprovalTypeSelect = React.forwardRef<RefSelectProps, ApprovalTypeSelectProps>(
  ({ ...selectProps }, ref) => {
    const [configUtil] = useConfigUtil();
    const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket') as any;
    const showNewDetail = ticketScopeCommonConfigs?.changes?.features?.showNewDetail;
    const { approvalTypeTextKeyMap, approvalTypes } = useApprovalTypes();

    let filteredApprovalTypes: Record<string, any> = approvalTypes;

    if (!showNewDetail) {
      //GOC环境把：线上变更，线上变更总结，线上执行延期，线上变更模版，风险延期，风险单升级移除
      filteredApprovalTypes = Object.fromEntries(
        Object.entries(approvalTypes).filter(
          ([key]) =>
            key !== 'CHANGE_ONLINE_APPLY' &&
            key !== 'CHANGE_ONLINE_SUMMERY' &&
            key !== 'CHANGE_ONLINE_DELAY' &&
            key !== 'CHANGE_ONLINE_TEMPLATE' &&
            key !== 'RISK_TIME_EXTENSION' &&
            key !== 'RISK_ROC_UPGRADE'
        )
      );
    } else {
      //阳高环境把老的变更总结，变更申请，标准变更模版录入，标准变更模版延期，移除
      filteredApprovalTypes = Object.fromEntries(
        Object.entries(approvalTypes).filter(
          ([key]) =>
            key !== 'CHANGE_TEMPLATE' &&
            key !== 'CHANGE_SUMMERY' &&
            key !== 'CHANGE' &&
            key !== 'STANDARD_CHANGE_TEMPLATE' &&
            key !== 'STANDARD_CHANGE_TEMPLATE_POSTPONE'
        ) as any
      );
    }

    return (
      <Select
        ref={ref}
        aria-label="Approve_Type"
        showSearch
        optionFilterProp="label"
        {...selectProps}
        options={Object.values(filteredApprovalTypes).map(value => {
          return {
            value,
            label: approvalTypeTextKeyMap[value],
          };
        })}
      />
    );
  }
);
ApprovalTypeSelect.displayName = 'ApprovalTypeSelect';
