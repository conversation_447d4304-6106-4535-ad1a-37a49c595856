import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { UserSelect } from '@manyun/iam.ui.user-select';

import { approveCenterService } from '@manyun/dc-brain.legacy.services';

const { TextArea } = Input;
export function RedirectModalButton({
  instId,
  taskId,
  userId,
  blockGuid,
  filterUserIdList,
  text,
  type,
  compact,
  onSuccess,
}) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  return (
    <>
      <Button
        type={type}
        compact={compact}
        onClick={() => {
          setOpen(true);
        }}
      >
        {text}
      </Button>
      <Modal
        title={text}
        open={open}
        destroyOnClose
        okText="确认"
        okButtonProps={{ loading }}
        onOk={() => {
          form.validateFields().then(async values => {
            const { redirectUser, desc } = values;
            setLoading(true);

            const { error } = await approveCenterService.redirectApproveProcess({
              userId,
              instId,
              taskId,
              redirectUserId: redirectUser.id,
              desc,
            });
            setLoading(false);
            if (error) {
              message.error(error);
              return;
            }
            message.success('操作成功! ');
            setOpen(false);
            onSuccess();
          });
        }}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <Form form={form} preserve={false} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="转交人"
            name="redirectUser"
            required
            requiredMark={undefined}
            rules={[{ required: true, message: '转交人必选' }]}
          >
            <UserSelect
              blockGuid={blockGuid}
              disabledKeys={filterUserIdList}
              placeholder="请搜索转交人名字"
              style={{ width: '394px' }}
              userState="in-service"
            />
          </Form.Item>

          <Form.Item
            label="备注"
            name="desc"
            rules={[{ max: 500, message: '最多输入 500 个字符！' }]}
          >
            <TextArea rows={4} maxLength={500} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
