import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';

import type { Moment } from 'moment';

import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { ApprovalCenterSearchValues } from '@manyun/bpm.state.approval-center';
import {
  resetSearchValuesActionCreator,
  setSearchValuesActionCreator,
} from '@manyun/bpm.state.approval-center';
import { ApprovalTypeSelect } from '@manyun/bpm.ui.approval-type-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

type Values = ApprovalCenterSearchValues & {
  applicantId: { value: number; key: string };
  startTime: Moment[];
};

// eslint-disable-next-line no-empty-pattern
export function CarbonCopySearchForm() {
  const [form] = Form.useForm<Values>();
  const dispatch = useDispatch();
  const onSearch = useCallback(
    params => {
      dispatch(setSearchValuesActionCreator(params));
    },
    [dispatch]
  );
  const onReset = useCallback(() => {
    dispatch(resetSearchValuesActionCreator());
  }, [dispatch]);
  return (
    <QueryFilter<Values>
      form={form}
      items={[
        {
          label: '审批ID',
          name: 'instId',
          control: <Input aria-label="instId" allowClear />,
        },

        {
          label: '标题',
          name: 'title',
          control: <Input aria-label="title" allowClear />,
        },
        {
          label: '类型',
          name: 'bizType',
          control: <ApprovalTypeSelect allowClear />,
        },
        {
          label: '对应单号',
          name: 'bizId',
          control: <Input aria-label="bizId" allowClear />,
        },
        {
          label: '位置',
          name: 'blockGuid',
          control: <LocationTreeSelect allowClear />,
        },
        {
          label: '发起人',
          name: 'applicantId',
          control: <UserSelect allowClear />,
        },
        {
          label: '发起时间',
          name: 'startTime',
          span: 2,
          control: <DatePicker.RangePicker />,
        },
      ]}
      onSearch={value => {
        onSearch({
          ...value,
          startTime: changeDateIntoString(value?.startTime),
          applicantId: value?.applicantId?.value?.toString(),
        });
      }}
      onReset={onReset}
    />
  );
}

const changeDateIntoString = (dateList?: Moment[]) => {
  if (dateList?.length) {
    return [dateList[0].startOf('day').toISOString(), dateList[1].endOf('day').toISOString()];
  }
  return;
};
