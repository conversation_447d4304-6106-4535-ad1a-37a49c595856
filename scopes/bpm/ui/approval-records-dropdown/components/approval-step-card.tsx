import React, { useCallback, useEffect } from 'react';

import { Spin } from '@manyun/base-ui.ui.spin';
import { useLazyBusinessOrderApprovalDetail } from '@manyun/bpm.gql.client.approval';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { BpmInstanceViewer } from '@manyun/ticket.ui.bpm-instance-viewer';

export type ApprovalStepCardProps = {
  approvalInfo?: {
    id: string;
    relateBusinessOrderNumber: string;
    relateBusinessOrderType: string;
  };
  showFirstStep?: boolean;
};

export function ApprovalStepCard({ approvalInfo, showFirstStep = true }: ApprovalStepCardProps) {
  const [configUtil] = useConfigUtil();
  const showRiskRegister = (configUtil as Record<string, any>).getScopeCommonConfigs('legacy')?.risk
    ?.showRiskRegister;

  const [getApprovalDetail, { data: approvalDetailData, loading: approvalDetailLoading }] =
    useLazyBusinessOrderApprovalDetail();
  const fetchApprovalDetail = useCallback(() => {
    if (approvalInfo?.id && approvalInfo?.relateBusinessOrderType) {
      getApprovalDetail({
        variables: {
          instId: approvalInfo.id,

          permissionType: approvalInfo.relateBusinessOrderType,
        },
      });
    }
  }, [approvalInfo?.id, approvalInfo?.relateBusinessOrderType, getApprovalDetail]);
  useEffect(() => {
    fetchApprovalDetail();
  }, [fetchApprovalDetail]);

  return (
    <Spin spinning={approvalDetailLoading}>
      {approvalDetailData?.businessOrderApprovalDetail && (
        <BpmInstanceViewer
          showFirstStep={showFirstStep}
          bpmInstance={approvalDetailData.businessOrderApprovalDetail as unknown as BpmInstance}
          showCarbonCopyStep
          hideReason={showRiskRegister}
        />
      )}
    </Spin>
  );
}
