import get from 'lodash.get';
import React, { useEffect, useState } from 'react';

import { ApprovalStatus } from '@manyun/bpm.model.bpm-instance';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { fetchBpmInstance } from '@manyun/bpm.service.fetch-bpm-instance';
import { CommentModalButton } from '@manyun/bpm.ui.comment-modal-button';
import type { CommentModalButtonProps } from '@manyun/bpm.ui.comment-modal-button';
import {
  ManualCarbonCopyModal,
  didHappenWithinOneMonth,
} from '@manyun/bpm.ui.manual-carbon-copy-modal';

export function InnerApprovalModalButton({
  instId,
  isOuterApprovalSingle,
  onSuccess,
  onClick,
}: Pick<CommentModalButtonProps, 'instId' | 'onSuccess' | 'onClick'> & {
  isOuterApprovalSingle: boolean;
}) {
  const [baseInfo, setBaseInfo] = useState<BpmInstance>();
  const applyTime = get(baseInfo, 'applyTime');
  const applyUserId = get(baseInfo, 'applyUser');
  const status = get(baseInfo, 'status');

  const commentButtonCase = !!baseInfo && status !== ApprovalStatus.Revoke;
  const carbonCopyButtonCase =
    !!baseInfo &&
    !!applyTime &&
    status === ApprovalStatus.Pass &&
    didHappenWithinOneMonth(applyTime);

  useEffect(() => {
    (async function () {
      if (!instId) {
        return;
      }
      const { data } = await fetchBpmInstance({ code: instId });
      if (data) {
        setBaseInfo(data);
      }
    })();
  }, [instId]);

  return (
    <>
      {carbonCopyButtonCase && (
        <ManualCarbonCopyModal
          instId={instId}
          type="link"
          applyTime={applyTime}
          extraSuccessMessage="请在审批记录中查看更新进度"
          isOuterApprovalSingle={isOuterApprovalSingle}
          onSuccess={onSuccess}
          onClick={onClick}
        />
      )}
      {commentButtonCase && applyUserId && (
        <CommentModalButton
          instId={instId}
          applyUserId={applyUserId}
          extraSuccessMessage="请在审批记录中查看更新进度"
          type="link"
          isOuterApprovalSingle={isOuterApprovalSingle}
          onSuccess={onSuccess}
          onClick={onClick}
        />
      )}
    </>
  );
}
