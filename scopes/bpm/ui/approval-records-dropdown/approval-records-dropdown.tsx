/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-24
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyBusinessOrderApprovalIds } from '@manyun/bpm.gql.client.approval';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ApprovalProcessPreview } from '@manyun/bpm.ui.bpm-instance-viewer';
import { useApps } from '@manyun/dc-brain.context.apps';
import { Link } from '@manyun/dc-brain.navigation.link';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';

import { ApprovalStepCard } from './components/approval-step-card';
import { InnerApprovalModalButton } from './components/inner-approval-modal-button.js';

export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const collapsePrefixCls = `${prefixCls}-collapse`;

    return css`
      // overrides collapse's style
      .${collapsePrefixCls}-icon-position-end
        > .${collapsePrefixCls}-item
        > .${collapsePrefixCls}-header:first-child {
        background-color: #e6f7ff;
        padding: 4px 12px 4px 12px;
      }
      .${collapsePrefixCls}-content > .${collapsePrefixCls}-content-box {
        padding: 0;
      }
    `;
  }}
`;
export type ApprovalRecordsDropdownProps = {
  /**@deprecated 无用属性 */
  baseInfo?: BpmInstance;
  /** 业务单信息，包含业务类型和业务单单号以及审批类型 */
  /** status 需要传业务单的status，此组件根据业务单状态的变化rerender */
  businessOrderInfo?: {
    type: string;
    taskNumber: string;
    approvalPermissionType: string;
    status: string;
  };
  /** dropdown展示卡片的bodyStyle设置 */
  cardStyle?: React.CSSProperties;
  /**@deprecated 无用属性 */
  onCallBack?: () => void;
  title?: string;
};
export function ApprovalRecordsDropdown({
  businessOrderInfo,
  cardStyle,
  title,
}: ApprovalRecordsDropdownProps) {
  const [open, setOpen] = useState(false);
  const [activeKey, setActiveKey] = useState<string>();
  const { dcbase } = useApps();

  const [getRelateApprovalIds, { data: approvalIds, loading: approvalIdsLoading, refetch }] =
    useLazyBusinessOrderApprovalIds();

  const onOpenChange = (open: boolean) => {
    setOpen(open);
  };
  const fetchRelateApprovalIds = useCallback(() => {
    if (
      businessOrderInfo?.taskNumber &&
      businessOrderInfo?.type &&
      businessOrderInfo?.approvalPermissionType
    ) {
      getRelateApprovalIds({
        variables: {
          processType: businessOrderInfo.type,
          bizId: businessOrderInfo.taskNumber,
          permissionType: businessOrderInfo.approvalPermissionType,
        },
      });
    }
  }, [
    businessOrderInfo?.taskNumber,
    businessOrderInfo?.type,
    businessOrderInfo?.approvalPermissionType,
    getRelateApprovalIds,
  ]);

  useEffect(() => {
    fetchRelateApprovalIds();
  }, [fetchRelateApprovalIds]);
  useEffect(() => {
    // 业务单发生变化后，则需要重新查询一遍审批ids
    if (businessOrderInfo?.status || open) {
      refetch();
    }
  }, [businessOrderInfo?.status, refetch, open]);

  useEffect(() => {
    if (
      approvalIds &&
      approvalIds.businessOrderApprovalIds &&
      approvalIds.businessOrderApprovalIds.data &&
      approvalIds?.businessOrderApprovalIds?.data?.length > 0 &&
      open
    ) {
      setActiveKey(approvalIds.businessOrderApprovalIds.data[0]);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [approvalIds?.businessOrderApprovalIds?.data?.length, open]);

  return (
    <Theme prefixCls="manyun">
      <Dropdown
        open={open}
        dropdownRender={() => (
          <div style={{ width: 388 }}>
            {approvalIds &&
              approvalIds.businessOrderApprovalIds &&
              approvalIds.businessOrderApprovalIds.data &&
              approvalIds.businessOrderApprovalIds.data.length > 0 && (
                <Card
                  title="审批记录"
                  bodyStyle={{
                    maxHeight: '56vh',
                    overflow: 'hidden',
                    overflowY: 'auto',
                    ...cardStyle,
                  }}
                  extra={
                    <Space>
                      <InnerApprovalModalButton
                        instId={approvalIds.businessOrderApprovalIds.data[0]}
                        isOuterApprovalSingle={
                          approvalIds.businessOrderApprovalIds.data.length === 1
                        }
                        onSuccess={refetch}
                        onClick={() => {
                          setOpen(false);
                        }}
                      />
                      <div
                        onClick={() => {
                          setOpen(false);
                        }}
                      >
                        <ApprovalProcessPreview
                          processId={approvalIds.businessOrderApprovalIds.data[0]}
                        />
                      </div>
                    </Space>
                  }
                >
                  <Spin spinning={approvalIdsLoading}>
                    {open && (
                      <StyledContainer>
                        <Collapse
                          bordered={false}
                          expandIconPosition="end"
                          ghost
                          activeKey={activeKey}
                          onChange={key => {
                            setActiveKey(key as string);
                          }}
                        >
                          {approvalIds.businessOrderApprovalIds.data.map((approval, index) => (
                            <Collapse.Panel
                              key={approval}
                              className="ticketNodeCollapsePanel"
                              style={{
                                marginBottom:
                                  index !== approvalIds.businessOrderApprovalIds!.data!.length
                                    ? 8
                                    : 0,
                              }}
                              header={
                                <>
                                  <Typography.Text>审批ID:</Typography.Text>
                                  <Link
                                    target="_blank"
                                    external
                                    href={`${dcbase.baseURL}${generateBPMRoutePath({ id: approval }).replace(/^\/+/, '')}`}
                                  >
                                    {approval}
                                  </Link>
                                </>
                              }
                              collapsible="icon"
                            >
                              <ApprovalStepCard
                                approvalInfo={{
                                  id: approval,
                                  relateBusinessOrderNumber: businessOrderInfo!.taskNumber,
                                  relateBusinessOrderType:
                                    businessOrderInfo!.approvalPermissionType,
                                }}
                                showFirstStep
                              />
                            </Collapse.Panel>
                          ))}
                        </Collapse>
                      </StyledContainer>
                    )}
                  </Spin>
                </Card>
              )}
          </div>
        )}
        placement="bottomRight"
        onOpenChange={onOpenChange}
      >
        <Typography.Link>{title || '审批记录'}</Typography.Link>
      </Dropdown>
    </Theme>
  );
}
