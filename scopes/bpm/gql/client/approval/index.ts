// Auto-generated Types
// ------
export type { BpmProcess, BpmProcessUserInfo } from './generated-types/graphql';

// Queries
// ------
export {
  GET_BUSINESS_ORDER_APPROVAL_IDS,
  useBusinessOrderApprovalIds,
  useLazyBusinessOrderApprovalIds,
} from './queries/business-order-approval-ids.query';

export {
  GET_ACTIVE_APPROVAL_SCENE,
  useActiveApprovalScene,
  useLazyActiveApprovalScene,
} from './queries/active-approval-scene.query';

export {
  GET_BUSINESS_ORDER_APPROVAL_DETAIL,
  useBusinessOrderApprovalDetail,
  useLazyBusinessOrderApprovalDetail,
} from './queries/business-order-approval-detail.query';

export { GET_BPM_PROCESS, useBpmProcess, useLazyBpmProcess } from './queries/bpm-process.query';
// Mutations
// ------
