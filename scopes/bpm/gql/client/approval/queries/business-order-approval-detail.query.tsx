import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  BpmInstanceJson,
  Maybe,
  QueryBusinessOrderApprovalDetailArgs,
} from '../generated-types/graphql';

export type QueryBusinessOrderApprovalDetailData = {
  businessOrderApprovalDetail: Maybe<BpmInstanceJson>;
};

export const GET_BUSINESS_ORDER_APPROVAL_DETAIL: DocumentNode = gql`
  query GetBusinessOrderApprovalDetail($instId: String!, $permissionType: String!) {
    businessOrderApprovalDetail(instId: $instId, permissionType: $permissionType) {
      code
      title
      bizId
      bizType
      content
      oaType
      idc
      blockGuid
      room
      reason
      formJson
      status
      applyUser
      applyUserId
      applyUserName
      creatorId
      creatorName
      applyTime
      processChargeUser
      processChargeUserName
      operationRecords {
        operationName
        operationType
        operationTasks {
          commentAssigner
          date
          taskResult
          remark
          taskId
          missUser
          missUserInfo {
            type
            missId
            roleName
            currId
            resourceId
            resourceName
          }
          userList {
            id
            userName
            jobNo
            mobile
            email
            whetherEnable
          }
          taskRedirectInfoList {
            sourceUserId
            targetUserId
            redirectTime
            desc
          }
          taskFileInfoList {
            fileFormat
            fileName
            filePath
            fileSize
            fileType
            gmtCreate
            gmtModified
            id
            targetId
            targetType
            uploadBy
            uploadByName
            uploadTime
          }
        }
        nodeId
        ccUserList {
          id
          userName
          ccMsgIsRead
          jobNo
          mobile
          email
        }
        ccInfoList {
          activityId
          ccTime
          ccUserList {
            id
            userName
            ccMsgIsRead
            jobNo
            mobile
            email
          }
          description
          fileInfoList {
            fileFormat
            fileName
            filePath
            fileSize
            fileType
            gmtCreate
            gmtModified
            id
            targetId
            targetType
            uploadBy
            uploadByName
            uploadTime
          }
        }
        isCountersignNode
        nodeType
        processCommentInfo {
          commentTime
          content
          id
          isDeleted
          personId
          personName
          deleteTime
          fileInfoList {
            fileFormat
            fileName
            filePath
            fileSize
            fileType
            gmtCreate
            gmtModified
            id
            targetId
            targetType
            uploadBy
            uploadByName
            uploadTime
          }
        }
      }
      xml
      sla
      bizTypeSla
      msgId
      msgParentType
      msgSecondType
      formattedContent {
        label
        value
      }
      attachmentType
    }
  }
`;

export function useBusinessOrderApprovalDetail(
  options?: QueryHookOptions<
    QueryBusinessOrderApprovalDetailData,
    QueryBusinessOrderApprovalDetailArgs
  >
): QueryResult<QueryBusinessOrderApprovalDetailData, QueryBusinessOrderApprovalDetailArgs> {
  return useQuery(GET_BUSINESS_ORDER_APPROVAL_DETAIL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyBusinessOrderApprovalDetail(
  options?: LazyQueryHookOptions<
    QueryBusinessOrderApprovalDetailData,
    QueryBusinessOrderApprovalDetailArgs
  >
): LazyQueryResultTuple<
  QueryBusinessOrderApprovalDetailData,
  QueryBusinessOrderApprovalDetailArgs
> {
  return useLazyQuery(GET_BUSINESS_ORDER_APPROVAL_DETAIL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
