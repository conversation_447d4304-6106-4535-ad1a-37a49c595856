import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { BpmProcessResponse, Maybe, QueryBpmProcessArgs } from '../generated-types/graphql';

export type QueryBpmProcessData = {
  bpmProcess: Maybe<BpmProcessResponse>;
};

export const GET_BPM_PROCESS: DocumentNode = gql`
  # Write your query or mutation here
  query GetBpmProcess(
    $processType: String!
    $idcTag: String
    $blockGuidList: [String!]
    $processParams: ProcessParamsInput
  ) {
    bpmProcess(
      processType: $processType
      idcTag: $idcTag
      blockGuidList: $blockGuidList
      processParams: $processParams
    ) {
      processVersion
      processNodeInfoList {
        id
        name
        edit
        type
        userInfoList {
          id
          userId
          userName
          recommend
          isDuty
        }
      }
      enable
    }
  }
`;

export function useBpmProcess(
  options?: QueryHookOptions<QueryBpmProcessData, QueryBpmProcessArgs>
): QueryResult<QueryBpmProcessData, QueryBpmProcessArgs> {
  return useQuery(GET_BPM_PROCESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyBpmProcess(
  options?: LazyQueryHookOptions<QueryBpmProcessData, QueryBpmProcessArgs>
): LazyQueryResultTuple<QueryBpmProcessData, QueryBpmProcessArgs> {
  return useLazyQuery(GET_BPM_PROCESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
