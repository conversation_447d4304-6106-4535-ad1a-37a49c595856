import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  ActiveApprovalScene,
  Maybe,
  QueryActiveApprovalSceneArgs,
} from '../generated-types/graphql';

export type QueryActiveApprovalSceneData = {
  activeApprovalScene: Maybe<ActiveApprovalScene>;
};

export const GET_ACTIVE_APPROVAL_SCENE: DocumentNode = gql`
  # Write your query or mutation here
  query GetActiveApprovalScene($processType: String!) {
    activeApprovalScene(processType: $processType) {
      data {
        id
        oaType
        processModel
        processType
        processModelCode
      }
    }
  }
`;

export function useActiveApprovalScene(
  options?: QueryHookOptions<QueryActiveApprovalSceneData, QueryActiveApprovalSceneArgs>
): QueryResult<QueryActiveApprovalSceneData, QueryActiveApprovalSceneArgs> {
  return useQuery(GET_ACTIVE_APPROVAL_SCENE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyActiveApprovalScene(
  options?: LazyQueryHookOptions<QueryActiveApprovalSceneData, QueryActiveApprovalSceneArgs>
): LazyQueryResultTuple<QueryActiveApprovalSceneData, QueryActiveApprovalSceneArgs> {
  return useLazyQuery(GET_ACTIVE_APPROVAL_SCENE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
