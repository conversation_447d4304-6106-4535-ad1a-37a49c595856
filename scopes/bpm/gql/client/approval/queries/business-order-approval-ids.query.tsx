import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  BusinessOrderApprovalIdsResponse,
  Maybe,
  QueryBusinessOrderApprovalIdsArgs,
} from '../generated-types/graphql';

export type QueryBusinessOrderApprovalIdsData = {
  businessOrderApprovalIds: Maybe<BusinessOrderApprovalIdsResponse>;
};

export const GET_BUSINESS_ORDER_APPROVAL_IDS: DocumentNode = gql`
  query GetBusinessOrderApprovalIds(
    $bizId: String!
    $processType: String!
    $permissionType: String!
  ) {
    businessOrderApprovalIds(
      bizId: $bizId
      processType: $processType
      permissionType: $permissionType
    ) {
      data
      total
    }
  }
`;

export function useBusinessOrderApprovalIds(
  options?: QueryHookOptions<QueryBusinessOrderApprovalIdsData, QueryBusinessOrderApprovalIdsArgs>
): QueryResult<QueryBusinessOrderApprovalIdsData, QueryBusinessOrderApprovalIdsArgs> {
  return useQuery(GET_BUSINESS_ORDER_APPROVAL_IDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyBusinessOrderApprovalIds(
  options?: LazyQueryHookOptions<
    QueryBusinessOrderApprovalIdsData,
    QueryBusinessOrderApprovalIdsArgs
  >
): LazyQueryResultTuple<QueryBusinessOrderApprovalIdsData, QueryBusinessOrderApprovalIdsArgs> {
  return useLazyQuery(GET_BUSINESS_ORDER_APPROVAL_IDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
