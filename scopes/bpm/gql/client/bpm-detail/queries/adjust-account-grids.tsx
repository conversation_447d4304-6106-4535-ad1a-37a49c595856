import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  BatchAdjustApprovalGrid,
  Maybe,
  QueryBatchAdjustApprovalGridsArgs,
} from '../generated-types/graphql';

export type QueryAdjustAccountGridsData = {
  batchAdjustApprovalGrids: Maybe<{
    data: BatchAdjustApprovalGrid[];
    total: number;
  }>;
};

export const GET_ADJUST_ACCOUNT_GRIDS: DocumentNode = gql`
  query GetAdjustAccountGrids($adjustNo: String!) {
    batchAdjustApprovalGrids(adjustNo: $adjustNo) {
      data {
        gridGuid
        roomTag
        gridPower
        maxPowerTime
        originMaxPower
        adjustMaxPower
        originMaxPowerTime
        adjustMaxPowerTime
      }
      total
    }
  }
`;

export function useAdjustAccountGrids(
  options?: QueryHookOptions<QueryAdjustAccountGridsData, QueryBatchAdjustApprovalGridsArgs>
): QueryResult<QueryAdjustAccountGridsData, QueryBatchAdjustApprovalGridsArgs> {
  return useQuery(GET_ADJUST_ACCOUNT_GRIDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyAdjustAccountGrids(
  options?: LazyQueryHookOptions<QueryAdjustAccountGridsData, QueryBatchAdjustApprovalGridsArgs>
): LazyQueryResultTuple<QueryAdjustAccountGridsData, QueryBatchAdjustApprovalGridsArgs> {
  return useLazyQuery(GET_ADJUST_ACCOUNT_GRIDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
