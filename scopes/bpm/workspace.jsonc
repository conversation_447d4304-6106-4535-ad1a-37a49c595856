{"$schema": "https://static.bit.dev/teambit/schemas/schema.json", "teambit.workspace/workspace": {"name": "@manyun/bpm", "icon": "https://static.bit.dev/bit-logo.svg", "defaultDirectory": "{name}", "defaultScope": "manyun.bpm", "resolveAspectsFromNodeModules": true, "resolveEnvsFromRoots": true}, "teambit.dependencies/dependency-resolver": {"packageManager": "teambit.dependencies/pnpm", "policy": {"dependencies": {"@teammc/cli": "^2.0.0"}, "peerDependencies": {}}, "linkCoreAspects": true, "rootComponents": false}, "teambit.workspace/variants": {"*": {"teambit.pkg/pkg": {"packageJson": {"name": "@manyun/{scope}.{name}", "private": false, "publishConfig": {"scope": "@manyun", "registry": "https://packages.aliyun.com/5f8d28b593de78251872349a/npm/npm-registry/"}}, "packageManagerPublishArgs": ["--access public"]}}}, "teambit.component/issues": {"ignoreIssues": ["ImportNonMainFiles"]}, "teambit.workspace/workspace-config-files": {"enableWorkspaceConfigWrite": false}, "teambit.generator/generator": {"envs": ["teammc.snowcone/node-esm-env"]}}