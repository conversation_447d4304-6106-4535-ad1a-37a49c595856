/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  ApiQ as PageSearchApiQ,
  SvcQuery as PageSearchQuery,
} from '@manyun/ticket.service.fetch-paged-risk-registers';

export type SvcQuery = Omit<PageSearchQuery, 'pageNum' | 'pageSize'>;
export type SvcRespData = Blob;

export type RequestRespData = Blob;

export type ApiQ = Omit<PageSearchApiQ, 'pageNum' | 'pageSize'>;

export type ApiR = Response<Blob>;
