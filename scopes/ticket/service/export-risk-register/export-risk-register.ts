/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import dayjs from 'dayjs';

import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './export-risk-register.type';

const endpoint = '/dcom/risk/register/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/309/interface/api/24786)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      ...svcQuery,
      createBeginTime: svcQuery.createTime
        ? dayjs(svcQuery.createTime[0]).startOf('day').valueOf()
        : undefined,
      createEndTime: svcQuery.createTime
        ? dayjs(svcQuery.createTime[1]).endOf('day').valueOf()
        : undefined,
      updateBeginTime: svcQuery.updateTime
        ? dayjs(svcQuery.updateTime[0]).startOf('day').valueOf()
        : undefined,
      updateEndTime: svcQuery.updateTime
        ? dayjs(svcQuery.updateTime[1]).endOf('day').valueOf()
        : undefined,
    };
    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
