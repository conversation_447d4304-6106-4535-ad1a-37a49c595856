/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-risk-register';
import type { SvcQuery, SvcRespData } from './export-risk-register.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */

export function exportRiskRegister(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
