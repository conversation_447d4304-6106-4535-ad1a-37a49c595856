/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './delete-change-offline-execute-log.type';

const endpoint = '/dcom/change/order/offline/exe/log/delete';

/**
 * @see [Doc](http://172.16.0.17:13000/project/84/interface/api/19327)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
