/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteChangeOfflineExecuteLog as webService } from './delete-change-offline-execute-log.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ id: 1 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ id: 2 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
