/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-emergency-drill';
import type { ApiQ, SvcRespData } from './create-emergency-drill.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createEmergencyDrill(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
