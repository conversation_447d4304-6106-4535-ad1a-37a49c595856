/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendEmergencyDrillLevel,
  BackendEmergencyDrillMajor,
  BackendEmergencyDrillType,
} from '@manyun/ticket.model.ticket';

// 应急演练内容
export type PlanProperties = {
  level: BackendEmergencyDrillLevel;
  /** 负责人ID */
  principalId: number;
  /** 负责人名称 */
  principalName: string;
  description?: string;
  /** 专业 */
  specialty: BackendEmergencyDrillMajor;
};
// 计划阶段方案
export type PlanItemInfo = {
  /** 阶段 */
  stage: string;

  /** 计划内容 */
  planContent: string;
  /** 执行人ID */
  agentId: number;
  /** 执行人名称 */

  agentName: string;
  /** 阶段序号 */
  sequence: number;
};

export type SvcRespData = string;

export type RequestRespData = string | null;

// ApiQ 与 SvcQuery 相同
export type ApiQ = {
  /** 工单类型 */
  subType: BackendEmergencyDrillType;
  /** 工单标题 */
  taskTitle: string;
  /** 计划阶段列表 */
  planItemInfoList: PlanItemInfo[];
  /** 应急演练内容 */
  planProperties: PlanProperties;
  fileInfoList?: MixedUploadFile[];
};

export type ApiR = WriteResponse;
