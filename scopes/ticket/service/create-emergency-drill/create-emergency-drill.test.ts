/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import {
  BackendEmergencyDrillLevel,
  BackendEmergencyDrillMajor,
  BackendEmergencyDrillType,
} from '@manyun/ticket.model.ticket';

import { createEmergencyDrill as webService } from './create-emergency-drill.browser';
import { createEmergencyDrill as nodeService } from './create-emergency-drill.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    taskTitle: 'SUCCESS_ID',
    subType: BackendEmergencyDrillType.DRILL,
    planProperties: {
      level: BackendEmergencyDrillLevel.HIGH,
      principalId: 1,
      principalName: 'test',

      specialty: BackendEmergencyDrillMajor.COUNTERTERRORISM,
    },
    planItemInfoList: [],
  });

  expect(error?.code).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    taskTitle: 'FAIL_ID',
    subType: BackendEmergencyDrillType.DRILL,
    planProperties: {
      level: BackendEmergencyDrillLevel.HIGH,
      principalId: 1,
      principalName: 'test',

      specialty: BackendEmergencyDrillMajor.COUNTERTERRORISM,
    },
    planItemInfoList: [],
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    taskTitle: 'SUCCESS_ID',
    subType: BackendEmergencyDrillType.DRILL,
    planProperties: {
      level: BackendEmergencyDrillLevel.HIGH,
      principalId: 1,
      principalName: 'test',

      specialty: BackendEmergencyDrillMajor.COUNTERTERRORISM,
    },
    planItemInfoList: [],
  });

  expect(error?.code).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    taskTitle: 'FAIL_ID',
    subType: BackendEmergencyDrillType.DRILL,
    planProperties: {
      level: BackendEmergencyDrillLevel.HIGH,
      principalId: 1,
      principalName: 'test',

      specialty: BackendEmergencyDrillMajor.COUNTERTERRORISM,
    },
    planItemInfoList: [],
  });

  expect(typeof error).toBe('object');
});
