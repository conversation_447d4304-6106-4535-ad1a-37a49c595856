---
description: 'A createChangeShift HTTP API service.'
labels: ['service', 'http', change-shift-create]
---

## 概述

添加交接班记录

## 使用

### Browser

```ts
import { createChangeShift } from '@ticket/service.create-change-shift';

const { error, data } = await createChangeShift({
  applyScheduleId: 1,
  handScheduleId: 1,
  handUserId: 1,
  handoverInfo: '1',
  handoverEvents: [
    {
      eventName: '值班室工器具',
      value: '正常',
      remarks: 'asdsad',
    },
  ],
});
const { error, data } = await createChangeShift({
  applyScheduleId: 2,
  handScheduleId: 2,
  handUserId: 2,
  handoverInfo: '2',
  handoverEvents: [
    {
      eventName: '值班室工器具',
      value: '正常',
      remarks: 'asdsad',
    },
  ],
});
```

### Node

```ts
import { createChangeShift } from '@ticket/service.create-change-shift/dist/index.node';

const { data } = await createChangeShift({
  applyScheduleId: 1,
  handScheduleId: 1,
  handUserId: 1,
  handoverInfo: '1',
  handoverEvents: [
    {
      eventName: '值班室工器具',
      value: '正常',
      remarks: 'asdsad',
    },
  ],
});

try {
  const { data } = await createChangeShift({
    applyScheduleId: 2,
    handScheduleId: 2,
    handUserId: 2,
    handoverInfo: '2',
    handoverEvents: [
      {
        eventName: '值班室工器具',
        value: '正常',
        remarks: 'asdsad',
      },
    ],
  });
} catch (error) {
  // ...
}
```
