/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = string;

export type RequestRespData = string;

export type ApiQ = {
  applyScheduleId: number;
  handScheduleId: number;
  handUserId: number;
  handoverInfo: string;
  handoverEvents?: HandoverEvent[];
  files?: BackendMcUploadFile[];
  applyUserId?: number | null;
  ccStaffIds?: number[] | null;
};

export type HandoverEvent = {
  eventName: string;
  value: string;
  remarks: string;
};

export type ApiR = WriteResponse;
