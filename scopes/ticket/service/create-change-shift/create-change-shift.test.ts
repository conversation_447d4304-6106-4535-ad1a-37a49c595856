/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createChangeShift as webService } from './create-change-shift.browser';
import { createChangeShift as nodeService } from './create-change-shift.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    applyScheduleId: 1,
    handScheduleId: 1,
    handUserId: 1,
    handoverInfo: '1234',
    handoverEvents: [
      {
        eventName: '值班室工器具',
        value: '正常',
        remarks: 'asdsad',
      },
    ],
  });

  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    applyScheduleId: 2,
    handScheduleId: 2,
    handUserId: 2,
    handoverInfo: '2',
    handoverEvents: [
      {
        eventName: '值班室工器具',
        value: '正常',
        remarks: 'asdsad',
      },
    ],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    applyScheduleId: 1,
    handScheduleId: 1,
    handUserId: 1,
    handoverInfo: '1234',
    handoverEvents: [
      {
        eventName: '值班室工器具',
        value: '正常',
        remarks: 'asdsad',
      },
    ],
  });

  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    applyScheduleId: 2,
    handScheduleId: 2,
    handUserId: 2,
    handoverInfo: '2',
    handoverEvents: [
      {
        eventName: '值班室工器具',
        value: '正常',
        remarks: 'asdsad',
      },
    ],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
