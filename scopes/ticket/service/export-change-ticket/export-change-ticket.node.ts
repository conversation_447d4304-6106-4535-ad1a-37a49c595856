/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-change-ticket';
import type { SvcQuery, SvcRespData } from './export-change-ticket.type';

const executor = getExecutor(nodeRequest);

/**
 * @param SvcQuery
 * @returns
 */
export function exportChangeTicket(
  SvcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(SvcQuery);
}
