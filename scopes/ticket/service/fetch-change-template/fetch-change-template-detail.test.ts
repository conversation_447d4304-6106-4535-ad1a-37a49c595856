/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChangeTemplateDetail as webService } from './fetch-change-template-detail.browser';
import { fetchChangeTemplateDetail as nodeService } from './fetch-change-template-detail.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ templateId: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('templateId');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ templateId: '3' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toHaveProperty('templateId');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ templateId: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('templateId');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ templateId: '3' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toHaveProperty('templateId');
});
