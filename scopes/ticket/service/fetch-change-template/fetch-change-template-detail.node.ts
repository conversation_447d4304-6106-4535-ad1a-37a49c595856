/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-template-detail';
import type { SvcQuery, SvcRespData } from './fetch-change-template-detail.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchChangeTemplateDetail(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
