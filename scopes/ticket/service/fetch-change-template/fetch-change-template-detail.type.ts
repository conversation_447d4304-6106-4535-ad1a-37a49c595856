/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ExeWay = 'ON_LINE' | 'OFF_LINE';

export type Step = {
  stepOrder: number;
  stepName: string;
  stepDesc: string;
  ola: number;
  stepType: string;
  identifyWay: string;
  exeWay: ExeWay;
};

export type Template = {
  id: number;
  changeType: string;
  changeTypeName: string;

  creatorId: number;
  creatorName: string;
  executeRoleCode: string;
  executeRoleName: string;
  gmtCreate: number;
  gmtModified: number;

  modifyPersonId: 61;
  modifyPersonName: string;
  riskLevel: string;
  stepList: Step[];
  templateId: string;
  templateName: string;
  templateStatus: string;
};
export type SvcQuery = ApiQ;

export type SvcRespData = Template | null;

export type RequestRespData = Template | null;

export type ApiQ = {
  templateId: string;
};

export type ApiR = ListResponse<Template | null>;
