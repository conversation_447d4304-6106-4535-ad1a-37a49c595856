/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-template-detail';
import type { SvcQuery, SvcRespData } from './fetch-change-template-detail.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchChangeTemplate(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
