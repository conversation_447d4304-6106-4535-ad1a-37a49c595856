/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './export-change.type';

const endpoint = '/dcom/change/order/list/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/84/interface/api/19623)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
