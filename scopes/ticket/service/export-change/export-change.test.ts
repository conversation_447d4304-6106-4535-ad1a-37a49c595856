/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportChange as webService } from './export-change.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ pageNum: 1, pageSize: 10 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ pageNum: 2, pageSize: 10 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
