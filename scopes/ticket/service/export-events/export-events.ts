/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-16
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './export-events.type';

const endpoint = '/dcom/event/export';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/146/interface/api/20127)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<RequestRespData, ApiQ>(endpoint, svcQuery, {
      responseType: 'blob',
    });
  };
}
