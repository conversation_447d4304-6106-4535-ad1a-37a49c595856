/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-events';
import type { ApiQ, SvcRespData } from './export-events.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportEvents(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
