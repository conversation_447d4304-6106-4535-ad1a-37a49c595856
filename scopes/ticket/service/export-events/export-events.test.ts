/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-16
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportEvents as webService } from './export-events.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ eventId: 1 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ eventId: 0 });

  expect(typeof error?.code).toBe('string');
});
