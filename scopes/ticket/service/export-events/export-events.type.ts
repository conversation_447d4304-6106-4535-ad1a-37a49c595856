/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-16
 *
 * @packageDocumentation
 */

export type SvcRespData = Blob;
export type RequestRespData = Blob;
// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  deviceGuid?: string;
  eventLevel?: string;
  eventSource?: string;
  falseAlarms?: number[];
  key?: string;
  eventId?: number;
  topCategorys?: string[];
  secondCategorys?: string[];
  exportIncludeFields: string[];
};

export type ApiR = Blob;
