/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

const endpoint = '/taskcenter/warehouse/spareDevice/download';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/104/interface/api/27076)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<Blob>> => {
    const { error, data, ...rest } = await request.tryGet<Blob>(endpoint, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
