/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-consumable-template.js';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function downloadConsumableTemplate(): Promise<EnhancedAxiosResponse<Blob>> {
  return executor();
}
