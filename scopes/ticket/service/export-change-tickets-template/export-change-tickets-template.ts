/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './export-change-tickets-template.type';

const endpoint = '/dcom/change/template/export';

/**
 * 变更模版导出
 * @see [Doc](http://172.16.0.17:13000/project/84/interface/api/19812)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
