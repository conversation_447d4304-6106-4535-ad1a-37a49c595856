/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  riskId: string;
  measureId: number;
  handlerUserId: number;
  handleTime: string;
  handleContent: string;
  fileInfoList?: BackendMcUploadFile[];
};

export type ApiR = WriteResponse;
