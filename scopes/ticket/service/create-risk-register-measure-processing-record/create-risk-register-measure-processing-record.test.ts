/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createRiskRegisterMeasureProcessingRecord as webService } from './create-risk-register-measure-processing-record.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    riskId: '1',
    measureId: 1,
    handleContent: '1',
    handlerUserId: 1,
    handleTime: '1',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    riskId: '2',
    measureId: 1,
    handleContent: '1',
    handlerUserId: 1,
    handleTime: '1',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
