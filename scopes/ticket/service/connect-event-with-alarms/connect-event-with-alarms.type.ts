/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-7-8
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcData = {
  /**
   * Event ID
   */
  id: string;
  idc: string;
  alarmIds: number[];
  /**
   * 故障设备 GUID 集合
   */
  deviceGuids?: string[];
  alarmStartTimes: number[];
};

export type SvcRespData = {
  id: string;
} | null;

export type RequestRespData = boolean | null;

export type ApiD = {
  eventId: string;
  idcTag: string;
  alarmIds: number[];
  causeDevices: string[] | null;
  alarmStartTimes: number[];
};

export type ApiR = WriteResponse;
