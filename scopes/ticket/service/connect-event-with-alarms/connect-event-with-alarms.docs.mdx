---
description: 'A connectEventWithAlarms HTTP API service.'
labels: ['service', 'http']
---

## 概述

建立事件与告警的关联关系

## 使用

### Browser

```ts
import { connectEventWithAlarms } from '@manyun/ticket.service.connect-event-with-alarms';

const { error, data } = await connectEventWithAlarms({
  id: 0,
  idc: 'EC06',
  alarmIds: [1, 2, 3],
});
```

### Node

```ts
import { connectEventWithAlarms } from '@manyun/ticket.service.connect-event-with-alarms/dist/index.node';

const { error, data } = await connectEventWithAlarms({
  id: 0,
  idc: 'EC06',
  alarmIds: [1, 2, 3],
});
```
