/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-7-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { connectEventWithAlarms as webService } from './connect-event-with-alarms.browser';
import { connectEventWithAlarms as nodeService } from './connect-event-with-alarms.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    id: 0,
    idc: 'EC06',
    alarmIds: [1, 2, 3],
    alarmStartTimes: [1],
  });

  expect(error).toBe(undefined);
  expect(data!.id).toBe(0);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    id: 1,
    idc: 'EC06',
    alarmIds: [1, 2, 3],
    alarmStartTimes: [2],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    id: 0,
    idc: 'EC06',
    alarmIds: [1, 2, 3],
    alarmStartTimes: [1],
  });

  expect(error).toBe(undefined);
  expect(data!.id).toBe(0);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    id: 1,
    idc: 'EC06',
    alarmIds: [1, 2, 3],
    alarmStartTimes: [2],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
