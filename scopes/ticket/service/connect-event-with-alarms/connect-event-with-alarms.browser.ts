/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './connect-event-with-alarms';
import type { SvcData, SvcRespData } from './connect-event-with-alarms.type';

const executor = getExecutor(webRequest);

/**
 * @param svcData
 * @returns
 */
export function connectEventWithAlarms(
  svcData: SvcData
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcData);
}
