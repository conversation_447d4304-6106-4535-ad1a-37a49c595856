/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-8
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiD, RequestRespData, SvcData, SvcRespData } from './connect-event-with-alarms.type';

const endpoint = '/dcom/event/eventAssociateAlarms';

/**
 * @see [Doc](http://172.16.0.17:13000/project/146/interface/api/18584)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcData: SvcData): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiD: ApiD = {
      eventId: svcData.id,
      idcTag: svcData.idc,
      alarmIds: svcData.alarmIds,
      causeDevices: svcData.deviceGuids ?? null,
      alarmStartTimes: svcData.alarmStartTimes,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiD>(endpoint, apiD);

    if (error || data === null || data === false) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: {
        id: svcData.id,
      },
      ...rest,
    };
  };
}
