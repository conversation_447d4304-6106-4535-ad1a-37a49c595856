---
description: 'A fetchChangeShift HTTP API service.'
labels: ['service', 'http', fetch-change-shift]
---

## 概述

交接班详情

## 使用

### Browser

```ts
import { fetchChangeShift } from '@ticket/service.fetch-change-shift';

const { error, data } = await fetchChangeShift({ bizNo: '1' });
const { error, data } = await fetchChangeShift({ bizNo: '2' });
```

### Node

```ts
import { fetchChangeShift } from '@ticket/service.fetch-change-shift/dist/index.node';

const { data } = await fetchChangeShift({ bizNo: '1' });

try {
  const { data } = await fetchChangeShift({ bizNo: '2' });
} catch (error) {
  // ...
}
```
