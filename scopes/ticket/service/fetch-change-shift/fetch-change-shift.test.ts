/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChangeShift as webService } from './fetch-change-shift.browser';
import { fetchChangeShift as nodeService } from './fetch-change-shift.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ bizNo: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('bizNo');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ bizNo: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ bizNo: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('bizNo');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ bizNo: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
