/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { FetchChangeShiftAttendanceResult } from '@manyun/ticket.gql.client.tickets';
import type { BackendChangeShiftStatus } from '@manyun/ticket.model.ticket';

export type ChangeShiftJSON = {
  id: number;
  bizNo: string;
  handoverDate: number;
  applyScheduleId: number;
  applyDutyGroupId: number;
  applyDutyId: number;
  applyDutyGroupName: string;
  applyDutyName: string;
  handScheduleId: number;
  handDutyGroupId: number;
  handDutyGroupName: string;
  handDutyId: number;
  handDutyName: string;
  handUserId: number;
  applyUserId: number;
  submitTime: null;
  handTime: null;
  blockGuid: string;
  bizStatus: BackendChangeShiftStatus;
  gmtCreate: number;
  gmtModified: number;
  creatorId: number;
  handoverInfo: string;
  scheduleStartTime: number;
  scheduleEndTime: number;
  taskTitle: string;
  handoverEvents: HandoverEvents[];
  fileList: McUploadFileJSON[];
  counterSignTime: number | null;
  handoverCounterSigners: HandoverCounterSigners[];
  submitContent: SubmitContent;
  groupUsers: number[];
  ccStaffIds?: number[] | null;
};
export type SubmitContent = {
  submitReason: string | null;
  checkResults: FetchChangeShiftAttendanceResult[];
};
export type HandoverCounterSigners = {
  bizNo: string;
  content: string;
  files: BackendMcUploadFile[];
  finished: boolean;
  gmtCreate: number;
  gmtModified: number;
  id: number;
  staffId: number;
  staffName: string;
};

export type HandoverEvents = {
  description: string | null;
  eventName: string;
  eventCode: string | null;
  remarks: string;
  value: string;
};

export type SvcRespData = ChangeShiftJSON | null;

export type RequestRespData = ChangeShiftJSON | null;

export type ApiQ = {
  bizNo: string;
};

export type ApiR = ListResponse<ChangeShiftJSON[] | null>;
