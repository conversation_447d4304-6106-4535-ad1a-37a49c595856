/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-change-shift.type';

const endpoint = '/taskcenter/handover/detail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/154/interface/api/18296)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }
    const fileInfos = await fetchBizFileInfos({
      targetId: data.bizNo,
      targetType: 'HANDOVER',
    });
    return {
      error,
      data: { ...data, fileList: fileInfos.data.data },
      ...rest,
    };
  };
}
