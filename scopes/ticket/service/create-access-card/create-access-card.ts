/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-2
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';
import {
  EntranceGuardAuth,
  type EntranceGuardAuthJSON,
} from '@manyun/ticket.model.entrance-guard-auth';

import type { ApiQ, RequestRespData, SvcRespData } from './create-access-card.type';

const endpoint = '/taskcenter/general/access_card/create';

/**
 * @see [Doc](http://172.16.0.17:13000/project/114/interface/api/19431)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: EntranceGuardAuthJSON): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, {
      ...EntranceGuardAuth.fromJSON(svcQuery).toApiObject(),
    });

    return { error, data, ...rest };
  };
}
