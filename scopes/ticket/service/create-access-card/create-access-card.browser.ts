/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-2
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';
import type { EntranceGuardAuthJSON } from '@manyun/ticket.model.entrance-guard-auth';

import { getExecutor } from './create-access-card';
import type { SvcRespData } from './create-access-card.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createAccessCard(
  svcQuery: EntranceGuardAuthJSON
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
