/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-2
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import { AccessCardTaskSubType } from '@manyun/ticket.model.ticket';

import { createAccessCard as webService } from './create-access-card.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ authType: AccessCardTaskSubType.CardApply, idcTag: 'EC06' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ authType: AccessCardTaskSubType.CardApply, idcTag: 'EC01' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
