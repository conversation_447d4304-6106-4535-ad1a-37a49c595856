/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-event-processing-record.js';
import type { SvcApiQ, SvcRespData } from './export-event-processing-record.type.js';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportEventProcessingRecord(
  svcQuery: SvcApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
