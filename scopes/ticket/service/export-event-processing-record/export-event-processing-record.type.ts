/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common.js';

export type ExcelCheckErrDtosData = {
  /**
   * 阶段
   */
  eventPhase: string;
  /**
   * 处理时间
   */
  handleTime: string;
  /**
   * 处理人
   */
  handler: string;
  /**
   * 处理内容
   */
  handleContent: string;
  /**
   * 备注说明
   */
  comment?: string | null;
};

export type ExcelCheckErrDtos = {
  data?: ExcelCheckErrDtosData | null;
  /**
   * 集合map，key为错误字段，value为异常信息
   */
  errMessage: Record<string, string>;
  rowTag?: number | null;
};

export type ApiResponseData = {
  checkTotal: number;
  correctTotal: number;
  excelCheckDtos: ExcelCheckErrDtos[];
  faultTotal?: number | null;
};

export type SvcRespData = ApiResponseData;

export type RequestRespData = ApiResponseData;
// export type ApiQ = { file: FormData; id: string };
export type ApiQ = FormData;
export type SvcApiQ = FormData;
export type ApiR = Response<RequestRespData>;
