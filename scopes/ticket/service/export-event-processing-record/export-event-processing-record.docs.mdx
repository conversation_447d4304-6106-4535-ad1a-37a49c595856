---
description: 'A exportEventProcessingRecord HTTP API service.'
labels: ['service', 'http']
---

处理记录导入

## Usage

### Browser

```ts
import { exportEventProcessingRecord } from '@manyun/ticket.service.export-event-processing-record';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { ExportEventProcessingRecordService } from '@manyun/ticket.service.export-event-processing-record/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ExportEventProcessingRecordService.from(nodeRequest);
```
