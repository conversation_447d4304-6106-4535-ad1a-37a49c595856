/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcApiQ,
  SvcRespData,
} from './export-event-processing-record.type.js';

const endpoint = '/dcom/event/opt/record/import';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/146/interface/api/30524)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return { error, data, ...rest };
  };
}
