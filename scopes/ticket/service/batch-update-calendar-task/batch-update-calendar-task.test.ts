/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { batchUpdateCalendarTask as webService } from './batch-update-calendar-task.browser';
import { batchUpdateCalendarTask as nodeService } from './batch-update-calendar-task.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    changeExecuteList: [
      {
        schId: 123,
        blackDateTime: 'SUCCESS',
        whiteDateTime: 'SUCCESS',
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    changeExecuteList: [
      {
        schId: 123,
        blackDateTime: 'FAIL',
        whiteDateTime: 'FAIL',
      },
    ],
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    changeExecuteList: [
      {
        schId: 123,
        blackDateTime: 'SUCCESS',
        whiteDateTime: 'SUCCESS',
      },
    ],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    changeExecuteList: [
      {
        schId: 123,
        blackDateTime: 'FAIL',
        whiteDateTime: 'FAIL',
      },
    ],
  });

  expect(typeof error).toBe('object');
});
