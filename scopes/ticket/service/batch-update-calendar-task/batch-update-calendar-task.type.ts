/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChangeTask = {
  schId: number;
  blackDateTime: string;
  whiteDateTime?: string;
};

export type SvcRespData = RequestRespData;

export type RequestRespData = boolean | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  changeExecuteList: ChangeTask[];
};

export type ApiR = WriteResponse;
