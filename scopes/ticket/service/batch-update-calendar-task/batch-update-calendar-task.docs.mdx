---
description: 'A batchUpdateCalendarTask HTTP API service.'
labels: ['service', 'http']
---

## 概述

批量修改月度行事历中的任务

## 使用

### Browser

```ts
import { batchUpdateCalendarTask } from '@manyun/ticket.service.batch-update-calendar-task';

const { error, data } = await batchUpdateCalendarTask('success');
const { error, data } = await batchUpdateCalendarTask('error');
```

### Node

```ts
import { batchUpdateCalendarTask } from '@manyun/ticket.service.batch-update-calendar-task/dist/index.node';

const { data } = await batchUpdateCalendarTask('success');
const { error, data } = await batchUpdateCalendarTask('error');
```
