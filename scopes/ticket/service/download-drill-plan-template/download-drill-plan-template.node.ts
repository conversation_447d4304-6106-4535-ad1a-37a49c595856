/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-drill-plan-template';
import type { SvcRespData } from './download-drill-plan-template.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function downloadDrillPlanTemplate(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
