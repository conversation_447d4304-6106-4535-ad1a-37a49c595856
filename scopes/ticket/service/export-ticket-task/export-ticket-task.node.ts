/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-ticket-task';
import type { ApiArgs } from './export-ticket-task.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function exportTicketTask(args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(args);
}
