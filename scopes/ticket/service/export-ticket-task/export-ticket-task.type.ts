/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  includeColumnFiledNames: string[];
  /**
   * 机房楼栋
   */
  blockGuidList?: string[] | null;
  /**
   * 计划名称
   */
  name?: string | null;
  /**
   * 计划类型:巡检/盘点/维护：详见备注
   */
  jobTypeList: string[];
  /**
   * mop类型
   */
  subJobType?: string | null;
  /**
   * 周期：NONE、DAY、WEEK、MONTH、YEAR
   */
  periodUnit?: string | null;
  /**
   * 是否生效(1:失效 0:有效
   */
  isInvalid?: string | null;
  /**
   * 状态 : 1:启用  0:禁用
   */
  status?: string | null;
  /**
   * 创建人id
   */
  creatorId?: string | null;
  /**
   * 开始时间
   */
  startTime?: number | null;
  /**
   * 结束时间
   */
  endTime?: number | null;
  /**
   * 修改开始时间
   */
  startDateOfUpdate?: number | null;
  /**
   * 修改结束时间
   */
  endDateOfUpdate?: number | null;
  /**
   * 维护周期
   */
  guidePeriod?: string | null;
  /**
   * 是否自定义排序
   */
  defineDesc?: boolean | null;
  /**
   * gmtCreate/triggerTime/gmtModified
   */
  sortByField?: string | null;
  /**
   * DESC：降序/ASC ：升序
   */
  sortInfo?: string | null;
};

export type ApiResponse = Response<Blob>;
