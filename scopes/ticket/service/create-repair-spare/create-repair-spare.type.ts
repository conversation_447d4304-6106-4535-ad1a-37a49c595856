/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-8
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type RepairSpare = {
  name: string;
  vendor: string;
  productModel: string;
  num: string;
  description: string;
  sourceNo: string;
};

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;
//ApiQ与SvcQ相同
export type ApiQ = {
  idcTag: string;
  taskNo: string;
  repairSpareList: RepairSpare[];
};

export type ApiR = WriteResponse;
