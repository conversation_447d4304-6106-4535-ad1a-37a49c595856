/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-20
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancel-change';
import type { ApiQ, SvcRespData } from './cancel-change.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function cancelChange(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
