/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { feedbackRepairException as webService } from './feedback-repair-exception.browser';
import { feedbackRepairException as nodeService } from './feedback-repair-exception.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    title: '【发起人】+发起维保联系人信息错误',
    taskNo: 'SUCCESS',
    idcTag: 'EC06',
    blockGuid: 'EC06.A',
    reason: '213123',
    processType: 'COMMON_APPROVAL',
    detailParam: { applyType: '维保联系人信息错误' },
    startProcessParams: { applyType: 'CONTACT_INFO_ERR' },
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    title: '【发起人】+发起维保联系人信息错误',
    taskNo: 'FAIL',
    idcTag: 'EC06',
    blockGuid: 'EC06.A',
    reason: '213123',
    processType: 'COMMON_APPROVAL',
    detailParam: { applyType: '维保联系人信息错误' },
    startProcessParams: { applyType: 'CONTACT_INFO_ERR' },
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    title: '【发起人】+发起维保联系人信息错误',
    taskNo: 'SUCCESS',
    idcTag: 'EC06',
    blockGuid: 'EC06.A',
    reason: '213123',
    processType: 'COMMON_APPROVAL',
    detailParam: { applyType: '维保联系人信息错误' },
    startProcessParams: { applyType: 'CONTACT_INFO_ERR' },
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    title: '【发起人】+发起维保联系人信息错误',
    taskNo: 'FAIL',
    idcTag: 'EC06',
    blockGuid: 'EC06.A',
    reason: '213123',
    processType: 'COMMON_APPROVAL',
    detailParam: { applyType: '维保联系人信息错误' },
    startProcessParams: { applyType: 'CONTACT_INFO_ERR' },
  });

  expect(typeof error).toBe('object');
});
