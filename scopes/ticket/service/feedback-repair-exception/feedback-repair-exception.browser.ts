/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './feedback-repair-exception';
import type { ApiQ, SvcRespData } from './feedback-repair-exception.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function feedbackRepairException(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
