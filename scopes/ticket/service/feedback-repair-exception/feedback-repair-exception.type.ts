/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;
// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  idcTag: string;
  taskNo: string;
  blockGuid: string;
  reason: string;
  title: string;
  processType: string;
  detailParam: { applyType: string };
  startProcessParams: { applyType: string };
};

export type ApiR = WriteResponse;
