/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-2
 *
 * @packageDocumentation
 */
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';
import { EntranceGuardAuth } from '@manyun/ticket.model.entrance-guard-auth';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-access-card.type';

const endpoint = '/taskcenter/general/access_card/detail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/114/interface/api/19434)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }
    const fileInfos = await fetchBizFileInfos({
      targetId: data.taskNo,
      targetType: 'ACCESS_CARD_AUTH',
    });
    return {
      error,
      data: {
        ...EntranceGuardAuth.fromApiObject({ ...data, fileInfoList: fileInfos.data.data }).toJSON(),
      },
      ...rest,
    };
  };
}
