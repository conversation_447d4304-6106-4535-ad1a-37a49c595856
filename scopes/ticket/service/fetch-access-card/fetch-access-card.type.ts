/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-2
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendEntranceGuardAuth,
  EntranceGuardAuthJSON,
} from '@manyun/ticket.model.entrance-guard-auth';

export type AccessCardJson = EntranceGuardAuthJSON;

export type SvcRespData = AccessCardJson | null;

export type RequestRespData = BackendEntranceGuardAuth | null;

export type ApiQ = {
  taskNo: string;
};

export type ApiR = Response<RequestRespData>;
