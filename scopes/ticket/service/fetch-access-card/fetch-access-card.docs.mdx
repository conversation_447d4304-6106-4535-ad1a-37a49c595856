---
description: 'A fetchAccessCard HTTP API service.'
labels: ['service', 'http']
---

## 概述

获取门禁卡工单详情

## 使用

### Browser

```ts
import { fetchAccessCard } from '@manyun/ticket.service.fetch-access-card';

const { error, data } = await fetchAccessCard('success');
const { error, data } = await fetchAccessCard('error');
```

### Node

```ts
import { fetchAccessCard } from '@manyun/ticket.service.fetch-access-card/dist/index.node';

const { data } = await fetchAccessCard('success');
const { error, data } = await fetchAccessCard('error');
```
