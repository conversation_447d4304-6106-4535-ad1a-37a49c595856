/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import {
  BackendEventUpgradeChannel,
  BackendEventUpgradeLevel,
  BackendEventUpgradeType,
} from '@manyun/ticket.model.event';

import { createEventUpgrade as webService } from './create-event-upgrade.browser';
import { createEventUpgrade as nodeService } from './create-event-upgrade.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    upgradeLevel: BackendEventUpgradeLevel.Tier1,
    upgradeChannels: [BackendEventUpgradeChannel.Email],
    upgradeType: BackendEventUpgradeType.Offline,
    upgradeContext: 'test',
    upgradeUserIds: [1],
    upgradeWay: 'NORMAL',
    eventId: 1,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    upgradeLevel: BackendEventUpgradeLevel.Tier1,
    upgradeChannels: [BackendEventUpgradeChannel.Email],
    upgradeType: BackendEventUpgradeType.Offline,
    upgradeContext: 'test',
    upgradeWay: 'NORMAL',

    upgradeUserIds: [1],
    eventId: 0,
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    upgradeLevel: BackendEventUpgradeLevel.Tier1,
    upgradeChannels: [BackendEventUpgradeChannel.Email],
    upgradeWay: 'NORMAL',
    upgradeType: BackendEventUpgradeType.Offline,
    upgradeContext: 'test',
    upgradeUserIds: [1],
    eventId: 1,
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    upgradeLevel: BackendEventUpgradeLevel.Tier1,
    upgradeChannels: [BackendEventUpgradeChannel.Email],
    upgradeType: BackendEventUpgradeType.Offline,
    upgradeWay: 'NORMAL',

    upgradeContext: 'test',
    upgradeUserIds: [1],
    eventId: 0,
  });

  expect(typeof error).toBe('object');
});
