/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-6
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendEventUpgradeChannel,
  BackendEventUpgradeLevel,
  BackendEventUpgradeType,
} from '@manyun/ticket.model.event';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

// ApiQ 和 SvcQuery 相同
export type ApiQ = {
  /** 升级级别 */
  upgradeLevel: BackendEventUpgradeLevel;
  /** 升级渠道 */
  upgradeChannels: BackendEventUpgradeChannel[];
  /** 线上 or 线下 */
  upgradeType: BackendEventUpgradeType;
  /** 升级内容 */
  upgradeContext: string;
  /** 升级用户id列表 */
  upgradeUserIds: number[];
  eventId: number;
  /** 升级方式：阶段更新（PHASE），事件升级（NORMAL）	 */
  upgradeWay: 'PHASE' | 'NORMAL';
};

export type ApiR = WriteResponse;
