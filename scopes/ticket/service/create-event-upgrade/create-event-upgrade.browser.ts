/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-event-upgrade';
import type { ApiQ, SvcRespData } from './create-event-upgrade.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createEventUpgrade(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
