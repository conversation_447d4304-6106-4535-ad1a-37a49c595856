/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-6
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = {
  data: McUploadFile[];
  total: number;
};

type ApiRespData = BackendMcUploadFile[] | null;

export type RequestRespData = {
  data: ApiRespData;
  total: number;
} | null;

export type ApiQ = { applyScheduleId?: number; bizNo?: string; handlerId?: number | null };

export type ApiR = ListResponse<ApiRespData>;
