/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-6
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-change-shift-data-files.type';

const endpoint = '/taskcenter/handover/excelModel';

/**
 * 获取交接班运维数据附件
 *
 * @see [Doc](http://172.16.0.17:13000/project/154/interface/api/20766)
 *
 * @param svcQuery
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data == null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      ...rest,
      error,
      data: {
        data: data.data.map(backendMcUploadFile => McUploadFile.fromApiObject(backendMcUploadFile)),
        total: data.total,
      },
    };
  };
}
