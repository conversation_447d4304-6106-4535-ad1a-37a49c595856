/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChangeShiftDataFiles as webService } from './fetch-change-shift-data-files.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve a success response', async () => {
  const { error, data } = await webService({
    bizNo: '1',
  });

  expect(error).toBe(undefined);
  data.data.forEach(mcUploadFile => {
    expect(mcUploadFile).toBeInstanceOf(McUploadFile);
  });
  expect(typeof data.total).toBe('number');
});

test('should resolve a failure response', async () => {
  const { error, data } = await webService({
    bizNo: '2',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
