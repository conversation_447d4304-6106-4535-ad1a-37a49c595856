/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-shift-data-files';
import type { ApiQ, SvcRespData } from './fetch-change-shift-data-files.type';

/**
 * @param svcQuery
 * @returns
 */
export async function fetchChangeShiftDataFiles(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return await getExecutor(nodeRequest)(svcQuery);
}
