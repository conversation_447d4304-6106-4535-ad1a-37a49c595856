/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-10
 *
 * @packageDocumentation
 */

/** Use Local Mocks --- START */
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './fetch-calendar-tasks.mock';
// import { fetchCalendarTasks as webService } from './fetch-calendar-tasks.browser';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));

/** Use Local Mocks --- END */
// Use Remote Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchCalendarTasks as webService } from './fetch-calendar-tasks.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    endDate: '2022-02-07',
    startDate: '2021-12-27',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({
    endDate: '2022-02-07',
    startDate: '2021-12-20',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
