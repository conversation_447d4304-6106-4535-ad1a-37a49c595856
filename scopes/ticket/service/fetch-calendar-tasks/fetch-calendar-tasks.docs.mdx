---
description: 'A fetchCalendarTasks HTTP API service.'
labels: ['service', 'http', fetch-calendar-tasks]
---

## 概述

月度任务

## 使用

### Browser

```ts
import { fetchCalendarTasks } from '@manyun/ticket.service.fetch-calendar-tasks';

const { error, data } = await fetchCalendarTasks({
  endDate: '2022-02-07',
  startDate: '2021-12-27',
});
```

### Node

```ts
import { fetchCalendarTasks } from '@manyun/ticket.service.fetch-calendar-tasks/dist/index.node';

const { data } = await fetchCalendarTasks({
  endDate: '2022-02-07',
  startDate: '2021-12-27',
});

try {
  const { data } = await fetchCalendarTasks({
    endDate: '2022-02-07',
    startDate: '2021-12-27',
  });
} catch (error) {
  // ...
}
```
