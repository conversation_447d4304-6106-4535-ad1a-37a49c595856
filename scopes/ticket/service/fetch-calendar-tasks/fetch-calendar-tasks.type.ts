/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-10
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendTask, TaskJSON } from '@manyun/ticket.model.task';

export type SvcQuery = {
  idcTag?: string;
  blockTag?: string;
  blockGuidList?: string[];
  startDate?: string; //2021-12-27
  endDate?: string;
  schName?: string;
  schId?: string;
  schStatusList?: ('WILL_TRIGGER' | 'UNFINISHED' | 'FINISHED')[];
  jobTypeList?: string[];
  deviceTypeCode?: string;
  schLevel?: string;
};

export type BackendRes = {
  localDate: string;
  calendarExecuteInfoSet: BackendTask[];
};

export type SvcRespData = {
  data: {
    localDate: string;
    calendarExecuteInfoSet: TaskJSON[];
  }[];
  total: number;
};

export type RequestRespData = {
  data: BackendRes[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<RequestRespData>;
