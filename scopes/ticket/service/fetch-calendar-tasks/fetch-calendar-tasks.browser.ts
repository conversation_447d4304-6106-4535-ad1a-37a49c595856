/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-10
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';
import { Task } from '@manyun/ticket.model.task';

import { endpoint } from './fetch-calendar-tasks';
import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-calendar-tasks.type';

/**
 * @see [月度任务](http://172.16.0.17:13000/project/112/interface/api/5744)
 *
 * @param query
 * @returns
 */
export async function fetchCalendarTasks(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, SvcQuery>(
    endpoint,
    query
  );

  if (error || data === null || data.data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }

  return {
    error,
    data: {
      data: data.data.map(item => ({
        ...item,
        calendarExecuteInfoSet: item?.calendarExecuteInfoSet.map(Task.fromApiObject),
      })),
      total: data.total,
    },
    ...rest,
  };
}
