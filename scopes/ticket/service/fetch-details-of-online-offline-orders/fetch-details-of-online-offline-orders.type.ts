/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common.js';

export type ApiArgs = {
  /**
   * 工单编号
   */
  taskNo: string;
  /**
   * 包间guid
   */
  roomGuid: string;
  pageNum?: number;
  pageSize?: number;
};

export type ApiResponseData = {
  /**
   * id
   */
  id: number;
  /**
   * 工单编号
   */
  taskNo: string;
  /**
   * 设备guid
   */
  deviceGuid: string;
  /**
   * 设备名称
   */
  deviceName: string;
  /**
   * 设备三级分类
   */
  deviceType: string;
  /**
   * 机房
   */
  idcTag: string;
  /**
   * 楼栋
   */
  blockGuid: string;
  /**
   * 包间guid
   */
  roomGuid: string;
  /**
   * 包间名称
   */
  roomName: string;
  /**
   * 包间类型
   */
  roomType: string;
  /**
   * SN
   */
  serialNumber: string;
};

export type ApiResponse = ListResponse<ApiResponseData[]>;
