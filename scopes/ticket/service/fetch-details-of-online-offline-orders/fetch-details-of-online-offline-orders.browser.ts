/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-details-of-online-offline-orders.js';
import type { ApiArgs, ApiResponse } from './fetch-details-of-online-offline-orders.type.js';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function fetchDetailsOfOnlineOfflineOrders(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
