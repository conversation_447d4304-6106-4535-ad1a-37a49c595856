---
description: 'A fetchAddAssigneeConfig HTTP API service.'
labels: ['service', 'http']
---

新增对接人配置

## Usage

### Browser

```ts
import { fetchAddAssigneeConfig } from '@manyun/ticket.service.fetch-add-assignee-config';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchAddAssigneeConfigService } from '@manyun/ticket.service.fetch-add-assignee-config/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchAddAssigneeConfigService.from(nodeRequest);
```
