/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-10-15
 *
 * @packageDocumentation
 */

import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs,  } from './fetch-add-assignee-config.type.js';

const endpoint = '/taskcenter/assignee/config/add';

/**
* @see [Doc](http://yapi.manyun-local.com/project/110/interface/api/29371)
*
* @param request
* @returns
*/
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);

  };
}             
