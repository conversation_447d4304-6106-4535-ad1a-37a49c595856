/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-10-15
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-add-assignee-config.js';
import type { ApiArgs,  } from './fetch-add-assignee-config.type.js';

/**
 * @param args
* @returns
 */
export function fetchAddAssigneeConfig(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
