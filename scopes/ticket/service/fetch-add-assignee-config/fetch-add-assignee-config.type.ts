/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-10-15
 *
 * @packageDocumentation
 */
import type { WriteBackendResponse } from '@glpdev/symphony.services.request';

export type ConfigListType = {
  id: number;
  userName: string;
  userType: number;
};

export type ArgsType = {
  blockGuid: string;
  customerNo: string;
  assigneeType: number;
  assigneeList?: ConfigListType[] | null;
};

export type ApiArgs = {
  configList: ArgsType[];
};

export type ApiResponse = WriteBackendResponse;
