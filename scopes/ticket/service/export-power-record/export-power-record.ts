/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './export-power-record.type';

const endpoint = '/taskcenter/power/grid/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/118/interface/api/19857)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
