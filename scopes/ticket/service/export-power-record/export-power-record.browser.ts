/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-power-record';
import type { SvcQuery, SvcRespData } from './export-power-record.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportPowerRecord(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
