/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2023-8-7
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-it-service-ticket';
import type { SvcQuery } from './create-it-service-ticket.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createItServiceTicket(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<string | null>> {
  return executor(svcQuery);
}
