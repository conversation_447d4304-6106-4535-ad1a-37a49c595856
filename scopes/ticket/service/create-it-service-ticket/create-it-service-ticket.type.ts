/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2023-8-7
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type InformationTechnologyServiceInput = {
  taskSubType: string;
  thirdOrderType: string;
  blockGuid: string;
  phone: string;
  sourceOfOrder: string;
  description: string;
  assigneeInfo?: { id: number; userName: string };
  fileInfoList?: BackendMcUploadFile[];
};

export type SvcQuery = {
  fileInfoList?: McUploadFileJSON[];
} & Omit<InformationTechnologyServiceInput, 'fileInfoList'>;

export type ApiQ = InformationTechnologyServiceInput;

export type ApiR = WriteResponse;
