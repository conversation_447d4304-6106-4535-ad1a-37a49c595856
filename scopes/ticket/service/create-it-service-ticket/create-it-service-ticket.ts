/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2023-8-7
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './create-it-service-ticket.type';

const endpoint = '/taskcenter/it/create';

/**
 * @see [Doc](http://172.16.0.17:13000/project/300/interface/api/24420)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<string | null>> => {
    const params: ApiQ = {
      ...svcQuery,
      fileInfoList: svcQuery.fileInfoList?.map(file => McUploadFile.fromJSON(file).toApiObject()),
    };

    const { error, data, ...rest } = await request.tryPost<string | null, ApiQ>(endpoint, params);
    return { error, data, ...rest };
  };
}
