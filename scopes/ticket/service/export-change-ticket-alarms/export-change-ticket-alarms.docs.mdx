---
description: 'A exportChangeTicketAlarms HTTP API service.'
labels: ['service', 'http', export-change-ticket-alarms]
---

## 概述

导出变更告警

## 使用

### Browser

```ts
import { exportChangeTicketAlarms } from '@manyun/dc-brain.service.export-change-ticket-alarms';

const { error, data } = await exportChangeTicketAlarms({
  changeId: 'C0045',
  idcTag: 'sxdtyg',
  isExpected: false,
});
const { error, data } = await exportChangeTicketAlarms({
  changeId: 'C0045',
  idcTag: 'sxdtyg',
  isExpected: false,
});
```

### Node

```ts
import { exportChangeTicketAlarms } from '@manyun/dc-brain.service.export-change-ticket-alarms/dist/index.node';

const { data } = await exportChangeTicketAlarms({
  changeId: 'C0045',
  idcTag: 'sxdtyg',
  isExpected: false,
});

try {
  const { data } = await exportChangeTicketAlarms({
    changeId: 'C0045',
    idcTag: 'sxdtyg',
    isExpected: false,
  });
} catch (error) {
  // ...
}
```
