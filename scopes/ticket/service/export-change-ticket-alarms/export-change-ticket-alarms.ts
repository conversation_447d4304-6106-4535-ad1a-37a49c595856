/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery } from './export-change-ticket-alarms.type';

export const endpoint = '/dcim/alarm/data/export';

/**
 * 导出变更告警
 *
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/5728)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (query: SvcQuery): Promise<EnhancedAxiosResponse<Blob>> => {
    return await request.tryPost<Blob, SvcQuery>(endpoint, query, {
      responseType: 'blob',
    });
  };
}
