/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-change-ticket-alarms';
import type { SvcQuery } from './export-change-ticket-alarms.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function exportChangeTicketAlarms(variant: SvcQuery): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(variant);
}
