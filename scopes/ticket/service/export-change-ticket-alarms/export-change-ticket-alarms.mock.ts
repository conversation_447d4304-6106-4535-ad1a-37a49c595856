/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-12
 *
 * @packageDocumentation
 */
// 使用以下代码提供本地 Mock，如果使用远程 Mock，则可以删除此文件
import { getMock } from '@manyun/service.request';

import { endpoint } from './export-change-ticket-alarms';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    const resp = new Blob(['Hello, FileExport'], { type: 'application/vnd.ms-excel' });
    return [200, resp];
  });
}
