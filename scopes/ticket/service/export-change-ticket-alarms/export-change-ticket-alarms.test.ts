/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-12
 *
 * @packageDocumentation
 */
import { getMock, useRemoteMock } from '@manyun/service.request';

import { exportChangeTicketAlarms as webService } from './export-change-ticket-alarms.browser';
import { registerWebMocks } from './export-change-ticket-alarms.mock';

registerWebMocks(getMock('web'));

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    changeId: 'C0045',
    idcTag: 'sxdtyg',
    isExpected: false,
  });
  expect(error).toBe(undefined);
  expect(data).toBeInstanceOf(Blob);
  expect(data.type).toBe('application/vnd.ms-excel');
});
