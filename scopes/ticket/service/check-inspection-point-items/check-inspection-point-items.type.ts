/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = InspectionCheckResult[];

export type RequestRespData = {
  data: InspectionCheckResult[];
};

export type InspectionCheckResult = {
  inspectItemId: string;
  realValue: number | null;
  checkResult: 'EXCEPTION' | 'NORMAL';
};

export type ApiQ = {
  taskNo: string;
  deviceGuid: string;
};

export type ApiR = ListResponse<InspectionCheckResult[] | null>;
