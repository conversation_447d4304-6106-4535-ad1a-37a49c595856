/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { checkInspectionPointItems as webService } from './check-inspection-point-items.browser';
import { checkInspectionPointItems as nodeService } from './check-inspection-point-items.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ taskNo: '1', deviceGuid: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ taskNo: '2', deviceGuid: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ taskNo: '1', deviceGuid: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ taskNo: '2', deviceGuid: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
