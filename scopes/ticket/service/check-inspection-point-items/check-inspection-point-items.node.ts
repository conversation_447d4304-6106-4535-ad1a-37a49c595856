/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './check-inspection-point-items';
import type { ApiQ, SvcRespData } from './check-inspection-point-items.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function checkInspectionPointItems(
  SvcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(SvcQuery);
}
