---
description: 'A checkInspectionPointItems HTTP API service.'
labels: ['service', 'http']
---

## 概述

批量检查测点巡检项值

## 使用

### Browser

```ts
import { checkInspectionPointItems } from '@manyun/ticket.service.check-inspection-point-items';

const { error, data } = await checkInspectionPointItems({ taskNo: '1', deviceGuid: '1' });
```

### Node

```ts
import { checkInspectionPointItems } from '@manyun/ticket.service.check-inspection-point-items/dist/index.node';

const { data } = await checkInspectionPointItems({ taskNo: '1', deviceGuid: '1' });
```
