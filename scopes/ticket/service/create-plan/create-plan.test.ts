/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-7
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import {
  CyclesType,
  JobType,
  MaintenanceCycle,
  PlanStatus,
  SlaUnit,
} from '@manyun/ticket.model.task';

import { createPlan as webService } from './create-plan.browser';
import { createPlan as nodeService } from './create-plan.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    name: 'SUCCESS_ID',
    guidePeriod: MaintenanceCycle.Annual,
    blockScope: [],
    jobType: JobType.DeviceInspection,
    jobSla: 1,
    slaUnit: SlaUnit.Day,
    jobItems: [],
    allowTriggerTime: ['12:00', '15:02'],
    endTime: 1671983999999,
    status: PlanStatus.On,
    splitors: [],
    cycles: {
      periodUnit: CyclesType.Day,
      period: 1,
      dayConfig: {
        days: null,
        isRemove: 0,
      },
    },
    periodUnit: CyclesType.Day,
    subJobType: 'MANUAL_INVENTORY',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    name: 'FAIL_ID',
    blockScope: [],
    guidePeriod: MaintenanceCycle.Annual,
    jobType: JobType.DeviceInspection,
    jobSla: 1,
    slaUnit: SlaUnit.Day,
    jobItems: [],
    allowTriggerTime: ['12:00', '15:02'],
    endTime: 1671983999999,
    status: PlanStatus.On,
    splitors: [],
    cycles: {
      periodUnit: CyclesType.Day,
      period: 1,
      dayConfig: {
        days: null,
        isRemove: 0,
      },
    },
    periodUnit: CyclesType.Day,
    subJobType: 'MANUAL_INVENTORY',
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    name: 'SUCCESS_ID',
    blockScope: [],
    guidePeriod: MaintenanceCycle.Annual,
    jobType: JobType.DeviceInspection,
    jobSla: 1,
    slaUnit: SlaUnit.Day,
    jobItems: [],
    allowTriggerTime: ['12:00', '15:02'],
    endTime: 1671983999999,
    status: PlanStatus.On,
    splitors: [],
    cycles: {
      periodUnit: CyclesType.Day,
      period: 1,
      dayConfig: {
        days: null,
        isRemove: 0,
      },
    },
    periodUnit: CyclesType.Day,
    subJobType: 'MANUAL_INVENTORY',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    name: 'FAIL_ID',
    blockScope: [],
    guidePeriod: MaintenanceCycle.Annual,
    jobType: JobType.DeviceInspection,
    jobSla: 1,
    slaUnit: SlaUnit.Day,
    jobItems: [],
    allowTriggerTime: ['12:00', '15:02'],
    endTime: 1671983999999,
    status: PlanStatus.On,
    splitors: [],
    cycles: {
      periodUnit: CyclesType.Day,
      period: 1,
      dayConfig: {
        days: null,
        isRemove: 0,
      },
    },
    periodUnit: CyclesType.Day,
    subJobType: 'MANUAL_INVENTORY',
  });

  expect(typeof error).toBe('object');
});
