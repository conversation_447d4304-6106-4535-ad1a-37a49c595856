/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-7
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendCreateParams, BackendPlan } from '@manyun/ticket.model.task';

export type SvcQuery = 'success' | 'error';

export type SvcRespData = BackendPlan | null;

export type RequestRespData = BackendPlan | null;

//SvcQuery与ApiQ相同
export type ApiQ = BackendCreateParams;

export type ApiR = Response<RequestRespData | null>;
