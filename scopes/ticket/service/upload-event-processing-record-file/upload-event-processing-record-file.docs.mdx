---
description: 'A uploadEventProcessingRecordFile HTTP API service.'
labels: ['service', 'http']
---

处理记录文件上传

## Usage

### Browser

```ts
import { uploadEventProcessingRecordFile } from '@manyun/ticket.service.upload-event-processing-record-file';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { UploadEventProcessingRecordFileService } from '@manyun/ticket.service.upload-event-processing-record-file/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = UploadEventProcessingRecordFileService.from(nodeRequest);
```
