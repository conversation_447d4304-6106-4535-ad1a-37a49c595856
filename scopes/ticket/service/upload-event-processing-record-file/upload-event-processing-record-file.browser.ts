/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-14
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './upload-event-processing-record-file.js';
import type { ApiArgs,  } from './upload-event-processing-record-file.type.js';

/**
 * @param args
* @returns
 */
export function uploadEventProcessingRecordFile(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
