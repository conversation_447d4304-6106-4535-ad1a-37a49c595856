/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './upload-event-processing-record-file.type.js';

const endpoint = '/dcom/event/opt/record/file/upload';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/146/interface/api/30596)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (svcQuery: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    const { error, data, ...rest } = await request.tryPost<boolean, ApiArgs>(endpoint, svcQuery);

    return { error, data, ...rest };
  };
}
