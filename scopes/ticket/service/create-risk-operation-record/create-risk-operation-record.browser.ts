/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-risk-operation-record';
import type { ApiQ, SvcRespData } from './create-risk-operation-record.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createRiskOperationRecord(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
