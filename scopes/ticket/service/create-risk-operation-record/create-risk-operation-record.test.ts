/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createRiskOperationRecord as webService } from './create-risk-operation-record.browser';
import { createRiskOperationRecord as nodeService } from './create-risk-operation-record.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    taskNo: 'SUCCESS_ID',
    staffId: 'test',
    optInfo: 'test',
    optTime: 'test',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    taskNo: 'FAIL_ID',
    staffId: 'test',
    optInfo: 'test',
    optTime: 'test',
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    taskNo: 'SUCCESS_ID',
    staffId: 'test',
    optInfo: 'test',
    optTime: 'test',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    taskNo: 'FAIL_ID',
    staffId: 'test',
    optInfo: 'test',
    optTime: 'test',
  });

  expect(typeof error).toBe('object');
});
