/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-risk-register-measure';
import type { ApiQ, SvcRespData } from './delete-risk-register-measure.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deleteRiskRegisterMeasure(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
