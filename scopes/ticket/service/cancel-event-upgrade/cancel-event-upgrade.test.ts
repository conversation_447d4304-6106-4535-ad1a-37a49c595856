/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-1-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { cancelEventUpgrade as webService } from './cancel-event-upgrade.browser';
import { cancelEventUpgrade as nodeService } from './cancel-event-upgrade.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ eventId: 1 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ eventId: 0 });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ eventId: 1 });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ eventId: 0 });

  expect(typeof error).toBe('object');
});
