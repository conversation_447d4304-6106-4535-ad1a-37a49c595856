/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  changeOrderId: string;
  operatorId: number;
  operatorName: string;
  exeContent: string;
  exeTime: number;
};

export type ApiR = WriteResponse;
