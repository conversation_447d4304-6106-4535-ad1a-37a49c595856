/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancel-event-relieve';
import type { ApiQ, SvcRespData } from './cancel-event-relieve.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function cancelEventRelieve(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
