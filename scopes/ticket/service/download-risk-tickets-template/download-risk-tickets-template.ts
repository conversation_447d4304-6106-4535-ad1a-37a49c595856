/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './download-risk-tickets-template.type';

const endpoint = '/taskcenter/risk/model/download';

/**
 * 风险工单导入模版下载
 * @see [Doc]( http://172.16.0.17:13000/project/164/interface/api/19686)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint, {
      responseType: 'blob',
    });

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data, ...rest };
  };
}
