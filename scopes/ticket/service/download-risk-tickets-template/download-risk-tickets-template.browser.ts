/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-risk-tickets-template';
import type { SvcRespData } from './download-risk-tickets-template.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function downloadRiskTicketsTemplate(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
