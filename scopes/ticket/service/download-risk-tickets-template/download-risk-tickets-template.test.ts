/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { downloadRiskTicketsTemplate as webService } from './download-risk-tickets-template.browser';
import { downloadRiskTicketsTemplate as nodeService } from './download-risk-tickets-template.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService();

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});
