/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-8
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-event.type';

const endpoint = '/dcom/event/create';

/**
 * @see [Doc](YAPI http://yapi.manyun-local.com/project/146/interface/api/18552)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      ...svcQuery,
      files: svcQuery.files?.map(file => McUploadFile.fromJSON(file).toApiObject()),
    };
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
