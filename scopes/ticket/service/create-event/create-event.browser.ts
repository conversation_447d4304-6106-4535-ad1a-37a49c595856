/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-event';
import type { SvcQuery, SvcRespData } from './create-event.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createEvent(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
