/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-8
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendFalseAlarm, FaultTargetType } from '@manyun/ticket.model.event';

export type EventCreateType = {
  idcTag?: string;
  blockTag?: string;
  occurTime?: number;
  detectTime?: number;
  eventLevel?: string;
  eventLevelName?: string;
  eventSource?: string;
  eventSourceName?: string;
  topCategory?: string;
  topCategoryName?: string;
  secondCategory?: string;
  secondCategoryName?: string;
  eventDesc?: string;
  files?: BackendMcUploadFile[];
  location?: string;
  northSync?: boolean;
  eventTitle?: string;
  changeCode?: string;
  incidentType?: string[];
  liablePerson?: { label: string; value: string };
  eventOwner?: { label: string; value: number } | null;
  infoType?: FaultTargetType;
  causeDevice?: string | ({ key: string } | string)[];
  eventInfluence?: {
    gridInfluence?: string;
    influenceScope: {
      influenceType: string;
      influenceGuid: string;
    }[];
  };
  liableDept?: string;
  createTime?: number;
  upgradeTime?: number;
  confirmTime?: number;
  confirmBy?: number;
  confirmByName?: string;
  causeDesc?: string;
  causeBy?: string;
  isFalseAlarm?: BackendFalseAlarm;
  isChangeAlarm?: boolean;
  enableRelieveSkip?: boolean;
  associateAlarmInfo?: {
    alarmIds: number[];
    alarmStartTimes: number[];
    causeDevices?: string;
  };
  eventOwnerInfoList?: number[] | null;
  majorCode?: string | null;
  faultDesc?: string | null;
  locationList?: EventLocationInfo[] | null;
};

export type EventLocationInfo = {
  /**
   * BLOCK(楼栋)，ROOM(包间)，DEVICE(设备)
   */
  locationType: string;
  /**
   *
   *   当位置类型为楼栋时不传
   *   为包间时传包间类型code
   *   为设备时传设备类型code
   */
  subType?: string | null;
  /**
   * 楼栋guid、包间guid、设备guid
   */
  guid: string;
  /**
   * 楼栋名称、包间名称、设备名称
   */
  name?: string | null;
  deviceLabel?: string | null;
  fromBlockGuid?: string | null;
  fromBlockName?: string | null;
  fromRoomGuid?: string | null;
  fromRoomName?: string | null;
};
export type SvcRespData = string | null;

export type RequestRespData = string | null;

export type SvcQuery = { files?: McUploadFile[] } & Omit<EventCreateType, 'files'>;
// ApiQ与SvcQuery一致
export type ApiQ = EventCreateType;

export type ApiR = WriteResponse;
