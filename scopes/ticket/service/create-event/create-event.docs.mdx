---
description: 'A createEvent HTTP API service.'
labels: ['service', 'http']
---

## 概述

创建事件

## 使用

### Browser

```ts
import { createEvent } from '@manyun/ticket.service.create-event';

const { error, data } = await createEvent({
  isChangeAlarm: false,
  isFalseAlarm: false,
  changeCode: '',
  eventOwnerId: 1,
  eventOwnerName: 'admin',
  occurTime: 1660121005781,
  detectTime: 1660531654991,
  files: [],
  blockTag: 'CQ1.B',
  idcTag: 'CQ1',
  eventSource: '1',
  eventSourceName: '监控系统',
});
```

### Node

```ts
import { createEvent } from '@manyun/ticket.service.create-event/dist/index.node';

const { data } = await createEvent({
  isChangeAlarm: false,
  isFalseAlarm: false,
  changeCode: '',
  eventOwnerId: 1,
  eventOwnerName: 'admin',
  occurTime: 1660121005781,
  detectTime: 1660531654991,
  files: [],
  blockTag: 'CQ1.B',
  idcTag: 'CQ1',
  eventSource: '1',
  eventSourceName: '监控系统',
});
```
