/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import { BackendFalseAlarm, FaultTargetType } from '@manyun/ticket.model.event';

import { createEvent as webService } from './create-event.browser';
import { createEvent as nodeService } from './create-event.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    isChangeAlarm: false,
    isFalseAlarm: BackendFalseAlarm.FalseAlarm,
    eventSource: 'test',
    eventSourceName: '1',
    eventOwner: { value: 1, label: 'admin' },
    idcTag: '',
    blockTag: '',
    createTime: 1,
    infoType: FaultTargetType.Device,
    occurTime: 1,
    detectTime: 1,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    isChangeAlarm: false,
    isFalseAlarm: BackendFalseAlarm.FalseAlarm,
    eventSource: 'test',
    eventSourceName: '1',
    eventOwner: { value: 1, label: 'admin' },
    idcTag: '',
    blockTag: '',
    createTime: 1,
    infoType: FaultTargetType.Device,
    occurTime: 1,
    detectTime: 1,
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    isChangeAlarm: false,
    isFalseAlarm: BackendFalseAlarm.FalseAlarm,
    eventSource: 'test',
    eventSourceName: '1',
    eventOwner: { value: 1, label: 'admin' },
    idcTag: '',
    blockTag: '',
    createTime: 1,
    infoType: FaultTargetType.Device,
    occurTime: 1,
    detectTime: 1,
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    isChangeAlarm: false,
    isFalseAlarm: BackendFalseAlarm.FalseAlarm,
    eventSource: 'test',
    eventSourceName: '1',
    eventOwner: { value: 1, label: 'admin' },
    idcTag: '',
    blockTag: '',
    createTime: 1,
    infoType: FaultTargetType.Device,
    occurTime: 1,
    detectTime: 1,
  });

  expect(typeof error).toBe('object');
});
