/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-10
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-event-custom-influences';
import type { ApiQ } from './export-event-custom-influences.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportEventCustomInfluences(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(svcQuery);
}
