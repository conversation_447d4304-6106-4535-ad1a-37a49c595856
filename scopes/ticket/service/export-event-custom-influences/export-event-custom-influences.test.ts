/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-10
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportEventCustomInfluences as webService } from './export-event-custom-influences.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ eventId: 1 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ eventId: 0 });

  expect(typeof error).toBe('object');
});
