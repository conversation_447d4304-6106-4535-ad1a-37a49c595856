/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-10
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ } from './export-event-custom-influences.type';

const endpoint = '/dcom/event/influence/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/146/interface/api/23520)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<Blob>> => {
    const { error, data, ...rest } = await request.tryPost<Blob, ApiQ>(endpoint, svcQuery, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
