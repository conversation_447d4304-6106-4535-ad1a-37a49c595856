/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 楼栋：EC01.A
   */
  blockTag?: string | null;
  /**
   * 关单开始时间
   */
  checkEndTime?: string | null;
  /**
   * 关单结束时间
   */
  checkStartTime?: string | null;
  /**
   * 创建人ID
   */
  creatorId?: number | null;
  /**
   * 创建人名称
   */
  creatorName?: string | null;
  /**
   * 是否是当前处理人
   */
  currentTaskAssignee?: boolean | null;
  /**
   * 创建开始时间
   */
  effectEndTime?: string | null;
  /**
   * 创建结束时间
   */
  effectStartTime?: string | null;
  /**
   * 机房
   */
  idcTag?: string | null;
  /**
   * 是否查询当前用户是创建人工单 是：true 否:false
   */
  initiator?: boolean | null;
  /**
   * 是否延迟
   */
  isDelay?: string | null;
  needTotalCount?: boolean | null;
  offset?: number | null;
  /**
   * true: 开启初始化以及撤销工单
   */
  openInitWithdraw?: boolean | null;
  /**
   * true: 开启任务触发产生工单
   */
  openSch?: string | null;
  /**
   * 生效时间
   */
  effectTime?: string | null;
  pageNum?: number | null;
  pageSize?: number | null;
  /**
   * 关联工单单号
   */
  relateTaskNo?: string | null;
  /**
   * 包间:EC06.A.A2-4
   */
  roomGuid?: string | null;
  roomGuidList?: string[] | null;
  /**
   * 任务ID
   */
  scheduleId?: number | null;
  subjectTag?: string | null;
  /**
   * 处理人Id
   */
  taskAssignee?: number | null;
  /**
   * 处理人姓名
   */
  taskAssigneeName?: string | null;
  /**
   * 工单编号
   */
  taskNo?: string | null;
  /**
   * 工单编号列表
   */
  taskNoList?: string[] | null;
  /**
   * 工单类型
   */
  taskStatus?: string | null;
  /**
   * 工单类型列表
   */
  taskStatusList?: string[] | null;
  /**
   * 工单子类型
   */
  taskSubType?: string | null;
  /**
   * 工单子类型列表
   */
  taskSubTypeList?: string[] | null;
  /**
   * 工单标题
   */
  taskTitle?: string | null;
  /**
   * 工单类型
   */
  taskType?: string | null;
  /**
   * 指派人信息
   */
  assigneeList?:
    | {
        /**
         * 用户ID
         */
        id: number;
        /**
         * 用户姓名
         */
        userName: string;
      }[]
    | null;
  /**
   * 关单流程CODE: 有走关单审批、无则正常关单
   */
  approvalCode?: string | null;
  /**
   * 巡检包间扫码
   */
  configRoomGuid?: string | null;
  /**
   * 是否按时间排序
   */
  onlyByTime?: boolean | null;
  /**
   * ASC：时间升序 / DESC ：时间倒序
   */
  orderByTimeInfo?: string | null;
};

export type ApiResponse = Response<Blob>;
