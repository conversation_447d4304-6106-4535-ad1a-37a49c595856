/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-risk-check-ticket';
import type { ApiArgs } from './export-risk-check-ticket.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function exportRiskCheckTicket(args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(args);
}
