/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-10
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { RiskStatus } from '@manyun/ticket.gql.client.risk-register';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  riskId: string;
  optType: OptType;
  curRiskStatus: RiskStatus;
};

export type OptType = 'REVOKE' | 'RESTART';

export type ApiR = WriteResponse;
