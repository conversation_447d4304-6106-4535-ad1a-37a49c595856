/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { changeRiskRegisterStatus as webService } from './change-risk-register-status.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    riskId: '1',
    optType: 'RESTART',
    curRiskStatus: 'DRAFT',
  });

  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ riskId: '2', optType: 'RESTART', curRiskStatus: 'DRAFT' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
