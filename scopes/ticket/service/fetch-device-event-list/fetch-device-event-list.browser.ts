/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-08-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-event-list';
import type { SvcQuery, SvcRespData } from './fetch-device-event-list.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceEventList(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
