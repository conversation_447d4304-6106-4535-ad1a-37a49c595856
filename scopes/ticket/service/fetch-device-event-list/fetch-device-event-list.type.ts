/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-08-24
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendEventStatus } from '@manyun/ticket.model.event';
import { DeviceEventType } from '@manyun/ticket.service.fetch-device-event-count/fetch-device-event-count.type';

export type SvcQuery = ApiQ;
export type DeviceEventSvcQuery = SvcQuery;

// @FIXME renaming me
export type DeviceEventModel = {
  /** 事件id */
  id: number;
  /** 机房tag */
  idcTag: string;
  /** 楼栋guid	*/
  blockTag: string;
  /** 事件级别 */
  eventLevel: string;
  /** 事件级别名称 */
  eventLevelName: null;
  /** 事件来源 */
  eventSource: string;
  /** 事件来源名 */
  eventSourceName: string;
  /** 一级分类 */
  topCategory: string;
  topCategoryName: string;
  secondCategory: string;
  secondCategoryName: string;
  /** 事件描述 */
  eventDesc: string;
  occurTime: number;
  /** 事件状态 */
  eventStatus: {
    code: keyof typeof DeviceEventType;
    desc: string;
    status: string;
    statusCode: string;
  };
  /** 事件负责人id */
  eventOwnerId: number;
  /** 事件负责人名 */
  eventOwnerName: string;
  /** 创建时间 */
  gmtCreate: number;
};

export type SvcRespData = {
  data: DeviceEventModel[];
  total: number;
};

export type RequestRespData = {
  data: DeviceEventModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  idcTags?: string[];
  /* 这里实际上需要guid */
  blockTags?: string[];
  deviceGuid: string;
  pageNum?: number;
  pageSize?: number;
  statusList?: BackendEventStatus[];
};

export type ApiR = ListResponse<DeviceEventModel[] | null>;
