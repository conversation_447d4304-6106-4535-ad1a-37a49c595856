---
description: 'A fetchDeviceEventList HTTP API service.'
labels: ['service', 'http', fetch-device-event-list]
---

## 概述

获取事件列表

## 使用

### Browser

```ts
import { fetchDeviceEventList } from '@resource-hub/service.fetch-device-event-list';

const { error, data } = await fetchDeviceEventList('success');
const { error, data } = await fetchDeviceEventList('error');
```

### Node

```ts
import { fetchDeviceEventList } from '@resource-hub/service.fetch-device-event-list/dist/index.node';

const { data } = await fetchDeviceEventList('success');

try {
  const { data } = await fetchDeviceEventList('error');
} catch (error) {
  // ...
}
```
