/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-12-16
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiQ, ApiResponseData } from './export-change-online-step.type.js';

const endpoint = '/dcom/change/online/order/step/import';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/84/interface/api/29696)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<ApiResponseData>> => {
    const { error, data, ...rest } = await request.tryPost<ApiResponseData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
