---
description: 'A exportChangeOnlineStep HTTP API service.'
labels: ['service', 'http']
---

步骤导入

## Usage

### Browser

```ts
import { exportChangeOnlineStep } from '@manyun/ticket.service.export-change-online-step';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { ExportChangeOnlineStepService } from '@manyun/ticket.service.export-change-online-step/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ExportChangeOnlineStepService.from(nodeRequest);
```
