/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-12-16
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './export-change-online-step.js';
import type { ApiQ, ApiResponse } from './export-change-online-step.type.js';

/**
 * @returns
 */
export function exportChangeOnlineStep(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(svcQuery);
}
