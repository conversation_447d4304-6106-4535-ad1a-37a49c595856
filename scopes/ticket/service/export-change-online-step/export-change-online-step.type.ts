/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-12-16
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ErrorCheckDto = {
  errDto: Record<string, string>;
  errMessage: Record<string, string>;
  /**
   * 错误信息
   */
  totalErrMessage: string;
};

export type CorrectDto = {
  stepName: string;
  operator: string;
};

export type ApiResponseData = {
  errorCheckDtoList?: ErrorCheckDto[] | null;
  correctDtoList?: CorrectDto[] | null;
};

export type SvcRespData = ApiResponseData;

export type RequestRespData = ApiResponseData;
export type ApiQ = FormData;

export type ApiResponse = BackendResponse<ApiResponseData>;
