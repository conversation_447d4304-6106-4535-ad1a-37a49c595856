/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type AlarmInfluence = {
  /** 用户影响面 */
  customerCount: number;
  /** 设备影响面 */
  deviceCount: number;
  /** 机柜影响面 */
  gridCount: number;
};

export type RespondAlarmInfluence = AlarmInfluence | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  tenantId?: string;
  targetId?: number | string;
  blockTag?: string;
  bizScenario?: { uniqueIdentity: string };
  deviceGuidList?: string[];
  idcTag?: string;
};

export type ApiR = Response<AlarmInfluence | null>;
