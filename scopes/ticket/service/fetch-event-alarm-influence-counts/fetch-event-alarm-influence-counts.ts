/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  AlarmInfluence,
  ApiQ,
  RespondAlarmInfluence,
} from './fetch-event-alarm-influence-counts.type';

const endpoint = '/dcim/alarm/query/influence/count';

/**
 * @see [Doc](http://172.16.0.17:13000/project/86/interface/api/2280)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<RespondAlarmInfluence>> => {
    const { error, data, ...rest } = await request.tryPost<AlarmInfluence | null, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data, ...rest };
  };
}
