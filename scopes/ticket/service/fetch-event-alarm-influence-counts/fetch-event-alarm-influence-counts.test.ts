/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchEventAlarmInfluenceCounts as webService } from './fetch-event-alarm-influence-counts.browser';
import { fetchEventAlarmInfluenceCounts as nodeService } from './fetch-event-alarm-influence-counts.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ targetId: 1 });

  expect(error).toBe(undefined);
  expect(data?.customerCount).toBe(123);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ targetId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ targetId: 1 });

  expect(error).toBe(undefined);
  expect(data?.customerCount).toBe(123);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ targetId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
