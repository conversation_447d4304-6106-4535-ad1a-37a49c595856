/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-event-alarm-influence-counts';
import type { ApiQ, RespondAlarmInfluence } from './fetch-event-alarm-influence-counts.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchEventAlarmInfluenceCounts(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<RespondAlarmInfluence>> {
  return executor(svcQuery);
}
