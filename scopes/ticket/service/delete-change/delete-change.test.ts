/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteChange as webService } from './delete-change.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ changeOrderId: '1' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ changeOrderId: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
