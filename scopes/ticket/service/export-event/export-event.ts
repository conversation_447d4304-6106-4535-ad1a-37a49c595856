/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-8-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, RequestRespData } from './export-event.type.js';

const endpoint = '/dcom/new_event/export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/409/interface/api/27621)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<RequestRespData>> => {
    // return await request.tryPost<boolean, ApiArgs>(endpoint, args);
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiArgs>(
      endpoint,
      args,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
