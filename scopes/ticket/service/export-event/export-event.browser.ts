/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-8-21
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './export-event.js';
import type { ApiArgs } from './export-event.type.js';

/**
 * @param args
 * @returns
 */
export function exportEvent(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
