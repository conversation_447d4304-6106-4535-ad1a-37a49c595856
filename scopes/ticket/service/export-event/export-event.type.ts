/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-8-21
 *
 * @packageDocumentation
 */

export type ApiArgs = {
  pageNum?: number | null;
  pageSize?: number | null;
  /**
   * CONFIRMING(事件确认)、RELIEVING(事件缓解)、FINISHING(事件结束)、REVIEWING(复盘)、AUDITING(评审)、CLOSED(关闭)
   */
  status?: string[] | null;
  /**
   * 事件单号
   */
  eventId?: string | null;
  /**
   * JS1
   */
  idcTagList?: string[] | null;
  /**
   * JS1.A
   */
  blockGuidList?: string[] | null;
  /**
   * 事件来源元数据code
   */
  eventSourceCode?: string | null;
  /**
   * 事件等级元数据code
   */
  eventLevelCode?: string | null;
  categoryCodeList?: string[] | null;
  /**
   * 线上处理人ID
   */
  ownerId?: number | null;
  /**
   * 创建人ID
   */
  createUserId?: string | null;
  /**
   * 事件发生时间
   */
  occurBeginTime?: number | null;
  /**
   * 事件发生时间
   */
  occurEndTime?: number | null;
  /**
   * 事件标题
   */
  eventTitle?: string | null;
  /**
   * 是否误报，【0,1,2】
   */
  falseAlarms?: number[] | null;
  /**
   * 设备guid
   */
  deviceGuid?: string | null;
  /**
   * 创建时间的开始时间
   */
  createBeginTime?: string | null;
  /**
   * 创建时间的结束时间
   */
  createEndTime?: string | null;
  /**
   * 关闭时间查询开始时间
   */
  closeBeginTime?: string | null;
  /**
   * 关闭时间查询结束时间
   */
  closeEndTime?: string | null;
  /**
   * 包间及其他
   */
  relateName?: string | null;
  /**
   * closeTime(关闭时间)、eventLevel(事件等级)
   */
  orderField?: string | null;
  /**
   * 默认为true
   * true: 降序
   * false：升序
   */
  desc?: boolean | null;
  exportIncludeFields: string[];
};

export type SvcRespData = Blob;

export type RequestRespData = Blob;
