---
description: 'A exportEvent HTTP API service.'
labels: ['service', 'http']
---

事件导出

## Usage

### Browser

```ts
import { exportEvent } from '@manyun/ticket.service.export-event';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { ExportEventService } from '@manyun/ticket.service.export-event/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ExportEventService.from(nodeRequest);
```
