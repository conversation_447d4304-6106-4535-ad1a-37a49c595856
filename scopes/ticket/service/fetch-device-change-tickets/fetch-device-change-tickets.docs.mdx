---
description: 'A fetchDeviceChangeTickets HTTP API service.'
labels: ['service', 'http', fetch-device-change-tickets]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceChangeTickets } from '@ticket/service.fetch-device-change-tickets';

const { error, data } = await fetchDeviceChangeTickets({ deviceGuid: '10101' });
```

### Node

```ts
import { fetchDeviceChangeTickets } from '@ticket/service.fetch-device-change-tickets/dist/index.node';

const { data } = await fetchDeviceChangeTickets({ deviceGuid: '10101' });

try {
  const { data } = await fetchDeviceChangeTickets({ deviceGuid: '' });
} catch (error) {
  // ...
}
```
