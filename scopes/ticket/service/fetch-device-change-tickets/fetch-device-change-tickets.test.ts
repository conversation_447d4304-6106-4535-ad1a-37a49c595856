/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceChangeTickets as webService } from './fetch-device-change-tickets.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090103', queryType: 'CHECK_ITEM' });

  expect(error).toBe(undefined);
  expect(data.data[0]).toHaveProperty('changeOrderId');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090101', queryType: 'CHECK_ITEM' });

  expect(error?.code).toBe('error');
  expect(data.data).toStrictEqual([]);
});
