/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-change-tickets';
import type { SvcQuery, SvcRespData } from './fetch-device-change-tickets.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceChangeTickets(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
