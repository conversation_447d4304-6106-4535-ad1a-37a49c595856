/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-18
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;
export type DeviceChangeQuery = ApiQ;

export type DeviceChangeListModel = {
  /** 变更ID */
  changeOrderId: string;
  exeUserGroupCode: string;
  idcTag: string;
  /** 位置 */
  blockTag: string;
  templateId: string;
  templateName: string;
  /** 变更类型 */
  changeType: string;
  changeTypeName: string;
  /** 变更等级 */
  riskLevel: string;
  /** 计划变更开始时间 */
  planStartTime: number;
  /** 计划变更完成时间 */
  planEndTime: number;
  /** 变更状态 */
  changeStatus: string;
  creatorId: number;
  /** 提单人 */
  creatorName: string;
  /** 执行变更开始时间 */
  realStartTime: number;
  /** 执行变更完成时间 */
  realEndTime: number;
  /** 变更标题 */
  title: string;
  workFlowId: number;
  modifyPersonId: number;
  modifyPersonName: string;
  gmtModified?: number;
  /** 创建时间 */
  gmtCreate?: number;
};

export type SvcRespData = {
  data: DeviceChangeListModel[];
  total: number;
};

export type RequestRespData = {
  data: DeviceChangeListModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  blockTag?: string;
  changeOrderId?: string;
  idcTag?: string;
  /** 是否去除草稿状态工单 默认值：'TRUE'    'FALSE' */
  notEqualDraft?: string;
  deviceGuid: string;
  riskLevel?: null;
  // urgency?: null;
  changeType?: null;
  /** 计划开始时间 2022-06-22 00:00:00	 */
  planStartTime?: string;
  /** 计划结束时间 2022-06-22 00:00:00	 */
  planEndTime?: string;
  // 执行开始时间 2022-06-22 00:00:00
  executeStartTime?: string;
  // 执行结束时间 2022-06-22 00:00:00
  executeEndTime?: string;
  statusList?: null;
  // plan_start_time plan_end_time real_start_time real_end_time
  sortField?: string;
  // DESCEND:ASCEND
  sortOrder?: 'DESCEND' | 'ASCEND';
  pageNum?: number;
  pageSize?: number;
  queryType?: 'OP_ITEM' | 'CHECK_ITEM';
};

export type ApiR = ListResponse<DeviceChangeListModel[] | null>;
