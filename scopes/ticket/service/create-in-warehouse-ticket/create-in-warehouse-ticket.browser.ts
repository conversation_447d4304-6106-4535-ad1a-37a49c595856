/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-in-warehouse-ticket';
import type { ApiQ, RequestRespData } from './create-in-warehouse-ticket.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createInWarehouseTicket(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<RequestRespData>> {
  return executor(svcQuery);
}
