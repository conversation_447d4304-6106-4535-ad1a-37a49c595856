/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData } from './create-in-warehouse-ticket.type';

const endpoint = '/taskcenter/warehouse/inWarehouse/create';

/**
 * @see [Doc](http://172.16.0.17:13000/project/142/interface/api/17840)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<RequestRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );
    if (error || data === null) {
      return {
        error,
        data: null,
        ...rest,
      };
    }
    return { error, data, ...rest };
  };
}
