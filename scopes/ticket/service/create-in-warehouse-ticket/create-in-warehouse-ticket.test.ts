/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createInWarehouseTicket as webService } from './create-in-warehouse-ticket.browser';
import { createInWarehouseTicket as nodeService } from './create-in-warehouse-ticket.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    taskTitle: 'EC06.A入库无编号资产',
    idcTag: 'EC06',
    blockGuid: 'SUCCESS_ID',
    roomGuid: 'EC06.A.A5-5',
    numbered: false,
    fileInfoList: [],
    deviceModelList: [
      {
        supplyVendor: '1',
        topCategory: '7',
        secondCategory: '701',
        deviceType: '70101',
        vendor: 'zj01',
        productModel: 'ces',
        warehouseCount: 123,
      },
    ],
    inWarehouseReason: '1231231',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    taskTitle: 'EC06.A入库无编号资产',
    idcTag: 'EC06',
    blockGuid: 'FAIL_ID',
    roomGuid: 'EC06.A.A5-5',
    numbered: false,
    fileInfoList: [],
    deviceModelList: [
      {
        supplyVendor: '1',
        topCategory: '7',
        secondCategory: '701',
        deviceType: '70101',
        vendor: 'zj01',
        productModel: 'ces',
        warehouseCount: 123,
      },
    ],
    inWarehouseReason: '1231231',
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    taskTitle: 'EC06.A入库无编号资产',
    idcTag: 'EC06',
    blockGuid: 'SUCCESS_ID',
    roomGuid: 'EC06.A.A5-5',
    numbered: false,
    fileInfoList: [],
    deviceModelList: [
      {
        supplyVendor: '1',
        topCategory: '7',
        secondCategory: '701',
        deviceType: '70101',
        vendor: 'zj01',
        productModel: 'ces',
        warehouseCount: 123,
      },
    ],
    inWarehouseReason: '1231231',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    taskTitle: 'EC06.A入库无编号资产',
    idcTag: 'EC06',
    blockGuid: 'FAIL_ID',
    roomGuid: 'EC06.A.A5-5',
    numbered: false,
    fileInfoList: [],
    deviceModelList: [
      {
        supplyVendor: '1',
        topCategory: '7',
        secondCategory: '701',
        deviceType: '70101',
        vendor: 'zj01',
        productModel: 'ces',
        warehouseCount: 123,
      },
    ],
    inWarehouseReason: '1231231',
  });

  expect(typeof error).toBe('object');
});
