/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type RequestRespData = {
  taskNo: string;
  respMsg: string;
} | null;
export type DeviceModel = {
  topCategory: string;
  secondCategory: string;
  deviceType: string;
  vendor: string;
  productModel: string;
  warehouseCount: number;
  assetNo?: string;
  blockGuid?: string;
  deviceGuid?: string;
  idcTag?: string;
  roomGuid?: string;
  roomTag?: string;
  serialNo?: string;
  supplyVendor?: string;
};

export type WarehouseTicket = {
  blockGuid: string;
  deviceModelList: DeviceModel[];
  idcTag: string;
  inWarehouseReason: string;
  numbered: boolean;
  roomGuid: string;
  taskTitle: string;
  fileInfoList?: BackendMcUploadFile[];
  relateTaskNo?: string;
  relateBizType?: string;
  relateUserId?: number;
  relateUserName?: string;
  tenantId?: string;
  bizScenario?: { uniqueIdentity: string };
  creatorId?: number;
  creatorName?: string;
};
// ApiQ 与SvcQuery 相同
export type ApiQ = WarehouseTicket;

export type ApiR = Response<RequestRespData>;
