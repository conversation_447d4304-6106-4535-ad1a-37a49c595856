---
description: 'A fetchChanges HTTP API service.'
labels: ['service', 'http', fetch-changes]
---

## 概述

交接班列表

## 使用

### Browser

```ts
import { fetchChanges } from '@ticket/service.fetch-changes';

const { error, data } = await fetchChanges({ pageNum: 1, pageSize: 10, dutyName: '1' });
const { error, data } = await fetchChanges({ pageNum: 1, pageSize: 10, dutyName: '2' });
```

### Node

```ts
import { fetchChanges } from '@ticket/service.fetch-changes/dist/index.node';

const { data } = await fetchChanges({ pageNum: 1, pageSize: 10, dutyName: '1' });

try {
  const { data } = await fetchChanges({ pageNum: 1, pageSize: 10, dutyName: '2' });
} catch (error) {
  // ...
}
```
