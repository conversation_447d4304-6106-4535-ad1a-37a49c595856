/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendChangeShiftStatus } from '@manyun/ticket.model.ticket';

export type BackendChangeShifts = {
  id: number;
  bizNo: string;
  handoverDate: number;
  applyScheduleId: number;
  applyDutyGroupId: number;
  applyDutyId: number;
  applyDutyGroupName: string;
  applyDutyName: string;
  handScheduleId: number;
  handDutyGroupId: number;
  handDutyGroupName: string;
  handDutyId: number;
  handDutyName: string;
  handUserId: number;
  submitTime: number | null;
  handTime: number | null;
  blockGuid: string;
  bizStatus: BackendChangeShiftStatus;
  gmtCreate: number;
  gmtModified: number;
  creatorId: number;
  handoverInfo: string | null;
  scheduleStartTime: number;
  scheduleEndTime: number;
  groupUsers: number[];
  applyUserId: number;
  handoverCounterSigners: HandoverCounterSigners[];
};
export type HandoverCounterSigners = {
  bizNo: string;
  content: string;
  files: BackendMcUploadFile[];
  finished: boolean;
  gmtCreate: number;
  gmtModified: number;
  id: number;
  staffId: number;
  staffName: string;
};
export type SvcRespData = {
  data: BackendChangeShifts[];
  total: number;
};

export type RequestRespData = {
  data: BackendChangeShifts[] | null;
  total: number;
} | null;

// SvcQuery 和 apiQ相同
export type ApiQ = {
  pageSize: number;
  pageNum: number;
  handoverStartDate?: number;
  handoverEndDate?: number;
  dutyName?: string;
  dutyGroupId?: number;
  handUserId?: number;
  statusList?: string[];
  counterSigners?: number[];
};

export type ChangeShiftsRes = {
  list: BackendChangeShifts[];
  total: number;
};

export type ApiR = ListResponse<BackendChangeShifts[] | null>;

export type ChangeShiftRes = {};
