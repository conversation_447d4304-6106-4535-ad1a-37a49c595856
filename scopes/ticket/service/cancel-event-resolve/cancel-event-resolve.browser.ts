/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-18
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancel-event-resolve';
import type { ApiQ, SvcRespData } from './cancel-event-resolve.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function cancelEventResolve(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
