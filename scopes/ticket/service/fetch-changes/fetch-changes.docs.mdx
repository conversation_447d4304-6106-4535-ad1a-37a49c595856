---
description: 'A fetchChanges HTTP API service.'
labels: ['service', 'http', fetch-changes]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchChanges } from '@ticket/service.fetch-changes';

const { error, data } = await fetchChanges('success');
const { error, data } = await fetchChanges('error');
```

### Node

```ts
import { fetchChanges } from '@ticket/service.fetch-changes/dist/index.node';

const { data } = await fetchChanges('success');

try {
  const { data } = await fetchChanges('error');
} catch(error) {
  // ...
}
```
