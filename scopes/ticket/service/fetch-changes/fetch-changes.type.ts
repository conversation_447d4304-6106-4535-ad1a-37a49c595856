/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Change = {
  // id
  id: number;
  changeOrderId: string;
  exeUserGroupCode: string;
  // 机房tag
  idcTag: string;
  // 楼栋guid
  blockTag: string;
  // urgency: string;

  templateId: string;
  templateName: string | null;
  changeType: string;
  changeTypeName: string;
  riskLevel: string;
  planStartTime: number;
  planEndTime: number;
  changeStatus: string;
  creatorId: number;
  creatorName: string;
  realStartTime: string | null;
  realEndTime: string | null;
  title: string;
  workFlowId: number | null;
  modifyPersonId: number;
  modifyPersonName: string;
  gmtModified: number | string;
  gmtCreate: number | string;
};

export type SvcRespData = {
  data: Change[];
  total: number;
};

export type RequestRespData = {
  data: Change[] | null;
  total: number;
} | null;

// SvcQuery 和 apiQ相同
export type ApiQ = {
  pageSize: number;
  pageNum: number;
  blockTag?: string;
  // urgency?: string;
  changeType?: string;
  planTime?: string;
  realTime?: string;
  statusList?: string[];
  riskLevel?: string;
  title?: string;
  changeOrderId?: string;
  orderByCondition?: string;
  gmtCreateStartTime?: number;
  gmtCreateEndTime?: number;
  idcTag?: string;
  blockTagList?: string[];
};

export type ApiR = ListResponse<Change[] | null>;
