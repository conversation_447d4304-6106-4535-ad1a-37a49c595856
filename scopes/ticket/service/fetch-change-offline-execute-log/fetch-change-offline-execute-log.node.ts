/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-offline-execute-log';
import type { ApiQ, SvcRespData } from './fetch-change-offline-execute-log.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchChangeOfflineExecuteLog(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
