/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChangeOfflineLItem = {
  id: number;
  operatorId: number;
  operatorName: string;
  exeContent: string;
  exeTime: number;
  changeId: string;
};

export type SvcRespData = {
  data: ChangeOfflineLItem[];
  total: number;
};

export type RequestRespData = {
  data: ChangeOfflineLItem[] | null;
  total: number;
} | null;

export type ApiQ = {
  changeOrderId: string;
};

export type ApiR = ListResponse<ChangeOfflineLItem[] | null>;
