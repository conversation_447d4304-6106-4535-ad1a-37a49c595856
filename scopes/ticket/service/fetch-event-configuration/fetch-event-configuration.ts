/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-event-configuration.type';

const endpoint = '/dcom/event/configuration';

/**
 * @see [Doc](http://***********:13000/project/146/interface/api/22881)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData | null>(endpoint);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: {
        skippedAuditEventLevels: data.eventAuditSkip,
        skippedRelieveEventLevels: data.eventRelieveSkip,
        processConfig: data.processConfig,
      },
      ...rest,
    };
  };
}
