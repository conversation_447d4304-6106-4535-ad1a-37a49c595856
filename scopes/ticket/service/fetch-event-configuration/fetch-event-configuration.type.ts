/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { ProcessEngineConfig } from '@manyun/ticket.model.event';

export type BackendEventConfiguration = {
  eventRelieveSkip: string[];
  eventAuditSkip: string[];
  processConfig?: ProcessEngineConfig;
};

export type EventConfiguration = {
  skippedAuditEventLevels: string[];
  skippedRelieveEventLevels: string[];
  processConfig?: ProcessEngineConfig;
};
export type SvcRespData = EventConfiguration | null;

export type RequestRespData = BackendEventConfiguration | null;

export type ApiR = Response<RequestRespData | null>;
