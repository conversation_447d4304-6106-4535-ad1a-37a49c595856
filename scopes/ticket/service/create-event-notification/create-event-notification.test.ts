/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createEventNotification as webService } from './create-event-notification.browser';
import { createEventNotification as nodeService } from './create-event-notification.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    id: 1,
    eventId: 1,
    occurTime: 1,
    reportContent: '',
    creatorId: '',
    creatorName: '',
    reportTime: 1,
    eventLevel: '',
    eventStatus: '',
    eventSource: '',
    eventInfluence: '',
    subscribeCode: '',
    eventProgress: '',
    eventCategory: '',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    id: 1,
    eventId: 0,
    occurTime: 1,
    reportContent: '',
    creatorId: '',
    creatorName: '',
    reportTime: 1,
    eventLevel: '',
    eventStatus: '',
    eventSource: '',
    eventInfluence: '',
    subscribeCode: '',
    eventProgress: '',
    eventCategory: '',
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    id: 1,
    eventId: 1,
    occurTime: 1,
    reportContent: '',
    creatorId: '',
    creatorName: '',
    reportTime: 1,
    eventLevel: '',
    eventStatus: '',
    eventSource: '',
    eventInfluence: '',
    subscribeCode: '',
    eventProgress: '',
    eventCategory: '',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    id: 1,
    eventId: 0,
    occurTime: 1,
    reportContent: '',
    creatorId: '',
    creatorName: '',
    reportTime: 1,
    eventLevel: '',
    eventStatus: '',
    eventSource: '',
    eventInfluence: '',
    subscribeCode: '',
    eventProgress: '',
    eventCategory: '',
  });

  expect(typeof error).toBe('object');
});
