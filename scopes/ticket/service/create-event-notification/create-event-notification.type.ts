/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type EventNotificationCreate = {
  eventId: number;
  /** 通报内容 */
  reportContent: string;

  /** 通报时间 */
  reportTime: number;
  eventLevel: string;
  eventStatus: string;
  eventSource: string;
  /** 影响面 */
  eventInfluence: string;

  /** 事件进展 */
  eventProgress?: string[] | null;
  /** 事件分类 */
  eventCategory: string;
  occurTime: number;
  blockTag: string;
  creatorId?: number;
  creatorName?: string;
  /** 目标类型 */
  infoType?: string;
  /** 订阅code */
  subscribeCode?: string;
  /** 事件描述 */
  eventDesc?: string;
  idcTag?: string;
  /** 原因 */
  causeDesc?: string;
  /** 通报用户名 */
  reportToPersons?: string[];
  /** 通报用户id列表 */
  reportToPersonIds?: number[];
  /** 通报角色Code */
  reportToRoleCodes?: string[];
  /** 通报角色名 */
  reportToRoleNames?: string[];
  /** 通报对象名称数组 */
  reportToObjects?: string[];
  /** 通报渠道 */
  reportChannels?: string[];
  /** 目标对象 */
  causeDevices?: string[] | string;
};

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

// ApiQ 与SvcQuery 相同
export type ApiQ = EventNotificationCreate;
export type ApiR = WriteResponse;
