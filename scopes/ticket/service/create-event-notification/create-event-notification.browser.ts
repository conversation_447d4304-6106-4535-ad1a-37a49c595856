/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-event-notification';
import type { ApiQ, SvcRespData } from './create-event-notification.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createEventNotification(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
