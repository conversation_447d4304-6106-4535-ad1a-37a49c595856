/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { downloadRiskRegisterTemplate as webService } from './download-risk-register-template.browser';
import { downloadRiskRegisterTemplate as nodeService } from './download-risk-register-template.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService();

  expect(error).toBe(undefined);
});
