/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-risk-register-template';
import type { SvcRespData } from './download-risk-register-template.type';

const executor = getExecutor(nodeRequest);

export function downloadRiskRegisterTemplate(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
