/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './download-risk-register-template.type';

const endpoint = '/dcom/risk/register/model/download';

/**
 * @see [Doc](http://172.16.0.17:13000/project/309/interface/api/24696)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
