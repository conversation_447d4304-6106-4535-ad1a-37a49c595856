/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-31
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AccessCardNumberInfo = {
  id: string;
  applyId: string;
  applyName: string;
  applyRole: string;
  dept: string;
  company: string;
  email: string;
  cardNo: string;
  entranceCardNo: string;
  entranceCardType: string;
  entranceCardParentType?: string;
  cardType: string;
  phone: string;
  authInfoList: {
    blockGuid: string;
    blockTag: string;
    authIdList: number[];
    effectTime?: string;
  }[];
};

export type SvcRespData = {
  data: AccessCardNumberInfo[];
  total: number;
};

export type RequestRespData = {
  data: AccessCardNumberInfo[] | null;
  total: number;
} | null;

export type ApiQ = {
  idcTag?: string;
  blockGuid?: string;
  changeType?: string;
};

export type ApiR = ListResponse<AccessCardNumberInfo[] | null>;
