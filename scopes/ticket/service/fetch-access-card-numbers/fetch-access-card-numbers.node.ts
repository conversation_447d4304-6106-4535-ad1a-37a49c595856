/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-31
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-access-card-numbers';
import type { ApiQ, SvcRespData } from './fetch-access-card-numbers.type';

const executor = getExecutor(nodeRequest);

/**
 * @param vasvcQueryriant
 * @returns
 */
export function fetchAccessCardNumbers(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
