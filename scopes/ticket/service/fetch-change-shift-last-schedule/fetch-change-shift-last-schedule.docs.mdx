---
description: 'A fetchChangeShiftLastSchedule HTTP API service.'
labels: ['service', 'http', change-shift-last-schedule]
---

## 概述

根据当前排班 id，查询上一次的交接班

## 使用

### Browser

```ts
import { fetchChangeShiftLastSchedule } from '@ticket/service.fetch-change-shift-last-schedule';

const { error, data } = await fetchChangeShiftLastSchedule({ scheduleId: '1' });
const { error, data } = await fetchChangeShiftLastSchedule({ scheduleId: '2' });
```

### Node

```ts
import { fetchChangeShiftLastSchedule } from '@ticket/service.fetch-change-shift-last-schedule/dist/index.node';

const { data } = await fetchChangeShiftLastSchedule({ scheduleId: '1' });

try {
  const { data } = await fetchChangeShiftLastSchedule({ scheduleId: '2' });
} catch (error) {
  // ...
}
```
