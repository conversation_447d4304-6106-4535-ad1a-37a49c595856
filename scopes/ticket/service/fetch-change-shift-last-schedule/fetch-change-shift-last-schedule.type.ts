/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-27
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendChangeShiftStatus } from '@manyun/ticket.model.ticket';

export type ChangeShiftJSON = {
  id: number;
  bizNo: string;
  handoverDate: number;
  applyScheduleId: number;
  applyDutyGroupId: number;
  applyDutyId: number;
  applyDutyGroupName: string;
  applyDutyName: string;
  handScheduleId: number;
  handDutyGroupId: number;
  handDutyGroupName: string;
  handDutyId: number;
  handDutyName: string;
  handUserId: number;
  submitTime: null;
  handTime: null;
  blockGuid: string;
  bizStatus: BackendChangeShiftStatus;
  gmtCreate: number;
  gmtModified: number;
  creatorId: number;
  handoverInfo: string;
  scheduleStartTime: number;
  scheduleEndTime: number;
  taskTitle: string;
  handoverEvents: HandoverEvents[];
};

export type HandoverEvents = {
  eventName: string;
  eventCode: string | null;
  remarks: string;
  value: string;
};

export type SvcRespData = ChangeShiftJSON | null;

export type RequestRespData = ChangeShiftJSON | null;

export type ApiQ = {
  scheduleId: number;
};

export type ApiR = ListResponse<ChangeShiftJSON[] | null>;
