/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-27
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChangeShiftLastSchedule as webService } from './fetch-change-shift-last-schedule.browser';
import { fetchChangeShiftLastSchedule as nodeService } from './fetch-change-shift-last-schedule.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ scheduleId: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ scheduleId: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ scheduleId: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ scheduleId: '2' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
