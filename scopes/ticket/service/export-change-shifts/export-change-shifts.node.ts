/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-change-shifts';
import type { ApiQ, SvcRespData } from './export-change-shifts.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function exportChangeShifts(svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
