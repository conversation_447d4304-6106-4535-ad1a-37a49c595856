/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportDrillPlans as webService } from './export-drill-plans.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ jobTypeList: ['1'], includeColumnFiledNames: ['1'] });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ jobTypeList: ['0'], includeColumnFiledNames: ['0'] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
