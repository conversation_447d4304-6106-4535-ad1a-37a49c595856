/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-drill-plans';
import type { ApiArgs, ApiResponse } from './export-drill-plans.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function exportDrillPlans(args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> {
  return executor(args);
}
