/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */

export type ApiArgs = {
  includeColumnFiledNames: string[];
  /**
   * 楼栋
   */
  blockGuidList?: string[] | null;
  /**
   * 专业类型
   */
  subJobType?: string | null;
  /**
   * 等级
   */
  schLevel?: string | null;
  /**
   * 名称
   */
  name?: string | null;
  /**
   * 周期
   */
  periodUnit?: string | null;
  /**
   * 是否有效
   */
  isInvalid?: string | null;
  /**
   * 启用状态
   */
  taskStatus?: string | null;
  /**
   * 创建人
   */
  creatorId?: number | null;
  /**
   * 开始时间
   */
  startTime?: number | null;
  /**
   * 结束时间
   */
  endTime?: number | null;
  /**
   * 任务类型：SCH_EXERCISE
   */
  jobTypeList: string[];
};

export type ApiResponse = Blob;
