/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './export-drill-plans.type';

const endpoint = '/taskcenter/schedule/drill/list/export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/349/interface/api/26687)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> => {
    return await request.tryPost<ApiResponse, ApiArgs>(endpoint, args, {
      responseType: 'blob',
    });
  };
}
