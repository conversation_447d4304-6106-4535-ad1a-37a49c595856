/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './complete-offline-change';
import type { ApiQ, SvcRespData } from './complete-offline-change.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function completeOfflineChange(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
