/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-19
 *
 * @packageDocumentation
 */

export type Assignee = {
  id?: string | null;
  userName?: string | null;
};

export type ApiArgs = {
  /**
   * 标题
   */
  title?: string | null;
  /**
   * 标题或单号
   */
  titleOrExecNo?: string | null;
  /**
   * 负责人
   */
  principalId?: number | null;
  /**
   * 处理人
   */
  taskAssignee?: number | null;
  /**
   * 演练单号
   */
  execNo?: string | null;
  /**
   * 开始时间
   */
  startTime?: number | null;
  /**
   * 结束时间
   */
  endTime?: number | null;
  /**
   * 关单时间段：开始
   */
  finishStartTime?: number | null;
  /**
   * 关单时间段：结束
   */
  finishEndTime?: number | null;
  /**
   * 创建人id
   */
  creatorId?: number | null;
  /**
   * 状态列表
   */
  taskStatusList?: string[] | null;
  /**
   * 演练单号列表
   */
  taskNoList?: string[] | null;
  /**
   * 任务id
   */
  scheduleId?: number | null;
  /**
   * 创建时间
   */
  gmtCreate?: string | null;
  /**
   * 专业
   */
  excMajor?: string | null;
  /**
   * 楼栋
   */
  blockGuid?: string | null;
  /**
   * 专业列表
   */
  excLevelList?: string[] | null;
  /**
   * 排序字段：关单时间：closeTime
   */
  sortByField?: string | null;
  /**
   * ASC：升序 /DESC :降序
   */
  sortInfo?: string | null;
  /**
   * 备用处理人
   */
  assigneeList?: Assignee[] | null;
};

export type ApiResponse = Blob;
