/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-drill-tickets';
import type { ApiArgs, ApiResponse } from './export-drill-tickets.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function exportDrillTickets(args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> {
  return executor(args);
}
