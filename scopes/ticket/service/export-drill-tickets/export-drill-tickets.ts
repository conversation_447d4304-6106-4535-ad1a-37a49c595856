/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './export-drill-tickets.type';

const endpoint = '/taskcenter/drill/order/list/export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/349/interface/api/26694)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> => {
    return await request.tryPost<ApiResponse, ApiArgs>(endpoint, args, {
      responseType: 'blob',
    });
  };
}
