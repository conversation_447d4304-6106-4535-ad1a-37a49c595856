/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportDrillTickets as webService } from './export-drill-tickets.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ title: 'success' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ title: 'error' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
