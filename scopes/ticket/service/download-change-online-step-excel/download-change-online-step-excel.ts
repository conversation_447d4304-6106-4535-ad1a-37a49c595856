/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-12-16
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './download-change-online-step-excel.type';

const endpoint = '/dcom/change/online/order/step/excel/download';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/84/interface/api/29691)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
