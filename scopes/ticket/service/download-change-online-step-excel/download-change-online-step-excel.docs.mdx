---
description: 'A downloadChangeOnlineStepExcel HTTP API service.'
labels: ['service', 'http']
---

步骤模板下载

## Usage

### Browser

```ts
import { downloadChangeOnlineStepExcel } from '@manyun/ticket.service.download-change-online-step-excel';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { DownloadChangeOnlineStepExcelService } from '@manyun/ticket.service.download-change-online-step-excel/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = DownloadChangeOnlineStepExcelService.from(nodeRequest);
```
