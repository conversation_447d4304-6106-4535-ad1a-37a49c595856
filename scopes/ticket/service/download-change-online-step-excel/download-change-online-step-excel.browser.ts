/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-12-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-change-online-step-excel';
import type { SvcRespData } from './download-change-online-step-excel.type';

const executor = getExecutor(webRequest);

export function downloadChangeOnlineStepExcel(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
