/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { PlanProperties } from '@manyun/ticket.service.create-emergency-drill';

export type SvcRespData = {
  data: PlanProperties[];
  total: number;
};

export type RequestRespData = {
  data: PlanProperties[] | null;
  total: number;
} | null;

// ApiQ与SvcQuery相同
export type ApiQ = {
  taskNo: string;
};

export type ApiR = ListResponse<PlanProperties[] | null>;
