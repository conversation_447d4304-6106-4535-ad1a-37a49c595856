/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-emergency-drill-item-list';
import type { ApiQ, SvcRespData } from './fetch-emergency-drill-item-list.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchEmergencyDrillItemList(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
