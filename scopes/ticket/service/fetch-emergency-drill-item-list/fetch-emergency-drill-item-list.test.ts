/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchEmergencyDrillItemList as webService } from './fetch-emergency-drill-item-list.browser';
import { fetchEmergencyDrillItemList as nodeService } from './fetch-emergency-drill-item-list.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ taskNo: 'SUCCESS_ID' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ taskNo: 'FAIL_ID' });
  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ taskNo: 'SUCCESS_ID' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ taskNo: 'FAIL_ID' });

  expect(typeof error).toBe('object');
});
