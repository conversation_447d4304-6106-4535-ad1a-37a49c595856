/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-22
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChangeStatus =
  | 'DRAFT'
  | 'APPROVING'
  | 'WAITING_CHANGE'
  | 'CHANGING'
  | 'IN_SUMMARY'
  | 'WAITING_CLOSE'
  | 'FINISH';
export type ChangeJSON = {
  id: number;
  gmtCreate: number;
  gmtModified: number;
  changeOrderId: string;
  changeStatus: ChangeStatus;
  exeUserGroupCode: string;
  exeUserGroupName: string;
  title: string;
  idcTag: string;
  blockTag: string;
  urgency: string | null;
  changeType: string;
  changeTypeName: string;
  reason: string;
  planStartTime: number;
  planEndTime: number;
  riskLevel: string;
  creatorId: number;
  creatorName: string;
  templateId: string | null;
  templateName: string | null;
  operatorId: number | null;
  operatorName: string | null;
  summery: string;
  summarizePersonId: number;
  summarizePersonName: string;
  realStartTime: number;
  realEndTime: number;
  changeOrderExtJson: ChangeOrderExtJson;
  stepList: StepList[];
  workFlowId: string | null;
  hasPermission: boolean;
  stopReason: string;
  reasonType: string | null;
  exeWay: ExeWay;
  exeResult: 'SUCCESS' | 'FAILED';
  fileInfoList: BackendMcUploadFile[];
  customerFileInfoList?: BackendMcUploadFile[];
  changeInfluence?: string;
  purpose?: string; // 变更目的
  sourceType?: string; // 来源类型
  sourceNo?: string; // 来源单号
  sourceChangeOrderId?: string; // 来源变更单号
  changeDeviceList?: ChangeDeviceIfdo[];
  // 变更时间列表
  changeTimeList?: ChangeTimeInfo[];
  respPersonId?: number | null;
  respPersonName?: string | null;
  changeVersion?: number | null;
  relateCustomer?: string | null;
  influenceArea?: string | null;
  reasonName?: string | null;
};

export type ChangeDeviceIfdo = {
  deviceGuid: string;
  deviceName: string;
  roomTag: string;
  idcTag: string;
  blockTag: string;
  inhibition: boolean;
  deviceType: string;
};

export type ChangeTimeInfo = {
  startDate: string;
  endDate: string;
  startHour: string;
  endHour: string;
};

export type StepList = {
  stepOrder: number;
  stepName: string;
  stepDesc: string;
  operatorId?: number;
  operatorName?: string;
  ola?: string;
  stepType?: string;
  opType?: string;
  operate?: string;
  opObjectName?: string;
  opObjectCode?: string;
  checkMethod?: string;
  pointCode?: string;
  pointName?: string;
  expectedValue?: string;
  pointValueText?: string;
  identifyWay?: string;
  exeWay?: string;
  matchObjectInfoList?: MatchObjectInfoList[];
  checkDeviceInfoList?: string[];
};
export type ChangeOrderExtJson = {
  modifyPersonId: string;
  modifyPersonName: string;
  reasonType: string;
  changeTimeList: ChangeTimeInfo[];
};
export type ExeWay = 'ON_LINE' | 'OFF_LINE';

export type SvcRespData = ChangeJSON | null;

export type RequestRespData = ChangeJSON | null;

export type MatchObjectInfoList = {
  name: string;
  code: string;
};

export type ApiQ = {
  changeOrderId: string;
};

export type ApiR = ListResponse<ChangeJSON[] | null>;
