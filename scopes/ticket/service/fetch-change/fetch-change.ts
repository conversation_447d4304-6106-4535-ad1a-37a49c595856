/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-change.type';

const endpoint = '/dcom/change/order/detail';

/**
 * @see [Doc]( http://172.16.0.17:13000/project/84/interface/api/19332)
 *
 * @param svcQuery
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data,
      ...rest,
    };
  };
}
