/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './event-audit';
import type { ApiQ, SvcRespData } from './event-audit.type';

const executor = getExecutor(webRequest);

/**
 * @param  svcQuery
 * @returns
 */
export function eventAudit(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
