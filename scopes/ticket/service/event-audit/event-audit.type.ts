/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

// SvcQuery 与 ApiQ相同
export type ApiQ = {
  eventId: number;
  auditDesc?: string;
  files?: BackendMcUploadFile[];
};

export type ApiR = WriteResponse;
