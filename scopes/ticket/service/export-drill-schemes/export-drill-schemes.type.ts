/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */

export type ApiArgs = {
  /**
   * 楼栋
   */
  blockGuidList?: string[] | null;
  /**
   * ALL/BLOCK
   */
  effectType: string;
  /**
   * 专业
   */
  excMajor?: string | null;
  /**
   * 等级
   */
  excLevelList?: string[] | null;
  /**
   * 创建人id
   */
  creatorId?: number | null;
  /**
   * 开始时间
   */
  startDate?: number | null;
  /**
   * 结束时间
   */
  endDate?: number | null;
  /**
   * 演练名称
   */
  excName?: string | null;
};

export type ApiResponse = Blob;
