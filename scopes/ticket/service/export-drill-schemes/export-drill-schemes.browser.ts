/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-drill-schemes';
import type { ApiArgs, ApiResponse } from './export-drill-schemes.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function exportDrillSchemes(args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> {
  return executor(args);
}
