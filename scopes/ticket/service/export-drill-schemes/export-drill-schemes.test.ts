/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportDrillSchemes as webService } from './export-drill-schemes.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ blockGuidList: ['1'], effectType: '1' });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await webService({ blockGuidList: ['0'], effectType: '0' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
