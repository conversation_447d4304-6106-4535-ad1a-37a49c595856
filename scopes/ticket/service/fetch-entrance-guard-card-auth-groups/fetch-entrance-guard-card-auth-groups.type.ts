/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-23
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendAuthInfo = {
  id: number;
  authId: number;
  blockGuid: string;
  authName: string;
  gmtCreate: number;
  gmtModified: number;
};

export type SvcRespData = {
  data: BackendAuthInfo[];
  total: number;
};

export type RequestRespData = {
  data: BackendAuthInfo[] | null;
  total: number;
} | null;

export type ApiQ = {
  blockGuid: string;
};

export type ApiR = ListResponse<BackendAuthInfo[] | null>;
