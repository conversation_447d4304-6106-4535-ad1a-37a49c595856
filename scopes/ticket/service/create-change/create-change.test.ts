/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createChange as webService } from './create-change.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    changeOrderId: '1',
    title: '1',
    idcTag: '1',
    blockTag: '1',
    changeType: '1',
    planStartTime: '1',
    planEndTime: '1',
    riskLevel: '1',
    exeWay: '1',
    changeInfluence: '1',
    reason: '1',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    changeOrderId: '2',
    title: '1',
    idcTag: '1',
    blockTag: '1',
    changeType: '1',
    planStartTime: '1',
    planEndTime: '1',
    riskLevel: '1',
    exeWay: '1',
    changeInfluence: '1',
    reason: '1',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
