/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-19
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type CreateChange = {
  changeOrderId?: string;
  exeUserGroupCode?: string;
  title: string;
  idcTag: string;
  blockTag: string;
  urgency?: string;
  changeType: string;
  reason: string;
  planStartTime: number;
  planEndTime: number;
  templateId?: string;
  templateName?: string;
  riskLevel: string;
  exeWay: string;
  stepList?: StepList[];
  fileInfoList?: BackendMcUploadFile[];
  changeInfluence: string;
  customerFileInfoList?: BackendMcUploadFile[];
  purpose?: string; // 变更目的
  sourceType?: string; // 来源类型
  sourceNo?: string; // 来源单号
  sourceChangeOrderId?: string; // 来源变更单号
  changeDeviceList?: ChangeDeviceIfdo[];
  // 变更时间列表
  changeTimeList?: ChangeTimeInfo[];
};

export type ChangeDeviceIfdo = {
  deviceGuid: string;
  deviceName: string;
  roomTag?: string | null;
  idcTag?: string;
  blockTag?: string;
  inhibition: boolean;
  deviceType: string;
};

export type ChangeTimeInfo = {
  startDate: string;
  endDate: string;
  startHour: string;
  endHour: string;
};

export type StepList = {
  stepOrder: number;
  stepName: string;
  stepDesc: string;
  operatorId?: number;
  operatorName?: string;
  ola?: string;
  stepType?: string;
  opType?: string;
  operate?: string;
  opObjectName?: string;
  opObjectCode?: string;
  checkMethod?: string;
  pointCode?: string;
  pointName?: string;
  expectedValue?: string;
  pointValueText?: string;
  identifyWay?: string;
  exeWay?: string;
  matchObjectInfoList?: MatchObjectInfoList[];
  checkDeviceInfoList?: string[];
};

export type MatchObjectInfoList = {
  name: string;
  code: string;
};
export type SvcRespData = { changeOrderId: string; workflowId: string | null };

export type RequestRespData = { changeOrderId: string; workflowId: string | null };
export type ApiQ = CreateChange;

export type ApiR = WriteResponse;
