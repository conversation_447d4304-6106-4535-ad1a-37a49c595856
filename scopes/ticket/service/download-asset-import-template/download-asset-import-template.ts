/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2023-9-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

const endpoint = '/taskcenter/access/device/download';

/**
 * @see [Doc](http://172.16.0.17:13000/project/142/interface/api/25260)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<Blob>> => {
    const { error, data, ...rest } = await request.tryGet<Blob>(endpoint, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
