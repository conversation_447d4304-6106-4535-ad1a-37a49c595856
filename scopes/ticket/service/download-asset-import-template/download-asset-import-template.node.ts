/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2023-9-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-asset-import-template';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function downloadAssetImportTemplate(): Promise<EnhancedAxiosResponse<Blob>> {
  return executor();
}
