/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './close-risk-register.type';

const endpoint = '/dcom/risk/register/close';

/**
 * @see [Doc](http://172.16.0.17:13000/project/309/interface/api/24690)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );
    return { error, data, ...rest };
  };
}
