/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { RiskClearStatus } from '@manyun/ticket.model.risk-register';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  riskId: string;
  riskClearStatus?: RiskClearStatus;
  needApproval?: boolean;
  riskClearReason?: string;
  fileInfoList?: BackendMcUploadFile[];
};

export type ApiR = WriteResponse;
