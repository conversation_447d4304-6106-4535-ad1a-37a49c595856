/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './close-risk-register';
import type { ApiQ, SvcRespData } from './close-risk-register.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function closeRiskRegister(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
