/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { closeRiskRegister as webService } from './close-risk-register.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    riskId: '1',
    riskClearReason: '1',
    riskClearStatus: 'CLEARED',
    needApproval: true,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    riskId: '2',
    riskClearReason: '1',
    riskClearStatus: 'CLEARED',
    needApproval: true,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
