/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { ProcessEngineConfig } from '@manyun/ticket.model.event';

export type SvcQuery = {
  skippedRelieveEventLevels?: string[];
  skippedAuditEventLevels?: string[];
  processConfig?: ProcessEngineConfig;
};

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  eventRelieveSkip?: string[];
  eventAuditSkip?: string[];
  processConfig?: ProcessEngineConfig;
};

export type ApiR = WriteResponse;
