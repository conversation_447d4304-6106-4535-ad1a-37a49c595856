/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { configurationEvents as webService } from './configuration-events.browser';
import { configurationEvents as nodeService } from './configuration-events.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ skippedAuditEventLevels: [] });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ skippedAuditEventLevels: [] });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ skippedAuditEventLevels: [] });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ skippedAuditEventLevels: [] });

  expect(typeof error).toBe('object');
});
