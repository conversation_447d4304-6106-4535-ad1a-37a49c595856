/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './configuration-events';
import type { SvcQuery, SvcRespData } from './configuration-events.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function configurationEvents(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
