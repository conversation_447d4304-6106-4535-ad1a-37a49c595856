/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './configuration-events.type';

const endpoint = '/dcom/event/configuration';

/**
 * @see [Doc](http://***********:13000/project/146/interface/api/22836)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      eventAuditSkip: svcQuery.skippedAuditEventLevels,
      eventRelieveSkip: svcQuery.skippedRelieveEventLevels,
      processConfig: svcQuery.processConfig,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData | null, ApiQ>(
      endpoint,
      params
    );

    return { error, data, ...rest };
  };
}
