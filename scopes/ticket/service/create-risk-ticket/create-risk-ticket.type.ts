/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-17
 *
 * @packageDocumentation
 */
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { FaultTargetType } from '@manyun/ticket.model.event';
import { BackendRiskLevel, BackendRiskType } from '@manyun/ticket.model.ticket';

export type SvcRespData = string;

export type RequestRespData = string | null;

// ApiQ 和 SvcQuery 相同
export type ApiQ = {
  taskTitle: string;
  /** 工单子类型 */
  riskSubType: BackendRiskType;
  idcTag: string;
  blockGuid: string;
  /** 风险等级 */
  riskLevel: BackendRiskLevel;
  /** 风险对象类型 */
  riskObjType: FaultTargetType;
  /** 风险对象id */
  riskObjGuids: string[];
  fileInfoList?: MixedUploadFile[];
};

export type ApiR = WriteResponse;
