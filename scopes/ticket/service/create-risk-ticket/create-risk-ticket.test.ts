/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import { FaultTargetType } from '@manyun/ticket.model.event';
import { BackendRiskLevel, BackendRiskType } from '@manyun/ticket.model.ticket';

import { createRiskTicket as webService } from './create-risk-ticket.browser';
import { createRiskTicket as nodeService } from './create-risk-ticket.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    taskTitle: 'SUCCESS_ID',
    riskSubType: BackendRiskType.BUILDING,
    idcTag: 'test',
    blockGuid: 'test',
    riskLevel: BackendRiskLevel.HIGH,
    riskObjType: FaultTargetType.Device,
    riskObjGuids: ['test'],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    taskTitle: 'FAIL_ID',
    riskSubType: BackendRiskType.BUILDING,
    idcTag: 'test',
    blockGuid: 'test',
    riskLevel: BackendRiskLevel.HIGH,
    riskObjType: FaultTargetType.Device,

    riskObjGuids: ['test'],
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    taskTitle: 'SUCCESS_ID',
    riskSubType: BackendRiskType.BUILDING,
    idcTag: 'test',
    blockGuid: 'test',
    riskLevel: BackendRiskLevel.HIGH,
    riskObjType: FaultTargetType.Device,
    riskObjGuids: ['test'],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    taskTitle: 'FAIL_ID',
    riskSubType: BackendRiskType.BUILDING,
    idcTag: 'test',
    blockGuid: 'test',
    riskLevel: BackendRiskLevel.HIGH,
    riskObjType: FaultTargetType.Device,
    riskObjGuids: ['test'],
  });

  expect(typeof error).toBe('object');
});
