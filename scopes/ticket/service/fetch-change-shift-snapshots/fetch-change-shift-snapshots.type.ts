/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Snapshot = {
  id: number | string;
  title: string | null;
  bizType: string;
  handlerId: number;
  bizStatus: string;
  gmtCreate: number | string;
  gmtModified: number | string;
  bizNo: string;
  handerInfoList: number[];
  handlerIds?: number[];
};

export type SvcRespData = {
  data: Snapshot[];
  total: number;
};

export type RequestRespData = {
  data: Snapshot[] | null;
  total: number;
} | null;

export type ApiQ = {
  bizNo: string;
  bizType: BizType;
};

export enum BizType {
  Event = 'EVENT',
  Change = 'CHANGE',
  Repair = 'REPAIR',
  Maintenance = 'MAINTENANCE',
  Power = 'POWER',
  RiskRegister = 'RISK_REGISTER', // 风险
  RiskCheck = 'RISK_CHECK', // 风险检查单
  RiskRegisterNew = 'RISK_REGISTER_NEW', // 风险登记册
}

export type ApiR = ListResponse<Snapshot[] | null>;
