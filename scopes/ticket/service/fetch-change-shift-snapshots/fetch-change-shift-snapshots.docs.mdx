---
description: 'A fetchChangeShiftSnapshots HTTP API service.'
labels: ['service', 'http', change-shift-snapshots]
---

## 概述

交接班快照查询

## 使用

### Browser

```ts
import { fetchChangeShiftSnapshots } from '@ticket/service.fetch-change-shift-snapshots';

const { error, data } = await fetchChangeShiftSnapshots({ bizNo: '1', bizType: 'EVENT' });
const { error, data } = await fetchChangeShiftSnapshots({ bizNo: '2', bizType: 'EVENT' });
```

### Node

```ts
import { fetchChangeShiftSnapshots } from '@ticket/service.fetch-change-shift-snapshots/dist/index.node';

const { data } = await fetchChangeShiftSnapshots({ bizNo: '1', bizType: 'EVENT' });

try {
  const { data } = await fetchChangeShiftSnapshots({ bizNo: '2', bizType: 'EVENT' });
} catch (error) {
  // ...
}
```
