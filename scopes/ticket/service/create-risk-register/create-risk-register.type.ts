/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { RiskObject } from '@manyun/ticket.gql.client.risk-register';
import type { RiskRegisterJSON, RiskRegisterToApi } from '@manyun/ticket.model.risk-register';

export type SvcQuery = Omit<RiskRegisterJSON, 'riskObjects'> & {
  formId: string;
  commit: boolean;
  riskObjects?: RiskObject[] | null | string;
};

export type SvcRespData = RequestRespData;

export type RequestRespData = { riskId: string };

export type ApiQ = RiskRegisterToApi & {
  commit: boolean;
};

export type ApiR = Response<RequestRespData>;
