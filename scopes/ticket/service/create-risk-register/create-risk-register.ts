/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';
import { RiskRegister } from '@manyun/ticket.model.risk-register';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-risk-register.type';

const endpoint = '/dcom/risk/register/create';

/**
 * @see [Doc](http://172.16.0.17:13000/project/309/interface/api/24582)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (updateQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const backendSaleQuote = {
      ...RiskRegister.fromJSON({
        ...updateQ,
        riskObjects:
          typeof updateQ.riskObjects === 'string'
            ? [{ label: updateQ.riskObjects, objectName: updateQ.riskObjects }]
            : updateQ.riskObjects,
      }).toApiObject(),
      formId: updateQ.formId,
      immutableEventIdList: updateQ.immutableEventIdList,
    };
    const params: ApiQ = { ...backendSaleQuote, commit: updateQ.commit };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
