/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createRiskRegister as webService } from './create-risk-register.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    blockGuid: 'EC01.A',
    commit: true,
    fileInfoList: [],
    id: 'RIS20230822000037',
    relatePerson: { label: '1', value: '1', name: '1', type: 'internal' },
    riskDesc: 'wwwww',
    riskInfluenceDesc: 'jnkjnkjnmn3',
    riskLevel: 'HIGH',
    riskObjectType: 'DEVICE',
    riskObjects: [],
    riskOwner: 14107,
    riskPriorityCode: '3',
    riskResourceCode: '2',
    riskTypeCode: '1',
    createdAt: 11,
    modifiedAt: 11,
    idcTag: '1',
    riskStatus: '',
    riskClearStatus: null,
    createUser: 1,
    riskIdentifyTime: null,
    riskEvaluateTime: null,
    riskHandleTime: null,
    riskAuditTime: null,
    riskCloseTime: null,
    evaluateInstId: null,
    auditInstId: null,
    formId: '131231',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    blockGuid: 'EC01.B',
    commit: true,
    fileInfoList: [],
    id: 'RIS20230822000037',
    relatePerson: { label: '1', value: '1', name: '1', type: 'internal' },
    riskDesc: 'wwwww',
    riskInfluenceDesc: 'jnkjnkjnmn3',
    riskLevel: 'HIGH',
    riskObjectType: 'DEVICE',
    riskObjects: [],
    riskOwner: 14107,
    riskPriorityCode: '3',
    riskResourceCode: '2',
    riskTypeCode: '1',
    createdAt: 11,
    modifiedAt: 11,
    idcTag: '1',
    riskStatus: '',
    riskClearStatus: null,
    createUser: 1,
    riskIdentifyTime: null,
    riskEvaluateTime: null,
    riskHandleTime: null,
    riskAuditTime: null,
    riskCloseTime: null,
    evaluateInstId: null,
    auditInstId: null,
    formId: '131231',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
