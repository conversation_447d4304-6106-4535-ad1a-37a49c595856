/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcRespData,
} from './fetch-change-shift-take-over-group-user.type';

const endpoint = '/taskcenter/handover/groupUser';

/**
 * @see [Doc](YAPI Link)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
        },
      };
    }

    return { error, data: { data: data.data }, ...rest };
  };
}
