---
description: 'A fetchChangeShiftTakeOverGroupUser HTTP API service.'
labels: ['service', 'http', change-shift-take-over-group-user]
---

## 概述

根据业务单号及用户组 id 查询对应用户

## 使用

### Browser

````ts
import { fetchChangeShiftTakeOverGroupUser } from '@ticket/service.fetch-change-shift-take-over-group-user';

const { error, data } = await fetchChangeShiftTakeOverGroupUser({ bizNo: '1', dutyGroupId: 1 });
const { error, data } = await fetchChangeShiftTakeOverGroupUser({ bizNo: '2', dutyGroupId: 2 });

### Node

```ts
import { fetchChangeShiftTakeOverGroupUser } from '@ticket/service.fetch-change-shift-take-over-group-user/dist/index.node';

const { data } = await fetchChangeShiftTakeOverGroupUser({ bizNo: '1', dutyGroupId: 1 });

try {
  const { data } = await fetchChangeShiftTakeOverGroupUser({ bizNo: '2', dutyGroupId: 2 });
} catch (error) {
  // ...
}
````
