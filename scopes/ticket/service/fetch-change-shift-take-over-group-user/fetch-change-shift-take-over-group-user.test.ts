/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchChangeShiftTakeOverGroupUser as webService } from './fetch-change-shift-take-over-group-user.browser';
import { fetchChangeShiftTakeOverGroupUser as nodeService } from './fetch-change-shift-take-over-group-user.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ bizNo: '1', dutyGroupId: 1 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ bizNo: '2', dutyGroupId: 2 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ bizNo: '1', dutyGroupId: 1 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ bizNo: '2', dutyGroupId: 2 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
});
