/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = {
  data: { id: number; userName: string }[];
};

export type RequestRespData = {
  data: { id: number; userName: string }[] | null;
} | null;

export type ApiQ = {
  dutyGroupId: number;
  bizNo: string;
};

export type ApiR = ListResponse<{ id: number; userName: string }[] | null>;
