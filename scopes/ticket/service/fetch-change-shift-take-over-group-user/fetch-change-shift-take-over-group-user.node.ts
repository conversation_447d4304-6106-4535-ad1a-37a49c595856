/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-25
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-shift-take-over-group-user';
import type { ApiQ, SvcRespData } from './fetch-change-shift-take-over-group-user.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchChangeShiftTakeOverGroupUser(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
