/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './export-risk-pool.type';

const endpoint = '/taskcenter/riskpoint/list/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/317/interface/api/25399)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return await request.tryPost<RequestRespData, SvcQuery>(endpoint, svcQuery, {
      responseType: 'blob',
    });
  };
}
