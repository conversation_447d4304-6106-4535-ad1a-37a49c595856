/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  pageSize?: number | null;
  pageNum?: number | null;
  /**
   * 风险类别
   */
  riskCategoryList?: string[] | null;
  /**
   * 类型
   */
  riskTypeList?: string[] | null;
  /**
   * 风险等级
   */
  riskLevelList?: string[] | null;
  /**
   * 1:启用 0:禁用
   */
  isEnable?: string | null;
  /**
   * 开始时间
   */
  startDate?: number | null;
  /**
   * 结束时间
   */
  endDate?: number | null;
  /**
   * 修改开始时间
   */
  startDateOfUpdate?: number | null;
  /**
   * 修改结束时间
   */
  endDateOfUpdate?: number | null;
  /**
   * 是否自定义排序
   */
  defineOrder?: boolean | null;
  /**
   * 自定义排序字段：gmtModified/gmtCreate
   */
  orderFiled?: string | null;
  /**
   * 排序 ： 升序：ASC /降序：DESC
   */
  orderInfo?: string | null;
  /**
   * 创建人id
   */
  creatorId?: number | null;
  /**
   * 风险描述
   */
  riskDesc?: string | null;
};

export type SvcRespData = Blob;

export type RequestRespData = Blob;

export type ApiR = Response<Blob>;
