/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-risk-pool';
import type { SvcQuery, SvcRespData } from './export-risk-pool.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function exportRiskPool(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
