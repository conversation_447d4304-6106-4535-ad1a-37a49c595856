/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportRiskPool as webService } from './export-risk-pool.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve error response', async () => {
  const { error } = await webService({});

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
