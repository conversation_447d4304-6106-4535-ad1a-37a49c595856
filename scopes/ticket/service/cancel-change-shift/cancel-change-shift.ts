/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './cancel-change-shift.type';

const endpoint = '/taskcenter/handover/cancle';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/154/interface/api/18272 )
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
