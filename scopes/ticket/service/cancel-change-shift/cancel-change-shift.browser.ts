/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancel-change-shift';
import type { ApiQ, SvcRespData } from './cancel-change-shift.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function cancelChangeShift(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
