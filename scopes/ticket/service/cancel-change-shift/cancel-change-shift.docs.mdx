---
description: 'A cancelChangeShift HTTP API service.'
labels: ['service', 'http', change-shift-cancel]
---

## 概述

取消交接班

## 使用

### Browser

```ts
import { cancelChangeShift } from '@ticket/service.cancel-change-shift';

const { error, data } = await cancelChangeShift({ bizNo: 1 });
const { error, data } = await cancelChangeShift({ bizNo: 2 });
```

### Node

```ts
import { cancelChangeShift } from '@ticket/service.cancel-change-shift-cancel/dist/index.node';

const { data } = await cancelChangeShift({ bizNo: 1 });

try {
  const { data } = await cancelChangeShift({ bizNo: 2 });
} catch (error) {
  // ...
}
```
