---
description: 'A fetchChangeCountByIdc HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询机房下所有未结束的变更（状态非“结束”&“草稿”）

## 使用

### Browser

```ts
import { fetchChangeCountByIdc } from '@manyun/ticket.service.fetch-change-count-by-idc';
import { ChangeTicketState } from '@manyun/ticket.model.change';

const { error, data } = await fetchChangeCountByIdc({
  idcTag: 'EC01',
  changeStatusList: [ChangeTicketState.Approving],
});
const { error, data } = await fetchChangeCountByIdc({
  idcTag: 'EC02',
  changeStatusList: [ChangeTicketState.Approving],
});
```

### Node

```ts
import { fetchChangeCountByIdc } from '@manyun/ticket.service.fetch-change-count-by-idc/dist/index.node';
import { ChangeTicketState } from '@manyun/ticket.model.change';

const { data } = await fetchChangeCountByIdc({{ idcTag: 'EC01', changeStatusList: [ChangeTicketState.Approving] }});
const { error, data } = await fetchChangeCountByIdc({ idcTag: 'EC02', changeStatusList: [ChangeTicketState.Approving] });
```
