/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { ChangeTicketState } from '@manyun/ticket.model.change';

export type SvcQuery = {
  idcTag: string;
  changeStatusList: ChangeTicketState[];
};

export type Change = {
  blockGuid: string;
  count: number;
};

export type SvcRespData = {
  data: Change[];
  total: number;
};

export type RequestRespData = {
  data: Change[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<Change[] | null>;
