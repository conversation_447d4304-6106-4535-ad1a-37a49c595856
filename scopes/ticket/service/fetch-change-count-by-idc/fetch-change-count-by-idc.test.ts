/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';
import { ChangeTicketState } from '@manyun/ticket.model.change';

import { fetchChangeCountByIdc as webService } from './fetch-change-count-by-idc.browser';
import { fetchChangeCountByIdc as nodeService } from './fetch-change-count-by-idc.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    idcTag: 'EC01',
    changeStatusList: [ChangeTicketState.Approving],
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    idcTag: 'EC02',
    changeStatusList: [ChangeTicketState.Approving],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    idcTag: 'EC01',
    changeStatusList: [ChangeTicketState.Approving],
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    idcTag: 'EC02',
    changeStatusList: [ChangeTicketState.Approving],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
