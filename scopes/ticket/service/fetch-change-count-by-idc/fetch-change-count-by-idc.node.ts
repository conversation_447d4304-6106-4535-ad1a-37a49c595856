/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-count-by-idc';
import type { SvcQuery, SvcRespData } from './fetch-change-count-by-idc.type';

const executor = getExecutor(nodeRequest);

/**
 * @param params
 * @returns
 */
export function fetchChangeCountByIdc(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
