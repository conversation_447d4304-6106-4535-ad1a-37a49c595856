/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-risk-operation-record';
import type { ApiQ, SvcRespData } from './delete-risk-operation-record.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function deleteRiskOperationRecord(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
