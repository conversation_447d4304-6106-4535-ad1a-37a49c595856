/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = 'success' | 'error';

export type SvcRespData = {
  data: string[];
  total: number;
};

export type RequestRespData = {
  data: string[] | null;
  total: number;
} | null;

export type ApiQ = {
  changeId: string;
};

export type ApiR = ListResponse<string[] | null>;
