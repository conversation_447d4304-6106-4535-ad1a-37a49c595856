---
description: 'A downloadEventProcessingRecordTemplate HTTP API service.'
labels: ['service', 'http']
---

处理记录导入模版下载

## Usage

### Browser

```ts
import { downloadEventProcessingRecordTemplate } from '@manyun/ticket.service.download-event-processing-record-template';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { DownloadEventProcessingRecordTemplateService } from '@manyun/ticket.service.download-event-processing-record-template/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = DownloadEventProcessingRecordTemplateService.from(nodeRequest);
```
