/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  RequestRespData,
  SvcRespData,
} from './download-event-processing-record-template.type.js';

const endpoint = '/dcom/event/opt/record/model/download';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/146/interface/api/30516)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
