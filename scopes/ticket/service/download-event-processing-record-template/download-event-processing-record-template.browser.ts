/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-event-processing-record-template.js';
import type { SvcRespData } from './download-event-processing-record-template.type.js';

const executor = getExecutor(webRequest);

export function downloadEventProcessingRecordTemplate(): Promise<
  EnhancedAxiosResponse<SvcRespData>
> {
  return executor();
}
