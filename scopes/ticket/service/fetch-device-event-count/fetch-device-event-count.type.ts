/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-08-24
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export enum DeviceEventType {
  /** 已创建 */
  'CREATED' = 'CREATED',
  /** 处理中 */
  'PROCESSING' = 'PROCESSING',
  /** 已缓解 */
  'RELIEVED' = 'RELIEVED',
  /** 已解决 */
  'RESOLVED' = 'RESOLVED',
  /** 已评审 */
  'AUDITED' = 'AUDITED',
  /** 已关闭 */
  'CLOSED' = 'CLOSED',
}

export const EVENT_STATUS_TEXT_MAP = {
  [DeviceEventType.CREATED]: '已创建',
  [DeviceEventType.PROCESSING]: '处理中',
  [DeviceEventType.RELIEVED]: '已缓解',
  [DeviceEventType.RESOLVED]: '已解决',
  [DeviceEventType.AUDITED]: '已评审',
  [DeviceEventType.CLOSED]: '已关闭',
};

export const EVENT_STATUS_TEXT_COLOR_MAP = {
  [DeviceEventType.CREATED]: 'warning',
  [DeviceEventType.PROCESSING]: 'processing',
  [DeviceEventType.RELIEVED]: 'processing',
  [DeviceEventType.RESOLVED]: 'processing',
  [DeviceEventType.AUDITED]: 'processing',
  [DeviceEventType.CLOSED]: 'success',
};

type DeviceEventTypeInfo = keyof typeof DeviceEventType;

// @FIXME renaming me
export type fetchDeviceStatisticsEventCount = {
  /**事件状态 */
  eventStatus: DeviceEventTypeInfo;
  /**事件数量 */
  statusCount: number;
};

export type SvcRespData = {
  data: fetchDeviceStatisticsEventCount[];
  total: number;
};

export type RequestRespData = {
  data: fetchDeviceStatisticsEventCount[] | null;
  total: number;
} | null;

export type ApiQ = {
  deviceGuid: string;
};

export type ApiR = ListResponse<fetchDeviceStatisticsEventCount[] | null>;
