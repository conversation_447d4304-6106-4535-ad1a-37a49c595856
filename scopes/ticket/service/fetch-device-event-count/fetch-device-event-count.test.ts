/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-08-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceEventCount as webService } from './fetch-device-event-count.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({ deviceGuid: '1090103' });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090101' });

  expect(error?.code).toBe('error');
  expect(data.data).toStrictEqual([]);
});
