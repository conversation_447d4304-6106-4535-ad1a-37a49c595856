---
description: 'A fetchDeviceEventCount HTTP API service.'
labels: ['service', 'http', fetch-device-event-count]
---

## 概述

运维日志：获取事件数量

## 使用

### Browser

```ts
import { fetchDeviceEventCount } from '@resource-hub/service.fetch-device-event-count';

const { error, data } = await fetchDeviceEventCount({ deviceGuid: '10101' });
```

### Node

```ts
import { fetchDeviceEventCount } from '@resource-hub/service.fetch-device-event-count/dist/index.node';

const { error, data } = await fetchDeviceEventCount({ deviceGuid: '10101' });

try {
  const { error, data } = await fetchDeviceEventCount({ deviceGuid: '' });
} catch (error) {
  // ...
}
```
