/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { downloadAccessCardApplyUserTemplate as webService } from './download-access-card-apply-user-template.browser';
import { downloadAccessCardApplyUserTemplate as nodeService } from './download-access-card-apply-user-template.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService();

  expect(error).toBe(undefined);
});
