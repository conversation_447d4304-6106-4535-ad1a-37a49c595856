/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-access-card-apply-user-template';
import type { SvcRespData } from './download-access-card-apply-user-template.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function downloadAccessCardApplyUserTemplate(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
