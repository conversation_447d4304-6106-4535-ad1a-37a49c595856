/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './download-access-card-apply-user-template.type';

const endpoint = '/taskcenter/general/apply_user/download';

/**
 * @see [Doc]( http://172.16.0.17:13000/project/114/interface/api/20334)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
