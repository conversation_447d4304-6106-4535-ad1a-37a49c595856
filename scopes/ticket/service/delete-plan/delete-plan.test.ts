/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deletePlan as webService } from './delete-plan.browser';
import { deletePlan as nodeService } from './delete-plan.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});
test('[web] should resolve success response', async () => {
  const { error } = await webService({ id: 1 });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ id: 0 });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ id: 1 });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ id: 0 });

  expect(typeof error).toBe('object');
});
