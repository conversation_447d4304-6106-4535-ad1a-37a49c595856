/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-plan';
import type { ApiQ, SvcRespData } from './delete-plan.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deletePlan(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
