/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-6
 *
 * @packageDocumentation
 */
   
import type { Response } from '@manyun/dcbrain-types/lib/backend/common.js';



export type 对应列 = {
  /**
   * 一级分类
   */
  topCategory?: any| null;
  /**
   * 一级分类名称（展示用）
   */
  topCategoryName?: string| null;
  /**
   * 二级分类
   */
  secondCategory?: any| null;
  /**
   * 二级分类名称（展示用）
   */
  secondCategoryName?: string| null;
  /**
   * 三级分类
   */
  deviceType?: string| null;
  /**
   * 三级分类名称（展示用）
   */
  deviceTypeName?: string| null;
  /**
   * 厂商
   */
  vendor?: string| null;
  /**
   * 型号
   */
  productModel?: string| null;
  /**
   * 供应商简称
   */
  supplyVendor?: string| null;
  /**
   * 数量
   */
  warehouseCount?: number| null;
};

export type UnknownType = {
  /**
   * 对应列的错误信息
   */
  productModel?: string| null;
  /**
   * 对应列的错误信息
   */
  vendor?: string| null;
  /**
   * 对应列的错误信息
   */
  topCategoryName?: string| null;
  /**
   * 对应列的错误信息
   */
  secondCategoryName?: string| null;
  /**
   * 对应列的错误信息
   */
  supplyVendor?: string| null;
};

export type UnknownType = {
  rowTag?: number| null;
  /**
   * 对应列
   */
  data?: 对应列| null;
  errMessage?: UnknownType| null;
};

export type ApiResponseData = {
  excelCheckDtos?: UnknownType[]| null;
  checkTotal?: number| null;
  correctTotal?: number| null;
  faultTotal?: number| null;
  correctCheckDtoList?: any| null;
};


export type ApiResponse = Response<ApiResponseData>;
