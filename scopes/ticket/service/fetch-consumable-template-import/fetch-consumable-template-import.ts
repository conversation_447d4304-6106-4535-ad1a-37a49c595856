/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiResponse } from './fetch-consumable-template-import.type.js';

const endpoint = '/taskcenter/warehouse/spareDevice/import';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/104/interface/api/27071)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: FormData): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'], FormData>(endpoint, svcQuery);
  };
}
