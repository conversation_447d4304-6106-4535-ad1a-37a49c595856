/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-consumable-template-import.js';
import type { ApiResponse } from './fetch-consumable-template-import.type.js';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchConsumableTemplateImport(
  svcQuery: FormData
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(svcQuery);
}
