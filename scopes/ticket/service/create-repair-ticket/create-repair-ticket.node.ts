/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-repair-ticket';
import type { ApiQ, SvcRespData } from './create-repair-ticket.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function createRepairTicket(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
