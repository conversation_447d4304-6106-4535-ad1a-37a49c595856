/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createRepairTicket as webService } from './create-repair-ticket.browser';
import { createRepairTicket as nodeService } from './create-repair-ticket.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    relateBizType: 'test',
    relateTaskNo: 'SUCCESS_ID',
    taskSubType: 'test',
    idcTag: 'test',
    blockGuid: 'test',
    targetId: 'test',
    taskTitle: 'test',
    faultDesc: 'test',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    relateBizType: 'test',
    relateTaskNo: 'FAIL_ID',
    taskSubType: 'test',
    idcTag: 'test',
    blockGuid: 'test',
    targetId: 'test',
    taskTitle: 'test',
    faultDesc: 'test',
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    relateBizType: 'test',
    relateTaskNo: 'SUCCESS_ID',
    taskSubType: 'test',
    idcTag: 'test',
    blockGuid: 'test',
    targetId: 'test',
    taskTitle: 'test',
    faultDesc: 'test',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    relateBizType: 'test',
    relateTaskNo: 'FAIL_ID',
    taskSubType: 'test',
    idcTag: 'test',
    blockGuid: 'test',
    targetId: 'test',
    taskTitle: 'test',
    faultDesc: 'test',
  });

  expect(typeof error).toBe('object');
});
