---
description: 'A createRepairTicket HTTP API service.'
labels: ['service', 'http']
---

## 概述

在事件中创建维修工单

## 使用

### Browser

```ts
import { createRepairTicket } from '@manyun/ticket.service.create-repair-ticket';

const { error, data } = await createRepairTicket({
  relateBizType: 'test',
  relateTaskNo: 'test',
  taskSubType: 'test',
  idcTag: 'test',
  blockGuid: 'test',
  targetId: 'test',
  taskTitle: 'test',
  faultDesc: 'test',
});
```

### Node

```ts
import { createRepairTicket } from '@manyun/ticket.service.create-repair-ticket/dist/index.node';

const { data } = await createRepairTicket({
  relateBizType: 'test',
  relateTaskNo: 'test',
  taskSubType: 'test',
  idcTag: 'test',
  blockGuid: 'test',
  targetId: 'test',
  taskTitle: 'test',
  faultDesc: 'test',
});
```
