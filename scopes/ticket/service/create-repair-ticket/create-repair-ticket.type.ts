/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = string | null;

export type RequestRespData = string | null;

// ApiQ与SvcQuery 相同
export type ApiQ = {
  /** 关联类型	 */
  relateBizType: string;
  /** 关联单号		 */
  relateTaskNo: string;
  /** 目标类型		 */
  taskSubType: string;
  idcTag: string;
  blockGuid: string;
  targetId: string;
  /** 工单标题		 */
  taskTitle: string;
  /** 故障描述		 */
  faultDesc: string;
  /** 指派人Id数组 */
  assigneeIdList: number[];
  fileInfoList?: MixedUploadFile[];
};

export type ApiR = WriteResponse;
