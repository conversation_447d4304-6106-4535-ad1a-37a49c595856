/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChangeTemplateJson = {
  changeType: string;
  changeTypeName: string;
  creatorId: number;
  creatorName: string;
  executeRoleCode: string;
  gmtModified: number;
  hasPermission: boolean;
  modifyPersonId: number;
  modifyPersonName: string;
  riskLevel: string;
  templateId: string;
  templateName: string;
  templateStatus: string;
  workFlowId: string | null;
};

export type SvcRespData = {
  data: ChangeTemplateJson[];
  total: number;
};

export type RequestRespData = {
  data: ChangeTemplateJson[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  templateId?: string;
};

export type ApiR = ListResponse<ChangeTemplateJson[] | null>;
