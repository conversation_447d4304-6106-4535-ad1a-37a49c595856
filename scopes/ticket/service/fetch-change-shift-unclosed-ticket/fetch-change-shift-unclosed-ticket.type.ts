/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-3
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChangeShiftsUnclosedTicket = {
  id: number;
  title: string;
  bizType: string;
  handlerId: number | null;
  bizNo: string;
};

export type PagedChangeShiftsUnclosedTicket = {
  data: ChangeShiftsUnclosedTicket[];
  total: number;
};

export type ApiQ = {
  handoverId: string;
  bizType: string;
};

export type ApiR = ListResponse<ChangeShiftsUnclosedTicket[] | null>;
