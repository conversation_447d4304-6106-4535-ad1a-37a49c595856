/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-3
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-change-shift-unclosed-ticket';
import type {
  ApiQ,
  PagedChangeShiftsUnclosedTicket,
} from './fetch-change-shift-unclosed-ticket.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchChangeShiftUnclosedTicket(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<PagedChangeShiftsUnclosedTicket>> {
  return executor(svcQuery);
}
