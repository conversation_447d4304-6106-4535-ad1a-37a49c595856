/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-3
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  PagedChangeShiftsUnclosedTicket,
} from './fetch-change-shift-unclosed-ticket.type';

const endpoint = '/taskcenter/handover/issues';

/**
 * @see [Doc](http://172.16.0.17:13000/project/154/interface/api/20748)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: ApiQ
  ): Promise<EnhancedAxiosResponse<PagedChangeShiftsUnclosedTicket>> => {
    const { error, data, ...rest } = await request.tryPost<
      PagedChangeShiftsUnclosedTicket | null,
      ApiQ
    >(endpoint, svcQuery);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
