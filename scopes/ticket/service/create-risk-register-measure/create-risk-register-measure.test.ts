/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createRiskRegisterMeasure as webService } from './create-risk-register-measure.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    riskId: '1',
    measureDesc: '1',
    measureType: 'LONG',
    followUserId: 1,
    planCompleteTime: 1,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    riskId: '2',
    measureDesc: '1',
    measureType: 'LONG',
    followUserId: 1,
    planCompleteTime: 1,
  });
  expect(typeof error).toBe('object');
});
