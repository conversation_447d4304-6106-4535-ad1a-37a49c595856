/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './create-risk-register-measure.type';

const endpoint = '/dcom/risk/register/measure/create';

/**
 * @see [Doc](http://172.16.0.17:13000/project/309/interface/api/24630)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );
    return { error, data, ...rest };
  };
}
