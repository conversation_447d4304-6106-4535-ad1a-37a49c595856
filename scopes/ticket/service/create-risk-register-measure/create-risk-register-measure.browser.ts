/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-risk-register-measure';
import type { ApiQ, SvcRespData } from './create-risk-register-measure.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createRiskRegisterMeasure(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
