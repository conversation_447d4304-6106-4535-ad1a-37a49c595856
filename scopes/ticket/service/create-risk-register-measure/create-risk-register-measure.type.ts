/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-14
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { MeasureType } from '@manyun/ticket.gql.client.risk-register';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  riskId: string;
  measureType: MeasureType;
  measureDesc: string;
  followUserId: number;
  planCompleteTime: string;
};

export type ApiR = WriteResponse;
