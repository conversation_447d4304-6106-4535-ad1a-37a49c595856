/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;
// ApiQ 与 SvcQuery 相同
export type SvcQuery = {
  /** 更新时间 */
  optTime: number;
  /** 处理内容 */
  optContent: string;
  /** 后续计划 */
  optPlan: string;
  /** 事件ID */
  eventId: number;
  /** 处理人ID */
  optStaffId: string;
  /** 处理人名称 */
  optStaffName: string;
  files?: McUploadFile[];
};
export type ApiQ = {
  addedFiles?: BackendMcUploadFile[];
} & Omit<SvcQuery, 'files'>;

export type ApiR = WriteResponse;
