/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createEventProgress as webService } from './create-event-progress.browser';
import { createEventProgress as nodeService } from './create-event-progress.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    optTime: 123456,
    optContent: 'test',
    optPlan: 'test',
    eventId: 'SUCCESS_ID',
    optStaffId: 'test',
    optStaffName: 'test',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    optTime: 123456,
    optContent: 'test',
    optPlan: 'test',
    eventId: 'FAIL_ID',
    optStaffId: 'test',
    optStaffName: 'test',
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    optTime: 123456,
    optContent: 'test',
    optPlan: 'test',
    eventId: 'SUCCESS_ID',
    optStaffId: 'test',
    optStaffName: 'test',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    optTime: 123456,
    optContent: 'test',
    optPlan: 'test',
    eventId: 'FAIL_ID',
    optStaffId: 'test',
    optStaffName: 'test',
  });

  expect(typeof error).toBe('object');
});
