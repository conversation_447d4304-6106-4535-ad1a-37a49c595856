/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-event-progress.type';

const endpoint = '/dcom/eventOptLog/create';

/**
 * @see [Doc](http://172.16.0.17:13000/project/146/interface/api/18888)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { files, ...restQuery } = svcQuery;
    const params: ApiQ = {
      ...restQuery,
      addedFiles: (files ?? []).map(file => McUploadFile.fromJSON(file).toApiObject()),
    };
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
