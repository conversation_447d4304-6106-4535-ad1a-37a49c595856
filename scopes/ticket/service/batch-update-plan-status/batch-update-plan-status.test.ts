/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { batchUpdatePlanStatus as webService } from './batch-update-plan-status.browser';
import { batchUpdatePlanStatus as nodeService } from './batch-update-plan-status.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ ids: [1], status: 'SUCCESS_ID' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ ids: [1], status: 'FAIL_ID' });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ ids: [1], status: 'SUCCESS_ID' });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ ids: [1], status: 'FAIL_ID' });

  expect(typeof error).toBe('object');
});
