---
description: 'A batchUpdatePlanStatus HTTP API service.'
labels: ['service', 'http']
---

## 概述

TODO

## 使用

### Browser

```ts
import { batchUpdatePlanStatus } from '@manyun/ticket.service.batch-update-plan-status';

const { error, data } = await batchUpdatePlanStatus('success');
const { error, data } = await batchUpdatePlanStatus('error');
```

### Node

```ts
import { batchUpdatePlanStatus } from '@manyun/ticket.service.batch-update-plan-status/dist/index.node';

const { data } = await batchUpdatePlanStatus('success');
const { error, data } = await batchUpdatePlanStatus('error');
```
