/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = RequestRespData;

export type RequestRespData = boolean | null;

// ApiQ 与 SvcQuery 相同
export type ApiQ = {
  ids: number[];
  /** '1' :启用 '0' :禁用	 */
  status: string;
  ignoreJobItems?: boolean; //忽略校验任务项: 培训计划传:true
};

export type ApiR = WriteResponse;
