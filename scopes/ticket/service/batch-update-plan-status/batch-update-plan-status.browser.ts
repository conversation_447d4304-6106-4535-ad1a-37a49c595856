/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './batch-update-plan-status';
import type { ApiQ, SvcRespData } from './batch-update-plan-status.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function batchUpdatePlanStatus(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
