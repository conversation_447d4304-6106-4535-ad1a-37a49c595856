/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { eventResolve as webService } from './event-resolve.browser';
import { eventResolve as nodeService } from './event-resolve.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    eventId: 1,
    resolveTime: 1,
    resolveDesc: '1',
    resolveUserId: 1,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    eventId: 0,
    resolveTime: 1,
    resolveDesc: '1',
    resolveUserId: 1,
  });

  expect(typeof error).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    eventId: 1,
    resolveTime: 1,
    resolveDesc: '1',
    resolveUserId: 1,
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    eventId: 0,
    resolveTime: 1,
    resolveDesc: '1',
    resolveUserId: 1,
  });

  expect(typeof error).toBe('object');
});
