/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

// SvcQuery 与 ApiQ相同
export type ApiQ = {
  resolveTime: number;
  resolveUserId: number;
  eventId: number;
  resolveDesc: string;
};

export type ApiR = WriteResponse;
