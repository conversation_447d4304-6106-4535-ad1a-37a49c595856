/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './event-resolve';
import type { ApiQ, SvcRespData } from './event-resolve.type';

const executor = getExecutor(nodeRequest);

/**
 * @param  svcQuery
 * @returns
 */
export function eventResolve(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
