/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-18
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './event-upgrade-finish.type';

const endpoint = '/dcom/event/upgradeFinish';

/**
 * @see [Doc]( http://172.16.0.17:13000/project/146/interface/api/19375)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
