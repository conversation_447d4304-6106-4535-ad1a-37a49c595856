/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-18
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './event-upgrade-finish';
import type { ApiQ, SvcRespData } from './event-upgrade-finish.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function eventUpgradeFinish(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
