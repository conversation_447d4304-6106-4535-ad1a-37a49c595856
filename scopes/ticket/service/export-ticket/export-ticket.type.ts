/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockTag?: string;
  checkEndTime?: string;
  checkStartTime?: string;
  creatorId?: number;
  creatorName?: string;
  currentTaskAssignee?: boolean;
  effectEndTime?: string;
  effectStartTime?: string;
  idcTag?: string;
  initiator?: boolean;
  isDelay?: string;
  needTotalCount?: boolean;
  offset?: number;
  openInitWithdraw?: boolean;
  openSch?: string;
  effectTime?: string;
  relateTaskNo?: string;
  roomGuid?: string;
  roomGuidList?: string[];
  scheduleId?: number;
  subjectTag?: string;
  taskAssignee?: number;
  taskAssigneeName?: string;
  taskNo?: string;
  taskNoList?: string[];
  taskStatus?: string;
  taskStatusList?: string[];
  taskSubType?: string;
  taskSubTypeList?: string[];
  taskTitle?: string;
  taskType?: string;
  assigneeList?: [
    {
      id: number;
      userName: string;
    }
  ];
  approvalCode?: string;
  configRoomGuid?: string;
};
export type SvcRespData = Blob;

export type RequestRespData = Blob;

export type ApiQ = SvcQuery;

export type ApiR = Response<Blob>;
