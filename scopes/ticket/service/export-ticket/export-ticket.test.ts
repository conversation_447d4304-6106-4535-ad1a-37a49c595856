/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportTicket as webService } from './export-ticket.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({ taskNo: '11111111111' });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ taskNo: '0' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
