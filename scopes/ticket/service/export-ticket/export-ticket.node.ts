/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-ticket';
import type { SvcQuery, SvcRespData } from './export-ticket.type';

const executor = getExecutor(nodeRequest);

/**
 * @param SvcQuery
 * @returns
 */
export function exportTicket(SvcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(SvcQuery);
}
