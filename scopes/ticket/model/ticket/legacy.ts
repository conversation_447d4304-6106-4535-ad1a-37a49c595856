export enum BackendTaskStatus {
  INIT = '4',
  WAITTAKEOVER = '3',
  FINISH = '1',
  PROCESSING = '2',
  FAILURE = '0',
  UNDO = '5',
  CLOSE_APPROVER = '6',
}

export enum MaintenanceTaskSubType {
  /**
   * 消防维护
   */
  M_FIRE = 'M_FIRE',
  /**
   * 安防维护
   */
  M_SECURITY = 'M_SECURITY',

  /**
   * 设施维护
   */
  M_ESTABLISHMENT = 'M_ESTABLISHMENT',

  /**
   * IT维护
   */
  M_IT = 'M_IT',

  /**
   * 工器具送检
   */
  M_SJ = 'M_SJ',
}

export enum BackendChangeShiftStatus {
  INIT = 'INIT',
  WAIT_HANDLE = 'WAIT_HANDLE',
  HANDED = 'HANDED',
  CANCELED = 'CANCELED',
  COUNTERSIGN = 'COUNTERSIGN',
}
export enum BackendRiskType {
  ELECTRIC = 'ELECTRIC',
  HVAC = 'HVAC',
  SECURITY = 'SECURITY',
  FIRE = 'FIRE',
  BUILDING = 'BUILDING',
  CABLING_SYSTEM = 'CABLING_SYSTEM',
  OPERATION = 'OPERATION',
}

export enum BackendEmergencyDrillType {
  EMERGENCY = 'EMERGENCY',
  DRILL = 'DRILL',
}
export enum BackendEmergencyDrillLevel {
  HIGH = 'HIGH',
  MID = 'MID',
  LOW = 'LOW',
}
export enum BackendRiskLevel {
  HIGH = 'HIGH',
  MID = 'MID',
  LOW = 'LOW',
}
export enum BackendRiskStatus {
  RESOLVED = 'RESOLVED',
  UNSOLVED = 'UNSOLVED',
}
export enum BackendEmergencyDrillMajor {
  ELECTRICAL = 'ELECTRICAL',
  HVAC = 'HVAC',
  FIRE = 'FIRE',
  SECURITY = 'SECURITY',
  COUNTERTERRORISM = 'COUNTERTERRORISM',
  EXPLOSION_PROOF = 'EXPLOSION_PROOF',
  TYPHOON = 'TYPHOON',
  FLOOD_PREVENTION = 'FLOOD_PREVENTION',
}

export enum AccessCardTaskSubType {
  CardApply = 'CARD_APPLY',
  CardChange = 'CARD_CHANGE',
  CardOff = 'CARD_OFF',
  CardReplace = 'CARD_EXCHANGE',
}

export enum AccessCardChangeType {
  DELETE = 'DELETE',
  CHANGE = 'CHANGE',
  ADD = 'ADD',
}

export enum TicketSlaUnit {
  HOUR = 'HOUR',
  MINUTES = 'MINUTES',
  DAY = 'DAY',
  SECOND = 'SECOND',
}

export enum InspectionScenarios {
  All = 'ALL',
  On = 'ON',
  Off = 'OFF',
}

export const InspectionScenariosTextMap = {
  [InspectionScenarios.All]: '所有',
  [InspectionScenarios.On]: '仅开机',
  [InspectionScenarios.Off]: '仅关机',
};

export const TicketSlaUnitKeyTextMap = {
  [TicketSlaUnit.HOUR]: '小时',
  [TicketSlaUnit.MINUTES]: '分钟',
  [TicketSlaUnit.DAY]: '天',
  [TicketSlaUnit.SECOND]: '秒',
};

export const AccessCardChangeTypeMap = {
  [AccessCardChangeType.ADD]: '添加机房权限',
  [AccessCardChangeType.CHANGE]: '更新机房权限',
  [AccessCardChangeType.DELETE]: '删除机房权限',
};

export const AccessCardUserRoleList = [
  {
    label: '基础设施驻场',
    value: '基础设施驻场',
  },
  {
    label: 'IT运维驻场',
    value: 'IT运维驻场',
  },
  {
    label: '安防保洁人员',
    value: '安防保洁人员',
  },
  {
    label: '厂商人员',
    value: '厂商人员',
  },
  {
    label: '客户',
    value: '客户',
  },
  {
    label: '其他人员',
    value: '其他人员',
  },
];

export const EmergencyDrillMajorMap = {
  [BackendEmergencyDrillMajor.ELECTRICAL]: '电气',
  [BackendEmergencyDrillMajor.HVAC]: '暖通',
  [BackendEmergencyDrillMajor.FIRE]: '消防',
  [BackendEmergencyDrillMajor.SECURITY]: '弱电安防',
  [BackendEmergencyDrillMajor.COUNTERTERRORISM]: '防恐',
  [BackendEmergencyDrillMajor.EXPLOSION_PROOF]: '防爆',
  [BackendEmergencyDrillMajor.TYPHOON]: '防台',
  [BackendEmergencyDrillMajor.FLOOD_PREVENTION]: '防汛',
};
export const EmergencyDrillTypeMap = {
  [BackendEmergencyDrillType.EMERGENCY]: '应急',
  [BackendEmergencyDrillType.DRILL]: '演练',
};
export const EmergencyDrillLevelMap = {
  [BackendEmergencyDrillLevel.HIGH]: '高',
  [BackendEmergencyDrillLevel.MID]: '中',
  [BackendEmergencyDrillLevel.LOW]: '低',
};
export const ChangeShiftStatusMap = {
  [BackendChangeShiftStatus.INIT]: '已创建',
  [BackendChangeShiftStatus.COUNTERSIGN]: '加签中',
  [BackendChangeShiftStatus.WAIT_HANDLE]: '待接班',
  [BackendChangeShiftStatus.HANDED]: '已接班',
  [BackendChangeShiftStatus.CANCELED]: '已取消',
};

export const RiskTypeMap = {
  [BackendRiskType.ELECTRIC]: '电力',
  [BackendRiskType.HVAC]: '暖通',
  [BackendRiskType.SECURITY]: '安防',
  [BackendRiskType.FIRE]: '消防',
  [BackendRiskType.BUILDING]: '建筑物及外围',
  [BackendRiskType.CABLING_SYSTEM]: '综合布线',
  [BackendRiskType.OPERATION]: '运营能力',
};

export const RiskLevelMap = {
  [BackendRiskLevel.HIGH]: '高风险',
  [BackendRiskLevel.MID]: '中风险',
  [BackendRiskLevel.LOW]: '低风险',
};

export const RiskStatusMap = {
  [BackendRiskStatus.RESOLVED]: '已解除',
  [BackendRiskStatus.UNSOLVED]: '未解除',
};

export const TaskStatusMap = {
  [BackendTaskStatus.INIT]: '建单审批中',
  [BackendTaskStatus.WAITTAKEOVER]: '待接单',
  [BackendTaskStatus.FINISH]: '已关单',
  [BackendTaskStatus.PROCESSING]: '处理中',
  [BackendTaskStatus.FAILURE]: '失败',
  [BackendTaskStatus.UNDO]: '撤回',
  [BackendTaskStatus.CLOSE_APPROVER]: '关单审批中',
};

export const EMERGENCY_DRILL_TYPE_LIST = [
  { label: '应急', key: BackendEmergencyDrillType.EMERGENCY },
  { label: '演练', key: BackendEmergencyDrillType.DRILL },
];
export const EMERGENCY_DRILL_LEVEL_LIST = [
  { label: '高', key: BackendEmergencyDrillLevel.HIGH },
  { label: '中', key: BackendEmergencyDrillLevel.MID },
  { label: '低', key: BackendEmergencyDrillLevel.LOW },
];
export const EMERGENCY_DRILL_MAJOR_LIST = [
  { label: '电气', key: BackendEmergencyDrillMajor.ELECTRICAL },
  { label: '暖通', key: BackendEmergencyDrillMajor.HVAC },
  { label: '消防', key: BackendEmergencyDrillMajor.FIRE },
  { label: '弱电安防', key: BackendEmergencyDrillMajor.SECURITY },
  { label: '防恐', key: BackendEmergencyDrillMajor.COUNTERTERRORISM },
  { label: '防爆', key: BackendEmergencyDrillMajor.EXPLOSION_PROOF },
  { label: '防台', key: BackendEmergencyDrillMajor.TYPHOON },
  { label: '防汛', key: BackendEmergencyDrillMajor.FLOOD_PREVENTION },
];
export const RISK_LEVEL_LIST = [
  { label: '高风险', key: BackendRiskLevel.HIGH },
  { label: '中风险', key: BackendRiskLevel.MID },
  { label: '低风险', key: BackendRiskLevel.LOW },
];
export const RISK_STATUS_LIST = [
  { label: '已解除', key: BackendRiskStatus.RESOLVED },
  { label: '未解除', key: BackendRiskStatus.UNSOLVED },
];
export const RISK_TYPE_LIST = [
  { label: '电力', key: BackendRiskType.ELECTRIC },
  { label: '暖通', key: BackendRiskType.HVAC },
  { label: '安防', key: BackendRiskType.SECURITY },
  { label: '消防', key: BackendRiskType.FIRE },
  { label: '建筑物及外围', key: BackendRiskType.BUILDING },
  { label: '综合布线', key: BackendRiskType.CABLING_SYSTEM },
  { label: '运营能力', key: BackendRiskType.OPERATION },
];
export type TaskType =
  | 'INSPECTION'
  | 'REPAIR'
  | 'MAINTENANCE'
  | 'INVENTORY'
  | 'POWER'
  | 'ON_OFF'
  | 'DEVICE_GENERAL'
  | 'ACCESS'
  | 'WAREHOUSE'
  | 'ACCESS_CARD_AUTH'
  | 'VISITOR'
  | 'RISK_REGISTER';

export const TaskTypeMap: Record<TaskType, string> = {
  INSPECTION: '巡检',
  REPAIR: '维修',
  MAINTENANCE: '维护',
  INVENTORY: '盘点',
  POWER: '上下电',
  ON_OFF: '上下线',
  DEVICE_GENERAL: '上下架',
  ACCESS: '出入门',
  WAREHOUSE: '出入库',
  ACCESS_CARD_AUTH: '门禁授权',
  VISITOR: '人员入室申请',
  RISK_REGISTER: '风险',
};

export const TICKET_LIST_REPAIR_FILTER_STATUS_LIST: { label: string; value: string }[] = [
  {
    value: BackendTaskStatus.WAITTAKEOVER,
    label: '待接单',
  },
  {
    value: BackendTaskStatus.PROCESSING,
    label: '处理中',
  },
  {
    value: BackendTaskStatus.FINISH,
    label: '已关单',
  },
  {
    value: BackendTaskStatus.FAILURE,
    label: '失败',
  },
  {
    value: BackendTaskStatus.CLOSE_APPROVER,
    label: '关单审批中',
  },
];

export const AccessCardTaskSubTypeMapText = {
  [AccessCardTaskSubType.CardApply]: '门禁卡申请',
  [AccessCardTaskSubType.CardChange]: '门禁卡变更',
  [AccessCardTaskSubType.CardOff]: '门禁卡注销',
  [AccessCardTaskSubType.CardReplace]: '门禁卡换卡',
};
export type BackendTicket = {
  // 工单号
  taskNo: string;
  // 工单大类
  taskType: string;
  // 工单子类型
  taskSubType: string;
  // 位置
  blockTag: string;
  // 工单标题
  taskTitle: string;
  // 创建时间
  gmtCreate: string;
  // 修改时间
  gmtModified: string;
  // 工单状态，枚举类型
  taskStatus: BackendTaskStatus;
  // 处理人ID
  taskAssignee: number | null;
  // 处理人名称
  taskAssigneeName: string | null;
  taskProperties?: string | null;
  /** 提单人id */
  creatorId: number;
  /** 提单人 */
  creatorName: string;
  /** 生效时间 */
  effectTime: string;
};
export enum InspectionObject {
  Device = 'DEVICE',
  Environment = 'ENVIRONMENT',
}

export const INSPECTION_OBJECT_TEXT = {
  [InspectionObject.Device]: '设备',
  [InspectionObject.Environment]: '空间',
};
export const INSPECTION_OBJECT_KEY_MAP = [
  {
    label: INSPECTION_OBJECT_TEXT[InspectionObject.Device],
    value: InspectionObject.Device,
  },
  {
    label: INSPECTION_OBJECT_TEXT[InspectionObject.Environment],
    value: InspectionObject.Environment,
  },
];

export enum TicketPlanType {
  Maintenance = 'MAINTENANCE',
  Inspection = 'INSPECTION',
  Inventory = 'INVENTORY',
}

export enum HandoverConfig {
  SuccessionLeader,
  AllTeamMembers,
}

export const HANDOVER_CONFIG_TEXT_MAP = {
  [HandoverConfig.SuccessionLeader]: '仅校验接班负责人',
  [HandoverConfig.AllTeamMembers]: '校验接班班组全员',
};
export enum StandardChangeLibraryStatus {
  Draft = 'DRAFT',
  Approving = 'APPROVING',
  YqApproving = 'YQ_APPROVING',
  Expire = 'EXPIRE',
  Available = 'AVAILABLE',
}

export const statusColorMaps: Record<string, string> = {
  [StandardChangeLibraryStatus.Draft]: 'default',
  [StandardChangeLibraryStatus.Approving]: 'processing',
  [StandardChangeLibraryStatus.Expire]: 'error',
  [StandardChangeLibraryStatus.Available]: 'success',
  [StandardChangeLibraryStatus.YqApproving]: 'processing',
};

export const statusTextMaps: Record<string, string> = {
  [StandardChangeLibraryStatus.Draft]: '草稿',
  [StandardChangeLibraryStatus.Available]: '有效',
  [StandardChangeLibraryStatus.Expire]: '失效',
  [StandardChangeLibraryStatus.Approving]: '录入审批中',
  [StandardChangeLibraryStatus.YqApproving]: '延期审批中',
};
