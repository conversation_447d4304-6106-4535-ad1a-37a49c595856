import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BackendTaskStatus, BackendTicket } from './backend-ticket';
import { getTicketLocales } from './locales';
import type { TicketLocales } from './locales';

export type TicketJSON<T extends object = object> = {
  id: string;
  type: string;
  subType: string;
  blockGuid: string;
  title: string;
  createdAt: string;
  modifiedAt: string;
  state: BackendTaskStatus;
  assignee: { id: number; name: string } | null;
  taskProperties: T;
  effectiveAt: string;
  /** 提单人id */
  creatorId: number;
  /** 提单人 */
  creatorName: string;
};

export class Ticket<T extends object = object> {
  static fromApiObject<T extends object = object>(object: BackendTicket) {
    const copy = cloneDeep(object);

    return new Ticket<T>(
      copy.taskNo,
      copy.taskType,
      copy.taskSubType,
      copy.blockTag,
      copy.taskTitle,
      copy.gmtCreate,
      copy.gmtModified,
      copy.taskStatus,
      copy.taskAssignee && copy.taskAssigneeName
        ? { id: copy!.taskAssignee, name: copy!.taskAssigneeName }
        : null,
      !copy.taskProperties ? null : JSON.parse(copy.taskProperties),
      copy.creatorId,
      copy.creatorName,
      copy.effectTime
    );
  }

  static fromJSON<T extends object = object>(json: TicketJSON) {
    const copy = cloneDeep(json);

    return new Ticket<T>(
      copy.id,
      copy.type,
      copy.subType,
      copy.blockGuid,
      copy.title,
      copy.createdAt,
      copy.modifiedAt,
      copy.state,
      copy.assignee,
      copy.taskProperties as T,
      copy.creatorId,
      copy.creatorName,
      copy.effectiveAt
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: TicketLocales;

  constructor(
    // 工单号
    public id: string,
    // 工单大类
    public type: string,
    // 工单子类型
    public subType: string,
    // 位置
    public blockGuid: string,
    // 工单标题
    public title: string,
    // 创建时间
    public createdAt: string,
    // 修改时间
    public modifiedAt: string,
    // 工单状态，枚举类型
    public state: BackendTaskStatus,
    // 处理人ID
    public assignee: { id: number; name: string } | null,
    public taskProperties: T,
    /** 提单人id */
    public creatorId: number,
    /** 提单人 */
    public creatorName: string,
    /** 生效时间 */
    public effectiveAt: string
  ) {
    this._locales = getTicketLocales(this._localeCode);
  }
  public set locales(locales: TicketLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendTicket {
    const id = this.assignee ? this.assignee.id : null;
    const name = this.assignee ? this.assignee.name : null;

    return cloneDeep({
      taskNo: this.id,
      taskType: this.type,
      taskSubType: this.subType,
      blockTag: this.blockGuid,
      taskTitle: this.title,
      gmtCreate: this.createdAt,
      gmtModified: this.modifiedAt,
      taskStatus: this.state,
      taskAssignee: id,
      taskAssigneeName: name,
      taskProperties: this.taskProperties === null ? null : JSON.stringify(this.taskProperties),
      creatorId: this.creatorId,
      creatorName: this.creatorName,
      effectTime: this.effectiveAt,
    });
  }

  public toJSON(): TicketJSON<T> {
    return cloneDeep({
      id: this.id,
      type: this.type,
      subType: this.subType,
      blockGuid: this.blockGuid,
      title: this.title,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      state: this.state,
      assignee: this.assignee,
      taskProperties: this.taskProperties,
      effectiveAt: this.effectiveAt,
      creatorId: this.creatorId,
      creatorName: this.creatorName,
    });
  }
}
