import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import { McUploadFile, type McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';

import type {
  AggBlock,
  BackendPlan,
  BackendTask,
  Cycle,
  CyclesType,
  JobItem,
  JobType,
  MaintenanceCycle,
  PlanBlock,
  SchModeType,
  SlaUnit,
  SpecifyTarget,
  Splitor,
  TaskEffectStatus,
} from './backend-task';
import { PlanEffect, PlanStatus } from './backend-task';
import { getTaskLocales } from './locales';
import type { TaskLocales } from './locales';

type PlanCreator = {
  id: number;
  name: string;
};

type PlanModifyUser = {
  id?: number | null;
  name?: string | null;
};

export type MonthlyTasks = {
  localDate: string;
  calendarExecuteInfoSet: TaskJSON[];
};
export type PlanJSON = {
  id: number;
  name: string;
  guidePeriod: MaintenanceCycle;
  blockGuid: string;
  planType: JobType;
  mopCount: number;
  mopType: string;
  periodUnit: CyclesType;
  isActivated: PlanStatus;
  isInEffect: PlanEffect;
  lastExecuteTime: number | null;
  lastExecuteResult: string[];
  createTime: number;
  creator: PlanCreator;
  modifyUser?: PlanModifyUser | null;
  blockScope: PlanBlock[];
  modifyTime?: number;
  repeatCycle?: Cycle;
  allowTriggerTime?: string[] | null | string;
  /** 分发范围展示 */
  aggBlockScope?: AggBlock[];
  endTime?: number | null;
  splitor?: Splitor[];
  jobSla?: number;
  slaUnit?: SlaUnit;
  jobItemList?: JobItem[];
  finishTaskNoList?: string[];
  unFinishTaskNoList?: string[];
  manageType?: string;
  scheduleSplitRangeList?: SpecifyTarget[];
  schLevel?: string;
  fileInfoList?: McUploadFileJSON[] | null;
  taskResultNums?: number | null;
  principalUser?: PlanModifyUser | null;
  schMode?: SchModeType;
  schProperties?: string;
  owner?: PlanModifyUser | null;
};

export type TaskJSON = {
  effectTime: number;
  id: number;
  name: string;
  endTime: number | null;
  effectStatus: TaskEffectStatus;
  jobType: JobType;
  jobSla: number;
  blockGuid: string;
  jumpHolidayEffectTime?: number;
  schMode?: SchModeType;
};

class TaskBasicData {
  constructor(
    /** 计划Id */
    public id: number,
    /** 计划名称 */
    public name: string
  ) {}
}

export class Plan extends TaskBasicData {
  static fromApiObject(object: BackendPlan) {
    const copy = cloneDeep(object);

    return new Plan(
      copy.id,
      copy.name,
      copy.guidePeriod,
      copy.blockScope[0].blockGuid,
      copy.jobType,
      copy.jobItemNum,
      copy.subJobType,
      copy.periodUnit,
      copy.status,
      copy.isInvalid,
      typeof copy.triggerTime === 'string' ? dayjs(copy.triggerTime).valueOf() : null,
      copy.taskNoList,
      dayjs(copy.gmtCreate).valueOf(),
      {
        id: copy.creatorId,
        name: copy.creatorName,
      },
      copy.blockScope,
      {
        id: copy.modifierId,
        name: copy.modifierName,
      },
      typeof copy.gmtModified === 'string' ? dayjs(copy.gmtModified).valueOf() : undefined,
      copy.cycles,
      copy.allowTriggerTime,
      copy.aggBlockScope,
      dayjs(copy.endTime).valueOf(),
      copy.splitor,
      copy.jobSla,
      copy.slaUnit,
      copy.jobItemList,
      copy.finishTaskNoList,
      copy.unFinishTaskNoList,
      copy.manageType,
      copy.scheduleSplitRangeList,
      copy.schLevel,
      copy.fileInfoList?.map(item => McUploadFile.fromApiObject(item).toJSON()),
      copy.taskResultNums,
      {
        id: copy.principalId,
        name: copy.principalName,
      },
      copy.schMode,
      copy.schProperties,
      {
        id: copy.ownerId,
        name: copy.ownerName,
      }
    );
  }

  static fromJSON(json: PlanJSON) {
    const copy = cloneDeep(json);

    return new Plan(
      copy.id,
      copy.name,
      copy.guidePeriod,

      copy.blockGuid,
      copy.planType,
      copy.mopCount,
      copy.mopType,
      copy.periodUnit,
      copy.isActivated,
      copy.isInEffect,
      copy.lastExecuteTime,
      copy.lastExecuteResult,
      copy.createTime,
      copy.creator,
      copy.blockScope,
      copy.modifyUser,
      copy.modifyTime,
      copy.repeatCycle,
      copy.allowTriggerTime,
      copy.aggBlockScope,
      copy.endTime,
      copy.splitor,
      copy.jobSla,
      copy.slaUnit,
      copy.jobItemList,
      copy.finishTaskNoList,
      copy.unFinishTaskNoList,
      copy.manageType,
      copy.scheduleSplitRangeList,
      copy.schLevel,
      copy.fileInfoList,
      copy.taskResultNums,
      copy.principalUser,
      copy.schMode,
      copy.schProperties,
      copy.owner
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';

  private _locales: TaskLocales;

  constructor(
    /** 计划Id */
    public id: number,
    /** 计划名称 */
    public name: string,
    public guidePeriod: MaintenanceCycle,

    public blockGuid: string,
    public planType: JobType,
    public mopCount: number,
    public mopType: string,
    public periodUnit: CyclesType,
    /** 是否启用 */
    public isActivated: PlanStatus,
    /** 是否生效 */
    public isInEffect: PlanEffect,
    public lastExecuteTime: number | null,
    public lastExecuteResult: string[],
    public createTime: number,
    public creator: PlanCreator,
    public blockScope: PlanBlock[],
    public modifyUser?: PlanModifyUser | null,
    public modifyTime?: number,
    /** 重复周期 */
    public repeatCycle?: Cycle,
    public allowTriggerTime?: string[] | null | string,
    public aggBlockScope?: AggBlock[],
    public endTime?: number | null,
    public splitor?: Splitor[],
    public jobSla?: number,
    public slaUnit?: SlaUnit,
    public jobItemList?: JobItem[],
    public finishTaskNoList?: string[],
    public unFinishTaskNoList?: string[],
    public manageType?: string,
    public scheduleSplitRangeList?: SpecifyTarget[],
    public schLevel?: string,
    public fileInfoList?: McUploadFileJSON[] | null,
    public taskResultNums?: number | null,
    public principalUser?: PlanModifyUser | null,
    public schMode?: SchModeType,
    public schProperties?: string,
    public owner?: PlanModifyUser | null
  ) {
    super(id, name);
    this._locales = getTaskLocales(this._localeCode);
  }

  public set locales(locales: TaskLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createTime).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return this.modifyTime ? dayjs(this.modifyTime).format(template) : '--';
  }

  public getFormattedLastExecuteAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return this.lastExecuteTime ? dayjs(this.lastExecuteTime).format(template) : '--';
  }

  public getFormattedEndTime(template = 'YYYY-MM-DD HH:mm:ss') {
    return this.endTime ? dayjs(this.endTime).format(template) : '--';
  }

  public toApiObject(): BackendPlan {
    return cloneDeep({
      id: this.id,
      blockScope: this.blockScope,
      guidePeriod: this.guidePeriod,
      blockGuid: this.blockGuid,
      name: this.name,
      jobItemNum: this.mopCount,
      jobType: this.planType,
      jobSla: this.jobSla,
      subJobType: this.mopType,
      periodUnit: this.periodUnit,
      status: this.isActivated ? PlanStatus.On : PlanStatus.Off,
      isInvalid: this.isInEffect ? PlanEffect.Valid : PlanEffect.Invalid,
      triggerTime: this.getFormattedLastExecuteAt(),
      taskNoList: this.lastExecuteResult,
      gmtCreate: this.getFormattedCreatedAt(),
      gmtModified: this.getFormattedModifiedAt(),
      creatorId: this.creator.id,
      creatorName: this.creator.name,
      cycles: this.repeatCycle,
      aggBlockScope: this.aggBlockScope,
      allowTriggerTime: this.allowTriggerTime,
      endTime: this.getFormattedEndTime(),
      splitor: this.splitor,
      slaUnit: this.slaUnit,
      jobItemList: this.jobItemList,
      unFinishTaskNoList: this.unFinishTaskNoList,
      finishTaskNoList: this.finishTaskNoList,
      manageType: this.manageType,
      scheduleSplitRangeList: this.scheduleSplitRangeList,
      schLevel: this.schLevel,
      fileInfoList: this.fileInfoList?.map(item => McUploadFile.fromJSON(item).toApiObject()),
      taskResultNums: this.taskResultNums,
      modifierId: this.modifyUser?.id,
      modifierName: this.modifyUser?.name,
      principalId: this.principalUser?.id,
      principalName: this.principalUser?.name,
      schMode: this.schMode,
      schProperties: this.schProperties,
      ownerId: this.owner?.id,
      ownerName: this.owner?.name,
    });
  }

  public toJSON(): PlanJSON {
    return cloneDeep({
      id: this.id,
      name: this.name,
      guidePeriod: this.guidePeriod,
      blockGuid: this.blockGuid,
      planType: this.planType,
      mopCount: this.mopCount,
      mopType: this.mopType,
      periodUnit: this.periodUnit,
      isActivated: this.isActivated,
      isInEffect: this.isInEffect,
      lastExecuteTime: this.lastExecuteTime,
      lastExecuteResult: this.lastExecuteResult,
      createTime: this.createTime,
      modifyTime: this.modifyTime,
      creator: this.creator,
      modifyUser: this.modifyUser,
      blockScope: this.blockScope,
      repeatCycle: this.repeatCycle,
      aggBlockScope: this.aggBlockScope,
      allowTriggerTime: this.allowTriggerTime,
      endTime: this.endTime,
      splitor: this.splitor,
      jobSla: this.jobSla,
      slaUnit: this.slaUnit,
      jobItemList: this.jobItemList,
      unFinishTaskNoList: this.unFinishTaskNoList,
      finishTaskNoList: this.finishTaskNoList,
      manageType: this.manageType,
      scheduleSplitRangeList: this.scheduleSplitRangeList,
      schLevel: this.schLevel,
      fileInfoList: this.fileInfoList,
      taskResultNums: this.taskResultNums,
      principalUser: this.principalUser,
      schMode: this.schMode,
      schProperties: this.schProperties,
      owner: this.owner,
    });
  }
}
export class Task extends TaskBasicData {
  static fromApiObject(object: BackendTask) {
    const copy = cloneDeep(object);

    return new Task(
      copy.id,
      copy.name,
      dayjs(copy.endTime).valueOf(),
      dayjs(copy.effectTime).valueOf(),
      copy.effectStatus,
      copy.jobType,
      copy.jobSla,
      copy.blockGuid,
      dayjs(copy.jumpHolidayEffectTime).valueOf(),
      copy.schMode
    );
  }

  static fromJSON(json: TaskJSON) {
    const copy = cloneDeep(json);

    return new Task(
      copy.id,
      copy.name,
      copy.endTime,
      copy.effectTime,
      copy.effectStatus,
      copy.jobType,
      copy.jobSla,
      copy.blockGuid,
      copy.jumpHolidayEffectTime,
      copy.schMode
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';

  private _locales: TaskLocales;

  constructor(
    /** 计划Id */
    public id: number,
    /** 计划名称 */
    public name: string,
    /** 结束时间 */
    public endTime: number | null,
    /** 生效时间 */
    public effectTime: number,
    /** 是否生效 */
    public effectStatus: TaskEffectStatus,

    public jobType: JobType,
    public jobSla: number,
    public blockGuid: string,
    public jumpHolidayEffectTime?: number,
    public schMode?: SchModeType
  ) {
    super(id, name);
    this._locales = getTaskLocales(this._localeCode);
  }

  public set locales(locales: TaskLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedEndTime(template = 'YYYY-MM-DD HH:mm:ss') {
    return this.endTime ? dayjs(this.endTime).format(template) : null;
  }

  public getFormattedEffectedTime(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.effectTime).format(template);
  }

  public getFormattedJumpHolidayEffectTime(template = 'YYYY-MM-DD HH:mm:ss') {
    return this.jumpHolidayEffectTime
      ? dayjs(this.jumpHolidayEffectTime).format(template)
      : undefined;
  }

  public toApiObject(): BackendTask {
    return cloneDeep({
      id: this.id,
      name: this.name,
      effectStatus: this.effectStatus,
      effectTime: this.getFormattedEffectedTime(),
      endTime: this.getFormattedEndTime(),
      jobType: this.jobType,
      jobSla: this.jobSla,
      blockGuid: this.blockGuid,
      jumpHolidayEffectTime: this.getFormattedJumpHolidayEffectTime(),
      schMode: this.schMode,
    });
  }

  public toJSON(): TaskJSON {
    return cloneDeep({
      id: this.id,
      name: this.name,
      effectStatus: this.effectStatus,
      effectTime: this.effectTime,
      endTime: this.endTime,
      jobType: this.jobType,
      jobSla: this.jobSla,
      blockGuid: this.blockGuid,
      jumpHolidayEffectTime: this.jumpHolidayEffectTime,
      schMode: this.schMode,
    });
  }
}
