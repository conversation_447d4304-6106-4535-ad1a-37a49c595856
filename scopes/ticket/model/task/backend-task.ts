import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

export enum MaintenanceCycle {
  Weekly = 'WEEKLY',
  DoubleWeek = 'DOUBLE_WEEKLY',
  Monthly = 'MONTHLY',
  DoubleMonthly = 'DOUBLE_MONTHLY',
  Quarter = 'QUARTER',
  HalfAnnual = 'HALF_ANNUAL',
  Annual = 'ANNUAL',
  Other = 'OTHER',
}
export const MAINTENANCE_CYCLE_TEXT_MAP = {
  [MaintenanceCycle.Weekly]: '周度',
  [MaintenanceCycle.DoubleWeek]: '双周度',
  [MaintenanceCycle.Monthly]: '月度',
  [MaintenanceCycle.DoubleMonthly]: '双月度',
  [MaintenanceCycle.Quarter]: '季度',
  [MaintenanceCycle.HalfAnnual]: '半年度',
  [MaintenanceCycle.Annual]: '年度',
  [MaintenanceCycle.Other]: '其他',
};

export const MAINTENANCE_CYCLE_OPTIONS = [
  {
    value: MaintenanceCycle.Weekly,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.Weekly],
  },
  {
    value: MaintenanceCycle.DoubleWeek,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.DoubleWeek],
  },
  {
    value: MaintenanceCycle.Monthly,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.Monthly],
  },
  {
    value: MaintenanceCycle.DoubleMonthly,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.DoubleMonthly],
  },
  {
    value: MaintenanceCycle.Quarter,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.Quarter],
  },
  {
    value: MaintenanceCycle.HalfAnnual,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.HalfAnnual],
  },
  {
    value: MaintenanceCycle.Annual,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.Annual],
  },
  {
    value: MaintenanceCycle.Other,
    label: MAINTENANCE_CYCLE_TEXT_MAP[MaintenanceCycle.Other],
  },
];
export type AggBlock = {
  blockGuidList: string[];
  /** 楼栋/楼栋角色/人员	 */
  scopeType: DistributeMode;
  /** null/角色ID/人员ID	 */
  scopeFlag: string;
  /** 考勤组信息 */
  scopeFlagInfoList: {
    /** 考勤组名称	 */
    flagName: string;
    /** 考勤组ID */
    scopeFlag: number;
  }[];
};
export type Splitor = {
  /** 设备 or 空间 */
  splitType: SplitorType;
  splitParam: string | ThreeDeviceType | null | number;
};
export type JobItem = {
  id: number;
  jobId: number;
  scheduleId: number;
  name: string;
  itemType: string;
  scheduleType: string;
  jobTypeName: string;
  jobTypeCode: string;
  subJobTypeName: string;
  subJobTypeCode: string;
  gmtCreate: string;
};

export type PlanBlock = {
  blockGuid: string;
  scopeType: DistributeMode;
  scopeFlag?: number;
};
export type PlanDayConfig = {
  days: string[] | WeekType[] | null;
  isRemove: number;
};

export type Cycle = {
  periodUnit: CyclesType;
  period: number;
  dayConfig: PlanDayConfig;
  atDayConfig?: {
    month: string | null;
    sortNum: number;
    atDayType: MonthlyWorkDayType;
    isLast?: number;
  }[];
};
export type BackendPlan = {
  id: number;
  name: string;
  guidePeriod: MaintenanceCycle;
  /** mop 类型 */
  subJobType: string;
  /** mop 数量 */
  jobItemNum: number;
  /** 计划大类：维护、巡检、盘点 */
  jobType: JobType;
  periodUnit: CyclesType;
  /** 是否启用 */
  status: PlanStatus;
  /** 是否有效 */
  isInvalid: PlanEffect;
  /** 创建时间 */
  gmtCreate: string;
  creatorId: number;
  creatorName: string;
  /** 分发范围 */
  blockScope: PlanBlock[];
  /** 上一次执行结果 */ /** 上一次运行时间 */ triggerTime: string;
  taskNoList: string[];
  /** 更新时间 */
  gmtModified?: string;
  cycles?: Cycle;
  allowTriggerTime?: string[] | null | string;
  /** 分发范围展示 */
  aggBlockScope?: AggBlock[];
  endTime?: string | null;
  splitor?: Splitor[];
  jobSla?: number;
  slaUnit?: SlaUnit;
  jobItemList?: JobItem[];
  finishTaskNoList?: string[];
  unFinishTaskNoList?: string[];
  manageType?: string;
  scheduleSplitRangeList?: SpecifyTarget[];
  schLevel?: string;
  fileInfoList?: BackendMcUploadFile[];
  modifierName?: string | null;
  modifierId?: number | null;
  taskResultNums?: number | null; // Excel报表的报表数量
  principalId?: number | null;
  principalName?: string | null;
  schMode?: SchModeType; //培训方式
  schProperties?: string;
  ownerId?: number | null;
  ownerName?: string | null;
};
export type BackendMonthlyTaskSearchParam = {
  startDate?: string;
  endDate?: string;
  jobTypeList?: string[];
  effectStatus?: TaskEffectStatus;
  schName?: string;
  blockGuidList?: string[];
  schId?: string;
  subJobType?: string;
  deviceTypeCode?: string;
  periodUnit?: string;
  creatorId?: string;
  /** 演练等级 */
  schLevel?: string;
};

export type DismantleParameter = {
  splitType: DistributeMode;
  splitParam: string;
};

export type BackendPlanSearchParam = {
  /** 任务名称 */
  name?: string | null;
  /** 任务类型,MOP类型 */
  jobTypeList?: string[] | null;
  /** Mop类型 */
  subJobType?: string | null;
  /** 机房楼栋 */
  blockGuidList?: string[] | null;
  /** 周期单位 */
  periodUnit?: string | null;
  /** 是否生效 1:失效 0:有效	 */
  isInvalid?: PlanEffect | null;
  /** 状态 : 1:启用  0:禁用	 */
  taskStatus?: PlanStatus | null;
  creatorId?: string | null;
  /** 属地/中心。 LOCAL / GOC	 */
  manageType?: string | null;
  endTime?: number | null;
  startTime?: number | null;
  startDateOfUpdate?: number | null;
  endDateOfUpdate?: number | null;
  sortByField?: string | null;
  defineDesc?: boolean | null;
  sortInfo?: string | null;
  /** 演练等级 */
  schLevel?: string;
  schMode?: SchModeType;
};
export type SpecifyTargetRange = 'ROOM' | 'DEVICE' | 'OTHER';
export type SpecifyTarget = {
  rangeType: SpecifyTargetRange;
  uniFlag: string;
  cycleTimes: number;
  /** blockGuid 或者 三级分类: EC01.A / 100101	 */
  flagKey: string;
  flagValue: string[];
  filterType?: string;
  uniFlagType: 'WHITE' | 'BLACK';
};
export type BackendCreateParams = {
  name: string;
  guidePeriod: MaintenanceCycle;
  jobType: JobType;
  subJobType: string;
  /** 分发范围  */
  blockScope: PlanBlock[];
  periodUnit: CyclesType;
  jobSla: number;
  slaUnit: SlaUnit;
  cycles: Cycle;
  jobItems: JobItem[];
  splitors: Splitor[];
  allowTriggerTime: string[];
  status: PlanStatus;
  endTime: number | null;
  scheduleSplitRangeList?: SpecifyTarget[];
  /** LOCAL / GOC */
  manageType?: string;
  schLevel?: string;
  fileInfoList?: BackendMcUploadFile[];
  schMode?: SchModeType;
  schProperties?: string;
  ownerId?: number;
  ownerName?: string;
};
export type BackendPlanListItem = {
  id: number;
  name: string;
  blockGuid: string;
  jobType: string;
  subJobType: string;
  jobItemNum: number;
  allowTriggerTime: string;
  periodUnit: string;
  /** 分发范围 */
  blockScope: PlanBlock[];
  taskStatus: string;
  triggerTime: string | null;
  gmtCreate: string;
  creatorName: string;
  creatorId: number;
  isInvalid: PlanEffect;
  /** 分发范围展示 */
  aggBlockScope: AggBlock[];
};

export type BackendTask = {
  effectTime: string;
  id: number;
  name: string;
  effectStatus: TaskEffectStatus;
  jobType: JobType;
  jobSla: number;
  blockGuid: string;
  endTime?: string | null;
  jumpHolidayEffectTime?: string;
  schMode?: SchModeType;
};
export enum PlanType {
  MaintenancePlan = 'MT',
  InspectionPlan = 'IPT',
  InventoryPlan = 'IVT',
  RiskCheckPlan = 'RISK',
  RiskCheckTicket = 'RISK_TICKET',
  DrillPlan = 'SCH_EXERCISE',
  ExcelReport = 'EXCEL_REPORT',
}
export const PLAN_TYPE_TEXT_MAP = {
  [PlanType.InspectionPlan]: '机房设备巡检',
  [PlanType.InventoryPlan]: '机房设备盘点',
};
export const MONTH_IN_YEAR_OPYIONS = getMonths();

export enum MaintenancePlanType {
  DeviceMaintenance = 'FACILITY_WB',
  ToolsMaintenance = 'SJ_WB',
}

export enum JobType {
  /** 机房设备巡检 */
  DeviceInspection = 'FACILITY_XJ',
  /** 设备维护 */
  DeviceMaintenance = 'FACILITY_WB',
  /** 机房设备盘点, */
  InventoryCount = 'FACILITY_PD',
  /** 工器具维护 */
  ToolsMaintenance = 'SJ_WB',
  /** 风险计划 */
  RiskPlan = 'SCH_RISK',
  /** 培训计划 */
  TrainPlan = 'TRAIN_COURSE',
}
export const JOB_TYPE_TEXT_MAP = {
  [JobType.DeviceInspection]: '机房设备巡检',
  [JobType.DeviceMaintenance]: '设备维护',
  [JobType.InventoryCount]: '机房设备盘点',

  [JobType.ToolsMaintenance]: '工器具维护',
};

export const JOB_TYPE_OPTIONS = [
  {
    label: JOB_TYPE_TEXT_MAP[JobType.DeviceInspection],
    value: JobType.DeviceInspection,
  },
  {
    label: JOB_TYPE_TEXT_MAP[JobType.DeviceMaintenance],
    value: JobType.DeviceMaintenance,
  },
  {
    label: JOB_TYPE_TEXT_MAP[JobType.ToolsMaintenance],
    value: JobType.ToolsMaintenance,
  },
  {
    label: JOB_TYPE_TEXT_MAP[JobType.InventoryCount],
    value: JobType.InventoryCount,
  },
];
export const MAINTENANCE_TYPE_TEXT_MAP = {
  [MaintenancePlanType.DeviceMaintenance]: '机房设备维护',
  [MaintenancePlanType.ToolsMaintenance]: '机房工器具维护',
};

export const MAINTENANCE_TYPE_OPTIONS = [
  {
    label: MAINTENANCE_TYPE_TEXT_MAP[MaintenancePlanType.DeviceMaintenance],
    value: MaintenancePlanType.DeviceMaintenance,
  },
  {
    label: MAINTENANCE_TYPE_TEXT_MAP[MaintenancePlanType.ToolsMaintenance],
    value: MaintenancePlanType.ToolsMaintenance,
  },
];

export enum PlanLayer {
  Plans = 'plan-list',
  AnnualPlans = 'annual-tasks',
  MonthlyPlans = 'monthly-tasks',
}

export enum PlanStatus {
  On = '1',
  Off = '0',
}
export const PLAN_STATUS_TEXT_MAP = {
  [PlanStatus.On]: '启用',
  [PlanStatus.Off]: '未启用 ',
};

export const PLAN_STATUS_OPTIONS = [
  {
    value: PlanStatus.On,
    label: PLAN_STATUS_TEXT_MAP[PlanStatus.On],
  },
  {
    value: PlanStatus.Off,
    label: PLAN_STATUS_TEXT_MAP[PlanStatus.Off],
  },
];

export enum PlanEffect {
  Valid = '0',
  Invalid = '1',
}

export const PLAN_EFFECT_TEXT_MAP = {
  [PlanEffect.Valid]: '有效',
  [PlanEffect.Invalid]: '无效',
};

export const PLAN_EFFECT_OPTIONS = [
  {
    value: PlanEffect.Valid,
    label: PLAN_EFFECT_TEXT_MAP[PlanEffect.Valid],
  },
  {
    value: PlanEffect.Invalid,
    label: PLAN_EFFECT_TEXT_MAP[PlanEffect.Invalid],
  },
];

export enum CyclesType {
  None = 'NONE',
  Day = 'DAY', // 日维度
  Week = 'WEEK', // 周维度
  Month = 'MONTH', // 月维度
  Year = 'YEAR', // 年维度
}

export const CYCLES_TYPE_TEXT_MAP = {
  [CyclesType.None]: '无',
  [CyclesType.Day]: '每日',
  [CyclesType.Week]: '每周',
  [CyclesType.Month]: '每月',
  [CyclesType.Year]: '每年',
};

export const CYCLES_TYPE_OPTIONS = [
  {
    value: CyclesType.None,
    label: CYCLES_TYPE_TEXT_MAP[CyclesType.None],
  },
  {
    value: CyclesType.Day,
    label: CYCLES_TYPE_TEXT_MAP[CyclesType.Day],
  },
  {
    value: CyclesType.Week,
    label: CYCLES_TYPE_TEXT_MAP[CyclesType.Week],
  },
  {
    value: CyclesType.Month,
    label: CYCLES_TYPE_TEXT_MAP[CyclesType.Month],
  },
  {
    value: CyclesType.Year,
    label: CYCLES_TYPE_TEXT_MAP[CyclesType.Year],
  },
];

// SLA单位 枚举
export enum SlaUnit {
  SECOND = 'SECOND',
  Minutes = 'MINUTES',
  Hour = 'HOUR',
  Day = 'DAY',
}
export const SLA_UNIT_TYPE_TEXT_MAP = {
  [SlaUnit.SECOND]: '秒',
  [SlaUnit.Minutes]: '分钟',
  [SlaUnit.Hour]: '小时',
  [SlaUnit.Day]: '天',
};

export const SLA_UNIT_TYPE_OPTIONS = [
  {
    value: SlaUnit.SECOND,
    label: SLA_UNIT_TYPE_TEXT_MAP[SlaUnit.SECOND],
  },
  {
    value: SlaUnit.Minutes,
    label: SLA_UNIT_TYPE_TEXT_MAP[SlaUnit.Minutes],
  },
  {
    value: SlaUnit.Hour,
    label: SLA_UNIT_TYPE_TEXT_MAP[SlaUnit.Hour],
  },
  {
    value: SlaUnit.Day,
    label: SLA_UNIT_TYPE_TEXT_MAP[SlaUnit.Day],
  },
];
export enum DistributeMode {
  Block = 'BLOCK', //  自主接手楼栋下人员
  BlockRole = 'BLOCK_ROLE', // 角色指派
  BlockUser = 'BLOCK_USER', // 单选分发范围下的非离职用户
  BlockAttGroup = 'BLOCK_ATT_GROUP', // 考勤组
  DutyGroup = 'DUTY_GROUP', // 班组
}

export const DISTRIBUTE_MODE_TEXT_MAP = {
  [DistributeMode.Block]: '自主接单',
  [DistributeMode.BlockRole]: '角色指派	',
  [DistributeMode.BlockUser]: '用户指派	',
  [DistributeMode.BlockAttGroup]: '考勤组指派',
  [DistributeMode.DutyGroup]: '班组指派',
};

export const DISTRIBUTE_MODE_OPTIONS = [
  {
    value: DistributeMode.Block,
    label: DISTRIBUTE_MODE_TEXT_MAP[DistributeMode.Block],
  },
  {
    value: DistributeMode.BlockRole,
    label: DISTRIBUTE_MODE_TEXT_MAP[DistributeMode.BlockRole],
  },
  {
    value: DistributeMode.BlockUser,
    label: DISTRIBUTE_MODE_TEXT_MAP[DistributeMode.BlockUser],
  },
  {
    value: DistributeMode.BlockAttGroup,
    label: DISTRIBUTE_MODE_TEXT_MAP[DistributeMode.BlockAttGroup],
  },
  {
    value: DistributeMode.DutyGroup,
    label: DISTRIBUTE_MODE_TEXT_MAP[DistributeMode.DutyGroup],
  },
];

//  拆分规则 枚举
export enum SplitorType {
  DeviceType = 'DEVICE_TYPE',
  Space = 'SPACE',
  Floor = 'FLOOR',
  Room = 'ROOM',
  Risk = 'RISK_TYPE',
  DutyGroup = 'DUTY_GROUP',
}

export const SPLTOR_TYPE_TEXT_MAP = {
  [SplitorType.Space]: '按照空间拆分任务单',
  [SplitorType.DeviceType]: '按照设备分类拆分任务单',
  [SplitorType.Floor]: '按照楼层拆分任务单',
  [SplitorType.Room]: '按照包间拆分任务单',
};

export const SPLTOR_TYPE_OPTIONS = [
  {
    value: SplitorType.Space,
    label: SPLTOR_TYPE_TEXT_MAP[SplitorType.Space],
  },
  {
    value: SplitorType.DeviceType,
    label: SPLTOR_TYPE_TEXT_MAP[SplitorType.DeviceType],
  },
];

export const SOLIT_ROOM_FLOOR_OPTIONS = [
  {
    value: SplitorType.Floor,
    label: SPLTOR_TYPE_TEXT_MAP[SplitorType.Floor],
  },
  {
    value: SplitorType.Room,
    label: SPLTOR_TYPE_TEXT_MAP[SplitorType.Room],
  },
];

// 设备分类 枚举
export enum ThreeDeviceType {
  FirstCategory = 'FIRST_CATEGORY',
  SecondCategory = 'SECOND_CATEGORY',
  ThirdCategory = 'THIRD_CATEGORY',
}
export const THREE_DEVICE_TYPE_TEXT_MAP = {
  [ThreeDeviceType.FirstCategory]: '一级分类',
  [ThreeDeviceType.SecondCategory]: '二级分类',
  [ThreeDeviceType.ThirdCategory]: '三级分类',
};

export const THREE_DEVICE_TYPE_OPTIONS = [
  {
    value: ThreeDeviceType.FirstCategory,
    label: THREE_DEVICE_TYPE_TEXT_MAP[ThreeDeviceType.FirstCategory],
  },
  {
    value: ThreeDeviceType.SecondCategory,
    label: THREE_DEVICE_TYPE_TEXT_MAP[ThreeDeviceType.SecondCategory],
  },
  {
    value: ThreeDeviceType.ThirdCategory,
    label: THREE_DEVICE_TYPE_TEXT_MAP[ThreeDeviceType.ThirdCategory],
  },
];

// 星期 枚举

export enum WeekType {
  Fir = '1',
  Sec = '2',
  Thi = '3',
  Fou = '4',
  Fiv = '5',
  Six = '6',
  Sev = '7',
}

export const WEEK_DAY_TEXT_MAP = {
  [WeekType.Fir]: '周一',
  [WeekType.Sec]: '周二',
  [WeekType.Thi]: '周三',
  [WeekType.Fou]: '周四',
  [WeekType.Fiv]: '周五',
  [WeekType.Six]: '周六',
  [WeekType.Sev]: '周日',
};

// 月，日
function getDays() {
  let data: { label: string; value: string }[] = [];
  for (let i = 1; i <= 31; i++) {
    const tmp = {
      value: i.toString(),
      label: i.toString(),
    };
    data = [...data, tmp];
  }
  return data;
}
export const MONTH_DAY_OPYIONS = getDays();

export const WEEK_DAY_OPYIONS = [
  {
    value: WeekType.Fir,
    label: WEEK_DAY_TEXT_MAP[WeekType.Fir],
  },
  {
    value: WeekType.Sec,
    label: WEEK_DAY_TEXT_MAP[WeekType.Sec],
  },
  {
    value: WeekType.Thi,
    label: WEEK_DAY_TEXT_MAP[WeekType.Thi],
  },
  {
    value: WeekType.Fou,
    label: WEEK_DAY_TEXT_MAP[WeekType.Fou],
  },
  {
    value: WeekType.Fiv,
    label: WEEK_DAY_TEXT_MAP[WeekType.Fiv],
  },
  {
    value: WeekType.Six,
    label: WEEK_DAY_TEXT_MAP[WeekType.Six],
  },
  {
    value: WeekType.Sev,
    label: WEEK_DAY_TEXT_MAP[WeekType.Sev],
  },
];

export const NUMBER_OPYIONS = [
  {
    value: 1,
    label: '第一个',
  },
  {
    value: 2,
    label: '第二个',
  },
  {
    value: 3,
    label: '第三个',
  },
  {
    value: 4,
    label: '第四个',
  },
];
export enum MonthlyWorkDayType {
  Sunday = 'SUNDAY',
  Monday = 'MONDAY',
  Tuesday = 'TUESDAY',
  Wednesday = 'WEDNESDAY',
  Thursday = 'THURSDAY',
  Friday = 'FRIDAY',
  Saturday = 'SATURDAY',
  NaturalDay = 'NATURAL_DAY',
  WorkDay = 'WORK_DAY',
  Weekend = 'WEEKEND',
}

export const DAY_WORK_WEEK_TEXT_MAP = {
  [MonthlyWorkDayType.Sunday]: '星期日',
  [MonthlyWorkDayType.Monday]: '星期一',
  [MonthlyWorkDayType.Tuesday]: '星期二',
  [MonthlyWorkDayType.Wednesday]: '星期三',
  [MonthlyWorkDayType.Thursday]: '星期四',
  [MonthlyWorkDayType.Friday]: '星期五',
  [MonthlyWorkDayType.Saturday]: '星期六',
  [MonthlyWorkDayType.NaturalDay]: '自然日',
  [MonthlyWorkDayType.WorkDay]: '工作日',
  [MonthlyWorkDayType.Weekend]: '周末',
};

export const DAY_WORK_WEEK_OPYIONS = [
  {
    value: MonthlyWorkDayType.Sunday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Sunday],
  },
  {
    value: MonthlyWorkDayType.Monday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Monday],
  },
  {
    value: MonthlyWorkDayType.Tuesday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Tuesday],
  },
  {
    value: MonthlyWorkDayType.Wednesday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Wednesday],
  },
  {
    value: MonthlyWorkDayType.Thursday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Thursday],
  },
  {
    value: MonthlyWorkDayType.Friday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Friday],
  },
  {
    value: MonthlyWorkDayType.Saturday,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Saturday],
  },
  {
    value: MonthlyWorkDayType.NaturalDay,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.NaturalDay],
  },
  {
    value: MonthlyWorkDayType.WorkDay,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.WorkDay],
  },
  {
    value: MonthlyWorkDayType.Weekend,
    label: DAY_WORK_WEEK_TEXT_MAP[MonthlyWorkDayType.Weekend],
  },
];
function getMonthDays() {
  let data: { value: string; label: string; children: { value: string; label: string }[] }[] = [];
  for (let i = 1; i <= 12; i++) {
    let m = String(i);
    if (i < 10) {
      m = '0' + i;
    }
    const tmp = {
      value: m,
      label: `${i}月`,
      disabled: true,
      children: getDaysInMonth(i),
    };
    data = [...data, tmp];
  }
  return data;
}
export const MONTH_DAY_IN_YEAR_OPYIONS = getMonthDays();

function getDaysInMonth(month: number) {
  let data: { label: string; value: string }[] = [];
  let ln = 31;
  const minMonth = [4, 6, 9, 11];
  const maxMonth = [1, 3, 5, 7, 8, 10, 12];
  if (month === 2) {
    ln = 28;
  }
  if (minMonth.includes(month)) {
    ln = 30;
  }
  if (maxMonth.includes(month)) {
    ln = 31;
  }
  for (let i = 1; i <= ln; i++) {
    let m = String(month);
    let d = String(i);
    if (month < 10) {
      m = '0' + m;
    }
    if (i < 10) {
      d = '0' + d;
    }
    const tmp = {
      value: `${m}${d}`,
      label: `${month}月${i}号`,
    };
    data = [...data, tmp];
  }
  return data;
}

function getMonths() {
  let data: { label: string; value: string }[] = [];
  for (let i = 1; i <= 12; i++) {
    const tmp = {
      value: i.toString(),
      label: `${i}月`,
    };
    data = [...data, tmp];
  }
  return data;
}

export enum ConfigRangeType {
  Block = 'BLOCK',
  All = 'ALL',
}

export const CONFIG_RANGE_TEXT = {
  [ConfigRangeType.Block]: '属地',
  [ConfigRangeType.All]: '通用',
};

export const CONFIG_RANGE_KEY_MAP = [
  {
    label: CONFIG_RANGE_TEXT[ConfigRangeType.Block],
    value: ConfigRangeType.Block,
  },
  { label: CONFIG_RANGE_TEXT[ConfigRangeType.All], value: ConfigRangeType.All },
];

export enum TaskEffectStatus {
  Effected = 'effected',
  Ineffective = 'waited',
}

export const TASK_EFFECT_STATUS_TEXT = {
  [TaskEffectStatus.Ineffective]: '待生效',
  [TaskEffectStatus.Effected]: '已生效',
};

export const TASK_EFFECT_STATUS_OPTIONS = [
  {
    label: TASK_EFFECT_STATUS_TEXT[TaskEffectStatus.Ineffective],
    value: TaskEffectStatus.Ineffective,
  },
  { label: TASK_EFFECT_STATUS_TEXT[TaskEffectStatus.Effected], value: TaskEffectStatus.Effected },
];

export enum ExcelStatementSubjobType {
  ExcelReportPerson = 'EXCEL_REPORT_PERSON',
  ExcelReportCommon = 'EXCEL_REPORT_COMMON',
}

export const EXCEL_STATEMENT_SUBJOB_TYPE_TEXT = {
  [ExcelStatementSubjobType.ExcelReportCommon]: '公共',
  [ExcelStatementSubjobType.ExcelReportPerson]: '个人',
};

export enum SchModeType {
  Online = 'ONLINE', //在线培训
  Offline = 'OFFLINE', //线下培训
}

export const SCH_MODE_TYPE_TEXT = {
  [SchModeType.Online]: '在线培训',
  [SchModeType.Offline]: '线下培训',
};
