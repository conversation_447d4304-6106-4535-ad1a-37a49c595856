import type { LocaleCode } from '@teammc/react-intl';
import cloneDeep from 'lodash.clonedeep';
import get from 'lodash.get';
import moment from 'moment';
import shortid from 'shortid';

import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { SlaUnit } from '@manyun/ticket.model.task';
import { AccessCardTaskSubType } from '@manyun/ticket.model.ticket';

// import type { ApiQ } from '@manyun/ticket.service.create-access-card';
import type {
  ApplyUserInfo,
  BackendEntranceGuardAuth,
  ChangeAuthType,
} from './backend-entrance-guard-auth';
import { type EntranceGuardAuthLocales, getEntranceGuardAuthLocales } from './locales';

export type ApiQObject = {
  idcTag: string;
  authType:
    | AccessCardTaskSubType.CardApply
    | AccessCardTaskSubType.CardChange
    | AccessCardTaskSubType.CardOff
    | AccessCardTaskSubType.CardReplace;
  sla: number;
  slaUnit: SlaUnit;
  fileInfoList: BackendMcUploadFile[] | null;
  applyAuthContextList: ApplyAuthContextList[];
  description: string | null;
};

export type ApplyAuthInfo = {
  id: string;
  mergeRowsKey: string;
  apply: { value: number | string | null; label: string; id: number | null; type?: string | null };
  dept: string;
  company: string;
  phone: string;
  email: string;
  cardType: string;
  cardNo: string;
  entranceCardNo: string | null;
  effectTime: string | null;
  blockGuid: string;
  authId: number;
};

export type ChangeAuthInfo = {
  id: string;
  mergeRowsKey: string;
  applyId: number;
  applyName: string;
  entranceCardNo: string;
  apply: {
    value: number | string | null;
    label: string;
    id: number | null;
    cardType: string;
    cardNo: string;
  };
  cardType: string;
  cardNo: string;
  originAuthInfos: { blockGuid: string; authIdList: number[]; effectTime?: number }[];
  blockGuid: string;
  authId: number;
  originAuthId: number /**变更前的权限组 */;
  changeType: ChangeAuthType;
  effectTime?: string | null;
  oldEffectTime?: string | null;
};

export type DeleteAuthInfo = {
  applyId: number;
  applyName: string;
  entranceCardNo: string;
  cardType: string;
  cardNo: string;
  originAuthInfos: { blockGuid: string; authId: number }[];
};

export type ReplaceAuthInfo = {
  id: string;
  mergeRowsKey: string;
  apply: { value: number | string | null; label: string; id: number | null; type?: string | null };
  dept: string;
  company: string;
  phone: string;
  email: string;
  cardType: string;
  cardNo: string;
  entranceCardNo: string | null;
  oldEntranceCardNo: string | null;
  effectTime: string | null;
  blockGuid: string;
  authId: number;
  originAuthInfos: {
    blockTag: string;
    blockGuid: string;
    authIdList: number[];
    effectTime?: number;
  }[];
};

export type EntranceGuardAuthJSON = {
  taskNo: string;
  taskType: string;
  taskSubType: AccessCardTaskSubType;
  idcTag: string /**单据ID */;
  blockGuid: string | undefined;
  blockTag: string | undefined;
  roomGuid: string | undefined;
  description: string | undefined;
  workFlowId: string | null;
  applyAuths: ApplyAuthInfo[] | undefined /**申请时 */;
  changeAuthInfo: ChangeAuthInfo[] | undefined /**变更时 */;
  deleteAuthInfos: DeleteAuthInfo[] | undefined;
  replaceAuthInfos: ReplaceAuthInfo[] | undefined;
  fileInfoList: McUploadFile[] | undefined;
  sla: { value: number; unit: SlaUnit };
  effectTime: string | null;
};

export type ApplyAuthContextList = ApplyUserInfoJson & {
  originAuthInfoList?: AuthInfo[];
  authInfoList: AuthInfo[];
};
export type AuthInfo = {
  blockTag: string;
  authIdList: number[];
  effectTime?: number;
};
export type ApplyUserInfoJson = {
  id?: string;
  applyId?: number | string | null;
  applyName?: string;
  applyRole?: string;
  dept?: string;
  company?: string;
  phone?: string;
  email?: string;
  cardNo?: string;
  blockTagList?: string[];
  cardType?: string;
  entranceCardNo?: string | null;
  oldEntranceCardNo?: string;
};

export class EntranceGuardAuth {
  static fromApiObject(object: BackendEntranceGuardAuth) {
    const copy = cloneDeep(object);

    return new EntranceGuardAuth(
      copy.taskNo,
      copy.taskType,
      copy.taskSubType,
      copy.idcTag,
      { value: copy.taskSla, unit: copy.unit },
      copy.blockGuid ?? undefined,
      copy.blockTag ?? undefined,
      copy.roomGuid ?? undefined,
      copy.description ?? undefined,
      copy.workFlowId,
      copy.taskSubType === AccessCardTaskSubType.CardApply
        ? getApplyUserInfos(copy.applyUserInfoList, copy.idcTag)
        : undefined,
      copy.taskSubType === AccessCardTaskSubType.CardChange
        ? getChangeAuthInfos(copy.applyUserInfoList, copy.idcTag)
        : undefined,
      copy.taskSubType === AccessCardTaskSubType.CardOff
        ? getDeleteAuthInfo(copy.applyUserInfoList, copy.idcTag)
        : undefined,
      copy.taskSubType === AccessCardTaskSubType.CardReplace
        ? getReplaceAuthInfo(copy.applyUserInfoList, copy.idcTag)
        : undefined,
      copy.fileInfoList,
      copy.effectTime ?? undefined
    );
  }

  static fromJSON(json: EntranceGuardAuthJSON) {
    const copy = cloneDeep(json);
    return new EntranceGuardAuth(
      copy.taskNo,
      copy.taskType,
      copy.taskSubType,
      copy.idcTag,
      copy.sla ?? { value: 72, unit: SlaUnit.Hour },
      copy.blockGuid ?? undefined,
      copy.blockTag ?? undefined,
      copy.roomGuid ?? undefined,
      copy.description ?? undefined,
      copy.workFlowId,
      copy.applyAuths ?? undefined,
      copy.changeAuthInfo ?? undefined,
      copy.deleteAuthInfos ?? undefined,
      copy.replaceAuthInfos ?? undefined,
      copy.fileInfoList ?? undefined,
      copy.effectTime ?? undefined
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';

  private _locales: EntranceGuardAuthLocales;

  constructor(
    public taskNo: string,
    public taskType: string,
    public taskSubType: AccessCardTaskSubType,
    public idcTag: string,
    public sla: { value: number; unit: SlaUnit },
    public blockGuid: string | undefined,
    public blockTag: string | undefined,
    public roomGuid: string | undefined,
    public description: string | undefined,
    public workFlowId: string | null,
    public applyAuths: ApplyAuthInfo[] | undefined,
    public changeAuthInfo: ChangeAuthInfo[] | undefined,
    public deleteAuthInfos: DeleteAuthInfo[] | undefined,
    public replaceAuthInfos: ReplaceAuthInfo[] | undefined,
    public fileInfoList: McUploadFile[] | undefined,
    public effectTime: string | undefined
  ) {
    this._locales = getEntranceGuardAuthLocales(this._localeCode);
  }

  public set locales(locales: EntranceGuardAuthLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): ApiQObject {
    return cloneDeep({
      idcTag: this.idcTag,
      authType: this.taskSubType,
      blockTag: this.blockTag,
      blockGuid: this.blockGuid,
      sla: this.sla.value,
      slaUnit: this.sla.unit,
      applyAuthContextList: getToApiApplyAuthContent(
        this.taskSubType,
        this.applyAuths ?? [],
        this.changeAuthInfo ?? [],
        this.deleteAuthInfos ?? [],
        this.replaceAuthInfos ?? []
      ),
      description: this.description ?? null,
      fileInfoList: (this.fileInfoList ?? []).map(file =>
        McUploadFile.fromJSON(file).toApiObject()
      ),
      effectTime: this.effectTime,
    });
  }

  public toJSON(): EntranceGuardAuthJSON {
    return cloneDeep({
      taskNo: this.taskNo,
      taskType: this.taskType,
      taskSubType: this.taskSubType,
      idcTag: this.idcTag,
      blockGuid: this.blockGuid,
      blockTag: this.blockTag,
      roomGuid: this.roomGuid,
      description: this.description,
      workFlowId: this.workFlowId,
      applyAuths: this.applyAuths,
      changeAuthInfo: this.changeAuthInfo,
      deleteAuthInfos: this.deleteAuthInfos,
      replaceAuthInfos: this.replaceAuthInfos,
      fileInfoList: this.fileInfoList,
      sla: this.sla,
      effectTime: this.effectTime ?? null,
    });
  }
}

function getApplyUserInfos(applyAuthInfos: ApplyUserInfo[], idcTag: string): ApplyAuthInfo[] {
  const list: ApplyAuthInfo[] = [];
  applyAuthInfos.forEach(applyAuthInfo => {
    const mergeRowsKey = shortid();
    applyAuthInfo.authInfoList.forEach(authItem => {
      const id = shortid();
      list.push({
        id,
        mergeRowsKey,
        apply: {
          value: applyAuthInfo.applyId || applyAuthInfo.applyName,
          label: applyAuthInfo.applyName,
          id: applyAuthInfo.applyId,
          type: applyAuthInfo.applyId ? null : 'external',
        },
        dept: applyAuthInfo.dept ?? '',
        company: applyAuthInfo.company ?? '',
        phone: applyAuthInfo.phone ?? '',
        email: applyAuthInfo.email ?? '',
        cardType: applyAuthInfo.cardType ?? '',
        cardNo: applyAuthInfo.cardNo ?? '',
        entranceCardNo: applyAuthInfo.entranceCardNo,
        blockGuid: `${idcTag}.${authItem.blockTag}`,
        authId: authItem.authIdList[0],
        effectTime: authItem.effectTime!,
      });
    });
  });
  return list;
}

function getChangeAuthInfos(
  applyAuthInfos: ApplyUserInfo[],
  idcTag: string
): ChangeAuthInfo[] | undefined {
  const list: ChangeAuthInfo[] = [];
  applyAuthInfos.forEach(
    ({
      changeAuthInfoList,
      applyId,
      applyName,
      cardNo,
      cardType,
      entranceCardNo,
      originAuthInfoList,
    }) => {
      const mergeRowsKey = shortid();
      changeAuthInfoList.forEach(authItem => {
        const id = shortid();
        list.push({
          id,
          mergeRowsKey,
          applyId,
          applyName,
          apply: { label: applyName, value: applyId || applyName, id: applyId, cardNo, cardType },
          cardNo,
          cardType,
          entranceCardNo: entranceCardNo ?? '',
          originAuthInfos: originAuthInfoList.map(item => ({
            blockGuid: `${idcTag}.${item.blockTag}`,
            blockTag: item.blockTag,
            authIdList: item.authIdList,
            effectTime: item.effectTime,
          })),
          blockGuid: `${idcTag}.${authItem.blockTag}`,
          authId: authItem.authIdList[0],
          originAuthId: authItem.originAuthIdList?.[0],
          changeType: authItem.changeType,
          effectTime: authItem.effectTime,
          oldEffectTime: authItem.oldEffectTime,
        });
      });
    }
  );
  return list;
}

function getDeleteAuthInfo(
  applyAuthInfos: ApplyUserInfo[],
  idcTag: string
): DeleteAuthInfo[] | undefined {
  const list: DeleteAuthInfo[] = applyAuthInfos.map(item => ({
    applyId: item.applyId,
    applyName: item.applyName,
    entranceCardNo: item.entranceCardNo ?? '',
    originAuthInfos: item.originAuthInfoList.map(authItem => ({
      blockGuid: `${idcTag}.${authItem.blockTag}`,
      authId: authItem.authIdList[0],
    })),
    cardType: item.cardType,
    cardNo: item.cardNo,
  }));
  return list;
}

function getReplaceAuthInfo(
  applyAuthInfos: ApplyUserInfo[],
  idcTag: string
): ReplaceAuthInfo[] | undefined {
  const list: ReplaceAuthInfo[] = [];
  applyAuthInfos.forEach(applyAuthInfo => {
    const mergeRowsKey = shortid();
    applyAuthInfo.authInfoList.forEach(authItem => {
      const id = shortid();
      list.push({
        id,
        mergeRowsKey,
        apply: {
          value: applyAuthInfo.applyId || applyAuthInfo.applyName,
          label: applyAuthInfo.applyName,
          id: applyAuthInfo.applyId,
          type: applyAuthInfo.applyId ? null : 'external',
        },
        dept: applyAuthInfo.dept ?? '',
        company: applyAuthInfo.company ?? '',
        phone: applyAuthInfo.phone ?? '',
        email: applyAuthInfo.email ?? '',
        cardType: applyAuthInfo.cardType ?? '',
        cardNo: applyAuthInfo.cardNo ?? '',
        entranceCardNo: applyAuthInfo.entranceCardNo,
        oldEntranceCardNo: applyAuthInfo.oldEntranceCardNo,
        blockGuid: `${idcTag}.${authItem.blockTag}`,
        authId: authItem.authIdList[0],
        effectTime: authItem.effectTime ?? null,
        originAuthInfos: (applyAuthInfo?.originAuthInfoList ?? []).map(item => ({
          blockGuid: `${idcTag}.${item.blockTag}`,
          blockTag: item.blockTag,
          authIdList: item.authIdList,
          effectTime: item.effectTime,
        })),
      });
    });
  });
  return list;
}

function getToApiApplyAuthContent(
  authType: AccessCardTaskSubType,
  applyAuthInfos: ApplyAuthInfo[],
  changeAuthInfo: ChangeAuthInfo[],
  deleteAuthInfos: DeleteAuthInfo[],
  replaceAuthInfos: ReplaceAuthInfo[]
): ApplyAuthContextList[] {
  let list: ApplyAuthContextList[] = [];
  if (authType === AccessCardTaskSubType.CardApply) {
    list = applyAuthInfos.reduce((listAuth: ApplyAuthContextList[], key) => {
      const inx = listAuth.findIndex(item =>
        item.applyId ? item.applyId === key.apply.value : item.applyName === key.apply.label
      );
      if (inx > -1) {
        listAuth[inx] = {
          ...listAuth[inx],
          authInfoList: [
            ...listAuth[inx]?.authInfoList,
            {
              blockTag: transformBlockGuidToBlockTag(key.blockGuid),
              authIdList: [key.authId],
            },
          ],
        };
      } else {
        listAuth.push({
          ...key,
          applyId: key.apply.id ?? undefined,
          applyName: key.apply.label,
          dept: key.dept,
          company: key.company,
          phone: key.phone,
          email: key.email,
          cardNo: key.cardNo,
          cardType: key.cardType,
          entranceCardNo: undefined,
          authInfoList: [
            {
              blockTag: transformBlockGuidToBlockTag(key.blockGuid),
              authIdList: [key.authId],
            },
          ],
        });
      }
      return listAuth;
    }, []);
  }

  if (authType === AccessCardTaskSubType.CardChange && changeAuthInfo) {
    list = changeAuthInfo.reduce((listAuth: ApplyAuthContextList[], key) => {
      const inx = listAuth.findIndex((item: ApplyAuthContextList & { key?: string }) => {
        if (item.key) {
          return item.key === get(key, ['apply', 'key']) || item.key === get(key, ['mergeRowsKey']);
        }
        return item.applyId
          ? item.applyId === get(key, ['apply', 'value'])
          : item.applyName === get(key, ['apply', 'label']);
      });
      if (inx > -1) {
        listAuth[inx] = {
          ...listAuth[inx],
          authInfoList: [
            ...(listAuth[inx]?.authInfoList ?? []),
            {
              blockTag: transformBlockGuidToBlockTag(key.blockGuid),
              authIdList: [key.authId],
              effectTime: key?.effectTime ? moment(key.effectTime).endOf('D').valueOf() : undefined,
            },
          ],
        };
      } else {
        listAuth.push({
          // @ts-ignore
          key: get(key, ['apply', 'key']) || get(key, ['mergeRowsKey']),
          applyId: get(key, ['apply', 'id'], null),
          applyName: get(key, ['apply', 'label']),
          entranceCardNo: get(key, 'entranceCardNo'),
          cardNo: get(key, ['cardNo']),
          cardType: get(key, ['cardType']),
          originAuthInfoList: get(key, ['originAuthInfos'], []).map(item => ({
            blockTag: transformBlockGuidToBlockTag(get(item, 'blockGuid')),
            authIdList: get(item, 'authIdList') ?? [],
            effectTime: item?.effectTime ? moment(item.effectTime).endOf('D').valueOf() : undefined,
          })),
          authInfoList: [
            {
              blockTag: transformBlockGuidToBlockTag(key.blockGuid),
              authIdList: [key.authId],
              effectTime: key?.effectTime ? moment(key.effectTime).endOf('D').valueOf() : undefined,
            },
          ],
        });
      }
      return listAuth;
    }, []);
  }
  if (authType === AccessCardTaskSubType.CardReplace && replaceAuthInfos) {
    list = replaceAuthInfos.reduce((listAuth: ApplyAuthContextList[], key) => {
      const inx = listAuth.findIndex((item: ApplyAuthContextList & { key?: string }) => {
        if (item.key) {
          return item.key === get(key, ['apply', 'key']) || item.key === get(key, ['mergeRowsKey']);
        }
        return item.applyId
          ? item.applyId === get(key, ['apply', 'value'])
          : item.applyName === get(key, ['apply', 'label']);
      });
      if (inx > -1) {
        listAuth[inx] = {
          ...listAuth[inx],
          authInfoList: [
            ...(listAuth[inx]?.authInfoList ?? []),
            {
              blockTag: transformBlockGuidToBlockTag(key.blockGuid),
              authIdList: [key.authId],
              effectTime: key?.effectTime ? moment(key.effectTime).endOf('D').valueOf() : undefined,
            },
          ],
        };
      } else {
        listAuth.push({
          // @ts-ignore
          key: get(key, ['apply', 'key']) || get(key, ['mergeRowsKey']),
          applyId: get(key, ['apply', 'id'], null),
          applyName: get(key, ['apply', 'label']),
          entranceCardNo: get(key, 'entranceCardNo'),
          oldEntranceCardNo: get(key, 'oldEntranceCardNo') ?? undefined,
          cardNo: get(key, ['cardNo']),
          cardType: get(key, ['cardType']),
          originAuthInfoList: get(key, ['originAuthInfos'], []).map(
            (item: {
              blockGuid: string;
              blockTag: string;
              authIdList: number[];
              effectTime?: number;
            }) => ({
              blockTag: item.blockTag,
              authIdList: item.authIdList,
              effectTime: item?.effectTime
                ? moment(item.effectTime).endOf('D').valueOf()
                : undefined,
            })
          ),
          authInfoList: [
            {
              blockTag: transformBlockGuidToBlockTag(key.blockGuid),
              authIdList: [key.authId],
              effectTime: key?.effectTime ? moment(key.effectTime).endOf('D').valueOf() : undefined,
            },
          ],
        });
      }
      return listAuth;
    }, []);
  }
  if (authType === AccessCardTaskSubType.CardOff) {
    list = deleteAuthInfos.map(item => ({
      applyId: item.applyId,
      applyName: item.applyName,
      entranceCardNo: item.entranceCardNo,
      cardNo: item.cardNo,
      cardType: item.cardType,
      originAuthInfoList: item.originAuthInfos.map(item => ({
        blockTag: transformBlockGuidToBlockTag(item.blockGuid),
        authIdList: [item.authId],
      })),
      authInfoList: [],
    }));
  }
  return list;
}

function transformBlockGuidToBlockTag(blockGuid: string): string {
  const [, blockTag] = blockGuid.split('.');
  return blockTag;
}
