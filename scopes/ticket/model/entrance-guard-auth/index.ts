export { EntranceGuardAuth } from './entrance-guard-auth';
export type {
  EntranceGuardAuthJSON,
  DeleteAuthInfo,
  AfterChangeAuthInfo,
  ChangeAuthInfo,
} from './entrance-guard-auth';
export type {
  BackendEntranceGuardAuth,
  ApplyUserInfo,
  ChangeAuthType,
} from './backend-entrance-guard-auth';
export { getEntranceGuardAuthLocales } from './locales';
export type { EntranceGuardAuthLocales } from './locales';
