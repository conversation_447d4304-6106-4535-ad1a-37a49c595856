import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { SlaUnit } from '@manyun/ticket.model.task';
import type { AccessCardTaskSubType } from '@manyun/ticket.model.ticket';

export type ChangeAuthType = 'ADD' | 'DELETE' | 'CHANGE' | 'UN_CHANG';

export type BackendEntranceGuardAuth = {
  taskNo: string;
  taskType: string;
  taskSubType: AccessCardTaskSubType;
  idcTag: string /**单据ID */;
  blockGuid: string;
  blockTag: string | null;
  roomGuid: string;
  description: string;
  workFlowId: string;
  applyUserInfoList: ApplyUserInfo[];
  fileInfoList: McUploadFile[];
  taskSla: number;
  unit: SlaUnit;
  effectTime: string | null;
};
export type ChangeAuthInfo = {
  blockTag: string;
  authIdList: number[];
  originAuthIdList: number[];
  changeType: ChangeAuthType;
  effectTime?: string | null;
  oldEffectTime?: string | null;
};
export type ApplyUserInfo = {
  applyId: number;
  applyName: string;
  dept: string | null;
  company: string | null;
  phone: string | null;
  email: string | null;
  cardType: string;
  cardNo: string;
  entranceCardNo: string | null;
  oldEntranceCardNo: string | null;
  authInfoList: { blockTag: string; authIdList: number[]; effectTime?: string }[];
  originAuthInfoList: { blockTag: string; authIdList: number[]; effectTime?: number }[];
  changeAuthInfoList: ChangeAuthInfo[];
  effectTime: string | null;
};

export type ProgressivePueContents = {
  leftValue: number | null;
  leftSymbol: Symbol | null;
  rightValue: number | null;
  rightSymbol: Symbol | null;
  guaranteed: boolean | null;
  guaranteedPue: number | null;
};
