import type {
  BackendEventTierLevel,
  EventProcessPhaseConfigCondition,
  EventSparePartChange,
  EventSpecificProcessPhaseStatus,
  EventSpecificProcessStatus,
} from '../backend-event.type';

export type EventLocales = {
  title: string;
  content: string;
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  modifiedAt: string;
  status: {
    __self: string;
    CREATED: string;
    PROCESSING: string;
    RELIEVED: string;
    RESOLVED: string;
    AUDITED: string;
    AUDITING: string;
    CLOSED: string;
  };

  upgradeType: {
    __self: string;
    OFFLINE: string;
    ONLINE: string;
  };
  tierLevel: Record<'__self' | BackendEventTierLevel, string>;
  specificProcessStatus: Record<'__self' | EventSpecificProcessStatus, string>;
  sparePartChange: Record<'__self' | EventSparePartChange, string>;
  specificProcessPhaseStatus: Record<'__self' | EventSpecificProcessPhaseStatus, string>;
  specificProcessPhaseConfigCondition: Record<'__self' | EventProcessPhaseConfigCondition, string>;
};
