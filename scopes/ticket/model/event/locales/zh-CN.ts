import type { EventLocales } from './type';

export const zhCN: EventLocales = {
  title: '标题',
  content: '内容',
  createdBy: {
    __self: '创建人',
    id: '创建人 ID',
    name: '创建人姓名',
  },
  createdAt: '创建于',
  modifiedAt: '修改于',
  status: {
    __self: '事件状态',
    CREATED: '已创建',
    PROCESSING: '处理中',
    RELIEVED: '已缓解',
    RESOLVED: '已解决',
    AUDITED: '已评审',
    AUDITING: '评审中',
    CLOSED: '已关闭',
  },
  upgradeType: {
    __self: '升级方式',
    OFFLINE: '线下',
    ONLINE: '线上',
  },
  tierLevel: {
    __self: 'Tier阶段',
    0: 'Tier1升级',
    1: 'Tier1响应',
    2: 'Tier2升级',
    3: 'Tier2响应',
    4: 'Tier3升级',
    5: 'Tier3响应',
  },
  specificProcessStatus: {
    __self: '事件处理流程',
    YG_CREATED: '创建',
    YG_EMERGENCY: '事件应急',
    YG_REPAIR: '故障修复',
    YG_RECOVERY: '系统恢复',
    YG_FINISHING: '事件结单',
    YG_REVIEW: '复盘',
    YG_AUDIT: '评审',
    YG_CLOSE: '关闭',
  },
  sparePartChange: {
    __self: '备件更换',
    NONE: '无需更换备件',
    DEVICE: '更换库中设备',
    STUFF: '更换库中耗材',
    OTHER: '自定义',
  },
  specificProcessPhaseStatus: {
    __self: '事件处理流程阶段状态',
    INPROGRESS: '进行中',
    COMPLETED: '已完成',
    SKIPPED: '已跳过',
    PENDING: '待开始',
  },
  specificProcessPhaseConfigCondition: {
    __self: '阶段完成条件',
    NONE: '无限制',
    PHASE_MORE: '当前阶段至少有一条处理记录',
    EVENT_MORE: '事件单至少有一条处理记录',
  },
};

export default zhCN;
