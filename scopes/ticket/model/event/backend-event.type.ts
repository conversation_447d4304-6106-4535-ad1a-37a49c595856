import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

export enum FaultTargetType {
  Device = 'DEVICE',
  Room = 'ROOM',
  Other = 'OTHER',
}
// YG_CREATED(已创建)、YG_EMERGENCY(事件应急)、YG_REPAIR(故障修复)、YG_RECOVERY(系统恢复)、YG_REVIEW(复盘)、YG_AUDIT(评审)、YG_CLOSE(关闭)
export enum EventSpecificProcessStatus {
  Init = 'YG_CREATED',
  Emergency = 'YG_EMERGENCY',
  Fix = 'YG_REPAIR',
  Recovery = 'YG_RECOVERY',
  Finished = 'YG_FINISHING',
  Debrief = 'YG_REVIEW',
  Review = 'YG_AUDIT',
  Close = 'YG_CLOSE',
}
export const EventSpecificProcessStatusNumberValueMap = {
  [EventSpecificProcessStatus.Init]: 1,
  [EventSpecificProcessStatus.Emergency]: 2,
  [EventSpecificProcessStatus.Fix]: 3,
  [EventSpecificProcessStatus.Recovery]: 4,
  [EventSpecificProcessStatus.Finished]: 5,
  [EventSpecificProcessStatus.Debrief]: 6,
  [EventSpecificProcessStatus.Review]: 7,
  [EventSpecificProcessStatus.Close]: 8,
};

export enum EventSpecificProcessPhaseStatus {
  Inprogress = 'INPROGRESS',
  Completed = 'COMPLETED',
  Skipped = 'SKIPPED',
  Pending = 'PENDING',
}

export enum EventProcessPhaseConfigCondition {
  None = 'NONE',
  PhaseMore = 'PHASE_MORE',
  EventMore = 'EVENT_MORE',
}
export enum EventProcessPhaseOperator {
  Owner = 'OWNER',
  UserUnderBlock = 'BLOCK',
}

export enum EventSparePartChange {
  None = 'NONE',
  ChangeDevice = 'DEVICE',
  ChangeSpare = 'STUFF',
  Custom = 'OTHER',
}

export const EventSpecificProcessStatusFieldName = {
  [EventSpecificProcessStatus.Init]: undefined,
  [EventSpecificProcessStatus.Emergency]: 'emergency',
  [EventSpecificProcessStatus.Fix]: 'repair',
  [EventSpecificProcessStatus.Recovery]: 'recovery',
  [EventSpecificProcessStatus.Debrief]: undefined,
  [EventSpecificProcessStatus.Review]: undefined,
  [EventSpecificProcessStatus.Close]: undefined,
};

export type EventInfluence = {
  influenceScope?: { influenceType: string; influenceGuid: string }[] | null;
  gridInfluence?: string;
};
export const FAULT_TARGET_TYPE_TEXT = {
  [FaultTargetType.Device]: '设备',
  [FaultTargetType.Room]: '包间',
  [FaultTargetType.Other]: '其他',
};

export const FAULT_TARGET_TYPE_KEY_MAP = [
  {
    value: FaultTargetType.Device,
    label: '设备',
  },
  {
    value: FaultTargetType.Room,
    label: '包间',
  },

  { value: FaultTargetType.Other, label: '其他' },
];
export type EventStatus = {
  code: BackendEventStatusCode;
  desc: string;
  status: string;
  statusCode: string;
};
export type EventDevice = {
  idcTag: string;
  blockTag: string;
  roomTag: string;
  deviceType: string;
  deviceTag: string;
  deviceName: string;
  deviceGuid: string;
};

// todo @lyd，后续对 BackendEvent 中的字段进行model 角度的重新思考
export type BackendEvent = {
  id: number;
  idcTag: string;
  blockTag: string;
  blockGuidList?: string[];
  eventLevel: string;
  eventLevelName: string;
  eventSource: string;
  eventSourceName: string;
  /** 事件类型，一级 */
  topCategory: string;
  topCategoryName: string;
  /** 事件类型，二级 */
  secondCategory: string;
  secondCategoryName: string;
  eventDesc: string;
  occurTime: number;
  eventStatus: EventStatus;
  eventOwnerId: number;
  eventOwnerName: string;
  creatorId: number;
  createTime: number;
  gmtModified: number;
  /** 目标设备 */
  deviceModels: EventDevice[];
  infoType: FaultTargetType;
  /** 是否误报 */
  isFalseAlarm: BackendFalseAlarm;
  causeDevices: CauseDevice[];
  detectTime: number;
  processNo: string;
  needUrge: boolean;
  /** 支持跳过缓解 */
  enableRelieveSkip: boolean;
  /** 支持无需复盘 */
  enableAuditSkip: boolean;
  /** 可以进行事件升级撤回的用户 */
  upgradeRevokeUserIds?: number[];
  callBackUserIds?: number[];
  /** 变更导致 */
  isChangeAlarm?: boolean;
  changeCode?: string;
  /** 原因类型 */
  causeBy?: string;
  /** 原因描述 */
  causeDesc?: string;
  /** 责任部门 */
  liableDept?: string;
  /** 责任人 */
  liablePersonName?: string;
  liablePersonId?: string;
  /** 附件 */
  addFileInfos?: BackendMcUploadFile[];
  optStatus?: BackendEventOperationStatusCode;
  firFinishTime?: string;
  firRespondTime?: string;
  relieveTime?: string;
  resolveTime?: string;
  auditTime?: string;
  closeTime?: string;
  firRespondUserId: string | null;
  firRespondUserName?: string;
  auditDesc?: string;
  secRespondUserId: string | null;
  secRespondUserName?: string;
  thiRespondUserId: string | null;
  thiRespondUserName?: string;
  auditFileInfos?: BackendMcUploadFile[];
  eventInfluence?: EventInfluence;
  eventTitle: string;
  northSync: boolean;
  detectUserId?: number;
  /** 事件定位原因 */
  detectReason?: string;
  /** 文本类型新格式的事件单号 */
  eventNo?: string;
  /** 事件流程引擎单号 */
  eventInstNo?: string;
  curHandlerId?: number | null;
  curHandlerName?: string | null;
  eventOwnerInfoList?: { id: number; userName: string }[];
  createUserName?: string;
  createUserId?: number;
  majorCode?: string;
  majorName?: string;
  locationList?: EventLocationInfo[];
  faultDesc?: string | null;
  updatedInAudit?: boolean | null;
};

export type BackendEventSearchParams = {
  eventStatus: BackendEventStatusCode[];
  deviceGuid: string;
  eventLevel: string;
  eventSource: string;
  falseAlarms: number[];
  key: string;
  createUserId: number;
  ownerName: string;
  eventId: number;
  createBeginTime: number;
  createEndTime: number;
  topCategorys: string[];
  secondCategorys: string[];
  blockTags: string[];
};

export type EventLocationInfo = {
  /**
   * BLOCK(楼栋)，ROOM(包间)，DEVICE(设备)
   */
  locationType: string;
  /**
   *
   *   当位置类型为楼栋时不传
   *   为包间时传包间类型code
   *   为设备时传设备类型code
   */
  subType?: string | null;
  /**
   * 楼栋guid、包间guid、设备guid
   */
  guid: string;
  /**
   * 楼栋名称、包间名称、设备名称
   */
  name?: string | null;
  deviceLabel?: string | null;
  fromBlockGuid?: string | null;
  fromBlockName?: string | null;
  fromRoomGuid?: string | null;
  fromRoomName?: string | null;
};

export enum BackendEventTierLevel {
  Tier1Upgrade,
  Tier1Respond,
  Tier2Upgrade,
  Tier2Respond,
  Tier3Upgrade,
  Tier3Respond,
}

export enum BackendEventStatus {
  Created = '1',
  Processing = '2',
  Relieved = '3',
  Resolved = '4',
  Audited = '5',
  Auditing = '4.1',
  Closed = '6',
}

export enum BackendEventStatusCode {
  /** 已创建 */
  Created = 'CREATED',
  /** 处理中 */
  Processing = 'PROCESSING',
  /** 已缓解 */
  Relieved = 'RELIEVED',
  /** 已解决 */
  Resolved = 'RESOLVED',
  /** 已评审 */
  Audited = 'AUDITED',
  /** 评审中 */
  Auditing = 'AUDITING',
  /** 已关闭 */
  Closed = 'CLOSED',
}

export const EventStatusValueMap = {
  [BackendEventStatusCode.Created]: '1',
  [BackendEventStatusCode.Processing]: '2',
  [BackendEventStatusCode.Relieved]: '3',
  [BackendEventStatusCode.Resolved]: '4',
  [BackendEventStatusCode.Audited]: '5',
  [BackendEventStatusCode.Auditing]: '4.1',
  [BackendEventStatusCode.Closed]: '6',
};
export const EventStatusNumberValueMap = {
  [BackendEventStatusCode.Created]: 1,
  [BackendEventStatusCode.Processing]: 2,
  [BackendEventStatusCode.Relieved]: 3,
  [BackendEventStatusCode.Resolved]: 4,
  [BackendEventStatusCode.Audited]: 5,
  [BackendEventStatusCode.Auditing]: 4.1,
  [BackendEventStatusCode.Closed]: 6,
};

export enum BackendEventOperationStatusCode {
  /** t1升级回调，展示响应按钮 */
  T1WaitForConfirm = 'T1_UPGRADE_CALLBACK',
  /** t1进展更新，展示进展更新按钮  */
  T1LogAdd = 'T1_OPT_LOG_ADD',
  /** t1确认办结 */
  T1UpgradeFinish = 'T1_UPGRADE_FINISH',
  /** 展示可以将t1阶段更新至t2的按钮 */
  T2PhaseUpgrade = 'T2_PHASE_UPGRADE',
  /** t2升级回调，展示响应按钮 */
  T2WaitForConfirm = 'T2_UPGRADE_CALLBACK',
  /** t2进展更新，展示进展更新按钮  */
  T2LogAdd = 'T2_OPT_LOG_ADD',
  /** t2确认办结 */
  T2UpgradeFinish = 'T2_UPGRADE_FINISH',
  /** 展示可以将t2阶段更新至t3的按钮 */
  T3PhaseUpgrade = 'T3_PHASE_UPGRADE',
  /** t3升级回调，展示响应按钮 */
  T3WaitForConfirm = 'T3_UPGRADE_CALLBACK',
  /** t3进展更新，展示进展更新按钮  */
  T3LogAdd = 'T3_OPT_LOG_ADD',
  /** t3确认办结 */
  T3UpgradeFinish = 'T3_UPGRADE_FINISH',
  /** t级别事件结束，接下来走的是解决、复盘评审环节 */
  Finished = 'FINISHED',
}

export enum BackendEventUpgradeCallbackCode {
  ShowRespond,
  None,
  ShowOther,
}
export enum BackendEventUpgradeLevel {
  Tier1 = 'LEVEL_ONE',
  Tier2 = 'LEVEL_TWO',
  Tier3 = 'LEVEL_THREE',
}

export enum BackendEventUpgradeStatus {
  SUCCESS = 'SUCCESS',
  FAIL = 'FAIL',
  PROCESSING = 'PROCESSING',
  SYSERROR = 'SYSERROR',
  REVOKE = 'REVOKE',
}

export enum BackendEventUpgradeChannel {
  InternalMessage = 'INTERNAL_MESSAGE',
  Sms = 'SMS',
  Email = 'EMAIL',
  Phone = 'PHONE',
  Webhook = 'WEB_HOOK',
  OffLine = 'OFFLINE',
  FeiShuCard = 'FEI_SHU_CARD',
}

export enum BackendEventUpgradeType {
  Offline = 'OFFLINE',
  Online = 'ONLINE',
}

export enum BackendFalseAlarm {
  None,
  FalseAlarm,
  FalseBill,
}

export const FalseAlarmMap = {
  [BackendFalseAlarm.None]: '否',
  [BackendFalseAlarm.FalseAlarm]: '是(误告警)',
  [BackendFalseAlarm.FalseBill]: '是(误开单)',
};

export const EventStatusMap = {
  [BackendEventStatus.Created]: '已创建',
  [BackendEventStatus.Processing]: '处理中',
  [BackendEventStatus.Relieved]: '已缓解',
  [BackendEventStatus.Resolved]: '已解决',
  [BackendEventStatus.Audited]: '已评审',
  [BackendEventStatus.Auditing]: '评审中',
  [BackendEventStatus.Closed]: '已关闭',
};

export const EVENT_STEP_STATUS_MAP_TEXT = {
  /** "创建", "创建" */
  [BackendEventStatusCode.Created]: '已创建',
  /** "已响应", "待缓解" */
  [BackendEventStatusCode.Processing]: '已响应',
  /** "已缓解", "待解决" */
  [BackendEventStatusCode.Relieved]: '已缓解',
  /** "已解决", "待评审" */
  [BackendEventStatusCode.Resolved]: '已解决',
  /** "已评审", "待关闭" */
  [BackendEventStatusCode.Audited]: '已评审',
  [BackendEventStatusCode.Auditing]: '评审中',
  /** "已关闭", "已关闭" */
  [BackendEventStatusCode.Closed]: '已关闭',
};

export const EventUpgradeLevelMap = {
  [BackendEventUpgradeLevel.Tier1]: 'Tier1',
  [BackendEventUpgradeLevel.Tier2]: 'Tier2',
  [BackendEventUpgradeLevel.Tier3]: 'Tier3',
};
export const EventUpgradeTypeMap = {
  [BackendEventUpgradeType.Offline]: '线下',
  [BackendEventUpgradeType.Online]: '线上',
};
export const EventUpgradeChannelMap = {
  [BackendEventUpgradeChannel.Email]: '邮件',
  [BackendEventUpgradeChannel.InternalMessage]: '站内信',
  [BackendEventUpgradeChannel.Sms]: '短信',
  [BackendEventUpgradeChannel.Phone]: '电话',
  [BackendEventUpgradeChannel.Webhook]: 'Webhook',
  [BackendEventUpgradeChannel.OffLine]: '线下',
  [BackendEventUpgradeChannel.FeiShuCard]: '飞书',
};
export const EventUpgradeStatusMap = {
  [BackendEventUpgradeStatus.SUCCESS]: '成功',
  [BackendEventUpgradeStatus.REVOKE]: '撤回',

  [BackendEventUpgradeStatus.FAIL]: '失败',
  [BackendEventUpgradeStatus.PROCESSING]: '处理中',
  [BackendEventUpgradeStatus.SYSERROR]: '系统异常',
};

export const EVENT_FALSE_ALARM_LIST = [
  { label: '否', key: BackendFalseAlarm.None },
  { label: '是(误告警)', key: BackendFalseAlarm.FalseAlarm },
  { label: '是(误开单)', key: BackendFalseAlarm.FalseBill },
];

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum EVENT_SOURCE_KEY_MAP {
  /** 监控系统 */
  MonitorSystemAlarm = '1',
  /** 巡检发现 */
  TicketSystemPatrol = '2',
  /** 设备维护 */
  TicketSystemMaintenance = '3',
}
export type EventSourceTypeKey = (typeof EVENT_SOURCE_KEY_MAP)[keyof typeof EVENT_SOURCE_KEY_MAP];
export const isMonitorSystemAlarm = (eventSource: string | undefined) =>
  eventSource === EVENT_SOURCE_KEY_MAP.MonitorSystemAlarm;
export const isCurrentUserResponser = ({
  isFirResponser,
  isSecResponser,
  isThiResponser,
  status,
}: {
  isFirResponser: boolean;
  isSecResponser: boolean;
  isThiResponser: boolean;
  status?: BackendEventOperationStatusCode;
}) => {
  return (
    (isFirResponser &&
      status &&
      [
        BackendEventOperationStatusCode.T1LogAdd,
        BackendEventOperationStatusCode.T1UpgradeFinish,
      ].includes(status)) ||
    (isSecResponser &&
      status &&
      [
        BackendEventOperationStatusCode.T2LogAdd,
        BackendEventOperationStatusCode.T2UpgradeFinish,
      ].includes(status)) ||
    (isThiResponser &&
      status &&
      [
        BackendEventOperationStatusCode.T3LogAdd,
        BackendEventOperationStatusCode.T3UpgradeFinish,
      ].includes(status))
  );
};

export type EventNotification = {
  id: number | string;
  eventId?: number;
  eventTitle?: string;
  detectReason?: string;
  idcTag: string;
  blockTag: string;
  eventLevel: string;
  eventStatus: string;
  eventSource: string;
  /** 事件分类 */
  eventCategory: string;
  occurTime: number;
  /** 事件描述 */
  eventDesc: string;
  /** 影响面 */
  eventInfluence?: string;
  /** 事件进展 */
  eventProgress?: string[];
  /** 通报角色名 */
  reportToRoleNames?: string[];
  /** 通报用户名 */
  reportToPersons?: string[];
  /** 通报角色Code */
  reportToRoleCodes?: string;

  /** 通报渠道 */
  reportChannel?: string;
  creatorId?: number;
  creatorName?: string;
  /** 通报时间 */
  reportTime?: number;
  subscribeCode?: string;
  infoType?: string;
  causeDevices?: string[];
  /** 通报内容 */
  reportContent?: string;
  causeDesc?: string;
  eventFirstLine?: string;
  /** 通报对象名称数组 */
  reportToObjects?: string[];
  /** 将阳高事件按阶段整合处理记录 */
  groupedEventProcessRecordsByPhase?: { [x: string]: string[] };
  featureSpecialInfoSort: boolean;
  blockGuidList: string[];
  locationList: EventLocationInfo[];
};

export type CauseDevice = {
  deviceName: string;
  idcTag: string;
  blockTag: string;
  roomTag: string;
  deviceGuid: string;
  deviceType: string;
  deviceTypeName: string;
};
export type EventOptLog = {
  id: number;
  eventId: number;
  optTime: number;
  optPlan: string;
  optContent: string;
  creatorId: number;
  creatorName: string;
  optStaffId: number;
  optStaffName: string;
  remarks: EventPhase;
  addedFiles?: BackendMcUploadFile[];
};

export enum EventPhase {
  Respond = 'T1',
  Remit = 'T2',
  Resolved = 'T3',
}
export type EventSpecificProcessRecord = {
  eventPhase: EventSpecificProcessStatus;
  status: EventSpecificProcessPhaseStatus;
  handlerId?: number;
  handleContent?: string;
  sla?: number;
  startTime?: number;
  endTime?: number;
};
export type EventTimeLine = {
  createUserId: string;
  createUserName: string;
  occurTime: number;
  detectTime: number;
  firUpgradeTime: number;
  firUpgradeUserId: string;
  firUpgradeUserName: string;
  firUpgradeContext: string;
  firRespondTime: number;
  firRespondUserId: string;
  firRespondUserName: string;
  secUpgradeTime: number;
  secUpgradeUserId: string;
  secUpgradeUserName: string;
  secUpgradeContext: string;
  secRespondTime: number;
  secRespondUserId: string;
  secRespondUserName: string;
  thiUpgradeTime: number;
  thiUpgradeUserId: string;
  thiUpgradeUserName: string;
  thiUpgradeContext: string;
  thiRespondTime: number;
  thiRespondUserId: string;
  thiRespondUserName: string;
  relieveTime: number;
  relieveUserId: string;
  relieveUserName: string;
  relieveDesc: string;
  resolveTime: number;
  resolveUserId: string;
  resolveUserName: string;
  resolveDesc: string;
  auditTime: number;
  auditUserId: string;
  auditUserName: string;
  closeTime: number;
  closeUserId: string;
  closeUserName: string;
  firTTE: number;
  secTTE: number;
  thiTTE: number;
  ttd: number;
  ttr: number;
  ttm: number;
  secFinishTime: number;
  thiFinishTime: number;
  firOldestRespondTime: number;
  secOldestRespondTime: number;
  thiOldestRespondTime: number;
  /** 判断事件在更新至t2阶段时是否选择了人员升级 */
  secUpgradeInPhase: boolean;
  /** 判断事件在更新至t3阶段时是否选择了人员升级 */
  thiUpgradeInPhase: boolean;
  /** 事件缓解成功提交的时间 */
  relieveSubmitTime: number;
  /** 事件解决成功提交的时间 */
  resolveSubmitTime: number;
  eventProgress?: EventSpecificProcessRecord[];
};
export type ProcessEnginePhaseConfig = {
  condition: string;
  canSkip?: boolean;
  optAuth?: string;
};
export type ProcessEngineConfig = {
  emergency?: ProcessEnginePhaseConfig;
  repair?: ProcessEnginePhaseConfig;
  recovery?: ProcessEnginePhaseConfig;
};

export enum EventBetaProcessStatus {
  Confirming = 'CONFIRMING',
  Relieving = 'RELIEVING',
  Finishing = 'FINISHING',
  Reviewing = 'REVIEWING',
  Auditing = 'AUDITING',
  Closed = 'CLOSED',
}

export const EventBetaProcessStatusNumberValueMap = {
  [EventBetaProcessStatus.Confirming]: 1,
  [EventBetaProcessStatus.Relieving]: 2,
  [EventBetaProcessStatus.Finishing]: 3,
  [EventBetaProcessStatus.Reviewing]: 4,
  [EventBetaProcessStatus.Auditing]: 5,
  [EventBetaProcessStatus.Closed]: 6,
};

export const EventBetaStatusMap = {
  [EventBetaProcessStatus.Confirming]: '事件确认',
  [EventBetaProcessStatus.Relieving]: '事件缓解',
  [EventBetaProcessStatus.Finishing]: '事件结束',
  [EventBetaProcessStatus.Reviewing]: '复盘',
  [EventBetaProcessStatus.Auditing]: '评审',
  [EventBetaProcessStatus.Closed]: '关闭',
};

export enum EventBetaProcessRecordStatus {
  Inprogress = 'INPROGRESS',
  Completed = 'COMPLETED',
  Pending = 'PENDING',
}

export const EventBetaProcessRecordStatusMap = {
  [EventBetaProcessRecordStatus.Inprogress]: '进行中',
  [EventBetaProcessRecordStatus.Completed]: '已完成',
  [EventBetaProcessRecordStatus.Pending]: '待完成',
};

export enum EventBetaReportStatus {
  UnRelieve = 'UNRELIEVE',
  Relieve = 'RELIEVE',
  UnResolve = 'UNRESOLVE',
  Resolve = 'RESOLVE',
}

export const EventBetaReportStatusMap = {
  [EventBetaReportStatus.UnRelieve]: '未缓解',
  [EventBetaReportStatus.Relieve]: '已缓解',
  [EventBetaReportStatus.UnResolve]: '未解决',
  [EventBetaReportStatus.Resolve]: '已解决',
};
