import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

import type {
  BackendEvent,
  BackendEventOperationStatusCode,
  BackendFalseAlarm,
  CauseDevice,
  EventDevice,
  EventInfluence,
  EventLocationInfo,
  EventStatus,
  FaultTargetType,
} from './backend-event.type';
import { getEventLocales } from './locales';
import type { EventLocales } from './locales';

export type EventJSON = {
  id: number;
  idcTag: string;
  blockTag: string;
  blockGuidList?: string[];
  eventLevel: string;
  eventLevelName: string;
  eventSource: string;
  eventSourceName: string;
  topCategory: string;
  topCategoryName: string;
  secondCategory: string;
  secondCategoryName: string;
  eventDesc: string;
  occurTime: number;
  eventStatus: EventStatus;
  eventOwnerId: number;
  eventOwnerName: string;
  creatorId: number;
  createUserId?: number;
  createUserName?: string;
  createTime: number;
  gmtModified: number;
  detectTime: number;
  /** 目标设备 */
  deviceModels: EventDevice[];
  infoType: FaultTargetType;
  /** 是否误报 */
  isFalseAlarm: BackendFalseAlarm;
  causeDevices: CauseDevice[];
  processNo: string;
  needUrge: boolean;
  enableRelieveSkip: boolean;
  enableAuditSkip: boolean;
  /** 变更导致 */
  isChangeAlarm?: boolean;
  upgradeRevokeUserIds?: number[];
  callBackUserIds?: number[];
  changeCode?: string;
  /** 原因类型 */
  causeBy?: string;
  /** 原因描述 */
  causeDesc?: string;
  /** 责任部门 */
  liableDept?: string;
  /** 责任人 */
  liablePersonName?: string;
  liablePersonId?: string;
  /** 附件 */
  addFileInfos?: McUploadFileJSON[];
  optStatus?: BackendEventOperationStatusCode;
  firFinishTime?: string;
  firRespondTime?: string;
  relieveTime?: string;
  resolveTime?: string;
  auditTime?: string;
  closeTime?: string;
  firRespondUserId: number | null;
  firRespondUserName?: string;
  auditDesc?: string;
  secRespondUserId: number | null;
  secRespondUserName?: string;
  thiRespondUserId: number | null;
  thiRespondUserName?: string;
  auditFileInfos?: McUploadFileJSON[];
  eventInfluence?: EventInfluence;
  eventTitle: string;
  northSync: boolean;
  detectUserId?: number;
  detectReason?: string;
  eventNo?: string;
  eventInstNo?: string;
  eventOwnerInfoList?: { id: number; userName: string }[];
  majorCode?: string;
  majorName?: string;
  locationList?: EventLocationInfo[];
  faultDesc?: string | null;
  updatedInAudit?: boolean; //true表示在复盘阶段更新过
  needAudit?: boolean; //true表示选择了需要复盘；false表示选择了无需复盘
};

export class Event {
  static fromApiObject(object: BackendEvent) {
    const copy = cloneDeep(object);

    return new Event(
      copy.id,
      copy.idcTag,
      copy.blockTag,
      copy.eventLevel,
      copy.eventLevelName,
      copy.eventSource,
      copy.eventSourceName,
      copy.topCategory,
      copy.topCategoryName,
      copy.secondCategory,
      copy.secondCategoryName,
      copy.eventDesc,
      copy.occurTime,
      copy.eventStatus,
      copy.eventOwnerId,
      copy.eventOwnerName,
      copy.creatorId,
      dayjs(copy.createTime).valueOf(),
      dayjs(copy.gmtModified).valueOf(),
      copy.detectTime,
      copy.deviceModels,
      copy.infoType,
      copy.isFalseAlarm,
      copy.causeDevices,
      copy.processNo,
      copy.needUrge,
      copy.firRespondUserId ? Number(copy.firRespondUserId) : null,
      copy.secRespondUserId ? Number(copy.secRespondUserId) : null,
      copy.thiRespondUserId ? Number(copy.thiRespondUserId) : null,
      copy.enableRelieveSkip,
      copy.enableAuditSkip,
      copy.eventTitle,
      copy.northSync,
      copy.detectUserId,
      copy.detectReason,
      copy.eventNo,
      copy.eventInstNo,
      copy.isChangeAlarm,
      copy.upgradeRevokeUserIds,
      copy.callBackUserIds,
      copy.changeCode,
      copy.causeBy,
      copy.causeDesc,
      copy.liableDept,
      copy.liablePersonName,
      copy.liablePersonId,
      (copy.addFileInfos ?? []).map(file => McUploadFile.fromApiObject(file)),
      copy.optStatus,
      copy.firFinishTime,
      copy.firRespondTime,
      copy.relieveTime,
      copy.resolveTime,
      copy.auditTime,
      copy.closeTime,
      copy.firRespondUserName,
      copy.auditDesc,
      copy.secRespondUserName,
      copy.thiRespondUserName,
      (copy.auditFileInfos ?? []).map(file => McUploadFile.fromApiObject(file)),
      copy.eventInfluence,
      copy.eventOwnerInfoList,
      copy.createUserId,
      copy.createUserName,
      copy.blockGuidList,
      copy.majorCode,
      copy.majorName,
      copy.locationList,
      copy.faultDesc,
      copy.updatedInAudit
    );
  }

  static fromJSON(json: EventJSON) {
    const copy = cloneDeep(json);

    return new Event(
      copy.id,
      copy.idcTag,
      copy.blockTag,
      copy.eventLevel,
      copy.eventLevelName,
      copy.eventSource,
      copy.eventSourceName,
      copy.topCategory,
      copy.topCategoryName,
      copy.secondCategory,
      copy.secondCategoryName,
      copy.eventDesc,
      copy.occurTime,
      copy.eventStatus,
      copy.eventOwnerId,
      copy.eventOwnerName,
      copy.creatorId,
      copy.createTime,
      copy.gmtModified,
      copy.detectTime,
      copy.deviceModels,
      copy.infoType,
      copy.isFalseAlarm,
      copy.causeDevices,
      copy.processNo,
      copy.needUrge,
      copy.firRespondUserId,
      copy.secRespondUserId,
      copy.thiRespondUserId,
      copy.enableRelieveSkip,
      copy.enableAuditSkip,
      copy.eventTitle,
      copy.northSync,
      copy.detectUserId,
      copy.detectReason,
      copy.eventNo,
      copy.eventInstNo,
      copy.isChangeAlarm,
      copy.upgradeRevokeUserIds,
      copy.callBackUserIds,
      copy.changeCode,
      copy.causeBy,
      copy.causeDesc,
      copy.liableDept,
      copy.liablePersonName,
      copy.liablePersonId,
      copy.addFileInfos?.map(file => McUploadFile.fromJSON(file)),
      copy.optStatus,
      copy.firFinishTime,
      copy.firRespondTime,
      copy.relieveTime,
      copy.resolveTime,
      copy.auditTime,
      copy.closeTime,
      copy.firRespondUserName,
      copy.auditDesc,
      copy.secRespondUserName,
      copy.thiRespondUserName,
      copy.auditFileInfos?.map(file => McUploadFile.fromJSON(file)),
      copy.eventInfluence,
      copy.eventOwnerInfoList,
      copy.createUserId,
      copy.createUserName,
      copy.blockGuidList,
      copy.majorCode,
      copy.majorName,
      copy.locationList,
      copy.faultDesc,
      copy.updatedInAudit
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: EventLocales;

  constructor(
    public id: number,
    public idcTag: string,
    public blockTag: string,
    public eventLevel: string,
    public eventLevelName: string,
    public eventSource: string,
    public eventSourceName: string,
    public topCategory: string,
    public topCategoryName: string,
    public secondCategory: string,
    public secondCategoryName: string,
    public eventDesc: string,
    public occurTime: number,
    public eventStatus: EventStatus,
    public eventOwnerId: number,
    public eventOwnerName: string,
    public creatorId: number,
    public createTime: number,
    public gmtModified: number,
    public detectTime: number,
    public deviceModels: EventDevice[],
    public infoType: FaultTargetType,
    public isFalseAlarm: BackendFalseAlarm,
    public causeDevices: CauseDevice[],
    public processNo: string,
    public needUrge: boolean,
    public firRespondUserId: number | null,
    public secRespondUserId: number | null,
    public thiRespondUserId: number | null,
    public enableRelieveSkip: boolean,
    public enableAuditSkip: boolean,
    public eventTitle: string,
    public northSync: boolean,
    public detectUserId?: number,
    public detectReason?: string,
    public eventNo?: string,
    public eventInstNo?: string,
    public isChangeAlarm?: boolean,
    public upgradeRevokeUserIds?: number[],
    public callBackUserIds?: number[],
    public changeCode?: string,
    public causeBy?: string,
    public causeDesc?: string,
    public liableDept?: string,
    public liablePersonName?: string,
    public liablePersonId?: string,
    public addFileInfos?: McUploadFile[],
    public optStatus?: BackendEventOperationStatusCode,
    public firFinishTime?: string,
    public firRespondTime?: string,
    public relieveTime?: string,
    public resolveTime?: string,
    public auditTime?: string,
    public closeTime?: string,
    public firRespondUserName?: string,
    public auditDesc?: string,
    public secRespondUserName?: string,
    public thiRespondUserName?: string,
    public auditFileInfos?: McUploadFile[],
    public eventInfluence?: EventInfluence,
    public eventOwnerInfoList?: { id: number; userName: string }[],
    public createUserId?: number,
    public createUserName?: string,
    public blockGuidList?: string[],
    public majorCode?: string,
    public majorName?: string,
    public locationList?: EventLocationInfo[],
    public faultDesc?: string | null,
    public updatedInAudit?: boolean
  ) {
    this._locales = getEventLocales(this._localeCode);
  }

  public set locales(locales: EventLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }
  public get statusText() {
    return this._locales.status[this.eventStatus.code];
  }
  public getFormattedCreatedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createTime).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.gmtModified).format(template);
  }

  public toApiObject(): BackendEvent {
    return cloneDeep({
      id: this.id,
      idcTag: this.idcTag,
      blockTag: this.blockTag,
      eventLevel: this.eventLevel,
      eventLevelName: this.eventLevelName,
      eventSource: this.eventSource,
      eventSourceName: this.eventSourceName,
      topCategory: this.topCategory,
      topCategoryName: this.topCategoryName,
      secondCategory: this.secondCategory,
      secondCategoryName: this.secondCategoryName,
      eventDesc: this.eventDesc,
      occurTime: this.occurTime,
      eventStatus: this.eventStatus,
      eventOwnerId: this.eventOwnerId,
      eventOwnerName: this.eventOwnerName,
      creatorId: this.creatorId,
      createTime: this.createTime,
      gmtModified: this.gmtModified,
      detectTime: this.detectTime,
      deviceModels: this.deviceModels,
      infoType: this.infoType,
      isFalseAlarm: this.isFalseAlarm,
      causeDevices: this.causeDevices,
      processNo: this.processNo,
      needUrge: this.needUrge,
      firRespondUserId: this.firRespondUserId ? this.firRespondUserId.toString() : null,
      secRespondUserId: this.secRespondUserId ? this.secRespondUserId.toString() : null,
      thiRespondUserId: this.thiRespondUserId ? this.thiRespondUserId.toString() : null,
      upgradeRevokeUserIds: this.upgradeRevokeUserIds,
      callBackUserIds: this.callBackUserIds,
      enableRelieveSkip: this.enableRelieveSkip,
      enableAuditSkip: this.enableAuditSkip,
      eventTitle: this.eventTitle,
      northSync: this.northSync,
      detectUserId: this.detectUserId,
      detectReason: this.detectReason,
      eventNo: this.eventNo,
      eventInstNo: this.eventInstNo,
      isChangeAlarm: this.isChangeAlarm,
      changeCode: this.changeCode,
      causeBy: this.causeBy,
      causeDesc: this.causeDesc,
      liableDept: this.liableDept,
      liablePersonName: this.liablePersonName,
      liablePersonId: this.liablePersonId,
      addFileInfos: (this.addFileInfos ?? []).map(file => file.toApiObject()),
      optStatus: this.optStatus,
      firFinishTime: this.firFinishTime,
      firRespondTime: this.firRespondTime,
      relieveTime: this.relieveTime,
      resolveTime: this.resolveTime,
      auditTime: this.auditTime,
      closeTime: this.closeTime,
      firRespondUserName: this.firRespondUserName,
      auditDesc: this.auditDesc,
      secRespondUserName: this.secRespondUserName,
      thiRespondUserName: this.thiRespondUserName,
      auditFileInfos: (this.auditFileInfos ?? []).map(file => file.toApiObject()),
      eventInfluence: this.eventInfluence,
      eventOwnerInfoList: this.eventOwnerInfoList,
      blockGuidList: this.blockGuidList,
      majorCode: this.majorCode,
      locationList: this.locationList,
      faultDesc: this.faultDesc,
    });
  }

  public toJSON(): EventJSON {
    return cloneDeep({
      id: this.id,
      idcTag: this.idcTag,
      blockTag: this.blockTag,
      eventLevel: this.eventLevel,
      eventLevelName: this.eventLevelName,
      eventSource: this.eventSource,
      eventSourceName: this.eventSourceName,
      topCategory: this.topCategory,
      topCategoryName: this.topCategoryName,
      secondCategory: this.secondCategory,
      secondCategoryName: this.secondCategoryName,
      eventDesc: this.eventDesc,
      occurTime: this.occurTime,
      eventStatus: this.eventStatus,
      eventOwnerId: this.eventOwnerId,
      eventOwnerName: this.eventOwnerName,
      creatorId: this.creatorId,
      createTime: this.createTime,
      gmtModified: this.gmtModified,
      detectTime: this.detectTime,
      deviceModels: this.deviceModels,
      infoType: this.infoType,
      isFalseAlarm: this.isFalseAlarm,
      causeDevices: this.causeDevices,
      processNo: this.processNo,
      needUrge: this.needUrge,
      upgradeRevokeUserIds: this.upgradeRevokeUserIds,
      callBackUserIds: this.callBackUserIds,
      enableRelieveSkip: this.enableRelieveSkip,
      enableAuditSkip: this.enableAuditSkip,
      eventTitle: this.eventTitle,
      northSync: this.northSync,
      detectUserId: this.detectUserId,
      detectReason: this.detectReason,
      eventNo: this.eventNo,
      eventInstNo: this.eventInstNo,
      isChangeAlarm: this.isChangeAlarm,
      changeCode: this.changeCode,
      causeBy: this.causeBy,
      causeDesc: this.causeDesc,
      liableDept: this.liableDept,
      liablePersonName: this.liablePersonName,
      liablePersonId: this.liablePersonId,
      addFileInfos: this.addFileInfos,
      optStatus: this.optStatus,
      firFinishTime: this.firFinishTime,
      firRespondTime: this.firRespondTime,
      relieveTime: this.relieveTime,
      resolveTime: this.resolveTime,
      auditTime: this.auditTime,
      closeTime: this.closeTime,
      firRespondUserId: this.firRespondUserId,
      firRespondUserName: this.firRespondUserName,
      auditDesc: this.auditDesc,
      secRespondUserId: this.secRespondUserId,
      secRespondUserName: this.secRespondUserName,
      thiRespondUserId: this.thiRespondUserId,
      thiRespondUserName: this.thiRespondUserName,
      auditFileInfos: this.auditFileInfos?.map(file => file.toJSON()),
      eventInfluence: this.eventInfluence,
      eventOwnerInfoList: this.eventOwnerInfoList,
      createUserId: this.createUserId,
      createUserName: this.createUserName,
      blockGuidList: this.blockGuidList,
      majorCode: this.majorCode,
      majorName: this.majorName,
      locationList: this.locationList,
      faultDesc: this.faultDesc,
      updatedInAudit: this.updatedInAudit,
    });
  }
}
