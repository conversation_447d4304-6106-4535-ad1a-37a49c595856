import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type {
  RiskClearStatus,
  RiskLevel,
  RiskObject,
  RiskObjectType,
  RiskStatus,
} from '@manyun/ticket.gql.client.risk-register';

export type BackendRiskRegister = {
  riskId: string /** 风险单ID	*/;
  createTime: string /** 风险单创建时间	*/;
  updateTime: string | null /** 风险单更新时间	*/;
  idcTag: string /** 机房 */;
  blockGuid: string /** 楼栋 */;
  riskResourceCode: string /** 风险来源元数据code	*/;
  riskResourceName: string /** 风险来源元数据code	*/;
  riskTopTypeCode: string /** 风险类别code	*/;
  riskTopTypeName: string /** 风险类别code	*/;
  riskSecTypeCode: string /** 风险类型code	*/;
  riskSecTypeName: string /** 风险类型code	*/;
  riskPriorityCode: string /** 风险优先级元数据code	*/;
  riskLevel: RiskLevel /** 风险等级	*/;
  riskDesc: string /** 风险描述	*/;
  riskObjectType: RiskObjectType /** 风险对象类型	*/;
  riskObjects: RiskObject[];
  riskOwnerId: number /** 责任人id	*/;
  riskInfluenceDesc: string /** 影响范围描述 */;
  relatePersonId: number | null /** 风险关联人id	*/;
  relatePersonName: string | null /** 风险关联人名称	*/;
  riskStatus: RiskStatus /** 风险单状态	*/;
  riskClearStatus: RiskClearStatus | null /** 风险单关闭风险解除状态	*/;
  riskClearReason: string | null /**风险单关闭原因	*/;
  createUserId: number /**创建人ID	*/;
  riskIdentifyTime: string | null /**提交识别时间	*/;
  riskEvaluateTime: string | null /**风险评估时间	*/;
  riskHandleTime: string | null /**风险处理时间	*/;
  riskAuditTime: string | null /**风险审核时间	*/;
  riskCloseTime: string | null /**风险关闭时间	*/;
  evaluateInstId: string | null /**评估审批流程id	*/;
  auditInstId: string | null;
  measureProgress?: number;
  fileInfoList: BackendMcUploadFile[] | null;
  completeTime?: string | null;
  riskIdentifier?: string | null;
  deleted?: boolean | null;
  locationList?: RiskLocationInfo[] | null;
  riskOwnerInfoList: { id: number; userName: string }[];
  planCompleteTime?: string | null;
  longTermRisk?: boolean | null;
  upgradeRoc?: boolean | null;
  questionRelateEventList?: QuestionRelateEventInfo[] | null;
  immutableEventIdList?: string[] | null;
  authorizedUserList?: string[] | null;
  riskLevelName?: string | null;
};
export type QuestionRelateEventInfo = {
  eventId: string;
  idcTag: string | null;
  blockGuid: string | null;
  eventTitle: string | null;
  eventLevelCode: string | null;
  eventLevelName: string | null;
  categoryCode: string | null;
  categoryName: string | null;
  occurTime: number | null;
  eventStatus: string | null;
  ownerId: number | null;
  ownerName: string | null;
  closeTime: number | null;
};

export type RiskLocationInfo = {
  /**
   * BLOCK(楼栋)，ROOM(包间)，DEVICE(设备)
   */
  locationType: string;
  /**
   *
   *   当位置类型为楼栋时不传
   *   为包间时传包间类型code
   *   为设备时传设备类型code
   */
  subType?: string | null;
  /**
   * 楼栋guid、包间guid、设备guid
   */
  guid: string;
  /**
   * 楼栋名称、包间名称、设备名称
   */
  name?: string | null;
  deviceLabel?: string | null;
  fromBlockGuid?: string | null;
  fromBlockName?: string | null;
  fromRoomGuid?: string | null;
  fromRoomName?: string | null;
};
// export type RiskObject = {
//   idcTag: string | null;
//   blockTag: string | null;
//   roomTag: string | null;
//   deviceType: string | null;
//   deviceTag: string | null;
//   objectName: string;
//   objectGuid: string | null;
// };

// export type RiskLevel = 'HIGH' | 'MID' | 'LOW';
// export type RiskObjectType = 'DEVICE' | 'ROOM' | 'OTHER';
// export type RiskClearStatus = 'CLEARED' /** 解除 */ | 'UNCLEARED' /** 未解除 */;

// export enum RiskStatus {
//   Draft = 'DRAFT' /** 草稿 */,
//   WaitingIdentify = 'WAITING_IDENTIFY' /** 待识别 */,
//   WaitingEvaluate = 'WAITING_EVALUATE' /** 待评估 */,
//   Handling = 'HANDLING' /** 处理中 */,
//   Approcing = 'APPROVING' /** 审批中 */,
//   Closed = 'CLOSED' /** 关闭 */,
// }

export const RiskStatusStepMap: Record<RiskStatus, number> = {
  DRAFT: 0,
  WAITING_IDENTIFY: 1,
  WAITING_EVALUATE: 2,
  HANDLING: 3,
  APPROVING: 4,
  CLOSED: 6,
};
