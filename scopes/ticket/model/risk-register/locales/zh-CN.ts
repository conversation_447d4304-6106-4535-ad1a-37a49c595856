import type { RiskRegisterLocales } from './type';

export const zhCN: RiskRegisterLocales = {
  title: '标题',
  content: '内容',
  createdBy: {
    __self: '创建人',
    id: '创建人 ID',
    name: '创建人姓名',
  },
  createdAt: '创建于',
  modifiedAt: '修改于',
  riskLevel: {
    _self: '风险等级',
    enum: {
      HIGH: '高',
      MID: '中',
      LOW: '低',
    },
  },
  riskType: '风险类型',
  riskCategory: '风险类别',
  location: '位置',
  riskStatus: {
    _self: '风险单状态',
    enum: {
      DRAFT: '草稿',
      WAITING_IDENTIFY: '待识别',
      WAITING_EVALUATE: '待评估',
      HANDLING: '处理中',
      APPROVING: '审批中',
      CLOSED: '关闭',
    },
    enumFull: {
      DRAFT: '草稿',
      WAITING_IDENTIFY: '添加风险措施',
      WAITING_EVALUATE: '评估',
      HANDLING: '处理',
      APPROVING: '关单审批',
      CLOSED: '关单',
    },
  },
  riskClearStatus: {
    _self: '风险解除状态',
    enum: {
      CLEARED: '已解除',
      UNCLEARED: '无法解除',
    },
  },
  riskResourceCode: '风险来源',
  riskOwner: '责任人',
  registrant: '登记人',
  createTime: '登记时间',
  updateTime: '更新时间',
  riskObjectType: {
    _self: '风险对象类型',
    enum: {
      DEVICE: '设备',
      ROOM: '包间',
      OTHER: '其他',
    },
  },
  measureType: {
    _self: '措施类型',
    enum: {
      SHORT: '短期措施',
      LONG: '长期措施',
    },
  },
  resource: '风险来源',
  priority: '优先级',
  riskDesc: '风险描述',
  riskObjectName: '风险对象名称',
  riskInfluenceDesc: '影响范围描述',
  measureProgress: '措施进度',
  planCompleteTime: '计划完成时间',
  completeTime: '实际完成时间',
  operation: '操作',
  edit: {
    _self: '编辑',
    title: '编辑风险单',
  },
  delete: {
    _self: '删除',
    title: '确定删除该风险单？删除后不可恢复',
    confirmText: '确认删除',
    confirmCancel: '取消',
  },
  restart: {
    _self: '重启',
    title: '确认重启此风险单？重启后风险单会变为“处理中“状态，请谨慎操作！',
    confirmText: '确认重启',
    confirmCancel: '我再想想',
  },
  createRisk: '新建风险单',
  searchPlaceholder: '搜索风险ID或风险描述',
  fileName: '风险登记册',
  transfer: {
    _self: '转交',
    confirmText: '提交',
    successMessage: '转交成功',
    transferUser: '转交对象',
    transferUserRequiredMessage: '转交对象必选！',
    transferReason: '转交原因',
    transferReasonMaxLengthMessage: '最多输入 300 个字符！',
  },
  importRisk: {
    _self: '导入风险单',
    successMessage: '导入成功',
    importBtnText: '导入',
    downloadTemplate: '下载模板',
    downloadTemplateName: '风险单导入模板',
  },
  deviceName: '设备名称',
  relateName: '包间及其他',
  filter: {
    _self: '筛选',
    reset: '重置',
    search: '搜索',
  },
  riskIdentifier: '识别人',
};

export default zhCN;
