import type {
  MeasureType,
  RiskClearStatus,
  RiskLevel,
  RiskObjectType,
  RiskStatus,
} from '@manyun/ticket.gql.client.risk-register';

export type RiskRegisterLocales = {
  title: string;
  content: string;
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  modifiedAt: string;
  riskLevel: {
    _self: string;
    enum: Record<RiskLevel, string>;
  };
  riskType: string;
  riskCategory: string;
  location: string;
  riskStatus: {
    _self: string;
    enum: Record<RiskStatus, string>;
    enumFull: Record<RiskStatus, string>;
  };
  riskClearStatus: {
    _self: string;
    enum: Record<RiskClearStatus, string>;
  };
  riskResourceCode: string;
  riskOwner: string;
  registrant: string;
  createTime: string;
  updateTime: string;
  riskObjectType: {
    _self: string;
    enum: Record<RiskObjectType, string>;
  };
  measureType: {
    _self: string;
    enum: Record<MeasureType, string>;
  };
  resource: string;
  priority: string;
  riskDesc: string;
  riskObjectName: string;
  riskInfluenceDesc: string;
  measureProgress: string;
  planCompleteTime: string;
  completeTime: string;
  operation: string;
  edit: {
    _self: string;
    title: string;
  };
  delete: {
    _self: string;
    title: string;
    confirmText: string;
    confirmCancel: string;
  };
  restart: {
    _self: string;
    title: string;
    confirmText: string;
    confirmCancel: string;
  };
  createRisk: string;
  searchPlaceholder: string;
  fileName: string;
  transfer: {
    _self: string;
    confirmText: string;
    successMessage: string;
    transferUser: string;
    transferUserRequiredMessage: string;
    transferReason: string;
    transferReasonMaxLengthMessage: string;
  };
  importRisk: {
    _self: string;
    successMessage: string;
    importBtnText: string;
    downloadTemplate: string;
    downloadTemplateName: string;
  };
  deviceName: string;
  relateName: string;
  filter: {
    _self: string;
    reset: string;
    search: string;
  };
  riskIdentifier: string;
};
