import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import { McUploadFile as UploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type {
  McUploadFile,
  RelatePerson,
  RiskClearStatus,
  RiskLevel,
  RiskObject,
  RiskObjectType,
  RiskStatus,
} from '@manyun/ticket.gql.client.risk-register';

import type {
  BackendRiskRegister,
  QuestionRelateEventInfo,
  RiskLocationInfo,
} from './backend-risk-register';
import { getRiskRegisterLocales } from './locales';
import type { RiskRegisterLocales } from './locales';

export type RiskRegisterJSON = {
  id: string;
  createdAt: number;
  modifiedAt: number | null;
  idcTag?: string | null;
  blockGuid?: string | null;
  riskResourceCode?: string | null;
  riskResourceName?: string | null;
  riskCategoryCode?: string | null;
  riskCategoryName?: string | null;
  riskTypeCode?: string | null;
  riskTypeName?: string | null;
  riskLevel?: RiskLevel | null;
  riskObjectType?: RiskObjectType | null;
  riskObjects?: RiskObject[] | null;
  riskStatus: RiskStatus;
  riskClearStatus: RiskClearStatus | null;
  createUser: number | null;
  riskClearReason?: string | null;
  riskDesc?: string | null;
  riskInfluenceDesc?: string | null;
  relatePerson?: RelatePerson | null;
  measureProgress?: number;
  fileInfoList?: McUploadFile[] | null;
  riskIdentifyTime: string | null;
  riskEvaluateTime: string | null;
  riskHandleTime: string | null;
  riskAuditTime: string | null;
  riskCloseTime: string | null;
  evaluateInstId: string | null;
  auditInstId: string | null;
  completeTime?: string | null;
  deleted?: boolean | null;
  riskIdentifier?: string | null;
  locationList?: RiskLocationInfo[] | null;
  riskOwnerIdList?: number[] | null;
  planCompleteTime?: number | null;
  longTermRisk?: boolean | null;
  upgradeRoc?: boolean | null;
  relateEventIdList?: string[] | null;
  authorizedUserList?: string[] | null;
  immutableEventIdList?: string[] | null;
  questionRelateEventList?: QuestionRelateEventInfo[] | null;
  riskLevelName?: string | null;
};

export type RiskRegisterToApi = {
  riskId: string /** 风险单ID	*/;
  idcTag: string | null /** 机房 */;
  blockGuid: string | null /** 楼栋 */;
  riskResourceCode: string | null /** 风险来源元数据code	*/;
  riskTopTypeCode: string | null /** 风险类别code	*/;
  riskSecTypeCode: string | null /** 风险类型code	*/;
  riskLevel: RiskLevel | null /** 风险等级	*/;
  riskDesc: string | null /** 风险描述	*/;
  riskObjectType: RiskObjectType | null /** 风险对象类型	*/;
  riskObjectName: string | null /**当风险对象类型为设备或者包间时，英文逗号拼接多个guid */;
  relatePersonId?: number | null /** 风险关联人id	*/;
  relatePersonName?: string | null /** 风险关联人名称	*/;
  createUserId: number | null /**创建人ID	*/;
  riskInfluenceDesc: string | null;
  fileInfoList?: BackendMcUploadFile[];
  deleted?: boolean | null;
  riskIdentifier?: string | null /**识别人 */;
  locationList?: RiskLocationInfo[] | null;
  riskOwnerIdList?: number[] | null;
  planCompleteTime?: number | null;
  longTermRisk?: boolean | null;
  relateEventIdList?: string[] | null;
  immutableEventIdList?: string[] | null;
};

export class RiskRegister {
  static fromApiObject(object: BackendRiskRegister) {
    const copy = cloneDeep(object);

    return new RiskRegister(
      copy.riskId,
      dayjs(copy.createTime).valueOf(),
      copy.updateTime ? dayjs(copy.updateTime).valueOf() : null,
      copy.createUserId,
      copy.riskStatus,
      copy.riskClearStatus,
      copy.riskIdentifyTime,
      copy.riskEvaluateTime,
      copy.riskHandleTime,
      copy.riskAuditTime,
      copy.riskCloseTime,
      copy.evaluateInstId,
      copy.auditInstId,
      copy.riskInfluenceDesc ?? undefined,
      copy.riskClearReason ?? undefined,
      copy.relatePersonName
        ? {
            type: copy.relatePersonId ? 'internal' : 'external',
            id: copy.relatePersonId,
            name: copy.relatePersonName,
            label: copy.relatePersonName,
            value: copy.relatePersonId ? copy.relatePersonId.toString() : copy.relatePersonName,
          }
        : undefined,
      copy.measureProgress,
      (copy.fileInfoList ?? []).map(file => UploadFile.fromApiObject(file)),
      copy.idcTag,
      copy.blockGuid,
      copy.riskDesc,
      copy.riskResourceCode,
      copy.riskResourceName,
      copy.riskTopTypeCode,
      copy.riskTopTypeName,
      copy.riskSecTypeCode,
      copy.riskSecTypeName,
      copy.riskLevel,
      copy.riskLevelName,
      copy.riskObjectType,
      (copy.riskObjects || []).map(item => ({
        ...item,
        key: item.objectGuid ?? item.objectName,
        value: item.objectGuid ?? item.objectName,
        label: item.objectName,
      })),
      copy.completeTime,
      copy.planCompleteTime ? dayjs(copy.planCompleteTime).valueOf() : null,
      copy.riskIdentifier,
      copy.deleted,
      copy.locationList,
      (copy.riskOwnerInfoList ?? []).map(item => item.id),
      copy.longTermRisk,
      copy.upgradeRoc,
      copy.authorizedUserList,
      copy.questionRelateEventList,
      copy.immutableEventIdList,
      copy.questionRelateEventList?.map(item => item.eventId) ?? []
    );
  }

  static fromJSON(json: RiskRegisterJSON) {
    const copy = cloneDeep(json);

    return new RiskRegister(
      copy.id,
      copy.createdAt,
      copy.modifiedAt,
      copy.createUser,
      copy.riskStatus,
      copy.riskClearStatus,
      copy.riskIdentifyTime,
      copy.riskEvaluateTime,
      copy.riskHandleTime,
      copy.riskAuditTime,
      copy.riskCloseTime,
      copy.evaluateInstId,
      copy.auditInstId,
      copy.riskInfluenceDesc,
      copy.riskClearReason,
      copy.relatePerson,
      copy.measureProgress,
      copy.fileInfoList,
      copy.idcTag,
      copy.blockGuid,
      copy.riskDesc,
      copy.riskResourceCode,
      copy.riskResourceName,
      copy.riskCategoryCode,
      copy.riskCategoryName,
      copy.riskTypeCode,
      copy.riskTypeName,
      copy.riskLevel,
      copy.riskLevelName,
      copy.riskObjectType,
      copy.riskObjects,
      copy.completeTime,
      copy.planCompleteTime,
      copy.riskIdentifier,
      copy.deleted,
      copy.locationList,
      copy.riskOwnerIdList,
      copy.longTermRisk,
      copy.upgradeRoc,
      copy.authorizedUserList,
      copy.questionRelateEventList,
      copy.immutableEventIdList,
      copy.relateEventIdList
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';

  private _locales: RiskRegisterLocales;

  constructor(
    public id: string,
    public createdAt: number,
    public modifiedAt: number | null,
    public createUser: number | null,
    public riskStatus: RiskStatus,
    public riskClearStatus: RiskClearStatus | null,
    public riskIdentifyTime: string | null,
    public riskEvaluateTime: string | null,
    public riskHandleTime: string | null,
    public riskAuditTime: string | null,
    public riskCloseTime: string | null,
    public evaluateInstId: string | null,
    public auditInstId: string | null,
    public riskInfluenceDesc?: string | null,
    public riskClearReason?: string | null,
    public relatePerson?: RelatePerson | null,
    public measureProgress?: number,
    public fileInfoList?: McUploadFile[] | null,
    public idcTag?: string | null,
    public blockGuid?: string | null,
    public riskDesc?: string | null,
    public riskResourceCode?: string | null,
    public riskResourceName?: string | null,
    public riskCategoryCode?: string | null,
    public riskCategoryName?: string | null,
    public riskTypeCode?: string | null,
    public riskTypeName?: string | null,
    public riskLevel?: RiskLevel | null,
    public riskLevelName?: string | null,
    public riskObjectType?: RiskObjectType | null,
    public riskObjects?: RiskObject[] | null,
    public completeTime?: string | null,
    public planCompleteTime?: number | null,
    public riskIdentifier?: string | null,
    public deleted?: boolean | null,
    public locationList?: RiskLocationInfo[] | null,
    public riskOwnerIdList?: number[] | null,
    public longTermRisk?: boolean | null,
    public upgradeRoc?: boolean | null,
    public authorizedUserList?: string[] | null,
    public questionRelateEventList?: QuestionRelateEventInfo[] | null,
    public immutableEventIdList?: string[] | null,
    public relateEventIdList?: string[] | null
  ) {
    this._locales = getRiskRegisterLocales(this._localeCode);
  }

  public set locales(locales: RiskRegisterLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): RiskRegisterToApi {
    return cloneDeep({
      riskId: this.id,
      idcTag: this.blockGuid ? this.blockGuid.split('.')[0] : null,
      blockGuid: this.blockGuid?.split('.').length === 2 ? this.blockGuid : null,
      riskResourceCode: this.riskResourceCode ?? null,
      riskTopTypeCode: this.riskCategoryCode ?? null,
      riskSecTypeCode: this.riskTypeCode ?? null,
      riskLevel: this.riskLevel ?? null,
      riskDesc: this.riskDesc ?? null,
      riskObjectType: this.riskObjectType ?? null,
      riskObjectName: this.riskObjects
        ? this.riskObjects.map(item => item.value ?? item.label).join(',')
        : null,
      relatePersonId: this.relatePerson?.id ?? null,
      relatePersonName: this.relatePerson?.label ?? null,
      createUserId: this.createUser ?? null,
      riskInfluenceDesc: this.riskInfluenceDesc ?? null,
      fileInfoList: (this.fileInfoList ?? []).map(file => UploadFile.fromJSON(file).toApiObject()),
      riskIdentifier: this.riskIdentifier ?? null,
      locationList: this.locationList ?? null,
      riskOwnerIdList: this.riskOwnerIdList,
      longTermRisk: this.longTermRisk,
      planCompleteTime: this.planCompleteTime,
      immutableEventIdList: this.immutableEventIdList,
      relateEventIdList: this.relateEventIdList,
    });
  }

  public toJSON(): RiskRegisterJSON {
    return cloneDeep({
      id: this.id,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      idcTag: this.idcTag ?? undefined,
      blockGuid: this.blockGuid,
      riskResourceCode: this.riskResourceCode,
      riskResourceName: this.riskResourceName,
      riskCategoryCode: this.riskCategoryCode,
      riskCategoryName: this.riskCategoryName,
      riskTypeCode: this.riskTypeCode,
      riskTypeName: this.riskTypeName,
      riskLevel: this.riskLevel,
      riskLevelName: this.riskLevelName,
      riskObjectType: this.riskObjectType,
      riskObjects: this.riskObjects,
      riskStatus: this.riskStatus,
      riskClearStatus: this.riskClearStatus,
      createUser: this.createUser,
      riskClearReason: this.riskClearReason,
      riskDesc: this.riskDesc,
      relatePerson: this.relatePerson,
      riskIdentifyTime: this.riskIdentifyTime,
      riskEvaluateTime: this.riskEvaluateTime,
      riskHandleTime: this.riskHandleTime,
      riskAuditTime: this.riskAuditTime,
      riskCloseTime: this.riskCloseTime,
      evaluateInstId: this.evaluateInstId,
      auditInstId: this.auditInstId,
      fileInfoList: this.fileInfoList,
      riskInfluenceDesc: this.riskInfluenceDesc,
      completeTime: this.completeTime,
      planCompleteTime: this.planCompleteTime,
      riskIdentifier: this.riskIdentifier,
      deleted: this.deleted,
      locationList: this.locationList,
      riskOwnerIdList: this.riskOwnerIdList,
      longTermRisk: this.longTermRisk,
      upgradeRoc: this.upgradeRoc,
      authorizedUserList: this.authorizedUserList,
      questionRelateEventList: this.questionRelateEventList,
      immutableEventIdList: this.immutableEventIdList,
      relateEventIdList: this.relateEventIdList,
    });
  }
}
