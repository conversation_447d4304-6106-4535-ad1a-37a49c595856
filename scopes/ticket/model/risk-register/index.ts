export { RiskRegister } from './risk-register';
export type { RiskRegisterJSON, RiskRegisterToApi } from './risk-register';
export type {
  BackendRiskRegister,
  RiskLocationInfo,
  QuestionRelateEventInfo,
} from './backend-risk-register';
export { RiskStatusStepMap } from './backend-risk-register';
export { getRiskRegisterLocales } from './locales';
export type { RiskRegisterLocales } from './locales';
