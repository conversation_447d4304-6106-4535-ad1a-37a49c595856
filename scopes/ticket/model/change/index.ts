export {
  CHANGE_RISK_LEVEL_KEY_MAP,
  CHANGE_EXE_WAY_MAP,
  CHANGE_RESULT_KEY_MAP,
  CHANGE_RESULT_KEY_TEXT_MAP,
  CHANGE_EXE_WAY_TEXT_MAP,
  CHAN<PERSON>_RISK_LEVEL_TEXT_MAP,
  CHANGE_RISK_LEVEL_OPTIONS,
  ChangeTicketState,
  CHANGE_TICKET_STATUS_TEXT_MAP,
  SlaUnitTypeKeyMap,
  SLA_UNIT_TYPE_TEXT_MAP,
  ChangeDeviceInhibitionStatus,
  ChangeTabs,
  CH<PERSON>GE_TEMPLATE_STATUS_TEXT_MAP,
  ChangeTemplateState,
  <PERSON><PERSON><PERSON>_ONLINE_TICKET_STATUS_TEXT_MAP,
  ChangeOnlineTicketState,
  CHANGE_ONLINE_RESULT_KEY_TEXT_MAP,
} from './change';
export type { SourceType } from './change';
export { getChangeLocales } from './locales';
