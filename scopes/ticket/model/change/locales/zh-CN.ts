import type { ChangeLocales } from './type';

export const zhCN: ChangeLocales = {
  title: '标题',
  content: '内容',
  createdBy: {
    __self: '创建人',
    id: '创建人 ID',
    name: '创建人姓名',
  },
  createdAt: '创建于',
  modifiedAt: '修改于',
  changeSource: {
    __self: '变更来源',
    source: {
      EVENT: '事件',
      RISK: '风险',
      CHANGE: '变更',
      INSPECTION: '巡检',
      MAINTENANCE: '维护',
    },
  },
  upgradeType: {
    __self: '升级方式',
    OFFLINE: '线下',
    ONLINE: '线上',
  },
};

export default zhCN;
