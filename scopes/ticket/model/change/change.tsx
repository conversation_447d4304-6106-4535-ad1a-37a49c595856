// eslint-disable-next-line @typescript-eslint/naming-convention
export enum CHANGE_RISK_LEVEL_KEY_MAP {
  NormalFirst = 'NORMAL_FIRST',
  NormaSecond = 'NORMAL_SECOND',
  NormaThird = 'NORMAL_THIRD',
  NormaFour = 'NORMAL_FOUR',
  Standard = 'STANDARD',
  Urgent = 'URGENT',
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum CHANGE_EXE_WAY_MAP {
  OnLine = 'ON_LINE',
  OffLine = 'OFF_LINE',
}

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum CHANGE_RESULT_KEY_MAP {
  Success = 'SUCCESS',
  Failed = 'FAILED',
}

export enum ChangeTicketState {
  Draft = 'DRAFT',
  Approving = 'APPROVING',
  WaitingChange = 'WAITING_CHANGE',
  Changing = 'CHANGING',
  InSummary = 'IN_SUMMARY',
  WaitingClose = 'WAITING_CLOSE',
  Finish = 'FINISH',
  SummaryApproving = 'SUMMARY_APPROVING',
}

// SLA单位 枚举
export enum SlaUnitTypeKeyMap {
  Minutes = 'MIN',
  Hour = 'HOUR',
  Day = 'DAY',
}

export const CHANGE_RESULT_KEY_TEXT_MAP = {
  [CHANGE_RESULT_KEY_MAP.Success]: '执行成功',
  [CHANGE_RESULT_KEY_MAP.Failed]: '执行失败',
};
export const CHANGE_ONLINE_RESULT_KEY_TEXT_MAP = {
  [CHANGE_RESULT_KEY_MAP.Success]: '变更成功',
  [CHANGE_RESULT_KEY_MAP.Failed]: '变更失败',
};
export const CHANGE_EXE_WAY_TEXT_MAP = {
  [CHANGE_EXE_WAY_MAP.OnLine]: '线上变更',
  [CHANGE_EXE_WAY_MAP.OffLine]: '线下变更',
};

export const CHANGE_RISK_LEVEL_TEXT_MAP = {
  [CHANGE_RISK_LEVEL_KEY_MAP.NormalFirst]: '一般变更(一级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.NormaSecond]: '一般变更(二级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.NormaThird]: ' 一般变更(三级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.NormaFour]: ' 一般变更(四级)',
  [CHANGE_RISK_LEVEL_KEY_MAP.Standard]: '标准变更',
  [CHANGE_RISK_LEVEL_KEY_MAP.Urgent]: '紧急变更',
};

export const CHANGE_RISK_LEVEL_OPTIONS = [
  {
    label: CHANGE_RISK_LEVEL_TEXT_MAP[CHANGE_RISK_LEVEL_KEY_MAP.NormalFirst],
    value: CHANGE_RISK_LEVEL_KEY_MAP.NormalFirst,
  },
  {
    label: CHANGE_RISK_LEVEL_TEXT_MAP[CHANGE_RISK_LEVEL_KEY_MAP.NormaSecond],
    value: CHANGE_RISK_LEVEL_KEY_MAP.NormaSecond,
  },
  {
    label: CHANGE_RISK_LEVEL_TEXT_MAP[CHANGE_RISK_LEVEL_KEY_MAP.NormaThird],
    value: CHANGE_RISK_LEVEL_KEY_MAP.NormaThird,
  },
  {
    label: CHANGE_RISK_LEVEL_TEXT_MAP[CHANGE_RISK_LEVEL_KEY_MAP.NormaFour],
    value: CHANGE_RISK_LEVEL_KEY_MAP.NormaFour,
  },
  {
    label: CHANGE_RISK_LEVEL_TEXT_MAP[CHANGE_RISK_LEVEL_KEY_MAP.Standard],
    value: CHANGE_RISK_LEVEL_KEY_MAP.Standard,
  },
  {
    label: CHANGE_RISK_LEVEL_TEXT_MAP[CHANGE_RISK_LEVEL_KEY_MAP.Urgent],
    value: CHANGE_RISK_LEVEL_KEY_MAP.Urgent,
  },
];

export const CHANGE_TICKET_STATUS_TEXT_MAP = {
  [ChangeTicketState.Draft]: '草稿',
  [ChangeTicketState.Approving]: '申请审批中',
  [ChangeTicketState.WaitingChange]: '待变更',
  [ChangeTicketState.Changing]: '变更中',
  [ChangeTicketState.InSummary]: '总结',
  [ChangeTicketState.WaitingClose]: '待关闭',
  [ChangeTicketState.Finish]: '结束',
  [ChangeTicketState.SummaryApproving]: '总结审批中',
};

export enum ChangeOnlineTicketState {
  Draft = 'DRAFT',
  Approving = 'APPROVING',
  WaitingChange = 'WAITING_CHANGE',
  Changing = 'CHANGING',
  InSummary = 'IN_SUMMARY',
  Cancel = 'CANCEL',
  Finish = 'FINISH',
  SummaryApproving = 'SUMMARY_APPROVING',
}
export const CHANGE_ONLINE_TICKET_STATUS_TEXT_MAP = {
  [ChangeOnlineTicketState.Draft]: '草稿',
  [ChangeOnlineTicketState.Approving]: '申请审批中',
  [ChangeOnlineTicketState.WaitingChange]: '待变更',
  [ChangeOnlineTicketState.Changing]: '变更中',
  [ChangeOnlineTicketState.InSummary]: '总结',
  [ChangeOnlineTicketState.Cancel]: '已取消',
  [ChangeOnlineTicketState.Finish]: '结束',
  [ChangeOnlineTicketState.SummaryApproving]: '总结审批中',
};

export const SLA_UNIT_TYPE_TEXT_MAP = {
  [SlaUnitTypeKeyMap.Minutes]: '分钟',
  [SlaUnitTypeKeyMap.Hour]: '小时',
  [SlaUnitTypeKeyMap.Day]: '天',
};

export type SourceType = 'EVENT' | 'RISK' | 'CHANGE' | 'INSPECTION' | 'MAINTENANCE';

export enum ChangeDeviceInhibitionStatus {
  expectation = 1,
  unexpected = 2,
}

export enum ChangeTabs {
  Info = 'INFO',
  ExecuteRecord = 'EXECUTE_RECORDS',
  SummaryRecord = 'SUMMARY_RECORD',
  Alarm = 'ALARM',
  Relate = 'RELATE',
}

export enum ChangeTemplateState {
  Draft = 'DRAFT',
  Approving = 'APPROVING',
  Expire = 'EXPIRE',
  Available = 'AVAILABLE',
}

export const CHANGE_TEMPLATE_STATUS_TEXT_MAP = {
  [ChangeTemplateState.Draft]: '草稿',
  [ChangeTemplateState.Approving]: '审批中',
  [ChangeTemplateState.Expire]: '无效',
  [ChangeTemplateState.Available]: '生效',
};
