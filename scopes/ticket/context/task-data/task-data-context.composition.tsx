import React, { useContext } from 'react';
import { TaskDataProvider } from './task-data-context-provider';
import { TaskDataContext } from './task-data-context';

export function MockComponent() {
  const theme = useContext(TaskDataContext);

  return <div style={{ color: theme.color }}>this should be {theme.color}</div>;
}

export const BasicThemeUsage = () => {
  return (
    <TaskDataProvider color="blue">
      <MockComponent />
    </TaskDataProvider>
  );
};
