import React, { ReactNode } from 'react';

import { TaskDataContext } from './task-data-context';

export type TaskDataProviderProps = {
  /**
   * primary color of theme.
   */
  color?: string;

  /**
   * children to be rendered within this theme.
   */
  children: ReactNode;
};

export function TaskDataProvider({ color, children }: TaskDataProviderProps) {
  return <TaskDataContext.Provider value={{ color }}>{children}</TaskDataContext.Provider>;
}
