import { createContext, useContext } from 'react';

import moment from 'moment';

import type {
  BackendMonthlyTaskSearchParam,
  BackendPlanSearchParam,
  MonthlyTasks,
  PlanJSON,
  PlanType,
} from '@manyun/ticket.model.task';

export type MonthlyTaskQueryParams = {
  month?: string;
  year?: string;
} & BackendMonthlyTaskSearchParam;

export type Values = {
  planType: PlanType | null;
  plans: PlanJSON[];
  planLoading: boolean;
  planTotal: number;
  planPagination: { pageNum: number; pageSize: number };
  planQueryParams?: BackendPlanSearchParam;
  monthlyTasks: MonthlyTasks[];
  monthlyTasksLoading: boolean;
  year: string;
  month: string;
  monthlyCalendarQueryParams: MonthlyTaskQueryParams;
};

export type Handlers = {
  togglePlans: (params: BackendPlanSearchParam & { pageNum: number; pageSize: number }) => void;
  toggleMonthlyTasks: (params: MonthlyTaskQueryParams) => void;
  toggleYear: (param: string) => void;
  toggleMonth: (param: string) => void;
  togglePlanPagination: (params: { pageNum: number; pageSize: number }) => void;
  togglePlanQueryParams: (params: BackendPlanSearchParam) => void;
  toggleMonthlyCalendarQueryParams: (params: MonthlyTaskQueryParams) => void;
};
const noop = () => {};
const initialValue: [Values, Handlers] = [
  {
    planType: null,
    plans: [],
    planLoading: false,
    planTotal: 0,
    planPagination: { pageNum: 1, pageSize: 10 },
    monthlyTasks: [],
    monthlyTasksLoading: false,
    planQueryParams: {},
    monthlyCalendarQueryParams: {
      jobTypeList: [],
      endDate: moment().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      startDate: moment().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    },
    year: moment().format('YYYY'),
    month: moment().format('YYYY-MM'),
  },
  {
    togglePlans: noop,
    toggleMonthlyTasks: noop,
    togglePlanPagination: noop,
    toggleYear: noop,
    toggleMonth: noop,
    togglePlanQueryParams: noop,
    toggleMonthlyCalendarQueryParams: noop,
  },
];

export const TaskDataContext = createContext<[Values, Handlers]>(initialValue);
export const useTaskData = () => useContext(TaskDataContext);
