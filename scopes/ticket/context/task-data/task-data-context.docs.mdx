---
description: 'TaskData context.'
labels: ['context']
---

import { TaskDataContext } from './task-data-context';
import { TaskDataProvider } from './task-data-context-provider';
import { MockComponent } from './task-data-context.composition';

## React Theme Context

This is a simple [React Context](https://reactjs.org/docs/context.html) shared as a Bit component.
Use this component to apply a theme as a context to set on it's children.

### Component usage

```tsx
() => {
import React, { useContext } from 'react';
import { ThemeProvider } from './theme-context-provider';
import { ThemeContext } from './theme-context';

<TaskDataProvider color="blue">
  // My lovely children now get a theme!
  <MockComponent />
</TaskDataProvider>;
```

### Using props to customize the theme

Change the color to see the text change:

```tsx live
() => {
  return (
    <TaskDataProvider color="red">
      <MockComponent />
    </TaskDataProvider>
  );
};
```
