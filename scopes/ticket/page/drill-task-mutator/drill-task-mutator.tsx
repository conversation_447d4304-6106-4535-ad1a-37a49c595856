/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-19
 *
 * @packageDocumentation
 */
import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';

import { PlanType } from '@manyun/ticket.model.task';
import { DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';
import { TicketTaskMutator } from '@manyun/ticket.ui.ticket-task-mutator';

export function DrillTaskMutator() {
  const history = useHistory();
  const { mode, id } = useParams<{ mode: string; id: string }>();

  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }} color="default">
      <TicketTaskMutator
        planType={PlanType.DrillPlan}
        unusedFormItems={['manageType', 'splitors', 'fileInfoList']}
        mode={mode as 'edit' | 'copy'}
        id={id}
        onCancel={() => {
          history.goBack();
        }}
        onSuccess={() => {
          history.push(DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH);
        }}
      />
    </Container>
  );
}
