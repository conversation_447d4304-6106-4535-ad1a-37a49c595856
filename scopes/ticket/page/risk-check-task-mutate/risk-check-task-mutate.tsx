import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';

import { PlanType } from '@manyun/ticket.model.task';
import {
  RISK_CHECK_TASK_LIST_ROUTE_PATH,
  generateRiskCheckTaskDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { TicketTaskMutator } from '@manyun/ticket.ui.ticket-task-mutator';

export function RiskCheckTaskMutate() {
  const history = useHistory();
  const { mode, id } = useParams<{ mode: string; id: string }>();

  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }} color="default">
      <TicketTaskMutator
        planType={PlanType.RiskCheckPlan}
        unusedFormItems={['endTime', 'drillLevel', 'drillMajorType', 'fileInfoList']}
        mode={mode as 'edit' | 'copy'}
        id={id}
        onCancel={() => {
          history.push(RISK_CHECK_TASK_LIST_ROUTE_PATH);
        }}
        onSuccess={(taskId?: string) => {
          history.push(generateRiskCheckTaskDetailRoutePath({ id: taskId ?? id }));
        }}
      />
    </Container>
  );
}
