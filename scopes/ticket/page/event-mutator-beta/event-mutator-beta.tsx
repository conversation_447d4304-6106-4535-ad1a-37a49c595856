import React from 'react';
import { useHistory } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { EVENT_LIST_ROUTE_PATH, generateEvnetLocation } from '@manyun/ticket.route.ticket-routes';
import { EventMutatorBeta as EventMutatorBetaForm } from '@manyun/ticket.ui.event-mutator-beta';

export function EventMutatorBeta() {
  const history = useHistory();
  return (
    <Card bordered={false} bodyStyle={{ display: 'flex', justifyContent: 'center', width: '100%' }}>
      <EventMutatorBetaForm
        mode="create"
        onCancel={() => history.push(EVENT_LIST_ROUTE_PATH)}
        onSuccess={eventId => {
          history.push(
            generateEvnetLocation({
              id: eventId,
            })
          );
        }}
      />
    </Card>
  );
}
