import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import { DownloadOutlined } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeCascader } from '@manyun/resource-hub.ui.meta-type-cascader';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import { createItServiceTicket } from '@manyun/ticket.service.create-it-service-ticket';

const tailFormItemLayout = {
  wrapperCol: {
    xs: {
      span: 24,
      offset: 0,
    },
    sm: {
      span: 20,
      offset: 4,
    },
  },
};
export function InformationTechnologyServiceCreate() {
  const history = useHistory();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const handleOk = () => {
    form.validateFields().then(async values => {
      setLoading(true);
      const { assignee, location, type, description, phone, fileInfoList } = values;
      const { error, data } = await createItServiceTicket({
        taskSubType: type[0],
        thirdOrderType: type[1],
        assigneeInfo: assignee ? { id: assignee.value, userName: assignee.label } : undefined,
        sourceOfOrder: 'DC_BASE',
        blockGuid: location[1],
        description: description,
        phone: phone,
        fileInfoList: fileInfoList,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('新建成功');
      history.push(generateTicketLocation({ ticketType: 'it_service', id: data! }));
    });
  };
  return (
    <Card title="基本信息">
      <Form form={form} style={{ width: '50%' }} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
        <Form.Item label="类型" name="type" rules={[{ required: true, message: '请选择类型' }]}>
          <MetaTypeCascader
            style={{ width: 272 }}
            metaType={['IT_SERVICE', 'ITORDER_THIRD_CATEGORY']}
          />
        </Form.Item>
        <Form.Item label="位置" name="location" rules={[{ required: true, message: '请选择位置' }]}>
          <LocationCascader
            style={{ width: 272 }}
            authorizedOnly
            changeOnSelect={false}
            disabledNoChildsNodes={['IDC']}
            nodeTypes={['IDC', 'BLOCK']}
            allowClear
          />
        </Form.Item>
        <Form.Item label="指派人" name="assignee">
          <UserSelect style={{ width: 272 }} />
        </Form.Item>
        <Form.Item
          label="申请人联系电话"
          name="phone"
          rules={[
            { required: true, message: '申请人联系电话必填' },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '手机号格式错误，请重新输入',
            },
          ]}
        >
          <Input style={{ width: 208 }} allowClear />
        </Form.Item>
        <Form.Item
          label="说明"
          name="description"
          rules={[
            { required: true, message: '请输入说明' },
            { max: 120, message: '最多仅允许输入120个字！' },
          ]}
        >
          <Input.TextArea style={{ width: 348 }} />
        </Form.Item>
        <Form.Item
          name="fileInfoList"
          label="附件"
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
        >
          <McUpload accept=".png,.jpg,.jpeg,.image,.xls,.xlsx,.doc,.docx,.pdf" maxCount={10}>
            <Space direction="vertical">
              <Button icon={<DownloadOutlined />}>点此上传</Button>
              <Typography.Text type="secondary">
                支持扩展名：.png,.jpg,.jpeg,.image,.xls,.xlsx,.doc,.docx,.pdf
              </Typography.Text>
            </Space>
          </McUpload>
        </Form.Item>
        <Form.Item {...tailFormItemLayout}>
          <Space>
            <Button type="primary" loading={loading} onClick={handleOk}>
              提交
            </Button>
            <Button
              onClick={() => {
                history.goBack();
              }}
            >
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
