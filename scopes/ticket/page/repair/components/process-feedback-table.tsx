import React, { useRef, useState } from 'react';

import useDeepCompareEffect from 'use-deep-compare-effect';

import { Button } from '@manyun/base-ui.ui.button';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import type { EditableFormInstance, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';

import { ProcessFeedbackFilesUpload } from './process-feedback-files-upload';

export type FeedBackRow = {
  gmtCreate: string;
  feedback: string;
  resolved: boolean;
  operatorId: number;
  files?: McUploadFileJSON[];
};
export type DataSourceType = {
  id: React.Key;
} & FeedBackRow;
export type ProcessFeedbackTableProps = {
  processFeedbacks: DataSourceType[] | undefined;
  isCurrentUserCreator: boolean;
  handleRowEditing: ({
    id,
    feedbackContent,
    resolved,
  }: {
    id: React.Key;
    feedbackContent: string;
    resolved: boolean;
  }) => void;
  handleEditRowKeys?: (keys: React.Key[]) => void;
  onRefresh?: () => void;
};

export function ProcessFeedbackTable({
  processFeedbacks,
  isCurrentUserCreator,
  handleRowEditing,
  handleEditRowKeys,
  onRefresh,
}: ProcessFeedbackTableProps) {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DataSourceType[] | undefined>([]);
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '反馈时间',
      dataIndex: 'gmtModified',
      valueType: 'dateTime',
      renderFormItem: () => {
        return <Input disabled />;
      },
    },
    {
      title: '反馈内容',
      dataIndex: 'feedback',
      renderFormItem: () => {
        return <Input maxLength={50} />;
      },
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '反馈内容为必填项' }],
        };
      },
    },
    {
      title: '是否解决',
      dataIndex: 'resolved',
      renderFormItem: () => {
        return (
          <Select>
            <Select.Option value>是</Select.Option>
            <Select.Option value={false}>否</Select.Option>
          </Select>
        );
      },
      render(resolved) {
        if (resolved === true) {
          return '是';
        }
        return '否';
      },
    },
    {
      title: '现场照片',
      dataIndex: 'files',
      editable: false,
      render: files => {
        return files && Array.isArray(files) && files.length > 0 ? (
          <SimpleFileList files={files}>
            <Typography.Link>{files.length}</Typography.Link>
          </SimpleFileList>
        ) : (
          0
        );
      },
    },
    {
      title: '操作人',
      dataIndex: 'operatorName',
      renderFormItem: () => {
        return <Input disabled />;
      },
      render(name, { operatorId }) {
        return <UserLink id={operatorId} />;
      },
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: 120,
      render: (text, record, _, action) => [
        <Button
          key="editable"
          type="link"
          compact
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </Button>,
        <ProcessFeedbackFilesUpload
          key="upload"
          targetType="REPAIR_FEEDBACK"
          targetId={record.id.toString()}
          defaultFiles={record.files ?? []}
          onSuccess={() => {
            onRefresh?.();
          }}
        />,
      ],
    },
  ];

  useDeepCompareEffect(() => {
    setDataSource(processFeedbacks || []);
  }, [processFeedbacks]);

  return (
    <EditableProTable
      rowKey="id"
      value={dataSource}
      scroll={{ x: 'max-content' }}
      columns={isCurrentUserCreator ? columns : columns.slice(0, columns.length - 1)}
      editableFormRef={editorFormRef}
      recordCreatorProps={{
        /** 由于该表格不存在新增的功能，因此实际上record也不必存在，但record是必须的props，所以用as处理*/
        record: () => ({} as DataSourceType),
        style: {
          display: 'none',
        },
      }}
      editable={{
        type: 'single',
        editableKeys,
        onSave: async (rowKey, data, row) => {
          return handleRowEditing({
            id: data.id,
            feedbackContent: data.feedback,
            resolved: data.resolved,
          });
        },
        onChange: keys => {
          setEditableRowKeys(keys);
        },
        actionRender: (row, config, dom) => [dom.save, dom.cancel],
      }}
    />
  );
}
