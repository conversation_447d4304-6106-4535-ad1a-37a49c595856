import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import type { ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Typography } from '@manyun/base-ui.ui.typography';

import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import type { RepairSpare } from '@manyun/ticket.service.create-repair-spare';
import { SpareSource } from '@manyun/ticket.service.fetch-repair-spares';

export type DataSourceType = {
  id: React.Key;
  index: number;
  source: SpareSource;
} & RepairSpare;
const spareSourceTextMap = {
  [SpareSource.Outside]: '厂商备件更换',
  [SpareSource.System]: '自有备件出库',
};
export type ManufacturerSparePartsReplacementTableProps = {
  spares: DataSourceType[];
  loading: boolean;
  isCurrentUserCreator: boolean;
  handleRowEditing: (spare: RepairSpare & { id: number }) => void;
  handleRowDeleting: (id: number) => void;
  handleEditRowKeys?: (keys: React.Key[]) => void;
};

export function ManufacturerSparePartsReplacementTable({
  spares,
  isCurrentUserCreator,
  loading,
  handleRowEditing,
  handleRowDeleting,
  handleEditRowKeys,
}: ManufacturerSparePartsReplacementTableProps) {
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '三级分类/备件名称',
      dataIndex: 'name',
      width: 272,
      renderFormItem: () => {
        return <Input maxLength={20} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '备件名称必填！' }],
      }),
      render: (_, { name, source }) => {
        return source === SpareSource.System ? (
          <DeviceTypeText code={name} />
        ) : (
          <Typography.Text ellipsis={{ tooltip: true }}>{name}</Typography.Text>
        );
      },
    },
    {
      title: '品牌',
      dataIndex: 'vendor',
      width: 272,

      renderFormItem: () => {
        return <Input maxLength={32} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '品牌必填！' }],
      }),
    },
    {
      title: '型号/规格',
      width: 272,
      dataIndex: 'productModel',
      renderFormItem: () => {
        return <Input maxLength={32} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '型号/规格必填！' }],
      }),
      render: (_, { productModel }) => (
        <Typography.Text ellipsis={{ tooltip: true }}>{productModel}</Typography.Text>
      ),
    },

    {
      title: '数量',
      width: 122,
      dataIndex: 'num',
      renderFormItem: () => {
        return <InputNumber min={1} precision={0} max={9999} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '数量必填！' }],
      }),
    },
    {
      title: '备件来源',
      width: 122,
      dataIndex: 'source',

      editable: false,
      render: (_, { source }) => spareSourceTextMap[source],
    },
    {
      title: '说明',
      dataIndex: 'description',
      renderFormItem: () => {
        return <Input maxLength={120} />;
      },
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '说明必填！' }],
        };
      },
      render: (_, { description, source, sourceNo }) => {
        return source === SpareSource.Outside ? (
          <Typography.Paragraph style={{ marginBottom: 0 }} ellipsis={{ rows: 2, tooltip: true }}>
            {description}
          </Typography.Paragraph>
        ) : (
          <>
            <span>出库单号</span>
            <Link
              key={sourceNo}
              to={generateTicketLocation({ id: sourceNo, ticketType: 'warehouse' })}
            >
              {sourceNo}
            </Link>
          </>
        );
      },
    },
    {
      title: '操作',
      valueType: 'option',
      width: 104,
      fixed: 'right',
      render: (text, record, _, action) => {
        return record.source === SpareSource.Outside
          ? [
              <Button
                key="editable"
                type="link"
                compact
                onClick={() => {
                  action?.startEditable?.(record.id);
                }}
              >
                编辑
              </Button>,
              <Button
                key="delete"
                type="link"
                compact
                onClick={() => {
                  handleRowDeleting(record.id as number);
                }}
              >
                删除
              </Button>,
            ]
          : '--';
      },
    },
  ];

  return (
    <EditableProTable
      rowKey="id"
      loading={loading}
      scroll={{ x: 'max-content' }}
      value={spares}
      columns={isCurrentUserCreator ? columns : columns.slice(0, columns.length - 1)}
      recordCreatorProps={{
        /** 由于该表格不存在新增的功能，因此实际上record也不必存在，但record是必须的props，所以用as处理*/
        record: () => ({}) as DataSourceType,
        style: {
          display: 'none',
        },
      }}
      editable={{
        type: 'single',
        editableKeys,
        onSave: async (rowKey, data, row) => {
          const { index, id, ...rest } = data;
          return handleRowEditing({ ...rest, id: id as number });
        },

        onChange: keys => {
          setEditableRowKeys(keys);
        },
        actionRender: (row, config, dom) => [dom.save, dom.cancel],
      }}
    />
  );
}
