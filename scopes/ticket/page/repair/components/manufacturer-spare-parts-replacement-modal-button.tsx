import React, { useState } from 'react';

import shortid from 'shortid';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { EditableProTable, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { createRepairSpare } from '@manyun/ticket.service.create-repair-spare';
import type { RepairSpare } from '@manyun/ticket.service.create-repair-spare';

export type DataSourceType = {
  id: React.Key;
  index: number;
} & RepairSpare;
export type ManufacturerSparePartsReplacementModalButtonProps = {
  idcTag: string;
  taskNo: string;
  onSuccess?: () => void;
} & Pick<ButtonProps, 'type'>;

export function ManufacturerSparePartsReplacementModalButton({
  type,
  idcTag,
  taskNo,
  onSuccess,
}: ManufacturerSparePartsReplacementModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DataSourceType[]>([]);
  const handleSubmit = async () => {
    setLoading(true);
    const { error } = await createRepairSpare({
      idcTag,
      taskNo,
      repairSpareList: dataSource?.map(item => {
        const { id, index, ...rest } = item;

        return { ...rest };
      }),
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }

    setVisible(false);
    message.success('添加成功');
    setDataSource([]);
    onSuccess && onSuccess();
  };
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '备件名称',
      dataIndex: 'name',
      width: 242,
      renderFormItem: () => {
        return <Input maxLength={20} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '备件名称必填！' }],
      }),
    },
    {
      title: '品牌',
      dataIndex: 'vendor',
      renderFormItem: () => {
        return <Input maxLength={32} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '品牌必填！' }],
      }),
    },
    {
      title: '型号/规格',
      width: 242,
      dataIndex: 'productModel',
      renderFormItem: () => {
        return <Input maxLength={32} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '型号/规格必填！' }],
      }),
    },

    {
      title: '数量',
      width: 122,
      dataIndex: 'num',
      renderFormItem: () => {
        return <InputNumber min={1} precision={0} max={9999} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '数量必填！' }],
      }),
    },
    {
      title: '说明',
      dataIndex: 'description',
      renderFormItem: () => {
        return <Input maxLength={120} />;
      },
      formItemProps: () => ({
        rules: [{ required: true, message: '说明必填！' }],
      }),
    },
    {
      title: '操作',
      valueType: 'option',
      width: 120,
      render: (text, record, _, action) => [
        <Button
          key="editable"
          type="link"
          compact
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </Button>,
      ],
    },
  ];

  return (
    <>
      <Button
        type={type}
        compact={type === 'link' || type === 'text'}
        onClick={() => {
          setVisible(true);
        }}
      >
        厂商备件更换
      </Button>
      <Modal
        title="厂商备件更换"
        okText="提交"
        style={{ maxWidth: '85%', maxHeight: '80%', minWidth: 1260 }}
        open={visible}
        confirmLoading={loading}
        destroyOnClose
        okButtonProps={{ disabled: dataSource.length === 0 || editableKeys.length > 0 }}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
          setDataSource([]);
          setEditableRowKeys([]);
        }}
      >
        <EditableProTable
          style={{ width: '100%' }}
          size="small"
          rowKey="id"
          value={dataSource}
          columns={columns}
          scroll={{ x: 'max-content' }}
          recordCreatorProps={{
            position: 'top',
            creatorButtonText: '添加厂商备件',
            record: () => ({ id: shortid() } as DataSourceType),
          }}
          editable={{
            type: 'single',
            editableKeys,
            onChange: keys => {
              setEditableRowKeys(keys);
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
          onChange={value => {
            setDataSource(value);
          }}
        />
      </Modal>
    </>
  );
}
