import React, { useState } from 'react';

import MessageOutlined from '@ant-design/icons/es/icons/MessageOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { feedbackRepairException } from '@manyun/ticket.service.feedback-repair-exception';

export type FeedbackExceptionModalButtonProps = {
  idcTag: string;
  blockGuid: string;
  taskNo: string;
  currentProcessor: string;
  onSuccess?: () => void;
};
export type FormValue = {
  reason: string;
  applyType: string;
  blockGuid: string;
};
export function FeedbackExceptionModalButton({
  idcTag,
  blockGuid,
  taskNo,
  currentProcessor,
  onSuccess,
}: FeedbackExceptionModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<FormValue>();
  const handleSubmit = () => {
    form.validateFields().then(async value => {
      const { blockGuid, reason, applyType } = value;
      setLoading(true);
      const { error } = await feedbackRepairException({
        title: `${currentProcessor}发起维保联系人信息错误`,
        taskNo,
        idcTag,
        blockGuid,
        reason: reason,
        processType: 'COMMON_APPROVAL',
        detailParam: { applyType },
        startProcessParams: { applyType: 'CONTACT_INFO_ERR' },
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }

      setVisible(false);
      message.success('成功反馈维保异常信息');

      onSuccess && onSuccess();
    });
  };

  return (
    <>
      <Tooltip title="反馈异常">
        <MessageOutlined
          style={{ color: `var(--${prefixCls}-primary-color)` }}
          onClick={() => {
            setVisible(true);
          }}
        />
      </Tooltip>

      <Modal
        title="反馈异常"
        okText="提交"
        open={visible}
        confirmLoading={loading}
        afterClose={() => {
          form.resetFields();
        }}
        onOk={handleSubmit}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form
          form={form}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          initialValues={{ applyType: '维保联系人信息错误', blockGuid }}
        >
          <Form.Item label="申请类型" name="applyType">
            <Input disabled />
          </Form.Item>
          <Form.Item label="机房楼栋" name="blockGuid">
            <Input disabled />
          </Form.Item>
          <Form.Item label="说明" name="reason" rules={[{ required: true, message: '说明必填' }]}>
            <Input.TextArea maxLength={120} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
