import React, { useState } from 'react';

import { InboxOutlined } from '@ant-design/icons';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { saveBizFiles } from '@manyun/dc-brain.service.save-biz-files';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';

export type ProcessFeedbackFilesUploadProps = {
  targetType: string;
  targetId: string;
  defaultFiles?: McUploadFileJSON[];
  onSuccess?: () => void;
};
export const ProcessFeedbackFilesUpload = ({
  targetType,
  targetId,
  defaultFiles,
  onSuccess,
}: ProcessFeedbackFilesUploadProps) => {
  const [open, setOpen] = useState(false);
  const [files, setFiles] = useState<McUploadFileJSON[]>([]);
  const [overMaxCount, setOverMaxCount] = useState(false);

  return (
    <>
      <Button
        key="upload"
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          setOverMaxCount(false);
          setFiles(defaultFiles ?? []);
        }}
      >
        上传
      </Button>
      <Modal
        destroyOnClose
        title="上传附件"
        style={{ minWidth: 443 }}
        open={open}
        okText="提交"
        onCancel={() => {
          setOpen(false);
          setFiles([]);
        }}
        onOk={async () => {
          const { error } = await saveBizFiles({
            fileInfos: files,
            fileType: 'FEEDBACK_PHOTO',
            targetType,
            targetId,
          });
          if (error) {
            message.error(error.message);
            return;
          }
          onSuccess?.();
          setOpen(false);
          setFiles([]);
        }}
      >
        <McUpload
          type="drag"
          fileList={files as MixedUploadFile[]}
          accept="image/*"
          maxFileSize={2}
          maxCount={9}
          onChange={info => {
            /**上传第十个时提示文案 */
            if (info.file.status === 'uploading' && files.length === 9) {
              setOverMaxCount(true);
            } else {
              setOverMaxCount(false);
            }
            setFiles(info.fileList as McUploadFileJSON[]);
          }}
        >
          <Space direction="vertical">
            <p>
              <InboxOutlined style={{ fontSize: 48, color: `var(--${prefixCls}-primary-color)` }} />
            </p>
            <Typography.Text>点击或将文件拖拽到这里上传</Typography.Text>
            <Typography.Text type="secondary">支持扩展名：image/*</Typography.Text>
          </Space>
        </McUpload>
        {overMaxCount && (
          <Typography.Text style={{ marginTop: 8 }} type="danger">
            图片上传数量不能超过9张
          </Typography.Text>
        )}
      </Modal>
    </>
  );
};
