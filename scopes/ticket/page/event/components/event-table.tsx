import dayjs from 'dayjs';
import React from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import type { TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { FAULT_TARGET_TYPE_TEXT, FaultTargetType } from '@manyun/ticket.model.event';
import { generateEventDetailRoutePath } from '@manyun/ticket.route.ticket-routes';

export type EventTableProps = {
  operation?: unknown;
  showColumns: unknown[];
  fixedColumns?: unknown[];
} & TableProps<unknown>;
const columns = (operation: unknown, showColumns: unknown, { fixedColumns }: unknown) => {
  let list = [
    {
      title: '事件ID',
      dataIndex: 'id',
      fixed: 'left',

      render: (text: string) => (
        <Link
          key={text}
          target="_blank"
          to={generateEventDetailRoutePath({
            id: text,
          })}
        >
          {text}
        </Link>
      ),
    },
    {
      title: '位置',
      dataIndex: 'blockTag',
      render: (_: unknown, record: { blockTag: unknown; idcTag: unknown }) =>
        record.blockTag || record.idcTag,
    },
    {
      title: '事件来源',
      dataIndex: 'eventSourceName',
    },
    {
      title: '事件级别',
      dataIndex: 'eventLevelName',
      sorter: true,
    },
    {
      title: '事件类型',
      dataIndex: 'topCategoryName',
    },
    {
      title: '事件子类型',
      dataIndex: 'secondCategoryName',
    },
    {
      title: '目标类型',
      dataIndex: 'infoType',
      render: (text: string) => <span>{FAULT_TARGET_TYPE_TEXT[text as FaultTargetType]}</span>,
    },
    {
      title: '目标名称',
      dataIndex: 'deviceModels',
      render(_ignored: unknown, { infoType, deviceModels }: unknown) {
        return <FaultTargetLink type={infoType} causeDevices={deviceModels} />;
      },
    },

    {
      title: '事件描述',
      dataIndex: 'eventDesc',
      render(
        title:
          | boolean
          | React.ReactChild
          | React.ReactFragment
          | React.ReactPortal
          | null
          | undefined
      ) {
        return <Typography.Text ellipsis>{title}</Typography.Text>;
      },
    },
    {
      title: '开始时间',
      dataIndex: 'occurTime',
      sorter: true,
      render: (text: string) => {
        return <span>{dayjs().format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },
    {
      title: '事件状态',
      dataIndex: ['eventStatus', 'desc'],
    },
    {
      title: '处理人',
      dataIndex: 'eventOwnerName',
    },
    {
      title: '创建人',
      dataIndex: 'createUserId',
      render: (text: number) => <UserLink id={text} />,
    },
  ];
  if (showColumns.length) {
    list = list
      .map(item => {
        const showColumnsList = showColumns.filter(
          (showColumnsItem: { dataIndex: string | string[] }) =>
            typeof showColumnsItem === 'object' && showColumnsItem.dataIndex === item.dataIndex
        );
        if (showColumnsList.length) {
          return { ...item, visible: showColumnsList[0] ? showColumnsList[0].visible : false };
        }
        if (showColumns.includes(item.dataIndex)) {
          return item;
        }
        return false;
      })
      .filter(i => i !== false);
  }
  if (Array.isArray(fixedColumns) && fixedColumns.length) {
    fixedColumns.forEach(({ dataIndex, fixed }) => {
      const columnIdx = list.findIndex(clmn => clmn.dataIndex === dataIndex);
      if (columnIdx > -1) {
        list[columnIdx].fixed = fixed;
      }
    });
  }
  if (operation) {
    list.push(operation);
  }
  return list;
};
export const EventTable = ({
  showColumns = [],
  operation = null,
  fixedColumns = [],
  ...props
}: EventTableProps) => {
  return (
    <Table
      size="small"
      scroll={{ x: 'max-content' }}
      columns={columns(operation, showColumns, {
        fixedColumns,
      })}
      {...props}
    />
  );
};
const FaultTargetLink = ({
  type,
  causeDevices,
}: {
  type: FaultTargetType;
  causeDevices: unknown[];
}) => {
  switch (type) {
    case FaultTargetType.Device:
      return (
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {causeDevices.map(item => (
            <span key={item.deviceGuid}>
              <Link
                key={item.deviceGuid}
                to={generateDeviceRecordRoutePath({
                  guid: item.deviceGuid,
                })}
              >
                {item.deviceName}
              </Link>
            </span>
          ))}
        </Space>
      );
    case FaultTargetType.Room:
      return (
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {causeDevices.map(item => (
            <span key={item.deviceGuid}>
              <Typography.Link
                key={item.roomTag}
                onClick={() => {
                  window.open(
                    generateRoomMonitoringUrl({
                      idc: item.idcTag,
                      block: item.blockTag,
                      room: item.roomTag,
                    })
                  );
                }}
              >
                {item.roomTag}
              </Typography.Link>
            </span>
          ))}
        </Space>
      );
    case FaultTargetType.Other:
      return causeDevices[0].deviceName;
    default:
      return '';
  }
};
