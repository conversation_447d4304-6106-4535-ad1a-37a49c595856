import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { EVENT_FALSE_ALARM_LIST } from '@manyun/ticket.model.event';

export type FalseAlarmTypeSelectProps = SelectProps;
export const falseAlarmTypeOptions = () => {
  return EVENT_FALSE_ALARM_LIST.map(({ key, label }) => {
    return (
      <Select.Option key={key} value={key} label={label}>
        {label}
      </Select.Option>
    );
  });
};
export const FalseAlarmTypeSelect = React.forwardRef(
  ({ ...selectProps }: FalseAlarmTypeSelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <Select ref={ref} {...selectProps}>
        {falseAlarmTypeOptions()}
      </Select>
    );
  }
);
