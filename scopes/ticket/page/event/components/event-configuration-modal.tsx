import React, { useEffect, useState } from 'react';

import { SettingOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { configurationEvents } from '@manyun/ticket.service.configuration-events';
import { fetchEventConfiguration } from '@manyun/ticket.service.fetch-event-configuration';

export type EventSkipType = 'SkipRelief' | 'SkipAudit';
export type FormValue = {
  skippedRelieveEventLevels: string[];
  skippedAuditEventLevels: string[];
  skipRules: string[];
};
export function EventConfigurationModal() {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm<FormValue>();
  const skipRules = Form.useWatch('skipRules', form);

  const onOk = async () => {
    const { skippedRelieveEventLevels, skippedAuditEventLevels } = await form.validateFields();
    setLoading(true);
    const { error } = await configurationEvents({
      skippedAuditEventLevels,
      skippedRelieveEventLevels,
    });
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    message.success('事件配置成功，规则即时生效');
    setVisible(false);
  };

  useEffect(() => {
    // 每次弹窗被点开时，请求查询接口，初始化表单的值
    if (visible) {
      (async function () {
        const { error, data } = await fetchEventConfiguration();
        if (error) {
          message.error(error.message);
          return;
        }
        const skippedRelieveEventLevels =
          data!.skippedRelieveEventLevels?.length > 0 ? data!.skippedRelieveEventLevels : undefined;
        const skippedAuditEventLevels =
          data!.skippedAuditEventLevels?.length > 0 ? data!.skippedAuditEventLevels : undefined;
        const skipRules = [];
        if (skippedAuditEventLevels) {
          skipRules.push('SkipAudit');
        }
        if (skippedRelieveEventLevels) {
          skipRules.push('SkipRelief');
        }
        form.setFieldsValue({
          skipRules,
          skippedRelieveEventLevels,
          skippedAuditEventLevels,
        });
      })();
    }
  }, [form, visible]);
  return (
    <>
      <Button
        type="link"
        icon={<SettingOutlined />}
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        配置
      </Button>
      <Modal
        title="事件配置"
        width={720}
        okButtonProps={{ loading }}
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        onOk={onOk}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item name="skipRules" noStyle>
            <Checkbox.Group style={{ width: '100%' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Checkbox value="SkipRelief">支持跳过缓解阶段</Checkbox>
                {skipRules?.includes('SkipRelief') && (
                  <Form.Item label="当事件级别为" colon={false}>
                    <Space>
                      <Form.Item
                        name="skippedRelieveEventLevels"
                        rules={[{ required: true, message: '请选择事件级别' }]}
                        noStyle
                      >
                        <MetaTypeSelect
                          style={{ width: 210 }}
                          showSearch
                          optionLabelProp="label"
                          optionFilterProp="label"
                          mode="multiple"
                          maxTagCount="responsive"
                          metaType={MetaType.EVENT_LEVEL}
                          allowClear
                        />
                      </Form.Item>
                      <Typography.Text>
                        时，阶段更新可选择跳过缓解阶段，直接进入解决阶段
                      </Typography.Text>
                    </Space>
                  </Form.Item>
                )}
                <Checkbox value="SkipAudit">支持无需复盘</Checkbox>
                {skipRules?.includes('SkipAudit') && (
                  <Form.Item label="当事件级别为" colon={false}>
                    <Space>
                      <Form.Item
                        name="skippedAuditEventLevels"
                        rules={[{ required: true, message: '请选择事件级别' }]}
                        noStyle
                      >
                        <MetaTypeSelect
                          style={{ width: 210 }}
                          showSearch
                          optionLabelProp="label"
                          optionFilterProp="label"
                          mode="multiple"
                          maxTagCount="responsive"
                          metaType={MetaType.EVENT_LEVEL}
                          allowClear
                        />
                      </Form.Item>
                      <Typography.Text>时，支持无需复盘，直接提交事件评审 </Typography.Text>
                    </Space>
                  </Form.Item>
                )}
              </Space>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
