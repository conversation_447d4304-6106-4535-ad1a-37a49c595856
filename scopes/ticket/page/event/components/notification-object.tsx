import React, { useEffect, useState } from 'react';

import CheckOutlined from '@ant-design/icons/es/icons/CheckOutlined';
import CloseCircleFilled from '@ant-design/icons/es/icons/CloseCircleFilled';
import CloseOutlined from '@ant-design/icons/es/icons/CloseOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import { fetchExclusiveUsersWeb } from '@manyun/auth-hub.service.pm.fetch-exclusive-users';
import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { RoleText } from '@manyun/auth-hub.ui.role-text';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';

export type NotificationSelectObject = {
  key: string | null;
  label: string | null;
  type: 'Role' | 'User';
  value?: string;
};
export type NotificationObjectProps = {
  initializeObjectList: NotificationSelectObject[];

  setParentObjectList: (objectList: NotificationSelectObject[]) => void;
};
const PopRoleList = ({ name, code }: { name: string | null; code: string | null }) => {
  const [userNameList, setUserNameList] = useState<string[]>([]);
  useEffect(() => {
    const getUsersInRole = async () => {
      const { error, data } = await fetchExclusiveUsersWeb({
        roleCodes: typeof code === 'string' ? [code] : [],
        pageSize: 200,
        withDeleted: false,
        page: 1,
      });
      if (error) {
        message.error(error.message);
        return;
      }

      setUserNameList(data.data.map((user: { name: string }) => user.name));
    };
    getUsersInRole();
  }, [code]);
  return (
    <Popover
      content={
        <div style={{ maxHeight: 264, overflowY: 'auto' }}>
          <Space direction="vertical">
            {userNameList.map((user, index) => {
              return <span key={index}>{user}</span>;
            })}
          </Space>
        </div>
      }
      title={null}
    >
      <span key={code}>
        <RoleText code={code ?? ''} />
      </span>
    </Popover>
  );
};
export function NotificationObject({
  setParentObjectList,
  initializeObjectList = [],
}: NotificationObjectProps) {
  const [selectType, setSelectType] = useState<'Role' | 'User'>('Role');
  const [objectList, setObjectList] = useState<NotificationSelectObject[]>(initializeObjectList);
  const [newObject, setNewObject] = useState<NotificationSelectObject>();
  const [isAdding, setIsAdding] = useState<boolean>(false);
  const handleCheck = () => {
    const list = [...objectList];
    if (newObject?.key) {
      if (isUserOrRoleExisting(newObject, list)) {
        message.error('请勿重复添加用户或者角色');
        return;
      }
      list.push(newObject);
      setObjectList(list);
      setNewObject({ key: null, label: null, type: 'Role' });
    } else {
      message.error('请先选择通报对象');
      return;
    }
  };
  const handleDelete = (label: string | null = '') => {
    const list = [...objectList];

    setObjectList(list.filter(object => object.label !== label));
  };

  useEffect(() => {
    setObjectList(initializeObjectList);
    setIsAdding(false);
  }, [initializeObjectList]);
  useEffect(() => {
    setParentObjectList(objectList);
  }, [objectList, setParentObjectList]);
  return (
    <Space size={24}>
      <Space size={16}>
        {objectList?.map((object, index) => (
          <Space key={index}>
            {object.type === 'User' ? (
              <UserLink id={parseInt(object.key as string)} />
            ) : (
              <PopRoleList name={object?.label} code={object.value ?? ''}></PopRoleList>
            )}
            <CloseCircleFilled
              style={{ color: `var(--disabled-color)` }}
              onClick={() => {
                handleDelete(object?.label);
              }}
            />
          </Space>
        ))}
      </Space>
      {isAdding ? (
        <Space size={16}>
          <Space size={0}>
            <Select
              style={{ width: 104 }}
              value={selectType}
              onChange={value => {
                setSelectType(value);
              }}
            >
              <Select.Option label="角色" key="Role">
                角色
              </Select.Option>
              <Select.Option label="用户" key="User">
                用户
              </Select.Option>
            </Select>
            {selectType === 'User' ? (
              <UserSelect
                style={{ width: 210 }}
                value={newObject}
                onChange={value =>
                  setNewObject({ key: value.key, label: value.label, type: 'User' })
                }
              />
            ) : (
              <RoleSelect
                style={{ width: 210 }}
                value={newObject}
                fieldNames={{ value: 'code', label: 'name' }}
                labelInValue
                placeholder="输入角色名查询"
                onChange={value => {
                  setNewObject({
                    value: value.value,
                    key: value.key,
                    label: value.label,
                    type: 'Role',
                  });
                }}
              />
            )}
          </Space>

          <CheckOutlined
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={handleCheck}
          />
          <CloseOutlined
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={() => {
              setIsAdding(false);
            }}
          />
        </Space>
      ) : (
        <Button
          type="link"
          compact
          onClick={() => {
            setIsAdding(true);
          }}
        >
          添加
        </Button>
      )}
    </Space>
  );
}

const isUserOrRoleExisting = (
  newObject: NotificationSelectObject,
  objectList: NotificationSelectObject[]
) => {
  return (
    objectList.filter(object => object.key === newObject.key && newObject.type === object.type)
      ?.length > 0
  );
};
