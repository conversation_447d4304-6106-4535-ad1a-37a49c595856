import dayjs from 'dayjs';
import React, { useEffect } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { EventNotification } from '@manyun/ticket.model.event';

export type NotificationDetailProps = {
  notificationInfo: EventNotification;
};

export function NotificationDetail({ notificationInfo }: NotificationDetailProps) {
  const { eventLevel, ...rest } = notificationInfo;
  const [{ data }, { readMetaData }] = useMetaData(MetaType.EVENT_LEVEL);
  useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventLevel]);

  const eventLevelText =
    eventLevel && data.entities[eventLevel] ? data.entities[eventLevel].name : '未知';
  return (
    <Typography.Paragraph
      style={{
        width: '100%',
        maxHeight: 400,

        whiteSpace: 'pre-wrap',
        textAlign: 'left',
        overflowY: 'auto',
      }}
      copyable={{
        tooltips: false,
        icon: [
          <Button key="copy" style={{ position: 'absolute', bottom: 8, right: 8 }}>
            复制
          </Button>,
          <Button key="copied" style={{ position: 'absolute', bottom: 8, right: 8 }}>
            已复制
          </Button>,
        ],
      }}
    >
      {generateReportContent({
        notificationInfo: { ...rest, eventLevel: eventLevelText },
      })}
    </Typography.Paragraph>
  );
}

const generateReportContent = ({ notificationInfo }: NotificationDetailProps) => {
  const eventProgressText = notificationInfo.eventProgress?.length
    ? notificationInfo.eventProgress.map((item, index) => `\n${index + 1}.${item}`).join('')
    : '--';
  const viewContent = `${notificationInfo.eventFirstLine ?? '--'}\n【事件描述】：${
    notificationInfo.eventDesc ?? '--'
  }\n【事件级别】：${notificationInfo.eventLevel}\n【发生时间】：${
    notificationInfo.occurTime
      ? dayjs(notificationInfo.occurTime).format('YYYY-MM-DD HH:mm:ss')
      : '--'
  }\n【事件来源】：${notificationInfo.eventSource ?? '--'}\n【机房楼栋】：${
    notificationInfo.blockTag ?? '--'
  }\n【目标类型】：${notificationInfo.infoType ?? '--'}\n【目标名称】：${
    notificationInfo.causeDevices ?? '--'
  }\n【事件类型】：${notificationInfo.eventCategory ?? '--'}\n【事件状态】：${
    notificationInfo.eventStatus ?? '--'
  }\n【影响范围】 ：${notificationInfo.eventInfluence ?? '--'}\n【事件原因】：${
    notificationInfo.causeDesc ?? '--'
  }\n【处理进度】：${eventProgressText}\n\n\n`;
  return viewContent;
};
