import React, { use<PERSON>allback, useEffect, useMemo, useState } from 'react';

import { SettingOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import {
  EventSpecificProcessStatus,
  EventSpecificProcessStatusFieldName,
  getEventLocales,
} from '@manyun/ticket.model.event';
import type {
  EventProcessPhaseConfigCondition,
  ProcessEngineConfig,
} from '@manyun/ticket.model.event';
import { configurationEvents } from '@manyun/ticket.service.configuration-events';
import { fetchEventConfiguration } from '@manyun/ticket.service.fetch-event-configuration';

export type EventSkipType = 'SkipRelief' | 'SkipAudit';
export type FormValue = {
  skippedRelieveEventLevels: string[];
  skippedAuditEventLevels: string[];
  skipRules: string[];
  emergencyFinishStandard: string;
  emergencyAllowSkip: boolean;
  emergencyLimitOperation: string;
  repairFinishStandard: string;
  repairAllowSkip: boolean;
  repairLimitOperation: string;
  recoveryFinishStandard: string;
  recoveryAllowSkip: boolean;
  recoveryLimitOperation: string;
};

export function EventConfigurationDrawerButton() {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const locales = useMemo(() => getEventLocales(), []);
  const [processConfig, setProcessConfig] = useState<ProcessEngineConfig | undefined>();
  const skipRules = Form.useWatch('skipRules', form);

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onOk = () => {
    form.validateFields().then(async (value: FormValue) => {
      const { skippedRelieveEventLevels, skippedAuditEventLevels } = value;
      const fromProcessConfig = { ...processConfig };
      fromProcessConfig.emergency = {
        condition: value[`emergencyFinishStandard`],
        canSkip: value[`emergencyAllowSkip`],
        optAuth: value[`emergencyLimitOperation`],
      };

      fromProcessConfig.repair = {
        condition: value[`repairFinishStandard`],
        canSkip: value[`repairAllowSkip`],
        optAuth: value[`repairLimitOperation`],
      };
      fromProcessConfig.recovery = {
        condition: value[`recoveryFinishStandard`],
        canSkip: value[`recoveryAllowSkip`],
        optAuth: value[`recoveryLimitOperation`],
      };
      setLoading(true);
      const { error } = await configurationEvents({
        skippedAuditEventLevels,
        skippedRelieveEventLevels,
        processConfig: fromProcessConfig,
      });
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      message.success('事件配置成功，规则即时生效');
      setOpen(false);
    });
  };
  useEffect(() => {
    // 每次弹窗被点开时，请求查询接口，初始化表单的值
    if (open) {
      (async function () {
        const { error, data } = await fetchEventConfiguration();
        if (error) {
          message.error(error.message);
          return;
        }
        const skippedRelieveEventLevels =
          data!.skippedRelieveEventLevels?.length > 0 ? data!.skippedRelieveEventLevels : undefined;
        const skippedAuditEventLevels =
          data!.skippedAuditEventLevels?.length > 0 ? data!.skippedAuditEventLevels : undefined;
        const skipRules = [];
        if (skippedAuditEventLevels) {
          skipRules.push('SkipAudit');
        }
        if (skippedRelieveEventLevels) {
          skipRules.push('SkipRelief');
        }
        form.setFieldsValue({
          skipRules,
          skippedRelieveEventLevels,
          skippedAuditEventLevels,
          emergencyFinishStandard: data?.processConfig?.emergency?.condition,
          emergencyAllowSkip: data?.processConfig?.emergency?.canSkip,
          emergencyLimitOperation: data?.processConfig?.emergency?.optAuth,
          repairFinishStandard: data?.processConfig?.emergency?.condition,
          repairAllowSkip: data?.processConfig?.repair?.canSkip,
          repairLimitOperation: data?.processConfig?.repair?.optAuth,
          recoveryFinishStandard: data?.processConfig?.recovery?.condition,
          recoveryAllowSkip: data?.processConfig?.recovery?.canSkip,
          recoveryLimitOperation: data?.processConfig?.recovery?.optAuth,
        });
        setProcessConfig(data?.processConfig);
      })();
    }
  }, [form, open]);
  return (
    <>
      <Tooltip title="配置">
        <SettingOutlined size={24} style={{ marginRight: 8 }} onClick={showDrawer} />
      </Tooltip>
      <Drawer
        title="事件配置"
        placement="right"
        open={open}
        width={554}
        bodyStyle={{ overflow: 'hidden', overflowY: 'auto' }}
        destroyOnClose
        extra={
          <Space>
            <Popconfirm
              title={
                <Typography.Text>
                  确认取消？当前流程中已修改配置项，
                  <br />
                  如取消则修改的配置项将不会保存
                </Typography.Text>
              }
              style={{ width: 304 }}
              showCancel={false}
              okText="确认取消"
              onCancel={e => e?.stopPropagation()}
              onConfirm={async e => {
                e?.stopPropagation();
                setOpen(false);
              }}
            >
              <Button>取消</Button>
            </Popconfirm>
            <Button type="primary" loading={loading} onClick={onOk}>
              确定
            </Button>
          </Space>
        }
        onClose={onClose}
      >
        <Form
          size="small"
          preserve={false}
          form={form}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Timeline style={{ height: 800, width: 506 }} pending={false}>
            <Timeline.Item style={{ minHeight: 24 }}>
              <Typography.Text strong>{locales.specificProcessStatus.YG_CREATED} </Typography.Text>
            </Timeline.Item>
            {[
              EventSpecificProcessStatus.Emergency,
              EventSpecificProcessStatus.Fix,
              EventSpecificProcessStatus.Recovery,
            ].map(status => (
              <Timeline.Item key={status}>
                <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
                  <Typography.Text strong>{locales.specificProcessStatus[status]}</Typography.Text>
                  <Card
                    style={{ width: 466 }}
                    bodyStyle={{ display: 'flex', alignItems: 'center' }}
                    size="small"
                  >
                    <ConfigFormContent
                      fieldName={EventSpecificProcessStatusFieldName[status]!}
                      processConfig={processConfig}
                      form={form}
                    />
                  </Card>
                </Space>
              </Timeline.Item>
            ))}
            <Timeline.Item style={{ minHeight: 24 }}>
              <Typography.Text strong>{locales.specificProcessStatus.YG_FINISHING}</Typography.Text>
            </Timeline.Item>
            <Timeline.Item>
              <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
                <Typography.Text strong> {locales.specificProcessStatus.YG_REVIEW}</Typography.Text>
                <Card
                  style={{ width: 466 }}
                  bodyStyle={{ display: 'flex', alignItems: 'center' }}
                  size="small"
                >
                  <Form.Item style={{ marginBottom: 20 }} name="skipRules" noStyle>
                    <Checkbox.Group style={{ width: '100%' }}>
                      <Space direction="vertical" style={{ width: '100%' }} size="large">
                        <Checkbox value="SkipAudit">支持无需复盘</Checkbox>
                        {skipRules?.includes('SkipAudit') && (
                          <Form.Item style={{ width: 560 }} label="当事件级别为" colon={false}>
                            <Space>
                              <Form.Item
                                name="skippedAuditEventLevels"
                                rules={[{ required: true, message: '请选择事件级别' }]}
                                noStyle
                              >
                                <MetaTypeSelect
                                  style={{ width: 96 }}
                                  showSearch
                                  optionLabelProp="label"
                                  optionFilterProp="label"
                                  mode="multiple"
                                  maxTagCount="responsive"
                                  metaType={MetaType.EVENT_LEVEL}
                                  allowClear
                                />
                              </Form.Item>
                              <Typography.Text>时，支持无需复盘，直接提交事件评审 </Typography.Text>
                            </Space>
                          </Form.Item>
                        )}
                      </Space>
                    </Checkbox.Group>
                  </Form.Item>
                </Card>
              </Space>
            </Timeline.Item>
            <Timeline.Item style={{ minHeight: 24 }}>
              <Typography.Text strong>{locales.specificProcessStatus.YG_AUDIT}</Typography.Text>
            </Timeline.Item>
            <Timeline.Item style={{ minHeight: 24 }}>
              <Typography.Text strong>{locales.specificProcessStatus.YG_CLOSE}</Typography.Text>
            </Timeline.Item>
          </Timeline>
        </Form>
      </Drawer>
    </>
  );
}

export type ConfigFormContentProps = {
  fieldName: string;
  form: FormInstance<FormValue>;

  processConfig?: ProcessEngineConfig;
};

function ConfigFormContent({ fieldName, form, processConfig }: ConfigFormContentProps) {
  const locales = useMemo(() => getEventLocales(), []);

  const phaseConditionOptions = useMemo(() => {
    const statusKeys: EventProcessPhaseConfigCondition[] = Object.keys(
      locales.specificProcessPhaseConfigCondition
    ).filter(item => item !== '__self') as EventProcessPhaseConfigCondition[];

    return statusKeys.map(item => ({
      label: locales.specificProcessPhaseConfigCondition[item],
      value: item,
    }));
  }, [locales]);
  const initialForm = useCallback(() => {
    switch (fieldName) {
      case 'emergency':
        form.setFieldsValue({
          [`${fieldName}FinishStandard`]: processConfig?.emergency?.condition,
          [`${fieldName}AllowSkip`]: processConfig?.emergency?.canSkip,
          [`${fieldName}LimitOperation`]: processConfig?.emergency?.optAuth,
        });
        break;
      case 'repair':
        form.setFieldsValue({
          [`${fieldName}FinishStandard`]: processConfig?.repair?.condition,
          [`${fieldName}AllowSkip`]: processConfig?.repair?.canSkip,
          [`${fieldName}LimitOperation`]: processConfig?.repair?.optAuth,
        });
        break;
      case 'recovery':
        form.setFieldsValue({
          [`${fieldName}FinishStandard`]: processConfig?.recovery?.condition,
          [`${fieldName}AllowSkip`]: processConfig?.recovery?.canSkip,
          [`${fieldName}LimitOperation`]: processConfig?.recovery?.optAuth,
        });
        break;
    }
  }, [
    fieldName,
    form,
    processConfig?.emergency?.canSkip,
    processConfig?.emergency?.condition,
    processConfig?.emergency?.optAuth,
    processConfig?.recovery?.canSkip,
    processConfig?.recovery?.condition,
    processConfig?.recovery?.optAuth,
    processConfig?.repair?.canSkip,
    processConfig?.repair?.condition,
    processConfig?.repair?.optAuth,
  ]);

  useEffect(() => {
    initialForm();
  }, [initialForm]);

  return (
    <Form size="small" form={form}>
      <Form.Item name={`${fieldName}FinishStandard`} label="阶段完成条件">
        <Select style={{ width: 216 }} options={phaseConditionOptions} />
      </Form.Item>
      <Form.Item name={`${fieldName}AllowSkip`} label="阶段支持跳过">
        <Radio.Group
          options={[
            { label: '是', value: true },
            { label: '否', value: false },
          ]}
        />
      </Form.Item>
      <Form.Item
        style={{ marginBottom: 0 }}
        name={`${fieldName}LimitOperation`}
        label="阶段操作限权"
      >
        <Radio.Group
          options={[
            { label: '责任人', value: 'OWNER' },
            { label: '有位置资源权限的用户', value: 'BLOCK' },
          ]}
        />
      </Form.Item>
    </Form>
  );
}
