import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import type { TablePaginationConfig } from '@manyun/base-ui.ui.table';

import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { EVENT_CENTER_NEW } from '@manyun/ticket.route.ticket-routes';
import { selectEventList } from '@manyun/ticket.state.event';
import { EventLevelSelect } from '@manyun/ticket.ui.event-level-select';

import { METADATA_TYPE } from '@manyun/dc-brain.legacy.constants';

import { EventTable } from './components/event-table';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export type EventProps = any;

export function Event(props: EventProps) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [form] = Form.useForm<any>();
  const dispatch = useDispatch();
  const eventCenterList = useSelector(selectEventList());
  // useEffect(() => {
  //   dispatch(getEventList({ pageNum: 1, pageSize: 10 }));
  // }, [dispatch]);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const getParams = (value: any) => {
    // const filedValues = Object.keys(this.props.searchValues)?.reduce((map, filedname) => {
    //   map[filedname] = this.props.searchValues[filedname]?.value;
    //   return map;
    // }, {}) as any;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const filedValues: any = value;
    const { location, categorys, occurBeginTime, eventId, ownerName, createUserId, ...rest } =
      filedValues;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const topCategorys: any = [];
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const secondCategorys: any = [];
    categorys?.forEach((item: string) => {
      const categorysValue = item.split('_$_');
      if (categorysValue[0] === METADATA_TYPE['EVENT_TOP_CATEGORY']) {
        topCategorys.push(categorysValue[1]);
      }
      if (categorysValue[0] === METADATA_TYPE['EVENT_SECOND_CATEGORY']) {
        secondCategorys.push(categorysValue[1]);
      }
    });
    const spaceGuid = location?.split('.');
    return {
      ...rest,
      createUserId: createUserId?.value,
      ownerName: ownerName?.label,
      eventId: Number(eventId),
      occurBeginTime: occurBeginTime?.[0],
      occurEndTime: occurBeginTime?.[1],
      topCategorys: topCategorys,
      secondCategorys: secondCategorys,
      blockTags: spaceGuid?.length === 2 ? [location] : undefined,
      idcTags: location && spaceGuid.length === 1 ? [spaceGuid[0]] : undefined,
    };
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const onSearch = (value: any) => {
    // dispatch(
    //   getEventList({
    //     pageNum: eventCenterList.pageNum,
    //     pageSize: eventCenterList.pageSize,
    //     ...getParams(value),
    //   })
    // );
  };
  const changeEventTable = (
    pagination: TablePaginationConfig,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    filters: Record<string, any | null>,
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    sorter: any
  ) => {
    const { current, pageSize } = pagination;
    const { field, order } = sorter;
    if (field === 'eventLevelName') {
      // const params = this.getParams();
      // dispatch(
      //   getEventList({
      //     pageNum: current,
      //     pageSize,
      //     orderByEventLevelDesc: order === 'descend' ? true : false,
      //     // ...getParams(),
      //   })
      // );
    } else {
      // const params = this.getParams();
      // dispatch(
      //   getEventList({
      //     pageNum: current,
      //     pageSize,
      //     orderByGmtCreateDesc: order === 'descend' || !order ? true : false,
      //     // ...getParams(),
      //   })
      // );
    }
  };
  return (
    <Space direction="vertical" size={16} style={{ width: '100%', height: '100%' }}>
      <Card>
        {/*      eslint-disable-next-line @typescript-eslint/no-explicit-any
         */}
        <QueryFilter<any>
          form={form}
          items={[
            {
              label: '事件ID',
              name: 'eventId',
              control: <Input aria-label="instId" allowClear />,
            },

            {
              label: '位置',
              name: 'location',
              control: <LocationTreeSelect authorizedOnly allowClear includeVirtualBlocks />,
            },
            {
              label: '设备名称',
              name: 'deviceGuid',
              control: <DeviceSelect style={{ width: '100%' }} allowClear />,
            },

            {
              label: '事件级别',
              name: 'eventLevel',
              control: (
                <EventLevelSelect
                  allowClear
                  showSearch
                  subscribedOnly
                  optionFilterProp="title"
                  style={{ width: '100%' }}
                />
              ),
            },
          ]}
          onSearch={value => {
            onSearch({
              ...value,
            });
          }}
          // onReset={onReset}
        />
      </Card>
      <Card>
        <Space direction="vertical" size={16} style={{ width: '100%' }}>
          <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
            <Link target="_blank" to={{ pathname: EVENT_CENTER_NEW }}>
              <Button type="primary">新建</Button>
            </Link>

            <Space>
              <Button type="link">导出todo</Button>
              <Button type="link">定制列todo</Button>
            </Space>
          </div>
          <EventTable
            showColumns={[
              'id',
              'blockTag',
              'eventSourceName',
              'eventLevelName',
              'topCategoryName',
              'secondCategoryName',
              'infoType',
              'deviceModels',
              'eventDesc',
              'occurTime',
              'eventStatus.desc',
              'eventOwnerName',
              'createUserId',
            ]}
            pagination={{
              total: eventCenterList.total,
              current: eventCenterList.pageNum,
              pageSize: eventCenterList.pageSize,
            }}
            onChange={(pagination, filters, sorter) => {
              changeEventTable(pagination, filters, sorter);
            }}
            dataSource={eventCenterList.list}
          />
        </Space>
      </Card>
    </Space>
  );
}
