import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useLazyChangeOnlineTemplate } from '@manyun/ticket.gql.client.tickets';
import { CHANGE_TEMPLATE_STATUS_TEXT_MAP } from '@manyun/ticket.model.change';
import { ChangeStep, changeOnlineToJson } from '@manyun/ticket.page.change-online-mutator';

import { ApproveView } from './components/approve-view';

export function ChangeOnlineTemplateDetail() {
  const { id } = useParams<{ id: string }>();
  const [expandAll, setExpandAll] = useState(true);

  const [fetchDetail, { data, refetch }] = useLazyChangeOnlineTemplate();
  const detailInfo = data?.changeOnlineTemplate?.data ?? {};
  useEffect(() => {
    fetchDetail({
      variables: {
        templateId: id,
      },
    });
  }, [fetchDetail, id]);
  const { stepCodes, stepMaps } = changeOnlineToJson(data?.changeOnlineTemplate.data ?? {});
  return (
    <>
      <Card
        bordered={false}
        headStyle={{ borderBottom: 0 }}
        bodyStyle={{ padding: '0 24px' }}
        title="模板信息"
      >
        <Descriptions column={4} contentStyle={{ overflow: 'hidden', paddingRight: 16 }}>
          <Descriptions.Item
            label="模板名称"
            contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
          >
            <Typography.Text ellipsis={{ tooltip: detailInfo.templateName }}>
              {detailInfo.templateName}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="适用范围">
            {detailInfo.extJson?.availAreaList?.join(' | ')}
          </Descriptions.Item>

          <Descriptions.Item label="变更专业">
            {detailInfo.reason ? (
              <MetaTypeText code={detailInfo.reason} metaType={MetaType.CHANGE_ONLINE_REASON} />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="变更类别">
            {detailInfo.changeCategory ? (
              <MetaTypeText
                code={detailInfo.changeCategory}
                metaType={MetaType.CHANGE_ONLINE_CATEGORY}
              />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="变更等级">
            {detailInfo.riskLevel ? (
              <MetaTypeText code={detailInfo.riskLevel} metaType={MetaType.CHANGE_ONLINE_LEVEL} />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="状态">
            {detailInfo.templateStatus
              ? CHANGE_TEMPLATE_STATUS_TEXT_MAP[detailInfo.templateStatus as string]
              : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="审批">
            {detailInfo.workFlowId ? (
              <ApproveView
                id={id}
                instId={detailInfo.workFlowId}
                taskStatus={detailInfo.templateStatus}
                onCallBack={refetch}
              />
            ) : (
              '--'
            )}
          </Descriptions.Item>

          <Descriptions.Item label="附件">
            {detailInfo.fileInfoList?.length ? (
              <SimpleFileList files={detailInfo.fileInfoList}>
                <Typography.Link>查看</Typography.Link>
              </SimpleFileList>
            ) : (
              '--'
            )}
          </Descriptions.Item>

          <Descriptions.Item label="创建人">{detailInfo.creatorName ?? '--'}</Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {detailInfo.gmtCreate ? moment(detailInfo.gmtCreate).format('YYYY-MM-DD HH:mm') : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="生效时间">
            {detailInfo.effTime ? moment(detailInfo.effTime).format('YYYY-MM-DD HH:mm') : '--'}
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card
        title={
          <Space>
            <Typography.Text style={{ fontSize: 16, fontWeight: 500 }}>变更步骤</Typography.Text>
            <Button type="link" onClick={() => setExpandAll(!expandAll)}>
              全部收起/展开
            </Button>
          </Space>
        }
        bordered={false}
        style={{ marginTop: -12, marginBottom: 48 }}
        headStyle={{ borderBottom: 0 }}
        bodyStyle={{ padding: '0 24px' }}
      >
        <ChangeStep
          stepMaps={stepMaps}
          stepCodes={stepCodes}
          mode="templateView"
          showOperation={false}
          activeKeyAll={expandAll}

          //  changeOrderId={changeInfo.changeOrderId}
          //  refetch={() => refetchDetail()}
        />
      </Card>
    </>
  );
}
