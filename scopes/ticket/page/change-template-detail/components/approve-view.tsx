import React, { useCallback, useEffect } from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { useLazyBusinessOrderApprovalDetail } from '@manyun/bpm.gql.client.approval';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { ChangeTemplateState } from '@manyun/ticket.model.change';

export type ApproveViewProps = {
  instId: string;
  id: string;
  taskStatus?: string | null;
  onCallBack: () => void;
};

export function ApproveView({ instId, id, taskStatus, onCallBack }: ApproveViewProps) {
  const [getApprovalDetail, { data: approvalDetailData }] = useLazyBusinessOrderApprovalDetail();
  const fetchApprovalDetail = useCallback(() => {
    getApprovalDetail({
      variables: {
        instId,
        permissionType: 'CHANGE_ONLINE_TEMPLATE',
      },
    });
  }, [instId, getApprovalDetail]);

  useEffect(() => {
    fetchApprovalDetail();
  }, [fetchApprovalDetail, taskStatus]);

  if (!approvalDetailData?.businessOrderApprovalDetail) {
    return;
  }

  return (
    <Space>
      <ApprovalRecordsDropdown
        businessOrderInfo={{
          taskNumber: id,
          type: 'CHANGE_ONLINE_TEMPLATE',
          approvalPermissionType: 'CHANGE_ONLINE_TEMPLATE',
          status: taskStatus,
        }}
      />

      {/* <AwaitOperationPeopleTag bpmInstance={approvalDetailData.businessOrderApprovalDetail} /> */}
      {ChangeTemplateState.Approving === taskStatus && (
        <ApprovalOperationButtons
          baseInfo={approvalDetailData.businessOrderApprovalDetail as unknown as BpmInstance}
          getDetail={() => {
            onCallBack();
            fetchApprovalDetail();
          }}
        />
      )}
    </Space>
  );
}
