import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { RISK_STATUS_LIST } from '@manyun/ticket.model.ticket';

export const generateRiskStatusOptions = () => {
  return RISK_STATUS_LIST.map(riskStatus => {
    return (
      <Select.Option key={riskStatus.key} label={riskStatus.label}>
        {riskStatus.label}
      </Select.Option>
    );
  });
};
export type RiskStatusSelectProps = SelectProps;

export const RiskStatusSelect = React.forwardRef(
  ({ ...selectProps }: RiskStatusSelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <Select ref={ref} {...selectProps} allowClear>
        {generateRiskStatusOptions()}
      </Select>
    );
  }
);
