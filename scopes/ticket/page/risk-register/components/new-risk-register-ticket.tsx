import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { DeviceOrSpaceSelect } from '@manyun/resource-hub.ui.device-or-space-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { FaultTargetType } from '@manyun/ticket.model.event';
import { RISK_TYPE_LIST } from '@manyun/ticket.model.ticket';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import { createRiskTicket } from '@manyun/ticket.service.create-risk-ticket';
import { FaultTargetTypeSelect } from '@manyun/ticket.ui.fault-target-type-select';

import { RiskLevelSelect } from './risk-level-select';

export type NewRiskRegisterTicketProps = {
  id: string;
};

export function NewRiskRegisterTicket({ id }: NewRiskRegisterTicketProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<MixedUploadFile[]>([]);
  const history = useHistory();
  const [form] = Form.useForm();
  const { setFieldsValue } = form;
  const riskObjType = Form.useWatch('riskObjType', form);
  const location = Form.useWatch('location', form);
  const handleOk = async () => {
    const formValue = await form.validateFields();
    setLoading(true);
    const { location, riskObjType, riskObjGuids, fileInfoList, ...rest } = formValue;
    const { error, data } = await createRiskTicket({
      ...rest,
      idcTag: generateIdcTag(location),
      blockGuid: location,
      riskObjType,
      riskObjGuids: typeof riskObjGuids === 'string' ? [riskObjGuids] : riskObjGuids,
      fileInfoList: fileList?.map((file: McUploadFile) => ({
        ...McUploadFile.toApiObject(file),
        fileType: null,
      })),
    });
    if (error) {
      message.error(error.message);
      setLoading(false);

      return;
    }
    history.push(generateTicketLocation({ ticketType: 'risk_register', id: data }));

    message.success('创建风险工单成功');
    setLoading(false);
  };
  return (
    <>
      <Card title="基本信息">
        <Form form={form} style={{ width: '50%' }} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="工单子类型"
            name="riskSubType"
            rules={[{ required: true, message: '请选择工单子类型' }]}
          >
            <Select style={{ width: 216 }}>{generateRiskTypeOptions()}</Select>
          </Form.Item>
          <Form.Item
            label="位置"
            name="location"
            rules={[{ required: true, message: '请选择位置' }]}
          >
            <LocationTreeSelect
              includeVirtualBlocks
              style={{ width: 216 }}
              disabledTypes={['IDC']}
              onChange={() => {
                setFieldsValue({ riskObjGuids: [] });
              }}
            />
          </Form.Item>
          <Form.Item
            label="风险等级"
            name="riskLevel"
            rules={[{ required: true, message: '请选择风险等级' }]}
          >
            <RiskLevelSelect style={{ width: 216 }} />
          </Form.Item>

          <Form.Item
            label="风险对象类型"
            name="riskObjType"
            rules={[{ required: true, message: '请选择风险对象类型' }]}
          >
            <FaultTargetTypeSelect
              style={{ width: 395 }}
              onChange={value => {
                setFieldsValue({
                  riskObjGuids: value === 'OTHER' ? '' : undefined,
                });
              }}
            />
          </Form.Item>
          <Form.Item
            label="风险对象名称"
            name="riskObjGuids"
            required
            rules={generateRiskNameRules(riskObjType)}
          >
            <DeviceOrSpaceSelect
              idcTag={generateIdcTag(location)}
              blockTag={generateBlockTag(location)}
              disabled={!(location && riskObjType)}
              type={riskObjType}
              style={{ width: 395 }}
              multiple
            />
          </Form.Item>
          <Form.Item
            label="工单标题"
            name="taskTitle"
            rules={[
              { required: true, message: '请输入工单标题' },
              { max: 20, message: '最多仅允许输入20个字！' },
            ]}
          >
            <Input style={{ width: 395 }} />
          </Form.Item>
          <Form.Item label="附件" name="fileInfoList">
            <Upload
              accept=".png,.jpg,.jpeg,.image,.xls,.xlsx,.doc,.docx,.pdf"
              fileList={fileList}
              maxCount={10}
              onChange={({ file, fileList }) => {
                if (
                  file.status === 'uploading' &&
                  fileList.filter(file => file.status !== 'uploading').length === 10
                ) {
                  message.error('上传附件数量最多10个!');
                  return;
                }
                setFileList(fileList);
              }}
            >
              <Space direction="vertical">
                <Button icon={<DownloadOutlined />}>点此上传</Button>
                <Typography.Text type="secondary">
                  支持扩展名：.png,.jpg,.jpeg,.image,.xls,.xlsx,.doc,.docx,.pdf
                </Typography.Text>
              </Space>
            </Upload>
          </Form.Item>
        </Form>
      </Card>

      <FooterToolBar>
        <Space>
          <Button type="primary" loading={loading} onClick={handleOk}>
            提交
          </Button>
          <Button
            disabled={loading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </>
  );
}

export const generateRiskTypeOptions = () => {
  return RISK_TYPE_LIST.map(riskType => {
    return (
      <Select.Option key={riskType.key} label={riskType.label}>
        {riskType.label}
      </Select.Option>
    );
  });
};

const generateIdcTag = (location: string) => {
  return !location?.includes('.') ? location : location.split('.')[0];
};

const generateBlockTag = (location: string) => {
  return !location?.includes('.') ? '' : location.split('.')[1];
};

const generateRiskNameRules = (type: string) => {
  if (type) {
    if (type === FaultTargetType.Other) {
      return [
        { required: true, message: '请填写风险对象名称', whitespace: true },
        { max: 20, message: '最多仅允许输入20个字！' },
      ];
    }
    return [{ required: true, message: '请填写风险对象名称' }];
  }
  return [];
};
