import dayjs from 'dayjs';
import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import shortid from 'shortid';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import type { EditableFormInstance, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { FAULT_TARGET_TYPE_TEXT, FaultTargetType } from '@manyun/ticket.model.event';
import { BackendTaskStatus, RiskLevelMap, RiskStatusMap } from '@manyun/ticket.model.ticket';
import type { BackendTicket } from '@manyun/ticket.model.ticket';
import { createRiskOperationRecord } from '@manyun/ticket.service.create-risk-operation-record';
import { fetchRiskInfo } from '@manyun/ticket.service.fetch-risk-info';
import type { RiskDevice, RiskInfo } from '@manyun/ticket.service.fetch-risk-info';
import { fetchRiskOperationRecords } from '@manyun/ticket.service.fetch-risk-operation-records';
import type { RiskOperation } from '@manyun/ticket.service.fetch-risk-operation-records';

export type DetailProps = {
  taskNo: string;
  basicInfo: BackendTicket;
};
export type DataSourceType = {
  optStaffId: { label: string; value: number; name: string } | null;
} & Omit<RiskOperation, 'optStaffId'>;

export function Detail({ taskNo, basicInfo }: DetailProps) {
  const [, { checkUserId }] = useAuthorized();
  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<DataSourceType[]>([]);
  const [riskInfo, setRiskInfo] = useState<RiskInfo>();
  const isUserAuthorized = basicInfo.taskAssignee ? checkUserId(basicInfo.taskAssignee) : false;

  const getRiskOperationRecords = async () => {
    const { error, data } = await fetchRiskOperationRecords({ taskNo });
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      setDataSource(
        data.data.map(item => ({
          ...item,
          optStaffId: { value: item.optStaffId, label: '', name: '' },
        }))
      );
    }
  };
  useEffect(() => {
    (async () => {
      const { error, data } = await fetchRiskInfo({ taskNo });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data) {
        setRiskInfo(data);
      }
    })();

    (async () => {
      const { error, data } = await fetchRiskOperationRecords({ taskNo });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data) {
        setDataSource(
          data.data.map(item => ({
            ...item,
            optStaffId: { value: item.optStaffId, label: '', name: '' },
          }))
        );
      }
    })();
  }, [taskNo]);

  return (
    <Space direction="vertical">
      <Descriptions bordered={false} column={4}>
        <Descriptions.Item label="风险对象类型">
          {riskInfo ? FAULT_TARGET_TYPE_TEXT[riskInfo.objType] : '--'}
        </Descriptions.Item>
        <Descriptions.Item label="风险对象名称">
          {riskInfo ? (
            <FaultTargetLink type={riskInfo.objType} causeDevices={riskInfo.riskObjs} />
          ) : (
            '--'
          )}
        </Descriptions.Item>
        <Descriptions.Item label="风险等级">
          {riskInfo ? RiskLevelMap[riskInfo.riskLevel] : '--'}
        </Descriptions.Item>
        <Descriptions.Item label="风险解除状态">
          {riskInfo ? RiskStatusMap[riskInfo.riskStatus] : '--'}
        </Descriptions.Item>
      </Descriptions>

      <EditableProTable
        rowKey="id"
        editableFormRef={editorFormRef}
        value={dataSource}
        scroll={{ x: 'max-content' }}
        columns={generateColumns({ editorFormRef, ticketStatus: basicInfo.taskStatus })}
        recordCreatorProps={
          isUserAuthorized
            ? {
                position: 'top',
                creatorButtonText: '添加处理记录',
                record: () => ({
                  id: shortid(),
                  optStaffId: null,
                  taskNo: '',
                  optInfo: '',
                  optTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
                }),
                style: {
                  display: basicInfo.taskStatus === BackendTaskStatus.PROCESSING ? 'block' : 'none',
                },
              }
            : false
        }
        editable={{
          type: 'single',
          editableKeys,
          onSave: async (rowKey, data, row) => {
            const { optStaffId, optTime, optInfo } = data;
            const { error } = await createRiskOperationRecord({
              staffId: optStaffId!.value,
              optTime: Date.parse(optTime),
              optInfo: optInfo,
              taskNo: taskNo,
            });
            if (error) {
              message.error(error.message);
              return Promise.reject();
            }
            getRiskOperationRecords();
            return Promise.resolve();
          },

          onChange: keys => {
            setEditableRowKeys(keys);
          },
          actionRender: (row, config, dom) => [dom.save, dom.delete],
        }}
      />
    </Space>
  );
}
const generateColumns: ({
  editorFormRef,
  ticketStatus,
}: {
  editorFormRef: React.MutableRefObject<EditableFormInstance<DataSourceType> | undefined>;
  ticketStatus: BackendTaskStatus;
}) => ProColumns<DataSourceType>[] = ({ editorFormRef, ticketStatus }) => {
  const columns: ProColumns<DataSourceType>[] = [
    {
      title: '处理人',
      dataIndex: 'optStaffId',
      width: 200,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '处理人为必填项' }],
        };
      },
      renderFormItem: (_, { recordKey }) => {
        return (
          <UserSelect
            onChange={(_, nodeValue) => {
              if (recordKey) {
                const user = nodeValue['data-user'];
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  optStaffId: {
                    label: user.name,
                    value: user.id,
                    name: user.name,
                  },
                });
              }
            }}
          />
        );
      },
      render: (_, { optStaffId }) => {
        return <UserLink id={optStaffId!.value} />;
      },
    },
    {
      title: '处理内容',
      dataIndex: 'optInfo',
      valueType: 'text',
      formItemProps: () => {
        return {
          rules: [
            { required: true, message: '处理内容为必填项' },
            { max: 50, message: '最多仅允许输入50个字！' },
          ],
        };
      },
    },
    {
      title: '处理时间',
      width: 256,
      dataIndex: 'optTime',
      valueType: 'dateTime',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '处理时间为必填项' }],
        };
      },
    },
  ];
  if (ticketStatus === BackendTaskStatus.PROCESSING) {
    columns.push({
      title: '操作',
      valueType: 'option',
      width: 120,
      fixed: 'right',
      render: (text, record, _, action) => ['--'],
    });
  }
  return columns;
};

const FaultTargetLink = ({
  type,
  causeDevices,
}: {
  type: FaultTargetType;
  causeDevices: RiskDevice[];
}) => {
  switch (type) {
    case FaultTargetType.Device:
      return <FaultTargetDeviceEllipse causeDevices={causeDevices} />;
    case FaultTargetType.Room:
      return <FaultTargetRoomEllipse causeDevices={causeDevices} />;
    case FaultTargetType.Other:
      return <span>{causeDevices[0].deviceName}</span>;
    default:
      return <span>--</span>;
  }
};

const FaultTargetDeviceEllipse = ({ causeDevices }: { causeDevices: RiskDevice[] }) => {
  if (causeDevices.length <= 1) {
    return (
      <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
        {Array.isArray(causeDevices) &&
          causeDevices.map(item => (
            <Link
              key={item.deviceGuid}
              to={generateDeviceRecordRoutePath({
                guid: item.deviceGuid,
              })}
            >
              {`${item.deviceName}(${item.roomTag})`}
            </Link>
          ))}
      </Space>
    );
  } else {
    return (
      <Popover
        overlayInnerStyle={{ maxWidth: 344 }}
        content={
          <Space wrap size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
            {Array.isArray(causeDevices) &&
              causeDevices.map(item => (
                <Link
                  key={item.deviceGuid}
                  to={generateDeviceRecordRoutePath({
                    guid: item.deviceGuid,
                  })}
                >
                  {`${item.deviceName}(${item.roomTag})`}
                </Link>
              ))}
          </Space>
        }
      >
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {Array.isArray(causeDevices) &&
            causeDevices.map((item, index) => {
              if (index <= 0) {
                return (
                  <Link
                    key={item.deviceGuid}
                    to={generateDeviceRecordRoutePath({
                      guid: item.deviceGuid,
                    })}
                  >
                    {`${item.deviceName}(${item.roomTag})`}
                  </Link>
                );
              }
              return null;
            })}
          <Link
            key={causeDevices[1].deviceGuid}
            to={generateDeviceRecordRoutePath({
              guid: causeDevices[1].deviceGuid,
            })}
          >
            {`${causeDevices[1].deviceName[0]}...`}
          </Link>
        </Space>
      </Popover>
    );
  }
};

const FaultTargetRoomEllipse = ({ causeDevices }: { causeDevices: RiskDevice[] }) => {
  if (causeDevices.length <= 2) {
    return (
      <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
        {Array.isArray(causeDevices) &&
          causeDevices.map(({ idcTag, blockTag, roomTag }) => (
            <Typography.Link
              key={roomTag}
              onClick={() => {
                window.open(
                  generateRoomMonitoringUrl({
                    idc: idcTag,
                    block: blockTag,
                    room: roomTag,
                  })
                );
              }}
            >
              {roomTag}
            </Typography.Link>
          ))}
      </Space>
    );
  } else {
    return (
      <Popover
        overlayInnerStyle={{ maxWidth: 344 }}
        content={
          <Space wrap size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
            {Array.isArray(causeDevices) &&
              causeDevices.map(({ idcTag, blockTag, roomTag }) => (
                <Typography.Link
                  key={roomTag}
                  onClick={() => {
                    window.open(
                      generateRoomMonitoringUrl({
                        idc: idcTag,
                        block: blockTag,
                        room: roomTag,
                      })
                    );
                  }}
                >
                  {roomTag}
                </Typography.Link>
              ))}
          </Space>
        }
      >
        <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
          {Array.isArray(causeDevices) &&
            causeDevices.map(({ idcTag, blockTag, roomTag }, index) => {
              if (index <= 1) {
                return (
                  <Typography.Link
                    key={roomTag}
                    onClick={() => {
                      window.open(
                        generateRoomMonitoringUrl({
                          idc: idcTag,
                          block: blockTag,
                          room: roomTag,
                        })
                      );
                    }}
                  >
                    {roomTag}
                  </Typography.Link>
                );
              }
              return null;
            })}
          <Typography.Link
            key={causeDevices[2].roomTag}
            onClick={() => {
              window.open(
                generateRoomMonitoringUrl({
                  idc: causeDevices[2].idcTag,
                  block: causeDevices[2].blockTag,
                  room: causeDevices[2].roomTag,
                })
              );
            }}
          >
            {`${causeDevices[2].roomTag[0]}...`}
          </Typography.Link>
        </Space>
      </Popover>
    );
  }
};
