import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { RISK_LEVEL_LIST } from '@manyun/ticket.model.ticket';

export const generateRiskLevelOptions = () => {
  return RISK_LEVEL_LIST.map(riskLevel => {
    return (
      <Select.Option key={riskLevel.key} label={riskLevel.label}>
        {riskLevel.label}
      </Select.Option>
    );
  });
};
export type RiskLevelSelectProps = SelectProps;

export const RiskLevelSelect = React.forwardRef(
  ({ ...selectProps }: RiskLevelSelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <MetaTypeSelect
        showSearch
        allowClear
        optionLabelProp="label"
        optionFilterProp="label"
        metaType={MetaType.RISK_LEVEL}
        {...selectProps}
      />
    );
  }
);

RiskLevelSelect.displayName = 'RiskLevelSelect';
