import { UploadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import {
  useLazyChangeOnlineTemplate,
  useSaveChangeOnlineTemplate,
  useSubmitChangeOnlineTemplate,
} from '@manyun/ticket.gql.client.tickets';
import { ChangeStep } from '@manyun/ticket.page.change-online-mutator';
import type { ChangeStepInfo } from '@manyun/ticket.page.change-online-mutator';
import {
  changeOnlineToApi,
  changeOnlineToJson,
} from '@manyun/ticket.page.change-online-mutator/change-online-mutator';
import { generateChangeOnlineTemplateLocation } from '@manyun/ticket.route.ticket-routes';

export function ChangeTemplateMutator() {
  const [stepMaps, setStepMaps] = useState<Record<string, ChangeStepInfo>>({});
  const [stepCodes, setStepCodes] = useState<string[]>([]);
  const { id } = useParams<{ id: string }>();
  const { search } = useLocation();
  const { copyId } = getLocationSearchMap<{ copyId?: string }>(search);
  const history = useHistory();
  const [, { checkUserId }] = useAuthorized();

  const [stepForm] = Form.useForm();
  const [form] = Form.useForm();
  const [fetchDetail, { data }] = useLazyChangeOnlineTemplate({
    onCompleted(data) {
      if (data.changeOnlineTemplate.success) {
        const tempalteInfo = changeOnlineToJson(data?.changeOnlineTemplate.data ?? {});
        form.setFieldValue('step', tempalteInfo.stepCodes);
        setStepMaps(tempalteInfo.stepMaps);
        setStepCodes(tempalteInfo.stepCodes);
        form.setFieldsValue({
          ...tempalteInfo.changeInfo,
          availAreaList: data.changeOnlineTemplate.data?.extJson?.availAreaList
            ? data.changeOnlineTemplate.data.extJson.availAreaList.map(item => ({ value: item }))
            : [],
        });
      }
    },
  });

  const [submit, { loading: submitLoading }] = useSubmitChangeOnlineTemplate({
    onCompleted(data) {
      if (!data.submitChangeOnlineTemplate?.success) {
        message.error(data.submitChangeOnlineTemplate?.message);
        return;
      }
      message.success('创建完成');
      history.push(
        generateChangeOnlineTemplateLocation({
          id: data.submitChangeOnlineTemplate.data?.changeTemplateId,
        })
      );
    },
  });
  const [save, { loading: saveLoading }] = useSaveChangeOnlineTemplate({
    onCompleted(data) {
      if (!data.saveChangeOnlineTemplate?.success) {
        message.error(data.saveChangeOnlineTemplate?.message);
        return;
      }
      message.success('创建完成');
      history.push(
        generateChangeOnlineTemplateLocation({
          id: data?.saveChangeOnlineTemplate?.data,
        })
      );
    },
  });
  useEffect(() => {
    if (id || copyId) {
      fetchDetail({ variables: { templateId: id || copyId } });
    }
  }, [id, copyId, fetchDetail]);

  const blockGuidLabelInValue = Form.useWatch('availAreaList', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const blockGuid = blockGuidLabelInValue ? blockGuidLabelInValue.map(item => item.value) : [];
  const [loading, setLoading] = useState(false);

  const [idcTag, blockTag] = (blockGuid?.[0] ?? '').split('.');

  const onSubmit = () => {
    form.validateFields().then(values => {
      stepForm.validateFields().then(() => {
        const params = changeOnlineToApi(form.getFieldsValue(), stepCodes, stepMaps, true);
        submit({
          variables: {
            query: {
              templateId: id,
              ...params,
              availAreaList: values.availAreaList.map(item => item.value),

              templateName: values.templateName,
              reason: values.reason,
              changeCategory: values.changeCategory,
              riskLevel: values.riskLevel,
              fileInfoList: values.fileInfoList?.length
                ? values.fileInfoList.map(obj => ({
                    ...McUploadFile.fromApiObject(
                      McUploadFile.fromJSON(obj).toApiObject()
                    ).toJSON(),
                  }))
                : [],
            },
          },
        });
      });
    });
  };
  const onSave = () => {
    form.validateFields(['templateName', 'availAreaList']).then(() => {
      const values = form.getFieldsValue();
      const params = changeOnlineToApi(values, stepCodes, stepMaps, true);
      save({
        variables: {
          query: {
            templateId: id,
            ...params,
            availAreaList: values.availAreaList.map(item => item.value),
            templateName: values.templateName,
            reason: values.reason,
            changeCategory: values.changeCategory,
            riskLevel: values.riskLevel,
            fileInfoList: values.fileInfoList?.length
              ? values.fileInfoList.map(obj => ({
                  ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
                }))
              : [],
          },
        },
      });
    });
  };
  const getCanEdit = () => {
    return checkUserId(data?.changeOnlineTemplate.data?.creatorId);
  };

  return (
    <>
      <Form colon={false} form={form} labelCol={{ flex: ' 0 0 110px' }} wrapperCol={{ span: 24 }}>
        <Card
          bordered={false}
          headStyle={{ borderBottom: 0 }}
          title={
            <Typography.Title showBadge level={5}>
              基本信息
            </Typography.Title>
          }
          bodyStyle={{ display: 'flex', flexDirection: 'column' }}
        >
          <Form.Item
            label="模板名称"
            name="templateName"
            rules={[
              {
                required: true,
                message: '模板名称必填！',
              },
              {
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ]}
          >
            <Input allowClear style={{ width: 576 }} />
          </Form.Item>

          <Form.Item
            label="适用范围"
            name="availAreaList"
            rules={[
              {
                required: true,
                message: '适用范围必选！',
              },
            ]}
            extra={
              blockGuid?.length > 1 ? (
                <>
                  若「适用范围」选择多个位置，则变更步骤仅支持选择到设备/空间类型，具体的设备/空间需在变更单中选择
                </>
              ) : (
                ''
              )
            }
          >
            <LocationTreeSelect
              authorizedOnly
              showSearch
              allowClear
              multiple
              treeCheckable="true"
              treeCheckStrictly
              labelInValue
              style={{ width: 576 }}
              onChange={(value, a) => {
                const _newMaps: Record<string, ChangeStepInfo> = {};
                stepCodes.map(item => {
                  _newMaps[item] = {
                    ...stepMaps[item],
                    stepDeviceInfoList: [],
                    stepInhibitionItemInfoList: [],
                  };

                  return item;
                });
                setStepMaps(_newMaps);
              }}
            />
          </Form.Item>

          <Form.Item
            label="变更专业"
            name="reason"
            rules={[
              {
                required: true,
                message: '变更专业必填！',
              },
            ]}
          >
            <MetaTypeSelect style={{ width: 216 }} metaType={MetaType.CHANGE_ONLINE_REASON} />
          </Form.Item>

          <Form.Item
            label="变更类别"
            name="changeCategory"
            rules={[
              {
                required: true,
                message: '变更类别必填！',
              },
            ]}
          >
            <MetaTypeSelect style={{ width: 216 }} metaType={MetaType.CHANGE_ONLINE_CATEGORY} />
          </Form.Item>

          <Form.Item
            label="变更等级"
            name="riskLevel"
            rules={[
              {
                required: true,
                message: '变更等级必选！',
              },
            ]}
          >
            <MetaTypeSelect style={{ width: 216 }} metaType={MetaType.CHANGE_ONLINE_LEVEL} />
          </Form.Item>

          <Form.Item
            label="附件"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
              if (fileList.filter(file => file.status === 'uploading').length) {
                setLoading(true);
              } else {
                setLoading(false);
              }
              return fileList;
            }}
          >
            <McUpload
              showAccept
              accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
              showUploadList
              allowDelete
              maxCount={5}
              maxFileSize={20}
              openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 5}
            >
              <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 5}>
                点此上传
              </Button>
            </McUpload>
          </Form.Item>
        </Card>
        <Card
          bordered={false}
          headStyle={{ borderBottom: 0 }}
          style={{ marginBottom: 48 }}
          title={
            <Typography.Title showBadge level={5}>
              变更步骤
            </Typography.Title>
          }
          bodyStyle={{ display: 'flex', flexDirection: 'column' }}
        >
          <Form.Item
            label=""
            name="step"
            dependencies={['step']}
            rules={[
              {
                required: true,
                message: '至少添加一个步骤',
              },
            ]}
          >
            <ChangeStep
              form={stepForm}
              stepMaps={stepMaps}
              stepCodes={stepCodes}
              idcTag={idcTag}
              blockTag={blockTag}
              templateNewLocations={blockGuid}
              mode="templateNew"
              canEdit={id ? getCanEdit() : true}
              setStepInfo={(_stepMaps, _stepCodes) => {
                // form.setFieldValue('step', _stepCodes);
                setStepMaps(_stepMaps);
                setStepCodes(_stepCodes);
              }}
            />
          </Form.Item>
        </Card>
      </Form>
      <FooterToolBar>
        <Space>
          <Button
            type="primary"
            loading={submitLoading || loading || saveLoading}
            onClick={() => {
              onSubmit();
            }}
          >
            提交
          </Button>

          <Button loading={submitLoading || loading || saveLoading} onClick={onSave}>
            保存
          </Button>
        </Space>
      </FooterToolBar>
    </>
  );
}
