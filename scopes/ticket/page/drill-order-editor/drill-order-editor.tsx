/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-14
 *
 * @packageDocumentation
 */
import { UploadOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByType } from '@manyun/resource-hub.service.fetch-meta-data-by-type';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useCreateDrillOrder, useLazyDrillOrderInfo } from '@manyun/ticket.gql.client.drill-order';
import type { DrillConfig } from '@manyun/ticket.gql.client.tickets';
import { useLazyDrillConfig } from '@manyun/ticket.gql.client.tickets';
import { SlaUnit } from '@manyun/ticket.model.task';
import {
  DRILL_LIST_ROUTE_PATH,
  generateDrillOrderRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { DrillConfigSelector } from '@manyun/ticket.ui.drill-config-selector';
import DrillPlanTable from '@manyun/ticket.ui.drill-plan-table';
import { SlaSelect } from '@manyun/ticket.ui.sla-select';

export type DrillOrderEditorProps = {};

export function DrillOrderEditor() {
  const history = useHistory();
  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const [createDrillOrder, { loading }] = useCreateDrillOrder();
  const [getDrillOrderInfo] = useLazyDrillOrderInfo();
  const [getDrillConfig, { data: drillConfigData }] = useLazyDrillConfig();

  const [chosenDrillConfig, setChosenDrillConfig] = useState<DrillConfig>();

  useEffect(() => {
    (async () => {
      if (id) {
        const { error: excMajorError, data: excMajorData } = await fetchMetaDataByType({
          type: MetaType.EXC_MAJOR,
        });
        if (excMajorError) {
          message.error(excMajorError.message);
          return;
        }
        const { error: excLevelError, data: excLevelData } = await fetchMetaDataByType({
          type: MetaType.EXC_LEVEL,
        });
        if (excLevelError) {
          message.error(excLevelError.message);
          return;
        }

        const res = await getDrillOrderInfo({ variables: { execNo: id } });

        const drillOrderInfo = res.data?.drillOrderInfo;
        if (drillOrderInfo) {
          form.setFieldsValue({
            blockGuid: [drillOrderInfo.idcTag, drillOrderInfo.blockGuid],
            title: drillOrderInfo.title,
            desc: drillOrderInfo.desc,
            excMajor: (excMajorData.data ?? [])
              .map(item => item.code)
              .includes(drillOrderInfo.excMajor)
              ? { value: drillOrderInfo.excMajor, label: drillOrderInfo.excMajorName }
              : undefined,
            excLevel: (excLevelData.data ?? [])
              .map(item => item.code)
              .includes(drillOrderInfo.excLevel)
              ? { value: drillOrderInfo.excLevel, label: drillOrderInfo.excLevelName }
              : undefined,
            principal:
              drillOrderInfo.principalId && drillOrderInfo.principalName
                ? { value: drillOrderInfo.principalId, label: drillOrderInfo.principalName }
                : undefined,
            taskSla: {
              sla: drillOrderInfo.taskSla,
              unit: drillOrderInfo.unit,
            },
            fileInfoList: drillOrderInfo.fileInfoList?.map(item => {
              return {
                ...item,
                __typename: undefined,
                uploadUser: {
                  ...item.uploadUser,
                  __typename: undefined,
                },
              };
            }),
          });
          if (drillOrderInfo.exerciseOrderStepItemDoList?.length) {
            const drillConfigId = drillOrderInfo.exerciseOrderStepItemDoList[0].exerciseId;
            const drillConfigDetailRes = await getDrillConfig({ variables: { id: drillConfigId } });
            const drillConfigDetail = drillConfigDetailRes.data?.drillConfig?.data;
            if (drillConfigDetail) {
              setChosenDrillConfig({
                id: drillConfigDetail.id,
                excLevel: drillConfigDetail.excLevel,
                excMajor: drillConfigDetail.excMajor,
                excName: drillConfigDetail.excName,
              } as DrillConfig);
            }
          }
        }
      }
    })();
  }, [form, getDrillConfig, getDrillOrderInfo, id]);

  const onSubmit = async () => {
    if (!chosenDrillConfig) {
      message.error('请选择方案！');
      return;
    }
    const values = await form.validateFields();

    const {
      blockGuid,
      title,
      desc,
      excMajor,
      excLevel,
      principal,
      assigneeInfo,
      taskSla,
      fileInfoList,
    } = values;

    const params = {
      idcTag: blockGuid?.length > 1 ? blockGuid[0] : undefined,
      blockGuid: blockGuid?.length > 1 ? blockGuid[1] : undefined,
      title,
      desc,
      excMajor: excMajor.value,
      excMajorName: excMajor.label,
      excLevel: excLevel.value,
      excLevelName: excLevel.label,
      principalId: principal?.value,
      principalName: principal?.label,
      assigneeInfo: {
        id: assigneeInfo.value,
        userName: assigneeInfo.label,
      },
      taskSla: taskSla.sla,
      unit: taskSla.unit,
      fileInfoList: fileInfoList?.map((item: McUploadFileJSON) => McUploadFile.fromJSON(item)),
      exerciseId: chosenDrillConfig.id,
    };
    const { data } = await createDrillOrder({
      variables: {
        params,
      },
    });
    if (!data?.createDrillOrder?.success) {
      message.error(data?.createDrillOrder?.message);
      return;
    }
    history.push(generateDrillOrderRoutePath({ id: data.createDrillOrder.data }));
    message.success('创建成功！');
  };

  const blockGuidList = Form.useWatch('blockGuid', form);
  const excMajor = Form.useWatch('excMajor', form);
  const excLevel = Form.useWatch('excLevel', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);

  const blockGuid = blockGuidList?.length > 1 ? blockGuidList[1] : undefined;

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
        <Form
          form={form}
          initialValues={{ taskSla: { sla: 0, unit: SlaUnit.Minutes } }}
          labelCol={{ xl: 2 }}
          wrapperCol={{ xl: 22 }}
        >
          <Form.Item
            label="楼栋"
            name="blockGuid"
            rules={[
              {
                required: true,
                message: '楼栋必填',
              },
              {
                type: 'array',
                len: 2,
                message: '必须选择到楼栋',
              },
            ]}
          >
            <LocationCascader
              style={{ width: 216 }}
              nodeTypes={['IDC', 'BLOCK']}
              authorizedOnly
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="演练标题"
            name="title"
            rules={[
              {
                required: true,
                message: '演练标题必填',
              },
              {
                type: 'string',
                max: 100,
                message: '最多输入 100 个字符！',
              },
            ]}
          >
            <Input style={{ width: 546 }} />
          </Form.Item>
          <Form.Item
            label="说明"
            name="desc"
            rules={[
              {
                type: 'string',
                max: 300,
                message: '最多输入 300 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 546 }} />
          </Form.Item>
          <Form.Item
            label="专业类型"
            name="excMajor"
            rules={[
              {
                required: true,
                message: '专业类型必填！',
              },
            ]}
          >
            <MetaTypeSelect
              style={{ width: 216 }}
              labelInValue
              allowClear
              metaType={MetaType.EXC_MAJOR}
            />
          </Form.Item>
          <Form.Item
            label="等级"
            name="excLevel"
            rules={[
              {
                required: true,
                message: '等级必填！',
              },
            ]}
          >
            <MetaTypeSelect
              style={{ width: 216 }}
              labelInValue
              allowClear
              metaType={MetaType.EXC_LEVEL}
            />
          </Form.Item>
          <Form.Item
            label="负责人"
            name="principal"
            tooltip="本次演练主要负责人"
            rules={[
              {
                required: true,
                message: '负责人必填！',
              },
            ]}
          >
            <UserSelect
              style={{ width: 216 }}
              blockGuid={blockGuid}
              disabled={!blockGuid}
              allowClear
              labelInValue
            />
          </Form.Item>
          <Form.Item
            label="指派人"
            name="assigneeInfo"
            tooltip="本次演练单的操作人，仅指派人本人可接单，接单后支持转交"
            rules={[
              {
                required: true,
                message: '指派人必填！',
              },
            ]}
          >
            <UserSelect
              style={{ width: 216 }}
              blockGuid={blockGuid}
              disabled={!blockGuid}
              allowClear
              labelInValue
            />
          </Form.Item>
          <Form.Item
            label="SLA"
            name="taskSla"
            rules={[
              {
                required: true,
                message: 'SLA必填',
              },
              () => ({
                validator(_, value) {
                  if (value.sla === undefined || value.sla === null) {
                    return Promise.reject(new Error('请输入有效的SLA值'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <SlaSelect precision={0} />
          </Form.Item>
          <Form.Item
            label="附件"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <McUpload
              key="upload"
              accept=".docx,.pdf,image/*,.xls,.xlsx"
              maxFileSize={20}
              maxCount={5}
            >
              {fileInfoList && fileInfoList?.length >= 5 ? null : (
                <>
                  <Button icon={<UploadOutlined />}>上传</Button>
                  <Typography.Text type="secondary">
                    支持扩展名：.docx,.pdf,image/*,.xls,.xlsx
                  </Typography.Text>
                </>
              )}
            </McUpload>
          </Form.Item>
        </Form>
        <Typography.Title showBadge level={5}>
          演练方案
        </Typography.Title>
        <Space size="middle">
          <DrillConfigSelector
            disabled={!excMajor || !excLevel || !blockGuid}
            blockGuid={blockGuid}
            excLevel={excLevel?.value}
            excMajor={excMajor?.value}
            drillConfig={chosenDrillConfig}
            onCallBack={async drillConfig => {
              setChosenDrillConfig(drillConfig);
              if (drillConfig.id) {
                await getDrillConfig({ variables: { id: drillConfig.id } });
              }
            }}
          />

          <Typography.Text strong>{chosenDrillConfig?.excName}</Typography.Text>
          <Typography.Text type="secondary">
            专业类型：
            {chosenDrillConfig?.excMajor ? (
              <MetaTypeText code={chosenDrillConfig.excMajor} metaType={MetaType.EXC_MAJOR} />
            ) : (
              '--'
            )}
          </Typography.Text>
          <Typography.Text type="secondary">
            等级：
            {chosenDrillConfig?.excLevel ? (
              <MetaTypeText code={chosenDrillConfig.excLevel} metaType={MetaType.EXC_LEVEL} />
            ) : (
              '--'
            )}
          </Typography.Text>
        </Space>
        <DrillPlanTable
          dataSource={drillConfigData?.drillConfig?.data?.exercisePlanStepList ?? []}
        />
      </Space>
      <FooterToolBar fixed>
        <Space size={16}>
          <Button type="primary" loading={loading} onClick={onSubmit}>
            提交
          </Button>
          <Button href={DRILL_LIST_ROUTE_PATH}>取消</Button>
        </Space>
      </FooterToolBar>
    </Card>
  );
}
