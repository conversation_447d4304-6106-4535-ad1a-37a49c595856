import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import {
  EVENTS_ROUTE_PATH,
  generateEventDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { createEvent } from '@manyun/ticket.service.create-event';
import { EventMutator, generateEventCategory } from '@manyun/ticket.ui.event-mutator';
import type { FormValues } from '@manyun/ticket.ui.event-mutator';
import { filterEventInfluencesTreeData } from '@manyun/ticket.util.filter-event-influences-tree-data';

export function EventCreate() {
  const [loading, setLoading] = useState<boolean>(false);

  const history = useHistory();
  const [configUtil] = useConfigUtil();
  const {
    events: { features, showRacksImpacts },
  } = configUtil.getScopeCommonConfigs('ticket');
  const [form] = Form.useForm<FormValues>();
  const onSave = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const {
        isChangeAlarm,
        changeCode,
        incidentType,
        liablePerson,
        occurTime,
        detectTime,
        causeDevice,
        eventLevel,
        eventSource,
        eventOwner,
        files,
        eventInfluence,
        isFalseAlarm,
        blockGuidList,
        ...rest
      } = formValue;

      const { topCategory, topCategoryName, secondCategory, secondCategoryName } =
        generateEventCategory(incidentType);
      // const { idc, block } = getSpaceGuidMap(location!);
      const svcQuery = {
        ...rest,
        files: files,
        isChangeAlarm,
        blockGuidList,
        idcTag: blockGuidList?.[0]?.split('.')?.[0],
        eventOwnerName: eventOwner?.label,
        eventOwnerId: eventOwner?.value,
        eventSource: eventSource!.value,
        eventSourceName: eventSource!.label,
        eventLevel: eventLevel?.value,
        eventLevelName: eventLevel?.label,
        occurTime: occurTime ? occurTime.valueOf() : undefined,
        detectTime: detectTime ? detectTime.valueOf() : undefined,
        liablePersonId: liablePerson?.value,
        liablePersonName: liablePerson?.label,
        topCategory,
        topCategoryName,
        secondCategory,
        secondCategoryName,
        northSync: false,
        isFalseAlarm: features.isFalseAlarm === 'required' ? isFalseAlarm : 0,
        changeCode: isChangeAlarm ? changeCode : '',
        locationList: formValue.locationList
          ? formValue.locationList?.map(item => ({
              locationType: item.locationType,
              guid: item.guid,
              subType: item.subType,
            }))
          : null,
        eventInfluence: showRacksImpacts
          ? {
              gridInfluence: eventInfluence?.cabinetInfluence,
              influenceScope:
                eventInfluence && Array.isArray(eventInfluence.influenceScope)
                  ? filterEventInfluencesTreeData(eventInfluence.influenceScope)?.map(item => ({
                      influenceType: item.type,
                      influenceGuid: item.key,
                    }))
                  : [],
            }
          : undefined,
      };

      const { error, data } = await createEvent({ ...svcQuery });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('新建成功');
      history.push(
        generateEventDetailRoutePath({
          id: data,
        })
      );
    });
  };
  return (
    <>
      <div style={{ marginBottom: 54 }}>
        <EventMutator
          mode="create"
          externalForm={features.specialInfoSort ? form : undefined}
          showFooter={features.specialInfoSort ? false : true}
          unusedFormItems={features.onwerMultiple ? ['eventOwner'] : ['eventOwnerIdList']}
          onSuccess={eventId => {
            history.push(
              generateEventDetailRoutePath({
                id: eventId,
              })
            );
          }}
        />
      </div>
      {features.specialInfoSort && (
        <FooterToolBar>
          <Space>
            <Button loading={loading} type="primary" onClick={onSave}>
              提交
            </Button>
            <Button onClick={() => history.push(EVENTS_ROUTE_PATH)}>取消</Button>
          </Space>
        </FooterToolBar>
      )}
    </>
  );
}
