import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile, type McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { RichEditor } from '@manyun/dc-brain.ui.rich-editor';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { scrollToField } from '@manyun/dc-brain.util.scroll-to-field';
import { fetchCurrentSchedule } from '@manyun/hrm.service.fetch-current-schedule';
import type {
  CurrentSchedule,
  ScheduleStaffInfos,
} from '@manyun/hrm.service.fetch-current-schedule';
import { fetchNextScheduleById } from '@manyun/hrm.service.fetch-next-schedule-by-id';
import { fetchSchedule } from '@manyun/hrm.service.fetch-schedule';
import {
  type UserDutyJSON,
  fetchUserCurrentScheduleDuty,
} from '@manyun/hrm.service.fetch-user-current-schedule-duty';
import {
  type UserDutyGroupJSON,
  fetchUserCurrentScheduleDutyGroup,
} from '@manyun/hrm.service.fetch-user-current-schedule-duty-group';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import type { MetadataJSON } from '@manyun/resource-hub.model.metadata';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { useLazyQueryFetchChangeShiftApplyOwner } from '@manyun/ticket.gql.client.tickets';
import type { BackendChangeShiftStatus } from '@manyun/ticket.model.ticket';
import { ChangeShiftsUnclosedTicketTable, Handover } from '@manyun/ticket.page.change-shift';
import {
  type ChangeShiftEditRouteParams,
  generateChangeShiftDetail,
} from '@manyun/ticket.route.ticket-routes';
import { type ApiQ, createChangeShift } from '@manyun/ticket.service.create-change-shift';
import { type HandoverEvents, fetchChangeShift } from '@manyun/ticket.service.fetch-change-shift';
import { fetchChangeShiftDataFiles } from '@manyun/ticket.service.fetch-change-shift-data-files';
import { fetchChangeShiftLastSchedule } from '@manyun/ticket.service.fetch-change-shift-last-schedule';
import { updateChangeShift } from '@manyun/ticket.service.update-change-shift';
import { ChangeShiftMatterTable } from '@manyun/ticket.ui.change-shift-matter-table';

export function ChangeShiftsMutator() {
  const { id } = useParams<ChangeShiftEditRouteParams>();

  const [loading, setLoading] = useState(false);
  const [tabActiveKey, setTabActiveKey] = useState<'matter' | 'dutyMatter' | 'legacy'>('matter');
  const [currentScheduleInfo, setCurrentScheduleInfo] = useState<{
    scheduleEndTime: number;
  } | null>(null);
  // const [params, setParams] = useState<{
  //   applyScheduleId: number | null;
  //   handScheduleId: number | null;
  //   handUserId: number | null;
  // }>({ applyScheduleId: null, handScheduleId: null, handUserId: null });
  const [dutyGroups, setDutyGroups] = useState<UserDutyGroupJSON[]>([]);
  const [dutys, setDutys] = useState<UserDutyJSON[]>([]);
  const [scheduleStaffInfos, setScheduleStaffInfos] = useState<ScheduleStaffInfos[]>([]);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [handoverEvents, setHandoverEvents] = useState<HandoverEvents[]>([]);
  const [currentScheduleTime, setCurrentScheduleTime] = useState<number[]>([]);
  const [lastApplyTime, setLastApplyTime] = useState<number | null>(null);
  const [taskStatus, setTaskStatus] = useState<BackendChangeShiftStatus | null>(null);
  const [handleInfos, setHandleInfos] = useState<CurrentSchedule[] | null>(null);
  const [location, setLocation] = useState<string[]>([]);
  const [, { checkCode }] = useAuthorized();

  const showUpload = checkCode('element_ticket_change-shift_upload_data_model');
  const showDown = checkCode('element_ticket_change-shift_fetch_data_model');
  const [getChangeShiftApplyOwner] = useLazyQueryFetchChangeShiftApplyOwner({
    onCompleted(data) {
      if (data.fetchChangeShiftApplyOwner?.success) {
        const userId = getHandUserId(data.fetchChangeShiftApplyOwner.data as ScheduleStaffInfos[]);
        form.setFieldValue('applyUserId', userId);
      }
    },
  });

  const history = useHistory();
  const [form] = Form.useForm<{
    time?: Moment;
    applyDutyGroup?: { label: string; value: number };
    handDuty?: { scheduleId: number; label: string; value: number };
    applyDuty?: { scheduleId: number; label: string; value: number };
    handUserId?: number;
    applyUserId?: number;
    handDutyGroup?: { label: string; value: number };
    fileList?: McUploadFileJSON[];
    handInfo?: string;
    ccStaffIds?: number[] | null;
  }>();
  const fileList = Form.useWatch('fileList', form);
  const applyDutyForm = Form.useWatch('applyDuty', form);
  const handDuty = Form.useWatch('handDuty', form);
  const handUserId = Form.useWatch('handUserId', form);

  const [{ data: matters }, { readMetaData }] = useMetaData(MetaType.HANDOVER_EVENT_TYPE);
  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const showHandoverManager = ticketScopeCommonConfigs.changeShift.features.showHandoverManager;
  const showUserInDutyGroup = ticketScopeCommonConfigs.changeShift.features.showUserInDutyGroup;

  useEffect(() => {
    if (!id) {
      !handoverEvents.length &&
        setHandoverEvents(
          matters.data.map(item => ({
            ...item,
            value: '',
            remarks: '',
            eventName: item.label,
            eventCode: item.value,
          }))
        );
    } else {
      if (handoverEvents.length) {
        setHandoverEvents(
          matters.data.map(item => {
            const event = handoverEvents.filter(event => event.eventCode === item.value);
            return {
              ...item,
              value: event.length === 1 ? event[0].value : '',
              remarks: event.length === 1 ? event[0].remarks : '',
              eventName: event.length === 1 ? event[0].eventName : item.label,
              eventCode: event.length === 1 ? event[0].eventCode : item.value,
            };
          })
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [matters, id]);

  useEffect(() => {
    readMetaData();
    if (id) {
      getDetail();
    } else {
      form.setFieldsValue({
        time: moment(new Date().getTime()),
      });

      // 需要获取当前排班自动带入
      getCurrentSchedule();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getCurrentSchedule = React.useCallback(
    async () => {
      if (loading) {
        return;
      }
      const { data, error } = await fetchCurrentSchedule({
        currentTime: form.getFieldValue('time').valueOf(),
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (!data) {
        return;
      }
      form.setFieldsValue({
        applyDuty: { label: data.duty.dutyName, value: data.duty.id, scheduleId: data.id },
        applyDutyGroup: {
          label: data.dutyGroup.groupName,
          value: data.dutyGroup.id,
        },
      });
      setCurrentScheduleInfo({ scheduleEndTime: data.scheduleEndTime });
      getNextSchedule(true);
      getLastChangeShift(data.id);
      setCurrentScheduleTime([data.scheduleStartTime, data.scheduleEndTime]);
      setLocation([data.idcTag, data.blockTag]);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const getNextSchedule = React.useCallback(
    async (isSetFields: boolean) => {
      const { time, applyDuty, applyDutyGroup } = form.getFieldsValue();

      if (loading || !time || !applyDutyGroup || !applyDuty) {
        return;
      }
      const { data, error } = await fetchNextScheduleById({
        scheduleDate: time.hour(0).minute(0).second(0).millisecond(0).valueOf(),
        dutyGroupId: applyDutyGroup.value,
        dutyId: applyDuty.value,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (!data) {
        return;
      }
      let schedule = data.data[0];
      const idx = data.data.findIndex(i => i.sameAttGroup);
      if (idx > -1) {
        schedule = data.data[idx];
      }
      setHandleInfos(data.data);
      if (schedule && isSetFields) {
        const handUserId = getHandUserId(schedule.scheduleStaffInfos);
        getChangeShiftApplyOwner({ variables: { scheduleId: applyDuty.scheduleId } });
        form.setFieldsValue({
          handDuty: {
            label: schedule.duty.dutyName,
            value: schedule.duty.id,
            scheduleId: schedule.id,
          },
          handDutyGroup: {
            label: schedule.dutyGroup.groupName,
            value: schedule.dutyGroup.id,
          },
          handUserId,
        });
        // setParams({
        //   applyScheduleId,
        //   handScheduleId: schedule.id!,
        //   handUserId,
        // });
      }
      if (!schedule && isSetFields) {
        form.setFieldsValue({
          handDuty: undefined,
          handDutyGroup: undefined,
          handUserId: undefined,
        });
        // setParams({
        //   applyScheduleId: null,
        //   handScheduleId: null,
        //   handUserId: null,
        // });
      }
      setScheduleStaffInfos(schedule?.scheduleStaffInfos || []);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const getHandUserId = (scheduleStaffInfos: ScheduleStaffInfos[] | null) => {
    let handUserId = undefined;

    if (Array.isArray(scheduleStaffInfos) && scheduleStaffInfos.length) {
      const handUserIdx = scheduleStaffInfos.findIndex(i => i.teamLeader);
      if (handUserIdx >= 0) {
        handUserId = scheduleStaffInfos[handUserIdx].id;
      } else {
        handUserId = scheduleStaffInfos[0].id;
      }
    }
    return handUserId;
  };

  const getLastChangeShift = React.useCallback(
    async (scheduleId: number) => {
      if (loading) {
        return;
      }
      const { data, error } = await fetchChangeShiftLastSchedule({
        scheduleId,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (!data) {
        return;
      }
      form.setFieldValue('handInfo', data.handoverInfo);
      setLastApplyTime(data.submitTime);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  const getDetail = React.useCallback(
    async () => {
      if (loading) {
        return;
      }
      setLoading(true);
      const { data, error } = await fetchChangeShift({
        bizNo: id!,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      if (data === null) {
        return;
      }
      form.setFieldsValue({
        handDuty: {
          label: data.handDutyName,
          value: data.handDutyId,
          scheduleId: data.handScheduleId,
        },
        handDutyGroup: {
          label: data.handDutyGroupName,
          value: data.handDutyGroupId,
        },
        applyDutyGroup: { label: data.applyDutyGroupName, value: data.applyDutyGroupId },
        applyDuty: {
          label: data.applyDutyName,
          value: data.applyDutyId,
          scheduleId: data.applyScheduleId,
        },
        handUserId: data.handUserId,
        time: moment(data.handoverDate),
        fileList: data.fileList,
        applyUserId: data.applyUserId,
        ccStaffIds: data.ccStaffIds,
      });
      form.setFieldValue('handInfo', data.handoverInfo);
      setHandoverEvents(
        matters.data.length
          ? matters.data.map(item => {
              const event = data.handoverEvents.filter(event => event.eventCode === item.value);
              return {
                ...item,
                value: event.length === 1 ? event[0].value : '',
                remarks: event.length === 1 ? event[0].remarks : '',
                eventName: event.length === 1 ? event[0].eventName : item.label,
                eventCode: event.length === 1 ? event[0].eventCode : item.value,
              };
            })
          : data.handoverEvents
      );
      setCurrentScheduleInfo({ scheduleEndTime: data.scheduleEndTime });
      getHandUser(data.handScheduleId, 'user');
      setTaskStatus(data.bizStatus);
      setCurrentScheduleTime([data.scheduleStartTime, data.scheduleEndTime]);
      setLocation([data.blockGuid.substring(0, data.blockGuid.indexOf('.')), data.blockGuid]);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  // 交班班组
  const applyDutyGroup = async (isContinue: Boolean, dutyGroupName?: string) => {
    const { time } = form.getFieldsValue();
    if (!time) {
      return;
    }
    const { data, error } = await fetchUserCurrentScheduleDutyGroup({
      scheduleDate: time.hour(0).minute(0).second(0).millisecond(0).valueOf(),
      dutyGroupName,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (!data) {
      return;
    }
    if (isContinue && data.length) {
      form.setFieldsValue({
        applyDutyGroup: {
          label: data[0].groupName,
          value: data[0].id,
        },
      });
      applyDuty(true);
    }
    setDutyGroups(data || []);
  };

  // 接班人
  const getHandUser = async (applyScheduleId: number, checkType: string) => {
    const { data, error } = await fetchSchedule({
      id: applyScheduleId,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (data.data.length === 1) {
      if (checkType === 'user') {
        const users: ScheduleStaffInfos[] = data.data[0].scheduleStaffInfos.map(item => {
          return {
            id: item.id,
            userName: item.userName,
            loginName: item.loginName,
            locked: item.locked,
            teamLeader: false,
          };
        });
        setScheduleStaffInfos(users || []);
      }
      if (checkType === 'time') {
        setCurrentScheduleInfo({ scheduleEndTime: data.data[0]?.scheduleEndTime });
        setCurrentScheduleTime([data.data[0]?.scheduleStartTime, data.data[0]?.scheduleEndTime]);
        setLocation([data.data[0]?.idcTag, data.data[0]?.blockTag]);
      }
    }
  };
  // 交班班次
  const applyDuty = async (isContinue: boolean) => {
    const { time, applyDutyGroup } = form.getFieldsValue();
    if (!time || !applyDutyGroup) {
      return;
    }
    const { data, error } = await fetchUserCurrentScheduleDuty({
      scheduleDate: time.hour(0).minute(0).second(0).millisecond(0).valueOf(),
      dutyGroupId: applyDutyGroup.value,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (!data || !data.length) {
      return;
    }
    if (isContinue) {
      form.setFieldsValue({
        applyDuty: {
          label: data[0].dutyName,
          value: data[0].id,
          scheduleId: data[0].scheduleId,
        },
      });
      getNextSchedule(true);
      getLastChangeShift(data[0].scheduleId);
      getHandUser(data[0].scheduleId, 'time');
      showHandoverManager &&
        getChangeShiftApplyOwner({ variables: { scheduleId: data[0].scheduleId } });
    }
    setDutys(data || []);
  };

  const save = async () => {
    form
      .validateFields()
      .then(async () => {
        setSubmitting(true);
        const p = getParams();
        if (!p) {
          return;
        }
        if (id) {
          const { error } = await updateChangeShift({
            bizNo: id,
            ...p,
          });

          setSubmitting(false);
          if (error) {
            message.error(error.message);
            return;
          }
          history.push(generateChangeShiftDetail({ id }));
          message.success('保存成功！');
          return;
        }
        const { error, data } = await createChangeShift(p);
        setSubmitting(false);
        if (error) {
          message.error(error.message);
          return;
        }
        history.push(generateChangeShiftDetail({ id: data! }));
        message.success('保存成功！');
      })
      .catch(err => {
        setTabActiveKey('matter');
        scrollToField(form.scrollToField, err.errorFields);
      });
  };

  const getParams = () => {
    const { applyDuty, handDuty, handUserId, applyUserId, ccStaffIds } = form.getFieldsValue();
    if (applyDuty?.scheduleId && handDuty?.scheduleId && handUserId) {
      const _p: ApiQ = {
        handoverEvents,
        handoverInfo: form.getFieldValue('handInfo'),
        applyScheduleId: applyDuty.scheduleId,
        handScheduleId: handDuty.scheduleId,
        handUserId: handUserId,
        applyUserId,
        ccStaffIds,
        files:
          Array.isArray(fileList) && fileList.length
            ? fileList.map((file: McUploadFileJSON) => McUploadFile.fromJSON(file).toApiObject())
            : [],
      };
      return _p;
    }
    return null;
  };

  const getTime = () => {
    let startTime = lastApplyTime;
    if (!startTime) {
      startTime = currentScheduleTime[0];
    }
    return { startTime };
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const disabledDate = (current: any) => {
    return current && current > moment();
  };

  const getFiles = async () => {
    if (!applyDutyForm?.scheduleId) {
      return;
    }
    const { data, error } = await fetchChangeShiftDataFiles({
      applyScheduleId: applyDutyForm.scheduleId,
      bizNo: id,
      handlerId: handUserId,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    form.setFieldValue('fileList', data.data);
  };

  const showHandoverManagerBtn = () => {
    // 阳高环境
    if (showHandoverManager) {
      // 编辑,已创建和加签中均可编辑,但只有在已创建才可确认交班
      if (id) {
        return taskStatus === 'INIT';
      }
    }
    return true;
  };
  return (
    <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="large">
      <Form
        style={{ marginBottom: 16 }}
        autoComplete="off"
        labelCol={{ flex: '0 0 120px' }}
        wrapperCol={{ span: 24 }}
        form={form}
      >
        <Space style={{ display: 'flex' }} size="middle" direction="vertical">
          <Card key="info" title="基本信息">
            <Form.Item
              label="值班日期"
              name="time"
              rules={[{ required: true, message: '值班日期必选！' }]}
            >
              <DatePicker
                allowClear
                style={{ width: 256 }}
                format="YYYY-MM-DD"
                disabledDate={disabledDate}
                onChange={dates => {
                  applyDutyGroup(true);
                  form.setFieldsValue({
                    time: dates,
                    applyDutyGroup: undefined,
                    applyDuty: undefined,
                    handDutyGroup: undefined,
                    handDuty: undefined,
                    handUserId: undefined,
                  });
                  // setParams({ applyScheduleId: null, handScheduleId: null, handUserId: null });
                  form.setFieldValue('handInfo', '');

                  setLocation([]);
                  setLastApplyTime(null);
                  setCurrentScheduleInfo(null);
                }}
              />
            </Form.Item>
            <Form.Item label="交班班组" required style={{ marginBottom: 0 }}>
              <Form.Item
                name="applyDutyGroup"
                // label="交班班组"
                colon={false}
                rules={[{ required: true, message: '交班班组必选！' }]}
                style={{ display: 'inline-block', marginRight: 8 }}
              >
                <Select
                  labelInValue
                  style={{ width: 256 }}
                  fieldNames={{ label: 'groupName', value: 'id' }}
                  options={dutyGroups}
                  optionFilterProp="groupName"
                  showSearch
                  allowClear
                  onSelect={() => {
                    form.setFieldsValue({ applyDuty: undefined });
                    // setParams({
                    //   ...params,
                    //   applyScheduleId: null,
                    // });
                    setDutys([]);
                    setScheduleStaffInfos([]);
                    applyDuty(true);
                  }}
                  onSearch={value => {
                    applyDutyGroup(false, value);
                  }}
                  onFocus={() => applyDutyGroup(false)}
                />
              </Form.Item>
              <Form.Item
                colon={false}
                name="applyDuty"
                rules={[{ required: true, message: '交班班次必选！' }]}
                style={{ display: 'inline-block', marginRight: 8 }}
              >
                <Select
                  disabled={!form.getFieldValue('applyDutyGroup')}
                  labelInValue
                  style={{ width: 153 }}
                  fieldNames={{ label: 'dutyName', value: 'id' }}
                  options={dutys}
                  onChange={(_, option) => {
                    if (!Array.isArray(option) && option?.scheduleId) {
                      getLastChangeShift(option.scheduleId);
                      getNextSchedule(true);
                      getHandUser(option.scheduleId, 'time');
                      showHandoverManager &&
                        getChangeShiftApplyOwner({ variables: { scheduleId: option.scheduleId } });
                      form.setFieldsValue({
                        applyDuty: {
                          label: option.dutyName,
                          value: option.id,
                          scheduleId: option.scheduleId,
                        },
                      });
                    }
                  }}
                  onFocus={() => applyDuty(false)}
                />
              </Form.Item>
              {showHandoverManager && (
                <Form.Item
                  label="交班负责人"
                  name="applyUserId"
                  rules={[{ required: true, message: '交班负责人必选！' }]}
                  style={{ display: 'inline-block' }}
                >
                  <UserSelect blockGuid={location[1]} labelInValue={false} style={{ width: 153 }} />
                </Form.Item>
              )}
              {/* </Space> */}
            </Form.Item>
            <Form.Item label="接班班组" required style={{ marginBottom: 0 }}>
              <Form.Item
                name="handDutyGroup"
                rules={[{ required: true, message: '接班班组必选！' }]}
                style={{ display: 'inline-block', marginRight: 8 }}
              >
                <Select
                  labelInValue
                  style={{ width: 256 }}
                  options={handleInfos?.map(dutyGroupItem => ({
                    label: dutyGroupItem.dutyGroup.groupName,
                    value: dutyGroupItem.dutyGroup.id,
                    ...dutyGroupItem,
                  }))}
                  onFocus={() => {
                    getNextSchedule(false);
                  }}
                  onChange={(_, option) => {
                    if (Array.isArray(option)) {
                      return;
                    }
                    form.setFieldValue('handDuty', {
                      label: option.duty.dutyName,
                      value: option.duty.id,
                      scheduleId: option.id,
                    });
                    const handUserId = getHandUserId(option.scheduleStaffInfos);
                    form.setFieldValue('handUserId', handUserId);
                    // setParams({
                    //   ...params,
                    //   handScheduleId: option.id,
                    //   handUserId,
                    // });
                    setScheduleStaffInfos(option.scheduleStaffInfos || []);
                  }}
                />
              </Form.Item>
              <Form.Item
                colon={false}
                name="handDuty"
                rules={[{ required: true, message: '接班班次必选！' }]}
                style={{ display: 'inline-block', marginRight: 8 }}
              >
                <Select
                  options={handleInfos?.map(dutyItem => ({
                    label: dutyItem.duty.dutyName,
                    value: dutyItem.duty.id,
                    ...dutyItem,
                  }))}
                  disabled
                  labelInValue
                  style={{ width: 153 }}
                />
              </Form.Item>
              <Form.Item
                label="接班负责人"
                name="handUserId"
                rules={[{ required: true, message: '接班负责人必选！' }]}
                style={{ display: 'inline-block' }}
              >
                {showUserInDutyGroup ? (
                  <Select
                    style={{ width: 153 }}
                    options={scheduleStaffInfos.map(userItem => ({
                      ...userItem,
                      label: userItem.userName,
                      value: userItem.id,
                    }))}
                  />
                ) : (
                  <UserSelect blockGuid={location[1]} labelInValue={false} style={{ width: 153 }} />
                )}
              </Form.Item>
            </Form.Item>
            <Form.Item label="交班通知抄送" name="ccStaffIds">
              <UserSelect
                userState="in-service"
                mode="multiple"
                labelInValue={false}
                style={{ width: 416 }}
              />
            </Form.Item>
          </Card>

          <Card key="matter">
            <Tabs
              defaultActiveKey="matter"
              activeKey={tabActiveKey}
              items={[
                {
                  label: '交接事项',
                  key: 'matter',
                  children: (
                    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
                      {(showDown || showUpload) && (
                        <Space style={{ alignItems: 'flex-start' }}>
                          <div style={{ marginTop: 5 }}>
                            <Typography.Text>楼栋运维数据：</Typography.Text>
                            <Tooltip title="运维数据如有差异，可修改下载数据后重新上传附件，以最终上传附件为准。">
                              <QuestionCircleOutlined />
                            </Tooltip>
                          </div>
                          <Form.Item
                            name="fileList"
                            noStyle
                            valuePropName="fileList"
                            getValueFromEvent={value => {
                              if (typeof value === 'object') {
                                return value.fileList;
                              }
                            }}
                          >
                            <Upload
                              maxFileSize={20}
                              accept=".xls,.xlsx,.png,.jpg"
                              showUploadList
                              maxCount={1}
                            >
                              <Space>
                                {showDown &&
                                  (fileList?.length ? (
                                    <Popconfirm
                                      title="再次获取数据会覆盖当前文件，确定覆盖并获取数据？"
                                      onConfirm={e => {
                                        getFiles();
                                        e?.stopPropagation();
                                      }}
                                      onCancel={e => e?.stopPropagation()}
                                    >
                                      <Button
                                        type="primary"
                                        disabled={!applyDutyForm?.scheduleId}
                                        onClick={e => {
                                          e.stopPropagation();
                                        }}
                                      >
                                        获取数据
                                      </Button>
                                    </Popconfirm>
                                  ) : (
                                    <Button
                                      type="primary"
                                      disabled={!applyDutyForm?.scheduleId}
                                      onClick={e => {
                                        getFiles();
                                        e.stopPropagation();
                                      }}
                                    >
                                      获取数据
                                    </Button>
                                  ))}
                                {showUpload && <Button>上传附件</Button>}
                              </Space>
                            </Upload>
                          </Form.Item>
                        </Space>
                      )}
                      <Space direction="vertical" size="small" style={{ width: '100%' }}>
                        <Typography.Title level={5}>长期交接班事项</Typography.Title>
                        <Form.Item
                          name="handInfo"
                          rules={[{ max: 10000, message: '长期交接班事项不可超过10000个字符！' }]}
                          style={{ marginBottom: 0 }}
                        >
                          <RichEditor
                            style={{ maxHeight: 500 }}
                            toolbarConfig={{
                              excludeKeys: ['group-video'],
                            }}
                          />
                        </Form.Item>
                      </Space>
                      {((id && !loading) || !id) && (
                        <Table
                          rowKey="eventName"
                          columns={columns(
                            matters,
                            handoverEvents,
                            showHandoverManager,
                            setHandoverEvents
                          )}
                          dataSource={handoverEvents}
                          pagination={false}
                        />
                      )}
                    </Space>
                  ),
                },
                {
                  label: '值班期事项',
                  key: 'dutyMatter',
                  children: tabActiveKey === 'dutyMatter' && (
                    <ChangeShiftMatterTable
                      taskStatus={taskStatus}
                      id={id}
                      scheduleTime={getTime()}
                      idcTag={location[0]}
                      blockTag={location[1]}
                    />
                  ),
                },
                {
                  label: '遗留事项',
                  key: 'legacy',
                  children: tabActiveKey === 'legacy' && (
                    <ChangeShiftsUnclosedTicketTable
                      taskStatus={taskStatus}
                      id={id}
                      idcTag={location[0]}
                      blockTag={location[1]}
                    />
                  ),
                },
              ]}
              onChange={value => {
                setTabActiveKey(value as 'matter' | 'dutyMatter' | 'legacy');
              }}
            />
          </Card>
        </Space>
      </Form>
      <FooterToolBar>
        <Space style={{ width: '100%', justifyContent: 'center' }}>
          {showHandoverManagerBtn() && (
            <Handover
              time={currentScheduleInfo?.scheduleEndTime}
              staffId={handUserId}
              groupScheduleId={handDuty?.scheduleId}
              form={form}
              handoverEvents={handoverEvents}
              onSuccess={id => {
                history.push(generateChangeShiftDetail({ id: id! }));
              }}
            />
          )}
          <Button loading={submitting} onClick={save}>
            保存
          </Button>
          <Button
            disabled={submitting}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}

const columns = (
  matters: {
    total: number;
    codes: string[];
    loading: boolean;
    entities: Record<string, MetadataJSON>;
    keyMapper: Record<number, string>;
  },
  handoverEvents: HandoverEvents[],
  showHandoverManager: boolean,
  setHandoverEvents: React.Dispatch<React.SetStateAction<HandoverEvents[]>>
) => [
  {
    title: '事项',
    dataIndex: 'eventCode',
    render: (text: string, record: HandoverEvents) =>
      text && matters.entities[text] ? matters.entities[text].name : record.eventName,
  },
  {
    title: (
      <Space>
        {showHandoverManager ? null : <Typography.Text type="danger">*</Typography.Text>}状态
      </Space>
    ),
    dataIndex: 'value',
    render: (text: string, record: HandoverEvents, index: number) => {
      if (record.description === 'DOUBLE') {
        const separator = text.indexOf('/');
        const reality = separator !== -1 ? Number(text.substring(0, separator)) : undefined;
        const plan = separator !== -1 ? Number(text.substring(separator + 1)) : undefined;
        return (
          <Space style={{ display: 'flex', alignItems: 'center' }}>
            <Form.Item
              name={`${index}-reality`}
              rules={[{ required: true, message: '数量必填！' }]}
              initialValue={reality}
              style={{ marginBottom: 0 }}
            >
              <InputNumber
                style={{ width: '97px' }}
                placeholder="实际数量"
                value={Number(reality)}
                onChange={value => {
                  const newList = handoverEvents.map((item, idx) => {
                    if (idx === index) {
                      return {
                        ...item,
                        value: `${value}/${plan}`,
                      };
                    }
                    return item;
                  });
                  setHandoverEvents(newList);
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>/</Form.Item>
            <Form.Item
              name={`${index}-plan`}
              rules={[{ required: true, message: '数量必填！' }]}
              initialValue={plan}
              style={{ marginBottom: 0 }}
            >
              <InputNumber
                style={{ width: '97px' }}
                value={Number(plan)}
                placeholder="计划数量"
                onChange={value => {
                  const newList = handoverEvents.map((item, idx) => {
                    if (idx === index) {
                      return {
                        ...item,
                        value: `${reality}/${value}`,
                      };
                    }
                    return item;
                  });
                  setHandoverEvents(newList);
                }}
              />
            </Form.Item>
          </Space>
        );
      }
      return (
        <Form.Item
          name={`${index}-value`}
          rules={[{ required: !showHandoverManager, message: '状态必选！' }]}
          initialValue={text}
          style={{ marginBottom: 0 }}
        >
          {showHandoverManager ? (
            <Input
              maxLength={50}
              onChange={e => {
                const newList = handoverEvents.map((item, idx) => {
                  if (idx === index) {
                    return {
                      ...item,
                      value: e.target.value,
                    };
                  }
                  return item;
                });
                setHandoverEvents(newList);
              }}
            />
          ) : (
            <Select
              style={{ width: 216 }}
              allowClear={false}
              value={text}
              onChange={value => {
                const newList = handoverEvents.map((item, idx) => {
                  if (idx === index) {
                    return {
                      ...item,
                      value,
                    };
                  }
                  return item;
                });
                setHandoverEvents(newList);
              }}
            >
              <Select.Option value="正常">正常</Select.Option>
              <Select.Option value="异常">异常</Select.Option>
            </Select>
          )}
        </Form.Item>
      );
    },
  },
  {
    title: '说明',
    dataIndex: 'remarks',
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    render: (text: string, _: any, index: number) => {
      return (
        <Form.Item
          name={`${index}-remarks`}
          rules={[
            // { required: true, whitespace: true, message: '说明必填！' },
            {
              message: '说明不可超过30个字符！',
              max: 30,
            },
          ]}
          initialValue={text}
          style={{ marginBottom: 0 }}
        >
          <Input
            value={text}
            style={{ width: 618 }}
            onChange={e => {
              const newList = handoverEvents.map((item, idx) => {
                if (idx === index) {
                  return {
                    ...item,
                    remarks: e.target.value,
                  };
                }
                return item;
              });
              setHandoverEvents(newList);
            }}
          />
        </Form.Item>
      );
    },
  },
];
