import React, { useCallback, useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import type { ChangeDeviceList } from '@manyun/ticket.gql.client.tickets';

export type ChangeSourceProps = {
  idcTag: string;
  blockTag: string;
  onChange?: (value: LabelInvalue[]) => void;
  value?: LabelInvalue[];
} & TableProps<ChangeDeviceList>;

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const SelectDevice = React.forwardRef(
  ({ idcTag, blockTag, value, onChange }: ChangeSourceProps, ref: React.Ref<RefSelectProps>) => {
    const [deviceData, setDeviceData] = useState<
      (ChangeDeviceList & { productModel: string; vendor: string })[]
    >([]);
    const [total, setTotal] = useState<number>(0);
    const [filterValue, setFilterValue] = useState<DeviceParams>({
      pageNum: 1,
      pageSize: 10,
    });
    const [visible, setVisible] = useState<boolean>(false);
    const [selectedCodes, setSelectedCodes] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<LabelInvalue[]>([]);

    const getDeivceList = useCallback(async () => {
      const { data, error: fetchDeviceListError } = await fetchPageDevices({
        ...filterValue,
        idcTag,
        blockTag,
      });
      if (fetchDeviceListError) {
        setDeviceData([]);
        message.error(fetchDeviceListError.message);
        return;
      }
      if (data?.data) {
        setDeviceData(
          data.data.map(item => ({
            deviceGuid: item.guid,
            deviceName: item.name,
            deviceType: item.deviceCategory.level3,
            roomTag: item.spaceGuid.roomTag,
            inhibition: true,
            productModel: item.productModel,
            vendor: item.vendor,
          }))
        );
        setTotal(data.total);
      } else {
        setDeviceData([]);
      }

      return data.data;
    }, [filterValue, idcTag, blockTag]);

    useEffect(() => {
      getDeivceList();
    }, [getDeivceList]);

    return (
      <Space style={{ width: '100%' }} direction="horizontal">
        <Button
          disabled={!blockTag || !idcTag}
          onClick={() => {
            setVisible(true);
            getDeivceList();
            setSelectedRows(value || []);
            setSelectedCodes(value ? value.map(item => item.value) : []);
          }}
        >
          选择设备
        </Button>
        {value?.length ? (
          <Typography.Text>{`${value[0].label}（${value[0].roomTag}）${value.length === 1 ? '' : `等${value.length}项`}`}</Typography.Text>
        ) : null}
        <Modal
          zIndex={1010}
          width={1015}
          title="选择设备"
          open={visible}
          destroyOnClose
          onCancel={() => {
            setVisible(false);
            setFilterValue({ pageNum: 1, pageSize: 10 });
            setSelectedRows([]);
            setSelectedCodes([]);
          }}
          onOk={() => {
            setFilterValue({ pageNum: 1, pageSize: 10 });
            setSelectedRows([]);
            setSelectedCodes([]);
            setVisible(false);
            onChange && onChange(selectedRows);
          }}
        >
          <Space style={{ width: '100%' }} direction="vertical">
            <Space style={{ width: '100%' }} direction="horizontal">
              <Input.Search
                placeholder="设备名称"
                allowClear
                onSearch={value => {
                  setFilterValue({ ...filterValue, pageNum: 1, name: value });
                }}
              />
              {/* <RoomSelect
                style={{ width: 168 }}
                // blockGuid={`${idcTag}.${blockTag}`}
                blockGuid="EC01"
                placeholder="包间"
                allowClear
                onChange={(value: string) => {
                  if (value) {
                    const [, , roomTag] = value.split('.');
                    setFilterValue({ ...filterValue, pageNum: 1, roomTags: [roomTag] });
                  } else {
                    setFilterValue({ ...filterValue, pageNum: 1, roomTags: [] });
                  }
                }}
              /> */}
              <DeviceTypeCascader
                placeholder="三级分类"
                dataType={['snDevice']}
                style={{ width: 168 }}
                disabledTypeList={['C0', 'C1']}
                allowClear
                numbered
                onChange={value => {
                  setFilterValue({
                    ...filterValue,
                    pageNum: 1,
                    deviceTypeList: value ? [value] : [],
                  });
                }}
              />
              <VendorModelSelect
                disabled={false}
                deviceType={filterValue.deviceTypeList?.join(',')}
                style={{ width: 168 }}
                value={[filterValue.vendor, filterValue.productModel]}
                allowClear
                vendorPlaceholder="厂商"
                modelPlaceholder="型号"
                onChange={value => {
                  const [vendor, productModel] = value ?? [];
                  setFilterValue({ ...filterValue, pageNum: 1, vendor, productModel });
                }}
              />
            </Space>
            <Table
              size="middle"
              rowKey="deviceGuid"
              dataSource={deviceData}
              columns={[
                {
                  title: '设备名称',
                  dataIndex: 'deviceName',
                },
                {
                  title: '包间',
                  dataIndex: 'roomTag',
                },
                {
                  title: '三级分类',
                  dataIndex: 'deviceType',
                  render: (_, { deviceType }) => <DeviceTypeText code={deviceType} />,
                },
                {
                  title: '厂商',
                  dataIndex: 'vendor',
                },
                {
                  title: '型号',
                  dataIndex: 'productModel',
                },
              ]}
              rowSelection={{
                selectedRowKeys: selectedCodes,
                onChange: (selectedRowKeys, selectedRows) => {
                  setSelectedCodes(keys => [
                    ...keys.filter(key => !deviceData.find(item => item.deviceGuid === key)),
                    ...selectedRowKeys,
                  ]);
                  setSelectedRows(rows => [
                    ...rows.filter(row => !deviceData.find(item => item.deviceGuid === row.value)),
                    ...selectedRows.map(item => ({
                      label: item.deviceName,
                      value: item.deviceGuid,
                      key: item.deviceGuid,
                      roomTag: item.roomTag,
                    })),
                  ]);
                },
              }}
              pagination={{
                total: total,
                current: filterValue.pageNum,
                pageSize: filterValue.pageSize,
                onChange: (pageNum, pageSize) => {
                  setFilterValue({ ...filterValue, pageNum, pageSize });
                },
              }}
            />
          </Space>
        </Modal>
      </Space>
    );
  }
);

SelectDevice.displayName = 'SelectDevice';
