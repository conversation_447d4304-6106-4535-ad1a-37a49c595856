import React, { useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import type { RiskLocationInfo } from '@manyun/ticket.model.risk-register';

import { AddDevice } from './add-device';
import { BlockTable } from './block-table';
import { RoomTable } from './room-table';

export type ChangeSourceProps = {
  idcTag: string | null;
  blockTag?: string | null;
  onChange?: (value: RiskLocationInfo[]) => void;
  dataSource?: RiskLocationInfo[];
  mode?: 'new' | 'view';
};

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const RiskRegisterTabs = React.forwardRef(
  (
    { idcTag, blockTag, dataSource = [], mode = 'view', onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [defaultTabActiveKey, setDefaultTabActiveKey] = useState('block');

    const blockData = dataSource.filter(item => item.locationType === 'BLOCK') ?? [];
    const roomData = dataSource.filter(item => item.locationType === 'ROOM') ?? [];
    const deviceData = dataSource.filter(item => item.locationType === 'DEVICE') ?? [];
    useEffect(() => {
      if (!blockData.length && roomData.length) {
        setDefaultTabActiveKey('room');
      }
      if (!blockData.length && !roomData.length && deviceData.length) {
        setDefaultTabActiveKey('device');
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dataSource]);

    return (
      <Card style={{ width: 1080 }} bodyStyle={{ paddingTop: 12 }}>
        <Tabs
          style={{ width: '100%' }}
          defaultActiveKey={defaultTabActiveKey}
          activeKey={defaultTabActiveKey}
          onChange={setDefaultTabActiveKey}
        >
          <Tabs.TabPane
            key="block"
            tab={`楼栋${blockData.length ? '(' + blockData.length + ')' : ''}`}
          >
            <BlockTable
              dataSource={blockData}
              idcTag={idcTag}
              blockTag={blockTag}
              mode={mode}
              onChange={blocks => onChange && onChange([...blocks, ...roomData, ...deviceData])}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            key="room"
            tab={`包间${roomData.length ? '(' + roomData.length + ')' : ''}`}
          >
            <RoomTable
              dataSource={roomData}
              idcTag={idcTag}
              blockTag={blockTag}
              mode={mode}
              onChange={rooms => onChange && onChange([...rooms, ...blockData, ...deviceData])}
            />
          </Tabs.TabPane>
          <Tabs.TabPane
            key="device"
            tab={`设备${deviceData.length ? '(' + deviceData.length + ')' : ''}`}
          >
            <AddDevice
              dataSource={deviceData}
              idcTag={idcTag}
              blockTag={blockTag}
              mode={mode}
              onChange={devices => onChange && onChange([...devices, ...roomData, ...blockData])}
            />
          </Tabs.TabPane>
        </Tabs>
      </Card>
    );
  }
);

RiskRegisterTabs.displayName = 'RiskRegisterTabs';
