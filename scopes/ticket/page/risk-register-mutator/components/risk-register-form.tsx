import { MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import React, { useEffect } from 'react';
import { useLocation } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { DeviceOrSpaceSelect } from '@manyun/resource-hub.ui.device-or-space-select';
import { DevicesTableSelectModal } from '@manyun/resource-hub.ui.device-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import type {
  RiskObject,
  RiskRegisterBaseInfoResponse,
  RiskStatus,
} from '@manyun/ticket.gql.client.risk-register';
import { FaultTargetType } from '@manyun/ticket.model.event';
import { FaultTargetTypeSelect } from '@manyun/ticket.ui.fault-target-type-select';

import { EventSelect } from './event-beta-select';
import { RiskRegisterTabs } from './risk-locaiton-tabs';

export type RiskRegisterFormProps = {
  form: FormInstance<RiskRegisterFormValues>;
  values?: RiskRegisterFormValues;
  riskStatus?: RiskStatus;
  blockGuid?: string;
  oldRiskLevel?: string | null;
  unusedFormItems?: FieldName[];
  locationDisabled?: boolean;
  measuresPlanTime?: number;
  immutableEventIdList?: string[] | null;
};

export type FieldName = keyof RiskRegisterFormValues;

export type RiskRegisterFormValues = Omit<
  RiskRegisterBaseInfoResponse,
  | 'createdAt'
  | 'modifiedAt'
  | 'riskStatus'
  | 'riskClearStatus'
  | 'createUser'
  | 'riskClearReason'
  | 'measureProgress'
  | 'rectifyInfoList'
  | 'riskIdentifyTime'
  | 'riskEvaluateTime'
  | 'riskHandleTime'
  | 'riskAuditTime'
  | 'riskCloseTime'
  | 'evaluateInstId'
  | 'auditInstId'
  | 'riskObjects'
  | 'fileInfoList'
  | 'locationList'
> & {
  riskObjects?: RiskObject[] | null | string;
};

export function RiskRegisterForm({
  form,
  values,
  riskStatus,
  blockGuid: withoutBlockGuid,
  unusedFormItems,
  oldRiskLevel,
  locationDisabled = false,
  measuresPlanTime,
  immutableEventIdList,
}: RiskRegisterFormProps) {
  const blockGuid = Form.useWatch('blockGuid', form);
  const riskObjectType = Form.useWatch('riskObjectType', form);
  const riskLevel = Form.useWatch('riskLevel', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const riskResourceCode = Form.useWatch('riskResourceCode', form);
  const relateEventIdList = Form.useWatch('relateEventIdList', form);

  const { search } = useLocation();

  const [configUtil] = useConfigUtil();

  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  // @ts-ignore type error
  const baseInfo = features?.baseInfo;
  // @ts-ignore type error
  const riskSource = features?.riskSource;
  const isFull = baseInfo === 'full';

  const useWhichBlockGuid = withoutBlockGuid ?? blockGuid;
  const { idc, block } = getSpaceGuidMap(useWhichBlockGuid ?? '');

  const { eventSourceNo, eventSourceType, spaceGuid } = getLocationSearchMap<{
    eventSourceNo?: string;
    eventSourceType?: string;
    spaceGuid?: string;
  }>(search);

  const [idcTag, blockTag] = (spaceGuid ?? '')?.split('.');

  useEffect(() => {
    values && form.setFieldsValue(values);
  }, [values, form]);

  return (
    <Form
      form={form}
      labelCol={{ flex: ' 0 0 120px' }}
      wrapperCol={{ span: 16 }}
      style={{ marginBottom: 56 }}
    >
      <Card
        bordered={false}
        headStyle={{ borderBottom: 0 }}
        bodyStyle={{ padding: '8px 0', width: 680 }}
        title={
          <Typography.Title showBadge level={5}>
            基本信息
          </Typography.Title>
        }
      >
        <Row gutter={16}>
          {!unusedFormItems?.includes('blockGuid') && (
            <Col span={12}>
              <Form.Item
                label="位置"
                name="blockGuid"
                wrapperCol={{ span: 20 }}
                rules={[
                  {
                    required: true,
                    message: '位置必选！',
                  },
                ]}
                initialValue={spaceGuid}
              >
                <LocationTreeSelect
                  authorizedOnly
                  idc={idcTag}
                  block={blockTag}
                  disabled={locationDisabled || (riskStatus ? riskStatus !== 'DRAFT' : false)}
                  onChange={() => {
                    // setRiskLocationDataSource([]);
                    form.setFieldsValue({
                      riskOwnerIdList: undefined,
                      relatePerson: undefined,
                      riskObjectType: undefined,
                      riskObjects: undefined,
                      locationList: undefined,
                      relateEventIdList:
                        eventSourceNo || immutableEventIdList?.length
                          ? relateEventIdList.filter(
                              item => item === eventSourceNo || immutableEventIdList?.includes(item)
                            )
                          : [undefined],
                    });
                  }}
                />
              </Form.Item>
            </Col>
          )}

          {!unusedFormItems?.includes('riskCategoryCode') && (
            <Col span={12}>
              <Form.Item
                label={isFull ? '风险专业' : '风险类别'}
                name="riskCategoryCode"
                rules={[
                  {
                    required: true,
                    message: `${isFull ? '风险专业' : '风险类别'}必选！`,
                  },
                ]}
              >
                <MetaTypeSelect
                  showSearch
                  optionLabelProp="label"
                  optionFilterProp="label"
                  metaType={MetaType.RISK_TOP_TYPE}
                />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('riskLevel') && !isFull && (
            <Col span={12}>
              <Form.Item
                label={
                  isFull ? (
                    <Explanation
                      style={{ color: 'inherit' }}
                      iconType="exclamation"
                      tooltip={{
                        overlayInnerStyle: { width: 544 },
                        title: (
                          <div>
                            F0超高风险：已影响业务运行或该风险不进行处理将短期内发生大范围影响业务故障，需要短期内完成闭环处理。
                            <br />
                            F1高风险：暂时未影响业务运行，但该风险一旦触发将引发大面积影响到IT设备供电、制冷、故障等情况，一般考虑影响范围为楼栋级别或是多个包间级别及以上。发生概率较小的风险可以适当降级。
                            <br />
                            F2中风险：暂时未影响业务运行，但该风险一旦触发将引发列级别影响IT设备供电、制冷或故障等情况，一般考虑影响范围低于1个包间，一般影响面为一列或多列。发生概率较小的风险可以适当降级。
                            <br />{' '}
                            F3低风险：未影响业务运行，但该风险一旦触发将引发机柜级别，影响IT设备供电、制冷或故障等情况，一般考虑影响范围为单机机柜或几个机柜。
                            <br />{' '}
                            F4超低风险：该风险一般不影响到业务运行，但长期存在将影响到机房的正常运营
                            。
                          </div>
                        ),
                      }}
                    >
                      风险等级
                    </Explanation>
                  ) : (
                    '风险等级'
                  )
                }
                name="riskLevel"
                rules={[
                  {
                    required: true,
                    message: '风险等级必选！',
                  },
                ]}
              >
                <MetaTypeSelect
                  showSearch
                  optionLabelProp="label"
                  optionFilterProp="label"
                  metaType={MetaType.RISK_LEVEL}
                />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('riskTypeCode') && (
            <Col span={12}>
              <Form.Item
                label="风险类型"
                name="riskTypeCode"
                rules={[
                  {
                    required: true,
                    message: '风险类型必选！',
                  },
                ]}
              >
                <MetaTypeSelect
                  showSearch
                  optionLabelProp="label"
                  optionFilterProp="label"
                  metaType={MetaType.RISK_SEC_TYPE}
                />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('riskResourceCode') && (
            <Col span={12}>
              <Form.Item
                label="风险来源"
                name="riskResourceCode"
                rules={[
                  {
                    required: true,
                    message: '风险来源必选！',
                  },
                ]}
                initialValue={eventSourceNo ? 'EVENT_QUESTION' : undefined}
              >
                <MetaTypeSelect
                  showSearch
                  optionLabelProp="label"
                  optionFilterProp="label"
                  disabled={!!eventSourceNo}
                  metaType={MetaType.RISK_RESOURCE}
                />
              </Form.Item>
            </Col>
          )}

          {/* 问题关联事件 */}
          {riskResourceCode === 'EVENT_QUESTION' && riskSource && (
            <Col span={24}>
              <Form.List
                name="relateEventIdList"
                rules={[
                  {
                    validator: async (_, relateEventIdList) => {
                      if (!relateEventIdList || relateEventIdList.length === 0) {
                        return Promise.reject(new Error('至少添加一个关联事件'));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                initialValue={eventSourceNo ? [eventSourceNo] : [undefined]}
              >
                {(fields, { add, remove }) => (
                  <>
                    {fields.map(({ key, name, fieldKey, ...restField }, index) => (
                      <Form.Item
                        key={`${key}.${name}`}
                        label={index === 0 ? '问题关联事件' : ' '}
                        colon={index === 0}
                        required={index === 0}
                        wrapperCol={{ span: 20 }}
                      >
                        <div style={{ width: '100%', display: 'flex' }}>
                          <Form.Item
                            {...restField}
                            style={{ display: 'flex', flex: 1 }}
                            rules={[
                              {
                                validator: async (_, relateEventIdList) => {
                                  if (relateEventIdList === undefined) {
                                    return Promise.reject(new Error('事件ID必选'));
                                  }
                                  return Promise.resolve();
                                },
                              },
                            ]}
                            noStyle
                            name={[name]}
                          >
                            <EventSelect
                              blockGuid={blockGuid}
                              disabled={
                                !blockGuid ||
                                !!(
                                  (eventSourceNo && eventSourceType && index === 0) ||
                                  (immutableEventIdList?.length
                                    ? immutableEventIdList.includes(
                                        form.getFieldValue(['relateEventIdList', name])
                                      )
                                    : false)
                                )
                              }
                              relateEventIdList={relateEventIdList}
                            />
                          </Form.Item>

                          {fields.length > 1 &&
                          ((eventSourceNo && eventSourceType && index === 0) ||
                          (immutableEventIdList?.length &&
                            immutableEventIdList.includes(
                              form.getFieldValue(['relateEventIdList', name])
                            ))
                            ? false
                            : true) ? (
                            <MinusCircleOutlined
                              style={{ marginLeft: 8, width: 14 }}
                              onClick={() => remove(name)}
                            />
                          ) : null}
                        </div>
                      </Form.Item>
                    ))}
                    <Form.Item
                      label=" "
                      colon={false}
                      labelCol={{ flex: ' 0 0 120px' }}
                      wrapperCol={{ span: 20 }}
                    >
                      <Button
                        type="dashed"
                        icon={<PlusOutlined />}
                        style={{ width: '100%' }}
                        onClick={() => add()}
                      >
                        添加关联事件
                      </Button>
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Col>
          )}
          {!unusedFormItems?.includes('riskLevel') && isFull && (
            <Col span={12}>
              <Form.Item
                label={
                  isFull ? (
                    <Explanation
                      style={{ color: 'inherit' }}
                      iconType="exclamation"
                      tooltip={{
                        overlayInnerStyle: { width: 544 },
                        title: (
                          <div>
                            F0超高风险：已影响业务运行或该风险不进行处理将短期内发生大范围影响业务故障，需要短期内完成闭环处理。
                            <br />
                            F1高风险：暂时未影响业务运行，但该风险一旦触发将引发大面积影响到IT设备供电、制冷、故障等情况，一般考虑影响范围为楼栋级别或是多个包间级别及以上。发生概率较小的风险可以适当降级。
                            <br />
                            F2中风险：暂时未影响业务运行，但该风险一旦触发将引发列级别影响IT设备供电、制冷或故障等情况，一般考虑影响范围低于1个包间，一般影响面为一列或多列。发生概率较小的风险可以适当降级。
                            <br />{' '}
                            F3低风险：未影响业务运行，但该风险一旦触发将引发机柜级别，影响IT设备供电、制冷或故障等情况，一般考虑影响范围为单机机柜或几个机柜。
                            <br />{' '}
                            F4超低风险：该风险一般不影响到业务运行，但长期存在将影响到机房的正常运营
                            。
                          </div>
                        ),
                      }}
                    >
                      风险等级
                    </Explanation>
                  ) : (
                    '风险等级'
                  )
                }
                name="riskLevel"
                rules={[
                  {
                    required: true,
                    message: '风险等级必选！',
                  },
                ]}
              >
                <MetaTypeSelect
                  showSearch
                  optionLabelProp="label"
                  optionFilterProp="label"
                  metaType="RISK_LEVEL"
                />
              </Form.Item>
            </Col>
          )}
          {/* {features.priority !== 'disabled' && !unusedFormItems?.includes('riskPriorityCode') && (
              <Col span={12}>
                <Form.Item label="优先级" name="riskPriorityCode">
                  <MetaTypeSelect
                    showSearch
                    optionLabelProp="label"
                    optionFilterProp="label"
                    metaType={MetaType.RISK_PRIORITY}
                    allowClear
                    showArrow
                  />
                </Form.Item>
              </Col>
            )} */}
          {features.showIdentifyUser && !unusedFormItems?.includes('riskIdentifier') && (
            <Col span={12}>
              <Form.Item
                // wrapperCol={{ span: 10 }}
                label="风险识别人"
                name="riskIdentifier"
                rules={[
                  {
                    max: 10,

                    message: '最多输入 10 个字符！',
                  },
                ]}
              >
                <Input allowClear />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('planCompleteTime') && isFull && (
            <Col span={12}>
              <Form.Item
                label="计划完成时间"
                name="planCompleteTime"
                required
                rules={[
                  {
                    validator: (_, rang) => {
                      if (!rang) {
                        return Promise.reject('计划完成时间必选！');
                      }
                      if (measuresPlanTime) {
                        const _infoPlanCompleteTime = moment(measuresPlanTime)
                          .format('YYYY-MM-DD HH:mm')
                          .valueOf();
                        const planTime = moment(rang).format('YYYY-MM-DD HH:mm').valueOf();
                        if (planTime < _infoPlanCompleteTime) {
                          return Promise.reject('风险计划完成时间不可早风险措施计划完成时间');
                        }
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <DatePicker
                  style={{ width: '100%' }}
                  allowClear
                  format="YYYY-MM-DD HH:mm"
                  showTime
                  disabledDate={
                    measuresPlanTime
                      ? current => {
                          /**不可早于计划开始时间 */
                          return current < moment(measuresPlanTime).startOf('day');
                        }
                      : undefined
                  }
                  disabledTime={
                    measuresPlanTime
                      ? current => {
                          const compareMoment = moment(measuresPlanTime);
                          const hour = compareMoment.hour();
                          const minute = compareMoment.minute();
                          const second = compareMoment.second();
                          if (current && current.isSame(compareMoment, 'day')) {
                            const currentHour = current.hour();
                            const currentMinute = current.minute();
                            if (currentHour === hour) {
                              return {
                                disabledHours: () => generateRange(0, hour),
                                disabledMinutes: () => generateRange(0, minute),
                                disabledSeconds: () =>
                                  currentMinute === minute ? generateRange(0, second) : [],
                              };
                            } else {
                              return {
                                disabledHours: () => generateRange(0, hour),
                                disabledMinutes: () => [],
                                disabledSeconds: () => [],
                              };
                            }
                          }
                          return {
                            disabledHours: () => [],
                            disabledMinutes: () => [],
                            disabledSeconds: () => [],
                          };
                        }
                      : undefined
                  }
                  disabled={
                    riskStatus ? riskStatus !== 'DRAFT' && riskStatus !== 'WAITING_IDENTIFY' : false
                  }
                />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('longTermRisk') && isFull && (
            <Col span={12}>
              <Form.Item
                label="是否长期风险项"
                name="longTermRisk"
                rules={[
                  {
                    required: true,
                    message: '是否长期风险项必选！',
                  },
                ]}
              >
                <Select
                  // style={{ width: 210 }}
                  placeholder=" "
                  options={[
                    {
                      label: '是',
                      value: true,
                    },
                    {
                      label: '否',
                      value: false,
                    },
                  ]}
                  allowClear
                />
              </Form.Item>
            </Col>
          )}
          {riskLevel !== oldRiskLevel && riskStatus === 'HANDLING' && !isFull && (
            <Col span={24}>
              <Form.Item wrapperCol={{ span: 24 }}>
                <Alert
                  message="风险等级存在变更，提交后风险单会回退到识别中状态，请谨慎操作！"
                  type="warning"
                  showIcon
                />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('riskDesc') && (
            <Col span={24}>
              <Form.Item
                label="风险描述"
                name="riskDesc"
                wrapperCol={{ span: 20 }}
                rules={[
                  {
                    required: true,
                    message: '风险描述必填！',
                  },
                  { whitespace: true, max: 300, message: '最多输入 300 个字符！' },
                ]}
              >
                <Input.TextArea rows={3} showCount maxLength={300} />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('riskInfluenceDesc') && isFull && (
            <Col span={24}>
              <Form.Item
                label="影响范围描述"
                name="riskInfluenceDesc"
                wrapperCol={{ span: 20 }}
                rules={[{ whitespace: true, max: 300, message: '最多输入 300 个字符！' }]}
              >
                <Input.TextArea rows={3} showCount maxLength={300} />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('riskOwnerIdList') && isFull && (
            <Col span={24}>
              <Form.Item
                label="责任人"
                name="riskOwnerIdList"
                rules={[
                  {
                    required: true,
                    message: '责任人必选！',
                  },
                ]}
              >
                <UserSelect
                  labelInValue={false}
                  style={{ width: 560 }}
                  mode="multiple"
                  blockGuid={useWhichBlockGuid ?? undefined}
                  disabled={isFull ? false : riskStatus ? riskStatus !== 'DRAFT' : false}
                />
              </Form.Item>
            </Col>
          )}
          {!unusedFormItems?.includes('fileInfoList') && (
            <Col span={12}>
              <Form.Item
                label="问题附件"
                name="fileInfoList"
                valuePropName="fileList"
                getValueFromEvent={value => {
                  if (typeof value === 'object') {
                    return value.fileList;
                  }
                }}
              >
                <Upload
                  accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
                  maxCount={5}
                  maxFileSize={20}
                >
                  {fileInfoList && fileInfoList?.length >= 5 ? null : (
                    <Button icon={<UploadOutlined />}>点此上传</Button>
                  )}
                </Upload>
              </Form.Item>
            </Col>
          )}
        </Row>
      </Card>
      {!isFull ? (
        <Card
          bordered={false}
          headStyle={{ borderBottom: 0 }}
          bodyStyle={{ paddingTop: 8 }}
          title={
            <Typography.Title showBadge level={5}>
              风险对象信息
            </Typography.Title>
          }
        >
          <Row gutter={16}>
            {!unusedFormItems?.includes('riskObjectType') && (
              <Col span={24}>
                <Form.Item
                  label="风险对象类型"
                  name="riskObjectType"
                  wrapperCol={{ span: 10 }}
                  rules={[
                    {
                      required: true,
                      message: '风险对象类型必选！',
                    },
                  ]}
                >
                  <FaultTargetTypeSelect
                    style={{ width: '100%' }}
                    disabledOptionTypes={block ? undefined : [FaultTargetType.Device]}
                    onChange={() => {
                      form.setFieldsValue({ riskObjects: undefined });
                    }}
                  />
                </Form.Item>
              </Col>
            )}
            {!unusedFormItems?.includes('riskObjects') && (
              <Col span={24}>
                <Form.Item
                  label="风险对象名称"
                  name="riskObjects"
                  required
                  wrapperCol={{ span: 20 }}
                  rules={[
                    {
                      validator: (_, value) => {
                        if (riskObjectType !== 'OTHER' && !value?.length) {
                          return Promise.reject('风险对象名称必选！');
                        }
                        if (riskObjectType === 'OTHER' && value?.length > 20) {
                          return Promise.reject('最多仅允许输入20个字！');
                        }
                        if (riskObjectType === 'OTHER' && value.trim().length === 0) {
                          return Promise.reject('风险对象名称必填！');
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  {riskObjectType === 'DEVICE' ? (
                    <DevicesTableSelectModal
                      style={{}}
                      idcTag={idc!}
                      blockTag={block!}
                      matchProductModel
                      onChange={selectedRows => {
                        form.setFieldValue(
                          'riskObjects',
                          selectedRows?.map(item => ({
                            ...item,
                            label: item.deviceName,
                            value: item.deviceGuid,
                            key: item.deviceGuid,
                            roomTag: item.roomTag,
                          }))
                        );
                      }}
                    />
                  ) : (
                    <DeviceOrSpaceSelect
                      idcTag={idc!}
                      blockTag={block!}
                      disabled={!(useWhichBlockGuid && riskObjectType)}
                      style={{ maxHeight: 88, overflowY: 'auto' }}
                      type={riskObjectType!}
                      labelInValue
                      multiple
                      maxTagCount={9}
                    />
                  )}
                </Form.Item>
              </Col>
            )}
            {!unusedFormItems?.includes('riskInfluenceDesc') && !isFull && (
              <Col span={24}>
                <Form.Item
                  label="影响范围描述"
                  name="riskInfluenceDesc"
                  wrapperCol={{ span: 20 }}
                  rules={[{ whitespace: true, max: 300, message: '最多输入 300 个字符！' }]}
                >
                  <Input.TextArea rows={3} showCount maxLength={300} />
                </Form.Item>
              </Col>
            )}
            {!unusedFormItems?.includes('riskOwnerIdList') && !isFull && (
              <Col span={12}>
                <Form.Item
                  label="责任人"
                  name="riskOwnerIdList"
                  rules={[
                    {
                      required: true,
                      message: '责任人必选！',
                    },
                  ]}
                >
                  <UserSelect
                    labelInValue={false}
                    mode="multiple"
                    blockGuid={useWhichBlockGuid ?? undefined}
                    disabled={riskStatus ? riskStatus !== 'DRAFT' : false}
                  />
                </Form.Item>
              </Col>
            )}
            {!unusedFormItems?.includes('relatePerson') && !isFull && (
              <Col span={12}>
                <Form.Item
                  label="风险关联方"
                  name="relatePerson"
                  rules={[
                    {
                      validator: async (_, relatePerson) => {
                        if (relatePerson && relatePerson.label.length > 300) {
                          return Promise.reject('最多输入 300 个字符');
                        } else {
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                >
                  <UserSelect
                    reserveSearchValue
                    blockGuid={useWhichBlockGuid ?? undefined}
                    allowClear
                  />
                </Form.Item>
              </Col>
            )}
          </Row>
        </Card>
      ) : (
        <Card
          bordered={false}
          headStyle={{ borderBottom: 0 }}
          bodyStyle={{ paddingTop: 8 }}
          title={
            <Typography.Title showBadge level={5}>
              风险位置
            </Typography.Title>
          }
        >
          <Form.Item
            label=""
            colon={false}
            name="locationList"
            valuePropName="dataSource"
            rules={[
              {
                validator: async (_, locationList) => {
                  if ((locationList ?? []).length <= 0) {
                    return Promise.reject('请选择风险位置');
                  } else {
                    return Promise.resolve();
                  }
                },
              },
            ]}
          >
            <RiskRegisterTabs idcTag={idc} blockTag={block} mode="new" />
          </Form.Item>
        </Card>
      )}
    </Form>
  );
}
function generateRange(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
