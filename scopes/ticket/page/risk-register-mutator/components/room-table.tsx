import sortBy from 'lodash.sortby';
import React, { useEffect, useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Input } from '@manyun/base-ui.ui.input';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import type { RiskLocationInfo } from '@manyun/ticket.model.risk-register';

export type ChangeSourceProps = {
  idcTag?: string | null;
  blockTag?: string | null;
  onChange?: (value: RiskLocationInfo[]) => void;
  dataSource: RiskLocationInfo[];
  showSearch?: boolean;
  mode?: 'new' | 'view';
};
//  & TableProps<RiskLocationInfo>;

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const RoomTable = React.forwardRef(
  (
    { idcTag, blockTag, dataSource, mode, showSearch = true, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [filterValue, setFilterValue] = useState<DeviceParams>({
      pageNum: 1,
      pageSize: 10,
    });
    const [visible, setVisible] = useState<boolean>(false);
    const [selectedCodes, setSelectedCodes] = useState<React.Key[]>([]);
    const [selectBlocks, setSelectBlocks] = useState<RiskLocationInfo[]>([]);
    const [deviceTypes, setDeviceTypes] = useState<string[]>([]);
    const [searchDeviceName, setSearchDeviceName] = useState<string>(''); //搜索设备编号

    useEffect(() => {
      setSelectBlocks(dataSource);
    }, [dataSource]);

    const onDelete = (selectedCodes: string[]) => {
      onChange && onChange(dataSource.filter(item => !selectedCodes.includes(item.guid)));
      setSelectedCodes([]);
    };
    const isNew = mode === 'new';
    const roomDataList = dataSource.filter(item => {
      let isReturn: boolean = true;
      if (searchDeviceName) {
        isReturn = !!item.name?.includes(searchDeviceName);
      }
      if (deviceTypes.length && item.subType) {
        isReturn = isReturn && deviceTypes?.includes(item.subType);
      }

      return isReturn;
    });

    const roomTypeTag = dataSource.reduce((map, key) => {
      if (key.subType && key.subType in map) {
        map[key.subType] += 1;
      } else {
        map[key.subType] = 1;
      }
      return map;
    }, {});
    const roomTypeTags = [];
    for (const key in roomTypeTag) {
      roomTypeTags.push({ key, value: roomTypeTag[key] });
    }

    return (
      <Space style={{ width: '100%' }} direction="vertical">
        <Space direction="horizontal" style={{ width: '100%' }} wrap>
          {sortBy(roomTypeTags, function ({ value }) {
            return -value;
          }).map(({ key, value }) => {
            const isSelected = deviceTypes.includes(key);
            return (
              <Tag key={key} color={isSelected ? 'processing' : 'default'}>
                <div
                  style={{ cursor: 'pointer' }}
                  onClick={() => {
                    if (isSelected) {
                      setDeviceTypes(deviceTypes.filter(i => i !== key));
                    } else {
                      setDeviceTypes([...deviceTypes, key]);
                    }
                  }}
                >
                  {'  '}
                  <RoomTypeText code={key} />
                  {`  ${value}  `}
                </div>
              </Tag>
            );
          })}
        </Space>
        <Space>
          {isNew && (
            <Button type="primary" disabled={!idcTag} onClick={() => setVisible(true)}>
              添加风险位置
            </Button>
          )}
          {showSearch && (
            <Input.Search
              placeholder="搜索包间编号"
              allowClear
              value={searchDeviceName}
              onChange={e => setSearchDeviceName(e.target.value)}
              onSearch={(value: string) => {
                setSearchDeviceName(value);
              }}
            />
          )}
        </Space>
        {selectedCodes.length > 0 && isNew && (
          <Alert
            message={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Space>
                  <Typography.Text>已选择 {selectedCodes.length} 项</Typography.Text>
                  <Button type="link" compact onClick={() => setSelectedCodes([])}>
                    取消选择
                  </Button>
                </Space>
                <Popconfirm
                  title="您即将删除选中数据"
                  onConfirm={() => onDelete(selectedCodes as string[])}
                >
                  <Typography.Link>批量删除</Typography.Link>
                </Popconfirm>
              </div>
            }
            type="info"
          />
        )}
        <Drawer
          width={378}
          style={{ zIndex: 1100 }}
          title="选择包间"
          placement="right"
          open={visible}
          destroyOnClose
          extra={
            <Space>
              <Button
                onClick={() => {
                  setVisible(false);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  onChange && onChange(selectBlocks);
                  setVisible(false);
                  setSelectBlocks([]);
                }}
              >
                确定
              </Button>
            </Space>
          }
          onClose={() => {
            setVisible(false);
          }}
        >
          <Typography.Text
            style={{ marginBottom: 16, display: 'block' }}
          >{`已选${selectBlocks.length}个包间`}</Typography.Text>
          <ResourceTree
            authorizedOnly
            treeMode={['BLOCK', 'ROOM_TYPE', 'ROOM']}
            checkable
            checkableNodes={['ROOM_TYPE', 'ROOM']}
            defaultCheckedKeys={selectBlocks.map(item => item.guid)}
            onlySearchRoom
            spaceGuid={idcTag && blockTag ? `${idcTag}.${blockTag}` : (idcTag ?? '')}
            style={{ padding: 0 }}
            onCheck={(_, info) => {
              setSelectBlocks(
                info.checkedNodes
                  .filter(item => item.type !== 'ROOM_TYPE')
                  .map(item => ({
                    guid: item.key,
                    locationType: 'ROOM',
                    subType: item.custom.type,
                    name: getSpaceGuidMap(item.key).room,
                    fromBlockGuid: item.parentKey.substring(0, item.parentKey.lastIndexOf('.')),
                  }))
              );
            }}
          />
        </Drawer>
        <Table<RiskLocationInfo>
          size="middle"
          rowKey="guid"
          dataSource={roomDataList}
          columns={[
            {
              title: '包间编号',
              dataIndex: 'name',
            },
            {
              title: '包间类型',
              dataIndex: 'subType',
              render: (subType: string) => <RoomTypeText code={subType} />,
            },
            {
              title: '所属楼栋',
              dataIndex: 'fromBlockGuid',
              render: (fromBlockGuid: string) => <SpaceText guid={fromBlockGuid} />,
            },

            {
              title: '操作',
              dataIndex: 'operation',
              render: (_: string, { guid }: RiskLocationInfo) => (
                <Button
                  type="link"
                  compact
                  onClick={() => {
                    onDelete([guid] as string[]);
                  }}
                >
                  移除
                </Button>
              ),
            },
          ].filter(item => {
            if (!isNew && item.dataIndex === 'operation') {
              return false;
            }
            return true;
          })}
          rowSelection={
            isNew
              ? {
                  selectedRowKeys: selectedCodes,
                  onChange: (selectedRowKeys, selectedRows) => {
                    setSelectedCodes(keys => [
                      ...keys.filter(key => !dataSource.find(item => item.guid === key)),
                      ...selectedRowKeys,
                    ]);
                  },
                }
              : undefined
          }
          pagination={{
            total: roomDataList.length,
            current: filterValue.pageNum,
            pageSize: filterValue.pageSize,
            onChange: (pageNum, pageSize) => {
              setFilterValue({ ...filterValue, pageNum, pageSize });
            },
          }}
        />
      </Space>
    );
  }
);

RoomTable.displayName = 'RoomTable';
