import React, { useEffect, useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';
import type { RiskLocationInfo } from '@manyun/ticket.model.risk-register';

export type ChangeSourceProps = {
  idcTag?: string | null;
  blockTag?: string | null;
  onChange?: (value: RiskLocationInfo[]) => void;
  dataSource: RiskLocationInfo[];
  mode?: 'new' | 'view';
};

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const BlockTable = React.forwardRef(
  (
    { idcTag, blockTag, dataSource, mode, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [filterValue, setFilterValue] = useState<DeviceParams>({
      pageNum: 1,
      pageSize: 10,
    });
    const [visible, setVisible] = useState<boolean>(false);
    const [selectedCodes, setSelectedCodes] = useState<React.Key[]>([]);
    const [selectBlocks, setSelectBlocks] = useState<RiskLocationInfo[]>([]);

    useEffect(() => {
      visible && setSelectBlocks(dataSource);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible]);

    const onDelete = (selectedCodes: string[]) => {
      onChange && onChange(dataSource.filter(item => !selectedCodes.includes(item.guid)));
      setSelectedCodes([]);
    };
    const isNew = mode === 'new';

    return (
      <Space style={{ width: '100%' }} direction="vertical">
        {isNew && (
          <Popover
            destroyTooltipOnHide
            content={
              <div
                style={{
                  width: '100%',
                  minWidth: 30,
                  marginLeft: -26,
                  marginRight: -16,
                  marginTop: -38,
                }}
              >
                <ResourceTree
                  authorizedOnly
                  treeMode={['BLOCK']}
                  checkable
                  checkableNodes={['BLOCK']}
                  defaultCheckedKeys={selectBlocks.map(item => item.guid)}
                  showSearch={false}
                  spaceGuid={idcTag && blockTag ? `${idcTag}.${blockTag}` : idcTag ?? ''}
                  style={{ padding: 0, minWidth: 30, marginLeft: 8 }}
                  onCheck={(_, info) => {
                    setSelectBlocks(
                      info.checkedNodes.map(item => ({
                        guid: item.key,
                        locationType: 'BLOCK',
                        name: item.title as string,
                      }))
                    );
                  }}
                />
                <Space direction="vertical" style={{ marginLeft: 24, width: 'calc(100% - 8px)' }}>
                  <Divider style={{ margin: 0, width: 'calc(100% + 16px)' }} />
                  <Space
                    direction="horizontal"
                    style={{ justifyContent: 'space-between', width: 'calc(100% + 16px)' }}
                  >
                    <Button
                      size="small"
                      onClick={() => {
                        setVisible(false);
                        setSelectBlocks([]);
                      }}
                    >
                      取消
                    </Button>
                    <Button
                      type="primary"
                      size="small"
                      onClick={() => {
                        onChange && onChange(selectBlocks);
                        setVisible(false);
                        setSelectBlocks([]);
                      }}
                    >
                      添加
                    </Button>
                  </Space>
                </Space>
              </div>
            }
            overlayStyle={{ padding: 0, minWidth: 150 }}
            overlayInnerStyle={{ padding: 0, width: 'calc(100% - 72px)' }}
            trigger="click"
            open={visible}
            placement="topLeft"
            onOpenChange={setVisible}
          >
            <Button
              type="primary"
              disabled={!idcTag}
              onClick={() => {
                setVisible(true);
              }}
            >
              添加风险位置
            </Button>
          </Popover>
        )}

        {selectedCodes.length > 0 && isNew && (
          <Alert
            message={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Space>
                  <Typography.Text>已选择 {selectedCodes.length} 项</Typography.Text>
                  <Button type="link" compact onClick={() => setSelectedCodes([])}>
                    取消选择
                  </Button>
                </Space>
                <Popconfirm
                  title="您即将删除选中数据"
                  onConfirm={() => onDelete(selectedCodes as string[])}
                >
                  <Typography.Link>批量删除</Typography.Link>
                </Popconfirm>
              </div>
            }
            type="info"
          />
        )}
        <Table<RiskLocationInfo>
          size="middle"
          rowKey="guid"
          dataSource={dataSource}
          columns={[
            {
              title: '楼栋',
              dataIndex: 'name',
            },
            {
              title: '操作',
              dataIndex: 'operation',
              render: (_: string, { guid }: { guid: string }) => (
                <Button
                  type="link"
                  onClick={() => {
                    onDelete([guid] as string[]);
                  }}
                >
                  移除
                </Button>
              ),
            },
          ].filter(item => {
            if (!isNew && item.dataIndex === 'operation') {
              return false;
            }
            return true;
          })}
          rowSelection={
            isNew
              ? {
                  selectedRowKeys: selectedCodes,
                  onChange: (selectedRowKeys, selectedRows) => {
                    setSelectedCodes(keys => [
                      ...keys.filter(key => !dataSource.find(item => item.guid === key)),
                      ...selectedRowKeys,
                    ]);
                    // setSelectedRows(rows => [
                    //   ...rows.filter(row => !deviceData.find(item => item.guid === row.value)),
                    //   ...selectedRows,
                    // ]);
                  },
                }
              : undefined
          }
          pagination={{
            total: dataSource.length,
            current: filterValue.pageNum,
            pageSize: filterValue.pageSize,
            onChange: (pageNum, pageSize) => {
              setFilterValue({ ...filterValue, pageNum, pageSize });
            },
          }}
        />
      </Space>
    );
  }
);

BlockTable.displayName = 'BlockTable';
