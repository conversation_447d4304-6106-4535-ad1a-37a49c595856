import dayjs from 'dayjs';
import moment from 'moment';
import React, { useRef, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import {
  RISK_REGISTERS,
  generateRiskRegisterDetailLocation,
} from '@manyun/ticket.route.ticket-routes';
import { createRiskRegister } from '@manyun/ticket.service.create-risk-register';

import { RiskRegisterForm } from './components/risk-register-form';

export function RiskRegisterMutator() {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const formIdRef = useRef(`${shortid()}_${moment().valueOf()}`);
  const history = useHistory();
  const { search } = useLocation();
  const { eventSourceNo } = getLocationSearchMap<{
    eventSourceNo?: string;
    eventSourceType?: string;
    spaceGuid?: string;
  }>(search);

  const onSubmit = () => {
    form.validateFields().then(async values => {
      setLoading(true);
      const { data, error } = await createRiskRegister({
        ...values,
        formId: formIdRef.current,
        commit: true,
        immutableEventIdList: eventSourceNo ? [eventSourceNo] : null,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('风险单创建成功');
      history.push(generateRiskRegisterDetailLocation({ id: data.riskId }));
    });
  };

  const onSave = async () => {
    form.validateFields(['blockGuid']).then(async () => {
      const values = form.getFieldsValue();
      setLoading(true);
      const { error } = await createRiskRegister({
        ...form.getFieldsValue(),
        formId: formIdRef.current,
        commit: false,
        planCompleteTime: values.planCompleteTime ? dayjs(values.planCompleteTime).valueOf() : null,
        immutableEventIdList: eventSourceNo ? [eventSourceNo] : null,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('保存草稿成功');
      history.push(RISK_REGISTERS);
    });
  };

  return (
    <Card
      style={{ height: '100%', display: 'flex', paddingTop: 8, marginBottom: 52 }}
      bodyStyle={{ padding: 0 }}
      bordered={false}
    >
      <RiskRegisterForm form={form} />

      <FooterToolBar>
        <Space>
          <Button loading={loading} type="primary" onClick={onSubmit}>
            提交
          </Button>
          <Button loading={loading} onClick={onSave}>
            保存草稿
          </Button>
        </Space>
      </FooterToolBar>
      {/* </Form.Item> */}
    </Card>
  );
}
