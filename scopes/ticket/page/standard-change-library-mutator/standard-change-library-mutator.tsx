import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import { message } from 'antd';
import omit from 'lodash.omit';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { useLazyChangeTemplate, useUpdateChangeTemplate } from '@manyun/ticket.gql.client.tickets';
import { generateStandardChangeLibraryDetailLocation } from '@manyun/ticket.route.ticket-routes';

import { SelectChange } from './components/select-change';

export const CHANGE_RISK_LEVEL_TEXT_MAP = {
  STANDARD_CHANGE_SECOND: '标准变更（二级）',
  STANDARD_CHANGE_THIRD: '标准变更（三级）',
  STANDARD_CHANGE_FOURTH: '标准变更（四级）',
};

export function StandardChangeLibraryMutator() {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const blockGuid = Form.useWatch('availArea', form);
  const history = useHistory();
  const { id } = useParams<{ id: string }>();

  const [updateChangeTemplate, { loading: submitLoading }] = useUpdateChangeTemplate();
  const [getChangeTemplate] = useLazyChangeTemplate();

  const onSubmit = () => {
    form.validateFields().then(async values => {
      const { change, fileInfoList, ...resp } = values;
      updateChangeTemplate({
        variables: {
          query: {
            ...resp,
            sourceChangeId: change[0].changeOrderId,
            sourceChangeTitle: change[0].title,
            changeVersion: 2,
            fileInfoList: fileInfoList?.map(obj => ({
              ...omit(
                McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
                '__typename'
              ),
            })),
            templateId: id,
          },
        },
        onCompleted(data) {
          if (!data.updateChangeTemplate?.success) {
            message.error(data.updateChangeTemplate?.message);
            return;
          }
          if (data.updateChangeTemplate?.success && data.updateChangeTemplate.data) {
            history.push(
              generateStandardChangeLibraryDetailLocation({ id: data.updateChangeTemplate.data })
            );
            if (id) {
              message.success('修改标准变更模版成功');
            } else {
              message.success('新建标准变更模版成功');
            }
          }
        },
      });
    });
  };

  useEffect(() => {
    id &&
      getChangeTemplate({
        variables: { templateId: id },
        onCompleted(data) {
          if (!data.changeTemplate?.success) {
            message.error(data.changeTemplate?.message);
            return;
          }
          form.setFieldsValue({
            ...data.changeTemplate.data,
            change: [
              {
                changeOederId: data.changeTemplate.data?.sourceChangeId,
                title: data.changeTemplate.data?.sourceChangeTitle,
              },
            ],
          });
        },
      });
  }, [id, getChangeTemplate, form]);

  return (
    <Card
      bodyStyle={{ justifyContent: 'center', display: 'flex' }}
      title={id ? '编辑标准变更模版' : '新建标准变更模版'}
    >
      <Form
        style={{ width: 650 }}
        labelCol={{ xl: 5 }}
        wrapperCol={{ xl: 19 }}
        colon={false}
        form={form}
      >
        <Form.Item
          label="模版名称"
          name="templateName"
          rules={[
            {
              required: true,
              message: '模版名称必填！',
              whitespace: true,
            },
            {
              max: 50,
              message: '最多输入 50 个字符！',
            },
          ]}
        >
          <Input allowClear style={{ width: 334 }} />
        </Form.Item>
        <Form.Item
          label="适用机房/楼"
          name="availArea"
          rules={[
            {
              required: true,
              message: '适用机房/楼必选！',
            },
            {
              validator: (_, value) => {
                if (value && value.split('.').length !== 2) {
                  return Promise.reject('请选择至楼栋！');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <LocationTreeSelect
            style={{ width: 334 }}
            disabledTypes={['IDC']}
            authorizedOnly
            showSearch
            allowClear
            onChange={() => {
              form.setFieldValue('change', undefined);
            }}
          />
        </Form.Item>

        <Form.Item
          label="一般变更实施记录"
          name="change"
          rules={[
            {
              required: true,
              message: '一般变更实施记录必选！',
            },
          ]}
        >
          <SelectChange style={{ width: 334 }} blockTag={blockGuid} />
        </Form.Item>
        <Form.Item
          label="变更等级"
          name="riskLevel"
          rules={[
            {
              required: true,
              message: '变更等级必选！',
            },
          ]}
        >
          <Select
            style={{ width: 334 }}
            showArrow
            options={Object.entries(CHANGE_RISK_LEVEL_TEXT_MAP).map(item => ({
              label: item[1],
              value: item[0],
            }))}
          />
        </Form.Item>
        <Form.Item
          label="变更专业"
          name="reason"
          rules={[
            {
              required: true,
              message: '变更专业必填！',
            },
          ]}
        >
          <MetaTypeSelect style={{ width: 334 }} metaType={MetaType.CHANGE_REASON} />
        </Form.Item>
        <Form.Item
          label="变更类型"
          name="changeType"
          rules={[
            {
              required: true,
            },
          ]}
        >
          <MetaTypeSelect style={{ width: 334 }} metaType={MetaType.CHANGE} />
        </Form.Item>
        <Form.Item
          label="变更方案附件"
          name="fileInfoList"
          rules={[{ required: true, message: '请选择上传文件！' }]}
          valuePropName="fileList"
          getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
            if (fileList.filter(file => file.status === 'uploading').length) {
              setLoading(true);
            } else {
              setLoading(false);
            }
            return fileList;
          }}
        >
          <McUpload
            showAccept
            accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
            showUploadList
            maxCount={5}
            maxFileSize={20}
            openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 5}
          >
            <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 5}>
              点此上传
            </Button>
          </McUpload>
        </Form.Item>
        <Form.Item label=" " colon={false} labelCol={{ span: 5 }}>
          <Button type="primary" loading={loading || submitLoading} onClick={onSubmit}>
            提交
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}
