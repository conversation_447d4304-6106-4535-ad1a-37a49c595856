import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useLazyChanges } from '@manyun/ticket.gql.client.tickets';
import type { FetchChangeQ, FetchChangesData } from '@manyun/ticket.gql.client.tickets';
import { generateChangeTicketDetail } from '@manyun/ticket.route.ticket-routes';

export type ChangeSourceProps = {
  blockTag: string;
  onChange?: (value: FetchChangesData[]) => void;
  value?: FetchChangesData[];
} & TableProps<FetchChangesData>;

export const SelectChange = React.forwardRef(
  ({ blockTag, value, onChange }: ChangeSourceProps, ref: React.Ref<RefSelectProps>) => {
    const [changeData, setChangeData] = useState<FetchChangesData[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [filterValue, setFilterValue] = useState<FetchChangeQ>({
      pageNum: 1,
      pageSize: 10,
    });
    const [visible, setVisible] = useState<boolean>(false);
    const [selectedRows, setSelectedRows] = useState<FetchChangesData[]>([]);

    const [getChanges] = useLazyChanges();
    useEffect(() => {
      (async () => {
        if (!visible) {
          return;
        }
        const { data, error: fetchDeviceListError } = await getChanges({
          variables: {
            query: {
              ...filterValue,
              blockTag,
              changeVersion: 2,
              exeResult: 'SUCCESS',
              changeLevelList: [
                'NORMAL_CHANGE_FIRST',
                'NORMAL_CHANGE_SECOND',
                'NORMAL_CHANGE_THIRD',
                'NORMAL_CHANGE_FOURTH',
              ],
            },
          },
        });
        if (fetchDeviceListError) {
          setChangeData([]);
          message.error(fetchDeviceListError.message);
          return;
        }
        if (data?.changes.data) {
          setChangeData(data.changes.data);
          setTotal(data.changes.total);
        } else {
          setChangeData([]);
        }
      })();
    }, [filterValue, blockTag, visible, getChanges]);

    return (
      <Space style={{ width: '100%' }} direction="horizontal">
        <Button
          disabled={!blockTag}
          onClick={() => {
            setVisible(true);
            setSelectedRows(value ?? []);
          }}
        >
          {value?.length ? '点此修改' : '点此关联'}
        </Button>
        {value?.length ? (
          <Link
            target="_blank"
            to={{
              pathname: generateChangeTicketDetail({
                id: value[0].changeOrderId,
              }),
            }}
          >
            {value[0].title}
          </Link>
        ) : null}
        <Modal
          style={{ maxHeight: '80%', maxWidth: '85%', minWidth: 947 }}
          title={
            <Explanation
              iconType="question"
              tooltip={{
                title: '当前结果为已执行成功的一般变更记录',
              }}
            >
              <Typography.Text>关联一般变更实施记录</Typography.Text>
            </Explanation>
          }
          open={visible}
          destroyOnClose
          onCancel={() => {
            setVisible(false);
            setFilterValue({ pageNum: 1, pageSize: 10 });
            setSelectedRows([]);
          }}
          onOk={() => {
            setFilterValue({ pageNum: 1, pageSize: 10 });
            setSelectedRows([]);
            setVisible(false);
            onChange && onChange(selectedRows);
          }}
        >
          <Space style={{ width: '100%' }} direction="vertical">
            <Space style={{ width: '100%' }} direction="horizontal">
              <Input.Search
                placeholder="搜索变更ID或标题查询"
                allowClear
                onSearch={value => {
                  setFilterValue({ ...filterValue, pageNum: 1, changeOrderIdOrTitle: value });
                }}
              />
              <MetaTypeSelect
                metaType={MetaType.CHANGE_REASON}
                mode="multiple"
                allowClear
                placeholder="变更专业"
                style={{ width: 196 }}
                onChange={value => {
                  setFilterValue({
                    ...filterValue,
                    pageNum: 1,
                    changeReasonList: value as string[],
                  });
                }}
              />
              <MetaTypeSelect
                metaType={MetaType.CHANGE}
                style={{ width: 196 }}
                allowClear
                placeholder="变更类型"
                onChange={value => {
                  setFilterValue({
                    ...filterValue,
                    pageNum: 1,
                    changeType: value as string,
                  });
                }}
              />
              <MetaTypeSelect
                metaType={MetaType.CHANGE_LEVEL}
                style={{ width: 196 }}
                allowClear
                placeholder="变更等级"
                onChange={value => {
                  setFilterValue({
                    ...filterValue,
                    pageNum: 1,
                    riskLevel: value as string,
                  });
                }}
              />
            </Space>
            <Table
              size="middle"
              rowKey="changeOrderId"
              dataSource={changeData}
              columns={[
                {
                  title: '变更ID',
                  dataIndex: 'changeOrderId',
                  fixed: 'left',
                  render: (text: string) => {
                    return (
                      <Link
                        target="_blank"
                        to={{
                          pathname: generateChangeTicketDetail({
                            id: text,
                          }),
                        }}
                      >
                        {text}
                      </Link>
                    );
                  },
                },
                {
                  title: '变更标题',
                  dataIndex: 'title',
                },
                {
                  title: '位置',
                  dataIndex: 'blockTag',
                },
                {
                  title: '变更专业',
                  dataIndex: 'reason',
                  render: (_, { reason }) =>
                    reason ? (
                      <MetaTypeText code={reason} metaType={MetaType.CHANGE_REASON} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '变更类型',
                  dataIndex: 'changeType',
                  render: (_, { changeType }) =>
                    changeType ? (
                      <MetaTypeText code={changeType} metaType={MetaType.CHANGE} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '变更等级',
                  dataIndex: 'riskLevel',
                  render: (_, { riskLevel }) =>
                    riskLevel ? (
                      <MetaTypeText code={riskLevel} metaType={MetaType.CHANGE_LEVEL} />
                    ) : (
                      '--'
                    ),
                },
              ]}
              rowSelection={{
                selectedRowKeys: selectedRows.map(item => item.changeOrderId),
                type: 'radio',
                onChange: (_, selectedRows) => {
                  setSelectedRows(selectedRows);
                },
              }}
              pagination={{
                total: total,
                current: filterValue.pageNum,
                pageSize: filterValue.pageSize,
                onChange: (pageNum, pageSize) => {
                  setFilterValue({ ...filterValue, pageNum, pageSize });
                },
              }}
            />
          </Space>
        </Modal>
      </Space>
    );
  }
);

SelectChange.displayName = 'SelectChange';
