import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Table } from '@manyun/base-ui.ui.table';
import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

export type CopyStepMonitorProps = {
  deviceType: string;
  pointCode: string;
  deviceList: {
    checkPointValue: number;
    deviceGuid: string;
    deviceLabel: string;
    deviceName: string;
    deviceTag: string;
    roomTag: string;
  }[];
};

export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const PointValuePopover = ({ deviceType, deviceList, pointCode }: CopyStepMonitorProps) => {
  const [visible, setVisible] = useState(false);
  const [validLimits, setValidLimits] = useState<{ label: string; value: string }[]>([]);

  const getPoint = async () => {
    const { data } = await fetchPointsByCondition({
      deviceType,
      dataTypeList: ['DI'],
      pointTypeList: ['ORI', 'CAL_DEVICE', 'AGG_DEVICE'],
      isRemoveSub: true,
    });
    if (data && Array.isArray(data.data) && data.data.length) {
      const result = data.data.map(point => point.toApiObject());
      const validLimitsItem = result.filter(item => item.pointCode === pointCode);
      const list = validLimitsItem.length
        ? generateValidLimitsDataSource(validLimitsItem[0].validLimits)
        : [];

      setValidLimits(list);
    }
  };
  return (
    <>
      <Popover
        destroyTooltipOnHide
        overlayInnerStyle={{ maxWidth: 500 }}
        title="测点值"
        open={visible}
        content={
          <Table
            rowKey="id"
            dataSource={deviceList}
            size="small"
            columns={[
              {
                title: '设备编号',
                dataIndex: 'deviceName',
              },
              {
                title: '测点值',
                dataIndex: 'checkPointValue',
                render: (_, { checkPointValue }) => {
                  if (checkPointValue === null || checkPointValue === undefined) {
                    return '--';
                  }
                  const pointValidLimit = validLimits.filter(
                    item => item.value === checkPointValue?.toString()
                  );

                  return pointValidLimit.length ? pointValidLimit[0].label : '--';
                },
              },
            ]}
            pagination={{
              pageSize: 3,
              total: deviceList.length,
              showSizeChanger: false,
            }}
          />
        }
        placement="topRight"
        onOpenChange={v => {
          setVisible(v);
          if (v) {
            getPoint();
          }
        }}
      >
        <Button compact type="link">
          查看
        </Button>
      </Popover>
    </>
  );
};
