import { QuestionCircleOutlined } from '@ant-design/icons';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import React from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { type RefSelectProps, Select } from '@manyun/base-ui.ui.select';
import { type ColumnType, Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateAlarmConfigurationTemplateDetailUrl } from '@manyun/monitoring.route.monitoring-routes';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import type { ChangeStepInfo } from './add-change-step';
import { DeviceTypeSelectDrawer } from './device-type-select-drawer';
import { MonitorDeviceSpaceTableModal } from './monitor-device-space-table-modal';
import { MonitorGroupSelectDrawer } from './monitor-group-select-drawer';
import { monitorGroupDataMergeUtil } from './monitor-group-table';
import type { MonitorGroupMergeData, SelectedMonitorGroup } from './monitor-group-table';

export type MonitorGroupTableProps = {
  mode: 'delete' | 'view' | 'editAndDelete';
  dataSource: MonitorGroupMergeData[];
  stepCodes?: string[];
  templateMoreLocations?: boolean;
  needCheck?: boolean;
  currentStep: string;
  stepMaps: Record<string, ChangeStepInfo>;
  idcTag: string;
  blockTag: string;
  ticketMode?: string;
  onChange: (
    values: MonitorGroupMergeData[],
    stepMaps: Record<string, ChangeStepInfo>,
    type?: 'delete'
  ) => void;
};

export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const ChangeStepMonitorTable = React.forwardRef(
  (
    {
      dataSource,
      mode,
      stepCodes = [],
      templateMoreLocations,
      needCheck,
      currentStep,
      stepMaps,
      idcTag,
      blockTag,
      ticketMode,
      onChange,
    }: MonitorGroupTableProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const columns = () => {
      let basicColumns: ColumnType<MonitorGroupMergeData>[] = [
        {
          title: '类型',
          dataIndex: 'deviceType',
          render: text => {
            if (text === '90101') {
              return '机房';
            }
            if (text === '90102') {
              return '楼栋';
            }
            if (text === '90103') {
              return '包间';
            }
            if (text === '90104') {
              return '机列';
            }
            if (text === '90105') {
              return '机柜';
            }
            return <DeviceTypeText code={text} />;
          },
          onCell: (record, index) => {
            return {
              rowSpan: generateGetRowSpan(dataSource, { mergeProp: 'deviceType' })(
                record,
                index ?? 1
              ),
            };
          },
        },
        {
          title: '屏蔽周期',
          dataIndex: 'maxInfluencesStep',
          render: (text, { deviceType, id }) => {
            if (mode === 'editAndDelete') {
              const idx = stepCodes.indexOf(currentStep);
              const codes = stepCodes.slice(idx);
              return (
                <Form.Item
                  label=""
                  name={`${deviceType}-${id}-${currentStep}-maxInfluencesStep`}
                  style={{ marginBottom: 0 }}
                  initialValue={text}
                >
                  <Select
                    value={text}
                    options={[
                      ...codes.map(item => ({
                        label: `至步骤${stepCodes.indexOf(item) + 1}`,
                        value: item,
                      })),
                      { label: '至变更完成', value: '99999' },
                    ]}
                    onChange={value => {
                      // setFieldValue(
                      //   `${deviceType}-${id}-${currentStep}-maxInfluencesStep`,
                      //   undefined
                      // );
                      onChange(
                        dataSource.map(item => {
                          if (item.deviceType === deviceType) {
                            return {
                              ...item,
                              deviceList: item?.deviceList?.map(item => ({
                                ...item,
                                maxInfluencesStep: value,
                              })),
                              maxInfluencesStep: value,
                            };
                          } else {
                            return item;
                          }
                        }),
                        stepMaps
                      );
                    }}
                  />
                </Form.Item>
              );
            }
            if (mode === 'view') {
              if (text === '99999') {
                return '至变更完成';
              }
              return `步骤${stepCodes.indexOf(text) + 1}`;
            }
            return;
          },
          onCell: (record, index) => {
            return {
              rowSpan: generateGetRowSpan(dataSource, { mergeProp: 'deviceType' })(
                record,
                index ?? 1
              ),
            };
          },
        },
        {
          title: (
            <Typography.Text>
              屏蔽设备/空间数{' '}
              <Tooltip title="仅可选择与左侧类型一致的设备/空间">
                <QuestionCircleOutlined />
              </Tooltip>
            </Typography.Text>
          ),
          dataIndex: 'deviceList',
          render: (deviceList, { targetType }) =>
            deviceList?.length ? (
              <MonitorDeviceSpaceTableModal targetType={targetType} dataSource={deviceList} />
            ) : (
              <Typography.Text type={needCheck ? 'danger' : undefined}>--</Typography.Text>
            ),
        },
        {
          title: '屏蔽策略',
          dataIndex: 'inhibitionModelName',
          render: (inhibitionModelName, { inhibitionModelId }) => {
            return inhibitionModelId ? (
              <Link
                type="link"
                target="_blank"
                to={generateAlarmConfigurationTemplateDetailUrl({
                  id: inhibitionModelId.toString(),
                })}
              >
                {inhibitionModelName}
              </Link>
            ) : (
              <Typography.Text type={needCheck ? 'danger' : undefined}>--</Typography.Text>
            );
          },
        },
        {
          title: '屏蔽规则数',
          dataIndex: 'pointNum',
          render: text =>
            text !== null || text !== undefined ? (
              text
            ) : (
              <Typography.Text type={needCheck ? 'danger' : undefined}>--</Typography.Text>
            ),
        },
        {
          title: '移除',
          dataIndex: 'delete',
          render: (_, { inhibitionModelId, deviceType }) => {
            return (
              <Button
                type="link"
                compact
                onClick={() => {
                  onChange(
                    dataSource.filter(item =>
                      item.inhibitionModelId
                        ? item.inhibitionModelId !== inhibitionModelId
                        : item.deviceType !== deviceType
                    ),
                    stepMaps,
                    'delete'
                  );
                }}
              >
                移除
              </Button>
            );
          },
        },
        {
          title: '编辑',
          dataIndex: 'edit',
          render: (_, { deviceType, targetType }) => {
            if (templateMoreLocations) {
              return (
                <DeviceTypeSelectDrawer
                  // dataSource={}
                  mode="edit"
                  onChange={monitorGroup => {
                    onChange(monitorGroup, stepMaps);
                  }}
                />
              );
            }
            const deviceList: SelectedMonitorGroup[] = [];
            dataSource.forEach(item => {
              if (item.deviceType === deviceType) {
                deviceList.push(...(item?.deviceList ?? []));
              }
            });
            return (
              <MonitorGroupSelectDrawer
                idcTag={idcTag}
                blockTag={blockTag}
                dataSource={deviceList}
                deviceType={deviceType}
                targetType={targetType}
                mode="edit"
                onChange={monitorGroup => {
                  const list = [...dataSource];
                  const idx = dataSource.findIndex(data => data.deviceType === deviceType);
                  const monitorGroupMerge = monitorGroupDataMergeUtil(
                    [...monitorGroup],
                    ticketMode
                  );
                  list.splice(
                    idx,
                    list.filter(item => item.deviceType === deviceType).length,
                    ...monitorGroupMerge
                  );
                  onChange(list, stepMaps);
                }}
              />
            );
          },
          onCell: (record, index) => {
            return {
              rowSpan: generateGetRowSpan(dataSource, { mergeProp: 'deviceType' })(
                record,
                index ?? 1
              ),
            };
          },
        },
      ];
      if (mode === 'view') {
        basicColumns = basicColumns.filter(
          (item: ColumnType<MonitorGroupMergeData>) =>
            item.dataIndex !== 'edit' && item.dataIndex !== 'delete'
        );
      }
      if (mode === 'delete') {
        basicColumns = basicColumns.filter(
          (item: ColumnType<MonitorGroupMergeData>) =>
            item.dataIndex !== 'edit' &&
            item.dataIndex !== 'pointNum' &&
            item.dataIndex !== 'maxInfluencesStep'
        );
      }
      if (templateMoreLocations) {
        basicColumns = basicColumns.filter(
          (item: ColumnType<MonitorGroupMergeData>) => item.dataIndex !== 'edit'
        );
      }
      return basicColumns;
      // eslint-disable-next-line react-hooks/exhaustive-deps
    };

    return (
      <Table
        size="middle"
        rowKey="id"
        dataSource={dataSource}
        columns={columns()}
        pagination={false}
      />
    );
  }
);
ChangeStepMonitorTable.displayName = 'ChangeStepMonitorTable';
