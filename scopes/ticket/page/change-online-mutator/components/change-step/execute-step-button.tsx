import { omit } from 'lodash';
import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { useStartExecuteChangeOnlineStepData } from '@manyun/ticket.gql.client.tickets';

import type { ChangeStepInfo } from './add-change-step';

export type CloseRiskButtonProps = {
  stepCodes: string[];
  stepMaps: Record<string, ChangeStepInfo>;
  currentStep: string;
  stepId: number;
  disabled: boolean;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;
type StepFinishRequestInfo = {
  stepOrder: number;
  stepId: number;
  stepStatus: string;
  isFinish: string;
};
export function ExecuteStepButton({
  stepCodes,
  stepMaps,
  currentStep,
  stepId,
  disabled,
  onSuccess,
}: CloseRiskButtonProps) {
  const [visible, setVisible] = useState<string[]>([]);
  const [form] = Form.useForm<{ stepFinishRequestList: StepFinishRequestInfo[] }>();
  const [startExecuteChangeOnlineStep, { loading }] = useStartExecuteChangeOnlineStepData({
    onCompleted(data) {
      if (!data.startExecuteChangeOnlineStep.success) {
        message.error(data.startExecuteChangeOnlineStep.message);
        return;
      }
      setVisible([]);
      onSuccess();
    },
  });

  const onSubmit = async () => {
    const { stepFinishRequestList } = form.getFieldsValue();
    startExecuteChangeOnlineStep({
      variables: {
        query: {
          stepId,
          stepFinishRequestList: stepFinishRequestList
            .filter(item => item.isFinish === 'true')
            .map(item => omit(item, 'isFinish')),
        },
      },
    });
  };

  return (
    <>
      <Button
        type="primary"
        size="small"
        loading={loading}
        disabled={disabled}
        onClick={() => {
          const idx = stepCodes.indexOf(currentStep);
          const preStep = [...stepCodes].splice(0, idx);
          const unFinished = preStep.filter(item => stepMaps[item].stepStatus === 'PROCESSING');
          if (unFinished.length) {
            setVisible(unFinished);
          } else {
            startExecuteChangeOnlineStep({
              variables: {
                query: {
                  stepId,
                },
              },
            });
          }
        }}
      >
        执行
      </Button>
      <Modal
        title="执行"
        open={!!visible.length}
        okText="确认"
        width={640}
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: loading }}
        onCancel={() => {
          setVisible([]);
        }}
        onOk={onSubmit}
      >
        <Alert
          description="此步骤前尚有步骤未结束，请选择是否结束"
          type="warning"
          style={{ marginBottom: 24 }}
          showIcon
        />

        <Form
          form={form}
          initialValues={{
            stepFinishRequestList: visible.map(item => ({
              stepOrder: stepMaps[item].stepOrder,
              stepId: stepMaps[item].id,
              stepStatus: 'NORMAL',
              isFinish: 'true',
            })),
          }}
        >
          <Form.List name="stepFinishRequestList">
            {fields => (
              <>
                {fields.map(({ key, name, ...field }, index) => (
                  <Form.Item
                    key={`${key}.${name}`}
                    label=""
                    required={index === 0}
                    colon={index === 0}
                    noStyle
                    name={[name]}
                    {...field}
                  >
                    <Steps />
                  </Form.Item>
                ))}
              </>
            )}
          </Form.List>
        </Form>
      </Modal>
    </>
  );
}

const Steps = React.forwardRef(
  ({
    value,
    onChange,
  }: {
    value?: { stepOrder: number; stepId: number; stepStatus: string; isFinish: string };
    onChange?: (value: {
      stepOrder: number;
      stepId: number;
      stepStatus: string;
      isFinish: string;
    }) => void;
  }) => {
    return (
      <Space direction="horizontal" style={{ width: '100%' }}>
        <Form.Item label={`结束步骤${value?.stepOrder}`} required>
          <Radio.Group
            // defaultValue="true"
            value={value?.isFinish}
            onChange={e => {
              onChange &&
                onChange({
                  ...value,
                  isFinish: e.target.value,
                  stepStatus: e.target.value === 'true' ? 'NORMAL' : 'EXCEPTION',
                });
            }}
          >
            <Radio value="true">是</Radio>
            <Radio value="false">否</Radio>
          </Radio.Group>
        </Form.Item>
        {value?.isFinish === 'true' && (
          <Form.Item label={`步骤${value?.stepOrder}状态`} required>
            <Radio.Group
              // defaultValue="true"
              value={value?.stepStatus}
              onChange={e => {
                onChange && onChange({ ...value, stepStatus: e.target.value });
              }}
            >
              <Radio value="NORMAL">正常</Radio>
              <Radio value="EXCEPTION">异常</Radio>
            </Radio.Group>
          </Form.Item>
        )}
      </Space>
    );
  }
);
Steps.displayName = 'Steps';
