import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { DeviceTypeTree } from '@manyun/resource-hub.ui.device-type-cascader';

import type { SelectedMonitorGroup } from './monitor-group-table';

export type ChangeSourceProps = {
  onChange?: (value: SelectedMonitorGroup[]) => void;
  dataSource?: SelectedMonitorGroup[];
  mode?: 'new' | 'edit';
};

export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const DeviceTypeSelectDrawer = React.forwardRef(
  ({ dataSource = [], mode, onChange }: ChangeSourceProps, ref: React.Ref<RefSelectProps>) => {
    const [deviceTypeData, setMonitorData] = useState<SelectedMonitorGroup[]>([]);
    const [visible, setVisible] = useState(false);

    const [showDeviceTree, setShowDeviceTree] = useState(true);

    const onClose = () => {
      setVisible(false);
      setMonitorData([]);
    };
    useEffect(() => {
      if (!showDeviceTree) {
        setTimeout(() => {
          setShowDeviceTree(true);
        }, 200);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showDeviceTree]);

    return (
      <>
        <Button
          size="small"
          type={mode === 'edit' ? 'link' : 'primary'}
          compact={mode === 'edit'}
          onClick={() => {
            setVisible(true);

            setMonitorData(dataSource);
          }}
        >
          {mode === 'edit' ? '编辑' : '添加屏蔽对象'}
        </Button>
        <Drawer
          width={430}
          title="添加屏蔽对象"
          placement="right"
          open={visible}
          destroyOnClose
          extra={
            <Space>
              <Button
                onClick={() => {
                  onClose();
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  onChange && onChange(deviceTypeData);
                  onClose();
                }}
              >
                确定
              </Button>
            </Space>
          }
          onClose={() => {
            onClose();
          }}
        >
          <Space size="large" style={{ height: '100%', display: 'flex' }} direction="vertical">
            <Typography.Text type="secondary">
              当前模板适用多个位置，模板中仅支持选择到设备/空间类型，具体的设备/空间请在变更单中选择{' '}
            </Typography.Text>

            <DeviceTypeTree
              treeStyle={{
                overflowY: 'auto',
              }}
              numbered
              dataType={['snDevice', 'space']}
              inputProps={{
                style: { marginBottom: 8 },
                placeholder: '类型',
              }}
              checkedKeys={deviceTypeData.map(item => item.deviceType!)}
              checkable
              defaultExpandAll
              onCheck={(value, info) => {
                const list = info.checkedNodes.filter(item => item.type === 'C2');
                setMonitorData(
                  list.map(item => ({ deviceType: item.value, id: item.key.toString() }))
                );
              }}
              onSelect={(value, info) => {}}
            />
          </Space>
        </Drawer>
      </>
    );
  }
);

DeviceTypeSelectDrawer.displayName = 'DeviceTypeSelectDrawer';
