import uniqBy from 'lodash.uniqby';
import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Empty } from '@manyun/base-ui.ui.empty';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { DeviceTypeTree } from '@manyun/resource-hub.ui.device-type-cascader';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';
import type { NodeType } from '@manyun/resource-hub.ui.resource-tree/resource-tree.type';

import { ChangeStepMonitorTable } from './change-step-monitor-table';
import {
  MonitorGroupTable,
  type SelectDevice,
  type SelectedMonitorGroup,
  monitorGroupDataLoopUtil,
  monitorGroupDataMergeUtil,
} from './monitor-group-table';

export type ChangeSourceProps = {
  idcTag: string;
  blockTag?: string | null;
  deviceType?: string | null;
  targetType?: string | null;
  onChange?: (value: SelectedMonitorGroup[] | string, type?: string | null) => void;
  dataSource?: SelectedMonitorGroup[];
  mode?: 'new' | 'edit';
  ticketMode?: 'ticketNew' | 'templateNew' | 'ticketView' | 'templateView';
};

export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const MonitorGroupSelectDrawer = React.forwardRef(
  (
    {
      idcTag,
      blockTag,
      dataSource = [],
      deviceType,
      targetType,
      mode,
      ticketMode,
      onChange,
    }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [monitorData, setMonitorData] = useState<SelectedMonitorGroup[]>([]);
    const [viewDevice, setViewDevice] = useState(false);
    const [visible, setVisible] = useState(false);

    const [deviceTypeInfo, setDeviceTypeInfo] = useState<{
      deviceType: string;
      targetType: string;
    }>({ deviceType: '', targetType: '' });
    const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
    const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
    const [checkDevices, setCheckDevices] = useState<SelectDevice[]>([]);
    const [showDeviceTree, setShowDeviceTree] = useState(true);

    const onClose = () => {
      //   onCancel();
      setVisible(false);
      setViewDevice(false);
      setMonitorData([]);
      setExpandedKeys([]);
      setCheckedKeys([]);
      setCheckDevices([]);
      setDeviceTypeInfo({ deviceType: '', targetType: '' });

      // setViewDevice(false);
    };
    useEffect(() => {
      if (!showDeviceTree) {
        setTimeout(() => {
          setShowDeviceTree(true);
        }, 200);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showDeviceTree]);

    const getTitle = () => {
      if (deviceTypeInfo.targetType === 'DEVICE') {
        return `已选${uniqBy(monitorData, 'deviceGuid').length}个设备，共${monitorData.length}个策略`;
      }
      if (deviceTypeInfo.targetType === 'BLOCK') {
        return `已选${uniqBy(monitorData, 'deviceGuid').length}个楼栋，共${monitorData.length}个策略`;
      }
      if (deviceTypeInfo.targetType === 'ROOM') {
        return `已选${uniqBy(monitorData, 'deviceGuid').length}个包间，共${monitorData.length}个策略`;
      }
      if (deviceTypeInfo.targetType === 'COLUMN') {
        return `已选${uniqBy(monitorData, 'deviceGuid').length}个机列，共${monitorData.length}个策略`;
      }
      if (deviceTypeInfo.targetType === 'GRID') {
        return `已选${uniqBy(monitorData, 'deviceGuid').length}个机柜，共${monitorData.length}个策略`;
      }
      if (deviceTypeInfo.targetType === 'IDC') {
        return `已选${uniqBy(monitorData, 'deviceGuid').length}个机房，共${monitorData.length}个策略`;
      }
      return '已选0';
    };

    const getTreeMode = (): NodeType[] => {
      if (deviceTypeInfo.targetType === 'BLOCK') {
        return ['BLOCK'];
      }
      if (deviceTypeInfo.targetType === 'ROOM') {
        return ['BLOCK', 'ROOM_TYPE', 'ROOM'];
      }
      if (deviceTypeInfo.targetType === 'COLUMN') {
        return ['BLOCK', 'ROOM_TYPE', 'ROOM', 'COLUMN'];
      }
      if (deviceTypeInfo.targetType === 'GRID') {
        return ['BLOCK', 'ROOM_TYPE', 'ROOM', 'COLUMN', 'GRID'];
      }
      return [];
    };
    const getCheckableNodes = (): NodeType[] => {
      if (deviceTypeInfo.targetType === 'BLOCK') {
        return ['BLOCK'];
      }
      if (deviceTypeInfo.targetType === 'ROOM') {
        return ['ROOM'];
      }
      if (deviceTypeInfo.targetType === 'COLUMN') {
        return ['COLUMN'];
      }
      if (deviceTypeInfo.targetType === 'GRID') {
        return ['GRID'];
      }
      return [];
    };

    return (
      <>
        <Button
          size="small"
          type={mode === 'edit' ? 'link' : 'primary'}
          compact={mode === 'edit'}
          onClick={() => {
            setVisible(true);
            if (mode === 'edit') {
              setDeviceTypeInfo({ deviceType: deviceType ?? '', targetType: targetType ?? '' });
              setCheckDevices(dataSource);
              setMonitorData(dataSource);
            }
          }}
        >
          {mode === 'edit' ? '编辑' : '添加屏蔽对象'}
        </Button>
        <Drawer
          width={1280}
          // style={{ zIndex: 1100 }}
          title="添加屏蔽对象"
          placement="right"
          open={visible}
          destroyOnClose
          extra={
            <Space>
              <Button
                onClick={() => {
                  onClose();
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                disabled={
                  (ticketMode === 'templateNew' && !deviceTypeInfo.deviceType) ||
                  (ticketMode === 'ticketNew' && !monitorData.length)
                }
                onClick={() => {
                  if (
                    ticketMode === 'templateNew' &&
                    deviceTypeInfo.deviceType &&
                    !monitorData.length
                  ) {
                    onChange && onChange(deviceTypeInfo.deviceType, 'deviceType');
                    onClose();
                    return;
                  }
                  onChange && onChange(monitorData);
                  onClose();
                }}
              >
                确定
              </Button>
            </Space>
          }
          onClose={() => {
            onClose();
          }}
        >
          {viewDevice ? (
            <Typography.Link
              style={{ marginBottom: 16, display: 'block' }}
              onClick={() => setViewDevice(false)}
            >
              {'<<'}返回继续选择
            </Typography.Link>
          ) : (
            <Typography.Text style={{ marginBottom: 16, display: 'block' }}>
              {getTitle()}
              <Typography.Link onClick={() => setViewDevice(true)}>
                查看已选项{'>>'}
              </Typography.Link>
            </Typography.Text>
          )}
          {viewDevice && (
            <ChangeStepMonitorTable
              mode="delete"
              dataSource={monitorGroupDataMergeUtil(monitorData)}
              onChange={data => {
                setMonitorData(monitorGroupDataLoopUtil(data));
              }}
            />
          )}

          <div
            style={{
              width: '100%',
              height: '100%',
              alignItems: 'normal',
              marginTop: 16,
              display: viewDevice ? 'none' : 'flex',
              flexDirection: 'row',
              gap: 8,
            }}
          >
            <Space style={{ width: 240, height: '100%', display: 'flex' }} direction="vertical">
              <Typography.Title level={5}>类型列表</Typography.Title>

              <DeviceTypeTree
                style={{ width: 230 }}
                treeStyle={{
                  overflowY: 'auto',
                }}
                numbered
                idcTag={idcTag ?? ''}
                blockTag={blockTag ?? ''}
                dataType={['snDevice', 'space']}
                inputProps={{
                  style: { marginBottom: 8 },
                  placeholder: '类型',
                }}
                selectedKeys={[deviceTypeInfo.deviceType]}
                defaultExpandAll
                disabledTypeList={['C0', 'C1']}
                onSelect={(value, info) => {
                  if (mode === 'edit' || monitorData.length) {
                    return;
                  }
                  setCheckDevices([]);
                  setExpandedKeys([]);
                  setCheckedKeys([]);
                  setShowDeviceTree(false);
                  if (info.node?.metaStyle === 'SPACE') {
                    switch (info.node?.key) {
                      case '90101':
                        setDeviceTypeInfo({ deviceType: info.node?.key, targetType: 'IDC' });
                        setCheckDevices([{ deviceGuid: idcTag }]);
                        break;
                      case '90102':
                        setDeviceTypeInfo({ deviceType: info.node?.key, targetType: 'BLOCK' });
                        break;
                      case '90103':
                        setDeviceTypeInfo({ deviceType: info.node?.key, targetType: 'ROOM' });
                        break;
                      case '90104':
                        setDeviceTypeInfo({ deviceType: info.node?.key, targetType: 'COLUMN' });
                        break;
                      case '90105':
                        setDeviceTypeInfo({ deviceType: info.node?.key, targetType: 'GRID' });
                        break;
                      default:
                        break;
                    }
                    return;
                  }
                  setDeviceTypeInfo({ deviceType: info.node?.key, targetType: 'DEVICE' });
                  //   setFilterValue({ pageNum: 1, pageSize: 10 });
                }}
              />
            </Space>
            <Divider type="vertical" style={{ height: '100%' }} />
            {!deviceTypeInfo?.deviceType && (
              <div
                style={{
                  alignItems: 'center',
                  width: '100%',
                  justifyContent: 'center',
                  display: 'flex',
                  height: '100%',
                }}
              >
                <Empty description="请先选择类型" image={Empty?.PRESENTED_IMAGE_SIMPLE} />
              </div>
            )}
            {deviceTypeInfo.deviceType && deviceTypeInfo.targetType === 'DEVICE' && (
              <Space style={{ width: 280, height: '100%', display: 'flex' }} direction="vertical">
                <Typography.Title level={5}>设备列表</Typography.Title>
                {showDeviceTree && (
                  <ResourceTree
                    authorizedOnly
                    treeMode={['BLOCK', 'ROOM_TYPE', 'ROOM', 'DEVICE']}
                    spaceGuid={idcTag && blockTag ? `${idcTag}.${blockTag}` : idcTag}
                    style={{ padding: 0, height: '100%', minWidth: 280, width: 280 }}
                    checkable
                    expandedKeys={expandedKeys}
                    checkedKeys={checkedKeys}
                    checkedDevices={checkDevices.map(item => ({
                      guid: item.deviceGuid,
                      spaceGuid: { roomGuid: item.roomGuid },
                    }))}
                    outerDeviceType={deviceTypeInfo.deviceType}
                    fetchDevicesParams={{
                      deviceTypeList: [deviceTypeInfo.deviceType],
                      operationStatus: 'ON',
                    }}
                    checkableNodes={['ROOM', 'DEVICE']}
                    onCheck={(value, info) => {
                      const devices: SelectDevice[] = [];
                      const _checkedKeys: string[] = [];
                      info.checkedNodes.forEach(item => {
                        if (item.type === 'ROOM') {
                          if (item.children?.length) {
                            _checkedKeys.push(item.key);
                          }
                          return;
                        }
                        _checkedKeys.push(item.key);
                        devices.push({
                          deviceType: item.device?.deviceCategory.level3,
                          deviceTag: item.device?.tag,
                          deviceGuid: item.device?.guid,
                          blockGuid: item.device?.spaceGuid.blockGuid,
                          roomGuid: item.device?.spaceGuid.roomGuid,
                          roomType: item.device?.roomType,
                        });
                      });
                      setCheckDevices(devices);
                      setCheckedKeys(_checkedKeys);
                    }}
                    // onExpand={vvv => setExpandedKeys(vvv)}
                  />
                )}
              </Space>
            )}
            {deviceTypeInfo.deviceType &&
              deviceTypeInfo.targetType !== 'DEVICE' &&
              deviceTypeInfo.targetType !== 'IDC' && (
                <Space style={{ width: 280, height: '100%', display: 'flex' }} direction="vertical">
                  <Typography.Title level={5}>空间列表</Typography.Title>
                  {showDeviceTree && (
                    <ResourceTree
                      authorizedOnly
                      onlySearchRoom
                      treeMode={getTreeMode()}
                      spaceGuid={idcTag && blockTag ? `${idcTag}.${blockTag}` : idcTag}
                      style={{ padding: 0, height: '100%', minWidth: 280, width: 280 }}
                      checkable
                      expandedKeys={expandedKeys}
                      defaultCheckedKeys={dataSource.map(item => item.deviceGuid)}
                      // checkedDevices={checkDevices.map(item => ({
                      //   guid: item.deviceGuid,
                      //   spaceGuid: { roomGuid: item.roomGuid },
                      // }))}
                      checkableNodes={getCheckableNodes()}
                      onCheck={(value, info) => {
                        const _checkedKeys: string[] = [];

                        setCheckDevices(info.checkedNodes.map(item => ({ deviceGuid: item.key })));
                        setCheckedKeys(_checkedKeys);
                      }}
                      // onExpand={vvv => setExpandedKeys(vvv)}
                    />
                  )}
                </Space>
              )}
            {deviceTypeInfo.deviceType &&
              deviceTypeInfo.targetType !== 'DEVICE' &&
              deviceTypeInfo.targetType !== 'IDC' && (
                <Divider type="vertical" style={{ height: '100%' }} />
              )}
            {checkDevices.length ? (
              <MonitorGroupTable
                idcTag={idcTag}
                blockTag={blockTag}
                targetIdList={checkDevices}
                targetType={deviceTypeInfo.targetType}
                deviceType={deviceTypeInfo.deviceType}
                dataSource={monitorData}
                setMonitorData={setMonitorData}
              />
            ) : null}
          </div>
        </Drawer>
      </>
    );
  }
);

MonitorGroupSelectDrawer.displayName = 'MonitorGroupSelectDrawer';
