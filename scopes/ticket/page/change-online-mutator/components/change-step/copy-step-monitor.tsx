import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { ChangeStepInfo } from './add-change-step';

export type CopyStepMonitorProps = {
  currentStep: string;
  stepCodes: string[];
  stepMaps: Record<string, ChangeStepInfo>;
  disabledBtn?: boolean;
  onChange: (value: string[]) => void;
};

export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const CopyStepMonitor = React.forwardRef(
  (
    { currentStep, stepCodes, stepMaps, disabledBtn, onChange }: CopyStepMonitorProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    // const [showSteps, setShowSteps] = useState(true);
    // const [visible, setVisible] = useState(false);
    const [selectStepCodes, setSelectStepCodes] = useState<string[]>([]);

    const onClose = () => {
      // setVisible(false);
      // setShowSteps(true);

      setSelectStepCodes([]);
    };
    return (
      <>
        <Popconfirm
          destroyTooltipOnHide
          overlayInnerStyle={{ maxWidth: 306 }}
          title={
            <div style={{ display: 'flex', flexDirection: 'column' }}>
              <Typography.Text>
                勾选步骤，即可复制该步骤的屏蔽对象,复制后，当前步骤已有屏蔽对象会被覆盖
              </Typography.Text>
              {/* {showSteps && ( */}
              <Checkbox.Group
                style={{
                  marginTop: 8,
                  marginLeft: -20,
                  display: 'flex',
                  flexDirection: 'column',
                  gap: 8,
                }}
                options={stepCodes
                  .map((item, index) => ({ label: `步骤${index + 1}`, value: item }))
                  .filter(
                    item =>
                      item.value !== currentStep &&
                      stepMaps[item.value]?.stepInhibitionItemInfoList?.length
                  )}
                value={selectStepCodes}
                onChange={v => setSelectStepCodes(v as string[])}
              />
              {/* )} */}
            </div>
          }
          trigger="click"
          // open={visible}
          okText="确认复制"
          placement="topRight"
          // showCancel={!showSteps}
          onCancel={onClose}
          onConfirm={() => {
            message.success('复制屏蔽对象成功');
            onChange(selectStepCodes);
            onClose();
          }}
          // onOpenChange={v => {
          //   if (showSteps && !v) {
          //     onClose();
          //   }
          // }}
        >
          <Button
            size="small"
            disabled={disabledBtn}
            // onClick={() => {
            //   setVisible(true);
            //   if (stepMaps[currentStep]?.stepInhibitionItemInfoList?.length) {
            //     setShowSteps(false);
            //   }
            // }}
          >
            复制屏蔽对象
          </Button>
        </Popconfirm>
      </>
    );
  }
);

CopyStepMonitor.displayName = 'CopyStepMonitor';
