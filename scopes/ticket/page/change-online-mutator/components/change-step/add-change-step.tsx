import { omit } from 'lodash';
import React, { useEffect, useState } from 'react';

// import shortid from 'shortid';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { generateValidLimitsDataSource } from '@manyun/monitoring.model.point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { DevicesTableSelectModal } from '@manyun/resource-hub.ui.device-select';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { PointSelect } from '@manyun/resource-hub.ui.point-select';

import type { MonitorGroupMergeData } from './monitor-group-table';

export type AddChangeStepProps = {
  idcTag: string;
  blockTag?: string | null;
  title?: string | null;
  values?: ChangeStepInfo;
  mode: 'ticketNew' | 'templateNew' | 'ticketView' | 'templateView';
  templateNewLocations?: string[];
  setStepInfo: (value: ChangeStepInfo) => void;
};

export type ChangeStepInfo = {
  stepOrder?: string | null;
  stepName?: string | null;
  operator?: string | null;
  stepStatus?: string | null;
  operatorName?: string | null;
  operatorId?: number | null;
  id?: number | null;
  stepResponsibleUserList?: { label: string; value: number }[] | null;
  opType?: string | null;
  opObjectName?: string | null;
  opObjectCode?: string | null;
  pointCode?: string | null;
  pointName?: string | null;
  pointValueText?: string | null;
  expectedValue?: string | null;
  startTime?: number | null;
  endTime?: number | null;
  stepDesc?: string | null;
  stepInhibitionItemInfoList?: MonitorGroupMergeData[];
  stepDeviceInfoList?: {
    deviceGuid: string;
    deviceName: string;
    deviceTag: string;
    deviceLabel?: string | null;
    roomTag?: string | null;
    checkPointValue?: number | null;
  }[];
};

export function AddChangeStep({
  values,
  blockTag,
  idcTag,
  title,
  mode,
  templateNewLocations,
  setStepInfo,
}: AddChangeStepProps) {
  const [visible, setVisible] = useState(false);
  const [pointList, setPointList] = useState([]);
  const [validLimits, setValidLimits] = useState<{ label: string; value: string }[]>([]);

  const [form] = Form.useForm();

  const opType = Form.useWatch('opType', form);
  const opObjectCode = Form.useWatch('opObjectCode', form);
  const point = Form.useWatch('point', form);

  useEffect(() => {
    if (visible && values && values.opObjectCode) {
      getPoint(true);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  const getPoint = async init => {
    const { data } = await fetchPointsByCondition({
      deviceType: values?.opObjectCode ?? '',
      dataTypeList: ['DI'],
      pointTypeList: ['ORI', 'CAL_DEVICE', 'AGG_DEVICE'],
      isRemoveSub: true,
    });
    if (data && Array.isArray(data.data) && data.data.length) {
      const result = data.data.map(point => point.toApiObject());
      setPointList(result);
      if (init) {
        getValidation(values?.pointCode, true, result);
      }
    }
  };

  const getValidation = async (value, init, initPointList) => {
    if (value) {
      const newPointList = init ? initPointList : pointList;
      const validLimitsItem = newPointList.filter(item => item.pointCode === value);
      const list = validLimitsItem.length
        ? generateValidLimitsDataSource(validLimitsItem[0].validLimits)
        : [];

      setValidLimits(list);
    }

    if (!init) {
      form.setFieldsValue({ expected: undefined });
    }
  };

  const onClose = () => {
    setVisible(false);
    form.resetFields();
  };

  const disabledDevice = mode === 'templateNew' ? (templateNewLocations?.length ?? 0) > 1 : false;
  return (
    <>
      <Button
        disabled={mode === 'templateNew' ? !templateNewLocations?.length : false}
        compact={!!values?.stepName}
        type={values?.stepName ? 'link' : 'primary'}
        onClick={e => {
          e?.stopPropagation();
          setVisible(true);
          values &&
            form.setFieldsValue({
              ...values,
              point: { label: values.pointName, value: values.pointCode },
              expectedValue: { label: values.pointValueText, value: values.expectedValue },
            });
        }}
      >
        {values?.stepName ? '编辑' : '添加步骤'}
      </Button>
      <Drawer
        width={716}
        // style={{ zIndex: 1100 }}
        title={title}
        placement="right"
        open={visible}
        destroyOnClose
        extra={
          <Space>
            <Button
              onClick={() => {
                onClose();
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                form.validateFields().then(_values => {
                  const info = { ...omit(_values, 'point', 'expectedValue') };
                  // const id = shortid();
                  setStepInfo({
                    ...values,
                    ...info,
                    pointCode: _values?.point?.value,
                    pointName: _values?.point?.label,
                    pointValueText: _values?.expectedValue?.label,
                    expectedValue: _values?.expectedValue?.value,
                    // id,
                  });
                  onClose();
                });
              }}
            >
              确定
            </Button>
          </Space>
        }
        onClose={() => {
          onClose();
        }}
      >
        <Form
          form={form}
          labelCol={{ flex: ' 0 0 110px' }}
          initialValues={{
            opType: 'others',
          }}
        >
          <Form.Item
            label="步骤内容"
            name="stepName"
            rules={[
              {
                required: true,
                message: '步骤内容必填！',
                whitespace: true,
              },
              {
                max: 300,
                message: '最多可输入300 个字符！',
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          <Form.Item
            label="完成标准"
            name="operator"
            rules={[
              {
                required: true,
                message: '完成标准必填！',
                whitespace: true,
              },
              {
                max: 300,
                message: '最多输入300 个字符！',
              },
            ]}
          >
            <Input.TextArea />
          </Form.Item>
          {mode !== 'templateNew' && mode !== 'templateView' && (
            <Form.Item
              label="负责人"
              name="stepResponsibleUserList"
              rules={[
                {
                  required: true,
                  message: '负责人必选！',
                },
              ]}
            >
              <UserSelect userState="in-service" mode="multiple" />
            </Form.Item>
          )}
          <Form.Item label="步骤对象类型" name="opType">
            <Radio.Group
              onChange={() => {
                form.setFieldValue('stepDeviceInfoList', []);
                form.setFieldValue('point', undefined);
                form.setFieldValue('expectedValue', undefined);
                form.setFieldValue('opObjectCode', undefined);
              }}
            >
              <Radio value="device">设备</Radio>
              <Radio value="others">其他</Radio>
            </Radio.Group>
          </Form.Item>
          {opType === 'device' && (
            <Form.Item
              label="设备类型"
              name="opObjectCode"
              rules={[
                {
                  required: true,
                  message: '设备类型必选！',
                },
              ]}
            >
              <DeviceTypeCascader
                showSearch={false}
                style={{ width: 216 }}
                numbered
                treeDefaultExpandAll
                dataType={['snDevice']}
                disabledTypeList={['C0', 'C1']}
                onChange={() => {
                  form.setFieldValue('stepDeviceInfoList', []);
                  form.setFieldValue('point', undefined);
                  form.setFieldValue('expectedValue', undefined);
                }}
              />
            </Form.Item>
          )}
          {opType === 'others' && (
            <Form.Item
              label="步骤对象"
              name="opObjectName"
              rules={[
                {
                  max: 300,
                  message: '最多输入300 个字符！',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
          )}
          {opType === 'device' && (
            <Form.Item
              label="设备编号"
              name="stepDeviceInfoList"
              rules={[
                {
                  required: mode !== 'templateNew',
                  message: '设备编号必选！',
                },
              ]}
              extra={
                disabledDevice
                  ? '当前模板适用多个位置，模板中仅支持选择到设备/空间类型，具体的设备/空间请在变更单中选择'
                  : ''
              }
            >
              <DevicesTableSelectModal
                style={{ width: 88 }}
                idcTag={idcTag}
                blockTag={blockTag}
                // showDescription={false}
                disabled={!idcTag || disabledDevice || !opObjectCode}
                matchProductModel
                deviceType={opObjectCode}
              />
            </Form.Item>
          )}
          {opType === 'device' && opObjectCode && (
            <Form.Item label="验证测点" name="point">
              <PointSelect
                style={{ width: 216 }}
                showSearch
                treeNodeFilterProp="title"
                labelInValue
                dataTypeList={['DI']}
                pointTypeList={['ORI', 'CAL_DEVICE', 'AGG_DEVICE']}
                deviceType={opObjectCode}
                allowClear
                onChange={(_, __, ___, selectedPointEntities) => {
                  const list = selectedPointEntities?.validLimits
                    ? generateValidLimitsDataSource(selectedPointEntities.validLimits)
                    : [];
                  form.setFieldValue('expectedValue', undefined);
                  setValidLimits(list);
                }}
              />
            </Form.Item>
          )}
          {opType === 'device' && opObjectCode && (
            <Form.Item
              label="验证状态"
              name="expectedValue"
              rules={[
                ({ getFieldValue }) => ({
                  validator(_, value) {
                    if (getFieldValue('point') && !value) {
                      return Promise.reject('验证状态必选！');
                    }

                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <Select style={{ width: 216 }} labelInValue allowClear disabled={!point}>
                {validLimits.map(item => (
                  <Select.Option key={item.value}>{item.label}</Select.Option>
                ))}
              </Select>
            </Form.Item>
          )}
        </Form>
      </Drawer>
    </>
  );
}
