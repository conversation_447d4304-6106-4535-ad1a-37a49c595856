import { findIndex, uniqBy } from 'lodash';
import omit from 'lodash.omit';
import uniqWith from 'lodash/uniqWith';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import shortid from 'shortid';

import { Anchor } from '@manyun/base-ui.ui.anchor';
import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import styled, { css } from '@manyun/dc-brain.theme.theme';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import type { ChangeDeviceList } from '@manyun/ticket.gql.client.tickets';
import type { CreateChange } from '@manyun/ticket.service.create-change';
import type { CriticalStepForm } from '@manyun/ticket.ui.change-offline-critical-step-table';

import type { ChangeStepInfo } from './add-change-step';
import { AddChangeStep } from './add-change-step';
import { ChangeStepMonitorTable } from './change-step-monitor-table';
import { CopyStepMonitor } from './copy-step-monitor';
import { DeviceTypeSelectDrawer } from './device-type-select-drawer';
import { ExecuteStepButton } from './execute-step-button';
import { ImportStepModal } from './import-step-modal';
import { MonitorGroupSelectDrawer } from './monitor-group-select-drawer';
import { monitorGroupDataLoopUtil, monitorGroupDataMergeUtil } from './monitor-group-table';
import type { MonitorGroupMergeData, SelectedMonitorGroup } from './monitor-group-table';
import { PointValuePopover } from './point-value-popover';
import { StopStepButton } from './stop-step-button';

export const StepWarper = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-steps-item-finish
        > .${prefixCls}-steps-item-container
        > .${prefixCls}-steps-item-content
        > .${prefixCls}-steps-item-title {
        width: 100%;
      }
    `;
  }}
`;

export type ChangeOfflineFormValues = Omit<
  CreateChange,
  | 'planStartTime'
  | 'planEndTime'
  | 'customerFileInfoList'
  | 'blockTag'
  | 'fileInfoList'
  | 'fileList'
  | 'stepList'
  | 'changeTimeList'
  | 'changeSourceInfoList'
  | 'changeDeviceList'
> & {
  planEndTime: Moment;
  planStartTime: Moment;
  customerFileInfoList: McUploadFile[] | undefined;
  fileInfoList: McUploadFile[];
  blockGuid: string;
  changeDeviceList: ChangeDeviceList[];
  changeSourceInfoList: { sourceType: string; sourceId: string; title: string }[];
  stepList: CriticalStepForm[];
};

export type ChangeOfflineFormProps = {
  form?: FormInstance<ChangeOfflineFormValues>;
  idcTag?: string;
  blockTag?: string;
  stepCodes: string[];

  stepMaps: Record<string, ChangeStepInfo>;
  mode: 'ticketNew' | 'templateNew' | 'ticketView' | 'templateView';
  templateNewLocations?: string[];
  showOperation?: boolean;
  changeOrderId?: string;
  isOwnerUser?: boolean;
  activeKeyAll?: boolean;
  canEdit?: boolean; // 编辑状态是否可操作步骤
  setStepInfo?: (stepMaps: Record<string, ChangeStepInfo>, stepCodes: string[]) => void;
  refetch?: () => void;
  onChange: (value: string[]) => void;
};

export function ChangeStep({
  form,
  idcTag,
  blockTag,
  stepMaps,
  stepCodes,
  mode,
  templateNewLocations,
  changeOrderId,
  showOperation,
  isOwnerUser,
  activeKeyAll = true,
  canEdit = true,
  setStepInfo,
  refetch,
  onChange,
}: ChangeOfflineFormProps) {
  const [, { checkUserId }] = useAuthorized();
  const [activeKey, setActiveKey] = useState<string[]>([]);

  useEffect(() => {
    if (activeKeyAll) {
      setActiveKey(stepCodes);
    } else {
      setActiveKey([]);
    }
  }, [stepCodes, activeKeyAll]);

  const changeStepInfo = (stepInfo: ChangeStepInfo) => {
    const id = shortid();
    onChange([...stepCodes, id]);
    setStepInfo && setStepInfo({ ...stepMaps, [id]: stepInfo }, [...stepCodes, id]);
  };

  const editStepInfo = (stepInfo: ChangeStepInfo, id: string) => {
    onChange(stepCodes);
    setStepInfo && setStepInfo({ ...stepMaps, [id]: stepInfo }, stepCodes);
  };

  const deleteStepInfo = (id: string) => {
    const idx = stepCodes.indexOf(id);
    const codes = stepCodes.filter(item => id !== item);
    const _stepMaps = { ...stepMaps };
    stepCodes.forEach((item, index) => {
      if (index >= idx) {
        _stepMaps[item] = {
          ..._stepMaps[item],
          stepInhibitionItemInfoList: _stepMaps[item].stepInhibitionItemInfoList?.map(device => {
            form?.setFieldValue(
              `${device.deviceType}-${device.id}-${item}-maxInfluencesStep`,
              device.maxInfluencesStep === '99999' ? device.maxInfluencesStep : undefined
            );

            return {
              ...device,
              deviceList: device?.deviceList?.map(i => ({
                ...i,
                maxInfluencesStep:
                  i?.maxInfluencesStep === '99999' ? i?.maxInfluencesStep : undefined,
              })),
              maxInfluencesStep:
                device.maxInfluencesStep === '99999' ? device.maxInfluencesStep : undefined,
            };
          }),
        };
      }
      _stepMaps[item] = {
        ..._stepMaps[item],
        stepInhibitionItemInfoList: _stepMaps[item].stepInhibitionItemInfoList?.map(device => {
          if (device.maxInfluencesStep === id) {
            form?.setFieldValue(
              `${device.deviceType}-${device.id}-${item}-maxInfluencesStep`,
              undefined
            );
          }
          return {
            ...device,
            deviceList: device?.deviceList?.map(i => ({
              ...i,
              maxInfluencesStep: i.maxInfluencesStep === id ? undefined : i?.maxInfluencesStep,
            })),
            maxInfluencesStep:
              device.maxInfluencesStep === id ? undefined : device?.maxInfluencesStep,
          };
        }),
      };
    });

    const maps = omit(_stepMaps, id);
    onChange(codes);
    setStepInfo && setStepInfo(maps, codes);
  };
  const canOperate = (stepOwners: { value: number; label: string }[]) => {
    return isOwnerUser || stepOwners?.some(item => checkUserId(item.value));
  };
  const executeDisabeled = (index: number) => {
    const preList = [...stepCodes].splice(0, index);
    return preList.some(item => stepMaps[item]?.stepStatus === 'NOT_START');
  };
  const currentProcessingStep = findIndex(
    stepCodes,
    item => stepMaps[item]?.stepStatus === 'PROCESSING'
  );

  const changeStepMonitorSetStep = (monitorGroup, stepCode, type) => {
    if (mode === 'templateNew' && (templateNewLocations?.length ?? 0) > 1) {
      const maps = { ...stepMaps };
      const stepInhibitionItemInfoList =
        type === 'delete'
          ? monitorGroup
          : [...monitorGroup, ...(maps[stepCode].stepInhibitionItemInfoList ?? [])];
      maps[stepCode] = {
        ...maps[stepCode],

        stepInhibitionItemInfoList: uniqBy(
          stepInhibitionItemInfoList,
          'deviceType'
        ) as MonitorGroupMergeData[],
      };
      onChange(stepCodes);
      setStepInfo && setStepInfo(maps, stepCodes);
      return;
    }

    const maps = {
      ...stepMaps,
      [stepCode]: {
        ...stepMaps[stepCode],
        stepInhibitionItemInfoList: monitorGroup,
      },
    };
    onChange(stepCodes);
    setStepInfo && setStepInfo(maps, stepCodes);
  };

  const disabledBtn = () => {
    return stepCodes.every(item => (stepMaps[item]?.stepInhibitionItemInfoList?.length ?? 0) === 0);
  };

  return (
    <Form
      style={{ width: '100%' }}
      colon={false}
      form={form}
      labelCol={{ flex: ' 0 0 110px' }}
      wrapperCol={{ span: 24 }}
    >
      {['ticketNew', 'templateNew'].includes(mode) && canEdit && (
        <Space>
          <AddChangeStep
            idcTag={idcTag}
            mode={mode}
            title={`添加步骤：步骤${stepCodes?.length + 1}`}
            templateNewLocations={templateNewLocations}
            blockTag={blockTag}
            setStepInfo={changeStepInfo}
          />
          <ImportStepModal
            disabled={mode === 'templateNew' ? !templateNewLocations?.length : false}
            showRemind={!!stepCodes.length}
            onSuccess={value => {
              const stepMaps: Record<string, ChangeStepInfo> = {};
              const stepCodes = value.map(item => {
                const id = shortid();
                stepMaps[id] = {
                  ...item,
                };
                return id;
              });
              onChange(stepCodes);
              setStepInfo && setStepInfo(stepMaps, stepCodes);
            }}
          />
        </Space>
      )}
      {['ticketNew', 'templateNew'].includes(mode) && !stepCodes.length && (
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="请至少添加一个步骤" />
      )}
      <div
        // id="changeOnlineStepWrapper"
        style={{
          width: '100%',
          display: 'flex',
          flexDirection: 'row',
          marginTop: mode === 'templateView' ? -6 : 24,
          //   position: 'relative',
        }}
      >
        <div style={{ flex: 1, marginRight: 24 }}>
          <StepWarper>
            <Steps
              progressDot
              current={99999}
              direction="vertical"
              items={stepCodes.map((item, index) => ({
                title: (
                  <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
                    <Typography.Text id={item} strong style={{ fontSize: 16 }}>
                      步骤{index + 1}{' '}
                    </Typography.Text>
                    {stepMaps[item]?.stepStatus && stepMaps[item]?.stepStatus !== 'NOT_START' && (
                      <div style={{ display: 'flex', flexDirection: 'row', gap: 16 }}>
                        {stepMaps[item]?.stepStatus !== 'NOT_START' &&
                          stepMaps[item]?.stepStatus !== 'PROCESSING' && (
                            <Typography.Text style={{ fontSize: 14 }}>
                              {' '}
                              <Typography.Text type="secondary" style={{ fontSize: 14 }}>
                                执行人：
                              </Typography.Text>
                              {stepMaps[item]?.operatorName}
                            </Typography.Text>
                          )}
                        <Typography.Text style={{ fontSize: 14 }}>
                          <Typography.Text type="secondary" style={{ fontSize: 14 }}>
                            {' '}
                            执行时间：
                          </Typography.Text>
                          {moment(stepMaps[item]?.startTime).format('YYYY-MM-DD HH:mm')} ~{' '}
                          {stepMaps[item]?.endTime
                            ? moment(stepMaps[item]?.endTime).format('YYYY-MM-DD HH:mm')
                            : '--'}
                        </Typography.Text>
                        {stepMaps[item]?.stepStatus !== 'NOT_START' &&
                          stepMaps[item]?.stepStatus !== 'PROCESSING' && (
                            <Typography.Text>
                              <Typography.Text type="secondary" style={{ fontSize: 14 }}>
                                步骤说明：
                              </Typography.Text>
                              {stepMaps[item]?.stepDesc ? (
                                <Popover
                                  content={stepMaps[item]?.stepDesc}
                                  title="步骤说明"
                                  overlayInnerStyle={{ minWidth: 256, maxWidth: 520 }}
                                >
                                  <Button type="link" compact style={{ fontSize: 14 }}>
                                    查看
                                  </Button>
                                </Popover>
                              ) : (
                                '--'
                              )}
                            </Typography.Text>
                          )}
                      </div>
                    )}
                  </div>
                ),
                description: (
                  <Collapse
                    activeKey={activeKey}
                    collapsible="icon"
                    onChange={value => {
                      setActiveKey(value);
                    }}
                  >
                    <Collapse.Panel
                      key={item}
                      header={
                        <div
                          style={{
                            display: 'flex',
                            gap: 16,
                            flexDirection: 'row',
                            justifyContent: 'space-between',
                          }}
                        >
                          <div style={{ display: 'flex', gap: 4, flexDirection: 'column' }}>
                            <Typography.Text style={{ fontSize: 16, fontWeight: 500 }}>
                              {stepMaps[item]?.stepName}
                            </Typography.Text>
                            <Typography.Text>完成标准：{stepMaps[item]?.operator}</Typography.Text>
                          </div>
                          <div
                            style={{
                              display: 'flex',
                              gap: 12,
                              flexDirection: 'row',
                              alignItems: 'center',
                            }}
                          >
                            {mode !== 'templateNew' && mode !== 'templateView' && (
                              <>
                                <Typography.Text
                                  type={
                                    stepMaps[item]?.stepResponsibleUserList?.length
                                      ? 'secondary'
                                      : 'danger'
                                  }
                                  style={{ width: 46 }}
                                >
                                  负责人:
                                </Typography.Text>
                                <Typography.Text
                                  type={
                                    stepMaps[item]?.stepResponsibleUserList?.length
                                      ? undefined
                                      : 'danger'
                                  }
                                  style={{ marginRight: 12, width: 88 }}
                                  ellipsis={{
                                    tooltip: stepMaps[item]?.stepResponsibleUserList
                                      ?.map(item => item.label)
                                      .join(' | '),
                                  }}
                                >
                                  {stepMaps[item]?.stepResponsibleUserList?.length
                                    ? stepMaps[item]?.stepResponsibleUserList
                                        ?.map(item => item.label)
                                        .join(' | ')
                                    : '--'}
                                </Typography.Text>
                              </>
                            )}
                            {/* 工单详情执行 */}
                            {showOperation && (
                              <>
                                {stepMaps[item]?.stepStatus === 'NORMAL' && (
                                  <Tag color="success">正常</Tag>
                                )}
                                {stepMaps[item]?.stepStatus === 'EXCEPTION' && (
                                  <Tag color="error">异常</Tag>
                                )}
                                {stepMaps[item]?.stepStatus === 'NOT_START' &&
                                  canOperate(stepMaps[item].stepResponsibleUserList) && (
                                    <ExecuteStepButton
                                      disabled={executeDisabeled(index)}
                                      stepCodes={stepCodes}
                                      stepMaps={stepMaps}
                                      currentStep={item}
                                      stepId={stepMaps[item].id!}
                                      onSuccess={() => {
                                        refetch && refetch();
                                      }}
                                    />
                                  )}
                                {stepMaps[item]?.stepStatus === 'PROCESSING' &&
                                  canOperate(stepMaps[item].stepResponsibleUserList) && (
                                    <StopStepButton
                                      startTime={stepMaps[item]?.startTime!}
                                      changeOrderId={changeOrderId!}
                                      stepId={stepMaps[item].id!}
                                      onSuccess={() => {
                                        refetch && refetch();
                                      }}
                                    />
                                  )}
                              </>
                            )}
                            {(mode === 'templateNew' || mode === 'ticketNew') && canEdit && (
                              <>
                                <AddChangeStep
                                  idcTag={idcTag}
                                  blockTag={blockTag}
                                  values={stepMaps[item]}
                                  mode={mode}
                                  title={`编辑步骤：步骤${index + 1}`}
                                  templateNewLocations={templateNewLocations}
                                  setStepInfo={infos => editStepInfo(infos, item)}
                                />
                                <Popconfirm
                                  title="确认删除该步骤？操作后步骤及屏蔽对象均会被删除，请谨慎操作！"
                                  style={{ width: 290 }}
                                  okText="确认删除"
                                  cancelText="取消"
                                  onCancel={e => e?.stopPropagation()}
                                  onConfirm={async e => {
                                    deleteStepInfo(item);
                                    e?.stopPropagation();
                                    // const { error } = await updateRiskRegisterMeasureStatus({
                                    //   riskId: measure.riskId,
                                    //   measureId: measure.measureId,
                                    //   measureStatus: 'INVALID',
                                    // });
                                    // setLoading(false);
                                    // if (error) {
                                    //   message.error(error.message);
                                    //   return;
                                    // }
                                    // onRefetchMeasure && onRefetchMeasure();
                                  }}
                                >
                                  <Button
                                    compact
                                    type="link"
                                    onClick={event => event.stopPropagation()}
                                  >
                                    删除
                                  </Button>
                                </Popconfirm>
                              </>
                            )}
                          </div>
                        </div>
                      }
                    >
                      <Descriptions column={4} contentStyle={{ overflow: 'hidden' }}>
                        {stepMaps[item]?.opType === 'device' && (
                          <Descriptions.Item label="设备类型">
                            <DeviceTypeText code={stepMaps[item].opObjectCode ?? ''} />
                          </Descriptions.Item>
                        )}

                        {stepMaps[item]?.opType === 'device' && (
                          <Descriptions.Item
                            label={
                              <Typography.Text
                                type={
                                  mode === 'templateNew' || mode === 'templateView'
                                    ? undefined
                                    : stepMaps[item]?.stepDeviceInfoList?.length
                                      ? undefined
                                      : 'danger'
                                }
                              >
                                设备编号
                              </Typography.Text>
                            }
                            contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
                          >
                            {stepMaps[item]?.stepDeviceInfoList?.length ? (
                              <Popover
                                content={
                                  <Space
                                    size={0}
                                    wrap
                                    split={<Divider type="vertical" spaceSize="mini" emphasis />}
                                  >
                                    {stepMaps[item]?.stepDeviceInfoList?.map(device => (
                                      <Link
                                        key={device.deviceGuid}
                                        target="_blank"
                                        to={generateDeviceRecordRoutePath({
                                          guid: device.deviceGuid,
                                        })}
                                      >
                                        {device.deviceName}({device.roomTag})
                                      </Link>
                                    ))}
                                  </Space>
                                }
                              >
                                <Typography.Text ellipsis>
                                  {stepMaps[item]?.stepDeviceInfoList.map((device, index) => (
                                    <>
                                      <Link
                                        key={device.deviceGuid}
                                        target="_blank"
                                        to={generateDeviceRecordRoutePath({
                                          guid: device.deviceGuid,
                                        })}
                                      >
                                        {device.deviceName}({device.roomTag})
                                      </Link>
                                      {index !== stepMaps[item]?.stepDeviceInfoList?.length - 1 && (
                                        <Divider type="vertical" />
                                      )}
                                    </>
                                  ))}
                                </Typography.Text>
                              </Popover>
                            ) : (
                              <Typography.Text
                                type={
                                  mode === 'templateNew' || mode === 'templateView'
                                    ? undefined
                                    : stepMaps[item]?.stepDeviceInfoList?.length
                                      ? undefined
                                      : 'danger'
                                }
                                style={{ marginRight: 12 }}
                              >
                                --
                              </Typography.Text>
                            )}
                          </Descriptions.Item>
                        )}
                        {stepMaps[item]?.opType === 'device' && stepMaps[item]?.pointName && (
                          <Descriptions.Item label="验证测点">
                            {stepMaps[item]?.pointName ?? '--'}
                          </Descriptions.Item>
                        )}
                        {stepMaps[item]?.opType === 'device' && stepMaps[item]?.pointValueText && (
                          <Descriptions.Item label="验证状态">
                            {stepMaps[item]?.pointValueText ?? '--'}
                          </Descriptions.Item>
                        )}
                        {stepMaps[item]?.pointCode &&
                          stepMaps[item]?.opObjectCode &&
                          stepMaps[item]?.stepStatus === 'PROCESSING' && (
                            <Descriptions.Item label="测点值">-- </Descriptions.Item>
                          )}
                        {stepMaps[item]?.pointCode &&
                          stepMaps[item]?.opObjectCode &&
                          stepMaps[item]?.stepStatus === 'PROCESSING' && (
                            <Descriptions.Item label="状态">-- </Descriptions.Item>
                          )}
                        {stepMaps[item]?.pointCode &&
                          stepMaps[item]?.opObjectCode &&
                          (stepMaps[item]?.stepStatus === 'EXCEPTION' ||
                            stepMaps[item]?.stepStatus === 'NORMAL') && (
                            <Descriptions.Item label="测点值">
                              <PointValuePopover
                                deviceType={stepMaps[item]?.opObjectCode}
                                pointCode={stepMaps[item]?.pointCode}
                                deviceList={stepMaps[item]?.stepDeviceInfoList ?? []}
                              />
                            </Descriptions.Item>
                          )}
                        {stepMaps[item]?.pointCode &&
                          stepMaps[item]?.opObjectCode &&
                          (stepMaps[item]?.stepStatus === 'EXCEPTION' ||
                            stepMaps[item]?.stepStatus === 'NORMAL') && (
                            <Descriptions.Item label="状态">
                              {(stepMaps[item]?.stepDeviceInfoList ?? []).every(
                                step =>
                                  step?.checkPointValue?.toString() ===
                                  stepMaps[item]?.expectedValue?.toString()
                              )
                                ? '正常'
                                : '异常'}
                            </Descriptions.Item>
                          )}
                        {stepMaps[item]?.opType === 'others' && stepMaps[item]?.opObjectName && (
                          <Descriptions.Item
                            label="步骤对象"
                            contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
                          >
                            <Typography.Text ellipsis={{ tooltip: stepMaps[item]?.opObjectName }}>
                              {stepMaps[item]?.opObjectName}
                            </Typography.Text>
                          </Descriptions.Item>
                        )}
                      </Descriptions>
                      {['ticketNew', 'templateNew'].includes(mode) ? (
                        <Space direction="horizontal" style={{ marginBottom: 16 }}>
                          {mode === 'templateNew' && (templateNewLocations?.length ?? 0) > 1 ? (
                            <DeviceTypeSelectDrawer
                              dataSource={stepMaps[item]?.stepInhibitionItemInfoList}
                              onChange={monitorGroup => {
                                const maps = { ...stepMaps };

                                const stepInhibitionItemInfoList = [
                                  ...(maps[item].stepInhibitionItemInfoList ?? []),
                                  ...monitorGroup,
                                ];
                                maps[item] = {
                                  ...maps[item],
                                  stepInhibitionItemInfoList: uniqBy(
                                    stepInhibitionItemInfoList,
                                    'deviceType'
                                  ) as MonitorGroupMergeData[],
                                };
                                onChange(stepCodes);
                                setStepInfo(maps, stepCodes);
                              }}
                            />
                          ) : canEdit ? (
                            <MonitorGroupSelectDrawer
                              idcTag={idcTag}
                              blockTag={blockTag}
                              ticketMode={mode}
                              onChange={(monitorGroup, type) => {
                                if (type === 'deviceType') {
                                  const maps = { ...stepMaps };

                                  const stepInhibitionItemInfoList = [
                                    ...(maps[item].stepInhibitionItemInfoList ?? []),
                                    { deviceType: monitorGroup },
                                  ];
                                  maps[item] = {
                                    ...maps[item],
                                    stepInhibitionItemInfoList: uniqWith(
                                      stepInhibitionItemInfoList,
                                      (a, b) => {
                                        return (
                                          !b?.inhibitionModelId &&
                                          !a?.inhibitionModelId &&
                                          a.deviceType === b.deviceType
                                        );
                                      }
                                    ) as MonitorGroupMergeData[],
                                  };
                                  onChange(stepCodes);
                                  setStepInfo(maps, stepCodes);
                                  return;
                                }
                                const maps = { ...stepMaps };
                                const loopMonitorGroup = monitorGroupDataLoopUtil(
                                  maps[item]?.stepInhibitionItemInfoList ?? []
                                );
                                const stepInhibitionItemInfoList = monitorGroupDataMergeUtil(
                                  [
                                    ...loopMonitorGroup,
                                    ...(monitorGroup as SelectedMonitorGroup[]),
                                  ],
                                  mode
                                );
                                maps[item] = {
                                  ...maps[item],
                                  stepInhibitionItemInfoList: stepInhibitionItemInfoList,
                                };
                                onChange(stepCodes);

                                setStepInfo && setStepInfo(maps, stepCodes);
                              }}
                            />
                          ) : null}
                          {canEdit ? (
                            <CopyStepMonitor
                              currentStep={item}
                              stepCodes={stepCodes}
                              stepMaps={stepMaps}
                              disabledBtn={disabledBtn()}
                              onChange={codes => {
                                const maps = { ...stepMaps };
                                const list: MonitorGroupMergeData[] = [];
                                codes.forEach(code => {
                                  let _stepInhibitionItemInfoList = [
                                    ...stepMaps[code].stepInhibitionItemInfoList!,
                                  ];
                                  const copyIdx = stepCodes.indexOf(
                                    _stepInhibitionItemInfoList[0].maxInfluencesStep
                                  );
                                  _stepInhibitionItemInfoList =
                                    copyIdx < index
                                      ? _stepInhibitionItemInfoList.map(item => ({
                                          ...item,
                                          maxInfluencesStep: undefined,
                                          deviceList: item.deviceList?.map(deviceItem => ({
                                            ...deviceItem,
                                            maxInfluencesStep: undefined,
                                          })),
                                        }))
                                      : _stepInhibitionItemInfoList;
                                  list.push(..._stepInhibitionItemInfoList!);
                                });
                                maps[item] = { ...maps[item], stepInhibitionItemInfoList: list };
                                onChange(stepCodes);
                                setStepInfo(maps, stepCodes);
                              }}
                            />
                          ) : null}
                        </Space>
                      ) : null}
                      {stepMaps[item]?.stepInhibitionItemInfoList?.length ? (
                        <ChangeStepMonitorTable
                          dataSource={stepMaps[item]?.stepInhibitionItemInfoList}
                          mode={
                            (mode === 'templateNew' || mode === 'ticketNew') && canEdit
                              ? 'editAndDelete'
                              : 'view'
                          }
                          templateMoreLocations={
                            mode === 'templateNew' && (templateNewLocations?.length ?? 0) > 1
                          }
                          ticketMode={mode}
                          idcTag={idcTag}
                          blockTag={blockTag}
                          // form={form}
                          currentStep={item}
                          needCheck={mode === 'ticketNew'}
                          stepCodes={stepCodes}
                          stepMaps={stepMaps}
                          onChange={(monitorGroup, _stepMaps, type) => {
                            changeStepMonitorSetStep(monitorGroup, item, type);
                          }}
                        />
                      ) : null}
                    </Collapse.Panel>
                  </Collapse>
                ),
              }))}
            />
          </StepWarper>
        </div>
        <div style={{ width: 108 }}>
          <Anchor offsetTop={98}>
            {stepCodes.map((item, index) => (
              <Anchor.Link
                key={item}
                href={`#${item}`}
                title={
                  <>
                    步骤{index + 1}{' '}
                    {stepMaps[item]?.stepStatus === 'EXCEPTION' && (
                      <Tag color="error" style={{ fontSize: 10, borderWidth: 0 }}>
                        异常
                      </Tag>
                    )}
                    {currentProcessingStep === index && (
                      <Tag color="processing" style={{ fontSize: 10, borderWidth: 0 }}>
                        当前
                      </Tag>
                    )}
                  </>
                }
              />
            ))}
          </Anchor>
        </div>
      </div>
    </Form>
  );
}
