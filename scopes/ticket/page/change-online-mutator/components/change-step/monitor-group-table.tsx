import sortBy from 'lodash.sortby';
import uniqWith from 'lodash/uniqWith';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';
import { useLazyMonitorGroupsByInstanceObject } from 'scopes/ticket/gql/client/tickets';

import { Input } from '@manyun/base-ui.ui.input';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateAlarmConfigurationTemplateDetailUrl } from '@manyun/monitoring.route.monitoring-routes';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

// import { DeviceTable } from './device-table';

export type SelectDevice = {
  deviceType?: string | null;
  deviceTag?: string | null;
  deviceGuid?: string | null;
  blockGuid?: string | null;
  roomGuid?: string | null;
  roomType?: string | null;
};

export type MonitorGroupTableProps = {
  idcTag: string;
  blockTag?: string | null;
  targetIdList?: SelectDevice[];
  targetType?: string | null;
  deviceType: string;
  dataSource: SelectedMonitorGroup[];
  setMonitorData: React.Dispatch<React.SetStateAction<SelectedMonitorGroup[]>>;
};

export type SelectedMonitorGroup = {
  inhibitionModelId?: number;
  inhibitionModelName?: string;
  deviceType?: string | null;
  deviceTag?: string | null; //设备名称
  deviceGuid?: string | null; //
  blockGuid?: string | null;
  roomGuid?: string | null;
  roomType?: string | null;
  deviceName?: string | null; // 设备编号
  targetId?: string | null;
  id: string;
  targetType?: string | null;
  maxInfluencesStep?: string;
  inhibitionItemIdList?: string[];
  deviceLabel?: string | null;
  /**
   * 机柜
   */
  gridTag?: string | null;
  /**
   * 机列
   */
  columnTag?: string | null;
  /**
   * 机房
   */
  idcTag?: string;
  /**
   * 楼栋
   */
  blockTag?: string | null;
  /**
   * 包间编号
   */
  roomTag?: string | null;
  /**
   * 包间名称
   */
  roomName?: string | null;
  /**
   * 测点数量
   */
  pointNum?: number | null;
};

export type MonitorGroupMergeData = {
  pointNum?: number | null;
  targetType?: string | null;
  inhibitionModelId?: number;
  inhibitionModelName?: string;
  deviceType?: string | null;
  blockTag?: string | null;
  idcTag: string;
  id: string;
  maxInfluencesStep?: string;
  deviceList?: MonitorGroupMergeDataDeviceList[];
};

export type MonitorGroupMergeDataDeviceList = {
  id: string;
  targetType: string;
  inhibitionModelId: number;
  inhibitionModelName: string;
  roomName?: string | null;
  roomTag?: string | null;
  blockTag?: string | null;
  idcTag: string;
  columnTag?: string | null;
  gridTag?: string | null;
  deviceTag?: string | null; //设备名称
  deviceGuid?: string | null; //
  blockGuid?: string | null;
  roomGuid?: string | null;
  roomType?: string | null;
  deviceName?: string | null; // 设备编号
  targetId?: string | null;
  pointNum?: number | null;
  maxInfluencesStep?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const MonitorGroupTable = React.forwardRef(
  (
    {
      idcTag,
      blockTag,
      dataSource,
      deviceType,
      targetIdList,
      targetType,
      // roomTags,
      setMonitorData,
    }: MonitorGroupTableProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    // const [deviceData, setDeviceData] = useState<  SelectedMonitorGroup[]>([]);
    const [filterValue, setFilterValue] = useState<{
      name?: string;
      deviceLabel?: string;
      monitorName?: string;
      room?: string;
    }>({ name: '', deviceLabel: '', monitorName: '', room: '' });

    const [getMonitorGroupsByInstanceObjects, { data, loading }] =
      useLazyMonitorGroupsByInstanceObject();

    useEffect(() => {
      if (deviceType && targetIdList?.length && targetType && idcTag) {
        getMonitorGroupsByInstanceObjects({
          variables: {
            query: {
              idcTag,
              deviceType,
              targetIdList: targetIdList.map(item => item.deviceGuid!),
              targetType,
              blockTag,
            },
          },
        });
      }
    }, [deviceType, targetIdList, targetType, idcTag, blockTag, getMonitorGroupsByInstanceObjects]);

    // useEffect(() => {
    //   setSelectDevices(dataSource);
    // }, [dataSource]);

    const monitorDatas = data?.monitorGroupsByInstanceObject.data ?? [];
    const monitorList: SelectedMonitorGroup[] = monitorDatas.filter(item => {
      let isReturn: boolean = true;
      if (filterValue.name) {
        isReturn = !!item.deviceName?.includes(filterValue.name);
      }
      if (filterValue.deviceLabel) {
        isReturn = isReturn && !!item.deviceLabel?.includes(filterValue.deviceLabel);
      }
      if (filterValue.monitorName) {
        isReturn = isReturn && !!item.name?.includes(filterValue.monitorName);
      }
      if (filterValue.room) {
        isReturn =
          isReturn &&
          !!getSpaceGuidMap(`${item.idcTag}.${item.blockTag}.${item.roomTag}`)?.room?.includes(
            filterValue.room
          );
      }
      return isReturn;
    });

    const getColumns = () => {
      if (targetType === 'IDC') {
        return [
          {
            title: '策略名称',
            dataIndex: 'inhibitionModelName',
            render: (inhibitionModelName, { inhibitionModelId }) => {
              return (
                <Link
                  type="link"
                  target="_blank"
                  to={generateAlarmConfigurationTemplateDetailUrl({
                    id: inhibitionModelId.toString(),
                  })}
                >
                  {inhibitionModelName}
                </Link>
              );
            },
          },
        ];
      }
      if (targetType === 'BLOCK') {
        return [
          {
            title: '楼栋',
            dataIndex: 'blockTag',
            render: (_, { idcTag, blockTag }) => <SpaceText guid={`${idcTag}.${blockTag}`} />,
          },
          {
            title: '策略名称',
            dataIndex: 'inhibitionModelName',
            render: (inhibitionModelName, { inhibitionModelId }) => {
              return (
                <Link
                  type="link"
                  target="_blank"
                  to={generateAlarmConfigurationTemplateDetailUrl({
                    id: inhibitionModelId.toString(),
                  })}
                >
                  {inhibitionModelName}
                </Link>
              );
            },
          },
        ];
      }
      if (targetType === 'ROOM') {
        return [
          {
            title: '包间编号',
            dataIndex: 'roomTag',
            render: (_, { idcTag, blockTag, roomTag }) =>
              getSpaceGuidMap(`${idcTag}.${blockTag}.${roomTag}`).room,
          },
          {
            title: '策略名称',
            dataIndex: 'inhibitionModelName',
            render: (inhibitionModelName, { inhibitionModelId }) => {
              return (
                <Link
                  type="link"
                  target="_blank"
                  to={generateAlarmConfigurationTemplateDetailUrl({
                    id: inhibitionModelId.toString(),
                  })}
                >
                  {inhibitionModelName}
                </Link>
              );
            },
          },
        ];
      }
      if (targetType === 'COLUMN') {
        return [
          {
            title: '机列',
            dataIndex: 'columnTag',
            render: text => `${text}列`,
          },
          {
            title: '所属包间',
            dataIndex: 'roomTag',
            render: (_, { idcTag, blockTag, roomTag }) =>
              getSpaceGuidMap(`${idcTag}.${blockTag}.${roomTag}`).room,
          },
          {
            title: '策略名称',
            dataIndex: 'inhibitionModelName',
            render: (inhibitionModelName, { inhibitionModelId }) => {
              return (
                <Link
                  type="link"
                  target="_blank"
                  to={generateAlarmConfigurationTemplateDetailUrl({
                    id: inhibitionModelId.toString(),
                  })}
                >
                  {inhibitionModelName}
                </Link>
              );
            },
          },
        ];
      }
      if (targetType === 'GRID') {
        return [
          {
            title: '机柜',
            dataIndex: 'gridTag',
          },
          {
            title: '机列',
            dataIndex: 'columnTag',
            render: text => `${text}列`,
          },
          {
            title: '所属包间',
            dataIndex: 'roomTag',
            render: (_, { idcTag, blockTag, roomTag }) =>
              getSpaceGuidMap(`${idcTag}.${blockTag}.${roomTag}`).room,
          },
          {
            title: '策略名称',
            dataIndex: 'inhibitionModelName',
            render: (inhibitionModelName, { inhibitionModelId }) => {
              return (
                <Link
                  type="link"
                  target="_blank"
                  to={generateAlarmConfigurationTemplateDetailUrl({
                    id: inhibitionModelId.toString(),
                  })}
                >
                  {inhibitionModelName}
                </Link>
              );
            },
          },
        ];
      }
      return [
        {
          title: '设备编号',
          dataIndex: 'deviceName',
        },
        {
          title: '设备名称',
          dataIndex: 'deviceTag',
        },
        {
          title: '策略名称',
          dataIndex: 'inhibitionModelName',
          render: (inhibitionModelName, { inhibitionModelId }) => {
            return (
              <Link
                type="link"
                target="_blank"
                to={generateAlarmConfigurationTemplateDetailUrl({
                  id: inhibitionModelId.toString(),
                })}
              >
                {inhibitionModelName}
              </Link>
            );
          },
        },
      ];
    };

    return (
      <div style={{ height: '100%', flex: 1 }}>
        <Typography.Title level={5}>策略列表</Typography.Title>
        <Space style={{ width: '100%', marginTop: 8, marginBottom: 8 }} direction="vertical">
          <Space style={{ width: '100%' }} direction="horizontal">
            {targetType === 'DEVICE' && (
              <Input.Search
                placeholder="设备编号"
                allowClear
                value={filterValue.name}
                onChange={e => setFilterValue({ ...filterValue, name: e.target.value?.trim() })}
                onSearch={value => {
                  setFilterValue({ ...filterValue, name: value });
                }}
              />
            )}
            {targetType === 'DEVICE' && (
              <Input.Search
                placeholder="设备名称"
                allowClear
                value={filterValue.deviceLabel}
                onChange={e =>
                  setFilterValue({
                    ...filterValue,
                    deviceLabel: e.target.value?.trim(),
                  })
                }
                onSearch={value => {
                  setFilterValue({ ...filterValue, deviceLabel: value });
                }}
              />
            )}
            {['ROOM', 'COLUMN', 'GRID'].includes(targetType) && (
              <Input.Search
                placeholder="包间编号"
                allowClear
                value={filterValue.room}
                onChange={e =>
                  setFilterValue({
                    ...filterValue,
                    room: e.target.value?.trim(),
                  })
                }
                onSearch={value => {
                  setFilterValue({ ...filterValue, room: value });
                }}
              />
            )}
            <Input.Search
              placeholder="策略名称"
              allowClear
              value={filterValue.monitorName}
              onChange={e =>
                setFilterValue({
                  ...filterValue,
                  monitorName: e.target.value,
                })
              }
              onSearch={value => {
                setFilterValue({
                  ...filterValue,
                  monitorName: value,
                });
              }}
            />
          </Space>
          <Table<SelectedMonitorGroup>
            size="middle"
            rowKey="id"
            dataSource={monitorList}
            loading={loading}
            columns={getColumns()}
            rowSelection={{
              selectedRowKeys: dataSource.map(item => item.id),
              onChange: (_, selectedRows) => {
                setMonitorData(rows =>
                  [
                    ...rows.filter(row => !monitorList.find(item => item.id === row.id)),
                    ...selectedRows,
                  ].map(i => ({
                    ...i,
                    deviceType: i.deviceType ?? deviceType,
                    blockGuid: i.blockGuid ? i.blockGuid : `${i.idcTag}.${i.blockTag}`,
                    roomGuid: i.roomGuid ? i.roomGuid : `${i.idcTag}.${i.blockTag}.${i.roomTag}`,
                  }))
                );
              },
            }}
            pagination={{
              total: monitorList.length,
            }}
          />
        </Space>
      </div>
    );
  }
);
MonitorGroupTable.displayName = 'MonitorGroupTable';

export function monitorGroupDataMergeUtil(
  monitorGroups: SelectedMonitorGroup[],
  type?: string
): MonitorGroupMergeData[] {
  const data = sortBy(monitorGroups, 'deviceType');
  const list = uniqWith(data, (a, b) => {
    if (type === 'templateNew') {
      if (a.deviceType === b.deviceType) {
        if (a.inhibitionModelId && b.inhibitionModelId) {
          return (
            a.deviceGuid !== null &&
            b.inhibitionModelId !== null &&
            a.inhibitionModelId === b.inhibitionModelId &&
            a.deviceGuid === b.deviceGuid
          );
        }
        return a.inhibitionModelId ? false : true;
      }
      return false;
    }
    return (
      a.deviceGuid !== null &&
      b.inhibitionModelId !== null &&
      a.inhibitionModelId === b.inhibitionModelId &&
      a.deviceGuid === b.deviceGuid
    );
  }).reduce((listAuth: MonitorGroupMergeData[], key) => {
    const inx = listAuth.findIndex(
      item => item.deviceType === key.deviceType && item.inhibitionModelId === key.inhibitionModelId
    );
    if (inx > -1) {
      listAuth[inx] = {
        ...listAuth[inx],
        pointNum:
          'inhibitionItemIdList' in key ? key.inhibitionItemIdList?.length ?? 0 : key.pointNum,
        deviceList: [
          ...listAuth[inx]?.deviceList,
          {
            ...key,
            pointNum:
              'inhibitionItemIdList' in key ? key.inhibitionItemIdList?.length ?? 0 : key.pointNum,
          },
        ],
      };
    } else {
      listAuth.push({
        ...key,
        pointNum:
          'inhibitionItemIdList' in key ? key.inhibitionItemIdList?.length ?? 0 : key.pointNum,
        deviceList: key.deviceGuid
          ? [
              {
                ...key,
                pointNum:
                  'inhibitionItemIdList' in key
                    ? key.inhibitionItemIdList?.length ?? 0
                    : key.pointNum,
              },
            ]
          : [],
      });
    }
    return listAuth;
  }, []);
  return list;
}

export function monitorGroupDataLoopUtil(
  monitorGroups: MonitorGroupMergeData[]
): SelectedMonitorGroup[] {
  const list: SelectedMonitorGroup[] = [];
  monitorGroups.forEach(item => {
    if (item?.deviceList?.length) {
      item?.deviceList?.forEach(device => {
        list.push({ ...device, maxInfluencesStep: item.maxInfluencesStep });
      });
    } else {
      list.push({ ...item });
    }
  });
  return list;
}
