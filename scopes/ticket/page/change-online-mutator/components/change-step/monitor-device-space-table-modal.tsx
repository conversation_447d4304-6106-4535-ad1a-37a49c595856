import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { type ColumnType, Table } from '@manyun/base-ui.ui.table';
import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { MonitorGroupMergeDataDeviceList } from './monitor-group-table';

export type MonitorGroupTableProps = {
  targetType: string;
  dataSource: MonitorGroupMergeDataDeviceList[];
};

export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const MonitorDeviceSpaceTableModal = ({
  dataSource,
  targetType,
}: MonitorGroupTableProps) => {
  const [visible, setVisible] = useState(false);
  const [filterValue, setFilterValue] = useState<{
    name?: string;
    deviceLabel?: string;
    monitorName?: string;
    room?: string;
    roomType?: string;
    columnTag?: string;
    gridTag?: string;
  }>({
    name: '',
    deviceLabel: '',
    monitorName: '',
    room: '',
    roomType: '',
    columnTag: '',
    gridTag: '',
  });
  const columns = React.useMemo(() => {
    let basicColumns: ColumnType<MonitorGroupMergeDataDeviceList>[] = [
      {
        title: '设备编号',
        dataIndex: 'deviceName',
      },
      {
        title: '设备名称',
        dataIndex: 'deviceTag',
      },
      {
        title: '所属包间',
        dataIndex: 'roomGuid',
        render: text => getSpaceGuidMap(text)?.room,
      },
      {
        title: '包间类型',
        dataIndex: 'roomType',
        render: text => <RoomTypeText code={text} />,
      },
      {
        title: '所属楼栋',
        dataIndex: 'blockGuid',
        render: (_, { roomGuid }) =>
          roomGuid?.substring(0, roomGuid?.lastIndexOf('.')) && (
            <SpaceText guid={roomGuid.substring(0, roomGuid.lastIndexOf('.'))} />
          ),
      },
    ];
    if (targetType === 'IDC') {
      basicColumns = [
        {
          title: '园区',
          dataIndex: 'deviceGuid',
          render: text => <SpaceText guid={text} />,
        },
      ];
    }
    if (targetType === 'BLOCK') {
      basicColumns = [
        {
          title: '楼栋',
          dataIndex: 'deviceGuid',
          render: text => <SpaceText guid={text} />,
        },
      ];
    }
    if (targetType === 'ROOM') {
      basicColumns = [
        {
          title: '包间编号',
          dataIndex: 'deviceGuid',
          render: text => getSpaceGuidMap(text)?.room,
        },
        {
          title: '包间类型',
          dataIndex: 'roomType',
          render: text => <RoomTypeText code={text} />,
        },
        {
          title: '所属楼栋',
          dataIndex: 'blockGuid',
          render: (_, { roomGuid }) =>
            roomGuid?.substring(0, roomGuid?.lastIndexOf('.')) && (
              <SpaceText guid={roomGuid.substring(0, roomGuid.lastIndexOf('.'))} />
            ),
        },
      ];
    }
    if (targetType === 'COLUMN') {
      basicColumns = [
        {
          title: '机列',
          dataIndex: 'deviceGuid',
          render: text => `${text.substring(text.lastIndexOf('.') + 1)}列`,
        },
        {
          title: '所属包间',
          dataIndex: 'roomGuid',
          render: text => getSpaceGuidMap(text)?.room,
        },
        {
          title: '包间类型',
          dataIndex: 'roomType',
          render: text => <RoomTypeText code={text} />,
        },
        {
          title: '所属楼栋',
          dataIndex: 'blockGuid',
          render: (_, { roomGuid }) =>
            roomGuid?.substring(0, roomGuid?.lastIndexOf('.')) && (
              <SpaceText guid={roomGuid.substring(0, roomGuid.lastIndexOf('.'))} />
            ),
        },
      ];
    }
    if (targetType === 'GRID') {
      basicColumns = [
        {
          title: '机柜',
          dataIndex: 'deviceGuid',
          render: text => `${text.substring(text.lastIndexOf('.') + 1)}`,
        },
        {
          title: '机列',
          dataIndex: 'deviceGuid',
          render: text => {
            const [, , , clumnTag] = text?.split('.');
            const [tag] = clumnTag?.split('');
            return `${tag}列`;
          },
        },
        {
          title: '所属包间',
          dataIndex: 'roomGuid',
          render: text => getSpaceGuidMap(text)?.room,
        },
        {
          title: '包间类型',
          dataIndex: 'roomType',
          render: text => <RoomTypeText code={text} />,
        },
        {
          title: '所属楼栋',
          dataIndex: 'blockGuid',
          render: (_, { roomGuid }) =>
            roomGuid?.substring(0, roomGuid?.lastIndexOf('.')) && (
              <SpaceText guid={roomGuid.substring(0, roomGuid.lastIndexOf('.'))} />
            ),
        },
      ];
    }
    return basicColumns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [targetType, dataSource]);

  const _dataSource: MonitorGroupMergeDataDeviceList[] = dataSource.filter(item => {
    let isReturn: boolean = true;
    if (filterValue.name) {
      isReturn = !!item.deviceTag?.includes(filterValue.name);
    }
    if (filterValue.deviceLabel) {
      isReturn = isReturn && !!item.deviceName?.includes(filterValue.deviceLabel);
    }
    if (filterValue.roomType) {
      isReturn = isReturn && !!item.roomType?.includes(filterValue.roomType);
    }
    if (filterValue.room) {
      isReturn = isReturn && !!getSpaceGuidMap(item.roomGuid)?.room?.includes(filterValue.room);
    }
    if (filterValue.columnTag) {
      const [, , , clumnTag] = (item.deviceGuid ?? '')?.split('.');
      isReturn = isReturn && `${clumnTag}列`?.includes(filterValue.columnTag);
    }
    if (filterValue.gridTag) {
      const [, , , gridTag] = (item.deviceGuid ?? '')?.split('.');
      isReturn = isReturn && gridTag?.includes(filterValue.gridTag);
    }
    return isReturn;
  });

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        {dataSource.length}
      </Button>
      <Modal
        title={targetType === 'DEVICE' ? '屏蔽设备' : '屏蔽空间'}
        open={visible}
        width={targetType === 'BLOCK' || targetType === 'IDC' ? 344 : 800}
        destroyOnClose
        footer={false}
        bodyStyle={{ maxHeight: '60%' }}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <div
          style={{
            width: '100%',
            display: 'flex',
            flexDirection: 'row',
            gap: 16,
            marginBottom: 16,
          }}
        >
          {targetType === 'GRID' && (
            <Input.Search
              style={{ flex: 1 }}
              placeholder="机柜"
              allowClear
              value={filterValue.gridTag}
              onChange={e => setFilterValue({ ...filterValue, gridTag: e.target.value?.trim() })}
              onSearch={value => {
                setFilterValue({ ...filterValue, gridTag: value });
              }}
            />
          )}
          {(targetType === 'COLUMN' || targetType === 'GRID') && (
            <Input.Search
              style={{ flex: 1 }}
              placeholder="机列"
              allowClear
              value={filterValue.columnTag}
              onChange={e => setFilterValue({ ...filterValue, columnTag: e.target.value?.trim() })}
              onSearch={value => {
                setFilterValue({ ...filterValue, columnTag: value });
              }}
            />
          )}
          {targetType === 'DEVICE' && (
            <Input.Search
              style={{ flex: 1 }}
              placeholder="设备编号"
              allowClear
              value={filterValue.deviceLabel}
              onChange={e =>
                setFilterValue({ ...filterValue, deviceLabel: e.target.value?.trim() })
              }
              onSearch={value => {
                setFilterValue({ ...filterValue, deviceLabel: value });
              }}
            />
          )}
          {targetType === 'DEVICE' && (
            <Input.Search
              style={{ flex: 1 }}
              placeholder="设备名称"
              allowClear
              value={filterValue.name}
              onChange={e =>
                setFilterValue({
                  ...filterValue,
                  name: e.target.value?.trim(),
                })
              }
              onSearch={value => {
                setFilterValue({ ...filterValue, name: value });
              }}
            />
          )}
          {['ROOM', 'COLUMN', 'GRID', 'DEVICE'].includes(targetType) && (
            <RoomTypeSelect
              style={{ flex: 1 }}
              value={filterValue.roomType ? filterValue.roomType : null}
              placeholder="包间类型"
              onChange={value => {
                setFilterValue({ ...filterValue, roomType: value });
              }}
            />
          )}
          {['ROOM', 'COLUMN', 'GRID', 'DEVICE'].includes(targetType) && (
            <Input.Search
              style={{ flex: 1 }}
              placeholder="包间编号"
              allowClear
              value={filterValue.room}
              onChange={e =>
                setFilterValue({
                  ...filterValue,
                  room: e.target.value?.trim(),
                })
              }
              onSearch={value => {
                setFilterValue({ ...filterValue, room: value });
              }}
            />
          )}
        </div>
        <Table
          size="middle"
          rowKey="id"
          dataSource={_dataSource}
          columns={columns}
          pagination={
            targetType === 'IDC'
              ? false
              : {
                  total: _dataSource.length,
                }
          }
        />
      </Modal>
    </>
  );
};
