import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { useStopChangeOnlineStep } from '@manyun/ticket.gql.client.tickets';

export type StopStepButtonProps = {
  stepId: number;
  changeOrderId: string;
  startTime: number;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function StopStepButton({
  stepId,
  changeOrderId,
  startTime,
  onSuccess,
}: StopStepButtonProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [stop, { loading }] = useStopChangeOnlineStep({
    onCompleted(data) {
      if (!data.stopChangeOnlineStep.success) {
        message.error(data.stopChangeOnlineStep.message);
        return;
      }
      setVisible(false);
      onSuccess();
    },
  });

  const onSubmit = async () => {
    form.validateFields().then(values => {
      stop({
        variables: {
          query: {
            stepId,
            changeOrderId,
            endTime: values.endTime.valueOf(),
            stepStatus: values.stepStatus,
            stepDesc: values.stepDesc,
          },
        },
      });
    });
  };

  return (
    <>
      <Button
        type="primary"
        ghost
        size="small"
        loading={loading}
        onClick={() => {
          setVisible(true);
        }}
      >
        结束
      </Button>
      <Modal
        title="结束"
        open={visible}
        okText="确认"
        width={560}
        afterClose={() => form.resetFields()}
        // okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <Form
          form={form}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          initialValues={{
            startTime: moment(startTime),
            endTime: moment(),
            stepStatus: 'NORMAL',
          }}
        >
          <Form.Item label="步骤状态" name="stepStatus" required>
            <Radio.Group>
              <Radio value="NORMAL">正常</Radio>
              <Radio value="EXCEPTION">异常</Radio>
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label="开始时间"
            name="startTime"
            rules={[{ required: true, message: '开始时间必选！' }]}
          >
            <DatePicker disabled showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item
            label="结束时间"
            name="endTime"
            rules={[{ required: true, message: '结束时间必选！' }]}
          >
            <DatePicker disabled showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item
            label="步骤说明"
            name="stepDesc"
            rules={[{ max: 300, message: '最多输入 300 个字符！' }]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
