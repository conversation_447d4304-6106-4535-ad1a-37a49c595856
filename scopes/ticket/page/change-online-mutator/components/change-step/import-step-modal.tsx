import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import React, { useMemo, useState } from 'react';

import { Button, type ButtonProps } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { downloadChangeOnlineStepExcel } from '@manyun/ticket.service.download-change-online-step-excel';
import {
  type ApiResponseData,
  type CorrectDto,
  exportChangeOnlineStep,
} from '@manyun/ticket.service.export-change-online-step';

export type ImportRiskRegisterModalProps = {
  showRemind?: boolean;
  onSuccess: (value: { stepName: string; operator: string }[]) => void;
  disabled: boolean;
} & Pick<ButtonProps, 'disabled'>;

export function ImportStepModal({ showRemind, disabled, onSuccess }: ImportRiskRegisterModalProps) {
  const [visible, setVisible] = useState(false);
  const [riskRegistersImport, setRiskRegistersImport] = useState<ApiResponseData>();

  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await downloadChangeOnlineStepExcel();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `变更步骤模板.xlsx`);
  };

  return (
    <>
      {showRemind ? (
        <Popconfirm
          title="执行导入后将覆盖掉原有步骤"
          style={{ width: 290 }}
          okText="确认导入"
          cancelText="取消"
          onCancel={() => setVisible(false)}
          onConfirm={async e => {
            setVisible(true);
          }}
        >
          <Button disabled={disabled}>导入步骤</Button>
        </Popconfirm>
      ) : (
        <Button
          disabled={disabled}
          onClick={() => {
            setVisible(true);
          }}
        >
          导入步骤
        </Button>
      )}
      <Modal
        width={1024}
        open={visible}
        title="导入步骤"
        destroyOnClose
        footer={null}
        onCancel={() => {
          setVisible(false);
          setRiskRegistersImport(undefined);
        }}
      >
        <Row justify="space-between">
          <Space>
            <Upload
              showUploadList={false}
              accept=".csv,.xls,.xlsx"
              maxCount={1}
              customRequest={async ({ file }) => {
                setImportLoading(true);
                const fd = new FormData();
                fd.append('file', file);
                const { data, error } = await exportChangeOnlineStep(fd);
                setImportLoading(false);
                setRiskRegistersImport(data);
                if (error) {
                  message.error(error.message);
                  return;
                }
                if (data.errorCheckDtoList?.length === 0) {
                  message.success('导入成功');
                  setVisible(false);
                  setRiskRegistersImport(undefined);
                  onSuccess(data.correctDtoList);
                }
              }}
            >
              <Button loading={importLoading} type="primary">
                {locales.importRisk.importBtnText}
              </Button>
            </Upload>
            <Button type="link" loading={exportLoading} compact onClick={handleFileExport}>
              <DownloadOutlined />
              {locales.importRisk.downloadTemplate}
            </Button>
          </Space>
          {riskRegistersImport?.errorCheckDtoList && (
            <Typography.Text style={{ marginLeft: 'auto' }}>
              导入失败，{riskRegistersImport?.errorCheckDtoList?.length}条不符合填写规范
            </Typography.Text>
          )}
        </Row>

        <Table
          size="middle"
          scroll={{ x: 'max-content' }}
          loading={importLoading}
          dataSource={riskRegistersImport ? riskRegistersImport.errorCheckDtoList : []}
          columns={[
            {
              title: '步骤内容',
              dataIndex: ['errDto', 'stepName'],
              render: (_, { errDto, errMessage }) =>
                getToolTilp(errDto.stepName, errMessage, 'stepName'),
            },
            {
              title: '完成标准',
              dataIndex: ['errDto', 'operator'],
              render: (_, { errDto, errMessage }) =>
                getToolTilp(errDto.operator, errMessage, 'operator'),
            },
          ]}
        />
      </Modal>
    </>
  );
}

type RiskRegisterImportColumnKey = keyof CorrectDto;

function getToolTilp(
  value: string,
  errMessage: Record<string, string>,
  dataType: RiskRegisterImportColumnKey
) {
  if (Object.keys(errMessage).includes(dataType)) {
    return (
      <Space key={dataType}>
        <Typography.Text ellipsis type="danger">
          {value ?? '--'}
        </Typography.Text>
        <Tooltip title={errMessage[dataType]}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  }
  return <span key={dataType}> {value ?? '--'}</span>;
}
