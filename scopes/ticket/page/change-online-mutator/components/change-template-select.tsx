import React, { useEffect } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { useLazyChangeOnlineTemplates } from '@manyun/ticket.gql.client.tickets';
import { StandardChangeLibraryStatus } from '@manyun/ticket.model.ticket';

export type ChangeSourceProps = {
  locationGuid: string;
  onChange?: (value: string) => void;
};

export const ChangeTemplateSelect = React.forwardRef(
  ({ locationGuid, onChange, ...resp }: ChangeSourceProps, ref: React.Ref<RefSelectProps>) => {
    const [getTemplates, { data: templates }] = useLazyChangeOnlineTemplates();

    useEffect(() => {
      if (locationGuid) {
        getTemplates({
          variables: {
            query: {
              availAreaList: [locationGuid],
              templateStatusList: [StandardChangeLibraryStatus.Available],
            },
          },
        });
      }
    }, [locationGuid, getTemplates]);

    return (
      <Select
        // style={{ width: 112, marginRight: 8 }}
        fieldNames={{ label: 'templateName', value: 'templateId' }}
        options={templates?.changeOnlineTemplates?.data ?? []}
        allowClear
        disabled={!locationGuid}
        {...resp}
        onChange={(value, option) => {
          onChange && onChange(value);
        }}
      />
    );
  }
);

ChangeTemplateSelect.displayName = 'ChangeTemplateSelect';
