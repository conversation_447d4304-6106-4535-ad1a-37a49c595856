import debounce from 'lodash.debounce';
import React, { useEffect, useMemo, useState } from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { useFetchFuzzyChanges, useLazyFetchFuzzyEvents } from '@manyun/ticket.gql.client.tickets';
import { getChangeLocales } from '@manyun/ticket.model.change';
import type { SourceType } from '@manyun/ticket.model.change';
import type { TaskType } from '@manyun/ticket.model.ticket';
import { fetchPagedRiskRegisters } from '@manyun/ticket.service.fetch-paged-risk-registers';
import type { BaseInfo } from '@manyun/ticket.service.fetch-tickets';
import { fetchTickets } from '@manyun/ticket.service.fetch-tickets';

export type ChangeSourceProps = {
  idcTag: string;
  blockGuid: string;
  shouldShowEventNumber: boolean;
  value?:
    | {
        sourceType: SourceType;
        sourceId: { value: string; title: string; label: string; key: string }[];
        title?: string | null;
      }
    | undefined;
  disabled?: boolean;
  onChange?: (value: {
    sourceType?: SourceType;
    sourceId?: { value: string; title: string }[];
    title?: string | null;
  }) => void;
};

type LabelInValue = {
  label: string;
  key: string;
  value: string;
};

export const ChangeSourceSelect = React.forwardRef(
  (
    { idcTag, blockGuid, shouldShowEventNumber, value, disabled, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [dataSource, setDataSource] = useState<LabelInValue[]>([]);
    const locales = useMemo(() => getChangeLocales(), []);

    const [getFuzzyChanges] = useFetchFuzzyChanges();

    useEffect(() => {
      if (!dataSource.length) {
        setDataSource(value?.sourceId ?? []);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value?.sourceId]);

    const [getFuzzyEvents] = useLazyFetchFuzzyEvents({
      onCompleted(data) {
        if (data.fetchFuzzyEvents?.message) {
          message.error(data.fetchFuzzyEvents?.message);
          return;
        }
        const list: LabelInValue[] = (data?.fetchFuzzyEvents?.data ?? []).map(item => {
          return {
            title: item.eventTitle,
            label: `${shouldShowEventNumber ? item.eventNo : item.id} ${item.eventTitle ?? ''}`,
            value: item.eventNo!,
            key: item.eventNo!,
          };
        });
        setDataSource(list);
      },
    });

    const getChangeList = async (contentKey: string) => {
      if (!contentKey) {
        return;
      }
      const { data, error } = await getFuzzyChanges({
        variables: { queryCondition: contentKey, blockGuid, changeVersionList: [3] },
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = data?.fetchFuzzyChanges?.data
        ? data?.fetchFuzzyChanges?.data?.map(item => {
            return {
              title: item.title,
              label: `${item.changeOrderId} ${item.title ?? ''}`,
              value: item.changeOrderId,
              key: item.changeOrderId,
            };
          })
        : [];
      setDataSource(list);
    };

    const getEventList = debounce((contentKey: string) => {
      getFuzzyEvents({
        variables: {
          query: {
            blockGuid,
            keyword: contentKey,
          },
        },
      });
    }, 200);

    const getRiskRegisterList = async (contentKey: string) => {
      const { data, error } = await fetchPagedRiskRegisters({
        pageNum: 1,
        pageSize: 1000,
        blockGuids: [blockGuid],
        riskKey: contentKey,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = data.data.map(item => {
        return {
          title: item.riskDesc,
          label: `${item.id} ${item.riskDesc}`,
          value: item.id,
          key: item.id,
        };
      });
      setDataSource(list);
    };

    const getBaseTicketList = async (contentKey: string) => {
      if (value?.sourceType) {
        const { data, error } = await fetchTickets({
          pageNum: 1,
          pageSize: 1000,
          taskType: value.sourceType as TaskType,
          idcTag,
          blockTag: blockGuid,
          multiField: contentKey,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        const list: LabelInValue[] = data.data.map((item: BaseInfo) => {
          return {
            title: item.taskTitle,
            label: `${item.taskNo} ${item.taskTitle}`,
            value: item.taskNo,
            key: item.taskNo,
          };
        });

        setDataSource(list);
      }
    };
    const _disabled = !idcTag || !blockGuid || !value?.sourceType || disabled;
    return (
      <Space.Compact block style={{ width: 'calc(100% - 32px)', display: 'flex' }}>
        <MetaTypeSelect
          style={{ width: 112, marginRight: 8, height: 32 }}
          metaType={MetaType.CHANGE_ONLINE_SOURCE}
          value={value?.sourceType}
          allowClear
          disabled={!idcTag || !blockGuid || disabled}
          onChange={(value: SourceType) => {
            setDataSource([]);
            onChange && onChange({ sourceType: value, sourceId: undefined, title: null });
          }}
        />
        {Object.keys(locales.changeSource.source).includes(value?.sourceType ?? '') ? (
          <Select
            allowClear
            style={{ width: 418 }}
            disabled={_disabled}
            options={dataSource}
            value={value?.sourceId}
            showSearch
            mode="multiple"
            optionFilterProp="label"
            labelInValue
            placeholder="请输入ID或名称查询"
            onSearch={(content: string) => {
              switch (value?.sourceType) {
                case 'EVENT':
                  getEventList(content);
                  break;
                case 'CHANGE':
                  getChangeList(content);
                  break;
                case 'RISK':
                  getRiskRegisterList(content);
                  break;
                default:
                  getBaseTicketList(content);
                  break;
              }
            }}
            onChange={(id: string, info) => {
              onChange && onChange({ sourceType: value?.sourceType, sourceId: info });
            }}
          />
        ) : (
          <Input
            allowClear
            maxLength={100}
            disabled={disabled}
            style={{ width: 418 }}
            value={value?.sourceId?.[0]?.value}
            onChange={e => {
              onChange &&
                onChange({
                  sourceType: value?.sourceType,
                  sourceId: [{ value: e.target.value, title: '' }],
                  title: value?.title,
                });
            }}
          />
        )}
      </Space.Compact>
    );
  }
);

ChangeSourceSelect.displayName = 'ChangeSourceSelect';
