import { MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  type ChangeDeviceList,
  type CheckChangeOnlineLevelHasTicketData,
  type SubmitChangeOnlineQ,
  useLazyChangeOnlineTemplate,
  useLazyCheckChangeOnlineLevelHasTicket,
} from '@manyun/ticket.gql.client.tickets';
import { generateChangeTicketDetail } from '@manyun/ticket.route.ticket-routes';

import { changeOnlineToJson } from '../change-online-mutator';
import { ChangeSourceSelect } from './change-source';
import type { ChangeStepInfo } from './change-step/add-change-step';
import { ChangeTemplateSelect } from './change-template-select';

export type ChangeOnlineFormValues = Omit<
  SubmitChangeOnlineQ,
  | 'planStartTime'
  | 'planEndTime'
  | 'customerFileInfoList'
  | 'fileInfoList'
  | 'fileList'
  | 'stepList'
  | 'changeSourceInfoList'
  | 'changeResponsibleUserList'
> & {
  planEndTime: Moment;
  planStartTime: Moment;
  planTime: [Moment, Moment];
  fileInfoList: McUploadFile[];
  blockGuid: string;
  changeDeviceList?: ChangeDeviceList[];
  changeResponsibleUserList: { label: string; value: number }[];
  changeSourceInfoList: {
    sourceType: string;
    sourceId: { value: string; title: string }[];
    title: string;
  }[];
};

type RangeValue = [Moment | null, Moment | null] | null;

export type ChangeOfflineFormProps = {
  form: FormInstance<ChangeOnlineFormValues>;
  stepCodes: string[];
  stepMaps: Record<string, ChangeStepInfo>;
  setStepMaps: (stepMaps: Record<string, ChangeStepInfo>) => void;
  setStepCodes: (value: string[]) => void;
  setSubmitting: (loading: boolean) => void;
};

export function BaseInfoForm({
  form,
  stepCodes,
  stepMaps,
  setStepMaps,
  setStepCodes,
  setSubmitting,
}: ChangeOfflineFormProps) {
  const [dates, setDates] = useState<RangeValue>(null);
  const [changeOnlineLevelHasTicket, setChangeOnlineLevelHasTicket] = useState<
    CheckChangeOnlineLevelHasTicketData[]
  >([]);
  const blockGuid = Form.useWatch('blockGuid', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const planTime = Form.useWatch('planTime', form);
  const riskLevel = Form.useWatch('riskLevel', form);
  const changeSourceInfoList = Form.useWatch('changeSourceInfoList', form);
  const templateId = Form.useWatch('templateId', form);
  const { search } = useLocation();

  const { changeSourceNo, changeSourceType, spaceGuid } = getLocationSearchMap<{
    copyId?: string;
    changeSourceNo?: string;
    changeSourceType?: string;
    spaceGuid?: string;
  }>(search);

  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const featureIsEventConfigWithProcessEngine =
    ticketScopeCommonConfigs.events.features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';
  const [_idcTag] = (blockGuid ?? '').split('.');
  const [relatedIdcTag, relatedBlockTag] = spaceGuid
    ? spaceGuid.split('.')
    : [undefined, undefined];

  const [checkChangeOnlineLevelHasTicket] = useLazyCheckChangeOnlineLevelHasTicket({
    onCompleted(data) {
      if (data.checkChangeOnlineLevelHasTicket.data) {
        // if (_blockTag) {
        //   const checkList = data.checkChangeOnlineLevelHasTicket.data.filter(
        //     item => item.riskLevel === 'F2'
        //   );
        //   setChangeOnlineLevelHasTicket(checkList);
        // } else {
        //   const checkList = data.checkChangeOnlineLevelHasTicket.data.filter(item =>
        //     ['F0', 'F1'].includes(item.riskLevel ?? '')
        //   );
        //   setChangeOnlineLevelHasTicket(checkList);
        // }
        setChangeOnlineLevelHasTicket(data.checkChangeOnlineLevelHasTicket.data);
      }
    },
  });
  useEffect(() => {
    if (changeSourceNo && changeSourceType) {
      form.setFieldsValue({
        changeSourceInfoList: [
          { sourceType: changeSourceType, sourceId: [{ value: changeSourceNo, title: '' }] },
        ],
      });
    }
  }, [changeSourceNo, changeSourceType, form]);

  useEffect(() => {
    if (riskLevel && planTime?.[0] && planTime?.[1] && blockGuid) {
      const [_idc, block] = blockGuid.split('.');
      checkChangeOnlineLevelHasTicket({
        variables: {
          query: {
            idcTag: _idc,
            blockGuid: block ? blockGuid : null,
            planEndTime: planTime[1].valueOf(),
            planStartTime: planTime[0].valueOf(),
            currentRiskLevel: riskLevel,
          },
        },
      });
    }
  }, [planTime, riskLevel, blockGuid, checkChangeOnlineLevelHasTicket]);

  const [fetchTemplateDetail] = useLazyChangeOnlineTemplate({
    onCompleted(data) {
      if (data.changeOnlineTemplate.success) {
        const tempalteInfo = changeOnlineToJson(data?.changeOnlineTemplate.data ?? {});

        setStepMaps(tempalteInfo.stepMaps);
        setStepCodes(tempalteInfo.stepCodes);
        form.setFieldValue('riskLevel', data?.changeOnlineTemplate.data?.riskLevel);
        form.setFieldValue('changeCategory', data?.changeOnlineTemplate.data?.changeCategory);
        form.setFieldValue('reason', data?.changeOnlineTemplate.data?.reason);
      }
    },
  });
  const disabledDate = (current: Moment) => {
    if (!dates) {
      return false;
    }
    const tooLate =
      dates[0] &&
      (current.diff(dates[0], 'days') > 7 || (current && current < moment().subtract(8, 'days')));
    // const tooEarly = dates[1] && dates[1].diff(current, 'days') > 7;
    return !!tooLate || (current && current < moment().subtract(15, 'days'));
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setDates([null, null]);
    } else {
      setDates(null);
    }
  };

  return (
    <div style={{ width: 668 }}>
      <Form
        colon={false}
        form={form}
        labelCol={{ flex: ' 0 0 110px' }}
        wrapperCol={{ span: 24 }}
        initialValues={{
          changeSourceInfoList: [{ sourceType: undefined, sourceId: undefined, title: undefined }],
        }}
      >
        <Row>
          <Col span={24}>
            <Form.Item
              label="变更名称"
              name="title"
              rules={[
                {
                  required: true,
                  message: '变更名称必填！',
                  whitespace: true,
                },
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ]}
            >
              <Input allowClear />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="变更区域"
              name="blockGuid"
              rules={[
                {
                  required: true,
                  message: '变更区域必选！',
                },
              ]}
            >
              <LocationTreeSelect
                authorizedOnly
                showSearch
                allowClear
                idc={relatedIdcTag}
                block={relatedBlockTag}
                disabled={!!relatedBlockTag}
                onChange={value => {
                  const _maps: Record<string, ChangeStepInfo> = {};
                  stepCodes.forEach(item => {
                    _maps[item] = {
                      ...stepMaps[item],
                      stepDeviceInfoList: [],
                      stepInhibitionItemInfoList: [],
                    };
                  });
                  setStepMaps(_maps);
                  form.setFieldsValue({
                    changeSourceInfoList: [
                      changeSourceNo
                        ? {
                            sourceType: changeSourceType,
                            sourceId: [{ value: changeSourceNo, title: '' }],
                          }
                        : { sourceType: undefined, sourceId: undefined, title: undefined },
                    ],
                    templateId: undefined,
                  });
                  if (templateId) {
                    form.setFieldsValue({
                      reason: undefined,
                      changeCategory: undefined,
                      riskLevel: undefined,
                      // step: undefined,
                    });
                  }
                }}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="变更模板" name="templateId">
              <ChangeTemplateSelect
                locationGuid={blockGuid}
                onChange={templateId => {
                  templateId && fetchTemplateDetail({ variables: { templateId } });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更专业"
              name="reason"
              rules={[
                {
                  required: true,
                  message: '变更专业必填！',
                },
              ]}
            >
              <MetaTypeSelect disabled={!!templateId} metaType={MetaType.CHANGE_ONLINE_REASON} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更类别"
              name="changeCategory"
              rules={[
                {
                  required: true,
                  message: '变更类别必填！',
                },
              ]}
            >
              <MetaTypeSelect disabled={!!templateId} metaType={MetaType.CHANGE_ONLINE_CATEGORY} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更等级"
              name="riskLevel"
              rules={[
                {
                  required: true,
                  message: '变更等级必选！',
                },
              ]}
            >
              <MetaTypeSelect disabled={!!templateId} metaType={MetaType.CHANGE_ONLINE_LEVEL} />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.List name="changeSourceInfoList">
              {(fields, { add, remove }, { errors }) => (
                <>
                  {fields.map(({ key, name, ...field }, index) => (
                    <Form.Item
                      key={`${key}.${name}`}
                      required
                      label={index === 0 ? '变更来源' : ' '}
                      colon={index === 0}
                    >
                      <div style={{ width: '100%', display: 'flex' }}>
                        <Form.Item
                          {...field}
                          style={{ display: 'flex', flex: 1 }}
                          rules={[
                            {
                              validator: async (_, changeSourceInfoList) => {
                                if (
                                  changeSourceInfoList &&
                                  changeSourceInfoList.sourceType &&
                                  !changeSourceInfoList?.sourceId?.length
                                ) {
                                  return Promise.reject(new Error('变更来源ID必选'));
                                }
                                return Promise.resolve();
                              },
                            },
                          ]}
                          noStyle
                          name={[name]}
                        >
                          <ChangeSourceSelect
                            idcTag={_idcTag}
                            blockGuid={blockGuid}
                            disabled={changeSourceNo && changeSourceType && index === 0}
                            shouldShowEventNumber={featureIsEventConfigWithProcessEngineRequired}
                          />
                        </Form.Item>

                        {fields.length > 1 &&
                        (changeSourceNo && changeSourceType && index === 0 ? false : true) ? (
                          <MinusCircleOutlined
                            style={{ marginLeft: 8, width: 14 }}
                            onClick={() => remove(name)}
                          />
                        ) : null}
                      </div>
                    </Form.Item>
                  ))}
                  <Form.Item label=" " colon={false} labelCol={{ flex: ' 0 0 110px' }}>
                    {changeSourceInfoList?.length < 10 && (
                      <Button
                        style={{ width: '100%' }}
                        type="dashed"
                        disabled={fields.length >= 20}
                        icon={<PlusOutlined />}
                        onClick={() => {
                          add({
                            date: undefined,
                            time: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')],
                          });
                        }}
                      >
                        添加变更来源
                      </Button>
                    )}
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Col>
          <Col span={24}>
            <Form.Item
              label="变更窗口期"
              name="planTime"
              required
              rules={[
                {
                  validator: (_, rang) => {
                    if (!rang) {
                      return Promise.reject('变更窗口期必选！');
                    }
                    if (
                      rang[0] &&
                      moment(rang[0], 'YYYY-MM-DD HH:mm').isBefore(
                        moment().subtract(14, 'days'),
                        'minutes'
                      )
                    ) {
                      return Promise.reject('变更计划开始不可早于14天前');
                    }
                    if (rang[0] && rang[1]) {
                      if (
                        moment(rang[1], 'YYYY-MM-DD HH:mm').isAfter(
                          moment(rang[0], 'YYYY-MM-DD HH:mm').add(7, 'days'),
                          'minutes'
                        )
                      ) {
                        return Promise.reject('变更计划结束不得超过变更计划开始时间7天后');
                      }
                      if (
                        moment(rang[1], 'YYYY-MM-DD HH:mm').isBefore(
                          moment().subtract(7, 'days'),
                          'minutes'
                        )
                      ) {
                        return Promise.reject('变更计划结束不可早于7天前');
                      }
                      return Promise.resolve();
                    }

                    return Promise.resolve();
                  },
                },
              ]}
            >
              <DatePicker.RangePicker
                style={{ width: '100%' }}
                placeholder={['变更计划开始时间', '变更计划结束时间']}
                showTime={{ format: 'HH:mm' }}
                disabledDate={disabledDate}
                format="YYYY-MM-DD HH:mm"
                disabledTime={(current, type) => {
                  if (type === 'end' && planTime?.[0]) {
                    const now = moment().subtract(7, 'days');
                    const isSameDay = !current || current.isSame(now, 'day');
                    const isSameHour = !current || current.isSame(now, 'hour');
                    if (isSameDay) {
                      return {
                        disabledHours: isSameDay ? () => range(now.hours() + 1, 60) : undefined,
                        disabledMinutes:
                          isSameDay && isSameHour ? () => range(now.minutes() + 1, 60) : undefined,
                      };
                    }
                    const startSeven = moment(planTime[0]).add(7, 'days');
                    const isStartSameDay = !current || current.isSame(startSeven, 'day');
                    const isStartSameHour = !current || current.isSame(startSeven, 'hour');
                    if (isStartSameDay) {
                      return {
                        disabledHours: isStartSameDay
                          ? () => range(now.hours() + 1, 60)
                          : undefined,
                        disabledMinutes:
                          isStartSameDay && isStartSameHour
                            ? () => range(now.minutes() + 1, 60)
                            : undefined,
                      };
                    }
                    return {};
                  }
                  const now = moment().subtract(14, 'days');
                  const isSameDay = !current || current.isSame(now, 'day');
                  const isSameHour = !current || current.isSame(now, 'hour');
                  const isSameMinute = !current || current.isSame(now, 'minute');

                  if (type === 'start' || isSameDay) {
                    return {
                      disabledHours: isSameDay ? () => range(0, now.hours()) : undefined,
                      disabledMinutes:
                        isSameDay && isSameHour ? () => range(0, now.minutes()) : undefined,
                      disabledSeconds:
                        isSameDay && isSameHour && isSameMinute
                          ? () => [0, now.seconds()]
                          : undefined,
                    };
                  }
                  return {};
                }}
                onCalendarChange={(val, a, b) => {
                  setDates(val);
                  // form.setFieldValue('planTime', val);
                }}
                onOpenChange={onOpenChange}
                onChange={values => {
                  if (dates) {
                    // 强制设置秒为00
                    const formattedDates = values.map(date =>
                      date ? date.second(0).millisecond(0) : null
                    );
                    form.setFieldValue('planTime', formattedDates);
                  }
                  if (values && values.length && values[0] && values[1]) {
                    const startTime = moment(values[0].format('YYYY-MM-DD HH:mm')).valueOf();
                    const endTime = moment(values[1].format('YYYY-MM-DD HH:mm')).valueOf();
                    const fiveAfterTime = moment(moment().format('YYYY-MM-DD HH:mm'))
                      .add(5, 'd')
                      .valueOf();
                    const twoAfterTime = moment(moment().format('YYYY-MM-DD HH:mm'))
                      .add(2, 'd')
                      .valueOf();
                    // const twoLaterTime = moment().subtract(2, 'd').valueOf();
                    const oneLaterTime = moment(moment().format('YYYY-MM-DD HH:mm'))
                      .subtract(1, 'd')
                      .valueOf();
                    const sevenLaterTime = moment(moment().format('YYYY-MM-DD HH:mm'))
                      .subtract(7, 'd')
                      .valueOf();
                    // 2天*24h＜计划开始时间-当前时间＜5天*24h 非计划性变更
                    if (startTime > twoAfterTime && startTime < fiveAfterTime) {
                      form.setFieldValue('changeType', 'UNPLANNED');
                      form.validateFields(['changeType']);
                      return;
                    }
                    // 计划开始时间-当前时间≥5天*24h 计划性变更
                    if (startTime >= fiveAfterTime) {
                      form.setFieldValue('changeType', 'PLANNED');
                      form.validateFields(['changeType']);
                      return;
                    }
                    // 0h＜计划开始时间-当前时间≤48h 紧急变更
                    if (startTime <= twoAfterTime && startTime > moment().valueOf()) {
                      form.setFieldValue('changeType', 'URGENT');
                      form.validateFields(['changeType']);
                      return;
                    }
                    // 补提变更：0h≤当前时间-计划结束时间＜24h
                    if (endTime <= moment().valueOf() && endTime >= oneLaterTime) {
                      form.setFieldValue('changeType', 'SUPPLEMENTARY');
                      form.validateFields(['changeType']);
                      return;
                    }
                    // 超期补提变更：24h≤当前时间-计划结束时间≤7*24h
                    if (endTime < oneLaterTime && endTime >= sevenLaterTime) {
                      form.setFieldValue('changeType', 'OVERDUE');
                      form.validateFields(['changeType']);
                      return;
                    }
                    // 补提变更：0h≤当前时间-计划结束时间＜24h
                    if (startTime <= moment().valueOf() && endTime >= moment().valueOf()) {
                      form.setFieldValue('changeType', 'SUPPLEMENTARY');
                      form.validateFields(['changeType']);
                      return;
                    }
                  }

                  form.setFieldValue('changeType', undefined);
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更类型"
              name="changeType"
              rules={[
                {
                  required: true,
                  message: '变更类型必填！',
                },
              ]}
            >
              <MetaTypeSelect disabled metaType={MetaType.CHANGE_ONLINE_TYPE} />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更负责人"
              name="changeResponsibleUserList"
              rules={[
                {
                  required: true,
                  message: '变更负责人必填！',
                },
              ]}
            >
              <UserSelect userState="in-service" mode="multiple" />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="影响说明"
              name="changeInfluence"
              rules={[
                {
                  required: true,
                  message: '影响说明必填！',
                  whitespace: true,
                },
                {
                  max: 300,
                  message: '可最可输入300 个字符！',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="附件"
              name="fileInfoList"
              // rules={[{ required: true, message: '请选择上传文件！' }]}
              valuePropName="fileList"
              getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
                if (fileList.filter(file => file.status === 'uploading').length) {
                  setSubmitting(true);
                } else {
                  setSubmitting(false);
                }
                return fileList;
              }}
            >
              <McUpload
                showAccept
                accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
                showUploadList
                allowDelete
                maxCount={5}
                maxFileSize={20}
                openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 5}
              >
                <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 5}>
                  点此上传
                </Button>
              </McUpload>
            </Form.Item>
          </Col>
        </Row>
      </Form>
      <Modal
        width={640}
        open={!!changeOnlineLevelHasTicket.length}
        title="变更等级确认"
        destroyOnClose
        maskClosable={false}
        okText="确认"
        onOk={() => {
          setChangeOnlineLevelHasTicket([]);
        }}
        onCancel={() => {
          form.setFieldValue('planTime', undefined);
          form.setFieldValue('changeType', undefined);

          setChangeOnlineLevelHasTicket([]);
        }}
      >
        <Alert
          message={
            <>
              同一区域同期已存在{changeOnlineLevelHasTicket.length}
              个中/高风险变更，是否确认再新建1个
              <MetaTypeText metaType={MetaType.CHANGE_ONLINE_LEVEL} code={riskLevel} />
              变更？
            </>
          }
          type="warning"
          showIcon
        />
        <Table
          size="middle"
          scroll={{ x: 'max-content' }}
          dataSource={changeOnlineLevelHasTicket}
          columns={[
            {
              title: '变更ID',
              dataIndex: 'changeOrderId',
              fixed: true,
              render: (_, { changeOrderId }) => {
                if (changeOrderId) {
                  return (
                    <Link
                      target="_blank"
                      to={generateChangeTicketDetail({
                        id: encodeURIComponent(changeOrderId),
                      })}
                    >
                      {changeOrderId}
                    </Link>
                  );
                }
                return '--';
              },
            },
            {
              title: '变更名称',
              dataIndex: 'title',
            },
          ]}
        />
      </Modal>
    </div>
  );
}

function range(start: number, end: number) {
  const result: number[] = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }

  return result;
}
