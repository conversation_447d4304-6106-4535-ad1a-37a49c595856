import { message } from 'antd';
import { omit } from 'lodash';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import {
  useLazyChangeOnline,
  useSaveChangeOnline,
  useSubmitChangeOnline,
} from '@manyun/ticket.gql.client.tickets';
import type {
  ChangeOnlineData,
  ChangeStepInfo as ChangeStepInfoApi,
  SubmitChangeOnlineQ,
} from '@manyun/ticket.gql.client.tickets';
import { generateChangeTicketDetail } from '@manyun/ticket.route.ticket-routes';

import { BaseInfoForm } from './components/baseinfo-form';
import type { ChangeOnlineFormValues } from './components/baseinfo-form';
import type { ChangeStepInfo } from './components/change-step/add-change-step';
import { ChangeStep } from './components/change-step/change-step';
import {
  type SelectedMonitorGroup,
  monitorGroupDataLoopUtil,
  monitorGroupDataMergeUtil,
} from './components/change-step/monitor-group-table';

export function ChangeOnlineMutator() {
  const [stepCurrent, setStepCurrent] = useState<number>(0);
  const [stepMaps, setStepMaps] = useState<Record<string, ChangeStepInfo>>({});
  const [stepCodes, setStepCodes] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);

  const { id } = useParams<{ id: string }>();
  const { search } = useLocation();
  const { copyId, changeSourceNo, changeSourceType, spaceGuid } = getLocationSearchMap<{
    copyId?: string;
    changeSourceNo?: string;
    changeSourceType?: string;
    spaceGuid?: string;
  }>(search);
  const history = useHistory();
  const [, { checkUserId }] = useAuthorized();

  const [form] = Form.useForm();
  const [stepForm] = Form.useForm();
  const blockGuid = Form.useWatch('blockGuid', form);
  const [_idcTag, _blockTag] = (blockGuid ?? '').split('.');
  const [submit, { loading: submitLoading }] = useSubmitChangeOnline({
    onCompleted(data) {
      if (!data.submitChangeOnline?.success) {
        message.error(data.submitChangeOnline?.message);
        return;
      }
      message.success('创建成功');
      history.push(generateChangeTicketDetail({ id: data.submitChangeOnline.data?.changeOrderId }));
    },
  });
  const [save, { loading: saveLoading }] = useSaveChangeOnline({
    onCompleted(data) {
      if (!data.saveChangeOnline?.success) {
        message.error(data.saveChangeOnline?.message);
        return;
      }
      message.success('创建成功');
      history.push(generateChangeTicketDetail({ id: data.saveChangeOnline?.data }));
    },
  });

  const [fetchDetail, { data }] = useLazyChangeOnline({
    onCompleted(data) {
      if (data.changeOnline?.success) {
        const tempalteInfo = changeOnlineToJson(data?.changeOnline?.data ?? {});

        setStepMaps(tempalteInfo.stepMaps);
        setStepCodes(tempalteInfo.stepCodes);
        form.setFieldsValue({
          ...tempalteInfo.changeInfo,
          changeSourceInfoList: tempalteInfo.changeInfo.changeSourceInfoList?.length
            ? tempalteInfo.changeInfo.changeSourceInfoList
            : [{ sourceType: undefined, sourceId: [], title: undefined }],
        });
      }
    },
  });

  useEffect(() => {
    if (spaceGuid) {
      form.setFieldsValue({ blockGuid: spaceGuid });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spaceGuid]);

  useEffect(() => {
    if (id || copyId) {
      fetchDetail({ variables: { changeOrderId: id || copyId } });
    }
  }, [id, copyId, fetchDetail]);

  const onSubmit = () => {
    stepForm.validateFields().then(values => {
      let canSubmit = true;
      stepCodes.forEach(item => {
        const { stepResponsibleUserList, opType, stepDeviceInfoList, stepInhibitionItemInfoList } =
          stepMaps[item] ?? {};
        if (!stepResponsibleUserList?.length) {
          canSubmit = false;
        }
        if (canSubmit && opType === 'device' && !stepDeviceInfoList?.length) {
          canSubmit = false;
        }
        if (
          canSubmit &&
          stepInhibitionItemInfoList?.length &&
          stepInhibitionItemInfoList.some(i => !i.deviceList?.length)
        ) {
          canSubmit = false;
        }
      });
      if (!canSubmit) {
        message.error('请检查必填项');
        return;
      }
      const params = changeOnlineToApi(form.getFieldsValue(), stepCodes, stepMaps);
      submit({
        variables: { query: { ...params, changeOrderId: id, changeSourceType, changeSourceNo } },
      });
    });
  };
  const onSave = () => {
    form.validateFields(['blockGuid', 'title']).then(() => {
      const values = form.getFieldsValue();
      const params = changeOnlineToApi(values, stepCodes, stepMaps);
      save({
        variables: { query: { ...params, changeOrderId: id, changeSourceType, changeSourceNo } },
      });
    });
  };
  const getCanEdit = () => {
    let canEdit = checkUserId(data?.changeOnline.data?.creatorId);
    if (data?.changeOnline?.data?.extJson?.changeResponsibleUserInfoList?.length) {
      canEdit =
        canEdit ||
        data.changeOnline.data.extJson.changeResponsibleUserInfoList.some(item =>
          checkUserId(item?.userId)
        );
    }
    return canEdit;
  };

  return (
    <>
      <Card
        bordered={false}
        headStyle={{ borderBottom: 0 }}
        style={{ marginBottom: 48 }}
        title={
          <Typography.Title showBadge level={5}>
            {id ? '编辑变更' : '新建变更'}
          </Typography.Title>
        }
        bodyStyle={{ alignItems: 'center', display: 'flex', flexDirection: 'column' }}
      >
        <Steps
          style={{ paddingLeft: 100, paddingRight: 100, marginBottom: 40 }}
          current={stepCurrent}
        >
          <Steps.Step title="基本信息" />
          <Steps.Step title="变更步骤" />
          <Steps.Step title="创建完成" />
        </Steps>
        <div style={stepCurrent !== 0 ? { display: 'none' } : {}}>
          <BaseInfoForm
            form={form}
            spaceGuid={spaceGuid}
            stepMaps={stepMaps}
            stepCodes={stepCodes}
            setSubmitting={setLoading}
            setStepCodes={setStepCodes}
            setStepMaps={setStepMaps}
          />
        </div>
        {stepCurrent === 1 && (
          <Form colon={false} form={form} style={{ width: '100%' }}>
            <Form.Item
              label=""
              name="step"
              dependencies={['step']}
              rules={[
                {
                  required: true,
                  message: '至少添加一个步骤',
                },
              ]}
            >
              <ChangeStep
                form={stepForm}
                idcTag={_idcTag}
                blockTag={_blockTag}
                stepMaps={stepMaps}
                stepCodes={stepCodes}
                mode="ticketNew"
                canEdit={id ? getCanEdit() : true}
                setStepInfo={(_stepMaps, _stepCodes) => {
                  setStepMaps(_stepMaps);
                  setStepCodes(_stepCodes);
                }}
              />
            </Form.Item>
          </Form>
        )}
      </Card>
      <FooterToolBar>
        <Space>
          {stepCurrent === 1 && (
            <Button
              onClick={() => {
                setStepCurrent(stepCurrent - 1);
              }}
            >
              上一步
            </Button>
          )}
          {stepCurrent === 0 && (
            <Button
              type="primary"
              onClick={() => {
                form.validateFields().then(values => {
                  setStepCurrent(stepCurrent + 1);
                });
                // setStepCurrent(stepCurrent + 1);
              }}
            >
              下一步
            </Button>
          )}
          {stepCurrent === 1 && (
            <Button
              type="primary"
              loading={submitLoading || loading || saveLoading}
              disabled={!stepCodes?.length}
              onClick={() => {
                onSubmit();
              }}
            >
              提交
            </Button>
          )}

          <Button
            loading={submitLoading || loading || saveLoading}
            onClick={() => {
              onSave();
            }}
          >
            保存
          </Button>
        </Space>
      </FooterToolBar>
    </>
  );
}

export function changeOnlineToApi(
  value: ChangeOnlineFormValues,
  stepCodes: string[],
  stepMaps: Record<string, ChangeStepInfo>,
  isTemplate?: boolean
): SubmitChangeOnlineQ {
  const [idcTag] = isTemplate ? [] : (value.blockGuid || '')?.split('.');
  const changeSourceInfoList = [];
  if (value.changeSourceInfoList?.length) {
    value.changeSourceInfoList.map(item => {
      if (Array.isArray(item.sourceId)) {
        item.sourceId?.map(source => {
          if (item.sourceId.length) {
            changeSourceInfoList.push({
              sourceId: source.value,
              sourceType: item.sourceType,
              title: source.title,
            });
          }
          return {};
        });
      }
      // if (typeof item.sourceId === 'string') {
      //   changeSourceInfoList.push({
      //     sourceId: item.sourceId,
      //     sourceType: item.sourceType,
      //     title: '',
      //   });
      // }
      return {};
    });
  }
  const params: SubmitChangeOnlineQ = isTemplate
    ? {}
    : {
        title: value.title,
        idcTag: idcTag,
        blockGuid: value.blockGuid,
        reason: value.reason,
        changeCategory: value.changeCategory,
        riskLevel: value.riskLevel,
        changeType: value.changeType,
        planStartTime: value.planTime?.[0] && value.planTime[0].valueOf(),
        planEndTime: value.planTime?.[1] && value.planTime[1].valueOf(),
        templateId: value.templateId,
        changeInfluence: value.changeInfluence,
        fileInfoList: value.fileInfoList?.length
          ? value.fileInfoList.map(obj => ({
              ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
            }))
          : [],
        changeSourceInfoList: changeSourceInfoList,
        changeResponsibleUserList: (value.changeResponsibleUserList ?? []).map(item => ({
          userId: item.value,
          userName: item.label,
        })),
      };
  const stepList: ChangeStepInfoApi[] = [];
  if (stepCodes) {
    stepCodes.forEach((item, index) => {
      const { stepInhibitionItemInfoList, stepDeviceInfoList, stepResponsibleUserList, ...res } =
        stepMaps[item];

      const info = {
        ...omit(
          res,
          'id',
          'stepStatus',
          'operatorId',
          'endTime',
          'startTime',
          'stepDesc',
          'operatorName'
        ),
        stepOrder: index + 1,
        extendJson: {
          stepResponsibleUserList: (stepResponsibleUserList ?? []).map(item => ({
            userId: item.value,
            userName: item.label,
          })),
          stepDeviceInfoList: (stepDeviceInfoList ?? []).map(i => ({
            deviceGuid: i.deviceGuid,
            deviceName: i.deviceName,
            deviceTag: i.deviceTag,
            roomTag: i.roomTag,
            deviceLabel: i.deviceLabel,
          })),
          stepInhibitionItemInfoList: stepInhibitionItemInfoList?.length
            ? monitorGroupDataLoopUtil(stepInhibitionItemInfoList).map(stepInhibitionItemInfo => ({
                deviceType: stepInhibitionItemInfo.deviceType,
                deviceTag: stepInhibitionItemInfo.deviceLabel,
                deviceGuid: stepInhibitionItemInfo.deviceGuid,
                deviceName: stepInhibitionItemInfo.deviceName,
                blockGuid: stepInhibitionItemInfo.blockGuid,
                roomGuid: stepInhibitionItemInfo.roomGuid,
                roomType: stepInhibitionItemInfo.roomType,
                inhibitionModelId: stepInhibitionItemInfo.inhibitionModelId,
                maxInfluencesStep:
                  stepInhibitionItemInfo.maxInfluencesStep === '99999'
                    ? 99999
                    : stepInhibitionItemInfo.maxInfluencesStep
                      ? stepCodes.indexOf(stepInhibitionItemInfo.maxInfluencesStep ?? '') + 1
                      : stepCodes.indexOf(item ?? '') + 1,
              }))
            : [],
        },
      };
      stepList.push(info);
    });
  }
  return { ...params, stepList };
}

export function changeOnlineToJson(value: ChangeOnlineData): {
  changeInfo: ChangeOnlineFormValues;
  stepCodes: string[];
  stepMaps: Record<string, ChangeStepInfo>;
} {
  const sourceList = value.extJson?.changeSourceInfoList?.length
    ? value.extJson.changeSourceInfoList.reduce(
        (
          listAuth: {
            sourceType: string;
            sourceId: { value: string; title: string; label: string; key: string }[];
          }[],
          key
        ) => {
          const inx = listAuth.findIndex(item => item.sourceType === key.sourceType);
          if (inx > -1) {
            listAuth[inx] = {
              ...listAuth[inx],
              sourceId: [
                ...listAuth[inx]?.sourceId,
                {
                  value: key.sourceId!,
                  title: key.title!,
                  label: `${key.sourceId} ${key.title ?? ''}`!,
                  key: key.sourceId!,
                },
              ],
            };
          } else {
            listAuth.push({
              sourceType: key.sourceType!,
              sourceId: [
                {
                  value: key.sourceId!,
                  key: key.sourceId!,
                  title: key.title!,
                  label: `${key.sourceId} ${key.title ?? ''}`!,
                },
              ],
            });
          }
          return listAuth;
        },
        []
      )
    : [];
  const changeInfo = {
    ...omit(value, 'stepList', 'changeResponsibleUserList'),
    planTime:
      value.planStartTime && value.planEndTime
        ? [moment(value.planStartTime), moment(value.planEndTime)]
        : [],
    changeResponsibleUserList: value.extJson?.changeResponsibleUserInfoList?.map(item => ({
      label: item?.userName,
      value: item?.userId,
    })),
    changeSourceInfoList: sourceList,
  };
  const stepMaps: Record<string, ChangeStepInfo> = {};
  const stepCodes: string[] = (value.stepList ?? []).map(() => shortid());
  value.stepList?.forEach((item, index) => {
    stepMaps[stepCodes[index]] = {
      ...omit(item, '__typename'),
      stepResponsibleUserList: item?.extendJson?.stepResponsibleUserList?.map(i => ({
        label: i!.userName!,
        value: i!.userId!,
      })),

      stepInhibitionItemInfoList: monitorGroupDataMergeUtil(
        item?.extendJson.stepInhibitionItemInfoList?.length
          ? item.extendJson.stepInhibitionItemInfoList.map(stepInhibitionItemInfo => {
              let targetType = 'DEVICE';
              switch (stepInhibitionItemInfo.deviceType) {
                case '90101':
                  targetType = 'IDC';
                  break;
                case '90102':
                  targetType = 'BLOCK';
                  break;
                case '90103':
                  targetType = 'ROOM';
                  break;
                case '90104':
                  targetType = 'COLUMN';
                  break;
                case '90105':
                  targetType = 'GRID';
                  break;
                default:
                  break;
              }
              return {
                ...stepInhibitionItemInfo,
                targetType,
                maxInfluencesStep: stepInhibitionItemInfo.maxInfluencesStep
                  ? stepInhibitionItemInfo.maxInfluencesStep === 99999
                    ? '99999'
                    : stepCodes[stepInhibitionItemInfo.maxInfluencesStep - 1]
                  : null,
                id: `${stepInhibitionItemInfo.inhibitionModelId}.${stepInhibitionItemInfo.deviceGuid}`,
                idcTag: value.idcTag,
              } as SelectedMonitorGroup;
            })
          : []
      ),
      stepDeviceInfoList: item?.extendJson?.stepDeviceInfoList ?? [],
    };
  });
  return { changeInfo, stepMaps, stepCodes };
}
