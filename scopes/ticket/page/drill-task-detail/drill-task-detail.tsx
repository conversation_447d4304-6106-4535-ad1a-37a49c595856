import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';

import { PlanType } from '@manyun/ticket.model.task';
import { DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';
import { TicketTaskDetailInfo } from '@manyun/ticket.ui.ticket-task-detail-info';

export function DrillTaskDetail() {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();
  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }} color="default">
      <TicketTaskDetailInfo
        id={id}
        planType={PlanType.DrillPlan}
        unusedDescriptionsItems={['manageType']}
        onCallback={() => {
          history.push(DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH);
        }}
      />
    </Container>
  );
}
