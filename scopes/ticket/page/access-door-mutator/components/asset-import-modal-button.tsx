import React, { useState } from 'react';

import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import { nanoid } from 'nanoid';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Upload } from '@manyun/dc-brain.ui.upload';
import { AssetsTable } from '@manyun/resource-hub.ui.assets-table';
import { downloadAssetImportTemplate } from '@manyun/ticket.service.download-asset-import-template';
import { importAsset } from '@manyun/ticket.service.import-asset';

type AssetImportColumnKey =
  | 'rowTag'
  | 'deviceType'
  | 'serialNo'
  | 'vendor'
  | 'productModel'
  | 'accessCount';
export type AssetImportModalButtonProps = {
  location: string[];
  taskSubType: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  currentData: any[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  setCurrentData: (params: any[] | ((params: any[]) => any[])) => void;
} & ButtonProps;
export function AssetImportModalButton({
  location,
  taskSubType,
  currentData,
  setCurrentData,
  ...rest
}: AssetImportModalButtonProps) {
  const [visible, setVisible] = useState(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [dataSource, setDataSource] = useState<any[]>([]);
  const uploadFile = async (fd: FormData) => {
    const { error, data } = await importAsset(fd);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      if (data.faultTotal === 0) {
        message.success('导入成功！');
        const afterTypeChangeRacks: {
          id: string;
          sortType?: string | null;
          serialNumber?: string | null;
          brand?: string | null;
          model?: string | null;
          sum?: number | null;
        }[] = data.excelCheckDtos.map(asset => ({
          id: nanoid(),
          sortType: asset.data.deviceType,
          serialNumber: asset.data.serialNo,
          brand: asset.data.vendor,
          model: asset.data.productModel,
          sum: asset.data.accessCount,
        }));
        const newAssetList = [...currentData, ...afterTypeChangeRacks];
        const newAssetGuidList = newAssetList.map(item => item.sortType);

        const uniqAssetList = newAssetList.filter(
          (item, index) => newAssetGuidList.indexOf(item.sortType) === index
        );
        setCurrentData(uniqAssetList);
        setVisible(false);
        setDataSource([]);
        return;
      }
      setDataSource(data.excelCheckDtos);
    }
  };
  const beforeUpload = (file: File) => {
    const fd = new FormData();
    fd.append('file', file);
    fd.append('taskSubType', taskSubType);
    fd.append('idcTag', location[0]);
    fd.append('blockTag', location[1].split('.')[1]);
    uploadFile(fd);

    return false;
  };
  const download = async () => {
    const { error, data } = await downloadAssetImportTemplate();
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '物资导入模版.xlsx');
  };
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const columns: Array<ColumnType<any>> = [
    {
      title: '行号',
      dataIndex: 'rowTag',
      width: 120,
      fixed: 'left' as 'left',
    },

    {
      title: '物资分类',
      dataIndex: ['data', 'deviceType'],
      render: (text, { errMessage }) => getToolTilp(text, errMessage, 'deviceType'),
    },
    {
      title: 'SN',
      dataIndex: ['data', 'serialNo'],
      render: (text, { errMessage }) => getToolTilp(text, errMessage, 'serialNo'),
    },
    {
      title: '品牌',
      dataIndex: ['data', 'vendor'],
      render: (text, { errMessage }) => getToolTilp(text, errMessage, 'vendor'),
    },
    {
      title: '型号',
      dataIndex: ['data', 'productModel'],
      render: (text, { errMessage }) => getToolTilp(text, errMessage, 'productModel'),
    },

    {
      title: '数量',
      dataIndex: ['data', 'accessCount'],
      render: (text, { errMessage }) => getToolTilp(text, errMessage, 'accessCount'),
    },
  ];
  return (
    <Space>
      <Button
        // disabled={!location || location.length !== 2 || !taskSubType}
        {...rest}
        onClick={() => {
          setVisible(true);
        }}
      >
        导入物资
      </Button>

      <Modal
        width="1101px"
        title="导入物资"
        bodyStyle={{ maxHeight: '80vh', overflowY: 'auto', width: 1101 }}
        open={visible}
        destroyOnClose
        centered
        footer={null}
        onOk={() => {
          setVisible(false);
        }}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
          <Space>
            <Upload
              key="import"
              accept=".csv,.xls,.xlsx"
              showUploadList={false}
              beforeUpload={beforeUpload}
            >
              <Button type="primary">导入</Button>
            </Upload>
            <Button
              key="download"
              type="link"
              compact
              icon={<DownloadOutlined />}
              onClick={download}
            >
              下载模版
            </Button>
          </Space>
          <AssetsTable
            scroll={{ x: 'max-content' }}
            columns={columns}
            mode="viewer"
            parentDataSource={dataSource}
          />
        </Space>
      </Modal>
    </Space>
  );
}

function getToolTilp(
  value: string,
  errMessage: Record<string, string>,
  dataType: AssetImportColumnKey
) {
  if (Object.keys(errMessage).includes(dataType)) {
    return (
      <Space>
        <Typography.Text ellipsis type="danger">
          {value !== null && value !== undefined ? value : '--'}
        </Typography.Text>
        <Tooltip title={errMessage[dataType]}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  }
  return value;
}
