import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import { message } from 'antd';
import omit from 'lodash.omit';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import {
  useCreateEmergencyProcess,
  useLazyEmergencyProcessDetail,
  useUpdateEmergencyProcess,
} from '@manyun/ticket.gql.client.tickets';
import {
  EMERGENCY_PROCEE_LIST_ROUTE_PATH,
  generateEmergencyProcessLocation,
} from '@manyun/ticket.route.ticket-routes';

export function EmergencyProcessMutator() {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const history = useHistory();
  const { id } = useParams<{ id: string }>();

  const [createEmergencyProcess, { loading: createLoading }] = useCreateEmergencyProcess();
  const [updateEmergencyProcess, { loading: updateLoading }] = useUpdateEmergencyProcess();

  const [getEmergencyProcessDetail] = useLazyEmergencyProcessDetail();

  const onSubmit = () => {
    form.validateFields().then(async values => {
      const { fileInfoList, ...resp } = values;
      if (id) {
        updateEmergencyProcess({
          variables: {
            query: {
              ...resp,
              emergencyId: id,
              fileInfoList: fileInfoList?.map(obj => ({
                ...omit(
                  McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
                  '__typename'
                ),
              })),
            },
          },
          onCompleted(data) {
            if (!data.updateEmergencyProcess?.success) {
              message.error(data.updateEmergencyProcess?.message);
              return;
            }
            history.push(generateEmergencyProcessLocation({ id }));
            message.success('编辑应急流程成功');
          },
        });
        return;
      }
      createEmergencyProcess({
        variables: {
          query: {
            ...resp,
            fileInfoList: fileInfoList?.map(obj => ({
              ...omit(
                McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
                '__typename'
              ),
            })),
          },
        },
        onCompleted(data) {
          if (!data.createEmergencyProcess?.success) {
            message.error(data.createEmergencyProcess?.message);
            return;
          }
          if (data.createEmergencyProcess?.success && data.createEmergencyProcess.data) {
            history.push(
              generateEmergencyProcessLocation({ id: data.createEmergencyProcess.data })
            );
            message.success('新建应急流程成功');
          }
        },
      });
    });
  };

  useEffect(() => {
    id &&
      getEmergencyProcessDetail({
        variables: { emergencyId: id },
        onCompleted(data) {
          if (!data.emergencyProcessDetail?.success) {
            message.error(data.emergencyProcessDetail?.message);
            return;
          }
          form.setFieldsValue({
            ...data.emergencyProcessDetail.data,
          });
        },
      });
  }, [id, getEmergencyProcessDetail, form]);

  return (
    <Card
      bodyStyle={{ justifyContent: 'center', display: 'flex', padding: '0 24px' }}
      headStyle={{ borderBottom: 0 }}
      title={
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
      }
    >
      <Form
        style={{ width: 650 }}
        labelCol={{ xl: 5 }}
        wrapperCol={{ xl: 19 }}
        colon={false}
        form={form}
      >
        <Form.Item
          label="应急流程名称"
          name="name"
          rules={[
            {
              required: true,
              message: '应急流程名称必填！',
              whitespace: true,
            },
            {
              max: 50,
              message: '最多输入 50 个字符！',
            },
          ]}
        >
          <Input allowClear style={{ width: 334 }} />
        </Form.Item>
        <Form.Item
          label="适用楼栋"
          name="blockGuid"
          rules={[
            {
              required: true,
              message: '适用楼栋必选！',
            },
            {
              validator: (_, value) => {
                if (value && value.split('.').length !== 2) {
                  return Promise.reject('请选择至楼栋！');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <LocationTreeSelect
            style={{ width: 334 }}
            disabledTypes={['IDC']}
            authorizedOnly
            showSearch
            allowClear
            onChange={() => {
              form.setFieldValue('change', undefined);
            }}
          />
        </Form.Item>

        <Form.Item
          label="专业分类"
          name="categoryCode"
          rules={[
            {
              required: true,
              message: '专业分类必填！',
            },
          ]}
        >
          <MetaTypeSelect style={{ width: 334 }} metaType={MetaType.EMERGENCY_CATEGORY} />
        </Form.Item>

        <Form.Item
          label="附件"
          name="fileInfoList"
          rules={[{ required: true, message: '请选择上传文件！' }]}
          valuePropName="fileList"
          getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
            if (fileList.filter(file => file.status === 'uploading').length) {
              setLoading(true);
            } else {
              setLoading(false);
            }
            return fileList;
          }}
        >
          <McUpload
            showAccept
            accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
            showUploadList
            maxCount={5}
            maxFileSize={20}
            openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 5}
          >
            <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 5}>
              点此上传
            </Button>
          </McUpload>
        </Form.Item>
        <Form.Item label=" " colon={false} labelCol={{ span: 5 }}>
          <Button
            type="primary"
            loading={loading || createLoading || updateLoading}
            onClick={onSubmit}
          >
            提交
          </Button>
          <Button
            loading={loading || createLoading || updateLoading}
            onClick={() => history.push(EMERGENCY_PROCEE_LIST_ROUTE_PATH)}
          >
            取消
          </Button>
        </Form.Item>
      </Form>
    </Card>
  );
}
