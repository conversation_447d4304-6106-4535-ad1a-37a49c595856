import dayjs from 'dayjs';
import omit from 'lodash.omit';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import type { SpaceTreeNode } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  useDeferChangeTemplate,
  useDeleteChangeTemplate,
  useInvalidateChangeTemplate,
  useLazyChangeTemplates,
  useRevertChangeTemplateApproval,
} from '@manyun/ticket.gql.client.tickets';
import type {
  FetchChangeTemplatesData,
  FetchChangeTemplatesQ,
} from '@manyun/ticket.gql.client.tickets';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import {
  StandardChangeLibraryStatus,
  statusColorMaps,
  statusTextMaps,
} from '@manyun/ticket.model.ticket';
import {
  STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH,
  generateStandardChangeLibraryDetailLocation,
  generateStandardChangeLibraryEditLocation,
} from '@manyun/ticket.route.ticket-routes';

type SearchField = Omit<FetchChangeTemplatesQ, 'availAreaList'> & {
  availAreaList?: string | null;
  modifyTimeRange?: [Moment, Moment] | null;
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';

const sorterMaps: Record<string, string> = {
  availDate: 'AVAIL_DATE',
  effTime: 'EFF_TIME',
  gmtModified: 'GMT_CREATE',
};

export function StandardChangeLibraries() {
  const locales = useMemo(() => getRiskRegisterLocales(), []);
  const [, { checkUserId, checkCode }] = useAuthorized();
  const [form] = Form.useForm();
  const [idcs, setIdcs] = useState<SpaceTreeNode[]>([]);
  const isCreate = checkCode('element_create-standard-change-library');

  const [getTemplageList, { data, loading }] = useLazyChangeTemplates({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  const { search } = useLocation();

  const { pageNum, pageSize, ...restDefaultFields } = getLocationSearchMap<SearchField>(search, {
    parseNumbers: true,
    arrayKeys: ['changeLevelList', 'reasonList'],
  });
  const [fields, setFields] = useState<SearchField>({
    ...restDefaultFields,
    modifyTimeRange:
      restDefaultFields.gmtCreateStartTime && restDefaultFields.gmtCreateEndTime
        ? [moment(restDefaultFields.gmtCreateStartTime), moment(restDefaultFields.gmtCreateEndTime)]
        : undefined,
    pageNum: pageNum ?? 1,
    pageSize: pageSize ?? 10,
  });

  const history = useHistory();
  const [deleteChangeTemplate, { loading: deleteLoading }] = useDeleteChangeTemplate({
    onCompleted: data => {
      if (data?.deleteChangeTemplate?.success) {
        reloadData('delete');
      } else {
        message.error(data?.deleteChangeTemplate?.message);
      }
    },
  });
  const [revertChangeTemplate, { loading: revertLoading }] = useRevertChangeTemplateApproval({
    onCompleted: data => {
      if (data?.revertChangeTemplateApproval?.success) {
        reloadData();
      } else {
        message.error(data?.revertChangeTemplateApproval?.message);
      }
    },
  });
  const [deferChangeTemplate, { loading: deferLoading }] = useDeferChangeTemplate({
    onCompleted: data => {
      if (data?.deferChangeTemplate?.success) {
        message.info('延期申请已提交，请等待审批');
        reloadData();
      } else {
        message.error(data?.deferChangeTemplate?.message);
      }
    },
  });
  const [invalidateChangeTemplate, { loading: invalidateLoading }] = useInvalidateChangeTemplate({
    onCompleted: data => {
      if (data?.invalidateChangeTemplate?.success) {
        reloadData();
      } else {
        message.error(data?.invalidateChangeTemplate?.message);
      }
    },
  });

  const reloadData = useCallback(
    (action?: 'delete') => {
      if (action === 'delete') {
        setFields(pre => {
          return {
            ...pre,
            pageNum:
              (pre.pageNum - 1) * pre.pageSize + 1 === data?.changeTemplates?.total &&
              pre.pageNum > 1
                ? pre.pageNum - 1
                : pre.pageNum,
          };
        });
      } else {
        setFields(pre => ({ ...pre }));
      }
    },
    [data?.changeTemplates?.total]
  );

  useEffect(() => {
    getTemplageList({ variables: { query: { pageNum: 1, pageSize: 10, changeVersion: 2 } } });
  }, [getTemplageList]);

  useEffect(() => {
    let availAreaList: string[] | undefined | null = null;
    if (fields.availAreaList) {
      const [idc, block] = fields.availAreaList?.split('.');
      if (idc && block) {
        availAreaList = [fields.availAreaList!];
      }
      if (idc && !block) {
        const blocks = idcs.filter(item => item.value === fields.availAreaList)?.[0]?.children;
        availAreaList = blocks?.length ? blocks.map(item => item.value) : null;
      }
    }
    form.setFieldsValue(fields);
    getTemplageList({
      variables: {
        query: {
          ...omit(fields, 'modifyTimeRange'),
          changeVersion: 2,
          availAreaList: availAreaList,
          gmtCreateStartTime: fields.modifyTimeRange?.[0]
            ? moment(fields.modifyTimeRange[0].format('YYYY-MM-DD HH:mm:ss')).valueOf()
            : null,
          gmtCreateEndTime: fields.modifyTimeRange?.[1]
            ? moment(fields.modifyTimeRange[1].format('YYYY-MM-DD HH:mm:ss')).valueOf()
            : null,
        },
      },
    });
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, getTemplageList]);

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <QueryFilter
            form={form}
            items={[
              {
                label: '模版名称',
                name: 'templateName',
                control: <Input allowClear />,
              },
              {
                label: '变更等级',
                name: 'changeLevelList',
                control: (
                  <MetaTypeSelect metaType={MetaType.CHANGE_LEVEL} mode="multiple" allowClear />
                ),
              },
              {
                label: '状态',
                name: 'templateStatusList',
                control: (
                  <Select
                    options={Object.keys(statusTextMaps).map(item => ({
                      key: item,
                      value: item,
                      label: statusTextMaps[item],
                    }))}
                    mode="multiple"
                    allowClear
                  />
                ),
              },
              {
                label: '适用机房/楼',
                name: 'availAreaList',
                control: (
                  <LocationTreeSelect
                    authorizedOnly
                    onTreeDataChange={values => {
                      if (values) {
                        setIdcs(values);
                      }
                    }}
                  />
                ),
              },
              {
                label: '变更专业',
                name: 'reasonList',
                control: (
                  <MetaTypeSelect metaType={MetaType.CHANGE_REASON} mode="multiple" allowClear />
                ),
              },
              {
                label: '变更类型',
                name: 'changeTypeList',
                control: <MetaTypeSelect metaType={MetaType.CHANGE} mode="multiple" allowClear />,
              },
              {
                label: '创建时间',
                name: 'modifyTimeRange',
                span: 2,
                control: <DatePicker.RangePicker showTime format="YYYY-MM-DD HH:mm" allowClear />,
              },
            ]}
            // initialValues={fields}
            onSearch={value => {
              setLocationSearch({
                ...value,
                modifyStartTime: fields.modifyTimeRange?.[0]
                  ? moment(fields.modifyTimeRange[0]).startOf('day').valueOf()
                  : null,
                modifyEndTime: fields.modifyTimeRange?.[1]
                  ? moment(fields.modifyTimeRange[1]).startOf('day').valueOf()
                  : null,
              });
              setFields({ ...value, pageNum: 1, pageSize: fields.pageSize });
            }}
            onReset={() => {
              setLocationSearch({});
              setFields({ changeVersion: 2, pageNum: 1, pageSize: fields.pageSize });
            }}
          />
        </Card>
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            {isCreate && (
              <Button
                type="primary"
                onClick={() => history.push(STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH)}
              >
                新建标准变更模版
              </Button>
            )}

            <Table<FetchChangeTemplatesData>
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading}
              dataSource={data?.changeTemplates?.data ?? []}
              columns={[
                {
                  title: '模版名称',
                  dataIndex: 'templateName',
                  fixed: 'left',
                  disabled: true,
                  render: (_, { templateName, templateId }) => {
                    if (templateId) {
                      return (
                        <Typography.Text
                          ellipsis={{ tooltip: templateName }}
                          style={{ width: 320 }}
                        >
                          <Link
                            target="_blank"
                            to={generateStandardChangeLibraryDetailLocation({
                              id: encodeURIComponent(templateId),
                            })}
                          >
                            {templateName}
                          </Link>
                        </Typography.Text>
                      );
                    }
                    return '--';
                  },
                },
                {
                  title: '适用机房/楼',
                  dataIndex: 'availArea',
                },
                {
                  title: '变更等级',
                  dataIndex: 'riskLevel',
                  render: text =>
                    text ? <MetaTypeText code={text} metaType={MetaType.CHANGE_LEVEL} /> : '--',
                },
                {
                  title: '变更专业',
                  dataIndex: 'reason',
                  render: (_, { reason }) =>
                    reason ? (
                      <MetaTypeText code={reason} metaType={MetaType.CHANGE_REASON} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '变更类型',
                  dataIndex: 'changeType',
                  render: (_, { changeType }) =>
                    changeType ? (
                      <MetaTypeText code={changeType} metaType={MetaType.CHANGE} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '状态',
                  dataIndex: 'templateStatus',
                  render: (_, { templateStatus }) =>
                    templateStatus ? (
                      <Tag color={statusColorMaps[templateStatus]}>
                        {statusTextMaps[templateStatus]}
                      </Tag>
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '模版有效期',
                  dataIndex: 'availDate',
                  sorter: true,
                  render: availDate => (availDate ? dayjs(availDate).format('YYYY-MM-DD') : '--'),
                },
                {
                  title: '创建时间',
                  dataIndex: 'gmtCreate',
                  sorter: true,
                  render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '--'),
                },
                {
                  title: '最近生效时间',
                  dataIndex: 'effTime',
                  sorter: true,
                  render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '--'),
                },
                {
                  title: '创建人',
                  dataIndex: 'creatorName',
                },
                {
                  title: locales.operation,
                  dataIndex: '_action',
                  fixed: 'right',
                  disabled: true,
                  render: (_, record: FetchChangeTemplatesData) => {
                    const isCreater = checkUserId(record.creatorId);
                    const isModify = checkUserId(record.modifyPersonId);
                    const isDefer = checkCode('element_defer-standard-change-library');
                    // 修改
                    const isEdit = checkCode('element_edit-standard-change-library');
                    const isInvalidate = checkCode('element_invalidate-standard-change-library');

                    return (
                      <Space size={16}>
                        {isCreater &&
                          record.templateStatus === StandardChangeLibraryStatus.Draft && (
                            <Link
                              target="_blank"
                              to={generateStandardChangeLibraryEditLocation({
                                id: encodeURIComponent(record.templateId),
                              })}
                            >
                              编辑
                            </Link>
                          )}
                        {isCreater &&
                          record.templateStatus === StandardChangeLibraryStatus.Draft && (
                            <Popconfirm
                              title="确认删除此模版？删除后不可恢复"
                              style={{ width: 290 }}
                              okText="确认删除"
                              placement="topRight"
                              okButtonProps={{ disabled: deleteLoading }}
                              onConfirm={() =>
                                deleteChangeTemplate({
                                  variables: { templateId: record.templateId },
                                })
                              }
                            >
                              <Button compact type="link" disabled={loading}>
                                删除
                              </Button>
                            </Popconfirm>
                          )}
                        {isCreater &&
                          isDefer &&
                          (record.templateStatus === StandardChangeLibraryStatus.Expire ||
                            (record.templateStatus === StandardChangeLibraryStatus.Available &&
                              moment(record.availDate).diff(moment().startOf('day'), 'days') <=
                                15)) && (
                            <Button
                              compact
                              type="link"
                              disabled={loading || deferLoading}
                              onClick={() => {
                                deferChangeTemplate({
                                  variables: { templateId: record.templateId },
                                });
                              }}
                            >
                              延期
                            </Button>
                          )}
                        {isModify &&
                          (record.templateStatus === StandardChangeLibraryStatus.Approving ||
                            record.templateStatus === StandardChangeLibraryStatus.YqApproving) && (
                            <Button
                              compact
                              type="link"
                              disabled={loading || revertLoading}
                              onClick={() => {
                                revertChangeTemplate({
                                  variables: { templateId: record.templateId },
                                });
                              }}
                            >
                              撤回审批
                            </Button>
                          )}
                        {isEdit &&
                          isCreater &&
                          (record.templateStatus === StandardChangeLibraryStatus.Expire ||
                            record.templateStatus === StandardChangeLibraryStatus.Available) && (
                            <Link
                              target="_blank"
                              to={generateStandardChangeLibraryEditLocation({
                                id: encodeURIComponent(record.templateId),
                              })}
                            >
                              修改
                            </Link>
                          )}
                        {isCreater &&
                          isInvalidate &&
                          record.templateStatus === StandardChangeLibraryStatus.Available && (
                            <Popconfirm
                              title="确认失效此模版？"
                              style={{ width: 290 }}
                              okText="确认失效"
                              placement="topRight"
                              okButtonProps={{ disabled: invalidateLoading }}
                              onConfirm={() =>
                                invalidateChangeTemplate({
                                  variables: { templateId: record.templateId },
                                })
                              }
                            >
                              <Button compact type="link" disabled={loading}>
                                失效
                              </Button>
                            </Popconfirm>
                          )}
                      </Space>
                    );
                  },
                },
              ]}
              pagination={{
                total: data?.changeTemplates?.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, sorter, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                  setLocationSearch({
                    ...fields,
                    pageNum: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                }
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setFields(pre => ({
                        ...pre,
                        sortField: null,
                        sortOrder: null,
                        pageNum: 1,
                        pageSize: pre.pageSize,
                      }));
                      setLocationSearch({
                        ...fields,
                        sortField: null,
                        sortOrder: null,
                        pageNum: 1,
                        pageSize: fields.pageSize,
                      });
                      return;
                    }
                    setLocationSearch({
                      ...fields,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                      pageNum: 1,
                      pageSize: fields.pageSize,
                    });
                    setFields(pre => ({
                      ...pre,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                      pageNum: 1,
                      pageSize: pre.pageSize,
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
      </Space>
    </>
  );
}
