import React from 'react';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import { UserLink } from '@manyun/auth-hub.ui.user-link';

export type InformationTechnologyServiceDetailProps = {
  taskNo: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  basicInfo: any;
};
export function InformationTechnologyServiceDetail({
  taskNo,
  basicInfo,
}: InformationTechnologyServiceDetailProps) {
  const { taskProperties, assigneeList } = basicInfo;
  const detail = JSON.parse(taskProperties ?? '');
  return (
    <Descriptions column={4}>
      <Descriptions.Item label="指派人">
        {Array.isArray(assigneeList) && assigneeList.length > 0 ? (
          <Space size={0} split={<Divider type="vertical" />}>
            {assigneeList.map(assignee => {
              return (
                <UserLink
                  key={assignee.id}
                  external
                  userId={assignee.id}
                  userName={assignee.userName}
                />
              );
            })}
          </Space>
        ) : (
          '--'
        )}
      </Descriptions.Item>
      <Descriptions.Item label="申请人联系方式">{detail?.phone ?? '--'}</Descriptions.Item>
      <Descriptions.Item label="说明">{detail?.desc ?? '--'}</Descriptions.Item>
    </Descriptions>
  );
}
