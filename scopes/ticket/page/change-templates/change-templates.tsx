import dayjs from 'dayjs';
import { omit } from 'lodash';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  useEffectChangeTemplate,
  useExpireChangeTemplate,
  useLazyChangeOnlineTemplates,
  useRevertChangeTemplateApproval,
} from '@manyun/ticket.gql.client.tickets';
import type {
  ChangeOnlineTemplatesData,
  ChangeOnlineTemplatesQ,
} from '@manyun/ticket.gql.client.tickets';
import { CHANGE_TEMPLATE_STATUS_TEXT_MAP, ChangeTemplateState } from '@manyun/ticket.model.change';
import {
  CHANGE_TEMPLATE_CREATE_ROUTE_PATH,
  generateChangeOnlineTemplateEditLocation,
  generateChangeOnlineTemplateLocation,
  generateChangeTemplateCopyLocation,
} from '@manyun/ticket.route.ticket-routes';

import { DeleteButton } from './components/delete-btn';

type SearchField = Omit<ChangeOnlineTemplatesQ, 'pageNum' | 'pageSize'> & {
  createTime?: [Moment, Moment] | null;
  effTime?: [Moment, Moment] | null;
  availAreaList?: string | null;
  gmtCreateStartTime?: Moment | null;
  gmtCreateEndTime?: Moment | null;
  effStartTime?: Moment | null;
  effEndTime?: Moment | null;
  pageNum: number;
  pageSize: number;
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';

const sorterMaps: Record<string, string> = {
  gmtCreate: 'GMT_CREATE',
  effTime: 'EFF_TIME',
};

export function ChangeTemplates() {
  const [, { checkCode, checkUserId }] = useAuthorized();
  const isOperation = checkCode('element_ticket_change-template-operation');

  const [form] = Form.useForm();

  const [getList, { data, loading }] = useLazyChangeOnlineTemplates({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  const [revertChangeTemplate, { loading: revertLoading }] = useRevertChangeTemplateApproval({
    onCompleted(data) {
      if (!data.revertChangeTemplateApproval?.success) {
        message.error(data.revertChangeTemplateApproval?.message);
        return;
      }
      reloadData();
    },
  });
  const [expireChangeTemplate, { loading: expireLoading }] = useExpireChangeTemplate({
    onCompleted(data) {
      if (!data.expireChangeTemplate?.success) {
        message.error(data.expireChangeTemplate?.message);
        return;
      }
      reloadData();
    },
  });
  const [effectChangeTemplate, { loading: effectLoading }] = useEffectChangeTemplate({
    onCompleted(data) {
      if (!data.effectChangeTemplate?.success) {
        message.error(data.effectChangeTemplate?.message);
        return;
      }
      reloadData();
    },
  });

  const { search } = useLocation();
  const {
    pageNum,
    pageSize,
    gmtCreateStartTime,
    gmtCreateEndTime,
    effStartTime,
    effEndTime,
    ...restDefaultFields
  } = getLocationSearchMap<SearchField>(search, {
    // parseNumbers: true,
    arrayKeys: [
      'changeLevelList',
      'reasonList',
      'templateStatusList',
      'availAreaList',
      'changeCategoryList',
    ],
  });
  const [fields, setFields] = useState<SearchField>({
    ...restDefaultFields,
    createTime:
      gmtCreateStartTime && gmtCreateEndTime
        ? [moment(Number(gmtCreateStartTime)), moment(Number(gmtCreateEndTime))]
        : undefined,

    effTime:
      effStartTime && effEndTime
        ? [moment(Number(effStartTime)), moment(Number(effEndTime))]
        : undefined,
    pageNum: pageNum ? Number(pageNum) : 1,
    pageSize: pageSize ? Number(pageSize) : 10,
  });

  const history = useHistory();

  useEffect(() => {
    getList({ variables: { query: { pageNum: 1, pageSize: 10 } } });
  }, [getList]);

  useEffect(() => {
    getList({
      variables: {
        query: {
          ...getParams(),
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, getList]);

  const getParams = () => {
    const { effTime, createTime, ...resp } = fields;
    form.setFieldsValue(fields);

    const baseQ: ChangeOnlineTemplatesQ = {
      ...resp,
      gmtCreateStartTime: createTime?.length
        ? (moment(createTime[0].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      gmtCreateEndTime: createTime?.length
        ? moment(createTime[1].format('YYYY-MM-DD HH:mm:ss')).valueOf()
        : null,
      effStartTime: effTime?.length
        ? (moment(effTime[0].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      effEndTime: effTime?.length
        ? (moment(effTime[1].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      // idcTagList: idcTag ? [idcTag] : null,
      // blockGuidList: blockTag ? [location!] : null,
    };
    setLocationSearch({ ...omit(baseQ, 'idcTagList', 'blockGuidList', 'sortField', 'sortOrder') });
    return baseQ;
  };

  const reloadData = useCallback(
    (action?: 'delete') => {
      if (action === 'delete') {
        setFields(pre => {
          return {
            ...pre,
            pageNum:
              (pre.pageNum - 1) * pre.pageSize + 1 === data?.changeOnlineTemplates.total &&
              pre.pageNum > 1
                ? pre.pageNum - 1
                : pre.pageNum,
          };
        });
      } else {
        setFields(pre => ({ ...pre }));
      }
    },
    [data?.changeOnlineTemplates.total]
  );

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <QueryFilter
            form={form}
            items={[
              {
                label: '变更模板',
                name: 'templateName',
                control: <Input allowClear />,
              },
              {
                label: '变更专业',
                name: 'reasonList',
                control: (
                  <MetaTypeSelect
                    mode="multiple"
                    metaType={MetaType.CHANGE_ONLINE_REASON}
                    allowClear
                    showArrow
                  />
                ),
              },
              {
                label: '变更等级',
                name: 'changeLevelList',
                control: (
                  <MetaTypeSelect
                    mode="multiple"
                    metaType={MetaType.CHANGE_ONLINE_LEVEL}
                    allowClear
                    showArrow
                  />
                ),
              },
              {
                label: '状态',
                name: 'templateStatusList',
                control: (
                  <Select
                    mode="multiple"
                    options={Object.keys(ChangeTemplateState).map(item => ({
                      label: CHANGE_TEMPLATE_STATUS_TEXT_MAP[ChangeTemplateState[item]],
                      value: item,
                    }))}
                    showArrow
                  />
                ),
              },
              {
                label: '适用范围',
                name: 'availAreaList',
                control: <LocationTreeSelect authorizedOnly allowClear multiple showArrow />,
              },

              {
                label: '变更类别',
                name: 'changeCategoryList',
                control: (
                  <MetaTypeSelect
                    mode="multiple"
                    metaType={MetaType.CHANGE_ONLINE_CATEGORY}
                    allowClear
                    showArrow
                  />
                ),
              },
              {
                label: '模板ID',
                name: 'templateId',
                control: <Input allowClear />,
              },
              {
                label: '创建人',
                name: 'creatorId',
                control: <UserSelect allowClear labelInValue={false} />,
              },

              {
                label: '创建时间',
                name: 'createTime',
                control: (
                  <DatePicker.RangePicker
                    showTime={{ format: 'HH:mm:ss' }}
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
                span: 2,
              },
              {
                label: '生效时间',
                name: 'effTime',
                control: (
                  <DatePicker.RangePicker
                    showTime={{ format: 'HH:mm:ss' }}
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
                span: 2,
              },
            ]}
            // initialValues={fields}
            onSearch={value => {
              setFields({ ...value, pageNum: 1, pageSize: fields.pageSize });
            }}
            onReset={() => {
              form.setFieldsValue({ pageNum: 1, pageSize: fields.pageSize });
              setFields({ pageNum: 1, pageSize: fields.pageSize });
            }}
          />
        </Card>
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            {isOperation ? (
              <Button
                type="primary"
                onClick={() => history.push(CHANGE_TEMPLATE_CREATE_ROUTE_PATH)}
              >
                新建模板
              </Button>
            ) : (
              <span />
            )}

            <Table<ChangeOnlineTemplatesData>
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading || effectLoading || expireLoading || revertLoading}
              dataSource={data?.changeOnlineTemplates?.data ?? []}
              columns={[
                {
                  title: '模板ID',
                  dataIndex: 'templateId',
                  fixed: 'left',
                  disabled: true,
                  render: (text: string) => (
                    <Link
                      key={text}
                      target="_blank"
                      to={generateChangeOnlineTemplateLocation({
                        id: text,
                      })}
                    >
                      {text}
                    </Link>
                  ),
                },
                {
                  title: '模板名称',
                  dataIndex: 'templateName',
                  render: templateName => {
                    return (
                      <Typography.Text style={{ width: 480 }} ellipsis={{ tooltip: templateName }}>
                        {templateName}
                      </Typography.Text>
                    );
                  },
                },
                {
                  title: '适用范围',
                  dataIndex: 'availArea',
                  render: availArea => availArea?.split(',').join(' | '),
                },
                {
                  title: '变更专业',
                  dataIndex: 'reason',
                  render: reason => (
                    <MetaTypeText metaType={MetaType.CHANGE_ONLINE_REASON} code={reason} />
                  ),
                },
                {
                  title: '变更类别',
                  dataIndex: 'changeCategory',
                  render: changeCategory => (
                    <MetaTypeText
                      metaType={MetaType.CHANGE_ONLINE_CATEGORY}
                      code={changeCategory}
                    />
                  ),
                },
                {
                  title: '变更等级',
                  dataIndex: 'riskLevel',
                  render: riskLevel => (
                    <MetaTypeText metaType={MetaType.CHANGE_ONLINE_LEVEL} code={riskLevel} />
                  ),
                },
                {
                  title: '创建时间',
                  dataIndex: 'gmtCreate',
                  sorter: true,
                  render: (text: string) => {
                    return <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>;
                  },
                },

                {
                  title: '生效时间',
                  dataIndex: 'effTime',
                  sorter: true,
                  render: (text: string) => {
                    return text ? <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span> : '--';
                  },
                },
                {
                  title: '状态',
                  dataIndex: 'templateStatus',
                  render: templateStatus => {
                    let statusBadge = <Badge status="default" />;

                    if (templateStatus === ChangeTemplateState.Approving) {
                      statusBadge = <Badge color="blue" />;
                    }
                    if (templateStatus === ChangeTemplateState.Available) {
                      statusBadge = <Badge status="success" />;
                    }
                    if (templateStatus === ChangeTemplateState.Expire) {
                      statusBadge = <Badge status="error" />;
                    }
                    return (
                      <>
                        {statusBadge} {CHANGE_TEMPLATE_STATUS_TEXT_MAP[templateStatus]}
                      </>
                    );
                  },
                },
                {
                  title: '创建人',
                  dataIndex: 'createUserName',
                  render: (_, { creatorName, creatorId }) => (
                    <UserLink userId={creatorId} userName={creatorName} external />
                  ),
                },
                {
                  title: '操作',
                  dataIndex: 'operation',
                  disabled: true,
                  fixed: 'right',
                  render: (_, { templateId, templateStatus, creatorId }) => {
                    const isCreater = checkUserId(creatorId);
                    const deleteBtn = (
                      <DeleteButton
                        changeTemplateId={templateId}
                        onSuccess={() => {
                          reloadData('delete');
                        }}
                      />
                    );
                    const changeView = (
                      <Popconfirm
                        title="确认修改该模板？修改后需重新审批，请谨慎操作！"
                        overlayStyle={{ width: 290 }}
                        okText="确认修改"
                        trigger="click"
                        onConfirm={() => {
                          history.push(
                            generateChangeOnlineTemplateEditLocation({ id: templateId })
                          );
                        }}
                      >
                        <Button type="link" compact>
                          修改
                        </Button>
                      </Popconfirm>
                    );
                    const copy = (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          history.push(generateChangeTemplateCopyLocation({ id: templateId }));
                        }}
                      >
                        复制
                      </Button>
                    );
                    const efficacy = (
                      <Popconfirm
                        title="确认失效该模板？失效后不可使用，请谨慎操作！"
                        overlayStyle={{ width: 290 }}
                        okText="确认失效"
                        trigger="click"
                        onConfirm={() => {
                          expireChangeTemplate({ variables: { templateId } });
                        }}
                      >
                        <Button type="link" compact>
                          失效
                        </Button>
                      </Popconfirm>
                    );
                    const effect = (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          effectChangeTemplate({ variables: { templateId } });
                        }}
                      >
                        生效
                      </Button>
                    );
                    if (templateStatus === ChangeTemplateState.Approving && isCreater) {
                      return (
                        <Button
                          type="link"
                          compact
                          onClick={() => {
                            revertChangeTemplate({ variables: { templateId } });
                          }}
                        >
                          撤回
                        </Button>
                      );
                    }
                    if (templateStatus === ChangeTemplateState.Draft) {
                      return (
                        <Space>
                          {isCreater && (
                            <Button
                              type="link"
                              compact
                              onClick={() => {
                                history.push(
                                  generateChangeOnlineTemplateEditLocation({ id: templateId })
                                );
                              }}
                            >
                              编辑
                            </Button>
                          )}
                          {(isOperation || isCreater) && deleteBtn}
                        </Space>
                      );
                    }
                    if (templateStatus === ChangeTemplateState.Available) {
                      return (
                        <Space>
                          {(isCreater || isOperation) && changeView}
                          {isOperation && copy}
                          {(isCreater || isOperation) && efficacy}
                        </Space>
                      );
                    }
                    if (templateStatus === ChangeTemplateState.Expire) {
                      return (
                        <Space>
                          {(isCreater || isOperation) && effect}
                          {(isCreater || isOperation) && changeView}

                          {isOperation && (isOperation || isCreater) ? (
                            <Dropdown.Button
                              type="link"
                              menu={{
                                items: [
                                  {
                                    key: 'copy',
                                    label: copy,
                                  },
                                  {
                                    key: 'deleteBtn',
                                    label: deleteBtn,
                                  },
                                ],
                              }}
                              buttonsRender={([leftButton, rightButton]) => [null, rightButton]}
                              onClick={e => e.stopPropagation()}
                            />
                          ) : (
                            <>
                              {' '}
                              {isOperation && copy}
                              {(isOperation || isCreater) && deleteBtn}
                            </>
                          )}
                        </Space>
                      );
                    }
                    return '--';
                  },
                },
              ]}
              pagination={{
                total: data?.changeOnlineTemplates?.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, sorter, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                  setLocationSearch({
                    ...fields,
                    pageNum: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                }
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setFields(pre => ({
                        ...pre,
                        sortField: null,
                        sortOrder: null,
                      }));
                      setLocationSearch({
                        ...fields,
                        sortField: null,
                        sortOrder: null,
                      });
                      return;
                    }
                    // setLocationSearch({
                    //   ...fields,
                    //   sortField: sorterMaps[sorter.field as string],
                    //   sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                    // });
                    setFields(pre => ({
                      ...pre,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
      </Space>
    </>
  );
}
