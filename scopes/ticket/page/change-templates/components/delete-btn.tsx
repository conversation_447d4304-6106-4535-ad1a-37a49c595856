import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { useDeleteChangeTemplate } from '@manyun/ticket.gql.client.tickets';

export type CloseRiskFullButtonProps = {
  changeTemplateId: string;

  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function DeleteButton({
  changeTemplateId,

  onSuccess,
}: CloseRiskFullButtonProps) {
  const [unClosed, setUnClosed] = useState('');

  const [deleteChangeTemplate, { loading }] = useDeleteChangeTemplate();

  return (
    <>
      {unClosed ? (
        <Popconfirm
          title="该模板目前被草稿状态的变更单关联，暂不支持删除"
          overlayStyle={{ width: 290 }}
          okText="我知道了"
          showCancel={false}
          open
          onConfirm={() => {
            setUnClosed('');
          }}
        >
          <Button type="link" loading={loading} compact>
            删除
          </Button>
        </Popconfirm>
      ) : (
        <Popconfirm
          title="确认删除该模板？删除后不可恢复，请谨慎操作！"
          overlayStyle={{ width: 290 }}
          okText="确认删除"
          trigger="click"
          okButtonProps={{ loading }}
          onConfirm={() => {
            deleteChangeTemplate({
              variables: {
                templateId: changeTemplateId,
              },
              onCompleted(data) {
                if (data?.deleteChangeTemplate?.code === 'CHANGE_TEMPLATE_CAN_NOT_DELETE') {
                  setUnClosed('CHANGE_TEMPLATE_CAN_NOT_DELETE');
                  return;
                }
                onSuccess();
              },
            });
          }}
        >
          <Button type="link" compact loading={loading}>
            删除
          </Button>
        </Popconfirm>
      )}
    </>
  );
}
