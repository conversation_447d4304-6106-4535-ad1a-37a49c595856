/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState } from 'react';

import { omit, uniqBy } from 'lodash';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form, type FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { CONFIG_RANGE_KEY_MAP, ConfigRangeType, PlanType } from '@manyun/ticket.model.task';
import { fetchInspectConfigs } from '@manyun/ticket.service.fetch-inspect-configs';
import { fetchInventoryConfigs } from '@manyun/ticket.service.fetch-inventory-configs';
import { fetchMaintenanceListConfigs } from '@manyun/ticket.service.fetch-maintenance-configs';
import { TaskConfigurationSelect } from '@manyun/ticket.ui.task-configuration-select';
import { TicketTypeText } from '@manyun/ticket.ui.ticket-type-text';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

export type TaskConfigurationModalProps = {
  planType: PlanType;
  subType: string;
  value?: any;
  blockGuid: string;
  onChange?: (value: any) => void;
};

export const TaskConfigurationModal = React.forwardRef(
  (
    { planType, subType, value, blockGuid, onChange }: TaskConfigurationModalProps,
    ref?: React.Ref<any>
  ) => {
    const [form] = Form.useForm();
    const [visible, setVisible] = useState(false);
    const [radioValue, setRadioValue] = useState<ConfigRangeType>(ConfigRangeType.Block);
    const [dataSource, setDataSource] = useState<any[]>([]);
    const [total, setTotal] = useState(0);
    const [queryParams, setQueryParams] = useState<any>();
    const [pagination, setPagination] = useState<{ pageNum: number; pageSize: number }>({
      pageNum: 1,
      pageSize: 10,
    });
    const [loading, setLoading] = useState(false);
    const [selectedIds, setSelectedIds] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<any[]>([]);
    const [selectedGenuralIds, setSelectedGenuralIds] = useState<React.Key[]>([]);
    const [selectedGenuralRows, setSelectedGenuralRows] = useState<any[]>([]);
    const inspectSubject = Form.useWatch('inspectSubject', form);

    const [dataSourceForShowTable, setDataSourceForShowTable] = useState<any[]>(value ?? []);

    useEffect(() => {
      form.resetFields();
      setPagination({ pageNum: 1, pageSize: 10 });
      if (visible) {
        form.setFieldsValue({ type: subType });

        (async function () {
          setLoading(true);
          switch (planType) {
            case PlanType.MaintenancePlan:
              const { data: mData, error: mError } = await fetchMaintenanceListConfigs({
                maintenanceType: subType,
                effectType: radioValue,
                effectDomain:
                  radioValue === ConfigRangeType.Block ? blockGuid : ConfigRangeType.All,
              });

              if (mError) {
                message.error(mError.message);
                return;
              }
              setLoading(false);

              setDataSource(mData.data);
              setTotal(mData.total);
              break;
            case PlanType.InspectionPlan:
              const { data: iData, error: iError } = await fetchInspectConfigs({
                pageNum: 1,
                pageSize: 10,
                inspectType: subType,
                effectType: radioValue,
                effectDomainList: radioValue === ConfigRangeType.Block ? [blockGuid] : undefined,
              });

              if (iError) {
                message.error(iError.message);
                return;
              }
              setLoading(false);

              setDataSource(iData.data);
              setTotal(iData.total);

              break;
            case PlanType.InventoryPlan:
              const { data, error } = await fetchInventoryConfigs({
                pageNum: 1,
                pageSize: 10,
                inventoryType: subType,
                configType: radioValue,
                blockGuids: radioValue === ConfigRangeType.Block ? [blockGuid] : undefined,
              });

              if (error) {
                message.error(error.message);
                return;
              }
              setLoading(false);
              setDataSource(data.data);
              setTotal(data.total);

              break;
          }
        })();
      }
    }, [blockGuid, form, planType, radioValue, subType, visible]);

    useEffect(() => {
      setDataSourceForShowTable(value ?? []);
      setSelectedRows(
        value?.filter(
          (item: { itemType: ConfigRangeType }) => item.itemType === ConfigRangeType.Block
        ) ?? []
      );
      setSelectedIds(
        value
          ?.filter((item: { itemType: ConfigRangeType }) => item.itemType === ConfigRangeType.Block)
          ?.map((item: { id: number }) => item.id) ?? []
      );

      setSelectedGenuralRows(
        value?.filter(
          (item: { itemType: ConfigRangeType }) => item.itemType !== ConfigRangeType.Block
        ) ?? []
      );
      setSelectedGenuralIds(
        value
          ?.filter((item: { itemType: ConfigRangeType }) => item.itemType !== ConfigRangeType.Block)
          ?.map((item: { id: number }) => item.id) ?? []
      );
    }, [value, visible]);

    const getConfigType = async ({
      configType,
      params,
    }: {
      configType: ConfigRangeType;
      params?: any;
    }) => {
      setLoading(true);

      switch (planType) {
        case PlanType.MaintenancePlan:
          const { data: mData, error: mError } = await fetchMaintenanceListConfigs({
            ...omit(params, 'type'),
            pageNum: 1,
            pageSize: 10,
            maintenanceType: subType,
            effectType: configType,
            effectDomain: configType === ConfigRangeType.Block ? blockGuid : ConfigRangeType.All,
          });

          if (mError) {
            message.error(mError.message);
            return;
          }
          setLoading(false);

          setDataSource(mData.data);
          setTotal(mData.total);
          break;
        case PlanType.InspectionPlan:
          const { pageNum, pageSize, ...rest } = params;
          const { data: iData, error: iError } = await fetchInspectConfigs({
            ...omit(rest, 'type'),
            pageNum,
            pageSize,
            inspectType: subType,
            effectType: configType,
            effectDomainList: configType === ConfigRangeType.Block ? [blockGuid] : undefined,
          });

          if (iError) {
            message.error(iError.message);
            return;
          }
          setLoading(false);

          setDataSource(iData.data);
          setTotal(iData.total);

          break;
        case PlanType.InventoryPlan:
          const { data, error } = await fetchInventoryConfigs({
            ...omit(params, 'type'),

            inventoryType: subType,
            configType: configType,
            blockGuids: configType === ConfigRangeType.Block ? [blockGuid] : undefined,
          });

          if (error) {
            message.error(error.message);
            return;
          }
          setLoading(false);

          setDataSource(data.data);
          setTotal(data.total);
          break;
      }
    };
    const onSearch = (value: any) => {
      setQueryParams(value);
      switch (planType) {
        case PlanType.MaintenancePlan:
          getConfigType({ configType: radioValue, params: value });
          break;
        case PlanType.InspectionPlan:
          setPagination({ pageSize: 10, pageNum: 1 });
          getConfigType({ configType: radioValue, params: { ...value, pageNum: 1, pageSize: 10 } });
          break;
        case PlanType.InventoryPlan:
          getConfigType({ configType: radioValue, params: {} });
          break;
        default:
          return;
      }
    };

    const onReset = () => {
      setSelectedGenuralIds([]);
      setSelectedIds([]);
      form.setFieldsValue({
        type: subType,
      });
      setPagination({ pageSize: 10, pageNum: 1 });
      setQueryParams({});
      getConfigType({ configType: radioValue, params: {} });
    };
    const selectedValidRows = [...selectedRows, ...selectedGenuralRows].filter(
      item => Object.keys(item).length > 1
    );

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <Button
          type="primary"
          onClick={() => {
            if (!subType) {
              message.error(`请先选择${generateSpecificDataByTaskType(planType).columnTitle}`);
              return;
            }
            if (!blockGuid) {
              message.error('请先选择楼栋');
              return;
            }
            setVisible(true);
          }}
        >{`选择${generateSpecificDataByTaskType(planType).prefix}`}</Button>
        <Modal
          title={`选择${generateSpecificDataByTaskType(planType).prefix}`}
          visible={visible}
          width={1200}
          okButtonProps={{
            disabled: selectedValidRows.length === 0,
          }}
          onCancel={() => {
            form.resetFields();

            setVisible(false);
          }}
          onOk={() => {
            setDataSourceForShowTable(selectedValidRows);
            onChange && onChange(selectedValidRows);

            setVisible(false);
          }}
        >
          <Space direction="vertical" style={{ width: '100%' }}>
            <Radio.Group
              value={radioValue}
              onChange={({ target }) => {
                setRadioValue(target.value);
                setPagination({ pageNum: 1, pageSize: 10 });
                getConfigType({
                  configType: target.value,
                  params: { ...queryParams, pageNum: 1, pageSize: 10 },
                });
              }}
            >
              {CONFIG_RANGE_KEY_MAP.map(({ label, value }) => {
                return (
                  <Radio.Button key={value} value={value}>
                    {label}
                  </Radio.Button>
                );
              })}
            </Radio.Group>
            <QueryFilter
              form={form}
              items={generateQueryFilterItems(planType, inspectSubject, form)}
              onSearch={onSearch}
              onReset={onReset}
            />
            <Table<any>
              ref={ref}
              rowSelection={{
                preserveSelectedRowKeys: true,
                selectedRowKeys:
                  radioValue === ConfigRangeType.Block ? selectedIds : selectedGenuralIds,
                onChange: (selectedRowKeys, tableSelectedRows) => {
                  const compareAllTypeIds =
                    radioValue === ConfigRangeType.Block
                      ? [...selectedGenuralIds, ...selectedRowKeys]
                      : [...selectedIds, ...selectedRowKeys];

                  let compareTableSelectedRows =
                    radioValue === ConfigRangeType.Block
                      ? [...tableSelectedRows, ...selectedRows]
                      : [...tableSelectedRows, ...selectedGenuralRows];
                  compareTableSelectedRows = uniqBy(
                    compareTableSelectedRows.filter(row => compareAllTypeIds.includes(row?.id)),
                    'id'
                  );

                  const compareAllTypeRows =
                    radioValue === ConfigRangeType.Block
                      ? [...compareTableSelectedRows, ...selectedGenuralRows]
                      : [...compareTableSelectedRows, ...selectedRows];

                  if (compareAllTypeRows.length > 0 && hasSameSub(compareAllTypeRows, planType)) {
                    message.error('任务子类型请勿重复添加！');
                    return;
                  }
                  if (radioValue === ConfigRangeType.Block) {
                    setSelectedIds(selectedRowKeys);
                    setSelectedRows(
                      compareTableSelectedRows.map(row => ({ ...row, itemType: radioValue }))
                    );
                  } else {
                    setSelectedGenuralIds(selectedRowKeys);
                    setSelectedGenuralRows(
                      compareTableSelectedRows.map(row => ({ ...row, itemType: radioValue }))
                    );
                  }
                },
              }}
              style={{ width: '100%' }}
              rowKey="id"
              pagination={
                planType === PlanType.MaintenancePlan
                  ? {
                      total: total,
                    }
                  : {
                      total: total,

                      pageSize: pagination.pageSize,
                      current: pagination.pageNum,
                      onChange: (current, size) => {
                        setPagination({ pageNum: current, pageSize: size });
                        getConfigType({
                          configType: radioValue,
                          params: { ...queryParams, pageNum: current, pageSize: size },
                        });
                      },
                    }
              }
              loading={loading}
              dataSource={dataSource}
              scroll={{ x: 'max-content' }}
              columns={generateTableColumns(radioValue, blockGuid, planType, false)}
            />
          </Space>
        </Modal>
        {dataSourceForShowTable.length > 0 && (
          <div style={{ width: 1100 }}>
            <Table<any>
              style={{ width: '100%' }}
              dataSource={dataSourceForShowTable}
              columns={[
                ...generateTableColumns(radioValue, blockGuid, planType, true),
                {
                  title: '操作',
                  render: (_, record) => {
                    return (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          const newDatas = dataSourceForShowTable.filter(
                            item => item.id !== record.id
                          );
                          const newIds = selectedIds.filter(id => id !== record.id);
                          setSelectedRows(newDatas);
                          setSelectedIds(newIds);
                          setDataSourceForShowTable(newDatas);
                          onChange && onChange(newDatas);
                        }}
                      >
                        删除
                      </Button>
                    );
                  },
                },
              ]}
            />
          </div>
        )}
      </Space>
    );
  }
);

function generateTableColumns(
  radioValue: ConfigRangeType,
  blockGuid: string,
  planType: PlanType,
  isShow: boolean
) {
  const columns: ColumnType<any>[] = [
    { title: `${generateSpecificDataByTaskType(planType).prefix}名称`, dataIndex: 'configName' },
  ];

  switch (planType) {
    case PlanType.InspectionPlan:
      columns.push({
        title: 'SOP类型',
        dataIndex: 'inspectType',
        render: text => (
          <TicketTypeText code={`${generateSpecificDataByTaskType(planType).ticketType}${text}`} />
        ),
      });
      columns.push({
        title: '巡检对象类型',
        dataIndex: 'subTypeCode',
        render: (text: string, record: { inspectSubject: string }) => {
          return record?.inspectSubject === 'DEVICE' ? (
            <DeviceTypeText code={text} />
          ) : (
            <RoomTypeText code={text} />
          );
        },
      });

      break;
    case PlanType.InventoryPlan:
      columns.push({
        title: 'SOP类型',
        dataIndex: ['inventoryType', 'desc'],
      });
      columns.push({
        title: '资产分类',
        dataIndex: 'deviceTypes',
        width: 336,
        render: (text: string[]) => {
          return (
            <Popover
              content={
                <Space
                  size={0}
                  style={{ width: text.length > 3 ? 600 : 336 }}
                  split={<Divider type="vertical" spaceSize="mini" emphasis />}
                  wrap
                >
                  {text.map(item => (
                    <DeviceTypeText key={item} code={item} />
                  ))}
                </Space>
              }
            >
              <Typography.Text ellipsis style={{ width: 336 }}>
                {text.map((item, index) => (
                  <Typography.Text key={item}>
                    <DeviceTypeText code={item} />
                    {index !== text.length - 1 && <Divider type="vertical" />}
                  </Typography.Text>
                ))}
              </Typography.Text>
            </Popover>
          );
        },
      });

      break;
    case PlanType.MaintenancePlan:
      columns.push({
        title: '所属专业',
        dataIndex: 'maintenanceType',
        render: text => (
          <TicketTypeText code={`${generateSpecificDataByTaskType(planType).ticketType}${text}`} />
        ),
      });
      columns.push({
        title: '资产分类',
        dataIndex: 'deviceType',
        render: (text: string) => {
          return <DeviceTypeText code={text} />;
        },
      });
      break;
  }

  return isShow
    ? [
        ...columns,
        {
          title: '适用范围',
          dataIndex: 'itemType',
          render: (text: ConfigRangeType) => (text === 'ALL' ? '通用' : blockGuid),
        },
      ]
    : columns;
}

function generateQueryFilterItems(
  planType: PlanType,
  inspectSubject: string,
  form: FormInstance<any>
) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return [
        {
          label: `${generateSpecificDataByTaskType(planType).prefix}名称`,
          name: 'configName',

          control: <Input />,
        },

        {
          label: generateSpecificDataByTaskType(planType).columnTitle,
          name: 'type',

          control: (
            <TaskConfigurationSelect
              ticketPlanType={generateSpecificDataByTaskType(planType).ticketType}
              disabled
            />
          ),
        },
        {
          label: '资产分类',
          name: 'deviceType',

          control: (
            <DeviceTypeCascader
              numbered
              dataType={['snDevice']}
              disabledTypeList={['C0', 'C1']}
              placeholder="请选择设备类型"
              allowClear
            />
          ),
        },
      ];
    case PlanType.InspectionPlan:
      return [
        {
          label: `${generateSpecificDataByTaskType(planType).prefix}类型`,
          name: 'type',

          control: (
            <TaskConfigurationSelect
              ticketPlanType={generateSpecificDataByTaskType(planType).ticketType}
              disabled
            />
          ),
        },
        {
          label: '巡检对象',
          name: 'inspectSubject',

          control: (
            <Select
              options={[
                { label: '设备', value: 'DEVICE' },
                { label: '空间', value: 'ENVIRONMENT' },
              ]}
              onChange={() => {
                form.setFieldsValue({ subTypeCode: undefined });
              }}
            />
          ),
        },

        {
          label: '巡检对象类型',
          name: 'subTypeCode',

          control:
            inspectSubject === 'DEVICE' ? (
              <DeviceTypeCascader
                numbered
                dataType={['snDevice']}
                disabledTypeList={['C0', 'C1']}
                placeholder="请选择设备类型"
                allowClear
                disabled={!inspectSubject}
              />
            ) : (
              <RoomTypeSelect allowClear disabled={!inspectSubject} />
            ),
        },
      ];
    case PlanType.InventoryPlan:
      return [
        {
          label: `${generateSpecificDataByTaskType(planType).prefix}类型`,
          name: 'type',

          control: (
            <TaskConfigurationSelect
              ticketPlanType={generateSpecificDataByTaskType(planType).ticketType}
              disabled
            />
          ),
        },
      ];
    default:
      return [];
  }
}
function hasSameSub(rows: any, planType: PlanType) {
  let isSame = false;
  switch (planType) {
    case PlanType.MaintenancePlan:
      if (rows.length > 0) {
        const compareDeviceTypeCodes = [rows[0].deviceType];
        for (let i = 1; i < rows.length; i++) {
          const deviceTypeCode = rows[i].deviceType;
          isSame = compareDeviceTypeCodes.some(code => deviceTypeCode === code);
          if (isSame) {
            break;
          } else {
            compareDeviceTypeCodes.push(deviceTypeCode);
          }
        }
      }
      return isSame;

    case PlanType.InspectionPlan:
      if (rows.length > 0) {
        const compareSubJobTypeCodes = [rows[0].subTypeCode];
        for (let i = 1; i < rows.length; i++) {
          const subJobTypeCode = rows[i].subTypeCode;
          isSame = compareSubJobTypeCodes.some(code => subJobTypeCode === code);
          if (isSame) {
            break;
          } else {
            compareSubJobTypeCodes.push(subJobTypeCode);
          }
        }
      }

      return isSame;
    case PlanType.InventoryPlan:
      let compareSubJobTypeCodes = rows[0].deviceTypes;
      for (let i = 1; i < rows.length; i++) {
        const subJobTypeCodes = rows[i].deviceTypes;
        isSame = compareSubJobTypeCodes.some((code: string) => subJobTypeCodes.includes(code));
        if (isSame) {
          break;
        } else {
          compareSubJobTypeCodes = [...compareSubJobTypeCodes, ...subJobTypeCodes];
        }
      }
      return isSame;
    default:
      return isSame;
  }
}
TaskConfigurationModal.displayName = 'TaskConfigurationModal';
