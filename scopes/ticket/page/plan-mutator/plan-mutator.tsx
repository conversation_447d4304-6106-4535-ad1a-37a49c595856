import {
  DeleteOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import get from 'lodash.get';
import chunk from 'lodash/chunk';
import uniq from 'lodash/uniq';
import moment from 'moment';
import type { Moment } from 'moment';
import { nanoid } from 'nanoid';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';
import shallowequal from 'shallowequal';

import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import { Typography } from '@manyun/base-ui.ui.typography';
import { AttGroupSelect } from '@manyun/hrm.ui.att-group-select';
import type { DeviceType } from '@manyun/resource-hub.state.device-types';
import { getDeviceTypesAction, selectDeviceTypes } from '@manyun/resource-hub.state.device-types';
import { selectRoomTypeEntities } from '@manyun/resource-hub.state.room-type';
import type { RoomType } from '@manyun/resource-hub.state.room-type';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import type { Space as BasicSpace } from '@manyun/resource-hub.ui.location-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import type {
  BackendCreateParams,
  Cycle,
  JobItem,
  PlanBlock,
  PlanJSON,
  SpecifyTarget,
  Splitor,
  WeekType,
} from '@manyun/ticket.model.task';
import {
  CYCLES_TYPE_OPTIONS,
  ConfigRangeType,
  CyclesType,
  DAY_WORK_WEEK_OPYIONS,
  DISTRIBUTE_MODE_OPTIONS,
  DistributeMode,
  JOB_TYPE_TEXT_MAP,
  JobType,
  MAINTENANCE_TYPE_OPTIONS,
  MONTH_DAY_IN_YEAR_OPYIONS,
  MONTH_DAY_OPYIONS,
  MONTH_IN_YEAR_OPYIONS,
  MaintenancePlanType,
  MonthlyWorkDayType,
  NUMBER_OPYIONS,
  PlanStatus,
  PlanType,
  SOLIT_ROOM_FLOOR_OPTIONS,
  SlaUnit,
  SplitorType,
  THREE_DEVICE_TYPE_OPTIONS,
  ThreeDeviceType,
  WEEK_DAY_OPYIONS,
} from '@manyun/ticket.model.task';
import { InspectionObject } from '@manyun/ticket.model.ticket';
import { TaskConfigurationModal } from '@manyun/ticket.page.plan-mutator';
import { createPlan } from '@manyun/ticket.service.create-plan';
import { fetchPlanDetail } from '@manyun/ticket.service.fetch-plan-detail';
import { updatePlan } from '@manyun/ticket.service.update-plan';
import { selectTicketTypeEntities } from '@manyun/ticket.state.ticket-type';
import type { TicketType } from '@manyun/ticket.state.ticket-type';
import { MaintenanceCycleSelect } from '@manyun/ticket.ui.maintenance-cycle-select';
import { SlaSelect, generateCorrectJobSla } from '@manyun/ticket.ui.sla-select';
import { TaskConfigurationSelect } from '@manyun/ticket.ui.task-configuration-select';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};
export type PlanMutatorProps = {
  planType: PlanType;
  mode: 'edit' | 'create' | 'copy';
};

export function PlanMutator({ planType, mode }: PlanMutatorProps) {
  const [form] = Form.useForm();
  const chosenDeviceRef = useRef(new Map());
  const dispatch = useDispatch();
  const { entities } = useSelector(selectDeviceTypes, (left, right) =>
    shallowequal(left.entities, right.entities)
  );
  const { entities: ticketTypeEntities } = useSelector(selectTicketTypeEntities);
  const roomTypeEntities = useSelector(selectRoomTypeEntities, (left, right) =>
    shallowequal(left, right)
  );
  function updateChosenDeviceMap(deviceValues: { flagValue?: Record<string, string[]> }[]) {
    chosenDeviceRef.current.clear();
    const flagValues = deviceValues.map(item => item?.flagValue);
    flagValues.forEach(item => {
      if (item) {
        const deviceTypes = Object.keys(item);

        deviceTypes.forEach(deviceType => {
          if (chosenDeviceRef.current.get(deviceType)) {
            const uniqueArray = [...chosenDeviceRef.current.get(deviceType), ...item[deviceType]];
            chosenDeviceRef.current.set(deviceType, Array.from(new Set(uniqueArray)));
          } else {
            chosenDeviceRef.current.set(deviceType, item[deviceType]);
          }
        });
      }
    });
  }
  const history = useHistory();
  const isMaintenancePlan = planType === PlanType.MaintenancePlan;
  const distributeModeOptions = getDistributeModeOptions(planType);
  const isOtherSchedule = Form.useWatch('otherSchedule', form);
  const scheduleSplitRangeList = Form.useWatch('scheduleSplitRangeList', form);
  const specifyDeviceTypeCodes = Form.useWatch('specifyDeviceTypeCodes', form);
  const formDistributeMode = Form.useWatch('distributeMode', form);
  const jobItems = Form.useWatch('jobItems', form);
  const subJobType = Form.useWatch('subJobType', form);
  const splitRule = Form.useWatch('splitRule', form);
  const splitSpaceParams = Form.useWatch('splitSpaceParams', form);
  const periodUnit = Form.useWatch('periodUnit', form);
  const periodMonthWhen = Form.useWatch('periodMonthWhen', form);
  const blockScope = Form.useWatch('blockScope', form);
  const periodYearWhen = Form.useWatch('periodYearWhen', form);
  const planFormType = Form.useWatch('planFormType', form);
  const shieldingObjectList = Form.useWatch('shieldingObjectList', form);
  const checkShieldingObject = Form.useWatch('checkShieldingObject', form);

  const [loading, setLoading] = useState(false);
  const { id } = useParams<{ id: string }>();
  const planTypeText = generateSpecificDataByTaskType(planType).planTypeText;
  useEffect(() => {
    dispatch(getDeviceTypesAction());
  }, [dispatch]);

  useEffect(() => {
    if (['edit', 'copy'].includes(mode) && id && entities) {
      (async function () {
        const { data, error } = await fetchPlanDetail({ id });
        if (error) {
          message.error(error.message);
          return;
        }
        form.setFieldsValue(generateFormInitialValue(planType, data as PlanJSON, entities));
      })();
    } else {
      form.setFieldsValue({ otherUniFlag: nanoid() });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mode, id, entities]);
  const specificRoomType = useDeepCompareMemo(() => {
    const spaceItems =
      planType === PlanType.InspectionPlan && Array.isArray(jobItems) && jobItems.length > 0
        ? jobItems.every(job => isJobItemSpaceType(entities, job.subTypeCode))
          ? jobItems.filter(job => isJobItemSpaceType(entities, job.subTypeCode))
          : []
        : [];
    return spaceItems.length > 0
      ? spaceItems.map((job: { subTypeCode: string }) => job.subTypeCode)
      : undefined;
  }, [jobItems, planType, entities]);
  const specificTargetOptions: { label: string; value: string }[] = useMemo(() => {
    const options = [
      { label: '指定包间', value: 'ROOM' },
      { label: '指定设备', value: 'DEVICE' },
    ];
    if (planFormType === 'SJ_WB') {
      options.pop();
    }
    return options;
  }, [planFormType]);
  useEffect(() => {
    form.setFieldsValue({ specifyRooms: undefined });
  }, [form, specificRoomType]);
  const onFinish = () => {
    form.validateFields().then(async formValue => {
      const {
        blockScope,
        planFormType,
        sla,
        jobItems,
        allowTriggerTime,
        endTime,
        status,
        splitRule,
        splitFloorParams,
        splitSpaceParams,
        periodUnit,
        name,
        period,
        subJobType,
        distributeMode,
        executeRange,
        days,
        periodMonthWhen,
        sortNum,
        atDayType,
        splitDeviceParams,
        periodYearWhen,
        months,
        guidePeriod,
        isRemove,
        scheduleSplitRangeList,
        otherUniFlag,
        otherCycleTimes,
        shieldingObjectList,
      } = formValue;
      setLoading(true);
      const scheduleList: SpecifyTarget[] = [];
      const shieldingList: SpecifyTarget[] = [];
      Array.isArray(scheduleSplitRangeList) &&
        scheduleSplitRangeList.length > 0 &&
        scheduleSplitRangeList.forEach(item => {
          if (item.rangeType === 'ROOM') {
            Array.isArray(item.flagValue) &&
              item.flagValue.length > 0 &&
              scheduleList.push({
                rangeType: item.rangeType,
                flagKey: blockScope,
                flagValue: item.flagValue.map((room: string | string[]) => {
                  if (Array.isArray(room)) {
                    return room[0];
                  }
                  return room;
                }),
                uniFlag: item.uniFlag,
                cycleTimes: item.cycleTimes,
                filterType: item.filterType,
                uniFlagType: 'WHITE',
              });
          }
          if (item.rangeType === 'DEVICE') {
            scheduleList.push(
              ...item.DeviceTypeCodes.map((thirdCode: string) => {
                let deviceCodes = item.flagValue[thirdCode];
                if (typeof deviceCodes === 'string') {
                  deviceCodes = [deviceCodes];
                }
                return {
                  rangeType: item.rangeType,
                  flagKey: thirdCode,
                  flagValue: Array.isArray(deviceCodes) ? deviceCodes : [],
                  uniFlag: item.uniFlag,
                  cycleTimes: item.cycleTimes,
                  uniFlagType: 'WHITE',
                };
              }).filter(
                (item: SpecifyTarget) => Array.isArray(item.flagValue) && item.flagValue.length > 0
              )
            );
          }
          return item;
        });
      Array.isArray(shieldingObjectList) &&
        shieldingObjectList.length > 0 &&
        shieldingObjectList.forEach(item => {
          if (item.rangeType === 'ROOM') {
            Array.isArray(item.flagValue) &&
              item.flagValue.length > 0 &&
              shieldingList.push({
                rangeType: item.rangeType,
                flagKey: blockScope,
                flagValue: item.flagValue.map((room: string | string[]) => {
                  if (Array.isArray(room)) {
                    return room[0];
                  }
                  return room;
                }),
                uniFlag: item.uniFlag,
                cycleTimes: 1,
                filterType: item.filterType,
                uniFlagType: 'BLACK',
              });
          }
          if (item.rangeType === 'DEVICE') {
            shieldingList.push(
              ...item.DeviceTypeCodes.map((thirdCode: string) => {
                let deviceCodes = item.flagValue[thirdCode];
                if (typeof deviceCodes === 'string') {
                  deviceCodes = [deviceCodes];
                }
                return {
                  rangeType: item.rangeType,
                  flagKey: thirdCode,
                  flagValue: Array.isArray(deviceCodes) ? deviceCodes : [],
                  uniFlag: item.uniFlag,
                  cycleTimes: 1,
                  uniFlagType: 'BLACK',
                };
              }).filter(
                (item: SpecifyTarget) => Array.isArray(item.flagValue) && item.flagValue.length > 0
              )
            );
          }
          return item;
        });
      if (isOtherSchedule) {
        scheduleList.push({
          rangeType: 'OTHER',
          uniFlag: otherUniFlag,
          cycleTimes: otherCycleTimes,
          uniFlagType: 'WHITE',
        } as SpecifyTarget);
      }
      const splitors: Splitor[] = [];
      if (splitRule?.includes(SplitorType.Space)) {
        if (splitSpaceParams === SplitorType.Floor) {
          splitors.push({
            splitType: SplitorType.Floor,
            splitParam: splitFloorParams,
          });
        } else {
          splitors.push({
            splitType: SplitorType.Room,
            splitParam: null,
          });
        }
      }

      if (splitRule?.includes(SplitorType.DeviceType)) {
        splitors.push({
          splitType: SplitorType.DeviceType,
          splitParam: splitDeviceParams,
        });
      }

      const params = {
        guidePeriod,
        name: name,
        blockScope: Array.isArray(executeRange)
          ? executeRange.map(item => ({
              blockGuid: blockScope,
              scopeType: distributeMode,
              scopeFlag: distributeMode === DistributeMode.BlockUser ? item.value : item,
            }))
          : [
              {
                blockGuid: blockScope,
                scopeType: distributeMode,
                scopeFlag:
                  distributeMode === DistributeMode.BlockUser
                    ? executeRange.value
                    : (executeRange ?? undefined),
              },
            ],
        jobType: [PlanType.InspectionPlan, PlanType.InventoryPlan].includes(planType)
          ? generateSpecificDataByTaskType(planType).jobType
          : planFormType,
        jobSla: sla.sla,
        slaUnit: sla.unit,
        jobItems: generateJobItemsDataByTaskType(
          planType,
          [PlanType.InspectionPlan, PlanType.InventoryPlan].includes(planType)
            ? generateSpecificDataByTaskType(planType).jobType
            : planFormType,
          jobItems,
          entities,
          ticketTypeEntities,
          roomTypeEntities
        ),
        allowTriggerTime: allowTriggerTime.map((time: moment.MomentInput) =>
          moment(time)
            .set({ second: 0 })
            .format(periodUnit === CyclesType.None ? 'YYYY-MM-DD HH:mm:ss' : 'HH:mm')
        ),
        endTime: endTime ? moment(endTime).endOf('day').valueOf() : null,
        status: status === true ? PlanStatus.On : PlanStatus.Off,
        splitors: splitors,
        cycles: generateCyclesParams({
          periodUnit,
          allowTriggerTime,
          period,
          isRemove,
          days,
          periodMonthWhen,
          sortNum,
          atDayType,
          periodYearWhen,
          months,
        }),
        periodUnit: periodUnit,
        subJobType: subJobType,
        scheduleSplitRangeList: scheduleList.concat(shieldingList),
      };

      if (mode === 'create' || mode === 'copy') {
        const { error } = await createPlan(params as BackendCreateParams);
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        message.success(mode === 'create' ? '新建计划成功' : '复制计划成功');
      } else {
        const { error } = await updatePlan({ ...params, id: id });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        message.success('修改计划成功');
      }

      history.goBack();
    });
  };
  const targetOptions = useMemo(() => {
    return jobItems && Array.isArray(jobItems) && jobItems.length > 0
      ? generateSelectOptionsDataByTaskType(planType, jobItems, entities)
      : [];
  }, [entities, jobItems, planType]);
  const isPlanConfigChoosen = jobItems && Array.isArray(jobItems) && jobItems.length > 0;
  useDeepCompareEffect(() => {
    const targetOptionsValues = targetOptions.map(item => item.value);
    if (
      targetOptionsValues.length > 0 &&
      Array.isArray(specifyDeviceTypeCodes) &&
      specifyDeviceTypeCodes.length > 0
    ) {
      form.setFieldsValue({
        specifyDeviceTypeCodes: specifyDeviceTypeCodes.filter((item: string) =>
          targetOptionsValues.includes(item)
        ),
      });
    }
  }, [targetOptions, specifyDeviceTypeCodes]);

  const onRangeTypeChange = useCallback(
    (index: number, key: string) => {
      const currentInfos: { rangeType: string }[] = form.getFieldValue(key) ?? [];
      form.setFieldsValue({
        [key]: currentInfos.map((info, idx) => {
          if (idx !== index) {
            return info;
          }
          const rangeType = get(info, ['rangeType']);

          return {
            rangeType,
            uniFlag: nanoid(),
          };
        }),
      });
    },
    [form]
  );

  useEffect(() => {
    if (!checkShieldingObject) {
      form.setFieldsValue({ shieldingObjectList: [] });
    }
  }, [checkShieldingObject]);

  return (
    <Form
      form={form}
      labelCol={{ span: 4 }}
      wrapperCol={{ span: 20 }}
      initialValues={{
        sla: { sla: 0, unit: SlaUnit.Minutes },
        planFormType: generateSpecificDataByTaskType(planType).jobTypeText,
        isRemove: 0,
        sortNum: 1,
        atDayType: MonthlyWorkDayType.NaturalDay,
        months: [1],
        status: true,
        distributeMode: DistributeMode.Block,
        splitDeviceParams: ThreeDeviceType.FirstCategory,
        periodUnit: CyclesType.None,
        allowTriggerTime: [moment()],
      }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <Card title="基本信息" bodyStyle={{ width: '40%' }}>
          <Form.Item
            label="楼栋"
            name="blockScope"
            rules={[
              {
                required: true,
                message: '楼栋必填',
              },
            ]}
          >
            <LocationTreeSelect
              style={{ width: 224 }}
              authorizedOnly
              disabledTypes={['IDC']}
              disabled={mode === 'edit'}
              onChange={() => {
                form.setFieldsValue({
                  jobItems: undefined,
                  specifyRooms: undefined,
                  specifyTarget: undefined,
                  scheduleSplitRangeList: undefined,
                });
              }}
            />
          </Form.Item>

          <Form.Item
            label="计划名称"
            name="name"
            rules={[
              {
                required: true,
                message: '计划名称必填',
              },
              { max: 100, message: '计划名称超过100个字符，请修改计划名称' },
            ]}
          >
            <Input style={{ width: 336 }} />
          </Form.Item>

          <Form.Item
            label="计划类型"
            name="planFormType"
            rules={[
              {
                required: true,
                message: '计划类型必选',
              },
            ]}
          >
            {PlanType.MaintenancePlan === planType ? (
              <Select
                disabled={mode === 'edit'}
                style={{ width: 224 }}
                options={MAINTENANCE_TYPE_OPTIONS}
                onChange={() => {
                  form.setFieldsValue({ subJobType: undefined });
                  form.setFieldsValue({ jobItems: undefined });
                }}
              />
            ) : (
              <Input style={{ width: 224 }} disabled />
            )}
          </Form.Item>

          <Form.Item
            label={generateSpecificDataByTaskType(planType).columnTitle}
            name="subJobType"
            rules={[
              {
                required: true,
                message: `${generateSpecificDataByTaskType(planType).columnTitle}必选`,
              },
            ]}
          >
            <TaskConfigurationSelect
              style={{ width: 224 }}
              ticketPlanType={generateSpecificDataByTaskType(planType).ticketType}
              disabled={mode === 'edit'}
              isMaintenanceTools={planFormType === MaintenancePlanType.ToolsMaintenance}
              onChange={() => {
                form.setFieldsValue({ jobItems: undefined });
              }}
            />
          </Form.Item>
          {isMaintenancePlan && (
            <Form.Item
              label="维护周期"
              name="guidePeriod"
              rules={[
                {
                  required: true,
                  message: '维护周期必选',
                },
              ]}
            >
              <MaintenanceCycleSelect style={{ width: 224 }} />
            </Form.Item>
          )}
          <Form.Item label={isMaintenancePlan ? '计划排期' : '重复周期'} required>
            <Space direction="vertical" style={{ width: '100%' }} size="large">
              <Form.Item
                name="periodUnit"
                rules={[
                  {
                    required: true,
                    message: `${isMaintenancePlan ? '计划排期' : '重复周期'}必选`,
                  },
                ]}
                noStyle
              >
                <Select
                  style={{ width: 224 }}
                  options={CYCLES_TYPE_OPTIONS}
                  onChange={() => {
                    form.setFieldsValue({ period: 1, days: [], allowTriggerTime: [] });
                  }}
                />
              </Form.Item>
              {periodUnit === CyclesType.Day && (
                <Form.Item>
                  <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <Space>
                      <span>每</span>
                      <Form.Item
                        name="period"
                        noStyle
                        rules={[{ required: true, message: '必填' }]}
                      >
                        <InputNumber min={1} max={364} />
                      </Form.Item>
                      <span>天</span>
                    </Space>

                    <Form.Item name="isRemove" noStyle>
                      <RemoveWorkDays />
                    </Form.Item>
                  </Space>
                </Form.Item>
              )}
              {periodUnit === CyclesType.Week && (
                <Form.Item>
                  <Space direction="vertical" size="large">
                    <Space size="middle">
                      <Space>
                        <span>每</span>
                        <Form.Item
                          name="period"
                          noStyle
                          rules={[{ required: true, message: '必填' }]}
                        >
                          <InputNumber min={1} max={52} />
                        </Form.Item>
                        <span>周</span>
                      </Space>

                      <Space>
                        <span>天</span>
                        <Form.Item
                          name="days"
                          noStyle
                          rules={[{ required: true, message: '天必选' }]}
                        >
                          <Select
                            mode="multiple"
                            options={WEEK_DAY_OPYIONS}
                            style={{ width: 224 }}
                          />
                        </Form.Item>
                      </Space>
                    </Space>

                    <Form.Item name="isRemove" noStyle>
                      <RemoveWorkDays />
                    </Form.Item>
                  </Space>
                </Form.Item>
              )}

              {periodUnit === CyclesType.Month && (
                <Form.Item>
                  <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <Space>
                      <Space>
                        <span>每</span>
                        <Form.Item
                          name="period"
                          noStyle
                          rules={[{ required: true, message: '必填' }]}
                        >
                          <InputNumber min={1} max={12} />
                        </Form.Item>
                        <span>月</span>
                      </Space>
                    </Space>
                    <Form.Item
                      name="periodMonthWhen"
                      noStyle
                      rules={[{ required: true, message: '必选项' }]}
                    >
                      <Radio.Group>
                        <Space direction="vertical" style={{ width: '100%' }} size="large">
                          <Radio value>每逢</Radio>
                          {periodMonthWhen === true && (
                            <Form.Item label="天" required>
                              <Space direction="vertical" size="large">
                                <Form.Item
                                  name="days"
                                  rules={[{ required: true, message: '天必选' }]}
                                  noStyle
                                >
                                  <Select
                                    options={MONTH_DAY_OPYIONS}
                                    mode="multiple"
                                    style={{ width: '100%' }}
                                  />
                                </Form.Item>
                                <Form.Item name="isRemove" noStyle>
                                  <RemoveWorkDays />
                                </Form.Item>
                              </Space>
                            </Form.Item>
                          )}
                          <br />
                          <Radio value={false}>在</Radio>
                          {periodMonthWhen === false && (
                            <Space size="middle">
                              <Form.Item name="sortNum" noStyle>
                                <Select options={NUMBER_OPYIONS} style={{ width: 224 }} />
                              </Form.Item>
                              <Form.Item name="atDayType" noStyle>
                                <Select options={DAY_WORK_WEEK_OPYIONS} style={{ width: 224 }} />
                              </Form.Item>
                            </Space>
                          )}
                        </Space>
                      </Radio.Group>
                    </Form.Item>
                  </Space>
                </Form.Item>
              )}

              {periodUnit === CyclesType.Year && (
                <Form.Item>
                  <Space direction="vertical" style={{ width: '100%' }} size="large">
                    <Space>
                      <Space>
                        <span>每</span>
                        <Form.Item
                          name="period"
                          noStyle
                          rules={[{ required: true, message: '必填' }]}
                        >
                          <InputNumber min={1} max={10} />
                        </Form.Item>
                        <span>年</span>
                      </Space>
                    </Space>
                    <Form.Item
                      name="periodYearWhen"
                      noStyle
                      rules={[{ required: true, message: '必选项' }]}
                    >
                      <Radio.Group>
                        <Space direction="vertical" style={{ width: '100%' }} size="large">
                          <Radio value>每逢</Radio>
                          {periodYearWhen === true && (
                            <Form.Item label="日期">
                              <Space direction="vertical" size="large">
                                <Form.Item
                                  name="days"
                                  rules={[{ required: true, message: '日期必选' }]}
                                  noStyle
                                >
                                  <TreeSelect
                                    style={{ width: '100%' }}
                                    treeData={MONTH_DAY_IN_YEAR_OPYIONS}
                                    multiple
                                    treeNodeFilterProp="label"
                                    showSearch
                                  />
                                </Form.Item>
                                <Form.Item name="isRemove" noStyle>
                                  <RemoveWorkDays />
                                </Form.Item>
                              </Space>
                            </Form.Item>
                          )}
                          <br />
                          <Radio value={false}>在</Radio>
                          {periodYearWhen === false && (
                            <Space size="middle">
                              <Form.Item name="months" noStyle>
                                <Select
                                  mode="multiple"
                                  options={MONTH_IN_YEAR_OPYIONS}
                                  style={{ width: 224 }}
                                />
                              </Form.Item>
                              <Form.Item name="sortNum" noStyle>
                                <Select options={NUMBER_OPYIONS} style={{ width: 224 }} />
                              </Form.Item>
                              <Form.Item name="atDayType" noStyle>
                                <Select options={DAY_WORK_WEEK_OPYIONS} style={{ width: 224 }} />
                              </Form.Item>
                            </Space>
                          )}
                        </Space>
                      </Radio.Group>
                    </Form.Item>
                  </Space>
                </Form.Item>
              )}
            </Space>
          </Form.Item>
          <Form.List
            name="allowTriggerTime"
            rules={[
              {
                validator: async (_, triggerTime) => {
                  if (!triggerTime || triggerTime.length < 1 || !triggerTime.length) {
                    return Promise.reject(new Error('请至少添加一项触发时间'));
                  } else if (
                    triggerTime.length > 1 &&
                    !triggerTime.some(
                      (item: Moment | undefined | null) =>
                        typeof item === 'undefined' || item === null
                    )
                  ) {
                    let errorFlag = false;
                    for (let i = 0; i < triggerTime.length - 1; i++) {
                      if (
                        Math.abs(triggerTime[i].valueOf() - triggerTime[i + 1].valueOf()) <
                        10 * 60 * 1000
                      ) {
                        errorFlag = true;
                      }
                    }
                    return errorFlag
                      ? Promise.reject(new Error('触发时间前后至少间隔10分钟'))
                      : Promise.resolve();
                  } else {
                    return Promise.resolve();
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map((field, index) => (
                  <>
                    <Form.Item
                      required
                      label={index === 0 ? '触发时间' : ''}
                      {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                      key={field.key}
                    >
                      <Space>
                        <Form.Item
                          {...field}
                          noStyle
                          rules={[{ required: true, message: '请填写触发时间' }]}
                        >
                          <DatePicker
                            picker={periodUnit === CyclesType.None ? 'date' : 'time'}
                            format={periodUnit === CyclesType.None ? 'YYYY-MM-DD HH:mm' : 'HH:mm'}
                            showTime={periodUnit === CyclesType.None}
                            disabledDate={
                              periodUnit === CyclesType.None ? handleEndDisabledDate : undefined
                            }
                            disabledTime={
                              periodUnit === CyclesType.None ? disabledDateTime : undefined
                            }
                          />
                        </Form.Item>
                        {fields.length > 1 ? (
                          <MinusCircleOutlined onClick={() => remove(field.name)} />
                        ) : null}
                      </Space>
                    </Form.Item>
                  </>
                ))}
                <Form.Item {...formItemLayoutWithOutLabel}>
                  <Button
                    type="dashed"
                    style={{ width: 224 }}
                    icon={<PlusCircleOutlined />}
                    onClick={() => {
                      if (fields.length === 6) {
                        message.warning('最多支持添加6项触发时间');
                        return;
                      }
                      add();
                    }}
                  >
                    添加触发时间
                  </Button>
                  <Form.ErrorList errors={errors} />
                </Form.Item>
              </>
            )}
          </Form.List>

          {periodUnit !== CyclesType.None && (
            <Form.Item
              label="结束时间"
              name="endTime"
              rules={[
                () => ({
                  validator(_, value) {
                    if (value && moment(value).valueOf() < moment().startOf('day').valueOf()) {
                      return Promise.reject(new Error('结束时间不可小于今天！'));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <DatePicker format="YYYY-MM-DD" />
            </Form.Item>
          )}

          <Form.Item label="启用状态" name="status" valuePropName="checked">
            <Switch />
          </Form.Item>
        </Card>
        <Card title="任务配置" bodyStyle={{ width: '40%' }}>
          <Form.Item
            label={generateSpecificDataByTaskType(planType).prefix}
            name="jobItems"
            rules={[{ required: true, message: '配置必选' }]}
          >
            <TaskConfigurationModal
              planType={planType}
              subType={subJobType}
              blockGuid={blockScope}
              onChange={value => {
                form.setFieldsValue({
                  specifyTarget: undefined,
                  scheduleSplitRangeList: undefined,
                  shieldingObjectList: undefined,
                });
              }}
            />
          </Form.Item>
          <Form.Item
            required
            label="分发规则"
            rules={[
              {
                required: true,
                message: '分发规则必选',
              },
            ]}
          >
            <Space style={{ width: '100%' }}>
              <Form.Item
                name="distributeMode"
                rules={[{ required: true, message: '分发模式必选' }]}
                noStyle
              >
                <Select
                  options={distributeModeOptions}
                  disabled={!blockScope}
                  style={{ width: 224 }}
                  onChange={() => {
                    form.setFieldsValue({ executeRange: undefined });
                  }}
                />
              </Form.Item>

              {formDistributeMode === DistributeMode.Block ? (
                <span>楼栋下人员</span>
              ) : (
                <Form.Item
                  name="executeRange"
                  rules={[{ required: true, message: '执行范围必选' }]}
                  noStyle
                >
                  {generateExecuteRangeComponent(formDistributeMode, blockScope)}
                </Form.Item>
              )}
            </Space>
          </Form.Item>
          <Form.Item
            label={
              <Explanation
                iconType="question"
                tooltip={{
                  title: `根据生效次数批量生成目标${planTypeText}对象，一个周期覆盖所有目标${planTypeText}对象，若目标策略包含不同指定类型则只会生效交集部分，且更改次数或者新选择/删除${
                    generateSpecificDataByTaskType(planType).prefix
                  }则重新生效逻辑。`,
                }}
              >
                <Typography.Text>目标策略</Typography.Text>
              </Explanation>
            }
          >
            <Form.List name="scheduleSplitRangeList">
              {(fields, { add, remove }, { errors }) => (
                <>
                  {fields.map(({ key, name, ...restField }) => {
                    const scheduleSplitRange =
                      Array.isArray(scheduleSplitRangeList) &&
                      scheduleSplitRangeList.find((_: unknown, index: number) => name === index);
                    const rangeType = scheduleSplitRange?.rangeType;
                    const DeviceTypeCodes = scheduleSplitRange?.DeviceTypeCodes;
                    const flagValue = scheduleSplitRange?.flagValue;
                    const uniFlag = scheduleSplitRange?.uniFlag;

                    const maxCycleTimes =
                      rangeType === 'ROOM' && Array.isArray(flagValue) && flagValue.length > 0
                        ? scheduleSplitRange.flagValue.length
                        : rangeType === 'DEVICE' &&
                            Array.isArray(DeviceTypeCodes) &&
                            DeviceTypeCodes.length > 0 &&
                            flagValue
                          ? DeviceTypeCodes.map(item =>
                              Array.isArray(flagValue[item]) ? flagValue[item].length : 0
                            ).reduce((acr, cur) => {
                              return acr + cur;
                            }, 0)
                          : undefined;
                    const disabledOptions = (
                      Array.isArray(scheduleSplitRangeList)
                        ? scheduleSplitRangeList.filter(
                            (item: { rangeType: string; uniFlag: string }) =>
                              item.rangeType === rangeType && item.uniFlag !== uniFlag
                          )
                        : []
                    ).concat(
                      Array.isArray(shieldingObjectList)
                        ? shieldingObjectList.filter(
                            (item: { rangeType: string; uniFlag: string }) =>
                              item.rangeType === rangeType && item.uniFlag !== uniFlag
                          )
                        : []
                    );
                    const disabledRoom =
                      disabledOptions.length > 0 && rangeType === 'ROOM'
                        ? disabledOptions.reduce((acc, cur) => {
                            if (Array.isArray(cur?.flagValue) && cur?.flagValue.length > 0) {
                              return [
                                ...acc,
                                ...cur?.flagValue.map((room: string | string[]) => {
                                  if (Array.isArray(room)) {
                                    return room[0];
                                  }
                                  return room;
                                }),
                              ];
                            }
                            return acc;
                          }, [])
                        : undefined;

                    updateChosenDeviceMap(
                      (Array.isArray(scheduleSplitRangeList)
                        ? scheduleSplitRangeList.filter(
                            (item: { rangeType: string; uniFlag: string }) =>
                              item.rangeType !== 'ROOM'
                          )
                        : []
                      ).concat(
                        Array.isArray(shieldingObjectList)
                          ? shieldingObjectList.filter(
                              (item: { rangeType: string; uniFlag: string }) =>
                                item.rangeType !== 'ROOM'
                            )
                          : []
                      )
                    );

                    return (
                      <Form.Item key={key}>
                        <Space
                          style={{ display: 'flex', width: 1100 }}
                          direction="vertical"
                          size="middle"
                        >
                          <Row justify="space-between">
                            <Col>
                              <Space style={{ width: 500 }}>
                                <Form.Item
                                  name={[name, 'rangeType']}
                                  noStyle
                                  rules={[{ required: true, message: '目标策略指定类型必填' }]}
                                >
                                  <Select
                                    style={{ width: 224 }}
                                    disabled={!isPlanConfigChoosen}
                                    allowClear
                                    options={specificTargetOptions}
                                    onChange={() =>
                                      onRangeTypeChange(name, 'scheduleSplitRangeList')
                                    }
                                  />
                                </Form.Item>
                                每生效
                                <Form.Item
                                  noStyle
                                  name={[name, 'cycleTimes']}
                                  rules={[{ required: true, message: '周期次数必填' }]}
                                >
                                  <InputNumber precision={0} min={1} max={maxCycleTimes} />
                                </Form.Item>
                                次为一个周期
                              </Space>
                            </Col>
                            <Col>
                              {fields.length > 0 ? (
                                <DeleteOutlined
                                  style={{ marginLeft: 10 }}
                                  onClick={() => remove(name)}
                                />
                              ) : null}
                            </Col>
                          </Row>
                          <Form.Item name={[name, 'uniFlag']} dependencies={undefined} noStyle />
                          <Form.Item name={[name, 'filterType']} dependencies={undefined} noStyle />
                          {rangeType === 'ROOM' &&
                            blockScope &&
                            Array.isArray(jobItems) &&
                            jobItems.length > 0 && (
                              <Container color="default" style={{ width: 1100 }}>
                                <Space
                                  style={{ display: 'flex', width: '100%' }}
                                  direction="vertical"
                                >
                                  <Typography.Text>{blockScope}:</Typography.Text>
                                  <Form.Item name={[name, 'flagValue']} noStyle>
                                    <LocationCascader
                                      showSearch
                                      authorizedOnly
                                      nodeTypes={['ROOM']}
                                      roomTypes={specificRoomType}
                                      allowClear
                                      multiple
                                      style={{ minWidth: 210 }}
                                      blocks={[blockScope.split('.')[1]]}
                                      idc={blockScope.split('.')[0]}
                                      disabledSpaceCodes={disabledRoom}
                                      onChange={(value, selectOptions) => {
                                        const filterType =
                                          Array.isArray(selectOptions) &&
                                          selectOptions
                                            .map(item => {
                                              const spaces = item as BasicSpace[];
                                              return spaces[0]?.custom?.type;
                                            })
                                            .join();

                                        filterType &&
                                          form.setFieldsValue({
                                            scheduleSplitRangeList: scheduleSplitRangeList.map(
                                              (
                                                item: { filterType: string; flagValue: string[] },
                                                index: number
                                              ) => {
                                                if (index === name) {
                                                  return (item = {
                                                    ...item,
                                                    flagValue: value as string[],
                                                    filterType,
                                                  });
                                                }
                                                return item;
                                              }
                                            ),
                                          });
                                      }}
                                    />
                                  </Form.Item>
                                </Space>
                              </Container>
                            )}

                          {rangeType === 'DEVICE' &&
                            isPlanConfigChoosen &&
                            Array.isArray(targetOptions) &&
                            targetOptions.length > 0 && (
                              <Form.Item label="资源分类">
                                <Space
                                  style={{ display: 'flex', width: 1100 }}
                                  direction="vertical"
                                  size="middle"
                                >
                                  <Form.Item name={[name, 'DeviceTypeCodes']} noStyle>
                                    <Checkbox.Group style={{ width: '100%' }}>
                                      <Space
                                        style={{ display: 'flex', width: '100%' }}
                                        direction="vertical"
                                      >
                                        {chunk(targetOptions, 6).map(items => {
                                          return (
                                            <Row key={items[0].value}>
                                              {items.map(item => (
                                                <Col key={item.value} span={4}>
                                                  <Checkbox key={item.value} value={item.value}>
                                                    {item.label}
                                                  </Checkbox>
                                                </Col>
                                              ))}
                                            </Row>
                                          );
                                        })}
                                      </Space>
                                    </Checkbox.Group>
                                  </Form.Item>
                                </Space>
                              </Form.Item>
                            )}
                        </Space>

                        {rangeType === 'DEVICE' &&
                          DeviceTypeCodes &&
                          Array.isArray(DeviceTypeCodes) &&
                          DeviceTypeCodes.length > 0 && (
                            <Container color="default" style={{ width: 1100, marginTop: -8 }}>
                              <Space
                                style={{ display: 'flex', width: '100%' }}
                                direction="vertical"
                                size="large"
                              >
                                {DeviceTypeCodes.map(item => (
                                  <Space
                                    key={item}
                                    style={{ display: 'flex', width: '100%' }}
                                    direction="vertical"
                                  >
                                    <Typography.Text>{entities[item]?.metaName}:</Typography.Text>
                                    <Form.Item name={[name, 'flagValue', item]} noStyle>
                                      <DeviceSelect
                                        style={{ width: '100%' }}
                                        idcTag={blockScope.split('.')[0]}
                                        blockTag={blockScope.split('.')[1]}
                                        deviceTypeList={[item]}
                                        mode="multiple"
                                        allowClear
                                        disabledDeviceGuids={
                                          chosenDeviceRef.current.size !== 0 &&
                                          chosenDeviceRef.current.get(item)
                                            ? chosenDeviceRef.current
                                                .get(item)
                                                .filter((targetCode: string) =>
                                                  flagValue && flagValue[item]
                                                    ? !flagValue[item].includes(targetCode)
                                                    : true
                                                )
                                            : []
                                        }
                                      />
                                    </Form.Item>
                                  </Space>
                                ))}
                              </Space>
                            </Container>
                          )}
                      </Form.Item>
                    );
                  })}
                  <Form.Item>
                    <Button
                      style={{ width: 464 }}
                      type="dashed"
                      icon={<PlusOutlined />}
                      onClick={() => add({ uniFlag: nanoid() })}
                    >
                      添加对象
                    </Button>
                  </Form.Item>
                </>
              )}
            </Form.List>
            {Array.isArray(scheduleSplitRangeList) &&
              scheduleSplitRangeList.length > 0 &&
              scheduleSplitRangeList.some(i => !!i.rangeType) && (
                <Space style={{ width: 600 }}>
                  <Form.Item name="otherSchedule" valuePropName="checked" noStyle>
                    <Checkbox>配置除指定对象以外的目标策略</Checkbox>
                  </Form.Item>
                  {isOtherSchedule && (
                    <>
                      每生效
                      <Form.Item
                        noStyle
                        name="otherCycleTimes"
                        rules={[{ required: true, message: '周期次数必填' }]}
                      >
                        <InputNumber precision={0} min={1} />
                      </Form.Item>
                      次为一个周期
                      <Form.Item name="otherUniFlag" noStyle dependencies={undefined} />
                    </>
                  )}
                </Space>
              )}
            <Form.Item name="checkShieldingObject" valuePropName="checked" noStyle>
              <Checkbox>屏蔽对象</Checkbox>
            </Form.Item>
            <Form.List name="shieldingObjectList">
              {(fields, { add, remove }, { errors }) =>
                checkShieldingObject && (
                  <>
                    {fields.map(({ key, name, ...restField }) => {
                      const scheduleSplitRange =
                        Array.isArray(shieldingObjectList) &&
                        shieldingObjectList.find((_: unknown, index: number) => name === index);
                      const rangeType = scheduleSplitRange?.rangeType;
                      const DeviceTypeCodes = scheduleSplitRange?.DeviceTypeCodes;
                      const flagValue = scheduleSplitRange?.flagValue;
                      const uniFlag = scheduleSplitRange?.uniFlag;

                      const disabledOptions = (
                        Array.isArray(shieldingObjectList)
                          ? shieldingObjectList.filter(
                              (item: { rangeType: string; uniFlag: string }) =>
                                item.rangeType === rangeType && item.uniFlag !== uniFlag
                            )
                          : []
                      ).concat(
                        Array.isArray(scheduleSplitRangeList)
                          ? scheduleSplitRangeList.filter(
                              (item: { rangeType: string; uniFlag: string }) =>
                                item.rangeType === rangeType && item.uniFlag !== uniFlag
                            )
                          : []
                      );
                      const disabledRoom =
                        disabledOptions.length > 0 && rangeType === 'ROOM'
                          ? disabledOptions.reduce((acc, cur) => {
                              if (Array.isArray(cur?.flagValue) && cur?.flagValue.length > 0) {
                                return [
                                  ...acc,
                                  ...cur?.flagValue.map((room: string | string[]) => {
                                    if (Array.isArray(room)) {
                                      return room[0];
                                    }
                                    return room;
                                  }),
                                ];
                              }
                              return acc;
                            }, [])
                          : undefined;

                      updateChosenDeviceMap(
                        (Array.isArray(shieldingObjectList)
                          ? shieldingObjectList.filter(
                              (item: { rangeType: string; uniFlag: string }) =>
                                item.rangeType !== 'ROOM'
                            )
                          : []
                        ).concat(
                          Array.isArray(scheduleSplitRangeList)
                            ? scheduleSplitRangeList.filter(
                                (item: { rangeType: string; uniFlag: string }) =>
                                  item.rangeType !== 'ROOM'
                              )
                            : []
                        )
                      );

                      return (
                        <Form.Item key={key}>
                          <Space
                            style={{ display: 'flex', width: 1100 }}
                            direction="vertical"
                            size="middle"
                          >
                            <Row justify="space-between">
                              <Col>
                                <Space style={{ width: 500 }}>
                                  <Form.Item
                                    name={[name, 'rangeType']}
                                    noStyle
                                    // rules={[{ required: true, message: '' }]}
                                  >
                                    <Select
                                      style={{ width: 224 }}
                                      disabled={!isPlanConfigChoosen}
                                      allowClear
                                      options={specificTargetOptions}
                                      onChange={() =>
                                        onRangeTypeChange(name, 'shieldingObjectList')
                                      }
                                    />
                                  </Form.Item>
                                </Space>
                              </Col>
                              <Col>
                                {fields.length > 0 ? (
                                  <DeleteOutlined
                                    style={{ marginLeft: 10 }}
                                    onClick={() => remove(name)}
                                  />
                                ) : null}
                              </Col>
                            </Row>
                            {/* <Form.Item
                          name={[name, 'uniFlag']}
                          dependencies={undefined}
                          noStyle
                        />
                        <Form.Item
                          name={[name, 'shieldingFilterType']}
                          dependencies={undefined}
                          noStyle
                        /> */}
                            {rangeType === 'ROOM' &&
                              blockScope &&
                              Array.isArray(jobItems) &&
                              jobItems.length > 0 && (
                                <Container color="default" style={{ width: 1100 }}>
                                  <Space
                                    style={{ display: 'flex', width: '100%' }}
                                    direction="vertical"
                                  >
                                    <Typography.Text>{blockScope}:</Typography.Text>
                                    <Form.Item name={[name, 'flagValue']} noStyle>
                                      <LocationCascader
                                        showSearch
                                        authorizedOnly
                                        nodeTypes={['ROOM']}
                                        roomTypes={specificRoomType}
                                        allowClear
                                        multiple
                                        style={{ minWidth: 210 }}
                                        blocks={[blockScope.split('.')[1]]}
                                        idc={blockScope.split('.')[0]}
                                        disabledSpaceCodes={disabledRoom}
                                        onChange={(value, selectOptions) => {
                                          const filterType =
                                            Array.isArray(selectOptions) &&
                                            selectOptions
                                              .map(item => {
                                                const spaces = item as BasicSpace[];
                                                return spaces[0]?.custom?.type;
                                              })
                                              .join();

                                          filterType &&
                                            form.setFieldsValue({
                                              shieldingObjectList: shieldingObjectList.map(
                                                (
                                                  item: { filterType: string; flagValue: string[] },
                                                  index: number
                                                ) => {
                                                  if (index === name) {
                                                    return (item = {
                                                      ...item,
                                                      flagValue: value as string[],
                                                      filterType,
                                                    });
                                                  }
                                                  return item;
                                                }
                                              ),
                                            });
                                        }}
                                      />
                                    </Form.Item>
                                  </Space>
                                </Container>
                              )}

                            {rangeType === 'DEVICE' &&
                              isPlanConfigChoosen &&
                              Array.isArray(targetOptions) &&
                              targetOptions.length > 0 && (
                                <Form.Item label="资源分类">
                                  <Space
                                    style={{ display: 'flex', width: 1100 }}
                                    direction="vertical"
                                    size="middle"
                                  >
                                    <Form.Item name={[name, 'DeviceTypeCodes']} noStyle>
                                      <Checkbox.Group style={{ width: '100%' }}>
                                        <Space
                                          style={{ display: 'flex', width: '100%' }}
                                          direction="vertical"
                                        >
                                          {chunk(targetOptions, 6).map(items => {
                                            return (
                                              <Row key={items[0].value}>
                                                {items.map(item => (
                                                  <Col key={item.value} span={4}>
                                                    <Checkbox key={item.value} value={item.value}>
                                                      {item.label}
                                                    </Checkbox>
                                                  </Col>
                                                ))}
                                              </Row>
                                            );
                                          })}
                                        </Space>
                                      </Checkbox.Group>
                                    </Form.Item>
                                  </Space>
                                </Form.Item>
                              )}
                          </Space>

                          {rangeType === 'DEVICE' &&
                            DeviceTypeCodes &&
                            Array.isArray(DeviceTypeCodes) &&
                            DeviceTypeCodes.length > 0 && (
                              <Container color="default" style={{ width: 1100, marginTop: -8 }}>
                                <Space
                                  style={{ display: 'flex', width: '100%' }}
                                  direction="vertical"
                                  size="large"
                                >
                                  {DeviceTypeCodes.map(item => (
                                    <Space
                                      key={item}
                                      style={{ display: 'flex', width: '100%' }}
                                      direction="vertical"
                                    >
                                      <Typography.Text>{entities[item]?.metaName}:</Typography.Text>
                                      <Form.Item name={[name, 'flagValue', item]} noStyle>
                                        <DeviceSelect
                                          style={{ width: '100%' }}
                                          idcTag={blockScope.split('.')[0]}
                                          blockTag={blockScope.split('.')[1]}
                                          deviceTypeList={[item]}
                                          mode="multiple"
                                          allowClear
                                          disabledDeviceGuids={
                                            chosenDeviceRef.current.size !== 0 &&
                                            chosenDeviceRef.current.get(item)
                                              ? chosenDeviceRef.current
                                                  .get(item)
                                                  .filter((targetCode: string) =>
                                                    flagValue && flagValue[item]
                                                      ? !flagValue[item].includes(targetCode)
                                                      : true
                                                  )
                                              : []
                                          }
                                        />
                                      </Form.Item>
                                    </Space>
                                  ))}
                                </Space>
                              </Container>
                            )}
                        </Form.Item>
                      );
                    })}
                    <Form.Item>
                      <Button
                        style={{ width: 464 }}
                        type="dashed"
                        icon={<PlusOutlined />}
                        onClick={() => add({ uniFlag: nanoid() })}
                      >
                        添加屏蔽对象
                      </Button>
                    </Form.Item>
                  </>
                )
              }
            </Form.List>
          </Form.Item>

          <Form.Item label="拆分规则">
            <Form.Item name="splitRule" noStyle>
              <Checkbox.Group>
                <Space direction="vertical" size="large">
                  <Checkbox value={SplitorType.Space}>按照空间拆分任务单</Checkbox>
                  {splitRule?.includes(SplitorType.Space) && (
                    <Form.Item name="splitSpaceParams" noStyle>
                      <Radio.Group
                        options={SOLIT_ROOM_FLOOR_OPTIONS}
                        onChange={e => {
                          if (e.target.value !== SplitorType.Floor) {
                            form.setFieldsValue({ splitFloorParams: null });
                          } else {
                            form.setFieldsValue({ splitFloorParams: 1 });
                          }
                        }}
                      />
                    </Form.Item>
                  )}
                  {splitSpaceParams === SplitorType.Floor && (
                    <Space>
                      <span>每</span>
                      <Form.Item
                        name="splitFloorParams"
                        rules={[{ required: true, message: '楼层数必填' }]}
                        noStyle
                      >
                        <InputNumber min={1} max={10} />
                      </Form.Item>
                      <span>楼层进行拆分</span>
                    </Space>
                  )}
                </Space>

                <Space direction="vertical" size="large">
                  <Checkbox value={SplitorType.DeviceType}>按照设备分类拆分任务单</Checkbox>
                  {splitRule?.includes(SplitorType.DeviceType) && (
                    <Space>
                      <span>按照设备的</span>
                      <Form.Item name="splitDeviceParams" noStyle>
                        <Select style={{ width: 224 }} options={THREE_DEVICE_TYPE_OPTIONS} />
                      </Form.Item>
                      <span>进行拆分</span>
                    </Space>
                  )}
                </Space>
              </Checkbox.Group>
            </Form.Item>
          </Form.Item>
          <Form.Item
            label="SLA"
            name="sla"
            rules={[
              {
                required: true,
                message: 'SLA必填',
              },
              () => ({
                validator(_, value) {
                  if (value.sla === undefined || value.sla === null) {
                    return Promise.reject(new Error('请输入有效的SLA值'));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <SlaSelect />
          </Form.Item>
        </Card>
      </Space>
      <FooterToolBar>
        <Space>
          <Button type="primary" loading={loading} onClick={onFinish}>
            提交
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </Form>
  );
}

function generateExecuteRangeComponent(type: DistributeMode, blockScope: string) {
  switch (type) {
    case DistributeMode.BlockRole:
      return (
        <RoleSelect style={{ width: 224 }} trigger="onDidMount" mode="multiple" maxTagCount={2} />
      );
    case DistributeMode.BlockUser:
      return (
        <UserSelect
          style={{ width: 224 }}
          userState="in-service"
          blockGuid={blockScope}
          includeCurrentUser
          mode="multiple"
        />
      );
    case DistributeMode.BlockAttGroup:
      return (
        <AttGroupSelect
          blockGuids={[blockScope]}
          mode="multiple"
          style={{ width: 224 }}
          maxTagCount={2}
        />
      );
    default:
      return null;
  }
}

function RemoveWorkDays({
  value,
  onChange,
}: {
  value?: number;
  onChange?: (value: number) => void;
}) {
  return (
    <Space>
      <div>节假日排期限定</div>
      <Radio.Group
        value={value}
        options={[
          { label: '不限定', value: 0 },
          { label: '节假日不排期', value: 1 },
          { label: '顺延至工作日', value: 2 },
        ]}
        onChange={value => {
          onChange && onChange(value as unknown as number);
        }}
      />
    </Space>
  );
}

function generateJobItemsDataByTaskType(
  planType: PlanType,
  planFormType: JobType,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  jobItems: any[],
  entities: Record<string, DeviceType>,
  ticketTypeEntities: Record<string, TicketType>,
  roomTypeEntities: Record<string, RoomType>
) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return jobItems.map(job => ({
        jobId: job.id,
        name: job.configName,
        itemType: job.itemType,
        scheduleType: planFormType,
        jobTypeCode: job.maintenanceType,
        jobTypeName:
          ticketTypeEntities[
            `${generateSpecificDataByTaskType(planType).ticketType}${job.maintenanceType}`
          ]?.taskValue,
        subJobTypeCode: job.deviceType,
        subJobTypeName: entities[job.deviceType]?.metaName,
      }));
    case PlanType.InspectionPlan:
      return jobItems.map(job => ({
        jobId: job.id,
        name: job.configName,
        itemType: job.itemType,
        scheduleType: planFormType,
        jobTypeCode: job.inspectType,
        jobTypeName:
          ticketTypeEntities[
            `${generateSpecificDataByTaskType(planType).ticketType}${job.inspectType}`
          ]?.taskValue,
        subJobTypeCode: job.subTypeCode,
        subJobTypeName:
          entities[job.subTypeCode]?.metaName ?? roomTypeEntities[job.subTypeCode]?.text,
      }));
    case PlanType.InventoryPlan:
      return jobItems.reduce((pre, cur) => {
        const jobs = cur.deviceTypes.map((device: string | number) => ({
          jobId: cur.id,
          name: cur.configName,
          itemType: cur.itemType,
          scheduleType: JobType.InventoryCount,
          jobTypeCode: cur.inventoryType.code,
          jobTypeName: cur.inventoryType.desc,
          subJobTypeCode: device,
          subJobTypeName: entities[device]?.metaName,
        }));
        return [...pre, ...jobs];
      }, []);

    default:
      return { ticketType: '', prefix: '', jobType: null };
  }
}

function generateSelectOptionsDataByTaskType(
  planType: PlanType,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  jobItems: any[],
  entities: Record<string, DeviceType>
): { value: string; label: string }[] {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return jobItems.map(job => ({
        value: job.deviceType,
        label: entities[job.deviceType]?.metaName,
      }));
    case PlanType.InspectionPlan:
      return jobItems
        .filter(job => entities[job.subTypeCode])
        .map(job => ({
          value: job.subTypeCode,
          label: entities[job.subTypeCode]?.metaName,
        }));
    case PlanType.InventoryPlan:
      return jobItems.reduce((pre, cur) => {
        const jobs = cur.deviceTypes
          .filter((device: string | number) => entities[device]?.numbered)
          .map((device: string | number) => ({
            value: device,
            label: entities[device]?.metaName,
          }));
        return [...pre, ...jobs];
      }, []);

    default:
      return [];
  }
}

function generateCyclesParams({
  periodUnit,
  allowTriggerTime,
  period,
  isRemove,
  days,
  periodMonthWhen,
  sortNum,
  atDayType,
  periodYearWhen,
  months,
}: {
  periodUnit: CyclesType;
  allowTriggerTime: moment.MomentInput[];
  period: number;
  isRemove: number;
  days: string[] | WeekType[] | null;
  periodMonthWhen: string;
  sortNum: number;
  atDayType: MonthlyWorkDayType;
  periodYearWhen: string;
  months: string[];
}) {
  const cycles = {} as Cycle;
  switch (periodUnit) {
    case CyclesType.None:
      cycles.periodUnit = periodUnit;
      cycles.period = 0;
      cycles.dayConfig = {
        days: allowTriggerTime.map(time => moment(time).format('YYYY-MM-DD HH:mm:ss')),
        isRemove: 0,
      };

      break;
    case CyclesType.Day:
      cycles.periodUnit = periodUnit;
      cycles.period = period;
      cycles.dayConfig = {
        days: null,
        isRemove: isRemove,
      };

      break;
    case CyclesType.Week:
      cycles.periodUnit = periodUnit;
      cycles.period = period;
      cycles.dayConfig = {
        days: days,
        isRemove: isRemove,
      };

      break;
    case CyclesType.Month:
      cycles.periodUnit = periodUnit;
      cycles.period = period;

      if (periodMonthWhen) {
        cycles.dayConfig = {
          days: days,
          isRemove: isRemove,
        };
      } else {
        cycles.atDayConfig = [
          {
            month: null,
            sortNum: sortNum,
            atDayType: atDayType,
            isLast: 0,
          },
        ];
      }
      break;
    case CyclesType.Year:
      cycles.periodUnit = periodUnit;
      cycles.period = period;

      if (periodYearWhen) {
        cycles.dayConfig = {
          days: days,
          isRemove: isRemove,
        };
      } else {
        cycles.atDayConfig = months.map(month => ({
          month: month,
          sortNum: sortNum,
          atDayType: atDayType,
          isLast: 0,
        }));
      }
      break;
  }

  return cycles;
}

function generateFormInitialValue(
  planType: PlanType,
  detail: PlanJSON,
  entities: Record<string, DeviceType>
) {
  return {
    ...generateCyclesRelateData(detail?.repeatCycle, detail?.repeatCycle?.periodUnit),
    ...generateSplitorRules(detail.splitor ?? []),
    ...generateDistributeRules(detail.blockScope),
    ...generateSpecifyTarget(entities, detail.scheduleSplitRangeList),
    guidePeriod: detail?.guidePeriod,
    name: detail.name,
    subJobType: detail.mopType,
    blockScope: detail.blockScope[0].blockGuid,
    status: detail.isActivated === PlanStatus.On,
    allowTriggerTime: Array.isArray(detail?.allowTriggerTime)
      ? detail.allowTriggerTime.map(time =>
          moment(
            detail?.repeatCycle?.periodUnit !== CyclesType.None
              ? `${moment().format('YYYY-MM-DD')} ${time}`
              : moment(time)
          )
        )
      : [],
    planFormType: [JobType.InventoryCount, JobType.DeviceInspection].includes(detail.planType)
      ? JOB_TYPE_TEXT_MAP[detail.planType]
      : detail.planType,
    periodUnit: detail.repeatCycle?.periodUnit,
    jobItems: generateFormInitialJobItemsByType(planType, detail.jobItemList ?? [], entities),
    sla: {
      sla: generateCorrectJobSla(detail.jobSla!, detail.slaUnit!),
      unit: detail.slaUnit!,
    },
    endTime: detail.endTime ? moment(detail.endTime) : undefined,
  };
}

function generateDistributeRules(distributeData: PlanBlock[]) {
  if (distributeData.length === 1) {
    if (distributeData[0].scopeType === DistributeMode.Block) {
      return { distributeMode: distributeData[0].scopeType };
    } else {
      return {
        distributeMode: distributeData[0].scopeType,
        executeRange:
          distributeData[0].scopeType === DistributeMode.BlockUser
            ? { value: distributeData[0].scopeFlag }
            : distributeData[0].scopeFlag,
      };
    }
  } else {
    return {
      distributeMode: distributeData[0].scopeType,
      executeRange: distributeData.map(item =>
        item.scopeType === DistributeMode.BlockUser ? { value: item.scopeFlag } : item.scopeFlag
      ),
    };
  }
}

function generateCyclesRelateData(cycles?: Cycle, periodUnit?: CyclesType) {
  if (!cycles) {
    return {};
  }
  switch (periodUnit) {
    case CyclesType.None:
      return { period: 0, isRemove: 0 };
    case CyclesType.Day:
      return { period: cycles.period, isRemove: cycles.dayConfig.isRemove };
    case CyclesType.Week:
      return {
        period: cycles.period,
        isRemove: cycles.dayConfig.isRemove,
        days: cycles.dayConfig.days,
      };
    case CyclesType.Month:
      if (cycles.dayConfig) {
        return {
          period: cycles.period,
          isRemove: cycles.dayConfig.isRemove,
          days: cycles.dayConfig.days,
          periodMonthWhen: true,
        };
      } else {
        return {
          period: cycles.period,
          atDayType: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0].atDayType : null,
          sortNum: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0]?.sortNum : 0,
          periodMonthWhen: false,
          isRemove: 0,
        };
      }
    case CyclesType.Year:
      if (cycles.dayConfig) {
        return {
          period: cycles.period,
          isRemove: cycles.dayConfig.isRemove,
          days: cycles.dayConfig.days,
          periodYearWhen: true,
        };
      } else {
        return {
          months: cycles.atDayConfig?.map(item => item.month),
          period: cycles.period,
          atDayType: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0].atDayType : null,
          sortNum: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0].sortNum : 0,
          periodYearWhen: false,
          isRemove: 0,
        };
      }
    default:
      return {};
  }
}

function generateSplitorRules(splitor: Splitor[]) {
  const splitorFormData: {
    splitFloorParams?: number;
    splitSpaceParams?: SplitorType;
    splitDeviceParams?: ThreeDeviceType;
    splitRule: SplitorType[];
  } = { splitRule: [], splitDeviceParams: ThreeDeviceType.FirstCategory };

  const spaceSplitor = splitor.filter(item =>
    [SplitorType.Room, SplitorType.Floor].includes(item.splitType)
  )[0];
  if (spaceSplitor) {
    splitorFormData.splitRule.push(SplitorType.Space);
    switch (spaceSplitor?.splitType) {
      case SplitorType.Room:
        splitorFormData.splitSpaceParams = SplitorType.Room;
        break;
      case SplitorType.Floor:
        splitorFormData.splitFloorParams = spaceSplitor.splitParam as number;
        splitorFormData.splitSpaceParams = SplitorType.Floor;
        break;
    }
  }

  const deviceSplitor = splitor.filter(item => item.splitType === SplitorType.DeviceType)[0];
  if (deviceSplitor) {
    splitorFormData.splitRule.push(SplitorType.DeviceType);
    splitorFormData.splitDeviceParams = deviceSplitor.splitParam as ThreeDeviceType;
  }

  return splitorFormData;
}

function generateFormInitialJobItemsByType(
  planType: PlanType,
  jobItemList: JobItem[],
  entities: DeviceType,
  blockGuid?: string
) {
  switch (planType) {
    case PlanType.InventoryPlan:
      const newData: {
        deviceTypes: string[];
        id: number;
        itemType: string;
        configName: string;
        inventoryType: { code: string; desc: string };
      }[] = [];
      const ids = uniq(jobItemList.map(({ jobId }) => jobId));
      ids.forEach(jobId => {
        const sameJobIdData = jobItemList.filter(item => item.jobId === jobId);
        const tmp = {
          deviceTypes: sameJobIdData.map(({ subJobTypeCode }) => {
            return subJobTypeCode;
          }),

          id: jobId,
          itemType: sameJobIdData[0].itemType,
          configName: sameJobIdData[0].name,
          inventoryType: { code: sameJobIdData[0].jobTypeCode, desc: sameJobIdData[0].jobTypeName },
        };
        newData.push(tmp);
      });
      return newData;
    case PlanType.MaintenancePlan:
      return jobItemList.map(job => ({
        itemType: job.itemType,
        id: job.jobId,
        deviceType: job.subJobTypeCode,
        configName: job.name,
        maintenanceType: job.jobTypeCode,
        effectType: job.itemType,
        effectDomain: job.itemType === ConfigRangeType.Block ? blockGuid : null,
      }));
    case PlanType.InspectionPlan:
      return jobItemList.map(job => ({
        // 因为设备类型的deviceType的code  是 1 开头的。更改为在三级分类接口中找 否为 Environment类型 包间中查找
        inspectSubject: entities[job.subJobTypeCode]
          ? InspectionObject.Device
          : InspectionObject.Environment,
        itemType: job.itemType,
        id: job.jobId,
        subTypeCode: job.subJobTypeCode,
        configName: job.name,
        inspectType: job.jobTypeCode,
        effectType: job.itemType,
        effectDomain: job.itemType === ConfigRangeType.Block ? blockGuid : null,
      }));
    default:
      return jobItemList;
  }
}

function handleEndDisabledDate(current: Moment) {
  return current.valueOf() < moment().startOf('day').valueOf();
}
function range(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
function disabledDateTime(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const hours = moment().hours();
  const minute = moment().minute();
  const choseHour = moment(current).hours();
  const isToday = moment(current).isSame(moment(), 'day');
  if (isToday) {
    if (choseHour === hours) {
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minute),
        disabledSeconds: () => [],
      };
    }
    return {
      disabledHours: () => range(0, hours),
      disabledMinutes: () => [],
      disabledSeconds: () => [],
    };
  }
  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}

function generateSpecifyTarget(
  entities: Record<string, DeviceType>,
  targets?: (SpecifyTarget & {
    flagValues?: Record<string, string[]>;
    DeviceTypeCodes?: string[];
  })[]
) {
  if (targets && Array.isArray(targets) && targets.length > 0) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const scheduleList: any[] = [];
    const shieldingList: any[] = [];
    const otherSchedule = targets.find(item => item.rangeType === 'OTHER');
    targets
      .filter(item => item.rangeType !== 'OTHER')
      .map(item => {
        return {
          ...item,
          flagValues: { [item.flagKey]: item.flagValue },
          DeviceTypeCodes: [item.flagKey],
        };
      })
      .forEach(item => {
        // uniFlagType === 'BLACK' 屏蔽对象  uniFlagType === 'WHITE' 指定对象
        const { uniFlag, rangeType, uniFlagType } = item;
        if (uniFlagType === 'WHITE') {
          if (!scheduleList.find(schedule => schedule.uniFlag === uniFlag)) {
            scheduleList.push({
              ...item,
              flagValue:
                rangeType === 'DEVICE' ? item.flagValues : item.flagValue.map(value => [value]),
            });
          } else {
            scheduleList[scheduleList.length - 1] = {
              ...scheduleList[scheduleList.length - 1],
              DeviceTypeCodes: scheduleList[scheduleList.length - 1].DeviceTypeCodes.concat(
                item.DeviceTypeCodes
              ),
              flagValue: { ...scheduleList[scheduleList.length - 1].flagValue, ...item.flagValues },
            };
          }
        }
        if (uniFlagType === 'BLACK') {
          if (!shieldingList.find(schedule => schedule.uniFlag === uniFlag)) {
            shieldingList.push({
              ...item,
              flagValue:
                rangeType === 'DEVICE' ? item.flagValues : item.flagValue.map(value => [value]),
            });
          } else {
            shieldingList[shieldingList.length - 1] = {
              ...shieldingList[shieldingList.length - 1],
              DeviceTypeCodes: shieldingList[shieldingList.length - 1].DeviceTypeCodes.concat(
                item.DeviceTypeCodes
              ),
              flagValue: {
                ...shieldingList[shieldingList.length - 1].flagValue,
                ...item.flagValues,
              },
            };
          }
        }
      });

    return {
      scheduleSplitRangeList: scheduleList,
      shieldingObjectList: shieldingList,
      checkShieldingObject: !!shieldingList.length,
      otherUniFlag: otherSchedule ? otherSchedule.uniFlag : nanoid(),
      otherSchedule: !!otherSchedule,
      otherCycleTimes: otherSchedule?.cycleTimes,
    };
  }
  return { otherUniFlag: nanoid() };
}

function isJobItemSpaceType(
  allDevice: {
    [key: string]: any;
  },
  code: string
) {
  return !!!allDevice[code];
}
function getDistributeModeOptions(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
    case PlanType.InspectionPlan:
    case PlanType.InventoryPlan:
      return DISTRIBUTE_MODE_OPTIONS.filter(item => item.value !== DistributeMode.DutyGroup);
    case PlanType.RiskCheckPlan:
      return DISTRIBUTE_MODE_OPTIONS.filter(item =>
        [DistributeMode.Block, DistributeMode.BlockRole].includes(item.value)
      );
    case PlanType.RiskCheckTicket:
      return DISTRIBUTE_MODE_OPTIONS.filter(item =>
        [DistributeMode.Block, DistributeMode.BlockRole, DistributeMode.BlockUser].includes(
          item.value
        )
      );
    case PlanType.DrillPlan:
      return DISTRIBUTE_MODE_OPTIONS.filter(
        item => ![DistributeMode.BlockAttGroup, DistributeMode.BlockUser].includes(item.value)
      );
    default:
      return [];
  }
}
