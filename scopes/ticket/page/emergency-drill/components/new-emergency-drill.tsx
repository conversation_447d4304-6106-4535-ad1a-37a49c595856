import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';

import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import {
  EditableFormInstance,
  EditableProTable,
  ProColumns,
} from '@manyun/base-ui.ui.editable-pro-table';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { DefaultOptionType } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { EMERGENCY_DRILL_TYPE_LIST } from '@manyun/ticket.model.ticket';
import { BackendEmergencyDrillType, EmergencyDrillTypeMap } from '@manyun/ticket.model.ticket';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import { createEmergencyDrill } from '@manyun/ticket.service.create-emergency-drill';
import type { PlanItemInfo } from '@manyun/ticket.service.create-emergency-drill';

import { EmergencyLevelSelect } from './emergency-level-select';
import { EmergencyMajorSelect } from './emergency-major-select';

export type PlanItemInfoList = (PlanItemInfo & {
  id: string;
  agentId: { value: number; label: string };
})[];

export type PlanItemTableItem = PlanItemInfo & {
  id: string;
  agentId: { value: number; label: string };
};

export function NewEmergencyDrill() {
  const editorFormRef = useRef<EditableFormInstance<any>>();
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [dataSource, setDataSource] = useState<PlanItemInfoList>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [fileList, setFileList] = useState<MixedUploadFile[]>([]);
  const history = useHistory();
  const [form] = Form.useForm();
  const { setFieldsValue } = form;
  const subType = Form.useWatch('subType', form);
  const location = Form.useWatch('location', form);
  const handleOk = async () => {
    const formValue = await form.validateFields();
    if (!dataSource?.length) {
      message.error('方案制定为空！');
      return;
    }
    setLoading(true);
    const { location, fileInfoList, description, level, specialty, user, ...rest } = formValue;
    const { error, data } = await createEmergencyDrill({
      ...rest,
      idcTag: generateIdcTag(location),
      blockGuid: location,
      planProperties: {
        level,
        description,
        specialty,
        principalId: user.value,
        principalName: user.label,
      },
      planItemInfoList: dataSource?.map((data, index) => {
        const { agentId, id, ...rest } = data;
        return { ...rest, agentId: agentId.value, agentName: agentId.label, sequence: index };
      }),
      fileInfoList: fileList?.map((file: McUploadFile) => ({
        ...McUploadFile.toApiObject(file),
        fileType: null,
      })),
    });
    if (error) {
      message.error(error.message);
      setLoading(false);

      return;
    }

    message.success('创建应急演练工单成功');
    setLoading(false);
    history.push(generateTicketLocation({ ticketType: 'emergency_drill', id: data }));
  };
  useEffect(() => {
    setFieldsValue({
      taskTitle: `${subType ? EmergencyDrillTypeMap[subType as BackendEmergencyDrillType] : ''}${
        location ?? ''
      }`,
    });
  }, [subType, location, setFieldsValue]);
  return (
    <Space direction="vertical" style={{ width: '100%' }} size={12}>
      <Card title="基本信息">
        <Form
          form={form}
          style={{ width: 520 }}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          initialValues={{ subType: BackendEmergencyDrillType.DRILL }}
        >
          <Form.Item
            label="类型"
            name="subType"
            rules={[{ required: true, message: '请选择工单子类型' }]}
          >
            <Select style={{ width: 216 }}>{generateEmergencyTypeOptions()}</Select>
          </Form.Item>
          <Form.Item
            label="位置"
            name="location"
            rules={[{ required: true, message: '请选择位置' }]}
          >
            <LocationTreeSelect authorizedOnly style={{ width: 216 }} disabledTypes={['IDC']} />
          </Form.Item>
          <Form.Item
            label="专业"
            name="specialty"
            rules={[{ required: true, message: '请选择专业' }]}
          >
            <EmergencyMajorSelect style={{ width: 216 }} />
          </Form.Item>
          <Form.Item label="等级" name="level" rules={[{ required: true, message: '请选择等级' }]}>
            <EmergencyLevelSelect style={{ width: 216 }} />
          </Form.Item>

          <Form.Item
            label="负责人"
            name="user"
            rules={[{ required: true, message: '请选择负责人' }]}
          >
            <UserSelect style={{ width: 216 }} />
          </Form.Item>
          <Form.Item
            label="工单标题"
            name="taskTitle"
            rules={[
              { required: true, message: '请输入工单标题' },
              { max: 20, message: '最多仅允许输入20个字！' },
            ]}
          >
            <Input style={{ width: 395 }} />
          </Form.Item>
          <Form.Item
            label="说明"
            name="description"
            rules={[{ max: 50, message: '最多仅允许输入50个字！' }]}
          >
            <Input.TextArea style={{ width: 395 }} />
          </Form.Item>
          <Form.Item label="附件" name="fileInfoList">
            <Upload
              accept=".png,.jpg,.jpeg,.image,.xls,.xlsx,.doc,.docx,.pdf"
              fileList={fileList}
              maxCount={10}
              onChange={({ file, fileList }) => {
                if (
                  file.status === 'uploading' &&
                  fileList.filter(file => file.status !== 'uploading').length === 10
                ) {
                  message.error('上传附件数量最多10个!');
                  return;
                }
                setFileList(fileList);
              }}
            >
              <Space direction="vertical">
                <Button icon={<DownloadOutlined />}>点此上传</Button>
                <Typography.Text type="secondary">
                  支持扩展名：.png,.jpg,.jpeg,.image,.xls,.xlsx,.doc,.docx,.pdf
                </Typography.Text>
              </Space>
            </Upload>
          </Form.Item>
        </Form>
      </Card>

      <Card title="方案制定" style={{ marginBottom: 36 }}>
        <EditableProTable
          rowKey="id"
          editableFormRef={editorFormRef}
          value={dataSource}
          onChange={value => {
            setDataSource(value);
          }}
          scroll={{ x: 'max-content' }}
          columns={columns({ editorFormRef, dataSource, setDataSource })}
          recordCreatorProps={{
            position: 'top',
            creatorButtonText: '添加方案',
            record: () => ({
              id: shortid(),
            }),
          }}
          editable={{
            type: 'single',
            editableKeys,
            onSave: async (rowKey, data, row) => {
              if (dataSource.length >= 100) {
                message.error('添加方案的个数上限为100');
                return Promise.reject();
              }

              return Promise.resolve();
            },
            onChange: keys => {
              setEditableRowKeys(keys);
            },
            actionRender: (row, config, dom) => [dom.save, dom.cancel],
          }}
        />
      </Card>
      <FooterToolBar>
        <Space>
          <Button type="primary" loading={loading} onClick={handleOk}>
            提交
          </Button>
          <Button
            disabled={loading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}
const columns: ({
  editorFormRef,
  dataSource,
  setDataSource,
}: {
  editorFormRef: any;
  dataSource: PlanItemInfoList;
  setDataSource: (dataSource: PlanItemInfoList) => void;
}) => ProColumns<any>[] = ({ editorFormRef, dataSource, setDataSource }) => {
  return [
    {
      title: '阶段',
      dataIndex: 'stage',
      fixed: 'left',
      valueType: 'text',
      formItemProps: () => {
        return {
          rules: [
            { required: true, message: '阶段为必填项' },
            { max: 8, message: '最多仅允许输入8个字！' },
          ],
        };
      },
    },
    {
      title: '方案内容',
      dataIndex: 'planContent',
      valueType: 'text',
      formItemProps: () => {
        return {
          rules: [
            { required: true, message: '方案内容为必填项' },
            { max: 200, message: '最多仅允许输入200个字！' },
          ],
        };
      },
    },
    {
      title: '执行人员',
      dataIndex: 'agentId',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '执行人员为必填项' }],
        };
      },
      renderFormItem: (_, { recordKey, record }) => {
        return (
          <UserSelect
            onChange={(_, nodeValue: DefaultOptionType | DefaultOptionType[] | any) => {
              if (recordKey) {
                const user = nodeValue['data-user'];
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  agentId: {
                    label: user.name,
                    value: user.id,
                    name: user.name,
                  },
                });
              }
            }}
          />
        );
      },
      render: (_, { agentId }) => {
        return <UserLink id={agentId?.value} />;
      },
    },

    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: 244,
      render: (text, record, _, action) => [
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </a>,
        // eslint-disable-next-line jsx-a11y/anchor-is-valid
        <a
          key="delete"
          onClick={() => {
            const filterDataSource = dataSource?.filter(
              (item: { id: string }) => item.id !== record.id
            );
            setDataSource(filterDataSource);
          }}
        >
          删除
        </a>,

        <Button
          key="moveUp"
          type="link"
          compact
          disabled={isFirstItem(dataSource, record.id)}
          onClick={() => {
            const currentIndex = dataSource!.findIndex(
              (item: { id: string }) => item.id === record.id
            );
            const preIndex = currentIndex - 1;
            setDataSource(swapItem(dataSource, preIndex, currentIndex));
          }}
        >
          上移一层
        </Button>,
        <Button
          key="moveDown"
          compact
          type="link"
          disabled={isLastItem(dataSource, record.id)}
          onClick={() => {
            const currentIndex = dataSource!.findIndex(
              (item: { id: string }) => item.id === record.id
            );
            const postIndex = currentIndex + 1;
            setDataSource(swapItem(dataSource, postIndex, currentIndex));
          }}
        >
          下移一层
        </Button>,
      ],
    },
  ];
};
const generateIdcTag = (location: string) => {
  return !location?.includes('.') ? location : location.split('.')[0];
};
const swapItem = (dataSource: PlanItemInfoList, index1: number, index2: number) => {
  const tempList = [...dataSource];
  const temp = tempList[index1];
  tempList[index1] = tempList[index2];
  tempList[index2] = temp;
  return tempList;
};
const isFirstItem = (dataSource: PlanItemInfoList, id: string) => {
  return dataSource?.findIndex((item: { id: string }) => item.id === id) === 0;
};

const isLastItem = (dataSource: PlanItemInfoList, id: string) => {
  return dataSource?.findIndex((item: { id: string }) => item.id === id) === dataSource!.length - 1;
};

export const generateEmergencyTypeOptions = () => {
  return EMERGENCY_DRILL_TYPE_LIST.map(emergencyType => {
    return (
      <Select.Option key={emergencyType.key} label={emergencyType.label}>
        {emergencyType.label}
      </Select.Option>
    );
  });
};
