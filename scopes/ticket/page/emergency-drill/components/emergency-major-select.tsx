import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { EMERGENCY_DRILL_MAJOR_LIST } from '@manyun/ticket.model.ticket';

export const generateEmergencyMajorOptions = () => {
  return EMERGENCY_DRILL_MAJOR_LIST.map(emergencyMajor => {
    return (
      <Select.Option key={emergencyMajor.key} label={emergencyMajor.label}>
        {emergencyMajor.label}
      </Select.Option>
    );
  });
};
export type EmergencyMajorSelectProps = SelectProps;

export const EmergencyMajorSelect = React.forwardRef(
  ({ ...selectProps }: EmergencyMajorSelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <Select ref={ref} {...selectProps} allowClear>
        {generateEmergencyMajorOptions()}
      </Select>
    );
  }
);
