import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { EMERGENCY_DRILL_LEVEL_LIST } from '@manyun/ticket.model.ticket';

export const generateEmergencyLevelOptions = () => {
  return EMERGENCY_DRILL_LEVEL_LIST.map(emergencyLevel => {
    return (
      <Select.Option key={emergencyLevel.key} label={emergencyLevel.label}>
        {emergencyLevel.label}
      </Select.Option>
    );
  });
};
export type EmergencyLevelSelectProps = SelectProps;

export const EmergencyLevelSelect = React.forwardRef(
  ({ ...selectProps }: EmergencyLevelSelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <Select ref={ref} {...selectProps} allowClear>
        {generateEmergencyLevelOptions()}
      </Select>
    );
  }
);
