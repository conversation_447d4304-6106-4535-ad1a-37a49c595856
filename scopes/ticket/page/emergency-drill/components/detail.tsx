import React, { useEffect, useState } from 'react';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { UserLink } from '@manyun/auth-hub.ui.user';
import {
  BackendEmergencyDrillLevel,
  BackendEmergencyDrillMajor,
  EmergencyDrillLevelMap,
  EmergencyDrillMajorMap,
} from '@manyun/ticket.model.ticket';
import type { BackendTicket } from '@manyun/ticket.model.ticket';
import type { PlanProperties } from '@manyun/ticket.service.create-emergency-drill';
import { fetchEmergencyDrillItemList } from '@manyun/ticket.service.fetch-emergency-drill-item-list';

export type DetailProps = {
  taskNo: string;
  basicInfo: BackendTicket;
};

export function Detail({ taskNo, basicInfo }: DetailProps) {
  const { taskProperties } = basicInfo;
  // eslint-disable-next-line no-new-func
  const taskPropertiesJSON = new Function('return ' + taskProperties)();
  const [dataSource, setDataSource] = useState<PlanProperties[] | undefined>([]);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  useEffect(() => {
    const getPlanList = async () => {
      setTableLoading(true);
      const { error, data } = await fetchEmergencyDrillItemList({ taskNo });
      if (error) {
        message.error(error.message);
        return;
      }

      setDataSource(data.data);
      setTableLoading(false);
    };
    getPlanList();
  }, [taskNo]);
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Descriptions bordered={false} column={4}>
        <Descriptions.Item label="专业">
          {EmergencyDrillMajorMap[taskPropertiesJSON?.specialty as BackendEmergencyDrillMajor]}
        </Descriptions.Item>
        <Descriptions.Item label="等级">
          {EmergencyDrillLevelMap[taskPropertiesJSON?.level as BackendEmergencyDrillLevel]}
        </Descriptions.Item>
        <Descriptions.Item label="负责人">
          <UserLink id={taskPropertiesJSON?.principalId} />
        </Descriptions.Item>
        <Descriptions.Item label="说明">
          {taskPropertiesJSON?.description ?? '--'}
        </Descriptions.Item>
      </Descriptions>

      <Table
        dataSource={dataSource}
        scroll={{ x: 'max-content' }}
        loading={tableLoading}
        columns={columns}
      />
    </Space>
  );
}

const columns = [
  {
    title: '阶段',
    fixed: 'left' as 'left',
    dataIndex: 'stage',
  },

  {
    title: '方案内容',
    dataIndex: 'planContent',
  },
  {
    title: '执行人员',
    fixed: 'right' as 'right',
    dataIndex: 'agentId',
    render: (text: number) => {
      return <UserLink id={text} />;
    },
  },
];
