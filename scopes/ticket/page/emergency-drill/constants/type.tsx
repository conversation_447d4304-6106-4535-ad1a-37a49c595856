// 风险子类型
export const RISK_REGISTER_TYPE_KEY_MAP = {
  dianli: 'dian<PERSON>',
  nuantong: 'nuantong',
  af: 'af',
  xf: 'xf',
  jzw: 'jzw',
  bx: 'bx',
  yy: 'yy',
};

export const RISK_REGISTER_TEXT_MAP = {
  [RISK_REGISTER_TYPE_KEY_MAP.dianli]: '电力',
  [RISK_REGISTER_TYPE_KEY_MAP.nuantong]: '暖通',
  [RISK_REGISTER_TYPE_KEY_MAP.af]: '安防',
  [RISK_REGISTER_TYPE_KEY_MAP.xf]: '消防',
  [RISK_REGISTER_TYPE_KEY_MAP.jzw]: '建筑物及外围',
  [RISK_REGISTER_TYPE_KEY_MAP.bx]: '综合布线',
  [RISK_REGISTER_TYPE_KEY_MAP.yy]: '运营能力',
};

// 资产类型
export const ASSET_TYPE_KEY_MAP = {
  CLIENT_ASSET: 'CLIENT_ASSET',
  IDC_ASSET: 'IDC_ASSET',
};
export const ASSET_TYPE_TEXT_MAP = {
  [ASSET_TYPE_KEY_MAP.CLIENT_ASSET]: '客户资产',
  [ASSET_TYPE_KEY_MAP.IDC_ASSET]: '机房资产',
};
export const ASSET_TYPE_OPTIONS = [
  {
    value: ASSET_TYPE_KEY_MAP.CLIENT_ASSET,
    label: ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAP.CLIENT_ASSET],
  },
  {
    value: ASSET_TYPE_KEY_MAP.IDC_ASSET,
    label: ASSET_TYPE_TEXT_MAP[ASSET_TYPE_KEY_MAP.IDC_ASSET],
  },
];
//
// 工单状态
export const TICKET_STATUS_KEY_MAP = {
  INIT: '4',
  WAITTAKEOVER: '3',
  FINISH: '1',
  PROCESSING: '2',
  FAILURE: '0',
  UNDO: '5',
};

// 出入门类型
export const ACCESS_TYPE_KEY_MAP = {
  GO_OUT: 'GO_OUT',
  GO_IN: 'GO_IN',
};
