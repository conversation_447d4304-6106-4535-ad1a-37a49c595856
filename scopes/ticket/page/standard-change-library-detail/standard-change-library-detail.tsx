import dayjs from 'dayjs';
import moment from 'moment';
import React, { useEffect } from 'react';
import { useParams } from 'react-router';
import { Link, useHistory } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileList } from '@manyun/base-ui.ui.file-list';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  useDeferChangeTemplate,
  useInvalidateChangeTemplate,
  useLazyChangeTemplate,
} from '@manyun/ticket.gql.client.tickets';
import {
  StandardChangeLibraryStatus,
  statusColorMaps,
  statusTextMaps,
} from '@manyun/ticket.model.ticket';
import {
  generateChangeTicketDetail,
  generateStandardChangeLibraryEditLocation,
} from '@manyun/ticket.route.ticket-routes';

import { ApproveView } from './components/approve-view';

export function StandardChangeLibraryDetail() {
  // const [authorized] = useAuthorized({ checkByUserId: step1.creatorId });
  // const [operatorAuthorized] = useAuthorized({ checkByUserId: step1.operatorId });
  const { id } = useParams<{ id: string }>();
  const [, { checkUserId, checkCode }] = useAuthorized();
  const history = useHistory();

  const [getChangeTemplate, { data, refetch }] = useLazyChangeTemplate();

  const [deferChangeTemplate, { loading: deferLoading }] = useDeferChangeTemplate({
    onCompleted: data => {
      if (data?.deferChangeTemplate?.success) {
        message.info('延期申请已提交，请等待审批');
        refetch();
      } else {
        message.error(data?.deferChangeTemplate?.message);
      }
    },
  });

  const [invalidateChangeTemplate, { loading: invalidateLoading }] = useInvalidateChangeTemplate({
    onCompleted: data => {
      if (data?.invalidateChangeTemplate?.success) {
        refetch();
      } else {
        message.error(data?.invalidateChangeTemplate?.message);
      }
    },
  });

  useEffect(() => {
    getChangeTemplate({ variables: { templateId: id } });
  }, [id, getChangeTemplate]);

  if (!data?.changeTemplate?.data) {
    return <Spin />;
  }

  const { data: templateInfo } = data.changeTemplate;
  const isCreater = checkUserId(templateInfo.creatorId);
  const isDefer = checkCode('element_defer-standard-change-library');
  // 修改
  const isEdit = checkCode('element_edit-standard-change-library');
  const isInvalidate = checkCode('element_invalidate-standard-change-library');

  return (
    <>
      <Card
        title="模版信息"
        bordered={false}
        headStyle={{ borderWidth: 0 }}
        bodyStyle={{ paddingTop: 0, paddingBottom: 0 }}
      >
        <Descriptions column={4} contentStyle={{ overflow: 'hidden', paddingRight: 16 }}>
          <Descriptions.Item label="模版名称">
            <Typography.Text ellipsis={{ tooltip: templateInfo.templateName }}>
              {templateInfo.templateName}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="位置">{templateInfo.availArea}</Descriptions.Item>
          <Descriptions.Item label="状态">
            <Tag color={statusColorMaps[templateInfo.templateStatus]}>
              {statusTextMaps[templateInfo.templateStatus]}
            </Tag>
          </Descriptions.Item>
          <Descriptions.Item label="模版有效期">
            {templateInfo.availDate ? dayjs(templateInfo.availDate).format('YYYY-MM-DD') : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="变更等级">
            {templateInfo.riskLevel ? (
              <MetaTypeText code={templateInfo.riskLevel} metaType={MetaType.CHANGE_LEVEL} />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="变更专业">
            {templateInfo.reason ? (
              <MetaTypeText code={templateInfo.reason} metaType={MetaType.CHANGE_REASON} />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="变更类型">
            {templateInfo.changeType ? (
              <MetaTypeText code={templateInfo.changeType} metaType={MetaType.CHANGE} />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="一般变更实施记录">
            <Typography.Text ellipsis={{ tooltip: templateInfo.sourceChangeTitle }}>
              <Link
                target="_blank"
                to={{
                  pathname: generateChangeTicketDetail({
                    id: templateInfo.sourceChangeId,
                  }),
                }}
              >
                {templateInfo.sourceChangeTitle}
              </Link>
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="创建人">
            <UserLink userId={templateInfo.creatorId} external />
          </Descriptions.Item>
          <Descriptions.Item label="创建时间">
            {dayjs(templateInfo.gmtCreate).format('YYYY-MM-DD HH:mm')}
          </Descriptions.Item>
          <Descriptions.Item label="生效时间">
            {templateInfo.effTime ? dayjs(templateInfo.effTime).format('YYYY-MM-DD HH:mm') : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="模版录入">
            {templateInfo.workFlowId ? (
              <ApproveView
                id={templateInfo.templateId}
                instId={templateInfo.workFlowId}
                processType="STANDARD_CHANGE_TEMPLATE"
                taskStatus={templateInfo.templateStatus as StandardChangeLibraryStatus}
                onCallBack={() => refetch()}
              />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          {templateInfo.postponeApprovalId && (
            <Descriptions.Item label="模版延期">
              <ApproveView
                id={templateInfo.templateId}
                instId={templateInfo.postponeApprovalId}
                processType="STANDARD_CHANGE_TEMPLATE_POSTPONE"
                taskStatus={templateInfo.templateStatus as StandardChangeLibraryStatus}
                onCallBack={() => refetch()}
              />
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>
      <Card bordered={false} bodyStyle={{ paddingTop: 8 }}>
        <FileList
          title="变更方案"
          files={templateInfo.fileInfoList ?? []}
          groups={[
            {
              title: '文件',
              fileTypes: ['others', 'pdf'],
            },
            {
              title: '图片',
              fileTypes: ['image', 'video'],
              previewable: true,
              showName: true,
            },
          ]}
        />
      </Card>
      <Card
        title="操作记录"
        bordered={false}
        headStyle={{ borderWidth: 0 }}
        style={{ marginBottom: 58 }}
        bodyStyle={{ paddingTop: 0 }}
      >
        <OperationLogTable
          defaultSearchParams={{
            targetId: id,
            targetType: 'CHANGE_TEMPLATE',
          }}
          showColumns={['serialNumber', 'modifyType']}
          isTargetIdEqual={(targetId: string) => {
            return targetId === id;
          }}
        />
      </Card>

      {templateInfo.templateStatus !== StandardChangeLibraryStatus.Approving &&
        templateInfo.templateStatus !== StandardChangeLibraryStatus.YqApproving && (
          <FooterToolBar>
            <Space size={16}>
              {isCreater &&
                isDefer &&
                (templateInfo.templateStatus === StandardChangeLibraryStatus.Expire ||
                  (templateInfo.templateStatus === StandardChangeLibraryStatus.Available &&
                    moment(templateInfo.availDate).diff(moment().startOf('day'), 'days') <=
                      15)) && (
                  <Button
                    disabled={deferLoading}
                    onClick={() => {
                      deferChangeTemplate({
                        variables: { templateId: templateInfo.templateId },
                      });
                    }}
                  >
                    延期
                  </Button>
                )}

              {isEdit &&
                isCreater &&
                (templateInfo.templateStatus === StandardChangeLibraryStatus.Expire ||
                  templateInfo.templateStatus === StandardChangeLibraryStatus.Available) && (
                  <Button
                    onClick={() =>
                      history.push(
                        generateStandardChangeLibraryEditLocation({
                          id: encodeURIComponent(templateInfo.templateId),
                        })
                      )
                    }
                  >
                    修改
                  </Button>
                )}
              {isCreater &&
                isInvalidate &&
                templateInfo.templateStatus === StandardChangeLibraryStatus.Available && (
                  <Popconfirm
                    title="确认失效此模版？"
                    style={{ width: 290 }}
                    okText="确认失效"
                    placement="topRight"
                    okButtonProps={{ disabled: invalidateLoading }}
                    onConfirm={() =>
                      invalidateChangeTemplate({
                        variables: { templateId: templateInfo.templateId },
                      })
                    }
                  >
                    <Button disabled={invalidateLoading}>失效</Button>
                  </Popconfirm>
                )}
            </Space>
          </FooterToolBar>
        )}
    </>
  );
}
