import React, { useCallback, useEffect } from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { useLazyBusinessOrderApprovalDetail } from '@manyun/bpm.gql.client.approval';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import { StandardChangeLibraryStatus } from '@manyun/ticket.model.ticket';

export type ApproveViewProps = {
  instId: string;
  id: string;
  processType: string;
  taskStatus: StandardChangeLibraryStatus;
  onCallBack: () => void;
};

export function ApproveView({ instId, id, processType, taskStatus, onCallBack }: ApproveViewProps) {
  const [getApprovalDetail, { data: approvalDetailData }] = useLazyBusinessOrderApprovalDetail();
  const fetchApprovalDetail = useCallback(() => {
    getApprovalDetail({
      variables: {
        instId,
        permissionType: 'STANDARD_CHANGE_TEMPLATE',
      },
    });
  }, [instId, getApprovalDetail]);

  useEffect(() => {
    fetchApprovalDetail();
  }, [fetchApprovalDetail, taskStatus]);

  if (!approvalDetailData?.businessOrderApprovalDetail) {
    return;
  }

  return (
    <Space>
      <ApprovalRecordsDropdown
        businessOrderInfo={{
          taskNumber: id,
          type: processType,
          approvalPermissionType: 'STANDARD_CHANGE_TEMPLATE',
          status: taskStatus,
        }}
      />

      <AwaitOperationPeopleTag bpmInstance={approvalDetailData.businessOrderApprovalDetail} />
      {[StandardChangeLibraryStatus.Approving, StandardChangeLibraryStatus.YqApproving].includes(
        taskStatus
      ) && (
        <ApprovalOperationButtons
          baseInfo={approvalDetailData.businessOrderApprovalDetail as unknown as BpmInstance}
          getDetail={() => {
            onCallBack();
            fetchApprovalDetail();
          }}
        />
      )}
    </Space>
  );
}
