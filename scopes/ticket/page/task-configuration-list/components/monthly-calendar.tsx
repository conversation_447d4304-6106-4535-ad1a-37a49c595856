import { DeleteOutlined, EditOutlined } from '@ant-design/icons';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Link, Prompt } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { FullCalendar } from '@manyun/base-ui.ui.full-calendar';
import type { FullCalendarReact } from '@manyun/base-ui.ui.full-calendar';
import { message } from '@manyun/base-ui.ui.message';
import { notification } from '@manyun/base-ui.ui.notification';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useTaskData } from '@manyun/ticket.context.task-data';
import type { MonthlyTaskQueryParams } from '@manyun/ticket.context.task-data';
import type { TaskJSON } from '@manyun/ticket.model.task';
import { PlanLayer, PlanType, Task, TaskEffectStatus } from '@manyun/ticket.model.task';
import { TaskQueryFilter, TaskStatusBar } from '@manyun/ticket.page.task-configuration-list';
import {
  generateDrillPlanDetailRoutePath,
  generateInspectionPlanDetailRoutePath,
  generateInventoryPlanDetailRoutePath,
  generateMaintainPlanDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { batchUpdateCalendarTask } from '@manyun/ticket.service.batch-update-calendar-task';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { TaskRelateTicketsModal } from './task-relate-tickets-modal';
import type { TaskRelateTicketsModalProps } from './task-relate-tickets-modal';

export type CalendarEvent = {
  id: string;
  start: string;
  title: string;
  editable: boolean;

  extendedProps: {
    taskId: number;
    endTime: string | null;
    effectStatus: TaskEffectStatus;
    jobSla: number;
    jumpHolidayEffectTime?: string;
  };
};

export type MonthlyCalendarProps = {
  planType: PlanType;
  isEditable: boolean;
  saveTwiceRef: { current: boolean };
  setIsEditable: (v: boolean) => void;
};

type TargetEvent = {
  blackDateTime: string;
  whiteDateTime?: string;
  schId: number;
  eventId: string;
};
export function MonthlyCalendar({
  planType,
  isEditable,
  saveTwiceRef,
  setIsEditable,
}: MonthlyCalendarProps) {
  const calendarRef = useRef<FullCalendarReact>(null);
  const deleteEventItem = useRef<TargetEvent[]>([]);
  // 用于保存最原始的排期信息。构建排期id和排期最初的时间的映射关系
  const initialEventsRef = useRef<Record<string, string>>({});
  // 用于保存修改状态下排期与最新时间的映射关系，构建排期id和排期最新的时间以及关联计划id的映射关系
  const latestEventStartTimeRef = useRef<Record<string, { start: string; taskId: number }>>({});
  // 用于保存特殊类型的排期信息【配了顺延至节假日的排期】。构建排期id和原始排期时间的映射关系
  const originEventsTimeRef = useRef<Record<string, string>>({});
  const currentEventsRef = useRef<CalendarEvent[]>([]);

  const [
    { month, monthlyTasks, monthlyTasksLoading, monthlyCalendarQueryParams },
    { toggleMonthlyTasks },
  ] = useTaskData();
  const [saveLoading, setSaveLoading] = useState(false);
  // @ts-ignore return type error
  const [, { checkCode }] = useAuthorized();
  const [initialDate, setInitialDate] = useState<{ start: string; end: string }>({
    start: moment(month).startOf('month').format('YYYY-MM-DD'),
    end: moment(month).add(1, 'month').startOf('month').format('YYYY-MM-DD'),
  });
  const setInitialDateCallback = useCallback((month: string) => {
    setInitialDate({
      start: moment(month).startOf('month').format('YYYY-MM-DD'),
      end: moment(month).add(1, 'month').startOf('month').format('YYYY-MM-DD'),
    });
  }, []);
  useEffect(() => {
    if (monthlyTasksLoading) {
      /** 加载时，说明月度行事历被重新查询了 */
      setIsEditable(false);
    }
  }, [monthlyTasksLoading]);
  useEffect(() => {
    setInitialDateCallback(month);
  }, [month, setInitialDateCallback]);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [taskId, setTaskId] = useState<number>();
  const [effectTime, setEffectTime] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);
  useEffect(() => {
    let calendarEvents: CalendarEvent[] = [];
    if (monthlyTasks.length > 0) {
      calendarEvents = monthlyTasks.reduce<CalendarEvent[]>((pre, cur) => {
        const events = cur.calendarExecuteInfoSet.map((item: TaskJSON) => ({
          id: `${item.effectTime}${item.id}`,
          start: Task.fromJSON(item).getFormattedEffectedTime(),
          title: `${moment(item.effectTime).format('HH:mm')} ${item.blockGuid} ${item.name}`,
          extendedProps: {
            endTime: Task.fromJSON(item).getFormattedEndTime(),
            taskId: item.id,
            effectStatus: item.effectStatus,
            jobSla: item.jobSla,
            jumpHolidayEffectTime: Task.fromJSON(item).getFormattedJumpHolidayEffectTime(),
          },
          editable: false,
        }));
        return [...pre, ...events];
      }, []);
    }

    setEvents(calendarEvents);
    calendarEvents.forEach(item => {
      initialEventsRef.current[`${item.id}`] = item.start;
      if (item.extendedProps.jumpHolidayEffectTime) {
        originEventsTimeRef.current[`${item.id}`] = item.extendedProps.jumpHolidayEffectTime;
      }
    });
    currentEventsRef.current = calendarEvents;
  }, [monthlyTasks]);

  useDeepCompareEffect(() => {
    const calendarEvents = events.map(event => ({
      ...event,
      editable: event.extendedProps.effectStatus === TaskEffectStatus.Ineffective && isEditable,
    }));
    setEvents(calendarEvents);
    currentEventsRef.current = calendarEvents;
  }, [isEditable, events]);
  const checkTaskModifyCode = useCallback(() => {
    switch (planType) {
      case PlanType.MaintenancePlan:
        return checkCode('element_maintain-monthly-calendar-update');
      case PlanType.InspectionPlan:
        return checkCode('element_inspection-monthly-calendar-update');
      case PlanType.InventoryPlan:
        return checkCode('element_inventory-monthly-calendar-update');
      case PlanType.DrillPlan:
        return checkCode('element_drill-monthly-calendar-update');
      default:
        return false;
    }
  }, [planType, checkCode]);
  const checkTaskDeleteCode = useCallback(() => {
    switch (planType) {
      case PlanType.MaintenancePlan:
        return checkCode('element_maintain-monthly-calendar-delete');
      case PlanType.InspectionPlan:
        return checkCode('element_inspection-monthly-calendar-delete');
      case PlanType.InventoryPlan:
        return checkCode('element_inventory-monthly-calendar-delete');
      case PlanType.DrillPlan:
        return checkCode('element_drill-monthly-calendar-delete');
      default:
        return false;
    }
  }, [planType, checkCode]);
  const setLatestEventStartItem = (id: string, start: string, taskId: number) => {
    latestEventStartTimeRef.current[`${id}`] = { start, taskId };
  };
  const handleEventClick = (id: string, type: 'delete' | 'edit', newStartTime?: Moment) => {
    // 获取 FullCalendar 的 API 实例
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      // 使用 getEventById 方法获取事件对象
      const event = calendarApi.getEventById(id);
      if (event) {
        const schId = event.extendedProps.taskId;

        if (type === 'delete') {
          deleteEventItem.current.push({
            eventId: event.id,
            schId,
            blackDateTime: moment(event.start).format('YYYY-MM-DD HH:mm:ss'),
            whiteDateTime: undefined,
          });
          // 将被删除的任务从ref中移除。
          currentEventsRef.current = currentEventsRef.current.filter(item => {
            return item.id !== event.id;
          });
          event?.remove();
        } else {
          if (newStartTime) {
            event.setStart(newStartTime.toDate());
            event.setEnd(null);
            const [, blockGuid, name] = event.title.split(' ');
            event.setProp('title', `${newStartTime.format('HH:mm')} ${blockGuid} ${name}`);
            setLatestEventStartItem(
              event.id,
              moment(event.start).format('YYYY-MM-DD HH:mm:ss'),
              schId
            );
            currentEventsRef.current = currentEventsRef.current.map(item => {
              if (item.id === event.id) {
                return {
                  ...item,
                  start: moment(event.start).format('YYYY-MM-DD HH:mm'),
                };
              }
              return item;
            });
          }
        }
      }
    }
  };
  const saveChanges = async () => {
    // setLatestEventStartTiem(event.id, moment(event.start).format('YYYY-MM-DD HH:mm:ss'));

    if (
      Object.keys(latestEventStartTimeRef.current).length > 0 ||
      deleteEventItem.current.length > 0
    ) {
      const deleteEventItems = deleteEventItem.current
        .map(task => {
          if (initialEventsRef.current[task.eventId]) {
            return {
              ...task,
              blackDateTime: initialEventsRef.current[task.eventId],
            };
          }
          return task;
        })
        .map(item => {
          const { eventId, ...rest } = item;
          return { ...rest };
        });
      const changeItemWithoutDeleteKeys = Object.keys(latestEventStartTimeRef.current).filter(
        key => {
          // 被删除的排期要过滤掉。只保留没有删除的。
          return deleteEventItem.current.findIndex(item => item.eventId === key) === -1;
        }
      );
      const trueChangeEventItems = changeItemWithoutDeleteKeys
        .map(key => ({
          schId: latestEventStartTimeRef.current[key].taskId,
          blackDateTime: initialEventsRef.current[key],
          whiteDateTime: latestEventStartTimeRef.current[key].start,
        }))
        .filter(item => item.blackDateTime !== item.whiteDateTime);
      const deleteOriginEvents: Omit<TargetEvent, 'eventId'>[] = changeItemWithoutDeleteKeys.reduce(
        (pre, cur) => {
          if (originEventsTimeRef.current[cur]) {
            pre.push({
              schId: latestEventStartTimeRef.current[cur].taskId,
              blackDateTime: originEventsTimeRef.current[cur],
            });
          }
          return pre;
        },
        [] as Omit<TargetEvent, 'eventId'>[]
      );
      const changeExecuteList = [
        ...trueChangeEventItems,
        ...deleteEventItems,
        ...deleteOriginEvents,
      ];
      if (changeExecuteList.length > 0) {
        setSaveLoading(true);
        const { error } = await batchUpdateCalendarTask({
          changeExecuteList,
        });
        setSaveLoading(false);
        if (error) {
          message.error(error.message);
          // 接口若是调用失败，则要清空之前的操作。
          deleteEventItem.current = [];
          latestEventStartTimeRef.current = {};
          setIsEditable(false);
          saveTwiceRef.current = false;
        } else {
          message.success('保存成功');
          let month;
          if (Object.keys(latestEventStartTimeRef.current).length > 0) {
            const eventIndex = Object.keys(latestEventStartTimeRef.current)[0];
            month = moment(latestEventStartTimeRef.current[eventIndex].start).format('YYYY-MM');
          }
          if (deleteEventItem.current.length > 0) {
            month = moment(deleteEventItem.current[0].blackDateTime).format('YYYY-MM');
          }

          toggleMonthlyTasks({
            ...monthlyCalendarQueryParams,
            month,
          });

          // 保存成功之后要将暂存的删除事项清空
          deleteEventItem.current = [];
          latestEventStartTimeRef.current = {};
          setIsEditable(false);
          saveTwiceRef.current = false;
        }
      } else {
        // 没有无效拖动时，点完保存按钮不会调用接口，但是也要清空一下。
        deleteEventItem.current = [];
        latestEventStartTimeRef.current = {};
        setIsEditable(false);
        saveTwiceRef.current = false;
      }
    } else {
      setIsEditable(false);
      saveTwiceRef.current = false;
    }
  };

  const isTriggerTimeValidBySingleEvent = (value: Moment, taskId: number, eventId: string) => {
    const sameIdTasks = currentEventsRef.current.filter(
      task => task.extendedProps.taskId === taskId
    );
    // 找到原排期中与移动后排期为同一天的任务
    const sameDayTasks = sameIdTasks.filter(task => {
      const taskDay = moment(task.start).format('YYYY-MM-DD');
      return taskDay === value.format('YYYY-MM-DD');
    });

    // 过滤掉当前移动中的排期
    const sameDayExceptItsSelfTasks = sameDayTasks.filter(task => task.id !== eventId);
    // 将剩余任务转化为触发时间的数组
    const sameDayExceptItsSelfTriggerTimes = sameDayExceptItsSelfTasks.map(task => task.start);
    // 是否除当前移动排期外，目标日还存在其他触发时间的任务
    const exceptItsSelfTrrigerTimeLength = sameDayExceptItsSelfTriggerTimes.length;

    let isValid = true;
    for (let i = 0; i < exceptItsSelfTrrigerTimeLength; i++) {
      if (
        Math.abs(moment(sameDayExceptItsSelfTriggerTimes[i]).diff(moment(value), 'minute')) < 10
      ) {
        isValid = false;
      }
    }
    // 如果目标日不存在当前移动排期之外的其他触发时间的任务，则此次移动有效，否则，根据isValid的结果。
    return exceptItsSelfTrrigerTimeLength === 0 ? true : isValid;
  };

  useEffect(() => {
    return () => {
      if (saveTwiceRef.current) {
        saveChanges();
      }
    };
  }, [saveTwiceRef.current]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <TaskQueryFilter
        planType={planType}
        layer={PlanLayer.MonthlyPlans}
        getData={toggleMonthlyTasks}
      />
      <div style={{ width: '100%', display: 'flex', justifyContent: 'space-between' }}>
        <TaskStatusBar />
        <Space align="end">
          {isMonthNextMonth(initialDate.start) && checkTaskModifyCode() && (
            <>
              {!isEditable ? (
                <Button
                  type="link"
                  onClick={() => {
                    setIsEditable(true);
                  }}
                >
                  修改排期
                </Button>
              ) : (
                <Button
                  type="primary"
                  loading={saveLoading}
                  onClick={() => {
                    saveChanges();
                  }}
                >
                  保存修改
                </Button>
              )}
            </>
          )}
        </Space>
      </div>
      <Spin spinning={monthlyTasksLoading}>
        <FullCalendar
          ref={calendarRef}
          contentHeight="auto"
          /** 视图类型 */
          initialView="dayGridMonth"
          /** 上方工具栏 */
          headerToolbar={false}
          /** 设置要展示的事项 */
          events={events}
          /** 设置语言 */
          locale="zh-cn"
          /** 确定一周的第一天以周几开始 */
          firstDay={1}
          /** 自定义事项 */
          eventContent={({ event }: { event: CalendarEvent & { durationEditable: boolean } }) => {
            return (
              <CalendarItem
                events={events}
                eventEditable={event.durationEditable}
                title={event.title}
                planType={planType}
                time={event.start}
                endTime={event.extendedProps.endTime}
                eventId={event.id}
                id={event.extendedProps.taskId}
                effectStatus={event.extendedProps.effectStatus}
                toggleMonthlyTasks={toggleMonthlyTasks}
                jobSla={event.extendedProps.jobSla}
                monthlyCalendarQueryParams={monthlyCalendarQueryParams}
                setTaskId={setTaskId}
                setModalVisible={setModalVisible}
                setEffectTime={setEffectTime}
                showDeleteIcon={checkTaskDeleteCode()}
                showEditIcon={checkTaskModifyCode()}
                handleEventClick={handleEventClick}
                isTriggerTimeValidBySingleEvent={isTriggerTimeValidBySingleEvent}
              />
            );
          }}
          /** 日期有效范围，无效时间会置灰，无法拖动 */
          validRange={initialDate}
          visibleRange={initialDate}
          /** 决定事项是否可以编辑。如：拖动，缩放 */
          editable
          /** 拖动后放下的操作回调 */
          // @ts-ignore eventDrop 是存在的
          eventDrop={({ event, revert }: { event: CalendarEvent }) => {
            const preEvents = currentEventsRef.current;
            if (moment().valueOf() > moment(event.start).valueOf()) {
              message.error('无法拖至过去');
              revert();
              return;
            }
            if (
              event.extendedProps.endTime &&
              moment(event.start).valueOf() > moment(event.extendedProps.endTime).valueOf()
            ) {
              message.error('无法拖至计划结束时间之后');
              revert();
              return;
            }
            if (
              !isTriggerTimeValid(
                moment(event.start),
                event.extendedProps.taskId,
                preEvents,
                event.id
              )
            ) {
              message.error('同类型任务触发时间间隔至少为10分钟');
              revert();
              return;
            }
            setLatestEventStartItem(
              event.id,
              moment(event.start).format('YYYY-MM-DD HH:mm:ss'),
              Number(event.extendedProps.taskId)
            );
            currentEventsRef.current = preEvents.map(item => {
              if (item.id === event.id) {
                return {
                  ...item,
                  start: moment(event.start).format('YYYY-MM-DD HH:mm'),
                };
              }
              return item;
            });
          }}
          /** 日期格子的高宽比 */
          aspectRatio={2.3}
        />
      </Spin>
      <TaskTicketsModal
        zIndex={1031}
        taskId={Number(taskId)}
        effectTime={effectTime}
        planType={planType}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
        }}
      />
      <Prompt
        when={isEditable}
        message={location => {
          return '您有未保存的更改，确定要离开此页面吗？';
        }}
      />
    </Space>
  );
}
const TaskTicketsModal = React.memo(
  ({ taskId, effectTime, planType, open, onCancel }: TaskRelateTicketsModalProps) => {
    return (
      <TaskRelateTicketsModal
        zIndex={1031}
        taskId={Number(taskId)}
        effectTime={effectTime}
        planType={planType}
        open={open}
        onCancel={onCancel}
      />
    );
  }
);
TaskTicketsModal.displayName = 'TaskTicketsModal';

const TaskDetailLink = React.memo(
  ({ planType, id, title }: { planType: PlanType; id: number; title: string }) => {
    return (
      <Link
        target="_blank"
        to={() => {
          switch (planType) {
            case PlanType.MaintenancePlan:
              return generateMaintainPlanDetailRoutePath({ id: String(id), name: title });
            case PlanType.InspectionPlan:
              return generateInspectionPlanDetailRoutePath({
                id: String(id),
                name: title,
              });
            case PlanType.InventoryPlan:
              return generateInventoryPlanDetailRoutePath({
                id: String(id),
                name: title,
              });
            case PlanType.DrillPlan:
              return generateDrillPlanDetailRoutePath({
                id: String(id),
                name: title,
              });
            default:
              return '';
          }
        }}
      >
        任务详情
      </Link>
    );
  }
);
TaskDetailLink.displayName = 'TaskDetailLink';
const HandleTaskSchedule = React.memo(
  ({
    time,
    isEditable,
    effectStatus,
    id,
    events,
    eventId,
    endTime,
    monthlyCalendarQueryParams,
    toggleMonthlyTasks,
    showEditIcon,
    showDeleteIcon,
    handleEventClick,
    isTriggerTimeValidBySingleEvent,
  }: {
    time: string | null;
    isEditable: boolean;
    effectStatus: string;
    id: number;
    events: CalendarEvent[];
    eventId: string;
    endTime: string | null;
    monthlyCalendarQueryParams: MonthlyTaskQueryParams;
    toggleMonthlyTasks: (params: MonthlyTaskQueryParams) => void;
    showEditIcon: boolean;
    showDeleteIcon: boolean;
    handleEventClick: (id: string, type: 'delete' | 'edit', newStartTime?: Moment) => void;
    isTriggerTimeValidBySingleEvent: (value: Moment, taskId: number, eventId: string) => boolean;
  }) => {
    const [form] = Form.useForm();
    const pickerTime = moment(time);

    return (
      <Space>
        {isEditable && effectStatus !== TaskEffectStatus.Effected && showEditIcon && (
          <Popconfirm
            icon={null}
            title={
              <Space direction="vertical">
                <Typography.Title level={5}>修改排期</Typography.Title>
                <Form form={form}>
                  <Form.Item
                    label="排期时间"
                    name="newTime"
                    initialValue={pickerTime}
                    rules={[
                      () => ({
                        validator(_, value) {
                          if (!value) {
                            return Promise.reject(new Error('修改时间不能为空'));
                          } else if (
                            endTime &&
                            moment(value).diff(moment(endTime), 'seconds') > 0
                          ) {
                            return Promise.reject(new Error('修改时间不能超过结束时间'));
                          } else if (moment(value).diff(moment(), 'minutes') < 0) {
                            return Promise.reject(new Error('修改时间不能小于当前时间'));
                          } else if (!isTriggerTimeValidBySingleEvent(value, id, eventId)) {
                            return Promise.reject(new Error('同类型任务触发时间间隔至少为10分钟'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <DatePicker
                      picker="date"
                      showTime
                      format="YYYY-MM-DD HH:mm"
                      // @ts-ignore getPopupContainer 存在
                      getPopupContainer={trigger => trigger.parentNode.parentNode}
                    />
                  </Form.Item>
                </Form>
              </Space>
            }
            onConfirm={() => {
              form.validateFields().then(async formValue => {
                const { newTime } = formValue;
                handleEventClick(eventId, 'edit', newTime);
              });
            }}
            onCancel={() => {
              form.resetFields();
            }}
          >
            <EditOutlined />
          </Popconfirm>
        )}
        {isEditable && effectStatus !== TaskEffectStatus.Effected && showDeleteIcon && (
          <Popconfirm
            title={<Typography.Text style={{ width: 236 }}>确认删除该排期？</Typography.Text>}
            okText="确认删除"
            onConfirm={() => {
              handleEventClick(eventId, 'delete');
            }}
          >
            <DeleteOutlined />
          </Popconfirm>
        )}
      </Space>
    );
  }
);
HandleTaskSchedule.displayName = 'HandleTaskSchedule';
const CalendarItem = React.memo(
  ({
    events,
    eventEditable,
    title,
    time,
    id,
    endTime,
    effectStatus,
    planType,
    eventId,
    monthlyCalendarQueryParams,
    jobSla,
    toggleMonthlyTasks,
    setTaskId,
    setModalVisible,
    setEffectTime,
    showEditIcon,
    showDeleteIcon,
    handleEventClick,
    isTriggerTimeValidBySingleEvent,
  }: {
    events: CalendarEvent[];
    eventEditable: boolean;
    title: string;
    time: string | null;
    id: number;
    endTime: string | null;
    effectStatus: string;
    planType: PlanType;
    eventId: string;
    monthlyCalendarQueryParams: MonthlyTaskQueryParams;
    jobSla: number;
    toggleMonthlyTasks: (params: MonthlyTaskQueryParams) => void;
    setTaskId: (params: number) => void;
    setModalVisible: (params: boolean) => void;
    setEffectTime: (params: string) => void;
    showEditIcon: boolean;
    showDeleteIcon: boolean;
    handleEventClick: (id: string, type: 'delete' | 'edit', newStartTime?: Moment) => void;
    isTriggerTimeValidBySingleEvent: (value: Moment, taskId: number, eventId: string) => boolean;
  }) => {
    return (
      <div
        style={{
          width: 'calc(100% - 8px)',
          display: 'flex',
          marginLeft: 8,
          alignItems: 'center',
          alignContent: 'center',
          justifyContent: 'start',
          height: 30,
        }}
      >
        <div style={{ width: 'calc(100% - 40px)' }}>
          <Popover
            content={
              <Space direction="vertical">
                {effectStatus && effectStatus !== TaskEffectStatus.Ineffective && (
                  <Button
                    type="text"
                    compact
                    onClick={() => {
                      setEffectTime(moment(time).format('YYYY-MM-DD HH:mm:ss'));
                      setTaskId(id);
                      setModalVisible(true);
                    }}
                  >
                    查看工单
                  </Button>
                )}
                <Button type="text" compact>
                  <TaskDetailLink planType={planType} id={id} title={title} />
                </Button>
              </Space>
            }
            trigger="click"
            placement="bottom"
          >
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Badge color={effectStatus !== TaskEffectStatus.Ineffective ? 'green' : 'gold'} />
              <Typography.Text style={{ marginLeft: 8 }} ellipsis={{ tooltip: true }}>
                {title}
              </Typography.Text>
              {jobSla !== null && <Typography.Text>(</Typography.Text>}
              {jobSla ? (
                <TicketSlaText
                  unit="SECOND"
                  taskSla={0}
                  delay={jobSla}
                  effectTime={null}
                  endTime={null}
                />
              ) : jobSla === 0 ? (
                <Typography.Text>SLA无限制</Typography.Text>
              ) : (
                <Typography.Text />
              )}
              {jobSla !== null && <Typography.Text>)</Typography.Text>}
            </div>
          </Popover>
        </div>
        <HandleTaskSchedule
          time={time}
          isEditable={eventEditable}
          effectStatus={effectStatus}
          id={id}
          events={events}
          eventId={eventId}
          endTime={endTime}
          monthlyCalendarQueryParams={monthlyCalendarQueryParams}
          toggleMonthlyTasks={toggleMonthlyTasks}
          showDeleteIcon={showDeleteIcon}
          showEditIcon={showEditIcon}
          handleEventClick={handleEventClick}
          isTriggerTimeValidBySingleEvent={isTriggerTimeValidBySingleEvent}
        />
      </div>
    );
  }
);
CalendarItem.displayName = 'CalendarItem';

function isTodayLastFiveDay() {
  let flag = false;
  for (let i = 0; i < 5; i++) {
    if (moment().isSame(moment().endOf('month').subtract(i, 'day'), 'day')) {
      flag = true;
    }
  }

  return flag;
}

function isMonthNextMonth(month: string | number | Date | null | undefined) {
  return (
    (isTodayLastFiveDay() && moment().add(1, 'month').isSame(month, 'month')) ||
    moment().isSame(month, 'month')
  );
}

function isTriggerTimeValid(
  modifyTime: Moment,
  taskId: number,
  tasks: CalendarEvent[],
  eventId?: string
) {
  const sameIdTasks = tasks.filter(task => task.extendedProps.taskId === taskId);
  // 找到原排期中与移动后排期为同一天的任务
  const sameDayTasks = sameIdTasks.filter(task => {
    const taskDay = moment(task.start).format('YYYY-MM-DD');
    return taskDay === modifyTime.format('YYYY-MM-DD');
  });

  // 过滤掉当前移动中的排期
  const sameDayExceptItsSelfTasks = sameDayTasks.filter(task => task.id !== eventId);
  // 将剩余任务转化为触发时间的数组
  const sameDayExceptItsSelfTriggerTimes = sameDayExceptItsSelfTasks.map(task => task.start);
  // 是否除当前移动排期外，目标日还存在其他触发时间的任务
  const exceptItsSelfTrrigerTimeLength = sameDayExceptItsSelfTriggerTimes.length;

  let isValid = true;
  for (let i = 0; i < exceptItsSelfTrrigerTimeLength; i++) {
    if (
      Math.abs(moment(sameDayExceptItsSelfTriggerTimes[i]).diff(moment(modifyTime), 'minute')) < 10
    ) {
      isValid = false;
    }
  }
  // 如果目标日不存在当前移动排期之外的其他触发时间的任务，则此次移动有效，否则，根据isValid的结果。
  return exceptItsSelfTrrigerTimeLength === 0 ? true : isValid;
}
