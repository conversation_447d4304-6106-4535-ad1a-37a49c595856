import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { Table } from '@manyun/base-ui.ui.table';

import { User } from '@manyun/auth-hub.ui.user';
import type { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { TaskStatusMap } from '@manyun/ticket.model.ticket';
import { generateTicketUrl } from '@manyun/ticket.route.ticket-routes';
import type { BaseInfo } from '@manyun/ticket.service.fetch-tickets';
import { fetchTickets } from '@manyun/ticket.service.fetch-tickets';
import { TicketTypeText } from '@manyun/ticket.ui.ticket-type-text';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';
import type { TimeUnit } from '@manyun/ticket.util.sla';

export type TaskTicketsTableProps = {
  effectTime: string;
  taskNo: number;
};

export type TicketsTableListState = {
  loading: boolean;
  pageNum: number;
  pageSize: number;
  total: number;
  dataSource: BaseInfo[];
};

export function TaskTicketTable({ effectTime, taskNo }: TaskTicketsTableProps) {
  const [state, setState] = useState<TicketsTableListState>({
    loading: false,
    pageNum: 1,
    pageSize: 10,
    total: 0,
    dataSource: [],
  });
  const _fetchTickets = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    if (effectTime) {
      const { error, data } = await fetchTickets({
        scheduleId: taskNo,
        pageNum: state.pageNum,
        pageSize: state.pageSize,
        effectTime: effectTime,
        openInitWithdraw: true,
        openSch: true,
      });
      setState(pre => ({ ...pre, loading: false }));

      if (error) {
        message.error(error.message);
        return;
      }
      setState(pre => ({ ...pre, dataSource: data.data, total: data.total }));
    }
  }, [taskNo, state.pageNum, state.pageSize, effectTime]);

  useEffect(() => {
    _fetchTickets();
  }, [_fetchTickets]);
  return (
    <Table
      scroll={{ x: 'max-content' }}
      loading={state.loading}
      rowKey="taskNo"
      columns={[
        {
          title: '工单单号',
          dataIndex: 'taskNo',
          fixed: 'left',
          render: (text: string, record: BaseInfo) => {
            return (
              <Link
                target="_blank"
                to={{
                  pathname: generateTicketUrl({
                    ticketType: record.taskType.toLowerCase(),
                    id: text,
                  }),
                }}
              >
                {text}
              </Link>
            );
          },
        },
        {
          title: '工单类型',
          dataIndex: 'taskType',
          render: text => {
            return <TicketTypeText code={text} />;
          },
        },
        {
          title: '工单子类型',
          dataIndex: 'taskSubType',
          render: (text, { taskType }) => {
            return <TicketTypeText code={`${taskType}${text}`} />;
          },
        },
        {
          title: '工单标题',
          dataIndex: 'taskTitle',
        },
        {
          title: '位置',
          dataIndex: 'blockTag',
          render: (text, { roomGuid }) => roomGuid || text,
        },
        {
          title: 'SLA计时/标准',
          dataIndex: 'taskSla',
          render: (text, record) => (
            <TicketSlaText
              taskSla={text}
              delay={record.delay}
              unit={record.unit as TimeUnit}
              effectTime={Number(record.effectTime)}
              endTime={record.endTime}
              shouldLimitShow
            />
          ),
        },
        {
          title: '工单状态',
          dataIndex: 'taskStatus',
          render: (text: BackendTaskStatus) => TaskStatusMap[text],
        },
        {
          title: '处理人',
          dataIndex: 'taskAssignee',
          render: (_, { taskAssignee, taskAssigneeName }: BaseInfo) => (
            <User.Link id={taskAssignee ?? undefined} name={taskAssigneeName || undefined} />
          ),
        },
      ]}
      dataSource={state.dataSource}
      pagination={{
        total: state.total,
        current: state.pageNum,
        pageSize: state.pageSize,
      }}
      onChange={pagination => {
        setState(pre => ({
          ...pre,
          pageNum: pagination.current!,
          pageSize: pagination.pageSize!,
        }));
      }}
    />
  );
}
