import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Calendar } from '@manyun/base-ui.ui.calendar';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchDeviceScheduleCalendar } from '@manyun/resource-hub.service.fetch-device-schedule-calendar';
import type {
  ApiQ,
  ExecuteDateListInfo,
  FetchDeviceScheduleCalendarDataModel,
} from '@manyun/resource-hub.service.fetch-device-schedule-calendar';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useTaskData } from '@manyun/ticket.context.task-data';
import {
  CYCLES_TYPE_TEXT_MAP,
  MAINTENANCE_CYCLE_TEXT_MAP,
  PlanLayer,
  PlanType,
  Task,
  TaskEffectStatus,
} from '@manyun/ticket.model.task';
import type { TaskJSON } from '@manyun/ticket.model.task';
import {
  TaskQueryFilter,
  TaskStatusBar,
  generateJobType,
} from '@manyun/ticket.page.task-configuration-list';
import {
  generateDrillPlanDetailRoutePath,
  generateInspectionPlanDetailRoutePath,
  generateInventoryPlanDetailRoutePath,
  generateMaintainPlanDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { TicketTypeText } from '@manyun/ticket.ui.ticket-type-text';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

import { TaskRelateTicketsModal } from './task-relate-tickets-modal';

export type AnnualTasksProps = { planType: PlanType };
export type AnnualTaskJson = FetchDeviceScheduleCalendarDataModel & { schLevel?: string };
export function AnnualTasks({ planType }: AnnualTasksProps) {
  const [{ year }] = useTaskData();
  const [dataSource, setDataSource] = useState<AnnualTaskJson[]>([]);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [taskName, setTaskName] = useState<string>('');
  const [taskId, setTaskId] = useState<string>('');
  const [effectTime, setEffectTime] = useState<string>('');
  const [modalVisible, setModalVisible] = useState(false);
  const [queryParams, setQueryParams] = useState({});
  const [planTotal, setPlanTotal] = useState(0);
  const [planPagination, setPlanPagination] = useState<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const isMaintenancePlan = planType === PlanType.MaintenancePlan;

  const columns: Array<ColumnType<AnnualTaskJson>> = useMemo(() => {
    const basicColumns: Array<ColumnType<AnnualTaskJson>> = [
      { title: '机房楼栋', dataIndex: 'blockGuid', render: (_, record) => record.blockGuid },
      {
        title: '任务名称',
        dataIndex: 'name',
        width: 280,
        render: (_, record) => {
          return (
            <Typography.Text
              style={{ width: 280, color: `var(--${prefixCls}-primary-color)` }}
              ellipsis={{ tooltip: true }}
            >
              <Link
                target="_blank"
                to={() => {
                  switch (planType) {
                    case PlanType.MaintenancePlan:
                      return generateMaintainPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    case PlanType.InspectionPlan:
                      return generateInspectionPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    case PlanType.InventoryPlan:
                      return generateInventoryPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    case PlanType.DrillPlan:
                      return generateDrillPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    default:
                      return '';
                  }
                }}
              >
                {record.name}
              </Link>
            </Typography.Text>
          );
        },
      },
      {
        title: generateSpecificDataByTaskType(planType).columnTitle,
        dataIndex: 'subJobType',
        render: (text: string) => (
          <TicketTypeText code={`${generateSpecificDataByTaskType(planType).ticketType}${text}`} />
        ),
      },
      {
        title: `${generateSpecificDataByTaskType(planType).prefix}数量`,
        dataIndex: 'subJobTypeNum',
      },
      isMaintenancePlan
        ? {
            title: '维护周期',
            dataIndex: 'guidePeriod',
            render: (_, { guidePeriod }) =>
              guidePeriod ? MAINTENANCE_CYCLE_TEXT_MAP[guidePeriod] : '--',
          }
        : {
            title: '重复周期',
            dataIndex: 'periodUnit',
            render: (_, { periodUnit }) => CYCLES_TYPE_TEXT_MAP[periodUnit],
          },
      ...getMonths(),
      {
        title: '排期',
        dataIndex: 'action',
        fixed: 'right',

        render: (_, record) => (
          <>
            <Button
              type="link"
              compact
              onClick={() => {
                setDrawerVisible(true);
                setTaskName(record.name);
                setTaskId(String(record.id));
              }}
            >
              查看
            </Button>
          </>
        ),
      },
    ];
    if (planType === PlanType.DrillPlan) {
      basicColumns.splice(
        2,
        1,
        {
          title: '专业类型',
          dataIndex: 'subJobType',
          render: text =>
            text ? <MetaTypeText code={text} metaType={MetaType.EXC_MAJOR} /> : '--',
        },
        {
          title: '等级',
          dataIndex: 'schLevel',
          render: text =>
            text ? <MetaTypeText code={text} metaType={MetaType.EXC_LEVEL} /> : '--',
        }
      );
    }
    basicColumns.splice(4, 1);
    return basicColumns;
  }, [isMaintenancePlan, planType]);

  function getMonths() {
    const months = [];
    for (let i = 1; i < 13; i++) {
      months.push({
        title: `${i}月`,
        dataIndex: 'executeDateList',
        render: (list: ExecuteDateListInfo[]) => {
          const status = list[i - 1].effectStatus;
          return status ? (
            <Badge color={status === TaskEffectStatus.Effected ? 'green' : 'gold'} />
          ) : null;
        },
      });
    }
    return months;
  }
  const getAnnualTasks = async (params: ApiQ) => {
    const { year, ...rest } = params;
    setQueryParams(params);
    setLoading(true);
    const { error, data } = await fetchDeviceScheduleCalendar({
      ...rest,
      year: Number(moment(year).format('YYYY')),
      jobTypeList: generateJobType(planType),
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
    setPlanTotal(data.total);
  };
  useEffect(() => {
    getAnnualTasks({
      pageNum: 1,
      pageSize: 10,
      jobTypeList: generateJobType(planType),
      year: year,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [planType]);

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={16}>
      <TaskQueryFilter
        planType={planType}
        layer={PlanLayer.AnnualPlans}
        getData={value => {
          getAnnualTasks({
            ...value,
            pageSize: planPagination.pageSize,
            pageNum: 1,
          });
          setPlanPagination({ pageNum: 1, pageSize: planPagination.pageSize });
        }}
        resetPagination={setPlanPagination}
      />
      <TaskStatusBar />
      <Table<AnnualTaskJson>
        dataSource={dataSource}
        columns={columns}
        loading={loading}
        scroll={{ x: 'max-content' }}
        pagination={{
          total: planTotal,
          pageSize: planPagination.pageSize,
          current: planPagination.pageNum,
          onChange: (current, size) => {
            setPlanPagination({ pageNum: current, pageSize: size });
            getAnnualTasks({ ...queryParams, pageNum: current, pageSize: size });
          },
        }}
      />
      <AnnualCalendar
        planName={taskName}
        planId={taskId}
        drawerVisible={drawerVisible}
        setDrawerVisible={setDrawerVisible}
        setModalVisible={setModalVisible}
        setEffectTime={setEffectTime}
      />
      <TaskRelateTicketsModal
        taskId={Number(taskId)}
        effectTime={effectTime}
        planType={planType}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
        }}
      />
    </Space>
  );
}

function AnnualCalendar({
  planName,
  planId,
  drawerVisible,
  setDrawerVisible,
  setModalVisible,
  setEffectTime,
}: {
  planName: string;
  planId: string;
  drawerVisible: boolean;
  setDrawerVisible: (params: boolean) => void;
  setModalVisible: (params: boolean) => void;
  setEffectTime: (params: string) => void;
}) {
  const [{ year, monthlyTasks, monthlyTasksLoading }, { toggleMonthlyTasks }] = useTaskData();

  useEffect(() => {
    if (drawerVisible) {
      toggleMonthlyTasks({ year: moment(year).format('YYYY'), schId: planId });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [drawerVisible]);

  const getMonthData = (value: Moment) => {
    const monthTasks = monthlyTasks.filter(
      task =>
        moment(task.localDate).month() === value.month() && task.calendarExecuteInfoSet.length > 0
    );
    let normalTaskList: TaskJSON[] = [];

    normalTaskList = monthTasks.reduce((pre, cur) => {
      return [...pre, ...cur.calendarExecuteInfoSet];
    }, [] as TaskJSON[]);
    const allDaysTasks = normalTaskList
      .map(item => {
        return {
          ...item,
          name: `${moment(item.effectTime).format('MM-DD')} ${getWeek(
            moment(item.effectTime).weekday()
          )} ${moment(item.effectTime).format('HH:mm')}`,
        };
      })
      .sort((pre, post) => pre.effectTime - post.effectTime);
    return allDaysTasks;
  };
  function monthCellRender(
    value: Moment,
    setModalVisible: (params: boolean) => void,
    setEffectTime: (params: string) => void
  ) {
    const allDaysTasks = getMonthData(value);
    return (
      <Space direction="vertical">
        {allDaysTasks.map(item => (
          <span
            key={`${item.id}${item.name}`}
            onClick={() => {
              if (item.effectStatus && item.effectStatus !== 'waited') {
                setEffectTime(Task.fromJSON(item).getFormattedEffectedTime());

                setModalVisible(true);
              }
            }}
          >
            <Badge
              color={item.effectStatus === TaskEffectStatus.Ineffective ? 'gold' : 'green'}
              text={item.name}
            />
          </span>
        ))}
      </Space>
    );
  }

  return (
    <Drawer
      width="70%"
      open={drawerVisible}
      title={`${planName}-${year}年排期明细`}
      onClose={() => {
        setDrawerVisible(false);
      }}
    >
      <Spin spinning={monthlyTasksLoading}>
        <Space direction="vertical" style={{ width: '100%' }}>
          <TaskStatusBar />
          <Calendar
            headerRender={() => <></>}
            mode="year"
            monthCellRender={value => {
              return monthCellRender(value, setModalVisible, setEffectTime);
            }}
          />
        </Space>
      </Spin>
    </Drawer>
  );
}
function getWeek(value: number) {
  switch (value) {
    case 0:
      return '周一';
    case 1:
      return '周二';
    case 2:
      return '周三';
    case 3:
      return '周四';
    case 4:
      return '周五';
    case 5:
      return '周六';
    case 6:
      return '周日';
    default:
      return '';
  }
}
