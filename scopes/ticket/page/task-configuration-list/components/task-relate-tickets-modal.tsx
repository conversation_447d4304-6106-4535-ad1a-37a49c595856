import React, { useEffect } from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';
import type { ModalProps } from '@manyun/base-ui.ui.modal';

import { useLazyDrillOrders } from '@manyun/ticket.gql.client.drill-order';
import { PlanType } from '@manyun/ticket.model.task';
import { TaskTicketTable } from '@manyun/ticket.page.task-configuration-list';
import { DrillOrderTable } from '@manyun/ticket.ui.drill-order-table';

export type TaskRelateTicketsModalProps = {
  taskId: number;
  effectTime: string;
  planType: PlanType;
} & ModalProps;
export function TaskRelateTicketsModal({
  taskId,
  effectTime,
  planType,
  ...props
}: TaskRelateTicketsModalProps) {
  const [getDrillOrders, { data: drillsData, loading: drillDatasLoading }] = useLazyDrillOrders();
  const isDrillPlan = planType === PlanType.DrillPlan;
  useEffect(() => {
    if (isDrillPlan && taskId && effectTime) {
      (async function () {
        await getDrillOrders({
          variables: {
            params: {
              taskStatusList: [],
              pageNum: 1,
              pageSize: 1000,
              scheduleId: taskId,
              gmtCreate: effectTime,
            },
          },
        });
      })();
    }
  }, [getDrillOrders, isDrillPlan, taskId, effectTime]);
  return (
    <Modal width={1060} title="工单" {...props}>
      {isDrillPlan ? (
        <DrillOrderTable
          dataIndexs={['excNo', 'blockGuid', 'title', 'taskStatus', 'sla', 'taskAssignee']}
          size="middle"
          loading={drillDatasLoading}
          dataSource={drillsData?.drillOrders?.data ?? []}
          pagination={{
            total: drillsData?.drillOrders?.total ?? 0,
          }}
        />
      ) : (
        <TaskTicketTable taskNo={Number(taskId)} effectTime={effectTime} />
      )}
    </Modal>
  );
}
