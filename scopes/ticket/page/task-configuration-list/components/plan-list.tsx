import React, { useCallback, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useTaskData } from '@manyun/ticket.context.task-data';
import { useBatchUpdateTaskStatus, useDeleteTask } from '@manyun/ticket.gql.client.tickets';
import type { BackendPlanSearchParam, PlanJSON } from '@manyun/ticket.model.task';
import {
  CYCLES_TYPE_TEXT_MAP,
  MAINTENANCE_CYCLE_TEXT_MAP,
  PLAN_EFFECT_TEXT_MAP,
  Plan,
  PlanLayer,
  PlanStatus,
  PlanType,
} from '@manyun/ticket.model.task';
import { TaskQueryFilter } from '@manyun/ticket.page.task-configuration-list';
import {
  CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH,
  generateDrillPlanDetailRoutePath,
  generateDrillTaskConfigurationMutatorRoutePath,
  generateInspectionPlanDetailRoutePath,
  generateInspectionTaskConfigurationMutatorRoutePath,
  generateInventoryPlanDetailRoutePath,
  generateInventoryTaskConfigurationMutatorRoutePath,
  generateMaintainPlanDetailRoutePath,
  generateMaintainTaskConfigurationMutatorRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { batchUpdatePlanStatus } from '@manyun/ticket.service.batch-update-plan-status';
import { exportDrillPlans } from '@manyun/ticket.service.export-drill-plans';
import { FiltersTicketsModal } from '@manyun/ticket.ui.filters-tickets-modal';
import { TicketTypeText } from '@manyun/ticket.ui.ticket-type-text';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

export type PlanListProps = {
  /** 用于区分 巡检、维护还是盘点类型 */
  planType: PlanType;
};
export type DrillPlanExportParams = {
  includeColumnFiledNames: string[];
  jobTypeList: string[];
  creatorId?: number | null;
} & Omit<BackendPlanSearchParam, 'jobTypeList' | 'creatorId'>;
export function PlanList({ planType }: PlanListProps) {
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [filterParams, setFilterParams] = useState<DrillPlanExportParams>({
    includeColumnFiledNames: [''],
    jobTypeList: ['SCH_EXERCISE'],
  });
  const [exportLoading, setExportLoading] = useState(false);

  const [, { checkCode }] = useAuthorized();
  const canBatchOperationDrillTask = checkCode('element_batch-operate-drill-task');
  const canDeleteOperationDrillTask = checkCode('element_delete-drill-task');
  const canUpdateOperationDrillTask = checkCode('element_update-drill-task');
  const isDrillTask = planType === PlanType.DrillPlan;
  const shouldActivateButtonShow = (canBatchOperationDrillTask && isDrillTask) || !isDrillTask;
  const shouldDeleteButtonShow = (canDeleteOperationDrillTask && isDrillTask) || !isDrillTask;
  const shouldUpdateButtonShow = (canUpdateOperationDrillTask && isDrillTask) || !isDrillTask;
  const [updateTaskStatus, { loading: updateTaskStatusLoading }] = useBatchUpdateTaskStatus({
    onCompleted(data) {
      if (!data.batchUpdateTaskStatus?.success) {
        message.error(data.batchUpdateTaskStatus?.message);
        return;
      }
      message.success('设置成功');
      togglePlans({
        ...planQueryParams,
        pageNum: planPagination.pageNum,
        pageSize: planPagination.pageSize,
      });
    },
  });
  const [deleteTask, { loading: deleteLoading }] = useDeleteTask({
    onCompleted(data) {
      if (!data.deleteTask?.success) {
        message.error(data.deleteTask?.message ?? '删除失败');
        return;
      } else {
        message.success('删除成功');
        togglePlans({
          ...planQueryParams,
          pageNum: planPagination.pageNum,
          pageSize: planPagination.pageSize,
        });
      }
    },
  });

  const [
    { plans, planQueryParams, planLoading, planTotal, planPagination },
    {
      togglePlans,

      togglePlanPagination,
    },
  ] = useTaskData();
  const isMaintenancePlan = planType === PlanType.MaintenancePlan;

  const handleFileExport = useCallback(
    async (type: string) => {
      let params: DrillPlanExportParams = {
        includeColumnFiledNames: [
          'id',
          'name',
          'blockGuid',
          'guidePeriod',
          'subJobType',
          'jobItemNum',
          'jobType',
          'periodUnit',
          'status',
          'isInvalid',
          'gmtCreate',
          'creatorId',
          'creatorName',
          'blockScope',
          'triggerTime',
          'taskNoList',
          'gmtModified',
          'cycles',
          'allowTriggerTime',
          'aggBlockScope',
          'endTime',
          'splitor',
          'jobSla',
          'slaUnit',
          'jobItemList',
          'finishTaskNoList',
          'unFinishTaskNoList',
          'manageType',
          'scheduleSplitRangeList',
          'schLevel',
          'fileInfoList',
          'modifierName',
          'modifierId',
          'taskResultNums',
          'totalNum',
        ],
        jobTypeList: ['SCH_EXERCISE'],
      };
      if (type === 'filtered') {
        params = { ...filterParams, ...params };
      }
      setExportLoading(true);
      const { error, data } = await exportDrillPlans(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      return data;
    },
    [filterParams]
  );
  const columns: Array<ColumnType<PlanJSON>> = useMemo(() => {
    const basicColumns: Array<ColumnType<PlanJSON>> = [
      { title: '机房楼栋', dataIndex: 'blockScope', render: (_, { blockGuid }) => blockGuid },
      {
        title: '计划名称',
        dataIndex: 'name',
        width: 280,
        render: (_, record) => {
          return (
            <Typography.Text
              style={{ width: 280, color: `var(--${prefixCls}-primary-color)` }}
              ellipsis={{ tooltip: true }}
            >
              <Link
                target="_blank"
                to={() => {
                  switch (planType) {
                    case PlanType.MaintenancePlan:
                      return generateMaintainPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    case PlanType.InspectionPlan:
                      return generateInspectionPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    case PlanType.InventoryPlan:
                      return generateInventoryPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    case PlanType.DrillPlan:
                      return generateDrillPlanDetailRoutePath({
                        id: String(record.id),
                        name: record.name,
                      });
                    default:
                      return '';
                  }
                }}
              >
                {record.name}
              </Link>
            </Typography.Text>
          );
        },
      },
      {
        title: generateSpecificDataByTaskType(planType).columnTitle,
        dataIndex: 'mopType',
        render: (_, { mopType }) => (
          <TicketTypeText code={`${generateTicketType(planType)}${mopType}`} />
        ),
      },
      {
        title: `${generateSpecificDataByTaskType(planType).prefix}数量`,
        dataIndex: 'jobItemNum',
        render: (_, record) => record.mopCount,
      },
      isMaintenancePlan
        ? {
            title: '维护周期',
            dataIndex: 'guidePeriod',
            render: (_, { guidePeriod }) =>
              guidePeriod ? MAINTENANCE_CYCLE_TEXT_MAP[guidePeriod] : '--',
          }
        : {
            title: '重复周期',
            dataIndex: 'periodUnit',
            render: (_, { periodUnit }) => CYCLES_TYPE_TEXT_MAP[periodUnit],
          },
      {
        title: '是否有效',
        dataIndex: 'isInEffect',
        render: (_, { isInEffect }) => PLAN_EFFECT_TEXT_MAP[isInEffect],
      },
      {
        title: '上次运行时间',
        dataIndex: 'lastExecuteTime',
        render: (_, record) => Plan.fromJSON(record).getFormattedLastExecuteAt(),
      },
      {
        title: '运行结果',
        dataIndex: 'lastExecuteResult',
        render: (_, { lastExecuteResult, id }) =>
          lastExecuteResult?.length ? (
            <FiltersTicketsModal
              title="上次创建的工单记录"
              btnText={lastExecuteResult.length + ''}
              taskNos={lastExecuteResult}
              scheduleId={id.toString()}
              planType={planType}
            />
          ) : (
            0
          ),
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        render: (_, record) => Plan.fromJSON(record).getFormattedCreatedAt(),
      },
      {
        title: '创建人',
        dataIndex: 'creatorId',
        render: (_, { creator }) => <User.Link id={creator.id} />,
      },
      {
        title: '启用状态',
        dataIndex: 'isActivated',
        render: (_, record) => {
          const isActivated = record.isActivated === PlanStatus.On;
          const titlePrefix = isActivated ? '禁用' : '启用';

          return shouldActivateButtonShow ? (
            !isDrillTask ? (
              <Switch
                checked={isActivated}
                onChange={async () => {
                  await updateTaskStatus({
                    variables: {
                      ids: [record.id],
                      status: isActivated ? PlanStatus.Off : PlanStatus.On,
                    },
                  });
                }}
              />
            ) : (
              <Popconfirm
                key="status"
                title={`${titlePrefix}该演练计划?`}
                okButtonProps={{ loading: updateTaskStatusLoading }}
                okText={`确认${titlePrefix}`}
                onConfirm={async () => {
                  await updateTaskStatus({
                    variables: {
                      ids: [record.id],
                      status: isActivated ? PlanStatus.Off : PlanStatus.On,
                    },
                  });
                }}
              >
                <Switch checked={isActivated} />
              </Popconfirm>
            )
          ) : (
            <Badge
              status={isActivated ? 'success' : 'default'}
              text={isActivated ? '已启用' : '已禁用'}
            />
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 200,
        fixed: 'right',
        render: (_, record) => {
          return (
            <Space>
              {shouldUpdateButtonShow && (
                <Link
                  to={() => {
                    switch (planType) {
                      case PlanType.MaintenancePlan:
                        return generateMaintainTaskConfigurationMutatorRoutePath({
                          id: String(record.id),
                          mode: 'edit',
                        });
                      case PlanType.InspectionPlan:
                        return generateInspectionTaskConfigurationMutatorRoutePath({
                          id: String(record.id),
                          mode: 'edit',
                        });

                      case PlanType.InventoryPlan:
                        return generateInventoryTaskConfigurationMutatorRoutePath({
                          id: String(record.id),
                          mode: 'edit',
                        });
                      case PlanType.DrillPlan:
                        return generateDrillTaskConfigurationMutatorRoutePath({
                          id: String(record.id),
                          mode: 'edit',
                        });
                      default:
                        return '';
                    }
                  }}
                >
                  编辑
                </Link>
              )}

              <Link
                to={() => {
                  switch (planType) {
                    case PlanType.MaintenancePlan:
                      return generateMaintainTaskConfigurationMutatorRoutePath({
                        id: String(record.id),
                        mode: 'copy',
                      });
                    case PlanType.InspectionPlan:
                      return generateInspectionTaskConfigurationMutatorRoutePath({
                        id: String(record.id),
                        mode: 'copy',
                      });
                    case PlanType.InventoryPlan:
                      return generateInventoryTaskConfigurationMutatorRoutePath({
                        id: String(record.id),
                        mode: 'copy',
                      });
                    case PlanType.DrillPlan:
                      return generateDrillTaskConfigurationMutatorRoutePath({
                        id: String(record.id),
                        mode: 'copy',
                      });
                    default:
                      return '';
                  }
                }}
              >
                复制
              </Link>

              {shouldDeleteButtonShow &&
                (!isDrillTask ? (
                  <Popconfirm
                    key="remove"
                    title="确定删除该条计划？"
                    onConfirm={async () => {
                      await deleteTask({ variables: { id: record.id } });
                    }}
                  >
                    <Button compact type="link">
                      删除
                    </Button>
                  </Popconfirm>
                ) : (
                  <Popconfirm
                    key="remove"
                    title={
                      <Typography.Text>
                        删除该应急演练计划？删除后不可恢
                        <br />
                        复，请谨慎操作！
                      </Typography.Text>
                    }
                    okButtonProps={{ loading: deleteLoading }}
                    okText="确认删除"
                    onConfirm={async () => {
                      await deleteTask({ variables: { id: record.id } });
                    }}
                  >
                    {record.isActivated === PlanStatus.Off && (
                      <Button compact type="link">
                        删除
                      </Button>
                    )}
                  </Popconfirm>
                ))}
            </Space>
          );
        },
      },
    ];
    if (isDrillTask) {
      basicColumns.splice(
        2,
        1,
        {
          title: '专业类型',
          dataIndex: 'mopType',
          render: text =>
            text ? <MetaTypeText code={text} metaType={MetaType.EXC_MAJOR} /> : '--',
        },
        {
          title: '等级',
          dataIndex: 'schLevel',
          render: text =>
            text ? <MetaTypeText code={text} metaType={MetaType.EXC_LEVEL} /> : '--',
        }
      );
    }
    basicColumns.splice(4, 1);
    return basicColumns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isMaintenancePlan,
    planPagination.pageNum,
    planPagination.pageSize,
    planQueryParams,
    planType,
    shouldActivateButtonShow,
    shouldDeleteButtonShow,
    shouldUpdateButtonShow,
  ]);
  return (
    <Space style={{ width: '100%' }} direction="vertical" size={16}>
      <TaskQueryFilter
        planType={planType}
        layer={PlanLayer.Plans}
        getData={value => {
          setFilterParams(value);
          togglePlans({
            ...value,
            pageSize: planPagination.pageSize,
            pageNum: 1,
          });
          togglePlanPagination({ pageNum: 1, pageSize: planPagination.pageSize });
        }}
        resetPagination={togglePlanPagination}
      />
      <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
        <Space>
          <Link to={generatePath(planType)}>
            <Button type="primary">新建计划</Button>
          </Link>
          {shouldActivateButtonShow && (
            <Button
              disabled={!(selectedIds.length > 0)}
              onClick={async () => {
                const { error } = await batchUpdatePlanStatus({
                  ids: selectedIds,
                  status: PlanStatus.On,
                });
                if (error) {
                  message.error(error.message);
                  return;
                }
                message.success('批量启用成功');
                setSelectedIds([]);
                togglePlans({
                  ...planQueryParams,
                  pageNum: planPagination.pageNum,
                  pageSize: planPagination.pageSize,
                });
              }}
            >
              启用
            </Button>
          )}
          {shouldActivateButtonShow && (
            <Button
              disabled={!(selectedIds.length > 0)}
              onClick={async () => {
                const { error } = await batchUpdatePlanStatus({
                  ids: selectedIds,
                  status: PlanStatus.Off,
                });
                if (error) {
                  message.error(error.message);
                  return;
                }
                message.success('批量禁用成功');
                setSelectedIds([]);
                togglePlans({
                  ...planQueryParams,
                  pageNum: planPagination.pageNum,
                  pageSize: planPagination.pageSize,
                });
              }}
            >
              禁用
            </Button>
          )}
        </Space>
        {isDrillTask && (
          <FileExport
            filename="演练任务明细.xls"
            disabled={exportLoading}
            data={type => {
              return handleFileExport(type);
            }}
            showExportFiltered
          />
        )}
      </Space>
      <Table<PlanJSON>
        style={{ width: '100%' }}
        rowKey="id"
        dataSource={plans}
        scroll={{ x: 'max-content' }}
        rowSelection={{
          selectedRowKeys: selectedIds,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedIds(selectedRowKeys as number[]);
          },
        }}
        pagination={{
          total: planTotal,
          pageSize: planPagination.pageSize,
          current: planPagination.pageNum,
          onChange: (current, size) => {
            togglePlanPagination({ pageNum: current, pageSize: size });
            togglePlans({ ...planQueryParams, pageNum: current, pageSize: size });
          },
        }}
        loading={planLoading}
        columns={columns}
      />
    </Space>
  );
}

function generatePath(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH;
    case PlanType.InspectionPlan:
      return CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH;
    case PlanType.InventoryPlan:
      return CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH;
    case PlanType.DrillPlan:
      return CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH;
    default:
      return '';
  }
}

function generateTicketType(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return 'MAINTENANCE';
    case PlanType.InspectionPlan:
      return 'INSPECTION';
    case PlanType.InventoryPlan:
      return 'INVENTORY';
    default:
      return '';
  }
}
