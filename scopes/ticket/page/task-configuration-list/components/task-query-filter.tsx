import { omit } from 'lodash';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { useTaskData } from '@manyun/ticket.context.task-data';
import type { PlanEffect, PlanStatus, TaskEffectStatus } from '@manyun/ticket.model.task';
import {
  CYCLES_TYPE_OPTIONS,
  PLAN_EFFECT_OPTIONS,
  PLAN_STATUS_OPTIONS,
  PlanLayer,
  PlanType,
  TASK_EFFECT_STATUS_OPTIONS,
} from '@manyun/ticket.model.task';
import { MaintenanceCycleSelect } from '@manyun/ticket.ui.maintenance-cycle-select';
import { TaskConfigurationSelect } from '@manyun/ticket.ui.task-configuration-select';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

type FiltersFormValues = {
  blockGuidList?: string[];
  subJobType?: string;
  periodUnit?: string;
  name?: string;
  isInvalid?: PlanEffect;
  taskStatus?: PlanStatus;
  creator?: { label: string; value: number };
  gmtCreate?: string;
  effectStatus?: TaskEffectStatus;
  year?: Moment;
  month?: Moment;
};
export type TaskQueryFilterProps = {
  /** 用于区分 巡检、维护还是盘点类型 */
  planType: PlanType;
  /** 用于区分 调用的查询接口是 计划、年度还是月度 */
  layer: PlanLayer;
  /** 接受三个不同层次的传参 */
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  getData: (params: any) => void;

  resetPagination?: (params: { pageNum: number; pageSize: number }) => void;
};

export function TaskQueryFilter({
  planType,
  layer,
  getData,
  resetPagination,
}: TaskQueryFilterProps) {
  const [form] = Form.useForm<FiltersFormValues>();
  const [
    { year, month },
    { toggleYear, togglePlanQueryParams, toggleMonthlyCalendarQueryParams, toggleMonth },
  ] = useTaskData();
  const onSearch = (value: FiltersFormValues) => {
    const { creator, gmtCreate, year, month, ...rest } = value;
    const isDrillPlan = planType === PlanType.DrillPlan;

    switch (layer) {
      case PlanLayer.Plans:
        togglePlanQueryParams(value);
        getData({
          ...rest,
          creatorId: creator?.value,
          startTime: gmtCreate
            ? isDrillPlan
              ? moment(gmtCreate[0]).startOf('day').valueOf()
              : moment(gmtCreate[0]).milliseconds(0).valueOf()
            : undefined,
          endTime: gmtCreate
            ? isDrillPlan
              ? moment(gmtCreate[1]).endOf('day').valueOf()
              : moment(gmtCreate[1]).milliseconds(999).valueOf()
            : undefined,
        });
        break;
      case PlanLayer.AnnualPlans:
        if (year) {
          toggleYear(year.format('YYYY'));
          getData({
            ...rest,
            year: year,
          });
        } else {
          message.error('年份必选');
        }
        break;
      case PlanLayer.MonthlyPlans:
        if (month) {
          toggleMonth(month.format('YYYY-MM'));
          toggleMonthlyCalendarQueryParams({
            ...omit(value, 'year'),
            month: month?.format('YYYY-MM'),
          });
          getData({
            ...rest,
            month: month,
          });
        } else {
          message.error('月份必选');
        }

        break;
      default:
        break;
    }
  };

  const onReset = () => {
    switch (layer) {
      case PlanLayer.Plans:
        getData({});
        togglePlanQueryParams({});
        resetPagination && resetPagination({ pageNum: 1, pageSize: 10 });
        break;
      case PlanLayer.AnnualPlans:
        getData({ year: moment() });
        resetPagination && resetPagination({ pageNum: 1, pageSize: 10 });
        toggleYear(moment().format('YYYY'));
        form.setFieldsValue({ year: moment() });
        break;
      case PlanLayer.MonthlyPlans:
        getData({ month: moment() });
        toggleMonth(moment().format('YYYY-MM'));
        form.setFieldsValue({ month: moment() });
        break;
      default:
        break;
    }
  };
  useEffect(() => {
    form.setFieldsValue({ year: moment(year), month: moment(month) });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [year, month]);
  return (
    <QueryFilter<FiltersFormValues>
      form={form}
      defaultExpanded={layer === PlanLayer.AnnualPlans}
      items={generateFilterItems(planType, layer)}
      onSearch={onSearch}
      onReset={onReset}
    />
  );
}

function generateFilterItems(planType: PlanType, planLayer: PlanLayer) {
  const isDrillPlan = planType === PlanType.DrillPlan;

  const items = [
    {
      label: '机房楼栋',
      name: 'blockGuidList',

      control: <LocationTreeSelect multiple authorizedOnly disabledTypes={['IDC']} allowClear />,
    },
    {
      label: generateSpecificDataByTaskType(planType).columnTitle,
      name: 'subJobType',

      control: (
        <TaskConfigurationSelect
          ticketPlanType={generateSpecificDataByTaskType(planType).ticketType}
          allowClear
          isAllMaintenance
        />
      ),
    },
  ];
  switch (planLayer) {
    case PlanLayer.Plans:
      const basicFilters = [
        ...items,
        {
          label: '计划名称',
          name: 'name',
          control: <Input aria-label="instId" allowClear />,
        },
        generatePeriodItem(planType),
        {
          label: '是否有效',
          name: 'isInvalid',

          control: <Select options={PLAN_EFFECT_OPTIONS} allowClear />,
        },

        {
          label: '启用状态',
          name: 'taskStatus',

          control: <Select options={PLAN_STATUS_OPTIONS} allowClear />,
        },
        {
          label: '创建人',
          name: 'creator',

          control: <UserSelect allowClear />,
        },

        {
          label: '创建时间',
          name: 'gmtCreate',
          span: 2,
          control: isDrillPlan ? (
            <DatePicker.RangePicker allowClear />
          ) : (
            <DatePicker.RangePicker showTime allowClear format="YYYY-MM-DD HH:mm:ss" />
          ),
        },
      ];
      if (isDrillPlan) {
        basicFilters.splice(
          1,
          1,
          {
            label: '专业类型',
            name: 'subJobType',
            control: (
              <MetaTypeSelect
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_MAJOR}
                allowClear
              />
            ),
          },
          {
            label: '等级',
            name: 'schLevelList',
            control: (
              <MetaTypeSelect
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_LEVEL}
                mode="multiple"
                allowClear
              />
            ),
          }
        );
      }
      return basicFilters;

    case PlanLayer.AnnualPlans:
      const basicAnnualFilter = [
        {
          label: '选择年份',
          name: 'year',
          require: true,
          control: <DatePicker picker="year" format="YYYY" />,
        },
        ...items,
        generatePeriodItem(planType),
        {
          label: '任务名称',
          name: 'name',
          control: <Input aria-label="instId" allowClear />,
        },
      ];
      if (isDrillPlan) {
        basicAnnualFilter.splice(
          2,
          1,
          {
            label: '专业类型',
            name: 'subJobType',
            control: (
              <MetaTypeSelect
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_MAJOR}
                allowClear
              />
            ),
          },
          {
            label: '等级',
            name: 'schLevel',
            control: (
              <MetaTypeSelect
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_LEVEL}
                allowClear
              />
            ),
          }
        );
      }
      return basicAnnualFilter;
    case PlanLayer.MonthlyPlans:
      const basicMonthlyFilter = [
        {
          label: '选择月份',
          name: 'month',

          control: <DatePicker picker="month" format="YYYY-MM" />,
        },
        ...items,
        generatePeriodItem(planType),
        {
          label: '任务名称',
          name: 'name',
          control: <Input aria-label="instId" allowClear />,
        },

        {
          label: '任务状态',
          name: 'effectStatus',
          control: <Select options={TASK_EFFECT_STATUS_OPTIONS} allowClear />,
        },
      ];
      if (isDrillPlan) {
        basicMonthlyFilter.splice(
          2,
          1,
          {
            label: '专业类型',
            name: 'subJobType',
            control: (
              <MetaTypeSelect
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_MAJOR}
                allowClear
              />
            ),
          },
          {
            label: '等级',
            name: 'schLevel',
            control: (
              <MetaTypeSelect
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_LEVEL}
                allowClear
              />
            ),
          }
        );
      }
      return basicMonthlyFilter;
    default:
      return items;
  }
}

function generatePeriodItem(planType: PlanType) {
  return planType === PlanType.MaintenancePlan
    ? {
        label: '维护周期',
        name: 'guidePeriod',
        control: <MaintenanceCycleSelect allowClear />,
      }
    : {
        label: '重复周期',
        name: 'periodUnit',

        control: <Select options={CYCLES_TYPE_OPTIONS} allowClear />,
      };
}
