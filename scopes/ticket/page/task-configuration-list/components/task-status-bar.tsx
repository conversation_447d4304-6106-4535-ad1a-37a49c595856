import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Space } from '@manyun/base-ui.ui.space';

import { TASK_EFFECT_STATUS_TEXT, TaskEffectStatus } from '@manyun/ticket.model.task';

export function TaskStatusBar() {
  return (
    <Space>
      <Badge color="gold" text={TASK_EFFECT_STATUS_TEXT[TaskEffectStatus.Ineffective]} />
      <Badge color="green" text={TASK_EFFECT_STATUS_TEXT[TaskEffectStatus.Effected]} />
    </Space>
  );
}
