import { ExclamationCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { TaskDataContext } from '@manyun/ticket.context.task-data';
import type {
  BackendMonthlyTaskSearchParam,
  BackendPlanSearchParam,
  MonthlyTasks,
  PlanJSON,
} from '@manyun/ticket.model.task';
import { JobType, PlanLayer, PlanType } from '@manyun/ticket.model.task';
import {
  AnnualTasks,
  MonthlyCalendar,
  PlanList,
} from '@manyun/ticket.page.task-configuration-list';
import { fetchCalendarTasks } from '@manyun/ticket.service.fetch-calendar-tasks';
import { fetchTasks } from '@manyun/ticket.service.fetch-tasks';

const { confirm } = Modal;
export type TaskConfigurationListProps = {
  planType: PlanType;
};
const tabList = (planType: PlanType) => [
  {
    key: PlanLayer.Plans,
    tab: generateTabNameByType(planType),
  },
  {
    key: PlanLayer.AnnualPlans,
    tab: '年度任务',
  },
  {
    //  - 链接，全部小写，抽一个类型作为useState
    key: PlanLayer.MonthlyPlans,
    tab: '月度任务',
  },
];

export function TaskConfigurationList({ planType }: TaskConfigurationListProps) {
  const [activeKey, setActiveKey] = useState<PlanLayer>(PlanLayer.Plans);
  const [plans, setPlans] = useState<PlanJSON[]>([]);
  const [monthlyTasks, setMonthlyTasks] = useState<MonthlyTasks[]>([]);
  const [monthlyTasksLoading, setMonthlyTasksLoading] = useState(false);
  const [planLoading, setPlanLoading] = useState(false);
  const [annualYear, setAnnualYear] = useState<string>(dayjs().format('YYYY'));
  const [month, setMonth] = useState<string>(dayjs().format('YYYY-MM'));

  const [planTotal, setPlanTotal] = useState(0);
  const [planQueryParams, setPlanQueryParams] = useState<BackendPlanSearchParam>();
  const [monthlyCalendarQueryParams, setMonthlyCalendarQueryParams] =
    useState<BackendMonthlyTaskSearchParam>({
      jobTypeList: [],
      endDate: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
      startDate: dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
    });
  const [planPagination, setPlanPagination] = useState<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const [isEditable, setIsEditable] = useState(false);
  const saveTwiceRef = React.useRef(false);
  const contentList: Record<string, React.ReactNode> = {
    [PlanLayer.Plans]: <PlanList planType={planType} />,
    [PlanLayer.AnnualPlans]: <AnnualTasks planType={planType} />,
    [PlanLayer.MonthlyPlans]: (
      <MonthlyCalendar
        planType={planType}
        isEditable={isEditable}
        setIsEditable={setIsEditable}
        saveTwiceRef={saveTwiceRef}
      />
    ),
  };
  const getPlans = async (
    params: BackendPlanSearchParam & { pageNum: number; pageSize: number }
  ) => {
    setPlanLoading(true);
    const { error, data } = await fetchTasks({
      ...params,
      jobTypeList: generateJobType(planType),
    });
    setPlanLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setPlans(data.data);
    setPlanTotal(data.total);
  };

  const getMonthlyTasks = async (
    params: BackendMonthlyTaskSearchParam & { month?: string; year?: string }
  ) => {
    setMonthlyTasksLoading(true);
    const { month, year, ...rest } = params;
    const dateRange = { startDate: '', endDate: '' };
    if (month) {
      dateRange.startDate = dayjs(month).startOf('month').format('YYYY-MM-DD');
      dateRange.endDate = dayjs(month).endOf('month').format('YYYY-MM-DD');
    }
    if (year) {
      dateRange.startDate = dayjs(year).startOf('year').format('YYYY-MM-DD');
      dateRange.endDate = dayjs(year).endOf('year').format('YYYY-MM-DD');
    }
    const { error, data } = await fetchCalendarTasks({
      ...rest,
      ...dateRange,
      jobTypeList: generateJobType(planType),
    });
    setMonthlyTasksLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setMonthlyTasks(data.data);
  };

  useEffect(() => {
    if (activeKey === PlanLayer.Plans) {
      getPlans({
        pageNum: planPagination.pageNum,
        pageSize: planPagination.pageSize,
        jobTypeList: generateJobType(planType),
      });
    }
    if (activeKey === PlanLayer.MonthlyPlans) {
      getMonthlyTasks({
        month: dayjs().format('YYYY-MM'),
        jobTypeList: generateJobType(planType),
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [planType, activeKey]);
  return (
    <TaskDataContext.Provider
      value={[
        {
          planType: planType,
          plans: plans,
          planLoading: planLoading,
          planTotal: planTotal,
          planPagination: planPagination,
          planQueryParams: planQueryParams,
          year: annualYear,
          month: month,
          monthlyCalendarQueryParams: monthlyCalendarQueryParams,
          monthlyTasks: monthlyTasks,
          monthlyTasksLoading: monthlyTasksLoading,
        },
        {
          togglePlans: getPlans,

          toggleMonthlyTasks: getMonthlyTasks,
          togglePlanPagination: (params: { pageNum: number; pageSize: number }) => {
            setPlanPagination(params);
          },
          toggleYear: (params: string) => {
            setAnnualYear(params);
          },
          togglePlanQueryParams: setPlanQueryParams,
          toggleMonthlyCalendarQueryParams: setMonthlyCalendarQueryParams,
          toggleMonth: setMonth,
        },
      ]}
    >
      <Card
        activeTabKey={activeKey}
        tabList={tabList(planType)}
        onTabChange={key => {
          if (isEditable && key !== PlanLayer.MonthlyPlans) {
            confirm({
              title: '当前修改未保存，是否保存修改',
              icon: <ExclamationCircleOutlined />,
              content: '',
              onOk() {
                saveTwiceRef.current = true;
                setActiveKey(key as PlanLayer);
              },
              onCancel() {
                setActiveKey(key as PlanLayer);
              },
              okText: '保存修改',
              cancelText: '不保存',
            });
            return;
          }
          setActiveKey(key as PlanLayer);
        }}
      >
        {contentList[activeKey]}
      </Card>
    </TaskDataContext.Provider>
  );
}

function generateTabNameByType(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return '维护计划';
    case PlanType.InspectionPlan:
      return '巡检计划';
    case PlanType.InventoryPlan:
      return '盘点计划';
    case PlanType.DrillPlan:
      return '演练计划';
    default:
      return null;
  }
}
export function generateJobType(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return [JobType.DeviceMaintenance, JobType.ToolsMaintenance];
    case PlanType.InspectionPlan:
      return [JobType.DeviceInspection];
    case PlanType.InventoryPlan:
      return [JobType.InventoryCount];
    case PlanType.DrillPlan:
      return [PlanType.DrillPlan];
    default:
      return [];
  }
}
