import uniq from 'lodash/uniq';
import React, { useEffect, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import { RoleText } from '@manyun/auth-hub.ui.role-text';
import { User } from '@manyun/auth-hub.ui.user';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Card } from '@manyun/base-ui.ui.card';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import type {
  AggBlock,
  Cycle,
  JobItem,
  MonthlyWorkDayType,
  PlanJSON,
  SpecifyTarget,
  Splitor,
  ThreeDeviceType,
  WeekType,
} from '@manyun/ticket.model.task';
import {
  CyclesType,
  DAY_WORK_WEEK_TEXT_MAP,
  DISTRIBUTE_MODE_TEXT_MAP,
  DistributeMode,
  MAINTENANCE_CYCLE_TEXT_MAP,
  PLAN_STATUS_TEXT_MAP,
  Plan,
  PlanType,
  SPLTOR_TYPE_TEXT_MAP,
  SplitorType,
  THREE_DEVICE_TYPE_TEXT_MAP,
  WEEK_DAY_TEXT_MAP,
} from '@manyun/ticket.model.task';
import { fetchMopConfig } from '@manyun/ticket.service.fetch-mop-config';
import { fetchPlanDetail } from '@manyun/ticket.service.fetch-plan-detail';
import { FiltersTicketsModal } from '@manyun/ticket.ui.filters-tickets-modal';
import { TicketTypeText } from '@manyun/ticket.ui.ticket-type-text';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

export type PlanDetailProps = {
  planType: PlanType;
};

export function PlanDetail({ planType }: PlanDetailProps) {
  const { id } = useParams<{ id: string }>();
  const [detailInfo, setDetailInfo] = useState<PlanJSON>();
  const [loading, setLoading] = useState(false);
  const planTypeText = generateSpecificDataByTaskType(planType).planTypeText;

  const isMaintenancePlan = planType === PlanType.MaintenancePlan;
  const handleLinkClick = async (jobId: number, name: string, planType: PlanType) => {
    const { error, data } = await fetchMopConfig({ mopRelateId: jobId });
    if (error) {
      message.error(error.message);
      return;
    }
    window.open(
      generateSpecificDataByTaskType(planType).generateRoutePath({
        id: String(data!.id),
        name: name,
      })
    );
  };
  useEffect(() => {
    (async function () {
      setLoading(true);
      const { data, error } = await fetchPlanDetail({ id });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setDetailInfo(data as PlanJSON);
    })();
  }, [id]);
  return (
    <Spin spinning={loading}>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Card title="基本信息">
          <Descriptions column={4}>
            <Descriptions.Item label="机房楼栋">
              {detailInfo?.blockScope && detailInfo.blockScope[0].blockGuid}
            </Descriptions.Item>
            <Descriptions.Item label="计划名称">{detailInfo?.name}</Descriptions.Item>
            <Descriptions.Item label={`${generateSpecificDataByTaskType(planType).prefix}类型`}>
              <TicketTypeText
                code={`${
                  generateSpecificDataByTaskType(planType).ticketType
                }${detailInfo?.mopType}`}
              />
            </Descriptions.Item>
            <Descriptions.Item label="状态">
              {detailInfo?.isActivated ? PLAN_STATUS_TEXT_MAP[detailInfo.isActivated] : '--'}
            </Descriptions.Item>
            {isMaintenancePlan && (
              <Descriptions.Item label="维护周期">
                {detailInfo?.guidePeriod
                  ? MAINTENANCE_CYCLE_TEXT_MAP[detailInfo.guidePeriod]
                  : '--'}
              </Descriptions.Item>
            )}
            <Descriptions.Item label={isMaintenancePlan ? '计划排期' : '重复周期'}>
              {detailInfo?.repeatCycle && getCycsTxt(detailInfo.repeatCycle)}
            </Descriptions.Item>
            <Descriptions.Item label="结束时间">
              {detailInfo && Plan.fromJSON(detailInfo).getFormattedEndTime()}
            </Descriptions.Item>
            <Descriptions.Item label="触发时间">
              {detailInfo?.allowTriggerTime &&
                Array.isArray(detailInfo.allowTriggerTime) &&
                detailInfo.allowTriggerTime.join('、')}
            </Descriptions.Item>
            <Descriptions.Item label="上次运行时间">
              {detailInfo && Plan.fromJSON(detailInfo).getFormattedLastExecuteAt()}
            </Descriptions.Item>

            <Descriptions.Item label="上次运行结果">
              {detailInfo?.lastExecuteResult?.length ? (
                <FiltersTicketsModal
                  title="上次创建的工单记录"
                  btnText={detailInfo.lastExecuteResult.length + ''}
                  taskNos={detailInfo.lastExecuteResult}
                />
              ) : (
                0
              )}
            </Descriptions.Item>
            <Descriptions.Item label="创建人">
              <User.Link id={detailInfo?.creator.id} />
            </Descriptions.Item>
            <Descriptions.Item label="创建时间">
              {detailInfo && Plan.fromJSON(detailInfo).getFormattedCreatedAt()}
            </Descriptions.Item>
          </Descriptions>
        </Card>
        <Card title="任务配置">
          <Space direction="vertical" style={{ width: '100%' }}>
            <Descriptions column={4}>
              <Descriptions.Item
                span={4}
                label={generateSpecificDataByTaskType(planType)?.prefix}
                labelStyle={{ width: 90 }}
              >
                <Table<
                  | {
                      id: number;
                      jobId: number;
                      scheduleId: number;
                      name: string;
                      itemType: string;
                      scheduleType: string;
                      jobTypeName: string;
                      jobTypeCode: string;
                      subJobTypeName: string;
                      subJobTypeCode: { code: string; name: string }[];
                      gmtCreate: string;
                    }
                  | JobItem
                >
                  columns={[
                    {
                      title: `${generateSpecificDataByTaskType(planType).prefix}名称`,
                      dataIndex: 'name',
                      render(_, { name, jobId }) {
                        return isMaintenancePlan ? (
                          <Typography.Text
                            style={{
                              color: `var(--${prefixCls}-primary-color)`,
                              cursor: 'pointer',
                            }}
                            ellipsis={{ tooltip: true }}
                            onClick={() => handleLinkClick(jobId, name, planType)}
                          >
                            {name}
                          </Typography.Text>
                        ) : (
                          <Link
                            type="link"
                            target="_blank"
                            to={generateSpecificDataByTaskType(planType).generateRoutePath({
                              id: String(jobId),
                              name: name,
                            })}
                          >
                            <Typography.Text
                              style={{ color: `var(--${prefixCls}-primary-color)` }}
                              ellipsis={{ tooltip: true }}
                            >
                              {name}
                            </Typography.Text>
                          </Link>
                        );
                      },
                    },
                    {
                      title: `${generateSpecificDataByTaskType(planType).prefix}类型`,
                      dataIndex: 'jobTypeName',
                    },
                    {
                      title: planType === PlanType.InspectionPlan ? '巡检对象类型' : '资产分类',
                      width: '50%',
                      dataIndex: 'subJobTypeName',
                      ellipsis: true,
                    },
                  ]}
                  dataSource={
                    detailInfo?.jobItemList?.length
                      ? generateJobItemsByType(planType, detailInfo.jobItemList)
                      : []
                  }
                />
              </Descriptions.Item>
              <Descriptions.Item label="分发规则" span={4} labelStyle={{ width: 90 }}>
                {detailInfo?.aggBlockScope?.length ? (
                  <Space>
                    <span>{DISTRIBUTE_MODE_TEXT_MAP[detailInfo.aggBlockScope[0].scopeType]}</span>
                    {detailInfo.aggBlockScope.map(i => generateDistributeRules(i))}
                  </Space>
                ) : (
                  '--'
                )}
              </Descriptions.Item>
              {detailInfo?.scheduleSplitRangeList &&
                detailInfo.scheduleSplitRangeList.length > 0 && (
                  <Descriptions.Item
                    label={
                      <Explanation
                        iconType="question"
                        tooltip={{
                          title: `根据生效次数批量生成目标${planTypeText}对象，一个周期覆盖所有目标${planTypeText}对象，若目标策略包含不同指定类型则只会生效交集部分，且更改次数或者新选择/删除${
                            generateSpecificDataByTaskType(planType).prefix
                          }则重新生效逻辑。`,
                        }}
                      >
                        <Typography.Text>目标策略</Typography.Text>
                      </Explanation>
                    }
                    span={4}
                    labelStyle={{ width: 90 }}
                  >
                    <SpecificTargetsCollapse targets={detailInfo.scheduleSplitRangeList} />
                  </Descriptions.Item>
                )}

              <Descriptions.Item label="拆分规则" span={4} labelStyle={{ width: 90 }}>
                {detailInfo?.splitor?.length ? (
                  <Space direction="vertical">
                    <span>{generateSplitorRules(detailInfo.splitor, true)}</span>
                    <span>{generateSplitorRules(detailInfo.splitor, false)}</span>
                  </Space>
                ) : (
                  '不拆分'
                )}
              </Descriptions.Item>
              <Descriptions.Item label="SLA" span={4} labelStyle={{ width: 90 }}>
                {detailInfo?.jobSla && (
                  <TicketSlaText
                    unit="SECOND"
                    taskSla={0}
                    delay={detailInfo.jobSla}
                    effectTime={null}
                    endTime={null}
                  />
                )}
              </Descriptions.Item>
            </Descriptions>
          </Space>
        </Card>
      </Space>
    </Spin>
  );
}

function generateJobItemsByType(planType: PlanType, jobItemList: JobItem[]) {
  switch (planType) {
    case PlanType.InventoryPlan:
      const newData: {
        id: number;
        jobId: number;
        scheduleId: number;
        name: string;
        itemType: string;
        scheduleType: string;
        jobTypeName: string;
        jobTypeCode: string;
        subJobTypeName: string;
        subJobTypeCode: { code: string; name: string }[];
        gmtCreate: string;
      }[] = [];
      const ids = uniq(jobItemList.map(({ jobId }) => jobId));
      ids.forEach(jobId => {
        const sameJobIdData = jobItemList.filter(item => item.jobId === jobId);
        const tmp = {
          ...sameJobIdData[0],
          subJobTypeCode: sameJobIdData.map(({ subJobTypeName, subJobTypeCode }) => {
            return {
              code: subJobTypeCode,
              name: subJobTypeName,
            };
          }),
          subJobTypeName: sameJobIdData.map(({ subJobTypeName }) => subJobTypeName).join('|'),
        };
        newData.push(tmp);
      });
      return newData;
    case PlanType.MaintenancePlan:
    case PlanType.InspectionPlan:
    default:
      return jobItemList;
  }
}

function generateSplitorRules(splitors: Splitor[], isSpace: boolean) {
  if (isSpace) {
    const spaceSplitor = splitors.filter(item =>
      [SplitorType.Room, SplitorType.Floor].includes(item.splitType)
    )[0];
    if (spaceSplitor) {
      switch (spaceSplitor?.splitType) {
        case SplitorType.Room:
          return SPLTOR_TYPE_TEXT_MAP[SplitorType.Room];
        case SplitorType.Floor:
          return (
            <Space>
              <span>{SPLTOR_TYPE_TEXT_MAP[SplitorType.Floor]}</span>
              <span>{`每${spaceSplitor.splitParam}层楼拆分`}</span>
            </Space>
          );
      }
    }
    return null;
  } else {
    const deviceSplitor = splitors.filter(item => item.splitType === SplitorType.DeviceType)[0];
    if (deviceSplitor) {
      return (
        <Space>
          <span>{SPLTOR_TYPE_TEXT_MAP[SplitorType.DeviceType]}</span>
          <span>{`以设备的${
            THREE_DEVICE_TYPE_TEXT_MAP[deviceSplitor.splitParam as ThreeDeviceType]
          }拆分`}</span>
        </Space>
      );
    }
    return null;
  }
}

function generateDistributeRules(distributeData: AggBlock) {
  return <span>{generateDistributeObject(distributeData)}</span>;
}

function generateDistributeObject(distributeData: AggBlock) {
  const { scopeType, scopeFlag, scopeFlagInfoList } = distributeData;
  switch (scopeType) {
    case DistributeMode.Block:
      return '楼栋下人员';
    case DistributeMode.BlockUser:
      return <User.Link id={Number(scopeFlag)} />;

    case DistributeMode.BlockRole:
      return <RoleText id={Number(scopeFlag)} />;

    case DistributeMode.BlockAttGroup:
      return <Space>{scopeFlagInfoList.map(item => item.flagName)}</Space>;
    default:
      return '--';
  }
}

function getCycsTxt(cycles: Cycle) {
  if (!cycles) {
    return;
  }
  if (cycles.periodUnit === CyclesType.Year) {
    let txt = '';
    // 每逢
    if (cycles.dayConfig) {
      const days = cycles.dayConfig?.days?.map(item => {
        return item.substr(0, 2) + '月' + item.substr(2, 3) + '日';
      });
      txt = `每${cycles.period}年的${days?.join('、')}${getSchedulePostText(
        cycles.dayConfig.isRemove
      )}`;
    }
    // 在
    if (cycles.atDayConfig) {
      const days = cycles.atDayConfig.map(({ month }) => month + '月');
      txt = `每${cycles.period}年的${days.join('、')}的第${cycles.atDayConfig[0].sortNum}个${
        DAY_WORK_WEEK_TEXT_MAP[cycles.atDayConfig[0].atDayType as MonthlyWorkDayType]
      }`;
    }

    return txt;
  }
  if (cycles.periodUnit === CyclesType.Month) {
    let txt = '';

    // 每逢
    if (cycles.dayConfig) {
      txt = `每${cycles.period}月的${cycles.dayConfig.days?.join('、')}${getSchedulePostText(
        cycles.dayConfig.isRemove
      )}`;
    }

    // 在
    if (cycles.atDayConfig) {
      txt = `每${cycles.period}月的第${cycles.atDayConfig[0].sortNum}个${
        DAY_WORK_WEEK_TEXT_MAP[cycles.atDayConfig[0].atDayType as MonthlyWorkDayType]
      }`;
    }
    return txt;
  }
  if (cycles.periodUnit === CyclesType.Week) {
    let txt = '';
    const days = cycles.dayConfig.days?.map(item => WEEK_DAY_TEXT_MAP[item as WeekType]);
    txt = `每${cycles.period}周的${days?.join('、')}${getSchedulePostText(
      cycles.dayConfig.isRemove
    )}`;
    return txt;
  }
  if (cycles.periodUnit === CyclesType.Day) {
    const txt = `每${cycles.period}天${getSchedulePostText(cycles.dayConfig.isRemove)}`;
    return txt;
  }
  if (cycles.periodUnit === CyclesType.None) {
    return '无';
  }
  return;
}
function getSchedulePostText(isRemove: number) {
  if (isRemove === 0) {
    return '';
  } else if (isRemove === 1) {
    return '（如遇节假日，则自动取消当日排期）';
  } else {
    return '（如遇节假日，则自动顺延至下一个工作日）';
  }
}
function SpecificTargetsCollapse({
  targets,
}: {
  targets: (SpecifyTarget & {
    uniFlag: string;
    cycleTimes: number;
    filterType?: string;
    flagValues?: Record<string, string[]>;
    DeviceTypeCodes?: string[];
  })[];
}) {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const scheduleList: any[] = [];
  const shieldingList: any[] = [];
  const otherSchedule = targets.find(item => item.rangeType === 'OTHER');
  targets
    .filter(item => item.rangeType !== 'OTHER')
    .map(item => {
      return {
        ...item,
        flagValues: { [item.flagKey]: item.flagValue },
        DeviceTypeCodes: [item.flagKey],
      };
    })
    .forEach(item => {
      const { uniFlag, rangeType, uniFlagType } = item;
      if (uniFlagType === 'WHITE') {
        if (!scheduleList.find(schedule => schedule.uniFlag === uniFlag)) {
          scheduleList.push({
            ...item,
            flagValue: rangeType === 'DEVICE' ? item.flagValues : item.flagValue,
          });
        } else {
          scheduleList[scheduleList.length - 1] = {
            ...scheduleList[scheduleList.length - 1],
            DeviceTypeCodes: scheduleList[scheduleList.length - 1].DeviceTypeCodes.concat(
              item.DeviceTypeCodes
            ),
            flagValue: { ...scheduleList[scheduleList.length - 1].flagValue, ...item.flagValues },
          };
        }
      }
      if (uniFlagType === 'BLACK') {
        if (!shieldingList.find(schedule => schedule.uniFlag === uniFlag)) {
          shieldingList.push({
            ...item,
            flagValue: rangeType === 'DEVICE' ? item.flagValues : item.flagValue,
          });
        } else {
          shieldingList[shieldingList.length - 1] = {
            ...shieldingList[shieldingList.length - 1],
            DeviceTypeCodes: shieldingList[shieldingList.length - 1].DeviceTypeCodes.concat(
              item.DeviceTypeCodes
            ),
            flagValue: { ...shieldingList[shieldingList.length - 1].flagValue, ...item.flagValues },
          };
        }
      }
    });
  return (
    <Space style={{ width: '100%' }} direction="vertical">
      {scheduleList.length >= 1 && (
        <Collapse style={{ width: '100%' }} defaultActiveKey={scheduleList[0].uniFlag}>
          {scheduleList.map(schedule => {
            return (
              <Collapse.Panel
                key={schedule.uniFlag}
                header={
                  <Typography.Text>{`在指定${
                    schedule.rangeType === 'DEVICE' ? '对象' : '包间'
                  }下，每生效${schedule.cycleTimes}次为一个周期 `}</Typography.Text>
                }
              >
                <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                  {schedule.rangeType === 'DEVICE' ? (
                    Array.isArray(schedule.DeviceTypeCodes) && (
                      <Space style={{ flexWrap: 'wrap' }}>
                        {schedule.DeviceTypeCodes.map((deviceCode: string) => {
                          return (
                            <Space key={deviceCode} direction="vertical">
                              <DeviceTypeText code={deviceCode} />
                              <DeviceNames guids={schedule.flagValue[deviceCode]} />
                            </Space>
                          );
                        })}
                      </Space>
                    )
                  ) : (
                    <Space style={{ flexWrap: 'wrap' }}>
                      {schedule.flagValue.map((item: string) => (
                        <div>
                          <Typography.Text> {item}</Typography.Text>
                          <Divider
                            style={{ margin: '0px 4px 0px 10px' }}
                            type="vertical"
                            spaceSize="mini"
                          />
                        </div>
                      ))}
                    </Space>
                  )}
                </div>
              </Collapse.Panel>
            );
          })}
        </Collapse>
      )}
      {!!otherSchedule && (
        <Typography.Text>{`配置除指定对象以外的目标策略，每生效${otherSchedule.cycleTimes}次为一个周期`}</Typography.Text>
      )}
      {shieldingList.length >= 1 && (
        <Collapse style={{ width: '100%' }} defaultActiveKey={shieldingList[0].uniFlag}>
          {shieldingList.map(schedule => {
            return (
              <Collapse.Panel
                key={schedule.uniFlag}
                header={<Typography.Text>屏蔽对象</Typography.Text>}
              >
                <div style={{ maxHeight: '400px', overflow: 'auto' }}>
                  {schedule.rangeType === 'DEVICE' ? (
                    Array.isArray(schedule.DeviceTypeCodes) && (
                      <Space style={{ flexWrap: 'wrap' }}>
                        {schedule.DeviceTypeCodes.map((deviceCode: string) => {
                          return (
                            <Space key={deviceCode} direction="vertical">
                              <DeviceTypeText code={deviceCode} />
                              <DeviceNames guids={schedule.flagValue[deviceCode]} />
                            </Space>
                          );
                        })}
                      </Space>
                    )
                  ) : (
                    <Space style={{ flexWrap: 'wrap' }}>
                      {schedule.flagValue.map((item: string) => (
                        <div>
                          <Typography.Text> {item}</Typography.Text>
                          <Divider
                            style={{ margin: '0px 4px 0px 10px' }}
                            type="vertical"
                            spaceSize="mini"
                          />
                        </div>
                      ))}
                    </Space>
                  )}
                </div>
              </Collapse.Panel>
            );
          })}
        </Collapse>
      )}
    </Space>
  );
}

function DeviceNames({ guids }: { guids: string[] }) {
  const [names, setNames] = useState<string[]>([]);
  useEffect(() => {
    (async function () {
      const { data, error } = await fetchPageDevices({
        deviceGuidList: guids,
        pageNum: 1,
        pageSize: guids.length,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setNames(data.data.map(item => item.name));
    })();
  }, [guids]);
  return (
    <Space style={{ flexWrap: 'wrap' }}>
      {names.map(item => (
        <div>
          <Typography.Text key={item} type="secondary">
            {item}
          </Typography.Text>
          <Divider style={{ margin: '0px 4px 0px 10px' }} type="vertical" spaceSize="mini" />
        </div>
      ))}
    </Space>
  );
}
