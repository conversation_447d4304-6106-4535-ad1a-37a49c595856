/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-14
 *
 * @packageDocumentation
 */
import dayjs from 'dayjs';
import { saveAs } from 'file-saver';
import findLastIndex from 'lodash/findLastIndex';
import { nanoid } from 'nanoid';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import {
  useDrillConfigCreation,
  useDrillConfigUpdate,
  useLazyDrillConfig,
} from '@manyun/ticket.gql.client.tickets';
import { CONFIG_RANGE_KEY_MAP, ConfigRangeType } from '@manyun/ticket.model.task';
import type { TicketSlaUnit } from '@manyun/ticket.model.ticket';
import { TicketSlaUnitKeyTextMap } from '@manyun/ticket.model.ticket';
import { DRILL_CONFIG_LIST_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';
import { downloadDrillPlanTemplate } from '@manyun/ticket.service.download-drill-plan-template';
import { importDrillPlan } from '@manyun/ticket.service.import-drill-plan';
import type { ImportResult } from '@manyun/ticket.service.import-drill-plan';
import { DrillPlanTable } from '@manyun/ticket.ui.drill-plan-table';
import type { DrillPlan } from '@manyun/ticket.ui.drill-plan-table';

import { DrillPlanMutator } from './components/drill-plan-mutator';

export function DrillConfigMutator({ mode = 'new' }: { mode?: 'new' | 'edit' | 'copy' }) {
  const [createDrillConfig, { loading: createLoading }] = useDrillConfigCreation();
  const [updateDrillConfig, { loading: updateLoading }] = useDrillConfigUpdate();
  const history = useHistory();
  const [form] = Form.useForm();
  const effectType = Form.useWatch('effectType', form);
  const [dataSource, setDataSource] = useState<DrillPlan[]>([]);
  const [importResult, setImportResult] = useState<ImportResult>({
    excelCheckDtos: [],
    correctCheckDtoList: [],
    checkTotal: 0,
    correctTotal: 0,
    faultTotal: 0,
  });
  const [uploadLoading, setUploadLoading] = useState(false);
  const [downloadLoading, setDownloadLoading] = useState(false);
  const importSuccess = (importResult.correctCheckDtoList ?? []).map(({ data }) => data);
  const { id } = useParams<{ id: string }>();
  const [getDrillConfig, { data }] = useLazyDrillConfig();
  const drillConfig = data?.drillConfig?.data;
  const [isOverMaxLength, setIsOverMaxLength] = useState(false);
  const [showAlert, setShowAlert] = useState(true);
  const [
    {
      data: { entities: excLevelEntities },
    },
    { readMetaData: excLevelReadMetaData },
  ] = useMetaData(MetaType.EXC_LEVEL);
  const [
    {
      data: { entities: excMajorEntities },
    },
    { readMetaData: excMajorReadMetaData },
  ] = useMetaData(MetaType.EXC_MAJOR);

  useEffect(() => {
    if (mode !== 'new') {
      excLevelReadMetaData();
      excMajorReadMetaData();
    }
  }, [excLevelReadMetaData, excMajorReadMetaData, mode]);

  useShallowCompareEffect(() => {
    setIsOverMaxLength(dataSource.length >= 100);
  }, [dataSource]);

  const fetchDrillConfig = useCallback(() => {
    if (!id) {
      return;
    }
    getDrillConfig({ variables: { id: Number(id) } });
  }, [getDrillConfig, id]);

  useEffect(() => {
    fetchDrillConfig();
  }, [fetchDrillConfig]);

  useShallowCompareEffect(() => {
    if (drillConfig) {
      const { excName, excLevel, excMajor, effectType, blockGuid, exercisePlanStepList } =
        drillConfig;
      form.setFieldsValue({
        excName,
        excMajor: excMajorEntities[excMajor] ? excMajor : undefined,
        excLevel: excLevelEntities[excLevel] ? excLevel : undefined,
        effectType,
        effectDomain: effectType === ConfigRangeType.All ? ConfigRangeType.All : blockGuid,
      });
      setDataSource(exercisePlanStepList);
    }
  }, [drillConfig, form, excLevelEntities, excMajorEntities]);

  useShallowCompareEffect(() => {
    if (importSuccess.length + dataSource.length > 100) {
      message.error('导入失败，方案步骤请勿超过100条');
      setShowAlert(false);
      return;
    }
    setDataSource(prev => {
      const currentDataSource = prev.slice();
      if (importSuccess.length) {
        importSuccess.forEach(
          ({
            excPhase,
            followMethod,
            roles,
            sla,
            slaUnit,
            stationPosition,
            stepDetail,
            stepFlag,
            stepMeasure,
          }) => {
            const currentPlan = {
              id: nanoid(),
              sla: Number(sla),
              excPhase,
              followMethod,
              roles,
              slaUnit,
              stationPosition,
              stepDetail,
              stepFlag,
              stepMeasure,
            };
            const insertIndex = findLastIndex(
              currentDataSource,
              current => current.excPhase === excPhase
            );
            if (insertIndex === -1) {
              currentDataSource.push({ ...currentPlan, orderNo: currentDataSource.length });
            } else {
              currentDataSource.splice(insertIndex + 1, 0, {
                ...currentPlan,
                orderNo: insertIndex + 1,
              });
            }
          }
        );
      }
      return currentDataSource;
    });
  }, [importSuccess]);

  const sheetData = useMemo(() => {
    return {
      headers: [
        {
          title: '阶段',
          dataIndex: 'excPhase',
        },
        {
          title: '步骤标签',
          dataIndex: 'stepFlag',
        },
        {
          title: '任务详情',
          dataIndex: 'stepDetail',
        },
        {
          title: '现场判断标准',
          dataIndex: 'stepMeasure',
        },
        {
          title: '站位',
          dataIndex: 'stationPosition',
        },
        {
          title: '处理角色',
          dataIndex: 'roles',
        },
        {
          title: '通知跟进方式',
          dataIndex: 'followMethod',
        },
        {
          title: '处理时限',
          dataIndex: 'sla',
        },
        {
          title: 'sla单位',
          dataIndex: 'slaUnit',
          stringify: (slaUnit: TicketSlaUnit) => TicketSlaUnitKeyTextMap[slaUnit],
        },
        {
          title: '失败原因',
          dataIndex: 'totalErrMessage',
        },
      ],
      data: (importResult.excelCheckDtos ?? []).map(({ data, errMessage }) => ({
        ...data,
        totalErrMessage: errMessage ? Object.values(errMessage).join('；') : undefined,
      })),
    };
  }, [importResult.excelCheckDtos]);

  const downloadDemo = useCallback(async () => {
    setDownloadLoading(true);
    const { data, error } = await downloadDrillPlanTemplate();
    setDownloadLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    data && saveAs(data, '方案步骤模板下载.csv');
  }, []);

  return (
    <Space style={{ width: '100%', marginBottom: 24 }} direction="vertical" size={16}>
      <Container style={{ padding: '24px 24px 0px' }}>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title level={5} showBadge>
            基本信息
          </Typography.Title>
          <Form
            form={form}
            labelCol={{ xl: 2 }}
            wrapperCol={{ xl: 22 }}
            initialValues={{
              effectType: ConfigRangeType.Block,
            }}
          >
            <Form.Item label="适用范围" name="effectType" required>
              <Radio.Group options={CONFIG_RANGE_KEY_MAP} />
            </Form.Item>
            {effectType === ConfigRangeType.Block && (
              <Form.Item
                label="属地楼栋"
                name="effectDomain"
                rules={[{ required: true, message: '请选择属地楼栋' }]}
              >
                <LocationTreeSelect
                  style={{ width: 224 }}
                  authorizedOnly
                  disabledTypes={['IDC']}
                  allowClear
                />
              </Form.Item>
            )}
            <Form.Item
              label="专业类型"
              name="excMajor"
              rules={[{ required: true, message: '请选择专业类型' }]}
            >
              <MetaTypeSelect
                style={{ width: 224 }}
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_MAJOR}
                allowClear
              />
            </Form.Item>
            <Form.Item
              label="等级"
              name="excLevel"
              rules={[{ required: true, message: '请选择等级' }]}
            >
              <MetaTypeSelect
                style={{ width: 224 }}
                showSearch
                optionLabelProp="label"
                optionFilterProp="label"
                metaType={MetaType.EXC_LEVEL}
                allowClear
              />
            </Form.Item>
            <Form.Item
              label="演练方案名称"
              name="excName"
              rules={[{ required: true }, { max: 100, message: '最多输入 100 个字符' }]}
            >
              <Input style={{ width: 546 }} allowClear />
            </Form.Item>
          </Form>
        </Space>
      </Container>
      <Container style={{ padding: 24 }}>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title level={5} showBadge>
            方案步骤
          </Typography.Title>
          <Space>
            <DrillPlanMutator
              disabled={isOverMaxLength}
              onSuccess={drillPlan => {
                setDataSource(prev => {
                  const insertIndex = findLastIndex(
                    prev,
                    ({ excPhase }) => excPhase === drillPlan.excPhase
                  );
                  if (insertIndex === -1) {
                    return [...prev, drillPlan];
                  }
                  const current = [...prev];
                  current.splice(insertIndex + 1, 0, drillPlan);

                  return current;
                });
              }}
            />
            <Upload
              showUploadList={false}
              accept=".csv,.xls,.xlsx"
              maxCount={1}
              beforeUpload={async file => {
                const fd = new FormData();
                fd.append('file', file);
                setUploadLoading(true);
                const { data, error } = await importDrillPlan(fd);
                setUploadLoading(false);
                if (error) {
                  message.error(error.message);
                  return;
                }
                setImportResult(data);
                return false;
              }}
            >
              <Button loading={uploadLoading} disabled={isOverMaxLength}>
                导入
              </Button>
            </Upload>
            <Button loading={downloadLoading} onClick={() => downloadDemo()}>
              下载模板
            </Button>
          </Space>
          {showAlert && importResult.checkTotal > 0 && (
            <Alert
              type="info"
              message={
                <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                  <Space>
                    <Typography.Text>导入：{importResult.checkTotal}条</Typography.Text>
                    <Typography.Text>成功：{importResult.correctTotal}条</Typography.Text>
                    <Typography.Text>失败：{importResult.faultTotal}条</Typography.Text>
                  </Space>
                  {typeof importResult.faultTotal === 'number' && importResult.faultTotal > 0 && (
                    <Button
                      type="link"
                      compact
                      onClick={() => {
                        saveJSONAsXLSX(
                          [sheetData],
                          `${dayjs().format('YYYY-MM-DD')}-方案步骤导入失败`
                        );
                      }}
                    >
                      失败文件下载
                    </Button>
                  )}
                </Space>
              }
            />
          )}
          <DrillPlanTable
            dragHandle
            dataSource={dataSource}
            extraColumns={[
              {
                title: '操作',
                dataIndex: 'action',
                width: 104,
                render: (_, record) => (
                  <Space size={16}>
                    <DrillPlanMutator
                      isEdit
                      defaultDrillPlan={record}
                      onSuccess={drillPlan => {
                        setDataSource(prev => {
                          const prevIndex = prev.findIndex(item => item.id === drillPlan.id);
                          const current = [...prev];
                          current.splice(prevIndex, 1, drillPlan);

                          return current;
                        });
                      }}
                    />
                    <DeleteConfirm
                      variant="popconfirm"
                      targetName={record.stepFlag}
                      title="删除该方案步骤？删除后不可恢复，请谨慎操作！"
                      onOk={() => {
                        setDataSource(prev => prev.filter(item => item.id !== record.id));
                        return Promise.resolve(true);
                      }}
                    >
                      <Button type="link" compact>
                        删除
                      </Button>
                    </DeleteConfirm>
                  </Space>
                ),
              },
            ]}
            onDrag={drillPlans => setDataSource(drillPlans)}
          />
        </Space>
      </Container>
      <FooterToolBar>
        <Space>
          <Button
            type="primary"
            loading={mode === 'edit' ? updateLoading : createLoading}
            onClick={() => {
              form.validateFields().then(async values => {
                if (!dataSource.length) {
                  message.error('请至少添加一条方案步骤');
                  return;
                }
                const params = {
                  ...values,
                  exercisePlanStepList: dataSource.map(
                    (
                      {
                        excPhase,
                        stepFlag,
                        stepDetail,
                        stepMeasure,
                        stationPosition,
                        roles,
                        followMethod,
                        sla,
                        slaUnit,
                      },
                      index
                    ) => ({
                      excPhase,
                      stepFlag,
                      stepDetail,
                      stepMeasure,
                      stationPosition,
                      roles,
                      followMethod,
                      sla,
                      slaUnit,
                      orderNo: index + 1,
                    })
                  ),
                  effectDomain:
                    values.effectType === ConfigRangeType.All
                      ? ConfigRangeType.All
                      : values.effectDomain,
                };
                if (mode === 'copy' || mode === 'new') {
                  const { data } = await createDrillConfig({
                    variables: params,
                  });
                  if (data?.createDrillConfig?.message) {
                    message.error(data?.createDrillConfig?.message);
                  } else {
                    message.success(mode === 'new' ? '新建成功' : '复制成功');
                    history.push(DRILL_CONFIG_LIST_ROUTE_PATH);
                  }
                } else {
                  const { data } = await updateDrillConfig({
                    variables: { ...params, id: Number(id) },
                  });
                  if (data?.editDrillConfig?.message) {
                    message.error(data?.editDrillConfig?.message);
                  } else {
                    message.success('编辑成功');
                    history.push(DRILL_CONFIG_LIST_ROUTE_PATH);
                  }
                }
              });
            }}
          >
            提交
          </Button>
          <Button onClick={() => history.push(DRILL_CONFIG_LIST_ROUTE_PATH)}>取消</Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}
