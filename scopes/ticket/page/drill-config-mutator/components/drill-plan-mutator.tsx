import React, { useCallback, useEffect, useState } from 'react';
import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { SLA_UNIT_TYPE_OPTIONS } from '@manyun/ticket.model.task';
import { TicketSlaUnit } from '@manyun/ticket.model.ticket';
import type { DrillPlan } from '@manyun/ticket.ui.drill-plan-table';

export function DrillPlanMutator({
  isEdit,
  defaultDrillPlan,
  disabled,
  onSuccess,
}: {
  isEdit?: boolean;
  defaultDrillPlan?: DrillPlan;
  onSuccess?: (drillPlan: DrillPlan) => void;
} & Pick<ButtonProps, 'disabled'>) {
  const [form] = Form.useForm();
  const [visible, setVisible] = useState(false);

  const handleVisibleChange = useCallback(() => {
    setVisible(prev => !prev);
  }, []);

  useEffect(() => {
    if (isEdit && defaultDrillPlan) {
      form.setFieldsValue({
        ...defaultDrillPlan,
      });
    }
  }, [defaultDrillPlan, form, isEdit]);

  return (
    <>
      <Button
        type={!isEdit ? 'primary' : 'link'}
        compact={isEdit}
        disabled={disabled}
        onClick={() => {
          handleVisibleChange();
        }}
      >
        {isEdit ? '编辑' : '新建步骤'}
      </Button>
      <Modal
        width={582}
        bodyStyle={{ maxHeight: '80vh', overflowY: 'auto' }}
        centered
        title={`${isEdit ? '编辑' : '新建'}步骤`}
        open={visible}
        okText={isEdit ? '保存' : '提交'}
        onCancel={handleVisibleChange}
        onOk={() => {
          form
            .validateFields()
            .then(async values => {
              onSuccess?.({ ...values, id: defaultDrillPlan?.id ?? shortid() });
              handleVisibleChange();
              form.resetFields();
            })
            .catch();
        }}
      >
        <Form
          form={form}
          labelCol={{ xl: 5 }}
          wrapperCol={{ xl: 19 }}
          initialValues={{ slaUnit: TicketSlaUnit.MINUTES }}
        >
          <Form.Item
            label="阶段"
            name="excPhase"
            required
            rules={[{ required: true }, { max: 20, message: '最多输入 20 个字符' }]}
          >
            <Input style={{ width: 224 }} allowClear />
          </Form.Item>
          <Form.Item
            label="步骤标签"
            name="stepFlag"
            rules={[{ required: true }, { max: 20, message: '最多输入 20 个字符' }]}
          >
            <Input style={{ width: 224 }} allowClear />
          </Form.Item>
          <Form.Item
            label="任务详情"
            name="stepDetail"
            rules={[{ required: true }, { max: 500, message: '最多输入 500 个字符' }]}
          >
            <Input.TextArea
              style={{ width: 400 }}
              autoSize={{ minRows: 3, maxRows: 5 }}
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="现场判断标准"
            name="stepMeasure"
            rules={[{ required: true }, { max: 500, message: '最多输入 500 个字符' }]}
          >
            <Input.TextArea
              style={{ width: 400 }}
              autoSize={{ minRows: 3, maxRows: 5 }}
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="站位"
            name="stationPosition"
            rules={[{ required: true }, { max: 20, message: '最多输入 20 个字符' }]}
          >
            <Input style={{ width: 224 }} allowClear />
          </Form.Item>
          <Form.Item
            label="处理角色"
            name="roles"
            rules={[{ required: true }, { max: 20, message: '最多输入 20 个字符' }]}
          >
            <Input style={{ width: 224 }} allowClear />
          </Form.Item>
          <Form.Item
            label="通知跟进方式"
            name="followMethod"
            rules={[{ required: true }, { max: 20, message: '最多输入 20 个字符' }]}
          >
            <Input style={{ width: 224 }} allowClear />
          </Form.Item>
          <Form.Item label="处理时限" required>
            <Space.Compact style={{ height: 54 }}>
              <Form.Item label="" name="sla" rules={[{ required: true, message: '处理时限必填' }]}>
                <InputNumber
                  style={{ width: 120 }}
                  precision={0}
                  min={1}
                  step={1}
                  max={999999999.9}
                />
              </Form.Item>
              <Form.Item
                label=""
                name="slaUnit"
                rules={[{ required: true, message: '处理时限单位必选' }]}
              >
                <Select style={{ width: 104 }} options={SLA_UNIT_TYPE_OPTIONS} />
              </Form.Item>
            </Space.Compact>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
