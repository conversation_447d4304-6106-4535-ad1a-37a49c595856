import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { AccessCardChangeTypeMap } from '@manyun/ticket.model.ticket';

export const ChangeTypeSelect = React.forwardRef(
  ({ ...rest }: SelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <Select ref={ref} {...rest}>
        {Object.entries(AccessCardChangeTypeMap).map(subType => {
          return (
            <Select.Option key={subType[0]} value={subType[0]} label={subType[1]}>
              {subType[1]}
            </Select.Option>
          );
        })}
      </Select>
    );
  }
);
