import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { AccessCardNumberInfo } from '@manyun/ticket.service.fetch-access-card-numbers';

export const AccessCardUserSelect = React.forwardRef(
  (
    { users, ...rest }: SelectProps & { users: AccessCardNumberInfo[] },
    ref?: React.Ref<RefSelectProps>
  ) => {
    return (
      <Select ref={ref} showSearch labelInValue {...rest}>
        {users.map(user => {
          const { applyId, applyName, id, phone, cardType, cardNo, entranceCardNo } = user;
          return (
            <Select.Option
              key={`${cardType}_${cardNo}_${applyId}_${applyName}`}
              value={`${cardType}_${cardNo}_${applyId}_${applyName}`}
              label={`${applyName}_${phone}`}
              data-user={user}
            >
              <Space>
                <Typography.Text> {applyName}</Typography.Text>
                <Typography.Text> {phone}</Typography.Text>
              </Space>
            </Select.Option>
          );
        })}
      </Select>
    );
  }
);

AccessCardUserSelect.displayName = 'AccessCardUserSelect';
