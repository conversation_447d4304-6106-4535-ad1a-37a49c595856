import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { AccessCardNumberInfo } from '@manyun/ticket.service.fetch-access-card-numbers';
import { fetchAccessCardNumbers } from '@manyun/ticket.service.fetch-access-card-numbers';

export type CardNumber = {
  entranceCardNo: string;
  cardNo: string;
  cardType: string;
  applyId: string;
  applyName: string;
  originAuthInfos: { blockGuid: string; authId: number }[];
};
export const AccessCardNumberSelect = React.forwardRef(
  (
    {
      idcTag,
      blockGuid,
      cardNumbers,
      trigger = 'onFocus',
      onFocus,
      ...rest
    }: Omit<SelectProps<string, AccessCardNumberInfo>, 'options'> & {
      idcTag?: string;
      blockGuid?: string;
      changeType?: string;
      cardNumbers?: CardNumber[];
      trigger?: 'onFocus' | 'onDidMount';
    },
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [options, setOptions] = useState<AccessCardNumberInfo[]>([]);

    const getOptions = React.useCallback(async () => {
      if (idcTag || blockGuid) {
        const { data, error } = await fetchAccessCardNumbers({ idcTag, blockGuid });
        if (error) {
          message.error(error.message);
          return;
        }
        setOptions(data.data);
      }
    }, [idcTag, blockGuid]);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        getOptions();
      }
    }, [trigger, getOptions]);

    const filterOptions = () => {
      const numbers = Array.isArray(cardNumbers) ? cardNumbers.map(num => num?.entranceCardNo) : [];
      return options.filter(option => !numbers.includes(option.entranceCardNo));
    };

    return (
      <Select<string, AccessCardNumberInfo>
        ref={ref}
        options={filterOptions()}
        fieldNames={{ label: 'entranceCardNo', value: 'entranceCardNo' }}
        showSearch
        optionFilterProp="entranceCardNo"
        onFocus={evt => {
          if (trigger === 'onFocus' && options.length <= 0) {
            getOptions();
          }
          onFocus?.(evt);
        }}
        {...rest}
      />
    );
  }
);

AccessCardNumberSelect.displayName = 'AccessCardNumberSelect';
