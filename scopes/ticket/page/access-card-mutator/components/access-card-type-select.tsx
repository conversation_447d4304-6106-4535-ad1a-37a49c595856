import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps, CascaderRef } from '@manyun/base-ui.ui.cascader';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export const AccessCardTypeSelect = React.forwardRef(
  (
    { disabledOption, ...rest }: CascaderProps & { disabledOption?: string },
    ref?: React.Ref<CascaderRef>
  ) => {
    const dispatch = useDispatch();

    useEffect(() => {
      dispatch(syncCommonDataAction({ strategy: { accessCardTypes: 'FORCED' } }));
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const accessCardCategory = useSelector(selectAccessCardCategory);

    const treeList = useMemo(() => {
      if (!disabledOption) {
        return accessCardCategory?.treeList;
      }
      return getDisabled(accessCardCategory?.treeList, disabledOption);
    }, [disabledOption, accessCardCategory]);

    return (
      <Cascader
        ref={ref}
        allowClear={false}
        options={treeList}
        fieldNames={{ label: 'metaName', value: 'metaCode', children: 'children' }}
        {...rest}
      />
    );
  }
);

function selectAccessCardCategory({
  common: { accessCardTypes },
}: {
  common: {
    accessCardTypes: {
      treeList: [
        { metaName: string; metaCode: string; children: []; parentCode: string; metaType: string },
      ];
    };
  };
}) {
  return accessCardTypes;
}

function getDisabled(tree, disabledOption) {
  return tree.map(item => {
    if (item.metaType === MetaType.ACCESS_CARD_SECOND_CATEGORY) {
      return {
        ...item,
        disabled: disabledOption === item.metaCode,
      };
    }
    if (item.children && item.children.length) {
      return {
        ...item,
        children: getDisabled(item.children, disabledOption),
      };
    }
    return item;
  });
}
