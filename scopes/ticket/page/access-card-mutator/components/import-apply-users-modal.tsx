import React, { useState } from 'react';

import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import shortid from 'shortid';

import { Button, type ButtonProps } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { Upload } from '@manyun/dc-brain.ui.upload';
import { downloadAccessCardApplyUserTemplate } from '@manyun/ticket.service.download-access-card-apply-user-template';
import {
  type ApplyUser,
  type ApplyUserImport,
  importAccessCardApplyUsers,
} from '@manyun/ticket.service.import-access-card-apply-users';

export type ImportApplyUsersModalProps = {
  onSuccess: (
    params: Array<
      ApplyUser & {
        apply: {
          label: string;
          value: string;
          type: string;
          key: string;
        };
        applyRole: string;
        id: string;
      }
    >
  ) => void;
} & Pick<ButtonProps, 'disabled'>;

export function ImportApplyUsersModal({
  disabled,

  onSuccess,
}: ImportApplyUsersModalProps) {
  const [visible, setVisible] = useState(false);
  const [applyUsersImport, setApplyUsersImport] = useState<ApplyUserImport>();

  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await downloadAccessCardApplyUserTemplate();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '门禁授权申请导入模版.xlsx');
  };
  return (
    <>
      <Button
        type="primary"
        disabled={disabled}
        onClick={() => {
          setVisible(true);
        }}
      >
        导入申请人
      </Button>
      <Modal
        width={1024}
        open={visible}
        title="导入申请人"
        destroyOnClose
        footer={null}
        onCancel={() => {
          setVisible(false);
          setApplyUsersImport(undefined);
        }}
      >
        <Row justify="space-between">
          <Space>
            <Upload
              showUploadList={false}
              accept=".csv,.xls,.xlsx"
              maxCount={1}
              customRequest={async ({ file }) => {
                setImportLoading(true);
                const fd = new FormData();
                fd.append('file', file);
                const { data, error } = await importAccessCardApplyUsers(fd);
                setImportLoading(false);
                setApplyUsersImport(data);
                if (error) {
                  message.error(error.message);
                  return;
                }
                if (data.faultTotal === 0) {
                  message.success('导入成功');
                  setVisible(false);
                  setApplyUsersImport(undefined);

                  onSuccess(
                    data.excelCheckDtos.map(row => {
                      const { data } = row;
                      const { applyName } = data;
                      const id = shortid();
                      return {
                        ...data,
                        apply: {
                          label: applyName,
                          value: applyName,
                          type: 'external',
                          key: `external-${applyName}`,
                        },
                        id,
                        mergeRowsKey: id,
                        isNew: true,
                      };
                    })
                  );
                }
              }}
            >
              <Button loading={importLoading} type="primary">
                导入模版
              </Button>
            </Upload>
            <Button type="link" loading={exportLoading} compact onClick={handleFileExport}>
              <DownloadOutlined />
              下载模版
            </Button>
          </Space>
          {applyUsersImport?.excelCheckDtos && (
            <Typography.Text style={{ marginLeft: 'auto' }}>
              导入失败，{applyUsersImport.faultTotal}条不符合填写规范
            </Typography.Text>
          )}
        </Row>

        <Table
          size="middle"
          scroll={{ x: 'max-content' }}
          loading={importLoading}
          dataSource={applyUsersImport ? applyUsersImport.excelCheckDtos : []}
          columns={[
            {
              title: '申请人',
              dataIndex: ['data', 'applyName'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data.applyName, errMessage, 'applyName'),
            },
            {
              title: '所属部门',
              dataIndex: ['data', 'dept'],
              render: (_, { data, errMessage }) => getToolTilp(data.dept, errMessage, 'dept'),
            },
            {
              title: '所属公司',
              dataIndex: ['data', 'company'],
              render: (_, { data, errMessage }) => getToolTilp(data.company, errMessage, 'company'),
            },
            {
              title: '联系电话',
              dataIndex: ['data', 'phone'],
              render: (_, { data, errMessage }) => getToolTilp(data.phone, errMessage, 'phone'),
            },
            {
              title: '电子邮箱',
              dataIndex: ['data', 'email'],
              render: (_, { data, errMessage }) => getToolTilp(data.email, errMessage, 'email'),
            },
            {
              title: '证件类型',
              dataIndex: ['data', 'cardType'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data.cardType, errMessage, 'cardType'),
            },
            {
              title: '证件编号',
              dataIndex: ['data', 'cardNo'],
              render: (_, { data, errMessage }) => getToolTilp(data.cardNo, errMessage, 'cardNo'),
            },
          ]}
        />
      </Modal>
    </>
  );
}

type ApplyUserImportColumnKey =
  | 'applyName'
  | 'applyRole'
  | 'dept'
  | 'company'
  | 'phone'
  | 'email'
  | 'cardType'
  | 'cardNo';

function getToolTilp(
  value: string,
  errMessage: Record<string, string>,
  dataType: ApplyUserImportColumnKey
) {
  if (Object.keys(errMessage).includes(dataType)) {
    return (
      <Space key={dataType}>
        <Typography.Text ellipsis type="danger">
          {getErrorDisplayContent(dataType, value)}
        </Typography.Text>
        <Tooltip title={errMessage[dataType]}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  }
  return <span key={dataType}> {value ?? '--'}</span>;
}
function getErrorDisplayContent(dataType: ApplyUserImportColumnKey, value: string) {
  const displayErrorContent = value ? '未知' : '--';
  switch (dataType) {
    case 'applyRole':
    case 'cardType':
      return displayErrorContent;
    default:
      return value ?? '--';
  }
}
