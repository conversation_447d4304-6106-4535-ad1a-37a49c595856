import {
  MinusCircleOutlined,
  PlusCircleFilled,
  PlusCircleOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { generateGetRowSpan } from '@galiojs/awesome-antd/lib/table/utils';
import { get } from 'lodash';
import cloneDeep from 'lodash/cloneDeep';
import uniqBy from 'lodash/uniqBy';
import moment from 'moment';
import React, { useEffect, useRef, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import shortid from 'shortid';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import type { CertificateType } from '@manyun/base-ui.ui.certificate-type-select';
import {
  CERTIFICATE_TYPE_TEXT_MAP,
  CertificateTypeSelect,
  isIdCard,
  isPassport,
  isPermit,
} from '@manyun/base-ui.ui.certificate-type-select';
// import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { EditableFormInstance, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { UnboundEntranceGuardCardSelect } from '@manyun/sentry.ui.unbound-entrance-guard-card-select';
import { SlaUnit } from '@manyun/ticket.model.task';
import { AccessCardTaskSubType } from '@manyun/ticket.model.ticket';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';
import type { AccessCardEditRouteParams } from '@manyun/ticket.route.ticket-routes';
import { createAccessCard } from '@manyun/ticket.service.create-access-card';
import { fetchAccessCard } from '@manyun/ticket.service.fetch-access-card';
import { fetchAccessCardNumbers } from '@manyun/ticket.service.fetch-access-card-numbers';
import type { AccessCardNumberInfo } from '@manyun/ticket.service.fetch-access-card-numbers';
import { resendAccessCardNumber } from '@manyun/ticket.service.resend-access-card-number';
import { EntranceGuardCardAuthGroupText } from '@manyun/ticket.ui.entrance-guard-card-auth-group-text';
import { EntranceGuardCardAuthGroupsSelect } from '@manyun/ticket.ui.entrance-guard-card-auth-groups-select';
import { SlaSelect, generateCorrectJobSla } from '@manyun/ticket.ui.sla-select';

import { AccessCardNumberSelect } from './components/access-card-number-select';
import type { CardNumber } from './components/access-card-number-select';
import { AccessCardUserSelect } from './components/access-card-user-select';
import { ImportApplyUsersModal } from './components/import-apply-users-modal';
import { SubTypeSelect } from './components/sub-type-select';

export type DataSourceType = {
  apply: {
    id: number | null;
    label: string;
    value: number | null;
    loginName: string;
  };
  dept?: string;
  company?: string;
  phone?: string;
  email?: string;
  cardNo?: string;
  blockTagList?: string[];
  cardType?: CertificateType;
  id: string;
  blockGuid?: string[];
  authId?: number;
  mergeRowsKey: string;
  entranceCardNo?: string;
  oldEntranceCardNo?: string;
  effectTime?: moment.Moment;
  isNew?: boolean;
  originAuthInfos?: {
    blockGuid: string;
    blockTag: string;
    authIdList: number[];
    effectTime?: number;
  }[];
};

export function AccessCardMutator() {
  const [form] = Form.useForm();
  const { id } = useParams<AccessCardEditRouteParams>();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);
  const [savedRowKeys, setSavedRowKeys] = useState<string[]>([]);
  const [userInfos, setUserInfos] = useState<AccessCardNumberInfo[]>([]);

  const editorFormRef = useRef<EditableFormInstance<DataSourceType>>();

  const history = useHistory();

  useEffect(() => {
    if (id) {
      getDetail();
    } else {
      form.setFieldsValue({ taskSubType: AccessCardTaskSubType.CardApply });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const getDetail = async () => {
    setLoading(true);
    const { data, error } = await fetchAccessCard({ taskNo: id! });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data === null) {
      return;
    }
    form.setFieldsValue({
      ...data,
      applyAuths:
        Array.isArray(data?.applyAuths) && data.applyAuths.length > 0
          ? data.applyAuths.map(item => ({ ...item, blockGuid: [item.blockGuid] }))
          : undefined,
      taskSla: { sla: generateCorrectJobSla(data.sla.value, data.sla.unit), unit: data.sla.unit },
    });
    setSavedRowKeys(data.applyAuths ? data.applyAuths?.map(item => item.id) : []);
    if (data.taskSubType !== AccessCardTaskSubType.CardApply) {
      getUserInfos(data.idcTag);
    }
    if (data.taskSubType === AccessCardTaskSubType.CardChange && data.changeAuthInfo) {
      form.setFieldsValue({
        ...data.changeAuthInfo,
        afterChangeAuthInfos: data.changeAuthInfo
          .map(item => {
            const key = `${item.cardType}_${item.cardNo}_${item.apply.id}_${item.apply.label}`;
            return {
              ...item,
              blockGuid: [item.blockGuid],
              effectTime: item.effectTime ? moment(item.effectTime) : undefined,
              originAuthInfos: get(item, 'originAuthInfos'),
              apply: {
                ...item.apply,
                key,
                value: key,
                _value: item.apply.id ? item.apply.id : item.apply.label,
                _label: item.apply.label,
              },
            };
          })
          .filter(i => i.changeType !== 'DELETE'),
      });
      setSavedRowKeys(data.changeAuthInfo ? data.changeAuthInfo?.map(item => item.id) : []);
    }

    if (data.taskSubType === AccessCardTaskSubType.CardReplace && data.replaceAuthInfos) {
      form.setFieldsValue({
        afterReplaceAuthInfos: data.replaceAuthInfos.map(item => {
          const key = `${item.cardType}_${item.cardNo}_${item.apply.id}_${item.apply.label}`;
          return {
            ...item,
            blockGuid: [item.blockGuid],
            effectTime: item.effectTime ? moment(item.effectTime) : undefined,
            originAuthInfos: get(item, 'originAuthInfos'),
            apply: {
              ...item.apply,
              key,
              value: key,
              _value: item.apply.id ? item.apply.id : item.apply.label,
              _label: item.apply.label,
            },
          };
        }),
      });
      setSavedRowKeys(data.replaceAuthInfos ? data.replaceAuthInfos?.map(item => item.id) : []);
    }
  };

  const submit = () => {
    if (editableKeys?.length) {
      message.error('请先保存门禁授权信息后再提交');
      return;
    }
    form
      .validateFields()
      .then(async values => {
        setSubmitting(true);
        let newId = null;
        let params = {
          ...values,
          sla: { value: values.taskSla.sla, unit: values.taskSla.unit },
          applyAuths:
            Array.isArray(values.applyAuths) && values.applyAuths.length > 0
              ? values.applyAuths.map((item: { blockGuid: string[] }) => ({
                  ...item,
                  blockGuid: getBlockGuidFromArray(item.blockGuid),
                }))
              : undefined,
        };
        if (values.taskSubType === AccessCardTaskSubType.CardApply) {
          const uniqList: any[] = uniqBy(params.applyAuths, 'mergeRowsKey');
          const applyAuthMap: Record<
            string,
            { cardNo: string; cardType: CertificateType; count: number }
          > = uniqList.reduce(
            (
              cur: Record<string, { cardNo: string; cardType: CertificateType; count: number }>,
              item
            ) => {
              const key = `${item.cardNo}_${item.cardType}`;
              if (cur[key]) {
                cur[key].count += 1;
              } else {
                cur[key] = {
                  cardNo: item.cardNo,
                  cardType: item.cardType,
                  count: 1,
                };
              }
              return cur;
            },
            {}
          );
          const dulList = Object.values(applyAuthMap).filter(({ count }) => count > 1);
          if (dulList.length) {
            const errorText = dulList
              .map(
                ({ cardNo, cardType }) =>
                  `${CERTIFICATE_TYPE_TEXT_MAP[cardType]}：${cardNo}存在重复记录`
              )
              .join('\n');
            message.error(<div style={{ whiteSpace: 'pre-line' }}>{errorText}</div>);
            setSubmitting(false);
            return;
          }
        }
        if (values.taskSubType === AccessCardTaskSubType.CardChange) {
          params = {
            ...params,
            changeAuthInfo: values.afterChangeAuthInfos.map(
              (item: {
                blockGuid: string[];
                apply: { value: string; _value: string | number; label: string; _label: string };
              }) => ({
                ...item,
                apply: {
                  ...item.apply,
                  value: item.apply._value,
                  _value: item.apply.value,
                  label: item.apply._label,
                  _label: item.apply.label,
                },
                blockGuid: getBlockGuidFromArray(item.blockGuid),
              })
            ),
          };
          const uniqList: any[] = uniqBy(params.changeAuthInfo, 'mergeRowsKey');
          const applyAuthMap: Record<string, { name: string; count: number }> = uniqList.reduce(
            (cur, item: { apply: { label: string; value: number; key: string } }) => {
              const key = item.apply.key;
              if (cur[key]) {
                cur[key].count += 1;
              } else {
                cur[key] = {
                  name: item.apply.label,
                  count: 1,
                };
              }
              return cur;
            },
            {}
          );
          const dulList = Object.values(applyAuthMap).filter(({ count }) => count > 1);
          if (dulList.length) {
            const errorText = dulList.map(({ name }) => `${name}存在重复记录`).join('\n');
            message.error(<div style={{ whiteSpace: 'pre-line' }}>{errorText}</div>);
            setSubmitting(false);
            return;
          }
        }
        if (values.taskSubType === AccessCardTaskSubType.CardReplace) {
          params = {
            ...params,

            replaceAuthInfos: values.afterReplaceAuthInfos.map(
              (item: {
                blockGuid: string[];
                apply: { value: string; _value: string | number; label: string; _label: string };
              }) => ({
                ...item,
                apply: {
                  ...item.apply,
                  value: item.apply._value,
                  _value: item.apply.value,
                  label: item.apply._label,
                  _label: item.apply.label,
                },
                blockGuid: getBlockGuidFromArray(item.blockGuid),
              })
            ),
          };
          const uniqList: any[] = uniqBy(params.replaceAuthInfos, 'mergeRowsKey');
          const uniqNoList = uniqBy(uniqList, 'entranceCardNo');
          if (uniqList.length !== uniqNoList.length) {
            message.error('新门禁卡编号不能重复');
            setSubmitting(false);
            return;
          }
          const applyAuthMap: Record<string, { name: string; count: number }> = uniqList.reduce(
            (cur, item: { apply: { label: string; value: number; key: string } }) => {
              const key = item.apply.key;
              if (cur[key]) {
                cur[key].count += 1;
              } else {
                cur[key] = {
                  name: item.apply.label,
                  count: 1,
                };
              }
              return cur;
            },
            {}
          );
          const dulList = Object.values(applyAuthMap).filter(({ count }) => count > 1);
          if (dulList.length) {
            const errorText = dulList.map(({ name }) => `${name}存在重复记录`).join('\n');
            message.error(<div style={{ whiteSpace: 'pre-line' }}>{errorText}</div>);
            setSubmitting(false);
            return;
          }
        }
        if (id) {
          const { data, error } = await resendAccessCardNumber({ ...params, taskNo: id });
          setSubmitting(false);

          if (error) {
            message.error(error.message);
            return;
          }
          newId = data;
        } else {
          const { data, error } = await createAccessCard(params);
          setSubmitting(false);
          if (error) {
            message.error(error.message);
            return;
          }
          newId = data;
        }
        message.success('提交成功');
        newId &&
          history.push(generateTicketLocation({ ticketType: 'access_card_auth', id: newId }));
      })
      .catch();
  };

  const onDeleteUser = (record: DataSourceType) => {
    form.setFieldsValue({
      applyAuths: form
        .getFieldValue('applyAuths')
        .filter((user: DataSourceType) => record.id !== user.id),
    });
  };

  const onDeleteChangeUser = (record: DataSourceType) => {
    form.setFieldsValue({
      afterChangeAuthInfos: form
        .getFieldValue('afterChangeAuthInfos')
        .filter((user: DataSourceType) => record.id !== user.id),
    });
  };

  const onDeleteReplaceUser = (record: DataSourceType) => {
    form.setFieldsValue({
      afterReplaceAuthInfos: form
        .getFieldValue('afterReplaceAuthInfos')
        .filter((user: DataSourceType) => record.id !== user.id),
    });
  };

  const getUserInfos = React.useCallback(async (idcTag: string) => {
    if (idcTag) {
      const { data, error } = await fetchAccessCardNumbers({ idcTag });
      if (error) {
        message.error(error.message);
        return;
      }
      setUserInfos(data.data ?? []);
    }
  }, []);

  if (loading) {
    return null;
  }

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Form
        labelCol={{ flex: '96px' }}
        wrapperCol={{ span: 6 }}
        form={form}
        style={{ marginBottom: 24 }}
        autoComplete="off"
        initialValues={{ taskSla: { sla: 72, unit: SlaUnit.Hour } }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Card
            title={
              <Typography.Title level={5} showBadge>
                基本信息
              </Typography.Title>
            }
          >
            <Form.Item
              label="工单子类型"
              name="taskSubType"
              rules={[
                {
                  required: true,
                  message: '工单子类型必选！',
                },
              ]}
              shouldUpdate
            >
              <SubTypeSelect
                style={{ width: 216 }}
                disabled={!!id}
                onChange={value => {
                  form.resetFields();
                  form.setFieldsValue({ taskSla: { sla: 72, unit: SlaUnit.Hour } });
                  if (value === AccessCardTaskSubType.CardChange) {
                    form.setFieldsValue({
                      taskSubType: value,
                    });
                  } else {
                    form.setFieldsValue({
                      deleteAuthInfos: [{ entranceCardNo: undefined, applyName: undefined }],
                      taskSubType: value,
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              shouldUpdate={(pre, next) =>
                pre.taskSubType !== next.taskSubType ||
                getBlockGuidFromArray(pre.blockGuid) !== getBlockGuidFromArray(next.blockGuid) ||
                pre.deleteAuthInfos !== next.deleteAuthInfos
              }
              noStyle
            >
              {({ getFieldsValue, setFieldsValue }) => {
                const { taskSubType, deleteAuthInfos } = getFieldsValue();
                if (taskSubType === AccessCardTaskSubType.CardOff) {
                  return (
                    <Form.Item noStyle>
                      <Form.Item
                        label="门禁卡归属"
                        name="blockGuid"
                        extra="请选择需注销的门禁卡归属地"
                        rules={[
                          {
                            required: true,
                            message: '门禁卡归属必选！',
                          },
                        ]}
                        shouldUpdate={(pre, next) =>
                          getBlockGuidFromArray(pre.blockGuid) !==
                          getBlockGuidFromArray(next.blockGuid)
                        }
                      >
                        <LocationTreeSelect
                          style={{ width: 216 }}
                          disabledTypes={['IDC']}
                          authorizedOnly
                          showSearch
                          allowClear
                          onChange={() => {
                            setFieldsValue({
                              deleteAuthInfos: [
                                { entranceCardNo: undefined, applyName: undefined },
                              ],
                            });
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        required
                        label="门禁卡编号"
                        style={{ marginBottom: 0 }}
                        wrapperCol={{ span: 24 }}
                        shouldUpdate={(pre, next) =>
                          pre.deleteAuthInfos !== next.deleteAuthInfos ||
                          getBlockGuidFromArray(pre.blockGuid) !==
                            getBlockGuidFromArray(next.blockGuid)
                        }
                      >
                        {({ getFieldValue, setFieldsValue }) => {
                          const blockGuid = getFieldValue('blockGuid');
                          return (
                            <Form.List name="deleteAuthInfos">
                              {(fields, { add, remove }) => (
                                <>
                                  {fields.map(({ key, name, ...respField }, index) => (
                                    <Space key={key} style={{ display: 'flex' }} align="baseline">
                                      <Form.Item
                                        {...respField}
                                        name={[name, 'entranceCardNo']}
                                        rules={[{ required: true, message: '门禁卡编号必选' }]}
                                      >
                                        <AccessCardNumberSelect
                                          style={{ width: 216 }}
                                          disabled={!blockGuid}
                                          blockGuid={blockGuid}
                                          cardNumbers={deleteAuthInfos}
                                          trigger="onDidMount"
                                          onChange={(value, option) => {
                                            if (!Array.isArray(option)) {
                                              let newNums: CardNumber[] = deleteAuthInfos || [];
                                              if (
                                                deleteAuthInfos &&
                                                newNums.some(
                                                  item =>
                                                    item?.entranceCardNo ===
                                                    deleteAuthInfos[name]?.entranceCardNo
                                                )
                                              ) {
                                                newNums = newNums.map(num => {
                                                  if (
                                                    num?.entranceCardNo ===
                                                    deleteAuthInfos[name]?.entranceCardNo
                                                  ) {
                                                    return {
                                                      entranceCardNo: value,
                                                      cardNo: option.cardNo,
                                                      cardType: option.cardType,
                                                      originAuthInfos: option.authInfoList.map(
                                                        item => ({
                                                          blockGuid: blockGuid,
                                                          authId: item.authIdList[0],
                                                        })
                                                      ),
                                                      applyId: option.applyId,
                                                      applyName: option.applyName,
                                                    };
                                                  }
                                                  return num;
                                                });
                                              } else {
                                                newNums.splice(
                                                  deleteAuthInfos.length - 2,
                                                  deleteAuthInfos.length - 1,
                                                  {
                                                    entranceCardNo: value,
                                                    cardNo: option.cardNo,
                                                    cardType: option.cardType,
                                                    originAuthInfos: option.authInfoList.map(
                                                      item => ({
                                                        blockGuid: blockGuid,
                                                        authId: item.authIdList[0],
                                                      })
                                                    ),
                                                    applyId: option.applyId,
                                                    applyName: option.applyName,
                                                  }
                                                );
                                              }
                                              setFieldsValue({ deleteAuthInfos: newNums });
                                            }
                                          }}
                                        />
                                      </Form.Item>
                                      <Form.Item
                                        {...respField}
                                        label="持卡人"
                                        required={false}
                                        name={[name, 'applyName']}
                                        rules={[{ required: true, message: '持卡人必选' }]}
                                      >
                                        <Select disabled style={{ width: 216 }} options={[]} />
                                      </Form.Item>
                                      <PlusCircleOutlined onClick={() => add()} />
                                      {index !== 0 && (
                                        <MinusCircleOutlined onClick={() => remove(name)} />
                                      )}
                                    </Space>
                                  ))}
                                </>
                              )}
                            </Form.List>
                          );
                        }}
                      </Form.Item>
                    </Form.Item>
                  );
                }
                return null;
              }}
            </Form.Item>
            <Form.Item shouldUpdate={(pre, next) => pre.taskSubType !== next.taskSubType} noStyle>
              {({ getFieldValue, setFieldsValue }) => {
                if (
                  [
                    AccessCardTaskSubType.CardApply,
                    AccessCardTaskSubType.CardChange,
                    AccessCardTaskSubType.CardReplace,
                  ].includes(getFieldValue('taskSubType'))
                ) {
                  return (
                    <Form.Item required label="授权机房">
                      <Space>
                        <Form.Item
                          name="idcTag"
                          required
                          rules={[
                            {
                              required: true,
                              message: '授权机房必选！',
                            },
                          ]}
                          noStyle
                          shouldUpdate
                        >
                          <LocationTreeSelect
                            style={{ width: 216 }}
                            nodeTypes={['IDC']}
                            authorizedOnly
                            showSearch
                            allowClear
                            onChange={value => {
                              if (typeof value === 'string') {
                                getUserInfos(value);
                              }
                              setFieldsValue({
                                entranceCardNo: undefined,
                                apply: undefined,
                                beforeAuth: undefined,
                                afterAuth: undefined,
                                accessCardTypeChange: undefined,
                                applyAuths: [],
                                afterChangeAuthInfos: [],
                                afterReplaceAuthInfos: [],
                              });
                              setEditableRowKeys([]);
                            }}
                          />
                        </Form.Item>
                        <Explanation
                          iconType="question"
                          tooltip={{
                            title: '授权机房与平台数据权限相关，请联系权限管理员关联授权机房资源',
                          }}
                        />
                      </Space>
                    </Form.Item>
                  );
                }
                return null;
              }}
            </Form.Item>
            <Form.Item
              label="说明"
              name="description"
              rules={[
                {
                  type: 'string',
                  max: 100,
                  message: '说明最多可输入100位字符！',
                },
              ]}
            >
              <Input.TextArea rows={2} allowClear style={{ width: 395 }} />
            </Form.Item>
            <Form.Item label="SLA" name="taskSla">
              <SlaSelect />
            </Form.Item>
            <Form.Item
              label="附件"
              name="fileInfoList"
              valuePropName="fileList"
              wrapperCol={{ span: 8 }}
              getValueFromEvent={({ fileList }) => {
                if (
                  fileList.filter((file: MixedUploadFile) => file.status === 'uploading').length
                ) {
                  setSubmitting(true);
                } else {
                  setSubmitting(false);
                }
                return fileList;
              }}
            >
              <McUpload
                showAccept
                accept="image/*,.xls,.xlsx,.pdf"
                showUploadList
                allowDelete
                maxCount={20}
              >
                <Button icon={<UploadOutlined />}>点此上传</Button>
              </McUpload>
            </Form.Item>
          </Card>
          <Form.Item
            shouldUpdate={(pre, next) =>
              pre.taskSubType !== next.taskSubType ||
              pre.applyAuths !== next.applyAuths ||
              pre.idcTag !== next.idcTag
            }
            noStyle
          >
            {({ getFieldsValue, setFieldsValue, setFieldValue }) => {
              const { taskSubType, applyAuths, idcTag } = getFieldsValue();
              if (taskSubType !== AccessCardTaskSubType.CardOff) {
                return (
                  <Card
                    title={
                      <Typography.Title level={5} showBadge>
                        {taskSubType === AccessCardTaskSubType.CardChange
                          ? '门禁变更'
                          : taskSubType === AccessCardTaskSubType.CardReplace
                            ? '门禁换卡'
                            : '门禁授权'}
                      </Typography.Title>
                    }
                  >
                    {taskSubType === AccessCardTaskSubType.CardApply && (
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <ImportApplyUsersModal
                          disabled={!idcTag}
                          onSuccess={value => {
                            setFieldsValue({
                              applyAuths: [...applyAuths, ...value],
                            });
                            setEditableRowKeys([...editableKeys, ...value.map(item => item.id)]);
                          }}
                        />

                        <Form.Item
                          name="applyAuths"
                          wrapperCol={{ span: 24 }}
                          rules={[
                            {
                              validator: async (_, users) => {
                                if (!users?.length) {
                                  // EditableProTable 的 locale已经有提示信息
                                  return Promise.reject();
                                }
                                return Promise.resolve();
                              },
                            },
                          ]}
                          noStyle
                        >
                          <EditableProTable
                            rowKey="id"
                            ghost
                            scroll={{ x: 'max-content' }}
                            editableFormRef={editorFormRef}
                            columns={getColumns({
                              idcTag: idcTag,
                              editorFormRef,
                              applyAuths: applyAuths,
                              editableKeys,
                              setRow: setFieldValue,
                              setEditableRowKeys,
                              onDeleteUser,
                            })}
                            recordCreatorProps={
                              idcTag
                                ? {
                                    position: 'top',
                                    newRecordType: 'dataSource',
                                    creatorButtonText: '添加授权',
                                    record: () => {
                                      const newId = shortid();
                                      return {
                                        id: newId,
                                        mergeRowsKey: newId,
                                        apply: {
                                          label: '',
                                          value: null,
                                          id: null,
                                          loginName: '',
                                        },
                                        dept: undefined,
                                        company: undefined,
                                        phone: undefined,
                                        email: undefined,
                                        cardType: undefined,
                                        cardNo: undefined,
                                        blockGuid: undefined,
                                        isNew: true,
                                      };
                                    },
                                  }
                                : false
                            }
                            editable={{
                              form: form,
                              type: 'multiple',
                              editableKeys,
                              onChange: setEditableRowKeys,
                              onSave: (_, __, row) => {
                                setSavedRowKeys([...savedRowKeys, row.id]);
                                return Promise.resolve();
                              },
                              onCancel: (_, __, originRow) => {
                                let newData = [...applyAuths];
                                if (!savedRowKeys.includes(originRow.id)) {
                                  newData = newData.filter(d => d.id !== originRow.id);
                                } else {
                                  let currentData = newData.find(d => d.id === originRow.id);
                                  if (currentData) {
                                    currentData = { ...originRow };
                                  }
                                }
                                setFieldValue('applyAuths', newData);
                                return Promise.resolve();
                              },
                              actionRender: (row, config, dom) => [dom.save, dom.cancel],
                            }}
                            locale={{
                              emptyText: (
                                <Typography.Text type="danger">
                                  {idcTag ? '请添加申请人' : '请先添加授权机房'}
                                </Typography.Text>
                              ),
                            }}
                          />
                        </Form.Item>
                      </Space>
                    )}
                    {isAuthTypeChange(taskSubType) && (
                      <Form.Item
                        wrapperCol={{ span: 24 }}
                        shouldUpdate={(prevValues, curValues) =>
                          prevValues.taskSubType !== curValues.taskSubType ||
                          prevValues.idcTag !== curValues.idcTag ||
                          prevValues.afterChangeAuthInfos !== curValues.afterChangeAuthInfos
                        }
                      >
                        {({ getFieldsValue }) => {
                          const { taskSubType, idcTag, afterChangeAuthInfos } = getFieldsValue();

                          return (
                            <Form.Item
                              name="afterChangeAuthInfos"
                              wrapperCol={{ span: 24 }}
                              rules={[
                                {
                                  validator: async (_, users) => {
                                    if (!users?.length) {
                                      // EditableProTable 的 locale已经有提示信息
                                      return Promise.reject();
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                              noStyle
                            >
                              <EditableProTable
                                rowKey="id"
                                ghost
                                scroll={{ x: 'max-content' }}
                                editableFormRef={editorFormRef}
                                columns={getColumns({
                                  idcTag: idcTag,
                                  editorFormRef,
                                  applyAuths: afterChangeAuthInfos,
                                  editableKeys,
                                  setRow: setFieldValue,
                                  setEditableRowKeys,
                                  onDeleteUser: onDeleteChangeUser,
                                  taskSubType,
                                  userInfos,
                                })}
                                recordCreatorProps={
                                  idcTag
                                    ? {
                                        position: 'top',
                                        newRecordType: 'dataSource',
                                        creatorButtonText: '添加授权',
                                        record: () => {
                                          const newId = shortid();
                                          return {
                                            id: newId,
                                            mergeRowsKey: newId,
                                            apply: {
                                              label: '',
                                              value: null,
                                              id: null,
                                              loginName: '',
                                            },
                                            entranceCardNo: undefined,
                                            blockGuid: undefined,
                                            authId: undefined,
                                            effectTime: undefined,
                                            isNew: true,
                                          };
                                        },
                                      }
                                    : false
                                }
                                editable={{
                                  form: form,
                                  type: 'multiple',
                                  editableKeys,
                                  onChange: setEditableRowKeys,
                                  onSave: (_, __, row) => {
                                    setSavedRowKeys([...savedRowKeys, row.id]);
                                    return Promise.resolve();
                                  },
                                  onCancel: (_, __, originRow) => {
                                    let newData = [...afterChangeAuthInfos];
                                    if (!savedRowKeys.includes(originRow.id)) {
                                      newData = newData.filter(d => d.id !== originRow.id);
                                    } else {
                                      let currentData = newData.find(d => d.id === originRow.id);
                                      if (currentData) {
                                        currentData = { ...originRow };
                                      }
                                    }
                                    setFieldValue('afterChangeAuthInfos', newData);
                                    return Promise.resolve();
                                  },
                                  actionRender: (row, config, dom) => [dom.save, dom.cancel],
                                }}
                                locale={{
                                  emptyText: (
                                    <Typography.Text type="danger">
                                      {idcTag ? '请添加持卡人' : '请先添加授权机房'}
                                    </Typography.Text>
                                  ),
                                }}
                              />
                            </Form.Item>
                          );
                        }}
                      </Form.Item>
                    )}
                    <Form.Item
                      wrapperCol={{ span: 24 }}
                      shouldUpdate={(prevValues, curValues) =>
                        prevValues.taskSubType !== curValues.taskSubType ||
                        prevValues.idcTag !== curValues.idcTag ||
                        prevValues.afterReplaceAuthInfos !== curValues.afterReplaceAuthInfos
                      }
                    >
                      {({ getFieldsValue }) => {
                        const { taskSubType, idcTag, afterReplaceAuthInfos } = getFieldsValue();
                        if (taskSubType === AccessCardTaskSubType.CardReplace) {
                          return (
                            <Form.Item
                              name="afterReplaceAuthInfos"
                              wrapperCol={{ span: 24 }}
                              rules={[
                                {
                                  validator: async (_, users) => {
                                    if (!users?.length) {
                                      // EditableProTable 的 locale已经有提示信息
                                      return Promise.reject();
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                              noStyle
                            >
                              <EditableProTable
                                rowKey="id"
                                ghost
                                scroll={{ x: 'max-content' }}
                                editableFormRef={editorFormRef}
                                columns={getColumns({
                                  idcTag: idcTag,
                                  editorFormRef,
                                  applyAuths: afterReplaceAuthInfos,
                                  editableKeys,
                                  setRow: setFieldValue,
                                  setEditableRowKeys,
                                  onDeleteUser: onDeleteReplaceUser,
                                  taskSubType,
                                  userInfos,
                                })}
                                recordCreatorProps={
                                  idcTag
                                    ? {
                                        position: 'top',
                                        newRecordType: 'dataSource',
                                        creatorButtonText: '添加授权',
                                        record: () => {
                                          const newId = shortid();
                                          return {
                                            id: newId,
                                            mergeRowsKey: newId,
                                            apply: {
                                              label: '',
                                              value: null,
                                              id: null,
                                              loginName: '',
                                            },
                                            entranceCardNo: undefined,
                                            blockGuid: undefined,
                                            authId: undefined,
                                            effectTime: undefined,
                                            isNew: true,
                                          };
                                        },
                                      }
                                    : false
                                }
                                editable={{
                                  form: form,
                                  type: 'multiple',
                                  editableKeys,
                                  onChange: setEditableRowKeys,
                                  onSave: (_, __, row) => {
                                    setSavedRowKeys([...savedRowKeys, row.id]);
                                    return Promise.resolve();
                                  },
                                  onCancel: (_, __, originRow) => {
                                    let newData = [...afterReplaceAuthInfos];
                                    if (!savedRowKeys.includes(originRow.id)) {
                                      newData = newData.filter(d => d.id !== originRow.id);
                                    } else {
                                      let currentData = newData.find(d => d.id === originRow.id);
                                      if (currentData) {
                                        currentData = { ...originRow };
                                      }
                                    }
                                    setFieldValue('afterReplaceAuthInfos', newData);
                                    return Promise.resolve();
                                  },
                                  actionRender: (row, config, dom) => [dom.save, dom.cancel],
                                }}
                                locale={{
                                  emptyText: (
                                    <Typography.Text type="danger">
                                      {idcTag ? '请添加持卡人' : '请先添加授权机房'}
                                    </Typography.Text>
                                  ),
                                }}
                              />
                            </Form.Item>
                          );
                        }
                        return null;
                      }}
                    </Form.Item>
                  </Card>
                );
              }
              return null;
            }}
          </Form.Item>
        </Space>
      </Form>
      <FooterToolBar>
        <Space style={{ width: '100%', justifyContent: 'center' }}>
          <Button type="primary" loading={submitting} onClick={submit}>
            提交
          </Button>
          <Button
            disabled={submitting}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}

const isAuthTypeChange = (taskSubType: AccessCardTaskSubType) =>
  taskSubType === AccessCardTaskSubType.CardChange;

const isSystemUser = (id: number | null | undefined) => !!id;

const getDefaultUserInfo = (value: string, userInfos?: AccessCardNumberInfo[]) => {
  return userInfos?.find(info => {
    const [cardType, cardNo, applyId, applyName] = value?.split('_');
    return (
      (info.applyId === applyId || info.applyName === applyName) &&
      info.cardType === cardType &&
      info.cardNo === cardNo
    );
  });
};

const getColumns: ({
  idcTag,
  editorFormRef,
  applyAuths,
  editableKeys,
  setEditableRowKeys,
  setRow,
  onDeleteUser,
  taskSubType,
}: {
  idcTag: string;
  editorFormRef: React.MutableRefObject<EditableFormInstance<DataSourceType> | undefined>;
  applyAuths: DataSourceType[];
  editableKeys: React.Key[];
  setEditableRowKeys: (key: React.Key[]) => void;
  onDeleteUser: (record: DataSourceType) => void;
  setRow: (name: string, value: unknown) => void;
  taskSubType?: string;
  userInfos?: AccessCardNumberInfo[];
}) => ProColumns<DataSourceType>[] = ({
  idcTag,
  editorFormRef,
  applyAuths,
  editableKeys,
  setEditableRowKeys,
  setRow,
  onDeleteUser,
  taskSubType,
  userInfos,
}) => {
  const auths = applyAuths ?? [];
  if (taskSubType === AccessCardTaskSubType.CardChange) {
    return [
      {
        title: '持卡人',
        dataIndex: 'apply',
        onCell: (record, index) => {
          return {
            rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
          };
        },
        formItemProps: () => {
          return {
            rules: [
              {
                validator: (_, value) =>
                  value?.value ? Promise.resolve() : Promise.reject('持卡人必选!'),
              },
            ],
          };
        },
        renderFormItem: (_, { recordKey, record }) => {
          return (
            <AccessCardUserSelect
              style={{ width: '200px' }}
              users={userInfos ?? []}
              onChange={value => {
                if (recordKey) {
                  const [_cardType, _cardNo, _applyId, _applyName] = (value.key as string).split(
                    '_'
                  );
                  const id = _applyId !== 'null' ? +_applyId : null;
                  // const _label = `${_applyName} ${_phone}`;
                  const _value = _applyId !== 'null' ? +_applyId : _applyName;
                  const info = getDefaultUserInfo(value.value, userInfos);
                  const authInfoList = info?.authInfoList ?? [];
                  const defaultInfo = {
                    mergeRowsKey: record?.mergeRowsKey,
                    entranceCardNo: info?.entranceCardNo,
                    apply: {
                      label: _applyName,
                      value: value.value,
                      id: id,
                      loginName: value.login,
                      _label: _applyName,
                      _value: _value,
                    },
                    cardNo: info?.cardNo,
                    cardType: info?.cardType as CertificateType,
                    originAuthInfos: info?.authInfoList,
                  };
                  const _users = cloneDeep(applyAuths);
                  const insertIdx = _users.findIndex(
                    item => item.mergeRowsKey === record?.mergeRowsKey
                  );
                  const insertUser = _users.find(
                    item => item.mergeRowsKey === record?.mergeRowsKey
                  );
                  let deleteNum = 0;
                  if (insertUser?.entranceCardNo) {
                    deleteNum = [..._users].filter(
                      item => item.mergeRowsKey === record?.mergeRowsKey
                    ).length;
                    deleteNum > 0 && _users.splice(insertIdx + 1, deleteNum - 1);
                  }
                  const currentEditableRowKeys = [...editableKeys];
                  authInfoList.forEach(({ blockGuid, authIdList, effectTime }, index) => {
                    const newId = shortid();
                    let newMainItem = {
                      ...defaultInfo,
                      id: index === 0 ? record?.id : newId,
                      blockGuid: blockGuid ? [blockGuid] : undefined,
                      authId: authIdList?.[0],
                      effectTime: effectTime
                        ? moment(effectTime.includes('T') ? effectTime.split('T')[0] : effectTime)
                        : undefined,
                    } as DataSourceType;
                    if (index === 0) {
                      editorFormRef.current?.setRowData?.(recordKey.toString(), newMainItem);
                      _users.splice(insertIdx, 1, newMainItem);
                    } else {
                      currentEditableRowKeys.push(newId);
                      newMainItem = { ...newMainItem, isNew: true };
                      _users.splice(insertIdx, 0, newMainItem);
                      setRow('afterChangeAuthInfos', _users);
                      editorFormRef.current?.setRowData?.(newId, newMainItem);
                    }
                  });
                  setEditableRowKeys(currentEditableRowKeys);
                }
              }}
            />
          );
        },
        render: (_, record) => {
          return record.apply.label;
        },
      },
      {
        title: '门禁卡编号',
        dataIndex: 'entranceCardNo',
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '门禁卡编号必选！' }],
          };
        },
        renderFormItem: () => {
          return (
            <AccessCardNumberSelect trigger="onDidMount" showSearch idcTag={idcTag} disabled />
          );
        },
        onCell: (record, index) => {
          return {
            rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
          };
        },
      },
      {
        title: '授权楼栋',
        dataIndex: 'blockGuid',
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '授权楼栋必选！' }],
          };
        },
        renderFormItem: (_, { recordKey, record }) => {
          const disabledSpaceCodes: string[] = [];
          auths.forEach(auth => {
            if (auth.mergeRowsKey === record?.mergeRowsKey) {
              auth.blockGuid && disabledSpaceCodes.push(getBlockGuidFromArray(auth.blockGuid)!);
            }
          });
          const isApplyUserInSystem = record?.apply?.id;
          return (
            <LocationCascader
              style={{
                minWidth: 118,
              }}
              idc={idcTag}
              authorizedOnly={Boolean(isApplyUserInSystem)}
              userName={isApplyUserInSystem ? record.apply.loginName : undefined}
              disabled={!record?.apply?.value}
              nodeTypes={['BLOCK']}
              disabledSpaceCodes={disabledSpaceCodes}
              onChange={(value, _, selectedValues) => {
                if (recordKey) {
                  editorFormRef.current?.setRowData?.(recordKey.toString(), {
                    authId: undefined,
                  });
                }
              }}
            />
          );
        },
        render: (_, record, index) => {
          const { mergeRowsKey, apply, entranceCardNo, originAuthInfos, cardNo, cardType } = record;
          const rowSpan = generateGetRowSpan(auths)(record, index ?? 1);
          if (!record.blockGuid) {
            return '--';
          }
          if (rowSpan >= 1) {
            return (
              <span>
                <PlusCircleFilled
                  style={{ marginRight: 8, color: 'var(--manyun-info-color)' }}
                  onClick={() => {
                    const newId = shortid();
                    const newMainItem = {
                      id: newId,
                      mergeRowsKey,
                      apply,
                      entranceCardNo,
                      blockGuid: undefined,
                      authId: undefined,
                      effectTime: undefined,
                      originAuthInfos,
                      cardNo,
                      cardType,
                      isNew: true,
                    };
                    const _users = cloneDeep(applyAuths);
                    const insertIdx = _users.findIndex(
                      item => item.mergeRowsKey === newMainItem.mergeRowsKey
                    );
                    _users.splice(insertIdx, 0, newMainItem);
                    setRow('afterChangeAuthInfos', _users);
                    setEditableRowKeys([newId, ...editableKeys]);
                  }}
                />
                <SpaceText guid={getBlockGuidFromArray(record.blockGuid) ?? ''} />
              </span>
            );
          } else {
            return <SpaceText guid={getBlockGuidFromArray(record.blockGuid) ?? ''} />;
          }
        },
      },
      {
        title: '门禁权限组角色',
        dataIndex: 'authId',
        width: 200,
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '门禁权限组角色必选！' }],
          };
        },
        renderFormItem: (_, { record }) => {
          return (
            <EntranceGuardCardAuthGroupsSelect
              style={{ width: '100%' }}
              trigger="onDidMount"
              disabled={!record?.blockGuid}
              blockGuid={getBlockGuidFromArray(record?.blockGuid)}
            />
          );
        },
        render: (_, record) =>
          record.authId && record.blockGuid ? (
            <EntranceGuardCardAuthGroupText
              code={record.authId}
              blockGuid={getBlockGuidFromArray(record.blockGuid) ?? ''}
            />
          ) : (
            '--'
          ),
      },
      {
        title: '门禁卡有效期限',
        dataIndex: 'effectTime',
        valueType: 'date',
        width: 200,
        formItemProps: {
          rules: [{ required: true, message: '门禁卡有效期限必选!' }],
        },
        // renderFormItem: () => {
        //   return <DatePicker allowClear showTime={false} format="YYYY-MM-DD 23:59:59" />;
        // },
        render: (_, record) =>
          record.effectTime
            ? moment(record.effectTime).endOf('d').format('YYYY-MM-DD HH:mm:ss')
            : '--',
      },
      {
        title: '操作',
        valueType: 'option',
        fixed: 'right',
        width: 88,
        render: (text, record, _, action) => [
          <Button
            key={record.id}
            type="link"
            compact
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </Button>,
          <Button
            key={`${record.id}${text}`}
            type="link"
            compact
            onClick={() => {
              onDeleteUser(record);
            }}
          >
            删除
          </Button>,
        ],
      },
    ];
  }
  if (taskSubType === AccessCardTaskSubType.CardReplace) {
    return [
      {
        title: '持卡人',
        dataIndex: 'apply',
        onCell: (record, index) => {
          return {
            rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
          };
        },
        formItemProps: () => {
          return {
            rules: [
              {
                validator: (_, value) =>
                  value?.value ? Promise.resolve() : Promise.reject('持卡人必选!'),
              },
            ],
          };
        },
        renderFormItem: (_, { recordKey, record }) => {
          return (
            <AccessCardUserSelect
              style={{ width: '200px' }}
              users={userInfos ?? []}
              onChange={value => {
                if (recordKey) {
                  const [_cardType, _cardNo, _applyId, _applyName] = (value.key as string).split(
                    '_'
                  );
                  const id = _applyId !== 'null' ? +_applyId : null;
                  // const _label = `${_applyName} ${_phone}`;
                  const _value = _applyId !== 'null' ? +_applyId : _applyName;
                  const info = getDefaultUserInfo(value.value, userInfos);
                  const authInfoList = info?.authInfoList ?? [];
                  const defaultInfo = {
                    mergeRowsKey: record?.mergeRowsKey,
                    oldEntranceCardNo: info?.entranceCardNo,
                    entranceCardNo: undefined,
                    apply: {
                      label: _applyName,
                      value: value.value,
                      id: id,
                      loginName: value.login,
                      _label: _applyName,
                      _value: _value,
                    },
                    cardNo: info?.cardNo,
                    cardType: info?.cardType as CertificateType,
                    originAuthInfos: info?.authInfoList,
                  };
                  const _users = cloneDeep(applyAuths);
                  const insertIdx = _users.findIndex(
                    item => item.mergeRowsKey === record?.mergeRowsKey
                  );
                  const insertUser = _users.find(
                    item => item.mergeRowsKey === record?.mergeRowsKey
                  );
                  let deleteNum = 0;
                  if (insertUser?.oldEntranceCardNo) {
                    deleteNum = [..._users].filter(
                      item => item.mergeRowsKey === record?.mergeRowsKey
                    ).length;
                    deleteNum > 0 && _users.splice(insertIdx + 1, deleteNum - 1);
                  }
                  const currentEditableRowKeys = [...editableKeys];
                  authInfoList.forEach(({ blockGuid, authIdList, effectTime }, index) => {
                    const newId = shortid();
                    let newMainItem = {
                      ...defaultInfo,
                      id: index === 0 ? record?.id : newId,
                      blockGuid: blockGuid ? [blockGuid] : undefined,
                      authId: authIdList?.[0],
                      effectTime: effectTime
                        ? moment(effectTime.includes('T') ? effectTime.split('T')[0] : effectTime)
                        : undefined,
                    } as DataSourceType;
                    if (index === 0) {
                      editorFormRef.current?.setRowData?.(recordKey.toString(), newMainItem);
                      _users.splice(insertIdx, 1, newMainItem);
                      setRow('afterReplaceAuthInfos', _users);
                    } else {
                      currentEditableRowKeys.push(newId);
                      newMainItem = { ...newMainItem, isNew: true };
                      _users.splice(insertIdx, 0, newMainItem);
                      setRow('afterReplaceAuthInfos', _users);
                      editorFormRef.current?.setRowData?.(newId, newMainItem);
                    }
                  });
                  setEditableRowKeys(currentEditableRowKeys);
                }
              }}
            />
          );
        },
        render: (_, record) => {
          return record.apply.label;
        },
      },
      {
        title: '原门禁卡编号',
        dataIndex: 'oldEntranceCardNo',
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '原门禁卡编号不能为空！' }],
          };
        },
        renderFormItem: () => {
          return (
            <AccessCardNumberSelect trigger="onDidMount" showSearch idcTag={idcTag} disabled />
          );
        },
        onCell: (record, index) => {
          return {
            rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
          };
        },
      },
      {
        title: '新门禁卡编号',
        dataIndex: 'entranceCardNo',
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '新门禁卡编号必选！' }],
          };
        },
        renderFormItem: (_, { recordKey, record }) => {
          return (
            <UnboundEntranceGuardCardSelect
              showSearch
              idcTag={idcTag}
              onChange={value => {
                const _users = cloneDeep(applyAuths);
                _users.forEach(auth => {
                  if (auth.mergeRowsKey === record?.mergeRowsKey) {
                    editorFormRef.current?.setRowData?.(auth.id, {
                      ...auth,
                      entranceCardNo: value,
                    });
                  }
                });
                setRow(
                  'afterReplaceAuthInfos',
                  _users.map(auth => ({
                    ...auth,
                    entranceCardNo:
                      auth.mergeRowsKey === record?.mergeRowsKey ? value : auth.entranceCardNo,
                  }))
                );
              }}
            />
          );
        },
        onCell: (record, index) => {
          return {
            rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
          };
        },
      },
      {
        title: '授权楼栋',
        dataIndex: 'blockGuid',
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '授权楼栋必选！' }],
          };
        },
        renderFormItem: (_, { recordKey, record }) => {
          const disabledSpaceCodes: string[] = [];
          auths.forEach(auth => {
            if (auth.mergeRowsKey === record?.mergeRowsKey) {
              auth.blockGuid && disabledSpaceCodes.push(getBlockGuidFromArray(auth.blockGuid)!);
            }
          });
          const isApplyUserInSystem = record?.apply?.id;
          return (
            <LocationCascader
              style={{
                minWidth: 118,
              }}
              idc={idcTag}
              authorizedOnly={Boolean(isApplyUserInSystem)}
              userName={isApplyUserInSystem ? record.apply.loginName : undefined}
              disabled={!record?.apply?.value}
              nodeTypes={['BLOCK']}
              disabledSpaceCodes={disabledSpaceCodes}
              onChange={(value, _, selectedValues) => {
                if (recordKey) {
                  editorFormRef.current?.setRowData?.(recordKey.toString(), {
                    authId: undefined,
                  });
                }
              }}
            />
          );
        },
        render: (_, record, index) => {
          const {
            mergeRowsKey,
            apply,
            entranceCardNo,
            oldEntranceCardNo,
            originAuthInfos,
            cardNo,
            cardType,
          } = record;
          const rowSpan = generateGetRowSpan(auths)(record, index ?? 1);
          if (!record.blockGuid) {
            return '--';
          }
          if (rowSpan >= 1) {
            return (
              <span>
                <PlusCircleFilled
                  style={{ marginRight: 8, color: 'var(--manyun-info-color)' }}
                  onClick={() => {
                    const newId = shortid();
                    const newMainItem = {
                      id: newId,
                      mergeRowsKey,
                      apply,
                      entranceCardNo,
                      oldEntranceCardNo,
                      blockGuid: undefined,
                      authId: undefined,
                      effectTime: undefined,
                      originAuthInfos,
                      cardNo,
                      cardType,
                      isNew: true,
                    };
                    const _users = cloneDeep(applyAuths);
                    const insertIdx = _users.findIndex(
                      item => item.mergeRowsKey === newMainItem.mergeRowsKey
                    );
                    _users.splice(insertIdx, 0, newMainItem);
                    setRow('afterReplaceAuthInfos', _users);
                    setEditableRowKeys([newId, ...editableKeys]);
                  }}
                />
                <SpaceText guid={getBlockGuidFromArray(record.blockGuid) ?? ''} />
              </span>
            );
          } else {
            return <SpaceText guid={getBlockGuidFromArray(record.blockGuid) ?? ''} />;
          }
        },
      },
      {
        title: '门禁权限组角色',
        dataIndex: 'authId',
        width: 200,
        formItemProps: () => {
          return {
            rules: [{ required: true, message: '门禁权限组角色必选！' }],
          };
        },
        renderFormItem: (_, { record }) => {
          return (
            <EntranceGuardCardAuthGroupsSelect
              style={{ width: '100%' }}
              trigger="onDidMount"
              disabled={!record?.blockGuid}
              blockGuid={getBlockGuidFromArray(record?.blockGuid)}
            />
          );
        },
        render: (_, record) =>
          record.authId && record.blockGuid ? (
            <EntranceGuardCardAuthGroupText
              code={record.authId}
              blockGuid={getBlockGuidFromArray(record.blockGuid) ?? ''}
            />
          ) : (
            '--'
          ),
      },
      {
        title: '门禁卡有效期限',
        dataIndex: 'effectTime',
        valueType: 'date',
        width: 200,
        formItemProps: {
          rules: [{ required: true, message: '门禁卡有效期限必选!' }],
        },
        // renderFormItem: () => {
        //   return <DatePicker allowClear showTime={false} format="YYYY-MM-DD 23:59:59" />;
        // },
        render: (_, record) =>
          record.effectTime
            ? moment(record.effectTime).endOf('d').format('YYYY-MM-DD HH:mm:ss')
            : '--',
      },
      {
        title: '操作',
        valueType: 'option',
        fixed: 'right',
        width: 88,
        render: (text, record, _, action) => [
          <Button
            key={record.id}
            type="link"
            compact
            onClick={() => {
              action?.startEditable?.(record.id);
            }}
          >
            编辑
          </Button>,
          <Button
            key={`${record.id}${text}`}
            type="link"
            compact
            onClick={() => {
              onDeleteUser(record);
            }}
          >
            删除
          </Button>,
        ],
      },
    ];
  }

  return [
    {
      title: '申请人',
      dataIndex: 'apply',
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
      formItemProps: () => {
        return {
          rules: [
            {
              validator: (_, value) =>
                value?.value ? Promise.resolve() : Promise.reject('申请人必选!'),
            },
          ],
        };
      },
      renderFormItem: (_, { recordKey }) => {
        return (
          <UserSelect
            style={{ width: 120 }}
            labelInValue
            reserveSearchValue
            onChange={value => {
              if (recordKey) {
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  dept: value.department || undefined,
                  company: value.company || undefined,
                  phone: value.mobileNumber,

                  email: value.email || undefined,
                  cardType: undefined,
                  cardNo: undefined,
                  blockGuid: undefined,
                  apply: {
                    label: value.label,
                    value: value.value,
                    id: value.id,
                    loginName: value.login,
                  },
                });
              }
            }}
          />
        );
      },
      render: (_, record) => {
        return record.apply.label;
      },
    },
    {
      title: '联系电话',
      dataIndex: 'phone',
      width: 152,
      formItemProps: (form, config) => {
        const rowKey = config.rowKey ? config.rowKey[0] : null;
        const fileds = rowKey ? form.getFieldValue(rowKey) : {};
        if (!fileds || fileds?.apply?.id) {
          return {
            rules: [{ required: true, message: '联系电话必填！' }],
          };
        }
        return {
          rules: [
            { required: true, message: '联系电话必填！' },
            {
              pattern: /^1[3456789]\d{9}$/,
              message: '联系电话格式不正确',
            },
          ],
        };
      },
      renderFormItem: (_, { record }) => {
        return <Input maxLength={11} disabled={isSystemUser(record?.apply?.id)} />;
      },
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
    },
    {
      title: '电子邮箱',
      dataIndex: 'email',
      formItemProps: (form, config) => {
        const rowKey = config.rowKey ? config.rowKey[0] : null;
        const fileds = rowKey ? form.getFieldValue(rowKey) : {};
        if (!fileds || fileds?.apply?.id) {
          return {
            rules: [{ required: true, message: '请输入邮箱' }],
          };
        }
        return {
          rules: [
            {
              type: 'email',
              message: '请输入正确邮箱',
            },
          ],
        };
      },
      renderFormItem: (_, { record }) => {
        return (
          <Input
            style={{
              minWidth: 156,
            }}
            maxLength={32}
            disabled={isSystemUser(record?.apply?.id)}
          />
        );
      },
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
    },
    {
      title: '证件类型',
      dataIndex: 'cardType',
      width: 164,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '证件类型必选！' }],
        };
      },
      renderFormItem: (_, { recordKey }) => {
        return (
          <CertificateTypeSelect
            style={{
              minWidth: 118,
            }}
            hiddenTypes={['OTHER']}
            onChange={() => {
              if (recordKey) {
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  cardNo: undefined,
                });
              }
            }}
          />
        );
      },
      render: (_, { cardType }) => cardType && CERTIFICATE_TYPE_TEXT_MAP?.[cardType],
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
    },
    {
      title: '证件编号',
      dataIndex: 'cardNo',
      width: 226,
      formItemProps: (form, config) => {
        return {
          rules: [
            {
              validator: async (_, cardNo) => {
                const rowKey = config.rowKey ? config.rowKey[0] : null;
                const fileds = rowKey ? form.getFieldsValue()[rowKey] : {};
                if (!cardNo) {
                  return Promise.reject(new Error('证件编号必填!'));
                }
                if (
                  (fileds.cardType === 'ID_CARD' && !isIdCard(cardNo)) ||
                  (fileds.cardType === 'PASSPORT' && !isPassport(cardNo)) ||
                  (fileds.cardType === 'PERMIT' && !isPermit(cardNo))
                ) {
                  return Promise.reject(new Error('格式不正确'));
                } else {
                  return Promise.resolve();
                }
              },
            },
          ],
        };
      },
      renderFormItem: (_, { record }) => {
        return (
          <Input
            style={{
              minWidth: 161,
            }}
            disabled={!record?.cardType}
          />
        );
      },
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
    },
    {
      title: '所属部门',
      dataIndex: 'dept',
      width: 188,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '所属部门必填!' }],
        };
      },
      renderFormItem: () => {
        return (
          <Input
            style={{
              minWidth: 118,
            }}
            maxLength={8}
          />
        );
      },
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
    },
    {
      title: '所属公司',
      dataIndex: 'company',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '所属公司必填!' }],
        };
      },
      renderFormItem: () => {
        return (
          <Input
            style={{
              minWidth: 118,
            }}
            maxLength={16}
          />
        );
      },
      onCell: (record, index) => {
        return {
          rowSpan: generateGetRowSpan(auths)(record, index ?? 1),
        };
      },
    },
    {
      title: '授权楼栋',
      dataIndex: 'blockGuid',
      width: 188,
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '授权楼栋必选！' }],
        };
      },
      renderFormItem: (_, { recordKey, record, ...rest }) => {
        const disabledSpaceCodes: string[] = [];
        auths.forEach(auth => {
          if (auth.mergeRowsKey === record?.mergeRowsKey) {
            auth.blockGuid && disabledSpaceCodes.push(getBlockGuidFromArray(auth.blockGuid)!);
          }
        });
        const isApplyUserInSystem = record?.apply?.id;
        return (
          <LocationCascader
            style={{
              minWidth: 118,
            }}
            idc={idcTag}
            authorizedOnly={Boolean(isApplyUserInSystem)}
            userName={isApplyUserInSystem ? record.apply.loginName : undefined}
            disabled={!record?.apply?.value}
            nodeTypes={['BLOCK']}
            disabledSpaceCodes={disabledSpaceCodes}
            onChange={(value, _, selectedValues) => {
              if (recordKey) {
                editorFormRef.current?.setRowData?.(recordKey.toString(), {
                  authId: undefined,
                });
              }
            }}
          />
        );
      },
      render: (_, record, index) => {
        const { mergeRowsKey, apply, dept, company, phone, email, cardType, cardNo } = record;
        const rowSpan = generateGetRowSpan(auths)(record, index ?? 1);
        if (!record.blockGuid) {
          return '--';
        }
        if (rowSpan >= 1) {
          return (
            <span>
              <PlusCircleFilled
                style={{ marginRight: 8, color: 'var(--manyun-info-color)' }}
                onClick={() => {
                  const newId = shortid();
                  const newMainItem = {
                    id: newId,
                    mergeRowsKey,
                    apply,
                    dept,
                    company,
                    phone,
                    email,
                    cardType,
                    cardNo,
                    entranceCardNo: undefined,
                    blockGuid: undefined,
                    authId: undefined,
                    authName: undefined,
                    isNew: true,
                  };
                  const _users = cloneDeep(applyAuths);
                  const insertIdx = _users.findIndex(
                    item => item.mergeRowsKey === newMainItem.mergeRowsKey
                  );
                  _users.splice(insertIdx, 0, newMainItem);
                  setRow('applyAuths', _users);
                  setEditableRowKeys([newId, ...editableKeys]);
                }}
              />
              <SpaceText guid={getBlockGuidFromArray(record.blockGuid) ?? ''} />
            </span>
          );
        } else {
          return <SpaceText guid={getBlockGuidFromArray(record.blockGuid) ?? ''} />;
        }
      },
    },

    {
      title: '门禁权限组角色',
      dataIndex: 'authId',
      formItemProps: () => {
        return {
          rules: [{ required: true, message: '门禁权限组角色必选！' }],
        };
      },
      renderFormItem: (_, { record }) => {
        return (
          <EntranceGuardCardAuthGroupsSelect
            style={{ maxWidth: 180 }}
            trigger="onDidMount"
            disabled={!record?.blockGuid}
            blockGuid={getBlockGuidFromArray(record?.blockGuid)}
          />
        );
      },
      render: (_, record) =>
        record.authId && record.blockGuid ? (
          <EntranceGuardCardAuthGroupText
            code={record.authId}
            blockGuid={getBlockGuidFromArray(record.blockGuid) ?? ''}
          />
        ) : (
          '--'
        ),
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      width: 88,
      render: (text, record, _, action) => [
        <Button
          key={record.id}
          type="link"
          compact
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </Button>,
        <Button
          key={`${record.id}${text}`}
          type="link"
          compact
          onClick={() => {
            onDeleteUser(record);
          }}
        >
          删除
        </Button>,
      ],
    },
  ];
};

function getBlockGuidFromArray(blockGuid?: string[]) {
  if (blockGuid && blockGuid.length > 0) {
    return blockGuid[0];
  }
  return;
}
