import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';

import { fetchPowerCheckItemList } from '@manyun/ticket.service.fetch-power-check-item-list';
import type { CheckItem } from '@manyun/ticket.service.fetch-power-check-item-list';

import styles from './check-item-table.module.less';

export type CheckItemTableProps = {
  /** 工单子类 */
  taskSubType: string;
  /** 上下电机柜 ID */
  powerGridId: number;
};

export function CheckItemTable({ taskSubType, powerGridId }: CheckItemTableProps) {
  const [beforeList, setBeforeList] = useState<CheckItem[]>([]); // 上电前的
  const [afterList, setAfterList] = useState<CheckItem[]>([]); // 上电后的
  useEffect(() => {
    fetchPowerCheckItemList({
      taskSubType,
      powerGridId,
    }).then(({ data, error }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setBeforeList(data.data.filter(item => item.itemType === 'CHECK_BEFORE'));
      setAfterList(data.data.filter(item => item.itemType === 'CHECK_AFTER'));
    });
  }, [powerGridId, taskSubType]);

  const columns: ColumnsType<CheckItem> = [
    {
      title: '操作检查结果',
      dataIndex: 'id',
      key: 'id',
      colSpan: 4,
      className: styles.firstColumn,
      render: (_value, _record, index) => {
        if (index === 0) {
          return '操作上电前';
        }
        if (index === beforeList.length) {
          return '操作上电后';
        }
        return '';
      },
      onCell: (_, index) => {
        if (index === 0) {
          return { rowSpan: beforeList.length };
        }
        if (index === beforeList.length) {
          return { rowSpan: afterList.length };
        }
        return { rowSpan: 0 };
      },
    },
    {
      title: '',
      dataIndex: 'itemName',
      key: 'itemName',
      colSpan: 0,
    },
    {
      title: '',
      dataIndex: 'itemMethod',
      key: 'itemMethod-itemNormal',
      colSpan: 0,
      render: (_, record) => `${record.itemMethod}-${record.itemNormal}`,
    },
    {
      title: '',
      dataIndex: 'checkResult',
      key: 'checkResult',
      colSpan: 0,
      render: (_, record) => {
        if (record.checkResult === 'NORMAL') {
          return <Tag color="success">正常</Tag>;
        }
        if (record.checkResult === 'ABNORMAL') {
          return <Tag color="error">异常</Tag>;
        }
        return '--';
      },
    },
  ];

  // 无数据时不显示
  if (beforeList.length === 0 && afterList.length === 0) {
    return null;
  }

  return (
    <Table
      className={styles.table}
      columns={columns}
      dataSource={[...beforeList, ...afterList]}
      bordered
      pagination={false}
    />
  );
}
