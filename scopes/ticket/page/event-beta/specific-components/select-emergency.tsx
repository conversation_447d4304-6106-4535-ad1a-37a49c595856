import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetatypeSelect } from '@manyun/resource-hub.ui.metatype-select';
import { useLazyEmergencyProcess } from '@manyun/ticket.gql.client.tickets';
import type { EmergencyProcessData, EmergencyProcessQ } from '@manyun/ticket.gql.client.tickets';
import { generateEmergencyProcessLocation } from '@manyun/ticket.route.ticket-routes';

export type ChangeSourceProps = {
  blockGuid: string;
  onChange?: (value: EmergencyProcessData[]) => void;
  value?: EmergencyProcessData[];
} & TableProps<EmergencyProcessData>;

export const SelectEmergency = (
  { blockGuid, value, onChange }: ChangeSourceProps,
  ref: React.Ref<RefSelectProps>
) => {
  const [emergencyProcessData, setEmergencyProcessData] = useState<EmergencyProcessData[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [filterValue, setFilterValue] = useState<EmergencyProcessQ>({
    pageNum: 1,
    pageSize: 10,
  });

  const [visible, setVisible] = useState<boolean>(false);
  const [keyword, setKeyword] = useState<string>('');
  const [categoryCode, setCategoryCode] = useState<string | null>(null);
  const [selectedRows, setSelectedRows] = useState<EmergencyProcessData[]>([]);
  const [getEmergencyProcess, { loading }] = useLazyEmergencyProcess({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  useEffect(() => {
    (async () => {
      if (!visible) {
        return;
      }
      const { data, error: fetchDeviceListError } = await getEmergencyProcess({
        variables: {
          query: {
            ...filterValue,
            blockGuid,
          },
        },
      });
      if (fetchDeviceListError) {
        setEmergencyProcessData([]);
        message.error(fetchDeviceListError.message);
        return;
      }
      if (data?.emergencyProcess.data) {
        setEmergencyProcessData(data.emergencyProcess.data);
        setTotal(data.emergencyProcess?.total ?? 0);
      } else {
        setEmergencyProcessData([]);
      }
    })();
  }, [filterValue, blockGuid, visible, getEmergencyProcess]);

  return (
    <>
      <Space direction="horizontal">
        <Button
          disabled={!blockGuid}
          onClick={() => {
            setVisible(true);
            setSelectedRows(value ?? []);
          }}
        >
          {value?.length ? '点此修改' : '点此选择'}
        </Button>
        {value?.length ? (
          <Typography.Text
            ellipsis={{ tooltip: value[0].name }}
            style={{ width: 300, verticalAlign: 'center' }}
          >
            <Link
              target="_blank"
              to={{
                pathname: generateEmergencyProcessLocation({
                  id: value[0].emergencyId,
                }),
              }}
            >
              {value[0].name}
            </Link>
          </Typography.Text>
        ) : null}
      </Space>
      <Drawer
        placement="right"
        size="large"
        title="选择应急流程"
        open={visible}
        destroyOnClose
        // zIndex={1100}
        maskClosable={false}
        extra={
          <Space>
            <Button
              onClick={() => {
                setVisible(false);
                setFilterValue({ pageNum: 1, pageSize: 10 });
                setSelectedRows([]);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                setFilterValue({ pageNum: 1, pageSize: 10 });
                setSelectedRows([]);
                setVisible(false);
                onChange && onChange(selectedRows);
              }}
            >
              确认
            </Button>
          </Space>
        }
        onClose={() => {
          setVisible(false);
          setFilterValue({ pageNum: 1, pageSize: 10 });
          setSelectedRows([]);
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <Space style={{ width: '100%' }} direction="horizontal">
            <Input
              placeholder="搜索应急流程名称"
              allowClear
              style={{ width: 216 }}
              value={keyword}
              onChange={e => {
                setKeyword(e.target.value);
              }}
            />
            <MetatypeSelect
              metaType={MetaType.EMERGENCY_CATEGORY}
              allowClear
              placeholder="选择专业分类"
              style={{ width: 216 }}
              value={categoryCode}
              queryDelete
              onChange={(value: string) => {
                setCategoryCode(value);
              }}
            />
            <Button
              type="primary"
              onClick={() => {
                setFilterValue({ ...filterValue, categoryCode, keyword, pageNum: 1 });
              }}
            >
              搜索
            </Button>
            <Button
              onClick={() => {
                setFilterValue({ ...filterValue, categoryCode: '', keyword: '', pageNum: 1 });
                setKeyword('');
                setCategoryCode(null);
              }}
            >
              重置
            </Button>
          </Space>
          <Table
            loading={loading}
            size="middle"
            rowKey="emergencyId"
            dataSource={emergencyProcessData}
            columns={[
              {
                title: '应急流程名称',
                dataIndex: 'name',
                fixed: 'left',
                render: (_, { name, emergencyId }) => {
                  if (emergencyId) {
                    return (
                      <Typography.Text ellipsis={{ tooltip: name }} style={{ width: 300 }}>
                        <Link
                          target="_blank"
                          to={generateEmergencyProcessLocation({
                            id: encodeURIComponent(emergencyId),
                          })}
                        >
                          {name}
                        </Link>
                      </Typography.Text>
                    );
                  }
                  return '--';
                },
              },
              {
                title: '专业分类',
                dataIndex: 'categoryName',
              },
            ]}
            rowSelection={{
              selectedRowKeys: selectedRows.map(item => item.emergencyId),
              type: 'radio',
              onChange: (_, selectedRows) => {
                setSelectedRows(selectedRows);
              },
            }}
            pagination={{
              total: total,
              current: filterValue.pageNum ?? 1,
              pageSize: filterValue.pageSize ?? 10,
              onChange: (pageNum, pageSize) => {
                setFilterValue({ ...filterValue, pageNum, pageSize });
              },
            }}
          />
        </Space>
      </Drawer>
    </>
  );
};
