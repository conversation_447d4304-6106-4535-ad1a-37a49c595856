import { EditOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { useChangeEventLevel } from '@manyun/ticket.gql.client.tickets';

export type EventLevelUpdateModalProps = {
  eventId: string;
  showIcon?: boolean;
  refetchDetail: () => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
};

type FormValue = {
  targetLevelCode: string;
  reason: string;
};
export function EventLevelUpdateModal({
  eventId,
  showIcon = false,
  refetchDetail,
  getIsDetectTimeout,
}: EventLevelUpdateModalProps) {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();
  const [detectEventProblem, { loading }] = useChangeEventLevel({
    onCompleted(data) {
      if (data.changeEventLevel?.success) {
        message.success('事件等级校正已提交');
        refetchDetail && refetchDetail();
        setOpen(false);
      } else {
        message.error(data.changeEventLevel?.message);
      }
    },
  });

  const showDrawer = () => {
    const { isTimeout } = getIsDetectTimeout();
    if (!showIcon && isTimeout) {
      message.error('事件处理超时，请先操作超时升级确认');
      setTimeout(() => {
        refetchDetail();
      }, 3000);
      return;
    }
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      detectEventProblem({
        variables: {
          query: {
            eventId,
            ...values,
          },
        },
      });
    });
  };
  return (
    <>
      {showIcon ? (
        <Tooltip title="事件等级校正">
          <EditOutlined
            style={{ marginLeft: '4px', color: 'var(--manyun-primary-color)' }}
            onClick={showDrawer}
          />
        </Tooltip>
      ) : (
        <Button onClick={showDrawer}>事件升级请示</Button>
      )}
      <Modal
        destroyOnClose
        title="事件等级校正"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              提交
            </Button>
          </Space>
        }
        bodyStyle={{ paddingBottom: 0 }}
        onCancel={onClose}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Alert
            message="事件等级校正后，将触发审批流，审批通过后事件等级将自动更新，审批拒绝则事件等级保持不变。"
            type="warning"
            closable={false}
          />
          <Form
            // style={{ width: 560 }}
            form={form}
            preserve={false}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
          >
            <Form.Item
              label="事件等级校正为"
              name="targetLevelCode"
              rules={[{ required: true, message: '事件校正等级为必选' }]}
            >
              <MetaTypeSelect metaType={MetaType.N_EVENT_LEVEL} />
            </Form.Item>

            <Form.Item label="校正原因" name="reason">
              <Input.TextArea style={{ width: 428 }} maxLength={300} />
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
}
