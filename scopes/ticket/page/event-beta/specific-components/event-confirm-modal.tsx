import type { Moment } from 'moment';
import moment from 'moment';
import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfirmEvent } from '@manyun/ticket.gql.client.tickets';
import type { EmergencyProcessData } from '@manyun/ticket.gql.client.tickets';

import { SelectEmergency } from './select-emergency';

export type EventConfirmModalProps = {
  eventId: string;
  blockGuid: string;
  startTime: number;
  currentUser?: number;
  refetchDetail: () => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
};

type FormValue = {
  /**
   * 处理人id
   */
  handlerId: number;
  /**
   * 开始时间 毫秒级时间戳
   */
  startTime: number;
  /**
   * 完成时间 毫秒级时间戳
   */
  endTime: number;
  /**
   * 是否关联应急流程
   */
  relateEmerge: boolean;
  emergencyIdList?: string[] | null;
  /**
   * 处理结果
   */
  handleContent: string;
  emergency: EmergencyProcessData[];
};
export function EventConfirmModal({
  eventId,
  blockGuid,
  startTime,
  currentUser,
  refetchDetail,
  getIsDetectTimeout,
}: EventConfirmModalProps) {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();
  const relateEmerge = Form.useWatch('relateEmerge', form);
  const [confirmEvent, { loading }] = useConfirmEvent({
    onCompleted(data) {
      if (data.confirmEvent?.success) {
        message.success('事件已确认');
        refetchDetail && refetchDetail();
        setOpen(false);
      } else {
        if (data.confirmEvent?.code === 'STATUS_OVER_TIME') {
          setTimeout(() => {
            refetchDetail();
          }, 3000);
          message.error('事件处理超时，请先操作超时升级确认');
          setOpen(false);
          return;
        }
        message.error(data.confirmEvent?.message);
      }
    },
  });

  const showDrawer = () => {
    const { isTimeout } = getIsDetectTimeout();
    if (isTimeout) {
      message.error('事件处理超时，请先操作超时升级确认');
      setTimeout(() => {
        refetchDetail();
      }, 3000);
      return;
    }
    form.setFieldValue('startTime', moment(startTime));
    form.setFieldValue('handlerId', currentUser);

    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      const { startTime, endTime, emergency, ...resp } = values;
      confirmEvent({
        variables: {
          query: {
            eventId,
            ...resp,
            startTime: startTime.valueOf(),
            endTime: endTime.valueOf(),
            emergencyIdList: emergency?.length ? [emergency[0].emergencyId] : null,
          },
        },
      });
    });
  };
  return (
    <>
      <Button type="primary" onClick={showDrawer}>
        事件确认
      </Button>
      <Modal
        destroyOnClose
        title="事件确认"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              提交
            </Button>
          </Space>
        }
        onCancel={onClose}
      >
        <Form
          style={{ width: 520 }}
          form={form}
          preserve={false}
          labelCol={{ span: 5 }}
          wrapperCol={{ span: 19 }}
        >
          <Form.Item
            label="处理人"
            name="handlerId"
            rules={[{ required: true, message: '处理人必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              userState="in-service"
              resourceParams={[{ resourceType: 'BUILDING', resourceCodes: [blockGuid] }]}
              allowClear
              labelInValue={false}
            />
          </Form.Item>
          <Form.Item
            label="开始时间"
            name="startTime"
            rules={[{ required: true, message: '开始时间必选' }]}
          >
            <DatePicker style={{ width: 216 }} disabled showTime format="YYYY-MM-DD HH:mm:ss" />
          </Form.Item>
          <Form.Item
            label="完成时间"
            name="endTime"
            dependencies={['startTime']}
            rules={[
              { required: true, message: '完成时间必选' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (value) {
                    if (moment(value).diff(moment(getFieldValue('startTime')), 'seconds') <= 0) {
                      return Promise.reject(new Error('完成时间不可早于开始时间'));
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={handleEndDisabledDate}
              disabledTime={disabledDateTimeAfterToday}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            label="有应急流程"
            name="relateEmerge"
            rules={[{ required: true, message: '有应急流程必选' }]}
          >
            <Radio.Group
              options={[
                { label: '是', value: true },
                { label: '否', value: false },
              ]}
            />
          </Form.Item>
          {relateEmerge && (
            <Form.Item
              label="应急流程名称"
              name="emergency"
              rules={[
                {
                  required: true,
                  message: '应急流程名称必选！',
                },
              ]}
            >
              <SelectEmergency blockGuid={blockGuid} />
            </Form.Item>
          )}
          <Form.Item
            label="处理结果"
            name="handleContent"
            rules={[{ required: true, message: '处理结果必填' }]}
          >
            <Input.TextArea maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

function handleEndDisabledDate(current: Moment) {
  return current.valueOf() > moment().endOf('day').valueOf();
}
function range(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export function disabledDateTimeAfterToday(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (current) {
    const choseHour = current.hours();
    const choseMinute = current.minute();
    const isToday = current.isSame(compareMoment, 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => (choseMinute === minute ? range(second, 60) : []),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}
