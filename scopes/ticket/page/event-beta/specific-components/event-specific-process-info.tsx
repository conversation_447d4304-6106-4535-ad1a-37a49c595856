import { QuestionCircleOutlined } from '@ant-design/icons';
import sortBy from 'lodash.sortby';
import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { RoleText } from '@manyun/auth-hub.ui.role-text';
import { User } from '@manyun/auth-hub.ui.user';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type {
  EventBetaData,
  EventProgressData,
  ProcessRecordInfo,
} from '@manyun/ticket.gql.client.tickets';
import { useLazyEventProgress } from '@manyun/ticket.gql.client.tickets';
import type {
  EventBetaProcessRecordStatusType,
  EventBetaProcessStatusType,
} from '@manyun/ticket.model.event';
import {
  EventBetaProcessRecordStatus,
  EventBetaProcessRecordStatusMap,
  EventBetaProcessStatus,
  EventBetaStatusMap,
} from '@manyun/ticket.model.event';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';
import { getSla } from '@manyun/ticket.util.sla';

import { EventDetectModal } from './event-detect-modal';
import { EventProcessRecordModal } from './event-process-record-modal';

export type PhaseRecord = {
  key: string;
  handleTime: string;
  handlePeople?: { id: number };
  handleContent: string;
  spare?: string[];
};

export type EventSpecificProcessInfoProps = {
  blockGuid: string;
  eventCenterInfo: EventBetaData;
  isDetectTimeout: {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
    sla?: number | null;
    startTime?: number | null;
    endTime?: number | null;
  };
  currentUser?: number;
  refetchDetail: () => void;
  setProcess: (data: EventProgressData[]) => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
};
export function EventSpecificProcessInfo({
  blockGuid,
  eventCenterInfo,
  isDetectTimeout,
  currentUser,
  refetchDetail,
  setProcess,
  getIsDetectTimeout,
}: EventSpecificProcessInfoProps) {
  const [expandedRowKeys, setExpandedRowKeys] = useState<string[]>([]);
  //查询进展
  const [getEventProgress, { data: progress, loading }] = useLazyEventProgress({
    onCompleted: data => {
      if (data.eventProgress.data) {
        setProcess(data.eventProgress.data);
        const _expandedRowKeys = data.eventProgress.data
          .filter(item => item.processRecordList?.length)
          .map(item => item.phase);
        setExpandedRowKeys(_expandedRowKeys);
      }
    },
  });

  const [, { checkUserId }] = useAuthorized();
  const isOwner = eventCenterInfo?.inHandover
    ? checkUserId(eventCenterInfo?.handoverUserId)
    : checkUserId(eventCenterInfo?.ownerId);
  const { eventStatus } = eventCenterInfo;

  const columns: ColumnsType<EventProgressData> = useMemo(() => {
    const color = {
      [EventBetaProcessRecordStatus.Completed]: 'success',
      [EventBetaProcessRecordStatus.Inprogress]: 'processing',
      [EventBetaProcessRecordStatus.Pending]: 'warning',
    };
    const basicColumns: ColumnsType<EventProgressData> = [
      {
        title: '阶段',
        dataIndex: 'phase',
        key: 'phase',
        width: 180,
        render: (_, { status, phase }) => (
          <Space wrap>
            <div>{EventBetaStatusMap[phase as EventBetaProcessStatusType]}</div>
            <div>
              {status && (
                <Tag color={color[status as EventBetaProcessRecordStatusType]}>
                  {EventBetaProcessRecordStatusMap[status as EventBetaProcessRecordStatus]}
                </Tag>
              )}
            </div>
          </Space>
        ),
      },
      {
        title: '处理人',
        width: 90,
        dataIndex: 'handlerName',
        key: 'handlerName',
      },
      {
        title: '处理结果',
        dataIndex: 'handleContent',
        key: 'handleContent',
        width: '35%',
        render: (_, { handleContent }) => {
          if (handleContent) {
            return (
              <Typography.Paragraph
                style={{ marginBottom: 0, width: '100%' }}
                ellipsis={{ rows: 3, tooltip: true }}
              >
                {handleContent}
              </Typography.Paragraph>
            );
          }
          return '--';
        },
      },
      {
        title: 'SLA',
        dataIndex: 'sla',
        key: 'sla',
        render: (_, { sla }) => {
          if (sla) {
            return (
              <TicketSlaText
                taskSla={sla}
                delay={sla}
                unit="SECOND"
                effectTime={null}
                endTime={null}
              />
            );
          }
          return '--';
        },
      },
      {
        title: '开始时间',
        dataIndex: 'startTime',
        key: 'startTime',
        render: (_, { startTime }) => {
          if (startTime) {
            return moment(startTime).format('YYYY-MM-DD HH:mm:ss');
          }
          return '--';
        },
      },
      {
        title: '完成时间',
        dataIndex: 'endTime',
        key: 'endTime',
        render: (_, { endTime }) => {
          if (endTime) {
            return moment(endTime).format('YYYY-MM-DD HH:mm:ss');
          }
          return '--';
        },
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        render: (_, record) => {
          if (record.status === EventBetaProcessRecordStatus.Pending) {
            return '--';
          }
          return (
            <Space>
              <EventProcessRecordModal
                blockGuid={blockGuid}
                eventInfo={{ eventId: eventCenterInfo.eventId, eventPhase: record.phase }}
                currentUser={currentUser}
                onSuccess={() => {
                  getEventProgress({
                    variables: {
                      eventId: eventCenterInfo.eventId,
                    },
                  });
                }}
              />
            </Space>
          );
        },
      },
    ];
    if (
      ![
        EventBetaProcessStatus.Confirming,
        EventBetaProcessStatus.Relieving,
        EventBetaProcessStatus.Finishing,
      ].includes(eventStatus as EventBetaProcessStatusType) ||
      !isOwner
    ) {
      return basicColumns.slice(0, basicColumns.length - 1);
    }
    return basicColumns;
  }, [blockGuid, eventCenterInfo.eventId, eventStatus, isOwner, currentUser, getEventProgress]);

  useEffect(() => {
    getEventProgress({
      variables: {
        eventId: eventCenterInfo.eventId,
      },
    });
  }, [eventCenterInfo.eventId, eventCenterInfo.eventStatus, getEventProgress]);

  const getSlaTooltip = () => {
    if (isDetectTimeout.needCountdown && isDetectTimeout.stage === 'first') {
      return '此处为事件单创建后的计时，超时仍待查将触发升级提醒';
    }
    if (isDetectTimeout.needCountdown && isDetectTimeout.stage === 'sed') {
      return '此处为事件单超时升级后的计时，超时仍待查将再次触发升级提醒';
    }
    if (isDetectTimeout.needCountdown && isDetectTimeout.stage === 'ascertained') {
      return '此处为事件原因已查明后的计时，超时仍未缓解将触发升级提醒';
    }
  };

  const getRoles = () => {
    if (isDetectTimeout.stage === 'first') {
      return (
        <>
          {eventCenterInfo.otDetectInfo?.firHandleRoleList?.map((item, index) => (
            <>
              <RoleText key={item} code={item} />
              {index + 1 !== eventCenterInfo.otDetectInfo?.firHandleRoleList?.length ||
              eventCenterInfo.otDetectInfo?.firHandleUserList?.length
                ? '/'
                : ''}
            </>
          ))}
          {eventCenterInfo.otDetectInfo?.firHandleUserList?.map((item, index) => (
            <>
              <User key={item} id={item} showAvatar={false} />
              {index + 1 !== eventCenterInfo.otDetectInfo?.firHandleUserList?.length ? '/' : ''}
            </>
          ))}
        </>
      );
    }
    if (isDetectTimeout.stage === 'sed') {
      return (
        <>
          {eventCenterInfo.otDetectInfo?.secHandleRoleList?.map((item, index) => (
            <>
              <RoleText key={item} code={item} />
              {index + 1 !== eventCenterInfo.otDetectInfo?.secHandleRoleList?.length ||
              eventCenterInfo.otDetectInfo?.secHandleUserList?.length
                ? '/'
                : ''}
            </>
          ))}
          {eventCenterInfo.otDetectInfo?.secHandleUserList?.map((item, index) => (
            <>
              <User key={item} id={item} showAvatar={false} />
              {index + 1 !== eventCenterInfo.otDetectInfo?.secHandleUserList?.length ? '/' : ''}
            </>
          ))}
        </>
      );
    }
    if (isDetectTimeout.stage === 'ascertained') {
      return (
        <>
          {eventCenterInfo?.otRelieveInfo?.handleRoleList?.map((item, index) => (
            <>
              <RoleText key={item} code={item} />
              {index + 1 !== eventCenterInfo.otRelieveInfo?.handleRoleList?.length ||
              eventCenterInfo.otRelieveInfo?.handleUserList?.length
                ? '/'
                : ''}
            </>
          ))}
          {eventCenterInfo.otRelieveInfo?.handleUserList?.map((item, index) => (
            <>
              <User key={item} id={item} showAvatar={false} />
              {index + 1 !== eventCenterInfo.otRelieveInfo?.handleUserList?.length ? '/' : ''}
            </>
          ))}
        </>
      );
    }
  };

  // 已查明
  const ascertained = eventCenterInfo.detectStatus === 1;
  const { timeNumerator } =
    isDetectTimeout.sla && isDetectTimeout.startTime && isDetectTimeout.endTime
      ? getSla({
          taskSla: isDetectTimeout.sla,
          delay: null,
          effectTime: isDetectTimeout.startTime,
          endTime: isDetectTimeout.endTime,
          unit: 'SECOND',
        })
      : { timeNumerator: '--' };
  const isMisdescription = eventCenterInfo?.falseAlarm === 1 || eventCenterInfo?.falseAlarm === 2;
  const misdescriptionIsClose = isMisdescription
    ? EventBetaProcessStatus.Closed === eventStatus
      ? ascertained
      : true
    : true;
  return (
    <Spin spinning={loading}>
      <Space style={{ display: 'flex', width: '100%' }} size="middle" direction="vertical">
        <Space style={{ display: 'flex', width: '100%' }} direction="horizontal">
          {(([
            EventBetaProcessStatus.Confirming,
            EventBetaProcessStatus.Relieving,
            EventBetaProcessStatus.Finishing,
          ].includes(eventStatus as EventBetaProcessStatusType) &&
            isOwner &&
            !ascertained &&
            !isDetectTimeout.isTimeout) ||
            (EventBetaProcessStatus.Closed !== eventStatus &&
              isMisdescription &&
              isOwner &&
              !ascertained)) && (
            <EventDetectModal
              eventId={eventCenterInfo.eventId}
              getIsDetectTimeout={getIsDetectTimeout}
              refetchDetail={refetchDetail}
              onSuccess={() => refetchDetail()}
            />
          )}
          {ascertained && (
            <Typography.Title style={{ marginBottom: 0 }} level={5}>
              事件定位
            </Typography.Title>
          )}
          {/* 误报 关闭后如已填定位，则正常展示；如未填定位，则事件定位整块内容不透出 */}
          {misdescriptionIsClose && (
            <Tag color={ascertained ? 'success' : 'warning'}>{ascertained ? '已查明' : '待查'}</Tag>
          )}
          {isDetectTimeout.needCountdown && misdescriptionIsClose ? (
            <Space direction="horizontal">
              <Typography.Text
                type={isDetectTimeout.isTimeout && !isMisdescription ? 'danger' : undefined}
              >
                SLA计时:
              </Typography.Text>
              {isDetectTimeout.sla && isDetectTimeout.startTime && (
                <Typography.Text
                  type={isDetectTimeout.isTimeout && !isMisdescription ? 'danger' : undefined}
                >
                  {timeNumerator}
                </Typography.Text>
              )}
              {!(
                isMisdescription ||
                (isDetectTimeout.stage === 'sed' && !eventCenterInfo.otDetectInfo?.firUpgraded)
              ) && (
                <Tooltip title={getSlaTooltip()}>
                  <QuestionCircleOutlined />
                </Tooltip>
              )}
              {isDetectTimeout.isTimeout && !isMisdescription && (
                <Tag color="error">待{getRoles()}操作超时升级确认</Tag>
              )}
            </Space>
          ) : null}
        </Space>
        {ascertained && (
          <Space style={{ display: 'flex', width: '100%' }} direction="vertical" size="middle">
            <Typography.Text>
              定位时间：
              {moment(eventCenterInfo.detectTime).format('YYYY-MM-DD HH:mm:ss')}
            </Typography.Text>
            <Typography.Text>
              定位原因：
              {eventCenterInfo.detectReason ?? '--'}
            </Typography.Text>
          </Space>
        )}
        <Table
          rowKey={record => record.phase}
          loading={loading}
          dataSource={progress?.eventProgress.data}
          columns={columns}
          expandable={{
            expandedRowRender: parent => (
              <ExpandTable originDatasource={parent.processRecordList ?? []} />
            ),
            rowExpandable: record => !!record.processRecordList?.length,
            expandedRowKeys: expandedRowKeys,
            onExpandedRowsChange(expandedKeys) {
              setExpandedRowKeys(expandedKeys as string[]);
            },
          }}
          pagination={false}
        />
      </Space>
    </Spin>
  );
}

function ExpandTable({ originDatasource }: { originDatasource: ProcessRecordInfo[] }) {
  const subColumns: ColumnsType<ProcessRecordInfo> = [
    {
      title: '处理时间',
      dataIndex: 'handleTime',
      key: 'handleTime',
      width: 167,
      sorter: (a, b) => Number(a.handleTime ?? 0) - Number(b.handleTime ?? 0),
      render: (_, { handleTime }) => {
        if (handleTime) {
          return moment(handleTime).format('YYYY-MM-DD HH:mm:ss');
        }
        return '--';
      },
    },
    {
      title: '处理人',
      dataIndex: 'handlerName',
      width: 90,
      key: 'handlerName',
    },
    {
      title: '处理内容',
      dataIndex: 'handleContent',
      key: 'handleContent',
      width: '76%',
      render: (_, { handleContent }) => {
        if (handleContent) {
          return (
            <Typography.Paragraph
              style={{ marginBottom: 0, width: '100%' }}
              ellipsis={{ rows: 3, tooltip: true }}
            >
              {handleContent}
            </Typography.Paragraph>
          );
        }
        return '--';
      },
    },
  ];

  return originDatasource.length > 0 ? (
    <Table
      columns={subColumns}
      dataSource={sortBy(originDatasource, 'handleTime')}
      pagination={false}
    />
  ) : null;
}
