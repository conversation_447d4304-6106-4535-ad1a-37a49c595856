import React from 'react';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { Card } from '@manyun/base-ui.ui.card';
import type { EventBetaData } from '@manyun/ticket.gql.client.tickets';

export function OperationRecords({ eventCenterInfo }: { eventCenterInfo: EventBetaData }) {
  return (
    <Card bordered={false}>
      <OperationLogTable
        showColumns={['serialNumber', 'targetType', 'modifyType']}
        defaultSearchParams={{
          targetType: 'EVENT',
          targetId: eventCenterInfo?.eventId,
        }}
        isTargetIdEqual={(targetId: string) => {
          return targetId === eventCenterInfo?.eventId;
        }}
      />
    </Card>
  );
}
