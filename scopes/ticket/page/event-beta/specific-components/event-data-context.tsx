import { createContext, useContext } from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import type { DrillOrderApprovalNode } from '@manyun/ticket.gql.client.tickets/';
import type { ProcessEngineConfig } from '@manyun/ticket.model.event';

export type EventTab = {
  key: string;
  tab: string;
  isRendered: boolean;
  isValid: boolean;
};
export type Values = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  parentForm: FormInstance<any> | undefined;
  drawerVisible: boolean;
  eventProcessEngineNodes: DrillOrderApprovalNode[];
  eventProcessEngineConfig: ProcessEngineConfig | undefined;
  notificationVisible: boolean;
};

export type Handlers = {
  setDrawerVisible: (param: boolean) => void;
  setEventProcessEngineNodes: () => void;
  setNotificationVisible: (param: boolean) => void;
};
const noop = () => {};
export const initialValue: [Values, Handlers] = [
  {
    parentForm: undefined,
    drawerVisible: false,
    eventProcessEngineNodes: [],
    eventProcessEngineConfig: undefined,
    notificationVisible: false,
  },
  {
    setDrawerVisible: noop,
    setEventProcessEngineNodes: noop,
    setNotificationVisible: noop,
  },
];

export const EventDataContext = createContext<[Values, Handlers]>(initialValue);
export const useEventData = () => useContext(EventDataContext);
