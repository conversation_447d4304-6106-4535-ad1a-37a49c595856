import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useRedirectEvent } from '@manyun/ticket.gql.client.tickets';

import { useEventData } from './event-data-context';

export type EventRedirectModalButtonProps = {
  eventId: number;
  blockGuid: string;
  onSuccess?: () => void;
} & ButtonProps;
export function EventRedirectModalButton({
  eventId,
  blockGuid,
  onSuccess,
}: EventRedirectModalButtonProps) {
  const [redirectEvent, { loading }] = useRedirectEvent({
    onCompleted(data) {
      if (data.redirectEvent?.success) {
        message.success('转交成功');
        closeModal();
        onSuccess && onSuccess();
      } else {
        message.error(data.redirectEvent?.message);
      }
    },
  });
  const [{ eventProcessEngineNodes }] = useEventData();
  const [form] = Form.useForm();
  const [open, setOpen] = useState(false);
  const showModal = () => {
    setOpen(true);
  };
  const closeModal = () => {
    setOpen(false);
  };

  return (
    <>
      <Button
        onClick={() => {
          showModal();
        }}
      >
        转交
      </Button>
      <Modal
        destroyOnClose
        title="转交"
        open={open}
        okButtonProps={{ loading }}
        okText="提交"
        onCancel={() => {
          closeModal();
        }}
        onOk={() => {
          form.validateFields().then(values => {
            const { redirectPeople, redirectReason } = values;
            redirectEvent({
              variables: {
                eventId,
                eventPhase: eventProcessEngineNodes[eventProcessEngineNodes.length - 1].code,
                targetUserId: redirectPeople.value,
                reason: redirectReason,
              },
            });
          });
        }}
      >
        <Form form={form} preserve={false} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="转交对象"
            name="redirectPeople"
            rules={[{ required: true, message: '转交对象必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              userState="in-service"
              resourceParams={[{ resourceType: 'BUILDING', resourceCodes: [blockGuid] }]}
              allowClear
            />
          </Form.Item>

          <Form.Item label="转交原因" name="redirectReason">
            <Input.TextArea style={{ width: 428 }} maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
