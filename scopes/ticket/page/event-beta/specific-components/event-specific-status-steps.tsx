import dayjs from 'dayjs';
import React, { useEffect, useMemo } from 'react';
import { useLazyBusinessOrderApprovalDetail } from 'scopes/bpm/gql/client/approval';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Container } from '@manyun/base-ui.ui.container';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';
import type { EventBetaProcessStatusType } from '@manyun/ticket.model.event';
import { EventBetaProcessStatus } from '@manyun/ticket.model.event';

import { useEventData } from './event-data-context';

export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const stepsPrefixCls = `${prefixCls}-steps`;
    return css`
      // overrides steps's style
      .${stepsPrefixCls}-horizontal
        .${stepsPrefixCls}-item-content
        .${stepsPrefixCls}-item-description {
        max-width: none;
      }
    `;
  }}
`;
export type EventSpecificStatusStepsProps = {
  id: string;
  eventStatus?: string;
  processNo?: string | null;
  createTime?: number;
  refetchDetail: () => void;
};

export function EventSpecificStatusSteps({
  id,
  eventStatus,
  createTime,
  processNo,
  refetchDetail,
}: EventSpecificStatusStepsProps) {
  const [{ eventProcessEngineNodes }, { setEventProcessEngineNodes }] = useEventData();
  const [getApprovalDetail, { data: approvalData, refetch }] = useLazyBusinessOrderApprovalDetail();

  useEffect(() => {
    if (processNo && processNo !== '0') {
      getApprovalDetail({
        variables: {
          instId: processNo,
          permissionType: 'NEW_EVENT',
        },
      });
    }
  }, [id, processNo, getApprovalDetail]);

  useEffect(() => {
    if (id) {
      (function () {
        setEventProcessEngineNodes();
      })();
    }
  }, [id, setEventProcessEngineNodes]);
  const stepItems = useDeepCompareMemo(() => {
    const basicStepItems = [
      {
        title: '创建',
        code: 'CREATE',
        description: (
          <Typography.Text type="secondary">
            {dayjs(createTime).format('YYYY-MM-DD HH:mm')}
          </Typography.Text>
        ),
      },
      {
        title: '事件确认',
        code: EventBetaProcessStatus.Confirming,
        description: undefined,
      },
      {
        title: '事件缓解',
        code: EventBetaProcessStatus.Relieving,
        description: undefined,
      },
      {
        title: '事件结束',
        code: EventBetaProcessStatus.Finishing,
        description: undefined,
      },
      {
        title: '复盘',
        code: EventBetaProcessStatus.Reviewing,
        description: undefined,
      },
      {
        title: '评审',
        code: EventBetaProcessStatus.Auditing,
        description: (
          <>
            {approvalData?.businessOrderApprovalDetail && eventStatus && (
              <Space>
                <ApprovalRecordsDropdown
                  businessOrderInfo={{
                    type: 'EVENT_AUDIT_PROCESS',
                    taskNumber: id!,
                    status: eventStatus,
                    approvalPermissionType: 'NEW_EVENT',
                  }}
                />
                <AwaitOperationPeopleTag
                  bpmInstance={approvalData.businessOrderApprovalDetail as unknown as BpmInstance}
                />
              </Space>
            )}
          </>
        ),
      },
      {
        title: '关闭',
        code: EventBetaProcessStatus.Closed,
        description: undefined,
      },
    ];
    const isApprovalRevoked =
      eventProcessEngineNodes?.findIndex(item => item.status === 'EXECUTE') !==
      (eventProcessEngineNodes?.length ?? 0) - 1;
    const isReopen =
      eventProcessEngineNodes?.findIndex(item => item.status === 'EXECUTE') ===
      (eventProcessEngineNodes?.length ?? 0) - 3;
    const approvalEngineNodes = eventProcessEngineNodes.map(item => {
      const renderItem = basicStepItems.find(step => step.code === item.code)!;
      // const targetPhase = eventLife?.eventProgress?.find(
      //   progress => progress.eventPhase === item.code
      // );

      if (renderItem.code === EventBetaProcessStatus.Auditing) {
        return {
          ...renderItem,
          description:
            (item?.createTime && item.status === 'ALREADY_EXECUTE' && !isApprovalRevoked) ||
            (eventStatus as unknown as EventBetaProcessStatusType) ===
              EventBetaProcessStatus.Closed ? (
              <Space direction="vertical">
                {renderItem.description}
                <Typography.Text type="secondary">
                  {dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}
                </Typography.Text>
              </Space>
            ) : (
              renderItem.description
            ),
        };
      } else {
        return {
          ...renderItem,
          status:
            renderItem.code === EventBetaProcessStatus.Closed &&
            item.status === 'ALREADY_EXECUTE' &&
            !isApprovalRevoked
              ? ('finish' as 'finish')
              : undefined,
          description:
            item.createTime && item.status === 'ALREADY_EXECUTE' ? (
              <Space style={{ width: '100%' }} wrap>
                <Typography.Text type="secondary">
                  {!(isReopen && renderItem.code === EventBetaProcessStatus.Closed) &&
                    dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}
                </Typography.Text>
              </Space>
            ) : (
              renderItem.description
            ),
        };
      }
    });

    return [
      {
        title: '创建',
        code: 'CREATE',
        description: (
          <Typography.Text type="secondary">
            {dayjs(createTime).format('YYYY-MM-DD HH:mm')}
          </Typography.Text>
        ),
      },
      ...approvalEngineNodes,
    ];
  }, [
    approvalData?.businessOrderApprovalDetail,
    createTime,
    eventProcessEngineNodes,
    eventStatus,
    id,
  ]);
  const stepCurrent = useMemo(() => {
    const currentIndex = eventProcessEngineNodes?.findIndex(item => item.status === 'EXECUTE');
    if ((eventStatus as unknown as EventBetaProcessStatusType) !== EventBetaProcessStatus.Closed) {
      return (currentIndex ?? 0) + 1;
    } else {
      return 10;
    }
  }, [eventProcessEngineNodes, eventStatus]);
  return (
    <Theme prefixCls="manyun">
      <Container>
        <StyledContainer>
          <Steps current={stepCurrent} items={stepItems} />
        </StyledContainer>
        {approvalData?.businessOrderApprovalDetail &&
          (eventStatus as unknown as EventBetaProcessStatusType) ===
            EventBetaProcessStatus.Auditing && (
            <ApprovalOperationButtons
              baseInfo={approvalData.businessOrderApprovalDetail as unknown as BpmInstance}
              getDetail={() => {
                refetch();
                setTimeout(() => {
                  setEventProcessEngineNodes();
                  refetchDetail();
                }, 2000);
              }}
            />
          )}
      </Container>
    </Theme>
  );
}
