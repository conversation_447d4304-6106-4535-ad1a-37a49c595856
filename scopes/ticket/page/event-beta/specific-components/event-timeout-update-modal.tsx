import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { useConfirmEventOvertimeUpgrade } from '@manyun/ticket.gql.client.tickets';

export type EventTimeoutUpdateModalProps = {
  eventId: string;
  refetchDetail: () => void;
};

type FormValue = {
  upgraded: boolean;
  eventLevelCode: string;
  upgradeReason: string;
};
export function EventTimeoutUpdateModal({
  eventId,
  refetchDetail,
  // getIsDetectTimeout,
}: EventTimeoutUpdateModalProps) {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();
  const upgraded = Form.useWatch('upgraded', form);
  const [detectEventProblem, { loading }] = useConfirmEventOvertimeUpgrade({
    onCompleted(data) {
      if (data.confirmEventOvertimeUpgrade?.success) {
        message.success('事件超时升级确认已提交');
        refetchDetail && refetchDetail();
        setOpen(false);
      } else {
        message.error(data.confirmEventOvertimeUpgrade?.message);
      }
    },
  });

  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      detectEventProblem({
        variables: {
          query: {
            eventId,
            ...values,
          },
        },
      });
    });
  };
  return (
    <>
      <Button type="primary" onClick={showDrawer}>
        超时升级确认
      </Button>
      <Modal
        destroyOnClose
        title="事件超时升级确认"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              提交
            </Button>
          </Space>
        }
        onCancel={onClose}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <Alert
            message="目前事件超时未处理，请决定是否将事件提升一级处理？"
            type="warning"
            closable={false}
          />
          <Form
            // style={{ width: 560 }}
            form={form}
            preserve={false}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 19 }}
          >
            <Form.Item
              label="是否升级"
              name="upgraded"
              rules={[{ required: true, message: '是否升级必选' }]}
            >
              <Radio.Group
                options={[
                  { label: '升级', value: true },
                  { label: '不升级', value: false },
                ]}
                onChange={value => {
                  if (value) {
                    form.setFieldValue('upgradeReason', '事件超时待处理升级');
                  } else {
                    form.setFieldValue('upgradeReason', undefined);
                  }
                }}
              />
            </Form.Item>
            {upgraded && (
              <Form.Item
                label="升级为"
                name="eventLevelCode"
                rules={[{ required: true, message: '升级为必选' }]}
              >
                <MetaTypeSelect metaType={MetaType.N_EVENT_LEVEL} />
              </Form.Item>
            )}

            {upgraded && (
              <Form.Item
                label="升级原因"
                name="upgradeReason"
                rules={[{ required: true, message: '升级原因必填' }]}
              >
                <Input.TextArea style={{ width: 428 }} maxLength={300} />
              </Form.Item>
            )}
          </Form>
        </Space>
      </Modal>
    </>
  );
}
