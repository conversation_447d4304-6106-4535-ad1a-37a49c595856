import type { Moment } from 'moment';
import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { useDetectEventProblem } from '@manyun/ticket.gql.client.tickets';

export const detectStatusMap: Record<number, string> = {
  '0': '待查',
  '1': '已查明',
};
export type EventDetectModalProps = {
  eventId: string;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
  refetchDetail: () => void;
  onSuccess?: () => void;
};

type FormValue = {
  detectStatus: string;
  detectTime: Moment;
  detectReason: string;
};
export function EventDetectModal({
  eventId,
  getIsDetectTimeout,
  refetchDetail,
  onSuccess,
}: EventDetectModalProps) {
  const [detectStatus, setDetectStatus] = useState('0');
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();

  const [detectEventProblem, { loading }] = useDetectEventProblem({
    onCompleted(data) {
      if (data.detectEventProblem?.success) {
        message.success('事件定位提交成功');
        onSuccess && onSuccess();
        setOpen(false);
      } else {
        if (data.detectEventProblem?.code === 'STATUS_OVER_TIME') {
          setTimeout(() => {
            refetchDetail();
          }, 3000);
          setOpen(false);
          message.error('事件处理超时，请先操作超时升级确认');
          return;
        }
        message.error(data.detectEventProblem?.message);
      }
    },
  });

  const showDrawer = () => {
    const { isTimeout } = getIsDetectTimeout();
    if (isTimeout) {
      message.error('事件处理超时，请先操作超时升级确认');
      setTimeout(() => {
        refetchDetail();
      }, 3000);
      return;
    }
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      const { detectStatus, detectTime, detectReason } = values;

      detectEventProblem({
        variables: {
          query: {
            eventId: eventId,
            detectStatus: Number(detectStatus),
            detectTime: detectStatus === '1' ? detectTime.valueOf() : null,
            detectReason: detectStatus === '1' ? detectReason : null,
          },
        },
      });
    });
  };

  const disabledDate = (current: Moment) => {
    return current && current.valueOf() > moment().endOf('day').valueOf();
  };

  const disabledTime = (current: Moment | null) => {
    const now = moment();
    const hours = now.hours();
    const minute = now.minute();
    const seconds = now.seconds();

    const choseHour = moment(current).hours();
    const isToday = moment(current).isSame(moment(), 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => range(seconds + 1, 60),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
      };
    }
    return {};
  };

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };
  return (
    <>
      <Button type="primary" onClick={showDrawer}>
        事件定位
      </Button>
      <Modal
        destroyOnClose
        title="事件定位"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              提交
            </Button>
          </Space>
        }
        onCancel={onClose}
      >
        <Form
          style={{ width: 560 }}
          form={form}
          preserve={false}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            label="事件原因"
            name="detectStatus"
            rules={[{ required: true, message: '事件原因必选' }]}
            initialValue={detectStatus}
          >
            <Radio.Group
              options={Object.keys(detectStatusMap).map(value => ({
                label: detectStatusMap[value as unknown as number],
                value: value,
              }))}
              onChange={value => setDetectStatus(value.target.value)}
            />
          </Form.Item>
          <Form.Item
            label="定位时间"
            name="detectTime"
            rules={[
              {
                required: detectStatus === '1',
                validator(_, value) {
                  if (detectStatus === '1' && !value) {
                    return Promise.reject('定位时间必选');
                  }
                  return Promise.resolve();
                },
              },
            ]}
            hidden={detectStatus === '0'}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={disabledDate}
              disabledTime={disabledTime}
            />
          </Form.Item>
          <Form.Item
            label="定位原因"
            name="detectReason"
            rules={[
              {
                required: detectStatus === '1',
                validator(_, value) {
                  if (detectStatus === '1' && !value) {
                    return Promise.reject('定位原因必填');
                  }
                  return Promise.resolve();
                },
                whitespace: true,
              },
            ]}
            hidden={detectStatus === '0'}
          >
            <Input.TextArea style={{ width: 395 }} maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
