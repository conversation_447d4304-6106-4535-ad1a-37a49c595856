import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { useCheckEventCanAudit, useReplayEvent } from '@manyun/ticket.gql.client.tickets';
import type { EventBetaData } from '@manyun/ticket.gql.client.tickets';
import { generateRiskRegisterCreateLocation } from '@manyun/ticket.route.ticket-routes';

export type EventNotAbleToAuditModalButtonProps = {
  eventId: string;
  eventCenterInfo: EventBetaData;
  disabled?: boolean | null;
  setRelateRiskModalVisible: (visible: boolean) => void;
  onSuccess?: () => void;
  canNotSummary: () => string;
} & ButtonProps;

export function EventNotAbleToAuditModalButton({
  eventId,
  eventCenterInfo,
  disabled,
  setRelateRiskModalVisible,
  onSuccess,
  canNotSummary,
}: EventNotAbleToAuditModalButtonProps) {
  const [visibleSummeryPop, setVisibleSummeryPop] = useState(false);
  const [replayEvent, { loading }] = useReplayEvent({
    onCompleted(data) {
      if (!data.replayEvent?.success) {
        message.error(data.replayEvent?.message);
        return;
      }
      message.success('已提交评审');
      onSuccess && onSuccess();
    },
  });
  const [checkEventCanAudit] = useCheckEventCanAudit();

  return (
    <>
      <Popconfirm
        placement="topRight"
        title={canNotSummary()}
        open={visibleSummeryPop}
        okText="关联已有问题"
        cancelText="创建新问题"
        cancelButtonProps={{ type: 'primary' }}
        overlayInnerStyle={{
          width: 335,
        }}
        onCancel={() => {
          window.open(
            generateRiskRegisterCreateLocation({
              eventSourceNo: eventCenterInfo.eventId,
              eventSourceType: 'EVENT',
              spaceGuid: eventCenterInfo.blockGuid,
            }),
            '_blank'
          );
        }}
        onOpenChange={visible => {
          !visible && setVisibleSummeryPop(visible);
        }}
        onConfirm={() => {
          setVisibleSummeryPop(false);
          setRelateRiskModalVisible(true);
        }}
      >
        <Button
          loading={loading}
          disabled={disabled}
          onClick={async () => {
            if (canNotSummary()) {
              setVisibleSummeryPop(true);
              return;
            }

            if (isEventNotAbleToAudit(eventCenterInfo)) {
              message.error('事件信息不完整');
              return;
            }
            const { data } = await checkEventCanAudit({
              variables: {
                query: {
                  eventId,
                  eventVersion: 2,
                },
              },
            });
            if (
              data?.checkEventCanAudit?.code &&
              data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
            ) {
              message.error('需关闭所有关联风险单后，才可提交复盘或评审');
              return;
            }
            replayEvent({ variables: { query: { eventId } } });
          }}
        >
          无需复盘，提交评审
        </Button>
      </Popconfirm>
    </>
  );
}
export function isEventNotAbleToAudit(eventCenterInfo: EventBetaData) {
  const { eventLevelCode, categoryCode, eventTitle, eventDesc, faultType, faultModelList } =
    eventCenterInfo;
  let submitDisabled = true;

  if (
    eventLevelCode &&
    categoryCode &&
    eventTitle &&
    eventDesc &&
    faultType &&
    faultModelList?.length
  ) {
    submitDisabled = false;
  }
  return submitDisabled;
}
