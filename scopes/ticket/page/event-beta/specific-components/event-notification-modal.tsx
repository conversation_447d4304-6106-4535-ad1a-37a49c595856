import { uniqBy } from 'lodash';
import React from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Space } from '@manyun/base-ui.ui.space';
import { useSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import type {
  EventBetaData,
  EventProgressData,
  FaultModelInfo,
} from '@manyun/ticket.gql.client.tickets';
import { BackendFalseAlarm, FaultTargetType } from '@manyun/ticket.model.event';
import { NotificationModalButton } from '@manyun/ticket.ui.notification-modal-button';

import { useEventData } from './event-data-context';

export const SUBSCRIPTION_TYPE = {
  ALARM_SUBSCRIBE: 'ALARM_SUBSCRIBE',
  EVENT_SUBSCRIBE: 'EVENT_SUBSCRIBE',
};
export function processingSubscriptionLevel({
  text = '',
  type,
  cumulative = false,
}: {
  text: string;
  type: string;
  cumulative?: boolean;
}) {
  if (!text) {
    return text;
  }
  let levelText;
  if (typeof text === 'number') {
    levelText = text;
  } else {
    const index = text.lastIndexOf('_');
    if (index < 0) {
      return text;
    }
    levelText = text.substring(index + 1, text.length);
  }
  if (cumulative) {
    levelText = Number(levelText) + 1;
  }
  if (type === SUBSCRIPTION_TYPE.ALARM_SUBSCRIBE || type === SUBSCRIPTION_TYPE.EVENT_SUBSCRIBE) {
    return levelText.toString();
  }

  return levelText.toString();
}

export type EventNotificationModalProps = {
  eventCenterInfo: EventBetaData;
  eventProcessData: EventProgressData[];
  refetchDetail: () => void;
};

export function EventNotificationModal({
  eventCenterInfo,
  eventProcessData,
  refetchDetail,
}: EventNotificationModalProps) {
  const [{ notificationVisible }, { setNotificationVisible }] = useEventData();
  const [, { checkUserId }] = useAuthorized();

  useSpaces({
    variables: {
      idc: eventCenterInfo.idcTag,
      nodeTypes: ['IDC'],
    },
    fetchPolicy: 'network-only',
  });

  const [spaceIdc] = useSpace(eventCenterInfo.idcTag);

  let text = '';
  if (spaceIdc) {
    text += spaceIdc.label;
  }

  const notificationInfo = {
    eventId: eventCenterInfo.eventId,
    idcTag: eventCenterInfo.idcTag,
    blockTag: eventCenterInfo.blockGuid,
    eventLevel: eventCenterInfo.eventLevelName ?? '--',
    occurTime: eventCenterInfo.occurTime,
    eventDesc: eventCenterInfo.eventDesc ?? '--',
    eventTitle: eventCenterInfo?.eventTitle ?? '--',
    detectReason: eventCenterInfo?.detectStatus === 1 ? eventCenterInfo.detectReason : '待查',
    eventStatus: eventCenterInfo.eventStatus,
    eventSource: eventCenterInfo.eventSourceName,
    rooms: getRooms(
      eventCenterInfo.faultType as FaultTargetType,
      eventCenterInfo.faultModelList as unknown as FaultModelInfo[]
    ),
    faultType: eventCenterInfo.faultType,
    faultModelList: eventCenterInfo.faultModelList,
    influenceDesc: eventCenterInfo.influenceDesc,
    processRecord: eventProcessData,
    source: eventCenterInfo.eventSourceName,
    isOwner: eventCenterInfo?.inHandover
      ? checkUserId(eventCenterInfo?.handoverUserId)
      : checkUserId(eventCenterInfo?.ownerId),
    lastLevelChangeInfo: eventCenterInfo.lastLevelChangeInfo?.reason,
    isFalseAlarm: eventCenterInfo.falseAlarm !== BackendFalseAlarm.None,
    blockName: text,
    inHandover: eventCenterInfo.inHandover,
    refetchDetail: refetchDetail,
  };
  return (
    <Space>
      <NotificationModalButton
        type="event_beta"
        notificationInfo={{
          ...notificationInfo,
          creatorId: eventCenterInfo.createUserId,
          creatorName: eventCenterInfo.createUserName,
        }}
        notificationVisible={notificationVisible}
        setNotificationVisible={setNotificationVisible}
      />
    </Space>
  );
}
const getRooms = (type: FaultTargetType, causeDevices: FaultModelInfo[]) => {
  switch (type) {
    case FaultTargetType.Device:
      return uniqBy(causeDevices, 'roomTag')
        .map(device => `${device.roomTag}(${device.roomName})`)
        .join(' | ');
    case FaultTargetType.Room:
      return causeDevices.map(room => `${room.roomTag}(${room.name})`).join(' | ');
    case FaultTargetType.Other:
      return causeDevices.map(room => room.name).join(' | ');
    default:
      return '';
  }
};
