import React, { useMemo } from 'react';

// import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Card } from '@manyun/base-ui.ui.card';
import type {
  EventBetaData,
  EventProgressData,
  EvnetBetaRelateTicketsJson,
} from '@manyun/ticket.gql.client.tickets';

import { CheckingRecords } from './checking-records';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { EventSpecificProcessInfo } from './event-specific-process-info';
import { InfluenceSurfaceTab } from './influence-surface';
import MonitoringData from './monitoring-data';
import { OperationRecords } from './operation-records';
import { RelateTicketsTable } from './relate-tickets-table';
import { SourceRecordsTable } from './source-records-table';

export type EventTabsProps = {
  id: string;
  parentActiveKey: string;
  eventCenterInfo: EventBetaData;
  isDetectTimeout: {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
    sla?: number | null;
    startTime?: number | null;
    endTime?: number | null;
  };
  relateTickets: EvnetBetaRelateTicketsJson[];
  currentUser?: number;
  setRelateRiskModalVisible: (visible: boolean) => void;
  canNotSummary: () => string;
  setParentActiveKey: (key: string) => void;
  refetchDetail: (delayFetch?: boolean) => void;
  refetchGetEvnetBetaRelateTickets: () => void;
  setProcess: (data: EventProgressData[]) => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
  eventConfiguration: string[];
};

const tabList = [
  {
    key: 'eventProgress',
    tab: '事件进展',
  },

  {
    key: 'influenceSuface',
    tab: '影响范围',
  },
  {
    key: 'monitoringData',
    tab: '监控数据',
  },
  {
    key: 'sourceRecords',
    tab: '来源记录',
  },
  {
    key: 'checkingRecords',
    tab: '复盘记录',
  },
  {
    key: 'operationRecords',
    tab: '操作记录',
  },
];
export function EventTabs({
  id,
  parentActiveKey,
  eventCenterInfo,
  relateTickets,
  setParentActiveKey,
  refetchDetail,
  isDetectTimeout,
  currentUser,
  eventConfiguration,
  setRelateRiskModalVisible,
  setProcess,
  canNotSummary,
  getIsDetectTimeout,
}: EventTabsProps) {
  const contentList: Record<string, React.ReactNode> = useMemo(() => {
    if (eventCenterInfo?.blockGuid) {
      return {
        eventProgress: (
          <EventSpecificProcessInfo
            eventCenterInfo={eventCenterInfo}
            blockGuid={eventCenterInfo.blockGuid}
            isDetectTimeout={isDetectTimeout}
            refetchDetail={refetchDetail}
            currentUser={currentUser}
            setProcess={setProcess}
            getIsDetectTimeout={getIsDetectTimeout}
          />
        ),
        influenceSuface: <InfluenceSurfaceTab eventCenterInfo={eventCenterInfo} />,
        relateTickets: <RelateTicketsTable eventId={id} />,
        monitoringData: (
          <MonitoringData
            // @ts-ignore legacy component
            id={id}
            eventCenterInfo={eventCenterInfo}
          />
        ),

        checkingRecords: (
          <CheckingRecords
            id={id}
            eventCenterInfo={eventCenterInfo}
            eventConfiguration={eventConfiguration}
            setRelateRiskModalVisible={setRelateRiskModalVisible}
            canNotSummary={canNotSummary}
            refetchDetail={refetchDetail}
          />
        ),
        // @ts-ignore legacy component
        operationRecords: <OperationRecords id={id} eventCenterInfo={eventCenterInfo} />,
        sourceRecords: <SourceRecordsTable eventId={id} />,
      };
    }
    return {};
  }, [
    eventCenterInfo,
    id,
    isDetectTimeout,
    currentUser,
    eventConfiguration,
    canNotSummary,
    setProcess,
    refetchDetail,
    getIsDetectTimeout,
    setRelateRiskModalVisible,
  ]);

  const extraTabList = [
    {
      key: 'eventProgress',
      tab: '事件进展',
    },
    {
      key: 'relateTickets',
      tab: '关联事项',
    },
    {
      key: 'influenceSuface',
      tab: '影响范围',
    },
    {
      key: 'monitoringData',
      tab: '监控数据',
    },
    {
      key: 'sourceRecords',
      tab: '来源记录',
    },
    {
      key: 'checkingRecords',
      tab: '复盘记录',
    },
    {
      key: 'operationRecords',
      tab: '操作记录',
    },
  ];

  return (
    <Card
      tabList={relateTickets && relateTickets.length > 0 ? extraTabList : tabList}
      activeTabKey={parentActiveKey}
      style={{ width: '100%', height: '100%' }}
      onTabChange={key => {
        setParentActiveKey(key);
      }}
    >
      {contentList[parentActiveKey]}
    </Card>
  );
}
