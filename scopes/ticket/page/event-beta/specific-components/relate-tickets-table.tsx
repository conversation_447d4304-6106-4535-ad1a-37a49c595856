import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { message } from '@manyun/base-ui.ui.message';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyEvnetBetaRelateTickets } from '@manyun/ticket.gql.client.tickets';
import type { EvnetBetaRelateTicketsJson } from '@manyun/ticket.gql.client.tickets';
import { TaskStatusMap } from '@manyun/ticket.model.ticket';
import type { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import {
  generateRiskRegisterDetailLocation,
  generateTicketLocation,
} from '@manyun/ticket.route.ticket-routes';

const relateBizTypeMap: Record<string, string> = {
  RISK_REGISTER: '风险',
  REPAIR: '维修',
};

const riskRegisterMap: Record<string, string> = {
  DRAFT: '草稿',
  WAITING_IDENTIFY: '待识别',
  WAITING_EVALUATE: '待评估',
  HANDLING: '处理中',
  APPROVING: '审批中',
  CLOSED: '关闭',
};
export type RelateTicketsTableProps = {
  eventId: string;
} & Omit<TableProps<EvnetBetaRelateTicketsJson>, 'rowKey' | 'loading' | 'dataSource' | 'columns'>;
const columns: Array<ColumnType<EvnetBetaRelateTicketsJson>> = [
  {
    title: '类型',
    dataIndex: 'relateBizType',
    key: 'relateBizType',
    width: 88,
    render: (_, { relateBizType }) => relateBizTypeMap[relateBizType],
  },
  {
    title: 'ID',
    dataIndex: 'relateBizId',
    key: 'relateBizId',
    width: 180,
    render: (_, { relateBizId, relateBizType }) => (
      <Link
        to={
          relateBizType === 'REPAIR'
            ? generateTicketLocation({
                id: relateBizId,
                ticketType: relateBizType.toLowerCase(),
              })
            : generateRiskRegisterDetailLocation({ id: relateBizId })
        }
        target="_blank"
      >
        {relateBizId}
      </Link>
    ),
  },
  {
    title: '事项描述',
    dataIndex: 'relateBizDesc',
    key: 'relateBizDesc',
    render: (_, { relateBizDesc }) => (
      <Typography.Text ellipsis={{ tooltip: relateBizDesc }}>{relateBizDesc}</Typography.Text>
    ),
  },
  {
    title: '状态',
    dataIndex: 'relateBizStatus',
    key: 'relateBizStatus',
    width: 80,
    render: (_, { relateBizStatus, relateBizType }) => {
      if (relateBizType === 'RISK_REGISTER') {
        return riskRegisterMap[relateBizStatus];
      }
      if (relateBizType === 'REPAIR') {
        return TaskStatusMap[relateBizStatus as BackendTaskStatus];
      }
      return '--';
    },
  },
  {
    title: '创建人',
    width: 80,
    dataIndex: 'relateBizCreateUserId',
    key: 'relateBizCreateUserId',
    render: (_, { relateBizCreateUserId }) => (
      <UserLink userId={Number(relateBizCreateUserId)} external />
    ),
  },
  {
    title: '创建时间',
    width: 184,
    dataIndex: 'relateBizCreateTime',
    key: 'relateBizCreateTime',
    sorter: true,
    render: relateBizCreateTime => {
      return moment(relateBizCreateTime).format('YYYY-MM-DD HH:mm:ss');
    },
  },
];
export function RelateTicketsTable({ eventId }: RelateTicketsTableProps) {
  const [getRelateTickets] = useLazyEvnetBetaRelateTickets();
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
  }>({ pageNum: 1, pageSize: 10 });
  const [total, setTotal] = useState(0);
  const [dataSource, setDatasource] = useState<EvnetBetaRelateTicketsJson[]>([]);
  const [sortOrder, setSortOrder] = useState('desc');

  useEffect(() => {
    (async function () {
      setLoading(true);
      const { data, error } = await getRelateTickets({
        variables: {
          query: {
            eventId,
            sortField: 'gmt_create',
            sortOrder: sortOrder,
            pageNum: pagination.pageNum,
            pageSize: pagination.pageSize,
          },
        },
      });
      setLoading(false);
      if (error) {
        message.error(error.message);

        return;
      }
      setDatasource(data?.evnetBetaRelateTickets?.data ?? []);
      setTotal(data?.evnetBetaRelateTickets?.total ?? 0);
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortOrder, pagination.pageNum, pagination.pageSize]);

  return (
    <Table
      rowKey={record => record.relateBizId}
      loading={loading}
      columns={columns}
      scroll={{ y: 400 }}
      dataSource={dataSource}
      pagination={{ pageSize: pagination.pageSize, current: pagination.pageNum, total: total }}
      onChange={(pagination, _, sorter, { action }) => {
        const { order } = sorter as { field: string; order: string };
        setSortOrder(order === 'ascend' ? 'asc' : 'desc');
        setPagination({ pageNum: pagination.current!, pageSize: pagination.pageSize! });
      }}
    />
  );
}
