import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { useLazyUserResources } from '@manyun/iam.gql.client.iam';
import { getDeviceTypesAction } from '@manyun/resource-hub.state.device-types';
import {
  type EventBetaData,
  type EventProgressData,
  useLazyEventCurrentDutyLeader,
} from '@manyun/ticket.gql.client.tickets';
import {
  BackendFalseAlarm,
  EventBetaProcessStatus,
  FAULT_TARGET_TYPE_TEXT,
  FalseAlarmMap,
} from '@manyun/ticket.model.event';
import type { FaultTargetType } from '@manyun/ticket.model.event';
import { CreateRiskDrawerYg } from '@manyun/ticket.page.event-detail';
import { FaultTargetLink } from '@manyun/ticket.page.events-beta';
import {
  generateChangeTicketDetail,
  generateRiskRegisterCreateLocation,
  generateRiskRegisterDetailLocation,
} from '@manyun/ticket.route.ticket-routes';
import { CreateRepairTicketModal } from '@manyun/ticket.ui.create-repair-ticket-modal';

import { EventEditDrawer, initializeFaultTarget } from './event-edit-drawer';
import { EventLevelUpdateModal } from './event-level-update-modal';
import { EventNotificationModal } from './event-notification-modal';
import RelateIssueModal from './relate-question-modal';
import { RiskTicketCreateButton } from './risk-ticket-create-button';

export type EventInfoType = {
  id: string;
  eventCenterInfo: EventBetaData;
  eventProcessData: EventProgressData[];
  refetchDetail: () => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
};

export function EventInfo({
  id,
  eventCenterInfo,
  eventProcessData,
  refetchDetail,
  getIsDetectTimeout,
}: EventInfoType) {
  const { userId } = useSelector(selectMe);
  const [relateRiskModalVisible, setRelateRiskModalVisible] = useState(false);

  const [, { checkCode, checkUserId }] = useAuthorized();
  const [configUtil] = useConfigUtil();
  const [eventCurrentDutyLeader, { data }] = useLazyEventCurrentDutyLeader();
  const [getUserResources, { data: userResourcesData }] = useLazyUserResources();
  useEffect(() => {
    if (userId) {
      getUserResources({
        variables: { params: { userId } },
      });
    }
  }, [getUserResources, userId]);

  useEffect(() => {
    if (eventCenterInfo?.blockGuid) {
      eventCurrentDutyLeader({
        variables: {
          query: {
            blockGuid: eventCenterInfo.blockGuid,
            currentTime: dayjs().valueOf(),
          },
        },
      });
    }
  }, [eventCurrentDutyLeader, eventCenterInfo?.blockGuid, eventCenterInfo?.eventId]);

  const eventCenterInfoDutyLeader = data?.eventCurrentDutyLeader?.data;
  const isDutyLeader = eventCenterInfoDutyLeader?.id
    ? checkUserId(eventCenterInfoDutyLeader?.id)
    : false;
  const isEditLevel = checkCode('element_event-level-edit');
  const isOwner = eventCenterInfo?.inHandover
    ? checkUserId(eventCenterInfo?.handoverUserId)
    : checkUserId(eventCenterInfo?.ownerId);
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getDeviceTypesAction());
  }, [dispatch]);

  const { eventStatus } = eventCenterInfo;

  const status = eventStatus ? eventStatus : null;

  // 有资源权限可查看
  const showEventNotificationModal =
    (userResourcesData?.userResources &&
      userResourcesData?.userResources?.some(item => item.code === eventCenterInfo.blockGuid)) ||
    (status &&
      [
        EventBetaProcessStatus.Confirming,
        EventBetaProcessStatus.Relieving,
        EventBetaProcessStatus.Finishing,
      ].includes(status as EventBetaProcessStatus) &&
      isOwner);

  const generateCardExtra = () => {
    return (
      <Space>
        {showEventNotificationModal && (
          <EventNotificationModal
            eventCenterInfo={eventCenterInfo}
            refetchDetail={refetchDetail}
            eventProcessData={eventProcessData}
          />
        )}

        {[
          EventBetaProcessStatus.Confirming,
          EventBetaProcessStatus.Relieving,
          EventBetaProcessStatus.Finishing,
        ].includes(status as EventBetaProcessStatus) &&
          isOwner && (
            <Dropdown
              overlayStyle={{ zIndex: 1000 }}
              trigger={['click']}
              menu={{
                items: [
                  {
                    label: (
                      <CreateRepairTicketModal
                        type="text"
                        compact
                        eventBetaId={eventCenterInfo.eventId}
                        eventId={eventCenterInfo.eventId}
                        eventDesc={eventCenterInfo.eventDesc}
                        causeType={eventCenterInfo.faultType}
                        causeObject={
                          initializeFaultTarget(
                            eventCenterInfo.faultType as FaultTargetType,
                            eventCenterInfo.faultModelList ?? []
                          ) as { label: string; key: string }[]
                        }
                        blockGuid={eventCenterInfo.blockGuid}
                      />
                    ),
                    type: 'group',
                  },
                  {
                    label:
                      riskRegisters.features?.baseInfo !== 'full' ? (
                        <RiskTicketCreateButton
                          type="text"
                          compact
                          relateEvent={{
                            id: eventCenterInfo.eventId,
                            description: eventCenterInfo.eventDesc,
                            blockGuid: eventCenterInfo.blockGuid,
                          }}
                        />
                      ) : (
                        <CreateRiskDrawerYg
                          type="text"
                          compact
                          relateEvent={{
                            id: eventCenterInfo.eventId,
                            description: eventCenterInfo.eventDesc,
                            blockGuid: eventCenterInfo.blockGuid,
                          }}
                        />
                      ),
                    type: 'group',
                  },
                ],
              }}
            >
              <Button>创建关联工单</Button>
            </Dropdown>
          )}
        {[EventBetaProcessStatus.Reviewing].includes(status as EventBetaProcessStatus) &&
          (isOwner || isDutyLeader) &&
          !eventCenterInfo.convertToProblem && (
            <Dropdown
              overlayStyle={{ zIndex: 1000 }}
              trigger={['click']}
              menu={{
                items: [
                  {
                    label: (
                      <Button
                        type="text"
                        onClick={() =>
                          window.open(
                            generateRiskRegisterCreateLocation({
                              eventSourceNo: eventCenterInfo.eventId,
                              eventSourceType: 'EVENT',
                              spaceGuid: eventCenterInfo.blockGuid,
                            }),
                            '_blank'
                          )
                        }
                      >
                        创建新问题
                      </Button>
                    ),
                    type: 'group',
                  },
                  {
                    label: (
                      <Button type="text" onClick={() => setRelateRiskModalVisible(true)}>
                        关联已有问题
                      </Button>
                    ),
                    type: 'group',
                  },
                ],
              }}
            >
              <Button>转为问题</Button>
            </Dropdown>
          )}
      </Space>
    );
  };
  const generateChangeContent = () => {
    return eventCenterInfo.changeCause && eventCenterInfo.changeId ? (
      <>
        是<Divider type="vertical" style={{ marginLeft: 4, marginRight: 4 }} /> 变更ID：
        <Link target="_blank" to={generateChangeTicketDetail({ id: eventCenterInfo.changeId })}>
          {eventCenterInfo.changeId}
        </Link>
      </>
    ) : (
      <Space>
        <Badge status="default" />
        <Typography.Text>否</Typography.Text>
      </Space>
    );
  };
  const generateQuestionContent = () => {
    return eventCenterInfo.convertToProblem && eventCenterInfo.questionRiskId ? (
      <>
        <Badge status="error" style={{ marginRight: 4 }} /> 是
        <Divider type="vertical" style={{ marginLeft: 4, marginRight: 4 }} /> 问题ID：
        <Link
          target="_blank"
          to={generateRiskRegisterDetailLocation({ id: eventCenterInfo.questionRiskId })}
        >
          {eventCenterInfo.questionRiskId}
        </Link>
      </>
    ) : (
      <Space>
        <Badge status="default" />
        <Typography.Text>否</Typography.Text>
      </Space>
    );
  };
  // const generateConvertProlemContent = () => {
  //   if (
  //     ![EventBetaProcessStatus.Auditing, EventBetaProcessStatus.Closed].includes(
  //       eventCenterInfo.eventStatus as EventBetaProcessStatusType
  //     )
  //   ) {
  //     return '--';
  //   }
  //   if (
  //     eventCenterInfo.convertToProblem === null ||
  //     eventCenterInfo.convertToProblem === undefined
  //   ) {
  //     return '--';
  //   }
  //   if (eventCenterInfo.convertToProblem) {
  //     return (
  //       <Space>
  //         <Badge status="error" />
  //         <Typography.Text>是</Typography.Text>
  //       </Space>
  //     );
  //   }
  //   if (!eventCenterInfo.convertToProblem) {
  //     return (
  //       <Space>
  //         <Badge status="default" />
  //         <Typography.Text>否</Typography.Text>
  //       </Space>
  //     );
  //   }
  // };

  return (
    <Card
      title={
        <Space direction="horizontal">
          <Typography.Title showBadge level={5} style={{ marginBottom: 0 }}>
            事件信息
          </Typography.Title>
          {isOwner &&
            [
              EventBetaProcessStatus.Confirming,
              EventBetaProcessStatus.Relieving,
              EventBetaProcessStatus.Finishing,
            ].includes(status as EventBetaProcessStatus) && (
              <EventEditDrawer
                isEditLevel={isEditLevel}
                eventId={id}
                eventCenterInfo={eventCenterInfo}
                refetchDetail={refetchDetail}
              />
            )}
        </Space>
      }
      headStyle={{ borderBottom: 0 }}
      bodyStyle={{ padding: '0 24px' }}
      bordered={false}
      style={{ width: '100%' }}
      extra={generateCardExtra()}
    >
      <Space direction="vertical">
        <Typography.Title level={5} style={{ marginBottom: 0 }}>
          {eventCenterInfo.eventTitle}
        </Typography.Title>
        <Descriptions column={4}>
          <Descriptions.Item label="事件等级">
            <Space direction="horizontal">
              {eventCenterInfo.eventLevelName}
              {!isOwner &&
                isEditLevel &&
                EventBetaProcessStatus.Confirming === status &&
                !eventCenterInfo.levelChangeInfo?.processId && (
                  <EventLevelUpdateModal
                    eventId={id}
                    showIcon
                    refetchDetail={() => {
                      refetchDetail();
                    }}
                    getIsDetectTimeout={getIsDetectTimeout}
                  />
                )}
              {eventCenterInfo.levelChangeInfo?.processId && (
                <Popover
                  title="事件等级校正审批"
                  content={
                    <Space style={{ width: 236 }} direction="vertical">
                      <Typography.Text>
                        审批ID：
                        <Link
                          target="_blank"
                          to={generateBPMRoutePath({
                            id: eventCenterInfo.levelChangeInfo.processId,
                          })}
                        >
                          {eventCenterInfo.levelChangeInfo.processId}
                        </Link>
                      </Typography.Text>
                      <Typography.Text>
                        原事件等级：
                        {eventCenterInfo.levelChangeInfo.originLevelName}
                      </Typography.Text>
                      <Typography.Text>
                        校正后等级：
                        {eventCenterInfo.levelChangeInfo.targetLevelName}
                      </Typography.Text>
                    </Space>
                  }
                >
                  <Tag color="processing">等级校正审批中</Tag>
                </Popover>
              )}
            </Space>
          </Descriptions.Item>
          <Descriptions.Item label="位置">{eventCenterInfo.blockGuid}</Descriptions.Item>
          <Descriptions.Item label="是否误报">
            <Typography.Text
              type={eventCenterInfo.falseAlarm === BackendFalseAlarm.None ? undefined : 'danger'}
            >
              {FalseAlarmMap[eventCenterInfo.falseAlarm as BackendFalseAlarm]}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="事件来源">
            {' '}
            {eventCenterInfo.eventSourceName || '--'}
          </Descriptions.Item>
          <Descriptions.Item label="专业分类">
            {eventCenterInfo.categoryName || '--'}
          </Descriptions.Item>
          <Descriptions.Item label="事件发生时间">
            {eventCenterInfo.occurTime
              ? dayjs(eventCenterInfo.occurTime).format('YYYY-MM-DD HH:mm:ss')
              : '--'}
          </Descriptions.Item>
          <Descriptions.Item
            label="事件描述"
            span={2}
            contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
          >
            <Typography.Text ellipsis={{ tooltip: eventCenterInfo.eventDesc }}>
              {eventCenterInfo.eventDesc || '--'}
            </Typography.Text>
          </Descriptions.Item>
          <Descriptions.Item label="目标类型">
            {FAULT_TARGET_TYPE_TEXT[eventCenterInfo.faultType as FaultTargetType] || '--'}
          </Descriptions.Item>
          <Descriptions.Item label="目标名称" contentStyle={{ overflow: 'hidden' }}>
            {eventCenterInfo.faultModelList?.length ? (
              <FaultTargetLink
                type={eventCenterInfo.faultType as FaultTargetType}
                causeDevices={eventCenterInfo.faultModelList ?? []}
              />
            ) : (
              '--'
            )}
          </Descriptions.Item>
          <Descriptions.Item label="变更导致">{generateChangeContent()}</Descriptions.Item>
          <Descriptions.Item label="是否转为问题">{generateQuestionContent()}</Descriptions.Item>
          {/* <Descriptions.Item label="是否转为问题">
            {generateConvertProlemContent()}
          </Descriptions.Item> */}
          <Descriptions.Item label="创建人">
            <UserLink
              userId={eventCenterInfo.createUserId}
              userName={eventCenterInfo.createUserName}
              external
            />
          </Descriptions.Item>
          <Descriptions.Item label="线上处理人">
            <UserLink
              userId={eventCenterInfo.ownerId}
              userName={eventCenterInfo.ownerName}
              external
            />
          </Descriptions.Item>
          <Descriptions.Item label="转交工程师">
            <UserLink userId={eventCenterInfo.handoverUserId} external />
            {eventCenterInfo.inHandover ? (
              <Tag style={{ marginLeft: 8 }} color="warning">
                待办结
              </Tag>
            ) : null}
          </Descriptions.Item>

          <Descriptions.Item label="附件">
            {Array.isArray(eventCenterInfo.fileInfoList) &&
              eventCenterInfo.fileInfoList.length > 0 && (
                <SimpleFileList files={eventCenterInfo.fileInfoList}>
                  <Button type="link" compact>
                    查看
                  </Button>
                </SimpleFileList>
              )}
          </Descriptions.Item>
          <Descriptions.Item
            label="影响范围"
            span={4}
            contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
          >
            <Typography.Text ellipsis={{ tooltip: eventCenterInfo.influenceDesc }}>
              {eventCenterInfo.influenceDesc || '--'}
            </Typography.Text>
          </Descriptions.Item>
        </Descriptions>
      </Space>
      <RelateIssueModal
        visible={relateRiskModalVisible}
        eventId={eventCenterInfo.eventId}
        blockGuid={eventCenterInfo.blockGuid}
        idcTag={eventCenterInfo.idcTag}
        onCancle={() => {
          setRelateRiskModalVisible(false);
        }}
        onSuccess={() => {
          refetchDetail();
          setRelateRiskModalVisible(false);
        }}
      />
    </Card>
  );
}
