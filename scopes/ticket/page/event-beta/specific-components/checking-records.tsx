import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import React, { useEffect, useMemo } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import {
  type EventBetaData,
  useCheckEventCanAudit,
  useReplayEvent,
} from '@manyun/ticket.gql.client.tickets';
import { EventBetaProcessStatus } from '@manyun/ticket.model.event';
import { generateRiskRegisterCreateLocation } from '@manyun/ticket.route.ticket-routes';

import {
  EventNotAbleToAuditModalButton,
  isEventNotAbleToAudit,
} from './event-not-able-to-audit-modal-button';

export type CheckingRecordsProps = {
  id: string;
  eventCenterInfo: EventBetaData;
  eventConfiguration: string[];
  canNotSummary: () => string;
  setRelateRiskModalVisible: (visible: boolean) => void;
  refetchDetail: (refetchDetail?: boolean) => void;
};

const formItemLayout = {
  labelCol: { flex: ' 0 0 108px' },
};

export function CheckingRecords({
  id,
  eventCenterInfo,
  eventConfiguration,
  canNotSummary,
  setRelateRiskModalVisible,
  refetchDetail,
}: CheckingRecordsProps) {
  const [visibleSummery, setVisibleSummery] = React.useState(false);
  const [replayEvent, { loading }] = useReplayEvent({
    onCompleted(data) {
      if (!data.replayEvent?.success) {
        message.error(data.replayEvent?.message);
        return;
      }
      refetchDetail(true);
    },
  });
  const [checkEventCanAudit, { data }] = useCheckEventCanAudit();
  const [form] = Form.useForm();
  const submitDisabled = useMemo(() => {
    return isEventNotAbleToAudit(eventCenterInfo);
  }, [eventCenterInfo]);

  useEffect(() => {
    checkEventCanAudit({
      variables: {
        query: {
          eventId: id!,
          eventVersion: 2,
        },
      },
    });
  }, [checkEventCanAudit, id]);

  const [, { checkUserId }] = useAuthorized();
  const isOwner = eventCenterInfo?.inHandover
    ? checkUserId(eventCenterInfo?.handoverUserId)
    : checkUserId(eventCenterInfo?.ownerId);
  const { eventStatus } = eventCenterInfo;

  const onFinish = () => {
    form.validateFields().then(async formValue => {
      replayEvent({
        variables: {
          query: {
            eventId: id,
            fileInfoList: formValue.fileInfoList?.map(obj => ({
              ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
            })),
            auditDesc: formValue.desc,
            convertToProblem: formValue.convertToProblem,
          },
        },
      });
    });
  };

  const isEditable = eventStatus === EventBetaProcessStatus.Reviewing && isOwner;

  return (
    <>
      {isEditable ? (
        <Form
          form={form}
          // onFinish={onFinish}
          {...formItemLayout}
          initialValues={{
            desc: eventCenterInfo.auditDesc,
            fileInfoList: Array.isArray(eventCenterInfo?.auditFileInfoList)
              ? eventCenterInfo.auditFileInfoList.map(file => McUploadFile.fromJSON(file))
              : [],
            convertToProblem: eventCenterInfo.convertToProblem,
          }}
        >
          {data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED' ? (
            <Space style={{ marginBottom: 24 }}>
              <ExclamationCircleFilled style={{ color: `var(--${prefixCls}-warning-color)` }} />
              <Typography.Text>需关闭所有关联风险单后，才可提交复盘或评审</Typography.Text>
            </Space>
          ) : null}
          {/* <Form.Item
            name="convertToProblem"
            label="是否转为问题"
            rules={[
              {
                required: true,
                message: '是否转为问题必选！',
              },
            ]}
          >
            <Radio.Group>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item> */}
          <Form.Item
            name="desc"
            label="复盘内容"
            rules={[
              {
                max: 1000,
                message: '最多输入 1000 个字符！',
              },
            ]}
          >
            <Input.TextArea
              placeholder="请输入事件复盘总结"
              disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}
              style={{ width: 800 }}
              autoSize={{ minRows: 2, maxRows: 6 }}
            />
          </Form.Item>
          <Form.Item
            name="fileInfoList"
            label="上传文档"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
            rules={[{ required: true, message: '请上传文档' }]}
          >
            {isEditable ? (
              <Upload
                key="upload"
                maxFileSize={20}
                disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}
              >
                <Button disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}>
                  上传
                </Button>
              </Upload>
            ) : (
              Array.isArray(eventCenterInfo.auditFileInfoList) &&
              eventCenterInfo.auditFileInfoList.length > 0 && (
                <SimpleFileList
                  files={eventCenterInfo.auditFileInfoList.map(file => McUploadFile.fromJSON(file))}
                >
                  <Button type="link" compact>
                    查看
                  </Button>
                </SimpleFileList>
              )
            )}
          </Form.Item>
          {isEditable && (
            <Form.Item
              labelCol={{ flex: ' 0 0 108px' }}
              label=" "
              colon={false}
              extra={
                submitDisabled ? (
                  <Typography.Text type="danger">
                    请填写「事件信息」中全部必填项后，再提交复盘
                  </Typography.Text>
                ) : null
              }
            >
              <Space>
                <Popconfirm
                  placement="topRight"
                  title={canNotSummary()}
                  open={visibleSummery}
                  okText="关联已有问题"
                  cancelText="创建新问题"
                  overlayInnerStyle={{
                    width: 335,
                  }}
                  cancelButtonProps={{ type: 'primary' }}
                  onCancel={() => {
                    window.open(
                      generateRiskRegisterCreateLocation({
                        eventSourceNo: eventCenterInfo.eventId,
                        eventSourceType: 'EVENT',
                        spaceGuid: eventCenterInfo.blockGuid,
                      }),
                      '_blank'
                    );
                  }}
                  onOpenChange={visible => {
                    !visible && setVisibleSummery(visible);
                  }}
                  onConfirm={() => {
                    setRelateRiskModalVisible(true);
                    setVisibleSummery(false);
                  }}
                >
                  <Button
                    type="primary"
                    // htmlType="submit"
                    loading={loading}
                    disabled={
                      submitDisabled ||
                      data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
                    }
                    onClick={() => {
                      if (canNotSummary()) {
                        setVisibleSummery(true);
                        return;
                      }
                      onFinish();
                    }}
                  >
                    提交复盘
                  </Button>
                </Popconfirm>

                {(
                  eventConfiguration.length
                    ? eventConfiguration.includes(eventCenterInfo.eventLevelCode)
                    : false
                ) ? (
                  <EventNotAbleToAuditModalButton
                    eventCenterInfo={eventCenterInfo}
                    eventId={id}
                    disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}
                    canNotSummary={canNotSummary}
                    setRelateRiskModalVisible={setRelateRiskModalVisible}
                    onSuccess={() => refetchDetail(true)}
                  />
                ) : null}
                {/* <Button
                  loading={loading}
                  disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}
                  onClick={() => {
                    if (isEventNotAbleToAudit(eventCenterInfo)) {
                      message.error('事件信息不完整');
                      return;
                    }
                    replayEvent({ variables: { query: { eventId: id } } });
                  }}
                >
                  无需复盘，提交评审
                </Button> */}
              </Space>
            </Form.Item>
          )}
        </Form>
      ) : (
        <Descriptions>
          <Descriptions.Item label="复盘内容" span={3}>
            {eventCenterInfo.auditDesc ?? '--'}
          </Descriptions.Item>
          <Descriptions.Item label="上传文档" span={3}>
            {Array.isArray(eventCenterInfo.auditFileInfoList) &&
            eventCenterInfo.auditFileInfoList.length > 0 ? (
              <SimpleFileList
                files={eventCenterInfo.auditFileInfoList.map(file => McUploadFile.fromJSON(file))}
              >
                <Button type="link" compact>
                  查看
                </Button>
              </SimpleFileList>
            ) : (
              '--'
            )}
          </Descriptions.Item>
        </Descriptions>
      )}
    </>
  );
}
