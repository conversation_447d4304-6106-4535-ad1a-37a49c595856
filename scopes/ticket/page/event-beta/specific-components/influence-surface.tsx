import React from 'react';
import { useSelector } from 'react-redux';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { EventBetaData } from '@manyun/ticket.gql.client.tickets';
import { FaultTargetType } from '@manyun/ticket.model.event';

import { InfluenceSurface } from '@manyun/dc-brain.legacy.components';

export function InfluenceSurfaceTab({ eventCenterInfo }: { eventCenterInfo: EventBetaData }) {
  // const eventCenterInfo = useSelector(selectEventDetail);
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const {
    events: { showRacksImpacts, features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureIsEventConfigWithProcessEngineRequired =
    features.isEventConfigWithProcessEngine === 'required';

  return (
    <InfluenceSurface
      idcTag={eventCenterInfo.idcTag}
      blockTag={
        eventCenterInfo.blockGuid &&
        eventCenterInfo.blockGuid.substring(eventCenterInfo.blockGuid.indexOf('.') + 1)
      }
      targetId={eventCenterInfo.eventId}
      featureIsEventConfigWithProcessEngineRequired={featureIsEventConfigWithProcessEngineRequired}
      eventNo={eventCenterInfo.eventId ?? ''}
      targetType="EVENT"
      showRacksImpacts={showRacksImpacts}
      tabBarStyle={{ borderBottom: 0 }}
      deviceGuids={generateCauseDevices(
        eventCenterInfo.faultType as FaultTargetType,
        eventCenterInfo.faultModelList ?? []
      )}
    />
  );
}
const generateCauseDevices = (type: FaultTargetType, causeDevices: { guid?: string | null }[]) => {
  if (type === FaultTargetType.Device) {
    return causeDevices.map((device: { guid: string }) => device.guid).join(',');
  }
  return null;
};
