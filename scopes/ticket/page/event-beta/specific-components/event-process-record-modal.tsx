import type { Moment } from 'moment';
import moment from 'moment';
import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { useAddEventProgressProcessingRecords } from '@manyun/ticket.gql.client.tickets';
import { EventSparePartChange } from '@manyun/ticket.model.event';

export type EventProcessRecordModalProps = {
  eventInfo: { eventId: string; eventPhase: string };
  blockGuid: string;
  currentUser?: number;
  onSuccess?: () => void;
};

type FormValue = {
  handlePeople: number;
  handleTime: Moment;
  handleContent: string;
};
export function EventProcessRecordModal({
  eventInfo,
  blockGuid,
  currentUser,
  onSuccess,
}: EventProcessRecordModalProps) {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();

  const [createEventProcessRecord, { loading }] = useAddEventProgressProcessingRecords({
    onCompleted(data) {
      if (data.addEventProgressProcessingRecords?.success) {
        message.success('已添加处理记录');
        onSuccess && onSuccess();
        setOpen(false);
      } else {
        message.error(data.addEventProgressProcessingRecords?.message);
      }
    },
  });

  const showDrawer = () => {
    form.setFieldValue('handlePeople', currentUser);
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      const { handlePeople, handleTime, handleContent } = values;

      createEventProcessRecord({
        variables: {
          query: {
            eventId: eventInfo.eventId,
            phase: eventInfo.eventPhase,
            handlerId: handlePeople,
            handleTime: handleTime.valueOf(),
            handleContent,
          },
        },
      });
    });
  };

  const disabledDate = (current: Moment) => {
    return current && current.valueOf() > moment().endOf('day').valueOf();
  };

  const disabledTime = (current: Moment | null) => {
    const now = moment();
    const hours = now.hours();
    const minute = now.minute();
    const seconds = now.seconds();
    const choseHour = moment(current).hours();

    const isToday = moment(current).isSame(moment(), 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => range(seconds + 1, 60),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
      };
    }
    return {};
  };

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  return (
    <>
      <Button type="link" compact onClick={showDrawer}>
        添加处理记录
      </Button>
      <Modal
        destroyOnClose
        title="添加处理记录"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              确定
            </Button>
          </Space>
        }
        onCancel={onClose}
      >
        <Form
          style={{ width: 560 }}
          form={form}
          preserve={false}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
          initialValues={{ sparePartChange: EventSparePartChange.None }}
        >
          <Form.Item
            label="处理人"
            name="handlePeople"
            rules={[{ required: true, message: '处理人必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              userState="in-service"
              resourceParams={[{ resourceType: 'BUILDING', resourceCodes: [blockGuid] }]}
              allowClear
              labelInValue={false}
            />
          </Form.Item>
          <Form.Item
            label="处理时间"
            name="handleTime"
            rules={[{ required: true, message: '处理时间必选' }]}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={disabledDate}
              disabledTime={disabledTime}
            />
          </Form.Item>
          <Form.Item
            label="处理内容"
            name="handleContent"
            rules={[{ required: true, message: '处理内容必填' }]}
          >
            <Input.TextArea style={{ width: 395 }} maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
