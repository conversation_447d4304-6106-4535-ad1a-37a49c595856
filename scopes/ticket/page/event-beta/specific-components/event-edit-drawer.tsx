import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import {
  type EventBetaData,
  type FaultModelInfo,
  useUpdateEventBeta,
} from '@manyun/ticket.gql.client.tickets';
import {
  BackendFalseAlarm,
  EventBetaProcessStatus,
  FaultTargetType,
} from '@manyun/ticket.model.event';
import type { FieldName, FormValues } from '@manyun/ticket.ui.event-mutator-beta';
import { EventMutatorBeta, formatCauseDevicesParams } from '@manyun/ticket.ui.event-mutator-beta';

import { useEventData } from './event-data-context';

export type EventEditDrawerProps = {
  eventId: string;
  eventCenterInfo: EventBetaData;
  isEditLevel: boolean;
  refetchDetail: () => void;
};
export function EventEditDrawer({
  eventId,
  eventCenterInfo,
  isEditLevel,
  refetchDetail,
}: EventEditDrawerProps) {
  const [{ parentForm, drawerVisible }, { setDrawerVisible }] = useEventData();

  const [eventLevel, setEventLevel] = useState<string | null | undefined>('');
  const [update, { loading }] = useUpdateEventBeta();
  const [form] = Form.useForm<FormValues>(parentForm);

  const onSave = () => {
    form.validateFields().then(async formValue => {
      const {
        location,
        changeCause,
        changeId,
        occurTime,
        faultGuidList,
        eventLevelCode,
        eventSourceCode,
        ownerId,
        fileInfoList,
        influenceDesc,
        eventTitle,
        faultType,
        ...rest
      } = formValue;

      const { idc, block } = getSpaceGuidMap(location!);
      const svcQuery = {
        ...rest,
        fileInfoList: fileInfoList?.map(obj => ({
          ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
        })),
        changeCause,
        ownerId: ownerId,
        eventSourceCode: eventSourceCode!,
        eventLevelCode: eventLevelCode!,
        blockGuid: `${idc}.${block}`,
        idcTag: idc!,
        faultGuidList: formatCauseDevicesParams(faultGuidList!) as string[],
        faultType: faultType!,
        occurTime: occurTime ? occurTime.valueOf() : undefined,
        changeId: changeCause ? changeId : '',
        influenceDesc,
        eventTitle: eventTitle!,
      };

      update({
        variables: {
          query: {
            eventId,
            ...svcQuery,
          },
        },
        onCompleted(data) {
          if (data.updateEventBeta?.success) {
            message.success('编辑成功');
            setDrawerVisible(false);
            refetchDetail();
          } else {
            message.error(data.updateEventBeta?.message);
          }
        },
      });
    });
  };
  const showModal = () => {
    form.setFieldsValue({
      eventLevelCode: eventCenterInfo?.eventLevelCode,
      location: eventCenterInfo?.blockGuid,
      falseAlarm:
        eventCenterInfo?.falseAlarm &&
        [
          BackendFalseAlarm.None,
          BackendFalseAlarm.FalseAlarm,
          BackendFalseAlarm.FalseBill,
        ].includes(eventCenterInfo.falseAlarm)
          ? eventCenterInfo.falseAlarm
          : BackendFalseAlarm.None,
      eventSourceCode: eventCenterInfo?.eventSourceCode,
      occurTime: eventCenterInfo?.occurTime ? moment(eventCenterInfo.occurTime) : undefined,

      ownerId: eventCenterInfo?.ownerId,
      eventDesc: eventCenterInfo?.eventDesc,
      fileInfoList:
        (eventCenterInfo?.fileInfoList ?? []).map(file => McUploadFile.fromJSON(file)) ?? [],
      changeCause: eventCenterInfo?.changeCause ?? false,
      changeId: eventCenterInfo?.changeId,
      faultGuidList: initializeFaultTarget(
        eventCenterInfo?.faultType as FaultTargetType,
        eventCenterInfo?.faultModelList ?? []
      ),
      faultType: eventCenterInfo?.faultType as FaultTargetType,
      eventTitle: eventCenterInfo?.eventTitle,
      influenceDesc: eventCenterInfo?.influenceDesc,
      categoryCode: eventCenterInfo.categoryCode,
    });
    setDrawerVisible(true);
  };
  const closeModal = () => {
    setDrawerVisible(false);
  };

  const disabledItem = (): FieldName[] => {
    if (EventBetaProcessStatus.Confirming !== eventCenterInfo.eventStatus) {
      return ['eventLevelCode', 'falseAlarm'];
    }
    if (!isEditLevel) {
      return ['eventLevelCode'];
    }
    if (eventCenterInfo.levelChangeInfo?.processId) {
      return ['eventLevelCode'];
    }
    return [];
  };

  return (
    <>
      <Button type="link" onClick={showModal}>
        编辑事件信息
      </Button>
      <Drawer
        title="编辑事件信息"
        size="large"
        open={drawerVisible}
        forceRender
        extra={
          <Space>
            <Button onClick={closeModal}>取消</Button>
            {eventCenterInfo.eventLevelCode &&
            eventLevel &&
            eventLevel !== eventCenterInfo.eventLevelCode ? (
              <Popconfirm
                title="您修改了事件等级，将触发审批流，审批通过后事件等级将自动更新，审批拒绝则事件等级保持不变。"
                style={{ width: 332 }}
                okText="确认修改"
                // placement="topRight"
                onConfirm={onSave}
              >
                <Button type="primary">确认</Button>
              </Popconfirm>
            ) : (
              <Button loading={loading} type="primary" onClick={onSave}>
                确认
              </Button>
            )}
          </Space>
        }
        bodyStyle={{ padding: 0 }}
        onClose={() => {
          closeModal();
          form.resetFields();
        }}
      >
        <EventMutatorBeta
          externalForm={form}
          mode="edit"
          showFooter={false}
          disabledFormItems={disabledItem()}
          onValuesChange={value => {
            if ('eventLevelCode' in value) {
              setEventLevel(value.eventLevelCode);
            }
          }}
        />
      </Drawer>
    </>
  );
}

export const initializeFaultTarget = (type: FaultTargetType, causeDevices: FaultModelInfo[]) => {
  switch (type) {
    case FaultTargetType.Device:
      return causeDevices.map(item => ({
        ...item,
        deviceGuid: item.guid,
        deviceName: item.name,
        key: item.guid,
        label: item.name,
      }));
    case FaultTargetType.Room:
      return causeDevices.map(item => ({
        label: `${item.roomTag}(${item.name})`,
        key: item.guid,
      }));
    case FaultTargetType.Other:
      return causeDevices[0]?.name!;
    default:
      return undefined;
  }
};
