import { Button, Input, Modal, Space, Table, message } from 'antd';
import React, { useEffect, useMemo, useState } from 'react';
import { Link } from 'react-router-dom';

import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import MetatypeSelect from '@manyun/resource-hub.ui.metatype-select';
import type {
  RiskRegisterBaseInfoResponse,
  RiskStatus,
} from '@manyun/ticket.gql.client.risk-register';
import { useEventRelateRiskQuestion } from '@manyun/ticket.gql.client.tickets';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { riskStatusColorMaps } from '@manyun/ticket.page.risk-registers';
import { generateRiskRegisterDetailLocation } from '@manyun/ticket.route.ticket-routes';
import { fetchPagedRiskRegisters } from '@manyun/ticket.service.fetch-paged-risk-registers';
import type { SvcRespData } from '@manyun/ticket.service.fetch-paged-risk-registers';

const { Search } = Input;

export default function RelateIssueModal({
  visible,
  eventId,
  blockGuid,
  idcTag,
  onCancle,
  onSuccess,
}: {
  visible: boolean;
  eventId: string;
  blockGuid: string;
  idcTag: string;
  onCancle: () => void;
  onSuccess: () => void;
}) {
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [tableLoading, setTableLoading] = useState(false);

  const [data, setData] = useState<SvcRespData>({
    data: [],
    total: 0,
  });
  const [fields, setFields] = useState<{
    riskKey?: string;
    riskTopTypeCode?: string;
    pageNum: number;
    pageSize: number;
  }>({
    pageNum: 1,
    pageSize: 10,
  });

  const locales = useMemo(() => getRiskRegisterLocales(), []);
  const [eventRelateRiskQuestion, { loading }] = useEventRelateRiskQuestion({
    onCompleted(data) {
      if (data?.eventRelateRiskQuestion?.success) {
        message.success('关联成功');
        onSuccess();
      } else {
        message.error(data?.eventRelateRiskQuestion?.message);
      }
    },
  });
  const getList = async () => {
    setTableLoading(true);
    const { error, data: resData } = await fetchPagedRiskRegisters({
      ...fields,
      blockGuids: [idcTag, blockGuid],
      riskResourceCode: 'EVENT_QUESTION',
      riskStatus: ['WAITING_EVALUATE', 'HANDLING', 'APPROVING'] as RiskStatus[],
    });
    setTableLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(resData);
  };

  useEffect(() => {
    getList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, blockGuid, idcTag]);

  const onClose = () => {
    setSelectedRowKeys([]);
    setFields({ pageNum: 1, pageSize: 10 });
    onCancle();
  };

  // 初始化加载
  useEffect(() => {
    if (visible) {
      getList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible]);

  // 表格列定义
  const columns: ColumnType<RiskRegisterBaseInfoResponse>[] = [
    {
      title: '风险ID',
      dataIndex: 'id',
      render: (_, { id }) => {
        if (id) {
          return (
            <Link
              target="_blank"
              to={generateRiskRegisterDetailLocation({ id: encodeURIComponent(id) })}
            >
              {id}
            </Link>
          );
        }
        return '--';
      },
    },
    {
      title: '位置',
      dataIndex: 'blockGuid',
      render: (_, { idcTag, blockGuid }) => (blockGuid ? blockGuid : (idcTag ?? '--')),
    },
    {
      title: '类别',
      dataIndex: 'riskCategoryCode',
      render: text =>
        text ? <MetaTypeText code={text} metaType={MetaType.RISK_TOP_TYPE} /> : '--',
    },
    {
      title: '问题描述',
      dataIndex: 'riskDesc',
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (_, { riskStatus }) => {
        return riskStatus ? (
          <Tag color={riskStatusColorMaps[riskStatus]}>{locales.riskStatus.enum[riskStatus]}</Tag>
        ) : (
          '--'
        );
      },
    },
  ];
  const onConfirm = () => {
    if (selectedRowKeys.length === 0) {
      message.error('请至少选择一个问题单');
      return;
    }
    eventRelateRiskQuestion({
      variables: { query: { riskId: selectedRowKeys.join(''), eventId } },
    });
  };

  return (
    <Modal
      title="关联已有问题"
      open={visible}
      footer={[
        <Button
          key="confirm"
          type="primary"
          loading={loading}
          disabled={selectedRowKeys.length === 0}
          onClick={() => onConfirm(selectedRowKeys)}
        >
          确定
        </Button>,
      ]}
      width={1080}
      onCancel={onClose}
    >
      <Space style={{ marginBottom: 16, flexDirection: 'row' }}>
        <Typography.Text>类别</Typography.Text>
        <MetatypeSelect
          metaType={MetaType.RISK_TOP_TYPE}
          allowClear
          style={{ width: 216, marginRight: 16 }}
          value={fields.riskTopTypeCode}
          onChange={(value: string) => {
            setFields(prev => ({ ...prev, riskTopTypeCode: value, pageNum: 1 }));
          }}
        />
        <Typography.Text>ID/描述</Typography.Text>
        <Search
          style={{ width: 200 }}
          // value={fields.riskKey}
          onSearch={value => {
            setFields(prev => ({ ...prev, riskKey: value, pageNum: 1 }));
          }}
        />
      </Space>
      <Table
        rowSelection={{
          type: 'radio',
          selectedRowKeys,
          onChange: keys => setSelectedRowKeys(keys),
        }}
        columns={columns}
        dataSource={data.data}
        loading={tableLoading}
        pagination={{
          total: data.total,
          current: fields.pageNum,
          pageSize: fields.pageSize,
          onChange: (page, pageSize) => {
            setFields(pre => ({
              ...pre,
              pageNum: page!,
              pageSize: pageSize!,
            }));
          },
        }}
        rowKey="id"
      />
    </Modal>
  );
}
