import Form from '@ant-design/compatible/es/form';
import { ApiSelect } from '@galiojs/awesome-antd';
import _ from 'lodash';
import React, { Component } from 'react';
import { connect } from 'react-redux';
import styled from 'styled-components';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { Alarm } from '@manyun/monitoring.model.alarm';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import type { BackendEventStatusCode } from '@manyun/ticket.model.event';
import { EventStatusValueMap } from '@manyun/ticket.model.event';

import { AlarmTable, AssetClassificationApiTreeSelect } from '@manyun/dc-brain.legacy.components';
import * as alarmScreenService from '@manyun/dc-brain.legacy.services/alarmScreenService';
import {
  fetchAlarmRemoveAssociateEvent,
  fetchEventAlarmQueryCount,
} from '@manyun/dc-brain.legacy.services/eventCenterService';
import { getObjectOwnProps } from '@manyun/dc-brain.legacy.utils';

// import { useEventData } from './event-data-context';

export function CancelAssociateEvent({ record, onCancelAssociateEvent, status }) {
  return (
    <DeleteConfirm
      variant="popconfirm"
      title={`确认取消关联： ${record.id}吗？`}
      onOk={() => onCancelAssociateEvent(record)}
    >
      <Button
        type="link"
        disabled={status === EventStatusValueMap.CLOSED}
        style={{ padding: 0, height: 'auto' }}
      >
        取消关联
      </Button>
    </DeleteConfirm>
  );
}

class MonitoringDataInfo extends Component {
  state = {
    alarmQueryCount: {},
    dataViewList: [
      {
        text: '总告警数',
        key: 'totalCount',
      },
      {
        text: '恢复告警数',
        key: 'RECOVER',
      },
      {
        text: '正在告警数',
        key: 'TRIGGER',
      },
    ],
    alarmList: [],
    total: 0,
    pageNum: 1,
    pageSize: 10,
  };

  componentDidMount() {
    this.props.syncCommonData({ strategy: { deviceCategory: 'IF_NULL' } });
    this.eventAlarmQueryCount();
    this.eventAlarmQueryList(1, 10);
  }

  eventAlarmQueryCount = async () => {
    const { response } = await fetchEventAlarmQueryCount({
      targetId: this.props.id,
      idcTag: this.props.eventCenterInfo.idcTag,
      blockTag: this.props.eventCenterInfo.blockGuid?.substring(
        this.props.eventCenterInfo.blockGuid.indexOf('.') + 1
      ),
    });
    if (response) {
      this.setState({ alarmQueryCount: response });
    }
  };

  eventAlarmQueryList = async (pageNum, pageSize) => {
    const params = this.getParams();
    const { data, error } = await fetchAlarms({
      ...params,
      pageNum,
      pageSize,
      eventId: this.props.id,
      isQueryData: true,
      status: 'REMOVED',
      isMerge: true,
      idcTag: this.props.eventCenterInfo.idcTag,
    });
    if (!error) {
      this.setState({
        alarmList: data.data.map(Alarm.toApiObject),
        total: data.total,
        pageNum,
        pageSize,
      });
    } else {
      message.error(error.message || '请求通报列表失败');
    }
  };
  onChangePage = (pageNum, pageSize) => {
    this.eventAlarmQueryList(pageNum, pageSize);
    this.eventAlarmQueryCount();
  };

  // getOptions = list => {
  //   const element = list.map(item => <Option key={item.metaCode}>{item.metaName}</Option>);
  //   return element;
  // };

  handleReset = () => {
    this.props.form.resetFields();
    this.eventAlarmQueryList(1, 10);
  };

  handleSearch = () => {
    this.eventAlarmQueryCount();
    this.eventAlarmQueryList(1, 10);
  };

  getParams = () => {
    const data = this.props.form.getFieldsValue();
    const params = _.omit(data, 'constructTime', 'triggerTime', 'deviceName');
    if (data.constructTime) {
      params.alarmCreateTimeStart = Number(data.constructTime.unix() + '000');
    }
    if (data.triggerTime) {
      params.triggerTime = Number(data.triggerTime.unix() + '000');
    }
    if (data.deviceName) {
      params.deviceName = data.deviceName.trim();
    }
    const p = _.omitBy(params, (value, key) => {
      if (value === null || value === undefined || value === '') {
        return key;
      }
    });
    return p;
  };

  onCancelAssociateEvent = async record => {
    const { response, error } = await fetchAlarmRemoveAssociateEvent({
      alarmId: record.id,
      eventId: this.props.id,
      idcTag: this.props.eventCenterInfo.idcTag,
      blockTag: this.props.eventCenterInfo.blockGuid?.substring(
        this.props.eventCenterInfo.blockGuid.indexOf('.') + 1
      ),
    });
    if (response) {
      this.eventAlarmQueryCount();
      this.eventAlarmQueryList(1, 10);
    }
    if (error) {
      message.error(error);
    }
  };

  render() {
    const { form, nomalizedDeviceCategory, eventCenterInfo } = this.props;
    const { eventStatus = {} } = eventCenterInfo;
    const status = eventStatus
      ? EventStatusValueMap[eventStatus.code as BackendEventStatusCode]
      : undefined;

    const { alarmQueryCount, dataViewList, alarmList, total, pageSize, pageNum } = this.state;
    const { getFieldDecorator } = form;
    const formItemLayout = {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
    };
    return (
      <Card bodyStyle={{ height: '100%' }}>
        <Space style={{ width: '100%', height: '100%', display: 'flex' }} direction="vertical">
          <OverView list={dataViewList} count={alarmQueryCount} />
          <Divider />
          <Form {...formItemLayout}>
            <Row>
              <Col xl={12} xxl={8} md={24}>
                <Form.Item label="资产分类">
                  {getFieldDecorator('deviceTypeList')(
                    <AssetClassificationApiTreeSelect
                      dataType={['space', 'snDevice']}
                      category="categorycode"
                      disabledDepths={[0, 1]}
                      requestOnDidMount
                      allowClear
                      multiple
                      maxTagCount={1}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col xl={12} xxl={8} md={24}>
                <Form.Item label="告警状态">
                  {getFieldDecorator('triggerStatus')(
                    <ApiSelect
                      showSearch
                      style={{ width: 200 }}
                      allowClear
                      mode="multiple"
                      fieldNames={{ label: 'label', value: 'value' }}
                      dataService={async () => {
                        const { response } = await alarmScreenService.fetchTriggerStatus();
                        if (response) {
                          return Promise.resolve(getObjectOwnProps(response));
                        } else {
                          return Promise.resolve([]);
                        }
                      }}
                    />
                  )}
                </Form.Item>
              </Col>
              <Col xl={12} xxl={8} md={24}>
                <Form.Item label="设备名称">
                  {getFieldDecorator('deviceName')(<Input allowClear style={{ width: 200 }} />)}
                </Form.Item>
              </Col>
              <Col xl={12} xxl={8} md={24}>
                <Form.Item label="告警开始时间">
                  {getFieldDecorator('constructTime')(
                    <DatePicker format="YY-MM-DD HH:mm:ss" showTime />
                  )}
                </Form.Item>
              </Col>
              <Col xl={12} xxl={8} md={24}>
                <Form.Item label="最新告警时间">
                  {getFieldDecorator('triggerTime')(
                    <DatePicker format="YY-MM-DD HH:mm:ss" showTime />
                  )}
                </Form.Item>
              </Col>
              <Col xl={12} xxl={8} md={24} style={{ textAlign: 'right' }}>
                <Space>
                  <Button type="primary" onClick={this.handleSearch}>
                    搜索
                  </Button>
                  <Button onClick={this.handleReset}>重置</Button>
                </Space>
              </Col>
            </Row>
          </Form>
          <AlarmTable
            dataSource={alarmList}
            showColumns={[
              'id',
              { dataIndex: 'deviceType', visible: true },
              'deviceName',
              'gmtCreate',
              'triggerTime',
              'pointValue',
              'pointCodeName',
              'triggerStatus',
            ]}
            operation={{
              title: '操作',
              dataIndex: 'operation',
              fixed: 'right',
              render: (_, record) => (
                <CancelAssociateEvent
                  record={record}
                  status={status}
                  onCancelAssociateEvent={this.onCancelAssociateEvent}
                />
              ),
            }}
            pagination={{
              total: total,
              current: pageNum,
              onChange: this.onChangePage,
              pageSize: pageSize,
            }}
            metaCategoryEntities={nomalizedDeviceCategory}
          />
        </Space>
      </Card>
    );
  }
}

export const Wrapper = styled.div`
  display: flex;
  flex: 1;
  align-items: center;
`;

export const RightBorder = styled.div`
  width: 1px;
  height: 32px;
  background-color: var(--color-reference);
`;

export const ContentWrapper = styled.div`
  width: calc(100% - 1px);
  justify-content: center;
  display: flex;
`;

export function OverView({ list = [], count = {} }) {
  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        height: 100,
      }}
    >
      {list.map((item, index) => (
        <Wrapper key={index}>
          <ContentWrapper>
            <div>
              <div style={{ fontSize: 28 }}>{count[item.key]}</div>
              <div>{item.text}</div>
            </div>
          </ContentWrapper>
          {index + 1 !== list.length && <RightBorder />}
        </Wrapper>
      ))}
    </div>
  );
}

const mapStateToProps = ({ common: { deviceCategory } }) => ({
  nomalizedDeviceCategory: deviceCategory ? deviceCategory.normalizedList : {},
  deviceCategory,
});
const mapDispatchToProps = {
  syncCommonData: syncCommonDataAction,
};

const MonitoringForm = connect(
  mapStateToProps,
  mapDispatchToProps
)(Form.create({ name: 'event_alarm_list' })(MonitoringDataInfo));

export default function MonitoringData(props) {
  return (
    <MonitoringForm
      // pdfTabList={pdfTabList}
      // setPdfTabList={setPdfTabList}
      // tabsExpandAll={tabsExpandAll}
      {...props}
    />
  );
}
