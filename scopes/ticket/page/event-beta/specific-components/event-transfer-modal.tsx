import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { useHandoverEvent } from '@manyun/ticket.gql.client.tickets';

export type EventTransferModalProps = {
  eventId: string;
  blockGuid: string;
  refetchDetail: () => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
};

type FormValue = {
  targetUserId: number;
  reason: string;
};
export function EventTransferModal({
  eventId,
  blockGuid,
  refetchDetail,
  getIsDetectTimeout,
}: EventTransferModalProps) {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();

  const [confirmEvent, { loading }] = useHandoverEvent({
    onCompleted(data) {
      if (data.handoverEvent?.success) {
        message.success('事件已转交');
        refetchDetail && refetchDetail();
        setOpen(false);
      } else {
        if (data.handoverEvent?.code === 'STATUS_OVER_TIME') {
          setTimeout(() => {
            refetchDetail();
          }, 3000);
          setOpen(false);
          message.error('事件处理超时，请先操作超时升级确认');
          return;
        }
        message.error(data.handoverEvent?.message);
      }
    },
  });

  const showDrawer = () => {
    const { isTimeout } = getIsDetectTimeout();
    if (isTimeout) {
      message.error('事件处理超时，请先操作超时升级确认');
      setTimeout(() => {
        refetchDetail();
      }, 3000);
      return;
    }
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      const { ...resp } = values;

      confirmEvent({
        variables: {
          query: {
            eventId,
            ...resp,
          },
        },
      });
    });
  };
  return (
    <>
      <Button onClick={showDrawer}>转交</Button>
      <Modal
        destroyOnClose
        title="转交"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              提交
            </Button>
          </Space>
        }
        onCancel={onClose}
      >
        <Form
          style={{ width: 560 }}
          form={form}
          preserve={false}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            label="转交对象"
            name="targetUserId"
            rules={[{ required: true, message: '转交对象必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              userState="in-service"
              resourceParams={[{ resourceType: 'BUILDING', resourceCodes: [blockGuid] }]}
              allowClear
              labelInValue={false}
              includeCurrentUser={false}
            />
          </Form.Item>
          <Form.Item label="转交原因" name="reason">
            <Input.TextArea style={{ width: 428 }} maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
