import type { Moment } from 'moment';
import moment from 'moment';
import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { useFinishedEvent, useLazyEventReports } from '@manyun/ticket.gql.client.tickets';
import type { EvnetBetaRelateTicketsJson } from '@manyun/ticket.gql.client.tickets';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';

import { useEventData } from './event-data-context';

export type EventFinishModalProps = {
  eventId: string;
  blockGuid: string;
  detectStatus?: number | null;
  relateList: EvnetBetaRelateTicketsJson[];
  currentUser?: number;
  refetchDetail: () => void;
  getIsDetectTimeout: () => {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
  };
};

type FormValue = {
  /**
   * 处理人id
   */
  handlerId: number;
  /**
   * 开始时间 毫秒级时间戳
   */
  startTime: number;
  /**
   * 完成时间 毫秒级时间戳
   */
  endTime: number;
  /**
   * 处理结果
   */
  handleContent: string;
};
export function EventFinishModal({
  eventId,
  blockGuid,
  detectStatus,
  relateList,
  currentUser,
  refetchDetail,
  getIsDetectTimeout,
}: EventFinishModalProps) {
  const [open, setOpen] = useState(false);
  const [noFinishedReports, setNoFinishedReports] = useState<
    'notification' | 'detect' | 'repair' | null
  >(null);
  const [, { setNotificationVisible }] = useEventData();

  const [form] = Form.useForm<FormValue>();
  const [getEventReports, { loading: eventReportsLoading }] = useLazyEventReports();
  const [finishedEvent, { loading }] = useFinishedEvent({
    onCompleted(data) {
      if (data.finishedEvent?.success) {
        message.success('事件已结束');
        refetchDetail && refetchDetail();
        setOpen(false);
      } else {
        if (data.finishedEvent?.code === 'STATUS_OVER_TIME') {
          setTimeout(() => {
            refetchDetail();
          }, 3000);
          setOpen(false);
          message.error('事件处理超时，请先操作超时升级确认');
          return;
        }
        message.error(data.finishedEvent?.message);
      }
    },
  });

  const showDrawer = async () => {
    const { isTimeout } = getIsDetectTimeout();
    if (isTimeout) {
      message.error('事件处理超时，请先操作超时升级确认');
      setTimeout(() => {
        refetchDetail();
      }, 3000);
      return;
    }
    if (detectStatus !== 1) {
      setNoFinishedReports('detect');
      return;
    }

    const { data } = await getEventReports({
      variables: { query: { eventId, pageNum: 1, pageSize: 200 } },
    });
    if (!data?.eventReports.data.filter(item => item.reportType === 'FIN').length) {
      setNoFinishedReports('notification');
      return;
    }

    if (
      relateList.filter(
        item =>
          item.relateBizType === 'REPAIR' &&
          ![BackendTaskStatus.FAILURE, BackendTaskStatus.FINISH].includes(item.relateBizStatus)
      ).length
    ) {
      setNoFinishedReports('repair');
      return;
    }

    form.setFieldValue('handlerId', currentUser);
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  const onConfirm = () => {
    form.validateFields().then(values => {
      const { startTime, endTime, ...resp } = values;

      finishedEvent({
        variables: {
          query: {
            eventId,
            ...resp,
            startTime: startTime.valueOf(),
            endTime: endTime.valueOf(),
          },
        },
      });
    });
  };

  return (
    <>
      {noFinishedReports ? (
        <>
          {noFinishedReports === 'detect' && (
            <Popconfirm
              title="事件定位填写后，才可关闭事件单"
              style={{ width: 290 }}
              okText="我知道了"
              showCancel={false}
              placement="topRight"
              open
              onCancel={() => setNoFinishedReports(null)}
              onConfirm={() => setNoFinishedReports(null)}
            >
              <Button type="primary">确认事件结束</Button>
            </Popconfirm>
          )}
          {noFinishedReports === 'notification' && (
            <Popconfirm
              title="请先发送结束通报，再操作事件结束"
              style={{ width: 290 }}
              okText="发送通报"
              showCancel={false}
              placement="topRight"
              open
              onConfirm={() => {
                setNoFinishedReports(null);
                setNotificationVisible(true);
              }}
            >
              <Button type="primary">确认事件结束</Button>
            </Popconfirm>
          )}
          {noFinishedReports === 'repair' && (
            <Popconfirm
              title="关联维修单关闭后，才可关闭事件单"
              style={{ width: 290 }}
              okText="我知道了"
              showCancel={false}
              open
              placement="topRight"
              onCancel={() => setNoFinishedReports(null)}
              onConfirm={() => setNoFinishedReports(null)}
            >
              <Button type="primary">确认事件结束</Button>
            </Popconfirm>
          )}
        </>
      ) : (
        <Button type="primary" loading={eventReportsLoading} onClick={showDrawer}>
          确认事件结束
        </Button>
      )}
      <Modal
        destroyOnClose
        title="确认事件结束"
        width={560}
        open={open}
        footer={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              提交
            </Button>
          </Space>
        }
        onCancel={onClose}
      >
        <Form
          style={{ width: 560 }}
          form={form}
          preserve={false}
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          <Form.Item
            label="处理人"
            name="handlerId"
            rules={[{ required: true, message: '处理人必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              userState="in-service"
              resourceParams={[{ resourceType: 'BUILDING', resourceCodes: [blockGuid] }]}
              allowClear
              labelInValue={false}
            />
          </Form.Item>
          <Form.Item
            label="开始时间"
            name="startTime"
            rules={[{ required: true, message: '开始时间必选' }]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={handleEndDisabledDate}
              disabledTime={disabledDateTimeAfterToday}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            label="完成时间"
            name="endTime"
            dependencies={['startTime']}
            rules={[
              { required: true, message: '完成时间必选' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (value) {
                    if (moment(value).diff(moment(getFieldValue('startTime')), 'seconds') <= 0) {
                      return Promise.reject(new Error('完成时间不可早于开始时间'));
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={handleEndDisabledDate}
              disabledTime={disabledDateTimeAfterToday}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            label="处理结果"
            name="handleContent"
            rules={[{ required: true, message: '处理结果必填' }]}
          >
            <Input.TextArea style={{ width: 428 }} maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

function handleEndDisabledDate(current: Moment) {
  return current.valueOf() > moment().endOf('day').valueOf();
}
function range(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export function disabledDateTimeAfterToday(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (current) {
    const choseHour = current.hours();
    const choseMinute = current.minute();
    const isToday = current.isSame(compareMoment, 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => (choseMinute === minute ? range(second, 60) : []),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}
