import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileList } from '@manyun/base-ui.ui.file-list';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useAppIdentity } from '@manyun/iam.gql.client.iam';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { useLazyDrillOrderApproval } from '@manyun/ticket.gql.client.drill-order';
import {
  type DrillOrderApprovalNode,
  type EventProgressData,
  useCheckEventCanAudit,
  useConcludeEvnet,
  useLazyEventBeta,
  useLazyEventConfiguration,
  useLazyEvnetBetaRelateTickets,
} from '@manyun/ticket.gql.client.tickets';
import {
  EventBetaProcessStatus,
  type EventBetaProcessStatusType,
  type ProcessEngineConfig,
} from '@manyun/ticket.model.event';
import { generateRiskRegisterCreateLocation } from '@manyun/ticket.route.ticket-routes';

import { EventConfirmModal } from './specific-components/event-confirm-modal';
import { EventDataContext } from './specific-components/event-data-context';
import { EventFinishModal } from './specific-components/event-finished-modal';
import { EventInfo } from './specific-components/event-info';
import { EventLevelUpdateModal } from './specific-components/event-level-update-modal';
import { EventNotAbleToAuditModalButton } from './specific-components/event-not-able-to-audit-modal-button';
import { EventRelieveModal } from './specific-components/event-relieve-modal';
import { EventSpecificStatusSteps } from './specific-components/event-specific-status-steps';
import { EventTabs } from './specific-components/event-tabs';
import { EventTimeoutUpdateModal } from './specific-components/event-timeout-update-modal';
import { EventTransferModal } from './specific-components/event-transfer-modal';
import RelateIssueModal from './specific-components/relate-question-modal';

export function EventBeta() {
  // const configUtil = new ConfigUtil(config);
  // const eventCenterInfo = useSelector(selectEventDetail);
  const [processConfig, setProcessConfig] = useState<ProcessEngineConfig | undefined>();
  const [processData, setProcess] = useState<EventProgressData[]>([]);
  const [visibleSummery, setVisibleSummery] = useState(false);
  const [relateRiskModalVisible, setRelateRiskModalVisible] = useState(false);
  const [, { checkUserId }] = useAuthorized();
  const [getEventInfo, { data, loading: eventDetailLoading, refetch: refetchDetail }] =
    useLazyEventBeta({
      onCompleted(data) {
        setLoading(false);
        if (data.eventBeta.message) {
          message.error(data.eventBeta.message);
        }
      },
    });
  // 查询关联工单
  const [
    getEvnetBetaRelateTickets,
    { data: relateTickets, refetch: refetchGetEvnetBetaRelateTickets },
  ] = useLazyEvnetBetaRelateTickets();
  // 转交人确认办结
  const [concludeEvnet, { loading: concludeEvnetLoading }] = useConcludeEvnet({
    onCompleted(data) {
      if (!data.concludeEvnet?.success) {
        message.error(data.concludeEvnet?.message);
        return;
      }
      refetch();
    },
  });
  // 获取事件配置
  const [getEventConfiguration, { data: eventConfiguration }] = useLazyEventConfiguration({});

  const [checkEventCanAudit] = useCheckEventCanAudit();
  const { data: eventCenterInfo } = data?.eventBeta || {};
  const isOwner = eventCenterInfo?.inHandover
    ? checkUserId(eventCenterInfo?.handoverUserId)
    : checkUserId(eventCenterInfo?.ownerId);

  const [getDrillOrderProgress] = useLazyDrillOrderApproval();
  const { id } = useParams<{ id: string }>();
  const [tabKey, setTabKey] = useState<string>('eventProgress');
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const [notificationVisible, setNotificationVisible] = useState(false);
  const [processEngineNodes, setProcessEngineNodes] = useState<DrillOrderApprovalNode[]>([]);
  const [form] = Form.useForm();
  const { data: appIdentityData } = useAppIdentity({ fetchPolicy: 'no-cache' });

  useEffect(() => {
    getEventInfo({ variables: { eventId: id } });
    getEvnetBetaRelateTickets({ variables: { query: { eventId: id } } });
    setProcessConfig(undefined);
    getEventConfiguration();
  }, [id, getEventInfo, getEvnetBetaRelateTickets, getEventConfiguration]);

  const setEventProcessEngineNodes = useCallback(async () => {
    if (eventCenterInfo?.processInstNo) {
      const { data: progressData } = await getDrillOrderProgress({
        variables: { params: { instId: eventCenterInfo.processInstNo, needNodes: true } },
      });
      setLoading(false);
      setProcessEngineNodes(progressData?.drillOrderApproval?.nodeList ?? []);
    }
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [eventCenterInfo?.processInstNo, eventCenterInfo?.eventStatus, getDrillOrderProgress]);

  const isDetectTimeout = (): {
    isTimeout: boolean;
    stage?: 'first' | 'sed' | 'ascertained';
    needCountdown: boolean;
    sla?: number | null;
    startTime?: number | null;
    endTime?: number | null;
  } => {
    const currentTime = new Date().getTime();

    // 待查
    if (!eventCenterInfo?.detectStatus) {
      // 第一阶段未确认 并且不需要倒计时
      if (!eventCenterInfo?.otDetectInfo?.firDetectThreshold) {
        return {
          isTimeout: false,
          needCountdown: true,
          startTime: eventCenterInfo?.gmtCreate,
          endTime: currentTime,
          stage: 'first',
          sla: 60 * 60,
        };
      }
      // 第一阶段未确认 并且需要倒计时
      if (
        !eventCenterInfo?.otDetectInfo?.firConfirmed &&
        eventCenterInfo?.otDetectInfo?.firDetectThreshold
      ) {
        const sla = moment(eventCenterInfo.gmtCreate)
          .add(eventCenterInfo.otDetectInfo.firDetectThreshold * 60, 'minutes')
          .valueOf();

        // 第一阶段取创建时间，超时
        if (sla < currentTime) {
          return {
            isTimeout: true,
            needCountdown: true,
            stage: 'first',
            sla: eventCenterInfo.otDetectInfo.firDetectThreshold * 60,
            startTime: eventCenterInfo.gmtCreate,
            endTime: currentTime,
          };
        }
        return {
          isTimeout: false,
          needCountdown: true,
          stage: 'first',
          sla: eventCenterInfo.otDetectInfo.firDetectThreshold * 60,
          startTime: eventCenterInfo.gmtCreate,
          endTime: currentTime,
        };
      }
      // 第一阶段已确认 ,第二阶段未确认，并且不会超时
      if (
        eventCenterInfo?.otDetectInfo?.firConfirmed &&
        !eventCenterInfo?.otDetectInfo?.secConfirmed &&
        !eventCenterInfo?.otDetectInfo?.secDetectThreshold
      ) {
        return {
          isTimeout: false,
          needCountdown: true,
          startTime: eventCenterInfo?.gmtCreate,
          endTime: currentTime,
          stage: 'sed',
          sla: 60 * 60,
        };
      }
      // 第一阶段已确认 ,第二阶段未确认，并且需要倒计时
      if (
        eventCenterInfo?.otDetectInfo?.firConfirmed &&
        !eventCenterInfo?.otDetectInfo?.secConfirmed &&
        eventCenterInfo?.otDetectInfo?.secDetectThreshold
      ) {
        // 超时未定位第一阶段选择升级，才进行第二阶段
        if (eventCenterInfo?.otDetectInfo?.firUpgraded) {
          const sla = moment(eventCenterInfo.otDetectInfo.firConfirmedTime)
            .add(eventCenterInfo.otDetectInfo.secDetectThreshold * 60, 'minutes')
            .valueOf();
          // 第一阶段取创建时间，超时
          if (sla < currentTime) {
            return {
              isTimeout: true,
              needCountdown: true,
              stage: 'sed',
              sla: eventCenterInfo.otDetectInfo.secDetectThreshold * 60,
              startTime: eventCenterInfo.otDetectInfo.firConfirmedTime,
              endTime: currentTime,
            };
          }
          return {
            isTimeout: false,
            needCountdown: true,
            stage: 'sed',
            sla: eventCenterInfo.otDetectInfo.secDetectThreshold * 60,
            startTime: eventCenterInfo.otDetectInfo.firConfirmedTime,
            endTime: currentTime,
          };
        } else {
          return {
            isTimeout: false,
            needCountdown: true,
            stage: 'sed',
            sla: eventCenterInfo.otDetectInfo.secDetectThreshold * 60,
            startTime: eventCenterInfo.gmtCreate,
            endTime: currentTime,
          };
        }
      }
      if (
        eventCenterInfo?.otDetectInfo?.firConfirmed &&
        eventCenterInfo?.otDetectInfo?.secConfirmed
      ) {
        if (eventCenterInfo?.otDetectInfo.firUpgraded) {
          return {
            isTimeout: false,
            needCountdown: true,
            stage: 'sed',
            sla: 60 * 60,
            startTime: eventCenterInfo.otDetectInfo.firConfirmedTime,
            endTime: currentTime,
          };
        }
        return {
          isTimeout: false,
          needCountdown: true,
          stage: 'sed',
          sla: 60 * 60,
          startTime: eventCenterInfo.gmtCreate,
          endTime: currentTime,
        };
      }
    }
    if (eventCenterInfo?.detectStatus === 1 && eventCenterInfo.detectTime) {
      // 已查明 在事件确认和时间缓解阶段，会有超时升级
      if (
        [EventBetaProcessStatus.Confirming, EventBetaProcessStatus.Relieving].includes(
          eventCenterInfo.eventStatus as EventBetaProcessStatusType
        ) &&
        eventCenterInfo.otRelieveInfo?.relieveThreshold
      ) {
        if (
          eventCenterInfo.otRelieveInfo?.relieveThreshold &&
          !eventCenterInfo.otRelieveInfo.confirmed
        ) {
          const sla = moment(eventCenterInfo.detectTime)
            .add(eventCenterInfo.otRelieveInfo.relieveThreshold * 60, 'minutes')
            .valueOf();
          // 超时
          if (sla < currentTime) {
            return {
              isTimeout: true,
              needCountdown: true,
              stage: 'ascertained',
              sla: eventCenterInfo.otRelieveInfo.relieveThreshold * 60,
              startTime: eventCenterInfo.detectTime,
              endTime: currentTime,
            };
          }
          return {
            isTimeout: false,
            needCountdown: true,
            stage: 'ascertained',
            sla: eventCenterInfo.otRelieveInfo.relieveThreshold * 60,
            startTime: eventCenterInfo.detectTime,
            endTime: currentTime,
          };
        } else {
          return {
            isTimeout: false,
            needCountdown: true,
            stage: 'ascertained',
            sla: 60 * 60,
            startTime: eventCenterInfo.detectTime,
            endTime: currentTime,
          };
        }
      }
      // 事件确认和事件缓解阶段，虽已查明但sla仍要展示
      if (
        [EventBetaProcessStatus.Confirming, EventBetaProcessStatus.Relieving].includes(
          eventCenterInfo.eventStatus as EventBetaProcessStatusType
        )
      ) {
        return {
          isTimeout: false,
          needCountdown: true,
          stage: 'ascertained',
          sla: 60 * 60,
          startTime: eventCenterInfo.detectTime,
          endTime: currentTime,
        };
      }
      return {
        isTimeout: false,
        needCountdown: false,
        stage: 'ascertained',
        sla: 60 * 60,
        startTime: eventCenterInfo.detectTime,
        endTime: currentTime,
      };
    } else {
      return {
        isTimeout: false,
        needCountdown: true,
        stage: 'ascertained',
        sla: 60 * 60,
        startTime: eventCenterInfo.detectTime,
        endTime: currentTime,
      };
    }
  };

  const refetch = (delayFetch?: boolean) => {
    setLoading(true);
    let delay = 0;
    if (delayFetch) {
      delay = 2000;
    }
    setTimeout(() => {
      refetchDetail();
      setEventProcessEngineNodes();
    }, delay);
  };
  const canNotSummary = () => {
    if (eventCenterInfo?.eventLevelCode === 'S' && !eventCenterInfo.convertToProblem) {
      return 'S级事件须先转问题管理，再提交复盘关单';
    }
    if (eventCenterInfo?.eventLevelCode === 'I1' && !eventCenterInfo.convertToProblem) {
      return 'I1级事件须先转问题管理，再提交复盘关单';
    }
    if (
      eventCenterInfo?.occurTime &&
      moment(eventCenterInfo?.occurTime).isSameOrBefore(moment().subtract(30, 'days')) &&
      eventCenterInfo?.eventStatus !== EventBetaProcessStatus.Finishing &&
      !eventCenterInfo.convertToProblem
    ) {
      return '发生后30天内未关单的事件，须先转问题管理，再提交复盘关单';
    }
    return '';
  };

  const { isTimeout, stage } = isDetectTimeout();
  const isHandover = eventCenterInfo?.inHandover;
  const isMisdescription = eventCenterInfo?.falseAlarm === 1 || eventCenterInfo?.falseAlarm === 2;
  const roleCode = appIdentityData?.appIdentity?.roleCode || '';
  const emergencyInfo = eventCenterInfo?.emergencyInfoList?.length
    ? eventCenterInfo.emergencyInfoList[0]
    : null;

  return (
    <>
      {eventCenterInfo && (
        <EventDataContext.Provider
          value={[
            {
              parentForm: form,
              drawerVisible,
              eventProcessEngineNodes: processEngineNodes,
              eventProcessEngineConfig: processConfig,
              notificationVisible,
            },
            {
              setDrawerVisible,
              setEventProcessEngineNodes: setEventProcessEngineNodes,
              setNotificationVisible: param => {
                setNotificationVisible(param);
              },
            },
          ]}
        >
          <Spin spinning={eventDetailLoading || loading}>
            <Space
              direction="vertical"
              size="middle"
              style={{ width: '100%', height: '100%', marginBottom: 48 }}
            >
              {eventCenterInfo.processInstNo && (
                <EventSpecificStatusSteps
                  id={eventCenterInfo.eventId}
                  eventStatus={eventCenterInfo.eventStatus}
                  createTime={eventCenterInfo.gmtCreate}
                  processNo={eventCenterInfo.auditNewestInstNo}
                  refetchDetail={refetch}
                />
              )}
              <EventInfo
                id={id}
                eventCenterInfo={eventCenterInfo}
                refetchDetail={refetch}
                eventProcessData={processData}
                getIsDetectTimeout={isDetectTimeout}
              />

              <EventTabs
                parentActiveKey={tabKey}
                id={id}
                setParentActiveKey={setTabKey}
                eventCenterInfo={eventCenterInfo}
                isDetectTimeout={isDetectTimeout()}
                relateTickets={relateTickets?.evnetBetaRelateTickets.data ?? []}
                refetchGetEvnetBetaRelateTickets={() => refetchGetEvnetBetaRelateTickets()}
                refetchDetail={refetch}
                currentUser={appIdentityData?.appIdentity?.userId}
                setProcess={value => setProcess(value)}
                eventConfiguration={
                  eventConfiguration?.eventConfiguration?.data?.eventAuditSkip || []
                }
                setRelateRiskModalVisible={setRelateRiskModalVisible}
                canNotSummary={canNotSummary}
                getIsDetectTimeout={isDetectTimeout}
              />
              {emergencyInfo && (
                <Card
                  bordered={false}
                  headStyle={{ borderBottom: 0 }}
                  title={
                    <Typography.Title showBadge level={5}>
                      关联应急流程
                    </Typography.Title>
                  }
                >
                  <Space direction="vertical">
                    <Descriptions
                      column={4}
                      contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
                    >
                      <Descriptions.Item label="应急流程名称">
                        {emergencyInfo.name}
                      </Descriptions.Item>
                      <Descriptions.Item label="适用楼栋">
                        {emergencyInfo.blockGuid}
                      </Descriptions.Item>
                      <Descriptions.Item label="专业分类">
                        {emergencyInfo.categoryName}
                      </Descriptions.Item>
                    </Descriptions>
                    <FileList
                      title="附件"
                      files={emergencyInfo.fileInfoList ?? []}
                      groups={[
                        {
                          title: '文件',
                          fileTypes: ['others', 'pdf'],
                        },
                        {
                          title: '图片',
                          fileTypes: ['image', 'video'],
                          previewable: true,
                          showName: true,
                        },
                      ]}
                    />
                  </Space>
                </Card>
              )}
            </Space>

            {(eventCenterInfo.eventStatus as unknown as EventBetaProcessStatusType) !==
              EventBetaProcessStatus.Auditing && (
              <FooterToolBar>
                {!eventDetailLoading && (
                  <Space direction="horizontal">
                    {isTimeout &&
                      [
                        EventBetaProcessStatus.Confirming,
                        EventBetaProcessStatus.Relieving,
                        EventBetaProcessStatus.Finishing,
                      ].includes(eventCenterInfo.eventStatus as EventBetaProcessStatus) &&
                      ((stage === 'first' &&
                        (eventCenterInfo.otDetectInfo?.firHandleUserList?.some(item =>
                          checkUserId(item)
                        ) ||
                          eventCenterInfo.otDetectInfo?.firHandleRoleList?.includes(
                            roleCode || ''
                          ))) ||
                        (stage === 'sed' &&
                          (eventCenterInfo.otDetectInfo?.secHandleUserList?.some(item =>
                            checkUserId(item)
                          ) ||
                            eventCenterInfo.otDetectInfo?.secHandleRoleList?.includes(roleCode))) ||
                        (stage === 'ascertained' &&
                          (eventCenterInfo.otRelieveInfo?.handleUserList?.some(item =>
                            checkUserId(item)
                          ) ||
                            eventCenterInfo.otRelieveInfo?.handleRoleList?.includes(roleCode)))) &&
                      !isMisdescription && (
                        <EventTimeoutUpdateModal
                          eventId={id}
                          refetchDetail={() => {
                            refetch();
                          }}
                          // getIsDetectTimeout={isDetectTimeout}
                        />
                      )}
                    {EventBetaProcessStatus.Confirming === eventCenterInfo.eventStatus &&
                      isOwner &&
                      !isTimeout &&
                      !isHandover && (
                        <EventConfirmModal
                          eventId={id}
                          blockGuid={eventCenterInfo.blockGuid}
                          startTime={eventCenterInfo.occurTime!}
                          refetchDetail={() => {
                            refetch();
                          }}
                          currentUser={appIdentityData?.appIdentity?.userId}
                          getIsDetectTimeout={isDetectTimeout}
                        />
                      )}
                    {EventBetaProcessStatus.Relieving === eventCenterInfo.eventStatus &&
                      isOwner &&
                      !isTimeout &&
                      !isHandover && (
                        <EventRelieveModal
                          eventId={id}
                          blockGuid={eventCenterInfo.blockGuid}
                          currentUser={appIdentityData?.appIdentity?.userId}
                          refetchDetail={() => {
                            refetch();
                          }}
                          getIsDetectTimeout={isDetectTimeout}
                        />
                      )}
                    {EventBetaProcessStatus.Relieving === eventCenterInfo.eventStatus &&
                      isOwner &&
                      !isTimeout &&
                      !isHandover &&
                      !eventCenterInfo?.levelChangeInfo?.processId && (
                        <EventLevelUpdateModal
                          eventId={id}
                          refetchDetail={() => {
                            refetch();
                          }}
                          getIsDetectTimeout={isDetectTimeout}
                        />
                      )}
                    {EventBetaProcessStatus.Finishing === eventCenterInfo.eventStatus &&
                      isOwner &&
                      !isTimeout &&
                      !isHandover &&
                      !isMisdescription && (
                        <EventFinishModal
                          eventId={id}
                          blockGuid={eventCenterInfo.blockGuid}
                          detectStatus={eventCenterInfo.detectStatus}
                          currentUser={appIdentityData?.appIdentity?.userId}
                          relateList={relateTickets?.evnetBetaRelateTickets.data || []}
                          refetchDetail={() => {
                            refetch();
                          }}
                          getIsDetectTimeout={isDetectTimeout}
                        />
                      )}
                    {EventBetaProcessStatus.Finishing === eventCenterInfo.eventStatus &&
                      checkUserId(eventCenterInfo?.handoverUserId) &&
                      !isTimeout &&
                      isHandover && (
                        <Button
                          loading={concludeEvnetLoading}
                          type="primary"
                          onClick={() => {
                            if (isDetectTimeout().isTimeout) {
                              message.error('事件处理超时，请先操作超时升级确认');
                              refetch(true);

                              return;
                            }
                            concludeEvnet({ variables: { eventId: id } });
                          }}
                        >
                          确认办结
                        </Button>
                      )}
                    {EventBetaProcessStatus.Finishing === eventCenterInfo.eventStatus &&
                      isOwner &&
                      !isTimeout &&
                      !isMisdescription && (
                        <EventTransferModal
                          eventId={id}
                          blockGuid={eventCenterInfo.blockGuid}
                          refetchDetail={() => {
                            refetch();
                          }}
                          getIsDetectTimeout={isDetectTimeout}
                        />
                      )}
                    {EventBetaProcessStatus.Finishing === eventCenterInfo.eventStatus &&
                      isOwner &&
                      !isHandover &&
                      isMisdescription && (
                        <Button
                          type="primary"
                          onClick={() => {
                            setNotificationVisible(true);
                          }}
                        >
                          发送结束通报并关闭
                        </Button>
                      )}

                    {EventBetaProcessStatus.Reviewing === eventCenterInfo.eventStatus &&
                      isOwner &&
                      tabKey !== 'checkingRecords' && (
                        <Popconfirm
                          placement="topRight"
                          title={canNotSummary()}
                          open={visibleSummery}
                          okText="关联已有问题"
                          cancelText="创建新问题"
                          cancelButtonProps={{ type: 'primary' }}
                          overlayInnerStyle={{
                            width: 335,
                          }}
                          onCancel={() => {
                            window.open(
                              generateRiskRegisterCreateLocation({
                                eventSourceNo: eventCenterInfo.eventId,
                                eventSourceType: 'EVENT',
                                spaceGuid: eventCenterInfo.blockGuid,
                              }),
                              '_blank'
                            );
                          }}
                          onOpenChange={visible => {
                            !visible && setVisibleSummery(visible);
                          }}
                          onConfirm={() => {
                            setRelateRiskModalVisible(true);
                          }}
                        >
                          <Button
                            type="primary"
                            onClick={async () => {
                              if (canNotSummary()) {
                                setVisibleSummery(true);
                                return;
                              }

                              const { data } = await checkEventCanAudit({
                                variables: {
                                  query: {
                                    eventId: id,
                                    eventVersion: 2,
                                  },
                                },
                              });
                              if (
                                data?.checkEventCanAudit?.code &&
                                data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
                              ) {
                                message.error('需关闭所有关联风险单后，才可提交复盘或评审');
                                return;
                              }
                              setTabKey('checkingRecords');
                            }}
                          >
                            事件复盘
                          </Button>
                        </Popconfirm>
                      )}
                    {EventBetaProcessStatus.Reviewing === eventCenterInfo.eventStatus &&
                      isOwner &&
                      (eventConfiguration?.eventConfiguration.data?.eventAuditSkip?.length
                        ? eventConfiguration.eventConfiguration.data.eventAuditSkip.includes(
                            eventCenterInfo.eventLevelCode
                          )
                        : false) &&
                      tabKey !== 'checkingRecords' && (
                        <EventNotAbleToAuditModalButton
                          eventCenterInfo={eventCenterInfo}
                          eventId={id}
                          canNotSummary={canNotSummary}
                          setRelateRiskModalVisible={setRelateRiskModalVisible}
                          onSuccess={() => refetch(true)}
                        />
                      )}
                  </Space>
                )}
              </FooterToolBar>
            )}
          </Spin>
          <RelateIssueModal
            visible={relateRiskModalVisible}
            eventId={eventCenterInfo.eventId}
            blockGuid={eventCenterInfo.blockGuid}
            idcTag={eventCenterInfo.idcTag}
            onCancle={() => {
              setRelateRiskModalVisible(false);
            }}
            onSuccess={() => {
              refetch();
              setRelateRiskModalVisible(false);
            }}
          />
        </EventDataContext.Provider>
      )}
    </>
  );
}
