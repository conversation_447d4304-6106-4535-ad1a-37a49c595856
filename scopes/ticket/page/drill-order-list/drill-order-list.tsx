/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-14
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import type { DrillOrder, QueryDrillOrdersParams } from '@manyun/ticket.gql.client.drill-order';
import { useLazyDrillOrders, useTakeDrillOrder } from '@manyun/ticket.gql.client.drill-order';
import {
  CREATE_DRILL_ORDER_ROUTE_PATH,
  generateCopyDrillOrderRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { exportDrillTickets } from '@manyun/ticket.service.export-drill-tickets';
import type { DrillOrderTableProps } from '@manyun/ticket.ui.drill-order-table';
import { DrillOrderTable } from '@manyun/ticket.ui.drill-order-table';

import type { DrillOrderFilterFormParams } from './components/drill-order-filter-form';
import { DrillOrderFilter } from './components/drill-order-filter-form';
import { TransferModel } from './components/transfer-model';

export function DrillOrderList() {
  const { userId } = getUserInfo();
  const [, { checkCode }] = useAuthorized();
  const [getDrillOrders, { data, loading }] = useLazyDrillOrders();
  const [takeDrillOrder] = useTakeDrillOrder();
  const [exportLoading, setExportLoading] = useState(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [transferModelOpen, setTransferModelOpen] = useState(false);
  const [selectDrillOrder, setSelectDrillOrder] = useState<DrillOrder>();
  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
  }>({ pageNum: 1, pageSize: 10 });

  const filterParamsRef = useRef<Omit<QueryDrillOrdersParams, 'pageSize' | 'pageNum'>>();
  const paramsFromRoute = getLocationSearchMap(window.location.search);
  const handleFileExport = async (type: string) => {
    let exportParams: Omit<QueryDrillOrdersParams, 'pageSize' | 'pageNum'> = {};
    if (type === 'filtered') {
      exportParams = filterParamsRef.current ?? {};
    }
    setExportLoading(true);
    const { error, data } = await exportDrillTickets(exportParams);
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    return data;
  };
  const fetchDrillOrders = useCallback(
    async (params: Partial<QueryDrillOrdersParams>) => {
      const pageNum = params?.pageNum ?? pagination.pageNum;
      const pageSize = params?.pageSize ?? pagination.pageSize;
      setPagination({ pageNum, pageSize });

      await getDrillOrders({
        variables: {
          params: {
            pageNum,
            pageSize,
            ...filterParamsRef.current,
            ...params,
          },
        },
      });
    },
    [getDrillOrders, pagination.pageNum, pagination.pageSize]
  );

  useEffect(() => {
    const params: QueryDrillOrdersParams = {
      pageSize: 10,
      pageNum: 1,
    };
    if (
      paramsFromRoute.taskAssignee &&
      paramsFromRoute.taskStatusList &&
      paramsFromRoute.taskStatusList.length > 0
    ) {
      params.taskAssignee = JSON.parse(decodeURIComponent(paramsFromRoute.taskAssignee)).value;
      params.taskStatusList = paramsFromRoute.taskStatusList;
    }
    fetchDrillOrders(params);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onFilterFormChange = (params: DrillOrderFilterFormParams) => {
    filterParamsRef.current = { ...filterParamsRef.current, ...params };
    fetchDrillOrders({ pageNum: 1 });
  };

  const onHandleTableChange: DrillOrderTableProps['onChange'] = (_, __, sorter) => {
    const sorterOrder =
      filterParamsRef.current?.sortInfo === 'ASC'
        ? 'ascend'
        : filterParamsRef.current?.sortInfo === 'DESC'
          ? 'descend'
          : undefined;
    if (!Array.isArray(sorter) && sorterOrder !== sorter.order) {
      filterParamsRef.current = {
        ...filterParamsRef.current,
        sortByField: sorter.order ? 'closeTime' : undefined,
        sortInfo:
          sorter.order === 'ascend' ? 'ASC' : sorter.order === 'descend' ? 'DESC' : undefined,
      };
      fetchDrillOrders({});
    }
  };

  const onBatchReceivingOrder = useCallback(
    async (execNoList: string[]) => {
      const { data } = await takeDrillOrder({
        variables: {
          execNoList,
        },
      });
      if (!data?.takeDrillOrder?.success) {
        message.error(data?.takeDrillOrder?.message);
        return;
      }
      message.success('接单成功！');
      fetchDrillOrders({});
    },
    [fetchDrillOrders, takeDrillOrder]
  );

  const operation: DrillOrderTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record: DrillOrder) => {
        const havePermission = userId === record.taskAssignee;
        const haveWaitAacceptPermission =
          (record.assigneeList?.map(item => item.id).includes(userId) ||
            !record.assigneeList?.length) &&
          record.taskStatus === 'WAIT_ACCEPT';
        return (
          <Space>
            {haveWaitAacceptPermission && (
              <Button type="link" compact onClick={() => onBatchReceivingOrder([record.excNo])}>
                接单
              </Button>
            )}
            {havePermission && record.taskStatus === 'ALREADY_ACCEPT' && !record.startTime && (
              <Button
                type="link"
                compact
                onClick={() => {
                  setTransferModelOpen(true);
                  setSelectDrillOrder(record);
                }}
              >
                转交
              </Button>
            )}
            <Button
              type="link"
              href={generateCopyDrillOrderRoutePath({
                id: record.excNo,
              })}
              target="_blank"
              compact
            >
              复制
            </Button>
          </Space>
        );
      },
    };
  }, [onBatchReceivingOrder, userId]);

  const rowSelection = {
    getCheckboxProps: (record: DrillOrder) => ({
      disabled: !(
        record.assigneeList?.map(item => item.id).includes(userId) &&
        record.taskStatus === 'WAIT_ACCEPT'
      ),
    }),
    selectedRowKeys,
    onChange: (newSelectedRowKeys: React.Key[]) => {
      setSelectedRowKeys(newSelectedRowKeys);
    },
  };

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title showBadge level={5}>
          演练列表
        </Typography.Title>
        <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Space size="middle">
            {checkCode('element_drill-order-create') && (
              <Button type="primary" href={CREATE_DRILL_ORDER_ROUTE_PATH}>
                新建
              </Button>
            )}
            <Button
              disabled={!selectedRowKeys.length}
              onClick={() => {
                onBatchReceivingOrder(selectedRowKeys as string[]);
                setSelectedRowKeys([]);
              }}
            >
              批量接单
            </Button>
            <Input.Search
              style={{ width: 224 }}
              placeholder="请输入ID或标题搜索"
              onChange={e => {
                filterParamsRef.current = {
                  ...filterParamsRef.current,
                  titleOrExecNo: e.target.value,
                };
              }}
              onSearch={value => {
                filterParamsRef.current = { ...filterParamsRef.current, titleOrExecNo: value };
                fetchDrillOrders({ pageNum: 1 });
              }}
            />
            <DrillOrderFilter onChange={onFilterFormChange} />
          </Space>
          <FileExport
            filename="演练单列表数据.xls"
            disabled={exportLoading}
            data={type => {
              return handleFileExport(type);
            }}
            showExportFiltered
          />
        </Space>
        <DrillOrderTable
          loading={loading}
          operation={operation}
          rowSelection={rowSelection}
          dataSource={data?.drillOrders?.data ?? []}
          pagination={{
            total: data?.drillOrders?.total ?? 0,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (pageNum: number, pageSize: number) => {
              fetchDrillOrders({ pageNum, pageSize });
            },
          }}
          onChange={onHandleTableChange}
        />
      </Space>
      {selectDrillOrder && (
        <TransferModel
          excNo={selectDrillOrder.excNo}
          blockGuid={selectDrillOrder.blockGuid}
          open={transferModelOpen}
          onClose={() => setTransferModelOpen(false)}
          onCallBack={() => fetchDrillOrders({ pageNum: 1, pageSize: 10 })}
        />
      )}
    </Card>
  );
}
