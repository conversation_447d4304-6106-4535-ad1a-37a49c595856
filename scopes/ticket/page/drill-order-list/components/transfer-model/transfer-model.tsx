import React from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useTransferDrillOrder } from '@manyun/ticket.gql.client.drill-order';

export type TransferModelProps = {
  excNo: string;
  blockGuid: string;
  open: boolean;
  onClose: () => void;
  onCallBack: () => void;
};
export function TransferModel({ excNo, blockGuid, open, onClose, onCallBack }: TransferModelProps) {
  const { userId } = getUserInfo();
  const [form] = Form.useForm();

  const [transferDrillOrder, { loading }] = useTransferDrillOrder({
    onCompleted(data) {
      if (!data.transferDrillOrder?.success) {
        message.error(data.transferDrillOrder?.message);
        return;
      }
      message.success('转交成功');
      onCallBack && onCallBack();
    },
  });

  const onSubmit = async () => {
    const values = await form.validateFields();
    const params = {
      execNo: excNo,
      taskAssignee: values.taskAssignee.id,
      assignReason: values.assignReason,
    };
    await transferDrillOrder({ variables: { params } });
    form.setFieldsValue({ taskAssignee: undefined, assignReason: undefined });
    onClose();
  };
  return (
    <Modal title="转交" open={open} confirmLoading={loading} onOk={onSubmit} onCancel={onClose}>
      <Form
        form={form}
        initialValues={{ slaUnit: 'minutes' }}
        labelCol={{ xl: 4 }}
        wrapperCol={{ xl: 20 }}
      >
        <Form.Item
          label="转交对象"
          name="taskAssignee"
          rules={[
            {
              required: true,
              message: '转交对象必填',
            },
          ]}
        >
          <UserSelect
            style={{ width: 334 }}
            blockGuid={blockGuid}
            userState="leaving"
            disabledKeys={userId ? [userId] : undefined}
            allowClear
          />
        </Form.Item>
        <Form.Item
          label="转交原因"
          name="assignReason"
          rules={[
            { required: true, whitespace: true, message: '转交原因必选！' },
            {
              max: 20,
              message: '最多输入 20 个字符！',
            },
          ]}
        >
          <Input style={{ width: 334 }} allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
}
