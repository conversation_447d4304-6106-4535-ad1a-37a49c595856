import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type TicketStatusSelectProps = Omit<SelectProps, 'options'>;

const options: SelectProps['options'] = [
  { value: 'WAIT_ACCEPT', label: '接单' },
  { value: 'ALREADY_ACCEPT', label: '演练' },
  { value: 'WAIT_COMMENT', label: '点评' },
  { value: 'WAIT_REVIEW', label: '复盘' },
  { value: 'APPROVE_TO_CLOSE', label: '审批' },
  { value: 'CLOSE', label: '完成' },
  { value: 'STOP', label: '已终止' },
];

export const TicketStatusSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select {...props} ref={ref} options={options} />;
  }
);

TicketStatusSelect.displayName = 'TicketStatusSelect';
