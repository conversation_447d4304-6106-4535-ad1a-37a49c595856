import { FilterOutlined } from '@ant-design/icons';
import type { Moment } from 'moment';
import React, { useEffect, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import type { Rule } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import type { DrillOrderTaskStatus } from '@manyun/ticket.gql.client.drill-order';

import { TicketStatusSelect } from './components/ticket-status-select';

export type DrillOrderFilterFormParams = {
  blockGuid?: string;
  taskStatusList?: DrillOrderTaskStatus[];
  excMajor?: string;
  excLevelList?: string[];
  taskAssignee?: number;
  principalId?: number;
  creatorId?: number;
  startTime?: number;
  endTime?: number;
};

export type DrillOrderFilterProps = {
  onChange: (params: DrillOrderFilterFormParams) => void;
};

export function DrillOrderFilter({ onChange }: DrillOrderFilterProps) {
  const [open, setOpen] = useState(false);
  const [showActiveIcon, setShowActiveIcon] = useState(false);

  const onHide = () => {
    setOpen(false);
  };

  const onOpenChange = (open: boolean) => {
    setOpen(open);
  };

  const onFilterFormChange = (params: DrillOrderFilterFormParams) => {
    onChange(params);
    if (params && Object.values(params).some(value => value)) {
      setShowActiveIcon(true);
    } else {
      setShowActiveIcon(false);
    }
  };

  return (
    <Dropdown
      open={open}
      dropdownRender={() => <FilterForm onHide={onHide} onChange={onFilterFormChange} />}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={onOpenChange}
    >
      <Tooltip title="筛选">
        <Button
          icon={<FilterOutlined />}
          ghost={showActiveIcon}
          type={showActiveIcon ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}

function FilterForm({
  onHide,
  onChange,
}: {
  onHide: () => void;
  onChange: (params: DrillOrderFilterFormParams) => void;
}) {
  const [form] = Form.useForm();
  const formItems = [
    {
      label: '机房楼栋',
      name: 'blockGuid',
      colSpan: 12,
      render: <LocationCascader authorizedOnly />,
      rules: [
        {
          type: 'array',
          len: 2,
          message: '必须选择到楼栋',
        },
      ],
    },
    {
      label: '工单状态',
      name: 'taskStatusList',
      colSpan: 12,
      render: <TicketStatusSelect allowClear mode="multiple" maxTagCount="responsive" />,
    },
    {
      label: '专业类型',
      name: 'excMajor',
      colSpan: 12,
      render: <MetaTypeSelect metaType={MetaType.EXC_MAJOR} allowClear />,
    },
    {
      label: '等级',
      name: 'excLevelList',
      colSpan: 12,
      render: (
        <MetaTypeSelect
          metaType={MetaType.EXC_LEVEL}
          allowClear
          mode="multiple"
          maxTagCount="responsive"
        />
      ),
    },
    {
      label: '处理人',
      name: 'taskAssignee',
      colSpan: 12,
      render: <UserSelect allowClear />,
    },
    {
      label: '责任人',
      name: 'principal',
      colSpan: 12,
      render: <UserSelect allowClear />,
    },
    {
      label: '创建人',
      name: 'creator',
      colSpan: 12,
      render: <UserSelect allowClear showSystem />,
    },
    {
      label: '创建时间',
      name: 'createTime',
      colSpan: 12,
      render: <DatePicker.RangePicker allowClear />,
    },
  ];

  useEffect(() => {
    const paramsFromRoute = getLocationSearchMap(window.location.search);
    const taskStatusListFromRoute =
      paramsFromRoute.taskStatusList && paramsFromRoute.taskStatusList.length > 0
        ? paramsFromRoute.taskStatusList
        : undefined;
    const taskAssigneeFromRoute = paramsFromRoute.taskAssignee
      ? JSON.parse(decodeURIComponent(paramsFromRoute.taskAssignee))
      : undefined;
    if (taskStatusListFromRoute && taskStatusListFromRoute.length > 0 && taskAssigneeFromRoute) {
      form.setFieldValue('taskStatusList', taskStatusListFromRoute);
      form.setFieldValue('taskAssignee', taskAssigneeFromRoute);
    }
  }, []);

  const onFinish = (values: {
    blockGuid?: string[];
    taskStatusList?: DrillOrderTaskStatus[];
    excMajor?: string;
    excLevelList?: string[];
    taskAssignee?: { id: number };
    principal?: { id: number };
    creator?: { id: number };
    createTime?: Moment[];
  }) => {
    const {
      blockGuid,
      taskStatusList,
      excMajor,
      excLevelList,
      taskAssignee,
      principal,
      creator,
      createTime,
    } = values;

    const params: DrillOrderFilterFormParams = {
      blockGuid: blockGuid && blockGuid?.length > 1 ? blockGuid[1] : undefined,
      taskStatusList: taskStatusList?.length ? taskStatusList : undefined,
      excMajor,
      excLevelList: excLevelList?.length ? excLevelList : undefined,
      taskAssignee: taskAssignee?.id,
      principalId: principal?.id,
      creatorId: creator?.id,
      startTime:
        Array.isArray(createTime) && createTime.length > 1
          ? createTime[0].startOf('day').valueOf()
          : undefined,
      endTime:
        Array.isArray(createTime) && createTime.length > 1
          ? createTime[1].endOf('day').valueOf()
          : undefined,
    };

    onChange(params);
    onHide();
  };

  const onReset = async () => {
    const values = await form.validateFields();
    onFinish(values);
  };

  return (
    <Card style={{ width: 516 }}>
      <Form layout="vertical" form={form} onFinish={onFinish} onReset={onReset}>
        <Row gutter={16}>
          {formItems.map(item => {
            const { label, name, colSpan, render, rules } = item;
            return (
              <Col key={name} span={colSpan}>
                <Form.Item label={label} name={name} rules={rules as Rule[]}>
                  {React.cloneElement(render, {
                    style: { ...render.props?.style },
                  })}
                </Form.Item>
              </Col>
            );
          })}
        </Row>
        <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Space>
            <Button htmlType="reset">重置</Button>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Space>
        </div>
      </Form>
    </Card>
  );
}
