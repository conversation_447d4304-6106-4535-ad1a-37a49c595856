import dayjs from 'dayjs';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useLazyChanges, useRevertChangeApproval } from '@manyun/ticket.gql.client.tickets';
import type { FetchChangeQ, FetchChangesData } from '@manyun/ticket.gql.client.tickets';
import {
  CHANGE_RESULT_KEY_MAP,
  CHANGE_RESULT_KEY_TEXT_MAP,
  CHANGE_TICKET_STATUS_TEXT_MAP,
  ChangeTicketState,
} from '@manyun/ticket.model.change';
import type { ChangeTicketState as ChangeTicketStateType } from '@manyun/ticket.model.change';
import {
  CHANGE_OFFLIEN_CREATE_ROUTE_PATH,
  generateChangeOfflineEditLocation,
  generateChangeOfflineLocation,
} from '@manyun/ticket.route.ticket-routes';
import { cancelChange } from '@manyun/ticket.service.cancel-change';
import { completeOfflineChange } from '@manyun/ticket.service.complete-offline-change';
import { deleteChange } from '@manyun/ticket.service.delete-change';
import { exportChange } from '@manyun/ticket.service.export-change';

type SearchField = Omit<FetchChangeQ, 'planTime'> & {
  availAreaList?: string | null;
  planTime?: [Moment, Moment] | null;
  executeTime?: [Moment, Moment] | null;
  status?: string[];
  location?: string | null;
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';

const sorterMaps: Record<string, string> = {
  planStartTime: 'PLAN_START_TIME',
};

export function ChangeOfflines() {
  const [exportLoading, setExportLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [changeOrderId, setChangeOrderId] = useState('');
  const [, { checkCode, checkUserId }] = useAuthorized();
  const isCreate = checkCode('element_ticket_change-offline-create');

  const [form] = Form.useForm();
  const [cancelForm] = Form.useForm();

  const [getList, { data, loading }] = useLazyChanges({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  const { search } = useLocation();

  const {
    pageNum,
    pageSize,
    status,
    idcTag,
    blockTag,
    executeEndTime,
    executeStartTime,
    planEndTime,
    planStartTime,
    ...restDefaultFields
  } = getLocationSearchMap<SearchField>(search, {
    parseNumbers: true,
    arrayKeys: ['changeLevelList', 'changeReasonList', 'status'],
  });
  const [fields, setFields] = useState<SearchField>({
    statusList: status,
    ...restDefaultFields,
    planTime:
      planEndTime && planStartTime ? [moment(planStartTime), moment(planEndTime)] : undefined,
    executeTime:
      executeEndTime && executeStartTime
        ? [moment(executeStartTime), moment(executeEndTime)]
        : undefined,
    location: blockTag ?? idcTag,
    pageNum: pageNum ?? 1,
    pageSize: pageSize ?? 10,
  });

  const history = useHistory();
  const [revertChangeApproval] = useRevertChangeApproval();
  const reloadData = useCallback(
    (action?: 'delete') => {
      if (action === 'delete') {
        setFields(pre => {
          return {
            ...pre,
            pageNum:
              (pre.pageNum - 1) * pre.pageSize + 1 === data?.changes?.total && pre.pageNum > 1
                ? pre.pageNum - 1
                : pre.pageNum,
          };
        });
      } else {
        setFields(pre => ({ ...pre }));
      }
    },
    [data?.changes?.total]
  );

  useEffect(() => {
    getList({ variables: { query: { pageNum: 1, pageSize: 10, changeVersion: 2 } } });
  }, [getList]);

  useEffect(() => {
    getList({
      variables: {
        query: {
          changeVersion: 2,
          ...getParams(),
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, getList]);

  const getParams = () => {
    const { planTime, executeTime, location, changeOrderId, ...resp } = fields;
    const [idcTag, blockTag] = (location ?? '').split('.');
    form.setFieldsValue(fields);

    const baseQ: FetchChangeQ = {
      ...resp,
      changeOrderId: changeOrderId?.trim(),
      planStartTime: planTime?.length ? planTime[0].startOf('day').format('YYYY-MM-DD') : null,
      planEndTime: planTime?.length ? planTime[1].startOf('day').format('YYYY-MM-DD') : null,
      executeStartTime: executeTime?.length ? executeTime[0].startOf('day').valueOf() : null,
      executeEndTime: executeTime?.length ? executeTime[1].endOf('day').valueOf() : null,
      idcTag,
      blockTag: blockTag ? location : null,
    };
    setLocationSearch(baseQ);
    return baseQ;
  };

  const handleFileExport = useCallback(
    async type => {
      setExportLoading(true);
      let params = {};
      if (type === 'filtered') {
        params = { ...getParams() };
      }
      const { error, data } = await exportChange({ ...params, changeVersion: 2 });
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return false;
      }
      return data;
    },
    //  eslint-disable-next-line react-hooks/exhaustive-deps
    [fields]
  );

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <QueryFilter
            form={form}
            items={[
              {
                label: '变更ID',
                name: 'changeOrderId',
                control: <Input allowClear />,
              },
              {
                label: '位置',
                name: 'location',
                control: <LocationTreeSelect authorizedOnly allowClear />,
              },
              {
                label: '变更状态',
                name: 'statusList',
                control: (
                  <Select allowClear mode="multiple">
                    {Object.entries(CHANGE_TICKET_STATUS_TEXT_MAP).map(item => (
                      <Select.Option key={item[0]} value={item[0]}>
                        {item[1]}
                      </Select.Option>
                    ))}
                  </Select>
                ),
              },
              {
                label: '变更结果',
                name: 'exeResult',
                control: (
                  <Select allowClear>
                    {Object.entries(CHANGE_RESULT_KEY_TEXT_MAP).map(item => (
                      <Select.Option key={item[0]} value={item[0]}>
                        {item[1]}
                      </Select.Option>
                    ))}
                  </Select>
                ),
              },
              {
                label: '变更等级',
                name: 'changeLevelList',
                control: (
                  <MetaTypeSelect metaType={MetaType.CHANGE_LEVEL} allowClear mode="multiple" />
                ),
              },
              {
                label: '变更专业',
                name: 'changeReasonList',
                control: (
                  <MetaTypeSelect metaType={MetaType.CHANGE_REASON} allowClear mode="multiple" />
                ),
              },
              {
                label: '变更类型',
                name: 'changeType',
                control: <MetaTypeSelect metaType={MetaType.CHANGE} allowClear />,
              },
              {
                label: '申请人',
                name: 'creatorId',
                control: <UserSelect labelInValue={false} allowClear />,
              },
              {
                label: '负责人',
                name: 'respPersonId',
                control: <UserSelect labelInValue={false} allowClear />,
              },
              {
                label: '执行人',
                name: 'operatorId',
                control: <UserSelect labelInValue={false} allowClear />,
              },
              {
                label: '计划时间',
                name: 'planTime',
                span: 2,
                control: <DatePicker.RangePicker format="YYYY-MM-DD" allowClear />,
              },
              {
                label: '执行时间',
                name: 'executeTime',
                span: 2,
                control: <DatePicker.RangePicker format="YYYY-MM-DD" allowClear />,
              },
            ]}
            // initialValues={fields}
            onSearch={value => {
              setFields({ ...value, pageNum: 1, pageSize: fields.pageSize });
            }}
            onReset={() => {
              form.setFieldsValue({ changeVersion: 2, pageNum: 1, pageSize: fields.pageSize });
              // setLocationSearch({ changeVersion: 2, pageNum: 1, pageSize: fields.pageSize });
              setFields({ changeVersion: 2, pageNum: 1, pageSize: fields.pageSize });
            }}
          />
        </Card>
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space
              style={{ width: '100%', justifyContent: 'space-between' }}
              direction="horizontal"
              size="middle"
            >
              {isCreate ? (
                <Button
                  type="primary"
                  onClick={() => history.push(CHANGE_OFFLIEN_CREATE_ROUTE_PATH)}
                >
                  新建线下变更
                </Button>
              ) : (
                <span />
              )}
              <FileExport
                text="导出"
                filename="线下变更.xls"
                disabled={exportLoading}
                data={type => {
                  return handleFileExport(type);
                }}
                showExportFiltered
              />
            </Space>

            <Table<FetchChangesData>
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading}
              dataSource={data?.changes?.data ?? []}
              columns={[
                {
                  title: '变更ID',
                  dataIndex: 'changeOrderId',
                  fixed: true,
                  render: (_, { changeOrderId }) => {
                    if (changeOrderId) {
                      return (
                        <Link
                          target="_blank"
                          to={generateChangeOfflineLocation({
                            id: encodeURIComponent(changeOrderId),
                          })}
                        >
                          {changeOrderId}
                        </Link>
                      );
                    }
                    return '--';
                  },
                },
                {
                  title: '位置',
                  dataIndex: 'blockTag',
                },
                {
                  title: '变更标题',
                  dataIndex: 'title',
                },
                {
                  title: '变更状态',
                  dataIndex: 'changeStatus',
                  render: changeStatus => (
                    <span>
                      {CHANGE_TICKET_STATUS_TEXT_MAP[changeStatus as ChangeTicketStateType]}
                    </span>
                  ),
                },
                {
                  title: '变更专业',
                  dataIndex: 'reason',
                  render: reason => (
                    <MetaTypeText metaType={MetaType.CHANGE_REASON} code={reason} />
                  ),
                },

                {
                  title: '变更类型',
                  dataIndex: 'changeType',
                  render: changeType => (
                    <MetaTypeText metaType={MetaType.CHANGE} code={changeType} />
                  ),
                },
                {
                  title: '变更等级',
                  dataIndex: 'riskLevel',
                  render: riskLevel => (
                    <MetaTypeText metaType={MetaType.CHANGE_LEVEL} code={riskLevel} />
                  ),
                },

                {
                  title: '计划时间',
                  dataIndex: 'planStartTime',
                  width: 256,
                  sorter: true,
                  render: (_, { planStartTime, planEndTime, changeTimeList }) => {
                    return (
                      <>
                        {dayjs(planStartTime).format('YYYY-MM-DD HH:mm')} -<br />
                        {dayjs(planEndTime).format('YYYY-MM-DD HH:mm')}
                      </>
                    );
                  },
                },
                {
                  title: '执行时间',
                  dataIndex: 'realStartTime',
                  render: (_, { realStartTime, realEndTime }) => {
                    if (realStartTime && realEndTime) {
                      return (
                        <>
                          {dayjs(realStartTime).format('YYYY-MM-DD HH:mm')} -<br />
                          {dayjs(realEndTime).format('YYYY-MM-DD HH:mm')}
                        </>
                      );
                    }
                    if (realStartTime && !realEndTime) {
                      return (
                        <>
                          {dayjs(realStartTime).format('YYYY-MM-DD HH:mm')} -<br />
                          --
                        </>
                      );
                    }
                    return '--';
                  },
                },

                {
                  title: '变更结果',
                  dataIndex: 'exeResult',
                  render: exeResult => {
                    if (!exeResult) {
                      return '--';
                    }
                    return (
                      <Tag color={exeResult === CHANGE_RESULT_KEY_MAP.Failed ? 'error' : 'success'}>
                        {CHANGE_RESULT_KEY_TEXT_MAP[exeResult as CHANGE_RESULT_KEY_MAP]}
                      </Tag>
                    );
                  },
                },
                {
                  title: '变更执行人',
                  dataIndex: 'operatorId',
                  render: operatorId =>
                    operatorId ? <User id={operatorId} showAvatar={false} /> : '-',
                },
                {
                  title: '变更申请人',
                  dataIndex: 'creatorName',
                  render: (creatorName, text) =>
                    text.creatorId ? (
                      <User name={creatorName} id={text.creatorId} showAvatar={false} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '变更负责人',
                  dataIndex: 'respPersonId',
                  render: respPersonId =>
                    respPersonId ? <User id={respPersonId} showAvatar={false} /> : '--',
                },
                {
                  title: '操作',
                  dataIndex: 'action',
                  render: (_, { changeOrderId, changeStatus, creatorId, operatorId }) => {
                    const authorized = checkUserId(creatorId);
                    const operatorAuthorized = checkUserId(operatorId);

                    const deleteChangeView = (
                      <Popconfirm
                        key="delete"
                        title="确认删除该条变更单吗？"
                        okText="确定删除"
                        onConfirm={async () => {
                          const { error } = await deleteChange({ changeOrderId });
                          if (error) {
                            message.error(error.message);
                            return;
                          }
                          reloadData('delete');
                        }}
                      >
                        <Button compact type="link">
                          删除
                        </Button>
                      </Popconfirm>
                    );
                    if (changeStatus === ChangeTicketState.Draft) {
                      return (
                        <Space direction="horizontal" size="small">
                          <Link
                            to={generateChangeOfflineEditLocation({
                              id: changeOrderId,
                            })}
                          >
                            编辑
                          </Link>

                          {deleteChangeView}
                        </Space>
                      );
                    }
                    if (changeStatus === ChangeTicketState.Changing) {
                      return (
                        <Button
                          type="link"
                          compact
                          onClick={async () => {
                            const { error } = await completeOfflineChange({ changeOrderId });
                            if (error) {
                              message.error(error.message);
                              return;
                            }
                            message.success('变更已完成');
                            reloadData();
                          }}
                        >
                          完成变更
                        </Button>
                      );
                    }
                    if (
                      changeStatus === ChangeTicketState.WaitingChange &&
                      (authorized || operatorAuthorized)
                    ) {
                      return (
                        <Button
                          type="link"
                          compact
                          onClick={() => {
                            setChangeOrderId(changeOrderId);
                            setVisible(true);
                          }}
                        >
                          取消变更
                        </Button>
                      );
                    }
                    if (
                      changeStatus === ChangeTicketState.Approving ||
                      changeStatus === ChangeTicketState.SummaryApproving
                    ) {
                      return (
                        <Button
                          type="link"
                          compact
                          onClick={() => {
                            revertChangeApproval({
                              variables: { changeOrderId },
                              onCompleted(data) {
                                if (data.revertChangeApproval?.message) {
                                  message.error(data.revertChangeApproval?.message);
                                  return;
                                }
                                reloadData();
                              },
                            });
                          }}
                        >
                          撤回审批
                        </Button>
                      );
                    }
                    return '--';
                  },
                },
              ]}
              pagination={{
                total: data?.changes?.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, sorter, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                  setLocationSearch({
                    ...fields,
                    pageNum: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                }
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setFields(pre => ({
                        ...pre,
                        sortField: null,
                        sortOrder: null,
                      }));
                      setLocationSearch({
                        ...fields,
                        sortField: null,
                        sortOrder: null,
                      });
                      return;
                    }
                    setLocationSearch({
                      ...fields,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                    });
                    setFields(pre => ({
                      ...pre,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
        <Modal
          key="cancelChange"
          title="取消变更"
          open={visible}
          width={750}
          onCancel={() => setVisible(false)}
          onOk={() => {
            cancelForm.validateFields().then(async values => {
              const { error } = await cancelChange({ cancelReason: values.reason, changeOrderId });
              if (error) {
                message.error(error.message);
                return;
              }
              reloadData();
              setVisible(false);
              message.success('已取消变更');
            });
          }}
        >
          <Form form={cancelForm} colon={false} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
            <Form.Item
              label="取消原因"
              name="reason"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: '原因必填',
                },
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ]}
            >
              <Input.TextArea style={{ width: 300 }} allowClear />
            </Form.Item>
          </Form>
        </Modal>
      </Space>
    </>
  );
}
