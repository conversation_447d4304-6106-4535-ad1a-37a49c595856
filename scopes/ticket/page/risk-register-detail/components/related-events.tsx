import dayjs from 'dayjs';
import React from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { EventBetaStatusMap } from '@manyun/ticket.model.event';
import type { QuestionRelateEventInfo } from '@manyun/ticket.model.risk-register';
import { EventBetaProcessStatusTagColorMap } from '@manyun/ticket.page.events-beta';
import { generateEvnetLocation } from '@manyun/ticket.route.ticket-routes';

export type RelatedMattersProps = {
  dataSource?: QuestionRelateEventInfo[];
};

export function RelatedEvents({ dataSource }: RelatedMattersProps) {
  const columns: ColumnsType<QuestionRelateEventInfo> = [
    {
      title: '事件ID',
      dataIndex: 'eventId',
      render: id => {
        return (
          <Link target="_blank" to={generateEvnetLocation({ id })}>
            {id}
          </Link>
        );
      },
    },
    {
      title: '事件标题',
      dataIndex: 'eventTitle',
      width: 300,
      ellipsis: {
        showTitle: true,
      },
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevelName',
    },
    {
      title: '专业分类',
      dataIndex: 'categoryName',
    },
    {
      title: '事件状态',
      dataIndex: 'eventStatus',
      render: (_, { eventStatus }) => {
        return (
          <Tag color={EventBetaProcessStatusTagColorMap[eventStatus]}>
            {EventBetaStatusMap[eventStatus]}
          </Tag>
        );
      },
    },
    {
      title: '发生时间',
      dataIndex: 'occurTime',
      render: time => {
        return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    },
    {
      title: '关闭时间',
      dataIndex: 'closeTime',
      render: time => {
        return time ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '--';
      },
    },
    {
      title: '线上处理人',
      dataIndex: 'ownerId',
      render: userId => <UserLink external userId={userId} />,
    },
  ];

  return (
    <Table<QuestionRelateEventInfo> rowKey="eventId" columns={columns} dataSource={dataSource} />
  );
}
