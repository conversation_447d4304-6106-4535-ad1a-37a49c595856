import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
// import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
// import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
// import { Radio } from '@manyun/base-ui.ui.radio';
import type { RiskLevel } from '@manyun/ticket.gql.client.risk-register';
import { submitRiskRegisterIdentify } from '@manyun/ticket.service.submit-risk-register-identify';

export type SubmitRiskIdentifyButtonProps = {
  riskId: string;
  riskLevel: RiskLevel;
  canSubmit: boolean;
  isFull: boolean;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function SubmitRiskIdentifyButton({
  riskId,
  riskLevel,
  canSubmit,
  isFull,
  onSuccess,
  ...restProps
}: SubmitRiskIdentifyButtonProps) {
  // const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  // const [form] = Form.useForm();

  const onSubmit = async () => {
    setSubmitLoading(true);
    const { error } = await submitRiskRegisterIdentify({
      riskId,
      needApproval: isFull ? false : true,
    });
    setSubmitLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    message.success('提交风险识别成功');
    // setVisible(false);
    onSuccess();
  };
  if (isFull) {
    return (
      <Button
        {...restProps}
        type="primary"
        loading={submitLoading}
        disabled={!canSubmit}
        onClick={() => {
          onSubmit();
        }}
      >
        提交风险措施评估
      </Button>
    );
  }
  return (
    <>
      {canSubmit ? (
        <Button
          {...restProps}
          type="primary"
          loading={submitLoading}
          onClick={() => {
            // if (riskLevel === 'LOW') {
            //   setVisible(true);
            //   form.setFieldValue('needApproval', 'true');
            // } else {
            onSubmit();
            // }
          }}
        >
          提交风险识别
        </Button>
      ) : (
        <Popconfirm
          title="至少添加一条措施，才可提交风险识别"
          style={{ width: 290 }}
          okText="我知道了"
          showCancel={false}
        >
          <Button type="primary">提交风险识别</Button>
        </Popconfirm>
      )}
      {/* <Modal
        title="提交风险识别"
        open={visible}
        okText="提交"
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: submitLoading }}
        bodyStyle={{ paddingBottom: 0 }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <Form labelCol={{ flex: ' 0 0 124px' }} form={form}>
          <Form.Item label="是否需要评估审核" name="needApproval">
            <Radio.Group>
              <Radio value="true">需要</Radio>
              <Radio value="false">不需要</Radio>
            </Radio.Group>
          </Form.Item>
        </Form>
      </Modal> */}
    </>
  );
}
