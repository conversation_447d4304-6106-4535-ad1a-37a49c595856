import React, { useCallback, useEffect } from 'react';

import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Space } from '@manyun/base-ui.ui.space';
import { useLazyBusinessOrderApprovalDetail } from '@manyun/bpm.gql.client.approval';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import type {
  RiskRegisterBaseInfoResponse,
  RiskStatus,
} from '@manyun/ticket.gql.client.risk-register';

import { RiskFooterBar } from './footer-bar';
import { useRiskRegisterContext } from './risk-register-context';

export type ApproveViewProps = {
  instId: string;
  id: string;
  processType: string;
  taskStatus: RiskStatus;
  isRoc?: boolean;
  detailInfo: RiskRegisterBaseInfoResponse;
  timeout: boolean;
  postponeRisk: boolean;
  isOwner: boolean;
  isFull: boolean;
  closeCheck: boolean;
  approveInfo?: BpmInstance | null;
  showNotProblemBtn?: boolean;
  isAuthorizedUser?: boolean;
  canSubmit: () => boolean;
  canClosed: () => boolean;
  onCallBack: () => void;
  setApproveStatus?: (status: string) => void;
};

export function ApproveView({
  instId,
  id,
  processType,
  taskStatus,
  isRoc,
  detailInfo,
  timeout,
  postponeRisk,
  isOwner,
  isFull,
  closeCheck,
  showNotProblemBtn,
  isAuthorizedUser,
  canSubmit,
  canClosed,
  onCallBack,
  setApproveStatus,
}: ApproveViewProps) {
  const { isExportPDF } = useRiskRegisterContext();
  const [getApprovalDetail, { data: approvalDetailData }] = useLazyBusinessOrderApprovalDetail({
    onCompleted(data) {
      if (data.businessOrderApprovalDetail && setApproveStatus && isRoc) {
        setApproveStatus(data.businessOrderApprovalDetail.status);
      }
    },
  });
  const fetchApprovalDetail = useCallback(() => {
    getApprovalDetail({
      variables: {
        instId,
        permissionType: 'RISK_REGISTER',
      },
    });
  }, [instId, getApprovalDetail]);

  useEffect(() => {
    fetchApprovalDetail();
  }, [fetchApprovalDetail, taskStatus]);

  if (!approvalDetailData?.businessOrderApprovalDetail) {
    return;
  }

  return (
    <Space>
      <ApprovalRecordsDropdown
        businessOrderInfo={{
          taskNumber: id,
          type: processType,
          approvalPermissionType: 'RISK_REGISTER',
          status: taskStatus,
        }}
        title={
          isRoc
            ? approvalDetailData.businessOrderApprovalDetail.status === 'APPROVING'
              ? '升级ROC审批中'
              : '升级审批记录'
            : undefined
        }
      />

      <AwaitOperationPeopleTag bpmInstance={approvalDetailData.businessOrderApprovalDetail} />
      {!isExportPDF && ['APPROVING', 'WAITING_EVALUATE'].includes(taskStatus) && !isRoc && (
        <ApprovalOperationButtons
          baseInfo={approvalDetailData.businessOrderApprovalDetail as unknown as BpmInstance}
          getDetail={() => {
            onCallBack();
            fetchApprovalDetail();
          }}
        />
      )}
      {!isExportPDF && ['HANDLING'].includes(taskStatus) && isRoc && (
        <FooterToolBar>
          <Space direction="horizontal">
            <RiskFooterBar
              detailInfo={detailInfo}
              timeout={timeout}
              postponeRisk={postponeRisk}
              isOwner={isOwner}
              isFull={isFull}
              showNotProblemBtn={showNotProblemBtn}
              isAuthorizedUser={isAuthorizedUser}
              showCloseBtn={approvalDetailData.businessOrderApprovalDetail.status !== 'APPROVING'}
              closeCheck={closeCheck}
              canSubmit={canSubmit}
              canClosed={canClosed}
              onSuccess={() => {
                onCallBack();
                fetchApprovalDetail();
              }}
            />
            {approvalDetailData.businessOrderApprovalDetail.status === 'APPROVING' && (
              <ApprovalOperationButtons
                insideApproval
                insideOtherFootBar
                baseInfo={approvalDetailData.businessOrderApprovalDetail as unknown as BpmInstance}
                getDetail={() => {
                  onCallBack();
                  fetchApprovalDetail();
                }}
              />
            )}
          </Space>
        </FooterToolBar>
      )}
    </Space>
  );
}
