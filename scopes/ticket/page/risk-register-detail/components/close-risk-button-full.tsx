import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { useJudgeRiskRegisterCanClose } from '@manyun/ticket.gql.client.tickets';
import { closeRiskRegister } from '@manyun/ticket.service.close-risk-register';

export type CloseRiskFullButtonProps = {
  riskId: string;

  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function CloseRiskFullButton({
  riskId,
  type = 'primary',

  onSuccess,
}: CloseRiskFullButtonProps) {
  const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [unClosed, setUnClosed] = useState('');

  const [form] = Form.useForm();
  // const locales = useMemo(() => getRiskRegisterLocales(), []);
  // const riskClearStatus = Form.useWatch('riskClearStatus', form);
  const [canCloseRiskRegister, { loading }] = useJudgeRiskRegisterCanClose();

  const onSubmit = async () => {
    form.validateFields().then(async values => {
      setSubmitLoading(true);
      const { error } = await closeRiskRegister({
        ...values,
        riskId,
      });
      setSubmitLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      message.success('提交风险关闭申请成功');
      setVisible(false);
      onSuccess();
    });
  };

  return (
    <>
      {!unClosed ? (
        <Button
          type={type}
          loading={loading}
          onClick={() => {
            canCloseRiskRegister({
              variables: {
                query: {
                  riskId,
                },
              },
              onCompleted(data) {
                if (data?.judgeRiskRegisterCanClose?.message) {
                  setUnClosed(data.judgeRiskRegisterCanClose.message);
                }
                if (data?.judgeRiskRegisterCanClose?.success) {
                  setVisible(true);
                }
              },
            });
          }}
        >
          关闭风险单
        </Button>
      ) : (
        <Popconfirm
          title={unClosed}
          // overlayStyle={{ width: 290 }}
          overlayInnerStyle={{ maxWidth: 290 }}
          okText="我知道了"
          showCancel={false}
          open
          onConfirm={() => {
            setUnClosed('');
          }}
        >
          <Button type="primary">关闭风险单</Button>
        </Popconfirm>
      )}
      <Modal
        title="关闭风险单"
        open={visible}
        okText="提交"
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form}>
          <Form.Item
            label="关闭原因"
            name="riskClearReason"
            rules={[
              { required: true, whitespace: true, message: '关闭原因必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
