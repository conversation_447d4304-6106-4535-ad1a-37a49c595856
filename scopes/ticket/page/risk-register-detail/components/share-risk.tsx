import html2canvas from 'html2canvas';
import { jsPDF } from 'jspdf';
import { useEffect, useRef, useState } from 'react';
import { useLatest } from 'react-use';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { uploadFullFile } from '@manyun/dc-brain.service.upload-full-file';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import {
  useJudgeQuestionKnowledgeExist,
  useLazyRiskQuestionKnowledgeCode,
  useShareRiskQuestionToKnowledge,
} from '@manyun/ticket.gql.client.tickets';

import type { ExportPDFTabs } from './risk-register-context';

export default function ShareRisk({
  riskId,
  riskDesc,
  exportPDFTabs,
  setIsExportPDF,
  setExportPdfLoading,
  setExportPDFTab,
  onSuccess,
}: {
  riskId: string;
  riskDesc: string;
  exportPDFTabs: ExportPDFTabs[];
  setIsExportPDF: (mode: boolean) => void;
  setExportPdfLoading: (mode: boolean) => void;
  setExportPDFTab: () => void;
  onSuccess: () => void;
}) {
  const [isShareModalVisible, setIsShareModalVisible] = useState(false);
  const [file, setFile] = useState<{
    fileName: string;
    fileSize: number;
    fileType: string;
    filePath: string;
  }>({
    fileName: '',
    fileSize: 0,
    fileType: '',
    filePath: '',
  });
  const [form] = Form.useForm();
  const [judgeQuestionKnowledgeExist] = useJudgeQuestionKnowledgeExist();
  const exportPdfInterval = useRef<NodeJS.Timer | null>(null);
  const latestExportPDFTabs = useLatest(exportPDFTabs);
  const [shareRiskQuestionToKnowledge, { loading }] = useShareRiskQuestionToKnowledge({
    onCompleted: data => {
      if (data?.shareRiskQuestionToKnowledge?.success) {
        message.success('已分享到知识库');
        setFile({
          fileName: '',
          fileSize: 0,
          fileType: '',
          filePath: '',
        });
        setIsShareModalVisible(false);
        setIsExportPDF(false);
        setExportPdfLoading(false);
        setExportPDFTab();
        exportPdfInterval.current && clearInterval(exportPdfInterval.current);
        onSuccess();
      } else {
        message.error(data?.shareRiskQuestionToKnowledge?.message);
      }
    },
  });
  const [riskQuestionKnowledgeCode, { data }] = useLazyRiskQuestionKnowledgeCode();

  useEffect(() => {
    riskQuestionKnowledgeCode();
  }, [riskQuestionKnowledgeCode]);

  const pdf = () => {
    try {
      // const element = document.getElementById('root');
      const hideMsg = message.loading('正在生成 PDF...', 0);
      const element = document.getElementById('root') ?? document.body;
      element.scrollTop = 0;
      //获取需要导出pdf区域的宽度和高度
      const eleWidth = element.clientWidth;
      let eleHeight = element.clientHeight;

      if (eleWidth > eleHeight) {
        eleHeight = eleWidth;
      }

      html2canvas(document.getElementById('root') ?? document.body, {
        allowTaint: true,
        logging: true,
        useCORS: true,
        scale: 2,
        width: eleWidth,
        height: eleHeight,
      })
        .then(async function (canvas) {
          const contentWidth = canvas.width / 2;
          const contentHeight = canvas.height / 2;
          const pageData = canvas.toDataURL('image/jpeg', 1.0);

          const pdf = new jsPDF('p', 'pt', [contentWidth, contentHeight]);
          pdf.addImage(pageData, 'jpeg', 0, 0, contentWidth, contentHeight);
          // pdf.save(`${'风险'}.pdf`);
          const pdfBlob = pdf.output('blob');
          const { error, data } = await uploadFullFile(new File([pdfBlob], `${'风险'}.pdf`)); // 假设 uploadPdf 是上传服务
          setIsExportPDF(false);
          setExportPdfLoading(false);
          setExportPDFTab();
          if (error) {
            message.error('问题单PDF上传失败，请重试');
            return;
          }

          if (data?.length) {
            setFile({
              fileName: data[0]?.name,
              fileSize: data[0]?.size,
              fileType: data[0]?.ext.toUpperCase(),
              filePath: data[0]?.patialPath,
            });
          }
          setIsShareModalVisible(true);

          form.setFieldsValue({
            courseName: riskDesc,
          });
          // message.success('PDF 上传成功');
          hideMsg();
        })
        .catch(console.error);

      // 将 PDF 转换为 Blob
    } catch (err) {
      console.error(err);
      message.error('生成或上传 PDF 失败');
    }
  };
  const handleShare = async () => {
    form.validateFields().then(() => {
      shareRiskQuestionToKnowledge({
        variables: {
          query: {
            riskId,
            categoryCode: Number(data?.riskQuestionKnowledgeCode.data),
            courseName: form.getFieldValue('courseName'),
            fileName: file.fileName,
            fileSize: file.fileSize,
            fileType: file.fileType,
            filePath: file.filePath,
          },
        },
      });
    });
  };

  return (
    <>
      <Button
        type="link"
        style={{ position: 'fixed', right: 8, top: 52 }}
        onClick={async () => {
          const { data } = await judgeQuestionKnowledgeExist({
            variables: {
              riskId,
            },
          });
          if (data?.judgeQuestionKnowledgeExist?.data) {
            message.error('问题单已分享，请勿重复操作');
            return;
          }
          setIsExportPDF(true);
          setExportPdfLoading(true);

          exportPdfInterval.current = setInterval(() => {
            if (latestExportPDFTabs.current.some((item: ExportPDFTabs) => !item.isRendered)) {
              return;
            }
            setExportPdfLoading(false);
            exportPdfInterval.current && clearInterval(exportPdfInterval.current);
            setTimeout(async () => {
              pdf();
            }, 1000);
          }, 1000);
        }}
      >
        分享到知识库
      </Button>

      <Modal
        title="分享到知识库"
        open={isShareModalVisible}
        destroyOnClose
        footer={[
          <Button key="submit" type="primary" loading={loading} onClick={handleShare}>
            一键分享
          </Button>,
        ]}
        onCancel={() => {
          setIsShareModalVisible(false);
          setExportPDFTab();
          exportPdfInterval.current && clearInterval(exportPdfInterval.current);
        }}
      >
        <Alert
          message={
            <>
              问题单将以 PDF 格式分享到知识库中
              <br />
              如需在知识库中查阅，可联系课件管理员进行授权
            </>
          }
          type="info"
          showIcon
          style={{ width: '100%', marginBottom: 24 }}
        />
        <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item label="分享至" name="categoryCode">
            <CoursewareCategory
              categoryCode={Number(data?.riskQuestionKnowledgeCode.data)}
              variant={Variant.Auth}
            />
          </Form.Item>
          <Form.Item
            label="课件名称"
            name="courseName"
            rules={[
              { required: true, message: '请输入课件名称', whitespace: true },
              {
                max: 60,
                message: '最多输入 60 个字符！',
              },
            ]}
          >
            <Input placeholder="请输入课件名称" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
