import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import React, { useMemo, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import {
  type RiskClearStatus,
  type RiskLevel,
  useCanCloseRiskRegister,
} from '@manyun/ticket.gql.client.risk-register';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { closeRiskRegister } from '@manyun/ticket.service.close-risk-register';
import type { ApiQ } from '@manyun/ticket.service.close-risk-register';

export type CloseRiskButtonProps = {
  riskId: string;
  riskLevel: RiskLevel;
  canClosed: boolean;

  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function CloseRiskButton({
  riskId,
  riskLevel,
  type = 'primary',
  canClosed,
  onSuccess,
}: CloseRiskButtonProps) {
  const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [changeIsClose, setChangeIsClose] = useState(true);

  const [form] = Form.useForm<
    Omit<ApiQ, 'needApproval' | 'fileInfoList'> & {
      needApproval: string;
      fileInfoList?: McUploadFile[];
    }
  >();
  const locales = useMemo(() => getRiskRegisterLocales(), []);
  const riskClearStatus = Form.useWatch('riskClearStatus', form);
  const [canCloseRiskRegister, { loading }] = useCanCloseRiskRegister();

  const onSubmit = async () => {
    form.validateFields().then(async values => {
      setSubmitLoading(true);
      const { error } = await closeRiskRegister({
        ...values,
        fileInfoList: values.fileInfoList
          ? values.fileInfoList.map((item: McUploadFile) =>
              McUploadFile.fromJSON(item).toApiObject()
            )
          : [],
        needApproval: true,
        riskId,
      });
      setSubmitLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      message.success(values.needApproval ? '提交风险关闭申请成功' : '风险单已关闭');
      setVisible(false);
      onSuccess();
    });
  };

  // const getDescription = () => {
  //   if (riskClearStatus === 'UNCLEARED') {
  //     return '当前风险解除状态为“无法解除”，将自动提交至风险管理员进行审核，审核通过后风险单将自动关闭。';
  //   } else {
  //     if (riskLevel === 'HIGH') {
  //       return '当前风险等级为高风险，将自动提交至风险管理员进行审核，审核通过后风险单将自动关闭。';
  //     }
  //     if (riskLevel === 'MID') {
  //       return '当前风险等级为中风险，将自动提交至风险管理员进行审核，审核通过后风险单将自动关闭。';
  //     }
  //     return (
  //       <span>
  //         当前风险等级为低风险，且风险已解除，请选择是否需要提交审核：
  //         <br />
  //         1.若选择需要审核，将自动提交至风险管理员进行审核，审核通过后风险单将自动关闭；
  //         <br />
  //         2.若选择不需要审核，风险单会直接关闭。
  //       </span>
  //     );
  //   }
  // };

  if (!changeIsClose) {
    return (
      <Popconfirm
        title="请确保当前风险单创建的变更单都为「结束」状态，再关闭此风险单。"
        overlayStyle={{ width: 290 }}
        okText="我知道了"
        showCancel={false}
        open
        onConfirm={() => {
          setChangeIsClose(true);
        }}
      >
        <Button type="primary">关闭风险单</Button>
      </Popconfirm>
    );
  }

  return (
    <>
      {canClosed ? (
        <Button
          type={type}
          loading={loading}
          onClick={() => {
            canCloseRiskRegister({
              variables: {
                query: {
                  riskId,
                  bizType: 'CHANGE',
                },
              },
              onCompleted(data) {
                if (data.canCloseRiskRegister.success && data.canCloseRiskRegister.data) {
                  setVisible(true);
                  form.setFieldValue('needApproval', 'true');
                }
                if (data.canCloseRiskRegister.success && !data.canCloseRiskRegister.data) {
                  setChangeIsClose(false);
                }
              },
            });
          }}
        >
          关闭风险单
        </Button>
      ) : (
        <Popconfirm
          title="请确保所有措施为“已完成”状态，再关闭风险单"
          overlayStyle={{ width: 290 }}
          okText="我知道了"
          showCancel={false}
        >
          <Button type="primary">关闭风险单</Button>
        </Popconfirm>
      )}
      <Modal
        title="关闭风险单"
        open={visible}
        okText="提交"
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        {/* {riskClearStatus && (
          <Alert description={getDescription()} type="warning" style={{ marginBottom: 24 }} />
        )} */}
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form}>
          <Form.Item
            label="风险是否解除"
            name="riskClearStatus"
            rules={[{ required: true, message: '风险是否解除必选！' }]}
          >
            <Radio.Group
              onChange={value => {
                if (value.target.value === 'UNCLEARED') {
                  form.setFieldValue('needApproval', 'true');
                }
              }}
            >
              {Object.keys(locales.riskClearStatus.enum).map(status => (
                <Radio key={status} value={status}>
                  {locales.riskClearStatus.enum[status as RiskClearStatus]}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          {riskClearStatus === 'CLEARED' && (
            <Form.Item
              label="整改附件"
              name="fileInfoList"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <Upload
                accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf,.zip,.rar"
                maxCount={5}
              >
                <Space direction="vertical">
                  <Button icon={<UploadOutlined />}>点此上传</Button>
                </Space>
              </Upload>
            </Form.Item>
          )}
          {/* <Form.Item label="是否需要审核" name="needApproval">
            <Radio.Group
              defaultValue="true"
              disabled={
                riskLevel === 'HIGH' || riskLevel === 'MID' || riskClearStatus === 'UNCLEARED'
              }
            >
              <Radio value="true">需要</Radio>
              <Radio value="false">不需要</Radio>
            </Radio.Group>
          </Form.Item> */}
          <Form.Item
            label="关闭原因"
            name="riskClearReason"
            rules={[
              { required: true, whitespace: true, message: '关闭原因必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
