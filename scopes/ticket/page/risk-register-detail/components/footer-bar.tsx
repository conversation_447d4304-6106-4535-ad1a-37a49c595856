import React, { useState } from 'react';

import { Button, type ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import ApprovalOperationButtons from '@manyun/bpm.ui.approval-operation-buttons';
import type {
  RiskLevel,
  RiskRegisterBaseInfoResponse,
} from '@manyun/ticket.gql.client.risk-register';
import { TransferButton } from '@manyun/ticket.page.risk-registers';
import { closeRiskRegister } from '@manyun/ticket.service.close-risk-register';

import { CloseRiskButton } from './close-risk-button';
import { CloseRiskFullButton } from './close-risk-button-full';
import PostponeRisk from './postpone-risk-register';
import { SubmitRiskIdentifyButton } from './submit-risk-identify-button';

export type UpdateRocProps = {
  detailInfo: RiskRegisterBaseInfoResponse;
  timeout: boolean;
  postponeRisk: boolean;
  isOwner: boolean;
  isFull: boolean;
  closeCheck: boolean;
  approveInfo?: BpmInstance | null;
  showCloseBtn?: boolean;
  showNotProblemBtn?: boolean;
  isAuthorizedUser?: boolean;
  canSubmit: () => boolean;
  canClosed: () => boolean;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function RiskFooterBar({
  detailInfo,
  timeout,
  postponeRisk,
  isOwner,
  isFull,
  closeCheck,
  approveInfo,
  canSubmit,
  showCloseBtn = true,
  isAuthorizedUser,
  showNotProblemBtn,
  canClosed,
  onSuccess,
}: UpdateRocProps) {
  const [loading, setSubmitLoading] = useState(false);
  const onSubmit = async () => {
    setSubmitLoading(true);
    const { error } = await closeRiskRegister({
      riskId: detailInfo.id,
    });
    setSubmitLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    message.success('风险单已关闭');
    onSuccess();
  };
  const isQuestion = detailInfo.riskResourceCode === 'EVENT_QUESTION';
  return (
    // <FooterToolBar>
    <Space>
      {detailInfo.riskStatus === 'HANDLING' &&
        timeout &&
        !detailInfo.timeExtensionApprInfo &&
        postponeRisk &&
        isOwner && (
          <PostponeRisk
            riskId={detailInfo.id}
            planCompleteTime={detailInfo.planCompleteTime}
            type="primary"
            onSuccess={onSuccess}
          />
        )}
      {(!isFull ? isAuthorizedUser : isOwner) && detailInfo.riskStatus === 'WAITING_IDENTIFY' && (
        <SubmitRiskIdentifyButton
          riskId={detailInfo.id}
          riskLevel={detailInfo.riskLevel as RiskLevel}
          canSubmit={canSubmit()}
          isFull={isFull}
          onSuccess={onSuccess}
        />
      )}
      {isAuthorizedUser &&
        detailInfo.riskStatus === 'WAITING_IDENTIFY' &&
        isQuestion &&
        showNotProblemBtn && (
          <Popconfirm
            title="确定关闭该问题？关闭后不可恢复"
            style={{ width: 290 }}
            okText="确定关闭"
            okButtonProps={{ loading }}
            onConfirm={() => {
              onSubmit();
            }}
          >
            <Button>非问题直接关闭</Button>
          </Popconfirm>
        )}
      {isOwner && detailInfo.riskStatus === 'HANDLING' && detailInfo.riskLevel && showCloseBtn ? (
        closeCheck ? (
          // yanggao
          <CloseRiskFullButton
            riskId={detailInfo.id}
            type={timeout && !detailInfo.timeExtensionApprInfo ? 'default' : 'primary'}
            onSuccess={onSuccess}
          />
        ) : (
          <CloseRiskButton
            riskId={detailInfo.id}
            type={timeout && !detailInfo.timeExtensionApprInfo ? 'default' : 'primary'}
            riskLevel={detailInfo.riskLevel as RiskLevel}
            canClosed={canClosed()}
            onSuccess={onSuccess}
          />
        )
      ) : null}
      {isOwner && ['WAITING_IDENTIFY', 'HANDLING'].includes(detailInfo.riskStatus) && (
        <TransferButton
          riskId={detailInfo.id}
          blockGuid={detailInfo.blockGuid}
          onSuccess={onSuccess}
        />
      )}
      {detailInfo.riskStatus === 'HANDLING' &&
        !timeout &&
        !detailInfo.timeExtensionApprInfo &&
        postponeRisk && (
          <PostponeRisk
            riskId={detailInfo.id}
            planCompleteTime={detailInfo.planCompleteTime}
            onSuccess={onSuccess}
          />
        )}
      {detailInfo.riskStatus === 'HANDLING' &&
        detailInfo.upgradeRoc &&
        approveInfo?.status === 'APPROVING' && (
          <ApprovalOperationButtons
            baseInfo={approveInfo as unknown as BpmInstance}
            getDetail={() => {
              onSuccess();
              // fetchApprovalDetail();
            }}
          />
        )}
    </Space>
    // </FooterToolBar>
  );
}
