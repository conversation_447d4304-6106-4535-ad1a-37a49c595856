import moment from 'moment';
import type { Moment } from 'moment';
import React, { useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import type { MeasureJson, MeasureType, RiskStatus } from '@manyun/ticket.gql.client.risk-register';
import type { RiskRegisterMeasureAndProcessingRecordResponse } from '@manyun/ticket.gql.client.tickets';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { createRiskRegisterMeasure } from '@manyun/ticket.service.create-risk-register-measure';
import type { ApiQ } from '@manyun/ticket.service.create-risk-register-measure';
import { updateRiskRegisterMeasure } from '@manyun/ticket.service.update-risk-register-measure';

export type AddEditMeasureProps = {
  riskId: string;
  text: string;
  riskStatus: RiskStatus;
  measureId?: number;
  values?: MeasureJson | RiskRegisterMeasureAndProcessingRecordResponse;
  isFull?: boolean;
  infoPlanCompleteTime?: string | null;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

function generateRange(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}

export default function AddEditMeasureButton({
  riskId,
  text,
  measureId,
  riskStatus,
  values,
  isFull,
  infoPlanCompleteTime,
  onSuccess,
  ...restProps
}: AddEditMeasureProps) {
  const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm<Omit<ApiQ, 'planCompleteTime'> & { planCompleteTime: Moment }>();
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const getFescription = () => {
    if (isFull) {
      if (measureId) {
        return '修改「措施类型」、「措施内容」并保存后，风险单将回退到“添加风险措施”节点，需重新评估审批，请谨慎操作！';
      }
      return '添加并提交后，风险单将回退到“添加风险措施”节点，需重新评估审批，请谨慎操作！';
    } else {
      if (measureId) {
        return '修改「措施类型」、「措施描述」并保存后，风险单将回退到“待识别”状态，请谨慎操作！';
      }
      return '添加并提交后，风险单将回退到”待识别“状态，请谨慎操作！';
    }
  };
  return (
    <>
      <Button
        {...restProps}
        onClick={event => {
          event.stopPropagation();
          setVisible(true);
          if (values) {
            form.setFieldsValue({
              ...values,
              planCompleteTime: moment(values.planCompleteTime),
            });
          }
        }}
      >
        {text}
      </Button>
      <Modal
        title={measureId ? '编辑风险措施' : '添加风险措施'}
        open={visible}
        okText={measureId ? '保存' : '提交'}
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: submitLoading }}
        onCancel={event => {
          event.stopPropagation();
          setVisible(false);
        }}
        onOk={async event => {
          event.stopPropagation();
          form.validateFields().then(async values => {
            setSubmitLoading(true);
            if (measureId) {
              const { error } = await updateRiskRegisterMeasure({
                measureId,
                ...values,
                riskId,
                planCompleteTime: moment(values.planCompleteTime).format('YYYY-MM-DD HH:mm:ss'),
              });
              setSubmitLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
            } else {
              const { error } = await createRiskRegisterMeasure({
                ...values,
                riskId,
                planCompleteTime: moment(values.planCompleteTime).format('YYYY-MM-DD HH:mm:ss'),
              });
              setSubmitLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
            }
            message.success(`${measureId ? '保存' : '提交'}成功`);
            setVisible(false);
            onSuccess();
          });
        }}
      >
        {riskStatus === 'HANDLING' && (
          <Alert
            message={`确认${measureId ? '修改' : '添加'}风险措施？`}
            description={getFescription()}
            type="warning"
            showIcon
            style={{ marginBottom: 24 }}
          />
        )}
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form}>
          <Form.Item
            label={locales.measureType._self ?? '措施类型'}
            name="measureType"
            rules={[{ required: true, message: '措施类型必选！' }]}
          >
            <Radio.Group>
              {Object.keys(locales.measureType.enum).map(status => (
                <Radio key={status} value={status}>
                  {locales.measureType.enum[status as MeasureType]}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            label={isFull ? '措施内容' : '措施描述'}
            name="measureDesc"
            rules={[
              {
                required: true,
                whitespace: true,
                message: isFull ? '措施内容必填！' : '措施描述必填！',
              },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
          <Form.Item
            label="跟进人"
            name="followUserId"
            rules={[{ required: true, message: '跟进人必选！' }]}
          >
            <UserSelect labelInValue={false} />
          </Form.Item>
          <Form.Item
            label="计划完成时间"
            name="planCompleteTime"
            required
            rules={[
              {
                validator: (_, rang) => {
                  if (!rang) {
                    return Promise.reject('计划完成时间必选！');
                  }
                  const _infoPlanCompleteTime = moment(infoPlanCompleteTime)
                    .format('YYYY-MM-DD HH:mm')
                    .valueOf();
                  const planTime = moment(rang).format('YYYY-MM-DD HH:mm').valueOf();
                  if (planTime > _infoPlanCompleteTime && isFull) {
                    return Promise.reject('风险措施计划完成时间，不可晚于风险单计划完成时间');
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <DatePicker
              disabledDate={
                isFull && infoPlanCompleteTime
                  ? current => {
                      /**不可早于计划开始时间 */
                      return current > moment(infoPlanCompleteTime).endOf('day');
                    }
                  : undefined
              }
              disabledTime={
                isFull && infoPlanCompleteTime
                  ? current => {
                      const compareMoment = moment(infoPlanCompleteTime);
                      const hour = compareMoment.hour();
                      const minute = compareMoment.minute();
                      const second = compareMoment.second();
                      if (current && current.isSame(compareMoment, 'day')) {
                        const currentHour = current.hour();
                        const currentMinute = current.minute();
                        if (currentHour === hour) {
                          return {
                            disabledHours: () => generateRange(hour + 1, 59),
                            disabledMinutes: () => generateRange(minute + 1, 59),
                            disabledSeconds: () =>
                              currentMinute === minute ? generateRange(second + 1, 59) : [],
                          };
                        } else {
                          return {
                            disabledHours: () => generateRange(hour + 1, 59),
                            disabledMinutes: () => [],
                            disabledSeconds: () => [],
                          };
                        }
                      }
                      return {
                        disabledHours: () => [],
                        disabledMinutes: () => [],
                        disabledSeconds: () => [],
                      };
                    }
                  : undefined
              }
              picker="date"
              showTime
              format="YYYY-MM-DD HH:mm"
              showNow={!isFull}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
