import dayjs from 'dayjs';
import React from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import type { ChangeTicketState } from '@manyun/ticket.model.change';
import { CHANGE_TICKET_STATUS_TEXT_MAP } from '@manyun/ticket.model.change';
import {
  generateChangeOfflineLocation,
  generateChangeTicketDetail,
} from '@manyun/ticket.route.ticket-routes';
import type { RiskRegisterRelate } from '@manyun/ticket.service.fetch-risk-register-relates';

export type RelatedMattersProps = {
  dataSource: RiskRegisterRelate[];
};

export function RelatedMatters({ dataSource }: RelatedMattersProps) {
  const columns: ColumnsType<RiskRegisterRelate> = [
    {
      title: '类型',
      dataIndex: 'relateBizType',
      width: 150,
      render: relateBizType => {
        switch (relateBizType) {
          case 'CHANGE':
            return '变更';
          default:
            return '--';
        }
      },
    },
    {
      title: 'ID',
      dataIndex: 'relateBizId',
      width: 150,
      render: id => {
        if (id.startsWith('N')) {
          return (
            <Link
              target="_blank"
              to={generateChangeOfflineLocation({
                id,
              })}
            >
              {id}
            </Link>
          );
        }
        return (
          <Link target="_blank" to={generateChangeTicketDetail({ id })}>
            {id}
          </Link>
        );
      },
    },
    {
      title: '事项描述',
      dataIndex: 'relateBizDesc',
      width: 300,
      ellipsis: {
        showTitle: true,
      },
    },
    {
      title: '状态',
      dataIndex: 'relateBizStatus',
      width: 150,
      render: (_, { relateBizStatus }) =>
        CHANGE_TICKET_STATUS_TEXT_MAP[relateBizStatus as ChangeTicketState],
    },
    {
      title: '创建人',
      dataIndex: 'relateBizCreateUserId',
      width: 150,
      render: userId => <UserLink external userId={userId} />,
    },
    {
      title: '创建时间',
      dataIndex: 'relateBizCreateTime',
      width: 150,
      sorter: (a, b) => a.relateBizCreateTime - b.relateBizCreateTime,
      render: text => dayjs(text).format('YYYY-MM-DD HH:mm:ss'),
    },
  ];

  return (
    <Table<RiskRegisterRelate> rowKey="relateBizId" columns={columns} dataSource={dataSource} />
  );
}
