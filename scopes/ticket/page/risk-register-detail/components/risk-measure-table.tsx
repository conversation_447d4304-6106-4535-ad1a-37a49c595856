import RightOutlined from '@ant-design/icons/es/icons/RightOutlined';
import dayjs from 'dayjs';
import moment from 'moment';
import React, { useState } from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Empty } from '@manyun/base-ui.ui.empty';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import type {
  MeasureStatus,
  RiskRegisterBaseInfoResponse,
} from '@manyun/ticket.gql.client.risk-register';
import type { RiskRegisterMeasureAndProcessingRecordResponse } from '@manyun/ticket.gql.client.tickets';
import { deleteRiskRegisterMeasure } from '@manyun/ticket.service.delete-risk-register-measure';
import { updateRiskRegisterMeasureStatus } from '@manyun/ticket.service.update-risk-register-measure-status';

import AddEditMeasure from './add-edit-measure';
import AddMeasureProcessingRecordButton from './add-measure-processing-record';
import { MeasureConfirmButton } from './measure-confirm-button';

export type RiskMeasureTableProps = {
  detailInfo: RiskRegisterBaseInfoResponse;
  measuresAllData: RiskRegisterMeasureAndProcessingRecordResponse[];
  isOwner?: boolean;
  onRefetchDetail?: () => void;
  onRefetchMeasure?: () => void;
};

const expandedRowRender = (record: RiskRegisterMeasureAndProcessingRecordResponse) => {
  return (
    <Table
      rowKey="optRecordId"
      columns={[
        {
          title: '处理时间',
          dataIndex: 'handleTime',
          sorter: (a, b) => a.handleTime - b.handleTime,
          width: 180,
          render: handleTime => dayjs(handleTime).format('YYYY-MM-DD HH:mm:ss'),
        },
        {
          title: '处理人',
          dataIndex: 'handlerUserId',
          width: 88,
          render: handlerUserId => <User id={handlerUserId} showAvatar={false} />,
        },
        {
          title: '处理内容',
          dataIndex: 'handleContent',
          render: value => {
            return (
              <Typography.Paragraph
                style={{ marginBottom: 0, width: '90%' }}
                ellipsis={{ rows: 2, tooltip: true }}
              >
                {value}
              </Typography.Paragraph>
            );
          },
        },
        {
          title: '附件',
          dataIndex: 'deviceGuid',
          width: 190,
          render: (value, { fileInfoList }) => {
            if (!fileInfoList?.length) {
              return '--';
            }

            return (
              <SimpleFileList files={fileInfoList}>
                <Typography.Link>查看</Typography.Link>
              </SimpleFileList>
            );
          },
        },
      ]}
      dataSource={(record?.optRecordList ?? []).map(item => ({
        ...item,
        handleTime: moment(item.handleTime).valueOf(),
      }))}
      pagination={false}
    />
  );
};

export default function RiskMeasureTable({
  detailInfo,
  measuresAllData,
  isOwner,
  onRefetchDetail,
  onRefetchMeasure,
}: RiskMeasureTableProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const [, { checkUserId }] = useAuthorized();

  const onDelete = async (measure: RiskRegisterMeasureAndProcessingRecordResponse) => {
    setLoading(true);
    const { error } = await deleteRiskRegisterMeasure({
      riskId: measure.riskId,
      measureId: measure.measureId,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    onRefetchMeasure && onRefetchMeasure();
    onRefetchDetail && onRefetchDetail();
  };

  const getOpeartion = (measure: RiskRegisterMeasureAndProcessingRecordResponse) => {
    const isHandling = detailInfo.riskStatus === 'HANDLING';
    const isFollowUserId = checkUserId(measure.followUserId);

    const edit = (
      <AddEditMeasure
        text="编辑"
        type={!isDone(measure.measureStatus).done && isHandling ? 'text' : 'link'}
        compact
        riskId={detailInfo.id}
        riskStatus={detailInfo.riskStatus}
        values={measure}
        measureId={measure.measureId}
        infoPlanCompleteTime={detailInfo.planCompleteTime}
        isFull
        onSuccess={() => {
          onRefetchMeasure && onRefetchMeasure();
          onRefetchDetail && onRefetchDetail();
        }}
      />
    );

    if (detailInfo.riskStatus === 'WAITING_IDENTIFY' && (isFollowUserId || isOwner)) {
      return (
        <Space>
          {edit}
          <Popconfirm
            title="确认删除该措施？删除后无法恢复"
            style={{ width: 290 }}
            okText="确认删除"
            cancelText="取消"
            onConfirm={async e => {
              onDelete(measure);
            }}
          >
            <Button
              loading={loading}
              compact
              type="link"
              onClick={event => event.stopPropagation()}
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      );
    }

    if (isHandling && (isFollowUserId || isOwner)) {
      const invalidateMeasure = (
        <Popconfirm
          title="确定失效该措施？失效后将无法进行任何操作"
          style={{ width: 290 }}
          okText="确认失效"
          cancelText="取消"
          onCancel={e => e?.stopPropagation()}
          onConfirm={async e => {
            e?.stopPropagation();
            const { error } = await updateRiskRegisterMeasureStatus({
              riskId: measure.riskId,
              measureId: measure.measureId,
              measureStatus: 'INVALID',
            });
            setLoading(false);
            if (error) {
              message.error(error.message);
              return;
            }
            onRefetchMeasure && onRefetchMeasure();
          }}
        >
          <Button loading={loading} compact type="text" onClick={event => event.stopPropagation()}>
            失效
          </Button>
        </Popconfirm>
      );
      if (isDone(measure.measureStatus).done) {
        return (
          <Space>
            <Button
              type="link"
              compact
              loading={loading}
              onClick={async event => {
                event.stopPropagation();
                setLoading(true);
                const { error } = await updateRiskRegisterMeasureStatus({
                  riskId: measure.riskId,
                  measureId: measure.measureId,
                  measureStatus: 'UNDONE',
                });
                setLoading(false);
                if (error) {
                  message.error(error.message);
                  return;
                }
                onRefetchMeasure && onRefetchMeasure();
              }}
            >
              置为未完成
            </Button>

            {/* {edit} */}
          </Space>
        );
      }
      if (!isDone(measure.measureStatus).done) {
        return (
          <Space>
            <AddMeasureProcessingRecordButton
              riskId={measure.riskId}
              riskStatus={detailInfo.riskStatus}
              measureId={measure.measureId}
              blockGuid={detailInfo.blockGuid}
              onSuccess={() => {
                onRefetchMeasure && onRefetchMeasure();
              }}
            />

            <MeasureConfirmButton
              riskId={detailInfo.id}
              measure={measure}
              onSuccess={() => {
                onRefetchMeasure && onRefetchMeasure();
              }}
            />
            <Dropdown.Button
              type="link"
              menu={{
                items: [
                  {
                    key: 'edit',
                    label: edit,
                  },
                  {
                    key: ' invalidateMeasure',
                    label: invalidateMeasure,
                  },
                ],
              }}
              buttonsRender={([leftButton, rightButton]) => [null, rightButton]}
              onClick={e => e.stopPropagation()}
            />
          </Space>
        );
      }
    }
    return null;
  };

  return (
    <>
      {measuresAllData?.length ? (
        <Table<RiskRegisterMeasureAndProcessingRecordResponse>
          // scroll={{ x: 'max-content' }}
          dataSource={measuresAllData}
          rowKey="measureId"
          columns={[
            {
              title: '序号',
              dataIndex: 'measureId',
              fixed: 'left',
              width: 60,
              render: (_, measure, index) => {
                return (
                  <Typography.Text disabled={measure.measureStatus === 'INVALID'}>
                    {index + 1}
                  </Typography.Text>
                );
              },
            },
            {
              title: '措施类型',
              width: 120,
              dataIndex: 'measureType',
              render: (measureType, measure) => {
                if (measureType === 'LONG') {
                  return (
                    <Typography.Text disabled={measure.measureStatus === 'INVALID'}>
                      长期措施
                    </Typography.Text>
                  );
                }
                return (
                  <Typography.Text disabled={measure.measureStatus === 'INVALID'}>
                    短期措施
                  </Typography.Text>
                );
              },
            },
            {
              title: '跟进人',
              dataIndex: 'followUserId',
              width: 88,
              render: (handlerUserId, measure) => (
                <Typography.Text disabled={measure.measureStatus === 'INVALID'}>
                  <User id={handlerUserId} showAvatar={false} />
                </Typography.Text>
              ),
            },
            {
              title: '措施内容',
              dataIndex: 'measureDesc',
              render: (measureDesc, { measureStatus }) => {
                return (
                  <div
                    style={{
                      width: '100%',
                      display: 'flex',
                      flexDirection: 'row',
                      gap: 8,
                      alignItems: 'center',
                    }}
                  >
                    <Tag color={isDone(measureStatus).tagColor} style={{ height: 22 }}>
                      {isDone(measureStatus).text}
                    </Tag>
                    <Typography.Paragraph
                      disabled={measureStatus === 'INVALID'}
                      style={{ marginBottom: 0, width: '90%' }}
                      ellipsis={{ rows: 2, tooltip: true }}
                    >
                      {measureDesc}
                    </Typography.Paragraph>
                  </div>
                );
              },
            },
            {
              title: '计划完成时间',
              dataIndex: 'planCompleteTime',
              width: 165,
              render: (planCompleteTime, { measureStatus }) => (
                <Typography.Text disabled={measureStatus === 'INVALID'}>
                  {dayjs(planCompleteTime).format('YYYY-MM-DD HH:mm')}
                </Typography.Text>
              ),
            },
            {
              title: '实际完成时间',
              dataIndex: 'completeTime',
              width: 165,
              render: (completeTime, { measureStatus }) => (
                <Typography.Text disabled={measureStatus === 'INVALID'}>
                  {completeTime ? dayjs(completeTime).format('YYYY-MM-DD HH:mm') : '--'}
                </Typography.Text>
              ),
            },
            {
              title: '操作',
              dataIndex: 'operarion',
              width: 190,
              render: (_, record) => record.measureStatus !== 'INVALID' && getOpeartion(record),
            },
          ]}
          expandable={{
            expandedRowRender: record => expandedRowRender(record),
            rowExpandable: record => !!record.optRecordList?.length,
            expandIcon: ({ expanded, onExpand, record }) =>
              record.optRecordList?.length ? (
                expanded ? (
                  <RightOutlined
                    style={{ transition: 'transform 0.24s', transform: 'rotate(90deg)' }}
                    onClick={e => onExpand(record, e)}
                  />
                ) : (
                  <RightOutlined
                    style={{ transition: 'transform 0.24s' }}
                    onClick={e => onExpand(record, e)}
                  />
                )
              ) : (
                <></>
              ),
            defaultExpandedRowKeys: ['0'],
          }}
          loading={loading}
        />
      ) : (
        <Empty description="请至少添加一条措施" />
      )}
    </>
  );
}

function isDone(status: MeasureStatus): { tagColor: string; text: string; done: boolean } {
  if (status === 'DONE') {
    return { tagColor: 'green', text: '已完成', done: true };
  }
  if (status === 'UNDONE') {
    return { tagColor: 'red', text: '未完成', done: false };
  }
  return { tagColor: 'default', text: '已失效', done: false };
}
