import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';

import type {
  MeasureJson,
  RiskRegisterBaseInfoResponse,
} from '@manyun/ticket.gql.client.risk-register';

import RiskMeasureCard from './risk-measure-card';

export type RiskMeasureDeletedDrawerProps = {
  detailInfo: RiskRegisterBaseInfoResponse;
  measureData: { longMeasures: MeasureJson[]; shortMeasures: MeasureJson[] };
};

export default function RiskMeasureDeletedDrawer({
  detailInfo,
  measureData,
}: RiskMeasureDeletedDrawerProps) {
  const [visible, setVisible] = useState(false);
  return (
    <>
      <Button type="link" onClick={() => setVisible(true)}>
        查看已删除措施
      </Button>
      <Drawer
        forceRender
        title="已删除措施"
        size="large"
        placement="right"
        open={visible}
        width={849}
        onClose={() => setVisible(false)}
      >
        <RiskMeasureCard detailInfo={detailInfo} measureData={measureData} showExtra={false} />
      </Drawer>
    </>
  );
}
