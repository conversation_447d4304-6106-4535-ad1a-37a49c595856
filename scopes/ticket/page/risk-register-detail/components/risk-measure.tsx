import dayjs from 'dayjs';
import React from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Container } from '@manyun/base-ui.ui.container';
import { Empty } from '@manyun/base-ui.ui.empty';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import type {
  MeasureType,
  ProcessingReacrdResponse,
  RiskRegisterBaseInfoResponse,
  RiskRegisterMeasureInfoResponse,
} from '@manyun/ticket.gql.client.risk-register';
import type { RiskRegisterMeasureAndProcessingRecordResponse } from '@manyun/ticket.gql.client.tickets';

import AddEditMeasure from './add-edit-measure';
import CreateChange from './create-change';
import RiskMeasureCard from './risk-measure-card';
import RiskMeasureDeletedDrawer from './risk-measure-deleted-drawer';
import RiskMeasureTable from './risk-measure-table';
import { UpdateRoc } from './update-roc';

export type RiskMeasureProps = {
  isOwner: boolean;
  detailInfo: RiskRegisterBaseInfoResponse;
  measureData?: RiskRegisterMeasureInfoResponse;
  newestProcessingRecords: ProcessingReacrdResponse[];
  measuresAllData?: RiskRegisterMeasureAndProcessingRecordResponse[];
  isFull?: boolean;
  showMeasureTable?: boolean;
  updateRoc?: boolean;
  approveStatus?: string | null;
  isAuthorizedUser?: boolean;
  onRefetchDetail: () => void;
  onRefetchMeasure: () => void;
  onRefetchLatestDevelopment: () => void;
  onRefetchRiskRegisterRelates: () => void;
};

export default function RiskMeasure({
  isOwner,
  detailInfo,
  measureData,
  measuresAllData = [],
  newestProcessingRecords,
  isFull,
  showMeasureTable,
  updateRoc,
  approveStatus,
  isAuthorizedUser,
  onRefetchDetail,
  onRefetchMeasure,
  onRefetchLatestDevelopment,
  onRefetchRiskRegisterRelates,
}: RiskMeasureProps) {
  const [, { checkCode, checkUserId }] = useAuthorized();

  const isUpdateRoc = checkCode('element_ticket-risk-register-update-roc');
  const isFollowUser = [
    ...(measureData?.longMeasures ?? []),
    ...(measureData?.shortMeasures ?? []),
    ...measuresAllData,
  ].some(item => checkUserId(item.followUserId));

  return (
    <Space direction="vertical" style={{ width: '100%' }} size={16}>
      <Space>
        {((!isFull &&
          (detailInfo.riskStatus === 'WAITING_IDENTIFY'
            ? isAuthorizedUser
            : isOwner && ['HANDLING'].includes(detailInfo.riskStatus))) ||
          (isFull &&
            isOwner &&
            (detailInfo.riskStatus === 'WAITING_IDENTIFY' ||
              detailInfo.riskStatus === 'HANDLING'))) && (
          <AddEditMeasure
            text="添加风险措施"
            type={detailInfo.riskStatus === 'HANDLING' ? 'default' : 'primary'}
            riskId={detailInfo.id}
            riskStatus={detailInfo.riskStatus}
            infoPlanCompleteTime={detailInfo.planCompleteTime}
            isFull={isFull}
            onSuccess={() => {
              onRefetchMeasure();
              onRefetchDetail();
            }}
          />
        )}
        {detailInfo.riskStatus === 'HANDLING' &&
          detailInfo.riskDesc &&
          detailInfo.idcTag &&
          (isOwner || isFollowUser) && (
            <CreateChange
              blockGuid={detailInfo.blockGuid}
              riskId={detailInfo.id}
              riskDesc={detailInfo.riskDesc}
              idcTag={detailInfo.idcTag}
              onSuccess={() => {
                onRefetchRiskRegisterRelates();
              }}
            />
          )}
        {detailInfo.riskStatus === 'HANDLING' && (isOwner || isUpdateRoc) && updateRoc && (
          <UpdateRoc
            block={detailInfo.blockGuid ?? undefined}
            riskId={detailInfo.id}
            idc={detailInfo.idcTag}
            approveStatus={approveStatus}
            isUndone={measuresAllData.some(item => item.measureStatus === 'UNDONE')}
            onSuccess={() => {
              onRefetchDetail();
            }}
          />
        )}
      </Space>

      {showMeasureTable ? (
        <RiskMeasureTable
          detailInfo={detailInfo}
          measuresAllData={measuresAllData ?? []}
          isOwner={isOwner}
          onRefetchDetail={onRefetchDetail}
          onRefetchMeasure={onRefetchMeasure}
        />
      ) : (
        <Row gutter={16}>
          <Col span={18}>
            <RiskMeasureCard
              measureData={measureData}
              detailInfo={detailInfo}
              showExtra
              isOwner={isOwner}
              onRefetchDetail={onRefetchDetail}
              onRefetchMeasure={onRefetchMeasure}
              onRefetchLatestDevelopment={onRefetchLatestDevelopment}
            />
            {measureData?.deletedLongMeasures.length || measureData?.deletedShortMeasures.length ? (
              <RiskMeasureDeletedDrawer
                detailInfo={detailInfo}
                measureData={{
                  longMeasures: measureData.deletedLongMeasures,
                  shortMeasures: measureData.deletedShortMeasures,
                }}
              />
            ) : null}
          </Col>
          <Col span={6}>
            <Typography.Title showBadge level={5}>
              最新进展
            </Typography.Title>
            {newestProcessingRecords.length ? (
              <Timeline>
                {newestProcessingRecords.slice(0, 4).map(item => (
                  <Timeline.Item key={item.optRecordId}>
                    <Container color="default">
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <Space style={{ justifyContent: 'space-between', width: '100%' }}>
                          <Typography.Text type="secondary">
                            {dayjs(item.handleTime).format('YYYY-MM-DD HH:mm:ss')} |
                            <UserLink external userId={item.handlerUserId} />
                          </Typography.Text>
                          <Tag color={measureTypeIsLong(item.measureType) ? 'cyan' : 'geekblue'}>
                            {measureTypeIsLong(item.measureType) ? '长期措施' : '短期措施'}
                          </Tag>
                        </Space>
                        <Typography.Paragraph
                          ellipsis={{ tooltip: item.handleContent, rows: 2 }}
                          style={{ marginBottom: 0 }}
                        >
                          {item.handleContent}
                        </Typography.Paragraph>
                        <Typography.Text type="secondary" ellipsis={{ tooltip: item.measureDesc }}>
                          对应措施：{item.measureDesc}
                        </Typography.Text>
                        {Array.isArray(item.fileInfoList) && item.fileInfoList.length ? (
                          <SimpleFileList files={item.fileInfoList}>
                            <Typography.Link>{item.fileInfoList[0].name}</Typography.Link>
                          </SimpleFileList>
                        ) : null}
                      </Space>
                    </Container>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <Empty description="暂无处理记录" />
            )}
          </Col>
        </Row>
      )}
    </Space>
  );
}

function measureTypeIsLong(measureType?: MeasureType | null): boolean {
  return measureType === 'LONG';
}
