import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import {
  type MeasureJson,
  useLazyRiskRegisterLatestDevelopment,
} from '@manyun/ticket.gql.client.risk-register';
import type { RiskRegisterMeasureAndProcessingRecordResponse } from '@manyun/ticket.gql.client.tickets';
import { updateRiskRegisterMeasureStatus } from '@manyun/ticket.service.update-risk-register-measure-status';
import type { ApiQ } from '@manyun/ticket.service.update-risk-register-measure-status';

export type MeasureConfirmButtonProps = {
  riskId: string;
  measure: MeasureJson | RiskRegisterMeasureAndProcessingRecordResponse;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function MeasureConfirmButton({ riskId, measure, onSuccess }: MeasureConfirmButtonProps) {
  const [visible, setVisible] = useState(false);
  const [canConfirm, setCanConfirm] = useState(false);
  const [form] = Form.useForm<ApiQ>();

  const [fetchLatestDevelopment, { loading }] = useLazyRiskRegisterLatestDevelopment();

  return (
    <>
      <Popconfirm
        title={
          canConfirm ? (
            <Form form={form}>
              <Form.Item
                label="实际完成时间"
                name="completeTime"
                style={{ marginBottom: 0 }}
                rules={[{ required: true, message: '实际完成时间必选！' }]}
              >
                <DatePicker
                  picker="date"
                  showTime
                  format="YYYY-MM-DD HH:mm"
                  disabledDate={currentDate => {
                    return currentDate && currentDate.diff(moment().endOf('day')) > 0;
                  }}
                  disabledTime={current => {
                    return {
                      disabledHours: () => {
                        if (current?.get('date') === moment().get('date')) {
                          return range(moment().get('hour') + 1, 24);
                        }
                        return [];
                      },
                      disabledMinutes: selectedHour => {
                        if (
                          current?.get('date') === moment().get('date') &&
                          selectedHour === moment().get('hour')
                        ) {
                          return range(moment().get('minute'), 60);
                        }
                        return [];
                      },
                    };
                  }}
                  // @ts-ignore getPopupContainer 存在
                  getPopupContainer={trigger => trigger.parentNode.parentNode}
                />
              </Form.Item>
            </Form>
          ) : (
            '至少添加一条处理记录，才可操作完成'
          )
        }
        open={visible}
        icon={canConfirm ? null : <ExclamationCircleFilled />}
        style={{ width: 290 }}
        okText={canConfirm ? '确认完成' : '我知道了'}
        okButtonProps={{ disabled: loading }}
        showCancel={canConfirm ? true : false}
        onOpenChange={async visible => {
          if (visible) {
            const { data } = await fetchLatestDevelopment({
              variables: {
                query: {
                  riskId,
                  measureId: measure.measureId,
                },
              },
            });
            setVisible(true);
            if (data?.riskRegisterLatestDevelopment.length) {
              setCanConfirm(true);
            }
          }
        }}
        onCancel={e => {
          e?.stopPropagation();
          setVisible(false);
        }}
        onConfirm={async e => {
          if (canConfirm) {
            e?.stopPropagation();
            form.validateFields().then(async formValue => {
              const { error } = await updateRiskRegisterMeasureStatus({
                riskId: measure.riskId,
                measureId: measure.measureId,
                measureStatus: 'DONE',
                completeTime: moment(formValue.completeTime).format('YYYY-MM-DD HH:mm'),
              });
              if (error) {
                message.error(error.message);
                return;
              }
              onSuccess && onSuccess();
              setVisible(false);
              setCanConfirm(false);
            });
          } else {
            setVisible(false);
          }
        }}
      >
        <Button compact type="link" onClick={event => event.stopPropagation()}>
          完成
        </Button>
      </Popconfirm>
    </>
  );
}

const range = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
