import dayjs from 'dayjs';
import difference from 'lodash/difference';
import React, { useEffect, useState } from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Empty } from '@manyun/base-ui.ui.empty';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { useLazyRiskRegisterLatestDevelopment } from '@manyun/ticket.gql.client.risk-register';
import type {
  MeasureJson,
  MeasureStatus,
  ProcessingReacrdResponse,
  RiskRegisterBaseInfoResponse,
} from '@manyun/ticket.gql.client.risk-register';
import { deleteRiskRegisterMeasure } from '@manyun/ticket.service.delete-risk-register-measure';
import { updateRiskRegisterMeasureStatus } from '@manyun/ticket.service.update-risk-register-measure-status';

import AddEditMeasure from './add-edit-measure';
import AddMeasureProcessingRecordButton from './add-measure-processing-record';
import { MeasureConfirmButton } from './measure-confirm-button';
import styles from './risk-measure-card.module.less';
import { useRiskRegisterContext } from './risk-register-context';

export type RiskMeasureCardProps = {
  detailInfo: RiskRegisterBaseInfoResponse;
  measureData?: { longMeasures: MeasureJson[]; shortMeasures: MeasureJson[] };
  showExtra: boolean;
  isOwner?: boolean;
  onRefetchDetail?: () => void;
  onRefetchMeasure?: () => void;
  onRefetchLatestDevelopment?: () => void;
};

export default function RiskMeasureCard({
  detailInfo,
  measureData,
  showExtra = true,
  isOwner,
  onRefetchDetail,
  onRefetchMeasure,
  onRefetchLatestDevelopment,
}: RiskMeasureCardProps) {
  const [collapseactiveKey, setCollapseactiveKey] = useState<string[]>([]);
  const [processingRecords, setProcessingRecords] = useState<
    Record<number, ProcessingReacrdResponse[]>
  >({});
  const [loading, setLoading] = useState<boolean>(false);
  const [, { checkUserId }] = useAuthorized();

  const { isExportPDF, setExportPDFTabs } = useRiskRegisterContext();
  const isAuthorizedUser = (detailInfo.authorizedUserList ?? []).some(item => checkUserId(item));

  const [fetchLatestDevelopment] = useLazyRiskRegisterLatestDevelopment({
    onCompleted: data => {
      if (data.riskRegisterLatestDevelopment.length) {
        const measureId = data.riskRegisterLatestDevelopment[0].measureId;
        setProcessingRecords({
          ...processingRecords,
          [measureId]: data.riskRegisterLatestDevelopment,
        });
      }
    },
  });

  useEffect(() => {
    if (isExportPDF) {
      if (measureData?.longMeasures.length) {
        Promise.all(
          measureData.longMeasures.map(item =>
            fetchLatestDevelopment({
              variables: {
                query: {
                  riskId: detailInfo.id,
                  measureId: Number(item.measureId),
                },
              },
            })
          )
        )
          .then(results => {
            const hasData = results.filter(res => res.data?.riskRegisterLatestDevelopment.length);
            const ids = hasData.map(item => {
              const id = item.data?.riskRegisterLatestDevelopment[0].measureId.toString();
              setProcessingRecords(processingRecords => ({
                ...processingRecords,
                [id]: item.data!.riskRegisterLatestDevelopment!,
              }));
              return id;
            });
            setCollapseactiveKey(ctiveKey => [...ids, ...ctiveKey]);

            setExportPDFTabs(exportPDFTabs => {
              return exportPDFTabs.map(item => ({
                ...item,
                isValid: item.key === 'longMeasure' ? !!hasData.length : item.isValid,
                isRendered: item.key === 'longMeasure' ? true : item.isRendered,
              }));
            });
          })
          .catch(error => {
            // eslint-disable-next-line no-console
            console.error(error);
          });
      } else {
        setExportPDFTabs(exportPDFTabs => {
          return exportPDFTabs.map(item => ({
            ...item,
            isValid: item.key === 'longMeasure' ? false : item.isValid,
            isRendered: item.key === 'longMeasure' ? true : item.isRendered,
          }));
        });
      }
      if (measureData?.shortMeasures.length) {
        Promise.all(
          measureData.shortMeasures.map(item =>
            fetchLatestDevelopment({
              variables: {
                query: {
                  riskId: detailInfo.id,
                  measureId: Number(item.measureId),
                },
              },
            })
          )
        )
          .then(results => {
            const hasData = results.filter(res => res.data?.riskRegisterLatestDevelopment.length);
            const ids = hasData.map(item => {
              const id = item.data?.riskRegisterLatestDevelopment[0].measureId.toString();
              setProcessingRecords(processingRecords => ({
                ...processingRecords,
                [id]: item.data!.riskRegisterLatestDevelopment!,
              }));
              return id;
            });
            setCollapseactiveKey(ctiveKey => [...ids, ...ctiveKey]);
            setExportPDFTabs(exportPDFTabs => {
              return exportPDFTabs.map(item => ({
                ...item,
                isValid: item.key === 'shortMeasure' ? !!hasData.length : item.isValid,
                isRendered: item.key === 'shortMeasure' ? true : item.isRendered,
              }));
            });
          })
          .catch(error => {
            // eslint-disable-next-line no-console
            console.error(error);
          });
      } else {
        setExportPDFTabs(exportPDFTabs => {
          return exportPDFTabs.map(item => ({
            ...item,
            isValid: item.key === 'shortMeasure' ? false : item.isValid,
            isRendered: item.key === 'shortMeasure' ? true : item.isRendered,
          }));
        });
      }
    }
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getMeasureHeader = ({
    measureStatus,
    measureDesc,
    followUserId,
    planCompleteTime,
    completeTime,
  }: MeasureJson) => {
    const isdone = isDone(measureStatus);

    return (
      <Space direction="vertical" style={{ width: '100%' }}>
        <div style={{ display: 'flex' }}>
          <Tag color={isdone ? 'green' : 'red'}>{isdone ? '已完成' : '未完成'}</Tag>
          <Typography.Title
            ellipsis={{ tooltip: measureDesc }}
            level={5}
            style={{ margin: '0 16px' }}
          >
            {measureDesc}
          </Typography.Title>
        </div>
        <Space size={32}>
          <Typography.Text type="secondary">
            跟进人：
            <UserLink external userId={followUserId} />
          </Typography.Text>
          <Typography.Text type="secondary">
            计划完成时间：
            {dayjs(planCompleteTime).format('YYYY-MM-DD HH:mm')}
          </Typography.Text>
          <Typography.Text type="secondary">
            实际完成时间：
            {completeTime ? dayjs(completeTime).format('YYYY-MM-DD HH:mm') : '--'}
          </Typography.Text>
        </Space>
      </Space>
    );
  };

  const processingView = ({
    handleContent,
    optRecordId,
    handleTime,
    handlerUserId,
    fileInfoList,
  }: ProcessingReacrdResponse) => (
    <Timeline.Item key={optRecordId}>
      <Space direction="vertical">
        <Typography.Text type="secondary">
          {dayjs(handleTime).format('YYYY-MM-DD HH:mm:ss')} | <UserLink userId={handlerUserId} />
        </Typography.Text>
        <Typography.Text>{handleContent}</Typography.Text>
        {Array.isArray(fileInfoList) && fileInfoList.length ? (
          <SimpleFileList files={fileInfoList}>
            <Typography.Link>{fileInfoList[0].name}</Typography.Link>
          </SimpleFileList>
        ) : null}
      </Space>
    </Timeline.Item>
  );

  const onDelete = async (measure: MeasureJson) => {
    setLoading(true);
    const { error } = await deleteRiskRegisterMeasure({
      riskId: measure.riskId,
      measureId: measure.measureId,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    onRefetchMeasure && onRefetchMeasure();
    onRefetchDetail && onRefetchDetail();
  };

  const getMeasureExtra = (measure: MeasureJson) => {
    const isHandling = detailInfo.riskStatus === 'HANDLING';
    const canOperable = detailInfo.riskStatus === 'WAITING_IDENTIFY' ? isAuthorizedUser : isOwner;
    if (detailInfo.riskStatus === 'WAITING_IDENTIFY' || isHandling) {
      const edit = (
        <AddEditMeasure
          text="编辑"
          type={!isDone(measure.measureStatus) && isHandling ? 'text' : 'link'}
          compact
          riskId={detailInfo.id}
          riskStatus={detailInfo.riskStatus}
          values={measure}
          measureId={measure.measureId}
          onSuccess={() => {
            onRefetchMeasure && onRefetchMeasure();
            onRefetchDetail && onRefetchDetail();
          }}
        />
      );
      const deleteMeasure = (
        <Popconfirm
          title={
            isHandling
              ? '确认删除风险措施？删除后风险单将回退到“待识别”状态，请谨慎操作！'
              : '确认删除风险措施？删除后不可撤回，请谨慎操作！'
          }
          style={{ width: 290 }}
          okText="确认删除"
          cancelText="我再想想"
          onCancel={e => e?.stopPropagation()}
          onConfirm={async e => {
            e?.stopPropagation();
            onDelete(measure);
          }}
        >
          <Button
            loading={loading}
            compact
            type={!isDone(measure.measureStatus) && isHandling ? 'text' : 'link'}
            onClick={event => event.stopPropagation()}
          >
            删除
          </Button>
        </Popconfirm>
      );

      if (isDone(measure.measureStatus)) {
        return (
          <Space>
            {isHandling && isOwner && (
              <Button
                type="link"
                compact
                loading={loading}
                onClick={async event => {
                  event.stopPropagation();
                  setLoading(true);
                  const { error } = await updateRiskRegisterMeasureStatus({
                    riskId: measure.riskId,
                    measureId: measure.measureId,
                    measureStatus: 'UNDONE',
                  });
                  setLoading(false);
                  if (error) {
                    message.error(error.message);
                    return;
                  }
                  onRefetchMeasure && onRefetchMeasure();
                }}
              >
                置为未完成
              </Button>
            )}
            {canOperable && edit}
            {canOperable && deleteMeasure}
          </Space>
        );
      }
      if (!isDone(measure.measureStatus)) {
        return (
          <Space>
            {isHandling && (
              <AddMeasureProcessingRecordButton
                riskId={measure.riskId}
                riskStatus={detailInfo.riskStatus}
                measureId={measure.measureId}
                blockGuid={detailInfo.blockGuid}
                onSuccess={() => {
                  onRefetchLatestDevelopment && onRefetchLatestDevelopment();
                  setCollapseactiveKey([]);
                }}
              />
            )}
            {isHandling && isOwner && (
              <MeasureConfirmButton
                riskId={detailInfo.id}
                measure={measure}
                onSuccess={() => {
                  onRefetchMeasure && onRefetchMeasure();
                }}
              />
            )}
            {isHandling && isOwner ? (
              <Dropdown.Button
                type="link"
                menu={{
                  items: [
                    {
                      key: 'edit',
                      label: edit,
                    },
                    {
                      key: 'deleteMeasure',
                      label: deleteMeasure,
                    },
                  ],
                }}
                buttonsRender={([leftButton, rightButton]) => [null, rightButton]}
                onClick={e => e.stopPropagation()}
              />
            ) : (
              <>
                {canOperable && edit}
                {canOperable && deleteMeasure}
              </>
            )}
          </Space>
        );
      }
    }
    return null;
  };

  return (
    <Space direction="vertical" style={{ width: '100%' }} size={24}>
      <Space direction="vertical" style={{ width: '100%' }}>
        <Typography.Title showBadge level={5}>
          短期措施
        </Typography.Title>
        {measureData?.shortMeasures?.length ? (
          <Collapse
            className={styles.collapse}
            activeKey={collapseactiveKey}
            collapsible="header"
            onChange={keys => {
              const measureId = difference(keys, collapseactiveKey);
              if (measureId.length === 1) {
                if (keys.length > collapseactiveKey.length) {
                  fetchLatestDevelopment({
                    variables: {
                      query: {
                        riskId: detailInfo.id,
                        measureId: Number(measureId[0]),
                      },
                    },
                  });
                }
                setProcessingRecords({ ...processingRecords, [measureId[0]]: [] });
              }

              setCollapseactiveKey(keys as string[]);
            }}
          >
            {measureData.shortMeasures.map(item => (
              <Collapse.Panel
                key={item.measureId.toString()}
                header={getMeasureHeader(item)}
                extra={showExtra && getMeasureExtra(item)}
                style={{ width: '100%', overflow: 'hidden' }}
                forceRender
              >
                {collapseactiveKey?.includes(item.measureId.toString()) ? (
                  processingRecords[item.measureId].length ? (
                    <Timeline style={{ padding: '8px 0 0 8px', marginBottom: -20 }}>
                      {processingRecords[item.measureId]?.map(processingRecord =>
                        processingView(processingRecord)
                      )}
                    </Timeline>
                  ) : (
                    <Typography.Text disabled style={{ padding: '8px 0 0 8px', marginBottom: -20 }}>
                      暂无处理记录
                    </Typography.Text>
                  )
                ) : null}
              </Collapse.Panel>
            ))}
          </Collapse>
        ) : (
          <Empty description="暂无短期措施" />
        )}
      </Space>
      <Space direction="vertical" style={{ width: '100%', marginBottom: 24 }}>
        <Typography.Title showBadge level={5}>
          长期措施
        </Typography.Title>
        {measureData?.longMeasures?.length ? (
          <Collapse
            className={styles.collapse}
            activeKey={collapseactiveKey}
            collapsible="header"
            // style={{ padding: '24px 0' }}
            onChange={keys => {
              const measureId = difference(keys, collapseactiveKey);
              if (measureId.length === 1) {
                if (keys.length > collapseactiveKey.length) {
                  fetchLatestDevelopment({
                    variables: {
                      query: {
                        riskId: detailInfo.id,
                        measureId: Number(measureId[0]),
                      },
                    },
                  });
                }
                setProcessingRecords({ ...processingRecords, [measureId[0]]: [] });
              }

              setCollapseactiveKey(keys as string[]);
            }}
          >
            {measureData.longMeasures.map(item => (
              <Collapse.Panel
                key={item.measureId}
                header={getMeasureHeader(item)}
                extra={showExtra && getMeasureExtra(item)}
              >
                {collapseactiveKey?.includes(item.measureId.toString()) ? (
                  processingRecords[item.measureId].length ? (
                    <Timeline style={{ padding: '8px 0 0 8px', marginBottom: -20 }}>
                      {processingRecords[item.measureId]?.map(processingRecord =>
                        processingView(processingRecord)
                      )}
                    </Timeline>
                  ) : (
                    <Typography.Text disabled style={{ padding: '8px 0 0 8px', marginBottom: -20 }}>
                      暂无处理记录
                    </Typography.Text>
                  )
                ) : null}
              </Collapse.Panel>
            ))}
          </Collapse>
        ) : (
          <Empty description="暂无长期措施" />
        )}
      </Space>
    </Space>
  );
}

function isDone(status: MeasureStatus): boolean {
  return status === 'DONE';
}
