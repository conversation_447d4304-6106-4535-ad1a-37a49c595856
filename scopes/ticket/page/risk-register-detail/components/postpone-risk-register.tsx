import moment from 'moment';
import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { usePostponeRiskRegister } from '@manyun/ticket.gql.client.tickets';

export type PostponeRiskProps = {
  riskId: string;
  planCompleteTime?: string | null;
  isFull?: boolean;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;
function generateRange(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export default function PostponeRisk({
  riskId,
  planCompleteTime,
  isFull,
  onSuccess,
  ...restProps
}: PostponeRiskProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  const [postponeRiskRegister, { loading: postponeRiskRegisterLoading }] = usePostponeRiskRegister({
    onCompleted: data => {
      if (!data.postponeRiskRegister?.success) {
        message.error(data.postponeRiskRegister?.message);
        return;
      }
      setVisible(false);
      onSuccess();
    },
  });

  return (
    <>
      <Button
        {...restProps}
        onClick={event => {
          setVisible(true);
        }}
      >
        延期
      </Button>
      <Modal
        title="风险延期"
        open={visible}
        okText="确认延期"
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: postponeRiskRegisterLoading }}
        onCancel={event => {
          event.stopPropagation();
          setVisible(false);
        }}
        onOk={async event => {
          event.stopPropagation();
          form.validateFields().then(async values => {
            postponeRiskRegister({
              variables: {
                query: {
                  riskId,
                  ...values,
                  targetTime: values.targetTime.valueOf(),
                },
              },
            });
          });
        }}
      >
        <Alert
          description="风险延期提交后，将进行延期申请审批，是否确认延期？"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form}>
          <Form.Item label="原计划完成时间" name="measureType">
            {moment(planCompleteTime).format('YYYY-MM-DD HH:mm')}
          </Form.Item>
          <Form.Item
            label="延期至"
            name="targetTime"
            rules={[{ required: true, message: '延期至必选！' }]}
          >
            <DatePicker
              picker="date"
              showNow={false}
              disabledDate={current => {
                /**不可早于计划开始时间 */
                return current < moment(planCompleteTime).startOf('day');
              }}
              disabledTime={current => {
                const compareMoment = moment(planCompleteTime);
                const hour = compareMoment.hour();
                const minute = compareMoment.minute();
                const second = compareMoment.second();
                if (current && current.isSame(compareMoment, 'day')) {
                  const currentHour = current.hour();
                  const currentMinute = current.minute();
                  if (currentHour === hour) {
                    return {
                      disabledHours: () => generateRange(0, hour),
                      disabledMinutes: () => generateRange(0, minute + 1),
                      disabledSeconds: () =>
                        currentMinute === minute ? generateRange(0, second) : [],
                    };
                  } else {
                    return {
                      disabledHours: () => generateRange(0, hour),
                      disabledMinutes: () => [],
                      disabledSeconds: () => [],
                    };
                  }
                }
                return {
                  disabledHours: () => [],
                  disabledMinutes: () => [],
                  disabledSeconds: () => [],
                };
              }}
              showTime
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
          <Form.Item
            label="延期原因"
            name="reason"
            rules={[
              { required: true, whitespace: true, message: '延期原因必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
