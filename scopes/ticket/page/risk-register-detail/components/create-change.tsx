import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useCreateChangeByRiskRegister } from '@manyun/ticket.gql.client.risk-register';
import { useLazyChangeVersions } from '@manyun/ticket.gql.client.tickets';
import { ChangeOfflineForm, getPlantime } from '@manyun/ticket.page.change-offline';
import type { ChangeOfflineFormValues } from '@manyun/ticket.page.change-offline';
import { ChangeOfflineFormBeta } from '@manyun/ticket.page.change-offline-mutator';
import { generateChangeOnlineCreateLocation } from '@manyun/ticket.route.ticket-routes';

export type AddEditMeasureProps = {
  riskId: string;
  blockGuid?: string | null;
  idcTag: string;
  riskDesc: string;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export default function CreateChange({
  riskId,
  blockGuid,
  riskDesc,
  idcTag,
  onSuccess,
  ...restProps
}: AddEditMeasureProps) {
  const [visible, setVisible] = useState<'oldCreate' | 'newCreate' | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [getChangeVersions, { data }] = useLazyChangeVersions();
  const [configUtil] = useConfigUtil();

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const showNewDetail = ticketScopeCommonConfigs.changes.features.showNewDetail;

  const [form] = Form.useForm<
    ChangeOfflineFormValues & {
      operator?: { label: string; value: number };
      respPerson?: { label: string; value: number };
      relateCustomer?: string | null;
      influenceArea?: string | null;
      template?: { label: string; value: string };
    }
  >();

  useEffect(() => {
    getChangeVersions();
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [createChangeByRiskRegister, { loading }] = useCreateChangeByRiskRegister({
    onCompleted: data => {
      if (!data.createChangeByRiskRegister?.success) {
        message.error(data.createChangeByRiskRegister?.message);
        return;
      }
      onSuccess();
      setVisible(null);
      message.success('创建成功');
    },
  });

  return (
    <>
      {showNewDetail && (
        <Button type="default">
          <Link
            to={generateChangeOnlineCreateLocation({
              changeSourceNo: riskId,
              changeSourceType: 'RISK',
              spaceGuid: blockGuid ?? idcTag,
            })}
            target="_blank"
          >
            创建变更
          </Link>
        </Button>
      )}

      {!showNewDetail && (
        <Button
          {...restProps}
          onClick={() => {
            if ((data?.changeVersions.data ?? []).includes(idcTag)) {
              setVisible('newCreate');
              form.setFieldValue('blockGuid', blockGuid ?? idcTag);
            } else {
              setVisible('oldCreate');
              form.setFieldValue('blockGuid', blockGuid ?? idcTag);
            }
          }}
        >
          创建变更
        </Button>
      )}

      <Drawer
        forceRender
        title="创建变更单"
        size="large"
        placement="right"
        open={!!visible}
        width={720}
        extra={
          <Space>
            <Button
              onClick={() => {
                form.resetFields();
                setVisible(null);
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              loading={loading || submitLoading}
              onClick={() => {
                form.validateFields().then(async values => {
                  const {
                    title,
                    changeInfluence,
                    riskLevel,
                    customerFileInfoList,
                    changeType,
                    planEndTime,
                    planStartTime,
                    reason,
                    fileInfoList,
                    blockGuid,
                    changeDeviceList,
                    stepList,
                    changeTimeList,
                    purpose,
                    changeSource,
                    influenceArea,
                    operator,
                    respPerson,
                    relateCustomer,
                    template,
                  } = values;
                  const planTime = changeTimeList?.length ? getPlantime({ changeTimeList }) : null;

                  createChangeByRiskRegister({
                    variables: {
                      query: {
                        riskId,
                        title,
                        idcTag,
                        influenceDesc: changeInfluence,
                        changeTypeCode: changeType,
                        planEndTime: planTime ? planTime.planEndTime : planEndTime.valueOf(),
                        planStartTime: planTime ? planTime.planStartTime : planStartTime.valueOf(),
                        changeLevel: riskLevel,
                        fileInfoList: fileInfoList?.map(obj => ({
                          ...McUploadFile.fromApiObject(
                            McUploadFile.fromJSON(obj).toApiObject()
                          ).toJSON(),
                        })),
                        customerFiles: customerFileInfoList?.map((obj: McUploadFile) => ({
                          ...McUploadFile.fromApiObject(
                            McUploadFile.fromJSON(obj).toApiObject()
                          ).toJSON(),
                        })),
                        changeMajorCode: visible === 'oldCreate' ? reason : reason?.value,
                        blockGuid,
                        stepList: stepList
                          ? stepList.map(item => ({
                              stepName: item.stepName,
                              stepOrder: item.stepOrder,
                              operatorId: item.operator?.value,
                              operatorName: item.operator?.label ?? '',
                              stepDesc: item.stepDesc,
                            }))
                          : [],
                        changeTimeList: planTime ? planTime.changeTimeList : [],
                        sourceNo: changeSource?.id,
                        sourceType: changeSource?.type,
                        purpose,
                        changeDeviceList: changeDeviceList?.map(item => ({
                          deviceGuid: item.deviceGuid,
                          deviceName: item.deviceName,
                          roomTag: item.roomTag,
                          deviceType: item.deviceType,
                          inhibition: item.inhibition,
                        })),
                        changeVersion: visible === 'newCreate' ? 2 : 1,
                        influenceArea,
                        operatorId: operator?.value,
                        operatorName: operator?.label,
                        respPersonId: respPerson?.value,
                        respPersonName: respPerson?.label,
                        relateCustomer,
                        templateId: template ? template.value : null,
                        templateName: template ? template.label : null,
                      },
                    },
                  });
                });
              }}
            >
              提交
            </Button>
          </Space>
        }
        onClose={() => setVisible(null)}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          <Alert message="关联风险" description={`${riskId} - ${riskDesc}`} type="info" />
          {visible === 'oldCreate' ? (
            <ChangeOfflineForm
              form={form}
              blockGuid={blockGuid}
              setSubmitting={setSubmitLoading}
              idcTag={idcTag}
            />
          ) : (
            <ChangeOfflineFormBeta
              form={form}
              blockGuid={blockGuid}
              setSubmitting={setSubmitLoading}
              idcTag={idcTag}
            />
          )}
        </Space>
      </Drawer>
    </>
  );
}
