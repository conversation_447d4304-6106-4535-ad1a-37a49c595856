import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import moment from 'moment';
import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import type { RiskStatus } from '@manyun/ticket.gql.client.risk-register';
import { createRiskRegisterMeasureProcessingRecord } from '@manyun/ticket.service.create-risk-register-measure-processing-record';

export type AddMeasureProcessingRecordProps = {
  riskId: string;
  riskStatus: RiskStatus;
  measureId?: number;
  blockGuid?: string | null;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export default function AddMeasureProcessingRecordButton({
  riskId,
  measureId,
  riskStatus,
  blockGuid,
  onSuccess,
  ...restProps
}: AddMeasureProcessingRecordProps) {
  const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm();

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  return (
    <>
      <Button
        {...restProps}
        type="link"
        compact
        onClick={event => {
          event.stopPropagation();
          setVisible(true);
        }}
      >
        添加处理记录
      </Button>
      <Modal
        title="添加处理记录"
        open={visible}
        okText="提交"
        okButtonProps={{ loading: submitLoading }}
        afterClose={() => form.resetFields()}
        onCancel={event => {
          event.stopPropagation();
          setVisible(false);
          setSubmitLoading(false);
        }}
        onOk={async event => {
          event.stopPropagation();
          form.validateFields().then(async values => {
            setSubmitLoading(true);
            if (measureId) {
              const { error } = await createRiskRegisterMeasureProcessingRecord({
                riskId,
                measureId,
                ...values,
                handleTime: moment(values.handleTime).format('YYYY-MM-DD HH:mm:ss'),
                fileInfoList: values.fileInfoList
                  ? values.fileInfoList.map((item: McUploadFileJSON) =>
                      McUploadFile.fromJSON({ ...item, type: 'RISK_REGISTER_OPT' }).toApiObject()
                    )
                  : [],
              });
              setSubmitLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
            }
            message.success('提交成功');
            setVisible(false);
            onSuccess();
          });
        }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} form={form}>
          <Form.Item
            label="处理人"
            name="handlerUserId"
            rules={[{ required: true, message: '处理人必选！' }]}
          >
            <UserSelect labelInValue={false} />
          </Form.Item>
          <Form.Item
            label="处理时间"
            name="handleTime"
            rules={[{ required: true, message: '处理时间必选！' }]}
          >
            <DatePicker
              picker="date"
              showTime
              format="YYYY-MM-DD HH:mm"
              disabledDate={currentDate => {
                return currentDate && currentDate.valueOf() > moment().endOf('day').valueOf();
              }}
              disabledTime={current => {
                if (current) {
                  const compareMoment = moment();
                  const hours = compareMoment.hours();
                  const minute = compareMoment.minute();
                  const choseHour = current.hours();
                  const isToday = current.isSame(moment(), 'day');
                  if (isToday) {
                    if (choseHour === hours) {
                      return {
                        disabledHours: () => range(hours + 1, 24),
                        disabledMinutes: () => range(minute + 1, 60),
                      };
                    }
                    return {
                      disabledHours: () => range(hours + 1, 24),
                    };
                  }
                }
                return {};
              }}
            />
          </Form.Item>
          <Form.Item
            label="处理内容"
            name="handleContent"
            rules={[
              { required: true, whitespace: true, message: '处理内容必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
          <Form.Item
            label="附件"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <Upload
              accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>点此上传</Button>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
