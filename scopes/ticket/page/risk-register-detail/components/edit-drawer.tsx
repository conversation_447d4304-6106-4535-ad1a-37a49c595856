import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import {
  type RiskObject,
  type RiskStatus,
  useLazyRiskRegister,
} from '@manyun/ticket.gql.client.risk-register';
import {
  RiskRegisterForm,
  type RiskRegisterFormValues,
} from '@manyun/ticket.page.risk-register-mutator';
import { updateRiskRegister } from '@manyun/ticket.service.update-risk-register';

export type EditFormDrawerProps = {
  values: Omit<RiskRegisterFormValues, 'fileInfoList' | 'riskObjects'> & {
    id: string;
    riskStatus: RiskStatus;
    riskObjects?: RiskObject[] | null;
  };
  text: string;
  title: string;
  measuresPlanTime?: number;
  onSuccess: () => void;
};

export function EditFormDrawer({
  values,
  text,
  title,
  measuresPlanTime,
  onSuccess,
}: EditFormDrawerProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';

  const [fetchDetail, { data }] = useLazyRiskRegister();
  return (
    <>
      <Button
        type="link"
        compact
        onClick={async () => {
          setVisible(true);
          const { data } = await fetchBizFileInfos({
            targetId: values.id,
            targetType: 'RISK_REGISTER',
            extensionType: 'RISK_REGISTER',
          });
          const { data: detailInfo } = await fetchDetail({
            variables: { query: { riskId: values.id } },
          });
          form.setFieldsValue({
            ...values,
            blockGuid: values.blockGuid ?? values.idcTag,
            fileInfoList: data.data ?? [],
            locationList: detailInfo?.riskRegister?.locationList ?? [],
            riskOwnerIdList: values.riskOwnerIdList ?? [],
            planCompleteTime: values.planCompleteTime ? moment(values.planCompleteTime) : undefined,
            relateEventIdList:
              (detailInfo?.riskRegister?.questionRelateEventList ?? []).map(item => item.eventId) ??
              [],
            riskObjects:
              values.riskObjectType === 'OTHER'
                ? values.riskObjects?.[0]?.label
                : values.riskObjects?.map(item => ({
                    ...item,
                    deviceName: item.objectName,
                    deviceGuid: item.objectGuid,
                  })),
          });
        }}
      >
        {text}
      </Button>
      <Drawer
        forceRender
        title={title}
        size="large"
        placement="right"
        open={visible}
        width={isFull ? 1350 : 752}
        extra={
          <Space>
            <Button
              onClick={() => {
                form.resetFields();

                setVisible(false);
              }}
            >
              取消
            </Button>
            {values.riskStatus === 'DRAFT' && (
              <Button
                type="primary"
                loading={loading}
                onClick={async () => {
                  setLoading(true);
                  const formValues = form.getFieldsValue();

                  const { error } = await updateRiskRegister({
                    ...formValues,
                    commit: false,
                    id: values.id,
                    immutableEventIdList: data?.riskRegister?.immutableEventIdList,
                  });
                  setLoading(false);
                  if (error) {
                    message.error(error.message);
                    return;
                  }
                  onSuccess();
                  message.success('保存草稿成功');
                  setVisible(false);
                }}
              >
                保存草稿
              </Button>
            )}
            <Button
              type="primary"
              loading={loading}
              onClick={() => {
                form.validateFields().then(async formValues => {
                  setLoading(true);
                  const { error } = await updateRiskRegister({
                    ...formValues,
                    commit: true,
                    id: values.id,
                    immutableEventIdList: data?.riskRegister?.immutableEventIdList,
                  });
                  setLoading(false);
                  if (error) {
                    message.error(error.message);
                    return;
                  }
                  onSuccess();
                  message.success('提交成功');
                  setVisible(false);
                });
              }}
            >
              提交
            </Button>
          </Space>
        }
        onClose={() => setVisible(false)}
      >
        <RiskRegisterForm
          form={form}
          oldRiskLevel={values.riskLevel}
          riskStatus={values?.riskStatus}
          locationDisabled
          measuresPlanTime={measuresPlanTime}
          immutableEventIdList={data?.riskRegister?.immutableEventIdList}
        />
      </Drawer>
    </>
  );
}
