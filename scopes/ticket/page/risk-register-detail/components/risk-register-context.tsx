import { createContext, useContext } from 'react';

export type ExportPDFTabs = {
  isValid: boolean;
  key: string;
  isRendered: boolean;
  title: string;
};

export type RiskRegisterContextValue = {
  isExportPDF: boolean;
  exportPDFTabs: ExportPDFTabs[];
  setIsExportPDF: (mode: boolean) => void;
  setExportPDFTabs: React.Dispatch<React.SetStateAction<ExportPDFTabs[]>>;
};

export const initTabs = [
  { key: 'shortMeasure', isValid: true, isRendered: false, title: '短期措施' },
  { key: 'longMeasure', isValid: true, isRendered: false, title: '长期措施' },
  { key: 'relateEvent', isValid: true, isRendered: false, title: '问题关联事件' },
  { key: 'processingRecords', isValid: true, isRendered: false, title: '最新进展' },
  { key: 'influenceSuface', isValid: true, isRendered: false, title: '影响范围' },
  { key: 'relatedMatters', isValid: true, isRendered: false, title: '关联事项' },
];

export const RiskRegisterContext = createContext<RiskRegisterContextValue>({
  isExportPDF: false,
  exportPDFTabs: initTabs,
  setIsExportPDF: () => {},
  setExportPDFTabs: () => {},
});

export function useRiskRegisterContext() {
  return useContext(RiskRegisterContext);
}
