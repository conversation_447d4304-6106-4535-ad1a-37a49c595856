import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import {
  useLazyUsersByRoleSpace,
  useUpdateRiskRegisterRoc,
} from '@manyun/ticket.gql.client.tickets';

export type UpdateRocProps = {
  riskId: string;
  idc?: string | null;
  block?: string | null;
  isUndone?: boolean;
  approveStatus?: string | null;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function UpdateRoc({
  riskId,
  type = 'primary',
  idc,
  block,
  isUndone,
  approveStatus,
  onSuccess,
}: UpdateRocProps) {
  const [visible, setVisible] = useState(false);

  const [form] = Form.useForm();

  const [canCloseRiskRegister, { loading }] = useUpdateRiskRegisterRoc({
    onCompleted(data) {
      if (data.updateRiskRegisterRoc?.message) {
        message.error(data.updateRiskRegisterRoc.message);
        return;
      }
      message.success('提交成功');
      setVisible(false);
      onSuccess();
    },
  });
  const [getUsersByRoleSpace] = useLazyUsersByRoleSpace({
    onCompleted(data) {
      if (data.usersByRoleSpace) {
        form.setFieldValue(
          'upgradeTo',
          data.usersByRoleSpace.map(item => item.id)
        );
      }
    },
  });

  const onSubmit = async () => {
    form.validateFields().then(async values => {
      canCloseRiskRegister({
        variables: {
          query: {
            riskId,
            ...values,
          },
        },
      });
    });
  };

  return (
    <>
      <Button
        type="default"
        onClick={() => {
          if (isUndone) {
            message.error('请完成所有风险措施后，再操作升级');
            return;
          }
          if (approveStatus === 'APPROVING') {
            message.error('目前处于升级ROC审批中，如需再次升级请先撤回审批');
            return;
          }
          getUsersByRoleSpace({
            variables: {
              query: {
                roleCodeList: ['ROCzg'],
                idcTagList: idc ? [idc] : [],
                blockGuidList: block ? [block] : [],
              },
            },
          });
          setVisible(true);
        }}
      >
        升级ROC{' '}
      </Button>

      <Modal
        title="升级ROC"
        open={visible}
        okText="提交"
        width={581}
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: loading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onSubmit}
      >
        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 14 }} form={form}>
          <Alert
            message="提交后将进行升级审批，审批通过后风险单“责任人”将自动变更为被升级人。"
            type="warning"
            showIcon
            style={{ marginBottom: 24 }}
          />
          <Form.Item
            label="升级到"
            name="upgradeTo"
            rules={[{ required: true, message: '升级到必填！' }]}
          >
            <UserSelect mode="multiple" labelInValue={false} style={{ width: 216 }} />
          </Form.Item>
          <Form.Item
            label="升级原因"
            name="upgradeReason"
            rules={[
              { required: true, whitespace: true, message: '升级原因必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
