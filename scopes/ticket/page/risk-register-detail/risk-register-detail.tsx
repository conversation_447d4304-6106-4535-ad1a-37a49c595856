import { InfoCircleOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import moment from 'moment';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Link, useHistory, useParams } from 'react-router-dom';
import { useLatest } from 'react-use';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { User } from '@manyun/auth-hub.ui.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Result } from '@manyun/base-ui.ui.result';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
// import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
// import ApprovalOperationButtons from '@manyun/bpm.ui.approval-operation-buttons';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import styled, { css } from '@manyun/dc-brain.theme.theme';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import {
  useLazyRiskRegister,
  useLazyRiskRegisterLatestDevelopment,
  useLazyRiskRegisterMeasure,
} from '@manyun/ticket.gql.client.risk-register';
import type {
  MeasureJson,
  RiskObject,
  RiskObjectType,
} from '@manyun/ticket.gql.client.risk-register';
import { useLazyRiskRegisterMeasureAndProcessingRecord } from '@manyun/ticket.gql.client.tickets';
import { RiskStatusStepMap, getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { RiskRegisterLocationDrawer } from '@manyun/ticket.page.risk-register-mutator';
// import { TransferButton } from '@manyun/ticket.page.risk-registers';
import { RISK_REGISTERS } from '@manyun/ticket.route.ticket-routes';
import type { OptType } from '@manyun/ticket.service.change-risk-register-status';
import { changeRiskRegisterStatus } from '@manyun/ticket.service.change-risk-register-status';
import { fetchRiskRegisterRelates } from '@manyun/ticket.service.fetch-risk-register-relates';
import type { RiskRegisterRelate } from '@manyun/ticket.service.fetch-risk-register-relates';

import { InfluenceSurface } from '@manyun/dc-brain.legacy.components';

import Cleared from './assets/cleared.png';
import UnCleared from './assets/unCleared.png';
import { ApproveView } from './components/approve-view';
// import { CloseRiskButton } from './components/close-risk-button';
// import { CloseRiskFullButton } from './components/close-risk-button-full';
import { EditFormDrawer } from './components/edit-drawer';
import { RiskFooterBar } from './components/footer-bar';
import { RelatedEvents } from './components/related-events';
// import PostponeRisk from './components/postpone-risk-register';
import { RelatedMatters } from './components/related-matters';
import RiskMeasure from './components/risk-measure';
import { RiskRegisterContext, initTabs } from './components/risk-register-context';
import type { ExportPDFTabs } from './components/risk-register-context';
import ShareRisk from './components/share-risk';

// import { SubmitRiskIdentifyButton } from './components/submit-risk-identify-button';

export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const stepsPrefixCls = `${prefixCls}-steps`;
    return css`
      // overrides steps's style
      .${stepsPrefixCls}-horizontal
        .${stepsPrefixCls}-item-content
        .${stepsPrefixCls}-item-description {
        max-width: none;
      }
    `;
  }}
`;

export function RiskRegisterDetail() {
  const { id } = useParams<{ id: string }>();
  const history = useHistory();

  const [riskRegisterRelates, setRiskRegisterRelates] = useState<RiskRegisterRelate[]>([]);
  const [isExportPDF, setIsExportPDF] = useState(false);
  const [exportPDFTabs, setExportPDFTabs] = useState(initTabs);
  const [exportPdfLoading, setExportPdfLoading] = useState(false);
  const [approveStatus, setApproveStatus] = useState('');

  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';
  const {
    showRecognition,
    postponeRisk,
    closeCheck,
    showMeasureTable,
    updateRoc,
    riskSource,
    showNotProblemBtn,
  } = features;

  const [fetchDetail, { loading, data, refetch }] = useLazyRiskRegister({
    onCompleted(data) {
      setExportPDFTabs(exportPDFTabs => {
        return exportPDFTabs.map(item => ({
          ...item,
          isValid:
            item.key === 'relateEvent'
              ? !!data.riskRegister?.questionRelateEventList?.length
              : item.isValid,
          isRendered: item.key === 'relateEvent' ? true : item.isRendered,
        }));
      });
    },
  });
  const [fetchMeasureAll, { data: measuresAllData, refetch: refetchAllMeasure }] =
    useLazyRiskRegisterMeasureAndProcessingRecord();

  const [fetchMeasure, { data: measures, refetch: refetchMeasure }] = useLazyRiskRegisterMeasure({
    onCompleted(data) {
      setExportPDFTabs(exportPDFTabs => {
        return exportPDFTabs.map(item => ({
          ...item,
          isValid:
            item.key === 'longMeasure'
              ? !!data.riskRegisterMeasure.longMeasures.length
              : item.isValid,
          isRendered:
            item.key === 'longMeasure'
              ? data.riskRegisterMeasure.longMeasures.length
                ? false
                : true
              : item.isRendered,
        }));
      });
      setExportPDFTabs(exportPDFTabs => {
        return exportPDFTabs.map(item => ({
          ...item,
          isValid:
            item.key === 'shortMeasure'
              ? !!data.riskRegisterMeasure.shortMeasures.length
              : item.isValid,
          isRendered:
            item.key === 'shortMeasure'
              ? data.riskRegisterMeasure.shortMeasures.length
                ? false
                : true
              : item.isRendered,
        }));
      });
    },
  });

  const [fetchLatestDevelopment, { data: latestDevelopments, refetch: refetchLatestDevelopment }] =
    useLazyRiskRegisterLatestDevelopment({
      onCompleted(data) {
        if (isExportPDF) {
          setExportPDFTabs(exportPDFTabs => {
            return exportPDFTabs.map(item => ({
              ...item,
              isValid:
                item.key === 'processingRecords'
                  ? !!data.riskRegisterLatestDevelopment.length
                  : item.isValid,
              isRendered: item.key === 'processingRecords' ? true : item.isRendered,
            }));
          });
        }
      },
    });
  const detailInfo = data?.riskRegister;
  const exportPdfInterval = useRef<NodeJS.Timer | null>(null);
  const latestExportPDFTabs = useLatest(exportPDFTabs);

  useEffect(() => {
    getRiskRegisterRelates();
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const getRiskRegisterRelates = async () => {
    if (!id) {
      return;
    }
    const { error, data } = await fetchRiskRegisterRelates({ riskId: id });
    if (error) {
      message.error(error.message);
      return;
    }
    if (isExportPDF) {
      setExportPDFTabs(exportPDFTabs => {
        return exportPDFTabs.map(item => ({
          ...item,
          isValid: item.key === 'relatedMatters' ? !!data.data.length : item.isValid,
          isRendered: item.key === 'relatedMatters' ? true : item.isRendered,
        }));
      });
    }
    setRiskRegisterRelates(data.data);
  };

  useEffect(() => {
    fetchDetail({
      variables: {
        query: {
          riskId: id,
        },
      },
    });
    if (isFull) {
      fetchMeasureAll({ variables: { query: { riskId: id, deleted: false } } });
    } else {
      fetchMeasure({
        variables: {
          query: {
            riskId: id,
          },
        },
      });
    }
    fetchLatestDevelopment({
      variables: {
        query: {
          riskId: id,
        },
      },
    });
  }, [fetchDetail, fetchMeasure, fetchLatestDevelopment, fetchMeasureAll, isFull, id]);

  useEffect(() => {
    if (isExportPDF) {
      fetchLatestDevelopment({
        variables: {
          query: {
            riskId: id,
          },
        },
      });
      getRiskRegisterRelates();
      fetchDetail({
        variables: {
          query: {
            riskId: id,
          },
        },
      });
    }
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isExportPDF]);

  const locales = useMemo(() => getRiskRegisterLocales(), []);
  const [, { checkUserId, checkCode }] = useAuthorized();
  const isAuthorized = checkCode('element_risk-register-share-to-knowledge');

  const handleChangeStatus = async (optType: OptType) => {
    if (!detailInfo?.riskStatus) {
      return;
    }
    const { error } = await changeRiskRegisterStatus({
      riskId: id,
      optType,
      curRiskStatus: detailInfo.riskStatus,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    refetch();
  };

  const generateCauseDevices = () => {
    if (detailInfo?.riskObjectType === 'DEVICE' && Array.isArray(detailInfo.riskObjects)) {
      return detailInfo.riskObjects.map((device: RiskObject) => device.objectGuid).join(',');
    }
    return null;
  };

  const generateCauseDevicesIsFull = () => {
    if (Array.isArray(detailInfo?.locationList)) {
      return detailInfo?.locationList
        .filter(item => item.locationType === 'DEVICE')
        .map(device => device.guid)
        .join(',');
    }
    return null;
  };

  const canClosed = () => {
    const { longMeasures, shortMeasures } = measures?.riskRegisterMeasure ?? {};
    let measureDatas: MeasureJson[] = [];
    if (longMeasures && longMeasures.length) {
      measureDatas = measureDatas.concat(longMeasures);
    }
    if (shortMeasures && shortMeasures.length) {
      measureDatas = measureDatas.concat(shortMeasures);
    }
    return measureDatas.every(item => item.measureStatus === 'DONE');
  };

  const canSubmit = () => {
    if (showMeasureTable) {
      return !!measuresAllData?.riskRegisterMeasureAndProcessingRecord.length;
    }
    const { longMeasures, shortMeasures } = measures?.riskRegisterMeasure ?? {};
    return !!(longMeasures?.length || shortMeasures?.length);
  };

  if (!detailInfo) {
    return null;
  }
  if (detailInfo.deleted) {
    return (
      <Container
        style={{
          width: '100%',
          height: `calc(100vh - 112px)`,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Result
          status="warning"
          title="当前风险单已被删除"
          extra={
            <Button type="primary" onClick={() => history.push(RISK_REGISTERS)}>
              返回风险登记册列表
            </Button>
          }
        />
      </Container>
    );
  }

  const getPlanTimeText = () => {
    const { riskStatus, completeTime, planCompleteTime } = detailInfo;
    if (planCompleteTime) {
      let isTimeout = false;
      if (isFull) {
        if (
          ['APPROVING', 'CLOSED'].includes(riskStatus) &&
          riskStatus &&
          planCompleteTime &&
          completeTime &&
          new Date(completeTime).getTime() > moment(planCompleteTime).valueOf()
        ) {
          isTimeout = true;
        }
        if (
          !['APPROVING', 'CLOSED'].includes(riskStatus) &&
          planCompleteTime &&
          new Date().getTime() > moment(planCompleteTime).valueOf()
        ) {
          isTimeout = true;
        }
      } else {
        if (
          !['APPROVING', 'CLOSED'].includes(riskStatus) &&
          planCompleteTime &&
          new Date().getTime() > moment(planCompleteTime).valueOf()
        ) {
          isTimeout = true;
        }
      }

      return (
        <>
          {detailInfo.timeExtensionApprInfo?.processId ? (
            <Typography.Text
              type={isTimeout ? 'danger' : undefined}
              ellipsis={{ tooltip: dayjs(planCompleteTime).format('YYYY-MM-DD HH:mm') }}
            >
              {dayjs(planCompleteTime).format('YYYY-MM-DD HH:mm')}
            </Typography.Text>
          ) : (
            <Typography.Text type={isTimeout ? 'danger' : undefined}>
              {dayjs(planCompleteTime).format('YYYY-MM-DD HH:mm')}
            </Typography.Text>
          )}

          {isTimeout && isFull && (
            <Explanation
              style={{ color: 'inherit', display: 'inline' }}
              iconType="question"
              tooltip={{
                title: '处理已超过风险单计划完成时间',
              }}
            />
          )}
        </>
      );
    }
    return '--';
  };
  const isOwner = (detailInfo.riskOwnerIdList ?? []).some(item => checkUserId(item));
  const isAuthorizedUser = (detailInfo.authorizedUserList ?? []).some(item => checkUserId(item));
  const isCreater = checkUserId(detailInfo.createUser);
  const auditInstId = detailInfo.auditInstId;
  const evaluateInstId = detailInfo.evaluateInstId;
  const rocUpgradeInstId = detailInfo.rocUpgradeInstId;
  const currentStep = detailInfo.riskStatus ? RiskStatusStepMap[detailInfo?.riskStatus] : 0;
  const timeout =
    moment(detailInfo.planCompleteTime).valueOf() - moment().valueOf() <= 60 * 60 * 1000;

  return (
    <RiskRegisterContext.Provider
      value={{
        isExportPDF: isExportPDF,
        exportPDFTabs: exportPDFTabs,
        setIsExportPDF: setIsExportPDF,
        setExportPDFTabs: setExportPDFTabs,
      }}
    >
      <Spin spinning={loading || exportPdfLoading}>
        {!isFull &&
          detailInfo.riskStatus === 'CLOSED' &&
          detailInfo.riskResourceCode === 'EVENT_QUESTION' && (
            <DownloadPdfButton
              key="download"
              compact={false}
              style={{
                position: 'fixed',
                right:
                  !isFull &&
                  detailInfo.riskStatus === 'CLOSED' &&
                  isAuthorized &&
                  detailInfo.riskResourceCode === 'EVENT_QUESTION'
                    ? 132
                    : 8,
                top: 52,
              }}
              disabled={isExportPDF}
              pdfName={`${detailInfo.id} ${detailInfo.riskDesc}`}
              exportElement={document.getElementById('root')}
              beforeDownload={() => {
                setIsExportPDF(true);
                setExportPdfLoading(true);
                return new Promise(resolve => {
                  exportPdfInterval.current = setInterval(() => {
                    if (
                      latestExportPDFTabs.current.some((item: ExportPDFTabs) => !item.isRendered)
                    ) {
                      return;
                    }
                    setExportPdfLoading(false);
                    setTimeout(() => {
                      resolve();
                    }, 1000);
                  }, 1000);
                });
              }}
              onFinish={() => {
                setIsExportPDF(false);
                setExportPDFTabs(initTabs);
                exportPdfInterval.current && clearInterval(exportPdfInterval.current);
              }}
            />
          )}
        {!isFull &&
          detailInfo.riskStatus === 'CLOSED' &&
          isAuthorized &&
          detailInfo.riskResourceCode === 'EVENT_QUESTION' && (
            <ShareRisk
              riskId={detailInfo.id}
              riskDesc={detailInfo.riskDesc}
              setExportPDFTab={() => setExportPDFTabs(initTabs)}
              exportPDFTabs={exportPDFTabs}
              setIsExportPDF={setIsExportPDF}
              setExportPdfLoading={setExportPdfLoading}
              onSuccess={refetch}
            />
          )}
        <Space direction="vertical" style={{ width: '100%', marginBottom: 48 }} size="middle">
          <Card bordered={false}>
            <StyledContainer>
              <Steps
                current={currentStep}
                // 阳高不只是识别中，展示添加风险措施
                items={
                  showRecognition
                    ? [
                        {
                          title: <Typography.Text>创建</Typography.Text>,
                          description:
                            detailInfo.createdAt &&
                            dayjs(detailInfo.createdAt).format('YYYY-MM-DD HH:mm'),
                        },
                        {
                          title: (
                            <div>
                              {currentStep < RiskStatusStepMap.WAITING_IDENTIFY && (
                                <Typography.Text>待识别</Typography.Text>
                              )}
                              {currentStep === RiskStatusStepMap.WAITING_IDENTIFY && (
                                <Typography.Text>识别中</Typography.Text>
                              )}
                              {currentStep > RiskStatusStepMap.WAITING_IDENTIFY && (
                                <Typography.Text>已识别</Typography.Text>
                              )}
                            </div>
                          ),
                          description:
                            currentStep > RiskStatusStepMap.WAITING_IDENTIFY &&
                            detailInfo.riskIdentifyTime
                              ? dayjs(detailInfo.riskIdentifyTime).format('YYYY-MM-DD HH:mm')
                              : '--',
                        },
                        {
                          title: (
                            <div>
                              {currentStep < RiskStatusStepMap.WAITING_EVALUATE && (
                                <Typography.Text>待评估</Typography.Text>
                              )}
                              {currentStep === RiskStatusStepMap.WAITING_EVALUATE && (
                                <Typography.Text>评估中</Typography.Text>
                              )}
                              {currentStep > RiskStatusStepMap.WAITING_EVALUATE && (
                                <Typography.Text>已评估</Typography.Text>
                              )}
                            </div>
                          ),
                          description: (
                            <Space>
                              {currentStep > RiskStatusStepMap.WAITING_EVALUATE &&
                              detailInfo.riskEvaluateTime
                                ? dayjs(detailInfo.riskEvaluateTime).format('YYYY-MM-DD HH:mm')
                                : '--'}
                              {evaluateInstId &&
                                isApprovalIdValid(evaluateInstId) &&
                                detailInfo.riskLevel && (
                                  <ApproveView
                                    id={detailInfo.id}
                                    instId={evaluateInstId}
                                    processType="RISK_REGISTER_EVAL"
                                    taskStatus={detailInfo.riskStatus}
                                    detailInfo={detailInfo}
                                    timeout={timeout}
                                    postponeRisk={postponeRisk}
                                    isOwner={isOwner}
                                    isFull={isFull}
                                    closeCheck={closeCheck}
                                    isAuthorizedUser={isAuthorizedUser}
                                    canSubmit={canSubmit}
                                    showNotProblemBtn={showNotProblemBtn}
                                    canClosed={canClosed}
                                    onCallBack={() => refetch()}
                                  />
                                )}
                            </Space>
                          ),
                        },
                        {
                          title: (
                            <div>
                              {currentStep < RiskStatusStepMap.HANDLING && (
                                <Typography.Text>待处理</Typography.Text>
                              )}
                              {currentStep === RiskStatusStepMap.HANDLING && (
                                <Typography.Text>处理中</Typography.Text>
                              )}
                              {currentStep > RiskStatusStepMap.HANDLING && (
                                <Typography.Text>已处理</Typography.Text>
                              )}
                            </div>
                          ),
                          description:
                            currentStep > RiskStatusStepMap.HANDLING && detailInfo.riskHandleTime
                              ? dayjs(detailInfo.riskHandleTime).format('YYYY-MM-DD HH:mm')
                              : '--',
                        },
                        {
                          title: (
                            <div>
                              {currentStep < RiskStatusStepMap.APPROVING && (
                                <Typography.Text>待审核</Typography.Text>
                              )}
                              {currentStep === RiskStatusStepMap.APPROVING && (
                                <Typography.Text>审核中</Typography.Text>
                              )}
                              {currentStep > RiskStatusStepMap.APPROVING && (
                                <Typography.Text>已审核</Typography.Text>
                              )}
                            </div>
                          ),
                          description: (
                            <Space>
                              {currentStep > RiskStatusStepMap.APPROVING && detailInfo.riskAuditTime
                                ? dayjs(detailInfo.riskAuditTime).format('YYYY-MM-DD HH:mm')
                                : '--'}
                              {auditInstId && isApprovalIdValid(auditInstId) && (
                                <ApproveView
                                  id={detailInfo.id}
                                  instId={auditInstId}
                                  processType="RISK_REGISTER_CLOSE"
                                  taskStatus={detailInfo.riskStatus}
                                  detailInfo={detailInfo}
                                  timeout={timeout}
                                  postponeRisk={postponeRisk}
                                  isOwner={isOwner}
                                  isFull={isFull}
                                  showNotProblemBtn={showNotProblemBtn}
                                  isAuthorizedUser={isAuthorizedUser}
                                  closeCheck={closeCheck}
                                  canSubmit={canSubmit}
                                  canClosed={canClosed}
                                  onCallBack={() => refetch()}
                                />
                              )}
                            </Space>
                          ),
                        },
                        {
                          title: <Typography.Text>关闭</Typography.Text>,
                          description: (
                            <Space>
                              {detailInfo.riskCloseTime &&
                                dayjs(detailInfo.riskCloseTime).format('YYYY-MM-DD HH:mm')}
                              {isOwner && detailInfo.riskStatus === 'CLOSED' && (
                                <Popconfirm
                                  title="确认重启此风险单？重启后风险单会变为“处理中“状态，请谨慎操作！"
                                  style={{ width: 290 }}
                                  okText="确认重启"
                                  cancelText="我再想想"
                                  placement="bottomRight"
                                  onConfirm={() => handleChangeStatus('RESTART')}
                                >
                                  <Button compact type="link" disabled={loading}>
                                    重启
                                  </Button>
                                </Popconfirm>
                              )}
                            </Space>
                          ),
                        },
                      ]
                    : [
                        {
                          title: <Typography.Text>创建</Typography.Text>,
                          description:
                            detailInfo.createdAt &&
                            dayjs(detailInfo.createdAt).format('YYYY-MM-DD HH:mm'),
                        },
                        {
                          title: '添加风险措施',
                          description:
                            detailInfo.riskIdentifyTime &&
                            dayjs(detailInfo.riskIdentifyTime).format('YYYY-MM-DD HH:mm'),
                        },
                        {
                          title: '评估',
                          description: (
                            <Space>
                              {currentStep >= RiskStatusStepMap.WAITING_EVALUATE &&
                              detailInfo.riskEvaluateTime
                                ? dayjs(detailInfo.riskEvaluateTime).format('YYYY-MM-DD HH:mm')
                                : '--'}
                              {evaluateInstId &&
                                isApprovalIdValid(evaluateInstId) &&
                                detailInfo.riskLevel && (
                                  <ApproveView
                                    id={detailInfo.id}
                                    instId={evaluateInstId}
                                    processType="RISK_REGISTER_EVAL"
                                    taskStatus={detailInfo.riskStatus}
                                    detailInfo={detailInfo}
                                    timeout={timeout}
                                    postponeRisk={postponeRisk}
                                    isOwner={isOwner}
                                    isFull={isFull}
                                    showNotProblemBtn={showNotProblemBtn}
                                    isAuthorizedUser={isAuthorizedUser}
                                    closeCheck={closeCheck}
                                    canSubmit={canSubmit}
                                    canClosed={canClosed}
                                    onCallBack={() => refetch()}
                                  />
                                )}
                            </Space>
                          ),
                        },
                        {
                          title: '处理',
                          description: (
                            <Space>
                              {currentStep > RiskStatusStepMap.HANDLING &&
                                detailInfo.riskHandleTime &&
                                dayjs(detailInfo.riskHandleTime).format('YYYY-MM-DD HH:mm')}
                              {rocUpgradeInstId &&
                                isApprovalIdValid(rocUpgradeInstId) &&
                                detailInfo.riskLevel && (
                                  <ApproveView
                                    id={detailInfo.id}
                                    instId={rocUpgradeInstId}
                                    processType="RISK_ROC_UPGRADE"
                                    taskStatus={detailInfo.riskStatus}
                                    isRoc
                                    detailInfo={detailInfo}
                                    timeout={timeout}
                                    postponeRisk={postponeRisk}
                                    isOwner={isOwner}
                                    isFull={isFull}
                                    closeCheck={closeCheck}
                                    showNotProblemBtn={showNotProblemBtn}
                                    isAuthorizedUser={isAuthorizedUser}
                                    canSubmit={canSubmit}
                                    canClosed={canClosed}
                                    setApproveStatus={setApproveStatus}
                                    onCallBack={() => refetch()}
                                  />
                                )}
                            </Space>
                          ),
                        },
                        {
                          title: '关单审批',
                          description: (
                            <Space>
                              {currentStep > RiskStatusStepMap.APPROVING && detailInfo.riskAuditTime
                                ? dayjs(detailInfo.riskAuditTime).format('YYYY-MM-DD HH:mm')
                                : '--'}
                              {auditInstId && isApprovalIdValid(auditInstId) && (
                                <ApproveView
                                  id={detailInfo.id}
                                  instId={auditInstId}
                                  processType="RISK_REGISTER_CLOSE"
                                  taskStatus={detailInfo.riskStatus}
                                  detailInfo={detailInfo}
                                  timeout={timeout}
                                  postponeRisk={postponeRisk}
                                  isOwner={isOwner}
                                  isFull={isFull}
                                  showNotProblemBtn={showNotProblemBtn}
                                  isAuthorizedUser={isAuthorizedUser}
                                  closeCheck={closeCheck}
                                  canSubmit={canSubmit}
                                  canClosed={canClosed}
                                  onCallBack={() => refetch()}
                                />
                              )}
                            </Space>
                          ),
                        },
                        {
                          title: '关单',
                          description:
                            detailInfo.riskCloseTime &&
                            dayjs(detailInfo.riskCloseTime).format('YYYY-MM-DD HH:mm'),
                        },
                      ]
                }
              />
            </StyledContainer>
          </Card>
          <Card
            bordered={false}
            headStyle={{ borderBottom: 0 }}
            bodyStyle={{ padding: '0 24px' }}
            title={
              <Typography.Title showBadge level={5}>
                基本信息
              </Typography.Title>
            }
            extra={
              ((isOwner &&
                ['DRAFT', 'WAITING_IDENTIFY', 'HANDLING'].includes(detailInfo.riskStatus)) ||
                (isCreater && detailInfo.riskStatus === 'DRAFT') ||
                (isAuthorizedUser && !isFull && detailInfo.riskStatus === 'WAITING_IDENTIFY')) && (
                <EditFormDrawer
                  text="编辑基本信息"
                  title="编辑基本信息"
                  values={detailInfo}
                  measuresPlanTime={
                    isFull
                      ? (measuresAllData?.riskRegisterMeasureAndProcessingRecord ?? [])
                          .filter(item => item.measureStatus !== 'INVALID')
                          .map(item => dayjs(item.planCompleteTime).valueOf())
                          .sort((a, b) => b - a)?.[0]
                      : undefined
                  }
                  onSuccess={refetch}
                />
              )
            }
          >
            {!isFull && ['APPROVING', 'CLOSED'].includes(detailInfo.riskStatus) && (
              <img
                alt="状态"
                style={{ position: 'absolute', right: 24, top: 24, width: 80, height: 80 }}
                src={detailInfo.riskClearStatus === 'UNCLEARED' ? UnCleared : Cleared}
              />
            )}
            <Descriptions column={4} contentStyle={{ overflow: 'hidden', paddingRight: 16 }}>
              <Descriptions.Item label="位置">
                {detailInfo.blockGuid ?? detailInfo.idcTag}
              </Descriptions.Item>
              <Descriptions.Item label="风险来源">
                {detailInfo.riskResourceName ?? '--'}
              </Descriptions.Item>
              <Descriptions.Item label={isFull ? '风险专业' : '风险类别'}>
                {detailInfo.riskCategoryName ?? '--'}
              </Descriptions.Item>
              <Descriptions.Item label="风险类型">
                {detailInfo.riskTypeName ?? '--'}
              </Descriptions.Item>
              <Descriptions.Item label="风险等级">
                {detailInfo.riskLevelName ? detailInfo.riskLevelName : '--'}
              </Descriptions.Item>
              {isFull && (
                <Descriptions.Item label="风险位置">
                  <RiskRegisterLocationDrawer riskId={id} />
                </Descriptions.Item>
              )}
              {isFull && (
                <Descriptions.Item label="计划完成时间" contentStyle={{ overflow: 'hidden' }}>
                  {getPlanTimeText()}
                  {detailInfo.timeExtensionApprInfo?.processId && (
                    <Popover
                      title="风险延期审批"
                      content={
                        <Space style={{ width: 236 }} direction="vertical">
                          <Typography.Text>
                            审批ID：
                            <Link
                              target="_blank"
                              to={generateBPMRoutePath({
                                id: detailInfo.timeExtensionApprInfo.processId,
                              })}
                            >
                              {detailInfo.timeExtensionApprInfo.processId}
                            </Link>
                          </Typography.Text>
                          <Typography.Text>
                            原计划完成时间：
                            {moment(detailInfo.timeExtensionApprInfo.originPlanCompleteTime).format(
                              'YYYY-MM-DD HH:mm'
                            )}
                          </Typography.Text>
                          <Typography.Text>
                            延期至：
                            {moment(detailInfo.timeExtensionApprInfo.planCompleteTime).format(
                              'YYYY-MM-DD HH:mm'
                            )}
                          </Typography.Text>
                        </Space>
                      }
                    >
                      <Tag color="processing" style={{ marginLeft: 8 }}>
                        风险延期审批中
                      </Tag>
                    </Popover>
                  )}
                </Descriptions.Item>
              )}
              {isFull && (
                <Descriptions.Item label="是否长期风险项">
                  {detailInfo.longTermRisk !== null ? (
                    detailInfo.longTermRisk ? (
                      <>
                        <Badge status="warning" style={{ marginRight: 8 }} />是
                      </>
                    ) : (
                      <>
                        <Badge status="default" style={{ marginRight: 8 }} />否
                      </>
                    )
                  ) : (
                    '--'
                  )}
                </Descriptions.Item>
              )}
              {!isFull && (
                <Descriptions.Item label="责任人" contentStyle={{ overflow: 'hidden' }}>
                  <PopoverUser riskOwnerIdList={detailInfo.riskOwnerIdList} userLink />
                </Descriptions.Item>
              )}
              {!isFull && (
                <Descriptions.Item label="风险对象类型">
                  {detailInfo.riskObjectType
                    ? locales.riskObjectType.enum[detailInfo.riskObjectType]
                    : '--'}
                </Descriptions.Item>
              )}
              {!isFull && (
                <Descriptions.Item label="风险对象名称">
                  {detailInfo.riskObjectType && detailInfo.riskObjects ? (
                    <RiskObjectLink
                      type={detailInfo.riskObjectType}
                      objects={detailInfo.riskObjects}
                    />
                  ) : (
                    '--'
                  )}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="风险描述" span={2}>
                <Typography.Text ellipsis={{ tooltip: detailInfo.riskDesc }}>
                  {detailInfo.riskDesc}
                </Typography.Text>
              </Descriptions.Item>
              {isFull && (
                <Descriptions.Item label="责任人" contentStyle={{ overflow: 'hidden' }}>
                  <PopoverUser riskOwnerIdList={detailInfo.riskOwnerIdList} userLink />
                </Descriptions.Item>
              )}
              {isFull && (
                <Descriptions.Item label="风险识别人">
                  {detailInfo.riskIdentifier ? detailInfo.riskIdentifier : '--'}
                </Descriptions.Item>
              )}
              {!isFull && (
                <Descriptions.Item label="影响范围描述" span={2}>
                  <Typography.Text ellipsis={{ tooltip: detailInfo.riskInfluenceDesc }}>
                    {detailInfo.riskInfluenceDesc ?? '--'}
                  </Typography.Text>
                </Descriptions.Item>
              )}

              {features.showIdentifyUser && !isFull && (
                <Descriptions.Item label="识别人">
                  {detailInfo.riskIdentifier ? detailInfo.riskIdentifier : '--'}
                </Descriptions.Item>
              )}
              {!isFull && (
                <Descriptions.Item label="风险关联方">
                  {detailInfo.relatePerson ? (
                    detailInfo.relatePerson.id ? (
                      <UserLink
                        userId={detailInfo.relatePerson.id}
                        userName={detailInfo.relatePerson.name}
                      />
                    ) : (
                      detailInfo.relatePerson.name
                    )
                  ) : (
                    '--'
                  )}
                </Descriptions.Item>
              )}
              <Descriptions.Item label="问题附件">
                {detailInfo.fileInfoList?.length ? (
                  <SimpleFileList files={detailInfo.fileInfoList ?? []}>
                    <Typography.Link>查看 </Typography.Link>
                  </SimpleFileList>
                ) : (
                  '--'
                )}
              </Descriptions.Item>
              {isFull && (
                <Descriptions.Item label="影响范围描述" span={2}>
                  <Typography.Text ellipsis={{ tooltip: detailInfo.riskInfluenceDesc }}>
                    {detailInfo.riskInfluenceDesc ?? '--'}
                  </Typography.Text>
                </Descriptions.Item>
              )}
              {detailInfo.rectifyInfoList?.length && (
                <Descriptions.Item label="整改附件">
                  <SimpleFileList files={detailInfo.rectifyInfoList}>
                    <Typography.Link>查看</Typography.Link>
                  </SimpleFileList>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>

          {isExportPDF ? (
            <>
              {measures?.riskRegisterMeasure.longMeasures.length ||
              measures?.riskRegisterMeasure.shortMeasures.length ||
              latestDevelopments?.riskRegisterLatestDevelopment.length ? (
                <Card title="风险措施" bordered={false}>
                  <RiskMeasure
                    isOwner={isOwner}
                    detailInfo={detailInfo}
                    measureData={measures && measures?.riskRegisterMeasure}
                    measuresAllData={
                      measuresAllData ? measuresAllData.riskRegisterMeasureAndProcessingRecord : []
                    }
                    newestProcessingRecords={
                      latestDevelopments?.riskRegisterLatestDevelopment ?? []
                    }
                    updateRoc={updateRoc}
                    onRefetchDetail={refetch}
                    onRefetchMeasure={refetchMeasure}
                    onRefetchLatestDevelopment={refetchLatestDevelopment}
                    onRefetchRiskRegisterRelates={getRiskRegisterRelates}
                  />
                </Card>
              ) : null}
              {exportPDFTabs.some(
                item => item.key === 'relateEvent' && item.isValid && item.isRendered
              ) && riskSource ? (
                <Card title="问题关联事件" bordered={false}>
                  <RelatedEvents dataSource={detailInfo.questionRelateEventList} />
                </Card>
              ) : null}
              {exportPDFTabs.some(
                item => item.key === 'relatedMatters' && item.isValid && item.isRendered
              ) ? (
                <Card title="关联事项" bordered={false}>
                  <RelatedMatters dataSource={riskRegisterRelates} />
                </Card>
              ) : null}
              {exportPDFTabs.some(
                item =>
                  item.key === 'influenceSuface' &&
                  ((item.isValid && item.isRendered) || !item.isRendered)
              ) ? (
                <Card title="影响范围" bordered={false}>
                  <InfluenceSurface
                    idcTag={detailInfo.idcTag}
                    blockTag={
                      detailInfo.blockGuid &&
                      detailInfo.blockGuid.substring(detailInfo.blockGuid.indexOf('.') + 1)
                    }
                    deviceGuids={isFull ? generateCauseDevicesIsFull() : generateCauseDevices()}
                    pdfTabList={exportPDFTabs}
                    tabsExpandAll={isExportPDF}
                    setPdfTabList={setExportPDFTabs}
                  />
                </Card>
              ) : null}
            </>
          ) : (
            <Card bordered={false} bodyStyle={{ padding: '12px 24px' }}>
              <Tabs
                defaultActiveKey="measure"
                items={[
                  {
                    key: 'measure',
                    label: '风险措施',
                    children: (
                      <RiskMeasure
                        isOwner={isOwner}
                        detailInfo={detailInfo}
                        isAuthorizedUser={isAuthorizedUser}
                        measureData={measures && measures?.riskRegisterMeasure}
                        newestProcessingRecords={
                          latestDevelopments?.riskRegisterLatestDevelopment ?? []
                        }
                        measuresAllData={
                          measuresAllData
                            ? measuresAllData.riskRegisterMeasureAndProcessingRecord
                            : []
                        }
                        isFull={isFull}
                        updateRoc={updateRoc}
                        showMeasureTable={showMeasureTable}
                        approveStatus={approveStatus}
                        onRefetchDetail={refetch}
                        onRefetchMeasure={isFull ? refetchAllMeasure : refetchMeasure}
                        onRefetchLatestDevelopment={refetchLatestDevelopment}
                        onRefetchRiskRegisterRelates={getRiskRegisterRelates}
                      />
                    ),
                  },
                  {
                    key: 'related-events',
                    label: '问题关联事件',
                    children: <RelatedEvents dataSource={detailInfo.questionRelateEventList} />,
                  },
                  {
                    key: 'related-matters',
                    label: (
                      <>
                        {' '}
                        关联事项{' '}
                        {riskSource && (
                          <Tooltip title="通过风险单创建的其他业务单">
                            <Typography.Text type="secondary" style={{ marginLeft: 4 }}>
                              <InfoCircleOutlined />
                            </Typography.Text>
                          </Tooltip>
                        )}
                      </>
                    ),
                    children: <RelatedMatters dataSource={riskRegisterRelates} />,
                  },
                  {
                    key: 'influence',
                    label: '影响范围',
                    children: (
                      <InfluenceSurface
                        idcTag={detailInfo.idcTag}
                        blockTag={
                          detailInfo.blockGuid &&
                          detailInfo.blockGuid.substring(detailInfo.blockGuid.indexOf('.') + 1)
                        }
                        deviceGuids={isFull ? generateCauseDevicesIsFull() : generateCauseDevices()}
                        pdfTabList={exportPDFTabs}
                        tabsExpandAll={isExportPDF}
                        setPdfTabList={setExportPDFTabs}
                      />
                    ),
                  },
                  {
                    key: 'operation',
                    label: '操作记录',
                    children: (
                      <OperationLogTable
                        defaultSearchParams={{
                          targetId: id,
                          targetType: 'NEW_RISK_REGISTER',
                        }}
                        showColumns={['modifyType', 'targetType']}
                        isTargetIdEqual={(targetId: string) => {
                          return targetId === id;
                        }}
                      />
                    ),
                  },
                ].filter(item => {
                  if (item.key === 'related-matters') {
                    return riskRegisterRelates.length > 0;
                  }
                  if (item.key === 'related-events') {
                    return riskSource && (detailInfo.questionRelateEventList?.length ?? 0) > 0;
                  }
                  return true;
                })}
              />
            </Card>
          )}
        </Space>
        {(isOwner || isAuthorizedUser) &&
        (['WAITING_IDENTIFY'].includes(detailInfo.riskStatus) ||
          (detailInfo.riskStatus === 'HANDLING' && !detailInfo.rocUpgradeInstId)) &&
        !isExportPDF ? (
          <FooterToolBar>
            <RiskFooterBar
              detailInfo={detailInfo}
              timeout={timeout}
              postponeRisk={postponeRisk}
              isOwner={isOwner}
              isFull={isFull}
              closeCheck={closeCheck}
              showNotProblemBtn={showNotProblemBtn}
              isAuthorizedUser={isAuthorizedUser}
              canSubmit={canSubmit}
              canClosed={canClosed}
              onSuccess={refetch}
            />
          </FooterToolBar>
        ) : // <FooterToolBar>
        //   <Space>
        //     {detailInfo.riskStatus === 'HANDLING' &&
        //       timeout &&
        //       !detailInfo.timeExtensionApprInfo &&
        //       postponeRisk && (
        //         <PostponeRisk
        //           riskId={detailInfo.id}
        //           planCompleteTime={detailInfo.planCompleteTime}
        //           type="primary"
        //           onSuccess={refetch}
        //         />
        //       )}
        //     {isOwner && detailInfo.riskStatus === 'WAITING_IDENTIFY' && detailInfo.riskLevel && (
        //       <SubmitRiskIdentifyButton
        //         riskId={detailInfo.id}
        //         riskLevel={detailInfo.riskLevel as RiskLevel}
        //         canSubmit={canSubmit()}
        //         isFull={isFull}
        //         onSuccess={refetch}
        //       />
        //     )}
        //     {isOwner && detailInfo.riskStatus === 'HANDLING' && detailInfo.riskLevel ? (
        //       closeCheck ? (
        //         <CloseRiskFullButton
        //           riskId={detailInfo.id}
        //           type={timeout && !detailInfo.timeExtensionApprInfo ? 'default' : 'primary'}
        //           onSuccess={refetch}
        //         />
        //       ) : (
        //         <CloseRiskButton
        //           riskId={detailInfo.id}
        //           type={timeout && !detailInfo.timeExtensionApprInfo ? 'default' : 'primary'}
        //           riskLevel={detailInfo.riskLevel as RiskLevel}
        //           canClosed={canClosed()}
        //           onSuccess={refetch}
        //         />
        //       )
        //     ) : null}
        //     {isOwner && ['WAITING_IDENTIFY', 'HANDLING'].includes(detailInfo.riskStatus) && (
        //       <TransferButton
        //         riskId={detailInfo.id}
        //         blockGuid={detailInfo.blockGuid}
        //         onSuccess={refetch}
        //       />
        //     )}
        //     {detailInfo.riskStatus === 'HANDLING' &&
        //       !timeout &&
        //       !detailInfo.timeExtensionApprInfo &&
        //       postponeRisk && (
        //         <PostponeRisk
        //           riskId={detailInfo.id}
        //           planCompleteTime={detailInfo.planCompleteTime}
        //           onSuccess={refetch}
        //         />
        //       )}
        //     {detailInfo.riskStatus === 'HANDLING' &&
        //       detailInfo.upgradeRoc &&
        //       approveInfo?.status === 'APPROVING' && (
        //         <ApprovalOperationButtons
        //           baseInfo={approveInfo as unknown as BpmInstance}
        //           getDetail={() => {
        //             refetch();
        //             // fetchApprovalDetail();
        //           }}
        //         />
        //       )}
        //   </Space>
        // </FooterToolBar>
        null}
      </Spin>
    </RiskRegisterContext.Provider>
  );
}

export const RiskObjectLink = ({
  type,
  objects,
}: {
  type: RiskObjectType;
  objects: RiskObject[];
}) => {
  const causeDevicesLinkContent = (isInPopover: boolean = false) => {
    switch (type) {
      case 'DEVICE':
        return getDeviceContent(isInPopover);
      case 'ROOM':
        return getRoomContent(isInPopover);
      case 'OTHER':
        return getOtherContent();
      default:
        return '--';
    }
  };
  function getDeviceContent(isInPopover: boolean) {
    return objects.map((item, index) => (
      <span key={item.objectGuid}>
        <Link
          key={item.objectGuid}
          target="_blank"
          to={generateDeviceRecordRoutePath({
            guid: item.objectGuid,
          })}
        >
          {item.objectName}({item.deviceType && <DeviceTypeText code={item.deviceType} />})
        </Link>
        {!isInPopover && index !== objects.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }

  function getRoomContent(isInPopover: boolean) {
    return objects.length
      ? objects.map((item, index) => (
          <span key={item.roomTag}>
            <Typography.Link
              key={item.roomTag}
              onClick={() => {
                window.open(
                  generateRoomMonitoringUrl({
                    idc: item.idcTag!,
                    block: item.blockTag!,
                    room: item.roomTag!,
                  })
                );
              }}
            >
              {item.roomTag}
              {item.deviceType && (
                <span>
                  (
                  <RoomTypeText code={item.deviceType} />)
                </span>
              )}
            </Typography.Link>
            {!isInPopover && index !== objects.length - 1 && <Divider type="vertical" />}
          </span>
        ))
      : '--';
  }

  function getOtherContent() {
    return objects.length ? <span>{objects[0].objectName}</span> : '--';
  }
  return (
    <Popover
      content={
        <Space style={{ maxWidth: 400 }} split={<Divider type="vertical" spaceSize="mini" />} wrap>
          {causeDevicesLinkContent(true)}
        </Space>
      }
    >
      <Typography.Text
        ellipsis
        style={{
          color:
            type !== 'OTHER' && objects.length ? `var(--${prefixCls}-primary-color)` : undefined,
          width: '100%',
        }}
      >
        {causeDevicesLinkContent()}
      </Typography.Text>
    </Popover>
  );
};

function isApprovalIdValid(approvalId?: string | null) {
  return approvalId && approvalId !== '0';
}

export const PopoverUser = ({
  riskOwnerIdList,
  userLink,
  width,
}: {
  riskOwnerIdList?: number[] | null;
  userLink: boolean;
  width?: number;
}) => {
  return (
    <Popover
      content={
        <Space style={{ maxWidth: 400 }} split={<Divider type="vertical" spaceSize="mini" />} wrap>
          {(riskOwnerIdList ?? []).map((item: number) =>
            !userLink ? (
              <User key={item} id={item} showAvatar={false} />
            ) : (
              <UserLink key={item} userId={item} />
            )
          )}
        </Space>
      }
    >
      <Typography.Text
        ellipsis={{
          tooltip: false,
        }}
        style={{ width }}
      >
        {(riskOwnerIdList ?? []).map((item: number, index: number) => (
          <>
            {!userLink ? (
              <User key={item} id={item} showAvatar={false} />
            ) : (
              <UserLink key={item} userId={item} />
            )}
            {index + 1 !== riskOwnerIdList?.length && <Divider type="vertical" spaceSize="mini" />}
          </>
        ))}
      </Typography.Text>
    </Popover>
  );
};
