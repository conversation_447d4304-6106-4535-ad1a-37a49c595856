/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-13
 *
 * @packageDocumentation
 */
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useLocation } from '@manyun/dc-brain.navigation.link';
import { UserLink } from '@manyun/iam.ui.user-link';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useDeleteDrillConfig, useLazyDrillConfigs } from '@manyun/ticket.gql.client.tickets';
import type { DrillConfig } from '@manyun/ticket.gql.client.tickets';
import { ConfigRangeType } from '@manyun/ticket.model.task';
import {
  DRILL_CONFIG_CREATE_ROUTE_PATH,
  generateDrillConfigCopyLocation,
  generateDrillConfigDetailLocation,
  generateDrillConfigEditLocation,
} from '@manyun/ticket.route.ticket-routes';
import { exportDrillSchemes } from '@manyun/ticket.service.export-drill-schemes';
import { ConfigRangeTypeRadioButton } from '@manyun/ticket.ui.config-range-type-radio-button';

export type FilteredValueMapper = {
  pageNum: number;
  pageSize: number;
  excName?: string;
  blockGuidList?: string[];
  excMajor?: string;
  excLevelList?: string[];
  createdBy?: number;
  createdByName?: string;
  createAt?: number[];
  effectType?: ConfigRangeType;
};
export type FiltersFormValues = {
  excName?: string;
  blockGuidList?: string[];
  excMajor?: string;
  excLevelList?: string[];
  createdBy?: { value: number; label: string };
  createAt?: Moment[];
  effectType: ConfigRangeType;
};

const defaultPagination: FilteredValueMapper = {
  pageNum: 1,
  pageSize: 10,
};

export function DrillConfigList() {
  const history = useHistory();
  const [authorized] = useAuthorized({ checkByCode: 'element_drill-config-mutate' });
  const [exportLoading, setExportLoading] = useState(false);
  const [form] = Form.useForm<FiltersFormValues>();
  const { search } = useLocation()!;
  const defaultFields = getLocationSearchMap<FilteredValueMapper>(search, {
    arrayKeys: ['createAt', 'blockGuidList', 'excLevelList'],
  });
  const [params, setParams] = useState<FilteredValueMapper>({
    ...defaultFields,
    effectType: defaultFields.effectType ?? ConfigRangeType.Block,
    pageNum: Number(defaultFields.pageNum ?? 1),
    pageSize: Number(defaultFields.pageSize ?? 10),
  });
  const isBlock = params.effectType === ConfigRangeType.Block;
  const [getDrillConfigs, { data, loading }] = useLazyDrillConfigs();
  const fetchDrillConfigs = useCallback(() => {
    const { createdBy, createAt, ...rest } = params;
    getDrillConfigs({
      variables: {
        creatorId: Number(createdBy),
        startDate: createAt ? Number(createAt[0]) : undefined,
        endDate: createAt ? Number(createAt[1]) : undefined,
        ...rest,
      },
    });
  }, [getDrillConfigs, params]);

  useEffect(() => {
    fetchDrillConfigs();
  }, [fetchDrillConfigs]);

  const [deleteDrillConfig] = useDeleteDrillConfig({
    onCompleted(data) {
      if (!data.deleteDrillConfig?.success) {
        message.error(data.deleteDrillConfig?.message ?? '删除失败');
        return;
      } else {
        message.success('删除成功');
        setParams(prev => ({ ...prev, ...defaultPagination }));
      }
    },
  });
  const handleFileExport = useCallback(
    async (type: string) => {
      const { pageNum, pageSize, effectType, createAt, ...rest } = params;
      let exportParams: Omit<FilteredValueMapper, 'pageNum' | 'pageSize' | 'effectType'> & {
        effectType: string;
        startDate?: number;
        endDate?: number;
      } = { effectType: effectType! };
      if (type === 'filtered') {
        exportParams = {
          ...exportParams,
          ...rest,
          startDate: createAt && Array.isArray(createAt) ? createAt[0] : undefined,
          endDate: createAt && Array.isArray(createAt) ? createAt[1] : undefined,
        };
      }
      setExportLoading(true);
      const { error, data } = await exportDrillSchemes(exportParams);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      return data;
    },
    [params]
  );
  const filterItems = useMemo(() => {
    const defaultFilters = [
      {
        label: '方案名称',
        name: 'excName',
        control: <Input allowClear />,
      },
      {
        label: '专业类型',
        name: 'excMajor',
        control: (
          <MetaTypeSelect
            showSearch
            optionLabelProp="label"
            optionFilterProp="label"
            metaType={MetaType.EXC_MAJOR}
            allowClear
          />
        ),
      },
      {
        label: '等级',
        name: 'excLevelList',
        control: (
          <MetaTypeSelect
            mode="multiple"
            maxTagCount="responsive"
            showSearch
            optionLabelProp="label"
            optionFilterProp="label"
            metaType={MetaType.EXC_LEVEL}
            allowClear
          />
        ),
      },
      {
        label: '创建人',
        name: 'createdBy',
        control: <UserSelect allowClear />,
      },
      {
        label: '创建时间',
        name: 'createAt',
        span: 2,
        control: (
          <DatePicker.RangePicker format="YYYY-MM-DD" placeholder={['开始时间', '结束时间']} />
        ),
      },
    ];
    if (isBlock) {
      defaultFilters.splice(1, 0, {
        label: '楼栋',
        name: 'blockGuidList',
        control: (
          <LocationTreeSelect
            multiple
            maxTagCount="responsive"
            authorizedOnly
            disabledTypes={['IDC']}
            allowClear
          />
        ),
      });
    }
    return defaultFilters;
  }, [isBlock]);

  const columns = useMemo(() => {
    const defaultColumns: ColumnsType<DrillConfig> = [
      {
        title: '演练方案名称',
        dataIndex: 'excName',
        width: '35%',
        render: (val, { id, excName }) => (
          <Typography.Text
            ellipsis={{ tooltip: true }}
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={() => {
              history.push(
                generateDrillConfigDetailLocation({
                  id: id.toString(),
                  name: excName,
                })
              );
            }}
          >
            {val}
          </Typography.Text>
        ),
      },
      {
        title: '专业类型',
        dataIndex: 'excMajor',
        render: val => <MetaTypeText code={val} metaType={MetaType.EXC_MAJOR} />,
      },
      {
        title: '等级',
        dataIndex: 'excLevel',
        render: val => <MetaTypeText code={val} metaType={MetaType.EXC_LEVEL} />,
      },
      { title: '步骤条数', dataIndex: 'count', render: val => val },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        width: 200,
        render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
      },
      { title: '创建人', dataIndex: 'creatorId', render: val => <UserLink userId={val} /> },
      {
        title: '操作',
        dataIndex: 'action',
        width: 160,
        render: (_, { id, excName }) => {
          const currentId = id.toString();
          return (
            <Space size={0} split={<Divider type="vertical" spaceSize="mini" />}>
              {authorized && (
                <Link
                  target="_blank"
                  to={generateDrillConfigEditLocation({
                    id: currentId,
                    name: excName,
                  })}
                >
                  编辑
                </Link>
              )}
              <Link
                target="_blank"
                to={generateDrillConfigCopyLocation({
                  id: currentId,
                  name: excName,
                })}
              >
                复制
              </Link>
              {authorized && (
                <DeleteConfirm
                  variant="popconfirm"
                  targetName={excName}
                  title="删除该演练方案？删除后不可恢复，请谨慎操作！"
                  onOk={async () => {
                    await deleteDrillConfig({ variables: { id } });

                    return Promise.resolve(true);
                  }}
                >
                  <Button type="link" compact>
                    删除
                  </Button>
                </DeleteConfirm>
              )}
            </Space>
          );
        },
      },
    ];

    return isBlock
      ? [{ title: '楼栋', dataIndex: 'effectDomain' }, ...defaultColumns]
      : defaultColumns;
  }, [authorized, deleteDrillConfig, history, isBlock]);

  useDeepCompareEffect(() => {
    form.setFieldsValue({
      ...defaultFields,
      createdBy:
        defaultFields.createdBy && defaultFields.createdByName
          ? { value: defaultFields.createdBy, label: defaultFields.createdByName }
          : undefined,
      createAt: defaultFields?.createAt
        ? defaultFields?.createAt.map(time => moment(Number(time)))
        : undefined,
    });
  }, [defaultFields, form]);

  useDeepCompareEffect(() => {
    setLocationSearch(params);
  }, [params]);

  return (
    <Container style={{ padding: 24 }}>
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Row justify="space-between">
          <Typography.Title level={5} showBadge>
            演练方案
          </Typography.Title>
          <ConfigRangeTypeRadioButton
            configRangeType={params.effectType!}
            ticketPrefix="方案"
            onChange={type => {
              setParams({ ...defaultPagination, effectType: type });
              form.resetFields();
            }}
          />
        </Row>
        <QueryFilter<FiltersFormValues>
          form={form}
          items={filterItems}
          onSearch={values => {
            setParams(prev => ({
              ...defaultPagination,
              ...values,
              effectType: prev.effectType,
              createAt:
                Array.isArray(values.createAt) && values.createAt.length === 2
                  ? [
                      values.createAt[0].startOf('day').valueOf(),
                      values.createAt[1].endOf('day').valueOf(),
                    ]
                  : undefined,
              createdBy: values.createdBy?.value,
              createdByName: values.createdBy?.label,
            }));
          }}
          onReset={() => {
            setParams(prev => ({ ...defaultPagination, effectType: prev.effectType }));
            form.resetFields();
          }}
        />
        <Space style={{ display: 'flex', width: '100%', position: 'relative' }}>
          {authorized && (
            <Button type="primary" href={DRILL_CONFIG_CREATE_ROUTE_PATH}>
              新建演练方案
            </Button>
          )}
          <div style={{ position: 'absolute', right: 0, bottom: 0 }}>
            <FileExport
              filename="演练方案明细.xls"
              disabled={exportLoading}
              data={type => {
                return handleFileExport(type);
              }}
              showExportFiltered
            />
          </div>
        </Space>
        <Table
          rowKey="id"
          tableLayout="fixed"
          columns={columns}
          loading={loading}
          dataSource={data?.drillConfigs?.data ?? []}
          pagination={{
            total: data?.drillConfigs?.total ?? 0,
            current: params.pageNum,
            pageSize: params.pageSize,
            onChange: (current, size) => {
              setParams(prev => ({ ...prev, pageNum: current, pageSize: size }));
            },
          }}
        />
      </Space>
    </Container>
  );
}
