import React, { useEffect } from 'react';
import { useParams } from 'react-router';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileList } from '@manyun/base-ui.ui.file-list';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import {
  useDeleteEmergencyProcess,
  useLazyEmergencyProcessDetail,
} from '@manyun/ticket.gql.client.tickets';
import {
  EMERGENCY_PROCEE_LIST_ROUTE_PATH,
  generateEmergencyProcessEditLocation,
} from '@manyun/ticket.route.ticket-routes';

export function EmergencyProcess() {
  const { id } = useParams<{ id: string }>();
  const [, { checkCode }] = useAuthorized();
  const history = useHistory();

  const [getEmergencyProcessDetail, { data }] = useLazyEmergencyProcessDetail();

  const [deleteEmergencyProcess, { loading: invalidateLoading }] = useDeleteEmergencyProcess({
    onCompleted: data => {
      if (data?.deleteEmergencyProcess?.success) {
        history.push(EMERGENCY_PROCEE_LIST_ROUTE_PATH);
      } else {
        message.error(data?.deleteEmergencyProcess?.message);
      }
    },
  });

  useEffect(() => {
    getEmergencyProcessDetail({ variables: { emergencyId: id } });
  }, [id, getEmergencyProcessDetail]);

  if (!data?.emergencyProcessDetail?.data) {
    return <Spin />;
  }

  const { data: emergencyProcess } = data.emergencyProcessDetail;
  const isEdit = checkCode('element_ticket_emergency-process-operate');

  return (
    <>
      <Card
        bordered={false}
        headStyle={{ borderBottom: 0 }}
        bodyStyle={{ padding: '0 24px' }}
        title={
          <Typography.Title showBadge level={5}>
            基本信息
          </Typography.Title>
        }
      >
        <Descriptions column={4} contentStyle={{ overflow: 'hidden', paddingRight: 16 }}>
          <Descriptions.Item label="应急流程名称">{emergencyProcess.name}</Descriptions.Item>
          <Descriptions.Item label="适用楼栋">{emergencyProcess.blockGuid}</Descriptions.Item>
          <Descriptions.Item label="专业分类">{emergencyProcess.categoryName}</Descriptions.Item>
        </Descriptions>
      </Card>
      <Card bordered={false}>
        <FileList
          title="附件"
          files={emergencyProcess.fileInfoList ?? []}
          groups={[
            {
              title: '文件',
              fileTypes: ['others', 'pdf'],
            },
            {
              title: '图片',
              fileTypes: ['image', 'video'],
              previewable: true,
              showName: true,
            },
          ]}
        />
      </Card>
      <FooterToolBar>
        <Space size={16}>
          {isEdit && (
            <Button
              type="primary"
              onClick={() =>
                history.push(
                  generateEmergencyProcessEditLocation({
                    id,
                  })
                )
              }
            >
              编辑
            </Button>
          )}
          {isEdit && (
            <Popconfirm
              title="删除该应急流程？删除后不可恢复，请谨慎操作！"
              style={{ width: 290 }}
              okText="确认删除"
              placement="topRight"
              okButtonProps={{ disabled: invalidateLoading }}
              onConfirm={() =>
                deleteEmergencyProcess({
                  variables: { emergencyIdList: [id] },
                })
              }
            >
              <Button disabled={invalidateLoading}>删除</Button>
            </Popconfirm>
          )}
        </Space>
      </FooterToolBar>
    </>
  );
}
