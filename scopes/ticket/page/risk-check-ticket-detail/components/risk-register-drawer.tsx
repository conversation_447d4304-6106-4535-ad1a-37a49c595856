import dayjs from 'dayjs';
import React, { useEffect } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import type { DrawerProps } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useHitRiskPoint } from '@manyun/ticket.gql.client.tickets';
import type { BackendTicket } from '@manyun/ticket.model.ticket';
import { RiskRegisterForm } from '@manyun/ticket.page.risk-register-mutator';
import type { RiskRegisterFormValues } from '@manyun/ticket.page.risk-register-mutator';

export type RiskRegisterDrawerProps = {
  blockGuid: string;
  riskPointInfo: {
    id: number;
    taskNo: string;
    riskCategory: string;
    riskType: string;
    riskLevel: string;
  };

  basicInfo: BackendTicket;
  setOpen: (param: boolean) => void;
  callback: () => {};
  riskDesc?: string;
} & DrawerProps;
export function RiskRegisterDrawer({
  blockGuid,
  basicInfo,
  riskPointInfo,
  callback,
  setOpen,
  riskDesc,
  ...props
}: RiskRegisterDrawerProps) {
  const [form] = Form.useForm<RiskRegisterFormValues>();
  const [hitRiskPoint, { loading }] = useHitRiskPoint({
    onCompleted(data) {
      if (!data.hitRiskPoint?.success) {
        message.error(data.hitRiskPoint?.message ?? '命中风险点失败');
        return;
      }
      message.success('命中风险点成功');
      callback();
      setOpen(false);
      return;
    },
  });

  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  // @ts-ignore type error
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';
  const onSubmit = () => {
    form.validateFields().then(async values => {
      const {
        riskDesc,
        fileInfoList,
        riskObjects,
        riskObjectType,
        riskIdentifier,
        riskOwnerIdList,
        longTermRisk,
        planCompleteTime,
        locationList,
      } = values;
      const { id, taskNo, riskCategory, riskType, riskLevel } = riskPointInfo;
      const riskObjectsParams =
        typeof riskObjects === 'string'
          ? [{ label: riskObjects, objectName: riskObjects }]
          : riskObjects;
      const fileInfoListParams =
        Array.isArray(fileInfoList) && fileInfoList?.length > 0
          ? fileInfoList.map(file => McUploadFile.fromJSON(file).toJSON())
          : [];
      const variables: any = {
        blockGuid,
        idcTag: blockGuid.split('.')[0],
        assigneeId: basicInfo.taskAssignee!,
        taskNo,
        pointId: id,
        riskDesc: riskDesc!,
        fileInfoList: fileInfoListParams,
        riskLevel,
        riskCategory,
        riskResourceCode: '4',
        riskType,
        riskOwnerIdList: riskOwnerIdList!,
        riskIdentifier,
        locationList: locationList.length
          ? locationList.map(item => ({
              locationType: item.locationType,
              subType: item.subType,
              guid: item.guid,
            }))
          : [],
      };

      if (!isFull && riskObjectType) {
        variables.riskObjectType = riskObjectType;
      }
      if (!isFull && riskObjectsParams?.length) {
        variables.riskObjectName = riskObjectsParams.map(item => item.objectName || '').join(',');
      }
      if (isFull) {
        variables.longTermRisk = longTermRisk || false;
      }
      if (isFull && planCompleteTime) {
        variables.planCompleteTime = dayjs(planCompleteTime).valueOf();
      }

      await hitRiskPoint({
        variables,
      });
    });
  };
  useEffect(() => {
    if (riskDesc) {
      form.setFieldsValue({ riskDesc: riskDesc });
    }
  }, [form, riskDesc]);
  return (
    <Drawer
      {...props}
      extra={
        <Space>
          <Button
            onClick={() => {
              setOpen(false);
            }}
          >
            取消
          </Button>
          <Button type="primary" loading={loading} onClick={onSubmit}>
            提交
          </Button>
        </Space>
      }
    >
      <RiskRegisterForm
        blockGuid={blockGuid}
        form={form}
        unusedFormItems={[
          'blockGuid',
          'riskResourceCode',
          'riskCategoryCode',
          'riskTypeCode',
          'riskLevel',
          'riskInfluenceDesc',
          'riskOwner',
          'relatePerson',
          ...(isFull ? (['riskIdentifier'] as const) : []),
        ]}
      />
    </Drawer>
  );
}
