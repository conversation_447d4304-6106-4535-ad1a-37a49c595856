import React, { useEffect, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import {
  useBatchOperateRiskPools,
  useLazyRiskOrderCheckItems,
} from '@manyun/ticket.gql.client.risk-register';
import type { BackendTicket } from '@manyun/ticket.model.ticket';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import { RiskPoolFilterForm } from '@manyun/ticket.ui.risk-pool-filter-form';
import { RiskPoolTable } from '@manyun/ticket.ui.risk-pool-table';

import { RiskRegisterDrawer } from './components/risk-register-drawer';

export type RiskCheckTicketDetailProps = {
  taskNo: string;
  basicInfo: BackendTicket;
  getBasicInfo: () => void;
};
export function RiskCheckTicketDetail({
  taskNo,
  basicInfo,
  getBasicInfo,
}: RiskCheckTicketDetailProps) {
  const isTicketStatusAwaitHandle = basicInfo.taskStatus === BackendTaskStatus.WAITTAKEOVER;
  const isTicketFinishedOrFailure = [BackendTaskStatus.FINISH, BackendTaskStatus.FAILURE].includes(
    basicInfo.taskStatus
  );
  const isTicketStatusProcessing = basicInfo.taskStatus === BackendTaskStatus.PROCESSING;
  const [riskPointBasicInfo, setRiskPointBasicInfo] = useState<{
    id: number;
    taskNo: string;
    riskCategory: string;
    riskType: string;
    riskLevel: string;
  }>();
  const [riskDesc, setRiskDesc] = useState<string>();
  const [selectedIds, setSelectedIds] = useState<React.Key[]>([]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [selectedRows, setSelectedRows] = useState<any[]>([]);
  const [open, setOpen] = useState(false);
  const { taskAssignee } = basicInfo;
  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  // @ts-ignore type error
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';

  const [batchOperateRiskPools] = useBatchOperateRiskPools({
    onCompleted(data) {
      if (!data.batchOperateRiskPools?.success) {
        message.error(data.batchOperateRiskPools?.message ?? '未命中失败');
        return;
      } else {
        message.success('未命中成功');
        return;
      }
    },
  });
  const [getTicketRelateRiskPools, { loading, data: riskPools }] = useLazyRiskOrderCheckItems();
  useEffect(() => {
    if (taskNo) {
      (async function () {
        await getTicketRelateRiskPools({ variables: { query: { taskNo, isFlatten: true } } });
      })();
    }
  }, [taskNo, getTicketRelateRiskPools]);
  const [, { checkUserId }] = useAuthorized();
  const isUserHandler = Boolean(taskAssignee && checkUserId(taskAssignee));
  const onSuccess = async () => {
    await getTicketRelateRiskPools({ variables: { query: { taskNo, isFlatten: true } } });
    getBasicInfo();
  };
  return (
    <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
      <Space>
        {isUserHandler && isTicketStatusProcessing && (
          <Popconfirm
            placement="topLeft"
            title={
              <Typography.Text>
                确认批量置为“未命中”？批量识别对所有已选 <br />
                项生效，请谨慎核对！
              </Typography.Text>
            }
            okText="确认"
            cancelText="取消"
            okButtonProps={{ loading: loading }}
            onConfirm={async () => {
              await batchOperateRiskPools({
                variables: {
                  assigneeId: taskAssignee!,
                  pointIdList: selectedRows.map(item => getRiskPointId(item.id)),
                  taskNo,
                },
              });
              onSuccess();
              setSelectedIds([]);
              setSelectedRows([]);
            }}
          >
            <Button type="primary" disabled={Boolean(selectedRows.length === 0)}>
              批量未命中风险
            </Button>
          </Popconfirm>
        )}
        <RiskPoolFilterForm
          unusedFormItems={['isEnable', 'creatorId', 'createTime', 'updateTime']}
          onSearch={async params => {
            await getTicketRelateRiskPools({
              variables: { query: { ...params, taskNo, isFlatten: true } },
            });
          }}
        />
      </Space>
      <RiskPoolTable
        showColumns={['riskCategory', 'riskType', 'riskLevel', 'riskDesc', 'verify', 'fileId']}
        rowKey="id"
        rowSelection={
          isUserHandler && isTicketStatusProcessing
            ? {
                selectedRowKeys: selectedIds,
                onChange: (selectedRowKeys, selectedRows) => {
                  // 保留之前选择的表格数据（即不在当前 dataSource 中的）

                  setSelectedIds(keys => [
                    ...keys.filter(
                      key => !riskPools?.riskOrderCheckItems.data.find(item => item.id === key)
                    ),
                    ...selectedRowKeys,
                  ]);
                  setSelectedRows(rows => [
                    ...rows.filter(
                      row => !riskPools?.riskOrderCheckItems.data.find(item => item.id === row.id)
                    ),
                    ...selectedRows,
                  ]);
                },
                getCheckboxProps: record => ({
                  disabled: record.verifyResult === '1',
                }),
                renderCell: (_, record, index, originNode) => {
                  return {
                    children: originNode, //显示原来的dom显示
                    props: {
                      //通过属性的配置来做单元格合并,rowNum 为合并几行
                      rowSpan: record.mergeRowsNum,
                    },
                  };
                },
              }
            : undefined
        }
        operationColumn={{
          dataIndex: 'verifyResult',
          title: '是否命中风险',
          width: 177,
          onCell: record => {
            return {
              rowSpan: record.mergeRowsNum,
            };
          },
          render: (
            _,
            { id, verifyResult, verifyId, riskCategory, riskType, riskLevel, riskDesc }
          ) => {
            if (isTicketStatusAwaitHandle || (!isUserHandler && isTicketStatusProcessing)) {
              return '--';
            }
            if (isUserHandler && isTicketStatusProcessing) {
              return (
                <Radio.Group
                  style={{ width: 168 }}
                  value={verifyResult}
                  disabled={verifyResult === '1'}
                  options={[
                    { label: '命中', value: '1' },
                    { label: '未命中', value: '0' },
                  ]}
                  onChange={async e => {
                    if (taskAssignee && e.target.value === '0') {
                      await batchOperateRiskPools({
                        variables: {
                          pointIdList: [getRiskPointId(id)],
                          assigneeId: taskAssignee,
                          taskNo,
                        },
                      });
                      onSuccess();
                    } else {
                      setRiskPointBasicInfo({
                        id: getRiskPointId(id),
                        taskNo,
                        riskCategory,
                        riskType,
                        riskLevel,
                      });
                      setRiskDesc(riskDesc);
                      setOpen(true);
                    }
                  }}
                />
              );
            }
            if (isTicketFinishedOrFailure) {
              if (!verifyResult) {
                return (
                  <Space>
                    <Badge status="warning" />
                    未识别
                  </Space>
                );
              }
              return verifyResult && verifyResult === '1' ? (
                <Space>
                  <Badge status="error" />
                  命中
                </Space>
              ) : (
                <Space>
                  <Badge status="success" />
                  未命中
                </Space>
              );
            }
            return '--';
          },
        }}
        dataSource={riskPools?.riskOrderCheckItems.data ?? []}
        loading={loading}
      />
      <RiskRegisterDrawer
        title="登记风险"
        size="large"
        width={isFull ? 1280 : undefined}
        blockGuid={basicInfo.blockTag}
        basicInfo={basicInfo}
        riskPointInfo={riskPointBasicInfo!}
        riskDesc={riskDesc}
        open={open}
        setOpen={setOpen}
        callback={onSuccess}
        onClose={() => {
          setOpen(false);
        }}
      />
    </Space>
  );
}

function getRiskPointId(id: string) {
  return Number(id.split('__')[0]);
}
