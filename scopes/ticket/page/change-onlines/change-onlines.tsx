import dayjs from 'dayjs';
import { omit } from 'lodash';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  useCancelChangeOnline,
  useLazyChanges,
  useRevertChangeApproval,
} from '@manyun/ticket.gql.client.tickets';
import type { FetchChangeQ, FetchChangesData } from '@manyun/ticket.gql.client.tickets';
import {
  CHANGE_ONLINE_RESULT_KEY_TEXT_MAP,
  CHANGE_ONLINE_TICKET_STATUS_TEXT_MAP,
  CHANGE_RESULT_KEY_MAP,
  ChangeOnlineTicketState,
  ChangeTicketState,
} from '@manyun/ticket.model.change';
import {
  CHANGE_ONLINE_CREATE_ROUTE_PATH,
  generateChangeOnlineCreateLocation,
  generateChangeOnlineDetailLocation,
  generateChangeOnlineEditLocation,
  generateChangeTicketDetail,
} from '@manyun/ticket.route.ticket-routes';
import { deleteChange } from '@manyun/ticket.service.delete-change';
import { exportChange } from '@manyun/ticket.service.export-change';

import { PostponeChange } from './components/postpone-change';
import { RescheduleChange } from './components/reschedule-change';

type SearchField = Omit<FetchChangeQ, 'planTime'> & {
  availAreaList?: string | null;
  planTime?: [Moment, Moment] | null;
  executeTime?: [Moment, Moment] | null;
  status?: string[];
  location?: string | null;
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';
const CHANGE_ONLINE_TICKET_STATUS_COLOR_MAP = {
  [ChangeOnlineTicketState.Approving]: 'warning',
  [ChangeOnlineTicketState.SummaryApproving]: 'warning',
  [ChangeOnlineTicketState.Cancel]: 'default',
  [ChangeOnlineTicketState.Draft]: 'default',
  [ChangeOnlineTicketState.Finish]: 'default',
  [ChangeOnlineTicketState.WaitingChange]: 'processing',
  [ChangeOnlineTicketState.Changing]: 'processing',
  [ChangeOnlineTicketState.InSummary]: 'processing',
};
const sorterMaps: Record<string, string> = {
  planStartTime: 'PLAN_START_TIME',
  realStartTime: 'REAL_START_TIME',
};
const changeTypeBadgeMaps = {
  SUPPLEMENTARY: 'default',
  OVERDUE: 'default',
  PLANNED: 'success',
  UNPLANNED: 'warning',
  URGENT: 'error',
};

export function ChangeOnlines() {
  const [exportLoading, setExportLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [changeOrderId, setChangeOrderId] = useState('');
  const [, { checkCode, checkUserId }] = useAuthorized();
  const isCreate = checkCode('element_ticket_change-online-create');

  const [form] = Form.useForm();
  const [cancelForm] = Form.useForm();

  const [getList, { data, loading }] = useLazyChanges({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  const { search } = useLocation();

  const {
    pageNum,
    pageSize,
    status,
    idcTag,
    blockTag,
    executeEndTime,
    executeStartTime,
    planEndTime,
    statusList,
    planStartTime,
    respPersonIdList,
    changeCategoryList,
    planEndTimeToSecond,
    planStartTimeToSecond,
    ...restDefaultFields
  } = getLocationSearchMap<SearchField>(search, {
    parseNumbers: true,
    arrayKeys: [
      'changeLevelList',
      'changeReasonList',
      'status',
      'changeCategoryList',
      'changeTypeList',
      'blockTagList',
      'statusList',
      'respPersonIdList',
    ],
  });
  const [fields, setFields] = useState<
    Omit<SearchField, 'statusList' | 'respPersonIdList'> & {
      statusList?: string | null;
      respPersonIdList?: string | null;
    }
  >({
    statusList: statusList ? statusList.join(',') : '',
    respPersonIdList: respPersonIdList ? respPersonIdList.join(',') : '',
    ...restDefaultFields,
    planTime:
      planEndTimeToSecond && planStartTimeToSecond
        ? [moment(planStartTimeToSecond), moment(planEndTimeToSecond)]
        : undefined,
    executeTime:
      executeEndTime && executeStartTime
        ? [moment(executeStartTime), moment(executeEndTime)]
        : undefined,
    location: blockTag ?? idcTag,
    pageNum: pageNum ?? 1,
    pageSize: pageSize ?? 10,
    changeCategoryList: changeCategoryList?.length
      ? changeCategoryList.map(item => item.toString())
      : [],
  });

  const history = useHistory();
  const [revertChangeApproval] = useRevertChangeApproval();
  const reloadData = useCallback(
    (action?: 'delete') => {
      if (action === 'delete') {
        setFields(pre => {
          return {
            ...pre,
            pageNum:
              (pre.pageNum - 1) * pre.pageSize + 1 === data?.changes?.total && pre.pageNum > 1
                ? pre.pageNum - 1
                : pre.pageNum,
          };
        });
      } else {
        setFields(pre => ({ ...pre }));
      }
    },
    [data?.changes?.total]
  );

  useEffect(() => {
    getList({ variables: { query: { pageNum: 1, pageSize: 10, changeVersion: 3 } } });
  }, [getList]);

  useEffect(() => {
    getList({
      variables: {
        query: {
          changeVersion: 3,
          ...getParams(),
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, getList]);

  const getParams = () => {
    const {
      planTime,
      executeTime,
      location,
      changeOrderId,
      statusList,
      respPersonIdList,
      ...resp
    } = fields;
    const [idcTag, blockTag] = (location ?? '').split('.');
    form.setFieldsValue(fields);

    const baseQ: FetchChangeQ = {
      ...resp,
      changeOrderId: changeOrderId?.trim(),
      planStartTimeToSecond: planTime?.length ? planTime[0].valueOf() : null,
      planEndTimeToSecond: planTime?.length ? planTime[1].valueOf() : null,
      executeStartTime: executeTime?.length ? executeTime[0].valueOf() : null,
      executeEndTime: executeTime?.length ? executeTime[1].valueOf() : null,
      idcTag,
      blockTag: blockTag ? location : null,
      statusList: statusList ? [statusList] : [],
      respPersonIdList: respPersonIdList ? [respPersonIdList] : [],
    };
    setLocationSearch(omit(baseQ, 'sortField', 'sortOrder'));
    return baseQ;
  };

  const handleFileExport = useCallback(
    async type => {
      setExportLoading(true);
      let params = {};
      if (type === 'filtered') {
        params = { ...getParams() };
      }
      const { error, data } = await exportChange({ ...params, changeVersion: 3 });
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return false;
      }
      return data;
    },
    //  eslint-disable-next-line react-hooks/exhaustive-deps
    [fields]
  );
  const [cancelChangeOnline, { loading: cancelLoading }] = useCancelChangeOnline({
    onCompleted(data) {
      if (!data.cancelChangeOnline?.success) {
        message.error(data.cancelChangeOnline?.message);
        return;
      }
      setVisible(false);
      setChangeOrderId('');
      reloadData();
    },
  });

  const onSubmit = async () => {
    cancelForm.validateFields().then(values => {
      cancelChangeOnline({
        variables: {
          query: {
            changeOrderId,
            cancelReason: values.cancelReason,
          },
        },
      });
    });
  };

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <QueryFilter
            form={form}
            items={[
              {
                label: '变更ID',
                name: 'changeOrderId',
                control: <Input allowClear />,
              },
              {
                label: '变更名称',
                name: 'title',
                control: <Input allowClear />,
              },
              {
                label: '变更区域',
                name: 'blockTagList',
                control: <LocationTreeSelect authorizedOnly allowClear multiple />,
              },
              {
                label: '变更状态',
                name: 'statusList',
                control: (
                  <Select allowClear>
                    {Object.entries(CHANGE_ONLINE_TICKET_STATUS_TEXT_MAP).map(item => (
                      <Select.Option key={item[0]} value={item[0]}>
                        {item[1]}
                      </Select.Option>
                    ))}
                  </Select>
                ),
              },
              {
                label: '变更结果',
                name: 'exeResult',
                control: (
                  <Select allowClear>
                    {Object.entries(CHANGE_ONLINE_RESULT_KEY_TEXT_MAP).map(item => (
                      <Select.Option key={item[0]} value={item[0]}>
                        {item[1]}
                      </Select.Option>
                    ))}
                  </Select>
                ),
              },
              {
                label: '变更等级',
                name: 'changeLevelList',
                control: (
                  <MetaTypeSelect
                    metaType={MetaType.CHANGE_ONLINE_LEVEL}
                    allowClear
                    mode="multiple"
                  />
                ),
              },
              {
                label: '变更专业',
                name: 'changeReasonList',
                control: (
                  <MetaTypeSelect
                    metaType={MetaType.CHANGE_ONLINE_REASON}
                    allowClear
                    mode="multiple"
                  />
                ),
              },
              {
                label: '变更类别',
                name: 'changeCategoryList',
                control: (
                  <MetaTypeSelect
                    metaType={MetaType.CHANGE_ONLINE_CATEGORY}
                    allowClear
                    mode="multiple"
                  />
                ),
              },
              {
                label: '变更类型',
                name: 'changeTypeList',
                control: (
                  <MetaTypeSelect
                    metaType={MetaType.CHANGE_ONLINE_TYPE}
                    allowClear
                    mode="multiple"
                  />
                ),
              },
              {
                label: '提单人',
                name: 'creatorId',
                control: <UserSelect labelInValue={false} allowClear />,
              },
              // {
              //   label: '负责人',
              //   name: 'respPersonId',
              //   control: <UserSelect labelInValue={false} allowClear />,
              // },
              // {
              //   label: '执行人',
              //   name: 'operatorId',
              //   control: <UserSelect labelInValue={false} allowClear />,
              // },
              {
                label: '变更窗口期',
                name: 'planTime',
                span: 2,
                control: (
                  <DatePicker.RangePicker
                    format="YYYY-MM-DD HH:mm"
                    allowClear
                    showTime={{ format: 'HH:mm' }}
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
              },
              {
                label: '执行时间',
                name: 'executeTime',
                span: 2,
                control: (
                  <DatePicker.RangePicker
                    format="YYYY-MM-DD HH:mm"
                    allowClear
                    showTime
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
              },

              {
                label: '变更负责人',
                name: 'respPersonIdList',
                control: <UserSelect labelInValue={false} allowClear />,
              },
            ]}
            // initialValues={fields}
            onSearch={value => {
              setFields({ ...value, pageNum: 1, pageSize: fields.pageSize });
            }}
            onReset={() => {
              form.setFieldsValue({ changeVersion: 3, pageNum: 1, pageSize: fields.pageSize });
              // setLocationSearch({ changeVersion: 2, pageNum: 1, pageSize: fields.pageSize });
              setFields({ changeVersion: 3, pageNum: 1, pageSize: fields.pageSize });
            }}
          />
        </Card>
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space
              style={{ width: '100%', justifyContent: 'space-between' }}
              direction="horizontal"
              size="middle"
            >
              {isCreate ? (
                <Button
                  type="primary"
                  onClick={() => history.push(CHANGE_ONLINE_CREATE_ROUTE_PATH)}
                >
                  新建变更
                </Button>
              ) : (
                <span />
              )}
              <FileExport
                text="导出"
                filename="变更.xls"
                disabled={exportLoading}
                data={type => {
                  return handleFileExport(type);
                }}
                showExportFiltered
              />
            </Space>

            <Table<FetchChangesData>
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading}
              dataSource={data?.changes?.data ?? []}
              columns={[
                {
                  title: '变更ID',
                  dataIndex: 'changeOrderId',
                  fixed: true,
                  render: (_, { changeOrderId }) => {
                    if (changeOrderId) {
                      return (
                        <Link
                          target="_blank"
                          to={generateChangeTicketDetail({
                            id: encodeURIComponent(changeOrderId),
                          })}
                        >
                          {changeOrderId}
                        </Link>
                      );
                    }
                    return '--';
                  },
                },
                {
                  title: '变更名称',
                  dataIndex: 'title',
                  render: title => {
                    return (
                      <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: title }}>
                        {title}
                      </Typography.Text>
                    );
                  },
                },
                {
                  title: '变更区域',
                  dataIndex: 'blockTag',
                  render: (_, { blockTag, idcTag }) => blockTag ?? idcTag,
                },
                {
                  title: '变更专业',
                  dataIndex: 'reason',
                  render: reason => (
                    <MetaTypeText metaType={MetaType.CHANGE_ONLINE_REASON} code={reason} />
                  ),
                },

                {
                  title: '变更类别',
                  dataIndex: 'changeCategory',
                  render: category => (
                    <MetaTypeText metaType={MetaType.CHANGE_ONLINE_CATEGORY} code={category} />
                  ),
                },

                {
                  title: '变更等级',
                  dataIndex: 'riskLevel',
                  render: riskLevel => (
                    <MetaTypeText metaType={MetaType.CHANGE_ONLINE_LEVEL} code={riskLevel} />
                  ),
                },
                {
                  title: '变更类型',
                  dataIndex: 'changeType',
                  render: changeType => (
                    <Space>
                      <Badge status={changeTypeBadgeMaps[changeType]} />
                      <MetaTypeText metaType={MetaType.CHANGE_ONLINE_TYPE} code={changeType} />
                    </Space>
                  ),
                },

                {
                  title: '变更窗口期',
                  dataIndex: 'planStartTime',
                  sorter: true,
                  render: (_, { planStartTime, planEndTime, changeTimeList }) => {
                    return (
                      <>
                        {planStartTime ? dayjs(planStartTime).format('YYYY-MM-DD HH:mm') : '--'} -
                        <br />
                        {planEndTime ? dayjs(planEndTime).format('YYYY-MM-DD HH:mm') : '--'}
                      </>
                    );
                  },
                },
                {
                  title: '执行时间',
                  dataIndex: 'realStartTime',
                  sorter: true,
                  render: (_, { realStartTime, realEndTime }) => {
                    if (realStartTime && realEndTime) {
                      return (
                        <>
                          {dayjs(realStartTime).format('YYYY-MM-DD HH:mm')} -<br />
                          {dayjs(realEndTime).format('YYYY-MM-DD HH:mm')}
                        </>
                      );
                    }
                    if (realStartTime && !realEndTime) {
                      return (
                        <>
                          {dayjs(realStartTime).format('YYYY-MM-DD HH:mm')} -<br />
                          --
                        </>
                      );
                    }
                    return '--';
                  },
                },

                {
                  title: '变更状态',
                  dataIndex: 'changeStatus',
                  render: changeStatus => (
                    <Tag color={CHANGE_ONLINE_TICKET_STATUS_COLOR_MAP[changeStatus]}>
                      {
                        CHANGE_ONLINE_TICKET_STATUS_TEXT_MAP[
                          changeStatus as ChangeOnlineTicketState
                        ]
                      }
                    </Tag>
                  ),
                },
                {
                  title: '变更结果',
                  dataIndex: 'exeResult',
                  render: exeResult => {
                    if (!exeResult) {
                      return '--';
                    }
                    return (
                      <Tag color={exeResult === CHANGE_RESULT_KEY_MAP.Failed ? 'error' : 'success'}>
                        {CHANGE_ONLINE_RESULT_KEY_TEXT_MAP[exeResult as CHANGE_RESULT_KEY_MAP]}
                      </Tag>
                    );
                  },
                },
                {
                  title: '提单人',
                  dataIndex: 'creatorName',
                  render: (creatorName, text) =>
                    text.creatorId ? (
                      <User name={creatorName} id={text.creatorId} showAvatar={false} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '变更负责人',
                  dataIndex: 'changeResponsibleUserList',
                  render: changeResponsibleUserList =>
                    changeResponsibleUserList?.length
                      ? changeResponsibleUserList.map(item => item.userName).join(' | ')
                      : '--',
                },
                {
                  title: '操作',
                  dataIndex: 'action',
                  fixed: 'right',
                  render: (
                    _,
                    {
                      changeOrderId,
                      changeStatus,
                      creatorId,
                      changeResponsibleUserList,
                      planEndTime,
                      planStartTime,
                      existDelayApproval,
                    }
                  ) => {
                    const authorized = checkUserId(creatorId);
                    const operatorAuthorized = changeResponsibleUserList?.some(item =>
                      checkUserId(item.userId)
                    );
                    const copy = (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          history.push(generateChangeOnlineCreateLocation({ id: changeOrderId }));
                        }}
                      >
                        复制
                      </Button>
                    );
                    const deleteChangeView = (
                      <Popconfirm
                        key="delete"
                        title="确认删除该变更？删除后不可恢复，请谨慎操作！"
                        okText="确定删除"
                        onConfirm={async () => {
                          const { error } = await deleteChange({ changeOrderId });
                          if (error) {
                            message.error(error.message);
                            return;
                          }
                          reloadData('delete');
                        }}
                      >
                        <Button compact type="link">
                          删除
                        </Button>
                      </Popconfirm>
                    );
                    if (changeStatus === ChangeTicketState.Draft) {
                      return (
                        <Space direction="horizontal" size="small">
                          {(authorized || operatorAuthorized) && (
                            <Link
                              to={generateChangeOnlineEditLocation({
                                id: changeOrderId,
                              })}
                            >
                              编辑
                            </Link>
                          )}

                          {(authorized || operatorAuthorized) && deleteChangeView}
                          {isCreate && copy}
                        </Space>
                      );
                    }
                    if (changeStatus === ChangeTicketState.Changing) {
                      return (
                        <Space>
                          {operatorAuthorized && !existDelayApproval && (
                            <PostponeChange
                              type="link"
                              compact
                              title="延期"
                              changeOrderId={changeOrderId}
                              planEndTime={planEndTime}
                              onSuccess={() => {
                                reloadData();
                              }}
                            />
                          )}
                          {isCreate && copy}
                        </Space>
                      );
                    }
                    if (changeStatus === ChangeTicketState.WaitingChange) {
                      return (
                        <Space>
                          {operatorAuthorized && (
                            <RescheduleChange
                              changeOrderId={changeOrderId}
                              planEndTime={planEndTime}
                              planStartTime={planStartTime}
                              type="link"
                              compact
                              title="改期"
                              onSuccess={() => {
                                reloadData();
                              }}
                            />
                          )}
                          {operatorAuthorized && (
                            <Button
                              type="link"
                              compact
                              onClick={() => {
                                setChangeOrderId(changeOrderId);
                                setVisible(true);
                              }}
                            >
                              取消变更
                            </Button>
                          )}
                          {isCreate && copy}
                        </Space>
                      );
                    }
                    if (
                      changeStatus === ChangeTicketState.Approving ||
                      changeStatus === ChangeTicketState.SummaryApproving
                    ) {
                      return (
                        <Space>
                          {authorized && (
                            <Button
                              type="link"
                              compact
                              onClick={() => {
                                revertChangeApproval({
                                  variables: { changeOrderId },
                                  onCompleted(data) {
                                    if (data.revertChangeApproval?.message) {
                                      message.error(data.revertChangeApproval?.message);
                                      return;
                                    }
                                    reloadData();
                                  },
                                });
                              }}
                            >
                              撤回审批
                            </Button>
                          )}
                          {isCreate && copy}
                        </Space>
                      );
                    }
                    if (changeStatus === ChangeTicketState.InSummary) {
                      return (
                        <Space>
                          {operatorAuthorized && (
                            <Button
                              type="link"
                              compact
                              onClick={() => {
                                history.push(
                                  generateChangeOnlineDetailLocation({
                                    id: changeOrderId,
                                    currentTab: 'summaryRecords',
                                  })
                                );
                              }}
                            >
                              变更总结
                            </Button>
                          )}
                          {isCreate && copy}
                        </Space>
                      );
                    }

                    return isCreate ? copy : '--';
                  },
                },
              ]}
              pagination={{
                total: data?.changes?.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, sorter, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                  setLocationSearch({
                    ...fields,
                    pageNum: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                }
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setFields(pre => ({
                        ...pre,
                        sortField: null,
                        sortOrder: null,
                      }));
                      // setLocationSearch({
                      //   ...fields,
                      //   sortField: null,
                      //   sortOrder: null,
                      // });
                      return;
                    }
                    // setLocationSearch({
                    //   ...fields,
                    //   sortField: sorterMaps[sorter.field as string],
                    //   sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                    // });
                    setFields(pre => ({
                      ...pre,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'ASCEND' : 'DESCEND',
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
        <Modal
          title="取消变更"
          open={visible}
          okText="确认取消"
          width={640}
          okButtonProps={{ disabled: cancelLoading }}
          afterClose={() => cancelForm.resetFields()}
          onCancel={() => {
            setVisible(false);
          }}
          onOk={onSubmit}
        >
          <Alert
            description="是否取消变更？取消后变更单将变为「已取消」状态，无法继续操作"
            type="warning"
            style={{ marginBottom: 24 }}
            showIcon
          />
          <Form form={cancelForm} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
            <Form.Item
              label="变更取消原因"
              name="cancelReason"
              rules={[
                { required: true, message: '变更取消原因必填！', whitespace: true },
                { max: 300, message: '最多输入 300 个字符！' },
              ]}
            >
              <Input.TextArea rows={3} showCount maxLength={300} />
            </Form.Item>
          </Form>
        </Modal>
      </Space>
    </>
  );
}
