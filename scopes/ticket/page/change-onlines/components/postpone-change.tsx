import moment from 'moment';
import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { usePostponeChange } from '@manyun/ticket.gql.client.tickets';

export type PostponeChangeProps = {
  changeOrderId: string;
  planEndTime?: string | null;
  title?: string;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;
function generateRange(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export function PostponeChange({
  changeOrderId,
  planEndTime,
  title = '变更延期',
  onSuccess,
  ...restProps
}: PostponeChangeProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();

  const [postponeChangeOnline, { loading: postponeRiskRegisterLoading }] = usePostponeChange({
    onCompleted: data => {
      if (!data.postponeChangeOnline?.success) {
        message.error(data.postponeChangeOnline?.message);
        return;
      }
      setVisible(false);
      onSuccess();
    },
  });

  return (
    <>
      <Button
        {...restProps}
        onClick={event => {
          setVisible(true);
        }}
      >
        {title}
      </Button>
      <Modal
        title="变更延期"
        open={visible}
        okText="确认延期"
        width={640}
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: postponeRiskRegisterLoading }}
        onCancel={event => {
          event.stopPropagation();
          setVisible(false);
        }}
        onOk={async event => {
          event.stopPropagation();
          form.validateFields().then(async values => {
            postponeChangeOnline({
              variables: {
                query: {
                  changeOrderId,
                  reason: values.reason,
                  endTime: values.endTime.valueOf(),
                },
              },
            });
          });
        }}
      >
        <Alert
          description="变更延期提交后，将进行审批，审批通过后方可生效，是否确认延期？"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form}>
          <Form.Item label="原计划结束时间" name="time">
            {moment(planEndTime).format('YYYY-MM-DD HH:mm')}
          </Form.Item>
          <Form.Item
            label="变更延期至"
            name="endTime"
            rules={[{ required: true, message: '变更延期至必选！' }]}
          >
            <DatePicker
              picker="date"
              showNow={false}
              placeholder="请输入新的计划结束时间"
              disabledDate={current => {
                /**不可早于计划开始时间 */
                return current < moment(planEndTime).startOf('day');
              }}
              disabledTime={current => {
                const compareMoment = moment(planEndTime);
                const hour = compareMoment.hour();
                const minute = compareMoment.minute();
                const second = compareMoment.second();
                if (current && current.isSame(compareMoment, 'day')) {
                  const currentHour = current.hour();
                  const currentMinute = current.minute();
                  if (currentHour === hour) {
                    return {
                      disabledHours: () => generateRange(0, hour),
                      disabledMinutes: () => generateRange(0, minute + 1),
                      disabledSeconds: () =>
                        currentMinute === minute ? generateRange(0, second) : [],
                    };
                  } else {
                    return {
                      disabledHours: () => generateRange(0, hour),
                      disabledMinutes: () => [],
                      disabledSeconds: () => [],
                    };
                  }
                }
                return {
                  disabledHours: () => [],
                  disabledMinutes: () => [],
                  disabledSeconds: () => [],
                };
              }}
              showTime
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
          <Form.Item
            label="变更延期原因"
            name="reason"
            rules={[
              { required: true, whitespace: true, message: '变更延期原因必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
