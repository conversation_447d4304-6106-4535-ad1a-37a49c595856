import moment from 'moment';
import type { Moment } from 'moment';
import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useRescheduleChangeOnline } from '@manyun/ticket.gql.client.tickets';

export type RescheduleChangeProps = {
  changeOrderId: string;
  planEndTime?: number | null;
  planStartTime?: number | null;
  title?: string;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;
type RangeValue = [Moment | null, Moment | null] | null;

export function RescheduleChange({
  changeOrderId,
  planEndTime,
  planStartTime,
  title = '变更改期',
  onSuccess,
  ...restProps
}: RescheduleChangeProps) {
  const [visible, setVisible] = useState(false);
  const [dates, setDates] = useState<RangeValue>(null);

  const [form] = Form.useForm();
  const planTime = Form.useWatch('time', form);

  const [rescheduleChangeOnline, { loading: postponeRiskRegisterLoading }] =
    useRescheduleChangeOnline({
      onCompleted: data => {
        if (!data.rescheduleChangeOnline?.success) {
          message.error(data.rescheduleChangeOnline?.message);
          return;
        }
        setVisible(false);
        onSuccess();
      },
    });
  const disabledDate = (current: Moment) => {
    if (!dates) {
      return false;
    }
    const tooLate =
      dates[0] &&
      (current.diff(dates[0], 'days') > 7 || (current && current < moment().subtract(8, 'days')));
    // const tooEarly = dates[1] && dates[1].diff(current, 'days') > 7;
    return !!tooLate || (current && current < moment().subtract(15, 'days'));
  };

  const onOpenChange = (open: boolean) => {
    if (open) {
      setDates([null, null]);
    } else {
      setDates(null);
    }
  };
  return (
    <>
      <Button
        {...restProps}
        onClick={event => {
          setVisible(true);
        }}
      >
        {title}
      </Button>
      <Modal
        title="变更改期"
        open={visible}
        okText="确认改期"
        width={640}
        afterClose={() => form.resetFields()}
        okButtonProps={{ loading: postponeRiskRegisterLoading }}
        onCancel={event => {
          event.stopPropagation();
          setVisible(false);
        }}
        onOk={async event => {
          event.stopPropagation();
          form.validateFields().then(async values => {
            rescheduleChangeOnline({
              variables: {
                query: {
                  changeOrderId,
                  reason: values.reason,
                  startDate: values.time[0].valueOf(),
                  endDate: values.time[1].valueOf(),
                },
              },
            });
          });
        }}
      >
        <Alert
          description="变更改期提交后，将重新进行「变更申请审批」，是否确认改期？"
          type="warning"
          showIcon
          style={{ marginBottom: 24 }}
        />

        <Form labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} form={form}>
          <Form.Item label="原变更窗口期" name="time">
            {moment(planStartTime).format('YYYY-MM-DD HH:mm')} ~{' '}
            {moment(planEndTime).format('YYYY-MM-DD HH:mm')}
          </Form.Item>
          <Form.Item
            label="新变更窗口"
            name="time"
            rules={[
              {
                validator: (_, rang) => {
                  if (!rang) {
                    return Promise.reject('变更窗口期必选！');
                  }
                  if (
                    rang[0] &&
                    moment(rang[0], 'YYYY-MM-DD HH:mm').isBefore(
                      moment().subtract(14, 'days'),
                      'minutes'
                    )
                  ) {
                    return Promise.reject('变更计划开始不可早于14天前');
                  }
                  if (rang[0] && rang[1]) {
                    if (
                      moment(rang[1], 'YYYY-MM-DD HH:mm').isAfter(
                        moment(rang[0], 'YYYY-MM-DD HH:mm').add(7, 'days'),
                        'minutes'
                      )
                    ) {
                      return Promise.reject('变更计划结束不得超过变更计划开始时间7天后');
                    }
                    if (
                      moment(rang[1], 'YYYY-MM-DD HH:mm').isBefore(
                        moment().subtract(7, 'days'),
                        'minutes'
                      )
                    ) {
                      return Promise.reject('变更计划结束不可早于7天前');
                    }
                    return Promise.resolve();
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <DatePicker.RangePicker
              style={{ width: '100%' }}
              placeholder={['变更计划开始时间', '变更计划结束时间']}
              showTime={{ format: 'HH:mm' }}
              disabledDate={disabledDate}
              disabledTime={(current, type) => {
                if (type === 'end' && planTime?.[0]) {
                  const now = moment().subtract(7, 'days');
                  const isSameDay = !current || current.isSame(now, 'day');
                  const isSameHour = !current || current.isSame(now, 'hour');
                  if (isSameDay) {
                    return {
                      disabledHours: isSameDay ? () => range(now.hours() + 1, 60) : undefined,
                      disabledMinutes:
                        isSameDay && isSameHour ? () => range(now.minutes() + 1, 60) : undefined,
                    };
                  }
                  const startSeven = moment(planTime[0]).add(7, 'days');
                  const isStartSameDay = !current || current.isSame(startSeven, 'day');
                  const isStartSameHour = !current || current.isSame(startSeven, 'hour');
                  if (isStartSameDay) {
                    return {
                      disabledHours: isStartSameDay ? () => range(now.hours() + 1, 60) : undefined,
                      disabledMinutes:
                        isStartSameDay && isStartSameHour
                          ? () => range(now.minutes() + 1, 60)
                          : undefined,
                    };
                  }
                  return {};
                }
                const now = moment().subtract(14, 'days');
                const isSameDay = !current || current.isSame(now, 'day');
                const isSameHour = !current || current.isSame(now, 'hour');
                const isSameMinute = !current || current.isSame(now, 'minute');

                if (type === 'start' || isSameDay) {
                  return {
                    disabledHours: isSameDay ? () => range(0, now.hours()) : undefined,
                    disabledMinutes:
                      isSameDay && isSameHour ? () => range(0, now.minutes()) : undefined,
                    disabledSeconds:
                      isSameDay && isSameHour && isSameMinute
                        ? () => [0, now.seconds()]
                        : undefined,
                  };
                }
                return {};
              }}
              onCalendarChange={val => {
                setDates(val);
                // form.setFieldValue('planTime', val);
              }}
              onOpenChange={onOpenChange}
            />
          </Form.Item>
          <Form.Item
            label="变更改期原因"
            name="reason"
            rules={[
              { required: true, whitespace: true, message: '变更改期原因必填！' },
              { max: 300, message: '最多输入 300 个字符！' },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
function range(start: number, end: number) {
  const result: number[] = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }

  return result;
}
