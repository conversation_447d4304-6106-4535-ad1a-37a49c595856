import React from 'react';
import { useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';

import { PlanType } from '@manyun/ticket.model.task';
import { TicketTaskDetailInfo } from '@manyun/ticket.ui.ticket-task-detail-info';

export function RiskCheckTaskDetail() {
  const { id } = useParams<{ id: string }>();
  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }} color="default">
      <TicketTaskDetailInfo
        id={id}
        planType={PlanType.RiskCheckPlan}
        unusedDescriptionsItems={[
          'guidePeriod',
          'jobType',
          'subJobType',
          'periodUnit',
          'jobItems',
          'allowTriggerTime',
          'endTime',
          'lastExecuteTime',
          'lastExecuteResult',
          'drillMajorType',
          'drillLevel',
          'creator',
        ]}
      />
    </Container>
  );
}
