import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { FilterOutlined } from '@ant-design/icons';
import type { ColumnsType } from 'antd/es/table';
import omit from 'lodash.omit';
import moment from 'moment';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import type { ExportType } from '@manyun/base-ui.ui.file-export/dist/file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import {
  type BackendDutyGroup,
  fetchPagedDutyGroup,
} from '@manyun/hrm.service.fetch-paged-duty-group';
import { type BackendChangeShiftStatus, ChangeShiftStatusMap } from '@manyun/ticket.model.ticket';
import { BackendChangeShiftStatus as ChangeShiftStatus } from '@manyun/ticket.model.ticket';
import { Countersign } from '@manyun/ticket.page.change-shift';
import {
  CHANGE_SHIFT_NEW,
  generateChangeShiftDetail,
  generateChangeShiftEdit,
} from '@manyun/ticket.route.ticket-routes';
import {
  type ApiQ as exportChangeApiQ,
  exportChangeShifts,
} from '@manyun/ticket.service.export-change-shifts';
import {
  type ApiQ,
  type BackendChangeShifts,
  type ChangeShiftsRes,
  fetchChangesShifts,
} from '@manyun/ticket.service.fetch-change-shifts';
import { revokeChangeShift } from '@manyun/ticket.service.revoke-change-shift';
import { takeOverChangeShift } from '@manyun/ticket.service.take-over-change-shift';

import { CancelBtn } from './components/cancel-btn';
import { ChangeShiftConfig } from './components/config';

const ChangeShiftsStatusTagColorMapper: Record<
  BackendChangeShiftStatus,
  'geekblue' | 'volcano' | 'green' | 'default'
> = {
  INIT: 'geekblue',
  WAIT_HANDLE: 'volcano',
  HANDED: 'green',
  CANCELED: 'default',
  COUNTERSIGN: 'geekblue',
};

export function ChangeShifts() {
  const [changeShifts, setChangeShifts] = useState<ChangeShiftsRes>({
    list: [],
    total: 0,
  });
  const [visible, setVisible] = useState(false);
  const [dutyGroupLoading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [options, setOptions] = useState<BackendDutyGroup[]>([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);

  const history = useHistory();

  const [form] = Form.useForm();
  const [, { checkUserId }] = useAuthorized();

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const showCountersign = ticketScopeCommonConfigs.changeShift.features.showCountersign;
  const { search } = useLocation();

  const { pageNum, pageSize, ...restDefaultFields } = getLocationSearchMap<
    Partial<
      ApiQ & {
        pageNum: number;
        pageSize: number;
        handoverEndDate?: Moment;
        handoverStartDate?: Moment;
      }
    >
  >(search, {
    parseNumbers: true,
    arrayKeys: ['statusList', 'counterSigners'],
  });
  const [pagination, setPagination] = React.useState({
    pageNum: pageNum ?? 1,
    pageSize: pageSize ?? 10,
  });

  const [fields, setFields] = useState<
    Omit<ApiQ, 'pageNum' | 'pageSize'> & {
      dateTime?: Moment[];
    }
  >({
    ...restDefaultFields,
    dateTime:
      restDefaultFields.handoverStartDate && restDefaultFields.handoverEndDate
        ? [moment(restDefaultFields.handoverStartDate), moment(restDefaultFields.handoverEndDate)]
        : undefined,
  });

  useEffect(() => {
    form.setFieldsValue({ ...fields });
    getList();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pagination]);

  const searchHandler = async () => {
    if (dutyGroupLoading) {
      return;
    }
    setLoading(true);
    const { data, error } = await fetchPagedDutyGroup({
      pageNum: 1,
      pageSize: 1000,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setOptions(data.data);
  };

  const getList = React.useCallback(
    async () => {
      if (tableLoading) {
        return;
      }
      setTableLoading(true);
      const params: ApiQ = {
        pageNum: pagination.pageNum,
        pageSize: pagination.pageSize,
        ...fields,
        ...getParams(),
      };

      const { data, error } = await fetchChangesShifts(params);
      setTableLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setLocationSearch(params);
      setChangeShifts({ list: data.data, total: data.total });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [pagination]
  );

  const takeOver = async (bizNo: string) => {
    const { error } = await takeOverChangeShift({ bizNo });
    if (!error) {
      message.success('接班成功');
      getList();
    } else {
      message.error(error.message);
    }
  };

  const revoke = async (bizNo: string) => {
    const { error } = await revokeChangeShift({ bizNo });
    if (!error) {
      getList();
    } else {
      message.error(error.message);
    }
  };

  const getParams = () => {
    const { dateTime, dutyName, dutyGroupId, handUserId } = form.getFieldsValue();
    const params: ApiQ | exportChangeApiQ = {
      // dutyName,
      // dutyGroupId,
    };
    if (Array.isArray(dateTime) && dateTime?.length === 2) {
      params.handoverStartDate = dateTime[0].clone().startOf('day');
      params.handoverEndDate = dateTime[1].clone().endOf('day');
    }
    if (dutyName) {
      params.dutyName = dutyName;
    }
    if (dutyGroupId) {
      params.dutyGroupId = dutyGroupId;
    }
    if (handUserId) {
      params.handUserId = handUserId;
    }
    return params;
  };

  const handleFileExport = useCallback(
    async (type: ExportType) => {
      setExportLoading(true);
      let params: exportChangeApiQ = {};
      if (type === 'filtered') {
        params = { ...getParams() };
      }
      if (type === 'selected') {
        params.bizNos = selectedKeys;
      }
      const { error, data } = await exportChangeShifts(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return false;
      }
      return data;
    },
    //  eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedKeys]
  );

  const hasFilter = () => {
    return Object.values(
      omit(fields, 'dateTime', 'pageSize', 'pageNum', 'handoverStartDate', 'handoverEndDate')
    ).some(value => value !== undefined);
  };

  return (
    <Card title="线上交接班">
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Form form={form} wrapperCol={{ span: 16 }} labelCol={{ span: 6 }}>
          <Space style={{ justifyContent: 'space-between', width: '100%' }}>
            <Space size="middle">
              <Button type="primary" onClick={() => history.push(CHANGE_SHIFT_NEW)}>
                新建交接班工单
              </Button>
              <Form.Item noStyle label=" " name="dateTime">
                <Button style={{ borderRightWidth: 0 }}>值班日期</Button>
                <DatePicker.RangePicker
                  allowClear
                  style={{ width: 248 }}
                  value={form.getFieldValue('dateTime')}
                  onChange={dates => {
                    form.setFieldsValue({ dateTime: dates });
                    getList();
                  }}
                />
              </Form.Item>
              <Dropdown
                trigger={['click']}
                open={visible}
                overlay={
                  <Card
                    style={{ width: 363 }}
                    bodyStyle={{ paddingBottom: 0 }}
                    actions={[
                      <Space
                        key="space"
                        style={{ display: 'flex', justifyContent: 'flex-end', marginRight: 16 }}
                      >
                        <Button
                          onClick={() => {
                            form.resetFields();
                            setVisible(false);
                            setFields({});
                            setPagination({ pageNum: 1, pageSize: pagination.pageSize });
                          }}
                        >
                          重置
                        </Button>
                        <Button
                          type="primary"
                          onClick={() => {
                            setVisible(false);
                            setFields({
                              ...form.getFieldsValue(),
                            });
                            setPagination({ pageNum: 1, pageSize: pagination.pageSize });
                          }}
                        >
                          搜索
                        </Button>
                      </Space>,
                    ]}
                  >
                    <Form.Item label="值班班次" name="dutyName">
                      <Input
                        style={{ width: '236px' }}
                        allowClear
                        onChange={value => {
                          form.setFieldValue('dutyName', value.target.value || undefined);
                        }}
                      />
                    </Form.Item>
                    <Form.Item label="交班班组" name="dutyGroupId">
                      <Select style={{ width: '236px' }} allowClear onFocus={() => searchHandler()}>
                        {options.map(item => (
                          <Select.Option key={item.id} value={item.id}>
                            {item.groupName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item label="责任人" name="handUserId">
                      <UserSelect
                        labelInValue={false}
                        allowClear
                        style={{ width: '236px' }}
                        placeholder="接班负责人"
                      />
                    </Form.Item>
                    <Form.Item label="状态" name="statusList">
                      <Select
                        allowClear
                        style={{ width: '236px' }}
                        mode="multiple"
                        options={
                          showCountersign
                            ? Object.keys(ChangeShiftStatusMap).map(status => ({
                                label: ChangeShiftStatusMap[status as BackendChangeShiftStatus],
                                value: status,
                              }))
                            : Object.keys(ChangeShiftStatusMap)
                                .map(status => ({
                                  label: ChangeShiftStatusMap[status as BackendChangeShiftStatus],
                                  value: status,
                                }))
                                .filter(item => item.value !== ChangeShiftStatus.COUNTERSIGN)
                        }
                      />
                    </Form.Item>
                    {showCountersign && (
                      <Form.Item label="加签人" name="counterSigners">
                        <UserSelect
                          labelInValue={false}
                          mode="multiple"
                          allowClear
                          style={{ width: '236px' }}
                          placeholder="加签人"
                        />
                      </Form.Item>
                    )}
                  </Card>
                }
                placement="bottomLeft"
                arrow
                onOpenChange={_visible => {
                  setVisible(_visible);
                  if (_visible === true) {
                    form.setFieldsValue({
                      ...fields,
                    });
                  }
                }}
              >
                <Button
                  icon={<FilterOutlined />}
                  type={hasFilter() ? 'primary' : 'default'}
                  ghost={hasFilter()}
                  onClick={() => {
                    setVisible(!visible);
                  }}
                />
              </Dropdown>
            </Space>
            <Space direction="horizontal">
              <ChangeShiftConfig />
              <FileExport
                text="导出"
                filename="线上交接班.xls"
                disabled={exportLoading}
                data={type => {
                  return handleFileExport(type);
                }}
                showExportFiltered
                showExportSelected={!!selectedKeys.length}
              />
            </Space>
          </Space>
        </Form>
        <Table
          scroll={{ x: 'max-content' }}
          rowKey="bizNo"
          columns={getColumns(showCountersign, takeOver, checkUserId, revoke, getList)}
          dataSource={changeShifts.list}
          pagination={{
            total: changeShifts.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
          }}
          rowSelection={{
            selectedRowKeys: selectedKeys,
            onChange: ids => {
              setSelectedKeys(ids as string[]);
            },
          }}
          onChange={pagination => {
            setPagination({ pageNum: pagination.current!, pageSize: pagination.pageSize! });
          }}
        />
      </Space>
    </Card>
  );
}

export const getColumns = (
  showCountersign: boolean,
  takeOver: (bizNo: string) => void,
  checkUserId: (_checkByUserId: number) => boolean,
  revoke: (bizNo: string) => void,
  getList: () => void
) => {
  return [
    {
      title: '工单单号',
      dataIndex: 'bizNo',
      ellipsis: true,
      fixed: 'left',
      render: (text: string) => <Link to={generateChangeShiftDetail({ id: text })}>{text}</Link>,
    },
    {
      title: '工单标题',
      dataIndex: 'taskTitle',
      ellipsis: true,
    },
    {
      title: '值班日期',
      dataIndex: 'handoverDate',
      render: (text: number) => moment(text).format('YYYY-MM-DD'),
      // ellipsis: true,
      // render: (text: string) => <UserGenderText userGender={text} />,
    },
    {
      title: '值班班次',
      dataIndex: 'applyDutyName',
      // ellipsis: true,
    },
    {
      title: '接班班组',
      dataIndex: 'handDutyGroupName',
      ellipsis: true,
    },
    {
      title: '位置',
      dataIndex: 'blockGuid',
    },
    {
      title: '交班负责人',
      dataIndex: 'applyUserId',
      render: (text: number) => <User.Link id={text} />,
    },
    {
      title: '接班负责人',
      dataIndex: 'handUserId',
      render: (text: number) => <User.Link id={text} />,
    },
    {
      title: '状态',
      dataIndex: 'bizStatus',
      ellipsis: true,
      render: (text: BackendChangeShiftStatus) => (
        <Tag color={ChangeShiftsStatusTagColorMapper[text]}>
          {ChangeShiftStatusMap[text] ?? '--'}
        </Tag>
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, record: BackendChangeShifts) => {
        const isApplyUser = checkUserId(record.applyUserId);
        const isHandeUser = checkUserId(record.handUserId);
        const dutyUsers = showCountersign
          ? record.groupUsers?.some(item => checkUserId(item))
          : true;
        const handoverCounterSigners = record.handoverCounterSigners?.some(
          item => !item.finished && checkUserId(item.staffId)
        );
        const cancelBtn = (
          <CancelBtn
            bizNo={record.bizNo}
            style={{ padding: 0, height: 'auto' }}
            onSuccess={() => {
              getList();
            }}
          />
        );
        const revokeBtn = (
          <Popconfirm
            title="确认撤回交班？撤回后交接班工单将退回至“已创建”状态。"
            overlayStyle={{ width: 236 }}
            okText="确认撤回"
            cancelText="我再想想"
            placement="topLeft"
            onConfirm={() => {
              revoke(record.bizNo);
            }}
          >
            <Button type="link" style={{ padding: 0, height: 'auto' }}>
              撤回
            </Button>
          </Popconfirm>
        );
        if (record.bizStatus === 'INIT' || record.bizStatus === 'COUNTERSIGN') {
          return (
            <Space size="small">
              {(isApplyUser || dutyUsers || handoverCounterSigners) && (
                <Link key="edit" to={generateChangeShiftEdit({ id: record.bizNo })}>
                  编辑
                </Link>
              )}
              {showCountersign && isApplyUser && record.bizStatus === 'INIT' && (
                <Countersign
                  id={record.bizNo}
                  blockGuid={record.blockGuid}
                  compact
                  type="link"
                  onSuccess={() => {
                    getList();
                  }}
                />
              )}
              {record.bizStatus === 'COUNTERSIGN' && isApplyUser && revokeBtn}
              {(isApplyUser || dutyUsers || handoverCounterSigners) && cancelBtn}
            </Space>
          );
        }
        if (record.bizStatus === 'WAIT_HANDLE' && isHandeUser) {
          return (
            <Button
              type="link"
              style={{ padding: 0, height: 'auto' }}
              onClick={() => {
                takeOver(record.bizNo);
              }}
            >
              接班
            </Button>
          );
        }
        if (record.bizStatus === 'WAIT_HANDLE' && isApplyUser) {
          return revokeBtn;
        }
        return '--';
      },
    },
  ] as ColumnsType<BackendChangeShifts>;
};
