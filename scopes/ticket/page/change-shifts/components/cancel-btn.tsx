import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';

import { cancelChangeShift } from '@manyun/ticket.service.cancel-change-shift';

export type CancelBtnProps = {
  bizNo: string;
  onSuccess: () => void;
} & ButtonProps;

export function CancelBtn({ bizNo, type = 'link', onSuccess, ...props }: CancelBtnProps) {
  const cancel = async () => {
    const { error } = await cancelChangeShift({ bizNo });
    if (!error) {
      message.success('取消成功');
      onSuccess();
    } else {
      message.error(error.message);
    }
  };
  return (
    <Popconfirm
      title="确认取消交班？取消后交接班工单将被作废。"
      overlayStyle={{ width: 236 }}
      okText="取消交班"
      cancelText="我再想想"
      placement="topLeft"
      onConfirm={() => {
        cancel();
      }}
    >
      <Button type={type} {...props}>
        取消
      </Button>
    </Popconfirm>
  );
}
