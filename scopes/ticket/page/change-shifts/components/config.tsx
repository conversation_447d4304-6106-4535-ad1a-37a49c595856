import { SettingOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import {
  useLazyChangeShiftConfig,
  useUpdateChangeShiftConfig,
} from '@manyun/ticket.gql.client.tickets';
import { HANDOVER_CONFIG_TEXT_MAP, HandoverConfig } from '@manyun/ticket.model.ticket';

export function ChangeShiftConfig() {
  const [visible, setVisible] = useState(false);
  const [getChangeShiftConfig] = useLazyChangeShiftConfig({
    onCompleted(data) {
      if (data.fetchChangeShiftConfig?.success) {
        form.setFieldsValue({
          ...data.fetchChangeShiftConfig,
          handoverDutyCheck: !!data.fetchChangeShiftConfig.handoverDutyCheck,
        });
      }
    },
  });
  const [updateChangeShiftConfig] = useUpdateChangeShiftConfig();
  const [form] = Form.useForm();

  const handoverDutyCheck = Form.useWatch('handoverDutyCheck', form);

  useEffect(() => {
    if (visible) {
      getChangeShiftConfig();
    }
  }, [visible, getChangeShiftConfig]);

  return (
    <>
      <Button
        type="link"
        icon={<SettingOutlined />}
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        配置
      </Button>
      <Modal
        open={visible}
        width={485}
        title="配置"
        onOk={() => {
          form.validateFields().then(async values => {
            updateChangeShiftConfig({
              variables: {
                query: {
                  ...values,
                  handoverDutyCheck: values.handoverDutyCheck ? 1 : 0,
                },
              },
              onCompleted(data) {
                if (!data.updateChangeShiftConfig?.success) {
                  message.error(data.updateChangeShiftConfig?.message);
                } else {
                  setVisible(false);
                }
              },
            });
          });
        }}
        onCancel={() => setVisible(false)}
      >
        <Form form={form}>
          <Form.Item label="" name="handoverDutyCheck" valuePropName="checked">
            <Checkbox
              onChange={value => {
                if (value) {
                  form.setFieldValue('handoverDutyCheckType', HandoverConfig.SuccessionLeader);
                } else {
                  form.setFieldValue('handoverDutyCheckType', undefined);
                }
              }}
            >
              接班班组已打上班卡，交班班组才可交班
            </Checkbox>
          </Form.Item>
          {handoverDutyCheck && (
            <Form.Item
              label=""
              name="handoverDutyCheckType"
              rules={[{ required: true, message: '请选择配置！' }]}
            >
              <Radio.Group
                options={[
                  {
                    label: HANDOVER_CONFIG_TEXT_MAP[HandoverConfig.SuccessionLeader],
                    value: HandoverConfig.SuccessionLeader,
                  },
                  {
                    label: HANDOVER_CONFIG_TEXT_MAP[HandoverConfig.AllTeamMembers],
                    value: HandoverConfig.AllTeamMembers,
                  },
                ]}
              />
            </Form.Item>
          )}
        </Form>
      </Modal>
    </>
  );
}
