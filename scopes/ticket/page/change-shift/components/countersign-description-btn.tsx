import React, { useState } from 'react';

import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { useSubmitChangeShiftCountersignDescriptionData } from '@manyun/ticket.gql.client.tickets';

export type CountersignDescriptionProps = {
  id: string;
  blockGuid: string;
  onSuccess: () => void;
};

export function CountersignDescription({ id, blockGuid, onSuccess }: CountersignDescriptionProps) {
  const [forcedChangeShift, setForcedChangeShift] = useState<boolean>(false);
  const [form] = Form.useForm();
  const fileList = Form.useWatch('files', form);
  const [submit, { loading }] = useSubmitChangeShiftCountersignDescriptionData();

  return (
    <Space direction="vertical" size="middle">
      <Button loading={loading} type="primary" onClick={() => setForcedChangeShift(true)}>
        加签说明
      </Button>
      <Modal
        open={forcedChangeShift}
        width={560}
        title="加签"
        okText="提交"
        okButtonProps={{ loading: loading }}
        onOk={() => {
          form.validateFields().then(async values => {
            submit({
              variables: {
                query: {
                  bizNo: id,
                  content: values.content,
                  files: values.files?.map((obj: McUploadFile) => McUploadFile.fromJSON(obj)),
                },
              },
              onCompleted(data) {
                if (!data.submitChangeShiftCountersignDescription?.success) {
                  message.error(data.submitChangeShiftCountersignDescription?.message);
                  return;
                }
                setForcedChangeShift(false);
                onSuccess();
              },
            });
          });
        }}
        onCancel={() => {
          setForcedChangeShift(false);
        }}
      >
        <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Alert description="当所有加签人全部完成加签后，将自动提交交班。" type="warning" />

            <Form.Item
              label="加签说明"
              name="content"
              rules={[
                { required: true, message: '请填写加签说明' },
                {
                  max: 300,
                  message: '最多输入300个字符',
                },
              ]}
            >
              <Input.TextArea rows={2} />
            </Form.Item>
            <Form.Item
              label="附件"
              name="files"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <Upload
                disabled={fileList?.length > 5}
                accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
                maxCount={5}
                openFileDialogOnClick={fileList === undefined || fileList?.length < 5}
              >
                <Button icon={<UploadOutlined />}>点此上传</Button>
              </Upload>
            </Form.Item>
          </Space>
        </Form>
      </Modal>
    </Space>
  );
}
