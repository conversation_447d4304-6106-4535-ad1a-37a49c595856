import React, { useEffect, useState } from 'react';

import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Steps } from '@manyun/base-ui.ui.steps';

import { BackendChangeShiftStatus } from '@manyun/ticket.model.ticket';
import type { ChangeShiftJSON } from '@manyun/ticket.service.fetch-change-shift';

export type StepProps = {
  info: ChangeShiftJSON;
};

export function Step({ info }: StepProps) {
  const [steps, setSteps] = useState<{ title: string; time: number | null }[]>([]);
  const [currentStep, setcurrentStep] = useState<number>(0);

  useEffect(() => {
    const list = [];
    const statusStepMap: Record<BackendChangeShiftStatus, number> = {
      [BackendChangeShiftStatus.INIT]: 1,
      [BackendChangeShiftStatus.WAIT_HANDLE]: 2,
      [BackendChangeShiftStatus.COUNTERSIGN]: 1,
      [BackendChangeShiftStatus.CANCELED]: 5,
      [BackendChangeShiftStatus.HANDED]: 4,
    };
    list.push({ title: '创建', time: info.gmtCreate });
    if (info.bizStatus === BackendChangeShiftStatus.CANCELED) {
      list.push({ title: '取消', time: info.gmtCreate });
    } else {
      if (info.handoverCounterSigners?.length) {
        list.push({ title: '加签', time: info.counterSignTime });
        statusStepMap[BackendChangeShiftStatus.WAIT_HANDLE] = 3;
        statusStepMap[BackendChangeShiftStatus.HANDED] = 5;
      } else {
        statusStepMap[BackendChangeShiftStatus.WAIT_HANDLE] = 2;
        statusStepMap[BackendChangeShiftStatus.HANDED] = 3;
      }
      list.push({ title: '交班', time: info.submitTime });
      list.push({ title: '接班', time: info.handTime });
    }
    setcurrentStep(statusStepMap[info.bizStatus]);
    setSteps(list);
  }, [info]);

  return (
    <Card bordered={false}>
      <Steps
        current={currentStep}
        labelPlacement="vertical"
        items={steps.map(item => ({
          title: item.title,
          description: (
            <div style={{ fontSize: '12px' }}>
              {item.time && dayjs(item.time).format('YYYY-MM-DD HH:mm')}
            </div>
          ),
        }))}
      />
    </Card>
  );
}
