import dayjs from 'dayjs';
import React, { useState } from 'react';

import { User } from '@manyun/auth-hub.ui.user';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form, type FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { scrollToField } from '@manyun/dc-brain.util.scroll-to-field';
import {
  useLazyChangeShiftConfig,
  useLazyQueryFetchChangeShiftAttendanceResult,
} from '@manyun/ticket.gql.client.tickets';
import type { FetchChangeShiftAttendanceResult } from '@manyun/ticket.gql.client.tickets';
import { type ApiQ } from '@manyun/ticket.service.create-change-shift';
import type { HandoverEvents } from '@manyun/ticket.service.fetch-change-shift';
import { saveAndSubmitChangeShift } from '@manyun/ticket.service.save-and-submit-change-shift';
import { submitChangeShift } from '@manyun/ticket.service.submit-change-shift';

export type HandoverProps = {
  id?: string;
  form?: FormInstance;
  time?: number;
  handoverEvents?: HandoverEvents[];
  staffId?: number;
  groupScheduleId?: number;
  setTabActiveKey?: (key: string) => void;
  onSuccess: (id?: string | null) => void;
};

export function Handover({
  id,
  handoverEvents,
  time,
  form,
  staffId,
  groupScheduleId,
  setTabActiveKey,
  onSuccess,
}: HandoverProps) {
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [forcedChangeShift, setForcedChangeShift] = useState<boolean>(false);
  const [getChangeShiftAttendanceResult, { data }] = useLazyQueryFetchChangeShiftAttendanceResult();
  const [getChangeShiftConfig] = useLazyChangeShiftConfig({});
  const [forcedChangeShiftForm] = Form.useForm();

  const submit = async () => {
    if (form) {
      form
        .validateFields()
        .then(() => {
          onSubmitBefore();
        })
        .catch(err => {
          setTabActiveKey && setTabActiveKey('matter');
          if (form) {
            scrollToField(form.scrollToField, err.errorFields);
          }
        });
    } else {
      onSubmitBefore();
    }
  };

  const onSubmitBefore = () => {
    if (!time) {
      return;
    }
    const now = new Date().getTime();
    const timeDifferenceValue = (time - now) / 1000 / 60;
    if (timeDifferenceValue <= 30 && timeDifferenceValue > 0) {
      Modal.confirm({
        content: '交班时间还没到，确认现在交班吗？',
        okText: '确认',
        cancelText: '取消',
        onOk: async () => {
          onSubmit();
        },
      });
      return;
    }
    onSubmit();
  };

  const onSubmit = async () => {
    setSubmitting(true);
    const { data } = await getChangeShiftConfig();
    if (data?.fetchChangeShiftConfig?.success && data?.fetchChangeShiftConfig?.handoverDutyCheck) {
      const { data: attendanceResult } = await getChangeShiftAttendanceResult({
        variables: {
          query: {
            staffId: data?.fetchChangeShiftConfig.handoverDutyCheckType ? null : staffId,
            groupScheduleId,
          },
        },
      });
      if (
        Array.isArray(attendanceResult?.fetchChangeShiftAttendanceResult?.data) &&
        attendanceResult.fetchChangeShiftAttendanceResult.data.some(item => !item?.onDutyTime)
      ) {
        setSubmitting(false);
        setForcedChangeShift(true);
      } else {
        onConfirmSubmit();
      }
      return;
    }
    onConfirmSubmit();
  };

  const onConfirmSubmit = async () => {
    setSubmitting(true);
    let errorMessage = '';
    let newId: string | null | undefined = id;
    const submitReason = forcedChangeShift
      ? forcedChangeShiftForm.getFieldValue('submitReason')
      : null;
    const checkResults =
      forcedChangeShift && data?.fetchChangeShiftAttendanceResult?.data?.length
        ? (data.fetchChangeShiftAttendanceResult.data as FetchChangeShiftAttendanceResult[])
        : null;
    // 详情点击交班
    if (id && !form) {
      const { error } = await submitChangeShift({ bizNo: id, submitReason, checkResults });
      if (error) {
        errorMessage = error.message;
      }
    }

    if (form) {
      const p = getParams();
      if (!p) {
        return;
      }
      // 编辑点击交班
      if (id) {
        const { error } = await saveAndSubmitChangeShift({
          ...p,
          bizNo: id,
          submitReason,
          checkResults,
        });
        if (error) {
          errorMessage = error.message;
        }
      }
      // 新建点击交班
      if (!id) {
        const { error, data } = await saveAndSubmitChangeShift({
          ...p,
          submitReason,
          checkResults,
        });
        if (error) {
          errorMessage = error.message;
        }
        newId = data;
      }
    }

    setSubmitting(false);
    if (errorMessage) {
      message.error(errorMessage);
      return;
    }
    onSuccess(newId);
    message.success('交班成功！');
  };

  const getParams = () => {
    const { applyDuty, handDuty, handUserId, fileList, handInfo, applyUserId, ccStaffIds } =
      form!.getFieldsValue();
    if (applyDuty?.scheduleId && handDuty?.scheduleId && handUserId) {
      const _p: ApiQ = {
        handoverEvents,
        handoverInfo: handInfo,
        applyScheduleId: applyDuty.scheduleId,
        handScheduleId: handDuty.scheduleId,
        handUserId: handUserId,
        applyUserId: applyUserId,
        ccStaffIds,
        files:
          Array.isArray(fileList) && fileList.length
            ? fileList.map((file: McUploadFileJSON) => McUploadFile.fromJSON(file).toApiObject())
            : [],
      };
      return _p;
    }
    return null;
  };

  return (
    <Space direction="vertical" size="middle">
      <Button type="primary" loading={submitting} onClick={submit}>
        确认交班
      </Button>
      <Modal
        open={forcedChangeShift}
        width={560}
        title="强制交班"
        okText="强制交班"
        okButtonProps={{ loading: submitting }}
        onOk={() => {
          forcedChangeShiftForm.validateFields().then(async () => {
            onConfirmSubmit();
          });
        }}
        onCancel={() => {
          setForcedChangeShift(false);
          setSubmitting(false);
        }}
      >
        <Form form={forcedChangeShiftForm} layout="vertical">
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Alert
              message="是否强制交班？"
              description="当前接班班组还未打卡，是否强制交班？"
              type="warning"
              showIcon
            />
            <Space direction="vertical">
              <Typography.Text>接班人员上班打卡情况：</Typography.Text>
              <Space direction="horizontal" wrap style={{ width: '100%', display: 'flex' }}>
                {data?.fetchChangeShiftAttendanceResult?.data.map(item => (
                  <div
                    key={item?.staffId}
                    style={{
                      width: 120,
                      height: 62,
                      border: '1px solid var(--border-color-split)',
                      borderRadius: 8,
                      display: 'flex',
                      flexDirection: 'column',
                      justifyContent: 'center',
                      paddingLeft: 8,
                      paddingRight: 8,
                    }}
                  >
                    <User showAvatar={false} id={item?.staffId} />
                    {item?.onDutyTime ? (
                      <Typography.Text strong>
                        {dayjs(item.onDutyTime).format('HH:mm')}
                      </Typography.Text>
                    ) : (
                      <Typography.Text strong type="danger">
                        未打卡
                      </Typography.Text>
                    )}
                  </div>
                ))}
              </Space>
            </Space>
            <Form.Item
              label="强制交班原因："
              name="submitReason"
              rules={[
                { required: true, message: '请输入强制交班原因' },
                {
                  max: 300,
                  message: '最多输入300个字符',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
          </Space>
        </Form>
      </Modal>
    </Space>
  );
}
