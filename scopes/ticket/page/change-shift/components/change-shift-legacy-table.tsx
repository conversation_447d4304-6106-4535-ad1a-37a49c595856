import React, { useCallback, useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { type RiskRegisterBaseInfoResponse } from '@manyun/ticket.gql.client.risk-register';
import { useJudgeEventBetaVersion, useLazyAllEvents } from '@manyun/ticket.gql.client.tickets';
import { ChangeTicketState } from '@manyun/ticket.model.change';
import {
  BackendEventStatusCode,
  type Event,
  EventBetaProcessStatus,
  EventSpecificProcessStatus,
} from '@manyun/ticket.model.event';
import {
  type BackendChangeShiftStatus,
  BackendTaskStatus,
  type TaskType,
} from '@manyun/ticket.model.ticket';
import {
  generateChangeOfflineLocation,
  generateChangeTicketDetail,
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateRiskRegisterDetailLocation,
  generateTicketUrl,
} from '@manyun/ticket.route.ticket-routes';
import type { BizType, Snapshot } from '@manyun/ticket.service.fetch-change-shift-snapshots';
import { BizType as MatterType } from '@manyun/ticket.service.fetch-change-shift-snapshots';
import { fetchChangeShiftUnclosedTicket } from '@manyun/ticket.service.fetch-change-shift-unclosed-ticket';
import { type Change, fetchChanges } from '@manyun/ticket.service.fetch-changes';
import { fetchEvents } from '@manyun/ticket.service.fetch-events';
import { fetchPagedRiskRegisters } from '@manyun/ticket.service.fetch-paged-risk-registers';
import { type BaseInfo, fetchTickets } from '@manyun/ticket.service.fetch-tickets';

import { useChangeShiftContext } from './change-shift-context';

const initLegacy = { list: [], pageNum: 1, pageSize: 10, total: 0 };
const initLegacyState = {
  [MatterType.Change]: initLegacy,
  [MatterType.Event]: initLegacy,
  [MatterType.Repair]: initLegacy,
  [MatterType.Maintenance]: initLegacy,
  [MatterType.Power]: initLegacy,
  [MatterType.RiskRegister]: initLegacy,
  [MatterType.RiskCheck]: initLegacy,
  [MatterType.RiskRegisterNew]: initLegacy,
};

export type ChangeShiftsUnclosedTicketTableProps = {
  taskStatus: BackendChangeShiftStatus | null;
  id: string | null;
  idcTag: string;
  blockTag: string;
};

export type ChangeShiftsUnclosedTicket = {
  id: number | string;
  title: string;
  bizType: string;
  handlerId: number | null;
  bizNo: string;
};

export type MatterTableProps = {
  dataSource: ChangeShiftsUnclosedTicket[];
  pagination:
    | {
        current: number;
        pageSize: number;
        total: number;
        onChange: (page: number, pageSize: number) => void;
      }
    | undefined
    | false;
  bizType: BizType;
};

export const mattersRadios: { key: BizType; text: string }[] = [
  { key: MatterType.Event, text: '事件' },
  { key: MatterType.Change, text: '变更' },
  { key: MatterType.RiskRegister, text: '风险' },
  { key: MatterType.Repair, text: '维修' },
  { key: MatterType.Power, text: '上下电' },
  { key: MatterType.Maintenance, text: '维护' },
  { key: MatterType.RiskRegisterNew, text: '风险登记册' },
  { key: MatterType.RiskCheck, text: '风险检查单' },
];

export function ChangeShiftsUnclosedTicketTable({
  taskStatus,
  id,
  idcTag,
  blockTag,
}: ChangeShiftsUnclosedTicketTableProps) {
  const [dutyMatter, setDutyMatter] =
    useState<
      Record<
        BizType,
        { list: ChangeShiftsUnclosedTicket[]; total: number; pageNum: number; pageSize: number }
      >
    >(initLegacyState);
  const [dutyMatterActiceKey, setDutyMatterActiceKey] = useState<BizType>(MatterType.Event);
  const { isExportPDF, exportPDFTabs, setExportPDFTabs } = useChangeShiftContext();
  const [configUtil] = useConfigUtil();
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureSpecialInfoSort = features.specialInfoSort;
  const [judgeEventBetaVersion] = useJudgeEventBetaVersion();
  const [getAllEvents] = useLazyAllEvents();

  const getChangeList = useCallback(
    async (pageNum: number, pageSize: number) => {
      const { data, error } = await fetchChanges({
        pageNum: pageNum,
        pageSize: pageSize,
        idcTag,
        blockTag,
        statusList: [
          // ChangeTicketState.Draft,
          ChangeTicketState.Approving,
          ChangeTicketState.WaitingChange,
          ChangeTicketState.Changing,
          ChangeTicketState.InSummary,
          ChangeTicketState.WaitingClose,
        ],
      });
      isExportPDF && setPdfTbs(MatterType.Change, !data?.total);
      if (error) {
        message.error(error.message);
        return;
      }
      const list: ChangeShiftsUnclosedTicket[] = data.data.map((item: Change) => {
        return {
          id: item.id,
          title: item.title,
          bizType: item.changeType,
          handlerId: item.creatorId,
          bizNo: item.changeOrderId,
        };
      });
      setDutyMatter(dutyMatter => ({
        ...dutyMatter,
        [MatterType.Change]: {
          list,
          total: data.total,
          pageNum,
          pageSize,
        },
      }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [idcTag, blockTag, isExportPDF]
  );

  const getEventList = useCallback(
    async (pageNum: number, pageSize: number) => {
      if (featureSpecialInfoSort) {
        const { data, error } = await fetchEvents({
          pageNum,
          pageSize,
          idcTags: [idcTag],
          blockTags: [blockTag],
          eventStatus: [
            EventSpecificProcessStatus.Debrief,
            EventSpecificProcessStatus.Emergency,
            EventSpecificProcessStatus.Finished,
            EventSpecificProcessStatus.Fix,
            EventSpecificProcessStatus.Init,
            EventSpecificProcessStatus.Recovery,
            EventSpecificProcessStatus.Review,
          ],
        });
        isExportPDF && setPdfTbs(MatterType.Event, !data?.total);

        if (error) {
          message.error(error.message);
          return;
        }
        const list: ChangeShiftsUnclosedTicket[] = data.data.map((item: Event) => {
          return {
            id: item.eventNo,
            title: item.eventDesc,
            bizType: MatterType.Event,
            handlerId: item.eventOwnerInfoList,
            bizStatus: item.eventStatus.code,
            // gmtCreate: item.gmtCreate,
            // gmtModified: item.gmtModified,
            bizNo: item.eventNo,
          };
        });
        setDutyMatter(dutyMatter => ({
          ...dutyMatter,
          [MatterType.Event]: {
            list,
            total: data.total,
            pageNum,
            pageSize,
          },
        }));
        return;
      }
      judgeEventBetaVersion({
        variables: { guid: idcTag },
        onCompleted: async ({ judgeEventBetaVersion }) => {
          getAllEvents({
            variables: {
              query: {
                pageNum,
                pageSize,
                idcTagList: [idcTag],
                blockGuidList: [blockTag],
                newEventStatusList: [
                  EventBetaProcessStatus.Confirming,
                  EventBetaProcessStatus.Relieving,
                  EventBetaProcessStatus.Reviewing,
                  EventBetaProcessStatus.Finishing,
                  EventBetaProcessStatus.Auditing,
                ],
                oldEventStatusList: [
                  BackendEventStatusCode.Created,
                  BackendEventStatusCode.Processing,
                  BackendEventStatusCode.Relieved,
                  BackendEventStatusCode.Resolved,
                  BackendEventStatusCode.Auditing,
                ],
              },
            },
            onCompleted(data) {
              if (data.allEvents.data) {
                isExportPDF && setPdfTbs(MatterType.Event, !!data.allEvents.total);
                const list: Snapshot[] = data.allEvents.data.map(item => {
                  return {
                    id: item.eventId,
                    title: judgeEventBetaVersion?.data ? item.eventTitle : item.eventDesc,
                    bizType: MatterType.Event,
                    handlerId: item.curHandlerId,
                    bizStatus: item.eventStatus,
                    gmtCreate: item.gmtCreate,
                    gmtModified: item.gmtModified,
                    bizNo: item.eventId,
                  };
                });
                setDutyMatter(dutyMatter => ({
                  ...dutyMatter,
                  EVENT: {
                    list,
                    total: data.allEvents.total,
                    pageNum,
                    pageSize,
                  },
                }));
              }
            },
          });
          // if (data.judgeEventBetaVersion?.data) {
          //   getEventsBeta({
          //     variables: {
          //       query: {
          //         pageNum,
          //         pageSize,
          //         idcTagList: [idcTag],
          //         blockGuidList: [blockTag],
          //         eventStatusList: [
          //           EventBetaProcessStatus.Confirming,
          //           EventBetaProcessStatus.Relieving,
          //           EventBetaProcessStatus.Reviewing,
          //           EventBetaProcessStatus.Finishing,
          //           EventBetaProcessStatus.Auditing,
          //         ],
          //       },
          //     },
          //     onCompleted(data) {
          //       if (data.eventsBeta.data) {
          //         isExportPDF && setPdfTbs(MatterType.Event, !!data.eventsBeta.total);
          //         const list: ChangeShiftsUnclosedTicket[] = data.eventsBeta.data.map(item => {
          //           return {
          //             id: item.eventId,
          //             title: item.eventTitle!,
          //             bizType: MatterType.Event,
          //             handlerId: item.ownerId,
          //             bizStatus: item.eventStatus,
          //             gmtCreate: item.gmtCreate,
          //             gmtModified: item.gmtModified,
          //             bizNo: item.eventId,
          //           };
          //         });
          //         setDutyMatter(dutyMatter => ({
          //           ...dutyMatter,
          //           EVENT: {
          //             list,
          //             total: data.eventsBeta.total,
          //             pageNum,
          //             pageSize,
          //           },
          //         }));
          //       }
          //     },
          //   });
          // } else {
          //   const { data, error } = await fetchEvents({
          //     pageNum,
          //     pageSize,
          //     idcTags: [idcTag],
          //     blockTags: [blockTag],
          //     eventStatus: [
          //       BackendEventStatusCode.Created,
          //       BackendEventStatusCode.Processing,
          //       BackendEventStatusCode.Relieved,
          //       BackendEventStatusCode.Resolved,
          //       BackendEventStatusCode.Auditing,
          //     ],
          //   });
          //   isExportPDF && setPdfTbs(MatterType.Event, !data?.total);

          //   if (error) {
          //     message.error(error.message);
          //     return;
          //   }
          //   const list: ChangeShiftsUnclosedTicket[] = data.data.map((item: Event) => {
          //     return {
          //       id: item.id,
          //       title: item.eventDesc,
          //       bizType: MatterType.Event,
          //       handlerId: item.eventOwnerId,
          //       bizStatus: item.eventStatus.code,
          //       // gmtCreate: item.gmtCreate,
          //       // gmtModified: item.gmtModified,
          //       bizNo: item.id.toString(),
          //     };
          //   });
          //   setDutyMatter(dutyMatter => ({
          //     ...dutyMatter,
          //     [MatterType.Event]: {
          //       list,
          //       total: data.total,
          //       pageNum,
          //       pageSize,
          //     },
          //   }));
          // }
        },
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [idcTag, blockTag, isExportPDF]
  );

  const getBaseTicketList = useCallback(
    async (pageNum: number, pageSize: number, type: TaskType) => {
      const { data, error } = await fetchTickets({
        pageNum,
        pageSize,
        taskType: type,
        idcTag,
        blockTag,
        taskStatusList: [
          BackendTaskStatus.PROCESSING,
          BackendTaskStatus.WAITTAKEOVER,
          BackendTaskStatus.INIT,
          BackendTaskStatus.CLOSE_APPROVER,
        ],
      });
      isExportPDF && setPdfTbs(type as BizType, !data?.total);

      if (error) {
        message.error(error.message);
        return;
      }
      const list: ChangeShiftsUnclosedTicket[] = data.data.map((item: BaseInfo) => {
        return {
          id: item.taskNo,
          title: item.taskTitle,
          bizType: type,
          handlerId: item.creatorId,
          bizNo: item.taskNo,
        };
      });

      setDutyMatter(_dutyMatter => ({
        ..._dutyMatter,
        [type]: {
          list,
          total: data.total,
          pageNum,
          pageSize,
        },
      }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [idcTag, blockTag, isExportPDF]
  );

  const dutyMatterList = useCallback(
    async (bizType: BizType) => {
      const { data, error } = await fetchChangeShiftUnclosedTicket({
        handoverId: id!,
        bizType,
      });
      isExportPDF && setPdfTbs(bizType, !data?.data?.length);
      if (error) {
        message.error(error.message);
        return;
      }
      setDutyMatter(dutyMatter => ({
        ...dutyMatter,
        [bizType]: { list: data.data, total: data.data.length },
      }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [id, isExportPDF]
  );

  const getRiskRegister = useCallback(
    async (pageNum: number, pageSize: number) => {
      const { error, data } = await fetchPagedRiskRegisters({
        blockGuids: [blockTag],
        pageNum,
        pageSize,
        riskStatus: ['APPROVING', 'HANDLING', 'WAITING_EVALUATE', 'WAITING_IDENTIFY'],
      });
      isExportPDF && setPdfTbs(MatterType.RiskRegisterNew, !data?.total);

      if (error) {
        message.error(error.message);
        return;
      }
      const list: ChangeShiftsUnclosedTicket[] = data.data.map(
        (item: RiskRegisterBaseInfoResponse) => {
          return {
            id: item.id,
            title: item.riskDesc ?? '',
            bizType: MatterType.RiskRegisterNew,
            handlerId: item.createUser,
            bizNo: item.id.toString(),
          };
        }
      );
      setDutyMatter(dutyMatter => ({
        ...dutyMatter,
        [MatterType.RiskRegisterNew]: {
          list,
          total: data.total,
          pageNum,
          pageSize,
        },
      }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [blockTag, isExportPDF]
  );

  useEffect(() => {
    if (idcTag && blockTag) {
      getDataSource(10);
    } else {
      setDutyMatter(initLegacyState);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [idcTag, blockTag, taskStatus]);

  useEffect(() => {
    if (isExportPDF) {
      getDataSource(10000);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isExportPDF]);

  const getDataSource = (pageSize: number) => {
    if (taskStatus === 'WAIT_HANDLE' || taskStatus === 'HANDED') {
      dutyMatterList(MatterType.Event);
      dutyMatterList(MatterType.Change);
      dutyMatterList(MatterType.Repair);
      dutyMatterList(MatterType.Maintenance);
      dutyMatterList(MatterType.Power);
      dutyMatterList(MatterType.RiskRegister);
      dutyMatterList(MatterType.RiskCheck);
      dutyMatterList(MatterType.RiskRegisterNew);
    } else {
      getChangeList(1, pageSize);
      getEventList(1, pageSize);
      getBaseTicketList(1, pageSize, MatterType.Repair);
      getBaseTicketList(1, pageSize, MatterType.Maintenance);
      getBaseTicketList(1, pageSize, MatterType.Power);
      getBaseTicketList(1, pageSize, MatterType.RiskRegister);
      getBaseTicketList(1, pageSize, MatterType.RiskCheck);
      getRiskRegister(1, pageSize);
    }
  };

  const setPdfTbs = (type: BizType, isVolid: boolean) => {
    if (isExportPDF) {
      const list = exportPDFTabs['legacy'].childrenList;
      const idx: number = list.findIndex(tab => tab.key === type);
      list[idx].isRenderd = true;
      list[idx].isVolid = isVolid;
      setExportPDFTabs({
        ...exportPDFTabs,
        legacy: {
          childrenList: list,
          isVolid: list.every(item => item.isRenderd && !item.isVolid),
        },
      });
    }
  };

  const onChangeRadio = (type: BizType, pageNum: number, pageSize: number) => {
    switch (type) {
      case MatterType.Change:
        getChangeList(pageNum, pageSize);
        break;
      case MatterType.Event:
        getEventList(pageNum, pageSize);
        break;
      case MatterType.RiskRegisterNew:
        getRiskRegister(pageNum, pageSize);
        break;
      default:
        getBaseTicketList(pageNum, pageSize, type as TaskType);
    }
  };

  return (
    <Space direction="vertical" size="middle">
      {!isExportPDF && (
        <Radio.Group
          defaultValue={MatterType.Event}
          value={dutyMatterActiceKey}
          onChange={e => {
            setDutyMatterActiceKey(e.target.value);
            if (taskStatus === 'WAIT_HANDLE' || taskStatus === 'HANDED') {
              dutyMatterList(e.target.value);
              return;
            }
            if (idcTag && blockTag) {
              onChangeRadio(e.target.value, 1, 10);
            }
          }}
        >
          {mattersRadios.map(item => (
            <Radio.Button key={item.key} value={item.key}>{`${item.text} ${
              dutyMatter[item.key].total || 0
            }`}</Radio.Button>
          ))}
        </Radio.Group>
      )}
      {!isExportPDF && (
        <MatterTable
          bizType={dutyMatterActiceKey}
          dataSource={dutyMatter[dutyMatterActiceKey].list}
          pagination={
            taskStatus !== 'WAIT_HANDLE' && taskStatus !== 'HANDED'
              ? {
                  current: dutyMatter[dutyMatterActiceKey].pageNum!,
                  pageSize: dutyMatter[dutyMatterActiceKey].pageSize!,
                  total: dutyMatter[dutyMatterActiceKey].total,
                  onChange: (current, pageSize) => {
                    onChangeRadio(dutyMatterActiceKey, current, pageSize);
                  },
                }
              : undefined
          }
        />
      )}
      {isExportPDF && (
        <>
          {mattersRadios.map(item =>
            dutyMatter[item.key].total ? (
              <Space key={item.key} style={{ width: '100%' }} direction="vertical">
                <Typography.Text>{item.text}</Typography.Text>
                <MatterTable
                  bizType={item.key}
                  dataSource={dutyMatter[item.key].list}
                  pagination={false}
                />
              </Space>
            ) : null
          )}
        </>
      )}
    </Space>
  );
}

export function MatterTable({ dataSource, pagination, bizType }: MatterTableProps) {
  return (
    <Table
      rowKey="id"
      columns={dutyMatterolumns(bizType)}
      dataSource={dataSource}
      pagination={pagination}
    />
  );
}

const dutyMatterolumns = (bizType: BizType) => [
  {
    title: '单号',
    dataIndex: 'bizNo',
    render: (text: string) => {
      let path = generateTicketUrl({
        id: text,
        ticketType: bizType.toLowerCase(),
      });
      if (bizType === 'CHANGE') {
        if (text.startsWith('N')) {
          path = generateChangeOfflineLocation({
            id: text,
          });
        } else {
          path = generateChangeTicketDetail({ id: text });
        }
      }
      if (bizType === 'EVENT') {
        if (text.startsWith('N')) {
          path = generateEvnetLocation({
            id: text,
          });
        } else {
          path = generateEventDetailRoutePath({ id: text });
        }
      }
      if (bizType === 'RISK_REGISTER_NEW') {
        path = generateRiskRegisterDetailLocation({ id: encodeURIComponent(text) });
      }
      return <Link to={path}>{text}</Link>;
    },
  },
  {
    title: '描述',
    width: '70%',
    dataIndex: 'title',
    ellipsis: true,
  },
];
