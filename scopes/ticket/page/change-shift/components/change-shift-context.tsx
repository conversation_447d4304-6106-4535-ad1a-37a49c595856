import { createContext, useContext } from 'react';

export type ExportPDFTabs = Record<
  'dutyMatter' | 'legacy',
  {
    childrenList: { key: string; isVolid: boolean; isRenderd: boolean }[];
    isVolid: boolean;
  }
>;

export type ChangeShiftContextValue = {
  isExportPDF: boolean;
  exportPDFTabs: ExportPDFTabs;
  setIsExportPDF: (mode: boolean) => void;
  setExportPDFTabs: (tabs: ExportPDFTabs) => void;
};

export const initTabs = {
  dutyMatter: {
    isVolid: false,
    childrenList: [
      { key: 'EVENT', isVolid: false, isRenderd: false },
      { key: 'CHANGE', isVolid: false, isRenderd: false },
      { key: 'RISK_REGISTER', isVolid: false, isRenderd: false },
      { key: 'REPAIR', isVolid: false, isRenderd: false },
      { key: 'POWER', isVolid: false, isRenderd: false },
      { key: 'MAINTENANCE', isVolid: false, isRenderd: false },
      { key: 'RISK_REGISTER_NEW', isVolid: false, isRenderd: false },
      { key: 'RISK_CHECK', isVolid: false, isRenderd: false },
    ],
  },
  legacy: {
    isVolid: false,
    childrenList: [
      { key: 'EVENT', isVolid: false, isRenderd: false },
      { key: 'CHANGE', isVolid: false, isRenderd: false },
      { key: 'RISK_REGISTER', isVolid: false, isRenderd: false },
      { key: 'REPAIR', isVolid: false, isRenderd: false },
      { key: 'POWER', isVolid: false, isRenderd: false },
      { key: 'MAINTENANCE', isVolid: false, isRenderd: false },
      { key: 'RISK_REGISTER_NEW', isVolid: false, isRenderd: false },
      { key: 'RISK_CHECK', isVolid: false, isRenderd: false },
    ],
  },
};
export const ChangeShiftContext = createContext<ChangeShiftContextValue>({
  isExportPDF: false,
  exportPDFTabs: initTabs,
  setIsExportPDF: () => {},
  setExportPDFTabs: () => {},
});

export function useChangeShiftContext() {
  return useContext(ChangeShiftContext);
}
