import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { DefaultOptionType, RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchChanges } from '@manyun/ticket.service.fetch-changes';

export type ChangeIdSelectProps = SelectProps;

export const ChangeIdSelect = React.forwardRef(
  ({ ...selectProps }: ChangeIdSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const [options, setOptions] = useState<DefaultOptionType[]>([]);
    useEffect(() => {
      (async function () {
        const { data, error } = await fetchChanges({ pageNum: 1, pageSize: 1000 });
        if (error) {
          message.error(error.message);
          setOptions([]);
          return;
        }
        setOptions(
          data.data?.map(changeTicket => ({
            label: changeTicket.changeOrderId,
            value: changeTicket.changeOrderId,
          }))
        );
      })();
    }, []);

    return <Select ref={ref} {...selectProps} options={options} />;
  }
);
