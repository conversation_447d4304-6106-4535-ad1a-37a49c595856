import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { submitChangeShift } from '@manyun/ticket.service.submit-change-shift';

export type CountersignProps = {
  id: string;
  blockGuid: string;
  onSuccess: () => void;
} & ButtonProps;

export function Countersign({ id, blockGuid, onSuccess, ...resp }: CountersignProps) {
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [forcedChangeShift, setForcedChangeShift] = useState<boolean>(false);

  const [forcedChangeShiftForm] = Form.useForm();

  return (
    <Space direction="vertical" size="middle">
      <Button loading={submitting} {...resp} onClick={() => setForcedChangeShift(true)}>
        加签
      </Button>
      <Modal
        open={forcedChangeShift}
        width={560}
        title="加签"
        okText="提交"
        onOk={() => {
          forcedChangeShiftForm.validateFields().then(async values => {
            setSubmitting(true);
            const { error } = await submitChangeShift({
              bizNo: id,
              countSignerIds: values.countSignerIds,
            });
            setSubmitting(false);
            if (error) {
              message.error(error.message);
              return;
            }
            setForcedChangeShift(false);
            onSuccess();
          });
        }}
        onCancel={() => {
          setForcedChangeShift(false);
          setSubmitting(false);
        }}
      >
        <Form form={forcedChangeShiftForm}>
          <Space direction="vertical" style={{ width: '100%' }} size="large">
            <Alert
              description="选择加签人后，需这些人员全部确认加签后，方可交班。"
              type="warning"
            />

            <Form.Item
              label="选择加签人"
              name="countSignerIds"
              rules={[
                { required: true, message: '请选择加签人' },
                () => ({
                  validator(_, value) {
                    if (value && value.length > 5) {
                      return Promise.reject(new Error('最多选择五个加签人'));
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <UserSelect labelInValue={false} mode="multiple" blockGuid={blockGuid} />
            </Form.Item>
          </Space>
        </Form>
      </Modal>
    </Space>
  );
}
