import { CheckCircleOutlined, CloseCircleOutlined, PaperClipOutlined } from '@ant-design/icons';
import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetadataJSON } from '@manyun/resource-hub.model.metadata';
import { BackendChangeShiftStatus, ChangeShiftStatusMap } from '@manyun/ticket.model.ticket';
import { CancelBtn } from '@manyun/ticket.page.change-shifts';
import {
  type ChangeShiftRouteParams,
  generateChangeShiftEdit,
} from '@manyun/ticket.route.ticket-routes';
import type { ChangeShiftJSON, HandoverEvents } from '@manyun/ticket.service.fetch-change-shift';
import { fetchChangeShift } from '@manyun/ticket.service.fetch-change-shift';
import { fetchChangeShiftLastSchedule } from '@manyun/ticket.service.fetch-change-shift-last-schedule';
import { fetchChangeShiftTakeOverGroupUser } from '@manyun/ticket.service.fetch-change-shift-take-over-group-user';
import { takeOverChangeShift } from '@manyun/ticket.service.take-over-change-shift';
import { ChangeShiftMatterTable } from '@manyun/ticket.ui.change-shift-matter-table';

import styles from './change-shift.module.less';
import type { ExportPDFTabs } from './components/change-shift-context';
import { ChangeShiftContext, initTabs } from './components/change-shift-context';
import { ChangeShiftsUnclosedTicketTable } from './components/change-shift-legacy-table';
import { Countersign } from './components/countersign-btn';
import { CountersignDescription } from './components/countersign-description-btn';
import { Handover } from './components/handover-btn';
import { Step } from './components/step';

export function ChangeShift() {
  const { id } = useParams<ChangeShiftRouteParams>();

  const [changeShift, setChangeShift] = useState<ChangeShiftJSON | null>(null);
  const [loading, setLoading] = useState(false);
  const [dutyGroupUsers, setDutyGroupUsers] = useState<
    Record<number, { id: number; userName: string }[]>
  >({});
  const [tabActiveKey, setTabActiveKey] = useState<'matter' | 'dutyMatter' | 'legacy'>('matter');
  const [takeLoading, setTakeLoading] = useState<boolean>(false);
  const [lastSchedule, setLastSchedule] = useState<number | null>();
  const [, { checkUserId, checkCode }] = useAuthorized();
  const [isExportPDF, setIsExportPDF] = useState(false);
  const [exportPDFTabs, setExportPDFTabs] = useState<ExportPDFTabs>(initTabs);
  const showUpload = checkCode('element_ticket_change-shift_upload_data_model');
  const showDown = checkCode('element_ticket_change-shift_fetch_data_model');
  let exportPdfInterval: NodeJS.Timer | null = null;
  const config = useSelector(selectCurrentConfig);
  const isApplyUser = changeShift ? checkUserId(changeShift?.applyUserId) : false;
  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const showCountersign = ticketScopeCommonConfigs.changeShift.features.showCountersign;
  const [{ data: matters }, { readMetaData }] = useMetaData(MetaType.HANDOVER_EVENT_TYPE);

  useEffect(() => {
    getDetail();
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getDetail = React.useCallback(async () => {
    if (loading) {
      return;
    }
    setLoading(true);
    const { data, error } = await fetchChangeShift({
      bizNo: id!,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setChangeShift(data);
  }, [id, loading]);

  const dutyGroupUser = async (visible: boolean, dutyGroupId: number) => {
    if (visible) {
      const { data, error } = await fetchChangeShiftTakeOverGroupUser({
        bizNo: id!,
        dutyGroupId,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setDutyGroupUsers({ [dutyGroupId]: data.data });
    }
  };

  const onChangeTab = (value: 'matter' | 'dutyMatter' | 'legacy') => {
    if (value === 'dutyMatter') {
      getLastChangeShift();
    } else {
      setTabActiveKey(value);
    }
  };

  const takeOver = async (bizNo: string) => {
    setTakeLoading(true);
    const { error } = await takeOverChangeShift({ bizNo });
    setTakeLoading(false);
    if (!error) {
      message.success('接班成功');
      getDetail();
    } else {
      message.error(error.message);
    }
  };

  const getLastChangeShift = async () => {
    if (loading || !changeShift) {
      return;
    }
    const { data, error } = await fetchChangeShiftLastSchedule({
      scheduleId: changeShift.applyScheduleId,
    });
    setTabActiveKey('dutyMatter');
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      setLastSchedule(data.submitTime);
    }
  };

  if (loading || changeShift === null) {
    return null;
  }

  const getTime = () => {
    // 1、已取消的再去查看详情的时候，时间取上个交班时间到新单子的已保存时间，即【单子的创建时间】；2、如果没有上个班的交班时间，取班次的开始时间.3.未提交的我记得好像之前说取的是截止查看时刻的
    let startTime = lastSchedule;
    let endTime: number | null = null;
    if (!startTime) {
      startTime = changeShift.scheduleStartTime;
    }
    // if (changeShift.bizStatus === BackendChangeShiftStatus.INIT) {
    //   // time = [startTime, new Date().getTime()];
    // }
    if (changeShift.bizStatus === BackendChangeShiftStatus.CANCELED) {
      endTime = changeShift.gmtCreate;
      // time = [startTime, changeShift.gmtCreate];
    }
    if (
      (changeShift.bizStatus === BackendChangeShiftStatus.HANDED ||
        changeShift.bizStatus === BackendChangeShiftStatus.WAIT_HANDLE) &&
      changeShift.submitTime
    ) {
      endTime = changeShift.submitTime;
      // time = [startTime, changeShift.submitTime];
    }
    return { startTime, endTime };
  };

  const getOperabilityCountersignDescriptionUser = () => {
    return changeShift.handoverCounterSigners?.some(
      item => !item.finished && checkUserId(item.staffId)
    );
  };

  const handoverView = () => (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      <Space direction="vertical" size="small" style={{ width: '100%' }}>
        {(showUpload || showDown) && (
          <Space>
            <Typography.Title style={{ marginBottom: 0 }} level={5}>
              运维交接数据：
            </Typography.Title>
            {changeShift.fileList?.[0]?.name ? (
              <SimpleFileList files={changeShift.fileList}>
                <Button type="link" style={{ height: 'auto', padding: 0 }}>
                  {changeShift.fileList[0].name}
                </Button>
              </SimpleFileList>
            ) : (
              '--'
            )}
          </Space>
        )}
        <Typography.Title level={5}>长期交接班事项</Typography.Title>
        <div
          style={{
            maxHeight: 408,
            minHeight: 184,
            width: '100%',
            border: '1px solid var(--border-color-base)',
            overflow: 'auto',
            padding: 24,
          }}
          dangerouslySetInnerHTML={{
            __html: changeShift.handoverInfo ? changeShift.handoverInfo : '',
          }}
        />
      </Space>
      <Table
        rowKey="eventName"
        columns={columns(matters)}
        dataSource={changeShift.handoverEvents}
      />
    </Space>
  );
  const dutyUsers =
    showCountersign && changeShift?.groupUsers?.length
      ? changeShift.groupUsers?.some(item => checkUserId(item))
      : true;
  return (
    <ChangeShiftContext.Provider
      value={{
        isExportPDF: isExportPDF,
        exportPDFTabs: exportPDFTabs,
        setIsExportPDF: setIsExportPDF,
        setExportPDFTabs: setExportPDFTabs,
      }}
    >
      <Spin spinning={isExportPDF}>
        <div id="page_change_shift_detail">
          <DownloadPdfButton
            key="download"
            compact={false}
            style={{ position: 'fixed', right: 8, top: 52 }}
            disabled={isExportPDF}
            pdfName="交接班"
            exportElement={document.getElementById('page_change_shift_detail')}
            beforeDownload={() => {
              setIsExportPDF(true);
              return new Promise(resolve => {
                exportPdfInterval = setInterval(() => {
                  const tabs = [
                    ...exportPDFTabs.dutyMatter.childrenList,
                    ...exportPDFTabs.legacy.childrenList,
                  ];
                  if (tabs.some(item => !item.isRenderd)) {
                    return;
                  }
                  setTimeout(() => {
                    resolve();
                  }, 2000);
                }, 1000);
              });
            }}
            onFinish={() => {
              setIsExportPDF(false);
              setExportPDFTabs(initTabs);
              exportPdfInterval && clearInterval(exportPdfInterval);
            }}
          />
          <Space direction="vertical" style={{ display: 'flex' }} size="large">
            <Space direction="vertical" style={{ display: 'flex' }} size="middle">
              <Step info={changeShift} />
              <Card key="info" title="工单信息">
                <Descriptions column={4}>
                  <Descriptions.Item label="工单标题">
                    <Space direction="horizontal">
                      {changeShift.taskTitle}
                      {changeShift.submitContent ? (
                        <Popover
                          placement="topRight"
                          title="强制交班"
                          style={{ maxWidth: 356, maxHeight: 560 }}
                          content={
                            <Space direction="vertical">
                              <Space direction="vertical">
                                <Typography.Text>接班人员上班打卡情况</Typography.Text>
                                <Space split={<Divider type="vertical" spaceSize="mini" />} wrap>
                                  {changeShift.submitContent?.checkResults?.map(item => (
                                    <>
                                      <User showAvatar={false} id={item.staffId} />
                                      {item.onDutyTime ? (
                                        <Typography.Text type="secondary">
                                          {moment(item.onDutyTime).format('HH:mm')}
                                        </Typography.Text>
                                      ) : (
                                        <Typography.Text type="danger">未打卡</Typography.Text>
                                      )}
                                    </>
                                  ))}
                                </Space>
                              </Space>
                              <Space direction="vertical">
                                <Typography.Text>强制交班原因</Typography.Text>
                                <Typography.Text type="secondary">
                                  {changeShift.submitContent.submitReason}
                                </Typography.Text>
                              </Space>
                            </Space>
                          }
                        >
                          <ExclamationCircleOutlined className={styles.icon} />
                        </Popover>
                      ) : null}
                    </Space>
                  </Descriptions.Item>
                  <Descriptions.Item label="所属机房/楼栋">
                    {changeShift.blockGuid}
                  </Descriptions.Item>
                  <Descriptions.Item label="值班时间">
                    {moment(changeShift.scheduleStartTime).format('YYYY-MM-DD HH:mm')} -{' '}
                    {moment(changeShift.scheduleEndTime).format('HH:mm')}
                  </Descriptions.Item>
                  <Descriptions.Item label="工单状态">
                    {ChangeShiftStatusMap[changeShift.bizStatus]}
                  </Descriptions.Item>
                  <Descriptions.Item label="交班班组" contentStyle={{ alignItems: 'center' }}>
                    {changeShift.applyDutyGroupName}
                    <Explanation
                      iconType="exclamation"
                      tooltip={{
                        title:
                          changeShift.applyDutyGroupId &&
                          dutyGroupUsers &&
                          dutyGroupUsers[changeShift.applyDutyGroupId]
                            ? dutyGroupUsers[changeShift.applyDutyGroupId]?.map(item => (
                                <span key={item.id}>{item.userName} </span>
                              ))
                            : ' ',
                        onVisibleChange: visible =>
                          dutyGroupUser(visible, changeShift.applyDutyGroupId),
                      }}
                      style={{ marginLeft: 9, padding: 0 }}
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="交班班次">
                    {changeShift.applyDutyName}
                  </Descriptions.Item>

                  <Descriptions.Item label="交班负责人">
                    <User.Link id={changeShift.applyUserId} />
                  </Descriptions.Item>
                  <Descriptions.Item label="交班时间">
                    {changeShift.submitTime
                      ? moment(changeShift.submitTime).format('YYYY-MM-DD HH:mm')
                      : '--'}
                  </Descriptions.Item>
                  <Descriptions.Item label="接班班组" contentStyle={{ alignItems: 'center' }}>
                    {changeShift.handDutyGroupName}
                    <Explanation
                      iconType="exclamation"
                      tooltip={{
                        title:
                          changeShift.handDutyGroupId &&
                          dutyGroupUsers &&
                          dutyGroupUsers[changeShift.handDutyGroupId]
                            ? dutyGroupUsers[changeShift.handDutyGroupId]?.map(item => (
                                <span key={item.id}>{item.userName} </span>
                              ))
                            : ' ',
                        onVisibleChange: visible =>
                          dutyGroupUser(visible, changeShift.handDutyGroupId),
                      }}
                      style={{ marginLeft: 9, padding: 0 }}
                    />
                  </Descriptions.Item>
                  <Descriptions.Item label="接班班次">{changeShift.handDutyName}</Descriptions.Item>
                  <Descriptions.Item label="接班负责人">
                    <User.Link id={changeShift.handUserId} />
                  </Descriptions.Item>
                  <Descriptions.Item label="接班时间">
                    {changeShift.handTime
                      ? moment(changeShift.handTime).format('YYYY-MM-DD HH:mm')
                      : '--'}
                  </Descriptions.Item>
                  <Descriptions.Item
                    span={1}
                    label="交班通知抄送"
                    contentStyle={{ overflow: 'hidden' }}
                  >
                    {changeShift.ccStaffIds?.length ? (
                      <Popover
                        content={
                          <Space split={<Divider type="vertical" spaceSize="mini" />}>
                            {changeShift.ccStaffIds.map(item => (
                              <User key={item} showAvatar={false} id={item} />
                            ))}
                          </Space>
                        }
                      >
                        <Typography.Text ellipsis>
                          {changeShift.ccStaffIds.map((item, index) => (
                            <>
                              <User key={item} showAvatar={false} id={item} />
                              {index !== changeShift.ccStaffIds!.length - 1 && (
                                <Divider type="vertical" />
                              )}
                            </>
                          ))}
                        </Typography.Text>
                      </Popover>
                    ) : (
                      '--'
                    )}
                  </Descriptions.Item>
                </Descriptions>
              </Card>
              {changeShift.handoverCounterSigners?.length ? (
                <Card
                  bordered={false}
                  headStyle={{ borderBottom: 0 }}
                  // bodyStyle={{ padding: '0 24px' }}
                  title={
                    <Typography.Title showBadge level={5}>
                      加签说明
                    </Typography.Title>
                  }
                >
                  <Space direction="vertical" size={24} style={{ width: '100%' }}>
                    {changeShift.handoverCounterSigners.map(item => (
                      <div
                        key={item.staffId}
                        style={{
                          display: 'flex',
                          justifyContent: 'space-between',
                        }}
                      >
                        <Space style={{ flex: 1 }} size={8}>
                          <User showName={false} id={item.staffId} />
                          <Typography.Text
                            style={{ width: 64, marginRight: 8 }}
                            ellipsis={{
                              tooltip: true,
                            }}
                          >
                            <User id={item.staffId} showAvatar={false} />
                          </Typography.Text>
                          <Typography.Text>{item.content || '--'}</Typography.Text>
                        </Space>
                        <Space style={{ alignItems: 'flex-start' }}>
                          {item.files?.length && (
                            <SimpleFileList
                              files={item.files.map(file => McUploadFile.fromApiObject(file))}
                            >
                              <Space>
                                <PaperClipOutlined />
                                <Typography.Link>查看附件</Typography.Link>
                              </Space>
                            </SimpleFileList>
                          )}
                          {item.finished && item.gmtModified && (
                            <Typography.Text>
                              {moment(item.gmtModified).format('YYYY-MM-DD HH:mm')}
                            </Typography.Text>
                          )}
                        </Space>
                      </div>
                    ))}
                  </Space>
                </Card>
              ) : null}
              <Card key="matter" style={{ marginBottom: 16 }}>
                {isExportPDF ? (
                  <Space direction="vertical">
                    <Typography.Text>交接事项</Typography.Text>
                    {handoverView()}
                    {exportPDFTabs['dutyMatter'].childrenList.every(
                      item => item.isRenderd && item.isVolid
                    )
                      ? null
                      : [
                          <Typography.Text key="matter_title">值班期事项</Typography.Text>,
                          <ChangeShiftMatterTable
                            key="matter_table"
                            taskStatus={changeShift.bizStatus}
                            id={id}
                            scheduleTime={{ ...getTime() }}
                            idcTag={changeShift.blockGuid.substring(
                              0,
                              changeShift.blockGuid.indexOf('.')
                            )}
                            blockTag={changeShift.blockGuid}
                          />,
                        ]}
                    {exportPDFTabs['legacy'].childrenList.every(
                      item => item.isRenderd && item.isVolid
                    )
                      ? null
                      : [
                          <Typography.Text key="legacy_title">遗留事项</Typography.Text>,
                          <ChangeShiftsUnclosedTicketTable
                            key="legacy_table"
                            taskStatus={changeShift.bizStatus}
                            id={id}
                            idcTag={changeShift.blockGuid.substring(
                              0,
                              changeShift.blockGuid.indexOf('.')
                            )}
                            blockTag={changeShift.blockGuid}
                          />,
                        ]}
                  </Space>
                ) : (
                  <Tabs
                    defaultActiveKey="matter"
                    activeKey={tabActiveKey}
                    onChange={value => {
                      onChangeTab(value as 'matter' | 'dutyMatter' | 'legacy');
                    }}
                  >
                    <Tabs.TabPane key="matter" tab="交接事项">
                      {handoverView()}
                    </Tabs.TabPane>
                    <Tabs.TabPane key="dutyMatter" tab="值班期事项">
                      {tabActiveKey === 'dutyMatter' && (
                        <ChangeShiftMatterTable
                          taskStatus={changeShift.bizStatus}
                          id={id}
                          scheduleTime={{ ...getTime() }}
                          idcTag={changeShift.blockGuid.substring(
                            0,
                            changeShift.blockGuid.indexOf('.')
                          )}
                          blockTag={changeShift.blockGuid}
                        />
                      )}
                    </Tabs.TabPane>
                    <Tabs.TabPane key="legacy" tab="遗留事项">
                      {tabActiveKey === 'legacy' && (
                        <ChangeShiftsUnclosedTicketTable
                          taskStatus={changeShift.bizStatus}
                          id={id}
                          idcTag={changeShift.blockGuid.substring(
                            0,
                            changeShift.blockGuid.indexOf('.')
                          )}
                          blockTag={changeShift.blockGuid}
                        />
                      )}
                    </Tabs.TabPane>
                  </Tabs>
                )}
              </Card>
            </Space>
            {!isExportPDF && (
              <FooterToolBar>
                <Space style={{ width: '100%', justifyContent: 'center' }}>
                  {changeShift.bizStatus === 'INIT' && (
                    <Handover
                      id={changeShift.bizNo}
                      time={changeShift?.scheduleEndTime}
                      staffId={changeShift.handUserId}
                      groupScheduleId={changeShift.handScheduleId}
                      onSuccess={() => {
                        getDetail();
                      }}
                    />
                  )}
                  {changeShift.bizStatus === 'COUNTERSIGN' &&
                    showCountersign &&
                    getOperabilityCountersignDescriptionUser() && (
                      <CountersignDescription
                        id={changeShift.bizNo}
                        blockGuid={changeShift.blockGuid}
                        onSuccess={() => {
                          getDetail();
                        }}
                      />
                    )}
                  {(changeShift.bizStatus === 'INIT' || changeShift.bizStatus === 'COUNTERSIGN') &&
                    (isApplyUser || getOperabilityCountersignDescriptionUser() || dutyUsers) && (
                      <Button>
                        <Link key="edit" to={generateChangeShiftEdit({ id: changeShift.bizNo })}>
                          编辑
                        </Link>
                      </Button>
                    )}
                  {changeShift.bizStatus === 'INIT' && showCountersign && isApplyUser && (
                    <Countersign
                      id={changeShift.bizNo}
                      blockGuid={changeShift.blockGuid}
                      onSuccess={() => {
                        getDetail();
                      }}
                    />
                  )}
                  {changeShift.bizStatus === 'WAIT_HANDLE' &&
                    checkUserId(changeShift.handUserId) && (
                      <Button
                        type="primary"
                        disabled={takeLoading}
                        onClick={() => {
                          takeOver(changeShift.bizNo);
                        }}
                      >
                        接班
                      </Button>
                    )}
                  {(changeShift.bizStatus === 'INIT' || changeShift.bizStatus === 'COUNTERSIGN') &&
                    (isApplyUser || getOperabilityCountersignDescriptionUser() || dutyUsers) && (
                      <CancelBtn
                        type="default"
                        bizNo={changeShift.bizNo}
                        onSuccess={() => {
                          getDetail();
                        }}
                      />
                    )}
                </Space>
              </FooterToolBar>
            )}
          </Space>
        </div>
      </Spin>
    </ChangeShiftContext.Provider>
  );
}

const columns = (matters: {
  total: number;
  codes: string[];
  loading: boolean;
  entities: Record<string, MetadataJSON>;
  keyMapper: Record<number, string>;
}) => [
  {
    title: '事项',
    dataIndex: 'eventCode',
    render: (text: string, record: HandoverEvents) =>
      matters.entities[text] ? matters.entities[text].name : record.eventName,
  },
  {
    title: '状态',
    dataIndex: 'value',
    render: (text: string) => {
      if (text === '正常') {
        return (
          <Tag icon={<CheckCircleOutlined />} color="success">
            {text}
          </Tag>
        );
      }
      if (text === '异常') {
        return (
          <Tag icon={<CloseCircleOutlined />} color="error">
            {text}
          </Tag>
        );
      }
      return text;
    },
  },
  {
    title: '说明',
    dataIndex: 'remarks',
    ellipsis: true,
  },
];
