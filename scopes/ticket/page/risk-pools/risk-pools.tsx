import dayjs from 'dayjs';
import omit from 'lodash.omit';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import {
  useDeleteRiskPool,
  useLazyRiskPools,
  useUpdateRiskPoolStatus,
} from '@manyun/ticket.gql.client.risk-register';
import type { RiskPool, RiskPoolsQ } from '@manyun/ticket.gql.client.risk-register';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { RISK_POOL_NEW_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';
import { exportRiskPool } from '@manyun/ticket.service.export-risk-pool';
import { RiskPoolFilterForm } from '@manyun/ticket.ui.risk-pool-filter-form';
import type { FormValues } from '@manyun/ticket.ui.risk-pool-filter-form';
import { RiskPoolTable, basicColumns } from '@manyun/ticket.ui.risk-pool-table';

import { EditFormDrawer } from './components/edit-drawer';

export function RiskPools() {
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const [authorized] = useAuthorized({ checkByCode: 'element_risk-pool-operate' });

  const [tableColumns, setTableColumns] = useState<ColumnType<RiskPool>[]>([]);
  const [exportLoading, setExportLoading] = useState(false);

  const [fields, setFields] = useState<FormValues>();
  const [sortFields, setSortFields] = useState<{
    orderFiled?: string;
    orderInfo?: 'ASC' | 'DESC';
    defineOrder?: boolean;
  }>();
  const history = useHistory();

  const [fetch, { loading, data, refetch }] = useLazyRiskPools();

  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  // @ts-ignore type error
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';
  const text = isFull ? '风险专业' : '风险类别';

  const [deleteRiskPool, { loading: deleteLoading }] = useDeleteRiskPool({
    onCompleted(data) {
      if (!data.deleteRiskPool.success) {
        message.error(data.deleteRiskPool?.message);
        return;
      }
      refetch({
        query: { ...fields, ...sortFields, isFlatten: true },
      });
      message.success('删除成功');
    },
  });

  const [updateRiskPoolStatus, { loading: updateLoading }] = useUpdateRiskPoolStatus({
    onCompleted(data) {
      if (!data.updateRiskPoolStatus.success) {
        message.error(data.updateRiskPoolStatus?.message);
        return;
      }
      refetch({
        query: { ...fields, ...sortFields, isFlatten: true },
      });
    },
  });

  const getParams = useCallback(() => {
    const params: RiskPoolsQ = {
      ...omit(fields, 'createTime', 'updateTime'),
      ...sortFields,
    };
    if (fields && fields.createTime) {
      params.startDate = fields.createTime[0].startOf('day').format('x');
      params.endDate = fields.createTime[1].endOf('day').format('x');
    }
    if (fields && fields.updateTime) {
      params.startDateOfUpdate = fields.updateTime[0].startOf('day').format('x');
      params.endDateOfUpdate = fields.updateTime[1].endOf('day').format('x');
    }
    return params;
  }, [fields, sortFields]);

  const _fetchData = useCallback(async () => {
    const params = getParams();
    refetch({
      query: { ...params, isFlatten: true },
    });
  }, [refetch, getParams]);

  useDeepCompareEffect(() => {
    _fetchData();
  }, [_fetchData]);

  useEffect(() => {
    fetch({
      variables: {
        query: { isFlatten: true },
      },
    });
  }, [fetch]);

  const handleFileExport = async (type: string) => {
    const params = getParams();
    setExportLoading(true);
    const { error, data } = await exportRiskPool(type === 'filtered' ? params : {});
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  };

  const operationColumn: ColumnType<RiskPool> = {
    title: '操作',
    dataIndex: 'operation',
    width: 100,
    fixed: 'right',
    disabled: true,
    render: (_, record) =>
      authorized ? (
        <Space>
          <EditFormDrawer
            values={{
              ...record,
              verifyList: record.riskPointVerifyList.map(item => item.verify ?? ''),
            }}
            id={record.riskPointId}
            onSuccess={() =>
              refetch({
                query: { ...fields, ...sortFields, isFlatten: true },
              })
            }
          />
          {record.isEnable === '0' && (
            <Popconfirm
              title="确认删除此风险点么？删除后将无法恢复，请谨慎操作。"
              style={{ width: 290 }}
              okText="确认删除"
              cancelText="我再想想"
              placement="bottomRight"
              okButtonProps={{ disabled: deleteLoading }}
              onConfirm={() => deleteRiskPool({ variables: { id: record.riskPointId } })}
            >
              <Button compact type="link" disabled={loading}>
                删除
              </Button>
            </Popconfirm>
          )}
        </Space>
      ) : (
        '--'
      ),
    onCell: record => {
      return {
        rowSpan: record.mergeRowsNum,
      };
    },
  };

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space style={{ justifyContent: 'space-between', width: '100%' }}>
              <Space size="middle">
                {authorized && (
                  <Button type="primary" onClick={() => history.push(RISK_POOL_NEW_ROUTE_PATH)}>
                    新建风险点
                  </Button>
                )}

                <RiskPoolFilterForm values={fields} onSearch={setFields} />
              </Space>
              <Space size="middle">
                <FileExport
                  filename={`风险库${dayjs(new Date()).format('YYYY-MM-DD')}.xls`}
                  disabled={exportLoading}
                  data={type => {
                    return handleFileExport(type);
                  }}
                  showExportFiltered
                />
                <EditColumns
                  uniqKey="INDEPENDENT_TICKETS_RISK_POOLS"
                  listsHeight={360}
                  defaultValue={[
                    ...basicColumns(locales, false, false, undefined, text),
                    operationColumn,
                  ]}
                  onChange={columns => {
                    setTableColumns(columns);
                  }}
                />
              </Space>
            </Space>
            <RiskPoolTable
              dataSource={data?.riskPools.data ?? []}
              mergedColumn
              updateEnabledStatus={(status, id) => {
                updateRiskPoolStatus({
                  variables: {
                    query: {
                      id,
                      isEnable: status === '1' ? '0' : '1',
                    },
                  },
                });
              }}
              showColumns={tableColumns
                .filter(item => item.show !== false && item.dataIndex)
                .map(item => item.dataIndex as string)}
              operationColumn={operationColumn}
              loading={loading || updateLoading}
              onChange={(_, __, sorter, { action }) => {
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setSortFields(pre => ({
                        ...pre,
                        defineOrder: false,
                        sortField: null,
                        sortOrder: null,
                      }));
                      return;
                    }
                    setSortFields(pre => ({
                      ...pre,
                      defineOrder: true,
                      orderFiled: sorter.field === 'gmtCreate' ? 'gmtCreate' : 'gmtModified',
                      orderInfo: sorter.order === 'ascend' ? 'ASC' : 'DESC',
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
      </Space>
    </>
  );
}
