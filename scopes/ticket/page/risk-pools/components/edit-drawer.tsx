import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useUpdateRiskPool } from '@manyun/ticket.gql.client.risk-register';
import { RiskToolForm } from '@manyun/ticket.page.risk-pool-mutator';
import type { RiskPoolFormValues } from '@manyun/ticket.page.risk-pool-mutator';

export type EditFormDrawerProps = {
  values: RiskPoolFormValues;
  id: number;
  onSuccess: () => void;
};

export function EditFormDrawer({ values, id, onSuccess }: EditFormDrawerProps) {
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm<RiskPoolFormValues>();
  const [updateRiskPool, { loading }] = useUpdateRiskPool({
    onCompleted(data) {
      if (!data.updateRiskPool.success) {
        message.error(data.updateRiskPool?.message);
        return;
      }
      onSuccess();
      message.success('保存成功');
      setVisible(false);
    },
  });

  return (
    <>
      <Button
        type="link"
        compact
        onClick={async () => {
          setVisible(true);
        }}
      >
        编辑
      </Button>
      <Drawer
        forceRender
        title="编辑风险点详情"
        size="large"
        placement="right"
        open={visible}
        width={752}
        extra={
          <Space>
            <Button
              onClick={() => {
                setVisible(false);
              }}
            >
              取消
            </Button>

            <Button
              type="primary"
              loading={loading}
              onClick={() => {
                form.validateFields().then(async formValues => {
                  updateRiskPool({
                    variables: {
                      query: {
                        id,
                        ...formValues,
                        fileInfoList: formValues.fileInfoList
                          ? formValues.fileInfoList.map(item => ({
                              ...McUploadFile.fromJSON(item).toJSON(),
                              uploadUser: { id: item.uploadUser.id, name: item.uploadUser.name },
                            }))
                          : null,
                      },
                    },
                  });
                });
              }}
            >
              保存
            </Button>
          </Space>
        }
        onClose={() => setVisible(false)}
      >
        <RiskToolForm form={form} values={values} />
      </Drawer>
    </>
  );
}
