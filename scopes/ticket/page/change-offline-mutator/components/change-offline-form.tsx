import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { BpmInstanceForm } from '@manyun/bpm.ui.bpm-instance-form';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useLoggedInUser } from '@manyun/iam.context.logged-in-user';
import { UserSelect } from '@manyun/iam.ui.user-select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import {
  useLazyAvailableChangeTemplate,
  useLazyChangeVersions,
  useNeedUploadChangeCustomerAgreement,
} from '@manyun/ticket.gql.client.tickets';

export type ChangeOfflineFormProps = {
  form: FormInstance<{}>;
  blockGuid?: string | null;
  idcTag?: string | null;
  setSubmitting: (loading: boolean) => void;
};

export function ChangeOfflineForm({
  form,
  blockGuid,
  idcTag,
  setSubmitting,
}: ChangeOfflineFormProps) {
  const [templateFileInfo, setTemplateFileInfo] = useState([]);
  const planStartTime = Form.useWatch('planStartTime', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const customerFileInfoList = Form.useWatch('customerFileInfoList', form);
  const riskLevel = Form.useWatch('riskLevel', form);
  const blockGuidInterior = Form.useWatch('blockGuid', form);
  const template = Form.useWatch('template', form);
  const reason = Form.useWatch('reason', form);
  const changeType = Form.useWatch('changeType', form);
  const respPerson = Form.useWatch('respPerson', form);
  const [getChangeVersions, { data: changeVersion }] = useLazyChangeVersions();
  const { user } = useLoggedInUser();
  const [{ data: changeLevel }, { readMetaData }] = useMetaData(MetaType.CHANGE_LEVEL);

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCustomerAgreement =
    ticketScopeCommonConfigs.changes.features.customerAgreement;
  const isChangesTicketCustomerAgreementEnabled = changesTicketCustomerAgreement !== 'disabled';
  const [needUploadChangeCustomerAgreement, { data }] = useNeedUploadChangeCustomerAgreement();
  const [planStartTimeBeforeThreeDays, { data: canSelectThreeDaysAgo }] =
    useNeedUploadChangeCustomerAgreement();
  const [availableChangeTemplate, { data: availableChangeTemplates }] =
    useLazyAvailableChangeTemplate();

  useEffect(() => {
    readMetaData();
  }, [readMetaData]);

  useEffect(() => {
    if (blockGuid) {
      form.setFieldsValue({ blockGuid });
    }
    getChangeVersions();
  }, [blockGuid, form, getChangeVersions]);

  useEffect(() => {
    form.setFieldValue('operator', {
      label: user.name,
      value: user.id,
    });
  }, [user, form]);

  useEffect(() => {
    if (riskLevel) {
      needUploadChangeCustomerAgreement({
        variables: { changeLevel: riskLevel, type: 'CUSTOMER_AGREE_2' },
      });
      planStartTimeBeforeThreeDays({
        variables: { changeLevel: riskLevel, type: 'PLAN_TIME_2' },
      });
    }
  }, [riskLevel, needUploadChangeCustomerAgreement, planStartTimeBeforeThreeDays]);

  const disabledDate = (current: Moment) => {
    if (canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement.data) {
      return current && current < moment().subtract(3, 'day').startOf('day');
    }
    return current && current < moment().subtract(1, 'days').hour(23).minute(59).second(59);
  };

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  const endTimeDisabledDateTime = (current: Moment | null) => {
    const time = form.getFieldValue('planStartTime');
    if (!time || !current) {
      return {};
    }
    const hours = time.hours();
    const minute = time.minute();
    const second = time.second();
    const currentHours = current.hours();
    const isStartToday = time.isSame(current, 'day');

    if (isStartToday) {
      if (currentHours > hours) {
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minute),
        disabledSeconds: () => range(0, second),
      };
    }
    return {};
  };

  const disabledDateTime = (current: Moment | null) => {
    if (!current || canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement.data) {
      return {};
    }

    const compareMoment = moment();
    const hours = compareMoment.hours();
    const minute = compareMoment.minute();
    const second = compareMoment.second();
    const currentHours = current.hours();

    const isToday = current.isSame(moment(), 'day');
    if (isToday) {
      if (currentHours > hours) {
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minute),
        disabledSeconds: () => range(0, second),
      };
    }
    return {};
  };

  const endTimeDisabledDate = (current: Moment) => {
    const startTime = form.getFieldValue('planStartTime');
    return (
      current && current < moment(startTime).subtract(1, 'days').hour(23).minute(59).second(59)
    );
  };

  // 内测版
  return (
    // eslint-disable-next-line react/jsx-filename-extension
    <>
      <Form labelCol={{ xl: 5 }} wrapperCol={{ xl: 17 }} colon={false} form={form}>
        <Form.Item
          label="变更标题"
          name="title"
          rules={[
            {
              required: true,
              message: '变更标题必填！',
              whitespace: true,
            },
            {
              max: 50,
              message: '最多输入 50 个字符！',
            },
          ]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label="机房/楼"
          name="blockGuid"
          rules={[
            {
              required: true,
              message: '机房/楼必选！',
            },
            {
              validator: (_, value) => {
                if (value && value.split('.').length !== 2) {
                  return Promise.reject('请选择至楼栋！');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <LocationTreeSelect
            disabled={!!blockGuid}
            disabledTypes={['IDC']}
            authorizedOnly
            showSearch
            allowClear
            idc={idcTag}
            onChange={value => {
              const [idcTag] = (value as string).split('.');
              if (!(changeVersion?.changeVersions.data ?? []).includes(idcTag)) {
                message.error(
                  '您所在机房暂不适用「变更管理（内测版）」，请进入「变更列表」进行操作'
                );
              }
              // judgeEventBetaVersion({
              //   variables: { guid: idcTag },
              //   onCompleted(data) {
              //     if (!data.judgeEventBetaVersion?.data) {
              //       message.error(
              //         '您所在机房暂不适用「变更管理（内测版）」，请进入「变更列表」进行操作'
              //       );
              //     }
              //   },
              // });
              form.setFieldValue('operator', {
                label: user.name,
                value: user.id,
              });
              if (template) {
                form.setFieldValue('template', undefined);
                form.setFieldValue('changeType', undefined);
                form.setFieldValue('riskLevel', undefined);
                form.setFieldValue('fileInfoList', undefined);
                setTemplateFileInfo([]);
              }
            }}
          />
        </Form.Item>
        <Form.Item
          label="变更专业"
          name="reason"
          rules={[
            {
              required: true,
              message: '变更专业必填！',
            },
          ]}
        >
          <MetaTypeSelect metaType={MetaType.CHANGE_REASON} labelInValue />
        </Form.Item>
        <Form.Item label="标准变更模版" name="template">
          <Select
            options={availableChangeTemplates?.availableChangeTemplates?.data ?? []}
            fieldNames={{ label: 'templateName', value: 'templateId' }}
            disabled={!blockGuidInterior}
            labelInValue
            allowClear
            onChange={(_, options) => {
              if (options) {
                form.setFieldValue('changeType', options.changeType);
                form.setFieldValue('riskLevel', options.riskLevel);
                form.setFieldValue('fileInfoList', options.fileInfoList);
                setTemplateFileInfo(options.fileInfoList);
              } else {
                form.setFieldValue('changeType', undefined);
                form.setFieldValue('riskLevel', undefined);
                form.setFieldValue('fileInfoList', undefined);
                setTemplateFileInfo([]);
              }
            }}
            onFocus={() => {
              availableChangeTemplate({
                variables: { changeVersion: 2, availArea: blockGuidInterior },
              });
            }}
          />
        </Form.Item>
        <Form.Item
          label="变更类型"
          name="changeType"
          rules={[
            {
              required: true,
              message: '变更类型必选！',
            },
          ]}
        >
          <MetaTypeSelect disabled={template} metaType={MetaType.CHANGE} />
        </Form.Item>
        <Form.Item
          label="变更等级"
          name="riskLevel"
          rules={[
            {
              required: true,
              message: '变更等级必选！',
            },
          ]}
        >
          <Select
            showArrow
            disabled={template}
            options={(template
              ? changeLevel.data
              : changeLevel.data.filter(
                  item =>
                    ![
                      'STANDARD_CHANGE_SECOND',
                      'STANDARD_CHANGE_THIRD',
                      'STANDARD_CHANGE_FOURTH',
                    ].includes(item.value)
                )
            ).map(item => ({
              label: item.label,
              value: item.value,
            }))}
          />
        </Form.Item>
        <Form.Item
          label="变更计划开始时间"
          name="planStartTime"
          rules={[
            {
              required: true,
              message: '变更计划开始时间必填！',
            },
            {
              validator(_, value) {
                const planStartTimeMoment = moment(value);
                if (canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement?.data) {
                  if (
                    value &&
                    moment(planStartTimeMoment.subtract(3, 'day').startOf('day')).diff(
                      planStartTimeMoment,
                      'minutes'
                    ) > 0
                  ) {
                    return Promise.reject(new Error('变更计划开始时间不可早于三天前'));
                  }
                  return Promise.resolve();
                }
                if (value && moment().diff(planStartTimeMoment, 'minutes') > 0) {
                  return Promise.reject(new Error('变更计划开始时间不可早于当前时间'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <DatePicker
            disabledDate={disabledDate}
            disabledTime={disabledDateTime}
            showTime
            format="YYYY-MM-DD HH:mm"
            showNow={false}
            onChange={() => {
              form.setFieldsValue({ planEndTime: undefined });
            }}
          />
        </Form.Item>
        <Form.Item
          label="变更计划完成时间"
          name="planEndTime"
          rules={[
            {
              required: true,
              message: '变更计划完成时间必填！',
            },
            {
              validator(_, value) {
                if (value && moment(planStartTime).diff(moment(value), 'minutes') > 0) {
                  return Promise.reject(new Error('变更计划完成时间不可早于变更计划开始时间'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <DatePicker
            disabledTime={endTimeDisabledDateTime}
            disabledDate={endTimeDisabledDate}
            disabled={!planStartTime}
            showTime
            format="YYYY-MM-DD HH:mm"
            showNow={false}
          />
        </Form.Item>
        {isChangesTicketCustomerAgreementEnabled &&
          data?.needUploadChangeCustomerAgreement.data && (
            <Form.Item
              label="客户同意书"
              name="customerFileInfoList"
              valuePropName="fileList"
              getValueFromEvent={({ fileList }: { fileList: MixedUploadFile[] }) => {
                if (fileList.filter(file => file.status === 'uploading').length) {
                  setSubmitting(true);
                } else {
                  setSubmitting(false);
                }
                return fileList;
              }}
            >
              <McUpload
                showAccept
                accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
                showUploadList
                allowDelete
                maxCount={3}
                openFileDialogOnClick={
                  customerFileInfoList === undefined || customerFileInfoList.length < 3
                }
              >
                <Button
                  disabled={customerFileInfoList && customerFileInfoList.length >= 3}
                  icon={<UploadOutlined />}
                >
                  点此上传
                </Button>
              </McUpload>
            </Form.Item>
          )}
        <Form.Item
          label="变更方案附件"
          name="fileInfoList"
          rules={[{ required: true, message: '请选择上传文件！' }]}
          valuePropName="fileList"
          getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
            if (fileList.filter(file => file.status === 'uploading').length) {
              setSubmitting(true);
            } else {
              setSubmitting(false);
            }
            return fileList;
          }}
        >
          <McUpload
            showAccept
            accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
            showUploadList
            allowDelete
            maxCount={10}
            openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 10}
            onRemove={value => {
              return new Promise((resolve, reject) => {
                if (templateFileInfo.filter(item => item.id === value.id).length) {
                  message.error('标准变更模版中的附件，不支持删除');
                  reject();
                }
                resolve();
              });
            }}
          >
            <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 10}>
              点此上传
            </Button>
          </McUpload>
        </Form.Item>
        <Form.Item
          label="影响区域"
          name="influenceArea"
          rules={[
            {
              required: true,
              message: '影响区域必填！',
              whitespace: true,
            },
            {
              max: 300,
              message: '最多可输入300 个字符！',
            },
          ]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
        <Form.Item
          label="涉及客户"
          name="relateCustomer"
          rules={[
            {
              required: true,
              message: '涉及客户必填！',
              whitespace: true,
            },
            {
              max: 30,
              message: '最多可输入30 个字符！',
            },
          ]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label="变更负责人"
          name="respPerson"
          rules={[
            {
              required: true,
              message: '变更负责人必填！',
            },
          ]}
        >
          <UserSelect allowClear userState="in-service" />
        </Form.Item>
        <Form.Item
          label="变更执行人"
          name="operator"
          hidden
          rules={[
            {
              required: true,
              message: '变更执行人必填！',
            },
          ]}
        >
          <UserSelect
            allowClear
            userState="in-service"
            resourceParams={[{ resourceType: 'BUILDING', resourceCodes: [blockGuidInterior] }]}
          />
        </Form.Item>
      </Form>
      {blockGuidInterior && riskLevel && reason && changeType && respPerson && (
        <BpmInstanceForm
          outerForm={form}
          labelCol={{ xl: 5 }}
          wrapperCol={{ xl: 17 }}
          idcTag={blockGuidInterior.split('.')[0]}
          blockGuid={blockGuidInterior}
          riskLevel={riskLevel}
          reason={reason.label}
          changeType={changeType}
          changeRespPerson={respPerson.value}
        />
      )}
    </>
  );
}
