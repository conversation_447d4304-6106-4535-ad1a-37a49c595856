import sortBy from 'lodash.sortby';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';
import { useLatest } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import type { ProcessNodeInfo } from '@manyun/bpm.ui.bpm-instance-form';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useCreateChange } from '@manyun/ticket.gql.client.tickets';
import { CHANGE_EXE_WAY_MAP } from '@manyun/ticket.model.change';
import { generateChangeOfflineLocation } from '@manyun/ticket.route.ticket-routes';
import { fetchChange } from '@manyun/ticket.service.fetch-change';

import { ChangeOfflineForm } from './components/change-offline-form';

export function ChangeOfflineMutator() {
  const [form] = Form.useForm();
  const { id } = useParams<{ id: string }>();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const history = useHistory();
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [createChange, { loading }] = useCreateChange();
  const processNodeInfoList: ProcessNodeInfo[] = Form.useWatch('processNodeInfoList', form) ?? [];
  const processNodeInfoListRef = useLatest(processNodeInfoList);

  useEffect(() => {
    if (id) {
      getDetail();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const getDetail = async () => {
    const { data, error } = await fetchChange({ changeOrderId: id });
    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      form.setFieldsValue({
        title: data.title,
        blockGuid: data.blockTag,
        reason: { value: data.reason, label: data.reasonName },
        changeType: data.changeType,
        riskLevel: data.riskLevel,
        planStartTime: moment(data.planStartTime),
        planEndTime: moment(data.planEndTime),
        fileInfoList: data.fileInfoList?.map(obj => McUploadFile.fromApiObject(obj)),
        customerFileInfoList: data.customerFileInfoList?.map(obj =>
          McUploadFile.fromApiObject(obj)
        ),
        operator: { label: data.operatorName, value: data.operatorId },
        influenceArea: data.influenceArea,
        relateCustomer: data.relateCustomer,
        respPerson: { label: data.respPersonName, value: data.respPersonId },
        template: data.templateId
          ? { label: data.templateName, value: data.templateId }
          : undefined,
      });
    }
  };

  const submit = useCallback(() => {
    form
      .validateFields()
      .then(async values => {
        const [idcTag] = values.blockGuid.split('.');
        const blockTag = values.blockGuid;
        const {
          operator,
          influenceArea,
          relateCustomer,
          customerFileInfoList,
          riskLevel,
          changeType,
          planStartTime,
          planEndTime,
          fileInfoList,
          reason,
          title,
          respPerson,
          template,
          processVersion,
          processNodeInfoList,
        } = values;
        const _p = {
          riskLevel,
          changeType,
          planStartTime: planStartTime ? planStartTime.valueOf() : 0,
          planEndTime: planEndTime ? planEndTime.valueOf() : 0,
          title,
          idcTag,
          blockTag,
          reason: reason.value,
          exeWay: CHANGE_EXE_WAY_MAP.OffLine,
          changeVersion: 2,
          influenceArea,
          operatorId: operator.value,
          operatorName: operator.label,
          fileInfoList: fileInfoList?.map(obj => ({
            ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
          })),
          respPersonId: respPerson.value,
          respPersonName: respPerson.label,
          customerFileInfoList: customerFileInfoList?.map((obj: McUploadFile) => ({
            ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
          })),
          relateCustomer,
          templateId: null,
          templateName: null,
          processVersion,
          processCustomUserInfoList: Array.isArray(processNodeInfoList)
            ? processNodeInfoList
                .filter(info => info.edit && (info.userIdList ?? []).length !== 0)
                .map(({ id, name, userIdList }) => ({
                  id,
                  name,
                  userIdList,
                }))
            : undefined,
        };
        if (template) {
          _p.templateId = template.value;
          _p.templateName = template.label;
        }

        createChange({
          variables: {
            query: {
              changeOrderId: id,
              ..._p,
            },
          },
          onCompleted(data) {
            if (data.createChange?.message) {
              message.error(data.createChange?.message);
              return;
            }
            message.success('提交成功');
            history.push(
              generateChangeOfflineLocation({
                id: data.createChange?.changeOrderId,
              })
            );
          },
        });
        // setSubmitting(false);
      })
      .catch(err => {
        form.scrollToField(err.errorFields[0].name.toString(), {
          behavior: actions => {
            actions.forEach(action => {
              action.el.scrollTop = action.top - 55;
            });
          },
        });
      });
  }, [createChange, form, history, id]);

  const handleConfirmOpenChange = useCallback(
    (newOpen: boolean) => {
      if (!newOpen) {
        setConfirmOpen(false);
        return;
      }
      let canSubmit = true;
      processNodeInfoListRef.current.forEach(processNodeInfo => {
        if (processNodeInfo.edit && (processNodeInfo.userIdList ?? []).length === 0) {
          canSubmit = false;
          setConfirmOpen(newOpen);
          return;
        }
      });
      canSubmit && submit();
    },
    [submit, processNodeInfoListRef]
  );

  return (
    <>
      <Card title="新建线下变更" bodyStyle={{ justifyContent: 'center', display: 'flex' }}>
        <Space direction="vertical" style={{ width: 680 }}>
          <ChangeOfflineForm form={form} setSubmitting={setSubmitting} />
          <Form.Item label=" " colon={false} labelCol={{ span: 5 }}>
            <Popconfirm
              title="未完成选择审批流程中的审批人，确定按照默认审批流程？"
              open={confirmOpen}
              onOpenChange={handleConfirmOpenChange}
              onConfirm={() => {
                setConfirmOpen(true);
                submit();
              }}
              onCancel={() => {
                setConfirmOpen(false);
              }}
            >
              <Button type="primary" loading={submitting || loading}>
                提交
              </Button>
            </Popconfirm>
          </Form.Item>
        </Space>
      </Card>
    </>
  );
}

export type SelectedStoreState = {
  userId: number;
  username: string;
  name: string;
};

export function selectUserInfos({
  user: { userId, username, name },
}: {
  user: { userId: number; username: string; name: string };
}): SelectedStoreState {
  return {
    userId,
    username,
    name,
  };
}

export function getPlantime({
  changeTimeList,
}: {
  changeTimeList: { date: [Moment, Moment]; time: [Moment, Moment] }[];
}): {
  changeTimeList: { startDate: string; endDate: string; startHour: string; endHour: string }[];
  planStartTime: number;
  planEndTime: number;
} {
  let times: string[] = [];

  const timeList = changeTimeList.map(item => {
    const startDate = item.date[0].format('YYYY-MM-DD');
    const endDate = item.date[1].format('YYYY-MM-DD');
    const startHour = item.time[0].format('HH:mm');
    const endHour = item.time[1].format('HH:mm');
    times.push(`${startDate} ${startHour}:00`);
    times.push(`${endDate} ${endHour}:00`);
    return {
      startDate,
      endDate,
      startHour,
      endHour,
    };
  });
  times = sortBy(times);
  const planStartTime = new Date(times[0]).getTime();
  const planEndTime = new Date(times[times.length - 1]).getTime();
  return {
    planStartTime,
    planEndTime,
    changeTimeList: timeList,
  };
}
