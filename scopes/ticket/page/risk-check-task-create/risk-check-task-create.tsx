import React from 'react';
import { useHistory } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';

import { PlanType } from '@manyun/ticket.model.task';
import {
  RISK_CHECK_TASK_LIST_ROUTE_PATH,
  generateRiskCheckTaskDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { TicketTaskMutator } from '@manyun/ticket.ui.ticket-task-mutator';

export function RiskCheckTaskCreate() {
  const history = useHistory();

  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }} color="default">
      <TicketTaskMutator
        planType={PlanType.RiskCheckPlan}
        unusedFormItems={['endTime', 'drillLevel', 'drillMajorType', 'fileInfoList']}
        mode="create"
        onCancel={() => {
          history.push(RISK_CHECK_TASK_LIST_ROUTE_PATH);
        }}
        onSuccess={(taskId?: string) => {
          if (taskId) {
            history.push(generateRiskCheckTaskDetailRoutePath({ id: taskId }));
          }
        }}
      />
    </Container>
  );
}
