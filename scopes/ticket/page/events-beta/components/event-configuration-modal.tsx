import React, { useEffect, useState } from 'react';

import { SettingOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import {
  useLazyEventConfiguration,
  useSubmitEventConfiguration,
} from '@manyun/ticket.gql.client.tickets';

export type EventSkipType = 'SkipRelief' | 'SkipAudit';
export type FormValue = {
  skippedAuditEventLevels: string[];
  skipRules: string[];
};
export function EventConfigurationModal() {
  const [visible, setVisible] = useState<boolean>(false);
  const [form] = Form.useForm<FormValue>();
  const skipRules = Form.useWatch('skipRules', form);
  const [getEventConfiguration, { loading }] = useLazyEventConfiguration({
    onCompleted(data) {
      if (data.eventConfiguration.message) {
        message.error(data.eventConfiguration.message);
        return;
      }
      const skippedAuditEventLevels =
        (data.eventConfiguration.data?.eventAuditSkip ?? []).length > 0
          ? (data.eventConfiguration.data?.eventAuditSkip as string[])
          : undefined;
      const skipRules = [];
      if (skippedAuditEventLevels) {
        skipRules.push('SkipAudit');
      }

      form.setFieldsValue({
        skipRules,
        skippedAuditEventLevels,
      });
    },
  });
  const [submitEventConfiguration, { loading: submitLoading }] = useSubmitEventConfiguration();
  const onOk = async () => {
    const { skippedAuditEventLevels } = await form.validateFields();
    const { data } = await submitEventConfiguration({
      variables: { query: { eventAuditSkip: skippedAuditEventLevels ?? [] } },
    });

    if (!data?.submitEventConfiguration?.success) {
      message.error(data?.submitEventConfiguration?.message);
      return;
    }
    message.success('事件配置成功，规则即时生效');
    setVisible(false);
  };

  useEffect(() => {
    // 每次弹窗被点开时，请求查询接口，初始化表单的值
    if (visible) {
      getEventConfiguration();
    }
  }, [form, visible, getEventConfiguration]);
  return (
    <>
      <Button
        type="link"
        icon={<SettingOutlined />}
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        配置
      </Button>
      <Modal
        title="事件配置"
        width={720}
        okButtonProps={{ loading: loading || submitLoading }}
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        onOk={onOk}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item name="skipRules" noStyle>
            <Checkbox.Group style={{ width: '100%' }}>
              <Space direction="vertical" style={{ width: '100%' }}>
                <Checkbox value="SkipAudit">支持无需复盘</Checkbox>
                {skipRules?.includes('SkipAudit') && (
                  <Form.Item label="当事件等级为" colon={false}>
                    <Space>
                      <Form.Item
                        name="skippedAuditEventLevels"
                        rules={[{ required: true, message: '请选择事件等级' }]}
                        noStyle
                      >
                        <MetaTypeSelect
                          style={{ width: 210 }}
                          showSearch
                          optionLabelProp="label"
                          optionFilterProp="label"
                          mode="multiple"
                          maxTagCount="responsive"
                          metaType={MetaType.N_EVENT_LEVEL}
                          allowClear
                        />
                      </Form.Item>
                      <Typography.Text>时，支持无需复盘，直接提交事件评审 </Typography.Text>
                    </Space>
                  </Form.Item>
                )}
              </Space>
            </Checkbox.Group>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
