import dayjs from 'dayjs';
import { omit } from 'lodash';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { User } from '@manyun/auth-hub.ui.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { RoomTypeText } from '@manyun/resource-hub.ui.room-type-text';
import { useLazyEventsBeta, useReopenEvent } from '@manyun/ticket.gql.client.tickets';
import type {
  EventBetaJson,
  EventBetaQuery,
  FaultModelInfo,
} from '@manyun/ticket.gql.client.tickets';
import {
  BackendFalseAlarm,
  EventBetaProcessStatus,
  type EventBetaProcessStatusType,
  EventBetaStatusMap,
  FAULT_TARGET_TYPE_TEXT,
  FalseAlarmMap,
  FaultTargetType,
} from '@manyun/ticket.model.event';
import { EVENT_CREATE_ROUTE_PATH, generateEvnetLocation } from '@manyun/ticket.route.ticket-routes';
import { exportEvent } from '@manyun/ticket.service.export-event';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { EventConfigurationModal } from './components/event-configuration-modal';
import { FalseAlarmTypeSelect } from './components/false-alarm-select';

type SearchField = Omit<EventBetaQuery, 'planTime' | 'pageNum' | 'pageSize'> & {
  createTime?: [Moment, Moment] | null;
  closeTime?: [Moment, Moment] | null;
  occurTime?: [Moment, Moment] | null;
  location?: string[] | null;
  pageNum: number;
  pageSize: number;
  idcTag?: string;
  blockTag?: string;
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';

const sorterMaps: Record<string, string> = {
  closeTime: 'closeTime',
  eventLevelName: 'eventLevel',
  gmtCreate: 'createTime',
  occurTime: 'occurTime',
  confirmSla: 'confirmSla',
  relieveSla: 'relieveSla',
  finishSla: 'finishSla',
};
export const EventBetaProcessStatusTagColorMap = {
  [EventBetaProcessStatus.Auditing]: 'warning',
  [EventBetaProcessStatus.Confirming]: 'processing',
  [EventBetaProcessStatus.Finishing]: 'processing',
  [EventBetaProcessStatus.Relieving]: 'processing',
  [EventBetaProcessStatus.Reviewing]: 'processing',
  [EventBetaProcessStatus.Closed]: 'default',
};

export function EventsBeta() {
  const [exportLoading, setExportLoading] = useState(false);
  const [, { checkCode }] = useAuthorized();
  const isCreate = checkCode('element_event-create');
  const [tableColumns, setTableColumns] = useState<ColumnType<EventBetaJson>[]>([
    {
      title: '事件ID',
      dataIndex: 'eventId',
      fixed: 'left',
      disabled: true,
      render: (text: string) => (
        <Link
          key={text}
          target="_blank"
          to={generateEvnetLocation({
            id: text,
          })}
        >
          {text}
        </Link>
      ),
    },
    {
      title: '位置',
      dataIndex: 'blockGuid',
      render: (_, { blockGuid, idcTag }) => blockGuid || idcTag,
    },
    {
      title: '事件标题',
      dataIndex: 'eventTitle',
      render(title) {
        return (
          <Typography.Text style={{ width: 264 }} ellipsis={{ tooltip: title }}>
            {title}
          </Typography.Text>
        );
      },
    },
    {
      title: '事件来源',
      dataIndex: 'eventSourceName',
    },
    {
      title: '事件等级',
      dataIndex: 'eventLevelName',
      sorter: true,
    },
    {
      title: '是否误报',
      dataIndex: 'falseAlarm',
      render: text => (
        <Typography.Text>
          {/* <Typography.Text type={text === BackendFalseAlarm.None ? undefined : 'danger'}> */}
          {FalseAlarmMap[text as BackendFalseAlarm]}
        </Typography.Text>
      ),
    },
    {
      title: '专业分类',
      dataIndex: 'categoryName',
    },
    {
      title: '目标类型',
      dataIndex: 'faultType',
      render: (text: string) => <span>{FAULT_TARGET_TYPE_TEXT[text as FaultTargetType]}</span>,
    },
    {
      title: '目标名称',
      dataIndex: 'faultName',
      width: 264,
      render(_, { faultType, faultModelList }) {
        return (
          <FaultTargetLink
            type={faultType as FaultTargetType}
            causeDevices={faultModelList}
            width={264}
          />
        );
      },
    },

    {
      title: '事件描述',
      dataIndex: 'eventDesc',
      render(eventDesc) {
        return (
          <Typography.Text style={{ width: 264 }} ellipsis={{ tooltip: eventDesc }}>
            {eventDesc}
          </Typography.Text>
        );
      },
    },
    {
      title: '事件状态',
      dataIndex: 'eventStatus',
      render: (_, { eventStatus }) => {
        return (
          <Tag color={EventBetaProcessStatusTagColorMap[eventStatus]}>
            {EventBetaStatusMap[eventStatus]}
          </Tag>
        );
      },
    },
    {
      title: '缓解方案',
      dataIndex: 'relieveHandleContent',
      show: false,
      render(relieveHandleContent) {
        return (
          <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: relieveHandleContent }}>
            {relieveHandleContent ?? '--'}
          </Typography.Text>
        );
      },
    },
    {
      title: '结束方案',
      dataIndex: 'finishHandleContent',
      show: false,
      render(finishHandleContent) {
        return (
          <Typography.Text style={{ width: 240 }} ellipsis={{ tooltip: finishHandleContent }}>
            {finishHandleContent ?? '--'}
          </Typography.Text>
        );
      },
    },
    {
      title: '是否转为问题',
      dataIndex: 'convertToProblem',
      render: convertToProblem => {
        if (convertToProblem === null || convertToProblem === undefined) {
          return '--';
        }
        if (convertToProblem) {
          return (
            <Space>
              <Badge status="error" />
              <Typography.Text>是</Typography.Text>
            </Space>
          );
        }
        if (!convertToProblem) {
          return (
            <Space>
              <Badge status="default" />
              <Typography.Text>否</Typography.Text>
            </Space>
          );
        }
      },
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      sorter: true,
      render: (text: string) => {
        return <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },

    {
      title: '发生时间',
      dataIndex: 'occurTime',
      sorter: true,
      render: (text: string) => {
        return <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span>;
      },
    },

    {
      title: '应急恢复时间',
      dataIndex: 'confirmSla',
      key: 'confirmSla',
      sorter: true,
      render: (_, { confirmSla }) => {
        if (confirmSla) {
          return (
            <TicketSlaText
              taskSla={confirmSla}
              delay={confirmSla}
              unit="SECOND"
              effectTime={null}
              endTime={null}
            />
          );
        }
        return '--';
      },
    },
    {
      title: '故障修复时间',
      dataIndex: 'relieveSla',
      key: 'relieveSla',
      sorter: true,
      render: (_, { relieveSla }) => {
        if (relieveSla) {
          return (
            <TicketSlaText
              taskSla={relieveSla}
              delay={relieveSla}
              unit="SECOND"
              effectTime={null}
              endTime={null}
            />
          );
        }
        return '--';
      },
    },

    {
      title: '系统恢复时间',
      dataIndex: 'finishSla',
      key: 'finishSla',
      sorter: true,
      render: (_, { finishSla }) => {
        if (finishSla) {
          return (
            <TicketSlaText
              taskSla={finishSla}
              delay={finishSla}
              unit="SECOND"
              effectTime={null}
              endTime={null}
            />
          );
        }
        return '--';
      },
    },
    {
      title: '关闭时间',
      dataIndex: 'closeTime',
      sorter: true,
      render: (text: string) => {
        return text ? <span>{dayjs(text).format('YYYY-MM-DD HH:mm:ss')}</span> : '--';
      },
    },
    {
      title: '线上处理人',
      dataIndex: 'ownerName',
      render: (_, { ownerId }) => <User.Link id={ownerId} />,
    },
    {
      title: '创建人',
      dataIndex: 'createUserName',
      render: (_, { createUserName, createUserId }) => (
        <UserLink userId={createUserId} userName={createUserName} external />
      ),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      disabled: true,
      fixed: 'right',
      render: (_, { closeTime, eventId, eventStatus, falseAlarm }) => {
        if (
          isCreate &&
          moment().diff(moment(closeTime).startOf('day'), 'days') <= 180 &&
          eventStatus === EventBetaProcessStatus.Closed &&
          falseAlarm === BackendFalseAlarm.None
        ) {
          return (
            <Popconfirm
              key="delete"
              title="确认重启事件单？重启后事件将回到复盘阶段"
              okText="确定重启"
              okButtonProps={{ disabled: reopenLoading }}
              onConfirm={async () => {
                reopen({
                  variables: { eventId },
                });
              }}
            >
              <Button compact type="link">
                重启
              </Button>
            </Popconfirm>
          );
        }
        return '--';
      },
    },
  ]);
  const [form] = Form.useForm();

  const [getList, { data, loading }] = useLazyEventsBeta({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  const [reopen, { loading: reopenLoading }] = useReopenEvent({
    onCompleted(data) {
      if (!data.reopenEvent?.success) {
        message.error(data.reopenEvent?.message);
        return;
      }
      setFields({ ...fields, pageNum: 1 });
    },
  });

  const { search } = useLocation();
  const {
    pageNum,
    pageSize,
    idcTag,
    blockTag,
    createBeginTime,
    createEndTime,
    closeBeginTime,
    closeEndTime,
    occurBeginTime,
    occurEndTime,
    convertToProblem,
    location,
    ...restDefaultFields
  } = getLocationSearchMap<SearchField>(search, {
    // parseNumbers: true,
    arrayKeys: [
      'idcTagList',
      'blockGuidList',
      'eventLevelCodeList',
      'categoryCodeList',
      'falseAlarms',
      'location',
      'eventSourceList',
    ],
  });

  const [fields, setFields] = useState<SearchField>({
    ...restDefaultFields,
    createTime:
      createBeginTime && createEndTime
        ? [moment(Number(createBeginTime)), moment(Number(createEndTime))]
        : undefined,
    closeTime:
      closeBeginTime && closeEndTime
        ? [moment(Number(closeBeginTime)), moment(Number(closeEndTime))]
        : undefined,
    occurTime:
      occurBeginTime && occurEndTime
        ? [moment(Number(occurBeginTime)), moment(Number(occurEndTime))]
        : undefined,
    location: location,
    pageNum: pageNum ? Number(pageNum) : 1,
    pageSize: pageSize ? Number(pageSize) : 10,
    convertToProblem:
      convertToProblem === 'true' ? true : convertToProblem === 'false' ? false : null,
  });

  const history = useHistory();

  useEffect(() => {
    getList({ variables: { query: { pageNum: 1, pageSize: 10 } } });
  }, [getList]);

  useEffect(() => {
    getList({
      variables: {
        query: {
          ...getParams(),
        },
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, getList]);

  const getParams = () => {
    const { closeTime, createTime, location, eventId, occurTime, falseAlarms, ...resp } = fields;
    form.setFieldsValue(fields);
    const idcTags = location ? location.filter(item => item.split('.').length === 1) : [];
    const blockTags = location ? location.filter(item => item.split('.').length === 2) : [];
    const baseQ: EventBetaQuery = {
      ...resp,
      eventId: eventId?.trim(),
      falseAlarms: falseAlarms?.map(item => Number(item)),
      createBeginTime: createTime?.length
        ? (moment(createTime[0].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      createEndTime: createTime?.length
        ? moment(createTime[1].format('YYYY-MM-DD HH:mm:ss')).valueOf()
        : null,
      closeBeginTime: closeTime?.length
        ? (moment(closeTime[0].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      closeEndTime: closeTime?.length
        ? (moment(closeTime[1].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      occurBeginTime: occurTime?.length
        ? (moment(occurTime[0].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      occurEndTime: occurTime?.length
        ? (moment(occurTime[1].format('YYYY-MM-DD HH:mm:ss')).valueOf() as number)
        : null,
      idcTagList: idcTags,
      blockGuidList: blockTags,
    };
    setLocationSearch({ ...omit(baseQ, 'idcTagList', 'blockGuidList'), location });
    return baseQ;
  };

  const handleFileExport = useCallback(
    async type => {
      setExportLoading(true);
      let params = {};
      if (type === 'filtered') {
        params = { ...getParams() };
      }
      const { error, data } = await exportEvent({
        ...params,
        exportIncludeFields: tableColumns
          .map(item => item.show !== false && item.dataIndex)
          .filter(Boolean),
      });
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return false;
      }
      return data;
    },
    //  eslint-disable-next-line react-hooks/exhaustive-deps
    [fields, tableColumns]
  );

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <QueryFilter
            form={form}
            items={[
              {
                label: '事件ID',
                name: 'eventId',
                control: <Input allowClear />,
              },
              {
                label: '位置',
                name: 'location',
                control: <LocationTreeSelect authorizedOnly allowClear multiple />,
              },
              {
                label: '事件状态',
                name: 'eventStatusList',
                control: (
                  <Select
                    showSearch
                    allowClear
                    mode="multiple"
                    maxTagCount={1}
                    options={Object.keys(EventBetaStatusMap).map(item => ({
                      label: EventBetaStatusMap[item as EventBetaProcessStatusType],
                      value: item,
                    }))}
                  />
                ),
              },
              {
                label: '事件等级',
                name: 'eventLevelCodeList',
                control: (
                  <MetaTypeSelect mode="multiple" metaType={MetaType.N_EVENT_LEVEL} allowClear />
                ),
              },
              {
                label: '事件来源',
                name: 'eventSourceList',
                control: (
                  <MetaTypeSelect metaType={MetaType.EVENT_SOURCE} allowClear mode="multiple" />
                ),
              },

              {
                label: '专业分类',
                name: 'categoryCodeList',
                control: (
                  <MetaTypeSelect mode="multiple" metaType={MetaType.N_EVENT_CATEGORY} allowClear />
                ),
              },
              {
                label: '是否误报',
                name: 'falseAlarms',
                control: <FalseAlarmTypeSelect mode="multiple" allowClear />,
              },
              {
                label: '事件标题',
                name: 'eventTitle',
                control: <Input allowClear />,
              },
              {
                label: '设备名称',
                name: 'deviceGuid',
                control: (
                  <DeviceSelect
                    fieldNames={{
                      label: 'name',
                      value: 'guid',
                    }}
                    style={{ width: 200 }}
                    allowClear
                  />
                ),
              },
              {
                label: '包间及其他',
                name: 'relateName',
                control: <Input style={{ width: 200 }} allowClear />,
              },

              {
                label: '线上处理人',
                name: 'ownerId',
                control: <UserSelect allowClear labelInValue={false} />,
              },
              {
                label: '创建人',
                name: 'createUserId',
                control: <UserSelect allowClear labelInValue={false} />,
              },

              {
                label: '创建时间',
                name: 'createTime',
                control: (
                  <DatePicker.RangePicker
                    showTime={{ format: 'HH:mm:ss' }}
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
                span: 2,
              },
              {
                label: '发生时间',
                name: 'occurTime',
                control: (
                  <DatePicker.RangePicker
                    showTime={{ format: 'HH:mm:ss' }}
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
                span: 2,
              },
              {
                label: '关闭时间',
                name: 'closeTime',
                control: (
                  <DatePicker.RangePicker
                    showTime={{ format: 'HH:mm:ss' }}
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder={['开始时间', '结束时间']}
                  />
                ),
                span: 2,
              },
              {
                label: '是否转为问题',
                name: 'convertToProblem',
                control: (
                  <Select
                    showSearch
                    allowClear
                    options={[
                      { label: '是', value: true },
                      { label: '否', value: false },
                    ]}
                  />
                ),
                span: 1,
              },
            ]}
            // initialValues={fields}
            onSearch={value => {
              setFields({ ...value, pageNum: 1, pageSize: fields.pageSize });
            }}
            onReset={() => {
              form.setFieldsValue({ pageNum: 1, pageSize: fields.pageSize });
              setFields({ pageNum: 1, pageSize: fields.pageSize });
            }}
          />
        </Card>
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space
              style={{ width: '100%', justifyContent: 'space-between' }}
              direction="horizontal"
              size="middle"
            >
              {isCreate ? (
                <Button type="primary" onClick={() => history.push(EVENT_CREATE_ROUTE_PATH)}>
                  新建事件
                </Button>
              ) : (
                <span />
              )}
              <Space direction="horizontal">
                <EventConfigurationModal />
                <FileExport
                  text="导出"
                  filename="事件.xls"
                  disabled={exportLoading}
                  data={type => {
                    return handleFileExport(type);
                  }}
                  showExportFiltered
                />
                <EditColumns
                  uniqKey="EVNET_BETA_EDIT_COLUMNS"
                  listsHeight={360}
                  defaultValue={tableColumns}
                  onChange={columns => {
                    setTableColumns(columns);
                  }}
                />
              </Space>
            </Space>

            <Table<EventBetaJson>
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading}
              dataSource={data?.eventsBeta?.data ?? []}
              columns={tableColumns}
              pagination={{
                total: data?.eventsBeta?.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, sorter, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                  setLocationSearch({
                    ...fields,
                    pageNum: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                }
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setFields(pre => ({
                        ...pre,
                        orderField: null,
                        desc: null,
                      }));
                      setLocationSearch({
                        ...fields,
                        orderField: null,
                        desc: null,
                      });
                      return;
                    }
                    setLocationSearch({
                      ...fields,
                      orderField: sorterMaps[sorter.field as string],
                      desc: sorter.order === 'descend',
                    });
                    setFields(pre => ({
                      ...pre,
                      orderField: sorterMaps[sorter.field as string],
                      desc: sorter.order === 'descend',
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
      </Space>
    </>
  );
}

export const FaultTargetLink = ({
  type,
  causeDevices,
  width,
}: {
  type: FaultTargetType;
  causeDevices: FaultModelInfo[];
  width?: number;
}) => {
  const causeDevicesLinkContent = (isInPopover: boolean = false) => {
    switch (type) {
      case FaultTargetType.Device:
        return getDeviceContent(isInPopover);
      case FaultTargetType.Room:
        return getRoomContent(isInPopover);
      case FaultTargetType.Other:
        return getOtherContent();
    }
  };
  function getDeviceContent(isInPopover: boolean) {
    return causeDevices.map((item, index) => (
      <span key={item.guid}>
        <Link
          key={item.guid}
          target="_blank"
          to={generateDeviceRecordRoutePath({
            guid: item.guid,
          })}
        >
          {item.name}
          {item.deviceType && (
            <>
              (
              <DeviceTypeText code={item.deviceType} />)
            </>
          )}
        </Link>
        {!isInPopover && index !== causeDevices.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }

  function getRoomContent(isInPopover: boolean) {
    return causeDevices.map((item, index) => (
      <span key={item.roomTag}>
        <Typography.Link
          key={item.roomTag}
          onClick={() => {
            window.open(
              generateRoomMonitoringUrl({
                idc: item.idcTag!,
                block: item.blockTag!,
                room: item.roomTag!,
              })
            );
          }}
        >
          {item.roomTag}(<RoomTypeText code={item.deviceType!} />)
        </Typography.Link>
        {!isInPopover && index !== causeDevices.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }

  function getOtherContent() {
    return (
      <>
        {Array.isArray(causeDevices) &&
          causeDevices.map(item => <span key={item.guid}>{item.name}</span>)}
      </>
    );
  }
  return (
    <Popover
      content={
        <Space style={{ maxWidth: 400 }} split={<Divider type="vertical" spaceSize="mini" />} wrap>
          {causeDevicesLinkContent(true)}
        </Space>
      }
    >
      <Typography.Text
        ellipsis
        style={{
          width,
          color: type !== FaultTargetType.Other ? `var(--${prefixCls}-primary-color)` : undefined,
        }}
      >
        {causeDevicesLinkContent()}
      </Typography.Text>
    </Popover>
  );
};
