import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Container } from '@manyun/base-ui.ui.container';
import { Space } from '@manyun/base-ui.ui.space';

import { useLazyTasks } from '@manyun/ticket.gql.client.tickets';
import type { PlanJSON } from '@manyun/ticket.model.task';
import { PlanType } from '@manyun/ticket.model.task';

import { RiskCheckTasksFilter } from './components/risk-check-tasks-filter';
import type { FiltersFormValues } from './components/risk-check-tasks-filter';
import { RiskCheckTasksTable } from './components/risk-check-tasks-table';

export type TaskSorter = { defineDesc: boolean; sortByField?: string; sortInfo?: string };
export function RiskCheckTaskList() {
  const [getTasks, { data, loading }] = useLazyTasks();
  const [taskSorter, setTaskSorter] = useState<TaskSorter>({ defineDesc: false });
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [exportQueryParams, setExportQueryParams] = useState<any>();
  const filterSearchParamsRef = useRef<FiltersFormValues | undefined>();
  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({
    pageNum: 1,
    pageSize: 10,
    total: 0,
  });

  const fetchTasks = useCallback(
    async ({
      pageNum = 1,
      pageSize = 10,
      sorter = { defineDesc: true, sortByField: 'gmtCreate', sortInfo: 'DESC' },
    }: {
      pageNum?: number;
      pageSize?: number;
      sorter?: TaskSorter;
    }) => {
      const { defineDesc, sortByField, sortInfo } = sorter;
      const queryParams = {
        name: filterSearchParamsRef.current?.name,
        creatorId: filterSearchParamsRef.current?.creatorId?.value
          ? `${filterSearchParamsRef.current.creatorId.value}`
          : undefined,
        manageType: filterSearchParamsRef.current?.manageType,

        startTime:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.createdTimeRange) &&
          filterSearchParamsRef.current.createdTimeRange.length > 0
            ? filterSearchParamsRef.current.createdTimeRange[0].startOf('day')!.valueOf()
            : undefined,
        endTime:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.createdTimeRange) &&
          filterSearchParamsRef.current.createdTimeRange.length > 0
            ? filterSearchParamsRef.current.createdTimeRange[1].endOf('day').valueOf()
            : undefined,
        startDateOfUpdate:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.updateTimeRange) &&
          filterSearchParamsRef.current.updateTimeRange.length > 0
            ? filterSearchParamsRef.current.updateTimeRange[0].startOf('day')!.valueOf()
            : undefined,
        endDateOfUpdate:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.updateTimeRange) &&
          filterSearchParamsRef.current.updateTimeRange.length > 0
            ? filterSearchParamsRef.current.updateTimeRange[1].endOf('day').valueOf()
            : undefined,
      };
      setPagination({ ...pagination, pageNum, pageSize });
      setExportQueryParams({
        ...queryParams,
        jobTypeList: ['SCH_RISK'],
        subJobType: 'SCH_RISK',
        defineDesc,
        sortByField,
        sortInfo,
      });
      await getTasks({
        variables: {
          ...queryParams,
          jobTypeList: ['SCH_RISK'],
          subJobType: 'SCH_RISK',
          pageNum,
          pageSize,
          defineDesc,
          sortByField,
          sortInfo,
        },
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  useEffect(() => {
    fetchTasks({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Container style={{ width: '100%', height: '100%' }}>
      <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
        <RiskCheckTasksFilter
          onChange={(params: FiltersFormValues | undefined) => {
            filterSearchParamsRef.current = params;
            fetchTasks({
              pageNum: 1,
              pageSize: pagination.pageSize,
              sorter: taskSorter,
            });
          }}
        />

        <RiskCheckTasksTable
          rowKey="id"
          planType={PlanType.RiskCheckPlan}
          dataSource={(data?.tasks?.data as PlanJSON[]) ?? []}
          searchParams={exportQueryParams}
          pagination={{
            total: data?.tasks?.total,
            pageSize: pagination.pageSize,
            current: pagination.pageNum,
          }}
          scroll={{ x: 'max-content' }}
          loading={loading}
          onSuccess={() => {
            fetchTasks({
              pageNum: pagination.pageNum,
              pageSize: pagination.pageSize,
              sorter: taskSorter,
            });
          }}
          onChange={(pagination, filters, sorter) => {
            const { field, order } = sorter as { field: string; order?: 'ascend' | 'descend' };
            let sortInfo;
            if (order === 'ascend') {
              sortInfo = 'ASC';
            }
            if (order === 'descend') {
              sortInfo = 'DESC';
            }
            fetchTasks({
              pageNum: pagination.current!,
              pageSize: pagination.pageSize!,
              sorter: { defineDesc: Boolean(sortInfo), sortByField: field, sortInfo },
            });
            setTaskSorter({ defineDesc: Boolean(sortInfo), sortByField: field, sortInfo });
          }}
        />
      </Space>
    </Container>
  );
}
