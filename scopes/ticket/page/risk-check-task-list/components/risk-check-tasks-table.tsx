import React, { useState } from 'react';
import { Link, useHistory } from 'react-router-dom';

import dayjs from 'dayjs';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import type { ColumnType as BasicColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { useBatchUpdateTaskStatus, useDeleteTask } from '@manyun/ticket.gql.client.tickets';
import type { PlanJSON } from '@manyun/ticket.model.task';
import { Plan, PlanStatus, PlanType } from '@manyun/ticket.model.task';
import { BackendTaskStatus } from '@manyun/ticket.model.ticket';
import {
  CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH,
  CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH,
  RISK_CHECK_TASK_CREATE_ROUTE_PATH,
  generateInspectionPlanDetailRoutePath,
  generateInspectionTaskConfigurationMutatorRoutePath,
  generateInventoryPlanDetailRoutePath,
  generateInventoryTaskConfigurationMutatorRoutePath,
  generateMaintainPlanDetailRoutePath,
  generateMaintainTaskConfigurationMutatorRoutePath,
  generateRiskCheckTaskConfigurationMutatorRoutePath,
  generateRiskCheckTaskDetailRoutePath,
} from '@manyun/ticket.route.ticket-routes';
import { exportTicketTask } from '@manyun/ticket.service.export-ticket-task';
import { FiltersTicketsModal } from '@manyun/ticket.ui.filters-tickets-modal';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';
import { generateSpecificDataByTaskType } from '@manyun/ticket.util.task-utils';

export type RiskCheckTasksTableProps = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  searchParams: any;
  planType: PlanType;
  onSuccess?: () => void;
} & TableProps<PlanJSON>;
export function RiskCheckTasksTable({
  planType,
  searchParams,
  onSuccess,
  ...rest
}: RiskCheckTasksTableProps) {
  const history = useHistory();
  const [exportLoading, setExportLoading] = useState(false);

  const [authorized] = useAuthorized({ checkByCode: 'element_risk-check-task-create' });
  const [updateTaskStatus, { loading: updateTaskStatusLoading }] = useBatchUpdateTaskStatus({
    onCompleted(data) {
      if (!data.batchUpdateTaskStatus?.success) {
        message.error(data.batchUpdateTaskStatus?.message);
        return;
      }
      message.success('设置成功');
      onSuccess && onSuccess();
    },
  });

  const [columns, setColumns] = useState<Array<BasicColumnType<PlanJSON>>>([
    {
      title: '任务名称',
      dataIndex: 'name',
      disabled: true,
      render: (_, record) => {
        return (
          <Typography.Text
            style={{ width: 170, color: `var(--${prefixCls}-primary-color)` }}
            ellipsis={{ tooltip: true }}
          >
            <Link
              target="_blank"
              to={() => {
                switch (planType) {
                  case PlanType.MaintenancePlan:
                    return generateMaintainPlanDetailRoutePath({
                      id: String(record.id),
                      name: record.name,
                    });
                  case PlanType.InspectionPlan:
                    return generateInspectionPlanDetailRoutePath({
                      id: String(record.id),
                      name: record.name,
                    });
                  case PlanType.InventoryPlan:
                    return generateInventoryPlanDetailRoutePath({
                      id: String(record.id),
                      name: record.name,
                    });
                  case PlanType.RiskCheckPlan:
                    return generateRiskCheckTaskDetailRoutePath({
                      id: String(record.id),
                    });
                  case PlanType.DrillPlan:
                    return '';
                  case PlanType.RiskCheckTicket:
                    return '';
                }
              }}
            >
              {record.name}
            </Link>
          </Typography.Text>
        );
      },
    },
    {
      title: `${generateSpecificDataByTaskType(planType).prefix}类型`,
      dataIndex: 'manageType',
      render: (_, record) => record.manageType,
    },
    {
      title: `${generateSpecificDataByTaskType(planType).columnTitle}数量`,
      dataIndex: 'jobItemNum',
      render: (_, record) => record.mopCount,
    },

    {
      title: '上次运行时间',
      dataIndex: 'triggerTime',
      render: (_, record) => Plan.fromJSON(record).getFormattedLastExecuteAt(),
      sorter: true,
    },
    {
      title: '运行结果',
      dataIndex: 'totalNum',
      render: (_, { lastExecuteResult }) =>
        lastExecuteResult?.length ? (
          <FiltersTicketsModal
            title="上次创建的工单记录"
            btnText={lastExecuteResult.length + ''}
            taskNos={lastExecuteResult}
            unusedStatusOptions={[
              BackendTaskStatus.INIT,
              BackendTaskStatus.CLOSE_APPROVER,
              BackendTaskStatus.UNDO,
            ]}
          />
        ) : (
          0
        ),
    },
    {
      dataIndex: 'finishNum',
      title: '已关单',
      render: (_, { finishTaskNoList }) =>
        Array.isArray(finishTaskNoList) ? finishTaskNoList.length : '--',
    },
    {
      dataIndex: 'unFinishNum',
      title: '未关单',
      render: (_, { unFinishTaskNoList }) =>
        Array.isArray(unFinishTaskNoList) ? unFinishTaskNoList.length : '--',
    },
    {
      title: 'SLA',
      dataIndex: 'slaInfo',
      render: (_, { jobSla }) =>
        jobSla ? (
          <TicketSlaText
            unit="SECOND"
            taskSla={0}
            delay={jobSla}
            effectTime={null}
            endTime={null}
          />
        ) : (
          '不限'
        ),
    },
    {
      title: '启用状态',
      dataIndex: 'status',
      render: (_, record) => {
        const isTaskActivated = record.isActivated === PlanStatus.On;
        const titlePrefix = isTaskActivated ? '禁用' : '启用';
        return (
          <Popconfirm
            key="status"
            title={`${titlePrefix}该风险检查任务?`}
            okButtonProps={{ loading: updateTaskStatusLoading }}
            okText={`确认${titlePrefix}`}
            onConfirm={async () => {
              await updateTaskStatus({
                variables: {
                  ids: [record.id],
                  status: isTaskActivated ? PlanStatus.Off : PlanStatus.On,
                },
              });
            }}
          >
            <Switch checked={isTaskActivated} />
          </Popconfirm>
        );
      },
    },
    {
      title: '创建人/owner',
      dataIndex: 'creatorName',
      render: (_, { creator }) => creator.name,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtCreate',
      render: (_, record) => Plan.fromJSON(record).getFormattedCreatedAt(),
      sorter: true,
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      show: false,
      render: (_, record) => Plan.fromJSON(record).getFormattedModifiedAt(),
      sorter: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 140,
      disabled: true,
      fixed: 'right',
      render: (_, record) => {
        return <Operation record={record} planType={planType} onSuccess={onSuccess} />;
      },
    },
  ]);

  const handleFileExport = async (type: string, columns: Array<BasicColumnType<PlanJSON>>) => {
    let params = { jobTypeList: ['SCH_RISK'], subJobType: 'SCH_RISK' };
    if (type === 'filtered') {
      params = searchParams;
    }
    setExportLoading(true);
    const { error, data } = await exportTicketTask({
      ...params,
      includeColumnFiledNames: columns
        .filter(item => item.show !== false && item.dataIndex !== 'action')
        .map(item => item.dataIndex) as string[],
    });
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  };
  return (
    <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
      <Space style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
        {authorized && (
          <Button
            type="primary"
            onClick={() => {
              history.push(RISK_CHECK_TASK_CREATE_ROUTE_PATH);
            }}
          >
            新建检查任务
          </Button>
        )}

        <Space size={0}>
          <FileExport
            text=""
            filename={`风险检查任务${dayjs().format('YYYY-MM-DD')}.xls`}
            disabled={exportLoading}
            data={type => {
              return handleFileExport(type, columns);
            }}
            showExportFiltered
          />
          <EditColumns
            uniqKey="INDEPENDENT_TICKETS_RISK_CHECK_TASK_KEY"
            defaultValue={columns}
            onChange={columns => {
              setColumns(columns);
            }}
          />
        </Space>
      </Space>

      <Table {...rest} columns={columns} />
    </Space>
  );
}

function Operation({
  record,
  planType,
  onSuccess,
}: {
  record: PlanJSON;
  planType: PlanType;
  onSuccess?: () => void;
}) {
  const [, { checkCode, checkUserId }] = useAuthorized();
  const [deleteTask, { loading: deleteLoading }] = useDeleteTask({
    onCompleted(data) {
      if (!data.deleteTask?.success) {
        message.error(data.deleteTask?.message ?? '删除失败');
        return;
      } else {
        message.success('删除成功');
        onSuccess && onSuccess();
      }
    },
  });
  const isLocalTask = record.manageType === '属地';
  const isCreator = checkUserId(record.creator.id);
  const hasLocalPermission = checkCode('element_risk-check-task-operation');
  return (isCreator && !isLocalTask) || (isLocalTask && hasLocalPermission) ? (
    <Space>
      <Link
        to={() => {
          switch (planType) {
            case PlanType.MaintenancePlan:
              return generateMaintainTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'edit',
              });
            case PlanType.InspectionPlan:
              return generateInspectionTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'edit',
              });

            case PlanType.InventoryPlan:
              return generateInventoryTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'edit',
              });
            case PlanType.RiskCheckPlan:
              return generateRiskCheckTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'edit',
              });
            case PlanType.DrillPlan:
              return '';
            case PlanType.RiskCheckTicket:
              return '';
          }
        }}
      >
        编辑
      </Link>

      <Link
        to={() => {
          switch (planType) {
            case PlanType.MaintenancePlan:
              return generateMaintainTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'copy',
              });
            case PlanType.InspectionPlan:
              return generateInspectionTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'copy',
              });
            case PlanType.InventoryPlan:
              return generateInventoryTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'copy',
              });
            case PlanType.RiskCheckPlan:
              return generateRiskCheckTaskConfigurationMutatorRoutePath({
                id: String(record.id),
                mode: 'copy',
              });
            case PlanType.DrillPlan:
              return '';
            case PlanType.RiskCheckTicket:
              return '';
          }
        }}
      >
        复制
      </Link>

      <Popconfirm
        key="remove"
        title="删除该风险检查任务?"
        okButtonProps={{ loading: deleteLoading }}
        okText="确认删除"
        onConfirm={async () => {
          await deleteTask({ variables: { id: record.id } });
        }}
      >
        <Button compact type="link" disabled={record.isActivated === PlanStatus.On}>
          删除
        </Button>
      </Popconfirm>
    </Space>
  ) : (
    '--'
  );
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
function generatePath(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH;
    case PlanType.InspectionPlan:
      return CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH;
    case PlanType.InventoryPlan:
      return CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH;
    default:
      return '';
  }
}
// eslint-disable-next-line @typescript-eslint/no-unused-vars
function generateTicketType(planType: PlanType) {
  switch (planType) {
    case PlanType.MaintenancePlan:
      return 'MAINTENANCE';
    case PlanType.InspectionPlan:
      return 'INSPECTION';
    case PlanType.InventoryPlan:
      return 'INVENTORY';
    default:
      return '';
  }
}
