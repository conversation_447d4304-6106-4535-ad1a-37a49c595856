import React from 'react';

import type { Moment } from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import type { QueryFilterProps } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';

export type FiltersFormValues = {
  name?: string;
  jobTypeList?: string[];
  subJobType?: string;
  blockGuidList?: string;
  periodUnit?: string;
  isInvalid?: string;
  taskStatus?: string;
  creatorId?: { value: string; label: string };
  manageType?: string;
  createdTimeRange?: Moment[];
  updateTimeRange?: Moment[];
};

export type RiskCheckTasksFilterProps = {
  onChange: (params?: FiltersFormValues) => void;
} & Omit<QueryFilterProps<FiltersFormValues>, 'form' | 'items' | 'defaultExpanded'>;

export function RiskCheckTasksFilter({ onChange, ...rest }: RiskCheckTasksFilterProps) {
  const [form] = Form.useForm();
  const items = [
    {
      label: '任务名称',
      name: 'name',
      control: <Input allowClear />,
    },
    {
      label: '任务类型',
      name: 'manageType',
      control: (
        <Select
          allowClear
          options={[
            { label: '属地', value: 'LOCAL' },
            { label: '通用', value: 'GOC' },
          ]}
        />
      ),
    },
    {
      label: '创建人',
      name: 'creatorId',
      control: <UserSelect allowClear />,
    },
    {
      label: '创建时间',
      name: 'createdTimeRange',
      span: 2,
      control: <DatePicker.RangePicker format="YYYY-MM-DD" allowClear />,
    },
    {
      label: '更新时间',
      name: 'updateTimeRange',
      span: 2,
      control: <DatePicker.RangePicker format="YYYY-MM-DD" allowClear />,
    },
  ];

  return (
    <Card>
      <QueryFilter<FiltersFormValues>
        {...rest}
        form={form}
        items={items}
        onSearch={filterFormValues => {
          onChange(filterFormValues);
        }}
        onReset={() => {
          form.resetFields();
          onChange();
        }}
      />
    </Card>
  );
}
