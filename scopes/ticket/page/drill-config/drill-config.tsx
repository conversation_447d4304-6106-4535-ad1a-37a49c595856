/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-14
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { useLazyDrillConfig } from '@manyun/ticket.gql.client.tickets';
import { CONFIG_RANGE_TEXT, ConfigRangeType } from '@manyun/ticket.model.task';
import {
  generateDrillConfigCopyLocation,
  generateDrillConfigEditLocation,
} from '@manyun/ticket.route.ticket-routes';
import { DrillPlanTable } from '@manyun/ticket.ui.drill-plan-table';

export function DrillConfig() {
  const history = useHistory();
  const { id } = useParams<{ id: string }>();
  const [getDrillConfig, { loading, data }] = useLazyDrillConfig();
  const drillConfig = data?.drillConfig?.data;

  const fetchDrillConfig = useCallback(() => {
    if (!id) {
      return;
    }
    getDrillConfig({ variables: { id: Number(id) } });
  }, [getDrillConfig, id]);

  useEffect(() => {
    fetchDrillConfig();
  }, [fetchDrillConfig]);

  return loading ? (
    <Skeleton active />
  ) : (
    <Space style={{ marginBottom: 24 }} direction="vertical" size={16}>
      <Container style={{ padding: '24px 24px 8px' }}>
        <Space direction="vertical" size={16}>
          <Typography.Title level={5} showBadge>
            基本信息
          </Typography.Title>
          <Descriptions column={4} contentStyle={{ overflow: 'hidden' }}>
            <Descriptions.Item label="演练方案名称">
              <Typography.Text ellipsis={{ tooltip: true }}>
                {drillConfig?.excName ?? '--'}
              </Typography.Text>
            </Descriptions.Item>
            <Descriptions.Item label="适用范围">
              {drillConfig?.effectType === ConfigRangeType.All
                ? CONFIG_RANGE_TEXT['ALL']
                : drillConfig?.blockGuid ?? '--'}
            </Descriptions.Item>
            <Descriptions.Item label="专业类型">
              {drillConfig?.excMajor ? (
                <MetaTypeText code={drillConfig.excMajor} metaType={MetaType.EXC_MAJOR} />
              ) : (
                '--'
              )}
            </Descriptions.Item>
            <Descriptions.Item label="等级">
              {drillConfig?.excLevel ? (
                <MetaTypeText code={drillConfig.excLevel} metaType={MetaType.EXC_LEVEL} />
              ) : (
                '--'
              )}
            </Descriptions.Item>
          </Descriptions>
        </Space>
      </Container>
      <Container style={{ padding: 24 }}>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Typography.Title level={5} showBadge>
            方案详情
          </Typography.Title>
          <DrillPlanTable dataSource={drillConfig?.exercisePlanStepList ?? []} />
        </Space>
      </Container>
      <FooterToolBar>
        <Space>
          <Button
            type="primary"
            onClick={() =>
              history.push(
                generateDrillConfigEditLocation({
                  id: id.toString(),
                })
              )
            }
          >
            编辑
          </Button>
          <Button
            onClick={() =>
              history.push(
                generateDrillConfigCopyLocation({
                  id: id.toString(),
                })
              )
            }
          >
            复制
          </Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}
