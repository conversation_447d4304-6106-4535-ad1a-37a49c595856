/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-18
 *
 * @packageDocumentation
 */
import React from 'react';
import { useHistory } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';

import { PlanType } from '@manyun/ticket.model.task';
import { DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';
import { TicketTaskMutator } from '@manyun/ticket.ui.ticket-task-mutator';

export type DrillTaskCreateProps = {};

export function DrillTaskCreate() {
  const history = useHistory();

  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }} color="default">
      <TicketTaskMutator
        planType={PlanType.DrillPlan}
        mode="create"
        unusedFormItems={['manageType', 'splitors', 'fileInfoList']}
        onCancel={() => {
          history.push(DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH);
        }}
        onSuccess={() => {
          history.push(DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH);
        }}
      />
    </Container>
  );
}
