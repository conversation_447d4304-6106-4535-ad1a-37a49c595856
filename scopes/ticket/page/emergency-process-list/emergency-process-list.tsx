import dayjs from 'dayjs';
import omit from 'lodash.omit';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import {
  useDeleteEmergencyProcess,
  useLazyEmergencyProcess,
} from '@manyun/ticket.gql.client.tickets';
import type { EmergencyProcessData, EmergencyProcessQ } from '@manyun/ticket.gql.client.tickets';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import {
  EMERGENCY_PROCEE_CREATE_ROUTE_PATH,
  generateEmergencyProcessEditLocation,
  generateEmergencyProcessLocation,
} from '@manyun/ticket.route.ticket-routes';

import FilterFormPopover from './components/filter-form-popver/filter-form-popver';

type SearchField = EmergencyProcessQ & {
  createTime?: [Moment, Moment] | null;
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';

export function EmergencyProcessList() {
  const locales = useMemo(() => getRiskRegisterLocales(), []);
  const [, { checkCode }] = useAuthorized();
  const [form] = Form.useForm();
  const isEdit = checkCode('element_ticket_emergency-process-operate');

  const [getEmergencyProcess, { data, loading }] = useLazyEmergencyProcess({
    onError(error) {
      if (error) {
        message.error(error.message);
      }
    },
  });
  const { search } = useLocation();

  const { pageNum, pageSize, ...restDefaultFields } = getLocationSearchMap<SearchField>(search, {
    parseNumbers: true,
  });
  const [fields, setFields] = useState<SearchField>({
    ...restDefaultFields,
    createTime:
      restDefaultFields.createBeginTime && restDefaultFields.createEndTime
        ? [moment(restDefaultFields.createBeginTime), moment(restDefaultFields.createEndTime)]
        : undefined,
    pageNum: pageNum ?? 1,
    pageSize: pageSize ?? 10,
  });

  const history = useHistory();

  const [deleteEmergencyProcess, { loading: deleteLoading }] = useDeleteEmergencyProcess({
    onCompleted: data => {
      if (data?.deleteEmergencyProcess?.success) {
        reloadData('delete');
      } else {
        message.error(data?.deleteEmergencyProcess?.message);
      }
    },
  });

  const reloadData = useCallback(
    (action?: 'delete') => {
      if (action === 'delete') {
        setFields(pre => {
          return {
            ...pre,
            pageNum:
              ((pre.pageNum ?? 1) - 1) * (pre.pageSize ?? 10) + 1 ===
                data?.emergencyProcess?.total && (pre.pageNum ?? 1) > 1
                ? (pre.pageNum ?? 1) - 1
                : pre.pageNum,
          };
        });
      } else {
        setFields(pre => ({ ...pre }));
      }
    },
    [data?.emergencyProcess.total]
  );

  useEffect(() => {
    getEmergencyProcess({ variables: { query: { pageNum: 1, pageSize: 10 } } });
  }, [getEmergencyProcess]);

  useEffect(() => {
    const params = {
      ...omit(fields, 'createTime'),
      createBeginTime: fields.createTime?.[0]
        ? fields.createTime[0].startOf('day').valueOf()
        : null,
      createEndTime: fields.createTime?.[1] ? fields.createTime[1].endOf('day').valueOf() : null,
    };

    form.setFieldsValue(fields);
    getEmergencyProcess({
      variables: {
        query: params,
      },
    });
    setLocationSearch({
      ...omit(params, 'createTime'),
    });
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields, getEmergencyProcess]);

  return (
    <Card
      bordered={false}
      headStyle={{ borderBottom: 0 }}
      bodyStyle={{ padding: '0 24px' }}
      title={
        <Typography.Title showBadge level={5}>
          应急流程
        </Typography.Title>
      }
    >
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Space direction="horizontal" style={{ width: '100%', display: 'flex' }}>
          {isEdit && (
            <Button type="primary" onClick={() => history.push(EMERGENCY_PROCEE_CREATE_ROUTE_PATH)}>
              新建应急流程
            </Button>
          )}
          <Input.Search
            placeholder="请输入方案名称搜索"
            allowClear
            value={fields.keyword || ''}
            style={{ width: 248 }}
            onChange={e => {
              setFields({
                ...fields,
                pageNum: 1,
                pageSize: fields.pageSize,
                keyword: e.target.value,
              });
            }}
          />
          <FilterFormPopover
            values={omit(fields, 'pageNum', 'pageSize', 'keyword')}
            onSearch={formValues => {
              setFields(pre => ({
                ...pre,
                pageNum: 1,
                pageSize: fields.pageSize,
                ...formValues,
              }));
            }}
          />
        </Space>
        <Table<EmergencyProcessData>
          rowKey="emergencyId"
          scroll={{ x: 'max-content' }}
          loading={loading}
          dataSource={data?.emergencyProcess?.data ?? []}
          columns={[
            {
              title: '应急流程名称',
              dataIndex: 'name',
              fixed: 'left',
              render: (_, { name, emergencyId }) => {
                if (emergencyId) {
                  return (
                    <Typography.Text ellipsis={{ tooltip: name }} style={{ width: 600 }}>
                      <Link
                        target="_blank"
                        to={generateEmergencyProcessLocation({
                          id: encodeURIComponent(emergencyId),
                        })}
                      >
                        {name}
                      </Link>
                    </Typography.Text>
                  );
                }
                return '--';
              },
            },
            {
              title: '适用机房/楼',
              dataIndex: 'blockGuid',
            },
            {
              title: '专业分类',
              dataIndex: 'categoryName',
            },
            {
              title: '创建时间',
              dataIndex: 'gmtCreate',
              render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '--'),
            },
            {
              title: '创建人',
              dataIndex: 'createUserId',
              render: (_, { createUserId, createUserName }) => (
                <UserLink external userId={createUserId} userName={createUserName} />
              ),
            },
            {
              title: locales.operation,
              dataIndex: '_action',
              fixed: 'right',
              disabled: true,
              render: (_, record) => {
                if (!isEdit) {
                  return '--';
                }
                return (
                  <Space size={16}>
                    <Link
                      target="_blank"
                      to={generateEmergencyProcessEditLocation({
                        id: encodeURIComponent(record.emergencyId),
                      })}
                    >
                      编辑
                    </Link>

                    <Popconfirm
                      title="确认删除此模版？删除后不可恢复"
                      style={{ width: 290 }}
                      okText="确认删除"
                      placement="topRight"
                      okButtonProps={{ disabled: deleteLoading }}
                      onConfirm={() =>
                        deleteEmergencyProcess({
                          variables: { emergencyIdList: [record.emergencyId] },
                        })
                      }
                    >
                      <Button compact type="link" disabled={loading}>
                        删除
                      </Button>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
          pagination={{
            total: data?.emergencyProcess?.total ?? 0,
            current: fields.pageNum ?? 1,
            pageSize: fields.pageSize ?? 10,
          }}
          onChange={(pagination, _, sorter, { action }) => {
            if (action === 'paginate') {
              setFields(pre => ({
                ...pre,
                pageNum: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }
          }}
        />
      </Space>
    </Card>
  );
}
