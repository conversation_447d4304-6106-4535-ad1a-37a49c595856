import { FilterOutlined } from '@ant-design/icons';
import omit from 'lodash.omit';
import type { Moment } from 'moment';
import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

type SearchField = {
  blockGuid?: string | null;
  categoryCode?: string | null;
  createUserId?: number | null;
  createTime?: [Moment, Moment] | null;
};

export type FilterFormPopoverProps = {
  values?: SearchField;
  onSearch: (formValues: SearchField) => void;
};

export default function FilterFormPopover({ values, onSearch }: FilterFormPopoverProps) {
  const [visible, setVisible] = useState(false);
  const hasFilter = useDeepCompareMemo(() => {
    return values && Object.values(values).some(value => value !== undefined);
  }, [values]);

  const [form] = Form.useForm();

  const formItems = [
    {
      label: '适用楼栋',
      name: 'blockGuid',
      colSpan: 12,
      children: (
        <LocationTreeSelect
          authorizedOnly
          allowClear
          includeVirtualBlocks
          disabledTypes={['IDC']}
        />
      ),
    },
    {
      label: '专业分类',
      name: 'categoryCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          allowClear
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.EMERGENCY_CATEGORY}
          showArrow
        />
      ),
    },
    {
      label: '创建人',
      name: 'createUserId',
      colSpan: 12,
      children: <UserSelect allowClear labelInValue={false} />,
    },
    {
      label: '创建时间',
      name: 'createTime',
      colSpan: 12,
      children: <DatePicker.RangePicker format="YYYY-MM-DD HH:mm:ss" />,
    },
  ];

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => (
        <Card style={{ width: 544 }}>
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              {formItems.map(itemProps => {
                const { label, name, colSpan, children, ...rest } = itemProps;
                return (
                  <Col key={name} span={colSpan}>
                    <Form.Item label={label} name={name} {...rest}>
                      {children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Row justify="end">
              <Space>
                <Button
                  onClick={() => {
                    form.resetFields();
                    onSearch({
                      ...form.getFieldsValue(),
                      createBeginTime: undefined,
                      createEndTime: undefined,
                      updateBeginTime: undefined,
                      updateEndTime: undefined,
                    });
                    setVisible(false);
                  }}
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    const fields = form.getFieldsValue();
                    onSearch({
                      ...omit(fields),
                    });
                    setVisible(false);
                  }}
                >
                  搜索
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={_visible => {
        if (_visible === true && values) {
          form.setFieldsValue({
            ...values,
          });
        }
        setVisible(_visible);
      }}
    >
      <Tooltip title="高级搜索">
        <Button
          icon={<FilterOutlined />}
          ghost={hasFilter}
          type={hasFilter ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}
