import React from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { useStopDrillOrder } from '@manyun/ticket.gql.client.drill-order';

export type FinishModelProps = {
  excNo: string;
  open: boolean;
  onClose: () => void;
  onCallBack: () => void;
};
export function FinishModel({ excNo, open, onClose, onCallBack }: FinishModelProps) {
  const [form] = Form.useForm();

  const [transferDrillOrder, { loading }] = useStopDrillOrder({
    onCompleted(data) {
      if (!data.stopDrillOrder?.success) {
        message.error(data.stopDrillOrder?.message);
        return;
      }
      message.success('终止成功');
      onCallBack();
    },
  });

  const onSubmit = async () => {
    const values = await form.validateFields();
    const params = {
      execNo: excNo,
      stopReason: values.stopReason,
    };
    await transferDrillOrder({ variables: { params } });
    onClose();
  };
  return (
    <Modal
      width={520}
      title="异常终止"
      open={open}
      confirmLoading={loading}
      onOk={onSubmit}
      onCancel={onClose}
    >
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Alert
          message="是否终止演练？终止后工单将直接变为「已终止」状态，无法继续执行，请谨慎操作！"
          type="warning"
          showIcon
        />
        <Form form={form} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
          <Form.Item
            label="终止原因"
            name="stopReason"
            rules={[
              {
                required: true,
                message: '终止原因必填！',
              },
              {
                type: 'string',
                max: 300,
                message: '最多输入 300 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 390 }} />
          </Form.Item>
        </Form>
      </Space>
    </Modal>
  );
}
