import { UploadOutlined } from '@ant-design/icons';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import {
  useReviewDrillOrder,
  useWithoutReviewDrillOrder,
} from '@manyun/ticket.gql.client.drill-order';

export type ReplayModelProps = {
  excNo: string;
  open: boolean;
  onClose: () => void;
  onCallBack: () => void;
};
export function ReplayModel({ excNo, open, onClose, onCallBack }: ReplayModelProps) {
  const [form] = Form.useForm();
  const [reviewDrillOrder, { loading }] = useReviewDrillOrder({
    onCompleted(data) {
      if (!data.reviewDrillOrder?.success) {
        message.error(data.reviewDrillOrder?.message);
        return;
      }
      message.success('提交审批成功！');
      onCallBack();
    },
  });

  const [withoutReviewDrillOrder] = useWithoutReviewDrillOrder({
    onCompleted(data) {
      if (!data.withoutReviewDrillOrder?.success) {
        message.error(data.withoutReviewDrillOrder?.message);
        return;
      }
      message.success('提交审批成功！');
      onCallBack();
    },
  });

  const onSubmit = async () => {
    const values = await form.validateFields();
    const { reviewDesc, fileInfoList } = values;
    if (!reviewDesc && !fileInfoList) {
      message.error('复盘描述和复盘附件请至少填写一项!');
      return;
    }
    const params = {
      execNo: excNo,
      reviewDesc,
      fileInfoList: fileInfoList?.map((item: McUploadFileJSON) => McUploadFile.fromJSON(item)),
    };
    await reviewDrillOrder({ variables: { params } });
    onClose();
  };

  const fileInfoList = Form.useWatch('fileInfoList', form);

  return (
    <Modal
      width={720}
      title="演练复盘"
      open={open}
      footer={[
        <Button
          key="back"
          onClick={async () => {
            await withoutReviewDrillOrder({
              variables: {
                execNo: excNo,
              },
            });
            onClose();
          }}
        >
          无需复盘，提交评审
        </Button>,
        <Button key="submit" type="primary" loading={loading} onClick={onSubmit}>
          提交复盘
        </Button>,
      ]}
      onOk={onSubmit}
      onCancel={onClose}
    >
      <Form form={form} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
        <Form.Item
          label="复盘描述"
          name="reviewDesc"
          rules={[
            {
              max: 1000,
              message: '最多输入 1000 个字符！',
            },
          ]}
        >
          <Input.TextArea style={{ width: 598 }} placeholder="请输入" />
        </Form.Item>

        <Form.Item
          label="复盘附件"
          name="fileInfoList"
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
        >
          <McUpload
            key="upload"
            accept=".png,.jpg,.jpeg,.doc,.image,.xls,.xlsx,.doc,.docx,.pdf"
            maxFileSize={20}
            maxCount={5}
          >
            {fileInfoList && fileInfoList?.length >= 5 ? null : (
              <>
                <Button icon={<UploadOutlined />}>上传</Button>
                <Typography.Text type="secondary">
                  支持扩展名: png、jpg、jpeg、doc、image、xls、xlsx、doc、docx、pdf
                </Typography.Text>
              </>
            )}
          </McUpload>
        </Form.Item>
      </Form>
    </Modal>
  );
}
