import React from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useApplyCommentDrillOrder } from '@manyun/ticket.gql.client.drill-order';

export type ReviewerModelProps = {
  blockGuid: string;
  excNo: string;
  open: boolean;
  onClose: () => void;
  onCallBack: () => void;
};
export function ReviewerModel({ blockGuid, excNo, open, onClose, onCallBack }: ReviewerModelProps) {
  const [form] = Form.useForm();

  const [applyCommentDrillOrder, { loading }] = useApplyCommentDrillOrder({
    onCompleted(data) {
      if (!data.applyCommentDrillOrder?.success) {
        message.error(data.applyCommentDrillOrder?.message);
        return;
      }
      message.success('选择点评人成功');
      onCallBack();
    },
  });

  const onSubmit = async () => {
    const values = await form.validateFields();
    const params = {
      execNo: excNo,
      commentUserId: values.user.id,
      commentUserName: values.user.name,
    };
    await applyCommentDrillOrder({ variables: { params } });
    onClose();
  };

  return (
    <Modal
      width={520}
      title="选择点评人"
      open={open}
      confirmLoading={loading}
      onOk={onSubmit}
      onCancel={onClose}
    >
      <Form form={form} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
        <Form.Item
          label="选择点评人"
          name="user"
          rules={[
            {
              required: true,
              message: '点评人必填',
            },
          ]}
        >
          <UserSelect style={{ width: 216 }} blockGuid={blockGuid} allowClear />
        </Form.Item>
      </Form>
    </Modal>
  );
}
