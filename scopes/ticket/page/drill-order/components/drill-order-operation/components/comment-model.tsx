import { UploadOutlined } from '@ant-design/icons';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { useCommentDrillOrder } from '@manyun/ticket.gql.client.drill-order';

export type CommentModelProps = {
  excNo: string;
  open: boolean;
  onClose: () => void;
  onCallBack: () => void;
};
export function CommentModel({ excNo, open, onClose, onCallBack }: CommentModelProps) {
  const [form] = Form.useForm();
  const [commentDrillOrder, { loading }] = useCommentDrillOrder({
    onCompleted(data) {
      if (!data.commentDrillOrder?.success) {
        message.error(data.commentDrillOrder?.message);
        return;
      }
      message.success('点评成功');
      onCallBack();
    },
  });

  const onSubmit = async () => {
    const values = await form.validateFields();
    const { commentContent, fileInfoList } = values;

    const params = {
      execNo: excNo,
      commentContent,
      fileInfoList: fileInfoList?.map((item: McUploadFileJSON) => McUploadFile.fromJSON(item)),
    };
    await commentDrillOrder({ variables: { params } });
    onClose();
  };

  const fileInfoList = Form.useWatch('fileInfoList', form);

  return (
    <Modal
      width={720}
      title="点评"
      open={open}
      confirmLoading={loading}
      onOk={onSubmit}
      onCancel={onClose}
    >
      <Form form={form} labelCol={{ xl: 4 }} wrapperCol={{ xl: 20 }}>
        <Form.Item
          label="点评内容"
          name="commentContent"
          rules={[
            {
              required: true,
              message: '点评内容必填！',
            },
            {
              type: 'string',
              max: 1000,
              message: '最多输入 1000 个字符！',
            },
          ]}
        >
          <Input.TextArea style={{ width: 598 }} />
        </Form.Item>

        <Form.Item
          label="点评附件"
          name="fileInfoList"
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
        >
          <McUpload
            key="upload"
            accept=".png,.jpg,.jpeg,.doc,.image,.xls,.xlsx,.doc,.docx,.pdf"
            maxFileSize={20}
            maxCount={5}
          >
            {fileInfoList && fileInfoList?.length >= 5 ? null : (
              <>
                <Button icon={<UploadOutlined />}>上传</Button>
                <Typography.Text type="secondary">
                  支持扩展名: png、jpg、jpeg、doc、image、xls、xlsx、doc、docx、pdf
                </Typography.Text>
              </>
            )}
          </McUpload>
        </Form.Item>
      </Form>
    </Modal>
  );
}
