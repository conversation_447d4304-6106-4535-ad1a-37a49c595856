import React, { useCallback, useState } from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import type { DrillOrderInfo } from '@manyun/ticket.gql.client.drill-order';
import {
  useCompleteDrillOrder,
  useStartDrillOrder,
  useTakeDrillOrder,
  useWithoutReviewDrillOrder,
} from '@manyun/ticket.gql.client.drill-order';

import { CommentModel } from './components/comment-model';
import { FinishModel } from './components/finish-model';
import { ReplayModel } from './components/replay-model';
import { ReviewerModel } from './components/reviewer-model';
import { TransferModel } from './components/transfer-model';

export type DrillOrderOperationProps = {
  drillOrderInfo: DrillOrderInfo;
  approvalBaseInfo?: BpmInstance;
  onCallBack: (params?: { type: string }) => void;
};
export function DrillOrderOperation({
  drillOrderInfo,
  approvalBaseInfo,
  onCallBack,
}: DrillOrderOperationProps) {
  const { userId } = getUserInfo();
  const [takeDrillOrder, { loading: takeOverLoading }] = useTakeDrillOrder({
    onCompleted(data) {
      if (!data.takeDrillOrder?.success) {
        message.error(data.takeDrillOrder?.message);
        return;
      }
      message.success('接单成功！');
      onCallBack();
    },
  });

  const [startDrillOrder, { loading: startDrillLoading }] = useStartDrillOrder({
    onCompleted(data) {
      if (!data.startDrillOrder?.success) {
        message.error(data.startDrillOrder?.message);
        return;
      }
      message.success('开始演练成功！');
      onCallBack();
    },
  });

  const [withoutReviewDrillOrder, { loading: withoutReviewDrillLoading }] =
    useWithoutReviewDrillOrder({
      onCompleted(data) {
        if (!data.withoutReviewDrillOrder?.success) {
          message.error(data.withoutReviewDrillOrder?.message);
          return;
        }
        message.success('提交审批成功！');
        onCallBack();
      },
    });

  const [completeDrillOrder, { loading: completeDrillLoading }] = useCompleteDrillOrder({
    onCompleted(data) {
      if (!data.completeDrillOrder?.success) {
        message.error(data.completeDrillOrder?.message);
        return;
      }
      message.success('完成演练成功！');
      onCallBack();
    },
  });

  const [transferModelOpen, setTransferModelOpen] = useState(false);
  const [finishModelOpen, setFinishModelOpen] = useState(false);
  const [replayModelOpen, setReplayModelOpen] = useState(false);
  const [commentModelOpen, setCommentModelOpen] = useState(false);
  const [reviewerModelOpen, setReviewerModelOpen] = useState(false);

  const onReceivingOrder = useCallback(() => {
    takeDrillOrder({
      variables: {
        execNoList: [drillOrderInfo.excNo],
      },
    });
  }, [drillOrderInfo.excNo, takeDrillOrder]);

  const onStartDrill = useCallback(() => {
    startDrillOrder({
      variables: {
        execNo: drillOrderInfo.excNo,
      },
    });
  }, [drillOrderInfo.excNo, startDrillOrder]);

  const onCompleteDrill = useCallback(() => {
    completeDrillOrder({
      variables: {
        execNo: drillOrderInfo.excNo,
      },
    });
  }, [completeDrillOrder, drillOrderInfo.excNo]);

  const onWithoutReview = useCallback(() => {
    withoutReviewDrillOrder({
      variables: {
        execNo: drillOrderInfo.excNo,
      },
    });
  }, [withoutReviewDrillOrder, drillOrderInfo.excNo]);

  const onApprovalCallBack = async (params?: { type: string }) => {
    if (params && ['revoke', 'refuse', 'pass'].includes(params.type)) {
      const timer = setTimeout(() => {
        if (drillOrderInfo.taskStatus !== 'APPROVE_TO_CLOSE') {
          window.clearTimeout(timer);
        } else {
          onCallBack();
        }
      }, 1500);
    } else {
      onCallBack();
    }
  };

  const operator = useDeepCompareMemo(() => {
    const havePermission = userId === drillOrderInfo.taskAssignee;
    const haveWaitAacceptPermission =
      (drillOrderInfo.assigneeList?.map(item => item.id).includes(userId) ||
        !drillOrderInfo.assigneeList?.length) &&
      drillOrderInfo.taskStatus === 'WAIT_ACCEPT';

    if (haveWaitAacceptPermission) {
      return (
        <Button type="primary" loading={takeOverLoading} onClick={onReceivingOrder}>
          接单
        </Button>
      );
    }

    if (!havePermission) {
      return undefined;
    }

    if (drillOrderInfo.taskStatus === 'ALREADY_ACCEPT' && !drillOrderInfo.startTime) {
      return (
        <Space>
          <Button type="primary" loading={startDrillLoading} onClick={onStartDrill}>
            开始演练
          </Button>
          <Button onClick={() => setTransferModelOpen(true)}>转交</Button>
        </Space>
      );
    }
    if (drillOrderInfo.taskStatus === 'ALREADY_ACCEPT' && drillOrderInfo.startTime) {
      const completeDisabled = drillOrderInfo.exerciseOrderStepItemDoList.some(
        item => !item.finishTime
      );
      return (
        <Space>
          <Button
            type="primary"
            disabled={completeDisabled}
            loading={completeDrillLoading}
            onClick={onCompleteDrill}
          >
            完成演练
          </Button>
          <Button onClick={() => setFinishModelOpen(true)}>异常终止</Button>
        </Space>
      );
    }

    if (drillOrderInfo.taskStatus === 'WAIT_REVIEW') {
      return (
        <Space>
          <Button type="primary" onClick={() => setReplayModelOpen(true)}>
            演练复盘
          </Button>
          <Button loading={withoutReviewDrillLoading} onClick={onWithoutReview}>
            无需复盘，提交评审
          </Button>
        </Space>
      );
    }
    if (drillOrderInfo.taskStatus === 'WAIT_COMMENT' && !drillOrderInfo.commitCommentTime) {
      return (
        <Button type="primary" onClick={() => setReviewerModelOpen(true)}>
          选择点评人
        </Button>
      );
    }
    if (drillOrderInfo.taskStatus === 'WAIT_COMMENT' && drillOrderInfo.commitCommentTime) {
      return (
        <Button type="primary" onClick={() => setCommentModelOpen(true)}>
          去点评
        </Button>
      );
    }
    return undefined;
  }, [
    drillOrderInfo.assigneeList,
    drillOrderInfo.commitCommentTime,
    drillOrderInfo.exerciseOrderStepItemDoList,
    drillOrderInfo.startTime,
    drillOrderInfo.taskAssignee,
    drillOrderInfo.taskStatus,
    onCompleteDrill,
    onReceivingOrder,
    onStartDrill,
    onWithoutReview,
    takeOverLoading,
    startDrillLoading,
    completeDrillLoading,
    withoutReviewDrillLoading,
    userId,
  ]);

  return (
    <>
      {approvalBaseInfo && drillOrderInfo.taskStatus === 'APPROVE_TO_CLOSE' && (
        <ApprovalOperationButtons baseInfo={approvalBaseInfo} getDetail={onApprovalCallBack} />
      )}
      {operator && <FooterToolBar>{operator}</FooterToolBar>}
      <TransferModel
        excNo={drillOrderInfo.excNo}
        blockGuid={drillOrderInfo.blockGuid}
        open={transferModelOpen}
        onClose={() => setTransferModelOpen(false)}
        onCallBack={onCallBack}
      />
      <FinishModel
        excNo={drillOrderInfo.excNo}
        open={finishModelOpen}
        onClose={() => setFinishModelOpen(false)}
        onCallBack={onCallBack}
      />
      <ReviewerModel
        excNo={drillOrderInfo.excNo}
        blockGuid={drillOrderInfo.blockGuid}
        open={reviewerModelOpen}
        onClose={() => setReviewerModelOpen(false)}
        onCallBack={onCallBack}
      />
      <CommentModel
        excNo={drillOrderInfo.excNo}
        open={commentModelOpen}
        onClose={() => setCommentModelOpen(false)}
        onCallBack={onCallBack}
      />
      <ReplayModel
        excNo={drillOrderInfo.excNo}
        open={replayModelOpen}
        onClose={() => setReplayModelOpen(false)}
        onCallBack={onCallBack}
      />
    </>
  );
}
