import { UploadOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import React, { useEffect } from 'react';

import { Avatar } from '@manyun/base-ui.ui.avatar';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import {
  useCreateDrillOrderRemark,
  useLazyDrillOrderStep,
} from '@manyun/ticket.gql.client.drill-order';

import styles from './step-remark-model.module.less';

export type StepRemarkMode = 'edit' | 'read';
export type StepRemarkModelProps = {
  excNo: string;
  itemId: number;
  mode: StepRemarkMode;
  open: boolean;
  onClose: () => void;
  onCallBack: () => void;
};
export function StepRemarkModel({
  excNo,
  itemId,
  mode,
  open,
  onClose,
  onCallBack,
}: StepRemarkModelProps) {
  const [form] = Form.useForm();

  const [getDrillOrderStep, { data: drillOrderStepRemarkRes }] = useLazyDrillOrderStep();

  useEffect(() => {
    if (open) {
      (async () => {
        await getDrillOrderStep({ variables: { itemId } });
      })();
    }
  }, [getDrillOrderStep, itemId, open]);

  const [createDrillOrderRemark, { loading }] = useCreateDrillOrderRemark({
    onCompleted(data) {
      if (!data.createDrillOrderRemark?.success) {
        message.error(data.createDrillOrderRemark?.message);
        return;
      }
      message.success('备注成功！');
      onCallBack();
    },
  });

  const onSubmit = async () => {
    const values = await form.validateFields();
    const { remarkDesc, fileInfoList } = values;
    if (!remarkDesc && !fileInfoList) {
      message.error('备注和附件请至少填写一项!');
      return;
    }
    const params = {
      execNo: excNo,
      itemId,
      remarkDesc,
      fileInfoList: fileInfoList?.map((item: McUploadFileJSON) => McUploadFile.fromJSON(item)),
    };
    await createDrillOrderRemark({ variables: { params } });
    form.setFieldsValue({ remarkDesc: undefined, fileInfoList: [] });
    onClose();
  };

  const drillOrderStepRemarkList = drillOrderStepRemarkRes?.drillOrderStep?.data;

  const fileInfoList = Form.useWatch('fileInfoList', form);

  return (
    <Modal
      width={520}
      title="备注"
      open={open}
      footer={mode === 'edit' ? undefined : null}
      confirmLoading={loading}
      onOk={onSubmit}
      onCancel={onClose}
    >
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        {mode === 'read' && !drillOrderStepRemarkList?.length && <Empty />}
        {!!drillOrderStepRemarkList?.length && (
          <>
            {drillOrderStepRemarkList.map(item => {
              const firstLetter = item.creatorName.substring(0, 1);

              return (
                <Space key={item.id} style={{ width: '100%' }} direction="vertical">
                  <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                    <Space>
                      <Avatar
                        style={{ backgroundColor: 'var(--manyun-primary-color)' }}
                        shape="circle"
                        src={item.avatarUrl}
                      >
                        {firstLetter}
                      </Avatar>
                      <Typography.Text>{item.creatorName}</Typography.Text>
                    </Space>
                    <Typography.Text type="secondary">
                      {dayjs(item.gmtCreate).format('YYYY-MM-DD HH:mm:ss')}
                    </Typography.Text>
                  </div>
                  <Card
                    className={styles.stepRemarkCard}
                    bodyStyle={{ padding: 12 }}
                    bordered={false}
                  >
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                      <Typography.Text>{item.remark}</Typography.Text>
                      <McUpload
                        targetId={item.id}
                        targetType="DRILL_ORDER_ITEM_REMARK_FILE"
                        allowDelete={false}
                        disabled
                      />
                    </div>
                  </Card>
                </Space>
              );
            })}
          </>
        )}
        {mode === 'edit' && (
          <Form form={form}>
            <Form.Item
              label="备注"
              name="remarkDesc"
              rules={[
                {
                  type: 'string',
                  max: 300,
                  message: '最多输入 300 个字符！',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
            <Form.Item
              label="附件"
              name="fileInfoList"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <McUpload
                key="upload"
                accept=".png,.jpg,.jpeg,.doc,.image,.xls,.xlsx,.doc,.docx,.pdf"
                maxFileSize={20}
                maxCount={5}
              >
                {fileInfoList && fileInfoList?.length >= 5 ? null : (
                  <>
                    <Button icon={<UploadOutlined />}>上传</Button>
                    <Typography.Text type="secondary">
                      支持扩展名: png、jpg、jpeg、doc、image、xls、xlsx、doc、docx、pdf
                    </Typography.Text>
                  </>
                )}
              </McUpload>
            </Form.Item>
          </Form>
        )}
      </Space>
    </Modal>
  );
}
