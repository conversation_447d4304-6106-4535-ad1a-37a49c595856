import React from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Typography } from '@manyun/base-ui.ui.typography';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import type { DrillOrderInfo } from '@manyun/ticket.gql.client.drill-order';
import DrillOrderStatusText from '@manyun/ticket.ui.drill-order-status-text';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';
import type { TimeUnit } from '@manyun/ticket.util.sla';

const SYSTEM_ID = 0;
export type TicketInfoCardProps = { drillOrderInfo: DrillOrderInfo };
export function TicketInfoCard({ drillOrderInfo }: TicketInfoCardProps) {
  return (
    <Card>
      <Descriptions
        title={
          <Typography.Title showBadge level={5}>
            工单信息
          </Typography.Title>
        }
        column={4}
      >
        <Descriptions.Item label="楼栋">{drillOrderInfo.blockGuid}</Descriptions.Item>
        <Descriptions.Item label="专业类型">
          <MetaTypeText code={drillOrderInfo.excMajor} metaType={MetaType.EXC_MAJOR} />
        </Descriptions.Item>
        <Descriptions.Item label="等级">
          <MetaTypeText code={drillOrderInfo.excLevel} metaType={MetaType.EXC_LEVEL} />
        </Descriptions.Item>
        <Descriptions.Item label="状态">
          <DrillOrderStatusText status={drillOrderInfo.taskStatus} />
        </Descriptions.Item>
        <Descriptions.Item
          contentStyle={{ width: '72%', paddingRight: 48 }}
          label="工单标题"
          span={2}
        >
          <Typography.Text ellipsis={{ tooltip: drillOrderInfo.title }}>
            {drillOrderInfo.title}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item contentStyle={{ width: '72%', paddingRight: 48 }} label="说明">
          <Typography.Text ellipsis={{ tooltip: drillOrderInfo.desc }}>
            {drillOrderInfo.desc ?? '--'}
          </Typography.Text>
        </Descriptions.Item>
        <Descriptions.Item label="SLA计时/标准">
          <TicketSlaText
            taskSla={drillOrderInfo.taskSla}
            delay={drillOrderInfo.slaSeconds}
            unit={drillOrderInfo.unit as TimeUnit}
            effectTime={null}
            endTime={null}
            shouldLimitShow
          />
        </Descriptions.Item>
        <Descriptions.Item label="处理人">
          <UserLink userId={drillOrderInfo.taskAssignee} />
        </Descriptions.Item>
        <Descriptions.Item label="负责人">{drillOrderInfo.principalName ?? '--'}</Descriptions.Item>
        <Descriptions.Item label="创建人">
          {drillOrderInfo.creatorId === SYSTEM_ID ? '系统' : drillOrderInfo.creatorName ?? '--'}
        </Descriptions.Item>
        <Descriptions.Item label="附件">
          {drillOrderInfo.fileInfoList?.length ? (
            <SimpleFileList files={drillOrderInfo.fileInfoList}>
              <Typography.Link>查看 </Typography.Link>
            </SimpleFileList>
          ) : (
            '--'
          )}
        </Descriptions.Item>
      </Descriptions>
    </Card>
  );
}
