import React from 'react';

import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { User as UserAvatar } from '@manyun/auth-hub.ui.user';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import type { DrillOrderInfo } from '@manyun/ticket.gql.client.drill-order';

export type CommentReviewCardProps = { drillOrderInfo: DrillOrderInfo };
export function CommentReviewCard({ drillOrderInfo }: CommentReviewCardProps) {
  const showComment =
    drillOrderInfo.commitCommentUser && drillOrderInfo.commentContent && drillOrderInfo.commentTime;
  const showReview = !!drillOrderInfo.exerciseOrderReviewList?.length;
  return (
    <>
      {(showComment || showReview) && (
        <Card>
          <Descriptions
            title={
              <Typography.Title showBadge level={5}>
                点评复盘
              </Typography.Title>
            }
            column={1}
          >
            {showComment && (
              <>
                <Descriptions.Item
                  contentStyle={{ display: 'block' }}
                  labelStyle={{ marginTop: 18 }}
                  label="点评内容"
                >
                  <Space style={{ width: '100%', marginTop: 16 }} direction="vertical">
                    <Space>
                      <UserAvatar
                        id={drillOrderInfo.commentUser}
                        direction="horizontal"
                        showName
                        editable={false}
                      />
                      <Typography.Text style={{ marginLeft: 12 }} type="secondary">
                        {dayjs(drillOrderInfo.commentTime).format('YYYY-MM-DD HH:mm:ss')}
                      </Typography.Text>
                    </Space>
                    <Typography.Text>{drillOrderInfo.commentContent}</Typography.Text>
                  </Space>
                </Descriptions.Item>
                {!!drillOrderInfo.commentFileInfoList?.length && (
                  <Descriptions.Item label="点评附件">
                    <McUpload
                      fileList={drillOrderInfo.commentFileInfoList as MixedUploadFile[]}
                      allowDelete={false}
                      disabled
                    />
                  </Descriptions.Item>
                )}
              </>
            )}
            {drillOrderInfo.exerciseOrderReviewList?.map(item => {
              return (
                <>
                  {item.reviewDesc && (
                    <Descriptions.Item label="复盘描述">{item.reviewDesc}</Descriptions.Item>
                  )}
                  {!!item.fileInfoList?.length && (
                    <Descriptions.Item label="复盘附件">
                      <McUpload
                        fileList={item.fileInfoList as MixedUploadFile[]}
                        allowDelete={false}
                        disabled
                      />
                    </Descriptions.Item>
                  )}
                </>
              );
            })}
          </Descriptions>
        </Card>
      )}
    </>
  );
}
