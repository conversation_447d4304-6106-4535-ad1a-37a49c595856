/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-14
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import dayjs from 'dayjs';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Card } from '@manyun/base-ui.ui.card';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import type { StepsProps } from '@manyun/base-ui.ui.steps';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';
import type { DrillOrderInfo } from '@manyun/ticket.gql.client.drill-order';
import {
  useFinishDrillOrderRemark,
  useLazyDrillOrderApproval,
  useLazyDrillOrderApprovalInfo,
  useLazyDrillOrderInfo,
} from '@manyun/ticket.gql.client.drill-order';
import type { DrillPlan, DrillPlanTableProps } from '@manyun/ticket.ui.drill-plan-table';
import DrillPlanTable from '@manyun/ticket.ui.drill-plan-table';

import { CommentReviewCard } from './components/comment-review-card';
import { DrillOrderOperation } from './components/drill-order-operation';
import type { StepRemarkMode } from './components/step-remark-model';
import { StepRemarkModel } from './components/step-remark-model';
import { TicketInfoCard } from './components/ticket-info-card';

export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const stepsPrefixCls = `${prefixCls}-steps`;
    return css`
      // overrides steps's style
      .${stepsPrefixCls}-horizontal
        .${stepsPrefixCls}-item-content
        .${stepsPrefixCls}-item-description {
        max-width: none;
      }
    `;
  }}
`;
export function DrillOrder() {
  const { id } = useParams<{ id: string }>();
  const { userId } = getUserInfo();
  const [getDrillOrderInfo, { loading }] = useLazyDrillOrderInfo();
  const [getDrillOrderProgress, { data: progressData }] = useLazyDrillOrderApproval();
  const [getDrillOrderApprovalInfo, { data: approvalInfo }] = useLazyDrillOrderApprovalInfo();
  const [finishDrillOrderRemark] = useFinishDrillOrderRemark();
  const [isPdfExporting, setIsPdfExporting] = useState<boolean>(false);

  const [drillOrderInfo, setDrillOrderInfo] = useState<DrillOrderInfo>();
  const [activeTabKey, setActiveTabKey] = useState('drillPlan');
  const [stepRemarkModelOpen, setStepRemarkModelOpen] = useState(false);
  const [stepRemarkId, setStepRemarkId] = useState<number>();
  const [stepRemarkMode, setStepRemarkMode] = useState<StepRemarkMode>('edit');

  const fetchDrillOrderInfo = useCallback(async () => {
    const res = await getDrillOrderInfo({ variables: { execNo: id } });
    if (res.data?.drillOrderInfo) {
      const drillOrderInfo = res.data.drillOrderInfo;
      setDrillOrderInfo(drillOrderInfo);
      await getDrillOrderProgress({
        variables: { params: { instId: drillOrderInfo.bizProcessId, needNodes: true } },
      });
      if (drillOrderInfo.closeProcessId) {
        await getDrillOrderApprovalInfo({
          variables: {
            params: { processId: drillOrderInfo.closeProcessId, execNo: drillOrderInfo.excNo },
          },
        });
      }
    }
  }, [getDrillOrderApprovalInfo, getDrillOrderInfo, getDrillOrderProgress, id]);

  useEffect(() => {
    fetchDrillOrderInfo();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const approvalBaseInfo = approvalInfo?.drillOrderApprovalInfo?.data as BpmInstance;

  const progressSteps = useDeepCompareMemo(() => {
    const progressNodes = progressData?.drillOrderApproval?.nodeList;
    let _progressSteps: StepsProps['items'] = [
      {
        title: '创建',
        description: dayjs(drillOrderInfo?.gmtCreate).format('YYYY-MM-DD HH:mm'),
      },
    ];
    if (!progressNodes?.length) {
      return _progressSteps;
    }
    _progressSteps = [
      ..._progressSteps,
      ...progressNodes.map(item => {
        let description = dayjs(item.createTime).format('YYYY-MM-DD HH:mm');
        if (
          //未完成流程之前 显示--
          (item.code === 'TAKE' ||
            item.code === 'COMMENT' ||
            item.code === 'EXERCISE' ||
            item.code === 'REVIEW') &&
          item.status !== 'ALREADY_EXECUTE'
        ) {
          description = '--';
        }

        if (item.code === 'APPROVE') {
          return {
            title: '审批',
            description: (
              <Space style={{ width: '100%' }}>
                {item.status === 'ALREADY_EXECUTE' &&
                  drillOrderInfo?.taskStatus === 'APPROVE_TO_CLOSE' && (
                    <div style={{ whiteSpace: 'nowrap' }}>{description}</div>
                  )}
                <ApprovalRecordsDropdown
                  businessOrderInfo={{
                    taskNumber: id,
                    type: 'EME_PROCESS',
                    approvalPermissionType: 'EXERCISE',
                    status: drillOrderInfo!.taskStatus,
                  }}
                />
                {approvalBaseInfo && <AwaitOperationPeopleTag bpmInstance={approvalBaseInfo} />}
              </Space>
            ),
          };
        }

        return {
          title: item.name,
          description,
        };
      }),
    ];
    if (drillOrderInfo?.taskStatus === 'STOP') {
      _progressSteps = [
        ..._progressSteps,
        {
          title: '已终止',
          description: dayjs(drillOrderInfo?.stopTime).format('YYYY-MM-DD HH:mm'),
        },
      ];
    }
    if (drillOrderInfo?.taskStatus === 'CLOSE') {
      _progressSteps = [
        ..._progressSteps,
        {
          title: '完成',
          description: dayjs(drillOrderInfo?.closeTime).format('YYYY-MM-DD HH:mm'),
        },
      ];
    }
    return _progressSteps.map(item => {
      return {
        title: item.title,
        description: <div style={{ whiteSpace: 'nowrap' }}>{item.description}</div>,
      };
    });
  }, [
    approvalBaseInfo,
    drillOrderInfo?.closeTime,
    drillOrderInfo?.gmtCreate,
    drillOrderInfo?.stopTime,
    drillOrderInfo?.taskStatus,

    fetchDrillOrderInfo,
    progressData?.drillOrderApproval?.nodeList,
  ]);

  const onFinishDirllOrderStep = useCallback(
    async ({ excNo, id }: { excNo: string; id: number }) => {
      const res = await finishDrillOrderRemark({ variables: { params: { execNo: excNo, id } } });
      if (!res.data?.finishDrillOrderRemark?.success) {
        message.error(res.data?.finishDrillOrderRemark?.message);
        return;
      }
      message.success('完成成功！');
      fetchDrillOrderInfo();
    },
    [fetchDrillOrderInfo, finishDrillOrderRemark]
  );

  const onCreateStepRemark = useCallback(async (id: number, mode: StepRemarkMode) => {
    setStepRemarkId(id);
    setStepRemarkModelOpen(true);
    setStepRemarkMode(mode);
  }, []);

  const extraColumns: DrillPlanTableProps['extraColumns'] = useDeepCompareMemo(() => {
    if (!drillOrderInfo) {
      return [];
    }

    const firstNoFinishTimeItem = drillOrderInfo.exerciseOrderStepItemDoList.find(
      item => !item.finishTime
    );

    if (drillOrderInfo?.startTime && drillOrderInfo?.taskStatus === 'ALREADY_ACCEPT') {
      return [
        {
          title: '完成时间',
          width: 120,
          dataIndex: 'finishTime',
          render: (_, record) => {
            if (record?.finishTime) {
              return dayjs(record.finishTime as string).format('YYYY-MM-DD HH:mm:ss');
            }
            if (userId !== drillOrderInfo.taskAssignee) {
              return '--';
            }
            return (
              <Typography.Link
                disabled={record.id !== firstNoFinishTimeItem?.id}
                onClick={() =>
                  onFinishDirllOrderStep({ excNo: drillOrderInfo.excNo, id: record.id as number })
                }
              >
                完成
              </Typography.Link>
            );
          },
        },
        {
          title: '操作',
          width: 60,
          dataIndex: 'operation',
          render: (_, record) => {
            return (
              <Typography.Link
                disabled={!(record.finishTime || record.id === firstNoFinishTimeItem?.id)}
                onClick={() => onCreateStepRemark(record.id as number, 'edit')}
              >
                备注
              </Typography.Link>
            );
          },
        },
      ];
    }
    if (
      drillOrderInfo.taskStatus !== 'WAIT_ACCEPT' &&
      drillOrderInfo.taskStatus !== 'ALREADY_ACCEPT'
    ) {
      return [
        {
          title: '完成时间',
          width: 120,
          dataIndex: 'finishTime',
          render: finishTime => {
            return finishTime ? dayjs(finishTime).format('YYYY-MM-DD HH:mm:ss') : '--';
          },
        },
        {
          title: '操作',
          width: 60,
          dataIndex: 'operation',
          render: (_, record) => {
            if (record.isRemark) {
              return (
                <Typography.Link onClick={() => onCreateStepRemark(record.id as number, 'read')}>
                  查看
                </Typography.Link>
              );
            }
            return '--';
          },
        },
      ];
    }
    return [];
  }, [drillOrderInfo, onCreateStepRemark, onFinishDirllOrderStep, userId]);
  const stepCurrent = useMemo(() => {
    const progressNodes = progressData?.drillOrderApproval?.nodeList;
    const currentIndex = progressNodes?.findIndex(item => item.status === 'EXECUTE');
    if (drillOrderInfo?.taskStatus !== 'CLOSE' && drillOrderInfo?.taskStatus !== 'STOP') {
      return (currentIndex ?? 0) + 1;
    } else {
      return 10;
    }
  }, [progressData?.drillOrderApproval?.nodeList, drillOrderInfo?.taskStatus]);
  return (
    <>
      <Theme prefixCls="manyun">
        {drillOrderInfo ? (
          <Space style={{ display: 'flex', paddingBottom: 48 }} size="middle" direction="vertical">
            <DownloadPdfButton
              key="download"
              style={{ position: 'fixed', top: 52, right: 8 }}
              compact={false}
              disabled={isPdfExporting}
              pdfName={`${drillOrderInfo.excNo}_${drillOrderInfo.title}`}
              exportElement={document.getElementById('root')}
              beforeDownload={() => {
                setIsPdfExporting(true);
                return new Promise(resolve => {
                  setTimeout(() => {
                    resolve();
                  }, 2000);
                });
              }}
              onFinish={() => {
                setIsPdfExporting(false);
              }}
            />
            <Card
              headStyle={{ borderBottom: 'none', paddingTop: 8 }}
              bodyStyle={{ paddingTop: 12 }}
              title={
                <Typography.Title showBadge level={5}>
                  工单步骤
                </Typography.Title>
              }
            >
              <StyledContainer>
                <Steps size="small" current={stepCurrent} items={progressSteps} />
              </StyledContainer>
            </Card>
            <TicketInfoCard drillOrderInfo={drillOrderInfo} />
            <CommentReviewCard drillOrderInfo={drillOrderInfo} />
            {!isPdfExporting && (
              <DrillOrderOperation
                drillOrderInfo={drillOrderInfo}
                approvalBaseInfo={approvalBaseInfo}
                onCallBack={fetchDrillOrderInfo}
              />
            )}
            {!isPdfExporting && (
              <Card
                style={{ width: '100%' }}
                tabList={[
                  {
                    key: 'drillPlan',
                    tab: '演练方案',
                  },
                  {
                    key: 'operationRecords',
                    tab: '操作记录',
                  },
                ]}
                activeTabKey={activeTabKey}
                onTabChange={key => {
                  setActiveTabKey(key);
                }}
              >
                {activeTabKey === 'drillPlan' ? (
                  <DrillPlanTable
                    dataSource={(drillOrderInfo.exerciseOrderStepItemDoList as DrillPlan[]) ?? []}
                    showTicketSla={
                      !!(drillOrderInfo.taskStatus !== 'WAIT_ACCEPT' && drillOrderInfo.startTime)
                    }
                    extraColumns={extraColumns}
                  />
                ) : (
                  <OperationLogTable
                    defaultSearchParams={{ targetType: 'EME', targetId: drillOrderInfo.excNo }}
                    isTargetIdEqual={targetId => {
                      return targetId === drillOrderInfo.excNo;
                    }}
                    showColumns={['modifyType']}
                  />
                )}
              </Card>
            )}
            {isPdfExporting && (
              <Card title="演练方案">
                <DrillPlanTable
                  dataSource={(drillOrderInfo.exerciseOrderStepItemDoList as DrillPlan[]) ?? []}
                  showTicketSla={
                    !!(drillOrderInfo.taskStatus !== 'WAIT_ACCEPT' && drillOrderInfo.startTime)
                  }
                  extraColumns={extraColumns}
                />
              </Card>
            )}
            {stepRemarkId && (
              <StepRemarkModel
                excNo={drillOrderInfo.excNo}
                itemId={stepRemarkId}
                mode={stepRemarkMode}
                open={stepRemarkModelOpen}
                onClose={() => setStepRemarkModelOpen(false)}
                onCallBack={fetchDrillOrderInfo}
              />
            )}
          </Space>
        ) : (
          <Spin style={{ width: '100%' }} spinning={loading} />
        )}
      </Theme>
    </>
  );
}
