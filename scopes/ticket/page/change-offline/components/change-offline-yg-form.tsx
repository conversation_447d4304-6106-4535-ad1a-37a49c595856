import { MinusCircleOutlined, PlusOutlined, UploadOutlined } from '@ant-design/icons';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import {
  useLazyChangeVersions,
  useNeedUploadChangeCustomerAgreement,
} from '@manyun/ticket.gql.client.tickets';
import type { ChangeDeviceList } from '@manyun/ticket.gql.client.tickets';
import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';
import type { CreateChange } from '@manyun/ticket.service.create-change';
import { ChangeOfflineCriticalStepTable } from '@manyun/ticket.ui.change-offline-critical-step-table';
import type { CriticalStepForm } from '@manyun/ticket.ui.change-offline-critical-step-table';

import { ChangeSourceSelect } from './change-source';
import { SelectDevice } from './select-device';

export type ChangeOfflineFormValues = Omit<
  CreateChange,
  | 'planStartTime'
  | 'planEndTime'
  | 'customerFileInfoList'
  | 'blockTag'
  | 'fileInfoList'
  | 'fileList'
  | 'stepList'
  | 'changeTimeList'
  | 'changeSource'
  | 'changeDeviceList'
> & {
  planEndTime: Moment;
  planStartTime: Moment;
  customerFileInfoList: McUploadFile[] | undefined;
  fileInfoList: McUploadFile[];
  blockGuid: string;
  changeDeviceList: ChangeDeviceList[];
  changeSource: { type: string; id: string };
  changeTimeList: { date: [Moment, Moment]; time: [Moment, Moment] }[];
  stepList: CriticalStepForm[];
};

export type ChangeOfflineFormProps = {
  form: FormInstance<ChangeOfflineFormValues>;

  blockGuid?: string | null;
  idcTag?: string;
  setSubmitting: (loading: boolean) => void;
};

export function ChangeOfflineYgForm({
  form,
  blockGuid,
  idcTag,
  setSubmitting,
}: ChangeOfflineFormProps) {
  const _blockGuid = Form.useWatch('blockGuid', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const customerFileInfoList = Form.useWatch('customerFileInfoList', form);
  const riskLevel = Form.useWatch('riskLevel', form);
  const changeTimeList = Form.useWatch('changeTimeList', form);
  const stepList = Form.useWatch('stepList', form);
  // const [judgeEventBetaVersion] = useJudgeEventBetaVersion();
  const [getChangeVersions, { data: changeVersion }] = useLazyChangeVersions();

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCustomerAgreement =
    ticketScopeCommonConfigs.changes.features.customerAgreement;
  const isChangesTicketCustomerAgreementEnabled = changesTicketCustomerAgreement !== 'disabled';
  const featureIsEventConfigWithProcessEngine =
    ticketScopeCommonConfigs.events.features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';
  const [_idcTag, _blockTag] = (_blockGuid ?? '').split('.');
  const [{ data: changeReason }, { readMetaData: changeReasonReadMetaData }] = useMetaData(
    MetaType.CHANGE_REASON
  );
  const [{ data: changeType }, { readMetaData: changeTypeReadMetaData }] = useMetaData(
    MetaType.CHANGE
  );

  useEffect(() => {
    changeReasonReadMetaData();
    changeTypeReadMetaData();
  }, [changeReasonReadMetaData, changeTypeReadMetaData]);

  const [needUploadChangeCustomerAgreement, { data }] = useNeedUploadChangeCustomerAgreement();
  const [planStartTimeBeforeThreeDays, { data: canSelectThreeDaysAgo }] =
    useNeedUploadChangeCustomerAgreement();

  useEffect(() => {
    if (blockGuid) {
      form.setFieldsValue({ blockGuid });
    }
    getChangeVersions();
  }, [blockGuid, form, getChangeVersions]);

  useEffect(() => {
    if (riskLevel) {
      needUploadChangeCustomerAgreement({
        variables: { changeLevel: riskLevel, type: 'CUSTOMER_AGREE' },
      });
      planStartTimeBeforeThreeDays({
        variables: { changeLevel: riskLevel, type: 'PLAN_TIME' },
      });
    }
  }, [riskLevel, needUploadChangeCustomerAgreement, planStartTimeBeforeThreeDays]);

  const disabledDate = (current: Moment) => {
    if (canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement.data) {
      return current && current < moment().subtract(3, 'day').startOf('day');
    }
    return current && current < moment().subtract(1, 'days').hour(23).minute(59).second(59);
  };

  return (
    <Form
      colon={false}
      form={form}
      labelCol={{ flex: ' 0 0 110px' }}
      wrapperCol={{ span: 24 }}
      initialValues={{
        changeTimeList: [
          { date: undefined, time: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')] },
        ],
      }}
    >
      <Card
        bordered={false}
        title={
          <Typography.Title showBadge level={5}>
            基本信息
          </Typography.Title>
        }
        bodyStyle={{ width: 700 }}
      >
        <Row>
          <Col span={12}>
            <Form.Item
              label="机房/楼"
              name="blockGuid"
              rules={[
                {
                  required: true,
                  message: '机房/楼必选！',
                },
                {
                  validator: (_, value) => {
                    if (value && value.split('.').length !== 2) {
                      return Promise.reject('请选择至楼栋！');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <LocationTreeSelect
                disabled={!!blockGuid}
                disabledTypes={['IDC']}
                authorizedOnly
                showSearch
                allowClear
                idc={idcTag}
                onChange={value => {
                  form.setFieldsValue({
                    stepList: [],
                    changeDeviceList: [],
                    changeSource: undefined,
                  });
                  const [idcTag] = (value as string)?.split('.');
                  if ((changeVersion?.changeVersions.data ?? []).includes(idcTag)) {
                    message.error(
                      '您所在机房适用「变更管理（内测版）」，请进入「变更管理（内测版）」进行操作'
                    );
                  }
                  // judgeEventBetaVersion({
                  //   variables: { guid: idcTag },
                  //   onCompleted(data) {
                  //     if (data.judgeEventBetaVersion?.data) {
                  //       message.error(
                  //         '您所在机房适用「变更管理（内测版）」，请进入「变更管理（内测版）」进行操作'
                  //       );
                  //     }
                  //   },
                  // });
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更专业"
              name="reason"
              rules={[
                {
                  required: true,
                  message: '变更专业必填！',
                },
              ]}
            >
              <Select
                allowClear
                options={changeReason.data.map(({ label, value }) => ({ label, value: label }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更类型"
              name="changeType"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <Select
                allowClear
                options={changeType.data.map(({ label, value }) => ({ label, value }))}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="变更等级"
              name="riskLevel"
              rules={[
                {
                  required: true,
                  message: '变更等级必选！',
                },
              ]}
            >
              <Select
                showArrow
                options={Object.entries(CHANGE_RISK_LEVEL_TEXT_MAP).map(item => ({
                  label: item[1],
                  value: item[0],
                }))}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="变更标题"
              name="title"
              rules={[
                {
                  required: true,
                  message: '变更标题必填！',
                },
                {
                  max: 50,
                  message: '最多输入 50 个字符！',
                },
              ]}
            >
              <Input allowClear />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="变更目的"
              name="purpose"
              rules={[
                {
                  required: true,
                  message: '变更目的必填！',
                },
                {
                  max: 500,
                  message: '最多输入 500 个字符！',
                },
              ]}
            >
              <Input.TextArea rows={2} allowClear />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item
              label="变更来源"
              name="changeSource"
              rules={[
                {
                  validator: async (_, changeSource) => {
                    if (changeSource && changeSource.type && !changeSource.id) {
                      return Promise.reject(new Error('变更来源ID必选'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <ChangeSourceSelect
                idcTag={_idcTag}
                blockGuid={_blockGuid}
                shouldShowEventNumber={featureIsEventConfigWithProcessEngineRequired}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <Form.Item
              label="影响范围"
              name="changeInfluence"
              rules={[
                {
                  required: true,
                  message: '影响范围必填！',
                },
                {
                  max: 300,
                  message: '可最可输入300 个字符！',
                },
              ]}
            >
              <Input.TextArea />
            </Form.Item>
          </Col>
          <Col span={24}>
            <Form.List
              name="changeTimeList"
              rules={[
                {
                  validator: async (_, names) => {
                    if (!names || names.length < 1) {
                      return Promise.reject(new Error('至少添加一项'));
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              {(fields, { add, remove }, { errors }) => (
                <>
                  {fields.map(({ key, name, ...field }, index) => (
                    <Form.Item
                      key={`${key}.${name}`}
                      label={index === 0 ? '变更计划时间' : ' '}
                      required={index === 0}
                      colon={index === 0}
                    >
                      <Space>
                        <Form.Item
                          {...field}
                          rules={[
                            {
                              required: true,
                              message: `请选择变更计划日期`,
                            },
                          ]}
                          noStyle
                          name={[name, 'date']}
                        >
                          <DatePicker.RangePicker
                            format="YYYY-MM-DD"
                            allowClear
                            disabledDate={disabledDate}
                          />
                        </Form.Item>
                        <Form.Item
                          {...field}
                          rules={[
                            {
                              required: true,
                              message: `请选择变更计划时间`,
                            },
                          ]}
                          noStyle
                          name={[name, 'time']}
                        >
                          <TimePicker.RangePicker format="HH:mm" allowClear />
                        </Form.Item>
                        {fields.length > 1 ? (
                          <MinusCircleOutlined onClick={() => remove(name)} />
                        ) : null}
                      </Space>
                    </Form.Item>
                  ))}
                  <Form.Item label=" " colon={false} labelCol={{ flex: ' 0 0 110px' }}>
                    {changeTimeList?.length < 10 && (
                      <Button
                        style={{ width: '100%' }}
                        type="dashed"
                        disabled={fields.length >= 20}
                        icon={<PlusOutlined />}
                        onClick={() => {
                          add({
                            date: undefined,
                            time: [moment('00:00', 'HH:mm'), moment('23:59', 'HH:mm')],
                          });
                        }}
                      >
                        添加变更计划时间
                      </Button>
                    )}
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                </>
              )}
            </Form.List>
          </Col>
          {isChangesTicketCustomerAgreementEnabled &&
            data?.needUploadChangeCustomerAgreement.data && (
              <Col span={24}>
                <Form.Item
                  label="客户同意书"
                  name="customerFileInfoList"
                  valuePropName="fileList"
                  getValueFromEvent={({ fileList }: { fileList: MixedUploadFile[] }) => {
                    if (fileList.filter(file => file.status === 'uploading').length) {
                      setSubmitting(true);
                    } else {
                      setSubmitting(false);
                    }
                    return fileList;
                  }}
                >
                  <McUpload
                    showAccept
                    accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
                    showUploadList
                    allowDelete
                    maxCount={3}
                    openFileDialogOnClick={
                      customerFileInfoList === undefined || customerFileInfoList.length < 3
                    }
                  >
                    <Button
                      disabled={customerFileInfoList && customerFileInfoList.length >= 3}
                      icon={<UploadOutlined />}
                    >
                      点此上传
                    </Button>
                  </McUpload>
                </Form.Item>
              </Col>
            )}
          <Col span={24}>
            <Form.Item
              label="变更方案附件"
              name="fileInfoList"
              rules={[{ required: true, message: '请选择上传文件！' }]}
              valuePropName="fileList"
              getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
                if (fileList.filter(file => file.status === 'uploading').length) {
                  setSubmitting(true);
                } else {
                  setSubmitting(false);
                }
                return fileList;
              }}
            >
              <McUpload
                showAccept
                accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
                showUploadList
                allowDelete
                maxCount={10}
                openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 10}
              >
                <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 10}>
                  点此上传
                </Button>
              </McUpload>
            </Form.Item>
          </Col>
        </Row>
      </Card>
      <Card
        bordered={false}
        title={
          <Typography.Title showBadge level={5}>
            变更设备
          </Typography.Title>
        }
        bodyStyle={{ width: '100%' }}
      >
        <Form.Item
          labelCol={{ flex: ' 0 0 0' }}
          wrapperCol={{ span: 24 }}
          required={false}
          label=" "
          name="changeDeviceList"
          colon={false}
          valuePropName="dataSource"
        >
          <SelectDevice blockTag={_blockTag} idcTag={_idcTag} />
        </Form.Item>
      </Card>
      <Card
        bordered={false}
        title={
          <Typography.Title showBadge level={5}>
            关键步骤
          </Typography.Title>
        }
        bodyStyle={{ width: '100%' }}
      >
        <Form.Item
          labelCol={{ flex: ' 0 0 0' }}
          wrapperCol={{ span: 24 }}
          required={false}
          label=" "
          name="stepList"
          colon={false}
          rules={[
            { required: true, message: '请添加关键步骤' },
            () => ({
              validator() {
                if (stepList && stepList.length > 100) {
                  return Promise.reject('最多添加100个步骤');
                }
                return Promise.resolve();
              },
            }),
          ]}
          valuePropName="dataSource"
        >
          <ChangeOfflineCriticalStepTable isEdit blockGuid={_blockGuid} />
        </Form.Item>
      </Card>
    </Form>
  );
}
