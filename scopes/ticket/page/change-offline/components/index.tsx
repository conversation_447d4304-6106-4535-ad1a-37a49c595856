import React from 'react';
import { useSelector } from 'react-redux';

import type { FormInstance } from '@manyun/base-ui.ui.form';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

import { ChangeOfflineGocForm } from './change-offline-form';
import { ChangeOfflineYgForm } from './change-offline-yg-form';
import type { ChangeOfflineFormValues } from './change-offline-yg-form';

export type { ChangeOfflineFormValues } from './change-offline-yg-form';
export type ChangeOfflineFormProps = {
  form: FormInstance<ChangeOfflineFormValues>;
  blockGuid?: string | null;
  idcTag?: string;
  setSubmitting: (loading: boolean) => void;
};

export function ChangeOfflineForm({ ...props }: ChangeOfflineFormProps) {
  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;

  return (
    <>
      {changesTicketCreateChangeOffline === 'full' ? (
        <ChangeOfflineYgForm {...props} />
      ) : (
        <ChangeOfflineGocForm {...props} />
      )}
    </>
  );
}
