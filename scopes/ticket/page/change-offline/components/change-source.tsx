import React, { useEffect, useMemo, useState } from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import { useLazyMetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import { useFetchFuzzyChanges, useLazyFetchFuzzyEvents } from '@manyun/ticket.gql.client.tickets';
import { getChangeLocales } from '@manyun/ticket.model.change';
import type { SourceType } from '@manyun/ticket.model.change';
import type { TaskType } from '@manyun/ticket.model.ticket';
import { fetchPagedRiskRegisters } from '@manyun/ticket.service.fetch-paged-risk-registers';
import type { BaseInfo } from '@manyun/ticket.service.fetch-tickets';
import { fetchTickets } from '@manyun/ticket.service.fetch-tickets';

export type ChangeSourceProps = {
  idcTag: string;
  blockGuid: string;
  shouldShowEventNumber: boolean;
  value?: { type: SourceType; id: string } | undefined;
  onChange?: (value: { type?: SourceType; id?: string }) => void;
};

type LabelInValue = {
  label: string;
  key: string;
  value: string;
};

export const ChangeSourceSelect = React.forwardRef(
  (
    { idcTag, blockGuid, shouldShowEventNumber, value, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [dataSource, setDataSource] = useState<LabelInValue[]>([]);
    const locales = useMemo(() => getChangeLocales(), []);
    const [getMetaData, { data: metaData }] = useLazyMetadataTree({
      variables: {
        type: 'CHANGE_SOURCE',
      },
    });
    const [getFuzzyChanges] = useFetchFuzzyChanges();
    const [getFuzzyEvents] = useLazyFetchFuzzyEvents();

    const options = useMemo(() => {
      const list = Object.keys(locales.changeSource.source).map(status => ({
        label: locales.changeSource.source[status as SourceType],
        value: status,
      }));

      const _metaData: LabelInValue[] = metaData?.metadataTree
        ? metaData.metadataTree.map(item => ({
            label: item.name,
            value: item.code,
            key: item.code,
          }))
        : [];
      return [...list, ..._metaData];
    }, [metaData, locales.changeSource.source]);

    useEffect(() => {
      getMetaData();
    }, [getMetaData]);

    const getChangeList = async (contentKey: string) => {
      if (!contentKey) {
        return;
      }
      const { data, error } = await getFuzzyChanges({
        variables: { queryCondition: contentKey, blockGuid },
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = data?.fetchFuzzyChanges?.data
        ? data?.fetchFuzzyChanges?.data?.map(item => {
            return {
              label: `${item.changeOrderId} ${item.title}`,
              value: item.changeOrderId,
              key: item.changeOrderId,
            };
          })
        : [];
      setDataSource(list);
    };

    const getEventList = async (contentKey: string) => {
      const { data, error } = await getFuzzyEvents({
        variables: {
          query: {
            blockGuid,
            keyword: contentKey,
          },
        },
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = (data?.fetchFuzzyEvents?.data ?? []).map(item => {
        return {
          label: `${shouldShowEventNumber ? item.eventNo : item.id} ${item.eventTitle ?? ''}`,
          value: item.eventNo!,
          key: item.eventNo!,
        };
      });
      setDataSource(list);
    };

    const getRiskRegisterList = async (contentKey: string) => {
      const { data, error } = await fetchPagedRiskRegisters({
        pageNum: 1,
        pageSize: 1000,
        blockGuids: [blockGuid],
        riskKey: contentKey,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      const list: LabelInValue[] = data.data.map(item => {
        return {
          label: `${item.id} ${item.riskDesc}`,
          value: item.id,
          key: item.id,
        };
      });
      setDataSource(list);
    };

    const getBaseTicketList = async (contentKey: string) => {
      if (value?.type) {
        const { data, error } = await fetchTickets({
          pageNum: 1,
          pageSize: 1000,
          taskType: value.type as TaskType,
          idcTag,
          blockTag: blockGuid,
          multiField: contentKey,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        const list: LabelInValue[] = data.data.map((item: BaseInfo) => {
          return {
            label: `${item.taskNo} ${item.taskTitle}`,
            value: item.taskNo,
            key: item.taskNo,
          };
        });

        setDataSource(list);
      }
    };
    const disabled = !idcTag || !blockGuid || !value?.type;
    return (
      <Space.Compact block style={{ width: '100%', display: 'flex' }}>
        <Select
          style={{ width: 112, marginRight: 8 }}
          options={options}
          value={value?.type}
          allowClear
          disabled={!idcTag || !blockGuid}
          onChange={(value: SourceType) => {
            setDataSource([]);
            onChange && onChange({ type: value, id: undefined });
          }}
        />
        {Object.keys(locales.changeSource.source).includes(value?.type ?? '') ? (
          <Select
            allowClear
            style={{ flex: 1 }}
            disabled={disabled}
            options={dataSource}
            value={value?.id}
            showSearch
            placeholder="请输入ID或名称查询"
            filterOption={false}
            optionLabelProp="title"
            onSearch={(content: string) => {
              switch (value?.type) {
                case 'EVENT':
                  getEventList(content);
                  break;
                case 'CHANGE':
                  getChangeList(content);
                  break;
                case 'RISK':
                  getRiskRegisterList(content);
                  break;
                default:
                  getBaseTicketList(content);
                  break;
              }
            }}
            onChange={(id: string) => {
              onChange && onChange({ type: value?.type, id });
            }}
          />
        ) : (
          <Input
            allowClear
            maxLength={100}
            disabled={disabled}
            onChange={e => {
              onChange && onChange({ type: value?.type, id: e.target.value });
            }}
          />
        )}
      </Space.Compact>
    );
  }
);

ChangeSourceSelect.displayName = 'ChangeSourceSelect';
