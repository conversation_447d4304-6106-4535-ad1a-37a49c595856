import React from 'react';

import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import type { TableProps } from '@manyun/base-ui.ui.table';
import { DevicesTableSelectModal } from '@manyun/resource-hub.ui.device-select';
import type { ChangeDeviceList } from '@manyun/ticket.gql.client.tickets';
import { ChangeOfflineDeviceTable } from '@manyun/ticket.ui.change-offline-device-table';

export type ChangeSourceProps = {
  idcTag: string;
  blockTag: string;
  onChange?: (value: ChangeDeviceList[]) => void;
} & TableProps<ChangeDeviceList>;

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};

export const SelectDevice = React.forwardRef(
  (
    { idcTag, blockTag, dataSource, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    return (
      <Space style={{ width: '100%' }} direction="vertical">
        <ChangeOfflineDeviceTable
          actionBtn={
            <DevicesTableSelectModal
              style={{ width: 88 }}
              idcTag={idcTag!}
              blockTag={blockTag!}
              showDescription={false}
              value={dataSource}
              disabled={!idcTag || !blockTag}
              onChange={onChange}
              matchProductModel
            />
          }
          dataSource={dataSource}
          mode="add"
          onChange={onChange}
        />
      </Space>
    );
  }
);

SelectDevice.displayName = 'SelectDevice';
