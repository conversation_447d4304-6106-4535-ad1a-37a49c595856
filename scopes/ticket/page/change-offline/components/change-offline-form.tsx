import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import {
  useLazyChangeVersions,
  useNeedUploadChangeCustomerAgreement,
} from '@manyun/ticket.gql.client.tickets';
import { CHANGE_RISK_LEVEL_TEXT_MAP } from '@manyun/ticket.model.change';

import type { ChangeOfflineFormValues } from './change-offline-yg-form';

export type ChangeOfflineFormProps = {
  form: FormInstance<ChangeOfflineFormValues>;

  blockGuid?: string | null;
  idcTag?: string;
  setSubmitting: (loading: boolean) => void;
};

export function ChangeOfflineGocForm({
  form,
  blockGuid,
  idcTag,
  setSubmitting,
}: ChangeOfflineFormProps) {
  const planStartTime = Form.useWatch('planStartTime', form);
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const customerFileInfoList = Form.useWatch('customerFileInfoList', form);
  const riskLevel = Form.useWatch('riskLevel', form);
  const [getChangeVersions, { data: changeVersion }] = useLazyChangeVersions();

  // const [judgeEventBetaVersion] = useJudgeEventBetaVersion();

  const [{ data: changeReason }, { readMetaData: changeReasonReadMetaData }] = useMetaData(
    MetaType.CHANGE_REASON
  );
  const [{ data: changeType }, { readMetaData: changeTypeReadMetaData }] = useMetaData(
    MetaType.CHANGE
  );

  useEffect(() => {
    changeReasonReadMetaData();
    changeTypeReadMetaData();
  }, [changeReasonReadMetaData, changeTypeReadMetaData]);

  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCustomerAgreement =
    ticketScopeCommonConfigs.changes.features.customerAgreement;
  const isChangesTicketCustomerAgreementEnabled = changesTicketCustomerAgreement !== 'disabled';
  const [needUploadChangeCustomerAgreement, { data }] = useNeedUploadChangeCustomerAgreement();
  const [planStartTimeBeforeThreeDays, { data: canSelectThreeDaysAgo }] =
    useNeedUploadChangeCustomerAgreement();

  useEffect(() => {
    if (blockGuid) {
      form.setFieldsValue({ blockGuid });
    }
    getChangeVersions();
  }, [blockGuid, form, getChangeVersions]);

  useEffect(() => {
    if (riskLevel) {
      needUploadChangeCustomerAgreement({
        variables: { changeLevel: riskLevel, type: 'CUSTOMER_AGREE' },
      });
      planStartTimeBeforeThreeDays({
        variables: { changeLevel: riskLevel, type: 'PLAN_TIME' },
      });
    }
  }, [riskLevel, needUploadChangeCustomerAgreement, planStartTimeBeforeThreeDays]);

  const disabledDate = (current: Moment) => {
    if (canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement.data) {
      return current && current < moment().subtract(3, 'day').startOf('day');
    }
    return current && current < moment().subtract(1, 'days').hour(23).minute(59).second(59);
  };

  const range = (start: number, end: number) => {
    const result = [];
    for (let i = start; i < end; i++) {
      result.push(i);
    }
    return result;
  };

  const endTimeDisabledDateTime = (current: Moment | null) => {
    const time = form.getFieldValue('planStartTime');
    if (!time || !current) {
      return {};
    }
    const hours = time.hours();
    const minute = time.minute();
    const second = time.second();
    const currentHours = current.hours();
    const isStartToday = time.isSame(current, 'day');

    if (isStartToday) {
      if (currentHours > hours) {
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minute),
        disabledSeconds: () => range(0, second),
      };
    }
    return {};
  };

  const disabledDateTime = (current: Moment | null) => {
    if (!current || canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement.data) {
      return {};
    }

    const compareMoment = moment();
    const hours = compareMoment.hours();
    const minute = compareMoment.minute();
    const second = compareMoment.second();
    const currentHours = current.hours();

    const isToday = current.isSame(moment(), 'day');
    if (isToday) {
      if (currentHours > hours) {
        return {
          disabledHours: () => range(0, hours),
        };
      }
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minute),
        disabledSeconds: () => range(0, second),
      };
    }
    return {};
  };

  const endTimeDisabledDate = (current: Moment) => {
    const startTime = form.getFieldValue('planStartTime');
    return (
      current && current < moment(startTime).subtract(1, 'days').hour(23).minute(59).second(59)
    );
  };

  return (
    <Form labelCol={{ xl: 5 }} wrapperCol={{ xl: 17 }} colon={false} form={form}>
      <Form.Item
        label="变更标题"
        name="title"
        rules={[
          {
            required: true,
            message: '变更标题必填！',
          },
          {
            max: 50,
            message: '最多输入 50 个字符！',
          },
        ]}
      >
        <Input allowClear />
      </Form.Item>
      <Form.Item
        label="机房/楼"
        name="blockGuid"
        rules={[
          {
            required: true,
            message: '机房/楼必选！',
          },
          {
            validator: (_, value) => {
              if (value && value.split('.').length !== 2) {
                return Promise.reject('请选择至楼栋！');
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <LocationTreeSelect
          disabled={!!blockGuid}
          disabledTypes={['IDC']}
          authorizedOnly
          showSearch
          allowClear
          idc={idcTag}
          onChange={value => {
            const [idcTag] = (value as string)?.split('.');
            if ((changeVersion?.changeVersions.data ?? []).includes(idcTag)) {
              message.error(
                '您所在机房适用「变更管理（内测版）」，请进入「变更管理（内测版）」进行操作'
              );
            }
            // judgeEventBetaVersion({
            //   variables: { guid: idcTag },
            //   onCompleted(data) {
            //     if (data.judgeEventBetaVersion?.data) {
            //       message.error(
            //         '您所在机房适用「变更管理（内测版）」，请进入「变更管理（内测版）」进行操作'
            //       );
            //     }
            //   },
            // });
          }}
        />
      </Form.Item>
      <Form.Item
        label="变更专业"
        name="reason"
        rules={[
          {
            required: true,
            message: '变更专业必填！',
          },
        ]}
      >
        <Select allowClear>
          {changeReason.data.map(({ label }) => (
            <Select.Option key={label} value={label}>
              {label}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item
        label="变更类型"
        name="changeType"
        rules={[
          {
            required: true,
          },
        ]}
      >
        <Select allowClear>
          {changeType.data.map(({ label, value }) => (
            <Select.Option key={value} value={value}>
              {label}
            </Select.Option>
          ))}
        </Select>
      </Form.Item>
      <Form.Item
        label="变更等级"
        name="riskLevel"
        rules={[
          {
            required: true,
            message: '变更等级必选！',
          },
        ]}
      >
        <Select
          showArrow
          options={Object.entries(CHANGE_RISK_LEVEL_TEXT_MAP).map(item => ({
            label: item[1],
            value: item[0],
          }))}
        />
      </Form.Item>
      <Form.Item
        label="变更计划开始时间"
        name="planStartTime"
        rules={[
          {
            required: true,
            message: '变更计划开始时间必填！',
          },
          {
            validator(_, value) {
              const planStartTimeMoment = moment(value);
              if (canSelectThreeDaysAgo?.needUploadChangeCustomerAgreement?.data) {
                if (
                  value &&
                  moment(planStartTimeMoment.subtract(3, 'day').startOf('day')).diff(
                    planStartTimeMoment,
                    'minutes'
                  ) > 0
                ) {
                  return Promise.reject(new Error('变更计划开始时间不可早于三天前'));
                }
                return Promise.resolve();
              }
              if (value && moment().diff(planStartTimeMoment, 'minutes') > 0) {
                return Promise.reject(new Error('变更计划开始时间不可早于当前时间'));
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <DatePicker
          disabledDate={disabledDate}
          disabledTime={disabledDateTime}
          showTime
          format="YYYY-MM-DD HH:mm"
          showNow={false}
          onChange={() => {
            form.setFieldsValue({ planEndTime: undefined });
          }}
        />
      </Form.Item>
      <Form.Item
        label="变更计划完成时间"
        name="planEndTime"
        rules={[
          {
            required: true,
            message: '变更计划完成时间必填！',
          },
          {
            validator(_, value) {
              if (value && moment(planStartTime).diff(moment(value), 'minutes') > 0) {
                return Promise.reject(new Error('变更计划完成时间不可早于变更计划开始时间'));
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <DatePicker
          disabledTime={endTimeDisabledDateTime}
          disabledDate={endTimeDisabledDate}
          disabled={!planStartTime}
          showTime
          format="YYYY-MM-DD HH:mm"
          showNow={false}
        />
      </Form.Item>
      {isChangesTicketCustomerAgreementEnabled && data?.needUploadChangeCustomerAgreement.data && (
        <Form.Item
          label="客户同意书"
          name="customerFileInfoList"
          valuePropName="fileList"
          getValueFromEvent={({ fileList }: { fileList: MixedUploadFile[] }) => {
            if (fileList.filter(file => file.status === 'uploading').length) {
              setSubmitting(true);
            } else {
              setSubmitting(false);
            }
            return fileList;
          }}
        >
          <McUpload
            showAccept
            accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
            showUploadList
            allowDelete
            maxCount={3}
            openFileDialogOnClick={
              customerFileInfoList === undefined || customerFileInfoList.length < 3
            }
          >
            <Button
              disabled={customerFileInfoList && customerFileInfoList.length >= 3}
              icon={<UploadOutlined />}
            >
              点此上传
            </Button>
          </McUpload>
        </Form.Item>
      )}
      <Form.Item
        label="变更方案附件"
        name="fileInfoList"
        rules={[{ required: true, message: '请选择上传文件！' }]}
        valuePropName="fileList"
        getValueFromEvent={({ fileList, ...r }: { fileList: MixedUploadFile[] }) => {
          if (fileList.filter(file => file.status === 'uploading').length) {
            setSubmitting(true);
          } else {
            setSubmitting(false);
          }
          return fileList;
        }}
      >
        <McUpload
          showAccept
          accept=".zip,.rar,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
          showUploadList
          allowDelete
          maxCount={10}
          openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 10}
        >
          <Button icon={<UploadOutlined />} disabled={fileInfoList?.length >= 10}>
            点此上传
          </Button>
        </McUpload>
      </Form.Item>
      <Form.Item
        label="影响范围"
        name="changeInfluence"
        rules={[
          {
            required: true,
            message: '影响范围必填！',
          },
          {
            max: 300,
            message: '可最可输入300 个字符！',
          },
        ]}
      >
        <Input.TextArea />
      </Form.Item>
    </Form>
  );
}
