import { createContext, useContext } from 'react';

import { ChangeTabs } from '@manyun/ticket.model.change';

export type ExportPDFTabs = {
  isVolid: boolean;
  key: string;
  isRenderd: boolean;
  title: string;
}[];

export type ChangeContextValue = {
  isExportPDF: boolean;
  exportPDFTabs: ExportPDFTabs;
  setIsExportPDF: (mode: boolean) => void;
  setExportPDFTabs: React.Dispatch<React.SetStateAction<ExportPDFTabs>>;
};

export const initTabs = [
  { key: ChangeTabs.Info, isVolid: false, isRenderd: true, title: '变更信息' },
  { key: ChangeTabs.ExecuteRecord, isVolid: false, isRenderd: false, title: '执行记录' },
  { key: ChangeTabs.SummaryRecord, isVolid: false, isRenderd: false, title: '总结记录' },
];

export const ChangeContext = createContext<ChangeContextValue>({
  isExportPDF: false,
  exportPDFTabs: initTabs,
  setIsExportPDF: () => {},
  setExportPDFTabs: () => {},
});

export function useChangeContext() {
  return useContext(ChangeContext);
}
