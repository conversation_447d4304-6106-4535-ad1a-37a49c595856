import sortBy from 'lodash.sortby';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useLazyChangeOrderDevices } from '@manyun/ticket.gql.client.tickets';
import { CHANGE_EXE_WAY_MAP } from '@manyun/ticket.model.change';
import { generateChangeTicketDetail } from '@manyun/ticket.route.ticket-routes';
import type { CreateChange } from '@manyun/ticket.service.create-change';
import { createChange } from '@manyun/ticket.service.create-change';
import { fetchChange } from '@manyun/ticket.service.fetch-change';

import { ChangeOfflineForm } from './components/index';
import type { ChangeOfflineFormValues } from './components/index';

export function ChangeOffline() {
  const [form] = Form.useForm<ChangeOfflineFormValues>();
  const { id } = useParams<{ id: string }>();
  const [submitting, setSubmitting] = useState<boolean>(false);
  const history = useHistory();
  const config = useSelector(selectCurrentConfig);

  const configUtil = new ConfigUtil(config);

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');

  const changesTicketCreateChangeOffline =
    ticketScopeCommonConfigs.changes.features.createChangeOffline;
  const isYgOffline = changesTicketCreateChangeOffline === 'full';

  useEffect(() => {
    if (id) {
      getDetail();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);
  const [getChangeOrderDevices] = useLazyChangeOrderDevices();

  const getDetail = async () => {
    const { data, error } = await fetchChange({ changeOrderId: id });
    if (error) {
      message.error(error.message);
      return;
    }
    const { data: deviceList } = await getChangeOrderDevices({
      variables: { query: { changeOrderId: id, pageNum: 1, pageSize: 50000 } },
    });
    if (data) {
      form.setFieldsValue({
        title: data.title,
        blockGuid: data.blockTag,
        reason: data.reason,
        changeType: data.changeType,
        riskLevel: data.riskLevel,
        planStartTime: moment(data.planStartTime),
        planEndTime: moment(data.planEndTime),
        fileInfoList: data.fileInfoList?.map(obj => McUploadFile.fromApiObject(obj)),
        customerFileInfoList: data.customerFileInfoList?.map(obj =>
          McUploadFile.fromApiObject(obj)
        ),
        changeInfluence: data.changeInfluence,
      });
      if (isYgOffline) {
        form.setFieldsValue({
          changeDeviceList: deviceList?.changeOrderDevices?.data,
          stepList: data.stepList.map(item => ({
            ...item,
            operator: { label: item.operatorName, value: item.operatorId },
          })),
          purpose: data.purpose,
          changeSource: { type: data.sourceType, id: data.sourceNo },
          changeTimeList: data.changeOrderExtJson?.changeTimeList?.map(item => ({
            date: [moment(item.startDate), moment(item.endDate)],
            time: [
              moment(`${item.startDate} ${item.startHour}:00`),
              moment(`${item.endDate} ${item.endHour}:00`),
            ],
          })),
        });
      }
    }
  };

  const submit = () => {
    form
      .validateFields()
      .then(async values => {
        const [idcTag] = values.blockGuid.split('.');
        const blockTag = values.blockGuid;
        const {
          riskLevel,
          changeType,
          planStartTime,
          planEndTime,
          fileInfoList,
          reason,
          title,
          changeInfluence,
          customerFileInfoList,
          changeDeviceList,
          stepList,
          changeTimeList,
          changeSource,
          purpose,
        } = values;
        const _p: CreateChange = {
          riskLevel,
          changeType,
          planStartTime: planStartTime ? planStartTime.valueOf() : 0,
          planEndTime: planEndTime ? planEndTime.valueOf() : 0,
          title: title,
          fileInfoList: fileInfoList.map((obj: McUploadFile) => McUploadFile.toApiObject(obj)),
          idcTag,
          blockTag,
          reason: reason,
          exeWay: CHANGE_EXE_WAY_MAP.OffLine,
          changeInfluence,
          customerFileInfoList: customerFileInfoList?.map((obj: McUploadFile) =>
            McUploadFile.toApiObject(obj)
          ),
        };
        if (id) {
          _p.changeOrderId = id;
        }
        if (isYgOffline) {
          _p.changeDeviceList = changeDeviceList;
          _p.stepList = stepList
            ? stepList.map(item => ({
                stepName: item.stepName,
                stepOrder: item.stepOrder,
                operatorId: item.operator?.value,
                operatorName: item.operator?.label,
                stepDesc: item.stepDesc,
              }))
            : [];
          const planTime = getPlantime({ changeTimeList });
          _p.changeTimeList = planTime.changeTimeList;
          _p.sourceNo = changeSource?.id;
          _p.sourceType = changeSource?.type;
          _p.purpose = purpose;
          _p.planStartTime = planTime.planStartTime;
          _p.planEndTime = planTime.planEndTime;
        }
        setSubmitting(true);
        const { data, error } = await createChange(_p);
        setSubmitting(false);
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('提交成功');
        history.push(
          generateChangeTicketDetail({
            id: data.changeOrderId,
          })
        );
      })
      .catch();
  };

  return (
    <>
      {isYgOffline ? (
        <Card style={{ marginBottom: 56, padding: 0 }} bodyStyle={{ padding: 0 }}>
          <Space style={{ width: '100%' }}>
            <ChangeOfflineForm form={form} setSubmitting={setSubmitting} />
          </Space>
          <FooterToolBar>
            <Space>
              <Button type="primary" loading={submitting} onClick={submit}>
                提交
              </Button>
              <Button
                loading={submitting}
                onClick={() => {
                  history.goBack();
                }}
              >
                取消
              </Button>
            </Space>
          </FooterToolBar>
        </Card>
      ) : (
        <Card bodyStyle={{ justifyContent: 'center', display: 'flex' }}>
          <Space direction="vertical" style={{ width: 680 }}>
            <ChangeOfflineForm form={form} setSubmitting={setSubmitting} />
            <Form.Item label=" " colon={false} labelCol={{ span: 5 }}>
              <Button type="primary" loading={submitting} onClick={submit}>
                提交
              </Button>
            </Form.Item>
          </Space>
        </Card>
      )}
    </>
  );
}

export type SelectedStoreState = {
  userId: number;
  username: string;
  name: string;
};

export function selectUserInfos({
  user: { userId, username, name },
}: {
  user: { userId: number; username: string; name: string };
}): SelectedStoreState {
  return {
    userId,
    username,
    name,
  };
}

export function getPlantime({
  changeTimeList,
}: {
  changeTimeList: { date: [Moment, Moment]; time: [Moment, Moment] }[];
}): {
  changeTimeList: { startDate: string; endDate: string; startHour: string; endHour: string }[];
  planStartTime: number;
  planEndTime: number;
} {
  let times: string[] = [];

  const timeList = changeTimeList.map(item => {
    const startDate = item.date[0].format('YYYY-MM-DD');
    const endDate = item.date[1].format('YYYY-MM-DD');
    const startHour = item.time[0].format('HH:mm');
    const endHour = item.time[1].format('HH:mm');
    times.push(`${startDate} ${startHour}:00`);
    times.push(`${endDate} ${endHour}:00`);
    return {
      startDate,
      endDate,
      startHour,
      endHour,
    };
  });
  times = sortBy(times);
  const planStartTime = new Date(times[0]).getTime();
  const planEndTime = new Date(times[times.length - 1]).getTime();
  return {
    planStartTime,
    planEndTime,
    changeTimeList: timeList,
  };
}
