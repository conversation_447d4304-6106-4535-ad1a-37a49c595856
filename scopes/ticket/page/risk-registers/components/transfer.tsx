import React, { useMemo, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/iam.ui.user-select';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { transferRiskRegister } from '@manyun/ticket.service.transfer-risk-register';

export type TransferProps = {
  riskId: string;
  blockGuid?: string | null;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function TransferButton({ riskId, blockGuid, onSuccess, ...restProps }: TransferProps) {
  const [visible, setVisible] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm();
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  return (
    <>
      <Button
        {...restProps}
        onClick={() => {
          setVisible(true);
        }}
      >
        {locales.transfer._self}
      </Button>
      <Modal
        title={locales.transfer._self}
        open={visible}
        okText={locales.transfer.confirmText}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={async () => {
          form.validateFields().then(async values => {
            setSubmitLoading(true);
            const { error } = await transferRiskRegister({
              riskId,
              ...values,
            });
            setSubmitLoading(false);

            if (error) {
              message.error(error.message);
              return;
            }
            message.success(locales.transfer.successMessage);
            setVisible(false);
            onSuccess();
          });
        }}
      >
        <Form labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} form={form}>
          <Form.Item
            label={locales.transfer.transferUser}
            name="transferUserId"
            rules={[{ required: true, message: locales.transfer.transferUserRequiredMessage }]}
          >
            <UserSelect
              includeCurrentUser={false}
              labelInValue={false}
              blockGuid={blockGuid ?? undefined}
            />
          </Form.Item>
          <Form.Item
            label={locales.transfer.transferReason}
            name="transferReason"
            rules={[
              {
                whitespace: true,
                max: 300,
                message: locales.transfer.transferReasonMaxLengthMessage,
              },
            ]}
          >
            <Input.TextArea rows={3} showCount maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
