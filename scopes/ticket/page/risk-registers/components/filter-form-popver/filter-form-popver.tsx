import { FilterOutlined } from '@ant-design/icons';
import omit from 'lodash.omit';
import moment from 'moment';
import React, { useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import type { RiskClearStatus, RiskStatus } from '@manyun/ticket.gql.client.risk-register';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import type { SvcQuery } from '@manyun/ticket.service.fetch-paged-risk-registers';

type SearchField = Omit<SvcQuery, 'pageNum' | 'pageSize' | 'blockGuids' | 'riskOwnerInfoList'> & {
  blockGuids?: string;
  riskOwnerInfoList?: number[];
};

export type FilterFormPopoverProps = {
  values?: SearchField;
  isFull: boolean;
  onSearch: (formValues: SearchField) => void;
};

export default function FilterFormPopover({ values, isFull, onSearch }: FilterFormPopoverProps) {
  const [visible, setVisible] = useState(false);
  const hasFilter = useDeepCompareMemo(() => {
    return values && Object.values(values).some(value => value !== undefined);
  }, [values]);

  const [form] = Form.useForm();
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const formItemProps = [
    {
      label: locales.location,
      name: 'blockGuids',
      colSpan: 12,
      children: <LocationTreeSelect authorizedOnly allowClear includeVirtualBlocks />,
    },
    {
      label: locales.riskCategory,
      name: 'riskTopTypeCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          allowClear
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.RISK_TOP_TYPE}
          showArrow
        />
      ),
    },

    {
      label: locales.riskType,
      name: 'riskSecTypeCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          allowClear
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.RISK_SEC_TYPE}
          showArrow
        />
      ),
    },
    {
      label: locales.riskLevel._self,
      name: 'riskLevels',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          mode="multiple"
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType="RISK_LEVEL"
        />
      ),
    },
    {
      label: locales.riskStatus._self,
      name: 'riskStatus',
      colSpan: 12,
      children: (
        <Select
          options={Object.keys(locales.riskStatus.enum).map(status => ({
            label: locales.riskStatus.enum[status as RiskStatus],
            value: status,
          }))}
          allowClear
          mode="multiple"
          showArrow
        />
      ),
    },
    {
      label: locales.riskClearStatus._self,
      name: 'riskClearStatus',
      colSpan: 12,
      children: (
        <Select
          options={Object.keys(locales.riskClearStatus.enum).map(status => ({
            label: locales.riskClearStatus.enum[status as RiskClearStatus],
            value: status,
          }))}
          allowClear
        />
      ),
    },
    {
      label: locales.deviceName,
      name: 'deviceGuid',
      colSpan: 12,
      children: <DeviceSelect allowClear showDeviceLabel />,
    },
    {
      label: locales.relateName,
      name: 'relateName',
      colSpan: 12,
      children: <Input allowClear />,
    },
    {
      label: locales.riskResourceCode,
      name: 'riskResourceCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.RISK_RESOURCE}
          allowClear
          showArrow
        />
      ),
    },
    {
      label: locales.riskOwner,
      name: 'riskOwnerId',
      colSpan: 12,
      children: <UserSelect allowClear labelInValue={false} />,
    },
    {
      label: locales.registrant,
      name: 'createUserId',
      colSpan: 12,
      children: <UserSelect allowClear labelInValue={false} />,
    },
    {
      label: locales.createTime,
      name: 'createTime',
      colSpan: 12,
      children: <DatePicker.RangePicker format="YYYY-MM-DD" />,
    },
    {
      label: locales.updateTime,
      name: 'updateTime',
      colSpan: 12,
      children: <DatePicker.RangePicker format="YYYY-MM-DD HH:mm:ss" />,
    },
  ];
  const formItemFullProps = [
    {
      label: locales.location,
      name: 'blockGuids',
      colSpan: 12,
      children: <LocationTreeSelect authorizedOnly allowClear includeVirtualBlocks />,
    },
    {
      label: locales.riskStatus._self,
      name: 'riskStatus',
      colSpan: 12,
      children: (
        <Select
          options={Object.keys(locales.riskStatus.enumFull).map(status => ({
            label: locales.riskStatus.enumFull[status as RiskStatus],
            value: status,
          }))}
          allowClear
          mode="multiple"
          showArrow
        />
      ),
    },
    {
      label: locales.riskResourceCode,
      name: 'riskResourceCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.RISK_RESOURCE}
          allowClear
          showArrow
        />
      ),
    },
    {
      label: '风险专业',
      name: 'riskTopTypeCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          allowClear
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.RISK_TOP_TYPE}
          showArrow
        />
      ),
    },

    {
      label: locales.riskType,
      name: 'riskSecTypeCode',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          allowClear
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType={MetaType.RISK_SEC_TYPE}
          showArrow
        />
      ),
    },
    {
      label: locales.riskLevel._self,
      name: 'riskLevels',
      colSpan: 12,
      children: (
        <MetaTypeSelect
          mode="multiple"
          showSearch
          optionLabelProp="label"
          optionFilterProp="label"
          metaType="RISK_LEVEL"
        />
      ),
    },

    {
      label: '长期风险项',
      name: 'longTermRisk',
      colSpan: 12,
      children: (
        <Select
          // style={{ width: 210 }}
          placeholder=" "
          options={[
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ]}
          allowClear
        />
      ),
    },
    {
      label: '升级ROC',
      name: 'upgradeRoc',
      colSpan: 12,
      children: (
        <Select
          // style={{ width: 210 }}
          placeholder=" "
          options={[
            {
              label: '是',
              value: true,
            },
            {
              label: '否',
              value: false,
            },
          ]}
          allowClear
        />
      ),
    },
    {
      label: locales.deviceName,
      name: 'deviceGuid',
      colSpan: 12,
      children: <DeviceSelect showDeviceLabel allowClear />,
    },
    {
      label: '包间编号',
      name: 'relateName',
      colSpan: 12,
      children: <Input allowClear />,
    },

    {
      label: locales.riskOwner,
      name: 'riskOwnerId',
      colSpan: 12,
      children: <UserSelect allowClear labelInValue={false} />,
    },
    {
      label: '识别人',
      name: 'riskIdentifier',
      colSpan: 12,
      children: <Input allowClear />,
    },
    {
      label: '创建时间',
      name: 'createTime',
      colSpan: 12,
      children: <DatePicker.RangePicker format="YYYY-MM-DD" />,
    },
    {
      label: locales.updateTime,
      name: 'updateTime',
      colSpan: 12,
      children: <DatePicker.RangePicker format="YYYY-MM-DD" />,
    },
  ];
  return (
    <Dropdown
      open={visible}
      dropdownRender={() => (
        <Card style={{ width: 544 }}>
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              {(isFull ? formItemFullProps : formItemProps).map(itemProps => {
                const { label, name, colSpan, children, ...rest } = itemProps;
                return (
                  <Col key={name} span={colSpan}>
                    <Form.Item label={label} name={name} {...rest}>
                      {children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Row justify="end">
              <Space>
                <Button
                  onClick={() => {
                    form.resetFields();
                    onSearch({
                      ...form.getFieldsValue(),
                      createBeginTime: undefined,
                      createEndTime: undefined,
                      updateBeginTime: undefined,
                      updateEndTime: undefined,
                    });
                    setVisible(false);
                  }}
                >
                  {locales.filter.reset}
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    const fields = form.getFieldsValue();
                    onSearch({
                      ...omit(fields, 'createTime', 'updateTime'),
                      createBeginTime: fields.createTime
                        ? moment(fields.createTime[0]).startOf('day').valueOf()
                        : undefined,
                      createEndTime: fields.createTime
                        ? moment(fields.createTime[1]).endOf('day').valueOf()
                        : undefined,
                      updateBeginTime: fields.updateTime
                        ? moment(fields.updateTime[0]).startOf('day').valueOf()
                        : undefined,
                      updateEndTime: fields.updateTime
                        ? moment(fields.updateTime[1]).endOf('day').valueOf()
                        : undefined,
                    });
                    setVisible(false);
                  }}
                >
                  {locales.filter.search}
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={_visible => {
        if (_visible === true && values) {
          form.setFieldsValue({
            ...values,
            createTime:
              values.createBeginTime && values.createEndTime
                ? [moment(values.createBeginTime), moment(values.createEndTime)]
                : undefined,
            updateTime:
              values.updateBeginTime && values.updateEndTime
                ? [moment(values.updateBeginTime), moment(values.updateEndTime)]
                : undefined,
          });
        }
        setVisible(_visible);
      }}
    >
      <Tooltip title={locales.filter._self}>
        <Button
          icon={<FilterOutlined />}
          ghost={hasFilter}
          type={hasFilter ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}
