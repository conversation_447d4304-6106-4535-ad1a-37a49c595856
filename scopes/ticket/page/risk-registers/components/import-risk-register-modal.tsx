import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import React, { useMemo, useState } from 'react';

import { Button, type ButtonProps } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { downloadRiskRegisterTemplate } from '@manyun/ticket.service.download-risk-register-template';
import { importRiskRegister } from '@manyun/ticket.service.import-risk-register';
import type { RiskRegister, RiskRegisterImport } from '@manyun/ticket.service.import-risk-register';

export type ImportRiskRegisterModalProps = {
  isFull: boolean;
  onSuccess: () => void;
} & Pick<ButtonProps, 'disabled'>;

export function ImportRiskRegisterModal({ isFull, onSuccess }: ImportRiskRegisterModalProps) {
  const [visible, setVisible] = useState(false);
  const [riskRegistersImport, setRiskRegistersImport] = useState<RiskRegisterImport>();

  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await downloadRiskRegisterTemplate();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `${locales.importRisk.downloadTemplateName}.xlsx`);
  };

  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
      >
        {locales.importRisk._self}
      </Button>
      <Modal
        width={isFull ? 1024 : 1280}
        style={{ height: '80%' }}
        open={visible}
        title={locales.importRisk._self}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setVisible(false);
          setRiskRegistersImport(undefined);
        }}
      >
        <Row justify="space-between" style={{ marginBottom: 16 }}>
          <Space>
            <Upload
              showUploadList={false}
              accept=".csv,.xls,.xlsx"
              maxCount={1}
              customRequest={async ({ file }) => {
                setImportLoading(true);
                const fd = new FormData();
                fd.append('file', file);
                const { data, error } = await importRiskRegister(fd);
                setImportLoading(false);
                setRiskRegistersImport(data);
                if (error) {
                  message.error(error.message);
                  return;
                }
                if (data.faultTotal === 0) {
                  message.success(locales.importRisk.successMessage);
                  setVisible(false);
                  setRiskRegistersImport(undefined);
                  onSuccess();
                }
              }}
            >
              <Button loading={importLoading} type="primary">
                {locales.importRisk.importBtnText}
              </Button>
            </Upload>
            <Button type="link" loading={exportLoading} compact onClick={handleFileExport}>
              <DownloadOutlined />
              {locales.importRisk.downloadTemplate}
            </Button>
          </Space>
          {riskRegistersImport?.excelCheckDtos && (
            <Typography.Text style={{ marginLeft: 'auto' }}>
              导入失败，{riskRegistersImport.faultTotal}条不符合填写规范
            </Typography.Text>
          )}
        </Row>

        <Table
          size="middle"
          scroll={{ x: 'max-content' }}
          loading={importLoading}
          dataSource={riskRegistersImport ? riskRegistersImport.excelCheckDtos : []}
          columns={
            isFull
              ? [
                  {
                    title: locales.riskDesc,
                    dataIndex: ['data', 'riskDesc'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskDesc, errMessage, 'riskDesc'),
                  },
                  {
                    title: locales.location,
                    dataIndex: ['data', 'blockGuid'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.blockGuid, errMessage, 'blockGuid'),
                  },
                  {
                    title: locales.resource,
                    dataIndex: ['data', 'riskResource'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskResource, errMessage, 'riskResource'),
                  },
                  {
                    title: isFull ? '风险专业' : locales.riskCategory,
                    dataIndex: ['data', 'riskTopType'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskTopType, errMessage, 'riskTopType'),
                  },
                  {
                    title: locales.riskType,
                    dataIndex: ['data', 'riskSecType'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskSecType, errMessage, 'riskSecType'),
                  },
                  {
                    title: locales.riskLevel._self,
                    dataIndex: ['data', 'riskLevel'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskLevel, errMessage, 'riskLevel'),
                  },
                  {
                    title: locales.riskOwner,
                    dataIndex: ['data', 'riskOwnerName'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskOwnerName, errMessage, 'riskOwnerName'),
                  },
                ]
              : [
                  {
                    title: locales.riskDesc,
                    dataIndex: ['data', 'riskDesc'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskDesc, errMessage, 'riskDesc'),
                  },
                  {
                    title: locales.location,
                    dataIndex: ['data', 'blockGuid'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.blockGuid, errMessage, 'blockGuid'),
                  },
                  {
                    title: locales.resource,
                    dataIndex: ['data', 'riskResource'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskResource, errMessage, 'riskResource'),
                  },
                  {
                    title: '关联事件单号',
                    dataIndex: ['data', 'relateEventId'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.relateEventId, errMessage, 'relateEventId'),
                  },
                  {
                    title: isFull ? '风险专业' : locales.riskCategory,
                    dataIndex: ['data', 'riskTopType'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskTopType, errMessage, 'riskTopType'),
                  },
                  {
                    title: locales.riskType,
                    dataIndex: ['data', 'riskSecType'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskSecType, errMessage, 'riskSecType'),
                  },
                  {
                    title: locales.riskLevel._self,
                    dataIndex: ['data', 'riskLevel'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskLevel, errMessage, 'riskLevel'),
                  },
                  {
                    title: locales.riskObjectType._self,
                    dataIndex: ['data', 'riskObjectType'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskObjectType, errMessage, 'riskObjectType'),
                  },
                  {
                    title: locales.riskObjectName,
                    dataIndex: ['data', 'riskObjectName'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskObjectName, errMessage, 'riskObjectName'),
                  },
                  {
                    title: locales.riskOwner,
                    dataIndex: ['data', 'riskOwnerName'],
                    render: (_, { data, errMessage }) =>
                      getToolTilp(data.riskOwnerName, errMessage, 'riskOwnerName'),
                  },
                ]
          }
        />
      </Modal>
    </>
  );
}

type RiskRegisterImportColumnKey = keyof RiskRegister;

function getToolTilp(
  value: string,
  errMessage: Record<string, string>,
  dataType: RiskRegisterImportColumnKey
) {
  if (Object.keys(errMessage).includes(dataType)) {
    return (
      <Space key={dataType}>
        <Typography.Text ellipsis type="danger">
          {value ?? '--'}
        </Typography.Text>
        <Tooltip title={errMessage[dataType]}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  }
  return <span key={dataType}> {value ?? '--'}</span>;
}
