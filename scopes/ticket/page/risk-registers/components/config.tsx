import { SettingOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';

import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  useLazyRiskRegisterConfig,
  useUpdateRiskRegisterConfig,
} from '@manyun/ticket.gql.client.risk-register';

export function RiskRegisterConfig({
  isFull,
  onSuccess,
}: {
  isFull: boolean;
  onSuccess: () => void;
}) {
  const [visible, setVisible] = useState(false);
  const [getRiskRegisterConfig, { data, loading }] = useLazyRiskRegisterConfig();

  const [updateRiskRegisterConfig] = useUpdateRiskRegisterConfig();
  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      getRiskRegisterConfig();
    }
  }, [visible, getRiskRegisterConfig]);

  return (
    <>
      <Button
        type="link"
        icon={<SettingOutlined />}
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        配置
      </Button>
      <Modal
        maskClosable={false}
        open={visible}
        width={540}
        title="配置"
        destroyOnClose
        bodyStyle={{ paddingRight: 48 }}
        onOk={() => {
          form.validateFields().then(async values => {
            updateRiskRegisterConfig({
              variables: {
                query: {
                  configuration: Object.keys(values).map(item => ({
                    riskTopTypeCode: item,
                    roleCodeList: values[item],
                  })),
                },
              },
              onCompleted(data) {
                if (!data.updateRiskRegisterConfig?.success) {
                  message.error(data.updateRiskRegisterConfig?.message);
                } else {
                  setVisible(false);
                  onSuccess();
                }
              },
            });
          });
        }}
        onCancel={() => setVisible(false)}
      >
        <Space direction="vertical" size="large">
          <Typography.Text>
            请为不同{isFull ? '专业' : '类别'}的风险，配置可查看的角色（如未配置，则不对角色限权）：
          </Typography.Text>
          <Form form={form} preserve={false} labelCol={{ flex: '110px' }} labelWrap>
            {!loading &&
              data?.riskRegisterConfig?.data.map(item => (
                <Form.Item
                  key={item.riskTopTypeCode}
                  label={item.riskTopTypeName}
                  name={item.riskTopTypeCode!}
                  initialValue={item.roleInfoList?.map(item => item?.roleCode)}
                >
                  <RoleSelect
                    allowClear
                    trigger="onDidMount"
                    labelInvalue
                    mode="multiple"
                    fieldNames={{ value: 'code', label: 'name' }}
                  />
                </Form.Item>
              ))}
          </Form>
        </Space>
      </Modal>
    </>
  );
}
