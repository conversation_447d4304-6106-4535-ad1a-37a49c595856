import dayjs from 'dayjs';
import omit from 'lodash.omit';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { withdrawTodo } from '@manyun/bpm.service.withdraw-todo';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  type RiskRegisterBaseInfoResponse,
  type RiskStatus,
  useDeleteRiskRegister,
} from '@manyun/ticket.gql.client.risk-register';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import {
  EditFormDrawer,
  PopoverUser,
  RiskObjectLink,
} from '@manyun/ticket.page.risk-register-detail';
import { RiskRegisterLocationDrawer } from '@manyun/ticket.page.risk-register-mutator';
import {
  RISK_REGISTERS_NEW,
  generateRiskRegisterDetailLocation,
} from '@manyun/ticket.route.ticket-routes';
import { changeRiskRegisterStatus } from '@manyun/ticket.service.change-risk-register-status';
import { exportRiskRegister } from '@manyun/ticket.service.export-risk-register';
import { fetchPagedRiskRegisters } from '@manyun/ticket.service.fetch-paged-risk-registers';
import type {
  SortField,
  SvcQuery,
  SvcRespData,
} from '@manyun/ticket.service.fetch-paged-risk-registers';

import { RiskRegisterConfig } from './components/config';
import FilterFormPopover from './components/filter-form-popver/filter-form-popver';
import { ImportRiskRegisterModal } from './components/import-risk-register-modal';
import { TransferButton } from './components/transfer';

type SearchField = SvcQuery & {
  createTime?: number[];
  updateTime?: number[];
};
export type Action = 'add' | 'edit' | 'delete' | 'updateStatus' | 'sync';

export const riskStatusColorMaps: Record<RiskStatus, string> = {
  DRAFT: 'default',
  WAITING_IDENTIFY: 'blue',
  WAITING_EVALUATE: 'blue',
  HANDLING: 'blue',
  APPROVING: 'warning',
  CLOSED: 'default',
};
const riskStatusColorFullMaps: Record<RiskStatus, string> = {
  DRAFT: 'default',
  WAITING_IDENTIFY: 'processing',
  WAITING_EVALUATE: 'warning',
  HANDLING: 'processing',
  APPROVING: 'warning',
  CLOSED: 'success',
};

const sorterMaps: Record<string, SortField> = {
  measureProgress: 'measure_progress',
  planCompleteTime: 'plan_complete_time',
  completeTime: 'complete_time',
  createdAt: 'gmt_create',
  modifiedAt: 'last_operation_time',
};

export function RiskRegisters() {
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const [loading, setLoading] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [exportLoading, setExportLoading] = useState(false);
  const [, { checkUserId, checkCode }] = useAuthorized();
  const isShowConfig = checkCode('element_ticket_risk-register-config');
  const { userId } = getUserInfo();

  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';

  const [tableColumns, setTableColumns] = useState<ColumnType<RiskRegisterBaseInfoResponse>[]>([]);

  useEffect(() => {
    let list: ColumnType<RiskRegisterBaseInfoResponse>[] = [
      {
        title: '风险ID',
        dataIndex: 'id',
        fixed: 'left',
        disabled: true,
        render: (_, { id, planCompleteTime, riskStatus, completeTime }) => {
          if (id) {
            let isTimeout = false;
            if (isFull) {
              if (
                ['APPROVING', 'CLOSED'].includes(riskStatus) &&
                completeTime &&
                planCompleteTime &&
                new Date(completeTime).getTime() > moment(planCompleteTime).valueOf()
              ) {
                isTimeout = true;
              }
              if (
                !['APPROVING', 'CLOSED'].includes(riskStatus) &&
                planCompleteTime &&
                new Date().getTime() > moment(planCompleteTime).valueOf()
              ) {
                isTimeout = true;
              }
            }
            return (
              <Link
                target="_blank"
                to={generateRiskRegisterDetailLocation({ id: encodeURIComponent(id) })}
              >
                {id}
                {isTimeout && (
                  <Tag style={{ marginLeft: 8 }} color="warning">
                    超期
                  </Tag>
                )}
              </Link>
            );
          }
          return '--';
        },
      },
      {
        title: locales.location,
        dataIndex: 'blockGuid',
        render: (_, { idcTag, blockGuid }) => (blockGuid ? blockGuid : (idcTag ?? '--')),
      },
      {
        title: locales.resource,
        dataIndex: 'riskResourceName',
        render: text => text ?? '--',
      },
      {
        title: isFull ? '风险专业' : locales.riskCategory,
        dataIndex: 'riskCategoryName',
        render: (_, { riskCategoryName }) => riskCategoryName ?? '--',
      },
      {
        title: locales.riskType,
        dataIndex: 'riskTypeName',
        render: (_, { riskTypeName }) => riskTypeName ?? '--',
      },
      {
        title: locales.riskLevel._self,
        dataIndex: 'riskLevel',
        render: (_, { riskLevel, riskLevelName }) =>
          riskLevel ? (
            isFull ? (
              <MetaTypeText code={riskLevel} metaType={MetaType.RISK_LEVEL} />
            ) : (
              riskLevelName
            )
          ) : (
            '--'
          ),
      },
      {
        title: locales.riskDesc,
        dataIndex: 'riskDesc',
        width: 280,
        ellipsis: { showTitle: false },
        render: (_, { riskDesc }) =>
          riskDesc ? (
            <Tooltip placement="topLeft" title={riskDesc}>
              <Typography.Text style={{ maxWidth: '400px' }} ellipsis>
                {riskDesc}
              </Typography.Text>
            </Tooltip>
          ) : (
            '--'
          ),
      },
      {
        title: locales.riskObjectType._self,
        dataIndex: 'riskObjectType',
        render: (_, { riskObjectType }) =>
          riskObjectType ? locales.riskObjectType.enum[riskObjectType] : '--',
      },
      {
        title: '长期风险项',
        dataIndex: 'longTermRisk',
        render: (_, { longTermRisk }) => {
          if (longTermRisk === true) {
            return (
              <>
                <Badge status="warning" style={{ marginRight: 8 }} />是
              </>
            );
          }
          if (longTermRisk === false) {
            return (
              <>
                <Badge status="default" style={{ marginRight: 8 }} />否
              </>
            );
          }
          return '--';
        },
      },
      {
        title: '升级ROC',
        dataIndex: 'upgradeRoc',
        render: (_, { upgradeRoc }) => {
          if (upgradeRoc === true) {
            return (
              <>
                <Badge status="warning" style={{ marginRight: 8 }} />是
              </>
            );
          }
          if (upgradeRoc === false) {
            return (
              <>
                <Badge status="default" style={{ marginRight: 8 }} />否
              </>
            );
          }
          return '--';
        },
      },
      {
        title: '风险位置',
        dataIndex: 'riskLocation',
        render: (_, { id }) => {
          return <RiskRegisterLocationDrawer riskId={id} />;
        },
      },
      {
        title: locales.riskObjectName,
        dataIndex: 'riskObjects',
        width: 200,
        ellipsis: { showTitle: false },
        render: (_, { riskObjects, riskObjectType }) =>
          riskObjects && riskObjectType ? (
            <div style={{ maxWidth: 200 }}>
              <RiskObjectLink objects={riskObjects} type={riskObjectType} />
            </div>
          ) : (
            '--'
          ),
      },
      {
        title: locales.riskInfluenceDesc,
        dataIndex: 'riskInfluenceDesc',
        width: 400,
        ellipsis: { showTitle: false },
        show: false,
        render: (_, { riskInfluenceDesc }) => (
          <Tooltip placement="topLeft" title={riskInfluenceDesc}>
            <Typography.Text style={{ maxWidth: '400px' }} ellipsis>
              {riskInfluenceDesc}
            </Typography.Text>
          </Tooltip>
        ),
      },
      {
        title: locales.riskStatus._self,
        dataIndex: 'riskStatus',
        render: (_, { riskStatus }) =>
          riskStatus ? (
            <Tag
              color={isFull ? riskStatusColorFullMaps[riskStatus] : riskStatusColorMaps[riskStatus]}
            >
              {isFull
                ? locales.riskStatus.enumFull[riskStatus]
                : locales.riskStatus.enum[riskStatus]}
            </Tag>
          ) : (
            '--'
          ),
      },
      {
        title: locales.measureProgress,
        dataIndex: 'measureProgress',
        sorter: true,
        render: text => (text === null ? '--' : `${text}%`),
      },
      {
        title: locales.planCompleteTime,
        dataIndex: 'planCompleteTime',
        sorter: true,
        render: (text, { riskStatus, planCompleteTime, completeTime }) => {
          if (text) {
            let isTimeout = false;
            if (isFull) {
              if (
                ['APPROVING', 'CLOSED'].includes(riskStatus) &&
                completeTime &&
                planCompleteTime &&
                new Date(completeTime).getTime() > moment(planCompleteTime).valueOf()
              ) {
                isTimeout = true;
              }
              if (
                !['APPROVING', 'CLOSED'].includes(riskStatus) &&
                planCompleteTime &&
                new Date().getTime() > moment(planCompleteTime).valueOf()
              ) {
                isTimeout = true;
              }
            } else {
              if (
                !['APPROVING', 'CLOSED'].includes(riskStatus) &&
                planCompleteTime &&
                new Date().getTime() > moment(planCompleteTime).valueOf()
              ) {
                isTimeout = true;
              }
            }

            return (
              <Typography.Text type={isTimeout ? 'danger' : undefined}>
                {dayjs(text).format('YYYY-MM-DD HH:mm')}
                {isTimeout && isFull && (
                  <Explanation
                    style={{ color: 'inherit', display: 'inline' }}
                    iconType="question"
                    tooltip={{
                      title: '处理已超过风险单计划完成时间',
                    }}
                  />
                )}
              </Typography.Text>
            );
          }
          return '--';
        },
      },
      {
        title: locales.completeTime,
        dataIndex: 'completeTime',
        sorter: true,
        render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm') : '--'),
      },
      {
        title: locales.riskClearStatus._self,
        dataIndex: 'riskClearStatus',
        render: (_, { riskClearStatus }) => {
          if (!riskClearStatus) {
            return '--';
          }
          return (
            <Badge
              color={riskClearStatus === 'CLEARED' ? 'green' : 'red'}
              text={locales.riskClearStatus.enum[riskClearStatus]}
            />
          );
        },
      },
      ...(isFull
        ? [
            {
              title: locales.riskIdentifier,
              dataIndex: 'riskIdentifier',
              render: (text: any) => text ?? '--',
            },
          ]
        : []),
      {
        title: locales.riskOwner,
        dataIndex: 'riskOwnerIdList',
        width: 144,
        render: text => <PopoverUser riskOwnerIdList={text} userLink={!isFull} width={144} />,
      },
      {
        title: isFull ? '创建时间' : locales.createTime,
        dataIndex: 'createdAt',
        sorter: true,
        render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--'),
      },
      {
        title: locales.updateTime,
        dataIndex: 'modifiedAt',
        sorter: true,
        show: false,
        render: text => (text ? dayjs(text).format('YYYY-MM-DD HH:mm:ss') : '--'),
      },
      {
        title: locales.riskIdentifier,
        dataIndex: 'riskIdentifieryg',
        render: (text, { riskIdentifier }) => riskIdentifier ?? '--',
      },
      {
        title: locales.registrant,
        dataIndex: 'createUser',
        show: !isFull,
        render: text => <UserLink userId={text} />,
      },
      {
        title: locales.operation,
        dataIndex: '_action',
        fixed: 'right',
        disabled: true,
        render: (_, record: RiskRegisterBaseInfoResponse) => {
          const isOwner = (record.riskOwnerIdList ?? []).some(item => checkUserId(item));
          const isCreater = checkUserId(record.createUser);
          const isDelete = checkCode('element_ticket_risk-register-delete');
          const isRestart = checkCode('element_ticket-risk-register-restart');
          return (
            <Space size={16}>
              {isOwner &&
                isFull &&
                record.riskStatus === 'WAITING_EVALUATE' &&
                record.evaluateInstId && (
                  <Button type="link" compact onClick={() => revokeApprove(record.evaluateInstId!)}>
                    撤回审批
                  </Button>
                )}
              {isOwner && isFull && record.riskStatus === 'APPROVING' && record.auditInstId && (
                <Button type="link" compact onClick={() => revokeApprove(record.auditInstId!)}>
                  撤回审批
                </Button>
              )}
              {(isOwner && ['DRAFT', 'WAITING_IDENTIFY', 'HANDLING'].includes(record.riskStatus)) ||
              (isCreater && record.riskStatus === 'DRAFT') ? (
                <EditFormDrawer
                  text={locales.edit._self}
                  title={locales.edit.title}
                  values={record}
                  onSuccess={reloadData}
                />
              ) : null}
              {((!isFull && isDelete && record.riskStatus !== 'CLOSED') ||
                (isFull && record.riskStatus === 'DRAFT' && (isOwner || isCreater))) && (
                <Popconfirm
                  title={locales.delete.title}
                  style={{ width: 290 }}
                  okText={locales.delete.confirmText}
                  cancelText={locales.delete.confirmCancel}
                  placement="bottomRight"
                  okButtonProps={{ disabled: deleteLoading }}
                  onConfirm={() => deleteRiskRegister({ variables: { riskId: record.id } })}
                >
                  <Button compact type="link" disabled={loading}>
                    {locales.delete._self}
                  </Button>
                </Popconfirm>
              )}
              {isOwner && ['WAITING_IDENTIFY', 'HANDLING'].includes(record.riskStatus) && (
                <TransferButton
                  riskId={record.id}
                  blockGuid={record.blockGuid}
                  type="link"
                  compact
                  onSuccess={() => {
                    reloadData();
                  }}
                />
              )}

              {((!isFull && isOwner && record.riskStatus === 'CLOSED') ||
                (isFull && (isRestart || isOwner) && record.riskStatus === 'CLOSED')) && (
                <Popconfirm
                  title={isFull ? '确认重启该风险单？' : locales.restart.title}
                  style={{ width: 290 }}
                  okText={locales.restart.confirmText}
                  cancelText={locales.restart.confirmCancel}
                  placement="bottomRight"
                  onConfirm={() => changeStatus(record)}
                >
                  <Button compact type="link" disabled={loading}>
                    {locales.restart._self}
                  </Button>
                </Popconfirm>
              )}
            </Space>
          );
        },
      },
    ];

    if (isFull) {
      list = list.filter(item => {
        if (
          item.dataIndex === 'riskObjectType' ||
          item.dataIndex === 'riskObjects' ||
          item.dataIndex === 'riskClearStatus' ||
          item.dataIndex === 'riskIdentifier' ||
          item.dataIndex === 'riskInfluenceDesc'
        ) {
          return false;
        }
        return true;
      });
    } else {
      list = list.filter(item => {
        if (
          item.dataIndex === 'upgradeRoc' ||
          item.dataIndex === 'longTermRisk' ||
          item.dataIndex === 'riskIdentifieryg' ||
          item.dataIndex === 'riskLocation'
        ) {
          return false;
        }
        return true;
      });
    }
    setTableColumns(list);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isFull, locales]);

  const [data, setData] = useState<SvcRespData>({
    data: [],
    total: 0,
  });
  const { search } = useLocation();

  const { pageNum, pageSize, ...restDefaultFields } = getLocationSearchMap<
    Partial<
      Omit<SearchField, 'blockGuids' | 'riskOwnerInfoList' | 'longTermRisk' | 'upgradeRoc'> & {
        pageNum: number;
        pageSize: number;
        blockGuids?: string;
        riskOwnerInfoList?: number[];
        longTermRisk?: string;
        upgradeRoc?: string;
      }
    >
  >(search, {
    parseNumbers: true,
    arrayKeys: ['riskLevels', 'riskStatus', 'createTime', 'updateTime', 'riskOwnerInfoList'],
  });
  const [fields, setFields] = useState<
    Omit<SearchField, 'blockGuids' | 'riskOwnerInfoList'> & {
      pageNum: number;
      pageSize: number;
      blockGuids?: string;
      riskOwnerInfoList?: number[];
    }
  >({
    ...restDefaultFields,
    longTermRisk:
      'longTermRisk' in restDefaultFields ? restDefaultFields.longTermRisk === 'true' : undefined,
    upgradeRoc:
      'upgradeRoc' in restDefaultFields ? restDefaultFields.upgradeRoc === 'true' : undefined,
    pageNum: pageNum ? pageNum : 1,
    pageSize: pageSize ? pageSize : 10,
  });
  const history = useHistory();
  const [deleteRiskRegister, { loading: deleteLoading }] = useDeleteRiskRegister({
    onCompleted: data => {
      if (data.deleteRiskRegister.success) {
        reloadData('delete');
      } else {
        message.error(data.deleteRiskRegister.message);
      }
    },
  });

  const getList = useCallback(async () => {
    setTableLoading(true);
    const { error, data: resData } = await fetchPagedRiskRegisters({
      ...fields,
      blockGuids: fields?.blockGuids ? [fields.blockGuids] : undefined,
    });
    setTableLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(resData);
  }, [fields]);

  const revokeApprove = async (riskId: string) => {
    const { error } = await withdrawTodo({ instId: riskId, operator: String(userId) });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('撤回成功');
    reloadData();
    return;
  };

  const reloadData = useCallback(
    (action?: 'delete') => {
      if (action === 'delete') {
        setFields(pre => {
          return {
            ...pre,
            pageNum:
              (pre.pageNum - 1) * pre.pageSize + 1 === data.total && pre.pageNum > 1
                ? pre.pageNum - 1
                : pre.pageNum,
          };
        });
      } else {
        setFields(pre => ({ ...pre }));
      }
    },
    [data.total]
  );

  const handleFileExport = async (type: string) => {
    let _params: Omit<SvcQuery, 'pageNum' | 'pageSize'> = {};
    if (type === 'filtered') {
      _params = omit(fields, 'blockGuids');
      if (fields.blockGuids) {
        _params.blockGuids = [fields.blockGuids];
      }
    }

    setExportLoading(true);
    const { error, data } = await exportRiskRegister(_params);
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return error.message;
    }
    return data;
  };
  const changeStatus = async (record: RiskRegisterBaseInfoResponse) => {
    setLoading(true);
    const { error } = await changeRiskRegisterStatus({
      riskId: record.id,
      optType: 'RESTART',
      curRiskStatus: record.riskStatus,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    reloadData();
  };

  useEffect(() => {
    getList();
  }, [getList]);

  const _tableColumns = features.showIdentifyUser
    ? tableColumns.filter(item => {
        let isReturn = true;

        if (item.dataIndex === 'riskIdentifier') {
          isReturn = features.showIdentifyUser;
        }
        return isReturn;
      })
    : tableColumns;
  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Card>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space style={{ justifyContent: 'space-between', width: '100%' }}>
              <Space size="middle">
                <Button type="primary" onClick={() => history.push(RISK_REGISTERS_NEW)}>
                  {locales.createRisk}
                </Button>
                <ImportRiskRegisterModal isFull={isFull} onSuccess={reloadData} />
                <Input.Search
                  placeholder={locales.searchPlaceholder}
                  allowClear
                  value={fields.riskKey}
                  style={{ width: 248 }}
                  onSearch={getList}
                  onChange={e => {
                    if (e.target.value) {
                      setLocationSearch({
                        ...fields,
                        riskKey: e.target.value,
                        pageNum: 1,
                        pageSize: 10,
                      });
                    } else {
                      setLocationSearch({
                        ...omit(fields, 'riskKey'),
                        pageNum: 1,
                        pageSize: 10,
                      });
                    }
                    setFields({ ...fields, riskKey: e.target.value, pageNum: 1, pageSize: 10 });
                  }}
                />
                <FilterFormPopover
                  values={omit(fields, 'pageNum', 'pageSize', 'riskKey', 'sortField', 'sortOrder')}
                  isFull={isFull}
                  onSearch={formValues => {
                    setLocationSearch({ ...fields, pageNum: 1, pageSize: 10, ...formValues });
                    setFields(pre => ({ ...pre, pageNum: 1, pageSize: 10, ...formValues }));
                  }}
                />
              </Space>
              <Space size="middle">
                {isShowConfig && (
                  <RiskRegisterConfig
                    isFull={isFull}
                    onSuccess={() => {
                      setFields({ ...fields, pageNum: 1, pageSize: 10 });
                      setLocationSearch({
                        ...fields,
                        pageNum: 1,
                        pageSize: 10,
                      });
                    }}
                  />
                )}
                <FileExport
                  filename={`${locales.fileName}.xls`}
                  disabled={exportLoading}
                  data={type => {
                    return handleFileExport(type);
                  }}
                  showExportFiltered
                />
                {_tableColumns.length && (
                  <EditColumns
                    uniqKey="INDEPENDENT_TICKETS_RISK_REGISTER"
                    listsHeight={360}
                    defaultValue={_tableColumns}
                    onChange={columns => {
                      setTableColumns(columns);
                    }}
                  />
                )}
              </Space>
            </Space>

            <Table<RiskRegisterBaseInfoResponse>
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={tableLoading}
              dataSource={data.data}
              columns={_tableColumns}
              pagination={{
                total: data.total,
                current: fields.pageNum,
                pageSize: fields.pageSize,
              }}
              onChange={(pagination, _, sorter, { action }) => {
                if (action === 'paginate') {
                  setFields(pre => ({
                    ...pre,
                    pageNum: pagination.current!,
                    pageSize: pagination.pageSize!,
                  }));
                  setLocationSearch({
                    ...fields,
                    pageNum: pagination.current,
                    pageSize: pagination.pageSize,
                  });
                }
                if (action === 'sort') {
                  if (sorter && !Array.isArray(sorter) && sorter.field) {
                    if (!sorter.order) {
                      setFields(pre => ({
                        ...pre,
                        sortField: null,
                        sortOrder: null,
                      }));
                      setLocationSearch({
                        ...fields,
                        sortField: null,
                        sortOrder: null,
                      });
                      return;
                    }
                    setLocationSearch({
                      ...fields,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
                    });
                    setFields(pre => ({
                      ...pre,
                      sortField: sorterMaps[sorter.field as string],
                      sortOrder: sorter.order === 'ascend' ? 'asc' : 'desc',
                    }));
                  }
                }
              }}
            />
          </Space>
        </Card>
      </Space>
    </>
  );
}
