import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import React, { useEffect } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import type { RiskLevel } from '@manyun/ticket.gql.client.risk-register';

export type RiskRegisterFormProps = {
  form: FormInstance<RiskPoolFormValues>;
  values?: RiskPoolFormValues;
};
export type RiskPoolFormValues = {
  riskCategory: string;
  riskType: string;
  riskLevel: RiskLevel;
  riskDesc: string;
  verifyList: string[];
  fileInfoList?: McUploadFileJSON[] | null;
};

export function RiskToolForm({ form, values }: RiskRegisterFormProps) {
  const fileInfoList = Form.useWatch('fileInfoList', form);
  const [configUtil] = useConfigUtil();
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const features = riskRegisters.features;
  // @ts-ignore type error
  const baseInfo = features?.baseInfo;
  const isFull = baseInfo === 'full';
  const text = isFull ? '风险专业' : '风险类别';
  useEffect(() => {
    values && form.setFieldsValue(values);
  }, [values, form]);

  useEffect(() => {
    !values && form.setFieldsValue({ verifyList: [''] });
    //  eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Form form={form} labelCol={{ flex: ' 0 0 110px' }} wrapperCol={{ flex: '0 0 375px' }}>
        <Form.Item
          label={text}
          name="riskCategory"
          rules={[
            {
              required: true,
              message: '风险类别必选！',
            },
          ]}
        >
          <MetaTypeSelect
            showSearch
            optionLabelProp="label"
            optionFilterProp="label"
            metaType={MetaType.RISK_TOP_TYPE}
          />
        </Form.Item>
        <Form.Item
          label="风险类型"
          name="riskType"
          rules={[
            {
              required: true,
              message: '风险类型必选！',
            },
          ]}
        >
          <MetaTypeSelect
            showSearch
            optionLabelProp="label"
            optionFilterProp="label"
            metaType={MetaType.RISK_SEC_TYPE}
          />
        </Form.Item>
        <Form.Item
          label="风险等级"
          name="riskLevel"
          rules={[
            {
              required: true,
              message: '风险等级必选！',
            },
          ]}
        >
          <MetaTypeSelect
            showSearch
            allowClear
            optionLabelProp="label"
            optionFilterProp="label"
            metaType="RISK_LEVEL"
          />
        </Form.Item>
        <Form.Item
          label="风险点描述"
          name="riskDesc"
          rules={[
            {
              required: true,
              message: '风险点描述必填！',
            },
            { whitespace: true, max: 300, message: '最多输入 300 个字符！' },
          ]}
        >
          <Input.TextArea rows={3} showCount maxLength={300} />
        </Form.Item>
        <Form.List
          name="verifyList"
          rules={[
            {
              validator: async (_, names) => {
                if (!names || names.length < 1) {
                  return Promise.reject(new Error('至少添加一项'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          {(fields, { add, remove }, { errors }) => (
            <>
              {fields.map((field, index) => (
                <Form.Item
                  key={field.key}
                  label={index === 0 ? '验证方式' : ' '}
                  required={index === 0}
                  colon={index === 0}
                >
                  <Space>
                    <Form.Item
                      {...field}
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: `请输入验证方式`,
                        },
                        {
                          max: 300,
                          message: '最多输入 300 个字符！',
                        },
                      ]}
                      noStyle
                    >
                      <Input.TextArea rows={3} showCount maxLength={300} style={{ width: 375 }} />
                    </Form.Item>
                    {fields.length > 1 ? (
                      <MinusCircleOutlined onClick={() => remove(field.name)} />
                    ) : null}
                  </Space>
                </Form.Item>
              ))}
              <Form.Item
                label=" "
                colon={false}
                labelCol={{ flex: ' 0 0 110px' }}
                wrapperCol={{ span: 16 }}
              >
                <Button
                  style={{ width: 375 }}
                  type="dashed"
                  disabled={fields.length >= 20}
                  icon={<PlusOutlined />}
                  onClick={() => {
                    add();
                  }}
                >
                  添加验证方式
                </Button>
                <Form.ErrorList errors={errors} />
              </Form.Item>
            </>
          )}
        </Form.List>
        <Form.Item
          label="附件"
          name="fileInfoList"
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
          wrapperCol={{ flex: '0 0 500px' }}
        >
          <Upload
            showAccept
            accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
            maxCount={5}
            maxFileSize={20}
          >
            {fileInfoList && fileInfoList?.length >= 5 ? null : (
              <Button icon={<UploadOutlined />}>点此上传</Button>
            )}
          </Upload>
        </Form.Item>
      </Form>
    </Space>
  );
}
