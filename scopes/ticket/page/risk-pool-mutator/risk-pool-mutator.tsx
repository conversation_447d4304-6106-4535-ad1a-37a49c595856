import React from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useCreateRiskPool } from '@manyun/ticket.gql.client.risk-register';
import { RISK_POOL_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';

import { RiskToolForm } from './components/risk-tool-form';
import type { RiskPoolFormValues } from './components/risk-tool-form';

export function RiskPoolMutator() {
  const [form] = Form.useForm<RiskPoolFormValues>();
  const history = useHistory();

  const [createRiskPool, { loading }] = useCreateRiskPool({
    onCompleted(data) {
      if (!data.createRiskPool.success) {
        message.error(data.createRiskPool?.message);
        return;
      }
      message.success('新建成功');
      history.push(RISK_POOL_ROUTE_PATH);
    },
  });

  const onSubmit = async () => {
    form.validateFields().then(async values => {
      createRiskPool({
        variables: {
          query: {
            ...values,
            fileInfoList: values.fileInfoList
              ? values.fileInfoList.map(item => McUploadFile.fromJSON(item))
              : null,
          },
        },
      });
    });
  };

  return (
    <Card
      style={{ height: '100%', width: '100%' }}
      bodyStyle={{ width: '50%', marginRight: 'auto', marginLeft: 'auto' }}
      bordered={false}
      title={
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
      }
    >
      <RiskToolForm form={form} />
      <Form.Item
        label=" "
        colon={false}
        labelCol={{ flex: ' 0 0 110px' }}
        wrapperCol={{ span: 16 }}
      >
        <Space>
          <Button loading={loading} type="primary" onClick={onSubmit}>
            提交
          </Button>
          <Button loading={loading} onClick={() => history.push(RISK_POOL_ROUTE_PATH)}>
            取消
          </Button>
        </Space>
      </Form.Item>
    </Card>
  );
}
