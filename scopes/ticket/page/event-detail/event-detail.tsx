import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useLatest } from 'react-use';

import { User } from '@manyun/auth-hub.ui.user';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
// import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
// import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { useLazyDrillOrderApproval } from '@manyun/ticket.gql.client.drill-order';
import {
  type DrillOrderApprovalNode,
  useCheckEventCanAudit,
} from '@manyun/ticket.gql.client.tickets';
import type { ProcessEngineConfig } from '@manyun/ticket.model.event';
import {
  BackendEventStatusCode,
  EventSpecificProcessStatus,
  EventSpecificProcessStatusNumberValueMap,
} from '@manyun/ticket.model.event';
import { fetchEventConfiguration } from '@manyun/ticket.service.fetch-event-configuration';
import {
  getEventDetailAction,
  getEventLifeAction,
  selectEventDetail,
  selectEventDetailLoading,
} from '@manyun/ticket.state.event';

import { EventDataContext, initialValue } from './components/event-data-context';
import type { EventTab } from './components/event-data-context';
import { EventInfo } from './components/event-info';
import { EventOperationBar } from './components/event-operation-bar';
import { EventStatusSteps } from './components/event-status-steps';
import { EventTabs } from './components/event-tabs';
import { ProcessingRecords } from './components/processing-records';
import { EventSpecificOperationBar } from './specific-components/event-specific-operation-bar';
import { EventLocationModalButton } from './specific-components/event-specific-process-info';
import { EventSpecificStatusSteps } from './specific-components/event-specific-status-steps';

export function EventDetail() {
  // const config = useSelector(selectCurrentConfig);
  // const configUtil = new ConfigUtil(config);
  const [configUtil] = useConfigUtil();
  const eventCenterInfo = useSelector(selectEventDetail);
  const [{ pdfTabList: initTab }] = initialValue;
  const { eventStatus } = eventCenterInfo;
  const [processConfig, setProcessConfig] = useState<ProcessEngineConfig | undefined>();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureIsEventConfigWithProcessEngine = features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';
  const [getDrillOrderProgress] = useLazyDrillOrderApproval();
  const featuresOnwerMultiple = features.onwerMultiple;
  const featureSpecialInfoSort = features.specialInfoSort;

  const [checkEventCanAudit] = useCheckEventCanAudit({
    onCompleted(data) {
      if (
        data.checkEventCanAudit?.code &&
        data.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
      ) {
        message.error('需关闭所有关联风险单后，才可提交复盘或评审');
        return;
      }
      setTabKey('checkingRecords');
    },
  });

  const { id: inInString } = useParams<{ id: string }>();
  const [tabKey, setTabKey] = useState<string>('eventProgress');
  const [, { checkUserId, checkCode }] = useAuthorized();
  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const id = useMemo(() => {
    if (isNaN(Number(inInString))) {
      return eventCenterInfo.id;
    }
    return Number(inInString);
  }, [inInString, eventCenterInfo]);
  const exportPdfInterval = useRef<NodeJS.Timer | null>(null);

  const [drawerVisible, setDrawerVisible] = useState(false);
  const [tabsExpandAll, setTabsExpandAll] = useState(false);
  const [processEngineNodes, setProcessEngineNodes] = useState<DrillOrderApprovalNode[]>([]);
  const [pdfTabList, setPdfTabList] = useState<EventTab[]>(initTab);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const eventDetailLoading = useSelector(selectEventDetailLoading);
  const latestPdfTabList = useLatest(pdfTabList);

  const status = eventStatus ? eventStatus.code : null;
  const reviewButtonVisible = useMemo(() => tabKey !== 'checkingRecords', [tabKey]);
  const isEventBeforeReviewAndDebrief = status
    ? EventSpecificProcessStatusNumberValueMap[status as unknown as EventSpecificProcessStatus] <
      EventSpecificProcessStatusNumberValueMap[EventSpecificProcessStatus.Debrief]
    : false;
  const showEventLocationButton = checkCode('element_detect-event') || isOwner;
  const hasEventLocationInfo = eventCenterInfo.detectTime;
  useEffect(() => {
    (async function () {
      const { error, data } = await fetchEventConfiguration();
      if (error) {
        message.error(error.message);
        return;
      }
      setProcessConfig(data?.processConfig);
    })();
    dispatch(getEventDetailAction(inInString));
    dispatch(getEventLifeAction(inInString));
  }, [dispatch, inInString]);

  const setEventProcessEngineNodes = useCallback(async () => {
    if (eventCenterInfo.eventInstNo) {
      const { data: progressData } = await getDrillOrderProgress({
        variables: { params: { instId: eventCenterInfo.eventInstNo, needNodes: true } },
      });
      setProcessEngineNodes(progressData?.drillOrderApproval?.nodeList ?? []);
    }
  }, [eventCenterInfo.eventInstNo, getDrillOrderProgress]);
  return (
    <>
      {Object.keys(eventCenterInfo).length > 0 && (
        <EventDataContext.Provider
          value={[
            {
              parentForm: form,
              drawerVisible,
              tabsExpandAll,
              pdfTabList,
              eventProcessEngineNodes: processEngineNodes,
              eventProcessEngineConfig: processConfig,
            },
            {
              setDrawerVisible,
              setTabsExpandAll,
              setPdfTabList,
              setEventProcessEngineNodes: setEventProcessEngineNodes,
            },
          ]}
        >
          <Spin spinning={eventDetailLoading}>
            <DownloadPdfButton
              key="download"
              style={{ position: 'fixed', top: 52, right: 8 }}
              compact={false}
              disabled={tabsExpandAll}
              pdfName={
                featureIsEventConfigWithProcessEngineRequired
                  ? `${eventCenterInfo.eventNo}_${eventCenterInfo.eventTitle}`
                  : `${eventCenterInfo.id}_${eventCenterInfo.eventDesc}`
              }
              exportElement={document.getElementById('root')}
              beforeDownload={() => {
                setTabsExpandAll(true);
                return new Promise(resolve => {
                  exportPdfInterval.current = setInterval(() => {
                    if (
                      latestPdfTabList.current
                        .filter(item => item.isValid)
                        .some(item => !item.isRendered)
                    ) {
                      return;
                    }
                    setTimeout(() => {
                      resolve();
                    }, 2000);
                  }, 1000);
                });
              }}
              onFinish={() => {
                setTabsExpandAll(false);
                exportPdfInterval.current && clearInterval(exportPdfInterval.current);
              }}
            />
            <Space
              direction="vertical"
              size="middle"
              style={{ width: '100%', height: '100%', marginBottom: 48 }}
            >
              {!featureIsEventConfigWithProcessEngineRequired && <EventStatusSteps id={id} />}

              {featureIsEventConfigWithProcessEngineRequired && eventCenterInfo.eventInstNo && (
                <EventSpecificStatusSteps id={eventCenterInfo.eventInstNo} />
              )}
              <EventInfo id={id} />
              {hasEventLocationInfo && featureSpecialInfoSort && (
                <Card
                  title={
                    <Space direction="horizontal">
                      <Typography.Title showBadge level={5} style={{ marginBottom: 0 }}>
                        根因定位
                      </Typography.Title>
                      {isEventBeforeReviewAndDebrief && showEventLocationButton && (
                        <EventLocationModalButton
                          mode="edit"
                          eventId={eventCenterInfo.id}
                          blockGuid={eventCenterInfo.blockGuidList}
                          featureSpecialInfoSort={featureSpecialInfoSort}
                          initialValues={{
                            detectReason: eventCenterInfo.detectReason!,
                            detectUserId: { value: eventCenterInfo.detectUserId! },
                            detectTime: moment(eventCenterInfo.detectTime),
                          }}
                        />
                      )}
                    </Space>
                  }
                  headStyle={{ borderBottom: 0 }}
                  bodyStyle={{ padding: '0 24px' }}
                  bordered={false}
                  style={{ width: '100%' }}
                >
                  <Space
                    style={{ display: 'flex', width: '100%' }}
                    direction="vertical"
                    size="middle"
                  >
                    <Descriptions column={4}>
                      <Descriptions.Item label="定位时间">
                        {moment(eventCenterInfo.detectTime).format('YYYY-MM-DD HH:mm:ss')}
                      </Descriptions.Item>
                      <Descriptions.Item label="定位人">
                        <User id={eventCenterInfo.detectUserId!} showAvatar={false} />
                      </Descriptions.Item>
                      <Descriptions.Item
                        span={2}
                        label="根因定位"
                        contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
                      >
                        <Typography.Text
                          ellipsis={{
                            tooltip: eventCenterInfo.detectReason,
                          }}
                        >
                          {eventCenterInfo.detectReason}
                        </Typography.Text>
                      </Descriptions.Item>
                    </Descriptions>
                  </Space>
                </Card>
              )}
              <Row gutter={[16, 16]}>
                <Col sm={24} xl={featureIsEventConfigWithProcessEngineRequired ? 24 : 17}>
                  <EventTabs parentActiveKey={tabKey} id={id} setParentActiveKey={setTabKey} />
                </Col>
                {!featureIsEventConfigWithProcessEngineRequired && (
                  <Col sm={24} xl={7}>
                    <ProcessingRecords id={id} />
                  </Col>
                )}
              </Row>
            </Space>
            {!featureIsEventConfigWithProcessEngineRequired &&
              reviewButtonVisible &&
              status !== BackendEventStatusCode.Closed &&
              !tabsExpandAll && (
                <EventOperationBar
                  id={id}
                  reviewButtonVisible={reviewButtonVisible}
                  onRelieveButtonClick={() => {
                    checkEventCanAudit({
                      variables: {
                        query: {
                          eventId: eventCenterInfo.eventNo!,
                          eventVersion: 1,
                        },
                      },
                    });
                    // setTabKey('checkingRecords');
                  }}
                />
              )}
            {featureIsEventConfigWithProcessEngineRequired && !tabsExpandAll && (
              <EventSpecificOperationBar
                eventId={id}
                tabKey={tabKey}
                onRelieveButtonClick={() => {
                  checkEventCanAudit({
                    variables: {
                      query: {
                        eventId: eventCenterInfo.eventNo!,
                        eventVersion: 1,
                      },
                    },
                  });
                }}
              />
            )}
          </Spin>
        </EventDataContext.Provider>
      )}
    </>
  );
}
