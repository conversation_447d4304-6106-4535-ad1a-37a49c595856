import { InfoCircleOutlined } from '@ant-design/icons';
import React, { useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { useEventRelateTickets } from '@manyun/ticket.gql.client.tickets';
import {
  BackendEventOperationStatusCode,
  EventSpecificProcessStatus,
  EventSpecificProcessStatusNumberValueMap,
} from '@manyun/ticket.model.event';
import { cancelEventUpgrade } from '@manyun/ticket.service.cancel-event-upgrade';
import {
  getEventDetailAction,
  getEventLifeAction,
  getEventUpgradeRecordsAction,
  getProgressUpdateListAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

import {
  EventLocationModalButton,
  EventSpecificProcessInfo,
} from '../specific-components/event-specific-process-info';
import { CheckingRecords } from './checking-records';
import { useEventData } from './event-data-context';
import { InfluenceSurfaceTab } from './influence-surface';
import MonitoringData from './monitoring-data';
import { OperationRecords } from './operation-records';
import { PhaseUpgradeModal } from './phase-upgrade-modal';
import { RelateTicketsTable } from './relate-tickets-table';
import { SourceRecordsTable } from './source-records-table';
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import { StaffUpgradeRecords } from './staff-upgrade-records';
import { UpgradeListModal } from './upgrade-list-modal';

export type EventTabsProps = {
  id: number;
  parentActiveKey: string;
  setParentActiveKey: (key: string) => void;
};

const tabList = (featureSpecialInfoSort: boolean, updatedInAudit?: boolean | null) =>
  featureSpecialInfoSort
    ? [
        {
          key: 'eventProgress',
          tab: '事件记录',
        },
        {
          key: 'sourceRecords',
          tab: '来源记录',
        },
        {
          key: 'checkingRecords',
          tab: '复盘记录',
        },
        {
          key: 'operationRecords',
          tab: updatedInAudit ? (
            <>
              <Tooltip title="复盘阶段，存在事件信息的异常修改">
                <Typography.Text style={{ marginLeft: 4 }}>
                  操作记录{' '}
                  <InfoCircleOutlined
                    style={{ color: 'var(--manyun-error-color)', fontSize: 12 }}
                  />
                </Typography.Text>
              </Tooltip>
            </>
          ) : (
            '操作记录'
          ),
        },
      ]
    : [
        {
          key: 'eventProgress',
          tab: '事件进展',
        },
        {
          key: 'influenceSuface',
          tab: '影响范围',
        },
        {
          key: 'monitoringData',
          tab: '监控数据',
        },
        {
          key: 'sourceRecords',
          tab: '来源记录',
        },
        {
          key: 'checkingRecords',
          tab: '复盘记录',
        },
        {
          key: 'operationRecords',
          tab: '操作记录',
        },
      ];
export function EventTabs({ id, parentActiveKey, setParentActiveKey }: EventTabsProps) {
  const [{ tabsExpandAll, pdfTabList }] = useEventData();
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureIsEventConfigWithProcessEngine = features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';
  const featuresOnwerMultiple = features.onwerMultiple;
  const featureSpecialInfoSort = features.specialInfoSort;

  const { data: eventRelateTickets } = useEventRelateTickets({
    variables: {
      eventId: id,
      sort: { sortField: 'gmt_create', sortOrder: 'desc' },
      pageNum: 1,
      pageSize: 10,
    },
  });
  const [, { checkUserId, checkCode }] = useAuthorized();
  const dispatch = useDispatch();
  const eventCenterInfo = useSelector(selectEventDetail);

  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);
  const operationStatus = eventCenterInfo?.optStatus;
  const isCallbackUser =
    eventCenterInfo?.upgradeRevokeUserIds?.some((userId: number) => checkUserId(userId)) ?? false;
  const hasEventLocationInfo = eventCenterInfo.detectTime;
  const showEventLocationButton = checkCode('element_detect-event') || isOwner;
  const status = eventCenterInfo.optStatus;
  const ygStatus = eventCenterInfo.eventStatus.code;
  const isSecResponser = eventCenterInfo?.secRespondUserId
    ? checkUserId(eventCenterInfo.secRespondUserId)
    : false;
  const isThiResponser = eventCenterInfo?.thiRespondUserId
    ? checkUserId(eventCenterInfo.thiRespondUserId)
    : false;
  const isEventBeforeReviewAndDebrief = ygStatus
    ? EventSpecificProcessStatusNumberValueMap[ygStatus as unknown as EventSpecificProcessStatus] <=
      EventSpecificProcessStatusNumberValueMap[EventSpecificProcessStatus.Debrief]
    : false;

  const contentList: Record<string, React.ReactNode> = useMemo(() => {
    if (eventCenterInfo?.blockTag || eventCenterInfo.blockGuidList?.length) {
      return {
        eventProgress: featureIsEventConfigWithProcessEngineRequired ? (
          <EventSpecificProcessInfo
            eventCenterInfo={eventCenterInfo}
            blockGuid={eventCenterInfo.blockTag}
          />
        ) : (
          <StaffUpgradeRecords />
        ),
        influenceSuface: <InfluenceSurfaceTab />,
        relateTickets: <RelateTicketsTable eventId={id} />,
        monitoringData: (
          <MonitoringData
            // @ts-ignore legacy component
            id={featureIsEventConfigWithProcessEngineRequired ? eventCenterInfo.eventNo! : id}
            eventCenterInfo={eventCenterInfo}
          />
        ),

        checkingRecords: <CheckingRecords id={id} />,
        // @ts-ignore legacy component
        operationRecords: <OperationRecords id={id} />,

        sourceRecords: <SourceRecordsTable eventId={id} />,
      };
    }
    return {};
  }, [eventCenterInfo, featureIsEventConfigWithProcessEngineRequired, id]);
  const isUpgradeModalShow = () => {
    return status !== BackendEventOperationStatusCode.T1WaitForConfirm;
  };

  const isPhaseUpgradeModalShow = () => {
    switch (status) {
      case BackendEventOperationStatusCode.T2LogAdd:
      case BackendEventOperationStatusCode.T2UpgradeFinish:
        return isSecResponser;
      case BackendEventOperationStatusCode.T3UpgradeFinish:
      case BackendEventOperationStatusCode.T3LogAdd:
        return isThiResponser;
      default:
        return false;
    }
  };
  const isCancelButtonShow = () => {
    return operationStatus
      ? [
          BackendEventOperationStatusCode.T1WaitForConfirm,
          BackendEventOperationStatusCode.T2WaitForConfirm,
          BackendEventOperationStatusCode.T3WaitForConfirm,
        ].includes(operationStatus)
      : false;
  };
  const toggleEventUpgrade = async () => {
    const { error } = await cancelEventUpgrade({ eventId: id });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('撤销升级成功');
    dispatch(getEventDetailAction(id));
    dispatch(
      getProgressUpdateListAction({
        pageNum: 1,
        pageSize: 400,
        eventId: id,
      })
    );
    dispatch(getEventUpgradeRecordsAction({ pageNum: 1, pageSize: 10, eventId: id }));

    dispatch(getEventLifeAction(id));
  };
  const extraTabList = featureSpecialInfoSort
    ? [
        {
          key: 'eventProgress',
          tab: '事件记录',
        },
        {
          key: 'relateTickets',
          tab: '关联事项',
        },
        {
          key: 'sourceRecords',
          tab: '来源记录',
        },
        {
          key: 'checkingRecords',
          tab: '复盘记录',
        },
        {
          key: 'operationRecords',
          tab: eventCenterInfo.updatedInAudit ? (
            <>
              <Tooltip title="复盘阶段，存在事件信息的异常修改">
                <Typography.Text style={{ marginLeft: 4 }}>
                  操作记录{' '}
                  <InfoCircleOutlined
                    style={{ color: 'var(--manyun-error-color)', fontSize: 12 }}
                  />
                </Typography.Text>
              </Tooltip>
            </>
          ) : (
            '操作记录'
          ),
        },
      ]
    : [
        {
          key: 'eventProgress',
          tab: '事件进展',
        },
        {
          key: 'relateTickets',
          tab: '关联事项',
        },
        {
          key: 'influenceSuface',
          tab: '影响范围',
        },
        {
          key: 'monitoringData',
          tab: '监控数据',
        },
        {
          key: 'sourceRecords',
          tab: '来源记录',
        },
        {
          key: 'checkingRecords',
          tab: '复盘记录',
        },
        {
          key: 'operationRecords',
          tab: '操作记录',
        },
      ];

  return !tabsExpandAll ? (
    <Card
      tabList={
        eventRelateTickets?.eventRelateTickets && eventRelateTickets.eventRelateTickets.total > 0
          ? extraTabList
          : tabList(featureSpecialInfoSort, eventCenterInfo.updatedInAudit)
      }
      activeTabKey={parentActiveKey}
      tabBarExtraContent={
        featureIsEventConfigWithProcessEngineRequired ? (
          !hasEventLocationInfo &&
          showEventLocationButton &&
          isEventBeforeReviewAndDebrief &&
          featureSpecialInfoSort && (
            // 需要根据是否已经存在事件定位信息，判断是new还是edit,并且遇到直接关单的单子，也不应该展示
            <EventLocationModalButton
              mode="new"
              eventId={eventCenterInfo.id}
              blockGuid={eventCenterInfo.blockGuidList}
              featureSpecialInfoSort={featureSpecialInfoSort}
            />
          )
        ) : (
          <Space>
            {isUpgradeModalShow() && <UpgradeListModal id={id} />}

            {isPhaseUpgradeModalShow() && <PhaseUpgradeModal id={id} isOwnerOperation={false} />}
            {isCancelButtonShow() && isCallbackUser && (
              <Button onClick={toggleEventUpgrade}>撤销升级</Button>
            )}
          </Space>
        )
      }
      style={{ width: '100%', height: '100%' }}
      bodyStyle={
        featureIsEventConfigWithProcessEngineRequired
          ? undefined
          : {
              height: 600,
              overflowX: 'hidden',
              overflowY: 'auto',
            }
      }
      onTabChange={key => {
        setParentActiveKey(key);
      }}
    >
      {contentList[parentActiveKey]}
    </Card>
  ) : (
    <Space style={{ display: 'flex' }} direction="vertical">
      {pdfTabList
        .filter(item => item.isValid)
        .map(item => (
          <Card key={item.key} title={item.tab}>
            {contentList[item.key]}
          </Card>
        ))}
    </Space>
  );
}
