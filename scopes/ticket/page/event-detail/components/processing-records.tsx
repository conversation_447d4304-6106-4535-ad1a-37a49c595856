import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import {
  BackendEventOperationStatusCode,
  EventStatusValueMap,
  isCurrentUserResponser,
} from '@manyun/ticket.model.event';
import {
  getProgressUpdateListAction,
  selectEventDetail,
  selectUpgradeList,
} from '@manyun/ticket.state.event';

import { ProcessRecordItem } from './process-record-item';
import { ProgressUpgradeModal } from './progress-update-modal';

export type ProcessingRecordsProps = {
  id: number;
};
export function ProcessingRecords({ id }: ProcessingRecordsProps) {
  const dispatch = useDispatch();
  const eventCenterInfo = useSelector(selectEventDetail);
  const [, { checkUserId }] = useAuthorized();
  const status = eventCenterInfo.optStatus;
  const eventStatusValue = eventCenterInfo?.eventStatus?.code
    ? Number(EventStatusValueMap[eventCenterInfo.eventStatus.code])
    : null;
  const isFirResponser = eventCenterInfo.firRespondUserId
    ? checkUserId(eventCenterInfo.firRespondUserId)
    : false;
  const isSecResponser = eventCenterInfo.secRespondUserId
    ? checkUserId(eventCenterInfo.secRespondUserId)
    : false;
  const isThiResponser = eventCenterInfo.thiRespondUserId
    ? checkUserId(eventCenterInfo.thiRespondUserId)
    : false;
  const isResponser = isCurrentUserResponser({
    isFirResponser,
    isSecResponser,
    isThiResponser,
    status,
  });

  const [configUtil] = useConfigUtil();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featuresOnwerMultiple = features.onwerMultiple;

  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const progressUpgradeList = useSelector(selectUpgradeList);
  useEffect(() => {
    dispatch(getProgressUpdateListAction({ pageNum: 1, pageSize: 1000, eventId: id }));
  }, [dispatch, id]);
  const isCreateModalButtonShow = () => {
    return (
      isResponser ||
      (isOwner &&
        status &&
        [
          BackendEventOperationStatusCode.T2PhaseUpgrade,
          BackendEventOperationStatusCode.T3PhaseUpgrade,
          BackendEventOperationStatusCode.Finished,
        ].includes(status) &&
        eventStatusValue &&
        eventStatusValue <= Number(EventStatusValueMap.RESOLVED))
    );
  };
  return (
    <Card
      title="处理记录"
      style={{ width: '100%', height: '100%' }}
      bodyStyle={{ width: '100%', height: 600, overflowX: 'hidden', overflowY: 'auto' }}
      extra={<>{isCreateModalButtonShow() && <ProgressUpgradeModal id={id} />}</>}
    >
      <Space direction="vertical" style={{ width: '100%', height: '100%' }} size={16}>
        {progressUpgradeList?.map(item => (
          <ProcessRecordItem
            key={`${item.creatorId}${item.optTime}`}
            userId={item.optStaffId ?? item.creatorId}
            userName={item.optStaffName ?? item.creatorName}
            optContent={item.optContent}
            optPlan={item.optPlan}
            optTime={item.optTime}
            status={item.remarks}
            addedFiles={item.addedFiles}
          />
        ))}
      </Space>
    </Card>
  );
}
