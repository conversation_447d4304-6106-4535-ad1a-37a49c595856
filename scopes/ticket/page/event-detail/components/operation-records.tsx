import React from 'react';
import { useSelector } from 'react-redux';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { Card } from '@manyun/base-ui.ui.card';
import { selectEventDetail } from '@manyun/ticket.state.event';

export function OperationRecords() {
  const eventCenterInfo = useSelector(selectEventDetail);
  return (
    <Card bordered={false} style={{ marginTop: -16 }} bodyStyle={{ padding: 0 }}>
      <OperationLogTable
        showColumns={['serialNumber', 'targetType', 'modifyType']}
        defaultSearchParams={{
          targetType: 'EVENT',
          targetId: eventCenterInfo?.id.toString(),
        }}
        isTargetIdEqual={(targetId: string) => {
          return targetId === eventCenterInfo?.id.toString();
        }}
      />
    </Card>
  );
}
