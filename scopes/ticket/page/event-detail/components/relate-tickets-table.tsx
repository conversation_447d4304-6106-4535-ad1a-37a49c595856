import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { useLazyEventRelateTickets } from '@manyun/ticket.gql.client.tickets';
import {
  generateRiskRegisterDetailLocation,
  generateTicketLocation,
} from '@manyun/ticket.route.ticket-routes';

import { useEventData } from './event-data-context';

export type RelateTicketJSON = {
  relateTicket: { type: string; name: string; id: string };
  relateTicketDesc: string;
  relateTicketStatus: { name: string; value: string };
  relateTicketCreateUserId: string;
  relateTicketCreateTime: string;
};
export type RelateTicketsTableProps = {
  eventId: number;
} & Omit<TableProps<RelateTicketJSON>, 'rowKey' | 'loading' | 'dataSource' | 'columns'>;
const columns: Array<ColumnType<RelateTicketJSON>> = [
  {
    title: '类型',
    dataIndex: 'type',
    key: 'type',
    width: 88,
    render: (_, { relateTicket }) => relateTicket.name,
  },
  {
    title: 'ID',
    dataIndex: 'ticketId',
    key: 'ticketId',
    width: 180,
    render: (_, { relateTicket }) => (
      <Link
        to={
          relateTicket.type === 'REPAIR'
            ? generateTicketLocation({
                id: relateTicket.id,
                ticketType: relateTicket.type.toLowerCase(),
              })
            : generateRiskRegisterDetailLocation({ id: relateTicket.id })
        }
        target="_blank"
      >
        {relateTicket.id}
      </Link>
    ),
  },
  {
    title: '事项描述',
    dataIndex: 'relateTicketDesc',
    key: 'relateTicketDesc',
    render: (_, { relateTicketDesc }) => (
      <Typography.Text ellipsis={{ tooltip: true }}>{relateTicketDesc}</Typography.Text>
    ),
  },
  {
    title: '状态',
    dataIndex: 'relateTicketStatus',
    key: 'relateTicketStatus',
    width: 80,
    render: (_, { relateTicketStatus }) => relateTicketStatus.name,
  },
  {
    title: '创建人',
    width: 80,
    dataIndex: 'relateTicketCreateUserId',
    key: 'relateTicketCreateUserId',
    render: (_, { relateTicketCreateUserId }) => (
      <UserLink userId={Number(relateTicketCreateUserId)} external />
    ),
  },
  {
    title: '创建时间',
    width: 184,
    dataIndex: 'relateTicketCreateTime',
    key: 'relateTicketCreateTime',
    sorter: true,
  },
];
export function RelateTicketsTable({ eventId }: RelateTicketsTableProps) {
  const [getRelateTickets] = useLazyEventRelateTickets();
  const [{ tabsExpandAll }, { setPdfTabList }] = useEventData();
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
  }>({ pageNum: 1, pageSize: 10 });
  const [total, setTotal] = useState(0);
  const [dataSource, setDatasource] = useState<RelateTicketJSON[]>([]);
  const [sortOrder, setSortOrder] = useState('desc');
  useEffect(() => {
    (async function () {
      setLoading(true);
      const { data, error } = await getRelateTickets({
        variables: {
          eventId,
          sort: {
            sortField: 'gmt_create',
            sortOrder: sortOrder,
          },
          pageNum: pagination.pageNum,
          pageSize: tabsExpandAll ? 5000 : pagination.pageSize,
        },
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        setPdfTabList(pdfTabList => {
          return pdfTabList.map(item => {
            if (item.key === 'relateTickets') {
              return {
                ...item,
                isValid: false,
                isRendered: true,
              };
            }
            return item;
          });
        });
        return;
      }
      setDatasource(data?.eventRelateTickets?.data ?? []);
      setTotal(data?.eventRelateTickets?.total ?? 0);
      setPdfTabList(pdfTabList => {
        return pdfTabList.map(item => {
          if (item.key === 'relateTickets') {
            return {
              ...item,
              isValid: (data?.eventRelateTickets?.total ?? 0) > 0 ? true : false,
              isRendered: true,
            };
          }
          return item;
        });
      });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sortOrder, pagination.pageNum, pagination.pageSize, tabsExpandAll]);
  return (
    <Table
      rowKey={record => record.relateTicket.id}
      loading={loading}
      columns={columns}
      scroll={{ y: 400 }}
      dataSource={dataSource}
      pagination={{ pageSize: pagination.pageSize, current: pagination.pageNum, total: total }}
      onChange={(pagination, _, sorter, { action }) => {
        const { order } = sorter as { field: string; order: string };
        setSortOrder(order === 'ascend' ? 'asc' : 'desc');
        setPagination({ pageNum: pagination.current!, pageSize: pagination.pageSize! });
      }}
    />
  );
}
