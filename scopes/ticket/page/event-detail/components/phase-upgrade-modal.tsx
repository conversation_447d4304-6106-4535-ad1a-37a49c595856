import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import {
  BackendEventStatus,
  BackendEventUpgradeChannel,
  BackendEventUpgradeLevel,
  BackendEventUpgradeType,
  EventStatusValueMap,
} from '@manyun/ticket.model.event';
import { createEventUpgrade } from '@manyun/ticket.service.create-event-upgrade';
import {
  getEventDetailAction,
  getEventLifeAction,
  getEventUpgradeRecordsAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

const eventUpgradeChannelOnlineOptions = [
  {
    label: '邮件',
    value: BackendEventUpgradeChannel.Email,
    disabled: true,
  },
  {
    label: '站内信',
    value: BackendEventUpgradeChannel.InternalMessage,
  },
  {
    label: '短信',
    value: BackendEventUpgradeChannel.Sms,
  },
  {
    label: '电话',
    value: BackendEventUpgradeChannel.Phone,
  },
];

const eventUpgradeChannelOfflineOptions = [
  {
    label: '邮件',
    value: BackendEventUpgradeChannel.Email,
    disabled: true,
  },
];
export type PhaseUpgradeModalProps = {
  id: number;
  isOwnerOperation?: boolean;
};
export function PhaseUpgradeModal({ id, isOwnerOperation = true }: PhaseUpgradeModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const upgradeType = Form.useWatch('upgradeType', form);
  const isOtherPeople = Form.useWatch('isOtherPeople', form);
  const modalTitle = isOwnerOperation ? '阶段更新' : '人员升级';
  const eventCenterInfo = useSelector(selectEventDetail);
  const { eventStatus, eventOwnerId } = eventCenterInfo;
  const { code: eventStatusCode } = eventStatus;

  const initialStatus = useMemo(() => {
    return initializeLevel(eventStatusCode ? EventStatusValueMap[eventStatusCode] : null);
  }, [eventStatusCode]);

  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };
  const onOk = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const { upgradeUserIds, ...rest } = formValue;
      const defaultParams = {
        ...rest,
        eventId: id,
        upgradeChannels: [BackendEventUpgradeChannel.Email],
        upgradeType: BackendEventUpgradeType.Offline,
        upgradeUserIds: [eventOwnerId],
        upgradeWay: isOwnerOperation ? 'PHASE' : 'NORMAL',
      };

      const params =
        isOtherPeople || !isOwnerOperation
          ? {
              ...rest,
              upgradeUserIds: [upgradeUserIds.key],
              eventId: id,
              upgradeWay: isOwnerOperation ? 'PHASE' : 'NORMAL',
            }
          : { ...defaultParams, upgradeChannels: [] };
      const { error } = await createEventUpgrade({ ...params });

      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      dispatch(getEventDetailAction(id));
      dispatch(getEventLifeAction(id));
      dispatch(getEventUpgradeRecordsAction({ pageNum: 1, pageSize: 10, eventId: id }));
      setVisible(false);
    });
  };
  useEffect(() => {
    if (visible) {
      form.setFieldsValue({ upgradeLevel: initialStatus });
    }
  }, [visible, initialStatus, form]);
  return (
    <>
      <Button type={isOwnerOperation ? 'primary' : 'default'} loading={loading} onClick={showModal}>
        {modalTitle}
      </Button>
      <Modal
        open={visible}
        okButtonProps={{ loading: loading }}
        title={modalTitle}
        forceRender
        destroyOnClose
        onOk={onOk}
        onCancel={closeModal}
      >
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} preserve>
          <Form.Item
            label="更新阶段"
            name="upgradeLevel"
            rules={[{ required: true, message: '请选择升级级别' }]}
          >
            <Select
              disabled={
                !(
                  eventCenterInfo.enableRelieveSkip &&
                  EventStatusValueMap[eventStatusCode] === BackendEventStatus.Processing &&
                  isOwnerOperation
                )
              }
              showSearch
              allowClear
              style={{ width: 200 }}
            >
              <Select.Option
                key={BackendEventUpgradeLevel.Tier2}
                value={BackendEventUpgradeLevel.Tier2}
              >
                T2缓解阶段
              </Select.Option>

              <Select.Option
                key={BackendEventUpgradeLevel.Tier3}
                value={BackendEventUpgradeLevel.Tier3}
              >
                T3解决阶段
              </Select.Option>
            </Select>
          </Form.Item>
          {isOwnerOperation && (
            <Form.Item
              label="升级他人处理"
              name="isOtherPeople"
              initialValue
              rules={[{ required: true, message: '请选择是否升级他人处理' }]}
            >
              <Radio.Group>
                <Radio value>是</Radio>

                <Radio value={false}>否</Radio>
              </Radio.Group>
            </Form.Item>
          )}

          <>
            {isOtherPeople || !isOwnerOperation ? (
              <>
                <Form.Item
                  label="目标人员"
                  name="upgradeUserIds"
                  rules={[{ required: true, message: '请选择目标人员' }]}
                >
                  <UserSelect
                    blockGuid={eventCenterInfo?.blockTag}
                    disabled={!upgradeType}
                    style={{ width: 200 }}
                    includeCurrentUser={false}
                  />
                </Form.Item>
                <Form.Item
                  label="升级方式"
                  name="upgradeType"
                  rules={[{ required: true, message: '请选择升级方式' }]}
                  initialValue={BackendEventUpgradeType.Online}
                >
                  <Radio.Group>
                    <Radio value={BackendEventUpgradeType.Online}>
                      <Space>
                        线上
                        <Tooltip placement="top" title="通过线上方式，由系统自动通知对方">
                          <QuestionCircleOutlined />
                        </Tooltip>
                      </Space>
                    </Radio>

                    <Radio value={BackendEventUpgradeType.Offline}>
                      <Space>
                        线下
                        <Tooltip placement="top" title="通过线下人工联系的方式升级">
                          <QuestionCircleOutlined />
                        </Tooltip>
                      </Space>
                    </Radio>
                  </Radio.Group>
                </Form.Item>

                <Form.Item
                  label="通知渠道"
                  name="upgradeChannels"
                  rules={[{ required: true, message: '请选择通知渠道' }]}
                  initialValue={[BackendEventUpgradeChannel.Email]}
                >
                  {upgradeType === BackendEventUpgradeType.Online ? (
                    <Checkbox.Group options={eventUpgradeChannelOnlineOptions} />
                  ) : (
                    <Checkbox.Group options={eventUpgradeChannelOfflineOptions} />
                  )}
                </Form.Item>
                <Form.Item
                  label="升级内容"
                  name="upgradeContext"
                  rules={[
                    {
                      max: 50,
                      message: '最多输入 50 个字符！',
                    },
                  ]}
                >
                  <Input.TextArea rows={3} />
                </Form.Item>
              </>
            ) : (
              <Form.Item
                label="阶段更新说明"
                name="upgradeContext"
                rules={[
                  {
                    max: 50,
                    message: '最多输入 50 个字符！',
                  },
                ]}
              >
                <Input.TextArea rows={3} />
              </Form.Item>
            )}
          </>
        </Form>
      </Modal>
    </>
  );
}

const initializeLevel = (status: string | null) => {
  if (status) {
    if (status === BackendEventStatus.Processing) {
      return BackendEventUpgradeLevel.Tier2;
    } else if (status === BackendEventStatus.Relieved) {
      return BackendEventUpgradeLevel.Tier3;
    }
  }
  return;
};
