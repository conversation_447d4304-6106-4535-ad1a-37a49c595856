import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { User } from '@manyun/auth-hub.ui.user';
import type {
  BackendEventUpgradeChannel,
  BackendEventUpgradeStatus,
} from '@manyun/ticket.model.event';
import {
  EventUpgradeChannelMap,
  EventUpgradeLevelMap,
  EventUpgradeStatusMap,
  EventUpgradeTypeMap,
} from '@manyun/ticket.model.event';
import type { EventUpgradeLog } from '@manyun/ticket.service.fetch-event-upgrade-logs';
import {
  getEventUpgradeRecordsAction,
  selectEventUpgradeRecords,
  selectEventUpgradeRecordsTotal,
} from '@manyun/ticket.state.event';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

export const SUBSCRIPTION_TYPE = {
  ALARM_SUBSCRIBE: 'ALARM_SUBSCRIBE',
  EVENT_SUBSCRIBE: 'EVENT_SUBSCRIBE',
};

export type UpgradeListModalProps = {
  id: number;
};

export function UpgradeListModal({ id }: UpgradeListModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const eventUpgradeRecords = useSelector(selectEventUpgradeRecords);
  const [changePagination, setChangePagination] = useState<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const eventUpgradeRecordsTotal = useSelector(selectEventUpgradeRecordsTotal);
  const columns: ColumnType<EventUpgradeLog>[] = [
    {
      title: '序号',
      dataIndex: 'id',
      fixed: 'left',
      render: (_value, _record, index) =>
        serialNumberInReverse({
          total: eventUpgradeRecordsTotal,
          page: changePagination.pageNum,
          pageSize: changePagination.pageSize,
          index,
        }),
    },

    {
      title: '升级时间',
      dataIndex: 'gmtCreate',
      render: (gmtCreate: number) => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '响应时间',
      dataIndex: 'respondTime',
      render: (respondTime: number) =>
        respondTime ? dayjs(respondTime).format('YYYY-MM-DD HH:mm:ss') : '--',
    },

    {
      title: '升级级别',
      dataIndex: 'upgradeLevel',
      render: (_, { upgradeLevel }) => <span>{EventUpgradeLevelMap[upgradeLevel]}</span>,
    },
    {
      title: '目标人员',
      dataIndex: 'notifyUsersId',
      render: (_, { notifyUsersId, notifyUsersName }) => (
        <User.Link id={notifyUsersId[0]} name={notifyUsersName} />
      ),
    },
    {
      title: '升级内容',
      dataIndex: 'upgradeContext',
      render: (_, { upgradeContext }) =>
        upgradeContext ? (
          <Typography.Text style={{ width: 200 }} ellipsis={{ tooltip: true }}>
            {upgradeContext}
          </Typography.Text>
        ) : (
          '--'
        ),
    },
    {
      title: '升级方式',
      dataIndex: 'upgradeType',
      render: (_, { upgradeType }) => <span>{EventUpgradeTypeMap[upgradeType]}</span>,
    },
    {
      title: '通知渠道',
      dataIndex: 'upgradeChannels',
      render: (_, { upgradeChannels }) => {
        return upgradeChannels.length > 0 ? (
          <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
            {upgradeChannels.map((channel: BackendEventUpgradeChannel) => (
              <Typography.Text key={channel}>{EventUpgradeChannelMap[channel]}</Typography.Text>
            ))}
          </Space>
        ) : (
          '--'
        );
      },
    },
    {
      title: '升级状态',
      dataIndex: 'upgradeStatus',
      render: (text: BackendEventUpgradeStatus) => (
        <span>{text ? EventUpgradeStatusMap[text] : '--'}</span>
      ),
    },
    {
      title: '升级人',
      dataIndex: 'creatorName',
      render: (_, { creatorId, creatorName }) => <User.Link id={creatorId} name={creatorName} />,
    },
    {
      title: 'SLA',
      dataIndex: 'sla',
      render: (_, { gmtCreate, respondTime }) => {
        return (
          <>
            {respondTime && gmtCreate ? (
              <Space size={0}>
                <Typography.Text>响应SLA：</Typography.Text>
                <TicketSlaText
                  delay={dayjs(respondTime).diff(dayjs(gmtCreate), 'second')}
                  taskSla={0}
                  unit="MINUTES"
                  effectTime={null}
                  endTime={null}
                />
              </Space>
            ) : (
              '--'
            )}
          </>
        );
      },
    },
  ];
  const dispatch = useDispatch();
  const handlePageChange = useCallback(
    ({ pageNum, pageSize }) => {
      dispatch(getEventUpgradeRecordsAction({ pageNum, pageSize, eventId: id }));
    },
    [dispatch, id]
  );
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };
  useEffect(() => {
    handlePageChange({ pageNum: 1, pageSize: 10, eventId: id });
  }, [handlePageChange, id]);
  return (
    <>
      <Button onClick={showModal}>升级记录</Button>
      <Modal width={1200} title="升级记录" open={visible} footer={null} onCancel={closeModal}>
        <Space style={{ width: '100%' }} direction="vertical">
          <Table<EventUpgradeLog>
            rowKey="id"
            columns={columns}
            dataSource={eventUpgradeRecords}
            scroll={{ x: 'max-content' }}
            pagination={{
              total: eventUpgradeRecordsTotal,
              showSizeChanger: true,
              onChange: (changePageNum, changePageSize) => {
                setChangePagination({ pageNum: changePageNum, pageSize: changePageSize });
                handlePageChange({ pageNum: changePageNum, pageSize: changePageSize });
              },
            }}
          />
        </Space>
      </Modal>
    </>
  );
}

function serialNumberInReverse({
  total,
  page,
  pageSize,
  index,
}: {
  total: number;
  page: number;
  pageSize: number;
  index: number;
}) {
  const number = total - ((page - 1) * pageSize + index);
  return number;
}
