import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { useCheckEventCanAudit } from '@manyun/ticket.gql.client.tickets';
import { eventAudit } from '@manyun/ticket.service.event-audit';

export type ButtonWithoutAuditProps = {
  eventId: number;
  isInvalid: boolean;
  isSxdtyg?: boolean;
  eventNo: string;
  disabled?: boolean;
  onSuccess?: () => void;
};

export function ButtonWithoutAudit({
  isInvalid,
  eventId,
  isSxdtyg,
  eventNo,
  onSuccess,
  ...props
}: ButtonWithoutAuditProps) {
  const [loading, setLoading] = useState<boolean>(false);

  const [checkEventCanAudit] = useCheckEventCanAudit({
    onCompleted: async data => {
      if (
        data.checkEventCanAudit?.code &&
        data.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
      ) {
        message.error('需关闭所有关联风险单后，才可提交复盘或评审');
        return;
      }
      setLoading(true);
      const { error } = await eventAudit({
        eventId,
      });
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      message.success('已提交评审');
      onSuccess && onSuccess();
    },
  });

  return (
    <Button
      loading={loading}
      {...props}
      onClick={() => {
        if (isInvalid) {
          if (isSxdtyg) {
            message.error('请先完成「根因定位」');
          } else {
            message.error('事件信息不完整');
          }
          return;
        }
        checkEventCanAudit({
          variables: {
            query: {
              eventId: eventNo,
              eventVersion: 1,
            },
          },
        });
      }}
    >
      无需复盘，提交评审
    </Button>
  );
}
