import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { useLazyEventSourceRecords } from '@manyun/ticket.gql.client.tickets';
import { generateTicketLocation } from '@manyun/ticket.route.ticket-routes';

import { useEventData } from './event-data-context';

export type SourceRecordJson = {
  taskNo: string;
  taskType: string;
  exceptionSubjectTag: string;
  exceptionSubjectGuid: string;
  exceptionSubjectType: string;
  subjectItemName: string;
  subjectItemRelateTime: string;
  operatorId: number;
  operatorName: string;
  taskNoMergeRows: number;
  exceptionSubjectMergeRows: number;
};
export type SourceRecordsTableProps = {
  eventId: number;
} & Omit<TableProps<SourceRecordJson>, 'rowKey' | 'loading' | 'dataSource' | 'columns'>;
const columns: Array<ColumnType<SourceRecordJson>> = [
  {
    title: '工单单号',
    dataIndex: 'taskNo',
    ellipsis: true,
    render: (_, { taskNo, taskType }) => (
      <Link
        to={generateTicketLocation({ id: taskNo, ticketType: taskType.toLowerCase() })}
        target="_blank"
      >
        {taskNo}
      </Link>
    ),
    onCell: record => {
      return {
        rowSpan: record.taskNoMergeRows,
      };
    },
  },
  {
    title: '工单类型',
    dataIndex: 'taskType',
    width: 120,
    render: (_, { taskType }) => {
      if (taskType === 'INSPECTION') {
        return '巡检';
      }
      if (taskType === 'MAINTENANCE') {
        return '维护';
      }
      return '--';
    },
    onCell: record => {
      return {
        rowSpan: record.taskNoMergeRows,
      };
    },
  },
  {
    title: '异常对象',
    ellipsis: true,
    dataIndex: 'exceptionSubjectTag',
    render: (_, { exceptionSubjectGuid, exceptionSubjectTag, exceptionSubjectType }) => {
      const [idcTag, blockTag, roomTag] = exceptionSubjectGuid.split('.');
      return (
        <Typography.Link
          onClick={() => {
            if (exceptionSubjectType === 'ROOM') {
              window.open(
                generateRoomMonitoringUrl({
                  idc: idcTag,
                  block: blockTag,
                  room: roomTag,
                })
              );
            } else {
              window.open(
                // @ts-ignore ts error
                generateDeviceRecordRoutePath({
                  guid: exceptionSubjectGuid,
                })
              );
            }
          }}
        >
          {exceptionSubjectTag}
        </Typography.Link>
      );
    },
    onCell: record => {
      return {
        rowSpan: record.exceptionSubjectMergeRows,
      };
    },
  },
  {
    title: '异常项',
    ellipsis: true,
    dataIndex: 'subjectItemName',
  },
  {
    title: '关联时间',
    width: 120,
    dataIndex: 'subjectItemRelateTime',
    render: (_, { subjectItemRelateTime }) => dayjs(subjectItemRelateTime).format('YYYY-MM-DD'),
  },
  {
    title: '操作用户',
    width: 120,
    dataIndex: 'operatorId',
    render: (_, { operatorId, operatorName }) => (
      <UserLink userId={operatorId} userName={operatorName} external />
    ),
  },
];
export function SourceRecordsTable({ eventId }: SourceRecordsTableProps) {
  const [, { setPdfTabList }] = useEventData();
  const [getSourceRecords, { data, loading }] = useLazyEventSourceRecords({
    onCompleted(data) {
      setPdfTabList(pdfTabList => {
        return pdfTabList.map(item => {
          if (item.key === 'sourceRecords') {
            return {
              ...item,
              isValid: (data?.eventSourceRecords?.data?.length ?? 0) > 0 ? true : false,
              isRendered: true,
            };
          }
          return item;
        });
      });
    },
  });

  useEffect(() => {
    getSourceRecords({
      variables: {
        eventId,
      },
    });
  }, [eventId, getSourceRecords]);

  return (
    <Table
      rowKey={record => `${record.taskNo}.${record.exceptionSubjectGuid}.${record.subjectItemName}`}
      loading={loading}
      columns={columns}
      scroll={{ y: 400 }}
      dataSource={data?.eventSourceRecords?.data ?? []}
      pagination={{ total: data?.eventSourceRecords?.total }}
    />
  );
}
