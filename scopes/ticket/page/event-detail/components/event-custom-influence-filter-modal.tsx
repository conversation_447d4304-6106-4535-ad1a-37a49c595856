import React, { useState } from 'react';

import FilterOutlined from '@ant-design/icons/es/icons/FilterOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import type { FilterFormParams } from './event-cabinet-influence-filter';

const tailLayout = {
  wrapperCol: { offset: 6, span: 18 },
};
type EventCustomInfluenceFilterModalProps = {
  onChange: (params: FilterFormParams) => void;
  defaultValue?: FilterFormParams;
};
export function EventCustomInfluenceFilterModal({
  onChange,
  defaultValue,
}: EventCustomInfluenceFilterModalProps) {
  const [open, setOpen] = useState(false);
  return (
    <Dropdown
      open={open}
      dropdownRender={() => (
        <FilterForm
          defaultValue={defaultValue}
          onChange={onChange}
          onSuccess={() => setOpen(false)}
        />
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={open => {
        setOpen(open);
      }}
    >
      <Button icon={<FilterOutlined />} />
    </Dropdown>
  );
}

type FilterFormProps = {
  onChange: (params: FilterFormParams) => void;
  defaultValue?: FilterFormParams;
  onSuccess?: () => void;
};

function FilterForm({ onChange, defaultValue, onSuccess }: FilterFormProps) {
  const [form] = Form.useForm<FilterFormParams>();

  const onFinish = (values: FilterFormParams) => {
    const { column, grid, customers } = values;

    const params: FilterFormParams = {
      column,
      grid,
      customers,
    };

    onChange(params);
    onSuccess && onSuccess();
  };
  return (
    <Card style={{ width: 332 }}>
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValue}
        onFinish={onFinish}
        onReset={() => {
          onChange({ column: undefined, grid: undefined, customers: undefined });
        }}
      >
        <Form.Item label="机列" name="column">
          <Input style={{ width: '100%' }} allowClear />
        </Form.Item>
        <Form.Item label="机柜" name="grid">
          <Input style={{ width: '100%' }} allowClear />
        </Form.Item>
        <Form.Item label="客户名称" name="customers">
          <Input style={{ width: '100%' }} allowClear />
        </Form.Item>
        <Divider spaceSize="mini" />
        <Form.Item {...tailLayout}>
          <Space>
            <Button htmlType="reset">重置</Button>
            <Button type="primary" htmlType="submit">
              搜索
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}
