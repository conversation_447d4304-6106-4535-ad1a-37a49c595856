import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

// import shallowequal from 'shallowequal';
import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { prefixCls } from '@manyun/base-ui.style.style';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useLazyUserResources } from '@manyun/iam.gql.client.iam';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { getDeviceTypesAction } from '@manyun/resource-hub.state.device-types';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import {
  BackendEventStatusCode,
  BackendFalseAlarm,
  EventSpecificProcessStatus,
  EventSpecificProcessStatusNumberValueMap,
  FAULT_TARGET_TYPE_TEXT,
  FalseAlarmMap,
  FaultTargetType,
} from '@manyun/ticket.model.event';
import type { CauseDevice } from '@manyun/ticket.model.event';
import { generateChangeTicketDetail } from '@manyun/ticket.route.ticket-routes';
import { selectEventDetail } from '@manyun/ticket.state.event';
import { CreateRepairTicketModal } from '@manyun/ticket.ui.create-repair-ticket-modal';
import { EventLevelText } from '@manyun/ticket.ui.event-level-text';
import { EventLocationDrawer } from '@manyun/ticket.ui.event-mutator';

import { InfluenceSurfaceModal } from '../specific-components/influence-suface';
import { CreateRiskDrawerYg } from './create-risk-drawer-yg';
import { EventEditDrawer } from './event-edit-drawer';
import { EventNotificationModal } from './event-notification-modal';
import { RiskTicketCreateButton } from './risk-ticket-create-button';

export type EventInfoType = {
  id: number;
};

export function EventInfo({ id }: EventInfoType) {
  const eventCenterInfo = useSelector(selectEventDetail);

  const { userId } = useSelector(selectMe);
  const [getUserResources, { data: userResourcesData }] = useLazyUserResources();
  useEffect(() => {
    if (userId) {
      getUserResources({
        variables: { params: { userId } },
      });
    }
  }, [getUserResources, userId]);
  const [configUtil] = useConfigUtil();

  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  const featuresTitle = ticketScopeCommonConfigs.events.features.title;
  const featuresNorthbound = ticketScopeCommonConfigs.events.features.northbound;
  const featuresTitleNoDisabled = featuresTitle !== 'disabled';
  const featuresNorthboundNoDisabled = featuresNorthbound !== 'disabled';
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');

  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const featuresReasonType = features.reasonType;
  const featuresReasonTypeRequired = featuresReasonType === 'required';
  const featuresReason = features.reason;
  const featuresReasonRequired = featuresReason === 'required';
  const featuresIsOwnerLabelChange = features.isOwnerLabelChange;
  const featuresIsOwnerLabelChangeRequired = featuresIsOwnerLabelChange === 'required';
  const featuresIsResponsiblePersonLabelChange = features.isResponsiblePersonLabelChange;
  const featuresIsResponsiblePersonLabelChangeRequired =
    featuresIsResponsiblePersonLabelChange === 'required';
  const featureIsEventConfigWithProcessEngine = features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';
  const featureBlockMultiple = features.blockMultiple;

  const featuresOnwerMultiple = features.onwerMultiple;

  const featureSpecialInfoSort = features.specialInfoSort;

  const [, { checkUserId, checkCode }] = useAuthorized();
  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const isFirResponser = eventCenterInfo.firRespondUserId
    ? checkUserId(eventCenterInfo.firRespondUserId)
    : false;
  const dispatch = useDispatch();
  useEffect(() => {
    dispatch(getDeviceTypesAction());
  }, [dispatch]);

  // const { entities: deviceTypeEntities } = useSelector(selectDeviceTypes, (left, right) =>
  //   shallowequal(left.entities, right.entities)
  // );

  const { eventStatus } = eventCenterInfo;

  const status = eventStatus ? eventStatus.code : null;

  const isEventBeforeReviewAndDebrief = status
    ? EventSpecificProcessStatusNumberValueMap[status as unknown as EventSpecificProcessStatus] <=
      EventSpecificProcessStatusNumberValueMap[EventSpecificProcessStatus.Debrief]
    : false;
  const showEventNotificationModal = featureIsEventConfigWithProcessEngineRequired
    ? (userResourcesData?.userResources &&
        userResourcesData?.userResources?.some(item =>
          featureSpecialInfoSort
            ? eventCenterInfo.blockGuidList?.includes(item.code)
            : item.code === eventCenterInfo.blockTag
        )) ||
      isOwner
    : status &&
      [
        BackendEventStatusCode.Processing,
        BackendEventStatusCode.Relieved,
        BackendEventStatusCode.Created,
      ].includes(status) &&
      isOwner;
  const showCreateRelateTicketDropdown = featureIsEventConfigWithProcessEngineRequired
    ? isEventBeforeReviewAndDebrief
    : status &&
      [BackendEventStatusCode.Processing, BackendEventStatusCode.Relieved].includes(status);
  const hasEventEditAuth =
    featureIsEventConfigWithProcessEngineRequired &&
    (checkCode('element_update-event') || isOwner) &&
    isEventBeforeReviewAndDebrief;
  const generateCardExtra = () => {
    return (
      <Space>
        {showEventNotificationModal && <EventNotificationModal id={id} />}

        {showCreateRelateTicketDropdown && (
          <Dropdown
            overlayStyle={{ zIndex: 1000 }}
            trigger={['click']}
            menu={{
              items: [
                {
                  label: (
                    <CreateRepairTicketModal
                      type="text"
                      compact
                      eventId={eventCenterInfo.id}
                      eventNo={eventCenterInfo.eventNo}
                      eventDesc={eventCenterInfo.eventDesc}
                      featureSpecialInfoSort={featureSpecialInfoSort}
                      // causeType={eventCenterInfo.infoType}
                      // causeObject={
                      //   initializeFaultTarget(
                      //     eventCenterInfo.infoType,
                      //     eventCenterInfo.causeDevices,
                      //     false,
                      //     deviceTypeEntities
                      //   ) as { label: string; key: string }[]
                      // }
                      blockGuid={eventCenterInfo.blockGuidList}
                    />
                  ),
                  type: 'group',
                },
                {
                  label:
                    riskRegisters.features?.baseInfo !== 'full' ? (
                      <RiskTicketCreateButton
                        type="text"
                        compact
                        relateEvent={{
                          id: eventCenterInfo.id,
                          eventNo: eventCenterInfo.eventNo,
                          description: eventCenterInfo.eventDesc,
                          blockGuid: eventCenterInfo.blockTag,
                        }}
                      />
                    ) : (
                      <CreateRiskDrawerYg
                        type="text"
                        compact
                        relateEvent={{
                          id: eventCenterInfo.id,
                          eventNo: eventCenterInfo.eventNo,
                          description: eventCenterInfo.eventDesc,
                          blockGuid: eventCenterInfo.blockTag,
                        }}
                      />
                    ),
                  type: 'group',
                },
              ],
            }}
          >
            <Button>创建关联工单</Button>
          </Dropdown>
        )}
      </Space>
    );
  };
  const generateChangeContent = () => {
    return eventCenterInfo.isChangeAlarm && eventCenterInfo.changeCode ? (
      <>
        是(变更ID：
        <Link target="_blank" to={generateChangeTicketDetail({ id: eventCenterInfo.changeCode })}>
          {eventCenterInfo.changeCode}
        </Link>
        )
      </>
    ) : (
      <>否</>
    );
  };
  return (
    <Card
      title={
        <>
          事件信息
          {((((![
            BackendEventStatusCode.Closed,
            BackendEventStatusCode.Audited,
            BackendEventStatusCode.Auditing,
          ].includes(eventCenterInfo.eventStatus?.code) &&
            isOwner) ||
            (isFirResponser && status === BackendEventStatusCode.Created)) &&
            !featureIsEventConfigWithProcessEngineRequired) ||
            hasEventEditAuth) && <EventEditDrawer eventId={id} />}
        </>
      }
      bordered={false}
      style={{ width: '100%' }}
      extra={generateCardExtra()}
      bodyStyle={{ paddingTop: 12 }}
    >
      <Space direction="vertical">
        {(featuresTitleNoDisabled || featuresNorthboundNoDisabled) && (
          <Space>
            {featuresTitleNoDisabled && (
              <Typography.Title level={5} style={{ marginBottom: 0 }}>
                {eventCenterInfo.eventTitle ?? '--'}
              </Typography.Title>
            )}
            {/* {eventCenterInfo.northSync && featuresNorthboundNoDisabled && (
              <Tag color="blue">北向同步</Tag>
            )} */}
          </Space>
        )}
        {featureSpecialInfoSort ? (
          <Descriptions column={4}>
            <Descriptions.Item label="事件级别">
              <EventLevelText code={eventCenterInfo.eventLevel} />
            </Descriptions.Item>
            <Descriptions.Item label="楼栋" contentStyle={{ overflow: 'hidden', paddingRight: 16 }}>
              {featureBlockMultiple ? (
                <Typography.Text
                  ellipsis={{
                    tooltip: eventCenterInfo.blockGuidList?.map((item, index) => (
                      <>
                        {item}
                        {index + 1 !== eventCenterInfo?.blockGuidList?.length && (
                          <Divider type="vertical" spaceSize="mini" />
                        )}
                      </>
                    )),
                  }}
                >
                  {eventCenterInfo.blockGuidList?.map((item, index) => (
                    <>
                      {item}
                      {index + 1 !== eventCenterInfo?.blockGuidList?.length && (
                        <Divider type="vertical" spaceSize="mini" />
                      )}
                    </>
                  ))}
                </Typography.Text>
              ) : (
                eventCenterInfo.blockTag
              )}
            </Descriptions.Item>
            {features?.isFalseAlarm === 'required' && (
              <Descriptions.Item label="是否误报">
                <Typography.Text
                  type={
                    eventCenterInfo.isFalseAlarm === BackendFalseAlarm.None ? undefined : 'danger'
                  }
                >
                  {FalseAlarmMap[eventCenterInfo.isFalseAlarm as BackendFalseAlarm]}
                </Typography.Text>
              </Descriptions.Item>
            )}
            <Descriptions.Item label="事件来源">
              {eventCenterInfo.eventSourceName || '--'}
            </Descriptions.Item>
            {/* 事件发生时间,条件渲染 */}
            <Descriptions.Item label="事件发生时间">
              {eventCenterInfo.occurTime
                ? dayjs(eventCenterInfo.occurTime).format('YYYY-MM-DD HH:mm:ss')
                : '--'}
            </Descriptions.Item>
            <Descriptions.Item label="事件专业">
              {eventCenterInfo.majorName ? `${eventCenterInfo.majorName}` : '--'}
            </Descriptions.Item>

            <Descriptions.Item label="事件类型">
              {eventCenterInfo.topCategoryName
                ? `${eventCenterInfo.topCategoryName}/${eventCenterInfo.secondCategoryName}`
                : '--'}
            </Descriptions.Item>

            <Descriptions.Item
              label="事件描述"
              // span={2}
              contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
            >
              <Typography.Text
                ellipsis={{
                  tooltip: eventCenterInfo.eventDesc,
                }}
              >
                {eventCenterInfo.eventDesc || '--'}
              </Typography.Text>
            </Descriptions.Item>
            <Descriptions.Item label="附件">
              {Array.isArray(eventCenterInfo.addFileInfos) &&
              eventCenterInfo.addFileInfos.length > 0 ? (
                <SimpleFileList
                  files={eventCenterInfo.addFileInfos.map(file => McUploadFile.fromJSON(file))}
                >
                  <Button type="link" compact>
                    查看
                  </Button>
                </SimpleFileList>
              ) : (
                '--'
              )}
            </Descriptions.Item>
            <Descriptions.Item label="故障位置">
              <EventLocationDrawer eventId={eventCenterInfo.eventNo} />
            </Descriptions.Item>
            <Descriptions.Item
              label="故障说明"
              contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
            >
              <Typography.Text
                ellipsis={{
                  tooltip: eventCenterInfo.faultDesc,
                }}
              >
                {eventCenterInfo.faultDesc || '--'}
              </Typography.Text>
            </Descriptions.Item>
            <Descriptions.Item
              label="业务影响"
              contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
            >
              <InfluenceSurfaceModal eventId={eventCenterInfo.id} />
            </Descriptions.Item>
            <Descriptions.Item
              label="责任人"
              contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
            >
              {featuresOnwerMultiple ? (
                <Typography.Text
                  ellipsis={{
                    tooltip: eventCenterInfo.eventOwnerInfoList?.map((item, index) => (
                      <>
                        <UserLink key={item.id} userName={item.userName} userId={item.id} />
                        {index + 1 !== eventCenterInfo?.eventOwnerInfoList?.length && (
                          <Divider type="vertical" spaceSize="mini" />
                        )}
                      </>
                    )),
                  }}
                >
                  {eventCenterInfo.eventOwnerInfoList?.map((item, index) => (
                    <>
                      <UserLink key={item.id} userName={item.userName} userId={item.id} />
                      {index + 1 !== eventCenterInfo?.eventOwnerInfoList?.length && (
                        <Divider type="vertical" spaceSize="mini" />
                      )}
                    </>
                  ))}
                </Typography.Text>
              ) : (
                <UserLink
                  userId={eventCenterInfo.eventOwnerId}
                  userName={eventCenterInfo.eventOwnerName}
                  external
                />
              )}
            </Descriptions.Item>
          </Descriptions>
        ) : (
          <Descriptions column={4}>
            <Descriptions.Item label="事件级别">
              <EventLevelText code={eventCenterInfo.eventLevel} />
            </Descriptions.Item>
            <Descriptions.Item label="位置">{eventCenterInfo.blockTag}</Descriptions.Item>
            <Descriptions.Item label="是否误报">
              <Typography.Text
                type={
                  eventCenterInfo.isFalseAlarm === BackendFalseAlarm.None ? undefined : 'danger'
                }
              >
                {FalseAlarmMap[eventCenterInfo.isFalseAlarm as BackendFalseAlarm]}
              </Typography.Text>
            </Descriptions.Item>
            <Descriptions.Item label="事件来源">
              {' '}
              {eventCenterInfo.eventSourceName}
            </Descriptions.Item>
            <Descriptions.Item label="事件类型">
              {eventCenterInfo.topCategoryName
                ? `${eventCenterInfo.topCategoryName}/${eventCenterInfo.secondCategoryName}`
                : '--'}
            </Descriptions.Item>
            {/* 事件发生时间,条件渲染 */}
            <Descriptions.Item label="事件发生时间">
              {eventCenterInfo.occurTime
                ? dayjs(eventCenterInfo.occurTime).format('YYYY-MM-DD HH:mm:ss')
                : '--'}
            </Descriptions.Item>
            <Descriptions.Item
              label="事件描述"
              span={2}
              contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
            >
              <Typography.Text
                ellipsis={{
                  tooltip: eventCenterInfo.eventDesc,
                }}
              >
                {eventCenterInfo.eventDesc}
              </Typography.Text>
            </Descriptions.Item>
            <Descriptions.Item label="目标类型">
              {FAULT_TARGET_TYPE_TEXT[eventCenterInfo.infoType]}
            </Descriptions.Item>
            <Descriptions.Item label="目标名称" contentStyle={{ overflow: 'hidden' }}>
              <FaultTargetLink
                type={eventCenterInfo.infoType}
                causeDevices={eventCenterInfo.causeDevices}
              />
            </Descriptions.Item>
            <Descriptions.Item
              label={featuresIsOwnerLabelChangeRequired ? '负责人' : 'Owner'}
              contentStyle={{ overflow: 'hidden', paddingRight: 16 }}
            >
              {featuresOnwerMultiple ? (
                <Typography.Text
                  ellipsis={{
                    tooltip: eventCenterInfo.eventOwnerInfoList?.map((item, index) => (
                      <>
                        <UserLink key={item.id} userName={item.userName} userId={item.id} />
                        {index + 1 !== eventCenterInfo?.eventOwnerInfoList?.length && (
                          <Divider type="vertical" spaceSize="mini" />
                        )}
                      </>
                    )),
                  }}
                >
                  {eventCenterInfo.eventOwnerInfoList?.map((item, index) => (
                    <>
                      <UserLink key={item.id} userName={item.userName} userId={item.id} />
                      {index + 1 !== eventCenterInfo?.eventOwnerInfoList?.length && (
                        <Divider type="vertical" spaceSize="mini" />
                      )}
                    </>
                  ))}
                </Typography.Text>
              ) : (
                <UserLink
                  userId={eventCenterInfo.eventOwnerId}
                  userName={eventCenterInfo.eventOwnerName}
                  external
                />
              )}
            </Descriptions.Item>
            <Descriptions.Item label="变更导致">{generateChangeContent()}</Descriptions.Item>
            {featuresReasonTypeRequired && (
              <Descriptions.Item label="原因类型">
                {eventCenterInfo.causeBy ? (
                  <MetaTypeText code={eventCenterInfo.causeBy} metaType={MetaType.REASON_TYPE} />
                ) : (
                  '--'
                )}
              </Descriptions.Item>
            )}
            {featuresReasonRequired && (
              <Descriptions.Item label="原因描述" span={2}>
                {eventCenterInfo.causeDesc}
              </Descriptions.Item>
            )}
            <Descriptions.Item label="责任部门">
              {eventCenterInfo.liableDept && (
                <MetaTypeText
                  code={eventCenterInfo.liableDept}
                  metaType={MetaType.RESPONSIBLE_SECTOR}
                />
              )}
            </Descriptions.Item>
            <Descriptions.Item
              label={featuresIsResponsiblePersonLabelChangeRequired ? '关联责任人' : '责任人'}
            >
              {eventCenterInfo.liablePersonId ? (
                <UserLink
                  userId={Number(eventCenterInfo.liablePersonId)}
                  userName={eventCenterInfo.liablePersonName}
                  external
                />
              ) : (
                '--'
              )}
            </Descriptions.Item>
            <Descriptions.Item label="附件">
              {Array.isArray(eventCenterInfo.addFileInfos) &&
                eventCenterInfo.addFileInfos.length > 0 && (
                  <SimpleFileList
                    files={eventCenterInfo.addFileInfos.map(file => McUploadFile.fromJSON(file))}
                  >
                    <Button type="link" compact>
                      查看
                    </Button>
                  </SimpleFileList>
                )}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Space>
    </Card>
  );
}
export const FaultTargetLink = ({
  type,
  causeDevices,
  width,
}: {
  type: FaultTargetType;
  causeDevices: CauseDevice[];
  width?: number;
}) => {
  const [configUtil] = useConfigUtil();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');

  const featuresListInfoIsFull = features.listInfo === 'full';

  const causeDevicesLinkContent = (isInPopover: boolean = false) => {
    switch (type) {
      case FaultTargetType.Device:
        return getDeviceContent(isInPopover);
      case FaultTargetType.Room:
        return getRoomContent(isInPopover);
      case FaultTargetType.Other:
        return getOtherContent();
    }
  };
  function getDeviceContent(isInPopover: boolean) {
    return causeDevices.map((item, index) => (
      <span key={item.deviceGuid}>
        <Link
          key={item.deviceGuid}
          target="_blank"
          to={generateDeviceRecordRoutePath({
            guid: item.deviceGuid,
          })}
        >
          {item.deviceName}
          (<DeviceTypeText code={item.deviceType} />
          {featuresListInfoIsFull && <>/{item.roomTag}</>})
        </Link>
        {!isInPopover && index !== causeDevices.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }

  function getRoomContent(isInPopover: boolean) {
    return causeDevices.map((item, index) => (
      <span key={item.roomTag}>
        <Typography.Link
          key={item.roomTag}
          onClick={() => {
            window.open(
              generateRoomMonitoringUrl({
                idc: item.idcTag,
                block: item.blockTag,
                room: item.roomTag,
              })
            );
          }}
        >
          {item.roomTag}({item.deviceTypeName})
        </Typography.Link>
        {!isInPopover && index !== causeDevices.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }

  function getOtherContent() {
    return (
      <>
        {Array.isArray(causeDevices) &&
          causeDevices.map(item => <span key={item.deviceGuid}>{item.deviceName}</span>)}
      </>
    );
  }
  return (
    <Popover
      content={
        <Space style={{ maxWidth: 400 }} split={<Divider type="vertical" spaceSize="mini" />} wrap>
          {causeDevicesLinkContent(true)}
        </Space>
      }
    >
      <Typography.Text
        ellipsis
        style={{
          width,
          color: type !== FaultTargetType.Other ? `var(--${prefixCls}-primary-color)` : undefined,
        }}
      >
        {causeDevicesLinkContent()}
      </Typography.Text>
    </Popover>
  );
};
