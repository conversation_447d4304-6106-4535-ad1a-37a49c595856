import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import type { BackendEventUpgradeLevel } from '@manyun/ticket.model.event';
import { remindUserToRespond } from '@manyun/ticket.service.remind-user-to-respond';

export type ReminderButtonProps = {
  eventUpgradeLevel: BackendEventUpgradeLevel;
  eventId: number;
  onSuccess: () => void;
};
export function ReminderButton({ eventUpgradeLevel, eventId, onSuccess }: ReminderButtonProps) {
  const [loading, setLoading] = useState(false);
  const handleClick = async () => {
    setLoading(true);
    const { error } = await remindUserToRespond({ eventId, upgradeLevel: eventUpgradeLevel });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('催办成功');
    onSuccess();
  };
  return (
    <Button type="link" loading={loading} onClick={handleClick}>
      催办
    </Button>
  );
}
