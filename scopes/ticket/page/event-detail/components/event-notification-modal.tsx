import dayjs from 'dayjs';
import sortBy from 'lodash.sortby';
import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { useLazyEventProcessRecords } from '@manyun/ticket.gql.client.tickets';
import type { CauseDevice } from '@manyun/ticket.model.event';
import {
  EventSpecificProcessStatus,
  FAULT_TARGET_TYPE_TEXT,
  FaultTargetType,
} from '@manyun/ticket.model.event';
import { selectEventDetail } from '@manyun/ticket.state.event';
import { NotificationModalButton } from '@manyun/ticket.ui.notification-modal-button';

export const SUBSCRIPTION_TYPE = {
  ALARM_SUBSCRIBE: 'ALARM_SUBSCRIBE',
  EVENT_SUBSCRIBE: 'EVENT_SUBSCRIBE',
};
export function processingSubscriptionLevel({
  text = '',
  type,
  cumulative = false,
}: {
  text: string;
  type: string;
  cumulative?: boolean;
}) {
  if (!text) {
    return text;
  }
  let levelText;
  if (typeof text === 'number') {
    levelText = text;
  } else {
    const index = text.lastIndexOf('_');
    if (index < 0) {
      return text;
    }
    levelText = text.substring(index + 1, text.length);
  }
  if (cumulative) {
    levelText = Number(levelText) + 1;
  }
  if (type === SUBSCRIPTION_TYPE.ALARM_SUBSCRIBE || type === SUBSCRIPTION_TYPE.EVENT_SUBSCRIBE) {
    return levelText.toString();
  }

  return levelText.toString();
}

export type EventNotificationModalProps = {
  id: number;
};

export function EventNotificationModal({ id }: EventNotificationModalProps) {
  const [getPhaseRecords, { data: phaseRecords }] = useLazyEventProcessRecords();

  const eventCenterInfo = useSelector(selectEventDetail);
  // const config = useSelector(selectCurrentConfig);
  // const configUtil = new ConfigUtil(config);
  const [configUtil] = useConfigUtil();
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureIsEventConfigWithProcessEngineRequired =
    features.isEventConfigWithProcessEngine === 'required';
  const featureSpecialInfoSort = features.specialInfoSort;
  const eventTargetId = featureIsEventConfigWithProcessEngineRequired
    ? eventCenterInfo.eventNo!
    : eventCenterInfo.id;
  const notificationInfo = {
    id: eventTargetId,
    idcTag: eventCenterInfo.idcTag,
    blockTag: eventCenterInfo.blockTag,
    eventLevel:
      eventCenterInfo.eventLevel &&
      processingSubscriptionLevel({
        text: eventCenterInfo.eventLevel,
        type: SUBSCRIPTION_TYPE.EVENT_SUBSCRIBE,
      }),
    eventTitle: eventCenterInfo?.eventTitle,
    detectReason: eventCenterInfo?.detectReason ?? '--',
    eventStatus: eventCenterInfo.eventStatus && eventCenterInfo.eventStatus.desc,
    eventSource: eventCenterInfo.eventSourceName,

    eventCategory: eventCenterInfo.topCategoryName
      ? `${eventCenterInfo.topCategoryName}-${eventCenterInfo.secondCategoryName}`
      : '--',
    occurTime: eventCenterInfo.occurTime,
    eventDesc: eventCenterInfo.eventDesc,
    causeDesc: eventCenterInfo.causeDesc,
    reportContent: `[${
      eventCenterInfo.eventStatus && eventCenterInfo.eventStatus.desc
    }][${processingSubscriptionLevel({
      text: eventCenterInfo.eventLevel,
      type: SUBSCRIPTION_TYPE.EVENT_SUBSCRIBE,
    })}][${eventCenterInfo.idcTag}][${dayjs(eventCenterInfo.occurTime).format(
      'YYYY-MM-DD HH:mm:ss'
    )}][${eventCenterInfo.eventDesc}]`.replace(/\[null\]/g, ''),
    subscribeCode: eventCenterInfo.eventLevel,
    causeDevices: generateCauseDevices(eventCenterInfo.infoType, eventCenterInfo.causeDevices),
    infoType: eventCenterInfo.infoType && FAULT_TARGET_TYPE_TEXT[eventCenterInfo.infoType],
    featureSpecialInfoSort,
    blockGuidList: eventCenterInfo.blockGuidList,
    locationList: eventCenterInfo.locationList,
  };
  useEffect(() => {
    if (featureIsEventConfigWithProcessEngineRequired) {
      getPhaseRecords({
        variables: {
          mode: 'multiple',
          eventId: eventCenterInfo.id,
          eventPhase: EventSpecificProcessStatus.Emergency,
        },
      });
    }
  }, [eventCenterInfo.id, featureIsEventConfigWithProcessEngineRequired, getPhaseRecords]);
  const groupedEventProcessRecordsByPhase = sortBy(
    phaseRecords?.eventProcessRecords?.data,
    'handleTime'
  )?.reduce(
    (accumulator, current) => {
      // 获取当前记录的eventPhase
      const phase = current.eventPhase;
      // 如果accumulator中还没有这个phase的数组，则创建一个
      if (!accumulator[phase]) {
        accumulator[phase] = [];
      }
      // 将当前记录添加到对应的phase数组中
      accumulator[phase].push(
        `${current.handleContent}(${dayjs(current.handleTime).format('YYYY-MM-DD HH:mm:ss')})`
      );
      // 返回accumulator供下次迭代使用
      return accumulator;
    },
    {} as { [x: string]: string[] }
  );
  return (
    <Space>
      <NotificationModalButton
        type="event"
        notificationInfo={{
          ...notificationInfo,
          creatorId: eventCenterInfo.creatorId,
          creatorName: eventCenterInfo.createUserName,
          causeDevices: generateCauseDevices(
            eventCenterInfo.infoType,
            eventCenterInfo.causeDevices
          ),
          groupedEventProcessRecordsByPhase,
        }}
        deviceGuids={generateDeviceGuid(eventCenterInfo.infoType, eventCenterInfo.causeDevices)}
        eventId={eventCenterInfo.id}
      />
    </Space>
  );
}
const generateDeviceGuid = (type: FaultTargetType, causeDevices: CauseDevice[]) => {
  switch (type) {
    case FaultTargetType.Device:
      return causeDevices.map((device: { deviceGuid: string }) => device.deviceGuid);

    case FaultTargetType.Other:
      return causeDevices.map((device: { deviceName: string }) => device.deviceName);
    case FaultTargetType.Room:
      return causeDevices.map((room: { roomTag: string }) => room.roomTag);

    default:
      return [];
  }
};
const generateCauseDevices = (type: FaultTargetType, causeDevices: CauseDevice[]) => {
  switch (type) {
    case FaultTargetType.Device:
    case FaultTargetType.Other:
      return causeDevices.map((device: { deviceName: string }) => device.deviceName);
    case FaultTargetType.Room:
      return causeDevices.map((room: { roomTag: string }) => room.roomTag);

    default:
      return [];
  }
};
