import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import moment, { type Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { eventRelieve } from '@manyun/ticket.service.event-relieve';
import {
  getEventDetailAction,
  getEventLifeAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

export type EventRelieveModalProps = {
  id: number;
};
export function EventRelieveModal({ id }: EventRelieveModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const eventCenterInfo = useSelector(selectEventDetail);
  const dispatch = useDispatch();
  const onOk = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const { optPeople, relieveTime, ...rest } = formValue;
      const { error } = await eventRelieve({
        ...rest,
        relieveTime: relieveTime.valueOf(),
        eventId: id,
      });
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      dispatch(getEventDetailAction(id));
      dispatch(getEventLifeAction(id));
    });
  };
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };

  return (
    <>
      <Button type="primary" onClick={showModal}>
        事件缓解并通报
      </Button>
      <Modal
        title="事件缓解并通报"
        okButtonProps={{ loading }}
        open={visible}
        onOk={onOk}
        onCancel={closeModal}
      >
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={{
            optPeople: {
              value: Number(eventCenterInfo.secRespondUserId),
              label: eventCenterInfo.secRespondUserName,
            },
          }}
        >
          <Form.Item
            label="缓解人员"
            name="optPeople"
            rules={[{ required: true, message: '请选择缓解人员' }]}
          >
            <UserSelect disabled style={{ width: 216 }} />
          </Form.Item>
          <Form.Item
            label="缓解时间"
            name="relieveTime"
            rules={[
              { required: true, message: '请选择缓解时间' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (
                    value &&
                    moment(value).diff(moment(eventCenterInfo.occurTime), 'seconds') > 0
                  ) {
                    return Promise.resolve();
                  }
                  return Promise.reject(new Error('事件缓解时间不可早于事件发生时间'));
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              disabledDate={disabledDayAfterToday}
              disabledTime={disabledDateTimeAfterToday}
            />
          </Form.Item>
          <Form.Item
            label="缓解方案"
            name="relieveDesc"
            rules={[
              { required: true, message: '请输入缓解方案' },
              {
                max: 500,
                message: '最多输入 500 个字符！',
              },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
export function disabledDayAfterToday(current: Moment) {
  return current.valueOf() > moment().endOf('day').valueOf();
}
export function computeTimeRange(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export function disabledDateTimeAfterToday(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (current) {
    const choseHour = current.hours();
    const choseMinute = current.minute();
    const isToday = current.isSame(compareMoment, 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => computeTimeRange(hours + 1, 24),
          disabledMinutes: () => computeTimeRange(minute + 1, 60),
          disabledSeconds: () => (choseMinute === minute ? computeTimeRange(second, 60) : []),
        };
      }
      return {
        disabledHours: () => computeTimeRange(hours + 1, 24),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}
