import React from 'react';
import { useSelector } from 'react-redux';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { FaultTargetType } from '@manyun/ticket.model.event';
import { selectEventDetail } from '@manyun/ticket.state.event';

import { InfluenceSurface } from '@manyun/dc-brain.legacy.components';

import { useEventData } from './event-data-context';

export function InfluenceSurfaceTab() {
  const eventCenterInfo = useSelector(selectEventDetail);
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const {
    events: { showRacksImpacts, features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureIsEventConfigWithProcessEngineRequired =
    features.isEventConfigWithProcessEngine === 'required';
  const [{ pdfTabList, tabsExpandAll }, { setPdfTabList }] = useEventData();

  return (
    <InfluenceSurface
      idcTag={eventCenterInfo.idcTag}
      blockTag={
        eventCenterInfo.blockTag &&
        eventCenterInfo.blockTag.substring(eventCenterInfo.blockTag.indexOf('.') + 1)
      }
      targetId={eventCenterInfo.id}
      featureIsEventConfigWithProcessEngineRequired={featureIsEventConfigWithProcessEngineRequired}
      eventNo={eventCenterInfo.eventNo ?? ''}
      targetType="EVENT"
      showRacksImpacts={showRacksImpacts}
      tabBarStyle={{ borderBottom: 0 }}
      deviceGuids={generateCauseDevices(eventCenterInfo.infoType, eventCenterInfo.causeDevices)}
      pdfTabList={pdfTabList}
      tabsExpandAll={tabsExpandAll}
      setPdfTabList={setPdfTabList}
    />
  );
}
const generateCauseDevices = (type: FaultTargetType, causeDevices: { deviceGuid: string }[]) => {
  if (type === FaultTargetType.Device) {
    return causeDevices.map((device: { deviceGuid: string }) => device.deviceGuid).join(',');
  }
  return null;
};
