import { omit } from 'lodash';
import moment from 'moment';
import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useEventRelateRiskTicketCreation } from '@manyun/ticket.gql.client.tickets';
import { RiskRegister } from '@manyun/ticket.model.risk-register';
import { RiskRegisterForm } from '@manyun/ticket.page.risk-register-mutator';
import { generateRiskRegisterDetailLocation } from '@manyun/ticket.route.ticket-routes';

export type CreateRiskDrawerProps = {
  relateEvent: {
    id: number | string;
    eventNo?: string;
    description?: string | null;
    blockGuid: string;
  };
  onSuccess?: () => void;
} & Omit<ButtonProps, 'onClick'>;

export function CreateRiskDrawerYg({
  relateEvent,
  onSuccess,
  ...restProps
}: CreateRiskDrawerProps) {
  const [form] = Form.useForm();
  const history = useHistory();
  const [visible, setVisible] = useState<boolean>(false);

  const [createEventRelateRiskTicket, { loading }] = useEventRelateRiskTicketCreation({
    onCompleted(data) {
      if (!data || !data.createEventRelateRiskTicket?.success) {
        message.error(data.createEventRelateRiskTicket?.message);
        return;
      }
      setVisible(false);
      message.success('风险单创建成功');
      history.push(
        generateRiskRegisterDetailLocation({
          id: data.createEventRelateRiskTicket.riskTicketNumber!,
        })
      );
    },
  });
  const onOk = () => {
    form.validateFields().then(async formValue => {
      createEventRelateRiskTicket({
        variables: {
          query: {
            eventId: relateEvent.id,
            ...omit(
              RiskRegister.fromJSON(formValue).toApiObject(),
              'riskObjectType',
              'riskObjectName',
              'relatePersonId',
              'createUserId',
              'relatePersonName'
            ),
            planCompleteTime: moment(formValue.planCompleteTime).valueOf(),
            fileInfoList:
              formValue.fileInfoList?.length > 0
                ? formValue.fileInfoList.map(file => McUploadFile.fromJSON(file))
                : [],
          },
        },
      });
    });
  };

  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    form.resetFields();
    setVisible(false);
  };

  return (
    <>
      <Button onClick={showModal} {...restProps}>
        创建风险
      </Button>
      <Drawer
        width={1300}
        // style={{ zIndex: 1009 }}
        title="创建风险单"
        open={visible}
        placement="right"
        // size="large"
        extra={
          <Space>
            <Button onClick={closeModal}>取消</Button>
            <Button type="primary" loading={loading} onClick={onOk}>
              提交
            </Button>
          </Space>
        }
        onClose={closeModal}
      >
        <Alert
          message="关联事件"
          description={`${relateEvent.eventNo ?? relateEvent.id}-${relateEvent.description}`}
          type="info"
        />

        <RiskRegisterForm form={form} />
      </Drawer>
    </>
  );
}
