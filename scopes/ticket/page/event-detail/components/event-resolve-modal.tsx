import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { eventResolve } from '@manyun/ticket.service.event-resolve';
import {
  getEventDetailAction,
  getEventLifeAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

import { useEventData } from './event-data-context';
import { disabledDateTimeAfterToday, disabledDayAfterToday } from './event-relieve-modal';

export type EventResolveModalProps = {
  id: number;
};
export function EventResolveModal({ id }: EventResolveModalProps) {
  const [{ parentForm }, { setDrawerVisible }] = useEventData();

  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const eventCenterInfo = useSelector(selectEventDetail);
  const onOk = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const { resolveUserId, resolveTime, ...rest } = formValue;
      const { error } = await eventResolve({
        ...rest,
        resolveUserId: eventCenterInfo.eventOwnerId,
        resolveTime: resolveTime.valueOf(),
        eventId: id,
      });
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      dispatch(getEventDetailAction(id));
      dispatch(getEventLifeAction(id));
    });
  };
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          if (parentForm) {
            parentForm
              .validateFields()
              .then(value => {
                showModal();
              })
              .catch(err => {
                setDrawerVisible(true);
              });
          }
        }}
      >
        事件解决并通报
      </Button>
      <Modal
        title="事件解决并通报"
        okButtonProps={{ loading }}
        open={visible}
        onOk={onOk}
        onCancel={closeModal}
      >
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={{
            resolveUserId: {
              value: Number(eventCenterInfo.thiRespondUserId),
              label: eventCenterInfo.thiRespondUserName,
            },
          }}
        >
          <Form.Item
            label="解决人员"
            name="resolveUserId"
            rules={[{ required: true, message: '请选择解决人员' }]}
          >
            <UserSelect disabled style={{ width: 216 }} />
          </Form.Item>
          <Form.Item
            label="解决时间"
            name="resolveTime"
            rules={[
              { required: true, message: '请选择解决时间' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (value) {
                    const isResolveTimeBiggerThanDetectTime =
                      moment(value).diff(moment(eventCenterInfo.detectTime), 'seconds') > 0;
                    if (eventCenterInfo.relieveTime) {
                      if (
                        moment(value).diff(moment(eventCenterInfo.relieveTime), 'seconds') > 0 &&
                        isResolveTimeBiggerThanDetectTime
                      ) {
                        return Promise.resolve();
                      }
                      return Promise.reject(
                        new Error('事件解决时间不可早于事件定位时间和事件缓解时间')
                      );
                    } else {
                      if (isResolveTimeBiggerThanDetectTime) {
                        return Promise.resolve();
                      }
                      return Promise.reject(new Error('事件解决时间不可早于事件定位时间'));
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              disabledDate={disabledDayAfterToday}
              disabledTime={disabledDateTimeAfterToday}
            />
          </Form.Item>
          <Form.Item
            label="解决方案"
            name="resolveDesc"
            rules={[
              { required: true, message: '请输入解决方案' },
              {
                max: 500,
                message: '最多输入 500 个字符！',
              },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
