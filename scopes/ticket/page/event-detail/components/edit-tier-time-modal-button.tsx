import React, { useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import EditOutlined from '@ant-design/icons/es/icons/EditOutlined';
import moment, { type Moment } from 'moment';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { BackendEventTierLevel, getEventLocales } from '@manyun/ticket.model.event';
import { updateUpgradeRecords } from '@manyun/ticket.service.update-upgrade-records';
import {
  getEventDetailAction,
  getEventLifeAction,
  getEventUpgradeRecordsAction,
} from '@manyun/ticket.state.event';

export type EditTierTimeModalButtonProps = {
  /** 事件Id */
  eventId: number;
  tierLevel: BackendEventTierLevel;
  timeGroup: {
    currentUpgradeTime: Moment;
    currentRespondTime: Moment;
    prefixRespondTime?: Moment;
    postUpdateTime?: Moment;
  };
};

export type FormType = {
  tierTime: Moment;
};
export function EditTierTimeModalButton({
  eventId,
  tierLevel,
  timeGroup,
}: EditTierTimeModalButtonProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const locales = useMemo(() => getEventLocales(), []);

  const dispatch = useDispatch();
  const { currentUpgradeTime, currentRespondTime, prefixRespondTime, postUpdateTime } = timeGroup;
  const isUpgradeTime = tierLevel % 2 === 0;
  const [form] = Form.useForm<FormType>();
  const handleSubmit = () => {
    form.validateFields().then(async value => {
      const { tierTime } = value;
      const formatTierTime = tierTime.millisecond(0).valueOf();
      setLoading(true);
      const getParams = () => {
        switch (tierLevel) {
          case BackendEventTierLevel.Tier1Upgrade:
            return { firUpgradeTime: formatTierTime };
          case BackendEventTierLevel.Tier1Respond:
            return { firRespondTime: formatTierTime };
          case BackendEventTierLevel.Tier2Upgrade:
            return { secUpgradeTime: formatTierTime };
          case BackendEventTierLevel.Tier2Respond:
            return { secRespondTime: formatTierTime };
          case BackendEventTierLevel.Tier3Upgrade:
            return { thiUpgradeTime: formatTierTime };
          case BackendEventTierLevel.Tier3Respond:
            return { thiRespondTime: formatTierTime };
        }
      };
      const { error } = await updateUpgradeRecords({
        id: eventId,
        ...getParams(),
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('修改成功');
      setVisible(false);
      dispatch(getEventDetailAction(eventId));
      dispatch(getEventLifeAction(eventId));
      dispatch(getEventUpgradeRecordsAction({ pageNum: 1, pageSize: 10, eventId }));
    });
  };
  return (
    <>
      <EditOutlined
        style={{ color: `var(--${prefixCls}-primary-color)` }}
        onClick={() => {
          setVisible(true);
        }}
      />
      <Modal
        title="修改时间"
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        forceRender
        confirmLoading={loading}
        okText="提交"
        onOk={handleSubmit}
        onCancel={() => {
          form.resetFields();
          setVisible(false);
        }}
      >
        <Form
          form={form}
          initialValues={{
            tierTime: isUpgradeTime ? currentUpgradeTime : currentRespondTime,
          }}
        >
          <Form.Item
            name="tierTime"
            label={locales.tierLevel[tierLevel]}
            rules={[
              { required: true, message: '请如实填写时间' },
              {
                validator(_, value) {
                  if (
                    moment(value).diff(
                      isUpgradeTime ? prefixRespondTime : currentUpgradeTime,
                      'seconds'
                    ) <= 0
                  ) {
                    return Promise.reject(
                      new Error(
                        `${locales.tierLevel[tierLevel]}时间要晚于${
                          tierLevel === BackendEventTierLevel.Tier1Upgrade
                            ? '发生'
                            : locales.tierLevel[(tierLevel - 1) as BackendEventTierLevel]
                        }时间`
                      )
                    );
                  }
                  if (
                    moment(value).diff(
                      isUpgradeTime ? currentRespondTime : postUpdateTime,
                      'seconds'
                    ) >= 0
                  ) {
                    return Promise.reject(
                      new Error(
                        `${locales.tierLevel[tierLevel]}时间要早于${
                          tierLevel === BackendEventTierLevel.Tier3Respond
                            ? '事件解决'
                            : locales.tierLevel[(tierLevel + 1) as BackendEventTierLevel]
                        }时间`
                      )
                    );
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <DatePicker style={{ width: '100%' }} showTime />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
