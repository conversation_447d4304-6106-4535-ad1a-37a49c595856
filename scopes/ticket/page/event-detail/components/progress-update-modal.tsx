import React, { useState } from 'react';
import { useDispatch } from 'react-redux';

import { UploadOutlined } from '@ant-design/icons';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { createEventProgress } from '@manyun/ticket.service.create-event-progress';
import {
  getEventDetailAction,
  getEventLifeAction,
  getProgressUpdateListAction,
} from '@manyun/ticket.state.event';

import { disabledDateTimeAfterToday, disabledDayAfterToday } from './event-relieve-modal';

export type ProgressUpgradeModalProps = {
  id: number;
};
type FormValue = {
  optContent: string;
  optPlan: string;
  optTime: Moment;
  optPeople: { key: string; label: string };
};
export function ProgressUpgradeModal({ id }: ProgressUpgradeModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm<FormValue>();
  const dispatch = useDispatch();
  const onOk = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const { optPeople, optTime, ...rest } = formValue;
      const { error } = await createEventProgress({
        ...rest,
        optTime: optTime.valueOf(),
        optStaffId: optPeople.key,
        optStaffName: optPeople.label,
        eventId: id,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      dispatch(getEventDetailAction(id));
      dispatch(
        getProgressUpdateListAction({
          pageNum: 1,
          pageSize: 400,
          eventId: id,
        })
      );
      setVisible(false);
      dispatch(getEventLifeAction(id));
    });
  };
  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    setVisible(false);
  };
  return (
    <>
      <Button type="primary" onClick={showModal}>
        进展更新
      </Button>
      <Modal
        title="进展更新"
        okButtonProps={{ loading }}
        open={visible}
        afterClose={() => {
          form.resetFields();
        }}
        onCancel={closeModal}
        onOk={onOk}
      >
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item
            label="处理人"
            name="optPeople"
            rules={[{ required: true, message: '请选择处理人' }]}
          >
            <UserSelect style={{ width: 216 }} />
          </Form.Item>
          <Form.Item
            label="更新时间"
            name="optTime"
            rules={[{ required: true, message: '请选择更新时间' }]}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              disabledDate={disabledDayAfterToday}
              disabledTime={disabledDateTimeAfterToday}
            />
          </Form.Item>
          <Form.Item
            label="处理内容"
            name="optContent"
            rules={[
              { required: true, message: '请输入处理内容' },
              {
                max: 500,
                message: '最多输入 500 个字符！',
              },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            label="后续计划"
            name="optPlan"
            rules={[
              {
                max: 100,
                message: '最多输入 100 个字符！',
              },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
          <Form.Item
            name="files"
            label="附件"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <McUpload
              key="upload"
              accept=".jpg, .png ,.jpeg ,.gif, .txt ,.doc, .docx, .xls, .xlsx ,.ppt, .pptx ,.pdf, .zip,.rar"
              maxCount={1}
            >
              <Button icon={<UploadOutlined />}>点此上传</Button>
            </McUpload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
