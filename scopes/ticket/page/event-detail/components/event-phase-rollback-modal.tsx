import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { BackendEventOperationStatusCode } from '@manyun/ticket.model.event';
import { rollbackEventPhase } from '@manyun/ticket.service.rollback-event-phase';
import {
  getEventDetailAction,
  getEventLifeAction,
  getProgressUpdateListAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

export type EventPhaseRollbackModalProps = {
  id: number;
  type: BackendEventOperationStatusCode;
};

export function EventPhaseRollbackModal({ id, type }: EventPhaseRollbackModalProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const eventCenterInfo = useSelector(selectEventDetail);
  const modalText = getModalText(type);
  const onOk = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const { optPeople, ...rest } = formValue;
      const { error } = await rollbackEventPhase({
        ...rest,

        eventId: id,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('事件已退回至处理人');
      dispatch(getEventDetailAction(id));
      dispatch(
        getProgressUpdateListAction({
          pageNum: 1,
          pageSize: 400,
          eventId: id,
        })
      );
      setVisible(false);
      dispatch(getEventLifeAction(id));
    });
  };

  const closeModal = () => {
    setVisible(false);
  };
  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
      >
        {modalText}
      </Button>
      <Modal
        title={modalText}
        okButtonProps={{ loading }}
        open={visible}
        okText="提交"
        onCancel={closeModal}
        onOk={onOk}
      >
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
          <Form.Item
            label="退回人员"
            name="optPeople"
            initialValue={
              type === BackendEventOperationStatusCode.T3PhaseUpgrade
                ? {
                    value: Number(eventCenterInfo.secRespondUserId),
                    label: eventCenterInfo.secRespondUserName,
                  }
                : {
                    value: Number(eventCenterInfo.thiRespondUserId),
                    label: eventCenterInfo.thiRespondUserName,
                  }
            }
          >
            <UserSelect style={{ width: 216 }} disabled />
          </Form.Item>
          <Form.Item
            label="退回原因"
            name="reason"
            rules={[
              { required: true, message: '请输入退回原因' },
              {
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ]}
          >
            <Input.TextArea rows={3} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

function getModalText(type: BackendEventOperationStatusCode) {
  return type === BackendEventOperationStatusCode.T3PhaseUpgrade ? '未缓解退回' : '未解决退回';
}
