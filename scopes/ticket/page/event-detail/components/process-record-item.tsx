import React from 'react';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { User } from '@manyun/auth-hub.ui.user';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { EventPhase } from '@manyun/ticket.model.event';

export type ProcessRecordItemProps = {
  userId: number;
  userName: string;
  optTime: number;
  optContent: string;
  optPlan: string;
  /** 处理记录生成的事件阶段 */
  status?: EventPhase;
  addedFiles?: BackendMcUploadFile[];
};

export function ProcessRecordItem({
  userId,
  userName,
  optTime,
  optContent,
  optPlan,
  status,
  addedFiles,
}: ProcessRecordItemProps) {
  return (
    <div style={{ width: '100%', display: 'flex' }}>
      <User
        id={userId}
        name={userName}
        size={32}
        showName={false}
        avatarStyle={{ marginRight: 16 }}
      />
      <Space size={4} style={{ width: 'calc(100% - 48px)' }} direction="vertical">
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Space align="baseline" size={16}>
            <Typography.Title level={5} ellipsis>
              <User.Link id={userId} name={userName} />
            </Typography.Title>
            <ProcessRecordTag status={status} />
          </Space>

          <Typography.Text type="secondary" style={{ fontSize: 12 }}>
            {dayjs(optTime).format('YYYY.MM.DD HH:mm:ss')}
          </Typography.Text>
        </div>
        <Space direction="vertical" size={0} style={{ width: '100%' }}>
          <Row>
            <div style={{ width: 78 }}>处理内容：</div>
            <Typography.Paragraph style={{ flex: 1 }} ellipsis={{ rows: 2, tooltip: true }}>
              {optContent}
            </Typography.Paragraph>
          </Row>
          <Row>
            <div style={{ width: 78 }}>后续计划：</div>
            <Typography.Paragraph style={{ flex: 1 }} ellipsis={{ rows: 2, tooltip: true }}>
              {optPlan ?? '--'}
            </Typography.Paragraph>
          </Row>
          {Array.isArray(addedFiles) && addedFiles.length > 0 && (
            <Row>
              <Typography.Text>附件：</Typography.Text>
              <SimpleFileList files={addedFiles.map(file => McUploadFile.fromApiObject(file))}>
                <Button type="link" compact>
                  查看
                </Button>
              </SimpleFileList>
            </Row>
          )}
        </Space>
      </Space>
    </div>
  );
}
function ProcessRecordTag({ status }: { status?: EventPhase }) {
  switch (status) {
    case EventPhase.Respond:
      return <Tag color="geekblue">T1响应阶段</Tag>;

    case EventPhase.Remit:
      return <Tag color="cyan">T2缓解阶段</Tag>;

    case EventPhase.Resolved:
      return <Tag color="green">T3解决阶段</Tag>;

    default:
      return null;
  }
}
