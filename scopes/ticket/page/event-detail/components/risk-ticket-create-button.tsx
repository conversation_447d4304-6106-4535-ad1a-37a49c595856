import { UploadOutlined } from '@ant-design/icons';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { DeviceOrSpaceSelect } from '@manyun/resource-hub.ui.device-or-space-select';
import { DevicesTableSelectModal } from '@manyun/resource-hub.ui.device-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeCascader } from '@manyun/resource-hub.ui.meta-type-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { useEventRelateRiskTicketCreation } from '@manyun/ticket.gql.client.tickets';
import { FaultTargetType } from '@manyun/ticket.model.event';
import type { LabelInvalue } from '@manyun/ticket.page.risk-register-mutator';
import { generateRiskRegisterDetailLocation } from '@manyun/ticket.route.ticket-routes';
import { FaultTargetTypeSelect } from '@manyun/ticket.ui.fault-target-type-select';

export type RiskTicketCreateButtonProps = {
  relateEvent: { id: number; eventNo?: string; description: string; blockGuid: string };
} & Omit<ButtonProps, 'onClick'>;
type FormValue = {
  location: string[];
  owner: number[];
  source: string[];
  type: string[];
  category: string[];
  level: string;
  priority?: string[];
  optContent: string;
  riskObject: string | ({ key: string } | string)[];
  files: McUploadFileJSON[];
  riskObjectType: string;
  riskObjectName: LabelInvalue[] | string[] | string;
  riskIdentifier?: string;
};
export function RiskTicketCreateButton({ relateEvent, ...restProps }: RiskTicketCreateButtonProps) {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const { riskRegisters } = configUtil.getScopeCommonConfigs('ticket');
  const history = useHistory();
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm<FormValue>();
  const location = Form.useWatch('location', form);
  const files = Form.useWatch('files', form);
  const riskObjectType = Form.useWatch('riskObjectType', form);
  const { idc, block } = getSpaceGuidMap(
    Array.isArray(location) && location.length === 2 ? location[1] : ''
  );
  const { idc: eventIdc } = getSpaceGuidMap(relateEvent.blockGuid);
  const [createEventRelateRiskTicket] = useEventRelateRiskTicketCreation({
    onCompleted(data) {
      setLoading(false);
      if (!data || !data.createEventRelateRiskTicket?.success) {
        message.error(data.createEventRelateRiskTicket?.message);
        return;
      }
      setVisible(false);
      message.success('风险单创建成功');
      history.push(
        generateRiskRegisterDetailLocation({
          id: data.createEventRelateRiskTicket.riskTicketNumber!,
        })
      );
    },
  });
  const onOk = () => {
    form.validateFields().then(async formValue => {
      const {
        location,
        source,
        type,
        priority,
        level,
        optContent,
        owner,
        files,
        riskObjectType,
        riskObjectName,
        category,
        riskIdentifier,
      } = formValue;
      setLoading(true);
      createEventRelateRiskTicket({
        variables: {
          query: {
            eventId: relateEvent.id,
            idcTag: location[0],
            blockGuid: location.length > 1 ? location[1] : null,
            riskOwnerIdList: owner,
            riskResourceCode: source[0],
            riskTopTypeCode: category[0],
            riskSecTypeCode: type[0],
            riskPriorityCode: Array.isArray(priority) ? priority[0] : undefined,
            riskLevel: level,
            riskDesc: optContent,
            riskIdentifier: riskIdentifier,
            fileInfoList: files?.length > 0 ? files.map(file => McUploadFile.fromJSON(file)) : [],
            riskObject: {
              type: riskObjectType,
              names: getRiskObjectName(riskObjectName, riskObjectType),
            },
          },
        },
      });
    });
  };

  const getRiskObjectName = (
    riskObjectName: LabelInvalue[] | string[] | string,
    riskObjectType: string
  ): string => {
    if (riskObjectType === 'DEVICE' && Array.isArray(riskObjectName)) {
      return (riskObjectName as LabelInvalue[]).map(item => item.value).join(',');
    }
    if (riskObjectType === 'ROOM' && Array.isArray(riskObjectName)) {
      return riskObjectName.join(',');
    }
    return riskObjectName as string;
  };

  const showModal = () => {
    setVisible(true);
  };
  const closeModal = () => {
    form.resetFields();
    setVisible(false);
  };
  return (
    <>
      <Button onClick={showModal} {...restProps}>
        创建风险
      </Button>
      <Drawer
        style={{ zIndex: 1009 }}
        title="创建风险单"
        open={visible}
        placement="right"
        size="large"
        extra={
          <Space>
            <Button onClick={closeModal}>取消</Button>
            <Button type="primary" loading={loading} onClick={onOk}>
              提交
            </Button>
          </Space>
        }
        onClose={closeModal}
      >
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ location: [eventIdc, relateEvent.blockGuid] }}
        >
          <Space style={{ display: 'flex', width: '100%' }} direction="vertical" size="large">
            <Alert
              message="关联事件"
              description={`${relateEvent.eventNo ?? relateEvent.id}-${relateEvent.description}`}
              type="info"
            />
            <Container style={{ padding: 0 }}>
              <Space style={{ display: 'flex', width: '100%' }} direction="vertical" size="middle">
                <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                  基本信息
                </Typography.Title>
                <div style={{ width: 656 }}>
                  <Row>
                    <Col span={12}>
                      <Form.Item
                        label="位置"
                        name="location"
                        rules={[{ required: true, message: '请选择位置' }]}
                      >
                        <LocationCascader
                          includeVirtualBlocks
                          authorizedOnly
                          idc={eventIdc!}
                          style={{ width: '100%' }}
                          onChange={() => {
                            form.setFieldsValue({ riskObjectType: undefined });
                          }}
                        />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="责任人"
                        name="owner"
                        rules={[{ required: true, message: '请选择责任人' }]}
                      >
                        <UserSelect
                          style={{ width: '100%' }}
                          labelInValue={false}
                          mode="multiple"
                        />
                      </Form.Item>
                    </Col>
                  </Row>

                  <Row>
                    <Col span={12}>
                      <Form.Item
                        label="风险来源"
                        name="source"
                        rules={[{ required: true, message: '请选择风险来源' }]}
                      >
                        <MetaTypeCascader style={{ width: '100%' }} metaType={['RISK_RESOURCE']} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="风险类别"
                        name="category"
                        rules={[{ required: true, message: '请选择风险类别' }]}
                      >
                        <MetaTypeCascader style={{ width: '100%' }} metaType={['RISK_TOP_TYPE']} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={12}>
                      <Form.Item
                        label="风险类型"
                        name="type"
                        rules={[{ required: true, message: '请选择风险类型' }]}
                      >
                        <MetaTypeCascader style={{ width: '100%' }} metaType={['RISK_SEC_TYPE']} />
                      </Form.Item>
                    </Col>
                    <Col span={12}>
                      <Form.Item
                        label="风险等级"
                        name="level"
                        rules={[{ required: true, message: '请选择风险等级' }]}
                      >
                        <MetaTypeSelect
                          style={{ width: '100%' }}
                          showSearch
                          allowClear
                          optionLabelProp="label"
                          optionFilterProp="label"
                          metaType={MetaType.RISK_LEVEL}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    {riskRegisters.features.priority !== 'disabled' && (
                      <Col span={12}>
                        <Form.Item label="优先级" name="priority">
                          <MetaTypeCascader
                            style={{ width: '100%' }}
                            metaType={['RISK_PRIORITY']}
                          />
                        </Form.Item>
                      </Col>
                    )}
                    {riskRegisters.features.showIdentifyUser && (
                      <Col span={12}>
                        <Form.Item label="风险识别人" name="riskIdentifier">
                          <Input maxLength={10} allowClear />
                        </Form.Item>
                      </Col>
                    )}
                  </Row>
                  <Row>
                    <Col span={24}>
                      <Form.Item
                        label="风险描述"
                        name="optContent"
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 20 }}
                        rules={[
                          { required: true, message: '请输入风险描述' },
                          {
                            max: 300,
                            message: '最多输入 300 个字符！',
                          },
                        ]}
                      >
                        <Input.TextArea style={{ width: '100%' }} rows={3} />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={12}>
                      <Form.Item
                        name="files"
                        label="问题附件"
                        valuePropName="fileList"
                        getValueFromEvent={value => {
                          if (typeof value === 'object') {
                            return value.fileList;
                          }
                        }}
                      >
                        <McUpload
                          key="upload"
                          accept=".jpg, .png ,.jpeg ,.gif, .txt ,.doc, .docx, .xls, .xlsx ,.ppt, .pptx ,.pdf, .zip,.rar"
                          maxCount={5}
                          maxFileSize={20}
                        >
                          <Button disabled={files?.length >= 5} icon={<UploadOutlined />}>
                            点此上传
                          </Button>
                        </McUpload>
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Space>
            </Container>
            <Container style={{ padding: 0 }}>
              <Space style={{ display: 'flex', height: '100%' }} direction="vertical" size="middle">
                <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                  风险对象信息
                </Typography.Title>
                <div style={{ width: 656 }}>
                  <Row>
                    <Col span={12}>
                      <Form.Item
                        label="风险对象类型"
                        name="riskObjectType"
                        labelCol={{ span: 8 }}
                        wrapperCol={{ span: 16 }}
                        rules={[{ required: true, message: '请选择风险对象类型' }]}
                      >
                        <FaultTargetTypeSelect
                          style={{ width: '100%' }}
                          disabledOptionTypes={
                            location?.length === 1 ? [FaultTargetType.Device] : undefined
                          }
                          allowClear
                          onChange={() => {
                            form.setFieldsValue({ riskObjectName: undefined });
                          }}
                        />
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row>
                    <Col span={24}>
                      <Form.Item
                        label="风险对象名称"
                        name="riskObjectName"
                        labelCol={{ span: 4 }}
                        wrapperCol={{ span: 20 }}
                        required
                        rules={[
                          {
                            validator: (_, value) => {
                              if (riskObjectType !== 'OTHER' && !value?.length) {
                                return Promise.reject('风险对象名称必选！');
                              }
                              if (riskObjectType === 'OTHER' && value?.length > 20) {
                                return Promise.reject('最多仅允许输入20个字！');
                              }
                              if (riskObjectType === 'OTHER' && value.trim().length === 0) {
                                return Promise.reject('风险对象名称必填！');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        {riskObjectType === 'DEVICE' ? (
                          <DevicesTableSelectModal
                            idcTag={idc!}
                            blockTag={block!}
                            matchProductModel
                            zIndex={1015}
                            onChange={selectedRows => {
                              form.setFieldValue(
                                'riskObjectName',
                                selectedRows?.map(item => ({
                                  ...item,
                                  label: item.deviceName,
                                  value: item.deviceGuid,
                                  key: item.deviceGuid,
                                  roomTag: item.roomTag,
                                }))
                              );
                            }}
                          />
                        ) : (
                          <DeviceOrSpaceSelect
                            idcTag={idc!}
                            blockTag={block!}
                            disabled={!(location && riskObjectType)}
                            type={riskObjectType as 'DEVICE' | 'ROOM' | 'OTHER'}
                            multiple
                          />
                        )}
                      </Form.Item>
                    </Col>
                  </Row>
                </div>
              </Space>
            </Container>
          </Space>
        </Form>
      </Drawer>
    </>
  );
}
