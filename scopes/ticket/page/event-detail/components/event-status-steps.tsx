import dayjs from 'dayjs';
import React, { useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';
import { useLazyEventRelateApproval } from '@manyun/ticket.gql.client.tickets';
import {
  BackendEventUpgradeLevel,
  type EventStatus,
  EventStatusNumberValueMap,
} from '@manyun/ticket.model.event';
import { cancelEventAudit } from '@manyun/ticket.service.cancel-event-audit';
import { cancelEventRelieve } from '@manyun/ticket.service.cancel-event-relieve';
import { cancelEventResolve } from '@manyun/ticket.service.cancel-event-resolve';
import {
  getEventDetailAction,
  getEventLifeAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

import { useEventData } from '../components/event-data-context';
import { ReminderButton } from './reminder-button';

export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const stepsPrefixCls = `${prefixCls}-steps`;
    return css`
      // overrides steps's style
      .${stepsPrefixCls}-horizontal
        .${stepsPrefixCls}-item-content
        .${stepsPrefixCls}-item-description {
        max-width: none;
      }
    `;
  }}
`;
export type EventStatusStepsProps = {
  id: number;
};

export function EventStatusSteps({ id }: EventStatusStepsProps) {
  const eventCenterInfo = useSelector(selectEventDetail);
  const [{ tabsExpandAll }] = useEventData();
  const [, { checkUserId }] = useAuthorized();
  const [configUtil] = useConfigUtil();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featuresOnwerMultiple = features.onwerMultiple;

  const [getEventRelateApproval, { data: approvalData, refetch }] = useLazyEventRelateApproval();
  useEffect(() => {
    if (eventCenterInfo?.processNo && eventCenterInfo.processNo !== '0') {
      getEventRelateApproval({
        variables: { eventId: eventCenterInfo.id, processNo: eventCenterInfo.processNo },
      });
    }
  }, [eventCenterInfo.id, eventCenterInfo.processNo, getEventRelateApproval]);
  const dispatch = useDispatch();
  const { eventStatus } = eventCenterInfo;
  const {
    eventStatusCode,
    isEventAlreadyResponded,
    isEventAlreadyRelieved,
    isEventAlreadyResolved,
    isEventAuditing,
    isEventAlreadyAudited,
    isEventAlreadyClosed,
  } = getEventStatus(eventStatus);

  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const isReminderButtonAppeared = isOwner && eventCenterInfo.needUrge;
  const refresh = () => {
    dispatch(getEventDetailAction(id));
    dispatch(getEventLifeAction(id));
  };
  const onRelieveCancel = async () => {
    const { error } = await cancelEventRelieve({ eventId: id });

    if (error) {
      message.error(error.message);
      return;
    }
    refresh();
  };

  const onResolveCancel = async () => {
    const { error } = await cancelEventResolve({ eventId: id });
    if (error) {
      message.error(error.message);
      return;
    }
    refresh();
  };
  const onAuditCancel = async () => {
    const { error } = await cancelEventAudit({ eventId: id });
    if (error) {
      message.error(error.message);
      return;
    }
    refresh();
  };
  return (
    <Theme prefixCls="manyun">
      <Card bordered={false} style={{ width: '100%' }}>
        <StyledContainer>
          <Steps
            current={isEventAuditing ? EventStatusNumberValueMap.RESOLVED : eventStatusCode}
            items={[
              {
                title: '创建',
                description: (
                  <div style={{ fontSize: '12px' }}>
                    {eventCenterInfo.createTime &&
                      dayjs(eventCenterInfo.createTime).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ),
              },
              {
                title: isEventAlreadyResponded ? '已响应' : '待响应',
                description: (
                  <div style={{ fontSize: '12px' }}>
                    {eventCenterInfo.firFinishTime &&
                      dayjs(eventCenterInfo.firFinishTime).format('YYYY-MM-DD HH:mm:ss')}
                  </div>
                ),
              },
              {
                title: (
                  <div>
                    {isEventAlreadyRelieved ? (
                      <Typography.Text>
                        已缓解
                        {isOwner &&
                          !isEventAuditing &&
                          eventCenterInfo?.isFalseAlarm === 0 &&
                          eventStatusCode !== EventStatusNumberValueMap.CLOSED && (
                            <Button type="link" compact onClick={onRelieveCancel}>
                              (取消)
                            </Button>
                          )}
                      </Typography.Text>
                    ) : (
                      '待缓解'
                    )}
                  </div>
                ),
                description: (
                  <div>
                    {isReminderButtonAppeared &&
                      eventStatusCode === EventStatusNumberValueMap.PROCESSING && (
                        <ReminderButton
                          eventId={eventCenterInfo.id}
                          eventUpgradeLevel={BackendEventUpgradeLevel.Tier2}
                          onSuccess={refresh}
                        />
                      )}
                    {isEventAlreadyRelieved && (
                      <div style={{ fontSize: '12px' }}>
                        {eventCenterInfo.relieveTime
                          ? dayjs(eventCenterInfo.relieveTime).format('YYYY-MM-DD HH:mm:ss')
                          : '--'}
                      </div>
                    )}
                  </div>
                ),
              },
              {
                title: (
                  <div>
                    {isEventAlreadyResolved ? (
                      <Typography.Text>
                        已解决
                        {isOwner &&
                          !isEventAuditing &&
                          eventCenterInfo?.isFalseAlarm === 0 &&
                          eventStatusCode !== EventStatusNumberValueMap.CLOSED && (
                            <Button type="link" compact onClick={onResolveCancel}>
                              (取消)
                            </Button>
                          )}
                      </Typography.Text>
                    ) : (
                      '待解决'
                    )}
                  </div>
                ),
                description: (
                  <div>
                    {isReminderButtonAppeared &&
                      eventStatusCode === EventStatusNumberValueMap.RELIEVED && (
                        <ReminderButton
                          eventId={eventCenterInfo.id}
                          eventUpgradeLevel={BackendEventUpgradeLevel.Tier3}
                          onSuccess={refresh}
                        />
                      )}
                    <div style={{ fontSize: '12px' }}>
                      {eventCenterInfo.resolveTime &&
                        dayjs(eventCenterInfo.resolveTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                  </div>
                ),
              },
              {
                title: (
                  <div>
                    {isEventAlreadyAudited ? (
                      <div>
                        <Typography.Text>
                          已评审
                          {isOwner &&
                            eventCenterInfo?.isFalseAlarm === 0 &&
                            eventStatusCode !== EventStatusNumberValueMap.CLOSED && (
                              <Button type="link" compact onClick={onAuditCancel}>
                                (取消)
                              </Button>
                            )}
                        </Typography.Text>
                      </div>
                    ) : (
                      <Typography.Text>{isEventAuditing ? '评审中' : '待评审'}</Typography.Text>
                    )}
                  </div>
                ),
                description: (
                  <div>
                    <Space direction="vertical" size={0}>
                      {isEventAlreadyAudited && (
                        <div style={{ fontSize: '12px' }}>
                          {eventCenterInfo.auditTime &&
                            dayjs(eventCenterInfo.auditTime).format('YYYY-MM-DD HH:mm:ss')}
                        </div>
                      )}
                      {/* 当事件不走审批流时，详情接口返回的processNo字段是字符串0
                       */}
                      <Space>
                        {eventCenterInfo?.processNo &&
                          eventCenterInfo.processNo !== '0' &&
                          approvalData &&
                          eventCenterInfo.eventStatus.code && (
                            <Space>
                              <ApprovalRecordsDropdown
                                baseInfo={
                                  approvalData.eventRelateApproval as unknown as BpmInstance
                                }
                                businessOrderInfo={{
                                  type: 'EVENT_AUDIT_PROCESS',
                                  taskNumber: eventCenterInfo.eventNo!,
                                  status: eventCenterInfo.eventStatus.code,
                                  approvalPermissionType: 'EVENT',
                                }}
                              />
                              <AwaitOperationPeopleTag
                                bpmInstance={
                                  approvalData.eventRelateApproval as unknown as BpmInstance
                                }
                              />
                            </Space>
                          )}
                      </Space>
                    </Space>
                  </div>
                ),
              },
              {
                title: (
                  <Typography.Text>{isEventAlreadyClosed ? '已关闭' : '待关闭'}</Typography.Text>
                ),
                description: (
                  <div>
                    <div style={{ fontSize: '12px' }}>
                      {eventCenterInfo.closeTime &&
                        dayjs(eventCenterInfo.closeTime).format('YYYY-MM-DD HH:mm:ss')}
                    </div>
                  </div>
                ),
              },
            ]}
          />
        </StyledContainer>

        {eventCenterInfo?.isFalseAlarm === 0 &&
          !tabsExpandAll &&
          isEventAuditing &&
          approvalData?.eventRelateApproval && (
            <ApprovalOperationButtons
              baseInfo={approvalData.eventRelateApproval as unknown as BpmInstance}
              getDetail={() => {
                refetch();
                dispatch(getEventDetailAction(eventCenterInfo.eventNo!));
              }}
            />
          )}
      </Card>
    </Theme>
  );
}

export function getEventStatus(eventStatus: EventStatus): {
  eventStatusCode: number;
  isEventAlreadyResponded: boolean;
  isEventAlreadyRelieved: boolean;
  isEventAlreadyResolved: boolean;
  isEventAuditing: boolean;
  isEventAlreadyAudited: boolean;
  isEventAlreadyClosed: boolean;
} {
  const eventStatusCode = EventStatusNumberValueMap[eventStatus.code];

  return {
    eventStatusCode,
    isEventAlreadyResponded: eventStatusCode > EventStatusNumberValueMap.CREATED,
    isEventAlreadyRelieved: eventStatusCode > EventStatusNumberValueMap.PROCESSING,
    isEventAlreadyResolved: eventStatusCode > EventStatusNumberValueMap.RELIEVED,
    isEventAuditing: eventStatusCode === EventStatusNumberValueMap.AUDITING,
    isEventAlreadyAudited: eventStatusCode > EventStatusNumberValueMap.AUDITING,
    isEventAlreadyClosed: eventStatusCode > EventStatusNumberValueMap.AUDITED,
  };
}
