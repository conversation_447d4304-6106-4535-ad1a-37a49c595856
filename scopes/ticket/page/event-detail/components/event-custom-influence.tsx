import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { exportEventCustomInfluences } from '@manyun/ticket.service.export-event-custom-influences';
import { fetchEventInfluences } from '@manyun/ticket.service.fetch-event-influences';
import { EventCabinetInfluenceEditDrawerButton } from '@manyun/ticket.ui.event-cabinet-influence-edit-drawer-button';

import { EventCabinetInfluenceFilter } from './event-cabinet-influence-filter';
import { useEventData } from './event-data-context';

export type SearchParams = {
  location?: string;
  column?: string;
  grid?: string;
  customers?: string;
};
export type CustomInfluence = {
  influenceType: string;
  influenceGuid: string;
  consumerName: string[];
};
export type EventCustomInfluenceProps = {
  eventId: number;
  spaceGuid: string;
  /** 回调函数，在编辑操作完成后执行 */
  callback: () => void;
};
export function EventCustomInfluence({ eventId, spaceGuid, callback }: EventCustomInfluenceProps) {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const {
    events: { showRacksImpacts },
  } = configUtil.getScopeCommonConfigs('ticket');
  const [influences, setInfluences] = useState<CustomInfluence[]>([]);
  const [dataSource, setDataSource] = useState<CustomInfluence[]>([]);
  const [gridInfluence, setGridInfluence] = useState<string | undefined>('');
  const [exportLoading, setExportLoading] = useState(false);
  const [spinning, setSpinning] = useState(false);
  const [{ tabsExpandAll }] = useEventData();
  const [searchParams, setSearchParams] = useState<SearchParams>({});
  const getCustomInfluences = async () => {
    setSpinning(true);
    const { error, data } = await fetchEventInfluences({ eventId });
    setSpinning(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setInfluences(data!.influenceScope);
    setDataSource(data!.influenceScope);
    setGridInfluence(data?.influenceGrid);
    callback();
  };
  useEffect(() => {
    if (eventId) {
      (async function () {
        setSpinning(true);
        const { error, data } = await fetchEventInfluences({ eventId });
        setSpinning(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setInfluences(data!.influenceScope);
        setDataSource(data!.influenceScope);
        setGridInfluence(data?.influenceGrid);
      })();
    }
  }, [eventId]);
  const handleFileExport = useCallback(
    async (type: string) => {
      let params: {
        eventId: number;
        guid?: string;
        consumerName?: string;
        column?: string;
        grid?: string;
      } = {
        eventId: eventId,
      };
      if (type === 'filtered') {
        params = {
          ...params,
          guid: searchParams.location,
          grid: searchParams.grid,
          consumerName: searchParams.customers,
          column: searchParams.column,
        };
      }
      setExportLoading(true);

      const { error, data } = await exportEventCustomInfluences(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      return data;
    },
    [eventId, searchParams]
  );

  return (
    <Spin spinning={spinning}>
      <Space size="middle" direction="vertical" style={{ width: '100%' }}>
        <Row justify="space-between">
          <EventCabinetInfluenceFilter
            searchParams={searchParams}
            spaceGuid={spaceGuid}
            onChange={filterParams => {
              const newParams = { ...searchParams, ...filterParams };
              setSearchParams(newParams);
              const { location, column, grid, customers } = newParams;
              const afterfilteredInfluences = influences.filter(item => {
                const { cabinet } = getSpaceGuidMap(item.influenceGuid);
                if (location) {
                  if (!item.influenceGuid.includes(location)) {
                    return false;
                  }
                }
                if (column) {
                  if (
                    !(
                      (cabinet?.includes(column) && item.influenceType === 'COLUMN') ||
                      (item.influenceType === 'GRID' &&
                        typeof cabinet === 'string' &&
                        cabinet[0].includes(column))
                    )
                  ) {
                    return false;
                  }
                }
                if (grid) {
                  if (!(cabinet?.includes(grid) && item.influenceType === 'GRID')) {
                    return false;
                  }
                }
                if (customers) {
                  if (!item.consumerName?.join(',')?.includes(customers)) {
                    return false;
                  }
                }
                return true;
              });

              setDataSource(afterfilteredInfluences);
            }}
          />
          <Space>
            <EventCabinetInfluenceEditDrawerButton
              model="edit"
              spaceGuid={spaceGuid}
              eventId={eventId}
              initialValue={{ cabinetInfluence: gridInfluence, influenceScope: influences }}
              onSuccess={getCustomInfluences}
            />
            <FileExport
              key="export"
              text="导出"
              filename="自定义影响面.xls"
              showExportFiltered
              disabled={exportLoading || !influences.length}
              data={type => handleFileExport(type)}
            />
          </Space>
        </Row>
        {showRacksImpacts && (
          <Row>
            <Typography.Text>机柜影响：</Typography.Text>
            <Typography.Text>{gridInfluence ?? '--'}</Typography.Text>
          </Row>
        )}
        <Table<CustomInfluence>
          rowKey={record => record.influenceGuid}
          columns={[
            {
              dataIndex: 'idc',
              title: '机房',
              render: (_, { influenceGuid }) => {
                const { idc } = getSpaceGuidMap(influenceGuid);
                return idc ? <SpaceText guid={idc} /> : '--';
              },
            },
            {
              dataIndex: 'block',
              title: '楼栋',
              render: (_, { influenceGuid }) => getSpaceGuidMap(influenceGuid).block ?? '--',
            },
            {
              dataIndex: 'room',
              title: '包间',
              render: (_, { influenceGuid }) => getSpaceGuidMap(influenceGuid).room ?? '--',
            },
            {
              dataIndex: 'column',
              title: '机列',

              render: (_, { influenceType, influenceGuid }) => {
                const { cabinet } = getSpaceGuidMap(influenceGuid);
                return influenceType === 'GRID'
                  ? typeof cabinet === 'string'
                    ? cabinet[0]
                    : '--'
                  : cabinet ?? '--';
              },
            },
            {
              dataIndex: 'grid',
              title: '机柜',
              render: (_, { influenceType, influenceGuid }) => {
                if (influenceType === 'COLUMN') {
                  return '--';
                }
                return getSpaceGuidMap(influenceGuid).cabinet ?? '--';
              },
            },

            {
              dataIndex: 'consumerName',
              title: '供应商',
              render: (_, { consumerName }) =>
                Array.isArray(consumerName) ? consumerName.join(',') : '--',
            },
          ]}
          dataSource={dataSource}
          pagination={tabsExpandAll ? false : undefined}
        />
      </Space>
    </Spin>
  );
}
