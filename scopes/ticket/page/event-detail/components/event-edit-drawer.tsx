import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { BackendEventStatusCode } from '@manyun/ticket.model.event';
import { updateEvent } from '@manyun/ticket.service.update-event';
import {
  getEventDetailAction,
  getEventLifeAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';
import {
  EventMutator,
  formatCauseDevicesParams,
  generateEventCategory,
} from '@manyun/ticket.ui.event-mutator';
import type { FormValues } from '@manyun/ticket.ui.event-mutator';
import { filterEventInfluencesTreeData } from '@manyun/ticket.util.filter-event-influences-tree-data';

import { useEventData } from './event-data-context';

export type EventEditDrawerProps = {
  eventId: number;
};
export function EventEditDrawer({ eventId }: EventEditDrawerProps) {
  const [{ parentForm, drawerVisible }, { setDrawerVisible }] = useEventData();
  const [configUtil] = useConfigUtil();
  const {
    events: { features, showRacksImpacts },
  } = configUtil.getScopeCommonConfigs('ticket');
  // const featureSpecialInfoSort = features.specialInfoSort;

  const [loading, setLoading] = useState<boolean>(false);
  const eventCenterInfo = useSelector(selectEventDetail);

  const [form] = Form.useForm<FormValues>(parentForm);
  const dispatch = useDispatch();

  const onSave = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      // let influenceData: EventInfluenceDetail | null = null;
      // if (showRacksImpacts&&featureSpecialInfoSort) {
      //   const { error: fetchError, data } = await fetchEventInfluences({
      //     eventId: eventCenterInfo.id,
      //   });
      //   if (fetchError) {
      //     message.error(fetchError.message);
      //     return;
      //   }
      //   influenceData = data;
      // }

      const {
        location,
        isChangeAlarm,
        changeCode,
        incidentType,
        liablePerson,
        occurTime,
        detectTime,
        causeDevice,
        eventLevel,
        eventSource,
        eventOwner,
        files,
        eventInfluence,
        blockGuidList,
        ...rest
      } = formValue;
      const { idc, block } = getSpaceGuidMap(location ?? blockGuidList?.[0] ?? '');

      const svcQuery = {
        ...rest,
        files: files,
        blockGuidList,
        isChangeAlarm,
        eventOwnerName: eventOwner?.label,
        eventOwnerId: eventOwner?.value,
        eventSource: eventSource!.value,
        eventSourceName: eventSource!.label,
        eventLevel: eventLevel?.value,
        eventLevelName: eventLevel?.label,
        blockTag: location ? `${idc}.${block}` : undefined,
        idcTag: idc!,
        causeDevice: causeDevice ? formatCauseDevicesParams(causeDevice) : undefined,
        occurTime: occurTime ? occurTime.valueOf() : undefined,
        detectTime: detectTime ? detectTime.valueOf() : undefined,
        liablePersonId: liablePerson?.value,
        liablePersonName: liablePerson?.label,
        topCategory: generateEventCategory(incidentType).topCategory,
        topCategoryName: generateEventCategory(incidentType).topCategoryName,
        secondCategory: generateEventCategory(incidentType).secondCategory,
        secondCategoryName: generateEventCategory(incidentType).secondCategoryName,
        changeCode: isChangeAlarm ? changeCode : '',
        eventInfluence:
          eventInfluence && showRacksImpacts
            ? {
                gridInfluence: eventInfluence?.cabinetInfluence,
                influenceScope:
                  eventInfluence && Array.isArray(eventInfluence.influenceScope)
                    ? filterEventInfluencesTreeData(eventInfluence.influenceScope)?.map(item => ({
                        influenceType: item.type,
                        influenceGuid: item.key,
                      }))
                    : [],
              }
            : undefined,
      };
      const { error } = await updateEvent({ ...svcQuery, id: eventCenterInfo.id });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('编辑成功');
      setDrawerVisible(false);
      dispatch(getEventDetailAction(eventCenterInfo.id));
      dispatch(getEventLifeAction(eventCenterInfo.id));
    });
  };
  const showModal = () => {
    setDrawerVisible(true);
  };
  const closeModal = () => {
    // form.resetFields();
    setDrawerVisible(false);
  };

  return (
    <>
      <Button type="link" onClick={showModal}>
        编辑事件信息
      </Button>
      {drawerVisible && (
        <Drawer
          title="编辑事件信息"
          // size="large"
          width={1280}
          open={drawerVisible}
          forceRender
          destroyOnClose
          extra={
            <Space>
              <Button onClick={closeModal}>取消</Button>
              <Button loading={loading} type="primary" onClick={onSave}>
                保存
              </Button>
            </Space>
          }
          bodyStyle={{ padding: 0 }}
          onClose={() => {
            closeModal();
            form.resetFields();
          }}
        >
          <EventMutator
            externalForm={form}
            mode="edit"
            showFooter={false}
            unusedFormItems={features.onwerMultiple ? ['eventOwner'] : ['eventOwnerIdList']}
            eventResolutionRequired={
              eventCenterInfo.eventStatus.code === BackendEventStatusCode.Relieved ||
              eventCenterInfo.eventStatus.code === BackendEventStatusCode.Resolved
            }
            eventOccurrenceTimeRequired={
              eventCenterInfo.eventStatus.code === BackendEventStatusCode.Created ||
              eventCenterInfo.eventStatus.code === BackendEventStatusCode.Processing
            }
          />
        </Drawer>
      )}
    </>
  );
}
