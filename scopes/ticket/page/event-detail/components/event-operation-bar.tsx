import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import {
  BackendEventOperationStatusCode,
  BackendEventStatusCode,
} from '@manyun/ticket.model.event';
import { eventUpgradeFinish } from '@manyun/ticket.service.event-upgrade-finish';
import { respondEventUpgrade } from '@manyun/ticket.service.respond-event-upgrade';
import {
  getEventDetailAction,
  getEventLifeAction,
  getEventUpgradeRecordsAction,
  selectEventDetail,
} from '@manyun/ticket.state.event';

import { ButtonWithoutAudit } from './button-without-audit';
import { isEventNotAbleToAudit } from './checking-records';
import { useEventData } from './event-data-context';
import { EventPhaseRollbackModal } from './event-phase-rollback-modal';
import { EventRelieveModal } from './event-relieve-modal';
import { EventResolveModal } from './event-resolve-modal';
import { PhaseUpgradeModal } from './phase-upgrade-modal';

export type EventOperationBarProps = {
  id: number;
  reviewButtonVisible: boolean;
  onRelieveButtonClick: () => void;
};
export function EventOperationBar({
  id,
  reviewButtonVisible,
  onRelieveButtonClick,
}: EventOperationBarProps) {
  const dispatch = useDispatch();
  const [, { checkUserId }] = useAuthorized();
  const [{ parentForm }, { setDrawerVisible }] = useEventData();

  const eventCenterInfo = useSelector(selectEventDetail);

  const submitDisabled = isEventNotAbleToAudit(eventCenterInfo, false, false);
  const [loading, setLoading] = useState<boolean>(false);
  const { eventStatus } = eventCenterInfo;
  const status = eventStatus ? eventStatus.code : null;
  const isOwner = checkUserId(eventCenterInfo?.eventOwnerId);
  const isFirResponser = eventCenterInfo.firRespondUserId
    ? checkUserId(eventCenterInfo.firRespondUserId)
    : false;
  const isSecResponser = eventCenterInfo.secRespondUserId
    ? checkUserId(eventCenterInfo.secRespondUserId)
    : false;
  const isThiResponser = eventCenterInfo.thiRespondUserId
    ? checkUserId(eventCenterInfo.thiRespondUserId)
    : false;

  const isCallbackUser = eventCenterInfo?.callBackUserIds?.some((userId: number) =>
    checkUserId(userId)
  );

  const isResponser = [isCallbackUser, isFirResponser, isSecResponser, isThiResponser].includes(
    true
  );

  const operationStatus = eventCenterInfo?.optStatus;
  const respondEvent = async () => {
    setLoading(true);

    const { error } = await respondEventUpgrade({ eventId: id });
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    refresh();
  };

  const upgradeFish = async () => {
    setLoading(true);
    const { error } = await eventUpgradeFinish({ eventId: id });
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    refresh();
  };

  const refresh = () => {
    dispatch(getEventDetailAction(id));
    dispatch(getEventLifeAction(id));
    dispatch(getEventUpgradeRecordsAction({ pageNum: 1, pageSize: 10, eventId: id }));
  };

  const judgeFormOnClick = () => {
    if (parentForm) {
      parentForm
        .validateFields()
        .then(value => {
          upgradeFish();
        })
        .catch(err => {
          setDrawerVisible(true);
        });
    }
  };
  const generateOwnerButton = () => {
    switch (operationStatus) {
      case BackendEventOperationStatusCode.T2PhaseUpgrade:
        return <PhaseUpgradeModal id={id} />;

      case BackendEventOperationStatusCode.T3PhaseUpgrade:
        if (status === BackendEventStatusCode.Processing) {
          return (
            <Space>
              <EventRelieveModal id={id} />
              {isOwner && (
                <EventPhaseRollbackModal
                  id={id}
                  type={BackendEventOperationStatusCode.T3PhaseUpgrade}
                />
              )}
            </Space>
          );
        }
        return <PhaseUpgradeModal id={id} />;
      case BackendEventOperationStatusCode.Finished:
        if (status === BackendEventStatusCode.Relieved) {
          return (
            <Space>
              <EventResolveModal id={id} />
              {isOwner && (
                <EventPhaseRollbackModal id={id} type={BackendEventOperationStatusCode.Finished} />
              )}
            </Space>
          );
        }
        if (status !== BackendEventStatusCode.Closed) {
          return reviewButtonVisible ? (
            <Space>
              <Button type="primary" onClick={onRelieveButtonClick}>
                事件复盘
              </Button>
              {eventCenterInfo.enableAuditSkip && (
                <ButtonWithoutAudit
                  eventId={id}
                  eventNo={eventCenterInfo.eventNo!}
                  isInvalid={submitDisabled}
                  onSuccess={() => {
                    dispatch(getEventDetailAction(id));
                  }}
                />
              )}
            </Space>
          ) : null;
        }
        return null;

      default:
        return null;
    }
  };
  const generateResponserButton = () => {
    switch (operationStatus) {
      case BackendEventOperationStatusCode.T1WaitForConfirm:
      case BackendEventOperationStatusCode.T2WaitForConfirm:
      case BackendEventOperationStatusCode.T3WaitForConfirm:
        return isCallbackUser ? (
          <Button type="primary" loading={loading} onClick={respondEvent}>
            响应
          </Button>
        ) : null;

      case BackendEventOperationStatusCode.T1UpgradeFinish:
        return isFirResponser ? (
          <Button type="primary" loading={loading} onClick={judgeFormOnClick}>
            确认办结并通报
          </Button>
        ) : null;
      case BackendEventOperationStatusCode.T2UpgradeFinish:
        return isSecResponser ? (
          <Button type="primary" loading={loading} onClick={upgradeFish}>
            确认办结
          </Button>
        ) : null;
      case BackendEventOperationStatusCode.T3UpgradeFinish:
        return isThiResponser ? (
          <Button type="primary" loading={loading} onClick={upgradeFish}>
            确认办结
          </Button>
        ) : null;
      default:
        return null;
    }
  };

  const generateOwnerAndResponserButton = () => {
    switch (operationStatus) {
      case BackendEventOperationStatusCode.T1WaitForConfirm:
      case BackendEventOperationStatusCode.T2WaitForConfirm:
      case BackendEventOperationStatusCode.T3WaitForConfirm:
        return isCallbackUser ? (
          <Button type="primary" loading={loading} onClick={respondEvent}>
            响应
          </Button>
        ) : null;
      case BackendEventOperationStatusCode.T2PhaseUpgrade:
        return <PhaseUpgradeModal id={id} />;

      case BackendEventOperationStatusCode.T3PhaseUpgrade:
        return status === BackendEventStatusCode.Processing ? (
          <Space>
            <EventRelieveModal id={id} />
            {isOwner && (
              <EventPhaseRollbackModal
                id={id}
                type={BackendEventOperationStatusCode.T3PhaseUpgrade}
              />
            )}
          </Space>
        ) : (
          <PhaseUpgradeModal id={id} />
        );
      case BackendEventOperationStatusCode.Finished:
        return status === BackendEventStatusCode.Relieved ? (
          <Space>
            <EventResolveModal id={id} />
            {isOwner && (
              <EventPhaseRollbackModal id={id} type={BackendEventOperationStatusCode.Finished} />
            )}
          </Space>
        ) : status &&
          ![
            BackendEventStatusCode.Closed,
            BackendEventStatusCode.Auditing,
            BackendEventStatusCode.Audited,
          ].includes(status) &&
          reviewButtonVisible ? (
          <Space>
            <Button type="primary" onClick={onRelieveButtonClick}>
              事件复盘
            </Button>
            {eventCenterInfo.enableAuditSkip && (
              <ButtonWithoutAudit
                eventId={id}
                eventNo={eventCenterInfo.eventNo!}
                isInvalid={submitDisabled}
                onSuccess={() => {
                  dispatch(getEventDetailAction(id));
                }}
              />
            )}
          </Space>
        ) : null;
      case BackendEventOperationStatusCode.T1UpgradeFinish:
        return isFirResponser ? (
          <Button type="primary" loading={loading} onClick={judgeFormOnClick}>
            确认办结并通报
          </Button>
        ) : null;
      case BackendEventOperationStatusCode.T2UpgradeFinish:
        return isSecResponser ? (
          <Button type="primary" loading={loading} onClick={upgradeFish}>
            确认办结
          </Button>
        ) : null;
      case BackendEventOperationStatusCode.T3UpgradeFinish:
        return isThiResponser ? (
          <Button type="primary" loading={loading} onClick={upgradeFish}>
            确认办结
          </Button>
        ) : null;
      default:
        return null;
    }
  };

  const generateOperationBar = () => {
    const isOperator = isOwner || isResponser;
    const toolBarContent = () => {
      if (isOwner) {
        if (isResponser) {
          return generateOwnerAndResponserButton();
        }
        return generateOwnerButton();
      }

      return generateResponserButton();
    };

    return ![
      BackendEventStatusCode.Auditing,
      BackendEventStatusCode.Audited,
      BackendEventStatusCode.Closed,
    ].includes(eventCenterInfo.eventStatus.code) && isOperator ? (
      <FooterToolBar> {toolBarContent()}</FooterToolBar>
    ) : null;
  };
  return eventCenterInfo.isFalseAlarm === 0 ? generateOperationBar() : null;
}
