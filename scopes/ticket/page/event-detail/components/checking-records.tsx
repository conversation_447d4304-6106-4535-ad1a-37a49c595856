import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useDeepCompareEffect } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { useCheckEventCanAudit } from '@manyun/ticket.gql.client.tickets';
import {
  type EventJSON,
  EventSpecificProcessStatus,
  EventStatusValueMap,
} from '@manyun/ticket.model.event';
import { eventAudit } from '@manyun/ticket.service.event-audit';
import { getEventDetailAction, selectEventDetail } from '@manyun/ticket.state.event';

import { ButtonWithoutAudit } from './button-without-audit';
import { useEventData } from './event-data-context';

export type CheckingRecordsProps = {
  id: number;
};

const formItemLayout = {
  labelCol: { flex: ' 0 0 78px' },
};

export function CheckingRecords({ id }: CheckingRecordsProps) {
  const [configUtil] = useConfigUtil();
  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featureIsEventConfigWithProcessEngine = features.isEventConfigWithProcessEngine;
  const featureIsEventConfigWithProcessEngineRequired =
    featureIsEventConfigWithProcessEngine === 'required';

  const featuresOnwerMultiple = features.onwerMultiple;
  const featureSpecialInfoSort = features.specialInfoSort;
  const [, { setPdfTabList, setEventProcessEngineNodes }] = useEventData();

  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [checkEventCanAudit, { data }] = useCheckEventCanAudit();

  const dispatch = useDispatch();
  const [, { checkUserId }] = useAuthorized();
  const eventCenterInfo = useSelector(selectEventDetail);

  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const { eventStatus } = eventCenterInfo;
  const status = eventStatus ? Number(EventStatusValueMap[eventStatus.code]) : null;
  const submitDisabled = useMemo(() => {
    return isEventNotAbleToAudit(
      eventCenterInfo,
      featureIsEventConfigWithProcessEngineRequired,
      featuresOnwerMultiple
    );
  }, [eventCenterInfo, featureIsEventConfigWithProcessEngineRequired, featuresOnwerMultiple]);

  const onFinish = () => {
    form.validateFields().then(async formValue => {
      setLoading(true);
      const { error } = await eventAudit({
        eventId: id,
        files: formValue.files?.map((file: McUploadFile) => McUploadFile.toApiObject(file)),
        auditDesc: formValue.desc,
      });
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      dispatch(getEventDetailAction(eventCenterInfo.eventNo!));
      if (featureIsEventConfigWithProcessEngineRequired) {
        setTimeout(() => {
          setEventProcessEngineNodes();
        }, 2000);
      }
    });
  };
  const isEventInReviewStatus =
    (eventStatus.code as unknown as EventSpecificProcessStatus) ===
    EventSpecificProcessStatus.Debrief;
  const isEditable =
    (status === Number(EventStatusValueMap.RESOLVED) ||
      (featureIsEventConfigWithProcessEngineRequired && isEventInReviewStatus)) &&
    isOwner;
  useDeepCompareEffect(() => {
    setPdfTabList(pdfTabList => {
      return pdfTabList.map(item => {
        if (item.key === 'checkingRecords') {
          return {
            ...item,
            isValid:
              eventCenterInfo.auditDesc && Array.isArray(eventCenterInfo?.auditFileInfos)
                ? true
                : false,
            isRendered: true,
          };
        }
        return item;
      });
    });
  }, [eventCenterInfo.auditDesc, eventCenterInfo?.auditFileInfos, setPdfTabList]);
  useEffect(() => {
    checkEventCanAudit({
      variables: {
        query: {
          eventId: eventCenterInfo.eventNo!,
          eventVersion: 1,
        },
      },
    });
  }, [checkEventCanAudit, eventCenterInfo.eventNo]);

  return (
    <>
      {isEditable ? (
        <Form
          form={form}
          {...formItemLayout}
          initialValues={{
            desc: eventCenterInfo.auditDesc,
            files: Array.isArray(eventCenterInfo?.auditFileInfos)
              ? eventCenterInfo.auditFileInfos.map(file => McUploadFile.fromJSON(file))
              : [],
          }}
          onFinish={onFinish}
        >
          {data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED' ? (
            <Space style={{ marginBottom: 24 }}>
              <ExclamationCircleFilled style={{ color: `var(--${prefixCls}-warning-color)` }} />
              <Typography.Text>需关闭所有关联风险单后，才可提交复盘或评审</Typography.Text>
            </Space>
          ) : null}
          <Form.Item
            name="desc"
            label="复盘内容"
            rules={
              featureSpecialInfoSort
                ? [
                    {
                      max: 1000,
                      message: '最多输入 1000 个字符！',
                    },
                    {
                      required: true,
                      message: '复盘内容必填！',
                      whitespace: true,
                    },
                  ]
                : [
                    {
                      message: '不可仅输入空格！',
                      whitespace: true,
                    },
                  ]
            }
          >
            <Input.TextArea
              style={{ width: 800 }}
              disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}
              placeholder="请输入事件复盘总结"
              autoSize={{ minRows: 2, maxRows: 6 }}
            />
          </Form.Item>
          <Form.Item
            name="files"
            label={featureSpecialInfoSort ? '复盘附件' : '上传文档'}
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
            rules={[
              {
                required: !featureSpecialInfoSort,
                message: featureSpecialInfoSort ? '请上传复盘附件' : '请上传文档',
              },
            ]}
          >
            {isEditable ? (
              <Upload
                key="upload"
                maxFileSize={20}
                disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}
              >
                <Button disabled={data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'}>
                  上传
                </Button>
              </Upload>
            ) : (
              Array.isArray(eventCenterInfo.auditFileInfos) &&
              eventCenterInfo.auditFileInfos.length > 0 && (
                <SimpleFileList
                  files={eventCenterInfo.auditFileInfos.map(file => McUploadFile.fromJSON(file))}
                >
                  <Button type="link" compact>
                    查看
                  </Button>
                </SimpleFileList>
              )
            )}
          </Form.Item>
          {isEditable && (
            <Form.Item
              labelCol={{ flex: '0 0 78px' }}
              colon={false}
              label=" "
              extra={
                submitDisabled ? (
                  featureIsEventConfigWithProcessEngineRequired ? (
                    <Typography.Text type="danger">
                      请确保「事件信息」必填项和「根因定位」均已填写，再操作提交
                    </Typography.Text>
                  ) : (
                    <Typography.Text type="danger">
                      请填写「事件信息」中全部必填项后，再提交复盘
                    </Typography.Text>
                  )
                ) : null
              }
            >
              <Space>
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={loading}
                  disabled={
                    submitDisabled ||
                    data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
                  }
                >
                  提交复盘
                </Button>
                {eventCenterInfo.enableAuditSkip && (
                  <ButtonWithoutAudit
                    eventId={id}
                    isInvalid={submitDisabled}
                    isSxdtyg={featureIsEventConfigWithProcessEngineRequired}
                    eventNo={eventCenterInfo.eventNo!}
                    disabled={
                      submitDisabled ||
                      data?.checkEventCanAudit?.code === 'RELATE_RISK_EXIST_UNCLOSED'
                    }
                    onSuccess={() => {
                      dispatch(
                        getEventDetailAction(
                          featureIsEventConfigWithProcessEngineRequired
                            ? eventCenterInfo.eventNo!
                            : id
                        )
                      );
                      setTimeout(() => {
                        setEventProcessEngineNodes();
                      }, 2000);
                    }}
                  />
                )}
              </Space>
            </Form.Item>
          )}
        </Form>
      ) : (
        <Descriptions>
          <Descriptions.Item label="复盘内容" span={3}>
            {eventCenterInfo.auditDesc ?? '--'}
          </Descriptions.Item>
          <Descriptions.Item label={featureSpecialInfoSort ? '复盘附件' : '上传文档'} span={3}>
            {Array.isArray(eventCenterInfo.auditFileInfos) &&
            eventCenterInfo.auditFileInfos.length > 0 ? (
              <SimpleFileList
                files={eventCenterInfo.auditFileInfos.map(file => McUploadFile.fromJSON(file))}
              >
                <Button type="link" compact>
                  查看
                </Button>
              </SimpleFileList>
            ) : (
              '--'
            )}
          </Descriptions.Item>
        </Descriptions>
      )}
    </>
  );
}

export function isEventNotAbleToAudit(
  eventCenterInfo: EventJSON,
  featureIsEventConfigWithProcessEngineRequired: boolean,
  featuresOnwerMultiple: boolean
) {
  const {
    isChangeAlarm,
    changeCode,
    eventLevel,
    eventOwnerName,
    eventOwnerInfoList,
    occurTime,
    detectTime,
    topCategory,
    secondCategory,
    causeDevices,
    eventDesc,
    infoType,
    causeBy,
    causeDesc,
    blockGuidList,
    locationList,
    majorCode,
  } = eventCenterInfo;
  let submitDisabled = true;

  if (
    eventLevel &&
    (featuresOnwerMultiple ? eventOwnerInfoList?.length : eventOwnerName) &&
    occurTime &&
    detectTime &&
    topCategory &&
    secondCategory &&
    (blockGuidList?.length // 有blockGuidList说明是阳高
      ? locationList?.length && majorCode
      : causeDevices && causeDevices.length && infoType) &&
    eventDesc &&
    (featureIsEventConfigWithProcessEngineRequired ? true : causeBy) &&
    (featureIsEventConfigWithProcessEngineRequired ? true : causeDesc)
  ) {
    submitDisabled = false;
    if (isChangeAlarm === true && !changeCode) {
      submitDisabled = true;
    }
  }
  return submitDisabled;
}
