import { createContext, useContext } from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';

import type { DrillOrderApprovalNode } from '@manyun/ticket.gql.client.tickets/';
import type { ProcessEngineConfig } from '@manyun/ticket.model.event';

export type EventTab = {
  key: string;
  tab: string;
  isRendered: boolean;
  isValid: boolean;
};
export type Values = {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  parentForm: FormInstance<any> | undefined;
  drawerVisible: boolean;
  tabsExpandAll: boolean;
  pdfTabList: EventTab[];
  eventProcessEngineNodes: DrillOrderApprovalNode[];
  eventProcessEngineConfig: ProcessEngineConfig | undefined;
};

export type Handlers = {
  setDrawerVisible: (param: boolean) => void;
  setTabsExpandAll: (param: boolean) => void;
  setPdfTabList: React.Dispatch<React.SetStateAction<EventTab[]>>;
  setEventProcessEngineNodes: () => void;
};
const noop = () => {};
export const initialValue: [Values, Handlers] = [
  {
    parentForm: undefined,
    drawerVisible: false,
    tabsExpandAll: false,
    pdfTabList: [
      {
        key: 'eventProgress',
        tab: '事件进展',
        isValid: true,
        isRendered: true,
      },

      {
        key: 'influenceSuface',
        tab: '影响面',
        isValid: true,
        isRendered: false,
      },
      {
        key: 'relateTickets',
        tab: '关联事项',
        isValid: true,
        isRendered: false,
      },
      {
        key: 'monitoringData',
        tab: '监控数据',
        isValid: true,
        isRendered: false,
      },
      {
        key: 'sourceRecords',
        tab: '来源记录',
        isValid: true,
        isRendered: false,
      },
      {
        key: 'checkingRecords',
        tab: '复盘记录',
        isValid: true,
        isRendered: false,
      },
    ],
    eventProcessEngineNodes: [],
    eventProcessEngineConfig: undefined,
  },
  {
    setDrawerVisible: noop,
    setTabsExpandAll: noop,
    setPdfTabList: noop,
    setEventProcessEngineNodes: noop,
  },
];

export const EventDataContext = createContext<[Values, Handlers]>(initialValue);
export const useEventData = () => useContext(EventDataContext);
