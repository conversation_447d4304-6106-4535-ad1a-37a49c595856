import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';

import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { EventCustomInfluenceFilterModal } from './event-custom-influence-filter-modal';

export type FilterFormParams = {
  column?: string;
  grid?: string;
  customers?: string;
  location?: string;
};
export type EventCabinetInfluenceFilterProps = {
  searchParams: FilterFormParams;
  spaceGuid: string;
  onChange: (filterParams: FilterFormParams) => void;
};
export function EventCabinetInfluenceFilter({
  onChange,
  spaceGuid,
  searchParams,
}: EventCabinetInfluenceFilterProps) {
  const { idc, block } = getSpaceGuidMap(spaceGuid);
  return (
    <Space size="middle">
      <LocationTreeSelect
        nodeTypes={['IDC', 'BLOCK', 'ROOM']}
        showSearch
        idc={idc!}
        block={block!}
        allowClear
        authorizedOnly
        placeholder="搜索位置"
        includeVirtualBlocks
        style={{ width: 200 }}
        onChange={value => onChange({ ...searchParams, location: value as string })}
      />
      <EventCustomInfluenceFilterModal defaultValue={{}} onChange={onChange} />
    </Space>
  );
}
