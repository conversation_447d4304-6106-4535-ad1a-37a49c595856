import moment from 'moment';
import React from 'react';
import { useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { BackendEventTierLevel, EventStatusNumberValueMap } from '@manyun/ticket.model.event';
import { selectEventDetail, selectEventLife } from '@manyun/ticket.state.event';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { EditTierTimeModalButton } from './edit-tier-time-modal-button';
import { getEventStatus } from './event-status-steps';

export type StaffUpgradeRecord = {
  type: string | Element | JSX.Element;
  personnel: string;
  time: string | Element | JSX.Element;
  personnelId: string;
  sla?: string | Element | JSX.Element;
  content?: string;
};
const columns: ColumnType<StaffUpgradeRecord>[] = [
  {
    title: '阶段',
    width: 92,
    dataIndex: 'type',
    onCell: (_, rowIndex) => ({
      rowSpan: generateRowSpan(rowIndex || 0),
    }),
  },
  {
    title: '人员',
    width: 88,
    dataIndex: 'personnel',
    ellipsis: true,
    render: (_, { personnel, personnelId }) => (
      <UserLink userId={Number(personnelId)} userName={personnel} external />
    ),
  },
  {
    title: '时间',
    width: 280,
    dataIndex: 'time',
  },
  {
    title: '描述及方案',
    dataIndex: 'content',
    onCell: (_, rowIndex) => ({
      rowSpan: generateRowSpan(rowIndex || 0),
    }),
    render: (_, { content }, index: number) => (
      <Typography.Paragraph ellipsis={{ rows: 3, tooltip: true }}>{content}</Typography.Paragraph>
    ),
  },
  {
    title: 'SLA',
    width: 260,
    dataIndex: 'sla',
    className: 'sla',
    fixed: 'right' as 'right',
    onCell: (_, rowIndex) => ({
      rowSpan: generateRowSpan(rowIndex || 0),
    }),
  },
];
export function StaffUpgradeRecords() {
  const eventLife = useSelector(selectEventLife);
  const eventCenterInfo = useSelector(selectEventDetail);
  const [, { checkUserId }] = useAuthorized();

  const [configUtil] = useConfigUtil();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featuresOnwerMultiple = features.onwerMultiple;

  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const { eventStatus } = eventCenterInfo;
  const { eventStatusCode, isEventAlreadyRelieved } = getEventStatus(eventStatus);
  const isEditableModalVisible = eventStatusCode < EventStatusNumberValueMap.AUDITING && isOwner;

  const isEventRespondedInT1 = eventLife.firRespondTime && eventLife.firUpgradeTime;

  const isEventRespondedInT2 = eventLife.secRespondTime && eventLife.secUpgradeTime;
  const isEventRespondedInT3 = eventLife.thiRespondTime && eventLife.thiUpgradeTime;
  const t2Sla =
    eventLife.secOldestRespondTime && (eventLife.secFinishTime || eventLife.relieveSubmitTime)
      ? computeRespondSla(
          eventLife.secFinishTime ?? eventLife.relieveSubmitTime,
          eventLife.secOldestRespondTime
        )
      : null;
  const t3Sla =
    eventLife.thiOldestRespondTime && (eventLife.thiFinishTime || eventLife.resolveSubmitTime)
      ? computeRespondSla(
          eventLife.thiFinishTime ?? eventLife.resolveSubmitTime,
          eventLife.thiOldestRespondTime
        )
      : null;
  const t1RespondSla = isEventRespondedInT1
    ? computeRespondSla(eventLife.firRespondTime, eventLife.firUpgradeTime)
    : null;
  const t2RespondSla = isEventRespondedInT2
    ? computeRespondSla(eventLife.secRespondTime, eventLife.secUpgradeTime)
    : null;
  const t3RespondSla = isEventRespondedInT3
    ? computeRespondSla(eventLife.thiRespondTime, eventLife.thiUpgradeTime)
    : null;
  const momentFirRespondTime = moment(eventLife.firRespondTime);
  const momentFirUpgradeTime = moment(eventLife.firUpgradeTime);
  const momentOccurTime = moment(eventLife.occurTime);
  const momentSecUpgradeTime = moment(eventLife.secUpgradeTime);
  const momentSecRespondTime = moment(eventLife.secRespondTime);
  const momentThiUpgradeTime = moment(eventLife.thiUpgradeTime);
  const momentThiRespondTime = moment(eventLife.thiRespondTime);
  const momentResolveTime = moment(eventLife.resolveTime);
  const eventLifeList = [
    {
      key: 1,
      type: '事件发生',
      personnel: eventLife?.createUserName ?? '--',
      time: eventLife.occurTime ? momentOccurTime.format('YYYY-MM-DD HH:mm:ss') : '--',
      personnelId: eventLife.createUserId ?? '',
      content: eventCenterInfo.eventDesc ?? '--',
      sla: '--',
    },
    {
      key: 2,

      type: '事件定位',
      personnel: eventLife?.createUserName ?? '--',
      content: eventCenterInfo.causeDesc ?? '--',

      time: eventLife.detectTime
        ? moment(eventLife.detectTime).format('YYYY-MM-DD HH:mm:ss')
        : '--',
      personnelId: eventLife.createUserId || '',
      sla:
        typeof eventLife.ttd === 'number' ? (
          <Space size={0}>
            <Typography.Text>定位时效TTD：</Typography.Text>
            <TicketSlaText
              delay={convertMillisecondsToSeconds(eventLife.ttd)}
              unit="SECOND"
              effectTime={null}
              endTime={null}
              taskSla={0}
            />
          </Space>
        ) : (
          ''
        ),
    },
    {
      key: 3,

      type: (
        <Space direction="vertical" align="center">
          <Typography.Text>事件响应</Typography.Text>
          <Typography.Text>(Tier1)</Typography.Text>
        </Space>
      ),
      personnel: eventLife.firUpgradeUserName ?? '--',
      time: eventLife.firUpgradeTime ? (
        <Space>
          <Typography.Text>{`事件派发：${momentFirUpgradeTime.format(
            'YYYY-MM-DD HH:mm:ss'
          )}`}</Typography.Text>
          {isEditableModalVisible && (
            <EditTierTimeModalButton
              eventId={eventCenterInfo.id}
              tierLevel={BackendEventTierLevel.Tier1Upgrade}
              timeGroup={{
                currentRespondTime: momentFirRespondTime,
                currentUpgradeTime: momentFirUpgradeTime,
                prefixRespondTime: momentOccurTime,
              }}
            />
          )}
        </Space>
      ) : (
        '--'
      ),
      personnelId: eventLife.firUpgradeUserId || '',
      content: eventLife.firUpgradeContext ?? '--',

      sla:
        typeof eventLife.firTTE === 'number' ? (
          <Space direction="vertical">
            {eventLife.firRespondTime && eventLife.firUpgradeTime && (
              <Space size={0}>
                <Typography.Text>响应SLA：</Typography.Text>
                <TicketSlaText
                  delay={t1RespondSla}
                  taskSla={5}
                  unit="MINUTES"
                  effectTime={null}
                  endTime={null}
                  shouldLimitShow
                />
              </Space>
            )}

            <Space size={0}>
              <Typography.Text>响应时效TTE：</Typography.Text>
              <TicketSlaText
                delay={momentFirRespondTime.diff(momentOccurTime, 'second')}
                unit="SECOND"
                effectTime={null}
                endTime={null}
                taskSla={0}
              />
            </Space>
          </Space>
        ) : (
          '--'
        ),
    },
    {
      key: 4,

      type: '事件响应\n(Tier1)',
      personnel: eventLife.firRespondUserName ?? '--',
      time: eventLife.firRespondTime ? (
        <Space>
          <Typography.Text>{`Tier1响应：${momentFirRespondTime.format(
            'YYYY-MM-DD HH:mm:ss'
          )}`}</Typography.Text>
          {isEditableModalVisible && (
            <EditTierTimeModalButton
              eventId={eventCenterInfo.id}
              tierLevel={BackendEventTierLevel.Tier1Respond}
              timeGroup={{
                currentRespondTime: momentFirRespondTime,
                currentUpgradeTime: momentFirUpgradeTime,
                postUpdateTime: momentSecUpgradeTime,
              }}
            />
          )}
        </Space>
      ) : (
        '--'
      ),
      personnelId: eventLife.firRespondUserId || '',
    },
    {
      key: 5,

      type: (
        <Space direction="vertical" align="center">
          <Typography.Text>事件缓解</Typography.Text>
          <Typography.Text>(Tier2)</Typography.Text>
        </Space>
      ),
      personnel: eventLife.secUpgradeUserName ?? '--',
      time: eventLife.secUpgradeTime ? (
        <Space>
          <Typography.Text>{`${
            eventLife.secUpgradeInPhase ? 'Tier2升级' : '事件派发'
          }：${momentSecUpgradeTime.format('YYYY-MM-DD HH:mm:ss')}`}</Typography.Text>
          {isEditableModalVisible && (
            <EditTierTimeModalButton
              eventId={eventCenterInfo.id}
              tierLevel={BackendEventTierLevel.Tier2Upgrade}
              timeGroup={{
                currentRespondTime: momentSecRespondTime,
                currentUpgradeTime: momentSecUpgradeTime,
                prefixRespondTime: momentFirRespondTime,
              }}
            />
          )}
        </Space>
      ) : (
        '--'
      ),
      personnelId: eventLife.secUpgradeUserId || '',
      content: eventLife.relieveDesc ?? '--',

      sla:
        isEventRespondedInT2 || typeof eventLife.thiTTE === 'number' ? (
          <Space direction="vertical">
            <Space size={0}>
              <Typography.Text>响应SLA：</Typography.Text>

              <TicketSlaText
                delay={t2RespondSla}
                taskSla={0}
                unit="MINUTES"
                effectTime={null}
                endTime={null}
              />
            </Space>
            {isEventAlreadyRelieved && (
              <Space size={0}>
                <Typography.Text>处理SLA：</Typography.Text>
                <TicketSlaText
                  delay={t2Sla}
                  taskSla={24}
                  unit="HOUR"
                  effectTime={null}
                  endTime={null}
                  shouldLimitShow
                />
              </Space>
            )}

            {isEventAlreadyRelieved && (
              <Space size={0}>
                <Typography.Text>缓解时效TTM：</Typography.Text>
                <TicketSlaText
                  delay={eventLife.ttm ? convertMillisecondsToSeconds(eventLife.ttm) : null}
                  unit="SECOND"
                  effectTime={null}
                  endTime={null}
                  taskSla={0}
                />
              </Space>
            )}
          </Space>
        ) : (
          '--'
        ),
    },
    {
      key: 6,

      type: 'Tier2响应',
      personnel: eventLife.secRespondUserName ?? '--',
      time: eventLife.secRespondTime ? (
        <Space>
          <Typography.Text>{`Tier2响应：${momentSecRespondTime.format(
            'YYYY-MM-DD HH:mm:ss'
          )}`}</Typography.Text>
          {isEditableModalVisible && (
            <EditTierTimeModalButton
              eventId={eventCenterInfo.id}
              tierLevel={BackendEventTierLevel.Tier2Respond}
              timeGroup={{
                currentRespondTime: momentSecRespondTime,
                currentUpgradeTime: momentSecUpgradeTime,
                prefixRespondTime: momentFirRespondTime,
                postUpdateTime: momentThiUpgradeTime,
              }}
            />
          )}
        </Space>
      ) : (
        '--'
      ),
      personnelId: eventLife.secRespondUserId || '',
    },
    {
      key: 7,

      type: (
        <Space direction="vertical" align="center">
          <Typography.Text>事件解决</Typography.Text>
          <Typography.Text>(Tier3)</Typography.Text>
        </Space>
      ),
      personnel: eventLife.thiUpgradeUserName ?? '--',
      time: eventLife.thiUpgradeTime ? (
        <Space>
          <Typography.Text>{`${
            eventLife.thiUpgradeInPhase ? 'Tier3升级' : '事件派发'
          }：${momentThiUpgradeTime.format('YYYY-MM-DD HH:mm:ss')}`}</Typography.Text>
          {isEditableModalVisible && (
            <EditTierTimeModalButton
              eventId={eventCenterInfo.id}
              tierLevel={BackendEventTierLevel.Tier3Upgrade}
              timeGroup={{
                currentRespondTime: momentThiRespondTime,
                currentUpgradeTime: momentThiUpgradeTime,
                prefixRespondTime: momentSecRespondTime,
              }}
            />
          )}
        </Space>
      ) : (
        '--'
      ),
      personnelId: eventLife.thiUpgradeUserId || '',
      content: eventLife.resolveDesc ?? '--',

      sla:
        typeof eventLife.thiTTE === 'number' ? (
          <Space direction="vertical">
            {eventLife.thiRespondTime && eventLife.thiUpgradeTime && (
              <Space size={0}>
                <Typography.Text>响应SLA：</Typography.Text>
                <TicketSlaText
                  delay={t3RespondSla}
                  taskSla={0}
                  unit="MINUTES"
                  effectTime={null}
                  endTime={null}
                />
              </Space>
            )}
            {eventLife.thiRespondTime && eventLife.resolveTime && (
              <Space size={0}>
                <Typography.Text>处理SLA：</Typography.Text>
                <TicketSlaText
                  delay={t3Sla}
                  taskSla={24}
                  unit="HOUR"
                  effectTime={null}
                  endTime={null}
                  shouldLimitShow
                />
              </Space>
            )}

            {eventLife.ttr && (
              <Space size={0}>
                <Typography.Text>解决时效TTR：</Typography.Text>
                <TicketSlaText
                  delay={convertMillisecondsToSeconds(eventLife.ttr)}
                  unit="SECOND"
                  effectTime={null}
                  endTime={null}
                  taskSla={0}
                />
              </Space>
            )}
          </Space>
        ) : (
          '--'
        ),
    },
    {
      key: 8,

      type: 'Tier3响应',
      personnel: eventLife.thiRespondUserName ?? '--',
      time: eventLife.thiRespondTime ? (
        <Space>
          <Typography.Text>{`Tier3响应：${momentThiRespondTime.format(
            'YYYY-MM-DD HH:mm:ss'
          )}`}</Typography.Text>
          {isEditableModalVisible && (
            <EditTierTimeModalButton
              eventId={eventCenterInfo.id}
              tierLevel={BackendEventTierLevel.Tier3Respond}
              timeGroup={{
                currentRespondTime: momentThiRespondTime,
                currentUpgradeTime: momentThiUpgradeTime,
                prefixRespondTime: momentSecRespondTime,
                postUpdateTime: momentResolveTime,
              }}
            />
          )}
        </Space>
      ) : (
        '--'
      ),
      personnelId: eventLife.thiRespondUserId || '',
    },
  ];
  return (
    <Table<StaffUpgradeRecord>
      // scroll={{ x: 'max-content' }}
      dataSource={eventLifeList}
      columns={columns}
      pagination={false}
    />
  );
}

function generateRowSpan(index: number) {
  if (index < 2) {
    return 1;
  }
  return index % 2 === 0 ? 2 : 0;
}

function computeRespondSla(respondTime: number, upgradeTime: number) {
  return moment(respondTime).diff(moment(upgradeTime), 'second');
}
function convertMillisecondsToSeconds(milliseconds: number): number {
  return milliseconds / 1000;
}
