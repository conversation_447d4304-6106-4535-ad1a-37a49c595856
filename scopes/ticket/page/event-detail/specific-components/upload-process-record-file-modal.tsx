/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-22
 *
 * @packageDocumentation
 */
import { InboxOutlined } from '@ant-design/icons';
import React, { useState } from 'react';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { uploadEventProcessingRecordFile } from '@manyun/ticket.service.upload-event-processing-record-file';

export type UploadProcessRecordFileModalProps = {
  recordId: number;
  fileInfoList?: BackendMcUploadFile[] | null;
  onSuccess: () => void;
};

export function UploadProcessRecordFileModal({
  recordId,
  fileInfoList,
  onSuccess,
}: UploadProcessRecordFileModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);

  const [files, setFiles] = useState<McUploadFileJSON[]>(
    (fileInfoList ?? []).map(file => McUploadFile.fromApiObject(file).toJSON())
  );

  const closeModal = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          setFiles(
            (fileInfoList ?? []).map(file => ({
              ...McUploadFile.fromApiObject(file).toJSON(),
              status: 'done',
            }))
          );
        }}
      >
        上传
      </Button>
      <Modal
        destroyOnClose
        title="上传附件"
        open={open}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              closeModal();
              setFiles([]);
            }}
          >
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            disabled={
              (!files.length && (!Array.isArray(fileInfoList) || !fileInfoList.length)) ||
              files.some(file => file.status !== 'done')
            }
            loading={loading}
            onClick={async () => {
              setLoading(true);
              const { error } = await uploadEventProcessingRecordFile({
                recordId,
                fileInfoList: files.map(file => McUploadFile.fromJSON(file).toApiObject()),
              });
              setLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
              message.success('上传成功');
              closeModal();
              onSuccess();
            }}
          >
            提交
          </Button>,
        ]}
        onCancel={() => {
          closeModal();
          setFiles([]);
        }}
      >
        <McUpload
          type="drag"
          fileList={files}
          accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
          multiple
          maxFileSize={20}
          onChange={info => {
            /**上传第六个时提示文案 */
            if (info.fileList.length > 5) {
              message.error('附件最多上传5个');
              return;
            }
            setFiles(info.fileList);
          }}
        >
          <Space direction="vertical">
            <p>
              <InboxOutlined style={{ fontSize: 48, color: `var(--${prefixCls}-primary-color)` }} />
            </p>
            <Typography.Text>点击或将文件拖拽到这里上传</Typography.Text>
            <Typography.Text type="secondary">
              支持扩展名：.zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf
            </Typography.Text>
          </Space>
        </McUpload>
      </Modal>
    </>
  );
}
