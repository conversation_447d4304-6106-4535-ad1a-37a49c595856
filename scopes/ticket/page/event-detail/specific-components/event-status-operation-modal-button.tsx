import type { Moment } from 'moment';
import moment from 'moment';
import React, { useEffect, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { useFinishEventCurrentPhase } from '@manyun/ticket.gql.client.tickets';
import { EventSpecificProcessStatus } from '@manyun/ticket.model.event';

type FormValue = {
  handlePeople: { value: number };
  beginTime: Moment;
  finishTime: Moment;
  resultDescription: string;
};

type FormInitialValue = {
  handlePeople?: { value: number };
  beginTime?: Moment;
  finishTime?: Moment;
  resultDescription?: string;
};
export type EventStatusOperationModalButtonProps = {
  eventId: number;
  status: EventSpecificProcessStatus;
  blockGuid?: string[];
  /** 区分 编辑/新建 */
  mode: 'edit' | 'new';
  initialValue?: FormInitialValue;
  onSuccess?: () => void;
} & ButtonProps;

export function EventStatusOperationModalButton({
  eventId,
  status,
  mode,
  blockGuid,
  initialValue,
  onSuccess,
  ...props
}: EventStatusOperationModalButtonProps) {
  const [finishEventCurrentPhase, { loading }] = useFinishEventCurrentPhase({
    onCompleted(data) {
      if (data.finishEventCurrentPhase?.success) {
        message.success('操作成功');
        closeModal();
        onSuccess && onSuccess();
      } else {
        message.error(data.finishEventCurrentPhase?.message);
      }
    },
  });
  const [form] = Form.useForm<FormValue>();
  const [open, setOpen] = useState(false);

  const isEditMode = mode === 'edit';
  const showModal = () => {
    setOpen(true);
  };
  const closeModal = () => {
    setOpen(false);
  };
  useEffect(() => {
    if (open && initialValue) {
      form.setFieldsValue({ ...initialValue });
    }
  }, [open, isEditMode, initialValue, form]);
  function getStatusInfo(status: EventSpecificProcessStatus) {
    switch (status) {
      case EventSpecificProcessStatus.Emergency:
        return {
          title: '事件应急',
          isBeginTimeDisabled: true,
        };
      case EventSpecificProcessStatus.Fix:
        return {
          title: '故障修复',
          isBeginTimeDisabled: false,
        };
      case EventSpecificProcessStatus.Recovery:
        return {
          title: '系统恢复',
          isBeginTimeDisabled: false,
        };
      default:
        return {
          title: '--',
          isBeginTimeDisabled: false,
        };
    }
  }
  return (
    <>
      <Button
        type="link"
        compact
        onClick={e => {
          if (props.onClick) {
            props.onClick(e);
          } else {
            showModal();
          }
        }}
      >
        {isEditMode ? '编辑' : '完成'}
      </Button>
      <Modal
        destroyOnClose
        title={getStatusInfo(status).title}
        open={open}
        okButtonProps={{ loading }}
        okText="提交"
        onCancel={() => {
          closeModal();
        }}
        onOk={() => {
          form.validateFields().then(values => {
            const { handlePeople, resultDescription, finishTime, beginTime } = values;
            finishEventCurrentPhase({
              variables: {
                handleContent: resultDescription,
                handlerId: handlePeople.value,
                endTime: finishTime.valueOf(),
                startTime: beginTime.valueOf(),
                eventId,
                eventPhase: status,
              },
            });
          });
        }}
      >
        <Form form={form} preserve={false} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="处理人"
            name="handlePeople"
            rules={[{ required: true, message: '处理人必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              resourceParams={[
                {
                  resourceType: 'BUILDING',
                  resourceCodes: blockGuid ?? [],
                },
              ]}
              userState="in-service"
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="开始时间"
            name="beginTime"
            rules={[{ required: true, message: '开始时间必选' }]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={handleEndDisabledDate}
              disabledTime={disabledDateTimeAfterToday}
              disabled={getStatusInfo(status).isBeginTimeDisabled}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            label="完成时间"
            name="finishTime"
            dependencies={['beginTime']}
            rules={[
              { required: true, message: '完成时间必选' },
              ({ getFieldValue }) => ({
                validator(_, value) {
                  if (value) {
                    if (moment(value).diff(moment(getFieldValue('beginTime')), 'seconds') <= 0) {
                      return Promise.reject(new Error('阶段完成时间不可早于阶段开始时间'));
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              disabledDate={handleEndDisabledDate}
              disabledTime={disabledDateTimeAfterToday}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
          <Form.Item
            label="处理结果"
            name="resultDescription"
            rules={[{ required: true, message: '处理结果必填' }]}
          >
            <Input.TextArea style={{ width: 428 }} maxLength={500} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

function handleEndDisabledDate(current: Moment) {
  return current.valueOf() > moment().endOf('day').valueOf();
}
function range(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}
export function disabledDateTimeAfterToday(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (current) {
    const choseHour = current.hours();
    const choseMinute = current.minute();
    const isToday = current.isSame(compareMoment, 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => (choseMinute === minute ? range(second, 60) : []),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}
