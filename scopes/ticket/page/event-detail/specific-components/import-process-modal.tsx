import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';
import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';
import { saveAs } from 'file-saver';
import React, { useMemo, useState } from 'react';

import { Button, type ButtonProps } from '@manyun/base-ui.ui.button';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { Upload } from '@manyun/dc-brain.ui.upload';
import { getRiskRegisterLocales } from '@manyun/ticket.model.risk-register';
import { downloadEventProcessingRecordTemplate } from '@manyun/ticket.service.download-event-processing-record-template';
import { exportEventProcessingRecord } from '@manyun/ticket.service.export-event-processing-record';
import type {
  ApiResponseData,
  ExcelCheckErrDtosData,
} from '@manyun/ticket.service.export-event-processing-record';

export type ImportProcessModalProps = {
  eventId: number;
  onSuccess: () => void;
} & Pick<ButtonProps, 'disabled'>;

export function ImportProcess({ eventId, onSuccess }: ImportProcessModalProps) {
  const [visible, setVisible] = useState(false);
  const [riskRegistersImport, setRiskRegistersImport] = useState<ApiResponseData>();

  const [exportLoading, setExportLoading] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const locales = useMemo(() => getRiskRegisterLocales(), []);

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await downloadEventProcessingRecordTemplate();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, `事件处理记录.xlsx`);
  };

  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
      >
        导入处理记录
      </Button>

      <Modal
        width={1024}
        open={visible}
        title="导入处理记录"
        destroyOnClose
        footer={null}
        onCancel={() => {
          setVisible(false);
          setRiskRegistersImport(undefined);
        }}
      >
        <Row justify="space-between">
          <Space>
            <Upload
              showUploadList={false}
              accept=".csv,.xls,.xlsx"
              maxCount={1}
              customRequest={async ({ file }) => {
                setImportLoading(true);
                const fd = new FormData();
                fd.append('file', file);
                fd.append('id', eventId);
                const { data, error } = await exportEventProcessingRecord(fd);
                setImportLoading(false);
                setRiskRegistersImport(data);
                if (error) {
                  message.error(error.message);
                  return;
                }
                if ((data.faultTotal ?? 0) === 0) {
                  message.success('导入成功');
                  setVisible(false);
                  setRiskRegistersImport(undefined);
                  onSuccess();
                }
              }}
            >
              <Button loading={importLoading} type="primary">
                {locales.importRisk.importBtnText}
              </Button>
            </Upload>
            <Button type="link" loading={exportLoading} compact onClick={handleFileExport}>
              <DownloadOutlined />
              {locales.importRisk.downloadTemplate}
            </Button>
          </Space>
          {riskRegistersImport?.excelCheckDtos && (
            <Typography.Text style={{ marginLeft: 'auto' }}>
              导入失败，{riskRegistersImport?.excelCheckDtos?.length}条不符合填写规范
            </Typography.Text>
          )}
        </Row>

        <Table
          size="middle"
          scroll={{ x: 'max-content' }}
          loading={importLoading}
          style={{ marginTop: 24 }}
          dataSource={riskRegistersImport ? riskRegistersImport.excelCheckDtos : []}
          columns={[
            {
              title: '阶段',
              dataIndex: ['data', 'eventPhase'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data?.eventPhase!, errMessage, 'eventPhase'),
            },
            {
              title: '处理时间',
              dataIndex: ['data', 'handleTime'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data?.handleTime!, errMessage, 'handleTime'),
            },
            {
              title: '处理人',
              dataIndex: ['data', 'handler'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data?.handler!, errMessage, 'handler'),
            },

            {
              title: '处理内容',
              dataIndex: ['data', 'handleContent'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data?.handleContent!, errMessage, 'handleContent'),
            },

            {
              title: '备件说明',
              dataIndex: ['data', 'comment'],
              render: (_, { data, errMessage }) =>
                getToolTilp(data?.comment!, errMessage, 'comment'),
            },
          ]}
        />
      </Modal>
    </>
  );
}

type RiskRegisterImportColumnKey = keyof ExcelCheckErrDtosData;

function getToolTilp(
  value: string,
  errMessage: Record<string, string>,
  dataType: RiskRegisterImportColumnKey
) {
  if (Object.keys(errMessage).includes(dataType)) {
    return (
      <Space key={dataType}>
        <Typography.Text ellipsis type="danger">
          {value ?? '--'}
        </Typography.Text>
        <Tooltip title={errMessage[dataType]}>
          <QuestionCircleOutlined />
        </Tooltip>
      </Space>
    );
  }
  return <span key={dataType}> {value ?? '--'}</span>;
}
