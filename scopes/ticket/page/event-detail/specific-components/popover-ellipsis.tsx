import type { ReactNode } from 'react';
import React from 'react';

import { prefixCls } from '@manyun/base-ui.style.style';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

export type PopoverEllipsisProps = {
  targets: ReactNode[];
  width?: number;
};
export const PopoverEllipsis = ({ targets, width }: PopoverEllipsisProps) => {
  function getTargetContent(isInPopover?: boolean) {
    return targets.map((item, index) => (
      <span key={item?.toString()}>
        {item}
        {!isInPopover && index !== targets.length - 1 && <Divider type="vertical" />}
      </span>
    ));
  }
  return (
    <Popover
      content={
        <Space style={{ maxWidth: 400 }} direction="vertical">
          {targets.map(item => item)}
        </Space>
      }
    >
      <Typography.Text
        ellipsis
        style={{
          width,
          color: `var(--${prefixCls}-primary-color)`,
        }}
      >
        {getTargetContent()}
      </Typography.Text>
    </Popover>
  );
};
