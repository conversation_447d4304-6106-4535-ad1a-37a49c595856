import dayjs from 'dayjs';
import React, { useEffect, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Container } from '@manyun/base-ui.ui.container';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BpmInstance } from '@manyun/bpm.model.bpm-instance';
import { ApprovalOperationButtons } from '@manyun/bpm.ui.approval-operation-buttons';
import { ApprovalRecordsDropdown } from '@manyun/bpm.ui.approval-records-dropdown';
import { AwaitOperationPeopleTag } from '@manyun/bpm.ui.bpm-instance-viewer';
import styled, { Theme, css } from '@manyun/dc-brain.theme.theme';
import { useLazyEventRelateApproval } from '@manyun/ticket.gql.client.tickets';
import {
  EventSpecificProcessPhaseStatus,
  EventSpecificProcessStatus,
} from '@manyun/ticket.model.event';
import {
  getEventDetailAction,
  selectEventDetail,
  selectEventLife,
} from '@manyun/ticket.state.event';

import { useEventData } from '../components/event-data-context';

export const StyledContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    const stepsPrefixCls = `${prefixCls}-steps`;
    return css`
      // overrides steps's style
      .${stepsPrefixCls}-horizontal
        .${stepsPrefixCls}-item-content
        .${stepsPrefixCls}-item-description {
        max-width: none;
      }
    `;
  }}
`;
export type EventSpecificStatusStepsProps = {
  id: string;
};

export function EventSpecificStatusSteps({ id }: EventSpecificStatusStepsProps) {
  const dispatch = useDispatch();

  const eventCenterInfo = useSelector(selectEventDetail);
  const [{ eventProcessEngineNodes, tabsExpandAll }, { setEventProcessEngineNodes }] =
    useEventData();
  const eventLife = useSelector(selectEventLife);
  const [getEventRelateApproval, { data: approvalData, refetch }] = useLazyEventRelateApproval();
  useEffect(() => {
    if (eventCenterInfo?.processNo && eventCenterInfo.processNo !== '0') {
      getEventRelateApproval({
        variables: { eventId: eventCenterInfo.id, processNo: eventCenterInfo.processNo },
      });
    }
  }, [eventCenterInfo.id, eventCenterInfo.processNo, getEventRelateApproval]);

  useEffect(() => {
    if (id) {
      (function () {
        setEventProcessEngineNodes();
      })();
    }
  }, [id, setEventProcessEngineNodes]);
  const stepItems = useDeepCompareMemo(() => {
    const basicStepItems = [
      {
        title: '创建',
        code: EventSpecificProcessStatus.Init,
        description: (
          <Typography.Text type="secondary">
            {dayjs(eventCenterInfo.createTime).format('YYYY-MM-DD HH:mm')}
          </Typography.Text>
        ),
      },
      {
        title: '事件应急',
        code: EventSpecificProcessStatus.Emergency,
        description: undefined,
      },
      {
        title: '故障修复',
        code: EventSpecificProcessStatus.Fix,
        description: undefined,
      },
      {
        title: '系统恢复',
        code: EventSpecificProcessStatus.Recovery,
        description: undefined,
      },
      {
        title: '事件结单',
        code: EventSpecificProcessStatus.Finished,
        description: undefined,
      },
      {
        title: '复盘',
        code: EventSpecificProcessStatus.Debrief,
        description: undefined,
      },
      {
        title: '评审',
        code: EventSpecificProcessStatus.Review,
        description: (
          <>
            {approvalData?.eventRelateApproval && eventCenterInfo.eventStatus.code && (
              <Space>
                <ApprovalRecordsDropdown
                  baseInfo={approvalData.eventRelateApproval as unknown as BpmInstance}
                  businessOrderInfo={{
                    type: 'EVENT_AUDIT_PROCESS',
                    taskNumber: eventCenterInfo.eventNo!,
                    status: eventCenterInfo.eventStatus.code,
                    approvalPermissionType: 'EVENT',
                  }}
                />
                <AwaitOperationPeopleTag
                  bpmInstance={approvalData.eventRelateApproval as unknown as BpmInstance}
                />
              </Space>
            )}
          </>
        ),
      },
      {
        title: '关闭',
        code: EventSpecificProcessStatus.Close,
        description: undefined,
      },
    ];
    const isApprovalRevoked =
      eventProcessEngineNodes?.findIndex(item => item.status === 'EXECUTE') !==
      (eventProcessEngineNodes?.length ?? 0) - 1;

    const approvalEngineNodes = eventProcessEngineNodes.map(item => {
      const renderItem = basicStepItems.find(step => step.code === item.code)!;
      const targetPhase = eventLife?.eventProgress?.find(
        progress => progress.eventPhase === item.code
      );
      if (renderItem.code === EventSpecificProcessStatus.Review) {
        return {
          ...renderItem,
          description:
            (item?.createTime && item.status === 'ALREADY_EXECUTE' && !isApprovalRevoked) ||
            (eventCenterInfo.eventStatus.code as unknown as EventSpecificProcessStatus) ===
              EventSpecificProcessStatus.Close ? (
              <Space direction="vertical">
                {renderItem.description}
                <Typography.Text type="secondary">
                  {dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}
                </Typography.Text>
              </Space>
            ) : (
              renderItem.description
            ),
        };
      } else {
        return {
          ...renderItem,
          status:
            renderItem.code === EventSpecificProcessStatus.Close &&
            item.status === 'ALREADY_EXECUTE'
              ? ('finish' as 'finish')
              : undefined,
          description:
            item.createTime && item.status === 'ALREADY_EXECUTE' ? (
              <div
                style={{
                  width: '100%',
                  flexWrap: 'wrap',
                  display: 'flex',
                  paddingRight: 16,
                  flexDirection: 'column',
                }}
              >
                <Typography.Text type="secondary">
                  {dayjs(item.createTime).format('YYYY-MM-DD HH:mm')}
                </Typography.Text>
                {targetPhase?.status === EventSpecificProcessPhaseStatus.Skipped && (
                  <Tag style={{ width: 'fit-content' }} color="default">
                    已跳过
                  </Tag>
                )}
              </div>
            ) : (
              renderItem.description
            ),
        };
      }
    });
    return [
      {
        title: '创建',
        code: EventSpecificProcessStatus.Init,
        description: (
          <Typography.Text type="secondary">
            {dayjs(eventCenterInfo.createTime).format('YYYY-MM-DD HH:mm')}
          </Typography.Text>
        ),
      },
      ...approvalEngineNodes,
    ];
  }, [
    approvalData?.eventRelateApproval,
    eventProcessEngineNodes,
    eventLife.eventProgress,
    eventCenterInfo.eventStatus.code,
  ]);
  const stepCurrent = useMemo(() => {
    const currentIndex = eventProcessEngineNodes?.findIndex(item => item.status === 'EXECUTE');
    if (
      (eventCenterInfo.eventStatus.code as unknown as EventSpecificProcessStatus) !==
      EventSpecificProcessStatus.Close
    ) {
      return (currentIndex ?? 0) + 1;
    } else {
      return 10;
    }
  }, [eventProcessEngineNodes, eventCenterInfo.eventStatus.code]);
  return (
    <Theme prefixCls="manyun">
      <Container>
        <StyledContainer>
          <Steps current={stepCurrent} items={stepItems} />
        </StyledContainer>
        {approvalData?.eventRelateApproval &&
          !tabsExpandAll &&
          (eventCenterInfo.eventStatus.code as unknown as EventSpecificProcessStatus) !==
            EventSpecificProcessStatus.Close && (
            <ApprovalOperationButtons
              baseInfo={approvalData.eventRelateApproval as unknown as BpmInstance}
              getDetail={() => {
                refetch();
                setTimeout(() => {
                  setEventProcessEngineNodes();
                  dispatch(getEventDetailAction(eventCenterInfo.eventNo!));
                }, 2000);
              }}
              showCommentButton={false}
            />
          )}
      </Container>
    </Theme>
  );
}
