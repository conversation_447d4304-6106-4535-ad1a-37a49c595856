import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import {
  EventSpecificProcessStatus, // EventSpecificProcessStatusNumberValueMap,
} from '@manyun/ticket.model.event';
import { getEventDetailAction, selectEventDetail } from '@manyun/ticket.state.event';

import { ButtonWithoutAudit } from '../components/button-without-audit';
import { isEventNotAbleToAudit } from '../components/checking-records';
import { useEventData } from '../components/event-data-context';
import { EventLocationModalButton } from './event-specific-process-info';

// import { EventRedirectModalButton } from './event-redirect-modal-button';

export type EventSpecificOperationBarProps = {
  eventId: number;
  tabKey: string;
  onRelieveButtonClick: () => void;
};

export function EventSpecificOperationBar({
  eventId,
  tabKey,
  onRelieveButtonClick,
}: EventSpecificOperationBarProps) {
  const [, { checkUserId }] = useAuthorized();
  const dispatch = useDispatch();

  const eventCenterInfo = useSelector(selectEventDetail);

  const [configUtil] = useConfigUtil();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featuresOnwerMultiple = features.onwerMultiple;
  const featureSpecialInfoSort = features.specialInfoSort;

  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);
  const submitDisabled = isEventNotAbleToAudit(eventCenterInfo, true, true);
  const { eventStatus } = eventCenterInfo;

  const status = eventStatus ? eventStatus.code : null;

  const [, { setEventProcessEngineNodes }] = useEventData();
  // const isEventBeforeReviewAndDebrief = status
  //   ? EventSpecificProcessStatusNumberValueMap[status as unknown as EventSpecificProcessStatus] <=
  //     EventSpecificProcessStatusNumberValueMap[EventSpecificProcessStatus.Debrief]
  //   : false;

  const refetch = () => {
    dispatch(getEventDetailAction(eventCenterInfo.eventNo!));
    setTimeout(() => {
      setEventProcessEngineNodes();
    }, 2000);
  };
  // const shouldEventRedirectModalButtonShow = isEventBeforeReviewAndDebrief;
  if (
    (status as unknown as EventSpecificProcessStatus) === EventSpecificProcessStatus.Finished &&
    featureSpecialInfoSort &&
    isOwner
  ) {
    return (
      <FooterToolBar>
        <EventLocationModalButton
          mode="finished"
          eventId={eventCenterInfo.id}
          eventNo={eventCenterInfo.eventNo}
          blockGuid={eventCenterInfo.blockGuidList}
          featureSpecialInfoSort={featureSpecialInfoSort}
          detectReason={eventCenterInfo.detectReason}
          onSuccess={refetch}
        />
      </FooterToolBar>
    );
  }
  const isEventInReviewStatus =
    (status as unknown as EventSpecificProcessStatus) === EventSpecificProcessStatus.Debrief;
  const shouldAuditButtonShow = isEventInReviewStatus && tabKey !== 'checkingRecords';
  const shouldWithoutAuditButtonShow = eventCenterInfo.enableAuditSkip && isEventInReviewStatus;
  return isOwner ? (
    [
      // shouldEventRedirectModalButtonShow,
      shouldAuditButtonShow,
      shouldWithoutAuditButtonShow,
    ].includes(true) ? (
      <FooterToolBar>
        <Space>
          {/* {shouldEventRedirectModalButtonShow && (
            <EventRedirectModalButton
              eventId={eventId}
              blockGuid={eventCenterInfo.blockTag}
              onSuccess={() => {
                refetch();
              }}
            />
          )} */}
          {shouldAuditButtonShow && (
            <>
              {eventCenterInfo.detectTime ? (
                <Button type="primary" onClick={onRelieveButtonClick}>
                  事件复盘
                </Button>
              ) : (
                <Popconfirm
                  title="请填写事件定位后，再操作提交"
                  showCancel={false}
                  okText="我知道了"
                >
                  <Button type="primary">事件复盘</Button>
                </Popconfirm>
              )}
              {shouldWithoutAuditButtonShow &&
                (eventCenterInfo.detectTime ? (
                  <ButtonWithoutAudit
                    eventId={eventId}
                    eventNo={eventCenterInfo.eventNo}
                    isInvalid={submitDisabled}
                    onSuccess={() => {
                      refetch();
                    }}
                  />
                ) : (
                  <Popconfirm
                    title="请填写事件定位后，再操作提交"
                    showCancel={false}
                    okText="我知道了"
                  >
                    <Button>无需复盘，提交评审</Button>
                  </Popconfirm>
                ))}
            </>
          )}
        </Space>
      </FooterToolBar>
    ) : null
  ) : null;
}
