import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { useCreateEventProcessRecord } from '@manyun/ticket.gql.client.tickets';
import type { SpareInput } from '@manyun/ticket.gql.client.tickets';
import type { EventSpecificProcessStatus } from '@manyun/ticket.model.event';
import { EventSparePartChange } from '@manyun/ticket.model.event';

import type { RelateDevice } from './select-device-modal-button';

export type EventProcessRecordDrawerProps = {
  eventInfo: { eventId: number; eventPhase: EventSpecificProcessStatus };
  onSuccess?: () => void;
};

type FormValue = {
  handlerDesc: string;
  handleTime: Moment;
  handleContent: string;
  sparePartChange: EventSparePartChange;
  targetRoom: string[];
  changeDevices?: RelateDevice[];
  customSparePart?: string;
};
export function EventProcessRecordDrawer({ eventInfo, onSuccess }: EventProcessRecordDrawerProps) {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm<FormValue>();

  const fileInfoList = Form.useWatch('fileInfoList', form);
  // const { idc, block } = getSpaceGuidMap(blockGuid);
  const [createEventProcessRecord, { loading }] = useCreateEventProcessRecord({
    onCompleted(data) {
      if (data.createEventProcessRecord?.success) {
        // if (
        //   [EventSparePartChange.ChangeDevice, EventSparePartChange.ChangeSpare].includes(
        //     sparePartChange
        //   )
        // ) {
        //   message.success('提交成功，已创建出库工单');
        // } else {
        message.success('提交成功');
        // }
        onSuccess && onSuccess();
      } else {
        message.error(data.createEventProcessRecord?.message);
      }
    },
  });
  // const radioOptions = useMemo(() => {
  //   if (locales) {
  //     return Object.keys(locales.sparePartChange)
  //       .filter(item => item !== '__self')
  //       .map(item => ({
  //         label: locales.sparePartChange[item as EventSparePartChange],
  //         value: item,
  //       }));
  //   }
  //   return [];
  // }, [locales]);
  const showDrawer = () => {
    setOpen(true);
  };

  const onClose = () => {
    setOpen(false);
  };

  // useEffect(() => {
  //   form.setFieldsValue({ changeDevices: [] });
  // }, [form, sparePartChange]);

  const onConfirm = () => {
    form.validateFields().then(values => {
      const { handlerDesc, handleTime, handleContent, customSparePart, fileInfoList } = values;
      let spareInfo: SpareInput | null = null;
      // if (sparePartChange !== EventSparePartChange.None) {
      //   if (
      //     [EventSparePartChange.ChangeDevice, EventSparePartChange.ChangeSpare].includes(
      //       sparePartChange
      //     )
      //   ) {
      //     spareInfo = {
      //       type: EventSparePartChange.Custom,
      //     };
      //   } else {
      //     spareInfo = {
      //       type: sparePartChange,
      //       specific: customSparePart,
      //     };
      //   }
      // }
      spareInfo = {
        type: EventSparePartChange.Custom,
        specific: customSparePart,
      };
      createEventProcessRecord({
        variables: {
          query: {
            eventId: eventInfo.eventId,
            eventPhase: eventInfo.eventPhase,
            handlerDesc: handlerDesc,
            handleTime: handleTime.valueOf(),
            handleContent,
            spareInfo,
            fileInfoList: fileInfoList?.length
              ? fileInfoList.map((obj: McUploadFile) => ({
                  ...McUploadFile.fromApiObject(McUploadFile.fromJSON(obj).toApiObject()).toJSON(),
                }))
              : [],
          },
        },
      });

      setOpen(false);
    });
  };
  return (
    <>
      <Button type="link" compact onClick={showDrawer}>
        添加处理记录
      </Button>
      <Drawer
        destroyOnClose
        title="添加处理记录"
        placement="right"
        width={880}
        open={open}
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" loading={loading} onClick={onConfirm}>
              确定
            </Button>
          </Space>
        }
        onClose={onClose}
      >
        <Form
          style={{ width: 832 }}
          form={form}
          preserve={false}
          labelCol={{ span: 3 }}
          wrapperCol={{ span: 21 }}
        >
          <Form.Item
            label="处理人"
            name="handlerDesc"
            rules={[{ required: true, message: '处理人必填', whitespace: true }]}
          >
            <Input maxLength={50} />
          </Form.Item>
          <Form.Item
            label="处理时间"
            name="handleTime"
            rules={[
              { required: true, message: '处理时间必选' },
              () => ({
                validator(_, value) {
                  if (value) {
                    if (moment().diff(moment(value), 'seconds') < 0) {
                      return Promise.reject(new Error('处理时间不可晚于当前时间'));
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={current => current && current > moment().endOf('day')}
              disabledTime={disabledDateTime}
            />
          </Form.Item>
          <Form.Item
            label="处理内容"
            name="handleContent"
            rules={[{ required: true, message: '处理内容必填', whitespace: true }]}
          >
            <Input.TextArea style={{ width: 395 }} maxLength={300} showCount />
          </Form.Item>
          <Form.Item
            label="附件/图片"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <Upload
              accept=".zip,.rar,.7z,.mp4,.3gp,.mov,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx,.pdf"
              maxCount={5}
              maxFileSize={20}
              openFileDialogOnClick={fileInfoList === undefined || fileInfoList?.length < 3}
            >
              <Space direction="vertical">
                <Button
                  icon={<UploadOutlined />}
                  disabled={fileInfoList && fileInfoList?.length >= 3}
                >
                  点此上传
                </Button>
              </Space>
            </Upload>
          </Form.Item>
          <Form.Item
            label="备件说明"
            name="customSparePart"
            // rules={[{ required: true, message: '具体备件必填' }]}
          >
            <Input.TextArea style={{ width: 395 }} maxLength={300} showCount />
          </Form.Item>
          {/* <Form.Item
            label={locales.sparePartChange.__self}
            name="sparePartChange"
            extra={
              [EventSparePartChange.ChangeDevice, EventSparePartChange.ChangeSpare].includes(
                sparePartChange
              ) ? (
                <Typography.Text style={{ fontSize: 12 }} type="secondary">
                  选择“{locales.sparePartChange[sparePartChange as EventSparePartChange]}
                  ”，将自动创建出库工单
                </Typography.Text>
              ) : null
            }
          >
            <Radio.Group options={radioOptions} />
          </Form.Item>
          {sparePartChange === EventSparePartChange.Custom && (
            <Form.Item
              label="具体备件"
              name="customSparePart"
              rules={[{ required: true, message: '具体备件必填' }]}
            >
              <Input.TextArea style={{ width: 395 }} maxLength={300} />
            </Form.Item>
          )} */}
          {/* {sparePartChange === EventSparePartChange.ChangeDevice && (
            <Form.Item
              label="备件出至"
              name="targetRoom"
              rules={[{ required: true, message: '包间必选' }]}
            >
              <LocationCascader
                style={{ width: 216 }}
                idc={idc!}
                nodeTypes={['ROOM']}
                blocks={[block!]}
                authorizedOnly
              />
            </Form.Item>
          )}
          {[EventSparePartChange.ChangeDevice, EventSparePartChange.ChangeSpare].includes(
            sparePartChange
          ) && (
            <Form.Item
              name="changeDevices"
              rules={[
                {
                  required: true,
                  message: `至少选择1项库中${
                    sparePartChange === EventSparePartChange.ChangeDevice ? '设备' : '耗材'
                  }`,
                },
              ]}
              valuePropName="dataSource"
            >
              <SelectDeviceModalButton
                style={{ width: 832 }}
                blockGuid={blockGuid}
                numbered={sparePartChange === EventSparePartChange.ChangeDevice}
              />
            </Form.Item>
          )} */}
        </Form>
      </Drawer>
    </>
  );
}

const range = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
export function disabledDateTime(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (current) {
    const choseHour = current.hours();
    const choseMinute = current.minute();
    const isToday = current.isSame(moment(), 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => (choseMinute === minute ? range(second, 60) : []),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}
