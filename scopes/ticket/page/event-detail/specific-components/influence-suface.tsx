import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { exportEventCustomInfluences } from '@manyun/ticket.service.export-event-custom-influences';
import { fetchEventInfluences } from '@manyun/ticket.service.fetch-event-influences';

import type { CustomInfluence } from '../components/event-custom-influence';

const { Search } = Input;

export function InfluenceSurfaceModal({ eventId }: { eventId: number }) {
  const [loading, setLoading] = useState(false);
  const [filters, setFilters] = useState({ location: '', room: '', column: '', grid: '' });
  const [modalVisible, setModalVisible] = useState(false);

  const [influences, setInfluences] = useState<CustomInfluence[]>([]);
  const [dataSource, setDataSource] = useState<CustomInfluence[]>([]);
  const [gridInfluence, setGridInfluence] = useState<string | undefined>('');
  const [exportLoading, setExportLoading] = useState(false);

  useEffect(() => {
    if (modalVisible && eventId) {
      getCustomInfluences();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [modalVisible, eventId]);

  const getCustomInfluences = async () => {
    setLoading(true);
    const { error, data } = await fetchEventInfluences({ eventId });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setInfluences(data!.influenceScope);
    setDataSource(data!.influenceScope);
    setGridInfluence(data?.influenceGrid);
  };

  //   useEffect(() => {
  //     getCustomInfluences();
  //   }, [filters]);

  const handleFileExport = useCallback(
    async (type: string) => {
      let params: {
        eventId: number;
        guid?: string;
        consumerName?: string;
        column?: string;
        grid?: string;
        roomTag?: string;
      } = {
        eventId: eventId,
      };
      if (type === 'filtered') {
        params = {
          ...params,
          guid: filters.location,
          grid: filters.grid,
          column: filters.column,
          roomTag: filters.room,
        };
      }
      setExportLoading(true);

      const { error, data } = await exportEventCustomInfluences(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      return data;
    },
    [eventId, filters]
  );
  const afterfilteredInfluences = useMemo(() => {
    return influences.filter(item => {
      const { cabinet, room, block, idc } = getSpaceGuidMap(item.influenceGuid);
      if (filters.location) {
        if (`${idc}.${block}` !== filters.location) {
          return false;
        }
      }
      if (filters.room) {
        if (!room?.includes(filters.room)) {
          return false;
        }
      }
      if (filters.column) {
        if (!(cabinet && cabinet.split(',')[0].includes(filters.column)) || !cabinet) {
          return false;
        }
      }
      if (filters.grid) {
        if (!cabinet?.includes(filters.grid) || !cabinet) {
          return false;
        }
      }

      return true;
    });
  }, [influences, filters]); // 依赖项为 influences 和 filters

  useEffect(() => {
    setDataSource(afterfilteredInfluences);
  }, [afterfilteredInfluences]);
  // 表格列定义

  return (
    <>
      <Button type="link" compact onClick={() => setModalVisible(true)}>
        查看
      </Button>
      <Modal
        title="业务影响"
        open={modalVisible}
        footer={null}
        width={720}
        onCancel={() => setModalVisible(false)}
      >
        <Container
          //  key={`${userData.id}${userData.date}`}
          color="default"
          size="middle"
        >
          <Typography.Text type="secondary">机柜影响：</Typography.Text>
          <Typography.Text>{gridInfluence ?? '--'}</Typography.Text>
        </Container>
        {influences.length ? (
          <Space style={{ marginBottom: 16, marginTop: 16 }}>
            <LocationTreeSelect
              nodeTypes={['IDC', 'BLOCK']}
              showSearch
              // idc={idc!}
              // block={block!}
              allowClear
              authorizedOnly
              disabledTypes={['IDC']}
              placeholder="楼栋"
              includeVirtualBlocks
              style={{ width: 145 }}
              onChange={value => setFilters(prev => ({ ...prev, location: value as string }))}
            />

            <Search
              placeholder="包间编号"
              style={{ width: 150 }}
              allowClear
              onChange={value => setFilters(prev => ({ ...prev, room: value.target.value }))}
            />
            <Search
              placeholder="机列"
              style={{ width: 120 }}
              allowClear
              onChange={value => setFilters(prev => ({ ...prev, column: value.target.value }))}
            />
            <Search
              placeholder="机柜"
              style={{ width: 150 }}
              allowClear
              onChange={value => setFilters(prev => ({ ...prev, grid: value.target.value }))}
            />

            <FileExport
              key="export"
              text="导出"
              filename="业务影响.xls"
              showExportFiltered
              disabled={exportLoading || !influences.length}
              data={type => handleFileExport(type)}
            />
          </Space>
        ) : null}
        {influences.length ? (
          <Table
            rowKey={record => record.influenceGuid}
            columns={[
              {
                dataIndex: 'block',
                title: '楼栋',
                render: (_, { influenceGuid }) => getSpaceGuidMap(influenceGuid).block ?? '--',
              },
              {
                dataIndex: 'room',
                title: '包间',
                render: (_, { influenceGuid }) => getSpaceGuidMap(influenceGuid).room ?? '--',
              },
              {
                dataIndex: 'column',
                title: '机列',

                render: (_, { influenceType, influenceGuid }) => {
                  const { cabinet } = getSpaceGuidMap(influenceGuid);
                  return influenceType === 'GRID'
                    ? typeof cabinet === 'string'
                      ? cabinet[0]
                      : '--'
                    : (cabinet ?? '--');
                },
              },
              {
                dataIndex: 'grid',
                title: '机柜',
                render: (_, { influenceType, influenceGuid }) => {
                  if (influenceType === 'COLUMN') {
                    return '--';
                  }
                  return getSpaceGuidMap(influenceGuid).cabinet ?? '--';
                },
              },
            ]}
            dataSource={dataSource}
            loading={loading}
            pagination={{ total: dataSource.length }}
          />
        ) : null}
      </Modal>
    </>
  );
}
