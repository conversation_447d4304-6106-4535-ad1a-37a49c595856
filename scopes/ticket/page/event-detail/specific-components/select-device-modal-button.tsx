import React, { useEffect, useMemo, useState } from 'react';

import { cloneDeep } from 'lodash';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { useLazyAvailableDevicesInBlock } from '@manyun/resource-hub.gql.client.devices';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

export type RelateDevice = {
  topCategory: string;
  secondCategory: string;
  deviceType: string;
  vendor: string;
  productModel: string;
  blockGuid: string;
  roomTag: string;
  avaliableCount: number;
  useCount?: number;
};
export type SelectDeviceModalButtonProps = {
  style: React.CSSProperties;
  numbered: boolean;
  blockGuid: string;
  dataSource?: RelateDevice[];
  onChange?: (value: RelateDevice[]) => void;
};
// 应该是SelectDeviceTypeModalButton才对，选的是三级分类
export const SelectDeviceModalButton = React.forwardRef(
  (
    { numbered, blockGuid, dataSource, style, onChange }: SelectDeviceModalButtonProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([]);
    const [selectedRows, setSelectedRows] = useState<RelateDevice[]>([]);
    const [isCheckableMode, setIsCheckableMode] = useState(false);
    const [deviceUseCountMap, setDeviceUseCountMap] = useState<Record<string, number>>({});

    const [open, setOpen] = useState(false);
    const chosenDevicesCount = useDeepCompareMemo(() => {
      return selectedRows.reduce((pre, cur) => {
        return pre + (deviceUseCountMap[getRecordKey(cur)] ?? 0);
      }, 0);
    }, [selectedRows, deviceUseCountMap]);
    const showModal = () => {
      setOpen(true);
    };
    const closeModal = () => {
      setOpen(false);
      setIsCheckableMode(false);
    };
    useEffect(() => {
      if (open) {
        setSelectedKeys((dataSource ?? []).map(item => getRecordKey(item)));
        setSelectedRows(dataSource ?? []);
        const uniqueKeys = (dataSource ?? []).map(item => ({
          key: getRecordKey(item),
          value: item.useCount,
        }));
        const initialMap: Record<string, number> = {};
        if (uniqueKeys.length > 0) {
          for (const item of uniqueKeys) {
            initialMap[`${item.key}`] = item.value!;
          }
        }
        setDeviceUseCountMap(initialMap);
      }
    }, [dataSource, open]);
    const hasChosen = dataSource && dataSource.length > 0;
    return (
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="large">
        <>
          <Button
            type="primary"
            onClick={() => {
              showModal();
            }}
          >
            {hasChosen ? '修改备件' : '添加备件'}
          </Button>
          <Modal
            destroyOnClose
            width={960}
            title="选择备件"
            open={open}
            okText="确定"
            onCancel={() => {
              closeModal();
            }}
            onOk={() => {
              if (
                selectedRows.some(
                  item =>
                    deviceUseCountMap[getRecordKey(item)] === 0 ||
                    !deviceUseCountMap[getRecordKey(item)]
                )
              ) {
                message.error('所选备件中存在备件数量为空');
                return;
              }
              onChange &&
                onChange(
                  selectedRows
                    ? selectedRows.map(item => ({
                        ...item,
                        useCount: deviceUseCountMap[getRecordKey(item)],
                      }))
                    : []
                );
              const newMap = cloneDeep(deviceUseCountMap);

              const mapKeys = Object.keys(newMap);
              for (const key of mapKeys) {
                if (!selectedKeys.includes(key)) {
                  delete newMap[key];
                }
              }
              setDeviceUseCountMap(newMap);
              closeModal();
            }}
          >
            <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="middle">
              {!isCheckableMode &&
                selectedRows &&
                selectedRows.length > 0 &&
                chosenDevicesCount > 0 && (
                  <Space>
                    <Typography.Text>
                      已选择{selectedRows.length}项，共
                      {chosenDevicesCount}
                      个备件
                    </Typography.Text>
                    {!isCheckableMode && (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          setIsCheckableMode(true);
                        }}
                      >
                        {'查看已选项 >>'}
                      </Button>
                    )}
                  </Space>
                )}

              {isCheckableMode && (
                <Button
                  type="link"
                  compact
                  onClick={() => {
                    setIsCheckableMode(false);
                  }}
                >
                  {'<< 返回继续选择'}
                </Button>
              )}

              <DeviceTable
                blockGuid={blockGuid}
                numbered={numbered}
                mode={isCheckableMode ? 'check' : 'new'}
                deviceUseCountMap={deviceUseCountMap}
                setDeviceUseCountMap={setDeviceUseCountMap}
                dataSource={selectedRows}
                handleChosenRows={onChange}
                selectedKeys={selectedKeys}
                setSelectedKeys={setSelectedKeys}
                selectedRows={selectedRows}
                setSelectedRows={setSelectedRows}
              />
            </Space>
          </Modal>
        </>
        <DeviceTable
          style={style}
          numbered
          deviceUseCountMap={deviceUseCountMap}
          blockGuid={blockGuid}
          mode="view"
          dataSource={dataSource}
          handleChosenRows={onChange}
        />
      </Space>
    );
  }
);
SelectDeviceModalButton.displayName = 'SelectDeviceModalButton';

type DeviceTableProps = {
  mode: 'new' | 'view' | 'check';
  numbered: boolean;
  blockGuid: string;
  dataSource?: RelateDevice[];
  deviceUseCountMap?: Record<string, number>;
  setDeviceUseCountMap?: (params: Record<string, number>) => void;
  selectedKeys?: React.Key[];
  setSelectedKeys?: (params: (param: React.Key[]) => React.Key[]) => void;
  selectedRows?: RelateDevice[];
  setSelectedRows?: (params: ((param: RelateDevice[]) => RelateDevice[]) | RelateDevice[]) => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleChosenRows?: (value: RelateDevice[]) => void;
} & TableProps<RelateDevice>;
function DeviceTable({
  mode,
  numbered,
  blockGuid,
  dataSource,
  deviceUseCountMap,
  setDeviceUseCountMap,
  handleChosenRows,
  selectedKeys,
  setSelectedKeys,
  selectedRows,
  setSelectedRows,
  ...props
}: DeviceTableProps) {
  const [getDevices, { data, loading }] = useLazyAvailableDevicesInBlock();

  const [thirdTypeCode, setThirdTypeCode] = useState<string | undefined>(undefined);
  const [vendorCode, setVendorCode] = useState<(string | undefined)[] | undefined>(undefined);
  useEffect(() => {
    if (mode === 'new') {
      getDevices({ variables: { numbered, blockGuid, pageNum: 1, pageSize: 10 } });
    }
  }, [blockGuid, getDevices, mode, numbered]);

  const columns = useDeepCompareMemo(() => {
    const basicColumn: Array<ColumnType<RelateDevice>> = [
      {
        title: '三级分类',
        dataIndex: 'deviceType',
        key: 'deviceType',
        render: (_, { deviceType }) => {
          return <DeviceTypeText code={deviceType} />;
        },
      },
      {
        title: '机房楼栋',
        dataIndex: 'blockGuid',
        key: 'blockGuid',
        render: () => {
          return blockGuid;
        },
      },
      {
        title: '包间',
        dataIndex: 'roomTag',
        key: 'roomTag',
      },
      {
        title: '厂商',
        dataIndex: 'vendor',
        key: 'vendor',
      },
      {
        title: '型号',
        dataIndex: 'productModel',
        key: 'productModel',
      },
    ];
    if (mode === 'view') {
      const viewColumn: Array<ColumnType<RelateDevice>> = [
        ...basicColumn,
        {
          title: '使用数量',
          dataIndex: 'useCount',
          key: 'useCount',
          render: (_, record) => record.useCount,
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          render: (_, record) => {
            return (
              <Button
                type="link"
                compact
                onClick={() => {
                  handleChosenRows &&
                    handleChosenRows(
                      dataSource?.filter(item => getRecordKey(item) !== getRecordKey(record)) ?? []
                    );
                  if (deviceUseCountMap && setDeviceUseCountMap) {
                    const newMap = cloneDeep(deviceUseCountMap);
                    delete newMap[getRecordKey(record)];
                    setDeviceUseCountMap(newMap);
                  }
                }}
              >
                移除
              </Button>
            );
          },
        },
      ];
      return viewColumn;
    }
    if (mode === 'check') {
      const checkColumn: Array<ColumnType<RelateDevice>> = [
        ...basicColumn,
        { title: '可用库存', dataIndex: 'avaliableCount', key: 'avaliableCount' },
        {
          title: '出库数量',
          dataIndex: 'useCount',
          key: 'useCount',
          render: (_, record) => {
            const uniqueKey = getRecordKey(record);
            if (selectedKeys?.includes(uniqueKey)) {
              return (
                <InputNumber
                  defaultValue={
                    deviceUseCountMap && deviceUseCountMap[uniqueKey]
                      ? deviceUseCountMap[uniqueKey]
                      : 0
                  }
                  precision={0}
                  min={1}
                  max={record.avaliableCount}
                  onChange={value => {
                    if (deviceUseCountMap && setDeviceUseCountMap) {
                      const newMap = cloneDeep(deviceUseCountMap);
                      newMap[uniqueKey] = value as number;
                      setDeviceUseCountMap(newMap);
                    }
                  }}
                />
              );
            } else {
              return '--';
            }
          },
        },
        {
          title: '操作',
          dataIndex: 'operation',
          key: 'operation',
          render: (_, record) => {
            return (
              <Button
                type="link"
                compact
                onClick={() => {
                  setSelectedRows &&
                    setSelectedRows(
                      dataSource?.filter(item => getRecordKey(item) !== getRecordKey(record)) ?? []
                    );
                  const keys = dataSource
                    ?.filter(item => getRecordKey(item) !== getRecordKey(record))
                    .map(item => getRecordKey(item));
                  setSelectedKeys &&
                    setSelectedKeys((keys as unknown as (param: React.Key[]) => React.Key[]) ?? []);
                  if (deviceUseCountMap && setDeviceUseCountMap) {
                    const newMap = cloneDeep(deviceUseCountMap);
                    delete newMap[getRecordKey(record)];
                    setDeviceUseCountMap(newMap);
                  }
                }}
              >
                取消选择
              </Button>
            );
          },
        },
      ];
      return checkColumn;
    } else {
      const newColumn: Array<ColumnType<RelateDevice>> = [
        ...basicColumn,
        { title: '可用库存', dataIndex: 'avaliableCount', key: 'avaliableCount' },
        {
          title: '出库数量',
          dataIndex: 'useCount',
          key: 'useCount',
          render: (_, record) => {
            const uniqueKey = getRecordKey(record);
            if (selectedKeys?.includes(uniqueKey)) {
              return (
                <InputNumber
                  defaultValue={
                    deviceUseCountMap && deviceUseCountMap[uniqueKey]
                      ? deviceUseCountMap[uniqueKey]
                      : 0
                  }
                  precision={0}
                  min={1}
                  max={record.avaliableCount}
                  onChange={value => {
                    if (deviceUseCountMap && setDeviceUseCountMap) {
                      const newMap = cloneDeep(deviceUseCountMap);
                      newMap[uniqueKey] = value as number;
                      setDeviceUseCountMap(newMap);
                    }
                  }}
                />
              );
            } else {
              return '--';
            }
          },
        },
      ];

      return newColumn;
    }
  }, [
    mode,
    blockGuid,
    handleChosenRows,
    dataSource,
    selectedKeys,
    selectedRows,
    setSelectedRows,
    deviceUseCountMap,
  ]);

  const tableDataSource: RelateDevice[] = useMemo(() => {
    if (['view', 'check'].includes(mode)) {
      return (dataSource?.map(item => ({ ...item, blockGuid })) ?? []) as RelateDevice[];
    } else {
      return data?.availableDevicesInBlock?.data
        ? (data?.availableDevicesInBlock?.data?.map(item => ({
            ...item,
            blockGuid,
          })) as RelateDevice[])
        : [];
    }
  }, [mode, dataSource, blockGuid, data?.availableDevicesInBlock?.data]);
  return (
    <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="middle">
      {mode !== 'view' && (
        <Space>
          <DeviceTypeCascader
            style={{ width: 168 }}
            placeholder="三级分类"
            dataType={['snDevice', 'noSnDevice']}
            disabledTypeList={['C0', 'C1']}
            numbered={numbered}
            allowClear
            value={thirdTypeCode}
            onChange={value => {
              getDevices({
                variables: {
                  deviceType: value,
                  vendor:
                    Array.isArray(vendorCode) && vendorCode.length === 2
                      ? vendorCode[0]
                      : undefined,
                  productModel:
                    Array.isArray(vendorCode) && vendorCode.length === 2
                      ? vendorCode[1]
                      : undefined,
                  pageNum: 1,
                  pageSize: 10,
                  numbered,
                  blockGuid,
                },
              });
              setThirdTypeCode(value);
            }}
          />
          <VendorModelSelect
            style={{ width: 168 }}
            deviceType={thirdTypeCode}
            allowClear
            numbered={numbered}
            vendorPlaceholder="厂商"
            modelPlaceholder="型号"
            value={vendorCode as [string | undefined, string | undefined] | undefined}
            onChange={value => {
              setVendorCode(value);
              getDevices({
                variables: {
                  deviceType: thirdTypeCode,
                  vendor: Array.isArray(value) && value.length === 2 ? value[0] : undefined,
                  productModel: Array.isArray(value) && value.length === 2 ? value[1] : undefined,
                  pageNum: 1,
                  pageSize: 10,
                  numbered,
                  blockGuid,
                },
              });
            }}
          />
        </Space>
      )}
      <Table<RelateDevice>
        {...props}
        loading={mode === 'new' ? loading : false}
        columns={columns}
        rowKey={record => getRecordKey(record)}
        dataSource={tableDataSource}
        rowSelection={
          mode === 'new'
            ? {
                selectedRowKeys: selectedKeys,
                onChange: (selectedRowKeys, selectedRows) => {
                  if (setSelectedKeys && setSelectedRows) {
                    setSelectedKeys(keys => [
                      ...(keys ?? []).filter(
                        key =>
                          !(tableDataSource ?? []).find(
                            item => item && getRecordKey({ ...item, blockGuid }) === key
                          )
                      ),
                      ...selectedRowKeys,
                    ]);
                    // eslint-disable-next-line @typescript-eslint/no-explicit-any
                    setSelectedRows(rows => [
                      ...(rows ?? []).filter(
                        row =>
                          !(tableDataSource ?? []).find(
                            item =>
                              item && getRecordKey({ ...item, blockGuid }) === getRecordKey(row)
                          )
                      ),
                      ...selectedRows,
                    ]);
                  }
                },
              }
            : undefined
        }
        pagination={
          mode === 'new'
            ? {
                total: data?.availableDevicesInBlock?.total ?? 0,
                pageSize: 10,
                pageSizeOptions: ['10'],
                onChange: (page, pageSize) => {
                  getDevices({
                    variables: {
                      numbered,
                      blockGuid,
                      pageNum: page,
                      pageSize: 10,
                    },
                  });
                },
              }
            : undefined
        }
      />
    </Space>
  );
}

function getRecordKey(record: RelateDevice) {
  return `${record.deviceType}${record.blockGuid}${record.roomTag}${record.vendor}${record.productModel}`;
}
