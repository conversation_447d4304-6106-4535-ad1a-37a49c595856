import moment from 'moment';
import type { Moment } from 'moment';
import React, { useEffect, useLayoutEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { QueryUserResourceData } from '@manyun/iam.gql.client.iam';
import { useLazyUserResources } from '@manyun/iam.gql.client.iam';
import type {
  DrillOrderApprovalNode,
  EventProcessRecordJson,
} from '@manyun/ticket.gql.client.tickets';
import {
  useFinishedCurrentEvent,
  useLazyEventProcessRecords,
  useLocateEvent,
  useSkipEventCurrentPhase,
} from '@manyun/ticket.gql.client.tickets';
import type { EventJSON, ProcessEngineConfig } from '@manyun/ticket.model.event';
import {
  EventProcessPhaseConfigCondition,
  EventProcessPhaseOperator,
  EventSpecificProcessPhaseStatus,
  EventSpecificProcessStatus,
  EventSpecificProcessStatusNumberValueMap,
  getEventLocales,
} from '@manyun/ticket.model.event';
import {
  getEventDetailAction,
  getEventLifeAction,
  selectEventLife,
} from '@manyun/ticket.state.event';
import { TicketSlaText } from '@manyun/ticket.ui.tickets-table';

import { useEventData } from '../components/event-data-context';
import { EventProcessRecordDrawer } from './event-process-record-drawer';
import { EventStatusOperationModalButton } from './event-status-operation-modal-button';
import { ImportProcess } from './import-process-modal';
import { UploadProcessRecordFileModal } from './upload-process-record-file-modal';

export type PhaseRecord = {
  key: string;
  handleTime: string;
  handlePeople?: { id: number };
  handleContent: string;
  spare?: string[];
};
export type EventStatusRecord = {
  key: string;
  phase: { name: string; value: EventSpecificProcessStatus };
  nextPhase: { name: string; value: EventSpecificProcessStatus };
  phaseStatus: { value: string; color: string; label: string };
  handlePeople?: { id: number };
  handleResult?: string;
  sla?: number;
  beginTime?: number;
  finishTime?: number;
};

export type EventSpecificProcessInfoProps = {
  eventCenterInfo: EventJSON;
};
export function EventSpecificProcessInfo({ eventCenterInfo }: EventSpecificProcessInfoProps) {
  const locales = useMemo(() => getEventLocales(), []);
  const dispatch = useDispatch();
  const { userId } = useSelector(selectMe);

  const [getUserResources, { data: userResourcesData }] = useLazyUserResources();
  useEffect(() => {
    if (userId) {
      getUserResources({
        variables: { params: { userId } },
      });
    }
  }, [getUserResources, userId]);
  const [{ eventProcessEngineNodes, eventProcessEngineConfig }, { setEventProcessEngineNodes }] =
    useEventData();
  const [skipEventCurrentPhase, { loading: skipLoading }] = useSkipEventCurrentPhase({
    onCompleted(data) {
      if (data.skipEventCurrentPhase?.success) {
        message.success('跳过阶段成功');
        dispatch(getEventLifeAction(eventCenterInfo.eventNo!));
        dispatch(getEventDetailAction(eventCenterInfo.eventNo!));
        setEventProcessEngineNodes();
      } else {
        message.error(data.skipEventCurrentPhase?.message);
      }
    },
  });
  const [getPhaseRecords, { data: phaseRecords, refetch }] = useLazyEventProcessRecords();
  const [, { checkCode, checkUserId }] = useAuthorized();
  // const isOwner = checkUserId(eventCenterInfo?.eventOwnerId);
  const [configUtil] = useConfigUtil();

  const {
    events: { features },
  } = configUtil.getScopeCommonConfigs('ticket');
  const featuresOnwerMultiple = features.onwerMultiple;
  const featureSpecialInfoSort = features.specialInfoSort;
  const isOwner = featuresOnwerMultiple
    ? eventCenterInfo.eventOwnerInfoList?.some(item => checkUserId(item.id))
    : checkUserId(eventCenterInfo?.eventOwnerId);

  const eventLife = useSelector(selectEventLife);
  const [expendedKeys, setExpendedKeys] = useState<EventSpecificProcessStatus[]>([]);
  const [shouldRender, setShouldRender] = useState(false);

  const { eventStatus } = eventCenterInfo;

  const status = eventStatus ? eventStatus.code : null;

  const isEventBeforeReviewAndDebrief = status
    ? EventSpecificProcessStatusNumberValueMap[status as unknown as EventSpecificProcessStatus] <=
      EventSpecificProcessStatusNumberValueMap[EventSpecificProcessStatus.Debrief]
    : false;
  const columns: ColumnsType<EventStatusRecord> = useMemo(() => {
    const basicColumns: ColumnsType<EventStatusRecord> = [
      {
        title: '阶段',
        dataIndex: 'phase',
        key: 'phase',
        render: (_, { phaseStatus, phase }) => (
          <Space wrap>
            <div>{phase.name}</div>
            <div>{phaseStatus && <Tag color={phaseStatus.color}>{phaseStatus.label}</Tag>}</div>
          </Space>
        ),
      },
      {
        title: '处理人',
        width: 90,
        dataIndex: 'handlePeople',
        key: 'handlePeople',
        render: (_, { handlePeople }) => {
          if (handlePeople) {
            return <User id={handlePeople.id} showAvatar={false} />;
          }
          return '--';
        },
      },
      {
        title: '处理结果',
        dataIndex: 'handleResult',
        key: 'handleResult',
        width: '35%',
        render: (_, { handleResult }) => {
          if (handleResult) {
            return (
              <Typography.Paragraph
                style={{ marginBottom: 0, width: '100%' }}
                ellipsis={{ rows: 3, tooltip: true }}
              >
                {handleResult}
              </Typography.Paragraph>
            );
          }
          return '--';
        },
      },
      {
        title: 'SLA',
        dataIndex: 'sla',
        key: 'sla',
        render: (_, { sla }) => {
          if (sla) {
            return (
              <TicketSlaText
                taskSla={sla}
                delay={sla}
                unit="SECOND"
                effectTime={null}
                endTime={null}
              />
            );
          }
          return '--';
        },
      },
      {
        title: '开始时间',
        dataIndex: 'beginTime',
        key: 'beginTime',
        render: (_, { beginTime }) => {
          if (beginTime) {
            return moment(beginTime).format('YYYY-MM-DD HH:mm:ss');
          }
          return '--';
        },
      },
      {
        title: '完成时间',
        dataIndex: 'finishTime',
        key: 'finishTime',
        render: (_, { finishTime }) => {
          if (finishTime) {
            return moment(finishTime).format('YYYY-MM-DD HH:mm:ss');
          }
          return '--';
        },
      },
      {
        title: '操作',
        dataIndex: 'operation',
        key: 'operation',
        render: (_, record) => {
          const isPhaseFinished =
            record.phaseStatus.value === EventSpecificProcessPhaseStatus.Completed;
          const isPhaseSkipped =
            record.phaseStatus.value === EventSpecificProcessPhaseStatus.Skipped;
          return isPhaseOperatable(record.phase.value, eventProcessEngineNodes) &&
            ((judgePhaseOperator(record.phase.value, eventProcessEngineConfig).optAuth ===
              EventProcessPhaseOperator.Owner &&
              isOwner) ||
              (judgePhaseOperator(record.phase.value, eventProcessEngineConfig).optAuth ===
                EventProcessPhaseOperator.UserUnderBlock &&
                userResourcesData?.userResources &&
                userResourcesData?.userResources?.some(item =>
                  eventCenterInfo.blockGuidList?.includes(item.code)
                ))) ? (
            <Space>
              <EventProcessRecordDrawer
                eventInfo={{ eventId: eventCenterInfo.id, eventPhase: record.phase.value }}
                onSuccess={() => {
                  (async function () {
                    let data = [];
                    setShouldRender(false);
                    const { data: phaseRecordsData, error } = await getPhaseRecords({
                      variables: {
                        mode: 'multiple',
                        eventId: eventCenterInfo.id,
                        eventPhase: EventSpecificProcessStatus.Emergency,
                      },
                    });
                    if (error) {
                      message.error(error.message);
                      setShouldRender(true);
                      return;
                    }
                    data = [
                      {
                        type: EventSpecificProcessStatus.Emergency,
                        total:
                          phaseRecordsData?.eventProcessRecords?.data.filter(
                            item => item.eventPhase === EventSpecificProcessStatus.Emergency
                          )?.length ?? 0,
                      },
                      {
                        type: EventSpecificProcessStatus.Fix,
                        total:
                          phaseRecordsData?.eventProcessRecords?.data.filter(
                            item => item.eventPhase === EventSpecificProcessStatus.Fix
                          )?.length ?? 0,
                      },
                      {
                        type: EventSpecificProcessStatus.Recovery,
                        total:
                          phaseRecordsData?.eventProcessRecords?.data.filter(
                            item => item.eventPhase === EventSpecificProcessStatus.Recovery
                          )?.length ?? 0,
                      },
                    ];

                    setExpendedKeys(data.filter(item => item.total > 0).map(item => item.type));
                    setShouldRender(true);
                  })();
                }}
              />

              {judgePhaseOperator(record.phase.value, eventProcessEngineConfig).condition !==
                EventProcessPhaseConfigCondition.None &&
              ((judgePhaseOperator(record.phase.value, eventProcessEngineConfig).condition ===
                EventProcessPhaseConfigCondition.PhaseMore &&
                !expendedKeys.includes(record.phase.value)) ||
                (judgePhaseOperator(record.phase.value, eventProcessEngineConfig).condition ===
                  EventProcessPhaseConfigCondition.EventMore &&
                  !Boolean(phaseRecords?.eventProcessRecords?.data.length))) ? (
                <Popconfirm
                  title={
                    judgePhaseOperator(record.phase.value, eventProcessEngineConfig).condition ===
                    EventProcessPhaseConfigCondition.PhaseMore ? (
                      <Typography.Text>
                        当前阶段至少添加一条处理记
                        <br />
                        录，才可操作{`${isPhaseFinished ? '编辑' : '完成'}`}
                      </Typography.Text>
                    ) : (
                      <Typography.Text>
                        事件单至少添加一条处理记录，
                        <br />
                        才可操作{`${isPhaseFinished ? '编辑' : '完成'}`}
                      </Typography.Text>
                    )
                  }
                  showCancel={false}
                  okText="我知道了"
                >
                  <EventStatusOperationModalButton
                    mode={isPhaseFinished ? 'edit' : 'new'}
                    blockGuid={eventCenterInfo.blockGuidList}
                    eventId={eventCenterInfo.id!}
                    status={record.phase.value}
                    onClick={e => e.stopPropagation()}
                  />
                </Popconfirm>
              ) : (
                <EventStatusOperationModalButton
                  mode={isPhaseFinished ? 'edit' : 'new'}
                  blockGuid={eventCenterInfo.blockGuidList}
                  eventId={eventCenterInfo.id!}
                  status={record.phase.value}
                  initialValue={
                    isPhaseFinished
                      ? {
                          handlePeople: { value: record!.handlePeople!.id },
                          beginTime: moment(record.beginTime),
                          finishTime: moment(record.finishTime),
                          resultDescription: record.handleResult,
                        }
                      : {
                          handlePeople: { value: userId! },
                          beginTime: record.beginTime ? moment(record.beginTime) : undefined,
                        }
                  }
                  onSuccess={() => {
                    dispatch(getEventLifeAction(eventCenterInfo.eventNo!));
                    dispatch(getEventDetailAction(eventCenterInfo.eventNo!));
                    setEventProcessEngineNodes();
                  }}
                />
              )}

              {record.nextPhase.value !== EventSpecificProcessStatus.Emergency &&
                !isPhaseFinished &&
                !isPhaseSkipped &&
                judgePhaseOperator(record.phase.value, eventProcessEngineConfig).canSkip && (
                  <Popconfirm
                    title={
                      <Typography.Text>
                        确认跳过"{record.phase.name}"阶段？跳过后将直
                        <br />
                        接进入"{record.nextPhase.name}
                        "阶段，请谨慎操作！
                      </Typography.Text>
                    }
                    okText="确认跳过"
                    cancelText="我再想想"
                    okButtonProps={{ loading: skipLoading }}
                    onConfirm={() => {
                      skipEventCurrentPhase({
                        variables: { eventId: eventCenterInfo.id, eventPhase: record.phase.value },
                      });
                    }}
                  >
                    <Button type="link" loading={skipLoading} compact>
                      跳过
                    </Button>
                  </Popconfirm>
                )}
            </Space>
          ) : (
            '--'
          );
        },
      },
    ];
    if (!isEventBeforeReviewAndDebrief) {
      return basicColumns.slice(0, basicColumns.length - 1);
    }
    return basicColumns;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    isEventBeforeReviewAndDebrief,
    eventProcessEngineConfig?.emergency?.canSkip,
    eventProcessEngineConfig?.emergency?.optAuth,
    eventProcessEngineConfig?.emergency?.condition,
    eventProcessEngineConfig?.repair?.canSkip,
    eventProcessEngineConfig?.repair?.optAuth,
    eventProcessEngineConfig?.repair?.condition,
    eventProcessEngineConfig?.recovery?.canSkip,
    eventProcessEngineConfig?.recovery?.optAuth,
    eventProcessEngineConfig?.recovery?.condition,
    eventProcessEngineNodes,
    isOwner,
    userResourcesData?.userResources,
    eventCenterInfo.blockGuidList,
    eventCenterInfo.id,
    eventCenterInfo.blockTag,
    eventCenterInfo.eventNo,
    expendedKeys,
    phaseRecords?.eventProcessRecords?.data.length,
    userId,
    skipLoading,
    getPhaseRecords,
    dispatch,
    setEventProcessEngineNodes,
    skipEventCurrentPhase,
  ]);

  userResourcesData?.userResources?.findIndex(item =>
    eventCenterInfo.blockGuidList?.includes(item.code)
  );
  const dataSource = useMemo(() => {
    if (locales && eventLife.eventProgress) {
      return eventLife.eventProgress.map(item => {
        const nextPhase = getNextPhase(item.eventPhase);
        return {
          key: item.eventPhase,
          phase: {
            name: locales.specificProcessStatus[item.eventPhase],
            value: item.eventPhase,
          },
          nextPhase: {
            name: locales.specificProcessStatus[nextPhase],
            value: nextPhase,
          },
          phaseStatus: {
            value: item.status,
            label: locales.specificProcessPhaseStatus[item.status],
            color: getStatusColorMap(item.status),
          },
          handlePeople: item.handlerId ? { id: item.handlerId! } : undefined,
          handleResult: item.handleContent,
          sla: item.sla,
          beginTime: item.startTime,
          finishTime: item.endTime,
        };
      });
    }
    return [];
  }, [locales, eventLife.eventProgress]);

  useLayoutEffect(() => {
    (async function () {
      let data = [];
      const { data: phaseRecordsData, error } = await getPhaseRecords({
        variables: {
          mode: 'multiple',
          eventId: eventCenterInfo.id,
          eventPhase: EventSpecificProcessStatus.Emergency,
        },
      });
      if (error) {
        message.error(error.message);
        setShouldRender(true);
        return;
      }
      data = [
        {
          type: EventSpecificProcessStatus.Emergency,
          total:
            phaseRecordsData?.eventProcessRecords?.data.filter(
              item => item.eventPhase === EventSpecificProcessStatus.Emergency
            )?.length ?? 0,
        },
        {
          type: EventSpecificProcessStatus.Fix,
          total:
            phaseRecordsData?.eventProcessRecords?.data.filter(
              item => item.eventPhase === EventSpecificProcessStatus.Fix
            )?.length ?? 0,
        },
        {
          type: EventSpecificProcessStatus.Recovery,
          total:
            phaseRecordsData?.eventProcessRecords?.data.filter(
              item => item.eventPhase === EventSpecificProcessStatus.Recovery
            )?.length ?? 0,
        },
      ];

      setExpendedKeys(data.filter(item => item.total > 0).map(item => item.type));
      setShouldRender(true);
    })();
  }, [eventCenterInfo.id, getPhaseRecords]);
  const hasEventLocationInfo = eventCenterInfo.detectTime;
  const showEventLocationButton = checkCode('element_detect-event') || isOwner;
  return (
    <Spin spinning={!shouldRender}>
      <Space style={{ display: 'flex', width: '100%' }} size="middle" direction="vertical">
        {/* 需要根据是否已经存在事件定位信息，判断是new还是edit,并且遇到直接关单的单子，也不应该展示 */}
        {!hasEventLocationInfo &&
          showEventLocationButton &&
          isEventBeforeReviewAndDebrief &&
          !featureSpecialInfoSort && (
            <EventLocationModalButton
              mode="new"
              eventId={eventCenterInfo.id}
              blockGuid={eventCenterInfo.blockGuidList}
              featureSpecialInfoSort={featureSpecialInfoSort}
            />
          )}
        {hasEventLocationInfo && !featureSpecialInfoSort && (
          <Space style={{ display: 'flex', width: '100%' }} direction="vertical" size="middle">
            <Space>
              <Typography.Title style={{ marginBottom: 0 }} level={5}>
                事件定位
              </Typography.Title>
              <Space split={<Divider type="vertical" emphasis spaceSize="mini" />}>
                <Typography.Text>
                  {moment(eventCenterInfo.detectTime).format('YYYY-MM-DD HH:mm:ss')}
                </Typography.Text>
                <User id={eventCenterInfo.detectUserId!} showAvatar={false} />
              </Space>
              {isEventBeforeReviewAndDebrief && showEventLocationButton && (
                <EventLocationModalButton
                  mode="edit"
                  eventId={eventCenterInfo.id}
                  blockGuid={eventCenterInfo.blockGuidList}
                  featureSpecialInfoSort={featureSpecialInfoSort}
                  initialValues={{
                    detectReason: eventCenterInfo.detectReason!,
                    detectUserId: { value: eventCenterInfo.detectUserId! },
                    detectTime: moment(eventCenterInfo.detectTime),
                  }}
                />
              )}
            </Space>
            <Typography.Text>
              定位原因：
              {eventCenterInfo.detectReason ?? '--'}
            </Typography.Text>
          </Space>
        )}
        {isEventBeforeReviewAndDebrief && isOwner && (
          <ImportProcess eventId={eventCenterInfo.id} onSuccess={refetch} />
        )}
        {shouldRender ? (
          <Table
            loading={!shouldRender}
            dataSource={dataSource}
            columns={columns}
            expandable={{
              expandedRowRender: parent => (
                <ExpandTable
                  parent={parent}
                  eventStatus={status}
                  originDatasource={phaseRecords?.eventProcessRecords?.data ?? []}
                  eventProcessEngineConfig={eventProcessEngineConfig}
                  eventProcessEngineNodes={eventProcessEngineNodes}
                  userResourcesData={userResourcesData}
                  isOwner={isOwner}
                  blockGuidList={eventCenterInfo.blockGuidList}
                  onSuccess={() => {
                    refetch();
                  }}
                />
              ),
              defaultExpandedRowKeys: expendedKeys,
            }}
            pagination={false}
          />
        ) : null}
      </Space>
    </Spin>
  );
}

function ExpandTable({
  parent,
  originDatasource,
  eventProcessEngineConfig,
  eventProcessEngineNodes,
  userResourcesData,
  isOwner,
  blockGuidList,
  eventStatus,
  onSuccess,
}: {
  parent: EventStatusRecord;
  originDatasource: EventProcessRecordJson[];
  eventProcessEngineConfig?: ProcessEngineConfig;
  eventProcessEngineNodes: DrillOrderApprovalNode[];
  userResourcesData?: QueryUserResourceData;
  isOwner?: boolean;
  blockGuidList: string[];
  eventStatus: string | null;
  onSuccess: () => void;
}) {
  const subColumns: ColumnsType<EventProcessRecordJson> = [
    {
      title: '处理时间',
      dataIndex: 'handleTime',
      key: 'handleTime',
      width: 180,
      sorter: (a, b) => Number(a.handleTime ?? 0) - Number(b.handleTime ?? 0),
      render: (_, { handleTime }) => {
        if (handleTime) {
          return moment(handleTime).format('YYYY-MM-DD HH:mm:ss');
        }
        return '--';
      },
    },
    {
      title: '处理人',
      dataIndex: 'handlerDesc',
      key: 'handlerDesc',
      render: (_, { handleContent }) => {
        if (handleContent) {
          return (
            <Typography.Paragraph
              style={{ marginBottom: 0, width: '100%' }}
              ellipsis={{ rows: 3, tooltip: true }}
            >
              {handleContent}
            </Typography.Paragraph>
          );
        }
        return '--';
      },
    },
    {
      title: '处理内容',
      dataIndex: 'handleContent',
      key: 'handleContent',
      width: '45%',
      render: (_, { handleContent }) => {
        if (handleContent) {
          return (
            <Typography.Paragraph
              style={{ marginBottom: 0, width: '100%' }}
              ellipsis={{ rows: 3, tooltip: true }}
            >
              {handleContent}
            </Typography.Paragraph>
          );
        }
        return '--';
      },
    },
    {
      // title: (
      //   <Space align="center" size={4}>
      //     备件更换
      //     <Explanation
      //       style={{ display: 'inline' }}
      //       iconType="question"
      //       tooltip={{ title: '如发生备件更换，可点击备件明细查看相关出库工单' }}
      //     />
      //   </Space>
      // ),
      title: '备件说明',
      dataIndex: 'spare',
      key: 'spare',
      width: '25%',
      render: (_, { spareInfo }) => {
        if (spareInfo) {
          if (spareInfo.specific) {
            return (
              <Typography.Paragraph style={{ width: '100%' }} ellipsis={{ rows: 3, tooltip: true }}>
                {spareInfo.specific}
              </Typography.Paragraph>
            );
          }
          // return (
          //   <PopoverEllipsis
          //     width={280}
          //     targets={
          //       spareInfo.deviceModelList?.map(item => (
          //         <Link
          //           key={item.deviceType}
          //           target="_blank"
          //           to={generateTicketUrl({ ticketType: 'warehouse', id: wareHouseNo! })}
          //         >
          //           {item.warehouseCount}*<DeviceTypeText code={item.deviceType} />
          //           {`(${item.vendor}·${item.productModel})`}
          //         </Link>
          //       )) ?? [<></>]
          //     }
          //   />
          // );
        }
        return '无';
      },
    },
    {
      title: '附件/图片',
      dataIndex: 'operation',
      key: 'operation',
      width: 110,
      render: (_, { fileInfoList, recordId }) => {
        if (
          isPhaseOperatable(parent.phase.value, eventProcessEngineNodes) &&
          ((judgePhaseOperator(parent.phase.value, eventProcessEngineConfig).optAuth ===
            EventProcessPhaseOperator.Owner &&
            isOwner) ||
            (judgePhaseOperator(parent.phase.value, eventProcessEngineConfig).optAuth ===
              EventProcessPhaseOperator.UserUnderBlock &&
              userResourcesData?.userResources &&
              userResourcesData?.userResources?.some(item => blockGuidList.includes(item.code))))
        ) {
          return (
            <Space>
              {fileInfoList?.length ? (
                <SimpleFileList
                  files={(fileInfoList ?? []).map(file =>
                    McUploadFile.fromApiObject(file).toJSON()
                  )}
                >
                  <Button type="link" compact>
                    查看
                  </Button>
                </SimpleFileList>
              ) : eventStatus === EventSpecificProcessStatus.Close ||
                eventStatus === EventSpecificProcessStatus.Review ? (
                '--'
              ) : (
                ''
              )}
              {eventStatus !== EventSpecificProcessStatus.Close &&
                eventStatus !== EventSpecificProcessStatus.Review && (
                  <UploadProcessRecordFileModal
                    fileInfoList={fileInfoList}
                    recordId={recordId}
                    onSuccess={onSuccess}
                  />
                )}
            </Space>
          );
        }
        return fileInfoList?.length ? (
          <SimpleFileList
            files={(fileInfoList ?? []).map(file => McUploadFile.fromApiObject(file).toJSON())}
          >
            <Button type="link" compact>
              查看
            </Button>
          </SimpleFileList>
        ) : (
          '--'
        );
      },
    },
  ];

  const dataSource = useMemo(() => {
    switch (parent.key) {
      case EventSpecificProcessStatus.Emergency:
        return originDatasource.filter(
          item => item.eventPhase === EventSpecificProcessStatus.Emergency
        );
      case EventSpecificProcessStatus.Fix:
        return originDatasource.filter(item => item.eventPhase === EventSpecificProcessStatus.Fix);
      case EventSpecificProcessStatus.Recovery:
        return originDatasource.filter(
          item => item.eventPhase === EventSpecificProcessStatus.Recovery
        );
      default:
        return [];
    }
  }, [originDatasource, parent.key]);

  return dataSource.length > 0 ? (
    <Table columns={subColumns} dataSource={dataSource} pagination={false} />
  ) : null;
}

type EventDetectForm = {
  detectUserId: { value: number };
  detectTime: Moment;
  detectReason: string;
};
type EventLocationModalButtonProps = {
  mode: 'edit' | 'new' | 'finished';
  eventId: number;
  blockGuid?: string[];
  initialValues?: EventDetectForm;
  featureSpecialInfoSort: boolean;
  detectReason?: string | null;
  eventNo?: string | null;
  onSuccess?: () => void;
};

function getStatusColorMap(phaseStatus: EventSpecificProcessPhaseStatus) {
  switch (phaseStatus) {
    case EventSpecificProcessPhaseStatus.Inprogress:
      return 'processing';
    case EventSpecificProcessPhaseStatus.Completed:
      return 'success';
    case EventSpecificProcessPhaseStatus.Skipped:
      return '';
    case EventSpecificProcessPhaseStatus.Pending:
      return 'warning';
  }
}

function getNextPhase(phase: EventSpecificProcessStatus) {
  switch (phase) {
    case EventSpecificProcessStatus.Emergency:
      return EventSpecificProcessStatus.Fix;
    case EventSpecificProcessStatus.Fix:
      return EventSpecificProcessStatus.Recovery;
    case EventSpecificProcessStatus.Recovery:
      return EventSpecificProcessStatus.Finished;
    default:
      return EventSpecificProcessStatus.Fix;
  }
}

function isPhaseOperatable(
  currentPhase: EventSpecificProcessStatus,
  eventPhase: DrillOrderApprovalNode[]
) {
  const purePhases = eventPhase.map(item => item.code);

  return purePhases.includes(currentPhase);
}

const judgePhaseOperator = (
  phase: EventSpecificProcessStatus,
  eventProcessEngineConfig?: ProcessEngineConfig
) => {
  switch (phase) {
    case EventSpecificProcessStatus.Emergency:
      return {
        canSkip: eventProcessEngineConfig?.emergency?.canSkip,
        optAuth: eventProcessEngineConfig?.emergency?.optAuth,
        condition: eventProcessEngineConfig?.emergency?.condition,
      };
    case EventSpecificProcessStatus.Fix:
      return {
        canSkip: eventProcessEngineConfig?.repair?.canSkip,
        optAuth: eventProcessEngineConfig?.repair?.optAuth,
        condition: eventProcessEngineConfig?.repair?.condition,
      };
    case EventSpecificProcessStatus.Recovery:
      return {
        canSkip: eventProcessEngineConfig?.recovery?.canSkip,
        optAuth: eventProcessEngineConfig?.recovery?.optAuth,
        condition: eventProcessEngineConfig?.recovery?.condition,
      };
    default:
      return {
        canSkip: false,
        optAuth: EventProcessPhaseOperator.Owner,
        condition: EventProcessPhaseConfigCondition.None,
      };
  }
};

export function EventLocationModalButton({
  mode,
  eventId,
  blockGuid,
  initialValues,
  featureSpecialInfoSort,
  detectReason,
  eventNo,
  onSuccess,
}: EventLocationModalButtonProps) {
  const isEditMode = mode === 'edit';
  const dispatch = useDispatch();

  const [locateEvent, { loading }] = useLocateEvent({
    onCompleted(data) {
      if (data.locateEvent?.success) {
        message.success(featureSpecialInfoSort ? '根因定位成功' : '事件定位成功');
        if (mode === 'finished' && eventNo) {
          finishedEvent({ variables: { query: { eventId: eventNo } } });
          return;
        }
        closeModal();
        dispatch(getEventDetailAction(eventId));
      } else {
        message.error(data.locateEvent?.message);
      }
    },
  });
  const [finishedEvent, { loading: finishedLoading }] = useFinishedCurrentEvent({
    onCompleted(data) {
      if (data.finishedCurrentEvent?.success) {
        message.success('事件结单成功');
        closeModal();
        onSuccess && onSuccess();
      } else {
        message.error(data.finishedCurrentEvent?.message);
      }
    },
  });
  const [form] = Form.useForm<EventDetectForm>();
  const [open, setOpen] = useState(false);
  const showModal = () => {
    setOpen(true);
  };
  const closeModal = () => {
    setOpen(false);
  };
  useEffect(() => {
    if (isEditMode && initialValues && open) {
      form.setFieldsValue({ ...initialValues });
    }
  }, [form, initialValues, isEditMode, open]);
  return (
    <>
      {mode === 'finished' ? (
        <Button
          type="primary"
          loading={finishedLoading}
          onClick={() => {
            if (!detectReason) {
              showModal();
              return;
            }
            if (eventNo) {
              finishedEvent({ variables: { query: { eventId: eventNo } } });
            }
          }}
        >
          事件结单
        </Button>
      ) : (
        <Button
          type={isEditMode ? 'link' : 'primary'}
          onClick={() => {
            showModal();
          }}
        >
          {isEditMode ? '编辑' : featureSpecialInfoSort ? '根因定位' : '事件定位'}
        </Button>
      )}
      <Modal
        destroyOnClose
        title={featureSpecialInfoSort ? '根因定位' : '事件定位'}
        width={560}
        open={open}
        okButtonProps={{ loading: loading || finishedLoading }}
        okText="提交"
        onCancel={() => {
          closeModal();
        }}
        onOk={() => {
          form.validateFields().then(values => {
            const { detectReason, detectTime, detectUserId } = values;
            locateEvent({
              variables: {
                eventId,
                detectReason,
                detectTime: detectTime.valueOf(),
                detectUserId: detectUserId.value,
              },
            });
          });
        }}
      >
        <Form form={form} preserve={false} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          {mode === 'finished' && !detectReason && (
            <Alert
              message="请填写下方信息完成根因定位，再操作事件结单"
              showIcon
              type="warning"
              style={{ marginBottom: 24 }}
            />
          )}

          <Form.Item
            label="定位人"
            name="detectUserId"
            rules={[{ required: true, message: '定位人必选' }]}
          >
            <UserSelect
              style={{ width: 216 }}
              resourceParams={[
                {
                  resourceType: 'BUILDING',
                  resourceCodes: blockGuid ?? [],
                },
              ]}
              userState="in-service"
              allowClear
            />
          </Form.Item>
          <Form.Item
            label="定位时间"
            name="detectTime"
            rules={[
              { required: true, message: '定位时间必选' },
              () => ({
                validator(_, value) {
                  if (value) {
                    if (moment().diff(moment(value), 'seconds') < 0) {
                      return Promise.reject(new Error('定位时间不可晚于当前时间'));
                    }
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            <DatePicker
              style={{ width: 216 }}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
              disabledDate={current => current && current > moment().endOf('day')}
              disabledTime={disabledDateTime}
            />
          </Form.Item>
          <Form.Item
            label={featureSpecialInfoSort ? '根因定位' : '定位原因'}
            name="detectReason"
            rules={[
              {
                required: true,
                message: featureSpecialInfoSort ? '根因定位必填' : '定位原因必填',
                whitespace: true,
              },
            ]}
          >
            <Input.TextArea style={{ width: 428 }} maxLength={300} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

const range = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
export function disabledDateTime(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const compareMoment = moment();
  const hours = compareMoment.hours();
  const minute = compareMoment.minute();
  const second = compareMoment.second();
  if (current) {
    const choseHour = current.hours();
    const choseMinute = current.minute();
    const isToday = current.isSame(moment(), 'day');
    if (isToday) {
      if (choseHour === hours) {
        return {
          disabledHours: () => range(hours + 1, 24),
          disabledMinutes: () => range(minute + 1, 60),
          disabledSeconds: () => (choseMinute === minute ? range(second, 60) : []),
        };
      }
      return {
        disabledHours: () => range(hours + 1, 24),
        disabledMinutes: () => [],
        disabledSeconds: () => [],
      };
    }
  }

  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}
