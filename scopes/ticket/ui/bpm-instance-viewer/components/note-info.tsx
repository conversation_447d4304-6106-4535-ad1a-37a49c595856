import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

export function NoteInfo({ info, style = {} }: { info: string; style?: Record<string, any> }) {
  return (
    <Typography.Paragraph
      ellipsis={{ rows: 2, expandable: true }}
      style={{ color: 'rgba(0,0,0,0.65)', marginBottom: 8, ...style }}
    >
      {info}
    </Typography.Paragraph>
  );
}

export function StepTitle({ title, style }: Record<string, any>) {
  return (
    <div
      style={{
        fontSize: '16px',
        color: 'var(--heading-color)',
        ...style,
      }}
    >
      {title}
    </div>
  );
}
