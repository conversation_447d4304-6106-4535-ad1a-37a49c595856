import dayjs from 'dayjs';
import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import type { ProcessUser, TaskRedirectInfo } from '@manyun/bpm.model.bpm-instance';
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

import { UserInfoWithContainer } from './user-info-with-container.js';

interface IAlreadyOperateUserParams {
  alreadyOperateUsers: {
    userData: ProcessUser & {
      date: string;
      taskResult: string | null;
      remark: string | null;
      taskFileInfoList: BackendMcUploadFile[];
    };
    type: string;
    redirectAlreadyUsers: TaskRedirectInfo[];
  }[];
  applyUser: number;
}

const DEFAULT_MAX_TIME_VALUE = 9999999999999;

export function AlreadyOperateUser({ alreadyOperateUsers, applyUser }: IAlreadyOperateUserParams) {
  const sortedByDateAlreadyOperateUsers = alreadyOperateUsers.sort((pre, pro) => {
    let preNumber = 0;
    let proNumber = 0;
    preNumber = dayjs(pre.userData.date).valueOf();
    proNumber = dayjs(pro.userData.date).valueOf();
    if (pre.type === 'AwaitExecute') {
      preNumber = pre.userData.date === 'MAX' ? DEFAULT_MAX_TIME_VALUE : Number(pre.userData.date);
    }
    if (pro.type === 'AwaitExecute') {
      proNumber = pro.userData.date === 'MAX' ? DEFAULT_MAX_TIME_VALUE : Number(pre.userData.date);
    }

    return preNumber - proNumber;
  });

  const sortedRedirectRecords = sortedByDateAlreadyOperateUsers.filter(
    item => item.type === 'AlreadyExecute'
  );

  return sortedRedirectRecords.length ? (
    <Space direction="vertical" style={{ display: 'flex', width: '100%', marginTop: 8 }}>
      {sortedRedirectRecords.map((operateUser, index: number) => {
        const { userData, redirectAlreadyUsers, type } = operateUser;

        return (
          <Space
            key={`${userData.id}${userData.date}`}
            direction="vertical"
            style={{ width: '100%' }}
          >
            {type === 'AlreadyExecute' ? (
              <UserInfoWithContainer
                key={`${userData.id}${userData.date}`}
                userData={userData}
                applyUser={applyUser}
              />
            ) : null}
          </Space>
        );
      })}
    </Space>
  ) : null;
}
