import dayjs from 'dayjs';
import React from 'react';

import { Container } from '@manyun/base-ui.ui.container';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { TaskRedirectInfo } from '@manyun/bpm.model.bpm-instance';

import styles from '../bpm-instance-viewer.module.less';
import { generateUserStatusInAlert, getStatusTag } from '../utils.js';
import { NoteInfo } from './note-info.js';
import { UserAvatar } from './user-avatar';
import { UserLink } from './user-link.js';

interface RedirectAlertProps {
  index: number;
  redirectInfo: TaskRedirectInfo;
  applyUser: number;
}

export function RedirectAlert({ index, redirectInfo, applyUser }: RedirectAlertProps) {
  return (
    <Container key={index} style={{ width: '100%', padding: 8 }} color="default">
      <Space direction="vertical" style={{ display: 'flex', width: '100%', fontSize: 12 }} size={6}>
        <div
          style={{
            width: '100%',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
          }}
        >
          <Space>
            <div className={styles.avatar}>
              <UserAvatar
                user={{
                  taskResult: '转交',
                  isStep: false,
                  isCollapseAvatar: true,
                  userAvatarId: redirectInfo.sourceUserId,
                  whetherEnable: true,
                  applyUser,
                }}
              />
            </div>
            <UserLink
              style={{ fontSize: 14 }}
              userId={redirectInfo.sourceUserId}
              external
              styleBlack={true}
            />
            <Typography.Text type="secondary">
              {generateUserStatusInAlert('转交')
                ? getStatusTag(generateUserStatusInAlert('转交'), false, 12)
                : ''}
            </Typography.Text>
          </Space>
          <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
            {redirectInfo.redirectTime && dayjs(redirectInfo.redirectTime).format('MM.DD HH:mm')}
          </Typography.Text>
        </div>
        <Typography.Text type="secondary">
          已转交给
          <UserLink userId={redirectInfo.targetUserId} external styleBlack={true} />
        </Typography.Text>
        {redirectInfo?.desc ? <NoteInfo info={redirectInfo?.desc}></NoteInfo> : null}
      </Space>
    </Container>
  );
}
