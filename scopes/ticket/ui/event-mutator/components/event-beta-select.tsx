import { debounce } from 'lodash';
import React, { useCallback, useEffect, useState } from 'react';

// 引入 lodash 的 debounce 方法
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { DefaultOptionType, SelectProps } from '@manyun/base-ui.ui.select';
import { useLazyEventBetaListByKeyword } from '@manyun/ticket.gql.client.tickets';
import { EventBetaProcessStatus } from '@manyun/ticket.model.event';

export type EventSelectProps = {
  blockGuid?: string;
  onChange?: (value: string[]) => void;
  value?: string;
  relatedEvents?: string[];
} & SelectProps;

export function EventSelect({ blockGuid, value, relatedEvents, ...resp }: EventSelectProps) {
  const [eventOptions, setEventOptions] = useState<DefaultOptionType[]>([]);
  const [loading, setLoading] = useState(false);

  const [fetchEvents] = useLazyEventBetaListByKeyword({
    onError(error) {
      message.error(error.message);
    },
  });

  // 根据位置动态加载事件
  const loadEvents = async (searchKeyword?: string | null) => {
    if (!blockGuid) {
      return;
    }

    setLoading(true);
    try {
      const [idcTag, blockTag] = blockGuid.split('.');

      const { data } = await fetchEvents({
        variables: {
          query: {
            pageNum: 1,
            pageSize: 1000,
            eventStatusList: [
              EventBetaProcessStatus.Confirming,
              EventBetaProcessStatus.Finishing,
              EventBetaProcessStatus.Relieving,
              EventBetaProcessStatus.Reviewing,
            ], // 查询未关闭的事件
            blockGuid: blockTag ?? undefined,
            idcTag: idcTag ?? undefined,
            keyword: searchKeyword || undefined,
            filterConvertProblem: true,
          },
        },
      });
      setEventOptions(
        (data?.eventBetaListByKeyword.data ?? []).map(event => ({
          label: `${event.eventId} ${event.eventTitle}`,
          value: event.eventId,
          disabled: (relatedEvents ?? []).some(item => item === event.eventId),
        }))
      );
    } catch (error) {
      message.error('加载事件失败');
    } finally {
      setLoading(false);
    }
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedLoadEvents = useCallback(
    debounce((searchKeyword?: string | null) => loadEvents(searchKeyword), 300),
    [blockGuid]
  );

  // 初始化加载事件
  useEffect(() => {
    debouncedLoadEvents();
    // 清理防抖函数
    return () => debouncedLoadEvents.cancel();
  }, [blockGuid, debouncedLoadEvents]);

  return (
    <Select
      showSearch
      placeholder="搜索事件标题或ID"
      options={eventOptions}
      disabled={loading || !blockGuid}
      loading={loading}
      filterOption={false}
      //   style={{ width: 300 }}
      onSearch={debouncedLoadEvents} // 使用防抖后的方法
      {...resp}
    />
  );
}
