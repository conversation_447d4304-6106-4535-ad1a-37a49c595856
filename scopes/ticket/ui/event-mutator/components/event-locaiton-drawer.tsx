import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
// import { useLazyRiskRegister } from '@manyun/ticket.gql.client.risk-register';
import type { EventLocationInfo } from '@manyun/ticket.model.event';
import { fetchEventDetail } from '@manyun/ticket.service.fetch-event-detail';

import { AddDevice } from './add-device';
import { BlockTable } from './block-table';
import { RoomTable } from './room-table';

export type EventLocationDrawerProps = {
  eventId: string;
};

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const EventLocationDrawer = React.forwardRef(
  ({ eventId }: EventLocationDrawerProps, ref: React.Ref<RefSelectProps>) => {
    const [dataSource, setDataSource] = useState<EventLocationInfo[]>([]);
    // const dataSource = data?.riskRegister?.locationList ?? [];
    const [defaultTabActiveKey, setDefaultTabActiveKey] = useState('block');
    const [visible, setVisible] = useState<boolean>(false);
    useEffect(() => {
      if (visible && eventId) {
        getLocationList();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible, eventId]);

    const getLocationList = async () => {
      const { data } = await fetchEventDetail(eventId);
      if (data) {
        setDataSource(data?.locationList ?? []);
      }
    };
    const blockData = dataSource.filter(item => item.locationType === 'BLOCK') ?? [];
    const roomData = dataSource.filter(item => item.locationType === 'ROOM') ?? [];
    const deviceData = dataSource.filter(item => item.locationType === 'DEVICE') ?? [];

    useEffect(() => {
      if (!blockData.length && roomData.length) {
        setDefaultTabActiveKey('room');
      }
      if (!blockData.length && !roomData.length && deviceData.length) {
        setDefaultTabActiveKey('device');
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [dataSource]);

    return (
      <>
        <Button type="link" compact onClick={() => setVisible(true)}>
          查看
        </Button>
        <Drawer
          style={{ zIndex: 1100 }}
          width={1280}
          title="查看故障位置"
          placement="right"
          open={visible}
          extra={
            <Space>
              <Button
                onClick={() => {
                  setVisible(false);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  setVisible(false);
                }}
              >
                确定
              </Button>
            </Space>
          }
          onClose={() => {
            setVisible(false);
          }}
        >
          <Tabs
            style={{ width: '100%' }}
            defaultActiveKey={defaultTabActiveKey}
            activeKey={defaultTabActiveKey}
            onChange={setDefaultTabActiveKey}
          >
            <Tabs.TabPane
              key="block"
              tab={`楼栋${blockData.length ? '(' + blockData.length + ')' : ''}`}
            >
              <BlockTable dataSource={blockData} mode="view" />
            </Tabs.TabPane>
            <Tabs.TabPane
              key="room"
              tab={`包间${roomData.length ? '(' + roomData.length + ')' : ''}`}
            >
              <RoomTable dataSource={roomData} mode="view" />
            </Tabs.TabPane>
            <Tabs.TabPane
              key="device"
              tab={`设备${deviceData.length ? '(' + deviceData.length + ')' : ''}`}
            >
              <AddDevice dataSource={deviceData} mode="view" />
            </Tabs.TabPane>
          </Tabs>
        </Drawer>
      </>
    );
  }
);

EventLocationDrawer.displayName = 'EventLocationDrawer';
