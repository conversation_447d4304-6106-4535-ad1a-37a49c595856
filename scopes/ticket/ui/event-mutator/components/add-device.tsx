import React, { useState } from 'react';

import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import type { RiskLocationInfo } from '@manyun/ticket.model.risk-register';

import { DeviceSelect } from './device-select';
import { DeviceTable } from './device-table';

export type ChangeSourceProps = {
  idcTag?: string;
  blockGuids?: string[];
  onChange?: (value: RiskLocationInfo[]) => void;
  dataSource: RiskLocationInfo[];
  mode?: 'new' | 'view';
};

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const AddDevice = React.forwardRef(
  (
    { idcTag, blockGuids, dataSource, mode, onChange }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [visible, setVisible] = useState<boolean>(false);

    return (
      <Space style={{ width: '100%' }} direction="vertical">
        <DeviceSelect
          dataSource={dataSource}
          visible={visible}
          idcTag={idcTag ?? ''}
          mode={mode}
          blockGuids={blockGuids}
          onChange={onChange}
          onCancel={() => {
            setVisible(false);
          }}
        />
        <DeviceTable
          dataSource={dataSource}
          idcTag={idcTag}
          mode={mode}
          showAdd={mode === 'new'}
          setVisible={() => setVisible(true)}
          onChange={devices => onChange && onChange(devices)}
        />
      </Space>
    );
  }
);

AddDevice.displayName = 'AddDevice';
