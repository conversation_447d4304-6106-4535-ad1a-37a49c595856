import sortBy from 'lodash.sortby';
import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import type { RiskLocationInfo } from '@manyun/ticket.model.risk-register';

import styles from '../event-mutator.module.less';

export type ChangeSourceProps = {
  onChange?: (value: RiskLocationInfo[]) => void;
  dataSource: RiskLocationInfo[];
  mode?: 'new' | 'view';
  idcTag?: string | null;
  showSearch?: boolean;
  showAdd?: boolean;
  setVisible?: () => void;
};

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const DeviceTable = React.forwardRef(
  (
    {
      dataSource,
      mode,
      idcTag,
      showSearch = true,
      showAdd = false,
      setVisible,
      onChange,
    }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [deviceTypes, setDeviceTypes] = useState<string[]>([]);

    const [selectedCodes, setSelectedCodes] = useState<React.Key[]>([]);
    const [searchDeviceName, setSearchDeviceName] = useState<string>(''); //搜索设备编号

    const onDelete = (selectedCodes: string[]) => {
      onChange && onChange(dataSource.filter(item => !selectedCodes.includes(item.guid)));
      setSelectedCodes([]);
    };

    const isNew = mode === 'new';
    const deviceDataList = dataSource.filter(item => {
      let isReturn: boolean = true;
      if (searchDeviceName) {
        isReturn = !!item.name?.includes(searchDeviceName);
      }
      if (deviceTypes.length && item.subType) {
        isReturn = isReturn && deviceTypes?.includes(item.subType);
      }
      return isReturn;
    });

    const deviceTypeTag = dataSource.reduce((map: Record<string, number>, key) => {
      if (key.subType) {
        if (map[key.subType] !== undefined) {
          map[key.subType] += 1;
        } else {
          map[key.subType] = 1;
        }
      }
      return map;
    }, {});
    const deviceTypeTags = [];
    for (const key in deviceTypeTag) {
      deviceTypeTags.push({ key, value: deviceTypeTag[key] });
    }
    return (
      <Space style={{ width: '100%' }} direction="vertical">
        {deviceTypeTags.length ? (
          <Space direction="horizontal" style={{ width: '100%' }} wrap>
            {sortBy(deviceTypeTags, function ({ value }) {
              return -value;
            }).map(({ key, value }) => {
              const isSelected = deviceTypes.includes(key);
              return (
                <Tag key={key} color={isSelected ? 'processing' : 'default'}>
                  <div
                    style={{ cursor: 'pointer' }}
                    onClick={() => {
                      if (isSelected) {
                        setDeviceTypes(deviceTypes.filter(i => i !== key));
                      } else {
                        setDeviceTypes([...deviceTypes, key]);
                      }
                    }}
                  >
                    {'  '}
                    <DeviceTypeText code={key} />
                    {`  ${value}  `}
                  </div>
                </Tag>
              );
            })}
          </Space>
        ) : null}
        {(showAdd || showSearch) && (
          <Space>
            {showAdd ? (
              <Button type="primary" disabled={!idcTag} onClick={setVisible}>
                添加故障位置
              </Button>
            ) : null}
            {showSearch ? (
              <Input.Search
                placeholder="搜索设备编号"
                allowClear
                value={searchDeviceName}
                onChange={e => setSearchDeviceName(e.target.value)}
                onSearch={value => {
                  setSearchDeviceName(value);
                }}
              />
            ) : null}
          </Space>
        )}
        {selectedCodes.length > 0 && isNew && (
          <Alert
            message={
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <Space>
                  <Typography.Text>已选择 {selectedCodes.length} 项</Typography.Text>
                  <Button type="link" compact onClick={() => setSelectedCodes([])}>
                    取消选择
                  </Button>
                </Space>
                <Popconfirm
                  zIndex={1111}
                  title="您即将删除选中数据"
                  onConfirm={() => onDelete(selectedCodes as string[])}
                >
                  <Typography.Link>批量删除</Typography.Link>
                </Popconfirm>
              </div>
            }
            type="info"
          />
        )}

        <Table<RiskLocationInfo>
          size="middle"
          rowKey="guid"
          className={styles.tableContainer}
          dataSource={deviceDataList}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '设备编号',
              dataIndex: 'name',
              fixed: 'left',
            },
            {
              title: '设备名称',
              dataIndex: 'deviceLabel',
              // render:(deviceLabel)=><Typography.Text code={true}>{deviceLabel}</Typography.Text>,
            },
            {
              title: '设备类型',
              dataIndex: 'subType',
              render: (subType: string) => <DeviceTypeText code={subType} />,
            },
            {
              title: '所属包间',
              dataIndex: 'fromRoomName',
            },

            {
              title: '所属楼栋',
              dataIndex: 'fromBlockGuid',
              render: (fromBlockGuid: string) =>
                fromBlockGuid ? <SpaceText guid={fromBlockGuid} /> : '--',
            },
            {
              title: '操作',
              dataIndex: 'operation',
              fixed: 'right',
              render: (_: string, { guid }: RiskLocationInfo) => (
                <Button
                  type="link"
                  onClick={() => {
                    onDelete([guid] as string[]);
                  }}
                >
                  移除
                </Button>
              ),
            },
          ].filter(item => {
            if (!isNew && item.dataIndex === 'operation') {
              return false;
            }
            return true;
          })}
          rowSelection={
            isNew
              ? {
                  selectedRowKeys: selectedCodes,
                  onChange: (selectedRowKeys, selectedRows) => {
                    setSelectedCodes(keys => [
                      ...keys.filter(key => !dataSource.find(item => item.guid === key)),
                      ...selectedRowKeys,
                    ]);
                  },
                }
              : undefined
          }
          pagination={{
            total: deviceDataList.length,
          }}
        />
      </Space>
    );
  }
);

DeviceTable.displayName = 'DeviceTable';
