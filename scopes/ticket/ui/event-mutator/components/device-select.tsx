import React, { useCallback, useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import { DeviceTypeTree } from '@manyun/resource-hub.ui.device-type-cascader';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
// import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';
// import { RoomSelect } from '@manyun/resource-hub.ui.room-select';
import type { RiskLocationInfo } from '@manyun/ticket.model.risk-register';

import { DeviceTable } from './device-table';

export type ChangeSourceProps = {
  idcTag?: string;
  blockGuids?: string[];
  // roomTags?: string[];
  onChange?: (value: RiskLocationInfo[]) => void;
  dataSource: RiskLocationInfo[];
  mode?: 'new' | 'view';
  visible: boolean;
  onCancel: () => void;
};

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  deviceLabel?: string;
  roomGuidList?: string[];
  deviceTypeList?: string[];
  roomTag?: string;
  name?: string;
};
export type LabelInvalue = { label: string; value: string; key: string; roomTag?: string | null };

export const DeviceSelect = React.forwardRef(
  (
    {
      idcTag,
      blockGuids,
      dataSource,
      mode,
      visible,
      // roomTags,
      onCancel,
      onChange,
    }: ChangeSourceProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [deviceData, setDeviceData] = useState<RiskLocationInfo[]>([]);
    const [total, setTotal] = useState<number>(0);
    const [viewDevice, setViewDevice] = useState(false);
    const [showDeviceTypeTree, setShowDeviceTypeTree] = useState(true);
    const [filterValue, setFilterValue] = useState<DeviceParams>({
      pageNum: 1,
      pageSize: 10,
    });
    const [selectDevices, setSelectDevices] = useState<RiskLocationInfo[]>([]);
    const [spaceGuid, setSpaceGuid] = useState<{
      idcTag?: string;
      blockTag: string;
      rooms: string[];
    }>({ idcTag, blockTag: '', rooms: [] });
    const [deviceType, setDeviceType] = useState<{
      topCategory: string;
      secondCategory: string;
      deviceTypeList: string[];
    }>({ topCategory: '', secondCategory: '', deviceTypeList: [] });

    useEffect(() => {
      setSpaceGuid({ idcTag, blockTag: '', rooms: [] });
    }, [idcTag]);

    const getDeivceList = useCallback(async () => {
      if (
        (spaceGuid.blockTag || spaceGuid.rooms.length) &&
        (deviceType.topCategory || deviceType.secondCategory || deviceType.deviceTypeList.length)
      ) {
        let spaceGuidList: string[] = [];
        if (spaceGuid.blockTag) {
          spaceGuidList = [`${spaceGuid.idcTag}.${spaceGuid.blockTag}`];
        }
        if (spaceGuid.rooms.length) {
          spaceGuidList = spaceGuid.rooms;
        }
        const { data, error: fetchDeviceListError } = await fetchPageDevices({
          ...filterValue,
          spaceGuidList,
          ...deviceType,
        });
        if (fetchDeviceListError) {
          setDeviceData([]);
          message.error(fetchDeviceListError.message);
          return;
        }
        if (data?.data) {
          setDeviceData(
            data.data.map(item => ({
              locationType: 'DEVICE',
              guid: item.guid,
              name: item.name,
              subType: item.deviceCategory.level3,
              deviceLabel: item.deviceLabel,
              fromBlockGuid: item.spaceGuid.blockGuid,
              fromBlockName: item.spaceGuid.blockTag,
              fromRoomGuid: item.spaceGuid.roomGuid,
              fromRoomName: item.spaceGuid.roomTag,
            }))
          );
          setTotal(data.total);
        } else {
          setDeviceData([]);
        }

        return data.data;
      }
      return [];
    }, [filterValue, spaceGuid, deviceType]);

    useEffect(() => {
      if (visible) {
        getDeivceList();
        setSelectDevices(dataSource);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [visible]);
    useEffect(() => {
      if (!showDeviceTypeTree) {
        setTimeout(() => {
          setShowDeviceTypeTree(true);
        }, 200);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [showDeviceTypeTree]);

    useEffect(() => {
      if (visible) {
        getDeivceList();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [getDeivceList]);

    const onClose = () => {
      onCancel();
      setSpaceGuid({
        idcTag,
        blockTag: '',
        rooms: [],
      });
      setDeviceType({ topCategory: '', secondCategory: '', deviceTypeList: [] });
      setFilterValue({
        pageNum: 1,
        pageSize: 10,
      });
      setViewDevice(false);
    };

    return (
      <Drawer
        width={1280}
        // style={{ zIndex: 1100 }}
        title="选择设备"
        placement="right"
        open={visible}
        destroyOnClose
        extra={
          <Space>
            <Button
              onClick={() => {
                onClose();
              }}
            >
              取消
            </Button>
            <Button
              type="primary"
              onClick={() => {
                onChange && onChange(selectDevices);
                onClose();
                setSelectDevices([]);
              }}
            >
              确定
            </Button>
          </Space>
        }
        onClose={() => {
          onClose();
        }}
      >
        {viewDevice ? (
          <Typography.Link
            style={{ marginBottom: 16, display: 'block' }}
            onClick={() => setViewDevice(false)}
          >
            {'<<'}返回继续选择
          </Typography.Link>
        ) : (
          <Typography.Text style={{ marginBottom: 16, display: 'block' }}>
            已选{selectDevices.length}个设备{' '}
            <Typography.Link onClick={() => setViewDevice(true)}>查看已选项{'>>'}</Typography.Link>
          </Typography.Text>
        )}
        {viewDevice && (
          <DeviceTable
            dataSource={selectDevices}
            idcTag={idcTag}
            mode={mode}
            showSearch={false}
            onChange={setSelectDevices}
          />
        )}

        <Space
          style={{
            width: '100%',
            height: '100%',
            alignItems: 'normal',
            marginTop: 16,
            display: viewDevice ? 'none' : 'flex',
          }}
          direction="horizontal"
        >
          <Space style={{ width: 240, height: '100%', display: 'flex' }} direction="vertical">
            <Typography.Title level={5}>空间列表</Typography.Title>
            <ResourceTree
              authorizedOnly
              onlySearchRoom
              treeMode={['BLOCK', 'ROOM_TYPE', 'ROOM']}
              rootStyle={{ width: 240 }}
              idc={idcTag}
              blocks={blockGuids?.map(item => item.split('.')[1])}
              // spaceGuid={idcTag && blockTag ? `${idcTag}.${blockTag}` : idcTag}
              style={{
                padding: 0,
                minWidth: 240,
                height: '100%',
                width: 240,
              }}
              onSelect={(value, info) => {
                setFilterValue({ pageNum: 1, pageSize: 10 });
                setDeviceData([]);

                if (info.node.type === 'BLOCK') {
                  setSpaceGuid({
                    ...spaceGuid,
                    blockTag: info.node.key.substring(info.node.key.indexOf('.') + 1),
                    rooms: [],
                  });
                }
                if (info.node.type === 'ROOM_TYPE') {
                  setSpaceGuid({
                    ...spaceGuid,
                    blockTag: info.node.parentKey.substring(info.node.parentKey.indexOf('.') + 1),
                    rooms: (info.node.children ?? []).map(item => item.key),
                  });
                }
                if (info.node.type === 'ROOM') {
                  setSpaceGuid({
                    ...spaceGuid,
                    blockTag: info.node.key.substring(
                      info.node.key.indexOf('.') + 1,
                      info.node.key.lastIndexOf('.')
                    ),
                    rooms: [info.node.key],
                  });
                }
              }}
            />
          </Space>
          <Divider type="vertical" style={{ height: '100%' }} />
          {spaceGuid.blockTag || spaceGuid.rooms.length ? (
            <Space
              style={{
                width: 240,
                height: '100%',
                display: 'flex',
                overflowX: 'scroll',
              }}
              direction="vertical"
            >
              <Typography.Title level={5}>设备类型列表</Typography.Title>
              {showDeviceTypeTree && (
                <DeviceTypeTree
                  treeStyle={
                    {
                      // overflowY: 'auto',
                      // width: 230,
                    }
                  }
                  numbered
                  idcTag={idcTag ?? ''}
                  blockTag={spaceGuid.blockTag}
                  roomGuids={spaceGuid.rooms}
                  dataType={['snDevice']}
                  inputProps={{
                    style: { marginBottom: 8 },
                    placeholder: '搜索设备类型',
                  }}
                  onTreeDataChange={data => {
                    if (
                      (deviceType.topCategory ||
                        deviceType.secondCategory ||
                        deviceType.deviceTypeList.length) &&
                      !data.some(
                        item =>
                          item.key === deviceType.topCategory ||
                          item.key === deviceType.secondCategory ||
                          deviceType.deviceTypeList.includes(item.key)
                      )
                    ) {
                      setDeviceType({
                        topCategory: '',
                        secondCategory: '',
                        deviceTypeList: [],
                      });
                      setShowDeviceTypeTree(false);
                      // setDeviceData([]);
                    }
                    // setDeviceTypeData(data);
                  }}
                  onSelect={(value, info) => {
                    setFilterValue({ pageNum: 1, pageSize: 10 });
                    if ((info.node as { type?: string })?.type === 'C0') {
                      setDeviceType({
                        topCategory: (info.node as unknown as { value: string })?.value,
                        secondCategory: '',
                        deviceTypeList: [],
                      });
                    }
                    if ((info.node as { type?: string })?.type === 'C1') {
                      setDeviceType({
                        ...deviceType,
                        topCategory: '',
                        secondCategory: (info.node as unknown as { value: string })?.value,
                        deviceTypeList: [],
                      });
                    }
                    if ((info.node as { type?: string })?.type === 'C2') {
                      setDeviceType({
                        deviceTypeList: [(info.node as unknown as { value: string })?.value],
                        topCategory: '',
                        secondCategory: '',
                      });
                    }
                  }}
                />
              )}
            </Space>
          ) : null}
          {(spaceGuid.blockTag || spaceGuid.rooms.length) &&
          (deviceType.topCategory ||
            deviceType.secondCategory ||
            deviceType.deviceTypeList.length) ? (
            <>
              <Divider type="vertical" style={{ height: '100%' }} />
              <Space
                style={{ width: '100%', height: '100%', display: 'flex' }}
                direction="vertical"
              >
                <Typography.Title level={5}>设备列表</Typography.Title>
                <Space style={{ width: '100%' }} direction="vertical">
                  <Space style={{ width: '100%' }} direction="horizontal">
                    <Input.Search
                      placeholder="设备编号"
                      allowClear
                      value={filterValue.name}
                      onChange={e =>
                        setFilterValue({ ...filterValue, pageNum: 1, name: e.target.value?.trim() })
                      }
                      onSearch={value => {
                        setFilterValue({ ...filterValue, pageNum: 1, name: value });
                      }}
                    />
                    <Input.Search
                      placeholder="设备名称"
                      allowClear
                      value={filterValue.deviceLabel}
                      onChange={e =>
                        setFilterValue({
                          ...filterValue,
                          pageNum: 1,
                          deviceLabel: e.target.value?.trim(),
                        })
                      }
                      onSearch={value => {
                        setFilterValue({ ...filterValue, pageNum: 1, deviceLabel: value });
                      }}
                    />
                    <Input.Search
                      placeholder="包间编号"
                      allowClear
                      value={filterValue.roomTag}
                      onChange={e =>
                        setFilterValue({
                          ...filterValue,
                          pageNum: 1,
                          roomTag: e.target.value ? e.target.value?.trim() : '',
                        })
                      }
                      onSearch={value => {
                        setFilterValue({
                          ...filterValue,
                          pageNum: 1,
                          roomTag: value ? value : '',
                        });
                      }}
                    />
                    {/* <RoomSelect
                      style={{ width: 220, zIndex: 1200 }}
                      showSearch
                      // disabled={location?.length !== 2 && !location}
                      // mode="multiple"
                      blockSelectProps={{
                        authorizedOnly: false,
                        idc: spaceGuid.idcTa,
                        block: spaceGuid.blockTag,
                      }}
                      // blockGuid={`${spaceGuid.idcTag}.${spaceGuid.blockTag}`}
                      allowClear
                      onChange={(c, o) => console.log('c,o', c, o)}
                    /> */}
                    {/* <LocationCascader
                      nodeTypes={['ROOM']}
                      style={{ width: 220, zIndex: 1220 }}
                      showSearch
                      onChange={(c, o) => console.log('c,o', c, o)}
                    /> */}
                  </Space>
                  <Table
                    size="middle"
                    rowKey="guid"
                    dataSource={deviceData}
                    columns={[
                      {
                        title: '设备编号',
                        dataIndex: 'name',
                      },
                      {
                        title: '设备名称',
                        dataIndex: 'deviceLabel',
                      },
                      {
                        title: '所属包间',
                        dataIndex: 'fromRoomName',
                      },
                      {
                        title: '设备类型',
                        dataIndex: 'subType',
                        render: subType => <DeviceTypeText code={subType} />,
                      },
                    ]}
                    rowSelection={{
                      selectedRowKeys: selectDevices.map(item => item.guid),
                      onChange: (_, selectedRows) => {
                        setSelectDevices(rows => [
                          ...rows.filter(row => !deviceData.find(item => item.guid === row.guid)),
                          ...selectedRows,
                        ]);
                      },
                    }}
                    pagination={{
                      total: total,
                      current: filterValue.pageNum,
                      pageSize: filterValue.pageSize,
                      onChange: (pageNum, pageSize) => {
                        setFilterValue({ ...filterValue, pageNum, pageSize });
                      },
                    }}
                  />
                </Space>
              </Space>
            </>
          ) : null}
        </Space>
      </Drawer>
    );
  }
);

DeviceSelect.displayName = 'DeviceSelect';
