import { qs } from '@manyun/base-ui.util.query-string';
import { utilities } from '@manyun/dc-brain.navigation.link';
import type { CHANGE_EXE_WAY_MAP } from '@manyun/ticket.model.change';
import type { MineTicketMenuItemKey, MineTicketSubTabKey } from '@manyun/ticket.model.ticket';

const { generatePath } = utilities;

// 工单列表
export const TICKETS_ROUTE_PATH = '/page/tickets/:type';
export type TicketRouteParams = {
  /** 工单类型 */
  type: string;
  /** 机房编号,idcTag,如：EC06 */
  idc?: string | null;
  /** 楼栋编号,blockGuid,如：EC06.A */
  block?: string | null;
  /** 工单状态集合，多个事件状态以英文逗号分隔 */
  statusList?: string | null;
};
export const generateTicketsRoutePath = (params: TicketRouteParams) => {
  const { type, ...rest } = params;
  return `${generatePath(TICKETS_ROUTE_PATH, { type })}?${qs.stringify(
    { ...rest },
    {
      arrayFormat: 'comma',
    }
  )}`;
};

export type GenerateTicketUrl = {
  ticketType: string;
  id: string;
};

export type ChangeShiftRouteParams = {
  /** 交接班工单ID */
  id: string;
};

export type ChangeShiftEditRouteParams = {
  /** 交接班工单ID */
  id: string;
};

export type ChangeOfflineEditRouteParams = {
  /** 线下变更工单ID */
  id: string;
};

export type EventDetailRouteParams = {
  /** 事件详情ID */
  id: string;
};

export type AccessCardEditRouteParams = {
  /** 门禁卡工单ID */
  id: string;
};

export type InspectionTaskConfigurationDetailRouteParams = {
  /** 巡检计划详情 */
  id: string;
  name: string;
};

export type RiskCheckTaskConfigurationDetailRouteParams = {
  /** 风险检查任务详情 */
  id: string;
  // name: string;
};

export type MatainTaskConfigurationDetailRouteParams = {
  /** 维护计划详情 */
  id: string;
  name: string;
};

export type InventoryTaskConfigurationDetailRouteParams = {
  /** 盘点计划详情 */
  id: string;
  name: string;
};

export type DrillTaskConfigurationDetailRouteParams = {
  /** 演练计划详情 */
  id: string;
  name: string;
};

export type MatainTaskConfigurationMutatorRouteParams = {
  /** 维护计划编辑、复制 */
  id: string;
  mode: 'edit' | 'copy';
};

export type InspectionTaskConfigurationMutatorRouteParams = {
  /** 巡检计划编辑、复制 */
  id: string;
  mode: string;
};

export type InventoryTaskConfigurationMutatorRouteParams = {
  /** 盘点计划编辑、复制 */
  id: string;
  mode: string;
};

export type DrillTaskConfigurationMutatorRouteParams = {
  /** 演练计划编辑、复制 */
  id: string;
  mode: 'edit' | 'copy';
};

export type RiskCheckTaskConfigurationMutatorRouteParams = {
  /** 风险检查任务编辑、复制 */
  id: string;
  mode: string;
};
// 工单详情
export const TICKET_ROUTE_PATH = '/page/tickets/:type/:id';
export const generateTicketUrl = ({ ticketType, id }: GenerateTicketUrl) =>
  TICKET_ROUTE_PATH.replace(':type', ticketType).replace(':id', id);
export type GenerateTicketLocation = {
  id: string;
  idcTag?: string;
  blockTag?: string;
  ticketType: string;
};
export const generateTicketLocation = ({
  id,
  idcTag,
  blockTag,
  ticketType,
}: GenerateTicketLocation) => {
  let search = '?';
  if (idcTag) {
    search += `idcTag=${idcTag}`;
  }
  if (blockTag) {
    search += `&blockTag=${blockTag}`;
  }
  return { pathname: generateTicketUrl({ ticketType, id }), search };
};

// 事件新增
export const EVENT_CENTER_NEW = '/page/event-center/new';
// 事件列表
export const EVENTS_ROUTE_PATH = '/page/event-center/list';
export type EventsRouteParams =
  | {
      /** 事件状态集合，多个事件状态以英文逗号分隔 */
      eventStatus?: string | null;
      /** 机房编号集合，多个机房编号以英文逗号分隔，但目前仅支持单个机房搜索, 机房和楼栋互斥 */
      idcs?: string | null;
      /** 楼栋编号集合，多个楼栋编号以英文逗号分隔，但目前仅支持单个楼栋搜索 */
      blocks?: never;
      /** goc事件处理人 */
      curHandler?: { label: string; value: number };
      /** 【阳高】事件Owner */
      owner?: { label: string; value: number };
    }
  | {
      eventStatus?: string | null;
      idcs?: never;
      blocks?: string | null;
      curHandler?: { label: string; value: number };
      owner?: { label: string; value: number };
    };

export type DrillManageListRouteParams = {
  taskStatusList?: string;
  taskAssignee?: { key: number; value: number; title: string; label: string };
};
export const generateEventsRoutePath = (params: EventsRouteParams = {}) => {
  const encodedParams = {
    ...params,
    // 对curHandlerId进行编码，假设label和value是需要在查询字符串中表示的字段
    curHandler: params?.curHandler
      ? `label=${encodeURIComponent(params.curHandler.label)},value=${encodeURIComponent(
          params.curHandler.value
        )}`
      : undefined,
    owner: params?.owner
      ? `label=${encodeURIComponent(params.owner.label)},value=${encodeURIComponent(
          params.owner.value
        )}`
      : undefined,
  };

  return `${EVENTS_ROUTE_PATH}?${qs.stringify(encodedParams, { arrayFormat: 'comma' })}`;
};

// 演练管理
export const generateDrillManageListPath = (params: DrillManageListRouteParams = {}) => {
  const userInfoString = JSON.stringify(params.taskAssignee);
  const encodedUserInfo = encodeURIComponent(userInfoString);
  const encodedParams = {
    ...params,
    taskAssignee: encodedUserInfo,
  };

  return `${DRILL_LIST_ROUTE_PATH}?${qs.stringify(encodedParams, { arrayFormat: 'comma' })}`;
};

// 事件编辑
export const EVENT_CENTER_EDIT = '/page/event-center/:id/edit';

// 事件详情
export const EVENT_DETAIL_ROUTE_PATH = '/page/event-center/:id';
export const generateEventDetailRoutePath = (params: EventDetailRouteParams) =>
  generatePath(EVENT_DETAIL_ROUTE_PATH, params);

// 任务详情
export const TASK_ROUTE_PATH = '/page/task-center/:id';
export const generateTaskLocation = ({ id, name }: { id: string; name: string }) => {
  return {
    pathname: TASK_ROUTE_PATH.replace(':id', id),
    search: `?name=${name}`,
  };
};

export const TICKERT_CREATE_ROUTE_PATH = '/page/tickets/:type/new';
export const generateTicketCreateLocation = ({
  ticketType,
  variables,
}: {
  ticketType: string;
  variables?: Record<string, string>;
}) => {
  const pathname = TICKERT_CREATE_ROUTE_PATH.replace(':type', ticketType);
  const search = variables ? '?variables=' + JSON.stringify(variables) : '';
  return pathname + search;
};

// 新建任务
export const TASK_CREATE_ROUTE_PATH = '/page/task-center/create';

export const CHANGE_TEMPLATE_NEW = '/page/change/template/new';
export const CHANGE_TEMPLATE_DETAIL = '/page/change/template/detail/:id';
export const CHANGE_TEMPLATE_EDIT = '/page/change/template/edit/:id';
export const CHANGE_TEMPLATE_LIST = '/page/change/template/list';
// 风险检查任务
export const RISK_CHECK_TASK_CREATE_ROUTE_PATH = '/page/risk-check-task/new';
export const RISK_CHECK_TASK_MUTATE_ROUTE_PATH = '/page/risk-check-task/:mode/:id';
export const RISK_CHECK_TASK_DETAIL_ROUTE_PATH = '/page/risk-check-task/detail/:id';
export const RISK_CHECK_TASK_LIST_ROUTE_PATH = '/page/risk-check-task/list';
export const RISK_CHECK_TASK_CREATE_AUTH_CODE = 'page_risk-check-task-create';
export const RISK_CHECK_TASK_UPDATE_AUTH_CODE = 'page_risk-check-task-update';
export const RISK_CHECK_TASK_COPY_AUTH_CODE = 'page_risk-check-task-copy';
export const RISK_CHECK_TASK_DETAIL_AUTH_CODE = 'page_risk-check-task-detail';
export const RISK_CHECK_TASK_LIST_AUTH_CODE = 'page_risk-check-task-list';

// 风险检查工单
export const RISK_CHECK_TICKET_CREATE_ROUTE_PATH = '/page/risk-check-ticket/new';
export const RISK_CHECK_TICKET_DETAIL_ROUTE_PATH = '/page/risk-check-ticket/detail/:id';
export const RISK_CHECK_TICKET_LIST_ROUTE_PATH = '/page/tickets/risk_check';

export const RISK_CHECK_TICKET_CREATE_AUTH_CODE = 'page_risk-check-ticket-create';
export const RISK_CHECK_TICKET_DETAIL_AUTH_CODE = 'page_risk-check-ticket-info';
export const RISK_CHECK_TICKET_LIST_AUTH_CODE = 'page_risk-check-ticket-list';

/** 任务中心 */

/** 演练任务 */
export const DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH = '/page/task-center/drill/list';
export const DRILL_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE = 'page_drill-task-list';

export const CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/drill/new';
export const CREATE_DRILL_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_drill-task-create';

export const UPDATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/drill/edit/:id';
export const UPDATE_DRILL_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_drill-task-update';

export const DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_PATH =
  '/page/task-center/drill/detail/:id/:name';
export const DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE = 'page_drill-task-detail';

export const COPY_DRILL_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/drill/copy/:id';
export const COPY_DRILL_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_drill-task-copy';
export const DRILL_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH = '/page/task-center/drill/:mode/:id';

/** 维护任务 */
export const MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH = '/page/task-center/maintain/list';
export const MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE = 'page_maintenance-task';

export const CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/maintain/new';
export const CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_maintenance-task-new';

export const UPDATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/maintain/edit/:id';
export const UPDATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_maintenance-task-edit';

export const MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_PATH =
  '/page/task-center/maintain/detail/:id/:name';
export const MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE = 'page_maintenance-task-detail';

export const COPY_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/maintain/copy/:id';
export const COPY_MAINTAIN_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_maintenance-task-copy';
export const MAINTAIN_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH =
  '/page/task-center/maintain/:mode/:id';
/** 巡检任务 */
export const INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH = '/page/task-center/inspection/list';
export const INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE = 'page_inspect-task';

export const CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/inspection/new';
export const CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_inspect-task-new';

export const UPDATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH =
  '/page/task-center/inspection/edit/:id';
export const UPDATE_INSPECTION_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_inspect-task-edit';

export const INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_PATH =
  '/page/task-center/inspection/detail/:id/:name';
export const INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE = 'page_inspect-task-detail';

export const COPY_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH =
  '/page/task-center/inspection/copy/:id';
export const COPY_INSPECTION_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_inspect-task-copy';
export const INSPECTION_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH =
  '/page/task-center/inspection/:mode/:id';
/** 盘点任务 */
export const INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH = '/page/task-center/inventory/list';
export const INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE = 'page_inventory-task';

export const CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/inventory/new';
export const CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_inventory-task-new';

export const UPDATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH =
  '/page/task-center/inventory/edit/:id';
export const UPDATE_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_inventory-task-edit';
export const INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_PATH =
  '/page/task-center/inventory/detail/:id/:name';
export const INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE = 'page_inventory-task-detail';
export const COPY_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH = '/page/task-center/inventory/copy/:id';
export const COPY_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE = 'page_inventory-task-copy';
export const INVENTORY_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH =
  '/page/task-center/inventory/:mode/:id';

/** 变更列表 */
export const CHANGE_TICKETS_ROUTE_PATH = '/page/change/ticket/list';
export type ChangeTicketsRouteParams = {
  /** 变更状态集合，多个事件状态以英文逗号分隔 */
  status?: string | null;
  /** 机房编号,idcTag,如：EC06 */
  idc?: string | null;
  /** 楼栋编号,blockGuid,如：EC06.A */
  block?: string | null;
  /** 提单人 线下变更时提单人就是操作人 */
  creatorId?: number | null;
  /** 变更方式 */
  exeWay?: CHANGE_EXE_WAY_MAP;
};
export const generateChangeTicketsRoutePath = (params: ChangeTicketsRouteParams = {}) =>
  `${CHANGE_TICKETS_ROUTE_PATH}?${qs.stringify(params, { arrayFormat: 'comma' })}`;
export const CHANGE_TICKET_NEW = '/page/change/ticket/new';
export const CHANGE_TICKET_EDIT = '/page/change/ticket/edit/:id';

export const CHANGE_TICKET_CONFIG = '/page/change/ticket/:id/view';
// 巡检项配置列表
export const INSPECTION_CONFIG_LIST = '/page/inspection-config/list';
// 巡检项新建
export const INSPECTION_CONFIG_CREATE = '/page/inspection-config/create';
// 巡检项详情
export const INSPECTION_CONFIG_DETAIL = '/page/inspection-config/:id/detail';
export type InspectionConfigDetailParams = {
  id: string;
  name?: string;
};
export const generateInspectionConfigDetailRoutePath = ({
  id,
  name,
}: InspectionConfigDetailParams) => {
  if (name) {
    return `${generatePath(INSPECTION_CONFIG_DETAIL, { id })}?name=${name}`;
  }
  return `${generatePath(INSPECTION_CONFIG_DETAIL, { id })}`;
};

// 巡检项编辑
export const INSPECTION_CONFIG_EDIT = '/page/inspection-config/:id/edit';
// 巡检项复制

export const INSPECTION_CONFIG_COPY_ROUTE_PATH = '/page/inspection-config/:id/copy';
export const INSPECTION_CONFIG_COPY_ROUTE_AUTH_CODE = 'page_inspection-configuration-copy';
// 工单新建
export const TICKET_CREATE = '/page/tickets/:type/new';
// 工单编辑
export const TICKET_EDIT = '/page/tickets/:type/edit/:id';
export const TASK_CENTER_LIST = '/page/task-center/list';
export const TASK_CENTER_DETAIL = '/page/task-center/:id';
export const TASK_CENTER_CREATE = '/page/task-center/create';
export const TASK_CENTER_EDIT = '/page/task-center/:id/edit';
export const TASK_CENTER_SCHEDULE = '/page/task-center/schedule';
export const INVENTORY_CONFIG_LIST = '/page/inventory-config/list';
export const INVENTORY_CONFIG_CREATE = '/page/inventory-config/create';
export const INVENTORY_CONFIG_COPY_ROUTE_PATH = '/page/inventory-config/:id/copy';
export const INVENTORY_CONFIG_COPY_ROUTE_AUTH_CODE = 'page_inventory-configuration-copy';
export const INVENTORY_CONFIG_EDIT = '/page/inventory-config/:id/edit';
export const INVENTORY_CONFIG_DETAIL = '/page/inventory-config/:id';
// 盘点分析
export const INVENTORY_ANALYTICS_LIST = '/page/tickets/inventory-analytics';
export const OPERATION_CHECK_CONFIG_LIST = '/page/operation-check-config/list';
export const OPERATION_CHECK_CONFIG_DETAIL = '/page/operation-check-config/detail/:id';
export const OPERATION_CHECK_CONFIG_CREATE = '/page/operation-check-config/create';
export const OPERATION_CHECK_CONFIG_EDIT = '/page/operation-check-config/edit/:id';
// 列表
export const MAINTAIN_CONFIG_LIST = '/page/maintain-config/list';
// 新增
export const MAINTAIN_CONFIG_CREATE = '/page/maintain-config/create';
// 详情
export const MAINTAIN_CONFIG_DETAIL = '/page/maintain-config/detail/:id';
export type MaintainConfigDetailParams = {
  id: string;
  name?: string;
};
export const generateMaintainConfigDetailRoutePath = ({ id, name }: MaintainConfigDetailParams) => {
  if (name) {
    return `${generatePath(MAINTAIN_CONFIG_DETAIL, { id })}?name=${name}`;
  }
  return `${generatePath(MAINTAIN_CONFIG_DETAIL, { id })}`;
};

// 编辑
export const MAINTAIN_CONFIG_EDIT = '/page/maintain-config/edit/:id';
export const MAINTAIN_CONFIG_COPY_ROUTE_PATH = '/page/maintain-config/copy/:id';
export const MAINTAIN_CONFIG_COPY_ROUTE_AUTH_CODE = 'page_maintain-configuration-copy';
export type MineTicketsParams = {
  menuKey?: MineTicketMenuItemKey;
  /** 代表子组件tab的key
   * 0: 失败关单
   * 1: 成功关单
   * 2: 已接单
   * 3: 待接单
   * UNFINISHED: 未关单
   * FINISHED: 已关单
   */
  subTabKey?: MineTicketSubTabKey;
};
export const MINE_TICKET = '/page/mine-tickets';

export const generateMineTicketsRoutePath = (params: MineTicketsParams) => {
  return `${MINE_TICKET}?${qs.stringify(params)}`;
};
export const VISITOR_RECORD_NEW = '/page/visitor-manager/new';
export const VISITOR_BLACKLIST = '/page/visitor-blacklist/list';

// 维保订单编辑页面
export const WARRANTY_MANAGE_EDIT = '/page/warranty-manage/:id/edit';
// 维保订单详情页面
export const WARRANTY_MANAGE_DETAIL = '/page/warranty-manage/:id';
// 维保订单列表
export const WARRANTY_MANAGE_LIST = '/page/warranty-manage/list';
// 维保订单新建
export const WARRANTY_MANAGE_NEW = '/page/warranty-manage/new';
//预维保池列表
export const WARRANTY_POOL_LIST = '/page/warranty-pool/list';
// 预到货池列表
export const ARRIVAL_ASSET_LIST = '/page/arrival-asset/list';
// 预到货池新建页
export const ARRIVAL_ASSET_NEW = '/page/arrival-asset/new';
/** 事件工单详情 */
export const EVENT_CENTER_DETAIL = '/page/event-center/:id';
/** 工单详情 */
export const TICKET_VIEW = '/page/tickets/:type/:id';
// 变更详情
export const generateChangeTicketDetail = ({ id }: { id: string }) =>
  CHANGE_TICKET_CONFIG.replace(':id', id);
// 变更模版详情
export const generateChangeTemplateDetail = ({ id }: { id: string }) =>
  CHANGE_TEMPLATE_DETAIL.replace(':id', id);

/** 盘点计划详情 */
export const generateInventoryPlanDetailRoutePath = (
  params: InventoryTaskConfigurationDetailRouteParams
) => generatePath(INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_PATH, params);

/** 巡检计划详情 */
export const generateInspectionPlanDetailRoutePath = (
  params: InspectionTaskConfigurationDetailRouteParams
) => generatePath(INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_PATH, params);
/** 维护计划详情 */
export const generateMaintainPlanDetailRoutePath = (
  params: MatainTaskConfigurationDetailRouteParams
) => generatePath(MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_PATH, params);
/** 演练计划详情 */
export const generateDrillPlanDetailRoutePath = (params: DrillTaskConfigurationDetailRouteParams) =>
  generatePath(DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_PATH, params);

/** 风险检查任务详情 */
export const generateRiskCheckTaskDetailRoutePath = (
  params: RiskCheckTaskConfigurationDetailRouteParams
) => generatePath(RISK_CHECK_TASK_DETAIL_ROUTE_PATH, params);

/** 维护计划复制、编辑 */
export const generateMaintainTaskConfigurationMutatorRoutePath = (
  params: MatainTaskConfigurationMutatorRouteParams
) => generatePath(MAINTAIN_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH, params);

/** 巡检计划复制、编辑 */
export const generateInspectionTaskConfigurationMutatorRoutePath = (
  params: InspectionTaskConfigurationMutatorRouteParams
) => generatePath(INSPECTION_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH, params);

/** 盘点计划复制、编辑 */
export const generateInventoryTaskConfigurationMutatorRoutePath = (
  params: InventoryTaskConfigurationMutatorRouteParams
) => generatePath(INVENTORY_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH, params);

/** 演练计划复制、编辑 */
export const generateDrillTaskConfigurationMutatorRoutePath = (
  params: DrillTaskConfigurationMutatorRouteParams
) => generatePath(DRILL_TASK_CONFIGURATION_MUTATOR_ROUTE_PATH, params);

export const generateRiskCheckTaskConfigurationMutatorRoutePath = (
  params: RiskCheckTaskConfigurationMutatorRouteParams
) => generatePath(RISK_CHECK_TASK_MUTATE_ROUTE_PATH, params);

// 交接班工单
export const CHANGE_SHIFTS = '/page/independent-tickets/change-shifts';

export type ChangeShiftsRouteParams = {
  handoverStartDate?: number;
  handoverEndDate?: number;
  dutyName?: string;
  dutyGroupId?: number;
  handUserId?: number;
  statusList?: string[];
  counterSigners?: number[];
};
export const generateChangeShiftsRoutePath = (params: ChangeShiftsRouteParams) => {
  return `${CHANGE_SHIFTS}?${qs.stringify(params, {
    arrayFormat: 'comma',
  })}`;
};

// 交接班工单详情
export const generateChangeShiftDetail = ({ id }: ChangeShiftRouteParams) =>
  CHANGE_SHIFT.replace(':id', id);
export const CHANGE_SHIFT = '/page/independent-tickets/change-shift/:id/detail';

// 交接班工单新建
export const CHANGE_SHIFT_NEW = '/page/independent-tickets/change-shift/new';

// 交接班工单编辑
export const generateChangeShiftEdit = ({ id }: ChangeShiftEditRouteParams) =>
  CHANGE_SHIFT_EDIT.replace(':id', id);
export const CHANGE_SHIFT_EDIT = '/page/independent-tickets/change-shift/:id/edit';

// 线下变更工单新建
export const CHANGE_OFFLINE_NEW = '/page/independent-tickets/change-online/new';

// 线下变更工单编辑
export const CHANGE_OFFLINE_EDIT_ROUTE_PATH = '/page/independent-tickets/change-offline/:id/edit';
export const generateChangeOfflineEditRoutePath = (params: ChangeOfflineEditRouteParams) =>
  generatePath(CHANGE_OFFLINE_EDIT_ROUTE_PATH, params);

// 维保订单详情页面
export const generateWarrantyOrderDetailUrl = ({ id }: { id: string }) =>
  WARRANTY_MANAGE_DETAIL.replace(':id', id);

//变更编辑
export const generateChangeTicketEdit = ({ id }: { id: string }) =>
  CHANGE_TICKET_EDIT.replace(':id', id);

/**
 * 盘点项配置复制
 */
export const generateInventoryCopyLocation = ({ id, name }: { id: string; name: string }) => ({
  pathname: INVENTORY_CONFIG_COPY_ROUTE_PATH.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 维护项配置复制
 */
export const generateMaintainCopyLocation = ({ id, name }: { id: string; name: string }) => ({
  pathname: MAINTAIN_CONFIG_COPY_ROUTE_PATH.replace(':id', id),
  search: `?name=${name}`,
});

/**
 * 巡检项配置复制
 */
export const generateInspectionCopyLocation = ({ id, name }: { id: string; name: string }) => ({
  pathname: INSPECTION_CONFIG_COPY_ROUTE_PATH.replace(':id', id),
  search: `?name=${name}`,
});

// 盘点项详情
export const INVENROTY_CONFIG_DETAIL = '/page/inventory-config/:id';
export type InventoryConfigDetailParams = {
  id: string;
  name?: string;
};

export const generateInventoryConfigDetailRoutePath = ({
  id,
  name,
}: InventoryConfigDetailParams) => {
  if (name) {
    return `${generatePath(INVENROTY_CONFIG_DETAIL, { id })}?name=${name}`;
  }
  return `${generatePath(INVENROTY_CONFIG_DETAIL, { id })}`;
};

// 风险登记册列表
export const RISK_REGISTERS = '/page/independent-tickets/risk-register';
export const RISK_REGISTERS_NEW = '/page/independent-tickets/risk-register/new';
export const generateRiskRegisterCreateLocation = ({
  eventSourceNo,
  eventSourceType,
  spaceGuid,
}: {
  id?: string;
  eventSourceType?: string;
  eventSourceNo?: string;
  spaceGuid?: string;
}) => {
  const encodedParams = {
    eventSourceNo,
    eventSourceType,
    spaceGuid,
  };
  return `${RISK_REGISTERS_NEW}?${qs.stringify(encodedParams, { arrayFormat: 'comma' })}`;
};
export const RISK_REGISTER = '/page/independent-tickets/risk-register/:id/detail';
export const generateRiskRegisterDetailLocation = ({ id }: { id: string }) =>
  RISK_REGISTER.replace(':id', id);

export const RISK_POOL_NEW__ROUTE_AUTH_CODE = 'page_risk-pool-create';
export const RISK_POOL_ROUTE_AUTH_CODE = 'page_risk-pools';

export const RISK_POOL_NEW_ROUTE_PATH = '/page/independent-tickets/risk-pool/new';
export const RISK_POOL_ROUTE_PATH = '/page/independent-tickets/risk-pool';

export const DRILL_LIST_ROUTE_PATH = '/page/drill-manage/list';
export const DRILL_LIST_ROUTE_AUTH_CODE = 'page_drill-list';

export const CREATE_DRILL_ORDER_ROUTE_PATH = '/page/drill-order/create';
export const CREATE_DRILL_ORDER_ROUTE_AUTH_CODE = 'page_drill-order-create';

export const COPY_DRILL_ORDER_ROUTE_PATH = '/page/drill-order/copy/:id';
export const COPY_DRILL_ORDER_ROUTE_AUTH_CODE = 'page_drill-order-copy';
export const generateCopyDrillOrderRoutePath = ({ id }: { id: string }) =>
  generatePath(COPY_DRILL_ORDER_ROUTE_PATH, { id });

export const DRILL_ORDER_ROUTE_PATH = '/page/drill-order/:id';
export const DRILL_ORDER_ROUTE_AUTH_CODE = 'page_drill-order-detail';

export const generateDrillOrderRoutePath = ({ id }: { id: string }) =>
  generatePath(DRILL_ORDER_ROUTE_PATH, { id });
export const DRILL_CONFIG_LIST_ROUTE_PATH = '/page/drill-config/list';
export const DRILL_CONFIG_LIST_ROUTE_AUTH_CODE = 'page_drill-configurations';
export const DRILL_CONFIG_CREATE_ROUTE_PATH = '/page/drill-config/create';
export const DRILL_CONFIG_CREATE_ROUTE_AUTH_CODE = 'page_drill-configuration-create';
export const DRILL_CONFIG_COPY_ROUTE_PATH = '/page/drill-config/:id/copy';
export const DRILL_CONFIG_COPY_ROUTE_AUTH_CODE = 'page_drill-configuration-copy';
export const DRILL_CONFIG_EDIT_ROUTE_PATH = '/page/drill-config/:id/edit';
export const DRILL_CONFIG_EDIT_ROUTE_AUTH_CODE = 'page_drill-configuration-edit';
export const DRILL_CONFIG_DETAIL_ROUTE_PATH = '/page/drill-config/:id';
export const DRILL_CONFIG_DETAIL_ROUTE_AUTH_CODE = 'page_drill-configuration';

export const generateDrillConfigCopyLocation = (params: { id: string; name?: string }) =>
  generatePath(DRILL_CONFIG_COPY_ROUTE_PATH, params);

export const generateDrillConfigEditLocation = (params: { id: string; name?: string }) =>
  generatePath(DRILL_CONFIG_EDIT_ROUTE_PATH, params);

export const generateDrillConfigDetailLocation = ({ id, name }: { id: string; name?: string }) =>
  `${generatePath(DRILL_CONFIG_DETAIL_ROUTE_PATH, { id })}?name=${name ?? ''}`;

export const STANDARD_CHANGE_LIBRARIES_ROUTE_AUTH_CODE = 'page_ticket_standard-change-library-list';
export const STANDARD_CHANGE_LIBRARIES_ROUTE_PATH =
  '/page/independent-tickets/standard-change-library/list';

//  标准变更库详情
export const STANDARD_CHANGE_LIBRARIE_ROUTE_AUTH_CODE =
  'page_ticket_standard-change-library-detail';
export const STANDARD_CHANGE_LIBRARY_ROUTE_PATH =
  '/page/independent-tickets/standard-change-library/:id/detail';
export const generateStandardChangeLibraryDetailLocation = ({ id }: { id: string }) =>
  STANDARD_CHANGE_LIBRARY_ROUTE_PATH.replace(':id', id);

//  标准变更库新建
export const STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_AUTH_CODE =
  'page_ticket_standard-change-library-create';
export const STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH =
  '/page/independent-tickets/standard-change-library/create';

//  标准变更库编辑
export const STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_AUTH_CODE =
  'page_ticket_standard-change-library-edit';
export const STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_PATH =
  '/page/independent-tickets/standard-change-library/:id/edit';
export const generateStandardChangeLibraryEditLocation = ({ id }: { id: string }) =>
  STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_PATH.replace(':id', id);

//  线下变更 内测版 新建
export const CHANGE_OFFLIEN_CREATE_ROUTE_AUTH_CODE = 'page_ticket_change-offline-create';
export const CHANGE_OFFLIEN_CREATE_ROUTE_PATH = '/page/independent-tickets/change-offline/create';

//   线下变更 内测版 编辑
export const CHANGE_OFFLIEN_EDIT_ROUTE_AUTH_CODE = 'page_ticket_change-offline-edit';
export const CHANGE_OFFLIEN_EDIT_ROUTE_PATH =
  '/page/independent-tickets/change-offline-beta/:id/edit';
export const generateChangeOfflineEditLocation = ({ id }: { id: string }) =>
  CHANGE_OFFLIEN_EDIT_ROUTE_PATH.replace(':id', id);

//  线下变更 内测版 列表
export const CHANGE_OFFLIENS_ROUTE_AUTH_CODE = 'page_ticket_change-offline-list';
export const CHANGE_OFFLIENS_ROUTE_PATH = '/page/independent-tickets/change-offlines';

//  线下变更 内测版 详情
export const CHANGE_OFFLIEN_ROUTE_AUTH_CODE = 'page_ticket_change-offline-detail';
export const CHANGE_OFFLIEN_ROUTE_PATH = '/page/independent-tickets/change-offline/:id/detail';
export const generateChangeOfflineLocation = ({ id }: { id: string }) =>
  CHANGE_OFFLIEN_ROUTE_PATH.replace(':id', id);

//  事件管理 内测版 新建
export const EVENT_CREATE_ROUTE_AUTH_CODE = 'page_event-management-create';
export const EVENT_CREATE_ROUTE_PATH = '/page/independent-tickets/enevt/create';

//  事件管理 内测版 列表
export const EVENT_LIST_ROUTE_AUTH_CODE = 'page_event-management-list';
export const EVENT_LIST_ROUTE_PATH = '/page/independent-tickets/enevts';

//  事件管理 内测版 详情
export const EVENT_ROUTE_AUTH_CODE = 'page_event-management-detail';
export const EVENT_ROUTE_PATH = '/page/independent-tickets/enevt/:id/detail';
export const generateEvnetLocation = ({ id }: { id: string }) =>
  EVENT_ROUTE_PATH.replace(':id', id);

// 应急流程  列表
export const EMERGENCY_PROCEE_LIST_ROUTE_AUTH_CODE = 'page_ticket_emergency-process-list';
export const EMERGENCY_PROCEE_LIST_ROUTE_PATH = '/page/independent-tickets/emergency-process/list';

//  应急流程 编辑
export const EMERGENCY_PROCEE_EDIT_ROUTE_AUTH_CODE = 'page_ticket_emergency-process-edit';
export const EMERGENCY_PROCEE_EDIT_ROUTE_PATH =
  '/page/independent-tickets/emergency-process/:id/edit';
export const generateEmergencyProcessEditLocation = ({ id }: { id: string }) =>
  EMERGENCY_PROCEE_EDIT_ROUTE_PATH.replace(':id', id);

// 应急流程 新建
export const EMERGENCY_PROCEE_CREATE_ROUTE_AUTH_CODE = 'page_ticket_emergency-process-create';
export const EMERGENCY_PROCEE_CREATE_ROUTE_PATH =
  '/page/independent-tickets/emergency-process/create';

// 应急流程内测版 详情
export const EMERGENCY_PROCEE_ROUTE_AUTH_CODE = 'page_ticket_emergency-process-detail';
export const EMERGENCY_PROCEE_ROUTE_PATH = '/page/independent-tickets/emergency-process/:id/detail';
export const generateEmergencyProcessLocation = ({ id }: { id: string }) =>
  EMERGENCY_PROCEE_ROUTE_PATH.replace(':id', id);

// 简版变更新建(线上变更)
export const CHANGE_ONLINE_CREATE_ROUTE_AUTH_CODE = 'page_ticket_change-management-create';
export const CHANGE_ONLINE_CREATE_ROUTE_PATH = '/page/independent-tickets/change-online/create';
export const generateChangeOnlineCreateLocation = ({
  id,
  changeSourceNo,
  changeSourceType,
  spaceGuid,
}: {
  id?: string;
  changeSourceType?: string;
  changeSourceNo?: string;
  spaceGuid?: string;
}) => {
  const encodedParams = {
    copyId: id,
    changeSourceNo,
    changeSourceType,
    spaceGuid,
  };
  return `${CHANGE_ONLINE_CREATE_ROUTE_PATH}?${qs.stringify(encodedParams, { arrayFormat: 'comma' })}`;
};

// 变更模版新建
export const CHANGE_TEMPLATE_CREATE_ROUTE_AUTH_CODE = 'page_ticket_change-template-create';
export const CHANGE_TEMPLATE_CREATE_ROUTE_PATH = '/page/independent-tickets/change-template/create';
export const generateChangeTemplateCopyLocation = ({ id }: { id: string }) =>
  `${CHANGE_TEMPLATE_CREATE_ROUTE_PATH}?copyId=${id}`;

// 变更模版详情
export const CHANGE_TEMPLATE_DETAIL_ROUTE_AUTH_CODE = 'page_ticket_change-template-detail';
export const CHANGE_TEMPLATE_DETAIL_ROUTE_PATH =
  '/page/independent-tickets/change-template/:id/detail';
export const generateChangeOnlineTemplateLocation = ({ id }: { id: string }) =>
  CHANGE_TEMPLATE_DETAIL_ROUTE_PATH.replace(':id', id);

// 变更模版列表
export const CHANGE_TEMPLATE_LIST_ROUTE_AUTH_CODE = 'page_ticket_change-template-list';
export const CHANGE_TEMPLATE_LIST_ROUTE_PATH = '/page/independent-tickets/change-template/list';

// 变更模版编辑
export const CHANGE_TEMPLATE_EDIT_ROUTE_AUTH_CODE = 'page_ticket_change-template-detail';
export const CHANGE_TEMPLATE_EDIT_ROUTE_PATH = '/page/independent-tickets/change-template/:id/edit';
export const generateChangeOnlineTemplateEditLocation = ({ id }: { id: string }) =>
  CHANGE_TEMPLATE_EDIT_ROUTE_PATH.replace(':id', id);

// 变更简版列表
export const CHANGE_ONLINE_LIST_ROUTE_AUTH_CODE = 'page_ticket_change-managements';
export const CHANGE_ONLINE_LIST_ROUTE_PATH = '/page/independent-tickets/change-online/list';

// 变更简版编辑
export const CHANGE_ONLINE_EDIT_ROUTE_AUTH_CODE = 'page_ticket_change-management-edit';
export const CHANGE_ONLINE_EDIT_ROUTE_PATH = '/page/independent-tickets/change-online/:id/edit';
export const generateChangeOnlineEditLocation = ({ id }: { id: string }) =>
  CHANGE_ONLINE_EDIT_ROUTE_PATH.replace(':id', id);

// 变更简版详情
export const CHANGE_ONLINE_DETAIL_ROUTE_AUTH_CODE = 'page_ticket_change-management-detail';
export const CHANGE_ONLINE_DETAIL_ROUTE_PATH = '/page/independent-tickets/change-online/:id/detail';
export const generateChangeOnlineDetailLocation = ({
  id,
  currentTab,
}: {
  id: string;
  currentTab?: string;
}) => {
  const encodedParams = {
    currentTab,
  };
  return `${CHANGE_ONLINE_DETAIL_ROUTE_PATH.replace(':id', id)}?${qs.stringify(encodedParams, { arrayFormat: 'comma' })}`;
};

export const TICKET_ROUTE_PATH_AUTH_CODE_MAPPER: Record<string, string> = {
  [MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_PATH]: MAINTAIN_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE,
  [INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_PATH]:
    INSPECTION_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE,
  [INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_PATH]: INVENTORY_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE,

  [CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH]:
    CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [UPDATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH]:
    UPDATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]:
    MAINTAIN_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE,

  [CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH]:
    CREATE_INSPECTION_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [UPDATE_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH]:
    UPDATE_INSPECTION_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]:
    INSPECTION_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE,

  [CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH]:
    CREATE_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [UPDATE_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH]:
    UPDATE_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]:
    INVENTORY_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE,

  [COPY_INVENTORY_TASK_CONFIGURATION_ROUTE_PATH]: COPY_INVENTORY_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [COPY_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH]: COPY_MAINTAIN_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [COPY_INSPECTION_TASK_CONFIGURATION_ROUTE_PATH]:
    COPY_INSPECTION_TASK_CONFIGURATION_ROUTE_AUTH_CODE,

  [INVENTORY_CONFIG_COPY_ROUTE_PATH]: INVENTORY_CONFIG_COPY_ROUTE_AUTH_CODE,
  [MAINTAIN_CONFIG_COPY_ROUTE_PATH]: MAINTAIN_CONFIG_COPY_ROUTE_AUTH_CODE,
  [INSPECTION_CONFIG_COPY_ROUTE_PATH]: INSPECTION_CONFIG_COPY_ROUTE_AUTH_CODE,
  [RISK_POOL_NEW_ROUTE_PATH]: RISK_POOL_NEW__ROUTE_AUTH_CODE,
  [RISK_POOL_ROUTE_PATH]: RISK_POOL_ROUTE_AUTH_CODE,

  [DRILL_TASK_CONFIGURATION_LIST_ROUTE_PATH]: DRILL_TASK_CONFIGURATION_LIST_ROUTE_AUTH_CODE,
  [CREATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH]: CREATE_DRILL_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [UPDATE_DRILL_TASK_CONFIGURATION_ROUTE_PATH]: UPDATE_DRILL_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_PATH]: DRILL_TASK_CONFIGURATION_DETAIL_ROUTE_AUTH_CODE,
  [COPY_DRILL_TASK_CONFIGURATION_ROUTE_PATH]: COPY_DRILL_TASK_CONFIGURATION_ROUTE_AUTH_CODE,
  [DRILL_LIST_ROUTE_PATH]: DRILL_LIST_ROUTE_AUTH_CODE,
  [CREATE_DRILL_ORDER_ROUTE_PATH]: CREATE_DRILL_ORDER_ROUTE_AUTH_CODE,
  [DRILL_ORDER_ROUTE_PATH]: DRILL_ORDER_ROUTE_AUTH_CODE,
  [COPY_DRILL_ORDER_ROUTE_PATH]: COPY_DRILL_ORDER_ROUTE_AUTH_CODE,

  [DRILL_CONFIG_COPY_ROUTE_PATH]: DRILL_CONFIG_COPY_ROUTE_AUTH_CODE,
  [DRILL_CONFIG_CREATE_ROUTE_PATH]: DRILL_CONFIG_CREATE_ROUTE_AUTH_CODE,
  [DRILL_CONFIG_DETAIL_ROUTE_PATH]: DRILL_CONFIG_DETAIL_ROUTE_AUTH_CODE,
  [DRILL_CONFIG_EDIT_ROUTE_PATH]: DRILL_CONFIG_EDIT_ROUTE_AUTH_CODE,
  [DRILL_CONFIG_LIST_ROUTE_PATH]: DRILL_CONFIG_LIST_ROUTE_AUTH_CODE,
  [STANDARD_CHANGE_LIBRARIES_ROUTE_PATH]: STANDARD_CHANGE_LIBRARIES_ROUTE_AUTH_CODE,
  [STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_PATH]: STANDARD_CHANGE_LIBRARY_CREATE_ROUTE_AUTH_CODE,
  [STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_PATH]: STANDARD_CHANGE_LIBRARY_EDIT_ROUTE_AUTH_CODE,
  [CHANGE_OFFLIEN_CREATE_ROUTE_PATH]: CHANGE_OFFLIEN_CREATE_ROUTE_AUTH_CODE,
  [CHANGE_OFFLIEN_EDIT_ROUTE_PATH]: CHANGE_OFFLIEN_EDIT_ROUTE_AUTH_CODE,
  [CHANGE_OFFLIENS_ROUTE_PATH]: CHANGE_OFFLIENS_ROUTE_AUTH_CODE,
  [CHANGE_OFFLIEN_ROUTE_PATH]: CHANGE_OFFLIEN_ROUTE_AUTH_CODE,
  [EVENT_ROUTE_PATH]: EVENT_ROUTE_AUTH_CODE,
  [EVENT_LIST_ROUTE_PATH]: EVENT_LIST_ROUTE_AUTH_CODE,
  [EVENT_CREATE_ROUTE_PATH]: EVENT_CREATE_ROUTE_AUTH_CODE,
  [EMERGENCY_PROCEE_LIST_ROUTE_PATH]: EMERGENCY_PROCEE_LIST_ROUTE_AUTH_CODE,
  [EMERGENCY_PROCEE_EDIT_ROUTE_PATH]: EMERGENCY_PROCEE_EDIT_ROUTE_AUTH_CODE,
  [EMERGENCY_PROCEE_CREATE_ROUTE_PATH]: EMERGENCY_PROCEE_CREATE_ROUTE_AUTH_CODE,
  [EMERGENCY_PROCEE_ROUTE_PATH]: EMERGENCY_PROCEE_ROUTE_AUTH_CODE,
  [CHANGE_ONLINE_CREATE_ROUTE_PATH]: CHANGE_ONLINE_CREATE_ROUTE_AUTH_CODE,
  [CHANGE_TEMPLATE_CREATE_ROUTE_PATH]: CHANGE_TEMPLATE_CREATE_ROUTE_AUTH_CODE,
  [CHANGE_TEMPLATE_DETAIL_ROUTE_PATH]: CHANGE_TEMPLATE_DETAIL_ROUTE_AUTH_CODE,
  [CHANGE_TEMPLATE_LIST_ROUTE_PATH]: CHANGE_TEMPLATE_LIST_ROUTE_AUTH_CODE,
  [CHANGE_TEMPLATE_EDIT_ROUTE_PATH]: CHANGE_TEMPLATE_EDIT_ROUTE_AUTH_CODE,
  [CHANGE_ONLINE_LIST_ROUTE_PATH]: CHANGE_ONLINE_LIST_ROUTE_AUTH_CODE,
  [CHANGE_ONLINE_EDIT_ROUTE_PATH]: CHANGE_ONLINE_EDIT_ROUTE_AUTH_CODE,
  [CHANGE_ONLINE_DETAIL_ROUTE_PATH]: CHANGE_ONLINE_DETAIL_ROUTE_AUTH_CODE,
};
