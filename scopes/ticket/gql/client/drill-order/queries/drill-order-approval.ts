import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  DrillOrderApproval,
  DrillOrderApprovalInfoRes,
  Maybe,
  QueryDrillOrderApprovalArgs,
  QueryDrillOrderApprovalInfoArgs,
} from '../generated-types/graphql';

export type QueryDrillOrderApprovalData = {
  drillOrderApproval: Maybe<DrillOrderApproval>;
};

export const GET_DRILL_ORDER_APPROVAL: DocumentNode = gql`
  query DrillOrderApproval($params: DrillOrderApprovalParams!) {
    drillOrderApproval(params: $params) {
      bizId
      bizType
      creatorId
      creatorName
      applicantId
      applicantName
      instStatus
      instId
      title
      idcTag
      blockGuid
      roomTag
      content
      gmtCreate
      gmtModified
      gmtEnd
      revokeReason
      approvalReason
      wfProperties
      nodeList {
        id
        name
        type
        code
        status
        createTime
        cancelReason
      }
    }
  }
`;

export function useDrillOrderApproval(
  options?: QueryHookOptions<QueryDrillOrderApprovalData, QueryDrillOrderApprovalArgs>
): QueryResult<QueryDrillOrderApprovalData, QueryDrillOrderApprovalArgs> {
  return useQuery(GET_DRILL_ORDER_APPROVAL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillOrderApproval(
  options?: LazyQueryHookOptions<QueryDrillOrderApprovalData, QueryDrillOrderApprovalArgs>
): LazyQueryResultTuple<QueryDrillOrderApprovalData, QueryDrillOrderApprovalArgs> {
  return useLazyQuery(GET_DRILL_ORDER_APPROVAL, { fetchPolicy: 'network-only', ...options });
}

export type QueryDrillOrderApprovalInfoData = {
  drillOrderApprovalInfo: Maybe<DrillOrderApprovalInfoRes>;
};

export const GET_DRILL_ORDER_APPROVAL_INFO: DocumentNode = gql`
  query DrillOrderApprovalInfo($params: DrillOrderApprovalInfoParams!) {
    drillOrderApprovalInfo(params: $params) {
      data
    }
  }
`;

export function useDrillOrderApprovalInfo(
  options?: QueryHookOptions<QueryDrillOrderApprovalInfoData, QueryDrillOrderApprovalInfoArgs>
): QueryResult<QueryDrillOrderApprovalInfoData, QueryDrillOrderApprovalInfoArgs> {
  return useQuery(GET_DRILL_ORDER_APPROVAL_INFO, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillOrderApprovalInfo(
  options?: LazyQueryHookOptions<QueryDrillOrderApprovalInfoData, QueryDrillOrderApprovalInfoArgs>
): LazyQueryResultTuple<QueryDrillOrderApprovalInfoData, QueryDrillOrderApprovalInfoArgs> {
  return useLazyQuery(GET_DRILL_ORDER_APPROVAL_INFO, { fetchPolicy: 'network-only', ...options });
}
