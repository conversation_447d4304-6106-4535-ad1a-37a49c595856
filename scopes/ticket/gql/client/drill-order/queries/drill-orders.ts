import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { DrillOrde<PERSON>, Maybe, QueryDrillOrdersArgs } from '../generated-types/graphql';

export type QueryDrillOrdersData = {
  drillOrders: Maybe<{ data: DrillOrder[]; total: number }>;
};

export const GET_DRILL_ORDER: DocumentNode = gql`
  query DrillOrders($params: QueryDrillOrdersParams!) {
    drillOrders(params: $params) {
      data {
        excNo
        taskSla
        unit
        title
        desc
        idcTag
        blockGuid
        taskStatus
        takeTime
        excMajor
        excMajorName
        excLevel
        excLevelName
        principalId
        principalName
        startTime
        endTime
        creatorId
        commitCommentTime
        commitCommentUser
        commentTime
        commentContent
        commentUser
        reviewTime
        approveCloseTime
        closeTime
        stopTime
        taskAssignee
        taskAssigneeName
        assigneeList {
          id
          userName
        }
        modifierId
        gmtModified
        creatorName
        modifierName
        gmtCreate
        closeProcessId
        bizProcessId
        scheduleId
        slaSeconds
      }
      total
    }
  }
`;

export function useDrillOrders(
  options?: QueryHookOptions<QueryDrillOrdersData, QueryDrillOrdersArgs>
): QueryResult<QueryDrillOrdersData, QueryDrillOrdersArgs> {
  return useQuery(GET_DRILL_ORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillOrders(
  options?: LazyQueryHookOptions<QueryDrillOrdersData, QueryDrillOrdersArgs>
): LazyQueryResultTuple<QueryDrillOrdersData, QueryDrillOrdersArgs> {
  return useLazyQuery(GET_DRILL_ORDER, { fetchPolicy: 'network-only', ...options });
}
