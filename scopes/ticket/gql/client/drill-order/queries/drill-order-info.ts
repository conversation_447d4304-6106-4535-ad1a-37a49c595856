import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { DrillOrderInfo, Maybe, QueryDrillOrderInfoArgs } from '../generated-types/graphql';

export type QueryDrillOrderInfoData = {
  drillOrderInfo: Maybe<DrillOrderInfo>;
};

export const GET_DRILL_ORDER_INFO: DocumentNode = gql`
  query DrillOrderInfo($execNo: String!) {
    drillOrderInfo(execNo: $execNo) {
      id
      excNo
      taskSla
      unit
      title
      desc
      idcTag
      blockGuid
      taskStatus
      takeTime
      excMajor
      excMajorName
      excLevel
      excLevelName
      principalId
      principalName
      startTime
      endTime
      creatorId
      commitCommentTime
      commitCommentUser
      commentTime
      commentContent
      commentUser
      reviewTime
      approveCloseTime
      closeTime
      stopTime
      taskAssignee
      taskAssigneeName
      assigneeList {
        id
        userName
      }
      modifierId
      gmtModified
      creatorName
      modifierName
      gmtCreate
      closeProcessId
      bizProcessId
      scheduleId
      slaSeconds
      exerciseOrderStepItemDoList {
        id
        excNo
        exerciseId
        reckonStartTime
        finishTime
        creatorId
        creatorName
        gmtCreate
        excPhase
        orderNo
        stepFlag
        stepDetail
        stepMeasure
        stationPosition
        roles
        followMethod
        slaSeconds
        sla
        slaUnit
        isRemark
      }
      exerciseOrderReviewList {
        id
        excNo
        reviewDesc
        creatorId
        creatorName
        gmtCreate
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
      }
      commentFileInfoList {
        uid
        name
        patialPath
        uploadUser {
          id
          name
        }
        uploadedAt
        ext
        src
        id
        size
        type
        modifiedAt
        targetId
        targetType
      }
      fileInfoList {
        uid
        name
        patialPath
        uploadUser {
          id
          name
        }
        uploadedAt
        ext
        src
        id
        size
        type
        modifiedAt
        targetId
        targetType
      }
    }
  }
`;

export function useDrillOrderInfo(
  options?: QueryHookOptions<QueryDrillOrderInfoData, QueryDrillOrderInfoArgs>
): QueryResult<QueryDrillOrderInfoData, QueryDrillOrderInfoArgs> {
  return useQuery(GET_DRILL_ORDER_INFO, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillOrderInfo(
  options?: LazyQueryHookOptions<QueryDrillOrderInfoData, QueryDrillOrderInfoArgs>
): LazyQueryResultTuple<QueryDrillOrderInfoData, QueryDrillOrderInfoArgs> {
  return useLazyQuery(GET_DRILL_ORDER_INFO, { fetchPolicy: 'network-only', ...options });
}
