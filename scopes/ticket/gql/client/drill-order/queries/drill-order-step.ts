import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { DrillOrderStep, Maybe, QueryDrillOrderStepArgs } from '../generated-types/graphql';

export type QueryDrillOrderStepData = {
  drillOrderStep: Maybe<{ data: DrillOrderStep[]; total: number }>;
};

export const GET_DRILL_ORDER_STEP: DocumentNode = gql`
  query DrillOrderStep($itemId: Long!) {
    drillOrderStep(itemId: $itemId) {
      data {
        id
        itemId
        remark
        creatorId
        creatorName
        gmtCreate
        avatarUrl
      }
      total
    }
  }
`;

export function useDrillOrderStep(
  options?: QueryHookOptions<QueryDrillOrderStepData, QueryDrillOrderStepArgs>
): QueryResult<QueryDrillOrderStepData, QueryDrillOrderStepArgs> {
  return useQuery(GET_DRILL_ORDER_STEP, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillOrderStep(
  options?: LazyQueryHookOptions<QueryDrillOrderStepData, QueryDrillOrderStepArgs>
): LazyQueryResultTuple<QueryDrillOrderStepData, QueryDrillOrderStepArgs> {
  return useLazyQuery(GET_DRILL_ORDER_STEP, { fetchPolicy: 'network-only', ...options });
}
