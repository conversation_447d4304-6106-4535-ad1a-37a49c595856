import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateDrillOrderResponse,
  Maybe,
  MutationCreateDrillOrderArgs,
} from '../generated-types/graphql';

export type CreateDrillOrderData = {
  createDrillOrder: Maybe<CreateDrillOrderResponse>;
};

export const CreateDrillOrder: DocumentNode = gql`
  mutation CreateDrillOrder($params: CreateDrillOrderParams!) {
    createDrillOrder(params: $params) {
      code
      data
      message
      success
    }
  }
`;

export function useCreateDrillOrder(
  options?: MutationHookOptions<CreateDrillOrderData, MutationCreateDrillOrderArgs>
): MutationTuple<CreateDrillOrderData, MutationCreateDrillOrderArgs> {
  return useMutation(CreateDrillOrder, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
