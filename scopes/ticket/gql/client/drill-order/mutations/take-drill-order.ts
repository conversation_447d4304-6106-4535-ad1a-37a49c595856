import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationTakeDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type TakeDrillOrderData = {
  takeDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const TAKEDRILLORDER: DocumentNode = gql`
  mutation TakeDrillOrder($execNoList: [String!]!) {
    takeDrillOrder(execNoList: $execNoList) {
      code
      message
      success
    }
  }
`;

export function useTakeDrillOrder(
  options?: MutationHookOptions<TakeDrillOrderData, MutationTakeDrillOrderArgs>
): MutationTuple<TakeDrillOrderData, MutationTakeDrillOrderArgs> {
  return useMutation(TAKEDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
