import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationApplyCommentDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type ApplyCommentDrillOrderData = {
  applyCommentDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const APPLYCOMMENTDRILLORDER: DocumentNode = gql`
  mutation ApplyCommentDrillOrder($params: ApplyCommentDrillOrderParams!) {
    applyCommentDrillOrder(params: $params) {
      code
      message
      success
    }
  }
`;

export function useApplyCommentDrillOrder(
  options?: MutationHookOptions<ApplyCommentDrillOrderData, MutationApplyCommentDrillOrderArgs>
): MutationTuple<ApplyCommentDrillOrderData, MutationApplyCommentDrillOrderArgs> {
  return useMutation(APPLYCOMMENTDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
