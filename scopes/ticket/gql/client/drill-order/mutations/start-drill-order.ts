import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationStartDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type StartDrillOrderData = {
  startDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const STARTDRILLORDER: DocumentNode = gql`
  mutation StartDrillOrder($execNo: String!) {
    startDrillOrder(execNo: $execNo) {
      code
      message
      success
    }
  }
`;

export function useStartDrillOrder(
  options?: MutationHookOptions<StartDrillOrderData, MutationStartDrillOrderArgs>
): MutationTuple<StartDrillOrderData, MutationStartDrillOrderArgs> {
  return useMutation(STARTDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
