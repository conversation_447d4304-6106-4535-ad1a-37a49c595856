import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationCommentDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type CommentDrillOrderData = {
  commentDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const COMMENTDRILLORDER: DocumentNode = gql`
  mutation CommentDrillOrder($params: CommentDrillOrderParams!) {
    commentDrillOrder(params: $params) {
      code
      message
      success
    }
  }
`;

export function useCommentDrillOrder(
  options?: MutationHookOptions<CommentDrillOrderData, MutationCommentDrillOrderArgs>
): MutationTuple<CommentDrillOrderData, MutationCommentDrillOrderArgs> {
  return useMutation(COMMENTDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
