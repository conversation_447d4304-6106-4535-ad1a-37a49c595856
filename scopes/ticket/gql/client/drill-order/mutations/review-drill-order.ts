import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationReviewDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type ReviewDrillOrderData = {
  reviewDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const REVIEWDRILLORDER: DocumentNode = gql`
  mutation ReviewDrillOrder($params: ReviewDrillOrderParams!) {
    reviewDrillOrder(params: $params) {
      code
      message
      success
    }
  }
`;

export function useReviewDrillOrder(
  options?: MutationHookOptions<ReviewDrillOrderData, MutationReviewDrillOrderArgs>
): MutationTuple<ReviewDrillOrderData, MutationReviewDrillOrderArgs> {
  return useMutation(REVIEWDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
