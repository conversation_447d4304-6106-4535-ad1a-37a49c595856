import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationWithoutReviewDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type WithoutReviewDrillOrderData = {
  withoutReviewDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const WITHOUTREVIEWDRILLORDER: DocumentNode = gql`
  mutation WithoutReviewDrillOrder($execNo: String!) {
    withoutReviewDrillOrder(execNo: $execNo) {
      code
      message
      success
    }
  }
`;

export function useWithoutReviewDrillOrder(
  options?: MutationHookOptions<WithoutReviewDrillOrderData, MutationWithoutReviewDrillOrderArgs>
): MutationTuple<WithoutReviewDrillOrderData, MutationWithoutReviewDrillOrderArgs> {
  return useMutation(WITHOUTREVIEWDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
