import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationCompleteDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type CompleteDrillOrderData = {
  completeDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const COMPLETEDRILLORDER: DocumentNode = gql`
  mutation CompleteDrillOrder($execNo: String!) {
    completeDrillOrder(execNo: $execNo) {
      code
      message
      success
    }
  }
`;

export function useCompleteDrillOrder(
  options?: MutationHookOptions<CompleteDrillOrderData, MutationCompleteDrillOrderArgs>
): MutationTuple<CompleteDrillOrderData, MutationCompleteDrillOrderArgs> {
  return useMutation(COMPLETEDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
