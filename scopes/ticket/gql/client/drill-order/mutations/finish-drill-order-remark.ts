import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationFinishDrillOrderRemarkArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type FinishDrillOrderRemarkData = {
  finishDrillOrderRemark: Maybe<UpdateDrillOrderResponse>;
};

export const CREATEDRILLORDERRRMARK: DocumentNode = gql`
  mutation FinishDrillOrderRemark($params: FinishDrillOrderRemarkParams!) {
    finishDrillOrderRemark(params: $params) {
      code
      message
      success
    }
  }
`;

export function useFinishDrillOrderRemark(
  options?: MutationHookOptions<FinishDrillOrderRemarkData, MutationFinishDrillOrderRemarkArgs>
): MutationTuple<FinishDrillOrderRemarkData, MutationFinishDrillOrderRemarkArgs> {
  return useMutation(CREATEDRILLORDERRRMARK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
