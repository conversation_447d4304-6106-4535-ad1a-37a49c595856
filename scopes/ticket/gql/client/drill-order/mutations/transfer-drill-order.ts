import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationTransferDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type TransferDrillOrderData = {
  transferDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const TransferDrillOrder: DocumentNode = gql`
  mutation TransferDrillOrder($params: TransferDrillOrderParams!) {
    transferDrillOrder(params: $params) {
      code
      message
      success
    }
  }
`;

export function useTransferDrillOrder(
  options?: MutationHookOptions<TransferDrillOrderData, MutationTransferDrillOrderArgs>
): MutationTuple<TransferDrillOrderData, MutationTransferDrillOrderArgs> {
  return useMutation(TransferDrillOrder, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
