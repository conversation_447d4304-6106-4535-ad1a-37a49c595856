import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationCreateDrillOrderRemarkArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type CreateDrillOrderRemarkData = {
  createDrillOrderRemark: Maybe<UpdateDrillOrderResponse>;
};

export const CREATEDRILLORDERRRMARK: DocumentNode = gql`
  mutation CreateDrillOrderRemark($params: CreateDrillOrderRemarkParams!) {
    createDrillOrderRemark(params: $params) {
      code
      message
      success
    }
  }
`;

export function useCreateDrillOrderRemark(
  options?: MutationHookOptions<CreateDrillOrderRemarkData, MutationCreateDrillOrderRemarkArgs>
): MutationTuple<CreateDrillOrderRemarkData, MutationCreateDrillOrderRemarkArgs> {
  return useMutation(CREATEDRILLORDERRRMARK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
