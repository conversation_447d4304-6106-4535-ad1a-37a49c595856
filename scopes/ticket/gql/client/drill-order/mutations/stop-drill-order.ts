import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationStopDrillOrderArgs,
  UpdateDrillOrderResponse,
} from '../generated-types/graphql';

export type StopDrillOrderData = {
  stopDrillOrder: Maybe<UpdateDrillOrderResponse>;
};

export const STOPDRILLORDER: DocumentNode = gql`
  mutation StopDrillOrder($params: StopDrillOrderParams!) {
    stopDrillOrder(params: $params) {
      code
      message
      success
    }
  }
`;

export function useStopDrillOrder(
  options?: MutationHookOptions<StopDrillOrderData, MutationStopDrillOrderArgs>
): MutationTuple<StopDrillOrderData, MutationStopDrillOrderArgs> {
  return useMutation(STOPDRILLORDER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
