export {
  useLazyRiskRegister,
  useLazyRiskRegisterMeasure,
  useLazyRiskRegisterLatestDevelopment,
} from './queries/risk-register';
export {
  BATCH_OPERATE_RISK_POOLS,
  useBatchOperateRiskPools,
} from './mutations/batch-operate-risk-pools';

export { useLazyRiskPools } from './queries/risk-pools';
export { useCreateChangeByRiskRegister } from './mutations/create-change-by-risk-register';
export { useDeleteRiskRegister } from './mutations/delete-risk-register';
export { useLazyRiskOrderCheckItems } from './queries/risk-order-check-items';
export { useLazyRiskTaskCheckItems } from './queries/risk-task-check-items';
export { useCreateRiskPool } from './mutations/create-risk-pool';
export { useUpdateRiskPool } from './mutations/update-risk-pool';
export { useDeleteRiskPool } from './mutations/delete-risk-pool';
export { useUpdateRiskPoolStatus } from './mutations/update-risk-pool-status';
export type {
  RiskRegisterBaseInfoResponse,
  RiskObject,
  RiskLevel,
  RiskObjectType,
  RiskStatus,
  RiskClearStatus,
  AnySimpleUser,
  RiskRegisterMeasureInfoResponse,
  MeasureJson,
  ProcessingReacrdResponse,
  MeasureType,
  MeasureStatus,
  McUploadFile,
  RelatePerson,
  RiskPool,
  RiskPoolsQ,
} from './generated-types/graphql';
export { useLazyRiskRegisterConfig } from './queries/risk-register-config.query';
export { useUpdateRiskRegisterConfig } from './mutations/update-risk-register-config';
export { useCanCloseRiskRegister } from './mutations/can-close-risk-register';
