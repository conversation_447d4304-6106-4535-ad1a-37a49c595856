import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  BatchOperateRiskPoolsResponse,
  Maybe,
  MutationBatchOperateRiskPoolsArgs,
} from '../generated-types/graphql';

export type BatchOperateRiskPoolsData = {
  batchOperateRiskPools: Maybe<BatchOperateRiskPoolsResponse>;
};

export const BATCH_OPERATE_RISK_POOLS: DocumentNode = gql`
  mutation BatchOperateRiskPools($assigneeId: Int!, $pointIdList: [Int!]!, $taskNo: String!) {
    batchOperateRiskPools(pointIdList: $pointIdList, assigneeId: $assigneeId, taskNo: $taskNo) {
      success
      message
      code
    }
  }
`;

export function useBatchOperateRiskPools(
  options?: MutationHookOptions<BatchOperateRiskPoolsData, MutationBatchOperateRiskPoolsArgs>
): MutationTuple<BatchOperateRiskPoolsData, MutationBatchOperateRiskPoolsArgs> {
  return useMutation(BATCH_OPERATE_RISK_POOLS, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
