import { gql, useMutation } from '@apollo/client';
import type { MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateRiskPoolResponse,
  MutationCreateRiskPoolArgs,
} from '../generated-types/graphql';

export type CreateRiskPoolData = {
  createRiskPool: CreateRiskPoolResponse;
};

export function useCreateRiskPool(
  options?: MutationHookOptions<CreateRiskPoolData, MutationCreateRiskPoolArgs>
): MutationTuple<CreateRiskPoolData, MutationCreateRiskPoolArgs> {
  return useMutation(
    gql`
      mutation CreateRiskPool($query: CreateRiskPoolQ!) {
        createRiskPool(query: $query) {
          code
          message
          success
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
