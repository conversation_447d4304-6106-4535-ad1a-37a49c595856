import { gql, useMutation } from '@apollo/client';
import type { MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationUpdateRiskPoolArgs,
  UpdateRiskPoolResponse,
} from '../generated-types/graphql';

export type UpdateRiskPoolData = {
  updateRiskPool: UpdateRiskPoolResponse;
};

export function useUpdateRiskPool(
  options?: MutationHookOptions<UpdateRiskPoolData, MutationUpdateRiskPoolArgs>
): MutationTuple<UpdateRiskPoolData, MutationUpdateRiskPoolArgs> {
  return useMutation(
    gql`
      mutation UpdateRiskPool($query: UpdateRiskPoolQ!) {
        updateRiskPool(query: $query) {
          code
          message
          success
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
