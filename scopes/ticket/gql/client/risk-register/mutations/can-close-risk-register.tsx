import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CanCloseRiskRegisterResponse,
  MutationCanCloseRiskRegisterArgs,
} from '../generated-types/graphql';

export type CanCloseRiskRegisterData = {
  canCloseRiskRegister: CanCloseRiskRegisterResponse;
};

export const CAN_CLOSE_RISK_REGISTER: DocumentNode = gql`
  mutation CanCloseRiskRegister($query: CanCloseRiskRegisterQ!) {
    canCloseRiskRegister(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useCanCloseRiskRegister(
  options?: MutationHookOptions<CanCloseRiskRegisterData, MutationCanCloseRiskRegisterArgs>
): MutationTuple<CanCloseRiskRegisterData, MutationCanCloseRiskRegisterArgs> {
  return useMutation(CAN_CLOSE_RISK_REGISTER, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
