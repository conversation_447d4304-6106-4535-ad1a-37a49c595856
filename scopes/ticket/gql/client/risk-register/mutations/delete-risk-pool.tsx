import { gql, useMutation } from '@apollo/client';
import type { MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteRiskRegisterResponse,
  MutationDeleteRiskPoolArgs,
} from '../generated-types/graphql';

export type DeleteRiskPoolData = {
  deleteRiskPool: DeleteRiskRegisterResponse;
};

export function useDeleteRiskPool(
  options?: MutationHookOptions<DeleteRiskPoolData, MutationDeleteRiskPoolArgs>
): MutationTuple<DeleteRiskPoolData, MutationDeleteRiskPoolArgs> {
  return useMutation(
    gql`
      mutation DeleteRiskPool($id: Long!) {
        deleteRiskPool(id: $id) {
          code
          message
          success
          data
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
