import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationUpdateRiskRegisterConfigArgs,
  UpdateRiskRegisterConfigResponse,
} from '../generated-types/graphql';

export type UpdateUpdateRiskRegisterConfigData = {
  updateRiskRegisterConfig: UpdateRiskRegisterConfigResponse;
};

export const UPDATE_RISK_REGISTER_CONFIG: DocumentNode = gql`
  mutation UpdateRiskRegisterConfig($query: UpdateRiskRegisterConfigQ!) {
    updateRiskRegisterConfig(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateRiskRegisterConfig(
  options?: MutationHookOptions<
    UpdateUpdateRiskRegisterConfigData,
    MutationUpdateRiskRegisterConfigArgs
  >
): MutationTuple<UpdateUpdateRiskRegisterConfigData, MutationUpdateRiskRegisterConfigArgs> {
  return useMutation(UPDATE_RISK_REGISTER_CONFIG, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
