import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateChangeByRiskRegisterQ,
  CreateChangeByRiskRegisterResponse,
  Maybe,
} from '../generated-types/graphql';

export type CreateChangeByRiskRegisterData = {
  createChangeByRiskRegister: Maybe<CreateChangeByRiskRegisterResponse>;
};

export const CREATE_CHANGE_BY_RISK_REGISTER: DocumentNode = gql`
  mutation CreateChangeByRiskRegisterResolver($query: CreateChangeByRiskRegisterQ!) {
    createChangeByRiskRegister(query: $query) {
      code
      message
      success
      changeOrderId
    }
  }
`;

export function useCreateChangeByRiskRegister(
  options?: MutationHookOptions<
    CreateChangeByRiskRegisterData,
    { query: CreateChangeByRiskRegisterQ }
  >
): MutationTuple<CreateChangeByRiskRegisterData, { query: CreateChangeByRiskRegisterQ }> {
  return useMutation(CREATE_CHANGE_BY_RISK_REGISTER, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
