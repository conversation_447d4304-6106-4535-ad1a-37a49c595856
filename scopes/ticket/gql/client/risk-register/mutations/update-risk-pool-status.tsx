import { gql, useMutation } from '@apollo/client';
import type { MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationUpdateRiskPoolStatusArgs,
  UpdateRiskPoolStatusResponse,
} from '../generated-types/graphql';

export type UpdateRiskPoolStatusData = {
  updateRiskPoolStatus: UpdateRiskPoolStatusResponse;
};

export function useUpdateRiskPoolStatus(
  options?: MutationHookOptions<UpdateRiskPoolStatusData, MutationUpdateRiskPoolStatusArgs>
): MutationTuple<UpdateRiskPoolStatusData, MutationUpdateRiskPoolStatusArgs> {
  return useMutation(
    gql`
      mutation UpdateRiskPoolStatus($query: UpdateRiskPoolStatusQ!) {
        updateRiskPoolStatus(query: $query) {
          code
          message
          success
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
