import { gql, useMutation } from '@apollo/client';
import type { MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteRiskRegisterResponse,
  MutationDeleteRiskRegisterArgs,
} from '../generated-types/graphql';

export type DeleteRiskRegisterData = {
  deleteRiskRegister: DeleteRiskRegisterResponse;
};

export function useDeleteRiskRegister(
  options?: MutationHookOptions<DeleteRiskRegisterData, MutationDeleteRiskRegisterArgs>
): MutationTuple<DeleteRiskRegisterData, MutationDeleteRiskRegisterArgs> {
  return useMutation(
    gql`
      mutation DeleteRiskRegister($riskId: String!) {
        deleteRiskRegister(riskId: $riskId) {
          code
          message
          success
          data
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
