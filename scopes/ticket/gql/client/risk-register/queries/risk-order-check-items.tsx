import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { RiskOrderCheckItems, RiskOrderCheckItemsResponse } from '../generated-types/graphql';

export type RiskOrderCheckItemsData = {
  riskOrderCheckItems: RiskOrderCheckItemsResponse;
};

export function useLazyRiskOrderCheckItems(
  options?: QueryHookOptions<RiskOrderCheckItemsData, { query: RiskOrderCheckItems }>
): LazyQueryResultTuple<RiskOrderCheckItemsData, { query: RiskOrderCheckItems }> {
  return useLazyQuery<RiskOrderCheckItemsData, { query: RiskOrderCheckItems }>(
    gql`
      query RiskOrderCheckItems($query: RiskOrderCheckItems!) {
        riskOrderCheckItems(query: $query) {
          code
          message
          success
          data {
            id
            bizId
            riskCategory
            riskType
            riskLevel
            riskDesc
            fileId
            gmtCreate
            gmtModified
            creatorName
            creatorId
            modifiedName
            modifiedId
            isDelete
            isEnable
            riskPointVerifyList {
              id
              verify
              taskNo
            }
            verifyId
            mergeRowsNum
            verify
            taskNo
            verifyResult
            fileInfoList {
              uid
              name
              patialPath
              uploadUser {
                id
                name
              }
              uploadedAt
              ext
              src
              id
              size
              type
              modifiedAt
              targetId
              targetType
            }
          }
          total
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
