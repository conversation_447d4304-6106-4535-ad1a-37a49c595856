import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { RiskRegisterConfigInfo } from '../generated-types/graphql';

export type RiskRegisterConfigData = {
  riskRegisterConfig: { data: RiskRegisterConfigInfo[] };
};
export const GET_RISK_REGISTER_CONFIG: DocumentNode = gql`
  query RiskRegisterConfig {
    riskRegisterConfig {
      data {
        riskTopTypeCode
        riskTopTypeName
        roleInfoList {
          roleName
          roleCode
        }
      }
    }
  }
`;

export function useLazyRiskRegisterConfig(
  options?: QueryHookOptions<RiskRegisterConfigData, {}>
): LazyQueryResultTuple<RiskRegisterConfigData, {}> {
  return useLazyQuery<RiskRegisterConfigData, {}>(GET_RISK_REGISTER_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
