import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type {
  QueryRiskTaskCheckItemsArgs,
  RiskTaskCheckItemsResponse,
} from '../generated-types/graphql';

export type RiskTaskCheckItemsData = {
  riskTaskCheckItems: RiskTaskCheckItemsResponse;
};

export function useLazyRiskTaskCheckItems(
  options?: QueryHookOptions<RiskTaskCheckItemsData, QueryRiskTaskCheckItemsArgs>
): LazyQueryResultTuple<RiskTaskCheckItemsData, QueryRiskTaskCheckItemsArgs> {
  return useLazyQuery<RiskTaskCheckItemsData, QueryRiskTaskCheckItemsArgs>(
    gql`
      query RiskTaskCheckItems($schId: Long!, $isFlatten: Boolean) {
        riskTaskCheckItems(schId: $schId, isFlatten: $isFlatten) {
          code
          message
          success
          data {
            id
            bizId
            riskCategory
            riskType
            riskLevel
            riskDesc
            fileId
            gmtCreate
            gmtModified
            creatorName
            creatorId
            modifiedName
            modifiedId
            isDelete
            isEnable
            riskPointVerifyList {
              id
              riskPointId
              verify
            }
            verifyId
            riskPointId
            verify
            mergeRowsNum
          }
          total
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
