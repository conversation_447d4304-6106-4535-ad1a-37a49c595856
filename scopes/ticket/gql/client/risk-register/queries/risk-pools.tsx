import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { RiskPoolsQ, RiskPoolsResponse } from '../generated-types/graphql';

export type RiskPoolData = {
  riskPools: RiskPoolsResponse;
};

export function useLazyRiskPools(
  options?: QueryHookOptions<RiskPoolData, { query: RiskPoolsQ }>
): LazyQueryResultTuple<RiskPoolData, { query: RiskPoolsQ }> {
  return useLazyQuery<RiskPoolData, { query: RiskPoolsQ }>(
    gql`
      query RiskPools($query: RiskPoolsQ) {
        riskPools(query: $query) {
          code
          message
          success
          data {
            id
            bizId
            riskCategory
            riskType
            riskLevel
            riskDesc
            fileId
            gmtCreate
            gmtModified
            creatorName
            creatorId
            modifiedName
            modifiedId
            isDelete
            isEnable
            riskPointVerifyList {
              id
              riskPointId
              verify
            }
            verifyId
            riskPointId
            verify
            mergeRowsNum
            fileInfoList {
              uid
              name
              patialPath
              uploadUser {
                id
                name
              }
              uploadedAt
              ext
              src
              id
              size
              type
              modifiedAt
              targetId
              targetType
            }
          }
          total
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
