import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type {
  ProcessingReacrdResponse,
  RiskRegisterBaseInfoResponse,
  RiskRegisterLatestDevelopmentQuery,
  RiskRegisterMeasureInfoResponse,
  RiskRegisterQuery,
} from '../generated-types/graphql';

export type { RiskRegisterBaseInfoResponse } from '../generated-types/graphql';

export type RiskRegisterBaseInfoData = {
  riskRegister: RiskRegisterBaseInfoResponse | null;
};

export function useLazyRiskRegister(
  options?: QueryHookOptions<RiskRegisterBaseInfoData, { query: RiskRegisterQuery }>
): LazyQueryResultTuple<RiskRegisterBaseInfoData, { query: RiskRegisterQuery }> {
  return useLazyQuery<RiskRegisterBaseInfoData, { query: RiskRegisterQuery }>(
    gql`
      query RiskRegister($query: RiskRegisterQuery!) {
        riskRegister(query: $query) {
          authorizedUserList
          id
          createdAt
          modifiedAt
          idcTag
          blockGuid
          riskResourceCode
          riskResourceName
          riskCategoryCode
          riskCategoryName
          riskTypeCode
          riskTypeName
          riskPriorityCode
          riskLevel
          riskLevelName
          riskObjectType
          riskObjects {
            idcTag
            blockTag
            roomTag
            deviceType
            deviceTag
            objectName
            objectGuid
            label
            value
            key
          }
          riskStatus
          riskClearStatus
          createUser
          riskClearReason
          riskDesc
          riskInfluenceDesc
          relatePerson {
            type
            id
            name
            label
            value
          }
          measureProgress
          fileInfoList {
            uid
            name
            patialPath
            uploadUser {
              id
              name
            }
            uploadedAt
            ext
            src
            id
            size
            type
            modifiedAt
            targetId
            targetType
          }
          rectifyInfoList {
            uid
            name
            patialPath
            uploadUser {
              id
              name
            }
            uploadedAt
            ext
            src
            id
            size
            type
            modifiedAt
            targetId
            targetType
          }
          riskIdentifyTime
          riskEvaluateTime
          riskHandleTime
          riskAuditTime
          riskCloseTime
          evaluateInstId
          auditInstId
          riskIdentifier
          deleted
          locationList {
            locationType
            subType
            guid
            name
            deviceLabel
            fromBlockGuid
            fromBlockName
            fromRoomGuid
            fromRoomName
          }
          riskOwnerIdList
          planCompleteTime
          longTermRisk
          timeExtensionApprInfo {
            originPlanCompleteTime
            planCompleteTime
            processId
            reason
          }
          upgradeRoc
          upgradeRocTime
          rocUpgradeInstId
          deleted
          questionRelateEventList {
            eventId
            idcTag
            blockGuid
            eventTitle
            eventLevelCode
            eventLevelName
            categoryCode
            categoryName
            occurTime
            eventStatus
            ownerId
            ownerName
            closeTime
          }
          immutableEventIdList
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}

export type RiskRegisterMeasureInfoData = {
  riskRegisterMeasure: RiskRegisterMeasureInfoResponse;
};

export function useLazyRiskRegisterMeasure(
  options?: QueryHookOptions<RiskRegisterMeasureInfoData, { query: RiskRegisterQuery }>
): LazyQueryResultTuple<RiskRegisterMeasureInfoData, { query: RiskRegisterQuery }> {
  return useLazyQuery<RiskRegisterMeasureInfoData, { query: RiskRegisterQuery }>(
    gql`
      query RiskRegisterMeasure($query: RiskRegisterQuery!) {
        riskRegisterMeasure(query: $query) {
          longMeasures {
            riskId
            measureId
            measureType
            measureDesc
            measureStatus
            followUserId
            planCompleteTime
            completeTime
          }
          shortMeasures {
            riskId
            measureId
            measureType
            measureDesc
            measureStatus
            followUserId
            planCompleteTime
            completeTime
          }
          deletedLongMeasures {
            riskId
            measureId
            measureType
            measureDesc
            measureStatus
            followUserId
            planCompleteTime
            completeTime
          }
          deletedShortMeasures {
            riskId
            measureId
            measureType
            measureDesc
            measureStatus
            followUserId
            planCompleteTime
            completeTime
          }
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}

export type ProcessingReacrdData = {
  riskRegisterLatestDevelopment: ProcessingReacrdResponse[];
};
export function useLazyRiskRegisterLatestDevelopment(
  options?: QueryHookOptions<ProcessingReacrdData, { query: RiskRegisterLatestDevelopmentQuery }>
): LazyQueryResultTuple<ProcessingReacrdData, { query: RiskRegisterLatestDevelopmentQuery }> {
  return useLazyQuery<ProcessingReacrdData, { query: RiskRegisterLatestDevelopmentQuery }>(
    gql`
      query RiskRegisterLatestDevelopment($query: RiskRegisterLatestDevelopmentQuery!) {
        riskRegisterLatestDevelopment(query: $query) {
          riskId
          measureId
          measureType
          measureDesc
          measureStatus
          optRecordId
          handlerUserId
          handleTime
          handleContent
          fileInfoList {
            uid
            name
            patialPath
            uploadUser {
              id
              name
            }
            uploadedAt
            ext
            src
            id
            size
            type
            modifiedAt
            targetId
            targetType
          }
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
