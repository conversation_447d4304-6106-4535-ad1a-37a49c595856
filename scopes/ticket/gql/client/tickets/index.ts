// Auto Generated Types
// ------

export type {
  Maybe,
  TicketTypeNode,
  Task<PERSON>son,
  DrillConfig,
  QueryDrillConfigsArgs,
  DrillPlanStep,
  FetchChangeShiftAttendanceResult,
  FetchNonstandardInspectItem,
  UpdateOrDeleteNonstandardInspectItemQ,
  SpareInput,
  DrillOrderApprovalNode,
  EventProcessRecordJson,
} from './generated-types/graphql';

// Mutations
// ------

export {
  CREATE_EVENT_PROCESS_RECORD,
  useCreateEventProcessRecord,
} from './mutations/create-event-process-record';

export {
  SKIP_EVENT_CURRENT_PHASE,
  useSkipEventCurrentPhase,
} from './mutations/skip-event-current-phase';

export { REDIRECT_EVENT, useRedirectEvent } from './mutations/redirect-event';
export {
  FINISH_EVENT_CURRENT_PHASE,
  useFinishEventCurrentPhase,
} from './mutations/finish-event-current-phase';

export { LOCATE_EVENT, useLocateEvent } from './mutations/locate-event';
export { HIT_RISK_POINT, useHitRiskPoint } from './mutations/hit-risk-point';
export {
  BATCH_UPDATE_RISK_STATUS,
  useBatchUpdateTaskStatus,
} from './mutations/batch-update-task-status';
export { CREATE_TASK, useTaskCreation } from './mutations/task-creator';
export { DELETE_TASK, useDeleteTask } from './mutations/delete-task';
export { UPDATE_TASK, useTaskUpdate } from './mutations/task-update';
export {
  BATCH_URGE_RISK_CHECK_TICKETS,
  useBatchUrgeRiskCheckTickets,
} from './mutations/batch-urge-risk-check-tickets';
export {
  CREATE_EVENT_RELATE_RISK_TICKET,
  useEventRelateRiskTicketCreation,
} from './mutations/event-relate-risk-ticket-creator';
export {
  CREATE_RISK_CHECK_TICKET,
  useRiskCheckTicketCreation,
} from './mutations/risk-check-ticket-creator';
export {
  BATCH_MAINTENANCE_CHECK_ITEMS,
  useBatchMaintenanceCheckItems,
} from './mutations/batch-maintenance-check-items';
export {
  CREATE_TICKET_NOTIFICATION,
  useTicketNotificationCreation,
} from './mutations/ticket-notification-creator';
export {
  CREATE_NONSTANDARD_INSPECT_ITEM,
  useCreateNonstandardInspectItem,
} from './mutations/create-nonstandard-inspect-item';
export {
  UPDATE_OR_DELETE_NONSTANDARD_INSPECT_ITEM,
  useUpdateOrDeleteNonstandardInspectItem,
} from './mutations/update-or-delete-nonstandard-inspect-item';
export {
  UPLOAD_INSPECT_ITEM_FILE,
  useUploadInspectItemFile,
} from './mutations/upload-inspect-item-file';
export { TO_CLOSE_TICKET, useToCloseTicketMutation } from './mutations/to-close-ticket.mutation';
export { TACK_OVER_TICKET, useTakeOverTicketMutation } from './mutations/tack-over-ticket.mutation';

export { useAssignTicketMutation, TO_ASSIGN_TICKET } from './mutations/to-assgin-ticket.mutation';

// Queries
// ------
export {
  GET_EVENT_NOTIFICATIONS,
  useEventNotifications,
  useLazyEventNotifications,
} from './queries/event-notifications.query';
export {
  GET_EVENT_RELATE_APPROVAL,
  useEventRelateApproval,
  useLazyEventRelateApproval,
} from './queries/event-relate-approval.query';
export {
  GET_EVENT_PROCESS_RECORDS,
  useEventProcessRecords,
  useLazyEventProcessRecords,
} from './queries/event-process-records';

export {
  GET_TICKET_RELATE_APPROVAL_DETAIL,
  useLazyTicketRelateApprovalDetail,
  useTicketRelateApprovalDetail,
} from './queries/ticket-relate-approval-detail';

export {
  GET_TICKET_RELATE_APPROVALS,
  useLazyTicketRelateApprovals,
  useTicketRelateApprovals,
} from './queries/ticket-relate-approvals';
export {
  GET_REPAIR_FEEDBACKS,
  useRepairFeedbacks,
  useLazyRepairFeedbacks,
} from './queries/repair-feedbacks';
export { useLazyChangeNotifications } from './queries/change';
export {
  GET_ACCOUNT_RELATE_TICKETS,
  useAccountRelateTickets,
  useLazyAccountRelateTickets,
} from './queries/account-relate-rack-tickets';
export type { ChangeNotificationsData } from './queries/change';
export type { NeedUploadChangeCustomerType } from './mutations/change';
export {
  useCreateChangeNotification,
  useNeedUploadChangeCustomerAgreement,
} from './mutations/change';
export { GET_TICKET_TYPES, useTicketTypes, useLazyTicketTypes } from './queries/ticket-types.query';
export {
  GET_EVENT_RELATE_TICKETS,
  useEventRelateTickets,
  useLazyEventRelateTickets,
} from './queries/event-relate-tickets.query';
export type { QueryTicketTypesData } from './queries/ticket-types.query';
export { useLazyEventSourceRecords } from './queries/event-source-records.query';

export { GET_TASKS, useLazyTasks, useTasks } from './queries/tasks.query';
export { GET_TASK_DETAIL, useLazyTaskDetail, useTaskDetail } from './queries/task-detail';
export {
  GET_TICKET_NOTIFICATIONS,
  useLazyTicketNotifications,
  useTicketNotifications,
} from './queries/ticket-notifications';

export {
  GET_PDU_DEVICES_ON_RACK,
  useLazyPduDevicesOnRack,
  usePduDevicesOnRack,
} from './queries/pdu-devices-on-rack';

export { GET_DRILL_CONFIG, useDrillConfig, useLazyDrillConfig } from './queries/drill-config.query';
export {
  GET_DRILL_CONFIGS,
  useDrillConfigs,
  useLazyDrillConfigs,
} from './queries/drill-configs.query';
export { DELETE_DRILL_CONFIG, useDeleteDrillConfig } from './mutations/delete-drill-config';
export { CREATE_DRILL_CONFIG, useDrillConfigCreation } from './mutations/drill-config-creator';
export { UPDATE_DRILL_CONFIG, useDrillConfigUpdate } from './mutations/drill-config-update';
export {
  GET_DRILL_CONFIG_BY_BIZ,
  useDrillConfigByBiz,
  useLazyDrillConfigByBiz,
} from './queries/drill-config-by-biz.query';
export { GET_CHANGE_ORDER_DEVICES, useLazyChangeOrderDevices } from './queries/change-devices';

export type { ChangeDeviceList } from './queries/change-devices';

export {
  useUpdateChangeOrderDeviceAlarmStatus,
  UPDATE_CHANGE_ORDER_DEVICE_ALARM_STATUS,
} from './mutations/update-change-order-device-alarm-status';

export {
  useDeleteChangeOrderDevice,
  DELETE_CHANGE_ORDER_DEVICE,
} from './mutations/delete-change-order-device';

export {
  useChangeOfflineRelateTickets,
  GET_CHANGE_OFFLINE_RELATE_TICKETS,
} from './queries/change-offline-relate-tickets.query';
export type { ChangeOfflineRelateTicket } from './queries/change-offline-relate-tickets.query';

export {
  useRevokeChangeOfflineSummery,
  REVOKE_CHANGE_OFFLINE_SUMMERY,
} from './mutations/revoke-change-offline-summery';

export { useFetchFuzzyChanges, GET_FUZZY_CHANGES } from './queries/fetch-fuzzy-changes';

export { useLazyFetchFuzzyEvents, GET_FUZZY_EVENTS } from './queries/fetch-fuzzy-events';

export { useCreateEventByChange, CREATE_EVENT_BY_CHANGE } from './mutations/create-event-by-change';
export {
  useCreateRiskRegisterByChange,
  CREATE_RISK_REGISTER_BY_CHANGE,
} from './mutations/create-risk-register-by-change';

export {
  useLazyChangeOfflineDeviceInhibitionStatus,
  GET_CHANGE_OFFLINE_DEVICE_INHIBITION_STATUS,
} from './queries/change-offline-device-inhibition-status';

export {
  useLazyChangeShiftConfig,
  useChangeShiftConfig,
  GET_CHANGE_SHIFT_CONFIG,
} from './queries/fetch-change-shift-config.query';

export {
  useSubmitChangeShiftCountersignDescriptionData,
  SUBMIT_CHANGE_SHIFT_COUNTERSIGN_DESCRIPTION,
} from './mutations/submit-change-shift-countersign-description';

export {
  useLazyQueryFetchChangeShiftApplyOwner,
  GET_CHANGE_SHIFT_APPLY_OWNER,
} from './queries/fetch-change-shift-apply-owner.query';

export {
  useLazyQueryFetchChangeShiftAttendanceResult,
  GET_CHANGE_SHIFT_ATTENDANCE_RESULT,
} from './queries/fetch-change-shift-attendance-result.query';

export {
  useUpdateChangeShiftConfig,
  UPDATE_UPDATE_CHANGE_SHIFT_CONFIG,
} from './mutations/update-change-shift-config';

export {
  GET_NONSTANDARD_INSPECT_ITEM,
  useLazyQueryFetchNonstandardInspectItem,
} from './queries/nonstandard-inspect-item.query';

export {
  useLazyFetchChangeApproval,
  GET_CHANGE_ORDER_APPROVAL,
} from './queries/change-approval.query';

export {
  useUploadMaintenanceItemFile,
  UPLOAD_MAINTENANCE_ITEM_FILE,
} from './mutations/upload-maintenance-item-file';

export {
  useStartExecutionPowerGrid,
  START_EXECUTION_POWER_GRID,
} from './mutations/start-execution-power-grid';

export { useLazyAlarmCountByBiz, GET_ALARM_COUNT_BY_BIZ } from './queries/alarm-count-by-biz.query';

export {
  GET_TICKET_FIRST_TIME_RECIPIENT_INFO,
  useLazyTicketFirstTimeRecipientInfo,
  useTicketFirstTimeRecipientInfo,
} from './queries/ticket-first-time-recipient-info.query';

export {
  GET_TICKET_DELAY_TYPES,
  useLazyTicketDelayTypes,
  useTicketDelayTypes,
} from './queries/get-ticket-delay-types';
export {
  GET_TICKET_FAIL_REASON_TYPES,
  useTicketFailReasonTypes,
  useLazyTicketFailReasonTypes,
} from './queries/get-ticket-fail-reason-types';
export {
  useLazyFetchSchedulePageTurningId,
  GET_SCHEDULE_PAGE_TURNING_ID,
} from './queries/schedule-page-turning-id.query';

export {
  useTriggerExcelSchedule,
  TRIGGER_EXCEL_SCHEDULE,
} from './mutations/trigger-excel-schedule';

export { useLazyChangeTemplates, GET_CHANGE_TEMPLATES } from './queries/change-templates';
export type { FetchChangeTemplatesData, FetchChangeTemplatesQ } from './queries/change-templates';

export { useLazyChanges, GET_CHANGES } from './queries/changes.query';
export type { FetchChangesData, FetchChangeQ } from './queries/changes.query';

export {
  useUpdateChangeTemplate,
  UPDATE_UPDATE_CHANGE_TEMPLATE,
} from './mutations/update-change-template';
export type { UpdateChangeTemplateQ } from './mutations/update-change-template';

export {
  useDeleteChangeTemplate,
  DELETE_CHANGE_TEMPLATE,
} from './mutations/delete-change-template';

export {
  useRevertChangeTemplateApproval,
  RECERT_CHANGE_TEMPLATE_APPROVAL,
} from './mutations/revert-change-template-approval';

export { useDeferChangeTemplate, DEFER_CHANGE_TEMPLATE } from './mutations/defer-change-template';

export {
  useInvalidateChangeTemplate,
  INVALIDATE_CHANGE_TEMPLATE_APPROVAL,
} from './mutations/invalidate-change-template';

export { useLazyChangeTemplate, GET_CHANGE_TEMPLATE } from './queries/change-template.query';

export { useCreateChange, CREATE_CHANGE } from './mutations/create-change';

export {
  useLazyAvailableChangeTemplate,
  GET_AVAILABLE_CHANGE_TEMPLATE,
} from './queries/available-change-templates.query';

export { useLazyEventCurrentOwner } from './queries/event-current-owner.query';

export type { FetchEventCurrentOwnerData } from './queries/event-current-owner.query';

export { useRevertChangeApproval, REVERT_CHANGE_OFFLINE } from './mutations/revert-change-approval';

export { useLazyChangeVersions, GET_CHANGE_VERSIONS } from './queries/change-versions.query';

export { useCreateEventBeta, CREATE_EVENT_BETA } from './mutations/create-event-beta';

export { useLazyEventsBeta, GET_EVENTS_BETA } from './queries/events-beta.query';
export type { EventBetaJson, EventBetaQuery, FaultModelInfo } from './queries/events-beta.query';

export { useLazyEventBeta, GET_EVENT_BETA } from './queries/event-beta.query';
export type { EventBetaData } from './queries/event-beta.query';

export { useLazyEventProgress } from './queries/event-progress.query';
export type { EventProgressData, ProcessRecordInfo } from './queries/event-progress.query';

export { useAddEventProgressProcessingRecords } from './mutations/add-event-progress-processing-records';

export { useDetectEventProblem } from './mutations/detect-event-problem';

export { useConfirmEvent } from './mutations/confirm-event';
export { useConfirmEventOvertimeUpgrade } from './mutations/confirm-event-overtime-upgrade';

export { useRelieveEvent } from './mutations/relieve-event';
export { useFinishedEvent } from './mutations/finished-event';

export { useLazyEventReports } from './queries/event-reports.query';
export type { EventReportsJson } from './queries/event-reports.query';

export { useLazyEvnetBetaRelateTickets } from './queries/evnet-beta-relate-tickets.query';
export type { EvnetBetaRelateTicketsJson } from './queries/evnet-beta-relate-tickets.query';
export { useChangeEventLevel } from './mutations/change-event-level';

export { useHandoverEvent } from './mutations/handover-event';
export { useConcludeEvnet } from './mutations/conclude-evnet';

export { useCreateEventReport } from './mutations/create-event-report';
export { useReplayEvent } from './mutations/replay-event';

export { useLazyEventConfiguration } from './queries/event-configuration.query';
export { useSubmitEventConfiguration } from './mutations/submit-event-configuration';
export { useUpdateEventBeta } from './mutations/update-event-beta';
export { useCreateEmergencyProcess } from './mutations/create-emergency-process';
export { useUpdateEmergencyProcess } from './mutations/update-emergency-process';
export { useLazyEmergencyProcessDetail } from './queries/emergency-process-detail.query';
export { useDeleteEmergencyProcess } from './mutations/delete-emergency-process';

export { useLazyEmergencyProcess } from './queries/emergency-process.query';

export type { EmergencyProcessData, EmergencyProcessQ } from './queries/emergency-process.query';
export { useReopenEvent } from './mutations/reopen-event';
export { useCreateRiskRegisterByEventBeta } from './mutations/create-risk-register-by-event-beta';
export { useCreateRepairByEventBeta } from './mutations/create-repair-by-event-beta';
export { useJudgeEventBetaVersion } from './mutations/judge-event-beta-version';

export { useLazyEvnetSourceRecord } from './queries/event-source-record.query';
export type { EventSourceRecordJson } from './queries/event-source-record.query';

export {
  TICKET_TASK_COUNT,
  useTicketTaskCount,
  useLazyTicketTaskCount,
} from './queries/ticket-task-count.query';
export {
  TICKET_ROOM_START_CHECK,
  useTicketRoomStartCheck,
  useLazyTicketRoomStartCheck,
} from './queries/ticket-room-start-check';

export { useLazyAllEvents } from './queries/all-events.query';

export { useUpdateAssigneeConfigMutation } from './mutations/ticket-query-assignee-config';
export { useUpdateApproveAreaMutation } from './mutations/ticket-query-approve-area';
export { useUpdateOrderAssigneeMutation } from './mutations/ticket-query-order-assignee';

export { useUpdateRiskRegisterRoc } from './mutations/update-risk-register-roc';
export { usePostponeRiskRegister } from './mutations/postpone-risk-register';
export { useJudgeRiskRegisterCanClose } from './mutations/judge-risk-register-can-close';
export { useLazyRiskRegisterMeasureAndProcessingRecord } from './queries/risk-register-measure-and-processing-record.query';
export type { RiskRegisterMeasureAndProcessingRecordResponse } from './queries/risk-register-measure-and-processing-record.query';
export { useLazyUsersByRoleSpace } from './queries/users-by-role-space.query';
export type { FetchAvailableChangeTemplatesResponse } from './queries/available-change-templates.query';
export { useLazyMonitorGroupsByInstanceObject } from './queries/monitor-groups-by-instance-object.query';
export type { MonitorGroupsByInstanceObjectJson } from './queries/monitor-groups-by-instance-object.query';
export { useSubmitChangeOnline } from './mutations/submit-change-online';
export type {
  SubmitChangeOnlineResponse,
  SubmitChangeOnlineQ,
  ChangeStepInfo,
} from './mutations/submit-change-online';

export { useLazyChangeOnline } from './queries/change-online';
export type { ChangeOnlineData } from './queries/change-online';
export { useStartExecuteChangeOnlineData } from './mutations/start-execute-change-online';

export { useStartExecuteChangeOnlineStepData } from './mutations/start-execute-change-online-step';
export { useStopChangeOnlineStep } from './mutations/stop-change-online-step';
export { useStopChangeOnline } from './mutations/stop-change-online';
export { usePostponeChange } from './mutations/postpone-change';
export { useRescheduleChangeOnline } from './mutations/reschedule-change';
export { useCancelChangeOnline } from './mutations/cancel-change-online';
export { useSubmitChangeOnlineTemplate } from './mutations/submit-change-online-template';
export type { SubmitChangeOnlineTemplateQ } from './mutations/submit-change-online-template';
export { useSaveChangeOnlineTemplate } from './mutations/save-change-online-template';
export { useLazyChangeOnlineTemplate } from './queries/change-online-template';

export { useLazyChangeOnlineTemplates } from './queries/change-online-templats';

export type {
  ChangeOnlineTemplatesQ,
  ChangeOnlineTemplatesResponse,
  ChangeOnlineTemplatesData,
} from './queries/change-online-templats';
export { useExpireChangeTemplate } from './mutations/expire-change-template';
export { useEffectChangeTemplate } from './mutations/effect-change-template';
export { useSaveChangeOnline } from './mutations/save-change-online';
export { useLazyCheckChangeOnlineLevelHasTicket } from './queries/check-change-online-level-has-ticket.query';
export { useChangeOnlineRelateTicket } from './mutations/change-online-relate-ticket';
export { useSubmitChangeOnlineSummery } from './mutations/submit-change-online-summery';
export type { CheckChangeOnlineLevelHasTicketData } from './queries/check-change-online-level-has-ticket.query';
export { useFinishChangeOnline } from './mutations/finish-change-online';
export { useCheckEventCanAudit } from './mutations/check-event-can-audit';

export { useLazyEventAllVersionDetail } from './queries/event-all-version-detail.query';

export { useLazyEventBetaReportSceneInfo } from './queries/event-beta-report-scene-info.query';
export { useFinishedCurrentEvent } from './mutations/finished-current-event';
export { useLazyEventBetaListByKeyword } from './queries/event-beta-list-by-keyword.query';
export { useJudgeQuestionKnowledgeExist } from './mutations/judge-question-knowledge-exist';
export { useLazyRiskQuestionKnowledgeCode } from './queries/risk-question-knowledge-code.query';
export { useShareRiskQuestionToKnowledge } from './mutations/share-risk-question-to-knowledge';
export { useEventRelateRiskQuestion } from './mutations/event-relate-risk-question';
export { useLazyEventCurrentDutyLeader } from './queries/event-current-duty-leader.query';
