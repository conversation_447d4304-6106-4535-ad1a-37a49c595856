import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { FetchChangeResponse, QueryChangesArgs } from '../generated-types/graphql';

export type { FetchChangesData, FetchChangeQ } from '../generated-types/graphql';

export type ChangesData = {
  changes: FetchChangeResponse;
};

export const GET_CHANGES: DocumentNode = gql`
  query Changes($query: FetchChangeQ!) {
    changes(query: $query) {
      data {
        id
        changeOrderId
        exeUserGroupCode
        changeResponsibleUserList {
          userName
          userId
        }
        existDelayApproval
        idcTag
        blockTag
        templateId
        templateName
        changeType
        changeTypeName
        riskLevel
        planStartTime
        planEndTime
        changeStatus
        creatorId
        creatorName
        realStartTime
        realEndTime
        title
        workFlowId
        modifyPersonId
        modifyPersonName
        gmtModified
        gmtCreate
        operatorName
        changeCategory
        operatorId
        exeWay
        exeResult
        respPersonId
        reason
        changeTimeList {
          startDate
          endDate
          startHour
          endHour
        }
      }
      success
      total
    }
  }
`;
export function useLazyChanges(
  options?: QueryHookOptions<ChangesData, QueryChangesArgs>
): LazyQueryResultTuple<ChangesData, QueryChangesArgs> {
  return useLazyQuery<ChangesData, QueryChangesArgs>(GET_CHANGES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
