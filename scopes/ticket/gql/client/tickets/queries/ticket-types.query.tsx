import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Maybe, TicketTypeNode } from '../generated-types/graphql';

export type QueryTicketTypesData = {
  ticketTypes: Maybe<TicketTypeNode[]>;
};

export const GET_TICKET_TYPES: DocumentNode = gql`
  query TicketTypes {
    ticketTypes {
      label
      value
      parentValue
      children {
        label
        value
        parentValue
        isDeleted
      }
      isDeleted
    }
  }
`;

export function useTicketTypes(
  options?: QueryHookOptions<QueryTicketTypesData, {}>
): QueryResult<QueryTicketTypesData, {}> {
  return useQuery(GET_TICKET_TYPES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTicketTypes(
  options?: LazyQueryHookOptions<QueryTicketTypesData, {}>
): LazyQueryResultTuple<QueryTicketTypesData, {}> {
  return useLazyQuery(GET_TICKET_TYPES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
