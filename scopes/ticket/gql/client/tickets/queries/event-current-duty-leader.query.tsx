import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EventCurrentDutyLeaderResponse,
  QueryEventCurrentDutyLeaderArgs,
} from '../generated-types/graphql';

export type EventCurrentDutyLeaderData = {
  eventCurrentDutyLeader: EventCurrentDutyLeaderResponse;
};

export const EVENT_CURRENT_DUTY_LEADER: DocumentNode = gql`
  query EventCurrentDutyLeader($query: EventCurrentDutyLeaderQ!) {
    eventCurrentDutyLeader(query: $query) {
      success
      message
      code
      data {
        id
        userName
        loginName
        mobile
        email
        deleted
      }
    }
  }
`;

export function useLazyEventCurrentDutyLeader(
  options?: LazyQueryHookOptions<EventCurrentDutyLeaderData, QueryEventCurrentDutyLeaderArgs>
): LazyQueryResultTuple<EventCurrentDutyLeaderData, QueryEventCurrentDutyLeaderArgs> {
  return useLazyQuery(EVENT_CURRENT_DUTY_LEADER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
