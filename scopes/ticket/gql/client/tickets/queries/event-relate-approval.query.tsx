import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  B<PERSON>Inst<PERSON><PERSON><PERSON>,
  Maybe,
  QueryEventRelateApprovalArgs,
} from '../generated-types/graphql';

export type QueryEventRelateApprovalData = {
  eventRelateApproval: Maybe<BpmInstanceJson>;
};

export const GET_EVENT_RELATE_APPROVAL: DocumentNode = gql`
  query GetEventRelateApproval($processNo: String!, $eventId: Long!) {
    eventRelateApproval(processNo: $processNo, eventId: $eventId) {
      code
      title
      bizId
      bizType
      content
      oaType
      idc
      blockGuid
      room
      reason
      formJson
      status
      applyUser
      applyUserName
      creatorId
      creatorName
      applyTime
      operationRecords {
        operationName
        operationType
        operationTasks {
          date
          taskResult
          remark
          taskId
          missUser
          missUserInfo {
            type
            missId
            currId
          }
          userList {
            id
            userName
            jobNo
            mobile
            email
          }
          taskRedirectInfoList {
            sourceUserId
            targetUserId
            redirectTime
          }
          taskFileInfoList {
            fileFormat
            fileName
            filePath
            fileSize
            fileType
            gmtCreate
            gmtModified
            id
            targetId
            targetType
            uploadBy
            uploadByName
            uploadTime
          }
        }
        nodeId
        ccUserList {
          id
          userName
          ccMsgIsRead
          jobNo
          mobile
          email
        }
        isCountersignNode
        nodeType
        processCommentInfo {
          commentTime
          content
          id
          isDeleted
          personId
          personName
          deleteTime
        }
      }
      xml
      sla
      bizTypeSla
      msgId
      msgParentType
      msgSecondType
      formattedContent {
        label
        value
      }
      attachmentType
    }
  }
`;

export function useEventRelateApproval(
  options?: QueryHookOptions<QueryEventRelateApprovalData, QueryEventRelateApprovalArgs>
): QueryResult<QueryEventRelateApprovalData, QueryEventRelateApprovalArgs> {
  return useQuery(GET_EVENT_RELATE_APPROVAL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyEventRelateApproval(
  options?: LazyQueryHookOptions<QueryEventRelateApprovalData, QueryEventRelateApprovalArgs>
): LazyQueryResultTuple<QueryEventRelateApprovalData, QueryEventRelateApprovalArgs> {
  return useLazyQuery(GET_EVENT_RELATE_APPROVAL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
