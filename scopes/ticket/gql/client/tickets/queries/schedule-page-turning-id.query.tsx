import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchSchedulePageTurningIdResponse,
  Maybe,
  QueryFetchSchedulePageTurningIdArgs,
} from '../generated-types/graphql';

export type QueryFetchSchedulePageTurningIdData = {
  fetchSchedulePageTurningId: Maybe<FetchSchedulePageTurningIdResponse>;
};

export const GET_SCHEDULE_PAGE_TURNING_ID: DocumentNode = gql`
  query FetchSchedulePageTurningId($query: FetchSchedulePageTurningIdQ!) {
    fetchSchedulePageTurningId(query: $query) {
      lastId
      nextId
    }
  }
`;

export function useLazyFetchSchedulePageTurningId(
  options?: LazyQueryHookOptions<
    QueryFetchSchedulePageTurningIdData,
    QueryFetchSchedulePageTurningIdArgs
  >
): LazyQueryResultTuple<QueryFetchSchedulePageTurningIdData, QueryFetchSchedulePageTurningIdArgs> {
  return useLazyQuery(GET_SCHEDULE_PAGE_TURNING_ID, { fetchPolicy: 'network-only', ...options });
}
