import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query } from '../generated-types/graphql';

export type QueryTicketFailReasonData = Pick<Query, 'getTicketFailReasonType'>;

export const GET_TICKET_FAIL_REASON_TYPES = gql`
  query GetTicketFailReasonType {
    getTicketFailReasonType {
      label
      value
    }
  }
`;

export const useTicketFailReasonTypes = (
  options?: QueryHookOptions<QueryTicketFailReasonData>
): QueryResult<QueryTicketFailReasonData> =>
  useQuery(GET_TICKET_FAIL_REASON_TYPES, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyTicketFailReasonTypes = (
  options?: LazyQueryHookOptions<QueryTicketFailReasonData>
): LazyQueryResultTuple<QueryTicketFailReasonData, {}> =>
  useLazyQuery(GET_TICKET_FAIL_REASON_TYPES, {
    fetchPolicy: 'network-only',
    ...options,
  });
