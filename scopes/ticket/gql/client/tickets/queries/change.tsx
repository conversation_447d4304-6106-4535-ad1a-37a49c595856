import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { NotificationsResponse } from '../generated-types/graphql';

export type ChangeNotificationsData = {
  changeNotifications: NotificationsResponse | null;
};

export function useLazyChangeNotifications(
  options?: QueryHookOptions<ChangeNotificationsData, { changeId: String }>
): LazyQueryResultTuple<ChangeNotificationsData, { changeId: String }> {
  return useLazyQuery<ChangeNotificationsData, { changeId: String }>(
    gql`
      query ChangeNotifications($changeId: String!) {
        changeNotifications(changeId: $changeId) {
          data {
            id
            gmtCreate
            reportContent
            creatorId
            creatorName
            reportChannel
            changeStatus
            reportObject {
              type
              code
              name
            }
          }
          total
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
