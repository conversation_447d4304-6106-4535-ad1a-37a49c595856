import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, QueryHookOptions } from '@apollo/client';

import type { Query, QueryTicketTaskCountArgs } from '../generated-types/graphql.js';

export const TICKET_TASK_COUNT: DocumentNode = gql`
  query TicketTaskCount($taskType: String!, $taskStatus: String!, $assigneeId: Long) {
    ticketTaskCount(taskType: $taskType, taskStatus: $taskStatus, assigneeId: $assigneeId) {
      data
    }
  }
`;

export type TicketTaskCountGqlData = Pick<Query, 'ticketTaskCount'>;

export function useTicketTaskCount(
  variables: QueryTicketTaskCountArgs,
  options?: QueryHookOptions<TicketTaskCountGqlData, QueryTicketTaskCountArgs>
) {
  return useQuery<TicketTaskCountGqlData, QueryTicketTaskCountArgs>(TICKET_TASK_COUNT, {
    variables,
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTicketTaskCount(
  options?: LazyQueryHookOptions<TicketTaskCountGqlData, QueryTicketTaskCountArgs>
) {
  return useLazyQuery<TicketTaskCountGqlData, QueryTicketTaskCountArgs>(TICKET_TASK_COUNT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
