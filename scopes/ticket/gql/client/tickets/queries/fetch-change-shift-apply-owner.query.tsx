import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchChangeShiftApplyOwnerResponse,
  Maybe,
  QueryFetchChangeShiftApplyOwnerArgs,
} from '../generated-types/graphql';

export type QueryFetchChangeShiftApplyOwner = {
  fetchChangeShiftApplyOwner: Maybe<FetchChangeShiftApplyOwnerResponse>;
};

export const GET_CHANGE_SHIFT_APPLY_OWNER: DocumentNode = gql`
  query FetchChangeShiftApplyOwner($scheduleId: Long!) {
    fetchChangeShiftApplyOwner(scheduleId: $scheduleId) {
      data {
        id
        userName
        locked
        scheduleScene {
          code
          desc
          scene
        }
        enable
        teamLeader
      }
      total
      success
    }
  }
`;

export function useLazyQueryFetchChangeShiftApplyOwner(
  options?: LazyQueryHookOptions<
    QueryFetchChangeShiftApplyOwner,
    QueryFetchChangeShiftApplyOwnerArgs
  >
): LazyQueryResultTuple<QueryFetchChangeShiftApplyOwner, QueryFetchChangeShiftApplyOwnerArgs> {
  return useLazyQuery(GET_CHANGE_SHIFT_APPLY_OWNER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
