import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EventBetaReportSceneInfoResponse,
  QueryEventBetaReportSceneInfoArgs,
} from '../generated-types/graphql';

export type QueryEventBetaReportSceneInfoData = {
  eventBetaReportSceneInfo: EventBetaReportSceneInfoResponse;
};

export const GET_EVENTS_BETA_REPORT_SCENE_INFO: DocumentNode = gql`
  query EventBetaReportSceneInfo($query: EventBetaReportSceneInfoQ!) {
    eventBetaReportSceneInfo(query: $query) {
      data {
        channelList
        userIdList
        roleList {
          roleName
          roleCode
        }
      }
      success
    }
  }
`;

export function useLazyEventBetaReportSceneInfo(
  options?: LazyQueryHookOptions<
    QueryEventBetaReportSceneInfoData,
    QueryEventBetaReportSceneInfoArgs
  >
): LazyQueryResultTuple<QueryEventBetaReportSceneInfoData, QueryEventBetaReportSceneInfoArgs> {
  return useLazyQuery(GET_EVENTS_BETA_REPORT_SCENE_INFO, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
