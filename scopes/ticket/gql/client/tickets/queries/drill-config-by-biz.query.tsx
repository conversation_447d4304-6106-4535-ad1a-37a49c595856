import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  DrillConfigByBizResponse,
  Maybe,
  QueryDrillConfigByBizArgs,
} from '../generated-types/graphql';

export type DrillConfigByBizData = {
  drillConfigByBiz: Maybe<DrillConfigByBizResponse>;
};

export const GET_DRILL_CONFIG_BY_BIZ: DocumentNode = gql`
  query GetDrillConfigByBiz($bizId: String!) {
    drillConfigByBiz(bizId: $bizId) {
      data {
        id
        bizId
        excMajor
        excLevel
        excName
        effectType
        blockGuid
        exercisePlanStepList {
          id
          excPhase
          orderNo
          stepFlag
          stepDetail
          stepMeasure
          stationPosition
          roles
          followMethod
          sla
          slaUnit
        }
      }
    }
  }
`;

export function useDrillConfigByBiz(
  options?: QueryHookOptions<DrillConfigByBizData, QueryDrillConfigByBizArgs>
): QueryResult<DrillConfigByBizData, QueryDrillConfigByBizArgs> {
  return useQuery(GET_DRILL_CONFIG_BY_BIZ, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillConfigByBiz(
  options?: LazyQueryHookOptions<DrillConfigByBizData, QueryDrillConfigByBizArgs>
): LazyQueryResultTuple<DrillConfigByBizData, QueryDrillConfigByBizArgs> {
  return useLazyQuery(GET_DRILL_CONFIG_BY_BIZ, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
