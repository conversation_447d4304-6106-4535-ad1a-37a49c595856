import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { EventBetaResponse, QueryEventBetaArgs } from '../generated-types/graphql';

export type { EventBetaData } from '../generated-types/graphql';

export type QueryEventBetaData = {
  eventBeta: EventBetaResponse;
};

export const GET_EVENT_BETA: DocumentNode = gql`
  query EventBeta($eventId: String!) {
    eventBeta(eventId: $eventId) {
      data {
        eventId
        gmtCreate
        gmtModified
        idcTag
        blockGuid
        eventSourceCode
        eventSourceName
        falseAlarm
        occurTime
        categoryCode
        categoryName
        eventLevelCode
        eventLevelName
        eventTitle
        eventDesc
        ownerId
        ownerName
        createUserId
        createUserName
        eventStatus
        faultType
        auditNewestInstNo
        handoverUserId
        handoverUserName
        faultModelList {
          idcTag
          blockTag
          roomTag
          deviceType
          deviceTag
          name
          guid
          roomName
        }
        changeCause
        changeId
        influenceDesc
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
        auditDesc
        auditFileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
        inHandover
        detectStatus
        detectTime
        detectReason
        levelChangeInfo {
          processId
          originLevelCode
          originLevelName
          targetLevelCode
          targetLevelName
        }
        lastLevelChangeInfo {
          processId
          originLevelCode
          originLevelName
          targetLevelCode
          targetLevelName
          reason
        }
        emergencyInfoList {
          name
          emergencyId
          blockGuid
          categoryCode
          categoryName
          gmtCreate
          gmtModified
          createUserId
          createUserName
          fileInfoList {
            uid
            name
            patialPath
            uploadUser {
              id
              name
            }
            uploadedAt
            ext
            src
            id
            size
            type
            modifiedAt
            targetId
            targetType
          }
        }
        processInstNo
        otDetectInfo {
          firDetectThreshold
          firConfirmed
          firConfirmedTime
          firUpgraded
          firHandleRoleList
          firHandleUserList
          secDetectThreshold
          secConfirmed
          secHandleRoleList
          secHandleUserList
        }
        otRelieveInfo {
          relieveThreshold
          confirmed
          handleRoleList
          handleUserList
        }
        convertToProblem
        questionRiskId
      }
    }
  }
`;

export function useLazyEventBeta(
  options?: LazyQueryHookOptions<QueryEventBetaData, QueryEventBetaArgs>
): LazyQueryResultTuple<QueryEventBetaData, QueryEventBetaArgs> {
  return useLazyQuery(GET_EVENT_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
