import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchChangeShiftAttendanceResultResponse,
  Maybe,
  QueryFetchChangeShiftAttendanceResultArgs,
} from '../generated-types/graphql';

export type QueryFetchChangeShiftAttendanceResult = {
  fetchChangeShiftAttendanceResult: Maybe<FetchChangeShiftAttendanceResultResponse>;
};

export const GET_CHANGE_SHIFT_ATTENDANCE_RESULT: DocumentNode = gql`
  query FetchChangeShiftAttendanceResult($query: FetchChangeShiftAttendanceResultQ!) {
    fetchChangeShiftAttendanceResult(query: $query) {
      data {
        staffId
        onDutyTime
        offDutyTime
      }
      total
      success
    }
  }
`;

export function useLazyQueryFetchChangeShiftAttendanceResult(
  options?: LazyQueryHookOptions<
    QueryFetchChangeShiftAttendanceResult,
    QueryFetchChangeShiftAttendanceResultArgs
  >
): LazyQueryResultTuple<
  QueryFetchChangeShiftAttendanceResult,
  QueryFetchChangeShiftAttendanceResultArgs
> {
  return useLazyQuery(GET_CHANGE_SHIFT_ATTENDANCE_RESULT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
