import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EventsBetaByKeywordResponse,
  QueryEventBetaListByKeywordArgs,
} from '../generated-types/graphql';

export type { EventBetaData } from '../generated-types/graphql';

export type QueryEventBetaListByKeywordData = {
  eventBetaListByKeyword: EventsBetaByKeywordResponse;
};

export const GET_EVENT_BETA_BY_KEYWORD: DocumentNode = gql`
  query EventBetaListByKeyword($query: EventBetaListByKeywordQuery!) {
    eventBetaListByKeyword(query: $query) {
      data {
        eventId
        idcTag
        blockGuid
        eventTitle
        eventLevelCode
        eventLevelName
        categoryCode
        categoryName
        occurTime
        eventStatus
        ownerId
        ownerName
        closeTime
      }
    }
  }
`;

export function useLazyEventBetaListByKeyword(
  options?: LazyQueryHookOptions<QueryEventBetaListByKeywordData, QueryEventBetaListByKeywordArgs>
): LazyQueryResultTuple<QueryEventBetaListByKeywordData, QueryEventBetaListByKeywordArgs> {
  return useLazyQuery(GET_EVENT_BETA_BY_KEYWORD, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
