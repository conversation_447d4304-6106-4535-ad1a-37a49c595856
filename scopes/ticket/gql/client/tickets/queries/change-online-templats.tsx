import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  ChangeOnlineTemplatesResponse,
  QueryChangeOnlineTemplatesArgs,
} from '../generated-types/graphql';

export type {
  ChangeOnlineTemplatesQ,
  ChangeOnlineTemplatesResponse,
  ChangeOnlineTemplatesData,
} from '../generated-types/graphql';

export type ChangeOnlineTemplatesDatas = {
  changeOnlineTemplates: ChangeOnlineTemplatesResponse;
};

export const GET_CHANGE_ONLINE_TEMPLATES: DocumentNode = gql`
  query ChangeOnlineTemplates($query: ChangeOnlineTemplatesQ!) {
    changeOnlineTemplates(query: $query) {
      data {
        templateId
        templateName
        changeType
        riskLevel
        gmtModified
        gmtCreate
        templateStatus
        creatorId
        creatorName
        modifyPersonId
        modifyPersonName
        workFlowId
        reason
        availArea
        effTime
        changeCategory
      }
      total
    }
  }
`;

export function useLazyChangeOnlineTemplates(
  options?: LazyQueryHookOptions<ChangeOnlineTemplatesDatas, QueryChangeOnlineTemplatesArgs>
): LazyQueryResultTuple<ChangeOnlineTemplatesDatas, QueryChangeOnlineTemplatesArgs> {
  return useLazyQuery(GET_CHANGE_ONLINE_TEMPLATES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
