import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  QueryTicketFirstTimeRecipientInfoArgs,
  TicketFirstTimeRecipientInfo,
} from '../generated-types/graphql';

export type QueryTicketFirstTimeRecipientInfoData = {
  ticketFirstTimeRecipientInfo: Maybe<TicketFirstTimeRecipientInfo>;
};

export const GET_TICKET_FIRST_TIME_RECIPIENT_INFO: DocumentNode = gql`
  # Write your query or mutation here
  query GetTicketFirstTimeRecipientInfo($taskNo: String!) {
    ticketFirstTimeRecipientInfo(taskNo: $taskNo) {
      takeTime
      taskAssignee
      taskAssigneeName
    }
  }
`;

export function useTicketFirstTimeRecipientInfo(
  options?: QueryHookOptions<
    QueryTicketFirstTimeRecipientInfoData,
    QueryTicketFirstTimeRecipientInfoArgs
  >
): QueryResult<QueryTicketFirstTimeRecipientInfoData, QueryTicketFirstTimeRecipientInfoArgs> {
  return useQuery(GET_TICKET_FIRST_TIME_RECIPIENT_INFO, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTicketFirstTimeRecipientInfo(
  options?: LazyQueryHookOptions<
    QueryTicketFirstTimeRecipientInfoData,
    QueryTicketFirstTimeRecipientInfoArgs
  >
): LazyQueryResultTuple<
  QueryTicketFirstTimeRecipientInfoData,
  QueryTicketFirstTimeRecipientInfoArgs
> {
  return useLazyQuery(GET_TICKET_FIRST_TIME_RECIPIENT_INFO, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
