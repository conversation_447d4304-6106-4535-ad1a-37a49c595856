import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Maybe, QueryEventRelateTicketsArgs, RelateTicket } from '../generated-types/graphql';

export type QueryEventRelateTicketsData = {
  eventRelateTickets: Maybe<{
    data: RelateTicket[];
    total: number;
  }>;
};

export const GET_EVENT_RELATE_TICKETS: DocumentNode = gql`
  query EventRelateTickets($eventId: Int!, $pageNum: Int!, $pageSize: Int!, $sort: Sort!) {
    eventRelateTickets(eventId: $eventId, pageNum: $pageNum, pageSize: $pageSize, sort: $sort) {
      code
      message
      success
      data {
        relateTicket {
          id
          name
          type
        }
        relateTicketStatus {
          name
          value
        }
        relateTicketDesc
        relateTicketCreateTime
        relateTicketCreateUserId
      }
      total
    }
  }
`;

export function useEventRelateTickets(
  options?: QueryHookOptions<QueryEventRelateTicketsData, QueryEventRelateTicketsArgs>
): QueryResult<QueryEventRelateTicketsData, QueryEventRelateTicketsArgs> {
  return useQuery(GET_EVENT_RELATE_TICKETS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyEventRelateTickets(
  options?: LazyQueryHookOptions<QueryEventRelateTicketsData, QueryEventRelateTicketsArgs>
): LazyQueryResultTuple<QueryEventRelateTicketsData, QueryEventRelateTicketsArgs> {
  return useLazyQuery(GET_EVENT_RELATE_TICKETS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
