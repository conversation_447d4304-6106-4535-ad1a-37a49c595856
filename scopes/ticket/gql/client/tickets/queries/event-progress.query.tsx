import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { EventProgressResponse, QueryEventProgressArgs } from '../generated-types/graphql';

export type { EventProgressData, ProcessRecordInfo } from '../generated-types/graphql';

export type QueryEventProgressData = {
  eventProgress: EventProgressResponse;
};

export const GET_EVENT_PROGRESS: DocumentNode = gql`
  query EventProgress($eventId: String!) {
    eventProgress(eventId: $eventId) {
      data {
        phase
        status
        handlerId
        handlerName
        handleContent
        sla
        startTime
        endTime
        processRecordList {
          handleTime
          handlerId
          handlerName
          handleContent
        }
      }
      total
    }
  }
`;

export function useLazyEventProgress(
  options?: LazyQueryHookOptions<QueryEventProgressData, QueryEventProgressArgs>
): LazyQueryResultTuple<QueryEventProgressData, QueryEventProgressArgs> {
  return useLazyQuery(GET_EVENT_PROGRESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
