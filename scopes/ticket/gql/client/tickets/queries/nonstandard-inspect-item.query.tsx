import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchNonstandardInspectItemResponse,
  Maybe,
  QueryFetchNonstandardInspectItemArgs,
} from '../generated-types/graphql';

export type QueryFetchNonstandardInspectItem = {
  fetchNonstandardInspectItem: Maybe<FetchNonstandardInspectItemResponse>;
};

export const GET_NONSTANDARD_INSPECT_ITEM: DocumentNode = gql`
  query FetchNonstandardInspectItem($query: FetchNonstandardInspectItemQ!) {
    fetchNonstandardInspectItem(query: $query) {
      data {
        id
        taskNo
        roomGuid
        subjectName
        exDesc
        gmtCreate
        fileInfoList {
          ...MyMcUploadFile
        }
      }
      total
      success
    }
  }

  fragment MyMcUploadFile on McUploadFile {
    uid
    name
    patialPath
    uploadUser {
      id
      name
    }
    uploadedAt
    ext
    src
    id
    size
    type
    modifiedAt
    targetId
    targetType
  }
`;

export function useLazyQueryFetchNonstandardInspectItem(
  options?: LazyQueryHookOptions<
    QueryFetchNonstandardInspectItem,
    QueryFetchNonstandardInspectItemArgs
  >
): LazyQueryResultTuple<QueryFetchNonstandardInspectItem, QueryFetchNonstandardInspectItemArgs> {
  return useLazyQuery(GET_NONSTANDARD_INSPECT_ITEM, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
