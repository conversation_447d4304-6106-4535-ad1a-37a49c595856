import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  PduDevicesOnRackResponse,
  QueryPduDevicesOnRackArgs,
} from '../generated-types/graphql';

export type QueryPduDevicesOnRackData = {
  pduDevicesOnRack: Maybe<PduDevicesOnRackResponse>;
};

export const GET_PDU_DEVICES_ON_RACK: DocumentNode = gql`
  query GetPduDevicesOnRack(
    $idcTag: String!
    $blockTag: String!
    $gridGuidList: [String!]!
    $deviceTypeList: [String!]!
  ) {
    pduDevicesOnRack(
      idcTag: $idcTag
      blockTag: $blockTag
      gridGuidList: $gridGuidList
      deviceTypeList: $deviceTypeList
    ) {
      data {
        gridGuid
        relateDeviceList {
          idcTag
          blockTag
          roomTag
          deviceName
          deviceGuid
          assetNo
          extendPosition
          deviceType
          deviceTag
          deviceLabel
        }
      }
      total
    }
  }
`;

export function usePduDevicesOnRack(
  options?: QueryHookOptions<QueryPduDevicesOnRackData, QueryPduDevicesOnRackArgs>
): QueryResult<QueryPduDevicesOnRackData, QueryPduDevicesOnRackArgs> {
  return useQuery(GET_PDU_DEVICES_ON_RACK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyPduDevicesOnRack(
  options?: LazyQueryHookOptions<QueryPduDevicesOnRackData, QueryPduDevicesOnRackArgs>
): LazyQueryResultTuple<QueryPduDevicesOnRackData, QueryPduDevicesOnRackArgs> {
  return useLazyQuery(GET_PDU_DEVICES_ON_RACK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
