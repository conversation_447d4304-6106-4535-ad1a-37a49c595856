import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { ChangeVersionsResponse } from '../generated-types/graphql';

export type QueryChangeVersionsData = {
  changeVersions: ChangeVersionsResponse;
};

export const GET_CHANGE_VERSIONS: DocumentNode = gql`
  query ChangeVersions {
    changeVersions {
      data
    }
  }
`;

export function useLazyChangeVersions(
  options?: LazyQueryHookOptions<QueryChangeVersionsData, {}>
): LazyQueryResultTuple<QueryChangeVersionsData, {}> {
  return useLazyQuery(GET_CHANGE_VERSIONS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
