import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  DrillConfigsResponse,
  Maybe,
  QueryDrillConfigsArgs,
} from '../generated-types/graphql';

export type DrillConfigsData = {
  drillConfigs: Maybe<DrillConfigsResponse>;
};

export const GET_DRILL_CONFIGS: DocumentNode = gql`
  query GetDrillConfigs(
    $blockGuidList: [String!]
    $effectType: String
    $excMajor: String
    $excLevelList: [String!]
    $creatorId: Int
    $startDate: Long
    $endDate: Long
    $excName: String
    $pageNum: Int!
    $pageSize: Int!
  ) {
    drillConfigs(
      blockGuidList: $blockGuidList
      effectType: $effectType
      excMajor: $excMajor
      excLevelList: $excLevelList
      creatorId: $creatorId
      startDate: $startDate
      endDate: $endDate
      excName: $excName
      pageNum: $pageNum
      pageSize: $pageSize
    ) {
      data {
        id
        bizId
        excMajor
        excLevel
        excName
        effectType
        effectDomain
        creatorId
        creatorName
        gmtCreate
        count
      }
      total
    }
  }
`;

export function useDrillConfigs(
  options?: QueryHookOptions<DrillConfigsData, QueryDrillConfigsArgs>
): QueryResult<DrillConfigsData, QueryDrillConfigsArgs> {
  return useQuery(GET_DRILL_CONFIGS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillConfigs(
  options?: LazyQueryHookOptions<DrillConfigsData, QueryDrillConfigsArgs>
): LazyQueryResultTuple<DrillConfigsData, QueryDrillConfigsArgs> {
  return useLazyQuery(GET_DRILL_CONFIGS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
