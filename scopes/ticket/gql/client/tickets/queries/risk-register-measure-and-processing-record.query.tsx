import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  QueryRiskRegisterMeasureAndProcessingRecordArgs,
  RiskRegisterMeasureAndProcessingRecordResponse,
} from '../generated-types/graphql';

export type { RiskRegisterMeasureAndProcessingRecordResponse } from '../generated-types/graphql';

export type RiskRegisterMeasureAndProcessingRecordData = {
  riskRegisterMeasureAndProcessingRecord: RiskRegisterMeasureAndProcessingRecordResponse[];
};

export const GET_RISK_REGISTER_MEASURE_AND_PROCESSING_RECORD: DocumentNode = gql`
  query RiskRegisterMeasureAndProcessingRecord($query: RiskRegisterMeasureAndProcessingRecordQ!) {
    riskRegisterMeasureAndProcessingRecord(query: $query) {
      riskId
      measureId
      measureType
      measureDesc
      measureStatus
      followUserId
      planCompleteTime
      completeTime
      optRecordList {
        optRecordId
        handlerUserId
        handleTime
        handleContent
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
      }
    }
  }
`;

export function useLazyRiskRegisterMeasureAndProcessingRecord(
  options?: LazyQueryHookOptions<
    RiskRegisterMeasureAndProcessingRecordData,
    QueryRiskRegisterMeasureAndProcessingRecordArgs
  >
): LazyQueryResultTuple<
  RiskRegisterMeasureAndProcessingRecordData,
  QueryRiskRegisterMeasureAndProcessingRecordArgs
> {
  return useLazyQuery(GET_RISK_REGISTER_MEASURE_AND_PROCESSING_RECORD, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
