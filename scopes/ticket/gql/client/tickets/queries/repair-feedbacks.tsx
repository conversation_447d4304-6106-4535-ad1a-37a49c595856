import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Maybe, RepairFeedbacksResponse } from '../generated-types/graphql';

export type QueryRepairFeedbacksData = {
  repairFeedbacks: Maybe<RepairFeedbacksResponse>;
};

export const GET_REPAIR_FEEDBACKS: DocumentNode = gql`
  query RepairFeedbacks($taskNo: String!) {
    repairFeedbacks(taskNo: $taskNo) {
      data {
        id
        feedback
        resolved
        gmtCreate
        operatorId
        operatorName
        gmtModified
        fileNum
      }
      total
    }
  }
`;

export function useRepairFeedbacks(
  options?: QueryHookOptions<QueryRepairFeedbacksData, {}>
): QueryResult<QueryRepairFeedbacksData, {}> {
  return useQuery(GET_REPAIR_FEEDBACKS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyRepairFeedbacks(
  options?: LazyQueryHookOptions<QueryRepairFeedbacksData, {}>
): LazyQueryResultTuple<QueryRepairFeedbacksData, {}> {
  return useLazyQuery(GET_REPAIR_FEEDBACKS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
