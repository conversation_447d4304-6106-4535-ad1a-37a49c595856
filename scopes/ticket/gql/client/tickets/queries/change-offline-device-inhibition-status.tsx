import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  ChangeOfflineDeviceInhibitionStatusResponse,
  Maybe,
  QueryChangeOfflineDeviceInhibitionStatusArgs,
} from '../generated-types/graphql';

export type QueryChangeOfflineDeviceInhibitionStatusData = {
  changeOfflineDeviceInhibitionStatus: Maybe<ChangeOfflineDeviceInhibitionStatusResponse>;
};

export const GET_CHANGE_OFFLINE_DEVICE_INHIBITION_STATUS: DocumentNode = gql`
  query ChangeOfflineDeviceInhibitionStatus($changeOrderId: String!) {
    changeOfflineDeviceInhibitionStatus(changeOrderId: $changeOrderId) {
      data
    }
  }
`;

export function useLazyChangeOfflineDeviceInhibitionStatus(
  options?: LazyQueryHookOptions<
    QueryChangeOfflineDeviceInhibitionStatusData,
    QueryChangeOfflineDeviceInhibitionStatusArgs
  >
): LazyQueryResultTuple<
  QueryChangeOfflineDeviceInhibitionStatusData,
  QueryChangeOfflineDeviceInhibitionStatusArgs
> {
  return useLazyQuery(GET_CHANGE_OFFLINE_DEVICE_INHIBITION_STATUS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
