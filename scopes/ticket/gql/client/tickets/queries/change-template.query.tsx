import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchChangeTemplateResponse,
  Maybe,
  QueryChangeTemplateArgs,
} from '../generated-types/graphql';

export type { FetchChangeTemplatesData, FetchChangeTemplatesQ } from '../generated-types/graphql';
export type QueryChangeTemplateData = {
  changeTemplate: Maybe<FetchChangeTemplateResponse>;
};

export const GET_CHANGE_TEMPLATE: DocumentNode = gql`
  query ChangeTemplate($templateId: String!) {
    changeTemplate(templateId: $templateId) {
      data {
        id
        gmtCreate
        gmtModified
        workFlowId
        templateId
        templateStatus
        templateName
        changeType
        riskLevel
        creatorId
        modifyPersonId
        executeRoleCode
        executeRoleName
        stepList {
          stepOrder
          fileInfoList {
            uid
            name
            patialPath
            uploadUser {
              id
              name
            }
            uploadedAt
            ext
            src
            id
            size
            type
            modifiedAt
            targetId
            targetType
          }
        }
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
        stepFileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
        changeVersion
        sourceChangeId
        sourceChangeTitle
        availArea
        reason
        availDate
        effTime
        postponeApprovalId
      }
      success
      code
      message
    }
  }
`;

export function useLazyChangeTemplate(
  options?: LazyQueryHookOptions<QueryChangeTemplateData, QueryChangeTemplateArgs>
): LazyQueryResultTuple<QueryChangeTemplateData, QueryChangeTemplateArgs> {
  return useLazyQuery(GET_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
