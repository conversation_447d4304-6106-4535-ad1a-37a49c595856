import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  QueryTicketNotificationsArgs,
  TicketNotification,
} from '../generated-types/graphql';

export type QueryTicketNotificationsData = {
  ticketNotifications: Maybe<{
    data: TicketNotification[];
    total: number;
  }>;
};

export const GET_TICKET_NOTIFICATIONS: DocumentNode = gql`
  query GetTicketNotifications(
    $bizId: String!
    $bizType: String!
    $eventCode: String!
    $pageNum: Int!
    $pageSize: Int!
  ) {
    ticketNotifications(
      bizId: $bizId
      bizType: $bizType
      eventCode: $eventCode
      pageNum: $pageNum
      pageSize: $pageSize
    ) {
      data {
        creatorId
        gmtCreate
        creatorName
        reportChannel
        eventCode
        bizId
        bizType
        bizStatus {
          color
          name
          value
        }
        reportContent
        reportObjectList {
          type
          code
          name
        }
      }
      total
    }
  }
`;

export function useTicketNotifications(
  options?: QueryHookOptions<QueryTicketNotificationsData, QueryTicketNotificationsArgs>
): QueryResult<QueryTicketNotificationsData, QueryTicketNotificationsArgs> {
  return useQuery(GET_TICKET_NOTIFICATIONS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTicketNotifications(
  options?: LazyQueryHookOptions<QueryTicketNotificationsData, QueryTicketNotificationsArgs>
): LazyQueryResultTuple<QueryTicketNotificationsData, QueryTicketNotificationsArgs> {
  return useLazyQuery(GET_TICKET_NOTIFICATIONS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
