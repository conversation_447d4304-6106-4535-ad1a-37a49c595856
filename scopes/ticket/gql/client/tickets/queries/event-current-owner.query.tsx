import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type {
  FetchEventCurrentOwnerResponse,
  QueryFetchEventCurrentOwnerArgs,
} from '../generated-types/graphql';

export type { FetchEventCurrentOwnerData } from '../generated-types/graphql';
export type EventCurrentOwnerData = {
  fetchEventCurrentOwner: FetchEventCurrentOwnerResponse;
};

export function useLazyEventCurrentOwner(
  options?: QueryHookOptions<EventCurrentOwnerData, QueryFetchEventCurrentOwnerArgs>
): LazyQueryResultTuple<EventCurrentOwnerData, QueryFetchEventCurrentOwnerArgs> {
  return useLazyQuery<EventCurrentOwnerData, QueryFetchEventCurrentOwnerArgs>(
    gql`
      query FetchEventCurrentOwner($query: FetchEventCurrentOwnerQ!) {
        fetchEventCurrentOwner(query: $query) {
          data {
            id
            userName
            loginName
            mobile
            email
            deleted
          }
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
