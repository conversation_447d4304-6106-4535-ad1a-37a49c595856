import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { RiskQuestionKnowledgeCodeResponse } from '../generated-types/graphql';

export type { EventBetaData } from '../generated-types/graphql';

export type RiskQuestionKnowledgeCodeData = {
  riskQuestionKnowledgeCode: RiskQuestionKnowledgeCodeResponse;
};

export const GET_EVENT_BETA_BY_KEYWORD: DocumentNode = gql`
  query RiskQuestionKnowledgeCode {
    riskQuestionKnowledgeCode {
      data
      message
      success
    }
  }
`;

export function useLazyRiskQuestionKnowledgeCode(
  options?: LazyQueryHookOptions<RiskQuestionKnowledgeCodeData, {}>
): LazyQueryResultTuple<RiskQuestionKnowledgeCodeData, {}> {
  return useLazyQuery(GET_EVENT_BETA_BY_KEYWORD, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
