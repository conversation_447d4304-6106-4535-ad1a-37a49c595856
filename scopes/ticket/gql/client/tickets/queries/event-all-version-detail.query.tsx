import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchEventAllVersionDetailData,
  QueryEventAllVersionDetailArgs,
} from '../generated-types/graphql';

export type { EventBetaData } from '../generated-types/graphql';

export type QueryEventAllVersionDetailData = {
  eventAllVersionDetail: FetchEventAllVersionDetailData;
};

export const GET_EVENT_ALL_VERSION_DETAIL: DocumentNode = gql`
  query EventAllVersionDetail($query: FetchEventAllVersionDetailQuery!) {
    eventAllVersionDetail(query: $query) {
      data {
        idcTag
        blockGuid
        eventDesc
        eventTitle
        targetType
        targetGuidList
      }
    }
  }
`;

export function useLazyEventAllVersionDetail(
  options?: LazyQueryHookOptions<QueryEventAllVersionDetailData, QueryEventAllVersionDetailArgs>
): LazyQueryResultTuple<QueryEventAllVersionDetailData, QueryEventAllVersionDetailArgs> {
  return useLazyQuery(GET_EVENT_ALL_VERSION_DETAIL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
