import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { ChangeOnlineResponse, QueryChangeOnlineArgs } from '../generated-types/graphql';

export type { ChangeOnlineData } from '../generated-types/graphql';

export type ChangeOnline = {
  changeOnline: ChangeOnlineResponse;
};

export const GET_CHANGE_ONLINE: DocumentNode = gql`
  query ChangeOnline($changeOrderId: String!) {
    changeOnline(changeOrderId: $changeOrderId) {
      success
      code
      data {
        changeOrderId
        changeStatus
        title
        idcTag
        blockGuid
        changeType
        reason
        planStartTime
        planEndTime
        riskLevel
        changeCategory
        creatorId
        creatorName
        templateId
        templateName
        summery
        id
        summarizePersonId
        summarizePersonName
        realStartTime
        realEndTime
        workFlowId
        stopReason
        exeWay
        exeResult
        changeInfluence
        summeryApprovalId
        failReason
        changeVersion
        changeApprovalInfoList {
          changeId
          instType
          instId
          instStatus
          applyUseId
          applyUseName
          applyReason
          extJson {
            oldPlanEndDate
            newPlanEndDate
          }
        }
        extJson {
          statusInfoList {
            changeStatus
            startTime
          }
          lastPlanStartTime
          lastPlanEndTime
          modifyPersonId
          modifyPersonName
          reasonType
          offLineOperatorId
          offLineOperatorName
          cancelReason
          rescheduleReason
          rescheduleStartTime
          rescheduleEndTime
          changeTimeList
          changeSourceInfoList {
            sourceType
            sourceId
            title
          }
          changeResponsibleUserInfoList {
            userName
            userId
          }
        }
        currentOperatorList {
          userName
          userId
        }
        stepList {
          stepOrder
          id
          stepStatus
          stepName
          operator
          operatorId
          operatorName
          opType
          opObjectName
          opObjectCode
          pointCode
          pointName
          pointValueText
          expectedValue
          stepDesc
          startTime
          endTime
          extendJson {
            stepResponsibleUserList {
              userName
              userId
            }

            stepInhibitionItemInfoList {
              deviceType
              deviceTag
              deviceGuid
              deviceName
              roomGuid
              roomType
              maxInfluencesStep
              inhibitionModelId
              inhibitionModelName
              inhibitionItemIdList
            }

            stepDeviceInfoList {
              deviceGuid
              deviceName
              deviceTag
              roomTag
              checkPointValue
              deviceLabel
            }
          }
        }
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
      }
    }
  }
`;

export function useLazyChangeOnline(
  options?: LazyQueryHookOptions<ChangeOnline, QueryChangeOnlineArgs>
): LazyQueryResultTuple<ChangeOnline, QueryChangeOnlineArgs> {
  return useLazyQuery(GET_CHANGE_ONLINE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
