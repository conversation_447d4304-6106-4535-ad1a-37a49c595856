import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  ChangeOfflineRelateTicket,
  Maybe,
  QueryChangeOfflineRelateTicketsArgs,
} from '../generated-types/graphql';

export type { ChangeOfflineRelateTicket } from '../generated-types/graphql';
export type QueryTasksData = {
  changeOfflineRelateTickets: Maybe<{
    data: ChangeOfflineRelateTicket[];
    total: number;
  }>;
};
export const GET_CHANGE_OFFLINE_RELATE_TICKETS: DocumentNode = gql`
  query ChangeOfflineRelateTickets($changeOrderId: String!) {
    changeOfflineRelateTickets(changeOrderId: $changeOrderId) {
      data {
        relateNo
        relateType
        desc
        status
        creatorId
        gmtCreate
      }
      total
    }
  }
`;

export function useChangeOfflineRelateTickets(
  options?: LazyQueryHookOptions<QueryTasksData, QueryChangeOfflineRelateTicketsArgs>
): LazyQueryResultTuple<QueryTasksData, QueryChangeOfflineRelateTicketsArgs> {
  return useLazyQuery(GET_CHANGE_OFFLINE_RELATE_TICKETS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
