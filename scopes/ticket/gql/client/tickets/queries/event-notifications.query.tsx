import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  EventNotification,
  Maybe,
  QueryEventNotificationsArgs,
} from '../generated-types/graphql';

export type QueryEventNotificationsData = {
  eventNotifications: Maybe<{
    data: EventNotification[];
    total: number;
  }>;
};

export const GET_EVENT_NOTIFICATIONS: DocumentNode = gql`
  # Write your query or mutation here
  query GetEventNotifications($eventId: Long!, $pageNum: Int!, $pageSize: Int!) {
    eventNotifications(eventId: $eventId, pageNum: $pageNum, pageSize: $pageSize) {
      data {
        id
        eventId
        idcTag
        blockTag
        eventLevel
        eventStatus
        eventSource
        eventCategory
        occurTime
        eventDesc
        eventInfluence
        eventProgress
        reportToRoleNames
        reportToPersons
        reportToRoleCodes
        reportChannel
        creatorId
        creatorName
        reportTime
        subscribeCode
        infoType
        causeDevices
        reportContent
        causeDesc
        eventFirstLine
        reportToObjects
      }

      total
    }
  }
`;

export function useEventNotifications(
  options?: QueryHookOptions<QueryEventNotificationsData, QueryEventNotificationsArgs>
): QueryResult<QueryEventNotificationsData, QueryEventNotificationsArgs> {
  return useQuery(GET_EVENT_NOTIFICATIONS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyEventNotifications(
  options?: LazyQueryHookOptions<QueryEventNotificationsData, QueryEventNotificationsArgs>
): LazyQueryResultTuple<QueryEventNotificationsData, QueryEventNotificationsArgs> {
  return useLazyQuery(GET_EVENT_NOTIFICATIONS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
