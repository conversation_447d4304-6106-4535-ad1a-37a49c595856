import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EmergencyProcessDetailResponse,
  QueryEmergencyProcessDetailArgs,
} from '../generated-types/graphql';

export type QueryEmergencyProcessDetailData = {
  emergencyProcessDetail: EmergencyProcessDetailResponse;
};

export const GET_EMERGENCY_PROCESS_DETAIL: DocumentNode = gql`
  query EmergencyProcessDetail($emergencyId: String!) {
    emergencyProcessDetail(emergencyId: $emergencyId) {
      data {
        name
        emergencyId
        blockGuid
        categoryCode
        categoryName
        gmtCreate
        gmtModified
        createUserId
        createUserName
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
      }
      success
      code
      message
    }
  }
`;

export function useLazyEmergencyProcessDetail(
  options?: LazyQueryHookOptions<QueryEmergencyProcessDetailData, QueryEmergencyProcessDetailArgs>
): LazyQueryResultTuple<QueryEmergencyProcessDetailData, QueryEmergencyProcessDetailArgs> {
  return useLazyQuery(GET_EMERGENCY_PROCESS_DETAIL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
