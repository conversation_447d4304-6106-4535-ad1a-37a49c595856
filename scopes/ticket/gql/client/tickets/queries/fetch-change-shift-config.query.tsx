import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryResult,
} from '@apollo/client';

import type { FetchChangeShiftConfigResponse, Maybe } from '../generated-types/graphql';

export type QueryFetchChangeShiftConfig = {
  fetchChangeShiftConfig: Maybe<FetchChangeShiftConfigResponse>;
};

export const GET_CHANGE_SHIFT_CONFIG: DocumentNode = gql`
  query FetchChangeShiftConfig {
    fetchChangeShiftConfig {
      handoverDutyCheck
      handoverDutyCheckType
      success
    }
  }
`;

export function useChangeShiftConfig(): QueryResult<QueryFetchChangeShiftConfig> {
  return useQuery(GET_CHANGE_SHIFT_CONFIG, {
    fetchPolicy: 'network-only',
  });
}

export function useLazyChangeShiftConfig(
  options?: LazyQueryHookOptions<QueryFetchChangeShiftConfig>
): LazyQueryResultTuple<QueryFetchChangeShiftConfig, {}> {
  return useLazyQuery(GET_CHANGE_SHIFT_CONFIG, { fetchPolicy: 'network-only', ...options });
}
