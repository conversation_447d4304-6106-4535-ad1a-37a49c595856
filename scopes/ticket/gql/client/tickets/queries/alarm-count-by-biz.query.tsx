import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { AlarmCountByBiz, Maybe, QueryAlarmCountByBizArgs } from '../generated-types/graphql';

export type QueryAlarmCountByBizData = {
  alarmCountByBiz: Maybe<{
    data: AlarmCountByBiz;
  }>;
};

export const GET_ALARM_COUNT_BY_BIZ: DocumentNode = gql`
  query AlarmCountByBiz($query: AlarmCountByBizQ!) {
    alarmCountByBiz(query: $query) {
      data {
        totalCount
        RECOVER
        TRIGGER
      }
    }
  }
`;

export function useLazyAlarmCountByBiz(
  options?: LazyQueryHookOptions<QueryAlarmCountByBizData, QueryAlarmCountByBizArgs>
): LazyQueryResultTuple<QueryAlarmCountByBizData, QueryAlarmCountByBizArgs> {
  return useLazyQuery(GET_ALARM_COUNT_BY_BIZ, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
