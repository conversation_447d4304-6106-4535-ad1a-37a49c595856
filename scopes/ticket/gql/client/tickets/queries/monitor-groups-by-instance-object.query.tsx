import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  MonitorGroupsByInstanceObjectResponse,
  QueryMonitorGroupsByInstanceObjectArgs,
} from '../generated-types/graphql';

export type { MonitorGroupsByInstanceObjectJson } from '../generated-types/graphql';

export type QueryMonitorGroupsByInstanceObjectData = {
  monitorGroupsByInstanceObject: MonitorGroupsByInstanceObjectResponse;
};

export const GET_MONITOR_GROUPS_BY_INSTANCE_OBJECT: DocumentNode = gql`
  query MonitorGroupsByInstanceObject($query: MonitorGroupsByInstanceObjectQuery!) {
    monitorGroupsByInstanceObject(query: $query) {
      data {
        id
        name
        targetId
        targetType
        deviceName
        deviceLabel
        gridTag
        columnTag
        idcTag
        blockTag
        roomTag
        roomName
        pointNum
        inhibitionModelId
        inhibitionModelName
        roomType
        deviceTag
        deviceGuid
      }
      total
    }
  }
`;

export function useLazyMonitorGroupsByInstanceObject(
  options?: LazyQueryHookOptions<
    QueryMonitorGroupsByInstanceObjectData,
    QueryMonitorGroupsByInstanceObjectArgs
  >
): LazyQueryResultTuple<
  QueryMonitorGroupsByInstanceObjectData,
  QueryMonitorGroupsByInstanceObjectArgs
> {
  return useLazyQuery(GET_MONITOR_GROUPS_BY_INSTANCE_OBJECT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
