import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { DrillConfigResponse, Maybe, QueryDrillConfigArgs } from '../generated-types/graphql';

export type DrillConfigData = {
  drillConfig: Maybe<DrillConfigResponse>;
};

export const GET_DRILL_CONFIG: DocumentNode = gql`
  query GetDrillConfig($id: Long!) {
    drillConfig(id: $id) {
      data {
        id
        bizId
        excMajor
        excLevel
        excName
        effectType
        blockGuid
        exercisePlanStepList {
          id
          exerciseId
          creatorId
          creatorName
          gmtCreate
          excPhase
          orderNo
          stepFlag
          stepDetail
          stepMeasure
          stationPosition
          roles
          followMethod
          sla
          slaUnit
        }
      }
    }
  }
`;

export function useDrillConfig(
  options?: QueryHookOptions<DrillConfigData, QueryDrillConfigArgs>
): QueryResult<DrillConfigData, QueryDrillConfigArgs> {
  return useQuery(GET_DRILL_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyDrillConfig(
  options?: LazyQueryHookOptions<DrillConfigData, QueryDrillConfigArgs>
): LazyQueryResultTuple<DrillConfigData, QueryDrillConfigArgs> {
  return useLazyQuery(GET_DRILL_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
