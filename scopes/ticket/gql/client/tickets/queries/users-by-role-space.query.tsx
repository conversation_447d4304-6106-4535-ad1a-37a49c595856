import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  QueryUsersByRoleSpaceArgs,
  UsersByRoleSpaceResponse,
} from '../generated-types/graphql';

export type { RiskRegisterMeasureAndProcessingRecordResponse } from '../generated-types/graphql';

export type UsersByRoleSpaceData = {
  usersByRoleSpace: UsersByRoleSpaceResponse[];
};

export const GET_USERS_BY_ROLE_SPACE: DocumentNode = gql`
  query UsersByRoleSpace($query: UsersByRoleSpaceQ!) {
    usersByRoleSpace(query: $query) {
      id
      userName
      loginName
      mobile
      email
    }
  }
`;

export function useLazyUsersByRoleSpace(
  options?: LazyQueryHookOptions<UsersByRoleSpaceData, QueryUsersByRoleSpaceArgs>
): LazyQueryResultTuple<UsersByRoleSpaceData, QueryUsersByRoleSpaceArgs> {
  return useLazyQuery(GET_USERS_BY_ROLE_SPACE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
