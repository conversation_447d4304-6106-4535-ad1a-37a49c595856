import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchFuzzyChangesResponse,
  Maybe,
  QueryFetchFuzzyChangesArgs,
} from '../generated-types/graphql';

export type QueryFetchFuzzyChanges = {
  fetchFuzzyChanges: Maybe<FetchFuzzyChangesResponse>;
};

export const GET_FUZZY_CHANGES: DocumentNode = gql`
  query FetchFuzzyChanges(
    $queryCondition: String!
    $blockGuid: String!
    $changeVersionList: [Long!]
  ) {
    fetchFuzzyChanges(
      queryCondition: $queryCondition
      blockGuid: $blockGuid
      changeVersionList: $changeVersionList
    ) {
      data {
        title
        changeOrderId
      }
      total
    }
  }
`;

export function useFetchFuzzyChanges(
  options?: LazyQueryHookOptions<QueryFetchFuzzyChanges, QueryFetchFuzzyChangesArgs>
): LazyQueryResultTuple<QueryFetchFuzzyChanges, QueryFetchFuzzyChangesArgs> {
  return useLazyQuery(GET_FUZZY_CHANGES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
