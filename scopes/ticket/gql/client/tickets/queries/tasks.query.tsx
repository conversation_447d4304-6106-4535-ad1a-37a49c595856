import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Maybe, QueryTasksArgs, TaskJson } from '../generated-types/graphql';

export type QueryTasksData = {
  tasks: Maybe<{
    data: TaskJson[];
    total: number;
  }>;
};
export const GET_TASKS: DocumentNode = gql`
  query GetTasks(
    $name: String
    $jobTypeList: [String!]
    $subJobType: String
    $blockGuidList: [String!]
    $periodUnit: String
    $isInvalid: String
    $taskStatus: String
    $creatorId: String
    $pageNum: Int!
    $pageSize: Int!
    $manageType: String
    $endTime: Long
    $startTime: Long
    $startDateOfUpdate: Long
    $endDateOfUpdate: Long
    $sortByField: String
    $defineDesc: Boolean
    $sortInfo: String
  ) {
    tasks(
      name: $name
      jobTypeList: $jobTypeList
      subJobType: $subJobType
      blockGuidList: $blockGuidList
      periodUnit: $periodUnit
      isInvalid: $isInvalid
      taskStatus: $taskStatus
      creatorId: $creatorId
      pageNum: $pageNum
      pageSize: $pageSize
      manageType: $manageType
      endTime: $endTime
      startTime: $startTime
      startDateOfUpdate: $startDateOfUpdate
      endDateOfUpdate: $endDateOfUpdate
      sortByField: $sortByField
      defineDesc: $defineDesc
      sortInfo: $sortInfo
    ) {
      data {
        id
        name
        guidePeriod
        blockGuid
        planType
        mopType
        mopCount
        periodUnit
        isActivated
        isInEffect
        lastExecuteTime
        creator {
          id
          name
        }
        modifyUser {
          id
          name
        }
        createTime
        modifyTime
        blockScope {
          blockGuid
          scopeType
          scopeFlag
        }
        lastExecuteResult
        repeatCycle {
          period
          periodUnit
          dayConfig {
            days
            isRemove
          }
          atDayConfig {
            month
            atDayType
            isLast
            sortNum
          }
        }
        allowTriggerTime
        aggBlockScope {
          blockGuidList
          scopeType
          scopeFlag
          scopeFlagInfoList {
            flagName
            scopeFlag
          }
        }
        endTime
        splitor {
          splitType
          splitParam
        }
        jobSla
        slaUnit
        jobItemList {
          id
          jobId
          scheduleId
          name
          itemType
          scheduleType
          jobTypeName
          jobTypeCode
          subJobTypeName
          subJobTypeCode
          gmtCreate
        }
        unFinishTaskNoList
        finishTaskNoList
        manageType
        schLevel
        taskResultNums
      }
      total
    }
  }
`;

export function useTasks(
  options?: QueryHookOptions<QueryTasksData, QueryTasksArgs>
): QueryResult<QueryTasksData, QueryTasksArgs> {
  return useQuery(GET_TASKS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTasks(
  options?: LazyQueryHookOptions<QueryTasksData, QueryTasksArgs>
): LazyQueryResultTuple<QueryTasksData, QueryTasksArgs> {
  return useLazyQuery(GET_TASKS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
