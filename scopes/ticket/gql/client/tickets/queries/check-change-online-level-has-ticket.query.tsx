import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  CheckChangeOnlineLevelHasTicketResponse,
  QueryCheckChangeOnlineLevelHasTicketArgs,
} from '../generated-types/graphql';

export type { CheckChangeOnlineLevelHasTicketData } from '../generated-types/graphql';

export type CheckChangeOnlineLevelHasTicketsData = {
  checkChangeOnlineLevelHasTicket: CheckChangeOnlineLevelHasTicketResponse;
};

export const CHECK_CHANGE_ONLINE_LEVEL_HAS_TICKET: DocumentNode = gql`
  query CheckChangeOnlineLevelHasTicket($query: CheckChangeOnlineLevelHasTicketQ!) {
    checkChangeOnlineLevelHasTicket(query: $query) {
      data {
        riskLevel
        riskLevelName
        changeOrderId
        title
      }
      total
    }
  }
`;

export function useLazyCheckChangeOnlineLevelHasTicket(
  options?: LazyQueryHookOptions<
    CheckChangeOnlineLevelHasTicketsData,
    QueryCheckChangeOnlineLevelHasTicketArgs
  >
): LazyQueryResultTuple<
  CheckChangeOnlineLevelHasTicketsData,
  QueryCheckChangeOnlineLevelHasTicketArgs
> {
  return useLazyQuery(CHECK_CHANGE_ONLINE_LEVEL_HAS_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
