import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query } from '../generated-types/graphql';

export type QueryTicketFailReasonData = Pick<Query, 'getTicketDelayTypes'>;

export const GET_TICKET_DELAY_TYPES = gql`
  query GetTicketDelayType {
    getTicketDelayTypes {
      label
      value
    }
  }
`;

export const useTicketDelayTypes = (
  options?: QueryHookOptions<QueryTicketFailReasonData>
): QueryResult<QueryTicketFailReasonData> =>
  useQuery(GET_TICKET_DELAY_TYPES, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyTicketDelayTypes = (
  options?: LazyQueryHookOptions<QueryTicketFailReasonData>
): LazyQueryResultTuple<QueryTicketFailReasonData, {}> =>
  useLazyQuery(GET_TICKET_DELAY_TYPES, {
    fetchPolicy: 'network-only',
    ...options,
  });
