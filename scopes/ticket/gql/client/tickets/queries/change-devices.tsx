import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  ChangeDeviceList,
  Maybe,
  QueryChangeOrderDevicesArgs,
} from '../generated-types/graphql';

export type { ChangeDeviceList } from '../generated-types/graphql';
export type QueryChangeOrderDevicesData = {
  changeOrderDevices: Maybe<{
    data: ChangeDeviceList[];
    total: number;
  }>;
};

export const GET_CHANGE_ORDER_DEVICES: DocumentNode = gql`
  query ChangeOrderDevices($query: ChangeOrderDevicesQuery!) {
    changeOrderDevices(query: $query) {
      data {
        deviceGuid
        deviceName
        roomTag
        deviceType
        inhibition
      }
      total
    }
  }
`;

export function useLazyChangeOrderDevices(
  options?: LazyQueryHookOptions<QueryChangeOrderDevicesData, QueryChangeOrderDevicesArgs>
): LazyQueryResultTuple<QueryChangeOrderDevicesData, QueryChangeOrderDevicesArgs> {
  return useLazyQuery(GET_CHANGE_ORDER_DEVICES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
