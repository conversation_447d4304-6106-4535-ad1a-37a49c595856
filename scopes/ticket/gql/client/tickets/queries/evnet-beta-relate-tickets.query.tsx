import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EvnetBetaRelateTicketsResponse,
  QueryEvnetBetaRelateTicketsArgs,
} from '../generated-types/graphql';

export type { EvnetBetaRelateTicketsJson } from '../generated-types/graphql';

export type EvnetBetaRelateTicketsaData = {
  evnetBetaRelateTickets: EvnetBetaRelateTicketsResponse;
};

export const GET_EVENTS_BETA: DocumentNode = gql`
  query EvnetBetaRelateTickets($query: EvnetBetaRelateTicketsQuery!) {
    evnetBetaRelateTickets(query: $query) {
      data {
        relateBizType
        relateBizId
        relateBizDesc
        relateBizStatus
        relateBizCreateUserId
        relateBizCreateTime
      }
      total
    }
  }
`;

export function useLazyEvnetBetaRelateTickets(
  options?: LazyQueryHookOptions<EvnetBetaRelateTicketsaData, QueryEvnetBetaRelateTicketsArgs>
): LazyQueryResultTuple<EvnetBetaRelateTicketsaData, QueryEvnetBetaRelateTicketsArgs> {
  return useLazyQuery(GET_EVENTS_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
