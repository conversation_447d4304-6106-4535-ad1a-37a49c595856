import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EventSourceRecordResponse,
  QueryEventSourceRecordArgs,
} from '../generated-types/graphql';

export type { EventSourceRecordJson } from '../generated-types/graphql';

export type QueryEventSourceRecordData = {
  eventSourceRecord: EventSourceRecordResponse;
};

export const GET_EVENT_SOURCE_RECORD: DocumentNode = gql`
  query EventSourceRecord($query: EventSourceRecordQuery!) {
    eventSourceRecord(query: $query) {
      data {
        exceptionSubjectGuid
        exceptionSubjectMergeRows
        exceptionSubjectTag
        exceptionSubjectType
        operatorId
        operatorName
        subjectItemName
        subjectItemRelateTime
        taskNo
        taskNoMergeRows
        taskType
      }
      total
    }
  }
`;

export function useLazyEvnetSourceRecord(
  options?: LazyQueryHookOptions<QueryEventSourceRecordData, QueryEventSourceRecordArgs>
): LazyQueryResultTuple<QueryEventSourceRecordData, QueryEventSourceRecordArgs> {
  return useLazyQuery(GET_EVENT_SOURCE_RECORD, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
