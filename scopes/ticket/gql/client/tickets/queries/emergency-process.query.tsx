import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EmergencyProcessResponse,
  QueryEmergencyProcessArgs,
} from '../generated-types/graphql';

export type { EmergencyProcessData, EmergencyProcessQ } from '../generated-types/graphql';
export type QueryEmergencyProcessData = {
  emergencyProcess: EmergencyProcessResponse;
};

export const GET_EMERGENCY_PROCESS: DocumentNode = gql`
  query EmergencyProcess($query: EmergencyProcessQ!) {
    emergencyProcess(query: $query) {
      data {
        name
        emergencyId
        blockGuid
        categoryCode
        categoryName
        gmtCreate
        gmtModified
        createUserId
        createUserName
      }
      total
      success
      code
      message
    }
  }
`;

export function useLazyEmergencyProcess(
  options?: LazyQueryHookOptions<QueryEmergencyProcessData, QueryEmergencyProcessArgs>
): LazyQueryResultTuple<QueryEmergencyProcessData, QueryEmergencyProcessArgs> {
  return useLazyQuery(GET_EMERGENCY_PROCESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
