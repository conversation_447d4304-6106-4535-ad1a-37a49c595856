import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchChangeTemplatesResponse,
  Maybe,
  QueryChangeTemplatesArgs,
} from '../generated-types/graphql';

export type { FetchChangeTemplatesData, FetchChangeTemplatesQ } from '../generated-types/graphql';
export type QueryChangeTemplatesData = {
  changeTemplates: Maybe<FetchChangeTemplatesResponse>;
};

export const GET_CHANGE_TEMPLATES: DocumentNode = gql`
  query ChangeTemplates($query: FetchChangeTemplatesQ!) {
    changeTemplates(query: $query) {
      data {
        templateId
        templateName
        changeType
        changeTypeName
        riskLevel
        gmtModified
        templateStatus
        creatorId
        creatorName
        modifyPersonId
        modifyPersonName
        executeRoleCode
        workFlowId
        hasPermission
        reason
        availArea
        availDate
        effTime
        changeVersion
        postponeApprovalId
        sourceChangeId
        sourceChangeTitle
        gmtCreate
      }
      total
      success
    }
  }
`;

export function useLazyChangeTemplates(
  options?: LazyQueryHookOptions<QueryChangeTemplatesData, QueryChangeTemplatesArgs>
): LazyQueryResultTuple<QueryChangeTemplatesData, QueryChangeTemplatesArgs> {
  return useLazyQuery(GET_CHANGE_TEMPLATES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
