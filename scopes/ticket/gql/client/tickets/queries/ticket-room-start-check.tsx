import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, QueryHookOptions } from '@apollo/client';

import type { Query, QueryTicketRoomStartCheckArgs } from '../generated-types/graphql.js';

export const TICKET_ROOM_START_CHECK: DocumentNode = gql`
  query TicketRoomStartCheck($taskNo: String!, $roomGuid: String!) {
    ticketRoomStartCheck(taskNo: $taskNo, roomGuid: $roomGuid) {
      success
      message
    }
  }
`;

export type TicketRoomStartCheckGqlData = Pick<Query, 'ticketRoomStartCheck'>;

export function useTicketRoomStartCheck(
  variables: QueryTicketRoomStartCheckArgs,
  options?: QueryHookOptions<TicketRoomStartCheckGqlData, QueryTicketRoomStartCheckArgs>
) {
  return useQuery<TicketRoomStartCheckGqlData, QueryTicketRoomStartCheckArgs>(
    TICKET_ROOM_START_CHECK,
    {
      variables,
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}

export function useLazyTicketRoomStartCheck(
  options?: LazyQueryHookOptions<TicketRoomStartCheckGqlData, QueryTicketRoomStartCheckArgs>
) {
  return useLazyQuery<TicketRoomStartCheckGqlData, QueryTicketRoomStartCheckArgs>(
    TICKET_ROOM_START_CHECK,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
