import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  ChangeOnlineTemplateResponse,
  QueryChangeOnlineTemplateArgs,
} from '../generated-types/graphql';

export type { ChangeOnlineData } from '../generated-types/graphql';

export type ChangeOnlineTemplateData = {
  changeOnlineTemplate: ChangeOnlineTemplateResponse;
};

export const GET_CHANGE_ONLINE_TEMPLATE: DocumentNode = gql`
  query ChangeOnlineTemplate($templateId: String!) {
    changeOnlineTemplate(templateId: $templateId) {
      success
      code
      data {
        reason
        riskLevel
        changeCategory
        creatorId
        creatorName
        templateId
        templateName
        workFlowId
        changeVersion
        effTime
        gmtCreate
        modifyPersonId
        modifyPersonName
        templateStatus
        isDelete
        extJson {
          availAreaList
        }
        stepList {
          stepOrder
          id
          stepStatus
          stepName
          operator
          operatorId
          operatorName
          opType
          opObjectName
          opObjectCode
          pointCode
          pointName
          pointValueText
          expectedValue
          stepDesc
          startTime
          endTime

          extendJson {
            stepResponsibleUserList {
              userName
              userId
            }

            stepInhibitionItemInfoList {
              deviceType
              deviceTag
              deviceGuid
              deviceName
              roomGuid
              roomType
              maxInfluencesStep
              inhibitionModelId
              inhibitionModelName
              inhibitionItemIdList
            }

            stepDeviceInfoList {
              deviceGuid
              deviceName
              deviceTag
              roomTag
              checkPointValue
              deviceLabel
            }
          }
        }
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
      }
    }
  }
`;

export function useLazyChangeOnlineTemplate(
  options?: LazyQueryHookOptions<ChangeOnlineTemplateData, QueryChangeOnlineTemplateArgs>
): LazyQueryResultTuple<ChangeOnlineTemplateData, QueryChangeOnlineTemplateArgs> {
  return useLazyQuery(GET_CHANGE_ONLINE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
