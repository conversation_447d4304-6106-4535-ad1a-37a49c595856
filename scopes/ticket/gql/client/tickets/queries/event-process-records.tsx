import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  EventProcessRecordJson,
  Maybe,
  QueryEventProcessRecordsArgs,
} from '../generated-types/graphql';

export type QueryEventProcessRecordsData = {
  eventProcessRecords: Maybe<{
    data: EventProcessRecordJson[];
    total?: number;
  }>;
};

export const GET_EVENT_PROCESS_RECORDS: DocumentNode = gql`
  # Write your query or mutation here
  query GetEventProcessRecords($mode: String!, $eventId: Long!, $eventPhase: String!) {
    eventProcessRecords(mode: $mode, eventId: $eventId, eventPhase: $eventPhase) {
      data {
        recordId
        eventId
        eventPhase
        handlerDesc
        handleTime
        handleContent
        spareInfo {
          type
          targetRoom
          specific
          deviceModelList {
            topCategory
            secondCategory
            deviceType
            vendor
            productModel
            warehouseCount
          }
        }
        wareHouseNo
        fileInfoList {
          fileFormat
          fileName
          filePath
          fileSize
          fileType
          gmtCreate
          gmtModified
          id
          targetId
          targetType
          uploadBy
          uploadByName
          uploadTime
        }
      }
      code
      success
      message
      total
    }
  }
`;

export function useEventProcessRecords(
  options?: QueryHookOptions<QueryEventProcessRecordsData, QueryEventProcessRecordsArgs>
): QueryResult<QueryEventProcessRecordsData, QueryEventProcessRecordsArgs> {
  return useQuery(GET_EVENT_PROCESS_RECORDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyEventProcessRecords(
  options?: LazyQueryHookOptions<QueryEventProcessRecordsData, QueryEventProcessRecordsArgs>
): LazyQueryResultTuple<QueryEventProcessRecordsData, QueryEventProcessRecordsArgs> {
  return useLazyQuery(GET_EVENT_PROCESS_RECORDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
