import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  QueryTicketRelateApprovalsArgs,
  TicketRelateApproval,
} from '../generated-types/graphql';

export type QueryTicketRelateApprovalsData = {
  ticketRelateApprovals: Maybe<{
    data: TicketRelateApproval[];
    total: number;
  }>;
};

export const GET_TICKET_RELATE_APPROVALS: DocumentNode = gql`
  query GetTicketRelateApprovals($taskNo: String!, $taskNode: String) {
    ticketRelateApprovals(taskNo: $taskNo, taskNode: $taskNode) {
      data {
        id
        taskNo
        processId
        taskNode
        gmtCreate
        gmtModified
      }
      total
    }
  }
`;

export function useTicketRelateApprovals(
  options?: QueryHookOptions<QueryTicketRelateApprovalsData, QueryTicketRelateApprovalsArgs>
): QueryResult<QueryTicketRelateApprovalsData, QueryTicketRelateApprovalsArgs> {
  return useQuery(GET_TICKET_RELATE_APPROVALS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTicketRelateApprovals(
  options?: LazyQueryHookOptions<QueryTicketRelateApprovalsData, QueryTicketRelateApprovalsArgs>
): LazyQueryResultTuple<QueryTicketRelateApprovalsData, QueryTicketRelateApprovalsArgs> {
  return useLazyQuery(GET_TICKET_RELATE_APPROVALS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
