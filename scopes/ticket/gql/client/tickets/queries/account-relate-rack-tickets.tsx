import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  AccountRelateRackTicket,
  Maybe,
  QueryAccountRelateRackTicketsArgs,
} from '../generated-types/graphql';

export type QueryAccountRelateTicketsData = {
  accountRelateRackTickets: Maybe<{
    data: AccountRelateRackTicket[];
    total: number;
  }>;
};

export const GET_ACCOUNT_RELATE_TICKETS: DocumentNode = gql`
  query AccountRelateTicket(
    $gridGuid: String!
    $startTime: Long
    $endTime: Long
    $queryLatest: Boolean
    $pageNum: Int!
    $pageSize: Int!
  ) {
    accountRelateRackTickets(
      gridGuid: $gridGuid
      startTime: $startTime
      endTime: $endTime
      queryLatest: $queryLatest
      pageNum: $pageNum
      pageSize: $pageSize
    ) {
      data {
        id
        taskNo
        taskType {
          name
          value
        }
        gridGuid
        powerOnTime
        checkStatus {
          code
          name
        }
      }
      total
    }
  }
`;

export function useAccountRelateTickets(
  options?: QueryHookOptions<QueryAccountRelateTicketsData, QueryAccountRelateRackTicketsArgs>
): QueryResult<QueryAccountRelateTicketsData, QueryAccountRelateRackTicketsArgs> {
  return useQuery(GET_ACCOUNT_RELATE_TICKETS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyAccountRelateTickets(
  options?: LazyQueryHookOptions<QueryAccountRelateTicketsData, QueryAccountRelateRackTicketsArgs>
): LazyQueryResultTuple<QueryAccountRelateTicketsData, QueryAccountRelateRackTicketsArgs> {
  return useLazyQuery(GET_ACCOUNT_RELATE_TICKETS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
