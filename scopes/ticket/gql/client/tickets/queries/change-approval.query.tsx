import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchChangeApprovalResponse,
  Maybe,
  QueryFetchChangeApprovalArgs,
} from '../generated-types/graphql';

export type QueryFetchChangeApproval = {
  fetchChangeApproval: Maybe<FetchChangeApprovalResponse>;
};

export const GET_CHANGE_ORDER_APPROVAL: DocumentNode = gql`
  query FetchChangeApproval($query: FetchChangeApprovalQ!) {
    fetchChangeApproval(query: $query) {
      data
    }
  }
`;
export function useLazyFetchChangeApproval(
  options?: LazyQueryHookOptions<QueryFetchChangeApproval, QueryFetchChangeApprovalArgs>
): LazyQueryResultTuple<QueryFetchChangeApproval, QueryFetchChangeApprovalArgs> {
  return useLazyQuery(GET_CHANGE_ORDER_APPROVAL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
