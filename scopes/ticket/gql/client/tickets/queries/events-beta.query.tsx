import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { EventsBetaResponse, QueryEventsBetaArgs } from '../generated-types/graphql';

export type { EventBetaJson, EventBetaQuery, FaultModelInfo } from '../generated-types/graphql';

export type QueryEventsBetaData = {
  eventsBeta: EventsBetaResponse;
};

export const GET_EVENTS_BETA: DocumentNode = gql`
  query EventsBeta($query: EventBetaQuery!) {
    eventsBeta(query: $query) {
      data {
        eventId
        gmtCreate
        gmtModified
        idcTag
        blockGuid
        eventSourceCode
        eventSourceName
        falseAlarm
        occurTime
        categoryCode
        categoryName
        eventLevelCode
        eventLevelName
        eventTitle
        eventDesc
        ownerId
        ownerName
        createUserId
        createUserName
        eventStatus
        faultType
        convertToProblem
        confirmSla
        relieveSla
        finishSla
        faultModelList {
          idcTag
          blockTag
          roomTag
          deviceType
          deviceTag
          name
          guid
        }
        closeTime
        relieveHandleContent
        finishHandleContent
      }
      total
    }
  }
`;

export function useLazyEventsBeta(
  options?: LazyQueryHookOptions<QueryEventsBetaData, QueryEventsBetaArgs>
): LazyQueryResultTuple<QueryEventsBetaData, QueryEventsBetaArgs> {
  return useLazyQuery(GET_EVENTS_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
