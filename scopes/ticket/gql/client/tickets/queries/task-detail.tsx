import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Maybe, QueryTaskDetailArgs, TaskDetailJson } from '../generated-types/graphql';

export type QueryTaskDetailData = {
  taskDetail: Maybe<TaskDetailJson>;
};

export const GET_TASK_DETAIL: DocumentNode = gql`
  query GetTaskDetail($id: String!) {
    taskDetail(id: $id) {
      id
      name
      guidePeriod
      blockGuid
      planType
      mopType
      mopCount
      periodUnit
      isActivated {
        label
        value
      }
      isInEffect
      lastExecuteTime
      creator {
        id
        name
      }
      createTime
      modifyTime
      blockScope {
        blockGuid
        scopeType
        scopeFlag
      }
      lastExecuteResult
      repeatCycle {
        period
        periodUnit
        dayConfig {
          days
          isRemove
        }
        atDayConfig {
          month
          atDayType
          isLast
          sortNum
        }
      }
      allowTriggerTime
      aggBlockScope {
        blockGuidList
        scopeType
        scopeFlag
        scopeFlagInfoList {
          flagName
          scopeFlag
        }
      }
      endTime
      splitor {
        splitType
        splitParam
      }
      jobSla
      slaUnit
      jobItemList {
        id
        jobId
        scheduleId
        name
        itemType
        scheduleType
        jobTypeName
        jobTypeCode
        subJobTypeName
        subJobTypeCode
        gmtCreate
      }
      unFinishTaskNoList
      finishTaskNoList
      manageType {
        label
        value
      }
      schLevel
      taskResultNums
      executionStatus
      fileInfoList {
        uid
        name
        patialPath
        uploadUser {
          id
          name
        }
        uploadedAt
        ext
        src
        id
        size
        type
        modifiedAt
        targetId
        targetType
      }
      modifyUser {
        id
        name
      }
    }
  }
`;

export function useTaskDetail(
  options?: QueryHookOptions<QueryTaskDetailData, QueryTaskDetailArgs>
): QueryResult<QueryTaskDetailData, QueryTaskDetailArgs> {
  return useQuery(GET_TASK_DETAIL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyTaskDetail(
  options?: LazyQueryHookOptions<QueryTaskDetailData, QueryTaskDetailArgs>
): LazyQueryResultTuple<QueryTaskDetailData, QueryTaskDetailArgs> {
  return useLazyQuery(GET_TASK_DETAIL, { fetchPolicy: 'network-only', ...options });
}
