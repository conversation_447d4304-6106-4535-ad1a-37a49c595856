import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { EventReportsResponse, QueryEventReportsArgs } from '../generated-types/graphql';

export type { EventReportsJson } from '../generated-types/graphql';

export type EventReportsData = {
  eventReports: EventReportsResponse;
};

export const GET_EVENT_REPORTS: DocumentNode = gql`
  query EventReports($query: EventReportsQuery!) {
    eventReports(query: $query) {
      data {
        reportChannels
        reportToRoleCodes
        eventStatus
        reportContent
        eventId
        reportToUserList {
          id
          userName
        }
        reportTime
        createUserId
        createUserName
        reportType
        reportStatus
      }
      total
    }
  }
`;

export function useLazyEventReports(
  options?: LazyQueryHookOptions<EventReportsData, QueryEventReportsArgs>
): LazyQueryResultTuple<EventReportsData, QueryEventReportsArgs> {
  return useLazyQuery(GET_EVENT_REPORTS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
