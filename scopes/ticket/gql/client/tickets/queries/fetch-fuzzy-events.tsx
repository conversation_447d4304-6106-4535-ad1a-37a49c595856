import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchFuzzyEventsResponse,
  Maybe,
  QueryFetchFuzzyEventsArgs,
} from '../generated-types/graphql';

export type QueryFetchFuzzyEvents = {
  fetchFuzzyEvents: Maybe<FetchFuzzyEventsResponse>;
};

export const GET_FUZZY_EVENTS: DocumentNode = gql`
  query FetchFuzzyEvents($query: FetchFuzzyEventsQuery!) {
    fetchFuzzyEvents(query: $query) {
      data {
        eventTitle
        id
        eventNo
      }
      total
      code
      success
      message
    }
  }
`;

export function useLazyFetchFuzzyEvents(
  options?: LazyQueryHookOptions<QueryFetchFuzzyEvents, QueryFetchFuzzyEventsArgs>
): LazyQueryResultTuple<QueryFetchFuzzyEvents, QueryFetchFuzzyEventsArgs> {
  return useLazyQuery(GET_FUZZY_EVENTS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
