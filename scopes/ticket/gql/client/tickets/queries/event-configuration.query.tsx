import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type { EventConfigurationResponse } from '../generated-types/graphql';

export type QueryEventConfigurationData = {
  eventConfiguration: EventConfigurationResponse;
};

export const GET_EVENT_BETA_CONFIGURATION: DocumentNode = gql`
  query EventConfiguration {
    eventConfiguration {
      data {
        eventAuditSkip
      }
      success
      code
      message
    }
  }
`;

export function useLazyEventConfiguration(
  options?: LazyQueryHookOptions<QueryEventConfigurationData, {}>
): LazyQueryResultTuple<QueryEventConfigurationData, {}> {
  return useLazyQuery(GET_EVENT_BETA_CONFIGURATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
