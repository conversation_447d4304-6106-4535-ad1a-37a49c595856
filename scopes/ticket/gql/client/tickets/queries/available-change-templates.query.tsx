import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  FetchAvailableChangeTemplatesResponse,
  Maybe,
  QueryAvailableChangeTemplatesArgs,
} from '../generated-types/graphql';

export type {
  FetchAvailableChangeTemplatesResponse,
  FetchChangeTemplatesQ,
} from '../generated-types/graphql';
export type QueryAvailableChangeTemplateData = {
  availableChangeTemplates: Maybe<FetchAvailableChangeTemplatesResponse>;
};

export const GET_AVAILABLE_CHANGE_TEMPLATE: DocumentNode = gql`
  query AvailableChangeTemplates($changeVersion: Long!, $availArea: String!) {
    availableChangeTemplates(changeVersion: $changeVersion, availArea: $availArea) {
      data {
        templateId
        templateName
        changeType
        riskLevel
        fileInfoList {
          uid
          name
          patialPath
          uploadUser {
            id
            name
          }
          uploadedAt
          ext
          src
          id
          size
          type
          modifiedAt
          targetId
          targetType
        }
      }
      success
      code
      message
    }
  }
`;

export function useLazyAvailableChangeTemplate(
  options?: LazyQueryHookOptions<
    QueryAvailableChangeTemplateData,
    QueryAvailableChangeTemplatesArgs
  >
): LazyQueryResultTuple<QueryAvailableChangeTemplateData, QueryAvailableChangeTemplatesArgs> {
  return useLazyQuery(GET_AVAILABLE_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
