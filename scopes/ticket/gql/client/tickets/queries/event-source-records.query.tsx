import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryHookOptions, LazyQueryResultTuple } from '@apollo/client';

import type {
  EventSourceRecord,
  Maybe,
  QueryEventSourceRecordsArgs,
} from '../generated-types/graphql';

export type QueryEventSourceRecoedsData = {
  eventSourceRecords: Maybe<{
    data: EventSourceRecord[];
    total: number;
  }>;
};

export const GET_EVENT_SOURCE_RECORDS: DocumentNode = gql`
  query EventSourceRecords($eventId: Long!) {
    eventSourceRecords(eventId: $eventId) {
      code
      message
      success
      data {
        taskNo
        taskType
        exceptionSubjectTag
        exceptionSubjectGuid
        exceptionSubjectType
        subjectItemName
        subjectItemRelateTime
        operatorId
        operatorName
        taskNoMergeRows
        exceptionSubjectMergeRows
      }
      total
    }
  }
`;

export function useLazyEventSourceRecords(
  options?: LazyQueryHookOptions<QueryEventSourceRecoedsData, QueryEventSourceRecordsArgs>
): LazyQueryResultTuple<QueryEventSourceRecoedsData, QueryEventSourceRecordsArgs> {
  return useLazyQuery(GET_EVENT_SOURCE_RECORDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
