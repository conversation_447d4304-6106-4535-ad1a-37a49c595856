import { gql, useLazyQuery } from '@apollo/client';
import type { DocumentNode, LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { AllEventsResponse, QueryAllEventsArgs } from '../generated-types/graphql';

export type { AllEventsResponse } from '../generated-types/graphql';

export type AllEventsData = {
  allEvents: AllEventsResponse;
};

export const GET_ALL_EVENTS: DocumentNode = gql`
  query AllEvents($query: AllEventsQ!) {
    allEvents(query: $query) {
      data {
        eventId
        gmtCreate
        gmtModified
        idcTag
        blockGuid
        occurTime
        eventTitle
        eventDesc
        curHandlerId
        curHandlerName
        createUserId
        createUserName
        eventStatus
        closeTime
        handerInfoList {
          id
          userName
        }
      }
      total
    }
  }
`;
export function useLazyAllEvents(
  options?: QueryHookOptions<AllEventsData, QueryAllEventsArgs>
): LazyQueryResultTuple<AllEventsData, QueryAllEventsArgs> {
  return useLazyQuery<AllEventsData, QueryAllEventsArgs>(GET_ALL_EVENTS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
