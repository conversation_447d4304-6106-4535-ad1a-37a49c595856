import type { MockedResponse } from '@apollo/client/testing';

import { GET_TICKET_TYPES } from './../queries/ticket-types.query';
import type { QueryTicketTypesData } from './../queries/ticket-types.query';

export function mockTicketTypes(): MockedResponse<QueryTicketTypesData, {}>[] {
  return [
    {
      request: {
        query: GET_TICKET_TYPES,
        variables: {},
      },
      result: {
        data: {
          ticketTypes: [
            {
              label: '巡检',
              value: 'INSPECTION',
              parentValue: null,
              children: [
                {
                  label: '消防巡检',
                  value: 'FIRE',
                  parentValue: 'INSPECTION',
                  isDeleted: false,
                },
                {
                  label: '安防巡检',
                  value: 'SECURITY',
                  parentValue: 'INSPECTION',
                  isDeleted: false,
                },
                {
                  label: 'IT巡检',
                  value: 'IT',
                  parentValue: 'INSPECTION',
                  isDeleted: false,
                },
                {
                  label: '设施巡检',
                  value: 'ESTABLISHMENT',
                  parentValue: 'INSPECTION',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '维修',
              value: 'REPAIR',
              parentValue: null,
              children: [
                {
                  label: '设备',
                  value: 'DEVICE',
                  parentValue: 'REPAIR',
                  isDeleted: false,
                },
                {
                  label: '包间',
                  value: 'ROOM',
                  parentValue: 'REPAIR',
                  isDeleted: false,
                },
                {
                  label: '其他',
                  value: 'OTHER',
                  parentValue: 'REPAIR',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '维护',
              value: 'MAINTENANCE',
              parentValue: null,
              children: [
                {
                  label: '消防设施维护',
                  value: 'M_FIRE',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
                {
                  label: '安防维护',
                  value: 'M_SECURITY',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
                {
                  label: 'IT维护',
                  value: 'M_IT',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
                {
                  label: '设施维护',
                  value: 'M_ESTABLISHMENT',
                  parentValue: 'MAINTENANCE',
                  isDeleted: true,
                },
                {
                  label: '工器具送检',
                  value: 'M_SJ',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
                {
                  label: '强电设施维护',
                  value: 'M_STRONG_ELECTRICITY',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
                {
                  label: '弱电设施维护',
                  value: 'M_WEAK_ELECTRICITY',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
                {
                  label: '暖通设施维护',
                  value: 'M_HVAC',
                  parentValue: 'MAINTENANCE',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '盘点',
              value: 'INVENTORY',
              parentValue: null,
              children: [
                {
                  label: '人工盘点',
                  value: 'MANUAL_INVENTORY',
                  parentValue: 'INVENTORY',
                  isDeleted: false,
                },
                {
                  label: '自动盘点',
                  value: 'AUTO_INVENTORY',
                  parentValue: 'INVENTORY',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '出入库',
              value: 'WAREHOUSE',
              parentValue: null,
              children: [
                {
                  label: '出库',
                  value: 'EX_WAREHOUSE',
                  parentValue: 'WAREHOUSE',
                  isDeleted: false,
                },
                {
                  label: '入库',
                  value: 'IN_WAREHOUSE',
                  parentValue: 'WAREHOUSE',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '出入门',
              value: 'ACCESS',
              parentValue: null,
              children: [
                {
                  label: '出门',
                  value: 'GO_OUT',
                  parentValue: 'ACCESS',
                  isDeleted: false,
                },
                {
                  label: '入门',
                  value: 'GO_IN',
                  parentValue: 'ACCESS',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '上下电',
              value: 'POWER',
              parentValue: null,
              children: [
                {
                  label: '机柜下电申请',
                  value: 'POWER_OFF',
                  parentValue: 'POWER',
                  isDeleted: false,
                },
                {
                  label: '机柜测试电上电申请',
                  value: 'POWER_OFF_TO_TEST',
                  parentValue: 'POWER',
                  isDeleted: false,
                },
                {
                  label: '机柜正式电上电申请',
                  value: 'POWER_OFF_TO_ON',
                  parentValue: 'POWER',
                  isDeleted: false,
                },
                {
                  label: '机柜测试电转正式电申请',
                  value: 'POWER_TEST_TO_ON',
                  parentValue: 'POWER',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '上下线',
              value: 'ON_OFF',
              parentValue: null,
              children: [
                {
                  label: '上线',
                  value: 'ON',
                  parentValue: 'ON_OFF',
                  isDeleted: false,
                },
                {
                  label: '下线',
                  value: 'OFF',
                  parentValue: 'ON_OFF',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '人员入室申请',
              value: 'VISITOR',
              parentValue: null,
              children: [
                {
                  label: '内部人员',
                  value: 'VISITOR_IN',
                  parentValue: 'VISITOR',
                  isDeleted: false,
                },
                {
                  label: '客户人员',
                  value: 'VISITOR_CUSTOMER',
                  parentValue: 'VISITOR',
                  isDeleted: false,
                },
                {
                  label: '合作伙伴人员',
                  value: 'VISITOR_PARTNER',
                  parentValue: 'VISITOR',
                  isDeleted: false,
                },
                {
                  label: '其他人员',
                  value: 'VISITOR_OTHER',
                  parentValue: 'VISITOR',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '上下架',
              value: 'DEVICE_GENERAL',
              parentValue: null,
              children: [
                {
                  label: '服务器上架',
                  value: 'DEVICE_GENERAL_1',
                  parentValue: 'DEVICE_GENERAL',
                  isDeleted: false,
                },
                {
                  label: '服务器下架',
                  value: 'DEVICE_GENERAL_2',
                  parentValue: 'DEVICE_GENERAL',
                  isDeleted: false,
                },
                {
                  label: '网络设备上架',
                  value: 'DEVICE_GENERAL_3',
                  parentValue: 'DEVICE_GENERAL',
                  isDeleted: false,
                },
                {
                  label: '网络设备下架',
                  value: 'DEVICE_GENERAL_4',
                  parentValue: 'DEVICE_GENERAL',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '门禁授权',
              value: 'ACCESS_CARD_AUTH',
              parentValue: null,
              children: [
                {
                  label: '门禁卡申请',
                  value: 'CARD_APPLY',
                  parentValue: 'ACCESS_CARD_AUTH',
                  isDeleted: false,
                },
                {
                  label: '门禁卡变更',
                  value: 'CARD_CHANGE',
                  parentValue: 'ACCESS_CARD_AUTH',
                  isDeleted: false,
                },
                {
                  label: '门禁卡注销',
                  value: 'CARD_OFF',
                  parentValue: 'ACCESS_CARD_AUTH',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '到货验收',
              value: 'ACCEPT',
              parentValue: null,
              children: [
                {
                  label: '采购到货',
                  value: 'PURCHASE',
                  parentValue: 'ACCEPT',
                  isDeleted: false,
                },
                {
                  label: '调拨到货',
                  value: 'MOVE',
                  parentValue: 'ACCEPT',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
            {
              label: '应急演练',
              value: 'EMERGENCY_DRILL',
              parentValue: null,
              children: [
                {
                  label: '应急',
                  value: 'EMERGENCY',
                  parentValue: 'EMERGENCY_DRILL',
                  isDeleted: true,
                },
                {
                  label: '演练',
                  value: 'DRILL',
                  parentValue: 'EMERGENCY_DRILL',
                  isDeleted: true,
                },
              ],
              isDeleted: false,
            },
            {
              label: '风险',
              value: 'RISK_REGISTER',
              parentValue: null,
              children: [
                {
                  label: '电力',
                  value: 'ELECTRIC',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
                {
                  label: '暖通',
                  value: 'HVAC',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
                {
                  label: '安防',
                  value: 'SECURITY',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
                {
                  label: '消防',
                  value: 'FIRE',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
                {
                  label: '建筑物及外围',
                  value: 'BUILDING',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
                {
                  label: '综合布线',
                  value: 'CABLING_SYSTEM',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
                {
                  label: '运营能力',
                  value: 'OPERATION',
                  parentValue: 'RISK_REGISTER',
                  isDeleted: false,
                },
              ],
              isDeleted: false,
            },
          ],
        },
      },
    },
  ];
}
