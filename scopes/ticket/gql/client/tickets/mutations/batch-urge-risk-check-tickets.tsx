import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  BatchUrgeRiskCheckTicketsResponse,
  Maybe,
  MutationBatchUrgeRiskCheckTicketsArgs,
} from '../generated-types/graphql';

export type BatchUrgeRiskCheckTicketsData = {
  batchUrgeRiskCheckTickets: Maybe<BatchUrgeRiskCheckTicketsResponse>;
};

export const BATCH_URGE_RISK_CHECK_TICKETS: DocumentNode = gql`
  mutation BatchUrgeRiskCheckTickets($taskNos: [String!]!) {
    batchUrgeRiskCheckTickets(taskNos: $taskNos) {
      success
      message
      code
    }
  }
`;

export function useBatchUrgeRiskCheckTickets(
  options?: MutationHookOptions<
    BatchUrgeRiskCheckTicketsData,
    MutationBatchUrgeRiskCheckTicketsArgs
  >
): MutationTuple<BatchUrgeRiskCheckTicketsData, MutationBatchUrgeRiskCheckTicketsArgs> {
  return useMutation(BATCH_URGE_RISK_CHECK_TICKETS, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
