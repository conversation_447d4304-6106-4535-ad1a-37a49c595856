import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSubmitChangeOnlineSummeryArgs,
  SubmitChangeOnlineSummeryResponse,
} from '../generated-types/graphql';

export type SubmitChangeOnlineSummeryData = {
  submitChangeOnlineSummery: Maybe<SubmitChangeOnlineSummeryResponse>;
};

export const SUBMIT_CHANGE_ONLINE_SUMMERY: DocumentNode = gql`
  mutation SubmitChangeOnlineSummery($query: SubmitChangeOnlineSummeryQ!) {
    submitChangeOnlineSummery(query: $query) {
      code
      message
      success
    }
  }
`;

export function useSubmitChangeOnlineSummery(
  options?: MutationHookOptions<
    SubmitChangeOnlineSummeryData,
    MutationSubmitChangeOnlineSummeryArgs
  >
): MutationTuple<SubmitChangeOnlineSummeryData, MutationSubmitChangeOnlineSummeryArgs> {
  return useMutation(SUBMIT_CHANGE_ONLINE_SUMMERY, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
