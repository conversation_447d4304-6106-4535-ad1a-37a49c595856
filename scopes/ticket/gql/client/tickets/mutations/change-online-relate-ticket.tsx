import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ChangeOnlineRelateTicketResponse,
  Maybe,
  MutationChangeOnlineRelateTicketArgs,
} from '../generated-types/graphql';

export type CancelChangeOnlineData = {
  changeOnlineRelateTicket: Maybe<ChangeOnlineRelateTicketResponse>;
};

export const CHANGE_RELATE_TICKET: DocumentNode = gql`
  mutation ChangeOnlineRelateTicket($query: ChangeOnlineRelateTicketQ!) {
    changeOnlineRelateTicket(query: $query) {
      code
      message
      success
    }
  }
`;

export function useChangeOnlineRelateTicket(
  options?: MutationHookOptions<CancelChangeOnlineData, MutationChangeOnlineRelateTicketArgs>
): MutationTuple<CancelChangeOnlineData, MutationChangeOnlineRelateTicketArgs> {
  return useMutation(CHANGE_RELATE_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
