import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ChangeNotificationQuery,
  ChangeNotificationResponse,
  MutationNeedUploadChangeCustomerAgreementArgs,
  NeedUploadChangeCustomerAgreementResponse,
} from '../generated-types/graphql';

export type { NeedUploadChangeCustomerType } from '../generated-types/graphql';

export const CREATE_CHANGE_NOTIFICATION: DocumentNode = gql`
  mutation CreateChangeNotification($query: ChangeNotificationQuery!) {
    createChangeNotification(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export type ChangeNotificationData = {
  createChangeNotification: ChangeNotificationResponse;
};
export function useCreateChangeNotification(
  options?: MutationHookOptions<ChangeNotificationData, { query: ChangeNotificationQuery }>
): MutationTuple<ChangeNotificationData, { query: ChangeNotificationQuery }> {
  return useMutation(CREATE_CHANGE_NOTIFICATION, {
    ...options,
    fetchPolicy: 'network-only',
  });
}

export const NEED_UPLOAD_CHANGE_CUSTOMER_AGREEMENT: DocumentNode = gql`
  mutation NeedUploadChangeCustomerAgreement(
    $changeLevel: String!
    $type: NeedUploadChangeCustomerType!
  ) {
    needUploadChangeCustomerAgreement(changeLevel: $changeLevel, type: $type) {
      code
      message
      success
      data
    }
  }
`;

export type NeedUploadChangeCustomerAgreementData = {
  needUploadChangeCustomerAgreement: NeedUploadChangeCustomerAgreementResponse;
};

export function useNeedUploadChangeCustomerAgreement(
  options?: MutationHookOptions<
    NeedUploadChangeCustomerAgreementData,
    MutationNeedUploadChangeCustomerAgreementArgs
  >
): MutationTuple<
  NeedUploadChangeCustomerAgreementData,
  MutationNeedUploadChangeCustomerAgreementArgs
> {
  return useMutation(NEED_UPLOAD_CHANGE_CUSTOMER_AGREEMENT, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
