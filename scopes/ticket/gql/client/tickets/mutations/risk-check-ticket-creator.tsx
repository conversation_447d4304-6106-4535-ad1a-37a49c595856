import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateRiskCheckTicketResponse,
  Maybe,
  MutationCreateRiskCheckTicketArgs,
} from '../generated-types/graphql';

export type RiskCheckTicketCreatorData = {
  createRiskCheckTicket: Maybe<CreateRiskCheckTicketResponse>;
};

export const CREATE_RISK_CHECK_TICKET: DocumentNode = gql`
  mutation CreateRiskCheckTicket(
    $blockGuid: String!
    $scopeType: String!
    $scopeFlag: Int
    $title: String!
    $taskSla: Int!
    $slaUnit: String!
    $riskPointList: [Int!]!
  ) {
    createRiskCheckTicket(
      blockGuid: $blockGuid
      scopeType: $scopeType
      scopeFlag: $scopeFlag
      title: $title
      taskSla: $taskSla
      slaUnit: $slaUnit
      riskPointList: $riskPointList
    ) {
      code
      message
      success
    }
  }
`;

export function useRiskCheckTicketCreation(
  options?: MutationHookOptions<RiskCheckTicketCreatorData, MutationCreateRiskCheckTicketArgs>
): MutationTuple<RiskCheckTicketCreatorData, MutationCreateRiskCheckTicketArgs> {
  return useMutation(CREATE_RISK_CHECK_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
