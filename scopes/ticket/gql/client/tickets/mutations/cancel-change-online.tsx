import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CancelChangeOnlineResponse,
  Maybe,
  MutationCancelChangeOnlineArgs,
} from '../generated-types/graphql';

export type CancelChangeOnlineData = {
  cancelChangeOnline: Maybe<CancelChangeOnlineResponse>;
};

export const CANCEL_CHANGE: DocumentNode = gql`
  mutation CancelChangeOnline($query: CancelChangeOnlineQ!) {
    cancelChangeOnline(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCancelChangeOnline(
  options?: MutationHookOptions<CancelChangeOnlineData, MutationCancelChangeOnlineArgs>
): MutationTuple<CancelChangeOnlineData, MutationCancelChangeOnlineArgs> {
  return useMutation(CANCEL_CHANGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
