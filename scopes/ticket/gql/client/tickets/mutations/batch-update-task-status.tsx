import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  BatchUpdateTaskStatusResponse,
  Maybe,
  MutationBatchUpdateTaskStatusArgs,
} from '../generated-types/graphql';

export type BatchUpdateTaskStatusData = {
  batchUpdateTaskStatus: Maybe<BatchUpdateTaskStatusResponse>;
};

export const BATCH_UPDATE_RISK_STATUS: DocumentNode = gql`
  mutation BatchUpdateTaskStatus($status: String!, $ids: [Int!]!) {
    batchUpdateTaskStatus(ids: $ids, status: $status) {
      success
      message
      code
    }
  }
`;

export function useBatchUpdateTaskStatus(
  options?: MutationHookOptions<BatchUpdateTaskStatusData, MutationBatchUpdateTaskStatusArgs>
): MutationTuple<BatchUpdateTaskStatusData, MutationBatchUpdateTaskStatusArgs> {
  return useMutation(BATCH_UPDATE_RISK_STATUS, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
