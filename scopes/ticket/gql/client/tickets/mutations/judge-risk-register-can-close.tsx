import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  JudgeRiskRegisterCanCloseResponse,
  Maybe,
  MutationJudgeRiskRegisterCanCloseArgs,
} from '../generated-types/graphql';

export type JudgeRiskRegisterCanCloseData = {
  judgeRiskRegisterCanClose: Maybe<JudgeRiskRegisterCanCloseResponse>;
};

export const JUDGE_RISK_REGISTER_CAN_CLOSE: DocumentNode = gql`
  mutation JudgeRiskRegisterCanClose($query: JudgeRiskRegisterCanCloseQ!) {
    judgeRiskRegisterCanClose(query: $query) {
      code
      message
      success
    }
  }
`;

export function useJudgeRiskRegisterCanClose(
  options?: MutationHookOptions<
    JudgeRiskRegisterCanCloseData,
    MutationJudgeRiskRegisterCanCloseArgs
  >
): MutationTuple<JudgeRiskRegisterCanCloseData, MutationJudgeRiskRegisterCanCloseArgs> {
  return useMutation(JUDGE_RISK_REGISTER_CAN_CLOSE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
