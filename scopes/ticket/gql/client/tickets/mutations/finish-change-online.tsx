import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  FinishChangeOnlineResponse,
  MutationFinishChangeOnlineArgs,
} from '../generated-types/graphql';

export const STOP_CHANGE_ONLINE: DocumentNode = gql`
  mutation FinishChangeOnline($query: FinishChangeOnlineQ!) {
    finishChangeOnline(query: $query) {
      code
      message
      success
    }
  }
`;

export type FinishChangeOnlineData = {
  finishChangeOnline: FinishChangeOnlineResponse;
};

export function useFinishChangeOnline(
  options?: MutationHookOptions<FinishChangeOnlineData, MutationFinishChangeOnlineArgs>
): MutationTuple<FinishChangeOnlineData, MutationFinishChangeOnlineArgs> {
  return useMutation(STOP_CHANGE_ONLINE, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
