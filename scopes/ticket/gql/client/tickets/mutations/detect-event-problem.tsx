import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DetectEventProblemResponse,
  Maybe,
  MutationDetectEventProblemArgs,
} from '../generated-types/graphql';

export type DetectEventProblemData = {
  detectEventProblem: Maybe<DetectEventProblemResponse>;
};

export const DETECT_EVENT_PROBLEM: DocumentNode = gql`
  mutation DetectEventProblem($query: DetectEventProblemQ!) {
    detectEventProblem(query: $query) {
      code
      message
      success
    }
  }
`;

export function useDetectEventProblem(
  options?: MutationHookOptions<DetectEventProblemData, MutationDetectEventProblemArgs>
): MutationTuple<DetectEventProblemData, MutationDetectEventProblemArgs> {
  return useMutation(DETECT_EVENT_PROBLEM, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
