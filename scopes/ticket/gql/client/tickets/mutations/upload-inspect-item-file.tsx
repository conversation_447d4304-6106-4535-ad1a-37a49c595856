import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUploadInspectItemFileArgs,
  UploadInspectItemFileResponse,
} from '../generated-types/graphql';

export type UploadInspectItemFileData = {
  uploadInspectItemFile: Maybe<UploadInspectItemFileResponse>;
};

export const UPLOAD_INSPECT_ITEM_FILE: DocumentNode = gql`
  mutation UploadInspectItemFile($query: UploadInspectItemFileQ!) {
    uploadInspectItemFile(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUploadInspectItemFile(
  options?: MutationHookOptions<UploadInspectItemFileData, MutationUploadInspectItemFileArgs>
): MutationTuple<UploadInspectItemFileData, MutationUploadInspectItemFileArgs> {
  return useMutation(UPLOAD_INSPECT_ITEM_FILE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
