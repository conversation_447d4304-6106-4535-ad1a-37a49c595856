import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateEventByChangeResponse,
  Maybe,
  MutationCreateEventByChangeArgs,
} from '../generated-types/graphql';

export type CreateEventByChangeData = {
  createEventByChange: Maybe<CreateEventByChangeResponse>;
};

export const CREATE_EVENT_BY_CHANGE: DocumentNode = gql`
  mutation CreateEventByChange($query: CreateEventByChangeQ!) {
    createEventByChange(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCreateEventByChange(
  options?: MutationHookOptions<CreateEventByChangeData, MutationCreateEventByChangeArgs>
): MutationTuple<CreateEventByChangeData, MutationCreateEventByChangeArgs> {
  return useMutation(CREATE_EVENT_BY_CHANGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
