import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Mutation, MutationTakeOverTicketArgs } from '../generated-types/graphql';

export type TackOverMutationData = Pick<Mutation, 'takeOverTicket'>;

export const TACK_OVER_TICKET: DocumentNode = gql`
  mutation TackOverTicket($ticketNumber: String!, $taskType: String!) {
    takeOverTicket(ticketNumber: $ticketNumber, taskType: $taskType) {
      code
      message
      success
    }
  }
`;

export function useTakeOverTicketMutation(
  options?: MutationHookOptions<TackOverMutationData, MutationTakeOverTicketArgs>
): MutationTuple<TackOverMutationData, MutationTakeOverTicketArgs> {
  return useMutation(TACK_OVER_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
