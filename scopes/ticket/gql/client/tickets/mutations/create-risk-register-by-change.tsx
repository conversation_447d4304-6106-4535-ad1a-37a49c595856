import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateRiskRegisterByChangeResponse,
  Maybe,
  MutationCreateRiskRegisterByChangeArgs,
} from '../generated-types/graphql';

export type CreateRiskRegisterByChangeData = {
  createRiskRegisterByChange: Maybe<CreateRiskRegisterByChangeResponse>;
};

export const CREATE_RISK_REGISTER_BY_CHANGE: DocumentNode = gql`
  mutation CreateRiskRegisterByChange($query: CreateRiskRegisterByChangeQ!) {
    createRiskRegisterByChange(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCreateRiskRegisterByChange(
  options?: MutationHookOptions<
    CreateRiskRegisterByChangeData,
    MutationCreateRiskRegisterByChangeArgs
  >
): MutationTuple<CreateRiskRegisterByChangeData, MutationCreateRiskRegisterByChangeArgs> {
  return useMutation(CREATE_RISK_REGISTER_BY_CHANGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
