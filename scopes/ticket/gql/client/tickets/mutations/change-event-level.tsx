import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ChangeEventLevelResponse,
  Maybe,
  MutationChangeEventLevelArgs,
} from '../generated-types/graphql';

export type ChangeEventLevelData = {
  changeEventLevel: Maybe<ChangeEventLevelResponse>;
};

export const CHANGE_EVENT_LEVEL: DocumentNode = gql`
  mutation ChangeEventLevel($query: ChangeEventLevelQ!) {
    changeEventLevel(query: $query) {
      code
      message
      success
    }
  }
`;

export function useChangeEventLevel(
  options?: MutationHookOptions<ChangeEventLevelData, MutationChangeEventLevelArgs>
): MutationTuple<ChangeEventLevelData, MutationChangeEventLevelArgs> {
  return useMutation(CHANGE_EVENT_LEVEL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
