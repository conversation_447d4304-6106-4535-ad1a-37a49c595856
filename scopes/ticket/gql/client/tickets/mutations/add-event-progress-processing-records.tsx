import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  AddEventProgressProcessingRecordsResponse,
  Maybe,
  MutationAddEventProgressProcessingRecordsArgs,
} from '../generated-types/graphql';

export type AddEventProgressProcessingRecordsData = {
  addEventProgressProcessingRecords: Maybe<AddEventProgressProcessingRecordsResponse>;
};

export const ADD_EVENT_PROGRESS_PROGRESS_RECORDS: DocumentNode = gql`
  mutation AddEventProgressProcessingRecords($query: AddEventProgressProcessingRecordsQ!) {
    addEventProgressProcessingRecords(query: $query) {
      code
      message
      success
    }
  }
`;

export function useAddEventProgressProcessingRecords(
  options?: MutationHookOptions<
    AddEventProgressProcessingRecordsData,
    MutationAddEventProgressProcessingRecordsArgs
  >
): MutationTuple<
  AddEventProgressProcessingRecordsData,
  MutationAddEventProgressProcessingRecordsArgs
> {
  return useMutation(ADD_EVENT_PROGRESS_PROGRESS_RECORDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
