import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Mutation, MutationToAssignTicketArgs } from '../generated-types/graphql';

export type ToAssignMutationData = Pick<Mutation, 'toAssignTicket'>;

export const TO_ASSIGN_TICKET: DocumentNode = gql`
  mutation ToAssignTicket(
    $ticketNumber: String!
    $taskType: String!
    $userId: Long!
    $userName: String!
    $reason: String!
  ) {
    toAssignTicket(
      ticketNumber: $ticketNumber
      taskType: $taskType
      userId: $userId
      userName: $userName
      reason: $reason
    ) {
      code
      message
      success
    }
  }
`;

export function useAssignTicketMutation(
  options?: MutationHookOptions<ToAssignMutationData, MutationToAssignTicketArgs>
): MutationTuple<ToAssignMutationData, MutationToAssignTicketArgs> {
  return useMutation(TO_ASSIGN_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
