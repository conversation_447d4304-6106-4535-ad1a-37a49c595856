import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateEmergencyProcessArgs,
  UpdateEmergencyProcessResponse,
} from '../generated-types/graphql';

export type UpdateEmergencyProcessData = {
  updateEmergencyProcess: Maybe<UpdateEmergencyProcessResponse>;
};

export const UPDATE_EMEGENCY_PROCESS: DocumentNode = gql`
  mutation UpdateEmergencyProcess($query: UpdateEmergencyProcessQ!) {
    updateEmergencyProcess(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateEmergencyProcess(
  options?: MutationHookOptions<UpdateEmergencyProcessData, MutationUpdateEmergencyProcessArgs>
): MutationTuple<UpdateEmergencyProcessData, MutationUpdateEmergencyProcessArgs> {
  return useMutation(UPDATE_EMEGENCY_PROCESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
