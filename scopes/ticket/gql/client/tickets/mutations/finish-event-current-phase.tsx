import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  FinishEventCurrentPhaseResponse,
  Maybe,
  MutationFinishEventCurrentPhaseArgs,
} from '../generated-types/graphql';

export type FinishEventCurrentPhaseData = {
  finishEventCurrentPhase: Maybe<FinishEventCurrentPhaseResponse>;
};

export const FINISH_EVENT_CURRENT_PHASE: DocumentNode = gql`
  mutation FinishEventCurrentPhase(
    $handlerId: Int!
    $startTime: Long!
    $endTime: Long!
    $handleContent: String!
    $eventId: Long!
    $eventPhase: String!
  ) {
    finishEventCurrentPhase(
      handlerId: $handlerId
      startTime: $startTime
      endTime: $endTime
      handleContent: $handleContent
      eventId: $eventId
      eventPhase: $eventPhase
    ) {
      code
      message
      success
    }
  }
`;

export function useFinishEventCurrentPhase(
  options?: MutationHookOptions<FinishEventCurrentPhaseData, MutationFinishEventCurrentPhaseArgs>
): MutationTuple<FinishEventCurrentPhaseData, MutationFinishEventCurrentPhaseArgs> {
  return useMutation(FINISH_EVENT_CURRENT_PHASE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
