import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationReopenEventArgs,
  ReopenEventResponse,
} from '../generated-types/graphql';

export type ReplayEventData = {
  reopenEvent: Maybe<ReopenEventResponse>;
};

export const REOPEN_EVENT_BETA: DocumentNode = gql`
  mutation ReopenEvent($eventId: String!) {
    reopenEvent(eventId: $eventId) {
      code
      message
      success
    }
  }
`;

export function useReopenEvent(
  options?: MutationHookOptions<ReplayEventData, MutationReopenEventArgs>
): MutationTuple<ReplayEventData, MutationReopenEventArgs> {
  return useMutation(REOPEN_EVENT_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
