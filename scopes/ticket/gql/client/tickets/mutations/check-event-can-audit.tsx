import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CheckEventCanAuditResponse,
  Maybe,
  MutationCheckEventCanAuditArgs,
} from '../generated-types/graphql';

export type CheckEventCanAuditData = {
  checkEventCanAudit: Maybe<CheckEventCanAuditResponse>;
};

export const CHECK_EVENT_CAN_AUDIT: DocumentNode = gql`
  mutation CheckEventCanAudit($query: CheckEventCanAuditQ!) {
    checkEventCanAudit(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCheckEventCanAudit(
  options?: MutationHookOptions<CheckEventCanAuditData, MutationCheckEventCanAuditArgs>
): MutationTuple<CheckEventCanAuditData, MutationCheckEventCanAuditArgs> {
  return useMutation(CHECK_EVENT_CAN_AUDIT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
