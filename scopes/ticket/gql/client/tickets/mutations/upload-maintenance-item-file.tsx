import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUploadMaintenanceItemFileArgs,
  UploadMaintenanceItemFileResponse,
} from '../generated-types/graphql';

export type UploadMaintenanceItemFileData = {
  uploadMaintenanceItemFile: Maybe<UploadMaintenanceItemFileResponse>;
};

export const UPLOAD_MAINTENANCE_ITEM_FILE: DocumentNode = gql`
  mutation UploadMaintenanceItemFile($query: UploadMaintenanceItemFileQ!) {
    uploadMaintenanceItemFile(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUploadMaintenanceItemFile(
  options?: MutationHookOptions<
    UploadMaintenanceItemFileData,
    MutationUploadMaintenanceItemFileArgs
  >
): MutationTuple<UploadMaintenanceItemFileData, MutationUploadMaintenanceItemFileArgs> {
  return useMutation(UPLOAD_MAINTENANCE_ITEM_FILE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
