import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSkipEventCurrentPhaseArgs,
  SkipEventCurrentPhaseResponse,
} from '../generated-types/graphql';

export type SkipEventCurrentPhaseData = {
  skipEventCurrentPhase: Maybe<SkipEventCurrentPhaseResponse>;
};

export const SKIP_EVENT_CURRENT_PHASE: DocumentNode = gql`
  mutation SkipEventCurrentPhase($eventId: Long!, $eventPhase: String!) {
    skipEventCurrentPhase(eventId: $eventId, eventPhase: $eventPhase) {
      code
      message
      success
    }
  }
`;

export function useSkipEventCurrentPhase(
  options?: MutationHookOptions<SkipEventCurrentPhaseData, MutationSkipEventCurrentPhaseArgs>
): MutationTuple<SkipEventCurrentPhaseData, MutationSkipEventCurrentPhaseArgs> {
  return useMutation(SKIP_EVENT_CURRENT_PHASE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
