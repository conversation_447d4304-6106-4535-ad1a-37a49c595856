import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateChangeOrderDeviceAlarmStatusArgs,
  UpdateChangeOrderDeviceAlarmStatusResponse,
} from '../generated-types/graphql';

export type UpdateChangeOrderDeviceAlarmStatusData = {
  updateChangeOrderDeviceAlarmStatus: Maybe<UpdateChangeOrderDeviceAlarmStatusResponse>;
};

export const UPDATE_CHANGE_ORDER_DEVICE_ALARM_STATUS: DocumentNode = gql`
  mutation UpdateChangeOrderDeviceAlarmStatus($query: UpdateChangeOrderDeviceAlarmStatusQ!) {
    updateChangeOrderDeviceAlarmStatus(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateChangeOrderDeviceAlarmStatus(
  options?: MutationHookOptions<
    UpdateChangeOrderDeviceAlarmStatusData,
    MutationUpdateChangeOrderDeviceAlarmStatusArgs
  >
): MutationTuple<
  UpdateChangeOrderDeviceAlarmStatusData,
  MutationUpdateChangeOrderDeviceAlarmStatusArgs
> {
  return useMutation(UPDATE_CHANGE_ORDER_DEVICE_ALARM_STATUS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
