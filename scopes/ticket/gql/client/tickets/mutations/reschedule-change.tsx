import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationRescheduleChangeOnlineArgs,
  RescheduleChangeOnlineResponse,
} from '../generated-types/graphql';

export type RescheduleChangeOnlineData = {
  rescheduleChangeOnline: Maybe<RescheduleChangeOnlineResponse>;
};

export const RESCHEDULE_CHANGE: DocumentNode = gql`
  mutation RescheduleChangeOnline($query: RescheduleChangeOnlineQ!) {
    rescheduleChangeOnline(query: $query) {
      code
      message
      success
    }
  }
`;

export function useRescheduleChangeOnline(
  options?: MutationHookOptions<RescheduleChangeOnlineData, MutationRescheduleChangeOnlineArgs>
): MutationTuple<RescheduleChangeOnlineData, MutationRescheduleChangeOnlineArgs> {
  return useMutation(RESCHEDULE_CHANGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
