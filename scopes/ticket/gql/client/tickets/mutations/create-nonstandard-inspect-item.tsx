import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateNonstandardInspectItemResponse,
  Maybe,
  MutationCreateNonstandardInspectItemArgs,
} from '../generated-types/graphql';

export type CreateNonstandardInspectItemData = {
  createNonstandardInspectItem: Maybe<CreateNonstandardInspectItemResponse>;
};

export const CREATE_NONSTANDARD_INSPECT_ITEM: DocumentNode = gql`
  mutation CreateNonstandardInspectItem($query: CreateNonstandardInspectItemQ!) {
    createNonstandardInspectItem(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCreateNonstandardInspectItem(
  options?: MutationHookOptions<
    CreateNonstandardInspectItemData,
    MutationCreateNonstandardInspectItemArgs
  >
): MutationTuple<CreateNonstandardInspectItemData, MutationCreateNonstandardInspectItemArgs> {
  return useMutation(CREATE_NONSTANDARD_INSPECT_ITEM, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
