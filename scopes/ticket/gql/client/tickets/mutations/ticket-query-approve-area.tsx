import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

// 1. 定义返回类型和输入参数类型
import type {
  ApproveAreaApiArgsInput,
  Mutation,
  UpdateChangeApproveAreaResponse,
} from '../generated-types/graphql';

export type UpdateApproveAreaData = Pick<Mutation, 'updateChangeApproveArea'>;
export type UpdateApproveAreaVariables = {
  query: ApproveAreaApiArgsInput;
};

// 2. 定义 GraphQL Mutation
export const UPDATE_CHANGE_APPROVE_AREA: DocumentNode = gql`
  mutation UpdateChangeApproveArea($query: ApproveAreaApiArgsInput!) {
    updateChangeApproveArea(query: $query) {
      code
      success
      message
      data {
        resourceNo
        resourceType
        blockGuid
        roomCategory
        roomType
        roomCategoryName
        blockType
        blockConLevel
      }
    }
  }
`;

// 3. 创建使用 Mutation 的 Hook
export function useUpdateApproveAreaMutation(
  options?: MutationHookOptions<UpdateApproveAreaData, UpdateApproveAreaVariables>
): MutationTuple<UpdateApproveAreaData, UpdateApproveAreaVariables> {
  return useMutation(UPDATE_CHANGE_APPROVE_AREA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
