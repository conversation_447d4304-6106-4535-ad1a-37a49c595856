import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSubmitChangeShiftCountersignDescriptionArgs,
  SubmitChangeShiftCountersignDescriptionResponse,
} from '../generated-types/graphql';

export type SubmitChangeShiftCountersignDescriptionData = {
  submitChangeShiftCountersignDescription: Maybe<SubmitChangeShiftCountersignDescriptionResponse>;
};

export const SUBMIT_CHANGE_SHIFT_COUNTERSIGN_DESCRIPTION: DocumentNode = gql`
  mutation UpdateChangeShiftConfig($query: SubmitChangeShiftCountersignDescriptionQ!) {
    submitChangeShiftCountersignDescription(query: $query) {
      code
      message
      success
    }
  }
`;

export function useSubmitChangeShiftCountersignDescriptionData(
  options?: MutationHookOptions<
    SubmitChangeShiftCountersignDescriptionData,
    MutationSubmitChangeShiftCountersignDescriptionArgs
  >
): MutationTuple<
  SubmitChangeShiftCountersignDescriptionData,
  MutationSubmitChangeShiftCountersignDescriptionArgs
> {
  return useMutation(SUBMIT_CHANGE_SHIFT_COUNTERSIGN_DESCRIPTION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
