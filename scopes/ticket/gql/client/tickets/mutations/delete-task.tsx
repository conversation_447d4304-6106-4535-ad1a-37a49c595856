import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { DeleteTaskResponse, Maybe, MutationDeleteTaskArgs } from '../generated-types/graphql';

export type DeleteTaskData = {
  deleteTask: Maybe<DeleteTaskResponse>;
};

export const DELETE_TASK: DocumentNode = gql`
  mutation DeleteTask($id: Long!) {
    deleteTask(id: $id) {
      code
      message
      success
    }
  }
`;

export function useDeleteTask(
  options?: MutationHookOptions<DeleteTaskData, MutationDeleteTaskArgs>
): MutationTuple<DeleteTaskData, MutationDeleteTaskArgs> {
  return useMutation(DELETE_TASK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
