import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Mutation, MutationToCloseTicketArgs } from '../generated-types/graphql';

export type ToCloseMutationData = Pick<Mutation, 'toCloseTicket'>;

export const TO_CLOSE_TICKET: DocumentNode = gql`
  mutation ToCloseTicket(
    $ticketNumber: String!
    $taskType: String!
    $routeEndWay: String!
    $failedReason: String
    $delayDesc: String
    $delayReason: String
    $failedDesc: String
    $ignoreCheckEnd: Boolean
  ) {
    toCloseTicket(
      ticketNumber: $ticketNumber
      taskType: $taskType
      routeEndWay: $routeEndWay
      failedReason: $failedReason
      delayDesc: $delayDesc
      delayReason: $delayReason
      failedDesc: $failedDesc
      ignoreCheckEnd: $ignoreCheckEnd
    ) {
      code
      message
      success
      data
    }
  }
`;

export function useToCloseTicketMutation(
  options?: MutationHookOptions<ToCloseMutationData, MutationToCloseTicketArgs>
): MutationTuple<ToCloseMutationData, MutationToCloseTicketArgs> {
  return useMutation(TO_CLOSE_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
