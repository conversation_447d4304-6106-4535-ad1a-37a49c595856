import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteChangeOrderDeviceResponse,
  Maybe,
  MutationDeleteChangeTemplateArgs,
} from '../generated-types/graphql';

export type DeleteChangeTemplateData = {
  deleteChangeTemplate: Maybe<DeleteChangeOrderDeviceResponse>;
};

export const DELETE_CHANGE_TEMPLATE: DocumentNode = gql`
  mutation DeleteChangeTemplate($templateId: String!) {
    deleteChangeTemplate(templateId: $templateId) {
      code
      message
      success
    }
  }
`;

export function useDeleteChangeTemplate(
  options?: MutationHookOptions<DeleteChangeTemplateData, MutationDeleteChangeTemplateArgs>
): MutationTuple<DeleteChangeTemplateData, MutationDeleteChangeTemplateArgs> {
  return useMutation(DELETE_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
