import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  LocateEventResponse,
  Maybe,
  MutationLocateEventArgs,
} from '../generated-types/graphql';

export type LocateEventData = {
  locateEvent: Maybe<LocateEventResponse>;
};
export const LOCATE_EVENT: DocumentNode = gql`
  mutation LocateEvent(
    $detectUserId: Int!
    $eventId: Long!
    $detectReason: String!
    $detectTime: Long!
  ) {
    locateEvent(
      detectUserId: $detectUserId
      eventId: $eventId
      detectReason: $detectReason
      detectTime: $detectTime
    ) {
      code
      message
      success
    }
  }
`;

export function useLocateEvent(
  options?: MutationHookOptions<LocateEventData, MutationLocateEventArgs>
): MutationTuple<LocateEventData, MutationLocateEventArgs> {
  return useMutation(LOCATE_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
