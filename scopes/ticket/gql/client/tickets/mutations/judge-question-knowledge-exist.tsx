import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  JudgeQuestionKnowledgeExistResponse,
  Maybe,
  MutationJudgeQuestionKnowledgeExistArgs,
} from '../generated-types/graphql';

export type JudgeQuestionKnowledgeExistData = {
  judgeQuestionKnowledgeExist: Maybe<JudgeQuestionKnowledgeExistResponse>;
};

export const JUDGE_QUESTION_KNOWLEDGE_EXIST: DocumentNode = gql`
  mutation JudgeQuestionKnowledgeExist($riskId: String!) {
    judgeQuestionKnowledgeExist(riskId: $riskId) {
      code
      message
      success
      data
    }
  }
`;

export function useJudgeQuestionKnowledgeExist(
  options?: MutationHookOptions<
    JudgeQuestionKnowledgeExistData,
    MutationJudgeQuestionKnowledgeExistArgs
  >
): MutationTuple<JudgeQuestionKnowledgeExistData, MutationJudgeQuestionKnowledgeExistArgs> {
  return useMutation(JUDGE_QUESTION_KNOWLEDGE_EXIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
