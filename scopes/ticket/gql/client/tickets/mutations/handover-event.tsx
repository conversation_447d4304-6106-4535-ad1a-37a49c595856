import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  HandoverEventResponse,
  Maybe,
  MutationHandoverEventArgs,
} from '../generated-types/graphql';

export type HandoverEventData = {
  handoverEvent: Maybe<HandoverEventResponse>;
};

export const HANDOVER_EVENT: DocumentNode = gql`
  mutation HandoverEvent($query: HandoverEventQ!) {
    handoverEvent(query: $query) {
      code
      message
      success
    }
  }
`;

export function useHandoverEvent(
  options?: MutationHookOptions<HandoverEventData, MutationHandoverEventArgs>
): MutationTuple<HandoverEventData, MutationHandoverEventArgs> {
  return useMutation(HANDOVER_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
