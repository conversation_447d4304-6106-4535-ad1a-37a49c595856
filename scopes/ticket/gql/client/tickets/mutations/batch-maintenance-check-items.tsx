import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  BatchMaintenanceCheckItemsResponse,
  Maybe,
  MutationBatchMaintenanceCheckItemsArgs,
} from '../generated-types/graphql';

export type BatchMaintenanceCheckItemsData = {
  batchMaintenanceCheckItems: Maybe<BatchMaintenanceCheckItemsResponse>;
};

export const BATCH_MAINTENANCE_CHECK_ITEMS: DocumentNode = gql`
  mutation BatchMaintenanceCheckItemsResolver($taskNo: String!, $roomGuid: String) {
    batchMaintenanceCheckItems(taskNo: $taskNo, roomGuid: $roomGuid) {
      code
      message
      success
    }
  }
`;

export function useBatchMaintenanceCheckItems(
  options?: MutationHookOptions<
    BatchMaintenanceCheckItemsData,
    MutationBatchMaintenanceCheckItemsArgs
  >
): MutationTuple<BatchMaintenanceCheckItemsData, MutationBatchMaintenanceCheckItemsArgs> {
  return useMutation(BATCH_MAINTENANCE_CHECK_ITEMS, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
