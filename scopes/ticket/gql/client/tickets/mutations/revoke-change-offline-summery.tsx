import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationRevokeChangeOfflineSummeryArgs,
  RevokeChangeOfflineSummeryResponse,
} from '../generated-types/graphql';

export type RevokeChangeOfflineSummeryData = {
  revokeChangeOfflineSummery: Maybe<RevokeChangeOfflineSummeryResponse>;
};

export const REVOKE_CHANGE_OFFLINE_SUMMERY: DocumentNode = gql`
  mutation RevokeChangeOfflineSummery($changeOrderId: String!) {
    revokeChangeOfflineSummery(changeOrderId: $changeOrderId) {
      code
      message
      success
    }
  }
`;

export function useRevokeChangeOfflineSummery(
  options?: MutationHookOptions<
    RevokeChangeOfflineSummeryData,
    MutationRevokeChangeOfflineSummeryArgs
  >
): MutationTuple<RevokeChangeOfflineSummeryData, MutationRevokeChangeOfflineSummeryArgs> {
  return useMutation(REVOKE_CHANGE_OFFLINE_SUMMERY, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
