import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateRiskRegisterRocArgs,
  UpdateRiskRegisterRocResponse,
} from '../generated-types/graphql';

export type UpdateRiskRegisterRocData = {
  updateRiskRegisterRoc: Maybe<UpdateRiskRegisterRocResponse>;
};

export const UPDATE_RISK_REGISTER_ROC: DocumentNode = gql`
  mutation UpdateRiskRegisterRoc($query: UpdateRiskRegisterRocQ!) {
    updateRiskRegisterRoc(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateRiskRegisterRoc(
  options?: MutationHookOptions<UpdateRiskRegisterRocData, MutationUpdateRiskRegisterRocArgs>
): MutationTuple<UpdateRiskRegisterRocData, MutationUpdateRiskRegisterRocArgs> {
  return useMutation(UPDATE_RISK_REGISTER_ROC, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
