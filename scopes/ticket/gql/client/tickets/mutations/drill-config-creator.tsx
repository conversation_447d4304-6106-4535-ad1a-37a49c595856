import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateDrillConfigResponse,
  Maybe,
  MutationCreateDrillConfigArgs,
} from '../generated-types/graphql';

export type DrillConfigCreatorData = {
  createDrillConfig: Maybe<CreateDrillConfigResponse>;
};

export const CREATE_DRILL_CONFIG: DocumentNode = gql`
  mutation CreateDrillConfig(
    $excMajor: String!
    $excLevel: String!
    $excName: String!
    $effectType: String!
    $effectDomain: String!
    $exercisePlanStepList: [DrillPlanStep!]!
  ) {
    createDrillConfig(
      excMajor: $excMajor
      excLevel: $excLevel
      excName: $excName
      effectType: $effectType
      effectDomain: $effectDomain
      exercisePlanStepList: $exercisePlanStepList
    ) {
      code
      message
      success
      data
    }
  }
`;

export function useDrillConfigCreation(
  options?: MutationHookOptions<DrillConfigCreatorData, MutationCreateDrillConfigArgs>
): MutationTuple<DrillConfigCreatorData, MutationCreateDrillConfigArgs> {
  return useMutation(CREATE_DRILL_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
