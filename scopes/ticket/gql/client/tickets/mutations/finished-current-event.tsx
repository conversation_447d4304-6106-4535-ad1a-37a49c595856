import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  FinishedCurrentEventResponse,
  Maybe,
  MutationFinishedCurrentEventArgs,
} from '../generated-types/graphql';

export type FinishedCurrentEventData = {
  finishedCurrentEvent: Maybe<FinishedCurrentEventResponse>;
};

export const FINISHED_CURRENT_EVENT: DocumentNode = gql`
  mutation FinishedCurrentEvent($query: FinishedCurrentEventQ!) {
    finishedCurrentEvent(query: $query) {
      code
      message
      success
    }
  }
`;

export function useFinishedCurrentEvent(
  options?: MutationHookOptions<FinishedCurrentEventData, MutationFinishedCurrentEventArgs>
): MutationTuple<FinishedCurrentEventData, MutationFinishedCurrentEventArgs> {
  return useMutation(FINISHED_CURRENT_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
