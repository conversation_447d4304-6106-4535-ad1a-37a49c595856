import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateRepairByEventBetaResponse,
  Maybe,
  MutationCreateRepairByEventBetaArgs,
} from '../generated-types/graphql';

export type CreateRepairByEventBetaData = {
  createRepairByEventBeta: Maybe<CreateRepairByEventBetaResponse>;
};

export const CREATE_REPAIRR_BY_EVENT_BETA: DocumentNode = gql`
  mutation CreateRepairByEventBeta($query: CreateRepairByEventBetaQ!) {
    createRepairByEventBeta(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useCreateRepairByEventBeta(
  options?: MutationHookOptions<CreateRepairByEventBetaData, MutationCreateRepairByEventBetaArgs>
): MutationTuple<CreateRepairByEventBetaData, MutationCreateRepairByEventBetaArgs> {
  return useMutation(CREATE_REPAIRR_BY_EVENT_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
