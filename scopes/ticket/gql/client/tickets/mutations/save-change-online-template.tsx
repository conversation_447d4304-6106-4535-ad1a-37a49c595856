import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSaveChangeOnlineTemplateArgs,
  SaveChangeOnlineTemplateResponse,
} from '../generated-types/graphql';

export type {
  SaveChangeOnlineTemplateResponse,
  SaveChangeOnlineTemplateQ,
} from '../generated-types/graphql';

export type SaveChangeOnlineTemplateData = {
  saveChangeOnlineTemplate: Maybe<SaveChangeOnlineTemplateResponse>;
};

export const SAVE_CHANGE_ONLINE_TEMPLATE: DocumentNode = gql`
  mutation SaveChangeOnlineTemplate($query: SaveChangeOnlineTemplateQ!) {
    saveChangeOnlineTemplate(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useSaveChangeOnlineTemplate(
  options?: MutationHookOptions<SaveChangeOnlineTemplateData, MutationSaveChangeOnlineTemplateArgs>
): MutationTuple<SaveChangeOnlineTemplateData, MutationSaveChangeOnlineTemplateArgs> {
  return useMutation(SAVE_CHANGE_ONLINE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
