import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationRelieveEventArgs,
  RelieveEventResponse,
} from '../generated-types/graphql';

export type RelieveEventData = {
  relieveEvent: Maybe<RelieveEventResponse>;
};

export const RELIEVE_EVENT: DocumentNode = gql`
  mutation RelieveEvent($query: RelieveEventQ!) {
    relieveEvent(query: $query) {
      code
      message
      success
    }
  }
`;

export function useRelieveEvent(
  options?: MutationHookOptions<RelieveEventData, MutationRelieveEventArgs>
): MutationTuple<RelieveEventData, MutationRelieveEventArgs> {
  return useMutation(RELIEVE_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
