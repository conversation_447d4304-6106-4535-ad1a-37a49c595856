import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ConcludeEvnetResponse,
  Maybe,
  MutationConcludeEvnetArgs,
} from '../generated-types/graphql';

export type ConcludeEvnetData = {
  concludeEvnet: Maybe<ConcludeEvnetResponse>;
};

export const CONCLUDE_EVENT: DocumentNode = gql`
  mutation ConcludeEvnet($eventId: String!) {
    concludeEvnet(eventId: $eventId) {
      code
      message
      success
    }
  }
`;

export function useConcludeEvnet(
  options?: MutationHookOptions<ConcludeEvnetData, MutationConcludeEvnetArgs>
): MutationTuple<ConcludeEvnetData, MutationConcludeEvnetArgs> {
  return useMutation(CONCLUDE_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
