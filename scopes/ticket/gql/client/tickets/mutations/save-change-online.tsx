import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSaveChangeOnlineArgs,
  SaveChangeOnlineResponse,
} from '../generated-types/graphql';

export type { SaveChangeOnlineResponse, SaveChangeOnlineQ } from '../generated-types/graphql';

export type SaveChangeOnlineData = {
  saveChangeOnline: Maybe<SaveChangeOnlineResponse>;
};

export const SAVE_CHANGE_ONLINE_TEMPLATE: DocumentNode = gql`
  mutation SaveChangeOnline($query: SaveChangeOnlineQ!) {
    saveChangeOnline(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useSaveChangeOnline(
  options?: MutationHookOptions<SaveChangeOnlineData, MutationSaveChangeOnlineArgs>
): MutationTuple<SaveChangeOnlineData, MutationSaveChangeOnlineArgs> {
  return useMutation(SAVE_CHANGE_ONLINE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
