import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  InvalidateChangeTemplateResponse,
  Maybe,
  MutationInvalidateChangeTemplateArgs,
} from '../generated-types/graphql';

export type InvalidateChangeTemplateData = {
  invalidateChangeTemplate: Maybe<InvalidateChangeTemplateResponse>;
};

export const INVALIDATE_CHANGE_TEMPLATE_APPROVAL: DocumentNode = gql`
  mutation InvalidateChangeTemplate($templateId: String!) {
    invalidateChangeTemplate(templateId: $templateId) {
      code
      message
      success
    }
  }
`;

export function useInvalidateChangeTemplate(
  options?: MutationHookOptions<InvalidateChangeTemplateData, MutationInvalidateChangeTemplateArgs>
): MutationTuple<InvalidateChangeTemplateData, MutationInvalidateChangeTemplateArgs> {
  return useMutation(INVALIDATE_CHANGE_TEMPLATE_APPROVAL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
