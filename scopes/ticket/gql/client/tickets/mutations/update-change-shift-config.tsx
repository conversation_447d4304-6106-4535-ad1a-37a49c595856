import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateChangeShiftConfigArgs,
  UpdateChangeShiftConfigResponse,
} from '../generated-types/graphql';

export type UpdateChangeShiftConfigData = {
  updateChangeShiftConfig: Maybe<UpdateChangeShiftConfigResponse>;
};

export const UPDATE_UPDATE_CHANGE_SHIFT_CONFIG: DocumentNode = gql`
  mutation UpdateChangeShiftConfig($query: UpdateChangeShiftConfigQ!) {
    updateChangeShiftConfig(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateChangeShiftConfig(
  options?: MutationHookOptions<UpdateChangeShiftConfigData, MutationUpdateChangeShiftConfigArgs>
): MutationTuple<UpdateChangeShiftConfigData, MutationUpdateChangeShiftConfigArgs> {
  return useMutation(UPDATE_UPDATE_CHANGE_SHIFT_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
