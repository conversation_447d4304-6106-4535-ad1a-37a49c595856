import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationStopChangeOnlineStepArgs,
  StopChangeOnlineStepResponse,
} from '../generated-types/graphql';

export const STOP_CHANGE_ONLINE_STEP: DocumentNode = gql`
  mutation StopChangeOnlineStep($query: StopChangeOnlineStepQ!) {
    stopChangeOnlineStep(query: $query) {
      code
      message
      success
    }
  }
`;

export type StopChangeOnlineStepData = {
  stopChangeOnlineStep: StopChangeOnlineStepResponse;
};

export function useStopChangeOnlineStep(
  options?: MutationHookOptions<StopChangeOnlineStepData, MutationStopChangeOnlineStepArgs>
): MutationTuple<StopChangeOnlineStepData, MutationStopChangeOnlineStepArgs> {
  return useMutation(STOP_CHANGE_ONLINE_STEP, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
