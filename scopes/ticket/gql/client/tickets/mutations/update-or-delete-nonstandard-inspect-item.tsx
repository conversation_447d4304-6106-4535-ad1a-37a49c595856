import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateOrDeleteNonstandardInspectItemArgs,
  UpdateOrDeleteNonstandardInspectItemResponse,
} from '../generated-types/graphql';

export type UpdateOrDeleteNonstandardInspectItemData = {
  updateOrDeleteNonstandardInspectItem: Maybe<UpdateOrDeleteNonstandardInspectItemResponse>;
};

export const UPDATE_OR_DELETE_NONSTANDARD_INSPECT_ITEM: DocumentNode = gql`
  mutation UpdateOrDeleteNonstandardInspectItem($query: UpdateOrDeleteNonstandardInspectItemQ!) {
    updateOrDeleteNonstandardInspectItem(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateOrDeleteNonstandardInspectItem(
  options?: MutationHookOptions<
    UpdateOrDeleteNonstandardInspectItemData,
    MutationUpdateOrDeleteNonstandardInspectItemArgs
  >
): MutationTuple<
  UpdateOrDeleteNonstandardInspectItemData,
  MutationUpdateOrDeleteNonstandardInspectItemArgs
> {
  return useMutation(UPDATE_OR_DELETE_NONSTANDARD_INSPECT_ITEM, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
