import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationStartExecutionPowerGridArgs,
  StartExecutionPowerGridResponse,
} from '../generated-types/graphql';

export type StartExecutionPowerGridData = {
  startExecutionPowerGrid: Maybe<StartExecutionPowerGridResponse>;
};

export const START_EXECUTION_POWER_GRID: DocumentNode = gql`
  mutation StartExecutionPowerGrid($query: StartExecutionPowerGridQ!) {
    startExecutionPowerGrid(query: $query) {
      success
      message
      data
    }
  }
`;

export function useStartExecutionPowerGrid(
  options?: MutationHookOptions<StartExecutionPowerGridData, MutationStartExecutionPowerGridArgs>
): MutationTuple<StartExecutionPowerGridData, MutationStartExecutionPowerGridArgs> {
  return useMutation(START_EXECUTION_POWER_GRID, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
