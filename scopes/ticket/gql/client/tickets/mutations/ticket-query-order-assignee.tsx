import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

// 1. 定义返回类型和输入参数类型
import type {
  Mutation,
  OrderAssigneeApiArgsInput,
  UpdateChangeOrderAssigneeResponse,
} from '../generated-types/graphql';

export type UpdateOrderAssigneeData = Pick<Mutation, 'updateChangeOrderAssignee'>;
export type UpdateOrderAssigneeVariables = {
  query: OrderAssigneeApiArgsInput;
};

// 2. 定义 GraphQL Mutation
export const UPDATE_CHANGE_ORDER_ASSIGNEE: DocumentNode = gql`
  mutation UpdateChangeOrderAssignee($query: OrderAssigneeApiArgsInput!) {
    updateChangeOrderAssignee(query: $query) {
      code
      success
      message
      data {
        id
        gmtCreate
        gmtModified
        assigneeType
        blockGuid
        taskNo
        assigneeList {
          id
          userName
        }
      }
    }
  }
`;

// 3. 创建使用 Mutation 的 Hook
export function useUpdateOrderAssigneeMutation(
  options?: MutationHookOptions<UpdateOrderAssigneeData, UpdateOrderAssigneeVariables>
): MutationTuple<UpdateOrderAssigneeData, UpdateOrderAssigneeVariables> {
  return useMutation(UPDATE_CHANGE_ORDER_ASSIGNEE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
