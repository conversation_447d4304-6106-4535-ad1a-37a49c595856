import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  HitRiskPointResponse,
  Maybe,
  MutationHitRiskPointArgs,
} from '../generated-types/graphql';

export type HitRiskPointData = {
  hitRiskPoint: Maybe<HitRiskPointResponse>;
};

export const HIT_RISK_POINT: DocumentNode = gql`
  mutation HitRiskPoint(
    $pointId: Int!
    $taskNo: String!
    $assigneeId: Int!
    $idcTag: String!
    $blockGuid: String!
    $riskResourceCode: String!
    $riskCategory: String!
    $riskType: String!
    $riskLevel: String!
    $riskDesc: String!
    $riskObjectType: String
    $riskObjectName: String
    $riskOwnerIdList: [Int!]!
    $fileInfoList: [McUploadFileInput!]
    $riskIdentifier: String
    $longTermRisk: Boolean
    $planCompleteTime: Long
    $locationList: [HitRiskPointRiskLocationInfoInput!]
  ) {
    hitRiskPoint(
      pointId: $pointId
      taskNo: $taskNo
      assigneeId: $assigneeId
      idcTag: $idcTag
      blockGuid: $blockGuid
      riskResourceCode: $riskResourceCode
      riskCategory: $riskCategory
      riskType: $riskType
      riskLevel: $riskLevel
      riskDesc: $riskDesc
      riskObjectType: $riskObjectType
      riskObjectName: $riskObjectName
      riskOwnerIdList: $riskOwnerIdList
      fileInfoList: $fileInfoList
      riskIdentifier: $riskIdentifier
      longTermRisk: $longTermRisk
      planCompleteTime: $planCompleteTime
      locationList: $locationList
    ) {
      success
      code
      message
    }
  }
`;

export function useHitRiskPoint(
  options?: MutationHookOptions<HitRiskPointData, MutationHitRiskPointArgs>
): MutationTuple<HitRiskPointData, MutationHitRiskPointArgs> {
  return useMutation(HIT_RISK_POINT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
