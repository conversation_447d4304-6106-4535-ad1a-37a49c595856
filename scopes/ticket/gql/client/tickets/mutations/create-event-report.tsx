import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateEventReportResponse,
  Maybe,
  MutationCreateEventReportArgs,
} from '../generated-types/graphql';

export type { UpdateChangeTemplateQ } from '../generated-types/graphql';

export type CreateEventReportData = {
  createEventReport: Maybe<CreateEventReportResponse>;
};

export const CREATE_EVENT_REPORT: DocumentNode = gql`
  mutation CreateEventReport($query: CreateEventReportQ!) {
    createEventReport(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCreateEventReport(
  options?: MutationHookOptions<CreateEventReportData, MutationCreateEventReportArgs>
): MutationTuple<CreateEventReportData, MutationCreateEventReportArgs> {
  return useMutation(CREATE_EVENT_REPORT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
