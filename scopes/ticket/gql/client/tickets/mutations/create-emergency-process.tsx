import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateEmergencyProcessResponse,
  Maybe,
  MutationCreateEmergencyProcessArgs,
} from '../generated-types/graphql';

export type CreateEmergencyProcessData = {
  createEmergencyProcess: Maybe<CreateEmergencyProcessResponse>;
};

export const CREATE_EMEGENCY_PROCESS: DocumentNode = gql`
  mutation CreateEmergencyProcess($query: CreateEmergencyProcessQ!) {
    createEmergencyProcess(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useCreateEmergencyProcess(
  options?: MutationHookOptions<CreateEmergencyProcessData, MutationCreateEmergencyProcessArgs>
): MutationTuple<CreateEmergencyProcessData, MutationCreateEmergencyProcessArgs> {
  return useMutation(CREATE_EMEGENCY_PROCESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
