import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { CreateTaskResponse, Maybe, MutationCreateTaskArgs } from '../generated-types/graphql';

export type TaskCreatorData = {
  createTask: Maybe<CreateTaskResponse>;
};

export const CREATE_TASK: DocumentNode = gql`
  mutation CreateTask(
    $name: String!
    $guidePeriod: String
    $jobType: String!
    $subJobType: String!
    $blockScope: [PlanBlockInput!]!
    $periodUnit: String
    $jobSla: Int!
    $slaUnit: String!
    $cycles: CycleInput!
    $jobItems: [JobItemInput!]!
    $splitors: [SplitorInput!]!
    $allowTriggerTime: [String!]!
    $status: String!
    $endTime: Long
    $manageType: String
    $schLevel: String
  ) {
    createTask(
      name: $name
      guidePeriod: $guidePeriod
      jobType: $jobType
      subJobType: $subJobType
      blockScope: $blockScope
      periodUnit: $periodUnit
      jobSla: $jobSla
      slaUnit: $slaUnit
      cycles: $cycles
      jobItems: $jobItems
      splitors: $splitors
      allowTriggerTime: $allowTriggerTime
      status: $status
      endTime: $endTime
      manageType: $manageType
      schLevel: $schLevel
    ) {
      code
      message
      success
      taskId
    }
  }
`;

export function useTaskCreation(
  options?: MutationHookOptions<TaskCreatorData, MutationCreateTaskArgs>
): MutationTuple<TaskCreatorData, MutationCreateTaskArgs> {
  return useMutation(CREATE_TASK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
