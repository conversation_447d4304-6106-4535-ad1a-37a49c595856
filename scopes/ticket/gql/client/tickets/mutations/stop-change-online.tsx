import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationStopChangeOnlineArgs,
  StopChangeOnlineResponse,
} from '../generated-types/graphql';

export const STOP_CHANGE_ONLINE: DocumentNode = gql`
  mutation StopChangeOnline($query: StopChangeOnlineQ!) {
    stopChangeOnline(query: $query) {
      code
      message
      success
    }
  }
`;

export type StopChangeOnlineData = {
  stopChangeOnline: StopChangeOnlineResponse;
};

export function useStopChangeOnline(
  options?: MutationHookOptions<StopChangeOnlineData, MutationStopChangeOnlineArgs>
): MutationTuple<StopChangeOnlineData, MutationStopChangeOnlineArgs> {
  return useMutation(STOP_CHANGE_ONLINE, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
