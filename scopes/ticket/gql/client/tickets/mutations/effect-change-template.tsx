import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  EffectChangeTemplateResponse,
  Maybe,
  MutationEffectChangeTemplateArgs,
} from '../generated-types/graphql';

export type EffectChangeTemplateData = {
  effectChangeTemplate: Maybe<EffectChangeTemplateResponse>;
};

export const EFFECT_CHANGE_TEMPLATE: DocumentNode = gql`
  mutation EffectChangeTemplate($templateId: String!) {
    effectChangeTemplate(templateId: $templateId) {
      code
      message
      success
    }
  }
`;

export function useEffectChangeTemplate(
  options?: MutationHookOptions<EffectChangeTemplateData, MutationEffectChangeTemplateArgs>
): MutationTuple<EffectChangeTemplateData, MutationEffectChangeTemplateArgs> {
  return useMutation(EFFECT_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
