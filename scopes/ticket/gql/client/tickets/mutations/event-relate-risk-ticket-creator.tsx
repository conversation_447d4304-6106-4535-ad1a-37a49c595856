import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateEventRelateRiskTicketResponse,
  Maybe,
  MutationCreateEventRelateRiskTicketArgs,
} from '../generated-types/graphql';

export type EventRelateRiskTicketCreatorData = {
  createEventRelateRiskTicket: Maybe<CreateEventRelateRiskTicketResponse>;
};

export const CREATE_EVENT_RELATE_RISK_TICKET: DocumentNode = gql`
  mutation CreateEventRelateRiskTicket($query: CreateEventRelateRiskTicketQ!) {
    createEventRelateRiskTicket(query: $query) {
      code
      message
      success
      riskTicketNumber
    }
  }
`;

export function useEventRelateRiskTicketCreation(
  options?: MutationHookOptions<
    EventRelateRiskTicketCreatorData,
    MutationCreateEventRelateRiskTicketArgs
  >
): MutationTuple<EventRelateRiskTicketCreatorData, MutationCreateEventRelateRiskTicketArgs> {
  return useMutation(CREATE_EVENT_RELATE_RISK_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
