import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationTriggerExcelScheduleArgs,
  TriggerExcelScheduleResponse,
} from '../generated-types/graphql';

export type TriggerExcelScheduleData = {
  triggerExcelSchedule: Maybe<TriggerExcelScheduleResponse>;
};

export const TRIGGER_EXCEL_SCHEDULE: DocumentNode = gql`
  mutation TriggerExcelSchedule($schId: String!, $contextTime: Long) {
    triggerExcelSchedule(schId: $schId, contextTime: $contextTime) {
      code
      message
      success
      data
    }
  }
`;

export function useTriggerExcelSchedule(
  options?: MutationHookOptions<TriggerExcelScheduleData, MutationTriggerExcelScheduleArgs>
): MutationTuple<TriggerExcelScheduleData, MutationTriggerExcelScheduleArgs> {
  return useMutation(TRIGGER_EXCEL_SCHEDULE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
