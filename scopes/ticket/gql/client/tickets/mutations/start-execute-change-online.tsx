import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationStartExecuteChangeOnlineArgs,
  StartExecuteChangeOnlineResponse,
} from '../generated-types/graphql';

export const START_CHANGE_ONLINE: DocumentNode = gql`
  mutation StartExecuteChangeOnline($changeOrderId: String!) {
    startExecuteChangeOnline(changeOrderId: $changeOrderId) {
      code
      message
      success
    }
  }
`;

export type StartExecuteChangeOnlineData = {
  startExecuteChangeOnline: StartExecuteChangeOnlineResponse;
};
export function useStartExecuteChangeOnlineData(
  options?: MutationHookOptions<StartExecuteChangeOnlineData, MutationStartExecuteChangeOnlineArgs>
): MutationTuple<StartExecuteChangeOnlineData, MutationStartExecuteChangeOnlineArgs> {
  return useMutation(START_CHANGE_ONLINE, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
