import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSubmitEventConfigurationArgs,
  SubmitEventConfigurationResponse,
} from '../generated-types/graphql';

export type SubmitEventConfigurationData = {
  submitEventConfiguration: Maybe<SubmitEventConfigurationResponse>;
};

export const SUBMIT_EVENT_CONFIGURATION: DocumentNode = gql`
  mutation SubmitEventConfiguration($query: SubmitEventConfigurationQ!) {
    submitEventConfiguration(query: $query) {
      code
      message
      success
    }
  }
`;

export function useSubmitEventConfiguration(
  options?: MutationHookOptions<SubmitEventConfigurationData, MutationSubmitEventConfigurationArgs>
): MutationTuple<SubmitEventConfigurationData, MutationSubmitEventConfigurationArgs> {
  return useMutation(SUBMIT_EVENT_CONFIGURATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
