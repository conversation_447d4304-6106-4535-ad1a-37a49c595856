import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateEventBetaResponse,
  Maybe,
  MutationCreateEventBetaArgs,
} from '../generated-types/graphql';

export type { UpdateChangeTemplateQ } from '../generated-types/graphql';

export type CreateEventBetaData = {
  createEventBeta: Maybe<CreateEventBetaResponse>;
};

export const CREATE_EVENT_BETA: DocumentNode = gql`
  mutation CreateEventBeta($query: CreateEventBetaQ!) {
    createEventBeta(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useCreateEventBeta(
  options?: MutationHookOptions<CreateEventBetaData, MutationCreateEventBetaArgs>
): MutationTuple<CreateEventBetaData, MutationCreateEventBetaArgs> {
  return useMutation(CREATE_EVENT_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
