import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateChangeResponse,
  Maybe,
  MutationCreateChangeArgs,
} from '../generated-types/graphql';

export type CreateChangeData = {
  createChange: Maybe<CreateChangeResponse>;
};

export const CREATE_CHANGE: DocumentNode = gql`
  mutation CreateChange($query: CreateChangeQ!) {
    createChange(query: $query) {
      code
      message
      success
      changeOrderId
    }
  }
`;

export function useCreateChange(
  options?: MutationHookOptions<CreateChangeData, MutationCreateChangeArgs>
): MutationTuple<CreateChangeData, MutationCreateChangeArgs> {
  return useMutation(CREATE_CHANGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
