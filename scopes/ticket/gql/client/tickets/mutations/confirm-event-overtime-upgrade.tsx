import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ConfirmEventOvertimeUpgradeResponse,
  Maybe,
  MutationConfirmEventOvertimeUpgradeArgs,
} from '../generated-types/graphql';

export type ConfirmEventOvertimeUpgradeData = {
  confirmEventOvertimeUpgrade: Maybe<ConfirmEventOvertimeUpgradeResponse>;
};

export const CONFIRM_EVENT_OVERTIME_UPGRADE: DocumentNode = gql`
  mutation ConfirmEventOvertimeUpgrade($query: ConfirmEventOvertimeUpgradeQ!) {
    confirmEventOvertimeUpgrade(query: $query) {
      code
      message
      success
    }
  }
`;

export function useConfirmEventOvertimeUpgrade(
  options?: MutationHookOptions<
    ConfirmEventOvertimeUpgradeData,
    MutationConfirmEventOvertimeUpgradeArgs
  >
): MutationTuple<ConfirmEventOvertimeUpgradeData, MutationConfirmEventOvertimeUpgradeArgs> {
  return useMutation(CONFIRM_EVENT_OVERTIME_UPGRADE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
