import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateEventBetaArgs,
  UpdateEventBetaResponse,
} from '../generated-types/graphql';

export type UpdateEventBetaData = {
  updateEventBeta: Maybe<UpdateEventBetaResponse>;
};

export const DELETE_CHANGE_TEMPLATE: DocumentNode = gql`
  mutation UpdateEventBeta($query: UpdateEventBetaQ!) {
    updateEventBeta(query: $query) {
      code
      message
      success
    }
  }
`;

export function useUpdateEventBeta(
  options?: MutationHookOptions<UpdateEventBetaData, MutationUpdateEventBetaArgs>
): MutationTuple<UpdateEventBetaData, MutationUpdateEventBetaArgs> {
  return useMutation(DELETE_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
