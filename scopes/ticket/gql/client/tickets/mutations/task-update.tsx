import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Maybe, MutationUpdateTaskArgs, UpdateTaskResponse } from '../generated-types/graphql';

export type TaskUpdateData = {
  updateTask: Maybe<UpdateTaskResponse>;
};

export const UPDATE_TASK: DocumentNode = gql`
  mutation UpdateTask(
    $id: String!
    $name: String!
    $guidePeriod: String
    $jobType: String!
    $subJobType: String!
    $blockScope: [PlanBlockInput!]!
    $periodUnit: String
    $jobSla: Int!
    $slaUnit: String!
    $cycles: CycleInput!
    $jobItems: [JobItemInput!]!
    $splitors: [SplitorInput!]!
    $allowTriggerTime: [String!]!
    $status: String!
    $endTime: Long
    $manageType: String
    $schLevel: String
  ) {
    updateTask(
      id: $id
      name: $name
      guidePeriod: $guidePeriod
      jobType: $jobType
      subJobType: $subJobType
      blockScope: $blockScope
      periodUnit: $periodUnit
      jobSla: $jobSla
      slaUnit: $slaUnit
      cycles: $cycles
      jobItems: $jobItems
      splitors: $splitors
      allowTriggerTime: $allowTriggerTime
      status: $status
      endTime: $endTime
      manageType: $manageType
      schLevel: $schLevel
    ) {
      code
      message
      success
    }
  }
`;

export function useTaskUpdate(
  options?: MutationHookOptions<TaskUpdateData, MutationUpdateTaskArgs>
): MutationTuple<TaskUpdateData, MutationUpdateTaskArgs> {
  return useMutation(UPDATE_TASK, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
