import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationShareRiskQuestionToKnowledgeArgs,
  ShareRiskQuestionToKnowledgeResponse,
} from '../generated-types/graphql';

export type ShareRiskQuestionToKnowledgeData = {
  shareRiskQuestionToKnowledge: Maybe<ShareRiskQuestionToKnowledgeResponse>;
};

export const SHARE_RISK_QUESTION_TO_KONLEDGE: DocumentNode = gql`
  mutation ShareRiskQuestionToKnowledge($query: ShareRiskQuestionToKnowledgeQ!) {
    shareRiskQuestionToKnowledge(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useShareRiskQuestionToKnowledge(
  options?: MutationHookOptions<
    ShareRiskQuestionToKnowledgeData,
    MutationShareRiskQuestionToKnowledgeArgs
  >
): MutationTuple<ShareRiskQuestionToKnowledgeData, MutationShareRiskQuestionToKnowledgeArgs> {
  return useMutation(SHARE_RISK_QUESTION_TO_KONLEDGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
