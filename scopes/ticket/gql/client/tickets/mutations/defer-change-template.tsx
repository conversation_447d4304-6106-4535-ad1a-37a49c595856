import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeferChangeTemplateResponse,
  Maybe,
  MutationDeferChangeTemplateArgs,
} from '../generated-types/graphql';

export type DeferChangeTemplateData = {
  deferChangeTemplate: Maybe<DeferChangeTemplateResponse>;
};

export const DEFER_CHANGE_TEMPLATE: DocumentNode = gql`
  mutation DeferChangeTemplate($templateId: String!) {
    deferChangeTemplate(templateId: $templateId) {
      code
      message
      success
    }
  }
`;

export function useDeferChangeTemplate(
  options?: MutationHookOptions<DeferChangeTemplateData, MutationDeferChangeTemplateArgs>
): MutationTuple<DeferChangeTemplateData, MutationDeferChangeTemplateArgs> {
  return useMutation(DEFER_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
