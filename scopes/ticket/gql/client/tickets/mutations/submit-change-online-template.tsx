import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSubmitChangeOnlineTemplateArgs,
  SubmitChangeOnlineTemplateResponse,
} from '../generated-types/graphql';

export type {
  SubmitChangeOnlineTemplateResponse,
  SubmitChangeOnlineTemplateQ,
} from '../generated-types/graphql';

export type SubmitChangeOnlineTemplateData = {
  submitChangeOnlineTemplate: Maybe<SubmitChangeOnlineTemplateResponse>;
};

export const SUBMIT_CHANGE_ONLINE_TEMPLATE: DocumentNode = gql`
  mutation SubmitChangeOnlineTemplate($query: SubmitChangeOnlineTemplateQ!) {
    submitChangeOnlineTemplate(query: $query) {
      code
      message
      success
      data {
        changeTemplateId
        workFlowId
      }
    }
  }
`;

export function useSubmitChangeOnlineTemplate(
  options?: MutationHookOptions<
    SubmitChangeOnlineTemplateData,
    MutationSubmitChangeOnlineTemplateArgs
  >
): MutationTuple<SubmitChangeOnlineTemplateData, MutationSubmitChangeOnlineTemplateArgs> {
  return useMutation(SUBMIT_CHANGE_ONLINE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
