import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationSubmitChangeOnlineArgs,
  SubmitChangeOnlineResponse,
} from '../generated-types/graphql';

export type {
  SubmitChangeOnlineResponse,
  SubmitChangeOnlineQ,
  ChangeStepInfo,
} from '../generated-types/graphql';

export type SubmitChangeOnlineData = {
  submitChangeOnline: Maybe<SubmitChangeOnlineResponse>;
};

export const SUBMIT_CHANGE_ONLINE: DocumentNode = gql`
  mutation SubmitChangeOnline($query: SubmitChangeOnlineQ!) {
    submitChangeOnline(query: $query) {
      code
      message
      success
      data {
        changeOrderId
        workFlowId
      }
    }
  }
`;

export function useSubmitChangeOnline(
  options?: MutationHookOptions<SubmitChangeOnlineData, MutationSubmitChangeOnlineArgs>
): MutationTuple<SubmitChangeOnlineData, MutationSubmitChangeOnlineArgs> {
  return useMutation(SUBMIT_CHANGE_ONLINE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
