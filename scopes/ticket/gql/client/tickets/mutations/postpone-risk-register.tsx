import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationPostponeRiskRegisterArgs,
  PostponeRiskRegisterResponse,
} from '../generated-types/graphql';

export type PostponeRiskRegisterData = {
  postponeRiskRegister: Maybe<PostponeRiskRegisterResponse>;
};

export const POSTPONE_RISK_REGISTER: DocumentNode = gql`
  mutation PostponeRiskRegister($query: PostponeRiskRegisterQ!) {
    postponeRiskRegister(query: $query) {
      code
      message
      success
    }
  }
`;

export function usePostponeRiskRegister(
  options?: MutationHookOptions<PostponeRiskRegisterData, MutationPostponeRiskRegisterArgs>
): MutationTuple<PostponeRiskRegisterData, MutationPostponeRiskRegisterArgs> {
  return useMutation(POSTPONE_RISK_REGISTER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
