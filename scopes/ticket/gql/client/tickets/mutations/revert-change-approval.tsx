import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationRevertChangeApprovalArgs,
  RevokeChangeOfflineSummeryResponse,
} from '../generated-types/graphql';

export type RevertChangeApprovalData = {
  revertChangeApproval: Maybe<RevokeChangeOfflineSummeryResponse>;
};

export const REVERT_CHANGE_OFFLINE: DocumentNode = gql`
  mutation RevertChangeApproval($changeOrderId: String!) {
    revertChangeApproval(changeOrderId: $changeOrderId) {
      code
      message
      success
    }
  }
`;

export function useRevertChangeApproval(
  options?: MutationHookOptions<RevertChangeApprovalData, MutationRevertChangeApprovalArgs>
): MutationTuple<RevertChangeApprovalData, MutationRevertChangeApprovalArgs> {
  return useMutation(REVERT_CHANGE_OFFLINE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
