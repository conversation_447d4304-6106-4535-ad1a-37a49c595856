import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateTicketNotificationResponse,
  Maybe,
  MutationCreateTicketNotificationArgs,
} from '../generated-types/graphql';

export type TicketNotificationCreatorData = {
  createTicketNotification: Maybe<CreateTicketNotificationResponse>;
};

export const CREATE_TICKET_NOTIFICATION: DocumentNode = gql`
  mutation CreateTicketNotification(
    $bizId: String!
    $bizType: String!
    $bizStatus: String!
    $reportContent: String!
    $reportChannel: String!
    $idcTag: String
    $blockGuidList: [String!]
    $eventCode: String!
    $reportObjectList: [ReportObjectInput!]!
  ) {
    createTicketNotification(
      bizId: $bizId
      bizType: $bizType
      bizStatus: $bizStatus
      reportContent: $reportContent
      reportChannel: $reportChannel
      idcTag: $idcTag
      blockGuidList: $blockGuidList
      eventCode: $eventCode
      reportObjectList: $reportObjectList
    ) {
      code
      message
      success
    }
  }
`;

export function useTicketNotificationCreation(
  options?: MutationHookOptions<TicketNotificationCreatorData, MutationCreateTicketNotificationArgs>
): MutationTuple<TicketNotificationCreatorData, MutationCreateTicketNotificationArgs> {
  return useMutation(CREATE_TICKET_NOTIFICATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
