import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationRevertChangeTemplateApprovalArgs,
  RevertChangeTemplateApprovalResponse,
} from '../generated-types/graphql';

export type RevertChangeTemplateApprovalData = {
  revertChangeTemplateApproval: Maybe<RevertChangeTemplateApprovalResponse>;
};

export const RECERT_CHANGE_TEMPLATE_APPROVAL: DocumentNode = gql`
  mutation RevertChangeTemplateApproval($templateId: String!) {
    revertChangeTemplateApproval(templateId: $templateId) {
      code
      message
      success
    }
  }
`;

export function useRevertChangeTemplateApproval(
  options?: MutationHookOptions<
    RevertChangeTemplateApprovalData,
    MutationRevertChangeTemplateApprovalArgs
  >
): MutationTuple<RevertChangeTemplateApprovalData, MutationRevertChangeTemplateApprovalArgs> {
  return useMutation(RECERT_CHANGE_TEMPLATE_APPROVAL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
