import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  EventRelateRiskQuestionResponse,
  Maybe,
  MutationEventRelateRiskQuestionArgs,
} from '../generated-types/graphql';

export type EventRelateRiskQuestionData = {
  eventRelateRiskQuestion: Maybe<EventRelateRiskQuestionResponse>;
};

export const EVENT_RELATE_RISK_REGISTER: DocumentNode = gql`
  mutation EventRelateRiskQuestion($query: EventRelateRiskQuestionQ!) {
    eventRelateRiskQuestion(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useEventRelateRiskQuestion(
  options?: MutationHookOptions<EventRelateRiskQuestionData, MutationEventRelateRiskQuestionArgs>
): MutationTuple<EventRelateRiskQuestionData, MutationEventRelateRiskQuestionArgs> {
  return useMutation(EVENT_RELATE_RISK_REGISTER, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
