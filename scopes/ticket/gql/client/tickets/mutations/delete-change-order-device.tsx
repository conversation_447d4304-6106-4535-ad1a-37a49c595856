import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteChangeOrderDeviceResponse,
  Maybe,
  MutationDeleteChangeOrderDeviceArgs,
} from '../generated-types/graphql';

export type DeleteChangeOrderDeviceData = {
  deleteChangeOrderDevice: Maybe<DeleteChangeOrderDeviceResponse>;
};

export const DELETE_CHANGE_ORDER_DEVICE: DocumentNode = gql`
  mutation DeleteChangeOrderDevice($query: DeleteChangeOrderDeviceQ!) {
    deleteChangeOrderDevice(query: $query) {
      code
      message
      success
    }
  }
`;

export function useDeleteChangeOrderDevice(
  options?: MutationHookOptions<DeleteChangeOrderDeviceData, MutationDeleteChangeOrderDeviceArgs>
): MutationTuple<DeleteChangeOrderDeviceData, MutationDeleteChangeOrderDeviceArgs> {
  return useMutation(DELETE_CHANGE_ORDER_DEVICE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
