import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  EditDrillConfigResponse,
  Maybe,
  MutationEditDrillConfigArgs,
} from '../generated-types/graphql';

export type DrillConfigUpdateData = {
  editDrillConfig: Maybe<EditDrillConfigResponse>;
};

export const UPDATE_DRILL_CONFIG: DocumentNode = gql`
  mutation EditDrillConfig(
    $id: Long!
    $excMajor: String!
    $excLevel: String!
    $excName: String!
    $effectType: String!
    $effectDomain: String!
    $exercisePlanStepList: [DrillPlanStep!]!
  ) {
    editDrillConfig(
      id: $id
      excMajor: $excMajor
      excLevel: $excLevel
      excName: $excName
      effectType: $effectType
      effectDomain: $effectDomain
      exercisePlanStepList: $exercisePlanStepList
    ) {
      code
      message
      success
      data
    }
  }
`;

export function useDrillConfigUpdate(
  options?: MutationHookOptions<DrillConfigUpdateData, MutationEditDrillConfigArgs>
): MutationTuple<DrillConfigUpdateData, MutationEditDrillConfigArgs> {
  return useMutation(UPDATE_DRILL_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
