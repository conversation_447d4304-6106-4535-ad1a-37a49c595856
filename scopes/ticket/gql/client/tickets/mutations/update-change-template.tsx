import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateChangeTemplateArgs,
  UpdateChangeTemplateResponse,
} from '../generated-types/graphql';

export type { UpdateChangeTemplateQ } from '../generated-types/graphql';

export type UpdateChangeTemplateData = {
  updateChangeTemplate: Maybe<UpdateChangeTemplateResponse>;
};

export const UPDATE_UPDATE_CHANGE_TEMPLATE: DocumentNode = gql`
  mutation UpdateChangeTemplate($query: UpdateChangeTemplateQ!) {
    updateChangeTemplate(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useUpdateChangeTemplate(
  options?: MutationHookOptions<UpdateChangeTemplateData, MutationUpdateChangeTemplateArgs>
): MutationTuple<UpdateChangeTemplateData, MutationUpdateChangeTemplateArgs> {
  return useMutation(UPDATE_UPDATE_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
