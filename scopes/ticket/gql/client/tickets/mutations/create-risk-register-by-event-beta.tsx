import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateRiskRegisterByEventBetaResponse,
  Maybe,
  MutationCreateRiskRegisterByEventBetaArgs,
} from '../generated-types/graphql';

export type CreateRiskRegisterByEventBetaData = {
  createRiskRegisterByEventBeta: Maybe<CreateRiskRegisterByEventBetaResponse>;
};

export const CREATE_RISK_REGISTER_BY_EVENT_BETA: DocumentNode = gql`
  mutation CreateRiskRegisterByEventBeta($query: CreateRiskRegisterByEventBetaQ!) {
    createRiskRegisterByEventBeta(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useCreateRiskRegisterByEventBeta(
  options?: MutationHookOptions<
    CreateRiskRegisterByEventBetaData,
    MutationCreateRiskRegisterByEventBetaArgs
  >
): MutationTuple<CreateRiskRegisterByEventBetaData, MutationCreateRiskRegisterByEventBetaArgs> {
  return useMutation(CREATE_RISK_REGISTER_BY_EVENT_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
