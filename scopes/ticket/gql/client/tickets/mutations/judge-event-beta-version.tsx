import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  JudgeEventBetaVersionResponse,
  Maybe,
  MutationJudgeEventBetaVersionArgs,
} from '../generated-types/graphql';

export type JudgeEventBetaVersionData = {
  judgeEventBetaVersion: Maybe<JudgeEventBetaVersionResponse>;
};

export const JUDGE_EVENT_BETA_VERSION: DocumentNode = gql`
  mutation JudgeEventBetaVersion($guid: String!) {
    judgeEventBetaVersion(guid: $guid) {
      code
      message
      success
      data
    }
  }
`;

export function useJudgeEventBetaVersion(
  options?: MutationHookOptions<JudgeEventBetaVersionData, MutationJudgeEventBetaVersionArgs>
): MutationTuple<JudgeEventBetaVersionData, MutationJudgeEventBetaVersionArgs> {
  return useMutation(JUDGE_EVENT_BETA_VERSION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
