import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationReplayEventArgs,
  ReplayEventResponse,
} from '../generated-types/graphql';

export type { UpdateChangeTemplateQ } from '../generated-types/graphql';

export type ReplayEventData = {
  replayEvent: Maybe<ReplayEventResponse>;
};

export const REPLAY_EVENT_BETA: DocumentNode = gql`
  mutation ReplayEvent($query: ReplayEventQ!) {
    replayEvent(query: $query) {
      code
      message
      success
      data
    }
  }
`;

export function useReplayEvent(
  options?: MutationHookOptions<ReplayEventData, MutationReplayEventArgs>
): MutationTuple<ReplayEventData, MutationReplayEventArgs> {
  return useMutation(REPLAY_EVENT_BETA, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
