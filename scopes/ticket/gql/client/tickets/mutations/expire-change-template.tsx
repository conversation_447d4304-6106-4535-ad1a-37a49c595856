import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ExpireChangeTemplateResponse,
  Maybe,
  MutationExpireChangeTemplateArgs,
} from '../generated-types/graphql';

export type ExpireChangeTemplateData = {
  expireChangeTemplate: Maybe<ExpireChangeTemplateResponse>;
};

export const EXPIRE_CHANGE_TEMPLATE: DocumentNode = gql`
  mutation ExpireChangeTemplate($templateId: String!) {
    expireChangeTemplate(templateId: $templateId) {
      code
      message
      success
    }
  }
`;

export function useExpireChangeTemplate(
  options?: MutationHookOptions<ExpireChangeTemplateData, MutationExpireChangeTemplateArgs>
): MutationTuple<ExpireChangeTemplateData, MutationExpireChangeTemplateArgs> {
  return useMutation(EXPIRE_CHANGE_TEMPLATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
