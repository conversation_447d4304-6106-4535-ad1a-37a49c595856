import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  MutationStartExecuteChangeOnlineStepArgs,
  StartExecuteChangeOnlineStepResponse,
} from '../generated-types/graphql';

export const START_CHANGE_ONLINE_STEP: DocumentNode = gql`
  mutation StartExecuteChangeOnlineStep($query: StartExecuteChangeOnlineStepQ!) {
    startExecuteChangeOnlineStep(query: $query) {
      code
      message
      success
    }
  }
`;

export type StartExecuteChangeOnlineStepData = {
  startExecuteChangeOnlineStep: StartExecuteChangeOnlineStepResponse;
};
export function useStartExecuteChangeOnlineStepData(
  options?: MutationHookOptions<
    StartExecuteChangeOnlineStepData,
    MutationStartExecuteChangeOnlineStepArgs
  >
): MutationTuple<StartExecuteChangeOnlineStepData, MutationStartExecuteChangeOnlineStepArgs> {
  return useMutation(START_CHANGE_ONLINE_STEP, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
