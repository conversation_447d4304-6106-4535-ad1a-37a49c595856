import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteEmergencyProcessResponse,
  Maybe,
  MutationDeleteEmergencyProcessArgs,
} from '../generated-types/graphql';

export type DeleteEmergencyProcessData = {
  deleteEmergencyProcess: Maybe<DeleteEmergencyProcessResponse>;
};

export const DELETE_EMERGENCY_PROCESS: DocumentNode = gql`
  mutation DeleteEmergencyProcess($emergencyIdList: [String!]!) {
    deleteEmergencyProcess(emergencyIdList: $emergencyIdList) {
      code
      message
      success
    }
  }
`;

export function useDeleteEmergencyProcess(
  options?: MutationHookOptions<DeleteEmergencyProcessData, MutationDeleteEmergencyProcessArgs>
): MutationTuple<DeleteEmergencyProcessData, MutationDeleteEmergencyProcessArgs> {
  return useMutation(DELETE_EMERGENCY_PROCESS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
