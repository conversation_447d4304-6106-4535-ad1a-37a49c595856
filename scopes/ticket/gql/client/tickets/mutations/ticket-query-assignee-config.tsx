import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

// 1. 定义返回类型和输入参数类型
import type {
  AssigneeConfigApiArgsInput,
  Mutation,
  UpdateChangeAssigneeConfigResponse,
} from '../generated-types/graphql';

export type UpdateAssigneeConfigData = Pick<Mutation, 'updateChangeAssigneeConfig'>;
export type UpdateAssigneeConfigVariables = {
  query: AssigneeConfigApiArgsInput;
};

// 2. 定义 GraphQL Mutation
export const UPDATE_CHANGE_ASSIGNEE_CONFIG: DocumentNode = gql`
  mutation UpdateChangeAssigneeConfig($query: AssigneeConfigApiArgsInput!) {
    updateChangeAssigneeConfig(query: $query) {
      code
      success
      message
      data {
        id
        gmtCreate
        gmtModified
        blockGuid
        customerNo
        customerName
        assigneeType
        assigneeList {
          id
          userName
          userType
        }
      }
    }
  }
`;

// 3. 创建使用 Mutation 的 Hook
export function useUpdateAssigneeConfigMutation(
  options?: MutationHookOptions<UpdateAssigneeConfigData, UpdateAssigneeConfigVariables>
): MutationTuple<UpdateAssigneeConfigData, UpdateAssigneeConfigVariables> {
  return useMutation(UPDATE_CHANGE_ASSIGNEE_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
