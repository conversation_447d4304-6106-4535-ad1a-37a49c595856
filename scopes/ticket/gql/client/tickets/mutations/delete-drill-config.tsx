import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteDrillConfigResponse,
  Maybe,
  MutationDeleteDrillConfigArgs,
} from '../generated-types/graphql';

export type DeleteDrillConfig = {
  deleteDrillConfig: Maybe<DeleteDrillConfigResponse>;
};

export const DELETE_DRILL_CONFIG: DocumentNode = gql`
  mutation DeleteDrillConfig($id: Long!) {
    deleteDrillConfig(id: $id) {
      code
      message
      success
    }
  }
`;

export function useDeleteDrillConfig(
  options?: MutationHookOptions<DeleteDrillConfig, MutationDeleteDrillConfigArgs>
): MutationTuple<DeleteDrillConfig, MutationDeleteDrillConfigArgs> {
  return useMutation(DELETE_DRILL_CONFIG, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
