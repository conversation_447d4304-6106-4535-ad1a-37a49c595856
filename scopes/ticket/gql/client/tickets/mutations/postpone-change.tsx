import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationPostponeChangeOnlineArgs,
  PostponeChangeOnlineResponse,
} from '../generated-types/graphql';

export type PostponeChangeData = {
  postponeChangeOnline: Maybe<PostponeChangeOnlineResponse>;
};

export const POSTPONE_CHANGE: DocumentNode = gql`
  mutation PostponeChangeOnline($query: PostponeChangeOnlineQ!) {
    postponeChangeOnline(query: $query) {
      code
      message
      success
    }
  }
`;

export function usePostponeChange(
  options?: MutationHookOptions<PostponeChangeData, MutationPostponeChangeOnlineArgs>
): MutationTuple<PostponeChangeData, MutationPostponeChangeOnlineArgs> {
  return useMutation(POSTPONE_CHANGE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
