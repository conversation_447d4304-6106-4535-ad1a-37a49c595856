import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateEventProcessRecordResponse,
  Maybe,
  MutationCreateEventProcessRecordArgs,
} from '../generated-types/graphql';

export type CreateEventProcessRecordData = {
  createEventProcessRecord: Maybe<CreateEventProcessRecordResponse>;
};

export const CREATE_EVENT_PROCESS_RECORD: DocumentNode = gql`
  mutation CreateEventProcessRecord($query: CreateEventProcessRecordQ!) {
    createEventProcessRecord(query: $query) {
      code
      message
      success
    }
  }
`;

export function useCreateEventProcessRecord(
  options?: MutationHookOptions<CreateEventProcessRecordData, MutationCreateEventProcessRecordArgs>
): MutationTuple<CreateEventProcessRecordData, MutationCreateEventProcessRecordArgs> {
  return useMutation(CREATE_EVENT_PROCESS_RECORD, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
