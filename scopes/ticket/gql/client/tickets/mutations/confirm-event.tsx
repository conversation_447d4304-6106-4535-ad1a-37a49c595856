import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  ConfirmEventResponse,
  Maybe,
  MutationConfirmEventArgs,
} from '../generated-types/graphql';

export type ConfirmEventData = {
  confirmEvent: Maybe<ConfirmEventResponse>;
};

export const CONFIRM_EVENT: DocumentNode = gql`
  mutation ConfirmEvent($query: ConfirmEventQ!) {
    confirmEvent(query: $query) {
      code
      message
      success
    }
  }
`;

export function useConfirmEvent(
  options?: MutationHookOptions<ConfirmEventData, MutationConfirmEventArgs>
): MutationTuple<ConfirmEventData, MutationConfirmEventArgs> {
  return useMutation(CONFIRM_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
