import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  FinishedEventResponse,
  Maybe,
  MutationFinishedEventArgs,
} from '../generated-types/graphql';

export type FinishedEventData = {
  finishedEvent: Maybe<FinishedEventResponse>;
};

export const FINISHED_EVENT: DocumentNode = gql`
  mutation FinishedEvent($query: FinishedEventQ!) {
    finishedEvent(query: $query) {
      code
      message
      success
    }
  }
`;

export function useFinishedEvent(
  options?: MutationHookOptions<FinishedEventData, MutationFinishedEventArgs>
): MutationTuple<FinishedEventData, MutationFinishedEventArgs> {
  return useMutation(FINISHED_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
