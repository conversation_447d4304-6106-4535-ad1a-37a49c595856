import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationRedirectEventArgs,
  RedirectEventResponse,
} from '../generated-types/graphql';

export type RedirectEventData = {
  redirectEvent: Maybe<RedirectEventResponse>;
};

export const REDIRECT_EVENT: DocumentNode = gql`
  mutation RedirectEvent(
    $targetUserId: Int!
    $eventId: Long!
    $eventPhase: String!
    $reason: String
  ) {
    redirectEvent(
      targetUserId: $targetUserId
      eventId: $eventId
      eventPhase: $eventPhase
      reason: $reason
    ) {
      code
      message
      success
    }
  }
`;

export function useRedirectEvent(
  options?: MutationHookOptions<RedirectEventData, MutationRedirectEventArgs>
): MutationTuple<RedirectEventData, MutationRedirectEventArgs> {
  return useMutation(REDIRECT_EVENT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
