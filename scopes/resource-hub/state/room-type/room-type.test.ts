// import { expectSaga } from 'redux-saga-test-plan';
import { roomTypeSlice } from './room-type.slice';
import type { RoomTypeSliceState } from './room-type.slice';

// import { roomTypeSliceActions, getRoomTypeAction } from './room-type.action';
// import { getRoomTypeSaga } from './room-type.saga';

test('should return the initial state', () => {
  expect(roomTypeSlice.reducer(undefined, {} as any)).toEqual<RoomTypeSliceState>({
    entities: {},
    codes: [],
    loading: false,
  });
});

// test('should put a `setRoomType` action', () => {
//   const initialState = roomTypeSlice.reducer(undefined, {} as any);

//   return expectSaga(getRoomTypeSaga, {
//     type: getRoomTypeAction.type,
//   })
//     .withState({ [roomTypeSlice.name]: initialState })
//     .put({
//       type: roomTypeSliceActions.fetchRoomTypeStart.type,
//     })
//     .put({
//       type: roomTypeSliceActions.setRoomType.type,
//       payload: {
//         data: [],
//         total: 0,
//       },
//     })
//     .run();
// });
