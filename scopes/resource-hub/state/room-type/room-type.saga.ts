import { call, fork, put, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { fetchRoomTypes } from '@manyun/resource-hub.service.fetch-room-types';

import { getRoomTypeAction, roomTypeSliceActions } from './room-type.action';

/** Workers */

export function* getRoomTypeSaga({ payload }: ReturnType<typeof getRoomTypeAction>) {
  yield put(roomTypeSliceActions.fetchRoomTypeStart());

  const { error, data }: SagaReturnType<typeof fetchRoomTypes> = yield call(fetchRoomTypes);

  if (error) {
    yield put(roomTypeSliceActions.fetchRoomTypeError());
    return;
  }

  yield put(roomTypeSliceActions.setRoomType(data));
}

/** Watchers */

function* watchGetRoomType() {
  yield takeLatest(getRoomTypeAction.type, getRoomTypeSaga);
}

export default [fork(watchGetRoomType)];
