import { createAction } from '@reduxjs/toolkit';

import { roomTypeSlice } from './room-type.slice';

const prefix = `${roomTypeSlice.name}/` as const;
const postfix = '--async' as const;

export const roomTypeSliceActions = roomTypeSlice.actions;

const getRoomTypesActionType = `${prefix}get-room-types${postfix}` as const;
export const getRoomTypeAction = createAction<void, typeof getRoomTypesActionType>(
  getRoomTypesActionType
);
