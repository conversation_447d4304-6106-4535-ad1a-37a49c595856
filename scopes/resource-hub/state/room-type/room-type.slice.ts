import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

type RoomTypeCode = string;

export type RoomType = {
  code: RoomTypeCode;
  text: string;
};

export type RoomTypeSliceState = {
  /**
   * a `code: entity` object.
   */
  entities: Record<RoomTypeCode, RoomType>;

  /**
   * a `code` array that exist in `entities`.
   */
  codes: RoomTypeCode[];

  /**
   * an indicator can be used to indicate whether is fetching data from a remote API.
   */
  loading: boolean;
};

type RoomTypeSliceCaseReducers = {
  fetchRoomTypeStart: CaseReducer<RoomTypeSliceState, PayloadAction<void>>;
  setRoomType: CaseReducer<RoomTypeSliceState, PayloadAction<RoomType[]>>;
  fetchRoomTypeError: CaseReducer<RoomTypeSliceState, PayloadAction<void>>;
};

export const roomTypeSlice = createSlice<
  RoomTypeSliceState,
  RoomTypeSliceCaseReducers,
  'resource.room-type'
>({
  name: 'resource.room-type',
  initialState: {
    entities: {},
    codes: [],
    loading: false,
  },
  reducers: {
    fetchRoomTypeStart(sliceState) {
      sliceState.loading = true;
    },
    setRoomType(sliceState, { payload: roomTypes }) {
      const codes: string[] = [];
      roomTypes.forEach(roomType => {
        codes.push(roomType.code);

        // cache update performance optimization
        const existingRoomType = sliceState.entities[roomType.code];
        if (existingRoomType && existingRoomType.text === roomType.text) {
          return;
        }

        sliceState.entities[roomType.code] = roomType;
      });
      sliceState.codes = codes;
      sliceState.loading = false;
    },
    fetchRoomTypeError(sliceState) {
      sliceState.loading = false;
    },
  },
});
