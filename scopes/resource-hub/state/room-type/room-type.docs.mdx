---
description: 'RoomType redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import roomTypeSliceReducer from '@manyun/[scope].state.room-type';

const rootReducer = {
  ...roomTypeSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { roomTypeWatchers } from '@manyun/[scope].state.room-type';

const function* rootSaga() {
  yield all(
    ...roomTypeWatchers,
    // other sagas...
  );
};
```
