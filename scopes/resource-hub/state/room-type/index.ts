import roomTypeWatchers from './room-type.saga';
import { roomTypeSlice } from './room-type.slice';

export type { RoomType, RoomTypeSliceState } from './room-type.slice';
export * from './room-type.action';
export * from './room-type.selector';
export { roomTypeWatchers };
export default {
  [roomTypeSlice.name]: roomTypeSlice.reducer,
};

// dev use only
// ------
export { createCompositionWrapper } from './room-type.composition-wrapper.creator';
