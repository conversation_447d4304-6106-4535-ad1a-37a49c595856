import { Point } from '@manyun/monitoring.model.point';

import { pointsSlice } from './points.slice';
import type { PointKey, PointsSliceState } from './points.slice';

function getPointsEntities(
  entities: Record<string, Point>,
  keys?: PointKey[],
  isOnlyCore?: boolean
) {
  let pointKeys = keys === undefined ? Object.keys(entities) : keys;
  const filters = pointKeys.map(key => entities[key]).filter(entity => entity !== undefined);

  return isOnlyCore ? filters.filter(entity => entity.priority !== null) : filters;
}

export const selectPoints = () => (storeState: { [pointsSlice.name]: PointsSliceState }) => {
  return storeState[pointsSlice.name];
};

export const selectPointsEntities =
  (keys: PointKey[], isOnlyCore?: boolean) =>
  (storeState: { [pointsSlice.name]: PointsSliceState }) => {
    const { entities } = storeState[pointsSlice.name];

    return getPointsEntities(entities, keys, isOnlyCore);
  };
