import { createAction } from '@reduxjs/toolkit';

import type { SvcQuery } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { pointsSlice } from './points.slice';

const prefix = pointsSlice.name;

export const pointsSliceActions = pointsSlice.actions;

export const getPointsAction = createAction<{
  fields: SvcQuery;
  callback?: (pointKeys: string[]) => void;
}>(prefix + '/GET_POINTS');
