import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shallowequal from 'shallowequal';

import { Point } from '@manyun/monitoring.model.point';

/**
 * @example
 *
 * ```
 * const pointKey = `${deviceType}.${pointCode}`; // '10901.1001000'
 * ```
 */
export type PointKey = string;

// type DeviceType = string;
// type SpaceGuid = string;
// type HostKey = DeviceType | SpaceGuid;
// type PointCode = string;
export type PointsSliceState = {
  entities: Record<PointKey, Point>;
  // TODO: @Clf 630 迭代改造
  // entities: Record<HostKey, Record<PointCode, PointJSON>>;
};

type PointsSliceCaseReducers = {
  setPoints: CaseReducer<
    PointsSliceState,
    PayloadAction<{
      data: Point[];
    }>
  >;
};

export const pointsSlice = createSlice<
  PointsSliceState,
  PointsSliceCaseReducers,
  'resource-hub.points'
>({
  name: 'resource-hub.points',
  initialState: {
    entities: {},
  },
  reducers: {
    setPoints(sliceState, { payload: { data } }) {
      const { entities } = sliceState;

      data.forEach(point => {
        const { deviceType, code: pointCode } = point;
        const key = `${deviceType}.${pointCode}`;
        const existing: Point | undefined = entities[key];
        if (existing === undefined || !shallowequal(existing, point)) {
          entities[key] = point;
        }
      });
    },
  },
});
