import { normalize, schema } from 'normalizr';
import { call, fork, put, takeEvery } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { configSliceActions as configActions } from '@manyun/dc-brain.state.config';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

import { getPointsAction, pointsSliceActions } from './points.action';

/** Workers */

export function* getPointsSaga({
  payload: { fields, callback },
}: ReturnType<typeof getPointsAction>) {
  const { error, data }: SagaReturnType<typeof fetchPointsByCondition> = yield call(
    fetchPointsByCondition,
    fields
  );
  if (error) {
    message.error(error.message);
    callback && callback([]);
    return;
  }
  yield put(pointsSliceActions.setPoints({ data: data.data }));
  if (data.data.length) {
    const partialPointsDefinitionMap = normalize(data.data, [
      new schema.Entity(
        'defs',
        {},
        {
          idAttribute: 'deviceType',
          processStrategy({ name, code, dataType, unit, validLimits, parentCode }) {
            return {
              [code]: {
                name,
                dataType: dataType.code,
                unit,
                validLimits,
                parentCode,
              },
            };
          },
          mergeStrategy(entityA, entityB) {
            return { ...entityA, ...entityB };
          },
        }
      ),
    ]).entities.defs;
    yield put(configActions.updatePointsDefinitionMap(partialPointsDefinitionMap!));
  }
  callback && callback(data.data.map(({ deviceType, code }) => `${deviceType}.${code}`));
}

/** Watchers */

function* watchGetPoints() {
  yield takeEvery(getPointsAction.type, getPointsSaga);
}

export default [fork(watchGetPoints)];
