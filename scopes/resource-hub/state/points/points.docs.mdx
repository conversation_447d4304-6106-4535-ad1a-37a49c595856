---
description: 'Points redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'points']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import pointsSliceReducer from '@manyun/resource-hub.state.points';

const rootReducer = {
  ...pointsSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { pointsWatchers } from '@manyun/resource-hub.state.points';

const function* rootSaga() {
  yield all(
    ...pointsWatchers,
    // other sagas...
  );
};
```