import memoizeOne from 'memoize-one';

import { spaceSlice } from './space.slice';
import type { SpaceSliceState } from './space.slice';

type StoreState = {
  [spaceSlice.name]: SpaceSliceState;
};

export const selectSpaces = (storeState: StoreState) => {
  return storeState[spaceSlice.name];
};

const selectSpaceEntitiesBeforeMemoize = (
  externalCodes: string[] | null | undefined,
  codes: SpaceSliceState['codes'],
  entities: SpaceSliceState['entities']
) => {
  if (externalCodes === undefined || externalCodes === null) {
    if (codes && entities) {
      return codes.map(code => entities[code]);
    }
    return Object.keys(entities).map(code => entities[code]);
  }

  return externalCodes.map(code => entities[code]);
};
const memoizeSelectSpaceEntities = memoizeOne(selectSpaceEntitiesBeforeMemoize);
export const selectSpaceEntities =
  (externalCodes?: string[] | null) => (storeState: StoreState) => {
    const { entities, codes } = storeState[spaceSlice.name];

    return memoizeSelectSpaceEntities(externalCodes, codes, entities);
  };

export const selectSpaceCodes = (storeState: StoreState) => {
  const { codes } = storeState[spaceSlice.name];
  return codes;
};

export const selectBlocksByIdc =
  (options: {
    idc: string;
    /**
     * @defaultValue false
     */
    includesVirtualBlocks?: boolean;
  }) =>
  (storeState: { [spaceSlice.name]: SpaceSliceState }) => {
    const { idc, includesVirtualBlocks } = options;
    const { spaceMapper } = storeState[spaceSlice.name];
    const selectBlockGuids: string[] = [];
    if (idc && Array.isArray(spaceMapper[idc])) {
      spaceMapper[idc].forEach(space => {
        if (!includesVirtualBlocks && space.isVirtual) {
          return;
        }
        selectBlockGuids.push(space.guid);
      });
    }
    return selectBlockGuids;
  };
