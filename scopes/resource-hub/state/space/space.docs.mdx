---
description: 'Space redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import spaceSliceReducer from '@manyun/[scope].state.space';

const rootReducer = {
  ...spaceSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { spaceWatchers } from '@manyun/[scope].state.space';

const function* rootSaga() {
  yield all(
    ...spaceWatchers,
    // other sagas...
  );
};
```