import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shallowequal from 'shallowequal';

import type { Space } from '@manyun/resource-hub.service.fetch-spaces';

type SpaceGuid = string;
type SpaceMapperItem = {
  guid: SpaceGuid;
  isVirtual?: boolean;
  roomType?: string;
  roomTypeName?: string;
};
/**
 * 机房下楼栋 ｜ 楼栋下包间 映射数据
 * {
 *  'EC06': [{
 *   guid: 'EC06.A',
 *   isVirtual: false
 * },{
 *   guid: 'EC06.EC06',
 *   isVirtual: true
 * }],
 *  'EC06.A': [{
 *   guid: 'EC06.A.A-1'
 * },{
 *    guid: 'EC06.A.A-2
 * },{
 *   guid: 'EC06.B.B-1'}
 * ]
 * }
 */
type SpaceMapper = {
  [spaceGuid: string]: SpaceMapperItem[];
};

export type SpaceSliceState = {
  /**
   * an `code: entity` object.
   */
  entities: Record<string, Space>;
  /**
   * all resource codes
   */
  codes: string[] | null;
  spaceMapper: SpaceMapper;
};

type SpaceSliceCaseReducers = {
  setSpaces: CaseReducer<SpaceSliceState, PayloadAction<{ data: Space[] }>>;
};

export const spaceSlice = createSlice<SpaceSliceState, SpaceSliceCaseReducers, 'resource.spaces'>({
  name: 'resource.spaces',
  initialState: getInitialSpaces(),
  reducers: {
    setSpaces(sliceState, { payload: { data } }) {
      const { entities, codes } = sliceState;
      const _codes: string[] = codes ?? [];
      data.forEach(space => {
        const existing: Space | undefined = entities[space.code];
        if (existing !== undefined && shallowequal(existing, space)) {
          return;
        }
        sliceState.entities[space.code] = space;
        if (!_codes?.find(code => code === space.code)) {
          _codes?.push(space.code);
        }
        sliceState.codes = _codes;
      });
      const spaceMapper = data.reduce<SpaceMapper>((pre, cur) => {
        if (cur.type === 'BLOCK' || cur.type === 'ROOM') {
          const mapper: SpaceMapperItem = {
            guid: cur.code,
          };
          if (cur.type === 'BLOCK') {
            mapper.isVirtual = cur.isVirtual;
          }
          if (cur.type === 'ROOM') {
            mapper.roomType = cur.roomType;
            mapper.roomTypeName = cur.roomTypeName;
          }
          if (!Array.isArray(pre[cur.parentCode])) {
            pre[cur.parentCode] = [mapper];
          } else if (pre[cur.parentCode]?.findIndex(space => space.guid === cur.code) === -1) {
            pre[cur.parentCode].push(mapper);
          }
        }
        return pre;
      }, {});
      sliceState.spaceMapper = spaceMapper;
    },
  },
});

export function getInitialSpaces(): SpaceSliceState {
  return {
    entities: {},
    codes: null,
    spaceMapper: {},
  };
}
