// import { expectSaga } from 'redux-saga-test-plan';
import { spaceSlice } from './space.slice';
import type { SpaceSliceState } from './space.slice';

// import { spaceSliceActions } from './space.action';

test('should return the initial state', () => {
  expect(spaceSlice.reducer(undefined, { type: 'some-fake-action-type' })).toEqual<SpaceSliceState>(
    {
      entities: {},
      codes: null,
      spaceMapper: {},
    }
  );
});
