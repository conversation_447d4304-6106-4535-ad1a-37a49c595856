import { call, fork, put, takeEvery } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import type { DeviceNode } from '@manyun/resource-hub.service.fetch-device-type-tree';
import { fetchDeviceTypeTree } from '@manyun/resource-hub.service.fetch-device-type-tree';

import { deviceTypesSliceActions, getDeviceTypesAction } from './device-types.action';
import { treeToArray } from './device-types.slice';

/** Workers */

function* getDeviceTypesSaga({ payload }: ReturnType<typeof getDeviceTypesAction>) {
  const { error, data }: SagaReturnType<typeof fetchDeviceTypeTree> = yield call(
    fetchDeviceTypeTree,
    {
      numbered: payload?.numbered ?? null,
      idcTag: payload?.idcTag,
      blockTag: payload?.blockTag,
      roomGuidList: payload?.roomGuidList,
    }
  );

  if (typeof payload?.callback == 'function') {
    const flatDeviceTypes = treeToArray(data.data);
    const entities: Record<string, Omit<DeviceNode, 'children'>> = {};
    const _codes = flatDeviceTypes.map(deviceType => {
      entities[deviceType.metaCode] = deviceType;
      return deviceType.metaCode;
    });
    payload.callback(error ?? null, {
      codes: _codes,
      entities,
    });
  }

  if (error) {
    return;
  }

  yield put(deviceTypesSliceActions.setDeviceTypes(data));
}

/** Watchers */

function* watchGetDeviceTypes() {
  yield takeEvery(getDeviceTypesAction.type, getDeviceTypesSaga);
}

export default [fork(watchGetDeviceTypes)];
