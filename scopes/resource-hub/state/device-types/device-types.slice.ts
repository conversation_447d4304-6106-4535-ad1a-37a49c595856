import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shallowequal from 'shallowequal';

import type { DeviceNode } from '@manyun/resource-hub.service.fetch-device-type-tree';

export type DeviceTypesSliceState = {
  /**
   * an `id: entity` object.
   */
  entities: Record<string, Omit<DeviceNode, 'children'>>;

  /**
   * an `id` array that exist in `entities`.
   */
  codes: string[];
};

type DeviceTypesSliceCaseReducers = {
  setDeviceTypes: CaseReducer<
    DeviceTypesSliceState,
    PayloadAction<{
      data: DeviceNode[];
      total: number;
    }>
  >;
};
export const treeToArray = (tree: DeviceNode[]) => {
  let res: Omit<DeviceNode, 'children'>[] = [];
  for (const item of tree) {
    const { children, ...i } = item;
    if (children && children.length) {
      res = res.concat(treeToArray(children));
    }
    res.push(i);
  }
  return res;
};
export const deviceTypesSlice = createSlice<
  DeviceTypesSliceState,
  DeviceTypesSliceCaseReducers,
  'resource.device-types'
>({
  name: 'resource.device-types',
  initialState: {
    entities: {},
    codes: [],
  },
  reducers: {
    setDeviceTypes(sliceState, { payload: { data } }) {
      const { entities, codes } = sliceState;
      const changeData = treeToArray(data);
      const _codes: string[] = codes ?? [];
      changeData.forEach(deviceTypes => {
        // cache update performance optimization
        const existing: DeviceNode | undefined = entities[deviceTypes.metaCode];
        if (existing !== undefined && shallowequal(existing, deviceTypes)) {
          return;
        }

        sliceState.entities[deviceTypes.metaCode] = deviceTypes;
        if (!_codes?.find(code => code === deviceTypes.metaCode)) {
          _codes?.push(deviceTypes.metaCode);
        }
      });
      sliceState.codes = _codes;
    },
  },
});
