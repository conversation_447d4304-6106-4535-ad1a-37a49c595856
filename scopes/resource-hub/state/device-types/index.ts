import deviceTypesWatchers from './device-types.saga';
import { deviceTypesSlice } from './device-types.slice';

export type { DeviceTypesSliceState, DeviceType } from './device-types.slice';
export * from './device-types.action';
export * from './device-types.selector';
export { deviceTypesWatchers };
export default {
  [deviceTypesSlice.name]: deviceTypesSlice.reducer,
};

// dev use only
// ------
export { createCompositionWrapper } from './device-types.composition-wrapper.creator';
