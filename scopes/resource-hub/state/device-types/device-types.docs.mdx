---
description: 'DeviceTypes redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import deviceTypesSliceReducer from '@manyun/[scope].state.device-types';

const rootReducer = {
  ...deviceTypesSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { deviceTypesWatchers } from '@manyun/[scope].state.device-types';

const function* rootSaga() {
  yield all(
    ...deviceTypesWatchers,
    // other sagas...
  );
};
```