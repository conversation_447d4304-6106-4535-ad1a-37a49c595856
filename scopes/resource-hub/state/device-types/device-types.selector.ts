import { deviceTypesSlice } from './device-types.slice';
import type { DeviceTypesSliceState } from './device-types.slice';

export const selectDeviceTypes = (storeState: {
  [deviceTypesSlice.name]: DeviceTypesSliceState;
}) => {
  return storeState[deviceTypesSlice.name];
};
export const selectDeviceTypesEntities =
  (codes?: string[]) => (storeState: { [deviceTypesSlice.name]: DeviceTypesSliceState }) => {
    const { entities } = storeState[deviceTypesSlice.name];

    if (codes === undefined) {
      return Object.keys(entities).map(code => entities[code]);
    }

    return codes.map(code => entities[code]);
  };

export const selectDeviceTypesCodes = (storeState: {
  [deviceTypesSlice.name]: DeviceTypesSliceState;
}) => {
  const { codes } = storeState[deviceTypesSlice.name];
  return codes;
};

export const selectDefaultDeviceTypes = (storeState: {
  [deviceTypesSlice.name]: DeviceTypesSliceState;
}) => {
  const { entities } = storeState[deviceTypesSlice.name];

  return entities['10101'];
};
