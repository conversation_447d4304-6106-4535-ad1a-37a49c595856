import { createAction } from '@reduxjs/toolkit';

import type { DeviceNode } from '@manyun/resource-hub.service.fetch-device-type-tree';
import type { RequestError } from '@manyun/service.request';

import { deviceTypesSlice } from './device-types.slice';

const PREFIX = `${deviceTypesSlice.name}/` as const;
const POSTFIX = '/async' as const;

export const deviceTypesSliceActions = deviceTypesSlice.actions;

export type GetDeviceTypesActionPayload = {
  numbered?: boolean | null;
  callback?: (
    error: RequestError | null,
    /** 返回最新分类数据 */
    deviceTypes: { codes: string[]; entities: Record<string, Omit<DeviceNode, 'children'>> }
  ) => void;
  idcTag?: string;
  blockTag?: string;
  roomGuidList?: string[];
};
const GET_DEVICE_TYPES_ACTION_TYPE = `${PREFIX}get-device-types${POSTFIX}` as const;
export const getDeviceTypesAction = createAction<
  GetDeviceTypesActionPayload | undefined,
  typeof GET_DEVICE_TYPES_ACTION_TYPE
>(GET_DEVICE_TYPES_ACTION_TYPE);
