import type { Task } from 'redux-saga';
import { call, cancel, fork, put, select, takeEvery, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { Metadata, MetadataJSON } from '@manyun/resource-hub.model.metadata';
import { createMetaDataWeb } from '@manyun/resource-hub.service.create-meta-data';
import { deleteMetaDataWeb } from '@manyun/resource-hub.service.delete-meta-data';
import { editMetaData } from '@manyun/resource-hub.service.edit-meta-data';
import { fetchMetaDataByTypeWeb } from '@manyun/resource-hub.service.fetch-meta-data-by-type';

import { getMetaDataAction, metaDataSliceActions, mutateMetaDataAction } from './meta-data.action';
import { selectMetaData } from './meta-data.selector';
import { getStaticReadPolicy } from './meta-data.utils';

/** Workers */

export function* getMetaData({
  payload: { metaType, queryDeleted, callback },
}: ReturnType<typeof getMetaDataAction>) {
  yield put(metaDataSliceActions.fetchMetaDataStart({ metaType }));
  let callbackData: Metadata[] = [];
  if (metaType === MetaType.EVENT_TYPE) {
    const { error, data }: SagaReturnType<typeof fetchMetaDataByTypeWeb> = yield call(
      fetchMetaDataByTypeWeb,
      {
        type: MetaType.EVENT_TOP_CATEGORY,
        queryDeleted,
      }
    );
    if (error) {
      message.error(error.message);
      yield put(metaDataSliceActions.fetchMetaDataError({ metaType: MetaType.EVENT_TOP_CATEGORY }));
    } else {
      const topData = data.data;
      if (topData) {
        const { error, data }: SagaReturnType<typeof fetchMetaDataByTypeWeb> = yield call(
          fetchMetaDataByTypeWeb,
          {
            type: MetaType.EVENT_SECOND_CATEGORY,
            queryDeleted,
          }
        );
        if (error) {
          message.error(error.message);
          yield put(
            metaDataSliceActions.fetchMetaDataError({ metaType: MetaType.EVENT_SECOND_CATEGORY })
          );
        } else {
          const secData = data.data;

          if (secData) {
            callbackData = [...topData, ...secData];
            yield put(
              metaDataSliceActions.setMetaData({
                data: [...topData, ...secData],
                metaType: MetaType.EVENT_TYPE,
                total: data.total,
              })
            );
          }
        }
      }
    }
  } else {
    const { error, data }: SagaReturnType<typeof fetchMetaDataByTypeWeb> = yield call(
      fetchMetaDataByTypeWeb,
      {
        type: metaType,
        queryDeleted,
      }
    );
    if (error) {
      message.error(error.message);
      yield put(metaDataSliceActions.fetchMetaDataError({ metaType }));
    } else {
      callbackData = data.data;
      yield put(metaDataSliceActions.setMetaData({ ...data, metaType }));
    }
  }
  if (callback && typeof callback === 'function') {
    callback(callbackData);
  }
}

export function* mutateMetaData({
  payload: { type, params, metaType, callback },
}: ReturnType<typeof mutateMetaDataAction>) {
  yield put(metaDataSliceActions.fetchMetaDataStart({ metaType }));
  if (type === 'CREATE') {
    const { error, data }: SagaReturnType<typeof createMetaDataWeb> = yield call(
      //  @ts-ignore: for bit tag ,note for now
      createMetaDataWeb,
      params
    );
    if (error) {
      message.error(error.message);
      callback?.();
      return;
    }
    message.success('新增成功');
    yield put(metaDataSliceActions.mutateMetaDataSuccess({ data: data as MetadataJSON, metaType }));
    if (callback && typeof callback === 'function') {
      callback();
    }
  }
  if (type === 'EDIT') {
    //  @ts-ignore: for bit tag ,note for now
    const { error, data }: SagaReturnType<typeof editMetaData> = yield call(editMetaData, params);
    if (callback) {
      callback(!error);
    }
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('编辑成功');

    yield put(metaDataSliceActions.mutateMetaDataSuccess({ data: data as MetadataJSON, metaType }));
    if (callback && typeof callback === 'function') {
      callback();
    }
  }

  if (type === 'DELETE') {
    const { error, data }: SagaReturnType<typeof deleteMetaDataWeb> = yield call(
      //  @ts-ignore: for bit tag ,note for now
      deleteMetaDataWeb,
      params
    );
    if (callback) {
      callback(!error);
    }
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功！');
    yield put(metaDataSliceActions.deleteMetaDataSuccess({ id: data, metaType }));
  }
}

/** Watchers */

let lastTask: Task | undefined = undefined;
let lastActionPayloadMetaType: MetaType | undefined = undefined;

function* getMetaDataSaga(action: ReturnType<typeof getMetaDataAction>) {
  const existing: SagaReturnType<ReturnType<typeof selectMetaData>> = yield select(
    selectMetaData(action.payload.metaType)
  );

  const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.codes);
  // 得判断请求的metaCode是否相同，相同才做性能优化
  if (staticPolicy === 'network-only' && !existing.loading) {
    if (lastTask && lastActionPayloadMetaType === action.payload.metaType) {
      yield cancel(lastTask);
    }
    lastTask = yield fork(getMetaData, action);
    lastActionPayloadMetaType = action.payload.metaType;
  }
}

/** Watchers */

function* watchGetMetaData() {
  yield takeEvery(getMetaDataAction.type, getMetaDataSaga);
}

function* watchMutateMetaData() {
  yield takeLatest(mutateMetaDataAction.type, mutateMetaData);
}

export default [fork(watchGetMetaData), fork(watchMutateMetaData)];
