import memoizeOne from 'memoize-one';

import type { MetaType } from '@manyun/resource-hub.model.metadata';

import { metaDataSlice } from './meta-data.slice';
import type { MetaDataSliceState } from './meta-data.slice';

const selectMetaDataBeforeMemoize =
  (metaType: MetaType) => (storeState: { [metaDataSlice.name]: MetaDataSliceState }) => {
    const data = storeState[metaDataSlice.name].metaData?.[metaType];
    if (!data) {
      return {
        total: 0,
        codes: [],
        loading: false,
        entities: {},
        data: [],
        keyMapper: {},
        permissions: null,
      };
    }
    const { total, codes, loading, entities, keyMapper } = data;
    return {
      total: total || 0,
      codes: codes || [],
      loading,
      keyMapper: keyMapper || {},
      entities: entities || {},
      data:
        (codes as string[])?.map(code => ({
          label: entities[code]?.name,
          type: entities[code]?.type,
          value: entities[code]?.code,
          parentCode: entities[code]?.parentCode,
          id: entities[code]?.id,
          description: entities[code]?.description,
          permissions: entities[code]?.permissions,
          tag: entities[code]?.tag,
        })) || [],
    };
  };

export const selectMetaData = memoizeOne(selectMetaDataBeforeMemoize);
