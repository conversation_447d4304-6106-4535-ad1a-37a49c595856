---
description: 'MetaData redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import metaDataSliceReducer from '@manyun/[scope].state.meta-data';

const rootReducer = {
  ...metaDataSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { metaDataWatchers } from '@manyun/[scope].state.meta-data';

const function* rootSaga() {
  yield all(
    ...metaDataWatchers,
    // other sagas...
  );
};
```
