import metaDataWatchers from './meta-data.saga';
import { generateEventTypeCode, metaDataSlice } from './meta-data.slice';

export type { MetaDataSliceState, MetaDataInfos as SliceMeTaData } from './meta-data.slice';
export { generateEventTypeCode };
export * from './meta-data.action';
export * from './meta-data.selector';
export { metaDataWatchers };
export default {
  [metaDataSlice.name]: metaDataSlice.reducer,
};
