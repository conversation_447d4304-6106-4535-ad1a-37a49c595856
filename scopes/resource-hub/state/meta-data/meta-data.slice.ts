import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetadataJSON } from '@manyun/resource-hub.model.metadata';

export type MetaDataInfos = {
  total: number;
  codes: string[];
  loading: boolean;
  entities: Record<string, MetadataJSON>;
  keyMapper: Record<number, string>;
};

export type MetaDataSliceState = {
  metaData: Record<MetaType, MetaDataInfos>;
};

type MetaDataSliceCaseReducers = {
  fetchMetaDataStart: CaseReducer<MetaDataSliceState, PayloadAction<{ metaType: MetaType }>>;
  setMetaData: CaseReducer<
    MetaDataSliceState,
    PayloadAction<{
      data: MetadataJSON[];
      total: number;
      metaType: MetaType;
    }>
  >;
  fetchMetaDataError: CaseReducer<MetaDataSliceState, PayloadAction<{ metaType: MetaType }>>;
  mutateMetaDataSuccess: CaseReducer<
    MetaDataSliceState,
    PayloadAction<{ metaType: MetaType; data: MetadataJSON }>
  >;
  deleteMetaDataSuccess: CaseReducer<
    MetaDataSliceState,
    PayloadAction<{ metaType: MetaType; id: number }>
  >;
};
export const generateEventTypeCode = (metaData: MetadataJSON) => {
  return `${metaData.type}_$$_${metaData.code}`;
};
export const metaDataSlice = createSlice<
  MetaDataSliceState,
  MetaDataSliceCaseReducers,
  'resource.meta-data'
>({
  name: 'resource.meta-data',
  initialState: {
    metaData: {
      [MetaType.ACCESS_CARD_TOP_CATEGORY]: createInitialSpecificMetaDataInfos(),
      [MetaType.ACCESS_CARD_SECOND_CATEGORY]: createInitialSpecificMetaDataInfos(),
      [MetaType.ADJUST_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.ALARM_LEVEL]: createInitialSpecificMetaDataInfos(),
      [MetaType.ALARM_REASON]: createInitialSpecificMetaDataInfos(),
      [MetaType.APPROVAL_VARIABLE]: createInitialSpecificMetaDataInfos(),
      [MetaType.Block]: createInitialSpecificMetaDataInfos(),
      [MetaType.CHANGE]: createInitialSpecificMetaDataInfos(),
      [MetaType.CHANGE_REASON]: createInitialSpecificMetaDataInfos(),
      [MetaType.CUSTOMER_LEVEL]: createInitialSpecificMetaDataInfos(),
      [MetaType.DEVICE_GENERAL]: createInitialSpecificMetaDataInfos(),
      [MetaType.DEVICE_POWER_LINE]: createInitialSpecificMetaDataInfos(),
      [MetaType.DEVICE_UNIT]: createInitialSpecificMetaDataInfos(),
      [MetaType.EVENT_LEVEL]: createInitialSpecificMetaDataInfos(),
      [MetaType.EVENT_SOURCE]: createInitialSpecificMetaDataInfos(),
      [MetaType.EVENT_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.EVENT_TOP_CATEGORY]: createInitialSpecificMetaDataInfos(),
      [MetaType.EVENT_SECOND_CATEGORY]: createInitialSpecificMetaDataInfos(),
      [MetaType.INDUSTRY]: createInitialSpecificMetaDataInfos(),
      [MetaType.Idc]: createInitialSpecificMetaDataInfos(),
      [MetaType.NOTIFY_EVENT_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.REASON_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.RESPONSIBLE_SECTOR]: createInitialSpecificMetaDataInfos(),
      [MetaType.ROOM_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.Room]: createInitialSpecificMetaDataInfos(),
      [MetaType.SPEC_UNIT]: createInitialSpecificMetaDataInfos(),
      [MetaType.TYPE_UNIT]: createInitialSpecificMetaDataInfos(),
      [MetaType.VISITOR]: createInitialSpecificMetaDataInfos(),
      [MetaType.WORK_ORDER]: createInitialSpecificMetaDataInfos(),
      [MetaType.APPROVAL_DIRECTOR]: createInitialSpecificMetaDataInfos(),
      [MetaType.COMMON_APPROVAL_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.RISK_TOP_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.RISK_PRIORITY]: createInitialSpecificMetaDataInfos(),
      [MetaType.RISK_RESOURCE]: createInitialSpecificMetaDataInfos(),
      [MetaType.RISK_SEC_TYPE]: createInitialSpecificMetaDataInfos(),
      [MetaType.IT_SERVICE]: createInitialSpecificMetaDataInfos(),
      [MetaType.ITORDER_THIRD_CATEGORY]: createInitialSpecificMetaDataInfos(),
    },
  },
  reducers: {
    fetchMetaDataStart(sliceState, { payload: { metaType } }) {
      if (!sliceState.metaData[metaType]) {
        sliceState.metaData = {
          ...sliceState.metaData,
          [metaType]: {
            loading: true,
            total: 0,
            codes: [],
            entities: {},
            keyMapper: {},
          },
        };
        return;
      }
      sliceState.metaData[metaType].loading = true;
    },
    setMetaData(sliceState, { payload: { data, total, metaType } }) {
      const codes: string[] = [];
      data.forEach(metaData => {
        let metaDataCode = metaData.code;
        if (metaType === MetaType.EVENT_TYPE) {
          metaDataCode = generateEventTypeCode(metaData);
        }

        codes.push(metaDataCode);

        // cache update performance optimization
        const existingMetaData = sliceState.metaData[metaType]?.entities[metaDataCode];
        if (existingMetaData && existingMetaData.updateAt === metaData.updateAt) {
          return;
        }
        sliceState.metaData[metaType].entities[metaDataCode] = metaData;
        sliceState.metaData[metaType].keyMapper[metaData.id as number] = metaData.code;
      });

      sliceState.metaData[metaType].codes = codes;
      sliceState.metaData[metaType].loading = false;
      sliceState.metaData[metaType].total = total;
    },
    fetchMetaDataError(sliceState, { payload: { metaType } }) {
      sliceState.metaData[metaType].loading = false;
    },
    mutateMetaDataSuccess(sliceState, { payload: { metaType, data } }) {
      if (!sliceState.metaData[metaType].entities[data.code]) {
        sliceState.metaData[metaType].codes = [...sliceState.metaData[metaType].codes, data.code];
        sliceState.metaData[metaType].keyMapper[data.id as number] = data.code;
      }
      sliceState.metaData[metaType].entities[data.code] = data;
      sliceState.metaData[metaType].loading = true;
    },
    deleteMetaDataSuccess(sliceState, { payload: { metaType, id } }) {
      const code = sliceState.metaData[metaType].keyMapper[id];
      delete sliceState.metaData[metaType].entities[code];
      const codes = (sliceState.metaData[metaType].codes as string[]).filter(
        metaDataCode => metaDataCode !== code
      );
      sliceState.metaData[metaType].codes = codes;
      delete sliceState.metaData[metaType].keyMapper[id];
    },
  },
});

function createInitialSpecificMetaDataInfos(): MetaDataInfos {
  return {
    total: 0,
    codes: [],
    loading: false,
    entities: {},
    keyMapper: {},
  };
}
