import { createAction } from '@reduxjs/toolkit';

import type { Metadata, MetaType } from '@manyun/resource-hub.model.metadata';

import { metaDataSlice } from './meta-data.slice';

type StaticReadPolicy = 'network-only' | 'cache-only';
type CallbackReadPolicy = (codes: string[]) => StaticReadPolicy;
export type ReadCustomersLevelPolicy = StaticReadPolicy | CallbackReadPolicy;

const prefix = metaDataSlice.name;

export const metaDataSliceActions = metaDataSlice.actions;

export const getMetaDataAction = createAction<{
  metaType: MetaType;
  queryDeleted?: boolean;
  policy?: ReadCustomersLevelPolicy;
  callback?: (metaData: Metadata[]) => void;
}>(prefix + '/GET_CUSTOMERS_INDUSTRY');

export const mutateMetaDataAction = createAction<{
  type: 'CREATE' | 'DELETE' | 'EDIT';
  metaType: MetaType;
  // params?: { id?: number; name?: string; type?: MetaType };
  params?: unknown;
  callback?: (result?: boolean) => void;
}>(prefix + '/MUTATE_CUSTOMERS');
