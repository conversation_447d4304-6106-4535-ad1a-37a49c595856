// Auto-generated Types
// ------

export type { QueryCustomersOnRacksArgs, CustomerOnRack } from './generated-types/graphql';

// Queries
// ------

export {
  GET_CUSTOMERS_ON_RACKS,
  useCustomersOnRacks,
  useLazyCustomersOnRacks,
} from './queries/use-customers-on-racks.query';
export type { QueryCustomersOnRacksData } from './queries/use-customers-on-racks.query';

export {
  GET_RACKS_ON_POWER_TICKET,
  useLazyRacksOnPowerTicket,
  useRacksOnPowerTicket,
} from './queries/use-racks-on-power-tickets.query';
export type { RackOnPowerTicketJSON } from './queries/use-racks-on-power-tickets.query';
// Mutations
// ------
