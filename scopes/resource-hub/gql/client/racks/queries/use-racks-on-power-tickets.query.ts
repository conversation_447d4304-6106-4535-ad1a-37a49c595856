import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Query,
  QueryRacksOnPowerTicketArgs,
  RackOnPowerTicket,
} from '../generated-types/graphql';

export type QueryRacksOnPowerTicketData = Pick<Query, 'racksOnPowerTicket'>;
export type RackOnPowerTicketJSON = RackOnPowerTicket;
export const GET_RACKS_ON_POWER_TICKET: DocumentNode = gql`
  query GetRacksOnPowerTicket(
    $taskNo: String!
    $idcTag: String!
    $blockTag: String!
    $roomTag: String!
    $sortModel: String
    $gridTag: String
    $pageNum: Int
    $pageSize: Int
  ) {
    racksOnPowerTicket(
      taskNo: $taskNo
      idcTag: $idcTag
      blockTag: $blockTag
      roomTag: $roomTag
      sortModel: $sortModel
      gridTag: $gridTag
      pageNum: $pageNum
      pageSize: $pageSize
    ) {
      racks {
        id
        taskNo
        gridGuid
        idcTag
        blockTag
        roomTag
        gridTag
        columnTag
        ratedPower
        gridType {
          code
          name
        }
        powerOnTime
        executeTime
        modified
        modReason
        checkStatus {
          code
          name
        }
        operatorBy
        operatorName
        frontRack {
          deviceGuid
          serialNumber
          deviceName
          deviceType
          deviceTag
          extendPosition
        }
        pduDevice {
          deviceGuid
          serialNumber
          deviceName
          deviceType
          deviceTag
          extendPosition
        }
        frontGrids {
          deviceGuid
          serialNumber
          deviceName
          deviceType
          deviceTag
          extendPosition
        }
        pduDevices {
          deviceGuid
          serialNumber
          deviceName
          deviceType
          deviceTag
          extendPosition
        }
        rackRowSpan
        roomRowSpan
      }
      errorPduDeviceGuids
      total
    }
  }
`;

export const useRacksOnPowerTicket = (
  options?: QueryHookOptions<QueryRacksOnPowerTicketData, QueryRacksOnPowerTicketArgs>
): QueryResult<QueryRacksOnPowerTicketData, QueryRacksOnPowerTicketArgs> =>
  useQuery(GET_RACKS_ON_POWER_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyRacksOnPowerTicket = (
  options?: LazyQueryHookOptions<QueryRacksOnPowerTicketData, QueryRacksOnPowerTicketArgs>
): LazyQueryResultTuple<QueryRacksOnPowerTicketData, QueryRacksOnPowerTicketArgs> =>
  useLazyQuery(GET_RACKS_ON_POWER_TICKET, {
    fetchPolicy: 'network-only',
    ...options,
  });
