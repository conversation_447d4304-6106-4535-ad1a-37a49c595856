import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryCustomersOnRacksArgs } from '../generated-types/graphql';

export type QueryCustomersOnRacksData = Pick<Query, 'customersOnRacks'>;

export const GET_CUSTOMERS_ON_RACKS: DocumentNode = gql`
  query CustomersOnRacks($name: String) {
    customersOnRacks(name: $name) {
      code
      name
    }
  }
`;

export const useCustomersOnRacks = (
  options?: QueryHookOptions<QueryCustomersOnRacksData, QueryCustomersOnRacksArgs>
): QueryResult<QueryCustomersOnRacksData, QueryCustomersOnRacksArgs> =>
  useQuery(GET_CUSTOMERS_ON_RACKS, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyCustomersOnRacks = (
  options?: LazyQueryHookOptions<QueryCustomersOnRacksData, QueryCustomersOnRacksArgs>
): LazyQueryResultTuple<QueryCustomersOnRacksData, QueryCustomersOnRacksArgs> =>
  useLazyQuery(GET_CUSTOMERS_ON_RACKS, {
    fetchPolicy: 'network-only',
    ...options,
  });
