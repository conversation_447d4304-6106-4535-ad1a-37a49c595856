import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: [
    {
      [`${process.env.BFF_URL}/graphql`]: {
        headers: {
          Authorization: `Bearer ${process.env.AUTH_TOKEN}`,
        },
      },
    },
  ],
  generates: {
    [`${__dirname}/generated-types/`]: {
      preset: 'client',
      config: {
        useTypeImports: true,
      },
      plugins: [
        {
          add: {
            content: '// @ts-nocheck',
          },
        },
      ],
    },
  },
};

export default config;
