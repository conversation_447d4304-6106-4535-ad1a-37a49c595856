import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryAvailableDevicesInBlockArgs } from '../generated-types/graphql';

export type QueryAvailableDevicesInBlockData = Pick<Query, 'availableDevicesInBlock'>;

export const GET_AVAILABLE_DEVICES_IN_BLOCK: DocumentNode = gql`
  query GetAvailableDevicesInBlock(
    $blockGuid: String!
    $pageNum: Int!
    $pageSize: Int!
    $numbered: Boolean!
    $deviceType: String
    $vendor: String
    $productModel: String
  ) {
    availableDevicesInBlock(
      blockGuid: $blockGuid
      pageNum: $pageNum
      pageSize: $pageSize
      numbered: $numbered
      deviceType: $deviceType
      vendor: $vendor
      productModel: $productModel
    ) {
      data {
        topCategory
        secondCategory
        deviceType
        vendor
        productModel
        roomTag
        avaliableCount
      }
      total
      success
      code
      message
    }
  }
`;

export const useAvailableDevicesInBlock = (
  options?: QueryHookOptions<QueryAvailableDevicesInBlockData, QueryAvailableDevicesInBlockArgs>
): QueryResult<QueryAvailableDevicesInBlockData, QueryAvailableDevicesInBlockArgs> =>
  useQuery(GET_AVAILABLE_DEVICES_IN_BLOCK, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyAvailableDevicesInBlock = (
  options?: LazyQueryHookOptions<QueryAvailableDevicesInBlockData, QueryAvailableDevicesInBlockArgs>
): LazyQueryResultTuple<QueryAvailableDevicesInBlockData, QueryAvailableDevicesInBlockArgs> =>
  useLazyQuery(GET_AVAILABLE_DEVICES_IN_BLOCK, {
    fetchPolicy: 'network-only',
    ...options,
  });
