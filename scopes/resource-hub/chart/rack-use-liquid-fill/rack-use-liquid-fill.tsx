import React from 'react';

import styled from 'styled-components';

import { LiquidFill } from '@manyun/base-ui.chart.liquid-fill';
import { message } from '@manyun/base-ui.ui.message';

import { fetchRackUtilizationStatistics } from '@manyun/resource-hub.service.fetch-rack-utilization-statistics';
import type { StatusCount } from '@manyun/resource-hub.service.fetch-rack-utilization-statistics';

export type RackUseLiquidFillProps = {
  idc: string;
};

const BLANK_PLACEHOLDER = '--';

export function RackUseLiquidFill({ idc }: RackUseLiquidFillProps) {
  const [statistics, setStatistics] = React.useState({} as StatusCount);
  const [seriesData, setSeriesData] = React.useState({});

  const fetchStatistics = React.useCallback(async () => {
    const { data, error } = await fetchRackUtilizationStatistics({ idcTag: idc });
    if (error) {
      message.error(error.message);
    }
    setStatistics(data);
    setSeriesData({
      value: data.TOTAL ? data.USED / data.TOTAL : '--',
    });
  }, [idc]);

  React.useEffect(() => {
    fetchStatistics();
  }, [fetchStatistics]);

  return (
    <Chart
      style={{ position: 'absolute', width: '100%', display: 'flex', justifyContent: 'flex-start' }}
    >
      <div
        style={{
          width: '50%',
          height: '138px',
          paddingLeft: 30,
          paddingTop: 42,
        }}
      >
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          机柜总数
          <span>{`${statistics?.TOTAL || BLANK_PLACEHOLDER}台`}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between', padding: '5px 0' }}>
          使用中<span>{`${statistics?.USED || BLANK_PLACEHOLDER}台`}</span>
        </div>
        <div style={{ display: 'flex', justifyContent: 'space-between' }}>
          未使用<span>{`${statistics?.UNUSED || BLANK_PLACEHOLDER}台`}</span>
        </div>
      </div>
      <LiquidFill
        style={{
          width: '50%',
          height: '138px',
        }}
        series={[
          {
            backgroundStyle: {
              color: '#E6F7FF',
            },
            itemStyle: {
              shadowBlur: 0,
            },
            radius: '85%',
            data: [
              {
                ...seriesData,
                itemStyle: {
                  normal: {
                    color: 'rgba(24,144,255)',
                  },
                },
              },
              {
                ...seriesData,
                itemStyle: {
                  normal: {
                    color: 'rgba(24,144,255,0.3)',
                  },
                },
              },
            ],
            outline: {
              show: true,
              borderDistance: 6,
              itemStyle: {
                borderWidth: 1,
                borderColor: '#1890FF',
              },
            },
            label: {
              fontSize: 24,
              formatter: function ({ value }) {
                return typeof value === 'number' ? `${Number((value * 100).toFixed(1))}%` : '--';
              },
            },
          },
        ]}
      />
    </Chart>
  );
}

const Chart = styled.div`
  > div > div > canvas {
    cursor: default;
  }
`;
