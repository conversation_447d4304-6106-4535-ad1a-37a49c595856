import { useSelector } from 'react-redux';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

export function useSpacesType() {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const idcDT: string = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_IDC);

  const gridDT: string = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
  const columnDT: string = configUtil.getOneDeviceType(
    ConfigUtil.constants.deviceTypes.SPACE_COLUMN
  );
  const roomDT: string = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM);
  const blockDT: string = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const spaceKeyMap = {
    IDC: idcDT,
    BLOCK: blockDT,
    ROOM: roomDT,
    COLUMN: columnDT,
    GRID: gridDT,
  };
  return { idcDT, blockDT, roomDT, columnDT, gridDT, spaceKeyMap } as const;
}
