import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useMountedState } from 'react-use';

import type { Metadata, MetaType } from '@manyun/resource-hub.model.metadata';
import {
  getMetaDataAction,
  mutateMetaDataAction,
  selectMetaData,
} from '@manyun/resource-hub.state.meta-data';

type StaticReadPolicy = 'network-only' | 'cache-only';
type CallbackReadPolicy = (codes: string[]) => StaticReadPolicy;
export type ReadCustomersLevelPolicy = StaticReadPolicy | CallbackReadPolicy;
export type ReadOptions = {
  policy?: ReadCustomersLevelPolicy;
  callback?: (metaData: Metadata[]) => void;
};
const defaultReadOptions: Pick<Required<ReadOptions>, 'policy'> = {
  policy: 'network-only',
};

function getStaticReadPolicy(
  policy: StaticReadPolicy | CallbackReadPolicy = defaultReadOptions.policy,
  codes: string[]
): StaticReadPolicy {
  if (typeof policy == 'function') {
    if (codes && codes.length <= 0) {
      return 'network-only';
    }
    return policy(codes);
  }

  return policy;
}

export function useMetaData(metaType: MetaType, queryDeleted?: boolean) {
  const dispatch = useDispatch();
  const isMounted = useMountedState();

  const [loading, setLoading] = useState<boolean>(false);
  const data = useSelector(selectMetaData(metaType));
  const readMetaData = useCallback(
    (options?: ReadOptions) => {
      let opts: ReadOptions = defaultReadOptions;
      if (options && typeof options == 'object') {
        opts = options;
      }
      setLoading(true);
      const staticPolicy = getStaticReadPolicy(opts.policy, data.codes);
      dispatch(
        getMetaDataAction({
          policy: staticPolicy,
          metaType,
          queryDeleted,
          callback: metaData => {
            if (isMounted()) {
              setLoading(false);
            }
            if (opts?.callback) {
              opts.callback(metaData);
            }
          },
        })
      );
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );

  const createMetaData = useCallback(
    ({ params, callback }) => {
      dispatch(mutateMetaDataAction({ type: 'CREATE', metaType, params, callback }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );

  const editMetaData = useCallback(
    ({ params, callback }) => {
      dispatch(mutateMetaDataAction({ type: 'EDIT', metaType, params, callback }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );

  const deleteMetaData = useCallback(
    ({ params, callback }) => {
      dispatch(mutateMetaDataAction({ type: 'DELETE', metaType, params, callback }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );
  return [
    { data, loading },
    { readMetaData, createMetaData, editMetaData, deleteMetaData },
  ] as const;
}
