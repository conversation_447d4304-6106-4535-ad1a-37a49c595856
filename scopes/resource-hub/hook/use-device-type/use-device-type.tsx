import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import shallowequal from 'shallowequal';

import { getDeviceTypesAction, selectDeviceTypes } from '@manyun/resource-hub.state.device-types';
import type { DeviceType } from '@manyun/resource-hub.state.device-types';

export function useDeviceType(deviceType: string): DeviceType | undefined {
  const dispatch = useDispatch();
  React.useEffect(() => {
    dispatch(getDeviceTypesAction());
  }, [dispatch]);

  const { entities } = useSelector(selectDeviceTypes, (left, right) =>
    shallowequal(left.entities, right.entities)
  );

  return entities[deviceType];
}
