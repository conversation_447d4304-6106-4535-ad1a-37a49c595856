import { waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';

import { createCompositionWrapper } from '@manyun/resource-hub.state.device-types';
import { useRemoteMock } from '@manyun/service.request';

import { useDeviceType } from './use-device-type';

const StoreCompositionWrapper = createCompositionWrapper();

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should return device type 10101', async () => {
  const { result } = renderHook(() => useDeviceType('10101'), { wrapper: StoreCompositionWrapper });
  await waitFor(() => {
    expect(result.current!.metaCode).toBe('10101');
  });
});
