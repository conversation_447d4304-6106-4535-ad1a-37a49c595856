import React from 'react';

import { wrapCompositions } from '@manyun/dc-brain.composition.compositions-wrapper';
import { createCompositionWrapper } from '@manyun/resource-hub.state.device-types';

import { useDeviceType } from './use-device-type';

const StoreCompositionWrapper = createCompositionWrapper();
const CompositionWrapper = wrapCompositions([StoreCompositionWrapper]);

const BasicuseDeviceType = () => {
  const { metaName, metaCode } = useDeviceType('10101') ?? {};

  return (
    <code>
      {metaName}({metaCode})
    </code>
  );
};
export const RenderDeviceType = () => (
  <CompositionWrapper>
    <BasicuseDeviceType />
  </CompositionWrapper>
);
