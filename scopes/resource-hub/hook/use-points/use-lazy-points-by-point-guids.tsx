import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import type { Point } from '@manyun/monitoring.model.point';
import { fetchPoint } from '@manyun/resource-hub.service.fetch-point';

type PointGuid = {
  deviceType: string;
  pointCode: string;
};

export function useLazyPointsByPointGuids() {
  const [points, setPoints] = useState<Point[]>([]);

  const getPoints = useCallback(async (pointGuids: PointGuid[]) => {
    if (!pointGuids.length) {
      console.error(`缺少参数：pointGuids`);
      return;
    }
    const devicePointList = pointGuids.map(point => `${point.deviceType}.${point.pointCode}`);
    const { data, error } = await fetchPoint({ devicePointList });
    if (error) {
      message.error(error.message);
    }
    setPoints(data.data);
  }, []);

  return [getPoints, { points }] as const;
}
