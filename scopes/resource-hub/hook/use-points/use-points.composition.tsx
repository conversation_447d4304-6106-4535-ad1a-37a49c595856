import React, { useEffect } from 'react';

import { useLazyPointsByPointGuids } from './use-lazy-points-by-point-guids';
import { usePoints } from './use-points';

export const BasicusePoints = () => {
  const [{ loading }, getPoints] = usePoints({ lazy: true });

  React.useEffect(() => {
    getPoints({ deviceType: '10101', isOnlyCore: true });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <>
      <h1>service请求状态loading：{loading}</h1>
    </>
  );
};

export const BasicUseLazyPointsByPointGuids = () => {
  const [getPoints, { points }] = useLazyPointsByPointGuids();

  useEffect(() => {
    getPoints([
      {
        deviceType: '10101',
        pointCode: '1001000',
      },
    ]);
  }, [getPoints]);

  return <>name: {points[0]?.name}</>;
};
