---
description: 'A React Hook to fetch points list'
labels: ['hook', 'use-points']
---

import { usePoints } from './use-points';

## 获取设备类型、空间类型或者空间实例下的测点列表

```js
import { usePoints } from './use-points';

const [{ loading, data }, getPoints] = usePoints();
```

## 新增：延迟获取测点 hook

```ts
import { useLazyPointsByPointGuids } from './use-lazy-points-by-point-guids';

const [getPoints, { points }] = useLazyPointsByPointGuids();
```
