import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import useDeepCompareEffect from 'use-deep-compare-effect';

import type { SvcQuery } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { getPointsAction, selectPointsEntities } from '@manyun/resource-hub.state.points';

export function usePoints({ fields = {}, lazy = false }: { fields?: SvcQuery; lazy?: boolean }) {
  const [pointKeys, setPointKeys] = useState<string[]>([]);
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();

  const getPoints = useCallback(
    (fields: SvcQuery) => {
      setLoading(true);
      dispatch(
        getPointsAction({
          fields,
          callback: (_pointKeys: string[]) => {
            setLoading(false);
            setPointKeys(_pointKeys);
          },
        })
      );
    },
    [dispatch]
  );

  useDeepCompareEffect(() => {
    if (lazy) {
      return;
    }
    getPoints(fields);
  }, [lazy, fields, getPoints]);

  const points = useSelector(selectPointsEntities(pointKeys));

  return [{ loading, data: points }, getPoints] as const;
}
