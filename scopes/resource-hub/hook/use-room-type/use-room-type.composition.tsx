import React from 'react';

import { wrapCompositions } from '@manyun/dc-brain.composition.compositions-wrapper';
import { createCompositionWrapper } from '@manyun/resource-hub.state.room-type';

import { useRoomType } from './use-room-type';

const StoreCompositionWrapper = createCompositionWrapper();
const CompositionWrapper = wrapCompositions([StoreCompositionWrapper]);

const BasicuseRoomType = () => {
  const { code, text } = useRoomType('IT_ROOM') ?? {};

  return (
    <code>
      {text}({code})
    </code>
  );
};
export const RenderDeviceType = () => (
  <CompositionWrapper>
    <BasicuseRoomType />
  </CompositionWrapper>
);
