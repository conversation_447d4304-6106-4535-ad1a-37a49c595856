import { waitFor } from '@testing-library/react';
import { renderHook } from '@testing-library/react-hooks';

import { createCompositionWrapper } from '@manyun/resource-hub.state.room-type';
import { useRemoteMock } from '@manyun/service.request';

import { useRoomType } from './use-room-type';

const StoreCompositionWrapper = createCompositionWrapper();

let mockOff: ReturnType<typeof useRemoteMock>;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should return device type IT_ROOM', async () => {
  const { result } = renderHook(() => useRoomType('IT_ROOM'), { wrapper: StoreCompositionWrapper });
  await waitFor(() => {
    expect(result.current!.code).toBe('IT_ROOM');
  });
});
