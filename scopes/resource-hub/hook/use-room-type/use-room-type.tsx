import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import shallowequal from 'shallowequal';

import { getRoomTypeAction, selectRoomTypeEntities } from '@manyun/resource-hub.state.room-type';
import type { RoomType } from '@manyun/resource-hub.state.room-type';

export function useRoomType(roomType: string): RoomType | undefined {
  const dispatch = useDispatch();
  React.useEffect(() => {
    dispatch(getRoomTypeAction());
  }, [dispatch]);

  const entities = useSelector(selectRoomTypeEntities, (left, right) => shallowequal(left, right));

  return entities[roomType];
}
