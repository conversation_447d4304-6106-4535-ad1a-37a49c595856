/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { Metadata } from '@manyun/resource-hub.model.metadata';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, ServiceQ, SvcRespData } from './edit-meta-data.type';

const endpoint = '/dccm/meta/update';

/**
 * @see [编辑元数据](http://172.16.0.17:13000/project/88/interface/api/2472)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ id, name, type }: ServiceQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { id, metaName: name, metaType: type };
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return { error, ...rest, data: null };
    }

    return { error, data: Metadata.fromApiObject(data), ...rest };
  };
}
