/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './edit-meta-data';
import type { ApiQ, SvcRespData } from './edit-meta-data.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function editMetaData(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
