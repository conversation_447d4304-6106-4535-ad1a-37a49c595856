/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendMetadata, Metadata, MetaType } from '@manyun/resource-hub.model.metadata';

export type SvcRespData = Metadata | null;

export type RequestRespData = BackendMetadata | null;
export type ServiceQ = {
  id?: number;
  name?: string;
  type?: MetaType;
};
export type ApiQ = {
  id?: number;
  metaName?: string;
  metaType?: string;
};

export type ApiR = Response<RequestRespData>;
