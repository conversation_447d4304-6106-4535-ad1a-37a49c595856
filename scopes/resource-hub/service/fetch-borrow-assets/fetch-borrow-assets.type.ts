/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AssetType = 'OFF_LINE' | 'ON_LINE';

export type BorrowAsset = {
  id: string;
  assetType: AssetType;
  borrowNo: string;
  borrowNum: number;
  returnNum: number;
  roomTag: string;
  roomGuid: string;
  assetNo: string;
  deviceName?: string;
  deviceGuid: string;
  numbered: boolean;
  deviceType: string;
  productModel: string;
  vendor: string;
  gmtCreate: number;
  gmtModified: number;
  topCategory: string;
  secondCategory: string;
  borrowApplyId: number;
  borrowType: 'BORROW' | 'RETURN';
  remark: string;
};

export type SvcQuery = {
  borrowNo: string;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: BorrowAsset[];
  total: number;
} | null;

export type SvcRespData = {
  data: BorrowAsset[];
  total: number;
};

export type ApiR = ListResponse<BorrowAsset[] | null>;
