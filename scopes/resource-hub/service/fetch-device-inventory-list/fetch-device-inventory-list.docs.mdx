---
description: 'A fetchDeviceInventoryList HTTP API service.'
labels: ['service', 'http', fetch-device-inventory-list]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceInventoryList } from '@resource-hub/service.fetch-device-inventory-list';

const { error, data } = await fetchDeviceInventoryList({ deviceGuid: '10101' });
```

### Node

```ts
import { fetchDeviceInventoryList } from '@resource-hub/service.fetch-device-inventory-list/dist/index.node';

const { data } = await fetchDeviceInventoryList({ deviceGuid: '10101' });

try {
  const { data } = await fetchDeviceInventoryList({ deviceGuid: '' });
} catch (error) {
  // ...
}
```
