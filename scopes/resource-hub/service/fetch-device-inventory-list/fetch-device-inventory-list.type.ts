/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceInventoryInfo = {
  /**状态 */
  status: InventoryDeviceStatusInfoType;
  /**设备类型 */
  deviceType: string;
  /**厂商 */
  vendor: string;
  /**型号 */
  productModel: string;
  /**备件库存 */
  inventoryNum: number;
  /** 安全库存 */
  safeNum: number;
  /**预到货数据量 */
  preNum: number;
};

export type DeviceInventoryListModel = {
  /** 概览 */
  deviceData: DeviceInventoryInfo;
  // 子配件
  subInventory: DeviceInventoryInfo[];
  parentInventory: DeviceInventoryInfo[];
};

export type SvcRespData = DeviceInventoryListModel | null;

export type RequestRespData = DeviceInventoryListModel | null;

export type ApiQ = {
  deviceGuid: string;
};

export type ApiR = ListResponse<DeviceInventoryListModel[] | null>;

export enum InventoryDeviceStatusInfo {
  /** 库存不足 */
  'INSUFFICIENT' = 'INSUFFICIENT',
  /** 库存较少 */
  'LESS' = 'LESS',
  /** 库存充足 */
  'ADEQUATE' = 'ADEQUATE',
}

export type InventoryDeviceStatusInfoType = keyof typeof InventoryDeviceStatusInfo;

export const inventoryDeviceStatusInfoMap: Record<InventoryDeviceStatusInfoType, string> = {
  [InventoryDeviceStatusInfo.ADEQUATE]: '库存充足',
  [InventoryDeviceStatusInfo.INSUFFICIENT]: '库存不足',
  [InventoryDeviceStatusInfo.LESS]: '库存较少',
};
