/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-device-alarm-status-count.type';

const endpoint = '/dcim/alarm/status/count';

/**
 * @see [告警状态统计](http://172.16.0.17:13000/project/86/interface/api/18144)
 * 告警状态统计
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: data, ...rest };
  };
}
