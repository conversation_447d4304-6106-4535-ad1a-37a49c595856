/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-30
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceAlarmStatusCountModel = {
  ACTIVE: number;
  CONFIRMED: number;
  PROCESS: number;
  REMOVED: number;
  PROCESS_CONFIRMED?: number;
};

export type SvcRespData = RequestRespData;

export type RequestRespData = DeviceAlarmStatusCountModel | null;

export type ApiQ = {
  deviceGuid: string;
};

export type ApiR = ListResponse<DeviceAlarmStatusCountModel[] | null>;
