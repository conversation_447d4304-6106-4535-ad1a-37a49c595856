---
description: 'A fetchDeviceAlarmStatusCount HTTP API service.'
labels: ['service', 'http', fetch-device-alarm-status-count]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceAlarmStatusCount } from '@resource-hub/service.fetch-device-alarm-status-count';

const { error, data } = await fetchDeviceAlarmStatusCount({ deviceGuid: '10101' });
```

### Node

```ts
import { fetchDeviceAlarmStatusCount } from '@resource-hub/service.fetch-device-alarm-status-count/dist/index.node';

const { data } = await fetchDeviceAlarmStatusCount({ deviceGuid: '10101' });

try {
  const { data } = await fetchDeviceAlarmStatusCount({ deviceGuid: '' });
} catch (error) {
  // ...
}
```
