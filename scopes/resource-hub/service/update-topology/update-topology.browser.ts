/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-topology';
import type { SvcQuery, SvcRespData } from './update-topology.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */

export function updateTopology(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
