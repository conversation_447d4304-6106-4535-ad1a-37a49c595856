/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-15
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  bizScenario?: {
    uniqueIdentity: string;
  };
  blocks?: string[];
  tenantId?: string;
  topologyJson?: string;
  viewJson?: string;
  id: string;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
