/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-borrow.type';

const endpoint = '/dccm/borrow/create';

/**
 * @see [创建借用单](http://172.16.0.17:13000/project/306/interface/api/24570)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
