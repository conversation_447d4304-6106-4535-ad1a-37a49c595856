/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BorrowAsset } from '@manyun/resource-hub.service.fetch-borrow-assets';

export type BorrowAssetInfo = Pick<
  BorrowAsset,
  | 'assetType'
  // | 'topCategory'
  // | 'secondCategory'
  | 'deviceType'
  | 'vendor'
  | 'productModel'
  | 'numbered'
> & {
  targetRoomGuid: string;
  targetRoomTag: string;
  applyNum: number;
};

export type SvcQuery = {
  title: string;
  idcTag: string;
  blockGuid: string;
  borrowType: string;
  borrowerType?: string;
  borrower?: number;
  borrowerName: string;
  personLiable?: string;
  personLiableName?: string;
  borrowStartDate: number;
  borrowEndDate: number;
  reason: string;
  borrowAssetInfoList: BorrowAssetInfo[];
  fileInfoList: McUploadFileJSON[];
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
