/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchSpareNum as webService } from './fetch-spare-num.browser';
import { fetchSpareNum as nodeService } from './fetch-spare-num.node';
import type { ApiQ } from './fetch-spare-num.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});
const params: ApiQ = {
  dimension: 'BLOCK',
  locationGuid: 'EC06.A',
  productModel: 'ces',
  spareType: '70101',
  vendor: 'zj01',
};

const errorParams: ApiQ = {
  ...params,
  dimension: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
