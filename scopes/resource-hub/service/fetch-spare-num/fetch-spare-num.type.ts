/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockGuidList: string[];
  productModel: string;
  spareType: string;
  vendor: string;
};

export type SvcRespData = number;

export type RequestRespData = number | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<number>;
