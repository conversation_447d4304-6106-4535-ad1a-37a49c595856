/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-spare-num.type';

const endpoint = '/dccm/spare/num/query';

/**
 * @see [可用耗材数量查询](http://172.16.0.17:13000/project/42/interface/api/24798)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: 0,
      };
    }

    return {
      error,
      data,
      ...rest,
    };
  };
}
