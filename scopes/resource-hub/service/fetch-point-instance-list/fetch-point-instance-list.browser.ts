/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-instance-list';
import type { SvcQuery, SvcRespData } from './fetch-point-instance-list.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPointInstanceList(
  svcQuery?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
