/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point-instance-list';
import type { SvcQuery, SvcRespData } from './fetch-point-instance-list.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPointInstanceList(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
