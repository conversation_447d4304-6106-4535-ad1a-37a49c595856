/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './transfer-borrow-asset';
import type { SvcQuery, SvcRespData } from './transfer-borrow-asset.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function transferBorrowAsset(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
