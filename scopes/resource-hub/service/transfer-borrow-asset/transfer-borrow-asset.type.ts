/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  borrowNo: string;
  opId?: string;
  borrowType: string;
  personLiable?: string;
  personLiableName?: string;
  borrower: string;
  borrowerName: string;
  borrowerType: string;
  endDate: number;
  reason: string;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
