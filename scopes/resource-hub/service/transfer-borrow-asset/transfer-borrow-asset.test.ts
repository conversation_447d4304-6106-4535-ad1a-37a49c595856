/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { transferBorrowAsset as webService } from './transfer-borrow-asset.browser';
import { transferBorrowAsset as nodeService } from './transfer-borrow-asset.node';
import type { ApiQ } from './transfer-borrow-asset.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  opId: '43',
  borrowNo: 'JH00065',
  borrowType: 'INNER',
  borrowerType: null,
  borrower: 46,
  borrowerName: 'HL',
  personLiable: 46,
  personLiableName: 'HL',
  endDate: 1686814576000,
  reason: 'test',
};

const errorParams: ApiQ = {
  ...params,
  borrowNo: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
