/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */

import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-custom-point';
import type { ApiArgs,  } from './export-custom-point.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
* @returns
 */
export function exportCustomPoint(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
