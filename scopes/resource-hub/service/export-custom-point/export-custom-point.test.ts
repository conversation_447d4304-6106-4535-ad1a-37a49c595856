/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportCustomPoint as webService } from './export-custom-point.browser';
import { exportCustomPoint as nodeService } from './export-custom-point.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ targetGuid: 'f7d2b368718e41658267ce857316c8a2' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ targetGuid: '' });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ targetGuid: 'f7d2b368718e41658267ce857316c8a2' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ targetGuid: '' });

  expect(data).toBe(null);
});
