/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { importCustomPoint as webService } from './import-custom-point.browser';
import { importCustomPoint as nodeService } from './import-custom-point.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await webService(fd);

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await webService(fd);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await nodeService(fd);

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const fd = new FormData();
  const file: File = new File(['somthing'], 'file.xlsx', { type: 'excel/xlsx' });
  fd.append('file', file);
  const { error } = await nodeService(fd);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
