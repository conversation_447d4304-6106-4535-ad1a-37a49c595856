/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiQ = FormData;

export type ImportexcelCheck = {
  errMessage: Record<string, string>;
  errDto: {
    id: number;
    category: string;
    deviceType: string;
    deviceName: string;
    deviceGuid: string;
    pointName: string;
    pointCode: string;
    formula: string;
    description: string;
  };
};

export type ApiResponseData = {
  faultTotal: number;
  checkTotal: number;
  correctTotal: number;
  excelCheckErrDtos: ImportexcelCheck[];
};

export type ApiResponse = Response<ApiResponseData>;
