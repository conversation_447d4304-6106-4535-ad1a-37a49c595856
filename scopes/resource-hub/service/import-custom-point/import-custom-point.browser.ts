/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-custom-point';
import type { ApiQ, ApiResponse } from './import-custom-point.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function importCustomPoint(
  params: ApiQ
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(params);
}
