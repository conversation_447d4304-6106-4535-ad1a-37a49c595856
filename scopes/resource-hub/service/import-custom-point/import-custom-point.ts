/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, ApiResponse } from './import-custom-point.type';

const endpoint = '/dccm/point/custom/import';

/**
 * @see [Doc](http://172.16.0.17:13000/project/222/interface/api/25098)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data']>(endpoint, svcQuery);
  };
}
