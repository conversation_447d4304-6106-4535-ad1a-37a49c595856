/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-24
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendBorrowReturn,
  BorrowReturnJSON,
} from '@manyun/resource-hub.model.borrow-return';

export type ApplyAssetType = 'device' | 'spare' | 'all';

export type BackendBorrowReturnDetail = BackendBorrowReturn & {
  applyAssetType: ApplyAssetType;
  fileInfoList: BackendMcUploadFile[] | null;
};

export type BorrowReturnDetail = BorrowReturnJSON & {
  applyAssetType: ApplyAssetType;
  fileInfoList?: McUploadFileJSON[];
};

export type SvcQuery = {
  borrowNo: string;
};

export type SvcRespData = BorrowReturnDetail | null;

export type RequestRespData = BackendBorrowReturnDetail | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<BorrowReturnDetail | null>;
