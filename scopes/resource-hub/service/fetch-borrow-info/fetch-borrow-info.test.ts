/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchBorrowInfo as webService } from './fetch-borrow-info.browser';
import { fetchBorrowInfo as nodeService } from './fetch-borrow-info.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ borrowNo: 'JH00016' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ borrowNo: 'JH00016' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ borrowNo: '31' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
