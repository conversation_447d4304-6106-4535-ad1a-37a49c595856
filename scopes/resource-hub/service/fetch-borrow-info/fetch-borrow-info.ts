/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-24
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { BorrowReturn } from '@manyun/resource-hub.model.borrow-return';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-borrow-info.type';

const endpoint = '/dccm/borrow/detail';

/**
 * @see [借用单详情](https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#MIOeg)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: {
        ...BorrowReturn.fromApiObject(data).toJSON(),
        applyAssetType: data.applyAssetType,
        fileInfoList: data.fileInfoList?.map(item => McUploadFile.fromApiObject(item).toJSON()),
      },
      ...rest,
    };
  };
}
