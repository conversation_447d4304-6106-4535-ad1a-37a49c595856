---
description: 'A fetchBlocksByPermission HTTP API service.'
labels: ['service', 'http', fetch-blocks-by-permission]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchBlocksByPermission } from '@resource-hub/service.fetch-blocks-by-permission';

const { error, data } = await fetchBlocksByPermission('success');
const { error, data } = await fetchBlocksByPermission('error');
```

### Node

```ts
import { fetchBlocksByPermission } from '@resource-hub/service.fetch-blocks-by-permission/dist/index.node';

const { data } = await fetchBlocksByPermission('success');

try {
  const { data } = await fetchBlocksByPermission('error');
} catch(error) {
  // ...
}
```
