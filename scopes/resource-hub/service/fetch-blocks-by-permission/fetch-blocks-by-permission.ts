/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-blocks-by-permission.type';

const endpoint = '/dccm/query/block/by/permission';

/**
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/8528)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params: variant,
    });

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data,
      ...rest,
    };
  };
}
