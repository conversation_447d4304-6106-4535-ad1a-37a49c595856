/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-blocks-by-permission';
import type { ApiQ, SvcRespData } from './fetch-blocks-by-permission.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchBlocksByPermission(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
