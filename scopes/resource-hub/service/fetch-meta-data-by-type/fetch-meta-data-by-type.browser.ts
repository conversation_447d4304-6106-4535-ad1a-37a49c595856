/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import { Metadata } from '@manyun/resource-hub.model.metadata';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-meta-data-by-type';
import type { ApiQ, RequestRD, ServiceQ, ServiceRD } from './fetch-meta-data-by-type.type';

/**
 * 查询元数据列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/pkinbk/aaibne)
 *
 * @param query Service query object
 * @returns
 */
export async function fetchMetaDataByType(
  query: ServiceQ
): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = {
    type: query.type,
    queryDeleted: query.queryDeleted,
  };

  const { error, data, ...rest } = await webRequest.tryGet<RequestRD, ApiQ>(endpoint, {
    params: apiQ,
  });

  if (error || data === null || data.data === null) {
    return {
      error,
      ...rest,
      data: {
        total: 0,
        data: [],
      },
    };
  }
  return {
    error,
    ...rest,
    data: { total: data.total, data: data.data.map(Metadata.fromApiObject) },
  };
}
