---
description: 'A fetchMetaDataByType HTTP API service.'
labels: ['service', 'http', 'label3']
---

## 获取某个类型的元数据

### Browser

```ts
import { fetchMetaDataByType } from '@resource-hub/service.dccm.fetch-meta-data-by-type/dist/index.browser';

import { MetaType } from '@manyun/resource-hub.model.metadata';

const { error, data } = await fetchMetaDataByType({ type: MetaType.NOTIFY_EVENT_TYPE });
```
