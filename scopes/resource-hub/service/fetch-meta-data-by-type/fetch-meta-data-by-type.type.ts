/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendMetadata, Metadata, MetaType } from '@manyun/resource-hub.model.metadata';

export type ServiceQ = {
  type: MetaType;
  /**是否查询已软删除的数据，默认false */
  queryDeleted?: boolean;
};

export type ServiceRD = {
  data: Metadata[];
  total: number;
};

export type BaseRequestRD = BackendMetadata[] | null;

export type RequestRD = {
  data: BaseRequestRD;
  total: number;
} | null;

export type ApiQ = {
  type: string;
  queryDeleted?: boolean;
};

export type ApiR = ListResponse<BaseRequestRD>;
