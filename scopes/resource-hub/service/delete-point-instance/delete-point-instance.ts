/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './delete-point-instance.type';

const endpoint = '/dccm/point/instance/delete';

/**
 * @see [删除点位实例](http://172.16.0.17:13000/project/140/interface/api/19353)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );
    return { error, data, ...rest };
  };
}
