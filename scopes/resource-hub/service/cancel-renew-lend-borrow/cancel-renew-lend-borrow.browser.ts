/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-2
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancel-renew-lend-borrow';
import type { SvcQuery, SvcRespData } from './cancel-renew-lend-borrow.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function cancelRenewLendBorrow(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
