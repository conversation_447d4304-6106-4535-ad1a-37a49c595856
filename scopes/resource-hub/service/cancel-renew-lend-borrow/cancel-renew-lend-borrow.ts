/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-2
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './cancel-renew-lend-borrow.type';

const endpoint = '/dccm/borrow/asset/renew/transfer/revert';

/**
 * @see [续转审批撤回](https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#Bx4qC)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
