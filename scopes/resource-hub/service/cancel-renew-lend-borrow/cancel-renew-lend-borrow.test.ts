/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-2
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { cancelRenewLendBorrow as webService } from './cancel-renew-lend-borrow.browser';
import { cancelRenewLendBorrow as nodeService } from './cancel-renew-lend-borrow.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    opId: '653',
    reason: 'test',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ opId: '', reason: 'test' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({
    opId: '653',
    reason: 'test',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ opId: '', reason: 'test' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
