/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-7
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-grid-statistics';
import type { ApiArgs, ApiResponse } from './fetch-grid-statistics.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function fetchGridStatistics(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
