/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-7
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 楼栋guid
   */
  blockGuid: string;
  /**
   * 上电模式
   */
  powerStatus?: string | null;
  /**
   * it类型，IT   NON_IT
   */
  itType: string;
  /**
   * 客户编号
   */
  customerNo: string;
};

export type GridStatistic = {
  /**
   * 上电状态
   */
  powerStatus: string;
  /**
   * 机柜类型
   */
  gridType?: string | null;
  /**
   * 签约功率
   */
  signedPower?: number | null;
  /**
   * 数量
   */
  count: number;
};

export type ApiResponse = ListResponse<GridStatistic[]>;
