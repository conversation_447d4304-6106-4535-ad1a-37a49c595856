/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Room = {
  id: number;
  // 机房tag
  idcTag: string;
  // 楼栋tag
  blockTag: string;

  guid: string;
  // 包间类型
  roomType: string;

  // 包间tag
  tag: string;
};

export type SvcRespData = {
  data: Room[];
  total: number;
};

export type RequestRespData = {
  data: Room[] | null;
  total: number;
} | null;

// SvcQuery 和 apiQ相同
export type ApiQ = {
  // 机房tag
  idcTag: string;
  // 楼栋tag
  blockTag: string;
  deviceTypeList?: string[];
  roomTypeList?: string[];
};

export type ApiR = ListResponse<Room[] | null>;
