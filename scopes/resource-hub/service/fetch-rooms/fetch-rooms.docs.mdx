---
description: 'A fetchRooms HTTP API service.'
labels: ['service', 'http', fetch-rooms]
---

## 概述

根据设备类型或者包间类型查询包间信息

## 使用

### Browser

```ts
import { fetchRooms } from '@resource-hub/service.fetch-rooms';

const { error, data } = await fetchRooms({...});
```

### Node

```ts
import { fetchRooms } from '@resource-hub/service.fetch-rooms/dist/index.node';

const { data } = await fetchRooms({...});

try {
  const { data } = await fetchRooms({...});
} catch (error) {
  // ...
}
```
