---
description: 'A fetchStockThresholds HTTP API service.'
labels: ['service', 'http', fetch-stock-thresholds]
---

## 概述

获取阈值列表数据

## 使用

### Browser

```ts
import { fetchStockThresholds } from '@manyun/redash.service.fetch-stock-thresholds';

const { error, data } = await fetchStockThresholds({ code: 11111 });
```

### Node

```ts
import { fetchStockThresholds } from '@manyun/redash.service.fetch-stock-thresholds/dist/index.node';

const { data } = await fetchStockThresholds('success');

try {
  const { data } = await fetchStockThresholds('error');
} catch (error) {
  // ...
}
```
