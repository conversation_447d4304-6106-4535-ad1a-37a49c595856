/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

//  SvcQuery, SvcRespData, RequestRespData,
export type BackendInventoryThreshold = {
  // 自增id
  id: number;
  // 位置，仅楼栋
  blockGuid: string;
  // 三级分类code，左侧决定
  code: string;
  // 水位阈值
  level: number;
  // 型号
  productModel?: string;
  // 厂商
  vendor?: string;
  // 类型：耗材/设备
  deviceNumType: string;
  // 创建时间
  gmtCreate: string;
  // 修改时间
  gmtModified: string;
  // 创建人
  creatorId: number;
  // 修改人
  modifierId: number;
};
export type SvcQuery = {
  deviceType: string;
};

export type SvcRespData = {
  data: {
    id: number;
    blockGuid: string;
    level: number;
    productModel?: string;
    vendor?: string;
  }[];
  total: number;
};

export type RequestRespData = {
  data: BackendInventoryThreshold[] | null;
  total: number;
} | null;

export type ApiQ = {
  code: string;
};

export type ApiR = ListResponse<BackendInventoryThreshold>;
