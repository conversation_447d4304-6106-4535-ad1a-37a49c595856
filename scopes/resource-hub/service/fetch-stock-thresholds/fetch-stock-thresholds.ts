/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-stock-thresholds.type';

const endpoint = '/dccm/inventory/warn/list';
/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/102/interface/api/3808)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { code: variant?.deviceType };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => ({
          id: item.id,
          blockGuid: item.blockGuid,
          level: item.level,
          productModel: item.productModel,
          vendor: item.vendor,
        })),
        total: data.total,
      },
      ...rest,
    };
  };
}
