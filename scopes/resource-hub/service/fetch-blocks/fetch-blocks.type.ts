/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendType } from '@manyun/resource-hub.model.block/backend-block';

export type BlockItem = {
  id: number;
  guid: string;
  tag: string;
  blockType: {
    code: BackendType;
    name: string;
  };
  idcTag: string;
  idcName: string;
  period: string;
  operationStatus: {
    code: string;
    name: string;
  };
  /** 建设日期 */
  constructTime: string;
  /** 投产日期 */
  operationTime: string;
  principalId: number;
  principalName: string;
  area: number;
  coordinate: string;
  /** 描述 */
  description: string;
  blockProperties: {
    coolingModeConfigs: {
      coolingMode: string;
      expression: string;
    }[];
    // IT设计值
    itDesign: number;
    // 市电设计值
    eleDesign: number;
    // pue设计值
    designPue: number;
    // 安全等级
    safeLevel: number;
    // 设计标准
    designNormal: string;
  };
};

export type SvcRespData = {
  data: BlockItem[];
  total: number;
};

export type RequestRespData = {
  data: BlockItem[] | null;
  total: number;
} | null;

export type ApiQ = {
  constructTimeEnd?: string;
  constructTimeStart?: string;
  idcName?: string;
  idcTag?: string;
  operationStatus?: string;
  operationTimeEnd?: string;
  operationTimeStart?: string;
  pageNum?: number;
  pageSize?: number;
  period?: string;
  tag?: string;
};

export type ApiR = ListResponse<BlockItem[] | null>;
