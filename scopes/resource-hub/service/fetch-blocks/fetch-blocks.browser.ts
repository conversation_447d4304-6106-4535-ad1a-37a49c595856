/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-blocks';
import type { ApiQ, SvcRespData } from './fetch-blocks.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchBlocks(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
