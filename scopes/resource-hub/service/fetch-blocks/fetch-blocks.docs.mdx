---
description: '查询楼栋列表'
labels: ['service', 'http']
---

## 使用

### Browser

```ts
import { fetchBlocks } from '@resource-hub/service.fetch-blocks';

const {  data } = await fetchBlocks({
    idcTag: 'EC06'
});
const { error, data } = await fetchBlocks({
    idcTag: ''
});
```

### Node

```ts
import { fetchBlocks } from '@resource-hub/service.fetch-blocks/dist/index.node';

const { error, data } = await fetchBlocks({
    idcTag: 'EC06'
});
const { error, data } = await fetchBlocks({
    idcTag: ''
});
```
