---
description: '查询机房列表'
labels: ['service', 'http']
---

## 使用

### Browser

```ts
import { fetchDataCenterList } from '@manyun/dc-brain.service.fetch-data-center-list';

const { error, data } = await fetchDataCenterList();
const { error, data } = await fetchDataCenterList();
```

### Node

```ts
import { fetchDataCenterList } from '@manyun/dc-brain.service.fetch-data-center-list/dist/index.node';

const { error, data } = await fetchDataCenterList();
```
