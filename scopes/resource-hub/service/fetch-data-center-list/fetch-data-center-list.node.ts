/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-9
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-data-center-list';
import type { SvcRespData } from './fetch-data-center-list.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDataCenterList(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
