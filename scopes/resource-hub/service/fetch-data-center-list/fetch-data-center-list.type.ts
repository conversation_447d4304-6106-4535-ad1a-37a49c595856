/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type DataCenterList = {
  address: string;
  backupOperator: string;
  cityCode: string;
  constructTime: string;
  format: string;
  districtCode: string;
  guid: string;
  // 枚举: 数据中心
  idcType: string;
  name: string;
  nationCode: string;
  // 枚举: 已启用,未启用
  operationStatus: string;
  operationTime: string;
  operatorId: string;
  operatorName: string;
  provinceCode: string;
  regionCode: string;
  // 是否有权限
  permission: boolean;
  // 机房下楼栋tag   A   B   C
  blockTags: string[];
};

export type SvcRespData = {
  data: DataCenterList[];
  total: number;
};

export type RequestRespData = {
  data: DataCenterList[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<DataCenterList[] | null>;
