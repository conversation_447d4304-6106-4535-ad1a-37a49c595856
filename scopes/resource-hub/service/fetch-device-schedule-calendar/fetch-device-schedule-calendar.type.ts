/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  CyclesType,
  JobType,
  MaintenanceCycle,
  TaskEffectStatus,
} from '@manyun/ticket.model.task';

export type SvcQuery = ApiQ;

/** 任务状态(BLUE：将要执行，GRAY:未完成，GREEN:已完成)	*/
export type ExecuteDateColorType = 'BLUE' | 'GRAY' | 'GREEN';
export enum ExecuteDateColorInfo {
  /** 将要执行 */
  'BLUE' = 'BLUE',
  /** 未完成 */
  'GRAY' = 'GRAY',
  /** 已完成 */
  'GREEN' = 'GREEN',
}
export type ExecuteDateListInfo = {
  /** 执行日期以及状态详情: 2022-06-23	*/
  executeDate?: string;
  /** 任务状态(BLUE：将要执行，GRAY:未完成，GREEN:已完成)	*/
  color?: string;

  startDate: string;
  endDate: string;
  month: string;
  effectStatus: TaskEffectStatus;
};

export type FetchDeviceScheduleCalendarDataModel = {
  /**@deprecated 被替换成了id */
  scheduleId?: number;
  /** 计划id */
  id: number;
  /**@deprecated 被替换成了 name */
  schName?: string;
  /** 计划名称 */
  name: string;
  /**@deprecated 被替换成了 jobType */
  scheduleType?: string;
  /** 计划分类  */
  jobType: JobType;

  /** mop 类型 */
  subJobType: string;
  /** mop数量 */
  subJobTypeNum: number;

  // 三级分类名称
  jobSubTypeName?: string;
  // 三级分类code
  subJobTypeCode?: string;
  /** 重复周期 */
  periodUnit: CyclesType;
  guidePeriod: MaintenanceCycle;
  blockGuid: string;
  jobItemId?: number;
  executeDateList: ExecuteDateListInfo[];
};

export type SvcRespData = {
  data: FetchDeviceScheduleCalendarDataModel[];
  total: number;
};

export type RequestRespData = {
  data: FetchDeviceScheduleCalendarDataModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  /** 三级分类 | 设备类型 */
  deviceTypeCode?: string;
  /** 月初时间 */
  startDate?: string;
  /** 月末时间 */
  endDate?: string;
  pageSize?: number;
  pageNum?: number;
  blockTag?: string;
  /** 新增 */
  blockGuidList?: string[];
  name?: string;
  jobTypeList?: string[];
  subJobType?: string;
  periodUnit?: string;
  year?: string | number;
  schLevel?: string;
};

export type ApiR = ListResponse<FetchDeviceScheduleCalendarDataModel[] | null>;
