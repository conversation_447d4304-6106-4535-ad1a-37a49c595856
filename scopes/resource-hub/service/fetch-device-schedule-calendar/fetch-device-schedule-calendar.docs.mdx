---
description: 'A fetchDeviceScheduleCalendar HTTP API service.'
labels: ['service', 'http', fetch-device-schedule-calendar]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceScheduleCalendar } from '@resource-hub/service.fetch-device-schedule-calendar';

const { error, data } = await fetchDeviceScheduleCalendar({
  deviceTypeCode: '10101',
  startDate: '2022-06-01',
  endDate: '2022-06-30',
  pageSize: 1000,
  pageNum: 1,
});
```

### Node

```ts
import { fetchDeviceScheduleCalendar } from '@resource-hub/service.fetch-device-schedule-calendar/dist/index.node';

const { data } = await fetchDeviceScheduleCalendar({
  deviceTypeCode: '10101',
  startDate: '2022-06-01',
  endDate: '2022-06-30',
  pageSize: 1000,
  pageNum: 1,
});

try {
  const { data } = await fetchDeviceScheduleCalendar({
    startDate: '2022-06-01',
    endDate: '2022-06-30',
    pageSize: 1000,
    pageNum: 1,
  });
} catch (error) {
  // ...
}
```
