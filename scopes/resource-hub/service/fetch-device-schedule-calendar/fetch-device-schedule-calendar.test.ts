/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceScheduleCalendar as webService } from './fetch-device-schedule-calendar.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    deviceTypeCode: '1090103',
    startDate: '2022-06-01',
    endDate: '2022-06-30',
    pageSize: 1000,
    pageNum: 1,
  });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

// test('[web] should resolve error response', async () => {
//   const { error, data } = await webService({
//     deviceTypeCode: '1090101',
//     startDate: '2022-06-01',
//     endDate: '2022-06-30',
//     pageSize: 1000,
//     pageNum: 0,
//   });

//   console.log('>>>error', error);

//   expect(error?.code).toBe('error');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });
// >>>error {
//   code: undefined,
//   message: 'YApi project ID not found by url(/taskcenter/schedule/year/calendar), you can ask Jerry for help!'
// }
