/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-7
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 楼栋guid
   */
  blockGuid: string;
  /**
   * 客户编号
   */
  customerNo: string;
  /**
   * it类型，IT   NON_IT
   */
  itType: string;
  /**
   * 格式 2023-06-30
   */
  startTime: string;
  /**
   * 格式 2023-07-30
   */
  endTime: string;
};

export type ApiResponseData = {
  /**
   * 时间 2023-06-30
   */
  time: string;
  /**
   * 上正式电数量
   */
  onNum?: number | null;
  /**
   * 未上电数量
   */
  offNum?: number | null;
  /**
   * 上测试电数量
   */
  testNum?: number | null;
};

export type ApiResponse = ListResponse<ApiResponseData[]>;
