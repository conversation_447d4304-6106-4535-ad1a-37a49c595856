/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-point';
import type { ApiArgs } from './export-point.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function exportPoint(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
