/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './export-point.type';

const endpoint = '/dccm/point/model/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/140/interface/api/25086)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> => {
    return await request.tryPost<Blob, ApiArgs>(endpoint, args, { responseType: 'blob' });
  };
}
