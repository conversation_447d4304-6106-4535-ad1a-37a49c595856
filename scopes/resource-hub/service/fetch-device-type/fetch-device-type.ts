/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-device-type.type';

const endpoint = 'dccm/device/type/query/detail';

/**
 * @see [查询资产类型详情](http://172.16.0.17:13000/project/136/interface/api/7376)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: { data: data.data }, ...rest };
  };
}
