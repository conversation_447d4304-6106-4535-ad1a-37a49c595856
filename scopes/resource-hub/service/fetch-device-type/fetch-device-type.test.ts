/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceType as webService } from './fetch-device-type.browser';
import { fetchDeviceType as nodeService } from './fetch-device-type.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ code: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ code: '0' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toHaveProperty('length');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ code: '2' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ code: '0' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toHaveProperty('length');
});
