/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-24
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendSpec } from '@manyun/resource-hub.model.spec';

export type BackendTypeAttributionType = 'DEVICE' | 'IT';

export type BackendDeviceTypeInfo = {
  code?: string;
  description?: string;
  deviceNum?: number;
  gmtCreate?: string;
  gmtModified?: string;
  id?: number;
  level?: string;
  name?: string;
  numbered?: boolean;
  operatorId?: number;
  operatorName?: string;
  parentCode?: string;
  pointNum?: number;
  specInfos?: BackendSpec[];
  specNum?: number;
  type?: BackendTypeAttributionType;
};

export type SvcQuery = {
  code: string;
  id?: number;
};

export type SvcRespData = {
  data: BackendDeviceTypeInfo;
} | null;

export type RequestRespData = {
  data: BackendDeviceTypeInfo | null;
};

export type ApiQ = {
  code: string;
  id?: number;
};

export type ApiR = ListResponse<BackendDeviceTypeInfo | null>;
