/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-type';
import type { SvcQuery, SvcRespData } from './fetch-device-type.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchDeviceType(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
