/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-27
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-rack-utilization-statistics';
import type {
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-rack-utilization-statistics.type';

/**
 * 统计机柜数量
 *
 * @see [统计机柜数量](http://172.16.0.17:13000/project/73/interface/api/5480)
 *
 * @param query Service query object
 * @returns
 */
export async function fetchRackUtilizationStatistics(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const apiQ = query;

  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, SvcQuery>(
    endpoint,
    apiQ
  );

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        TOTAL: 0,
        UNUSED: 0,
        USED: 0,
      },
    };
  }

  return { error, data, ...rest };
}
