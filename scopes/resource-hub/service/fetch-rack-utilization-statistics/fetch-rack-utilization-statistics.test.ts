/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-27
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchRackUtilizationStatistics as webService } from './fetch-rack-utilization-statistics.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ idcTag: 'EC01' });

  expect(error).toBe(undefined);
  expect(data!.TOTAL).toBe(10);
});

test('should resolve error response', async () => {
  const { error } = await webService({ idcTag: 'EC02' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
