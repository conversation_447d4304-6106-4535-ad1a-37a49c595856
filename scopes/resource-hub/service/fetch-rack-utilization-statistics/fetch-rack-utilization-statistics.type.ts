/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-27
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  idcTag: string;
  blockTag?: string;
};

export type type = 'USED' | 'UNUSED' | 'TOTAL';

export type StatusCount = {
  [K in type]: number;
};

export type SvcRespData = StatusCount;

export type RequestRespData = StatusCount | null;

export type ApiR = Response<RequestRespData>;
