/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { returnBorrowAsset as webService } from './return-borrow-asset.browser';
import { returnBorrowAsset as nodeService } from './return-borrow-asset.node';
import type { ApiQ } from './return-borrow-asset.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  borrowNo: 'JH00057',
  returnRoomGuid: 'EC06.A.园区门卫室',
  returnRoomTag: '园区门卫室',
  remark: '123',
  returnAssertInfoList: [
    {
      id: '43',
      assetNo: '13',
      assetType: 'ON_LINE',
      borrowNo: 'JH00057',
      borrowNum: 1,
      deviceType: '70101',
      numbered: false,
      productModel: '8888',
      returnNum: 1,
      roomGuid: 'EC06.A.A1-1',
      roomTag: 'A1-1',
      vendor: 'zj01',
    },
  ],
};

const errorParams: ApiQ = {
  ...params,
  borrowNo: '',
  returnRoomGuid: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
