/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './return-borrow-asset';
import type { SvcQuery, SvcRespData } from './return-borrow-asset.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function returnBorrowAsset(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
