/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

type ReturnAssertInfo = {
  id: string;
  assetType: 'ON_LINE' | 'OFF_LINE';
  deviceType: string;
  deviceTypeName: string;
  numbered: boolean;
  assetNo: string;
  vendor: string;
  productModel: string;
  returnNum: number;
  returnRoomGuid: string;
  returnRoomTag?: string;
};
export type SvcQuery = {
  borrowNo: string;
  ownerId: string;
  returnRoomGuid?: string;
  returnRoomTag?: string;
  remark: string;
  returnAssertInfoList: ReturnAssertInfo[];
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
