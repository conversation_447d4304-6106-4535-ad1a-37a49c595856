/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-device-norm.type';

const endpoint = '/dccm/pg/device/norm/query';

/**
 * @see [查询设备指标](http://172.16.0.17:13000/project/152/interface/api/18224)
 * 查询设备指标 | 设备综合效率 ｜ 设备MTBF MTTR
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
