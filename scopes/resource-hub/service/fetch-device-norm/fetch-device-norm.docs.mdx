---
description: 'A fetchDeviceNorm HTTP API service.'
labels: ['service', 'http', fetch-device-norm]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceNorm } from '@resource-hub/service.fetch-device-norm';

const { error, data } = await fetchDeviceNorm({
  deviceGuid: '10101',
  startTime: '2022-06-12',
  endTime: '2022-06-15',
});
```

### Node

```ts
import { fetchDeviceNorm } from '@resource-hub/service.fetch-device-norm/dist/index.node';

const { data } = await fetchDeviceNorm({
  deviceGuid: '10101',
  startTime: '2022-06-12',
  endTime: '2022-06-15',
});

try {
  const { data } = await fetchDeviceNorm({
    deviceGuid: '',
    startTime: '2022-06-12',
    endTime: '2022-06-15',
  });
} catch (error) {
  // ...
}
```
