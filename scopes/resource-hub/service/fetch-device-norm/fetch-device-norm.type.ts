/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceNormlDataModel = {
  mtbf: number;
  mttr: number;
  // 2022-06-30
  time: string;
  /** 开动率 */
  actionRate: number;
  /** 可靠性 */
  reliability: number;
  oee: number;
  /** 开动率中位数 */
  actionRateMedian?: number;
  /** 可靠性中位数 */
  reliabilityMedian?: number;
  /** oee中位数 */
  oeeMedian?: number;
  /** MTBF变化率百分比 */
  intervalRate?: number;
  /** MTTR变化率百分比 */
  repairRate?: number;
};

export type SvcRespData = {
  data: DeviceNormlDataModel[];
  total: number;
};

export type RequestRespData = {
  data: DeviceNormlDataModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  deviceGuid: string;
  startTime: string;
  endTime: string;
  /** 所属楼栋 */
  blockGuid: string;
  /** 设备类型 */
  deviceType: string;
};

export type ApiR = ListResponse<DeviceNormlDataModel[] | null>;
