/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceNorm as webService } from './fetch-device-norm.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    deviceGuid: '10101',
    startTime: '2022-06-12',
    endTime: '2022-06-15',
  });

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    deviceGuid: '10102',
    startTime: '2022-06-12',
    endTime: '2022-06-15',
  });

  expect(error?.code).toBe('error');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
