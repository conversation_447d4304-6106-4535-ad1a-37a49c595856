/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceNum as webService } from './fetch-device-num.browser';
import { fetchDeviceNum as nodeService } from './fetch-device-num.node';
import type { ApiQ } from './fetch-device-num.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  idcTag: 'EC01',
  blockTag: 'A',
  deviceType: '10901',
  vendor: 'zj01',
  productModel: 'ces',
  roomTypeList: ['WAREHOUSE'],
  filterRoomGuid: 'EC01.A.A1-1',
};

const errorParams: ApiQ = {
  ...params,
  idcTag: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
