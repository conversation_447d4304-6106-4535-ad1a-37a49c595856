/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { AssetState } from '@manyun/resource-hub.model.device';

export type SvcQuery = {
  idcTag: string;
  blockTag: string;
  deviceType: string;
  vendor: string;
  productModel: string;
  roomTypeList: string[];
  filterRoomGuid?: string;
  assetStatus?: AssetState;
};

export type SvcRespData = number;

export type RequestRespData = number | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<number>;
