/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-1
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  borrowNo: string;
  borrowApplyId: number;
  assetNo?: string;
  borrowRoomGuid?: string;
  borrowRoomTag?: string;
  ownerId?: number;
  borrowNum?: number;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
