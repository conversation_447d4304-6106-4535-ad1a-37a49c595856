/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { borrowAsset as webService } from './borrow-asset.browser';
import { borrowAsset as nodeService } from './borrow-asset.node';
import type { ApiQ } from './borrow-asset.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  borrowNo: 'JH00071',
  borrowApplyId: 134,
};

const errorParams: ApiQ = {
  borrowNo: '',
  borrowApplyId: 134,
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
