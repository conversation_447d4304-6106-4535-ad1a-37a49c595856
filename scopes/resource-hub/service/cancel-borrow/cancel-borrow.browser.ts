/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancel-borrow';
import type { SvcQuery, SvcRespData } from './cancel-borrow.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function cancelBorrow(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
