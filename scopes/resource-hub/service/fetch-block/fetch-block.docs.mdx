---
description: 'A fetchBlockDetail HTTP API service.'
labels: ['service', 'http']
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchBlockDetail } from '@resource-hub/service.fetch-block-detail';

const { data, error } = await fetchBlockDetail({
  blockGuid: 'EC03.A',
});
if (error) {
  message.error(error.message);
} else if (data?.data) {
  setCanMechine(data.data.configured);
}
```

### Node

```ts
import { fetchBlockDetail } from '@resource-hub/service.fetch-block-detail/dist/index.node';
```
