/**
 * <AUTHOR>
 * @since 2022-5-18
 *
 * @packageDocumentation
 */
import { Block } from '@manyun/resource-hub.model.block';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-block.type';

const endpoint = 'dccm/block/detail';

/**
 * @see [查询楼栋详情](http://172.16.0.17:13000/project/140/interface/api/17992)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = {
      blockGuid: svcQuery.guid,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data: Block.fromApiObject(data),
      ...rest,
    };
  };
}
