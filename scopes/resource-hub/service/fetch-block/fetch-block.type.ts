/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-18
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendBlock, Block } from '@manyun/resource-hub.model.block';

export type SvcQuery = {
  guid: string;
};

export type SvcRespData = Block | null;

export type RequestRespData = BackendBlock | null;

export type ApiQ = {
  blockGuid: string;
};

export type ApiR = Response<RequestRespData>;
