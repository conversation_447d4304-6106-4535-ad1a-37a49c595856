/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-6
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendFloor,
  FloorJSON,
  FloorType,
  OperationStatus,
} from '@manyun/resource-hub.model.floor';

export type SvcQuery = {
  pageNum: number;
  pageSize: number;
  blockGuidList?: string[];
  name?: string;
  floorType?: FloorType;
  operationStatus?: OperationStatus;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: BackendFloor[];
  total: number;
} | null;

export type SvcRespData = {
  data: FloorJSON[];
  total: number;
};

export type ApiR = ListResponse<FloorJSON[] | null>;
