/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-6
 *
 * @packageDocumentation
 */
import { Floor } from '@manyun/resource-hub.model.floor';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-floors.type';

const endpoint = '/dccm/floor/page/query';

/**
 * @see [查询楼层列表](http://172.16.0.17:13000/project/252/interface/api/24096)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => Floor.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
