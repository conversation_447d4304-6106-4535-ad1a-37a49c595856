/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './sync-auth-groups.type';

const endpoint = '/dccm/entrance/auth_group/aysc';

/**
 * @see [同步楼栋权限组](http://172.16.0.17:13000/project/234/interface/api/23781)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return { error, ...rest, data: null };
    }

    return { error, data: data, ...rest };
  };
}
