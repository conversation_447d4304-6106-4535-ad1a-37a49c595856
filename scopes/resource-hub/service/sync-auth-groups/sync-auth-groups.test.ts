/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { syncAuthGroups as webService } from './sync-auth-groups.browser';
import { syncAuthGroups as nodeService } from './sync-auth-groups.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ blockGuid: 'EC06.A' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ blockGuid: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ blockGuid: 'EC06.A' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ blockGuid: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
