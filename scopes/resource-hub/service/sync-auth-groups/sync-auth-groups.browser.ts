/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './sync-auth-groups';
import type { ApiQ, SvcRespData } from './sync-auth-groups.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function syncAuthGroups(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
