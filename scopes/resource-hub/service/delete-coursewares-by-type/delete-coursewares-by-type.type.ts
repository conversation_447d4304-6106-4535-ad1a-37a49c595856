/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  targetId: string;
  courseFileIds: number[];
};

export type ApiQ = {
  targetId: string;
  /**
   * 现在targetType的值只有 'DEVICE_TYPE'
   */
  targetType: string;
  courseFileIds: number[];
};

export type SvcRespData = boolean;

export type ApiR = WriteResponse;
