/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './delete-coursewares-by-type.type';

const endpoint = '/dcexam/course/file/target/del';

/**
 * @see [标签取消关联课件](YAPI Link)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    targetId,
    courseFileIds,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { targetId, courseFileIds, targetType: 'DEVICE_TYPE' };

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
