/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-coursewares-by-type';
import type { SvcQuery, SvcRespData } from './delete-coursewares-by-type.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function deleteCoursewaresByType(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
