/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-influence';
import type { ApiQ, SvcRespData } from './fetch-influence.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchInfluence(params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
