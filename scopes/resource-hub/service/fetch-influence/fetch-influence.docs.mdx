---
description: 'A fetchInfluence HTTP API service.'
labels: ['service', 'http', fetch-influence]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchInfluence } from '@resource-hub/service.fetch-influence';

const { error, data } = await fetchInfluence('success');
const { error, data } = await fetchInfluence('error');
```

### Node

```ts
import { fetchInfluence } from '@resource-hub/service.fetch-influence/dist/index.node';

const { data } = await fetchInfluence('success');

try {
  const { data } = await fetchInfluence('error');
} catch(error) {
  // ...
}
```
