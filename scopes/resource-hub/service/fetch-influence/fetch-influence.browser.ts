/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-influence';
import type { SvcQuery, SvcRespData } from './fetch-influence.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchInfluence(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
