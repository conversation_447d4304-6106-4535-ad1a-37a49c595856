/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createFloor as webService } from './create-floor.browser';
import { createFloor as nodeService } from './create-floor.node';
import type { ApiQ } from './create-floor.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  name: '2层',
  floorType: 'BASEMENT_FLOOR',
  tag: '1',
  operationStatus: 'ON',
  idcTag: 'EC06',
  blockGuid: 'EC06.A',
};

const errorParams: ApiQ = {
  ...params,
  idcTag: '',
  blockGuid: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
