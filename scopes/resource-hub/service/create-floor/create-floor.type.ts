/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-6
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { FloorType, OperationStatus } from '@manyun/resource-hub.model.floor';

export type SvcQuery = {
  name: string;
  floorType: FloorType;
  tag: string;
  operationStatus: OperationStatus;
  idcTag: string;
  blockGuid: string;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
