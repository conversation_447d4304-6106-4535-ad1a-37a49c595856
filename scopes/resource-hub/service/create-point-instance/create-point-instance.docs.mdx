---
description: 'A createPointInstance HTTP API service.'
labels: ['service', 'http']
---

## 概述

点位实例创建

## 使用

### Browser

```ts
import { createPointInstance } from '@resource-hub/service.create-point-instance';

const { error, data } = await createPointInstance({
  targetGuid: 'EC06.A.A1-80',
  pointId: 1556000,
  expressInfos: [
    {
      children: null,
      id: 'JUzLIDNKcT',
      name: '【CFE2-1】药剂浓度',
      type: 'point',
      value: 'K_770769_1001000_20216',
    },
  ],
});
```
