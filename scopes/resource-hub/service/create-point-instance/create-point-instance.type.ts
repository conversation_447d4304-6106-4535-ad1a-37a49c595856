/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type RequestRespData = SvcRespData;

export type ExpressInfo = {
  children: ExpressInfo[] | null;
  id: string;
  name: string;
  type: string;
  value: string;
};
export type ApiQ = {
  targetGuid: string; //所属对象guid
  deviceId?: number; //设备id
  pointId: number;
  expressInfos: ExpressInfo[]; //表达式
};

export type SvcRespData = boolean | null;

export type ApiR = WriteResponse;
