/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-point-instance';
import type { SvcQuery, SvcRespData } from './create-point-instance.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function createPointInstance(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
