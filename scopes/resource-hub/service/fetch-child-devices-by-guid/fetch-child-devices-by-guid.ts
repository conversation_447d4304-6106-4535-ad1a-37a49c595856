/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import { Device } from '@manyun/resource-hub.model.device';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, ApiR, SvcRespData } from './fetch-child-devices-by-guid.type';

export const endpoint = '/dccm/device/query/by/parent/guid';

/**
 * 根据父设备查询子设备（http://172.16.0.17:13000/project/140/interface/api/14392）
 * @param variant
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      spaceGuid: variant.spaceGuid,
      parentGuid: variant.parentGuid,
    };

    const {
      error,
      data: { data, total },

      ...rest
    } = await request.tryPost<ApiR, ApiQ>(endpoint, params);

    if (error || data === null || !data) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.map(Device.fromApiObject), total }, ...rest };
  };
}
