/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendDevice, Device } from '@manyun/resource-hub.model.device';

export type SvcRespData = {
  data: Device[];
  total: number;
};

export type RequestRespData = {
  data: BackendDevice[] | null;
  total: number;
};

export type ApiQ = {
  parentGuid: string;
  spaceGuid: string;
};

export type ApiR = ListResponse<RequestRespData['data']>;
