---
description: 'A fetchChildDevicesByGuid HTTP API service.'
labels: ['service', 'http', fetch-child-devices-by-guid]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchChildDevicesByGuid } from '@resource-hub/service.fetch-child-devices-by-guid';

const { error, data } = await fetchChildDevicesByGuid('success');
const { error, data } = await fetchChildDevicesByGuid('error');
```

### Node

```ts
import { fetchChildDevicesByGuid } from '@resource-hub/service.fetch-child-devices-by-guid/dist/index.node';

const { data } = await fetchChildDevicesByGuid('success');

try {
  const { data } = await fetchChildDevicesByGuid('error');
} catch(error) {
  // ...
}
```
