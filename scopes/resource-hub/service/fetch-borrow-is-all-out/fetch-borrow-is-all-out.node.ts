/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-borrow-is-all-out';
import type { SvcQuery, SvcRespData } from './fetch-borrow-is-all-out.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchBorrowIsAllOut(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
