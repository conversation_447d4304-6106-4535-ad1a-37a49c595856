/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendMetadata, Metadata, MetaType } from '@manyun/resource-hub.model.metadata';

export type BaseServiceQ = {
  name: string;
  type: MetaType;
};

export type TreeServiceQ = BaseServiceQ & {
  parentCode: string;
  parentType: MetaType;
};

export type ServiceQ = BaseServiceQ | TreeServiceQ;

export type ServiceRD = Metadata | null;

export type RequestRD = BackendMetadata | null;

export type BaseApiQ = {
  metaName: string;
  metaType: MetaType;
};

export type TreeApiQ = BaseApiQ & {
  parentMetaCode: string;
  parentMetaType: MetaType;
};

export type ApiQ = BaseApiQ | TreeApiQ;

export type ApiR = Response<RequestRD>;
