/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import { Metadata } from '@manyun/resource-hub.model.metadata';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './create-meta-data';
import type {
  ApiQ,
  BaseApiQ,
  RequestRD,
  ServiceQ,
  ServiceRD,
  TreeApiQ,
} from './create-meta-data.type';

/**
 * 添加元数据
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/pkinbk/zs0t7v)
 *
 * @param query Service query object
 * @returns
 */
export async function createMetaData(query: ServiceQ): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: BaseApiQ = {
    metaName: query.name,
    metaType: query.type,
  };

  if ('parentCode' in query) {
    (apiQ as TreeApiQ).parentMetaCode = query.parentCode;
    (apiQ as TreeApiQ).parentMetaType = query.parentType;
  }

  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data === null) {
    return { error, ...rest, data: null };
  }

  return { error, data: Metadata.fromApiObject(data), ...rest };
}
