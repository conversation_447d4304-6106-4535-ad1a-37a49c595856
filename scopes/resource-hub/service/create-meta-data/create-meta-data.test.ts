/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { getMock } from '@manyun/service.request';

import { createMetaData as webService } from './create-meta-data.browser';
import { registerWebMocks } from './create-meta-data.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('create notify meta data', async () => {
  const payload = { type: MetaType.NOTIFY_EVENT_TYPE, name: '12345' };
  const { data, error } = await webService(payload);
  expect(data!.name).toBe(payload.name);
  expect(data!.type).toBe(payload.type);
  expect(error).toBe(undefined);
});

test('create event meta data', async () => {
  const payload = {
    type: MetaType.EVENT_SECOND_CATEGORY,
    name: '2345',
    parentType: MetaType.EVENT_TOP_CATEGORY,
    parentCode: '0',
  };
  const { data, error } = await webService(payload);
  expect(data!.name).toBe(payload.name);
  expect(data!.type).toBe(payload.type);
  expect(data!.parentCode).toBe(payload.parentCode);
  expect(error).toBe(undefined);
});
