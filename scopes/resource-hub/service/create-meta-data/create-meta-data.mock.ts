/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import type { getMock } from '@manyun/service.request';

import { endpoint } from './create-meta-data';
import type { ApiQ, ApiR, TreeApiQ } from './create-meta-data.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    const { metaName, metaType, ...rest } = JSON.parse(config.data) as ApiQ;
    const resp: ApiR = {
      success: true,
      errCode: null,
      errMessage: null,
      data: {
        id: 1,
        metaType,
        metaCode: 'fake-meta-code',
        metaName,
        metaStyle: null,
        numbered: false,
        parentCode: (rest as TreeApiQ).parentMetaCode ?? null,
        createTime: Date.now(),
        updateTime: Date.now(),
        lastOperator: 0,
        lastOperatorName: 'admin',
        isDeleted: false,
        roomType: '1010',
        roomTypeName: 'rooname',
      },
    };

    return [200, resp];
  });
}
