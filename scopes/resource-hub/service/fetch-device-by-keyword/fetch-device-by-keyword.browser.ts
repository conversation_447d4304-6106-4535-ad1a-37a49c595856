/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-by-keyword';
import type { SvcQuery, SvcRespData } from './fetch-device-by-keyword.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceByKeyword(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
