---
description: 'A fetchDeviceByKeyword HTTP API service.'
labels: ['service', 'http', fetch-device-by-keyword]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceByKeyword } from '@resource-hub/service.fetch-device-by-keyword';

const { error, data } = await fetchDeviceByKeyword({ content: '1', idcTag: 'EC01', blockTag: '1' });
const { error, data } = await fetchDeviceByKeyword({ content: '2', idcTag: 'EC01', blockTag: '1' });
```
