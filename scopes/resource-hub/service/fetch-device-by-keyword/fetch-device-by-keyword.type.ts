/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceInfo = {
  deviceType: string;
  guid: string;
  name: string;
  productModel: string;
  spaceGuid: SpaceGuid;
  vendor: string;
};
export type SpaceGuid = {
  blockGuid: string;
  blockTag: string;
  idcGuid: string;
  roomGuid: string;
  roomTag: string;
};

export type SvcRespData = {
  data: DeviceInfo[];
  total: number;
};

export type RequestRespData = {
  data: DeviceInfo[] | null;
  total: number;
} | null;

export type ApiQ = {
  idcTag: string;
  blockTag: string;
  content: string;
  deviceGuidList?: string[];
  assertStatusList?: string[];
  operationStatus?: string;
  pageNum?: number;
  pageSize?: number;
};

export type ApiR = ListResponse<DeviceInfo[] | null>;
