/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import type { BackendInfluence, Influence } from '@manyun/monitoring.model.influence';

export type SvcRespData = Influence | null;

export type RequestRespData = BackendInfluence | null;

export type ApiQ = {
  bizScenario?: {
    uniqueIdentity?: string;
  };
  deviceGuidList?: string[];
  idcTag?: string;
  blockTag?: string;
  tenantId?: string;
  targetId?: string; // 事件/变更/告警id
  targetType?: string; // 事件/告警 类型：EVENT ALARM
};
