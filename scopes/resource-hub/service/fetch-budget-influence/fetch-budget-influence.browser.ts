/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-budget-influence';
import type { ApiQ, SvcRespData } from './fetch-budget-influence.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchBudgetInfluence(params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
