---
description: 'A fetchBudgetInfluence HTTP API service.'
labels: ['service', 'http', fetch-budget-influence]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchBudgetInfluence } from '@resource-hub/service.fetch-budget-influence';

const { error, data } = await fetchBudgetInfluence('success');
const { error, data } = await fetchBudgetInfluence('error');
```

### Node

```ts
import { fetchBudgetInfluence } from '@resource-hub/service.fetch-budget-influence/dist/index.node';

const { data } = await fetchBudgetInfluence('success');

try {
  const { data } = await fetchBudgetInfluence('error');
} catch(error) {
  // ...
}
```
