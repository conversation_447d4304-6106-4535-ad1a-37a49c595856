/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-17
 *
 * @packageDocumentation
 */
import { BorrowReturn } from '@manyun/resource-hub.model.borrow-return';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-borrows.type';

const endpoint = '/dccm/borrow/list';

/**
 * @see [借用单列表](https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#MIOeg)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => BorrowReturn.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
