/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendBorrowReturn,
  BorrowReturnJSON,
  BorrowStatus,
} from '@manyun/resource-hub.model.borrow-return';

export type SvcQuery = {
  pageNum: number;
  pageSize: number;
  borrowNo?: string;
  assetNo?: string; //资产ID
  title?: string;
  idcTag?: string;
  blockGuid?: string;
  borrower?: string;
  borrowerName?: string;
  borrowStatusList?: BorrowStatus[];
  creatorId?: number;
  borrowEndTime?: number;
  borrowStartTime?: number;
  createEndTime?: number;
  createStartTime?: number;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: BackendBorrowReturn[];
  total: number;
} | null;

export type SvcRespData = {
  data: BorrowReturnJSON[];
  total: number;
};

export type ApiR = ListResponse<BorrowReturnJSON[] | null>;
