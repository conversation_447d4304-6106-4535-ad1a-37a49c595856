/**
 * <AUTHOR>  <<EMAIL>>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-by-guid';
import type { ApiQ, SvcRespData } from './fetch-device-by-guid.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceByGuid(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
