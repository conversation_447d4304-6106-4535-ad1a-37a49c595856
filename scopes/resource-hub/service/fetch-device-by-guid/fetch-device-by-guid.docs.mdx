---
description: 'A fetchDeviceByGuid HTTP API service.'
labels: ['service', 'http', fetch-device-by-guid]
---

## 概述

根据 guid 查询设备详情

## 使用

### Browser

```ts
import { fetchDeviceByGuid } from '@resource-hub/service.fetch-device-by-guid';

const { error, data } = await fetchDeviceByGuid('success');
const { error, data } = await fetchDeviceByGuid('error');
```

### Node

```ts
import { fetchDeviceByGuid } from '@resource-hub/service.fetch-device-by-guid/dist/index.node';

const { data } = await fetchDeviceByGuid('success');

try {
  const { data } = await fetchDeviceByGuid('error');
} catch (error) {
  // ...
}
```
