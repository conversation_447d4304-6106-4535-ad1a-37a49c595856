/**
 * <AUTHOR>  <<EMAIL>>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-by-guid';
import type { SvcQuery, SvcRespData } from './fetch-device-by-guid.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceByGuid(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
