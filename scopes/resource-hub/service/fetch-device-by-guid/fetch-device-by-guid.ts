/**
 * <AUTHOR>  <<EMAIL>>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { Device } from '@manyun/resource-hub.model.device';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-device-by-guid.type';

const endpoint = '/dccm/device/detail';

/**
 * @see [Doc](https://manyun.yuque.com/ewe5b3/sbs6q1/kphm12)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = variant;

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: Device.fromApiObject(data), ...rest };
  };
}
