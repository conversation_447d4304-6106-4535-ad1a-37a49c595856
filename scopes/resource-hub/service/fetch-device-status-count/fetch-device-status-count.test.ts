/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceStatusCount as webService } from './fetch-device-status-count.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    deviceGuid: '1090103',
    taskTypeList: ['INSPECTION'],
  });

  expect(data.data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090101', taskTypeList: [] });

  expect(error?.code).toBe('error');
  expect(data.data).toStrictEqual(null);
});
