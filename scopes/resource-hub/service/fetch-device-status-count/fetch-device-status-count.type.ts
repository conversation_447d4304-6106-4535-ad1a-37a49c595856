/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { TaskType } from '@manyun/resource-hub.service.fetch-device-task-list';

export type SvcQuery = ApiQ;

export type DeviceStatusCountTypeModel = {
  /** 工单状态code */
  status: string;
  /** 工单状态名称 */
  name: string;
  /** 工单状态数量 */
  value: number;
};

export type SvcRespData = {
  data: DeviceStatusCountTypeModel[] | null;
};

export type RequestRespData = {
  data: DeviceStatusCountTypeModel[] | null;
} | null;

export type ApiQ = {
  deviceGuid: string;
  taskTypeList: TaskType[];
};

export type ApiR = ListResponse<DeviceStatusCountTypeModel[] | null>;
