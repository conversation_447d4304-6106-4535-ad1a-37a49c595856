/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-device-status-count.type';

const endpoint = '/taskcenter/device/status/count';

/**
 * @see [统计设备下工单状态数量](http://172.16.0.17:13000/project/144/interface/api/18184)
 * 统计设备下工单状态数量
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: null,
        },
      };
    }

    return { error, data: { data: data.data }, ...rest };
  };
}
