---
description: 'A fetchDeviceStatusCount HTTP API service.'
labels: ['service', 'http', fetch-device-status-count]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceStatusCount } from '@resource-hub/service.fetch-device-status-count';

const { error, data } = await fetchDeviceStatusCount({ deviceId: '10101' });
```

### Node

```ts
import { fetchDeviceStatusCount } from '@resource-hub/service.fetch-device-status-count/dist/index.node';

const { data } = await fetchDeviceStatusCount({ deviceId: '10101' });

try {
  const { data } = await fetchDeviceStatusCount({ deviceId: '1' });
} catch (error) {
  // ...
}
```
