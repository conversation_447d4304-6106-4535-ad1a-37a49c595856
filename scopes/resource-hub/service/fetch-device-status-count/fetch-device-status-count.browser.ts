/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-status-count';
import type { SvcQuery, SvcRespData } from './fetch-device-status-count.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceStatusCount(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
