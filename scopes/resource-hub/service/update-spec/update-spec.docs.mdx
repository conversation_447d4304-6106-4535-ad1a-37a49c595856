---
description: 'A updateSpec HTTP API service.'
labels: ['service', 'http']
---

## 概述

TODO

## 使用

### Browser

```ts
import { updateSpec } from '@resource-hub/service.update-spec';

const { error, data } = await updateSpec({
  id: 10101,
  required: true,
  specName: '测试规格',
});
```

### Node

```ts
import { updateSpec } from '@resource-hub/service.update-spec/dist/index.node';

const { data } = await updateSpec({
  id: 10101,
  required: true,
  specName: '测试规格',
});
```
