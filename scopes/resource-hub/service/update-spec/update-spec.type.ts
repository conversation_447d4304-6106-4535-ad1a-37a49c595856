/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = boolean | null;

export type RequestRespData = boolean | null;

export type ApiQ = {
  id: number;
  // 是否必填
  required: boolean;
  // 规格名称
  specName: string;
  // 备注
  description?: string;
  // 规格单位
  unit?: string;
  // 选择项多个 逗号隔开
  options?: string;
  // 选择类型「单选或者多选」
  optionType: boolean;
};

export type ApiR = WriteResponse;
