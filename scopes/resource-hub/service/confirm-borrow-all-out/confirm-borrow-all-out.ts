/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './confirm-borrow-all-out.type';

const endpoint = '/dccm/borrow/all/stock/out/confirm';

/**
 * @see [出库完成确认](https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#QoDtZ)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
