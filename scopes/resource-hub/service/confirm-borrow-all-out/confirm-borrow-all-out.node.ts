/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './confirm-borrow-all-out';
import type { SvcQuery, SvcRespData } from './confirm-borrow-all-out.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function confirmBorrowAllOut(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
