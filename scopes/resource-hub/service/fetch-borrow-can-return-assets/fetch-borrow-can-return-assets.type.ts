/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-20
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AssetType = 'ON_LINE' | 'OFF_LINE';
export type BorrowAsset = {
  id: string;
  assetType: AssetType;
  borrowNo: string;
  borrowNum: number;
  returnNum: number;
  roomTag: string;
  roomGuid: string;
  assetNo: string;
  numbered: boolean; //true 设备 false 耗材
  deviceType: string;
  productModel: string;
  vendor: string;
  topCategory: string;
  secondCategory: string;
  borrowApplyId: number;
  borrowType: string;
  gmtCreate: number;
  gmtModified: number;
};

export type SvcQuery = {
  borrowNo: string;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: BorrowAsset[];
  total: number;
} | null;

export type SvcRespData = {
  data: BorrowAsset[];
  total: number;
};

export type ApiR = ListResponse<BorrowAsset[] | null>;
