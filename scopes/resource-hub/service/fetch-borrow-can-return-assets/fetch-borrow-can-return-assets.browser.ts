/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-20
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-borrow-can-return-assets';
import type { SvcQuery, SvcRespData } from './fetch-borrow-can-return-assets.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchBorrowCanReturnAssets(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
