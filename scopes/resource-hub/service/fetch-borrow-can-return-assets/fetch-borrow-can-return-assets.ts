/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-20
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-borrow-can-return-assets.type';

const endpoint = '/dccm/borrow/asset/can/return/list';

/**
 * @see [可归还资产查询](http://172.16.0.17:13000/project/306/interface/api/24708)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data,
        total: data.total,
      },
      ...rest,
    };
  };
}
