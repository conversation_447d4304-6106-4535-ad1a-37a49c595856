---
description: 'A fetchRoomsByBlock HTTP API service.'
labels: ['service', 'http', fetch-rooms-by-block]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchRoomsByBlock } from '@resource-hub/service.fetch-rooms-by-block';

const { error, data } = await fetchRoomsByBlock('success');
const { error, data } = await fetchRoomsByBlock('error');
```

### Node

```ts
import { fetchRoomsByBlock } from '@resource-hub/service.fetch-rooms-by-block/dist/index.node';

const { data } = await fetchRoomsByBlock('success');

try {
  const { data } = await fetchRoomsByBlock('error');
} catch(error) {
  // ...
}
```
