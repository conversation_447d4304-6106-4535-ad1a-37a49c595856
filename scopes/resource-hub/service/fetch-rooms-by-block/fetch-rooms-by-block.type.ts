/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import type { BackendRoom, Room } from '@manyun/resource-hub.model.room';

export type SvcRespData = {
  data: Room[];
  total: number;
};

export type RequestRespData = {
  data: BackendRoom[] | null;
  total: number;
};

export type ApiQ = {
  bizScenario?: {
    uniqueIdentity?: string;
  };
  blockTags?: string[];
  idcTag?: string;
  tenantId?: string;
} | null;
