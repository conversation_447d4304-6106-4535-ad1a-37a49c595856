/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-rooms-by-block';
import type { ApiQ, SvcRespData } from './fetch-rooms-by-block.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchRoomsByBlock(params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
