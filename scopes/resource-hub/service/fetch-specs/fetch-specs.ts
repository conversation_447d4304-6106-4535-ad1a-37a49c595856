/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import { Spec } from '@manyun/resource-hub.model.spec';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-specs.type';

const endpoint = '/dccm/spec/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/140/interface/api/15920)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    /** @cf task:  , fetchSpec返回值替换为specJSON,影响面大需要任务跟进 */
    const mergeSpecData = data.data.map(item => {
      const specJson = Spec.fromApiObject(item).toJSON();
      return {
        ...specJson,
        ...item,
      };
    });

    return {
      error,
      data: {
        data: mergeSpecData,
        total: data.total,
      },
      ...rest,
    };
  };
}
