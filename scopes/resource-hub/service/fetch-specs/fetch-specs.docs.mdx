---
description: 'A fetchSpecs HTTP API service.'
labels: ['service', 'http', fetch-specs]
---

## 概述

获取设备参数

## 使用

### Browser

```ts
import { fetchSpecs } from '@resource-hub/service.fetch-specs';

const { error, data } = await fetchSpecs({ deviceType: '100000' });
```

### Node

```ts
import { fetchSpecs } from '@resource-hub/service.fetch-specs/dist/index.node';

const { data } = await fetchSpecs({ deviceType: '100000' });
```
