/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';

export type Spec = BackendSpec;

export type SvcRespData = {
  data: (SpecJSON & Spec)[];
  total: number;
};

export type RequestRespData = {
  data: BackendSpec[] | null;
  total: number;
} | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  deviceType: string;
  modelCode?: string;
  /** 厂商 */
  vendor?: string;
  modelId?: string /* 型号id或空间guid */;
};
export type ApiR = ListResponse<SpecJSON[] | null>;
