/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-specs';
import type { ApiQ, SvcRespData } from './fetch-specs.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchSpecs(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
