/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import { Idc } from '@manyun/resource-hub.model.idc';
import type { BackendIdc } from '@manyun/resource-hub.model.idc';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcQuery, SvcRespData } from './fetch-idc.type.js';

const endpoint = '/dccm/idc/detail';

/**
 * @see [查询机房详情](http://172.16.0.17:13000/project/140/interface/api/18008)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<BackendIdc | null, SvcQuery>(
      endpoint,
      variant
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data: Idc.fromApiObject(data), ...rest };
  };
}
