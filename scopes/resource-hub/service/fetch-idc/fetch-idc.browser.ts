/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-idc.js';
import type { SvcQuery, SvcRespData } from './fetch-idc.type.js';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchIdc(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
