/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-facility-count';
import type { ApiQ, SvcRespData } from './fetch-facility-count.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchFacilityCount(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
