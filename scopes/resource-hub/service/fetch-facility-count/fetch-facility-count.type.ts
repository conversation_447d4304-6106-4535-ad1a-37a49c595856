/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
export type FacilityCountModel = {
  customerMap: Record<string, number>;
  customerTotal: number;
  deviceTotal: number;
  gridTotal: number;
};

export type SvcRespData = FacilityCountModel | null;

export type RequestRespData = FacilityCountModel | null;

export type ApiQ = {
  idcTag: string;
  blockTag: string;
  targetId?: number;
  targetType?: string;
};
