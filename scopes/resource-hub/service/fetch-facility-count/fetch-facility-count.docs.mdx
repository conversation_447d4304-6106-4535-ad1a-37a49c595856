---
description: 'A fetchFacilityCount HTTP API service.'
labels: ['service', 'http', fetch-facility-count]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchFacilityCount } from '@resource-hub/service.fetch-facility-count';

const { error, data } = await fetchFacilityCount('success');
const { error, data } = await fetchFacilityCount('error');
```

### Node

```ts
import { fetchFacilityCount } from '@resource-hub/service.fetch-facility-count/dist/index.node';

const { data } = await fetchFacilityCount('success');

try {
  const { data } = await fetchFacilityCount('error');
} catch(error) {
  // ...
}
```
