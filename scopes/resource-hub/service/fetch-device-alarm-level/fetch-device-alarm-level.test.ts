/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceAlarmLevel as webService } from './fetch-device-alarm-level.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090103' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090101' });

  expect(error?.code).toBe('error');
  expect(data.data).toStrictEqual([]);
});
