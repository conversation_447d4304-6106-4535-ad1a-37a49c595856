/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceAlartmLevelDataModel = {
  /** 状告警等级状态 */
  status: string;
  /** 告警等级名称*/
  name: string;
  /** 状告警等级值 */
  value: string;
};

export type SvcRespData = {
  data: DeviceAlartmLevelDataModel[];
  total: number;
};

export type RequestRespData = {
  data: DeviceAlartmLevelDataModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  deviceGuid: string;
};

export type ApiR = ListResponse<DeviceAlartmLevelDataModel[] | null>;
