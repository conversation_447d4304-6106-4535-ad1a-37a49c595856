---
description: 'A fetchDeviceAlarmLevel HTTP API service.'
labels: ['service', 'http', fetch-device-alarm-level]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceAlarmLevel } from '@resource-hub/service.fetch-device-alarm-level';

const { error, data } = await fetchDeviceAlarmLevel({ deviceGuid: '10101' });
```

### Node

```ts
import { fetchDeviceAlarmLevel } from '@resource-hub/service.fetch-device-alarm-level/dist/index.node';

const { data } = await fetchDeviceAlarmLevel({ deviceGuid: '10101' });

try {
  const { data } = await fetchDeviceAlarmLevel({ deviceGuid: '' });
} catch (error) {
  // ...
}
```
