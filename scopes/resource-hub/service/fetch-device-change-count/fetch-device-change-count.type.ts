/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceChangeCountModel = {
  changeStatus: string;
  num: number;
};

export enum DeviceChangeType {
  /** 草稿 */
  'DRAFT' = 'DRAFT',
  /** 审批中 */
  'APPROVING' = 'APPROVING',
  /** 待变更 */
  'WAITING_CHANGE' = 'WAITING_CHANGE',
  /** 变更中 */
  'CHANGING' = 'CHANGING',
  /** 总结 */
  'IN_SUMMARY' = 'IN_SUMMARY',
  /** 待关闭 */
  'WAITING_CLOSE' = 'WAITING_CLOSE',
  /**  结束*/
  'FINISH' = 'FINISH',
}

export type SvcRespData = {
  data: DeviceChangeCountModel[] | null;
  total: number;
};

export type RequestRespData = {
  data: DeviceChangeCountModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  blockTag?: string;
  idcTag?: string;
  deviceGuid?: string;
  statusList?: string[];
};

export type ApiR = ListResponse<DeviceChangeCountModel[] | null>;
