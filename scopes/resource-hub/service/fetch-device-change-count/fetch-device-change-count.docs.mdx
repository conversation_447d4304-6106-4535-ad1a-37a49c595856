---
description: 'A fetchDeviceChangeCount HTTP API service.'
labels: ['service', 'http', fetch-device-change-count]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceChangeCount } from '@resource-hub/service.fetch-device-change-count';

const { error, data } = await fetchDeviceChangeCount({});
const { error, data } = await fetchDeviceChangeCount({});
```

### Node

```ts
import { fetchDeviceChangeCount } from '@resource-hub/service.fetch-device-change-count/dist/index.node';

const { data } = await fetchDeviceChangeCount({});

try {
  const { data } = await fetchDeviceChangeCount({});
} catch (error) {
  // ...
}
```
