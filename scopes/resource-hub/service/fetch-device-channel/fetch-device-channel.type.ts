/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-30
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = { deviceGuid: string };

export type DeviceChannel = BackEndDeviceChannel;
export type BackEndDeviceChannel = {
  connectStatus: string /*通道状态 */;
  id: number;
  ip: string /*通道ip */;
  name: string /*通道名称 */;
  nodeGuid: string /*节点guid */;
  nodeId: number /*节点id */;
  nodeName: string /*节点名称 */;
  protocol: string /*协议 */;
};

export type SvcRespData = {
  data: DeviceChannel[];
  total: number;
};

export type RequestRespData = {
  data: BackEndDeviceChannel[] | null;
  total: number;
} | null;

export type ApiQ = {
  deviceGuid: string;
};

export type ApiR = ListResponse<BackEndDeviceChannel[] | null>;
