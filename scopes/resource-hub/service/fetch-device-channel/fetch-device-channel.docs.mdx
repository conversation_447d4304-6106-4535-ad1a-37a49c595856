---
description: 'A fetchDeviceChannel HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询设备通道信息

## 使用

### Browser

```ts
import { fetchDeviceChannel } from '@resource-hub/service.fetch-device-channel';

const { error, data } = await fetchDeviceChannel({ deviceGuid: '10101' });
const { error } = await fetchDeviceChannel({ deviceGuid: null });
```

### Node

```ts
import { fetchDeviceChannel } from '@resource-hub/service.fetch-device-channel/dist/index.node';

const { error, data } = await fetchDeviceChannel({ deviceGuid: '10101' });
const { error } = await fetchDeviceChannel({ deviceGuid: null });
```
