---
description: 'A fetchDeviceInventoryTime HTTP API service.'
labels: ['service', 'http', fetch-device-inventory-time]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceInventoryTime } from '@resource-hub/service.fetch-device-inventory-time';

const { error, data } = await fetchDeviceInventoryTime({ deviceGuid: '10101' });
```

### Node

```ts
import { fetchDeviceInventoryTime } from '@resource-hub/service.fetch-device-inventory-time/dist/index.node';

const { data } = await fetchDeviceInventoryTime({deviceGuid: '10101'});

try {
  const { data } = await fetchDeviceInventoryTime({deviceGuid: });
} catch(error) {
  // ...
}
```
