/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-device-inventory-time.type';

const endpoint = '/dccm/pg/inventory/time/query';

/**
 * @see [查询备品库存可用时效查询](http://172.16.0.17:13000/project/152/interface/api/18256)
 * 查询备品库存可用时效查询
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data, ...rest };
  };
}
