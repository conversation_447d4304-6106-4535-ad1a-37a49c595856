/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-30
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-inventory-time';
import type { SvcQuery, SvcRespData } from './fetch-device-inventory-time.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceInventoryTime(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
