/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceInventoryTime as webService } from './fetch-device-inventory-time.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({ deviceGuid: '1090103' });

  expect(data).toHaveProperty('data');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ deviceGuid: '1090101' });

  expect(error?.code).toBe('error');
  expect(data).toStrictEqual(null);
});
