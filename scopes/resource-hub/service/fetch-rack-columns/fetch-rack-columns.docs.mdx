---
description: '查询机列列表.'
labels: ['service', 'http']
---

## 使用

### Browser

```ts
import { fetchRackColumns } from '@resource-hub/service.fetch-rack-columns';

const { error, data } = await fetchRackColumns({
  idcTag: 'EC06',
});
```

### Node

```ts
import { fetchRackColumns } from '@resource-hub/service.fetch-rack-columns/dist/index.node';

const { error, data } = await fetchRackColumns({
  idcTag: 'EC06',
});
```
