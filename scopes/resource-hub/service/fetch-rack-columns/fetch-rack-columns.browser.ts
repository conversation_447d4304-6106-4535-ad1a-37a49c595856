/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-23
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-rack-columns';
import type { ApiQ, SvcRespData } from './fetch-rack-columns.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchRackColumns(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
