/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-23
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type RackColumns = {
  blockTag: string;
  columnTag: string;
  guid: string;
  idcTag: string;
  roomTag: string;
  tag: string;
  ratedPower: number;
  signedPower: number;
};

export type SvcRespData = {
  data: RackColumns[];
  total: number;
};

export type RequestRespData = {
  data: RackColumns[] | null;
  total: number;
} | null;

export type ApiQ = {
  /** 如：'A' */
  blockTag?: string;
  /** 如： 'EC06' */
  idcTag: string;
  /** 如： 'A-1' */
  roomTag?: string;
};

export type ApiR = ListResponse<RackColumns[] | null>;
