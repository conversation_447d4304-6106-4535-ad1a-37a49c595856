/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  name: string;
  blockType: string;
  constructTime?: number;
  operationStatus: string;
  operationTime?: number;
  period: string;
  principalId: number;
  principalName: string;
  coordinate?: string;
  area?: number;

  id?: number;
  operatorNotes?: string;
  tag?: string /* 楼号 */;
  idcTag?: string;
};

export type AddApiQ = {
  blockType: string;
  constructTime?: number;
  operationStatus: string;
  operationTime?: number;
  period: string;
  principalId: number;
  principalName: string;
  coordinate?: string;
  area?: number;

  tag: string /* 楼号 */;
  idcTag: string;
};

export type UpdateApiQ = {
  blockType: string;
  constructTime?: number;
  operationStatus: string;
  operationTime?: number;
  period: string;
  principalId: number;
  principalName: string;
  coordinate?: string;
  area?: number;

  id: number;
  operatorNotes: string;
};

export type SvcRespData = boolean;

export type ApiR = WriteResponse;
