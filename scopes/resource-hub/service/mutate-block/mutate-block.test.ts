/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateBlock as webService } from './mutate-block.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve update success response', async () => {
  const { data } = await webService({
    blockType: 'IDC_BLOCK',
    constructTime: 1652803200000,
    id: 1,
    idcTag: 'EC03',
    operationStatus: 'OFF',
    operationTime: 1652889600000,
    operatorNotes: 'test',
    period: '一期',
    tag: 'G',
    principalId: 1,
    principalName: 'admin',
  });

  expect(data).toBe(true);
});

test('[web] should resolve update error response', async () => {
  const { error, data } = await webService({
    blockType: 'IDC_BLOCK',
    constructTime: 1652803200000,
    id: 2,
    idcTag: 'EC03',
    operationStatus: 'OFF',
    operationTime: 1652889600000,
    operatorNotes: 'test',
    period: '一期',
    tag: 'G',
    principalId: 1,
    principalName: 'admin',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
