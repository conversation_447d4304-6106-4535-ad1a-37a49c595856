/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './mutate-block';
import type { SvcQuery, SvcRespData } from './mutate-block.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function mutateBlock(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
