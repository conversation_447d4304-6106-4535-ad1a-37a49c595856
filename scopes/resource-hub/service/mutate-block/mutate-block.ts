/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { AddApiQ, SvcQuery, SvcRespData, UpdateApiQ } from './mutate-block.type';

const addEndpoint = '/dccm/block/add';
const updateEndpoint = '/dccm/block/update';

/**
 * @see [添加楼栋](http://172.16.0.17:13000/project/140/interface/api/13864)
 * @see [修改楼栋](http://172.16.0.17:13000/project/140/interface/api/13896)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    id,
    tag,
    idcTag,
    operatorNotes,
    ...rest
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    if (id) {
      const params: UpdateApiQ = { id, operatorNotes: operatorNotes!, ...rest };
      return await request.tryPost<SvcRespData, UpdateApiQ>(updateEndpoint, params);
    } else {
      const params: AddApiQ = { tag: tag!, idcTag: idcTag!, ...rest };

      return await request.tryPost<SvcRespData, AddApiQ>(addEndpoint, params);
    }
  };
}
