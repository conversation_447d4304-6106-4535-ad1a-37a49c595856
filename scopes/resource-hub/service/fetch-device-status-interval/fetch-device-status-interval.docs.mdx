---
description: 'A fetchDeviceStatusInterval HTTP API service.'
labels: ['service', 'http', fetch-device-status-interval]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceStatusInterval } from '@resource-hub/service.fetch-device-status-interval';

const { error, data } = await fetchDeviceStatusInterval();
const { error, data } = await fetchDeviceStatusInterval();
```

### Node

```ts
import { fetchDeviceStatusInterval } from '@resource-hub/service.fetch-device-status-interval/dist/index.node';

const { data } = await fetchDeviceStatusInterval();

try {
  const { data } = await fetchDeviceStatusInterval();
} catch (error) {
  // ...
}
```
