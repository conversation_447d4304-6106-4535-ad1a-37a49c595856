/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-27
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-status-interval';
import type { SvcQuery, SvcRespData } from './fetch-device-status-interval.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchDeviceStatusInterval(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
