/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-27
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceStatusInterval as webService } from './fetch-device-status-interval.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    deviceGuid: '1090103',
    startTime: 1656345600000,
    endTime: 1656382254597,
  });

  expect(data).toHaveProperty('stateIntervals');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    deviceGuid: '1090101',
    startTime: 1656345600000,
    endTime: 1656382254597,
  });

  expect(error?.code).toBe('error');
  expect(data).toStrictEqual(null);
});
