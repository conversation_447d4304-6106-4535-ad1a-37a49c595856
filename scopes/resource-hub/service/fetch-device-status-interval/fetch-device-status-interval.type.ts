/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-27
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DataModel = {
  /** 下线：OFF,告警：ALARM_ERROR,预警：ALARM_WARN,正常：NORMAL */
  status: TimeStatusInfo;
  stateIntervals: stateIntervalsInfo[];
};

export type stateIntervalsInfo = {
  /** 时间段状态 */
  startTime: string | number;
  endTime: string | number;
  status: TimeStatusType;
};

export type SvcRespData = DataModel | null;

export type RequestRespData = DataModel | null;

export type ApiQ = {
  /** 设备id */
  deviceGuid: string;
  /** 当日凌晨时间戳 */
  startTime: number;
  /** 当前时间戳 */
  endTime: number;
};

export type ApiR = ListResponse<DataModel | null>;

// 下线：OFF,告警：ALARM_ERROR,预警：ALARM_WARN,正常：NORMAL
export enum TimeStatusType {
  /** 正常 */
  'NORMAL' = 'NORMAL',
  /** 告警 */
  'ALARM_ERROR' = 'ALARM_ERROR',
  /** 预警 */
  'ALARM_WARN' = 'ALARM_WARN',
  /** 下线 */
  'OFF' = 'OFF',
}

export type TimeStatusInfo = keyof typeof TimeStatusType | undefined;

export const TimeStatusColorMap: Map<TimeStatusInfo, string> = new Map([
  [TimeStatusType.NORMAL, 'success'],
  [TimeStatusType.ALARM_WARN, 'warning'],
  [TimeStatusType.OFF, 'default'],
  [TimeStatusType.ALARM_ERROR, 'error'],
]);

export const TimeStatusMap: Map<TimeStatusInfo, string> = new Map([
  [TimeStatusType.NORMAL, '正常'],
  [TimeStatusType.ALARM_ERROR, '告警'],
  [TimeStatusType.ALARM_WARN, '预警'],
  [TimeStatusType.OFF, '下线'],
]);
