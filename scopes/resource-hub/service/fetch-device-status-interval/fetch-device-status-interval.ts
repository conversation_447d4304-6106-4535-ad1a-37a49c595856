/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-27
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-device-status-interval.type';

const endpoint = '/taskcenter/state/interval/query';

/**
 * @see [查询设备状态区间](http://172.16.0.17:13000/project/144/interface/api/18136)
 * 查询设备状态区间
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data,
      ...rest,
    };
  };
}
