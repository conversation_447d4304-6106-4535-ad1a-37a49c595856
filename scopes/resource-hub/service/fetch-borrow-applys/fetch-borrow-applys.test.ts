/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-31
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchBorrowApplys as webService } from './fetch-borrow-applys.browser';
import { fetchBorrowApplys as nodeService } from './fetch-borrow-applys.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ borrowNo: 'JH00071' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ borrowNo: 'JH00071' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
