/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-31
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-borrow-applys';
import type { SvcQuery, SvcRespData } from './fetch-borrow-applys.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchBorrowApplys(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
