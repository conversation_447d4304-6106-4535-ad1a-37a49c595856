/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-31
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BorrowApplyAsset = {
  id: number;
  borrowNo: string;
  topCategory: string;
  secondCategory: string;
  deviceType: string;
  vendor: string;
  productModel: string;
  numbered: boolean; //设备类型 0:耗材 1:设备
  targetRoomGuid: string;
  targetRoomTag: string;
  applyNum: number; //申请数量
  stockOutNum: number; //借用数量
  assetType: 'OFF_LINE' | 'ON_LINE';
  gmtCreate: number;
  gmtModified: number;
  returnNum: number;
};

export type SvcQuery = {
  borrowNo: string;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: BorrowApplyAsset[];
  total: number;
} | null;

export type SvcRespData = {
  data: BorrowApplyAsset[];
  total: number;
};

export type ApiR = ListResponse<BorrowApplyAsset[] | null>;
