/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-4
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDevices as webService } from './fetch-devices.browser';
import { fetchDevices as nodeService } from './fetch-devices.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ blockGuids: ['EC06.A'] });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ blockGuids: ['EC06.A'] });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
