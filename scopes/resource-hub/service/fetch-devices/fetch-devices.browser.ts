/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-4
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-devices';
import type { SvcQuery, SvcRespData } from './fetch-devices.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchDevices(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
