/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-4
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { AssetState, BackendDevice, Device } from '@manyun/resource-hub.model.device';

export type SvcQuery = {
  idc: string;
  block?: string;
  room?: string;
  name?: string;
  tag?: string;
  vendor?: string;
  /**
   * 型号
   */
  model?: string;
  assetNumbers?: string[];
  assetStates?: AssetState[];
  deviceTags?: string[];
  deviceGuids?: string[];
  deviceTypes?: string[];
  blockGuids?: string[];
  roomGuids?: string[];
  operationStatus?: 'ON' | 'OFF';
  querySpec?: boolean;
};

export type SvcRespData = {
  data: Device[];
  total: number;
};

export type RequestRespData = {
  data: BackendDevice[] | null;
  total: number;
} | null;

export type ApiQ = {
  name?: string | null;
  tag?: string | null;
  vendor?: string | null;
  productModel?: string | null;
  idcTag: string;
  blockTag?: string | null;
  roomTag?: string | null;
  blockGuidList?: string[] | null;
  roomGuidList?: string[] | null;
  assetNoList?: string[] | null;
  assetStatusList?: string[] | null;
  deviceTags?: string[] | null;
  deviceGuidList?: string[] | null;
  deviceTypes?: string[] | null;
  operationStatus?: 'ON' | 'OFF';
  querySpec?: boolean;
};

export type ApiR = ListResponse<BackendDevice[] | null>;
