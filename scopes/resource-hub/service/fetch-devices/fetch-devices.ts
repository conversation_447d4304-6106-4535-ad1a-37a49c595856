/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-4
 *
 * @packageDocumentation
 */
import { Device } from '@manyun/resource-hub.model.device';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-devices.type';

const endpoint = '/dccm/query/device/by/block';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/140/interface/api/15600)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = {
      name: svcQuery.name,
      tag: svcQuery.tag,
      vendor: svcQuery.vendor,
      productModel: svcQuery.model,
      idcTag: svcQuery.idc,
      blockTag: svcQuery.block,
      roomTag: svcQuery.room,
      blockGuidList: svcQuery.blockGuids,
      roomGuidList: svcQuery.roomGuids,
      assetNoList: svcQuery.assetNumbers,
      assetStatusList: svcQuery.assetStates,
      deviceTags: svcQuery.deviceTags,
      deviceGuidList: svcQuery.deviceGuids,
      deviceTypes: svcQuery.deviceTypes,
      operationStatus: svcQuery.operationStatus,
      querySpec: svcQuery.querySpec,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(Device.fromApiObject), total: data.total },
      ...rest,
    };
  };
}
