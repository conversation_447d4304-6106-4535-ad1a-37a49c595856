/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-7
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-grid-customers';
import type { ApiArgs, ApiResponse } from './fetch-grid-customers.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function fetchGridCustomers(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
