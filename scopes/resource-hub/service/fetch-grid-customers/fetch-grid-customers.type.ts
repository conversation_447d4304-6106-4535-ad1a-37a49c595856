/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-7
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 客户名称
   */
  customerName?: string | null;
  /**
   * 机房
   */
  idcTag?: string | null;
  /**
   * 楼栋
   */
  blockTag?: string | null;
};

export type Customer = {
  /**
   * 客户名称
   */
  customerName: string;
  /**
   * 客户编号
   */
  customerNo: string;
};

export type ApiResponse = ListResponse<Customer[]>;
