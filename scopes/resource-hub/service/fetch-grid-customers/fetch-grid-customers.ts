/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-11-7
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './fetch-grid-customers.type';

const endpoint = '/dccm/customer/by/grid/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/140/interface/api/25287)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    args: ApiArgs
  ): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> => {
    const { error, data, ...rest } = await request.tryPost<
      Pick<ApiResponse, 'data' | 'total'> | null,
      ApiArgs
    >(endpoint, args);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
