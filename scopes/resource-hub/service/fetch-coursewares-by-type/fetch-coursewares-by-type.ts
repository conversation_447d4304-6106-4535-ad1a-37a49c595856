/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-coursewares-by-type.type';

const endpoint = '/dcexam/course/file/target/query';

/**
 * @see [查询标签关联课件](http://172.16.0.17:13000/project/15/interface/api/18160)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ targetId }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { targetId, targetType: 'DEVICE_TYPE' };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(courseware => ({
          ...courseware,
          groupKey: `${courseware.fileType}_${courseware.id}`,
        })),
        total: data.total,
      },
      ...rest,
    };
  };
}
