/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  targetId: string;
};

export type BackendCourseware = {
  id: number;
  fileName: string;
  filePath?: string;
  fileSize: number;
  fileTime?: number;
  fileType: string;
  isDownload?: boolean;
  gmtCreate: number;
  gmtModified: number;
  categoryCode: string;
  operatorId: number;
  operatorName: string;
};

export type Courseware = BackendCourseware & { groupKey: string };

export type SvcRespData = {
  data: Courseware[];
  total: number;
};

export type RequestRespData = {
  data: BackendCourseware[] | null;
  total: number;
} | null;

export type ApiQ = {
  targetId: string;
  /**
   * 现在targetType的值只有 'DEVICE_TYPE'
   */
  targetType: string;
};

export type ApiR = ListResponse<BackendCourseware[] | null>;
