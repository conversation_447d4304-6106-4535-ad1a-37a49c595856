/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-3
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-borrow-resubmit';
import type { SvcQuery, SvcRespData } from './create-borrow-resubmit.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createBorrowResubmit(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
