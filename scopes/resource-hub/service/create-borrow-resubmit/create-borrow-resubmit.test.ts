/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-3
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createBorrowResubmit as webService } from './create-borrow-resubmit.browser';
import { createBorrowResubmit as nodeService } from './create-borrow-resubmit.node';
import type { ApiQ } from './create-borrow-resubmit.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

const params: ApiQ = {
  borrowNo: 'JH00071',
};

const errorParams: ApiQ = {
  borrowNo: '',
};

test('[web] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService(params);

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService(errorParams);

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
