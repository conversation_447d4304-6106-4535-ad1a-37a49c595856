/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-3
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { SvcQuery as CreateBorrowParams } from '@manyun/resource-hub.service.create-borrow';

export type SvcQuery = CreateBorrowParams & {
  borrowNo: string;
};

export type RequestRespData = boolean | null;

export type SvcRespData = RequestRespData;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
