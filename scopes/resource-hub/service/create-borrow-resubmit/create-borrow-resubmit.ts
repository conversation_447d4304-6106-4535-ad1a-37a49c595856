/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-8-3
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-borrow-resubmit.type';

const endpoint = '/dccm/borrow/resubmit';

/**
 * @see [借用单重新发起](http://172.16.0.17:13000/project/306/interface/api/24780)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    return { error, data, ...rest };
  };
}
