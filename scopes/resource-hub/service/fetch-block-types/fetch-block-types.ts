/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { SvcRespData } from './fetch-block-types.type';

const endpoint = '/dccm/get/block/type';

/**
 * @see [查询楼栋类别](http://172.16.0.17:13000/project/136/interface/api/7416)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse> => {
    const { error, data, ...rest } = await request.tryGet<SvcRespData, undefined>(endpoint);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: data, ...rest };
  };
}
