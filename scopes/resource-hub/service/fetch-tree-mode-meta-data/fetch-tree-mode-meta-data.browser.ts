/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-27
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-tree-mode-meta-data';
import type { RequestRD, SvcQuery, SvcRespData } from './fetch-tree-mode-meta-data.type';

/**
 * 在这里补充 Service 说明
 *
 * @see [Doc](在这里补充 API 文档链接)
 *
 * @param
 * @returns
 */
export async function fetchTreeModeMetaData(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const { error, data, ...rest } = await webRequest.tryGet<RequestRD, SvcQuery>(endpoint, {
    params,
  });

  if (error || data === null || !data.data) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }

  return { error, data: { data: data.data, total: data.total }, ...rest };
}
