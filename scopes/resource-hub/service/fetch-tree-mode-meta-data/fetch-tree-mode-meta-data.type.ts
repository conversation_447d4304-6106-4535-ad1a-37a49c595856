/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-27
 *
 * @packageDocumentation
 */
import type { BackendMetadata } from '@manyun/resource-hub.model.metadata';

export type BackendData = BackendMetadata & { children: BackendData[] };
export type SvcQuery = {
  topCategory: string;
  secondCategory: string;
};

export type SvcRespData = {
  data: BackendData[];
  total: number;
};

export type RequestRD = SvcRespData | null;
