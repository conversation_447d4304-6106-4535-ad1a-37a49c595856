/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-27
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './fetch-tree-mode-meta-data.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchTreeModeMetaData as webService } from './fetch-tree-mode-meta-data.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    topCategory: 'OPERATION_SERVICE',
    secondCategory: 'SERVICE_SUB_TYPE',
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({
    topCategory: 'OPERATION_SERVICE',
    secondCategory: 'OPERATION_SERVICE',
  });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
