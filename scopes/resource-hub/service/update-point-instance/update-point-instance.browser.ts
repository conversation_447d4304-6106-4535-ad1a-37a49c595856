/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-point-instance';
import type { SvcQuery, SvcRespData } from './update-point-instance.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updatePointInstance(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
