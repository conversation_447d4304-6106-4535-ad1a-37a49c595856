/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type RequestRespData = SvcRespData;

export type ExpressInfo = {
  children: ExpressInfo[] | null;
  id: string;
  name: string;
  type: string;
  value: string;
};
export type ApiQ = {
  id: number;
  expressInfos: ExpressInfo[];
};

export type SvcRespData = boolean | null;

export type ApiR = WriteResponse;
