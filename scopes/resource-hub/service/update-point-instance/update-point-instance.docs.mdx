---
description: 'A updatePointInstance HTTP API service.'
labels: ['service', 'http']
---

## 概述

点位实例更新

## 使用

### Browser

```ts
import { updatePointInstance } from '@resource-hub/service.update-point-instance';

const { error, data } = await updatePointInstance({
  id: 121,
  expressInfos: [
    {
      children: null,
      id: 'JUzLIDNKcT',
      name: '【CFE2-1】药剂浓度',
      type: 'point',
      value: 'K_770769_1001000_20216',
    },
  ],
});
```
