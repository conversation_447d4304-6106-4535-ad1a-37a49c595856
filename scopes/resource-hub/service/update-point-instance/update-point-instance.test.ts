/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updatePointInstance as webService } from './update-point-instance.browser';
import { updatePointInstance as nodeService } from './update-point-instance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    id: 121,
    expressInfos: [
      {
        children: null,
        id: 'JUzLIDNKcT',
        name: '【CFE2-1】药剂浓度',
        type: 'point',
        value: 'K_770769_1001000_20216',
      },
    ],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ id: 1, expressInfos: [] });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    id: 121,
    expressInfos: [
      {
        children: null,
        id: 'JUzLIDNKcT',
        name: '【CFE2-1】药剂浓度',
        type: 'point',
        value: 'K_770769_1001000_20216',
      },
    ],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ id: 1, expressInfos: [] });

  expect(data).toBe(null);
});
