/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPoint, DataType, Point, PointType } from '@manyun/monitoring.model.point';

export type SvcQuery = {
  isCoreFirst?: boolean;
  dataTypeList?: DataType[];
  deviceType?: string;
  isRemoveMain?: boolean;
  isRemoveSub?: boolean;
  invalid?: boolean;
  isOnlyCore?: boolean;
  pointTypeList?: PointType[];
  spaceGuid?: string;
  // 是否查询非标点位，默认false
  isQueryNon?: boolean;
  blockGuid?: string;
};

export type SvcRespData = {
  data: Point[];
  total: number;
};

export type RequestRespData = {
  data: BackendPoint[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendPoint[] | null>;
