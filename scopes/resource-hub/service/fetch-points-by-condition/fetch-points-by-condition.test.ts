/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPointsByCondition as webService } from './fetch-points-by-condition.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    // dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
    deviceType: '10101',
    isRemoveSub: true,
    // pointTypeList: ['ORI'],
  });
  // console.log('data:', data);

  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    // dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
    deviceType: '10102',
    isRemoveSub: true,
    // pointTypeList: ['ORI'],
  });
  // console.log('error:', error);
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
