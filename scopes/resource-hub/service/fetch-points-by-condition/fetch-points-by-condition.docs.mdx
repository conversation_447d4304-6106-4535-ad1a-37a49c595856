---
description: 'A fetchPointsByCondition HTTP API service.'
labels: ['service', 'http', fetch-points-by-condition]
---

## 概述

查询测点列表

## 使用

### Browser

```ts
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';

const { error, data } = await fetchPointsByCondition({
  dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
  deviceType: '10101',
  isRemoveSub: true,
  pointTypeList: ['ORI'],
});
```
