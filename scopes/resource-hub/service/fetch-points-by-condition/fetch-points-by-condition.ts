/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { Point } from '@manyun/monitoring.model.point';
import type { BackendPoint } from '@manyun/monitoring.model.point';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-points-by-condition.type';

const endpoint = '/dccm/point/query/by/condition';

/**
 * @see [查询测点列表](http://172.16.0.17:13000/project/140/interface/api/15536)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: getSortedCoreList(data.data, variant.isOnlyCore, variant.isCoreFirst).map(
          Point.fromApiObject
        ),
        total: data.total,
      },
      ...rest,
    };
  };
}

function getSortedCoreList(list: BackendPoint[], isOnlyCore?: boolean, isCoreFirst?: boolean) {
  if (!isOnlyCore && !isCoreFirst) {
    return list;
  }
  const arr = [...list];
  arr.sort((a, b) => {
    return (a.priority ?? Number.POSITIVE_INFINITY) - (b.priority ?? Number.POSITIVE_INFINITY);
  });

  return arr;
}
