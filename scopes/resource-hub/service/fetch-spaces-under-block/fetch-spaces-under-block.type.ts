/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Space = {
  metaCode: string;
  metaName: string;
  metaType: string;
  parentCode: string;
};

export type SvcRespData = {
  data: Space[];
  total: number;
};

export type RequestRespData = {
  data: Space[] | null;
  total: number;
} | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  content: string;
  pageSize: number;
  spaceGuid: string;
  type: string;
};
export type ApiR = ListResponse<Space[] | null>;
