---
description: 'A fetchSpacesUnderBlock HTTP API service.'
labels: ['service', 'http', fetch-spaces-under-block]
---

## 概述

获取楼栋下的机列、机柜

## 使用

### Browser

```ts
import { fetchSpacesUnderBlock } from '@resource-hub/service.fetch-spaces-under-block';

const { error, data } = await fetchSpacesUnderBlock('success');
const { error, data } = await fetchSpacesUnderBlock('error');
```

### Node

```ts
import { fetchSpacesUnderBlock } from '@resource-hub/service.fetch-spaces-under-block/dist/index.node';

const { data } = await fetchSpacesUnderBlock('success');

try {
  const { data } = await fetchSpacesUnderBlock('error');
} catch (error) {
  // ...
}
```
