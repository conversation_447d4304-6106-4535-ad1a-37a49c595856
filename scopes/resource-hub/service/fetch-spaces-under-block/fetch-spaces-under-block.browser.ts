/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-spaces-under-block';
import type { ApiQ, SvcRespData } from './fetch-spaces-under-block.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchSpacesUnderBlock(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
