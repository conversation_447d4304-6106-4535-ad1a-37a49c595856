---
description: 'A fetchCabinetType HTTP API service.'
labels: ['service', 'http', fetch-cabinet-type]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchCabinetType } from '@resource-hub/service.fetch-cabinet-type';

const { error, data } = await fetchCabinetType('success');
const { error, data } = await fetchCabinetType('error');
```

### Node

```ts
import { fetchCabinetType } from '@resource-hub/service.fetch-cabinet-type/dist/index.node';

const { data } = await fetchCabinetType('success');

try {
  const { data } = await fetchCabinetType('error');
} catch(error) {
  // ...
}
```
