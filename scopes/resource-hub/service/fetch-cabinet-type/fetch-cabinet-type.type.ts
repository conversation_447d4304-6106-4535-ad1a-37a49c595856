/**
 * <AUTHOR> <PERSON> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
export type GridTypeModel = {
  NETWORK_GRID: string;
  SERVER_GRID: string;
  OTHER: string;
};

export type RackType =
  | 'NETWORK_GRID'
  | 'SERVER_GRID'
  | 'OTHER'
  | 'ODF_GRID'
  | 'WEAK_GRID'
  | 'WHOLE_GRID'
  | 'EMPTY_GRID'
  | 'SPARE_GRID';

export const rackTypeTextMap: Record<RackType, string> = {
  NETWORK_GRID: '网络机柜',
  SERVER_GRID: '服务器机柜',
  ODF_GRID: 'ODF机柜',
  WEAK_GRID: '弱电机柜',
  OTHER: '其他',
  WHOLE_GRID: '整机柜',
  EMPTY_GRID: '空柜',
  SPARE_GRID: '备用机柜',
};

export type SvcRespData = GridTypeModel | null;

export type RequestRespData = GridTypeModel | null;
