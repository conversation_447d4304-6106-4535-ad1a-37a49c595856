/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-cabinet-type';
import type { SvcRespData } from './fetch-cabinet-type.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function fetchCabinetType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
