/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-cabinet-type.type';

const endpoint = '/dccm/get/grid/type';

let requesting = false;

/**
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/7448)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    /* @Todo @Jerry */
    if (requesting) {
      await new Promise(resolve => setTimeout(resolve, 800));
    }
    requesting = true;
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint, {
      cache: {
        ttl: 8 * 60 * 60 * 1000,
      },
    });
    requesting = false;

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data,
      ...rest,
    };
  };
}
