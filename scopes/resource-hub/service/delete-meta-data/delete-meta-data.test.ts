/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { getMock } from '@manyun/service.request';

import { deleteMetaData as webService } from './delete-meta-data.browser';
import { registerWebMocks } from './delete-meta-data.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('deleteMetaData', async () => {
  const payload = { id: 1, metaType: MetaType.NOTIFY_EVENT_TYPE };
  const { data, error } = await webService(payload);
  expect(data).toBe(payload.id);
  expect(error).toBe(undefined);
});
