/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './delete-meta-data';
import type { ApiQ, RequestRD, ServiceQ, ServiceRD } from './delete-meta-data.type';

/**
 * 删除元数据
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/pkinbk/harsyg)
 *
 * @param query Service query object
 * @returns
 */
export async function deleteMetaData(query: ServiceQ): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = query;
  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);
  return { error, data: query.id, ...rest };
}
