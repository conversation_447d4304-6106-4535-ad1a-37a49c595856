/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-meta-data';
import type { ApiQ } from './delete-meta-data.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    // POST 请求需要 parse config.data
    const { id, metaType } = JSON.parse(config.data) as ApiQ;

    return [
      200,
      {
        success: true,
        errCode: null,
        errMessage: null,
        data: id,
      },
    ];
  });
}
