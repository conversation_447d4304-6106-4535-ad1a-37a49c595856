/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-22
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type ServiceQ = {
  id: number;
  metaType: MetaType;
};

export type ServiceRD = number;

export type RequestRD = number | null;

export type ApiQ = {
  id: number;
  metaType: MetaType;
};

export type ApiR = Response<RequestRD>;
