/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-3
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-room-types.type';

const endpoint = '/dccm/get/room/type';

/**
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/7512)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData>(endpoint);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    const roomTypes: SvcRespData = Object.keys(data).map(code => ({ code, text: data[code] }));

    return { error, data: roomTypes, ...rest };
  };
}
