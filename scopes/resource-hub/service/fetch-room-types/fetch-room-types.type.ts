/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-3
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type RoomTypeCode = string;
export type RoomTypeText = string;
export type BackendRoomTypeRecord = Record<RoomTypeCode, RoomTypeText>;

export type SvcRespData = Array<{
  code: RoomTypeCode;
  text: RoomTypeText;
}>;
export type RequestRespData = BackendRoomTypeRecord | null;
export type ApiR = Response<RequestRespData>;
