/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-3
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-room-types';
import type { SvcRespData } from './fetch-room-types.type';

const executor = getExecutor(nodeRequest);

export function fetchRoomTypes(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
