---
description: '新增规格'
labels: ['service', 'http']
---

## 使用

### Browser

```ts
import { createSpec } from '@resource-hub/service.create-spec';

const { error, data } = await createSpec({
  deviceType: '10101',
  required: true,
  specName: '规格1',
  valueType: 1,
  inputWay: 'INPUT',
});
```

### Node

```ts
import { createSpec } from '@resource-hub/service.create-spec/dist/index.node';

const { data } = await createSpec({
  deviceType: '10101',
  required: true,
  specName: '规格1',
  valueType: 1,
  inputWay: 'INPUT',
});
```
