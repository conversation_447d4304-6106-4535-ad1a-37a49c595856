/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createSpec as webService } from './create-spec.browser';
import { createSpec as nodeService } from './create-spec.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    deviceType: '10101',
    required: true,
    specName: '规格1',
    valueType: 'NUMBER',
    inputWay: 'INPUT',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    deviceType: '',
    required: true,
    specName: '规格1',
    valueType: 'NUMBER',
    inputWay: 'INPUT',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    deviceType: '10101',
    required: true,
    specName: '规格1',
    valueType: 'NUMBER',
    inputWay: 'INPUT',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    deviceType: '',
    required: true,
    specName: '规格1',
    valueType: 'NUMBER',
    inputWay: 'INPUT',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});
