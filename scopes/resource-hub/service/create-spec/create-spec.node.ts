/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-20
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-spec';
import type { ApiQ, SvcRespData } from './create-spec.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createSpec(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
