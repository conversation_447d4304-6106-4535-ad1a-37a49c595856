/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendIdc, Idc } from '@manyun/resource-hub.model.idc';

export type SvcQuery = ApiQ;

export type SvcRespData = {
  data: Idc[];
  total: number;
};

export type RequestRespData = {
  data: BackendIdc[] | null;
  total: number;
} | null;

export type ApiQ = {
  cityCode?: string;
  constructTimeEnd?: string; //建设日期
  constructTimeStart?: string;
  guid?: string;
  name?: string;
  nationCode?: string;
  operationStatus?: string; //已启用,未启用
  operationTimeEnd?: string; //投产日期
  operationTimeStart?: string;
  provinceCode?: string;
  regionCode?: string;
};

export type ApiR = ListResponse<BackendIdc[] | null>;
