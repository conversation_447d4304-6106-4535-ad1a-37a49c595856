/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  CreateApiQ,
  RequestRespData,
  SvcRespData,
  UpdateApiQ,
} from './mutate-stock-threshold.type';

const updateEndpoint = '/dccm/inventory/warn/update';
const createEndpoint = '/dccm/inventory/warn/create';

/**
 * @see [Doc](YAPI create:http://172.16.0.17:13000/project/102/interface/api/3792 update:http://172.16.0.17:13000/project/102/interface/api/3816)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    stockThreshold: UpdateApiQ | CreateApiQ
  ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    if ('id' in stockThreshold) {
      const apiQ: UpdateApiQ = stockThreshold;

      const { error, data, ...rest } = await request.tryPost<RequestRespData, UpdateApiQ>(
        updateEndpoint,
        apiQ
      );
      return { error, data, ...rest };
    }

    const apiQ: CreateApiQ = stockThreshold;

    const { error, data, ...rest } = await request.tryPost<RequestRespData, CreateApiQ>(
      createEndpoint,
      apiQ
    );
    return { error, data, ...rest };
  };
}
