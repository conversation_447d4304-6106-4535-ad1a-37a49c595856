/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './mutate-stock-threshold';
import type { CreateApiQ, SvcRespData, UpdateApiQ } from './mutate-stock-threshold.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function mutateStockThreshold(
  variant: CreateApiQ | UpdateApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
