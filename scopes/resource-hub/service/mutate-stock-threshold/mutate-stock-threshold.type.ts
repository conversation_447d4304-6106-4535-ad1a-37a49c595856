/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = string | null;

export type RequestRespData = string | null;

// SvcQuery 与 apiQ 相同
export type UpdateApiQ = {
  // 自增id
  id: number;
  // 位置，仅楼栋
  blockGuid: string;
  // 三级分类code，左侧决定
  code: string;
  // 水位阈值
  level: number;
  // 型号
  productModel?: string | null;
  // 厂商
  vendor?: string | null;
};
export type CreateApiQ = Omit<UpdateApiQ, 'id'>;
export type ApiR = Response<RequestRespData>;
