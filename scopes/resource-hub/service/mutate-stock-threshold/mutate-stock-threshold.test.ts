/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateStockThreshold as webService } from './mutate-stock-threshold.browser';

// import { mutateStockThreshold as nodeService } from './mutate-stock-threshold.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    code: '11',
    level: 10,
    productModel: null,
    vendor: null,
    blockGuid: '80101',
  });

  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    code: '11',
    level: 10,
    productModel: null,
    vendor: null,
    blockGuid: '80101',
  });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  // const { error, data } = await nodeService('success');
  // expect(error).toBe(undefined);
  // expect(data.data).toHaveProperty('length');
  // expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  // const { error, data } = await nodeService('error');
  // expect(typeof error!.code).toBe('string');
  // expect(typeof error!.message).toBe('string');
  // expect(data.data).toHaveProperty('length');
  // expect(typeof data.total).toBe('number');
});
