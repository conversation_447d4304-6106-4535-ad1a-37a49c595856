---
description: 'A mutateStockThreshold HTTP API service.'
labels: ['service', 'http', mutate-stock-threshold]
---

## 概述

更新阈值信息

## 使用

### Browser

```ts
import { mutateStockThreshold } from '@manyun/redash.service.mutate-stock-threshold';

const { error, data } = await mutateStockThreshold({
  code: '11',
  level: 10,
  productModel: null,
  vendor: null,
  blockGuid: '80101',
});
```

### Node

```ts
import { mutateStockThreshold } from '@manyun/redash.service.mutate-stock-threshold/dist/index.node';

const { data } = await mutateStockThreshold({
  code: '11',
  level: 10,
  productModel: null,
  vendor: null,
  blockGuid: '80101',
});
```
