/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './relate-coursewares-by-type.type';

const endpoint = '/dcexam/course/file/target/relate';

/**
 * @see [标签关联课件](http://172.16.0.17:13000/project/15/interface/api/18168)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    targetId,
    courseFileIds,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { targetId, courseFileIds, targetType: 'DEVICE_TYPE' };

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
