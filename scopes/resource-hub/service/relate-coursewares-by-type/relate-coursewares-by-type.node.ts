/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './relate-coursewares-by-type';
import type { SvcQuery, SvcRespData } from './relate-coursewares-by-type.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function relateCoursewaresByType(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
