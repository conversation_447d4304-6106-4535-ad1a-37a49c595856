/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { relateCoursewaresByType as webService } from './relate-coursewares-by-type.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ targetId: '10101', courseFileIds: [1] });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ targetId: '10102', courseFileIds: [1] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
