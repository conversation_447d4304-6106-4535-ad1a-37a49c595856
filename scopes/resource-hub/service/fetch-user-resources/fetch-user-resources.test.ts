/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-9
 *
 * 查询当前用户拥有的资源
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserResources as webService } from './fetch-user-resources.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    resourceTypes: ['ROOT'],
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    searchName: undefined,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
