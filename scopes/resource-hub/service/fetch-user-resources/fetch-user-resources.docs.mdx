---
description: '查询当前用户拥有的资源'
labels: ['service', 'http']
---

## 概述

查询当前用户拥有的资源

## 使用

### Browser

```ts
import { fetchUserResources } from '@resource-hub/service.fetch-user-resources';

const { data } = await fetchUserResources({
  resourceTypes: ['ROOT'],
});
const { error, data } = await fetchUserResources({
  resourceTypes: null,
});
```

### Node

```ts
import { fetchUserResources } from '@resource-hub/service.fetch-user-resources/dist/index.node';

const { data } = await fetchUserResources({
  resourceTypes: ['ROOT'],
});
const { error, data } = await fetchUserResources({
  resourceTypes: null,
});
```
