/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-user-resources.type';

const endpoint = '/pm/resource/user/resources';

/**
 * 查询当前用户拥有的资源
 * @see [查询当前用户拥有的资源](http://172.16.0.17:13000/project/78/interface/api/19347)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params?: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
