/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type UserResourceModel = {
  /**父id */
  parentId: number;
  /**id */
  id: number;
  /**子级 */
  children: UserResourceModel;
  key: string;
  /**标题 */
  title: string;
  /**类型 */
  type: string;
  /**资源名称 */
  resourceName: string;
  /** 备注 */
  remarks: string;
  /** 资源code	 */
  resourceCode: string;
  /** 资源类型	 */
  resourceType: UserResouceTypes;
  /**创建人id */
  creatorId: number;
  /**修改人id */
  modifierId: number;
  /**创建人名 */
  creatorName: string;
  /** 修改人名 */
  modifierName: string;
};

export type SvcRespData = {
  data: UserResourceModel[];
  total: number;
};

export type RequestRespData = {
  data: UserResourceModel[] | null;
  total: number;
} | null;

/** 资源类型 ： ROOT("根节点"),    IDC("机房"),       BUILDING("楼栋"),       AREA("区域"),    CUSTOMER("客户") */
export type UserResouceTypes = 'ROOT' | 'IDC' | 'BUILDING' | 'AREA' | 'CUSTOMER';

export type ApiQ = {
  /**资源类型 */
  resourceTypes?: UserResouceTypes[];
  /**搜索名字 */
  searchName?: string;
  /** 父类code */
  parentCode?: string;
  /** 是否需要虚拟楼栋 */
  needVirtualBlock?: boolean;
  /** 用户id */
  userId?: string;
};

export type ApiR = ListResponse<UserResourceModel[] | null>;
