/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-resources';
import type { SvcQuery, SvcRespData } from './fetch-user-resources.type';

const executor = getExecutor(webRequest);

/**
 * 查询当前用户拥有的资源
 * @param variant
 * @returns
 */
export function fetchUserResources(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
