/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-core-points';
import type { SvcQuery, SvcRespData } from './update-core-points.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function updateCorePoints(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
