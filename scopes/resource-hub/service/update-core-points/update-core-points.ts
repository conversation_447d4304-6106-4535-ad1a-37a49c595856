/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './update-core-points.type';

const endpoint = '/dccm/point/core/update';

/**
 * @see [添加、删除、调序核心点位](http://172.16.0.17:13000/project/140/interface/api/17896)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ pointList, variant }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pointList: pointList.map((point, idx) => ({
        deviceType: point.deviceType!,
        pointCode: point.code!,
        priority: point.priority !== null || variant === 'add' ? idx + 1 : null,
      })),
    };

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
