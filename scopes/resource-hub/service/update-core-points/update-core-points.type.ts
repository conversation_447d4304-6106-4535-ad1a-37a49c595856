/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { Point } from '@manyun/monitoring.model.point';

export type CorePoint = {
  deviceType: string;
  pointCode: string;
  priority?: number | null;
};

export type SvcQuery = {
  variant: 'add' | 'delete';
  pointList: Partial<Point>[];
};

export type SvcRespData = boolean;

export type ApiQ = {
  pointList: CorePoint[];
};

export type ApiR = WriteResponse;
