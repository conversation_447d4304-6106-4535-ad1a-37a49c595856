/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-entrance-auth-groups';
import type { ApiQ, SvcRespData } from './fetch-entrance-auth-groups.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchEntranceAuthGroups(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
