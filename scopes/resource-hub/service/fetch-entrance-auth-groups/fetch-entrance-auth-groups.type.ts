/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type EntranceAuthGroups = {
  id: number;
  /**  权限组ID*/
  authId: string;
  blockGuid: string;
  /** 权限组名称 */
  authName: string;
  gmtCreate: number;
  gmtModified: number;
};

export type SvcRespData = {
  data: EntranceAuthGroups[];
  total: number;
};

export type RequestRespData = {
  data: EntranceAuthGroups[] | null;
  total: number;
} | null;

export type ApiQ = {
  blockGuid: string;
};

export type ApiR = ListResponse<EntranceAuthGroups[] | null>;
