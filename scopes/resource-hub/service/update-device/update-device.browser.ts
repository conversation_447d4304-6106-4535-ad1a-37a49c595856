/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-device';
import type { SvcQuery, SvcRespData } from './update-device.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updateDevice(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
