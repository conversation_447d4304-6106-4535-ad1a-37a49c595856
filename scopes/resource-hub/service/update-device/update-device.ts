/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './update-device.type';

const endpoint = '/dccm/device/update';

/**
 * @see [编辑设备](http://172.16.0.17:13000/project/140/interface/api/14448)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    areaIdcBlockRoom,
    categoryCascaderList,
    vendorModel,
    ...rest
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params = {
      idcTag: areaIdcBlockRoom[0],
      blockTag: areaIdcBlockRoom[1],
      roomTag: areaIdcBlockRoom[2],
      topCategory: categoryCascaderList.firstCategoryCode,
      secondCategory: categoryCascaderList.secondCategoryCode,
      deviceType: categoryCascaderList.thirdCategorycode,
      vendor: vendorModel[0],
      productModel: vendorModel[1],
      ...rest,
    } as ApiQ;

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
