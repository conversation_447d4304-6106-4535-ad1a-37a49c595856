---
description: 'A updateDevice HTTP API service.'
labels: ['service', 'http', update-device]
---

## 概述

编辑设备

## 使用

### Browser

```ts
import { updateDevice } from '@manyun/resource-hub.service.update-device';

const { error, data } = await updateDevice({
  assetNo: '1',
  guid: '1',
  name: '1',
  operatorNotes: '1',
  areaIdcBlockRoom: ['EC01', 'A', 'A1-1'],
  categoryCascaderList: {
    thirdCategorycode: '10807',
    secondCategoryCode: '108',
    firstCategoryCode: '1',
  },
  vendorModel: ['1', '2'],
});
```
