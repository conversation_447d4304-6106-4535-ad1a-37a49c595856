/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiQ = {
  assetNo: string;
  assetStatus?: string;
  checkTime?: number;
  enableTime?: number;
  extendPosition?: string;
  guid: string;
  idcTag: string;
  blockTag: string;
  roomTag: string;
  name: string;
  operationStatus?: string;
  operator?: string;
  operatorId?: number;
  operatorNotes: string;
  parentGuid?: string;
  powerLine?: string;
  purchasePrice?: number;
  purchaseTime?: number;
  scrapTime?: number;
  serialNumber?: string;
  tag?: string;
  deviceLabel?: string;
  topCategory?: string;
  secondCategory?: string;
  deviceType: string;
  unit?: string;
  vendor: string;
  productModel: string;
  warrantyPrice?: number;
  warrantyTime?: number;
  warrantyVendor?: string;
};

export type SvcQuery = {
  assetNo: string;
  assetStatus?: string;
  checkTime?: number;
  enableTime?: number;
  extendPosition?: string;
  guid: string;
  name: string;
  operationStatus?: string;
  operator?: string;
  operatorId?: number;
  operatorNotes: string;
  parentGuid?: string;
  powerLine?: string;
  purchasePrice?: number;
  purchaseTime?: number;
  scrapTime?: number;
  serialNumber?: string;
  tag?: string;
  deviceLabel?: string;
  unit?: string;
  warrantyPrice?: number;
  warrantyTime?: number;
  warrantyVendor?: string;

  areaIdcBlockRoom: string[];
  categoryCascaderList: Record<string, string>;
  vendorModel: string[];
};

export type SvcRespData = boolean;

export type ApiR = WriteResponse;
