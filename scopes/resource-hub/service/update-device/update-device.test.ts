/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateDevice as webService } from './update-device.browser';

let webMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
});
afterAll(() => {
  webMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    assetNo: '1',
    guid: '1',
    name: '1',
    operatorNotes: '1',
    areaIdcBlockRoom: ['EC01', 'A', 'A1-1'],
    categoryCascaderList: {
      thirdCategorycode: '10807',
      secondCategoryCode: '108',
      firstCategoryCode: '1',
    },
    vendorModel: ['1', '2'],
  });

  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    assetNo: '2',
    guid: '1',
    name: '1',
    operatorNotes: '1',
    areaIdcBlockRoom: ['EC01', 'A', 'A1-1'],
    categoryCascaderList: {
      thirdCategorycode: '10807',
      secondCategoryCode: '108',
      firstCategoryCode: '1',
    },
    vendorModel: ['1', '2'],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
