/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BorrowType, BorrowerType } from '@manyun/resource-hub.model.borrow-return';

export type ApprovalStatus = 'APPROVING' | 'PASS' | 'REVOKE' | 'REFUSE'; //APPROVING:审批  PASS:通过 REFUSE:拒绝 REVOKE: 撤回
export type BorrowRenewLendRecord = {
  id: string;
  borrowNo: string;
  opId: string;
  opType: 'RENEW' | 'TRANSFER'; //RENEW：续借 TRANSFER：转借
  approvalId: string;
  approvalStatus: ApprovalStatus;
  opJson: {
    endDate: number;
    borrowType: BorrowType;
    borrowerType: BorrowerType;
    borrower: string;
    borrowerName: string;
    personLiable: string;
    personLiableName: string;
    reason: string;
  };
  operator: string;
  operatorName: string;
  gmtCreate: number;
  gmtModified: number;
};

export type SvcQuery = {
  borrowNo: string;
};

export type ApiQ = SvcQuery;

export type RequestRespData = {
  data: BorrowRenewLendRecord[];
  total: number;
} | null;

export type SvcRespData = {
  data: BorrowRenewLendRecord[];
  total: number;
};

export type ApiR = ListResponse<BorrowRenewLendRecord[] | null>;
