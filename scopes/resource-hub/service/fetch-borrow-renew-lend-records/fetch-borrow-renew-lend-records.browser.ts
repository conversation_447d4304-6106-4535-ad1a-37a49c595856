/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-borrow-renew-lend-records';
import type { SvcQuery, SvcRespData } from './fetch-borrow-renew-lend-records.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchBorrowRenewLendRecords(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
