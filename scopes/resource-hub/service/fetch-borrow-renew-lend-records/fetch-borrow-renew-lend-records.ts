/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-borrow-renew-lend-records.type';

const endpoint = '/dccm/borrow/oplog/list';

/**
 * @see [续转记录](https://manyun.yuque.com/ewe5b3/bnpnl9/vvo1ms#Bx4qC)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data,
        total: data.total,
      },
      ...rest,
    };
  };
}
