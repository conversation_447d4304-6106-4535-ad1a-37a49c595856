/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchBorrowRenewLendRecords as webService } from './fetch-borrow-renew-lend-records.browser';
import { fetchBorrowRenewLendRecords as nodeService } from './fetch-borrow-renew-lend-records.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ borrowNo: 'JH00016' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ borrowNo: 'JH00016' });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
