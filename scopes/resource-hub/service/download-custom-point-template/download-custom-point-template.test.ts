/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { downloadCustomPointTemplate as webService } from './download-custom-point-template.browser';
import { downloadCustomPointTemplate as nodeService } from './download-custom-point-template.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService();

  expect(error).toBe(undefined);
});
