/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './download-custom-point-template';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function downloadCustomPointTemplate(): Promise<EnhancedAxiosResponse<Blob>> {
  return executor();
}
