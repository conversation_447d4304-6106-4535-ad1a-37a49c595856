/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-9-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

const endpoint = '/dccm/point/custom/download';

/**
 * @see [Doc](http://172.16.0.17:13000/project/222/interface/api/25104)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<Blob>> => {
    return await request.tryPost<Blob>(endpoint, {}, { responseType: 'blob' });
  };
}
