/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './revert-borrow';
import type { RequestRespData, SvcQuery } from './revert-borrow.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function revertBorrow(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<RequestRespData>> {
  return executor(svcQuery);
}
