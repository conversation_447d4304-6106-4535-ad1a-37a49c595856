/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { revertBorrow as webService } from './revert-borrow.browser';
import { revertBorrow as nodeService } from './revert-borrow.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ borrowNo: 'JH00071' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({ borrowNo: 'JH00071' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ borrowNo: '' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
