---
description: 'A fetchDeviceAssetInfo HTTP API service.'
labels: ['service', 'http']
---

## 概述

### 设备资产台账信息

## 使用

### Browser

```ts
import { fetchDeviceAssetInfo } from '@resource-hub/service.fetch-device-asset-info';

const { error, data } = await fetchDeviceAssetInfo({ deviceGuid: '1010101' });
const { error } = await fetchDeviceAssetInfo({ deviceGuid: null });
```

### Node

```ts
import { fetchDeviceAssetInfo } from '@resource-hub/service.fetch-device-asset-info/dist/index.node';

const { error, data } = await fetchDeviceAssetInfo({ deviceGuid: '1010101' });
const { error } = await fetchDeviceAssetInfo({ deviceGuid: null });
```
