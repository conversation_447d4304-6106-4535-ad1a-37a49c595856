/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceAssetInfo as webService } from './fetch-device-asset-info.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    deviceGuid: '1010101',
  });

  expect(data.data).toHaveProperty('projectName');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    deviceGuid: '',
  });

  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('projectName');
});
