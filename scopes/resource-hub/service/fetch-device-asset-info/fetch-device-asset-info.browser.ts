/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-device-asset-info';
import type { SvcQuery, SvcRespData } from './fetch-device-asset-info.type';

const executor = getExecutor(webRequest);

/**
 * 设备资产台账信息
 * @param variant
 * @returns
 */
export function fetchDeviceAssetInfo(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
