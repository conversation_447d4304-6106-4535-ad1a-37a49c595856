/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-device-asset-info.type';

const endpoint = '/dctrans/asset/device/info';

/**
 * 设备资产台账信息
 * @see [Doc](http://172.16.0.17:13000/project/124/interface/api/19319)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data, ...rest };
  };
}
