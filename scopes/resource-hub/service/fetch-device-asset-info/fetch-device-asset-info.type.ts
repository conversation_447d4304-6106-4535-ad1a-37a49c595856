/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  deviceGuid: number | string;
};

export type DeviceAssetInfoModel = {
  /** 项目名称 */
  projectName: string;
  /** 项目ID */
  projectId: number;
  /** 含税金额 */
  priceWithTax: number;
  /**   原值	*/
  originValue: number;
  /** 税率 */
  taxRate: number;
  /** 净值 */
  assetAmount: number;
  /** 累计折旧 */
  depreciateAmount: number;
  /** 每期折旧 */
  eachDepreciateAmount: number;
  /** 报废日期 */
  scrapDate: string;
  /** 采购日期 */
  purchaseDate: string;
};

export type SvcRespData = DeviceAssetInfoModel | null;

export type RequestRespData = DeviceAssetInfoModel | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<DeviceAssetInfoModel | null>;
