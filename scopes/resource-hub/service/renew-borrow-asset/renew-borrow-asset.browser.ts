/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './renew-borrow-asset';
import type { SvcQuery, SvcRespData } from './renew-borrow-asset.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function renewBorrowAsset(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
