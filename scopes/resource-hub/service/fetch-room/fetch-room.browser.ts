/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-31
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-room';
import type { ApiQ, SvcRespData } from './fetch-room.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchRoom(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
