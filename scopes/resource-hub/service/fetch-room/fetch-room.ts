/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-31
 *
 * @packageDocumentation
 */
import { Room } from '@manyun/resource-hub.model.room';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-room.type';

const endpoint = 'dccm/room/query/detail';

/**
 * 查询包间详情
 * @see [查询包间详情](http://172.16.0.17:13000/project/140/interface/api/15768)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, svcQ);

    if (error || data === null) {
      return {
        ...rest,
        error: error ? error : { message: `找不到包间：${svcQ.roomGuid}` },
        data: null,
      };
    }

    return {
      error,
      data: Room.fromApiObject(data),
      ...rest,
    };
  };
}
