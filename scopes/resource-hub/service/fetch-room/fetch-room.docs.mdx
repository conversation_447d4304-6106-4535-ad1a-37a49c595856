---
description: 'A fetchRoom HTTP API service.'
labels: ['service', 'http', fetch-room]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchRoom } from '@manyun/resource-hub.service.fetch-room';

const { error, data } = await fetchRoom('success');
const { error, data } = await fetchRoom('error');
```

### Node

```ts
import { fetchRoom } from '@resource-hub/service.fetch-room/dist/index.node';

const { data } = await fetchRoom('success');

try {
  const { data } = await fetchRoom('error');
} catch (error) {
  // ...
}
```
