/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-25
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type RoomInfo = {
  area: number;
  blockTag: string;
  floor: number;
  guid: string;
  id: number;
  idcName: string;
  idcTag: string;
  name: string;
  operationStatus: string;
  operationTime: string;
  ratedPower: number;
  refrigerationStructure: string;
  relateRoom: string;
  roomType: string;
  roomTypeName: string;
  tag: string;
};

export type SvcRespData = {
  data: RoomInfo[];
  total: number;
};

export type RequestRespData = {
  data: RoomInfo[] | null;
  total: number;
} | null;

export type ApiQ = {
  blockTag?: string;
  idcTag?: string;
  name?: string;
  operationStatus?: string;
  operationTimeStart?: string;
  operationTimeEnd?: string;
  pageNum: number;
  pageSize: number;
  roomTag?: string;
  roomType?: string;
  roomTypeList?: string[];
  content?: string;
};

export type ApiR = ListResponse<RoomInfo[] | null>;
