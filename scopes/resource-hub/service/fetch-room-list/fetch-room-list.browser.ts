/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-room-list';
import type { SvcQuery, SvcRespData } from './fetch-room-list.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchRoomList(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
