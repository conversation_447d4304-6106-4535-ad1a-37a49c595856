---
description: 'A fetchDeviceTaskList HTTP API service.'
labels: ['service', 'http', fetch-device-task-list]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchDeviceTaskList } from '@resource-hub/service.fetch-device-task-list';

const { error, data } = await fetchDeviceTaskList({});
const { error, data } = await fetchDeviceTaskList({});
```

### Node

```ts
import { fetchDeviceTaskList } from '@resource-hub/service.fetch-device-task-list/dist/index.node';

const { data } = await fetchDeviceTaskList({});

try {
  const { data } = await fetchDeviceTaskList({});
} catch (error) {
  // ...
}
```
