/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeviceTaskList as webService } from './fetch-device-task-list.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({
    spaceGuid: 'EC06.A.A1-1',
    taskTypeList: ['REPAIR'],
    taskStatusList: ['5', '1', '4', '2', '3'],
    limit: 10,
    deviceGuid: '1032323',
    sortField: 'END_TIME',
  });

  expect(data.data).toHaveLength(1);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    spaceGuid: 'EC06.A.A1-1',
    taskTypeList: ['REPAIR'],
    taskStatusList: ['5', '1', '4', '2', '3'],
    limit: 10,
    deviceGuid: '1010101',
    sortField: 'END_TIME',
  });

  expect(error?.code).toBe('error');
  expect(data.data).toStrictEqual([]);
});
