/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type DeviceTaskListModel = {
  id: number;
  // 工单号
  taskNo: string;
  // 创建时间
  gmtCreate: number;
  // 修改时间
  gmtModified: number;
  // 生效
  effectTime: number;
  // 工单类型
  taskType: TaskType;
  // 工单子类型
  taskSubType: string;
  // 工单标题
  taskTitle: string;
  // 工单处理人id
  taskAssignee: number;
  // 工单处理名称
  taskAssigneeName: string;
  // 工单状态
  taskStatus: TaskStatusStringInfo;
  // 关单时间
  endTime?: number;
};

export type SvcRespData = {
  data: DeviceTaskListModel[];
  total: number;
};

export type RequestRespData = {
  data: DeviceTaskListModel[] | null;
  total: number;
} | null;

/**工单类型*/
export enum TaskTypeInfo {
  /** 巡检 */
  'INSPECTION' = 'INSPECTION',
  /** 维修 */
  'REPAIR' = 'REPAIR',
  /** 维护 */
  'MAINTENANCE' = 'MAINTENANCE',
  /** 上下线 */
  'ON_OFF' = 'ON_OFF',
}
export type TaskType = keyof typeof TaskTypeInfo;
export const taskTypeMapper = {
  [TaskTypeInfo.INSPECTION]: '巡检',
  [TaskTypeInfo.REPAIR]: '维修',
  [TaskTypeInfo.MAINTENANCE]: '维护',
  [TaskTypeInfo.ON_OFF]: '上下线',
};

export enum TaskStatusInfo {
  /** 关单审批中 */
  'CLOSE_APPROVER' = '6',
  /**已撤销 */
  'CX' = '5',
  /**初始化|待审批 */
  'INIT' = '4',
  /** 待接手 */
  'WAITING' = '3',
  /** 处理中 */
  'PROCESSING' = '2',
  /** 已关单 */
  'END' = '1',
  /** 失败 */
  'FAILED' = '0',
}

export type TaskStatusMapperKey = keyof typeof TaskStatusInfo;

export type TaskStatusStringInfo = `${TaskStatusInfo}`;
export const taskStatusMapper: Record<string, string> = {
  [TaskStatusInfo.CX]: '已撤销',
  [TaskStatusInfo.INIT]: '初始化|待审批',
  [TaskStatusInfo.PROCESSING]: '处理中',
  [TaskStatusInfo.END]: '已关单',
  [TaskStatusInfo.WAITING]: '待接单',
  [TaskStatusInfo.FAILED]: '失败',
  [TaskStatusInfo.CLOSE_APPROVER]: '关单审批中',
};

export const taskStatusColorMapper: Record<TaskStatusStringInfo, string> = {
  [TaskStatusInfo.CX]: 'primary',
  [TaskStatusInfo.INIT]: 'warning',
  [TaskStatusInfo.PROCESSING]: 'processing',
  [TaskStatusInfo.END]: 'success',
  [TaskStatusInfo.WAITING]: 'warning',
  [TaskStatusInfo.FAILED]: 'error',
  [TaskStatusInfo.CLOSE_APPROVER]: 'processing',
};

export type ApiQ = {
  /** 包间guid，EC06.A.A1-1 */
  spaceGuid: string;
  /** 设备guid */
  deviceGuid: string;
  /** 类型list */
  taskTypeList: TaskType[];
  pageSize?: number;
  /**工单状态 */
  taskStatusList: TaskStatusStringInfo[];
  /** 排序字段，	关单时间:END_TIME :创建时间CREATE_TIME */
  sortField?: 'END_TIME' | 'CREATE_TIME';
  /** 不同工单请求限制 */
  limit: number;
};

export type ApiR = ListResponse<DeviceTaskListModel[] | null>;
