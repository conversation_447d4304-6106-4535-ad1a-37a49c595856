/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { Point } from '@manyun/monitoring.model.point';
import type { BackendPoint } from '@manyun/monitoring.model.point';

export type SvcRespData = {
  data: Point[];
  total: number;
};

export type RequestRespData = {
  data: BackendPoint[] | null;
  total: number;
} | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  deviceType: string;
};

export type ApiR = ListResponse<BackendPoint[] | null>;
