/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-stock-threshold';
import type { ApiQ, SvcRespData } from './delete-stock-threshold.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function deleteStockThreshold(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
