---
description: 'A deleteStockThreshold HTTP API service.'
labels: ['service', 'http', delete-stock-threshold]
---

## 概述

删除阈值信息

## 使用

### Browser

```ts
import { deleteStockThreshold } from '@manyun/redash.service.delete-stock-threshold';

const { error, data } = await deleteStockThreshold(id:123);
```

### Node

```ts
import { deleteStockThreshold } from '@manyun/redash.service.delete-stock-threshold/dist/index.node';

const { data } = await deleteStockThreshold(id:123);

```
