/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-2-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './delete-stock-threshold.type';

const endpoint = '/dccm/inventory/warn/delete';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/102/interface/api/3800)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: false,
      };
    }

    return { error, data: true, ...rest };
  };
}
