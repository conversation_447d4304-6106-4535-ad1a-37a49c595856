/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-21
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type PointInstance = {
  id: string;
  name: string;
  pointCode: string;
  pointId: number;
  targetGuid: string;
  targetName: string;
  dataType: string;
  description: string;
  deviceType: string;
  dimension: string;
  formula: string;
  formulaJson: string;
  pointType: string;
  precision: number;
  unit: string;
  validLimits: Record<string, number>;
};

export type SvcRespData = {
  data: PointInstance[];
  total: number;
};

export type RequestRespData = {
  data: PointInstance[] | null;
  total: number;
} | null;

export type ApiQ = {
  id: number;
};

export type ApiR = ListResponse<PointInstance[] | null>;
