/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-28
 *
 * @packageDocumentation
 */
import { Point } from '@manyun/monitoring.model.point';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-point.type';

const endpoint = '/dccm/point/batch/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/136/interface/api/8392)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...variant };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(Point.fromApiObject), total: data.total },
      ...rest,
    };
  };
}
