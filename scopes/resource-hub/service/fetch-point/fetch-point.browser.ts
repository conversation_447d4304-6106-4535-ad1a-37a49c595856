/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-28
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-point';
import type { SvcQuery, SvcRespData } from './fetch-point.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPoint(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
