/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-5-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPoint, Point } from '@manyun/monitoring.model.point';

export type SvcQuery = {
  devicePointList: string[];
  blockGuidList?: string[];
};

export type SvcRespData = {
  data: Point[];
  total: number;
};

export type RequestRespData = {
  data: BackendPoint[] | null;
  total: number;
} | null;

export type ApiQ = {
  devicePointList: string[];
};

export type ApiR = ListResponse<BackendPoint[] | null>;
