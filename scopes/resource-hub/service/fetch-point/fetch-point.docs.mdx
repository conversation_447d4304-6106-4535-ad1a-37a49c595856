---
description: 'A fetchPoint HTTP API service.'
labels: ['service', 'http', fetch-point]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchPoint } from '@resource-hub/fetch-point';

const { error, data } = await fetchPoint('success');
const { error, data } = await fetchPoint('error');
```

### Node

```ts
import { fetchPoint } from '@resource-hub/fetch-point/dist/index.node';

const { data } = await fetchPoint('success');

try {
  const { data } = await fetchPoint('error');
} catch(error) {
  // ...
}
```
