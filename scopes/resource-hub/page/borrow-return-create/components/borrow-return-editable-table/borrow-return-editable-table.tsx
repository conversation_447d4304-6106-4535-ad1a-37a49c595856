import React, { useState } from 'react';

import shortid from 'shortid';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import type { EditableFormInstance, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ModelSelect } from '@manyun/crm.ui.model-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import type { AssetData } from '../../borrow-return-create';
import { ApplyNumInput } from './components/apply-num-input';
import { AssetDeviceTypeSelect } from './components/asset-device-type-select';

export type BorrowReturnEditableTableProps = {
  assetData: AssetData[];
  editableKeys: React.Key[];
  editableFormRef: React.MutableRefObject<EditableFormInstance<AssetData> | undefined>;
  blockGuid: string;
  onSetEditableRowKeys: (editableKeys: React.Key[]) => void;
  onSetAssetDataSource: (datasource: AssetData[]) => void;
  onClear: () => void;
};

export const BorrowReturnEditableTable = ({
  assetData,
  editableKeys,
  onSetAssetDataSource,
  editableFormRef,
  blockGuid,
  onSetEditableRowKeys,
  onClear,
}: BorrowReturnEditableTableProps) => {
  const [savedRowKeys, setSavedRowKeys] = useState<string[]>([]);

  const columns: ProColumns<AssetData>[] = [
    {
      title: '物资分类',
      dataIndex: 'assetAndDeviceType',
      fixed: 'left',
      renderFormItem: (_, { recordKey }) => (
        <AssetDeviceTypeSelect
          style={{ width: 296 }}
          onChange={() => {
            if (recordKey) {
              editableFormRef.current?.setRowData?.(recordKey as string, {
                vendor: undefined,
                productModel: undefined,
                roomGuid: undefined,
                applyNum: undefined,
              });
            }
          }}
        />
      ),
      render: (_, record) => {
        if (record?.assetAndDeviceType?.assetType && record?.assetAndDeviceType?.deviceType) {
          if (record.assetAndDeviceType.assetType === 'ON_LINE') {
            return (
              <>
                线上物资/
                <DeviceTypeText code={record.assetAndDeviceType.deviceType} />
              </>
            );
          } else {
            return `线下物资/${record.assetAndDeviceType.deviceType}`;
          }
        }
        return '--';
      },
      formItemProps: {
        rules: [
          {
            whitespace: true,
            validator: async (_, val) => {
              if (!val?.assetType || !val?.deviceType) {
                return Promise.reject(new Error('物资分类必填'));
              }
              return Promise.resolve();
            },
          },
        ],
      },
    },
    {
      title: '品牌',
      dataIndex: 'vendor',
      renderFormItem: (_, { recordKey, record }) => {
        if (record?.assetAndDeviceType?.assetType === 'ON_LINE') {
          return (
            <VendorSelect
              style={{ width: 220 }}
              allowClear
              deviceType={record?.assetAndDeviceType?.deviceType}
              disabled={!record?.assetAndDeviceType?.deviceType}
              onChange={() => {
                if (recordKey) {
                  editableFormRef.current?.setRowData?.(recordKey.toString(), {
                    productModel: undefined,
                    applyNum: undefined,
                  });
                }
              }}
            />
          );
        }

        return (
          <Input
            style={{ width: 220 }}
            disabled={record?.assetAndDeviceType?.assetType !== 'OFF_LINE'}
          />
        );
      },
      render: (_, record) => {
        return record.vendor ?? '--';
      },
      formItemProps: (form, config) => {
        return {
          rules: [
            {
              validator: (_, val) => {
                const rowKey = config.rowKey ? config.rowKey[0] : null;
                const fileds = rowKey ? form.getFieldsValue()[rowKey] : {};
                if (fileds.assetAndDeviceType.assetType === 'ON_LINE' && !val) {
                  return Promise.reject('品牌必填');
                }
                return Promise.resolve();
              },
            },
            {
              max: 32,
              message: '最大字符数32!',
            },
          ],
        };
      },
    },
    {
      title: '型号',
      dataIndex: 'productModel',
      renderFormItem: (_, { record, recordKey }) => {
        if (record?.assetAndDeviceType?.assetType === 'ON_LINE') {
          return (
            <ModelSelect
              style={{ width: 220 }}
              deviceTypes={
                record.assetAndDeviceType?.deviceType
                  ? [record.assetAndDeviceType.deviceType]
                  : undefined
              }
              vendorCode={record.vendor}
              disabled={!(record.assetAndDeviceType?.deviceType && record?.vendor)}
              allowClear
              onChange={() => {
                if (recordKey) {
                  editableFormRef.current?.setRowData?.(recordKey.toString(), {
                    applyNum: undefined,
                  });
                }
              }}
            />
          );
        }

        return (
          <Input
            style={{ width: 220 }}
            disabled={record?.assetAndDeviceType?.assetType !== 'OFF_LINE'}
          />
        );
      },
      render: (_, record) => {
        return record.productModel ?? '--';
      },
      formItemProps: (form, config) => {
        return {
          rules: [
            {
              validator: (_, val) => {
                const rowKey = config.rowKey ? config.rowKey[0] : null;
                const fileds = rowKey ? form.getFieldsValue()[rowKey] : {};
                if (fileds.assetAndDeviceType?.assetType === 'ON_LINE' && !val) {
                  return Promise.reject('型号必填');
                }
                return Promise.resolve();
              },
            },
            {
              max: 32,
              message: '最大字符数32!',
            },
          ],
        };
      },
    },
    {
      title: '借用至目的位置',
      dataIndex: 'roomGuid',
      renderFormItem: () => <Input style={{ width: 220 }} />,
      render: (_, record) => {
        return record.roomGuid ?? '--';
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '位置必填',
          },
          {
            max: 12,
            message: '最大字符数12!',
          },
        ],
      },
    },
    {
      title: '申请借用数量',
      dataIndex: 'applyNum',
      renderFormItem: (_, { record }) => {
        return (
          <ApplyNumInput
            blockGuid={blockGuid}
            disabled={
              !(
                record?.assetAndDeviceType?.deviceType &&
                record?.vendor &&
                record?.productModel &&
                record?.roomGuid
              )
            }
            assetData={record}
          />
        );
      },
      formItemProps: {
        rules: [
          {
            required: true,
            message: '申请借用数量必填',
          },
          {
            type: 'number',
            min: 1,
            message: '申请数量最小值为1',
          },
          {
            type: 'number',
            max: 999999,
            message: '申请数量最大999999！',
          },
        ],
      },
    },
    {
      title: '操作',
      valueType: 'option',
      fixed: 'right',
      render: (_, record, __, action) => [
        <Typography.Link
          key="editable"
          onClick={() => {
            action?.startEditable?.(record.id);
          }}
        >
          编辑
        </Typography.Link>,
        <Typography.Link
          key="delete"
          onClick={() => {
            onSetAssetDataSource(assetData.filter(item => item.id !== record.id));
          }}
        >
          删除
        </Typography.Link>,
      ],
    },
  ];

  return (
    <Card
      title={
        <Typography.Title showBadge level={5}>
          借用信息
        </Typography.Title>
      }
    >
      <Space style={{ width: '100%' }} direction="vertical">
        <Button onClick={onClear}>全部移除</Button>
        <EditableProTable<AssetData>
          rowKey="id"
          scroll={{ x: 'max-content' }}
          recordCreatorProps={{
            position: 'top',
            record: () => ({ id: shortid() }),
            newRecordType: 'dataSource',
            creatorButtonText: '添加物资',
            disabled: !blockGuid,
          }}
          editableFormRef={editableFormRef}
          columns={columns}
          value={assetData}
          editable={{
            type: 'multiple',
            editableKeys,
            onChange: onSetEditableRowKeys,
            onSave: (_, __, row) => {
              setSavedRowKeys([...savedRowKeys, row.id]);
              return Promise.resolve();
            },
            onCancel: (_, __, row) => {
              if (!savedRowKeys.includes(row.id)) {
                onSetAssetDataSource(assetData.filter(item => item.id !== row.id));
              }
              return Promise.resolve();
            },
            actionRender: (_, __, dom) => [dom.save, dom.cancel],
          }}
          onChange={onSetAssetDataSource}
        />
      </Space>
    </Card>
  );
};
