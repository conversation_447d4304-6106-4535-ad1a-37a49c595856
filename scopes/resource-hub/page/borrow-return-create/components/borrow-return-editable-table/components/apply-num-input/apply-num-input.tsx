import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useShallowCompareEffect } from 'react-use';

import type { InputNumberProps } from '@manyun/base-ui.ui.input-number';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getDeviceTypeMetaData } from '@manyun/dc-brain.state.common';
import { fetchDeviceNum } from '@manyun/resource-hub.service.fetch-device-num';
import { fetchSpareNum } from '@manyun/resource-hub.service.fetch-spare-num';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { AssetData } from '../../../../borrow-return-create';

export type ApplyNumInputProps = Omit<InputNumberProps, 'min' | 'max' | 'precision'> & {
  blockGuid: string;
  assetData?: AssetData;
};

export const ApplyNumInput = ({ blockGuid, assetData, ...reset }: ApplyNumInputProps) => {
  const deviceCategory = useSelector(getDeviceTypeMetaData);
  const deviceNormalizedList = deviceCategory ? deviceCategory.normalizedList : {};

  const [count, setCount] = useState(0);

  useShallowCompareEffect(() => {
    setCount(0);
    const isDisabled =
      assetData &&
      assetData.vendor &&
      assetData.productModel &&
      assetData.assetAndDeviceType?.assetType === 'ON_LINE';

    const { idc, block } = getSpaceGuidMap(blockGuid);
    if (
      isDisabled &&
      deviceNormalizedList[assetData.assetAndDeviceType.deviceType]?.numbered &&
      idc &&
      block
    ) {
      (async () => {
        const { data, error } = await fetchDeviceNum({
          idcTag: idc,
          blockTag: block,
          deviceType: assetData.assetAndDeviceType.deviceType,
          vendor: assetData.vendor,
          productModel: assetData.productModel,
          roomTypeList: ['WAREHOUSE'],
          assetStatus: 'NORMAL',
        });

        if (error) {
          message.error(error);
          return;
        }
        setCount(data);
      })();
    }
    if (
      isDisabled &&
      !deviceNormalizedList[assetData.assetAndDeviceType.deviceType].numbered &&
      blockGuid
    ) {
      (async () => {
        const { data, error } = await fetchSpareNum({
          spareType: assetData.assetAndDeviceType.deviceType,
          blockGuidList: [blockGuid],
          vendor: assetData.vendor,
          productModel: assetData.productModel,
        });

        if (error) {
          message.error(error);
          return;
        }
        setCount(data);
      })();
    }
  }, [assetData, blockGuid, deviceNormalizedList]);

  const isOnLine = assetData?.assetAndDeviceType?.assetType === 'ON_LINE';
  return (
    <Space style={{ display: 'flex' }}>
      <InputNumber style={{ width: 220 }} {...reset} precision={0} min={1} />
      {isOnLine && <Typography.Text>库存数量: {count}</Typography.Text>}
    </Space>
  );
};
