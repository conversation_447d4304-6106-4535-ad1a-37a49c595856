import React, { useState } from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';

export type AssetDeviceTypeSelectProps = Omit<SelectProps, 'options' | 'onChange'> & {
  onChange?: (value: { assetType?: string; deviceType?: string }) => void;
};

type Asset = 'ON_LINE' | 'OFF_LINE';

const options: { value: Asset; label: string }[] = [
  { value: 'ON_LINE', label: '线上物资' },
  { value: 'OFF_LINE', label: '线下物资' },
];

export const AssetDeviceTypeSelect = React.forwardRef(
  (props: AssetDeviceTypeSelectProps, ref: React.Ref<RefSelectProps>) => {
    const _defaultAssetValue = props?.value?.assetType || props.defaultValue?.assetType;
    const _defaultDeviceTypeValue = props?.value?.deviceType || props?.defaultValue?.deviceType;

    const [assetType, setAssetType] = useState<Asset>(_defaultAssetValue);
    const [deviceType, setDeviceType] = useState<string | undefined>(_defaultDeviceTypeValue);

    return (
      <div style={{ display: 'flex' }}>
        <Select
          ref={ref}
          style={{ width: 120 }}
          value={assetType}
          options={options}
          onChange={value => {
            setAssetType(value);
            setDeviceType(undefined);
            props?.onChange && props.onChange({ assetType: value, deviceType: undefined });
          }}
        />
        {assetType === 'ON_LINE' && (
          <DeviceTypeCascader
            style={{ width: 200 }}
            dataType={['noSnDevice', 'snDevice']}
            disabledTypeList={['C0', 'C1']}
            allowClear
            value={deviceType}
            onChange={value => {
              setDeviceType(value);
              props?.onChange && props.onChange({ assetType, deviceType: value });
            }}
          />
        )}
        {assetType === 'OFF_LINE' && (
          <Input
            style={{ width: 200 }}
            maxLength={16}
            value={deviceType}
            onChange={e => {
              const deviceType = e.target.value;
              setDeviceType(deviceType);
              props?.onChange && props.onChange({ assetType, deviceType });
            }}
          />
        )}
      </div>
    );
  }
);

AssetDeviceTypeSelect.displayName = 'AssetDeviceTypeSelect';
