import React, { useEffect, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import cloneDeep from 'lodash.clonedeep';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { EditableFormInstance } from '@manyun/base-ui.ui.editable-pro-table';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { getDeviceTypeMetaData, syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { BORROWS_AND_RETURN_LIST } from '@manyun/resource-hub.route.resource-routes';
import type { SvcQuery as CreateborrowParams } from '@manyun/resource-hub.service.create-borrow';
import { createBorrow } from '@manyun/resource-hub.service.create-borrow';
import type { SvcQuery as CreateBorrowResubmitParams } from '@manyun/resource-hub.service.create-borrow-resubmit';
import { createBorrowResubmit } from '@manyun/resource-hub.service.create-borrow-resubmit';
import { fetchBorrowApplys } from '@manyun/resource-hub.service.fetch-borrow-applys';
import type { BorrowReturnDetail } from '@manyun/resource-hub.service.fetch-borrow-info';
import { fetchBorrowInfo } from '@manyun/resource-hub.service.fetch-borrow-info';
import { BorrowerTypeSelect } from '@manyun/resource-hub.ui.borrower-type-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { BorrowReturnEditableTable } from './components/borrow-return-editable-table';

export type AssetData = {
  id: string;
  assetAndDeviceType: {
    assetType: 'OFF_LINE' | 'ON_LINE';
    deviceType: string;
  };
  vendor: string;
  productModel: string;
  roomGuid: string;
  applyNum: number;
};

export function BorrowReturnCreate() {
  const history = useHistory();
  const dispatch = useDispatch();
  const deviceCategory = useSelector(getDeviceTypeMetaData);
  const deviceNormalizedList = deviceCategory ? deviceCategory.normalizedList : null;

  const [form] = Form.useForm();
  const { validateFields } = form;

  const { id } = useParams<{ id: string }>();

  const [isOutBorrowType, setIsOutBorrowType] = useState(false);
  const [blockGuid, setBlockGuid] = useState<string>('');

  const [assetData, setAssetDataSource] = useState<AssetData[]>([]);

  const [borrowReturnDetail, setBorrowReturnDetail] = useState<BorrowReturnDetail>();

  const [editableKeys, setEditableRowKeys] = useState<React.Key[]>([]);

  const [loading, setLoading] = useState(false);

  const editableFormRef = useRef<EditableFormInstance>();

  const { setFieldsValue } = form;

  useEffect(() => {
    (async () => {
      if (!id) {
        return;
      }
      const { error, data } = await fetchBorrowInfo({ borrowNo: id });
      const { error: borrowApplyError, data: borrowApplyData } = await fetchBorrowApplys({
        borrowNo: id,
      });
      if (borrowApplyError) {
        message.error(borrowApplyError.message);
        return;
      }
      if (error) {
        message.error(error.message);
        return;
      }
      if (!data) {
        return;
      }
      setBorrowReturnDetail(data);
      setBlockGuid(data.source.blockGuid);
      setFieldsValue({
        title: data.title,
        blockGuid: data.source.blockGuid,
        borrowType: data.borrowType,
        borrower: data.borrower.name,
        borrowingDate:
          data.borrowStartAt && data.borrowEndAt
            ? [moment(data.borrowStartAt), moment(data.borrowEndAt)]
            : [],
        reason: data.reason,
        fileInfoList: data.fileInfoList,
        borrowerType: data.borrower.type,
        personLiable: data.personLiable.name,
      });

      setIsOutBorrowType(data.borrowType === 'OUT');

      setAssetDataSource(
        borrowApplyData.data.map(item => {
          return {
            id: String(item.id),
            assetAndDeviceType: {
              assetType: item.assetType,
              deviceType: item.deviceType,
            },
            vendor: item.vendor,
            productModel: item.productModel,
            roomGuid: item.targetRoomGuid,
            applyNum: item.applyNum,
          };
        })
      );
    })();
  }, [id, setFieldsValue]);

  useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { deviceCategory: 'IF_NULL' } }));
  }, [dispatch]);

  const _onClickSubmit = async () => {
    if (editableKeys?.length) {
      message.error('请先保存借用信息后再提交');
      return;
    }

    try {
      await editableFormRef.current?.validateFields();
    } catch (error) {
      console.error(error);
      return;
    }

    const cloneAssetData = cloneDeep(assetData).filter(item => item.assetAndDeviceType);
    if (!cloneAssetData.length) {
      message.error('借用信息必选！');
      return;
    }

    const values = await validateFields();

    const {
      title,
      blockGuid,
      borrowType,
      borrower,
      borrowerType,
      personLiable,
      borrowingDate,
      reason,
      fileInfoList,
    } = values;

    const borrowAssetInfoList = cloneAssetData.map(item => {
      const isOnLine = item.assetAndDeviceType?.assetType === 'ON_LINE';
      const { room } = getSpaceGuidMap(item.roomGuid!);
      return {
        assetType: item.assetAndDeviceType?.assetType,
        deviceType: item.assetAndDeviceType?.deviceType,
        vendor: item.vendor,
        productModel: item.productModel,
        targetRoomGuid: item.roomGuid,
        targetRoomTag: room!,
        applyNum: item.applyNum,
        numbered: isOnLine
          ? deviceNormalizedList[item.assetAndDeviceType?.deviceType].numbered
          : false,
      };
    });

    const borrowerId = borrower.id;
    const borrowerName = borrower.name ?? borrower.label;

    let params: CreateborrowParams = {
      title,
      idcTag: getSpaceGuidMap(blockGuid).idc!,
      blockGuid,
      borrowType,
      borrower: borrowerId,
      borrowerName: borrowerName,
      borrowStartDate: borrowingDate[0].millisecond(0).valueOf(),
      borrowEndDate: borrowingDate[1].millisecond(999).valueOf(),
      reason,
      fileInfoList: fileInfoList
        ? fileInfoList?.map((file: McUploadFileJSON) => McUploadFile.fromJSON(file).toApiObject())
        : [],
      borrowAssetInfoList,
    };

    if (borrowType === 'OUT') {
      params = {
        ...params,
        borrowerType,
        personLiable: personLiable.id,
        personLiableName: personLiable.name,
      };
    }
    setLoading(true);
    if (id) {
      let createBorrowResubmitParams: CreateBorrowResubmitParams = {
        ...params,
        borrowNo: id,
        borrower: borrowerId ?? borrowReturnDetail?.borrower.id,
        borrowerName: borrowerName ?? borrowReturnDetail?.borrower.name,
      };
      if (borrowType === 'OUT') {
        createBorrowResubmitParams = {
          ...createBorrowResubmitParams,
          personLiable: personLiable.id ?? borrowReturnDetail?.personLiable.id,
          personLiableName: personLiable.name ?? borrowReturnDetail?.personLiable.name,
        };
      }
      const { error } = await createBorrowResubmit(createBorrowResubmitParams);
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('重新发起成功！');
      history.push(BORROWS_AND_RETURN_LIST);
      return;
    }
    const { error } = await createBorrow(params);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('创建成功！');
    history.push(BORROWS_AND_RETURN_LIST);
  };

  const onClearEditableTable = () => {
    setAssetDataSource([]);
    setEditableRowKeys([]);
  };

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: 8, paddingBottom: 36 }}>
      <Card
        title={
          <Typography.Title showBadge level={5}>
            基本信息
          </Typography.Title>
        }
      >
        <Form
          form={form}
          labelCol={{ xl: 2 }}
          wrapperCol={{ xl: 22 }}
          initialValues={{ borrowType: 'INNER' }}
        >
          <Form.Item
            label="标题"
            name="title"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '标题必填！',
              },
              {
                max: 20,
                message: '最多输入 20 个字符！',
              },
            ]}
          >
            <Input style={{ width: 216 }} allowClear />
          </Form.Item>
          <Form.Item
            label="位置"
            name="blockGuid"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '位置必选！',
              },
            ]}
          >
            <LocationTreeSelect
              style={{ width: 216 }}
              allowClear
              authorizedOnly
              disabledTypes={['IDC']}
              onChange={value => {
                setBlockGuid(value as string);
                onClearEditableTable();
              }}
            />
          </Form.Item>
          <Form.Item label="借用类型" name="borrowType">
            <Radio.Group
              onChange={({ target: { value } }) => {
                setFieldsValue({ borrower: undefined });
                setIsOutBorrowType(value === 'OUT');
              }}
            >
              <Radio key="INNER" value="INNER">
                内部员工
              </Radio>
              <Radio key="OUT" value="OUT">
                外部人员
              </Radio>
            </Radio.Group>
          </Form.Item>
          {isOutBorrowType && (
            <Form.Item
              label="借用人类型"
              name="borrowerType"
              rules={[
                {
                  required: true,
                  message: '借用人类型必选',
                },
              ]}
            >
              <BorrowerTypeSelect style={{ width: 216 }} showSearch />
            </Form.Item>
          )}
          <Form.Item
            label="借用人"
            name="borrower"
            rules={[
              {
                required: true,
                message: '借用人必选',
              },
            ]}
          >
            <UserSelect
              style={{ width: 216 }}
              userState="in-service"
              allowClear
              reserveSearchValue={isOutBorrowType}
            />
          </Form.Item>
          {isOutBorrowType && (
            <Form.Item
              label="负责人"
              name="personLiable"
              rules={[
                {
                  required: true,
                  message: '负责人必选',
                },
              ]}
            >
              <UserSelect style={{ width: 216 }} userState="in-service" allowClear />
            </Form.Item>
          )}
          <Form.Item
            label="借用起止日期"
            name="borrowingDate"
            rules={[
              {
                required: true,
                message: '借用起止日期必选',
              },
            ]}
          >
            <DatePicker.RangePicker
              style={{ width: 395 }}
              format="YYYY-MM-DD"
              disabledDate={current => current < moment().startOf('day')}
            />
          </Form.Item>
          <Form.Item
            label="原因"
            name="reason"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '原因必填',
              },
              {
                max: 120,
                message: '最多输入 120 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 395 }} />
          </Form.Item>
          <Form.Item
            label="附件"
            name="fileInfoList"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <McUpload key="upload" accept="image/*,.xls,.xlsx" maxFileSize={20}>
              <Button icon={<UploadOutlined />}>上传</Button>
              <Typography.Text type="secondary">支持扩展名：image/*,.xls,.xlsx</Typography.Text>
            </McUpload>
          </Form.Item>
        </Form>
      </Card>
      <BorrowReturnEditableTable
        assetData={assetData}
        editableFormRef={editableFormRef}
        blockGuid={blockGuid}
        editableKeys={editableKeys}
        onSetEditableRowKeys={setEditableRowKeys}
        onSetAssetDataSource={setAssetDataSource}
        onClear={onClearEditableTable}
      />
      <FooterToolBar>
        <Space size={16}>
          <Button type="primary" loading={loading} onClick={_onClickSubmit}>
            提交
          </Button>
          <Button
            type="default"
            onClick={() => {
              history.push(BORROWS_AND_RETURN_LIST);
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </div>
  );
}
