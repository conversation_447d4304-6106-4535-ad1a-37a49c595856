import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import type { FloorJSON, FloorLocales } from '@manyun/resource-hub.model.floor';
import { getFloorLocales } from '@manyun/resource-hub.model.floor';
import { generateGraphixFloorViewRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { FloorTypeText } from '@manyun/resource-hub.ui.floor-type-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

const getLocalAlarmRuleColumns = (locales: FloorLocales) => {
  const localAlarmRuleColumns: ColumnType<FloorJSON>[] = [
    {
      title: locales['idcName'],
      dataIndex: 'idcName',
      fixed: 'left',
    },
    {
      title: locales['idcTag'],
      dataIndex: 'idcTag',
    },
    {
      title: locales['blockGuid'],
      dataIndex: 'blockGuid',
    },
    {
      title: locales['floorTag'],
      dataIndex: 'floorTag',
      render: (floorTag, record) => (
        <Button
          type="link"
          compact
          onClick={() => {
            window.open(
              generateGraphixFloorViewRoutePath({
                idc: record.idcTag,
                block: getSpaceGuidMap(record.blockGuid).block!,
                floor: record.floorTag,
              })
            );
          }}
        >
          {floorTag}
        </Button>
      ),
    },
    {
      title: locales['floorName'],
      dataIndex: 'floorName',
      render: (floorName, record) => (
        <Button
          type="link"
          compact
          onClick={() => {
            window.open(
              generateGraphixFloorViewRoutePath({
                idc: record.idcTag,
                block: getSpaceGuidMap(record.blockGuid).block!,
                floor: record.floorTag,
              })
            );
          }}
        >
          {floorName}
        </Button>
      ),
    },
    {
      title: locales['floorType'],
      dataIndex: 'floorType',
      render: floorType => <FloorTypeText code={floorType} />,
    },
    {
      title: locales['operationStatus'],
      dataIndex: 'operationStatus',
      render: operationStatus => (operationStatus === 'ON' ? '启用' : '未启用'),
    },
  ];

  return localAlarmRuleColumns;
};

export type ColumnDataIndex =
  | 'idcName'
  | 'idcTag'
  | 'blockGuid'
  | 'floorTag'
  | 'floorName'
  | 'floorType'
  | 'operationStatus';

export type FloorManagementTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<FloorJSON>;
} & Omit<TableProps<FloorJSON>, 'columns'>;

export function FloorManagementTable({
  dataIndexs = [
    'idcName',
    'idcTag',
    'blockGuid',
    'floorTag',
    'floorName',
    'floorType',
    'operationStatus',
  ],
  operation,
  ...rest
}: FloorManagementTableProps) {
  const columns = useDeepCompareMemo(() => {
    const locales = getFloorLocales();
    const newColumns = dataIndexs
      .map(dataIndex => {
        return getLocalAlarmRuleColumns(locales).find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<FloorJSON> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      rowKey="id"
      {...rest}
    />
  );
}
