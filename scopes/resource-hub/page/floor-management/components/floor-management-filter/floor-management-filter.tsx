import React, { useMemo } from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';

import type { SvcQuery as FloorParams } from '@manyun/resource-hub.service.fetch-floors';
import { FloorStatusSelect } from '@manyun/resource-hub.ui.floor-status-select';
import { FloorTypeSelect } from '@manyun/resource-hub.ui.floor-type-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

type FormParams = Omit<FloorParams, 'idcTag' | 'pageSize' | 'pageNum'>;

export type FloorManagementFilterProps = { onChange: (params: FormParams) => void };

export function FloorManagementFilter({ onChange }: FloorManagementFilterProps) {
  const [form] = Form.useForm();

  const items = useMemo(
    () =>
      [
        {
          label: '机房楼栋',
          name: 'blockGuidList',
          control: (
            <LocationTreeSelect
              style={{ width: 216 }}
              authorizedOnly
              allowClear
              disabledTypes={['IDC']}
              multiple
            />
          ),
        },
        {
          label: '楼层名称',
          name: 'name',
          control: <Input style={{ width: 216 }} allowClear />,
        },
        {
          label: '楼层类型',
          name: 'floorType',
          control: <FloorTypeSelect style={{ width: 216 }} allowClear />,
        },
        {
          label: '启用状态',
          name: 'operationStatus',
          control: <FloorStatusSelect style={{ width: 216 }} allowClear />,
        },
      ].filter(Boolean),
    []
  );

  return (
    <QueryFilter
      form={form}
      items={items}
      onSearch={onChange}
      onReset={() => {
        onChange({});
        form.resetFields();
      }}
    />
  );
}
