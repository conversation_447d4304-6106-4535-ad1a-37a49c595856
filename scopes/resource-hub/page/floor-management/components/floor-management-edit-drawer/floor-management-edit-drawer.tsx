import React from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import type { FloorJSON } from '@manyun/resource-hub.model.floor';
import { createFloor } from '@manyun/resource-hub.service.create-floor';
import { updateFloor } from '@manyun/resource-hub.service.update-floor';
import { FloorStatusSelect } from '@manyun/resource-hub.ui.floor-status-select';
import { FloorTypeSelect } from '@manyun/resource-hub.ui.floor-type-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type FloorManagementEditDrawerProps = {
  open: boolean;
  floor?: FloorJSON;
  onClose: () => void;
  onRefresh: (pageNum?: number) => void;
};

export function FloorManagementEditDrawer({
  open,
  floor,
  onClose,
  onRefresh,
}: FloorManagementEditDrawerProps) {
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;

  useShallowCompareEffect(() => {
    if (floor && open) {
      const { blockGuid, floorTag, floorName, operationStatus, floorType } = floor;
      setFieldsValue({ blockGuid, floorTag, floorName, operationStatus, floorType });
    } else {
      setFieldsValue({
        blockGuid: undefined,
        floorTag: undefined,
        floorName: undefined,
        operationStatus: undefined,
        floorType: undefined,
      });
    }
  }, [floor, open, setFieldsValue]);

  const onFinish = async () => {
    const values = await validateFields();
    const { blockGuid, floorName, floorTag, floorType, operationStatus } = values;

    if (floor) {
      const { error } = await updateFloor({
        id: floor.id,
        name: floorName,
        tag: floorTag,
        floorType,
        operationStatus,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      onRefresh();
      message.success('编辑楼层成功!');
    } else {
      const { idc } = getSpaceGuidMap(blockGuid);
      const { error } = await createFloor({
        idcTag: idc!,
        blockGuid,
        name: floorName,
        tag: floorTag,
        floorType,
        operationStatus,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      onRefresh(1);
      message.success('新建楼层成功!');
    }

    onClose();
  };

  return (
    <Drawer
      title={floor ? '编辑楼层' : '新建楼层'}
      open={open}
      extra={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={onFinish}>
            提交
          </Button>
        </Space>
      }
      onClose={onClose}
    >
      <Form layout="vertical" form={form}>
        <Form.Item
          label="位置"
          name="blockGuid"
          rules={[{ required: true, message: '请选择位置!' }]}
        >
          <LocationTreeSelect
            style={{ width: 224 }}
            authorizedOnly
            allowClear
            disabled={!!floor}
            disabledTypes={['IDC']}
          />
        </Form.Item>
        <Form.Item
          label="楼层编号"
          name="floorTag"
          rules={[
            { required: true, message: '请输入楼层编号!' },
            {
              validator: (_, val) => {
                if (Number(val) < -99999999 || Number(val) > 99999999) {
                  return Promise.reject(new Error('请输入-99999999至99999999之间整数!'));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <InputNumber style={{ width: 224 }} disabled={!!floor} precision={0} />
        </Form.Item>
        <Form.Item
          label="楼层名称"
          name="floorName"
          rules={[
            { required: true, message: '请选择楼层名称!' },
            {
              max: 16,
              message: '最多输入 16 个字符！',
            },
          ]}
        >
          <Input style={{ width: 224 }} allowClear />
        </Form.Item>
        <Form.Item
          label="楼层类型"
          name="floorType"
          rules={[{ required: true, message: '请选择楼层类型!' }]}
        >
          <FloorTypeSelect style={{ width: 224 }} allowClear />
        </Form.Item>
        <Form.Item
          label="状态"
          name="operationStatus"
          rules={[{ required: true, message: '请选择状态!' }]}
        >
          <FloorStatusSelect style={{ width: 224 }} />
        </Form.Item>
      </Form>
    </Drawer>
  );
}
