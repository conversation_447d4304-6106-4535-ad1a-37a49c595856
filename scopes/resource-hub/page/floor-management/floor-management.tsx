import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import type { FloorJSON } from '@manyun/resource-hub.model.floor';
import { generateEditGraphixFloorRoutePath } from '@manyun/resource-hub.route.resource-routes';
import { deleteFloor } from '@manyun/resource-hub.service.delete-floor';
import type { SvcQuery as FloorParams } from '@manyun/resource-hub.service.fetch-floors';
import { fetchFloors } from '@manyun/resource-hub.service.fetch-floors';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { FloorManagementEditDrawer } from './components/floor-management-edit-drawer';
import { FloorManagementFilter } from './components/floor-management-filter';
import type { FloorManagementTableProps } from './components/floor-management-table';
import { FloorManagementTable } from './components/floor-management-table';

type FormParams = Omit<FloorParams, 'idcTag' | 'pageSize' | 'pageNum'>;

export function FloorManagement() {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [floors, setFloors] = useState<FloorJSON[]>([]);

  const [selectdFloor, setSelectdFloor] = useState<FloorJSON>();

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const filterParamsRef = useRef<FormParams | undefined>();

  const getFloors = async ({ pageNum, pageSize }: { pageNum: number; pageSize: number }) => {
    setLoading(true);
    const { data, error } = await fetchFloors({
      pageNum: pageNum,
      pageSize: pageSize,
      ...filterParamsRef.current,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setPagination({ pageNum, pageSize, total: data.total });
    setFloors(data.data);
  };

  const onRefresh = (pageNum?: number) => {
    getFloors({ pageNum: pageNum ?? pagination.pageNum, pageSize: pagination.pageSize });
  };

  useEffect(() => {
    onRefresh();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onFilterChange = (params: FormParams) => {
    filterParamsRef.current = params;
    getFloors({ pageNum: 1, pageSize: pagination.pageSize });
  };

  const onDelete = useCallback(
    async (id: string) => {
      const { error } = await deleteFloor({ id });
      if (error) {
        message.error(error.message);
        return;
      }
      getFloors({ pageNum: pagination.pageNum, pageSize: pagination.pageSize });
    },
    [pagination.pageNum, pagination.pageSize]
  );

  const operation: FloorManagementTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space split={<Divider type="vertical" />}>
            <Button
              type="link"
              compact
              onClick={() => {
                window.open(
                  generateEditGraphixFloorRoutePath({
                    idc: record.idcTag,
                    block: getSpaceGuidMap(record.blockGuid).block!,
                    floor: record.floorTag,
                    mode: 'new',
                  })
                );
              }}
            >
              楼层视图
            </Button>
            <Button
              type="link"
              compact
              onClick={() => {
                setSelectdFloor(record);
                setOpen(true);
              }}
            >
              编辑
            </Button>
            <DeleteConfirm
              variant="popconfirm"
              targetName="楼层"
              title="确认删除，删除后不可恢复"
              onOk={() => {
                onDelete(record.id);
                return Promise.resolve(true);
              }}
            >
              <Button type="link" compact>
                删除
              </Button>
            </DeleteConfirm>
          </Space>
        );
      },
    };
  }, [onDelete]);

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size={24}>
        <FloorManagementFilter onChange={onFilterChange} />
        <Space size="middle">
          <Button
            type="primary"
            onClick={() => {
              setSelectdFloor(undefined);
              setOpen(true);
            }}
          >
            新建楼层
          </Button>
        </Space>
        <FloorManagementTable
          scroll={{ x: 'max-content' }}
          loading={loading}
          rowKey="id"
          dataSource={floors}
          operation={operation}
          pagination={{
            total: pagination.total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (pageNum, pageSize) => {
              getFloors({ pageNum, pageSize });
            },
          }}
        />
      </Space>
      <FloorManagementEditDrawer
        open={open}
        floor={selectdFloor}
        onClose={() => setOpen(false)}
        onRefresh={onRefresh}
      />
    </Card>
  );
}
