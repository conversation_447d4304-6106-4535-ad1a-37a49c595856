import { QuestionCircleOutlined } from '@ant-design/icons';
import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { ImportexcelCheck } from '@manyun/resource-hub.service.import-custom-point';

type ImportColumnKey =
  | 'category'
  | 'deviceType'
  | 'deviceName'
  | 'deviceGuid'
  | 'pointName'
  | 'pointCode'
  | 'formula'
  | 'description';
export const getImportColumns = () => {
  const getRenderItem = (record: ImportexcelCheck, key: ImportColumnKey) => {
    const { errDto, errMessage } = record;
    return (
      <Space>
        {errMessage[key] ? (
          <Typography.Text type="danger">{errDto[key] ?? '--'}</Typography.Text>
        ) : (
          errDto[key] ?? '--'
        )}
        {errMessage[key] && (
          <Tooltip title={errMessage[key]}>
            <QuestionCircleOutlined style={{ fontSize: 12 }} />
          </Tooltip>
        )}
      </Space>
    );
  };

  const columns: ColumnType<ImportexcelCheck>[] = [
    {
      title: '分类',
      dataIndex: 'category',
      fixed: 'left',
    },
    {
      title: '设备类型',
      dataIndex: 'deviceTypeName',
    },
    {
      title: '设备名称',
      dataIndex: 'deviceName',
    },
    {
      title: '设备guid',
      dataIndex: 'deviceGuid',
    },
    {
      title: '测点名称',
      dataIndex: 'pointName',
    },
    {
      title: '测点code',
      dataIndex: 'pointCode',
    },
    {
      title: '表达式',
      dataIndex: 'formula',
    },
    {
      title: '备注',
      dataIndex: 'description',
    },
  ];

  return columns.map(item => {
    return {
      ...item,
      render: (_: ImportColumnKey, record: ImportexcelCheck) => {
        return getRenderItem(record, item.dataIndex as ImportColumnKey);
      },
    };
  });
};
