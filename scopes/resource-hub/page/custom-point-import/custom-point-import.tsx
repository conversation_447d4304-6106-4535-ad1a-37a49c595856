import React, { useMemo, useState } from 'react';
import { useHistory, useLocation } from 'react-router-dom';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { Upload } from '@manyun/dc-brain.ui.upload';
import {
  generateCustomPointRoutePath,
  generateMergesProcessedPointDetailPath,
} from '@manyun/monitoring.route.monitoring-routes';
import { downloadCustomPointTemplate } from '@manyun/resource-hub.service.download-custom-point-template';
import type { ApiResponseData as PointImport } from '@manyun/resource-hub.service.import-custom-point';
import { importCustomPoint } from '@manyun/resource-hub.service.import-custom-point';

import { getImportColumns } from './utils';

export function CustomPointImport() {
  const history = useHistory();
  const { search } = useLocation();

  const [loading, setLoading] = useState<boolean>(false);
  const [exportLoading, setExportLoading] = useState<boolean>(false);

  const [pointImport, setPointImport] = useState<PointImport>();

  const { idc } = getLocationSearchMap<{
    idc?: string;
  }>(search);

  const handleFileExport = async () => {
    setExportLoading(true);
    const { error, data } = await downloadCustomPointTemplate();
    setExportLoading(false);
    if (error) {
      message.error(error.message);
    }
    return data;
  };

  const errorAlert = useMemo(() => {
    if (!pointImport) {
      return '';
    }
    return (
      <Space>
        <Typography.Text>导入: {pointImport.checkTotal}</Typography.Text>
        <Typography.Text>成功: {pointImport.correctTotal}</Typography.Text>
        <Typography.Text>失败: {pointImport.faultTotal}</Typography.Text>
        <Typography.Text>当前仅展示失败内容</Typography.Text>
      </Space>
    );
  }, [pointImport]);

  return (
    <Card>
      <Space direction="vertical" style={{ width: '100%' }} size="middle">
        <Space size="middle">
          <Upload
            maxCount={1}
            showUploadList={false}
            customRequest={async ({ file, onError, onSuccess }) => {
              setLoading(true);
              const fd = new FormData();
              fd.append('file', file);
              const { data, error } = await importCustomPoint(fd);

              setLoading(false);
              if (error) {
                message.error(error.message);
                onError!(new Error(error.message));
              } else {
                setPointImport(data);
                onSuccess!(data);
                if (data.faultTotal === 0) {
                  message.success('导入成功！');
                  if (idc) {
                    history.push(generateCustomPointRoutePath({ idc }));
                  } else {
                    history.push(generateMergesProcessedPointDetailPath({ tab: 'custom' }));
                  }
                }
              }
            }}
            accept=".csv,.xls,.xlsx"
          >
            <Button type="primary" loading={loading}>
              上传文件
            </Button>
          </Upload>
          <FileExport
            text="下载模版"
            filename="自定义测点模版.xls"
            disabled={exportLoading}
            data={() => {
              return handleFileExport();
            }}
          />
        </Space>
        {pointImport && <Alert message={errorAlert} type="info" />}
        <Table
          scroll={{ x: 'max-content' }}
          tableLayout="fixed"
          dataSource={pointImport ? pointImport.excelCheckErrDtos : []}
          rowKey="id"
          columns={getImportColumns()}
          loading={loading}
        />
      </Space>
    </Card>
  );
}
