import isNil from 'lodash.isnil';
import omit from 'lodash.omit';
import moment from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';
import { ResourceSpecs } from '@manyun/resource-hub.model.spec';
import { fetchBlock } from '@manyun/resource-hub.service.fetch-block';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { mutateBlock } from '@manyun/resource-hub.service.mutate-block';
import type { SvcQuery as mutateParams } from '@manyun/resource-hub.service.mutate-block';

import { BLockBasicInfo } from './components/block-basic-info';
import { BLockClock } from './components/block-clock';
import { BlockEntryAccess } from './components/block-entry-access';
import { BLockSpecInfo } from './components/block-spec';

const layout = {
  labelCol: { span: 3 },
  wrapperCol: { span: 21 },
};

export function BlockMutator() {
  const { id: blockId, blockGuid } = useParams<{ id: string; blockGuid: string }>();

  const isEdit = !!blockId;
  const [form] = Form.useForm();
  const formRef = React.useRef<Form>(null);
  const history = useHistory();
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [specInfo, setSpecInfo] = useState<(SpecJSON & BackendSpec)[]>([]);
  const [configUtil] = useConfigUtil();
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);

  const entranceAccess = Form.useWatch(`Spec_${ResourceSpecs.HasEntrance}`, form) === '是';

  useEffect(() => {
    (async () => {
      if (isEdit && blockGuid) {
        setLoading(true);
        const { error: fileError, data: fileData } = await fetchBizFileInfos({
          targetId: blockGuid,
          targetType: 'BLOCK',
        });
        if (fileError) {
          message.error(fileError.message);
        } else {
          form.setFieldValue(
            'blockPicture',
            fileData.data.map(info => ({ ...info, url: info.src, src: info.src }))
          );
        }
        const { error, data } = await fetchBlock({ guid: blockGuid });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        if (data) {
          form.setFieldsValue({
            ...data,
            idcTag: data.idc,
            principal: { key: data.owner.id, label: data.owner.name },
            period: data.constructionCycleText,
            blockType: data.type,
            constructTime: moment(data.constructedAt),
            operationTime: moment(data.putIntoProductionAt),
            operationStatus: data.status,
            longitude: data.coordinates?.long,
            latitude: data.coordinates?.lat,
            area: data.clockOnOffRadius,
          });
        }
      }
    })();
  }, [isEdit, form, blockId, blockGuid]);

  // @ts-ignore ts()
  const resourcesConfig = configUtil.getScopeCommonConfigs('resources');
  // @ts-ignore ts()
  const showControlFaceCustom = resourcesConfig?.specs?.showControlFaceCustom;

  const submit = useCallback(async () => {
    //  如果楼栋别名是空，提交的时候就填充楼栋名称的值
    const blockName = form.getFieldValue('name');
    const blockAlias = form.getFieldValue('Spec_REPORT_NAME');

    if (blockName && !blockAlias && !isEdit) {
      form.setFieldsValue({
        Spec_REPORT_NAME: blockName,
      });
    }

    try {
      setSubmitLoading(true);
      const formValues = await form.validateFields();

      let params = omit(formValues, 'constructTime', 'operationTime', 'latitude', 'longitude');

      const specParams: Record<string, string> = {};
      Object.keys(formValues)
        .filter(key => key.indexOf('Spec_') > -1)
        .forEach(key => {
          specParams[key] = moment.isMoment(formValues[key])
            ? moment(formValues[key]).format('HH:mm')
            : formValues[key]; // 兼容属性信息的时间选择
        });

      const arr: Record<string, any>[] = [];

      Object.keys(specParams).forEach(item => {
        const spec = specInfo.find(spec => spec.code === item.replace('Spec_', ''));
        const specValue = specParams[item];

        if (spec?.specCode === 'ACS_PERIOD' && showControlFaceCustom) {
          arr.push({
            specId: spec?.id,
            specName: spec?.specName,
            specValue:
              formValues?.Spec_ACS_PERIOD === 'custom'
                ? moment(formValues?.Spec_ACS_PERIOD_VALUE).format('HH:mm')
                : formValues?.Spec_ACS_PERIOD_VALUE, // 兼容处理 门禁人脸
          });

          return;
        }

        if (Array.isArray(specValue) && spec?.optionType) {
          specValue.forEach(val => {
            arr.push({
              specId: spec?.id,
              specName: spec?.specName,
              specValue: Array.isArray(val) ? val[val.length - 1] : val,
            });
          });

          return;
        }

        arr.push({
          specId: spec?.id,
          specName: spec?.specName,
          specValue: Array.isArray(specValue) ? specValue[specValue.length - 1] : specValue,
        });
      });

      params.specParams = arr.filter(item => !!item.specValue && !!item.specId);

      if ([formValues.longitude, formValues.latitude].every(e => !isNil(e) && e !== '')) {
        params.coordinate = [formValues.longitude, formValues.latitude].join(',');
      }

      if (params.blockPicture) {
        params.fileInfoList = params.blockPicture?.map((file: McUploadFile) => ({
          ...McUploadFile.toApiObject(file),
        }));
        params = omit(params, 'blockPicture');
      }

      if (formValues.constructTime) {
        params.constructTime = formValues.constructTime.valueOf();
      }
      if (formValues.operationTime) {
        params.operationTime = formValues.operationTime.valueOf();
      }
      if (formValues.principal) {
        params.principalId = Number(formValues.principal.key);
        params.principalName = formValues.principal.label;
        params = omit(params, 'principal');
      }
      if (blockId) {
        params.id = blockId;
      }

      const { error } = await mutateBlock(params as mutateParams);
      if (error) {
        message.error(error.message);
      } else {
        message.success('操作成功！');
        history.goBack();
      }
      setSubmitLoading(false);
    } catch (error) {
      console.error(error);
      // @ts-ignore
      const errorFields = error.errorFields;
      if (Array.isArray(errorFields) && errorFields.length >= 1) {
        const firstErrorField = errorFields[0].name;
        formRef.current?.scrollToField(firstErrorField, {
          behavior: 'smooth',
          block: 'center',
        });
      }
      setSubmitLoading(false);
    }
  }, [form, blockId, specInfo, history]);

  const getSpecs = useCallback(async () => {
    if (!blockDeviceType) {
      return;
    }
    const params: {
      deviceType: string;
      modelId?: string;
    } = { deviceType: blockDeviceType };
    if (blockGuid) {
      params.modelId = blockGuid;
    }
    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    setSpecInfo(data.data);
  }, [blockDeviceType, blockGuid]);

  useEffect(() => {
    getSpecs();
  }, [getSpecs]);

  return (
    <div style={{ position: 'relative' }}>
      <Form ref={formRef} form={form} {...layout}>
        <Spin spinning={loading || submitLoading}>
          <Space direction="vertical" style={{ width: '100%' }} size={16}>
            <BLockBasicInfo form={form} isEdit={isEdit} />
            <BLockSpecInfo form={form} specInfo={specInfo} />
            <BLockClock />
            <BlockEntryAccess hasEntrance={entranceAccess} blockGuid={blockGuid} />
          </Space>
        </Spin>
        <FooterToolBar>
          <Space>
            <Form.Item style={{ margin: 0 }}>
              <Space size="middle">
                <Button
                  type="primary"
                  htmlType="submit"
                  loading={submitLoading || loading}
                  onClick={submit}
                >
                  提交
                </Button>
                <Button loading={submitLoading || loading} onClick={() => history.goBack()}>
                  取消
                </Button>
              </Space>
            </Form.Item>
          </Space>
        </FooterToolBar>
      </Form>
    </div>
  );
}
