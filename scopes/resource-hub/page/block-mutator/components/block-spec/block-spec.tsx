import moment from 'moment';
import React from 'react';
import { useDeepCompareEffect } from 'react-use';

import { Card } from '@manyun/base-ui.ui.card';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';
import { SpecFormItems } from '@manyun/resource-hub.ui.spec-form-items';

export type BLockSpecInfoProps = {
  form: FormInstance;
  specInfo: (SpecJSON & BackendSpec)[];
};

/** 属性信息 */
export function BLockSpecInfo({ form, specInfo }: BLockSpecInfoProps) {
  const [configUtil] = useConfigUtil();

  // @ts-ignore ts()
  const resourcesConfig = configUtil.getScopeCommonConfigs('resources');
  // @ts-ignore ts()
  const showControlFaceCustom = resourcesConfig?.specs?.showControlFaceCustom;

  useDeepCompareEffect(() => {
    // 阳高环境额外处理
    if (showControlFaceCustom && specInfo?.length) {
      const spec = specInfo.find(item => item.specCode === 'ACS_PERIOD');
      const isTime = spec?.specValue?.includes(':');
      if (spec?.specValue === null) {
        form.setFieldsValue({
          Spec_ACS_PERIOD: 'custom',
        });
        return;
      } else if (spec) {
        form.setFieldsValue({
          Spec_ACS_PERIOD: isTime ? 'custom' : 'valid',
          Spec_ACS_PERIOD_VALUE: isTime ? moment(spec.specValue, 'HH:mm') : spec.specValue,
        });
      }
    }
  }, [form, showControlFaceCustom, specInfo]);

  return (
    // eslint-disable-next-line react/jsx-filename-extension
    <Card
      title={
        <Typography.Title showBadge level={5}>
          属性信息
        </Typography.Title>
      }
    >
      <SpecFormItems
        specInfo={specInfo}
        form={form}
        formLayout="vertical"
        showControlFaceCustom={showControlFaceCustom}
      />
    </Card>
  );
}
