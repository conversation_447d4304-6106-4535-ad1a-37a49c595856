import React from 'react';

import QuestionCircleOutlined from '@ant-design/icons/es/icons/QuestionCircleOutlined';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { getBlockLocales } from '@manyun/resource-hub.model.block';

const longrg =
  /^(-|\+)?(((\d|[1-9]\d|1[0-7]\d|0{1,3})\.\d{0,6})|(\d|[1-9]\d|1[0-7]\d|0{1,3})|180\.0{0,6}|180)$/;
const latreg = /^(-|\+)?([0-8]?\d{1}\.\d{0,6}|90\.0{0,6}|[0-8]?\d{1}|90)$/;

/** 打卡规则 */
export function BLockClock() {
  const locales = getBlockLocales();

  return (
    <Card
      title={
        <Typography.Title showBadge level={5}>
          打卡规则
        </Typography.Title>
      }
    >
      <Form.Item
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        label={
          <>
            <span>
              {locales['longitude']}{' '}
              <Tooltip title="若此楼栋的考勤方式为：GPS打卡，则必须填写坐标经度；请使用高德地图配置经度">
                <QuestionCircleOutlined />
              </Tooltip>
            </span>
          </>
        }
        name="longitude"
        rules={[
          {
            pattern: new RegExp(longrg, 'g'),
            message: '经度整数部分为0-180,小数部分为0到6位',
          },
        ]}
      >
        <InputNumber style={{ width: 220 }} addonBefore="经度" placeholder="经度，例：119.975243" />
      </Form.Item>

      <Form.Item
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        label={
          <>
            <span>
              {locales['latitude']}{' '}
              <Tooltip title="若此楼栋的考勤方式为：GPS打卡，则必须填写坐标纬度；请使用高德地图配置纬度">
                <QuestionCircleOutlined />
              </Tooltip>
            </span>
          </>
        }
        name="latitude"
        rules={[
          {
            pattern: new RegExp(latreg, 'g'),
            message: '纬度整数部分为0-90,小数部分为0到6位!',
          },
        ]}
      >
        <InputNumber style={{ width: 220 }} addonBefore="纬度" placeholder="纬度，例：30.232482" />
      </Form.Item>

      <Form.Item
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
        name="area"
        label={
          <>
            <span>
              有效打卡范围（米）{' '}
              <Tooltip title="GPS打卡的有效直线距离">
                <QuestionCircleOutlined />
              </Tooltip>
            </span>
          </>
        }
      >
        <InputNumber
          style={{ width: 200 }}
          min={1}
          max={500}
          precision={0}
          placeholder="请输入0-500的整数"
          addonAfter="m"
        />
      </Form.Item>
    </Card>
  );
}
