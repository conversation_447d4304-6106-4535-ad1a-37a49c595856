import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchBlockTypes } from '@manyun/resource-hub.service.fetch-block-types';

export type BlockTypesSelectProps = Omit<
  SelectProps,
  'filterOption' | 'options' | 'onSearch' | 'loading'
> & { trigger: 'onDidMount' | 'onFocus' };

export const BlockTypesSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', onFocus, ...props }: BlockTypesSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [blockTypeList, setBlockTypeList] = useState<{ label: string; value: string }[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        getBlockTypeList();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getBlockTypeList = async () => {
      setLoading(true);
      const { data, error } = await fetchBlockTypes();
      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }

      setBlockTypeList(
        Object.keys(data).map(item => ({
          value: item,
          label: data[item],
        }))
      );
    };

    return (
      <Select
        ref={ref}
        {...props}
        loading={loading}
        options={blockTypeList}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            getBlockTypeList();
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);

BlockTypesSelect.displayName = 'BlockTypesSelect';
