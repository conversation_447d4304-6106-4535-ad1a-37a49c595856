import { PlusOutlined } from '@ant-design/icons';
import React from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { getBlockLocales } from '@manyun/resource-hub.model.block';
import { fetchIdc } from '@manyun/resource-hub.service.fetch-idc';
import { BlockStatusSelect } from '@manyun/resource-hub.ui.block-status-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { BlockPeriodSelect } from '../block-period';
import { BlockTypesSelect } from '../block-type-select';

export type BLockBasicInfoProps = {
  form: FormInstance;
  isEdit: boolean;
};

/** 基本信息 */
export function BLockBasicInfo({ form, isEdit }: BLockBasicInfoProps) {
  const locales = getBlockLocales();
  const idcTag = Form.useWatch('idcTag', form);
  const fileList = Form.useWatch('blockPicture', form);

  return (
    <Card
      title={
        <Typography.Title showBadge level={5}>
          基本信息
        </Typography.Title>
      }
    >
      <Row gutter={24}>
        <Col span={24}>
          <Form form={form} labelCol={{ span: 2 }} wrapperCol={{ span: 22 }}>
            <Form.Item
              label={locales.idcTag}
              name="idcTag"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <LocationTreeSelect
                style={{ width: 200 }}
                authorizedOnly
                nodeTypes={['IDC']}
                showSearch
                getPopupContainer={trigger => trigger.parentNode}
                disabled={isEdit}
                onSelect={async (value: unknown) => {
                  const { error, data } = await fetchIdc({ idcTag: value as string });
                  if (error) {
                    message.error(error);
                    return;
                  }
                  if (data) {
                    form.setFieldsValue({
                      principal: { key: data.owner.id, label: data.owner.name },
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label={locales.principal}
              name="principal"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <UserSelect style={{ width: 200 }} disabled={!idcTag} placeholder="仅可选择1位" />
            </Form.Item>
            <Form.Item
              label={locales.tag}
              name="tag"
              rules={[
                {
                  required: true,
                },
                {
                  pattern: /^[A-Za-z0-9]{1,6}$/,
                  message: '只能包含英文字母和数字，且不超过6个字符！',
                },
              ]}
            >
              <Input style={{ width: 200 }} maxLength={6} disabled={isEdit} />
            </Form.Item>
            <Form.Item label={locales.name} name="name" rules={[{ max: 64 }, { required: true }]}>
              <Input style={{ width: 200 }} />
            </Form.Item>
            <Form.Item
              label={locales.period}
              name="period"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <BlockPeriodSelect
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
              />
            </Form.Item>

            <Form.Item
              label={locales.blockType}
              name="blockType"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <BlockTypesSelect
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
                showSearch
                trigger="onDidMount"
              />
            </Form.Item>

            <Form.Item
              label={locales.constructTime}
              name="constructTime"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <DatePicker style={{ width: 200 }} format="YYYY-MM-DD" />
            </Form.Item>

            <Form.Item
              label={locales.operationTime}
              name="operationTime"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <DatePicker style={{ width: 200 }} format="YYYY-MM-DD" />
            </Form.Item>

            <Form.Item
              label={locales.operationStatus}
              name="operationStatus"
              rules={[
                {
                  required: true,
                },
              ]}
            >
              <BlockStatusSelect
                style={{ width: 200 }}
                getPopupContainer={trigger => trigger.parentNode}
                showSearch
              />
            </Form.Item>

            <Form.Item
              label={locales.blockPicture}
              name="blockPicture"
              extra={
                <Typography.Text type="secondary">
                  图片建议比例：16:9；格式支持：image/*；大小上限：2M
                </Typography.Text>
              }
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <Upload
                listType="picture-card"
                fileList={fileList}
                showUploadList
                accept=".png,.jpg,.jpeg"
                allowDelete
                maxFileSize={2}
                maxCount={1}
              >
                {fileList?.length >= 1 ? null : (
                  <Button style={{ width: 104, height: 104 }} type="text">
                    <PlusOutlined />
                    <br />
                    <Typography.Text type="secondary">上传图片</Typography.Text>
                  </Button>
                )}
              </Upload>
            </Form.Item>
          </Form>
        </Col>
      </Row>
    </Card>
  );
}
