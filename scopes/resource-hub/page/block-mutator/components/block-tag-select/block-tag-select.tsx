import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type BlockTagSelectProps = Omit<SelectProps, 'options'>;

const tagList: string[] = [];
for (var i = 65; i < 91; i++) {
  tagList.push(String.fromCharCode(i));
}
tagList.join(',');

export const BlockTagSelect = React.forwardRef(
  (props: BlockTagSelectProps, ref: React.Ref<RefSelectProps>) => {
    return (
      <Select
        optionFilterProp="label"
        {...props}
        options={tagList.map(tag => ({
          label: tag,
          value: tag,
        }))}
      />
    );
  }
);

BlockTagSelect.displayName = 'BlockTagSelect';
