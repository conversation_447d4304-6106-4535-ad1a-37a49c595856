import React, { useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { fetchEntranceAuthGroups } from '@manyun/resource-hub.service.fetch-entrance-auth-groups';
import type { EntranceAuthGroups } from '@manyun/resource-hub.service.fetch-entrance-auth-groups';

export type BlockEntryAccessProps = {
  hasEntrance: boolean;
  blockGuid?: string;
};

/** 门禁配置 */
export function BlockEntryAccess({ hasEntrance, blockGuid }: BlockEntryAccessProps) {
  const [loading, setLoading] = useState(false);
  const [entranceAuthGroups, setEntranceAuthGroups] = useState<EntranceAuthGroups[]>([]);

  useEffect(() => {
    (async () => {
      if (!hasEntrance || !blockGuid) {
        return;
      }
      setLoading(true);
      const { error, data } = await fetchEntranceAuthGroups({
        blockGuid,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
      }
      setEntranceAuthGroups(data.data);
    })();
  }, [blockGuid, hasEntrance]);

  if (!hasEntrance || !blockGuid) {
    return null;
  }

  return (
    <Card
      style={{ marginBottom: 100 }}
      title={
        <Typography.Title showBadge level={5}>
          门禁权限组
        </Typography.Title>
      }
      loading={loading}
    >
      <Space direction="vertical" style={{ width: '100%' }} size={16}>
        <Row gutter={[16, 16]}>
          {entranceAuthGroups.map(group => {
            return (
              <Col key={group.id} span={4}>
                <Card>
                  <Typography.Text>{group.authName}</Typography.Text>
                </Card>
              </Col>
            );
          })}
        </Row>
      </Space>
    </Card>
  );
}
