import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type BlockPeriodSelectProps = Omit<SelectProps, 'options'>;

export const BlockPeriodSelect = React.forwardRef(
  (props: BlockPeriodSelectProps, ref: React.Ref<RefSelectProps>) => {
    return (
      <Select
        optionFilterProp="label"
        {...props}
        options={[
          {
            value: '一期',
            label: '一期',
          },
          {
            value: '二期',
            label: '二期',
          },
          {
            value: '三期',
            label: '三期',
          },
          {
            value: '四期',
            label: '四期',
          },
        ]}
      />
    );
  }
);

BlockPeriodSelect.displayName = 'BlockPeriodSelect';
