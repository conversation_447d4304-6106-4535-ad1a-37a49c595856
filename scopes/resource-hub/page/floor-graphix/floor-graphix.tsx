import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import BookOutlined from '@ant-design/icons/es/icons/BookOutlined';
import { useApolloClient } from '@apollo/client';
import cloneDeep from 'lodash.clonedeep';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type {
  PageSnapshotIn,
  StoreInstance,
  StoreSnapshotOut,
} from '@manyun/dc-brain.aura-graphix';
import {
  AuraGraphix,
  ThemeColors,
  ThemeCompositions,
  createStore,
  getImagePropsAsync,
  toBase64,
  traverse,
} from '@manyun/dc-brain.aura-graphix';
import type { Space } from '@manyun/dc-brain.gql.client';
import type { RcFile } from '@manyun/dc-brain.ui.upload';
import { fetchTopology } from '@manyun/monitoring.service.fetch-topology';
import { relatedElementUtil } from '@manyun/monitoring.state.topology';
import { readSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import type { GraphixFloorRouteParams } from '@manyun/resource-hub.route.resource-routes';
import {
  BASIC_RESOURCES_FLOOR_ROUTE_PATH,
  generateEditGraphixFloorRoutePath,
} from '@manyun/resource-hub.route.resource-routes';
import { createTopology } from '@manyun/resource-hub.service.create-topology';
import type { SvcQuery } from '@manyun/resource-hub.service.create-topology';
import { updateTopology } from '@manyun/resource-hub.service.update-topology';
import { GraphixBaseMapImport } from '@manyun/resource-hub.ui.graphix-base-map-import';

const PAGE_CONFIG: Pick<PageSnapshotIn, 'size' | 'width' | 'height' | 'padding'> = {
  size: 'fixed',
  width: 1920,
  height: 1080,
  padding: 0,
};

const BACKGROUD_IMAGE_TYPE = 'background-image';

export function FloorGraphix() {
  const [store, setStore] = React.useState<StoreInstance | null>(null);
  const [hasBackgroundImage, setHasBackgroupdImage] = React.useState(false);
  const [importingBackgroundImage, setImportingBackgroundImage] = React.useState(false);

  const history = useHistory();

  const { idc, block, floor, mode } = useParams<GraphixFloorRouteParams>();

  const spaceGuid = useMemo(
    () => (idc && block && floor ? idc + '.' + block + '.' + floor : undefined),
    [block, floor, idc]
  );

  useSpaces({
    variables: {
      nodeTypes: ['IDC', 'BLOCK', 'FLOOR', 'ROOM_TYPE', 'ROOM'],
      includeVirtualBlocks: false,
      authorizedOnly: true,
    },
  });

  const client = useApolloClient();
  const floorSpace = useMemo(
    () => (spaceGuid ? readSpace(client, spaceGuid, ['FLOOR', 'ROOM_TYPE', 'ROOM']) : null),
    [client, spaceGuid]
  );

  const roomSpaces = initializeRoomSpaces(floorSpace?.children ?? []);

  const baseRooms = useDeepCompareMemo(
    () => [
      {
        key: 'rooms',
        icon: <BookOutlined />,
        label: '包间',
        children: roomSpaces,
      },
    ],
    [floorSpace?.children]
  );

  const [graphId, setGraphId] = useState<string>();

  const initGraph = (graph: StoreSnapshotOut) => {
    const _store = createStore();
    _store
      .loadJSON(
        cloneDeep({
          ...graph,
          pages: graph.pages.map(p => ({
            ...p,
            ...PAGE_CONFIG,
          })),
        })
      )
      .then(() => {
        for (let index = 0; index < graph.pages.length; index++) {
          const page = graph.pages[index];
          let hasBackImg = false;
          traverse(page.children, element => {
            if (element.custom?.type === BACKGROUD_IMAGE_TYPE) {
              hasBackImg = true;
            }
          });
          if (hasBackImg) {
            setHasBackgroupdImage(true);
            break;
          }
        }
        storeRef.current = _store;
        setStore(_store);
      });
  };

  useEffect(() => {
    (async () => {
      if (!spaceGuid) {
        return;
      }
      const { data, error } = await fetchTopology({
        blockGuid: spaceGuid,
        topologyType: 'FLOOR',
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data?.id) {
        history.push(
          generateEditGraphixFloorRoutePath({
            idc,
            block,
            floor,
            mode: 'edit',
          })
        );
      }
      if (data?.graph && data?.id) {
        initGraph(data.graph);
        setGraphId(data.id.toString());
      } else {
        const _store = createStore();
        _store.addPage(PAGE_CONFIG);
        storeRef.current = _store;
        setStore(_store);
      }
    })();
    return () => {
      setStore(null);
    };
  }, [block, floor, history, idc, spaceGuid]);

  const storeRef = React.useRef<StoreInstance | null>(null);

  const onBackgroundImageChange = useCallback(
    async (file: RcFile) => {
      if (!store) {
        message.error(`store not found`);
        return;
      }
      setImportingBackgroundImage(true);

      const dataUrl = await toBase64(file);
      const { width, height } = await getImagePropsAsync(dataUrl);
      const activePage = store.activePage!;
      const pageWidth = activePage.width + activePage.padding * 2;
      const pageHeight = activePage.height + activePage.padding * 2;
      const minPageSize = Math.min(pageWidth, pageHeight);
      const resizedWidth = width > minPageSize ? minPageSize * 0.9 : width;
      const resizedHeight = resizedWidth !== width ? (resizedWidth / width) * height : height;

      activePage.addElement({
        type: 'image',
        name: '底图',
        x: (pageWidth - resizedWidth) / 2 - activePage.padding,
        y: (pageHeight - resizedHeight) / 2 - activePage.padding,
        width: resizedWidth,
        height: resizedHeight,
        src: dataUrl,
        locked: false,
        custom: {
          type: BACKGROUD_IMAGE_TYPE,
        },
      });

      await new Promise(resolve => setTimeout(resolve, 500));

      setImportingBackgroundImage(false);
      setHasBackgroupdImage(true);
    },
    [store]
  );

  const onGraphSave = useCallback(
    async graph => {
      if (!spaceGuid) {
        return;
      }
      const params: SvcQuery = {
        viewJson: JSON.stringify(graph),
        topologyJson: JSON.stringify({ nodeList: [], flowList: [] }),
        blocks: [spaceGuid],
        topologyType: 'FLOOR',
      };
      const { error } =
        mode === 'new'
          ? await createTopology({
              ...params,
            })
          : await updateTopology({
              ...params,
              id: graphId!,
            });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success(`${mode === 'new' ? '新建' : '编辑'}楼层视图成功`);
      history.push(BASIC_RESOURCES_FLOOR_ROUTE_PATH);
    },
    [graphId, history, mode, spaceGuid]
  );

  if (!store) {
    return <Spin style={{ height: '100vh', margin: '40vh' }} tip="正在初始化..." />;
  }

  return (
    <div>
      <ThemeCompositions theme={document.body.dataset.theme === 'dark' ? 'dark' : 'light'}>
        <div
          style={{
            width: '100vw',
            height: '100vh',
            position: 'fixed',
            top: 0,
            right: 0,
            bottom: 0,
            left: 0,
            zIndex: 999,
          }}
        >
          <AuraGraphix
            debug={env.__DEBUG_MODE__}
            store={store}
            width="100vw"
            height="100vh"
            title={`${mode === 'new' ? '新建' : '编辑'} ${spaceGuid} 视图`}
            resourceCategories={baseRooms}
            onAutoFixes={store => {
              store.pages.forEach(page => {
                traverse(page.children, element => {
                  if (element.custom?.type === 'device') {
                    element.set({ fill: ThemeColors.ContainerBg });
                  } else if (element.custom?.type === 'device_text') {
                    element.set({ fill: ThemeColors.TextColor, stroke: ThemeColors.TextColor });
                  }
                });
              });
            }}
            onSave={snapshot => {
              onGraphSave(snapshot);
            }}
            onQuitClick={() => {
              history.push(BASIC_RESOURCES_FLOOR_ROUTE_PATH);
            }}
          />
          <GraphixBaseMapImport
            open={!hasBackgroundImage}
            importLoading={importingBackgroundImage}
            onBackgroundImageChange={onBackgroundImageChange}
          />
        </div>
      </ThemeCompositions>
    </div>
  );
}

function initializeRoomSpaces(floorSpace: Space[]) {
  const width = 100;
  const height = 100;

  return floorSpace.map(roomType => ({
    key: roomType.value,
    type: roomType.type,
    label: roomType.label,
    children: Array.isArray(roomType.children)
      ? roomType.children.map(room => {
          const remarkTextElementId = relatedElementUtil.generate(room.value);
          const [, remarkTextRowKey] = relatedElementUtil.split(remarkTextElementId);

          return {
            key: room.value,
            copyMax: 1,
            type: room.type,
            img: '/images/floor-graphix/light/floor-rect.png',
            label: room.label,
            elementConfig: {
              custom: { type: 'room_group' },
              type: 'group',
              name: room.label,
              width,
              height,
              canUngroup: true,
              children: [
                {
                  custom: {
                    type: 'room',
                    name: room.label,
                    roomGuid: room.value,
                    remarkTextRowKeys: [remarkTextRowKey],
                  },
                  type: 'rect',
                  id: room.value,
                  x: 0,
                  y: 0,
                  width,
                  height,
                  fill: ThemeColors.ContainerBg,
                },
                {
                  custom: { type: 'room_text' },
                  id: remarkTextElementId,
                  type: 'text',
                  x: 0,
                  y: 0,
                  width,
                  height,
                  align: 'center',
                  verticalAlign: 'middle',
                  text: room.label,
                  fill: ThemeColors.TextColor,
                  stroke: ThemeColors.TextColor,
                },
              ],
            },
            isLeaf: true,
          };
        })
      : [],
  }));
}
