import debounce from 'lodash.debounce';
import React, { useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { getSpecLocales } from '@manyun/resource-hub.model.spec';
import type { SpecJSON } from '@manyun/resource-hub.model.spec';
import { deleteSpec } from '@manyun/resource-hub.service.delete-spec';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';

export type ResourceSpaceAttributeProps = {
  deviceType: string;
  onAttributeInfoChange?: (spec?: SpecJSON) => void;
  onPatternChange?: (pattern: string, type: string) => void;
};

export const ELEMNT_DELETE_SPEC = 'element_delete-space-attribute';
export const ELEMNT_EDIT_SPEC = 'element_edit-space-attribute';

const locales = getSpecLocales();

export function ResourceSpaceAttribute({
  deviceType,
  onAttributeInfoChange,
  onPatternChange,
}: ResourceSpaceAttributeProps) {
  const [loading, setLoading] = React.useState(false);
  const [dataSource, setDataSource] = React.useState<SpecJSON[]>([]);
  const [, { checkCode }] = useAuthorized();
  const deleteable = checkCode(ELEMNT_DELETE_SPEC);
  const editable = checkCode(ELEMNT_EDIT_SPEC);
  const [searchKey, setSearchKey] = useState<string>();

  const fetchSpaceAttribute = React.useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchSpecs({ deviceType });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource(data.data);
  }, [deviceType]);

  const deleteSpaceAttribute = React.useCallback(
    async (id: number, operatorNotes: string) => {
      const { error } = await deleteSpec({
        id,
        operatorNotes,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('删除成功');
      setSearchKey('');
      fetchSpaceAttribute();
    },
    [fetchSpaceAttribute]
  );

  const onSearch = debounce((searchKey: string) => {
    setSearchKey(searchKey);
  }, 300);

  const specList: SpecJSON[] = useDeepCompareMemo(() => {
    if (!searchKey) {
      return dataSource;
    }

    return dataSource.filter(
      spec => spec.name && spec.name.toLowerCase().indexOf(searchKey.toLowerCase()) > -1
    );
  }, [searchKey, dataSource]);

  React.useEffect(() => {
    if (deviceType) {
      fetchSpaceAttribute();
    }
  }, [fetchSpaceAttribute, deviceType]);

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="middle">
      <Space
        style={{
          display: 'flex',
          justifyContent: 'space-between',
        }}
      >
        <Button
          type="primary"
          onClick={() => {
            onPatternChange && onPatternChange('attributeEdit', 'add');
          }}
        >
          新建
        </Button>
        <Input.Search
          style={{ width: 250 }}
          placeholder="搜索属性名称"
          onSearch={onSearch}
          onChange={({ target: { value } }) => setSearchKey(value)}
        />
      </Space>
      <Table
        rowKey="id"
        loading={loading}
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
        dataSource={specList}
        columns={[
          {
            title: '属性名称',
            dataIndex: 'name',
            ellipsis: true,
          },
          {
            title: '是否必填',
            dataIndex: 'required',
            ellipsis: true,
            render: (text: boolean | undefined) => {
              if (typeof text !== 'boolean') {
                return '--';
              }
              return text ? '是' : '否';
            },
          },
          {
            title: '属性值选择方式',
            dataIndex: 'inputWay',
            ellipsis: true,
            render: (_, record) => {
              if (!record.inputWay) {
                return '--';
              }
              switch (record.inputWay) {
                case 'INPUT':
                  return locales['inputWayInput'];
                case 'OPT':
                  return locales['inputWayOpt'];
                case 'COMPONENT':
                  return locales['inputWayComponent'];
              }
            },
          },
          {
            title: '属性类型',
            dataIndex: 'type',
            ellipsis: true,
            render: (_, record) => {
              if (!record.type) {
                return '--';
              }
              return record.type === 'CHARACTER'
                ? locales['valueTypeCharacter']
                : locales['valueTypeNumber'];
            },
          },
          {
            title: '单位',
            dataIndex: 'unit',
            ellipsis: true,
            render: (_, record) => {
              if (!record.unit) {
                return '--';
              }
              return record.unit;
            },
          },
          {
            title: '选择项',
            dataIndex: 'options',
            ellipsis: true,
            render: (_, record) => {
              if (!record.options || record.inputWay === 'COMPONENT') {
                return '--';
              }
              return record.options.split(',').join(' | ');
            },
          },
          {
            title: '更新人',
            dataIndex: 'operatorName',
            ellipsis: true,
          },
          {
            title: '更新时间',
            dataIndex: 'modifiedAt',
            ellipsis: true,
          },
          {
            title: '操作',
            dataIndex: 'action',
            fixed: 'right',
            render: (_, record: SpecJSON) => {
              return (
                <Space>
                  {!deleteable && !editable ? (
                    '--'
                  ) : (
                    <>
                      {editable && (
                        <Button
                          type="link"
                          compact
                          onClick={() => {
                            record.id && onAttributeInfoChange && onAttributeInfoChange(record);
                            onPatternChange && onPatternChange('attributeEdit', 'edit');
                          }}
                        >
                          编辑
                        </Button>
                      )}

                      {deleteable && (
                        <DeleteConfirm
                          variant="modal"
                          targetName="该条属性配置"
                          title={`删除 ${record.name || ''}`}
                          onOk={({ reason }) => {
                            if (typeof deleteSpaceAttribute === 'function' && record.id) {
                              deleteSpaceAttribute(record.id, reason);
                            }
                            return Promise.resolve(true);
                          }}
                        >
                          <Button type="link" compact>
                            删除
                          </Button>
                        </DeleteConfirm>
                      )}
                    </>
                  )}
                </Space>
              );
            },
          },
        ]}
        pagination={false}
      />
    </Space>
  );
}
