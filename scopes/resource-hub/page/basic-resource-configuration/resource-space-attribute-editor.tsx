import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import React, { useCallback, useMemo, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { CheckboxOptionType } from '@manyun/base-ui.ui.checkbox';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { PageHeader } from '@manyun/base-ui.ui.page-header';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Slider } from '@manyun/base-ui.ui.slider';
import type { SliderBaseProps } from '@manyun/base-ui.ui.slider';
import { Space } from '@manyun/base-ui.ui.space';
import type { SpaceNodeType } from '@manyun/resource-hub.gql.client.spaces';
import type { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaTypeTextMap } from '@manyun/resource-hub.model.metadata';
import type { SpecJSON } from '@manyun/resource-hub.model.spec';
import { getSpecLocales } from '@manyun/resource-hub.model.spec';
import { createSpec } from '@manyun/resource-hub.service.create-spec';
import type { ApiQ as createSpecParams } from '@manyun/resource-hub.service.create-spec';
import type { ApiQ as updateSpecParams } from '@manyun/resource-hub.service.update-spec';
import { updateSpec } from '@manyun/resource-hub.service.update-spec';
import { SpecInputWayRadio, SpecValueTypeRadio } from '@manyun/resource-hub.ui.spec-select';
import { SpecUnitSelect } from '@manyun/resource-hub.ui.spec-unit-select';

const { TextArea } = Input;

export type ResourceSpaceAttributeEditorProps = {
  attributeInfo: SpecJSON | undefined;
  deviceType: string;
  onPatternChange?: (pattern: string) => void;
  onAttributeInfoChange?: (id?: number) => void;
};

const layout = {
  labelCol: { span: 7 },
  wrapperCol: { span: 17 },
};

const formListLayout = {
  wrapperCol: { span: 17, offset: 7 },
};

const formSubmitLayout = {
  wrapperCol: { offset: 7, span: 17 },
};

const COMPONENTS_VALUES_MAPPER = {
  location: 'location',
  metaData: 'metaData',
  time: 'time',
};

const nodeTypesMapper: Record<number, SpaceNodeType> = {
  // 0: 'REGION',
  1: 'IDC',
  2: 'BLOCK',
  3: 'FLOOR',
  4: 'ROOM_TYPE',
  5: 'ROOM',
};

export function ResourceSpaceAttributeEditor({
  attributeInfo,
  deviceType,
  onPatternChange,
  onAttributeInfoChange,
}: ResourceSpaceAttributeEditorProps) {
  const isEdit = useMemo(() => !!attributeInfo?.id, [attributeInfo?.id]);

  const initialValues = useMemo(() => {
    const componentOpt =
      attributeInfo?.options &&
      attributeInfo.inputWay === 'COMPONENT' &&
      JSON.parse(attributeInfo.options);

    let nodeValues =
      Array.isArray(componentOpt?.nodes) &&
      Object.keys(nodeTypesMapper).reduce<number[]>((pre, cur) => {
        if (componentOpt?.nodes.indexOf(nodeTypesMapper[+cur]) > -1) {
          pre.push(+cur);
        }
        return pre;
      }, []);
    if (Array.isArray(nodeValues)) {
      nodeValues = [nodeValues[0], nodeValues[nodeValues.length - 1]];
    }

    return isEdit
      ? attributeInfo?.inputWay === 'COMPONENT'
        ? {
            ...attributeInfo,
            components: componentOpt?.components,
            componentProps: componentOpt?.componentProps,
            nodes: nodeValues,
          }
        : {
            ...attributeInfo,
            options: attributeInfo?.options?.split(','),
          }
      : {
          optionType: false,
          options: [null],
          nodes: [1, 2],
          components: COMPONENTS_VALUES_MAPPER.location,
        };
  }, [attributeInfo, isEdit]);

  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState(false);
  const locales = useMemo(() => getSpecLocales(), []);
  const components = Form.useWatch('components', form);
  const inputWayValue = Form.useWatch('inputWay', form);

  const handleLeave = useCallback(() => {
    onPatternChange && onPatternChange('list');
    onAttributeInfoChange && onAttributeInfoChange(undefined);
  }, [onAttributeInfoChange, onPatternChange]);

  const submit = useCallback(async () => {
    try {
      const values: SpecJSON & {
        options: string[];
        components: string;
        componentProps: string[];
        nodes: number[];
        optionType: boolean;
      } = await form.validateFields();

      const spaceNodes =
        Array.isArray(values.nodes) &&
        Object.keys(nodeTypesMapper).reduce<string[]>((pre, cur) => {
          if (+cur <= values.nodes[values.nodes.length - 1] && +cur >= values.nodes[0]) {
            pre.push(nodeTypesMapper[+cur]);
          }
          return pre;
        }, []);

      const componentOpt = JSON.stringify({
        components: values.components,
        componentProps: values.components === 'time' ? 'HH:mm' : values.componentProps, // 时间选择固定值为时/分
        nodes: spaceNodes,
      });

      const params = {
        required: values.required,
        specName: values.name,
        description: values.description,
        optionType: values.optionType,
      };

      const createParams: createSpecParams = {
        ...params,
        deviceType,
        inputWay: values.inputWay,
        specUnit: values.inputWay === 'INPUT' ? values.unit : undefined,
        valueType:
          values.inputWay === 'OPT' || values.inputWay === 'COMPONENT' ? 'CHARACTER' : values.type,
        options: values.inputWay === 'COMPONENT' ? componentOpt : values.options?.join(','),
      };

      const editParams: Omit<updateSpecParams, 'id'> = {
        ...params,
        unit: values.inputWay === 'INPUT' ? values.unit : undefined,
        options:
          values.inputWay === 'COMPONENT'
            ? componentOpt
            : values.inputWay === 'OPT'
              ? values.options?.join(',')
              : undefined,
      };
      setSubmitLoading(true);
      const { error } =
        isEdit && attributeInfo?.id
          ? await updateSpec({
              id: attributeInfo?.id,
              ...editParams,
            })
          : await createSpec(createParams);
      setSubmitLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('操作成功');
      handleLeave();
    } catch (error) {
      setSubmitLoading(false);
      console.error(error);
    }
  }, [form, deviceType, attributeInfo?.id, isEdit, handleLeave]);

  const getInputWay = (inputWay: string) => {
    const spaceMarks = {
      // 0: '区域',
      1: '机房',
      2: '楼栋',
      3: '楼层',
      4: '包间类型',
      5: '包间',
    };
    const componentOptions: CheckboxOptionType[] = [
      { label: '支持数据权限', value: 'authorized' },
      { label: '支持虚拟楼', value: 'virtual' },
    ];
    switch (inputWay) {
      case 'INPUT':
        return (
          // eslint-disable-next-line react/jsx-filename-extension
          <Form.Item
            label={locales.valueType}
            name="type"
            rules={[{ required: true, message: '请选择属性值类型' }]}
          >
            <SpecValueTypeRadio disabled={isEdit} />
          </Form.Item>
        );
      case 'OPT':
        return (
          <Form.List
            name="options"
            rules={[
              {
                validator: async (_, options) => {
                  if (!options || options.length < 1) {
                    return Promise.reject(new Error('请至少填写一个选择项'));
                  } else {
                    const newNamesLength = new Set(options).size;

                    if (
                      options.every((option: string) => !!option) &&
                      options.length !== newNamesLength
                    ) {
                      return Promise.reject(new Error('选择项不能重复'));
                    }
                    return Promise.resolve();
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map((field, index) => (
                  <Form.Item
                    {...(index === 0 ? layout : formListLayout)}
                    key={field.key}
                    label={index === 0 ? locales.options : ''}
                    required
                  >
                    <Form.Item
                      {...field}
                      validateTrigger={['onChange', 'onBlur']}
                      rules={[
                        {
                          required: true,
                          message: '请输入选择项',
                        },
                        {
                          max: 50,
                          message: '最多输入 50 个字符！',
                        },
                      ]}
                      noStyle
                    >
                      <Input style={{ width: 200 }} maxLength={10} />
                    </Form.Item>
                    {fields.length > 1 ? (
                      <MinusCircleOutlined
                        style={{ marginLeft: 10 }}
                        onClick={() => remove(field.name)}
                      />
                    ) : null}
                  </Form.Item>
                ))}
                {fields.length < 30 && (
                  <Form.Item {...formListLayout}>
                    <Button
                      style={{ width: 230 }}
                      type="dashed"
                      icon={<PlusOutlined />}
                      onClick={() => {
                        add();
                      }}
                    >
                      添加选择项
                    </Button>
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                )}
              </>
            )}
          </Form.List>
        );
      case 'COMPONENT':
        return (
          <>
            <Form.Item
              label="组件值"
              name="components"
              rules={[{ required: true, message: '请选择组件值' }]}
            >
              <Select
                style={{ width: 200 }}
                options={[
                  { label: '位置选择', value: COMPONENTS_VALUES_MAPPER.location },
                  { label: '元数据选择', value: COMPONENTS_VALUES_MAPPER.metaData },
                  { label: '时间选择', value: COMPONENTS_VALUES_MAPPER.time },
                ]}
                onSelect={value => {
                  form.setFieldValue('componentProps', undefined); // componentProps固定form字段，切换组件值后清空重选
                  //把组件值的error清空掉
                  form.setFields([
                    {
                      name: 'componentProps',
                      errors: [],
                    },
                  ]);
                }}
              />
            </Form.Item>
            {getComponentsFormItems(components, {
              componentOptions,
              spaceMarks,
            })}
          </>
        );
      default:
        return null;
    }
  };

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={16}>
      <PageHeader
        style={{ padding: 0 }}
        title={`${isEdit ? '编辑' : '新建'}属性`}
        onBack={handleLeave}
      />
      <Form form={form} initialValues={initialValues} {...layout}>
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item
              label={locales.specName}
              name="name"
              rules={[
                {
                  required: true,
                  message: '请输入规格名称',
                },
                {
                  max: 30,
                  message: '最多输入 30 个字符！',
                },
              ]}
            >
              <Input style={{ width: 328 }} />
            </Form.Item>
            <Form.Item
              label={locales.required}
              name="required"
              rules={[{ required: true, message: '请选择是否必填' }]}
            >
              <Radio.Group
                options={[
                  {
                    value: true,
                    label: '是',
                  },
                  {
                    value: false,
                    label: '否',
                  },
                ]}
              />
            </Form.Item>
            <Form.Item
              label={locales.inputWay}
              name="inputWay"
              rules={[{ required: true, message: '请选择属性值填写方式' }]}
            >
              <SpecInputWayRadio disabled={isEdit} />
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(pre, next) => pre.inputWay !== next.inputWay}>
              {({ getFieldValue }) => getInputWay(getFieldValue('inputWay'))}
            </Form.Item>
            <Form.Item noStyle shouldUpdate={(pre, next) => pre.inputWay !== next.inputWay}>
              {({ getFieldValue }) => {
                if (getFieldValue('inputWay') === 'INPUT') {
                  return (
                    <Form.Item label={locales.specUnit} name="unit">
                      <SpecUnitSelect
                        style={{
                          width: 200,
                        }}
                        allowClear
                        showSearch
                      />
                    </Form.Item>
                  );
                }
                return null;
              }}
            </Form.Item>
            {(inputWayValue === 'OPT' ||
              (inputWayValue === 'COMPONENT' &&
                components === COMPONENTS_VALUES_MAPPER.metaData)) && (
              <Form.Item label={locales.type} name="optionType">
                <Radio.Group
                  disabled={isEdit}
                  options={[
                    {
                      value: false,
                      label: '单选',
                    },
                    {
                      value: true,
                      label: '多选',
                    },
                  ]}
                />
              </Form.Item>
            )}
            <Form.Item label={locales.description} name="description">
              <TextArea style={{ width: '400px' }} maxLength={120} />
            </Form.Item>
            <Form.Item {...formSubmitLayout}>
              <Space size="middle">
                <Button type="primary" loading={submitLoading} onClick={submit}>
                  提交
                </Button>
                <Button onClick={handleLeave}>取消</Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Space>
  );
}

function getComponentsFormItems(
  component: 'metaData' | 'time' | 'location',
  itemOptions: {
    componentOptions?: CheckboxOptionType[];
    spaceMarks?: SliderBaseProps['marks'];
  }
) {
  switch (component) {
    case 'time':
      return <Form.Item name="componentProps" {...formListLayout} noStyle initialValue="HH:mm" />;

    case 'metaData':
      return (
        <Form.Item
          label="元数据类型"
          name="componentProps"
          rules={[{ required: true, message: '请选择元数据类型' }]}
        >
          <Select
            style={{ width: 200 }}
            options={Object.keys(MetaTypeTextMap).reduce(
              (options, key) => {
                options.push({
                  // @ts-ignore ts(7053)
                  label: MetaTypeTextMap[key as MetaType],
                  value: key,
                });
                return options;
              },
              [] as { label: string; value: string }[]
            )}
          />
        </Form.Item>
      );
    case 'location':
      return (
        <>
          <Form.Item name="componentProps" {...formListLayout}>
            <Checkbox.Group options={itemOptions.componentOptions} />
          </Form.Item>
          <Form.Item name="nodes" {...formListLayout}>
            <Slider style={{ width: 300 }} marks={itemOptions.spaceMarks} max={5} min={1} range />
          </Form.Item>
        </>
      );
    default:
      return null;
  }
}
