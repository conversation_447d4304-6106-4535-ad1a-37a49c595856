import { createContext, useContext } from 'react';

import type { BorrowReturnDetail as BorrowReturnDetailJson } from '@manyun/resource-hub.service.fetch-borrow-info';

export type Values = {
  borrowReturnDetail: BorrowReturnDetailJson | undefined;
};

export type Handlers = {
  getDetailData: () => void;
};

export type BorrowReturnDetailContextProps = [Values, Handlers];

const initialValue: BorrowReturnDetailContextProps = [
  {
    borrowReturnDetail: undefined,
  },
  {
    getDetailData: () => {},
  },
];

export const BorrowReturnDetailContext =
  createContext<BorrowReturnDetailContextProps>(initialValue);

export const useBorrowReturnDetail = () => useContext(BorrowReturnDetailContext);
