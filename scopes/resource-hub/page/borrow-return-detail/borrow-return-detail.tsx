import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Anchor } from '@manyun/base-ui.ui.anchor';
import { Card } from '@manyun/base-ui.ui.card';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { Operator } from '@manyun/resource-hub.page.borrow-return';
import type { BorrowApplyAsset } from '@manyun/resource-hub.service.fetch-borrow-applys';
import { fetchBorrowApplys } from '@manyun/resource-hub.service.fetch-borrow-applys';
import type { BorrowAsset } from '@manyun/resource-hub.service.fetch-borrow-assets';
import { fetchBorrowAssets } from '@manyun/resource-hub.service.fetch-borrow-assets';
import type { BorrowReturnDetail as BorrowReturnDetailJson } from '@manyun/resource-hub.service.fetch-borrow-info';
import { fetchBorrowInfo } from '@manyun/resource-hub.service.fetch-borrow-info';
import type { BorrowRenewLendRecord } from '@manyun/resource-hub.service.fetch-borrow-renew-lend-records';
import { fetchBorrowRenewLendRecords } from '@manyun/resource-hub.service.fetch-borrow-renew-lend-records';

import { BorrowReturnDetailContext } from './borrow-return-detail-context';
import { BaseInfo } from './components/base-info';
import { LendRecord } from './components/lend-record';
import { OfflineAssets } from './components/offline-assets';
import { OnlineAssets } from './components/online-assets';
import { RenewRecord } from './components/renew-record';

export type AnchorLink = {
  href: string;
  title: string;
};

const anchorLinks: AnchorLink[] = [
  {
    href: 'basic-info',
    title: '基本信息',
  },
  {
    href: 'online-goods',
    title: '线上物资',
  },
  {
    href: 'offline-goods',
    title: '线下物资',
  },
  {
    href: 'renew-record',
    title: '续借记录',
  },
  {
    href: 'lending-record',
    title: '转借记录',
  },
  {
    href: 'operate-record',
    title: '操作记录',
  },
];

export function BorrowReturnDetail() {
  const { id } = useParams<{ id: string }>();

  const [loading, setLoading] = useState(false);
  const [borrowReturnDetail, setBorrowReturnDetail] = useState<BorrowReturnDetailJson>();
  const [onlineBorrowApplyAssets, setOnlinBorrowApplyAssets] = useState<BorrowApplyAsset[]>([]);
  const [offlineBorrowAppleAssets, setOfflineBorrowAppleAssets] = useState<BorrowApplyAsset[]>([]);

  const [onlineBorrowAssets, setOnlinBorrowAssets] = useState<BorrowAsset[]>([]);
  const [offlineBorrowAssets, setOfflineBorrowAssets] = useState<BorrowAsset[]>([]);
  //续转
  const [renewData, setRenewData] = useState<BorrowRenewLendRecord[]>([]);
  const [lendData, setLendData] = useState<BorrowRenewLendRecord[]>([]);

  const getBorrowApplyData = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchBorrowApplys({
      borrowNo: id,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setOnlinBorrowApplyAssets(data.data.filter(item => item.assetType === 'ON_LINE'));
    setOfflineBorrowAppleAssets(data.data.filter(item => item.assetType === 'OFF_LINE'));
  }, [id]);

  const getBorrowInfo = useCallback(async () => {
    const { error, data } = await fetchBorrowInfo({ borrowNo: id });
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      setBorrowReturnDetail(data);
    }
  }, [id]);

  const getBorrowAssetData = useCallback(async () => {
    const { error, data } = await fetchBorrowAssets({
      borrowNo: id,
    });

    if (error) {
      message.error(error.message);
      return;
    }
    setOnlinBorrowAssets(data.data.filter(item => item.assetType === 'ON_LINE'));
    setOfflineBorrowAssets(data.data.filter(item => item.assetType === 'OFF_LINE'));
  }, [id]);

  const getDetailData = useCallback(async () => {
    getBorrowInfo();
    getBorrowAssetData();
    getBorrowApplyData();
  }, [getBorrowApplyData, getBorrowAssetData, getBorrowInfo]);

  const getRenewLendData = useCallback(async () => {
    const { error, data } = await fetchBorrowRenewLendRecords({ borrowNo: id });
    if (error) {
      message.error(error.message);
      return;
    }
    setRenewData(data.data.filter(item => item.opType === 'RENEW'));
    setLendData(data.data.filter(item => item.opType === 'TRANSFER'));
  }, [id]);

  useEffect(() => {
    getDetailData();
    getRenewLendData();
  }, [getDetailData, getRenewLendData]);

  const showOnlineAssets = useMemo(() => {
    return !!onlineBorrowAssets.length || !!onlineBorrowApplyAssets.length;
  }, [onlineBorrowApplyAssets.length, onlineBorrowAssets.length]);

  const showOfflineAssets = useMemo(() => {
    return !!offlineBorrowAssets.length || !!offlineBorrowAppleAssets.length;
  }, [offlineBorrowAppleAssets.length, offlineBorrowAssets.length]);

  return (
    <Card
      style={{
        height: 'calc(var(--content-height) - 40px)',
        overflowY: 'auto',
      }}
      id="borrowReturnDetail"
      loading={loading}
    >
      <BorrowReturnDetailContext.Provider value={[{ borrowReturnDetail }, { getDetailData }]}>
        {borrowReturnDetail && (
          <div style={{ display: 'flex', justifyContent: 'space-between' }}>
            <Space style={{ width: 'calc(100% - 104px)' }} direction="vertical" size={32}>
              <BaseInfo borrowReturnDetail={borrowReturnDetail} anchorLink={anchorLinks[0]} />
              {!!showOnlineAssets && (
                <OnlineAssets
                  blockGuid={borrowReturnDetail.source.blockGuid}
                  borrowStatus={borrowReturnDetail.borrowStatus}
                  onlineBorrowApplyAssets={onlineBorrowApplyAssets}
                  onlineBorrowAssets={onlineBorrowAssets}
                  anchorLink={anchorLinks[1]}
                />
              )}
              {!!showOfflineAssets && (
                <OfflineAssets
                  blockGuid={borrowReturnDetail.source.blockGuid}
                  offlineBorrowAppleAssets={offlineBorrowAppleAssets}
                  offlineBorrowAssets={offlineBorrowAssets}
                  borrowStatus={borrowReturnDetail.borrowStatus}
                  anchorLink={anchorLinks[2]}
                />
              )}

              <RenewRecord
                borrowReturnDetail={borrowReturnDetail}
                renewData={renewData}
                anchorLink={anchorLinks[3]}
                getRenewLendData={getRenewLendData}
              />
              <LendRecord
                borrowReturnDetail={borrowReturnDetail}
                lendData={lendData}
                anchorLink={anchorLinks[4]}
                getRenewLendData={getRenewLendData}
              />
              <Space id={anchorLinks[5].href} style={{ width: '100%' }} direction="vertical">
                <Typography.Title showBadge level={5}>
                  {anchorLinks[5].title}
                </Typography.Title>
                <OperationLogTable
                  defaultSearchParams={{ targetType: 'BORROW', targetId: id }}
                  isTargetIdEqual={targetId => {
                    return targetId === id;
                  }}
                  showColumns={['modifyType']}
                />
              </Space>
            </Space>
            <Anchor
              offsetTop={40}
              getContainer={() => document.getElementById('borrowReturnDetail') as HTMLElement}
            >
              {anchorLinks
                .filter(
                  link =>
                    !(
                      (link.href === 'online-goods' && !showOnlineAssets) ||
                      (link.href === 'offline-goods' && !showOfflineAssets)
                    )
                )
                .map(link => (
                  <Anchor.Link key={link.href} href={`#${link.href}`} title={link.title} />
                ))}
            </Anchor>
          </div>
        )}
      </BorrowReturnDetailContext.Provider>
      {borrowReturnDetail && (
        <FooterToolBar>
          <Operator
            borrowReturn={borrowReturnDetail}
            type="button"
            emptyText=""
            onRefresh={() => {
              getDetailData();
              getRenewLendData();
            }}
          />
        </FooterToolBar>
      )}
    </Card>
  );
}
