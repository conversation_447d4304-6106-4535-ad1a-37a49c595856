import React from 'react';
import { Link } from 'react-router-dom';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { BorrowReturn, getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';
import type { BorrowReturnDetail } from '@manyun/resource-hub.service.fetch-borrow-info';

import type { AnchorLink } from '../../borrow-return-detail';

export type BaseInfoProps = { borrowReturnDetail: BorrowReturnDetail; anchorLink: AnchorLink };

export function BaseInfo({ borrowReturnDetail, anchorLink }: BaseInfoProps) {
  const borrowReturnLocales = getBorrowReturnLocales();

  const descriptionlist = useDeepCompareMemo(() => {
    const list = [
      {
        key: 'borrowNo',
        label: '编号ID',
        text: borrowReturnDetail.borrowNo,
      },
      {
        key: 'title',
        label: '标题',
        text: borrowReturnDetail.title,
      },
      {
        key: 'blockGuid',
        label: '位置',
        text: borrowReturnDetail.source.blockGuid,
      },
      {
        key: 'borrowStatus',
        label: '状态',
        text: borrowReturnLocales['borrowStatus'][borrowReturnDetail.borrowStatus],
      },
      {
        key: 'borrowType',
        label: '借用类型',
        text: borrowReturnLocales['borrowType'][borrowReturnDetail.borrowType],
      },
      {
        key: 'borrower',
        label: '借用人',
        text: (() => {
          if (borrowReturnDetail.borrowType === 'INNER') {
            return borrowReturnDetail.borrower.name;
          }
          return borrowReturnDetail.borrower.name && borrowReturnDetail.borrower.type
            ? `${borrowReturnDetail.borrower.name}(${
                borrowReturnLocales['borrower'][borrowReturnDetail.borrower.type]
              })`
            : null;
        })(),
      },
      {
        key: 'creator',
        label: '创建人',
        text: <UserLink userId={Number(borrowReturnDetail.creator.id)} />,
      },
      {
        key: 'gmtCreate',
        label: '创建时间',
        text: BorrowReturn.fromJSON(borrowReturnDetail).getFormattedCreateAt(),
      },
      {
        key: 'borrowRetuenAt',
        label: '借用起止日期',
        text: `${BorrowReturn.fromJSON(
          borrowReturnDetail
        ).getFormattedBorrowStartAt()}~${BorrowReturn.fromJSON(
          borrowReturnDetail
        ).getFormattedBorrowEndAt()}`,
      },
      {
        key: '借用审批',
        label: '借用审批',
        text:
          !borrowReturnDetail.approvalId || borrowReturnDetail.approvalId === '0' ? (
            '--'
          ) : (
            <Link
              to={generateBPMRoutePath({
                id: borrowReturnDetail.approvalId,
              })}
            >
              {borrowReturnDetail.approvalId}
            </Link>
          ),
      },
      {
        key: 'reason',
        label: '原因',
        text: borrowReturnDetail.reason,
      },
      {
        key: 'fileInfoList',
        label: '附件',
        text: (
          <div style={{ maxWidth: 220 }}>
            {borrowReturnDetail.fileInfoList?.length ? (
              <McUpload
                fileList={borrowReturnDetail.fileInfoList as MixedUploadFile[]}
                allowDelete={false}
                disabled
              />
            ) : (
              '--'
            )}
          </div>
        ),
      },
    ];
    if (borrowReturnDetail.borrowType === 'OUT') {
      list.splice(6, 0, {
        key: 'personLiable',
        label: '负责人',
        text: <UserLink userId={Number(borrowReturnDetail.personLiable.id)} />,
      });
    }
    return list;
  }, [borrowReturnDetail, borrowReturnLocales]);

  return (
    <div id={anchorLink.href}>
      <Descriptions
        title={
          <Typography.Title showBadge level={5}>
            {anchorLink.title}
          </Typography.Title>
        }
        column={4}
      >
        {descriptionlist.map(item => (
          <Descriptions.Item key={item.key} label={item.label}>
            {item.text}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </div>
  );
}
