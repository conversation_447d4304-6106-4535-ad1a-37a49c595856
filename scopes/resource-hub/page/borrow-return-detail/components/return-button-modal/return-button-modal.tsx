import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { getDeviceTypeMetaData, syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { WarehouseRoomSelect } from '@manyun/resource-hub.page.borrow-return';
import type { BorrowAsset } from '@manyun/resource-hub.service.fetch-borrow-assets';
import type { SvcQuery as ReturnBorrowAsset } from '@manyun/resource-hub.service.return-borrow-asset';
import { returnBorrowAsset } from '@manyun/resource-hub.service.return-borrow-asset';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { useBorrowReturnDetail } from '../../borrow-return-detail-context';

export type ReturnButtonModalProps = {
  blockGuid: string;
  borrowAsset: BorrowAsset;
};

export const ReturnButtonModal = ({ blockGuid, borrowAsset }: ReturnButtonModalProps) => {
  const dispatch = useDispatch();

  const deviceCategory = useSelector(getDeviceTypeMetaData);
  const deviceNormalizedList = deviceCategory ? deviceCategory.normalizedList : [];

  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;

  const [{ borrowReturnDetail }, { getDetailData }] = useBorrowReturnDetail();

  const [open, setOpen] = useState(false);

  useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { deviceCategory: 'IF_NULL' } }));
  }, [dispatch]);

  useEffect(() => {
    if (open) {
      setFieldsValue({
        ownerId: undefined,
        returnRoomGuid: undefined,
        returnNum: undefined,
        remark: undefined,
      });
    }
  }, [open, setFieldsValue]);

  const onFinish = async () => {
    if (!borrowReturnDetail) {
      return;
    }
    const values = await validateFields();
    const { ownerId, returnRoomGuid, returnNum, remark } = values;

    let params: ReturnBorrowAsset = {
      borrowNo: borrowReturnDetail.borrowNo,
      ownerId: ownerId.id ?? borrowReturnDetail.borrower.id,
      remark,
      returnAssertInfoList: [
        {
          id: borrowAsset.id,
          assetType: borrowAsset.assetType,
          deviceType: borrowAsset.deviceType,
          deviceTypeName: deviceNormalizedList[borrowAsset.deviceType]?.metaName,
          numbered: borrowAsset.numbered,
          assetNo: borrowAsset.assetNo,
          vendor: borrowAsset.vendor,
          productModel: borrowAsset.productModel,
          returnNum: borrowAsset.numbered ? 1 : returnNum,
        },
      ],
    };
    const { room } = getSpaceGuidMap(returnRoomGuid);
    if (borrowAsset.assetType === 'ON_LINE' && room) {
      params = {
        ...params,
        returnRoomGuid,
        returnRoomTag: room,
      };
    }

    const { error } = await returnBorrowAsset(params);
    setOpen(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('归还成功！');
    getDetailData();
  };

  const { idc, block } = getSpaceGuidMap(blockGuid);
  return (
    <>
      {borrowAsset.borrowNum === borrowAsset.returnNum ? (
        <Tooltip title="物资已归还">
          <Typography.Text style={{ cursor: 'pointer' }} type="secondary">
            归还
          </Typography.Text>
        </Tooltip>
      ) : (
        <Button type="link" compact onClick={() => setOpen(true)}>
          归还
        </Button>
      )}
      <Modal
        title="归还"
        destroyOnClose
        open={open}
        onCancel={() => setOpen(false)}
        onOk={onFinish}
      >
        <Form form={form} colon={false} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
          <Form.Item
            label="归还人"
            name="ownerId"
            rules={[
              {
                required: true,
                message: '归还人必填！',
              },
            ]}
          >
            <UserSelect style={{ width: 280 }} userState="in-service" allowClear />
          </Form.Item>
          {borrowAsset.assetType === 'ON_LINE' && borrowAsset.numbered && idc && block && (
            <Form.Item
              label="归还至"
              name="returnRoomGuid"
              rules={[
                {
                  required: true,
                  message: '位置必填！',
                },
              ]}
            >
              <LocationTreeSelect
                style={{ width: 280 }}
                idc={idc}
                block={block}
                allowClear
                authorizedOnly
                nodeTypes={['IDC', 'BLOCK', 'ROOM']}
                disabledTypes={['IDC', 'BLOCK']}
              />
            </Form.Item>
          )}

          {borrowAsset.assetType === 'ON_LINE' && !borrowAsset.numbered && idc && block && (
            <Form.Item
              label="归还至"
              name="returnRoomGuid"
              rules={[
                {
                  required: true,
                  message: '位置必填！',
                },
              ]}
            >
              <WarehouseRoomSelect style={{ width: 280 }} idcTag={idc} blockTag={block} />
            </Form.Item>
          )}
          {(!borrowAsset.numbered || borrowAsset.assetType === 'OFF_LINE') && (
            <Form.Item
              label="归还数量"
              name="returnNum"
              rules={[
                {
                  required: true,
                  message: '归还数量必填！',
                },
                {
                  max: borrowAsset.borrowNum - borrowAsset.returnNum,
                  type: 'number',
                  message: '不能多于待还数量！',
                },
              ]}
            >
              <InputNumber style={{ width: 160 }} min={1} precision={0} />
            </Form.Item>
          )}

          <Form.Item
            label="备注"
            name="remark"
            rules={[
              {
                max: 120,
                message: '最多输入 120 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 280 }} allowClear />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
