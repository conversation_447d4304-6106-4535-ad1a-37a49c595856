import React from 'react';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';

import { cancelRenewLendBorrow } from '@manyun/resource-hub.service.cancel-renew-lend-borrow';

export type WithdrawButtonModalProps = {
  opId: string;
  modalTitle?: string;
  onCallBack?: () => void;
};
export const WithdrawButtonModal = ({
  opId,
  modalTitle = `确认要撤回吗？`,
  onCallBack,
}: WithdrawButtonModalProps) => {
  const onWithDrawClick = () => {
    DeleteConfirm.confirm({
      icon: <ExclamationCircleFilled />,
      title: modalTitle,
      targetName: '撤回',
      content: '撤回后数据将不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await cancelRenewLendBorrow({ opId });
        if (error) {
          message.error(error.message);
          return Promise.resolve(true);
        }
        message.success('撤回成功！');
        onCallBack && onCallBack();
        return Promise.resolve(true);
      },
    });
  };

  return (
    <Button type="link" compact onClick={onWithDrawClick}>
      撤回
    </Button>
  );
};
