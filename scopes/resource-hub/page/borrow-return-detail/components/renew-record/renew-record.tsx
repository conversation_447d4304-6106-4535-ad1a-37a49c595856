import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import dayjs from 'dayjs';

import { Space } from '@manyun/base-ui.ui.space';
import { type ColumnType, Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import type { BorrowReturnDetail } from '@manyun/resource-hub.service.fetch-borrow-info';
import type { BorrowRenewLendRecord } from '@manyun/resource-hub.service.fetch-borrow-renew-lend-records';

import type { AnchorLink } from '../../borrow-return-detail';
import { WithdrawButtonModal } from '../withdraw-button-modal';

export type RenewRecordProps = {
  borrowReturnDetail: BorrowReturnDetail;
  renewData: BorrowRenewLendRecord[];
  anchorLink: AnchorLink;
  getRenewLendData: () => void;
};

export const approvalStatusMap = {
  APPROVING: '待审批',
  PASS: '已通过',
  REFUSE: '已驳回',
  REVOKE: '已撤回',
};

export function RenewRecord({
  borrowReturnDetail,
  renewData,
  anchorLink,
  getRenewLendData,
}: RenewRecordProps) {
  const renewColumns = useMemo(() => {
    const columns: ColumnType<BorrowRenewLendRecord>[] = [
      {
        title: '编号ID',
        dataIndex: 'opId',
      },
      {
        title: '续借结束日期',
        dataIndex: 'endDate',
        render: (_, record) => dayjs(record.opJson.endDate).format('YYYY-MM-DD'),
      },
      {
        title: '创建人',
        dataIndex: 'operator',
        render: (operator: number) => <UserLink userId={operator} />,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        render: gmtCreate => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '审批',
        dataIndex: 'approvalId',
        render: approvalId =>
          !approvalId || approvalId === '0' ? (
            '--'
          ) : (
            <Link
              to={generateBPMRoutePath({
                id: approvalId,
              })}
            >
              {approvalId}
            </Link>
          ),
      },
      {
        title: '原因',
        dataIndex: ['opJson', 'reason'],
      },
      {
        title: '状态',
        dataIndex: 'approvalStatus',
        render: (_, record) => approvalStatusMap[record.approvalStatus],
      },
      {
        title: '操作',
        dataIndex: 'action',
        render: (_, { approvalStatus, opId, approvalId }) => {
          if (approvalStatus === 'APPROVING') {
            return (
              <WithdrawButtonModal
                opId={opId}
                modalTitle={`确认要撤回${approvalId}吗？`}
                onCallBack={getRenewLendData}
              />
            );
          }
          return '--';
        },
      },
    ];
    return columns;
  }, [getRenewLendData]);

  return (
    <Space id={anchorLink.href} style={{ width: '100%' }} direction="vertical">
      <Typography.Title showBadge level={5}>
        {anchorLink.title}
      </Typography.Title>
      <Table
        rowKey="id"
        dataSource={renewData}
        columns={renewColumns}
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
      />
    </Space>
  );
}
