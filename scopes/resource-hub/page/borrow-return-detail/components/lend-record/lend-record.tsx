import React, { useMemo } from 'react';
import { Link } from 'react-router-dom';

import dayjs from 'dayjs';

import { Space } from '@manyun/base-ui.ui.space';
import { type ColumnType, Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';
import type { BorrowReturnDetail } from '@manyun/resource-hub.service.fetch-borrow-info';
import type {
  ApprovalStatus,
  BorrowRenewLendRecord,
} from '@manyun/resource-hub.service.fetch-borrow-renew-lend-records';

import type { AnchorLink } from '../../borrow-return-detail';
import { WithdrawButtonModal } from '../withdraw-button-modal';

export type LendRecordProps = {
  borrowReturnDetail: BorrowReturnDetail;
  lendData: BorrowRenewLendRecord[];
  anchorLink: AnchorLink;
  getRenewLendData: () => void;
};

export const approvalStatusMap = {
  APPROVING: '待审批',
  PASS: '已通过',
  REFUSE: '已驳回',
  REVOKE: '已撤回',
};

export function LendRecord({
  borrowReturnDetail,
  lendData,
  anchorLink,
  getRenewLendData,
}: LendRecordProps) {
  const locales = getBorrowReturnLocales();

  const lendColumns = useMemo(() => {
    const columns: ColumnType<BorrowRenewLendRecord>[] = [
      {
        title: '编号ID',
        dataIndex: 'opId',
      },
      {
        title: '借用类型',
        dataIndex: 'borrowType',
        render: (_, record) => locales['borrowType'][record.opJson.borrowType],
      },
      {
        title: '借用人',
        dataIndex: 'borrowerName',
        render: (_, record) => {
          if (record.opJson.borrowType === 'OUT') {
            return record.opJson.borrowerName;
          }
          return (
            <Link
              to={generateUserProfileRoutePath({
                id: record.opJson.borrower,
              })}
              target="_blank"
            >
              {record.opJson.borrowerName}
            </Link>
          );
        },
      },
      {
        title: '转借结束日期',
        dataIndex: 'endDate',
        render: (_, record) => dayjs(record.opJson.endDate).format('YYYY-MM-DD'),
      },
      {
        title: '创建人',
        dataIndex: 'operator',
        render: (_, record) => (
          <Link
            to={generateUserProfileRoutePath({
              id: record.operator,
            })}
            target="_blank"
          >
            {record.operatorName}
          </Link>
        ),
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        render: gmtCreate => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '审批',
        dataIndex: 'approvalId',
        render: approvalId =>
          !approvalId || approvalId === '0' ? (
            '--'
          ) : (
            <Link
              to={generateBPMRoutePath({
                id: approvalId,
              })}
            >
              {approvalId}
            </Link>
          ),
      },
      {
        title: '原因',
        dataIndex: ['opJson', 'reason'],
      },
      {
        title: '状态',
        dataIndex: 'approvalStatus',
        render: (approvalStatus: ApprovalStatus) => approvalStatusMap[approvalStatus],
      },
      {
        title: '操作',
        dataIndex: 'action',
        render: (_, { approvalStatus, opId, approvalId }) => {
          if (approvalStatus === 'APPROVING') {
            return (
              <WithdrawButtonModal
                opId={opId}
                modalTitle={`确认要撤回${approvalId}吗？`}
                onCallBack={getRenewLendData}
              />
            );
          }
          return '--';
        },
      },
    ];

    return columns;
  }, [getRenewLendData, locales]);

  return (
    <Space style={{ width: '100%' }} id={anchorLink.href} direction="vertical">
      <Typography.Title showBadge level={5}>
        {anchorLink.title}
      </Typography.Title>
      <Table
        rowKey="id"
        dataSource={lendData}
        columns={lendColumns}
        scroll={{ x: 'max-content' }}
        tableLayout="fixed"
      />
    </Space>
  );
}
