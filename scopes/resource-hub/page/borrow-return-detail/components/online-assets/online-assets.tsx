import dayjs from 'dayjs';
import React, { useMemo } from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Space } from '@manyun/base-ui.ui.space';
import { type ColumnType, Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BorrowStatus } from '@manyun/resource-hub.model.borrow-return';
import type { BorrowApplyAsset } from '@manyun/resource-hub.service.fetch-borrow-applys';
import type { BorrowAsset } from '@manyun/resource-hub.service.fetch-borrow-assets';
import type { BorrowAssetTableProps } from '@manyun/resource-hub.ui.borrow-asset-table';
import { BorrowAssetTable } from '@manyun/resource-hub.ui.borrow-asset-table';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

import type { AnchorLink } from '../../borrow-return-detail';
import { useBorrowReturnDetail } from '../../borrow-return-detail-context';
import { AssetCard } from '../asset-card';
import { ReturnButtonModal } from '../return-button-modal';

export type OnlineAssetsProps = {
  blockGuid: string;
  onlineBorrowApplyAssets: BorrowApplyAsset[];
  onlineBorrowAssets: BorrowAsset[];
  borrowStatus: BorrowStatus;
  anchorLink: AnchorLink;
};

export function OnlineAssets({
  blockGuid,
  onlineBorrowApplyAssets,
  onlineBorrowAssets,
  borrowStatus,
  anchorLink,
}: OnlineAssetsProps) {
  const [{ borrowReturnDetail }] = useBorrowReturnDetail();

  const operation: BorrowAssetTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        if (borrowStatus === 'BORROWING' && borrowReturnDetail?.hasPermission) {
          return <ReturnButtonModal blockGuid={blockGuid} borrowAsset={record} />;
        }
        return '--';
      },
    };
  }, [blockGuid, borrowReturnDetail?.hasPermission, borrowStatus]);

  const returnColumns: ColumnType<BorrowAsset>[] = useMemo(() => {
    return [
      {
        title: '资产ID',
        dataIndex: 'assetNo',
        fixed: 'left',
        render: (_, record) => {
          if (!record.assetNo || !record.deviceGuid) {
            return '--';
          }
          return (
            <SpaceOrDeviceLink id={record.deviceGuid} type="DEVICE_GUID" text={record.assetNo} />
          );
        },
      },
      {
        title: '三级分类',
        dataIndex: 'deviceType',
        render: deviceType => <DeviceTypeText code={deviceType} />,
      },
      {
        title: '品牌',
        dataIndex: 'vendor',
      },
      {
        title: '型号',
        dataIndex: 'productModel',
      },
      {
        title: '归还人',
        dataIndex: 'ownerId',
        render: text => <UserLink userId={text} />,
      },
      {
        title: '归还日期',
        dataIndex: 'gmtModified',
        render: gmtModified => dayjs(gmtModified).format('YYYY-MM-DD'),
      },
      {
        title: '归还目的位置',
        dataIndex: 'roomGuid',
      },
      {
        title: '归还数量',
        dataIndex: 'returnNum',
      },
      {
        title: '创建人',
        dataIndex: 'creatorId',
        render: text => <UserLink userId={text} />,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        render: gmtCreate => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '备注',
        dataIndex: 'remark',
      },
    ];
  }, []);

  const tabItems = useDeepCompareMemo(() => {
    const returnRecordTableMap = [
      {
        key: 'returnRecordDevice',
        title: '设备',
        columns: returnColumns.filter(item => item.dataIndex !== 'returnNum'),
        dataSource: onlineBorrowAssets.filter(
          item => item.numbered && item.borrowType === 'RETURN'
        ),
      },
      {
        key: 'returnRecordSpare',
        title: '耗材',
        columns: returnColumns.filter(item => item.dataIndex !== 'assetNo'),
        dataSource: onlineBorrowAssets.filter(
          item => !item.numbered && item.borrowType === 'RETURN'
        ),
      },
    ];
    return [
      {
        key: 'borrowRecord',
        label: '借用记录',
        children: (
          <>
            <BorrowAssetTable
              rowKey="id"
              title={() => '设备'}
              pagination={false}
              operation={operation}
              dataSource={onlineBorrowAssets.filter(
                item => item.numbered && item.borrowType === 'BORROW'
              )}
            />
            <BorrowAssetTable
              rowKey="id"
              title={() => '耗材'}
              dataIndexs={[
                'deviceType',
                'vendor',
                'productModel',
                'borrowNum',
                'toBeReturned',
                'ownerId',
              ]}
              operation={operation}
              pagination={false}
              dataSource={onlineBorrowAssets.filter(
                item => !item.numbered && item.borrowType === 'BORROW'
              )}
            />
          </>
        ),
      },
      {
        key: 'returnRecord',
        label: '归还记录',
        children: (
          <>
            {returnRecordTableMap.map(item => (
              <Table
                key={item.key}
                rowKey="id"
                title={() => item.title}
                columns={item.columns}
                scroll={{ x: 'max-content' }}
                tableLayout="fixed"
                pagination={false}
                dataSource={item.dataSource}
              />
            ))}
          </>
        ),
      },
    ];
  }, [onlineBorrowAssets, operation, returnColumns]);

  return (
    <Space style={{ width: '100%' }} id={anchorLink.href} direction="vertical">
      <Typography.Title showBadge level={5}>
        {anchorLink.title}
      </Typography.Title>
      <div style={{ display: 'flex', overflowX: 'auto' }}>
        <Space size={24}>
          {onlineBorrowApplyAssets.map(item => (
            <AssetCard
              key={item.id}
              blockGuid={blockGuid}
              borrowStatus={borrowStatus}
              borrowApplyAsset={item}
            />
          ))}
        </Space>
      </div>
      <Tabs defaultActiveKey="borrowRecord" items={tabItems} />
    </Space>
  );
}
