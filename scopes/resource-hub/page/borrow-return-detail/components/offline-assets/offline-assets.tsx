import dayjs from 'dayjs';
import React, { useMemo } from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Space } from '@manyun/base-ui.ui.space';
import { type ColumnType, Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BorrowStatus } from '@manyun/resource-hub.model.borrow-return';
import type { BorrowApplyAsset } from '@manyun/resource-hub.service.fetch-borrow-applys';
import type { BorrowAsset } from '@manyun/resource-hub.service.fetch-borrow-assets';
import type { BorrowAssetTableProps } from '@manyun/resource-hub.ui.borrow-asset-table';
import { BorrowAssetTable } from '@manyun/resource-hub.ui.borrow-asset-table';

import type { AnchorLink } from '../../borrow-return-detail';
import { useBorrowReturnDetail } from '../../borrow-return-detail-context';
import { AssetCard } from '../asset-card';
import { ReturnButtonModal } from '../return-button-modal';

export type OfflineAssetsProps = {
  blockGuid: string;
  offlineBorrowAppleAssets: BorrowApplyAsset[];
  offlineBorrowAssets: BorrowAsset[];
  borrowStatus: BorrowStatus;
  anchorLink: AnchorLink;
};

export function OfflineAssets({
  blockGuid,
  offlineBorrowAppleAssets,
  offlineBorrowAssets,
  borrowStatus,
  anchorLink,
}: OfflineAssetsProps) {
  const [{ borrowReturnDetail }] = useBorrowReturnDetail();

  const operation: BorrowAssetTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => {
        if (borrowStatus === 'BORROWING' && borrowReturnDetail?.hasPermission) {
          return <ReturnButtonModal blockGuid={blockGuid} borrowAsset={record} />;
        }
        return '--';
      },
    };
  }, [blockGuid, borrowReturnDetail?.hasPermission, borrowStatus]);

  const returnColumns: ColumnType<BorrowAsset>[] = useMemo(() => {
    return [
      {
        title: '物资分类',
        dataIndex: 'deviceType',
      },
      {
        title: 'SN',
        dataIndex: 'assetNo',
      },
      {
        title: '品牌',
        dataIndex: 'vendor',
      },
      {
        title: '型号',
        dataIndex: 'productModel',
      },
      {
        title: '归还人',
        dataIndex: 'ownerId',
        render: id => <UserLink userId={id} />,
      },
      {
        title: '归还日期',
        dataIndex: 'gmtModified',
        render: gmtModified => dayjs(gmtModified).format('YYYY-MM-DD'),
      },
      {
        title: '归还数量',
        dataIndex: 'returnNum',
      },
      {
        title: '创建人',
        dataIndex: 'creatorId',
        render: id => <UserLink userId={id} />,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreate',
        render: gmtCreate => dayjs(gmtCreate).format('YYYY-MM-DD HH:mm:ss'),
      },
      {
        title: '备注',
        dataIndex: 'remark',
      },
    ];
  }, []);

  const tabItems = useDeepCompareMemo(() => {
    return [
      {
        key: 'borrowRecord',
        label: '借用记录',
        children: (
          <BorrowAssetTable
            rowKey="id"
            dataIndexs={[
              'deviceType',
              'assetNo',
              'vendor',
              'productModel',
              'borrowNum',
              'toBeReturned',
              'ownerId',
            ]}
            assetNoTitle="SN"
            deviceTypeTitle="物资分类"
            operation={operation}
            pagination={false}
            dataSource={offlineBorrowAssets.filter(item => item.borrowType === 'BORROW')}
          />
        ),
      },
      {
        key: 'returnRecord',
        label: '归还记录',
        children: (
          <Table
            rowKey="id"
            columns={returnColumns}
            scroll={{ x: 'max-content' }}
            tableLayout="fixed"
            pagination={false}
            dataSource={offlineBorrowAssets.filter(item => item.borrowType === 'RETURN')}
          />
        ),
      },
    ];
  }, [offlineBorrowAssets, operation, returnColumns]);

  return (
    <Space id={anchorLink.href} style={{ width: '100%' }} direction="vertical">
      <Typography.Title showBadge level={5}>
        {anchorLink.title}
      </Typography.Title>
      <div style={{ display: 'flex', overflowX: 'auto' }}>
        <Space size={24}>
          {offlineBorrowAppleAssets.map(item => (
            <AssetCard
              key={item.id}
              blockGuid={blockGuid}
              borrowStatus={borrowStatus}
              borrowApplyAsset={item}
            />
          ))}
        </Space>
      </div>
      <Tabs defaultActiveKey="borrowRecord" items={tabItems} />
    </Space>
  );
}
