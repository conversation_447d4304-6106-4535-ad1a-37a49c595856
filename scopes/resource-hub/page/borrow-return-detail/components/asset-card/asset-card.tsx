import React, { useMemo } from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import type { BorrowStatus } from '@manyun/resource-hub.model.borrow-return';
import type { BorrowApplyAsset } from '@manyun/resource-hub.service.fetch-borrow-applys';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import { useBorrowReturnDetail } from '../../borrow-return-detail-context';
import { BorrowButtonModal } from './components/borrow-button-modal';

export type AssetCardProps = {
  blockGuid: string;
  borrowApplyAsset: BorrowApplyAsset;
  borrowStatus: BorrowStatus;
};

export function AssetCard({ blockGuid, borrowApplyAsset, borrowStatus }: AssetCardProps) {
  const [{ borrowReturnDetail }] = useBorrowReturnDetail();

  const descriptionlist = useMemo(() => {
    const list = [
      {
        key: 'vendor',
        label: '品牌',
        text: borrowApplyAsset.vendor ?? '--',
        span: 5,
      },
      {
        key: 'applyNum',
        label: <Badge status="processing" text="申请数量" />,
        text: borrowApplyAsset.applyNum,
        span: 3,
      },
      {
        key: 'productModel',
        label: '型号',
        text: borrowApplyAsset.productModel,
        span: 5,
      },
      {
        key: 'stockOutNum',
        label: <Badge status="success" text="借用数量" />,
        text: borrowApplyAsset.stockOutNum,
        span: 3,
      },
      {
        key: 'targetRoomGuid',
        label: '借用至目的位置',
        text: borrowApplyAsset.targetRoomGuid,
        span: 5,
      },
    ];
    if (borrowApplyAsset.stockOutNum > 0) {
      list.push({
        key: 'toBeReturned',
        label: <Badge status="error" text="待还数量" />,
        text: borrowApplyAsset.stockOutNum - borrowApplyAsset.returnNum,
        span: 3,
      });
    }
    return list;
  }, [
    borrowApplyAsset.applyNum,
    borrowApplyAsset.productModel,
    borrowApplyAsset.returnNum,
    borrowApplyAsset.stockOutNum,
    borrowApplyAsset.targetRoomGuid,
    borrowApplyAsset.vendor,
  ]);

  return (
    <Card bodyStyle={{ width: 442, paddingBottom: 12 }}>
      <Descriptions
        size="small"
        title={
          borrowApplyAsset.assetType === 'ON_LINE' ? (
            <DeviceTypeText code={borrowApplyAsset.deviceType} />
          ) : (
            borrowApplyAsset.deviceType
          )
        }
        extra={
          blockGuid &&
          borrowStatus === 'WAIT_STOCK_OUT' &&
          borrowApplyAsset.stockOutNum < borrowApplyAsset.applyNum &&
          borrowReturnDetail?.hasPermission && (
            <BorrowButtonModal blockGuid={blockGuid} borrowApplyAsset={borrowApplyAsset} />
          )
        }
        column={8}
      >
        {descriptionlist.map(item => (
          <Descriptions.Item key={item.key} label={item.label} span={item.span}>
            {item.text}
          </Descriptions.Item>
        ))}
      </Descriptions>
    </Card>
  );
}
