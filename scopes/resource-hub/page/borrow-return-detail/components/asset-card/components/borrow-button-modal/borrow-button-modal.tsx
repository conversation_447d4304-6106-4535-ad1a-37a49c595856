import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { SvcQuery as borrowAssetParmas } from '@manyun/resource-hub.service.borrow-asset';
import { borrowAsset } from '@manyun/resource-hub.service.borrow-asset';
import type { BorrowApplyAsset } from '@manyun/resource-hub.service.fetch-borrow-applys';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';
import { DeviceSelect } from '@manyun/resource-hub.ui.device-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { useBorrowReturnDetail } from '../../../../borrow-return-detail-context';

export type BorrowButtonModalProps = {
  blockGuid: string;
  borrowApplyAsset: BorrowApplyAsset;
};

export const BorrowButtonModal = ({ blockGuid, borrowApplyAsset }: BorrowButtonModalProps) => {
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;

  const [{ borrowReturnDetail }, { getDetailData }] = useBorrowReturnDetail();

  const [assetNo, setAssetNo] = useState<string>();
  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      setFieldsValue({
        assetNo: undefined,
        borrowRoomGuid: undefined,
        ownerId: undefined,
        borrowNum: undefined,
        vendor: borrowApplyAsset.vendor,
        productModel: borrowApplyAsset.productModel,
      });
      setAssetNo(undefined);
    }
  }, [borrowApplyAsset.productModel, borrowApplyAsset.vendor, open, setFieldsValue]);

  const onFinish = async () => {
    if (!borrowReturnDetail) {
      return;
    }
    const values = await validateFields();
    const { assetNo, borrowRoomGuid, ownerId, borrowNum } = values;

    let params: borrowAssetParmas = {
      borrowNo: borrowReturnDetail.borrowNo,
      borrowApplyId: borrowApplyAsset.id,
    };

    if (borrowApplyAsset.assetType === 'ON_LINE' && borrowApplyAsset.numbered) {
      const { data } = await fetchDeviceByGuid({ guid: assetNo });

      params = {
        ...params,
        assetNo: data?.asset.no!,
        borrowRoomGuid,
        borrowRoomTag: getSpaceGuidMap(borrowRoomGuid).room!,
        ownerId: ownerId.id,
      };
    }

    if (borrowApplyAsset.assetType === 'ON_LINE' && !borrowApplyAsset.numbered) {
      params = {
        ...params,
        ownerId: ownerId.id,
        borrowNum,
      };
    }

    if (borrowApplyAsset.assetType === 'OFF_LINE') {
      params = {
        ...params,
        ownerId: ownerId.id,
        assetNo,
        borrowNum,
      };
    }

    const { error } = await borrowAsset(params);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('借用成功！');
    setOpen(false);
    getDetailData();
  };

  const { idc, block } = getSpaceGuidMap(blockGuid);
  return (
    <>
      <Button type="primary" onClick={() => setOpen(true)}>
        借用
      </Button>

      <Modal
        title="借用"
        open={open}
        destroyOnClose
        onCancel={() => setOpen(false)}
        onOk={onFinish}
      >
        <Form form={form} colon={false} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
          {borrowApplyAsset.assetType === 'ON_LINE' && borrowApplyAsset.numbered && (
            <>
              {idc && block && (
                <Form.Item
                  label="设备编号"
                  name="assetNo"
                  rules={[
                    {
                      required: true,
                      message: '设备编号必填！',
                    },
                  ]}
                >
                  <DeviceSelect
                    style={{ width: 280 }}
                    idcTag={idc}
                    blockTag={block}
                    assetState="NORMAL"
                    deviceTypeList={[borrowApplyAsset.deviceType]}
                    vendor={borrowApplyAsset.vendor}
                    productModel={borrowApplyAsset.productModel}
                    placeholder="请输入设备编号"
                    allowClear
                    onSelect={(_, option) => setAssetNo(option.asset?.no)}
                    onClear={() => setAssetNo(undefined)}
                  />
                </Form.Item>
              )}
              {idc && block && assetNo && (
                <Form.Item label="资产id">
                  <Input style={{ width: 280 }} disabled value={assetNo} />
                </Form.Item>
              )}
              {idc && blockGuid && (
                <Form.Item
                  label="借用至目的位置"
                  name="borrowRoomGuid"
                  rules={[
                    {
                      required: true,
                      message: '位置必填！',
                    },
                  ]}
                >
                  <LocationTreeSelect
                    style={{ width: 280 }}
                    allowClear
                    authorizedOnly
                    treeDefaultExpandedKeys={[idc, blockGuid]}
                    nodeTypes={['IDC', 'BLOCK', 'ROOM']}
                    disabledTypes={['IDC', 'BLOCK']}
                  />
                </Form.Item>
              )}

              <Form.Item
                label="领用人"
                name="ownerId"
                rules={[
                  {
                    required: true,
                    message: '领用人必填！',
                  },
                ]}
              >
                <UserSelect style={{ width: 280 }} userState="in-service" allowClear />
              </Form.Item>
            </>
          )}
          {borrowApplyAsset.assetType === 'ON_LINE' && !borrowApplyAsset.numbered && (
            <>
              <Form.Item
                label="领用人"
                name="ownerId"
                rules={[
                  {
                    required: true,
                    message: '领用人必填！',
                  },
                ]}
              >
                <UserSelect style={{ width: 210 }} userState="in-service" allowClear />
              </Form.Item>

              <Form.Item
                label="借出数量"
                name="borrowNum"
                rules={[
                  {
                    required: true,
                    message: '借出数量必填！',
                  },
                  {
                    max: borrowApplyAsset.applyNum - borrowApplyAsset.stockOutNum,
                    type: 'number',
                    message: `不能超过${borrowApplyAsset.applyNum - borrowApplyAsset.stockOutNum}`,
                  },
                ]}
              >
                <InputNumber style={{ width: 160 }} min={1} precision={0} />
              </Form.Item>
            </>
          )}

          {borrowApplyAsset.assetType === 'OFF_LINE' && (
            <>
              <Form.Item
                label="领用人"
                name="ownerId"
                rules={[
                  {
                    required: true,
                    message: '领用人必填！',
                  },
                ]}
              >
                <UserSelect style={{ width: 280 }} userState="in-service" allowClear />
              </Form.Item>
              <Form.Item
                label="SN"
                name="assetNo"
                rules={[
                  {
                    max: 64,
                    message: '最多输入 64 个字符！',
                  },
                ]}
              >
                <Input style={{ width: 280 }} />
              </Form.Item>
              <Form.Item label="品牌" name="vendor">
                <Input style={{ width: 280 }} disabled />
              </Form.Item>
              <Form.Item label="型号" name="productModel">
                <Input style={{ width: 280 }} disabled />
              </Form.Item>
              <Form.Item
                label="借出数量"
                name="borrowNum"
                rules={[
                  {
                    required: true,
                    message: '借出数量必填！',
                  },
                  {
                    max: borrowApplyAsset.applyNum - borrowApplyAsset.stockOutNum,
                    type: 'number',
                    message: `不能超过${borrowApplyAsset.applyNum - borrowApplyAsset.stockOutNum}`,
                  },
                ]}
              >
                <InputNumber style={{ width: 160 }} min={1} precision={0} />
              </Form.Item>
            </>
          )}
        </Form>
      </Modal>
    </>
  );
};
