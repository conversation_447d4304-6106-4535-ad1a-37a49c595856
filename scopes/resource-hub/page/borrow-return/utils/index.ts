import dayjs from 'dayjs';
import type { Moment } from 'moment';

export function getRenewOrLendTimeDisabled({
  minDate,
  current,
  variant = 'renew',
}: {
  minDate: number;
  current: Moment;
  variant: 'lend' | 'renew';
}) {
  const minEndDate = dayjs(minDate).clone().endOf('day').valueOf();
  const today = dayjs().endOf('day').valueOf();
  const currentTime = current.endOf('day').valueOf();
  if (variant === 'lend') {
    return currentTime < minEndDate;
  }
  return currentTime <= minEndDate || currentTime < today;
}
