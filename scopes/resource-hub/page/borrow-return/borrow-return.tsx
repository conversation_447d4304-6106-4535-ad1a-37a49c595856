import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import type { BorrowReturnJSON } from '@manyun/resource-hub.model.borrow-return';
import { BORROWS_AND_RETURN_CREATE } from '@manyun/resource-hub.route.resource-routes';
import { fetchBorrows } from '@manyun/resource-hub.service.fetch-borrows';

import type { FormParams } from './components/borrow-return-filter';
import { BorrowReturnFilter } from './components/borrow-return-filter';
import type { BorrowReturnTableProps } from './components/borrow-return-table';
import { BorrowReturnTable } from './components/borrow-return-table';
import { Operator } from './components/operator';

export function BorrowReturn() {
  const [borrowReturnList, setBorrowReturnList] = useState<BorrowReturnJSON[]>([]);

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({ pageNum: 1, pageSize: 10, total: 0 });

  const filterParamsRef = useRef<FormParams | undefined>();

  const [loading, setLoading] = useState(false);

  const getBorrowReturnList = useCallback(
    async ({ pageNum, pageSize }: { pageNum: number; pageSize: number }) => {
      setLoading(true);
      const { data, error } = await fetchBorrows({ pageNum, pageSize, ...filterParamsRef.current });
      setLoading(false);

      if (!error) {
        setBorrowReturnList(data.data);
        setPagination({ pageNum, pageSize, total: data.total });
      } else {
        message.error(error.message);
      }
    },
    []
  );

  const onRefresh = useCallback(() => {
    getBorrowReturnList({ pageNum: pagination.pageNum, pageSize: pagination.pageSize });
  }, [getBorrowReturnList, pagination.pageNum, pagination.pageSize]);

  useEffect(() => {
    onRefresh();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const operation: BorrowReturnTableProps['operation'] = useMemo(() => {
    return {
      title: '操作',
      fixed: 'right',
      render: (_, record) => <Operator borrowReturn={record} onRefresh={onRefresh} />,
    };
  }, [onRefresh]);

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={24}>
      <BorrowReturnFilter
        onChange={params => {
          filterParamsRef.current = params;
          getBorrowReturnList({ pageNum: 1, pageSize: pagination.pageSize });
        }}
      />
      <Card>
        <Space style={{ width: '100%' }} direction="vertical">
          <Button type="primary" href={BORROWS_AND_RETURN_CREATE}>
            新建
          </Button>
          <BorrowReturnTable
            loading={loading}
            rowKey="id"
            dataSource={borrowReturnList}
            operation={operation}
            pagination={{
              total: pagination.total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: (pageNum, pageSize) => {
                getBorrowReturnList({ pageNum, pageSize });
              },
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}
