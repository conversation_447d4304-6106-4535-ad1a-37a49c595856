import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchRoomList } from '@manyun/resource-hub.service.fetch-room-list';

export type WarehouseRoomSelectProps = Omit<SelectProps, 'options'> & {
  idcTag: string;
  blockTag: string;
};

export const WarehouseRoomSelect = React.forwardRef(
  ({ idcTag, blockTag, ...reset }: WarehouseRoomSelectProps, ref: React.Ref<RefSelectProps>) => {
    const [options, setOptions] = useState<SelectProps['options']>([]);

    useEffect(() => {
      (async () => {
        const { error, data } = await fetchRoomList({
          pageNum: 1,
          pageSize: 200,
          idcTag: idcTag,
          blockTag: blockTag,
          roomType: 'WAREHOUSE',
        });
        if (error) {
          message.error(error.message);
          return;
        }
        const list = data.data.map(({ guid, name }) => {
          return { value: guid, label: name };
        });
        setOptions(list);
      })();
    }, [blockTag, idcTag]);

    return <Select {...reset} ref={ref} options={options} />;
  }
);

WarehouseRoomSelect.displayName = 'WarehouseRoomSelect';
