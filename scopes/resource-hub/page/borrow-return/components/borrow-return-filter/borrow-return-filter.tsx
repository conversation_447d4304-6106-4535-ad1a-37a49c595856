import React, { useMemo } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { BorrowStatus } from '@manyun/resource-hub.model.borrow-return';
import type { SvcQuery as fetchBorrowsParams } from '@manyun/resource-hub.service.fetch-borrows';
import { BorrowStatusSelect } from '@manyun/resource-hub.ui.borrow-status-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export type FormParams = Omit<fetchBorrowsParams, 'pageSize' | 'pageNum'>;

export type BorrowReturnFilterProps = { onChange: (params?: FormParams) => void };

export function BorrowReturnFilter({ onChange }: BorrowReturnFilterProps) {
  const [form] = Form.useForm();

  const items = useMemo(
    () => [
      {
        label: '编号ID',
        name: 'borrowNo',
        control: <Input allowClear />,
      },
      {
        label: '资产ID',
        name: 'assetNo',
        control: <Input allowClear />,
      },
      {
        label: '标题',
        name: 'title',
        control: <Input allowClear />,
      },
      {
        label: '位置',
        name: 'blockGuid',
        control: <LocationTreeSelect allowClear authorizedOnly />,
      },
      {
        label: '借用人',
        name: 'borrowerName',
        control: <Input allowClear placeholder="请输入关键字搜索" />,
      },
      {
        label: '状态',
        name: 'borrowStatus',
        control: <BorrowStatusSelect allowClear showSearch />,
      },
      {
        label: '创建人',
        name: 'creator',
        control: <UserSelect allowClear />,
      },
      {
        label: '创建日期',
        name: 'createTimeRange',
        span: 2,
        control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
      },
      {
        label: '借用结束日期',
        name: 'borrowTimeRange',
        span: 2,
        control: <DatePicker.RangePicker format="YYYY-MM-DD" />,
      },
    ],
    []
  );

  return (
    <Card>
      <QueryFilter
        form={form}
        items={items}
        onSearch={params => {
          const {
            borrowNo,
            assetNo,
            title,
            blockGuid,
            borrowStatus,
            borrowerName,
            creator,
            borrowTimeRange,
            createTimeRange,
          } = params;
          const { idc, block } = getSpaceGuidMap(blockGuid);

          onChange({
            borrowNo,
            assetNo,
            title,
            idcTag: idc ?? undefined,
            blockGuid: idc && block ? blockGuid : undefined,
            borrowStatusList: borrowStatus ? [borrowStatus as BorrowStatus] : undefined,
            borrowerName,
            creatorId: creator?.id,
            borrowStartTime:
              Array.isArray(borrowTimeRange) && borrowTimeRange.length > 1
                ? borrowTimeRange[0].startOf('day').valueOf()
                : undefined,
            borrowEndTime:
              Array.isArray(borrowTimeRange) && borrowTimeRange.length > 1
                ? borrowTimeRange[1].endOf('day').valueOf()
                : undefined,
            createStartTime:
              Array.isArray(createTimeRange) && createTimeRange.length > 1
                ? createTimeRange[0].startOf('day').valueOf()
                : undefined,
            createEndTime:
              Array.isArray(createTimeRange) && createTimeRange.length > 1
                ? createTimeRange[1].endOf('day').valueOf()
                : undefined,
          });
        }}
        onReset={() => {
          onChange();
          form.resetFields();
        }}
      />
    </Card>
  );
}
