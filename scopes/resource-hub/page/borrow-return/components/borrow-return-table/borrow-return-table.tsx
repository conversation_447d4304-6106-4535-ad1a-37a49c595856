import React from 'react';
import { Link } from 'react-router-dom';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { BorrowReturn, getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';
import type {
  BorrowReturnJSON,
  BorrowReturnLocales,
  BorrowType,
} from '@manyun/resource-hub.model.borrow-return';
import { generateBorrowAndReturnDetailLocation } from '@manyun/resource-hub.route.resource-routes';
import { BorrowStatusText } from '@manyun/resource-hub.ui.borrow-status-text';

const getBorrowReturnColumns: (locales: BorrowReturnLocales) => ColumnType<BorrowReturnJSON>[] = (
  locales: BorrowReturnLocales
) => {
  return [
    {
      title: locales['borrowNo'],
      dataIndex: 'borrowNo',
      fixed: 'left',
      render: borrowNo => (
        <Link to={generateBorrowAndReturnDetailLocation({ id: borrowNo })} target="_blank">
          {borrowNo}
        </Link>
      ),
    },
    {
      title: locales['title'],
      dataIndex: 'title',
    },
    {
      title: locales['source']['__self'],
      dataIndex: 'blockGuid',
      render: (_, record) => record.source.blockGuid,
    },
    {
      title: locales['borrowType']['__self'],
      dataIndex: 'borrowType',
      render: borrowType => locales['borrowType'][borrowType as BorrowType],
    },
    {
      title: locales['borrower']['__self'],
      dataIndex: 'borrower',
      render: (_, record) => {
        if (record.borrowType === 'OUT') {
          return record.borrower.name;
        }
        return <UserLink userId={Number(record.borrower.id)} />;
      },
    },
    {
      title: locales['borrowDate']['__self'],
      dataIndex: 'borrowRetuenAt',
      render: (_, record) => {
        return `${BorrowReturn.fromJSON(
          record
        ).getFormattedBorrowStartAt()}~${BorrowReturn.fromJSON(record).getFormattedBorrowEndAt()}`;
      },
    },
    {
      title: locales['creator'],
      dataIndex: 'creator',
      render: (_, record) => <UserLink userId={Number(record.creator.id)} />,
    },
    {
      title: locales['createAt'],
      dataIndex: 'createAt',
      render: (_, record) => BorrowReturn.fromJSON(record).getFormattedCreateAt(),
    },
    {
      title: locales['borrowStatus']['__self'],
      dataIndex: 'borrowStatus',
      render: borrowStatus => <BorrowStatusText code={borrowStatus} />,
    },
  ];
};

export type ColumnDataIndex =
  | 'borrowNo'
  | 'title'
  | 'blockGuid'
  | 'borrowType'
  | 'borrower'
  | 'borrowRetuenAt'
  | 'creator'
  | 'createAt'
  | 'borrowStatus';

export type BorrowReturnTableProps = {
  dataIndexs?: ColumnDataIndex[];
  operation?: ColumnType<BorrowReturnJSON>;
} & Omit<TableProps<BorrowReturnJSON>, 'columns'>;

export function BorrowReturnTable({
  dataIndexs = [
    'borrowNo',
    'title',
    'blockGuid',
    'borrowType',
    'borrower',
    'borrowRetuenAt',
    'creator',
    'createAt',
    'borrowStatus',
  ],
  operation,
  ...rest
}: BorrowReturnTableProps) {
  const columns = useDeepCompareMemo(() => {
    const locales = getBorrowReturnLocales();
    const newColumns = dataIndexs
      .map(dataIndex => {
        return getBorrowReturnColumns(locales).find(item => item.dataIndex === dataIndex);
      })
      .filter((item): item is ColumnType<BorrowReturnJSON> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      rowKey="id"
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      {...rest}
    />
  );
}
