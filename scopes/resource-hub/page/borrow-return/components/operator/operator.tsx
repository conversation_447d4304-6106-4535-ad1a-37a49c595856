import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import type { BorrowReturnJSON } from '@manyun/resource-hub.model.borrow-return';
import { generateBorrowAndReturnEditLocation } from '@manyun/resource-hub.route.resource-routes';

import { CancelBorrowConfirm } from './components/cancel-borrow-confirm';
import { FinishWarehouse } from './components/finish-warehouse-confirm';
import { LendButtonModal } from './components/lend-button-modal';
import { RenewButtonModal } from './components/renew-button-modal';
import { ReturnButtonModal } from './components/return-button-modal';
import { WithDrawConfirm } from './components/with-draw-confirm';

export type OperatorProps = {
  borrowReturn: BorrowReturnJSON;
  type?: 'link' | 'button';
  emptyText?: string;
  onRefresh: () => void;
};

export function Operator({
  borrowReturn,
  type = 'link',
  emptyText = '--',
  onRefresh,
}: OperatorProps) {
  if (!borrowReturn.hasPermission) {
    return <>{emptyText}</>;
  }

  const buttonProps: ButtonProps =
    type === 'link' ? { type: 'link', compact: true } : { type: 'primary' };
  const behindButtonProps: ButtonProps =
    type === 'link' ? { type: 'link', compact: true } : { type: 'default' };

  if (borrowReturn.borrowStatus === 'APPROVING') {
    //待审批 btn：撤回
    return (
      <WithDrawConfirm
        borrowNo={borrowReturn.borrowNo}
        buttonProps={buttonProps}
        onCallBack={onRefresh}
      />
    );
  }

  if (borrowReturn.borrowStatus === 'DRAFT') {
    //草稿  btn：取消 重新发起

    return (
      <Space split={type === 'link' && <Divider type="vertical" />} size={type === 'link' ? 0 : 16}>
        <CancelBorrowConfirm
          borrowNo={borrowReturn.borrowNo}
          buttonProps={buttonProps}
          onCallBack={onRefresh}
        />

        <Button
          {...behindButtonProps}
          href={generateBorrowAndReturnEditLocation({
            id: borrowReturn.borrowNo,
          })}
        >
          重新发起
        </Button>
      </Space>
    );
  }

  if (borrowReturn.borrowStatus === 'WAIT_STOCK_OUT') {
    // 待借用 btn：完成借用
    return (
      <FinishWarehouse
        borrowNo={borrowReturn.borrowNo}
        buttonProps={buttonProps}
        onCallBack={onRefresh}
      />
    );
  }
  if (borrowReturn.borrowStatus === 'BORROWING') {
    //待归还 btn：续借 转借 归还
    return (
      <Space split={type === 'link' && <Divider type="vertical" />} size={type === 'link' ? 0 : 16}>
        <ReturnButtonModal
          borrowReturn={borrowReturn}
          buttonProps={buttonProps}
          onCallBack={onRefresh}
        />
        <RenewButtonModal
          borrowReturn={borrowReturn}
          buttonProps={behindButtonProps}
          onCallBack={onRefresh}
        />
        <LendButtonModal
          borrowReturn={borrowReturn}
          buttonProps={behindButtonProps}
          onCallBack={onRefresh}
        />
      </Space>
    );
  }
  //已取消 已归还
  return <>{emptyText}</>;
}
