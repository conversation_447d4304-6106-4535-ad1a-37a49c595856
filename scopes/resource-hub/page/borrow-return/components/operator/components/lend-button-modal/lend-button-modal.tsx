import React, { useEffect, useState } from 'react';

import moment from 'moment';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { BorrowReturnJSON } from '@manyun/resource-hub.model.borrow-return';
import { borrowTypeMap, getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';
import { transferBorrowAsset } from '@manyun/resource-hub.service.transfer-borrow-asset';
import { BorrowerTypeSelect } from '@manyun/resource-hub.ui.borrower-type-select';

import { getRenewOrLendTimeDisabled } from '../../../../utils';

export type LendButtonModalProps = {
  borrowReturn: BorrowReturnJSON;
  opId?: string;

  buttonProps?: Omit<ButtonProps, 'onClick'>;
  onCallBack?: () => void;
};

export const LendButtonModal = ({
  borrowReturn,
  opId,
  buttonProps = {
    type: 'link',
    compact: true,
  },

  onCallBack,
}: LendButtonModalProps) => {
  const locales = getBorrowReturnLocales();

  const [form] = Form.useForm();
  const { validateFields, getFieldValue, setFieldsValue } = form;

  const [open, setOpen] = useState(false);
  const [isOutBorrowType, setIsOutBorrowType] = useState(false);

  useEffect(() => {
    if (open) {
      setFieldsValue({
        borrowType: 'INNER',
        borrowerType: undefined,
        borrower: undefined,
        personLiable: undefined,
        endDate: borrowReturn.borrowEndAt ? moment(borrowReturn.borrowEndAt) : moment(),
        reason: undefined,
      });
    }
  }, [borrowReturn.borrowEndAt, open, setFieldsValue]);

  const onFinish = async () => {
    const values = await validateFields();

    const { borrower, borrowerType, personLiable, borrowType, endDate, reason } = values;
    let params;
    const newEndDate = Number(endDate.clone().endOf('day').unix() + '000');

    const borrowerId = borrower.id;
    const borrowerName = borrower.name ?? borrower.label;

    if (borrowType === 'INNER') {
      params = {
        borrowNo: borrowReturn.borrowNo,
        opId,
        borrowType,
        borrower: borrowerId,
        borrowerName,
        borrowerType,
        endDate: newEndDate,
        reason: reason.trim(),
      };
    } else {
      params = {
        borrowNo: borrowReturn.borrowNo,
        opId,
        borrowType,
        personLiable: personLiable.id || borrower.key,
        personLiableName: personLiable.userName || borrower.label,
        borrower: borrowerId,
        borrowerName,
        borrowerType,
        endDate: newEndDate,
        reason: reason.trim(),
      };
    }

    const { error } = await transferBorrowAsset(params);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('转借成功！');
    setOpen(false);

    onCallBack && onCallBack();
  };

  return (
    <>
      <Button {...buttonProps} onClick={() => setOpen(true)}>
        转借
      </Button>

      <Modal
        title="转借"
        open={open}
        destroyOnClose
        onCancel={() => setOpen(false)}
        onOk={onFinish}
      >
        <Form form={form} colon={false} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
          <Form.Item label="借用类型" name="borrowType">
            <Radio.Group
              onChange={({ target: { value } }) => {
                if (value === 'INNER') {
                  setIsOutBorrowType(false);
                  const borrower = getFieldValue('borrower');
                  if (borrower && !borrower.id && borrower.label) {
                    setFieldsValue({ borrower: undefined });
                  }
                } else {
                  setIsOutBorrowType(true);
                }
              }}
            >
              <Radio key={borrowTypeMap['INNER']} value={borrowTypeMap['INNER']}>
                {locales['borrowType']['INNER']}
              </Radio>
              <Radio key={borrowTypeMap['OUT']} value={borrowTypeMap['OUT']}>
                {locales['borrowType']['OUT']}
              </Radio>
            </Radio.Group>
          </Form.Item>
          {isOutBorrowType && (
            <Form.Item
              label="借用人类型"
              name="borrowerType"
              rules={[
                {
                  required: true,
                  message: '借用人类型必选',
                },
              ]}
            >
              <BorrowerTypeSelect style={{ width: 300 }} allowClear showSearch />
            </Form.Item>
          )}
          <Form.Item
            label="借用人"
            name="borrower"
            rules={[
              {
                required: true,
                message: '借用人必选',
              },
            ]}
          >
            <UserSelect
              style={{ width: 300 }}
              userState="in-service"
              allowClear
              reserveSearchValue={isOutBorrowType}
            />
          </Form.Item>
          {isOutBorrowType && (
            <Form.Item
              label="负责人"
              name="personLiable"
              rules={[
                {
                  required: true,
                  message: '负责人必填',
                },
              ]}
            >
              <UserSelect style={{ width: 300 }} userState="in-service" allowClear />
            </Form.Item>
          )}
          <Form.Item
            label="借用结束日期"
            name="endDate"
            rules={[
              {
                required: true,
                message: '借用结束日期必填',
              },
            ]}
          >
            <DatePicker
              style={{ width: 300 }}
              allowClear
              format="YYYY-MM-DD"
              disabledDate={current =>
                getRenewOrLendTimeDisabled({
                  minDate: borrowReturn.borrowEndAt,
                  current,
                  variant: 'lend',
                })
              }
              defaultPickerValue={
                borrowReturn.borrowEndAt ? moment(borrowReturn.borrowEndAt) : moment()
              }
            />
          </Form.Item>
          <Form.Item
            label="原因"
            name="reason"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '原因必填',
              },
              {
                max: 120,
                message: '最多输入 120 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 300 }} allowClear />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
