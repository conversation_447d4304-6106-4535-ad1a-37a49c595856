import React from 'react';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';

import { revertBorrow } from '@manyun/resource-hub.service.revert-borrow';

export type WithDrawConfirmProps = {
  borrowNo: string;
  buttonProps?: ButtonProps;
  onCallBack?: () => void;
};
export const WithDrawConfirm = ({
  borrowNo,
  buttonProps = {
    type: 'link',
    compact: true,
  },
  onCallBack,
}: WithDrawConfirmProps) => {
  const onWithDrawClick = () => {
    DeleteConfirm.confirm({
      icon: <ExclamationCircleFilled />,
      title: `确认要撤回吗？`,
      targetName: '撤回',
      content: '撤回后数据将不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await revertBorrow({ borrowNo });
        if (error) {
          message.error(error.message);
          return Promise.resolve(true);
        }
        message.success('撤回成功！');
        onCallBack && onCallBack();
        return Promise.resolve(true);
      },
    });
  };

  return (
    <Button {...buttonProps} onClick={onWithDrawClick}>
      撤回
    </Button>
  );
};
