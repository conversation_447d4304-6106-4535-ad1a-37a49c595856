import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import type { ValueType } from '@manyun/base-ui.ui.input-number';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { getDeviceTypeMetaData, syncCommonDataAction } from '@manyun/dc-brain.state.common';
import type { BorrowReturnJSON } from '@manyun/resource-hub.model.borrow-return';
import type {
  AssetType,
  BorrowAsset,
} from '@manyun/resource-hub.service.fetch-borrow-can-return-assets';
import { fetchBorrowCanReturnAssets } from '@manyun/resource-hub.service.fetch-borrow-can-return-assets';
import { returnBorrowAsset } from '@manyun/resource-hub.service.return-borrow-asset';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { WarehouseRoomSelect } from '../../../warehouse-room-select';

export type ReturnButtonModalProps = {
  borrowReturn: BorrowReturnJSON;
  buttonProps?: Omit<ButtonProps, 'onClick'>;
  onCallBack?: () => void;
};

export const ReturnButtonModal = ({
  borrowReturn,
  buttonProps = {
    type: 'link',
    compact: true,
  },
  onCallBack,
}: ReturnButtonModalProps) => {
  const deviceCategory = useSelector(getDeviceTypeMetaData);
  const deviceNormalizedList = deviceCategory ? deviceCategory.normalizedList : null;
  const dispatch = useDispatch();

  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;

  const [open, setOpen] = useState(false);
  const [onLineBorrowAssets, setOnLineBorrowAssets] = useState<BorrowAsset[]>([]);
  const [offLineBorrowAssets, setOffLineBorrowAssets] = useState<BorrowAsset[]>([]);

  const [onLineSelectedRows, setOnLineSelectedRows] = useState<BorrowAsset[]>([]);

  const [offLineSelectedRows, setOffLineSelectedRows] = useState<BorrowAsset[]>([]);

  const [returnAssetCountsMap, setAeturnAssetCountsMap] = useState<Record<string, ValueType>>({});

  const [returnRoomGuidMap, setReturnRoomGuidMap] = useState<Record<string, string>>({});

  useEffect(() => {
    if (open) {
      setFieldsValue({
        ownerId: { value: borrowReturn.borrower.id, type: 'external' },
        returnRoomGuid: undefined,
        remark: undefined,
      });
      setOnLineSelectedRows([]);
      setOffLineSelectedRows([]);
    }
  }, [borrowReturn.borrower.id, open, setFieldsValue]);

  useEffect(() => {
    (async () => {
      if (!open) {
        return;
      }
      const { error, data } = await fetchBorrowCanReturnAssets({
        borrowNo: borrowReturn.borrowNo,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setOnLineBorrowAssets(data.data.filter(item => item.assetType === 'ON_LINE'));
      setOffLineBorrowAssets(data.data.filter(item => item.assetType !== 'ON_LINE'));

      setAeturnAssetCountsMap(
        data.data.reduce((result, item) => {
          return { ...result, [item.id]: Number(item.borrowNum) - Number(item.returnNum) };
        }, {})
      );
    })();
  }, [borrowReturn.borrowNo, open]);

  useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { deviceCategory: 'IF_NULL' } }));
  }, [dispatch]);

  const onFinish = async () => {
    const values = await validateFields();

    const selectedRows = [...offLineSelectedRows, ...onLineSelectedRows];
    if (!selectedRows.length) {
      message.error('还未选择要归还的资产！');
      return;
    }
    // 查找是否有选择的资产未填写归还数量
    const checkedAssetNoNum = selectedRows.find(
      ({ id, numbered }) => !returnAssetCountsMap[id] && !numbered
    );
    if (checkedAssetNoNum) {
      message.error('归还数量必填！');
      return;
    }

    const checkedReturnRoomGuid = selectedRows.find(({ id }) => !returnRoomGuidMap[id]);

    if (checkedReturnRoomGuid) {
      message.error('归还位置必填！');
      return;
    }

    const { ownerId, remark } = values;

    const params = {
      borrowNo: borrowReturn.borrowNo,
      ownerId: ownerId.id ?? borrowReturn.borrower.id,
      returnAssertInfoList: selectedRows.map(item => ({
        id: item.id,
        assetType: item.assetType,
        deviceType: item.deviceType,
        deviceTypeName: deviceNormalizedList[item.deviceType]?.metaName,
        numbered: item.numbered,
        assetNo: item.assetNo,
        vendor: item.vendor,
        productModel: item.productModel,
        returnNum: item.numbered ? 1 : (returnAssetCountsMap[item.id] as number),
        returnRoomGuid: returnRoomGuidMap[item.id],
        returnRoomTag:
          getSpaceGuidMap(returnRoomGuidMap[item.id]).room ?? returnRoomGuidMap[item.id],
      })),
      remark: remark?.trim(),
    };

    const { error } = await returnBorrowAsset(params);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('归还成功！');
    setOpen(false);
    onCallBack && onCallBack();
  };

  const getReturnNumColumn = (record: BorrowAsset, selectedRows: BorrowAsset[]) => {
    if (!record.numbered) {
      const checked = selectedRows.find(({ id }) => id === record.id);
      const validateStatus = !checked
        ? 'success'
        : !returnAssetCountsMap[record.id]
          ? 'error'
          : 'success';
      const help = !checked
        ? undefined
        : !returnAssetCountsMap[record.id]
          ? '归还数量必填！'
          : undefined;

      let max: ValueType | undefined;
      let min = 1;
      if (!record.borrowNum) {
        max = 0;
        min = 0;
      } else if (!record.returnNum) {
        max = record.borrowNum;
      } else {
        max = Number(record.borrowNum) - Number(record.returnNum);
      }

      return (
        <Form.Item validateStatus={validateStatus} help={help} style={{ marginBottom: 0 }}>
          <InputNumber
            min={min}
            max={max}
            value={returnAssetCountsMap[record.id]}
            precision={0}
            onChange={value => {
              if (!value || !max) {
                return;
              }
              if ((value as number) > max) {
                message.error('不可大于待还数量');
                return;
              }
              setAeturnAssetCountsMap({ ...returnAssetCountsMap, [record.id]: value });
            }}
          />
        </Form.Item>
      );
    }
    return 1;
  };

  const getColumns = (assetType: AssetType) => {
    let columns: ColumnType<BorrowAsset>[] = [];
    if (assetType === 'ON_LINE') {
      columns = [
        {
          title: '三级分类',
          dataIndex: 'deviceType',
          fixed: 'left',
          render: deviceType => <DeviceTypeText code={deviceType} />,
        },
        {
          title: '资产ID',
          dataIndex: 'assetNo',
          render: assetNo => assetNo || '--',
        },
      ];
    } else {
      columns = [
        {
          title: '物资分类',
          dataIndex: 'deviceType',
          fixed: 'left',
        },
        {
          title: 'SN',
          dataIndex: 'assetNo',
          render: assetNo => assetNo || '--',
        },
      ];
    }
    const commonColumns: ColumnType<BorrowAsset>[] = [
      {
        title: '品牌',
        dataIndex: 'vendor',
      },
      {
        title: '型号',
        dataIndex: 'productModel',
      },
      {
        title: '待还数量',
        dataIndex: 'waitReturnNum',
        render: (_, { borrowNum, returnNum }) => Number(borrowNum) - Number(returnNum),
      },
      {
        title: '归还数量',
        dataIndex: 'returnNum',
        render: (_, record) => {
          return getReturnNumColumn(
            record,
            assetType === 'ON_LINE' ? onLineSelectedRows : offLineSelectedRows
          );
        },
      },
      {
        title: '归还至',
        dataIndex: 'returnRoomGuid',
        render: (_, record) => {
          if (assetType === 'ON_LINE') {
            return (
              <WarehouseRoomSelect
                style={{ width: 200 }}
                allowClear
                idcTag={getSpaceGuidMap(borrowReturn.source.blockGuid).idc!}
                blockTag={getSpaceGuidMap(borrowReturn.source.blockGuid).block!}
                onChange={value => {
                  setReturnRoomGuidMap({ ...returnRoomGuidMap, [record.id]: value });
                }}
              />
            );
          }
          return (
            <Input
              maxLength={32}
              onChange={e =>
                setReturnRoomGuidMap({ ...returnRoomGuidMap, [record.id]: e.target.value })
              }
            />
          );
        },
      },
    ];
    return [...columns, ...commonColumns];
  };

  return (
    <>
      <Button {...buttonProps} onClick={() => setOpen(true)}>
        归还
      </Button>

      <Modal
        title="归还"
        open={open}
        width={1000}
        destroyOnClose
        onCancel={() => setOpen(false)}
        onOk={onFinish}
      >
        <Form form={form} colon={false} labelCol={{ xl: 2 }} wrapperCol={{ xl: 22 }}>
          <Form.Item
            label="归还人"
            name="ownerId"
            rules={[
              {
                required: true,
                message: '归还人必填！',
              },
            ]}
          >
            <UserSelect style={{ width: 210 }} userState="in-service" allowClear />
          </Form.Item>
          <Form.Item
            label="备注"
            name="remark"
            rules={[
              {
                max: 120,
                message: '最多输入 120 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 200 }} allowClear />
          </Form.Item>
        </Form>
        {!!onLineBorrowAssets.length && (
          <Space style={{ width: '100%' }} direction="vertical">
            <Typography.Text strong>待还线上物资</Typography.Text>
            <Table
              rowKey="id"
              columns={getColumns('ON_LINE')}
              scroll={{ x: 'max-content' }}
              tableLayout="fixed"
              dataSource={onLineBorrowAssets}
              pagination={false}
              rowSelection={{
                selectedRowKeys: onLineSelectedRows.map(({ id }) => id),
                onChange: (_, rows) => {
                  setOnLineSelectedRows(rows);
                },
              }}
            />
          </Space>
        )}
        {!!offLineBorrowAssets.length && (
          <Space style={{ width: '100%', marginTop: 24 }} direction="vertical">
            <Typography.Text strong>待还线下物资</Typography.Text>
            <Table
              rowKey="id"
              columns={getColumns('OFF_LINE')}
              scroll={{ x: 'max-content' }}
              tableLayout="fixed"
              dataSource={offLineBorrowAssets}
              pagination={false}
              rowSelection={{
                selectedRowKeys: offLineSelectedRows.map(({ id }) => id),
                onChange: (_, rows) => {
                  setOffLineSelectedRows(rows);
                },
              }}
            />
          </Space>
        )}
      </Modal>
    </>
  );
};
