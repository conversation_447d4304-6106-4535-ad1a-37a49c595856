import React from 'react';

import QuestionCircleFilled from '@ant-design/icons/es/icons/QuestionCircleFilled';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';

import { cancelBorrow } from '@manyun/resource-hub.service.cancel-borrow';

export type CancelBorrowConfirmProps = {
  borrowNo: string;
  buttonProps?: ButtonProps;
  onCallBack?: () => void;
};
export const CancelBorrowConfirm = ({
  borrowNo,
  buttonProps = {
    type: 'link',
    compact: true,
  },
  onCallBack,
}: CancelBorrowConfirmProps) => {
  const onCancelBorrowClick = () => {
    DeleteConfirm.confirm({
      icon: <QuestionCircleFilled />,
      title: `确认要取消${borrowNo}的借用归还吗？`,
      targetName: '取消借用归还',
      content: '取消后数据将不可恢复。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await cancelBorrow({ borrowNo });
        if (error) {
          message.error(error.message);
          return Promise.resolve(true);
        }
        message.success('取消成功！');
        onCallBack && onCallBack();
        return Promise.resolve(true);
      },
    });
  };

  return (
    <Button {...buttonProps} onClick={onCancelBorrowClick}>
      取消
    </Button>
  );
};
