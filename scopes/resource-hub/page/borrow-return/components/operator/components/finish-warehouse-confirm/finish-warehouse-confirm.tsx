import React from 'react';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { message } from '@manyun/base-ui.ui.message';

import { confirmBorrowAllOut } from '@manyun/resource-hub.service.confirm-borrow-all-out';
import { fetchBorrowIsAllOut } from '@manyun/resource-hub.service.fetch-borrow-is-all-out';

export type FinishWarehouseProps = {
  borrowNo: string;
  buttonProps?: ButtonProps;
  onCallBack?: () => void;
};
export const FinishWarehouse = ({
  borrowNo,
  buttonProps = {
    type: 'link',
    compact: true,
  },
  onCallBack,
}: FinishWarehouseProps) => {
  const onFinishWarehouseClick = async () => {
    //需要先请求是否全部出库
    const { error, data } = await fetchBorrowIsAllOut({ borrowNo });
    if (error) {
      message.error(error);
      return;
    }
    if (data) {
      const { error } = await confirmBorrowAllOut({ borrowNo });
      if (error) {
        message.error(error.message);
        return Promise.resolve(true);
      }
      message.success('完成借用成功！');
      onCallBack && onCallBack();
      return;
    }

    DeleteConfirm.confirm({
      icon: <ExclamationCircleFilled />,
      title: '还有物资未借出，确定吗？',
      targetName: '完成借用',
      content: '完成借用后将不可继续借用，如需借用请重新申请。',
      okText: '确认',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await confirmBorrowAllOut({ borrowNo });

        if (error) {
          message.error(error.message);
          return Promise.resolve(true);
        }
        message.success('完成借用成功！');
        onCallBack && onCallBack();
        return Promise.resolve(true);
      },
    });
    return;
  };

  return (
    <Button {...buttonProps} onClick={onFinishWarehouseClick}>
      完成借用
    </Button>
  );
};
