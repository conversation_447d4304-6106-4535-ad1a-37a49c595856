import React, { useEffect, useState } from 'react';

import moment from 'moment';

import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import type { BorrowReturnJSON } from '@manyun/resource-hub.model.borrow-return';
import { renewBorrowAsset } from '@manyun/resource-hub.service.renew-borrow-asset';

import { getRenewOrLendTimeDisabled } from '../../../../utils';

export type RenewButtonModalProps = {
  borrowReturn: BorrowReturnJSON;
  opId?: string;
  buttonProps?: Omit<ButtonProps, 'onClick'>;
  onCallBack?: () => void;
};
export const RenewButtonModal = ({
  borrowReturn,
  buttonProps = {
    type: 'link',
    compact: true,
  },
  opId,
  onCallBack,
}: RenewButtonModalProps) => {
  const [form] = Form.useForm();
  const { validateFields, setFieldsValue } = form;

  const [open, setOpen] = useState(false);

  useEffect(() => {
    if (open) {
      setFieldsValue({
        endDate: undefined,
        reason: undefined,
      });
    }
  }, [open, setFieldsValue]);

  const onFinish = async () => {
    const values = await validateFields();
    const { endDate, reason } = values;
    const params = {
      borrowNo: borrowReturn.borrowNo,
      opId,
      endDate: Number(endDate.clone().endOf('day').unix() + '000'),
      reason: reason.trim(),
    };
    const { error } = await renewBorrowAsset(params);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('续借成功！');
    setOpen(false);
    onCallBack && onCallBack();
  };

  return (
    <>
      <Button {...buttonProps} onClick={() => setOpen(true)}>
        续借
      </Button>

      <Modal
        title="续借"
        open={open}
        destroyOnClose
        onCancel={() => setOpen(false)}
        onOk={onFinish}
      >
        <Form form={form} colon={false} labelCol={{ xl: 6 }} wrapperCol={{ xl: 18 }}>
          <Form.Item
            label="续借结束日期"
            name="endDate"
            rules={[{ required: true, message: '续借结束日期必填' }]}
          >
            <DatePicker
              style={{ width: 300 }}
              allowClear
              format="YYYY-MM-DD"
              disabledDate={current =>
                getRenewOrLendTimeDisabled({
                  minDate: borrowReturn.borrowEndAt,
                  current: current,
                  variant: 'renew',
                })
              }
              defaultPickerValue={
                borrowReturn.borrowEndAt ? moment(borrowReturn.borrowEndAt) : moment()
              }
            />
          </Form.Item>
          <Form.Item
            label="原因"
            name="reason"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '原因必填！',
              },
              {
                max: 120,
                message: '最多输入 120 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 300 }} allowClear />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};
