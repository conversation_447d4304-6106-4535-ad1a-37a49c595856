import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';

import { RiskLevel } from './risk-level';

export function RiskLevelConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] =
    useMetaData('RISK_LEVEL');
  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space>
      <RiskLevel
        data={data?.data || []}
        readMetaData={readMetaData}
        createMetaData={createMetaData}
        deleteMetaData={deleteMetaData}
        editMetaData={editMetaData}
      />
    </Space>
  );
}
