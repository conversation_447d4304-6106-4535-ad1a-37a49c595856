import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ReadOptions } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

const configName = '专业分类';
const metaType = MetaType.EMERGENCY_CATEGORY;

export type EvalPositionProps = {
  data: MetaSelectData[];

  readMetaData: (options?: ReadOptions | undefined) => void;
  createMetaData: ({
    params,
    callback,
  }: {
    params: { name: string; type: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
  editMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; name: string; type: MetaType };
    callback: (result: boolean) => void;
  }) => void;
  deleteMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; metaType: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
};
export function Category({
  data,
  readMetaData,
  createMetaData,
  editMetaData,
  deleteMetaData,
}: EvalPositionProps) {
  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error(`该${configName}已存在，请勿重复添加`);
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: metaType,
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>{configName}：</span>
            <MetaDataCard
              maxLength={10}
              list={data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder={`添加${configName}`}
              getList={() => readMetaData()}
              metaType={metaType}
              deleteCategory={(id: number, callback: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: metaType,
                  },
                  callback,
                });
              }}
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error(`该${configName}已存在，请勿重复添加`);
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: metaType,
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}
