import React from 'react';

import styled from 'styled-components';

import { Space } from '@manyun/base-ui.ui.space';

import { BackendCategoryType } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

import Category from './category';

const StyledBatteryWapper = styled.div`
  display: grid;
  grid-template-columns: 48% 48%;
  grid-gap: 30px;
`;

const knowledgeHubCategory = [
  {
    title: '技能分类',
    type: BackendCategoryType.CERT,
  },
  {
    title: '课程分类',
    type: BackendCategoryType.COURSE,
  },
  {
    title: '考试分类',
    type: BackendCategoryType.EXAM,
  },
  {
    title: '试卷分类',
    type: BackendCategoryType.PAPER,
  },
];

export function KnowledgeHubConfiguration() {
  return (
    <Space direction="vertical">
      <StyledBatteryWapper>
        {knowledgeHubCategory.map(({ title, type }) => {
          return (
            <div key={type}>
              <Category title={title} type={type} />
            </div>
          );
        })}
      </StyledBatteryWapper>
    </Space>
  );
}
