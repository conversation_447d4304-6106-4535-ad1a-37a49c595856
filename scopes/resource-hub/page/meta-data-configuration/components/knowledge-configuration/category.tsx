import React, { useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { deleteKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.delete-knowledge-hub-category';
import { fetchKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { BackendCategoryType } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { mutateKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

const rootNode = 0;
export type CategoryProps = {
  title: string;
  type: BackendCategoryType;
};
export type KnowledgeHubCategoryData = {
  metaCode: number;
  metaName: string;
  id: number;
  name: string;
  parentId: number;
  value: number;
  type: string;
  code: number;
  editable?: boolean;
  level?: number | undefined;
  count?: number | undefined;
};
export function Category({ title, type }: CategoryProps) {
  const [categoryList, setCategoryList] = useState<KnowledgeHubCategoryData[]>([]);
  const [topCert, setTopCert] = useState<KnowledgeHubCategoryData[]>([]);
  const [secondCert, setSecondCert] = useState<KnowledgeHubCategoryData[]>([]);
  const [thirdCert, setThirdCert] = useState<KnowledgeHubCategoryData[]>([]);
  const [topParentCode, setTopParentCode] = useState<number | undefined>(undefined);
  const [secondParentCode, setSecondParentCode] = useState<number | undefined>(undefined);

  const getSkillCategory = async () => {
    const { error, data } = await fetchKnowledgeHubCategoryWeb({
      categoryType: type,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    const categoryList = data.map(item => ({
      ...item,
      value: item.id,
      metaCode: item.id,
      metaName: item.name,
    }));
    const codes = data.map(({ id }) => id);
    const topCert = categoryList.filter(({ parentId }) => parentId === rootNode);
    const topCode = topParentCode && codes.includes(topParentCode) ? topParentCode : topCert[0]?.id;
    const secondCert = categoryList.filter(({ parentId }) => parentId === topCode);
    const secondCode =
      secondParentCode && codes.includes(secondParentCode) ? secondParentCode : secondCert[0]?.id;
    const thirdCert = categoryList.filter(({ parentId }) => parentId === secondCode);
    setCategoryList(categoryList);
    setTopCert(topCert);
    setTopParentCode(topCode);
    setSecondCert(secondCert);
    setSecondParentCode(secondCode);
    setThirdCert(thirdCert);
  };
  useEffect(() => {
    getSkillCategory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const editCategory = async ({
    id,
    name,
    callback,
  }: {
    name: string;
    id: number;
    callback?: (result: boolean) => void;
  }) => {
    const { error } = await mutateKnowledgeHubCategoryWeb({
      id,
      name,
    });
    const result = error ? false : true;
    if (callback) {
      callback(result);
    }
    if (error) {
      message.error(error.message);
      return;
    }
    getSkillCategory();
  };

  const addCategory = async ({
    name,
    parentId,
    level,
  }: {
    name: string;
    parentId?: number;
    level: string;
  }) => {
    const { error, data } = await mutateKnowledgeHubCategoryWeb({
      type: type,
      parentId,
      name,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('新增成功！');
    if (level === 'top') {
      setTopParentCode(data.id);
      setSecondParentCode(undefined);
    }
    if (level === 'second') {
      setSecondParentCode(data.id);
    }
    getSkillCategory();
    return Promise.resolve(true);
  };

  const deleteCategory = async ({ id, level }: { id: number; level: string }) => {
    const { error } = await deleteKnowledgeHubCategoryWeb({ id });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功！');
    if (level === 'top') {
      setTopParentCode(undefined);
      setSecondParentCode(undefined);
    }
    if (level === 'second') {
      setSecondParentCode(undefined);
    }
    getSkillCategory();
  };

  const clickTopCategory = (category: { metaCode: number }) => {
    const secondCert = categoryList.filter(({ parentId }) => parentId === category.metaCode);
    const secondParentCode = secondCert[0]?.id;
    const thirdCert = categoryList.filter(({ parentId }) => parentId === secondParentCode);

    setTopParentCode(category.metaCode);
    setSecondCert(secondCert);
    setSecondParentCode(secondParentCode);
    setThirdCert(thirdCert);
  };

  const clickSecondCategory = (category: { metaCode: number }) => {
    const thirdCert = categoryList.filter(({ parentId }) => parentId === category.metaCode);
    setThirdCert(thirdCert);
    setSecondParentCode(category.metaCode);
  };

  return (
    <Space align="start">
      <span style={{ display: 'inline-block', verticalAlign: 'top', width: 70 }}>{title}：</span>
      <Card>
        <Space style={{ width: 'calc(100% - 70px)' }}>
          <Space size={8}>
            <MetaDataCard
              list={topCert}
              placeholder="添加一级类型"
              onClick={category => {
                clickTopCategory(category);
              }}
              bordered={false}
              onAdd={metaName => addCategory({ name: metaName, parentId: rootNode, level: 'top' })}
              parentCode={topParentCode?.toString()}
              // style={{ paddingRight: 16 }}
              cursor="pointer"
              getList={() => getSkillCategory()}
              onEdit={(
                id: number,
                name: string,
                isDuplicated,
                callback?: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error(`该${title}已存在，请勿重复添加`);
                  return;
                }
                editCategory({ id, name, callback });
              }}
              deleteCategory={id => deleteCategory({ id: id, level: 'top' })}
              maxLength={10}
            />
            <Divider type="vertical" style={{ height: 225, display: 'inline-block', margin: 0 }} />
            <MetaDataCard
              list={secondCert}
              placeholder="添加二级类型"
              onClick={category => {
                clickSecondCategory(category);
              }}
              bordered={false}
              onAdd={metaName =>
                addCategory({ name: metaName, parentId: topParentCode, level: 'second' })
              }
              onEdit={(
                id: number,
                name: string,
                isDuplicated,
                callback?: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error(`该${title}已存在，请勿重复添加`);
                  return;
                }
                editCategory({ id, name, callback });
              }}
              parentCode={secondParentCode?.toString()}
              showAddInput={!!topParentCode}
              // style={{ paddingRight: 16 }}
              cursor="pointer"
              getList={() => getSkillCategory()}
              deleteCategory={id => deleteCategory({ id: id, level: 'second' })}
              maxLength={10}
            />
            <Divider type="vertical" style={{ height: 225, display: 'inline-block', margin: 0 }} />
            <MetaDataCard
              list={thirdCert}
              placeholder="添加三级类型"
              onAdd={metaName =>
                addCategory({ name: metaName, parentId: secondParentCode, level: 'third' })
              }
              onEdit={(
                id: number,
                name: string,
                isDuplicated,
                callback?: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error(`该${title}已存在，请勿重复添加`);
                  return;
                }
                editCategory({ id, name, callback });
              }}
              bordered={false}
              parentCode={secondParentCode?.toString()}
              showAddInput={!!secondParentCode}
              getList={() => getSkillCategory()}
              deleteCategory={id => deleteCategory({ id: id, level: 'third' })}
              maxLength={10}
            />
          </Space>
        </Space>
      </Card>
    </Space>
  );
}

export default Category;
