import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

import { ManuallyAddExpenseAccounts } from './manually-add-expense-accounts';

export function ManuallyAddExpenseAccountsConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.BILL_FEE
  );

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ManuallyAddExpenseAccounts
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
    />
  );
}
