import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

import { TaxRate } from './tax-rate';

const metaType = MetaType.TAX_RATE;

export function TaxRateConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] =
    useMetaData(metaType);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <TaxRate
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
      metaType={metaType}
    />
  );
}
