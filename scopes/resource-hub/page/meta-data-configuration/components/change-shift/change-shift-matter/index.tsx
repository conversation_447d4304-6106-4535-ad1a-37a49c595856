import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

import { ChangeShiftMatter } from './change-shift-matter';

export function ChangeShiftMatterConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.HANDOVER_EVENT_TYPE
  );

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <ChangeShiftMatter
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
    />
  );
}
