import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';

import { EventMajor } from './event-major';

export function EventMajorConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] =
    useMetaData('EVENT_MAJOR');

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <EventMajor
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
    />
  );
}
