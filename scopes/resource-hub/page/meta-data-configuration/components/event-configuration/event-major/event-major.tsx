import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ReadOptions } from '@manyun/resource-hub.hook.use-meta-data';
import type { MetaSelectData, MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export type EventMajorProps = {
  data: MetaSelectData[];

  readMetaData: (options?: ReadOptions | undefined) => void;
  createMetaData: ({
    params,
    callback,
  }: {
    params: { name: string; type: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
  editMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; name: string; type: MetaType };
    callback: (result: boolean) => void;
  }) => void;
  deleteMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; metaType: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
};
export function EventMajor({
  data,
  readMetaData,
  createMetaData,
  editMetaData,
  deleteMetaData,
}: EventMajorProps) {
  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error('该事件专业已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: 'EVENT_MAJOR',
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          // justifyContent: 'space-between',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>事件专业：</span>
            <MetaDataCard
              maxLength={10}
              list={data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder="添加事件专业"
              getList={() => readMetaData()}
              metaType={'EVENT_MAJOR'}
              deleteCategory={(id: number, callback: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: 'EVENT_MAJOR',
                  },
                  callback,
                });
              }}
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error('该事件专业已存在，请勿重复添加');
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: 'EVENT_MAJOR',
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}
