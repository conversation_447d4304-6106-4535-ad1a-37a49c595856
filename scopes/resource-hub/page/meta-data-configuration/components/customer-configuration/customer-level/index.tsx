import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

import { CustomerLevel } from './customer-level';

export function CustomerLevelConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.CUSTOMER_LEVEL
  );

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <CustomerLevel
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
    />
  );
}
