import React, { useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ReadOptions } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export type RoomTypeProps = {
  data: MetaSelectData[];

  readMetaData: (options?: ReadOptions | undefined) => void;
  createMetaData: ({
    params,
    callback,
  }: {
    params: { name: string; type: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
  editMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; name: string; type: MetaType };
    callback: (result: boolean) => void;
  }) => void;
  deleteMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; metaType: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
};
export function RoomType({
  data,
  readMetaData,
  createMetaData,
  editMetaData,
  deleteMetaData,
}: RoomTypeProps) {
  const [addLoading, setAddLoading] = useState(false);

  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error('该包间类型已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      setAddLoading(true);
      createMetaData({
        params: {
          name: metaName,
          type: MetaType.ROOM_TYPE,
        },
        callback: () => {
          setAddLoading(false);
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          // justifyContent: 'space-between',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>包间类型：</span>
            <MetaDataCard
              maxLength={10}
              list={data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder="添加包间类型"
              getList={() => readMetaData()}
              metaType={MetaType.ROOM_TYPE}
              deleteCategory={(id: number, callback: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: MetaType.ROOM_TYPE,
                  },
                  callback,
                });
              }}
              addLoading={addLoading}
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error('该包间类型已存在，请勿重复添加');
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: MetaType.ROOM_TYPE,
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}
