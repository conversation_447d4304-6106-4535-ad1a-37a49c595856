import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

// import { DeviceUnit } from './device-unit';
import { DeviceUnit } from './device';

export function DeviceUnitConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.SPEC_UNIT
  );

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <DeviceUnit
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
    />
  );
}
