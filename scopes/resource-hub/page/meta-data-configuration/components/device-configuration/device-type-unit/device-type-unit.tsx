import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export function DeviceTypeUnitConfiguration() {
  const [, { readMetaData }] = useMetaData(MetaType.TYPE_UNIT);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <DeviceTypeUnit />;
}

function DeviceTypeUnit() {
  const [
    {
      data: { data },
    },
    { readMetaData, createMetaData, editMetaData, deleteMetaData },
  ] = useMetaData(MetaType.TYPE_UNIT);

  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error('该三级类目单位已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: MetaType.TYPE_UNIT,
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>三级类目单位：</span>
            <MetaDataCard
              maxLength={10}
              list={data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
                editable: true,
              }))}
              placeholder="添加三级类目单位"
              onAdd={addMetaData}
              onEdit={(id, metaName, isDuplicated, callback) => {
                if (isDuplicated) {
                  message.error('该三级类目单位已存在，请勿重复添加');
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: MetaType.TYPE_UNIT,
                  },
                  callback,
                });
              }}
              getList={() => readMetaData()}
              metaType={MetaType.TYPE_UNIT}
              deleteCategory={(id, callback) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: MetaType.TYPE_UNIT,
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}
