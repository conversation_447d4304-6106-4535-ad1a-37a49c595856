import React, { useState } from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import type { Metadata, MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { MetaType } from '@manyun/resource-hub.model.metadata';

import { Visitor } from './visitor';
import { VisitorSecondCategory } from './visitor-secondary';

export function VisitorConfiguration() {
  const [parentCode, setParentCode] = useState<string | undefined>();
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.VISITOR
  );
  const [
    { data: secData },
    {
      readMetaData: readSecMetaData,
      createMetaData: createSecMetaData,
      editMetaData: editSecMetaData,
      deleteMetaData: deleteSecMetaData,
    },
  ] = useMetaData(MetaType.VISITOR_THIRD_CATEGORY);

  React.useEffect(() => {
    readMetaData();
    readSecMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onInformationServiceTopCategoryClick = (metaData: MetaSelectData) => {
    setParentCode(metaData.value);
  };
  const getParentCode = (
    data: {
      total: number;
      codes: string[];
      loading: boolean;
      keyMapper: Record<number, string>;
      entities: Metadata[];
      data: MetaSelectData[];
    },
    parentCode?: string
  ) => {
    if (parentCode) {
      return parentCode;
    } else {
      if (data?.data) {
        return data?.data?.length ? data.data[0].value : null;
      }
      return null;
    }
  };

  return (
    <Space>
      <Visitor
        data={data?.data || []}
        parentCode={getParentCode(data, parentCode)}
        readMetaData={readMetaData}
        createMetaData={createMetaData}
        deleteMetaData={deleteMetaData}
        editMetaData={editMetaData}
        onClick={onInformationServiceTopCategoryClick}
      />
      <Divider type="vertical" style={{ height: 225, display: 'inline-block', margin: 0 }} />
      <VisitorSecondCategory
        data={secData?.data || []}
        parentCode={getParentCode(data, parentCode)}
        readMetaData={readSecMetaData}
        createMetaData={createSecMetaData}
        deleteMetaData={deleteSecMetaData}
        editMetaData={editSecMetaData}
      />
    </Space>
  );
}
