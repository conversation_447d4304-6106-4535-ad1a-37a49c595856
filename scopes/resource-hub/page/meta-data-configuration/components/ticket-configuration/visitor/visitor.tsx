import { CheckOutlined, CloseOutlined, PlusOutlined } from '@ant-design/icons';
import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import type { ReadOptions } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';
import { updateMetaTag } from '@manyun/ticket.services.update-meta-tag';

export type VisitorProps = {
  data: MetaSelectData[];
  parentCode: string;
  onClick: (metaData?: MetaSelectData) => void;
  readMetaData: (options?: ReadOptions | undefined) => void;
  createMetaData: ({
    params,
    callback,
  }: {
    params: { name: string; type: MetaType; parentCode: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
  editMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; name: string; type: MetaType; parentCode: MetaType };
    callback: (result: boolean) => void;
  }) => void;
  deleteMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; metaType: MetaType; parentCode: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
};
export function Visitor({
  data,
  parentCode,
  onClick,
  readMetaData,
  createMetaData,
  editMetaData,
  deleteMetaData,
}: VisitorProps) {
  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error('该人员入室类型已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: MetaType.VISITOR,
          parentCode: MetaType.WORK_ORDER,
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          // justifyContent: 'space-between',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>人员入室类型：</span>
            <MetaDataCard
              cardStyle={{ width: '286px' }}
              maxLength={10}
              metaType={MetaType.VISITOR}
              parentCode={parentCode}
              list={data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder="添加一级类型"
              cursor="pointer"
              getList={() => readMetaData()}
              deleteCategory={(id: number, callback: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: MetaType.VISITOR,
                    parentCode: MetaType.WORK_ORDER,
                  },
                  callback,
                });
              }}
              selectComponentRender={metaId => (
                <SelectComponent
                  metaId={metaId}
                  readMetaData={readMetaData}
                  // @ts-ignore ts(2339)
                  value={data.find(i => i.id === metaId)?.tag}
                />
              )}
              onClick={onClick}
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error('该人员入室类型已存在，请勿重复添加');
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: MetaType.VISITOR,
                    parentCode: MetaType.WORK_ORDER,
                  },

                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}

const tagOptions = [
  { label: '商务', value: 'BUSINESS', color: 'orange' },
  { label: '政府', value: 'GOVERNMENT', color: 'blue' },
];
// 选择tag组件
function SelectComponent({
  metaId,
  readMetaData,
  value,
}: {
  metaId: number;
  readMetaData: (options?: ReadOptions | undefined) => void;
  value?: string;
}) {
  // 是否显示下拉选择框
  const [showSelect, setShowSelect] = React.useState(false);
  const [tag, setTag] = React.useState(value);

  // 显示下拉选择框
  const handleShowSelect = () => {
    setShowSelect(true);
  };
  const tagInfo = React.useMemo(() => {
    return tagOptions.find(i => i.value === tag);
  }, [tag]);

  // 更新标签
  const handleUpdateTag = async (metaId: number, tagValue: string | undefined) => {
    const { data, error } = await updateMetaTag({
      id: metaId,
      tag: tagValue,
      metaType: MetaType.VISITOR,
    });
    setShowSelect(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      message.success(`更新成功`);
    }
    readMetaData();
  };

  return (
    <div style={{ display: 'flex', alignItems: 'center', marginLeft: 8 }}>
      {value && !showSelect && (
        <div style={{ marginLeft: 8, display: 'flex', flexWrap: 'wrap', gap: 4 }}>
          <Tag color={tagInfo?.color} closable onClose={() => handleUpdateTag(metaId, undefined)}>
            {tagInfo?.label}
          </Tag>
        </div>
      )}
      {showSelect ? (
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Select
            style={{ width: '80px' }}
            options={tagOptions}
            allowClear
            autoFocus
            value={tag}
            // placeholder="请选择类别"
            onChange={v => {
              setTag(v);
            }}
          />
          <Tooltip title="保存">
            <CheckOutlined
              style={{ margin: '0 4px' }}
              onClick={() => {
                handleUpdateTag(metaId, tag);
              }}
            />
          </Tooltip>
          <Tooltip title="取消">
            <CloseOutlined onClick={() => setShowSelect(false)} />
          </Tooltip>
        </div>
      ) : (
        <Tooltip title="添加标签">
          <PlusOutlined onClick={handleShowSelect} style={{ padding: '0 4px' }} />{' '}
        </Tooltip>
      )}
    </div>
  );
}
