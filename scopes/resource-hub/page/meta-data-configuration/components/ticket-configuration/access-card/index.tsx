import React, { useState } from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaData, MetaSelectData } from '@manyun/resource-hub.model.metadata';

import { AccessCardSecondCategory } from './access-card-second-category';
import { AccessCardTopCategory } from './access-card-top-category';

export function AccessCardTopCategoryConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.ACCESS_CARD_TOP_CATEGORY
  );
  const [
    { data: secData },
    {
      readMetaData: readSecMetaData,
      createMetaData: createSecMetaData,
      editMetaData: editSecMetaData,
      deleteMetaData: deleteSecMetaData,
    },
  ] = useMetaData(MetaType.ACCESS_CARD_SECOND_CATEGORY);
  const [parentCode, setParentCode] = useState<string | undefined>();
  React.useEffect(() => {
    readMetaData();
    readSecMetaData();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const onAccessCardTopCategoryClick = (metaData: MetaSelectData) => {
    setParentCode(metaData.value);
  };
  const getParentCode = (
    data: {
      total: number;
      codes: string[];
      loading: boolean;
      keyMapper: Record<number, string>;
      entities: MetaData[];
      data: MetaSelectData[];
    },
    parentCode?: string
  ) => {
    if (parentCode) {
      return parentCode;
    } else {
      if (data?.data) {
        return data?.data?.length ? data.data[0].value : null;
      }
      return null;
    }
  };
  return (
    <Space>
      <AccessCardTopCategory
        data={data?.data || []}
        parentCode={getParentCode(data, parentCode)}
        readMetaData={readMetaData}
        onClick={onAccessCardTopCategoryClick}
        createMetaData={createMetaData}
        deleteMetaData={deleteMetaData}
        editMetaData={editMetaData}
      />
      <Divider type="vertical" style={{ height: 225, display: 'inline-block', margin: 0 }} />
      <AccessCardSecondCategory
        data={secData?.data || []}
        parentCode={getParentCode(data, parentCode)}
        readMetaData={readSecMetaData}
        createMetaData={createSecMetaData}
        deleteMetaData={deleteSecMetaData}
        editMetaData={editSecMetaData}
      />
    </Space>
  );
}
