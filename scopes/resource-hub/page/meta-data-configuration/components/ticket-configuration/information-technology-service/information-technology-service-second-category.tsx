import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ReadOptions } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export type InformationServiceSecondCategoryProps = {
  data: MetaSelectData[];
  parentCode?: string | null;

  readMetaData: (options?: ReadOptions | undefined) => void;
  createMetaData: ({
    params,
    callback,
  }: {
    params: { name: string; type: MetaType; parentType: MetaType; parentCode?: string | null };
    callback: (result?: boolean) => void;
  }) => void;
  editMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; name: string; type: MetaType };
    callback: (result: boolean) => void;
  }) => void;
  deleteMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; metaType: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
};
export function InformationServiceSecondCategory({
  data,
  parentCode,
  readMetaData,
  createMetaData,
  editMetaData,
  deleteMetaData,
}: InformationServiceSecondCategoryProps) {
  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error('该IT服务类型已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: MetaType.ITORDER_THIRD_CATEGORY,
          parentType: MetaType.IT_SERVICE,
          parentCode: parentCode,
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          // justifyContent: 'space-between',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <MetaDataCard
            maxLength={10}
            list={data
              .filter(
                secondCategoryItem =>
                  `${MetaType['IT_SERVICE']}${parentCode}` === secondCategoryItem.parentCode
              )
              .map(d => ({ ...d, id: d.id, name: d.label }))}
            placeholder="添加二级类型"
            cursor="pointer"
            getList={() => readMetaData()}
            metaType={MetaType.ITORDER_THIRD_CATEGORY}
            deleteCategory={(id: number, callback: (result?: boolean) => void) => {
              deleteMetaData({
                params: {
                  id,
                  metaType: MetaType.ITORDER_THIRD_CATEGORY,
                },
                callback,
              });
            }}
            onAdd={addMetaData}
            onEdit={(
              id: number,
              metaName: string,
              isDuplicated,
              callback: (result: boolean) => void
            ) => {
              if (isDuplicated) {
                message.error('该IT服务类型已存在，请勿重复添加');
                return;
              }
              editMetaData({
                params: {
                  id,
                  name: metaName,
                  type: MetaType.ITORDER_THIRD_CATEGORY,
                },
                callback,
              });
            }}
          />
        </div>
      </div>
    </Space>
  );
}
