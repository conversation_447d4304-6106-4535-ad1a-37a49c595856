import React, { useState } from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaData, MetaSelectData } from '@manyun/resource-hub.model.metadata';

import { InformationServiceSecondCategory } from './information-technology-service-second-category';
import { InformationServiceTopCategory } from './information-technology-service-top-category';

export function InformationServiceTopCategoryConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.IT_SERVICE
  );
  const [
    { data: secData },
    {
      readMetaData: readSecMetaData,
      createMetaData: createSecMetaData,
      editMetaData: editSecMetaData,
      deleteMetaData: deleteSecMetaData,
    },
  ] = useMetaData(MetaType.ITORDER_THIRD_CATEGORY);
  const [parentCode, setParentCode] = useState<string | undefined>();
  React.useEffect(() => {
    readMetaData();
    readSecMetaData();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  const onInformationServiceTopCategoryClick = (metaData: MetaSelectData) => {
    setParentCode(metaData.value);
  };
  const getParentCode = (
    data: {
      total: number;
      codes: string[];
      loading: boolean;
      keyMapper: Record<number, string>;
      entities: MetaData[];
      data: MetaSelectData[];
    },
    parentCode?: string
  ) => {
    if (parentCode) {
      return parentCode;
    } else {
      if (data?.data) {
        return data?.data?.length ? data.data[0].value : null;
      }
      return null;
    }
  };
  return (
    <Space>
      <InformationServiceTopCategory
        data={data?.data || []}
        parentCode={getParentCode(data, parentCode)}
        readMetaData={readMetaData}
        createMetaData={createMetaData}
        deleteMetaData={deleteMetaData}
        editMetaData={editMetaData}
        onClick={onInformationServiceTopCategoryClick}
      />
      <Divider type="vertical" style={{ height: 225, display: 'inline-block', margin: 0 }} />
      <InformationServiceSecondCategory
        data={secData?.data || []}
        parentCode={getParentCode(data, parentCode)}
        readMetaData={readSecMetaData}
        createMetaData={createSecMetaData}
        deleteMetaData={deleteSecMetaData}
        editMetaData={editSecMetaData}
      />
    </Space>
  );
}
