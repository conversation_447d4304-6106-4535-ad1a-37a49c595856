import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export function DrillLevel() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.EXC_LEVEL
  );

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const addMetaData = async (metaName: string) => {
    if (data.data.filter(item => item.label === metaName).length) {
      message.error('该等级已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: MetaType.EXC_LEVEL,
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>等级：</span>
            <MetaDataCard
              maxLength={10}
              list={data.data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder="添加一级配置"
              getList={() => readMetaData()}
              metaType={MetaType.EXC_LEVEL}
              deleteCategory={(id: number, callback: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: MetaType.EXC_LEVEL,
                  },
                  callback,
                });
              }}
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error('该等级已存在，请勿重复添加');
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: MetaType.EXC_LEVEL,
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}
