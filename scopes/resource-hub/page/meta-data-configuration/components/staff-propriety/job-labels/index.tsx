/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';

import { JobLabels } from './job-labels';

export function JobLabelsConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    'JOB_LABEL' as any
  );
  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space>
      <JobLabels
        data={data?.data || []}
        readMetaData={readMetaData}
        createMetaData={createMetaData}
        deleteMetaData={deleteMetaData}
        editMetaData={editMetaData}
      />
    </Space>
  );
}
