/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';

import { PositionYG } from './position-yg';

export function PositionYGConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    'POSITION_YG' as any
  );
  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space>
      <PositionYG
        data={data?.data || []}
        readMetaData={readMetaData}
        createMetaData={createMetaData}
        deleteMetaData={deleteMetaData}
        editMetaData={editMetaData}
      />
    </Space>
  );
}
