import React from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ReadOptions } from '@manyun/resource-hub.hook.use-meta-data';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export type AlarmLevelProps = {
  data: MetaSelectData[];

  readMetaData: (options?: ReadOptions | undefined) => void;
  createMetaData: ({
    params,
    callback,
  }: {
    params: { name: string; type: MetaType };
    callback: (result?: boolean) => void;
  }) => void;
  editMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; name: string; type: MetaType };
    callback: (result: boolean) => void;
  }) => void;
  deleteMetaData: ({
    params,
    callback,
  }: {
    params: { id: number; metaType: MetaType };
    callback?: (result?: boolean) => void;
  }) => void;
};
export function AlarmLevel({
  data,
  readMetaData,
  createMetaData,
  editMetaData,
  deleteMetaData,
}: AlarmLevelProps) {
  const addMetaData = async (metaName: string) => {
    if (data?.filter(item => item.label === metaName).length) {
      message.error('该告警级别已存在，请勿重复添加');
      return;
    }
    return new Promise(resolve => {
      createMetaData({
        params: {
          name: metaName,
          type: MetaType.ALARM_LEVEL,
        },
        callback: () => {
          resolve(true);
        },
      });
    });
  };

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          // justifyContent: 'space-between',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>告警等级：</span>
            <MetaDataCard
              maxLength={10}
              list={data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder="添加告警等级"
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error('该告警级别已存在，请勿重复添加');
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: MetaType.ALARM_LEVEL,
                  },
                  callback,
                });
              }}
              getList={() => readMetaData()}
              metaType={MetaType.ALARM_LEVEL}
              deleteCategory={(id: number, callback?: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: MetaType.ALARM_LEVEL,
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}

export function AlarmLevelConfiguration() {
  const [{ data }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] = useMetaData(
    MetaType.ALARM_LEVEL
  );

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <AlarmLevel
      data={data?.data || []}
      readMetaData={readMetaData}
      createMetaData={createMetaData}
      deleteMetaData={deleteMetaData}
      editMetaData={editMetaData}
    />
  );
}
