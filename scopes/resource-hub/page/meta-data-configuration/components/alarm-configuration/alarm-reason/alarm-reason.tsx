import React, { useCallback } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { MetaDataCard } from '@manyun/resource-hub.ui.meta-data-card';

export function AlarmReasonConfiguration() {
  const metaType = MetaType.ALARM_REASON;
  const [{ data: alrarmData }, { readMetaData, createMetaData, editMetaData, deleteMetaData }] =
    useMetaData(metaType);

  const metaTitle = '告警原因';

  React.useEffect(() => {
    readMetaData();
  }, [readMetaData]);

  const addMetaData = useCallback(
    async (metaName: string) => {
      if (alrarmData.data?.filter(item => item.label === metaName).length) {
        message.error(`该${metaTitle}已存在，请勿重复添加`);
        return;
      }
      return new Promise(resolve => {
        createMetaData({
          params: {
            name: metaName,
            type: metaType,
          },
          callback: () => {
            resolve(true);
          },
        });
      });
    },
    [alrarmData.data, createMetaData, metaType]
  );

  return (
    <Space direction="vertical">
      <div
        style={{
          display: 'flex',
          width: '100%',
          height: 304,
          padding: '16px 0',
        }}
      >
        <div>
          <Space align="start" size={0}>
            <span style={{ display: 'inline-block', verticalAlign: 'top' }}>
              {`${metaTitle}`}：
            </span>
            <MetaDataCard
              maxLength={10}
              list={alrarmData.data.map(d => ({
                ...d,
                id: d.id,
                name: d.label,
              }))}
              placeholder={`添加${metaTitle}`}
              onAdd={addMetaData}
              onEdit={(
                id: number,
                metaName: string,
                isDuplicated,
                callback: (result: boolean) => void
              ) => {
                if (isDuplicated) {
                  message.error(`该${metaTitle}已存在，请勿重复添加`);
                  return;
                }
                editMetaData({
                  params: {
                    id,
                    name: metaName,
                    type: metaType,
                  },
                  callback,
                });
              }}
              getList={() => readMetaData()}
              metaType={metaType}
              deleteCategory={(id: number, callback?: (result?: boolean) => void) => {
                deleteMetaData({
                  params: {
                    id,
                    metaType: metaType,
                  },
                  callback,
                });
              }}
            />
          </Space>
        </div>
      </div>
    </Space>
  );
}
