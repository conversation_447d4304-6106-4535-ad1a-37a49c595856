/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';

import { AdjustAccountTypeConfiguration } from './components/adjust-account-configuration/adjust-account-type';
import { ManuallyAddExpenseAccountsConfiguration } from './components/adjust-account-configuration/manually-add-expense-accounts';
import { TaxRateConfiguration } from './components/adjust-account-configuration/tax-rate';
import { AlarmLevelConfiguration } from './components/alarm-configuration/alarm-level/alarm-level';
import { AlarmReasonConfiguration } from './components/alarm-configuration/alarm-reason/alarm-reason';
import { CommonApproval } from './components/approval-configuration';
import { ChangeLevelConfiguration } from './components/change-configuration/change-level';
import { ChangeReasonConfiguration } from './components/change-configuration/change-reason';
import { ChangeSourceConfiguration } from './components/change-configuration/change-source';
import { ChangeTypeConfiguration } from './components/change-configuration/change-type';
import { ChangeShiftMatterConfiguration } from './components/change-shift/change-shift-matter';
import { CustomerCodeConfiguration } from './components/customer-configuration/customer-code';
import { CustomerLevelConfiguration } from './components/customer-configuration/customer-level';
import { CustomerServiceTypeConfiguration } from './components/customer-configuration/customer-service-type';
import { IndustryConfiguration } from './components/customer-configuration/industry';
import { QuotationUnitConfiguration } from './components/customer-configuration/quotation-unit';
import { DeviceTypeUnitConfiguration } from './components/device-configuration/device-type-unit';
import { DeviceUnitConfiguration } from './components/device-configuration/device-unit';
import { GridBizTypeConfiguration } from './components/device-configuration/grid-biz-type';
import { RoomTypeConfiguration } from './components/device-configuration/room-type';
import { DrillLevel } from './components/drill-configuration/drill-level';
import { DrillProfessionalType } from './components/drill-configuration/drill-professional-type';
import { ChangeOnlineCategoryConfiguration } from './components/easy-change-configuration/change-online-category';
import { ChangeOnlineLevelConfiguration } from './components/easy-change-configuration/change-online-level';
import { ChangeOnlineReasonConfiguration } from './components/easy-change-configuration/change-online-reason';
import { ChangeOnlineSourceConfiguration } from './components/easy-change-configuration/change-online-source';
import { ChangeOnlineTypeConfiguration } from './components/easy-change-configuration/change-online-type';
import { EmergencyProcessConfiguration } from './components/emergency-process-mutator/emergency-process';
import { CategoryTypeConfiguration } from './components/event-configuration/category-type';
import { EventLevelConfiguration } from './components/event-configuration/event-level';
import { EventLevelBetaConfiguration } from './components/event-configuration/event-level-beta';
import { EventMajorConfiguration } from './components/event-configuration/event-major';
import { EventSourceConfiguration } from './components/event-configuration/event-source';
import { EventTopCategoryConfiguration } from './components/event-configuration/event-type';
import { InfluenceScopeConfiguration } from './components/event-configuration/influence-scope';
import { ReansonTypeConfiguration } from './components/event-configuration/reason-type';
import { ResponsibleSectorConfiguration } from './components/event-configuration/responsible-sector';
import { KnowledgeHubConfiguration } from './components/knowledge-configuration';
import { EvalPositionConfiguration } from './components/performance-configuration/position';
import { RiskCategoryConfiguration } from './components/risk-register-configuration/risk-category';
import { RiskLevelConfiguration } from './components/risk-register-configuration/risk-level';
import { RiskPriorityConfiguration } from './components/risk-register-configuration/risk-priority';
import { RiskSourceConfiguration } from './components/risk-register-configuration/risk-source';
import { RiskTypeConfiguration } from './components/risk-register-configuration/risk-type';
import { JobLabelsConfiguration } from './components/staff-propriety/job-labels';
import { PositionYGConfiguration } from './components/staff-propriety/position-yg';
import { CellPhoneAreaCodeConfiguration } from './components/ticket-configuration/cell-phone-area-code';
import { DeviceGeneralConfiguration } from './components/ticket-configuration/device-general';
import { InformationServiceTopCategoryConfiguration } from './components/ticket-configuration/information-technology-service';
import { InspectionSceneConfiguration } from './components/ticket-configuration/inspection-scene';
import { VisitorConfiguration } from './components/ticket-configuration/visitor';
import { VisitorCarryPersonalGoodsConfiguration } from './components/ticket-configuration/visitor-carry-personal-goods';

export type MetaDataConfigurationProps = {};
const { TabPane } = Tabs;
export function MetaDataConfiguration() {
  const [configUtil] = useConfigUtil();
  const {
    riskRegisters: { features },
    events,
    changes,
  } = configUtil.getScopeCommonConfigs('ticket');
  const needPositionYG = (configUtil.getScopeCommonConfigs as any)('iam')?.userProfile
    ?.needPosition;

  return (
    <Card>
      <Tabs>
        <TabPane key="event" tab="事件">
          <Space size={32} wrap>
            <EventLevelConfiguration />
            <EventTopCategoryConfiguration />
            <ReansonTypeConfiguration />
            <ResponsibleSectorConfiguration />
            <EventSourceConfiguration />
            <CategoryTypeConfiguration />
            <EventLevelBetaConfiguration />
            {events.features.impactScopeUseMetadata ? <InfluenceScopeConfiguration /> : null}
            <EventMajorConfiguration />
          </Space>
        </TabPane>
        <TabPane key="alarm" tab="告警">
          <Space size={32} wrap>
            <AlarmLevelConfiguration />
            <AlarmReasonConfiguration />
          </Space>
        </TabPane>
        {changes.features.showNewDetail ? null : (
          <TabPane key="change" tab="变更">
            <Space size={32}>
              <ChangeTypeConfiguration />
              <ChangeReasonConfiguration />
              <ChangeSourceConfiguration />
              <ChangeLevelConfiguration />
            </Space>
          </TabPane>
        )}
        <TabPane key="device" tab="设备">
          <Space size={32}>
            <DeviceUnitConfiguration />
            <RoomTypeConfiguration />
            <DeviceTypeUnitConfiguration />
            <GridBizTypeConfiguration />
          </Space>
        </TabPane>
        <TabPane key="adjust" tab="对账">
          <Space size={32}>
            <AdjustAccountTypeConfiguration />
            <ManuallyAddExpenseAccountsConfiguration />
            <TaxRateConfiguration />
          </Space>
        </TabPane>
        <TabPane key="currencyTicket" tab="工单">
          <Space size={32} wrap>
            <DeviceGeneralConfiguration />
            <VisitorConfiguration />
            <InformationServiceTopCategoryConfiguration />
            <VisitorCarryPersonalGoodsConfiguration />
            <CellPhoneAreaCodeConfiguration />
            <InspectionSceneConfiguration />
          </Space>
        </TabPane>
        <TabPane key="knowledgeHub" tab="知识中心">
          <KnowledgeHubConfiguration />
        </TabPane>
        <TabPane key="customer" tab="客户">
          <Space size={32} wrap>
            <CustomerLevelConfiguration />
            <IndustryConfiguration />
            <CustomerCodeConfiguration />
            <CustomerServiceTypeConfiguration />
            <QuotationUnitConfiguration />
          </Space>
        </TabPane>
        <TabPane key="approval" tab="审批">
          <CommonApproval />
        </TabPane>
        <TabPane key="risk" tab="风险">
          <Space size={32}>
            <RiskSourceConfiguration />
            <RiskCategoryConfiguration />
            <RiskTypeConfiguration />
            {features.priority !== 'disabled' && <RiskPriorityConfiguration />}
            <RiskLevelConfiguration />
          </Space>
        </TabPane>
        <TabPane key="drill" tab="演练">
          <Space size={32}>
            <DrillProfessionalType />
            <DrillLevel />
          </Space>
        </TabPane>
        <TabPane key="changeShift" tab="交接班">
          <Space size={32}>
            <ChangeShiftMatterConfiguration />
          </Space>
        </TabPane>
        <TabPane key="performance" tab="绩效">
          <Space size={32}>
            <EvalPositionConfiguration />
          </Space>
        </TabPane>
        <TabPane key="emergency" tab="应急">
          <Space size={32}>
            <EmergencyProcessConfiguration />
          </Space>
        </TabPane>
        <TabPane key="easyChange" tab={changes.features.showNewDetail ? '变更' : '线上变更'}>
          <Space size={32}>
            <ChangeOnlineSourceConfiguration />
            <ChangeOnlineTypeConfiguration />
            <ChangeOnlineReasonConfiguration />
            <ChangeOnlineLevelConfiguration />
            <ChangeOnlineCategoryConfiguration />
          </Space>
        </TabPane>
        <TabPane key="staffPropriety" tab="人员属性">
          <Space size={32}>
            <JobLabelsConfiguration />
            {needPositionYG && <PositionYGConfiguration />}
          </Space>
        </TabPane>
      </Tabs>
    </Card>
  );
}
