import { QuestionCircleOutlined } from '@ant-design/icons';
import debounce from 'lodash.debounce';
import isNil from 'lodash.isnil';
import omit from 'lodash.omit';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { BlockLocales } from '@manyun/resource-hub.model.block';
import { getBlockLocales } from '@manyun/resource-hub.model.block';
import type { Coordinates } from '@manyun/resource-hub.model.block/block';
import type { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';
import { ResourceSpecs } from '@manyun/resource-hub.model.spec';
import { BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_AUTH_CODE } from '@manyun/resource-hub.route.resource-routes';
import { fetchBlock } from '@manyun/resource-hub.service.fetch-block';
import type { EntranceAuthGroups } from '@manyun/resource-hub.service.fetch-entrance-auth-groups';
import { fetchEntranceAuthGroups } from '@manyun/resource-hub.service.fetch-entrance-auth-groups';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { syncAuthGroups } from '@manyun/resource-hub.service.sync-auth-groups';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import { fetchAppletQrCode } from '@manyun/sentry.service.fetch-applet-qr-code';

type BasicInfo = {
  idcTag: string;
  name: string;
  principal: string;
  tag: string;
  period: string;
  blockType: string;
  operationStatus: string;
  constructTime: string;
  operationTime: string;
};

export function BlockDetail() {
  const locales = omit(getBlockLocales(), ['type', 'status']);
  const types = getBlockLocales().type;
  const statusTypes = getBlockLocales().status;
  const [loading, setLoading] = useState(false);
  const { id: blockId, blockGuid } = useParams<{ id: string; blockGuid: string }>();
  const [blockPicture, setBlockPicture] = useState<McUploadFile>();
  const [hasEntrance, setHasEntrance] = useState(false);
  const [basicInfo, setBasicInfo] = useState<Partial<BasicInfo>>({});
  const [clockInfo, setClockInfo] = useState<{
    coordinates?: Coordinates | null;
    area?: number | null;
  }>({});
  const [specInfo, setSpecInfo] = useState<(SpecJSON & BackendSpec)[]>([]);

  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  // @ts-ignore
  const dockingStation = ticketScopeCommonConfigs?.entryOfPersons?.features?.dockingStation;

  const getSpecs = useCallback(async () => {
    if (!blockDeviceType) {
      return;
    }
    const params: {
      deviceType: string;
      modelId?: string;
    } = { deviceType: blockDeviceType };
    if (blockGuid) {
      params.modelId = blockGuid;
    }
    const { data, error } = await fetchSpecs(params);
    if (error) {
      message.error(error.message);
      return;
    }
    setSpecInfo(data.data.filter(item => !!item.specValue));
    const hasEntranceSpecValue = data.data.find(
      item => item.specCode === ResourceSpecs.HasEntrance
    )?.specValue;
    setHasEntrance(hasEntranceSpecValue === '是');
  }, [blockDeviceType, blockGuid]);

  useEffect(() => {
    getSpecs();
  }, [getSpecs]);

  useEffect(() => {
    (async () => {
      if (blockGuid && blockId) {
        setLoading(true);
        const { error: fileError, data: fileData } = await fetchBizFileInfos({
          targetId: blockGuid,
          targetType: 'BLOCK',
        });
        if (fileError) {
          message.error(fileError.message);
        }
        if (fileData.data.length) {
          setBlockPicture(fileData.data[0]);
        }
        const { error, data } = await fetchBlock({ guid: blockGuid });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        if (data) {
          setBasicInfo({
            idcTag: data.idc,
            tag: data.tag,
            name: data.name,
            principal: data.owner.name,
            period: data.constructionCycleText,
            blockType: types[data.type],
            constructTime: moment(data.constructedAt).format('YYYY-MM-DD'),
            operationTime: moment(data.putIntoProductionAt).format('YYYY-MM-DD'),
            operationStatus: statusTypes[data.status],
          });
          setClockInfo({
            coordinates: data.coordinates,
            area: data.clockOnOffRadius,
          });
        }
      }
    })();
  }, [blockId, blockGuid, types, statusTypes]);

  const basicInfos = useMemo(() => (basicInfo ? Object.keys(basicInfo) : []), [basicInfo]);

  const [entranceAuthGroups, setEntranceAuthGroups] = useState<EntranceAuthGroups[]>([]);

  useEffect(() => {
    (async () => {
      if (!hasEntrance || !blockGuid) {
        return;
      }
      setLoading(true);
      const { error, data } = await fetchEntranceAuthGroups({
        blockGuid,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
      }
      setEntranceAuthGroups(data.data);
    })();
  }, [blockGuid, hasEntrance]);

  const handleSyncAuthGroups = useCallback(async () => {
    const { error } = await syncAuthGroups({
      blockGuid,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
    } else {
      message.success('同步成功');
    }
  }, [blockGuid]);

  const renderItem = (title: string, value: string) => {
    if (title === '状态') {
      return <Tag color={value === '已启用' ? 'success' : 'default'}>{value}</Tag>;
    }
    return value;
  };

  const onDownloads = debounce(async () => {
    if (!basicInfo.idcTag && !blockGuid) {
      message.error('无楼栋guid');
      return;
    }
    let objectUrl;
    const pathUrl = window.location.hostname;
    try {
      const { data, error } = await fetchAppletQrCode({
        page: 'pages/scanVisitorRegister/index',
        scene: `idcTag=${basicInfo.idcTag}&blockGuid=${blockGuid}`,
        envVersion: pathUrl.includes('inc')
          ? 'develop'
          : pathUrl.includes('local')
            ? 'trial'
            : undefined,
      });
      if (error) {
        console.error('Error initiating QR Code download ', error);
      }

      if (data && typeof data === 'string' && data.length > 0) {
        const base64String = data;

        // 提取 Base64 内容和 MIME 类型
        // 如果 Base64 字符串带了 "data:image/jpeg;base64," 前缀，需要处理掉
        // /9j/ 开头通常是 JPEG
        const mimeType = 'image/jpeg';
        const base64Content = base64String.includes(',')
          ? base64String.split(',')[1]
          : base64String;

        // 将 Base64 字符串解码为二进制数据 (Blob)
        const byteCharacters = atob(base64Content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mimeType });

        // 创建一个临时的 URL (对象 URL)
        objectUrl = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = objectUrl; // 现在这里是一个有效的 URL
        link.download = `${basicInfo.idcTag}_${blockGuid}_qrcode.jpeg`; // 建议下载的文件名
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      message.error('下载二维码失败，请稍后再试。');
      console.error('Error initiating QR Code download on PC:', error);
    } finally {
      message.destroy(); // 移除加载提示
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    }
  }, 500);

  return (
    <LayoutContent
      pageCode={BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_AUTH_CODE}
      composeBreadcrumbs={value => [
        ...value,
        {
          key: BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_AUTH_CODE,
          text: getSpaceGuidMap(blockGuid).block,
        },
      ]}
    >
      <Spin spinning={loading}>
        <Space direction="vertical" style={{ width: '100%' }} size={16}>
          <Card
            title={
              <Typography.Title showBadge level={5}>
                基本信息
              </Typography.Title>
            }
          >
            <Descriptions column={4}>
              {basicInfos.map(item => {
                const title = locales[item as keyof Omit<BlockLocales, 'type' | 'status'>] ?? '';
                const value = basicInfo[item as keyof BasicInfo] ?? '';
                return (
                  <Descriptions.Item key={value} label={title}>
                    {renderItem(title, value)}
                  </Descriptions.Item>
                );
              })}
              <Descriptions.Item label="图片">
                {blockPicture?.name && (
                  <FilePreviewWithContainer
                    key="preview"
                    file={{
                      name: blockPicture?.name!,
                      src: blockPicture.src,
                      ext: blockPicture.ext,
                    }}
                  >
                    <Button type="link" compact>
                      {blockPicture.name}
                    </Button>
                  </FilePreviewWithContainer>
                )}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          <Card
            title={
              <Typography.Title showBadge level={5}>
                属性信息
              </Typography.Title>
            }
          >
            <Descriptions column={4}>
              {specInfo.map(item => {
                let value = item.specValue;
                if (item.specValue === 'AUTH_PERIOD') {
                  value = '授权有效期内';
                }
                if (item.specValue === 'REAL') {
                  value = '实时';
                }
                return (
                  <Descriptions.Item key={item.specCode} label={item.specName}>
                    {item.inputWay === 'COMPONENT' ? (
                      <div
                        style={
                          item.description ? { display: 'flex', alignItems: 'center' } : undefined
                        }
                      >
                        <SpaceText guid={value!} />
                        {item.description && (
                          <Tooltip title={item.description}>
                            <QuestionCircleOutlined
                              style={{
                                marginLeft: 4,
                              }}
                            />
                          </Tooltip>
                        )}
                      </div>
                    ) : (
                      value
                    )}
                    {item.unit ? <Typography.Text>{item.unit}</Typography.Text> : null}
                  </Descriptions.Item>
                );
              })}
              {!dockingStation && (
                <Descriptions.Item label="访客登记二维码">
                  <Button
                    style={{ position: 'relative', top: '-5px' }}
                    type="link"
                    onClick={onDownloads}
                  >
                    下载
                  </Button>
                </Descriptions.Item>
              )}
            </Descriptions>
          </Card>
          <Card
            title={
              <Typography.Title showBadge level={5}>
                打卡规则
              </Typography.Title>
            }
          >
            <Descriptions column={4}>
              <Descriptions.Item label={locales.longitude}>
                {!isNil(clockInfo.coordinates?.long) ? clockInfo.coordinates?.long : '--'}
              </Descriptions.Item>
              <Descriptions.Item label={locales.latitude}>
                {!isNil(clockInfo.coordinates?.lat) ? clockInfo.coordinates?.lat : '--'}
              </Descriptions.Item>
              <Descriptions.Item label={locales.clockArea}>
                {clockInfo?.area || '--'}
              </Descriptions.Item>
            </Descriptions>
          </Card>
          {hasEntrance ? (
            <Card
              title={
                <Typography.Title showBadge level={5}>
                  门禁权限组
                </Typography.Title>
              }
              loading={loading}
              extra={
                hasEntrance && (
                  <Button type="default" onClick={handleSyncAuthGroups}>
                    同步权限组
                  </Button>
                )
              }
            >
              {entranceAuthGroups.map(group => {
                return (
                  <Card key={group.id}>
                    <Typography.Text>{group.authName}</Typography.Text>
                  </Card>
                );
              })}
            </Card>
          ) : (
            <Card
              title={
                <Typography.Title showBadge level={5}>
                  门禁配置
                </Typography.Title>
              }
              loading={loading}
            >
              <Descriptions column={4}>
                <Descriptions.Item label="是否集成属地门禁">否</Descriptions.Item>
              </Descriptions>
            </Card>
          )}
        </Space>
      </Spin>
    </LayoutContent>
  );
}
