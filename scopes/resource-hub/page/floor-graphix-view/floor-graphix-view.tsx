import { useApolloClient } from '@apollo/client';
import cloneDeep from 'lodash.clonedeep';
import get from 'lodash.get';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useParams } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { StoreInstance } from '@manyun/dc-brain.aura-graphix';
import {
  AuraGraphixPreview as Preview,
  ThemeCompositions,
  createStore,
} from '@manyun/dc-brain.aura-graphix';
import '@manyun/dc-brain.ui.custom-shapes/alerting-marker';
import '@manyun/dc-brain.ui.custom-shapes/highlight';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import type { DeviceCount } from '@manyun/monitoring.service.fetch-points-alarm-count';
import { fetchPointsAlarmCount } from '@manyun/monitoring.service.fetch-points-alarm-count';
import { fetchTopology } from '@manyun/monitoring.service.fetch-topology';
import {
  SUBSCRIPTIONS_MODE,
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { generateGetDeviceAlarmStatus } from '@manyun/monitoring.util.get-monitoring-data';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import type { GraphixFloorViewRouteParams } from '@manyun/resource-hub.route.resource-routes';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { fixElementAttrs } from './util';

const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
const moduleId = 'floor-graphix-preview';
const ALERTING_COLOR = '#ff4d4f';
//楼层视图路由已删除
export function FloorGraphixView() {
  const { idc, block, floor } = useParams<GraphixFloorViewRouteParams>();
  const spaceGuid = useMemo(
    () => (idc && block && floor ? idc + '.' + block + '.' + floor : undefined),
    [block, floor, idc]
  );
  const blockGuid = getSpaceGuid(idc, block);
  const [store, setStore] = React.useState<StoreInstance | null>(null);
  const dispatch = useDispatch();
  const [roomGuids, setRoomGuids] = useState<string[]>([]);
  const [roomDevicesAlerts, setRoomDevicesAlert] = useState<DeviceCount>({});

  useShallowCompareEffect(() => {
    (async () => {
      if (blockGuid) {
        dispatch(subscribe({ mode, blockGuid, moduleId, deviceGuids: roomGuids }));
      }
      if (!spaceGuid) {
        return;
      }
      const { data, error } = await fetchTopology({
        blockGuid: spaceGuid,
        topologyType: 'FLOOR',
      });
      if (error) {
        message.error(error.message);
        return;
      }

      if (data?.graph) {
        const __store = createStore();
        const clone = cloneDeep({
          ...data.graph,
          pages: [
            {
              ...data.graph.pages[0],
              background: 'transparent',
            },
          ],
        });
        if (clone.pages?.length) {
          const roomGroups = clone.pages[0].children.filter(
            item => item.custom.type === 'room_group'
          );
          const rooms = roomGroups.map(item => item.children).flat();
          const roomGuids = rooms
            .filter((item: { custom: { type: string } }) => item.custom.type === 'room')
            .map(item => item.id);
          if (roomGuids.length) {
            setRoomGuids(roomGuids);
          }
        }

        fixElementAttrs(clone.pages[0].children);

        clone.pages[0].children.forEach(elem => {
          if (elem.custom?.type === 'room_group') {
            const roomRect = elem.children.find(
              (child: { custom: { type: string } }) => child.custom?.type === 'room'
            );
            if (roomRect) {
              elem.children.push(
                {
                  id: elem.id + '_highlight',
                  type: 'highlight',
                  width: elem.width,
                  height: elem.height,
                  locked: true,
                  animating: false,
                },
                {
                  id: `${elem.id}_$$_alerting-marker`,
                  type: 'alerting-marker',
                  x: elem.width - 13,
                  y: 13,
                  locked: true,
                  animating: false,
                }
              );
            }
          }
        });

        __store.loadJSON(clone).then(() => {
          setStore(__store);
        });
      }
    })();

    return () => {
      setStore(null);
      if (blockGuid) {
        dispatch(unsubscribe({ mode, blockGuid, moduleId }));
      }
    };
  }, [blockGuid, dispatch, roomGuids, spaceGuid]);

  const { devicesAlarmsData } = useSelector(getMonitoringData);
  const getDeviceAlarmStatus = generateGetDeviceAlarmStatus(devicesAlarmsData);

  const client = useApolloClient();
  const floorSpace = useMemo(
    () => (spaceGuid ? readSpace(client, spaceGuid, ['FLOOR', 'ROOM_TYPE', 'ROOM']) : null),
    [client, spaceGuid]
  );

  const getRommAlarmStatus = useCallback(
    // 包间下如果有设备告警也触发包间告警
    (roomGuid: string) => {
      return (
        getDeviceAlarmStatus(roomGuid).isAlerting ||
        !!get(
          roomDevicesAlerts,
          getSpaceGuidMap(roomGuid).block + '.' + getSpaceGuidMap(roomGuid).room,
          false
        )
      );
    },
    [getDeviceAlarmStatus, roomDevicesAlerts]
  );

  const fetchRoomALarmCounts = useCallback(async () => {
    const { error, data } = await fetchPointsAlarmCount({
      idcTag: idc!,
      blockTag: block!,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (data?.roomAlarmCount) {
      setRoomDevicesAlert(data.roomAlarmCount);
    }
  }, [block, idc]);

  useShallowCompareEffect(() => {
    // 包间下的设备告警
    fetchRoomALarmCounts();
    const currentInterval = window.setInterval(fetchRoomALarmCounts, 15 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [fetchRoomALarmCounts, roomGuids]);

  useEffect(() => {
    if (!store?.activePage) {
      return;
    }

    store.activePage.children.forEach(child => {
      if (child.custom?.type === 'room_group') {
        child.children.forEach(
          (elem: {
            group: { findOne: (arg0: (child: { type: string }) => boolean) => unknown };
            type: string;
            custom: { type: string; __DEFAULT_FILL: unknown };
            id: string;
            fill: string;
            set: (arg0: { fill: unknown }) => void;
          }) => {
            const highlight = elem.group.findOne(child => child?.type === 'highlight') as {
              animating: boolean;
              set: (arg0: Record<string, unknown>) => void;
            };

            const alertingMarker = elem.group.findOne(
              child => child.type === 'alerting-marker'
            ) as {
              animating: boolean;
              set: (arg0: { animating: boolean }) => void;
            };

            if (elem.type === 'text' && elem.custom?.type === 'room_text') {
              const roomGuid = elem.id.split('_$$_').length ? elem.id.split('_$$_')[0] : undefined;

              const isAlerting = roomGuid && getRommAlarmStatus(roomGuid);
              const __DEFAULT_FILL = elem.fill;
              if (isAlerting && elem.fill !== ALERTING_COLOR) {
                highlight.set({
                  fill: ALERTING_COLOR,
                  animating: true,
                  custom: { ...elem.custom, __DEFAULT_FILL },
                  stroke: ALERTING_COLOR,
                  strokeWidth: 0,
                  shadowColor: ALERTING_COLOR,
                  shadowBlur: 12,
                });
                if (alertingMarker && alertingMarker.animating === false) {
                  alertingMarker.set({
                    animating: true,
                  });
                }
              } else if (!isAlerting && elem.fill === ALERTING_COLOR) {
                elem.set({ fill: elem.custom.__DEFAULT_FILL });
              }
            }
          }
        );
      }
    });
  }, [getDeviceAlarmStatus, getRommAlarmStatus, store]);

  let preview = null;
  if (store) {
    preview = (
      <Preview
        width="100%"
        height="100%"
        store={store}
        onElementClick={(element: {
          custom: { type: string };
          children: {
            custom: {
              type: string;
              roomGuid: string;
            };
          }[];
        }) => {
          if (element.custom.type === 'room_group') {
            const custom = element.children?.find(child => child?.custom?.type === 'room')?.custom;
            if (custom?.roomGuid) {
              window.open(
                generateRoomMonitoringUrl({
                  idc: getSpaceGuidMap(custom.roomGuid).idc!,
                  block: getSpaceGuidMap(custom.roomGuid).block!,
                  room: getSpaceGuidMap(custom.roomGuid).room!,
                })
              );
            }
          }
        }}
      />
    );
  }

  return (
    <LayoutContent
      pageCode="page_room-monitoring"
      composeBreadcrumbs={() => [
        {
          key: 'idc',
          text: idc,
        },
        {
          key: 'block',
          text: block,
        },
        {
          key: 'room',
          text: floor,
        },
      ]}
    >
      <ThemeCompositions theme={document.body.dataset.theme === 'dark' ? 'dark' : 'light'}>
        <Card
          title={
            <Space size="middle">
              <Typography.Text>{floorSpace ? floorSpace.label : `${floor} F`} </Typography.Text>
            </Space>
          }
          bodyStyle={{
            height: 'calc(var(--content-height) - 65px)',
            overflowY: 'auto',
            padding: 2,
          }}
        >
          {preview}
        </Card>
      </ThemeCompositions>
    </LayoutContent>
  );
}
