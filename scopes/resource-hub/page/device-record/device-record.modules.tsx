import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import { RedirectToEcc } from '@manyun/monitoring.ui.redirect-to-ecc';
import type { BackendDevice } from '@manyun/resource-hub.model.device';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';

/** 设备履历 */
export const BasicDeviceRecord = () => {
  const [deviceInfo, setDeviceAllInfo] = useState<BackendDevice>(); // 处理需要的设备信息
  const { guid } = useParams<{ guid: string }>();

  useEffect(() => {
    if (guid) {
      fetchDeviceByGuid({ guid }).then(({ data, error }) => {
        if (error) {
          message.error(error.message);
          return;
        }
        if (data) {
          const formatData = data.toApiObject() || {};
          setDeviceAllInfo(formatData);
        }
      });
    }
  }, [guid]);

  return (
    <RedirectToEcc
      jumpKey="device_record"
      idcTag={deviceInfo?.spaceGuid.idcTag}
      type="device"
      guid={guid}
    />
  );
};
