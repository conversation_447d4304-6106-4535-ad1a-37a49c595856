import React from 'react';

import { But<PERSON> } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { CoursewaresSelector } from '@manyun/knowledge-hub.page.course-mutator';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import { deleteCoursewaresByType } from '@manyun/resource-hub.service.delete-coursewares-by-type';
import { fetchCoursewaresByType } from '@manyun/resource-hub.service.fetch-coursewares-by-type';
import type { Courseware } from '@manyun/resource-hub.service.fetch-coursewares-by-type';
import { relateCoursewaresByType } from '@manyun/resource-hub.service.relate-coursewares-by-type';

export type CoursewareRelationProps = {
  deviceType: string;
};

export function CoursewareRelation({ deviceType }: CoursewareRelationProps) {
  const [loading, setLoading] = React.useState(false);
  const [data, setData] = React.useState<Courseware[]>([]);
  const [selectedKeys, setSeletedKeys] = React.useState<string[]>([]);
  const [, { checkCode }] = useAuthorized();
  const deleteable = checkCode('element_delete-coursewares');

  const fetchCoursewares = React.useCallback(async () => {
    setLoading(true);
    const { data, error } = await fetchCoursewaresByType({ targetId: deviceType });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(data.data);
    setSeletedKeys(data.data.map(courseware => courseware.groupKey));
  }, [deviceType]);

  const deleteCourseware = React.useCallback(
    async id => {
      const { error } = await deleteCoursewaresByType({
        targetId: deviceType,
        courseFileIds: [id],
      });
      if (error) {
        message.error(error.message);
        return;
      }
      fetchCoursewares();
    },
    [deviceType, fetchCoursewares]
  );

  const handleAdd = React.useCallback(
    async (selectedKeys: string[], selectedCoursewares: Courseware[], callback: Function) => {
      const { error } = await relateCoursewaresByType({
        targetId: deviceType,
        courseFileIds: selectedCoursewares.map(courseware => courseware.id),
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setSeletedKeys(prev => [...selectedKeys, ...prev]);
      callback();
      fetchCoursewares();
    },
    [deviceType, fetchCoursewares]
  );

  React.useEffect(() => {
    fetchCoursewares();
  }, [fetchCoursewares]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {checkCode('element_relate-coursewares') && (
        <CoursewaresSelector
          btnText="关联"
          modelText="关联知识课件"
          handleAdd={handleAdd}
          coursewareKeys={selectedKeys}
        />
      )}
      <Table
        rowKey="groupKey"
        loading={loading}
        scroll={{ x: 'max-content' }}
        columns={getColumns(deleteCourseware, deleteable)}
        dataSource={data}
      />
    </Space>
  );
}

const getColumns: (callback: Function, deleteable: boolean) => ColumnsType<Courseware> = (
  callback,
  deleteable
) => {
  return [
    {
      title: '课件名称',
      dataIndex: 'fileName',
      ellipsis: true,
      render: (text: string, record: Courseware) => {
        if (!record.filePath) {
          return text;
        }
        return (
          <FilePreviewWithContainer
            key="preview"
            file={{
              name: record.fileName,
              src: McUploadFile.generateSrc(record.filePath, record.fileName),
              ext: record.fileType.toLocaleLowerCase(),
            }}
          >
            <Button type="link" compact>
              {text}
            </Button>
          </FilePreviewWithContainer>
        );
      },
    },
    {
      title: '课件分类',
      dataIndex: 'categoryCode',
      ellipsis: true,
      render: (text: string) => (
        <CoursewareCategory categoryCode={Number(text)} variant={Variant.ALL} />
      ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      render: (_, record: Courseware) =>
        deleteable ? (
          <DeleteConfirm
            variant="popconfirm"
            targetName={record.fileName}
            onOk={() => callback(record.id)}
          >
            <Button type="link" compact>
              移除
            </Button>
          </DeleteConfirm>
        ) : (
          '--'
        ),
    },
  ];
};
