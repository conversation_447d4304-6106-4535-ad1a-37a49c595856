/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "chart/rack-use-liquid-fill": {
        "name": "chart/rack-use-liquid-fill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "chart/rack-use-liquid-fill",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "gql/client/devices": {
        "name": "gql/client/devices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "gql/client/devices",
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "gql/client/racks": {
        "name": "gql/client/racks",
        "scope": "manyun.resource-hub",
        "version": "1.0.0",
        "mainFile": "index.ts",
        "rootDir": "gql/client/racks",
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "hook/use-device-type": {
        "name": "hook/use-device-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-device-type",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-meta-data": {
        "name": "hook/use-meta-data",
        "scope": "resource-hub",
        "version": "0.0.5",
        "mainFile": "index.ts",
        "rootDir": "hook/use-meta-data",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-points": {
        "name": "hook/use-points",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-points",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-room-type": {
        "name": "hook/use-room-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-room-type",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "hook/use-spaces-type": {
        "name": "hook/use-spaces-type",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-spaces-type",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "model/block": {
        "name": "model/block",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "model/block",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/borrow-return": {
        "name": "model/borrow-return",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "model/borrow-return",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/floor": {
        "name": "model/floor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "model/floor",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/idc": {
        "name": "model/idc",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "model/idc",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/room": {
        "name": "model/room",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "model/room",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/spec": {
        "name": "model/spec",
        "scope": "manyun.resource-hub",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "model/spec",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "page/basic-resource-configuration": {
        "name": "page/basic-resource-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/basic-resource-configuration",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/block-detail": {
        "name": "page/block-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/block-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/block-mutator": {
        "name": "page/block-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/block-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/borrow-return": {
        "name": "page/borrow-return",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/borrow-return",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/borrow-return-create": {
        "name": "page/borrow-return-create",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/borrow-return-create",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/borrow-return-detail": {
        "name": "page/borrow-return-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/borrow-return-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/courseware-relation": {
        "name": "page/courseware-relation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/courseware-relation",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/custom-point-import": {
        "name": "page/custom-point-import",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/custom-point-import",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/device-record": {
        "name": "page/device-record",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/device-record",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/floor-graphix": {
        "name": "page/floor-graphix",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/floor-graphix",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/floor-graphix-view": {
        "name": "page/floor-graphix-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/floor-graphix-view",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/floor-management": {
        "name": "page/floor-management",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/floor-management",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/meta-data-configuration": {
        "name": "page/meta-data-configuration",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "page/meta-data-configuration",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/resource-routes": {
        "name": "route/resource-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "route/resource-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/borrow-asset": {
        "name": "service/borrow-asset",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/borrow-asset",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/cancel-borrow": {
        "name": "service/cancel-borrow",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/cancel-borrow",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/cancel-renew-lend-borrow": {
        "name": "service/cancel-renew-lend-borrow",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/cancel-renew-lend-borrow",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/confirm-borrow-all-out": {
        "name": "service/confirm-borrow-all-out",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/confirm-borrow-all-out",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-borrow": {
        "name": "service/create-borrow",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-borrow",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-borrow-resubmit": {
        "name": "service/create-borrow-resubmit",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-borrow-resubmit",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-floor": {
        "name": "service/create-floor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-floor",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-meta-data": {
        "name": "service/create-meta-data",
        "scope": "resource-hub",
        "version": "0.0.14",
        "mainFile": "index.ts",
        "rootDir": "service/create-meta-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-point-instance": {
        "name": "service/create-point-instance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-point-instance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-spec": {
        "name": "service/create-spec",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-spec",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-topology": {
        "name": "service/create-topology",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/create-topology",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-coursewares-by-type": {
        "name": "service/delete-coursewares-by-type",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-coursewares-by-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-floor": {
        "name": "service/delete-floor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-floor",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-meta-data": {
        "name": "service/delete-meta-data",
        "scope": "resource-hub",
        "version": "0.0.14",
        "mainFile": "index.ts",
        "rootDir": "service/delete-meta-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-point-instance": {
        "name": "service/delete-point-instance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-point-instance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-spec": {
        "name": "service/delete-spec",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-spec",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-stock-threshold": {
        "name": "service/delete-stock-threshold",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/delete-stock-threshold",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/download-custom-point-template": {
        "name": "service/download-custom-point-template",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/download-custom-point-template",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/edit-meta-data": {
        "name": "service/edit-meta-data",
        "scope": "resource-hub",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/edit-meta-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-custom-point": {
        "name": "service/export-custom-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/export-custom-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-point": {
        "name": "service/export-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/export-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-block": {
        "name": "service/fetch-block",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-block",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-block-types": {
        "name": "service/fetch-block-types",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-block-types",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-blocks": {
        "name": "service/fetch-blocks",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-blocks",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-blocks-by-permission": {
        "name": "service/fetch-blocks-by-permission",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-blocks-by-permission",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrow-applys": {
        "name": "service/fetch-borrow-applys",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrow-applys",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrow-assets": {
        "name": "service/fetch-borrow-assets",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrow-assets",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrow-can-return-assets": {
        "name": "service/fetch-borrow-can-return-assets",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrow-can-return-assets",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrow-info": {
        "name": "service/fetch-borrow-info",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrow-info",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrow-is-all-out": {
        "name": "service/fetch-borrow-is-all-out",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrow-is-all-out",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrow-renew-lend-records": {
        "name": "service/fetch-borrow-renew-lend-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrow-renew-lend-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-borrows": {
        "name": "service/fetch-borrows",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-borrows",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-budget-influence": {
        "name": "service/fetch-budget-influence",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-budget-influence",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-cabinet-type": {
        "name": "service/fetch-cabinet-type",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-cabinet-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-child-devices-by-guid": {
        "name": "service/fetch-child-devices-by-guid",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-child-devices-by-guid",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-coursewares-by-type": {
        "name": "service/fetch-coursewares-by-type",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-coursewares-by-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-data-center-list": {
        "name": "service/fetch-data-center-list",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-data-center-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-alarm-level": {
        "name": "service/fetch-device-alarm-level",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-alarm-level",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-alarm-status-count": {
        "name": "service/fetch-device-alarm-status-count",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-alarm-status-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-asset-info": {
        "name": "service/fetch-device-asset-info",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-asset-info",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-by-guid": {
        "name": "service/fetch-device-by-guid",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-by-guid",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-by-keyword": {
        "name": "service/fetch-device-by-keyword",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-by-keyword",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-change-count": {
        "name": "service/fetch-device-change-count",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-change-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-channel": {
        "name": "service/fetch-device-channel",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-channel",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-inventory-list": {
        "name": "service/fetch-device-inventory-list",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-inventory-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-inventory-time": {
        "name": "service/fetch-device-inventory-time",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-inventory-time",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-norm": {
        "name": "service/fetch-device-norm",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-norm",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-num": {
        "name": "service/fetch-device-num",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-num",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-schedule-calendar": {
        "name": "service/fetch-device-schedule-calendar",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-schedule-calendar",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-status-count": {
        "name": "service/fetch-device-status-count",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-status-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-status-interval": {
        "name": "service/fetch-device-status-interval",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-status-interval",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-task-list": {
        "name": "service/fetch-device-task-list",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-task-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-device-type": {
        "name": "service/fetch-device-type",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-device-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-devices": {
        "name": "service/fetch-devices",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-devices",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-entrance-auth-groups": {
        "name": "service/fetch-entrance-auth-groups",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-entrance-auth-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-facility-count": {
        "name": "service/fetch-facility-count",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-facility-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-floors": {
        "name": "service/fetch-floors",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-floors",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-grid-customers": {
        "name": "service/fetch-grid-customers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-grid-customers",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-grid-statistics": {
        "name": "service/fetch-grid-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-grid-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-grid-status-statistics": {
        "name": "service/fetch-grid-status-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-grid-status-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-idc": {
        "name": "service/fetch-idc",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-idc",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-idcs": {
        "name": "service/fetch-idcs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-idcs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-influence": {
        "name": "service/fetch-influence",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-influence",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-meta-data-by-type": {
        "name": "service/fetch-meta-data-by-type",
        "scope": "resource-hub",
        "version": "0.0.15",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-meta-data-by-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point": {
        "name": "service/fetch-point",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-instance-by-id": {
        "name": "service/fetch-point-instance-by-id",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-instance-by-id",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-point-instance-list": {
        "name": "service/fetch-point-instance-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-point-instance-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-points": {
        "name": "service/fetch-points",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-points",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-points-by-condition": {
        "name": "service/fetch-points-by-condition",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-points-by-condition",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-rack-columns": {
        "name": "service/fetch-rack-columns",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-rack-columns",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-rack-utilization-statistics": {
        "name": "service/fetch-rack-utilization-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-rack-utilization-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-room": {
        "name": "service/fetch-room",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-room",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-room-list": {
        "name": "service/fetch-room-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-room-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-room-types": {
        "name": "service/fetch-room-types",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-room-types",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-rooms": {
        "name": "service/fetch-rooms",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-rooms",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-rooms-by-block": {
        "name": "service/fetch-rooms-by-block",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-rooms-by-block",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-spaces-under-block": {
        "name": "service/fetch-spaces-under-block",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-spaces-under-block",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-spare-num": {
        "name": "service/fetch-spare-num",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-spare-num",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-specs": {
        "name": "service/fetch-specs",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-specs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-stock-thresholds": {
        "name": "service/fetch-stock-thresholds",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-stock-thresholds",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-tree-mode-meta-data": {
        "name": "service/fetch-tree-mode-meta-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-tree-mode-meta-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-resources": {
        "name": "service/fetch-user-resources",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-resources",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-custom-point": {
        "name": "service/import-custom-point",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/import-custom-point",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-block": {
        "name": "service/mutate-block",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-block",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/mutate-stock-threshold": {
        "name": "service/mutate-stock-threshold",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/mutate-stock-threshold",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/relate-coursewares-by-type": {
        "name": "service/relate-coursewares-by-type",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/relate-coursewares-by-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/renew-borrow-asset": {
        "name": "service/renew-borrow-asset",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/renew-borrow-asset",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/return-borrow-asset": {
        "name": "service/return-borrow-asset",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/return-borrow-asset",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/revert-borrow": {
        "name": "service/revert-borrow",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/revert-borrow",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/sync-auth-groups": {
        "name": "service/sync-auth-groups",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/sync-auth-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/transfer-borrow-asset": {
        "name": "service/transfer-borrow-asset",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/transfer-borrow-asset",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-core-points": {
        "name": "service/update-core-points",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-core-points",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-device": {
        "name": "service/update-device",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-device",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-floor": {
        "name": "service/update-floor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-floor",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-point-instance": {
        "name": "service/update-point-instance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-point-instance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-spec": {
        "name": "service/update-spec",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-spec",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-topology": {
        "name": "service/update-topology",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "service/update-topology",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/device-types": {
        "name": "state/device-types",
        "scope": "resource-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "state/device-types",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/meta-data": {
        "name": "state/meta-data",
        "scope": "resource-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "state/meta-data",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/points": {
        "name": "state/points",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "state/points",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/room-type": {
        "name": "state/room-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "state/room-type",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "state/space": {
        "name": "state/space",
        "scope": "resource-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "state/space",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "ui/abstract-point-select": {
        "name": "ui/abstract-point-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/abstract-point-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/block-cover": {
        "name": "ui/block-cover",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/block-cover",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/block-infos": {
        "name": "ui/block-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/block-infos",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/borrow-asset-table": {
        "name": "ui/borrow-asset-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/borrow-asset-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/borrow-status-select": {
        "name": "ui/borrow-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/borrow-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/borrow-status-text": {
        "name": "ui/borrow-status-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/borrow-status-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/borrower-type-select": {
        "name": "ui/borrower-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/borrower-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/core-points-card": {
        "name": "ui/core-points-card",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/core-points-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/core-points-creator": {
        "name": "ui/core-points-creator",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/core-points-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/customers-on-racks-select": {
        "name": "ui/customers-on-racks-select",
        "scope": "manyun.resource-hub",
        "version": "4.0.0",
        "mainFile": "index.ts",
        "rootDir": "ui/customers-on-racks-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/device-or-space-select": {
        "name": "ui/device-or-space-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/device-or-space-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/device-select": {
        "name": "ui/device-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/device-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/device-type-cascader": {
        "name": "ui/device-type-cascader",
        "scope": "resource-hub",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "ui/device-type-cascader",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/device-type-text": {
        "name": "ui/device-type-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/device-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/di-point-state-select": {
        "name": "ui/di-point-state-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/di-point-state-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/floor-select": {
        "name": "ui/floor-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/floor-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/floor-status-select": {
        "name": "ui/floor-status-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/floor-status-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/floor-type-select": {
        "name": "ui/floor-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/floor-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/floor-type-text": {
        "name": "ui/floor-type-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/floor-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/graphix-base-map-import": {
        "name": "ui/graphix-base-map-import",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/graphix-base-map-import",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/grid-influence-select": {
        "name": "ui/grid-influence-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/grid-influence-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/idc-infos": {
        "name": "ui/idc-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/idc-infos",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/idc-select": {
        "name": "ui/idc-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/idc-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/location-tree-select": {
        "name": "ui/location-tree-select",
        "scope": "resource-hub",
        "version": "0.0.23",
        "mainFile": "index.ts",
        "rootDir": "ui/location-tree-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/meta-data-card": {
        "name": "ui/meta-data-card",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/meta-data-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/meta-data-item": {
        "name": "ui/meta-data-item",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/meta-data-item",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/meta-type-cascader": {
        "name": "ui/meta-type-cascader",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/meta-type-cascader",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/meta-type-select": {
        "name": "ui/meta-type-select",
        "scope": "resource-hub",
        "version": "0.0.7",
        "mainFile": "index.ts",
        "rootDir": "ui/meta-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/meta-type-text": {
        "name": "ui/meta-type-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/meta-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/my-blocks": {
        "name": "ui/my-blocks",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/my-blocks",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-select": {
        "name": "ui/point-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/point-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/point-type-select": {
        "name": "ui/point-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/point-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/points-table": {
        "name": "ui/points-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/points-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/power-model-select": {
        "name": "ui/power-model-select",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/power-model-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/rack-type-text": {
        "name": "ui/rack-type-text",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/rack-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/rack-types-select": {
        "name": "ui/rack-types-select",
        "scope": "resource-hub",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/rack-types-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/racks-with-pdu-devices-virtual-table": {
        "name": "ui/racks-with-pdu-devices-virtual-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/racks-with-pdu-devices-virtual-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/resource-point-drawer": {
        "name": "ui/resource-point-drawer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/resource-point-drawer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/resource-tree": {
        "name": "ui/resource-tree",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/resource-tree",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/resource-tree-transfer": {
        "name": "ui/resource-tree-transfer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/resource-tree-transfer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/room-select": {
        "name": "ui/room-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/room-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/room-type-select": {
        "name": "ui/room-type-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/room-type-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/room-type-text": {
        "name": "ui/room-type-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/room-type-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/space-or-device-link": {
        "name": "ui/space-or-device-link",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/space-or-device-link",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/spec-form-items": {
        "name": "ui/spec-form-items",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/spec-form-items",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/spec-search-select": {
        "name": "ui/spec-search-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/spec-search-select",
        "config": {
            "bitdev.react/react-env@2.1.3": {},
            "teambit.envs/envs": {
                "env": "bitdev.react/react-env"
            }
        }
    },
    "ui/spec-select": {
        "name": "ui/spec-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/spec-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/spec-unit-select": {
        "name": "ui/spec-unit-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/spec-unit-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/stock-thresholds-editable-table": {
        "name": "ui/stock-thresholds-editable-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/stock-thresholds-editable-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/topology-graphix": {
        "name": "ui/topology-graphix",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/topology-graphix",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/type-attribution": {
        "name": "ui/type-attribution",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/type-attribution",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/unit-meta-data-select": {
        "name": "ui/unit-meta-data-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/unit-meta-data-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/unit-meta-data-text": {
        "name": "ui/unit-meta-data-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/unit-meta-data-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-resource-select": {
        "name": "ui/user-resource-select",
        "scope": "",
        "version": "",
        "defaultScope": "resource-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/user-resource-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "util/type-of-racks-room": {
        "name": "util/type-of-racks-room",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.resource-hub",
        "mainFile": "index.ts",
        "rootDir": "util/type-of-racks-room",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "$schema-version": "17.0.0"
}