import cloneDeep from 'lodash.clonedeep';

import type { BackendRoom } from './backend-room.type';

export class Room {
  constructor(
    public area: number | null,
    public blockTag: string | null,
    public floor: number | null,
    public guid: string | null,
    public id: number | null,
    public idcName: string | null,
    public idcTag: string | null,
    public name: string | null,
    public operationStatus: { code: string; name: string } | null,
    public operationTime: string | null,
    public ratedPower: number | null,
    public refrigerationStructure: string | null,
    public relateRoom: string | null,
    public relateRooms: Record<string, string> | null,
    public roomType: string | null,
    public roomTypeName: string | null,
    public tag: string | null
  ) {}

  static fromApiObject(object: BackendRoom) {
    const copy = cloneDeep(object);

    return new Room(
      copy.area,
      copy.blockTag,
      copy.floor,
      copy.guid,
      copy.id,
      copy.idcName,
      copy.idcTag,
      copy.name,
      copy.operationStatus,
      copy.operationTime,
      copy.ratedPower,
      copy.refrigerationStructure,
      copy.relateRoom,
      copy.relateRooms,
      copy.roomType,
      copy.roomTypeName,
      copy.tag
    );
  }

  toApiObject(): BackendRoom {
    return cloneDeep({
      area: this.area,
      blockTag: this.blockTag,
      floor: this.floor,
      guid: this.guid,
      id: this.id,
      idcName: this.idcName,
      idcTag: this.idcTag,
      name: this.name,
      operationStatus: this.operationStatus,
      operationTime: this.operationTime,
      ratedPower: this.ratedPower,
      refrigerationStructure: this.refrigerationStructure,
      relateRoom: this.relateRoom,
      relateRooms: this.relateRooms,
      roomType: this.roomType,
      roomTypeName: this.roomTypeName,
      tag: this.tag,
    });
  }
}
