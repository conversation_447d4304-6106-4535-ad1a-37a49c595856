export enum RoomTypes {
  /**
   * IT 包间（可能也称为：数据机房）
   * */
  ItRoom = 'IT_ROOM',
  /**
   * 网络核心包间
   */
  NetworkCoreRoom = 'NETWORK_CORE_ROOM',
  /**
   * 仓库（可能也称为：库房）
   */
  Warehouse = 'WAREHOUSE',
  /**
   * 运营商接入室
   */
  CarrierNeutralFacility = 'OPERATOR_ACCESS_ROOM',
  /**
   * IT小套间（自增长的code，但与后端和产品确认，通常不需要考虑变化的情况）
   */
  ITSmallSuite = '113',
  /** 变配电室 */
  PowerDistributionRoom = 'POWER_DISTRIBUTION_ROOM',
  /** 电池室 */
  PowerPosition = '76',
  /** 高压配电室 */
  HighVoltageRoom = 'HIGH_VOLTAGE_ROOM',
  /** 冷站配电室 */
  ColdStationPowerRoom = 'COLD_STATION_POWER_ROOM',
  /** 强电间 */
  PowerRoom = 'POWER_ROOM',
  /** 低压配电室 */
  LowElectric = '73',
}

export type BackendRoomType = `${RoomTypes}`;

export type BackendRoom = {
  area: number | null;
  /**楼号 */
  blockTag: string | null;
  /** 楼层 */
  floor: number | null;
  guid: string | null;
  id: number | null;
  idcName: string | null;
  /** 机房 */
  idcTag: string | null;
  /** 包间名称 */
  name: string | null;
  /** 启用状态 */
  operationStatus: { code: string; name: string } | null;
  operationTime: string | null;
  ratedPower: number | null;
  refrigerationStructure: string | null;
  /** 关联包间guid，多个包间，隔开 */
  relateRoom: string | null;
  /** 关联包间map<编号，名称> */
  relateRooms: Record<string, string> | null;
  /** 包间类型code */
  roomType: string | null;
  /** 包间类型名称 */
  roomTypeName: string | null;
  /** 包间号 */
  tag: string | null;
};
