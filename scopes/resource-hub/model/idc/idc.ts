import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { SpecData } from '@manyun/resource-hub.model.device';

import { getIdcLocales } from './locales/index.js';
import type { IdcLocales } from './locales/index.js';

export type IdcType = 'DATA_CENTER';

export type IdcStatus = 'ON' | 'OFF';

export type BackendIdc = {
  guid: string;
  name: string;
  idcType: {
    code: IdcType;
    name: string;
  };
  /** 国家 */
  nationCode: string;
  /** 区域 */
  regionCode: string;
  /** 省 */
  provinceCode: string;
  /** 城市 */
  cityCode: string;
  /** 区 */
  districtCode: string;
  /**
   * 地址
   */
  address: string;
  /**
   * 状态
   */
  operationStatus: {
    code: IdcStatus;
    name: string;
  };
  /**
   * 建设日期
   *
   * Date String
   * @example
   * ```
   * "2021-11-29"
   * ```
   * */
  constructTime: string;
  /**
   * 投产日期
   *
   * Date String
   * @example
   * ```
   * "2021-11-29"
   * ```
   * */
  operationTime: string;
  /**
   * 责任人 ID
   */
  operatorId: string;
  /**
   * 责任人姓名
   */
  operatorName: string;
  /**
   * 备用责任人
   *
   * @example
   * ```
   * "[{\"userId\": 0, \"userName\": \"Jerry\"}]"
   * ```
   */
  backupOperator: string | null;
  specDataList: SpecData[] | null;
};

export type SimpleUser = {
  id: number;
  name: string;
};

export class Idc {
  static fromApiObject(object: BackendIdc) {
    const copy = cloneDeep(object);
    let backupOwners: SimpleUser[] = [];
    try {
      if (copy.backupOperator) {
        const backupOperators = JSON.parse(copy.backupOperator) as {
          userId: number;
          userName: string;
        }[];
        backupOwners = backupOperators.map(({ userId, userName }) => ({
          id: userId,
          name: userName,
        }));
      }
    } catch (error) {
      console.error('Error occurs when parsing idc backup owners property: ', copy, error);
    }

    return new Idc(
      copy.guid,
      copy.name,
      copy.idcType.code,
      copy.nationCode,
      copy.regionCode,
      copy.provinceCode,
      copy.cityCode,
      copy.districtCode,
      copy.address,
      copy.operationStatus,
      dayjs(copy.constructTime).valueOf(),
      dayjs(copy.operationTime).valueOf(),
      {
        id: Number(copy.operatorId),
        name: copy.operatorName,
      },
      backupOwners,
      copy.specDataList
    );
  }

  private _locales: IdcLocales;

  constructor(
    public guid: string,
    public name: string,
    public type: IdcType,
    public nationCode: string,
    public regionCode: string,
    public provinceCode: string,
    public cityCode: string,
    public districtCode: string,
    public address: string,
    public status: { code: IdcStatus; name: string },
    public constructedAt: number,
    /**
     * 投产日期
     */
    public putIntoProductionAt: number,
    public owner: SimpleUser,
    public backupOwners: SimpleUser[],
    public specDataList: SpecData[] | null
  ) {
    this._locales = getIdcLocales('zh-CN');
  }

  public set locales(locales: IdcLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public get typeName(): string {
    return this._locales.type[this.type];
  }

  public getFormattedConstructedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.constructedAt).format(template);
  }

  public getFormattedPutIntoProductionAt(template = 'YYYY-MM-DD') {
    return dayjs(this.putIntoProductionAt).format(template);
  }
}
