export type BorrowReturnLocales = {
  title: string;
  borrowNo: string;
  borrowType: {
    __self: string;
    INNER: string;
    OUT: string;
  };
  borrowStatus: {
    __self: string;
    WAIT_STOCK_OUT: string;
    BORROWING: string;
    RETURN: string;
    DRAFT: string;
    APPROVING: string;
    CANCELED: string;
  };
  reason: string;
  borrowDate: {
    __self: string;
    borrowStartDate: string;
    borrowEndDate: string;
  };
  source: {
    __self: string;
    idc: string;
    blockGuid: string;
  };
  borrower: {
    __self: string;
    PARTNER: string;
    CUSTOMER: string;
    SUPPLIER: string;
    OTHER: string;
  };
  creator: string;
  personLiable: string;
  approvalId: string;
  hasPermission: string;
  createAt: string;
  modifiedAt: string;
};
