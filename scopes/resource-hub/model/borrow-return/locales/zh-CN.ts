import type { BorrowReturnLocales } from './type';

export const zhCN: BorrowReturnLocales = {
  title: '标题',
  borrowNo: '编号ID',
  borrowType: {
    __self: '借用类型',
    INNER: '内部员工',
    OUT: '外部人员',
  },
  borrowStatus: {
    __self: '状态',
    WAIT_STOCK_OUT: '待借用',
    BORROWING: '待归还',
    RETURN: '已归还',
    DRAFT: '草稿',
    APPROVING: '待审批',
    CANCELED: '已取消',
  },
  reason: '借用原因',
  borrowDate: {
    __self: '借用起止日期	',
    borrowStartDate: '借用开始日期',
    borrowEndDate: '借用截止日期',
  },
  source: {
    __self: '位置',
    idc: '机房',
    blockGuid: '楼栋',
  },
  borrower: {
    __self: '借用人',
    PARTNER: '合作伙伴',
    CUSTOMER: '客户',
    SUPPLIER: '客户供应商',
    OTHER: '其他',
  },
  creator: '创建人',
  personLiable: '负责人',
  approvalId: '审批id',
  hasPermission: '是否有权限',
  createAt: '创建时间',
  modifiedAt: '修改时间',
};

export default zhCN;
