export type BorrowStatus =
  | 'WAIT_STOCK_OUT'
  | 'BORROWING'
  | 'RETURN'
  | 'DRAFT'
  | 'APPROVING'
  | 'CANCELED';

export type BorrowType = 'INNER' | 'OUT';

export const borrowTypeMap = {
  INNER: 'INNER',
  OUT: 'OUT',
};

export type BorrowerType = 'PARTNER' | 'CUSTOMER' | 'SUPPLIER' | 'OTHER';

export type BackendBorrowReturn = {
  id: string;
  title: string;
  borrowNo: string; //借用单号
  borrowType: BorrowType; //借用类型
  borrowStatus: BorrowStatus; //借用状态
  reason: string; //借用原因
  borrowStartDate: number; //借用开始日期
  borrowEndDate: number; //借用截止日期
  idcTag: string;
  blockGuid: string;
  borrower: string;
  borrowerName: string;
  borrowerType: BorrowerType; //借用人类型
  creator: string; //创建人
  creatorName: string;
  personLiable: string; //负责人ID
  personLiableName: string; //负责人名称
  approvalId: string;
  hasPermission: boolean;
  gmtCreate: number; //创建时间
  gmtModified: number | null; //修改时间
};
