import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type {
  BackendBorrowReturn,
  BorrowStatus,
  BorrowType,
  BorrowerType,
} from './backend-borrow-return';
import { getBorrowReturnLocales } from './locales';
import type { BorrowReturnLocales } from './locales';

type Source = {
  idc: string;
  blockGuid: string;
};

type Borrower = {
  id: string;
  name: string;
  type: BorrowerType;
};

type Creator = {
  id: string;
  name: string;
};

type PersonLiable = {
  id: string;
  name: string;
};

export type BorrowReturnJSON = {
  id: string;
  title: string;
  borrowNo: string;
  borrowType: BorrowType;
  borrowStatus: BorrowStatus;
  reason: string;
  borrowStartAt: number;
  borrowEndAt: number;
  source: Source;
  borrower: Borrower;
  creator: Creator;
  personLiable: PersonLiable;
  approvalId: string;
  hasPermission: boolean;
  createAt: number;
  modifiedAt: number | null;
};

export class BorrowReturn {
  static fromApiObject(object: BackendBorrowReturn) {
    const copy = cloneDeep(object);

    return new BorrowReturn(
      copy.id,
      copy.title,
      copy.borrowNo,
      copy.borrowType,
      copy.borrowStatus,
      copy.reason,
      copy.borrowStartDate,
      copy.borrowEndDate,
      {
        idc: copy.idcTag,
        blockGuid: copy.blockGuid,
      },
      {
        id: copy.borrower,
        name: copy.borrowerName,
        type: copy.borrowerType,
      },
      {
        id: copy.creator,
        name: copy.creatorName,
      },
      {
        id: copy.personLiable,
        name: copy.personLiableName,
      },
      copy.approvalId,
      copy.hasPermission,
      copy.gmtCreate,
      copy.gmtModified
    );
  }

  static fromJSON(json: BorrowReturnJSON) {
    const copy = cloneDeep(json);

    return new BorrowReturn(
      copy.id,
      copy.title,
      copy.borrowNo,
      copy.borrowType,
      copy.borrowStatus,
      copy.reason,
      copy.borrowStartAt,
      copy.borrowEndAt,
      copy.source,
      copy.borrower,
      copy.creator,
      copy.personLiable,
      copy.approvalId,
      copy.hasPermission,
      copy.createAt,
      copy.modifiedAt
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: BorrowReturnLocales;

  constructor(
    public id: string,
    public title: string,
    public borrowNo: string,
    public borrowType: BorrowType,
    public borrowStatus: BorrowStatus,
    public reason: string,
    public borrowStartAt: number,
    public borrowEndAt: number,
    public source: Source,
    public borrower: Borrower,
    public creator: Creator,
    public personLiable: PersonLiable,
    public approvalId: string,
    public hasPermission: boolean,
    public createAt: number,
    public modifiedAt: number | null
  ) {
    this._locales = getBorrowReturnLocales(this._localeCode);
  }

  public set locales(locales: BorrowReturnLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedBorrowStartAt(template = 'YYYY-MM-DD') {
    return dayjs(this.borrowStartAt).format(template);
  }

  public getFormattedBorrowEndAt(template = 'YYYY-MM-DD') {
    return dayjs(this.borrowEndAt).format(template);
  }

  public getFormattedCreateAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendBorrowReturn {
    return cloneDeep({
      id: this.id,
      title: this.title,
      borrowNo: this.borrowNo,
      borrowType: this.borrowType,
      reason: this.reason,
      borrowStartDate: this.borrowStartAt,
      borrowEndDate: this.borrowEndAt,
      idcTag: this.source.idc,
      blockGuid: this.source.blockGuid,
      borrower: this.borrower.id,
      borrowerName: this.borrower.name,
      borrowerType: this.borrower.type,
      creator: this.creator.id,
      creatorName: this.creator.name,
      personLiable: this.personLiable.id,
      personLiableName: this.personLiable.name,
      approvalId: this.approvalId,
      borrowStatus: this.borrowStatus,
      hasPermission: this.hasPermission,
      gmtCreate: this.createAt,
      gmtModified: this.modifiedAt,
    });
  }

  public toJSON(): BorrowReturnJSON {
    return cloneDeep({
      id: this.id,
      title: this.title,
      borrowNo: this.borrowNo,
      borrowType: this.borrowType,
      borrowStatus: this.borrowStatus,
      reason: this.reason,
      borrowStartAt: this.borrowStartAt,
      borrowEndAt: this.borrowEndAt,
      source: this.source,
      borrower: this.borrower,
      creator: this.creator,
      personLiable: this.personLiable,
      approvalId: this.approvalId,
      hasPermission: this.hasPermission,
      createAt: this.createAt,
      modifiedAt: this.modifiedAt,
    });
  }
}
