export type SpecLocales = {
  /** 规格名称 */
  specName: string;
  /** 是否必填值 */
  required: string;
  /** 属性值选择方式 */
  inputWay: string;
  /** 选择项, 如'test1,test2' */
  options: string;
  /** 属性值类型，判断只能为NUMBER时才可以作为参数选中 */
  valueType: string;
  /** 单位 */
  specUnit: string;
  /** 属性值-数值 */
  valueTypeNumber: string;
  /** 属性值-文本 */
  valueTypeCharacter: string;
  inputWayOpt: string;
  inputWayInput: string;
  inputWayComponent: string;
  type: string;
  description: string; // TODO: diff and tag
};
