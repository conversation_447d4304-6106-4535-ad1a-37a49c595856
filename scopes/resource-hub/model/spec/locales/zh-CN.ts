import type { SpecLocales } from './type';

export const zhCN: SpecLocales = {
  /** 规格名称 */
  specName: '属性名称',
  /** 是否必填值 */
  required: '是否必填',
  /** 属性值选择方式 */
  inputWay: '属性值选择方式',
  /** 选择项, 如'test1,test2' */
  options: '选择项',
  /** 属性值类型，判断只能为NUMBER时才可以作为参数选中 */
  valueType: '属性值类型',
  specUnit: '单位',
  valueTypeNumber: '数值',
  valueTypeCharacter: '文本',
  inputWayInput: '输入',
  inputWayOpt: '选择',
  inputWayComponent: '组件选择',
  type: '类型',
  /** 备注 */
  description: '备注',
};

export default zhCN;
