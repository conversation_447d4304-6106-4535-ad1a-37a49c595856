import type { LocaleCode } from '@teammc/react-intl';
import cloneDeep from 'lodash.clonedeep';

import type { BackendSpec, SpecInputWay, SpecValueType } from './backend-spec';
import { getSpecLocales } from './locales';
import type { SpecLocales } from './locales';

export type SpecJSON = {
  id: number;
  description: string;
  deviceType: string;
  operatorId: number;
  /** 更新人 */
  operatorName: string;
  /** 是否必填值 */
  required: boolean;
  /** 规格编码 */
  code: string;
  /** 规格名称 */
  name: string;
  /** 规格单位 */
  unit: string;
  /** 规格值 */
  value: string | null;
  /** 属性类型，判断只能为NUMBER时才可以作为参数选中 */
  type: SpecValueType;
  /** 属性值选择方式 */
  inputWay: SpecInputWay;
  /** 选择项, 如'test1,test2' */
  options: string | null;
  createdAt: string;
  modifiedAt: string;
};

export class Spec {
  static fromApiObject(object: BackendSpec) {
    const copy = cloneDeep(object);

    return new Spec(
      copy.id,
      copy.description,
      copy.deviceType,
      copy.gmtCreate,
      copy.gmtModified,
      copy.operatorId,
      copy.operatorName,
      copy.required,
      copy.specCode,
      copy.specName,
      copy.specUnit,
      copy.specValue,
      copy.valueType,
      copy.inputWay,
      copy.options
    );
  }

  static fromJSON(json: SpecJSON) {
    const copy = cloneDeep(json);

    return new Spec(
      copy.id,
      copy.description,
      copy.deviceType,
      copy.createdAt,
      copy.modifiedAt,
      copy.operatorId,
      copy.operatorName,
      copy.required,
      copy.code,
      copy.name,
      copy.unit,
      copy.value,
      copy.type,
      copy.inputWay,
      copy.options
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: SpecLocales;

  constructor(
    public id: number,
    public description: string,
    public deviceType: string,
    public createdAt: string,
    public modifiedAt: string,
    public operatorId: number,
    public operatorName: string,
    public required: boolean,
    public code: string,
    public name: string,
    public unit: string,
    public value: string | null,
    public type: SpecValueType,
    public inputWay: SpecInputWay,
    public options: string | null
  ) {
    this._locales = getSpecLocales(this._localeCode);
  }

  public set locales(locales: SpecLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendSpec {
    return cloneDeep({
      id: this.id,
      description: this.description,
      deviceType: this.deviceType,
      gmtCreate: this.createdAt,
      gmtModified: this.createdAt,
      operatorId: this.operatorId,
      operatorName: this.operatorName,
      required: this.required,
      specCode: this.code,
      specName: this.name,
      specUnit: this.unit,
      specValue: this.value,
      valueType: this.type,
      inputWay: this.inputWay,
      options: this.options,
    });
  }

  public toJSON(): SpecJSON {
    return cloneDeep({
      id: this.id,
      description: this.description,
      deviceType: this.deviceType,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      operatorId: this.operatorId,
      operatorName: this.operatorName,
      required: this.required,
      code: this.code,
      name: this.name,
      unit: this.unit,
      value: this.value,
      type: this.type,
      inputWay: this.inputWay,
      options: this.options,
    });
  }
}
