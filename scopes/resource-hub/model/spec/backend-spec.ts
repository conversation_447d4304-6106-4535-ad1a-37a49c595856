export type BackendSpec = {
  id: number;
  /** 描述 */
  description: string;
  /** 设备类型 */
  deviceType: string;
  /** 创建时间 */
  gmtCreate: string;
  /** 更新时间 */
  gmtModified: string;
  operatorId: number;
  /** 更新人 */
  operatorName: string;
  /** 是否必填值 */
  required: boolean;
  /** 规格编码 */
  specCode: string;
  /** 规格名称 */
  specName: string;
  /** 规格单位 */
  specUnit: string;
  /** 规格值 */
  specValue: string | null;
  /** 属性类型，判断只能为NUMBER时才可以作为参数选中 */
  valueType: SpecValueType;
  /** 属性值选择方式 */
  inputWay: SpecInputWay;
  /** 选择项, 如'test1,test2' */
  options: string | null;
  // 选择类型「单选或者多选」
  optionType: boolean;
};

/** 属性类型 文本或数值 */
export type SpecValueType = 'NUMBER' | 'CHARACTER';

/** 属性值选择方式 */
export type SpecInputWay = 'INPUT' | 'OPT' | 'COMPONENT';

/** 基础管理 - 资源属性映射 */
export enum ResourceSpecs {
  /** IT设计功率 */
  ItDesign = 'itDesign',
  /** 市电设计功率 */
  EleDesign = 'eleDesign',
  /** PUE设计值 */
  DesignPue = 'designPue',
  /** 制冷设计容量 */
  CoolingDesign = 'coolingDesign',
  /** 柴发设计架构 */
  DieselDesign = 'dieselDesign',
  /** 制冷单元架构 */
  CryogenDesign = 'cryogenDesign',
  /** 安全等级 */
  SafeLevel = 'safeLevel',
  /** 设计标准 */
  DesignNormal = 'designNormal',
  /** 是否集成属地门禁 */
  HasEntrance = 'HAS_ENTRANCE',
}
