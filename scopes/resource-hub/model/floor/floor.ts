import type { LocaleCode } from '@teammc/react-intl';
import cloneDeep from 'lodash.clonedeep';

import type { BackendFloor, FloorType, OperationStatus } from './backend-floor';
import { getFloorLocales } from './locales';
import type { FloorLocales } from './locales';

export type FloorJSON = {
  id: string;
  floorGuid: string;
  floorName: string;
  floorType: FloorType;
  floorTag: string;
  operationStatus: OperationStatus;
  idcTag: string;
  idcName: string;
  blockGuid: string;
};

export class Floor {
  static fromApiObject(object: BackendFloor) {
    const copy = cloneDeep(object);

    return new Floor(
      copy.id,
      copy.guid,
      copy.name,
      copy.floorType,
      copy.tag,
      copy.operationStatus,
      copy.idcTag,
      copy.idcName,
      copy.blockGuid
    );
  }

  static fromJSON(json: FloorJSON) {
    const copy = cloneDeep(json);

    return new Floor(
      copy.id,
      copy.floorGuid,
      copy.floorName,
      copy.floorType,
      copy.floorTag,
      copy.operationStatus,
      copy.idcTag,
      copy.idcName,
      copy.blockGuid
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: FloorLocales;

  constructor(
    public id: string,
    public floorGuid: string,
    public floorName: string,
    public floorType: FloorType,
    public floorTag: string,
    public operationStatus: OperationStatus,
    public idcTag: string,
    public idcName: string,
    public blockGuid: string
  ) {
    this._locales = getFloorLocales(this._localeCode);
  }

  public set locales(locales: FloorLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendFloor {
    return cloneDeep({
      id: this.id,
      guid: this.floorGuid,
      name: this.floorName,
      floorType: this.floorType,
      tag: this.floorTag,
      operationStatus: this.operationStatus,
      idcTag: this.idcTag,
      idcName: this.idcName,
      blockGuid: this.blockGuid,
    });
  }

  public toJSON(): FloorJSON {
    return cloneDeep({
      id: this.id,
      floorGuid: this.floorGuid,
      floorName: this.floorName,
      floorType: this.floorType,
      floorTag: this.floorTag,
      operationStatus: this.operationStatus,
      idcTag: this.idcTag,
      idcName: this.idcName,
      blockGuid: this.blockGuid,
    });
  }
}
