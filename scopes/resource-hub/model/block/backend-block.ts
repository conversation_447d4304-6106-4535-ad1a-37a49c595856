export type BackendType =
  | /* 机房楼*/ 'IDC_BLOCK'
  | /* 办公楼*/ 'OFFICE_BLOCK'
  | /* 110kV变电站*/ 'POWER_STATION';

export type BackendStatus = 'ON' | 'OFF';

/**
 * 冷机运行配置
 * 其中的coolingMode由页面配置DI测点时配置，比如0，1，2；分别对应不同的冷机模式
 * 其中的expressiond代表对应冷机模式的表达式
 * @example
 * ```
 * {"coolingMode": 1, "expression": "ge=8;le=15"}
 * ```
 */
export type CoolingModeConfig = { coolingMode: number; expression: string };

/**
 * 拓展字段
 */
export type ExtendProperties = {
  coolingModeConfigs: CoolingModeConfig[] | null;
  /** IT设计值 */
  itDesign: number;
  /** 市电设计值 */
  eleDesign: number;
  /** pue设计值 */
  designPue: number;
  /** 柴发设计架构 */
  dieselDesign: string;
  /** 制冷单元架构 */
  cryogenDesign: string;
  /** 制冷设计容量 */
  coolingDesign: string;
};

export type BackendBlock = {
  id: number;
  name: string;
  guid: string;
  tag: string;
  blockType: {
    code: BackendType;
    name: string;
  };
  idcTag: string;
  idcName: string;
  /**
   * 所属周期
   */
  period: string;
  /**
   * 状态
   */
  operationStatus: {
    code: BackendStatus;
    name: string;
  };
  /**
   * 建设日期
   *
   * @example
   * ```
   * "2021-12-10"
   * ```
   */
  constructTime: string;
  /**
   * 投产日期
   *
   * @example
   * ```
   * "2022-03-31"
   * ```
   */
  operationTime: string;
  /**
   * 责任人 User ID
   */
  principalId: number;
  /**
   * 责任人姓名
   */
  principalName: string;
  /**
   * 有效打卡范围（米）
   */
  area: number | null;
  /**
   * GPS 坐标经、纬度
   *
   * @example
   * ```
   * "120.83823,31.61076"
   * ```
   */
  coordinate: string | null;
  /**
   * 拓展字段
   */
  blockProperties: ExtendProperties | null;
};
