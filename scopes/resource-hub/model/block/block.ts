import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BackendBlock, BackendStatus, BackendType, ExtendProperties } from './backend-block';
import { getBlockLocales } from './locales';
import type { BlockLocales } from './locales';

type SimpleUser = {
  id: number;
  name: string;
};

export type Coordinates = {
  long: number;
  lat: number;
};

export type BlockJSON = {
  id: number;
  name: string;
  guid: string;
  tag: string;
  type: BackendType;
  idc: string;
  idcName: string;
  constructionCycleText: string;
  status: BackendStatus;
  constructedAt: number;
  putIntoProductionAt: number;
  owner: SimpleUser;
  clockOnOffRadius: number | null;
  coordinates: Coordinates | null;
  blockProperties: ExtendProperties | null;
};

export class Block {
  static fromApiObject(object: BackendBlock) {
    const copy = cloneDeep(object);
    const [long, lat] = (copy.coordinate ?? '0,0').split(',');

    return new Block(
      copy.id,
      copy.name,
      copy.guid,
      copy.tag,
      copy.blockType.code,
      copy.idcTag,
      copy.idcName,
      copy.period,
      copy.operationStatus.code,
      dayjs(copy.constructTime).valueOf(),
      dayjs(copy.operationTime).valueOf(),
      {
        id: copy.principalId,
        name: copy.principalName,
      },
      copy.area,
      copy.coordinate ? { long: Number(long), lat: Number(lat) } : null,
      copy.blockProperties
    );
  }

  static fromJSON(json: BlockJSON) {
    const copy = cloneDeep(json);

    return new Block(
      copy.id,
      copy.name,
      copy.guid,
      copy.tag,
      copy.type,
      copy.idc,
      copy.idcName,
      copy.constructionCycleText,
      copy.status,
      copy.constructedAt,
      copy.putIntoProductionAt,
      copy.owner,
      copy.clockOnOffRadius,
      copy.coordinates,
      copy.blockProperties
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: BlockLocales;

  constructor(
    public id: number,
    public name: string,
    public guid: string,
    public tag: string,
    public type: BackendType,
    public idc: string,
    public idcName: string,
    /** 所属周期 */
    public constructionCycleText: string,
    public status: BackendStatus,
    public constructedAt: number,
    /** 投产日期 */
    public putIntoProductionAt: number,
    public owner: SimpleUser,
    /** 有效打卡半径（米） */
    public clockOnOffRadius: number | null,
    public coordinates: Coordinates | null,
    public blockProperties: ExtendProperties | null
  ) {
    this._locales = getBlockLocales(this._localeCode);
  }

  public set locales(locales: BlockLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public get typeName() {
    return this._locales.type[this.type];
  }

  public get statusName() {
    return this._locales.status[this.status];
  }

  public getFormattedConstructedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.constructedAt).format(template);
  }

  public getFormattedPutIntoProductionAt(template = 'YYYY-MM-DD') {
    return dayjs(this.putIntoProductionAt).format(template);
  }

  public toApiObject(): BackendBlock {
    return cloneDeep({
      id: this.id,
      name: this.name,
      guid: this.guid,
      tag: this.tag,
      blockType: {
        code: this.type,
        name: this.typeName,
      },
      idcTag: this.idc,
      idcName: this.idcName,
      period: this.constructionCycleText,
      operationStatus: {
        code: this.status,
        name: this.statusName,
      },
      constructTime: this.getFormattedConstructedAt(),
      operationTime: this.getFormattedPutIntoProductionAt(),
      principalId: this.owner.id,
      principalName: this.owner.name,
      area: this.clockOnOffRadius,
      coordinate: this.coordinates ? `${this.coordinates.long},${this.coordinates.lat}` : null,
      blockProperties: this.blockProperties,
    });
  }

  public toJSON(): BlockJSON {
    return cloneDeep({
      id: this.id,
      name: this.name,
      guid: this.guid,
      tag: this.tag,
      type: this.type,
      idc: this.idc,
      idcName: this.idcName,
      constructionCycleText: this.constructionCycleText,
      status: this.status,
      constructedAt: this.constructedAt,
      putIntoProductionAt: this.putIntoProductionAt,
      owner: this.owner,
      clockOnOffRadius: this.clockOnOffRadius,
      coordinates: this.coordinates,
      blockProperties: this.blockProperties,
    });
  }
}
