import type { BlockLocales } from './type';

export const zhCN: BlockLocales = {
  type: {
    __self: '类别',
    IDC_BLOCK: '机房楼',
    OFFICE_BLOCK: '办公楼',
    POWER_STATION: '110kV变电站',
  },
  status: {
    __self: '状态',
    ON: '已启用',
    OFF: '未启用',
  },
  idcTag: '所属机房',
  principal: '责任人',
  tag: '楼栋编号',
  period: '所属周期',
  blockType: '类别',
  constructTime: '建设日期',
  operationTime: '投产日期',
  operationStatus: '状态',
  blockPicture: '图片',
  longitude: 'GPS坐标经度',
  latitude: 'GPS坐标纬度',
  clockArea: '有效打卡范围（米）',
  name: '楼栋名称',
};

export default zhCN;
