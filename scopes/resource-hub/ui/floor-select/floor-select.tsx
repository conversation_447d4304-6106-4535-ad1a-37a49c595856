import React, { useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchFloors } from '@manyun/resource-hub.service.fetch-floors';

export type FloorSelectProps = Omit<SelectProps, 'options'> & {
  blockGuidList: string[];
};

export const FloorSelect = React.forwardRef(
  (props: FloorSelectProps, ref: React.Ref<RefSelectProps>) => {
    const [options, setOptions] = useState<SelectProps['options']>([]);

    useDeepCompareEffect(
      function () {
        (async () => {
          const { error, data } = await fetchFloors({
            blockGuidList: props.blockGuidList,
            pageNum: 1,
            pageSize: 50,
          });

          if (error) {
            message.error(error.message);
            return;
          }
          const list = data.data.map(item => {
            return { value: item.id, label: item.floorTag };
          });
          setOptions(list);
        })();
      },
      [props.blockGuidList]
    );

    return <Select {...props} ref={ref} options={options} />;
  }
);

FloorSelect.displayName = 'FloorSelect';
