import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type FloorStatusSelectProps = Omit<SelectProps, 'options'>;

const options: SelectProps['options'] = [
  { value: 'ON', label: '启用' },
  { value: 'OFF', label: '未启用' },
];

export const FloorStatusSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select {...props} ref={ref} options={options} />;
  }
);

FloorStatusSelect.displayName = 'FloorStatusSelect';
