import React from 'react';

import { DeviceTypeCascader } from './device-type-cascader';
import { DeviceTypeTree } from './device-type-tree';

export const BasicDeviceTypeCascader = () => {
  return <DeviceTypeCascader style={{ width: 120 }} allowClear dataType={['space', 'snDevice']} />;
};

export const BasicDeviceTypeTre = () => {
  return (
    <DeviceTypeTree
      style={{ width: 200 }}
      numbered
      dataType={['snDevice']}
      treeStyle={{
        overflowY: 'auto',
        height: 'calc((var(--content-height) - 200px)',
      }}
      inputProps={{
        style: { marginBottom: 8 },
        placeholder: '输入搜索内容',
      }}
      onSelect={(selectedKeys, info) => {
        console.error('>>>selectedKeys', selectedKeys, info);
      }}
    />
  );
};
