import React from 'react';

import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';

import { useDevices } from './use-device';

/* REFACTORME BEGINS */
type ScrollAlign = 'top' | 'bottom' | 'auto';
type ScrollConfig =
  | {
      index: number;
      align?: ScrollAlign;
      offset?: number;
    }
  | {
      key: React.Key;
      align?: ScrollAlign;
      offset?: number;
    };
type ScrollTo = (arg: number | ScrollConfig) => void;
interface BaseSelectRef {
  focus: () => void;
  blur: () => void;
  scrollTo: ScrollTo;
}
/* REFACTORME ENDS */

export type DeviceTypeCascaderProps = {
  dataType: string[];
  numbered?: boolean;
  disabledTypeList?: string[];
  selectable?: string[];
} & TreeSelectProps;

export const DeviceTypeCascader: React.ForwardRefExoticComponent<DeviceTypeCascaderProps> =
  React.forwardRef<BaseSelectRef, DeviceTypeCascaderProps>((props, ref?) => {
    const { dataType, numbered, disabledTypeList, selectable, ...restProps } = props;
    const [tree, getDeviceTypes] = useDevices({
      numbered,
      disabledTypeList,
      dataType,
      selectable,
    });

    React.useEffect(() => {
      getDeviceTypes();
    }, [getDeviceTypes]);

    return (
      <TreeSelect
        // @ts-ignore types
        ref={ref}
        showSearch
        dropdownMatchSelectWidth={false}
        {...restProps}
        onChange={(codes, names, op) => {
          // @ts-ignore
          restProps.onChange?.(codes, names, op, tree);
        }}
        treeNodeFilterProp="title"
        treeData={tree ?? []}
      />
    );
  });

DeviceTypeCascader.displayName = 'DeviceTypeCascader';
