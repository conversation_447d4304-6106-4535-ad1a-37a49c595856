/* eslint-disable @typescript-eslint/no-explicit-any */
import { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useMountedState } from 'react-use';

import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';
import type { DeviceNode } from '@manyun/resource-hub.service.fetch-device-type-tree';
import type { DeviceType } from '@manyun/resource-hub.state.device-types';
import { getDeviceTypesAction } from '@manyun/resource-hub.state.device-types';

export type UseDevicesProps = {
  numbered?: boolean;
  deviceData?: any[];
  disabledTypeList?: string[];
  selectable?: string[];
  dataType?: any[];
  idcTag?: string;
  blockTag?: string;
  /** 设备资源过滤 */
  optionFilter?: (value: DeviceType, index: number, array: DeviceType[]) => boolean;
  roomGuids?: string[];
};
export type DeviceTreeNode = {
  label: string;
  title: string;
  value: string;
  key: string;
  disabled?: boolean;
  children?: DeviceTreeNode[];
  type?: string;
};

/**
 * 处理设备分类树hook
 * dataType=['space'] 返回虚拟类型
 */
export function useDevices({
  numbered,
  disabledTypeList,
  dataType,
  selectable,
  idcTag,
  blockTag,
  optionFilter,
  roomGuids,
}: UseDevicesProps = {}) {
  const dispatch = useDispatch();
  const isMounted = useMountedState();
  const [codes, setCodes] = useState<string[]>([]);
  const [entities, setEntities] = useState<{ [x: string]: Omit<DeviceNode, 'children'> }>({});
  const getDeviceTypes = useCallback(() => {
    dispatch(
      getDeviceTypesAction({
        numbered,
        callback: (_, deviceTypes) => {
          if (isMounted()) {
            setCodes(deviceTypes.codes);
            setEntities(deviceTypes.entities);
          }
        },
        idcTag,
        blockTag,
        roomGuidList: roomGuids,
      })
    );
  }, [dispatch, numbered, idcTag, blockTag, roomGuids, isMounted]);

  const dataList = codes.map(code => entities[code]).filter(item => item !== undefined);

  let filterDataList = [];
  for (const item of dataList) {
    if (dataType) {
      if (item?.metaStyle === 'SPACE' && dataType.includes('space')) {
        filterDataList.push(item);
      } else if (item?.metaStyle === 'DEVICE' && dataType.includes('snDevice') && numbered) {
        filterDataList.push(item);
      } else if (item?.metaStyle === 'DEVICE' && dataType.includes('noSnDevice') && !numbered) {
        filterDataList.push(item);
      }
    } else {
      filterDataList.push(item);
    }
  }

  if (optionFilter) {
    filterDataList = filterDataList.filter(optionFilter);
  }

  const tree = generateTreeData<DeviceTreeNode, DeviceType>(filterDataList, {
    key: ({ metaType, metaCode }) => {
      return `${metaType}${metaCode}`;
    },
    nodeTypes: ['C0', 'C1', 'C2', 'C3'],
    typeKey: 'metaType',
    parentKey: 'parentCode',
    getNode(node, children) {
      const label =
        node.count && node.metaType === 'C2' ? `${node.metaName}(${node.count})` : node.metaName;
      const data = {
        label,
        title: label,
        value: node.metaCode,
        key: node.metaCode,
        type: node.metaType,
        metaStyle: node.metaStyle,
        children: children === null ? undefined : children,
        disabled: disabledTypeList?.includes(node.metaType as any),
        selectable: selectable?.length ? selectable?.includes(node.metaType) : true,
      };
      return data;
    },
    filterNode(node, children) {
      return numbered === undefined || node.numbered === numbered;
    },
  });
  return [tree, getDeviceTypes] as const;
}
