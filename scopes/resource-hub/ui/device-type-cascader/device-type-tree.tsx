import debounce from 'lodash.debounce';
import isNil from 'lodash.isnil';
import type { Key } from 'react';
import React, { useCallback, useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import type { InputProps } from '@manyun/base-ui.ui.input';
import { Input } from '@manyun/base-ui.ui.input';
import type { TreeProps } from '@manyun/base-ui.ui.tree';
import { Tree } from '@manyun/base-ui.ui.tree';
import { Typography } from '@manyun/base-ui.ui.typography';
import { flattenTreeData } from '@manyun/dc-brain.util.flatten-tree-data';

import type { DeviceTreeNode, UseDevicesProps } from './use-device';
import { useDevices } from './use-device';

export type DeviceTypeTreeProps = {
  dataType: string[];
  numbered?: boolean;
  disabledTypeList?: string[];
  showSearch?: boolean;
  treeStyle?: React.CSSProperties;
  inputProps?: Omit<InputProps, 'onChange'>;
  idcTag?: string;
  blockTag?: string;
  optionFilter?: UseDevicesProps['optionFilter'];
  /** 树数据返回 */
  onTreeDataChange?: (flatTree: FlatCustomDeviceNode[], treeData: CustomDeviceTreeNode[]) => void;
  roomGuids?: string[];
} & Omit<TreeProps, 'treeData'>;

export type CustomDeviceTreeNode = Omit<DeviceTreeNode, 'title' | 'children'> & {
  title: string | React.ReactNode;
  children?: CustomDeviceTreeNode[];
};

export type FlatCustomDeviceNode = Omit<DeviceTreeNode, 'title' | 'children'> & {
  title: string | React.ReactNode;
};

/** 设备分类树 */
export const DeviceTypeTree = ({
  dataType,
  numbered,
  disabledTypeList,
  showSearch = true,
  treeStyle,
  inputProps,
  idcTag,
  blockTag,
  optionFilter,
  onTreeDataChange,
  roomGuids,
  ...restProps
}: DeviceTypeTreeProps) => {
  const [tree, getDeviceTypes] = useDevices({
    numbered,
    disabledTypeList,
    dataType,
    idcTag,
    blockTag,
    optionFilter,
    roomGuids,
  });
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);

  const onExpand = (newExpandedKeys: React.Key[]) => {
    setExpandedKeys(newExpandedKeys);
    setAutoExpandParent(false);
  };

  const getParentKey = useCallback(
    (nodeKey: string, treeData: DeviceTreeNode[]): React.Key | undefined => {
      let parentKey;
      for (let i = 0; i < treeData.length; i++) {
        const node = treeData[i];
        if (node.children) {
          if (node.children.some(item => item.value === nodeKey)) {
            parentKey = node.value;
          } else if (getParentKey(nodeKey, node.children)) {
            parentKey = getParentKey(nodeKey, node.children);
          }
        }
      }
      return parentKey;
    },
    []
  );

  // 处理树形数据，为C1节点添加自定义标题
  const processTreeData = (data: DeviceTreeNode[]): DeviceTreeNode[] => {
    return data.map(node => {
      const C1ChildrenCount = node.children?.length;
      const title = `${node.title}${node.type === 'C1' && C1ChildrenCount && C1ChildrenCount > 0 ? `(${C1ChildrenCount})` : ''}`;

      return {
        ...node,
        title,
        children: node.children ? processTreeData(node.children) : undefined,
      };
    });
  };

  const treeData = useDeepCompareMemo(() => {
    if (!searchValue) {
      return processTreeData(tree);
    }
    const loop = (data: DeviceTreeNode[]): CustomDeviceTreeNode[] =>
      data.map(item => {
        const C1ChildrenCount = item.children?.length;
        const strTitle =
          item.type === 'C1' && C1ChildrenCount ? `${item.label}(${C1ChildrenCount})` : item.label; // 处理二级分类显示子级数据
        const index = strTitle.indexOf(searchValue);
        const beforeStr = strTitle.substring(0, index);
        const afterStr = strTitle.slice(index + searchValue.length);
        const title =
          index > -1 ? (
            <Typography.Text>
              {beforeStr}
              <Typography.Text type="danger">{searchValue}</Typography.Text>
              {afterStr}
            </Typography.Text>
          ) : (
            strTitle
          );
        if (item.children) {
          return {
            ...item,
            title,
            children: loop(item.children),
          };
        }

        return {
          ...item,
          title,
        };
      });

    return loop(tree);
  }, [searchValue, tree]);

  const flattenTree = useDeepCompareMemo(() => {
    if (treeData) {
      return flattenTreeData(treeData);
    }
    return [];
  }, [treeData]);

  useDeepCompareEffect(() => {
    if (onTreeDataChange) {
      onTreeDataChange(flattenTree, treeData);
    }
  }, [flattenTree, onTreeDataChange, treeData]);

  const _onChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const searchValue = e.target.value.trim();
      setSearchValue(searchValue);
      if (!searchValue) {
        setExpandedKeys([]);
        setAutoExpandParent(false);
        return;
      }
      const flattenTree = flattenTreeData(tree);

      const newExpandedKeys = flattenTree
        .map(flatItem => {
          if (flatItem.title && flatItem.title.toString().includes(searchValue)) {
            const key = getParentKey(flatItem.value, tree);
            if (flatItem.type === 'C2' && key) {
              return [getParentKey(key as string, tree), key];
            }
            return key;
          }
          return null;
        })
        .flat()
        .filter((item, i, self): item is Key => self.indexOf(item) === i && !isNil(item));
      if (newExpandedKeys.length) {
        setExpandedKeys(newExpandedKeys);
        setAutoExpandParent(true);
      }
    },
    [getParentKey, tree]
  );

  const onChange = debounce(_onChange, 300);

  React.useEffect(() => {
    getDeviceTypes();
  }, [getDeviceTypes]);

  return (
    <>
      {showSearch && <Input.Search {...inputProps} onChange={onChange} />}
      <div style={treeStyle}>
        <Tree
          autoExpandParent={autoExpandParent}
          expandedKeys={expandedKeys}
          onExpand={onExpand}
          {...restProps}
          treeData={treeData}
        />
      </div>
    </>
  );
};
