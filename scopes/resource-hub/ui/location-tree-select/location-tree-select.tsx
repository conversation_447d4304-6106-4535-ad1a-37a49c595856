import type { LabeledValue } from 'antd/es/tree-select';
import classNames from 'classnames';
import type { BaseSelectRef } from 'rc-select/es/BaseSelect';
import type { ChangeEventExtra } from 'rc-tree-select/es/TreeSelect';
import React from 'react';
import { useDispatch } from 'react-redux';
import { useLatest } from 'react-use';

import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';

import styles from './location-tree-select.module.less';
import type { NodeType, SpaceTreeNode } from './use-spaces';
import { useSpaces } from './use-spaces';

export type ValueType = string | string[] | LabeledValue | LabeledValue[];

export type EnhancedChangeEventExtra = ChangeEventExtra & {
  /**
   * 当前选中项对应的数据源
   */
  dataRef: SpaceTreeNode | SpaceTreeNode[] | undefined;
};

export type LocationTreeSelectProps = {
  /**
   * 节点是否可选
   */
  selectabledTypes?: NodeType[];
  /**
   * 按空间类型禁用节点
   */
  disabledTypes?: NodeType[];
  /**
   * 只展示当前用户有权限的空间数据
   */
  authorizedOnly?: boolean;
  /**
   * 控制显示的节点类型
   * 非多选模式下不支持包间类型层级
   */
  nodeTypes?: NodeType[];
  /**
   * 展示某个空间节点下的数据
   */
  block?: string;
  /**
   * 指定某个机房后，仅显示这个机房下的楼[包间]数据
   */
  idc?: string;
  /**
   * 是否需要虚拟楼
   * @defaultValue false
   */
  includeVirtualBlocks?: boolean;
  /**
   * @deprecated Use `onTreeDataChange` instead
   */
  onTreeDataDidUpdate?: (treeData?: SpaceTreeNode[]) => void;
  onTreeDataChange?(treeData: SpaceTreeNode[]): void;
  onChange?: <V extends ValueType = ValueType>(
    value: V,
    labelList: React.ReactNode[],
    extra: EnhancedChangeEventExtra
  ) => void;
} & Omit<TreeSelectProps<ValueType, SpaceTreeNode>, 'onChange'>;

const noop = () => {};

/**
 * use locationCascader:  import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
 *
 */
export const LocationTreeSelect = React.forwardRef<BaseSelectRef, LocationTreeSelectProps>(
  (props, ref?) => {
    const {
      disabledTypes,
      selectabledTypes,
      authorizedOnly = false,
      nodeTypes = ['IDC', 'BLOCK'],
      idc,
      block,
      includeVirtualBlocks = false,
      onFocus,
      onTreeDataDidUpdate = noop,
      onTreeDataChange = onTreeDataDidUpdate,
      onChange,
      multiple,
      labelInValue,
      ...restProps
    } = props;
    const dispatch = useDispatch();

    const [{ entities, treeSpaces }] = useSpaces({
      authorizedOnly,
      nodeTypes: multiple ? nodeTypes : nodeTypes.filter(type => type !== 'ROOM-TYPE'),
      disabledTypes,
      idc,
      block,
      includeVirtualBlocks,
      selectabledTypes,
    });

    const getDataRef = React.useCallback(
      (value: ValueType) => {
        const _getDataRef = (value: ValueType): SpaceTreeNode | SpaceTreeNode[] | undefined => {
          if (value === null || typeof value == 'string') {
            return entities[value];
          } else if (typeof value == 'object' && !Array.isArray(value)) {
            return entities[(value as LabeledValue).value];
          } else if (Array.isArray(value)) {
            return value.map<SpaceTreeNode>(
              (item: ValueType) => _getDataRef(item) as SpaceTreeNode
            );
          }
          return undefined;
        };

        return _getDataRef(value);
      },
      [entities]
    );

    const onTreeDataChangeRef = useLatest(onTreeDataChange);
    React.useEffect(() => {
      onTreeDataChangeRef.current(treeSpaces);
    }, [onTreeDataChangeRef, treeSpaces]);

    React.useEffect(() => {
      dispatch(
        syncCommonDataAction({
          strategy: {
            space: 'FORCED',
            citiesTree: 'FORCED',
          },
        })
      );
    }, [dispatch]);

    const handleChange = React.useMemo(() => {
      const func: TreeSelectProps<ValueType, SpaceTreeNode>['onChange'] = (
        value,
        labelList,
        extra
      ) => {
        if (multiple) {
          const values = value;
          // 获取其中的包间类型 values
          const roomTypeValues = labelInValue
            ? (values as { value: string }[])
                .map(item => item.value)
                .filter(value => entities[value].type === 'ROOM-TYPE')
            : (values as string[]).filter(value => entities[value].type === 'ROOM-TYPE');
          const roomTypeNodes = roomTypeValues.map(value => entities[value]);
          /** 包间类型对应的包间数组聚合起来的数组 */
          const roomValues = roomTypeNodes.reduce(
            (acc, roomTypeNode) => [
              ...acc,
              ...(roomTypeNode.children ? roomTypeNode.children.map(node => node.value) : []),
            ],
            [] as string[]
          );
          // 因包间和包间类型可以单独选择，故还要去除重复的包间数据，同时也要去除包间类型数据
          if (labelInValue) {
            const typedValues = values as { value: string; label: React.ReactNode }[];
            const finalValues = [
              ...roomValues
                .filter(roomValue => typedValues.every(value => value.value !== roomValue))
                .map(roomValue => ({
                  value: roomValue,
                  label: entities[roomValue].label,
                })),
              ...typedValues.filter(value => !roomTypeValues.includes(value.value)),
            ];
            onChange?.(
              finalValues,
              finalValues.map(value => value.label),
              {
                ...extra,
                dataRef: getDataRef(value),
              }
            );
          } else {
            const typedValues = values as string[];
            const finalValues = [
              ...roomValues.filter(roomValue => !typedValues.includes(roomValue)),
              ...typedValues.filter(value => !roomTypeValues.includes(value)),
            ];
            onChange?.(
              finalValues,
              finalValues.map(value => entities[value].label),
              {
                ...extra,
                dataRef: getDataRef(value),
              }
            );
          }
        } else {
          onChange?.(value, labelList, {
            ...extra,
            dataRef: getDataRef(value),
          });
        }
      };
      return func;
    }, [multiple, labelInValue, entities, onChange, getDataRef]);

    return (
      <TreeSelect<ValueType, SpaceTreeNode>
        ref={ref}
        dropdownClassName={classNames(
          restProps.dropdownClassName,
          nodeTypes.toString() === ['IDC'].toString() && styles.locationIDCSelect
        )}
        virtual={false}
        treeNodeLabelProp="textLabel" // useSpaces新增展示字段
        multiple={multiple}
        labelInValue={labelInValue}
        {...restProps}
        treeData={treeSpaces}
        onChange={handleChange}
      />
    );
  }
);

LocationTreeSelect.displayName = 'LocationTreeSelect';
