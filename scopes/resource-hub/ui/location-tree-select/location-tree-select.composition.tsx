import React, { useEffect, useState } from 'react';

import { StoreProvider } from '@manyun/base-ui-web.context.store-context';
import { ConfigProvider } from '@manyun/base-ui.context.config';

import { destroyMock, webRequest } from '@manyun/service.request';

import { LocationTreeSelect } from './location-tree-select';
import { commonStore } from './location-tree-select.mock';

export const BasicIdcSelectForAuthorized = () => (
  <StoreProvider store={commonStore}>
    <ConfigProvider>
      <LocationTreeSelect
        authorizedOnly
        style={{ width: 300 }}
        multiple
        nodeTypes={['IDC', 'BLOCK', 'ROOM']}
        disabledTypes={['IDC']}
      />
    </ConfigProvider>
  </StoreProvider>
);

export const BasicIdcSelect = () => {
  const [initialize, update] = useState(false);

  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialize) {
    return <>Initializing...</>;
  }

  return (
    <StoreProvider store={commonStore}>
      <ConfigProvider>
        <LocationTreeSelect
          style={{ width: 300 }}
          nodeTypes={['IDC', 'BLOCK', 'ROOM']}
          showSearch
          disabledTypes={['IDC']}
        />
      </ConfigProvider>
    </StoreProvider>
  );
};
