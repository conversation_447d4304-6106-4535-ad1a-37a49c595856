import React from 'react';
import { useSelector } from 'react-redux';
import { useLatest } from 'react-use';

import { selectMyResourceCodes, selectMyResources } from '@manyun/auth-hub.state.user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';
import { getCitiesTree } from '@manyun/dc-brain.state.common';
import type { MetaType } from '@manyun/resource-hub.model.metadata';
import type { BaseSpace, Space, SpaceType } from '@manyun/resource-hub.service.fetch-spaces';
import { selectSpaceEntities } from '@manyun/resource-hub.state.space';

export type NodeType = SpaceType | 'REGION' | 'ROOM-TYPE';

export type RoomTypeSpace = Omit<BaseSpace, 'metaStyle'> & {
  type: 'ROOM-TYPE';
};

export type RegionSpace = Omit<BaseSpace, 'metaStyle'> & {
  type: 'REGION';
};

export type SourceData = Space | RoomTypeSpace | RegionSpace;

export type SpaceTreeNode = {
  type: NodeType;
  label: string;
  title: string;
  value: string;
  children?: SpaceTreeNode[];
  disabled?: boolean;
};

export type UseSpacesProps = {
  nodeTypes: NodeType[];
  idc?: string;
  block?: string;
  includeVirtualBlocks?: boolean;
  authorizedOnly?: boolean;
  disabledTypes?: NodeType[];
  selectabledTypes?: NodeType[];
  nodeMutator?: (node: SpaceTreeNode) => SpaceTreeNode;
};

/**
 * @deprecated use new graphql hook: import { useSpaces } from '@manyun/resource-hub.gql.client.spaces';
 */
export function useSpaces({
  nodeTypes,
  selectabledTypes,
  idc,
  block,
  authorizedOnly,
  disabledTypes,
  includeVirtualBlocks,
  nodeMutator,
}: UseSpacesProps) {
  const myResources = useSelector(selectMyResources);
  const myResourceCodes = useSelector(selectMyResourceCodes);
  const allResources = useSelector(selectSpaceEntities());

  const _nodeTypes = useDeepCompareMemo(() => {
    const __nodeTypes: NodeType[] = nodeTypes;
    if (__nodeTypes.includes('ROOM-TYPE')) {
      __nodeTypes.push('ROOM');
    }
    return Array.from(new Set(__nodeTypes));
  }, [idc, nodeTypes]);

  const cities = useSelector(getCitiesTree);

  const resources = React.useMemo(() => {
    let resources: SourceData[] = authorizedOnly ? myResources : allResources;

    const nodeTypesList = _nodeTypes;

    if (nodeTypesList.toString() === ['IDC'].toString()) {
      if (authorizedOnly) {
        // 如果仅支持选机房，且开启了数据权限控制，则仅可选择关联了园区的机房
        resources = resources.filter(
          resource =>
            resource?.type === 'IDC' &&
            myResourceCodes.includes(`${resource.code}.${resource.code}`)
        );
      } else {
        resources = resources.filter(resource => resource?.type === 'IDC');
      }
    }

    if (nodeTypesList.includes('REGION')) {
      const regions = cities.reduce(
        (acc, city) => [
          ...acc,
          ...(city.children ?? []).map(
            (item: { value: string; label: string }) =>
              ({
                code: item.value,
                name: item.label,
                type: 'REGION',
                parentCode: city.value,
              }) as RegionSpace
          ),
        ],
        [] as RegionSpace[]
      );
      resources = [...regions, ...resources];
    }

    if (!nodeTypesList.includes('ROOM')) {
      resources = resources.filter(resource =>
        ['REGION', 'IDC', 'BLOCK'].includes(resource?.type as MetaType)
      );
    }

    /**不返回虚拟楼 */
    if (includeVirtualBlocks === false) {
      resources = resources.filter(resource => {
        const codes = resource.code.split('.');
        if (resource.type === 'BLOCK' && codes[1] === codes[0]) {
          return false;
        }
        return true;
      });
    }

    // 指定了包间类型，则由前端人为增加一层
    if (nodeTypesList.includes('ROOM-TYPE')) {
      const roomTypes: RoomTypeSpace[] = [];
      /** key 和 value 均为 roomType 的 code */
      const roomTypeCodeMap = new Map<string, string>();
      resources = resources.map(resource => {
        if (resource.type !== 'ROOM') {
          return resource;
        }
        const roomTypeCode = `${resource.parentCode}.${resource.roomType}`;
        if (!roomTypeCodeMap.has(roomTypeCode)) {
          roomTypeCodeMap.set(roomTypeCode, roomTypeCode);
          roomTypes.push({
            type: 'ROOM-TYPE',
            code: roomTypeCode,
            name: resource.roomTypeName || '未知',
            parentCode: resource.parentCode,
          });
        }
        return { ...resource, parentCode: roomTypeCode };
      });
      resources = resources.concat(roomTypes);
    }

    resources = resources.filter(resource => {
      if (resource.type === 'IDC' && idc) {
        return resource.code === idc;
      }
      if (resource.type === 'BLOCK' && idc) {
        if (block) {
          return resource.code === `${idc}.${block}`;
        }
        return resource.parentCode === idc;
      }
      if ((resource.type === 'ROOM-TYPE' || resource.type === 'ROOM') && idc && block) {
        return resource.parentCode === `${idc}.${block}`;
      }
      return true;
    });

    return resources;
  }, [
    authorizedOnly,
    myResources,
    allResources,
    _nodeTypes,
    includeVirtualBlocks,
    myResourceCodes,
    cities,
    block,
    idc,
  ]);

  const nodeMutatorRef = useLatest(nodeMutator);
  const { entities, treeSpaces } = React.useMemo(() => {
    const entities: Record<string, SpaceTreeNode> = {};
    const treeSpaces = generateTreeData<SpaceTreeNode, SourceData>(resources, {
      key: 'code',
      typeKey: 'type',
      parentKey: 'parentCode',
      nodeTypes: _nodeTypes,
      getNode: (data, children) => {
        const metaCodes = data.code.split('.');
        const isIdcDotIdc = metaCodes.length === 2 && metaCodes[0] === metaCodes[1];
        // 如果是园区的话，就展示园区
        const nodeLabel = (() => {
          switch (data.type) {
            case 'IDC':
              return `${data.code} ${data.name}`;
            case 'BLOCK':
              return `${data.name} 栋`;
            case 'ROOM':
              return `${metaCodes[2]} ${data.name}`;
            default:
              return data.name;
          }
        })();
        const label = isIdcDotIdc ? `${metaCodes[0]}.${data.name}` : nodeLabel;
        const type = data.type;
        const node: SpaceTreeNode & {
          textLabel?: string;
          selectable?: boolean;
        } = {
          type,
          label,
          title: label,
          value: data.code,
          children: children === null ? undefined : children,
          disabled: !!disabledTypes?.includes(type),
          textLabel:
            data.type === 'IDC'
              ? `${data.code} ${data.name}`
              : data.type === 'ROOM-TYPE'
                ? `${data.parentCode}.${data.name}`
                : data.type === 'REGION'
                  ? data.name
                  : data.code,
        };
        if (selectabledTypes) {
          node.selectable = !!selectabledTypes?.includes(type);
        }
        const _node =
          typeof nodeMutatorRef.current == 'function' ? nodeMutatorRef.current(node) : node;
        entities[_node.value] = _node;
        return _node;
      },
    });

    return { entities, treeSpaces };
  }, [_nodeTypes, disabledTypes, nodeMutatorRef, resources, selectabledTypes]);

  return [
    { /** @deprecated Use `entities` instead */ paralleSpaces: resources, entities, treeSpaces },
  ] as const;
}
