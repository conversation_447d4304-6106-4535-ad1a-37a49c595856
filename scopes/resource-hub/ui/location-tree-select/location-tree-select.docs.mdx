---
description: 'A LocationTreeSelect component.'
labels: ['react', 'ui', 'space']
---

## 概述

提供\[当前登录用户有权限的\]空间位置（机房、楼、包间）

## 使用

```jsx
<LocationTreeSelect
  style={{ width: 300 }}
  multiple
  authorizedOnly
  disabledTypes={['IDC']}
  nodeTypes={['IDC', 'BLOCK', 'ROOM']}
/>
```

### 使用选中项原始数据源

```jsx
<LocationTreeSelect
  nodeTypes={['IDC', 'BLOCK']}
  onChange={(value, labelList, { dataRef }) => {
    console.log('dataRef: ', dataRef);
  }}
/>
```

![data reference](./assets/data-ref.png)
