import { configureStore, createSlice } from '@reduxjs/toolkit';

export const userSlice = createSlice({
  name: 'user',
  initialState: {
    resourceCodes: [
      'EC01',
      'EC02',
      'EC03',
      'EC04',
      'EC06',
      'EC09',
      'sxdtyg',
      'sxdtyg.1',
      'sxdtyg.10',
      'sxdtyg.11',
      'sxdtyg.12',
      'sxdtyg.2',
      'sxdtyg.3',
      'sxdtyg.4',
      'sxdtyg.5',
      'sxdtyg.6',
      'sxdtyg.7',
      'sxdtyg.8',
      'sxdtyg.9',
      'EC03.A',
      'EC01.A',
      'EC06.A',
      'EC06.EC06',
      'EC09.EC09',
      'EC01.M',
      'sxdtyg.sxdtyg',
    ],
  },
  reducers: {},
});

export const spacesSlice = createSlice({
  name: 'resource.spaces',
  initialState: {
    entities: {
      EC01: {
        id: null,
        type: 'IDC',
        code: 'EC01',
        name: 'EC01',
        parentCode: 'CC',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      EC02: {
        id: null,
        type: 'IDC',
        code: 'EC02',
        name: '1',
        parentCode: 'NE',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      EC03: {
        id: null,
        type: 'IDC',
        code: 'EC03',
        name: 'EC03',
        parentCode: 'NC',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      EC04: {
        id: null,
        type: 'IDC',
        code: 'EC04',
        name: 'EC04',
        parentCode: 'NW',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      EC06: {
        id: null,
        type: 'IDC',
        code: 'EC06',
        name: '普洛斯常熟东南数据中心',
        parentCode: 'EC',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      EC09: {
        id: null,
        type: 'IDC',
        code: 'EC09',
        name: 'EC09',
        parentCode: 'NC',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      sxdtyg: {
        id: null,
        type: 'IDC',
        code: 'sxdtyg',
        name: '中联绿色大数据产业基地',
        parentCode: 'NC',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.1': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.1',
        name: '1',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.10': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.10',
        name: '10',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.11': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.11',
        name: '11',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.12': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.12',
        name: '12',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.2': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.2',
        name: '2',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.3': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.3',
        name: '3',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.4': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.4',
        name: '4',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.5': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.5',
        name: '5',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.6': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.6',
        name: '6',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.7': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.7',
        name: '7',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.8': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.8',
        name: '8',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.9': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.9',
        name: '9',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'EC03.A': {
        id: null,
        type: 'BLOCK',
        code: 'EC03.A',
        name: 'A',
        parentCode: 'EC03',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'EC01.A': {
        id: null,
        type: 'BLOCK',
        code: 'EC01.A',
        name: 'A',
        parentCode: 'EC01',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'EC06.A': {
        id: null,
        type: 'BLOCK',
        code: 'EC06.A',
        name: 'A',
        parentCode: 'EC06',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'EC06.EC06': {
        id: null,
        type: 'BLOCK',
        code: 'EC06.EC06',
        name: 'EC06',
        parentCode: 'EC06',
        numbered: null,
        metaStyle: 'VIRTUAL_BLOCK',
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'EC09.EC09': {
        id: null,
        type: 'BLOCK',
        code: 'EC09.EC09',
        name: 'EC09',
        parentCode: 'EC09',
        numbered: null,
        metaStyle: 'VIRTUAL_BLOCK',
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'EC01.M': {
        id: null,
        type: 'BLOCK',
        code: 'EC01.M',
        name: 'M',
        parentCode: 'EC01',
        numbered: null,
        metaStyle: null,
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
      'sxdtyg.sxdtyg': {
        id: null,
        type: 'BLOCK',
        code: 'sxdtyg.sxdtyg',
        name: 'sxdtyg',
        parentCode: 'sxdtyg',
        numbered: null,
        metaStyle: 'VIRTUAL_BLOCK',
        createAt: null,
        updateAt: null,
        user: {
          id: null,
          name: null,
        },
        isDeleted: null,
      },
    },
    codes: [
      'EC01',
      'EC02',
      'EC03',
      'EC04',
      'EC06',
      'EC09',
      'sxdtyg',
      'sxdtyg.1',
      'sxdtyg.10',
      'sxdtyg.11',
      'sxdtyg.12',
      'sxdtyg.2',
      'sxdtyg.3',
      'sxdtyg.4',
      'sxdtyg.5',
      'sxdtyg.6',
      'sxdtyg.7',
      'sxdtyg.8',
      'sxdtyg.9',
      'EC03.A',
      'EC01.A',
      'EC06.A',
      'EC06.EC06',
      'EC09.EC09',
      'EC01.M',
      'sxdtyg.sxdtyg',
    ],
  },
  reducers: {},
});

export const commonStore: any = configureStore({
  reducer: {
    user: userSlice.reducer,
    spaces: spacesSlice.reducer,
  },
});
