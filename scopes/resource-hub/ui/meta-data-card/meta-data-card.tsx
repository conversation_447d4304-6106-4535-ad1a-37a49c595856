import CheckOutlined from '@ant-design/icons/es/icons/CheckOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Card } from '@manyun/base-ui.ui.card';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';
import { deleteMetaDataWeb } from '@manyun/resource-hub.service.delete-meta-data';
import { MetaDataItem } from '@manyun/resource-hub.ui.meta-data-item';

export type MetaDataCardProps = {
  list: MetaSelectData[];
  placeholder: string;
  onAdd: (text: string) => boolean | void | Promise<unknown>;
  deleteCategory: (id: number, callback: (result?: boolean) => void) => void;
  getList: () => void;
  maxLength: number;
  metaType?: MetaType;
  editable?: boolean;
  onClick?: (item?: MetaSelectData) => void;
  onEdit?: (
    id: number,
    name: string,
    isDuplicated: boolean,
    callback: (result: boolean) => void
  ) => void;
  cursor?: string;
  showAddInput?: boolean;
  bordered?: boolean;
  parentCode?: string | null;
  inputStyle?: React.CSSProperties;
  cardStyle?: React.CSSProperties;
  editText?: string;
  deleteText?: string;
  selectComponentRender?: (metaId: number) => React.ReactNode;
};

export const MetaDataCard = ({
  list,
  placeholder,
  onClick,
  onAdd,
  onEdit,
  cursor,
  parentCode,
  bordered,
  getList,
  deleteCategory,
  metaType,
  maxLength = 20,
  inputStyle,
  cardStyle,
  selectComponentRender,
}: MetaDataCardProps) => {
  const dispatch = useDispatch();
  const [itemList, setItemList] = useState<MetaSelectData[]>([]);
  const [inputText, setInputText] = useState<string>('');
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [, { checkCode }] = useAuthorized();
  const showEditIcon = checkCode('element_meta_data_update');
  const showCreateIcon = checkCode('element_meta_data_create');
  const showDeleteIcon = checkCode('element_meta_data_delete');
  const [mutateLoading, setMutateLoading] = useState(false);

  const updateSyncCommonData = (metaType?: MetaType) => {
    if (!metaType) {
      return;
    }
    if (metaType === MetaType.ROOM_TYPE) {
      dispatch(syncCommonDataAction({ strategy: { roomTypes: 'FORCED' } }));
    }
    if (metaType === MetaType.CHANGE) {
      dispatch(syncCommonDataAction({ strategy: { changeTypes: 'FORCED' } }));
    }

    if (
      [
        MetaType.DEVICE_GENERAL,
        MetaType.VISITOR,
        MetaType.IT_SERVICE,
        MetaType.ITORDER_THIRD_CATEGORY,
      ].includes(metaType)
    ) {
      dispatch(syncCommonDataAction({ strategy: { ticketTypes: 'FORCED' } }));
    }

    if (
      metaType === MetaType.ACCESS_CARD_SECOND_CATEGORY ||
      metaType === MetaType.ACCESS_CARD_TOP_CATEGORY
    ) {
      dispatch(syncCommonDataAction({ strategy: { accessCardTypes: 'FORCED' } }));
    }
    if (metaType === MetaType.EVENT_TOP_CATEGORY || metaType === MetaType.EVENT_SECOND_CATEGORY) {
      dispatch(syncCommonDataAction({ strategy: { eventTypes: 'FORCED' } }));
    }
  };
  const onAddNode = async () => {
    if (inputText) {
      if (typeof onAdd === 'function') {
        setMutateLoading(true);
        const data = await onAdd(inputText);
        setMutateLoading(false);
        if (data) {
          setInputText('');
          updateSyncCommonData(metaType);
        }
      }
    }
  };

  const onEditNode = (id: number, newMetaName: string, callback: (result: boolean) => void) => {
    if (typeof onEdit === 'function') {
      onEdit(id, newMetaName, isDuplicated(list, id, newMetaName), callback);

      return;
    }
  };

  const onDelete = async (id: number, callback: (result?: boolean) => void) => {
    if (typeof deleteCategory === 'function') {
      deleteCategory(id, callback);
      return;
    }
    if (metaType) {
      const { error } = await deleteMetaDataWeb({ id, metaType: metaType });

      if (error) {
        message.error(error.message);
        return;
      }
      message.success('删除成功！');
      getList();
      updateSyncCommonData(metaType);
    }
  };

  useEffect(() => {
    setItemList(list);
  }, [list]);
  return (
    <Card
      style={{ height: 288, width: 198, ...cardStyle }}
      bodyStyle={{ height: 240, overflow: 'hidden', overflowY: 'auto' }}
      bordered={bordered}
    >
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        {itemList?.map(item => (
          <div key={item.id}>
            <MetaDataItem
              metaItem={item}
              metaType={metaType}
              cursor={cursor}
              editable={typeof item?.editable === 'boolean' ? item.editable : showEditIcon}
              deletable={showDeleteIcon}
              parentCode={parentCode}
              maxLength={maxLength}
              isEditing={isEditing}
              selectComponentRender={selectComponentRender}
              setIsEditing={setIsEditing}
              onEdit={onEditNode}
              onClick={onClick}
              onDelete={onDelete}
            />
          </div>
        ))}
      </Space>
      {showCreateIcon && (
        <Input
          style={{ width: '85%', bottom: 8, position: 'absolute', left: '7.5%', ...inputStyle }}
          placeholder={placeholder}
          value={inputText}
          maxLength={maxLength}
          prefix={<PlusCircleOutlined style={{ color: `var(--${prefixCls}-success-color)` }} />}
          suffix={
            <Spin spinning={mutateLoading}>
              <CheckOutlined onClick={onAddNode} />
            </Spin>
          }
          onChange={e => setInputText(e.target.value.trim())}
        />
      )}
    </Card>
  );
};

export function isDuplicated(data: MetaSelectData[], id: number, metaName: string) {
  return (
    data?.filter(item => (item.label === metaName || item.name === metaName) && item.id !== id)
      .length > 0
  );
}
