import React from 'react';
import { Link } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock as mock } from '@manyun/service.request';

import { CorePointsCard, DeviceCorePointsCard } from './core-points-card';

export const BasicCorePointsCard = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const mockOff = mock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <CorePointsCard deviceType="10101" deviceGuid="CRAH2" />
      </FakeStore>
    </ConfigProvider>
  );
};

export const BasicDeviceCorePointsCard = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const mockOff = mock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <DeviceCorePointsCard
          deviceType="10101"
          deviceGuid="S1N1010108"
          extra={<Link to="">更多数据</Link>}
          style={{ width: 664 }}
        />
      </FakeStore>
    </ConfigProvider>
  );
};
