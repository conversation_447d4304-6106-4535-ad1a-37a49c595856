import dayjs from 'dayjs';
import get from 'lodash.get';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import type { LineProps } from '@manyun/base-ui.chart.line';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import type { CardProps } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import type { Alarm } from '@manyun/monitoring.model.alarm';
import type { Point } from '@manyun/monitoring.model.point';
import { generateRoomMonitoringUrl } from '@manyun/monitoring.route.monitoring-routes';
import { fetchAlarms } from '@manyun/monitoring.service.fetch-alarms';
import {
  SUBSCRIPTIONS_MODE,
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribe,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribe,
} from '@manyun/monitoring.state.subscriptions';
import { AddDeviceToDiffTool } from '@manyun/monitoring.ui.add-device-to-diff-tool';
import { AlarmLevelText } from '@manyun/monitoring.ui.alarm-level-text';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { usePoints } from '@manyun/resource-hub.hook.use-points';
import type { Device } from '@manyun/resource-hub.model.device';
import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';
import type { DeviceRecordItLogTabsKeys } from '@manyun/resource-hub.route.resource-routes';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';
import { fetchRoom } from '@manyun/resource-hub.service.fetch-room';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';
import {
  CountTypeMap,
  fetchTicketStatisticByDevice,
} from '@manyun/ticket.service.fetch-ticket-statistic-by-device';
import type {
  CountStatistic,
  CountType,
} from '@manyun/ticket.service.fetch-ticket-statistic-by-device';

import styles from './core-points-card.module.less';

export type CorePointsCardProps = {
  style?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
  deviceType: string;
  deviceGuid: string;
  bordered?: boolean;
  showPointsOnly?: boolean;
  showTool?: boolean;
  showRelatedRoom?: boolean;
  hideNoDataItems?: boolean;
} & Pick<LineProps, 'variant'>;

const mode = SUBSCRIPTIONS_MODE.REALTIME_N_ALARMS;
const moduleId = 'core-points-card';

export const CorePointsCard = ({
  style,
  bodyStyle,
  deviceType,
  deviceGuid,
  bordered = true,
  showPointsOnly = false,
  showTool = true,
  showRelatedRoom = false,
  variant = 'normal',
  hideNoDataItems,
}: CorePointsCardProps) => {
  const [relatedRooms, setRelatedRooms] = useState<[string, string][]>([]);
  const [deviceData, setDeviceData] = useState<Device | null>(null);
  const [alarms, setAlarms] = useState<Alarm[]>([]);
  const ticketCounts = useRef<CountStatistic>({
    EVENT: 0,
    CHANGE: 0,
    MAINTENANCE: 0,
    INSPECTION: 0,
    REPAIR: 0,
  });
  const [showTickets, setShowTickets] = React.useState(false);
  const blockGuid = deviceData === null ? null : deviceData.spaceGuid.blockGuid;
  const spaceGuid = deviceData === null ? null : deviceData.spaceGuid.roomGuid;
  const idc = deviceData === null ? null : deviceData.spaceGuid.idcTag;
  const block = deviceData === null ? null : deviceData.spaceGuid.blockTag;
  const room = deviceData === null ? null : deviceData.spaceGuid.roomTag;
  const roomName = deviceData?.roomName;

  const fetchRoomDetail = useCallback(async () => {
    if (!spaceGuid) {
      return;
    }
    const { error, data } = await fetchRoom({ roomGuid: spaceGuid });
    if (error) {
      message.error(error.message);
      return;
    }
    setRelatedRooms(Object.entries(data?.relateRooms ?? {}));
  }, [spaceGuid]);

  useEffect(() => {
    fetchDeviceByGuid({ guid: deviceGuid }).then(({ data, error }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setDeviceData(data);
    });
  }, [deviceGuid]);

  useEffect(() => {
    if (!idc || !block || !room || showPointsOnly) {
      return;
    }
    (async () => {
      const { error, data } = await fetchAlarms({
        idcTag: idc,
        blockTags: [block],
        roomTags: [room],
        deviceGuid,
        pageNum: 1,
        pageSize: 10,
        status: null,
        triggerStatus: ['TRIGGER'],
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setAlarms(data.data);
    })();
  }, [idc, block, room, deviceGuid, showPointsOnly]);

  useEffect(() => {
    if (!spaceGuid || showPointsOnly) {
      return;
    }
    (async () => {
      setShowTickets(false);
      const { countStatistic, total } = await fetchTicketStatisticByDevice({
        deviceGuid,
        spaceGuid,
      });
      ticketCounts.current = countStatistic;
      setShowTickets(total > 0);
    })();
  }, [deviceGuid, spaceGuid, showPointsOnly]);

  useEffect(() => {
    fetchRoomDetail();
  }, [fetchRoomDetail]);

  return (
    <Card
      style={{ width: 720, maxHeight: 432, overflowY: 'auto', ...style }}
      bodyStyle={bodyStyle}
      className={styles.corePointsCard}
      bordered={bordered}
      size="small"
      title={
        !showRelatedRoom ? (
          !showPointsOnly &&
          deviceData?.name && (
            <>
              <Link
                target="_blank"
                to={generateDeviceRecordRoutePath({
                  guid: deviceGuid,
                })}
              >
                <Typography.Text type={alarms.length > 0 ? 'danger' : undefined}>
                  {deviceData.name}
                </Typography.Text>
              </Link>
              {deviceData?.deviceLabel ? `（${deviceData.deviceLabel}）` : ''}
              {deviceData.deviceStatus.operation.code === 'OFF' && <Tag>未启用</Tag>}
              {showTool && <AddDeviceToDiffTool deviceGuid={deviceGuid} />}
            </>
          )
        ) : (
          <Link
            to={generateDeviceRecordRoutePath({
              guid: deviceGuid,
            })}
          >
            {deviceData?.name}
          </Link>
        )
      }
      extra={
        (!showPointsOnly || showRelatedRoom) && (
          <>
            设备位置：
            {room && (
              <Typography.Link
                style={{ paddingRight: 16 }}
                onClick={() => {
                  window.open(
                    generateRoomMonitoringUrl({
                      idc: idc!,
                      block: block!,
                      room: room!,
                      spotlightTarget: deviceGuid,
                    })
                  );
                }}
              >
                {`${room}${roomName ? ` ${roomName}` : ''}`}
              </Typography.Link>
            )}
          </>
        )
      }
    >
      <div
        style={{
          display: 'flex',
          height: '100%',
          flexDirection: 'column',
          justifyContent: 'space-between',
        }}
      >
        <CorePoints
          deviceType={deviceType}
          idc={idc}
          blockGuid={blockGuid}
          deviceGuid={deviceGuid}
          variant={variant}
          hideNoDataItems={hideNoDataItems}
        />
        {showRelatedRoom && relatedRooms.length > 0 && (
          <div>
            <Divider type="horizontal" />
            <>
              关联的包间：
              <Popover
                placement="topLeft"
                content={
                  <div style={{ maxWidth: 344 }}>
                    <RelatedRooms relatedRooms={relatedRooms} />
                  </div>
                }
                trigger="hover"
                mouseEnterDelay={1}
              >
                <Typography.Text
                  ellipsis
                  style={{
                    width: 'calc(100% - 84px)',
                    color: `var(--${prefixCls}-primary-color)`,
                  }}
                >
                  <RelatedRooms relatedRooms={relatedRooms} />
                </Typography.Text>
              </Popover>
            </>
          </div>
        )}
        {!showPointsOnly && (
          <div>
            <Divider style={{ margin: '8px 0' }} />
            <div style={{ display: 'flex', justifyContent: 'space-between' }}>
              <div>
                {alarms.length > 0 ? (
                  <>
                    未恢复告警：
                    <Link
                      target="_blank"
                      to={generateDeviceRecordRoutePath({
                        guid: deviceGuid,
                        tab: 'WARNING_LIST',
                      })}
                    >
                      <Typography.Text type="danger">{alarms.length}</Typography.Text>
                    </Link>
                    条
                  </>
                ) : (
                  <>暂无告警</>
                )}
              </div>
              <div>
                {showTickets ? (
                  <>
                    <span>进行中事项：</span>
                    {Object.keys(ticketCounts.current).map(
                      ticket =>
                        ticketCounts.current[ticket as CountType] > 0 && (
                          <Link
                            key={'ticket-link_' + ticket}
                            target="_blank"
                            className={styles.tabLink}
                            to={generateDeviceRecordRoutePath({
                              guid: deviceGuid,
                              tab: 'IT_LOG',
                              itLogTab: ticket.toLowerCase() as DeviceRecordItLogTabsKeys,
                            })}
                          >
                            {CountTypeMap[ticket as CountType]}
                          </Link>
                        )
                    )}
                  </>
                ) : (
                  <>暂无进行中事项</>
                )}
              </div>
            </div>
            {alarms.length > 0 && (
              <>
                <Divider style={{ margin: '8px 0' }} />
                <Space direction="vertical" style={{ width: '100%' }}>
                  {alarms.map(alarm => {
                    const {
                      point: { validLimits },
                      pointData: { snapshot },
                    } = alarm;

                    return (
                      <Badge
                        key={alarm.point.name}
                        style={{ width: '100%' }}
                        status={alarm.type.code === 'ERROR' ? 'error' : 'warning'}
                        text={
                          <Typography.Text>
                            {`${dayjs(alarm.createdAt).format('MM-DD HH:mm:ss')} `}
                            <AlarmLevelText code={alarm.level} />
                            {alarm.point.name
                              ? ` ${alarm.point.name}_${alarm.cause.name}_`
                              : ` ${alarm.cause.name}_`}
                            {alarm.point.dataType.code === 'AI'
                              ? `告警值:${alarm.pointData.snapshot}${alarm.point.unit || ''}`
                              : `${get(validLimits, [Number(snapshot)])}告警`}
                          </Typography.Text>
                        }
                      />
                    );
                  })}
                </Space>
              </>
            )}
          </div>
        )}
      </div>
    </Card>
  );
};

export const CorePoints = ({
  deviceType,
  deviceGuid,
  idc,
  blockGuid,
  type = 'description',
  variant = 'normal',
  hideNoDataItems = false,
}: {
  deviceType: string;
  deviceGuid: string;
  idc: string | null;
  blockGuid: string | null;
  type?: 'description' | 'statistic';
  hideNoDataItems?: boolean;
} & Pick<LineProps, 'variant'>) => {
  const dispatch = useDispatch();
  const [{ data }] = usePoints({
    fields: { deviceType, isOnlyCore: true, isRemoveMain: true, isRemoveSub: false },
  });
  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const configUtil = new ConfigUtil(useSelector(selectCurrentConfig));
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData!,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  useEffect(() => {
    dispatch(
      syncCommonDataAction({
        strategy: {
          deviceTypesPointsDefinition: [deviceType],
        },
      })
    );
  }, [deviceType, dispatch]);

  useEffect(() => {
    if (blockGuid) {
      dispatch(subscribe({ mode, blockGuid, moduleId, deviceGuids: [deviceGuid] }));
    }
    return () => {
      if (blockGuid) {
        dispatch(unsubscribe({ blockGuid, mode, moduleId }));
      }
    };
  }, [blockGuid, deviceGuid, dispatch]);

  function renderItem(point: Point) {
    const isAI = point.dataType.code === 'AI';
    const realTimeData = getPointMonitoringData(
      { deviceGuid, deviceType },
      isAI
        ? {
            hardCodedPointCode: point.code,
            formatted: true,
          }
        : {
            hardCodedPointCode: point.code,
            reflected: true,
          }
    );
    const btn = <PointDataRenderer key={'renderer_' + point.code} data={realTimeData} />;
    const pointGuids = [{ deviceGuid, pointCode: point.code, unit: point.unit }];
    const seriesOption = [{ name: point.name }];

    if (point.isSub && realTimeData.formattedText === '--') {
      return;
    }

    return isAI ? (
      <PointsLineModalButton
        idcTag={idc!}
        btnText={btn}
        modalText={point.name}
        pointGuids={pointGuids}
        seriesOption={seriesOption}
        variant={variant}
      />
    ) : (
      <PointsStateLineModalButton
        idcTag={idc!}
        btnText={btn}
        modalText={point.name}
        pointGuids={pointGuids}
        seriesOption={seriesOption}
        validLimitsMap={point.validLimits}
        variant={variant}
      />
    );
  }
  const corePoints = useMemo(() => {
    if (!hideNoDataItems) {
      return data;
    }
    return data.filter(point => {
      const data = getPointMonitoringData(
        { deviceGuid, deviceType },
        { hardCodedPointCode: point.code }
      );
      return !data.isBlank;
    });
  }, [data, deviceGuid, deviceType, getPointMonitoringData, hideNoDataItems]);

  return corePoints.length > 0 ? (
    type === 'description' ? (
      <Descriptions column={3} size="small" labelStyle={{ maxWidth: 140 }}>
        {corePoints.map(point => {
          const pointDisplay = renderItem(point);

          return (
            pointDisplay && (
              <Descriptions.Item
                key={'description_' + point.code}
                label={
                  <Tooltip placement="topLeft" title={point.name} mouseEnterDelay={1}>
                    <Typography.Text ellipsis>{point.name}</Typography.Text>
                  </Tooltip>
                }
              >
                {pointDisplay}
              </Descriptions.Item>
            )
          );
        })}
      </Descriptions>
    ) : (
      <Row style={{ height: 180, overflowY: 'auto' }}>
        {corePoints.map((point, index) => {
          const pointDisplay = renderItem(point);

          return (
            pointDisplay && (
              <Col key={point.code} span={6}>
                <div
                  style={{
                    display: 'flex',
                    height: '100%',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    marginBottom: '10px',
                  }}
                >
                  <Statistic
                    style={{ width: '90%' }}
                    valueStyle={{ fontSize: 14 }}
                    title={
                      <Tooltip
                        placement="topLeft"
                        title={point.unit ? `${point.name}(${point.unit})` : point.name}
                        mouseEnterDelay={1}
                      >
                        <Typography.Text ellipsis>
                          {point.unit ? `${point.name}(${point.unit})` : point.name}
                        </Typography.Text>
                      </Tooltip>
                    }
                    valueRender={() => pointDisplay}
                  />
                  {(index + 1) % 4 !== 0 && index !== data.length - 1 && (
                    <Divider type="vertical" style={{ height: '65%' }} />
                  )}
                </div>
              </Col>
            )
          );
        })}
      </Row>
    )
  ) : (
    <Empty />
  );
};

export const DeviceCorePointsCard = ({
  deviceType,
  deviceGuid,
  hideNoDataItems,
  ...rest
}: {
  deviceType: string;
  deviceGuid: string;
  hideNoDataItems?: boolean;
} & Pick<CardProps, 'style' | 'extra'>) => {
  const [deviceData, setDeviceData] = useState<Device | null>(null);
  const blockGuid = deviceData === null ? null : deviceData.spaceGuid.blockGuid;
  const idc = deviceData === null ? null : deviceData.spaceGuid.idcTag;

  useEffect(() => {
    fetchDeviceByGuid({ guid: deviceGuid }).then(({ data, error }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setDeviceData(data);
    });
  }, [deviceGuid]);

  return (
    <Card size="default" title="核心数据" {...rest}>
      <CorePoints
        deviceType={deviceType}
        idc={idc}
        blockGuid={blockGuid}
        deviceGuid={deviceGuid}
        type="statistic"
        hideNoDataItems={hideNoDataItems}
      />
    </Card>
  );
};

const RelatedRooms = ({ relatedRooms }: { relatedRooms: [string, string][] }) => {
  return (
    <>
      {relatedRooms.map(([guid, name], idx) => {
        const { idc, block, room } = getSpaceGuidMap(guid);

        return (
          <React.Fragment key={guid}>
            <Typography.Link
              onClick={() => {
                window.open(
                  generateRoomMonitoringUrl({
                    idc: idc!,
                    block: block!,
                    room: room!,
                  })
                );
              }}
            >
              {`${room} ${name}`}
            </Typography.Link>
            {idx !== relatedRooms.length - 1 && <Divider type="vertical" spaceSize="mini" />}
          </React.Fragment>
        );
      })}
    </>
  );
};
