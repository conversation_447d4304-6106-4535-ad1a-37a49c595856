import React from 'react';
import useDeepCompareEffect from 'use-deep-compare-effect';

import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import type { DataType, Point, PointType } from '@manyun/monitoring.model.point';
import { usePoints } from '@manyun/resource-hub.hook.use-points';

type LabeledValue = { value: string; label: string } | { value: string; label: string }[];

type ValueType = string | string[] | LabeledValue | undefined;

export type PointSelectProps<VT = ValueType> = {
  // 测点类型
  pointTypeList?: PointType[];
  // 设备的三级分类code
  deviceType?: string;
  //状态量or模拟量
  dataTypeList?: DataType[];
  isRemoveSub?: boolean;
  isRemoveMain?: boolean;
  spaceGuid?: string;
  //是否查询非标点位
  isQueryNon?: boolean;
  blockGuid?: string;
  disabledPoint?: string[];
  //是否过滤扩展点位
  filterExtPoint?: boolean;
  onChange?: (
    value: VT,
    labelList: React.ReactNode[],
    extra: unknown,
    selectedPointEntities?: Point | Point[]
  ) => void;
  lazy?: boolean;
} & Omit<TreeSelectProps<VT>, 'onChange'>;

/**
 * 测点选择下拉框
 * treeNodeLabelProp="textLabel" 支持code+name显示
 */
export const PointSelect = React.forwardRef(
  (
    {
      deviceType,
      pointTypeList,
      dataTypeList,
      isRemoveSub,
      isRemoveMain,
      spaceGuid,
      isQueryNon,
      blockGuid,
      disabledPoint,
      filterExtPoint,
      lazy = false,
      ...restProps
    }: PointSelectProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [{ loading, data }, getPoints] = usePoints({
      fields: {
        deviceType,
        pointTypeList,
        dataTypeList,
        isRemoveSub,
        isRemoveMain,
        spaceGuid,
        isQueryNon,
        blockGuid,
      },
      lazy,
    });

    useDeepCompareEffect(() => {
      if (!lazy) {
        return;
      }
      getPoints({
        deviceType,
        pointTypeList,
        dataTypeList,
        isRemoveSub,
        isRemoveMain,
        spaceGuid,
        isQueryNon,
        blockGuid,
      });
    }, [
      dataTypeList,
      deviceType,
      getPoints,
      isRemoveMain,
      isRemoveSub,
      pointTypeList,
      spaceGuid,
      isQueryNon,
      blockGuid,
      lazy,
    ]);

    const formalizeData = () => {
      if (data) {
        return data
          .map(point => ({
            ...point,
            value: point.code,
            title:
              restProps.treeNodeLabelProp === 'textLabel'
                ? `${point.code} ${point.name}`
                : point.name,
            isLeaf: true,
            disabled: disabledPoint ? disabledPoint.includes(point.code) : false,
            textLabel: `${point.code} ${point.name}`,
          }))
          .filter(d => {
            if (filterExtPoint === true) {
              return !d.extCount;
            }
            return true;
          });
      } else {
        return;
      }
    };

    return (
      <TreeSelect
        ref={ref}
        loading={loading}
        treeData={formalizeData()}
        {...restProps}
        allowClear
        onChange={(value, labelList, extra) => {
          const pointCodes: string | string[] | undefined = Array.isArray(value)
            ? value.map(val => (typeof val === 'object' && 'value' in val ? val.value : val))
            : typeof value === 'object' && 'value' in value
              ? value.value
              : value;
          const pointEntities = Array.isArray(pointCodes)
            ? data.filter(d => pointCodes.includes(d.code))
            : data.find(d => d.code === pointCodes);
          restProps?.onChange && restProps?.onChange(value, labelList, extra, pointEntities);
        }}
      />
    );
  }
);

PointSelect.displayName = 'PointSelect';
