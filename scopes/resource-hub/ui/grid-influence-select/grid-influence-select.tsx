import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type GridInfluenceSelectProps = Omit<SelectProps, 'options'>;
export enum GridInfluence {
  GridOneWayPowerFailure = 'GRID_ONE_WAY_POWER_FAILURE',
  GridTwoWayPowerFailure = 'GRID_TWO_WAY_POWER_FAILURE',
  GridOneWayPowerFailureOneMinute = 'GRID_ONE_WAY_POWER_FAILURE_ONE_MIN',
  GridShortTimeHeatUp = 'GRID_SHORT_TIME_HEAT_UP',
  GridTwoWayStayPowered = 'GRID_TWO_WAY_STAY_POWERED',
}
export const gridInfluenceTextMap = {
  [GridInfluence.GridOneWayPowerFailure]: '机柜单路掉电',
  [GridInfluence.GridTwoWayPowerFailure]: '机柜双路掉电',
  [GridInfluence.GridOneWayPowerFailureOneMinute]: '机柜短时单路掉电（1min）',
  [GridInfluence.GridShortTimeHeatUp]: '机柜短时升温',
  [GridInfluence.GridTwoWayStayPowered]: '机柜维持双路供电',
};

type GridInfluenceTypeOption = {
  value: string;
  label: string;
};

const options: GridInfluenceTypeOption[] = [
  { value: 'GRID_ONE_WAY_POWER_FAILURE', label: '机柜单路掉电' },
  { value: 'GRID_TWO_WAY_POWER_FAILURE', label: '机柜双路掉电' },
  { value: 'GRID_ONE_WAY_POWER_FAILURE_ONE_MIN', label: '机柜短时单路掉电（1min）' },
  { value: 'GRID_SHORT_TIME_HEAT_UP', label: '机柜短时升温' },
  { value: 'GRID_TWO_WAY_STAY_POWERED', label: '机柜维持双路供电' },
];

export const GridInfluenceSelect = React.forwardRef(
  ({ ...restProps }: GridInfluenceSelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select ref={ref} style={{ width: 200 }} {...restProps} options={options} />;
  }
);

GridInfluenceSelect.displayName = 'GridInfluenceSelect';
