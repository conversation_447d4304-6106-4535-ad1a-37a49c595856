import React from 'react';
import { useSelector } from 'react-redux';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { selectSpaceCodes, selectSpaceEntities } from '@manyun/resource-hub.state.space';

import { getIdcs } from './idc-select-utils';

export type OptionType = {
  disabled: boolean;
  guid: string;
  name: string;
  regionCode: string;
};

export type IdcSelectProps = {
  regionCode?: string | null;
  disabledIdcGuids?: string[];
} & SelectProps<string, OptionType>;

export const IdcSelect = React.forwardRef(
  (
    { regionCode, disabledIdcGuids = [], ...props }: IdcSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const codes = useSelector(selectSpaceCodes);
    const space = useSelector(selectSpaceEntities(codes));
    const options = getIdcs({ regionCode, space }).map(idc => ({
      ...idc,
      disabled: disabledIdcGuids.includes(idc.guid!),
    }));

    return (
      <Select<string, OptionType> ref={ref} optionLabelProp="guid" {...props} options={options} />
    );
  }
);

IdcSelect.displayName = 'IdcSelect';

IdcSelect.defaultProps = {
  fieldNames: {
    value: 'guid',
    label: 'guid',
  },
};
