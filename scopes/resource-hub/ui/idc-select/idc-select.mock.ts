import { configureStore, createSlice } from '@reduxjs/toolkit';

export const spacesSlice = createSlice({
  name: 'resource.spaces',
  initialState: {
    entities: [
      {
        type: 'IDC',
        code: 'sxdtyg',
        name: '中联绿色大数据产业基地',
        parentCode: 'NC',
      },
      {
        type: 'IDC',
        code: 'EC01',
        name: 'EC01',
        parentCode: 'NC',
      },
      {
        type: 'IDC',
        code: 'EC02',
        name: 'EC02',
        parentCode: 'NC',
      },
    ],
    codes: ['sxdtyg', 'EC01', 'EC02'],
  },
  reducers: {},
});

export const commonStore: any = configureStore({
  reducer: {
    common: spacesSlice.reducer,
  },
});
