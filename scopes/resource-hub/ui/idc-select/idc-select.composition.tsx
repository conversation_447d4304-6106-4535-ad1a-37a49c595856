import React from 'react';

import { StoreProvider } from '@manyun/base-ui-web.context.store-context';
import { ConfigProvider } from '@manyun/base-ui.context.config';

import { IdcSelect } from './idc-select';
import { commonStore } from './idc-select.mock';

export const BasicIdcSelect = () => (
  <StoreProvider store={commonStore}>
    <ConfigProvider>
      <IdcSelect style={{ width: 300 }} mode="multiple" disabledIdcGuids={['sxdtyg']} />
    </ConfigProvider>
  </StoreProvider>
);
