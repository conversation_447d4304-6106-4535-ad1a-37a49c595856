import { MetadataJSON, MetaType } from '@manyun/resource-hub.model.metadata';

export function getIdcs({
  regionCode,
  space,
}: {
  regionCode: string | null | undefined;
  space: MetadataJSON[];
}) {
  if (!space) {
    return [];
  }

  const transformer = (meta: any) => ({
    guid: meta.code,
    name: meta.name,
    regionCode: meta.parentCode,
  });

  if (!regionCode) {
    return space.filter(({ type }) => type === MetaType.Idc).map(transformer);
  }

  return space
    .filter(({ type, parentCode }) => type === MetaType.Idc && parentCode === regionCode)
    .map(transformer);
}
