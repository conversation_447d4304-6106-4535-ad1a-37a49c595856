import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';

import type { BorrowStatus } from '@manyun/resource-hub.model.borrow-return';
import { getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';

export type BorrowStatusTextProps = {
  code: BorrowStatus;
};

const borrowStatusTextColor: Record<BorrowStatus, string> = {
  WAIT_STOCK_OUT: 'warning',
  BORROWING: 'warning',
  RETURN: 'success',
  DRAFT: 'default',
  APPROVING: 'warning',
  CANCELED: 'default',
};

export function BorrowStatusText({ code }: BorrowStatusTextProps) {
  const borrowReturnLocales = getBorrowReturnLocales();

  return <Tag color={borrowStatusTextColor[code]}>{borrowReturnLocales['borrowStatus'][code]}</Tag>;
}
