import { Form } from '@galiojs/awesome-antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import type { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { getComponent } from '@manyun/resource-hub.ui.spec-form-items';

export type SpecSearchSelectProps = {
  searchType: 'block' | 'grid';
  form?: FormInstance;
};

/** 属性选择通用组件 */
export const SpecSearchSelect = ({ form, searchType = 'block' }: SpecSearchSelectProps) => {
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const deviceType = useMemo(() => {
    if (searchType === 'block') {
      return configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
    } else {
      return configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_GRID);
    }
  }, [configUtil, searchType]);

  const [specInfo, setSpecInfo] = useState<(SpecJSON & BackendSpec)[]>([]);
  const [specIdOptions, setSpecIdOptions] = useState<SelectProps['options']>([]);
  const [selectSpec, setSelectSpec] = useState<BackendSpec>();

  const getSpecs = useCallback(
    async (specId?: number) => {
      if (!deviceType) {
        return;
      }
      const { data, error } = await fetchSpecs({
        deviceType,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      setSpecInfo(data.data);
      setSpecIdOptions(
        data.data.map(item => ({
          label: item.specName,
          value: item.id,
        }))
      );
      if (specId) {
        // 搜索列表缓存兼容
        setSelectSpec(data.data.find(item => item.id === specId));
      }
    },
    [deviceType]
  );

  useEffect(() => {
    getSpecs();
  }, [getSpecs]);

  useEffect(() => {
    // 搜索列表缓存兼容
    if (!specInfo?.length && form?.getFieldValue('specInfo') && form?.getFieldValue('specId')) {
      getSpecs(form?.getFieldValue('specId'));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [specInfo]);

  return (
    <div>
      <Input.Group compact>
        <Form.Item name="specId">
          <Select
            style={{
              minWidth: 200,
            }}
            showSearch
            placeholder="请输入关键字搜索"
            filterOption={(input, option) => {
              return (option?.label as string).toLowerCase().includes(input.toLowerCase());
            }}
            options={specIdOptions}
            onSelect={value => {
              setSelectSpec(specInfo.find(item => item.id === value));
              form?.setFieldValue('specInfo', undefined);
            }}
          />
        </Form.Item>
        <Form.Item
          name="specInfo"
          rules={[{ required: !!form?.getFieldValue('specId'), message: `属性值不可为空` }]}
        >
          {selectSpec ? (
            getComponent(selectSpec, {
              selectMode: 'multiple',
            })
          ) : (
            <Select
              disabled
              style={{
                width: 230,
              }}
            />
          )}
        </Form.Item>
      </Input.Group>
    </div>
  );
};
