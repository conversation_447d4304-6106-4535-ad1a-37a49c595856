import React from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import type { BackendSpec } from '@manyun/resource-hub.model.spec';

export type SpecFaceControlSelectProps = {
  form?: FormInstance;
  specItem?: BackendSpec;
};
const validOptions = [
  {
    value: 'AUTH_PERIOD',
    label: '授权有效期内',
  },
  {
    value: 'REAL',
    label: '实时',
  },
];

/** 门禁人脸有效期-自定义组件 */
export const SpecFaceControlSelect = ({ form }: SpecFaceControlSelectProps) => {
  const faceType = Form.useWatch('Spec_ACS_PERIOD', form);
  const [configUtil] = useConfigUtil();

  const { accessControlFace = false } = configUtil.getScopeCommonConfigs('resources')?.specs ?? {
    accessControlFace: false,
  };
  const options = React.useMemo(
    () => [
      {
        value: 'custom',
        label: !accessControlFace ? '自定义' : '当天',
      },
      {
        value: 'valid',
        label: '有效条件',
      },
    ],
    [accessControlFace]
  );
  const filterValidOptions = React.useMemo(
    () =>
      !accessControlFace ? validOptions.filter(option => option.value !== 'REAL') : validOptions,
    [accessControlFace, validOptions]
  );
  return (
    <div>
      <Input.Group compact>
        <Form.Item name="Spec_ACS_PERIOD" initialValue="custom">
          <Select
            style={{
              minWidth: 100,
            }}
            options={options}
            onSelect={value => {
              if (value === 'custom') {
                form?.setFieldValue('Spec_ACS_PERIOD_VALUE', undefined);
              }
            }}
          />
        </Form.Item>
        <Form.Item name="Spec_ACS_PERIOD_VALUE">
          {faceType === 'valid' ? (
            <Select
              style={{
                minWidth: 130,
              }}
              options={filterValidOptions}
            />
          ) : (
            <TimePicker format="HH:mm" />
          )}
        </Form.Item>
      </Input.Group>
    </div>
  );
};
