import { QuestionCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import React from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance, FormProps } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Select } from '@manyun/base-ui.ui.select';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { CompanySelect } from '@manyun/crm.ui.company-select';
import type { BackendSpec, SpecJSON } from '@manyun/resource-hub.model.spec';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { SpecFaceControlSelect } from '@manyun/resource-hub.ui.spec-search-select';
import { VALUE_TYPE_KEY_MAP } from '@manyun/resource-hub.ui.spec-select';

export type SpecFormItemsProps = {
  /**
   * sets the component children.
   */
  specInfo?: (SpecJSON & BackendSpec)[];
  form: FormInstance;
  formLayout: FormProps['layout'];
  showControlFaceCustom?: boolean;
};

function getCustomComponent(
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  optComponent: any,
  options?: {
    selectMode?: SelectMode;
    item?: BackendSpec;
    showControlFaceCustom?: boolean;
    form?: FormInstance;
  }
) {
  if (options?.item?.specCode === 'ACS_PERIOD' && options?.showControlFaceCustom) {
    // 门禁人脸单独处理
    return <SpecFaceControlSelect specItem={options?.item} form={options.form} />;
  }
  switch (optComponent.components) {
    case 'location':
      return (
        <LocationCascader
          style={{ width: 200 }}
          nodeTypes={optComponent?.nodes}
          authorizedOnly={
            Array.isArray(optComponent?.componentProps) &&
            optComponent?.componentProps.indexOf('authorized') > -1
          }
          includeVirtualBlocks={
            Array.isArray(optComponent?.componentProps) &&
            optComponent?.componentProps.indexOf('virtual') > -1
          }
          changeOnSelect={false}
          multiple={options?.selectMode === 'multiple'}
        />
      );

    case 'metaData':
      return (
        <MetaTypeSelect
          style={{ width: 200 }}
          allowClear
          metaType={optComponent?.componentProps}
          mode={options?.selectMode === 'multiple' ? 'multiple' : undefined} //只处理multiple,其他类型不需要
        />
      );

    case 'time':
      return <TimePicker format="HH:mm" />;

    default:
      return null;
  }
}

export const getInitialValue = (item: SpecJSON & BackendSpec) => {
  if (item.inputWay !== 'COMPONENT') {
    if (item?.optionType === true) {
      return item.specValue?.split(',') || [];
    }

    return item.specValue;
  }

  const type = item.options && JSON.parse(item.options) && JSON.parse(item.options)?.components;

  if (type === 'location') {
    if (item?.optionType === true) {
      return item.specValue?.split(',')?.map(val => {
        return getLocationArr(val);
      });
    }

    return getLocationArr(item.specValue);
  } else if (type === 'time') {
    return item.specValue?.includes(':') ? moment(item.specValue, 'HH:mm') : undefined;
  } else {
    if (item?.optionType === true) {
      return item.specValue?.split(',') || [];
    }

    return item.specValue;
  }
};

function getOptions(options: string) {
  let newOptions = [];
  const optionsArr = options.split(',');
  newOptions = optionsArr.map(option => ({ label: option, value: option }));
  return newOptions;
}

export type SelectMode = 'signle' | 'multiple' | 'tags';

export function getComponent(
  item: BackendSpec,
  options?: {
    selectMode?: SelectMode;
    showControlFaceCustom?: boolean;
    form?: FormInstance;
    inputNumberStyle?: React.CSSProperties;
  }
) {
  const optComponent = item.inputWay === 'COMPONENT' && item.options && JSON.parse(item.options);

  if (item.specCode === 'PROJECT_COMPANY') {
    // 额外处理项目公司，建议后续改造基础管理
    return <CompanySelect style={{ width: 280 }} />;
  }

  switch (item.inputWay) {
    case 'INPUT':
      return item.valueType === VALUE_TYPE_KEY_MAP.NUMBER ? (
        // eslint-disable-next-line react/jsx-filename-extension
        <InputNumber
          style={{ width: 200, ...options?.inputNumberStyle }}
          addonAfter={item.specUnit}
          min={0.01}
          max={999999.99}
          precision={2}
        />
      ) : (
        <Input style={{ width: 200 }} allowClear addonAfter={item.specUnit} />
      );
    case 'OPT':
      return (
        <Select
          style={{ width: 200 }}
          allowClear
          options={getOptions(item.options!)}
          mode={
            (options?.selectMode === 'multiple' &&
              !getOptions(item.options!)?.some(item => item.label === '是')) ||
            item.optionType
              ? 'multiple'
              : undefined
          } // 兼容普通选择不走多选
        />
      );
    case 'COMPONENT':
      return getCustomComponent(optComponent, {
        selectMode: options?.selectMode || item.optionType === true ? 'multiple' : 'signle',
        item,
        showControlFaceCustom: options?.showControlFaceCustom,
        form: options?.form,
      });
    default:
      return null;
  }
}

function getLocationArr(value: string | null) {
  if (!value) {
    return;
  }
  const arr = value.split('.');
  const data = [arr[0]];
  for (let i = 1; i < arr.length; i++) {
    data[i] = data[i - 1] + '.' + arr[i];
  }
  return data;
}

const getRule = (item: Record<string, any>) => {
  if (item?.specName === '楼栋别名') {
    return [
      {
        max: 50,
        message: '最多输入 50 个字符！',
      },
    ];
  }

  return item.valueType === VALUE_TYPE_KEY_MAP.CHARACTER && item.inputWay === 'INPUT'
    ? [
        { required: item.required, message: `${item.specName}必填！` },
        {
          max: 200,
          message: '最多输入 200 个字符！',
        },
      ]
    : [{ required: item.required, message: `${item.specName}必填！` }];
};

export function SpecFormItems({
  specInfo,
  form,
  formLayout,
  showControlFaceCustom,
}: SpecFormItemsProps) {
  return (
    <div>
      {Array.isArray(specInfo) ? (
        <Form layout={formLayout} form={form}>
          <Row>
            {specInfo.map(item => {
              return (
                <Col key={item.id} span={12}>
                  <Form.Item
                    label={
                      <>
                        {item.specName}
                        {item.description && (
                          <Tooltip title={item.description}>
                            <QuestionCircleOutlined
                              style={{
                                marginLeft: 4,
                              }}
                            />
                          </Tooltip>
                        )}
                      </>
                    }
                    rules={getRule(item)}
                    name={`Spec_${item.specCode}`}
                    labelCol={{ span: 8 }}
                    initialValue={getInitialValue(item)}
                  >
                    {getComponent(item, { showControlFaceCustom, form })}
                  </Form.Item>
                </Col>
              );
            })}
          </Row>
        </Form>
      ) : null}
    </div>
  );
}
