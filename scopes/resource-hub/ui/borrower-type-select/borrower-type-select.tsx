import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';

export type BorrowerTypeSelectProps = Omit<SelectProps, 'options'>;

export const BorrowerTypeSelect = React.forwardRef(
  (props: BorrowerTypeSelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getBorrowReturnLocales();

    const options = useDeepCompareMemo(() => {
      return [
        {
          value: 'PARTNER',
          label: locales['borrower']['PARTNER'],
        },
        {
          value: 'CUSTOMER',
          label: locales['borrower']['CUSTOMER'],
        },
        {
          value: 'SUPPLIER',
          label: locales['borrower']['SUPPLIER'],
        },
        {
          value: 'OTHER',
          label: locales['borrower']['OTHER'],
        },
      ];
    }, [locales]);

    return <Select {...props} ref={ref} options={options} />;
  }
);

BorrowerTypeSelect.displayName = 'BorrowerTypeSelect';
