import React from 'react';

import ImportOutlined from '@ant-design/icons/es/icons/ImportOutlined';
import LoadingOutlined from '@ant-design/icons/es/icons/LoadingOutlined';

import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { RcFile } from '@manyun/dc-brain.ui.upload';
import { Upload } from '@manyun/dc-brain.ui.upload';

export type GraphixBaseMapImportProps = {
  open: boolean;
  importLoading: boolean;
  onBackgroundImageChange: (file: RcFile) => void;
  tips?: string;
};

// const BackgroundImageUpload = styled(Upload)`
//   width: auto;

//   > .${prefixCls}-upload {
//     width: 128px;
//     height: 128px;
//   }
// `;

export function GraphixBaseMapImport({
  open,
  importLoading,
  onBackgroundImageChange,
  tips = '底图是包间俯瞰图，可通过导入自动识别',
}: GraphixBaseMapImportProps) {
  return (
    <Modal title="新建视图" closable={false} footer={null} centered open={open}>
      <Space align="start">
        <Upload
          style={{
            width: 'auto',
          }}
          accept="image/*"
          listType="picture-card"
          showUploadList={false}
          beforeUpload={file => {
            onBackgroundImageChange(file);
            return false;
          }}
        >
          <div>
            {importLoading ? (
              <LoadingOutlined style={{ fontSize: 60 }} />
            ) : (
              <ImportOutlined style={{ fontSize: 60 }} />
            )}

            <div>导入底图</div>
          </div>
        </Upload>
        <span>
          <Typography.Title level={4}>底图</Typography.Title>
          <Typography.Paragraph>{tips}</Typography.Paragraph>
        </span>
      </Space>
    </Modal>
  );
}
