import uniq from 'lodash/uniq';

import type { ResouceTreeNode, ResourceData } from './resoure-tree-type';

/**
 * 根据左侧的checkedKeys 返回找出右侧的targetKeys,targetResouces
 * 规则：若父节点和子节点都被选中，则不展示子节点。
 * @param {string[]} checkedKeys 被选中节点的 key 的集合
 * @param {object} entities 数据源 实体
 * @return eg: [{name: '华北',code: '069NC'}]
 */
export function getTargetResouce(checkedKeys: string[], entities: Record<string, ResourceData>) {
  let _visibleResources: { name: string; code: string }[] = [];
  let _cacheKeys: string[] = [];

  checkedKeys.forEach(key => {
    const _item = entities[key];
    if (_item) {
      if (_item.parentCode) {
        /**node的父节点也选中则只返回父节点code，不包含则返回node code */
        if (checkedKeys.includes(_item.parentCode)) {
          _cacheKeys.push(_item.parentCode);
        } else {
          _cacheKeys.push(_item.code);
        }
      } else {
        /**node为区域，则返回区域code */
        _cacheKeys.push(key);
      }
    }
  });
  _cacheKeys = uniq(_cacheKeys);

  /**再次遍历 去除父节点已选中的节点 */
  _cacheKeys.forEach(key => {
    const _item = entities[key];
    if (_item) {
      if (!_item.parentCode || !_cacheKeys.includes(_item.parentCode ?? '')) {
        const fullName = getResourceFullName(_item.code, entities);

        _visibleResources.push({
          name: fullName,
          code: _item.code,
        });
      }
    }
  });

  return _visibleResources;
}
/**
 *
 * @param key
 * @param entities
 * @returns eg: 华东.EC06.A
 */
export function getResourceFullName(key: string, entities: Record<string, ResourceData>) {
  let _names: string[] = [];
  loop(key);
  function loop(_key: string) {
    const _item = entities[_key];
    if (_item) {
      _names.push(_item.name);
      if (_item.parentCode) {
        loop(_item.parentCode);
      }
    }
  }
  _names = _names.reverse();
  return _names.join('.').replace(/\./, '-');
}

/**
 * 根据右侧targetKey 返回左侧需要的checkedKeys
 * @param changeKeys 右侧targetKeys
 * @param treeData
 * @returns
 */
export function getCheckedKeysByTargetKey(changeKeys: string[], treeData: ResouceTreeNode[]) {
  let _checkedKeys: string[] = [];
  /**遍历找到所有的子节点 */
  changeKeys.forEach(key => {
    let _childrenKeys: string[] = [];
    getChildernByParentKey(key, treeData, _childrenKeys);
    _checkedKeys = _checkedKeys.concat(_childrenKeys);
    _checkedKeys.push(key);
  });
  return _checkedKeys;
}

function getChildernByParentKey(key: string, treeData: ResouceTreeNode[], _childrenKeys: string[]) {
  treeData.forEach(d => {
    if (d.key === key) {
      if (d.children) {
        loop(d.children, _childrenKeys);
      }
    } else if (d.children) {
      getChildernByParentKey(key, d.children, _childrenKeys);
    }
  });
}

function loop(_treeData: ResouceTreeNode[], _childrenKeys: string[]) {
  if (_treeData) {
    _treeData.forEach(d => {
      _childrenKeys.push(d.key);
      if (d.children) {
        loop(d.children, _childrenKeys);
      }
    });
  }
}

/**
 * 根据 穿梭框右侧的targerKeys 返回 接口需要的对应空间code
 * 注: 选中区域 将返回区域下所有机房编码，但不返回区域编码
 * @param targetKeys
 * @param entities
 * @param paralleData
 * @returns {string[]} 机房｜楼栋  数组
 */
export function transformCheckedKeys(
  targetKeys: string[],
  entities: Record<string, ResourceData>,
  paralleData: ResourceData[]
) {
  const _keys: string[] = [];
  targetKeys.forEach(key => {
    const _item = entities[key];
    if (_item) {
      if (_item.type !== 'REGION' && _item.parentCode && !targetKeys.includes(_item.parentCode)) {
        _keys.push(_item.code);
      } else if (_item.type === 'REGION') {
        const _idcTags = paralleData.filter(item => item.parentCode === _item.code);
        (_idcTags ?? []).forEach(idc => {
          _keys.push(idc.code);
        });
      }
    }
  });

  return _keys;
}

/**
 * 获取默认选择的所有节点，包含区域
 * 注：某个区域下所有的机房选中，则需要选中改区域
 * @param defaultTargetKeys 默认选中值
 * @param entities  实体
 * @param paralleData  数组
 * @returns
 */
export function getAllDefaultCheckedkeys(
  defaultTargetKeys: string[],
  entities: Record<string, ResourceData>,
  paralleData: ResourceData[]
) {
  let _keys: string[] = [];

  /**筛选中默认值的所有机房 */
  let _defaultAllIdcTags: { code: string; parentCode: string | null }[] = [];

  defaultTargetKeys.forEach(key => {
    const _item = entities[key];
    if (_item) {
      if (_item.type === 'IDC') {
        /**如果是机房，将机房下所有楼栋返回 */
        const _blockTags = paralleData.filter(d => d.parentCode === _item.code);
        _keys = _keys.concat(_blockTags.map(block => block.code));
        _defaultAllIdcTags.push({ code: _item.code, parentCode: _item.parentCode });
      }
      _keys.push(_item.code);
    }
  });

  /** 当idc 节点 key 都传入，region 节点也自动选中 */
  let _selectedRegionCodes: string[] = [];
  _defaultAllIdcTags.forEach(idc => {
    const _allIdcTags = paralleData
      .filter(resource => resource.parentCode === idc.parentCode)
      .map(r => r.code);

    if (
      _allIdcTags.every(idc => _defaultAllIdcTags.map(tag => tag.code).includes(idc)) &&
      idc.parentCode &&
      !_selectedRegionCodes.includes(idc.parentCode)
    ) {
      _selectedRegionCodes.push(idc.parentCode);
    }
  });

  return _keys.concat(_selectedRegionCodes);
}
