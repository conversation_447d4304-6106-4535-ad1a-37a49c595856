import React from 'react';
import { useSelector } from 'react-redux';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { selectMyResources } from '@manyun/auth-hub.state.user';
import { selectSpaceEntities } from '@manyun/resource-hub.state.space';

// @ts-ignore
import { getCitiesTree } from '@manyun/dc-brain.legacy.redux/selectors/commonSelectors';
// @ts-ignore
import { getEquipmentIdcDevices } from '@manyun/dc-brain.legacy.redux/selectors/equipmentSelector';

import type { ResouceTreeNode, ResourceData, ResourceTreeNodeType } from './resoure-tree-type';

/**
 * 返回所有空间数据(区域、机房、楼栋、包间、设备) -- 设备待完善
 */
export type UseResourcesProps = {
  nodeTypes: ResourceTreeNodeType[];
  disabledTypes?: ResourceTreeNodeType[];
  disabledNodes?: string[];
  authorizedOnly?: boolean;
  includeVirtualBlocks?: boolean;
  searchValue?: string;
  nodeMutator?: (node: ResouceTreeNode) => ResouceTreeNode;
};
export function useResources({
  nodeTypes,
  disabledTypes,
  disabledNodes,
  includeVirtualBlocks,
  authorizedOnly,
  searchValue,
  nodeMutator,
}: UseResourcesProps) {
  const regionResources = useSelector(getCitiesTree);
  const allLocationResources = useSelector(selectSpaceEntities());
  const myLocationResources = useSelector(selectMyResources);
  const deviceResources = useSelector(getEquipmentIdcDevices);

  const resources = useDeepCompareMemo(() => {
    let _resouces: ResourceData[] = [];
    if (nodeTypes.includes('REGION')) {
      (regionResources || []).forEach((region: { children: any[] }) => {
        region.children.forEach(children => {
          _resouces.push({
            type: 'REGION',
            code: children.value,
            name: children.label,
            parentCode: null,
          });
        });
      });
    }
    if (nodeTypes.includes('IDC') || nodeTypes.includes('BLOCK') || nodeTypes.includes('ROOM')) {
      _resouces = _resouces.concat(
        (authorizedOnly ? myLocationResources ?? [] : allLocationResources ?? []) as ResourceData[]
      );
    }

    if (nodeTypes.includes('DEVICE')) {
      Object.keys(deviceResources).forEach(deviceKey => {
        deviceResources[deviceKey].forEach((device: { metaCode: any; metaName: any }) => {
          _resouces.push({
            type: 'DEVICE',
            code: device.metaCode,
            name: device.metaName,
            parentCode: deviceKey,
          });
        });
      });
    }

    /**不返回虚拟楼 */
    if (includeVirtualBlocks === false) {
      _resouces = _resouces.filter(resource => {
        let codes = resource.code.split('.');
        if (resource.type === 'BLOCK' && codes[1] === codes[0]) {
          return false;
        }
        return true;
      });
    }

    return _resouces;
  }, [
    allLocationResources,
    authorizedOnly,
    includeVirtualBlocks,
    deviceResources,
    myLocationResources,
    nodeTypes,
    regionResources,
  ]);

  const resourceEntities = useDeepCompareMemo(() => {
    const _entities: Record<string, ResourceData> = {};

    resources.forEach(resource => {
      _entities[resource.code] = resource;
    });

    return _entities;
  }, [resources]);

  const treeData = useDeepCompareMemo(() => {
    let _treeData = generateTreeData<ResouceTreeNode, ResourceData>(resources, {
      key: 'code',
      typeKey: 'type',
      parentKey: 'parentCode',
      nodeTypes: nodeTypes,
      getNode: (data, children, depth) => {
        const metaCodes = data.code.split('.');
        const isIdcDotIdc = metaCodes.length === 2 && metaCodes[0] === metaCodes[1];
        // 如果是园区的话，就展示园区
        const label = isIdcDotIdc ? `${metaCodes[0]}.${data.name}` : data.name;

        let title: React.ReactNode | string = label;

        if (searchValue) {
          const strTitle = label;
          const index = strTitle.indexOf(searchValue);
          const beforeStr = strTitle.substring(0, index);
          const afterStr = strTitle.slice(index + searchValue.length);
          title =
            index > -1 ? (
              <span>
                {beforeStr}
                <span style={{ color: 'var(--manyun-error-color)' }}>{searchValue}</span>
                {afterStr}
              </span>
            ) : (
              <span>{strTitle}</span>
            );
        }
        const node = {
          label,
          title,
          key: data.code,
          type: data.type,
          children: children === null ? undefined : children,
          disabled:
            disabledTypes?.includes(data.type as unknown as ResourceTreeNodeType) ||
            disabledNodes?.includes(data.code),
          isLeaf: depth === nodeTypes.length - 1 ? true : false,
        };
        const _node = typeof nodeMutator == 'function' ? nodeMutator(node) : node;
        return _node;
      },
    });

    return _treeData;
  }, [disabledTypes, disabledNodes, nodeMutator, nodeTypes, resources, searchValue]);

  return [{ paralleData: resources, treeData: treeData, entities: resourceEntities }] as const;
}
