import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch } from 'react-redux';

import debounce from 'lodash.debounce';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Space } from '@manyun/base-ui.ui.space';
import { Transfer } from '@manyun/base-ui.ui.transfer';
import { Tree } from '@manyun/base-ui.ui.tree';

import { syncCommonDataAction } from '@manyun/dc-brain.state.common';

import {
  getAllDefaultCheckedkeys,
  getCheckedKeysByTargetKey,
  getTargetResouce,
  transformCheckedKeys,
} from './resource-tree-util';
import type { ResourceTreeNodeType } from './resoure-tree-type';
import { useResources } from './use-resources';

export type ResourceTreeTransferProps = {
  /**
   * 只展示当前用户有权限的空间数据
   *
   * @since 0.0.1
   */
  authorizedOnly?: boolean;

  /**
   * 控制显示的节点类型
   *
   * @since 0.0.1
   */
  nodeTypes?: ResourceTreeNodeType[];

  /**
   * 按空间类型禁用节点
   *
   * @since 0.0.1
   */
  disabledTypes?: ResourceTreeNodeType[];

  /**
   * 是否需要虚拟楼
   * @defaultValue false
   */
  includeVirtualBlocks?: boolean;
  /**
   *  默认选中值
   *  @since 0.0.1
   */
  defaultTargetKeys?: string[];

  /**
   * 选项在两栏之间转移时的回调函数
   * @since 0.0.1
   */
  onChange?: (keys: string[], allCheckedKeys?: string[]) => void;

  /**
   * 是否展示搜索框
   *  @defaultValue true
   */
  showSearch?: boolean;
};
/** @deprecated 后续替换@攀老师资源选择新组件，不再维护 */
export function ResourceTreeTransfer({
  authorizedOnly = false,
  nodeTypes = ['IDC', 'BLOCK', 'ROOM'],
  disabledTypes = [],
  includeVirtualBlocks = false,
  showSearch = true,
  defaultTargetKeys,
  onChange,
}: ResourceTreeTransferProps) {
  const dispatch = useDispatch();
  const [searchValue, setSearchValue] = useState<string | undefined>(undefined);
  /** tree checked target resource */
  const [targetResource, setTargetResource] = useState<{ name: string; code: string }[]>([]);
  /**  tree checked cache code */
  const [checkedKeys, setCheckedKeys] = useState<string[]>([]);
  /**  tree checked  target code */
  const [targetCheckedKeys, setTargetCheckedKeys] = useState<string[]>([]);
  const [{ paralleData, treeData, entities }] = useResources({
    authorizedOnly,
    nodeTypes,
    disabledTypes,
    disabledNodes: targetCheckedKeys,
    searchValue,
    includeVirtualBlocks,
  });

  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>([]);
  const [autoExpandParent, setAutoExpandParent] = useState(true);

  useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { space: 'IF_NULL', citiesTree: 'IF_NULL' } }));
  }, [dispatch]);

  useDeepCompareEffect(() => {
    let timeout: number | null;
    if (defaultTargetKeys) {
      const _defaultAllCheckedKeys = getAllDefaultCheckedkeys(
        defaultTargetKeys,
        entities,
        paralleData
      );
      const _defaultTargetResources = getTargetResouce(_defaultAllCheckedKeys, entities);
      setCheckedKeys(_defaultAllCheckedKeys);
      setExpandedKeys(_defaultAllCheckedKeys);
      setTargetResource(_defaultTargetResources);
      timeout = window.setTimeout(() => {
        setTargetCheckedKeys(_defaultAllCheckedKeys);
      }, 300);
    }
    return () => {
      if (timeout) {
        window.clearTimeout(timeout);
      }
    };
  }, [defaultTargetKeys, paralleData, entities]);

  const onSearchLeft = debounce((value: string) => {
    const newExpandedKeys = paralleData
      .map(item => {
        if (item.name.indexOf(value) > -1) {
          if (entities[item.code]) {
            return entities[item.code].parentCode;
          }
        }
        return null;
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);

    setExpandedKeys(newExpandedKeys as React.Key[]);
    setSearchValue(value);
    setAutoExpandParent(true);
  }, 300);

  const _extraProps = useMemo(() => {
    const _restProps = {};
    if (nodeTypes.includes('DEVICE')) {
      return {
        loadData: (node: unknown) => {
          /**@TODO 请求设备列表 */
          return Promise.resolve([]);
        },
      };
    }
    return _restProps;
  }, [nodeTypes]);

  return (
    <Transfer
      showSearch={showSearch}
      targetKeys={targetResource.map(resource => resource.code)}
      dataSource={[...paralleData, ...targetResource].map(d => ({
        key: d.code,
        title: d.name,
      }))}
      render={item => item.title}
      showSelectAll={false}
      listStyle={{
        width: '100%',
        height: 440,
        overflowY: 'auto',
      }}
      onChange={(keys, direction) => {
        const _targetResources = getTargetResouce(keys, entities);
        let _checkedKeys: string[] = [];
        if (direction === 'left') {
          /**从右侧像左侧转移 需要转换checkedKeys */
          _checkedKeys = getCheckedKeysByTargetKey(keys, treeData);
          setCheckedKeys(_checkedKeys);
        } else {
          _checkedKeys = checkedKeys;
        }
        setTargetCheckedKeys(_checkedKeys);
        setTargetResource(_targetResources);
        if (onChange) {
          const _effectiveKeys = transformCheckedKeys(_checkedKeys, entities, paralleData);
          onChange(_effectiveKeys, _checkedKeys);
        }
      }}
      onSearch={(direction, value) => {
        if (direction === 'left') {
          onSearchLeft(value);
        }
      }}
    >
      {({ direction, selectedKeys, onItemSelectAll }) => {
        if (direction === 'left') {
          return (
            <Space
              direction="vertical"
              style={{
                padding: '0 16px',
                width: '100%',
                height: 320,
                overflowY: 'auto',
              }}
            >
              <Tree
                blockNode
                checkable
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                checkedKeys={checkedKeys}
                treeData={treeData}
                selectable={false}
                onExpand={expandedKeys => {
                  setExpandedKeys(expandedKeys);
                  setAutoExpandParent(false);
                }}
                onCheck={(_, info) => {
                  const _checkedKeys: string[] = info.checkedNodes.map(
                    node => node.key
                  ) as string[];
                  setCheckedKeys(_checkedKeys as string[]);

                  /**selectedKeys _checkedKeys 差集 */
                  const _excludeKeys = selectedKeys
                    .concat(_checkedKeys)
                    .filter(v => selectedKeys.includes(v) && !_checkedKeys.includes(v));

                  /** _checkedKeys targetCheckedKeys 差集 */
                  const _includeKeys = _checkedKeys
                    .concat(targetCheckedKeys)
                    .filter(v => _checkedKeys.includes(v) && !targetCheckedKeys.includes(v));

                  onItemSelectAll(_includeKeys as string[], true);
                  onItemSelectAll(_excludeKeys as string[], false);
                }}
                {..._extraProps}
              />
            </Space>
          );
        }
      }}
    </Transfer>
  );
}
