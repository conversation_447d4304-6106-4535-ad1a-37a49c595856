import React from 'react';

import { wrapCompositions } from '@manyun/dc-brain.composition.compositions-wrapper';
import { createCompositionWrapper } from '@manyun/resource-hub.state.device-types';

import { DeviceTypeText } from './device-type-text';

const StoreCompositionWrapper = createCompositionWrapper();
const CompositionWrapper = wrapCompositions([StoreCompositionWrapper]);

export const BasicDeviceTypeText = () => {
  return (
    <CompositionWrapper>
      <DeviceTypeText code="10101" />
    </CompositionWrapper>
  );
};
