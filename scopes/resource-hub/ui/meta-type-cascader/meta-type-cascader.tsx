import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps, CascaderRef } from '@manyun/base-ui.ui.cascader';

import { useLazyMetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import type { MetadataTree } from '@manyun/resource-hub.gql.client.metadata';

export type MetaTypeCascaderProps = {
  metaType: string[];
  trigger?: 'onFocus' | 'onDidMount';
  allowClear?: boolean;
  changeOnSelect?: boolean;
  optionFilter?: (option: MetadataTree) => boolean;
} & Omit<CascaderProps<MetadataTree>, 'options' | 'loading'>;

function _MetaTypeCascader(
  {
    metaType,
    trigger = 'onFocus',
    onFocus,
    allowClear = true,
    changeOnSelect = true,
    optionFilter,
    ...restProps
  }: MetaTypeCascaderProps,
  ref?: React.ForwardedRef<CascaderRef>
) {
  const [getMetaData, { loading, data }] = useLazyMetadataTree({
    variables: {
      type: metaType.join(','),
    },
  });
  const options = useDeepCompareMemo(() => {
    if (optionFilter && data?.metadataTree) {
      return data.metadataTree.filter(optionFilter);
    }
    return data?.metadataTree ?? [];
  }, [data?.metadataTree, optionFilter]);

  React.useEffect(() => {
    if (trigger === 'onDidMount') {
      getMetaData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    // @ts-ignore: props accept singleValueType
    <Cascader<MetadataTree>
      allowClear={allowClear}
      changeOnSelect={changeOnSelect}
      fieldNames={{ label: 'name', value: 'code' }}
      loading={loading}
      {...restProps}
      ref={ref}
      options={options}
      onFocus={evt => {
        if (trigger === 'onFocus' && options.length <= 0) {
          getMetaData();
        }
        onFocus?.(evt);
      }}
    />
  );
}

interface GenericMetaTypeCascader {
  (props: MetaTypeCascaderProps, ref: React.ForwardedRef<CascaderRef>): JSX.Element;
  displayName: string;
}

export const MetaTypeCascader = React.forwardRef(_MetaTypeCascader) as GenericMetaTypeCascader;

MetaTypeCascader.displayName = 'MetaTypeCascader';
