import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type FloorTypeSelectProps = Omit<SelectProps, 'options'>;

const options: SelectProps['options'] = [
  { value: 'IT_FLOOR', label: 'IT楼层' },
  { value: 'DEVICE_FLOOR', label: '设施楼层' },
  { value: 'COMPLEX_FLOOR', label: '综合楼层' },
  { value: 'COOLING_FLOOR', label: '冷塔楼层' },
  { value: 'TOP_FLOOR', label: '顶层' },
  { value: 'BASEMENT_FLOOR', label: '地下楼层' },
  { value: 'OTHER', label: '其他' },
];

export const FloorTypeSelect = React.forwardRef(
  (props: SelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select {...props} ref={ref} options={options} />;
  }
);

FloorTypeSelect.displayName = 'FloorTypeSelect';
