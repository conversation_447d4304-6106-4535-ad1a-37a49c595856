import debounce from 'lodash.debounce';
import React, { useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import shallowequal from 'shallowequal';

import { Input, type InputProps } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import type { DefaultOptionType, RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import {
  type DeviceInfo,
  fetchDeviceByKeyword,
} from '@manyun/resource-hub.service.fetch-device-by-keyword';
import { type RoomInfo, fetchRoomList } from '@manyun/resource-hub.service.fetch-room-list';
import type { DeviceType } from '@manyun/resource-hub.state.device-types';
import { getDeviceTypesAction, selectDeviceTypes } from '@manyun/resource-hub.state.device-types';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

export type Type = 'DEVICE' | 'ROOM' | 'OTHER';
export type DeviceOrSpaceSelectProps = {
  idcTag: string;
  blockTag?: string; // 查询设备时才必传
  type?: Type;
  disabled?: boolean;
  availableOptions?: string[];
  deviceGuidList?: string[];
  assertStatusList?: string[]; // 资产状态
  operationStatus?: string; // 启用状态
  value?: string | string[] | number | number[];
  multiple?: boolean;
  roomGuids?: string[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  onChange?: ((value: any, option?: DefaultOptionType | DefaultOptionType[]) => void) | undefined;
} & SelectProps;

export const DeviceOrSpaceSelect = React.forwardRef(
  (props: DeviceOrSpaceSelectProps, ref?: React.Ref<RefSelectProps | InputProps>) => {
    const {
      idcTag,
      blockTag,
      type,
      disabled,
      availableOptions,
      value,
      multiple,
      deviceGuidList,
      assertStatusList,
      operationStatus,
      roomGuids,
      onChange,
      ...resp
    } = props;
    const [fetching, setFetching] = useState(false);
    const [dataSource, setDataSource] = useState<RoomInfo[] | DeviceInfo[]>([]);
    const dispatch = useDispatch();
    React.useEffect(() => {
      dispatch(getDeviceTypesAction());
    }, [dispatch]);

    const { entities: deviceTypeEntities } = useSelector(selectDeviceTypes, (left, right) =>
      shallowequal(left.entities, right.entities)
    );
    useEffect(() => {
      setDataSource([]);
    }, [idcTag, blockTag, type]);

    const debounceFetcher = useMemo(() => {
      const loadOptions = async (value: string) => {
        setDataSource([]);
        setFetching(true);

        const keyword = value.trim();
        if (!keyword) {
          setFetching(false);
          return;
        }
        let dataSource: RoomInfo[] | DeviceInfo[] = [];
        const extraQ = { idcTag, blockTag, content: keyword, pageNum: 1, pageSize: 300 };
        if (type === 'DEVICE') {
          if (!blockTag) {
            message.warn('缺少：blockTag');
            return;
          }
          const { data, error } = await fetchDeviceByKeyword({
            ...extraQ,
            blockTag: blockTag!,
            deviceGuidList,
            assertStatusList,
            operationStatus,
          });
          if (error) {
            return;
          }
          if (availableOptions) {
            dataSource = data.data.filter(item => availableOptions?.includes(item.guid));
          } else {
            dataSource = data.data;
          }
        }
        if (type === 'ROOM') {
          const { data, error } = await fetchRoomList(extraQ);
          if (error) {
            return;
          }
          dataSource = data.data;

          if (availableOptions) {
            dataSource = dataSource.filter(item => availableOptions?.includes(item.guid));
          }
          if (roomGuids) {
            dataSource = dataSource.filter(item => roomGuids?.includes(item.guid));
          }
        }
        setFetching(false);
        setDataSource(dataSource);
      };

      return debounce(loadOptions, 500);
    }, [
      idcTag,
      blockTag,
      type,
      deviceGuidList,
      assertStatusList,
      operationStatus,
      roomGuids,
      availableOptions,
    ]);
    if (type === 'OTHER') {
      // @ts-ignore ref类型报错，但是 InputRef 类型没有从Input组件中导出，所以暂时无法解决
      return <Input ref={ref} value={value as string} disabled={disabled} onChange={onChange} />;
    }
    const getPlaceholder = () => {
      if (type === 'DEVICE') {
        return '支持搜索格式：设备名称（空格）三级分类';
      }
      if (type === 'ROOM') {
        return '支持搜索格式：包间编号（空格）包间名称';
      }
      return null;
    };
    return (
      <Select
        ref={ref as React.Ref<RefSelectProps>}
        loading={fetching}
        style={{ width: '100%' }}
        mode={multiple ? 'multiple' : undefined}
        disabled={disabled}
        filterOption={false}
        showSearch
        value={value}
        placeholder={getPlaceholder()}
        optionLabelProp="label"
        onSearch={debounceFetcher}
        onChange={onChange}
        {...resp}
      >
        {getRichOptions(dataSource, deviceTypeEntities, type)}
      </Select>
    );
  }
);

export default DeviceOrSpaceSelect;
DeviceOrSpaceSelect.displayName = 'DeviceOrSpaceSelect';

function getRichOptions(
  dataSource: DeviceInfo[] | RoomInfo[],
  entities: Record<string, DeviceType>,
  type?: Type
) {
  if (!Array.isArray(dataSource)) {
    return;
  }

  if (type === 'DEVICE') {
    return dataSource.map(item => {
      const { name, guid, vendor, productModel, spaceGuid, deviceType } = item as DeviceInfo;
      return (
        <Select.Option
          key={guid}
          value={guid}
          label={`${name} ${entities[deviceType]?.metaName ?? '未知'} (${
            spaceGuid?.roomTag || ''
          })`}
        >
          <Space className="demo-option-label-item" size="small" direction="vertical">
            <div>
              {name}/<DeviceTypeText code={deviceType} />
            </div>
            <div>{`${spaceGuid?.roomTag}/${vendor}/${productModel}`}</div>
          </Space>
        </Select.Option>
      );
    });
  }

  if (type === 'ROOM') {
    return dataSource.map(item => {
      const { tag, guid, roomTypeName } = item as RoomInfo;

      const text = <span>{`${tag}${roomTypeName ? '(' + roomTypeName + ')' : ''}`}</span>;

      return (
        <Select.Option
          key={guid}
          value={guid}
          label={`${tag}${roomTypeName ? '(' + roomTypeName + ')' : ''}`}
        >
          {text}
        </Select.Option>
      );
    });
  }

  return;
}
