import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Radio } from '@manyun/base-ui.ui.radio';
import type { RadioGroupProps } from '@manyun/base-ui.ui.radio';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { useSpaces } from '@manyun/resource-hub.gql.client.resources';

export type MyBlocksProps = {
  idc: string;
  className?: string;
  includeVirtual?: boolean;
} & Omit<RadioGroupProps, 'options'>;

export function MyBlocks({
  className,
  idc,
  optionType = 'button',
  includeVirtual = false,
  ...restProps
}: MyBlocksProps) {
  const { data } = useSpaces({
    variables: {
      nodeTypes: ['IDC', 'BLOCK'],
      authorizedOnly: true,
      includeVirtualBlocks: includeVirtual,
      idc,
    },
    fetchPolicy: 'no-cache',
  });

  const options = useDeepCompareMemo(() => {
    if (data?.spaces?.length) {
      return (
        data.spaces[0]?.children?.map(block => ({
          label: block.label,
          value: block.value,
        })) ?? []
      );
    }
    return [];
  }, [data?.spaces]);

  return (
    <div className={className}>
      <Radio.Group optionType={optionType} {...restProps}>
        {options.map(item => (
          <Tooltip key={item.value} title={item.label}>
            <Radio value={item.value}>
              {item.label.length > 5 ? item.label.slice(0, 5) + '...' : item.label}
            </Radio>
          </Tooltip>
        ))}
      </Radio.Group>
    </div>
  );
}
