import React from 'react';

import type { FloorType } from '@manyun/resource-hub.model.floor';

export type FloorTypeTextProps = {
  code: FloorType;
};

const floorTypeMap = {
  IT_FLOOR: 'IT楼层',
  DEVICE_FLOOR: '设施楼层',
  COMPLEX_FLOOR: '综合楼层',
  COOLING_FLOOR: '冷塔楼层',
  TOP_FLOOR: '顶层',
  BASEMENT_FLOOR: '地下楼层',
  OTHER: '其他',
};

export function FloorTypeText({ code }: FloorTypeTextProps) {
  return <>{floorTypeMap[code]}</>;
}
