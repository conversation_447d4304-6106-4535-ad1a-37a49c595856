import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type PowerModelSelectProps = Omit<SelectProps, 'options'>;

const options: SelectProps['options'] = [
  { value: 'UPS+UPS', label: 'UPS+UPS' },
  { value: 'HVDC+HVDC', label: 'HVDC+HVDC' },
  { value: 'LVDC+LVDC', label: 'LVDC+LVDC' },
  { value: 'UPS+AC', label: 'UPS+AC' },
  { value: 'HVDC+AC', label: 'HVDC+AC' },
  { value: 'LVDC+AC', label: 'LVDC+AC' },
  { value: 'AC+AC', label: 'AC+AC' },
  { value: 'UPS+无供电', label: 'UPS+无供电' },
  { value: 'HVDC+无供电', label: 'HVDC+无供电' },
  { value: 'LVDC+无供电', label: 'LVDC+无供电' },
  { value: '无供电+无供电', label: '无供电+无供电' },
];

export const PowerModelSelect = React.forwardRef(
  (props: PowerModelSelectProps, ref: React.Ref<RefSelectProps>) => {
    return <Select {...props} ref={ref} options={options} />;
  }
);

PowerModelSelect.displayName = 'PowerModelSelect';
