import { CloseSquareOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { DeleteConfirm } from '@manyun/base-ui.ui.delete-confirm';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import type { MetaSelectData } from '@manyun/resource-hub.model.metadata';

export type MetaDataItemProps = {
  metaItem: MetaSelectData;
  maxLength: number;
  metaType?: MetaType;
  cursor?: string;
  onClick?: (metaItem?: MetaSelectData) => void;
  onDelete: (id: number, callback: (result?: boolean) => void) => void;
  onEdit: (id: number, newMetaName: string, callback: (result: boolean) => void) => void;
  isEditing: boolean;
  setIsEditing: (value: boolean) => void;
  parentCode?: string | null;
  editable?: boolean;
  deletable?: boolean;
  permissions?: boolean;
  selectComponentRender?: (metaId: number) => React.ReactNode;
};

export function MetaDataItem({
  metaItem,
  metaType,
  cursor,
  maxLength,
  onClick,
  onDelete,
  onEdit,
  setIsEditing,
  isEditing,
  selectComponentRender,
  parentCode,
  editable = false,
  deletable = false,
}: MetaDataItemProps) {
  const dispatch = useDispatch();
  const [editItemKey, setEditItemKey] = useState<number | null>();
  const [newMetaName, setNewMetaName] = useState<string | undefined>(metaItem.name);
  const updateSyncCommonData = (metaType?: MetaType) => {
    if (!metaType) {
      return;
    }
    if (metaType === MetaType.ROOM_TYPE) {
      dispatch(syncCommonDataAction({ strategy: { roomTypes: 'FORCED' } }));
    }
    if (metaType === MetaType.CHANGE) {
      dispatch(syncCommonDataAction({ strategy: { changeTypes: 'FORCED' } }));
    }

    if (metaType === MetaType.DEVICE_GENERAL) {
      dispatch(syncCommonDataAction({ strategy: { ticketTypes: 'FORCED' } }));
    }
    if (metaType === MetaType.VISITOR) {
      dispatch(syncCommonDataAction({ strategy: { ticketTypes: 'FORCED' } }));
    }
    if (
      metaType === MetaType.ACCESS_CARD_SECOND_CATEGORY ||
      metaType === MetaType.ACCESS_CARD_TOP_CATEGORY
    ) {
      dispatch(syncCommonDataAction({ strategy: { accessCardTypes: 'FORCED' } }));
    }
    if (metaType === MetaType.EVENT_TOP_CATEGORY || metaType === MetaType.EVENT_SECOND_CATEGORY) {
      dispatch(syncCommonDataAction({ strategy: { eventTypes: 'FORCED' } }));
    }
  };
  useEffect(() => {
    setNewMetaName(metaItem.name);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [editItemKey]);

  return (
    <>
      {metaItem.id !== editItemKey ? (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Typography.Text
            ellipsis
            style={{
              cursor,
              color:
                parentCode === metaItem?.value?.toString()
                  ? `var(--${prefixCls}-primary-color)`
                  : 'inherit',
            }}
            onClick={() => {
              if (onClick) {
                onClick(metaItem);
              }
            }}
          >
            {metaItem.name}
          </Typography.Text>

          {isEditing !== true && (
            <Space>
              {selectComponentRender && selectComponentRender(metaItem.id)}
              {/* permisson = 2 不可编辑 */}
              <Button
                style={{
                  padding: 0,
                  margin: 0,
                }}
                type="text"
                disabled={!editable || metaItem?.permissions === 2}
              >
                <EditOutlined
                  onClick={() => {
                    setEditItemKey(metaItem.id);
                    setIsEditing(true);
                  }}
                />
              </Button>
              {/* permisson = 1 或者 permisson = 2 不可删除 */}
              {deletable && (
                <DeleteConfirm
                  variant="popconfirm"
                  targetName=""
                  title="您将删除此数据"
                  onOk={() => {
                    if (onDelete && typeof onDelete === 'function') {
                      onDelete(metaItem.id, (result?: boolean) => {
                        if (result) {
                          updateSyncCommonData(metaType);
                        }
                      });
                    }
                    return Promise.resolve(true);
                  }}
                >
                  <Button
                    style={{
                      padding: 0,
                      margin: 0,
                    }}
                    type="text"
                    disabled={metaItem?.permissions === 1 || metaItem?.permissions === 2}
                  >
                    <DeleteOutlined />
                  </Button>
                </DeleteConfirm>
              )}
            </Space>
          )}
        </div>
      ) : (
        <div
          style={{
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Input
            value={newMetaName}
            style={{ width: 96 }}
            maxLength={maxLength}
            onChange={e => {
              setNewMetaName(e.target.value.trim());
            }}
          />
          <Space>
            <CloseSquareOutlined
              onClick={() => {
                setEditItemKey(null);
                setIsEditing(false);
              }}
            />
            <SaveOutlined
              onClick={() => {
                if (!newMetaName) {
                  message.error('元数据名称不能为空');
                  return;
                }
                onEdit(metaItem.id, newMetaName, (result: boolean) => {
                  if (result) {
                    setEditItemKey(null);
                    setIsEditing(false);
                    updateSyncCommonData(metaType);
                  }
                });
              }}
            />
          </Space>
        </div>
      )}
    </>
  );
}
