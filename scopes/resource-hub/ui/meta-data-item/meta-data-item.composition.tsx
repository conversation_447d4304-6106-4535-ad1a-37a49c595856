import React from 'react';

import { MetaDataItem } from './meta-data-item';

export const BasicMetaDataItem = () => {
  return (
    <MetaDataItem
      metaItem={{
        id: 849,
        label: 'I1',
        name: 'I1',
        parentCode: '0',
        type: 'EVENT_LEVEL',
        value: '36',
      }}
      maxLength={20}
      onDelete={() => {}}
      isEditing={false}
      onEdit={() => {}}
      setIsEditing={() => {}}
    />
  );
};
