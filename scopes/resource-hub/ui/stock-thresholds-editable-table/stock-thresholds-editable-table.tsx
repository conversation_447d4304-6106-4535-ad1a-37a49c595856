// @ts-nocheck pending refactor
import React, { useEffect, useState } from 'react';

import { nanoid } from 'nanoid';

import { Button } from '@manyun/base-ui.ui.button';
import { EditableTable } from '@manyun/base-ui.ui.editable-table';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';

import { VendorModelCascader } from '@manyun/crm.ui.vendor-model-cascader';
import { deleteStockThreshold } from '@manyun/resource-hub.service.delete-stock-threshold';
import { fetchStockThresholds } from '@manyun/resource-hub.service.fetch-stock-thresholds';
import type { UpdateApiQ } from '@manyun/resource-hub.service.mutate-stock-threshold';
import { mutateStockThreshold } from '@manyun/resource-hub.service.mutate-stock-threshold';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

export type StockThresholdsEditableTableProps = {
  /**
   * 三级分类code
   */
  deviceType: string;
  numbered?: boolean;
};
export type DataType = {
  blockGuid: string;
  vendorProductModel: unknown[];
  vendor?: string;
  productModel?: string;
  level: number;
  id: string | number;
};

export function StockThresholdsEditableTable({
  deviceType,
  numbered,
}: StockThresholdsEditableTableProps) {
  const [dataSource, setData] = useState<DataType[]>([]);
  const [editingRowKey, setEditingRowKey] = useState<string | null>(null);
  const [deleteByCancel, setDeleteByCancel] = useState(false);

  const [newAddFlag, setNewAddFlag] = useState(true);
  // 测试 service
  useEffect(() => {
    const fetchData = async () => {
      const res = await fetchStockThresholds({ deviceType });
      setData(
        res.data.data?.map(item => ({
          ...item,
          vendorProductModel: [
            item.vendor?.length !== 0 ? item.vendor : '--',
            item.productModel?.length !== 0 ? item.productModel : '--',
          ],
        }))
      );
    };
    fetchData();
  }, [deviceType]);
  // 新建\编辑水位阈值配置
  const mutateThresholds = async (allData: DataType[], newData: UpdateApiQ) => {
    const { error, data } = await mutateStockThreshold(newData);
    if (error?.code || !data) {
      message.error(error?.message);
    } else {
      // 新建的情况
      if (!('id' in newData)) {
        allData[0].id = data;
      }
      setData(allData);
      setEditingRowKey(null);
      setDeleteByCancel(false);
      message.success('保存成功');
    }
  };
  // 删除水位阈值配置
  const deleteThresholds = async (alldata: DataType[], id: number) => {
    const { error, data } = await deleteStockThreshold({ id });
    if (error?.code || !data) {
      message.error(error?.message);
    } else {
      setData(alldata);
      if (id.toString() === editingRowKey) {
        setEditingRowKey(null);
      }
      message.success('删除成功');
    }
  };
  // 确保位置、位置+厂商型号 唯一性
  const isLocationUnique = (
    currentId: string | null,
    currentLocation: string,
    currentVendorProductModel: unknown[]
  ) => {
    // 如果不存在厂商型号
    if (!currentVendorProductModel?.length) {
      for (const item of dataSource) {
        if (
          item.blockGuid === currentLocation &&
          (!item.vendorProductModel?.length || item.vendorProductModel[0] === '--') &&
          currentId &&
          item.id !== currentId
        ) {
          return false;
        }
      }
      return true;
    }
    // 如果存在厂商型号
    else {
      for (const item of dataSource) {
        // 同位置
        if (
          item.blockGuid === currentLocation &&
          currentId &&
          item.id !== currentId &&
          currentVendorProductModel.toString() === item.vendorProductModel.toString()
        ) {
          // 同厂商型号，只能存在一个

          return false;
        }
      }

      return true;
    }
  };

  // 限制数字输入框只能输入整数
  const limitNumberFormatter = (value: unknown) => {
    if (typeof value === 'string') {
      return !isNaN(Number(value)) ? value.replace(/^(0+)|[^\d]/g, '') : '';
    } else if (typeof value === 'number') {
      return !isNaN(value) ? String(value).replace(/^(0+)|[^\d]/g, '') : '';
    } else {
      return '';
    }
  };
  const limitNumberParse = (value: unknown) => {
    if (typeof value === 'string') {
      return !isNaN(Number(value)) ? parseInt(value.replace(/^(0+)|[^\d]/g, '')) : 0;
    } else if (typeof value === 'number') {
      return !isNaN(value) ? parseInt(String(value).replace(/^(0+)|[^\d]/g, '')) : 0;
    } else {
      return 0;
    }
  };

  const getValidVendorProductModelDataForBackEnd = (vendorProductModel: unknown[] | undefined) => {
    if (vendorProductModel === undefined) {
      return ['', ''];
    }
    let stringFlag = false;
    vendorProductModel.forEach(item => {
      if (item === '--') {
        stringFlag = true;
      }
    });

    if (stringFlag) {
      return ['', ''];
    } else {
      return vendorProductModel;
    }
  };

  const getValidVendorProductModelDataForDisplay = (vendorProductModel: unknown[] | undefined) => {
    return (
      <span>{`${vendorProductModel?.length ? vendorProductModel[0] : '--'}、${
        vendorProductModel?.length ? vendorProductModel[1] : '--'
      }`}</span>
    );
  };

  return (
    <div>
      {newAddFlag && (
        <Button
          type="primary"
          style={{ marginBottom: '10px' }}
          onClick={() => {
            const id = nanoid();
            setData([
              {
                blockGuid: '',
                vendorProductModel: [],
                level: 0,
                id,
              },
              ...dataSource,
            ]);

            setEditingRowKey(id);
            setDeleteByCancel(true);
            setNewAddFlag(false);
          }}
        >
          添加
        </Button>
      )}
      <EditableTable
        size="small"
        rowKey="id"
        showActionsColumn
        mergeProp="id"
        columns={[
          {
            width: 200,
            title: '位置',
            dataIndex: 'blockGuid',
            ellipsis: true,
            editable: true,
            mergeStrategy: {
              rows: true,
            },
            editingCtrl: (
              <LocationTreeSelect
                style={{ width: '200px' }}
                nodeTypes={['IDC', 'BLOCK']}
                disabledTypes={['IDC']}
                authorizedOnly
              />
            ),

            formItemProps: {
              rules: [{ required: true, message: '请选择位置' }],
            },
            render: (_, { blockGuid }) => <span>{blockGuid}</span>,
          },
          {
            width: 200,
            title: '厂商、型号',
            dataIndex: 'vendorProductModel',
            ellipsis: true,
            editable: true,
            mergeStrategy: {
              rows: true,
            },

            editingCtrl: <VendorModelCascader numbered={numbered} deviceType={deviceType} />,

            render: (_, { vendorProductModel }) =>
              getValidVendorProductModelDataForDisplay(vendorProductModel),
          },

          {
            width: 200,
            title: '水位阈值',
            dataIndex: 'level',
            ellipsis: true,
            editable: true,
            mergeStrategy: {
              rows: true,
            },
            editingCtrl: (
              <InputNumber min={1} formatter={limitNumberFormatter} parser={limitNumberParse} />
            ),
            formItemProps: {
              dependencies: ['vendorProductModel', 'blockGuid'],
              rules: [
                ({ getFieldValue }) => ({
                  validator: (_, value) => {
                    if (!getFieldValue('blockGuid')) {
                      return Promise.reject('请先选择位置');
                    } else if (
                      !isLocationUnique(
                        editingRowKey,
                        getFieldValue('blockGuid'),
                        getFieldValue('vendorProductModel')
                      )
                    ) {
                      return Promise.reject('配置已存在');
                    } else if (value <= 0 || !value) {
                      return Promise.reject('请输入水位阈值');
                    } else {
                      return Promise.resolve();
                    }
                  },
                }),
              ],
            },
          },
        ]}
        dataSource={dataSource}
        editingRowKey={editingRowKey}
        canDelete={() => true}
        onEdit={(rowKey: string) => {
          setEditingRowKey(rowKey);
        }}
        onCancel={() => {
          if (deleteByCancel) {
            setData(prevData => {
              const nextData = [...prevData];
              const idx = nextData.findIndex(record => record.id === editingRowKey);
              if (idx > -1) {
                nextData.splice(idx, 1);
              }

              return nextData;
            });
          }
          setEditingRowKey(null);
          setDeleteByCancel(false);
          setNewAddFlag(true);
        }}
        onSave={(__, data: DataType[]) => {
          const newData = editingRowKey ? data.find(({ id }) => id === editingRowKey) : data[0];

          const paramData: {
            id?: string | number;
            blockGuid: string;
            code: string;
            level: number;
            productModel: string;
            vendor: string;
            deviceNumType: string;
          } = {
            blockGuid: newData!.blockGuid,
            code: deviceType,
            level: newData!.level,
            productModel: getValidVendorProductModelDataForBackEnd(newData?.vendorProductModel)[1],
            vendor: getValidVendorProductModelDataForBackEnd(newData?.vendorProductModel)[0],
            deviceNumType: numbered ? 'DEVICE' : 'SPARE',
          };
          // 通过id是否是
          const isnum = /^\d+$/.test(newData!.id.toString());
          if (isnum) {
            paramData['id'] = newData!.id;
          }
          mutateThresholds(data, paramData);
          setNewAddFlag(true);
        }}
        onDelete={(rowKey: string, data: DataType[]) => {
          deleteThresholds(data, parseInt(rowKey));
        }}
      />
    </div>
  );
}
