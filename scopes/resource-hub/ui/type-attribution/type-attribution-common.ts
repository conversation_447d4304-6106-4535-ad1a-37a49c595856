import type { BackendTypeAttributionType } from '@manyun/resource-hub.service.fetch-device-type';

export const typeAttributionMapper = {
  DEVICE: '基础设施',
  IT: 'IT设施',
};

export const typeAttributionOptions: { label: string; value: BackendTypeAttributionType }[] = [
  {
    label: typeAttributionMapper.DEVICE,
    value: 'DEVICE',
  },
  {
    label: typeAttributionMapper.IT,
    value: 'IT',
  },
];
