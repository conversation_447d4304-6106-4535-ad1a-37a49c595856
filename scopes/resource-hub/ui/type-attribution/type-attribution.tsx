import React from 'react';

import type { TextProps } from '@manyun/base-ui.ui.typography';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { BackendTypeAttributionType } from '@manyun/resource-hub.service.fetch-device-type';

import { typeAttributionMapper } from './type-attribution-common';

export type TypeAttributionTextProps = {
  value: BackendTypeAttributionType;
} & TextProps;

export const TypeAttributionText = ({
  value,
  ...typographyTextProps
}: TypeAttributionTextProps) => {
  return (
    <Typography.Text {...typographyTextProps}>
      {typeAttributionMapper[value] ?? '--'}
    </Typography.Text>
  );
};
