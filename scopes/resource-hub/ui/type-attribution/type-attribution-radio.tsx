import React from 'react';

import { Radio } from '@manyun/base-ui.ui.radio';
import type { RadioProps } from '@manyun/base-ui.ui.radio';

import { typeAttributionOptions } from './type-attribution-common';

export type TypeAttributionRadioProps = Omit<RadioProps, 'options'>;

/**
 * 类型归属单选radio
 */
export const TypeAttributionRadio = React.forwardRef(
  (props: TypeAttributionRadioProps, ref: React.ForwardedRef<HTMLDivElement>) => {
    return <Radio.Group ref={ref} {...props} options={typeAttributionOptions} />;
  }
);

TypeAttributionRadio.displayName = 'AlarmTypeRadio';
