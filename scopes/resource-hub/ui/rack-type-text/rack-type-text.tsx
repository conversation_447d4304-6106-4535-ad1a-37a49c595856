import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { fetchCabinetType } from '@manyun/resource-hub.service.fetch-cabinet-type';
import type { GridTypeModel, RackType } from '@manyun/resource-hub.service.fetch-cabinet-type';

export type RackTypeTextProps = {
  rackType: RackType;
};

export function RackTypeText({ rackType }: RackTypeTextProps) {
  const [gridTypeData, setGridTypeData] = useState<GridTypeModel>();

  useEffect(() => {
    (async () => {
      if (!gridTypeData) {
        const { data, error } = await fetchCabinetType();
        if (error) {
          message.error(error.message);
        }
        if (data) {
          setGridTypeData(data);
        }
      }
    })();
  }, [gridTypeData]);

  return <>{gridTypeData && gridTypeData[rackType]}</>;
}
