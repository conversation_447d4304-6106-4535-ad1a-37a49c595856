import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { DataType } from '@manyun/monitoring.model.point';

export type PointTypeSelectProps = Omit<SelectProps, 'options'> & {
  /** 需要隐藏的测点类型 */
  hiddenTypes?: DataType[];
};

type PointTypeOption = {
  value: DataType;
  label: string;
};

const allOptions: PointTypeOption[] = [
  { value: 'AI', label: '模拟量读点' },
  { value: 'ALARM', label: '告警点' },
  { value: 'AO', label: '模拟量写点' },
  { value: 'DI', label: '状态量读点' },
  { value: 'DO', label: '状态量写点' },
];

export const PointTypeSelect = React.forwardRef(
  ({ hiddenTypes = [], ...rest }: PointTypeSelectProps, ref: React.Ref<RefSelectProps>) => {
    return (
      <Select
        ref={ref}
        style={{ width: 200 }}
        {...rest}
        options={allOptions.filter(option => !hiddenTypes.includes(option.value))}
      />
    );
  }
);

PointTypeSelect.displayName = 'PointTypeSelect';
