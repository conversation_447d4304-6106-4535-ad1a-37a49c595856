import debounce from 'lodash.debounce';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Result } from '@manyun/base-ui.ui.result';
import { Space } from '@manyun/base-ui.ui.space';
import { useConfigUtil } from '@manyun/dc-brain.context.configs';
import { getCitiesMapper, syncCommonDataAction } from '@manyun/dc-brain.state.common';
import type { Idc } from '@manyun/resource-hub.model.idc';
import { fetchIdc } from '@manyun/resource-hub.service.fetch-idc';
import { fetchAppletQrCode } from '@manyun/sentry.service.fetch-applet-qr-code';
import type { RequestError } from '@manyun/service.request';

export type IdcInfosProps = {
  guid?: string;
  instance?: Idc;
};

function InternalIdcInfos({ instance }: Pick<Required<IdcInfosProps>, 'instance'>) {
  const citiesMapper = useSelector(getCitiesMapper);
  const [configUtil] = useConfigUtil();
  const ticketScopeCommonConfigs = configUtil.getScopeCommonConfigs('ticket');
  // @ts-ignore
  const dockingStation = ticketScopeCommonConfigs?.entryOfPersons?.features?.dockingStation;
  const onDownloads = debounce(async () => {
    if (!instance.guid) {
      message.error('无机房guid');
      return;
    }
    let objectUrl;
    const pathUrl = window.location.hostname;
    try {
      const { data, error } = await fetchAppletQrCode({
        page: 'pages/scanVisitorRegister/index',
        scene: `idcTag=${instance.guid}`,
        envVersion: pathUrl.includes('inc')
          ? 'develop'
          : pathUrl.includes('local')
            ? 'trial'
            : undefined,
      });
      if (error) {
        console.error('Error initiating QR Code download ', error);
      }

      if (data && typeof data === 'string' && data.length > 0) {
        const base64String = data;

        // 提取 Base64 内容和 MIME 类型
        // 如果 Base64 字符串带了 "data:image/jpeg;base64," 前缀，需要处理掉
        // /9j/ 开头通常是 JPEG
        const mimeType = 'image/jpeg';
        const base64Content = base64String.includes(',')
          ? base64String.split(',')[1]
          : base64String;

        // 将 Base64 字符串解码为二进制数据 (Blob)
        const byteCharacters = atob(base64Content);
        const byteNumbers = new Array(byteCharacters.length);
        for (let i = 0; i < byteCharacters.length; i++) {
          byteNumbers[i] = byteCharacters.charCodeAt(i);
        }
        const byteArray = new Uint8Array(byteNumbers);
        const blob = new Blob([byteArray], { type: mimeType });

        // 创建一个临时的 URL (对象 URL)
        objectUrl = URL.createObjectURL(blob);

        const link = document.createElement('a');
        link.href = objectUrl; // 现在这里是一个有效的 URL
        link.download = `${instance.guid}_qrcode.jpeg`; // 建议下载的文件名
        link.target = '_blank';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error) {
      message.error('下载二维码失败，请稍后再试。');
      console.error('Error initiating QR Code download on PC:', error);
    } finally {
      message.destroy(); // 移除加载提示
      if (objectUrl) {
        URL.revokeObjectURL(objectUrl);
      }
    }
  }, 500);
  return (
    <Descriptions column={4}>
      <Descriptions.Item label="机房名称">{instance.name}</Descriptions.Item>
      <Descriptions.Item label="机房编号">{instance.guid}</Descriptions.Item>
      <Descriptions.Item label="机房类别">{instance.typeName}</Descriptions.Item>
      <Descriptions.Item label="国家">
        {citiesMapper?.[instance.nationCode]?.label}
      </Descriptions.Item>
      <Descriptions.Item label="区域">
        {citiesMapper?.[instance.regionCode]?.label}
      </Descriptions.Item>
      <Descriptions.Item label="省">
        {citiesMapper?.[instance.provinceCode]?.label}
      </Descriptions.Item>
      <Descriptions.Item label="城市">{citiesMapper?.[instance.cityCode]?.label}</Descriptions.Item>
      <Descriptions.Item label="区">
        {citiesMapper?.[instance.districtCode]?.label}
      </Descriptions.Item>
      <Descriptions.Item span={2} label="地址">
        {instance.address}
      </Descriptions.Item>
      <Descriptions.Item label="建设日期">{instance.getFormattedConstructedAt()}</Descriptions.Item>
      <Descriptions.Item label="投产日期">
        {instance.getFormattedPutIntoProductionAt()}
      </Descriptions.Item>
      <Descriptions.Item label="状态">{instance.status.name}</Descriptions.Item>
      <Descriptions.Item label="责任人">
        <UserLink {...instance.owner} />
      </Descriptions.Item>
      <Descriptions.Item label="备用责任人">
        {instance.backupOwners.length > 0 ? (
          <Space size={0} split={<Divider type="vertical" spaceSize="mini" emphasis />}>
            {instance.backupOwners.map(user => (
              <UserLink key={user.id} {...user} />
            ))}
          </Space>
        ) : (
          '--'
        )}
      </Descriptions.Item>
      {Array.isArray(instance.specDataList) &&
        instance.specDataList.map(spec => {
          let value = spec.specValue;
          if (spec.specValue === 'AUTH_PERIOD') {
            value = '授权有效期内';
          }
          if (spec.specValue === 'REAL') {
            value = '实时';
          }
          return (
            <Descriptions.Item key={spec.specCode} label={spec.specName}>
              {`${value}${spec.specUnit ?? ''}`}
            </Descriptions.Item>
          );
        })}
      {!dockingStation && (
        <Descriptions.Item label="访客登记二维码">
          <Button style={{ position: 'relative', top: '-5px' }} type="link" onClick={onDownloads}>
            下载
          </Button>
        </Descriptions.Item>
      )}
    </Descriptions>
  );
}

export function IdcInfos({ guid, instance }: IdcInfosProps) {
  const [_instance, setInstance] = React.useState<Idc | undefined>(instance);
  const [error, setError] = React.useState<RequestError | undefined>();

  React.useEffect(() => {
    if (guid) {
      fetchIdc({ idcTag: guid }).then(({ error, data }) => {
        if (error) {
          setError(error);
          return;
        }
        if (!data) {
          setError({ message: `找不到机房(${guid})!` });
          return;
        }
        setInstance(data);
        setError(undefined);
      });
    }
  }, [guid]);

  React.useEffect(() => {
    setInstance(instance);
  }, [instance]);

  const dispatch = useDispatch();
  React.useEffect(() => {
    dispatch(syncCommonDataAction({ strategy: { citiesTree: 'IF_NULL' } }));
  }, [dispatch]);

  if (error) {
    return <Result status="warning" title={error.message} />;
  }

  if (!_instance) {
    return null;
  }

  return <InternalIdcInfos instance={_instance} />;
}
