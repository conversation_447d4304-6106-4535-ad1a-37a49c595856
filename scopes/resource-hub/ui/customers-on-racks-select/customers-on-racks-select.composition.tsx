import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Divider } from '@manyun/base-ui.ui.divider';

import { webRequest } from '@manyun/service.request';

import { CustomersOnRacksSelect } from './customers-on-racks-select';

webRequest.axiosInstance.defaults.baseURL = 'http://yapi.manyun-local.com';
webRequest.mockOn();

export const BasicCustomersOnRacksSelect = () => {
  return (
    <ConfigProvider>
      <Divider orientation="left">Default</Divider>
      <CustomersOnRacksSelect<string>
        style={{ width: 200 }}
        onChange={(value, option) => {
          // eslint-disable-next-line no-console
          console.log('value: ', value, ' option: ', option);
        }}
      />
    </ConfigProvider>
  );
};

export const SelectMultipleCustomersOnRacksSelect = () => {
  return (
    <ConfigProvider>
      <Divider orientation="left">Multiple</Divider>
      <CustomersOnRacksSelect<string[]>
        style={{ width: 200 }}
        mode="multiple"
        onChange={(values, options) => {
          // eslint-disable-next-line no-console
          console.log('values: ', values, ' options: ', options);
        }}
      />
    </ConfigProvider>
  );
};
