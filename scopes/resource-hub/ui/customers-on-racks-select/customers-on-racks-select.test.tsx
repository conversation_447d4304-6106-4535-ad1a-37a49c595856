import React from 'react';

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicCustomersOnRacksSelect } from './customers-on-racks-select.composition';

it('should render with the correct options', async () => {
  render(<BasicCustomersOnRacksSelect />);
  const input = screen.getByRole('combobox');
  expect(input).toBeInTheDocument();
  userEvent.click(input);
  const option = await screen.findByText('Something Label');
  expect(option).toBeInTheDocument();
});
