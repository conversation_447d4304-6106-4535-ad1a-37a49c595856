import React from 'react';
import { useEffectOnce } from 'react-use';

import debounce from 'lodash.debounce';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { useLazyCustomersOnRacks } from '@manyun/resource-hub.gql.client.racks';
import type { CustomerOnRack } from '@manyun/resource-hub.gql.client.racks';

export type Option = CustomerOnRack;

export type CustomersOnRacksSelectProps<Value extends string | string[] = string> = {
  trigger?: 'onFocus' | 'onDidMount';
  onChange?: (value: Value, option: Value extends string ? Option : Option[]) => void;
} & Omit<SelectProps<Value, Option>, 'options' | 'onChange'>;

function _CustomersOnRacksSelect<Value extends string | string[] = string>(
  props: CustomersOnRacksSelectProps<Value>,
  ref?: React.ForwardedRef<RefSelectProps>
) {
  const { trigger = 'onDidMount', fieldNames, onFocus, onChange, ...restProps } = props;

  const [options, setOptions] = React.useState<Option[]>([]);
  const [getCustomersOnRacks] = useLazyCustomersOnRacks();
  const getOptions = React.useCallback(
    (customerName?: string) => {
      if (customerName) {
        getCustomersOnRacks({ variables: { name: customerName } }).then(({ error, data }) => {
          if (error) {
            message.error(error.message);
          }
          setOptions(data?.customersOnRacks ?? []);
        });
      } else {
        setOptions([]);
      }
    },
    [getCustomersOnRacks]
  );
  useEffectOnce(() => {
    if (trigger === 'onDidMount') {
      getOptions();
    }
  });
  const searchHandler = React.useMemo(() => debounce(getOptions, 200), [getOptions]);

  return (
    <Select<Value, Option>
      fieldNames={{
        label: 'name',
        value: 'code',
        ...fieldNames,
      }}
      optionFilterProp="name"
      placeholder="请输入公司名称"
      {...restProps}
      ref={ref}
      showSearch
      options={options}
      onFocus={evt => {
        if (trigger === 'onFocus' && options.length <= 0) {
          getOptions();
        }
        onFocus?.(evt);
      }}
      onSearch={searchHandler}
      onChange={(value, option) => {
        onChange?.(value, option as Value extends string ? Option : Option[]);
      }}
    />
  );
}

interface GenericCustomersOnRacksSelect {
  <Value extends string | string[] = string>(
    props: CustomersOnRacksSelectProps<Value>,
    ref: React.ForwardedRef<RefSelectProps>
  ): JSX.Element;
  displayName: string;
}

export const CustomersOnRacksSelect = React.forwardRef(
  _CustomersOnRacksSelect
) as GenericCustomersOnRacksSelect;

CustomersOnRacksSelect.displayName = 'CustomersOnRacksSelect';
