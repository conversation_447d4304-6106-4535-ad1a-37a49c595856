import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

const DIStateSplitSymbol = '=';

type RefSelectProps = {
  focus: () => void;
  blur: () => void;
  scrollTo: any;
};

export type DiPointStateSelectProps = {
  validLimits: string[];
  disabledLimits?: string[];
} & Omit<SelectProps, 'options'>;

export const DiPointStateSelect = React.forwardRef(
  (
    { validLimits, disabledLimits, ...props }: DiPointStateSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    return <Select ref={ref} options={generateOptions(validLimits, disabledLimits)} {...props} />;
  }
);

const generateOptions = (validLimits: string[], disabledLimits?: string[]) => {
  let options: { label: string; value: string }[] = [];
  if (validLimits.length) {
    options = validLimits.map(express => {
      const [value, label] = String(express).split(DIStateSplitSymbol);
      return { label, value, disabled: disabledLimits?.includes(express) };
    });
  }
  return options;
};
