import React from 'react';

import { wrapCompositions } from '@manyun/dc-brain.composition.compositions-wrapper';
import { createCompositionWrapper } from '@manyun/resource-hub.state.room-type';

import { RoomTypeText } from './room-type-text';

const StoreCompositionWrapper = createCompositionWrapper();
const CompositionWrapper = wrapCompositions([StoreCompositionWrapper]);

export const BasicRoomTypeText = () => {
  return (
    <CompositionWrapper>
      <RoomTypeText code="IT_ROOM" />
    </CompositionWrapper>
  );
};
