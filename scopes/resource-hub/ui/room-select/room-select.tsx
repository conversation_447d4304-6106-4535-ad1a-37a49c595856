import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { OptionProps, RefSelectProps, Select } from '@manyun/base-ui.ui.select';
import type { SelectProps, SelectValue } from '@manyun/base-ui.ui.select';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';

import { selectMyResources } from '@manyun/auth-hub.state.user';
import { selectSpaceEntities } from '@manyun/resource-hub.state.space';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { getRooms } from './room-select.utils';

type BlockSelectProps = {
  /**
   * 是否只展示当前用户有权限的空间数据
   */
  authorizedOnly?: boolean;
  idc?: string;
  block?: string;
} & TreeSelectProps<any>;

export type RoomSelectProps = ({ blockSelectProps: BlockSelectProps } | { blockGuid: string }) & {
  disabledRoomGuids?: string[];
} & SelectProps<any>;

const useSpaces = (blockSelectProps?: BlockSelectProps, blockGuid?: string) => {
  const { authorizedOnly, idc, block } = blockSelectProps || {};

  const allResources = useSelector(selectSpaceEntities());
  const myResources = useSelector(selectMyResources);

  const resources = useMemo(
    () => (authorizedOnly ? myResources : allResources),
    [myResources, allResources, authorizedOnly]
  );

  let defaultBlockGuid = idc && block ? `${idc}.${block}` : blockGuid;
  if (!defaultBlockGuid) {
    defaultBlockGuid = resources.find(
      r => (!idc || idc === r.parentCode) && r.type === 'BLOCK'
    )?.code;
  }

  return {
    defaultBlockGuid,
    space: resources,
  };
};

export const RoomSelect = React.forwardRef(
  (
    {
      //@ts-ignore
      blockSelectProps,
      //@ts-ignore
      blockGuid,
      disabledRoomGuids,
      onChange: onChangeForProps,
      ...roomProps
    }: RoomSelectProps,
    ref?: RefSelectProps
  ) => {
    let _defaultValue = roomProps.defaultValue;
    if ('value' in roomProps) {
      _defaultValue = roomProps.value;
    }
    const { defaultBlockGuid, space } = useSpaces(blockSelectProps, blockGuid);
    const [checkedBlockGuid, setCheckedBlockGuid] = useState<string | undefined>(defaultBlockGuid);
    const [checkedRoom, setCheckedRoom] = useState<SelectValue>(_defaultValue);

    useEffect(() => {
      setCheckedBlockGuid(defaultBlockGuid);
    }, [defaultBlockGuid]);

    const rooms = useMemo(
      () => getRooms({ blockGuid: checkedBlockGuid || defaultBlockGuid || '', space }),
      [checkedBlockGuid, defaultBlockGuid, space]
    );

    const handleRoomsChange = useCallback(
      (value: SelectValue, option?: OptionProps) => {
        setCheckedRoom(value);
        if (onChangeForProps) {
          onChangeForProps(value, option);
        }
      },
      [onChangeForProps]
    );

    useEffect(() => setCheckedRoom(roomProps.value), [roomProps.value]);

    if (!checkedBlockGuid && !defaultBlockGuid) {
      return null;
    }

    return (
      <div style={{ width: '100%', display: 'flex' }}>
        {blockSelectProps && (
          <LocationTreeSelect
            nodeTypes={['IDC', 'BLOCK']}
            value={checkedBlockGuid || defaultBlockGuid}
            onChange={value => {
              setCheckedBlockGuid(value);
              handleRoomsChange(roomProps.mode === 'multiple' ? [] : '');
            }}
            {...blockSelectProps}
            style={{ minWidth: 100, height: '100%', ...blockSelectProps.style }}
          />
        )}
        <Select
          ref={ref}
          value={checkedRoom}
          onChange={handleRoomsChange}
          {...roomProps}
          style={{ width: '100%', ...roomProps.style }}
        >
          {rooms.map(({ code, name }) => {
            return (
              <Select.Option value={code} key={code} disabled={disabledRoomGuids?.includes(code)}>
                {name}
              </Select.Option>
            );
          })}
        </Select>
      </div>
    );
  }
);
