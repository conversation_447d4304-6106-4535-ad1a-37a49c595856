---
description: 'A RoomSelect component.'
labels: ['select']
---

## RoomSelect

提供选择[当前登录用户有权限的]机房楼栋下除 `disabledRoomGuids` 外其余包间的能力

### RoomSelect Props

| 参数 | 说明 | 类型 | 默认值 | 版本 |  
| blockSelectProps | 楼栋选择下拉框参数配置；该参数不传时，不展示楼栋选择框 | BlockSelectProps | 无 | |  
| blockGuid | 楼栋 guid，仅展示该楼栋下的包间；当配置了`BlockSelectProps`时，该参数失效，否则该参数必传 | string | 无 | |  
| disabledRoomGuids | 禁用的包间 guid | string[] | false | 无 | |

其他参数，参考：  
manyun.base-ui/ui/select

### BlockSelectProps

| 参数 | 说明 | 类型 | 默认值 | 版本 |  
| authorizedOnly | 是否只展示当前用户有权限的空间数据 | boolean | 无 | |  
| idc | 机房；只展示该机房下的楼栋 | string | 无 | |  
| block | 楼栋；只展示该楼栋下的包间，不传则默认选中第一个楼栋 | string | 无 | |

其他参数，参考：  
resource-hub/ui/location-tree-select

### Component usage

```js
<RoomSelect
  blockSelectProps={{
    authorizedOnly: true,
    idc: 'EC06',
    block: 'A',
    style: { width: 150 },
  }}
  mode="multiple"
/>
```
