import React, { useMemo } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useLazyMetadataTree } from '@manyun/resource-hub.gql.client.metadata';
import type { MetaType, Metadata } from '@manyun/resource-hub.model.metadata';

import { flattenTreeData } from './util';

export type Option = {
  label: string;
  value: string;
};
export type Value = string | string[];
export type MetaTypeSelectProps<Value> = {
  metaType: MetaType;
  disabledMateCodes?: string[];
  onDidReceiveData?: (data: Metadata[]) => void;
  onChange?: (value: Value, option: Value extends string ? Option : Option[]) => void;
} & Omit<SelectProps<Value, Option>, 'options' | 'onChange'>;

function _MetaTypeSelect<Value>(
  {
    metaType,
    disabledMateCodes,
    onDidReceiveData,
    onFocus,
    onChange,
    ...restProps
  }: MetaTypeSelectProps<Value>,
  ref?: React.ForwardedRef<RefSelectProps>
) {
  const [getMetaData, { data: metaData }] = useLazyMetadataTree({
    variables: {
      type: metaType,
    },
    fetchPolicy: 'cache-first',
  });

  // @ts-ignore metadataTree type
  const _data = flattenTreeData(metaData?.metadataTree ?? []);
  const data = _data.map(item => ({
    ...item,
    value: item.code as string,
    label: item.name as string,
  }));

  React.useEffect(() => {
    getMetaData();
  }, []);

  useDeepCompareEffect(() => {
    if (onDidReceiveData && data) {
      // @ts-ignore metadataTree type
      onDidReceiveData(data);
    }
  }, [onDidReceiveData, data]);

  const options = useMemo(() => {
    return (
      data?.map(item => ({
        value: item.value,
        label: item.label,
        disabled: (disabledMateCodes ?? []).includes(item.value),
      })) ?? []
    );
  }, [data, disabledMateCodes]);

  return (
    <Select<Value, Option>
      {...restProps}
      ref={ref}
      options={options}
      onChange={(value, option) => {
        onChange?.(value, option as Value extends string ? Option : Option[]);
      }}
    />
  );
}

interface GenericMetaTypeSelect {
  <Value>(props: MetaTypeSelectProps<Value>, ref: React.ForwardedRef<RefSelectProps>): JSX.Element;
  displayName: string;
}

export const MetaTypeSelect = React.forwardRef(_MetaTypeSelect) as GenericMetaTypeSelect;

MetaTypeSelect.displayName = 'MetaTypeSelect';
