export type DummyTreeNode = {
  [x: string]: unknown;
  children?: unknown[];
};

export function flattenTreeData<T extends DummyTreeNode = DummyTreeNode>(
  treeData: T[]
): Omit<T, 'children'>[] {
  const dataList: Omit<T, 'children'>[] = [];
  loop(treeData);
  function loop(data: T[]) {
    for (let i = 0; i < data.length; i++) {
      const { children, ...node } = data[i];
      if (children) {
        // @ts-ignore children type
        loop(children);
      }
      dataList.push(node);
    }
  }

  return dataList;
}
