import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { webRequest } from '@manyun/service.request';

import { MetaTypeSelect } from './meta-type-select';

webRequest.axiosInstance.defaults.baseURL = 'http://***********:13000';
webRequest.mockOn();

export const BasicMetaTypeSelect = () => {
  return (
    <ConfigProvider>
      {/*  @ts-ignore: for bit tag ,note for now */}
      <MetaTypeSelect<string>
        style={{ width: 200 }}
        onChange={(value, option) => {
          // eslint-disable-next-line no-console
          console.log('value: ', value, ' option: ', option);
        }}
      />
    </ConfigProvider>
  );
};

export const SelectMultipleMetaTypeSelect = () => {
  return (
    <ConfigProvider>
      {/*  @ts-ignore: for bit tag ,note for now */}
      <MetaTypeSelect<string[]>
        style={{ width: 200 }}
        mode="multiple"
        onChange={(values, options) => {
          // eslint-disable-next-line no-console
          console.log('values: ', values, ' options: ', options);
        }}
      />
    </ConfigProvider>
  );
};
