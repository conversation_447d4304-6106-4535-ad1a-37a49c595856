import React from 'react';

import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import type { MetaType } from '@manyun/resource-hub.model.metadata';

export type MetaTypeTextProps = {
  /**
   * 等级code
   */
  code: string | number;
  /** 元数据类型 */
  metaType: MetaType;
  queryDeleted?: boolean;
  /**
   * 默认显示codes
   */
  defaultShow?: boolean;
};

export function MetaTypeText({
  defaultShow = false,
  code,
  metaType,
  queryDeleted,
}: MetaTypeTextProps) {
  const [{ data }, { readMetaData }] = useMetaData(metaType, queryDeleted);
  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [code]);

  const name = data.entities[code] ? data.entities[code].name : defaultShow ? code : '--';

  return <span aria-label="meta_type_name">{name}</span>;
}
