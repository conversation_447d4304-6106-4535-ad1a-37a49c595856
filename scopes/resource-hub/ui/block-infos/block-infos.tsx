import React from 'react';

import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Result } from '@manyun/base-ui.ui.result';

import { UserLink } from '@manyun/auth-hub.ui.user';
import type { Block } from '@manyun/resource-hub.model.block';
import { fetchBlock } from '@manyun/resource-hub.service.fetch-block';
import { isCampusGuid } from '@manyun/resource-hub.util.space-guid';
import type { RequestError } from '@manyun/service.request';

export type BlockInfosProps = {
  guid?: string;
  instance?: Block;
};

function InternalBlockInfos({ instance }: Pick<Required<BlockInfosProps>, 'instance'>) {
  return (
    <Descriptions column={4}>
      <Descriptions.Item label="机房名称">{instance.idcName}</Descriptions.Item>
      <Descriptions.Item label="机房编号">{instance.idc}</Descriptions.Item>
      <Descriptions.Item label="楼栋编号">{instance.tag}</Descriptions.Item>
      <Descriptions.Item label="所属周期">{instance.constructionCycleText}</Descriptions.Item>
      <Descriptions.Item label="楼栋类别">{instance.typeName}</Descriptions.Item>
      <Descriptions.Item label="建设日期">{instance.getFormattedConstructedAt()}</Descriptions.Item>
      <Descriptions.Item label="投产日期">
        {instance.getFormattedPutIntoProductionAt()}
      </Descriptions.Item>
      <Descriptions.Item label="状态">{instance.statusName}</Descriptions.Item>
      <Descriptions.Item label="责任人">
        <UserLink {...instance.owner} />
      </Descriptions.Item>
    </Descriptions>
  );
}

export function BlockInfos({ guid, instance }: BlockInfosProps) {
  const [_instance, setInstance] = React.useState<Block | undefined>(instance);
  const [error, setError] = React.useState<RequestError | undefined>();

  React.useEffect(() => {
    if (guid) {
      if (isCampusGuid(guid)) {
        setError({ code: 'EMPTY', message: 'No files for a campus block!' });
      } else {
        fetchBlock({ guid }).then(({ error, data }) => {
          if (error) {
            setError(error);
            return;
          }
          if (!data) {
            setError({ message: `找不到楼(${guid})!` });
            return;
          }
          setInstance(data);
          setError(undefined);
        });
      }
    }
  }, [guid]);

  React.useEffect(() => {
    setInstance(instance);
  }, [instance]);

  if (error) {
    if (error.code === 'EMPTY') {
      return <Empty />;
    }
    return <Result status="warning" title={error.message} />;
  }

  if (!_instance) {
    return null;
  }

  return <InternalBlockInfos instance={_instance} />;
}
