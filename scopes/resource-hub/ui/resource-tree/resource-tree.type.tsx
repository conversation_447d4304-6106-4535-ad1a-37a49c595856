import type { CSSProperties, Key, ReactNode } from 'react';

import type { TreeProps } from '@manyun/base-ui.ui.tree';
import type { Space } from '@manyun/dc-brain.gql.client';
import type { Device } from '@manyun/resource-hub.model.device';
import type { ApiQ as FetchGridsParams } from '@manyun/resource-hub.service.fetch-grids';
import type { ApiQ as FetchDevicesParams } from '@manyun/resource-hub.service.fetch-page-devices';

export type ResourceTreeProps = {
  /**
   * 是否只展示当前用户有权限的空间数据
   */
  authorizedOnly?: boolean;
  /**
   * 空间树层级
   */
  treeMode?: NodeType[];
  /**
   * 展示某个空间节点下的数据
   */
  spaceGuid?: string;
  style?: CSSProperties;
  /** TreeNode checkable (only effect when checkable is true) */
  checkableNodes?: NodeType[];
  /** max checked treeNode count (only effect when checkable is true) */
  maxCheckedCount?: number;
  checkedDevices?: Device[];
  onCheckedDevicesChange?: (checkedDevices: Device[]) => void;
  fetchDevicesParams?: Partial<FetchDevicesParams>;
  /** 是否需要虚拟楼 */
  needVirtualBlock?: boolean;
  wrapperClassName?: string;
  /** 是否显示设备筛选框，当为 true 时默认全展示，可使用对象格式单独关闭其中某些筛选 */
  showFilter?:
    | boolean
    | {
        roomType?: boolean;
        deviceType?: boolean;
      };
  /** 自定义渲染设备标题 */
  renderDeviceTitle?: (device: Device) => ReactNode;
  onExpandedKeysChange?: (expandedKeys: Key[]) => void;
  onSelectedKeysChange?: (selectedKeys: Key[]) => void;
  /** 是否展示搜索框，默认为 true */
  showSearch?: boolean;
  /*搜索机柜是否展示空间*/
  onlyExistGridSpace?: boolean;
  /** 仅查询包间 */
  onlySearchRoom?: boolean;
  /** 查询机柜排序 */
  gridSortOrder?: FetchGridsParams['sortOrder'];
  /** 机柜排序 */
  gridSortField?: FetchGridsParams['sortField'];
  /** 设备分类, 目前和showFilter不兼容，仅支持只传其中一个api */
  outerDeviceType?: string;
  /** 多楼栋查询, 比如['JS1.A']， 注意前提需要传参数idc: 'JS1' */
  blocks?: string[];
  /** 机房查询, 比如'JS1' */
  idc?: string;
  /** 是否处理新的机柜数据（原来数据由于机列机柜不一致有问题，考虑到线上正在跑的应用，加了api兼容） */
  isQueryNewGrids?: boolean;
} & Omit<TreeProps<TreeNode>, 'loadData' | 'checkedKeys' | 'loadedKeys'>;
/** DEVICE 与 COLUMN、GRID 不能同时设置 */
export type NodeType =
  | 'REGION'
  | 'IDC'
  | 'BLOCK'
  | 'FLOOR'
  | 'ROOM'
  | 'DEVICE'
  | 'COLUMN'
  | 'GRID'
  | 'ROOM_TYPE';

export type TreeNode = {
  parentKey: string;
  key: string;
  title: ReactNode;
  type: NodeType;
  children?: TreeNode[] | null;
  isLeaf?: boolean;
  icon?: JSX.Element;
  checked?: boolean;
  checkable?: boolean;
  disableCheckbox?: boolean;
  device?: Device;
  custom?: Space['custom'];
};
