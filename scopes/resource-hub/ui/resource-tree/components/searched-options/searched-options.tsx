import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import styles from './searched-options.module.less';

export type OptionType = {
  label: React.ReactNode;
  value: string;
  type: string;
  parentKey: string;
  key: string;
  title: React.ReactNode;
};

export type SearchedOption = {
  label: React.ReactNode;
  options: OptionType[];
};

export type SearchedOptionsProps = {
  className: string;
  options: SearchedOption[];
  onSelect: (value: string, option: OptionType) => void;
};

export function SearchedOptions(props: SearchedOptionsProps) {
  const { className, options, onSelect } = props;
  return (
    <Space className={className} direction="vertical">
      {options.map(({ label, options }) => (
        <>
          <Typography.Text type="secondary" className={styles.groupName}>
            {label}
          </Typography.Text>
          <Space direction="vertical" size="small">
            {options.map(option => (
              <Typography.Link
                key={option.key}
                className={styles.link}
                textColorType="default"
                onClick={() => onSelect(option.value, option)}
              >
                {option.label}
              </Typography.Link>
            ))}
          </Space>
        </>
      ))}
    </Space>
  );
}
