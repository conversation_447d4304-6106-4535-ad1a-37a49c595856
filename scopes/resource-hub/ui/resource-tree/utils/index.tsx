import React, { useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Row } from '@manyun/base-ui.ui.grid';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';
import type { Space, SpaceNodeType } from '@manyun/dc-brain.gql.client';
import { useSpaces } from '@manyun/resource-hub.gql.client.resources';
import type { Device } from '@manyun/resource-hub.model.device';
import type { RackColumns } from '@manyun/resource-hub.service.fetch-rack-columns';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { OptionType } from '../components/searched-options';
import styles from '../resource-tree.module.less';
import type { NodeType, TreeNode } from '../resource-tree.type';

export type ResourceType = Space;

//二期base-ui需要支持此方法,二期去除拍平数组resources
export const getFlatResources = (resourceMap: ResourceType[]) => {
  const result: ResourceType[] = [];

  const getFlatList = function (arr: ResourceType[]) {
    arr.forEach(item => {
      if (Array.isArray(item.children)) {
        getFlatList(item.children);
      }
      result.push(item);
    });
  };
  getFlatList(resourceMap);
  return result;
};

export const useResourceTree = (
  authorizedOnly: boolean | undefined,
  treeMode: NodeType[],
  spaceGuid: string | undefined,
  checkableNodes: NodeType[],
  needVirtualBlock: boolean,
  onlyExistGridSpace?: boolean,
  roomType?: string,
  deviceType?: string,
  /** 多楼栋查询, 比如['JS1.A']， 注意前提需要传idc: ['JS1'] */
  blocks?: string[],
  /** 机房查询, 比如'JS1' */
  idc?: string
) => {
  const nodeTypes = treeMode.filter(
    item => item !== 'DEVICE' && item !== 'COLUMN' && item !== 'GRID'
  ) as SpaceNodeType[];

  const { data } = useSpaces({
    variables: {
      nodeTypes,
      includeVirtualBlocks: needVirtualBlock,
      authorizedOnly,
      roomTypes: roomType ? [roomType] : [],
      deviceType: treeMode.includes('DEVICE') ? deviceType : undefined,
      onlyExistGridSpace,
      blocks,
      idc,
    },
    fetchPolicy: 'network-only',
  });
  const [resources, setResources] = useState<ResourceType[]>([]);

  useShallowCompareEffect(() => {
    // @ts-ignore ts(2322)
    const resources: ResourceType[] = data?.spaces ? getFlatResources(data.spaces) : [];

    const { idc, block, room } = getSpaceGuidMap(spaceGuid || '');
    //二期去除spaceGuid，在useSpaces里支持
    const filteredResource = resources.filter(resource => {
      if (!treeMode.includes(resource.type as NodeType)) {
        return false;
      }
      if (!spaceGuid) {
        return true;
      }
      if (room) {
        return (
          resource.value.includes(`${idc}.${block}.${room}`) ||
          resource.value === `${idc}.${block}` ||
          resource.value === idc
        );
      }
      if (block) {
        return (
          `${getSpaceGuidMap(resource.value)?.idc}.${getSpaceGuidMap(resource.value)?.block}` ===
            `${idc}.${block}` || resource.value === idc
        ); // 部分虚拟楼的value和idc一样，所以兼容, 同时处理js1.1和 js1.10的匹配问题
      }
      if (idc) {
        return resource.value.includes(idc);
      }
      return false;
    });
    setResources(filteredResource);
  }, [data, spaceGuid, treeMode]);

  const [resourceTreeNodes, setResourceTreeNodes] = useState<TreeNode[]>([]);
  useShallowCompareEffect(() => {
    if (!treeMode.length || !resources.length) {
      setResourceTreeNodes([]);
      return;
    }
    const resourceTreeNodes = generateTreeData<TreeNode, ResourceType>(resources, {
      key: 'value',
      typeKey: 'type',
      parentKey: 'parentValue',
      nodeTypes: treeMode,
      getNode: (data, children) => {
        const { type, label, value, parentValue, custom } = data;
        const isLeaf =
          type === treeMode[treeMode.length - 1] || (type !== 'ROOM' && children === null);
        const node = {
          key: value,
          type: type as NodeType,
          title: getLabel({ type, label, children }),
          parentKey: parentValue as string,
          children: children === null ? undefined : children,
          isLeaf,
          checkable: checkableNodes.includes(type as NodeType),
          custom,
        };
        return node;
      },
    });
    setResourceTreeNodes(resourceTreeNodes);
  }, [resources, treeMode, checkableNodes]);

  return { resources, resourceTreeNodes };
};

const getLabel = ({
  type,
  label,
  children,
}: {
  type: NodeType;
  label: string;
  children: TreeNode[] | null;
}) => {
  if (type === 'ROOM_TYPE') {
    return `${label}${children && children?.length > 0 ? '(' + children.length + ')' : ''}`;
  }
  return label;
};

export const getExpandKeys = (key: string) => {
  const { idc, block, room, cabinet } = getSpaceGuidMap(key.toString());
  const expandedKeys = [];
  if (idc) {
    expandedKeys.push(idc);
  }
  if (block) {
    expandedKeys.push(`${idc}.${block}`);
  }
  if (room) {
    expandedKeys.push(`${idc}.${block}.${room}`);
  }
  if (cabinet) {
    expandedKeys.push(`${idc}.${block}.${room}.${cabinet}`);
  }
  expandedKeys.pop();
  return expandedKeys;
};

export const getSpaceOptions = (
  value: string,
  dataSource: ResourceType[],
  type: ResourceType['type']
): OptionType[] => {
  return dataSource
    .filter(data => data.type === type && data.label.includes(value))
    .map(data => ({
      label: data.value.replace(/\./g, '/'),
      value: data.value,
      type,
      parentKey: data.parentValue as string,
      key: data.value,
      title: data.label,
    }));
};

export const getDeviceOptions = (dataSource: Device[], type: 'DEVICE'): OptionType[] => {
  // 设备列表组装成 options (value 要用 guid)
  return dataSource.map(device => {
    const { spaceGuid, guid, name } = device;
    const { roomGuid } = spaceGuid;
    return {
      label: (
        <Row justify="space-between">
          <Typography.Text className={styles.deviceName}>{name}</Typography.Text>
          <Typography.Text type="secondary">{roomGuid?.replace(/\./g, '/') || ''}</Typography.Text>
        </Row>
      ),
      value: `${roomGuid}.${guid}`,
      type,
      parentKey: roomGuid!,
      key: `${roomGuid}.${guid}`,
      title: name,
      isLeaf: true,
      device,
    };
  });
};

export const getDeviceKey = (device: Device) => `${device.spaceGuid.roomGuid}.${device.guid}`;
export const getRackColumnKey = (rack: RackColumns) => `${rack.guid}`;

export function filterTree(
  nodes: TreeNode[] | undefined,
  predicate: (node: TreeNode) => boolean
): TreeNode[] | undefined {
  if (!nodes?.length) {
    return nodes;
  }

  // 直接使用 Array 的 filter 可以过滤当层节点
  return nodes.filter(it => {
    // 不符合条件的直接砍掉
    if (!predicate(it)) {
      return false;
    }

    // 符合条件的保留，并且需要递归处理其子节点
    it.children = filterTree(it.children!, predicate);
    return true;
  });
}
