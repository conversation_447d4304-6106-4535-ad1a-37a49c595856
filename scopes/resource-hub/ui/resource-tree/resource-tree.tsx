import { FileOutlined } from '@ant-design/icons';
import classnames from 'classnames';
import cloneDeep from 'lodash.clonedeep';
import debounce from 'lodash.debounce';
import groupBy from 'lodash.groupby';
import isEqual from 'lodash.isequal';
import omit from 'lodash.omit';
import type { Key } from 'react';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useDeepCompareEffect, useLatest, useShallowCompareEffect } from 'react-use';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tree } from '@manyun/base-ui.ui.tree';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { SpaceNodeType } from '@manyun/dc-brain.gql.client';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import type { Device } from '@manyun/resource-hub.model.device';
import { fetchDeviceByGuid } from '@manyun/resource-hub.service.fetch-device-by-guid';
import { fetchGrids } from '@manyun/resource-hub.service.fetch-grids';
import type { Grid } from '@manyun/resource-hub.service.fetch-grids';
import type { ApiQ as FetchDevicesParams } from '@manyun/resource-hub.service.fetch-page-devices';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import type { RackColumns } from '@manyun/resource-hub.service.fetch-rack-columns';
import { fetchRackColumns } from '@manyun/resource-hub.service.fetch-rack-columns';
import { DeviceTypeCascader, useDevices } from '@manyun/resource-hub.ui.device-type-cascader';
import { RoomTypeSelect } from '@manyun/resource-hub.ui.room-type-select';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { SearchedOptions } from './components/searched-options';
import type { OptionType, SearchedOption } from './components/searched-options';
import styles from './resource-tree.module.less';
import type { ResourceTreeProps, TreeNode } from './resource-tree.type';
import {
  filterTree,
  getDeviceKey,
  getDeviceOptions,
  getExpandKeys,
  getRackColumnKey,
  getSpaceOptions,
  useResourceTree,
} from './utils';

export function ResourceTree({
  authorizedOnly,
  treeMode = [`IDC`, `BLOCK`, `ROOM`, `DEVICE`],
  spaceGuid,
  style,
  defaultCheckedKeys,
  checkableNodes = treeMode,
  maxCheckedCount,
  checkedDevices,
  onCheckedDevicesChange,
  fetchDevicesParams = {
    sortField: 'id',
    sortOrder: 'ascend',
  },
  needVirtualBlock = false,
  wrapperClassName,
  showFilter = false,
  renderDeviceTitle,
  expandedKeys,
  onExpandedKeysChange,
  selectedKeys,
  onSelectedKeysChange,
  showSearch = true,
  onlyExistGridSpace,
  onlySearchRoom,
  gridSortOrder,
  gridSortField,
  outerDeviceType,
  blocks,
  idc,
  ...treeProps
}: ResourceTreeProps) {
  const dispatch = useDispatch();

  const treeModeRef = useLatest(treeMode);
  const checkableNodesRef = useLatest(checkableNodes);

  const [checkedKeys, setCheckedKeys] = useState<Key[]>([]);
  const [internalSelectedKeys, setInternalSelectedKeys] = useState<Key[]>(
    treeProps.defaultSelectedKeys || []
  );
  useShallowCompareEffect(() => {
    // 当外部指定了 selectedKeys，则为受控模式
    if (selectedKeys) {
      setInternalSelectedKeys(selectedKeys);
    }
  }, [selectedKeys]);
  const onSelectedKeysChangeRef = useLatest(onSelectedKeysChange);
  const onInternalSelectedKeysChange = useCallback(
    (keys: Key[]) => {
      if (selectedKeys) {
        onSelectedKeysChangeRef.current?.(keys);
      } else {
        setInternalSelectedKeys(keys);
      }
    },
    [selectedKeys, onSelectedKeysChangeRef]
  );

  const [internalExpandedKeys, setInternalExpandedKeys] = useState<Key[]>(
    treeProps.defaultExpandedKeys || []
  );
  useShallowCompareEffect(() => {
    // 当外部指定了 expandedKeys，则为受控模式
    if (expandedKeys) {
      // 当外部设置了新的 expandedKeys，但是 treeData 还未发生改变且 autoExpandParent 为 true 时，
      // 此时在 rc-tree 逻辑中会从 treeData 中取对应的 expandedKeys，因 treeData 未更新，导致会返回空数组，
      // 故这边特意加了延迟，等待 treeData 更新以确保 expandedKeys 设置生效
      window.setTimeout(() => {
        setInternalExpandedKeys(expandedKeys);
        setAutoExpandParent(true);
      }, 200);
    }
  }, [expandedKeys]);
  const onExpandedKeysChangeRef = useLatest(onExpandedKeysChange);
  const onInternalExpandedKeysChange = useCallback(
    (keys: Key[]) => {
      setInternalExpandedKeys(keys);
      if (expandedKeys) {
        onExpandedKeysChangeRef.current?.(keys);
      }
    },
    [onExpandedKeysChangeRef, expandedKeys]
  );

  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [searchValue, setSearchValue] = useState('');
  const [searchedOptions, setSearchedOptions] = useState<SearchedOption[]>([]);

  const [spinning, setSpinning] = useState(false);
  const treeRef = useRef(null);

  const [roomType, setRoomType] = useState<string>();
  const [deviceType, setDeviceType] = useState<string>();

  const { resources, resourceTreeNodes } = useResourceTree(
    authorizedOnly,
    treeMode,
    spaceGuid,
    checkableNodes,
    needVirtualBlock,
    onlyExistGridSpace,
    roomType,
    deviceType,
    blocks,
    idc
  );

  const [treeData, setTreeData] = useState<TreeNode[]>([]);

  useDeepCompareEffect(() => {
    if (outerDeviceType && !showFilter) {
      setDeviceType(outerDeviceType); // 根据三级分类过滤包间用(仅显示有设备包间)
    }
  }, [outerDeviceType, showFilter]);

  useEffect(() => {
    if (treeModeRef.current.includes('REGION')) {
      dispatch(syncCommonDataAction({ strategy: { citiesTree: 'IF_NULL' } }));
    }
  }, [dispatch, treeModeRef]);

  useEffect(() => {
    setTreeData(resourceTreeNodes);
  }, [resourceTreeNodes]);

  //default check处理
  const onDefaultCheck = async (keys: Key[]) => {
    const results = await Promise.all(
      keys.map(async item => {
        const { cabinet } = getSpaceGuidMap(String(item));
        if (cabinet) {
          const { data } = await fetchDeviceByGuid({ guid: cabinet });
          const parentKey = data?.spaceGuid.roomGuid;
          if (!data || !parentKey) {
            return;
          }

          return {
            key: getDeviceKey(data),
            type: 'DEVICE',
            title: data.name,
            parentKey,
            isLeaf: true,
            children: null,
            icon: <FileOutlined />,
            device: data,
          };
        }
        return null;
      })
    );
    const newChildren = results.filter(item => item) as TreeNode[];
    const keyChildrenObj = groupBy(newChildren, 'parentKey');

    const newExpandedKeys = newChildren.map(item => item.key);
    Object.keys(keyChildrenObj).forEach(key => {
      updateTreeData(key, keyChildrenObj[key]);
    });
    if (newExpandedKeys.length > 0) {
      onInternalExpandedKeysChange(
        Array.from(new Set([...internalExpandedKeys, ...newExpandedKeys]))
      );
    }
    setCheckedKeys(keys);
  };

  const onDefaultCheckRef = useLatest(onDefaultCheck);
  const shouldSetDefaultCheckedKeysRef = useRef(true);
  useEffect(() => {
    // 保证 onDefaultCheck 仅在 treeData 有数据时执行一次
    if (defaultCheckedKeys && treeData.length && shouldSetDefaultCheckedKeysRef.current) {
      shouldSetDefaultCheckedKeysRef.current = false;
      onDefaultCheckRef.current?.(defaultCheckedKeys);
    }
  }, [defaultCheckedKeys, onDefaultCheckRef, treeData]);

  const updateTreeData = useCallback(
    (parentKey: string, newChildren: TreeNode[]) => {
      const loop = (data: TreeNode[]): TreeNode[] =>
        data.map(item => {
          if (item.key === parentKey) {
            return {
              ...item,
              children: newChildren,
            };
          }
          if (item.children) {
            return { ...item, children: loop(item.children) };
          }
          return item;
        });

      // const newTree = loop(treeData);
      setTreeData(pre => loop(pre));
    },
    [treeData]
  );

  const [treeVisible, setTreeVisible] = useState(true);
  const [searching, setSearching] = useState(false);
  const handleSearchValueChange = useMemo(() => {
    const func = async (value: string) => {
      if (!value) {
        setSearchedOptions([]);
        setTreeVisible(true);
        return;
      }
      setTreeVisible(false);
      setSearching(true);
      const options: SearchedOption[] = [];
      if (treeModeRef.current.includes('IDC')) {
        options.push({
          label: '机房',
          options: getSpaceOptions(value, resources, 'IDC' as SpaceNodeType),
        });
      }
      if (treeModeRef.current.includes('BLOCK')) {
        options.push({
          label: '楼栋',
          options: getSpaceOptions(value, resources, 'BLOCK' as SpaceNodeType),
        });
      }
      if (treeModeRef.current.includes('ROOM')) {
        options.push({
          label: '包间',
          options: getSpaceOptions(value, resources, 'ROOM' as SpaceNodeType),
        });
      }
      if (treeModeRef.current.includes('DEVICE')) {
        const { idc, block, room } = getSpaceGuidMap(spaceGuid || '');
        const params: {
          name: string;
          idcTag?: string;
          blockTag?: string;
          roomTags?: string[];
          pageNum: 1;
          pageSize: 2000;
        } = {
          name: value,
          pageNum: 1,
          pageSize: 2000,
        };
        if (idc) {
          params.idcTag = idc;
        }
        if (block) {
          params.blockTag = block;
        }
        if (room) {
          params.roomTags = [room];
        }
        const { error, data } = await fetchPageDevices(params);
        if (error) {
          message.error(error.message);
          return;
        }
        const deviceList = getDeviceOptions(data.data || [], 'DEVICE');
        if (deviceList.length) {
          options.push({
            label: '设备',
            options: deviceList,
          });
        }
      }
      setSearching(false);
      setSearchedOptions(options.filter(v => v.options.length));
    };
    return debounce(func, 200, { leading: true });
  }, [resources, spaceGuid, treeModeRef]);
  const onSearchValueChange = useCallback(
    (value: string) => {
      setSearchValue(value);
      handleSearchValueChange(value);
    },
    [handleSearchValueChange]
  );

  const onExpand: ResourceTreeProps['onExpand'] = useMemo(() => {
    const func: ResourceTreeProps['onExpand'] = (expandedKeys, info) => {
      onInternalExpandedKeysChange(expandedKeys);
      setAutoExpandParent(false);
      treeProps.onExpand?.(expandedKeys, info);
    };
    return func;
  }, [onInternalExpandedKeysChange, treeProps]);

  /** 组件内部的动态查询设备列表参数 */
  const localFetchDevicesParamsRef = useRef<Partial<FetchDevicesParams>>();
  const [loadedKeys, setLoadedKeys] = useState<Key[]>([]);
  const renderDeviceTitleRef = useLatest(renderDeviceTitle);

  // 设备三级分类
  const [, getDeviceTypes] = useDevices({ numbered: true });

  React.useEffect(() => {
    getDeviceTypes();
  }, [getDeviceTypes]);

  /**
   * 异步加载设备列表并展示节点
   */
  const onLoadData = useCallback(
    (node: TreeNode, fromSearch?: boolean) => {
      return new Promise<void>(async resolve => {
        const { key, children, type } = node;
        const { idc, block, room, cabinet } = getSpaceGuidMap(key);
        // 不止 room 层没有 children，block 层可能也没有（包间类型过滤）
        if (!children || !children?.length) {
          setLoadedKeys(keys => [...keys, key]);
        }
        if (
          (type === 'ROOM' && (!children || !children?.length) && treeMode.includes('DEVICE')) ||
          fromSearch
        ) {
          // 查询子设备
          const { error, data } = await fetchPageDevices({
            ...fetchDevicesParams,
            ...localFetchDevicesParamsRef.current,
            pageNum: 1,
            pageSize: 2000,
            idcTag: idc ?? undefined,
            blockTag: block ?? undefined,
            roomTags: room ? [room] : undefined,
          });
          if (error) {
            message.error(error.message);
            resolve();
            return;
          }
          if (data?.total > 0) {
            const deviceList = data.data;
            const newChildren: TreeNode[] = deviceList.map((device: Device) => {
              const titleText = device.deviceLabel
                ? `${device.name} ${device.deviceLabel}`
                : device.name;
              return {
                key: getDeviceKey(device),
                type: 'DEVICE',
                title: renderDeviceTitleRef.current ? (
                  renderDeviceTitleRef.current(device)
                ) : (
                  <div style={{ maxWidth: 170 }}>
                    <Typography.Text ellipsis={{ tooltip: titleText }}>{titleText}</Typography.Text>
                  </div>
                ),
                parentKey: key,
                isLeaf: true,
                children: null,
                icon: <FileOutlined />,
                checkable: checkableNodesRef.current.includes('DEVICE'),
                device,
              };
            });

            if (newChildren && newChildren.length) {
              updateTreeData(key, newChildren);
            }
          }

          if (!internalExpandedKeys.includes(key) && !fromSearch) {
            onInternalExpandedKeysChange([...internalExpandedKeys, key]);
          }
          resolve();
          return;
        }

        if (
          (type === 'ROOM' && (!children || !children?.length) && treeMode.includes('COLUMN')) ||
          fromSearch
        ) {
          const { error, data } = await fetchRackColumns({
            idcTag: idc!,
            blockTag: block ?? undefined,
            roomTag: room ?? undefined,
          });
          if (error) {
            message.error(error.message);
            resolve();
            return;
          }
          if (data?.total > 0) {
            const rackList = data.data;

            const newChildren: TreeNode[] = rackList.map((rack: RackColumns) => {
              return {
                key: getRackColumnKey(rack),
                type: 'COLUMN',
                title: `${rack.columnTag}列`,
                parentKey: key,
                isLeaf: !treeMode.includes('GRID'),
                children: null,
                icon: <FileOutlined />,
                checkable: checkableNodesRef.current.includes('COLUMN'),
                rack,
              };
            });

            if (newChildren && newChildren.length) {
              updateTreeData(key, newChildren);
            }
          }

          if (!internalExpandedKeys.includes(key) && !fromSearch) {
            onInternalExpandedKeysChange([...internalExpandedKeys, key]);
          }
          resolve();
          return;
        }

        if (
          (type === 'COLUMN' && (!children || !children?.length) && treeMode.includes('GRID')) ||
          fromSearch
        ) {
          const { error, data } = await fetchGrids({
            idcTag: idc!,
            blockTag: block ?? undefined,
            roomTag: room ?? undefined,
            columnTag: cabinet ?? undefined,
            pageNum: 1,
            pageSize: 100, // 机列下的机柜不会超过100, 目前查全部写死pageSize
            sortOrder: gridSortOrder,
            sortField: gridSortField,
          });
          if (error) {
            message.error(error.message);
            resolve();
            return;
          }
          if (data?.total > 0) {
            const gridList = data.data;

            const newChildren: TreeNode[] = gridList.map((grid: Grid) => {
              return {
                key: grid.guid,
                type: 'GRID',
                title: `${grid.tag}机柜`,
                parentKey: key,
                isLeaf: true,
                children: null,
                icon: <FileOutlined />,
                checkable: checkableNodesRef.current.includes('GRID'),
                grid,
              };
            });

            if (newChildren && newChildren.length) {
              updateTreeData(key, newChildren);
            }
          }

          if (!internalExpandedKeys.includes(key) && !fromSearch) {
            onInternalExpandedKeysChange([...internalExpandedKeys, key]);
          }
          resolve();
          return;
        }
        resolve();
      });
    },
    [
      treeMode,
      fetchDevicesParams,
      internalExpandedKeys,
      renderDeviceTitleRef,
      checkableNodesRef,
      updateTreeData,
      onInternalExpandedKeysChange,
    ]
  );

  //tree点击
  const onSelect = useMemo(() => {
    const func: ResourceTreeProps['onSelect'] = (keys, info) => {
      treeProps.onSelect?.(keys, info);
      onInternalSelectedKeysChange(keys);
    };
    return func;
  }, [onInternalSelectedKeysChange, treeProps]);

  // 搜索到的选项，方便快速定位
  const [selectedOption, setSelectedOption] = useState<OptionType>();
  const handleSelectSearchedOption = useCallback(
    async (value: string, option: OptionType) => {
      setSpinning(true);
      setSearchValue('');
      if (!value) {
        return;
      }
      // 视图树展开该项的父节点
      const { type } = option;
      if (type === 'DEVICE') {
        const key = value.split('.').slice(0, -1).join('.');
        await onLoadData({ key, type: 'ROOM', parentKey: '', title: '' }, true);
      }
      onInternalExpandedKeysChange(getExpandKeys(value));
      setAutoExpandParent(true);
      // todo：后期自定义 onSelect info 类型，更改后可移除该 any
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      onSelect([value], { node: option } as any);
      setSelectedOption(option);
      setTreeVisible(true);
    },
    [onInternalExpandedKeysChange, onLoadData, onSelect]
  );

  // 处理搜索下拉选选中产生的副作用
  useEffect(() => {
    let timer: number | undefined = undefined;
    if (selectedOption) {
      timer = window.setTimeout(
        () => {
          document.querySelector(`.${prefixCls}-tree-node-selected`)?.scrollIntoView();
          setSpinning(false);
          clearTimeout(timer);
        },
        selectedOption.type === 'DEVICE' ? 2000 : 0
      );
    }
    return () => clearTimeout(timer);
  }, [selectedOption]);

  // support checkedDevices props
  const checkedKeysRef = useLatest(checkedKeys);
  useEffect(() => {
    if (
      checkedDevices &&
      checkableNodesRef.current.length >= 1 &&
      checkableNodesRef.current.includes('DEVICE')
    ) {
      const keys = checkedDevices.map(getDeviceKey);
      if (!isEqual(keys, checkedKeysRef.current)) {
        // avoid cycle
        setCheckedKeys(keys);
      }
    }
  }, [checkableNodesRef, checkedDevices, checkedKeysRef]);
  const treeDataRef = useLatest(treeData);
  const getCheckedDevices = useCallback(
    (nodes: TreeNode[], checkedKeys: Key[], devices: Device[] = []) => {
      nodes.forEach(node => {
        if (node.type === 'DEVICE' && checkedKeys.includes(node.key)) {
          devices.push(node.device!);
        }
        if (node.children?.length) {
          getCheckedDevices(node.children, checkedKeys, devices);
        }
      });
      return devices;
    },
    []
  );

  const onCheckedDevicesChangeRef = useLatest(onCheckedDevicesChange);
  useEffect(() => {
    const _checkedDevices = treeDataRef.current
      ? getCheckedDevices(treeDataRef.current, checkedKeys)
      : [];
    if (!maxCheckedCount || maxCheckedCount >= _checkedDevices.length) {
      onCheckedDevicesChangeRef.current?.(_checkedDevices);
    }
  }, [checkedKeys, getCheckedDevices, maxCheckedCount, onCheckedDevicesChangeRef, treeDataRef]);

  // 重置 treeData，清空上面附加的设备数据以及相应的 keys 等数据
  const resetTreeData = useCallback(() => {
    setTreeData([...resourceTreeNodes]);
    setLoadedKeys([]);
    // 仅去除展开的包间
    onInternalExpandedKeysChange(
      internalExpandedKeys.filter(key => getSpaceGuidMap(String(key)).room === null)
    );
    onInternalSelectedKeysChange([]);
    setCheckedKeys([]);
  }, [
    internalExpandedKeys,
    onInternalExpandedKeysChange,
    onInternalSelectedKeysChange,
    resourceTreeNodes,
  ]);

  // support maxCheckedCount
  /** change every treeNode disableCheckbox */
  const handleChangeDisableCheckbox = useCallback(
    (disable: boolean) => {
      function changeNodesDisableCheckbox(nodes: TreeNode[]) {
        nodes.forEach(node => {
          if (checkableNodesRef.current.includes(node.type)) {
            if (disable && !checkedKeysRef.current.includes(node.key)) {
              node.disableCheckbox = true;
            } else {
              node.disableCheckbox = false;
            }
          }
          if (node.children?.length) {
            changeNodesDisableCheckbox(node.children);
          }
        });
      }
      if (treeProps.checkable) {
        changeNodesDisableCheckbox(treeData);
        setSpinning(true);
        setTimeout(() => {
          setSpinning(false);
        }, 100);
      }
    },
    [checkableNodesRef, checkedKeysRef, treeData, treeProps.checkable]
  );
  const handleChangeDisableCheckboxRef = useLatest(handleChangeDisableCheckbox);

  useEffect(() => {
    if (maxCheckedCount) {
      const len = checkedKeys.length;
      let disableCheckbox = false;
      if (len > maxCheckedCount) {
        setCheckedKeys(checkedKeys.slice(0, maxCheckedCount));
        disableCheckbox = true;
      } else if (len === maxCheckedCount) {
        disableCheckbox = true;
      }
      handleChangeDisableCheckboxRef.current?.(disableCheckbox);
    }
  }, [checkedKeys, handleChangeDisableCheckboxRef, maxCheckedCount, treeData]);

  const onCheck = useMemo(() => {
    const func: ResourceTreeProps['onCheck'] = (checkedKeys, info) => {
      setCheckedKeys(Array.isArray(checkedKeys) ? checkedKeys : checkedKeys.checked);
      treeProps.onCheck?.(checkedKeys, info);
    };
    return func;
  }, [treeProps]);

  // 因 onChange 无法获取当前节点的类型，故使用 onSelect 与 onClear 替代
  const handleDeviceTypeChange = useMemo(() => {
    const func = (val: unknown, option: unknown) => {
      resetTreeData();
      /** 不包含资产分类的 params */
      const paramsWithoutDeviceType = omit(localFetchDevicesParamsRef.current, [
        'deviceTypeList',
        'topCategory',
        'secondCategory',
      ]);
      if (val === undefined) {
        setDeviceType(undefined);
        localFetchDevicesParamsRef.current = paramsWithoutDeviceType;
        return;
      }
      const node = option as { type: 'C0' | 'C1' | 'C2' };
      const valStr = `${val}`;
      setDeviceType(valStr);
      if (node.type === 'C2') {
        // 三级分类
        localFetchDevicesParamsRef.current = {
          ...paramsWithoutDeviceType,
          deviceTypeList: [valStr],
        };
        return;
      }
      if (node.type === 'C1') {
        // 二级分类
        localFetchDevicesParamsRef.current = {
          ...paramsWithoutDeviceType,
          secondCategory: valStr,
        };
        return;
      }
      if (node.type === 'C0') {
        // 一级分类
        localFetchDevicesParamsRef.current = {
          ...paramsWithoutDeviceType,
          topCategory: valStr,
        };
        return;
      }
    };
    return func;
  }, [resetTreeData]);

  const handleRoomTypeChange = useCallback(
    (val: string) => {
      setRoomType(val);
      resetTreeData();
    },
    [resetTreeData]
  );

  const handleMatchNode = debounce((searchValue: string, resourceTreeNodes: TreeNode[]) => {
    if (!searchValue) {
      setTreeData(resourceTreeNodes);
      return;
    }
    const cloneResourcesTree = cloneDeep(resourceTreeNodes);
    const matchExpandKeys: string[] = [];
    const filterRoomTreeData = filterTree(cloneResourcesTree, node => {
      if (node.type === 'ROOM') {
        const strTitle = node.title! as string;
        const index = strTitle.indexOf(searchValue);
        const isMatch = index > -1;
        return isMatch;
      }
      return true;
    });

    const _treeData = filterTree(filterRoomTreeData, node => {
      if (node.type === 'ROOM_TYPE') {
        // 包间类型也按需过滤(子包间不存在)
        return !!node.children?.length;
      }
      return true;
    });

    const loop = (data: TreeNode[]): TreeNode[] =>
      data.map(item => {
        let _title = item.title;
        if (item.type === 'ROOM') {
          const strTitle = item.title! as string;
          const index = strTitle.indexOf(searchValue);
          const beforeStr = strTitle.substring(0, index);
          const afterStr = strTitle.slice(index + searchValue.length);
          const isMatch = index > -1;
          if (isMatch) {
            matchExpandKeys.push(item.parentKey);
          }

          _title = isMatch ? (
            <Typography.Text>
              {beforeStr}
              <Typography.Text type="danger">{searchValue}</Typography.Text>
              {afterStr}
            </Typography.Text>
          ) : (
            strTitle
          );
        }

        if (item.children) {
          return {
            ...item,
            title: _title,
            children: loop(item.children),
          };
        }

        return {
          ...item,
          title: _title,
        };
      });
    const _data = loop(_treeData!);
    setTreeData(_data);
    setInternalExpandedKeys(matchExpandKeys);
    setAutoExpandParent(true);
  }, 500);

  return (
    <Space
      style={style}
      className={classnames(styles.resourceTreeContainer, wrapperClassName)}
      size="middle"
      direction="vertical"
    >
      <Space style={{ width: '100%' }} direction="vertical">
        {(showFilter === true || (showFilter !== false && showFilter.roomType !== false)) && (
          <RoomTypeSelect
            style={{ width: '100%' }}
            placeholder="包间类型"
            value={roomType}
            onChange={handleRoomTypeChange}
          />
        )}
        {(showFilter === true || (showFilter !== false && showFilter.deviceType !== false)) && (
          <DeviceTypeCascader
            style={{ width: '100%' }}
            disabledTypeList={['C3']}
            value={deviceType}
            dataType={['space', 'snDevice']}
            placeholder="资产分类"
            allowClear
            numbered
            onSelect={handleDeviceTypeChange}
            onClear={() => handleDeviceTypeChange(undefined, undefined)}
          />
        )}
        {showSearch ? (
          onlySearchRoom ? (
            <Input.Search
              style={{ width: '100%' }}
              placeholder="搜索空间"
              onChange={e => {
                handleMatchNode(e.target.value, resourceTreeNodes);
              }}
            />
          ) : (
            <Input.Search
              style={{ width: '100%' }}
              value={searchValue}
              placeholder="搜索包间或设备"
              onChange={e => onSearchValueChange(e.target.value)}
            />
          )
        ) : null}
      </Space>

      <div className={styles.container}>
        <Spin
          key="searched"
          wrapperClassName={!treeVisible ? styles.spin : styles.hidden}
          spinning={searching}
        >
          <SearchedOptions
            className={styles.searchedOptions}
            options={searchedOptions}
            onSelect={handleSelectSearchedOption}
          />
        </Spin>
        <Spin
          key="tree"
          wrapperClassName={classnames(styles.spin, !treeVisible && styles.hidden)}
          spinning={spinning}
        >
          <Tree
            {...treeProps}
            ref={treeRef}
            autoExpandParent={autoExpandParent}
            treeData={treeData}
            expandedKeys={internalExpandedKeys}
            selectedKeys={internalSelectedKeys}
            checkedKeys={checkedKeys}
            loadedKeys={loadedKeys}
            loadData={onLoadData}
            onExpand={onExpand}
            onSelect={onSelect}
            onCheck={onCheck}
          />
        </Spin>
      </div>
    </Space>
  );
}
