@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.resourceTreeContainer {
  display: flex;
  height: 100%;
  flex-direction: column;
  min-width: 296px;
  padding: @padding-md;

  > :global(.@{prefixCls}-space-item) {
    &:last-child {
      flex: 1;
      overflow: auto;
    }
  }
}

.container {
  position: relative;
  height: 100%;

  .searchedOptions {
    width: 100%;
    height: 100%;
    min-height: 120px;
  }
}

.spin {
  height: 100%;

  &.hidden {
    visibility: hidden;
  }

  > :global(.@{prefixCls}-spin-container) {
    height: 100%;
  }
}

.deviceName {
  margin-right: @margin-xs;
}

.hidden {
  display: none;
}
