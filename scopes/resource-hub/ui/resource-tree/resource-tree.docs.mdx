---
description: '空间树'
labels: ['react', 'resource', 'tree', 'ResourceTree']
---

## ResourceTree

提供[当前登录用户有权限的]空间树展示、节点搜索、定位空间视图能力

### ResourceTree Props

| 参数 | 说明 | 类型 | 默认值 | 版本 |  
| authorizedOnly | 是否只展示当前用户有权限的空间数据 | boolean | 无 | |  
| treeMode | 空间树展示的节点层级 | Array | [`REGION`, `IDC`, `BLOCK`, `ROOM`, `DEVICE`] | 默认[`IDC`, `BLOCK`, `ROOM`, `DEVICE`] | REGION 地域
| spaceGuid | 展示某个空间节点下的数据 | string | 无 | 无 |  
| style | 组件自定义样式 | React.CSSProperties | {} | 无 |

其他参数，参考：  
manyun.base-ui/ui/tree
onSelect 获取所需要的数据
设备 Keys `${roomGuid}.${guid}`

### 使用

```js

<ResourceTree />
展示EC06下所有节点
<ResourceTree spaceGuid="EC06" treeMode={['BLOCK', `ROOM`, `DEVICE`]} />

<ResourceTree
  authorizedOnly
  style={{
    padding: 0,
    height: 'calc(var(--content-height) - 100px)',
    overflowY: 'hidden',
  }}
  height={644}
  treeMode={['BLOCK', 'ROOM', 'DEVICE']}
/>

```
