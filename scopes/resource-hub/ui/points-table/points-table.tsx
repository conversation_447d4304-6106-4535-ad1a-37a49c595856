import React, { useCallback, useMemo } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Empty } from '@manyun/base-ui.ui.empty';
import type { ColumnsType, TablePaginationConfig } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { PointsLineModalButton } from '@manyun/monitoring.chart.points-line';
import { PointsStateLineModalButton } from '@manyun/monitoring.chart.points-state-line';
import type { Point, PointObject } from '@manyun/monitoring.model.point';
import {
  getMonitoringData,
  addRealtimeNAlarmsDataSubscriptionActionCreator as subscribeAction,
  removeRealtimeNAlarmsDataSubscriptionActionCreator as unsubscribeAction,
} from '@manyun/monitoring.state.subscriptions';
import { PointDataRenderer } from '@manyun/monitoring.ui.point-data-renderer';
import { PointMonitorItemModalView } from '@manyun/monitoring.ui.point-monitor-item-modal-view';
import { generateGetPointMonitoringData } from '@manyun/monitoring.util.get-monitoring-data';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

export const getPointsTableColumns = ({
  deviceGuid,
  deviceType,
  spaceGuid,
  blockGuid,
  renderPointValue,
}: {
  deviceGuid: string;
  deviceType: string;
  blockGuid: string;
  spaceGuid: string;
  renderPointValue: (point: Point) => JSX.Element | string;
}): ColumnsType<Point> => [
  {
    title: '测点编号',
    dataIndex: 'code',
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    render: (name: string) => {
      return (
        <div style={{ maxWidth: 504 }}>
          <Typography.Text ellipsis={{ tooltip: name }}>{name}</Typography.Text>
        </div>
      );
    },
  },
  {
    title: '测点类型',
    dataIndex: 'dataType',
    filters: [
      {
        text: '模拟量读点',
        value: 'AI',
      },
      {
        text: '模拟量写点',
        value: 'AO',
      },
      {
        text: '状态量读点',
        value: 'DI',
      },
      {
        text: '状态量写点',
        value: 'DO',
      },
      {
        text: '告警点',
        value: 'ALARM',
      },
    ],
    filterSearch: true,
    onFilter: (value: string | number | boolean, record: Point) => {
      if (typeof value === 'string') {
        return record?.dataType?.code.startsWith(value);
      }
      return false;
    },
    render: (dataType: PointObject) => dataType.name,
  },
  {
    title: '实时值',
    dataIndex: 'value',
    render: (value: string, point: Point) => {
      return renderPointValue(point);
    },
  },
  {
    title: '告警规则',
    key: 'threshold',
    fixed: 'right',
    render: (__: string, { name, code }: Point) => {
      return (
        <PointMonitorItemModalView
          text="查看"
          modalTitle={`${name}-告警规则列表`}
          pointData={{
            pointCode: code,
            deviceGuid: deviceGuid,
            deviceType: deviceType,
            spaceGuid: spaceGuid,
            blockGuid,
          }}
        />
      );
    },
  },
];

export type AdvancedHideNoDataItems = {
  /**
   * 隐藏空数据的一般测点（即非扩展测点）
   *
   * @defaultValue `false`
   */
  generics?: boolean;
  /**
   * 隐藏空数据的宿主测点（即扩展测点的爸爸）
   *
   * @defaultValue `false`
   */
  hosts?: boolean;
  /**
   * 隐藏空数据的扩展测点
   *
   * @defaultValue `false`
   */
  extensions?: boolean;
};

export type PointsTableProps = {
  spaceGuid: string;
  /**
   * `points` 爸爸的 GUID
   */
  guid: string;
  points: Point[];
  /**
   * 如果 `guid` 是空间的话，需要传入空间对应的虚拟设备类型
   */
  deviceType?: string;
  /**
   * 隐藏空数据项
   *
   * @defaultValue `false`
   */
  hideNoDataItems?: boolean | AdvancedHideNoDataItems;
  pagination?: false | TablePaginationConfig;
};

export function PointsTable({
  spaceGuid,
  points,
  deviceType,
  guid = spaceGuid,
  hideNoDataItems = false,
  pagination,
}: PointsTableProps) {
  const dispatch = useDispatch();

  const { idc, block } = getSpaceGuidMap(spaceGuid);
  const blockGuid = getSpaceGuid(idc!, block!)!;

  React.useEffect(() => {
    if (deviceType) {
      dispatch(
        syncCommonDataAction({
          strategy: { deviceTypesPointsDefinition: [deviceType] },
        })
      );
    }
  }, [dispatch, deviceType]);

  React.useEffect(() => {
    const moduleId = `${spaceGuid}_$$_${guid}_$$_points-table`;

    dispatch(
      subscribeAction({
        mode: 'REALTIME_DATA_N_ALARMS_DATA',
        moduleId,
        blockGuid,
        deviceGuids: [guid],
      })
    );

    return () => {
      dispatch(
        unsubscribeAction({
          mode: 'REALTIME_DATA_N_ALARMS_DATA',
          moduleId,
          blockGuid,
        })
      );
    };
  }, [dispatch, spaceGuid, guid, blockGuid]);

  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  // FIXME @Jerry Use the exact `Space Device Type` rather than the fixed `SPACE_ROOM`
  const curDeviceType =
    deviceType ?? configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_ROOM)!;

  const { devicesRealtimeData, devicesAlarmsData } = useSelector(getMonitoringData);
  const getPointMonitoringData = generateGetPointMonitoringData({
    realtimeData: devicesRealtimeData,
    alarmsData: devicesAlarmsData,
    configUtil,
  });

  const renderPointValue = useCallback(
    (point: Point) => {
      if (!point) {
        return '--';
      }
      const isAnalogSignal = point.dataType.code === 'AI' || point.dataType.code === 'AO';
      const realTimeData = getPointMonitoringData(
        { deviceGuid: guid, deviceType: curDeviceType },
        isAnalogSignal
          ? {
              hardCodedPointCode: point.code,
              formatted: true,
            }
          : {
              hardCodedPointCode: point.code,
              reflected: true,
              validLimits: point.validLimits,
            }
      );
      const btn = <PointDataRenderer key={'renderer_' + point.code} data={realTimeData} />;
      const pointGuids = [{ deviceGuid: guid, pointCode: point.code, unit: point.unit }];
      const seriesOption = [{ name: point.name }];
      return isAnalogSignal ? (
        <PointsLineModalButton
          idcTag={idc!}
          btnText={btn}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
        />
      ) : (
        <PointsStateLineModalButton
          idcTag={idc!}
          btnText={btn}
          modalText={point.name}
          pointGuids={pointGuids}
          seriesOption={seriesOption}
          validLimitsMap={point.validLimits}
        />
      );
    },
    [curDeviceType, guid, getPointMonitoringData, idc]
  );

  const tableColumns = useMemo(
    () =>
      getPointsTableColumns({
        deviceGuid: guid,
        deviceType: curDeviceType,
        spaceGuid,
        blockGuid,
        renderPointValue,
      }),
    [guid, blockGuid, curDeviceType, spaceGuid, renderPointValue]
  );

  if (!points.length) {
    return (
      <div style={{ width: '100%', padding: '0 45px' }}>
        <Empty />
      </div>
    );
  }

  const visiblePoints =
    hideNoDataItems && !env.__DEBUG_MODE__
      ? points.filter(point => {
          const data = getPointMonitoringData(
            { deviceGuid: guid, deviceType: curDeviceType },
            { hardCodedPointCode: point.code }
          );

          if (typeof hideNoDataItems == 'boolean') {
            return hideNoDataItems && !data.isBlank;
          }

          const { generics = false, hosts = false, extensions = false } = hideNoDataItems;
          if (generics) {
            return !(point.extCount === null && !point.isSub);
          }
          if (hosts) {
            return !(point.extCount !== null);
          }
          if (extensions) {
            return !point.isSub;
          }

          return !data.isBlank;
        })
      : points;

  return (
    <Table
      rowKey="id"
      dataSource={visiblePoints}
      columns={tableColumns}
      scroll={{ x: 'max-content' }}
      pagination={false}
    />
  );
}
