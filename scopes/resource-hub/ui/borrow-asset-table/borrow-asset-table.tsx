/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-20
 *
 * @packageDocumentation
 */
import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import type { BorrowAsset } from '@manyun/resource-hub.service.fetch-borrow-assets';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';

const getBorrowAssetColumns = ({
  assetNoTitle,
  deviceTypeTitle,
}: {
  assetNoTitle?: '资产ID' | 'SN';
  deviceTypeTitle?: '三级分类' | '物资分类';
}) => {
  const borrowAssetColumns: ColumnType<BorrowAsset>[] = [
    {
      title: '设备编号',
      dataIndex: 'deviceName',
      render: deviceName => {
        return deviceName ?? '--';
      },
    },
    {
      title: assetNoTitle,
      dataIndex: 'assetNo',
      fixed: 'left',
      render: (_, record) => {
        if (record.assetType === 'OFF_LINE') {
          return record.assetNo;
        }
        if (!record.assetNo || !record.deviceGuid) {
          return '--';
        }
        return (
          <SpaceOrDeviceLink id={record.deviceGuid} type="DEVICE_GUID" text={record.assetNo} />
        );
      },
    },
    {
      title: deviceTypeTitle,
      dataIndex: 'deviceType',
      render: (deviceType, record) => {
        if (record.assetType === 'OFF_LINE') {
          return deviceType;
        }
        return <DeviceTypeText code={deviceType} />;
      },
    },
    {
      title: '品牌',
      dataIndex: 'vendor',
    },
    {
      title: '型号',
      dataIndex: 'productModel',
    },
    {
      title: '借用至目的位置',
      dataIndex: 'roomGuid',
    },
    {
      title: '借出数量',
      dataIndex: 'borrowNum',
    },
    {
      title: '待还数量',
      dataIndex: 'toBeReturned',
      render: (_, record) => {
        return record.borrowNum - record.returnNum;
      },
    },
    {
      title: '领用人',
      dataIndex: 'ownerId',
      render: text => <UserLink userId={text} />,
    },
  ];
  return borrowAssetColumns;
};

export type ColumnDataIndex =
  | 'deviceName'
  | 'assetNo'
  | 'deviceType'
  | 'vendor'
  | 'productModel'
  | 'roomGuid'
  | 'borrowNum'
  | 'toBeReturned'
  | 'ownerId';

export type BorrowAssetTableProps = {
  /*
    dataIndexs默认线上，根据传的dataIndexs顺序展示
    线上设备: 'deviceName', 'assetNo', 'deviceType', 'vendor', 'productModel', 'roomGuid', 'ownerId'
    线上耗材: 'deviceType', 'vendor', 'productModel', 'borrowNum', 'toBeReturned', 'ownerId'
    线下: 'deviceType', 'assetNo', 'vendor', 'productModel', 'borrowNum', 'toBeReturned', 'ownerId'
  */
  dataIndexs?: ColumnDataIndex[];
  assetNoTitle?: '资产ID' | 'SN';
  deviceTypeTitle?: '三级分类' | '物资分类';
  operation?: ColumnType<BorrowAsset>;
} & Omit<TableProps<BorrowAsset>, 'columns'>;

export function BorrowAssetTable({
  dataIndexs = [
    'deviceName',
    'assetNo',
    'deviceType',
    'vendor',
    'productModel',
    'roomGuid',
    'ownerId',
  ],
  /*用于线上线下column的title展示不一致，以及三级分类render不同*/
  assetNoTitle = '资产ID',
  deviceTypeTitle = '三级分类',
  operation,
  ...rest
}: BorrowAssetTableProps) {
  const columns = useDeepCompareMemo(() => {
    const newColumns = dataIndexs
      .map(dataIndex => {
        return getBorrowAssetColumns({ assetNoTitle, deviceTypeTitle }).find(
          item => item.dataIndex === dataIndex
        );
      })
      .filter((item): item is ColumnType<BorrowAsset> => item !== undefined);

    if (operation) {
      return [...newColumns, operation];
    }
    return newColumns;
  }, [dataIndexs, operation]);

  return (
    <Table
      rowKey="id"
      columns={columns}
      scroll={{ x: 'max-content' }}
      tableLayout="fixed"
      {...rest}
    />
  );
}
