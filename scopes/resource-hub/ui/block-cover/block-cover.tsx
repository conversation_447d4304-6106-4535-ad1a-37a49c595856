import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';

import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

export type BlockCoverProps = {
  blockGuid?: string;
  className?: string;
  src?: string;
};

/** 楼栋图片 */
export function BlockCover({ className, blockGuid, src }: BlockCoverProps) {
  const config = useSelector(selectCurrentConfig);
  const configUtil = new ConfigUtil(config);
  const dashboardsConfig = configUtil.getDashboardsConfig();
  const defaultCover = dashboardsConfig.idc.assets.backgroundImageUrl;
  const [blockCover, setBlockCover] = useState(defaultCover.fallback);

  const getBlockCover = useCallback(async () => {
    if (blockGuid && !src) {
      const { error, data } = await fetchBizFileInfos({
        targetId: blockGuid,
        targetType: 'BLOCK',
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data?.length) {
        const fileInfo = data.data[0];
        setBlockCover(fileInfo.src);
      } else {
        setBlockCover(defaultCover.fallback);
      }
    }
  }, [blockGuid, defaultCover, src]);

  useEffect(() => {
    getBlockCover();
  }, [getBlockCover]);

  return <img className={className} src={src || blockCover} alt={blockGuid} />;
}
