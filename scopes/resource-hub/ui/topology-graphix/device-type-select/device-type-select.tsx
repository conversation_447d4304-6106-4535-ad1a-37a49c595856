import React from 'react';

import { observer } from 'mobx-react-lite';

import { Select } from '@manyun/base-ui.ui.select';
import type { DefaultOptionType, RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { StoreInstance } from '@manyun/dc-brain.aura-graphix';

export type DeviceTypeSelectProps = {
  store: StoreInstance;
  deviceTypeMap: Record<string, { metaName: string }> | undefined;
} & Omit<SelectProps<string>, 'options'>;

function DeviceTypeSelect(
  { store, deviceTypeMap, size = 'small', ...restProps }: DeviceTypeSelectProps,
  ref?: React.ForwardedRef<RefSelectProps>
) {
  const options = store.activePage
    ?._getFlatChildren()
    ?.reduce<DefaultOptionType[]>((_options, element) => {
      if (element.custom?.type === 'device') {
        const { deviceType } = element.custom;
        if (!_options.some(option => option.value === deviceType)) {
          _options.push({
            label: deviceTypeMap?.[deviceType]?.metaName ?? deviceType,
            value: deviceType,
          });
        }
      }

      return _options;
    }, []);

  return <Select<string> ref={ref} size={size} options={options} {...restProps} />;
}

const RefForwardedDeviceTypeSelect = React.forwardRef(DeviceTypeSelect);
RefForwardedDeviceTypeSelect.displayName = 'DeviceTypeSelect';
export const ObservedDeviceTypeSelect = observer(RefForwardedDeviceTypeSelect);
