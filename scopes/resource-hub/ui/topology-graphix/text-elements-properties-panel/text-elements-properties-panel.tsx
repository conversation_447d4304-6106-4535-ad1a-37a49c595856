import React from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import type { Collapse } from '@manyun/base-ui.ui.collapse';
import type { Divider } from '@manyun/base-ui.ui.divider';

import type { StoreInstance, TextElementInstance } from '@manyun/dc-brain.aura-graphix';
import { TextElementProperties } from '@manyun/dc-brain.aura-graphix';

import { ObservedDeviceTypeSelect } from './../device-type-select/device-type-select';

export type TextPropsConfig = {
  textElements: boolean;
  pointElements: boolean;
};

export type Options = {
  Panel: typeof Collapse.Panel;
  BlockProp: {
    Wrapper: React.ComponentType;
    Name: React.ComponentType;
    Value: React.ComponentType;
  };
  Divider: typeof Divider;
  store: StoreInstance;
  deviceTypeMap: Record<string, { metaName: string }> | undefined;
  selectedDeviceType: string | undefined;
  textPropsConfig: TextPropsConfig;
  textElements: TextElementInstance[];
  pointElements: TextElementInstance[];
  onDeviceTypeChange: (deviceType: string) => void;
  onTextPropsConfigChange: (config: Partial<TextPropsConfig>) => void;
};

export function getTextElementsPropertiesPanel({
  Panel,
  BlockProp,
  Divider,
  store,
  deviceTypeMap,
  selectedDeviceType,
  textPropsConfig,
  textElements,
  pointElements,
  onDeviceTypeChange,
  onTextPropsConfigChange,
}: Options) {
  return (
    <Panel key="text-elements-properties-by-device-type" header="文本设置（按设备类型）">
      <BlockProp.Wrapper>
        <BlockProp.Name>设备类型</BlockProp.Name>
        <BlockProp.Value>
          <ObservedDeviceTypeSelect
            style={{ textAlign: 'left', width: '100%' }}
            store={store}
            deviceTypeMap={deviceTypeMap}
            value={selectedDeviceType}
            onChange={onDeviceTypeChange}
          />
        </BlockProp.Value>
      </BlockProp.Wrapper>
      <Divider />
      <BlockProp.Wrapper>
        <BlockProp.Name>生效范围</BlockProp.Name>
        <BlockProp.Value>
          <Checkbox
            checked={textPropsConfig.textElements}
            onChange={evt => {
              onTextPropsConfigChange({
                textElements: evt.target.checked,
              });
            }}
          >
            备注
          </Checkbox>
          <Checkbox
            checked={textPropsConfig.pointElements}
            onChange={evt => {
              onTextPropsConfigChange({
                pointElements: evt.target.checked,
              });
            }}
          >
            测点
          </Checkbox>
        </BlockProp.Value>
      </BlockProp.Wrapper>
      {(textPropsConfig.textElements || textPropsConfig.pointElements) &&
        (textElements.length > 0 || pointElements.length > 0) && (
          <>
            <Divider />
            <TextElementProperties
              autoWidth
              store={store}
              elements={[
                ...(textPropsConfig.textElements ? textElements : []),
                ...(textPropsConfig.pointElements ? pointElements : []),
              ]}
            />
          </>
        )}
    </Panel>
  );
}
