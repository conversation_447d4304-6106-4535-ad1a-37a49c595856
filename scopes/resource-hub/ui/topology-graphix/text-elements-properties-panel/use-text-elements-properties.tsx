import React from 'react';

import type { StoreInstance, TextElementInstance } from '@manyun/dc-brain.aura-graphix';
import { relatedElementUtil } from '@manyun/monitoring.state.topology';

type DeviceElementCustomProps = {
  deviceType: string;
  remarkTextRowKeys?: string[];
  visiblePointCodes?: string[];
};

export function useTextElementsProperties(store: StoreInstance | null) {
  const [selectedDeviceType, setSelectedDeviceType] = React.useState<string>();
  const { textElements, pointElements } = React.useMemo(() => {
    const elements = store?.activePage?._getFlatChildren();
    // const _deviceElements = [];
    const _textElements: TextElementInstance[] = [];
    const _pointElements: TextElementInstance[] = [];

    if (!selectedDeviceType || !elements) {
      return {
        // deviceElements: _deviceElements,
        textElements: _textElements,
        pointElements: _pointElements,
      };
    }

    elements.forEach(element => {
      if (element.custom?.type === 'device') {
        const {
          deviceType,
          remarkTextRowKeys = [],
          visiblePointCodes = [],
        } = element.custom as DeviceElementCustomProps;
        if (deviceType === selectedDeviceType) {
          // _deviceElements.push(element);

          const remarkTextElementIds = remarkTextRowKeys.map(remarkTextRowKey =>
            relatedElementUtil.join(element.id, remarkTextRowKey)
          );
          const pointElementIds = visiblePointCodes.map(visiblePointCode =>
            relatedElementUtil.join(element.id, visiblePointCode)
          );
          elements.forEach(elem => {
            if (remarkTextElementIds.includes(elem.id)) {
              _textElements.push(elem as TextElementInstance);
            } else if (pointElementIds.includes(elem.id)) {
              _pointElements.push(elem as TextElementInstance);
            }
          });
        }
      }
    });

    return {
      // deviceElements: _deviceElements,
      textElements: _textElements,
      pointElements: _pointElements,
    };
  }, [selectedDeviceType, store]);

  const [textPropsConfig, setTextPropsConfig] = React.useState({
    textElements: true,
    pointElements: true,
  });

  return [
    { selectedDeviceType, textElements, pointElements, textPropsConfig },
    {
      setSelectedDeviceType,
      setTextPropsConfig,
    },
  ] as const;
}
