import React from 'react';

import { observer } from 'mobx-react-lite';

import { Select } from '@manyun/base-ui.ui.select';

import type {
  GroupElementInstance,
  LineElementInstance,
  PolylineElementInstance,
} from '@manyun/dc-brain.aura-graphix';

import { WaterPipesMapper } from './constants';
import { updateWaterPipeColor } from './utils';

export type PipeTypeSelectProps = {
  element: GroupElementInstance | PolylineElementInstance | LineElementInstance;
};

const _PipeTypeSelect = ({ element }: PipeTypeSelectProps) => {
  return (
    <Select
      style={{ width: '100%' }}
      size="small"
      value={element.custom?.pipeType}
      options={[
        {
          label: '冷冻水出水',
          value: WaterPipesMapper.DELIVERY_CHILLED_WATER,
        },
        {
          label: '冷冻水回水',
          value: WaterPipesMapper.RETURN_CHILLED_WATER,
        },
        {
          label: '冷却水出水',
          value: WaterPipesMapper.DELIVERY_COOLING_WATER,
        },
        {
          label: '冷却水回水',
          value: WaterPipesMapper.RETURN_COOLING_WATER,
        },
      ]}
      onSelect={pipeType => {
        updateWaterPipeColor(element, pipeType);
      }}
    />
  );
};

export const PipeTypeSelect = observer(_PipeTypeSelect);
