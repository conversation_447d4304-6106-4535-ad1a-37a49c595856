import type {
  GroupElementInstance,
  LineElementInstance,
  PolylineElementInstance,
} from '@manyun/dc-brain.aura-graphix';

import { WaterPipesColorsMapper } from './constants';

/**
 * @param element Line or Polyline or PipeNetwork element
 */
export function updateWaterPipeColor(
  element: LineElementInstance | PolylineElementInstance | GroupElementInstance,
  pipeType: keyof typeof WaterPipesColorsMapper
) {
  const stroke = WaterPipesColorsMapper[pipeType];
  if (element.type === 'polyline' || element.type === 'line') {
    element.set({ stroke, custom: { ...element.custom, stroke, pipeType } });
  } else if (element.custom?.type === 'pipe-network') {
    element.set({ custom: { ...element.custom, pipeType } });
    const rect = (element as GroupElementInstance).findOne(
      childElem => childElem.custom?.type === 'pipe-network_rect'
    );
    rect.set({ stroke });
  }
}
