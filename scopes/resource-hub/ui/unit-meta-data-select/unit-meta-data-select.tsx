import React from 'react';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

type Option = {
  label: string;
  value: string;
};

export type UnitMetaDataSelectProps = {
  onOptionsDidUpdate?(options: Option[]): void;
} & Omit<SelectProps, 'options'>;

export function UnitMetaDataSelect({ onOptionsDidUpdate, ...slectProps }: UnitMetaDataSelectProps) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.TYPE_UNIT);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useDeepCompareEffect(() => {
    if (data.data.length > 0) {
      onOptionsDidUpdate?.(data.data);
    }
  }, [data.data, onOptionsDidUpdate]);

  return <Select {...slectProps} options={data.data} />;
}
