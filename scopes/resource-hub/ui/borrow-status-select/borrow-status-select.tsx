import React from 'react';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getBorrowReturnLocales } from '@manyun/resource-hub.model.borrow-return';

export type BorrowStatusSelectProps = Omit<SelectProps, 'options'>;

export const BorrowStatusSelect = React.forwardRef(
  (props: BorrowStatusSelectProps, ref: React.Ref<RefSelectProps>) => {
    const locales = getBorrowReturnLocales();

    const options = useDeepCompareMemo(() => {
      return [
        {
          value: 'WAIT_STOCK_OUT',
          label: locales['borrowStatus']['WAIT_STOCK_OUT'],
        },
        {
          value: 'BORROWING',
          label: locales['borrowStatus']['BORROWING'],
        },
        {
          value: 'RETURN',
          label: locales['borrowStatus']['RETURN'],
        },
        {
          value: 'DRAFT',
          label: locales['borrowStatus']['DRAFT'],
        },
        {
          value: 'APPROVING',
          label: locales['borrowStatus']['APPROVING'],
        },
        {
          value: 'CANCELED',
          label: locales['borrowStatus']['CANCELED'],
        },
      ];
    }, [locales]);

    return <Select {...props} ref={ref} options={options} />;
  }
);

BorrowStatusSelect.displayName = 'BorrowStatusSelect';
