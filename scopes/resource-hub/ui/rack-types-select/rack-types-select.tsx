import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchCabinetType } from '@manyun/resource-hub.service.fetch-cabinet-type';

export type RackTypesSelectProps = Omit<
  SelectProps,
  'filterOption' | 'options' | 'onSearch' | 'loading'
> & { trigger: 'onDidMount' | 'onFocus' };

export const RackTypesSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', onFocus, ...props }: RackTypesSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [gridTypeList, setGridTypeList] = useState<{ label: string; value: string }[]>([]);
    const [loading, setLoading] = useState(false);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        getGridTypeList();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    const getGridTypeList = async () => {
      if (!gridTypeList.length) {
        setLoading(true);
        const { data, error } = await fetchCabinetType();
        setLoading(false);

        if (error) {
          message.error(error.message);
          return;
        }

        const list = Object.entries(data || {}).map(item => ({
          label: item[1],
          value: item[0],
        }));
        setGridTypeList(list);
      }
    };

    return (
      <Select
        {...props}
        ref={ref}
        loading={loading}
        options={gridTypeList}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            getGridTypeList();
          }
          onFocus?.(evt);
        }}
      />
    );
  }
);

RackTypesSelect.displayName = 'RackTypesSelect';
