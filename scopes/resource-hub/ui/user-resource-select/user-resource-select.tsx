import React from 'react';
import { useShallowCompareEffect } from 'react-use';

import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Select } from '@manyun/base-ui.ui.select';
import type { UserResourceModel } from '@manyun/resource-hub.service.fetch-user-resources';

import { type UserResourceProps, useUserResources } from './use-user-resources';

export type UserResourceSelectProps = {
  loading?: boolean;
  data?: UserResourceModel[];
} & Omit<SelectProps<string, UserResourceModel>, 'options' | 'loading'> &
  UserResourceProps;

/**
 * 下拉选择用户资源
 * 默认后端兜掉当前用户的资源数据，支持userId
 * @returns
 */
export function UserResourceSelect(props: UserResourceSelectProps) {
  const {
    resourceType = ['CUSTOMER'],
    onFocus,
    onOptionsUpdate,
    filterDataFunc,
    parentCode,
    needVirtualBlock,
    userId,
    disabledResourceCodes = [],
    data,
    loading,
    ...restProps
  } = props;

  const [{ loading: fetchLoading, data: fetchedData }, { onLoadUserResource }] = useUserResources();

  useShallowCompareEffect(() => {
    if (!data) {
      onLoadUserResource({
        resourceType,
        disabledResourceCodes,
        parentCode,
        needVirtualBlock,
        userId,
        filterDataFunc,
        onOptionsUpdate,
      });
    }
  }, [
    data,
    disabledResourceCodes,
    filterDataFunc,
    needVirtualBlock,
    onLoadUserResource,
    onOptionsUpdate,
    parentCode,
    resourceType,
    userId,
  ]);

  return (
    <Select<string, UserResourceModel>
      showSearch
      fieldNames={{ value: 'resourceCode', label: 'resourceName' }}
      {...restProps}
      options={data ?? fetchedData}
      loading={fetchLoading || loading}
    />
  );
}
