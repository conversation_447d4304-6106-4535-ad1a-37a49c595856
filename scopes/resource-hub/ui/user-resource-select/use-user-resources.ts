import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { fetchUserResources } from '@manyun/resource-hub.service.fetch-user-resources';
import type {
  ApiQ,
  UserResouceTypes,
  UserResourceModel,
} from '@manyun/resource-hub.service.fetch-user-resources';

export type UserResourceProps = {
  disabledResourceCodes?: string[];
  resourceType: UserResouceTypes[];
  filterDataFunc?: (data: UserResourceModel) => boolean;
  onOptionsUpdate?: (data: UserResourceModel[]) => void;
} & Pick<ApiQ, 'parentCode' | 'needVirtualBlock' | 'userId' | 'searchName'>;

export const useUserResources = () => {
  const [data, setData] = useState<UserResourceModel[]>([]);
  const [loading, setLoading] = useState(false);

  const onLoadUserResource = useCallback(
    async ({
      resourceType = ['CUSTOMER'],
      disabledResourceCodes = [],
      parentCode,
      needVirtualBlock,
      userId,
      filterDataFunc,
      onOptionsUpdate,
    }: UserResourceProps) => {
      setLoading(true);
      const { error, data } = await fetchUserResources({
        resourceTypes: resourceType,
        ...(parentCode && { parentCode }),
        ...(userId && { userId }),
        ...(typeof needVirtualBlock === 'boolean' && { needVirtualBlock }),
      });
      setLoading(false);
      if (error) {
        setData([]);
        onOptionsUpdate?.([]);
        message.error(error.message);
        return;
      }

      let filteredData = data?.data ?? [];

      if (filterDataFunc) {
        filteredData = filteredData.filter(item => filterDataFunc(item));
      }

      if (filteredData.length > 0) {
        const updatedData = disabledResourceCodes.length
          ? filteredData.map(item => ({
              ...item,
              disabled: disabledResourceCodes.includes(item.resourceCode),
            }))
          : filteredData;

        setData(updatedData);
        onOptionsUpdate?.(updatedData);
      } else {
        setData([]);
        onOptionsUpdate?.([]);
      }
    },
    []
  );

  return [{ loading, data }, { onLoadUserResource }] as const;
};
