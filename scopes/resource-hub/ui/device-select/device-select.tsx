import debounce from 'lodash.debounce';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDeepCompareEffect, useLatest } from 'react-use';

import { message } from '@manyun/base-ui.ui.message';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Select } from '@manyun/base-ui.ui.select';
import { Device } from '@manyun/resource-hub.model.device';
import type { AssetState } from '@manyun/resource-hub.model.device';
import type { ApiQ } from '@manyun/resource-hub.service.fetch-page-devices';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';

export type DeviceSelectProps = Omit<
  SelectProps,
  'filterOption' | 'options' | 'onSearch' | 'loading'
> & {
  idcTag?: string;
  blockTag?: string;
  assetState?: AssetState;
  deviceTypeList?: string[];
  vendor?: string;
  productModel?: string;
  disabledDeviceGuids?: string[];
  /** 列表设备是否显示label */
  showDeviceLabel?: boolean;
};

/**
 * 下拉搜索设备列表
 * @returns
 */
export function DeviceSelect(props: DeviceSelectProps) {
  const {
    placeholder = '请输入设备编号',
    fieldNames,
    value,
    idcTag,
    blockTag,
    assetState,
    deviceTypeList,
    vendor,
    productModel,
    labelInValue,
    onChange,
    disabledDeviceGuids,
    showDeviceLabel,
    ...restProps
  } = props;
  const [devices, setDevices] = useState<Device[]>([]);
  const [loading, setLoading] = useState(false);

  const onChangeRef = useLatest(onChange);

  const getDeivceList = useCallback(
    async (params: ApiQ) => {
      setLoading(true);
      const { data, error: fetchDeviceListError } = await fetchPageDevices({
        ...params,
        idcTag,
        blockTag,
        assetStatus: assetState,
        deviceTypeList,
        vendor,
        productModel,
      });
      setLoading(false);
      if (fetchDeviceListError) {
        setDevices([]);
        message.error(fetchDeviceListError.message);
        return;
      }
      if (data?.data.length) {
        const devices = data.data.map(item => {
          const device = {
            ...item.toApiObject(),
            name:
              item.spaceGuid.roomTag && showDeviceLabel
                ? `${item.deviceLabel}（${item.spaceGuid.roomTag}）`
                : item.spaceGuid.roomTag
                  ? `${item.name}（${item.spaceGuid.roomTag}）`
                  : item.name,
          };
          return {
            ...Device.fromApiObject(device),
            disabled: disabledDeviceGuids?.includes(item.guid),
          };
        });
        setDevices(devices as unknown as Device[]);
      } else {
        setDevices([]);
      }
      return data.data;
    },
    [
      idcTag,
      blockTag,
      assetState,
      deviceTypeList,
      vendor,
      productModel,
      showDeviceLabel,
      disabledDeviceGuids,
    ]
  );

  const device = useMemo(
    () =>
      labelInValue
        ? devices.find(item => item.guid === value?.value)
        : devices.find(item => item.guid === value),
    [devices, labelInValue, value]
  );
  const deviceRef = useLatest(device);
  const valueRef = useLatest(value);
  const guid = labelInValue ? value?.value : value;
  useDeepCompareEffect(() => {
    if (!valueRef.current || deviceRef.current) {
      return;
    }
    getDeivceList({
      guid: typeof guid === 'string' ? guid : undefined,
      deviceGuidList: typeof guid === 'string' ? undefined : guid,
      pageNum: 1,
      pageSize: Array.isArray(guid) ? guid.length : 1,
    }).then(devices => {
      // labelInValue 初始值回填
      if (labelInValue && typeof valueRef.current?.guid === 'undefined') {
        onChangeRef.current?.(
          {
            ...valueRef.current,
            ...devices?.find(item => item.guid === guid),
          },
          {} as never
        );
      }
    });
  }, [getDeivceList, guid, valueRef, deviceRef, labelInValue, onChangeRef, disabledDeviceGuids]);

  // 当机房或楼栋改变时，清空之前查询到的设备
  useEffect(() => {
    setDevices([]);
  }, [blockTag, idcTag]);

  const handleSearch = useCallback(
    (newValue: string) => {
      if (newValue) {
        getDeivceList({ nameOrLabel: newValue, pageNum: 1, pageSize: 50 });
      } else {
        setDevices([]);
      }
    },
    [getDeivceList]
  );

  const onSearch = React.useMemo(() => debounce(handleSearch, 300), [handleSearch]);

  const changeHandler: DeviceSelectProps['onChange'] = (value, node) => {
    // 若 labelInValue 为 false 或清空时，直接执行 onChange
    if (!labelInValue || value === undefined || typeof value !== 'object') {
      onChangeRef.current?.(value, node);
      return;
    }
    onChangeRef.current?.(
      {
        ...value,
        ...devices.find(item => item.guid === value?.value),
      },
      node
    );
  };

  return (
    <Select
      showSearch
      fieldNames={{
        label: 'name',
        value: 'guid',
        ...fieldNames,
      }}
      placeholder={placeholder}
      value={value}
      {...restProps}
      loading={loading}
      labelInValue={labelInValue}
      filterOption={false}
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      options={devices as any[]}
      onSearch={onSearch}
      onChange={changeHandler}
    />
  );
}
