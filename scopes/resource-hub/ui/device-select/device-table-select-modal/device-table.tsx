import React, { useCallback, useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { VendorModelSelect } from '@manyun/crm.ui.vendor-model-select';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import type { RelateDevice } from './device-select-type';

export type DeviceParams = {
  pageNum: number;
  pageSize: number;
  vendor?: string;
  productModel?: string;
  roomTags?: string[];
  deviceTypeList?: string[];
  nameOrLabel?: string;
};
type DeviceTableProps = {
  idcTag: string;
  blockTag?: string | null;
  dataSource?: RelateDevice[];
  isCheckableMode?: boolean;
  selectedRows: RelateDevice[];
  deviceType?: string | null;
  setSelectedRows?: (params: ((param: RelateDevice[]) => RelateDevice[]) | RelateDevice[]) => void;
  setIsCheckableMode?: (value: boolean) => void;
} & TableProps<RelateDevice>;

const basicColumn: Array<ColumnType<RelateDevice>> = [
  {
    title: '设备编号',
    dataIndex: 'deviceName',
    key: 'deviceName',
  },
  {
    title: '设备名称',
    dataIndex: 'deviceLabel',
    key: 'deviceLabel',
  },
  {
    title: '包间',
    dataIndex: 'roomTag',
    key: 'roomTag',
  },
  {
    title: '三级分类',
    dataIndex: 'deviceType',
    key: 'deviceType',
    render: (_, { deviceType }) => {
      return <DeviceTypeText code={deviceType} />;
    },
  },

  {
    title: '厂商',
    dataIndex: 'vendor',
    key: 'vendor',
  },
  {
    title: '型号',
    dataIndex: 'productModel',
    key: 'productModel',
  },
];

export function DeviceTable({
  idcTag,
  blockTag,
  dataSource,
  isCheckableMode,
  selectedRows,
  setSelectedRows,
  setIsCheckableMode,
  deviceType,
  ...props
}: DeviceTableProps) {
  const [deviceData, setDeviceData] = useState<RelateDevice[]>([]);
  const [total, setTotal] = useState<number>(0);
  const [loading, setLoading] = useState<boolean>(false);
  const [filterValue, setFilterValue] = useState<DeviceParams>({
    pageNum: 1,
    pageSize: 10,
  });

  const showRowSelectionNFilter = React.useMemo(
    () => (typeof isCheckableMode !== 'undefined' ? !isCheckableMode : true),
    [isCheckableMode]
  );

  const getDeivceList = useCallback(async () => {
    setLoading(true);
    const { data, error: fetchDeviceListError } = await fetchPageDevices({
      ...filterValue,
      spaceGuidList: idcTag && blockTag ? [`${idcTag}.${blockTag}`] : [idcTag],
      deviceTypeList: deviceType ? [deviceType] : [],
    });
    setLoading(false);
    if (fetchDeviceListError) {
      setDeviceData([]);
      message.error(fetchDeviceListError.message);
      return;
    }
    if (data?.data) {
      setDeviceData(
        data.data.map(item => ({
          deviceGuid: item.guid,
          deviceName: item.name,
          deviceType: item.deviceCategory.level3,
          deviceLabel: item.deviceLabel,
          roomTag: item.spaceGuid.roomTag,
          inhibition:
            selectedRows?.length &&
            typeof selectedRows.find(row => row.deviceGuid === item.guid)?.inhibition === 'boolean'
              ? !!selectedRows.find(row => row.deviceGuid === item.guid)?.inhibition!
              : true, // 如果业务有放入预期内告警字段，则取该字段
          productModel: item.productModel,
          vendor: item.vendor,
        }))
      );
      setTotal(data.total);
    } else {
      setDeviceData([]);
    }

    return data.data;
  }, [filterValue, idcTag, blockTag, deviceType]);

  useEffect(() => {
    getDeivceList();
  }, [getDeivceList]);
  const _basicColumn = deviceType
    ? basicColumn.filter(item => item.dataIndex !== 'deviceType')
    : basicColumn;
  return (
    <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="middle">
      {showRowSelectionNFilter && (
        <Space>
          <Input.Search
            style={{ width: 224 }}
            placeholder="设备编号/名称"
            onSearch={value => {
              setFilterValue({ ...filterValue, pageNum: 1, nameOrLabel: value });
            }}
          />
          <LocationCascader
            placeholder="包间"
            idc={idcTag}
            authorizedOnly
            blocks={blockTag ? [blockTag] : []}
            nodeTypes={['ROOM']}
            style={{ width: 168 }}
            onChange={value => {
              if (value) {
                const roomTag = value.map(roomGuid => getSpaceGuidMap(roomGuid as string).room);
                setFilterValue({ ...filterValue, pageNum: 1, roomTags: roomTag as string[] });
              } else {
                setFilterValue({ ...filterValue, pageNum: 1, roomTags: [] });
              }
            }}
          />
          {deviceType ? null : (
            <DeviceTypeCascader
              style={{ width: 168 }}
              placeholder="三级分类"
              dataType={['snDevice', 'noSnDevice']}
              disabledTypeList={['C0', 'C1']}
              numbered
              allowClear
              onChange={value => {
                setFilterValue({
                  ...filterValue,
                  pageNum: 1,
                  deviceTypeList: value ? [value] : [],
                });
              }}
            />
          )}
          <VendorModelSelect
            deviceType={filterValue.deviceTypeList?.join(',')}
            style={{ width: 168 }}
            value={[filterValue.vendor, filterValue.productModel]}
            allowClear
            vendorPlaceholder="厂商"
            modelPlaceholder="型号"
            onChange={value => {
              const [vendor, productModel] = value ?? [];
              setFilterValue({ ...filterValue, pageNum: 1, vendor, productModel });
            }}
          />
        </Space>
      )}

      <Table<RelateDevice>
        size="middle"
        rowKey="deviceGuid"
        {...props}
        columns={
          showRowSelectionNFilter
            ? _basicColumn
            : _basicColumn.concat([
                {
                  title: '操作',
                  dataIndex: 'operation',
                  key: 'operation',
                  width: 80,
                  fixed: 'right',
                  render: (_, record) => {
                    return (
                      <Button
                        type="link"
                        compact
                        onClick={() => {
                          const _rows = selectedRows?.filter(
                            item => item.deviceGuid !== record.deviceGuid
                          )!;
                          setSelectedRows?.(_rows);
                          if (!_rows.length) {
                            setIsCheckableMode?.(false);
                          }
                        }}
                      >
                        取消选择
                      </Button>
                    );
                  },
                },
              ])
        }
        loading={loading}
        dataSource={!showRowSelectionNFilter ? selectedRows : deviceData}
        rowSelection={
          showRowSelectionNFilter
            ? {
                selectedRowKeys: selectedRows.map(row => row.deviceGuid),
                onChange: (selectedRowKeys, selectedRows) => {
                  if (setSelectedRows) {
                    setSelectedRows(rows => [
                      ...(rows ?? []).filter(
                        row =>
                          !(deviceData ?? []).find(
                            item => item && item.deviceGuid === row.deviceGuid
                          )
                      ),
                      ...selectedRows,
                    ]);
                  }
                },
              }
            : undefined
        }
        pagination={
          showRowSelectionNFilter
            ? {
                total: !showRowSelectionNFilter ? selectedRows?.length : total,
                current: filterValue.pageNum,
                pageSize: filterValue.pageSize,
                onChange: (pageNum, pageSize) => {
                  if (showRowSelectionNFilter) {
                    setFilterValue({ ...filterValue, pageNum, pageSize });
                  }
                },
              }
            : undefined
        }
      />
    </Space>
  );
}
