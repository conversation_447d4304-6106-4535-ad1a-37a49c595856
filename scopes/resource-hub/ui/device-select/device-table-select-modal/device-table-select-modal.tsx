import isNil from 'lodash.isnil';
import React, { useState } from 'react';
import { useDeepCompareEffect } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPageDevices } from '@manyun/resource-hub.service.fetch-page-devices';
import { DeviceTypeText } from '@manyun/resource-hub.ui.device-type-text';

import type { RelateDevice } from './device-select-type';
import { DeviceTable } from './device-table';

export type DevicesTableSelecteModalProps = {
  style: React.CSSProperties;
  idcTag: string;
  blockTag?: string | null;
  showDescription?: boolean;
  value?: RelateDevice[];
  onChange?: (value: RelateDevice[]) => void;
  matchProductModel?: boolean;
  zIndex?: number;
  deviceType?: string | null;
} & ButtonProps;

export const DevicesTableSelectModal = React.forwardRef(
  (
    {
      idcTag,
      blockTag,
      value = [],
      style,
      onChange,
      showDescription = true,
      matchProductModel,
      zIndex,
      deviceType,
      ...rest
    }: DevicesTableSelecteModalProps,
    ref: React.Ref<RefSelectProps>
  ) => {
    const [open, setOpen] = useState(false);
    const [selectedRows, setSelectedRows] = useState<RelateDevice[]>([]);
    const [isCheckableMode, setIsCheckableMode] = useState(false);

    const showModal = () => {
      setOpen(true);
    };
    const closeModal = () => {
      setOpen(false);
    };
    useDeepCompareEffect(() => {
      if (open) {
        if (isNil(value) || (Array.isArray(value) && !value.length)) {
          // 外部值不存在数据需要主动清空
          setSelectedRows([]);
          return;
        }
        if (Array.isArray(value) && value.length) {
          if (!matchProductModel) {
            // 大部分业务场景不走productModel匹配
            setSelectedRows(value);
            return;
          }
          fetchPageDevices({
            // 部分场景，业务无法给到设备全部信息，需要内部重新走接口同步型号厂商数据
            // idcTag,
            // blockTag,
            pageNum: 1,
            pageSize: value.length,
            deviceGuidList: value.map(item => item.deviceGuid),
            spaceGuidList: idcTag && blockTag ? [`${idcTag}.${blockTag}`] : [idcTag],
            deviceTypeList: deviceType ? [deviceType] : [],
          }).then(res => {
            const { data, error } = res;
            if (error) {
              message.error(error.message);
              return;
            }
            setSelectedRows(
              value.map(device => ({
                ...device,
                productModel:
                  device.productModel ??
                  data.data.find(item => item.guid === device.deviceGuid)?.productModel,
                vendor:
                  device.vendor ?? data.data.find(item => item.guid === device.deviceGuid)?.vendor,
              }))
            );
          });
        }
      }
    }, [value, open, idcTag, blockTag, deviceType, matchProductModel]);
    return (
      <>
        <Space size="middle">
          <Button
            {...rest}
            onClick={() => {
              showModal();
            }}
          >
            选择设备
          </Button>
          {value && value.length > 0 && showDescription ? (
            <Space>
              <Typography.Text type="secondary">
                {`${value[0].deviceName}`} <DeviceTypeText code={value[0].deviceType} />
              </Typography.Text>
              <Typography.Text type="secondary">{`(${value[0].roomTag})等${value.length}项`}</Typography.Text>
            </Space>
          ) : null}
        </Space>
        <Modal
          title="选择设备"
          destroyOnClose
          width={1015}
          open={open}
          zIndex={zIndex}
          footer={
            <Space>
              <Button
                onClick={() => {
                  closeModal();
                  setIsCheckableMode(false);
                }}
              >
                取消
              </Button>
              <Button
                type="primary"
                onClick={() => {
                  onChange && onChange(selectedRows);
                  closeModal();
                }}
              >
                确认
              </Button>
            </Space>
          }
          onCancel={() => {
            closeModal();
            setIsCheckableMode(false);
          }}
        >
          <Space style={{ width: '100%', display: 'flex' }} direction="vertical" size="middle">
            {!!selectedRows.length && (
              <Space>
                {!isCheckableMode ? (
                  <Typography.Text style={{ fontSize: 14 }}>
                    已选择 {selectedRows.length} 项设备
                  </Typography.Text>
                ) : null}
                <Button
                  type="link"
                  compact
                  onClick={() => {
                    setIsCheckableMode(!isCheckableMode);
                  }}
                >
                  {isCheckableMode ? '<< 返回继续选择' : '查看已选项 >>'}
                </Button>
              </Space>
            )}

            <DeviceTable
              idcTag={idcTag}
              blockTag={blockTag}
              selectedRows={selectedRows}
              setSelectedRows={setSelectedRows}
              isCheckableMode={isCheckableMode}
              setIsCheckableMode={setIsCheckableMode}
              deviceType={deviceType}
            />
          </Space>
        </Modal>
      </>
    );
  }
);
DevicesTableSelectModal.displayName = 'DevicesTableSelectModal';
