import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type SpecUnitSelectProps = Omit<SelectProps, 'options'>;

export const SpecUnitSelect = React.forwardRef(
  (props: SpecUnitSelectProps, ref: React.Ref<RefSelectProps>) => {
    const [{ data: specUnitData }, { readMetaData }] = useMetaData(MetaType.SPEC_UNIT);

    React.useEffect(() => {
      readMetaData();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <Select optionFilterProp="label" {...props} options={specUnitData.data} />;
  }
);

SpecUnitSelect.displayName = 'SpecUnitSelect';
