import React from 'react';

import { fireEvent, render, waitFor } from '@testing-library/react';

import {
  AssetSpaceOrDeviceLink,
  GuidSpaceOrDeviceLink,
  OtherSpaceOrDeviceLink,
} from './space-or-device-link.composition';

test('should redirect to `space-or-device` page by a device asset number', async () => {
  const { getByText } = render(<AssetSpaceOrDeviceLink />);
  fireEvent.click(getByText('fgtrfd1'));
  await waitFor(() => {
    const rendered = getByText('search: ?guid=0mE1ZHmxrqDLLljnKqLot3HaAhtokmCR&type=DEVICE');
    expect(rendered).toBeTruthy();
  });
});

test('should redirect to `space-or-device` page by a guid', async () => {
  const { getByText } = render(<OtherSpaceOrDeviceLink />);
  fireEvent.click(getByText('EC01'));
  await waitFor(() => {
    const rendered = getByText('search: ?guid=EC01&type=IDC');
    expect(rendered).toBeTruthy();
  });
});

test('should redirect to `space-or-device` page by a device asset number,show guid', async () => {
  const { getByText } = render(<GuidSpaceOrDeviceLink />);
  fireEvent.click(getByText('fgtrfd1'));
  await waitFor(() => {
    const rendered = getByText('search: ?guid=0mE1ZHmxrqDLLljnKqLot3HaAhtokmCR&type=DEVICE');
    expect(rendered).toBeTruthy();
  });
});
