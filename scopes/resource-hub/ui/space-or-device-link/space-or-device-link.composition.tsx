import React from 'react';
import { Route, MemoryRouter as Router, useLocation } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { SpaceOrDeviceLink } from './space-or-device-link';

export const AssetSpaceOrDeviceLink = () => {
  return (
    <Router initialEntries={['/']}>
      <Route path="/" exact>
        <ConfigProvider>
          <span>test:</span>
          <SpaceOrDeviceLink id="fgtrfd1" type="DEVICE_ASSET_NO" />
        </ConfigProvider>
      </Route>
      <Route path="/">
        <ShowDevuice />
      </Route>
    </Router>
  );
};

export const OtherSpaceOrDeviceLink = () => {
  return (
    <Router initialEntries={['/abc']}>
      <Route path="/abc" exact>
        <SpaceOrDeviceLink id="EC01" type="IDC" />
      </Route>
      <Route path="/">
        <ShowDevuice />
      </Route>
    </Router>
  );
};

export const GuidSpaceOrDeviceLink = () => {
  return (
    <Router initialEntries={['/abc']}>
      <Route path="/abc" exact>
        <SpaceOrDeviceLink
          id="0mE1ZHmxrqDLLljnKqLot3HaAhtokmCR"
          type="DEVICE_GUID"
          text="fgtrfd1"
        />
      </Route>
      <Route path="/">
        <ShowDevuice />
      </Route>
    </Router>
  );
};

function ShowDevuice() {
  const { search } = useLocation();

  return (
    <>
      <p>单设备视图</p>
      <p>search: {search}</p>
    </>
  );
}
