import React from 'react';
import { Link } from 'react-router-dom';

import { generateDeviceRecordRoutePath } from '@manyun/resource-hub.route.resource-routes';

export type SpaceOrDeviceLinkProps = {
  id: string;
  type: 'DEVICE_GUID' | 'DEVICE_ASSET_NO' | 'IDC' | 'BLOCK' | 'ROOM';
  text?: string;
};

export function SpaceOrDeviceLink({ id, type, text = id }: SpaceOrDeviceLinkProps) {
  if (!id) {
    return null;
  }

  const to = generateDeviceRecordRoutePath({
    guid: id,
  });

  return <Link to={to}>{text}</Link>;
}
