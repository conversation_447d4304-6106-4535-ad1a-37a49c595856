import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import shallowequal from 'shallowequal';

import { Select, SelectProps } from '@manyun/base-ui.ui.select';

import { getRoomTypeAction, selectRoomTypeEntities } from '@manyun/resource-hub.state.room-type';

type RoomSelectType = {
  label: string;
  value: string | number;
};

/**
 * 包间选择器
 * @returns
 */
export function RoomTypeSelect(props: SelectProps) {
  const [roomTypes, setRoomTypes] = useState<RoomSelectType[]>([]);
  const dispatch = useDispatch();
  React.useEffect(() => {
    dispatch(getRoomTypeAction());
  }, [dispatch]);

  const entities = useSelector(selectRoomTypeEntities, (left, right) => shallowequal(left, right));
  useEffect(() => {
    if (entities) {
      const typeList = Object.keys(entities).map(key => {
        return {
          label: entities[key].text,
          value: entities[key].code,
        };
      });
      setRoomTypes(typeList);
    }
  }, [entities]);

  return <Select showSearch allowClear optionFilterProp="label" {...props} options={roomTypes} />;
}
