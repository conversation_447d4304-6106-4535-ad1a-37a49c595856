import type { TextProps } from 'antd/es/typography/Text';
import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';
import { useMetaData } from '@manyun/resource-hub.hook.use-meta-data';
import { MetaType } from '@manyun/resource-hub.model.metadata';

export type UnitMetaDataTextProps = {
  unitCode: string;
} & TextProps;

export function UnitMetaDataText({ unitCode, ...typographyTextProps }: UnitMetaDataTextProps) {
  const [{ data }, { readMetaData }] = useMetaData(MetaType.TYPE_UNIT);

  React.useEffect(() => {
    readMetaData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Typography.Text {...typographyTextProps}>
      {data.entities[unitCode]?.name ?? '未知'}
    </Typography.Text>
  );
}
