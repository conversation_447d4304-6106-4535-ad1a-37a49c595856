import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Transfer } from '@manyun/base-ui.ui.transfer';

import { Point } from '@manyun/monitoring.model.point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { updateCorePoints } from '@manyun/resource-hub.service.update-core-points';

import styles from './core-points-creator.module.less';

export type CorePointsCreatorProps = {
  deviceType: string;
  cores?: Point[];
  callback?: () => void;
};

export function CorePointsCreator({ deviceType, cores = [], callback }: CorePointsCreatorProps) {
  const [isModalVisible, setIsModalVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [targetKeys, setTargetKeys] = React.useState<string[]>([]);
  const [selectedKeys, setSelectedKeys] = React.useState<string[]>([]);
  const list = React.useRef<any[]>([]);

  const triggerVisible = () => {
    setIsModalVisible(visible => !visible);
  };

  const fetchPoints = React.useCallback(async () => {
    const { error, data } = await fetchPointsByCondition({
      deviceType,
      pointTypeList: ['ORI', 'AGG_DEVICE', 'CAL_DEVICE'],
      dataTypeList: ['AI', 'DI', 'AO', 'DO', 'ALARM'],
      isRemoveSub: true,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    list.current = data.data.filter(point => point.priority === null);
  }, [deviceType]);

  const reset = React.useCallback(() => {
    setTargetKeys([]);
    setSelectedKeys([]);
  }, []);

  const addPoints = React.useCallback(
    async pointList => {
      setLoading(true);
      const { error } = await updateCorePoints({ pointList, variant: 'add' });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      reset();
      triggerVisible();
      typeof callback === 'function' && callback();
    },
    [callback, reset]
  );

  React.useEffect(() => {
    fetchPoints();
    return () => {
      reset();
    };
  }, [fetchPoints, reset]);

  return (
    <>
      <Button type="primary" onClick={triggerVisible}>
        添加
      </Button>
      <Modal
        bodyStyle={{ maxHeight: 'calc(80vh)', overflowY: 'auto' }}
        width={720}
        title={
          <>
            <span>添加核心测点 </span>
            <span className={styles['disable-text']}>{`已选择${targetKeys.length}/${
              12 - cores.length
            }`}</span>
          </>
        }
        visible={isModalVisible}
        confirmLoading={loading}
        okButtonProps={{
          disabled: targetKeys.length === 0 || cores.length + targetKeys.length > 12,
        }}
        onOk={() => {
          const targets = list.current.filter(point => targetKeys.includes(point.code));
          addPoints([...(cores || []), ...targets]);
        }}
        onCancel={triggerVisible}
      >
        <Transfer
          showSearch
          dataSource={list.current.filter(point => !cores.find(item => item.code === point.code))}
          titles={['测点列表', '核心测点']}
          targetKeys={targetKeys}
          selectedKeys={selectedKeys}
          onChange={targetKeys => {
            setTargetKeys(targetKeys);
          }}
          onSelectChange={(sourceSelectedKeys, targetSelectedKeys) => {
            setSelectedKeys([...sourceSelectedKeys, ...targetSelectedKeys]);
          }}
          listStyle={{ width: 316 }}
          render={record => record.name}
          rowKey={record => record.code}
          oneWay
          pagination
        />
      </Modal>
    </>
  );
}
