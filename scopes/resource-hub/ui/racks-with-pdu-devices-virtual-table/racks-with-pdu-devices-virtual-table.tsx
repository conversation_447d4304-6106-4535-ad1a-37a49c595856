/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-2
 *
 * @packageDocumentation
 */
import { Table } from 'antd';
import type { TableProps } from 'antd';
import classNames from 'classnames';
import ResizeObserver from 'rc-resize-observer';
import React, { useEffect, useRef, useState } from 'react';
import { VariableSizeGrid as Grid } from 'react-window';

import type { RackOnPowerTicketJSON } from '@manyun/resource-hub.gql.client.racks';

export type RacksWithPduDevicesVirtualTableProps = TableProps<RackOnPowerTicketJSON>;

export function RacksWithPduDevicesVirtualTable(
  props: RacksWithPduDevicesVirtualTableProps & {
    pagination: {
      current: number;
      pageSize: number;
      total: number;
      flatData: RackOnPowerTicketJSON[];
    };
    setPagination: (v: { current: number; pageSize: number }) => void;
  }
) {
  const { columns, scroll, pagination, setPagination } = props;
  const [tableWidth, setTableWidth] = useState(0);

  const widthColumnCount = columns!.filter(width => !width).length;
  const mergedColumns = columns!.map(column => {
    if (column.width) {
      return column;
    }

    return {
      ...column,
      width: Math.floor(tableWidth / widthColumnCount),
    };
  });

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const gridRef = useRef<any>();
  const [connectObject] = useState<unknown>(() => {
    const obj = {};
    Object.defineProperty(obj, 'scrollLeft', {
      get: () => {
        if (gridRef.current) {
          return gridRef.current?.state?.scrollLeft;
        }
        return null;
      },
      set: (scrollLeft: number) => {
        if (gridRef.current) {
          gridRef.current.scrollTo({ scrollLeft });
        }
      },
    });

    return obj;
  });
  const resetVirtualGrid = () => {
    gridRef.current?.resetAfterIndices({
      columnIndex: 0,
      shouldForceUpdate: true,
    });
  };
  useEffect(() => resetVirtualGrid, [tableWidth]);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const renderVirtualList = (rawData: object[], { scrollbarSize, ref, onScroll }: any) => {
    ref.current = connectObject;
    const totalHeight = rawData.length * 54;

    return (
      <Grid
        ref={gridRef}
        className="virtual-grid"
        columnCount={mergedColumns.length}
        columnWidth={(index: number) => {
          const { width } = mergedColumns[index];
          return totalHeight > (scroll!.y! as number) && index === mergedColumns.length - 1
            ? (width as number) - scrollbarSize - 1
            : (width as number);
        }}
        height={scroll!.y as number}
        rowCount={rawData.length}
        rowHeight={() => 54}
        width={tableWidth}
        onScroll={({ scrollLeft }: { scrollLeft: number }) => {
          onScroll({ scrollLeft });
        }}
      >
        {({
          columnIndex,
          rowIndex,
          style,
        }: {
          columnIndex: number;
          rowIndex: number;
          style: React.CSSProperties;
        }) => (
          <div
            className={classNames('virtual-table-cell', {
              'virtual-table-cell-last': columnIndex === mergedColumns.length - 1,
            })}
            style={style}
          >
            {/* eslint-disable-next-line @typescript-eslint/no-explicit-any  */}
            {(rawData[rowIndex] as any)[(mergedColumns as any)[columnIndex].dataIndex]}
          </div>
        )}
      </Grid>
    );
  };

  // 计算原始的总页数
  const totalPages = Math.ceil(pagination.total / pagination.pageSize);
  return (
    <ResizeObserver
      onResize={({ width }) => {
        setTableWidth(width);
      }}
    >
      <Table
        {...props}
        className="virtual-table"
        scroll={{ x: 'max-content' }}
        columns={mergedColumns}
        pagination={{
          total: totalPages * (pagination.flatData.length || pagination.pageSize),
          current: pagination.current,
          pageSize: pagination.flatData.length || pagination.pageSize,
          pageSizeOptions: [],

          onChange: (current, pageSize) => {
            setPagination({ ...pagination, current, pageSize: pagination.pageSize });
          },
        }}
        components={{
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          body: renderVirtualList as unknown as any,
        }}
      />
    </ResizeObserver>
  );
}
