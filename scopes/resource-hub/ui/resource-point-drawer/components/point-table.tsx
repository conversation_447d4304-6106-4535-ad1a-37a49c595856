import React, { useEffect, useMemo, useState } from 'react';

import { message } from 'antd';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';

import type { DataType } from '@manyun/monitoring.model.point';
import { fetchPointValuesByGuids } from '@manyun/monitoring.service.fetch-point-values-by-guids';
import { PointTypeSelect } from '@manyun/resource-hub.ui.point-type-select';

import type { TreePoint } from '../resource-point-drawer.type';

export type PointTableProps = Omit<TableProps<TreePoint>, 'columns'> & {
  wrapperClassName?: string;
  extraColumns?: Array<ColumnType<TreePoint>>;
  /** 仅当指定了 idc 才能显示有效测点单选框（接口要求必须具备 idc 参数） */
  idc?: string;
};

const defaultColumns: ColumnType<TreePoint>[] = [
  {
    title: '测点id',
    dataIndex: 'code',
    width: 90,
    fixed: 'left',
  },
  {
    title: '测点名称',
    dataIndex: 'name',
    width: 180,
  },
  {
    title: '测点类型',
    dataIndex: 'dataType',
    width: 90,
    render: dataType => dataType?.name,
  },
  {
    title: '单位',
    width: 60,
    dataIndex: 'unit',
  },
];

export function PointTable(props: PointTableProps) {
  const { wrapperClassName, extraColumns = [], dataSource = [], idc, ...rest } = props;
  const [pointKeyword, setPointKeyWord] = useState('');
  const [pointType, setPointType] = useState<DataType>();
  const [showValidPoints, setShowValidPoints] = useState(true);

  const [validPointCodes, setValidPointCodes] = useState<string[]>();
  useEffect(() => {
    if (idc && showValidPoints && dataSource.length !== 0) {
      getValidPointCodes(idc, dataSource).then(codes => {
        setValidPointCodes(codes);
      });
    }
  }, [dataSource, idc, showValidPoints]);

  const finalDataSource = useMemo(
    () =>
      dataSource.filter(item => {
        if (
          pointKeyword &&
          !item.code.includes(pointKeyword) &&
          !item.name.includes(pointKeyword)
        ) {
          return false;
        }
        if (pointType && item.dataType.code !== pointType) {
          return false;
        }
        if (
          showValidPoints &&
          Array.isArray(validPointCodes) &&
          !validPointCodes.includes(item.code)
        ) {
          return false;
        }
        return true;
      }),
    [dataSource, pointKeyword, pointType, showValidPoints, validPointCodes]
  );

  return (
    <Space className={wrapperClassName} direction="vertical" size="middle">
      <Space size="middle" wrap>
        <Input.Search
          placeholder="搜索测点名称/ID"
          onChange={e => setPointKeyWord(e.target.value)}
        />
        <PointTypeSelect value={pointType} allowClear onChange={value => setPointType(value)} />
        {idc && (
          <Checkbox checked={showValidPoints} onChange={e => setShowValidPoints(e.target.checked)}>
            仅展示有效测点
          </Checkbox>
        )}
      </Space>
      <Table
        style={{ height: '100%', overflowY: 'auto' }}
        pagination={false}
        scroll={{ x: 'max-content' }}
        size="small"
        {...rest}
        dataSource={finalDataSource}
        columns={defaultColumns.concat(extraColumns)}
      />
    </Space>
  );
}

/** 获取有效的测点 code，即有实时值的测点 */
async function getValidPointCodes(idc: string, dataSource: readonly TreePoint[]) {
  const guids = Array.from(new Set(dataSource.map(item => item.target.guid)));
  const { error, data } = await fetchPointValuesByGuids({ idc, guids });
  if (error) {
    message.error(error.message);
    return;
  }
  const codes = Object.keys(data).reduce((acc, key) => {
    const value = data[key];
    if (!value || !value.pointValues) {
      return acc;
    }
    return [...acc, ...Object.keys(value.pointValues)];
  }, [] as string[]);
  return codes;
}
