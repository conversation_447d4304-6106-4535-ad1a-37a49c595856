@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.header {
  margin-bottom: @margin-md;
}

.wrapper {
  position: relative;
  flex: 1;
  overflow: hidden;

  .selectedPoints {
    position: absolute;
    top: 0;
    left: 0;
    max-width: 100%;

    &.hidden {
      right: 0;
      transform: translate(120%, 0);
      transition: all @animation-duration-slow;
    }
  }
}

.container {
  display: flex;
  align-items: flex-start;
  height: 100%;

  &.hidden {
    transform: translate(120%, 0);
    transition: transform @animation-duration-slow;
  }

  .resourceTree {
    margin-right: @margin-md;
    width: 300px;
    height: 100%;
    padding: 0;
  }

  .pointTable {
    flex: 1 0 200px;
    height: 100%;
    overflow: hidden;

    > :global(.@{prefixCls}-space-item) {
      &:last-child {
        flex: 1;
        overflow: hidden;
      }
    }
  }
}
