import type React from 'react';

import type { DrawerProps } from '@manyun/base-ui.ui.drawer';

import type { DataType, PointJSON, PointType } from '@manyun/monitoring.model.point';
import type { Device } from '@manyun/resource-hub.model.device';
import type { ResourceTreeProps } from '@manyun/resource-hub.ui.resource-tree';

export type TreePoint = PointJSON & {
  spaceGuid: string;
  deviceId: number | null;
  target: {
    name: string;
    guid: string;
    type: string;
    device?: Device;
  };
};

export type ResourcePointDrawerProps = {
  style?: React.CSSProperties;
  onClose: () => void;
  dataTypeList?: DataType[];
  pointTypeList?: PointType[];
  isQueryNon?: boolean;
  onChange?: (arg: TreePoint[]) => void;
  resourceTreeProps?: ResourceTreeProps;
  /** 最大选中数量 */
  maxCheckedCount?: number;
} & Omit<DrawerProps, 'onClose'>;
