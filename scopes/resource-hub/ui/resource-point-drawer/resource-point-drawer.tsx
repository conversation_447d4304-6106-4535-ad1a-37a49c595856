import type { CSSProperties, Key } from 'react';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import classnames from 'classnames';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Space } from '@manyun/base-ui.ui.space';
import { reactNodeToText } from '@manyun/base-ui.util.react-node-to-text';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';
import type { ResourceTreeProps } from '@manyun/resource-hub.ui.resource-tree';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { PointTable } from './components/point-table';
import type { PointTableProps } from './components/point-table';
import styles from './resource-point-drawer.module.less';
import type { ResourcePointDrawerProps, TreePoint } from './resource-point-drawer.type';

export function ResourcePointDrawer({
  style,
  open,
  onClose,
  dataTypeList = ['AI', 'DI'],
  pointTypeList = ['ORI', 'CAL_SPACE', 'AGG_SPACE', 'CAL_DEVICE', 'AGG_DEVICE', 'CUSTOM'],
  isQueryNon = false,
  onChange,
  resourceTreeProps,
  maxCheckedCount = 20,
  ...resetProps
}: ResourcePointDrawerProps) {
  const config = useSelector(selectCurrentConfig);

  const [loading, setLoading] = useState<boolean>(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<TreePoint[]>([]);

  const [dataSource, setDataSource] = useState<TreePoint[]>([]);

  useEffect(() => {
    if (open) {
      setSelectedRowKeys([]);
      setSelectedRows([]);
      setDataSource([]);
    }
  }, [open]);

  const handleOk = () => {
    onChange && onChange(selectedRows);
    onClose();
  };

  // PointTable 所对应的当前机房 idc
  const [currentIdcForPointTable, setCurrentIdcForPointTable] = useState<string>();
  const onSelect: ResourceTreeProps['onSelect'] = async (_, event) => {
    const configUtil = new ConfigUtil(config);
    const roomDeviceType: string = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_ROOM
    )!;
    const blockDeviceType: string = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_BLOCK
    )!;
    const idcDeviceType: string = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_IDC
    )!;
    const { node } = event;
    const { key: spaceGuid, device, type, title } = node;

    setLoading(true);
    const deviceType = (() => {
      switch (type) {
        case 'DEVICE':
          return device!.deviceCategory.level3;
        case 'ROOM':
          return roomDeviceType;
        case 'BLOCK':
          return blockDeviceType;
        case 'IDC':
          return idcDeviceType;
        default:
          return '';
      }
    })();
    const { idc, block } = getSpaceGuidMap(spaceGuid);
    setCurrentIdcForPointTable(idc || undefined);

    const blockGuid = idc && block ? getSpaceGuid(idc, block)! : undefined;

    //查询非标点位时需要带上blockGuid，非标点位挂在楼栋下 不加查询不了
    const queryNonParams = isQueryNon ? { blockGuid, isQueryNon } : undefined;

    const {
      data: { data },
    } = await fetchPointsByCondition({
      deviceType,
      dataTypeList,
      pointTypeList,
      ...queryNonParams,
    });
    setLoading(false);
    setDataSource(
      data.reduce((acc, item) => {
        const treeJson = item.toJSON();
        const treePoint: TreePoint = {
          ...treeJson,
          spaceGuid,
          deviceId: device ? device.id : null,
          target: {
            name: type === 'DEVICE' ? device!.name : reactNodeToText(title),
            guid: type === 'DEVICE' ? device!.guid : spaceGuid,
            type,
            device: type === 'DEVICE' ? device : undefined,
          },
        };
        return [...acc, treePoint];
      }, [] as TreePoint[])
    );
  };

  const rowSelection: PointTableProps['rowSelection'] = {
    type: maxCheckedCount > 1 ? 'checkbox' : 'radio',
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TreePoint[]) => {
      const len = selectedRowKeys.length;
      if (!maxCheckedCount || len <= maxCheckedCount) {
        // 保留之前选择的表格数据（即不在当前 dataSource 中的）
        setSelectedRowKeys(keys => [
          ...keys.filter(key => !dataSource.find(item => item.id === key)),
          ...selectedRowKeys,
        ]);
        setSelectedRows(rows => [
          ...rows.filter(row => !dataSource.find(item => item.id === row.id)),
          ...selectedRows,
        ]);
      }
    },
  };

  const [showSelected, setShowSelected] = useState(false);
  const extraColumns = useMemo(() => {
    if (!showSelected) {
      return [];
    }
    const columns: PointTableProps['extraColumns'] = [
      {
        title: '归属',
        dataIndex: 'targetName',
        width: 120,
        render: (_, record) => record.target.name,
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width: 90,
        render: (_, record) => (
          <Button
            type="link"
            compact
            onClick={() => {
              setSelectedRows(rows => rows.filter(row => row.id !== record.id));
              setSelectedRowKeys(keys => keys.filter(key => key !== record.id));
            }}
          >
            取消选择
          </Button>
        ),
      },
    ];
    return columns;
  }, [showSelected]);

  const bodyStyle: CSSProperties = {
    display: 'flex',
    flexDirection: 'column',
    overflowX: 'hidden',
  };

  return (
    <Drawer
      bodyStyle={bodyStyle}
      contentWrapperStyle={{ width: '65vw', ...style }}
      placement="right"
      title="选择测点"
      {...resetProps}
      open={open}
      extra={
        <Space>
          <Button onClick={onClose}>取消</Button>
          <Button type="primary" onClick={handleOk}>
            保存
          </Button>
        </Space>
      }
      onClose={onClose}
    >
      <Space className={styles.header}>
        {!showSelected && `已选择 ${selectedRows.length} 项测点`}
        <Button type="link" compact onClick={() => setShowSelected(!showSelected)}>
          {showSelected ? '<< 返回继续选择' : '查看已选项 >>'}
        </Button>
      </Space>

      <section className={styles.wrapper}>
        <section className={classnames(styles.container, showSelected && styles.hidden)}>
          <ResourceTree
            authorizedOnly
            showFilter
            onSelect={onSelect}
            {...resourceTreeProps}
            wrapperClassName={classnames(styles.resourceTree, resourceTreeProps?.wrapperClassName)}
          />
          <PointTable
            rowKey="id"
            wrapperClassName={styles.pointTable}
            tableLayout="fixed"
            idc={currentIdcForPointTable}
            loading={loading}
            rowSelection={rowSelection}
            dataSource={dataSource}
          />
        </section>

        <PointTable
          rowKey="id"
          wrapperClassName={classnames(styles.selectedPoints, !showSelected && styles.hidden)}
          tableLayout="fixed"
          dataSource={selectedRows}
          extraColumns={extraColumns}
        />
      </section>
    </Drawer>
  );
}
