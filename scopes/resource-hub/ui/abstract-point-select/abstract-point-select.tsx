import React from 'react';

import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import type { Point, PointJSON } from '@manyun/monitoring.model.point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import type { SvcQuery as PointsFilters } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { DeviceTypeCascader } from '@manyun/resource-hub.ui.device-type-cascader';
import type { DeviceTypeCascaderProps } from '@manyun/resource-hub.ui.device-type-cascader';

export type Filters = Omit<PointsFilters, 'deviceType'>;

export type AbstractPointSelectProps = {
  deviceTypeCascaderProps?: Partial<DeviceTypeCascaderProps>;
  filters?: Filters;
  /**
   * When to fetch points
   *
   * @defaultValue `'onDidSelectDeviceType'`
   */
  trigger?: 'onDidSelectDeviceType' | 'onDidMountOrSelectDeviceType';
  pointFilterPredicate?: (point: Point, index: number, points: Point[]) => boolean;
} & SelectProps<string, PointJSON>;

const DEFAULT_POINT_DATA_TYPES: Filters['dataTypeList'] = ['AI', 'DI'];
const DEFAULT_POINT_TYPES: Filters['pointTypeList'] = [
  'ORI',
  'AGG_DEVICE',
  'AGG_SPACE',
  'CAL_DEVICE',
  'CAL_SPACE',
];

export function AbstractPointSelect({
  style,
  deviceTypeCascaderProps,
  filters,
  trigger = 'onDidSelectDeviceType',
  pointFilterPredicate,
  ...restProps
}: AbstractPointSelectProps) {
  const [points, setPoints] = React.useState<PointJSON[] | null>(null);

  const fetchPointsByDeviceType = async (deviceType: string) => {
    const { data } = await fetchPointsByCondition({
      deviceType,
      ...filters,
      dataTypeList: filters?.dataTypeList ?? DEFAULT_POINT_DATA_TYPES,
      pointTypeList: filters?.pointTypeList ?? DEFAULT_POINT_TYPES,
      isRemoveSub: filters?.isRemoveSub ?? true,
    });
    let copy = data.data;
    if (typeof pointFilterPredicate == 'function') {
      copy = data.data.filter(pointFilterPredicate);
    }
    setPoints(copy.map(point => point.toJSON()));
    deviceTypeCascaderProps?.onChange?.(deviceType);
  };

  React.useEffect(() => {
    if (trigger === 'onDidMountOrSelectDeviceType' && deviceTypeCascaderProps?.value) {
      fetchPointsByDeviceType(
        Array.isArray(deviceTypeCascaderProps.value)
          ? deviceTypeCascaderProps.value[0]
          : deviceTypeCascaderProps.value
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Input.Group compact>
      <DeviceTypeCascader
        numbered
        dataType={['space', 'snDevice']}
        category="categorycode"
        {...deviceTypeCascaderProps}
        style={{ width: '50%', ...deviceTypeCascaderProps?.style }}
        placeholder="设备类型"
        onChange={fetchPointsByDeviceType}
      />
      <Select<string, PointJSON>
        {...restProps}
        style={{
          width: '50%',
          ...style,
        }}
        showSearch
        optionFilterProp="name"
        placeholder="测点"
        fieldNames={{ label: 'name', value: 'code' }}
        disabled={points === null}
        options={points ?? []}
      />
    </Input.Group>
  );
}
