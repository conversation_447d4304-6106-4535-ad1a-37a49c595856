import React from 'react';
import { Provider } from 'react-redux';

import { combineReducers, configureStore, createSlice } from '@reduxjs/toolkit';

import { AbstractPointSelect } from './abstract-point-select';

const deviceTypesSlice = createSlice({
  name: 'resource.device-types',
  initialState: {
    entities: {
      '10101': {
        metaName: '10101',
        metaCode: '10101',
        metaStyle: 'DEVICE',
        metaType: 'C0',
        numbered: true,
      },
    },
    codes: ['10101'],
  },
  reducers: {},
});
const store = configureStore({
  reducer: combineReducers({
    [deviceTypesSlice.name]: deviceTypesSlice.reducer,
  }),
});

export const BasicAbstractPointSelect = () => {
  return (
    <Provider store={store}>
      <div style={{ margin: 16, width: 360 }}>
        <AbstractPointSelect
          filters={{
            isRemoveSub: true,
            dataTypeList: ['AI', 'DI'],
          }}
          deviceTypeCascaderProps={{
            onChange: deviceType => {
              // eslint-disable-next-line no-console
              console.log(deviceType);
            },
          }}
          onChange={(pointCode, pointJSON) => {
            // eslint-disable-next-line no-console
            console.log(pointCode, pointJSON);
          }}
        />
      </div>
    </Provider>
  );
};
