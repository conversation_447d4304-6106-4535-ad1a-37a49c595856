import React from 'react';

import { Radio, RadioProps } from '@manyun/base-ui.ui.radio';

import { SpecValueType, getSpecLocales } from '@manyun/resource-hub.model.spec';

export type SpecValueTypeRadioProps = Omit<RadioProps, 'options'>;

const locales = getSpecLocales();

/**
 * 属性值类型 单选
 */
export function SpecValueTypeRadio({ ...props }: SpecValueTypeRadioProps) {
  const specValueTypeOptions: {
    label: string;
    value: SpecValueType;
  }[] = [
    {
      label: locales['valueTypeCharacter'],
      value: 'CHARACTER',
    },
    {
      label: locales['valueTypeNumber'],
      value: 'NUMBER',
    },
  ];

  return <Radio.Group {...props} options={specValueTypeOptions} />;
}
