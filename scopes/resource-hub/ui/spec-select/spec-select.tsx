import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';

import { useSpacesType } from '@manyun/resource-hub.hook.use-spaces-type';
import type { Spec } from '@manyun/resource-hub.service.fetch-specs';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';

// 规格值类型 枚举
export const VALUE_TYPE_KEY_MAP = {
  NUMBER: 'NUMBER', // 数值
  CHARACTER: 'CHARACTER', // 文本
};

export type SpecSelectProps<VT = unknown> = {
  // 判断是否是设备类型的参数测点
  isDevice: boolean;

  deviceType: string;
  /**
   *  仅返回数值类型的规格
   */
  onlyNumber?: boolean;
  /**
   * 同单位
   */
  specUnit?: string;
  /**
   * 是否必填
   */
  required?: boolean;
} & TreeSelectProps<VT>;

type RefTreeSelectProps = {
  focus: () => void;
  blur: () => void;
};

export type SpecOption = Partial<Spec> & {
  value: string;
  title: string;
  name?: string;
  isLeaf: boolean;
  deviceName?: string;
};

export const SpecSelect: React.ForwardRefExoticComponent<
  {
    isDevice: boolean;
    deviceType: string;
    onlyNumber?: boolean;
    specUnit?: string;
    required?: boolean;
  } & TreeSelectProps<unknown> &
    React.RefAttributes<RefTreeSelectProps>
> = React.forwardRef((props: SpecSelectProps, ref?: React.Ref<RefTreeSelectProps>) => {
  const { isDevice, deviceType, onlyNumber, specUnit, required, ...restProps } = props;
  const [dataSource, setDataSource] = useState<SpecOption[]>([]);
  const { roomDT, columnDT, gridDT } = useSpacesType();
  const [loading, setLoading] = useState<boolean>(true);
  useEffect(() => {
    setLoading(true);
    const getDeviceSpecs = async (deviceType: string) => {
      const { error, data } = await fetchSpecs({ deviceType });
      if (error) {
        message.error(error.message);
      } else {
        let filterData = data.data;
        if (onlyNumber) {
          filterData = filterData.filter(item => item.valueType === VALUE_TYPE_KEY_MAP.NUMBER);
        }
        if (specUnit) {
          filterData = filterData.filter(
            item => item.specUnit?.toLowerCase() === specUnit.toLowerCase()
          );
        }
        if (required) {
          filterData = filterData.filter(item => item.required === true);
        }
        setDataSource(
          filterData.map(item => {
            return {
              ...item,
              id: item.id,
              value: item.specCode!,
              title: item.specName!,
              isLeaf: true,
              deviceType: deviceType,
            };
          })
        );
      }
      setLoading(false);
    };
    if (isDevice) {
      getDeviceSpecs(deviceType);
    } else {
      getSpaceSpecs(deviceType);
      setLoading(false);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isDevice, deviceType]);

  const getSpaceSpecs = async (deviceType: string) => {
    if (!deviceType) {
      return;
    }
    const { error, data } = await fetchSpecs({ deviceType });
    if (error) {
      message.error(error.message);
      return;
    }

    let treeDataSource: SpecOption[] = data.data
      .filter(item => item.valueType === VALUE_TYPE_KEY_MAP.NUMBER)
      .map(item => {
        return {
          ...item,
          value: item.specCode!,
          title: item.specName!,
          isLeaf: true,
          deviceType,
        };
      });

    switch (deviceType) {
      case roomDT:
        treeDataSource = [
          {
            value: `RATED_POWER`,
            name: '包间-设计总功率',
            title: '包间-设计总功率',
            deviceName: '包间',
            deviceType: roomDT,
            isLeaf: true,
            specUnit: 'kW',
          },
          ...treeDataSource,
        ];
        break;
      case columnDT:
        treeDataSource = [
          {
            value: `RATED_POWER`,
            name: '机列-设计功率总和',
            title: '机列-设计功率总和',
            deviceName: '机柜',
            deviceType: columnDT,
            isLeaf: true,
            specUnit: 'kW',
          },
          ...treeDataSource,
        ];
        break;
      case gridDT:
        treeDataSource = [
          {
            value: `RATED_POWER`,
            name: '机柜-设计功率',
            title: '机柜-设计功率',
            deviceName: '机柜',
            deviceType: gridDT,
            isLeaf: true,
            specUnit: 'kW',
          },
          {
            value: `SIGNED_POWER`,
            name: '机柜-签约功率',
            title: '机柜-签约功率',
            deviceName: '机柜',
            deviceType: gridDT,
            isLeaf: true,
            specUnit: 'kW',
          },
          ...treeDataSource,
        ];
        break;

      default:
        break;
    }
    if (specUnit) {
      treeDataSource = treeDataSource.filter(
        item => item.specUnit?.toLowerCase() === specUnit.toLowerCase()
      );
    }
    setDataSource(treeDataSource);
  };

  return <TreeSelect ref={ref} loading={loading} treeData={dataSource} {...restProps} />;
});

SpecSelect.displayName = 'SpecSelect';
