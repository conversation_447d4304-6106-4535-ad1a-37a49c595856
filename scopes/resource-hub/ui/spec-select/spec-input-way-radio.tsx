import React from 'react';

import type { RadioProps } from '@manyun/base-ui.ui.radio';
import { Radio } from '@manyun/base-ui.ui.radio';

import type { SpecInputWay } from '@manyun/resource-hub.model.spec';
import { getSpecLocales } from '@manyun/resource-hub.model.spec';

export type SpecInputWayRadioProps = Omit<RadioProps, 'options'>;

const locales = getSpecLocales();

/**
 * 属性值选择方式 单选
 */
export function SpecInputWayRadio({ ...props }: SpecInputWayRadioProps) {
  const specInputWayOptions: {
    label: string;
    value: SpecInputWay;
  }[] = [
    {
      label: locales['inputWayInput'],
      value: 'INPUT',
    },
    {
      label: locales['inputWayOpt'],
      value: 'OPT',
    },
    {
      label: locales['inputWayComponent'],
      value: 'COMPONENT',
    },
  ];

  return <Radio.Group {...props} options={specInputWayOptions} />;
}
