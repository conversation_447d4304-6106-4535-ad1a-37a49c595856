import { generatePath } from 'react-router-dom';

// 机房
export const BASIC_RESOURCES_IDC = '/page/infrastructure/idc/list';
// 楼栋管理
export const BASIC_RESOURCES_BUILDING = '/page/infrastructure/block/list';
/** 新建楼栋 */
export const BASIC_RESOURCES_BLOCK_CREATE_ROUTE_PATH = '/page/resource-hub/block/create';
/** 新建楼栋权限code */
export const BASIC_RESOURCES_BLOCK_CREATE_ROUTE_AUTH_CODE = 'page_resource-hub_block-create';
/** 编辑楼栋 */
export const BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_PATH =
  '/page/resource-hub/block/:id/:blockGuid/edit';
/** 编辑楼栋权限code */
export const BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_AUTH_CODE = 'page_resource-hub_block-mutator';
export const generateResourceBlockMutatorPath = (params: { id: string; blockGuid: string }) =>
  generatePath(BASIC_RESOURCES_BLOCK_MUTATOR_ROUTE_PATH, params);
/** 楼栋详情 */
export const BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_PATH = '/page/resource-hub/block/:id/:blockGuid';
export const BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_AUTH_CODE = 'page_resource-hub_block-detail';
export const generateResourceBlockDetailPath = (params: { id: string; blockGuid: string }) =>
  generatePath(BASIC_RESOURCES_BLOCK_DETAIL_ROUTE_PATH, params);
/*楼层管理*/
export const BASIC_RESOURCES_FLOOR_ROUTE_PATH = '/page/infrastructure/floor/list';
export const BASIC_RESOURCES_FLOOR_AUTH_CODE = 'page_resource-floors';

// 楼层视图(拓扑)编辑新增
export const FLOOR_GRAPHIX_ROUTE_PATH = '/page/graphix/floor/:idc/:block/:floor/:mode';

export type GraphixFloorRouteParams = {
  idc: string;
  block: string;
  floor: string;
  mode: 'new' | 'edit';
};
export const generateEditGraphixFloorRoutePath = (params: GraphixFloorRouteParams) =>
  generatePath(FLOOR_GRAPHIX_ROUTE_PATH, params);

export type GraphixFloorViewRouteParams = {
  idc: string;
  block: string;
  floor: string;
};
export const generateGraphixFloorViewRoutePath = ({
  idc,
  block,
  floor,
}: GraphixFloorViewRouteParams) => {
  // @ts-ignore ts error
  return `${window.apps.monitoring.replace('[idc]', idc)}workspace/${block}/${floor}`;
};

// 包间列表
export const ROOM_LIST = '/page/infrastructure/room/list';

// 机柜列表
export const CABINET_LIST = '/page/infrastructure/cabinet/list';
// 机柜详情
export const CABINET_DETAILS = '/page/infrastructure/cabinet/details/:id';
/**
 * 新建机柜详情的 URL
 */
export const generateDetailCabinetUrl = ({ id, state }: { id: string; state: unknown }) => {
  return {
    pathname: `${CABINET_DETAILS.replace(':id', id)}`,
    state,
  };
};
export type CabinetListRouteParams = {
  /** 导入机柜的版本（从操作日志跳转过来可能会携带此参数） */
  insertVersion?: string;
  /** 机柜 guid（从操作日志跳转过来可能携带此参数） */
  guid?: string;
};
export const generateCabinetListRoutePath = ({ insertVersion, guid }: CabinetListRouteParams) => {
  if (insertVersion && guid) {
    return `${CABINET_LIST}?insertVersion=${insertVersion}&guid=${guid}`;
  }
  if (insertVersion) {
    return `${CABINET_LIST}?insertVersion=${insertVersion}`;
  }
  if (guid) {
    return `${CABINET_LIST}?guid=${guid}`;
  }
  return CABINET_LIST;
};

// 导入机柜
export const SPECIFIC_CREATE_CABINET_ITEM = '/page/infrastructure/cabinet/new';
// 设备列表
export const EQUIPMENT_LIST = '/page/infrastructure/device/list';
// 新建设备
export const SPECIFIC_CREATE_EQUIPMENT_ITEM = '/page/infrastructure/device/new';
// 编辑设备
export const SPECIFIC_EDIT_EQUIPMENT_ITEM = '/page/infrastructure/device/:id/edit';
export const SPARE_LIST = '/page/infrastructure/spare/list';
// 新建耗材
export const SPECIFIC_CREATE_SPARE_ITEM = '/page/infrastructure/spare/new';
// 编辑耗材
export const SPECIFIC_EDIT_SPARE_ITEM = '/page/infrastructure/spare/:id/edit';
// 测点列表
export const DOPOINT_LIST = '/page/infrastructure/point/list';
//规格管理
export const SPEC_LIST = '/page/infrastructure/spec/list';
//设备类型管理
export const DEVICE_TYPE_LIST = '/page/resoure-configuration/device-type/list';
// 查看某个设备类型的详细信息
export const DEVICE_TYPE_DETAIL = '/page/resoure-configuration/device-type/:id';
// 元数据配置
export const METADATA_CONFIGURATION = '/page/metadata-configuration';
export const LOG_LIST = '/page/log-center/operational-log';
// 新建某个设施包间拓扑
// `:mode` can be `new, edit`
export const SPECIFIC_INFRA_GRAPHIX = '/page/graphix/room/:topologyType/:idc/:block/:room/:mode';

export type GenerateSpecificGraphixRouteParams = {
  /** 参考BackendTopologyType，route不引入type */
  topologyType: string;
  idc: string;
  block: string;
  room: string;
  mode: 'new' | 'edit';
};
/**
 * 生成设施包间拓扑设计器的
 */
export const generateSpecificGraphixRoutePath = (params: GenerateSpecificGraphixRouteParams) =>
  generatePath(SPECIFIC_INFRA_GRAPHIX, params);

/** 设备履历 */
export const DEVICE_RECORD_ROUTE_PATH = '/page/resource-hub/device-record/:guid';
/** 设备履历权限code */
export const DEVICE_RECORD_ROUTE_AUTH_CODE = 'page_resource-hub_device-record';

type DeviceRecordTabType =
  | 'CORE'
  | 'EQUEPMENT_INFO'
  | 'ASSETS'
  | 'RUNNING_DATA'
  | 'WARNING_LIST'
  | 'RECORD_DATE'
  | 'IT_LOG'
  | 'SPARE_LIST'
  | 'INFLUENCE';
export type DeviceRecordItLogTabsKeys = Lowercase<
  'EVENT' | 'CHANGE' | 'MAINTENANCE' | 'INSPECTION' | 'REPAIR'
>;

/** 处理设备履历url */
export const generateDeviceRecordRoutePath = ({
  guid,
  tab,
  itLogTab,
}: {
  guid: string;
  tab?: DeviceRecordTabType;
  itLogTab?: DeviceRecordItLogTabsKeys;
}) => {
  const searchItLogParams = buildUrlSearchParams({
    tab,
    itLogTab,
  });
  const searchTabParams = buildUrlSearchParams({
    tab,
  });
  const path = generatePath(DEVICE_RECORD_ROUTE_PATH, { guid });
  if (itLogTab && guid && tab) {
    return path + '?' + searchItLogParams.toString();
  }
  if (guid && tab) {
    return path + '?' + searchTabParams.toString();
  }
  return path || '';
};

// 元数据配置页面code
export const METADATA_CONFIGURATION_PAGE_CODE = 'page_metadata-configuration';
export function buildUrlSearchParams(variablesMap: Object) {
  const urlSearchParams = new URLSearchParams();

  for (const [name, value] of Object.entries(variablesMap)) {
    if (typeof value === 'string') {
      urlSearchParams.append(name, value);
    }
  }

  return urlSearchParams;
}
/**
 * 设备列表页面的 URL
 * @param {object} variables
 * @param {string} [variables.deviceGuid] 设备名称
 * @param {string} [variables.deviceName] 设备名称
 * @param {string} [variables.deviceTag] 设备名称（废弃）
 * @param {string} [variables.insertVersion] 导入设备的版本（从操作日志跳转过来可能会携带此参数）
 * @param {string} [variables.deviceType] 设备类型
 * @param {string} [variables.productModel] 型号名称
 * @param {string} [variables.vendor] 厂商名称
 * @param {string} [variables.blockGuid] 机房楼栋
 * @param {string} [variables.spaceGuidList] 设备位置
 */
export const generateDeviceListUrl = ({
  deviceGuid,
  deviceName,
  deviceTag,
  insertVersion,
  deviceType,
  productModel,
  vendor,
  blockGuid,
  roomGuid,
  spaceGuidList,
}: {
  deviceGuid?: string;
  deviceName?: string;
  deviceTag?: string;
  insertVersion?: string;
  deviceType?: string;
  productModel?: string;
  vendor?: string;
  blockGuid?: string;
  roomGuid?: string;
  spaceGuidList?: string;
} = {}) => {
  const pathname = EQUIPMENT_LIST;

  const searchParams = buildUrlSearchParams({
    guid: deviceGuid,
    name: deviceName || deviceTag,
    insertVersion,
    deviceType,
    productModel,
    vendor,
    blockGuid,
    roomGuid,
    spaceGuidList,
  });
  const search = searchParams.toString();
  if (!search) {
    return pathname;
  }

  return pathname + '?' + search;
};

/**
 * 耗材列表页面的 URL
 * @param {object} variables
 * @param {string} [variables.deviceType] 设备类型
 * @param {string} [variables.productModel] 型号名称
 * @param {string} [variables.vendor] 厂商名称
 * @param {string} [variables.blockGuid] 机房楼栋
 */
export const generateSpareListUrl = ({
  deviceType,
  productModel,
  vendor,
  blockGuid,
}: { deviceType?: string; productModel?: string; vendor?: string; blockGuid?: string } = {}) => {
  const pathname = SPARE_LIST;
  const searchParams = buildUrlSearchParams({
    deviceType,
    productModel,
    vendor,
    blockGuid,
  });
  const search = searchParams.toString();
  if (!search) {
    return pathname;
  }

  return pathname + '?' + search;
  // return {
  //   pathname: SPARE_LIST,
  //   search: `?blockGuid=${blockGuid}&deviceType=${deviceType}&vendor=${vendor}&productModel=${productModel}`,
  // };
};

// 借用归还
/*借用归还*/
export const BORROWS_AND_RETURN_LIST_CODE = 'page_borrow-return';
export const BORROWS_AND_RETURN_LIST = '/page/borrows-and-return/list';

export const BORROWS_AND_RETURN_CREATE = '/page/borrows-and-return/create';
export const BORROWS_AND_RETURN_CREATE_CODE = 'page_borrow-return-create';

export const BORROWS_AND_RETURN_EDIT = '/page/borrows-and-return/:id/edit';
export const BORROWS_AND_RETURN_EDIT_CODE = 'page_borrow-return-edit';

export const BORROWS_AND_RETURN_DETAIL = '/page/borrows-and-return/:id/detail';
export const BORROWS_AND_RETURN_DETAIL_CODE = 'page_borrow-return-info';

/**
 * 借用归还详情 Location
 */
export const generateBorrowAndReturnDetailLocation = ({ id }: { id: string }) =>
  generatePath(BORROWS_AND_RETURN_DETAIL, { id });

/**
 * 借用归还编辑 Location
 */
export const generateBorrowAndReturnEditLocation = ({ id }: { id: string }) =>
  generatePath(BORROWS_AND_RETURN_EDIT, { id });

export const CUSTOM_POINT_IMPORT_ROUTE_PATH = '/page/custom-point-import';
export const CUSTOM_POINT_IMPORT_ROUTE_AUTH_CODE = 'page_custom-point-import';
