enum RoomType {
  /** IT 包间（可能也称为：数据机房） */
  ITRoom = 'IT_ROOM',

  /** 网络核心包间 */
  NetworkCoreRoom = 'NETWORK_CORE_ROOM',

  /** 运营商接入室 */
  CarrierAccessRoom = 'OPERATOR_ACCESS_ROOM',

  /** IT 小套间 */
  ITMiniSuite = '113',
}

const RACKS_ROOM_TYPE_KEYS = [
  RoomType.ITRoom,
  RoomType.NetworkCoreRoom,
  RoomType.CarrierAccessRoom,
  RoomType.ITMiniSuite,
];

export type RacksRoomType = `${typeof RACKS_ROOM_TYPE_KEYS[number]}`;

/**
 * Given a `roomTypeKey`, check if it is a [racks room](https://manyun.yuque.com/fet/dc-base-wikis/zgrhgg#bUX9t) type.
 *
 * @param roomTypeKey Any room type key.
 * @returns
 */
export function typeofRacksRoom(
  roomTypeKey: string | null | undefined
): roomTypeKey is RacksRoomType {
  return (RACKS_ROOM_TYPE_KEYS as unknown[]).includes(roomTypeKey);
}
