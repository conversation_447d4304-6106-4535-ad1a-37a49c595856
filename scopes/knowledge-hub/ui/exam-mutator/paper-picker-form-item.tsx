import React from 'react';

import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import type { ColProps } from '@manyun/base-ui.ui.grid';
import type {
  BackendMarkingWay,
  BackendPaper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { PapersPicker } from '@manyun/knowledge-hub.ui.papers-picker';

import type { ExamMutatorFormValues } from './exam-mutator';

export type PaperPickerFormItemProps = {
  layout?: { labelCol: ColProps; wrapperCol: ColProps };
  markingType?: BackendMarkingWay;
  form: FormInstance<any>;
  setPassingScoreMax(paperScore: number): void;
};

export function PaperPickerFormItem({
  layout,
  markingType,
  form,
  setPassingScoreMax,
}: PaperPickerFormItemProps) {
  return (
    <Form.Item
      {...layout}
      label="考试试卷"
      name="paperId"
      rules={[
        {
          required: true,
          message: '考试试卷必选！',
          type: 'number',
        },
      ]}
      valuePropName="id"
      trigger="onPick"
      normalize={(paper: BackendPaper) => paper.id}
    >
      <PapersPicker
        markingWay={markingType}
        onPick={paper => {
          setPassingScoreMax(paper.totalGrade);
          form.validateFields(['paperId']);
          form.setFieldsValue({
            paperName: paper.name,
          });

          const passingScore = form.getFieldValue(
            'passingScore'
          ) as ExamMutatorFormValues['passingScore'];
          if (typeof passingScore == 'number' && passingScore > paper.totalGrade) {
            form.setFieldsValue({ passingScore: paper.totalGrade });
          }
        }}
      />
    </Form.Item>
  );
}
