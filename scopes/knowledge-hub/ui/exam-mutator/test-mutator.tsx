import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { BackendMarkingWay } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

import { ExamMutatorTimeLimit } from './exam-mutator-time-limit';
import { DEFAULT_TIME_LIMIT } from './exam-mutator.constants';
import { PaperPickerFormItem } from './paper-picker-form-item';

export type TestMutatorFormValues = {
  required?: boolean;
  paperId?: number;
  mustPass?: boolean;
  timeLimit?: number;
  passingScore?: number;
  notice?: boolean;
};

export type TestMutatorProps = {
  initialValues?: TestMutatorFormValues;
  form: FormInstance<TestMutatorFormValues>;
  onValuesChange?(changedValues: any, values: TestMutatorFormValues): void;
};

export const TestMutator = React.forwardRef<any, TestMutatorProps>(
  ({ initialValues, form, onValuesChange }, ref?) => {
    const [passingScoreMax, setPassingScoreMax] = React.useState<number | undefined>(1000);
    const [, forceUpdate] = React.useReducer(x => x + 1, 0);

    const required: boolean = form.getFieldValue('required') ?? initialValues?.required ?? true;

    const mustPass = Form.useWatch('mustPass', form);
    const notice = Form.useWatch('notice', form);

    return (
      <Card title="随堂测试">
        <Form
          ref={ref}
          style={{
            /** [xs grid](https://ant.design/components/grid-cn/#Col) */
            width: 575,
          }}
          labelCol={{ xs: 6 }}
          wrapperCol={{ xs: 18 }}
          form={form}
          initialValues={initialValues}
          onValuesChange={onValuesChange}
        >
          <Form.Item label="是否需要随堂测试" name="required">
            <Radio.Group
              onChange={() => {
                forceUpdate();
              }}
            >
              <Radio value>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>
          {required && (
            <>
              <PaperPickerFormItem
                markingType={BackendMarkingWay.AUTOGRADE}
                form={form}
                setPassingScoreMax={setPassingScoreMax}
              />
              <Form.Item name="paperName" style={{ display: 'none' }}>
                <Input />
              </Form.Item>
              <Form.Item label="是否必须测验通过" name="mustPass">
                <Radio.Group>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
              {!mustPass && (
                <Form.Item label="允许补考">
                  <Space>
                    <Form.Item
                      noStyle
                      name="retakeExamCount"
                      rules={[{ max: 5, message: '补考次数不能超过5次', type: 'number' }]}
                      initialValue={0}
                    >
                      <InputNumber
                        min={0}
                        precision={0}
                        formatter={value => {
                          if (value === undefined) {
                            return '0';
                          }
                          return value.toString();
                        }}
                        parser={displayValue => {
                          if (displayValue === undefined) {
                            return 0;
                          }

                          return Number(displayValue);
                        }}
                      />
                    </Form.Item>
                    <Typography.Text>次</Typography.Text>
                  </Space>
                </Form.Item>
              )}
              <Form.Item label="是否通知上级主管" name="notice">
                <Radio.Group>
                  <Radio value>是</Radio>
                  <Radio value={false}>否</Radio>
                </Radio.Group>
              </Form.Item>
              {notice && (
                <Form.Item label="未通过">
                  <Space>
                    <Form.Item
                      noStyle
                      name="failedExamCount"
                      initialValue={1}
                      dependencies={['retakeExamCount']}
                      rules={[
                        {
                          validator: (_, value) => {
                            const retakeExamCount = form.getFieldValue('retakeExamCount');
                            if (!mustPass && value > (retakeExamCount ?? 0) + 1) {
                              return Promise.reject(new Error('未通过次数不能超过考试次数'));
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <InputNumber
                        min={1}
                        max={99}
                        precision={0}
                        formatter={value => {
                          if (value === undefined) {
                            return '1';
                          }
                          return value.toString();
                        }}
                        parser={displayValue => {
                          if (displayValue === undefined) {
                            return 1;
                          }

                          return Number(displayValue);
                        }}
                      />
                    </Form.Item>
                    <Typography.Text>次</Typography.Text>
                  </Space>
                </Form.Item>
              )}
              <Form.Item label="考试时长" name="timeLimit">
                <ExamMutatorTimeLimit />
              </Form.Item>
              <Form.Item
                label="及格分数"
                name="passingScore"
                rules={[
                  {
                    required: true,
                    message: '及格分数必填！',
                  },
                ]}
              >
                <InputNumber style={{ width: 90 }} min={0} max={passingScoreMax} />
              </Form.Item>
            </>
          )}
        </Form>
      </Card>
    );
  }
);

TestMutator.displayName = 'TestMutator';
TestMutator.defaultProps = {
  initialValues: {
    required: true,
    mustPass: true,
    timeLimit: DEFAULT_TIME_LIMIT,
    passingScore: 100,
    notice: false,
  },
};
