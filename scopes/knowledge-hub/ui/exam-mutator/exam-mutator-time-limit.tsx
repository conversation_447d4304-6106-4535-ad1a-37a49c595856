import React from 'react';

import { InputNumber } from '@manyun/base-ui.ui.input-number';

import { DEFAULT_TIME_LIMIT, DEFAULT_TIME_LIMIT_MAX } from './exam-mutator.constants';

export type ExamMutatorTimeLimitProps = {
  max?: number;
};

const postfix: string = '分钟';

export const ExamMutatorTimeLimit = React.forwardRef<any, ExamMutatorTimeLimitProps>(
  (props, ref?) => {
    return (
      <InputNumber
        ref={ref}
        style={{ width: 90 }}
        max={DEFAULT_TIME_LIMIT_MAX}
        {...props}
        min={1}
        precision={0}
        formatter={value => {
          if (value === undefined) {
            return '';
          }

          return value.toString() + postfix;
        }}
        parser={displayValue => {
          if (displayValue === undefined) {
            return DEFAULT_TIME_LIMIT;
          }

          let normalizedDisplayValue = displayValue;
          [...Array.from(postfix)].forEach(txt => {
            normalizedDisplayValue = normalizedDisplayValue.replace(txt, '');
          });

          const val = Number(normalizedDisplayValue);

          if (Number.isNaN(val)) {
            return DEFAULT_TIME_LIMIT;
          }

          return val;
        }}
      />
    );
  }
);

ExamMutatorTimeLimit.displayName = 'ExamMutatorTimeLimit';
