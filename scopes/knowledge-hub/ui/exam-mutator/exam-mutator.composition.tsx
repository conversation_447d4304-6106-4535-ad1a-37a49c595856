import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks as registerFetchPapersCategoryMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { registerWebMocks as registerFetchPaperMock } from '@manyun/knowledge-hub.service.dcexam.fetch-paper';
import { registerWebMocks as registerFetchPapersMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { getMock } from '@manyun/service.request';

import { ExamMutator } from './exam-mutator';
import type { ExamMutatorFormValues } from './exam-mutator';
import { TestMutator } from './test-mutator';
import type { TestMutatorFormValues } from './test-mutator';

export const BasicExamMutator = () => {
  const [form] = Form.useForm<ExamMutatorFormValues>();
  const [initialized, setInitialized] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerFetchPapersMocks(mock);
    registerFetchPaperMock(mock);
    registerFetchPapersCategoryMocks(mock);
    setInitialized(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <ExamMutator form={form} onValuesChange={console.log} />
      </FakeStore>
    </ConfigProvider>
  );
};

export const BasicTestMutator = () => {
  const [form] = Form.useForm<TestMutatorFormValues>();
  const [initialized, setInitialized] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerFetchPapersMocks(mock);
    registerFetchPaperMock(mock);
    registerFetchPapersCategoryMocks(mock);
    setInitialized(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <TestMutator form={form} onValuesChange={console.log} />
        <br />
        <button
          className="manyun-btn manyun-btn-primary"
          onClick={() => {
            form.validateFields();
          }}
        >
          提交
        </button>
      </FakeStore>
    </ConfigProvider>
  );
};
