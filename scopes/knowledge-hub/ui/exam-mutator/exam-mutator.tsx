import React, { useState } from 'react';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker as DP } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tag } from '@manyun/base-ui.ui.tag';

import { ComplexUsersPicker } from '@manyun/auth-hub.ui.complex-users-picker';
import type { Value as ComplexUsersPickerValue } from '@manyun/auth-hub.ui.complex-users-picker';
import type { BackendExamType } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { ExamsCategoryCascader } from '@manyun/knowledge-hub.ui.exams-category';
import { GroupUsersPicker } from '@manyun/knowledge-hub.ui.group-users-picker';
import type { Users } from '@manyun/knowledge-hub.ui.group-users-picker';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { ExamMutatorTimeLimit } from './exam-mutator-time-limit';
import {
  DEFAULT_PASSING_SCORE_MAX,
  DEFAULT_TIME_LIMIT,
  DEFAULT_TIME_LIMIT_MAX,
} from './exam-mutator.constants';
import { PaperPickerFormItem } from './paper-picker-form-item';

export type ExamMutatorFormValues = {
  /** 实际上这个字段并没有对应的 UI 控件，只是为了方便修改考试时缓存现有的考试类型 */
  type?: BackendExamType;
  name?: string;
  categoryCode?: number[];
  timeRange?: [Moment, Moment];
  paperId?: number;
  timeLimit?: number;
  passingScore?: number;
  users: Users;
  markUsers?: ComplexUsersPickerValue;
  /** 考试范围（机房编号集合） */
  scopes?: string[];
  allowReviewPaperAfterHandedIn?: boolean;
  useSameMasterPaper?: boolean;
};

export type ExamMutatorProps = {
  categoryCodeTrigger?: 'onFocus' | 'onDidMount';
  defaultTimeLimitMax?: number;
  defaultPassingScoreMax?: number;
  initialValues?: ExamMutatorFormValues;
  form: FormInstance<ExamMutatorFormValues>;
  onValuesChange?(changedValues: any, values: ExamMutatorFormValues): void;
};

const formItemLayoutProps = {
  style: { width: 500 },
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

export const ExamMutator = React.forwardRef<any, ExamMutatorProps>(
  (
    {
      categoryCodeTrigger = 'onFocus',
      defaultTimeLimitMax = DEFAULT_TIME_LIMIT_MAX,
      defaultPassingScoreMax = DEFAULT_PASSING_SCORE_MAX,
      form,
      initialValues,
      onValuesChange,
    },
    ref?
  ) => {
    const [timeLimitMax, setTimeLimitMax] = React.useState<number>(defaultTimeLimitMax);
    const [passingScoreMax, setPassingScoreMax] = React.useState<number>(defaultPassingScoreMax);

    /** FIXME start */
    // @Jerry
    // Why we can't use `form.getFieldValue()` here?
    // It always return the initial value.
    const [requiredUsers, setRequiredUsers] = React.useState<ComplexUsersPickerValue>();
    const [markUsers, setMarkUsers] = React.useState<ComplexUsersPickerValue>();
    /** FIXME end */

    const users = Form.useWatch('users', form);
    const [userData, setUserData] = useState<Users>(
      initialValues?.users ?? {
        requiredUsers: {
          roleKeys: [],
          userKeys: [],
        },
        optionalUsers: {
          roleKeys: [],
          userKeys: [],
        },
      }
    );

    return (
      <Form ref={ref} form={form} initialValues={initialValues} onValuesChange={onValuesChange}>
        <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
          <Card title="考试信息" bordered={false}>
            <Form.Item
              {...formItemLayoutProps}
              label="考试名称"
              name="name"
              rules={[
                {
                  required: true,
                  message: '考试名称必填！',
                },
                { max: 50, message: '最多输入 50 个字符！' },
              ]}
            >
              <Input style={{ width: 384 }} />
            </Form.Item>
            <Form.Item
              {...formItemLayoutProps}
              label="考试分类"
              name="categoryCode"
              rules={[
                {
                  required: true,
                  message: '考试分类必选！',
                },
              ]}
            >
              <ExamsCategoryCascader
                style={{ width: 384 }}
                trigger={categoryCodeTrigger}
                onMappingCompleted={(value: any) => {
                  form.setFieldsValue({ categoryCode: value });
                }}
              />
            </Form.Item>
            <Form.Item
              {...formItemLayoutProps}
              label="考试时间"
              name="timeRange"
              rules={[
                {
                  required: true,
                  message: '考试时间必选！',
                },
              ]}
            >
              <DP.RangePicker
                style={{ width: 384 }}
                showTime={{ format: 'HH:mm' }}
                format="YYYY-MM-DD HH:mm"
                disabledDate={current => {
                  return current && current < moment().startOf('day');
                }}
                disabledTime={(current, type) => {
                  if (type === 'end') {
                    return {};
                  }

                  if (current === null) {
                    return {
                      disabledHours: () => range(0, 24),
                      disabledMinutes: () => range(0, 60),
                    };
                  }

                  const now = moment();
                  if (current?.format('YYYY-MM-DD') !== now.format('YYYY-MM-DD')) {
                    return {};
                  }

                  const hour = now.hour();
                  const minutes = now.minutes();
                  const seconds = now.seconds();
                  const currentHour = current.hour();
                  const currentMinites = current.minutes();

                  return {
                    disabledHours: () => range(0, hour),
                    disabledMinutes: currentHour === hour ? () => range(0, minutes) : undefined,
                    disabledSeconds:
                      currentMinites === minutes ? () => range(0, seconds) : undefined,
                  };
                }}
                renderExtraFooter={() => (
                  <Space>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('timeRange', [moment(), moment().add(7, 'day')])
                      }
                    >
                      一周
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('timeRange', [moment(), moment().add(1, 'month')])
                      }
                    >
                      一月
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('timeRange', [moment(), moment().add(1, 'year')])
                      }
                    >
                      一年
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('timeRange', [moment(), moment().add(10, 'year')])
                      }
                    >
                      十年
                    </Tag>
                  </Space>
                )}
                onChange={dates => {
                  if (!(dates && dates.length === 2 && dates[0] && dates[1])) {
                    return;
                  }

                  const [startedAtMoment, endedAtMoment] = dates;
                  const diffMinutes = endedAtMoment.diff(startedAtMoment, 'minutes');
                  const timeLimitMax = Math.min(diffMinutes, DEFAULT_TIME_LIMIT_MAX);
                  setTimeLimitMax(timeLimitMax);

                  const timeLimit = form.getFieldValue(
                    'timeLimit'
                  ) as ExamMutatorFormValues['timeLimit'];
                  if (typeof timeLimit == 'number' && timeLimit > timeLimitMax) {
                    form.setFieldsValue({ timeLimit: timeLimitMax });
                  }
                }}
              />
            </Form.Item>
            <PaperPickerFormItem
              layout={formItemLayoutProps}
              form={form}
              setPassingScoreMax={setPassingScoreMax}
            />
            <Form.Item
              {...formItemLayoutProps}
              label="考试时长"
              name="timeLimit"
              rules={[
                {
                  required: true,
                  message: '考试时长必填！',
                },
              ]}
            >
              <ExamMutatorTimeLimit max={timeLimitMax} />
            </Form.Item>
            <Form.Item
              {...formItemLayoutProps}
              label="及格分数"
              name="passingScore"
              rules={[
                {
                  required: true,
                  message: '及格分数必填！',
                },
              ]}
            >
              <InputNumber min={0} max={passingScoreMax} />
            </Form.Item>
            <Form.Item
              {...formItemLayoutProps}
              label="人员选择"
              name="users"
              rules={[
                {
                  required: true,
                  message: '必考人员必选！',
                  type: 'boolean',
                  transform: (value: Users) => {
                    if (
                      !value.requiredUsers?.roleKeys?.length &&
                      !value.requiredUsers?.userKeys?.length
                    ) {
                      return undefined;
                    }
                    return true;
                  },
                },
              ]}
            >
              <GroupUsersPicker
                requiredText="必考人员设置"
                optionalText="选考人员设置"
                showDepts={false}
                disabledUserKeys={markUsers?.userKeys}
                onChange={value => {
                  value.required && setRequiredUsers(value.requiredUsers);
                }}
                onReset={() => form.setFieldValue('users', userData)}
                onSubmit={() => setUserData(users)}
              />
            </Form.Item>
            <Form.Item
              {...formItemLayoutProps}
              label="考试范围"
              tooltip={{
                title:
                  '考试范围为需要参加本场考试的人员范围，系统将通过必考人员、选考人员+考试范围确定参加考试的人员。若必考人员为自定义人员，则以自定义人员为准。',
                icon: <ExclamationCircleOutlined />,
              }}
              name="scopes"
              rules={[
                {
                  required: true,
                  message: '考试范围必选！',
                },
              ]}
            >
              <LocationTreeSelect
                nodeTypes={['IDC']}
                style={{ width: 390 }}
                multiple
                authorizedOnly
              />
            </Form.Item>
            <Form.Item
              {...formItemLayoutProps}
              label="判卷人员"
              name="markUsers"
              rules={[
                {
                  required: true,
                  message: '判卷人员必选！',
                  type: 'boolean',
                  transform: (value?: ComplexUsersPickerValue) => {
                    if (!value?.roleKeys?.length && !value?.userKeys?.length) {
                      // `undefined` 表示校验未通过
                      return undefined;
                    }
                    return true;
                  },
                },
              ]}
            >
              <ComplexUsersPicker
                modalTitle="设置判卷人员"
                disabledUserKeys={requiredUsers?.userKeys}
                onChange={value => {
                  setMarkUsers(value);
                }}
              >
                <Button type="primary">设置</Button>
              </ComplexUsersPicker>
            </Form.Item>
          </Card>
          <Card title="考试设置" bordered={false}>
            <Space size="large">
              <Form.Item
                label="交卷后允许查看试卷"
                name="allowReviewPaperAfterHandedIn"
                valuePropName="checked"
              >
                <Switch />
              </Form.Item>
              <Form.Item label="考生同卷" name="useSameMasterPaper" valuePropName="checked">
                <Switch />
              </Form.Item>
            </Space>
          </Card>
        </Space>
      </Form>
    );
  }
);

export const defaultInitialValues: ExamMutatorFormValues = {
  timeLimit: DEFAULT_TIME_LIMIT,
  passingScore: 100,
  allowReviewPaperAfterHandedIn: true,
  useSameMasterPaper: true,
  users: {
    requiredUsers: { roleKeys: [], userKeys: [] },
    optionalUsers: { roleKeys: [], userKeys: [] },
  } as Users,
};

ExamMutator.displayName = 'ExamMutator';
ExamMutator.defaultProps = {
  initialValues: defaultInitialValues,
};

function range(start: number, end: number) {
  const result: number[] = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }

  return result;
}
