import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';

import { ExamTypes, selectTypedExams, updateExamsAction } from '@manyun/knowledge-hub.state.exams';
import { ExamsCategoryCascader } from '@manyun/knowledge-hub.ui.exams-category';

export type ExamsBatchUpdatorProps = {
  /** 按钮启禁用 */
  disabled?: boolean;
};

type ExamsBatchUpdatorFormValues = {
  categoryCode: [number, number, number];
};

export function ExamsBatchUpdator({ disabled }: ExamsBatchUpdatorProps) {
  const [visible, setVisible] = React.useState(false);
  const [loading, setLoading] = React.useState(false);
  const [form] = Form.useForm<ExamsBatchUpdatorFormValues>();

  const { selectedIds } = useSelector(selectTypedExams(ExamTypes.AllExam));

  const dispatch = useDispatch();
  const onHandleUpdatePapers = () => {
    form.validateFields().then(({ categoryCode }) => {
      setLoading(true);
      dispatch(
        updateExamsAction({
          // 只传第三级考试分类
          categoryCode: categoryCode[2],
          callback(result) {
            if (result) {
              setVisible(false);
              form.resetFields();
            }
            setLoading(false);
          },
        })
      );
    });
  };

  return (
    <>
      <Button
        disabled={disabled === undefined ? selectedIds.length <= 0 : disabled}
        type="primary"
        onClick={() => setVisible(true)}
      >
        批量更新
      </Button>
      <Modal
        title="批量更新"
        okText="提交"
        visible={visible}
        confirmLoading={loading}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={onHandleUpdatePapers}
      >
        <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            name="categoryCode"
            label="考试分类"
            rules={[
              {
                required: true,
                type: 'boolean',
                transform: value => {
                  // `false` 时返回 `undefined` 来表示校验失败
                  return value?.length === 3 || undefined;
                },
                message: '考试分类必须选到第三级！',
              },
            ]}
          >
            <ExamsCategoryCascader style={{ width: '100%' }} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
