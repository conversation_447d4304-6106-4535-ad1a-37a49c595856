import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { CoursesDeleter } from './courses-deleter';

export const BatchCoursesDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursesDeleter />
    </FakeStore>
  </ConfigProvider>
);

export const SingleCoursesDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursesDeleter id={1} />
    </FakeStore>
  </ConfigProvider>
);
