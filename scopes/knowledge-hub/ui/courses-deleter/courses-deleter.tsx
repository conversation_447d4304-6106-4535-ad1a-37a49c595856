import React from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import type { BackendCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { deleteCoursesAction } from '@manyun/knowledge-hub.state.courses';
import { selectAllCourses, selectCourseEntity } from '@manyun/knowledge-hub.state.courses';

export type CoursesDeleterProps = {
  id?: number;
};

export function CoursesDeleter({ id }: CoursesDeleterProps) {
  const dispatch = useDispatch();
  const batchDelete = id === undefined;
  const course = useSelector(selectCourseEntity(id)) as BackendCourse;
  const { selectedIds } = useSelector(selectAllCourses());

  const onHandleDelete = () => {
    Modal.confirm({
      title: batchDelete ? (
        '确认要删除所选的课程吗？'
      ) : (
        <span>
          确认要删除<span>{course.name}</span>的课程吗？
        </span>
      ),
      content: '删除后该课程将不可学习。',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dispatch(
          deleteCoursesAction({
            id,
            callback: res => {
              console.info('result:', res);
            },
          })
        );
      },
    });
  };

  return (
    <>
      <Button
        disabled={batchDelete && selectedIds.length === 0}
        type={batchDelete ? 'primary' : 'link'}
        danger={batchDelete}
        onClick={onHandleDelete}
        style={batchDelete ? {} : { padding: 0 }}
      >
        {batchDelete ? '批量删除' : '删除'}
      </Button>
      <Modal />
    </>
  );
}
