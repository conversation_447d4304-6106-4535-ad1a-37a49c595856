import React from 'react';
import { Link } from 'react-router-dom';

import { CardSize } from 'antd/es/card/Card';

import { Card } from '@manyun/base-ui.ui.card';
import type { CardProps } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { List } from '@manyun/base-ui.ui.list';
import { message } from '@manyun/base-ui.ui.message';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { WIKIS_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchCoursewaresByType } from '@manyun/resource-hub.service.fetch-coursewares-by-type';
import type { Courseware } from '@manyun/resource-hub.service.fetch-coursewares-by-type';

import styles from './coursewares-card.module.less';

export type CoursewaresCardProps = {
  deviceType: string;
  size?: CardSize;
  style?: React.CSSProperties;
  bodyStyle?: React.CSSProperties;
} & Omit<CardProps, 'style' | 'bodyStyle'>;

export function CoursewaresCard({ deviceType, style, bodyStyle, ...rest }: CoursewaresCardProps) {
  const [loading, setLoading] = React.useState(false);
  const [list, setList] = React.useState<Courseware[]>([]);

  React.useEffect(() => {
    (async () => {
      if (deviceType) {
        setLoading(true);
        const { data, error } = await fetchCoursewaresByType({ targetId: deviceType });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setList(data.data);
      }
    })();
  }, [deviceType]);

  return (
    <Card
      style={style}
      className={styles.card}
      bodyStyle={{ overflowY: 'auto', ...bodyStyle }}
      title="知识库"
      extra={<Link to={WIKIS_ROUTE_PATH}>更多</Link>}
      {...rest}
    >
      {list.length > 0 ? (
        <List
          loading={loading}
          dataSource={list}
          split={false}
          renderItem={courseware => (
            <List.Item key={courseware.groupKey}>
              <Skeleton loading={loading} active title>
                <List.Item.Meta
                  title={
                    !courseware.filePath ? (
                      <Tooltip title={courseware.fileName}>
                        <Typography.Text ellipsis>{courseware.fileName}</Typography.Text>
                      </Tooltip>
                    ) : (
                      <FilePreviewWithContainer
                        key={`preview_${courseware.groupKey}`}
                        file={{
                          name: courseware.fileName,
                          src: McUploadFile.generateSrc(courseware.filePath, courseware.fileName),
                          ext: courseware.fileType.toLocaleLowerCase(),
                        }}
                      >
                        <Tooltip title={courseware.fileName}>
                          <Typography.Link ellipsis>{courseware.fileName}</Typography.Link>
                        </Tooltip>
                      </FilePreviewWithContainer>
                    )
                  }
                />
                <>{courseware.operatorName}</>
              </Skeleton>
            </List.Item>
          )}
        />
      ) : (
        <Empty />
      )}
    </Card>
  );
}
