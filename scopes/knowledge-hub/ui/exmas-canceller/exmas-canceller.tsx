import React from 'react';
import { useDispatch } from 'react-redux';

import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { BackendExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { cancleExamAction } from '@manyun/knowledge-hub.state.exams';

export type ExmasCancellerProps = {
  exam: BackendExam;
};

export function ExmasCanceller({ exam }: ExmasCancellerProps) {
  const dispatch = useDispatch();

  const onHandleCancle = () => {
    Modal.confirm({
      width: 480,
      title: (
        <span>
          确认要取消
          <span style={{ color: `var(--${prefixCls}-warning-color)` }}>{exam.name}</span>
          这场考试吗?
        </span>
      ),
      icon: <ExclamationCircleOutlined />,
      content: '取消之后考生将不可考试。',
      onOk: () => {
        dispatch(
          cancleExamAction({
            examId: exam.id,
          })
        );
      },
    });
  };

  return (
    <Button compact type="link" onClick={onHandleCancle}>
      取消考试
    </Button>
  );
}
