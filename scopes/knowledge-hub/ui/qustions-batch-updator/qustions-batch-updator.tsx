import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  selectAllTypeQuestions,
  updateQuestionsAction,
} from '@manyun/knowledge-hub.state.questions';
import { SelectTreeQuestionCategory } from '@manyun/knowledge-hub.ui.questions-category';

export type QustionsBatchUpdatorProps = {
  onComplteUpdate?: (res: boolean) => void;
};

export function QustionsBatchUpdator({ onComplteUpdate }: QustionsBatchUpdatorProps) {
  const dispatch = useDispatch();

  const [isModalVisible, setIsModalVisible] = useState(false);

  const [loading, setLoading] = useState(false);

  const [form] = Form.useForm();

  const { selectedIds } = useSelector(selectAllTypeQuestions());

  const onHandleUpdateQuestions = () => {
    setLoading(true);
    form
      .validateFields()
      .then(({ categoryCode }) => {
        dispatch(
          updateQuestionsAction({
            categoryCode,
            callback: res => {
              setLoading(false);
              if (onComplteUpdate) {
                onComplteUpdate(res);
              }
              if (res) {
                setIsModalVisible(false);
              }
            },
          })
        );
      })
      .catch(err => {
        setLoading(false);

        // 验证不通过时进入 console.log(err);
      });
  };

  return (
    <>
      <Button
        disabled={selectedIds.length === 0}
        type="primary"
        onClick={() => setIsModalVisible(true)}
      >
        批量更新
      </Button>
      {isModalVisible && (
        <Modal
          title="批量更新"
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          onOk={() => onHandleUpdateQuestions()}
          confirmLoading={loading}
          okText="提交"
          cancelText="取消"
        >
          <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            <Form.Item
              name="categoryCode"
              label="试题分类"
              rules={[{ required: true, message: '请选择试题分类' }]}
            >
              <SelectTreeQuestionCategory />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}
