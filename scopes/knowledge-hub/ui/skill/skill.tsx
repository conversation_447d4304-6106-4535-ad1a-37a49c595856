import { EyeOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
// import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackendSkills } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';
import { SkillsLevel } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';
import type { BackendUserSkill } from '@manyun/knowledge-hub.service.dcexam.fetch-user-skills';
import {
  SKILL_LIMIT_TIME_TEXT_MAP,
  SkillLimitUnit,
} from '@manyun/knowledge-hub.service.dcexam.mutate-skill';
import { selectSkillEntity } from '@manyun/knowledge-hub.state.skills';
import { SkillMutator } from '@manyun/knowledge-hub.ui.skill-mutator';
import { SkillsCategory } from '@manyun/knowledge-hub.ui.skills-category';
import { SkillsDeleter } from '@manyun/knowledge-hub.ui.skills-deleter';
import { SKILLS_LEVEL_TEXT } from '@manyun/knowledge-hub.ui.skills-level';

import DefaultSkill from './assets/default-skill.png';
import HighSkillLevel from './assets/high-skill-level.png';
import LowSkillLevel from './assets/low-skill-level.png';
import MiddleSkillLevel from './assets/middle-skill-level.png';
import PersonalCenterDefaultLevel from './assets/personal-center-default-level.png';
import PersonalCenterHighLevel from './assets/personal-center-high-level.png';
import PersonalCenterLowLevel from './assets/personal-center-low-level.png';
import PersonalCenterMiddleLevel from './assets/personal-center-middle-level.png';
import SkillsHighLevel from './assets/skills-high-level.png';
import SkillsLowLevel from './assets/skills-low-level.png';
import SkillsMiddleLevel from './assets/skills-middle-level.png';
import UserManagementHighLevel from './assets/user-management-high-level.png';
import UserManagementLowLevel from './assets/user-management-low-level.png';
import UserManagementMiddleLevel from './assets/user-management-middle-level.png';
import { LearnPlansModal } from './components/learn-plans-modal';
// import skillCardBackground from './skill-card-background.png';
import { generateSkillLocation, judgeSkillIdIsReal } from './skill.utils';

export type SkillMutatorProps = {
  id?: number | string;
  type?: 'skill' | 'personalCenter' | 'userManagement';
  userSkillsData?: BackendUserSkill;
  style?: React.CSSProperties;
};

export function Skill({ id, type = 'skill', userSkillsData, style }: SkillMutatorProps) {
  const [data] = useState(useSelector(selectSkillEntity(id)) as BackendSkills);
  const history = useHistory();

  const skillData = userSkillsData ?? data;

  return !judgeSkillIdIsReal(id) ? (
    <Card
      style={{
        width: 290,
        height: 192,
      }}
      bodyStyle={{
        display: 'flex',
        flexWrap: 'wrap',
        justifyContent: 'center',
        alignContent: 'center',
        height: '100%',
      }}
    >
      <div>
        <div style={{ textAlign: 'center', marginBottom: 16 }}>
          <SkillMutator />
        </div>
        <div>上传技能认证</div>
      </div>
    </Card>
  ) : (
    <Card
      style={{
        width: style?.width ?? 290,
        height: style?.height ?? 192,
        backgroundImage: `url(${getBackgroundImage(skillData.level, type, userSkillsData?.own)})`,
        backgroundSize: `${style?.width ?? 290}px ${style?.height ?? 192}px`,
      }}
      bodyStyle={{ padding: 16 }}
    >
      <div
        style={{
          width: '100%',
          // backgroundImage: `url(${getBackgroundImage(skillData.level, type, userSkillsData?.own)})`,
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={24}>
          <Space style={{ justifyContent: 'space-between', width: '100%' }} align="start">
            <img
              src={getSkillLevelImage(skillData.level, type, userSkillsData?.own)}
              style={{ width: 60, height: 56 }}
              alt="SkillLevelPng"
            />
            {typeof userSkillsData?.expire === 'boolean' && (
              <Tag color={userSkillsData.expire ? 'error' : 'success'}>
                {userSkillsData.expire ? '过期' : '有效'}
              </Tag>
            )}
          </Space>
          <Space direction="vertical" size={2}>
            <Typography.Text
              style={{ width: 200 }}
              ellipsis={{ tooltip: `${SKILLS_LEVEL_TEXT?.[skillData.level]}${skillData.name}` }}
              type={userSkillsData?.own === false ? 'secondary' : undefined}
            >{`${SKILLS_LEVEL_TEXT?.[skillData.level]}${skillData.name}`}</Typography.Text>
            {type !== 'personalCenter' && (
              <SkillsCategory
                style={{ fontSize: 12 }}
                type="secondary"
                categoryCode={Number(skillData.categoryCode)}
              />
            )}
            {type === 'personalCenter' && !(userSkillsData?.expire === false) && (
              <LearnPlansModal certId={Number(skillData.id)} />
            )}
            {type === 'skill' && (
              <Typography.Text style={{ fontSize: 12 }} type="secondary">
                {`持证有效期：${data.limitTime && data.limitUnit ? `${data.limitTime}${data.limitUnit === SkillLimitUnit.Month ? '个' : ''}${SKILL_LIMIT_TIME_TEXT_MAP[data.limitUnit]}` : '--'}`}
              </Typography.Text>
            )}
            {type !== 'skill' && userSkillsData?.own && (
              <Typography.Text style={{ fontSize: 12 }} type="secondary">
                {`过期日期：${userSkillsData?.expireTime ? dayjs(userSkillsData?.expireTime).format('YYYY-MM-DD') : '--'}`}
              </Typography.Text>
            )}
            {type === 'skill' && (
              <Space>
                <EyeOutlined onClick={() => history.push(generateSkillLocation({ id: id! }))} />
                <SkillMutator id={id} />
                <SkillsDeleter id={id as number | string} />
              </Space>
            )}
          </Space>
        </Space>
      </div>
    </Card>
  );
}

function getSkillLevelImage(
  skillLevel: SkillsLevel,
  type: 'skill' | 'personalCenter' | 'userManagement',
  own?: boolean
) {
  if (type === 'personalCenter' && typeof own === 'boolean' && !own) {
    return DefaultSkill;
  }

  switch (skillLevel) {
    case SkillsLevel.HIGH:
      return HighSkillLevel;
    case SkillsLevel.LOW:
      return LowSkillLevel;

    case SkillsLevel.MIDDLE:
      return MiddleSkillLevel;
    default:
      return DefaultSkill;
  }
}

function getBackgroundImage(
  skillLevel: SkillsLevel,
  type: 'skill' | 'personalCenter' | 'userManagement',
  own?: boolean
) {
  if (type === 'personalCenter' && typeof own === 'boolean' && !own) {
    return PersonalCenterDefaultLevel;
  }
  if (type === 'personalCenter') {
    switch (skillLevel) {
      case SkillsLevel.HIGH:
        return PersonalCenterHighLevel;
      case SkillsLevel.LOW:
        return PersonalCenterLowLevel;

      case SkillsLevel.MIDDLE:
        return PersonalCenterMiddleLevel;
      default:
        return PersonalCenterDefaultLevel;
    }
  }
  if (type === 'skill') {
    switch (skillLevel) {
      case SkillsLevel.HIGH:
        return SkillsHighLevel;
      case SkillsLevel.LOW:
        return SkillsLowLevel;

      case SkillsLevel.MIDDLE:
        return SkillsMiddleLevel;
      default:
        return PersonalCenterDefaultLevel;
    }
  }
  if (type === 'userManagement') {
    switch (skillLevel) {
      case SkillsLevel.HIGH:
        return UserManagementHighLevel;
      case SkillsLevel.LOW:
        return UserManagementLowLevel;

      case SkillsLevel.MIDDLE:
        return UserManagementMiddleLevel;
      default:
        return PersonalCenterDefaultLevel;
    }
  }
}
