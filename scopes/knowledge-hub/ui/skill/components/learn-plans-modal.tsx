import React, { useCallback, useState } from 'react';
import { Link } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';

import { generateExamRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchAssociatedExamCategoriesWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-associated-exam-categories';
import { Variant, fetchExamsWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import type { BackendExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { ExamsCategory } from '@manyun/knowledge-hub.ui.exams-category';

const columns: ColumnType<BackendExam>[] = [
  {
    ellipsis: true,
    title: '考试名称',
    dataIndex: 'name',
    fixed: 'left',
    render(name, { id }) {
      return <Link to={generateExamRoutePath(id)}>{name}</Link>;
    },
  },
  {
    ellipsis: true,
    title: '考试分类',
    dataIndex: 'categoryCode',
    render(categoryCode: string) {
      return <ExamsCategory categoryCode={Number(categoryCode)} />;
    },
  },
  {
    width: 90,
    title: '考试时长',
    dataIndex: 'duration',
    render(duration) {
      return `${duration}min`;
    },
  },
  {
    width: 65,
    title: '总分',
    dataIndex: 'totalGrade',
  },

  {
    title: '考试时间',
    dataIndex: '_time',
    render(_, { startTime, endTime }) {
      const formatStyle = 'YYYY-MM-DD HH:mm';

      return `${dayjs(startTime).format(formatStyle)}~${dayjs(endTime).format(formatStyle)}`;
    },
  },
  {
    width: 116,
    title: '是否需要参加',
    dataIndex: 'needJoin',
    render(needJoin) {
      return needJoin ? '是' : '否';
    },
  },
];

export function LearnPlansModal({ certId }: { certId: number }) {
  const [open, setOpen] = useState(false);
  const [exams, setExams] = useState<BackendExam[]>();
  const [loading, setLoading] = useState(false);
  const [categoryCodes, setCategoryCodes] = useState<string[]>([]);
  const [pagination, setPagination] = useState({ pageNum: 1, pageSize: 10 });
  const [total, setTotal] = useState<number>(0);

  const showModal = () => {
    setOpen(true);
    fetchCategoryCode();
  };
  const closeModal = () => {
    setOpen(false);
  };

  useDeepCompareEffect(() => {
    if (categoryCodes.length > 0) {
      fetchExams();
    }
  }, [categoryCodes, pagination]);

  const fetchExams = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchExamsWeb({
      categoryCodes,
      variant: Variant.AllExam,
      page: pagination.pageNum,
      pageSize: pagination.pageSize,
    });

    if (error) {
      message.error(error.message);
      return;
    }
    setExams(data.data);
    setTotal(data.total);
    setLoading(false);
  }, [pagination, categoryCodes]);

  const fetchCategoryCode = async () => {
    setLoading(true);
    const { error, data } = await fetchAssociatedExamCategoriesWeb({
      certId,
    });

    if (error) {
      message.error(error.message);
      return;
    }
    if (Array.isArray(data.data) && data.data.length > 0) {
      setCategoryCodes(data.data.map(item => item.categoryCode));
    }
    setLoading(false);
  };

  return (
    <>
      <Button
        style={{ padding: 0, height: 22 }}
        type="link"
        onClick={() => {
          showModal();
        }}
      >
        学习计划
      </Button>
      <Modal
        width="85%"
        bodyStyle={{ maxHeight: 'calc(80vh - 109px)', overflowY: 'auto' }}
        title="学习计划"
        open={open}
        footer={null}
        onCancel={() => {
          closeModal();
        }}
        onOk={() => {
          closeModal();
        }}
      >
        <Table
          style={{ width: '100%' }}
          loading={loading}
          columns={columns}
          dataSource={exams}
          // scroll={{ x: 'max-content' }}
          pagination={{
            total,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (page, pageSize) => setPagination({ pageNum: page, pageSize }),
          }}
        />
      </Modal>
    </>
  );
}
