import React from 'react';
import { Route, MemoryRouter as Router } from 'react-router-dom';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { Skill } from './skill';

export const BasicSkill = () => (
  <Router initialEntries={['/']}>
    <Route path="/" exact>
      <FakeStore>
        <Skill id={2} />
      </FakeStore>
    </Route>
    <Route path={'/path/2'}>
      <>5555</>
    </Route>
  </Router>
);

export const BasicSkillCreator = () => (
  <FakeStore>
    <Skill />
  </FakeStore>
);
