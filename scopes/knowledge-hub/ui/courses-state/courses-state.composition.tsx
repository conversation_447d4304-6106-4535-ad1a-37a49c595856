import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { CourseStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';

import { SelectCoursesState, SwitchCoursesState, TagCoursesState } from './courses-state';

export const BasicSelectCoursesState = () => (
  <ConfigProvider>
    <FakeStore>
      <SelectCoursesState />
    </FakeStore>
  </ConfigProvider>
);

export const BasicTagCoursesState = () => (
  <ConfigProvider>
    <FakeStore>
      <TagCoursesState typeVal={CourseStatus.NORMAL} />
    </FakeStore>
  </ConfigProvider>
);

export const BasicSwitchCoursesState = () => (
  <ConfigProvider>
    <FakeStore>
      <SwitchCoursesState typeVal={CourseStatus.NORMAL} />
    </FakeStore>
  </ConfigProvider>
);
