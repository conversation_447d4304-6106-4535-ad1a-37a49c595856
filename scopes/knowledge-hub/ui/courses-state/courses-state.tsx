import React from 'react';

import { SelectValue } from 'antd/es/select';

import { Select, SelectProps } from '@manyun/base-ui.ui.select';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tag } from '@manyun/base-ui.ui.tag';

import { CourseStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';

export interface CoursesStateProps extends SelectProps<SelectValue> {
  typeVal?: string;
}

export function SelectCoursesState({ onChange, ...props }: CoursesStateProps) {
  const value =
    typeof props.value === 'boolean'
      ? props.value
        ? CourseStatus.NORMAL
        : CourseStatus.DISABLE
      : props.value;

  return (
    <Select
      allowClear
      value={value}
      style={{ width: 200, ...props.style }}
      options={options}
      onChange={onChange}
    />
  );
}

export function TagCoursesState({ typeVal, ...props }: CoursesStateProps) {
  return (
    <Tag color={typeVal && textMap[typeVal].color} style={props.style}>
      {typeVal && textMap[typeVal].label}
    </Tag>
  );
}

export function SwitchCoursesState({ typeVal, ...props }: CoursesStateProps) {
  return <Switch defaultChecked={typeVal !== undefined ? typeVal === CourseStatus.NORMAL : true} />;
}

const textMap = {
  [CourseStatus.NORMAL as string]: { label: '正常', color: 'green' },
  [CourseStatus.DISABLE as string]: { label: '禁用', color: 'red' },
};

const options: any[] = [
  { label: '正常', value: CourseStatus.NORMAL as string },
  { label: '禁用', value: CourseStatus.DISABLE as string },
];
