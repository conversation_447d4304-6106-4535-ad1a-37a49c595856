import type { TableRowSelection } from 'antd/es/table/interface';
import dayjs from 'dayjs';
import moment from 'moment';
import React, { useCallback, useEffect } from 'react';
import { Link } from 'react-router-dom';

import { User } from '@manyun/auth-hub.ui.user';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { useAllCourses, useMyCourses } from '@manyun/knowledge-hub.hook.use-courses';
import { useCoursesFields } from '@manyun/knowledge-hub.hook.use-courses-fields';
import { useCoursesSelected } from '@manyun/knowledge-hub.hook.use-courses-selected';
import {
  generateCourseDetailRoutePath,
  generateEditSpecificCourseRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type {
  BackendCourse,
  CompletedVariant,
} from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { Course } from '@manyun/knowledge-hub.ui.course';
import { CourseImagesViewButton } from '@manyun/knowledge-hub.ui.course-images-view-button';
import { CoursesCategory } from '@manyun/knowledge-hub.ui.courses-category';
import { CoursesDeleter } from '@manyun/knowledge-hub.ui.courses-deleter';
import { TagCoursesState } from '@manyun/knowledge-hub.ui.courses-state';
import { ReviewCourseModal } from '@manyun/knowledge-hub.ui.review-course-modal';
import { UploadCourseImagesModal } from '@manyun/knowledge-hub.ui.upload-course-images-modal';
import { SCH_MODE_TYPE_TEXT } from '@manyun/ticket.model.task';
import type { SchModeType } from '@manyun/ticket.model.task';

export function AllCourses() {
  const [, { checkUserId }] = useAuthorized();

  const [setSelectedIds] = useCoursesSelected();
  const [setFields] = useCoursesFields({ variant: 'all' });
  const [getCourses, { loading, data, fields, selectedIds }] = useAllCourses();

  useEffect(() => {
    getCourses();
  }, [getCourses]);

  const rowSelection: TableRowSelection<BackendCourse> | undefined = {
    selectedRowKeys: selectedIds,
    onChange: ids => {
      setSelectedIds({ selectedIds: ids as number[] });
    },
  };

  const pageHandler = useCallback(
    (_page: number, _pageSize: number) => {
      if (selectedIds.length > 0) {
        setSelectedIds({ selectedIds: [] });
      }
      setFields({ page: _page, pageSize: _pageSize, variant: 'all' });
      getCourses();
    },
    [selectedIds, setSelectedIds, getCourses, setFields]
  );

  const columns = [
    {
      title: '课程名称',
      dataIndex: 'name',
      ellipsis: true,
      render: (text: string, record: BackendCourse) => (
        <Link to={generateCourseDetailRoutePath(record.id)}>
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        </Link>
      ),
    },
    {
      title: '课程分类',
      dataIndex: 'categoryCode',
      visible: true,
      ellipsis: true,
      render: (txt: string) => <CoursesCategory categoryCode={Number(txt)} />,
    },
    {
      title: '培训方式',
      dataIndex: 'trainMode',
      render: (trainMode?: SchModeType) => (trainMode ? SCH_MODE_TYPE_TEXT[trainMode] : '--'),
    },
    {
      ellipsis: true,
      title: '开始时间',
      dataIndex: 'startTime',
      width: 200,
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      ellipsis: true,
      title: '结束时间',
      dataIndex: 'endTime',
      width: 200,
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      width: 60,
      render: (text: string) => <TagCoursesState typeVal={text} />,
    },
    {
      title: '现场照片',
      dataIndex: 'fileNum',
      render: (text: number, record: BackendCourse) =>
        text ? <CourseImagesViewButton courseId={record.id} fileNum={text} /> : 0,
    },
    {
      title: '创建人',
      dataIndex: 'operatorName',
      width: 200,
      render: (text: string, record: BackendCourse) => (
        <User.Link id={record.operatorId!} name={text} />
      ),
    },
    {
      ellipsis: true,
      title: '创建时间',
      dataIndex: 'gmtCreate',
      width: 200,
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '负责人',
      dataIndex: 'owner',
      render: (text: string, record: BackendCourse) =>
        record.ownerId && record.ownerName ? (
          <UserLink userId={record.ownerId} userName={record.ownerName} />
        ) : (
          '--'
        ),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      // width: 240,
      render: (text: string, record: BackendCourse) => {
        const isOwner = checkUserId(record.ownerId);
        const isOpen = dayjs().diff(dayjs(record.startTime)) > 0;

        return (
          <Space>
            <Link key="link" to={generateEditSpecificCourseRoutePath(record.id)}>
              编辑
            </Link>
            {isOwner && (
              <UploadCourseImagesModal key="upload" courseId={record.id} onSearch={getCourses} />
            )}
            {isOpen && isOwner && !record.review && (
              <ReviewCourseModal
                courseId={record.id}
                review={record.review}
                onSearch={getCourses}
              />
            )}
            <CoursesDeleter key="delete" id={record.id} />
          </Space>
        );
      },
    },
  ];

  return (
    <Table
      size="small"
      rowKey="id"
      scroll={{ x: 'max-content' }}
      loading={loading}
      columns={columns}
      dataSource={data.data}
      pagination={{
        current: fields.page,
        pageSize: fields.pageSize,
        total: data.total,
        onChange: pageHandler,
      }}
      rowSelection={rowSelection}
    />
  );
}

export type MyCoursesProps = {
  completedVariant: CompletedVariant;
};

export function MyCourses({ completedVariant }: MyCoursesProps) {
  const [getCourses, { data }] = useMyCourses({ completedVariant });

  useEffect(() => {
    getCourses();
  }, [getCourses]);

  const empty = () => (
    <div style={{ textAlign: 'center', width: '100%' }}>
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
    </div>
  );

  return data.length > 0 ? (
    <div
      style={{
        display: 'grid',
        gridTemplateColumns: 'repeat(auto-fill,minmax(242px,1fr))',
        gridRowGap: '58px',
        gridColumnGap: '16px',
      }}
    >
      {data.map(item => (
        <Course key={item.id} id={item.id} />
      ))}
    </div>
  ) : (
    empty()
  );
}
