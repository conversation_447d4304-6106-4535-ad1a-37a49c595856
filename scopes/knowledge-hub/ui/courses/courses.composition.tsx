import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { AllCourses } from './courses';

export const AllCoursewares = () => (
  <ConfigProvider>
    <FakeStore>
      <Router>
        <AllCourses />
      </Router>
    </FakeStore>
  </ConfigProvider>
);
