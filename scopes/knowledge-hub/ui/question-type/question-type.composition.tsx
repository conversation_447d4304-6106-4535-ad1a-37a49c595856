import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import {
  ButtonQuestionType,
  SelectQuestionType,
  TagQuestionType,
  TextQuestionType,
} from './question-type';

export const TagQuestionTypeDiv = () => (
  <ConfigProvider>
    <FakeStore>
      <TagQuestionType typeVal={1} />
    </FakeStore>
  </ConfigProvider>
);

export const TextQuestionTypeDiv = () => (
  <ConfigProvider>
    <FakeStore>
      <TextQuestionType typeVal={1} />
    </FakeStore>
  </ConfigProvider>
);

export const ButtonQuestionTypeDiv = () => (
  <ConfigProvider>
    <FakeStore>
      <ButtonQuestionType typeVal={1} />
    </FakeStore>
  </ConfigProvider>
);

export const SelectQuestionTypeDiv = () => (
  <ConfigProvider>
    <FakeStore>
      <SelectQuestionType />
    </FakeStore>
  </ConfigProvider>
);

export const MultipleSelectQuestionType = () => (
  <ConfigProvider>
    <FakeStore>
      <SelectQuestionType mode="multiple" style={{ width: 200 }} />
    </FakeStore>
  </ConfigProvider>
);
