---
description: '试题类型 UI'
labels: ['label1', 'label2', 'QuestionType']
---

## React Component for rendering text

A basic div that renders some text

### Component usage

```js
<QuestionType variant="tag" typeVal={1} />
```

```js
<QuestionType variant="button" onClick={() => {}} />
```

```js
<QuestionType variant="sigle" onChange={(val) => {}} />
```

```js
<QuestionType variant="multiple" onChange={(val) => {}} />
```
