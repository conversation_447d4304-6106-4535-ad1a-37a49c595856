import React from 'react';

import { SelectValue } from 'antd/es/select';

import { Button } from '@manyun/base-ui.ui.button';
import { Select, SelectProps } from '@manyun/base-ui.ui.select';
import { Tag } from '@manyun/base-ui.ui.tag';

import { useQuestionType } from '@manyun/knowledge-hub.hook.use-questions-type';

export interface QuestionTypeProps extends SelectProps<SelectValue> {
  typeVal?: string | number;
  onClick?: () => void;
}

export function SelectQuestionType({ onChange, onClick, ...props }: QuestionTypeProps) {
  const [typeOptions] = useQuestionType(true);
  return (
    <Select
      value={props.value}
      defaultValue={props.defaultValue}
      style={{ width: '100%', ...props.style }}
      mode={props.mode}
      options={typeOptions as []}
      onChange={onChange}
      allowClear={props.allowClear}
      {...props}
    />
  );
}

export function ButtonQuestionType({ typeVal, onClick }: QuestionTypeProps) {
  const [typeOptions] = useQuestionType(false);
  return (
    <Button type="primary" onClick={onClick}>
      {typeVal && typeOptions[typeVal as any]}
    </Button>
  );
}

export function TagQuestionType({ typeVal, onChange, onClick, ...props }: QuestionTypeProps) {
  const [typeOptions] = useQuestionType(false);
  return (
    <Tag
      color="#87d068"
      style={{
        background: 'var(--manyun-success-color-outline)',
        color: 'var(--manyun-success-color)',
        ...props.style,
      }}
    >
      {typeVal && typeOptions[typeVal as any]}
    </Tag>
  );
}

export function TextQuestionType({ typeVal, ...props }: QuestionTypeProps) {
  const [typeOptions] = useQuestionType(false);
  return <span style={props.style}>{typeVal && typeOptions[typeVal as any]}</span>;
}
