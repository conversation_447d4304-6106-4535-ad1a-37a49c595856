import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks as mutateSkillWebMocks } from '@manyun/knowledge-hub.service.dcexam.mutate-skill';
import { getMock } from '@manyun/service.request';

import { SkillMutator } from './skill-mutator';

export const BasicSkillUpdateMutator = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    mutateSkillWebMocks(mock);
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <SkillMutator />
      </FakeStore>
    </ConfigProvider>
  );
};
