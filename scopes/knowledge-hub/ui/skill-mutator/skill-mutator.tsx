import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import { EditOutlined, PlusCircleFilled } from '@ant-design/icons';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import { useSkillsMutation } from '@manyun/knowledge-hub.hook.use-skills-mutation';
import {
  SKILL_LIMIT_TIME_TEXT_MAP,
  SkillLimitUnit,
} from '@manyun/knowledge-hub.service.dcexam.mutate-skill';
import { mutateSkillAction, selectSkillEntity } from '@manyun/knowledge-hub.state.skills';
import { SkillsCategory, SkillsCategoryCascader } from '@manyun/knowledge-hub.ui.skills-category';
import { SkillsLevel } from '@manyun/knowledge-hub.ui.skills-level';

export type SkillMutatorProps = {
  id?: number | string;
};

export function SkillMutator({ id }: SkillMutatorProps) {
  const [form] = Form.useForm();
  const [visible, setVisiable] = useState(false);
  const [data, setData]: any = useState(useSelector(selectSkillEntity(id)));
  const [mutateSkill, { loading, result }] = useSkillsMutation();
  const toggleVisiable = () => setVisiable(!visible);

  const limitUnit = Form.useWatch('limitUnit', form);

  const onHandleUpdateSkill = useCallback(() => {
    form
      .validateFields()
      .then(values => {
        const params = {
          id,
          categoryCode: id
            ? data.categoryCode
            : values.categoryCode[values.categoryCode.length - 1],
          name: values.name,
          level: values.level,
          limitTime: values.limitTime,
          limitUnit: values.limitUnit,
        };
        setData(params);
        mutateSkill({
          params,
          callback: () => {
            setVisiable(false);
            setData({
              categoryCode: '',
              name: '',
              level: '',
            });
            form.resetFields();
          },
        });
      })
      .catch(err => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [mutateSkillAction]);

  useEffect(() => {
    if (visible && result) {
      setVisiable(false);
    }
  }, [visible, result]);

  return (
    <>
      {!id && id !== 0 ? (
        <PlusCircleFilled
          style={{ fontSize: 44, color: `var(--${prefixCls}-primary-color)` }}
          onClick={() => {
            toggleVisiable();
          }}
        />
      ) : (
        <EditOutlined
          key="edit"
          onClick={() => {
            toggleVisiable();
          }}
        />
      )}
      {visible && (
        <Modal
          title={id || id === 0 ? '编辑技能认证' : '上传技能认证'}
          visible={visible}
          width={560}
          confirmLoading={loading}
          okText="确定"
          cancelText="取消"
          destroyOnClose
          onOk={onHandleUpdateSkill}
          onCancel={toggleVisiable}
        >
          <Form form={form}>
            <Form.Item
              label="技能名称"
              name="name"
              rules={[
                { required: true, message: '请输入技能名称！' },
                { max: 30, message: '最多输入 30 个字符！' },
              ]}
              initialValue={data.name}
            >
              <Input style={{ width: 384 }} allowClear />
            </Form.Item>
            {!id && id !== 0 && (
              <Form.Item
                label="技能分类"
                name="categoryCode"
                rules={[
                  {
                    required: true,
                    message: '请选择技能三级分类！',
                    type: 'number',
                    transform: value => (value?.length === 3 ? 3 : false),
                  },
                ]}
              >
                <SkillsCategoryCascader style={{ width: 384 }} />
              </Form.Item>
            )}
            {(id || id === 0) && (
              <Form.Item label="技能分类">
                <SkillsCategory categoryCode={Number(data.categoryCode)} />
              </Form.Item>
            )}
            <Form.Item
              label="技能等级"
              name="level"
              rules={[{ required: true, message: '请选择技能等级！' }]}
              initialValue={data.level}
            >
              <SkillsLevel />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }} label="持证有效期" required>
              <Space.Compact>
                <Form.Item
                  name="limitTime"
                  rules={[{ required: true, message: `请输入持证有效期` }]}
                  initialValue={data.limitTime}
                  dependencies={['limitUnit']}
                >
                  <InputNumber min={1} precision={0} max={getMaxLimitTime(limitUnit)} />
                </Form.Item>
                <Form.Item
                  name="limitUnit"
                  rules={[{ required: true, message: `请选择持证有效期单位` }]}
                  initialValue={data.limitUnit ?? SkillLimitUnit.Month}
                >
                  <Select options={SKILLL_IMIT_UNIT_OPTIONS} />
                </Form.Item>
              </Space.Compact>
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}

const SKILLL_IMIT_UNIT_OPTIONS = [
  {
    value: SkillLimitUnit.Year,
    label: SKILL_LIMIT_TIME_TEXT_MAP[SkillLimitUnit.Year],
  },
  {
    value: SkillLimitUnit.Month,
    label: SKILL_LIMIT_TIME_TEXT_MAP[SkillLimitUnit.Month],
  },
  {
    value: SkillLimitUnit.Day,
    label: SKILL_LIMIT_TIME_TEXT_MAP[SkillLimitUnit.Day],
  },
];

function getMaxLimitTime(limitUnit: SkillLimitUnit) {
  /** 持证有效期最大100年，根据单位换算 */
  switch (limitUnit) {
    case SkillLimitUnit.Year:
      return 100;
    case SkillLimitUnit.Month:
      return 1200;
    case SkillLimitUnit.Day:
      return 438000;
    default:
      return 1200;
  }
}
