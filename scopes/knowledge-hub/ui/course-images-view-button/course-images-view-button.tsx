/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-23
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import type { File } from '@manyun/base-ui.util.guess-mime-type';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

export type CourseImagesViewButtonProps = { courseId: number; fileNum: number };

export function CourseImagesViewButton({ courseId, fileNum }: CourseImagesViewButtonProps) {
  const [attachments, setAttachments] = useState<File[]>([]);

  const fetchAttachments = useCallback(async () => {
    const { data, error } = await fetchBizFileInfos({
      targetId: courseId.toString(),
      targetType: 'COURSE',
      fileTypeList: ['COURSE_CERT_FILE'],
    });
    if (error) {
      message.error(error.message);
    } else {
      setAttachments(
        data.data.map(file => ({
          name: file.name,
          src: file.src,
          ext: file.ext,
        }))
      );
    }
  }, [courseId]);

  useEffect(() => {
    fetchAttachments();
  }, [fetchAttachments, fileNum]);

  if (!attachments.length) {
    return <>--</>;
  }

  return (
    <SimpleFileList
      files={attachments}
      children={
        <Button type="link" compact>
          {fileNum}
        </Button>
      }
    />
  );
}
