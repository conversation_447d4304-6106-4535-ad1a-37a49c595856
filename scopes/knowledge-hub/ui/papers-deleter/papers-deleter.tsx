import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { usePapersFields } from '@manyun/knowledge-hub.hook.use-papers-fields';
import { BackendPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { deletePapersAction, selectAllTypePapers } from '@manyun/knowledge-hub.state.papers';

export type PapersDeleterProps = {
  /**
   * 单个删除的试卷
   */
  paper?: Pick<BackendPaper, 'id' | 'name'>;
};

export function PapersDeleter({ paper }: PapersDeleterProps) {
  const dispatch = useDispatch();
  const [setFields] = usePapersFields();

  const { selectedIds } = useSelector(selectAllTypePapers());
  const batchDelete = paper === undefined;

  const onHandleDelete = () => {
    Modal.confirm({
      title:
        paper === undefined ? (
          `确定批量删除试卷？`
        ) : (
          <span>
            确定要删除
            <span style={{ color: `var(--${prefixCls}-warning-color)` }}>{paper.name}</span>
            试卷？
          </span>
        ),
      content: '删除之后,试卷将不可被用于考试中。',
      icon: <ExclamationCircleOutlined />,
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        dispatch(
          deletePapersAction({
            id: paper !== undefined ? paper.id : undefined,
            callback: res => {
              /**批量删除成功  更新page : 1 */
              if (res && paper === undefined) {
                setFields({ page: 1 });
              }
            },
          })
        );
      },
    });
  };

  return (
    <Button
      disabled={batchDelete && selectedIds.length === 0}
      type={batchDelete ? 'primary' : 'link'}
      danger={batchDelete}
      onClick={onHandleDelete}
      compact={batchDelete ? false : true}
    >
      {batchDelete ? '批量删除' : '删除'}
    </Button>
  );
}
