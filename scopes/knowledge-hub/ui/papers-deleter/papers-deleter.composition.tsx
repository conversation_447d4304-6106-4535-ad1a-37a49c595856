import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { PapersDeleter } from './papers-deleter';

export const BatchPapersDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <PapersDeleter />
    </FakeStore>
  </ConfigProvider>
);

export const SiglePapersDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <PapersDeleter paper={{ id: 1, name: '试卷123' }} />
    </FakeStore>
  </ConfigProvider>
);
