import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';

import { CoursewaresFilters } from './coursewares-filters';

export const BasicCoursewaresFilters = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursewaresFilters variant={Variant.ALL} />
    </FakeStore>
  </ConfigProvider>
);
