import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { useCoursewares } from '@manyun/knowledge-hub.hook.use-coursewares';
import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import {
  Variant,
  coursewaresSliceActions,
  selectCoursewares,
} from '@manyun/knowledge-hub.state.coursewares';

export type CoursewaresFiltersProps = {
  variant: Variant;
};

export function CoursewaresFilters({ variant }: CoursewaresFiltersProps) {
  const [form] = Form.useForm();
  const [loading] = useState(false);
  const [setFields] = useCoursewaresFields(variant);
  const {
    fields: { name },
  } = useSelector(selectCoursewares(variant));
  const [getCoursewares] = useCoursewares({ variant });
  const dispatch = useDispatch();

  useEffect(() => {
    form.setFieldsValue({
      name: name,
    });
  }, []);

  const onHandleSearch = useCallback(() => {
    getCoursewares();
  }, [getCoursewares]);

  const onHandleRest = useCallback(() => {
    dispatch(coursewaresSliceActions.resetFields({ variant }));
    getCoursewares();
  }, [dispatch, getCoursewares, variant]);

  return (
    <Form
      colon={false}
      form={form}
      layout="inline"
      onValuesChange={changedValues => {
        setFields({ ...changedValues });
      }}
      style={{ marginBottom: 12 }}
    >
      <Form.Item label="课件名称" name="name">
        <Input style={{ width: 243 }} allowClear />
      </Form.Item>
      <Space>
        <Button type="primary" onClick={onHandleSearch} loading={loading}>
          搜索
        </Button>
        <Button
          disabled={loading}
          onClick={() => {
            form.resetFields();
            onHandleRest();
          }}
        >
          重置
        </Button>
      </Space>
    </Form>
  );
}
