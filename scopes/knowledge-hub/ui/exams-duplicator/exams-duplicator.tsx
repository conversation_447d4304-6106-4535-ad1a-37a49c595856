import React from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import { BackendExam, Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { createExamAction, getExamAction } from '@manyun/knowledge-hub.state.exams';

export type ExamsDuplicatorProps = {
  exam: BackendExam;
};

export function ExamsDuplicator({ exam }: ExamsDuplicatorProps) {
  const dispatch = useDispatch();

  const onHandleDuplicator = () => {
    dispatch(
      getExamAction({
        examId: exam.id,
        callback: res => {
          if (res != null) {
            const exam: Omit<Exam, 'id'> = res;
            dispatch(
              createExamAction({
                exam,
                callback: result => {
                  if (result) {
                    message.success('复制成功！');
                  }
                },
              })
            );
          }
        },
      })
    );
  };

  return (
    <Button style={{ padding: 0 }} type="link" onClick={onHandleDuplicator}>
      复制
    </Button>
  );
}
