import cloneDeep from 'lodash.clonedeep';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { RestrictedDataNode } from '@manyun/base-ui.ui.editable-tree';
import { EditableTree } from '@manyun/base-ui.ui.editable-tree';
import { message } from '@manyun/base-ui.ui.message';
import type { RefSelectProps } from '@manyun/base-ui.ui.select';
import { Spin } from '@manyun/base-ui.ui.spin';
import type { DataNode } from '@manyun/base-ui.ui.tree';
import { Tree } from '@manyun/base-ui.ui.tree';
import type { TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import { useCoursewares } from '@manyun/knowledge-hub.hook.use-coursewares';
import type { CategoryTreeNode } from '@manyun/knowledge-hub.hook.use-coursewares-category';
import { useCoursewaresCategory } from '@manyun/knowledge-hub.hook.use-coursewares-category';
import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import {
  Variant,
  coursewaresSliceActions,
  selectCoursewareCategory,
  selectCoursewares,
} from '@manyun/knowledge-hub.state.coursewares';

function getTreeNode(): RestrictedDataNode {
  return {
    key: Math.round(Math.random() * 10).toString() + 'new',
    title: '',
    count: 0,
    parentId: 0,
    level: 1,
    isLeaf: false,
    isDraft: true,
  };
}

function walk<T extends { children?: T[] }>(treeData: T[], visitor: (node: T) => void) {
  treeData.forEach(node => {
    visitor(node);

    if (node.children) {
      walk(node.children, visitor);
    }
  });
}

export const EditTreeCoursewaresCategroy = () => {
  const [
    { loading, tree },
    {
      readCoursewareCategory,
      createCoursewareCategory,
      deleteCoursewareCategory,
      updateCoursewareCategory,
    },
  ] = useCoursewaresCategory({ variant: Variant.ALL, withTree: true, withCount: true });
  const [data, setData] = useState<CategoryTreeNode[]>([]);
  const [getCoursewares] = useCoursewares({ variant: Variant.ALL });
  const [setFields, { categoryCode }] = useCoursewaresFields(Variant.ALL);
  const { reRender } = useSelector(selectCoursewares(Variant.ALL));
  const { expandedKeys } = useSelector(selectCoursewareCategory(Variant.ALL));
  const dispatch = useDispatch();

  useEffect(() => {
    readCoursewareCategory({ policy: ids => 'network-only' });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [reRender]);

  useEffect(() => {
    setData(tree);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading]);

  const getCurrentTree = (key: string) => {
    let target: CategoryTreeNode | undefined;
    walk(data, _node => {
      if (_node.key === key) {
        target = _node;
        return target;
      }
      return;
    });
    return target;
  };

  const onSaveLocal = useCallback(node => {
    setData(prev => {
      const clone = cloneDeep(prev);
      walk(clone, _node => {
        if (_node.key === node.key) {
          _node.title = node.title;
          _node.isDraft = false;
        }
      });

      return clone;
    });
  }, []);

  const onAddLocal = useCallback((node, newNode) => {
    setData(prev => {
      const clone = cloneDeep(prev);
      let target: CategoryTreeNode | undefined;
      walk(clone, _node => {
        if (_node.key === node.key) {
          target = _node;
        }
      });
      if (!target) {
        return prev;
      }
      if (!target.children) {
        target.children = [newNode];
      } else {
        target.children.push(newNode);
      }

      return clone;
    });
  }, []);

  const onDeleteLocal = useCallback(node => {
    setData(prev => {
      const clone = cloneDeep(prev);
      const onRootLevelIdx = clone.findIndex(root => root.key === node.key);
      if (onRootLevelIdx > -1) {
        clone.splice(onRootLevelIdx, 1);
      } else {
        walk(clone, _node => {
          const targetIdx = _node.children?.findIndex(child => child.key === node.key);
          if (typeof targetIdx == 'number' && targetIdx > -1) {
            _node.children!.splice(targetIdx!, 1);
          }
        });
      }

      return clone;
    });
  }, []);

  const onUpdateLocal = useCallback((node, newNode) => {
    setData(prev => {
      const clone = cloneDeep(prev);
      walk(clone, _node => {
        if (_node.key === node.key) {
          _node.key = newNode.key;
        }
      });
      return clone;
    });
  }, []);

  const deleteTreeNodeHandler = useCallback((node: RestrictedDataNode) => {
    if (node.key.indexOf('new') === -1) {
      deleteCoursewareCategory(Number(node.key), res => {
        if (res != null) {
          message.success('删除成功！');
          handleRender();
        }
      });
    }
    onDeleteLocal(node);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const handleRender = useCallback(() => {
    setFields({ categoryCode: 2, name: undefined });
    dispatch(coursewaresSliceActions.toggleReRender());
    getCoursewares();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (data.length === 0) {
    return <Spin spinning={loading} delay={200} />;
  }

  return (
    <Spin spinning={loading}>
      <EditableTree
        expandedKeys={expandedKeys}
        selectedKeys={[categoryCode?.toString() || data[0]?.id!.toString()]}
        treeData={data as RestrictedDataNode[]}
        editText=""
        cancelText=""
        addChildTreeNodeText=""
        saveText=""
        deleteText=""
        allowDelete={node => {
          return node.parentId !== 0;
        }}
        allowEdit={node => {
          return node.key !== '2';
        }}
        allowAddChild={node => node.level !== 3}
        maxTitleLength={10}
        titleRender={node => {
          return `${node.title} (${node.count})`;
        }}
        onExpand={keys => {
          dispatch(
            coursewaresSliceActions.setExpandedKeys({ expandedKeys: keys, variant: Variant.ALL })
          );
        }}
        onCancel={node => {
          if (!node.isDraft) {
            return;
          }
          deleteTreeNodeHandler(node);
        }}
        onSave={node =>
          new Promise<boolean>(resolve => {
            if (node.key.indexOf('new') > -1) {
              const parentId = getCurrentTree(node.key)?.parentId || 1;
              createCoursewareCategory(
                {
                  name: node.title,
                  parentId,
                },
                res => {
                  if (res != null) {
                    message.success('保存成功！');
                    onUpdateLocal(node, { key: res.id });
                    onSaveLocal(node);
                    handleRender();
                  }
                  resolve(res != null);
                }
              );
            } else {
              updateCoursewareCategory(
                {
                  id: Number(node.key),
                  name: node.title,
                },
                res => {
                  if (res != null) {
                    message.success('更新成功！');
                    handleRender();
                  }
                  resolve(res != null);
                }
              );
            }
          })
        }
        onAddTreeNode={node => {
          const newNode = getTreeNode();
          if (node !== null) {
            newNode.parentId = Number(node.key);
            newNode.level = node.level + 1;
            newNode.isLeaf = node.level === 2;
            onAddLocal(node, newNode);
            dispatch(
              coursewaresSliceActions.setExpandedKeys({
                expandedKeys: [...expandedKeys, node.key],
                variant: Variant.ALL,
              })
            );
          }
          return newNode;
        }}
        onDeleteConfirm={node =>
          new Promise<boolean>(resolve => {
            deleteTreeNodeHandler(node);
            resolve(true);
          })
        }
        onSelect={(keys, info) => {
          if (Array.isArray(keys) && keys[0] !== undefined) {
            if (keys[0].indexOf('new') > -1) {
              return;
            }
            setFields({ categoryCode: Number(keys[0]) || 2 });
          }
        }}
      />
    </Spin>
  );
};

export type SelectTreeCoursewareCategoryrProps = Omit<TreeSelectProps<unknown>, 'treeData'> & {
  variant?: Variant;
};

export const SelectTreeCoursewareCategory = React.forwardRef(
  (
    { variant = Variant.ALL, ...props }: SelectTreeCoursewareCategoryrProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ loading, tree }, { readCoursewareCategory }] = useCoursewaresCategory({
      variant,
      withTree: true,
      isAuth: variant === Variant.Auth,
    });

    useEffect(() => {
      readCoursewareCategory({
        policy: ids => (ids.length > 0 ? 'cache-only' : 'network-only'),
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return <TreeSelect ref={ref} loading={loading} treeData={tree as []} {...props} />;
  }
);

SelectTreeCoursewareCategory.displayName = 'SelectTreeCoursewareCategory';

export const TreeCoursewareCategory = React.forwardRef(() => {
  const [getCoursewares] = useCoursewares({ variant: Variant.Auth });
  const [setFields, { categoryCode }] = useCoursewaresFields(Variant.Auth);
  const [{ loading, tree }, { readCoursewareCategory }] = useCoursewaresCategory({
    variant: Variant.Auth,
    withTree: true,
    isAuth: true,
  });
  const { expandedKeys } = useSelector(selectCoursewareCategory(Variant.Auth));
  const dispatch = useDispatch();

  useEffect(() => {
    readCoursewareCategory({
      policy: ids => (ids.length > 0 ? 'cache-only' : 'network-only'),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (Array.isArray(tree) && tree.length === 0) {
    return <Spin spinning={loading} delay={200} />;
  }

  return (
    <Spin spinning={loading} delay={200}>
      <Tree
        selectedKeys={[String(categoryCode || tree[0].id)]}
        expandedKeys={expandedKeys}
        fieldNames={{ title: 'title', key: 'key', children: 'children' }}
        treeData={tree as DataNode[]}
        titleRender={({ title }: DataNode) => <span>{title}</span>}
        onExpand={keys => {
          dispatch(
            coursewaresSliceActions.setExpandedKeys({
              expandedKeys: keys as string[],
              variant: Variant.Auth,
            })
          );
        }}
        onSelect={(keys, info) => {
          if (Array.isArray(keys) && keys[0] !== undefined) {
            setFields({ categoryCode: Number(keys[0]) || 2 });
            getCoursewares();
          }
        }}
      />
    </Spin>
  );
});

TreeCoursewareCategory.displayName = 'TreeCoursewareCategory';

export type CoursewareCategoryProps = {
  variant: Variant;
  style?: React.CSSProperties;
  categoryCode: number;
};

export function CoursewareCategory({ categoryCode, style, variant }: CoursewareCategoryProps) {
  const [{ loading, entities }, { readCoursewareCategory }] = useCoursewaresCategory({
    variant,
  });

  useEffect(() => {
    readCoursewareCategory({
      policy: ids => ([...ids, 0].includes(categoryCode) ? 'cache-only' : 'network-only'),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryCode]);

  const loopCategoryName = useCallback(() => {
    return getCategorys(entities, categoryCode).map(category => category.name);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading, categoryCode, entities]);

  if (loading) {
    return <Spin style={style} spinning={loading} size="small" />;
  }

  const categoryNames = loopCategoryName().join('/');

  return <span title={categoryNames}>{categoryNames}</span>;
}
export function getCategorys(entities: Record<number, Category>, categoryCode: number) {
  const list: { code: number | string; name: string }[] = [];
  var loop = (code: number) => {
    const entity = entities[code];
    if (!entity) {
      list.push({ code: '', name: code ? '未知' : '--' });
      return;
    }
    list.push({ code, name: entity.name });
    if (entity.parentId === 0) {
      return;
    }
    loop(entity.parentId);
  };
  loop(categoryCode);
  return list.reverse();
}
