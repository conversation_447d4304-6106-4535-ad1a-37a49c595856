import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';

import {
  CoursewareCategory,
  EditTreeCoursewaresCategroy,
  SelectTreeCoursewareCategory,
} from './coursewares-category';

export const EditTreeCoursewaresCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <EditTreeCoursewaresCategroy />
    </FakeStore>
  </ConfigProvider>
);

export const SelectTreeCoursewaresCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <SelectTreeCoursewareCategory onChange={val => {}} />
    </FakeStore>
  </ConfigProvider>
);

export const CascaderTextCoursewaresCategory = () => (
  <FakeStore>
    <FakeStore>
      <CoursewareCategory categoryCode={1} variant={Variant.ALL} />
    </FakeStore>
  </FakeStore>
);
