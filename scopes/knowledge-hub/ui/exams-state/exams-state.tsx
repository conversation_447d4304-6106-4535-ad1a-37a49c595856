import React from 'react';

import type { SelectValue } from 'antd/es/select';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Tag } from '@manyun/base-ui.ui.tag';

import {
  BAKEND_EXAM_STATUS_MAPPER,
  BackendExamStatus,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

const EXAMS_STATE_COLOR_MAPPER: Record<BackendExamStatus, string> = {
  [BackendExamStatus.INIT]: 'var(--manyun-success-color)',
  [BackendExamStatus.PROCESS]: 'var(--manyun-error-color)',
  [BackendExamStatus.CORRECT]: 'var(--manyun-warning-color)',
  [BackendExamStatus.GRADED]: 'var(--text-color-secondary)',
  [BackendExamStatus.CANCEL]: 'var(--text-color-secondary)',
};

const EXAMS_STATE_TAG_COLOR_MAPPER: Record<BackendExamStatus, string> = {
  [BackendExamStatus.INIT]: 'warning',
  [BackendExamStatus.PROCESS]: 'processing',
  [BackendExamStatus.CORRECT]: 'processing',
  [BackendExamStatus.GRADED]: 'success',
  [BackendExamStatus.CANCEL]: 'default',
};

export type ExamsStateProps = {
  variant: 'select' | 'text' | 'tag';
  examState?: BackendExamStatus;
  filterKeys?: string[];
} & SelectProps<SelectValue>;

export function ExamsState({ variant, examState, filterKeys, ...props }: ExamsStateProps) {
  const options =
    variant !== 'text'
      ? Object.keys(BAKEND_EXAM_STATUS_MAPPER)
          .filter(key => !filterKeys || !filterKeys.includes(key))
          .reduce(
            (options, key) => {
              options.push({
                label: (BAKEND_EXAM_STATUS_MAPPER as any)[key],
                value: key,
              });
              return options;
            },
            [] as { label: string; value: string }[]
          )
      : BAKEND_EXAM_STATUS_MAPPER;

  if (variant === 'text') {
    return (
      <span style={{ ...props.style, color: EXAMS_STATE_COLOR_MAPPER[examState!] }}>
        {(options as typeof BAKEND_EXAM_STATUS_MAPPER)[examState!]}
      </span>
    );
  }

  if (variant === 'tag' && examState) {
    return (
      <Tag color={EXAMS_STATE_TAG_COLOR_MAPPER[examState]}>
        {BAKEND_EXAM_STATUS_MAPPER[examState]}
      </Tag>
    );
  }

  const selectProps = { ...props, options: options as any };

  return <Select style={{ width: 100 }} {...selectProps} />;
}
