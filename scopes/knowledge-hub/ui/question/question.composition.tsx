import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { Question } from './question';

export const QuestionMarkingOrFinished = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：()',
        type: 1,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['手动切换换换换'],
        categoryCode: '01',
        difficulty: 1,
        options: [
          '关闭各IT包间一半空调进行电力将在仔',
          '手动切换换换换',
          '手动切换联络柜至B路市电',
          '手动切换联络柜至B路市电345',
        ],
        userAnswers: ['手动切换换换换'],
      }}
      mode="marking"
      index={1}
      onChange={val => {
        console.log(val);
      }}
    />
  </ConfigProvider>
);
export const QuestionTrueMarkingOrFinished = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：()',
        type: 3,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['true'],
        categoryCode: '01',
        difficulty: 1,
        options: [],
        userAnswers: ['false'],
      }}
      mode="marking"
      index={1}
      onChange={val => {
        console.log(val);
      }}
    />
  </ConfigProvider>
);

export const QuestionPreview = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：()',
        type: 1,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['手动切换换换换'],
        categoryCode: '01',
        difficulty: 1,
        options: [
          '关闭各IT包间一半空调进行电力将在仔',
          '手动切换换换换',
          '手动切换联络柜至B路市电',
          '手动切换联络柜至B路市电345',
        ],
        userAnswers: null,
      }}
      mode="preview"
      index={1}
      onChange={val => {
        console.log(val);
      }}
    />
  </ConfigProvider>
);

export const QuestionAnsewer = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：()()()</p>',
        type: 4,
        autoGrade: false,
        analysis: '你说的都对',
        answers: ['认为&&是的', '觉得', '是的'],
        userAnswers: ['是的', '不是'],
        categoryCode: '01',
        difficulty: 1,
        options: [],
      }}
      mode="answer"
      index={1}
      onChange={val => {
        console.log(val);
      }}
    />
  </ConfigProvider>
);

export const SingleQuestionAnsewer = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：()',
        type: 2,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['关闭各IT包间一半空调进行电力将在仔'],
        categoryCode: '01',
        difficulty: 1,
        options: [
          '关闭各IT包间一半空调进行电力将在仔',
          '手动切换换换换',
          '手动切换联络柜至B路市电',
          '手动切换联络柜至B路市电345',
        ],
        userAnswers: ['手动切换换换换'],
      }}
      mode="answer"
      index={1}
      onChange={val => {
        console.log(val);
      }}
    />
  </ConfigProvider>
);

export const MultipleQuestionAnswer = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：',
        type: 2,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['关闭各IT包间一半空调进行电力将在仔'],
        categoryCode: '01',
        difficulty: 1,
        options: [
          '关闭各IT包间一半空调进行电力将在仔',
          '手动切换换换换',
          '手动切换联络柜至B路市电',
          '手动切换联络柜至B路市电345',
        ],
        userAnswers: ['手动切换换换换', '手动切换联络柜至B路市电'],
      }}
      mode="answer"
      onChange={val => {
        console.log(val);
      }}
      index={1}
    />
  </ConfigProvider>
);

export const TrueOrFalseQuestionAnswer = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，是否正确：',
        type: 3,
        autoGrade: false,
        analysis: '正确',
        answers: ['true'],
        categoryCode: '01',
        difficulty: 1,
        options: [],
        userAnswers: ['true'],
      }}
      mode="answer"
      onChange={val => {
        console.log(val);
      }}
      index={1}
    />
  </ConfigProvider>
);

export const EssayQuestionAnswer = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：',
        type: 5,
        autoGrade: false,
        analysis: '',
        answers: [],
        categoryCode: '01',
        difficulty: 1,
        options: [],
        userAnswers: [],
      }}
      mode="answer"
      onChange={val => {
        console.log(val);
      }}
      index={1}
    />
  </ConfigProvider>
);

export const BasicQuestion2 = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：',
        type: 2,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['手动切换换换换', '手动切换联络柜至B路市电'],
        categoryCode: '01',
        difficulty: 1,
        options: [
          '关闭各IT包间一半空调进行电力将在仔',
          '手动切换换换换',
          '手动切换联络柜至B路市电',
          '手动切换联络柜至B路市电',
        ],
        userAnswers: null,
      }}
      mode="preview"
      index={1}
    />
  </ConfigProvider>
);
export const BasicQuestion3 = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：',
        type: 3,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['正确'],
        categoryCode: '01',
        difficulty: 1,
        options: [],
      }}
      mode="preview"
      index={1}
    />
  </ConfigProvider>
);
export const BasicQuestion4 = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：',
        type: 4,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['希望', '觉得', '认为'],
        categoryCode: '01',
        difficulty: 1,
        options: ['希望', '觉得', '认为'],
      }}
      mode="preview"
      index={1}
    />
  </ConfigProvider>
);

export const BasicQuestion5 = () => (
  <ConfigProvider>
    <Question
      question={{
        id: 1,
        title: '<p>双路市电中断情况下，下列哪种行为正确：',
        type: 5,
        autoGrade: false,
        analysis: '<p>你说的都对</p>',
        answers: ['A'],
        categoryCode: '01',
        difficulty: 1,
        options: [],
      }}
      mode="preview"
      index={1}
    />
  </ConfigProvider>
);
