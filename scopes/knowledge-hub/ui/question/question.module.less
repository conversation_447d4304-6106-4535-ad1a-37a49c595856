.questionContainer {
  display: flex;
  width: 100%;
  .content {
    flex: 1;
    display: flex;
    flex-direction: column;
    .title {
      display: flex;
      line-height: 22px;
      position: relative;
      padding-right: 10px;
      word-wrap: break-word;
      word-break: normal;
      margin: 5px 0;
      align-items: flex-start;
      .index {
        font-size: 14px;
        font-weight: 400;
        display: inline-block;
        max-height: 168px;
        line-height: 22px;
        text-align: right;
      }
      .innerHtml > p {
        margin: 0;
      }
    }
    .optionPrefix {
      color: var(--manyun-primary-color);
    }

    .analysis {
      display: flex;
      line-height: 22px;
      position: relative;
      word-wrap: break-word;
      word-break: normal;
      .index {
        font-size: 16px;
        font-weight: 400;
        color: 'var(--manyun-primary-color)';
        position: absolute;
        left: -25px;
        top: 0;
        display: inline-block;
        width: 41px;
        line-height: 22px;
        text-align: right;
      }
      .innerHtml > p {
        margin: 0;
      }
    }
  }
  .affixWrapper {
    padding-left: 6px;
  }
  div[data-answer-state='success'] {
    > *:global(.manyun-input-affix-wrapper-disabled) {
      color: var(--manyun-success-color-active) !important;
    }
    .affix {
      color: var(--manyun-success-color-active) !important;
    }
  }
  div[data-answer-state='error'] {
    > *:global(.manyun-input-affix-wrapper-disabled) {
      color: var(--manyun-error-color) !important;
    }
    .affix {
      color: var(--manyun-error-color) !important;
    }
  }
  .operate {
    min-width: 100px;
  }
}
