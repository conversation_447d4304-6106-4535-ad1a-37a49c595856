import React, { CSSProperties, ReactNode, useEffect, useState } from 'react';

import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import debounce from 'lodash.debounce';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { McUpload } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import type { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { BackendQuestionType } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import type { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { TagQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { TextQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';

import styles from './question.module.less';

export type QuestionMode = 'preview' | 'create' | 'answer' | 'marking' | 'finished';

export const SYNONYM_SPLIT = '&&';

export type QuestionProps = {
  /** container div id */
  id?: string;
  question: BackendQuestion | BackendSectionQuestion;
  mode: QuestionMode;
  index?: number;
  style?: CSSProperties | undefined;
  examPaperId?: string | number /**考卷id, 用于附件上传生成targetId */;
  onChange?: (value: any) => void;
  children?: ReactNode | null;
};

const colorsMapper: Record<string, string> = {
  error: 'var(--manyun-error-color)',
  success: 'var(--manyun-success-color-active)',
};

/** 延迟执行 600ms */
const delayTime = 600;

export function Question({
  id,
  question,
  mode,
  index,
  onChange,
  examPaperId,
  ...props
}: QuestionProps) {
  const [userAnswers, setUserAnswers] = useState<string[]>([]);

  const [fileList, setFileList] = useState<MixedUploadFile[]>([]);

  useEffect(() => {
    /**预览 答题 判卷 已完成 时的 填空题初始化 */
    if (!['answer', 'marking', 'finished', 'create'].includes(mode)) {
      return;
    }
    if (question.type === BackendQuestionType.SHORT_ANSWER) {
      const sectionQuestion = question as BackendSectionQuestion;
      let answers = new Array(question.answers.length).fill('');
      if (sectionQuestion.userAnswers !== null) {
        answers = answers.map((_, key) =>
          sectionQuestion.userAnswers && sectionQuestion.userAnswers[key]
            ? sectionQuestion.userAnswers[key]
            : ''
        );
      }
      setUserAnswers(answers);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onChangeVal = debounce((val: any) => {
    if (onChange) {
      onChange(val);
    }
  }, delayTime);

  const onChangeTarget = (e: any, index?: number) => {
    const value = e.target.value;
    if (index !== undefined) {
      userAnswers[index] = value;
      onChangeVal(userAnswers);
    } else {
      onChangeVal(value);
    }
  };

  /** 是否禁用选项 */
  const disabledOption = mode !== 'answer';

  /**单选题 选项默认值 */
  const defaltSigleAnswer =
    mode === 'create'
      ? question.answers && question.answers.length > 0 && question.answers[0]
      : ((question as BackendSectionQuestion).userAnswers &&
          ((question as BackendSectionQuestion).userAnswers as string[]).length > 0 &&
          ((question as BackendSectionQuestion).userAnswers as string[])[0]) ||
        undefined;

  /**多选题 选项默认值 */
  const defaltMultipleAnswer =
    mode === 'create'
      ? question.answers && question.answers
      : ((question as BackendSectionQuestion).userAnswers &&
          ((question as BackendSectionQuestion).userAnswers as string[])) ||
        undefined;

  /**判卷、已完成 设置 多选题、单选题选项颜色值 */
  const getOptionColor = (option: string) => {
    const style: CSSProperties = { color: undefined };

    if (['create'].includes(mode)) {
      const isCorrectOption = (question.answers || []).includes(option);
      if (isCorrectOption) {
        style.color = colorsMapper['success'];
      }
      return style;
    }

    if (!['marking', 'finished'].includes(mode)) {
      return style;
    }

    const isCorrectOption = (question.answers || []).includes(option);
    const isUserOption = (question as BackendSectionQuestion).userAnswers?.includes(option);
    if (isCorrectOption) {
      style.color = colorsMapper['success'];
    } else {
      if (isUserOption) {
        style.color = colorsMapper['error'];
      }
    }
    return style;
  };

  /**判卷、已完成设置填空题的答案颜色值 */
  const getAnswerClassName = (userAnswer: string, index: number) => {
    let className: string = '';
    if (!['marking', 'finished'].includes(mode)) {
      return className;
    }
    const rightAnswers = question.answers[index].split(SYNONYM_SPLIT);
    if (rightAnswers.includes(userAnswer)) {
      className = 'success';
    } else {
      className = 'error';
    }
    return className;
  };

  /** 判卷、已完成设置 判断题的答案颜色值*/
  const getTrueOrFalseTypographType = (value: string) => {
    let type: undefined | 'success' | 'danger' = undefined;
    if (['marking', 'finished'].includes(mode)) {
      const rightAnswer =
        question.answers.length > 0 && question.answers[0] ? question.answers[0] : '';
      const userAnswer =
        (question as BackendSectionQuestion).userAnswers &&
        ((question as BackendSectionQuestion).userAnswers as string[])
          ? ((question as BackendSectionQuestion).userAnswers as string[])[0]
          : '';

      if (userAnswer === value) {
        type = rightAnswer === userAnswer ? 'success' : 'danger';
      }
    }
    return type;
  };

  /**获取正确答案的 索引*/
  const getAnserwes = () => {
    /** 单选题、多选题、判断题 需要特殊处理 */
    if ([1, 2].includes(question.type)) {
      const indexsArray: string[] = [];
      (question.answers || []).forEach(answer => {
        const index = question.options.findIndex(option => answer === option);
        if (index > -1) {
          const letter = filterLetters(index);
          if (letter) {
            indexsArray.push(letter);
          }
        }
      });
      return indexsArray.sort().join('、');
    } else if (question.type === 3) {
      return question.answers && question.answers[0] === 'true' ? '正确' : '错误';
    } else {
      return question.answers ? question.answers.join(' | ') : '';
    }
  };

  return (
    <div id={id} className={styles.questionContainer} {...props}>
      {/* 题目内容 */}
      <div className={styles.content}>
        <div className={styles.title}>
          <span className={styles.index}>{index}.</span>
          <TagQuestionType typeVal={question.type} />
          {question.title && (
            <div
              className={styles.innerHtml}
              dangerouslySetInnerHTML={{ __html: question.title ? question.title : '' }}
            />
          )}
        </div>
        {/*start options */}
        {/* 单选题 */}
        {question.type === BackendQuestionType.SINGLE_CHOICE && (
          <Radio.Group
            defaultValue={defaltSigleAnswer}
            disabled={disabledOption}
            onChange={onChangeTarget}
          >
            <Space direction="vertical">
              {(question.options || []).map((option, index) => {
                return (
                  <Radio key={index} value={option}>
                    <div style={{ ...getOptionColor(option) }}>
                      <span className={styles.optionPrefix}>{filterLetters(index)}. </span>
                      <span>{option}</span>
                    </div>
                  </Radio>
                );
              })}
            </Space>
          </Radio.Group>
        )}

        {/* 多择题 */}
        {question.type === BackendQuestionType.MULTIPLE_CHOICE && (
          <Checkbox.Group
            defaultValue={defaltMultipleAnswer}
            disabled={disabledOption}
            onChange={onChangeVal}
          >
            <Space direction="vertical">
              {(question.options || []).map((option, index) => {
                return (
                  <Checkbox key={index} value={option}>
                    <span className={styles.optionPrefix}>{filterLetters(index)}.</span>
                    <span style={{ ...getOptionColor(option) }}>{option}</span>
                  </Checkbox>
                );
              })}
            </Space>
          </Checkbox.Group>
        )}

        {/* 判断题 */}
        {question.type === BackendQuestionType.TRUE_OR_FALSE && (
          <Radio.Group
            defaultValue={defaltSigleAnswer}
            disabled={disabledOption}
            onChange={onChangeTarget}
          >
            <Radio value={'true'}>
              <Typography.Text type={getTrueOrFalseTypographType('true')}>正确</Typography.Text>
            </Radio>
            <Radio value={'false'}>
              <Typography.Text type={getTrueOrFalseTypographType('false')}>错误</Typography.Text>
            </Radio>
          </Radio.Group>
        )}

        {/*填空题  - 输入框 */}
        {question.type === BackendQuestionType.SHORT_ANSWER &&
          ['answer', 'preview'].includes(mode) && (
            <>
              {userAnswers.map((userAnswer, index) => (
                <div
                  key={question.id + index}
                  data-answer-state={getAnswerClassName(userAnswer, index)}
                >
                  <Input
                    prefix={`${index + 1}.`}
                    disabled={mode !== 'answer'}
                    defaultValue={userAnswer}
                    style={{
                      width: 280,
                      marginLeft: 6,
                      marginBottom: 8,
                    }}
                    onChange={e => {
                      onChangeTarget(e, index);
                    }}
                    allowClear
                  />
                </div>
              ))}
            </>
          )}

        {question.type === BackendQuestionType.SHORT_ANSWER &&
          ['marking', 'finished'].includes(mode) && (
            <>
              {userAnswers.map((userAnswer, index) => (
                <div
                  key={userAnswer + index}
                  data-answer-state={getAnswerClassName(userAnswer, index)}
                >
                  <div className={styles.affixWrapper}>
                    答：
                    <span className={styles.affix}>{userAnswer}</span>
                  </div>
                  <div
                    style={{
                      marginLeft: ' 8px',
                      fontSize: '14px',
                      marginBottom: '5px',
                      marginTop: '3px',
                    }}
                  >
                    <span style={{ color: 'var(--manyun-success-color)' }}>正确答案：</span>
                    <span>{question.answers[index].split(SYNONYM_SPLIT).join(' 或 ')}</span>
                  </div>
                </div>
              ))}
            </>
          )}

        {/* 问答题 - 答卷、判卷、已完成才显示 */}
        {question.type === BackendQuestionType.ESSAY &&
          ['answer', 'marking', 'finished'].includes(mode) && (
            <div style={{ width: '360px' }}>
              <Input.TextArea
                disabled={mode !== 'answer'}
                onChange={onChangeTarget}
                defaultValue={
                  (question as BackendSectionQuestion).userAnswers != null
                    ? ((question as BackendSectionQuestion).userAnswers as string[])[0]
                    : ''
                }
                autoSize={{ minRows: 5, maxRows: 10 }}
                style={{ width: 300, marginBottom: mode === 'answer' ? '8px' : 0 }}
              />
              <McUpload
                targetId={`${examPaperId}_${question.id}`} /**生成唯一的考卷id */
                targetType="EXAM_QUESTION"
                fileList={fileList}
                onChange={info => {
                  setFileList(info.fileList.filter(file => file.status && file.status !== 'error'));
                }}
                accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.xls,.xlsx"
                showAccept={mode === 'answer'}
                allowDelete={mode === 'answer'}
              >
                {mode === 'answer' && <Button icon={<UploadOutlined />}>上传</Button>}
              </McUpload>
            </div>
          )}
        {/* end options */}

        {/* 答案、解析、难度 */}
        {mode !== 'answer' && (
          <Space direction="vertical" style={{ marginLeft: '5px', marginTop: '8px' }} size={2}>
            {/* 答案 */}
            {(![BackendQuestionType.SHORT_ANSWER].includes(question.type) ||
              (BackendQuestionType.SHORT_ANSWER === question.type && mode === 'create')) &&
              question.answers &&
              question.answers.length > 0 && (
                <Space align="start" style={{ width: '100%' }}>
                  <div
                    style={{
                      color: 'var(--manyun-success-color)',
                      width: '70px',
                      fontSize: '14px',
                    }}
                  >
                    正确答案：
                  </div>
                  {getAnserwes()}
                </Space>
              )}

            {/* 解析 */}
            {question.analysis && question.analysis !== '' && (
              <Space align="start">
                <div style={{ width: '45px' }}>解析：</div>
                {question.analysis}
              </Space>
            )}

            {/* 难度 */}
            <Space>
              难度：
              <TextQuestionsDifficulty difficultyVal={question.difficulty} />
            </Space>
          </Space>
        )}

        {/* 预览时 错误提示 */}
        {mode === 'create' && ((question as any).errors as string[]) && (
          <div
            style={{
              marginLeft: '5px',
              marginTop: '5px',
              color: 'var(--manyun-error-color-active)',
            }}
          >
            此处有
            <span>{((question as any).errors as string[]).length}</span>
            个错误输入提示：
            {((question as any).errors as string[]).join('\t')}
          </div>
        )}
      </div>
      {/* 左侧操作 */}
      {props.children}
    </div>
  );
}

export function filterLetters(index: number) {
  if (index >= 0 && index < 26) {
    return String.fromCharCode(65 + index);
  } else {
    return undefined;
  }
}
