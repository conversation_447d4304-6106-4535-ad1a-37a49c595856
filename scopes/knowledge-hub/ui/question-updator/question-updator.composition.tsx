import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { QuestionUpdator } from './question-updator';

export const BasicQustionUpdator = () => (
  <ConfigProvider>
    <FakeStore>
      <QuestionUpdator
        question={{
          id: 1, //试题id
          type: 1,
          categoryCode: '1001', //试题分类
          difficulty: 1, //难度
          title: '测试试题', //题目标题
          options: [
            //选项
            'A:测试选项1',
            'B:测试选项2',
            'C:测试选项3',
            'D:测试选项4',
          ],
          answers: [
            //答案
            'A',
            'B',
            'C',
          ],
          analysis: '这个题目明显选择 A B C', //分析
          autoGrade: true, //是否自动判分
        }}
      />
    </FakeStore>
  </ConfigProvider>
);
