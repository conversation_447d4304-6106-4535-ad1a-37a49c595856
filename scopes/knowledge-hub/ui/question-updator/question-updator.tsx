import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useDispatch } from 'react-redux';

import { FormInstance } from 'antd/es/form';
import Skeleton from 'antd/es/skeleton';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  BackendQuestion,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { getQuestionAction, updateQuestionsAction } from '@manyun/knowledge-hub.state.questions';
import { QuestionEditor } from '@manyun/knowledge-hub.ui.question-editor';
import { shortAnswerTitle as shortAnswerTitleReg } from '@manyun/knowledge-hub.ui.questions-editor';

export type QuestionUpdatorProps = {
  /**
   * 试题内容.
   */
  question: BackendQuestion;
  onOk?: () => void;
};

export function QuestionUpdator({ question, onOk }: QuestionUpdatorProps) {
  const dispatch = useDispatch();
  let editRef = useRef<FormInstance<any> & { initEditor: () => void }>();
  const [fetchLoading, setFetchLoading] = useState(true);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [questionDetail, setQuestionDetail] = useState<BackendQuestion>();

  const onClickUpdate = () => {
    setIsModalVisible(true);
    getQuestionDetail();
  };

  const getQuestionDetail = useCallback(() => {
    setFetchLoading(true);
    dispatch(
      getQuestionAction({
        questionId: question.id,
        callback: res => {
          setFetchLoading(false);
          if (res != null) {
            setQuestionDetail(res);
            setInitEditor(res);
          }
        },
      })
    );
  }, [dispatch, question]);

  /** 获取详情后 设置表单默认值 */
  const setInitEditor = useCallback(
    (questionDetail: BackendQuestion) => {
      if (questionDetail != undefined) {
        if (editRef.current) {
          /** 备注：多选题 number [] 单选题number 判断题 string 填空题为{answer: string }[]  默认 string [] */
          let answers: string | string[] | number[] | number | { answer: string }[] =
            questionDetail.answers;
          let options: string[] | { option: string }[] = questionDetail.options;
          /**单选题 多选题  选项设置*/
          if ([1, 2].includes(questionDetail.type)) {
            options = questionDetail.options.map((option: string) => ({ option: option }));
          }
          /**多选题  */
          if (questionDetail.type == 2) {
            answers = questionDetail.answers.reduce((indexArr, answer) => {
              const index = questionDetail.options.findIndex(option => option == answer);
              if (index > -1) {
                indexArr.push(index);
              }
              return indexArr;
            }, [] as number[]);
          }
          /**单选题  */
          if (questionDetail.type == 1) {
            answers =
              questionDetail.answers.length > 0
                ? questionDetail.options.findIndex(option => option == questionDetail.answers[0])
                : -1;
          }

          /** 判断题 */
          if (questionDetail.type == 3) {
            answers = questionDetail.answers[0];
          }

          /**填空题 */
          if (questionDetail.type == 4) {
            answers = questionDetail.answers.map((answer: string) => ({ answer: answer }));
          }

          editRef.current.setFieldsValue({
            difficulty: questionDetail.difficulty,
            type: questionDetail.type,
            autoGrade: questionDetail.autoGrade == true ? 1 : 0,
            title: questionDetail.title,
            analysis: questionDetail.analysis,
            options: options,
            answers: answers,
          });
          editRef.current.initEditor();
        }
      }
    },
    [editRef]
  );

  const onHandleUpdateQuestions = useCallback(() => {
    if (editRef.current) {
      (editRef.current as any).validateFields().then((values: any) => {
        setLoading(true);
        const { options, answers } = convertQuesionAnswer(values);
        if (values.type == 4) {
          const hit = values.title.match(shortAnswerTitleReg) || [];
          if (hit.length != answers.length) {
            message.error('请检查填空题题干的填空个数与答案个数是否一致！');
            setLoading(false);
            return;
          }
        }
        dispatch(
          updateQuestionsAction({
            question: {
              id: question.id,
              ...values,
              /**只有填空题是可以修改判题方式 简答题为人工判题 其他类型为系统判题  */
              autoGrade:
                values.type == BackendQuestionType.SHORT_ANSWER
                  ? values.autoGrade == 1
                    ? true
                    : false
                  : values.type == BackendQuestionType.ESSAY
                  ? false
                  : true,
              options: options,
              answers: answers,
            },
            callback: res => {
              setLoading(false);
              if (res) {
                setIsModalVisible(false);
                if (onOk) onOk();
              }
            },
          })
        );
      });
    }
  }, [dispatch, editRef]);

  const convertQuesionAnswer = useCallback((values: any) => {
    let options = values.options;
    let answers: string[] = [];
    /**单选题 */
    if (values.type == 1) {
      options = (values.options || []).map((o: { option: string }) => o.option);
      if (options[values.answers]) answers.push(options[values.answers]);
    }
    /**多选题 */
    if (values.type == 2) {
      options = (values.options || []).map((o: { option: string }) => o.option);
      answers = (values.answers || []).map((answer: number) => options[answer]);
    }
    /**判断题 */
    if (values.type == 3) {
      answers = Array.isArray(values.answers) ? values.answers : [values.answers];
      options = [];
    }
    /**填空题 */
    if (values.type == 4) {
      answers = (values.answers || []).map((o: { answer: string }) => o.answer);
      options = [];
    }
    /**问答题 */
    if (values.type == 5) {
      answers = [];
      options = [];
    }
    return { answers, options };
  }, []);

  return (
    <>
      <Button type="link" onClick={onClickUpdate} size="small" compact>
        修改
      </Button>
      <Modal
        destroyOnClose
        title="试题更新"
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={() => onHandleUpdateQuestions()}
        confirmLoading={loading}
        width={940}
        bodyStyle={{ minHeight: 400, maxHeight: 450, overflow: 'hidden', overflowY: 'scroll' }}
        // style={{ top: 10 }}
        okText="提交"
        cancelText="取消"
      >
        {fetchLoading ? (
          <Skeleton active />
        ) : (
          <QuestionEditor
            question={questionDetail}
            ref={editRef as any}
            disabledFields={['type', 'autoGrade']}
          />
        )}
      </Modal>
    </>
  );
}
