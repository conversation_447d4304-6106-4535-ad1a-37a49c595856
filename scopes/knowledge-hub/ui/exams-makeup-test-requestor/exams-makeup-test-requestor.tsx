import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tag } from '@manyun/base-ui.ui.tag';

import { BackendUserInfo } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users';
import { BackendExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  getExamFailedUserAction,
  requestMakeUpTestAction,
} from '@manyun/knowledge-hub.state.exams';

const { RangePicker } = DatePicker;

export type ExamsMakeupTestRequestorProps = {
  exam: BackendExam;
};

export function ExamsMakeupTestRequestor({ exam }: ExamsMakeupTestRequestorProps) {
  const dispatch = useDispatch();

  const [failedUsers, setFailedUsers] = useState([] as BackendUserInfo[]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const onHandleClick = () => {
    setIsModalVisible(true);
    // dispatch(
    //   getExamFailedUserAction({
    //     examId: exam.id,
    //     callback: (res) => {
    //       if (res != null) {
    //         setFailedUsers(res);
    //       }
    //     },
    //   })
    // );
    // mock data
    setFailedUsers([
      { id: 1, userName: 'admin', loginName: 'admin' },
      { id: 2, userName: 'test1', loginName: '<EMAIL>' },
      { id: 3, userName: 'test2', loginName: '<EMAIL>' },
      { id: 4, userName: 'test3', loginName: '<EMAIL>' },
      { id: 11, userName: 'admin', loginName: 'admin' },
      { id: 21, userName: 'test1', loginName: '<EMAIL>' },
      { id: 31, userName: 'test2', loginName: '<EMAIL>' },
      { id: 41, userName: 'test3', loginName: '<EMAIL>' },
      { id: 211, userName: 'test1', loginName: '<EMAIL>' },
      { id: 311, userName: 'test2', loginName: '<EMAIL>' },
      { id: 411, userName: 'test3', loginName: '<EMAIL>' },
    ]);
  };

  const onHandleRequestMakeUpTest = () => {
    // dispatch(requestMakeUpTestAction({ examId: exam.id, timeRange: [], roleIds: [], userIds: [] }));
  };

  const onClosedUser = (deleteUser: BackendUserInfo) => {
    const filterUsers = failedUsers.filter(user => user.id != deleteUser.id);
    setFailedUsers(filterUsers);
  };
  return (
    <>
      <Button type="link" style={{ padding: 0 }} onClick={onHandleClick}>
        发起补考
      </Button>
      <Modal
        title="发起补考"
        visible={isModalVisible}
        onCancel={() => setIsModalVisible(false)}
        onOk={onHandleRequestMakeUpTest}
        confirmLoading={loading}
        okText="确定"
        cancelText="取消"
      >
        <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item>{exam && exam.name}(补考)</Form.Item>
          <Form.Item label="考试时间" rules={[{ required: true, message: '请选择考试时间' }]}>
            <RangePicker
              picker="date"
              placeholder={['开始时间', '结束时间']}
              showTime={{ format: 'HH:mm' }}
              format="YYYY-MM-DD HH:mm"
            />
          </Form.Item>
          <Form.Item label="补考人员">
            <div
              style={{
                width: 350,
                height: 307,
                overflow: 'auto',
                padding: ' 10px',
              }}
            >
              {failedUsers.map(user => (
                <Tag
                  key={user.id}
                  color="#4d6589"
                  closable
                  onClose={() => {
                    onClosedUser(user);
                  }}
                  style={{ margin: '5px' }}
                >
                  {user.userName}
                </Tag>
              ))}
            </div>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
