/* eslint-disable react-hooks/exhaustive-deps */

/* eslint-disable react-hooks/rules-of-hooks */
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { ColumnsType } from 'antd/es/table';
import { ColumnType, TableRowSelection } from 'antd/es/table/interface';
import moment from 'moment';

import { Table } from '@manyun/base-ui.ui.table';

import { User } from '@manyun/auth-hub.ui.user';
import { usePapers } from '@manyun/knowledge-hub.hook.use-papers';
import { usePapersFields } from '@manyun/knowledge-hub.hook.use-papers-fields';
import { BackendPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { papersSliceActions } from '@manyun/knowledge-hub.state.papers';
import { PaperGenFunc } from '@manyun/knowledge-hub.ui.paper-gen-func';
import { PapersCategory } from '@manyun/knowledge-hub.ui.papers-category';
import { PapersMarkingMode } from '@manyun/knowledge-hub.ui.papers-marking-mode';

export type PapersProps = {
  modalVariant?: {
    data: { data: any[]; total: number };
    loading: boolean;
    page: number;
    pageSize: number;
    pageHandler: (page: number, pageSize?: number) => void;
    rowSelection?: TableRowSelection<BackendPaper> | undefined;
  };
  operateColumn?: {
    position?: 'start' | 'end';
    column: ColumnType<any>;
  };
};

export function Papers({ modalVariant, operateColumn }: PapersProps) {
  const dispatch = useDispatch();
  const [setFields] = usePapersFields();
  const [getPapers, { loading, data, fields, selectedIds }] = usePapers();
  const [columns, setColumns] = useState();

  const dataSource = modalVariant ? modalVariant.data.data : data.data;
  const total = modalVariant ? modalVariant.data.total : data.total;
  const page = modalVariant ? modalVariant.page : fields.page;
  const pageSize = modalVariant ? modalVariant.pageSize : fields.pageSize;
  const tableLoading = modalVariant ? modalVariant.loading : loading;

  const setSelectedIds = useCallback(
    (selectedIds: number[]) => {
      dispatch(papersSliceActions.setSelectedIds({ selectedIds }));
    },
    [dispatch]
  );

  const rowSelection: TableRowSelection<BackendPaper> | undefined =
    modalVariant === undefined
      ? {
          selectedRowKeys: selectedIds,
          onChange: setSelectedIds as any,
        }
      : modalVariant.rowSelection;

  const pageHandler =
    modalVariant !== undefined
      ? modalVariant.pageHandler
      : useCallback(
          (_page: number, _pageSize?: number) => {
            if (selectedIds.length > 0) {
              setSelectedIds([]);
            }
            setFields({ page: _page, pageSize: _pageSize });
            getPapers();
          },
          [selectedIds, setSelectedIds, getPapers, setFields]
        );

  useEffect(() => {
    if (modalVariant === undefined) {
      getPapers();
    }
  }, []);

  useEffect(() => {
    const columnsArr = getBaseColumns(modalVariant === undefined ? false : true)?.concat();
    if (operateColumn !== undefined) {
      const { position = 'end', column } = operateColumn;
      if (position === 'start') {
        columnsArr?.unshift(column);
      } else {
        columnsArr?.push(column);
      }
    }
    setColumns(columnsArr as any);
  }, []);

  return (
    <Table
      rowKey="id"
      loading={tableLoading}
      columns={columns}
      dataSource={dataSource}
      scroll={{ x: 'max-content' }}
      pagination={{
        showQuickJumper: true,
        showTotal: () => `共 ${total} 条`,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '50', '100', '200'],
        current: page,
        pageSize,
        total: total!,
        onChange: pageHandler,
      }}
      rowSelection={rowSelection}
    />
  );
}

function getBaseColumns(visible: boolean) {
  const baseColumns: ColumnsType<any> | undefined | any[] = [
    {
      title: '试卷名称',
      dataIndex: 'name',
      visible: true,
      ellipsis: true,
    },
    {
      title: '组卷方式',
      dataIndex: 'type',
      ellipsis: true,
      visible: true,
      width: 100,
      render: text => <PaperGenFunc variant="text" genFuncVal={text} />,
    },
    {
      title: '判卷方式',
      dataIndex: 'autoGrade',
      visible: true,
      ellipsis: true,
      width: 140,
      render: text => <PapersMarkingMode variant="text" markingMode={text ? '1' : '0'} />,
    },
    {
      title: '试卷分类',
      dataIndex: 'categoryCode',
      visible: true,
      ellipsis: true,
      render: text => <PapersCategory categoryCode={text} />,
    },
    {
      title: '试卷总分',
      dataIndex: 'totalGrade',
      visible: true,
      ellipsis: true,
      width: 80,
    },
    {
      title: '创建时间',
      dataIndex: 'gmtModified',
      visible: visible,
      ellipsis: true,
      width: 200,
      render: text => <span>{text > 0 && moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
    },
    {
      title: '更新人',
      dataIndex: 'modifierName',
      visible: !visible,
      render(name, { modifierId }) {
        return <User.Link id={modifierId} name={name} />;
      },
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      visible: !visible,
      ellipsis: true,
      width: 200,
      render(gmtModified) {
        return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
      },
    },
  ];
  return baseColumns.filter(colum => colum.visible === true);
}
