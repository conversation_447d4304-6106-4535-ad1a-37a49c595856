import React from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { PapersDeleter } from '@manyun/knowledge-hub.ui.papers-deleter';
import { PapersMutationPrepare } from '@manyun/knowledge-hub.ui.papers-mutation-prepare';

import { Papers } from './papers';

export const BasicPapers = () => (
  <FakeStore>
    <Papers
      operateColumn={{
        position: 'end',
        column: {
          title: '操作',
          render: (text, record) => {
            return (
              <span>
                <PapersMutationPrepare paper={record} />
                <Divider type="vertical" />
                <PapersDeleter paper={record} />
              </span>
            );
          },
        },
      }}
    />
  </FakeStore>
);

export const ModalPapers = () => (
  <FakeStore>
    <Papers
      modalVariant={{
        data: {
          data: [
            {
              id: 1, //id
              type: 'SELECT', //试卷类型
              categoryCode: '1001', //分类
              name: '测试试卷', //试卷名称
              totalGrade: 100, //总分
              gmtCreate: 1631762517869, //创建时间
              gmtModified: 1631762517869,
              modifierId: 1, //修改人
              modifierName: 'admin', //修改人姓名
            },
            {
              id: 2, //id
              type: 'SELECT', //试卷类型
              categoryCode: '1001', //分类
              name: '测试试卷', //试卷名称
              totalGrade: 100, //总分
              autoGrade: true,
              gmtCreate: 1631762517869, //创建时间
              gmtModified: 1631762517869,
              modifierId: 1, //修改人
              modifierName: 'admin', //修改人姓名
            },
          ],
          total: 1,
        },
        page: 1,
        pageSize: 10,
        pageHandler: () => {},
        loading: false,
        rowSelection: {
          type: 'radio',
          onChange: selectedRow => {
            // console.log(selectedRow);
          },
        },
      }}
    />
  </FakeStore>
);
