import React, { useCallback, useEffect, useState } from 'react';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchCoursewareVersionRecords } from '@manyun/knowledge-hub.service.dcexam.fetch-courseware-version-records';
import type { VersionRecord } from '@manyun/knowledge-hub.service.dcexam.fetch-courseware-version-records';

export type CoursewareVersionUpdateRecordsProps = {
  fileId: number;
};

export function CoursewareVersionUpdateRecords({ fileId }: CoursewareVersionUpdateRecordsProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [data, setData] = useState<VersionRecord[]>([]);

  const _fetchRecords = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchCoursewareVersionRecords({ fileId });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(data.data);
  }, [fileId]);

  useEffect(() => {
    if (visible) {
      _fetchRecords();
    }
  }, [_fetchRecords, visible]);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        版本
      </Button>
      <Modal
        visible={visible}
        title="版本记录"
        onCancel={() => {
          setVisible(false);
        }}
        footer={null}
        width={960}
        bodyStyle={{ maxHeight: '452px', overflowY: 'auto' }}
      >
        <Table
          loading={loading}
          dataSource={data}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '序号',
              dataIndex: 'index',
              render: (_, __, index) => index + 1,
            },
            {
              title: '课件名称',
              dataIndex: 'fileName',
              ellipsis: true,
              render: (value, record) => {
                return (
                  <FilePreviewWithContainer
                    key="preview"
                    file={{
                      src: McUploadFile.generateSrc(record.filePath, record.fileName),
                      name: record.fileName,
                      ext: record.fileType.toLocaleLowerCase(),
                    }}
                  >
                    <Button title={value} type="link" compact>
                      {value}
                    </Button>
                  </FilePreviewWithContainer>
                );
              },
            },
            {
              title: '更新人',
              dataIndex: 'operatorName',
              render: (_, record) => (
                <UserLink id={record.operatorId!} name={record.operatorName!} />
              ),
            },
            {
              title: '更新时间',
              dataIndex: 'gmtModified',
              render: value => dayjs(value).format('YYYY-MM-DD HH:mm:ss'),
              width: 180,
            },
            {
              title: '操作',
              dataIndex: 'action',
              fixed: 'right',
              render: (_, record) => {
                return (
                  <a
                    key="download"
                    href={McUploadFile.generateSrc(record.filePath, record.fileName)}
                    target="_blank"
                    rel="noreferrer"
                  >
                    下载
                  </a>
                );
              },
            },
          ]}
        />
      </Modal>
    </>
  );
}
