import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';

import { selectAllTypePapers, updatePapersAction } from '@manyun/knowledge-hub.state.papers';
import { PapersCategoryCascader } from '@manyun/knowledge-hub.ui.papers-category';

export type PapersBatchUpdatorProps = {};

export function PapersBatchUpdator({}: PapersBatchUpdatorProps) {
  const dispatch = useDispatch();

  const [isModalVisible, setIsModalVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const { selectedIds } = useSelector(selectAllTypePapers());

  const onHandleUpdatePapers = () => {
    setLoading(true);
    form
      .validateFields()
      .then(({ categoryCode }) => {
        dispatch(
          updatePapersAction({
            categoryCode: categoryCode.slice(-1)[0],
            callback: res => {
              setLoading(false);
              if (res) {
                setIsModalVisible(false);
              }
            },
          })
        );
      })
      .catch(err => {
        // 验证不通过时进入 console.log(err);
      });
  };
  return (
    <>
      <Button
        disabled={selectedIds.length === 0}
        type="primary"
        onClick={() => setIsModalVisible(true)}
      >
        批量更新
      </Button>
      {isModalVisible && (
        <Modal
          destroyOnClose
          title="批量更新"
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          onOk={() => onHandleUpdatePapers()}
          confirmLoading={loading}
          okText="提交"
          cancelText="取消"
        >
          <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            <Form.Item
              name="categoryCode"
              label="试卷分类"
              rules={[{ required: true, message: '请选择试卷分类' }]}
            >
              <PapersCategoryCascader style={{ width: '100%' }} />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}
