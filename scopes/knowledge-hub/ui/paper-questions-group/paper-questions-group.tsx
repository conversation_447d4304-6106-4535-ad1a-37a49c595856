import ArrowDownOutlined from '@ant-design/icons/es/icons/ArrowDownOutlined';
import ArrowUpOutlined from '@ant-design/icons/es/icons/ArrowUpOutlined';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';
import React, { useCallback } from 'react';
import { useDispatch } from 'react-redux';

import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Space } from '@manyun/base-ui.ui.space';
import type { SectionTypeConfig } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import type { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  BackendQuestionDifficulty,
  BackendQuestionType,
  QuestionTypeTextMapper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import type { QuestionsCount } from '@manyun/knowledge-hub.service.dcexam.fetch-questions-count';
import type { MutatePaperType } from '@manyun/knowledge-hub.state.papers';
import { papersSliceActions } from '@manyun/knowledge-hub.state.papers';
import { Question } from '@manyun/knowledge-hub.ui.question';
import { TagQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { QuestionsCategory } from '@manyun/knowledge-hub.ui.questions-category';
import { TextQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';
import {
  QuetionsPickerByRandomMode,
  QuetionsPickerBySelectMode,
} from '@manyun/knowledge-hub.ui.quetions-picker';

export type SelectPaperQuestionsGroupProps = {
  form: FormInstance<any>;
  groupId: number;
  mutateType: MutatePaperType;
  questions: (BackendQuestion & { grade: number })[];
};

export type TypeConfigRow = {
  type: number | 0;
  score: number;
  drawTotal?: number;
  difficultCount: {
    EASY: number | 0;
    EASIER_THAN_EASY: number | 0;
    NORMAL: number | 0;
    HARD: number | 0;
    HARDER_THAN_HARD: number | 0;
  };
};

export function SelectPaperQuestionsGroup({
  form,
  groupId,
  mutateType,
  questions,
}: SelectPaperQuestionsGroupProps) {
  const dispatch = useDispatch();

  const onDelete = useCallback(
    question => {
      // 删除的时候不但要把 form 里面每一题的数据删掉，还要把左侧的统一设置分值给删掉。
      const formVal = form.getFieldsValue(true);
      delete formVal[`${groupId}_${question.id}`];
      const keys = Object.keys(formVal);
      const currentGroupArr = keys.filter(key => key.startsWith(`${groupId}_`));
      if (currentGroupArr.length === 0) {
        delete formVal[`grade_${groupId}`];
      }

      form.setFieldsValue({ ...formVal });

      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType,
          updateSection: {
            id: groupId,
            deleteQuestionId: question.id,
          },
        })
      );
    },
    [dispatch]
  );

  const onSort = useCallback(
    (id, type) => {
      dispatch(
        papersSliceActions.sortOrderMutatePaperSectionQuestion({
          mutateType,
          type: type,
          sectionId: groupId,
          questionId: id,
        })
      );
    },
    [dispatch]
  );

  const onChangeQuestionGrade = useCallback(
    (val, question) => {
      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType,
          updateSection: {
            id: groupId,
            updateQuestion: {
              id: question.id,
              grade: val,
            },
          },
        })
      );
    },
    [dispatch]
  );

  return (
    <div style={{ width: '100%' }}>
      {questions.map((question, key) => (
        <Question
          key={question.id}
          question={question}
          mode="create"
          index={key + 1}
          style={{ marginBottom: '20px' }}
        >
          <Space direction="vertical" align="end">
            <Space size="middle">
              <DeleteOutlined size={17} onClick={() => onDelete(question)} />
              {questions.length > 1 && (
                <>
                  <ArrowUpOutlined
                    size={17}
                    onClick={() => {
                      onSort(question.id, 'asc');
                    }}
                  />
                  <ArrowDownOutlined
                    size={17}
                    onClick={() => {
                      onSort(question.id, 'desc');
                    }}
                  />
                </>
              )}
            </Space>
            <Space direction="horizontal" align="center">
              本题分值
              <Form.Item
                name={`${groupId}_${question.id}`}
                initialValue={question.grade || (form.getFieldValue(`grade_${groupId}`) ?? 0)}
                dependencies={[`grade_${groupId}`]}
                rules={[
                  {
                    required: true,
                    validator: async (_, val) => {
                      if (Number(form.getFieldValue(`${groupId}_${question.id}`) || 0) <= 0) {
                        return Promise.reject('分值需大于0');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber
                  min={0}
                  precision={1}
                  style={{ width: '75px' }}
                  onChange={val => {
                    onChangeQuestionGrade(val, question);
                    form.setFieldValue(`grade_${groupId}`, undefined);
                  }}
                />
              </Form.Item>
              分
            </Space>
          </Space>
        </Question>
      ))}
    </div>
  );
}

export type OthesPaperQuestionsGroupProps = {
  mutateType: MutatePaperType;
  groupId: number;
  groupQuestions?: { id: number; type: number }[] /**抽题模式下 模块下的试题 */;
  category?: string | number;
  paperType?: string;
  typeConfig?: Record<string, SectionTypeConfig>;
  paperMarkingWay?: boolean; //试卷判卷方式
};

export function DrawOrRandomPaperQuestionsGroup({
  mutateType,
  category,
  typeConfig,
  groupId,
  groupQuestions,
  paperType,
  paperMarkingWay,
}: OthesPaperQuestionsGroupProps) {
  const dispatch = useDispatch();

  /**每题分数 */
  const onChangeTypeScore = useCallback(
    (val, type) => {
      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType,
          updateSection: {
            id: groupId,
            updateTypeConfig: {
              typeConfigKey: BackendQuestionType[type.type as unknown],
              score: val,
            },
          },
        })
      );
    },
    [dispatch]
  );

  /**抽题模式下抽题数量 */
  const onChangeTypeDrawTotal = useCallback(
    (val, type: unknown) => {
      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType,
          updateSection: {
            id: groupId,
            updateTypeConfig: {
              typeConfigKey: BackendQuestionType[type.type as unknown],
              drawTotal: val,
            },
          },
        })
      );
    },
    [dispatch]
  );

  /** 随机模式下批量更新 difficult count */
  const onSaveTypesConfig = (data: QuestionsCount[], categoryCode: string | number | number[]) => {
    data.forEach(config => {
      const difficultCount = {
        EASIER_THAN_EASY: config.EASIER_THAN_EASY || 0,
        EASY: config.EASY || 0,
        NORMAL: config.NORMAL || 0,
        HARD: config.HARD || 0,
        HARDER_THAN_HARD: config.HARDER_THAN_HARD || 0,
      };
      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType,
          updateSection: {
            id: groupId,
            updateTypeConfig: {
              typeConfigKey: config.type as unknown,
              difficultAllCount: difficultCount,
            },
          },
        })
      );
    });
    dispatch(
      papersSliceActions.setMutatePaperFields({
        mutateType,
        updateSection: {
          id: groupId,
          updateQuestionCategory: Array.isArray(categoryCode)
            ? categoryCode
            : [categoryCode as string],
        },
      })
    );
  };

  /** 抽题模式下选定试题*/
  const onAddSelectSectionByType = (questions: BackendQuestion[], type: unknown) => {
    dispatch(
      papersSliceActions.setMutatePaperFields({
        mutateType,
        updateSection: {
          id: groupId,
          addQuestions: questions,
          addQuestionsType: type.type,
        },
      })
    );
  };

  const getSectionTypesConfig = () => {
    let typeConfigArr: TypeConfigRow[] = [];
    if (typeConfig) {
      typeConfigArr = Object.keys(typeConfig).map(key => ({
        ...typeConfig[key],
        type: BackendQuestionType[key as unknown],
      })) as unknown[];
    }
    return typeConfigArr;
  };

  /**抽题模式下 - 计算最大抽题数量 */
  const getConfigDifficultTotal = (typeConfig: TypeConfigRow) => {
    let total: number = 0;
    Object.keys(typeConfig.difficultCount).forEach(key => {
      total += Number((typeConfig.difficultCount as Record<string, number>)[key as string] || 0);
    });
    return total;
  };

  /**抽题模式下 根据题型选出试题 */
  const groupQuestionByType = useCallback(
    (type: number) => {
      const selectedIds: number[] = [];
      if (groupQuestions) {
        groupQuestions.forEach(question => {
          if (question.type === type) {
            selectedIds.push(question.id);
          }
        });
      }
      return selectedIds;
    },
    [groupQuestions]
  );

  if (paperType === 'RANDOM' && category === undefined) {
    return (
      <>
        <QuetionsPickerByRandomMode
          btnTxt="添加试题"
          typeConfigs={typeConfig}
          defaultCategory={category}
          onSave={(data, categoryCode) => {
            onSaveTypesConfig(data, categoryCode);
          }}
          autoGrade={paperMarkingWay}
        />
        <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="当前暂无数据,请点击添加试题" />
      </>
    );
  }

  return (
    <Space direction="vertical" align="baseline" style={{ width: '100%' }}>
      {paperType === 'RANDOM' && (
        <div style={{ display: 'flex', alignItems: 'baseline' }}>
          <span>试题分类：</span>
          <span style={{ marginRight: '10px' }}>
            <QuestionsCategory categoryCode={category as number} />
          </span>
          <QuetionsPickerByRandomMode
            typeConfigs={typeConfig}
            defaultCategory={category}
            onSave={(data, categoryCode) => {
              onSaveTypesConfig(data, categoryCode);
            }}
          />
        </div>
      )}
      <div style={{ display: 'flex', alignItems: 'start' }}>
        {paperType === 'RANDOM' && (
          <div style={{ width: '70px', marginTop: '9px' }}>试题数量：</div>
        )}
        <div style={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
          {getSectionTypesConfig()
            .sort((a, b) => a.type - b.type)
            .map((type, key) => (
              <Space key={type.type} direction="horizontal" size={4} style={{ margin: '5px 0' }}>
                <TagQuestionType typeVal={type.type} />
                {type.difficultCount &&
                  Object.keys(type.difficultCount).map(key => (
                    <Space key={key} direction="horizontal" size={1}>
                      <TextQuestionsDifficulty
                        difficultyVal={BackendQuestionDifficulty[key as unknown]}
                        colon
                      />
                      <div
                        style={{
                          color: 'var(--manyun-warning-color)',
                          minWidth: '22px',
                          textAlign: 'center',
                        }}
                      >
                        {(type.difficultCount as Record<string, number>)[key]}
                      </div>
                      题；
                    </Space>
                  ))}

                <Space direction="horizontal" align="center">
                  每题分值
                  <Form.Item
                    name={`${groupId}_每题分值_${type.type}`}
                    initialValue={type.score || 0}
                    rules={[
                      {
                        required: true,
                        validator: async (_, val) => {
                          const drawCount =
                            paperType === 'DRAW'
                              ? type.drawTotal || 0
                              : Object.keys(type.difficultCount).reduce((sum, key) => {
                                  return sum + Number((type.difficultCount as unknown)[key] || 0);
                                }, 0);
                          if (Number(val || 0) <= 0 && drawCount > 0) {
                            return Promise.reject('分值需大于0');
                          } else {
                            return Promise.resolve();
                          }
                        },
                      },
                    ]}
                  >
                    <InputNumber
                      style={{ width: '75px' }}
                      min={0}
                      precision={1}
                      onChange={val => {
                        onChangeTypeScore(val, type);
                      }}
                    />
                  </Form.Item>
                  分
                </Space>

                {/* 抽题模式下 */}
                {paperType !== 'RANDOM' && (
                  <>
                    <Space direction="horizontal" style={{ marginRight: '5px' }}>
                      抽题数量
                      <InputNumber
                        style={{ width: '75px' }}
                        precision={0}
                        min={0}
                        max={getConfigDifficultTotal(type)}
                        defaultValue={type.drawTotal}
                        onChange={val => {
                          onChangeTypeDrawTotal(val, type);
                        }}
                      />
                      题
                    </Space>

                    <QuetionsPickerBySelectMode
                      btnTxt="选定试题"
                      autoGrade={paperMarkingWay}
                      defaultQuestionIds={groupQuestionByType(type.type)}
                      title={`选定${(QuestionTypeTextMapper as Record<string, string>)[type.type]}`}
                      questionType={type.type}
                      onOk={questions => {
                        onAddSelectSectionByType(questions, type);
                      }}
                    />
                  </>
                )}
              </Space>
            ))}
        </div>
      </div>
    </Space>
  );
}
