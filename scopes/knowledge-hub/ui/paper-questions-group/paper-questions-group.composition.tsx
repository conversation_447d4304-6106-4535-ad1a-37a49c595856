import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { MutatePaperType } from '@manyun/knowledge-hub.state.papers';

import {
  DrawOrRandomPaperQuestionsGroup,
  SelectPaperQuestionsGroup,
} from './paper-questions-group';

export const RowdomPaperQuestionsGroup = () => (
  <ConfigProvider>
    <FakeStore>
      <DrawOrRandomPaperQuestionsGroup
        groupId={1}
        mutateType={MutatePaperType.UpdatePaper}
        typeConfig={{}}
      />
    </FakeStore>
  </ConfigProvider>
);

export const DrwaPaperQuestionsGroup = () => (
  <ConfigProvider>
    <FakeStore>
      <DrawOrRandomPaperQuestionsGroup
        groupId={1}
        mutateType={MutatePaperType.UpdatePaper}
        typeConfig={{}}
      />
    </FakeStore>
  </ConfigProvider>
);

export const BasicPaperQuestionsGroup = () => (
  <ConfigProvider>
    <FakeStore>
      <SelectPaperQuestionsGroup
        groupId={1}
        mutateType={MutatePaperType.UpdatePaper}
        questions={[
          {
            id: 1, //试题id
            type: 1,
            categoryCode: '1001', //试题分类
            difficulty: 1, //难度
            title: '测试试题', //题目标题
            options: [
              //选项
              'A:测试选项1',
              'B:测试选项2',
              'C:测试选项3',
              'D:测试选项4',
            ],
            answers: [
              //答案
              'A',
              'B',
              'C',
            ],
            analysis: '这个题目明显选择 A B C', //分析
            autoGrade: true, //是否自动判分
            grade: 0, //设置分数
          },
        ]}
      />
    </FakeStore>
  </ConfigProvider>
);
