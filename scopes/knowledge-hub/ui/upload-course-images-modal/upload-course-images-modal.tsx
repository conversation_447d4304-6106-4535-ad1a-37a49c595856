/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-23
 *
 * @packageDocumentation
 */
import { InboxOutlined } from '@ant-design/icons';
import React, { useCallback, useState } from 'react';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { saveCourseImages } from '@manyun/knowledge-hub.service.dcexam.save-course-images';

export type UploadCourseImagesModalProps = {
  courseId: number;
  onSearch: () => void;
  children?: JSX.Element;
  style?: React.CSSProperties;
};

export function UploadCourseImagesModal({
  courseId,
  onSearch,
  children,
  style,
}: UploadCourseImagesModalProps) {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isSubmitDisabled, setIsSubmitDisabled] = useState(false);

  const [files, setFiles] = useState<McUploadFileJSON[]>();

  const fetchAttachments = useCallback(async () => {
    const { data, error } = await fetchBizFileInfos({
      targetId: courseId.toString(),
      targetType: 'COURSE',
      fileTypeList: ['COURSE_CERT_FILE'],
    });
    if (error) {
      message.error(error.message);
    } else {
      setFiles(data.data);
      setIsSubmitDisabled(data.data.length > 0);
    }
  }, [courseId]);

  const showModal = () => {
    setOpen(true);
  };
  const closeModal = () => {
    setOpen(false);
    setFiles([]);
  };
  return (
    <>
      <Button
        style={style}
        type="link"
        compact
        onClick={() => {
          showModal();
          fetchAttachments();
        }}
      >
        {children ? children : '上传照片'}
      </Button>
      <Modal
        destroyOnClose
        title="上传现场照片"
        open={open}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              closeModal();
            }}
          >
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            disabled={!files?.length && !isSubmitDisabled}
            loading={loading}
            onClick={async () => {
              setLoading(true);
              const { error } = await saveCourseImages({
                courseId,
                fileInfoList: files ?? [],
              });
              setLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
              closeModal();
              onSearch();
            }}
          >
            提交
          </Button>,
        ]}
        onCancel={() => {
          closeModal();
        }}
      >
        <McUpload
          type="drag"
          fileList={files}
          multiple
          accept="image/*"
          onChange={info => {
            if (info.fileList.length >= 10) {
              message.error('图片上传数量不能超过9张');
              return;
            }
            setFiles(info.fileList);
          }}
        >
          <Space direction="vertical">
            <p>
              <InboxOutlined style={{ fontSize: 48, color: `var(--${prefixCls}-primary-color)` }} />
            </p>
            <Typography.Text>点击或将文件拖拽到这里上传</Typography.Text>
            <Typography.Text type="secondary">支持扩展名：image/*；图片上限9张</Typography.Text>
          </Space>
        </McUpload>
      </Modal>
    </>
  );
}
