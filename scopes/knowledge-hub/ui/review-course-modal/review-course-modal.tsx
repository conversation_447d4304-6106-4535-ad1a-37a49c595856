/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-23
 *
 * @packageDocumentation
 */
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { reviewCourse } from '@manyun/knowledge-hub.service.dcexam.review-course';

export type ReviewCourseModalProps = {
  courseId: number;
  review?: string;
  onSearch: () => void;
  children?: JSX.Element;
  style?: React.CSSProperties;
};

export function ReviewCourseModal({
  courseId,
  review,
  onSearch,
  children,
  style,
}: ReviewCourseModalProps) {
  const [form] = Form.useForm<{ review: string }>();

  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const showModal = () => {
    setOpen(true);
    form.setFieldValue('review', review ?? '');
  };
  const closeModal = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        style={style}
        type="link"
        compact
        onClick={() => {
          showModal();
        }}
      >
        {children ? children : '复盘总结'}
      </Button>
      <Modal
        width={538}
        destroyOnClose
        title="总结"
        open={open}
        footer={[
          <Button
            key="cancel"
            onClick={() => {
              closeModal();
            }}
          >
            取消
          </Button>,
          <Button
            key="save"
            type="primary"
            loading={loading}
            onClick={async () => {
              form.validateFields().then(async (val: Record<string, any>) => {
                setLoading(true);

                const { error } = await reviewCourse({
                  courseId,
                  review: val.review.trim(),
                });
                setLoading(false);

                if (error) {
                  message.error(error.message);
                  return;
                }

                closeModal();
                onSearch();
              });
            }}
          >
            提交
          </Button>,
        ]}
        onCancel={() => {
          closeModal();
        }}
      >
        <Form form={form}>
          <Form.Item
            label="复盘总结"
            name="review"
            rules={[
              { type: 'string', max: 500, message: '最多输入 500 个字符！' },
              { required: true, whitespace: true, message: '复盘总结不能为空！' },
            ]}
          >
            <Input.TextArea
              style={{ width: 400 }}
              rows={4}
              // maxLength={500}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
