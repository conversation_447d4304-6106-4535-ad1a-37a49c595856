import React, { useState } from 'react';

import { ComplexUsersPicker } from '@manyun/auth-hub.ui.complex-users-picker';
import type {
  ComplexUsersPickerProps,
  Value as ComplexUsersPickerValue,
} from '@manyun/auth-hub.ui.complex-users-picker';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Tabs } from '@manyun/base-ui.ui.tabs';

export type Users = {
  requiredUsers: ComplexUsersPickerValue;
  optionalUsers: ComplexUsersPickerValue;
};

export type GroupUsersPickerProps = {
  requiredText: string;
  optionalText: string;
  value?: Users;
  buttonText?: string;
  buttonType?: string;
  buttonLoading?: boolean;
  disabled?: boolean;
  blockGuids?: string[];
  onChange?: (value: {
    requiredUsers: ComplexUsersPickerValue | undefined;
    optionalUsers: ComplexUsersPickerValue | undefined;
    required: boolean;
  }) => void;
  onReset?: (value?: {
    requiredUsers: ComplexUsersPickerValue | undefined;
    optionalUsers: ComplexUsersPickerValue | undefined;
    required: boolean;
  }) => void;
  onSubmit?: (value?: {
    requiredUsers: ComplexUsersPickerValue | undefined;
    optionalUsers: ComplexUsersPickerValue | undefined;
    required: boolean;
  }) => void;
  showDepts?: boolean;
  id?: string;
} & Pick<
  ComplexUsersPickerProps,
  'onSelectedRolesChange' | 'onSelectedUsersChange' | 'disabledUserKeys'
>;

export const GroupUsersPicker = React.forwardRef<unknown, GroupUsersPickerProps>(
  (
    {
      requiredText,
      optionalText,
      buttonText,
      buttonType,
      buttonLoading,
      onChange,
      value,
      id,
      showDepts = true,
      disabledUserKeys,
      disabled,
      blockGuids,
      onReset,
      onSubmit,
      ...props
    },
    ref?
  ) => {
    const tabItems = [
      {
        label: requiredText,
        key: 'required',
        children: (
          <ComplexUsersPicker
            blockGuids={blockGuids}
            showDepts={showDepts}
            showModal={false}
            modalTitle={requiredText}
            value={value!.requiredUsers}
            disabledRoleKeys={value?.optionalUsers.roleKeys}
            disabledUserKeys={Array.from(
              new Set((disabledUserKeys ?? []).concat(value?.optionalUsers.userKeys ?? []))
            )}
            onChange={value => {
              onChangeUsers(value, true);
            }}
            {...props}
          />
        ),
      },
      {
        label: optionalText,
        key: 'optional',
        children: (
          <ComplexUsersPicker
            blockGuids={blockGuids}
            showDepts={showDepts}
            showModal={false}
            modalTitle={optionalText}
            value={value!.optionalUsers}
            disabledRoleKeys={value?.requiredUsers.roleKeys}
            disabledUserKeys={value?.requiredUsers.userKeys}
            onChange={value => {
              onChangeUsers(value, false);
            }}
          />
        ),
      },
    ];

    const onChangeUsers = (val: unknown, required: boolean) => {
      if (onChange) {
        required
          ? onChange({ requiredUsers: val, optionalUsers: value?.optionalUsers, required })
          : onChange({ optionalUsers: val, requiredUsers: value?.requiredUsers, required });
      }
    };
    const [open, setOpen] = useState(false);

    const showModal = () => {
      setOpen(true);
    };
    const closeModal = () => {
      setOpen(false);
    };

    return (
      <>
        <>
          <Button
            type={buttonType ?? 'default'}
            disabled={disabled}
            compact={!!buttonType}
            onClick={() => {
              showModal();
            }}
          >
            {buttonText ?? '添加人员'}
          </Button>
          <Modal
            destroyOnClose
            width={1080}
            bodyStyle={{ maxHeight: 'calc(80vh - 109px)', overflowY: 'auto' }}
            title={buttonText ?? '人员选择'}
            open={open}
            okText="提交"
            confirmLoading={buttonLoading}
            onCancel={() => {
              closeModal();
              if (onReset) {
                onReset();
              }
            }}
            onOk={() => {
              if (onSubmit) {
                onSubmit();
              }
              closeModal();
            }}
          >
            <Tabs items={tabItems} />
          </Modal>
        </>
      </>
    );
  }
);

GroupUsersPicker.displayName = 'GroupUsersPicker';
