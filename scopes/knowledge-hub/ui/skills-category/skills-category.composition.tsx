import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { SkillsCategory, SkillsCategoryCascader } from './skills-category';

export const BasicSkillsCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <SkillsCategoryCascader />
    </FakeStore>
  </ConfigProvider>
);

export const TextSkillsCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <SkillsCategory categoryCode={3} />
    </FakeStore>
  </ConfigProvider>
);
