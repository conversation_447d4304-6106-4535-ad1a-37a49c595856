import React, { useEffect } from 'react';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps } from '@manyun/base-ui.ui.cascader';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { TextProps } from '@manyun/base-ui.ui.typography';

import { useSkillsCategory } from '@manyun/knowledge-hub.hook.use-skills-category';

export type SkillCategoryProps = {
  style?: React.CSSProperties;
  /** 三级技能分类 ID */
  categoryCode: number;
  type?: TextProps['type'];
};

export function SkillsCategory({ style, categoryCode, type }: SkillCategoryProps) {
  const [{ loading, entities }, { readSkillCategory }] = useSkillsCategory({
    withTree: true,
  });

  const level3 = entities[categoryCode] || { name: '未知', parentId: null };
  const level2 = level3?.parentId ? entities[level3.parentId] : { name: '未知', parentId: null };
  const level1 = level2?.parentId ? entities[level2.parentId] : { name: '未知' };

  useEffect(() => {
    readSkillCategory({
      policy: (ids: number[]) => {
        return ids.includes(categoryCode) ? 'cache-only' : 'network-only';
      },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryCode]);

  return (
    <Spin spinning={loading}>
      <Typography.Text style={style} type={type}>
        {level1?.name}/{level2?.name}/{level3?.name}
      </Typography.Text>
    </Spin>
  );
}

export type SkillsCategoryCascaderProps = Omit<
  CascaderProps,
  'options' | 'defaultValue' | 'value'
> & {
  trigger?: 'onDidMount' | 'onFocus';
  defaultValue?: number[];
  value?: number[];
  /** 当组装完完整的三层分类时 */
  onMappingCompleted?(value: number[]): void;
};

export type CascaderRef = {
  focus: () => void;
  blur: () => void;
};

export const SkillsCategoryCascader = React.forwardRef(
  (
    {
      trigger = 'onFocus',
      onFocus,
      value,
      defaultValue,
      onMappingCompleted,
      ...props
    }: SkillsCategoryCascaderProps,
    ref?: React.Ref<CascaderRef>
  ) => {
    const [internalValue, setInternalValue] = React.useState<number[] | string[] | undefined>(
      value || defaultValue
    );
    const [{ loading, tree, entities }, { readSkillCategory }] = useSkillsCategory({
      withTree: true,
    });

    useEffect(() => {
      if (trigger === 'onDidMount') {
        readSkillCategory();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [trigger]);

    // 当只传入了一个分类 ID，则认为传入了三级考试分类
    // 需要组装完整的三层分类 ID 的数组
    React.useEffect(() => {
      if (
        value?.length === 1 &&
        entities[value[0]] &&
        internalValue?.length === 1 &&
        entities[internalValue[0] as number]
      ) {
        const defaultCategory = value[0];
        const level3 = entities[defaultCategory];
        const level2 = entities[level3!.parentId] || { id: undefined, parentId: -999 };
        const level1 = entities[level2.parentId] || { id: undefined, parentId: -999 };
        if (level2.id !== undefined && level1.id !== undefined) {
          const newValue: number[] = [level1.id, level2.id, level3!.id];
          if (typeof onMappingCompleted == 'function') {
            onMappingCompleted(newValue);
          } else {
            setInternalValue(newValue);
          }
        }
      }
    }, [value, entities, internalValue, onMappingCompleted]);

    return (
      <Cascader
        ref={ref}
        loading={loading}
        options={tree || []}
        defaultValue={defaultValue}
        value={internalValue}
        displayRender={label => {
          // if (label.length !== 3) {
          //   return <>未知/未知/未知</>;
          // }
          return label.join('/');
        }}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readSkillCategory();
          }
          onFocus?.(evt);
        }}
        {...props}
      />
    );
  }
);
SkillsCategoryCascader.displayName = 'SkillsCategoryCascader';
SkillsCategory.Cascader = SkillsCategoryCascader;
