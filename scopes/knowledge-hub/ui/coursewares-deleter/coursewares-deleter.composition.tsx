import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { CoursewaresDeleter } from './coursewares-deleter';

export const BatchCoursewaresDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursewaresDeleter />
    </FakeStore>
  </ConfigProvider>
);

// export const SingleCoursewaresDeleter = () => (
//   <ConfigProvider>
//     <FakeStore>
//       <CoursewaresDeleter id={1} />
//     </FakeStore>
//   </ConfigProvider>
// );
