import React from 'react';
import { useSelector } from 'react-redux';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import {
  Courseware,
  Variant,
  coursewaresSliceActions,
  deleteCoursewaresAction,
  selectCoursewareEntity,
  selectCoursewareSelectedIds,
} from '@manyun/knowledge-hub.state.coursewares';

export type CoursewaresDeleterProps = {
  groupKey?: string;
};

export function CoursewaresDeleter({ groupKey }: CoursewaresDeleterProps) {
  const dispatch = useDispatch();
  const batchDelete = groupKey === undefined;
  const courseware = useSelector(selectCoursewareEntity(groupKey)) as Courseware;
  const selectedIds = useSelector(selectCoursewareSelectedIds());
  const [setFields] = useCoursewaresFields(Variant.ALL);

  const onHandleDelete = () => {
    Modal.confirm({
      title: batchDelete ? (
        '请确认是否删除所选的课件分类（同时删除该分类下的所有数据）或课件。'
      ) : (
        <span>
          确认要删除<span>{courseware.fileName}</span>的
          {courseware.fileType === 'folder' ? '分类' : '课件'}吗？
        </span>
      ),
      content: '删除之后该分类下课件（或该课件）将不能学习，已经添加该课件的课程将不受影响。',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dispatch(
          deleteCoursewaresAction({
            id: groupKey,
            callback: res => {
              if (res) {
                setFields({ categoryCode: 2, name: undefined });
                dispatch(coursewaresSliceActions.toggleReRender());
              }
            },
          })
        );
      },
    });
  };

  return (
    <>
      <Button
        disabled={batchDelete && selectedIds.length === 0}
        type={batchDelete ? 'primary' : 'link'}
        danger={batchDelete}
        onClick={onHandleDelete}
        style={batchDelete ? {} : { padding: 0 }}
      >
        {batchDelete ? '批量删除' : '删除'}
      </Button>
      <Modal />
    </>
  );
}
