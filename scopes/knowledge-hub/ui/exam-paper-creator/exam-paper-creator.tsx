import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { BackendExamPaperDetail } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { mutateExamPaperWeb as createExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper';

export type ExamPaperCreatorProps = {
  examId: number;
  /** 如果是重新发起随堂测验，则需传入 `courseId` */
  courseId?: number;
  text?: React.ReactNode;
  onCompleted?(examPaperId: number): void;
  [key: string]: any;
};

export function ExamPaperCreator({
  examId,
  courseId,
  text = '开始考试',
  onCompleted,
  ...rest
}: ExamPaperCreatorProps) {
  return (
    <Button
      type="primary"
      onClick={async () => {
        const { error, data } = await createExamPaper('creating', { examId, courseId });
        if (error) {
          message.error(error.message);
          return;
        }
        onCompleted?.((data as BackendExamPaperDetail).id);
      }}
      {...rest}
    >
      {text}
    </Button>
  );
}
