import React, { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { EditableTree, RestrictedDataNode } from '@manyun/base-ui.ui.editable-tree';
import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';
import { TreeSelect, TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import { useQuestions } from '@manyun/knowledge-hub.hook.use-questions';
import { useQuestionsCategory } from '@manyun/knowledge-hub.hook.use-questions-category';
import { useQuestionsFields } from '@manyun/knowledge-hub.hook.use-questions-fields';
import {
  BackendCategoryType,
  Category,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { questionsSliceActions } from '@manyun/knowledge-hub.state.questions';

function getTreeNode(isLeaf = false) {
  return {
    key: (-1 * new Date().getTime()).toString(),
    title: '',
    isLeaf,
    parentId: 0,
    level: 1,
    count: 0,
    isDraft: true,
  };
}

export type EditTreeQuestionsCategroyProps = {};

export type EditTreeRef = {};

export const EditTreeQuestionsCategroy = React.forwardRef(
  // eslint-disable-next-line no-empty-pattern
  ({}: EditTreeQuestionsCategroyProps, ref?: React.Ref<EditTreeRef>) => {
    const [
      { loading, tree, expandedKeys },
      {
        readQuestionCategory,
        createQuestionCategory,
        deleteQuestionCategory,
        updateQuestionCategory,
        setCategoriesExpandedKeys,
      },
    ] = useQuestionsCategory(true);
    const [setFields, fields] = useQuestionsFields();
    const [getQuestions] = useQuestions();
    const dispatch = useDispatch();

    useEffect(() => {
      readQuestionCategory({ withCount: true });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    /**数据加载完成 */
    useEffect(() => {
      if (tree && tree.length > 0 && fields.categoryCode === undefined) {
        const rootParentKey = tree[0].key;
        setFields({ categoryCode: rootParentKey });
        if (rootParentKey !== undefined) {
          setCategoriesExpandedKeys([rootParentKey as string]);
        }
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [loading]);

    const onAddLocal = useCallback(
      (node, newNode) => {
        const id = Number(newNode.key);
        const newCategory: Category = {
          id: id,
          code: id,
          name: '',
          parentId: Number(node.key),
          type: BackendCategoryType.QUESTION,
        };
        dispatch(questionsSliceActions.setCategoriesAdd({ category: newCategory }));
      },
      [dispatch]
    );

    const onDeleteLocal = useCallback(
      node => {
        dispatch(questionsSliceActions.setCategoriesDelete({ id: node.key }));
      },
      [dispatch]
    );

    const deleteTreeNodeHandler = useCallback(
      (node: RestrictedDataNode) => {
        if (Number(node.key) > -1) {
          /**如果删除的是 选中的分类 则删除完需要更新选中分类为根节点 */
          deleteQuestionCategory(Number(node.key), res => {
            if (res) {
              if (Number(fields.categoryCode) === Number(node.key)) {
                setFields({ categoryCode: tree != null ? tree[0].key : '' });
              } else {
                getQuestions();
              }
            }
          });
        }
        onDeleteLocal(node);
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [fields.categoryCode]
    );

    const saveTreeNodeHandler = useCallback(
      (node: RestrictedDataNode, callback?: (res: boolean) => void) => {
        if (Number(node.key) < 0) {
          createQuestionCategory({
            id: Number(node.key),
            name: node.title.trim(),
            parentId: node.parentId,
          });
          callback && callback(true);
        } else {
          updateQuestionCategory({
            id: Number(node.key),
            name: node.title.trim(),
            callback: res => {
              callback && callback(res);
            },
          });
        }
      },
      // eslint-disable-next-line react-hooks/exhaustive-deps
      [fields.categoryCode, dispatch]
    );

    if (loading && tree && tree.length === 0) {
      return <Spin spinning />;
    }
    return (
      <Spin spinning={loading}>
        <EditableTree
          maxTitleLength={10}
          titleRender={node => `${node.title}(${node.count})`}
          selectedKeys={fields.categoryCode ? [fields.categoryCode.toString()] : []}
          expandedKeys={expandedKeys}
          treeData={tree as []}
          cancelText=""
          addChildTreeNodeText=""
          editText=""
          deleteText=""
          saveText=""
          allowDelete={node => {
            return node.parentId !== 0;
          }}
          allowEdit={node => {
            return node.parentId !== 0;
          }}
          allowAddChild={node => node.level !== 3}
          /**展开事件 */
          onExpand={expandedKeys => {
            setCategoriesExpandedKeys(expandedKeys);
          }}
          /**选中事件 */
          onSelect={val => {
            if (val.length === 0) {
              return;
            }
            if (Array.isArray(val) && val[0] !== undefined) {
              if (Number(val[0]) < 0) {
                return;
              }
            }
            setFields({ categoryCode: val[0] });
          }}
          /**新增node */
          onAddTreeNode={node => {
            const newNode = getTreeNode(node && node.level === 2 ? true : false);
            if (node !== null) {
              newNode.parentId = Number(node.key);
              newNode.level = node.level + 1;
              onAddLocal(node, newNode);
            }
            /**新增需要手动展开节点 */
            setCategoriesExpandedKeys(node.key);
            return newNode;
          }}
          /**保存node */
          onSave={node => {
            const name = node.title.trim();
            if (name === '') {
              message.error('不可添加为空格');
              return;
            }

            return new Promise<boolean>(resolve => {
              saveTreeNodeHandler(node, res => {
                resolve(res);
              });
            });
          }}
          /**删除node */
          onDeleteConfirm={node =>
            new Promise<boolean>(resolve => {
              deleteTreeNodeHandler(node);
              resolve(true);
            })
          }
          /**取消 */
          onCancel={node => {
            if (!node.isDraft) {
              return;
            }
            deleteTreeNodeHandler(node);
          }}
        />
      </Spin>
    );
  }
);

export type TreeRef = {
  focus: () => void;
  blur: () => void;
};

export type SelectTreeQuestionCategoryProps = Omit<TreeSelectProps<any>, 'treeData'> & {
  /** 试卷分类 ID */
  defaultCategoryCode?: number;
  trigger?: 'onDidMount' | 'onFocus';
};

export const SelectTreeQuestionCategory = React.forwardRef(
  (
    { trigger = 'onFocus', onFocus, ...props }: SelectTreeQuestionCategoryProps,
    ref?: React.Ref<any>
  ) => {
    const [{ loading, tree }, { readQuestionCategory }] = useQuestionsCategory(true);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        readQuestionCategory({
          policy: ids => (ids.length > 0 ? 'cache-only' : 'network-only'),
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [trigger]);

    return (
      <TreeSelect
        ref={ref}
        loading={loading}
        treeData={tree as []}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readQuestionCategory({
              policy: ids => (ids.length > 0 ? 'cache-only' : 'network-only'),
            });
          }
          onFocus?.(evt);
        }}
        {...props}
      />
    );
  }
);

export type QuestionsCategoryProps = {
  style?: React.CSSProperties;
  /** 试题分类 ID */
  categoryCode: number;
  showRoot?: boolean;
};

export function QuestionsCategory({
  categoryCode,
  style,
  showRoot = true,
}: QuestionsCategoryProps) {
  const [{ loading, entities }, { readQuestionCategory }] = useQuestionsCategory();

  useEffect(() => {
    readQuestionCategory({
      policy: ids => (ids.includes(categoryCode) ? 'cache-only' : 'network-only'),
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryCode]);

  const loopCategoryName = useCallback(() => {
    let list: string[] = [];
    var loop = (code: number) => {
      const entity = entities[code];
      if (!entity) {
        list.push('未知');
        return;
      }
      if (entity.parentId === 0) {
        if (showRoot) {
          list.push(entity.name);
        }
        return;
      } else {
        list.push(entity.name);
      }
      loop(entity.parentId);
    };
    if (entities[categoryCode] && entities[categoryCode].parentId === 0) {
      list.push(entities[categoryCode].name);
    } else {
      loop(categoryCode);
    }
    return list;
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [loading, entities]);

  if (loading) {
    return <Spin style={style} spinning={loading} size="small" />;
  }

  const categoryNames = loopCategoryName().reverse().join('/');

  return <span title={categoryNames}>{categoryNames}</span>;
}
