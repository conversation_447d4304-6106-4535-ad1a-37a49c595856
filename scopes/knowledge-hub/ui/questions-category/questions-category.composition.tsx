import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks as fetchCategoryMock } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { registerWebMocks as mutateCategoryMock } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';
import { getMock } from '@manyun/service.request';

import {
  EditTreeQuestionsCategroy,
  QuestionsCategory,
  SelectTreeQuestionCategory,
} from './questions-category';

export const EditTreeQuestionsCategory = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    fetchCategoryMock(mock);
    mutateCategoryMock(mock);
    mock.onAny().networkError();
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <EditTreeQuestionsCategroy />
      </FakeStore>
    </ConfigProvider>
  );
};

export const SelectTreeQuestionsCategory = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    fetchCategoryMock(mock);
    mutateCategoryMock(mock);
    mock.onAny().networkError();
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <SelectTreeQuestionCategory onChange={val => {}} />
      </FakeStore>
    </ConfigProvider>
  );
};

export const CascaderTextQuestionsCategory = () => {
  const [initial, update] = useState(false);
  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initial) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <QuestionsCategory categoryCode={45} />
      </FakeStore>
    </ConfigProvider>
  );
};
