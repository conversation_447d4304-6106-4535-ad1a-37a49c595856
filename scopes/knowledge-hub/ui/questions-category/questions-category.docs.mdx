---
description: '试题分类 - UI.'
labels: ['label1', 'label2', 'question-category']
---

## React Component for rendering text

A basic div that renders some text

### Component usage

```js
<QuestionsCategory variant="editTree" />
```

```js
<QuestionsCategory variant="selectTree" onChange={(value) => {}} />
```

```js
<QuestionsCategory variant="cascaderText" />
```

### Using props to customize the text
