import React, { useEffect, useState } from 'react';

import { ExclamationCircleOutlined } from '@ant-design/icons';
import { RadioGroupProps } from 'antd/es/radio';
import type { SelectValue } from 'antd/es/select';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { BakendMarkingWayMapper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export type PapersMarkingModeProps = {
  variant: 'select' | 'text' | 'radio';
  markingMode?: number | string;
} & SelectProps<SelectValue> &
  RadioGroupProps;

export function PapersMarkingMode({ variant, markingMode, ...props }: PapersMarkingModeProps) {
  const [options, setOptions] = useState(null);

  useEffect(() => {
    const markingModeOptions =
      variant !== 'text'
        ? Object.keys(BakendMarkingWayMapper).reduce((options, key) => {
            options.push({
              label: (BakendMarkingWayMapper as any)[key],
              value: key,
            });
            return options;
          }, [] as { label: string; value: string }[])
        : (BakendMarkingWayMapper as Record<string, string>);
    setOptions(markingModeOptions as any);
  }, []);

  if (variant === 'text') {
    return <span>{markingMode && options && (options as any)[markingMode]}</span>;
  } else if (variant === 'radio') {
    const radioProps = { ...props };
    return (
      <Radio.Group {...radioProps}>
        {((options || []) as any).map((mode: { value: any; label: any }) => {
          return (
            <Radio value={mode.value} key={mode.value}>
              {mode.label}
              {mode.value == '1' && (
                <Tooltip
                  title="开启后，组卷时不包含填空题(单独设置判卷方式除外)和问答题"
                  placement="top"
                >
                  <ExclamationCircleOutlined style={{ marginLeft: '10px' }} />
                </Tooltip>
              )}
            </Radio>
          );
        })}
      </Radio.Group>
    );
  } else {
    const selectProps = { ...props, options: options as any };
    return <Select {...selectProps} />;
  }
}
