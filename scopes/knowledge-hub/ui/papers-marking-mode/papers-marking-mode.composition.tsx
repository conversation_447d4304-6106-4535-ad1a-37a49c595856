import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { PapersMarkingMode } from './papers-marking-mode';

export const TextPaperMark = () => (
  <ConfigProvider>
    <FakeStore>
      <PapersMarkingMode variant="text" markingMode={'1'} />
    </FakeStore>
  </ConfigProvider>
);

export const RadioPaperMark = () => (
  <ConfigProvider>
    <PapersMarkingMode
      variant="radio"
      defaultValue={0}
      onChange={e => {
        // console.log((e as any).target.value);
      }}
    />
  </ConfigProvider>
);

export const MultiplePaperMark = () => (
  <ConfigProvider>
    <PapersMarkingMode variant="select" mode="multiple" style={{ width: 200 }} />
  </ConfigProvider>
);

export const SelectPaperMark = () => (
  <ConfigProvider>
    <PapersMarkingMode variant="select" style={{ width: 100 }} />
  </ConfigProvider>
);
