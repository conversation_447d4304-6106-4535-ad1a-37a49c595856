import React from 'react';
import { useHistory } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  HISTORY_EXAM_STATE_KEY,
  HISTORY_EXAM_STATE_TXT,
} from '@manyun/knowledge-hub.page.my-exams';
import { generateExamPaperLocation } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import {
  BackendUserExamPaperSatus,
  UserExamPaperStatusMapper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { MyExamTypes } from '@manyun/knowledge-hub.state.exams';
import { ExamPaperCreator } from '@manyun/knowledge-hub.ui.exam-paper-creator';

import coverImg from './cover-img.png';

export type ExamGalleryCoverProps = {
  exam: {
    status: BackendUserExamPaperSatus;
    [propName: string]: any;
  };
  examTypeTabKey: string;
};

export function ExamGalleryCover({ exam, examTypeTabKey }: ExamGalleryCoverProps) {
  const history = useHistory();

  return (
    <Card
      style={{ width: 242, height: 300 }}
      bodyStyle={{
        padding: '16px 24px',
      }}
      cover={
        <div style={{ display: 'flex', height: 140, position: 'relative' }}>
          <img
            alt="unknown"
            src={coverImg}
            style={{ width: '100%', height: 140, position: 'absolute' }}
          />
          <div
            style={{
              width: '100%',
              marginTop: 36,
              textAlign: 'center',
              color: 'white',
              zIndex: 2,
              marginLeft: 16,
              marginRight: 16,
            }}
          >
            <Typography.Paragraph
              style={{ width: 210, color: 'white' }}
              ellipsis={{ rows: 2, tooltip: exam.name }}
            >
              {exam.name}
            </Typography.Paragraph>
            <p>考试时长：{exam.duration}分钟</p>
          </div>
        </div>
      }
    >
      <p style={{ fontSize: 12 }}>开始时间: {moment(exam.startTime).format('YYYY-MM-DD HH:mm')}</p>
      <p style={{ fontSize: 12 }}>结束时间: {moment(exam.endTime).format('YYYY-MM-DD HH:mm')}</p>
      <Tag
        color={
          examTypeTabKey === MyExamTypes.MyAvailableExams
            ? getTagStatus(exam.status)
            : getHistoryTagStatus(exam.status, exam.pass)
        }
      >
        {examTypeTabKey === MyExamTypes.MyAvailableExams
          ? UserExamPaperStatusMapper[exam.status]
          : getHistoryTagTxt(exam.status, exam.pass)}
      </Tag>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        {examTypeTabKey === MyExamTypes.MyAvailableExams && (
          <>
            {exam.status === BackendUserExamPaperSatus.STARTED && (
              <ExamPaperCreator
                text="开始考试"
                examId={exam.examId}
                onCompleted={examPaperId =>
                  history.push(generateExamPaperLocation({ id: examPaperId, examId: exam.examId }))
                }
              />
            )}
            {exam.status === BackendUserExamPaperSatus.DURING && (
              <Button
                type="primary"
                onClick={() => {
                  history.push(generateExamPaperLocation({ examId: exam.examId, id: exam.id }));
                }}
              >
                继续考试
              </Button>
            )}
          </>
        )}

        {examTypeTabKey === MyExamTypes.MyHistoryExams && (
          <Button
            type="primary"
            onClick={() => {
              history.push(generateExamPaperLocation({ id: exam.id, examId: exam.examId }));
            }}
          >
            查看
          </Button>
        )}
      </div>
    </Card>
  );
}

function getTagStatus(status: BackendUserExamPaperSatus) {
  if (status === BackendUserExamPaperSatus.INIT) {
    return 'var(--manyun-primary-color)';
  }
  if (status === BackendUserExamPaperSatus.STARTED) {
    return 'var(--manyun-success-color)';
  }
  if (status === BackendUserExamPaperSatus.DURING) {
    return 'var(--manyun-warning-color)';
  }
  // TODO 判卷中，已完成的颜色
  return 'var(--manyun-error-color)';
}

function getHistoryTagStatus(status: string, pass: Boolean) {
  if (status === HISTORY_EXAM_STATE_KEY.SUBMIT) {
    return 'var(--manyun-warning-color)';
  }
  if (pass && status === BackendUserExamPaperSatus.GRADED) {
    return 'var(--manyun-success-color)';
  }
  return 'var(--manyun-error-color)';
}

function getHistoryTagTxt(status: string, pass: Boolean) {
  if (status === HISTORY_EXAM_STATE_KEY.SUBMIT) {
    return HISTORY_EXAM_STATE_TXT[status];
  }
  if (pass && status === BackendUserExamPaperSatus.GRADED) {
    return '已通过';
  }
  return '未通过';
}
