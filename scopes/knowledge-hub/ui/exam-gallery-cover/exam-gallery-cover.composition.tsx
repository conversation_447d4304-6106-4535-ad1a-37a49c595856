import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { BackendUserExamPaperSatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { MyExamTypes } from '@manyun/knowledge-hub.state.exams';

import { ExamGalleryCover } from './exam-gallery-cover';

export const BasicExamGalleryCover = () => (
  <ConfigProvider>
    <ExamGalleryCover
      exam={{ status: BackendUserExamPaperSatus.INIT }}
      examTypeTabKey={MyExamTypes.MyHistoryExams}
    />
  </ConfigProvider>
);
