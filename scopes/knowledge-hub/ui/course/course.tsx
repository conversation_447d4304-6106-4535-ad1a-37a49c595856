import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateLearningSpecificCourseRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import {
  BackendCourse,
  BackendStudyStatus,
} from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { selectCourseEntity } from '@manyun/knowledge-hub.state.courses';

import pic from './card.png';

export type CourseProps = {
  id: number;
};

export function Course({ id }: CourseProps) {
  const course = useSelector(selectCourseEntity(id)) as BackendCourse;
  const history = useHistory();

  return (
    <Card
      style={{ width: 242, height: 315 }}
      bodyStyle={{
        padding: '16px 24px',
        height: 170,
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between',
      }}
      cover={
        <div style={{ display: 'flex', height: 140, position: 'relative' }}>
          <img src={pic} style={{ width: '100%', height: 140, position: 'absolute' }} />
          <div
            style={{
              width: '100%',
              marginTop: 36,
              textAlign: 'center',
              zIndex: 2,
            }}
          >
            {course.name}
          </div>
        </div>
      }
    >
      <Typography.Text ellipsis={{ tooltip: course.name }}>{course.name}</Typography.Text>
      <span style={{ fontSize: 12 }}>
        开始时间: {moment(course.startTime).format('YYYY-MM-DD HH:mm')}
      </span>
      <span style={{ fontSize: 12 }}>
        结束时间: {moment(course.endTime).format('YYYY-MM-DD HH:mm')}
      </span>
      <Tag
        color={course.studyStatus && (textMap as any)[course.studyStatus].color}
        style={{ width: 52 }}
      >
        {course.studyStatus && (textMap as any)[course.studyStatus].label}
      </Tag>
      <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
        <Button
          type="primary"
          disabled={
            course.studyStatus === 'COMPLETED'
              ? false
              : moment().valueOf() < moment(course.startTime).valueOf() ||
                moment().valueOf() > moment(course.endTime).valueOf()
          }
          onClick={() => {
            history.push(generateLearningSpecificCourseRoutePath(course.id, course.studyStatus!));
          }}
        >
          进入课程
        </Button>
      </div>
    </Card>
  );
}

const textMap = {
  [BackendStudyStatus.UN_INVOLVED]: { label: '未参与', color: 'inherit' },
  [BackendStudyStatus.UNDONE]: { label: '未完成', color: 'var(--manyun-error-color)' },
  [BackendStudyStatus.COMPLETED]: { label: '已完成', color: 'var(--manyun-success-color)' },
};
