import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import ExclamationCircleOutlined from '@ant-design/icons/es/icons/ExclamationCircleOutlined';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  deleteQuestionsAction,
  selectAllTypeQuestions,
} from '@manyun/knowledge-hub.state.questions';

import styles from './questions-deleter.module.less';

export type QuestionsDeleterProps = {
  /**
   *  单个删除的试题
   */
  question?: Pick<BackendQuestion & { uniqId: string }, 'uniqId' | 'title' | 'id'>;
  onComplteDelete?: (res: boolean, deleteIds?: string[]) => void;
};

export function QuestionsDeleter({ question, onComplteDelete }: QuestionsDeleterProps) {
  const dispatch = useDispatch();

  const { selectedIds } = useSelector(selectAllTypeQuestions());
  const batchDelete = question === undefined;

  const onHandleDelete = () => {
    Modal.confirm({
      title:
        question === undefined ? (
          `确定批量删除？`
        ) : (
          <div style={{ display: 'flex' }}>
            确定要删除
            <div
              title={question.title.replace(/<[^>]+>/g, () => '')}
              className={styles.questionTitle}
              style={{ color: `var(--${prefixCls}-warning-color)` }}
            >
              {question.title.replace(/<[^>]+>/g, () => '')}
            </div>
            试题？
          </div>
        ),
      icon: <ExclamationCircleOutlined />,
      content: '删除之后,已应用到该试题的试卷及课程不受影响。',
      okText: '确认',
      cancelText: '取消',
      onOk: () => {
        dispatch(
          deleteQuestionsAction({
            id: question !== undefined ? question.uniqId : undefined,
            callback: res => {
              if (onComplteDelete) {
                onComplteDelete(res, selectedIds);
              }
            },
          })
        );
      },
    });
  };

  return (
    <Button
      disabled={batchDelete && selectedIds.length === 0}
      type={batchDelete ? 'primary' : 'link'}
      danger={batchDelete}
      onClick={onHandleDelete}
      compact={!batchDelete ? true : false}
    >
      {batchDelete ? '批量删除' : '删除'}
    </Button>
  );
}
