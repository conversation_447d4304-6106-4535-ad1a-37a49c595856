import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { QuestionsDeleter } from './questions-deleter';

export const BatchQuestionsDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <QuestionsDeleter />
    </FakeStore>
  </ConfigProvider>
);

export const SigleQuestionsDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <QuestionsDeleter question={{ uniqId: '1_1', title: '试题123', id: 1 }} />
    </FakeStore>
  </ConfigProvider>
);
