import React, { useEffect } from 'react';

import shallowequal from 'shallowequal';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps } from '@manyun/base-ui.ui.cascader';
import { Spin } from '@manyun/base-ui.ui.spin';

import { usePapersCategory } from '@manyun/knowledge-hub.hook.use-papers-category';

export type PapersCategoryProps = {
  style?: React.CSSProperties;
  /** 三级试卷分类 ID */
  categoryCode: number;
};

const unkonwn = { name: '未知', parentId: null };
export function PapersCategory({ style, categoryCode }: PapersCategoryProps) {
  const [{ ids, loading, entities }, { readPaperCategory }] = usePapersCategory();
  const level3 = entities[categoryCode] || unkonwn;
  const level2 = level3?.parentId ? entities[level3.parentId] : unkonwn;
  const level1 = level2?.parentId ? entities[level2.parentId] : unkonwn;

  useEffect(() => {
    readPaperCategory({
      policy: ids => (ids.includes(categoryCode) ? 'cache-only' : 'network-only'),
    });
  }, [categoryCode]);

  const categoryNames = `${level1?.name}/${level2?.name}/${level3?.name}`;
  if (loading) {
    return <Spin spinning={loading} size="small" />;
  }
  return <span title={categoryNames}>{categoryNames}</span>;
}

export type CascaderRef = {
  focus: () => void;
  blur: () => void;
};

export type PapersCategoryCascaderProps = Omit<CascaderProps, 'options'> & {
  /** 三级试卷分类 ID */
  defaultCategoryCode?: number;
  trigger?: 'onDidMount' | 'onFocus';
  defaultValue?: number[];
  value?: number[];
  onMappingCompleted?(value: number[]): void;
};

export const PapersCategoryCascader = React.forwardRef(
  (
    {
      trigger = 'onFocus',
      onFocus,
      defaultValue,
      onMappingCompleted,
      ...props
    }: PapersCategoryCascaderProps,
    ref?: React.Ref<CascaderRef>
  ) => {
    const [{ loading, tree, entities }, { readPaperCategory }] = usePapersCategory(true);
    const { value, ...restProps } = props;
    const [internalValue, setInternalValue] = React.useState<number[] | undefined>(
      value || defaultValue
    );

    const prevValue = usePrevious(value);
    useEffect(() => {
      if (!shallowequal(prevValue, value)) {
        setInternalValue(value);
      }
    }, [prevValue, value]);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        readPaperCategory();
      }
    }, [trigger]);

    // 当只传入了一个分类 ID，则认为传入了三级试卷分类
    // 需要组装完整的三层分类 ID 的数组
    useEffect(() => {
      if (trigger === 'onDidMount') {
        if (
          value?.length === 1 &&
          entities[value[0]] &&
          internalValue?.length === 1 &&
          entities[internalValue[0]]
        ) {
          const defaultCategory = value[0];
          const level3 = entities[defaultCategory];
          const level2 = entities[level3!.parentId] || { id: undefined, parentId: -999 };
          const level1 = entities[level2.parentId] || { id: undefined, parentId: -999 };
          if (level2.id !== undefined && level1.id !== undefined) {
            const newValue: number[] = [level1.id, level2.id, level3!.id];
            if (typeof onMappingCompleted == 'function') {
              onMappingCompleted(newValue);
            } else {
              setInternalValue(newValue);
            }
          }
        }
      }
    }, [value, entities, internalValue, onMappingCompleted]);

    if (loading && trigger == 'onDidMount') {
      return <Spin spinning />;
    }
    return (
      <Cascader
        ref={ref}
        loading={loading}
        options={tree}
        defaultValue={defaultValue}
        value={internalValue}
        getPopupContainer={trigger => trigger.parentNode.parentNode}
        displayRender={label => {
          const len = internalValue?.filter(val => val.toString() != '')?.length || 0;
          if (len > 0) {
            if (len == 3) {
              return <>{label.join('/')}</>;
            } else {
              return <>未知/未知/未知</>;
            }
          } else {
            return undefined;
          }
        }}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readPaperCategory();
          }
          onFocus?.(evt);
        }}
        {...restProps}
      />
    );
  }
);

PapersCategory.Cascader = PapersCategoryCascader;

function usePrevious<T>(value: T) {
  // The ref object is a generic container whose current property is mutable ...
  // ... and can hold any value, similar to an instance property on a class
  const ref = React.useRef<T>();
  // Store current value in ref
  React.useEffect(() => {
    ref.current = value;
  }, [value]); // Only re-run if value changes
  // Return previous value (happens before update in useEffect above)
  return ref.current;
}
