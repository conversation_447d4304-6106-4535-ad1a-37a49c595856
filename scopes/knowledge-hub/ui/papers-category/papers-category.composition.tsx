import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { PapersCategory, PapersCategoryCascader } from './papers-category';

export const BasicPapersCategory = () => {
  const [initialized, update] = React.useState(false);

  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <PapersCategoryCascader
          trigger="onDidMount"
          placeholder=""
          defaultValue={[13]}
          value={[13]}
          style={{ width: 300 }}
        />
      </FakeStore>
    </ConfigProvider>
  );
};

export const TextPapersCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <PapersCategory categoryCode={3} />
    </FakeStore>
  </ConfigProvider>
);
