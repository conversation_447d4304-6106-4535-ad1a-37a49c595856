import { FileTextFilled, FolderFilled, RightOutlined } from '@ant-design/icons';
import EllipsisOutlined from '@ant-design/icons/es/icons/EllipsisOutlined';
import type { TableRowSelection } from 'antd/es/table/interface';
import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { Value } from '@manyun/auth-hub.ui.complex-users-picker';
import { ComplexUsersPicker } from '@manyun/auth-hub.ui.complex-users-picker';
import { User } from '@manyun/auth-hub.ui.user';
import { Breadcrumb } from '@manyun/base-ui.ui.breadcrumb';
import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { Menu } from '@manyun/base-ui.ui.menu';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { useCoursewares } from '@manyun/knowledge-hub.hook.use-coursewares';
import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import { useCoursewaresSelected } from '@manyun/knowledge-hub.hook.use-coursewares-selected';
import { fetchCoursewareAuthorizedUsersWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-courseware-authorized-users';
import { grantCoursewarePermissionsWeb } from '@manyun/knowledge-hub.service.dcexam.grant-courseware-permissions';
import type {
  Role as R,
  ServiceD,
  User as U,
} from '@manyun/knowledge-hub.service.dcexam.grant-courseware-permissions';
import type { Courseware } from '@manyun/knowledge-hub.state.coursewares';
import {
  Variant,
  coursewaresSliceActions,
  selectCoursewareCategory,
  selectCoursewareSelectedIds,
} from '@manyun/knowledge-hub.state.coursewares';
import { CoursewareMutator } from '@manyun/knowledge-hub.ui.courseware-mutator';
import { CoursewareVersionUpdateRecords } from '@manyun/knowledge-hub.ui.courseware-version-update-records';
import { CoursewareCategory, getCategorys } from '@manyun/knowledge-hub.ui.coursewares-category';
import { CoursewaresDeleter } from '@manyun/knowledge-hub.ui.coursewares-deleter';

import { CourseRecordTraining } from './course-record-training';
import { CoursewaresDownloadPermission } from './coursewares-download-permission';

export type CoursewaresProps = {
  variant: Variant;
  categoryCode?: number;
};

const { Text } = Typography;

export function Coursewares({ variant, categoryCode }: CoursewaresProps) {
  const selectedIds = useSelector(selectCoursewareSelectedIds());
  const [setSeletedIds] = useCoursewaresSelected();
  const [getCoursewares, { loading, data }] = useCoursewares({ variant, init: true });
  const [setFields] = useCoursewaresFields(variant);
  const { expandedKeys, ids } = useSelector(selectCoursewareCategory(variant));
  const dispatch = useDispatch();
  const [v, setV] = useState<boolean>(false);
  const [courseItem, setCourseItem] = useState<any>(null);

  useEffect(() => {
    categoryCode && getCoursewares();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryCode]);

  const rowSelection: TableRowSelection<Courseware> | undefined =
    variant === Variant.ALL
      ? {
          selectedRowKeys: selectedIds,
          onChange: ids => {
            setSeletedIds({ selectedIds: ids as string[] });
          },
        }
      : undefined;

  const handleRecordClick = (record: Courseware) => {
    setV(!v);
    setCourseItem(record);
  };

  return (
    <Space direction="vertical" size="middle" style={{ width: '100%' }}>
      {ids.length > 0 && <CategoryBreadcrumb variant={variant} categoryCode={categoryCode} />}
      <Table
        rowKey="groupKey"
        loading={loading}
        scroll={{ x: 'max-content' }}
        columns={getColumns(
          true,
          variant,
          record => {
            setFields({ categoryCode: record.id, name: undefined });
            dispatch(
              coursewaresSliceActions.setExpandedKeys({
                expandedKeys: [...expandedKeys, record.categoryCode!],
                variant,
              })
            );
          },
          handleRecordClick
        )}
        dataSource={data}
        rowSelection={variant === Variant.ALL ? rowSelection : undefined}
      />
      <CourseRecordTraining v={v} setV={setV} courseItem={courseItem} />
    </Space>
  );
}

export function CategoryBreadcrumb({
  variant,
  categoryCode,
  handleClick,
}: {
  variant: Variant;
  categoryCode?: number;
  handleClick?: (categoryCode: number) => void;
}) {
  const [setFields] = useCoursewaresFields(variant);
  const { entities } = useSelector(selectCoursewareCategory(variant));
  const list = getCategorys(entities, categoryCode!);

  return list.length > 1 ? (
    <Breadcrumb separator={<RightOutlined style={{ padding: '0 2px' }} />}>
      {list.map((category, index) => (
        <Breadcrumb.Item key={`breadcrumb_${category.name}`}>
          <Button
            type="link"
            style={style}
            onClick={() => {
              if (handleClick !== undefined) {
                handleClick(Number(category.code));
              } else {
                setFields({ categoryCode: Number(category.code), name: undefined });
              }
            }}
          >
            {index !== 0 && (
              <FolderFilled
                style={{
                  color: 'var(--manyun-primary-color)',
                }}
              />
            )}
            {category.name}
          </Button>
        </Breadcrumb.Item>
      ))}
    </Breadcrumb>
  ) : (
    <></>
  );
}

export function AuthButton({
  fileId,
  categoryCode,
  fileType,
  authType = 'single',
}: {
  fileId?: number;
  categoryCode: number;
  fileType: string;
  authType?: string;
}) {
  const [value, setValue] = useState<Value>({
    roleKeys: [],
    userKeys: [],
  });
  const rolesRef = React.useRef<Record<number, R>>({});
  const usersRef = React.useRef<Record<number, U>>({});
  const selectedIds = useSelector(selectCoursewareSelectedIds());
  const dispatch = useDispatch();

  const grantPermissions = async (value: Value, type: string, id?: number, checked?: boolean) => {
    const userConfig = {
      users: [] as U[],
      roles: [] as R[],
    };
    const q: ServiceD = { userConfig, fileIds: [], folderIds: [] };
    if (id !== undefined) {
      if (type === 'role') {
        rolesRef.current[id].isDownload = checked;
      } else if (type === 'user') {
        usersRef.current[id].isDownload = checked;
      }
      Object.keys(usersRef.current).map(key => {
        const user = usersRef.current[Number(key)];
        return typeof user.isDownload === 'boolean' ? userConfig.users.push(user) : undefined;
      });
      Object.keys(rolesRef.current).map(key => {
        const role = rolesRef.current[Number(key)];
        return typeof role.isDownload === 'boolean' ? userConfig.roles.push(role) : undefined;
      });
    } else {
      userConfig.roles = value.roleKeys!.map(roleKey => rolesRef.current[roleKey]);
      userConfig.users = value.userKeys!.map(userKey => usersRef.current[userKey]);
    }
    if (authType !== 'single') {
      selectedIds.forEach(selectedId => {
        const list = selectedId.split('_');
        list[0] === 'folder'
          ? q.folderIds?.push(Number(list[1]))
          : q.fileIds?.push(Number(list[1]));
      });
    } else if (fileType !== 'folder') {
      q.fileIds?.push(fileId!);
    } else {
      q.folderIds?.push(categoryCode);
    }
    const { error, data } = await grantCoursewarePermissionsWeb(q);
    if (error) {
      message.error(error.message);
      return;
    }

    dispatch(coursewaresSliceActions.updateCoursewares({ coursewares: data.data }));
  };

  return (
    <ComplexUsersPicker
      modalTitle="授权"
      value={value}
      selectedRolesTableExtraColumns={[
        {
          title: '下载权限',
          dataIndex: 'isDownload',
          render: (_: unknown, { id }) => (
            <CoursewaresDownloadPermission
              hasPermission={rolesRef.current[id].isDownload!}
              onChange={checked => {
                grantPermissions(value, 'role', id, checked);
              }}
            />
          ),
        },
      ]}
      selectedUsersTableExtraColumns={[
        {
          title: '下载权限',
          dataIndex: 'isDownload',
          render: (_: unknown, { id }) => (
            <CoursewaresDownloadPermission
              hasPermission={usersRef.current[id].isDownload!}
              onChange={checked => {
                grantPermissions(value, 'user', id, checked);
              }}
            />
          ),
        },
      ]}
      onChange={(value, infos) => {
        if (infos.variant === 'role') {
          infos.keys.forEach(
            key =>
              (rolesRef.current[key] = {
                roleId: key,
                isDownload: infos.selected ? false : undefined,
              })
          );
        } else if (infos.variant === 'user') {
          infos.keys.forEach(
            key =>
              (usersRef.current[key] = {
                userId: key,
                isDownload: infos.selected ? false : undefined,
              })
          );
        }
        grantPermissions(value, infos.variant);
      }}
    >
      <Button
        key="auth"
        disabled={authType !== 'single' && selectedIds.length === 0}
        type={authType === 'single' ? 'link' : 'primary'}
        style={{ ...(authType === 'single' ? style : {}) }}
        onClick={() =>
          new Promise(async resolve => {
            if (authType === 'single') {
              const params = fileType !== 'folder' ? { fileId } : { categoryCode };

              const { data, error } = await fetchCoursewareAuthorizedUsersWeb(params);

              if (error) {
                message.error(error.message);
                resolve(false);
                return;
              }
              const userIds = data?.users.map(user => {
                usersRef.current[user.userId] = user;
                return user.userId;
              });
              const roleIds = data?.roles.map(role => {
                rolesRef.current[role.roleId] = role;
                return role.roleId;
              });

              setValue({
                userKeys: userIds,
                roleKeys: roleIds,
              });
            } else {
              setValue({
                userKeys: [],
                roleKeys: [],
              });
            }
            resolve(true);
          })
        }
      >
        {authType === 'single' ? '授权' : '批量授权'}
      </Button>
    </ComplexUsersPicker>
  );
}

export const getColumns = (
  hasAction: boolean,
  variant: Variant,
  callback: (record: Courseware) => void,
  handleRecordClick: (record: any) => void
) => {
  const action: ColumnType<Courseware>[] = [
    {
      title: '操作',
      fixed: 'right',
      dataIndex: 'action',
      render: (_: string, record: Courseware) => {
        const auth = [
          (variant === Variant.ALL || record.isDownload) && (
            <a
              key="download"
              href={McUploadFile.generateSrc(record.filePath!, record.fileName!)}
              target="_blank"
              rel="noreferrer"
            >
              下载
            </a>
          ),
        ];
        if (record.fileType === 'folder') {
          return variant === Variant.Auth
            ? '--'
            : [
                <AuthButton
                  key="auth"
                  fileId={record.id}
                  categoryCode={Number(record.id)}
                  fileType={record.fileType!}
                />,
                <Divider key="divide" type="vertical" />,
                <CoursewaresDeleter key="delete" groupKey={record.groupKey} />,
              ];
        }
        return variant === Variant.Auth
          ? auth
          : [
              <AuthButton
                key="auth"
                fileId={record.id}
                categoryCode={Number(record.id)}
                fileType={record.fileType!}
              />,
              <Divider key={1} type="vertical" />,
              <CoursewareMutator key="muatte" groupKey={record.groupKey} />,
              <Divider key={2} type="vertical" />,
              ...auth,
              <Divider key={3} type="vertical" />,
              <Dropdown
                key={4}
                overlay={
                  <Menu>
                    <Menu.Item key="hvac-system-topology">
                      <CoursewareVersionUpdateRecords fileId={record.id} />
                    </Menu.Item>
                    <Menu.Item key="delete">
                      <CoursewaresDeleter key="delete" groupKey={record.groupKey} />
                    </Menu.Item>
                  </Menu>
                }
              >
                <EllipsisOutlined style={{ color: 'var(--manyun-primary-color)' }} />
              </Dropdown>,
            ];
      },
    },
  ];

  const basic: (ColumnType<Courseware> & { visible?: boolean })[] = [
    {
      ellipsis: true, //当使用render的时候ellipsis会无用
      title: '课件名称',
      dataIndex: 'fileName',
      render: (text: string, record: Courseware) => {
        if (record.fileType === 'folder') {
          return (
            <Button style={{ padding: 0 }} type="link" onClick={() => callback(record)}>
              <div style={{ display: 'flex', justifyContent: 'left', alignItems: 'center' }}>
                <FolderFilled
                  style={{
                    paddingRight: '6px',
                  }}
                />
                <Text
                  style={{
                    width: 200,
                    color: 'var(--manyun-primary-color)',
                    textAlign: 'left',
                  }}
                  ellipsis={{ tooltip: text }}
                >
                  {text}
                </Text>
              </div>
            </Button>
          );
        }
        return (
          <div style={{ display: 'flex', justifyContent: 'left', alignItems: 'center' }}>
            <FileTextFilled style={{ color: 'var(--manyun-warning-color)', paddingRight: '6px' }} />
            <FilePreviewWithContainer
              key="preview"
              file={{
                src: McUploadFile.generateSrc(record.filePath!, record.fileName!),
                name: record.fileName!,
                ext: record.fileType!.toLocaleLowerCase(),
              }}
              allowDownload={variant === Variant.ALL || record.isDownload}
            >
              <Button type="link" compact>
                <Text
                  style={{ width: 200, color: 'var(--manyun-primary-color)', textAlign: 'left' }}
                  ellipsis={{ tooltip: text }}
                >
                  {text}
                </Text>
              </Button>
            </FilePreviewWithContainer>
          </div>
        );
      },
    },
    {
      title: '课件分类',
      dataIndex: 'categoryCode',
      visible: true,
      ellipsis: true,
      render: (text: string) => (
        <CoursewareCategory categoryCode={Number(text)} variant={variant} />
      ),
    },
    {
      ellipsis: true,
      visible: true,
      title: '文件大小',
      dataIndex: 'fileSize',
      render: (text: string) => (text ? `${(Number(text) / 1024 / 1024).toFixed(2)}MB` : '--'),
    },
    variant === Variant.ALL
      ? {
          ellipsis: true,
          visible: true,
          title: '培训记录',
          dataIndex: 'courseNum',
          render: (text: string, record: Courseware) => {
            return (
              <Button
                type={text ? 'link' : 'text'}
                onClick={text ? () => handleRecordClick(record) : () => null}
              >
                {text || 0}
              </Button>
            );
          },
        }
      : null,
    {
      ellipsis: true,
      visible: true,
      title: '更新人',
      dataIndex: 'operatorName',
      render: (text: string, record: Courseware) => (
        <User.Link id={record.operatorId!} name={text} />
      ),
    },
    {
      title: '更新时间',
      dataIndex: 'gmtModified',
      render: (text: string) => moment(text).format('YYYY-MM-DD HH:mm:ss'),
    },
  ].filter(_ => _);
  return hasAction ? [...basic, ...action] : basic;
};

const style = { padding: 0 };
