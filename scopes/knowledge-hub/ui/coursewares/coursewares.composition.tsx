import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';

import { Coursewares } from './coursewares';

export const AllCoursewares = () => (
  <ConfigProvider>
    <FakeStore>
      <Router>
        <Coursewares variant={Variant.ALL} />
      </Router>
    </FakeStore>
  </ConfigProvider>
);

export const AuthCoursewares = () => (
  <ConfigProvider>
    <FakeStore>
      <Router>
        <Coursewares variant={Variant.Auth} />
      </Router>
    </FakeStore>
  </ConfigProvider>
);
