import React from 'react';

import { Switch } from '@manyun/base-ui.ui.switch';

export type CoursewaresDownloadPermissionProps = {
  hasPermission: boolean;
  onChange: (checked: boolean) => void;
};

export function CoursewaresDownloadPermission({
  hasPermission,
  onChange,
}: CoursewaresDownloadPermissionProps) {
  const [checked, setChecked] = React.useState(hasPermission);

  return (
    <Switch
      checked={checked}
      onChange={checked => {
        setChecked(checked);
        onChange(checked);
      }}
    />
  );
}
