import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { calcCloseExpire } from '@manyun/hrm.ui.user-holiday-balance-card/user-holiday-balance-card';
import { CourseStatus, fetchCoursesWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { CoursesCategory } from '@manyun/knowledge-hub.ui.courses-category';

const { Text } = Typography;

const textMap = {
  [CourseStatus.NORMAL as string]: { label: '正常', color: 'green' },
  [CourseStatus.DISABLE as string]: { label: '禁用', color: 'red' },
};

export function CourseRecordTraining({ v, setV, courseItem }: Record<string, any>) {
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const [courses, setCourses] = useState<Record<string, any>[]>([]);
  const defaultPagination = {
    pageNum: 1,
    pageSize: 10,
  };
  const [pagination, setPagination] = useState<{ pageNum: number; pageSize: number }>(
    defaultPagination
  );

  const modelColumn = [
    {
      title: '课程名称',
      dataIndex: 'name',
      key: 'name',
      width: '210px',
      render: (val: string) => (
        <Text
          style={{
            textAlign: 'left',
          }}
          ellipsis={{ tooltip: val }}
        >
          {val}
        </Text>
      ),
    },
    {
      title: '课程分类',
      width: '210px',
      dataIndex: 'categoryCode',
      key: 'categoryCode',
      render: (txt: string) => <CoursesCategory categoryCode={Number(txt)} />,
    },
    {
      title: '状态',
      width: '100px',
      dataIndex: 'status',
      key: 'status',
      render: (val: string) => (
        <Tag color={val && textMap[val].color}>{val && textMap[val].label}</Tag>
      ),
    },
    {
      title: '培训时间',
      dataIndex: 'startTime',
      key: 'startTime',
      render: (_: any, record: Record<string, any>) => (
        <Text>
          {dayjs(record.startTime)?.format('YYYY-MM-DD HH:mm:ss')} ~
          {dayjs(record.endTime)?.format('YYYY-MM-DD HH:mm:ss')}
        </Text>
      ),
    },
  ];

  useEffect(() => {
    if (v) {
      (async function () {
        setLoading(true);
        const { error, data } = await fetchCoursesWeb({
          variant: 'all',
          fileId: courseItem?.id,
          page: pagination.pageNum,
          pageSize: pagination.pageSize,
        });

        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setCourses(data.data);
        setTotal(data.total ?? 0);
      })();
    }
  }, [v, pagination]);
  return (
    <Modal
      title="培训记录"
      open={v}
      footer={null}
      width="70vw"
      style={{
        maxHeight: '80vh',
        minHeight: '480px',
        maxWidth: '85vw',
        minWidth: '720px',
        overflow: 'hidden',
      }}
      bodyStyle={{
        display: 'flex',
        flexDirection: 'column',
      }}
      onCancel={() => {
        setV(false);
        setPagination(defaultPagination);
      }}
    >
      <Table
        style={{ height: 'calc(100% - 55px)' }}
        loading={loading}
        columns={modelColumn}
        dataSource={courses}
        pagination={{
          total: total,
          pageSize: pagination.pageSize,
          current: pagination.pageNum,
          onChange: (current, size) => {
            setPagination({ pageNum: current, pageSize: size });
          },
        }}
        scroll={{ x: 'max-content', y: 'calc(100vh - 376px)' }}
      />
    </Modal>
  );
}
