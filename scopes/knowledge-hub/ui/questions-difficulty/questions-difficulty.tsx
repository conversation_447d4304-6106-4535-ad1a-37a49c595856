import React, { HTMLAttributes } from 'react';

import type { SelectProps, SelectValue } from 'antd/es/select';

import { Select } from '@manyun/base-ui.ui.select';

import { useQuestionsDifficulty } from '@manyun/knowledge-hub.hook.use-questions-difficulty';

export type QuestionsDifficultyProps = {
  difficultyVal?: number | string;
  colon?: boolean;
} & HTMLAttributes<HTMLDivElement> &
  SelectProps<SelectValue>;

export function SelectQuestionsDifficulty({ ...props }: QuestionsDifficultyProps) {
  const [difficultOptions] = useQuestionsDifficulty(true);

  return (
    <Select
      style={{ width: '100%', ...props.style }}
      options={difficultOptions as []}
      value={props.value}
      onChange={props.onChange}
      defaultValue={props.defaultValue}
      mode={props.mode}
      allowClear={props.allowClear}
    />
  );
}

export function TextQuestionsDifficulty({
  difficultyVal,
  colon = false,
  ...props
}: QuestionsDifficultyProps) {
  const [difficultOptions] = useQuestionsDifficulty(false);

  return (
    <span style={props.style}>
      {difficultyVal && difficultOptions && difficultOptions[difficultyVal as any]}
      {colon && ':'}
    </span>
  );
}
