import React, { useEffect } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { SelectQuestionsDifficulty, TextQuestionsDifficulty } from './questions-difficulty';

// export const testMockQuestions = () => {
//   useEffect(() => {
//     (async function getQuestions() {
//       const { data, error } = await fetchQuestionsWeb({ page: 1, pageSize: 10 });
//     })();
//   }, []);
//   return <div>1</div>;
// };

export const TextQuestionsDifficultyDiv = () => (
  <ConfigProvider>
    <TextQuestionsDifficulty difficultyVal={1} />
  </ConfigProvider>
);

export const SelectQuestionsDifficultyDiv = () => (
  <ConfigProvider>
    <SelectQuestionsDifficulty />
  </ConfigProvider>
);

export const MultipleSelectQuestionsDifficulty = () => (
  <ConfigProvider>
    <SelectQuestionsDifficulty
      mode="multiple"
      style={{ width: 200 }}
      onChange={val => {
        // console.log(val);
      }}
    />
  </ConfigProvider>
);
