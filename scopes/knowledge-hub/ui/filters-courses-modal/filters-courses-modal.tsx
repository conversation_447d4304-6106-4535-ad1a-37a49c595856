import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { generateCourseDetailRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { BackendCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { fetchCoursesWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { TagCoursesState } from '@manyun/knowledge-hub.ui.courses-state';

export type FiltersCoursesModalProps = { courseIdList: number[]; btnText: number };

export function FiltersCoursesModal({ courseIdList, btnText }: FiltersCoursesModalProps) {
  const [courses, setCourses] = useState<BackendCourse[]>([]);

  const [visible, setVisible] = useState(false);
  const [usersLoading, setUsersLoading] = useState(false);
  const [total, setTotal] = useState(0);
  const defaultPagination = {
    pageNum: 1,
    pageSize: 10,
  };
  const [pagination, setPagination] = useState<{ pageNum: number; pageSize: number }>(
    defaultPagination
  );

  const columns = [
    {
      title: '课程名称',
      dataIndex: 'name',
      width: 200,
      ellipsis: true,
      render: (text: string, record: BackendCourse) => (
        <Link to={generateCourseDetailRoutePath(record.id)}>
          <Tooltip placement="topLeft" title={text}>
            {text}
          </Tooltip>
        </Link>
      ),
    },
    {
      ellipsis: true,
      title: '开始时间',
      dataIndex: 'startTime',
      width: 170,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      ellipsis: true,
      title: '结束时间',
      dataIndex: 'endTime',
      width: 170,
      render: (text: string) => dayjs(text).format('YYYY-MM-DD HH:mm'),
    },
    {
      title: '状态',
      dataIndex: 'status',
      render: (text: string) => <TagCoursesState typeVal={text} />,
    },
  ];

  useEffect(() => {
    if (visible && courseIdList) {
      (async function () {
        setUsersLoading(true);
        const { error, data } = await fetchCoursesWeb({
          variant: 'all',
          courseIdList: courseIdList,
          page: pagination.pageNum,
          pageSize: pagination.pageSize,
        });
        setUsersLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        setCourses(data.data);
        setTotal(data.total ?? 0);
      })();
    }
  }, [visible, courseIdList, pagination]);

  return (
    <Space direction="vertical" style={{ width: '100%' }} size="middle">
      <Button
        type="link"
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        {btnText}
      </Button>
      <Modal
        title="上次新建课程记录"
        open={visible}
        style={{
          maxHeight: '80vh',
          minHeight: '480px',
          maxWidth: '85vw',
          minWidth: '720px',
          overflow: 'hidden',
        }}
        bodyStyle={{
          display: 'flex',
          flexDirection: 'column',
        }}
        destroyOnClose
        footer={null}
        onCancel={() => {
          setVisible(false);
          setPagination(defaultPagination);
        }}
      >
        <div style={{ flex: 1 }}>
          <Table
            loading={usersLoading}
            style={{ height: 'calc(100% - 55px)' }}
            columns={[...columns]}
            scroll={{ y: 400 }}
            dataSource={courses}
            pagination={{
              total: total,
              pageSize: pagination.pageSize,
              current: pagination.pageNum,
              onChange: (current, size) => {
                setPagination({ pageNum: current, pageSize: size });
              },
            }}
          />
        </div>
      </Modal>
    </Space>
  );
}
