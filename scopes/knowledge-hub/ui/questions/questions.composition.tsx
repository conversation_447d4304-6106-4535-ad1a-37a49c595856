import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Divider } from '@manyun/base-ui.ui.divider';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { QuestionUpdator } from '@manyun/knowledge-hub.ui.question-updator';
import { QuestionsDeleter } from '@manyun/knowledge-hub.ui.questions-deleter';

import { Questions } from './questions';

export const BasicQuestions = () => (
  <ConfigProvider>
    <FakeStore>
      <Questions
        operateColumn={{
          column: {
            title: '操作',
            width: 150,
            render: (text, record) => {
              return (
                <span>
                  <QuestionsDeleter question={record} />
                  <Divider type="vertical" />
                  <QuestionUpdator question={record} />
                </span>
              );
            },
          },
        }}
      />
    </FakeStore>
  </ConfigProvider>
);

export const ModalQuestions = () => (
  <ConfigProvider>
    <FakeStore>
      <Questions
        modalVariant={{
          data: {
            data: [
              {
                id: 1,
                questionType: '1',
                type: 1,
                categoryCode: '1',
                difficulty: 1,
                title: '1',
                autoGrade: true,
                gmtModified: 1632900356000,
                modifierName: 'admin',
                modifierId: 1,
              },
              {
                id: 2,
                questionType: '1',
                type: 1,
                categoryCode: '1',
                difficulty: 1,
                title: '1',
                autoGrade: true,
                gmtModified: 1632900356000,
                modifierName: 'admin',
                modifierId: 1,
              },
              {
                id: 3,
                questionType: '1',
                type: 1,
                categoryCode: '1',
                difficulty: 1,
                title: '1',
                autoGrade: true,
                gmtModified: 1632900356000,
                modifierName: 'admin',
                modifierId: 1,
              },
            ] as any,
            total: 3,
          },
          page: 1,
          pageSize: 10,
          loading: false,
          pageHandler: () => {},
        }}
        operateColumn={{
          position: 'start',
          column: {
            title: '操作',
            width: 60,
            render: (text, record) => {
              return <span>+</span>;
            },
          },
        }}
      />
    </FakeStore>
  </ConfigProvider>
);
