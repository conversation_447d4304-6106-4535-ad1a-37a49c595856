import React, { useCallback, useState } from 'react';
import { <PERSON> } from 'react-router-dom';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateExamRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchQuestionExams } from '@manyun/knowledge-hub.service.dcexam.fetch-question-exams';
import type { BackendQuestionExam } from '@manyun/knowledge-hub.service.dcexam.fetch-question-exams';
import { ExamsCategory } from '@manyun/knowledge-hub.ui.exams-category';
import { ExamsState } from '@manyun/knowledge-hub.ui.exams-state';

const columns: ColumnType<BackendQuestionExam>[] = [
  {
    title: '考试名称',
    dataIndex: 'name',
    render(name, { id }) {
      return (
        <Typography.Text style={{ maxWidth: 350 }} ellipsis={{ tooltip: name }}>
          <Link to={generateExamRoutePath(id)}>{name}</Link>
        </Typography.Text>
      );
    },
  },
  {
    title: '考试分类',
    dataIndex: 'categoryCode',
    render(categoryCode: string) {
      return <ExamsCategory categoryCode={Number(categoryCode)} />;
    },
  },

  {
    title: '状态',
    width: 80,
    dataIndex: 'status',
    render(status) {
      return <ExamsState variant="tag" examState={status} />;
    },
  },
  {
    title: '考试时间',
    dataIndex: '_time',
    render(_, { startTime, endTime }) {
      const formatStyle = 'YYYY-MM-DD HH:mm';

      return `${dayjs(startTime).format(formatStyle)}~${dayjs(endTime).format(formatStyle)}`;
    },
  },
  {
    title: '正确率',
    width: 100,
    dataIndex: 'correctRate',
    render(_, { questionCorrectCount, questionTotalCount }) {
      return questionTotalCount === 0
        ? '--'
        : `${((questionCorrectCount / questionTotalCount) * 100).toFixed(2)}%`;
    },
  },
];

export function ExamRecordsModalButton({
  questionId,
  examCount,
}: {
  questionId: number;
  examCount: number;
}) {
  const defaultPagination = {
    pageNum: 1,
    pageSize: 10,
  };
  const [open, setOpen] = useState(false);
  const [questionExams, setQuestionExams] = useState<BackendQuestionExam[]>();
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState<{ pageNum: number; pageSize: number }>(
    defaultPagination
  );
  const [total, setTotal] = useState<number>(0);

  const showModal = () => {
    setOpen(true);
    fetchExams(pagination.pageNum, pagination.pageSize);
  };
  const closeModal = () => {
    setOpen(false);
    setPagination(defaultPagination);
  };

  const fetchExams = useCallback(
    async (pageNum: number, pageSize: number) => {
      setLoading(true);
      const { error, data } = await fetchQuestionExams({
        questionId,
        pageNum,
        pageSize,
      });

      if (error) {
        message.error(error.message);
        return;
      }
      setPagination({ pageNum, pageSize });
      setTotal(data.total);
      setQuestionExams(data.data);

      setLoading(false);
    },
    [questionId]
  );

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          showModal();
        }}
      >
        {examCount}
      </Button>
      <Modal
        width="85%"
        bodyStyle={{ maxHeight: 'calc(80vh - 109px)', overflowY: 'auto' }}
        title="考试记录"
        open={open}
        footer={null}
        destroyOnClose
        onCancel={() => {
          closeModal();
        }}
        onOk={() => {
          closeModal();
        }}
      >
        <Table
          style={{ width: '100%' }}
          loading={loading}
          columns={columns}
          dataSource={questionExams}
          // scroll={{ x: 'max-content' }}
          pagination={{
            total: total,
            pageSize: pagination.pageSize,
            current: pagination.pageNum,
            onChange: (current, size) => {
              fetchExams(current, size);
            },
          }}
        />
      </Modal>
    </>
  );
}
