import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import FileTextFilled from '@ant-design/icons/es/icons/FileTextFilled';
import FolderFilled from '@ant-design/icons/es/icons/FolderFilled';
import type { ColumnsType } from 'antd/es/table';
import type { ColumnType, TableRowSelection } from 'antd/es/table/interface';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { User } from '@manyun/auth-hub.ui.user';
import { useQuestions } from '@manyun/knowledge-hub.hook.use-questions';
import { useQuestionsFields } from '@manyun/knowledge-hub.hook.use-questions-fields';
import type { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { questionsSliceActions } from '@manyun/knowledge-hub.state.questions';
import { TextQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { QuestionsCategory } from '@manyun/knowledge-hub.ui.questions-category';
import { TextQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';

import { ExamRecordsModalButton } from './components/exam-records-modal-button';
import styles from './questions.module.less';

export type QuestionsProps = {
  modalVariant?: {
    data: { data: any[]; total: number };
    loading: boolean;
    page: number | undefined;
    pageSize: number | undefined;
    pageHandler: (page?: number, pageSize?: number) => void | undefined;
  };
  operateColumn?: {
    position?: 'start' | 'end';
    column: ColumnType<any>;
  };
  showIcon?: boolean;
  needInitial?: boolean /** 是否需要 初始化数据 */;
  rowSelection?: TableRowSelection<BackendQuestion>;
  onRowClick?: (record: BackendQuestion) => void;
};

export function Questions({
  modalVariant,
  operateColumn,
  rowSelection,
  needInitial = false,
  onRowClick,
}: QuestionsProps) {
  const dispatch = useDispatch();
  const [setFields] = useQuestionsFields();
  const [columns, setColumns] = useState();
  const [getQuestions, { loading, data, fields, selectedIds }] = useQuestions();

  const dataSource = modalVariant ? modalVariant.data.data : data.data;
  const total = modalVariant ? modalVariant.data.total : data.total;
  const page = modalVariant ? modalVariant.page : fields.page;
  const pageSize = modalVariant ? modalVariant.pageSize : fields.pageSize;
  const tableLoading = modalVariant ? modalVariant.loading : loading;

  const setSelectedIds = useCallback(
    (selectedIds: string[]) => {
      dispatch(questionsSliceActions.setSelectedIds({ selectedIds }));
    },
    [dispatch]
  );

  const rowSelections: TableRowSelection<BackendQuestion> | undefined =
    modalVariant === undefined
      ? {
          selectedRowKeys: selectedIds,
          onChange: selectedRowKeys => {
            setSelectedIds(selectedRowKeys as string[]);
          },
          getCheckboxProps: record => ({
            disabled: record.categoryCode === '0',
          }) /**配置无法勾选的列 全量无法选中 */,
        }
      : rowSelection;

  const pageHandler = modalVariant
    ? modalVariant.pageHandler
    : // eslint-disable-next-line react-hooks/rules-of-hooks
      useCallback(
        (_page: number, _pageSize?: number) => {
          if (selectedIds.length > 0) {
            setSelectedIds([]);
          }
          setFields({ page: _page, pageSize: _pageSize });
          getQuestions();
        },
        [selectedIds, setSelectedIds, getQuestions, setFields]
      );

  useEffect(() => {
    if (modalVariant === undefined && needInitial) {
      getQuestions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getBaseColumns = (visible: boolean) => {
    const baseColumns: ColumnsType<BackendQuestion> | undefined | any[] = [
      {
        title: '试题内容',
        dataIndex: 'title',
        width: 288,
        visible: true,
        render: (_, { title, questionType, id }, index: number) => {
          return (
            <Space
              direction="horizontal"
              style={{ marginLeft: questionType === '0' ? '0' : '5px' }}
            >
              {!visible &&
                (questionType === '0' ? (
                  <FolderFilled
                    style={{
                      color: 'var(--manyun-primary-color)',
                      marginLeft: index !== 0 ? '5px' : 0,
                    }}
                  />
                ) : (
                  <FileTextFilled
                    style={{ color: 'var(--manyun-warning-color-active)', marginLeft: '5px' }}
                  />
                ))}
              {questionType === '1' && (
                <div className={styles.questionTitle} title={title.replace(/<[^>]+>/g, () => '')}>
                  {title.replace(/<[^>]+>/g, () => '')}
                </div>
              )}
              {questionType === '0' && (
                <Button type="link" disabled={fields.categoryCode === id.toString()} compact>
                  {title}
                </Button>
              )}
            </Space>
          );
        },
      },
      {
        title: '题型',
        dataIndex: 'type',
        ellipsis: true,
        width: 80,
        visible: true,
        render: (text, { questionType }) =>
          questionType === '1' ? <TextQuestionType typeVal={text} /> : '--',
      },
      {
        title: '试题分类',
        dataIndex: 'categoryCode',
        visible: true,
        ellipsis: true,
        render: (text: string, { questionType }) =>
          questionType === '1' ? (
            <QuestionsCategory categoryCode={Number(text)} showRoot={false} />
          ) : (
            '--'
          ),
      },
      {
        title: '试题难度',
        dataIndex: 'difficulty',
        visible: true,
        ellipsis: true,
        width: 90,
        render: (text, { questionType }) =>
          questionType === '1' ? <TextQuestionsDifficulty difficultyVal={text} /> : '--',
      },
      {
        title: '考试记录',
        dataIndex: 'examCount',
        visible: !visible,
        // width: 200,
        render: (text, { examCount, id }) =>
          text === 0 ? 0 : <ExamRecordsModalButton examCount={examCount} questionId={id} />,
      },
      {
        title: '正确率',
        dataIndex: 'correctCount',
        visible: !visible,
        // width: 200,
        render: (text, { totalCount }) =>
          totalCount === 0 ? '--' : `${((text / totalCount) * 100).toFixed(0)}%`,
      },
      {
        title: '创建时间',
        dataIndex: 'gmtModified',
        visible: visible,
        ellipsis: true,
        width: 200,
        render: text => <span>{text > 0 && moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
      {
        title: '更新人',
        dataIndex: 'modifierName',
        visible: !visible,
        render(name, { modifierId }) {
          return <User.Link id={modifierId!} name={name} />;
        },
      },
      {
        title: '更新时间',
        dataIndex: 'gmtModified',
        width: 200,
        visible: !visible,
        ellipsis: true,
        render: text => <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>,
      },
    ];
    return baseColumns.filter(colum => colum.visible === true);
  };

  useEffect(() => {
    const columnsArr = getBaseColumns(modalVariant === undefined ? false : true)?.concat();
    if (operateColumn !== undefined) {
      const { position = 'end', column } = operateColumn;
      if (position === 'start') {
        columnsArr?.unshift(column);
      } else {
        columnsArr?.push(column);
      }
    }
    setColumns(columnsArr as any);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields.categoryCode]);

  return (
    <Table
      rowKey={record => `${record.id}_${record.questionType}`}
      loading={tableLoading}
      columns={columns}
      scroll={{ x: 'max-content' }}
      dataSource={dataSource}
      pagination={{
        showQuickJumper: true,
        showTotal: () => `共 ${total} 条`,
        showSizeChanger: true,
        pageSizeOptions: ['10', '20', '30', '50', '100', '200'],
        current: page,
        pageSize,
        total: total!,
        onChange: pageHandler,
      }}
      rowSelection={rowSelections}
      onRow={(record: BackendQuestion) => {
        return {
          onClick: event => {
            if (onRowClick) {
              onRowClick(record);
            }
          },
        };
      }}
    />
  );
}
