import React from 'react';

import type { RadioGroupProps } from 'antd/es/radio';
import type { SelectValue } from 'antd/es/select';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Select, SelectProps } from '@manyun/base-ui.ui.select';

import { usePapersGenFunc } from '@manyun/knowledge-hub.hook.use-papers-gen-func';

export type PaperGenFuncProps = {
  variant: 'select' | 'text' | 'radio';
  genFuncVal?: string;
} & SelectProps<SelectValue> &
  RadioGroupProps;

export function PaperGenFunc({ variant, genFuncVal, ...props }: PaperGenFuncProps) {
  const [genFuncOptions] = usePapersGenFunc(variant != 'text');
  if (variant === 'text') {
    return (
      <span>{genFuncVal && genFuncOptions && (genFuncOptions as any)[genFuncVal as any]}</span>
    );
  } else if (variant === 'radio') {
    const radioProps = { ...props, options: genFuncOptions as any };
    return <Radio.Group {...radioProps} />;
  } else {
    const selectProps = { ...props, options: genFuncOptions as any };
    return <Select {...selectProps} />;
  }
}
