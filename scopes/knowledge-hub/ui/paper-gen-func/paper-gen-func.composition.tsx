import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { PaperGenFunc } from './paper-gen-func';

export const TextPaperGenFunc = () => (
  <ConfigProvider>
    <FakeStore>
      <PaperGenFunc variant="text" genFuncVal={'SELECT'} />
    </FakeStore>
  </ConfigProvider>
);

export const RadioPaperGenFunc = () => (
  <ConfigProvider>
    <FakeStore>
      <PaperGenFunc
        variant="radio"
        defaultValue={'SELECT'}
        onChange={e => {
          // console.log((e as any).target.value);
        }}
      />
    </FakeStore>
  </ConfigProvider>
);

export const MultiplePaperGenFunc = () => (
  <ConfigProvider>
    <FakeStore>
      <PaperGenFunc variant="select" mode="multiple" style={{ width: 200 }} />
    </FakeStore>
  </ConfigProvider>
);

export const SelectPaperGenFunc = () => (
  <ConfigProvider>
    <FakeStore>
      <PaperGenFunc variant="select" style={{ width: 100 }} />
    </FakeStore>
  </ConfigProvider>
);
