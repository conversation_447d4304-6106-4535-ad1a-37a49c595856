import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { QuestionEditor } from './question-editor';

export const BatchQuestionsDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <QuestionEditor
        question={{
          id: 1,
          title: '',
          type: 1,
          categoryCode: 1,
          analysis: '',
          answers: [],
          options: [],
          difficulty: 1,
          autoGrade: true,
        }}
      />
    </FakeStore>
  </ConfigProvider>
);
