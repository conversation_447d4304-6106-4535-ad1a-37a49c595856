import React, {
  forwardRef,
  useCallback,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from 'react';

import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import { FormInstance } from 'antd/es/form';
import uniqBy from 'lodash.uniqby';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';

import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { SYNONYM_SPLIT } from '@manyun/knowledge-hub.ui.question';
import { SelectQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { SelectTreeQuestionCategory } from '@manyun/knowledge-hub.ui.questions-category';
import { SelectQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';
import { RichEditor, RichEditorRef } from '@manyun/knowledge-hub.ui.rich-editor';

import styles from './question-editor.module.less';

export type QuestionEditorRef = React.Ref<
  FormInstance<any> & {
    initEditor?: () => void;
  }
>;

export type QuestionEditorProps = {
  question?: BackendQuestion;
  type?: number;
  autoInit?: boolean;
  disabledFields?: ('type' | 'autoGrade')[];
  onEditorValuesChange?: (val: any) => void;
  onEditorClearSure?: (val?: any) => void;
};

const { Option } = Select;

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 2 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 22 },
  },
};

const formOptionItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 12 },
  },
  wrapperCol: {
    xs: { span: 12 },
    sm: { span: 24 },
  },
};

const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 22, offset: 2 },
  },
};

export const QuestionEditor = forwardRef(
  (
    {
      question,
      onEditorValuesChange,
      onEditorClearSure,
      autoInit = false,
      disabledFields,
    }: QuestionEditorProps,
    ref: QuestionEditorRef
  ) => {
    const [form] = Form.useForm();
    const [type, setType] = useState<number>(question?.type || 1);
    let richEditorRef = useRef<RichEditorRef>(null);

    useImperativeHandle(ref, () => ({
      ...form,
      initEditor: () => {
        initEditor();
      },
    }));

    useEffect(() => {
      /**自动初始化 */
      if (autoInit) {
        if (question?.title && richEditorRef.current) {
          const safeHtml = question?.title;
          richEditorRef.current.setRichEditorTxt(`<p>${safeHtml}</p>`);
        }
        if (question) setType(question.type);
      }
    }, []);

    const initEditor = () => {
      if (richEditorRef.current && question) {
        richEditorRef.current.setRichEditorTxt(`<p>${question?.title}</p>`);
      }
      if (question) setType(question.type);
    };

    const getAnswersInOptionsIndexs = useCallback(() => {
      const indexs: number[] = [];
      if (question && question.answers) {
        (question.answers || []).forEach(answer => {
          const index = question.options.findIndex(o => o == answer);
          if (index > -1) indexs.push(index);
        });
      }
      return indexs;
    }, [question]);

    const getAnswersInOptionsIndex = () => {
      const answer = question?.answers && question?.answers.length > 0 && question?.answers[0];
      if (answer) {
        return question.options.findIndex(o => o == answer);
      }
      return -1;
    };

    /**填空题 回车 添加同义词 */
    const onKeyDown = useCallback((e: any, index: number) => {
      const keycode = e.keyCode;
      if (keycode == 13) {
        const answers = form.getFieldValue('answers');
        let selectedAnswer = (answers[index] && answers[index].answer) || '';
        const regex = /\&\&$/;
        if (selectedAnswer && selectedAnswer != '' && !regex.test(selectedAnswer)) {
          answers[index].answer = selectedAnswer + SYNONYM_SPLIT;
          form.setFieldsValue({ answers });
        }
      }
    }, []);

    /**题型切换 */
    const onChangeType = useCallback(
      (val: any) => {
        /**从单/多选题 切换至单/多选题  或 问答题、判断题、填空题 直接切换 */
        if (
          [1, 2].includes(type) &&
          [3, 4, 5].includes(val) &&
          form.getFieldValue('options').length > 0
        ) {
          onConfirmChangeType({
            onOK: () => {
              setType(pre => {
                pre = val;
                return pre;
              });
              form.setFieldsValue({ answers: [], options: [] });
              if (onEditorClearSure) {
                onEditorClearSure(val);
              }
            },
            onCancle: () => {
              setType(pre => {
                return pre;
              });
              form.setFieldsValue({ type: type });
            },
          });
        } else {
          form.setFieldsValue({ answers: [] });
          setType(val);
        }
      },
      [type]
    );

    /**替换confirm */
    const onConfirmChangeType = useCallback(
      ({ onOK, onCancle }: { onOK: () => void; onCancle: () => void }) => {
        Modal.confirm({
          title: '所改试题类型与当前类型不同，将清除输入选项内容，是否确认修改？',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            onOK();
          },
          onCancel: () => {
            onCancle();
          },
        });
      },
      []
    );

    return (
      <Form
        {...formItemLayout}
        colon={false}
        ref={ref}
        form={form}
        className={styles.questionEditorContainer}
        onValuesChange={values => {
          if (onEditorValuesChange) onEditorValuesChange(values);
        }}
      >
        {question?.categoryCode && (
          <Form.Item
            label="试题分类"
            name="categoryCode"
            rules={[{ required: true, message: '请选择试题分类!' }]}
            initialValue={Number(question.categoryCode)}
          >
            <SelectTreeQuestionCategory trigger="onDidMount" style={{ width: 260 }} />
          </Form.Item>
        )}
        <Form.Item wrapperCol={{ offset: 2 }} noStyle>
          <div style={{ width: '100%', display: 'flex', alignItems: 'baseline' }}>
            <Form.Item
              labelCol={{ span: 6 }}
              style={{ flex: 1 }}
              label="试题难度"
              name="difficulty"
              rules={[{ required: true, message: '请选择试题难度!' }]}
              initialValue={question?.difficulty}
            >
              <SelectQuestionsDifficulty style={{ width: '80%' }} allowClear={false} />
            </Form.Item>
            <Form.Item
              label="试题类型"
              labelCol={{ span: 8 }}
              wrapperCol={{ span: 15 }}
              style={{ flex: 1 }}
              name="type"
              rules={[{ required: true, message: '请选择试题类型!' }]}
              initialValue={question?.type}
            >
              <SelectQuestionType
                value={type}
                style={{ width: '80%' }}
                disabled={(disabledFields || []).includes('type')}
                onChange={val => {
                  onChangeType(val);
                }}
                allowClear={false}
              />
            </Form.Item>
            <div style={{ flex: 1 }}>
              {type == 4 && (
                <Form.Item
                  label="判题方式"
                  labelCol={{ span: 10 }}
                  wrapperCol={{ span: 14 }}
                  style={{ flex: 1 }}
                  name="autoGrade"
                  rules={[{ required: true, message: '请选择判题方式!' }]}
                  initialValue={
                    typeof question?.autoGrade == 'number'
                      ? question?.autoGrade
                      : question?.autoGrade == true
                      ? 1
                      : 0
                  }
                >
                  <Select
                    style={{ width: '100%' }}
                    allowClear={false}
                    disabled={(disabledFields || []).includes('autoGrade')}
                  >
                    <Option value={1}>系统判题</Option>
                    <Option value={0}>人工判题</Option>
                  </Select>
                </Form.Item>
              )}
            </div>
          </div>
        </Form.Item>
        <Form.Item
          label="试题题目"
          name="title"
          rules={[
            {
              required: true,
              validator: async (_, value) => {
                /**自定义校验  */
                const txt = value.replace(/<[^>]+>/g, () => '');
                if (value == '') {
                  return Promise.reject('请填写试题题目!');
                } else if (txt == '') {
                  return Promise.reject('题干文本内容不应为空!');
                } else {
                  return Promise.resolve();
                }
              },
            },
          ]}
          initialValue={question?.title}
        >
          <RichEditor placeholder="请填写题目描述" ref={richEditorRef} />
        </Form.Item>
        {/* 单选题 */}
        {type == 1 && (
          <Form.Item
            name="answers"
            className={styles.answersItem}
            initialValue={getAnswersInOptionsIndex()}
            validateStatus="success"
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (value < 0) {
                    return Promise.reject('请选择答案！');
                  } else {
                    return Promise.resolve();
                  }
                },
              },
            ]}
            {...formOptionItemLayout}
          >
            <Radio.Group style={{ width: '100%' }}>
              <Form.List
                name={'options'}
                initialValue={question?.options.map(o => ({ option: o }))}
                rules={[
                  {
                    validator: async (_, names) => {
                      if (!names || names.length < 2) {
                        return Promise.reject(new Error('至少两项选项！'));
                      }
                    },
                  },
                ]}
              >
                {(fields, { add, remove }, { errors }) => (
                  <>
                    {fields.map(({ key, name, fieldKey, ...restField }) => (
                      <Form.Item
                        {...(key === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                        label={key === 0 ? '试题选项' : ''}
                        required={true}
                        key={key}
                      >
                        <div
                          style={{
                            width: '100%',
                            display: 'flex',
                            alignItems: 'baseline',
                          }}
                        >
                          <Form.Item {...restField}>
                            <Radio value={name} />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            fieldKey={[fieldKey, 'option']}
                            name={[name, 'option']}
                            style={{ width: '100%' }}
                            rules={[
                              {
                                required: true,
                                whitespace: true,
                                validator: (_, value) => {
                                  const options = form.getFieldValue('options');
                                  const tmp = uniqBy(options, 'option');
                                  if (
                                    (value || '').trim() == '' ||
                                    value == null ||
                                    value == undefined
                                  ) {
                                    return Promise.reject(new Error('请输入选项内容'));
                                  } else if (tmp.length != options.length) {
                                    return Promise.reject(new Error('选项不能重复'));
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },
                            ]}
                          >
                            <Input placeholder="请填写题目选项内容" style={{ width: '100%' }} />
                          </Form.Item>
                          {fields.length > 1 ? (
                            <MinusCircleOutlined
                              className="dynamic-delete-button"
                              onClick={() => {
                                /** 如果删除的是已选中的选项 则清空答案 */
                                const answers: number = form.getFieldValue('answers');
                                if (answers == name) {
                                  form.setFieldsValue({ answers: -1 });
                                }
                                remove(name);
                              }}
                            />
                          ) : null}
                        </div>
                      </Form.Item>
                    ))}
                    <Form.Item {...formItemLayoutWithOutLabel}>
                      <Button
                        type="primary"
                        onClick={() => add()}
                        style={{ width: '100%' }}
                        icon={<PlusOutlined />}
                        disabled={fields.length > 7}
                      >
                        添加选项(单选题的选项范围从 2 到 8)
                      </Button>
                      <Form.ErrorList errors={errors} />
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Radio.Group>
          </Form.Item>
        )}

        {/* 多选题 */}
        {type == 2 && (
          <Form.Item
            name="answers"
            className={styles.answersItem}
            initialValue={getAnswersInOptionsIndexs()}
            validateStatus="success"
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if ((value || []).length == 0) {
                    return Promise.reject('请选择答案！');
                  } else {
                    return Promise.resolve();
                  }
                },
              },
            ]}
            {...formOptionItemLayout}
          >
            <Checkbox.Group style={{ width: '100%' }}>
              <Form.List
                name={'options'}
                initialValue={question?.options.map(o => ({ option: o, ansewer: false }))}
                rules={[
                  {
                    validator: async (_, names) => {
                      if (!names || names.length < 2) {
                        return Promise.reject(new Error('至少两项选项！'));
                      }
                    },
                  },
                ]}
              >
                {(fields, { add, remove }, { errors }) => (
                  <>
                    {fields.map(({ key, name, fieldKey, ...restField }) => (
                      <Form.Item
                        {...(key === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                        label={key === 0 ? '试题选项' : ''}
                        required={true}
                        key={key}
                      >
                        <div style={{ width: '100%', display: 'flex', alignItems: 'baseline' }}>
                          <Form.Item {...restField}>
                            <Checkbox value={name} />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            fieldKey={[fieldKey, 'option']}
                            name={[name, 'option']}
                            required={true}
                            style={{ width: '100%', margin: '0 8px' }}
                            validateTrigger={['onChange', 'onBlur']}
                            rules={[
                              {
                                required: true,
                                whitespace: true,
                                validator: (_, value) => {
                                  const options = form.getFieldValue('options');
                                  const tmp = uniqBy(options, 'option');
                                  if (
                                    (value || '').trim() == '' ||
                                    value == null ||
                                    value == undefined
                                  ) {
                                    return Promise.reject('请输入选项内容');
                                  } else if (tmp.length != options.length) {
                                    return Promise.reject('选项不能重复');
                                  } else {
                                    return Promise.resolve();
                                  }
                                },
                              },
                            ]}
                          >
                            <Input placeholder="" style={{ width: '100%' }} />
                          </Form.Item>
                          {fields.length > 1 ? (
                            <MinusCircleOutlined
                              className="dynamic-delete-button"
                              onClick={() => {
                                let answers: number[] = form.getFieldValue('answers');
                                answers.splice(name, 1);
                                form.setFieldsValue({ answers });
                                remove(name);
                              }}
                            />
                          ) : null}
                        </div>
                      </Form.Item>
                    ))}
                    <Form.Item {...formItemLayoutWithOutLabel}>
                      <Button
                        type="primary"
                        onClick={() => add()}
                        style={{ width: '100%' }}
                        icon={<PlusOutlined />}
                        disabled={fields.length > 7}
                      >
                        添加选项(多选题的选项范围从 2 到 8)
                      </Button>
                      <Form.ErrorList errors={errors} />
                    </Form.Item>
                  </>
                )}
              </Form.List>
            </Checkbox.Group>
          </Form.Item>
        )}

        {/* 填空题 */}
        {type == 4 && (
          <Form.List
            name="answers"
            initialValue={question?.answers.map(o => ({ answer: o }))}
            rules={[
              {
                validator: async (_, names) => {
                  if (!names || names.length < 1) {
                    return Promise.reject(new Error('至少一项选项！'));
                  }
                },
              },
            ]}
          >
            {(fields, { add, remove }, { errors }) => (
              <>
                {fields.map(({ key, name, fieldKey, ...restField }) => (
                  <Form.Item
                    {...(key === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                    label={key === 0 ? '试题选项' : ''}
                    required={true}
                    key={key}
                  >
                    <div style={{ width: '100%', display: 'flex', alignItems: 'baseline' }}>
                      <Form.Item
                        {...restField}
                        fieldKey={[fieldKey, 'answer']}
                        name={[name, 'answer']}
                        required={true}
                        style={{ width: '100%' }}
                        rules={[
                          {
                            required: true,
                            whitespace: true,
                            message: '请输入选项内容',
                          },
                        ]}
                      >
                        <Input
                          placeholder="请填写试题选项内容"
                          style={{ width: '100%' }}
                          onKeyDown={e => {
                            onKeyDown(e, key);
                          }}
                        />
                      </Form.Item>
                      {fields.length > 1 ? (
                        <MinusCircleOutlined
                          className="dynamic-delete-button"
                          onClick={() => remove(name)}
                        />
                      ) : null}
                    </div>
                  </Form.Item>
                ))}
                <Form.Item {...formItemLayoutWithOutLabel}>
                  <Button
                    type="primary"
                    onClick={() => add()}
                    style={{ width: '100%' }}
                    icon={<PlusOutlined />}
                    disabled={fields.length > 7}
                  >
                    添加选项(填空题的选项范围从 1 到 8)
                  </Button>
                  <Form.ErrorList errors={errors} />
                </Form.Item>
              </>
            )}
          </Form.List>
        )}

        {/* 判断题 */}
        {type == 3 && (
          <Form.Item
            label="试题选项"
            name="answers"
            rules={[{ required: true, message: '请选择试题选项!' }]}
            initialValue={question?.answers[0]}
          >
            <Radio.Group buttonStyle="solid">
              <Radio.Button value={'true'}>正确</Radio.Button>
              <Radio.Button value={'false'}>错误</Radio.Button>
            </Radio.Group>
          </Form.Item>
        )}

        <Form.Item label="试题解析" name="analysis" initialValue={question?.analysis}>
          <Input.TextArea placeholder="请填写该试题的答案解释" rows={4} />
        </Form.Item>
      </Form>
    );
  }
);
