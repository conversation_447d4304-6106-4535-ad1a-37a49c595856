import React from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { useSkills, useSkillsFileds } from '@manyun/knowledge-hub.hook.use-skills';
import { skillsSliceActions } from '@manyun/knowledge-hub.state.skills';
import { SkillsCategoryCascader } from '@manyun/knowledge-hub.ui.skills-category';
import { SkillsLevel } from '@manyun/knowledge-hub.ui.skills-level';

export type SkillsFiltersProps = {};

export function SkillsFilters({}: SkillsFiltersProps) {
  const [getPapers, { loading }] = useSkills();
  const [setFields] = useSkillsFileds();

  const [form] = Form.useForm();
  const dispatch = useDispatch();

  return (
    <Form colon={false} form={form} layout="inline">
      <Form.Item label="技能分类" name="categoryCode">
        <SkillsCategoryCascader style={{ width: 330 }} />
      </Form.Item>
      <Form.Item label="技能等级" name="level">
        <SkillsLevel allowClear />
      </Form.Item>
      <Form.Item label="技能名称" name="name">
        <Input style={{ width: 200 }} allowClear placeholder="请输入技能名称" />
      </Form.Item>
      <Form.Item label=" ">
        <Space>
          <Button
            type="primary"
            onClick={() => {
              const values = form.getFieldsValue();
              setFields(values);
              getPapers();
            }}
            loading={loading}
          >
            搜索
          </Button>
          <Button
            onClick={() => {
              form.resetFields();
              setFields({});
              dispatch(skillsSliceActions.resetFields());
              getPapers();
            }}
            disabled={loading}
          >
            重置
          </Button>
        </Space>
      </Form.Item>
    </Form>
  );
}
