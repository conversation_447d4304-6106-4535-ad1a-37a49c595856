import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { BackendMarkingWay } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks as getPapersMock } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
// import { registerWebMocks as getPaperMock } from '@manyun/knowledge-hub.service.dcexam.fetch-paper';
// import { registerWebMocks as getCategoryMock } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { PapersPicker } from './papers-picker';

export const BasicPapersPicker = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // getPapersMock(mock);
    // getPaperMock(mock);
    // getCategoryMock(mock);
    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <PapersPicker
          defaultId={29}
          id={29}
          markingWay={BackendMarkingWay.AUTOGRADE}
          onPick={values => {
            // console.log(values);
          }}
        />
      </FakeStore>
    </ConfigProvider>
  );
};
