import EditOutlined from '@ant-design/icons/es/icons/EditOutlined';
import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { generatePaperPreviewRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import {
  BackendMarkingWay,
  BackendPaper,
  fetchPapersWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { getPaperAction } from '@manyun/knowledge-hub.state.papers';
import { PaperGenFunc } from '@manyun/knowledge-hub.ui.paper-gen-func';
import { Papers } from '@manyun/knowledge-hub.ui.papers';
import { PapersCategoryCascader } from '@manyun/knowledge-hub.ui.papers-category';
import { PapersMarkingMode } from '@manyun/knowledge-hub.ui.papers-marking-mode';

export type PapersPickerProps = {
  defaultId?: number;
  id?: number | null;
  markingWay?: BackendMarkingWay;
  onPick?: (paper: BackendPaper) => void;
};

export const PapersPicker = React.forwardRef(
  ({ defaultId, id, markingWay, onPick }: PapersPickerProps, ref?: any) => {
    const dispatch = useDispatch();
    const [visible, setVisible] = useState(false);
    const [entities, setEntities] = useState<Record<number, BackendPaper>>({});
    const [ids, setIds] = useState<number[]>([]);
    const [selectedId, setSelectedId] = useState<number | undefined>(
      typeof id === 'number' ? id : defaultId
    );
    const [total, setTotal] = useState(0);
    const [loading, setLoading] = useState(false);
    const [form] = Form.useForm();
    const [page, setPage] = useState(1);
    const [pageSize, setPageSize] = useState(10);
    const [selectedName, setSelectedName] = useState<string | undefined>(undefined);

    const getPapers = useCallback(async () => {
      setLoading(true);
      const searchValues = form.getFieldsValue();
      if (searchValues.timeRange !== undefined) {
        searchValues.timeRange = [
          searchValues.timeRange[0].valueOf(),
          searchValues.timeRange[1].valueOf(),
        ];
      }
      const markingWayTransform =
        markingWay !== undefined
          ? markingWay
          : searchValues.markingWay
            ? searchValues.markingWay === 1
              ? true
              : false
            : undefined;

      const { data, error } = await fetchPapersWeb({
        page: page,
        pageSize: pageSize,
        ...searchValues,
        markingWay: markingWayTransform,
      });

      setLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      const ids: number[] = [];
      (data.data || []).forEach(paper => {
        ids.push(paper.id);
        const existingPaper = (entities as Record<number, any>)[paper.id];
        if (existingPaper && existingPaper.gmtModified === paper.gmtModified) {
          return;
        }
        entities[paper.id] = paper;
      });
      setEntities(entities);
      setIds(ids);
      setTotal(data.total);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [page, pageSize, form]);

    const getNameFromService = (id: number) => {
      dispatch(
        getPaperAction({
          paperId: id,
          needDetail: false,
          callback: paper => {
            if (paper != null) {
              setSelectedName(paper.name);
              // FIXME @Jerry `res` should be `BackendPaper` type.
              onPick?.({
                id: paper.id,
                totalGrade: paper.totalScore,
              } as BackendPaper);
            }
          },
        })
      );
    };

    useEffect(() => {
      if (typeof id === 'number') {
        entities[id] === undefined && getNameFromService(id);
        setSelectedId(id);
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [id]);

    useEffect(() => {
      if (visible === true) {
        getPapers();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [page, pageSize, visible]);

    return (
      <>
        <Space>
          <Button
            ref={ref}
            onClick={() => {
              setVisible(true);
            }}
            type={selectedName !== undefined ? 'link' : 'primary'}
            compact={selectedName !== undefined ? true : false}
          >
            {selectedName !== undefined ? (
              <span>
                {selectedName} <EditOutlined size={18} />
              </span>
            ) : (
              '选择试卷'
            )}
          </Button>
          {selectedName && selectedId !== undefined && (
            <a
              href={generatePaperPreviewRoutePath({ id: selectedId.toString() })}
              target="_blank"
              rel="noreferrer"
            >
              浏览试卷
            </a>
          )}
        </Space>
        <Modal
          title="选择试卷"
          visible={visible}
          destroyOnClose
          width={980}
          bodyStyle={{
            maxHeight: '452px',
            overflowY: 'auto',
          }}
          onOk={() => {
            if (selectedId) {
              onPick && onPick(entities[selectedId]);
              setSelectedName(entities[selectedId]?.name);
            }
            setVisible(false);
          }}
          onCancel={() => {
            setVisible(false);
          }}
        >
          <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} colon={false}>
            <Row gutter={24}>
              <Col span={8}>
                <Form.Item label="试卷分类" name="categoryCodes">
                  <PapersCategoryCascader allowClear={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="组卷方式" name="genFun">
                  <PaperGenFunc variant="select" allowClear={true} />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="试卷名称" name="name">
                  <Input placeholder="请输入试卷名称" />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="判卷方式" name="markingWay" initialValue={markingWay?.toString()}>
                  <PapersMarkingMode
                    variant="select"
                    disabled={markingWay === BackendMarkingWay.AUTOGRADE ? true : false}
                    allowClear
                  />
                </Form.Item>
              </Col>
              <Col span={8}>
                <Form.Item label="创建时间" name="timeRange">
                  <DatePicker.RangePicker
                    showTime
                    format="YYYY-MM-DD HH:mm"
                    picker="date"
                    style={{ width: 330 }}
                  />
                </Form.Item>
              </Col>
              <Col span={8} style={{ textAlign: 'right' }}>
                <Space direction="horizontal">
                  <Button
                    type="primary"
                    onClick={() => {
                      setPage(1);
                      getPapers();
                    }}
                  >
                    搜索
                  </Button>
                  <Button
                    onClick={() => {
                      form.resetFields();
                      setPage(1);
                      getPapers();
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Col>
            </Row>
          </Form>
          <Papers
            modalVariant={{
              data: {
                data: ids.map(id => entities[id]),
                total: total,
              },
              page: page,
              pageSize: pageSize,
              loading: loading,
              pageHandler: (_page: number, _pageSize?: number) => {
                setPage(_page);
                if (_pageSize) {
                  setPageSize(_pageSize);
                }
              },
              rowSelection: {
                type: 'radio',
                selectedRowKeys: selectedId ? [selectedId] : [],
                onChange: (selectedRowsKey: React.Key[]) => {
                  if (selectedRowsKey.length > 0) {
                    setSelectedId(selectedRowsKey[0] as number);
                  }
                },
              },
            }}
          />
        </Modal>
      </>
    );
  }
);

PapersPicker.displayName = 'PapersPicker';
