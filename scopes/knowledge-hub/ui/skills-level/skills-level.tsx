import React from 'react';

import { Select, SelectProps, SelectValue } from '@manyun/base-ui.ui.select';

import { SkillsLevel as SL } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';

export interface SkillsLevelProps extends SelectProps<SelectValue> {
  disabled?: boolean;
}

export function SkillsLevel({ disabled = false, ...props }: SkillsLevelProps) {
  return (
    <Select
      options={options}
      value={props.value}
      defaultValue={props.defaultValue}
      style={{ width: 200, ...props.style }}
      disabled={disabled}
      {...props}
    />
  );
}

export const SKILLS_LEVEL_TEXT = {
  [SL.LOW]: '初级',
  [SL.MIDDLE]: '中级',
  [SL.HIGH]: '高级',
};

const options: any[] = [
  { label: SKILLS_LEVEL_TEXT[SL.LOW], value: SL.LOW },
  { label: SKILLS_LEVEL_TEXT[SL.MIDDLE], value: SL.MIDDLE },
  { label: SK<PERSON>LS_LEVEL_TEXT[SL.HIGH], value: SL.HIGH },
];
