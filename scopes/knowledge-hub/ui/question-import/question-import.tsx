import React, { useCallback, useState } from 'react';

import ContainerOutlined from '@ant-design/icons/es/icons/ContainerOutlined';
import DownloadOutlined from '@ant-design/icons/es/icons/DownloadOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { Upload } from '@manyun/dc-brain.ui.upload';
import { QuestionDifficultyTextMapper } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { filterLetters } from '@manyun/knowledge-hub.ui.question';
import { webRequest } from '@manyun/service.request';

export type QuestionImportProps = {
  onImport?: (text: string) => void;
};

export type BackendParsedQuestion = {
  type: number;
  categoryCode: number | null;
  difficulty: number | null;
  title: string;
  options: string[] | null;
  answers: string[] | null;
  analysis: string | null;
  autoGrade: boolean;
};

export const replaceStringRegx = /\n|\r/g; //替换所有的换行符

export function QuestionImport({ onImport }: QuestionImportProps) {
  const [visible, setVisible] = useState(false);
  const [parsedData, setParsedData] = useState<BackendParsedQuestion[]>([]);

  const onHandleUpload = useCallback((info: any) => {
    const {
      file: { response, status },
    } = info;
    if (status === 'done') {
      if (response.success) {
        const data = response.data;
        message.success('数据导入成功！');
        setParsedData(data);
      } else {
        message.error(response.errMessage);
      }
    } else if (status === 'error') {
      message.error(response.errMessage);
      setParsedData([]);
    } else {
      setParsedData([]);
    }
  }, []);

  const onHandleParseText = useCallback(() => {
    const arr: string[] = [];
    parsedData.forEach((data, index) => {
      /**题干 */
      arr.push(`${index + 1}. ${data.title.replace(replaceStringRegx, '')}`);

      /**选项 */
      if (data.options != null) {
        data.options.forEach((option, optionIndex) => {
          arr.push(`${filterLetters(optionIndex)}. ${option.replace(replaceStringRegx, '')}`);
        });
      }
      /**答案 */
      if (data.answers != null) {
        if (data.type === 3) {
          arr.push(`答案:${['正确', '对', 'true'].includes(data.answers[0]) ? '正确' : '错误'}`);
        } else if (data.type === 4) {
          arr.push(
            `答案:${data.answers.map(answer => answer.replace(replaceStringRegx, '')).join('|')}`
          );
        } else {
          const answerIndex = data.answers.reduce((indexsArr, answer) => {
            const index = (data.options || []).findIndex(option => option === answer);
            if (filterLetters(index) !== undefined) {
              indexsArr.push(filterLetters(index) as string);
            }
            return indexsArr;
          }, [] as string[]);
          arr.push(`答案:${answerIndex.join('')}`);
        }
      }

      /**分析 */
      if (data.analysis && data.analysis !== '') {
        arr.push(`解析:${data.analysis.replace(replaceStringRegx, '') || ''}`);
      }

      /**难度 */
      if (data.difficulty) {
        arr.push(
          `难度:${
            (QuestionDifficultyTextMapper as Record<number, string>)[data.difficulty] || '易'
          }`
        );
      }
    });

    if (onImport) {
      onImport(arr.join('\n'));
    }

    setVisible(false);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [parsedData]);

  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
        type="primary"
      >
        导入
      </Button>
      <Modal
        destroyOnClose
        visible={visible}
        title="模版批量导入"
        width={620}
        onCancel={() => setVisible(false)}
        onOk={() => {
          onHandleParseText();
        }}
      >
        <Form.Item label="步骤一" labelCol={{ span: 3 }}>
          <Space direction="horizontal">
            下载试题导入模板
            <a href={`${webRequest.axiosInstance.defaults.baseURL}/dcexam/question/export`}>
              <DownloadOutlined />
              下载模版
            </a>
          </Space>
        </Form.Item>
        <Form.Item label="步骤二" labelCol={{ span: 3 }}>
          <Space direction="horizontal">上传试题模版</Space>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 3 }}>
          <Upload
            accept=".xlsx,.xls"
            maxCount={1}
            action={`${webRequest.axiosInstance.defaults.baseURL}/dcexam/question/analysis`}
            onChange={info => {
              onHandleUpload(info);
            }}
          >
            <div
              style={{
                width: 392,
                height: 170,
                border: '1px  dashed var(--border-color-base)',
                textAlign: 'center',
                display: 'flex',
                flexDirection: 'column',
                justifyContent: 'center',
              }}
            >
              <ContainerOutlined style={{ fontSize: 36, color: 'var(--manyun-primary-color)' }} />
              <div style={{ margin: '10px 0px' }}>点击或将文件拖拽到这里上传</div>
              <div style={{ color: 'var(--text-color-secondary)' }}>支持扩展名：.xlsx</div>
            </div>
          </Upload>
        </Form.Item>
      </Modal>
    </>
  );
}
