import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { QuestionImport } from './question-import';

export const BasicQuestionImport = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // getPapersMock(mock);
    // getPaperMock(mock);
    // deletePapersMock(mock);
    // mutateMock(mock);
    // categoryMock(mock);
    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <QuestionImport
        onImport={txt => {
          console.log(txt);
        }}
      />
    </ConfigProvider>
  );
};
