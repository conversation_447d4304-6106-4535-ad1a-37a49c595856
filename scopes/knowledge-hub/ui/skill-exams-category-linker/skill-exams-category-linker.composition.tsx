import React, { useEffect, useState } from 'react';

import { registerWebMocks as associateExamCategoriesWebMocks } from '@manyun/knowledge-hub.service.dcexam.associate-exam-categories';
import { registerWebMocks as fetchKnowledgeHubCategoryWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { getMock } from '@manyun/service.request';

import { SkillExamsCategoryLinker } from './skill-exams-category-linker';

export const BasicSkillExamsCategoryLinker = () => {
  const [initialized, update] = useState(false);
  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    associateExamCategoriesWebMocks(mock);
    fetchKnowledgeHubCategoryWebMocks(mock);
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return <SkillExamsCategoryLinker certId={1} />;
};
