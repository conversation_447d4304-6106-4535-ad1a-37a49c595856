import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tree } from '@manyun/base-ui.ui.tree';

import { useExamsCategory } from '@manyun/knowledge-hub.hook.use-exams-category';
import { associateExamCategoriesWeb } from '@manyun/knowledge-hub.service.dcexam.associate-exam-categories';
import { getMock } from '@manyun/service.request';

export type SkillExamsCategoryLinkerProps = {
  certId: number;
  onConfirmButtonClose?: any;
  associatedExamTypes?: number[];
};

export function SkillExamsCategoryLinker({
  certId,
  onConfirmButtonClose,
  associatedExamTypes,
}: SkillExamsCategoryLinkerProps) {
  const [visible, setVisible] = useState(false);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [examTypes, setExamTypes]: any = useState([]);
  const [{ loading, tree, checkedNodes }, { readExamCategory }] = useExamsCategory({
    withTree: true,
    treeConsumer: 'Tree',
    treeNodeTransformer: node => {
      if (
        associatedExamTypes &&
        node.key &&
        node.checkable &&
        associatedExamTypes.includes(node.key)
      ) {
        // setDefaultCheckedKeys((prev:any)=>[...prev, node.key])
        return {
          ...node,
          disabled: true,
          disableCheckbox: true,
        };
      }
      return node;
    },
  });

  useEffect(() => {
    readExamCategory();
  }, []);

  const onOk = async () => {
    setConfirmLoading(true);
    const { error } = await associateExamCategoriesWeb({
      certId,
      examCodeList: examTypes,
    });
    setConfirmLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('关联考试类型成功！');
    setVisible(false);
    setExamTypes([]);
    if (onConfirmButtonClose) {
      onConfirmButtonClose();
    }
  };

  return (
    <>
      <Button type="primary" onClick={() => setVisible(true)}>
        关联考试类型
      </Button>
      <Modal
        title="关联考试类型"
        width={480}
        visible={visible}
        onCancel={() => setVisible(false)}
        onOk={onOk}
        confirmLoading={confirmLoading}
        okText="确定"
        cancelText="取消"
        okButtonProps={{ disabled: !examTypes.length }}
        destroyOnClose
      >
        <Spin spinning={loading} delay={200}>
          <Tree
            checkable
            defaultExpandAll
            defaultSelectedKeys={checkedNodes}
            defaultCheckedKeys={checkedNodes}
            // selectedKeys={examTypes}
            onCheck={(_, e) => {
              if (checkedNodes.indexOf(e.node.key) === -1) {
                setExamTypes((pre: any) => [...pre, e.node.key]);
              }
            }}
            treeData={tree}
          />
        </Spin>
      </Modal>
    </>
  );
}
