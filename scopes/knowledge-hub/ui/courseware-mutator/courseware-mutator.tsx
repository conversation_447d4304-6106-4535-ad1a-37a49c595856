import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import InboxOutlined from '@ant-design/icons/es/icons/InboxOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';

import { McUpload as Upload, getCompletedFileList } from '@manyun/dc-brain.ui.upload';
import type { MixedUploadFile } from '@manyun/dc-brain.ui.upload';
import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import { selectCoursewareEntity } from '@manyun/knowledge-hub.state.coursewares';
import {
  Courseware,
  Variant,
  coursewaresSliceActions,
  mutateCoursewaresAction,
} from '@manyun/knowledge-hub.state.coursewares';
import { SelectTreeCoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';

import styles from './courseware-mutator.module.less';

export type CoursewareMutatorProps = {
  groupKey?: string;
};

type LoadedFile = MixedUploadFile & {
  fileTime?: string;
};

export function CoursewareMutator({ groupKey }: CoursewareMutatorProps) {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [visible, setVisiable] = useState(false);
  const [loading, setLoading] = useState(false);
  const courseware = useSelector(selectCoursewareEntity(groupKey)) as Courseware;
  const [setFields] = useCoursewaresFields(Variant.ALL);

  const toggleVisiable = () => {
    setVisiable(!visible);
    form.resetFields();
  };

  const onHandleMutateCourseware = useCallback(() => {
    const loadFile = (file: MixedUploadFile) =>
      new Promise<LoadedFile>((resolve, reject) => {
        try {
          if (file.ext === '.mp4') {
            const video = document.createElement('video');
            video.preload = 'metadata';
            video.onloadedmetadata = function () {
              // console.log('duration:', Number(video.duration).toFixed(0));
              resolve({
                ...file,
                src: file.src,
                fileTime: Number(video.duration).toFixed(0),
              });
            };
            video.onerror = function () {
              reject('Invalid video. Please select a video file.');
            };
            video.src = URL.createObjectURL(file.originFileObj!);
          } else {
            resolve(file);
          }
        } catch (e) {
          reject(e);
        }
      });

    form
      .validateFields()
      .then(values => {
        setLoading(true);
        const fileList = (values.fileList as MixedUploadFile[]).map(async file => {
          const loadedFile = await loadFile(file);
          return loadedFile;
        });
        Promise.all(fileList).then((files: LoadedFile[]) => {
          dispatch(
            mutateCoursewaresAction({
              categoryCode: groupKey ? courseware.categoryCode : values.categoryCode,
              files,
              id: groupKey,
              type: 'single',
              callback: result => {
                if (result) {
                  setVisiable(false);
                  setFields({ categoryCode: 2, name: undefined });
                  form.resetFields();
                  dispatch(coursewaresSliceActions.toggleReRender());
                }
                setLoading(false);
              },
            })
          );
        });
      })
      .catch(err => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, courseware]);

  return (
    <>
      {!groupKey ? (
        <Button type="primary" onClick={toggleVisiable}>
          上传课件
        </Button>
      ) : (
        <Button type="link" onClick={toggleVisiable} style={{ padding: 0 }}>
          替换
        </Button>
      )}
      <Modal
        title={groupKey ? '替换' : '课件上传'}
        visible={visible}
        width={490}
        onOk={onHandleMutateCourseware}
        onCancel={toggleVisiable}
        okText="提交"
        cancelText="取消"
        destroyOnClose
        okButtonProps={{ loading }}
      >
        <Form
          colon={false}
          form={form}
          labelAlign="left"
          labelCol={{ span: 4 }}
          wrapperCol={{ span: 20 }}
        >
          {!groupKey && (
            <Form.Item
              label="上传分类"
              name="categoryCode"
              rules={[{ required: true, message: '请选择上传分类！' }]}
            >
              <SelectTreeCoursewareCategory style={{ width: 270 }} />
            </Form.Item>
          )}
          <Form.Item
            label=""
            name="fileList"
            wrapperCol={{ span: 24 }}
            rules={[
              { required: true, message: '请选择上传文件！' },
              {
                validator: (_, value) => {
                  if (value && getCompletedFileList(value).length !== value.length) {
                    return Promise.reject('文件必须已完成上传！');
                  }
                  return Promise.resolve();
                },
                validateTrigger: 'submit',
              },
            ]}
            className={styles.fileList}
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <Upload
              multiple
              type="drag"
              accept=".pdf,.mp4"
              maxCount={groupKey ? 1 : 100}
              showUploadList
              maxFileSize={2 * 1024}
              allowDelete
              retryOnError
            >
              <div className={styles.content}>
                <InboxOutlined className={styles.icon} />
                <p className={styles.text}>点击或将文件拖拽到这里上传</p>
                <p className={styles.desc}>支持扩展名：pdf、mp4。文件大小限制为2G</p>
              </div>
            </Upload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
