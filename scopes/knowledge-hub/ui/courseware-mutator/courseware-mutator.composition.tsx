import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { CoursewareMutator } from './courseware-mutator';

// export const UpdateCoursewareMutator = () => (
//   <ConfigProvider>
//       <FakeStore>
//         <CoursewareMutator id={1} />
//       </FakeStore>
//   </ConfigProvider>
// );

export const CreateCoursewareMutator = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursewareMutator />
    </FakeStore>
  </ConfigProvider>
);
