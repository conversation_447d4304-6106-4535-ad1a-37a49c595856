import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { MutatePaperType } from '@manyun/knowledge-hub.state.papers';

import {
  CollapsePaperQuestionsGroupList,
  StatisticPaperQuestionsGroupList,
} from './paper-questions-group-list';

export const CollapsePaperQuestionsGroupListDiv = () => (
  <ConfigProvider>
    <CollapsePaperQuestionsGroupList
      mutateType={MutatePaperType.UpdatePaper}
      groups={[
        { id: 0, name: '板块名称1', totalScore: 20, totalSize: 20 },
        { id: 1, name: '板块名称2', totalScore: 20, totalSize: 20 },
      ]}
    />
  </ConfigProvider>
);
export const BasicPaperQuestionsGroupList = () => (
  <ConfigProvider>
    <StatisticPaperQuestionsGroupList
      mutateType={MutatePaperType.UpdatePaper}
      paper={{
        totalScore: 60,
        totalSize: 8,
        groups: [
          {
            id: 0,
            name: '基础理论',
            totalSize: 4,
            totalScore: 30,
          },
          {
            id: 1,
            name: 'SOP考核',
            totalSize: 4,
            totalScore: 30,
          },
        ],
      }}
    />
  </ConfigProvider>
);
