import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import ArrowDownOutlined from '@ant-design/icons/es/icons/ArrowDownOutlined';
import ArrowUpOutlined from '@ant-design/icons/es/icons/ArrowUpOutlined';
import DeleteOutlined from '@ant-design/icons/es/icons/DeleteOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import type { CollapseProps } from '@manyun/base-ui.ui.collapse';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { List } from '@manyun/base-ui.ui.list';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type {
  PaperSection,
  SectionTypeConfig,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { BackendPapersGenFun } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import type { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  papersSliceActions,
  selectSectionSelectedQuestionIds,
} from '@manyun/knowledge-hub.state.papers';
import type { MutatePaperType } from '@manyun/knowledge-hub.state.papers';
import {
  DrawOrRandomPaperQuestionsGroup,
  SelectPaperQuestionsGroup,
} from '@manyun/knowledge-hub.ui.paper-questions-group';
import { QuetionsPickerBySelectMode } from '@manyun/knowledge-hub.ui.quetions-picker';

import styles from './paper-questions-group-list.module.less';

export type PaperGroup = {
  id: number;
  name: string;
  totalSize: number;
  totalScore: number;
  userScore?: number;
  questionsIndex?: number[];
  gradeList?: number[];
  questionIds?: number[];
};

export type StatisticaperQuestionsGroupListProps = {
  form: FormInstance<any>;
  mode?: 'edit' | 'preview';
  mutateType?: MutatePaperType;
  paperType?: BackendPapersGenFun; //组卷方式
  paper: {
    totalScore?: number;
    totalSize?: number;
    groups: PaperGroup[];
  };
  onDelete?: (sectionId?: number) => void;
  onSort?: (sectionId: number, type: 'desc' | 'asc') => void;
};

export function StatisticPaperQuestionsGroupList({
  form,
  paper,
  onDelete,
  onSort,
  mode = 'edit',
  mutateType,
  paperType,
}: StatisticaperQuestionsGroupListProps) {
  const dispatch = useDispatch();

  const onChangeQuestionGrade = useCallback(
    (val: number, id: number, groupId: number) => {
      if (mutateType) {
        dispatch(
          papersSliceActions.setMutatePaperFields({
            mutateType,
            updateSection: {
              id: groupId,
              updateQuestion: {
                id: id,
                grade: val,
              },
            },
          })
        );
      }
    },
    [dispatch, mutateType]
  );

  const FooterItem =
    paper.totalSize !== undefined && paper.totalScore !== undefined ? (
      <Space direction="vertical">
        <Space direction="horizontal">
          <Space style={{ fontSize: '15px' }}>总试题数：</Space>
          <Space style={{ color: 'var(--manyun-primary-color)' }}>{paper.totalSize}</Space>题
        </Space>
        <Space direction="horizontal">
          <Space style={{ fontSize: '15px' }}>试题总分数:</Space>
          <Space style={{ color: 'var(--manyun-primary-color)' }}>
            {paper.totalScore.toFixed(1)}
          </Space>
          分
        </Space>
      </Space>
    ) : undefined;

  if (paper.groups.length === 0) {
    return null;
  }

  return (
    <List
      bordered={false}
      dataSource={paper.groups}
      locale={{ emptyText: '暂无板块数据' }}
      renderItem={group => {
        const newGradeList: number[] = [];
        if (Array.isArray(group.gradeList) && group.gradeList.length > 0) {
          group.gradeList.forEach(item => {
            if (!newGradeList.includes(item)) {
              newGradeList.push(item);
            }
          });
        }

        return (
          <List.Item key={group.id}>
            <Space style={{ width: '100%' }} direction="vertical">
              <div style={{ width: '100%', display: 'flex', alignItems: 'center' }}>
                <div style={{ flex: 1 }}>
                  <Space
                    size={3}
                    style={{
                      fontSize: '15px',
                      color: 'var(--manyun-primary-color)',
                      maxWidth: '124px',
                    }}
                  >
                    {group.name}
                  </Space>
                  <Space direction="horizontal" size={3}>
                    (部分共
                    <span style={{ color: 'var(--manyun-primary-color)' }}>{group.totalSize}</span>
                    题,共
                    <span style={{ color: 'var(--manyun-primary-color)' }}>
                      {group.totalScore.toFixed(1)}
                    </span>
                    分 )
                  </Space>
                </div>
                {mode === 'edit' && (
                  <Space size={10}>
                    <DeleteOutlined
                      size={18}
                      onClick={() => {
                        if (onDelete) {
                          onDelete(group.id);
                        }
                      }}
                    />
                    {paper.groups.length > 1 && (
                      <>
                        <ArrowUpOutlined
                          size={19}
                          onClick={() => {
                            if (onSort) {
                              onSort(group.id, 'asc');
                            }
                          }}
                        />
                        <ArrowDownOutlined
                          size={19}
                          onClick={() => {
                            if (onSort) {
                              onSort(group.id, 'desc');
                            }
                          }}
                        />
                      </>
                    )}
                  </Space>
                )}
              </div>
              {paperType && paperType === BackendPapersGenFun.SELECT && (
                <Space direction="horizontal" align="center">
                  每题
                  <Form.Item
                    name={`grade_${group.id}`}
                    initialValue={newGradeList.length === 1 ? newGradeList[0] : undefined}
                  >
                    <InputNumber
                      style={{ width: '75px' }}
                      min={0}
                      precision={1}
                      onChange={val => {
                        if (Array.isArray(group.questionIds) && group.questionIds.length > 0) {
                          group.questionIds.forEach(questionId => {
                            onChangeQuestionGrade((val ?? 0) as number, questionId, group.id);
                            form.setFieldValue(`${group.id}_${questionId}`, val ?? 0);
                          });
                        }
                      }}
                    />
                  </Form.Item>
                  分
                </Space>
              )}
            </Space>
          </List.Item>
        );
      }}
      footer={FooterItem}
    />
  );
}

export type CollapseStatisticaperQuestionsGroupListProps = {
  form: FormInstance<any>;
  mutateType: MutatePaperType;
  paperType?: string; //组卷方式
  paperMarkingWay?: boolean; //判卷方式
  groups: PaperSection[];
  onAdd?: () => void;
} & CollapseProps;

function CollapsePanerHeader({
  form,
  groups,
  group,
  paperType = 'SELECT',
  paperMarkingWay,
  mutateType,
}: {
  form: FormInstance<any>;
  groups: PaperSection[];
  group: PaperSection;
  paperType?: string;
  mutateType: MutatePaperType;
  paperMarkingWay?: boolean; //判卷方式
}) {
  const dispatch = useDispatch();
  const selectedQuestionIds = useSelector(selectSectionSelectedQuestionIds(mutateType, group.id));

  const onChangeGroupName = (e: any, group: PaperSection) => {
    const val = e.target.value;
    dispatch(
      papersSliceActions.setMutatePaperFields({
        mutateType: mutateType,
        updateSection: {
          id: group.id,
          name: val,
        },
      })
    );
  };

  const onAddSelect = (questions: BackendQuestion[]) => {
    const gradeVal = form.getFieldValue(`grade_${group.id}`) || 0;

    dispatch(
      papersSliceActions.setMutatePaperFields({
        mutateType: mutateType,
        updateSection: {
          id: group.id,
          addQuestions: questions,
          gradeVal: gradeVal,
        },
      })
    );
  };

  return (
    <Space direction="horizontal" align="baseline">
      <Space>
        <Form.Item
          name={`板块名称_${group.id}`}
          rules={[
            {
              required: true,
              validator: async (_, val) => {
                const repeatArr = groups.filter(group => group.name === val);
                if (val === '') {
                  return Promise.reject('板块名称不能为空！');
                } else if (repeatArr.length > 1) {
                  return Promise.reject('板块名称不能重复！');
                } else {
                  return Promise.resolve();
                }
              },
            },
            { max: 10, message: '最多输入 10 个字符！' },
          ]}
          initialValue={group.name}
        >
          <Input
            style={{ width: 200 }}
            onChange={e => {
              e.preventDefault();
              e.stopPropagation();
              onChangeGroupName(e, group);
            }}
            onFocus={e => {
              e.preventDefault();
              e.stopPropagation();
            }}
            onClick={e => {
              e.preventDefault();
              e.stopPropagation();
            }}
          />
        </Form.Item>
      </Space>

      {paperType === 'SELECT' && (
        <QuetionsPickerBySelectMode
          autoGrade={paperMarkingWay}
          btnTxt="添加试题"
          defaultQuestionIds={selectedQuestionIds}
          onOk={(entities: BackendQuestion[]) => onAddSelect(entities)}
        />
      )}
      <Space>
        该部分试题数量
        <span style={{ color: 'var(--manyun-primary-color)' }}>{group.totalSize}</span>题
      </Space>
    </Space>
  );
}

export type CollapsePaperQuestionsGroupListRef = React.Ref<{
  setActiveKey: (key: string) => void;
}>;

export const CollapsePaperQuestionsGroupList = React.forwardRef(
  (
    {
      form,
      mutateType,
      paperType = 'SELECT',
      paperMarkingWay,
      groups,
      onAdd,
      ...props
    }: CollapseStatisticaperQuestionsGroupListProps,
    ref?: CollapsePaperQuestionsGroupListRef
  ) => {
    const [activeKeys, setActiveKeys] = useState<string[]>([]);
    const dispatch = useDispatch();

    useImperativeHandle(ref, () => ({
      /**添加activekey */
      setActiveKey: (key: string) => {
        if (!activeKeys.map(String).includes(key)) {
          const keys = [...activeKeys, key];
          setActiveKeys(keys);
        }
      },
    }));

    /**添加板块 */
    const onClickAddSection = () => {
      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType,
          addSection: {
            name: '板块名称',
            typeConfig: paperType !== 'SELECT' ? initialRandomTypeConfig : undefined,
          },
        })
      );
      /**展开添加的折叠面板 */
      const len = groups.length;
      if (len > 0) {
        const groupId = (groups[len - 1].id + 1).toString();
        activeKeys.push(groupId);
      } else {
        activeKeys.push('0');
      }
    };

    useEffect(() => {
      if (groups.length > 0) {
        setActiveKeys(groups.map(group => group.id.toString()));
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, []);

    return (
      <div className={styles.collapsePaperQuestionsGroupList}>
        <div style={{ height: '100%', overflowY: 'auto', marginBottom: '10px' }}>
          {groups.length > 0 && (
            <Collapse
              defaultActiveKey={groups.map(group => group.id.toString())}
              activeKey={activeKeys}
              style={{ width: '100%' }}
              onChange={(keys: string | string[]) => {
                setActiveKeys(keys as string[]);
              }}
              {...props}
            >
              {groups.map(group => (
                <Collapse.Panel
                  key={group.id.toString()}
                  header={
                    <CollapsePanerHeader
                      paperMarkingWay={paperMarkingWay}
                      group={group}
                      groups={groups}
                      paperType={paperType}
                      mutateType={mutateType}
                      form={form}
                    />
                  }
                  style={{ alignItems: 'baseline' }}
                >
                  {/* 选题模式下 */}
                  {paperType === 'SELECT' && (
                    <>
                      {group.questions?.length === 0 ? (
                        <Empty
                          description={
                            <Typography.Text type="secondary">
                              当前暂无数据,请点击添加试题
                            </Typography.Text>
                          }
                        />
                      ) : (
                        <SelectPaperQuestionsGroup
                          form={form}
                          groupId={group.id}
                          mutateType={mutateType}
                          questions={group.questions as (BackendQuestion & { grade: number })[]}
                        />
                      )}
                    </>
                  )}
                  {/* 抽题模式下 */}
                  {paperType === 'DRAW' && (
                    <DrawOrRandomPaperQuestionsGroup
                      groupId={group.id}
                      groupQuestions={(group.questions as BackendQuestion[])?.map(question => ({
                        id: question.id,
                        type: question.type,
                      }))}
                      category={group.questionCategory as string}
                      mutateType={mutateType}
                      paperType={paperType}
                      typeConfig={group.typeConfig || {}}
                      paperMarkingWay={paperMarkingWay}
                    />
                  )}
                  {/* 随机模式下 */}
                  {paperType === 'RANDOM' && (
                    <DrawOrRandomPaperQuestionsGroup
                      groupId={group.id}
                      category={group.questionCategory as string}
                      mutateType={mutateType}
                      paperType={paperType}
                      typeConfig={group.typeConfig}
                      paperMarkingWay={paperMarkingWay}
                    />
                  )}
                </Collapse.Panel>
              ))}
            </Collapse>
          )}
        </div>
        <div>
          <Button type="primary" onClick={onClickAddSection}>
            添加板块
          </Button>
        </div>
      </div>
    );
  }
);

CollapsePaperQuestionsGroupList.displayName = 'CollapsePaperQuestionsGroupList';

export const initialRandomTypeConfig: Record<string, SectionTypeConfig> = {
  ESSAY: {
    score: 0,
    drawTotal: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      NORMAL: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
    },
  },
  MULTIPLE_CHOICE: {
    score: 0,
    drawTotal: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      NORMAL: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
    },
  },
  SHORT_ANSWER: {
    score: 0,
    drawTotal: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      NORMAL: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
    },
  },
  SINGLE_CHOICE: {
    score: 0,
    drawTotal: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      NORMAL: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
    },
  },
  TRUE_OR_FALSE: {
    score: 0,
    drawTotal: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      NORMAL: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
    },
  },
};
