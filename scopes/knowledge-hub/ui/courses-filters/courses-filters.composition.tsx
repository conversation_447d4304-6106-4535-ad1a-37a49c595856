import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { AllCoursesFilters as AllFilters, MyCoursesFilters as MyFilters } from './courses-filters';

export const AllCoursesFilters = () => (
  <ConfigProvider>
    <FakeStore>
      <AllFilters />
    </FakeStore>
  </ConfigProvider>
);

export const MyCompletedCoursesFilters = () => (
  <ConfigProvider>
    <FakeStore>
      <MyFilters completedVariant="completed" type="form" />
    </FakeStore>
  </ConfigProvider>
);

export const MyUncompletedCoursesFilters = () => (
  <ConfigProvider>
    <FakeStore>
      <MyFilters completedVariant="uncompleted" type="form" />
    </FakeStore>
  </ConfigProvider>
);
