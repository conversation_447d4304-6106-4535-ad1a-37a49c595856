import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { useAllCourses, useMyCourses } from '@manyun/knowledge-hub.hook.use-courses';
import { useCoursesFields } from '@manyun/knowledge-hub.hook.use-courses-fields';
import { CourseStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { CompletedVariant } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { coursesSliceActions } from '@manyun/knowledge-hub.state.courses';
import { CoursesCategoryCascader } from '@manyun/knowledge-hub.ui.courses-category';
import { SelectCoursesState } from '@manyun/knowledge-hub.ui.courses-state';

export type CoursesFiltersProps = {
  type: 'tab' | 'form';
  completedVariant: CompletedVariant;
};

export enum BackendStudyStatus {
  UNDONE = 'UNDONE',
  UN_INVOLVED = 'UN_INVOLVED',
}

export const BackendStudyStatusMapper = {
  [BackendStudyStatus.UNDONE]: '未完成',
  [BackendStudyStatus.UN_INVOLVED]: '未参与',
};

const { TabPane } = Tabs;

export function AllCoursesFilters() {
  const [form] = Form.useForm();
  const [setFields, fields] = useCoursesFields({ variant: 'all' });
  const [getCourses, { loading }] = useAllCourses();
  const dispatch = useDispatch();

  useEffect(() => {
    form.setFieldsValue({
      enabled: fields.enabled,
      name: fields.name,
      categoryCode: fields.categoryCode,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onHandleSearch = useCallback(() => {
    form
      .validateFields()
      .then(values => {
        getCourses();
      })
      .catch(err => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onHandleRest = useCallback(() => {
    form.resetFields();
    dispatch(coursesSliceActions.resetFields({ variant: 'all' }));
    onHandleSearch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  return (
    <Form
      colon={false}
      form={form}
      layout="inline"
      onValuesChange={changedValues => {
        if (changedValues.enabled) {
          changedValues.enabled = changedValues.enabled === CourseStatus.NORMAL;
        }
        setFields({ variant: 'all', page: 1, ...changedValues });
      }}
    >
      <Form.Item label="状态" name="enabled">
        <SelectCoursesState />
      </Form.Item>
      <Form.Item label="课程名称" name="name">
        <Input style={{ width: 243 }} allowClear placeholder="请输入课程名称关键字" />
      </Form.Item>
      <Form.Item label="课程分类" name="categoryCode">
        <CoursesCategoryCascader style={{ width: 400 }} trigger="onDidMount" allowClear />
      </Form.Item>
      <Space>
        <Button type="primary" loading={loading} onClick={onHandleSearch}>
          搜索
        </Button>
        <Button
          disabled={loading}
          onClick={() => {
            form.resetFields();
            onHandleRest();
          }}
        >
          重置
        </Button>
      </Space>
    </Form>
  );
}

export function MyCoursesFilters({
  completedVariant,
  type,
  courseName,
}: CoursesFiltersProps & { courseName?: string }) {
  const [form] = Form.useForm();
  const [setFields, fields] = useCoursesFields({ variant: 'my', completedVariant });
  const [innerTabKey, setInnerTabKey] = useState(fields.required ? 'OBLIGATORY' : 'ELECTIVE');
  const [getCourses, { loading }] = useMyCourses({ completedVariant });
  const dispatch = useDispatch();

  useEffect(() => {
    form.setFieldsValue({
      involvedState: fields.involvedState,
      timeRange: fields.timeRange,
      name: courseName || fields.name,
    });

    /**FIX http://chandao.manyun-local.com/zentao/bug-view-4724.html */
    if (courseName) {
      setFields({ name: courseName, variant: 'my' });
      onHandleSearch();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [courseName]);

  const onHandleSearch = useCallback(() => {
    form
      .validateFields()
      .then(values => {
        getCourses();
      })
      .catch(err => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const onHandleRest = useCallback(() => {
    form.resetFields();
    dispatch(coursesSliceActions.resetFields({ variant: 'my', completedVariant }));
    onHandleSearch();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  return type === 'form' ? (
    <Form
      colon={false}
      form={form}
      layout="inline"
      onValuesChange={changedValues => {
        if (changedValues.timeRange) {
          changedValues.timeRange = changedValues.timeRange.map(
            (m: any) => m.clone().seconds(0).unix() + '000'
          ) as [number, number];
        }
        setFields({ variant: 'my', completedVariant, ...changedValues });
      }}
    >
      {completedVariant === 'uncompleted' && (
        <Form.Item label="状态" name="involvedState">
          <Select
            style={{ width: 200 }}
            allowClear
            options={(Object.keys(BackendStudyStatus) as BackendStudyStatus[]).map(status => ({
              label: BackendStudyStatusMapper[status],
              value: status,
            }))}
          />
        </Form.Item>
      )}
      {completedVariant === 'completed' && (
        <Form.Item label="课程时间" name="timeRange">
          <DatePicker.RangePicker
            format="YYYY-MM-DD HH:mm"
            showTime
            allowClear
            placeholder={['开始时间', '结束时间']}
          />
        </Form.Item>
      )}
      <Form.Item label="课程名称" name="name">
        <Input style={{ width: 243 }} allowClear placeholder="请输入课程名称关键字" />
      </Form.Item>
      <Space>
        <Button type="primary" onClick={onHandleSearch} loading={loading}>
          搜索
        </Button>
        <Button
          disabled={loading}
          onClick={() => {
            form.resetFields();
            onHandleRest();
          }}
        >
          重置
        </Button>
      </Space>
    </Form>
  ) : (
    <Tabs
      activeKey={innerTabKey}
      onChange={activeKey => {
        setInnerTabKey(activeKey);
        setFields({
          variant: 'my',
          completedVariant: 'uncompleted',
          required: activeKey === 'OBLIGATORY',
        });
        getCourses();
      }}
      tabBarStyle={{ border: 0 }}
    >
      <TabPane tab="必修课程" key="OBLIGATORY"></TabPane>
      <TabPane tab="选修课程" key="ELECTIVE"></TabPane>
    </Tabs>
  );
}
