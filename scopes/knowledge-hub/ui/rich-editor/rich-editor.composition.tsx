import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { RichEditor } from './rich-editor';

export const BasicRichEditor = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <RichEditor defaultValue="hello from RichEditor" />
    </ConfigProvider>
  );
};
