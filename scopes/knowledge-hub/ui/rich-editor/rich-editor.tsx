/**
 * wangEditor for redevelopment
 * 基于wangEditor 二次开发富文本框编辑器
 */
import React, { forwardRef, useCallback, useEffect, useImperativeHandle, useRef } from 'react';

import ReactWEditor from 'wangeditor-for-react';

import { message } from '@manyun/base-ui.ui.message';

import { uploadFullFile } from '@manyun/dc-brain.service.upload-full-file';
import { webRequest } from '@manyun/service.request';

import './rich-editor.less';

export type RichEditorRef = {
  setRichEditorTxt: (html: string) => void;
  /**设置编辑器内容 */
  destroy: () => void;
  /**销毁编辑器 */
};

export type RichEditorProps = {
  defaultValue?: string;
  placeholder?: string;
  uploadImgMaxSize?: number;
  onchangeTimeout?: number;
  onChange?: (html: string) => void;
  onBlur?: (html: string) => void;
  onFocus?: (html: string) => void;
};

export const RichEditor = forwardRef(
  (
    {
      defaultValue,
      placeholder,
      uploadImgMaxSize = 5,
      onchangeTimeout = 300,
      onChange,
      onBlur,
      onFocus,
    }: RichEditorProps,
    ref?: React.Ref<RichEditorRef>
  ) => {
    let richEditorRef = useRef<any>(null);

    useEffect(() => {
      return () => {
        if (richEditorRef.current) {
          richEditorRef.current?.destroy();
        }
      };
    }, []);

    useImperativeHandle(ref, () => ({
      setRichEditorTxt: setRichEditorTxt,
      destroy: () => {
        richEditorRef.current?.destroy();
      },
    }));

    const setRichEditorTxt = useCallback(
      (html: string) => {
        if (richEditorRef.current) {
          richEditorRef.current.editor.txt.html(html);
        }
      },
      [richEditorRef]
    );

    const handleUpload = async (resultFiles: any, callback: (url: any) => void) => {
      const { error, data } = await uploadFullFile(resultFiles[0]);
      if (error) {
        message.error(error.message);
        return;
      }
      const downloadApiUrl = `${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=`;
      const uploadedFileSrc = data![0].src;
      const path = data![0].patialPath;
      const location = window.location;
      const downloadImgUrl =
        location.port == '3000'
          ? `${downloadApiUrl}${path}`
          : `${location.origin}${uploadedFileSrc} `;
      if (downloadImgUrl) {
        callback(downloadImgUrl);
      }
    };

    return (
      <ReactWEditor
        ref={richEditorRef}
        defaultValue={defaultValue}
        config={{
          /**图片上传最大限制 默认5M*/
          uploadImgMaxSize: uploadImgMaxSize * 1024 * 1024,
          /**自定义上传图片 */
          customUploadImg: async (resultFiles: any, insertImgFn: any) => {
            handleUpload(resultFiles, insertImgFn);
          },
          /**自定义上传视频 */
          customUploadVideo: async (resultFiles: any, insertVideoFn: any) => {
            handleUpload(resultFiles, insertVideoFn);
          },
          zIndex: 10,
          height: 240,
          placeholder: placeholder,
          onchangeTimeout: onchangeTimeout,
          menus: [
            'bold',
            'underline',
            'italic',
            'strikeThrough',
            'fontSize',
            'foreColor',
            'justify',
            'table',
            'image',
            'video',
            'undo',
            'redo',
          ],
        }}
        onChange={html => {
          if (onChange) {
            onChange(html);
          }
        }}
        onBlur={html => {
          if (onBlur) {
            onBlur(html);
          }
        }}
        onFocus={html => {
          if (onFocus) {
            onFocus(html);
          }
        }}
      />
    );
  }
);
