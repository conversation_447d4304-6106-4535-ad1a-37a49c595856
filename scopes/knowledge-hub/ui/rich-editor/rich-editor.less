@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.w-e-toolbar {
  background-color: @component-background !important;
  border: 1px solid @border-color-split !important;
  /* border-bottom: 1px solid @border-color-split !important; */
}
.w-e-text {
  background-color: @component-background !important;
}

.w-e-toolbar,
.w-e-text-container,
.w-e-menu-panel {
  background-color: @component-background !important;
  color: @text-color !important;
  border: 1px solid @border-color-split !important;
  border-bottom: none;
}
.w-e-text-container {
  border: 1px solid @border-color-split !important;
}
.w-e-menu:hover {
  background-color: @background-color-light !important;
}
.w-e-menu > i {
  color: @text-color !important;
}

.w-e-panel-container {
  margin-top: 46px !important;
}
.w-e-menu .w-e-panel-container {
  background-color: @component-background !important;
  color: @text-color !important;
  border: 1px solid @border-color-split !important;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title {
  border-bottom: 1px solid @border-color-split !important;
  color: @text-color !important;
}

.w-e-menu .w-e-panel-container {
  border: 1px solid @border-color-split !important;
  box-shadow: none !important;
  border-radius: @border-radius-base !important;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-title .w-e-active {
  color: @text-color !important;
  border: none !important;
}

.w-e-menu .w-e-panel-container .w-e-panel-tab-content input[type='text'] {
  border: 1px solid @border-color-split !important;
  border-radius: @border-radius-base !important;
  height: 30px !important;
  text-indent: 10px !important;
  background-color: @component-background !important;
  color: @text-color !important;
  margin-bottom: 8px !important;
}

.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button {
  border-radius: @border-radius-base !important;
  color: @white !important;
  border-color: var(--manyun-primary-color) !important;
  background: var(--manyun-primary-color) !important;
  text-shadow: 0 -1px 0 rgb(0 0 0 / 12%);
  box-shadow: 0 2px 0 rgb(0 0 0 / 5%);
  height: 32px;
  padding: 4px 15px;
  font-size: 14px;
}
.w-e-menu .w-e-panel-container .w-e-panel-tab-content .w-e-button-container button:hover {
  background: var(--manyun-primary-color) !important;
}

.w-e-drop-list-item {
  color: @text-color !important;
}
/** w-e-droplist */
.w-e-toolbar .w-e-droplist {
  background-color: @component-background !important;
  border: 1px solid @border-color-split !important;
}

.w-e-toolbar .w-e-dp-title {
  text-align: center;
  color: @text-color !important;
  line-height: 2;
  border-bottom: 1px solid @border-color-split !important;
  font-size: 13px;
}
.w-e-toolbar > ul.w-e-list > li.w-e-item {
  color: @text-color !important;
}

.w-e-toolbar > ul.w-e-list > li.w-e-item :hover {
  background-color: @background-color-light !important;
}

.w-e-toolbar > ul.w-e-block > li.w-e-item:hover {
  background-color: @background-color-light !important;
}

.w-e-toolbar > .w-e-droplist {
  background-color: @component-background !important;
  border: 1px solid @border-color-split !important;
}

.w-e-toolbar > .w-e-droplist > .w-e-dp-title {
  color: @text-color !important;
  border-bottom: 1px solid @border-color-split !important;
}

.w-e-toolbar .w-e-droplist ul.w-e-list {
  color: rgb(226, 226, 226) !important;
}

.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item {
  color: @text-color !important;
}

.w-e-toolbar .w-e-droplist ul.w-e-list li.w-e-item:hover {
  background-color: @background-color-light !important;
}

.w-e-toolbar > .w-e-droplist > ul.w-e-block > li.w-e-item:hover {
  background-color: @background-color-light !important;
}
