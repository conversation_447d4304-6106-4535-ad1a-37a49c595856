import React, { useEffect, useState } from 'react';

import moment from 'moment';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';

import { fetchExamFailedUsersWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users';
import type { ServiceD as requestMakeupTestWebServiceD } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test';
import { requestMakeupTestWeb } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test';

export type MakeUpExamProps = {
  examId: string | number;
  examName: string;
  issueSuccessCb?: () => void;
};

export function MakeUpExam({ examId, examName, issueSuccessCb }: MakeUpExamProps) {
  const [visible, setVisible] = useState(false);
  const [users, setUsers] = useState<{ label: string; value: number }[]>([]);
  const [confirmButtonLoading, setConfirmButtonLoading] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [filedsValue, setFiledsValue]: any = useState({});
  const [form] = Form.useForm();
  const testName = `${examName}-补考`;

  useEffect(() => {
    if (!visible) {
      return;
    }
    getUsers(examId);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, examId]);

  const getUsers = async (examId: MakeUpExamProps['examId']) => {
    const { error, data } = await fetchExamFailedUsersWeb({ examId: Number(examId) });
    if (error) {
      message.error(error.message);
      return;
    }
    setUsers(data!.data.map(({ id, userName }) => ({ label: userName, value: id })));
    const ids = data!.data.map(({ id }) => id);
    form.setFieldsValue({ userIds: ids, name: testName });
    setFiledsValue({ ...filedsValue, userIds: ids });
  };

  const onOk = async () => {
    const { timeRange, userIds, name } = form.getFieldsValue();
    if (!userIds || !userIds.length || !timeRange || !timeRange.length) {
      return;
    }
    setConfirmButtonLoading(true);
    const params = {
      examId: Number(examId),
      userIds: userIds,
      timeRange: timeRange?.map((m: Moment) => m.clone().seconds(0).valueOf()) as [number, number],
      name: name,
    };
    const { error } = await requestMakeupTestWeb(params as requestMakeupTestWebServiceD);
    setConfirmButtonLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('发起补考成功！');
    if (issueSuccessCb) {
      issueSuccessCb();
    }
    form.resetFields();
    setVisible(false);
  };

  return (
    <>
      <Button type="link" style={{ padding: 0, height: 'auto' }} onClick={() => setVisible(true)}>
        发起补考
      </Button>
      <Modal
        title="发起补考"
        visible={visible}
        okButtonProps={{
          disabled:
            !filedsValue.userIds ||
            !filedsValue.userIds.length ||
            !filedsValue.timeRange ||
            !filedsValue.timeRange.length,
          loading: confirmButtonLoading,
        }}
        okText="确认"
        cancelText="取消"
        onCancel={() => setVisible(false)}
        onOk={onOk}
      >
        <Form
          colon={false}
          form={form}
          onValuesChange={values => setFiledsValue({ ...filedsValue, ...values })}
        >
          <Form.Item
            label="考试名称"
            name="name"
            rules={[
              { required: true, message: '请输入考试名称！' },
              { max: 50, message: '最多输入 50 个字符！' },
            ]}
          >
            <Input style={{ width: 400 }} />
          </Form.Item>
          <Form.Item
            label="考试时间"
            name="timeRange"
            rules={[{ required: true, message: '考试时间必选！' }]}
          >
            <DatePicker.RangePicker
              style={{ width: 400 }}
              showTime
              format="YYYY-MM-DD HH:mm"
              disabledDate={current => {
                return current && current < moment().startOf('day');
              }}
              disabledTime={(current, type) => {
                const now = moment();
                const isSameDay = !current || current.isSame(now, 'day');
                const isSameHour = !current || current.isSame(now, 'hour');
                const isSameMinute = !current || current.isSame(now, 'minute');

                if (type === 'start' || isSameDay) {
                  return {
                    disabledHours: isSameDay ? () => range(0, now.hours()) : undefined,
                    disabledMinutes:
                      isSameDay && isSameHour ? () => range(0, now.minutes()) : undefined,
                    disabledSeconds:
                      isSameDay && isSameHour && isSameMinute
                        ? () => [0, now.seconds()]
                        : undefined,
                  };
                }
                return {};
              }}
            />
          </Form.Item>
          <Form.Item
            label="补考人员"
            name="userIds"
            rules={[{ required: true, message: '补考人员必选！' }]}
          >
            <Select style={{ width: 400 }} options={users} mode="multiple" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

function range(start: number, end: number) {
  const result: number[] = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }

  return result;
}
