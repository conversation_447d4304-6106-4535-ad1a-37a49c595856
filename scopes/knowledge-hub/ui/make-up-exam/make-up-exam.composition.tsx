import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks as fetchExamFailedUsersWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users';
import { registerWebMocks as requestMakeupTestWebMocks } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test';
import { getMock } from '@manyun/service.request';

import { MakeUpExam } from './make-up-exam';

export const BasicMakeUpExam = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    fetchExamFailedUsersWebMocks(mock);
    requestMakeupTestWebMocks(mock);
    update(true);
  }, []);

  if (!initialized) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router>
          <MakeUpExam examId={1} examName="专业工程师暖通专业试卷" />
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
