import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { PapersMutationPrepare } from './papers-mutation-prepare';

export const CreatePapersMutationPrepare = () => (
  <ConfigProvider>
    <FakeStore>
      <PapersMutationPrepare />
    </FakeStore>
  </ConfigProvider>
);

export const UpdatePapersMutationPrepare = () => (
  <ConfigProvider>
    <FakeStore>
      <PapersMutationPrepare
        paper={{
          id: 0, //id
          type: 'SELECT', //试卷类型
          categoryCode: '1001', //分类
          name: '测试试卷', //试卷名称
          totalGrade: 100, //总分
          gmtCreate: 1631762517869, //创建时间
          gmtModified: 1631762517869,
          modifierId: 1, //修改人
          modifierName: 'admin', //修改人姓名
        }}
      />
    </FakeStore>
  </ConfigProvider>
);
