import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import {
  NEW_PAPER_ROUTE_PATH,
  generateEditSpecificPaperRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { BackendPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { MutatePaperType } from '@manyun/knowledge-hub.state.papers';
import {
  getPaperAction,
  papersSliceActions,
  selectMutatePaper,
} from '@manyun/knowledge-hub.state.papers';
import { PaperGenFunc } from '@manyun/knowledge-hub.ui.paper-gen-func';
import { PapersCategoryCascader } from '@manyun/knowledge-hub.ui.papers-category';
import { PapersMarkingMode } from '@manyun/knowledge-hub.ui.papers-marking-mode';

export type PapersMutationPrepareProps = {
  /**
   * 修改传入的peper 详情.
   */
  paper?: BackendPaper;
};

export function PapersMutationPrepare({ paper }: PapersMutationPrepareProps) {
  const dispatch = useDispatch();
  const history = useHistory();
  const [form] = Form.useForm();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const { name, genFun, markingWay, categoryCode } = useSelector(
    selectMutatePaper(
      paper !== undefined ? MutatePaperType.UpdatePaper : MutatePaperType.CreatePaper
    )
  );

  const onHandleClick = () => {
    setIsModalVisible(true);
    if (paper !== undefined) {
      /** 将详情绑定form */
      form.setFieldsValue({
        name: paper.name,
        categoryCode: Array.isArray(paper.categoryCode)
          ? paper.categoryCode.filter(code => code !== '')
          : paper.categoryCode && paper.categoryCode !== ''
          ? [paper.categoryCode]
          : [],
        markingWay: paper.autoGrade ? '1' : '0',
        genFun: paper.type,
      });
      dispatch(
        getPaperAction({
          paperId: paper.id,
          callback: res => {
            if (res != null) {
              dispatch(papersSliceActions.setInitialUpdatePaperFields({ ...res }));
            }
          },
        })
      );
    }
  };

  const onHandleUpdatePaperPrePare = useCallback(() => {
    form.validateFields().then(values => {
      const categoryCode = values.categoryCode.pop() || '';
      if (categoryCode === undefined || categoryCode === '') {
        message.error('请选择试卷分类!');
        return;
      }
      dispatch(
        papersSliceActions.setMutatePaperFields({
          mutateType:
            paper !== undefined ? MutatePaperType.UpdatePaper : MutatePaperType.CreatePaper,
          name: values.name,
          categoryCode: categoryCode,
          genFun: values.genFun,
          markingWay: values.markingWay,
        })
      );
      // 进行跳转至PaperMutator 页面
      setIsModalVisible(false);
      history.push(
        paper === undefined ? NEW_PAPER_ROUTE_PATH : generateEditSpecificPaperRoutePath(paper.id)
      );
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  return (
    <>
      <Button
        type={paper === undefined ? 'primary' : 'link'}
        onClick={onHandleClick}
        compact={paper ? true : false}
      >
        {paper === undefined ? '创建试卷' : '修改'}
      </Button>
      {isModalVisible && (
        <Modal
          destroyOnClose
          title={`${paper === undefined ? '创建' : '更新'}试卷`}
          visible={isModalVisible}
          onCancel={() => setIsModalVisible(false)}
          onOk={() => onHandleUpdatePaperPrePare()}
          okText={paper ? '确定' : '创建'}
          cancelText="取消"
          width={480}
        >
          <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
            <Form.Item
              name="name"
              label="试卷名称"
              initialValue={paper ? paper.name : name}
              rules={[
                { required: true, message: '请输入试卷名称' },
                { max: 30, message: '最多输入 30 个字符！' },
              ]}
            >
              <Input placeholder="请填写试卷名称" style={{ width: '280px' }} />
            </Form.Item>
            <Form.Item
              name="categoryCode"
              label="试卷分类"
              initialValue={
                paper ? [paper.categoryCode] : categoryCode !== '' ? [categoryCode] : []
              }
              rules={[{ required: true, message: '请选择试卷分类' }]}
            >
              <PapersCategoryCascader
                style={{ width: '280px' }}
                placeholder="请选择试卷分类"
                trigger="onDidMount"
                onMappingCompleted={(value: any) => {
                  form.setFieldsValue({ categoryCode: value });
                }}
              />
            </Form.Item>
            <Form.Item
              name="markingWay"
              label="判卷方式"
              rules={[{ required: true, message: '请选择判卷方式' }]}
              initialValue={
                paper !== undefined
                  ? paper.autoGrade === true
                    ? '1'
                    : '0'
                  : markingWay?.toString()
              }
            >
              <PapersMarkingMode variant="radio" />
            </Form.Item>
            <Form.Item
              name="genFun"
              label="组卷方式"
              initialValue={paper ? paper.type : genFun}
              rules={[{ required: true, message: '请选择组卷方式' }]}
            >
              <PaperGenFunc variant="radio" disabled={paper !== undefined ? true : false} />
            </Form.Item>
          </Form>
        </Modal>
      )}
    </>
  );
}
