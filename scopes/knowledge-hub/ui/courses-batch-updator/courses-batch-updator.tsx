import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';

import { mutateCoursesAction } from '@manyun/knowledge-hub.state.courses';
import { selectAllCourses } from '@manyun/knowledge-hub.state.courses';
import { CoursesCategoryCascader } from '@manyun/knowledge-hub.ui.courses-category';

export function CoursesBatchUpdator() {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [visible, setVisiable] = useState(false);
  const [loading, setLoading] = useState(false);
  const toggleVisiable = () => setVisiable(!visible);
  const { selectedIds } = useSelector(selectAllCourses());

  const onHandleMutateCourseware = useCallback(() => {
    form
      .validateFields()
      .then(values => {
        setLoading(true);
        dispatch(
          mutateCoursesAction({
            categoryCode: values.categoryCode,
            callback: result => {
              if (result) {
                setVisiable(false);
                form.resetFields();
              }
              setLoading(false);
            },
          })
        );
      })
      .catch(err => {});
  }, [dispatch]);

  return (
    <>
      <Button type="primary" onClick={toggleVisiable} disabled={selectedIds.length === 0}>
        批量更新
      </Button>
      <Modal
        title="批量更新"
        visible={visible}
        width={480}
        onOk={onHandleMutateCourseware}
        onCancel={toggleVisiable}
        confirmLoading={loading}
        okText="确定"
        cancelText="取消"
        destroyOnClose
      >
        <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="课程分类"
            name="categoryCode"
            rules={[
              {
                required: true,
                type: 'boolean',
                transform: value => {
                  return value?.length === 3 || undefined;
                },
                message: '请选择课程分类！',
              },
            ]}
          >
            <CoursesCategoryCascader style={{ width: 358 }} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
