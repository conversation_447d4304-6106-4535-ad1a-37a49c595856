import React, { forwardRef } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form, FormInstance, FormProps } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';

import { SelectQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { SelectTreeQuestionCategory } from '@manyun/knowledge-hub.ui.questions-category';
import { SelectQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';

const { RangePicker } = DatePicker;

export type FieldProps = {
  name: string;
  label: string;
  control: any;
  rules?: any[];
};

export type Field = 'title' | 'difficulty' | 'type' | 'timeRange' | 'categoryCode' | 'null';
export type QuestionsFiltersProps = {
  fieldNames: Field[];
  span?: number;
  buttonAlign?: 'start' | 'end' | 'left' | 'right' | 'center' | 'justify' | 'match-parent';
  onSearch?: (values: any) => void;
  onReset?: (values: any) => void;
  loading?: boolean;
  disableType?: boolean;
} & FormProps;

const formItemLayout = {
  labelCol: {
    xs: { span: 7 },
    sm: { span: 7 },
  },
  wrapperCol: {
    xs: { span: 17 },
    sm: { span: 17 },
  },
};

export const QuestionsFilters = forwardRef(
  (
    {
      fieldNames,
      onReset,
      onSearch,
      span = 8,
      buttonAlign = 'right',
      loading,
      disableType,
      ...props
    }: QuestionsFiltersProps,
    ref?: React.Ref<FormInstance<any>>
  ) => {
    const [form] = Form.useForm();

    const getItems = () => {
      const items = fieldNames.map(name => {
        const item = getBaseFields(disableType).find(field => field.name == name);
        if (item) {
          return item;
        } else {
          return {};
        }
      });
      return items as FieldProps[];
    };

    const getFields = () => {
      const items = getItems();
      const children: JSX.Element[] = [];
      items.forEach((item, i) => {
        children.push(
          <Col span={span} key={i}>
            <Form.Item name={item.name} label={item.label} rules={item.rules} key={i}>
              {item.control}
            </Form.Item>
          </Col>
        );
      });
      return children;
    };

    const onClickSerach = () => {
      if (onSearch) {
        onSearch(form.getFieldsValue());
      }
    };

    const onClickReset = () => {
      form.resetFields();
      if (onReset) onReset(form.getFieldsValue());
    };

    return (
      <Form form={form} {...formItemLayout} colon={false} ref={ref} {...props}>
        <Row gutter={24}>
          {getFields()}
          <Col span={span} style={{ textAlign: buttonAlign }}>
            <Button type="primary" onClick={onClickSerach} loading={loading}>
              搜索
            </Button>
            <Button style={{ margin: '0 8px' }} onClick={onClickReset}>
              重置
            </Button>
          </Col>
        </Row>
      </Form>
    );
  }
);

export function getBaseFields(disableType?: boolean) {
  const BaseFields: FieldProps[] = [
    {
      label: '题型',
      name: 'type',
      control: <SelectQuestionType allowClear />,
    },
    {
      label: '难度',
      name: 'difficulty',
      control: <SelectQuestionsDifficulty allowClear />,
    },
    {
      label: '试题内容',
      name: 'title',
      control: <Input placeholder="请输入试题关键字" allowClear />,
    },
    {
      label: '试题分类',
      name: 'categoryCode',
      control: <SelectTreeQuestionCategory style={{ width: '100%' }} allowClear />,
    },
    {
      label: '创建时间',
      name: 'timeRange',
      control: (
        <RangePicker showTime format="YYYY-MM-DD HH:mm" style={{ width: '330px' }} allowClear />
      ),
    },
  ];
  return BaseFields;
}
