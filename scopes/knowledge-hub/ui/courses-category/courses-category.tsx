import React, { useEffect } from 'react';

import shallowequal from 'shallowequal';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps } from '@manyun/base-ui.ui.cascader';
import { Spin } from '@manyun/base-ui.ui.spin';

import { useCoursesCategory } from '@manyun/knowledge-hub.hook.use-courses-category';

export type CoursesCategoryProps = {
  style?: React.CSSProperties;
  categoryCode: number;
};

export function CoursesCategory({ style, categoryCode }: CoursesCategoryProps) {
  const [{ loading, entities }, { readCourseCategory }] = useCoursesCategory();
  const level3 = entities[categoryCode] || { name: '未知', parentId: null };
  const level2 = level3?.parentId ? entities[level3.parentId] : { name: '未知', parentId: null };
  const level1 = level2?.parentId ? entities[level2.parentId] : { name: '未知' };

  useEffect(() => {
    readCourseCategory({
      policy: ids => (ids.includes(categoryCode) ? 'cache-only' : 'network-only'),
    });
  }, [categoryCode]);

  if (loading) {
    return <Spin style={style} spinning={loading} size="small" />;
  }

  const categoryNames = `${level1?.name}/${level2?.name}/${level3?.name}`;

  return <span title={categoryNames}>{categoryNames}</span>;
}

export type CoursesCategoryCascaderProps = Omit<CascaderProps, 'options'> & {
  trigger?: 'onDidMount' | 'onFocus';
  defaultValue?: number[];
  value?: number[];
  onMappingCompleted?(value: number[]): void;
};

export type CascaderRef = {
  focus: () => void;
  blur: () => void;
};

export const CoursesCategoryCascader = React.forwardRef(
  (
    {
      trigger = 'onFocus',
      defaultValue,
      onFocus,
      onMappingCompleted,
      ...props
    }: CoursesCategoryCascaderProps,
    ref?: React.Ref<CascaderRef>
  ) => {
    const { value, ...restProps } = props;

    const [internalValue, setInternalValue] = React.useState<number[] | undefined>(
      value || defaultValue
    );

    const [{ loading, tree, entities }, { readCourseCategory }] = useCoursesCategory({
      withTree: true,
      treeConsumer: 'Cascader',
    });

    const prevValue = usePrevious(value);
    useEffect(() => {
      if (!shallowequal(prevValue, value)) {
        setInternalValue(value);
      }
    }, [prevValue, value]);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        readCourseCategory();
      }
    }, [trigger]);

    useEffect(() => {
      if (
        value?.length === 1 &&
        entities[value[0]] &&
        internalValue?.length === 1 &&
        entities[internalValue[0]]
      ) {
        const defaultCategory = value[0];
        const level3 = entities[defaultCategory];
        const level2 = entities[level3!.parentId] || { id: undefined, parentId: -999 };
        const level1 = entities[level2.parentId] || { id: undefined, parentId: -999 };
        if (level2.id !== undefined && level1.id !== undefined) {
          const newValue: number[] = [level1.id, level2.id, level3!.id];
          if (typeof onMappingCompleted == 'function') {
            onMappingCompleted(newValue);
          } else {
            setInternalValue(newValue);
          }
        }
      }
    }, [value, entities, internalValue, onMappingCompleted]);

    return (
      <Cascader
        ref={ref}
        loading={loading}
        options={tree || []}
        defaultValue={defaultValue}
        value={internalValue}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readCourseCategory();
          }
          onFocus?.(evt);
        }}
        displayRender={(label, selectedOptions) => {
          if (internalValue && internalValue.length !== 3) {
            return '';
          }
          if (selectedOptions.length !== 3) {
            return <>未知/未知/未知</>;
          }
          return label.join('/');
        }}
        {...restProps}
      />
    );
  }
);

function usePrevious<T>(value: T) {
  // The ref object is a generic container whose current property is mutable ...
  // ... and can hold any value, similar to an instance property on a class
  const ref = React.useRef<T>();
  // Store current value in ref
  React.useEffect(() => {
    ref.current = value;
  }, [value]); // Only re-run if value changes
  // Return previous value (happens before update in useEffect above)
  return ref.current;
}
