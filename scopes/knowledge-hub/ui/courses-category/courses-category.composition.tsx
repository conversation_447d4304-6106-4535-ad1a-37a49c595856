import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { CoursesCategory, CoursesCategoryCascader } from './courses-category';

export const BasicCoursewaresCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursesCategoryCascader />
    </FakeStore>
  </ConfigProvider>
);

export const TextCoursewaresCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <CoursesCategory categoryCode={3} />
    </FakeStore>
  </ConfigProvider>
);
