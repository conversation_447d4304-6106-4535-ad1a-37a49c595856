import React, { useEffect, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';

import { User } from '@manyun/auth-hub.ui.user';
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { fetchExamResultWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-result';

import styles from './exam-result.module.less';

export type ExamResultProps = {
  id: number | string;
  showRank?: Boolean;
  examId?: number | string;
  getResultCb?: (data: any) => void;
  personalizedDisplay?: React.ReactElement;
};

export function ExamResult({
  id,
  examId,
  showRank = false,
  getResultCb,
  personalizedDisplay,
}: ExamResultProps) {
  const [result, setResult] = useState<any>(null);
  const [resultLoading, setResultLoading] = useState(false);
  const [ranks, setRanks] = useState<any>([]);
  const [rankLoading, setRankLoading] = useState(false);

  const getExamResult = async () => {
    setResultLoading(true);
    const { data, error } = await fetchExamResultWeb({ examPaperId: Number(id) });
    setResultLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setResult(data);
    if (getResultCb && typeof getResultCb === 'function') {
      getResultCb(data);
    }
  };

  const getRanks = async () => {
    setRankLoading(true);
    const { data, error } = await fetchExamResultWeb({ examId: Number(examId) });
    setRankLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setRanks(data);
  };

  useEffect(() => {
    if (!id && id !== 0) {
      return;
    }
    getExamResult();
  }, [id]);

  useEffect(() => {
    if (
      id === undefined ||
      result?.status !== BackendExamPaperStatus.GRADED ||
      (!examId && examId !== 0)
    ) {
      return;
    }
    getRanks();
  }, [id, result?.status]);

  useEffect(() => {
    let currentInterval: number | undefined;
    if (result?.status !== BackendExamPaperStatus.GRADED) {
      currentInterval = window.setInterval(getExamResult, 3 * 1000);
    }

    return () => {
      if (currentInterval !== undefined) {
        window.clearInterval(currentInterval);
      }
    };
  }, [result?.status]);

  const getUserRank = () => {
    const userRank = ranks.find(({ userId }: any) => userId === result.userId);
    return userRank?.rankIndex;
  };

  if (result === null) {
    return null;
  }

  return (
    <Spin spinning={resultLoading}>
      <Space style={{ width: '100%' }} direction="vertical">
        <div style={{ width: 557, margin: 'auto', textAlign: 'center' }}>
          <User size={80} id={result.userId} name={result.userName} />
        </div>
        <div style={{ width: 557, minHeight: 339, margin: 'auto' }}>
          <Space style={{ width: '100%' }} direction="vertical">
            <div style={{ display: 'table', margin: 'auto' }}>
              {!getResultStatus(result) && (
                <span style={{ fontSize: 26, color: 'var(--manyun-warning-color)' }}>
                  考试结束待判卷
                </span>
              )}
              {getResultStatus(result) && (
                <span
                  style={{
                    fontSize: 26,
                    color: result.pass
                      ? 'var(--manyun-success-color)'
                      : 'var(--manyun-error-color)',
                  }}
                >
                  {result.grade}分
                </span>
              )}
            </div>
            <Card>
              <div className={styles.text}>
                <p>
                  {result.pass || !getResultStatus(result) ? '恭喜您' : '很遗憾您'}
                  <>
                    {getResultStatus(result) && (
                      <span
                        style={{
                          color: result.pass
                            ? 'var(--manyun-success-color)'
                            : 'var(--manyun-error-color)',
                        }}
                      >
                        {result.pass ? '通过' : '未通过'}
                      </span>
                    )}
                    {!getResultStatus(result) && (
                      <span style={{ color: 'var(--manyun-success-color)' }}>结束</span>
                    )}
                  </>
                  本场考试
                </p>
                {personalizedDisplay}
                <p>
                  正确率：
                  {getResultStatus(result)
                    ? ((result.grade / result.totalGrade) * 100).toFixed(2) + '%'
                    : '--'}
                </p>
                {judgeShowRank(result, showRank) && <p>本场考试排名：{getUserRank()}</p>}
              </div>
              {judgeShowRank(result, showRank) && (
                <Table
                  size="small"
                  rowKey="userId"
                  loading={rankLoading}
                  dataSource={ranks}
                  columns={
                    [
                      {
                        title: '排名',
                        dataIndex: 'rankIndex',
                      },
                      {
                        title: '人员姓名',
                        dataIndex: 'userName',
                      },
                      {
                        title: '得分',
                        dataIndex: 'grade',
                      },
                      {
                        width: 130,
                        ellipsis: true,
                        title: '考生角色',
                        dataIndex: 'roleInfoList',
                        render: (roleInfoList: { roleName: string }[] | null) => {
                          if (!roleInfoList) {
                            return '--';
                          }
                          return roleInfoList.map(({ roleName }) => roleName).join(' | ');
                        },
                      },
                      {
                        width: 130,
                        ellipsis: true,
                        title: '所属位置',
                        dataIndex: 'resourceList',
                        render: (resourceList: { resourceCode: string }[] | null) => {
                          if (!resourceList) {
                            return '--';
                          }
                          return resourceList.map(({ resourceCode }) => resourceCode).join(' | ');
                        },
                      },
                    ] as ColumnsType<any>
                  }
                  pagination={false}
                />
              )}
            </Card>
          </Space>
        </div>
      </Space>
    </Spin>
  );
}

function getResultStatus(result: any) {
  return result.status === BackendExamPaperStatus.GRADED;
}

function judgeShowRank(result: any, showRank: Boolean) {
  return getResultStatus(result) && showRank;
}
