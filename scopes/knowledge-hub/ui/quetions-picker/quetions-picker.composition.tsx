import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks as fetchQuestionsMock } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { registerWebMocks as fetchQuestionCountMock } from '@manyun/knowledge-hub.service.dcexam.fetch-questions-count';
import { getMock } from '@manyun/service.request';

import { QuetionsPickerByRandomMode, QuetionsPickerBySelectMode } from './quetions-picker';

export const BasicQuetionsPicker = () => {
  const [initial, update] = useState(false);
  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    fetchQuestionsMock(mock);
    update(true);
  }, []);

  if (!initial) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <QuetionsPickerBySelectMode title="选题试题" />
      </FakeStore>
    </ConfigProvider>
  );
};

export const BasicQuetionsPicker2 = () => {
  const [initial, update] = useState(false);
  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    fetchQuestionCountMock(mock);
    update(true);
  }, []);

  if (!initial) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <QuetionsPickerByRandomMode
          title="选定单选题"
          typeConfigs={{
            '0': {
              score: 0,
              difficultCount: {
                '1': 10,
                '2': 10,
                '3': 10,
                '4': 10,
                '5': 10,
              },
            },
          }}
        />
      </FakeStore>
    </ConfigProvider>
  );
};
