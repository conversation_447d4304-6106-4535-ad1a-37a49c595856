import { MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { ColumnsType } from 'antd/es/table';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { SectionTypeConfig } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { fetchQuestionWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-question';
import {
  BackendQuestion,
  BackendQuestionType,
  QuestionTypeTextMapper,
  fetchQuestionsWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  QuestionsCount,
  fetchQuestionsCountWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions-count';
import { Questions } from '@manyun/knowledge-hub.ui.questions';
import { SelectTreeQuestionCategory } from '@manyun/knowledge-hub.ui.questions-category';
import { QuestionsFilters } from '@manyun/knowledge-hub.ui.questions-filters';

const { TabPane } = Tabs;

export type QuetionsPickerProps = {
  title?: string;
  btnTxt?: string;
  typeConfigs?: Record<string, SectionTypeConfig> /**默认的配置 */;
  questionType?: number; //试题类型
  defaultCategory?: number | string /**默认分类 */;
  defaultQuestionIds?: number[] /**默认选中的试题id */;
  autoGrade?: boolean; //是否为系统判题
  onOk?: (questions: BackendQuestion[]) => void;
  onSave?: (data: QuestionsCount[], category: number | string) => void;
};

export function QuetionsPickerBySelectMode({
  title = '选定试题',
  questionType,
  btnTxt = '选定试题',
  defaultQuestionIds,
  autoGrade,
  onOk,
}: QuetionsPickerProps) {
  const [isSetVisible, setIsSetVisible] = useState(false);
  const [activeKey, setActiveKey] = useState('0');
  const [total, setTotal] = useState<number>(0);
  const [selectedIds, setSelectedIds] = useState<number[]>([]);
  const [entities, setEntities] = useState<
    Record<number, BackendQuestion & { isSelected: boolean }>
  >({});
  const [ids, setIds] = useState<number[]>([]);
  const [loading, setLoading] = useState(false);
  const [fields, setFields] = useState<{ page: number; pageSize: number }>({
    page: 1,
    pageSize: 10,
  });

  const formRef = useRef<FormInstance<any>>(null);

  const getQuestions = async (initIds?: number[]) => {
    setLoading(true);
    let formValues: Record<string, any> = {};
    if (formRef.current) {
      const values = formRef.current.getFieldsValue();
      if (values.timeRange !== undefined) {
        values.timeRange = [values.timeRange[0].valueOf(), values.timeRange[1].valueOf()];
      }
      formValues = { ...values };
    }
    const { data, error } = await fetchQuestionsWeb({
      page: fields.page,
      pageSize: fields.pageSize,
      type: questionType,
      questionType: 1,
      autoGrade,
      ...formValues,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    const ids: number[] = [];
    (data.data || []).forEach(question => {
      ids.push(question.id);
      entities[question.id] = {
        ...question,
        isSelected: initIds ? initIds.includes(question.id) : selectedIds.includes(question.id),
      };
    });
    setEntities(entities);
    setIds(ids);
    setTotal(data.total);
  };

  useEffect(() => {
    if (isSetVisible) {
      getQuestions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields]);

  const onClickAddAction = (question: BackendQuestion) => {
    let keys: number[] = [];
    setSelectedIds(prevKeys => {
      if (!prevKeys.includes(question.id)) {
        keys = [...prevKeys, question.id];
      } else {
        keys = [...prevKeys];
      }
      return keys;
    });
    setEntities(pre => {
      pre[question.id] = { ...question, isSelected: true };
      return pre;
    });
  };

  const onClickRemoveAction = (question: BackendQuestion) => {
    let keys: number[] = [];
    setSelectedIds(prevKeys => {
      keys = prevKeys.filter(k => question.id !== k);
      return keys;
    });
    setEntities(pre => {
      pre[question.id] = { ...question, isSelected: false };
      return pre;
    });
  };

  const onHandleOk = () => {
    setIsSetVisible(false);
    if (onOk) {
      if (selectedIds.length > 0) {
        /**通过id批量获取详情 */
        getBatchQuestion(res => {
          if (res != null) {
            onOk(res as BackendQuestion[]);
          } else {
            onOk(selectedIds.map(id => entities[id]));
          }
        });
      } else {
        onOk([]);
      }
    }
  };

  const getBatchQuestion = useCallback(
    async (callback?: (data: BackendQuestion[] | null) => void, ids?: number[]) => {
      if (selectedIds.length > 0 || ids !== undefined) {
        const { data, error } = await fetchQuestionWeb({
          questionId: ids || (selectedIds as number[]),
        });
        if (!error && data != null) {
          if (callback) {
            callback(data as BackendQuestion[]);
          }
          return;
        }
        return null;
      } else {
        return null;
      }
    },
    [selectedIds]
  );

  const onClickButton = useCallback(() => {
    if (questionType !== undefined) {
      formRef.current?.setFieldsValue({ type: questionType });
    }
    setIsSetVisible(true);
    setActiveKey('0');
    setInitialData();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formRef, isSetVisible, defaultQuestionIds]);

  const setInitialData = useCallback(() => {
    /**获取选中的列表 */
    if (defaultQuestionIds !== undefined && defaultQuestionIds.length > 0) {
      setSelectedIds(defaultQuestionIds);
      getBatchQuestion(data => {
        if (data != null) {
          (data || []).forEach(question => {
            entities[question.id] = { ...question, questionType: '1', isSelected: true };
          });
        }
      }, defaultQuestionIds);
    } else {
      setSelectedIds([]);
    }
    /**获取试题列表 */
    getQuestions(defaultQuestionIds || []);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSetVisible, setSelectedIds, defaultQuestionIds]);

  return (
    <div
      onClick={e => {
        e.stopPropagation();
        e.preventDefault();
        e.nativeEvent.stopImmediatePropagation();
      }}
    >
      <Button type="primary" onClick={onClickButton}>
        {btnTxt}
      </Button>
      <Modal
        destroyOnClose
        title={title}
        visible={isSetVisible}
        onOk={onHandleOk}
        onCancel={e => {
          e.preventDefault();
          e.stopPropagation();
          setIsSetVisible(false);
        }}
        width={930}
        bodyStyle={{ height: '450px', overflowY: 'scroll' }}
      >
        <Tabs
          activeKey={activeKey}
          onChange={key => {
            setActiveKey(key);
            if (key === '0') {
              formRef.current?.resetFields();
              setFields({ page: 1, pageSize: fields.pageSize });
            }
          }}
        >
          {['可选试题', '已选试题'].map((tab, key) => (
            <TabPane
              tab={
                <Space direction="horizontal" size={2}>
                  {tab} {key === 1 && <Badge count={selectedIds.length} />}
                </Space>
              }
              key={key}
            >
              {activeKey === '0' && (
                <QuestionsFilters
                  ref={formRef as any}
                  fieldNames={
                    questionType === undefined
                      ? ['categoryCode', 'difficulty', 'type', 'title', 'timeRange']
                      : ['categoryCode', 'difficulty', 'title', 'timeRange', 'null']
                  }
                  buttonAlign="right"
                  loading={loading}
                  onSearch={() => {
                    setFields({ page: 1, pageSize: fields.pageSize });
                  }}
                  onReset={() => {
                    setFields({ page: 1, pageSize: fields.pageSize });
                  }}
                />
              )}
              <Questions
                modalVariant={{
                  data: {
                    data:
                      activeKey === '0'
                        ? ids.map(id => entities[id])
                        : selectedIds.map(id => entities[id]),
                    total: activeKey === '0' ? total : selectedIds.length,
                  },
                  loading: loading,
                  page: activeKey === '0' ? fields.page : undefined,
                  pageSize: activeKey === '0' ? fields.pageSize : undefined,
                  pageHandler: (page, pageSize) => {
                    if (activeKey === '0') {
                      setFields({ page: page!, pageSize: pageSize! });
                    }
                  },
                }}
                operateColumn={{
                  position: 'start',
                  column: {
                    title: '',
                    width: 45,
                    align: 'center',
                    dataIndex: '__action',
                    render: (__, question) => {
                      if (activeKey === '0') {
                        return (
                          <>
                            {question.isSelected ? (
                              <div
                                onClick={() => {
                                  onClickRemoveAction(question);
                                }}
                              >
                                <MinusCircleOutlined />
                              </div>
                            ) : (
                              <div
                                onClick={() => {
                                  onClickAddAction(question);
                                }}
                              >
                                <PlusCircleOutlined />
                              </div>
                            )}
                          </>
                        );
                      } else {
                        return (
                          <div
                            onClick={() => {
                              onClickRemoveAction(question);
                            }}
                          >
                            <MinusCircleOutlined />
                          </div>
                        );
                      }
                    },
                  },
                }}
              />
            </TabPane>
          ))}
        </Tabs>
      </Modal>
    </div>
  );
}

export function QuetionsPickerByRandomMode({
  typeConfigs,
  onSave,
  defaultCategory,
  title = '添加试题',
  btnTxt = '修改',
  autoGrade,
}: QuetionsPickerProps) {
  const [categoryCode, setCategoryCode] = useState<number | string>('');
  const [categoryTypeConfigs, setCategoryTypeConfigs] = useState<Record<string, SectionTypeConfig>>(
    typeConfigs || {}
  );
  const [isSetVisible, setIsSetVisible] = useState(false);
  const [data, setData] = useState<QuestionsCount[]>([]);
  const [loading, setLoading] = useState(false);
  const [editingKey, setEditingKey] = useState<any>('');
  const [form] = Form.useForm();

  const getQuestionsCount = async (
    code?: string,
    typeConfigs?: Record<string, SectionTypeConfig>
  ) => {
    let categoryCodeString =
      code !== undefined ? code : Array.isArray(categoryCode) ? categoryCode[0] : categoryCode;
    if (!categoryCodeString) {
      return;
    }
    setLoading(true);
    const { data, error } = await fetchQuestionsCountWeb({
      categoryCode: categoryCodeString,
      autoGrade: !!autoGrade,
    });

    setLoading(false);
    if (error) {
      return;
    }
    const configs = typeConfigs ? typeConfigs : categoryTypeConfigs;
    const newData: QuestionsCount[] = data.map((row: QuestionsCount) => {
      let difficultCountMapper: Record<string, number> = {
        EASIER_THAN_EASY: 0,
        EASY: 0,
        NORMAL: 0,
        HARD: 0,
        HARDER_THAN_HARD: 0,
      };
      const difficultyCountMapper: Record<string, number> | undefined =
        configs && configs[row.type] && configs[row.type].difficultCount
          ? configs[row.type].difficultCount
          : undefined;
      if (difficultyCountMapper !== undefined) {
        difficultCountMapper = {
          EASIER_THAN_EASY: difficultyCountMapper['EASIER_THAN_EASY'] || 0,
          EASY: difficultyCountMapper['EASY'] || 0,
          NORMAL: difficultyCountMapper['NORMAL'] || 0,
          HARD: difficultyCountMapper['HARD'] || 0,
          HARDER_THAN_HARD: difficultyCountMapper['HARDER_THAN_HARD'] || 0,
        };
      }
      return {
        ...row,
        ...difficultCountMapper,
      };
    });
    setData(newData);
  };

  const onClickChangeCategory = (val: any) => {
    setCategoryCode(val);
    setEditingKey('');
    setCategoryTypeConfigs({});
  };

  useEffect(() => {
    if (categoryCode !== '') {
      getQuestionsCount();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryCode]);

  const setVisibleTrue = () => {
    setIsSetVisible(true);
    setEditingKey('');
    if (defaultCategory !== '') {
      const code = Array.isArray(defaultCategory) ? defaultCategory[0] : defaultCategory;
      setCategoryCode(code);
      if (defaultCategory !== undefined) {
        getQuestionsCount(code, typeConfigs);
      } else {
        setData([]);
      }
    }
  };

  const isEditing = (record: QuestionsCount) => record.type === editingKey;

  const onClickEdit = (record: any) => {
    form.setFieldsValue({
      EASIER_THAN_EASY: 0,
      EASY: 0,
      NORMAL: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
      ...record,
    });
    setEditingKey(record.type);
  };

  const onClickSave = async (record: QuestionsCount) => {
    try {
      const config = (await form.validateFields()) as QuestionsCount;
      const newData = [...data];
      const index = newData.findIndex(item => record.type === item.type);
      if (index > -1) {
        const item = newData[index];
        newData.splice(index, 1, {
          ...item,
          ...config,
        });
        setData(newData);
        setEditingKey('');
      } else {
        newData.push(config);
        setData(newData);
        setEditingKey('');
      }
    } catch (errInfo) {
      // console.log('Validate Failed:', errInfo);
    }
  };

  const onClickCancle = () => {
    setEditingKey('');
  };

  const onOk = () => {
    if (categoryCode === '' || !categoryCode) {
      message.error('请先选择试题分类！');
      return;
    }
    //保存到store
    if (onSave) {
      onSave(data, categoryCode);
    }
    setIsSetVisible(false);
  };

  const columns: ColumnsType<any> | any[] = [
    {
      title: '',
      dataIndex: 'type',
      width: 65,
      render: type => (
        <span>
          {
            (QuestionTypeTextMapper as Record<string, string>)[
              BackendQuestionType[type as any] as string
            ]
          }
        </span>
      ),
    },
    {
      title: '易',
      dataIndex: 'EASIER_THAN_EASY',
      editable: true,
      render: (_, { EASIER_THAN_EASY, EASIER_THAN_EASY_ALL }) => (
        <span>
          {EASIER_THAN_EASY}/{EASIER_THAN_EASY_ALL || 0}
        </span>
      ),
    },
    {
      title: '偏易',
      dataIndex: 'EASY',
      editable: true,
      render: (_, { EASY, EASY_ALL }) => (
        <span>
          {EASY}/{EASY_ALL || 0}
        </span>
      ),
    },
    {
      title: '适中',
      dataIndex: 'NORMAL',
      editable: true,
      render: (_, { NORMAL, NORMAL_ALL }) => (
        <span>
          {NORMAL}/{NORMAL_ALL || 0}
        </span>
      ),
    },
    {
      title: '难',
      dataIndex: 'HARD',
      editable: true,
      render: (_, { HARD, HARD_ALL }) => (
        <span>
          {HARD}/{HARD_ALL || 0}
        </span>
      ),
    },
    {
      title: '偏难',
      dataIndex: 'HARDER_THAN_HARD',
      editable: true,
      render: (_, { HARDER_THAN_HARD, HARDER_THAN_HARD_ALL }) => (
        <span>
          {HARDER_THAN_HARD}/{HARDER_THAN_HARD_ALL || 0}
        </span>
      ),
    },
    {
      title: '操作',
      width: 90,
      dataIndex: '__action',
      render: (_, record) => {
        const editable = isEditing(record);
        return editable ? (
          <span>
            <Button
              compact
              type="link"
              onClick={() => {
                onClickSave(record);
              }}
              style={{ marginRight: 5 }}
            >
              保存
            </Button>
            <Button
              onClick={() => {
                onClickCancle();
              }}
              compact
              type="link"
            >
              取消
            </Button>
          </span>
        ) : (
          <Button
            disabled={editingKey !== ''}
            onClick={() => {
              onClickEdit(record);
            }}
            compact
            type="link"
          >
            编辑
          </Button>
        );
      },
    },
  ];

  const mergedColumns = columns.map(col => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: QuestionsCount) => ({
        record,
        dataIndex: col.dataIndex,
        title: col.title,
        editing: isEditing(record),
      }),
    };
  });

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          setVisibleTrue();
        }}
        size={btnTxt === '修改' ? 'small' : 'middle'}
      >
        {btnTxt}
      </Button>
      <Modal
        title={title}
        visible={isSetVisible}
        destroyOnClose
        width={860}
        okText="确定"
        cancelText="取消"
        onOk={() => {
          if (editingKey !== '') {
            message.error('编辑试题数量未保存,请先保存！');
            return;
          }
          onOk();
        }}
        onCancel={() => {
          setIsSetVisible(false);
        }}
      >
        <Form form={form}>
          <Form.Item
            label="试题分类"
            rules={[{ required: true, message: '请选择分类' }]}
            wrapperCol={{ span: 10 }}
          >
            <SelectTreeQuestionCategory
              onChange={(value?: number | string) => {
                onClickChangeCategory(value);
              }}
              defaultValue={categoryCode as any}
              value={categoryCode as any}
              allowClear={false}
            />
          </Form.Item>
          <Form.Item label="选题方式">
            <Table
              rowKey="type"
              components={{
                body: {
                  cell: EditableCell,
                },
              }}
              size="small"
              columns={mergedColumns}
              dataSource={data}
              loading={loading}
              pagination={false}
            />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

interface EditableCellProps extends React.HTMLAttributes<HTMLElement> {
  editing: boolean;
  dataIndex: string;
  title: any;
  inputType: 'number';
  record: QuestionsCount;
  index: number;
  value: string | number;
  children: React.ReactNode;
}

const EditableCell: React.FC<EditableCellProps> = ({
  editing,
  dataIndex,
  title,
  inputType,
  record,
  index,
  children,
  value,
  ...restProps
}) => {
  return (
    <td {...restProps}>
      {editing ? (
        <Form.Item noStyle>
          <Form.Item
            name={dataIndex}
            rules={[
              {
                required: true,
                message: `试题数量不能为空！`,
              },
            ]}
            noStyle
          >
            <InputNumber
              precision={0}
              style={{ width: 60 }}
              min={0}
              max={(record as any)[dataIndex + '_ALL'] || 0}
            />
          </Form.Item>
          <span> /{(record as any)[dataIndex + '_ALL'] || 0}</span>
        </Form.Item>
      ) : (
        children
      )}
    </td>
  );
};
