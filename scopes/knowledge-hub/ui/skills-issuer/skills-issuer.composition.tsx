import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { registerWebMocks as fetchAvailableUsersWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-available-users';
import { registerWebMocks as issueUsersSkillWebMocks } from '@manyun/knowledge-hub.service.dcexam.issue-users-skill';
import { getMock } from '@manyun/service.request';

import { SkillsIssuer } from './skills-issuer';

export const BasicSkillsIssuer = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    fetchAvailableUsersWebMocks(mock);
    issueUsersSkillWebMocks(mock);
    mock.onAny().networkError();
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <SkillsIssuer certId={1} />
    </ConfigProvider>
  );
};
