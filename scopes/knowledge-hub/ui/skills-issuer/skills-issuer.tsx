import React, { useEffect, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { fetchAvailableUsersWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-available-users';
import { issueUsersSkillWeb } from '@manyun/knowledge-hub.service.dcexam.issue-users-skill';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

export type SkillsIssuerProps = {
  certId: number;
  onConfirmButtonClose?: any;
};

export function SkillsIssuer({ certId = 1, onConfirmButtonClose }: SkillsIssuerProps) {
  const [visible, setVisible] = useState(false);
  const [users, setUsers] = useState({ data: [], total: 0 });
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10 });
  const [userSelectedData, setUserSelectedData]: any = useState([]);

  const [form] = Form.useForm();

  useEffect(() => {
    if (visible) {
      getUsers();
    }
  }, [visible, pagination.page, pagination.pageSize]);

  const getUsers = async () => {
    setUsers({ data: [], total: 0 });
    setLoading(true);
    const values: any = form.getFieldsValue();
    const params: any = { certId, ...pagination };
    if (values.blockGuids) {
      params.blockGuids = values.blockGuids;
    }
    if (values.roleIds) {
      params.roleIds = values.roleIds;
    }
    if (values.userId) {
      params.userId = values.userId.key;
    }
    const { data, error }: any = await fetchAvailableUsersWeb(params);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data.data && Array.isArray(data.data) && data.data.length) {
      setUsers({ data: data.data, total: data.total });
    }
  };

  const search = () => {
    resetPagination();
    getUsers();
  };

  const resetPagination = () => {
    setPagination({ page: 1, pageSize: 10 });
  };

  return (
    <>
      <Button type="primary" onClick={() => setVisible(true)}>
        人员发放
      </Button>
      <Modal
        width={1000}
        visible={visible}
        destroyOnClose
        title="发放人员"
        onCancel={() => {
          resetPagination();
          form.resetFields();
          setVisible(false);
        }}
        onOk={async () => {
          const { error } = await issueUsersSkillWeb({
            selectedUsers: userSelectedData.map(
              ({ userId, userName }: { userId: number; userName: string }) => ({ userId, userName })
            ),
            certId,
          });
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('人员发放成功！');
          setUserSelectedData([]);
          resetPagination();
          form.resetFields();
          setVisible(false);
          if (onConfirmButtonClose) {
            onConfirmButtonClose();
          }
        }}
        okButtonProps={{ disabled: !userSelectedData.length }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={21}>
          <Form form={form} colon={false} layout="inline">
            <Form.Item label="位置" name="blockGuids">
              <LocationTreeSelect
                style={{ width: 200 }}
                multiple
                disabledTypes={['IDC']}
                authorizedOnly
              />
            </Form.Item>
            <Form.Item label="角色" name="roleIds">
              <RoleSelect style={{ width: 200 }} mode="multiple" />
            </Form.Item>
            <Form.Item label="姓名" name="userId">
              <UserSelect style={{ width: 200 }} labelInValue />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    search();
                  }}
                >
                  搜索
                </Button>
                <Button
                  disabled={loading}
                  onClick={() => {
                    form.resetFields();
                    setUserSelectedData([]);
                    setPagination({
                      page: 1,
                      pageSize: 10,
                    });
                    getUsers();
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <Table
            size="small"
            rowKey="userId"
            loading={loading}
            dataSource={users.data}
            columns={[
              {
                title: '账号',
                dataIndex: 'accountName',
              },
              {
                title: '姓名',
                dataIndex: 'userName',
                render: (userName, { userId }) => <User.Link id={userId} name={userName} />,
              },
              {
                title: '位置',
                dataIndex: 'blockGuidList',
                ellipsis: true,
                render: txt => {
                  if (txt && Array.isArray(txt) && txt.length) {
                    return txt.join(' | ');
                  }
                  return '--';
                },
              },
              {
                title: '所属角色',
                dataIndex: 'roleList',
                ellipsis: true,
                render: txt => {
                  if (txt && Array.isArray(txt) && txt.length) {
                    return txt.join(' | ');
                  }
                  return '--';
                },
              },
            ]}
            pagination={{
              total: users.total,
              current: pagination.page,
              pageSize: pagination.pageSize,
            }}
            onChange={(pagination: any) => {
              setPagination({
                page: pagination.current,
                pageSize: pagination.pageSize,
              });
            }}
            rowSelection={{
              selectedRowKeys: userSelectedData.map(
                ({ userId }: { userId: string | number }) => userId
              ),
              onChange: (keys, rows) => setUserSelectedData(rows),
            }}
          />
        </Space>
      </Modal>
    </>
  );
}
