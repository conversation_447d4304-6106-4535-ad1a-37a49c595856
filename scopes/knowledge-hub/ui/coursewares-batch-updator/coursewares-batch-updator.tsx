import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import {
  Variant,
  coursewaresSliceActions,
  mutateCoursewaresAction,
} from '@manyun/knowledge-hub.state.coursewares';
import { selectCoursewareSelectedIds } from '@manyun/knowledge-hub.state.coursewares';
import { SelectTreeCoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import { CoursewaresDeleter } from '@manyun/knowledge-hub.ui.coursewares-deleter';

export function CoursewaresBatchUpdator() {
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [visible, setVisiable] = useState(false);
  const [loading, setLoading] = useState(false);
  const toggleVisiable = () => setVisiable(!visible);
  const [setFields] = useCoursewaresFields(Variant.ALL);
  const selectedIds = useSelector(selectCoursewareSelectedIds());

  const onHandleMutateCourseware = useCallback(() => {
    form
      .validateFields()
      .then(values => {
        setLoading(true);
        dispatch(
          mutateCoursewaresAction({
            categoryCode: values.categoryCode,
            type: 'batch',
            callback: result => {
              if (result) {
                setVisiable(false);
                setFields({ categoryCode: 2, name: undefined });
                dispatch(coursewaresSliceActions.toggleReRender());
                form.resetFields();
              }
              setLoading(false);
            },
          })
        );
      })
      .catch(err => {});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  return (
    <Space>
      <Button type="primary" onClick={toggleVisiable} disabled={selectedIds.length === 0}>
        批量更新
      </Button>
      <CoursewaresDeleter />
      <Modal
        title="批量更新"
        visible={visible}
        width={480}
        onOk={onHandleMutateCourseware}
        onCancel={toggleVisiable}
        confirmLoading={loading}
        destroyOnClose
        okText="确定"
        cancelText="取消"
      >
        <Form colon={false} form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="课件分类"
            name="categoryCode"
            rules={[{ required: true, message: '请选择课件分类！' }]}
          >
            <SelectTreeCoursewareCategory />
          </Form.Item>
        </Form>
      </Modal>
    </Space>
  );
}
