import React from 'react';
import { useDispatch } from 'react-redux';

import { DeleteOutlined } from '@ant-design/icons';

import { Modal } from '@manyun/base-ui.ui.modal';

import { deleteSkillAction } from '@manyun/knowledge-hub.state.skills';

export type SkillsDeleterProps = {
  id: number | string;
};

export function SkillsDeleter({ id }: SkillsDeleterProps) {
  const dispatch = useDispatch();

  const onHandleDelete = () => {
    Modal.confirm({
      title: '确认要删除这项技能吗？',
      content: '删除这项技能后，已获得此项技能的学生不受影响。',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        dispatch(
          deleteSkillAction({
            id,
          })
        );
      },
    });
  };

  return <DeleteOutlined key="delete" onClick={onHandleDelete} />;
}
