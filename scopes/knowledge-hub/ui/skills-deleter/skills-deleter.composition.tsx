import React, { useEffect, useState } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks as deleteSkillWebMocks } from '@manyun/knowledge-hub.service.dcexam.delete-skill';
import { getMock } from '@manyun/service.request';

import { SkillsDeleter } from './skills-deleter';

export const BasicSkillsDeleter = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    deleteSkillWebMocks(mock);
    mock.onAny().networkError();
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <FakeStore>
      <SkillsDeleter id={1} />
    </FakeStore>
  );
};
