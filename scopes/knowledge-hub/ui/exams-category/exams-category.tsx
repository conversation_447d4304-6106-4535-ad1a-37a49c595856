import React from 'react';

import shallowequal from 'shallowequal';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps } from '@manyun/base-ui.ui.cascader';
import { Spin } from '@manyun/base-ui.ui.spin';

import { useExamsCategory } from '@manyun/knowledge-hub.hook.use-exams-category';

import styles from './exams-category.module.less';

export type ExamsCategoryProps = {
  style?: React.CSSProperties;
  /** 三级考试分类 ID */
  categoryCode: number;
};

const fallbackEntity = { name: '未知', parentId: null };

export function ExamsCategory({ style, categoryCode }: ExamsCategoryProps) {
  const [{ loading, entities }, { readExamCategory }] = useExamsCategory();
  const level3 = entities[categoryCode] || { name: categoryCode.toString(), parentId: null };
  const level2 = level3?.parentId ? entities[level3.parentId] ?? fallbackEntity : fallbackEntity;
  const level1 = level2?.parentId ? entities[level2.parentId] ?? fallbackEntity : fallbackEntity;

  const text = `${level1?.name}/${level2?.name}/${level3?.name}`;

  React.useEffect(() => {
    readExamCategory({
      policy: ids => (ids.includes(categoryCode) ? 'cache-only' : 'network-only'),
    });
  }, [categoryCode]);

  return (
    <Spin
      // Why this `style` does not work?
      style={style}
      spinning={loading}
    >
      <div className={styles.textContainer} title={text}>
        {text}
      </div>
    </Spin>
  );
}

export type ExamsCategoryCascaderProps = Omit<
  CascaderProps,
  'options' | 'defaultValue' | 'value'
> & {
  trigger?: 'onDidMount' | 'onFocus';
  defaultValue?: number[];
  value?: number[];
  /** 当组装完完整的三层分类时 */
  onMappingCompleted?(value: number[]): void;
};

export type CascaderRef = {
  focus: () => void;
  blur: () => void;
};

export const ExamsCategoryCascader = React.forwardRef(
  (
    {
      trigger = 'onFocus',
      defaultValue,
      onFocus,
      onMappingCompleted,
      ...props
    }: ExamsCategoryCascaderProps,
    ref?: React.Ref<CascaderRef>
  ) => {
    const { value, ...restProps } = props;

    const [internalValue, setInternalValue] = React.useState<number[] | undefined>(
      value || defaultValue
    );
    const [{ loading, entities, tree }, { readExamCategory }] = useExamsCategory({
      withTree: true,
      treeConsumer: 'Cascader',
    });

    const prevValue = usePrevious(value);
    React.useEffect(() => {
      if (!shallowequal(prevValue, value)) {
        setInternalValue(value);
      }
    }, [prevValue, value]);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        readExamCategory();
      }
    }, [trigger]);

    // 当只传入了一个分类 ID，则认为传入了三级考试分类
    // 需要组装完整的三层分类 ID 的数组
    React.useEffect(() => {
      if (
        value?.length === 1 &&
        entities[value[0]] &&
        internalValue?.length === 1 &&
        entities[internalValue[0]]
      ) {
        const defaultCategory = value[0];
        const level3 = entities[defaultCategory];
        const level2 = entities[level3!.parentId] || { id: undefined, parentId: -999 };
        const level1 = entities[level2.parentId] || { id: undefined, parentId: -999 };
        if (level2.id !== undefined && level1.id !== undefined) {
          const newValue: number[] = [level1.id, level2.id, level3!.id];
          if (typeof onMappingCompleted == 'function') {
            onMappingCompleted(newValue);
          } else {
            setInternalValue(newValue);
          }
        }
      }
    }, [value, entities, internalValue, onMappingCompleted]);

    return (
      <Cascader
        ref={ref}
        loading={loading}
        options={tree || []}
        defaultValue={defaultValue}
        value={internalValue}
        onFocus={evt => {
          if (trigger === 'onFocus') {
            readExamCategory();
          }
          onFocus?.(evt);
        }}
        {...restProps}
      />
    );
  }
);

ExamsCategoryCascader.displayName = 'ExamsCategoryCascader';

ExamsCategory.Cascader = ExamsCategoryCascader;

// TODO @Jerry create a hook component
function usePrevious<T>(value: T) {
  // The ref object is a generic container whose current property is mutable ...
  // ... and can hold any value, similar to an instance property on a class
  const ref = React.useRef<T>();
  // Store current value in ref
  React.useEffect(() => {
    ref.current = value;
  }, [value]); // Only re-run if value changes
  // Return previous value (happens before update in useEffect above)
  return ref.current;
}
