import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { ExamsCategory, ExamsCategoryCascader } from './exams-category';

export const BasicExamsCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <ExamsCategoryCascader />
    </FakeStore>
  </ConfigProvider>
);

export const TextExamsCategory = () => (
  <ConfigProvider>
    <FakeStore>
      <ExamsCategory categoryCode={3} />
    </FakeStore>
  </ConfigProvider>
);
