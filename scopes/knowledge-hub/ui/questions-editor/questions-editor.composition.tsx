import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { QuestionsEditor } from './questions-editor';

export const BatchQuestionsDeleter = () => (
  <ConfigProvider>
    <FakeStore>
      <QuestionsEditor
      // onParserData={function (data: any, error: string | null): void {
      //   throw new Error('Function not implemented.');
      // }}
      />
    </FakeStore>
  </ConfigProvider>
);
