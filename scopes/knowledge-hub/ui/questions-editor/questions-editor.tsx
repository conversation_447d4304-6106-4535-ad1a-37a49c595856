import React, { useEffect, useImperativeHandle, useState } from 'react';
import {
  AutoSizer,
  CellMeasurer,
  CellMeasurerCache,
  ListRowProps,
  List as VList,
} from 'react-virtualized';

import { DownOutlined, UpOutlined } from '@ant-design/icons/es/icons';
import debounce from 'lodash.debounce';

import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';

import { useQuestionsCreateFields } from '@manyun/knowledge-hub.hook.use-questions-creation';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { Question } from '@manyun/knowledge-hub.ui.question';
import { QuestionImport } from '@manyun/knowledge-hub.ui.question-import';

import styles from './questions-editor.module.less';
import { translateContentArr } from './questions-editor.utils';

export type QuestionsEditorProps = {
  onParserData?: (data: any) => void;
};

export const QuestionsEditor = React.forwardRef(
  (
    { onParserData }: QuestionsEditorProps,
    ref?: React.Ref<{ generatorData: (difficult?: number) => void }>
  ) => {
    const [setCreateFields, fields] = useQuestionsCreateFields();
    const [isShowRules, setIsShowRules] = useState(false);
    const [isShowExample, setIsShowExample] = useState(false);
    const [onlyError, setOnlyError] = useState(false);

    const [parserData, setParserData] = useState([]);
    const [error, setError] = useState(false);

    useImperativeHandle(ref, () => ({
      generatorData: generatorDataByText,
    }));

    const onChangeText = (e: any) => {
      const txt = e.target.value;
      setCreateFields({ text: txt });
    };

    const generatorDataByText = debounce((difficult?: number) => {
      const txt = fields.byFile.text || '';
      const arr: any[] = txt.split('\n');
      const generate = translateContentArr(arr.filter(o => o !== ''));
      if (generate.length > 0) {
        type CreateQuestions = Omit<BackendQuestion, 'id'> & {
          errors?: string[];
          order?: number;
        };
        const creteQuestions: CreateQuestions[] = generate.map(data => ({
          categoryCode: fields.byFile.defaultCategoryCode as string,
          title: data.question.content,
          type: data.dataType,
          difficulty: data.difficult
            ? data.difficult
            : difficult ?? (fields.byFile.defaultDifficulty as number) ?? 1,
          options: data.options.map(o => o.content),
          answers: [1, 2].includes(data.dataType)
            ? Array.from(new Set(data.answers))
            : data.answers,
          /**单多选题 答案去重 */
          autoGrade: [1, 2, 3, 4].includes(data.dataType) ? true : false,
          /**除简答题外 都是系统判题 */
          analysis: data.analysis,
          errors: data.errors.length > 0 ? data.errors : undefined,
          order: data.order,
        }));
        setParserData(creteQuestions as []);
        const isValid = generate.every(data => data.errors.length === 0);
        const isError = generate.some(data => data.question.title === '');
        if (isError) {
          setError(true);
        } else {
          setError(false);
        }
        if (isValid) {
          if (onParserData) {
            onParserData(creteQuestions);
          }
        } else {
          if (onParserData) {
            onParserData([]);
          }
        }
      } else {
        setParserData([]);
        if (onParserData) {
          onParserData([]);
        }
      }
    }, 700);

    useEffect(() => {
      generatorDataByText();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [fields.byFile.text]);

    const errorsData = parserData.filter((data: any) => (data.errors || []).length > 0);
    const visibleData = onlyError && errorsData.length > 0 ? errorsData : parserData;

    const cache = new CellMeasurerCache({ defaultHeight: 200, fixedWidth: true });

    const renderItem = ({
      index,
      columnIndex,
      key,
      parent,
      style,
    }: Pick<ListRowProps, 'index' | 'key' | 'style' | 'parent' | 'columnIndex'>) => {
      return (
        <CellMeasurer
          cache={cache}
          columnIndex={columnIndex}
          key={key}
          style={style}
          parent={parent}
          rowIndex={index}
        >
          {({ registerChild }) => (
            <div style={style}>
              <Question
                question={visibleData[index]}
                mode="create"
                index={(visibleData[index] as any).order || index}
              />
              <Divider type="horizontal" style={{ margin: '10px 0' }} />
            </div>
          )}
        </CellMeasurer>
      );
    };

    return (
      <div className={styles.container}>
        <Card
          bordered={false}
          title="输入区域"
          extra={
            <Space direction="horizontal">
              <QuestionImport
                onImport={parsedTxt => {
                  setCreateFields({ text: parsedTxt });
                }}
              />
              <Space
                direction="horizontal"
                onClick={() => {
                  setIsShowExample(!isShowExample);
                  setIsShowRules(false);
                }}
              >
                输入示例
                {isShowExample ? <UpOutlined /> : <DownOutlined />}
              </Space>
              <Space
                direction="horizontal"
                onClick={() => {
                  setIsShowRules(!isShowRules);
                  setIsShowExample(false);
                }}
              >
                输入规范
                {isShowRules ? <UpOutlined /> : <DownOutlined />}
              </Space>
            </Space>
          }
        >
          {(isShowRules || isShowExample) && (
            <div
              style={{
                border: '1px solid var(--border-color-base)',
                padding: '5px',
                fontSize: '13px',
                paddingLeft: '10px',
                borderRadius: '3px',
                marginBottom: '5px',
                maxHeight: '300px',
                overflow: 'auto',
              }}
            >
              {isShowRules && rulesDiv}
              {isShowExample && exampleDiv}
            </div>
          )}
          <Input.TextArea
            value={fields.byFile.text as string}
            id="previewLeft"
            onChange={val => {
              onChangeText(val);
            }}
            style={{ height: 'calc(100%)', appearance: 'none', resize: 'none' }}
          />
        </Card>
        <Card
          bordered={false}
          title={
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <span style={{ flex: 1 }}>
                展示区域{parserData.length > 0 && <span>(共{parserData.length}题)</span>}
              </span>
              {errorsData.length > 0 && (
                <span>
                  <span style={{ fontSize: '14px', color: 'var(--manyun-error-color-active)' }}>
                    {errorsData.length}处错误，
                  </span>
                  <span>
                    <span style={{ fontSize: '14px' }}>仅看错题：</span>
                    <Switch
                      checked={onlyError}
                      onChange={val => {
                        setOnlyError(val);
                      }}
                    />
                  </span>
                </span>
              )}
            </div>
          }
          bodyStyle={{ overflow: 'hidden' }}
        >
          <div id="previewRight" style={{ display: 'flex', height: '100%' }}>
            {visibleData.length === 0 && (
              <Empty
                style={{ flex: '1 1 ' }}
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description="暂无数据"
              />
            )}

            {visibleData.length > 0 &&
              (error ? (
                <Space direction="vertical" className={styles.errorContainer}>
                  <div>无法解析数据，请按照规范输入文本内容！</div>
                </Space>
              ) : (
                <div style={{ flex: '1 1 ' }}>
                  <AutoSizer>
                    {({ width, height }) => (
                      <VList
                        ref="List"
                        height={height}
                        overscanRowCount={10}
                        rowCount={visibleData.length}
                        deferredMeasurementCache={cache}
                        rowHeight={cache.rowHeight}
                        rowRenderer={renderItem}
                        width={width}
                      />
                    )}
                  </AutoSizer>
                </div>
              ))}
          </div>
        </Card>
      </div>
    );
  }
);

const rulesDiv = (
  <>
    输入规范
    <br /> 1、所有题型标号支持1.或1、或（1）三种格式。
    <br /> 2、除问答题外所有题型必须含有 “ 答案:”字段，且不能为空。
    <br /> 3、除问答题外所有题型 “ 解析:” 字段必需，且不能为空。
    <br /> 4、所有题型必须含有 “ 难度:”字段，且不能为空。
    <br /> 5、所有题型题目中包含图片，则将图片插入到指定位置即可。
    <br />
    6、选择题最少支持2个选项A,B，最多支持8个选项A,B,C,D,E,F,G,H且按照顺序使用。
    <br />
    7、选择题A-H这些选项号与内容之间要用、或 . 分开。
    <br />
    8、选择题答案中请勿加分隔符或者空格。
    <br />
    9、判断题答案支持 “错误”，“正确” 或者 “错”，“对”。
    <br />
    10、填空题仅支持题目中出现括号。
    <br />
    11、填空题目里的多个填空选项要用 “|” 分割，单个答案不用添加。
    <br />
    12、填空题的填空答案支持输入同义词，用&&连接多个同义词答案。
    <br />
  </>
);

const exampleDiv = (
  <>
    {/* 单选题 */}
    单选题示例：
    <br />
    1.驾驶人有下列哪种违法行为一次记6分
    <br /> A.使用其他车辆行驶证
    <br />
    B.饮酒后驾驶机动车
    <br /> C.车速超过规定时速50%以上
    <br /> D.违法占用应急车道行驶
    <br />
    答案:D
    <br />
    解析:请仔细阅读交规
    <br />
    难度: 易
    <br />
    <br />
    {/* 多选题 */}
    多选题示例：
    <br />
    1.驾驶人有下列哪种违法行为一次记6分
    <br /> A.使用其他车辆行驶证
    <br />
    B.饮酒后驾驶机动车
    <br /> C.车速超过规定时速50%以上
    <br /> D.违法占用应急车道行驶
    <br />
    答案:AB
    <br />
    解析:请仔细阅读交规
    <br />
    难度: 易
    <br />
    <br />
    {/* 判断题 */}
    判断题示例：
    <br />
    1.驾驶人使用其他车辆行驶证行为一次记6分？
    <br />
    答案:正确
    <br />
    解析:请仔细阅读交规
    <br />
    难度: 易
    <br />
    <br />
    {/* 填空题 */}
    填空题示例：
    <br />
    1.驾驶人()、()行为一次记6分？
    <br />
    答案:车速超过规定时速50%以上|违法占用应急车道行驶
    <br />
    解析:请仔细阅读交规
    <br />
    难度: 易
    <br />
    <br />
    {/* 问答题 */}
    问答题示例：
    <br />
    1.驾驶人哪些情况下行为一次记6分？
    <br />
    解析:请仔细阅读交规
    <br />
    难度: 易
    <br />
  </>
);
