import {
  BackendQuestion,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { QuestionDifficultyTextMapper } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

const QuestionDifficultyReverseMapper: Record<string, number> = Object.keys(
  QuestionDifficultyTextMapper as Record<string, string>
).reduce((newMapper, key) => {
  const newKey: string = (QuestionDifficultyTextMapper as Record<string, string>)[key] as string;
  newMapper[newKey] = Number(key);
  return newMapper;
}, {} as Record<string, number>);

export type GeneratorQuestion = {
  question: {
    title: string;
    content: string;
  };
  options: { prefix: string; content: string }[];
  correct: string;
  answers: string[];
  analysis: string;
  difficult: number;
  errors: string[];
  dataType: number;
  order: number;
};

export type GeneratorQuestion2 = BackendQuestion & {
  errors: string[];
};

const regex1 = /^([0-9]\d*(\.|\u3001)|\([0-9]\d*\)|\uFF08[0-9]\d*\uFF09)/; // 匹配题目序号
const regex2 = /[A-H](\u3001|\.)/; //匹配选项
const sigleType = /^[A-H]$/; //单选题
export const shortAnswerTitle = /\(\)|\uFF08\uFF09|\uFF08\u0020\uFF09/g; //匹配填空题 题干
const shortAnswerSplit = /[|\uff5c]/; //拆分填空题答案

// eslint-disable-next-line no-useless-escape
const colonSplitSearch = /\:|\uff1a/;
const titleSplitSearch = /\.|\u3001|\)|\uFF09/;
const optionSplitSearch = /\.|\u3001/;

/**
 * 根据规则将数组进行二维拆分
 * @param {*} arr
 */
export function translateContentArr(arr: any[]): GeneratorQuestion[] {
  const a: any[] = [];
  arr.forEach((txt, index) => {
    if (regex1.test(txt)) {
      a.push(index);
    }
    return null;
  });
  a.push(arr.length);
  const b: any[] = [];
  let i = 0;
  a.forEach(k => {
    if (k !== 0) {
      b.push(arr.slice(i, k));
      i = k;
    }
    return null;
  });
  const data: GeneratorQuestion[] = b.map((b1, index) => {
    return translateArrToObject(b1, index);
  });
  return data;
}

/**
 *  将数组转换成对象
 * @param {*} arr
 */
function translateArrToObject(arr: string[], index: number) {
  let obj: GeneratorQuestion = {
    question: {
      title: '',
      content: '',
    },
    options: [],
    analysis: '',
    difficult: 0,
    errors: [],
    dataType: 1,
    correct: '',
    answers: [],
    order: 0,
  };
  arr.forEach(element => {
    const ele = Trim(element);
    if (regex1.test(ele)) {
      /**题干 */
      const splitIndex = ele.search(titleSplitSearch);
      if (splitIndex > 0) {
        const content = Trim(ele.slice(splitIndex + 1));
        obj.question = {
          title: ele.slice(0, splitIndex),
          content: content,
        };
        if (content === '') {
          obj.errors.push('题干不能为空！');
        }
      }
    } else if (regex2.test(ele)) {
      const splitIndex = ele.search(optionSplitSearch);
      if (splitIndex > 0) {
        obj.options.push({
          prefix: ele.slice(0, splitIndex),
          content: Trim(ele.slice(splitIndex + 1)),
        });
      }
    } else if (validStartsWith(ele, '答案')) {
      const splitIndex = ele.search(colonSplitSearch);
      obj.correct = Trim(ele.slice(splitIndex + 1));
    } else if (validStartsWith(ele, '解析')) {
      const splitIndex = ele.search(colonSplitSearch);
      obj.analysis = Trim(ele.slice(splitIndex + 1));
    } else if (validStartsWith(ele, '难度')) {
      const splitIndex = ele.search(colonSplitSearch);
      const txt = ele.slice(splitIndex + 1) && Trim(ele.slice(splitIndex + 1));
      obj.difficult = QuestionDifficultyReverseMapper[txt];
    } else {
      if (Trim(ele) !== '') {
        obj.errors.push('输入有误！');
      }
    }
  });
  /**题目的序号 */
  obj.order = Number(index) + Number(1);
  setQuestionType(obj);
  return obj;
}

/**
 *  根据答案文本内容 以及问题内容进行题目类型判断
 * @param {*} obj
 */
function setQuestionType(obj: GeneratorQuestion) {
  if (['正确', '错误', '对', '错'].includes(obj.correct)) {
    obj.dataType = 3; //判断题
    obj.answers.push(['正确', '对'].includes(obj.correct) ? 'true' : 'false');
  } else if (obj.options.length > 0) {
    if (sigleType.test(obj.correct)) {
      obj.dataType = 1; //单选题
      const answer = obj.options.find(option => option.prefix === obj.correct)?.content;
      if (answer) obj.answers.push(answer);
    } else if (
      isIncludeArr(
        Array.from(Trim(obj.correct)),
        (Array.isArray(obj.options) ? obj.options : []).map((obj: any) => obj.prefix)
      )
    ) {
      obj.dataType = 2; //多选题
      obj.answers = obj.correct
        .split('')
        .map(
          answer =>
            (obj.options.find(option => option.prefix === answer) &&
              obj.options.find(option => option.prefix === answer)?.content) ||
            ''
        );
    } else {
      obj.dataType = 2; //多选题
    }
    /**错误验证 */
    const len = obj.options.length;
    if (len <= 1) {
      obj.errors.push('单选题或多选题选项至少两项！');
    }
    if (len > 8) {
      obj.errors.push('单选题或多选题选项至多八项！');
    }
    if (isRepeatArr(obj.options.map(opt => opt.content))) {
      obj.errors.push('单选题或多选题选项内容不能重复！');
    }
    /**end */
  } else if (shortAnswerTitle.test(obj.question.content)) {
    obj.dataType = 4; //填空题
    const hit = obj.question.content.match(shortAnswerTitle) || []; //获取命中次数
    obj.answers = obj.correct ? obj.correct.split(shortAnswerSplit) : [];
    const answerslen = obj.answers.length;
    if (answerslen > 0) {
      if (hit.length !== answerslen) {
        obj.errors.push('答案和题干()数量不匹配!');
      } else if (answerslen < 1) {
        obj.errors.push('填空题选项至少一项！');
      } else if (answerslen > 8) {
        obj.errors.push('填空题选项至多八项！');
      }
    }
    obj.options = [];
  } else {
    obj.dataType = 5; //问答题
    obj.answers = obj.correct ? [obj.correct] : [];
    obj.options = [];
  }

  /**检查答案是否为空 */
  if (obj.answers.length === 0 && obj.dataType !== BackendQuestionType.ESSAY) {
    obj.errors.push('答案不能为空(单选题或多选题请检查答案与选项是否一致)！');
  }
}

/**
 * 判断目标数组中是否包含源数组中所有元素
 * @param {*} source  源数组
 * @param {*} target  目标数组
 */
function isIncludeArr(source: any, target: any) {
  return (Array.isArray(source) ? source : []).every(val =>
    (Array.isArray(target) ? target : []).includes(val)
  );
}

function Trim(str: string) {
  return str.replace(/(^\s*)|(\s*$)/g, '');
}

function validStartsWith(txt: string, str: string) {
  return txt.startsWith(str + ':') || txt.startsWith(str + '\u3001') || txt.startsWith(str + '：');
}

/**
 * 判断字符串数组是否有重复元素
 * @param arrs
 * @returns boolean
 */
export function isRepeatArr(arrs: string[]): boolean {
  let hash: Record<string, boolean> = {};
  for (let i in arrs) {
    if (hash[arrs[i]]) {
      return true;
    }
    hash[arrs[i]] = true;
  }
  return false;
}
