import { qs } from '@manyun/base-ui.util.query-string';
import type { MyExamTypes } from '@manyun/knowledge-hub.state.exams';

export type CourseLearningRouteParams = {
  /** 课程ID */
  id: string;
  studyStatus: string;
};

/**
 * 课程学习页面路径
 */
export const LEARNING_SPECIFIC_COURSE_ROUTE_PATH =
  '/page/knowledge-hub/courses/:id/:studyStatus/learning';

export function generateLearningSpecificCourseRoutePath(id: number, studyStatus: string) {
  return LEARNING_SPECIFIC_COURSE_ROUTE_PATH.replace(':id', id.toString()).replace(
    ':studyStatus',
    studyStatus
  );
}

export type CourseMutatorRouteParams = {
  /** 课程ID */
  id?: string;
};

/**
 * 创建课程页面路径
 */
export const NEW_COURSE_ROUTE_PATH = '/page/knowledge-hub/courses/new';

/**
 * 修改课程页面路径
 */
export const EDIT_SPECIFIC_COURSE_ROUTE_PATH = '/page/knowledge-hub/courses/:id/edit';

export function generateEditSpecificCourseRoutePath(id: number) {
  return EDIT_SPECIFIC_COURSE_ROUTE_PATH.replace(':id', id.toString());
}

export const COURSES_ROUTE_PATH = '/page/knowledge-hub/courses';

export const COURSE_DETAIL_ROUTE_PATH = '/page/knowledge-hub/courses/:id';

export function generateCourseDetailRoutePath(id: number) {
  return COURSE_DETAIL_ROUTE_PATH.replace(':id', id.toString());
}

export const COURSEWARES_ROUTE_PATH = '/page/knowledge-hub/coursewares';

export type ExamRouteParams = {
  /** 考试 ID */
  id: string;
};

export const EXAM_ROUTE_PATH = '/page/knowledge-hub/exams/:id';

export function generateExamRoutePath(id: number) {
  return EXAM_ROUTE_PATH.replace(':id', id.toString());
}

export type NewExamSearchParams = {
  /** 试卷 ID */
  paperId?: string;
  /** Template Exam ID */
  templateId?: string;
};

export type ExamMutatorRouteParams = {
  /** 考试 ID */
  id?: string;
};

/**
 * 创建考试页面路径
 */
export const NEW_EXAM_ROUTE_PATH = '/page/knowledge-hub/exams/new';

/**
 * 修改考试页面路径
 */
export const EDIT_SPECIFIC_EXAM_ROUTE_PATH = '/page/knowledge-hub/exams/:id/edit';

export function generateNewExamURL({ paperId = '', templateId = '' }: NewExamSearchParams) {
  return `${NEW_EXAM_ROUTE_PATH}?paperId=${paperId}&templateId=${templateId}`;
}

export function generateEditSpecificExamRoutePath(id: number) {
  return EDIT_SPECIFIC_EXAM_ROUTE_PATH.replace(':id', id.toString());
}

export type ExamPaperRouteParams = {
  /** 仅当查看随堂测验的考卷时才会存在你 */
  courseId?: string;
  examId: string;
  id: string;
};

/**
 * 一般考试、补考-考卷
 */
export const EXAM_PAPER_URL = '/page/knowledge-hub/exams/:examId/paper/:id';

/**
 * 课程学习-随堂测验-考卷
 */
export const COURSE_LEARNING_TEST_PAPER_ROUTE_PATH =
  '/page/knowledge-hub/courses/:courseId/learning/test/:examId/paper/:id';

export const generateExamPaperLocation = ({ examId, id }: { examId: number; id: number }) =>
  EXAM_PAPER_URL.replace(':examId', examId.toString()).replace(':id', id.toString());

type TestPaperRoutePathGenFuncParams = {
  courseId: number;
  examId: number;
  id: number;
};

export const generateTestPaperRoutePath = ({
  courseId,
  examId,
  id,
}: TestPaperRoutePathGenFuncParams) =>
  COURSE_LEARNING_TEST_PAPER_ROUTE_PATH.replace(':courseId', courseId.toString())
    .replace(':examId', examId.toString())
    .replace(':id', id.toString());

export type ExamPaperResultRouteParams = {
  examId: string;
  id: string;
};

export const EXAM_PAPER_RESULT_PAGE_URL = '/page/knowledge-hub/exams/:examId/paper/:id/result';

export const generateExamPaperResultLocation = ({ id, examId }: ExamPaperResultRouteParams) =>
  EXAM_PAPER_RESULT_PAGE_URL.replace(':examId', String(examId)).replace(':id', String(id));

export const EXAMS_ROUTE_PATH = '/page/knowledge-hub/exams';
export type ExamsRouteParams = {
  /** 考试状态集合 */
  status?: string | null;
};
export const generateExamsRoutePath = (params: ExamsRouteParams = {}) =>
  `${EXAMS_ROUTE_PATH}?${qs.stringify(params, { arrayFormat: 'comma' })}`;

export const MY_COURSES_ROUTE_PATH = '/page/knowledge-hub/my-courses';

export const generateMyCoursesLocation = ({ courseName }: { courseName?: string }) => {
  let search = '?';
  if (courseName) {
    search += `courseName=${courseName}`;
  }
  return {
    pathname: MY_COURSES_ROUTE_PATH,
    search,
  };
};

export const MY_EXAMS_PAGE_URL = '/page/knowledge-hub/my-exams';

export const generateMyExamPaperLocation = ({
  type,
  examName,
}: {
  type?: MyExamTypes;
  examName?: string;
}) => {
  let search = '?';
  if (type) {
    search += `type=${type}&`;
  }
  if (examName) {
    search += `examName=${examName}`;
  }
  return {
    pathname: MY_EXAMS_PAGE_URL,
    search,
  };
};

export type PaperMutatorRouteParams = {
  /** 试卷 ID */
  id?: string;
};

/**
 * 创建试卷页面路径
 */
export const NEW_PAPER_ROUTE_PATH = '/page/knowledge-hub/papers/new';

/**
 * 修改考试页面路径
 */
export const EDIT_SPECIFIC_PAPER_ROUTE_PATH = '/page/knowledge-hub/papers/:id/edit';

export function generateEditSpecificPaperRoutePath(id: number) {
  return EDIT_SPECIFIC_PAPER_ROUTE_PATH.replace(':id', id.toString());
}

export const PAPERS_ROUTE_PATH = '/page/knowledge-hub/papers';

export const PENDING_MARK_EXAMS_ROUTE_PATH = '/page/knowledge-hub/pending-mark-exams';

export const QUESTIONS_ROUTE_PATH = '/page/knowledge-hub/questions';

export const QUESTIONS_CREATOR_ROUTE_PATH = '/page/knowledge-hub/questions-creator';

export const SKILL_PAGE_URL = '/page/knowledge-hub/skills/:id';

export const SKILLS_PAGE_URL = '/page/knowledge-hub/skills';

export type SkillsRouteParams = {
  /** 技能 ID */
  id: string;
};

export type TestPaperResultRouteParams = {
  id: string;
  examId: string;
  courseId: string;
};

export const TEST_PAPER_RESULT_ROUTE_PATH =
  '/page/knowledge-hub/courses/:courseId/:examId/paper/:id/result';

export const generateTestPaperResultRoutePath = ({
  id,
  examId,
  courseId,
}: TestPaperResultRouteParams) =>
  TEST_PAPER_RESULT_ROUTE_PATH.replace(':courseId', courseId)
    .replace(':examId', examId)
    .replace(':id', id);

export const WIKIS_ROUTE_PATH = '/page/knowledge-hub/wikis';

//试卷预览
export const PAPER_PREVIEW_URL = '/page/knowledge-hub/paper-preview/:id';

export type PaperPreviewRoutePathGenFuncParams = {
  id: string /**试卷id */;
};
export const generatePaperPreviewRoutePath = ({ id }: PaperPreviewRoutePathGenFuncParams) => {
  return PAPER_PREVIEW_URL.replace(':id', id.toString());
};

/**
 * 新建培训计划页面路径
 */
export const NEW_TRAIN_PLAN_ROUTE_PATH = '/page/knowledge-hub/train-plan/new';

/**
 * 修改培训计划路径
 */
export const EDIT_TRAIN_PLAN_ROUTE_PATH = '/page/knowledge-hub/train-plan/:id/edit';

export const TRAIN_PLANS_ROUTE_PATH = '/page/knowledge-hub/train-plans';

export const TRAIN_PLAN_ROUTE_PATH = '/page/knowledge-hub/train-plan/:id';

export function generateTrainPlanRoutePath(id: number) {
  return TRAIN_PLAN_ROUTE_PATH.replace(':id', id.toString());
}

export function generateEditTrainPlanRoutePath(id: number) {
  return EDIT_TRAIN_PLAN_ROUTE_PATH.replace(':id', id.toString());
}

export const generateEditTrainPlanUrl = ({ id, name }: { id: number; name?: string }) =>
  `${generateEditTrainPlanRoutePath(id)}?${qs.stringify({ name })}`;
