/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "hook/use-courses": {
        "name": "hook/use-courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-courses"
    },
    "hook/use-courses-category": {
        "name": "hook/use-courses-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-courses-category"
    },
    "hook/use-courses-fields": {
        "name": "hook/use-courses-fields",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-courses-fields"
    },
    "hook/use-courses-selected": {
        "name": "hook/use-courses-selected",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-courses-selected"
    },
    "hook/use-coursewares": {
        "name": "hook/use-coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-coursewares"
    },
    "hook/use-coursewares-category": {
        "name": "hook/use-coursewares-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-coursewares-category"
    },
    "hook/use-coursewares-fields": {
        "name": "hook/use-coursewares-fields",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-coursewares-fields"
    },
    "hook/use-coursewares-mutation": {
        "name": "hook/use-coursewares-mutation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-coursewares-mutation"
    },
    "hook/use-coursewares-selected": {
        "name": "hook/use-coursewares-selected",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-coursewares-selected"
    },
    "hook/use-exams": {
        "name": "hook/use-exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-exams"
    },
    "hook/use-exams-category": {
        "name": "hook/use-exams-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-exams-category"
    },
    "hook/use-exams-fields": {
        "name": "hook/use-exams-fields",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-exams-fields"
    },
    "hook/use-exams-selected": {
        "name": "hook/use-exams-selected",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-exams-selected"
    },
    "hook/use-paper-mutation": {
        "name": "hook/use-paper-mutation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-paper-mutation"
    },
    "hook/use-papers": {
        "name": "hook/use-papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-papers"
    },
    "hook/use-papers-category": {
        "name": "hook/use-papers-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-papers-category"
    },
    "hook/use-papers-fields": {
        "name": "hook/use-papers-fields",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-papers-fields"
    },
    "hook/use-papers-gen-func": {
        "name": "hook/use-papers-gen-func",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-papers-gen-func"
    },
    "hook/use-questions": {
        "name": "hook/use-questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-questions"
    },
    "hook/use-questions-category": {
        "name": "hook/use-questions-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-questions-category"
    },
    "hook/use-questions-creation": {
        "name": "hook/use-questions-creation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-questions-creation"
    },
    "hook/use-questions-difficulty": {
        "name": "hook/use-questions-difficulty",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-questions-difficulty"
    },
    "hook/use-questions-fields": {
        "name": "hook/use-questions-fields",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-questions-fields"
    },
    "hook/use-questions-type": {
        "name": "hook/use-questions-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-questions-type"
    },
    "hook/use-skill-exams-category-linking": {
        "name": "hook/use-skill-exams-category-linking",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skill-exams-category-linking"
    },
    "hook/use-skill-issuing-avaiable-users": {
        "name": "hook/use-skill-issuing-avaiable-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skill-issuing-avaiable-users"
    },
    "hook/use-skill-linked-exams-categories": {
        "name": "hook/use-skill-linked-exams-categories",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skill-linked-exams-categories"
    },
    "hook/use-skill-revoke": {
        "name": "hook/use-skill-revoke",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skill-revoke"
    },
    "hook/use-skills": {
        "name": "hook/use-skills",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skills"
    },
    "hook/use-skills-category": {
        "name": "hook/use-skills-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skills-category"
    },
    "hook/use-skills-deletion": {
        "name": "hook/use-skills-deletion",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skills-deletion"
    },
    "hook/use-skills-mutation": {
        "name": "hook/use-skills-mutation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "hook/use-skills-mutation"
    },
    "page/course-detail": {
        "name": "page/course-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/course-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/course-learning": {
        "name": "page/course-learning",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/course-learning",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/course-mutator": {
        "name": "page/course-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/course-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/courses": {
        "name": "page/courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/courses",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/coursewares": {
        "name": "page/coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/coursewares",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/exam": {
        "name": "page/exam",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/exam",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/exam-mutator": {
        "name": "page/exam-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/exam-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/exam-paper": {
        "name": "page/exam-paper",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/exam-paper",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/exam-paper-result": {
        "name": "page/exam-paper-result",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/exam-paper-result",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/exams": {
        "name": "page/exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/exams",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/my-courses": {
        "name": "page/my-courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/my-courses",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/my-exams": {
        "name": "page/my-exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/my-exams",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/paper-mutator": {
        "name": "page/paper-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/paper-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/paper-preview": {
        "name": "page/paper-preview",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/paper-preview",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/papers": {
        "name": "page/papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/papers",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/pending-mark-exams": {
        "name": "page/pending-mark-exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/pending-mark-exams",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/questions": {
        "name": "page/questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/questions",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/questions-creator": {
        "name": "page/questions-creator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/questions-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/skill": {
        "name": "page/skill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/skill",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/skills": {
        "name": "page/skills",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/skills",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/test-paper-result": {
        "name": "page/test-paper-result",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/test-paper-result",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/train-plan": {
        "name": "page/train-plan",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/train-plan",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/train-plan-mutator": {
        "name": "page/train-plan-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/train-plan-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/train-plans": {
        "name": "page/train-plans",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/train-plans",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/wikis": {
        "name": "page/wikis",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "page/wikis",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/knowledge-hub-routes": {
        "name": "route/knowledge-hub-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "route/knowledge-hub-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.0": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/dcexam/add-exam-users": {
        "name": "service/dcexam/add-exam-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/add-exam-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/analysis-question": {
        "name": "service/dcexam/analysis-question",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/analysis-question",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/associate-exam-categories": {
        "name": "service/dcexam/associate-exam-categories",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/associate-exam-categories",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/cancle-associated-exam": {
        "name": "service/dcexam/cancle-associated-exam",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/cancle-associated-exam",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/cancle-exam": {
        "name": "service/dcexam/cancle-exam",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/cancle-exam",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/delete-courses": {
        "name": "service/dcexam/delete-courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/delete-courses",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/delete-coursewares": {
        "name": "service/dcexam/delete-coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/delete-coursewares",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/delete-knowledge-hub-category": {
        "name": "service/dcexam/delete-knowledge-hub-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/delete-knowledge-hub-category",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/delete-papers": {
        "name": "service/dcexam/delete-papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/delete-papers",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/delete-questions": {
        "name": "service/dcexam/delete-questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/delete-questions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/delete-skill": {
        "name": "service/dcexam/delete-skill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/delete-skill",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/export-question": {
        "name": "service/dcexam/export-question",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/export-question",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-associated-exam-categories": {
        "name": "service/dcexam/fetch-associated-exam-categories",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-associated-exam-categories",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-associated-users": {
        "name": "service/dcexam/fetch-associated-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-associated-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-available-users": {
        "name": "service/dcexam/fetch-available-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-available-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-course": {
        "name": "service/dcexam/fetch-course",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-course",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-course-learning": {
        "name": "service/dcexam/fetch-course-learning",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-course-learning",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-course-student": {
        "name": "service/dcexam/fetch-course-student",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-course-student",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-course-users-picker": {
        "name": "service/dcexam/fetch-course-users-picker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-course-users-picker",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.22": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/dcexam/fetch-courses": {
        "name": "service/dcexam/fetch-courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-courses",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-courseware-authorized-users": {
        "name": "service/dcexam/fetch-courseware-authorized-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-courseware-authorized-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-courseware-version-records": {
        "name": "service/dcexam/fetch-courseware-version-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-courseware-version-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-coursewares": {
        "name": "service/dcexam/fetch-coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-coursewares",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exam": {
        "name": "service/dcexam/fetch-exam",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exam",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exam-count": {
        "name": "service/dcexam/fetch-exam-count",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exam-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exam-failed-users": {
        "name": "service/dcexam/fetch-exam-failed-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exam-failed-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exam-paper": {
        "name": "service/dcexam/fetch-exam-paper",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exam-paper",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exam-paper-users": {
        "name": "service/dcexam/fetch-exam-paper-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exam-paper-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exam-result": {
        "name": "service/dcexam/fetch-exam-result",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exam-result",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-exams": {
        "name": "service/dcexam/fetch-exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-exams",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-knowledge-hub-category": {
        "name": "service/dcexam/fetch-knowledge-hub-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-knowledge-hub-category",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-paper": {
        "name": "service/dcexam/fetch-paper",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-paper",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-papers": {
        "name": "service/dcexam/fetch-papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-papers",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-preview-paper": {
        "name": "service/dcexam/fetch-preview-paper",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-preview-paper",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-question": {
        "name": "service/dcexam/fetch-question",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-question",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-question-exams": {
        "name": "service/dcexam/fetch-question-exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-question-exams",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-questions": {
        "name": "service/dcexam/fetch-questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-questions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-questions-count": {
        "name": "service/dcexam/fetch-questions-count",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-questions-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-skills": {
        "name": "service/dcexam/fetch-skills",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-skills",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/fetch-user-skills": {
        "name": "service/dcexam/fetch-user-skills",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/fetch-user-skills",
        "nextVersion": {
            "version": "patch",
            "message": "tag -v",
            "username": "chenjun",
            "email": "<EMAIL>"
        },
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/grant-courseware-permissions": {
        "name": "service/dcexam/grant-courseware-permissions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/grant-courseware-permissions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/issue-users-skill": {
        "name": "service/dcexam/issue-users-skill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/issue-users-skill",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-courses": {
        "name": "service/dcexam/mutate-courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-courses",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-coursewares": {
        "name": "service/dcexam/mutate-coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-coursewares",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-exam-paper": {
        "name": "service/dcexam/mutate-exam-paper",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-exam-paper",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-exams": {
        "name": "service/dcexam/mutate-exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-exams",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-knowledge-hub-category": {
        "name": "service/dcexam/mutate-knowledge-hub-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-knowledge-hub-category",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-papers": {
        "name": "service/dcexam/mutate-papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-papers",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-questions": {
        "name": "service/dcexam/mutate-questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-questions",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/mutate-skill": {
        "name": "service/dcexam/mutate-skill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/mutate-skill",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/request-makeup-test": {
        "name": "service/dcexam/request-makeup-test",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/request-makeup-test",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/review-course": {
        "name": "service/dcexam/review-course",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/review-course",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/revoke-users-skill": {
        "name": "service/dcexam/revoke-users-skill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/revoke-users-skill",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/dcexam/save-course-images": {
        "name": "service/dcexam/save-course-images",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "service/dcexam/save-course-images",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/courses": {
        "name": "state/courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "state/courses"
    },
    "state/coursewares": {
        "name": "state/coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "state/coursewares"
    },
    "state/exams": {
        "name": "state/exams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "state/exams"
    },
    "state/papers": {
        "name": "state/papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "state/papers"
    },
    "state/questions": {
        "name": "state/questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "state/questions"
    },
    "state/skills": {
        "name": "state/skills",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "state/skills"
    },
    "ui/course": {
        "name": "ui/course",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/course",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/course-images-view-button": {
        "name": "ui/course-images-view-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/course-images-view-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courses": {
        "name": "ui/courses",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courses",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courses-batch-updator": {
        "name": "ui/courses-batch-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courses-batch-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courses-category": {
        "name": "ui/courses-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courses-category",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courses-deleter": {
        "name": "ui/courses-deleter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courses-deleter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courses-filters": {
        "name": "ui/courses-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courses-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courses-state": {
        "name": "ui/courses-state",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courses-state",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courseware-mutator": {
        "name": "ui/courseware-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courseware-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/courseware-version-update-records": {
        "name": "ui/courseware-version-update-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/courseware-version-update-records",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/coursewares": {
        "name": "ui/coursewares",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/coursewares",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/coursewares-batch-updator": {
        "name": "ui/coursewares-batch-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/coursewares-batch-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/coursewares-card": {
        "name": "ui/coursewares-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/coursewares-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/coursewares-category": {
        "name": "ui/coursewares-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/coursewares-category",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/coursewares-deleter": {
        "name": "ui/coursewares-deleter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/coursewares-deleter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/coursewares-filters": {
        "name": "ui/coursewares-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/coursewares-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exam-gallery-cover": {
        "name": "ui/exam-gallery-cover",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exam-gallery-cover",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exam-mutator": {
        "name": "ui/exam-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exam-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exam-paper-creator": {
        "name": "ui/exam-paper-creator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exam-paper-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exam-result": {
        "name": "ui/exam-result",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exam-result",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exams-batch-updator": {
        "name": "ui/exams-batch-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exams-batch-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exams-category": {
        "name": "ui/exams-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exams-category",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exams-duplicator": {
        "name": "ui/exams-duplicator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exams-duplicator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exams-makeup-test-requestor": {
        "name": "ui/exams-makeup-test-requestor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exams-makeup-test-requestor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exams-state": {
        "name": "ui/exams-state",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exams-state",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/exmas-canceller": {
        "name": "ui/exmas-canceller",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/exmas-canceller",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/filters-courses-modal": {
        "name": "ui/filters-courses-modal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/filters-courses-modal",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/group-users-picker": {
        "name": "ui/group-users-picker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/group-users-picker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/make-up-exam": {
        "name": "ui/make-up-exam",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/make-up-exam",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/paper-gen-func": {
        "name": "ui/paper-gen-func",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/paper-gen-func",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/paper-questions-group": {
        "name": "ui/paper-questions-group",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/paper-questions-group",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/paper-questions-group-list": {
        "name": "ui/paper-questions-group-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/paper-questions-group-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/paper-questions-type-config-mutator": {
        "name": "ui/paper-questions-type-config-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/paper-questions-type-config-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers": {
        "name": "ui/papers",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers-batch-updator": {
        "name": "ui/papers-batch-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers-batch-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers-category": {
        "name": "ui/papers-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers-category",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers-deleter": {
        "name": "ui/papers-deleter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers-deleter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers-marking-mode": {
        "name": "ui/papers-marking-mode",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers-marking-mode",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers-mutation-prepare": {
        "name": "ui/papers-mutation-prepare",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers-mutation-prepare",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/papers-picker": {
        "name": "ui/papers-picker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/papers-picker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/question": {
        "name": "ui/question",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/question",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/question-editor": {
        "name": "ui/question-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/question-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/question-import": {
        "name": "ui/question-import",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/question-import",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/question-type": {
        "name": "ui/question-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/question-type",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/question-updator": {
        "name": "ui/question-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/question-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/questions": {
        "name": "ui/questions",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/questions",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/questions-category": {
        "name": "ui/questions-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/questions-category",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/questions-deleter": {
        "name": "ui/questions-deleter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/questions-deleter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/questions-difficulty": {
        "name": "ui/questions-difficulty",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/questions-difficulty",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/questions-editor": {
        "name": "ui/questions-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/questions-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/questions-filters": {
        "name": "ui/questions-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/questions-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/quetions-picker": {
        "name": "ui/quetions-picker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/quetions-picker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/qustions-batch-updator": {
        "name": "ui/qustions-batch-updator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/qustions-batch-updator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/review-course-modal": {
        "name": "ui/review-course-modal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/review-course-modal",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/rich-editor": {
        "name": "ui/rich-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/rich-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill": {
        "name": "ui/skill",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill-exams-category-linker": {
        "name": "ui/skill-exams-category-linker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill-exams-category-linker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill-exams-category-unlinker": {
        "name": "ui/skill-exams-category-unlinker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill-exams-category-unlinker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill-issued-users-filters": {
        "name": "ui/skill-issued-users-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill-issued-users-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill-linked-exams-categories": {
        "name": "ui/skill-linked-exams-categories",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill-linked-exams-categories",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill-mutator": {
        "name": "ui/skill-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skill-revoker": {
        "name": "ui/skill-revoker",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skill-revoker",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skills-category": {
        "name": "ui/skills-category",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skills-category",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skills-deleter": {
        "name": "ui/skills-deleter",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skills-deleter",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skills-filters": {
        "name": "ui/skills-filters",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skills-filters",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skills-issuer": {
        "name": "ui/skills-issuer",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skills-issuer",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/skills-level": {
        "name": "ui/skills-level",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/skills-level",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/upload-course-images-modal": {
        "name": "ui/upload-course-images-modal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.knowledge-hub",
        "mainFile": "index.ts",
        "rootDir": "ui/upload-course-images-modal",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "$schema-version": "14.9.0"
}
