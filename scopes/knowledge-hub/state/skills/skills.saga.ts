import type { Task } from 'redux-saga';
import { call, cancel, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deleteSkillWeb } from '@manyun/knowledge-hub.service.dcexam.delete-skill';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { fetchSkillsWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';
import { mutateSkillWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-skill';

import {
  deleteSkillAction,
  getSkillCategoriesAction,
  getSkillsAction,
  mutateSkillAction,
  skillsSliceActions,
} from './skills.action';
import { selectSkills, selectSkillsCategory } from './skills.selector';
import { getStaticReadPolicy } from './skills.util';

/** Workers */

export function* getSkillCategoriesSaga({
  payload: { withCount },
}: ReturnType<typeof getSkillCategoriesAction>) {
  const { error, data }: SagaReturnType<typeof fetchKnowledgeHubCategoryWeb> = yield call<
    typeof fetchKnowledgeHubCategoryWeb
  >(fetchKnowledgeHubCategoryWeb, {
    categoryType: BackendCategoryType.CERT,
    needNum: withCount,
  });
  if (error) {
    message.error(error);
    return;
  }
  yield put(skillsSliceActions.setCategories({ categories: data }));
}

export function* getSkillsSaga() {
  const existing: SagaReturnType<ReturnType<typeof selectSkills>> = yield select(selectSkills());
  const fileds = existing.fields;
  yield put(skillsSliceActions.fetchSkillsStart(fileds));
  const params: any = {};
  if (fileds?.categoryCode && Array.isArray(fileds.categoryCode) && fileds.categoryCode.length) {
    params.categoryCode = fileds.categoryCode[fileds.categoryCode.length - 1];
  }
  if (fileds?.name) {
    params.name = fileds.name.trim();
  }
  if (fileds?.level) {
    params.level = fileds.level;
  }
  const { error, data }: SagaReturnType<typeof fetchSkillsWeb> = yield call<typeof fetchSkillsWeb>(
    fetchSkillsWeb,
    params
  );
  if (error) {
    message.error(error.message);
    yield put(skillsSliceActions.fetchSkillsError());
    return;
  }
  yield put(skillsSliceActions.setSkills({ skills: data.data }));
}

export function* deleteSkillSaga({ payload: { id } }: ReturnType<typeof deleteSkillAction>) {
  const { error }: SagaReturnType<typeof deleteSkillWeb> = yield call<typeof deleteSkillWeb>(
    deleteSkillWeb,
    {
      id: Number(id),
    }
  );

  if (error) {
    message.error(error.message);
    return;
  }
  message.success('删除成功！');
  yield put(getSkillsAction());
}

export function* mutateSkillSaga({
  payload: { id, categoryCode, level, name, limitTime, limitUnit, callback },
}: ReturnType<typeof mutateSkillAction>) {
  const { error }: SagaReturnType<typeof mutateSkillWeb> = yield call(mutateSkillWeb as any, {
    id,
    categoryCode,
    level,
    name,
    limitTime,
    limitUnit,
  });

  if (error) {
    message.error(error.message);
    return;
  }
  if (callback) {
    callback(true);
  }
  message.success(id || id === 0 ? '修改成功！' : '创建成功！');
  yield put(getSkillsAction());
}

/** Watchers */

function* watchGetSkillCategories() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getSkillCategoriesAction> = yield take(
      getSkillCategoriesAction.type
    );
    const existing: SagaReturnType<ReturnType<typeof selectSkillsCategory>> =
      yield select(selectSkillsCategory());
    const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.ids);
    if (staticPolicy === 'network-only' && !existing.loading) {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(skillsSliceActions.fetchCategoriesStart({}));
      lastTask = yield fork(getSkillCategoriesSaga, action);
    }
  }
}

function* watchGetSkills() {
  yield takeLatest(getSkillsAction.type, getSkillsSaga);
}

function* watchDeleteSkill() {
  yield takeLatest(deleteSkillAction.type, deleteSkillSaga);
}

function* watchMutateSkill() {
  yield takeLatest(mutateSkillAction.type, mutateSkillSaga);
}

export default [
  fork(watchGetSkillCategories),
  fork(watchGetSkills),
  fork(watchDeleteSkill),
  fork(watchMutateSkill),
];
