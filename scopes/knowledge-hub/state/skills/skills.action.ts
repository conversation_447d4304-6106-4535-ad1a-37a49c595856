import { createAction } from '@reduxjs/toolkit';

import type { ReadSkillsCategoriesPolicy } from '@manyun/knowledge-hub.hook.use-skills-category';
import type { SkillLimitUnit } from '@manyun/knowledge-hub.service.dcexam.mutate-skill';

import { skillsSlice } from './skills.slice';

const prefix = skillsSlice.name;

export const skillsSliceActions = skillsSlice.actions;

export const getSkillCategoriesAction = createAction<{
  policy?: ReadSkillsCategoriesPolicy;
  withCount: boolean;
}>(prefix + '/GET_SKILLS_CATEGORY');

export const getSkillsAction = createAction<undefined>(prefix + '/GET_SKILLS');

export const deleteSkillAction = createAction<{ id: number | string }>(prefix + '/DELETE_SKILL');

export const mutateSkillAction = createAction<{
  id?: number | string;
  categoryCode: string | number;
  level: string;
  name: string;
  limitTime: number;
  limitUnit: SkillLimitUnit;
  callback?: (result: boolean) => void;
}>(prefix + '/MUTATE_SKILL');
