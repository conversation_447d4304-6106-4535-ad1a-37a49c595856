import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import type { BackendSkills, ServiceQ } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';

// type Skills = {
//   id: number | string;
//   categoryCode: string;
//   name: string;
//   level: string;
//   gmtModified?: number;
// };

export type AllSkills = {
  fields: ServiceQ;
  loading: boolean;
  ids: any[];
};

export type SkillsSliceState = {
  entities: Record<string, BackendSkills>;

  all: AllSkills;

  category: {
    entities: Record<number, Category>;
    ids: any[];
    loading: boolean;
  };
};

type SkillsSliceCaseReducers = {
  fetchCategoriesStart: CaseReducer<SkillsSliceState, PayloadAction<any>>;
  setCategories: CaseReducer<SkillsSliceState, PayloadAction<{ categories: Category[] }>>;
  setFields: CaseReducer<SkillsSliceState, PayloadAction<ServiceQ>>;
  resetFields: CaseReducer<SkillsSliceState, PayloadAction<undefined>>;
  fetchSkillsStart: CaseReducer<SkillsSliceState, PayloadAction<any>>;
  setSkills: CaseReducer<
    SkillsSliceState,
    PayloadAction<{
      skills: BackendSkills[];
    }>
  >;
  fetchSkillsError: CaseReducer<SkillsSliceState, PayloadAction<undefined>>;
};

export const skillsSlice = createSlice<
  SkillsSliceState,
  SkillsSliceCaseReducers,
  'knowledge-hub.skills'
>({
  name: 'knowledge-hub.skills',
  initialState: {
    entities: {},
    all: getInitialAllSkills(),
    category: {
      entities: {},
      ids: [],
      loading: false,
    },
  },
  reducers: {
    fetchCategoriesStart(sliceState, payload: {}) {
      sliceState.category.loading = true;
    },

    setCategories(sliceState, { payload: { categories } }) {
      const ids: any[] = [];
      categories.forEach(category => {
        ids.push(category.id);
        sliceState.category.entities[category.id] = category;
      });
      sliceState.category.ids = ids;
      sliceState.category.loading = false;
    },

    setFields(sliceState, { payload }) {
      sliceState['all'].fields = { ...sliceState['all'].fields, ...payload };
    },
    resetFields(sliceState) {
      sliceState['all'].fields = {};
    },
    fetchSkillsStart(sliceState) {
      sliceState['all'].loading = true;
      sliceState['all'].ids = [];
    },
    setSkills(sliceState, { payload: { skills } }) {
      const ids: any[] = [];
      skills.forEach(skill => {
        ids.push(skill.id);
        // cache update performance optimization
        const existingSkill = sliceState.entities[skill.id!];
        if (
          existingSkill &&
          existingSkill.gmtModified === skill.gmtModified &&
          existingSkill.gmtModified !== undefined
        ) {
          return;
        }
        sliceState.entities[skill.id!] = skill;
      });
      sliceState['all'].ids = ids;
      sliceState['all'].loading = false;
    },
    fetchSkillsError(sliceState) {
      sliceState['all'].loading = false;
    },
  },
});

export function getInitialAllSkills(): AllSkills {
  return {
    loading: false,
    ids: [],
    fields: {},
  };
}
