---
description: 'Skills redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import skillsSliceReducer from '@manyun/[scope].state.skills';

const rootReducer = {
  ...skillsSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { skillsWatchers } from '@manyun/[scope].state.skills';

const function* rootSaga() {
  yield all(
    ...skillsWatchers,
    // other sagas...
  );
};
```