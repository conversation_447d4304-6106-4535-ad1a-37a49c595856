import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

import { skillsSlice } from './skills.slice';
import type { SkillsSliceState } from './skills.slice';

export const selectSkillsEntities =
  (ids?: string[] | number[]) => (storeState: { [skillsSlice.name]: SkillsSliceState }) => {
    const { entities } = storeState[skillsSlice.name];
    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[id]);
    }
    return ids.map(id => entities[id]);
  };

export const selectSkills = () => (storeState: { [skillsSlice.name]: SkillsSliceState }) => {
  return storeState[skillsSlice.name]['all'];
};

export const selectSkillEntity =
  (id?: number | string) => (storeState: { [skillsSlice.name]: SkillsSliceState }) => {
    const { entities } = storeState[skillsSlice.name];

    if (id || id === 0) {
      return entities[id];
    } else {
      return {
        categoryCode: '',
        name: '',
        level: '',
      };
    }
  };

export const selectSkillsCategory =
  () =>
  (storeState: {
    [skillsSlice.name]: SkillsSliceState;
  }): { entities: Record<number, Category>; ids: number[]; loading: boolean } =>
    storeState[skillsSlice.name].category;

export const selectSkillsCategoryEntities =
  (ids?: number[]) => (storeState: { [skillsSlice.name]: SkillsSliceState }) => {
    const {
      category: { entities },
    } = storeState[skillsSlice.name];
    if (ids === undefined) return Object.keys(entities).map(id => entities[Number(id)]);

    return ids.map(id => entities[id]);
  };
