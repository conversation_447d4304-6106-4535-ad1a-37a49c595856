import { expectSaga } from 'redux-saga-test-plan';

import { getInitialAllSkills, skillsSlice } from './skills.slice';
import type { SkillsSliceState } from './skills.slice';

test('should return the initial state', () => {
  expect(skillsSlice.reducer(undefined, {} as any)).toEqual<SkillsSliceState>({
    entities: {},
    all: getInitialAllSkills(),
    category: {
      entities: {},
      ids: [],
      loading: false,
    },
  });
});
