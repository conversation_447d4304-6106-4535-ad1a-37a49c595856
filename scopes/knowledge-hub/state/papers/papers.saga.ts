/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import type { Task } from 'redux-saga';
import { call, cancel, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deletePapersWeb } from '@manyun/knowledge-hub.service.dcexam.delete-papers';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { fetchPaperWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-paper';
import {
  BackendPapersGenFun,
  fetchPapersWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import type { Paper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { mutatePapersWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-papers';

import {
  createPaperAction,
  deletePapersAction,
  getPaperAction,
  getPaperCategoriesAction,
  getPapersAction,
  papersSliceActions,
  updatePapersAction,
} from './papers.action';
import { selectAllTypePapers, selectPaperCategory } from './papers.selector';
import { getInitialCreatePaper } from './papers.slice';
import { getStaticReadPolicy } from './papers.util';

/**Workers */

export function* getPaperCategoriesSaga({
  payload: { withCount, callback },
}: ReturnType<typeof getPaperCategoriesAction>) {
  const { error, data }: SagaReturnType<typeof fetchKnowledgeHubCategoryWeb> = yield call<
    typeof fetchKnowledgeHubCategoryWeb
  >(fetchKnowledgeHubCategoryWeb, {
    categoryType: BackendCategoryType.PAPER,
    needNum: withCount,
  });
  if (callback) {
    callback();
  }
  if (error) {
    message.error(error);
    yield put(papersSliceActions.fetchCategoriesEnd({}));
    return;
  }
  yield put(papersSliceActions.setCategories({ categories: data }));
}

export function* getPapersSaga({}) {
  const existing: SagaReturnType<ReturnType<typeof selectAllTypePapers>> = yield select(
    selectAllTypePapers()
  );
  yield put(papersSliceActions.fetchPapersStart(existing.fields));

  if (existing.selectedIds.length > 0) {
    yield put(papersSliceActions.setSelectedIds({ selectedIds: [] }));
  }
  const { genFun, categoryCodes, name, timeRange, page, pageSize } = existing.fields;
  const { error, data }: SagaReturnType<typeof fetchPapersWeb> = yield call<typeof fetchPapersWeb>(
    fetchPapersWeb,
    {
      categoryCodes,
      genFun,
      name,
      timeRange,
      page: page!,
      pageSize: pageSize!,
    }
  );

  if (error) {
    message.error(error.message);
    yield put(papersSliceActions.fetchPapersError({ error }));
    return;
  }

  yield put(papersSliceActions.setPapers({ papers: data.data, total: data.total }));
}

export function* getPaperSaga({
  payload: { paperId, needDetail = true, callback },
}: ReturnType<typeof getPaperAction>) {
  const { data, error }: SagaReturnType<typeof fetchPaperWeb> = yield call(fetchPaperWeb, {
    paperId,
    needDetail,
  });
  if (error) {
    message.error(error.message);
    callback(null);
    return;
  }
  callback(data as Paper);
}

export function* deletePapersSaga({
  payload: { id, callback },
}: ReturnType<typeof deletePapersAction>) {
  let deleteIds: number[] = [];
  if (id) {
    deleteIds.push(id);
  } else {
    const papers: SagaReturnType<ReturnType<typeof selectAllTypePapers>> = yield select(
      selectAllTypePapers()
    );
    deleteIds = papers.selectedIds;
  }

  const { error } = yield call(deletePapersWeb, { paperIds: deleteIds });

  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }

  message.success('试卷删除成功!');
  callback(true);
  if (id === undefined) yield put(papersSliceActions.setSelectedIds({ selectedIds: [] }));
  yield put(getPapersAction());
}

export function* updatePapersSaga({
  payload: { paper, categoryCode, callback },
}: ReturnType<typeof updatePapersAction>) {
  let updatePaperEntities: Paper | Pick<Paper, 'id' | 'categoryCode'>[];
  if (paper) {
    //更新 验证试卷
    const isValid = validMutatePaper(paper);
    if (!isValid) {
      callback(false);
      return;
    }
    updatePaperEntities = paper;
  } else {
    //批量更新
    const allTypeQuestion: SagaReturnType<ReturnType<typeof selectAllTypePapers>> = yield select(
      selectAllTypePapers()
    );
    updatePaperEntities = allTypeQuestion.selectedIds.map(id => ({
      id,
      categoryCode,
    })) as Pick<Paper, 'id' | 'categoryCode'>[];
  }

  const { error } = yield call(mutatePapersWeb, updatePaperEntities);
  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }

  message.success('试卷更新成功!');
  callback(true);
  if (paper === undefined) {
    yield put(papersSliceActions.setSelectedIds({ selectedIds: [] }));
    yield put(getPapersAction());
  }
}

export function* createPaperSaga({
  payload: { paper, callback },
}: ReturnType<typeof createPaperAction>) {
  /**试卷 验证 */
  const isValid = validMutatePaper(paper);
  if (!isValid) {
    callback(false);
    return;
  }
  const { error } = yield call(mutatePapersWeb, paper);
  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }
  message.success('试卷创建成功!');
  yield put(papersSliceActions.setInitialCreatePaperFields(getInitialCreatePaper()));
  callback(true);
}

/**验证试卷 */
function validMutatePaper(paper: Paper | Omit<Paper, 'id'>): boolean {
  if (paper.sectionIds.length == 0) {
    message.error('请添加试卷板块');
    return false;
  }

  if (paper.totalScore == 0 || paper.totalScore == undefined) {
    message.error('试卷总分数不能小于等于0');
    return false;
  }

  if (paper.totalQuestionSize == 0 || paper.totalQuestionSize == undefined) {
    message.error('试卷总题数不能小于等于0');
    return false;
  }

  /**选题模式下需 验证 每题分数 是否大于0*/
  if (paper.genFun == BackendPapersGenFun.SELECT) {
    const len = paper.sectionIds.length;
    for (let i = 0; i < len; i++) {
      const section = paper.sectionEntities[paper.sectionIds[i]];
      const isValid = section.questions?.every(question => question.grade && question.grade > 0);
      if (!isValid) {
        message.error('试卷中每道试题分数不能小于等于0');
        return false;
      }
    }
  }

  if (
    !paper.sectionIds
      .map(id => paper.sectionEntities[id])
      .every(section => section.totalScore && section.totalScore > 0)
  ) {
    message.error('试卷中每个板块总分数不能小于等于0');
    return false;
  }

  if (
    !paper.sectionIds
      .map(id => paper.sectionEntities[id])
      .every(section => section.totalSize && section.totalSize > 0)
  ) {
    message.error('试卷中每个板块总题数不能小于等于0');
    return false;
  }

  return true;
}

/**Watchers */

function* watchGetPapersCategories() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getPaperCategoriesAction> = yield take(
      getPaperCategoriesAction.type
    );
    const existing: SagaReturnType<ReturnType<typeof selectPaperCategory>> = yield select(
      selectPaperCategory()
    );
    const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.ids);
    if (!existing.loading && staticPolicy === 'network-only') {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(papersSliceActions.fetchCategoriesStart({}));
      lastTask = yield fork(getPaperCategoriesSaga, action);
    }
  }
  // yield takeLatest(getPaperCategoriesAction.type, getPaperCategoriesSaga);
}

function* watchGetPapers() {
  yield takeLatest(getPapersAction.type, getPapersSaga);
}

function* watchGetPaper() {
  yield takeLatest(getPaperAction.type, getPaperSaga);
}

function* watchDeletePapers() {
  yield takeLatest(deletePapersAction.type, deletePapersSaga);
}

function* watchUpdatePapers() {
  yield takeLatest(updatePapersAction.type, updatePapersSaga);
}

function* watchCreatePaper() {
  yield takeLatest(createPaperAction.type, createPaperSaga);
}

export default [
  fork(watchGetPapers),
  fork(watchGetPaper),
  fork(watchDeletePapers),
  fork(watchUpdatePapers),
  fork(watchCreatePaper),
  fork(watchGetPapersCategories),
];
