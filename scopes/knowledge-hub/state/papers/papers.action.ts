/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { createAction } from '@reduxjs/toolkit';

import { ReadPapersCategoriesPolicy } from '@manyun/knowledge-hub.hook.use-papers-category';
import { Paper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

import { papersSlice } from './papers.slice';

const prefix = papersSlice.name;

export const papersSliceActions = papersSlice.actions;

export const getPaperCategoriesAction = createAction<{
  withCount: boolean;
  policy?: ReadPapersCategoriesPolicy;
  callback?: () => void;
}>(prefix + '/GET_PAPER_CATEGORIES');

export const getPapersAction = createAction(prefix + '/GET_PAPERS');

export const getPaperAction = createAction<{
  paperId: number;
  needDetail?: boolean;
  callback: (result: Paper | null) => void;
}>(prefix + '/GET_PAPER');

export const deletePapersAction = createAction<{
  id?: number;
  callback: (result: boolean) => void;
}>(prefix + '/DELETE_PAPERS');

export const updatePapersAction = createAction<{
  paper?: Paper;
  categoryCode?: number;
  callback: (result: boolean) => void;
}>(prefix + '/UPDATE_PAPERS');

export const createPaperAction = createAction<{
  paper: Omit<Paper, 'id'>;
  callback: (result: boolean) => void;
}>(prefix + '/CREATE_PAPER');
