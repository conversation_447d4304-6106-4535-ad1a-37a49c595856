/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { Paper, PaperSection } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

import { AllTypedPapers, MutatePaperType, papersSlice } from './papers.slice';
import type { PapersSliceState } from './papers.slice';

export const selectPapersEntities =
  (ids?: number[]) => (storeState: { [papersSlice.name]: PapersSliceState }) => {
    const { entities } = storeState[papersSlice.name];
    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[Number(id)]);
    }
    return ids.map(id => entities[id]);
  };

export const selectAllTypePapers =
  () =>
  (storeState: { [papersSlice.name]: PapersSliceState }): AllTypedPapers =>
    storeState[papersSlice.name].all;

export const selectPaperCategoryEntities =
  (ids?: number[]) => (storeState: { [papersSlice.name]: PapersSliceState }) => {
    const {
      category: { entities },
    } = storeState[papersSlice.name];
    if (ids === undefined) return Object.keys(entities).map(id => entities[Number(id)]);
    return ids.map(id => entities[id]);
  };

export const selectPaperCategory =
  () =>
  (storeState: {
    [papersSlice.name]: PapersSliceState;
  }): { entities: Record<number, Category>; ids: number[]; loading: boolean } =>
    storeState[papersSlice.name].category;

export const selectSectionSelectedQuestionIds =
  (mutateType: MutatePaperType, sectionId: number) =>
  (storeState: { [papersSlice.name]: PapersSliceState }): number[] =>
    storeState[papersSlice.name][mutateType].sectionEntities[sectionId].questions
      ? (
          storeState[papersSlice.name][mutateType].sectionEntities[sectionId]
            .questions as BackendQuestion[]
        )?.map(question => question.id)
      : [];

export const selectMutatePaper =
  (mutateType: MutatePaperType) =>
  (storeState: { [papersSlice.name]: PapersSliceState }): Omit<Paper, 'id'> | Paper =>
    storeState[papersSlice.name][mutateType];

export const selectMutatePaperSectionsEntities =
  (mutateType: MutatePaperType, ids?: number[]) =>
  (storeState: { [papersSlice.name]: PapersSliceState }): PaperSection[] => {
    const { sectionEntities } = storeState[papersSlice.name][mutateType];
    if (ids == undefined) {
      return Object.keys(sectionEntities).map(id => sectionEntities[Number(id)]);
    }
    return ids.map(id => storeState[papersSlice.name][mutateType].sectionEntities[id]);
  };

export const selectMutatePaperSectionQuestionIds =
  (mutateType: MutatePaperType, sectionId: number) =>
  (storeState: { [papersSlice.name]: PapersSliceState }): number[] => {
    const { sectionEntities } = storeState[papersSlice.name][mutateType];
    return sectionEntities[sectionId].questionIds || [];
  };

export const selectMutatePaperSectionQuestionEntities =
  (mutateType: MutatePaperType, sectionId: number, ids?: number[]) =>
  (storeState: {
    [papersSlice.name]: PapersSliceState;
  }): (BackendQuestion & { grade?: number })[] => {
    const { sectionEntities } = storeState[papersSlice.name][mutateType];
    const sectionEntity = sectionEntities[sectionId];
    const questionEntities: Record<number, any> = sectionEntity.questions || {};
    if (ids == undefined) {
      return Object.keys(questionEntities).map(id => questionEntities[Number(id)]);
    }
    return ids.map(id => questionEntities[id]);
  };
