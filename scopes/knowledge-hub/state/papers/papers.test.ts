/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { expectSaga } from 'redux-saga-test-plan';

import {
  BackendPapersGenFun,
  fetchPapersWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

import { getPaperAction, getPapersAction, papersSliceActions } from './papers.action';
import { getPapersSaga } from './papers.saga';
import { MutatePaperType, getInitialAllPapers, papersSlice } from './papers.slice';
import type { PapersSliceState } from './papers.slice';

test('should  handle a `categoryCode` being updated to paper fields', () => {
  const initialState = papersSlice.reducer(undefined, {} as any);
  const categoryCodes = [1];
  const questionsState = papersSlice.reducer(
    initialState,
    papersSlice.actions.setFields({
      categoryCodes,
      page: 1,
    })
  );

  expect(questionsState.all.fields.categoryCodes).toEqual<string[] | number[]>([1]);
});

test('should  handle `addSection updateSection deleteSection` being updated to create paper', () => {
  const initialState = papersSlice.reducer(undefined, {} as any);
  const questionsState = papersSlice.reducer(
    initialState,
    papersSlice.actions.setMutatePaperFields({
      mutateType: MutatePaperType.CreatePaper,
      genFun: BackendPapersGenFun.DRAW,
      addSection: {
        name: '测试板块',
        questions: [
          {
            analysis: '分析',
            answers: ['A:拉拉啦', 'B:lalalala'],
            autoGrade: true,
            categoryCode: '10001',
            difficulty: 1,
            options: ['A:啦啦啦', 'B:lalala'],
            title: '题目',
            type: 1,
            id: 1,
          },
          {
            analysis: '分析',
            answers: ['A:拉拉啦', 'B:lalalala'],
            autoGrade: true,
            categoryCode: '10001',
            difficulty: 1,
            options: ['A:啦啦啦', 'B:lalala'],
            title: '题目',
            type: 1,
            id: 2,
          },
        ],
      },
    })
  );

  const questionsState2 = papersSlice.reducer(
    questionsState,
    papersSlice.actions.setMutatePaperFields({
      mutateType: MutatePaperType.CreatePaper,
      genFun: BackendPapersGenFun.SELECT,
      addSection: {
        name: '测试板块',
        questions: [
          {
            analysis: '分析',
            answers: ['A:拉拉啦', 'B:lalalala'],
            autoGrade: true,
            categoryCode: '10001',
            difficulty: 1,
            options: ['A:啦啦啦', 'B:lalala'],
            title: '题目',
            type: 1,
            id: 1,
          },
          {
            analysis: '分析',
            answers: ['A:拉拉啦', 'B:lalalala'],
            autoGrade: true,
            categoryCode: '10001',
            difficulty: 1,
            options: ['A:啦啦啦', 'B:lalala'],
            title: '题目',
            type: 1,
            id: 2,
          },
        ],
      },
    })
  );

  const questionsState3 = papersSlice.reducer(
    questionsState2,
    papersSlice.actions.setMutatePaperFields({
      mutateType: MutatePaperType.CreatePaper,
      deleteSectionId: 2,
    })
  );

  const questionState4 = papersSlice.reducer(
    questionsState3,
    papersSlice.actions.setMutatePaperFields({
      mutateType: MutatePaperType.CreatePaper,
      updateSection: {
        id: 1,
        addQuestions: [
          {
            analysis: '分析',
            answers: ['A:拉拉啦', 'B:lalalala'],
            autoGrade: true,
            categoryCode: '10001',
            difficulty: 1,
            options: ['A:啦啦啦', 'B:lalala'],
            title: '题目',
            type: 1,
            id: 3,
          },
        ],
      },
    })
  );

  // console.log(questionState4[MutatePaperType.CreatePaper].sectionEntities['1'].questions);
  // expect(questionsState.all.fields.categoryCodes).toEqual<string[] | number[]>([1]);

  const questionState5 = papersSlice.reducer(
    questionState4,
    papersSlice.actions.setMutatePaperFields({
      mutateType: MutatePaperType.CreatePaper,
      updateSection: {
        id: 1,
        updateQuestion: {
          id: 3,
          grade: 40,
        },
      },
    })
  );

  // console.log(questionState5[MutatePaperType.CreatePaper]);
});

test('should  handle `addSection updateSection deleteSection` being updated to create paper which mode is DRAW', () => {
  const initialState = papersSlice.reducer(undefined, {} as any);
  const questionsState = papersSlice.reducer(
    initialState,
    papersSlice.actions.setMutatePaperFields({
      mutateType: MutatePaperType.CreatePaper,
      genFun: BackendPapersGenFun.DRAW,
      addSection: {
        name: '测试板块',
        typeConfig: {
          ESSAY: { drawTotal: 5, score: 5 },
          MULTIPLE_CHOICE: { drawTotal: 5, score: 5 },
          SHORT_ANSWER: { drawTotal: 5, score: 5 },
          SINGLE_CHOICE: { drawTotal: 5, score: 5 },
          TRUE_OR_FALSE: { drawTotal: 5, score: 5 },
        },
      },
    })
  );
  // console.log(questionsState[MutatePaperType.CreatePaper]);
  expect(questionsState.create.totalQuestionSize).toEqual(25);
});
