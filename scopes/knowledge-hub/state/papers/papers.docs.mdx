---
description: 'Papers redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import papersSliceReducer from '@manyun/[scope].state.papers';

const rootReducer = {
  ...papersSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { papersWatchers } from '@manyun/[scope].state.papers';

const function* rootSaga() {
  yield all(
    ...papersWatchers,
    // other sagas...
  );
};
```
