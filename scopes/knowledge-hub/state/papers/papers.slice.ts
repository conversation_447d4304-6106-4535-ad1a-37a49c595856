/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import sortBy from 'lodash.sortby';
import uniqBy from 'lodash.uniqby';

import { message } from '@manyun/base-ui.ui.message';
import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { addNumUtil } from '@manyun/knowledge-hub.service.dcexam.fetch-paper';
import type {
  BackendPaper,
  PaperSection,
  SectionTypeConfig,
  ServiceQ,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  BackendMarkingWay,
  BackendPapersGenFun,
  Paper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  BackendQuestion,
  BackendQuestionDifficulty,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export interface AllTypedPapers {
  fields: SetFieldsActionPayload;
  loading: boolean;
  error: string | null;
  ids: number[];
  selectedIds: number[];
  total: number;
}

export enum MutatePaperType {
  CreatePaper = 'create',
  UpdatePaper = 'update',
}

export type SetFieldsActionPayload = Partial<ServiceQ>;

export type PickMutatePaperProperty = Pick<
  Paper,
  | 'name'
  | 'genFun'
  | 'markingWay'
  | 'orderByQuestionTypeInSections'
  | 'allowArbitraryQuestionsOrder'
  | 'allowArbitraryOptionsOrder'
  | 'categoryCode'
  | 'id'
>;

export type SetMutateFieldsActionPayload = Partial<PickMutatePaperProperty> & {
  mutateType: MutatePaperType;
  addSection?: {
    name: string;
    questions?: BackendQuestion[]; // 注: 选题模式下 在添加板块时直接添加试题 - questions
    typeConfig?: Record<string, SectionTypeConfig>; //注: 抽题模式下 添加板块时 直接添加 五种题型配置
    questionCategory?: string | number;
  };
  deleteSectionId?: number;
  updateSection?: {
    id: number;
    name?: string;
    addQuestions?: BackendQuestion[];
    gradeVal?: number;
    addQuestionsType?: number;
    deleteQuestionId?: number;
    updateQuestion?: {
      id: number; // 题目id  -- 修改成试题 index
      grade: number; //注: 选题模式下 试题分数
    };
    updateTypeConfigAll?: Record<string, SectionTypeConfig>;
    updateSomeTypeConfig?: Record<string, SectionTypeConfig>;
    updateTypeConfig?: {
      typeConfigKey: string;
      score?: number; // 分数
      drawTotal?: number; // 抽题数
      difficultCount?: {
        difficultKey: number;
        count: number;
      };
      difficultAllCount?: Record<string | number, number>;
    };
    updateQuestionCategory?: string[] | number[]; //注： 随机模式下 更新板块试题分类
  };
};

export type PapersSliceState = {
  category: {
    entities: Record<number, Category>;
    ids: number[];
    loading: boolean;
  };
  entities: Record<string, BackendPaper>;
  all: AllTypedPapers;
  [MutatePaperType.CreatePaper]: Omit<Paper, 'id'>;
  [MutatePaperType.UpdatePaper]: Paper;
};

export type papersSliceCaseReducers = {
  setCategories: CaseReducer<PapersSliceState, PayloadAction<{ categories: Category[] }>>;
  fetchCategoriesStart: CaseReducer<PapersSliceState, PayloadAction<{}>>;
  fetchCategoriesEnd: CaseReducer<PapersSliceState, PayloadAction<{}>>;
  setFields: CaseReducer<PapersSliceState, PayloadAction<SetFieldsActionPayload>>;
  setSelectedIds: CaseReducer<PapersSliceState, PayloadAction<{ selectedIds: number[] }>>;
  fetchPapersStart: CaseReducer<PapersSliceState, PayloadAction<{}>>;
  setPapers: CaseReducer<
    PapersSliceState,
    PayloadAction<{ papers: BackendPaper[]; total: number }>
  >;
  fetchPapersError: CaseReducer<PapersSliceState, PayloadAction<{ error: { message: string } }>>;
  setInitialUpdatePaperFields: CaseReducer<PapersSliceState, PayloadAction<Paper>>;
  setInitialCreatePaperFields: CaseReducer<PapersSliceState, PayloadAction<Omit<Paper, 'id'>>>;
  setMutatePaperFields: CaseReducer<PapersSliceState, PayloadAction<SetMutateFieldsActionPayload>>;
  sortOrderMutatePaperSection: CaseReducer<
    PapersSliceState,
    PayloadAction<{ mutateType: MutatePaperType; type: 'asc' | 'desc'; sectionId: number }>
  >;
  sortOrderMutatePaperSectionQuestion: CaseReducer<
    PapersSliceState,
    PayloadAction<{
      mutateType: MutatePaperType;
      type: 'asc' | 'desc';
      sectionId: number;
      questionId: number;
    }>
  >;
};

export const papersSlice = createSlice<
  PapersSliceState,
  papersSliceCaseReducers,
  'knowledge-hub.papers'
>({
  name: 'knowledge-hub.papers',
  initialState: {
    category: {
      entities: {}, // category entities
      ids: [],
      loading: false,
    },
    entities: {},
    all: getInitialAllPapers(),
    [MutatePaperType.CreatePaper]: getInitialCreatePaper(),
    [MutatePaperType.UpdatePaper]: getInitialUpdatePaper(),
  },
  reducers: {
    setCategories(sliceState, { payload: { categories } }) {
      const ids: number[] = [];
      categories.forEach(category => {
        ids.push(category.code);
        sliceState.category.entities[category.code] = category;
      });
      sliceState.category.loading = false;
      sliceState.category.ids = ids;
    },

    fetchCategoriesStart(sliceState, { payload: {} }) {
      sliceState.category.loading = true;
    },

    fetchCategoriesEnd(sliceState, { payload: {} }) {
      sliceState.category.loading = false;
    },

    setFields(sliceState, { payload: { ...params } }) {
      sliceState.all.fields = { ...sliceState.all.fields, ...params };
    },

    setSelectedIds(sliceState, { payload: { selectedIds } }) {
      sliceState.all.selectedIds = selectedIds;
    },

    fetchPapersStart(sliceState, { payload: {} }) {
      sliceState.all.ids = [];
      sliceState.all.loading = true;
      sliceState.all.error = null;
    },

    setPapers(sliceState, { payload: { papers, total } }) {
      const ids: number[] = [];
      papers.forEach(paper => {
        ids.push(paper.id);
        const existingQuestion = sliceState.entities[paper.id];
        if (existingQuestion && existingQuestion.gmtModified === paper.gmtModified) {
          return;
        }
        sliceState.entities[paper.id] = paper;
      });
      sliceState.all.loading = false;
      sliceState.all.ids = ids;
      sliceState.all.total = total;
    },

    fetchPapersError(sliceState, { payload: { error } }) {
      sliceState.all.error = error.message;
      sliceState.all.loading = false;
    },

    setInitialUpdatePaperFields(sliceState, { payload }) {
      sliceState.update = payload;
    },

    setInitialCreatePaperFields(sliceState, { payload }) {
      sliceState.create = payload;
    },

    setMutatePaperFields(
      sliceState,
      {
        payload: {
          mutateType,
          id,
          name,
          genFun,
          categoryCode,
          markingWay,
          orderByQuestionTypeInSections,
          allowArbitraryOptionsOrder,
          allowArbitraryQuestionsOrder,
          addSection,
          deleteSectionId,
          updateSection,
        },
      }
    ) {
      if (id !== undefined) sliceState.update.id = id;

      if (name !== undefined) sliceState[mutateType].name = name;
      if (genFun !== undefined) {
        /**判卷方式和之前不一致 需要清空试卷板块内容的缓存 */
        if (sliceState[mutateType].genFun != genFun) {
          sliceState[mutateType].totalScore = 0;
          sliceState[mutateType].sectionEntities = {};
          sliceState[mutateType].sectionIds = [];
        }
        sliceState[mutateType].genFun = genFun;
      }
      if (categoryCode !== undefined) sliceState[mutateType].categoryCode = categoryCode;
      if (markingWay !== undefined) sliceState[mutateType].markingWay = markingWay;
      if (orderByQuestionTypeInSections !== undefined)
        sliceState[mutateType].orderByQuestionTypeInSections = orderByQuestionTypeInSections;
      if (allowArbitraryOptionsOrder !== undefined)
        sliceState[mutateType].allowArbitraryOptionsOrder = allowArbitraryOptionsOrder;
      if (allowArbitraryQuestionsOrder !== undefined)
        sliceState[mutateType].allowArbitraryQuestionsOrder = allowArbitraryQuestionsOrder;

      //add section   --   to compute totalSize and totalNum
      if (addSection != undefined) {
        let len = sliceState[mutateType].sectionIds.length;
        const addSectionId = len > 0 ? sliceState[mutateType].sectionIds[len - 1] + 1 : 0;
        sliceState[mutateType].sectionIds.push(addSectionId);

        sliceState[mutateType].sectionEntities[addSectionId] = {
          id: addSectionId,
          name: addSection.name ? addSection.name : '',
          totalScore: 0,
          totalSize: addSection.questions ? addSection.questions.length : 0,
          questions: addSection.questions || [],
          typeConfig: addSection.typeConfig,
        };

        if (addSection.questions !== undefined) {
          sliceState[mutateType].sectionEntities[addSectionId].questions = sortBy(
            addSection.questions,
            'type'
          );

          sliceState[mutateType].sectionEntities[addSectionId].questionIds =
            addSection.questions.map(q => q.id);

          computeMutatePaper({ type: 'size', mutatePaper: sliceState[mutateType] });
        }

        if (addSection.questionCategory !== undefined) {
          sliceState[mutateType].sectionEntities[addSectionId].questionCategory =
            addSection.questionCategory;
        }

        if (addSection.typeConfig) {
          const { totalScore, totalSize } = getSectionTotalByTypeConfig(
            addSection.typeConfig,
            sliceState[mutateType].genFun == BackendPapersGenFun.RANDOM
          );
          sliceState[mutateType].sectionEntities[addSectionId].totalScore = totalScore;
          sliceState[mutateType].sectionEntities[addSectionId].totalSize = totalSize;
          computeMutatePaper({ type: 'all', mutatePaper: sliceState[mutateType] });
        }
      }

      //delete section
      if (deleteSectionId !== undefined) {
        sliceState[mutateType].sectionIds = sliceState[mutateType].sectionIds.filter(
          sectionId => sectionId !== deleteSectionId
        );
        if (sliceState[mutateType].sectionEntities[deleteSectionId])
          delete sliceState[mutateType].sectionEntities[deleteSectionId];

        computeMutatePaper({ type: 'all', mutatePaper: sliceState[mutateType] });
      }

      //udpate section
      if (updateSection && updateSection.id !== undefined) {
        const updateSectionEntity = sliceState[mutateType].sectionEntities[updateSection.id];

        if (updateSection.name !== undefined && updateSection.name != null) {
          updateSectionEntity.name = updateSection.name;
        }

        /** section add questions for mode is SELECT  sort by type */
        if (updateSection.addQuestions != undefined) {
          /**试卷下 其他板块下所有的试题id */
          const paperAllQuestionIds: number[] = Object.keys(
            sliceState[mutateType].sectionEntities
          ).reduce((allOtherQuestionIds: number[], id) => {
            if (updateSection.id != Number(id)) {
              const sectionQuestionIds =
                sliceState[mutateType].sectionEntities[Number(id)].questionIds || [];
              allOtherQuestionIds = allOtherQuestionIds.concat(sectionQuestionIds);
            }
            return allOtherQuestionIds;
          }, []);

          const isRepeat = updateSection.addQuestions.some(question =>
            paperAllQuestionIds.includes(question.id)
          );

          if (isRepeat) {
            updateSection.addQuestions = updateSection.addQuestions.filter(
              question => !paperAllQuestionIds.includes(question.id)
            );
            message.warning('系统已为您自动过滤试卷中不同板块重复的试题！');
          }

          /**选题模式下直接更新 */
          if (updateSection.addQuestionsType == undefined) {
            let questions = updateSection.addQuestions.map(question => {
              const oldQuestion = updateSectionEntity.questions?.find(row => row.id == question.id);
              return {
                ...question,
                grade:
                  oldQuestion && oldQuestion.grade != null
                    ? oldQuestion.grade
                    : updateSection?.gradeVal,
              };
            });

            questions = sortBy(uniqBy(questions, 'id'), 'type');
            updateSectionEntity.questions = questions;
            updateSectionEntity.questionIds = questions.map(question => question.id);
          } else {
            /**抽题模式下 只更新该类型的试题 */
            let questions = (
              updateSectionEntity.questions as { id: number; type: number }[]
            )?.filter(question => question.type != updateSection.addQuestionsType);

            questions = uniqBy(questions.concat(updateSection.addQuestions), 'id');
            updateSectionEntity.questions = questions;
            updateSectionEntity.questionIds = questions.map(question => question.id);
          }

          // 抽题模式 重新计算typeconfig difficulty
          if (sliceState[mutateType].genFun == BackendPapersGenFun.DRAW) {
            const questions: BackendQuestion[] = updateSectionEntity.questions as BackendQuestion[];
            if (updateSectionEntity.typeConfig) {
              Object.keys(updateSectionEntity.typeConfig).forEach(key => {
                if (updateSectionEntity.typeConfig) {
                  updateSectionEntity.typeConfig[key].difficultCount = {
                    EASIER_THAN_EASY: 0,
                    EASY: 0,
                    NORMAL: 0,
                    HARD: 0,
                    HARDER_THAN_HARD: 0,
                  };
                }
              });
            }
            const typeConfigs: Record<string, SectionTypeConfig> =
              updateSectionEntity.typeConfig || {};

            questions.forEach((question, key) => {
              if (question.type != null) {
                const typeConfig: Record<string | number, number> = typeConfigs[
                  BackendQuestionType[question.type]
                ].difficultCount as Record<string | number, number>;

                typeConfig[BackendQuestionDifficulty[question.difficulty]] =
                  typeConfig[BackendQuestionDifficulty[question.difficulty]] + 1;
              }
            });
          }

          computeMutatePaper({
            type: 'all',
            updateSectionEntity,
            mutatePaper: sliceState[mutateType],
          });
        }

        // section delete question
        if (updateSection.deleteQuestionId !== undefined) {
          updateSectionEntity.questions = (
            updateSectionEntity.questions as { id: number }[]
          ).filter(question => question.id !== updateSection.deleteQuestionId);

          computeMutatePaper({
            type: 'all',
            updateSectionEntity,
            mutatePaper: sliceState[mutateType],
          });
        }

        //section update question score for mode is SELECT
        if (updateSection.updateQuestion && updateSection.updateQuestion.id !== undefined) {
          const updateQuestionEntity = (
            updateSectionEntity.questions as { id: number; grade: number }[]
          ).find(question => question.id === (updateSection.updateQuestion as { id: number }).id);

          if (updateQuestionEntity) updateQuestionEntity.grade = updateSection.updateQuestion.grade;
          // 重新计算模块总分
          computeMutatePaper({
            type: 'grade',
            updateSectionEntity,
            mutatePaper: sliceState[mutateType],
          });
        }

        //section update typeConfig for mode is DRAW or RANDOM
        if (
          updateSection.updateTypeConfig &&
          updateSection.updateTypeConfig.typeConfigKey !== undefined
        ) {
          const { typeConfigKey } = updateSection.updateTypeConfig;
          const updateTypeConfigRecord =
            updateSectionEntity.typeConfig && updateSectionEntity.typeConfig[typeConfigKey];

          if (updateTypeConfigRecord !== undefined) {
            if (updateSection.updateTypeConfig.score != undefined) {
              updateTypeConfigRecord.score = updateSection.updateTypeConfig.score;

              computeMutatePaper({
                type: 'grade',
                updateSectionEntity,
                mutatePaper: sliceState[mutateType],
              });
            }

            if (updateSection.updateTypeConfig.drawTotal !== undefined) {
              updateTypeConfigRecord.drawTotal = updateSection.updateTypeConfig.drawTotal;

              computeMutatePaper({
                type: 'all',
                updateSectionEntity,
                mutatePaper: sliceState[mutateType],
              });
            }

            if (updateSection.updateTypeConfig.difficultCount !== undefined) {
              if (updateTypeConfigRecord.difficultCount != undefined) {
                updateTypeConfigRecord.difficultCount =
                  updateSection.updateTypeConfig.difficultCount;

                computeMutatePaper({
                  type: 'all',
                  updateSectionEntity,
                  mutatePaper: sliceState[mutateType],
                });
              }
            }

            if (updateSection.updateTypeConfig.difficultAllCount !== undefined) {
              updateTypeConfigRecord.difficultCount =
                updateSection.updateTypeConfig.difficultAllCount;

              computeMutatePaper({
                type: 'all',
                updateSectionEntity,
                mutatePaper: sliceState[mutateType],
              });
            }
          }
        }

        // section update all typeconfig
        if (updateSection.updateTypeConfigAll !== undefined) {
          updateSectionEntity.typeConfig = updateSection.updateTypeConfigAll;

          computeMutatePaper({
            type: 'all',
            updateSectionEntity,
            mutatePaper: sliceState[mutateType],
          });
        }

        //update some typeConfig
        if (updateSection.updateSomeTypeConfig !== undefined) {
          Object.keys(updateSection.updateSomeTypeConfig).forEach(typeConfigKey => {
            const updateTypeConfigRecord =
              updateSectionEntity.typeConfig && updateSectionEntity.typeConfig[typeConfigKey];
            const config = (
              updateSection.updateSomeTypeConfig as Record<string, SectionTypeConfig>
            )[typeConfigKey];
            if (config.difficultCount) {
              Object.keys(config.difficultCount).map(difficuty => {
                (updateTypeConfigRecord as any).difficultCount[difficuty] = (
                  config.difficultCount as any
                )[difficuty];
              });
            }
          });

          computeMutatePaper({
            type: 'all',
            updateSectionEntity,
            mutatePaper: sliceState[mutateType],
          });
        }

        //section update category
        if (updateSection.updateQuestionCategory !== undefined) {
          updateSectionEntity.questionCategory = updateSection.updateQuestionCategory;
        }
      }
    },

    /**板块排序 */
    sortOrderMutatePaperSection(sliceState, { payload: { mutateType, type, sectionId } }) {
      const mutatePaperSectionIds = sliceState[mutateType].sectionIds;
      const sectionIndex = mutatePaperSectionIds.findIndex(id => id == sectionId);
      if (type == 'desc') {
        if (sectionIndex != mutatePaperSectionIds.length - 1) {
          mutatePaperSectionIds[sectionIndex] = mutatePaperSectionIds.splice(
            sectionIndex + 1,
            1,
            mutatePaperSectionIds[sectionIndex]
          )[0];
        }
      } else {
        if (sectionIndex !== 0) {
          mutatePaperSectionIds[sectionIndex] = mutatePaperSectionIds.splice(
            sectionIndex - 1,
            1,
            mutatePaperSectionIds[sectionIndex]
          )[0];
        }
      }
    },

    /** 试题排序 - 同题型可以排序 不同题型的试题之间不能排序 */
    sortOrderMutatePaperSectionQuestion(
      sliceState,
      { payload: { mutateType, type, sectionId, questionId } }
    ) {
      const mutatePaperSection = sliceState[mutateType].sectionEntities[sectionId];
      const questionsArr = mutatePaperSection.questions || [];
      const questionLen = questionsArr.length;
      const questionIndex = mutatePaperSection.questions?.findIndex(
        question => question.id == questionId
      );
      if (questionIndex == undefined) {
        return;
      }
      if (type == 'desc') {
        if (questionIndex != questionLen - 1) {
          if (
            (questionsArr[questionIndex + 1] as BackendQuestion).type !=
            (questionsArr[questionIndex] as BackendQuestion).type
          ) {
            return;
          }
          questionsArr[questionIndex] = questionsArr.splice(
            questionIndex + 1,
            1,
            questionsArr[questionIndex]
          )[0];
        }
      } else {
        if (questionIndex !== 0) {
          if (
            (questionsArr[questionIndex - 1] as BackendQuestion).type !=
            (questionsArr[questionIndex] as BackendQuestion).type
          ) {
            return;
          }
          questionsArr[questionIndex] = questionsArr.splice(
            questionIndex - 1,
            1,
            questionsArr[questionIndex]
          )[0];
        }
      }
    },
  },
});

/**
 * 重新计算试卷 总分数 总试题数量
 * @param { type  , updateSectionEntity, mutatePaper}
 */
export function computeMutatePaper({
  type = 'size',
  updateSectionEntity,
  mutatePaper,
}: {
  type: 'size' | 'grade' | 'all';
  updateSectionEntity?: PaperSection;
  mutatePaper: Paper | Omit<Paper, 'id'>;
}) {
  const sectionEntities = mutatePaper.sectionEntities;
  const paperType = mutatePaper.genFun;
  /**重新计算试题数量 */
  if (type === 'size' || type === 'all') {
    if (updateSectionEntity) {
      if (paperType === BackendPapersGenFun.SELECT) {
        updateSectionEntity.totalSize = updateSectionEntity.questions?.length;
      } else {
        if (updateSectionEntity.typeConfig) {
          const { totalSize } = getSectionTotalByTypeConfig(
            updateSectionEntity.typeConfig,
            mutatePaper.genFun == BackendPapersGenFun.RANDOM
          );
          updateSectionEntity.totalSize = totalSize;
        }
      }
    }

    mutatePaper.totalQuestionSize = Object.keys(sectionEntities).reduce((totalSize, sectionKey) => {
      return addNumUtil(totalSize, sectionEntities[sectionKey as any].totalSize!);
    }, 0);
  }
  /** 重新计算分数 */
  if (type === 'grade' || type === 'all') {
    if (updateSectionEntity) {
      if (paperType === BackendPapersGenFun.SELECT) {
        updateSectionEntity.totalScore = (
          updateSectionEntity.questions as { id: number; grade: number }[]
        ).reduce((sum, question) => {
          return addNumUtil(sum, Number(question.grade ? question.grade : 0));
        }, 0);
      } else {
        if (updateSectionEntity.typeConfig) {
          const { totalScore } = getSectionTotalByTypeConfig(
            updateSectionEntity.typeConfig,
            mutatePaper.genFun == BackendPapersGenFun.RANDOM
          );
          updateSectionEntity.totalScore = totalScore;
        }
      }
    }

    mutatePaper.totalScore = Object.keys(sectionEntities).reduce((totalScore, sectionKey) => {
      return addNumUtil(totalScore, sectionEntities[sectionKey as any].totalScore!);
    }, 0);
  }
}

/**
 *
 * @param typeconfig 抽题、选题模式下计算模块总题数和总分数
 * @param isDifficultCount 是否通过difficultCount 计算
 * @returns
 */
export function getSectionTotalByTypeConfig(
  typeconfig: Record<string, SectionTypeConfig>,
  isDifficultCount: boolean
) {
  let totalSize: number = 0;
  let totalScore: number = 0;
  Object.keys(typeconfig).forEach(key => {
    const difficultCount = typeconfig[key].difficultCount;
    if (difficultCount && isDifficultCount) {
      Object.keys(difficultCount).forEach(countKey => {
        totalSize += Number(difficultCount[countKey]);
        totalScore = addNumUtil(
          Number((difficultCount[countKey] * typeconfig[key].score).toFixed(1)),
          totalScore
        );
      });
    } else {
      totalSize += Number(typeconfig[key].drawTotal);
      totalScore = addNumUtil(
        Number((Number(typeconfig[key].drawTotal) * Number(typeconfig[key].score)).toFixed(1)),
        totalScore
      );
    }
  });
  return { totalSize, totalScore };
}

export function getInitialAllPapers(): AllTypedPapers {
  return {
    loading: false,
    error: null,
    fields: {
      page: 1,
      pageSize: 10,
      categoryCodes: undefined,
      genFun: undefined,
      name: undefined,
      timeRange: undefined,
    },
    ids: [],
    selectedIds: [],
    total: 0,
  };
}

export function getInitialCreatePaper(): Omit<Paper, 'id'> {
  return {
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    categoryCode: '',
    genFun: BackendPapersGenFun.SELECT,
    markingWay: BackendMarkingWay.MIXINGRADE,
    name: '',
    orderByQuestionTypeInSections: false,
    sectionEntities: {},
    sectionIds: [],
    totalScore: 0,
    totalQuestionSize: 0,
  };
}

export function getInitialUpdatePaper(): Paper {
  return {
    ...getInitialCreatePaper(),
    id: -1, // 设计缺陷
  };
}

export default papersSlice.reducer;
