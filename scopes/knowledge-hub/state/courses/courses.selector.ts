import type { CompletedVariant } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

import { coursesSlice } from './courses.slice';
import type { CoursesSliceState } from './courses.slice';

export const selectCoursesEntities =
  (ids?: number[]) => (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
    const { entities } = storeState[coursesSlice.name];

    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[id]);
    }

    return ids.map(id => entities[id]);
  };

export const selectAllCourses = () => (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
  return storeState[coursesSlice.name].all;
};

export const selectMyCourses =
  (completedVariant: CompletedVariant) =>
  (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
    return storeState[coursesSlice.name].my[completedVariant];
  };

export const selectCourseLearning =
  () => (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
    return storeState[coursesSlice.name].learning;
  };

export const selectCourseValues =
  (id?: number) => (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
    return id !== undefined
      ? storeState[coursesSlice.name].update
      : storeState[coursesSlice.name].create;
  };

export const selectUpdateCourseValues =
  () => (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
    return storeState[coursesSlice.name].update;
  };

export const selectCourseEntity =
  (id?: number) => (storeState: { [coursesSlice.name]: CoursesSliceState }) => {
    if (id) {
      return storeState[coursesSlice.name].entities[id];
    }
    return {};
  };

export const selectCourseCategory = (storeState: {
  [coursesSlice.name]: CoursesSliceState;
}): { entities: Record<number, Category | undefined>; ids: number[]; loading: boolean } =>
  storeState[coursesSlice.name].category;
