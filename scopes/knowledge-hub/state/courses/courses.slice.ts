import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import type { ExamConfig, MutateCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import type {
  Lecture,
  StudyProgress,
} from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import type {
  BackendCourse,
  BackendStudyStatus,
  CompletedVariant,
  Variant,
} from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

enum Type {
  Update = 'update',
  Create = 'create',
  Learning = 'learning',
}

export interface Learning {
  id: number;
  isFinished: boolean;
  name: string;
  openingHours?: number[];
  lectureIds: number[];
  lectureEntities: Record<string, Lecture>;
  test: {
    id: number;
    endTime?: number;
    userScore?: number;
    totalScore?: number;
    result?: number;
    paperId?: number;
  };
  testCount: number;
  properties?: ExamConfig;
  error?: any;
}

export type AllFields = {
  name?: string;
  categoryCode?: number;
  enabled?: boolean;
  page?: number;
  pageSize?: number;
};

export type MyFields = {
  categoryCode?: number;
  timeRange?: number[];
  name?: string;
  required?: boolean;
  involvedState?: BackendStudyStatus;
};

export type Fields = AllFields & MyFields;

export type AllCourses = {
  loading: boolean;
  ids: number[];
  total: number;
  fields: Fields;
  selectedIds: number[];
};

export type MyCourses = {
  loading: boolean;
  ids: number[];
  fields: Fields;
};

export type CoursesSliceState = {
  entities: Record<string, BackendCourse>;

  all: AllCourses;
  my: {
    uncompleted: MyCourses;
    completed: MyCourses;
  };

  create: MutateCourse;
  update: MutateCourse;

  learning: Learning;

  category: {
    entities: Record<number, Category>;
    ids: number[];
    loading: boolean;
  };
};

export type SetFieldsActionPayload = {
  variant: Variant;
  completedVariant?: CompletedVariant;
  name?: string;
  categoryCode?: number;
  enabled?: boolean;
  required?: boolean;
  timeRange?: number[];
  page?: number;
  pageSize?: number;
  involvedState?: BackendStudyStatus;
};

export type CoursesSliceCaseReducers = {
  fetchCategoriesStart: CaseReducer<CoursesSliceState>;
  setCategories: CaseReducer<CoursesSliceState, PayloadAction<{ categories: Category[] }>>;
  setFields: CaseReducer<CoursesSliceState, PayloadAction<SetFieldsActionPayload>>;
  resetFields: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      variant: Variant;
      completedVariant?: CompletedVariant;
    }>
  >;
  setSelectedIds: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      selectedIds: number[];
    }>
  >;
  fetchCoursesStart: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      variant: Variant;
      completedVariant?: CompletedVariant;
    }>
  >;
  fetchCoursesError: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      variant: Variant;
      completedVariant?: CompletedVariant;
    }>
  >;
  setCourses: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      variant: Variant;
      completedVariant?: CompletedVariant;
      courses: BackendCourse[];
      total: number | null;
    }>
  >;
  setMutateCoursesValue: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      id?: number;
      value: Partial<MutateCourse>;
    }>
  >;
  resetMutateCoursesValue: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      id?: number;
    }>
  >;
  setLearningCourse: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      value: Partial<Learning>;
    }>
  >;
  setLearningCourseLecture: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      value: StudyProgress;
    }>
  >;
  resetLearningCourse: CaseReducer<CoursesSliceState>;
  getLearningCourseError: CaseReducer<
    CoursesSliceState,
    PayloadAction<{
      error: any;
    }>
  >;
};

export const coursesSlice = createSlice<
  CoursesSliceState,
  CoursesSliceCaseReducers,
  'knowledge-hub.courses'
>({
  name: 'knowledge-hub.courses',
  initialState: {
    entities: {},
    all: getInitialAllCourses(),
    my: {
      uncompleted: getInitialMyCourses(),
      completed: getInitialMyCourses(),
    },

    create: getInitialCreateCourse(),
    update: getInitialUpdateCourse(),

    learning: getInitialLearningCourse(),

    category: {
      entities: {},
      ids: [],
      loading: false,
    },
  },
  reducers: {
    fetchCategoriesStart(sliceState) {
      sliceState.category.loading = true;
    },
    setCategories(sliceState, { payload: { categories } }) {
      const ids: number[] = [];
      categories.forEach(category => {
        ids.push(category.id);
        sliceState.category.entities[category.id] = category;
      });
      sliceState.category.ids = ids;
      sliceState.category.loading = false;
    },

    setFields(
      sliceState,
      {
        payload: {
          variant,
          completedVariant,
          name,
          categoryCode,
          enabled,
          page,
          pageSize,
          required,
          timeRange,
          involvedState,
        },
      }
    ) {
      if (variant === 'all') {
        if (name !== undefined) sliceState[variant].fields.name = name.trim();
        if (categoryCode !== undefined) sliceState[variant].fields.categoryCode = categoryCode;
        if (enabled !== undefined) sliceState[variant].fields.enabled = enabled;
        if (page !== undefined) sliceState[variant].fields.page = page;
        if (pageSize !== undefined) sliceState[variant].fields.pageSize = pageSize;
      } else {
        if (name !== undefined) sliceState['my'][completedVariant!].fields.name = name.trim();
        if (required !== undefined) sliceState['my'][completedVariant!].fields.required = required;
        if (involvedState !== undefined)
          sliceState['my'][completedVariant!].fields.involvedState = involvedState;
        if (timeRange !== undefined)
          sliceState['my'][completedVariant!].fields.timeRange = timeRange;
      }
    },
    resetFields(sliceState, { payload: { variant, completedVariant } }) {
      if (variant === 'all') {
        sliceState[variant].fields = { page: 1, pageSize: 10 };
      } else {
        const required = sliceState['my'][completedVariant!].fields.required;
        sliceState['my'][completedVariant!].fields =
          completedVariant === 'uncompleted' ? { required } : {};
      }
    },
    setSelectedIds(sliceState, { payload: { selectedIds } }) {
      sliceState['all'].selectedIds = selectedIds;
    },
    fetchCoursesStart(sliceState, { payload: { variant, completedVariant } }) {
      if (variant === 'all') {
        sliceState[variant].ids = [];
        sliceState[variant].loading = true;
      } else {
        sliceState[variant][completedVariant!].ids = [];
        sliceState[variant][completedVariant!].loading = true;
      }
    },
    fetchCoursesError(sliceState, { payload: { variant, completedVariant } }) {
      if (variant === 'all') {
        sliceState[variant].loading = false;
      } else {
        sliceState[variant][completedVariant!].loading = false;
      }
    },
    setCourses(sliceState, { payload: { variant, completedVariant, courses, total } }) {
      const ids: number[] = [];
      courses.forEach(course => {
        ids.push(course.id);

        // const existingCourse = sliceState.entities[course.id];
        // if (existingCourse && existingCourse.gmtModify === course.gmtModify) {
        //   return;
        // }
        sliceState.entities[course.id] = course;
      });
      if (variant === 'all') {
        sliceState[variant].ids = ids;
        sliceState[variant].total = total!;
        sliceState[variant].loading = false;
      } else {
        sliceState['my'][completedVariant!].ids = ids;
        sliceState['my'][completedVariant!].loading = false;
      }
    },
    setMutateCoursesValue(
      sliceState,
      {
        payload: {
          id,
          value: {
            categoryCode,
            enabled,
            lectureEntities,
            lectureIds,
            mustPass,
            name,
            openingHours,
            optionalRoleIds,
            optionalUserIds,
            optionalDeptIds,
            paperId,
            passingScore,
            requiredRoleIds,
            requiredUserIds,
            requiredDeptIds,
            roleEntities,
            timeLimit,
            userEntities,
            retakeExamCount,
            failedExamCount,
            studySla,
            incompleteTimes,
            optNotify,
            trainMode,
            ownerId,
            ownerName,
          },
        },
      }
    ) {
      const type = id !== undefined ? Type.Update : Type.Create;

      if (id !== undefined) sliceState[type].id = id;
      if (categoryCode !== undefined) sliceState[type].categoryCode = categoryCode;
      if (enabled !== undefined) sliceState[type].enabled = enabled;
      if (lectureEntities !== undefined) sliceState[type].lectureEntities = lectureEntities;
      if (lectureIds !== undefined) sliceState[type].lectureIds = lectureIds;
      if (mustPass !== undefined) sliceState[type].mustPass = mustPass;
      if (name !== undefined) sliceState[type].name = name;
      if (openingHours !== undefined) sliceState[type].openingHours = openingHours;
      if (optionalRoleIds !== undefined) sliceState[type].optionalRoleIds = optionalRoleIds;
      if (optionalUserIds !== undefined) sliceState[type].optionalUserIds = optionalUserIds;
      if (optionalDeptIds !== undefined) sliceState[type].optionalDeptIds = optionalDeptIds;
      if (paperId !== undefined) sliceState[type].paperId = paperId;
      if (passingScore !== undefined) sliceState[type].passingScore = passingScore;
      if (requiredRoleIds !== undefined) sliceState[type].requiredRoleIds = requiredRoleIds;
      if (requiredUserIds !== undefined) sliceState[type].requiredUserIds = requiredUserIds;
      if (requiredDeptIds !== undefined) sliceState[type].requiredDeptIds = requiredDeptIds;
      if (roleEntities !== undefined) sliceState[type].roleEntities = roleEntities;
      if (timeLimit !== undefined) sliceState[type].timeLimit = timeLimit;
      if (userEntities !== undefined) sliceState[type].userEntities = userEntities;
      if (retakeExamCount !== undefined) sliceState[type].retakeExamCount = retakeExamCount;
      if (failedExamCount !== undefined) sliceState[type].failedExamCount = failedExamCount;
      if (studySla !== undefined) sliceState[type].studySla = studySla;
      if (incompleteTimes !== undefined) sliceState[type].incompleteTimes = incompleteTimes;
      if (optNotify !== undefined) sliceState[type].optNotify = optNotify;
      if (trainMode !== undefined) sliceState[type].trainMode = trainMode;
      if (ownerId !== undefined) sliceState[type].ownerId = ownerId;
      if (ownerName !== undefined) sliceState[type].ownerName = ownerName;
    },
    resetMutateCoursesValue(sliceState, { payload: { id } }) {
      if (id) {
        sliceState[Type.Update] = getInitialUpdateCourse();
      } else {
        sliceState[Type.Create] = getInitialCreateCourse();
      }
    },
    setLearningCourse(sliceState, { payload: { value } }) {
      sliceState[Type.Learning] = value as any;
    },
    setLearningCourseLecture(sliceState, { payload: { value } }) {
      sliceState[Type.Learning].lectureEntities[value.fileId] = {
        ...sliceState[Type.Learning].lectureEntities[value.fileId],
        studyTime: value.studyTime,
        isFinished: value.isFinished,
      };
    },
    resetLearningCourse(sliceState) {
      sliceState[Type.Learning] = getInitialLearningCourse();
    },
    getLearningCourseError(sliceState, { payload: { error } }) {
      sliceState[Type.Learning].error = error;
    },
  },
});

export default coursesSlice.reducer;

export function getInitialMyCourses(): MyCourses {
  return {
    loading: false,
    ids: [],
    fields: {
      required: true,
    },
  };
}

export function getInitialAllCourses(): AllCourses {
  const initialMyCourses = getInitialMyCourses();

  return {
    ...initialMyCourses,
    total: 0,
    selectedIds: [],
    fields: {
      page: 1,
      pageSize: 10,
    },
  };
}

export function getInitialCreateCourse(): Omit<MutateCourse, 'id'> {
  return {
    name: '',
    categoryCode: '',
    openingHours: null,
    enabled: true,
    roleEntities: {},
    userEntities: {},
    requiredUserIds: [],
    requiredRoleIds: [],
    optionalUserIds: [],
    optionalRoleIds: [],
    lectureEntities: {},
    lectureIds: [],
    paperId: 0,
    mustPass: true,
    passingScore: 0,
    timeLimit: 0,
  };
}

export function getInitialUpdateCourse(): MutateCourse {
  const initialCreateCourse = getInitialCreateCourse();

  return {
    id: 0,
    ...initialCreateCourse,
  };
}

export function getInitialLearningCourse(): Learning {
  return {
    id: 0,
    isFinished: false,
    name: '',
    lectureIds: [],
    lectureEntities: {},
    test: {
      id: 0,
    },
    testCount: 0,
  };
}
