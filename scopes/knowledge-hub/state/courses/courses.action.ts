import { createAction } from '@reduxjs/toolkit';

import type { ReadCoursesCategoriesPolicy } from '@manyun/knowledge-hub.hook.use-courses-category';
import type { MutateCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import type { CompletedVariant, Variant } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';

import { coursesSlice } from './courses.slice';

const prefix = coursesSlice.name;

export const coursesSliceActions = coursesSlice.actions;

export const getCoursesAction = createAction<{
  variant: Variant;
  completedVariant?: CompletedVariant;
}>(prefix + '/GET_COURSES');

export const getCourseAction = createAction<{
  id: number;
}>(prefix + '/GET_COURSE');

export const getCourseLearningAction = createAction<{
  id: number;
  fileId?: number;
  progress?: number;
  callback?: (result: any) => void;
}>(prefix + '/GET_COURSE_LEARNING');

export const deleteCoursesAction = createAction<{
  id?: number;
  callback: (result: boolean) => void;
}>(prefix + '/DELETE_COURSES');

export const mutateCoursesAction = createAction<{
  course?: Partial<MutateCourse>;
  categoryCode?: number[];
  callback: (result: boolean) => void;
}>(prefix + '/MUTATE_COURSES');

export const getCoursesCategoriesAction = createAction<{
  policy?: ReadCoursesCategoriesPolicy;
  withCount: boolean;
}>(prefix + '/GET_COURSES_CATEGORIES');
