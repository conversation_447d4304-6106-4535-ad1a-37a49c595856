import type { Task } from 'redux-saga';
import { call, cancel, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deleteCoursesWeb } from '@manyun/knowledge-hub.service.dcexam.delete-courses';
import { fetchCourseWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { fetchCourseLearningWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import type { StudyProgress } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import { fetchCoursesWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { mutateCoursesWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-courses';

import {
  coursesSliceActions,
  deleteCoursesAction,
  getCourseAction,
  getCourseLearningAction,
  getCoursesAction,
  getCoursesCategoriesAction,
  mutateCoursesAction,
} from './courses.action';
import { selectAllCourses, selectMyCourses } from './courses.selector';
import { selectCourseCategory } from './courses.selector';
import type { AllCourses, AllFields, MyFields } from './courses.slice';
import { getStaticReadPolicy } from './courses.util';

/** Workers */

export function* getCoursesCategoriesSaga({
  payload: { withCount },
}: ReturnType<typeof getCoursesCategoriesAction>) {
  const query = {
    categoryType: BackendCategoryType.COURSE,
    needNum: withCount,
  };
  const { data, error }: SagaReturnType<typeof fetchKnowledgeHubCategoryWeb> = yield call(
    fetchKnowledgeHubCategoryWeb,
    query
  );

  if (error) {
    message.error(error.message);
    return;
  }

  yield put(coursesSliceActions.setCategories({ categories: data }));
}

export function* getCoursesSaga({
  payload: { variant, completedVariant },
}: ReturnType<typeof getCoursesAction>) {
  const { fields } =
    variant === 'all'
      ? yield select(selectAllCourses())
      : yield select(selectMyCourses(completedVariant!));
  const AllQ = fields as AllFields;
  const MyQ = fields as MyFields;

  yield put(coursesSliceActions.fetchCoursesStart({ variant, completedVariant }));

  const { error, data }: SagaReturnType<typeof fetchCoursesWeb> = yield call(
    fetchCoursesWeb as any,
    {
      variant,
      completedVariant,
      ...(variant === 'all' ? AllQ : MyQ),
    }
  );

  if (error) {
    message.error(error.message);
    yield put(coursesSliceActions.fetchCoursesError({ variant, completedVariant }));
    return;
  }

  yield put(
    coursesSliceActions.setCourses({
      variant,
      completedVariant,
      courses: data.data,
      total: data.total,
    })
  );
}

export function* getCourseSaga({ payload: { id } }: ReturnType<typeof getCourseAction>) {
  const { error, data }: SagaReturnType<typeof fetchCourseWeb> = yield call(fetchCourseWeb, {
    courseId: id,
  });

  if (error) {
    message.error(error.message);
    return;
  }

  yield put(
    coursesSliceActions.setMutateCoursesValue({
      id,
      value: data!,
    })
  );
}

export function* getCourseLearningSaga({
  payload: { id, fileId, progress, callback },
}: ReturnType<typeof getCourseLearningAction>) {
  const { error, data }: SagaReturnType<typeof fetchCourseLearningWeb> = yield call(
    fetchCourseLearningWeb,
    {
      courseId: id,
      fileId,
      progress,
    }
  );

  if (error) {
    message.error(error.message);
    yield put(coursesSliceActions.getLearningCourseError({ error }));
    callback && callback(false);
    return;
  }

  if (fileId) {
    yield put(
      coursesSliceActions.setLearningCourseLecture({
        value: data as StudyProgress,
      })
    );
  } else {
    callback && callback(data);
    yield put(
      coursesSliceActions.setLearningCourse({
        value: data!,
      })
    );
  }
}

export function* deleteCoursesSaga({
  payload: { id, callback },
}: ReturnType<typeof deleteCoursesAction>) {
  const existing: AllCourses = yield select(selectAllCourses());
  const { error }: SagaReturnType<typeof deleteCoursesWeb> = yield call(deleteCoursesWeb, {
    selectedIds: id ? [id] : existing.selectedIds,
  });

  if (error) {
    callback(false);
    message.error(error.message);
    return;
  }
  callback(true);
  message.success('删除成功！');
  if (id === undefined) {
    yield put(coursesSliceActions.setSelectedIds({ selectedIds: [] }));
  }
  yield put(getCoursesAction({ variant: 'all' }));
}

export function* mutateCoursesSaga({
  payload: { course, categoryCode, callback },
}: ReturnType<typeof mutateCoursesAction>) {
  const existing: AllCourses = yield select(selectAllCourses());
  const { error }: SagaReturnType<typeof mutateCoursesWeb> = yield call(mutateCoursesWeb as any, {
    course,
    categoryCode: categoryCode ? categoryCode[categoryCode.length - 1] : undefined,
    selectedIds: existing.selectedIds,
  });

  if (error) {
    callback(false);
    message.error(error.message);
    return;
  }
  if (course) {
    message.success(course.id ? '修改成功！' : '创建成功！');
    yield put(coursesSliceActions.resetMutateCoursesValue({ id: course.id }));
    callback(true);
  } else if (existing.selectedIds) {
    callback(true);
    message.success('批量更新成功！');
    yield put(coursesSliceActions.setSelectedIds({ selectedIds: [] }));
  }
  yield put(getCoursesAction({ variant: 'all' }));
}

/** Watchers */

function* watchGetCoursesCategories() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getCoursesCategoriesAction> = yield take(
      getCoursesCategoriesAction.type
    );
    const existing: SagaReturnType<typeof selectCourseCategory> =
      yield select(selectCourseCategory);
    const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.ids);
    if (!existing.loading && staticPolicy === 'network-only') {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(coursesSliceActions.fetchCategoriesStart());
      lastTask = yield fork(getCoursesCategoriesSaga, action);
    }
  }
}

function* watchGetCourses() {
  yield takeLatest(getCoursesAction.type, getCoursesSaga);
}

function* watchGetCourse() {
  yield takeLatest(getCourseAction.type, getCourseSaga);
}

function* watchGetCourseLearning() {
  yield takeLatest(getCourseLearningAction.type, getCourseLearningSaga);
}

function* watchDeleteCourses() {
  yield takeLatest(deleteCoursesAction.type, deleteCoursesSaga);
}

function* watchMutateCourses() {
  yield takeLatest(mutateCoursesAction.type, mutateCoursesSaga);
}

export default [
  fork(watchGetCoursesCategories),
  fork(watchGetCourses),
  fork(watchGetCourse),
  fork(watchGetCourseLearning),
  fork(watchDeleteCourses),
  fork(watchMutateCourses),
];
