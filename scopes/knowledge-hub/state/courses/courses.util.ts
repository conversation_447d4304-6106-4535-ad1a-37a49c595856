export type StaticReadPolicy = 'network-only' | 'cache-only';
export type CallbackReadPolicy<T extends keyof any> = (ids: T[]) => StaticReadPolicy;
export function getStaticReadPolicy<T extends keyof any>(
  policy: StaticReadPolicy | CallbackReadPolicy<T> = 'cache-only',
  ids: T[]
): StaticReadPolicy {
  if (ids.length <= 0) {
    return 'network-only';
  }
  if (typeof policy == 'function') {
    return policy(ids);
  }
  return policy;
}
