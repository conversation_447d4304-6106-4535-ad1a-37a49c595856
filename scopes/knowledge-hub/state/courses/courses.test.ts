import { expectSaga } from 'redux-saga-test-plan';

import {
  coursesSlice,
  getInitialAllCourses,
  getInitialCreateCourse,
  getInitialLearningCourse,
  getInitialMyCourses,
  getInitialUpdateCourse,
} from './courses.slice';
import type { CoursesSliceState } from './courses.slice';

test('should return the initial state', () => {
  expect(coursesSlice.reducer(undefined, {} as any)).toEqual<CoursesSliceState>({
    entities: {},
    all: getInitialAllCourses(),
    my: {
      uncompleted: getInitialMyCourses(),
      completed: getInitialMyCourses(),
    },

    create: getInitialCreateCourse(),
    update: getInitialUpdateCourse(),

    learning: getInitialLearningCourse(),

    category: {
      entities: {},
      ids: [],
      loading: false,
    },
  });
});
