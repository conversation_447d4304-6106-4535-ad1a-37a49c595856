---
description: 'Courses redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'courses']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import coursesSliceReducer from '@manyun/[scope].state.coursewares';

const rootReducer = {
  ...coursesSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { coursesWatchers } from '@manyun/[scope].state.coursewares';

const function* rootSaga() {
  yield all(
    ...coursesWatchers,
    // other sagas...
  );
};
```