/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import type { Task } from 'redux-saga';
import type { SagaReturnType } from 'redux-saga/effects';
import { call, cancel, fork, put, select, take, takeLatest } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deleteQuestionsWeb } from '@manyun/knowledge-hub.service.dcexam.delete-questions';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { fetchQuestionWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-question';
import type { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { fetchQuestionsWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { mutateQuestionsWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-questions';

import {
  createQustionsAction,
  deleteQuestionsAction,
  getQuestionAction,
  getQuestionsAction,
  getQuestionsCategoryAction,
  questionsSliceActions,
  updateQuestionsAction,
} from './questions.action';
import { selectAllTypeQuestions, selectQuestionsEntities } from './questions.selector';
import { selectQuestionCategory } from './questions.selector';
import { getInitialCreateQustions } from './questions.slice';
import { getStaticReadPolicy } from './questions.util';

/**Workers */
export function* getQuestionsCategorySaga({
  payload: { withCount, callback },
}: ReturnType<typeof getQuestionsCategoryAction>) {
  const { error, data }: SagaReturnType<typeof fetchKnowledgeHubCategoryWeb> = yield call<
    typeof fetchKnowledgeHubCategoryWeb
  >(fetchKnowledgeHubCategoryWeb, {
    categoryType: BackendCategoryType.QUESTION,
    needNum: withCount,
  });
  if (callback) {
    callback();
  }
  if (error) {
    message.error(error);
    yield put(questionsSliceActions.fetchCategoriesEnd({}));
    return;
  }
  yield put(questionsSliceActions.setCategories({ categories: data }));
}

export function* getQuestionsSaga() {
  const existing: SagaReturnType<ReturnType<typeof selectAllTypeQuestions>> = yield select(
    selectAllTypeQuestions()
  );

  yield put(questionsSliceActions.fetchQuestionsStart(existing.fields));
  if (existing.selectedIds.length > 0) {
    yield put(questionsSliceActions.setSelectedIds({ selectedIds: [] }));
  }
  const { categoryCode, difficulty, type, title, page, pageSize } = existing.fields;
  const { error, data }: SagaReturnType<typeof fetchQuestionsWeb> = yield call<
    typeof fetchQuestionsWeb
  >(fetchQuestionsWeb, {
    categoryCode,
    difficulty,
    type,
    title,
    questionType: categoryCode !== undefined ? 0 : undefined,
    page,
    pageSize,
  });

  if (error) {
    message.error(error.message);
    yield put(questionsSliceActions.fetchQuestionsError({ error }));
    return;
  }

  yield put(questionsSliceActions.setQuestions({ questions: data.data, total: data.total }));
}

export function* deleteQuestionsSaga({
  payload: { id, callback },
}: ReturnType<typeof deleteQuestionsAction>) {
  let deleteIds: string[] = [];
  if (id) {
    /**单个删除 */
    deleteIds.push(id);
  } else {
    /**批量删除 */
    const { selectedIds }: SagaReturnType<ReturnType<typeof selectAllTypeQuestions>> = yield select(
      selectAllTypeQuestions()
    );

    deleteIds = selectedIds;
  }

  const deleteEntities: SagaReturnType<ReturnType<typeof selectQuestionsEntities>> = yield select(
    selectQuestionsEntities(deleteIds)
  );

  const { error } = yield call(deleteQuestionsWeb, deleteEntities);
  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }
  callback(true);
  message.success('删除成功!');
  if (id === undefined) {
    yield put(questionsSliceActions.setSelectedIds({ selectedIds: [] }));
    /** 批量删除 如果删除了试题分类 需要手动刷新页面的试题分类， 不能直接刷新试题列表 */
  } else {
    yield put(getQuestionsAction());
  }
}

export function* updateQuestionsSaga({
  payload: { question, categoryCode, callback },
}: ReturnType<typeof updateQuestionsAction>) {
  let updateQustionsEntities:
    | BackendQuestion
    | Pick<BackendQuestion, 'id' | 'categoryCode' | 'questionType'>[];
  if (question) {
    //更新
    updateQustionsEntities = question;
  } else {
    //批量更新
    const allTypeQuestion: SagaReturnType<ReturnType<typeof selectAllTypeQuestions>> = yield select(
      selectAllTypeQuestions()
    );
    const updateEntities: SagaReturnType<ReturnType<typeof selectQuestionsEntities>> = yield select(
      selectQuestionsEntities(allTypeQuestion.selectedIds)
    );
    updateQustionsEntities = updateEntities.map(question => {
      return { id: question.id, categoryCode: categoryCode!, questionType: question.questionType };
    }) as Pick<BackendQuestion, 'id' | 'categoryCode' | 'questionType'>[];
  }

  const { error } = yield call(mutateQuestionsWeb, updateQustionsEntities);

  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }

  message.success('试题更新成功!');
  callback(true);
  if (question === undefined) {
    yield put(questionsSliceActions.setSelectedIds({ selectedIds: [] }));
    yield put(getQuestionsAction());
  }
}

export function* createQuestionsSaga({
  payload: { questions, callback },
}: ReturnType<typeof createQustionsAction>) {
  const { error } = yield call(mutateQuestionsWeb, questions);
  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }
  yield put(questionsSliceActions.setInitCreateFields(getInitialCreateQustions()));
  message.success('试题创建成功!');

  callback(true);
}

export function* getQuestionSaga({
  payload: { questionId, callback },
}: ReturnType<typeof getQuestionAction>) {
  const { data, error }: SagaReturnType<typeof fetchQuestionWeb> = yield call(fetchQuestionWeb, {
    questionId,
  });
  if (error) {
    message.error(error.message);
    callback(null);
    return;
  }
  callback(data as BackendQuestion);
}

/**Watchers */
function* watchGetQuestionsCategories() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getQuestionsCategoryAction> = yield take(
      getQuestionsCategoryAction.type
    );
    const existing: SagaReturnType<ReturnType<typeof selectQuestionCategory>> = yield select(
      selectQuestionCategory()
    );
    const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.ids);
    if (!existing.loading && staticPolicy === 'network-only') {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(questionsSliceActions.fetchCategoriesStart({}));
      lastTask = yield fork(getQuestionsCategorySaga, action);
    }
  }
}

function* watchGetQuestions() {
  yield takeLatest(getQuestionsAction.type, getQuestionsSaga);
}

function* watchGetQuestion() {
  yield takeLatest(getQuestionAction.type, getQuestionSaga);
}

function* watchDeleteQuestions() {
  yield takeLatest(deleteQuestionsAction.type, deleteQuestionsSaga);
}

function* watchUpdateQuestions() {
  yield takeLatest(updateQuestionsAction.type, updateQuestionsSaga);
}

function* watchCreateQuestions() {
  yield takeLatest(createQustionsAction.type, createQuestionsSaga);
}

export default [
  fork(watchGetQuestions),
  fork(watchGetQuestion),
  fork(watchDeleteQuestions),
  fork(watchUpdateQuestions),
  fork(watchCreateQuestions),
  fork(watchGetQuestionsCategories),
];
