/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export type QuestionsFields = {
  categoryCode?: string | number;
  difficulty?: number;
  type?: number;
  title?: string;
  page: number;
  pageSize: number;
};

export type SetFieldsActionPayload = Partial<QuestionsFields>;

export interface AllTypedQuestions {
  fields: QuestionsFields;
  loading: boolean;
  error: string | null;
  ids: string[];
  selectedIds: string[];
  total: number | null;
}

export type CreateMode = 'byFile' | 'byHand';

export interface CreateTypedQuestion {
  mode: CreateMode;
  entities: Record<number, BackendQuestion>; // 题目实体
  byFile: {
    defaultCategoryCode: string | number | null;
    defaultDifficulty: number | string | null;
    text: string | null;
  };
  byHand: {
    defaultCategoryCode: number | string | null;
    ids: number[];
  };
}

export type QuestionsSliceState = {
  category: {
    entities: Record<number, Category>;
    ids: number[];
    expandedKeys: string[];
    loading: boolean;
  };
  entities: Record<string, BackendQuestion & { uniqId: string }>;
  all: AllTypedQuestions;
  create: CreateTypedQuestion;
};

export type SetCreateFieldsActionPayload = {
  mode?: CreateMode;
  defaultCategoryCode?: number | string;
  defaultDifficulty?: number | string;
  text?: string;
  addQuestion?: { type: number }; // 添加的试题
  deleteQuestion?: BackendQuestion; //删除的试题
  updateQuestion?: Partial<
    Omit<BackendQuestion, 'answers' | 'autoGrade'> & {
      answers: number[] | string[];
      autoGrade: number;
      answersForOptions: string[];
    }
  >; //编辑添加的试题
};

export type QuestionsSliceCaseReducers = {
  fetchCategoriesStart: CaseReducer<QuestionsSliceState, PayloadAction<{}>>;
  fetchCategoriesEnd: CaseReducer<QuestionsSliceState, PayloadAction<{}>>;
  setCategories: CaseReducer<QuestionsSliceState, PayloadAction<{ categories: Category[] }>>;
  setCategoriesExpandedKeys: CaseReducer<
    QuestionsSliceState,
    PayloadAction<{ expandedKeys: string[] | string }>
  >;
  setCategoriesUpdate: CaseReducer<
    QuestionsSliceState,
    PayloadAction<{ id: number; updateCategory: Category }>
  >;
  setCategoriesDelete: CaseReducer<QuestionsSliceState, PayloadAction<{ id: number }>>;
  setCategoriesAdd: CaseReducer<QuestionsSliceState, PayloadAction<{ category: Category }>>;

  setFields: CaseReducer<QuestionsSliceState, PayloadAction<SetFieldsActionPayload>>;
  setSelectedIds: CaseReducer<QuestionsSliceState, PayloadAction<{ selectedIds: string[] }>>;
  fetchQuestionsStart: CaseReducer<QuestionsSliceState, PayloadAction<{}>>;
  setQuestions: CaseReducer<
    QuestionsSliceState,
    PayloadAction<{ questions: BackendQuestion[]; total: number | null }>
  >;
  fetchQuestionsError: CaseReducer<
    QuestionsSliceState,
    PayloadAction<{ error: { message: string } }>
  >;
  setInitCreateFields: CaseReducer<QuestionsSliceState, PayloadAction<CreateTypedQuestion>>;
  setCreateFields: CaseReducer<QuestionsSliceState, PayloadAction<SetCreateFieldsActionPayload>>;
};

export const questionsSlice = createSlice<
  QuestionsSliceState,
  QuestionsSliceCaseReducers,
  'knowledge-hub.questions'
>({
  name: 'knowledge-hub.questions',
  initialState: {
    category: {
      entities: {}, // category entities
      ids: [],
      expandedKeys: [],
      loading: false,
    },
    entities: {},
    all: getInitialAllQustions(),
    create: getInitialCreateQustions(),
  },
  reducers: {
    fetchCategoriesStart(sliceState, { payload: {} }) {
      sliceState.category.loading = true;
    },

    fetchCategoriesEnd(sliceState, { payload: {} }) {
      sliceState.category.loading = false;
    },

    setCategories(sliceState, { payload: { categories } }) {
      const ids: number[] = [];
      categories.forEach(category => {
        ids.push(category.code);
        sliceState.category.entities[category.code] = category;
      });
      sliceState.category.ids = ids;
      sliceState.category.loading = false;
    },

    setCategoriesExpandedKeys(sliceState, { payload: { expandedKeys } }) {
      if (Array.isArray(expandedKeys)) {
        sliceState.category.expandedKeys = expandedKeys;
      } else {
        sliceState.category.expandedKeys = [...sliceState.category.expandedKeys, expandedKeys];
      }
    },

    setCategoriesUpdate(sliceState, { payload: { id, updateCategory } }) {
      const idIndex = sliceState.category.ids.findIndex(key => key == id);
      const ids: number[] = [...sliceState.category.ids];
      if (idIndex > -1) {
        ids.splice(idIndex, 1, updateCategory.id);
      }
      sliceState.category.ids = [...ids];

      sliceState.category.entities[updateCategory.id] = {
        ...updateCategory,
        count: sliceState.category.entities[id].count,
      };
      if (updateCategory.id !== id && sliceState.category.entities[id]) {
        delete sliceState.category.entities[id];
      }
    },

    setCategoriesDelete(sliceState, { payload: { id } }) {
      const idIndex = sliceState.category.ids.findIndex(key => key == id);
      if (idIndex > -1) {
        sliceState.category.ids.splice(idIndex, 1);
      }
      if (sliceState.category.entities[id]) {
        delete sliceState.category.entities[id];
      }
    },

    setCategoriesAdd(sliceState, { payload: { category } }) {
      sliceState.category.ids.push(category.id);
      sliceState.category.entities[category.id] = category;
    },

    setFields(sliceState, { payload: { ...params } }) {
      sliceState.all.fields = { ...sliceState.all.fields, ...params };
    },

    setSelectedIds(sliceState, { payload: { selectedIds } }) {
      sliceState.all.selectedIds = selectedIds;
    },

    fetchQuestionsStart(sliceState, { payload: {} }) {
      sliceState.all.ids = [];
      sliceState.all.loading = true;
      sliceState.all.error = null;
    },

    setQuestions(sliceState, { payload: { questions, total } }) {
      const ids: string[] = [];
      questions.forEach(question => {
        const uniqId: string = `${question.id}_${question.questionType}`;
        ids.push(uniqId);
        const existingQuestion = sliceState.entities[question.id];
        if (
          existingQuestion &&
          existingQuestion.gmtModified === question.gmtModified &&
          existingQuestion.title == question.title
        ) {
          return;
        }
        sliceState.entities[uniqId] = { ...question, uniqId };
      });
      sliceState.all.loading = false;
      sliceState.all.ids = ids;
      sliceState.all.total = total;
    },

    fetchQuestionsError(sliceState, { payload: { error } }) {
      sliceState.all.error = error.message;
      sliceState.all.loading = false;
    },

    setInitCreateFields(sliceState, { payload }) {
      sliceState.create = payload;
    },

    setCreateFields(
      sliceState,
      {
        payload: {
          mode,
          defaultCategoryCode,
          defaultDifficulty,
          text,
          addQuestion,
          deleteQuestion,
          updateQuestion,
        },
      }
    ) {
      const existingMode = sliceState.create.mode;
      if (mode !== undefined) {
        sliceState.create.mode = mode;
      }
      if (text !== undefined) {
        sliceState.create.byFile.text = text;
      }
      if (defaultCategoryCode !== undefined) {
        sliceState.create[existingMode].defaultCategoryCode = defaultCategoryCode;
      }

      if (defaultDifficulty !== undefined) {
        sliceState.create.byFile.defaultDifficulty = defaultDifficulty;
      }

      if (addQuestion !== undefined) {
        const len = sliceState.create.byHand.ids.length;
        const id = sliceState.create.byHand.ids[len - 1] + 1;
        const question = { ...getEmptyQuestion(), id, type: addQuestion.type };
        sliceState.create.entities[id] = question;
        if (!sliceState.create.byHand.ids.find(i => i == question.id)) {
          sliceState.create.byHand.ids.push(id);
        }
      }

      if (deleteQuestion) {
        sliceState.create.byHand.ids = sliceState.create.byHand.ids.filter(
          id => id !== deleteQuestion.id
        );
        if (sliceState.create.entities[deleteQuestion.id]) {
          delete sliceState.create.entities[deleteQuestion.id];
        }
      }

      if (updateQuestion && updateQuestion.id !== undefined) {
        const updateEntities: BackendQuestion = sliceState.create.entities[updateQuestion.id];

        if (updateQuestion.options !== undefined) {
          updateEntities.options = updateQuestion.options;
        }
        if (updateQuestion.answersForOptions != undefined) {
          updateEntities.answers = updateQuestion.answersForOptions;
        }

        if (updateQuestion.answers !== undefined) {
          const options = updateEntities.options || [];
          let answers: string[] = [];

          if (Array.isArray(updateQuestion.answers)) {
            /**单选题 多选题 */
            if ([1, 2].includes(updateEntities.type)) {
              answers = (updateQuestion.answers as number[]).map(
                (optionIndex: number) => options[optionIndex]
              );
            } else {
              answers = updateQuestion.answers as string[];
            }
          } else if (typeof updateQuestion.answers == 'number') {
            answers.push(options[updateQuestion.answers]);
          } else {
            answers.push(updateQuestion.answers);
          }
          updateEntities.answers = answers;
        }

        if (updateQuestion.autoGrade !== undefined)
          updateEntities.autoGrade = updateQuestion.autoGrade == 1 ? true : false;

        if (updateQuestion.title !== undefined) {
          updateEntities.title = updateQuestion.title;
        }

        if (updateQuestion.difficulty !== undefined) {
          updateEntities.difficulty = updateQuestion.difficulty;
        }

        if (updateQuestion.type !== undefined) {
          updateEntities.type = updateQuestion.type;
        }

        if (updateQuestion.analysis !== undefined) {
          updateEntities.analysis = updateQuestion.analysis;
        }

        if (updateQuestion.categoryCode !== undefined) {
          updateEntities.categoryCode = updateQuestion.categoryCode;
        }
      }
    },
  },
});

export function getInitialAllQustions(): AllTypedQuestions {
  return {
    loading: false,
    error: null,
    fields: {
      page: 1,
      pageSize: 10,
      categoryCode: undefined,
      difficulty: undefined,
      type: undefined,
      title: undefined,
    },
    ids: [],
    selectedIds: [],
    total: 0,
  };
}

export function getInitialCreateQustions(): CreateTypedQuestion {
  return {
    mode: 'byFile',
    entities: {
      0: {
        id: 0,
        ...getEmptyQuestion(),
      },
    },
    byFile: {
      defaultCategoryCode: null,
      defaultDifficulty: 1 /**默认难度为易 */,
      text: null,
    },
    byHand: {
      defaultCategoryCode: null,
      ids: [0],
    },
  };
}

export function getEmptyQuestion(): Omit<BackendQuestion, 'id'> {
  return {
    title: '',
    difficulty: 1,
    categoryCode: '',
    type: 1,
    answers: [],
    options: [],
    analysis: '',
    autoGrade: true,
  };
}

export default questionsSlice.reducer;
