/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { expectSaga } from 'redux-saga-test-plan';

import { getInitialAllQustions, getInitialCreateQustions, questionsSlice } from './questions.slice';
import type { QuestionsSliceState } from './questions.slice';

test('should return the initial state', () => {
  expect(questionsSlice.reducer(undefined, {} as any)).toEqual<QuestionsSliceState>({
    category: {
      entities: {},
      ids: [],
      expandedKeys: [],
      loading: false,
    },
    entities: {},
    all: getInitialAllQustions(),
    create: getInitialCreateQustions(),
  });
});

test('should  handle a `categoryCode` being updated to questions', () => {
  const initialState = questionsSlice.reducer(undefined, {} as any);
  const categoryCode = '1001';
  const questionsState = questionsSlice.reducer(
    initialState,
    questionsSlice.actions.setFields({
      categoryCode,
      page: 1,
      pageSize: 100,
    })
  );
  expect(questionsState.all.fields.categoryCode).toEqual<string | number>(categoryCode);
});

test('should  handle a `create mode` being updated to create', () => {
  const initialState = questionsSlice.reducer(undefined, {} as any);
  const addQuestion = {
    analysis: '分析',
    answers: ['A:拉拉啦', 'B:lalalala'],
    autoGrade: true,
    categoryCode: '10001',
    folderCode: '111',
    difficulty: 1,
    options: ['A:啦啦啦', 'B:lalala'],
    title: '题目',
    type: 1,
    id: 1,
  };
  const questionsState = questionsSlice.reducer(
    initialState,
    questionsSlice.actions.setCreateFields({
      mode: 'byHand',
      addQuestion: addQuestion,
    })
  );
  expect(questionsState.create.mode).toEqual<string>('byHand');
});

test('should  handle a `delete mode` being updated to create', () => {
  const initialState = questionsSlice.reducer(undefined, {} as any);
  const categoryCode = '1001';
  const addQuestion = {
    analysis: '分析',
    answers: ['A:拉拉啦', 'B:lalalala'],
    autoGrade: true,
    categoryCode: '10001',
    folderCode: '111',
    difficulty: 1,
    options: ['A:啦啦啦', 'B:lalala'],
    title: '题目',
    type: 1,
    id: 1,
  };
  const questionsState = questionsSlice.reducer(
    initialState,
    questionsSlice.actions.setCreateFields({
      mode: 'byHand',
      addQuestion: addQuestion,
    })
  );

  const deleteQuestionsState = questionsSlice.reducer(
    questionsState,
    questionsSlice.actions.setCreateFields({
      deleteQuestion: addQuestion,
    })
  );

  expect(deleteQuestionsState.create.byHand.ids).toEqual<[]>([]);
});

test('should  handle a `update mode` being updated to create', () => {
  const initialState = questionsSlice.reducer(undefined, {} as any);
  const categoryCode = '1001';
  const addQuestion = {
    analysis: '分析',
    answers: ['A:拉拉啦', 'B:lalalala'],
    autoGrade: true,
    categoryCode: '10001',
    folderCode: '111',
    difficulty: 1,
    options: ['A:啦啦啦', 'B:lalala'],
    title: '题目',
    type: 1,
    id: 1,
  };
  const questionsState = questionsSlice.reducer(
    initialState,
    questionsSlice.actions.setCreateFields({
      mode: 'byHand',
      addQuestion: addQuestion,
    })
  );

  const updateQuestionsState = questionsSlice.reducer(
    questionsState,
    questionsSlice.actions.setCreateFields({
      updateQuestion: { id: 1, title: '我是修改后的题目' },
    })
  );
  expect(updateQuestionsState.create.entities['1'].title).toEqual<string>('我是修改后的题目');
});
