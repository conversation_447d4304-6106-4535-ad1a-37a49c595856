/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

import { questionsSlice } from './questions.slice';
import type {
  AllTypedQuestions,
  CreateTypedQuestion,
  QuestionsSliceState,
} from './questions.slice';

export const selectQuestionsEntities =
  (ids?: string[]) => (storeState: { [questionsSlice.name]: QuestionsSliceState }) => {
    const { entities } = storeState[questionsSlice.name];
    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[id]);
    }
    return ids.map(id => entities[id]);
  };

export const selectAllTypeQuestions =
  () =>
  (storeState: { [questionsSlice.name]: QuestionsSliceState }): AllTypedQuestions =>
    storeState[questionsSlice.name].all;

export const selectQuestionsCategoryEntities =
  (ids?: number[]) => (storeState: { [questionsSlice.name]: QuestionsSliceState }) => {
    const {
      category: { entities },
    } = storeState[questionsSlice.name];
    if (ids === undefined) return Object.keys(entities).map(id => entities[Number(id)]);
    return ids.map(id => entities[id]);
  };

export const selectQuestionCategory =
  () =>
  (storeState: {
    [questionsSlice.name]: QuestionsSliceState;
  }): {
    entities: Record<number, Category>;
    ids: number[];
    expandedKeys: string[];
    loading: boolean;
  } =>
    storeState[questionsSlice.name].category;

export const selectCreateQuestions =
  () =>
  (storeState: { [questionsSlice.name]: QuestionsSliceState }): CreateTypedQuestion =>
    storeState[questionsSlice.name].create;

export const selectCreateQuestionsEntities =
  (ids?: number[]) =>
  (storeState: { [questionsSlice.name]: QuestionsSliceState }): BackendQuestion[] => {
    const { entities } = storeState[questionsSlice.name].create;
    if (ids === undefined) return Object.keys(entities).map(id => entities[Number(id)]);
    return ids.map(id => entities[id]);
  };
