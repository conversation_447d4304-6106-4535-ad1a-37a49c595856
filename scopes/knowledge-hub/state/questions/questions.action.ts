/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { createAction } from '@reduxjs/toolkit';

import { ReadQuestionsCategoriesPolicy } from '@manyun/knowledge-hub.hook.use-questions-category';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

import { questionsSlice } from './questions.slice';

const prefix = questionsSlice.name;

export const questionsSliceActions = questionsSlice.actions;

export const getQuestionsAction = createAction(prefix + '/GET_QUESTIONS');

export const getQuestionsCategoryAction = createAction<{
  withCount: boolean;
  policy?: ReadQuestionsCategoriesPolicy;
  callback?: () => void;
}>(prefix + '/GET_QUESTIONS_CATEGORY');

export const resetQuestionsSearchAction = createAction<{}>(prefix + '/RESET_QUESTIONS_SEARCH');

export const deleteQuestionsAction = createAction<{
  id?: string /**uniqId */;
  callback: (result: boolean) => void;
}>(prefix + '/DELETE_QUESTIONS');

export const updateQuestionsAction = createAction<{
  question?: BackendQuestion;
  categoryCode?: string;
  callback: (result: boolean) => void;
}>(prefix + '/UPDATE_QUESTIONS');

export const createQustionsAction = createAction<{
  questions: Omit<BackendQuestion, 'id'>[];
  callback: (result: boolean) => void;
}>(prefix + '/CREATE_QUESTIONS');

export const getQuestionAction = createAction<{
  questionId: number;
  callback: (result: BackendQuestion | null) => void;
}>(prefix + '/GET_QUESTION');
