---
description: 'Questions redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import questionsSliceReducer from '@manyun/[scope].state.questions';

const rootReducer = {
  ...questionsSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { questionsWatcher } from '@manyun/[scope].state.questions';

const function* rootSaga() {
  yield all(
    ...questionsWatcher,
    // other sagas...
  );
};
```
