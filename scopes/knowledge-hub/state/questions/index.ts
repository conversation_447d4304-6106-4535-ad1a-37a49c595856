import questionsWatchers from './questions.saga';
import { questionsSlice } from './questions.slice';

export type {
  QuestionsSliceState,
  SetFieldsActionPayload,
  SetCreateFieldsActionPayload,
  CreateMode,
  QuestionsFields,
} from './questions.slice';
export * from './questions.action';
export * from './questions.selector';
export { questionsWatchers };
export default {
  [questionsSlice.name]: questionsSlice.reducer,
};
