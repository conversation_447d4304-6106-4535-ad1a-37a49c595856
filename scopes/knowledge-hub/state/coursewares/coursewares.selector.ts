import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

import { coursewaresSlice } from './coursewares.slice';
import type { CoursewaresSliceState, Variant } from './coursewares.slice';

export const selectCoursewaresEntities =
  (ids?: string[]) => (storeState: { [coursewaresSlice.name]: CoursewaresSliceState }) => {
    const { entities } = storeState[coursewaresSlice.name];

    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[id]);
    }

    return ids.map(id => entities[id]);
  };

export const selectCoursewares =
  (variant: Variant) => (storeState: { [coursewaresSlice.name]: CoursewaresSliceState }) => {
    return storeState[coursewaresSlice.name][variant];
  };

export const selectCoursewareSelectedIds =
  () => (storeState: { [coursewaresSlice.name]: CoursewaresSliceState }) => {
    return storeState[coursewaresSlice.name]['all'].selectedIds;
  };

export const selectCoursewareEntity =
  (id?: string) => (storeState: { [coursewaresSlice.name]: CoursewaresSliceState }) => {
    if (id) {
      return storeState[coursewaresSlice.name].entities[id];
    }
    return {};
  };

export const selectCoursewareCategoryEntities =
  (variant: Variant, ids?: number[]) =>
  (storeState: { [coursewaresSlice.name]: CoursewaresSliceState }) => {
    const {
      category: { entities },
    } = storeState[coursewaresSlice.name][variant];
    if (ids === undefined) return Object.keys(entities).map(id => entities[Number(id)]);
    return ids.map(id => entities[id]);
  };

export const selectCoursewareCategory =
  (variant: Variant) =>
  (storeState: {
    [coursewaresSlice.name]: CoursewaresSliceState;
  }): {
    entities: Record<number, Category>;
    ids: number[];
    expandedKeys: string[];
    loading: boolean;
  } =>
    storeState[coursewaresSlice.name][variant].category;
