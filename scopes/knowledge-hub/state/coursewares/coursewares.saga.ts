import type { Task } from 'redux-saga';
import { call, cancel, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { deleteCoursewaresWeb } from '@manyun/knowledge-hub.service.dcexam.delete-coursewares';
import { fetchCoursewaresWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { mutateCoursewaresWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares';
import type { ServiceD } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares';

import {
  coursewaresSliceActions,
  deleteCoursewaresAction,
  getCoursewaresAction,
  getCoursewaresCategoriesAction,
  mutateCoursewaresAction,
} from './coursewares.action';
import {
  selectCoursewareCategory,
  selectCoursewares,
  selectCoursewaresEntities,
} from './coursewares.selector';
import type { AllCoursewares, Courseware } from './coursewares.slice';
import { Variant } from './coursewares.slice';
import { getStaticReadPolicy, handleSelectedIds } from './coursewares.util';

/** Workers */

export function* getCoursewaresCategoriesSaga({
  payload: { withCount, variant, isAuth },
}: ReturnType<typeof getCoursewaresCategoriesAction>) {
  const existing: SagaReturnType<ReturnType<typeof selectCoursewares>> = yield select(
    selectCoursewares(variant)
  );
  const { categoryCode } = existing.fields;
  const query = {
    categoryType: BackendCategoryType.COURSEWARE,
    needNum: variant === Variant.ALL && withCount,
    isAuth,
  };
  const { data, error }: SagaReturnType<typeof fetchKnowledgeHubCategoryWeb> = yield call(
    fetchKnowledgeHubCategoryWeb,
    query
  );

  if (error) {
    message.error(error.message);
    yield put(coursewaresSliceActions.fetchCategoriesEnd({ variant }));
    return;
  }
  if (!categoryCode) {
    yield put(coursewaresSliceActions.setFields({ variant, categoryCode: 2 }));
  }
  yield put(coursewaresSliceActions.setCategories({ variant, categories: data }));
}

export function* getCoursewaresSaga({
  payload: { variant },
}: ReturnType<typeof getCoursewaresAction>) {
  const existing: SagaReturnType<ReturnType<typeof selectCoursewares>> = yield select(
    selectCoursewares(variant)
  );
  yield put(coursewaresSliceActions.fetchCoursewaresStart({ variant }));
  if (variant === Variant.ALL && (existing as AllCoursewares).selectedIds.length > 0) {
    yield put(coursewaresSliceActions.setSelectedIds({ selectedIds: [] }));
  }
  const { categoryCode, name } = existing.fields;
  const { error, data }: SagaReturnType<typeof fetchCoursewaresWeb> = yield call(
    fetchCoursewaresWeb as unknown,
    {
      variant,
      categoryCode,
      name,
    }
  );

  if (error) {
    message.error(error.message);
    yield put(coursewaresSliceActions.fetchCoursewaresError({ variant }));
    return;
  }
  yield put(coursewaresSliceActions.setCoursewares({ variant, coursewares: data!.data }));
}

export function* deleteCoursewaresSaga({
  payload: { id, callback },
}: ReturnType<typeof deleteCoursewaresAction>) {
  const existing: AllCoursewares = yield select(selectCoursewares(Variant.ALL));
  const coursewares: Courseware[] = yield select(
    selectCoursewaresEntities(id ? [id] : existing.selectedIds)
  );
  const { folderIds, fileIds } = handleSelectedIds(coursewares);

  const { error }: SagaReturnType<typeof deleteCoursewaresWeb> = yield call(deleteCoursewaresWeb, {
    folderIds,
    fileIds,
  });

  if (error) {
    callback(false);
    message.error(error.message);
    return;
  }
  callback(true);
  message.success('删除成功！');
  if (id === undefined) {
    yield put(coursewaresSliceActions.setSelectedIds({ selectedIds: [] }));
  }
  yield put(getCoursewaresAction({ variant: Variant.ALL }));
}

export function* mutateCoursewaresSaga({
  payload: { id, categoryCode, files, callback, type },
}: ReturnType<typeof mutateCoursewaresAction>) {
  const existing: AllCoursewares = yield select(selectCoursewares(Variant.ALL));
  const coursewares: Courseware[] = yield select(
    selectCoursewaresEntities(id === undefined ? existing.selectedIds : [id])
  );
  const { folderIds, fileIds } = handleSelectedIds(coursewares);
  const fileId = id === undefined ? undefined : Number(id.split('_')[1]);
  const q: ServiceD = { categoryCode, fileIds, folderIds, fileId };
  if (files) {
    if (id === undefined) {
      q.fileInfo = files.map(file => {
        const { name, ext, patialPath, size, fileTime } = file;
        if (fileTime) {
          return {
            fileName: name,
            filePath: patialPath,
            fileSize: size!,
            fileType: ext?.toUpperCase()!,
            fileTime: +fileTime,
          };
        }
        return {
          fileName: name,
          filePath: patialPath,
          fileSize: size!,
          fileType: ext?.toUpperCase()!,
        };
      });
    } else {
      const { name, ext, patialPath, size, fileTime } = files[0];
      if (fileTime) {
        q.fileInfo = {
          fileName: name,
          filePath: patialPath,
          fileSize: size!,
          fileType: ext?.toUpperCase()!,
          fileTime: +fileTime,
        };
      } else {
        q.fileInfo = {
          fileName: name,
          filePath: patialPath,
          fileSize: size!,
          fileType: ext?.toUpperCase()!,
        };
      }
    }
  }
  const { error }: SagaReturnType<typeof mutateCoursewaresWeb> = yield call(
    mutateCoursewaresWeb,
    q
  );

  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }
  callback(true);
  if (id) {
    message.success('替换成功！');
  } else if (type === 'single') {
    message.success('上传成功！');
  } else {
    message.success('批量更新成功！');
    yield put(coursewaresSliceActions.setSelectedIds({ selectedIds: [] }));
  }

  yield put(getCoursewaresAction({ variant: Variant.ALL }));
}

/** Watchers */

function* watchGetCoursewaresCategories() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getCoursewaresCategoriesAction> = yield take(
      getCoursewaresCategoriesAction.type
    );
    const existing: SagaReturnType<ReturnType<typeof selectCoursewareCategory>> = yield select(
      selectCoursewareCategory(action.payload.variant)
    );
    const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.ids);
    if (!existing.loading && staticPolicy === 'network-only') {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(coursewaresSliceActions.fetchCategoriesStart({ variant: action.payload.variant }));
      lastTask = yield fork(getCoursewaresCategoriesSaga, action);
    }
  }
}

function* watchGetCoursewares() {
  yield takeLatest(getCoursewaresAction.type, getCoursewaresSaga);
}

function* watchDeleteCoursewares() {
  yield takeLatest(deleteCoursewaresAction.type, deleteCoursewaresSaga);
}

function* watchMutateCoursewares() {
  yield takeLatest(mutateCoursewaresAction.type, mutateCoursewaresSaga);
}

export default [
  fork(watchGetCoursewaresCategories),
  fork(watchGetCoursewares),
  fork(watchDeleteCoursewares),
  fork(watchMutateCoursewares),
];
