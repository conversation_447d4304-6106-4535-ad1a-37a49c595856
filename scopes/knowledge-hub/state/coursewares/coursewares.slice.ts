import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import type { BackendCourseware } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

export type Courseware = BackendCourseware & { groupKey: string };

//用以区分使用场景，all为课件列表页面，auth为知识库页面
export enum Variant {
  ALL = 'all',
  Auth = 'auth',
}

export enum Type {
  File = 'file',
  Category = 'category',
}

export type Fields = {
  name?: string;
  categoryCode?: number;
};

export type AllCoursewares = {
  selectedIds: string[];
  reRender: boolean;
} & AuthCoursewares;

export type AuthCoursewares = {
  reRender: boolean;
  fields: Fields;
  loading: boolean;
  ids: string[];
  category: {
    loading: boolean;
    entities: Record<number, Category>;
    ids: number[];
    expandedKeys: string[];
  };
};

export type CoursewaresSliceState = {
  entities: Record<string, Courseware>;

  all: AllCoursewares;
  auth: AuthCoursewares;
};

type CoursewaresSliceCaseReducers = {
  fetchCategoriesStart: CaseReducer<CoursewaresSliceState, PayloadAction<{ variant: Variant }>>;
  fetchCategoriesEnd: CaseReducer<CoursewaresSliceState, PayloadAction<{ variant: Variant }>>;
  setCategories: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{ variant: Variant; categories: Category[] }>
  >;
  setCategoriesUpdate: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{ id: number; name: string }>
  >;
  setCategoriesDelete: CaseReducer<CoursewaresSliceState, PayloadAction<{ id: number }>>;
  setCategoriesAdd: CaseReducer<CoursewaresSliceState, PayloadAction<{ category: Category }>>;

  setFields: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{ variant: Variant; name?: string; categoryCode?: number }>
  >;
  resetFields: CaseReducer<CoursewaresSliceState, PayloadAction<{ variant: Variant }>>;
  setSelectedIds: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{
      selectedIds: string[];
    }>
  >;
  fetchCoursewaresStart: CaseReducer<CoursewaresSliceState, PayloadAction<{ variant: Variant }>>;
  setCoursewares: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{
      variant: Variant;
      coursewares: Courseware[];
    }>
  >;
  fetchCoursewaresError: CaseReducer<CoursewaresSliceState, PayloadAction<{ variant: Variant }>>;
  toggleReRender: CaseReducer<CoursewaresSliceState>;
  setExpandedKeys: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{
      variant: Variant;
      expandedKeys: string[];
    }>
  >;
  updateCoursewares: CaseReducer<
    CoursewaresSliceState,
    PayloadAction<{ coursewares: BackendCourseware[] }>
  >;
};

export const coursewaresSlice = createSlice<
  CoursewaresSliceState,
  CoursewaresSliceCaseReducers,
  'knowledge-hub.coursewares'
>({
  name: 'knowledge-hub.coursewares',
  initialState: {
    entities: {},
    all: getInitialAllCoursewares(),
    auth: getInitialAuthCoursewares(),
  },
  reducers: {
    fetchCategoriesStart(sliceState, { payload: { variant } }) {
      sliceState[variant].category.loading = true;
    },
    fetchCategoriesEnd(sliceState, { payload: { variant } }) {
      sliceState[variant].category.loading = false;
    },
    setCategories(sliceState, { payload: { variant, categories } }) {
      const ids: number[] = [];
      categories.forEach(category => {
        ids.push(category.id);
        sliceState[variant].category.entities[category.id] = category;
      });
      sliceState[variant].category.ids = ids;
      sliceState[variant].category.loading = false;
    },
    setCategoriesUpdate(sliceState, { payload: { id, name } }) {
      const updateCategory = sliceState['all'].category.entities[id];
      updateCategory.name = name;
    },

    setCategoriesDelete(sliceState, { payload: { id } }) {
      sliceState['all'].category.ids = sliceState['all'].category.ids.filter(key => id !== key);
      delete sliceState['all'].category.entities[id];
    },

    setCategoriesAdd(sliceState, { payload: { category } }) {
      sliceState['all'].category.ids.push(category.id);
      sliceState['all'].category.entities[category.id] = category;
    },
    setFields(sliceState, { payload: { variant, name, categoryCode } }) {
      if (name !== undefined) {
        sliceState[variant].fields.name = name ? name.trim() : name;
      }
      if (categoryCode !== undefined) {
        sliceState[variant].fields.categoryCode = categoryCode;
      }
    },
    resetFields(sliceState, { payload: { variant } }) {
      sliceState[variant].fields.name = undefined;
    },
    setSelectedIds(sliceState, { payload: { selectedIds } }) {
      sliceState[Variant.ALL].selectedIds = selectedIds;
    },
    fetchCoursewaresStart(sliceState, { payload: { variant } }) {
      sliceState[variant].loading = true;
      sliceState[variant].ids = [];
    },
    setCoursewares(sliceState, { payload: { coursewares, variant } }) {
      const ids: string[] = [];
      coursewares.forEach(courseware => {
        ids.push(courseware.groupKey);

        // cache update performance optimization
        // const existingCourseware = sliceState.entities[courseware.groupKey];
        // if (existingCourseware && existingCourseware.gmtModified === courseware.gmtModified) {
        //   return;
        // }

        sliceState.entities[courseware.groupKey] = courseware;
      });
      sliceState[variant].ids = ids;
      sliceState[variant].loading = false;
    },
    fetchCoursewaresError(sliceState, { payload: { variant } }) {
      sliceState[variant].loading = false;
    },
    toggleReRender(sliceState) {
      sliceState[Variant.ALL].reRender = !sliceState[Variant.ALL].reRender;
    },
    setExpandedKeys(sliceState, { payload: { expandedKeys, variant } }) {
      sliceState[variant].category.expandedKeys = expandedKeys;
    },
    updateCoursewares(sliceState, { payload: { coursewares } }) {
      coursewares.forEach(courseware => {
        const groupKey = `${courseware.fileType}_${courseware.id}`;
        const existingCourseware = sliceState.entities[groupKey];
        if (existingCourseware.gmtModified === courseware.gmtModified) {
          return;
        }
        sliceState.entities[groupKey] = { ...existingCourseware, ...courseware };
      });
    },
  },
});

export function getInitialAuthCoursewares(): AuthCoursewares {
  return {
    loading: false,
    ids: [],
    fields: {
      categoryCode: 0,
    },
    category: {
      loading: false,
      entities: {},
      ids: [],
      expandedKeys: ['2'],
    },
    reRender: false,
  };
}

export function getInitialAllCoursewares(): AllCoursewares {
  const initialMyCoursewares = getInitialAuthCoursewares();

  return {
    ...initialMyCoursewares,
    selectedIds: [],
    reRender: false,
  };
}
