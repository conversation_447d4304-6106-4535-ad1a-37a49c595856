import { createAction } from '@reduxjs/toolkit';

import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { ReadCoursewaresCategoriesPolicy } from '@manyun/knowledge-hub.hook.use-coursewares-category';

import type { Type, Variant } from './coursewares.slice';
import { coursewaresSlice } from './coursewares.slice';

const prefix = coursewaresSlice.name;

export const coursewaresSliceActions = coursewaresSlice.actions;

export const getCoursewaresAction = createAction<{ variant: Variant }>(prefix + '/GET_COURSEWARES');

export const deleteCoursewaresAction = createAction<{
  id?: string;
  callback: (result: boolean) => void;
}>(prefix + '/DELETE_COURSEWARES');

export const mutateCoursewaresAction = createAction<{
  id?: string;
  categoryCode?: string;
  files?: (McUploadFile & { fileTime?: string })[];
  type?: string;
  callback: (result: boolean) => void;
}>(prefix + '/MUTATE_COURSEWARES');

export const getCoursewaresAuthorizedUsersAction = createAction<{ id: number; type: Type }>(
  prefix + '/GET_COURSEWARES_AUTHORIZED_USERS'
);

export const grantCoursewarePermissionsAction = createAction<{ id: number; type: Type; roles: [] }>(
  prefix + '/GRANT_COURSEWARE_PERMISSIONS'
);

export const getCoursewaresCategoriesAction = createAction<{
  variant: Variant;
  policy?: ReadCoursewaresCategoriesPolicy;
  withCount?: boolean;
  isAuth?: boolean;
}>(prefix + '/GET_COURSEWARES_CATEGORIES');
