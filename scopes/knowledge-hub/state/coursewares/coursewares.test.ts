import { expectSaga } from 'redux-saga-test-plan';

import {
  coursewaresSlice,
  getInitialAllCoursewares,
  getInitialAuthCoursewares,
} from './coursewares.slice';
import type { CoursewaresSliceState } from './coursewares.slice';

test('should return the initial state', () => {
  expect(coursewaresSlice.reducer(undefined, {} as any)).toEqual<CoursewaresSliceState>({
    entities: {},
    all: getInitialAllCoursewares(),
    auth: getInitialAuthCoursewares(),
  });
});
