---
description: 'Coursewares redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import coursewaresSliceReducer from '@manyun/[scope].state.coursewares';

const rootReducer = {
  ...coursewaresSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { coursewaresWatchers } from '@manyun/[scope].state.coursewares';

const function* rootSaga() {
  yield all(
    ...coursewaresWatchers,
    // other sagas...
  );
};
```