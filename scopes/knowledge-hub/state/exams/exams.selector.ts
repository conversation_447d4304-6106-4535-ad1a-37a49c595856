import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

import { examsSlice } from './exams.slice';
import type { ExamTypes, ExamsSliceState, MyExamTypes } from './exams.slice';

export const selectExamsEntities =
  (ids?: number[]) => (storeState: { [examsSlice.name]: ExamsSliceState }) => {
    const { examsEntities } = storeState[examsSlice.name].exams;

    if (ids === undefined) {
      return Object.keys(examsEntities).map(id => examsEntities[id]);
    }

    return ids.map(id => examsEntities[id]);
  };

export const selectTypedExams =
  <T extends ExamTypes = ExamTypes.AllExam>(examType: T) =>
  (storeState: { [examsSlice.name]: ExamsSliceState }): ExamsSliceState['exams'][T] =>
    storeState[examsSlice.name].exams[examType];

export const selectMyExamsEntities =
  (examIds?: number[]) => (storeState: { [examsSlice.name]: ExamsSliceState }) => {
    const { myExamsEntities } = storeState[examsSlice.name].myExams;

    if (examIds === undefined) {
      return Object.keys(myExamsEntities).map(examId => myExamsEntities[examId]);
    }

    return examIds.map(examId => myExamsEntities[examId]);
  };

export const selectTypedMyExams =
  <T extends MyExamTypes = MyExamTypes>(myExamType: T) =>
  (storeState: { [examsSlice.name]: ExamsSliceState }): ExamsSliceState['myExams'][T] =>
    storeState[examsSlice.name].myExams[myExamType];

export const selectUserExamPaper = () => (storeState: { [examsSlice.name]: ExamsSliceState }) =>
  storeState[examsSlice.name].userExamPaper;

export const selectExamsCategory = (storeState: {
  [examsSlice.name]: ExamsSliceState;
}): ExamsSliceState['exams']['category'] => storeState[examsSlice.name].exams.category;

export const selectActiveExamStatusTabKey =
  () =>
  (storeState: {
    [examsSlice.name]: ExamsSliceState;
  }): ExamsSliceState['myExams']['activeExamStatusTabKey'] =>
    storeState[examsSlice.name].myExams.activeExamStatusTabKey;

export const selectActiveExamTypeTabKey =
  () =>
  (storeState: {
    [examsSlice.name]: ExamsSliceState;
  }): ExamsSliceState['myExams']['activeExamTypeTabKey'] =>
    storeState[examsSlice.name].myExams.activeExamTypeTabKey;

export const selectAllowMarkingExamSearchValues =
  () =>
  (storeState: {
    [examsSlice.name]: ExamsSliceState;
  }): ExamsSliceState['exams']['allowMarkingExam']['fields'] =>
    storeState[examsSlice.name].exams.allowMarkingExam.fields;
