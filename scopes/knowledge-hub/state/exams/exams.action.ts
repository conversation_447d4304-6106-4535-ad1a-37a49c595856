import { createAction } from '@reduxjs/toolkit';

import type { ReadExamsCategoriesPolicy } from '@manyun/knowledge-hub.hook.use-exams-category';
import type { BackendUserInfo } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users';
import type { ExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type { ExamPaperUser } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type { Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

import { examsSlice } from './exams.slice';
import type { ExamTypes, MyExamTypes } from './exams.slice';

const prefix = examsSlice.name;

export const examsSliceActions = examsSlice.actions;

export const getExamsCategoriesAction = createAction<{
  policy?: ReadExamsCategoriesPolicy;
  withCount: boolean;
}>(prefix + '/GET_EXAMS_CATEGORIES');

/**考试分页数据 */
export const getExamsAction = createAction<{ examType: ExamTypes }>(prefix + '/GET_EXAMS');

/**考试对应的考卷列表 */
export const getExamPaperUsersAction = createAction<{
  examId: number;
  callback: (result: { data: ExamPaperUser[]; total: number } | null) => void;
}>(prefix + '/GET_EXAM_PAPER_USERS');

/**考试明细 */
export const getExamAction = createAction<{
  examId: number;
  callback: (result: Exam | null) => void;
}>(prefix + '/GET_EXAM');

/**取消考试 - 可判卷的考试无法取消 */
export const cancleExamAction = createAction<{
  examId: number;
}>(prefix + '/CANCLE_EXAM');

/**批量更新、单条更新 - 仅在全部考试列表中 */
export const updateExamsAction = createAction<{
  exam?: Exam;
  categoryCode?: number | string;
  callback: (result: boolean) => void;
}>(prefix + '/UPDATE_EXAMS');

/**创建考试 */
export const createExamAction = createAction<{
  exam: Omit<Exam, 'id'>;
  callback: (result: boolean) => void;
}>(prefix + '/CREATE_EXAM');

/**我的考卷列表 */
export const getMyExamsAction = createAction<{ myExamType: MyExamTypes }>(prefix + '/GET_MY_EXAMS');

/**获取补考人员 */
export const getExamFailedUserAction = createAction<{
  examId: number;
  callback: (result: BackendUserInfo[] | null) => void;
}>(prefix + '/GET_EXAM_FAILED_USERS');

/** 发起补考 */
export const requestMakeUpTestAction = createAction<{
  examId: number;
  roleIds?: number[];
  userIds?: number[];
  timeRange: [number, number];
  callback: (result: boolean) => void;
}>(prefix + '/REQUEST_MAKE_UP_TEST');

/**考卷详情 */
export const getExamPaperAction = createAction<{
  examPaperId: number;
  callback: (result: ExamPaper | null) => void;
}>(prefix + '/GET_EXAM_PAPER');
