import type { Task } from 'redux-saga';
import { call, cancel, fork, put, select, take, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';
import { HISTORY_EXAM_STATE_KEY } from '@manyun/knowledge-hub.page.my-exams';
import { cancleExamWeb } from '@manyun/knowledge-hub.service.dcexam.cancle-exam';
import { fetchExamWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam';
import { fetchExamFailedUsersWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users';
import type { ExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { fetchExamPaperWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type {
  BackendMyExam,
  ExamPaperUser,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { fetchExamPaperUsersWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type { Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { fetchExamsWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { mutateExamsWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-exams';
import { requestMakeupTestWeb } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test';

import {
  cancleExamAction,
  createExamAction,
  examsSliceActions,
  getExamAction,
  getExamFailedUserAction,
  getExamPaperAction,
  getExamPaperUsersAction,
  getExamsAction,
  getExamsCategoriesAction,
  getMyExamsAction,
  requestMakeUpTestAction,
  updateExamsAction,
} from './exams.action';
import { selectExamsCategory, selectTypedExams, selectTypedMyExams } from './exams.selector';
import type { AllTypedExams } from './exams.slice';
import { ExamTypes, MyExamTypes, getInitialCreateExam } from './exams.slice';
import { getStaticReadPolicy } from './exams.util';

/** Workers */

export function* getExamsCategoriesSaga({
  payload: { withCount },
}: ReturnType<typeof getExamsCategoriesAction>) {
  const query = {
    categoryType: BackendCategoryType.EXAM,
    needNum: withCount,
  };
  const { data, error }: SagaReturnType<typeof fetchKnowledgeHubCategoryWeb> = yield call(
    fetchKnowledgeHubCategoryWeb,
    query
  );

  if (error) {
    message.error(error.message);
    return;
  }

  yield put(examsSliceActions.setExamCategories({ categories: data }));
}

export function* getExamsSaga({ payload: { examType } }: ReturnType<typeof getExamsAction>) {
  const existing: SagaReturnType<ReturnType<typeof selectTypedExams>> = yield select(
    selectTypedExams(examType)
  );
  yield put(examsSliceActions.fetchExamsStart({ examType, ...existing.fields }));
  if (examType === ExamTypes.AllExam && (existing as AllTypedExams).selectedIds.length > 0) {
    yield put(examsSliceActions.setExamsSeletedIds({ selectedIds: [] }));
  }
  const { error, data }: SagaReturnType<typeof fetchExamsWeb> = yield call<typeof fetchExamsWeb>(
    fetchExamsWeb,
    {
      variant: examType,
      page: existing.fields.page,
      pageSize: existing.fields.pageSize,
      examName: existing.fields.examName,
      timeRange: existing.fields.timeRange,
      examStatus: existing.fields.examStatus,
      categoryCode:
        existing.fields.categoryCode && existing.fields.categoryCode.length === 3
          ? existing.fields.categoryCode[2]
          : undefined,
      orderByModifyTimeDesc: existing.fields.orderByModifyTimeDesc,
    }
  );

  if (error) {
    message.error(error.message);
    yield put(examsSliceActions.fetchExamsError({ examType, error }));
    return;
  }

  yield put(examsSliceActions.setExams({ examType, exams: data.data, total: data.total }));
}

function* getExamPaperUsersSaga({
  payload: { examId, callback },
}: ReturnType<typeof getExamPaperUsersAction>) {
  const { data, error }: SagaReturnType<typeof fetchExamPaperUsersWeb> = yield call(
    fetchExamPaperUsersWeb,
    {
      examId,
    }
  );
  if (error) {
    message.error(error.message);
    callback(null);
    return;
  }
  callback({ data: data.data as ExamPaperUser[], total: data.total });
}

export function* getExamSaga({ payload: { examId, callback } }: ReturnType<typeof getExamAction>) {
  const { data, error }: SagaReturnType<typeof fetchExamWeb> = yield call(fetchExamWeb, {
    examId,
  });
  if (error) {
    message.error(error.message);
    callback(null);
    return;
  }
  callback(data);
}

export function* updateExamsSaga({
  payload: { exam, categoryCode, callback },
}: ReturnType<typeof updateExamsAction>) {
  let updateExamsEntities: Exam | Pick<Exam, 'id' | 'categoryCode'>[];
  if (exam !== undefined) {
    updateExamsEntities = exam;
  } else {
    const existingAllExams: SagaReturnType<ReturnType<typeof selectTypedExams>> = yield select(
      selectTypedExams(ExamTypes.AllExam)
    );
    updateExamsEntities = (existingAllExams as AllTypedExams).selectedIds.map(id => ({
      id,
      categoryCode,
    })) as Pick<Exam, 'id' | 'categoryCode'>[];
  }
  const { error } = yield call(mutateExamsWeb, updateExamsEntities);

  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }

  message.success('更新成功!');
  callback(true);

  if (exam === undefined) {
    yield put(examsSliceActions.setExamsSeletedIds({ selectedIds: [] }));
    yield put(getExamsAction({ examType: ExamTypes.AllExam }));
  }
}

export function* cancleExamSaga({ payload: { examId } }: ReturnType<typeof cancleExamAction>) {
  const { error } = yield call(cancleExamWeb, { examId });

  if (error) {
    message.error(error.message);
    return;
  }

  message.success('取消考试成功!');
  yield put(getExamsAction({ examType: ExamTypes.AllExam }));
}

export function* createExamSaga({
  payload: { exam, callback },
}: ReturnType<typeof createExamAction>) {
  const { error } = yield call(mutateExamsWeb, exam);
  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }
  // message.success('考试创建成功!');

  yield put(examsSliceActions.setCreateExamFields(getInitialCreateExam()));
  if (callback) {
    callback(true);
  }
}

function* getMyExamsSaga({ payload: { myExamType } }: ReturnType<typeof getMyExamsAction>) {
  yield put(examsSliceActions.fetchMyExamsStart({ myExamType }));
  const existing: SagaReturnType<ReturnType<typeof selectTypedMyExams>> = yield select(
    selectTypedMyExams(myExamType)
  );
  if (existing.visibleIds.length > 0) {
    yield put(examsSliceActions.setMyExamsVisibleIds({ myExamType, visibleIds: [] }));
  }
  const {
    fields: { name, timeRange },
  } = existing;
  const params = {
    userExamName: name?.trim(),
    timeRange,
  };
  const { error, data }: SagaReturnType<typeof fetchExamPaperUsersWeb> = yield call(
    fetchExamPaperUsersWeb as unknown,
    {
      variant: myExamType,
      ...params,
    }
  );

  if (error) {
    message.error(error.message);
    yield put(examsSliceActions.fetchMyExamsError({ myExamType, error }));
    return;
  }
  yield put(examsSliceActions.setMyExams({ myExamType, myExams: data.data as BackendMyExam[] }));
  if (existing.fields.state) {
    const visibleIds: number[] = [];
    (data.data as BackendMyExam[]).map(myExam => {
      if (myExamType === MyExamTypes.MyAvailableExams) {
        //可参加考试
        const result = searchFilterAvailableData({ ...existing.fields, myExam });
        if (result) {
          visibleIds.push(myExam.examId);
        }
      } else {
        //历史考试
        const result = searchFilterHistoryData({ ...existing.fields, myExam });
        if (result) {
          visibleIds.push(myExam.examId);
        }
      }
      return myExam;
    });
    yield put(examsSliceActions.setMyExamsVisibleIds({ myExamType, visibleIds }));
  } else {
    const visibleIds = data.data.map(({ examId }) => examId);
    yield put(examsSliceActions.setMyExamsVisibleIds({ myExamType, visibleIds }));
  }
}

function searchFilterAvailableData({ state, myExam }: unknown) {
  let dataStatus = true;

  if (dataStatus && state && state !== myExam.status) {
    dataStatus = false;
  }
  return dataStatus;
}

function searchFilterHistoryData({ state, myExam }: unknown) {
  let dataStatus = true;
  if (dataStatus && state) {
    if (
      state === HISTORY_EXAM_STATE_KEY.SUBMIT &&
      myExam.status !== HISTORY_EXAM_STATE_KEY.SUBMIT
    ) {
      dataStatus = false;
    }

    if (state === HISTORY_EXAM_STATE_KEY.PASS && !myExam.pass) {
      dataStatus = false;
    }

    if (state === HISTORY_EXAM_STATE_KEY.FAIL && myExam.pass && myExam.status === 'GRADED') {
      dataStatus = false;
    }
  }
  return dataStatus;
}

export function* getExamFailedUsersSaga({
  payload: { examId, callback },
}: ReturnType<typeof getExamFailedUserAction>) {
  const { data, error }: SagaReturnType<typeof fetchExamFailedUsersWeb> = yield call(
    fetchExamFailedUsersWeb,
    {
      examId,
    }
  );
  if (error) {
    message.error(error.message);
    if (callback) {
      callback(null);
    }
    return;
  }
  if (callback) {
    callback(data!.data);
  }
}

export function* requestMakeUpTestSaga({
  payload: { examId, roleIds, userIds, timeRange, callback },
}: ReturnType<typeof requestMakeUpTestAction>) {
  const { error }: SagaReturnType<typeof requestMakeupTestWeb> = yield call(
    requestMakeupTestWeb as unknown,
    {
      examId,
      roleIds,
      userIds,
      timeRange,
    }
  );
  if (error) {
    message.error(error.message);
    callback(false);
    return;
  }
  callback(false);
}

export function* getExamPaperSaga({
  payload: { examPaperId, callback },
}: ReturnType<typeof getExamPaperAction>) {
  const { data, error }: SagaReturnType<typeof fetchExamPaperWeb> = yield call(
    fetchExamPaperWeb as unknown,
    {
      examPaperId,
    }
  );
  if (error) {
    message.error(error.message);
    callback(null);
    return;
  }
  callback(data as ExamPaper);
}

/** Watchers */

function* watchGetExamsCategories() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getExamsCategoriesAction> = yield take(
      getExamsCategoriesAction.type
    );
    const existing: SagaReturnType<typeof selectExamsCategory> = yield select(selectExamsCategory);
    const staticPolicy = getStaticReadPolicy(action.payload.policy, existing.ids);
    if (!existing.loading && staticPolicy === 'network-only') {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(examsSliceActions.fetchExamsCategoriesStart({}));
      lastTask = yield fork(getExamsCategoriesSaga, action);
    }
  }
}

function* watchGetExams() {
  yield takeLatest(getExamsAction.type, getExamsSaga);
}

function* watchGetExamPaperUsers() {
  yield takeLatest(getExamPaperUsersAction.type, getExamPaperUsersSaga);
}

function* watchGetExam() {
  yield takeLatest(getExamAction.type, getExamSaga);
}

function* watchCancleExam() {
  yield takeLatest(cancleExamAction.type, cancleExamSaga);
}

function* watchUpdateExams() {
  yield takeLatest(updateExamsAction.type, updateExamsSaga);
}

function* watchGetMyExams() {
  yield takeLatest(getMyExamsAction.type, getMyExamsSaga);
}

function* watchCreateExam() {
  yield takeLatest(createExamAction.type, createExamSaga);
}

function* watchGetExamFailedUsers() {
  yield takeLatest(getExamFailedUserAction.type, getExamFailedUsersSaga);
}

function* watchRequestMakeUpTest() {
  yield takeLatest(requestMakeUpTestAction.type, requestMakeUpTestSaga);
}

function* watchFetchExamPaper() {
  yield takeLatest(getExamPaperAction.type, getExamPaperSaga);
}

export default [
  fork(watchGetExamsCategories),
  fork(watchGetExams),
  fork(watchGetExamPaperUsers),
  fork(watchGetExam),
  fork(watchCancleExam),
  fork(watchUpdateExams),
  fork(watchCreateExam),
  fork(watchGetMyExams),
  fork(watchGetExamFailedUsers),
  fork(watchRequestMakeUpTest),
  fork(watchFetchExamPaper),
];
