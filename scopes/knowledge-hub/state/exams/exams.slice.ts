import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import moment from 'moment';

import type {
  ExamPaper,
  QuestionsGroup,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type { BackendMyExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type {
  BackendExam,
  BackendExamStatus,
  Exam,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  BackendExamType,
  Variant as ExamTypes,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

export { ExamTypes };

export enum MyExamTypes {
  MyAvailableExams = 'myAvailable',
  MyHistoryExams = 'myHistory',
}

export type ExamFields = {
  timeRange?: [number, number];
  examStatus?: BackendExamStatus;
  examName?: string;
  categoryCode?: number[];
  page: number;
  pageSize: number;
  orderByModifyTimeDesc?: boolean | null;
};

export type SetExamsFieldsActionPayload = Partial<ExamFields> & { examsType: ExamTypes };

export interface MarkTypedExams {
  loading: boolean;
  error: string | null;
  ids: number[];
  total: number;
  fields: ExamFields;
}

export interface AllTypedExams extends MarkTypedExams {
  selectedIds: number[];
}

export enum MutateExamType {
  CreateExam = 'create',
  UpdateExam = 'update',
}

export type MyExamFields = {
  timeRange: [] | [number, number];
  state: string | null;
  name: string | null;
  required?: boolean;
};

export type SetMyExamsFieldsActionPayload = Partial<MyExamFields> & { myExamType: MyExamTypes };

export interface MyExams {
  fields: MyExamFields;
  ids: number[];
  visibleIds: number[];
  error: string | null;
  loading: boolean;
}

export type SetExamPaperFieldsActionPayload = {
  displayManuallyMarkingQuestionsOnly?: boolean; // 是否只查看需要人工判题
  displayErrorQuestionsOnly?: boolean; //是否仅显示错误题目
  answerQuestion?: {
    //答题
    questionGroupIndex: number; //板块的索引
    questionId: number;
    userAnswers: string[];
  };
  markQuestion?: {
    //判题
    questionGroupIndex: number; //板块的索引
    questionId: number;
    userGrade?: number;
    correct?: boolean | null; //是否正确错误
  };
};

export type ExamsSliceState = {
  exams: {
    category: {
      loading: boolean;
      entities: Record<number, Category>;
      ids: number[];
    };
    examsEntities: Record<string, BackendExam | undefined>;
    [ExamTypes.AllExam]: AllTypedExams;
    [ExamTypes.AllowMarkingExam]: MarkTypedExams;
    [MutateExamType.CreateExam]: Omit<Exam, 'id'>;
    [MutateExamType.UpdateExam]: Exam;
  };
  myExams: {
    myExamsEntities: Record<string, BackendMyExam>;
    [MyExamTypes.MyAvailableExams]: MyExams;
    [MyExamTypes.MyHistoryExams]: MyExams;
    activeExamStatusTabKey: MyExamTypes.MyAvailableExams | MyExamTypes.MyHistoryExams;
    activeExamTypeTabKey: 'required' | 'slected';
  };
  userExamPaper: ExamPaper; // todo
};

type ExamsSliceCaseReducers = {
  fetchExamsCategoriesStart: CaseReducer<ExamsSliceState, PayloadAction<unknown>>;
  setExamCategories: CaseReducer<ExamsSliceState, PayloadAction<{ categories: Category[] }>>;

  setExamsFields: CaseReducer<ExamsSliceState, PayloadAction<SetExamsFieldsActionPayload>>;
  fetchExamsStart: CaseReducer<ExamsSliceState, PayloadAction<{ examType: ExamTypes }>>;
  setExams: CaseReducer<
    ExamsSliceState,
    PayloadAction<{
      examType: ExamTypes;
      exams: BackendExam[];
      total: number;
    }>
  >;
  fetchExamsError: CaseReducer<
    ExamsSliceState,
    PayloadAction<{ examType: ExamTypes; error: { message: string } }>
  >;
  setExamsSeletedIds: CaseReducer<ExamsSliceState, PayloadAction<{ selectedIds: number[] }>>;

  setCreateExamFields: CaseReducer<ExamsSliceState, PayloadAction<Partial<Exam>>>;

  setUpdateExamFields: CaseReducer<ExamsSliceState, PayloadAction<Partial<Exam>>>;

  setMyExamsFields: CaseReducer<ExamsSliceState, PayloadAction<SetMyExamsFieldsActionPayload>>;
  fetchMyExamsStart: CaseReducer<ExamsSliceState, PayloadAction<{ myExamType: MyExamTypes }>>;
  setMyExams: CaseReducer<
    ExamsSliceState,
    PayloadAction<{
      myExamType: MyExamTypes;
      myExams: BackendMyExam[];
    }>
  >;
  fetchMyExamsError: CaseReducer<
    ExamsSliceState,
    PayloadAction<{ myExamType: MyExamTypes; error: { message: string } }>
  >;
  setMyExamsVisibleIds: CaseReducer<
    ExamsSliceState,
    PayloadAction<{ myExamType: MyExamTypes; visibleIds: number[] }>
  >;

  setInitialUserExamPaperFields: CaseReducer<ExamsSliceState, PayloadAction<ExamPaper>>;

  setUserExamPaperFields: CaseReducer<
    ExamsSliceState,
    PayloadAction<SetExamPaperFieldsActionPayload>
  >;

  updateActiveExamStatusTabKey: CaseReducer<
    ExamsSliceState,
    PayloadAction<MyExamTypes.MyAvailableExams | MyExamTypes.MyHistoryExams>
  >;

  updateActiveExamTypeTabKey: CaseReducer<ExamsSliceState, PayloadAction<'required' | 'slected'>>;

  setAllowMarkingExamSearchValues: CaseReducer<
    ExamsSliceState,
    PayloadAction<{
      examName?: string;
      timeRange?: [number, number];
      examStatus?: BackendExamStatus;
      categoryCode?: number[];
      page?: number;
      pageSize?: number;
    }>
  >;
};

export const examsSlice = createSlice<
  ExamsSliceState,
  ExamsSliceCaseReducers,
  'knowledge-hub.exams'
>({
  name: 'knowledge-hub.exams',
  initialState: {
    exams: {
      category: {
        loading: false,
        entities: {}, // category entities
        ids: [],
      },
      examsEntities: {},
      [ExamTypes.AllExam]: getInitialAllExams(),
      [ExamTypes.AllowMarkingExam]: getInitialMarkExams(),
      [MutateExamType.CreateExam]: getInitialCreateExam() as unknown,
      [MutateExamType.UpdateExam]: getInitialUpdateExam() as unknown,
    },
    myExams: {
      myExamsEntities: {},
      [MyExamTypes.MyAvailableExams]: getInitalMyExams({ type: 'available' }),
      [MyExamTypes.MyHistoryExams]: getInitalMyExams({ type: 'history' }),
      activeExamStatusTabKey: MyExamTypes.MyAvailableExams,
      activeExamTypeTabKey: 'required',
    },
    userExamPaper: getInitUserExamPaper(),
  },
  reducers: {
    fetchExamsCategoriesStart(sliceState) {
      sliceState.exams.category.loading = true;
    },
    setExamCategories(sliceState, { payload: { categories } }) {
      const ids: number[] = [];
      categories.forEach(category => {
        ids.push(category.id);
        sliceState.exams.category.entities[category.id] = category;
      });
      sliceState.exams.category.ids = ids;
      sliceState.exams.category.loading = false;
    },

    setExamsFields(sliceState, { payload: { examsType, ...params } }) {
      sliceState.exams[examsType].fields = {
        ...sliceState.exams[examsType].fields,
        ...params,
      };
    },

    fetchExamsStart(sliceState, { payload: { examType } }) {
      sliceState.exams[examType].ids = [];
      sliceState.exams[examType].error = null;
      sliceState.exams[examType].loading = true;
    },

    setExams(sliceState, { payload: { examType, exams, total } }) {
      const ids: number[] = [];
      exams.forEach(exam => {
        ids.push(exam.id);
        // cache update performance optimization
        const existingExam = sliceState.exams.examsEntities[exam.id];
        if (existingExam && existingExam.gmtModified === exam.gmtModified) {
          return;
        }
        sliceState.exams.examsEntities[exam.id] = exam;
      });
      sliceState.exams[examType].ids = ids;
      sliceState.exams[examType].loading = false;
      sliceState.exams[examType].total = total;
    },

    fetchExamsError(sliceState, { payload: { examType, error } }) {
      sliceState.exams[examType].error = error.message;
      sliceState.exams[examType].loading = false;
    },

    setExamsSeletedIds(sliceState, { payload: { selectedIds } }) {
      sliceState.exams[ExamTypes.AllExam].selectedIds = selectedIds;
    },

    //  todo test
    setCreateExamFields(sliceState, { payload }) {
      Object.keys(payload).forEach(key => {
        if (
          (payload as unknown)[key] !== undefined &&
          (sliceState.exams[MutateExamType.CreateExam] as unknown)[key] !== undefined
        ) {
          (sliceState.exams[MutateExamType.CreateExam] as unknown)[key] = (payload as unknown)[key];
        }
      });
    },

    setUpdateExamFields(sliceState, { payload }) {
      Object.keys(payload).map(key => {
        if (
          (payload as unknown)[key] !== undefined &&
          (sliceState.exams[MutateExamType.UpdateExam] as unknown)[key] !== undefined
        ) {
          (sliceState.exams[MutateExamType.UpdateExam] as unknown)[key] = (payload as unknown)[key];
        }
        return key;
      });
    },

    setMyExamsFields(sliceState, { payload: { myExamType, ...props } }) {
      if (props.timeRange) {
        sliceState.myExams[myExamType].fields = {
          ...sliceState.myExams[myExamType].fields,
          ...props,
          timeRange: props.timeRange.length
            ? (props.timeRange.map((m: unknown) => m.clone().seconds(0).valueOf()) as [
                number,
                number
              ])
            : [],
        };
        return;
      }
      sliceState.myExams[myExamType].fields = {
        ...sliceState.myExams[myExamType].fields,
        ...props,
      };
      // if (timeRange !== undefined) sliceState.myExams[myExamType].fields.timeRange = timeRange;
      // if (state !== undefined) sliceState.myExams[myExamType].fields.state = state;
      // if (name !== undefined) sliceState.myExams[myExamType].fields.name = name;
      // if (required !== undefined) sliceState.myExams[myExamType].fields.required = required;
    },

    fetchMyExamsStart(sliceState, { payload: { myExamType } }) {
      sliceState.myExams[myExamType].ids = [];
      sliceState.myExams[myExamType].error = null;
      sliceState.myExams[myExamType].loading = true;
    },

    setMyExams(sliceState, { payload: { myExamType, myExams } }) {
      const examIds: number[] = [];
      myExams.forEach(myExam => {
        examIds.push(myExam.examId);
        // cache update performance optimization
        // api返回的数据中 ownerStatus 更新时，不会更新 gmtModified
        // const existingExam = sliceState.myExams.myExamsEntities[myExam.examId];
        // if (existingExam && existingExam.gmtModified === myExam.gmtModified) {
        //   return;
        // }
        sliceState.myExams.myExamsEntities[myExam.examId] = myExam;
      });
      sliceState.myExams[myExamType].ids = examIds;
      sliceState.myExams[myExamType].loading = false;
    },

    fetchMyExamsError(sliceState, { payload: { myExamType, error } }) {
      sliceState.myExams[myExamType].error = error.message;
      sliceState.myExams[myExamType].loading = false;
    },

    setMyExamsVisibleIds(sliceState, { payload: { myExamType, visibleIds } }) {
      sliceState.myExams[myExamType].visibleIds = visibleIds;
    },

    setInitialUserExamPaperFields(sliceState, { payload }) {
      sliceState.userExamPaper = payload;
    },

    setUserExamPaperFields(
      sliceState,
      {
        payload: {
          displayManuallyMarkingQuestionsOnly,
          displayErrorQuestionsOnly,
          answerQuestion,
          markQuestion,
        },
      }
    ) {
      //仅查看人工判题
      if (displayManuallyMarkingQuestionsOnly !== undefined) {
        sliceState.userExamPaper.displayManuallyMarkingQuestionsOnly =
          displayManuallyMarkingQuestionsOnly;
      }

      // 仅看错题
      if (displayErrorQuestionsOnly !== undefined) {
        sliceState.userExamPaper.displayErrorQuestionsOnly = displayErrorQuestionsOnly;
      }

      // answer question
      if (answerQuestion !== undefined) {
        const questionGroup = sliceState.userExamPaper.questionsGroups.find(
          (group, groupIndex) => groupIndex === answerQuestion.questionGroupIndex
        );
        if (questionGroup !== undefined) {
          questionGroup.questionEntities[answerQuestion.questionId].userAnswers =
            answerQuestion.userAnswers;
          // update exampaper finishedQuestionSize
          computeMutateExamPaper({
            type: 'answer',
            examPaper: sliceState.userExamPaper,
          });
        }
      }

      //mark question
      if (markQuestion !== undefined) {
        const questionGroup = sliceState.userExamPaper.questionsGroups.find(
          (group, groupIndex) => groupIndex === markQuestion.questionGroupIndex
        );
        if (questionGroup !== undefined) {
          const { questionEntities } = questionGroup;
          questionEntities[markQuestion.questionId].userGrade = markQuestion.userGrade!;
          questionEntities[markQuestion.questionId].correct = markQuestion.correct!;

          // update exampaper userTotalScore
          computeMutateExamPaper({
            type: 'mark',
            questionGroup: questionGroup,
            examPaper: sliceState.userExamPaper,
          });
        }
      }
    },

    updateActiveExamStatusTabKey(sliceState, { payload }) {
      sliceState.myExams.activeExamStatusTabKey = payload;
    },

    updateActiveExamTypeTabKey(sliceState, { payload }) {
      sliceState.myExams.activeExamTypeTabKey = payload;
    },

    setAllowMarkingExamSearchValues(sliceState, { payload }) {
      sliceState.exams[ExamTypes.AllowMarkingExam].fields = {
        ...sliceState.exams[ExamTypes.AllowMarkingExam].fields,
        ...payload,
      };
    },
  },
});

/**
 *  答题更新完成数量、判卷时 更新 模块以及试卷的用户得分
 *  @param type
 *  @param questionGroup
 *  @param examPaper
 */
export function computeMutateExamPaper({
  type,
  questionGroup,
  examPaper,
}: {
  type: 'answer' | 'mark';
  questionGroup?: QuestionsGroup;
  examPaper: ExamPaper;
}) {
  if (type === 'answer') {
    let totalFinishedQuestionSize = 0;
    examPaper.questionsGroups.forEach(group => {
      const { questionEntities } = group;
      totalFinishedQuestionSize += Object.keys(questionEntities).reduce((total, questionKey) => {
        return total + (questionEntities[questionKey as unknown].userAnswers?.length !== 0 ? 1 : 0);
      }, 0);
    });
    examPaper.finishedQuestionSize = totalFinishedQuestionSize;
  } else {
    if (questionGroup) {
      const { questionEntities } = questionGroup;
      questionGroup.userScore = Object.keys(questionEntities).reduce((totalScore, questionKey) => {
        return totalScore + (questionEntities[questionKey as unknown].userGrade || 0);
      }, 0);

      examPaper.userScore = examPaper.questionsGroups.reduce((totalScore, group) => {
        return totalScore + group.userScore;
      }, 0);
    }
  }
}

export function getInitialMarkExams(): MarkTypedExams {
  return {
    ids: [],
    fields: {
      page: 1,
      pageSize: 10,
      timeRange: undefined,
      examStatus: undefined,
      examName: undefined,
      categoryCode: undefined,
      orderByModifyTimeDesc: null,
    },
    total: 0,
    error: null,
    loading: false,
  };
}

export function getInitialAllExams(): AllTypedExams {
  const initialMarkExams = getInitialMarkExams();
  return { ...initialMarkExams, selectedIds: [] };
}

export function getInitialCreateExam(): Partial<Omit<Exam, 'id'>> {
  return {
    name: '',
    type: BackendExamType.FORMAL, //BackendExamType
    requiredRoleIds: [],
    requiredUserIds: [],
    markRoleIds: [],
    markUserIds: [],
    optionalRoleIds: [],
    optionalUserIds: [],
    paperId: 0, // todo UpdateService be null
    scopes: [],
    timeRange: [0, 0],
    categoryCode: undefined,
    allowReviewPaperAfterHandedIn: true,
    useSameMasterPaper: true,
    totalGrade: 0,
    passingScore: 0,
    timeLimit: 0,
  };
}

export function getInitialUpdateExam(): Partial<Exam> {
  return {
    ...getInitialCreateExam(),
    id: 0,
  };
}

export function getInitUserExamPaper(): ExamPaper {
  return {
    id: 1, // to update Service
    examId: 1,
    paperId: 1,
    state: BackendExamPaperStatus.INIT,
    startTime: 0,
    endTime: 0,
    timeLimit: 60,
    questionsGroups: [],
    userId: 0,
    userScore: 0,
    totalScore: 0,
    userRanking: 0, // null
    result: false, //null,
    finishedQuestionSize: 0,
    questionSize: 1,
    autoMark: false,
  };
}

export function getInitalMyExams({ type }: { type: 'available' | 'history' }): MyExams {
  return {
    ids: [],
    fields: {
      timeRange:
        type === 'available'
          ? []
          : [moment().startOf('month').valueOf(), moment().endOf('month').valueOf()],
      state: null,
      name: null,
      required: true,
    },
    visibleIds: [],
    loading: false,
    error: null,
  };
}
