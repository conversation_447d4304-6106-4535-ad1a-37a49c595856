---
description: '<PERSON>ams redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import examsSliceReducer from '@manyun/[scope].state.exams';

const rootReducer = {
  ...examsSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { examsWatchers } from '@manyun/[scope].state.exams';

const function* rootSaga() {
  yield all(
    ...examsWatchers,
    // other sagas...
  );
};
```