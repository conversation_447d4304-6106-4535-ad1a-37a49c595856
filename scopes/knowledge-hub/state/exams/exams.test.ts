import { expectSaga } from 'redux-saga-test-plan';

import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type { Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

import { MyExamTypes } from '.';
import { examsSliceActions, getExamsAction } from './exams.action';
import { getExamsSaga } from './exams.saga';
import {
  MutateExamType,
  examsSlice,
  getInitUserExamPaper,
  getInitalMyExams,
  getInitialAllExams,
  getInitialCreateExam,
  getInitialMarkExams,
  getInitialUpdateExam,
} from './exams.slice';
import type { ExamsSliceState } from './exams.slice';

test('should return the initial state', () => {
  expect(examsSlice.reducer(undefined, {} as any)).toEqual<ExamsSliceState>({
    exams: {
      category: {
        loading: false,
        entities: {},
        ids: [],
      },
      examsEntities: {},
      allExam: getInitialAllExams(),
      allowMarkingExam: getInitialMarkExams(),
      create: getInitialCreateExam() as any,
      update: getInitialUpdateExam() as any,
    },
    myExams: {
      activeExamStatusTabKey: MyExamTypes.MyAvailableExams,
      activeExamTypeTabKey: 'required',
      myExamsEntities: {},
      myAvailable: getInitalMyExams({ type: 'available' }),
      myHistory: getInitalMyExams({ type: 'history' }),
    },
    userExamPaper: getInitUserExamPaper(),
  });
});

test('should handle a `name` being updated to create exam', () => {
  const initialState = examsSlice.reducer(undefined, {} as any);

  const exams = examsSlice.reducer(
    initialState,
    examsSlice.actions.setCreateExamFields({
      timeLimit: 1,
      name: '期末考试',
      type: 'TEST' as any,
    })
  ).exams[MutateExamType.CreateExam];
  expect(exams.name).toEqual<string>('期末考试');
});

test('should handle get exam detail  being updated to update exam', () => {
  const initialState = examsSlice.reducer(undefined, {} as any);

  const exam = examsSlice.reducer(
    initialState,
    examsSlice.actions.setUpdateExamFields({
      id: 1,
      name: 'test',
      type: 'FORMAL' as any,
      categoryCode: 1,
      timeRange: [1631768065830, 1631768065830],
      paperId: 1,
      paperName: '测试试卷1',
      passingScore: 60,
      requiredUserIds: [1],
      requiredRoleIds: [1],
      optionalUserIds: [1],
      optionalRoleIds: [1],
      markUserIds: [1],
      markRoleIds: [1],
      scopes: ['EC01.A', 'EC01.B'],
      allowReviewPaperAfterHandedIn: false,
      useSameMasterPaper: false,
      totalGrade: 100,
    })
  ).exams[MutateExamType.UpdateExam] as Exam;

  expect(exam.name).toEqual<string>('test');
});

test('should handle `answer exam paper` being updated to set exam paper fields', () => {
  const initialState = examsSlice.reducer(undefined, {} as any);
  const initExam = examsSlice.reducer(
    initialState,
    examsSlice.actions.setInitialUserExamPaperFields({
      id: 1,
      examId: 1,
      paperId: 1,
      state: BackendExamPaperStatus.GRADED,
      startTime: 1631775056806,
      endTime: 1631775056806,
      timeLimit: 60,
      questionsGroups: [
        {
          id: 'fake-section-id-1',
          name: '第一板块',
          questionSize: 1,
          questionScore: 2,
          userScore: 1,
          questionEntities: {
            1: {
              id: 1,
              questionOrder: 1,
              type: 3,
              categoryCode: '1001',
              difficulty: 1,
              title: '测试题目1',
              options: [
                //选项
                'A:测试选项1',
                'B:测试选项2',
                'C:测试选项3',
                'D:测试选项4',
              ],
              answers: [
                //答案
                'A',
                'B',
                'C',
              ],
              userAnswers: [
                //用户答案
                'A',
                'B',
              ],
              analysis: '这个题目明显选择 A B C',
              autoGrade: false,
              correct: false,
              grade: 2,
              userGrade: 1,
            },
          },
          questionIds: [1],
          visibleQuestionIds: [1],
          visibleErrorQuestionIds: [1],
        },
      ],
      userId: 1,
      userScore: 50,
      totalScore: 100,
      userRanking: 1,
      result: true,
      finishedQuestionSize: 1,
      questionSize: 1,
      autoMark: false,
    })
  );
  const updateExamPaper = examsSlice.reducer(
    initExam,
    examsSlice.actions.setUserExamPaperFields({
      answerQuestion: {
        questionGroupIndex: 0,
        questionId: 1,
        userAnswers: ['A', 'B', 'C'],
      },
    })
  );

  const markExamPaper = examsSlice.reducer(
    initExam,
    examsSlice.actions.setUserExamPaperFields({
      markQuestion: {
        questionGroupIndex: 0,
        questionId: 1,
        correct: true,
        userGrade: 10,
      },
    })
  );
});
