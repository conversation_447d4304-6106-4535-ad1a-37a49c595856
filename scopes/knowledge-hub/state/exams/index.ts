import examsWatchers from './exams.saga';
import { ExamTypes, MutateExamType, MyExamTypes, examsSlice } from './exams.slice';

export type {
  ExamsSliceState,
  SetExamsFieldsActionPayload,
  AllTypedExams,
  ExamFields,
} from './exams.slice';
export type { StaticReadPolicy, CallbackReadPolicy } from './exams.util';
export * from './exams.action';
export * from './exams.selector';
export { examsWatchers, ExamTypes, MyExamTypes, MutateExamType };
export default {
  [examsSlice.name]: examsSlice.reducer,
};
