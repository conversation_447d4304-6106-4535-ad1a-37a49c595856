import React, { useEffect } from 'react';
import { Link } from 'react-router-dom';

import pick from 'lodash.pick';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { usePapers } from '@manyun/knowledge-hub.hook.use-papers';
import { usePapersFields } from '@manyun/knowledge-hub.hook.use-papers-fields';
import { generateNewExamURL } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { BackendPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { PaperGenFunc } from '@manyun/knowledge-hub.ui.paper-gen-func';
import { Papers as PaperTable } from '@manyun/knowledge-hub.ui.papers';
import { PapersBatchUpdator } from '@manyun/knowledge-hub.ui.papers-batch-updator';
import { PapersCategoryCascader } from '@manyun/knowledge-hub.ui.papers-category';
import { PapersDeleter } from '@manyun/knowledge-hub.ui.papers-deleter';
import { PapersMutationPrepare } from '@manyun/knowledge-hub.ui.papers-mutation-prepare';

export type PapersProps = {};

export function Papers() {
  const [setFields, { loading, fields }] = usePapersFields();
  const [form] = Form.useForm();
  const [getPapers] = usePapers();

  useEffect(() => {
    form.setFieldsValue({
      genFun: fields.genFun,
      name: fields.name,
      categoryCodes: fields.categoryCodes,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card bordered={false}>
        <Form
          colon={false}
          layout="inline"
          form={form}
          onValuesChange={changedValues => {
            const fields = pick(changedValues, ['genFun', 'name', 'categoryCodes']);
            setFields(fields);
          }}
        >
          <Form.Item label="组卷方式" name="genFun">
            <PaperGenFunc variant="select" style={{ width: 180 }} allowClear />
          </Form.Item>
          <Form.Item label="试卷名称" name="name">
            <Input placeholder="请输入试卷名称关键字" allowClear />
          </Form.Item>
          <Form.Item label="试卷分类" name="categoryCodes">
            <PapersCategoryCascader style={{ width: '300px' }} allowClear />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                loading={loading}
                onClick={() => {
                  setFields({ page: 1 });
                  getPapers();
                }}
              >
                搜索
              </Button>
              <Button
                disabled={loading}
                onClick={() => {
                  form.resetFields();
                  setFields({
                    page: 1,
                    name: undefined,
                    genFun: undefined,
                    categoryCodes: undefined,
                  });
                  getPapers();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      <Card bordered={false}>
        <Space style={{ width: '100%' }} direction="vertical">
          <Space>
            <PapersMutationPrepare />
            <PapersBatchUpdator />
            <PapersDeleter />
          </Space>
          <PaperTable
            operateColumn={{
              position: 'end',
              column: {
                title: '操作',
                dataIndex: '__actions',
                fixed: 'right',
                width: 200,
                render(_, paper: BackendPaper) {
                  return (
                    <>
                      <PapersMutationPrepare paper={paper} />
                      <Divider type="vertical" />
                      <PapersDeleter paper={paper} />
                      <Divider type="vertical" />
                      <Link to={generateNewExamURL({ paperId: paper.id.toString() })}>
                        创建考试
                      </Link>
                    </>
                  );
                },
              },
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}
