import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  NEW_EXAM_ROUTE_PATH,
  NEW_PAPER_ROUTE_PATH,
  PAPERS_ROUTE_PATH,
  generateEditSpecificPaperRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Papers } from './papers';

const PapersMutatorMock = () => {
  const history = useHistory();
  return (
    <>
      <span>PapersMutator</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </>
  );
};

const ExamMutateMock = () => {
  const history = useHistory();
  return (
    <ConfigProvider>
      <span>创建考试页面</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </ConfigProvider>
  );
};
export const BasicPapers = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // getPapersMock(mock);
    // getPaperMock(mock);
    // deletePapersMock(mock);
    // mutateMock(mock);
    // categoryMock(mock);
    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[PAPERS_ROUTE_PATH]}>
          <Switch>
            <Route path={PAPERS_ROUTE_PATH} exact>
              <Papers />
            </Route>
            <Route path={NEW_PAPER_ROUTE_PATH} exact>
              <PapersMutatorMock />
            </Route>
            <Route path={generateEditSpecificPaperRoutePath(28)} exact>
              <PapersMutatorMock />
            </Route>
            <Route path={NEW_EXAM_ROUTE_PATH} exact>
              <ExamMutateMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
