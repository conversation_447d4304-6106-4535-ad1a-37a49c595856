import React, { useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import {
  generateLearningSpecificCourseRoutePath,
  generateTestPaperRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { TestPaperResultRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchCourseWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { BackendStudyStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { ExamPaperCreator } from '@manyun/knowledge-hub.ui.exam-paper-creator';
import { ExamResult } from '@manyun/knowledge-hub.ui.exam-result';

export type TestPaperResultProps = {};

export function TestPaperResult({}: TestPaperResultProps) {
  const { courseId, examId, id } = useParams<TestPaperResultRouteParams>();
  const history = useHistory();
  const [retestVisible, setRetestVisible] = useState(false);
  const [pass, setPass] = useState(false);

  const getCourse = async (result: any) => {
    const pass = result.pass;
    if (pass) {
      setPass(true);
      return;
    }
    const params = {
      courseId: Number(courseId),
    };
    const { error, data } = await fetchCourseWeb(params);
    if (error) {
      message.error(error.message);
      return;
    }
    const endTime = data?.openingHours?.[1];
    let isTestEnded = false;
    if (endTime) {
      isTestEnded = moment(endTime).isSameOrBefore(moment());
    }
    if (isTestEnded) {
      return;
    }
    if (data?.mustPass) {
      setRetestVisible(true);
    }
  };

  return (
    <Space style={{ width: '100%' }} size="large" direction="vertical">
      <Card>
        <ExamResult
          id={id}
          getResultCb={result => {
            getCourse(result);
          }}
          personalizedDisplay={
            !retestVisible ? (
              <p>本次课程已完成</p>
            ) : pass ? (
              <p>本次课程已完成</p>
            ) : (
              <p>本次课程未完成</p>
            )
          }
        />
      </Card>
      <div style={{ width: '100%', textAlign: 'center' }}>
        <Space>
          {retestVisible && !pass && (
            <ExamPaperCreator
              text="重新测验"
              examId={Number(examId)}
              courseId={Number(courseId)}
              onCompleted={_examPaperId =>
                history.push(
                  generateTestPaperRoutePath({
                    courseId: Number(courseId),
                    examId: Number(examId),
                    id: _examPaperId,
                  })
                )
              }
            />
          )}
          <Button
            onClick={() => {
              if (retestVisible && !pass) {
                history.push(
                  generateLearningSpecificCourseRoutePath(
                    Number(courseId),
                    BackendStudyStatus.UNDONE
                  )
                );
                return;
              }
              history.goBack();
            }}
          >
            返回
          </Button>
        </Space>
      </div>
    </Space>
  );
}
