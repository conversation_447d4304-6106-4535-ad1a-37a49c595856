import React, { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, useHistory, useParams } from 'react-router-dom';
import { Link } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import {
  LEARNING_SPECIFIC_COURSE_ROUTE_PATH,
  TEST_PAPER_RESULT_ROUTE_PATH,
  generateTestPaperResultRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { TestPaperResult } from './test-paper-result';

const url = generateTestPaperResultRoutePath({ id: '69', examId: '69', courseId: '13' });

export const BasicTestPaperResult = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <Router initialEntries={['/']}>
        <Route path={'/'} exact>
          <Link to={url}>可重新测验，测试结果展示与考试结果一致</Link>
        </Route>
        <Route path={TEST_PAPER_RESULT_ROUTE_PATH}>
          <TestPaperResult />
        </Route>
        <Route path={LEARNING_SPECIFIC_COURSE_ROUTE_PATH}>
          <Show />
        </Route>
      </Router>
    </ConfigProvider>
  );
};

function Show() {
  const data = useParams();
  const history = useHistory();
  return (
    <>
      <p>path: {history.location.pathname}</p>
      <p>params: {JSON.stringify(data)}</p>
    </>
  );
}
