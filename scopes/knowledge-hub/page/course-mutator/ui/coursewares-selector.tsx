import type { TableRowSelection } from 'antd/es/table/interface';
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { fetchCoursewaresWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import type { ServiceQ } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import { Courseware, Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CategoryBreadcrumb, getColumns } from '@manyun/knowledge-hub.ui.coursewares';
import { SelectTreeCoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';

export function CoursewaresSelector({
  btnText,
  modelText,
  handleAdd,
  coursewareKeys,
  variant = Variant.ALL,
}: {
  btnText: string;
  modelText: string;
  handleAdd: Function;
  coursewareKeys: string[];
  variant?: Variant;
}) {
  const [visible, setVisiable] = useState(false);
  const [tableLoading, setTableLoading] = useState(false);
  const [coursewares, setCoursewares] = useState([]);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [selectedCoursewares, setSelectedCoursewares] = useState([]);
  const [treeExpandedKeys, setTreeExpandedKeys] = useState(['2']);
  const [fields, setFields] = useState<ServiceQ>({ variant, categoryCode: '2' });
  const [modalForm] = Form.useForm();
  const toggleVisiable = () => setVisiable(!visible);

  const rowSelection: TableRowSelection<Courseware> = {
    selectedRowKeys: selectedKeys,
    onChange: (keys: any[], selectedRows: any) => {
      setSelectedKeys(keys);
      setSelectedCoursewares(selectedRows);
    },
    getCheckboxProps: item => ({
      disabled: item.fileType === 'folder' || coursewareKeys.includes(item.groupKey),
    }),
  };

  const getCoursewares = async (categoryCode: string, name?: string) => {
    setCoursewares([]);
    setTableLoading(true);
    const { data, error } = await fetchCoursewaresWeb({
      variant,
      categoryCode,
      name: name?.trim() || '',
    });
    setTableLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setCoursewares(data!.data as any);
  };

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          toggleVisiable();
          getCoursewares('2');
          setSelectedKeys([]);
          setSelectedCoursewares([]);
          modalForm.resetFields();
          setFields({ variant, categoryCode: '2' });
        }}
      >
        {btnText}
      </Button>
      <Modal
        title={modelText}
        visible={visible}
        width={1024}
        onOk={() => {
          handleAdd(selectedKeys, selectedCoursewares, toggleVisiable);
        }}
        onCancel={toggleVisiable}
        okButtonProps={{ disabled: selectedKeys.length === 0 }}
        okText="确定"
        cancelText="取消"
        destroyOnClose
        bodyStyle={{ height: 'calc(80vh - 55px - 53px)', overflowY: 'auto' }}
      >
        <Form
          colon={false}
          form={modalForm}
          layout="inline"
          onValuesChange={changedValues => {
            setFields({ ...fields, ...changedValues });
          }}
          style={{ marginBottom: 24 }}
          initialValues={{
            categoryCode: 2,
          }}
        >
          <Form.Item label="课件名称" name="name">
            <Input style={{ width: 200 }} placeholder="请输入课件名称关键字" allowClear />
          </Form.Item>
          <Form.Item label="课件分类" name="categoryCode">
            <SelectTreeCoursewareCategory
              style={{ width: 270 }}
              variant={variant}
              treeExpandedKeys={treeExpandedKeys}
              onTreeExpand={(treeExpandedKeys: any) => {
                setTreeExpandedKeys(treeExpandedKeys);
              }}
            />
          </Form.Item>
          <Space>
            <Button
              type="primary"
              onClick={() => {
                modalForm.validateFields().then(values => {
                  getCoursewares(values.categoryCode, values.name);
                });
              }}
              loading={tableLoading}
            >
              搜索
            </Button>
            <Button
              disabled={tableLoading}
              onClick={() => {
                modalForm.resetFields();
                setFields({ variant, categoryCode: '2' });
                getCoursewares('2');
                setTreeExpandedKeys(['2']);
              }}
            >
              重置
            </Button>
          </Space>
        </Form>
        <CategoryBreadcrumb
          variant={variant}
          categoryCode={modalForm.getFieldValue('categoryCode')}
          handleClick={categoryCode => {
            modalForm.setFieldsValue({ categoryCode });
            getCoursewares(categoryCode.toString(), fields.name);
          }}
        />
        <Table
          size="small"
          scroll={{ x: 'max-content' }}
          rowKey="groupKey"
          loading={tableLoading}
          columns={
            getColumns(false, variant, (record: Courseware) => {
              setTreeExpandedKeys([...treeExpandedKeys, record.id.toString()]);
              modalForm.setFieldsValue({ categoryCode: record.id });
              getCoursewares(record.id.toString(), fields.name);
            }) as ColumnsType
          }
          dataSource={coursewares}
          rowSelection={rowSelection}
        />
      </Modal>
    </>
  );
}
