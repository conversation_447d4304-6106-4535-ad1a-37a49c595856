import React from 'react';
import { Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

// import { registerWebMocks as registerFetchKnowledgeHubCategoryMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
// import { registerWebMocks as fetchCoursewaresMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
// import { registerWebMocks as mutateCoursesMocks } from '@manyun/knowledge-hub.service.dcexam.mutate-courses';
// import { registerRoleMocks, registerUserMocks } from '@manyun/auth-hub.ui.complex-users-picker';
// import { registerWebMocks as fetchCourseMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
// import { registerWebMocks as registerFetchPapersMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
// import { registerWebMocks as registerFetchPaperMock } from '@manyun/knowledge-hub.service.dcexam.fetch-paper';
// import { registerWebMocks as registerFetchPapersCategoryMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { Courses } from '@manyun/knowledge-hub.page.courses';
import {
  COURSES_ROUTE_PATH,
  EDIT_SPECIFIC_COURSE_ROUTE_PATH,
  NEW_COURSE_ROUTE_PATH,
  generateEditSpecificCourseRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
// import { getMock } from '@manyun/service.request';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { CourseMutator } from './course-mutator';

export const CreateCourseMutator = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchKnowledgeHubCategoryMocks(mock);
    // fetchCoursewaresMocks(mock);
    // mutateCoursesMocks(mock);
    // registerRoleMocks(mock);
    // registerUserMocks(mock);
    // fetchCourseMocks(mock);
    // registerFetchPapersMocks(mock);
    // registerFetchPaperMock(mock);
    // registerFetchPapersCategoryMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[NEW_COURSE_ROUTE_PATH]}>
          <Switch>
            <Route path={COURSES_ROUTE_PATH} exact>
              <Courses />
            </Route>
            <Route path={NEW_COURSE_ROUTE_PATH} exact>
              <CourseMutator />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

export const UpdateCourseMutator = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchKnowledgeHubCategoryMocks(mock);
    // fetchCoursewaresMocks(mock);
    // mutateCoursesMocks(mock);
    // registerRoleMocks(mock);
    // registerUserMocks(mock);
    // fetchCourseMocks(mock);
    // registerFetchPapersMocks(mock);
    // registerFetchPaperMock(mock);
    // registerFetchPapersCategoryMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateEditSpecificCourseRoutePath(1)]}>
          <Switch>
            <Route path={COURSES_ROUTE_PATH} exact>
              <span>Courses</span>
            </Route>
            <Route path={EDIT_SPECIFIC_COURSE_ROUTE_PATH} exact>
              <CourseMutator />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
