import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  DeleteOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import dayjs from 'dayjs';
import moment from 'moment';
import React, { forwardRef, useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { List } from '@manyun/base-ui.ui.list';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { COURSES_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { CourseMutatorRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import {
  getCourseAction,
  mutateCoursesAction,
  selectCourseCategory,
  selectUpdateCourseValues,
} from '@manyun/knowledge-hub.state.courses';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import type { Courseware } from '@manyun/knowledge-hub.state.coursewares';
import { CoursesCategoryCascader } from '@manyun/knowledge-hub.ui.courses-category';
import { CoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import { TestMutator } from '@manyun/knowledge-hub.ui.exam-mutator';
import { GroupUsersPicker } from '@manyun/knowledge-hub.ui.group-users-picker';
import type { Users } from '@manyun/knowledge-hub.ui.group-users-picker';
import { SchModeType } from '@manyun/ticket.model.task';

import { CoursewaresSelector } from './ui/coursewares-selector';

export type CourseMutatorProps = {};

const timeFormatted = (second: number) => {
  const time = moment.duration(second, 'seconds');
  const hours = time.hours();
  const minutes = time.minutes();
  const seconds = time.seconds();
  return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss');
};

export const CourseMutator = forwardRef(
  // eslint-disable-next-line no-empty-pattern
  ({}: CourseMutatorProps, ref: React.Ref<FormInstance<unknown>>) => {
    const { id } = useParams<CourseMutatorRouteParams>();
    const exsitingCourse = useSelector(selectUpdateCourseValues());
    const [loading, setLoading] = useState(false);
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    const [courseFiles, setCourseFiles] = useState([] as any[]);
    const [coursewareKeys, setCoursewareKeys] = useState([] as string[]);
    const [form] = Form.useForm();
    const dispatch = useDispatch();
    const history = useHistory();
    const { entities } = useSelector(selectCourseCategory);
    const studySla = Form.useWatch('studySla', form);
    const users = Form.useWatch('users', form);
    const openingHours = Form.useWatch('openingHours', form);
    const studySlaMax =
      Array.isArray(openingHours) && openingHours.length === 2
        ? dayjs(openingHours[1]).diff(dayjs(openingHours[0]), 'day') || 1
        : 46;
    const [userData, setUserData] = useState<Users>({
      requiredUsers: {},
      optionalUsers: {},
    });

    const isFinish =
      !id ||
      (Array.isArray(exsitingCourse.openingHours) &&
        (dayjs().diff(dayjs(exsitingCourse.openingHours[1])) > 0 ||
          dayjs().diff(dayjs(exsitingCourse.openingHours[0])) < 0));

    useEffect(() => {
      if (id !== undefined) {
        dispatch(getCourseAction({ id: Number(id) }));
      }
    }, [id, dispatch]);

    useEffect(() => {
      if (id === undefined) {
        return;
      }
      const level2 = entities[Number(exsitingCourse.categoryCode)]?.parentId;
      const level1 = level2 ? entities[level2]?.parentId : undefined;
      form.setFieldsValue({
        courseName: exsitingCourse.name,
        categoryCode: [level1, level2, Number(exsitingCourse.categoryCode)],
        openingHours: exsitingCourse.openingHours && [
          moment(exsitingCourse.openingHours[0]),
          moment(exsitingCourse.openingHours[1]),
        ],
        enabled: exsitingCourse.enabled,
        required: exsitingCourse.paperId !== null,
        paperId: exsitingCourse.paperId,
        mustPass: exsitingCourse.paperId ? exsitingCourse.mustPass : true,
        timeLimit: exsitingCourse.paperId ? exsitingCourse.timeLimit : 120,
        passingScore: exsitingCourse.passingScore ?? 60,
        users: {
          requiredUsers: {
            roleKeys: exsitingCourse.requiredRoleIds,
            userKeys: exsitingCourse.requiredUserIds,
            deptKeys: exsitingCourse.requiredDeptIds,
          },
          optionalUsers: {
            roleKeys: exsitingCourse.optionalRoleIds,
            userKeys: exsitingCourse.optionalUserIds,
            deptKeys: exsitingCourse.optionalDeptIds,
          },
        },
        retakeExamCount: exsitingCourse.retakeExamCount ?? 0,
        failedExamCount: exsitingCourse.failedExamCount ?? 1,
        studySla: exsitingCourse.studySla,
        incompleteTimes: exsitingCourse.incompleteTimes,
        optNotify: exsitingCourse.optNotify ? ['required', 'optional'] : ['required'],
        notice: !!exsitingCourse.failedExamCount,
        trainMode: exsitingCourse.trainMode,
        owner: exsitingCourse.ownerId
          ? { label: exsitingCourse.ownerName, value: exsitingCourse.ownerId }
          : null,
      });
      const files = exsitingCourse.lectureIds.map(groupKey => {
        const lecture = exsitingCourse.lectureEntities[groupKey];
        const checked =
          lecture.minSecondsAsCompleted !== undefined && lecture.minSecondsAsCompleted > 0;
        form.setFieldsValue({ [`${lecture.groupKey}-name`]: lecture.name });
        return {
          ...lecture,
          checked,
          minSecondsAsCompleted: checked ? Number(lecture.minSecondsAsCompleted) / 60 || 1 : 1,
        };
      });
      setCourseFiles(files);
      setCoursewareKeys(exsitingCourse.lectureIds);
      setUserData({
        requiredUsers: {
          roleKeys: exsitingCourse.requiredRoleIds,
          userKeys: exsitingCourse.requiredUserIds,
          deptKeys: exsitingCourse.requiredDeptIds,
        },
        optionalUsers: {
          roleKeys: exsitingCourse.optionalRoleIds,
          userKeys: exsitingCourse.optionalUserIds,
          deptKeys: exsitingCourse.optionalDeptIds,
        },
      });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [exsitingCourse, id]);

    const onHandleCkick = useCallback(() => {
      if (courseFiles.length === 0) {
        message.error('至少添加一条课件！');
        return;
      }

      setLoading(true);
      form
        .validateFields()
        .then(values => {
          const lectureEntities: Record<string, unknown> = {};
          courseFiles.forEach((courseFile, index) => {
            lectureEntities[courseFile.groupKey] = {
              holdOnBlur: courseFile.holdOnBlur,
              allowSeekForward: courseFile.allowSeekForward,
              minSecondsAsCompleted: courseFile.checked
                ? courseFile.minSecondsAsCompleted * 60
                : undefined,
              name: values[`${courseFile.groupKey}-name`].trim(),
              order: index + 1,
              fileId: courseFile.groupKey.split('_')[1],
            };
          });

          const course = {
            id: Number(id),
            name: values.courseName,
            categoryCode: values.categoryCode[values.categoryCode.length - 1],
            openingHours: values.openingHours.map((m: unknown) =>
              m.clone().seconds(0).valueOf()
            ) as [number, number],
            enabled: values.enabled !== undefined ? values.enabled : true,
            lectureIds: coursewareKeys,
            lectureEntities,
            paperId: values.paperId,
            mustPass: values.mustPass,
            passingScore: values.passingScore,
            timeLimit: values.timeLimit,
            requiredUserIds: values.users.requiredUsers?.userKeys,
            requiredRoleIds: values.users.requiredUsers?.roleKeys,
            requiredDeptIds: values.users.requiredUsers?.deptKeys,
            optionalUserIds: values.users.optionalUsers?.userKeys,
            optionalRoleIds: values.users.optionalUsers?.roleKeys,
            optionalDeptIds: values.users.optionalUsers?.deptKeys,
            retakeExamCount: values.retakeExamCount,
            failedExamCount: values.failedExamCount,
            studySla: values.studySla,
            incompleteTimes:
              Array.isArray(values.incompleteTimes) && values.incompleteTimes.length > 0
                ? values.incompleteTimes.filter(Boolean)
                : [],
            optNotify: Array.isArray(values.optNotify) && values.optNotify.length === 2,
            trainMode: values.trainMode,
            ownerId: values.owner.value,
            ownerName: values.owner.label,
          };

          dispatch(
            mutateCoursesAction({
              course,
              callback: result => {
                setLoading(false);
                result && history.push(COURSES_ROUTE_PATH);
              },
            })
          );
        })
        .catch(err => {
          setLoading(false);
          form.scrollToField(err.errorFields[0].name.toString(), {
            behavior: actions => {
              actions.forEach(action => {
                action.el.scrollTop = action.top - 55;
              });
            },
          });
        });
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [courseFiles, dispatch]);

    const onHandleValues = (groupKey: string, key: string, value: unknown) => {
      const idx = courseFiles.findIndex(courseFile => courseFile.groupKey === groupKey);
      const datas = [...courseFiles];
      datas[idx] = { ...datas[idx], [key]: value };
      setCourseFiles(datas);
    };

    const onHandleActions = (groupKey: string, action: string) => {
      const idx = courseFiles.findIndex(courseFile => courseFile.groupKey === groupKey);
      const datas = [...courseFiles];
      if (action === 'delete') {
        datas.splice(idx, 1);
        const index = coursewareKeys.findIndex(courseFilesId => courseFilesId === groupKey);
        const ids = [...coursewareKeys];
        ids.splice(index, 1);
        setCoursewareKeys(ids);
      } else if (action === 'up') {
        if (idx === 0) {
          message.error('已经是第一条！');
          return;
        }
        datas.splice(idx - 1, 0, datas[idx]);
        datas.splice(idx + 1, 1);
      } else if (action === 'down') {
        datas.splice(idx + 2, 0, datas[idx]);
        datas.splice(idx, 1);
        if (idx === courseFiles.length - 1) {
          message.error('已经是最后一条！');
          return;
        }
      }
      setCourseFiles(datas);
    };

    const handleAdd = useCallback(
      (selectedIds, selectedCoursewares, callback) => {
        const datas = selectedCoursewares.map((selectedCourseware: Courseware) => {
          return {
            ...selectedCourseware,
            checked: false,
            allowSeekForward: true,
            holdOnBlur: true,
            minSecondsAsCompleted: selectedCourseware.fileTime
              ? (Math.floor(selectedCourseware.fileTime / 60) || 1).toFixed(0)
              : 1,
          };
        });
        setCourseFiles([...courseFiles, ...datas]);
        setCoursewareKeys([...selectedIds, ...coursewareKeys]);
        callback();
        // eslint-disable-next-line react-hooks/exhaustive-deps
      },
      [courseFiles, coursewareKeys]
    );

    return (
      <Space
        style={{ width: '100%', display: 'flex', marginBottom: '30px' }}
        direction="vertical"
        size="middle"
      >
        <Card title="课程信息">
          <Form
            ref={ref}
            style={{ width: 575 }}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 19 }}
            form={form}
            initialValues={{
              enabled: true,
              users: {
                requiredUsers: { roleKeys: [], userKeys: [] },
                optionalUsers: { roleKeys: [], userKeys: [] },
              } as Users,
              trainMode: SchModeType.Online,
              mustPass: true,
            }}
          >
            <Form.Item
              label="课程名称"
              name="courseName"
              rules={[
                { required: true, whitespace: true, message: '请输入课程名称！' },
                { max: 30, message: '最多输入 30 个字符！' },
              ]}
            >
              <Input style={{ width: 384 }} allowClear />
            </Form.Item>
            <Form.Item
              label="课程分类"
              name="categoryCode"
              rules={[
                {
                  required: true,
                  type: 'boolean',
                  transform: value => {
                    return value?.length === 3 || undefined;
                  },
                  message: '请选择课程分类！',
                },
              ]}
            >
              <CoursesCategoryCascader style={{ width: 384 }} trigger="onDidMount" />
            </Form.Item>
            <Form.Item label="培训方式" name="trainMode" rules={[{ required: true }]}>
              <Radio.Group>
                <Radio value={SchModeType.Online}>在线培训</Radio>
                <Radio value={SchModeType.Offline}>线下培训</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="开放时间"
              name="openingHours"
              rules={[{ required: true, message: '请选择开放时间！' }]}
            >
              <DatePicker.RangePicker
                showTime
                disabled={!isFinish}
                format="YYYY-MM-DD HH:mm"
                style={{ width: 384 }}
                placeholder={['开始时间', '结束时间']}
                renderExtraFooter={() => (
                  <Space>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('openingHours', [moment(), moment().add(7, 'day')])
                      }
                    >
                      一周
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('openingHours', [moment(), moment().add(1, 'month')])
                      }
                    >
                      一月
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('openingHours', [moment(), moment().add(1, 'year')])
                      }
                    >
                      一年
                    </Tag>
                    <Tag
                      color="processing"
                      onClick={() =>
                        form.setFieldValue('openingHours', [moment(), moment().add(10, 'year')])
                      }
                    >
                      十年
                    </Tag>
                  </Space>
                )}
              />
            </Form.Item>
            <Form.Item
              label={
                <>
                  课程状态
                  <Tooltip title="课程状态更改后会重新计算学员的合理学习时效">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </>
              }
              name="enabled"
              initialValue
              rules={[{ required: true }]}
            >
              <Radio.Group>
                <Radio value>正常</Radio>
                <Radio value={false}>禁用</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              label="人员选择"
              name="users"
              rules={[
                {
                  required: true,
                  message: '必修人员或选修人员至少选其一！',
                  type: 'boolean',
                  transform: (value: Users) => {
                    if (
                      !value.optionalUsers?.roleKeys?.length &&
                      !value.optionalUsers?.userKeys?.length &&
                      !value.optionalUsers?.deptKeys?.length &&
                      !value.requiredUsers?.roleKeys?.length &&
                      !value.requiredUsers?.userKeys?.length &&
                      !value.requiredUsers?.deptKeys?.length
                    ) {
                      return undefined;
                    }
                    return true;
                  },
                },
              ]}
            >
              <GroupUsersPicker
                requiredText="必修人员设置"
                optionalText="选修人员设置"
                onReset={() => form.setFieldValue('users', userData)}
                onSubmit={() => setUserData(users)}
              />
            </Form.Item>
            <Form.Item
              label="负责人"
              name="owner"
              rules={[
                {
                  required: true,
                  message: '负责人必填！',
                },
              ]}
            >
              <UserSelect style={{ width: 216 }} allowClear labelInValue />
            </Form.Item>
            <Form.Item
              label={
                <>
                  合理学习时效
                  <Tooltip title="自培训课程开始后学员接受培训学习的合理时长">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </>
              }
            >
              <Space>
                <>
                  <Form.Item style={{ marginBottom: 0 }} name="studySla">
                    <InputNumber
                      min={1}
                      precision={0}
                      max={studySlaMax > 45 ? 45 : studySlaMax}
                      disabled={!isFinish}
                    />
                  </Form.Item>
                  天
                </>
                {'  '}
                {studySla && (
                  <>
                    包括：
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      name="optNotify"
                      initialValue={['required']}
                    >
                      <Checkbox.Group
                        options={[
                          { label: '必修人员', value: 'required', disabled: true },
                          { label: '选修人员', value: 'optional' },
                        ]}
                      />
                    </Form.Item>
                  </>
                )}
              </Space>
            </Form.Item>
            {studySla && (
              <Form.Item>
                <Form.List
                  initialValue={[null]}
                  name="incompleteTimes"
                  rules={[
                    {
                      validator: async (_, options) => {
                        if (!options || options.length < 1) {
                          return Promise.resolve();
                        } else {
                          const newNamesLength = new Set(options).size;

                          if (
                            options.every((option: string) => !!option) &&
                            options.length !== newNamesLength
                          ) {
                            return Promise.reject(new Error('通知时段天数不能重复'));
                          }
                          return Promise.resolve();
                        }
                      },
                    },
                  ]}
                >
                  {(fields, { add, remove }, { errors }) => (
                    <>
                      {fields.map((field, index) => (
                        <Form.Item
                          key={field.key}
                          style={{ marginBottom: 0 }}
                          wrapperCol={{ span: 19, offset: 5 }}
                          required
                        >
                          <Space align="baseline">
                            第
                            <Form.Item name={field.name}>
                              <InputNumber
                                style={{ width: 90 }}
                                min={1}
                                max={studySla}
                                disabled={!isFinish}
                              />
                            </Form.Item>
                            天通知未完成学员
                            {fields.length > 1 && isFinish ? (
                              <DeleteOutlined
                                style={{ marginLeft: 10, marginTop: 9 }}
                                onClick={() => remove(field.name)}
                              />
                            ) : null}
                          </Space>
                        </Form.Item>
                      ))}
                      {fields.length < 45 && isFinish && (
                        <Form.Item wrapperCol={{ span: 18, offset: 6 }}>
                          <Button
                            style={{ width: 224, position: 'relative', left: '3px' }}
                            icon={<PlusOutlined />}
                            onClick={() => {
                              add();
                            }}
                          >
                            添加通知时间
                          </Button>
                          <Form.ErrorList errors={errors} />
                        </Form.Item>
                      )}
                    </>
                  )}
                </Form.List>
              </Form.Item>
            )}
          </Form>
        </Card>
        <Card title="课件信息">
          <CoursewaresSelector
            btnText="添加课件"
            modelText="添加课件"
            handleAdd={handleAdd}
            coursewareKeys={coursewareKeys}
            variant={Variant.Auth}
          />
          {courseFiles.length > 0 && <Divider style={{ margin: '0 0 12px' }} />}
          <Form form={form}>
            <List
              itemLayout="horizontal"
              dataSource={courseFiles}
              rowKey="id"
              renderItem={(
                item: Courseware & {
                  checked: boolean;
                  holdOnBlur: boolean;
                  allowSeekForward: boolean;
                  minSecondsAsCompleted?: number | string;
                }
              ) => (
                <List.Item
                  actions={[
                    <DeleteOutlined
                      key="delete"
                      onClick={() => onHandleActions(item.groupKey, 'delete')}
                    />,
                    <ArrowUpOutlined
                      key="up"
                      onClick={() => onHandleActions(item.groupKey, 'up')}
                    />,
                    <ArrowDownOutlined
                      key="down"
                      onClick={() => onHandleActions(item.groupKey, 'down')}
                    />,
                  ]}
                >
                  <List.Item.Meta
                    title={
                      <Space align="start" style={{ width: '100%' }}>
                        <Tag color={item.fileType === '.MP4' ? 'green' : 'blue'}>
                          {item.fileType === '.MP4' ? '视频类' : '文档类'}
                        </Tag>
                        <Space direction="vertical" style={{ width: 600 }}>
                          <Form.Item
                            label=""
                            name={`${item.groupKey}-name`}
                            rules={[
                              { required: true, whitespace: true, message: '课件别名不能为空！' },
                              { max: 30, message: '课件别名不超过30位！' },
                            ]}
                          >
                            <Input style={{ width: 384 }} placeholder="请输入课件别名" allowClear />
                          </Form.Item>
                          <Descriptions column={1}>
                            <Descriptions.Item label="课件名称">{`${item.fileName}${
                              item.fileType === '.MP4' ? `(${timeFormatted(item.fileTime!)})` : ''
                            }`}</Descriptions.Item>
                            <Descriptions.Item label="课件分类">
                              <CoursewareCategory
                                categoryCode={Number(item.categoryCode)}
                                variant={Variant.ALL}
                              />
                            </Descriptions.Item>
                          </Descriptions>
                        </Space>
                      </Space>
                    }
                    description={
                      <Space>
                        <Checkbox
                          checked={item.checked}
                          onChange={e => {
                            onHandleValues(item.groupKey, 'checked', e.target.checked);
                          }}
                        />
                        <>
                          设定最小学习时间
                          <InputNumber
                            // defaultValue={item.minSecondsAsCompleted || 1}
                            value={item.minSecondsAsCompleted}
                            min={1}
                            max={500}
                            step={1}
                            precision={0}
                            formatter={value => {
                              if (value === undefined) {
                                return '1';
                              }
                              return value.toString();
                            }}
                            parser={displayValue => {
                              if (displayValue === undefined) {
                                return 1;
                              }

                              return Number(displayValue);
                            }}
                            disabled={!item.checked}
                            onChange={value => {
                              onHandleValues(item.groupKey, 'minSecondsAsCompleted', value);
                            }}
                          />
                          分钟
                          {item.fileType === '.MP4' && (
                            <>
                              <Divider type="vertical" />
                              离开页面停止播放:
                              <Switch
                                style={{ marginLeft: 20 }}
                                checked={item.holdOnBlur}
                                onChange={checked => {
                                  onHandleValues(item.groupKey, 'holdOnBlur', checked);
                                }}
                              />
                              <Divider type="vertical" />
                              允许视频倍速播放:
                              <Switch
                                style={{ marginLeft: 20 }}
                                checked={item.allowSeekForward}
                                onChange={checked => {
                                  onHandleValues(item.groupKey, 'allowSeekForward', checked);
                                }}
                              />
                            </>
                          )}
                        </>
                      </Space>
                    }
                  />
                </List.Item>
              )}
            />
          </Form>
        </Card>
        <TestMutator form={form} />
        <FooterToolBar>
          <Button
            type="primary"
            style={{ marginRight: 24 }}
            loading={loading}
            onClick={() => {
              onHandleCkick();
            }}
          >
            {id ? '保存' : '提交'}
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </FooterToolBar>
      </Space>
    );
  }
);

CourseMutator.displayName = 'CourseMutator';
