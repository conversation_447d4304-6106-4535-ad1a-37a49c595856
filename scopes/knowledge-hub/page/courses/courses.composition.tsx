import React from 'react';
import { Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { CourseMutator } from '@manyun/knowledge-hub.page.course-mutator';
// import { registerWebMocks as registerFetchKnowledgeHubCategoryMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
// import { registerWebMocks as registerFetchCoursesWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
// import { registerWebMocks as registerDeleteCoursesWebMocks } from '@manyun/knowledge-hub.service.dcexam.delete-courses';
// import { registerWebMocks as registerMutateCoursesWebMocks } from '@manyun/knowledge-hub.service.dcexam.mutate-courses';
import {
  COURSES_ROUTE_PATH,
  EDIT_SPECIFIC_COURSE_ROUTE_PATH,
  NEW_COURSE_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
// import { getMock } from '@manyun/service.request';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Courses } from './courses';

export const BasicCourses = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchKnowledgeHubCategoryMocks(mock);
    // registerFetchCoursesWebMocks(mock);
    // registerDeleteCoursesWebMocks(mock);
    // registerMutateCoursesWebMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[COURSES_ROUTE_PATH]}>
          <Switch>
            <Route path={COURSES_ROUTE_PATH} exact>
              <Courses />
            </Route>
            <Route path={NEW_COURSE_ROUTE_PATH} exact>
              <span>create</span>
              {/* <CourseMutator /> */}
            </Route>
            <Route path={EDIT_SPECIFIC_COURSE_ROUTE_PATH} exact>
              <span>edit</span>
              {/* <CourseMutator /> */}
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
