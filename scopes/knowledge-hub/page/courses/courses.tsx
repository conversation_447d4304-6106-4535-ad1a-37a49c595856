import React from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { NEW_COURSE_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { AllCourses as AllCoursesTable } from '@manyun/knowledge-hub.ui.courses';
import { CoursesBatchUpdator } from '@manyun/knowledge-hub.ui.courses-batch-updator';
import { CoursesDeleter } from '@manyun/knowledge-hub.ui.courses-deleter';
import { AllCoursesFilters } from '@manyun/knowledge-hub.ui.courses-filters';

export function Courses() {
  const history = useHistory();

  return (
    <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
      <Card bordered={false}>
        <AllCoursesFilters />
      </Card>
      <Card bordered={false}>
        <Space style={{ width: '100%' }} direction="vertical">
          <Space>
            <Button
              key="link"
              type="primary"
              onClick={() => {
                history.push(NEW_COURSE_ROUTE_PATH);
              }}
            >
              创建课程
            </Button>
            <CoursesBatchUpdator key="update" />
            <CoursesDeleter key="delete" />
          </Space>
          <AllCoursesTable />
        </Space>
      </Card>
    </Space>
  );
}
