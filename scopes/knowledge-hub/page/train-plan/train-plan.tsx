import moment from 'moment';
import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { Breadcrumb as LayoutBreadcrumb } from '@manyun/dc-brain.context.layout';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import type { SchProperties } from '@manyun/knowledge-hub.page.train-plan-mutator';
import { TRAIN_PLANS_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { generatePaperPreviewRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchCourseUsersPicker } from '@manyun/knowledge-hub.service.dcexam.fetch-course-users-picker';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CoursesCategory } from '@manyun/knowledge-hub.ui.courses-category';
import { CoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import { FiltersCoursesModal } from '@manyun/knowledge-hub.ui.filters-courses-modal';
import type { Cycle, MonthlyWorkDayType, PlanJSON, WeekType } from '@manyun/ticket.model.task';
import {
  CyclesType,
  DAY_WORK_WEEK_TEXT_MAP,
  PLAN_STATUS_TEXT_MAP,
  Plan,
  SCH_MODE_TYPE_TEXT,
  WEEK_DAY_TEXT_MAP,
} from '@manyun/ticket.model.task';
import { fetchPlanDetail } from '@manyun/ticket.service.fetch-plan-detail';

export type TrainPlanProps = {
  /**
   * sets the component children.
   */
};
type UserConfig = { users: number[]; roles: number[]; departments: number[] };

export function TrainPlan() {
  const { id } = useParams<{ id: string }>();
  const [detailInfo, setDetailInfo] = useState<PlanJSON>();
  const [schProperties, setSchProperties] = useState<
    SchProperties & {
      defaultUserConfig: UserConfig;
      optionalUserConfig: UserConfig;
    }
  >();
  const history = useHistory();
  const [allUsers, setAllUsers] = useState<Record<string, any>[]>([]);

  useEffect(() => {
    (async function () {
      const { data, error } = await fetchPlanDetail({ id: id || '' });

      if (error) {
        message.error(error.message);
        return;
      }
      setDetailInfo(data as PlanJSON);
      if (data?.schProperties) {
        const schPropertiesVal = JSON.parse(data.schProperties);
        setSchProperties(schPropertiesVal);

        const requiredUser = schPropertiesVal?.defaultUserConfig as Record<string, any>;
        const optionalUser = schPropertiesVal?.optionalUserConfig as Record<string, any>;

        const { data: requiredUserData } = await fetchCourseUsersPicker(requiredUser);
        const { data: optionalUserData } = await fetchCourseUsersPicker(optionalUser);

        const requiredAll = [
          ...(requiredUserData?.departments || []),
          ...(requiredUserData?.users || []),
          ...(requiredUserData?.roles || []),
        ].map(item => ({
          ...item,
          courseType: '必修',
        }));

        const optionalAll = [
          ...(optionalUserData?.departments || []),
          ...(optionalUserData?.users || []),
          ...(optionalUserData?.roles || []),
        ].map(item => ({
          ...item,
          courseType: '选修',
        }));

        const allUser = [...requiredAll, ...optionalAll];

        setAllUsers(allUser);
      }
    })();
  }, [id]);

  return (
    <LayoutContent
      pageCode="page_knowledge-hub_train-plan-detail"
      composeBreadcrumbs={(value: LayoutBreadcrumb[]) => [
        ...value,
        { key: 'train-plan-mutator', text: detailInfo?.name ?? id },
      ]}
    >
      <Space
        style={{ width: '100%', display: 'flex', marginBottom: '30px' }}
        direction="vertical"
        size="middle"
      >
        <Card bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              基本配置
            </Typography.Title>
            <Descriptions column={4}>
              <Descriptions.Item label="计划名称">{detailInfo?.name}</Descriptions.Item>
              <Descriptions.Item label="计划范围">
                {detailInfo?.blockScope &&
                  detailInfo.blockScope.map(item => item.blockGuid).join(' | ')}
              </Descriptions.Item>
              <Descriptions.Item label="课程分类">
                {schProperties?.categoryCode && (
                  <CoursesCategory categoryCode={Number(schProperties.categoryCode)} />
                )}
              </Descriptions.Item>
              <Descriptions.Item label="培训方式">
                {detailInfo?.schMode ? SCH_MODE_TYPE_TEXT[detailInfo.schMode] : '--'}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {detailInfo?.isActivated ? PLAN_STATUS_TEXT_MAP[detailInfo.isActivated] : '--'}
              </Descriptions.Item>

              <Descriptions.Item label="计划排期">
                {detailInfo?.repeatCycle && getCycsTxt(detailInfo.repeatCycle)}
              </Descriptions.Item>
              <Descriptions.Item label="结束时间">
                {detailInfo && Plan.fromJSON(detailInfo).getFormattedEndTime()}
              </Descriptions.Item>
              <Descriptions.Item label="触发时间">
                {detailInfo?.allowTriggerTime &&
                  Array.isArray(detailInfo.allowTriggerTime) &&
                  detailInfo.allowTriggerTime.join('、')}
              </Descriptions.Item>
              <Descriptions.Item label="上次运行时间">
                {detailInfo && Plan.fromJSON(detailInfo).getFormattedLastExecuteAt()}
              </Descriptions.Item>

              <Descriptions.Item label="上次运行结果">
                {detailInfo?.lastExecuteResult?.length ? (
                  <FiltersCoursesModal
                    btnText={detailInfo.lastExecuteResult.length}
                    courseIdList={detailInfo.lastExecuteResult.map(item => Number(item))}
                  />
                ) : (
                  0
                )}
              </Descriptions.Item>
              <Descriptions.Item label="开放时间">
                {schProperties &&
                  `${schProperties.openTime}${schProperties.openUnit === 'DAY' ? '天' : '小时'}`}
              </Descriptions.Item>
              <Descriptions.Item label="负责人">
                {detailInfo?.owner && (
                  <UserLink userId={detailInfo.owner.id!} userName={detailInfo.owner.name!} />
                )}
              </Descriptions.Item>
            </Descriptions>
          </Space>
        </Card>
        <Card bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              培训人员
            </Typography.Title>
            <Row gutter={[16, 16]}>
              {allUsers.map((item: Record<string, any>, i) => (
                <Col key={i} span={4}>
                  <Card>
                    <Space style={{ width: '100%' }} direction="vertical">
                      <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                        {item?.userName || item?.roleName || item?.departmentName}
                      </Typography.Title>
                      <Space>
                        类别 <Tag color="success">{item.courseType}</Tag>
                      </Space>
                    </Space>
                  </Card>
                </Col>
              ))}
            </Row>
          </Space>
        </Card>
        <Card bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              课件配置
            </Typography.Title>
            {schProperties?.courseFileList &&
              Array.isArray(schProperties?.courseFileList) &&
              schProperties.courseFileList.map(courseFile => (
                <Card key={courseFile.fileId} style={{ paddingBottom: 0 }}>
                  <Descriptions column={2}>
                    <Descriptions.Item label="课件别名">
                      <Space>
                        {courseFile.name}
                        <Tag color={courseFile.fileType === '.MP4' ? 'green' : 'blue'}>
                          {courseFile.fileType === '.MP4' ? '视频类' : '文档类'}
                        </Tag>
                      </Space>
                    </Descriptions.Item>
                    <Descriptions.Item label="课件名称">{`${courseFile.fileName}${
                      courseFile.fileType === '.MP4'
                        ? `(${timeFormatted(courseFile.fileTime!)})`
                        : ''
                    }`}</Descriptions.Item>
                    <Descriptions.Item label="课件分类">
                      <CoursewareCategory
                        categoryCode={Number(courseFile.categoryCode)}
                        variant={Variant.ALL}
                      />
                    </Descriptions.Item>
                    <Descriptions.Item label="最小学习时间">
                      {courseFile.minTime ? `${courseFile.minTime / 60}分钟` : '--'}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              ))}
          </Space>
        </Card>
        <Card bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              随堂测试
            </Typography.Title>
            {schProperties?.examConfig && (
              <Descriptions column={4}>
                <Descriptions.Item label="需要随堂测试">
                  {schProperties.examConfig.paperId ? '是' : '否'}
                </Descriptions.Item>
                {schProperties.examConfig.paperId && (
                  <>
                    <Descriptions.Item label="必须测验通过">
                      {schProperties.examConfig.isPass ? '是' : '否'}
                    </Descriptions.Item>
                    <Descriptions.Item label="是否通知上级主管">
                      {schProperties.examConfig.failedExamCount
                        ? `未通过${schProperties.examConfig.failedExamCount}次通知`
                        : '否'}
                    </Descriptions.Item>
                    <Descriptions.Item label="考试试卷">
                      {schProperties?.examConfig?.paperName ||
                      schProperties?.examConfig?.paperId ? (
                        <a
                          href={generatePaperPreviewRoutePath({
                            id: schProperties?.examConfig?.paperId.toString(),
                          })}
                          target="_blank"
                          rel="noreferrer"
                        >
                          {schProperties.examConfig.paperName || schProperties?.examConfig?.paperId}
                        </a>
                      ) : (
                        '--'
                      )}
                    </Descriptions.Item>
                    <Descriptions.Item label="考试时长">{`${schProperties.examConfig.answerTime}分钟`}</Descriptions.Item>
                    <Descriptions.Item label="及格分数">
                      {`${schProperties.examConfig.passGrade}分`}
                    </Descriptions.Item>
                  </>
                )}
              </Descriptions>
            )}
          </Space>
        </Card>
        <FooterToolBar>
          <Button
            onClick={() => {
              history.push(TRAIN_PLANS_ROUTE_PATH);
            }}
          >
            返回
          </Button>
        </FooterToolBar>
      </Space>
    </LayoutContent>
  );
}

function getCycsTxt(cycles: Cycle) {
  if (!cycles) {
    return;
  }
  if (cycles.periodUnit === CyclesType.Year) {
    let txt = '';
    // 每逢
    if (cycles.dayConfig) {
      const days = cycles.dayConfig?.days?.map(item => {
        return item.substr(0, 2) + '月' + item.substr(2, 3) + '日';
      });
      txt = `每${cycles.period}年的${days?.join('、')}${getSchedulePostText(
        cycles.dayConfig.isRemove
      )}`;
    }
    // 在
    if (cycles.atDayConfig) {
      const days = cycles.atDayConfig.map(({ month }) => month + '月');
      txt = `每${cycles.period}年的${days.join('、')}的第${cycles.atDayConfig[0].sortNum}个${
        DAY_WORK_WEEK_TEXT_MAP[cycles.atDayConfig[0].atDayType as MonthlyWorkDayType]
      }`;
    }

    return txt;
  }
  if (cycles.periodUnit === CyclesType.Month) {
    let txt = '';

    // 每逢
    if (cycles.dayConfig) {
      txt = `每${cycles.period}月的${cycles.dayConfig.days?.join('、')}${getSchedulePostText(
        cycles.dayConfig.isRemove
      )}`;
    }

    // 在
    if (cycles.atDayConfig) {
      txt = `每${cycles.period}月的第${cycles.atDayConfig[0].sortNum}个${
        DAY_WORK_WEEK_TEXT_MAP[cycles.atDayConfig[0].atDayType as MonthlyWorkDayType]
      }`;
    }
    return txt;
  }
  if (cycles.periodUnit === CyclesType.Week) {
    let txt = '';
    const days = cycles.dayConfig.days?.map(item => WEEK_DAY_TEXT_MAP[item as WeekType]);
    txt = `每${cycles.period}周的${days?.join('、')}${getSchedulePostText(
      cycles.dayConfig.isRemove
    )}`;
    return txt;
  }
  if (cycles.periodUnit === CyclesType.Day) {
    const txt = `每${cycles.period}天${getSchedulePostText(cycles.dayConfig.isRemove)}`;
    return txt;
  }
  if (cycles.periodUnit === CyclesType.None) {
    return '无';
  }
  return;
}

function getSchedulePostText(isRemove: number) {
  if (isRemove === 0) {
    return '';
  } else if (isRemove === 1) {
    return '（如遇节假日，则自动取消当日排期）';
  } else {
    return '（如遇节假日，则自动顺延至下一个工作日）';
  }
}

const timeFormatted = (second: number) => {
  const time = moment.duration(second, 'seconds');
  const hours = time.hours();
  const minutes = time.minutes();
  const seconds = time.seconds();
  return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss');
};
