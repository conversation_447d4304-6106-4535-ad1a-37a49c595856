import React, { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  QUESTIONS_CREATOR_ROUTE_PATH,
  QUESTIONS_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { QuestionsPage } from './questions';

const QuestionCreateMock = () => {
  const history = useHistory();
  return (
    <>
      <span>QuestionCreate</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </>
  );
};
export const BasicQuestions = () => {
  const [initial, update] = useState(false);
  useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // fetchQuestionsMock(mock);
    // fetchQuestionMock(mock);
    // deleteMock(mock);
    // mutateMock(mock);
    // categoryMock(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initial) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[QUESTIONS_ROUTE_PATH]}>
          <Switch>
            <Route path={QUESTIONS_ROUTE_PATH} exact>
              <QuestionsPage />
            </Route>
            <Route path={QUESTIONS_CREATOR_ROUTE_PATH} exact>
              <QuestionCreateMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
