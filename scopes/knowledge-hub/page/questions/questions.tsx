import React, { useEffect, useRef } from 'react';
import { useHistory } from 'react-router-dom';

import pick from 'lodash.pick';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';

import { useQuestions } from '@manyun/knowledge-hub.hook.use-questions';
import { useQuestionsCategory } from '@manyun/knowledge-hub.hook.use-questions-category';
import { useQuestionsFields } from '@manyun/knowledge-hub.hook.use-questions-fields';
import { QUESTIONS_CREATOR_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { SelectQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { QuestionUpdator } from '@manyun/knowledge-hub.ui.question-updator';
import { Questions } from '@manyun/knowledge-hub.ui.questions';
import {
  EditTreeQuestionsCategroy,
  EditTreeRef,
} from '@manyun/knowledge-hub.ui.questions-category';
import { QuestionsDeleter } from '@manyun/knowledge-hub.ui.questions-deleter';
import { SelectQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';
import { QustionsBatchUpdator } from '@manyun/knowledge-hub.ui.qustions-batch-updator';

import styles from './questions.module.less';

export type QuestionsProps = {};

export function QuestionsPage() {
  const [setFields] = useQuestionsFields();
  const [{ entities }, { readQuestionCategory, setCategoriesExpandedKeys }] =
    useQuestionsCategory();
  const [getQuestions, { loading, fields }] = useQuestions();
  const [form] = Form.useForm();
  const history = useHistory();
  const treeRef = useRef<EditTreeRef>(null);

  useEffect(() => {
    form.setFieldsValue({
      difficulty: fields.difficulty,
      type: fields.type,
      title: fields.title,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /***edittree 选中值 发生变化时 更新table */
  useEffect(() => {
    if (fields.categoryCode !== undefined) {
      setFields({ page: 1 });
      getQuestions();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fields.categoryCode]);

  return (
    <Space className={styles.questionsContainer} align={'start'} direction="horizontal">
      <Card bordered={false} title="试题分类">
        <EditTreeQuestionsCategroy ref={treeRef} />
      </Card>
      <Card bordered={false} bodyStyle={{ height: '100%', paddingBottom: '10px' }}>
        <Space direction="vertical" style={{ width: '100%' }} size="middle">
          <Form
            layout="inline"
            form={form}
            colon={false}
            onValuesChange={changedValues => {
              const fields = pick(changedValues, ['difficulty', 'type', 'title']);
              setFields(fields);
            }}
          >
            <Form.Item label="难度" name="difficulty">
              <SelectQuestionsDifficulty style={{ width: '180px' }} allowClear />
            </Form.Item>
            <Form.Item label="题型" name="type">
              <SelectQuestionType style={{ width: '180px' }} allowClear />
            </Form.Item>
            <Form.Item label="试题内容" name="title">
              <Input placeholder="请输入试题关键字" allowClear />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    setFields({ page: 1 });
                    getQuestions();
                  }}
                >
                  搜索
                </Button>
                <Button
                  disabled={loading}
                  onClick={() => {
                    form.resetFields();
                    setFields({
                      page: 1,
                      difficulty: undefined,
                      type: undefined,
                      title: undefined,
                    });
                    getQuestions();
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <Space direction="horizontal" style={{ width: '100%' }}>
            <Button
              type="primary"
              onClick={() => {
                history.push(QUESTIONS_CREATOR_ROUTE_PATH);
              }}
            >
              创建试题
            </Button>
            <QustionsBatchUpdator
              onComplteUpdate={res => {
                if (res) {
                  readQuestionCategory(true);
                }
              }}
            />
            <QuestionsDeleter
              onComplteDelete={(res, deleteIds) => {
                if (
                  fields.categoryCode !== undefined &&
                  deleteIds?.includes(`${fields.categoryCode}_0`)
                ) {
                  Object.keys(entities).forEach(id => {
                    const entity = entities[Number(id)];
                    if (entity.parentId === 0) {
                      setFields({ categoryCode: entity.code });
                    }
                  });
                }
                if (res) {
                  readQuestionCategory(true);
                  setFields({ page: 1 });
                  getQuestions();
                }
              }}
            />
          </Space>
          <Questions
            operateColumn={{
              column: {
                title: '操作',
                dataIndex: '__action',
                width: 130,
                fixed: 'right',
                render: (_, question) => {
                  if (question.questionType === '1') {
                    return (
                      <>
                        <QuestionUpdator
                          question={question}
                          onOk={() => {
                            getQuestions();
                            readQuestionCategory(true);
                          }}
                        />
                        <Divider type="vertical" />
                        <QuestionsDeleter
                          question={question}
                          onComplteDelete={res => {
                            if (res) {
                              /**单个删除 内部已经重新刷新了table  刷新 category */
                              readQuestionCategory(true);
                            }
                          }}
                        />
                      </>
                    );
                  } else {
                    return '--';
                  }
                },
              },
            }}
            onRowClick={(record: BackendQuestion) => {
              if (record.questionType === '0') {
                const categoryCode = record.id;
                if (fields.categoryCode === categoryCode) {
                  return;
                }
                setFields({ categoryCode: categoryCode });
                setCategoriesExpandedKeys(record.categoryCode.toString());
                getQuestions();
              }
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}
