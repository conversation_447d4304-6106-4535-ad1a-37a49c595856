import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { Coursewares as CoursewaresTable } from '@manyun/knowledge-hub.ui.coursewares';
import { TreeCoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import { CoursewaresFilters } from '@manyun/knowledge-hub.ui.coursewares-filters';

import styles from './wikis.module.less';

export function Wikis() {
  const [, { categoryCode }] = useCoursewaresFields(Variant.Auth);

  return (
    <div className={styles.container}>
      <Space className={styles.wikisContainer} align="start" size="middle">
        <Card bordered={false} title="课件分类">
          <TreeCoursewareCategory />
        </Card>
        <Card bordered={false}>
          <Space direction="vertical" size="middle" style={{ width: '100%' }}>
            <CoursewaresFilters variant={Variant.Auth} />
            <CoursewaresTable variant={Variant.Auth} categoryCode={categoryCode} />
          </Space>
        </Card>
      </Space>
    </div>
  );
}
