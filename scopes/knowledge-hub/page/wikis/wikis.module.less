.container {
  width: 100%;
  height: 100%;

  > * + * {
    margin-top: 8px;
  }

  .wikisContainer {
    width: 100%;
    height: 100%;
    display: flex;

    > :global(.manyun-space-item) {
      height: 100%;

      &:last-child {
        flex: 1;
      }

      &:first-child {
        width: 380px;
      }

      > * {
        height: 100%;

        > :global(.manyun-card-body) {
          height: calc(100% - /* Card Head height */ 58px);
          overflow-y: auto;
        }
      }
    }
  }
}
