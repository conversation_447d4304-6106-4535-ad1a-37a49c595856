import React from 'react';
import { Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { WIKIS_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Wikis } from './wikis';

export const BasicCoursewares = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[WIKIS_ROUTE_PATH]}>
          <Switch>
            <Route path={WIKIS_ROUTE_PATH} exact>
              <Wikis />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
