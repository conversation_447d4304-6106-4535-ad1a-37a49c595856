.container {
  width: 100%;
  height: 100%;

  > * + * {
    margin-top: 8px;
  }

  .courseLearningContainer {
    width: 100%;
    height: 100%;
    display: flex;

    > :global(.manyun-space-item) {
      height: 100%;

      &:first-child {
        flex: 1;
        width: 0;
      }

      &:last-child {
        width: 325px;
      }

      > * {
        height: 100%;

        > :global(.manyun-card-body) {
          height: calc(100% - /* Card Head height */ 58px);
          overflow-y: auto;
        }
      }
    }
  }
}

.view {
  display: flex;
  justify-content: center;
  height: calc(80vh);
  width: 100%;
  border: 6px;
}
.pageContainer {
  // width: max-content;
  max-width: 100%;
  max-height: fit-content;
}
.pageTool {
  position: absolute;
  bottom: 20px;
  padding: 8px 15px;
  display: flex;
  justify-content: space-between;

  > * + * {
    margin-right: 8px;
  }
}

.videoPlayer {
  :global {
    button.vjs-big-play-button {
      top: 50%;
      left: 50%;
      margin-top: -0.81666em;
      margin-left: -1.5em;
    }
  }
}