import React from 'react';
import { Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  LEARNING_SPECIFIC_COURSE_ROUTE_PATH,
  generateLearningSpecificCourseRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { registerWebMocks as registerFetchCourseLearningMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import { registerWebMocks as registerFetchExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
// import { getMock } from '@manyun/service.request';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { CourseLearning } from './course-learning';

export const BasicCourseLearning = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchCourseLearningMocks(mock);
    // registerFetchExamPaper(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateLearningSpecificCourseRoutePath(1, 'UNDONE')]}>
          <Switch>
            <Route path={LEARNING_SPECIFIC_COURSE_ROUTE_PATH} exact>
              <CourseLearning />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
