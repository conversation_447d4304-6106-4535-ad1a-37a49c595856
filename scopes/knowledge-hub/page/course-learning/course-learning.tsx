import { HomeOutlined, MinusCircleOutlined, PlusCircleOutlined } from '@ant-design/icons';
import moment from 'moment';
// @ts-ignore
import pdfjsWorker from 'pdfjs-dist/build/pdf.worker.entry';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Document, Page, pdfjs } from 'react-pdf';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';
import { useLatest } from 'react-use';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Pagination } from '@manyun/base-ui.ui.pagination';
import { Progress } from '@manyun/base-ui.ui.progress';
import { Radio } from '@manyun/base-ui.ui.radio';
import type { RadioProps } from '@manyun/base-ui.ui.radio';
import { Result } from '@manyun/base-ui.ui.result';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { VideoPlayer } from '@manyun/base-ui.ui.video-player';
import type { Player, PlayerOptions } from '@manyun/base-ui.ui.video-player';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { generateTestPaperRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { CourseLearningRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { Lecture } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import { fetchExamPaperWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type { ExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import {
  coursesSliceActions,
  getCourseLearningAction,
  selectCourseLearning,
} from '@manyun/knowledge-hub.state.courses';
import type { Learning } from '@manyun/knowledge-hub.state.courses';
import { ExamPaperCreator } from '@manyun/knowledge-hub.ui.exam-paper-creator';
import { webRequest } from '@manyun/service.request';

import styles from './course-learning.module.less';

pdfjs.GlobalWorkerOptions.workerSrc = pdfjsWorker;

export function CourseLearning() {
  const courseLearning = useSelector(selectCourseLearning()) as Learning;
  const { id } = useParams<CourseLearningRouteParams>();
  const [finished, setFinished] = useState(true);
  const [mask, setMask] = useState(true);
  const [loading, setLoading] = useState(true);
  const [selectedFile, setSelectedFile]: any = useState({});
  const [selectedFileId, setSelectedFileId]: any = useState(0);
  const [examPaper, setExamPaper] = React.useState<ExamPaper | null>(null);
  const history = useHistory();
  const dispatch = useDispatch();

  const lectureEntitiesValues = Object.values(courseLearning?.lectureEntities).map(
    item => item.isFinished
  );
  const isFinished = lectureEntitiesValues.every(item => item);

  useEffect(() => {
    if (id !== undefined) {
      dispatch(
        getCourseLearningAction({
          id: Number(id),
          callback: result => {
            setLoading(false);
          },
        })
      );
    }
    return () => {
      dispatch(coursesSliceActions.resetLearningCourse());
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  useEffect(() => {
    const examPaperId = courseLearning.test.paperId;
    if (!examPaperId) {
      return;
    }
    fetchExamPaperWeb({ examPaperId: examPaperId }).then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      setExamPaper(data);
    });
  }, [courseLearning]);

  useEffect(() => {
    let completed = true;
    if (courseLearning.lectureIds.length) {
      setSelectedFileId(selectedFileId || courseLearning.lectureIds[0]);
      setSelectedFile(
        selectedFileId
          ? courseLearning.lectureEntities[selectedFileId]
          : courseLearning.lectureEntities[courseLearning.lectureIds[0]]
      );
      courseLearning.lectureIds.forEach(id => {
        if (!courseLearning.lectureEntities[id].isFinished) {
          completed = false;
        }
      });
      if (courseLearning.isFinished) {
        return;
      }

      if (completed && finished) {
        setFinished(false);
        dispatch(
          getCourseLearningAction({
            id: Number(id),
          })
        );
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, courseLearning]);

  if (loading) {
    return <Spin spinning />;
  }

  if (courseLearning.error && courseLearning.error.code === 'UN_AUTH_LEARN') {
    return <Result status="403" title="403" subTitle="抱歉，你无权查看此课程！" />;
  }

  if (!courseLearning.id) {
    return <Result status="404" title="404" subTitle="抱歉，此课程学习不存在！" />;
  }

  return (
    <LayoutContent
      pageCode="page_knowledge-hub_course-learning"
      composeBreadcrumbs={(value: any[]) => [...value, { key: 'name', text: courseLearning.name }]}
    >
      <div className={styles.container}>
        <Space className={styles.courseLearningContainer} align="start" size="middle">
          <Card
            bordered={false}
            bodyStyle={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              alignContent: 'center',
              flexWrap: 'wrap',
            }}
          >
            {mask && (
              <div
                style={{
                  width: 338,
                  minHeight: 400,
                  display: 'flex',
                  flexDirection: 'column',
                }}
              >
                <span style={{ fontSize: 26, textAlign: 'center' }}>{selectedFile.name}</span>
                <Descriptions
                  column={1}
                  style={{ fontSize: 12, paddingTop: 80, paddingBottom: 80 }}
                >
                  <Descriptions.Item label="开始时间">
                    {courseLearning.openingHours &&
                      moment(courseLearning.openingHours[0]).format('YYYY-MM-DD HH:mm')}
                  </Descriptions.Item>
                  <Descriptions.Item label="结束时间">
                    {courseLearning.openingHours &&
                      moment(courseLearning.openingHours[1]).format('YYYY-MM-DD HH:mm')}
                  </Descriptions.Item>
                </Descriptions>
                <div style={{ display: 'flex', justifyContent: 'center' }}>
                  <Button
                    type="primary"
                    onClick={() => {
                      setMask(false);
                    }}
                  >
                    {selectedFile.studyTime ? '继续学习' : '开始学习'}
                  </Button>
                </div>
              </div>
            )}
            {!mask &&
              (selectedFile && selectedFile.fileType === '.PDF' ? (
                <PDF id={Number(id)} file={selectedFile} />
              ) : (
                <Video id={Number(id)} file={selectedFile} />
              ))}
          </Card>
          <Card
            bodyStyle={{
              padding: 0,
            }}
            bordered={false}
          >
            <Card
              title="课程目录"
              headStyle={{ borderBottom: 0 }}
              bodyStyle={{ padding: '16px 24px' }}
              bordered={false}
            >
              <Radio.Group
                value={selectedFileId}
                onChange={e => {
                  setSelectedFileId(e.target.value);
                  setSelectedFile(courseLearning.lectureEntities[e.target.value]);
                  setMask(true);
                }}
              >
                <Space direction="vertical">
                  {courseLearning.lectureIds.map(id =>
                    FileProgress({ file: courseLearning.lectureEntities[id] })
                  )}
                </Space>
              </Radio.Group>
            </Card>
            {courseLearning.test.id && (
              <Card
                title="随堂测验"
                headStyle={{ borderBottom: 0 }}
                bordered={false}
                bodyStyle={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyContent: 'space-between',
                  padding: '16px 24px',
                }}
              >
                <Descriptions column={1}>
                  <Descriptions.Item label="测验时间">
                    {examPaper ? moment(examPaper.startTime).format('YYYY-MM-DD HH:mm') : '--'}
                  </Descriptions.Item>
                  <Descriptions.Item label="测验成绩">{`${
                    examPaper ? `${examPaper.userScore}/${examPaper.totalScore}` : '--'
                  }`}</Descriptions.Item>
                  <Descriptions.Item label="测验结果">
                    {examPaper ? (
                      examPaper.result ? (
                        <Tag color="var(--manyun-success-color)">通过</Tag>
                      ) : (
                        <Tag color="var(--manyun-error-color)">未通过</Tag>
                      )
                    ) : (
                      '--'
                    )}
                  </Descriptions.Item>
                  <Descriptions.Item label="查看试卷">
                    {courseLearning.test.paperId ? (
                      <Button
                        type="link"
                        style={{ padding: 0, height: 22 }}
                        onClick={() => {
                          history.push(
                            generateTestPaperRoutePath({
                              id: courseLearning.test.paperId!,
                              examId: courseLearning.test.id,
                              courseId: courseLearning.id,
                            })
                          );
                        }}
                      >
                        查看
                      </Button>
                    ) : (
                      '--'
                    )}
                  </Descriptions.Item>
                </Descriptions>
                {courseLearning.properties &&
                  ((courseLearning.properties.isPass && !courseLearning.isFinished) ||
                    (!courseLearning.properties.isPass &&
                      (courseLearning.properties.retakeExamCount ?? 1) >=
                        courseLearning.testCount &&
                      !examPaper?.result)) && (
                    <div
                      style={{ display: 'flex', justifyContent: 'flex-end', alignItems: 'center' }}
                    >
                      {!isFinished ? (
                        <div style={{ color: '#ff4d4f', marginRight: '8px' }}>请先完成课程</div>
                      ) : (
                        <></>
                      )}
                      {examPaper &&
                      isFinished &&
                      (examPaper.state === BackendExamPaperStatus.INIT ||
                        examPaper.state === BackendExamPaperStatus.PROCESS) ? (
                        <Button
                          type="primary"
                          onClick={() => {
                            history.push(
                              generateTestPaperRoutePath({
                                id: courseLearning.test.paperId!,
                                examId: courseLearning.test.id,
                                courseId: courseLearning.id,
                              })
                            );
                          }}
                        >
                          继续测验
                        </Button>
                      ) : (
                        <ExamPaperCreator
                          text="开始测验"
                          disabled={!isFinished}
                          examId={courseLearning.test.id}
                          courseId={Number(id)}
                          onCompleted={examPaperId =>
                            history.push(
                              generateTestPaperRoutePath({
                                id: examPaperId,
                                examId: courseLearning.test.id,
                                courseId: courseLearning.id,
                              })
                            )
                          }
                        />
                      )}
                    </div>
                  )}
              </Card>
            )}
          </Card>
        </Space>
      </div>
    </LayoutContent>
  );
}

export function FileProgress({ file, ...rest }: { file: Lecture } & RadioProps) {
  return (
    <div key={file.id} style={{ width: 275 }}>
      <Tag
        key="tag"
        color={
          file.fileType === '.MP4' ? 'var(--manyun-success-color)' : 'var(--manyun-primary-color)'
        }
      >
        {file.fileType === '.MP4' ? '视频类' : '文档类'}
      </Tag>
      <Radio value={file.id} {...rest}>
        <Typography.Text
          key="label"
          ellipsis={{ tooltip: `${file.order}.${file.name}` }}
          style={{ width: 160 }}
        >{`${file.order}.${file.name}`}</Typography.Text>
      </Radio>
      <Progress
        key="progress"
        percent={
          file.isFinished
            ? 100
            : Number(((file.studyTime! * 100) / file.minSecondsAsCompleted!).toFixed(2))
        }
        size="small"
        style={{ width: 275 }}
      />
    </div>
  );
}

export function PDFReader({ filePath }: { filePath: string }) {
  const [numPages, setNumPages] = useState(null);
  const [pageNumber, setPageNumber] = useState(1);
  const [scale, setScale] = useState(1);

  function onDocumentLoadSuccess({ numPages }: { numPages: any }) {
    setNumPages(numPages);
  }

  function pageDefaultScreen() {
    setScale(1);
  }

  return (
    <div className={styles.view} style={{ overflow: 'auto' }}>
      <div className={styles.pageContainer}>
        <Document file={filePath} onLoadSuccess={onDocumentLoadSuccess}>
          <Page pageNumber={pageNumber} scale={scale} loading={<Spin size="large" />} />
        </Document>
      </div>
      <div className={styles.pageTool}>
        <Pagination
          simple
          current={pageNumber}
          total={(numPages as any) * 10}
          onChange={page => {
            setPageNumber(page);
          }}
        />
        <Tooltip title="放大">
          <PlusCircleOutlined
            style={{ fontSize: 22 }}
            onClick={() => {
              setScale(scale * 1.2);
            }}
          />
        </Tooltip>
        <Tooltip title="缩小">
          <MinusCircleOutlined
            style={{ fontSize: 22 }}
            onClick={() => {
              setScale(scale * 0.8);
            }}
          />
        </Tooltip>
        <Tooltip title="恢复默认">
          <HomeOutlined style={{ fontSize: 22 }} onClick={pageDefaultScreen} />
        </Tooltip>
      </div>
    </div>
  );
}

export function PDF({ id, file }: { id: number; file?: any }) {
  const intervalRef = useRef();
  const dispatch = useDispatch();

  const updateProgress = useCallback(() => {
    dispatch(
      getCourseLearningAction({
        id,
        fileId: file.id,
        progress: 2,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  useEffect(() => {
    if (file.isFinished) {
      return;
    }
    const currentInterval = window.setInterval(updateProgress, 2000);
    intervalRef.current = currentInterval as any;

    return () => {
      if (intervalRef.current !== undefined) {
        window.clearInterval(intervalRef.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  useEffect(() => {
    if (file.isFinished && intervalRef.current !== undefined) {
      window.clearInterval(intervalRef.current);
    }
  }, [file]);

  return file ? (
    <PDFReader
      filePath={`${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=${file.filePath}`}
    />
  ) : (
    <Spin spinning />
  );
}

export function Video({ id, file }: { id: number; file?: any }) {
  const playerRef = useRef<Player | null>(null);
  const intervalRef = useRef();
  const dispatch = useDispatch();
  const playerOptions: PlayerOptions = useMemo(
    () => ({
      fluid: true,
      controls: true,
      suppressNotSupportedError: false,
      preload: 'auto',
      playbackRates: [0.5, 1, 1.5, 2, 2.5],
      controlBar: {
        children: [
          { name: 'playToggle' },
          { name: 'currentTimeDisplay' },
          { name: 'progressControl' },
          { name: 'remainingTimeDisplay' },
          file.allowSeekForward && { name: 'playbackRateMenuButton' },
          {
            name: 'volumePanel',
            inline: false,
          },
          { name: 'FullscreenToggle' },
        ],
      },
    }),
    [file.allowSeekForward]
  );

  const updateProgress = useCallback(() => {
    dispatch(
      getCourseLearningAction({
        id,
        fileId: file.id,
        progress: 2,
      })
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch]);

  const playing = () => {
    const currentInterval = window.setInterval(updateProgress, 2000);
    intervalRef.current = currentInterval as any;
  };

  const pause = () => {
    window.clearInterval(intervalRef.current);
  };

  const handlePlayerMountedRef = useLatest(({ player }: { player: Player }) => {
    playerRef.current = player;

    if (file.isFinished) {
      return;
    }

    player.on('playing', playing);
    player.on('pause', pause);
  });

  const handlePlayerUnmounted = () => {
    if (intervalRef.current !== undefined) {
      window.clearInterval(intervalRef.current);
    }
  };

  useEffect(() => {
    if (file.isFinished) {
      if (intervalRef.current !== undefined) {
        window.clearInterval(intervalRef.current);
      }
      if (playerRef.current) {
        // playerRef.current.pause();
        // playerRef.current.off('playing', playing);
        // playerRef.current.off('pause', pause);
      }
    }
  }, [file]);

  useEffect(() => {
    const handleOnBlur = function () {
      if (file.holdOnBlur) {
        playerRef.current?.pause();
      }
    };
    window.addEventListener('blur', handleOnBlur);
    return () => {
      window.removeEventListener('blur', handleOnBlur);
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const videoPlayerView = useMemo(
    () => (
      <VideoPlayer
        className={styles.videoPlayer}
        sources={[
          {
            src: `${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=${file.filePath}`,
            type: 'video/mp4',
          },
        ]}
        options={playerOptions}
        onMounted={handlePlayerMountedRef.current}
        onUnmounted={handlePlayerUnmounted}
      />
    ),
    [file.filePath, handlePlayerMountedRef, playerOptions]
  );

  return file ? <div className={styles.view}>{videoPlayerView}</div> : <Spin spinning />;
}
