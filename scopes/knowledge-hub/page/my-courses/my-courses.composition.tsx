import React from 'react';
import { <PERSON>, Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  LEARNING_SPECIFIC_COURSE_ROUTE_PATH,
  MY_COURSES_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { MyCourses } from './my-courses';

export const BasicCoursewares = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchKnowledgeHubCategoryMocks(mock);
    // registerFetchCoursesWebMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[MY_COURSES_ROUTE_PATH]}>
          <Switch>
            <Route path={MY_COURSES_ROUTE_PATH} exact>
              <MyCourses />
            </Route>
            <Route path={LEARNING_SPECIFIC_COURSE_ROUTE_PATH} exact>
              {/* <CourseLearning /> */}
              <Link to={MY_COURSES_ROUTE_PATH}>GO BACK</Link>
              <span>courseLearning</span>
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
