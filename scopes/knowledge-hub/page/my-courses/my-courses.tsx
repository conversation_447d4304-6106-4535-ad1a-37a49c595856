import React, { useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { getLocationSearchMap } from '@manyun/dc-brain.util.get-location-search-map';
import { MyCourses as MyCoursesList } from '@manyun/knowledge-hub.ui.courses';
import { MyCoursesFilters } from '@manyun/knowledge-hub.ui.courses-filters';

export type MyCoursesProps = {};
const { TabPane } = Tabs;

export function MyCourses(props: MyCoursesProps) {
  const [tabKey, setTabKey] = useState('uncompleted');
  const { location } = useHistory();
  const { courseName } = getLocationSearchMap(location.search, ['courseName']);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card bordered={false}>
        <Tabs
          type="card"
          activeKey={tabKey}
          onChange={activeKey => {
            setTabKey(activeKey);
          }}
        >
          <TabPane tab="未学习" key="uncompleted">
            <MyCoursesFilters completedVariant="uncompleted" type="form" courseName={courseName} />
          </TabPane>
          <TabPane tab="已学习" key="completed">
            <MyCoursesFilters completedVariant="completed" type="form" />
          </TabPane>
        </Tabs>
      </Card>
      <Card bordered={false}>
        <Space style={{ width: '100%' }} direction="vertical">
          {tabKey === 'uncompleted' ? (
            <>
              <MyCoursesFilters completedVariant="uncompleted" type="tab" />
              <MyCoursesList completedVariant="uncompleted" />
            </>
          ) : (
            <MyCoursesList completedVariant="completed" />
          )}
        </Space>
      </Card>
    </Space>
  );
}
