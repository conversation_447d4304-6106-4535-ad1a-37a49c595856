import React, { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, useHistory, useParams } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EXAM_PAPER_URL,
  MY_EXAMS_PAGE_URL,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { ExamRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { MyExams } from './my-exams';

const SpecificExamMock = () => {
  const history = useHistory();
  const { id } = useParams<ExamRouteParams>();

  return (
    <div>
      <span>SPECIFIC EXAM: {id}</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </div>
  );
};

export const BasicMyExams = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // fetchExamPaperUsersWebMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[MY_EXAMS_PAGE_URL]}>
          <Route path={MY_EXAMS_PAGE_URL} exact>
            <MyExams />
          </Route>
          <Route path={EXAM_PAPER_URL} exact>
            <SpecificExamMock />
          </Route>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
