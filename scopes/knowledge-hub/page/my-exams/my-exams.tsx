import sortBy from 'lodash.sortby';
import moment from 'moment';
import React, { useCallback, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap } from '@manyun/dc-brain.util.get-location-search-map';
import {
  BackendUserExamPaperSatus,
  UserExamPaperStatusMapper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import {
  MyExamTypes,
  examsSliceActions,
  getMyExamsAction,
  selectActiveExamStatusTabKey,
  selectActiveExamTypeTabKey,
  selectMyExamsEntities,
  selectTypedMyExams,
} from '@manyun/knowledge-hub.state.exams';
import { ExamGalleryCover } from '@manyun/knowledge-hub.ui.exam-gallery-cover';

export const USER_EXAM_STATUS = [
  {
    value: BackendUserExamPaperSatus.INIT,
    label: UserExamPaperStatusMapper[BackendUserExamPaperSatus.INIT],
  },
  {
    value: BackendUserExamPaperSatus.DURING,
    label: UserExamPaperStatusMapper[BackendUserExamPaperSatus.DURING],
  },
  {
    value: BackendUserExamPaperSatus.STARTED,
    label: UserExamPaperStatusMapper[BackendUserExamPaperSatus.STARTED],
  },
];

export const HISTORY_EXAM_STATE_KEY = {
  SUBMIT: 'SUBMIT',
  PASS: 'PASS',
  FAIL: 'FAIL',
};

export const HISTORY_EXAM_STATE_TXT = {
  [HISTORY_EXAM_STATE_KEY.SUBMIT]: '判卷中',
  [HISTORY_EXAM_STATE_KEY.PASS]: '已通过',
  [HISTORY_EXAM_STATE_KEY.FAIL]: '未通过',
};

export const HISTORY_EXAM_STATE = [
  {
    value: HISTORY_EXAM_STATE_KEY.SUBMIT,
    label: HISTORY_EXAM_STATE_TXT[HISTORY_EXAM_STATE_KEY.SUBMIT],
  },
  {
    value: HISTORY_EXAM_STATE_KEY.PASS,
    label: HISTORY_EXAM_STATE_TXT[HISTORY_EXAM_STATE_KEY.PASS],
  },
  {
    value: HISTORY_EXAM_STATE_KEY.FAIL,
    label: HISTORY_EXAM_STATE_TXT[HISTORY_EXAM_STATE_KEY.FAIL],
  },
];

export type MyExamsProps = {};

export function MyExams(props: MyExamsProps) {
  const examTypeTabKey = useSelector(selectActiveExamStatusTabKey());
  const examTabKey = useSelector(selectActiveExamTypeTabKey());
  const { location } = useHistory();

  const { type, examName } = getLocationSearchMap(location.search, ['type', 'examName']);

  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [form1] = Form.useForm();

  const getExams = useCallback(
    key => {
      dispatch(
        getMyExamsAction({
          myExamType: key,
        })
      );
    },
    [dispatch]
  );

  useEffect(() => {
    if (examName && type) {
      dispatch(examsSliceActions.updateActiveExamStatusTabKey(type));
      dispatch(
        examsSliceActions.setMyExamsFields({
          myExamType: type,
          name: examName,
        })
      );
    } else if (examName) {
      dispatch(
        examsSliceActions.setMyExamsFields({
          myExamType: examTypeTabKey,
          name: examName,
        })
      );
    } else if (type) {
      dispatch(examsSliceActions.updateActiveExamStatusTabKey(type));
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examName, type]);

  useEffect(() => {
    getExams(examTypeTabKey);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examTypeTabKey]);

  const data = useSelector(selectTypedMyExams(examTypeTabKey));

  useEffect(() => {
    if (examTypeTabKey === MyExamTypes.MyAvailableExams) {
      const name = type === MyExamTypes.MyAvailableExams ? examName : '';
      form.setFieldsValue({
        timeRange: data.fields.timeRange?.length ? data.fields.timeRange.map(m => moment(m)) : [],
        state: data.fields.state,
        name: type ? name : data.fields.name,
      });
    }
    if (examTypeTabKey === MyExamTypes.MyHistoryExams) {
      const name = type === MyExamTypes.MyHistoryExams ? examName : '';
      form1.setFieldsValue({
        timeRange: data.fields.timeRange?.length ? data.fields.timeRange.map(m => moment(m)) : [],
        state: data.fields.state,
        name: type ? name : data.fields.name,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examTypeTabKey]);

  const exams = useSelector(selectMyExamsEntities(data?.visibleIds));
  const requiredExams = exams.filter(({ defaultUser }) => defaultUser);
  const selectedExams = exams.filter(({ defaultUser }) => !defaultUser);

  const search = () => {
    getExams(examTypeTabKey);
  };

  const reset = () => {
    dispatch(
      examsSliceActions.setMyExamsFields({
        myExamType: examTypeTabKey,
        timeRange: undefined,
        state: null,
        name: null,
      })
    );
    getExams(examTypeTabKey);
  };

  const empty = () => (
    <div style={{ textAlign: 'center', width: '100%' }}>
      <Empty image={Empty.PRESENTED_IMAGE_SIMPLE} description="暂无数据" />
    </div>
  );

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="large">
      <Card bordered={false}>
        <Tabs
          type="card"
          activeKey={examTypeTabKey}
          onChange={activeKey => {
            dispatch(
              examsSliceActions.updateActiveExamStatusTabKey(
                activeKey as MyExamTypes.MyAvailableExams | MyExamTypes.MyHistoryExams
              )
            );
          }}
        >
          <Tabs.TabPane tab="当前考试" key={MyExamTypes.MyAvailableExams}>
            <Form
              colon={false}
              layout="inline"
              form={form}
              onValuesChange={values => {
                dispatch(
                  examsSliceActions.setMyExamsFields({
                    ...values,
                    myExamType: MyExamTypes.MyAvailableExams,
                  })
                );
              }}
            >
              <Form.Item label="考试时间" name="timeRange">
                <DatePicker.RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm"
                  placeholder={['开始时间', '结束时间']}
                />
              </Form.Item>
              <Form.Item label="状态" name="state">
                <Select allowClear style={{ width: 200 }} options={USER_EXAM_STATUS}></Select>
              </Form.Item>
              <Form.Item label="考试名称" name="name">
                <Input allowClear placeholder="请输入考试名称关键字" />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" onClick={search}>
                    搜索
                  </Button>
                  <Button
                    onClick={() => {
                      form.resetFields();
                      reset();
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Tabs.TabPane>
          <Tabs.TabPane tab="历史考试" key={MyExamTypes.MyHistoryExams}>
            <Form
              colon={false}
              layout="inline"
              form={form1}
              onValuesChange={values => {
                dispatch(
                  examsSliceActions.setMyExamsFields({
                    ...values,
                    myExamType: MyExamTypes.MyHistoryExams,
                  })
                );
              }}
            >
              <Form.Item label="考试时间" name="timeRange">
                <DatePicker.RangePicker
                  showTime
                  format="YYYY-MM-DD HH:mm"
                  placeholder={['开始时间', '结束时间']}
                />
              </Form.Item>
              <Form.Item label="状态" name="state">
                <Select allowClear style={{ width: 200 }} options={HISTORY_EXAM_STATE}></Select>
              </Form.Item>
              <Form.Item label="考试名称" name="name">
                <Input allowClear placeholder="请输入考试名称关键字" />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button type="primary" onClick={search}>
                    搜索
                  </Button>
                  <Button
                    onClick={() => {
                      form1.resetFields();
                      reset();
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Tabs.TabPane>
        </Tabs>
      </Card>
      <Card bordered={false}>
        {examTypeTabKey === MyExamTypes.MyAvailableExams && (
          <Tabs
            activeKey={examTabKey}
            onChange={activeKey => {
              dispatch(
                examsSliceActions.updateActiveExamTypeTabKey(activeKey as 'required' | 'slected')
              );
              dispatch(
                examsSliceActions.setMyExamsFields({
                  myExamType: examTypeTabKey,
                  required: activeKey === 'required' ? true : false,
                })
              );
            }}
          >
            <Tabs.TabPane tab="必考考试" key="required">
              {!!requiredExams.length && (
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill,minmax(242px,1fr))',
                    gridRowGap: '58px',
                    gridColumnGap: '16px',
                  }}
                >
                  {requiredExams.map((exam: any) => {
                    return (
                      <ExamGalleryCover
                        key={exam.examId}
                        exam={exam}
                        examTypeTabKey={examTypeTabKey}
                      />
                    );
                  })}
                </div>
              )}
              {!requiredExams.length && empty()}
            </Tabs.TabPane>
            <Tabs.TabPane tab="选考考试" key="slected">
              {!!selectedExams.length && (
                <div
                  style={{
                    display: 'grid',
                    gridTemplateColumns: 'repeat(auto-fill,minmax(242px,1fr))',
                    gridRowGap: '58px',
                    gridColumnGap: '16px',
                  }}
                >
                  {selectedExams.map((exam: any) => {
                    return (
                      <ExamGalleryCover
                        key={exam.examId}
                        exam={exam}
                        examTypeTabKey={examTypeTabKey}
                      />
                    );
                  })}
                </div>
              )}
              {!selectedExams.length && empty()}
            </Tabs.TabPane>
          </Tabs>
        )}
        {examTypeTabKey === MyExamTypes.MyHistoryExams && (
          <>
            {!exams.length && empty()}
            {!!exams.length && (
              <div
                style={{
                  display: 'grid',
                  gridTemplateColumns: 'repeat(auto-fill,minmax(242px,1fr))',
                  gridRowGap: '58px',
                  gridColumnGap: '16px',
                }}
              >
                {exams.map((exam: any) => {
                  return (
                    <ExamGalleryCover key={exam.id} exam={exam} examTypeTabKey={examTypeTabKey} />
                  );
                })}
              </div>
            )}
          </>
        )}
      </Card>
    </Space>
  );
}
