import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { useCoursewaresFields } from '@manyun/knowledge-hub.hook.use-coursewares-fields';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CoursewareMutator } from '@manyun/knowledge-hub.ui.courseware-mutator';
import { AuthButton, Coursewares as CoursewaresTable } from '@manyun/knowledge-hub.ui.coursewares';
import { CoursewaresBatchUpdator } from '@manyun/knowledge-hub.ui.coursewares-batch-updator';
import { EditTreeCoursewaresCategroy } from '@manyun/knowledge-hub.ui.coursewares-category';
import { CoursewaresFilters } from '@manyun/knowledge-hub.ui.coursewares-filters';

import styles from './coursewares.module.less';

export function Coursewares() {
  const [, { categoryCode }] = useCoursewaresFields(Variant.ALL);

  return (
    <Space className={styles.coursewaresContainer} align="start" size="middle">
      <Card bordered={false} title="课件分类">
        <EditTreeCoursewaresCategroy />
      </Card>
      <Card bordered={false}>
        <Space direction="vertical" size="middle" style={{ width: '100%' }}>
          <CoursewaresFilters variant={Variant.ALL} />
          <Space>
            <CoursewareMutator />
            <AuthButton categoryCode={2} fileType="folder" authType="all" />
            <CoursewaresBatchUpdator />
          </Space>
          <CoursewaresTable variant={Variant.ALL} categoryCode={categoryCode} />
        </Space>
      </Card>
    </Space>
  );
}
