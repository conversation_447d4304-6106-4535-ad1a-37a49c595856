import React from 'react';
import { Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { COURSEWARES_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Coursewares } from './coursewares';

export const BasicCoursewares = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[COURSEWARES_ROUTE_PATH]}>
          <Switch>
            <Route path={COURSEWARES_ROUTE_PATH} exact>
              <Coursewares />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
