.coursewaresContainer {
  width: 100%;
  height: 100%;
  display: flex;

  > :global(.manyun-space-item) {
    height: 100%;

    &:last-child {
      flex: 1;
      // 由于里面包含设置了scroll:{{x:'max-content'}}的Table，导致宽度异常撑开，因此暂时用此方法解决
      width: 0px;
      :global(.manyun-card-body) {
        height: 100%;
      }
    }

    &:first-child {
      width: 380px;
    }

    > * {
      height: 100%;
      > :global(.manyun-card-body) {
        height: calc(100% - /* Card Head height */ 58px);
        overflow-y: auto;
      }
    }
  }
}
