import React, { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { SKILL_PAGE_URL } from '@manyun/knowledge-hub.route.knowledge-hub-routes';

import { Skill } from './skill';

const url = SKILL_PAGE_URL.replace(':id', '12');

export const BasicSkill = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    // destroyMock();
    // webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[url]}>
          <Route path={SKILL_PAGE_URL}>
            <Skill />
          </Route>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
