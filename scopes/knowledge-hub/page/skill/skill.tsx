import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import moment from 'moment';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import type { User as UserId } from '@manyun/auth-hub.model.user';
import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { ExamRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { cancleAssociatedExamWeb } from '@manyun/knowledge-hub.service.dcexam.cancle-associated-exam';
import type { BackendAssociatedExamCategories } from '@manyun/knowledge-hub.service.dcexam.fetch-associated-exam-categories';
import { fetchAssociatedExamCategoriesWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-associated-exam-categories';
import type {
  BackendCertifiedStaffs,
  ServiceQ,
} from '@manyun/knowledge-hub.service.dcexam.fetch-associated-users';
import { fetchAssociatedUsersWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-associated-users';
import { revokeUsersSkillWeb } from '@manyun/knowledge-hub.service.dcexam.revoke-users-skill';
import { ExamsCategory } from '@manyun/knowledge-hub.ui.exams-category';
import { SkillExamsCategoryLinker } from '@manyun/knowledge-hub.ui.skill-exams-category-linker';
import { SkillsIssuer } from '@manyun/knowledge-hub.ui.skills-issuer';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

type SearchUser = {
  idc?: string[];
  roles?: number[];
  userId?: UserId;
  awardTime?: Moment[];
};

type UserData = { data: BackendCertifiedStaffs[]; total: number };

export function Skill() {
  const [activeKey, setActiveKey] = useState('exams');
  const [examloading, setExamLoading] = useState(false);
  const [examData, setExamData] = useState<BackendAssociatedExamCategories[]>([]);
  const [userloading, setUserLoading] = useState(false);
  const [userData, setUserData] = useState<UserData>({ data: [], total: 0 });
  const [userPagination, setUserPagination] = useState({ page: 1, pageSize: 10 });
  const [userSelectedData, setUserSelectedData] = useState<React.Key[]>([]);
  const [form] = Form.useForm();
  const { id } = useParams<ExamRouteParams>();

  const certId = +id;

  useEffect(() => {
    if (activeKey === 'exams') {
      getAssociatedExam();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey]);

  useEffect(() => {
    if (activeKey === 'users') {
      getAssociatedUsers();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey, userPagination.page, userPagination.pageSize]);

  const getAssociatedExam = async () => {
    setExamData([]);
    setExamLoading(true);
    const { data, error } = await fetchAssociatedExamCategoriesWeb({ certId });
    setExamLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data.data && Array.isArray(data.data)) {
      setExamData(data.data);
    }
  };

  const getAssociatedUsers = async () => {
    setUserLoading(true);
    const values: SearchUser = form.getFieldsValue();
    const awardTime: Moment[] | undefined = values.awardTime;
    const params: ServiceQ = {
      certId,
      ...userPagination,
    };
    if (values.idc && Array.isArray(values.idc) && values.idc.length) {
      params.blockGuids = values.idc;
    }
    if (values.roles && Array.isArray(values.roles) && values.roles.length) {
      params.roleIds = values.roles;
    }
    if (awardTime) {
      params.issuedAtTimeRange = [
        Number(awardTime[0].unix() + '000'),
        Number(awardTime[1].unix() + '000'),
      ];
    }
    if (values.userId) {
      params.userId = values.userId?.id;
    }
    const { data, error } = await fetchAssociatedUsersWeb(params);
    setUserLoading(false);
    if (error) {
      message.error(error.message);
      setUserData({ data: [], total: 0 });
      return;
    }
    if (data.data && Array.isArray(data.data)) {
      const list: BackendCertifiedStaffs[] = data.data;
      setUserData({ data: list, total: data.total });
    }
  };

  const cancle = (id: number) => {
    Modal.confirm({
      title: '确认要取消技能认证的关联吗？',
      content: '取消关联后，已获得此项技能认证的学生不受影响。',
      okText: '确认',
      cancelText: '取消',
      onOk: () =>
        new Promise(async resolve => {
          const { error } = await cancleAssociatedExamWeb({
            id,
          });
          if (error) {
            message.error(error.message);
            resolve(false);
            return;
          }
          message.success('取消关联成功！');
          resolve(true);
          getAssociatedExam();
        }),
    });
  };

  const revoke = (ids: number[]) => {
    Modal.confirm({
      title: '确认要回收这项技能吗？',
      content: '回收技能后，对应人员将不在拥有该技能',
      okText: '确认',
      cancelText: '取消',
      onOk: () =>
        new Promise(async resolve => {
          const { error } = await revokeUsersSkillWeb({
            idList: ids,
          });
          if (error) {
            message.error(error.message);
            resolve(false);
            return;
          }
          message.success('回收成功！');
          resolve(true);
          setUserSelectedData([]);
          getAssociatedUsers();
        }),
    });
  };

  const search = () => {
    setUserPagination({ page: 1, pageSize: userPagination.pageSize });
    getAssociatedUsers();
  };

  const reset = () => {
    form.resetFields();
    setUserPagination({ page: 1, pageSize: 10 });
    getAssociatedUsers();
  };

  return (
    <Card bordered={false}>
      <Tabs defaultActiveKey={activeKey} type="card" onChange={key => setActiveKey(key)}>
        <Tabs.TabPane key="exams" tab="关联的考试">
          <Space style={{ width: '100%' }} direction="vertical" wrap>
            <SkillExamsCategoryLinker
              certId={certId}
              associatedExamTypes={examData.map(({ categoryCode }) => Number(categoryCode))}
              onConfirmButtonClose={() => getAssociatedExam()}
            />
            <Table
              rowKey="id"
              size="small"
              loading={examloading}
              columns={examColumns({ cancle })}
              dataSource={examData}
            />
          </Space>
        </Tabs.TabPane>
        <Tabs.TabPane key="users" tab="已认证人员">
          <Space style={{ width: '100%' }} direction="vertical" wrap>
            <QueryFilter
              form={form}
              items={[
                {
                  label: '位置',
                  name: 'idc',
                  control: <LocationTreeSelect multiple disabledTypes={['IDC']} authorizedOnly />,
                },
                {
                  label: '角色',
                  name: 'roles',
                  control: <RoleSelect mode="multiple" />,
                },
                { label: '姓名', name: 'userId', control: <UserSelect /> },
                {
                  label: '发放时间',
                  name: 'awardTime',
                  control: <DatePicker.RangePicker showTime />,
                },
              ]}
              onSearch={search}
              onReset={reset}
            />
            <Space>
              <SkillsIssuer certId={certId} onConfirmButtonClose={() => getAssociatedUsers()} />
              <Button
                type="primary"
                danger
                disabled={!userSelectedData.length}
                onClick={() => revoke(userSelectedData as number[])}
              >
                批量回收
              </Button>
            </Space>
            <Table
              rowKey="id"
              size="small"
              loading={userloading}
              columns={userColumns({ revoke })}
              dataSource={userData.data}
              pagination={{
                current: userPagination.page,
                pageSize: userPagination.pageSize,
                total: userData.total,
                onChange: (current, pageSize) =>
                  setUserPagination({ page: current, pageSize: pageSize }),
              }}
              rowSelection={{
                selectedRowKeys: userSelectedData,
                onChange: keys => setUserSelectedData(keys),
              }}
            />
          </Space>
        </Tabs.TabPane>
      </Tabs>
    </Card>
  );
}

const examColumns = ({
  cancle,
}: {
  cancle: (id: number) => void;
}): ColumnsType<BackendAssociatedExamCategories> => [
  {
    title: '考试类型',
    dataIndex: 'categoryCode',
    render: (txt: string | number) => <ExamsCategory categoryCode={Number(txt)} />,
  },
  {
    title: '关联人',
    dataIndex: 'creatorId',
    render: (txt: number, { creatorName }: BackendAssociatedExamCategories) => (
      <User.Link id={txt} name={creatorName} />
    ),
  },
  {
    title: '关联时间',
    dataIndex: 'gmtCreate',
    render: (txt: string) => moment(txt).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    dataIndex: '_actions',
    width: 100,
    fixed: 'right',
    render: (
      _: string,
      record: {
        id: number;
      }
    ) => (
      <Button style={{ padding: 0, height: 'auto' }} type="link" onClick={() => cancle(record.id)}>
        取消关联
      </Button>
    ),
  },
];

const userColumns = ({
  revoke,
}: {
  revoke: (ids: number[]) => void;
}): ColumnsType<BackendCertifiedStaffs> => [
  {
    title: '账号',
    dataIndex: 'accountName',
  },
  {
    title: '姓名',
    dataIndex: 'userId',
    render: (txt: number, { userName }: BackendCertifiedStaffs) => (
      <User.Link id={txt} name={userName} />
    ),
  },
  {
    title: '位置',
    dataIndex: 'blockGuidList',
    ellipsis: true,
    render: (txt: string[]) => {
      if (txt && Array.isArray(txt) && txt.length) {
        return txt.join(' | ');
      }
      return '--';
    },
  },
  {
    title: '所属角色',
    dataIndex: 'roleList',
    ellipsis: true,
    render: (txt: string[]) => {
      if (txt && Array.isArray(txt) && txt.length) {
        return txt.join(' | ');
      }
      return '--';
    },
  },
  {
    title: '发放人',
    dataIndex: 'creatorId',
    render: (txt: number, { creatorName }: BackendCertifiedStaffs) => (
      <User.Link id={txt} name={creatorName} />
    ),
  },
  {
    title: '发放时间',
    dataIndex: 'awardTime',
    render: (txt: string) => moment(txt).format('YYYY-MM-DD HH:mm:ss'),
  },
  {
    title: '操作',
    dataIndex: '_actions',
    width: 100,
    fixed: 'right',
    render: (
      _: string,
      record: {
        id: number;
      }
    ) => (
      <Button
        style={{ padding: 0, height: 'auto' }}
        type="link"
        onClick={() => revoke([record.id])}
      >
        回收
      </Button>
    ),
  },
];
