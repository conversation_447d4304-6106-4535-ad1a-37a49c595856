import React, { useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import InfoCircleOutlined from '@ant-design/icons/es/icons/InfoCircleOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import {
  useQuestionsCreateFields,
  useQuestionsCreation,
} from '@manyun/knowledge-hub.hook.use-questions-creation';
import { QUESTIONS_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  CreateMode,
  selectCreateQuestions,
  selectCreateQuestionsEntities,
} from '@manyun/knowledge-hub.state.questions';
import { QuestionEditor } from '@manyun/knowledge-hub.ui.question-editor';
import { ButtonQuestionType } from '@manyun/knowledge-hub.ui.question-type';
import { SelectTreeQuestionCategory } from '@manyun/knowledge-hub.ui.questions-category';
import { SelectQuestionsDifficulty } from '@manyun/knowledge-hub.ui.questions-difficulty';
import { QuestionsEditor } from '@manyun/knowledge-hub.ui.questions-editor';

import styles from './questions-creator.module.less';

const { TabPane } = Tabs;
export type QuestionsCreatorProps = {};

// eslint-disable-next-line no-empty-pattern
export function QuestionsCreator({}: QuestionsCreatorProps) {
  const history = useHistory();
  const [createMode, setCreateMode] = useState<CreateMode>('byFile');
  const [setCreateFields, fields] = useQuestionsCreateFields();
  const [createQuestions, submitLoaing] = useQuestionsCreation();
  const [parserData, setParserData] = useState([]);
  const [defaultForm] = Form.useForm();
  const createState = useSelector(selectCreateQuestions());
  const createQuestionsEntities = useSelector(selectCreateQuestionsEntities());

  const editorsRef = React.createRef<{ generatorData: (difficult?: number) => void }>();

  let refMapper = new Map(
    createQuestionsEntities.map(question => [
      question.id,
      React.createRef<FormInstance<any> & { rebuildEditor: (question: BackendQuestion) => void }>(),
    ])
  );

  const changeCreateMode = (mode: CreateMode) => {
    setCreateMode(mode);
    setCreateFields({ mode });
  };

  /**表单变化时 */
  const onEditorValuesChange = (key: number) => (values: any, quetion: BackendQuestion) => {
    /** 单选题 多选题 选项变化时 - 答案需要跟随修改  */
    if (values.options !== undefined) {
      const optionsArr: { option: string }[] =
        refMapper.get(key)!.current!.getFieldValue('options') || [];
      const answers: string[] = [];
      /**单选时 number 多选时 number[] */
      const answersIndex: number | number[] = refMapper.get(key)!.current!.getFieldValue('answers');
      const options: string[] = optionsArr.map(o => (o && o.option) || '');
      if (Array.isArray(answersIndex)) {
        answersIndex.forEach(index => {
          if (options[index] !== undefined) {
            answers.push(options[index]);
          }
        });
      } else {
        if (options[answersIndex] !== undefined) {
          answers.push(options[answersIndex]);
        }
      }
      // refMapper.get(key)!.current!.setFieldsValue({ answers: null });
      setCreateFields({
        updateQuestion: {
          id: quetion.id,
          options: options,
          answersForOptions: answers,
        },
      });
      return;
    }
    /**填空题 答案变化时 */
    if (values.answers !== undefined && quetion.type === 4) {
      const answersArr: { answer: string }[] =
        refMapper.get(key)!.current!.getFieldValue('answers') || [];
      values.answers = answersArr.map(o => (o && o.answer) || '');
    }

    /** 题型变化时  */
    if (values.type !== undefined) {
      /** 不需要切换确认时直接更新store */
      if (
        !(
          [1, 2].includes(quetion.type) &&
          [3, 4, 5].includes(values.type) &&
          refMapper.get(key)!.current!.getFieldValue('options').length > 0
        )
      ) {
        if ([3, 4, 5].includes(values.type)) {
          values.options = [];
          values.answers = [];
          refMapper.get(key)!.current!.setFieldsValue({ options: [], answers: [] });
        }
        setCreateFields({ updateQuestion: { id: quetion.id, ...values } });
        return;
      }
      return;
    }
    setCreateFields({ updateQuestion: { id: quetion.id, ...values } });
  };

  /** 题型切换确认后 清空store的选项 和答案*/
  const onEditorClearSure = (key: number) => (val: any, quetion: BackendQuestion) => {
    if ([3, 4, 5].includes(val)) {
      setCreateFields({ updateQuestion: { id: quetion.id, type: val, answers: [], options: [] } });
    }
  };

  const onAddQuestion = (type: number) => {
    setCreateFields({
      addQuestion: {
        type: type,
      },
    });
  };

  const onClickSubmit = () => {
    defaultForm.validateFields().then(val => {
      /**批量录入 */
      if (createState.mode === 'byFile') {
        createQuestions(parserData, res => {
          if (res) {
            history.push(QUESTIONS_ROUTE_PATH);
          }
        });
        return;
      }
      /**手动录入*/
      const promiseAll: any[] = [];
      refMapper.forEach(ref => {
        if (ref.current) promiseAll.push(ref.current.validateFields);
      });
      Promise.all(promiseAll.map(f => f())).then(() => {
        createQuestions(null, res => {
          if (res) {
            history.push(QUESTIONS_ROUTE_PATH);
          }
        });
      });
    });
  };

  const onClickDelete = (question: BackendQuestion) => {
    setCreateFields({ deleteQuestion: question });
  };

  const setFormInitialValue = (mode?: CreateMode) => {
    let activeKey = mode === undefined ? createMode : mode;
    if (activeKey === 'byFile') {
      defaultForm.setFieldsValue({
        defaultCategoryCode: fields.byFile.defaultCategoryCode,
        defaultDifficulty: fields.byFile.defaultDifficulty,
      });
    } else {
      defaultForm.setFieldsValue({
        defaultCategoryCode: fields.byHand.defaultCategoryCode,
      });
    }
  };

  useEffect(() => {
    setFormInitialValue();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.questionsCreatorContainer}>
      <Card bordered={false}>
        <Tabs
          defaultActiveKey={createMode}
          onChange={(key: string) => {
            changeCreateMode(key === '0' ? 'byFile' : 'byHand');
            setFormInitialValue(key === '0' ? 'byFile' : 'byHand');
          }}
          type="card"
        >
          {['批量录入', '手工录入'].map((tab, index) => (
            <TabPane tab={tab} key={index}>
              <Form form={defaultForm} layout="inline">
                <Form.Item
                  label="默认分类"
                  name="defaultCategoryCode"
                  rules={[{ required: true, message: '请选择默认分类' }]}
                >
                  <SelectTreeQuestionCategory
                    onChange={val => {
                      setCreateFields({ defaultCategoryCode: val as string });
                    }}
                    style={{ width: 260 }}
                    virtual={false}
                  />
                </Form.Item>
                {createMode === 'byFile' && (
                  <Form.Item
                    label="默认难度"
                    name="defaultDifficulty"
                    tooltip={{
                      title: '输入框中未添加试题难度的题目,可通过此设置批量补充难度',
                      icon: <InfoCircleOutlined />,
                    }}
                    rules={[{ required: true, message: '请选择默认难度' }]}
                  >
                    <SelectQuestionsDifficulty
                      onChange={val => {
                        setCreateFields({
                          defaultDifficulty: val as number,
                        });
                        editorsRef.current?.generatorData(val as number);
                      }}
                      style={{ width: 190 }}
                      allowClear={false}
                    />
                  </Form.Item>
                )}
              </Form>
            </TabPane>
          ))}
        </Tabs>
      </Card>
      <div className={styles.questionsCreatorContent}>
        {createMode === 'byFile' ? (
          <QuestionsEditor
            ref={editorsRef}
            onParserData={parserData => {
              setParserData(parserData);
            }}
          />
        ) : (
          <Card
            bordered={false}
            style={{ width: '100%', height: '100%' }}
            bodyStyle={{ height: '100%', overflowY: 'auto' }}
          >
            {createQuestionsEntities.map((question, index) => (
              <Space key={question.id} direction="vertical" style={{ width: '100%' }} size={3}>
                <QuestionEditor
                  ref={refMapper.get(question.id)}
                  autoInit={true}
                  question={question}
                  onEditorValuesChange={(values: any) => {
                    onEditorValuesChange(question.id)(values, question);
                  }}
                  onEditorClearSure={val => onEditorClearSure(question.id)(val, question)}
                />
                {createQuestionsEntities.length > 1 && (
                  <div style={{ marginLeft: '8.3%', width: '92%' }}>
                    <Button
                      danger
                      type="primary"
                      onClick={() => {
                        onClickDelete(question);
                      }}
                    >
                      删除
                    </Button>
                  </div>
                )}
                <Divider type="horizontal" />
              </Space>
            ))}
            <Form.Item
              labelCol={{ span: 2 }}
              colon={false}
              label={
                <span style={{ color: 'var(--manyun-primary-color)', marginTop: '5px' }}>
                  添加试题
                </span>
              }
            >
              <Space
                direction="horizontal"
                size="middle"
                style={{ width: '100%', marginTop: ' 10px' }}
              >
                {[1, 2, 3, 4, 5].map(type => (
                  <ButtonQuestionType
                    key={type}
                    onClick={() => {
                      onAddQuestion(type);
                    }}
                    typeVal={type}
                  />
                ))}
              </Space>
            </Form.Item>
          </Card>
        )}
      </div>
      <Card bordered={false} style={{ textAlign: 'center' }} bodyStyle={{ padding: '10px' }}>
        <Space>
          <Button
            type="primary"
            onClick={() => {
              onClickSubmit();
            }}
            loading={submitLoaing}
          >
            提交
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            返回
          </Button>
        </Space>
      </Card>
    </div>
  );
}
