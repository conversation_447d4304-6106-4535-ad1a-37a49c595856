import React, { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  QUESTIONS_CREATOR_ROUTE_PATH,
  QUESTIONS_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { QuestionsCreator } from './questions-creator';

const QuestionSMock = () => {
  const history = useHistory();
  return (
    <>
      <span>QuestionCreate</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </>
  );
};

export const BasicQuestionsCreator = () => {
  const [initial, update] = useState(false);
  useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // mutateMock(mock);
    // categoryMock(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';

    update(true);
  }, []);

  if (!initial) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[QUESTIONS_CREATOR_ROUTE_PATH]}>
          <Switch>
            <Route path={QUESTIONS_CREATOR_ROUTE_PATH} exact>
              <QuestionsCreator />
            </Route>
            <Route path={QUESTIONS_ROUTE_PATH} exact>
              <QuestionSMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
