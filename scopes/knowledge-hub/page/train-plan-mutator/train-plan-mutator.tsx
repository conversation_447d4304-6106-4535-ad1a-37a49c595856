import {
  ArrowDownOutlined,
  ArrowUpOutlined,
  DeleteOutlined,
  MinusCircleOutlined,
  PlusCircleOutlined,
  QuestionCircleOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import type { Moment } from 'moment';
import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { List } from '@manyun/base-ui.ui.list';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import type { Breadcrumb as LayoutBreadcrumb } from '@manyun/dc-brain.context.layout';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { CoursewaresSelector } from '@manyun/knowledge-hub.page.course-mutator';
import type { Course } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import type { CoursewareConfig } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import { selectCourseCategory } from '@manyun/knowledge-hub.state.courses';
import type { Courseware } from '@manyun/knowledge-hub.state.coursewares';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CoursesCategoryCascader } from '@manyun/knowledge-hub.ui.courses-category';
import { CoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';
import { TestMutator } from '@manyun/knowledge-hub.ui.exam-mutator';
import type { Users } from '@manyun/knowledge-hub.ui.group-users-picker';
import { GroupUsersPicker } from '@manyun/knowledge-hub.ui.group-users-picker';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import {
  CYCLES_TYPE_OPTIONS,
  CyclesType,
  DAY_WORK_WEEK_OPYIONS,
  DistributeMode,
  JobType,
  MONTH_DAY_IN_YEAR_OPYIONS,
  MONTH_DAY_OPYIONS,
  MONTH_IN_YEAR_OPYIONS,
  MonthlyWorkDayType,
  NUMBER_OPYIONS,
  PlanStatus,
  SchModeType,
  SlaUnit,
  WEEK_DAY_OPYIONS,
} from '@manyun/ticket.model.task';
import type { BackendCreateParams, Cycle, WeekType } from '@manyun/ticket.model.task';
import { createPlan } from '@manyun/ticket.service.create-plan';
import { fetchPlanDetail } from '@manyun/ticket.service.fetch-plan-detail';
import { updatePlan } from '@manyun/ticket.service.update-plan';

const timeFormatted = (second: number) => {
  const time = moment.duration(second, 'seconds');
  const hours = time.hours();
  const minutes = time.minutes();
  const seconds = time.seconds();
  return moment({ h: hours, m: minutes, s: seconds }).format('HH:mm:ss');
};

const formItemLayout = {
  labelCol: {
    xs: { span: 24 },
    sm: { span: 4 },
  },
  wrapperCol: {
    xs: { span: 24 },
    sm: { span: 20 },
  },
};
const formItemLayoutWithOutLabel = {
  wrapperCol: {
    xs: { span: 24, offset: 0 },
    sm: { span: 20, offset: 4 },
  },
};

export type SchProperties = Course & {
  trainMode: SchModeType;
  ownerId: number;
  ownerName: string;
  openTime: number;
  openUnit: 'DAY' | 'HOUR';
};

export function TrainPlanMutator() {
  const { id } = useParams<{ id: string }>();

  const { search } = useLocation();
  const { name } = getLocationSearchMap(search);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [courseFiles, setCourseFiles] = useState([] as any[]);
  const [coursewareKeys, setCoursewareKeys] = useState([] as string[]);
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const { entities } = useSelector(selectCourseCategory);
  const defaultUserData = {
    requiredUsers: {},
    optionalUsers: {},
  };

  const [userData, setUserData] = useState<Users>(defaultUserData);

  const [form] = Form.useForm();
  const periodUnit = Form.useWatch('periodUnit', form);
  const periodMonthWhen = Form.useWatch('periodMonthWhen', form);
  const periodYearWhen = Form.useWatch('periodYearWhen', form);
  const users = Form.useWatch('users', form);
  const blockScope = Form.useWatch('blockScope', form);

  useEffect(() => {
    if (id) {
      (async function () {
        const { data, error } = await fetchPlanDetail({ id });
        if (error) {
          message.error(error.message);
          return;
        }
        if (data?.schProperties) {
          const schProperties = JSON.parse(data.schProperties) as SchProperties;

          const level2 = entities[Number(schProperties.categoryCode)]?.parentId;
          const level1 = level2 ? entities[level2]?.parentId : undefined;
          form.setFieldsValue({
            ...generateCyclesRelateData(data?.repeatCycle, data?.repeatCycle?.periodUnit),
            name: data.name,
            blockScope: data.blockScope.map(block => block.blockGuid),
            categoryCode: [level1, level2, Number(schProperties.categoryCode)],
            schMode: data.schMode,
            periodUnit: data.repeatCycle?.periodUnit,
            allowTriggerTime: Array.isArray(data?.allowTriggerTime)
              ? data.allowTriggerTime.map(time =>
                  moment(
                    data?.repeatCycle?.periodUnit !== CyclesType.None
                      ? `${moment().format('YYYY-MM-DD')} ${time}`
                      : moment(time)
                  )
                )
              : [],
            endTime: data.endTime ? moment(data.endTime) : undefined,
            users: {
              requiredUsers: {
                roleKeys: schProperties.defaultUserConfig.roles,
                userKeys: schProperties.defaultUserConfig.users,
                deptKeys: schProperties.defaultUserConfig.departments,
              },
              optionalUsers: {
                roleKeys: schProperties.optionalUserConfig.roles,
                userKeys: schProperties.optionalUserConfig.users,
                deptKeys: schProperties.optionalUserConfig.departments,
              },
            },
            openTime: schProperties.openTime,
            openUnit: schProperties.openUnit,
            owner: { label: data.owner?.name, value: data.owner?.id },
            status: data.isActivated,
            required: !!schProperties.examConfig.paperId,
            paperId: schProperties.examConfig.paperId,
            mustPass: schProperties.examConfig.isPass,
            passingScore: schProperties.examConfig.passGrade,
            timeLimit: schProperties.examConfig.answerTime,
            retakeExamCount: schProperties.examConfig.retakeExamCount,
            failedExamCount: schProperties.examConfig.failedExamCount,
          });

          const sort = schProperties.courseFileList.sort((a, b) => {
            return a.order - b.order;
          });

          const files = schProperties.courseFileList.map(courseFile => {
            const checked = courseFile.minTime !== undefined && courseFile.minTime > 0;
            form.setFieldsValue({
              [`${courseFile.fileType}_${courseFile.fileId}-name`]: courseFile.name,
            });
            return {
              ...courseFile,
              checked,
              minSecondsAsCompleted: checked ? Number(courseFile.minTime) / 60 || 1 : 1,
              holdOnBlur: courseFile.stopAfterLeave,
              id: courseFile.fileId,
              groupKey: `${courseFile.fileType}_${courseFile.fileId}`,
              allowSeekForward: courseFile.allowFastForward,
            };
          });
          setCourseFiles(files);
          setCoursewareKeys(sort.map(courseFile => `${courseFile.fileType}_${courseFile.fileId}`));
          setUserData({
            requiredUsers: {
              roleKeys: schProperties.defaultUserConfig.roles,
              userKeys: schProperties.defaultUserConfig.users,
              deptKeys: schProperties.defaultUserConfig.departments,
            },
            optionalUsers: {
              roleKeys: schProperties.optionalUserConfig.roles,
              userKeys: schProperties.optionalUserConfig.users,
              deptKeys: schProperties.optionalUserConfig.departments,
            },
          });
        }
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const onHandleActions = (groupKey: string, action: string) => {
    const idx = courseFiles.findIndex(courseFile => courseFile.groupKey === groupKey);
    const datas = [...courseFiles];
    if (action === 'delete') {
      datas.splice(idx, 1);
      const index = coursewareKeys.findIndex(courseFilesId => courseFilesId === groupKey);
      const ids = [...coursewareKeys];
      ids.splice(index, 1);
      setCoursewareKeys(ids);
    } else if (action === 'up') {
      if (idx === 0) {
        message.error('已经是第一条！');
        return;
      }
      datas.splice(idx - 1, 0, datas[idx]);
      datas.splice(idx + 1, 1);
    } else if (action === 'down') {
      datas.splice(idx + 2, 0, datas[idx]);
      datas.splice(idx, 1);
      if (idx === courseFiles.length - 1) {
        message.error('已经是最后一条！');
        return;
      }
    }
    setCourseFiles(datas);
  };

  const onHandleValues = (groupKey: string, key: string, value: unknown) => {
    const idx = courseFiles.findIndex(courseFile => courseFile.groupKey === groupKey);
    const datas = [...courseFiles];
    datas[idx] = { ...datas[idx], [key]: value };
    setCourseFiles(datas);
  };

  const handleAdd = useCallback(
    (selectedIds: string[], selectedCoursewares: Courseware[], callback: () => void) => {
      const datas = selectedCoursewares.map((selectedCourseware: Courseware) => {
        return {
          ...selectedCourseware,
          checked: false,
          allowSeekForward: true,
          holdOnBlur: true,
          minSecondsAsCompleted: selectedCourseware.fileTime
            ? (Math.floor(selectedCourseware.fileTime / 60) || 1).toFixed(0)
            : 1,
        };
      });
      setCourseFiles([...courseFiles, ...datas]);
      setCoursewareKeys([...selectedIds, ...coursewareKeys]);
      callback();
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [courseFiles, coursewareKeys]
  );

  const onFinish = () => {
    if (courseFiles.length === 0) {
      message.error('至少添加一条课件！');
      return;
    }

    form.validateFields().then(async formValue => {
      const {
        blockScope,

        allowTriggerTime,
        endTime,
        status,

        periodUnit,
        name,
        period,

        days,
        periodMonthWhen,
        sortNum,
        atDayType,
        periodYearWhen,
        months,
        isRemove,

        categoryCode,
        schMode,
        users,
        openTime,
        openUnit,
        owner,
        paperId,
        paperName,
        mustPass,
        passingScore,
        timeLimit,
        retakeExamCount,
        failedExamCount,
      } = formValue;

      setLoading(true);
      const lectureEntities: Record<string, CoursewareConfig> = {};
      courseFiles.forEach((courseFile, index) => {
        lectureEntities[courseFile.groupKey] = {
          holdOnBlur: courseFile.holdOnBlur,
          allowSeekForward: courseFile.allowSeekForward,
          minSecondsAsCompleted: courseFile.checked
            ? courseFile.minSecondsAsCompleted * 60
            : undefined,
          name: formValue[`${courseFile.groupKey}-name`].trim(),
          order: index + 1,
          fileId: courseFile.groupKey.split('_')[1],
          categoryCode: courseFile.categoryCode,
          fileName: courseFile.fileName,
          filePath: courseFile.filePath,
          fileTime: courseFile.fileTime,
          fileType: courseFile.fileType,
        };
      });

      const courseFileList = coursewareKeys.map(coursewareKey => {
        const {
          fileId,
          allowSeekForward,
          holdOnBlur,
          minSecondsAsCompleted,
          name,
          order,
          categoryCode,
          fileName,
          filePath,
          fileTime,
          fileType,
        } = lectureEntities[coursewareKey];

        return {
          name,
          fileId,
          order,
          allowFastForward: allowSeekForward,
          stopAfterLeave: holdOnBlur,
          minTime: minSecondsAsCompleted,
          categoryCode,
          fileName,
          filePath,
          fileTime,
          fileType,
        };
      });

      const defaultUserConfig = {
        users: users.requiredUsers?.userKeys || [],
        roles: users.requiredUsers?.roleKeys || [],
        departments: users.requiredUsers?.deptKeys || [],
      };

      const optionalUserConfig = {
        users: users.optionalUsers?.userKeys || [],
        roles: users.optionalUsers?.roleKeys || [],
        departments: users.optionalUsers?.deptKeys || [],
      };

      const examConfig = {
        paperId: paperId!,
        paperName: paperName,
        isPass: mustPass ?? false,
        passGrade: passingScore!,
        answerTime: timeLimit!,
        retakeExamCount: retakeExamCount,
        failedExamCount: failedExamCount,
      };

      const schProperties = {
        courseName: name,
        categoryCode: categoryCode[categoryCode.length - 1],
        trainMode: schMode,
        openTime,
        openUnit,

        defaultUserConfig,
        optionalUserConfig,
        courseFileList,
        examConfig,
      };

      const params = {
        name: name,
        blockScope:
          Array.isArray(blockScope) &&
          blockScope.map(blockguid => ({
            blockGuid: blockguid,
            scopeType: DistributeMode.Block,
            scopeFlag: null,
          })),

        jobType: JobType.TrainPlan,

        allowTriggerTime: allowTriggerTime.map((time: moment.MomentInput) =>
          moment(time)
            .set({ second: 0 })
            .format(periodUnit === CyclesType.None ? 'YYYY-MM-DD HH:mm:ss' : 'HH:mm')
        ),
        endTime: endTime ? moment(endTime).endOf('day').valueOf() : null,
        status: status,

        cycles: generateCyclesParams({
          periodUnit,
          allowTriggerTime,
          period,
          isRemove,
          days,
          periodMonthWhen,
          sortNum,
          atDayType,
          periodYearWhen,
          months,
        }),
        periodUnit: periodUnit,
        subJobType: categoryCode[categoryCode.length - 1],
        schMode: schMode,
        schProperties: JSON.stringify(schProperties),
        jobItems: [],
        jobSla: 0,
        slaUnit: SlaUnit.Minutes,
        splitors: [],
        ownerId: owner.value,
        ownerName: owner.label,
      };

      if (!id) {
        const { error } = await createPlan(params as BackendCreateParams);
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        message.success('新建计划成功');
      } else {
        const { error } = await updatePlan({ ...params, id: id });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        message.success('修改计划成功');
      }

      history.goBack();
    });
  };

  return (
    // eslint-disable-next-line react/jsx-filename-extension
    <LayoutContent
      pageCode={
        id ? 'page_knowledge-hub_train-plan-updater' : 'page_knowledge-hub_train-plan-creator'
      }
      composeBreadcrumbs={(value: LayoutBreadcrumb[]) => [
        ...value,
        { key: 'train-plan-mutator', text: id ? `编辑${name ?? id}` : '新建课程培训计划' },
      ]}
    >
      <Space
        style={{ width: '100%', display: 'flex', marginBottom: '30px' }}
        direction="vertical"
        size="middle"
      >
        <Card title="基本配置">
          <Form
            // ref={ref}
            style={{ width: 575 }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 20 }}
            form={form}
            initialValues={{
              enabled: true,
              users: {
                requiredUsers: { roleKeys: [], userKeys: [] },
                optionalUsers: { roleKeys: [], userKeys: [] },
              } as Users,
              isRemove: 0,
              sortNum: 1,
              atDayType: MonthlyWorkDayType.NaturalDay,
              months: [1],
              periodUnit: CyclesType.None,
              allowTriggerTime: [moment()],
              required: false,
            }}
          >
            <Form.Item
              label="计划名称"
              name="name"
              rules={[
                { required: true, whitespace: true, message: '请输入计划名称！' },
                { max: 30, message: '最多输入 30 个字符！' },
              ]}
            >
              <Input style={{ width: 384 }} allowClear />
            </Form.Item>
            <Form.Item
              label="计划范围"
              name="blockScope"
              rules={[
                {
                  required: true,
                  message: '楼栋必填',
                },
              ]}
            >
              <LocationTreeSelect
                style={{ width: 224 }}
                multiple
                authorizedOnly
                disabledTypes={['IDC']}
                // disabled={mode === 'edit'}
                onChange={() => {
                  form.setFieldValue('users', defaultUserData);
                  setUserData(defaultUserData);
                }}
              />
            </Form.Item>
            <Form.Item
              label="课程分类"
              name="categoryCode"
              rules={[
                {
                  required: true,
                  type: 'boolean',
                  transform: value => {
                    return value?.length === 3 || undefined;
                  },
                  message: '请选择课程分类！',
                },
              ]}
            >
              <CoursesCategoryCascader style={{ width: 384 }} trigger="onDidMount" />
            </Form.Item>
            <Form.Item
              label="培训方式"
              name="schMode"
              initialValue={SchModeType.Online}
              rules={[{ required: true }]}
            >
              <Radio.Group>
                <Radio value={SchModeType.Online}>在线培训</Radio>
                <Radio value={SchModeType.Offline}>线下培训</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item label="计划排期" required>
              <Space direction="vertical" style={{ width: '100%' }} size="large">
                <Form.Item
                  name="periodUnit"
                  rules={[
                    {
                      required: true,
                      message: '计划排期必选',
                    },
                  ]}
                  noStyle
                >
                  <Select
                    style={{ width: 224 }}
                    options={CYCLES_TYPE_OPTIONS}
                    onChange={() => {
                      form.setFieldsValue({ period: 1, days: [], allowTriggerTime: [] });
                    }}
                  />
                </Form.Item>
                {periodUnit === CyclesType.Day && (
                  <Form.Item>
                    <Space direction="vertical" style={{ width: '100%' }} size="large">
                      <Space>
                        <span>每</span>
                        <Form.Item
                          name="period"
                          noStyle
                          rules={[{ required: true, message: '必填' }]}
                        >
                          <InputNumber min={1} max={364} />
                        </Form.Item>
                        <span>天</span>
                      </Space>

                      <Form.Item name="isRemove" noStyle>
                        <RemoveWorkDays />
                      </Form.Item>
                    </Space>
                  </Form.Item>
                )}
                {periodUnit === CyclesType.Week && (
                  <Form.Item>
                    <Space direction="vertical" size="large">
                      <Space size="middle">
                        <Space>
                          <span>每</span>
                          <Form.Item
                            name="period"
                            noStyle
                            rules={[{ required: true, message: '必填' }]}
                          >
                            <InputNumber min={1} max={52} />
                          </Form.Item>
                          <span>周</span>
                        </Space>

                        <Space>
                          <span>天</span>
                          <Form.Item
                            name="days"
                            noStyle
                            rules={[{ required: true, message: '天必选' }]}
                          >
                            <Select
                              mode="multiple"
                              options={WEEK_DAY_OPYIONS}
                              style={{ width: 224 }}
                            />
                          </Form.Item>
                        </Space>
                      </Space>

                      <Form.Item name="isRemove" noStyle>
                        <RemoveWorkDays />
                      </Form.Item>
                    </Space>
                  </Form.Item>
                )}

                {periodUnit === CyclesType.Month && (
                  <Form.Item>
                    <Space direction="vertical" style={{ width: '100%' }} size="large">
                      <Space>
                        <Space>
                          <span>每</span>
                          <Form.Item
                            name="period"
                            noStyle
                            rules={[{ required: true, message: '必填' }]}
                          >
                            <InputNumber min={1} max={12} />
                          </Form.Item>
                          <span>月</span>
                        </Space>
                      </Space>
                      <Form.Item
                        name="periodMonthWhen"
                        noStyle
                        rules={[{ required: true, message: '必选项' }]}
                      >
                        <Radio.Group>
                          <Space direction="vertical" style={{ width: '100%' }} size="large">
                            <Radio value>每逢</Radio>
                            {periodMonthWhen === true && (
                              <Form.Item label="天" required>
                                <Space direction="vertical" size="large">
                                  <Form.Item
                                    name="days"
                                    rules={[{ required: true, message: '天必选' }]}
                                    noStyle
                                  >
                                    <Select
                                      options={MONTH_DAY_OPYIONS}
                                      mode="multiple"
                                      style={{ width: '100%' }}
                                    />
                                  </Form.Item>
                                  <Form.Item name="isRemove" noStyle>
                                    <RemoveWorkDays />
                                  </Form.Item>
                                </Space>
                              </Form.Item>
                            )}
                            <br />
                            <Radio value={false}>在</Radio>
                            {periodMonthWhen === false && (
                              <Space size="middle">
                                <Form.Item name="sortNum" noStyle>
                                  <Select options={NUMBER_OPYIONS} style={{ width: 224 }} />
                                </Form.Item>
                                <Form.Item name="atDayType" noStyle>
                                  <Select options={DAY_WORK_WEEK_OPYIONS} style={{ width: 224 }} />
                                </Form.Item>
                              </Space>
                            )}
                          </Space>
                        </Radio.Group>
                      </Form.Item>
                    </Space>
                  </Form.Item>
                )}

                {periodUnit === CyclesType.Year && (
                  <Form.Item>
                    <Space direction="vertical" style={{ width: '100%' }} size="large">
                      <Space>
                        <Space>
                          <span>每</span>
                          <Form.Item
                            name="period"
                            noStyle
                            rules={[{ required: true, message: '必填' }]}
                          >
                            <InputNumber min={1} max={10} />
                          </Form.Item>
                          <span>年</span>
                        </Space>
                      </Space>
                      <Form.Item
                        name="periodYearWhen"
                        noStyle
                        rules={[{ required: true, message: '必选项' }]}
                      >
                        <Radio.Group>
                          <Space direction="vertical" style={{ width: '100%' }} size="large">
                            <Radio value>每逢</Radio>
                            {periodYearWhen === true && (
                              <Form.Item label="日期">
                                <Space direction="vertical" size="large">
                                  <Form.Item
                                    name="days"
                                    rules={[{ required: true, message: '日期必选' }]}
                                    noStyle
                                  >
                                    <TreeSelect
                                      style={{ width: '100%' }}
                                      treeData={MONTH_DAY_IN_YEAR_OPYIONS}
                                      multiple
                                      treeNodeFilterProp="label"
                                      showSearch
                                    />
                                  </Form.Item>
                                  <Form.Item name="isRemove" noStyle>
                                    <RemoveWorkDays />
                                  </Form.Item>
                                </Space>
                              </Form.Item>
                            )}
                            <br />
                            <Radio value={false}>在</Radio>
                            {periodYearWhen === false && (
                              <Space size="middle">
                                <Form.Item name="months" noStyle>
                                  <Select
                                    mode="multiple"
                                    options={MONTH_IN_YEAR_OPYIONS}
                                    style={{ width: 224 }}
                                  />
                                </Form.Item>
                                <Form.Item name="sortNum" noStyle>
                                  <Select options={NUMBER_OPYIONS} style={{ width: 224 }} />
                                </Form.Item>
                                <Form.Item name="atDayType" noStyle>
                                  <Select options={DAY_WORK_WEEK_OPYIONS} style={{ width: 224 }} />
                                </Form.Item>
                              </Space>
                            )}
                          </Space>
                        </Radio.Group>
                      </Form.Item>
                    </Space>
                  </Form.Item>
                )}
              </Space>
            </Form.Item>
            <Form.List
              name="allowTriggerTime"
              rules={[
                {
                  validator: async (_, triggerTime) => {
                    if (!triggerTime || triggerTime.length < 1 || !triggerTime.length) {
                      return Promise.reject(new Error('请至少添加一项触发时间'));
                    } else if (
                      triggerTime.length > 1 &&
                      !triggerTime.some(
                        (item: Moment | undefined | null) =>
                          typeof item === 'undefined' || item === null
                      )
                    ) {
                      let errorFlag = false;
                      for (let i = 0; i < triggerTime.length - 1; i++) {
                        if (
                          Math.abs(triggerTime[i].valueOf() - triggerTime[i + 1].valueOf()) <
                          10 * 60 * 1000
                        ) {
                          errorFlag = true;
                        }
                      }
                      return errorFlag
                        ? Promise.reject(new Error('触发时间前后至少间隔10分钟'))
                        : Promise.resolve();
                    } else {
                      return Promise.resolve();
                    }
                  },
                },
              ]}
            >
              {(fields, { add, remove }, { errors }) => (
                <>
                  {fields.map((field, index) => (
                    <>
                      <Form.Item
                        required
                        label={index === 0 ? '触发时间' : ''}
                        {...(index === 0 ? formItemLayout : formItemLayoutWithOutLabel)}
                        key={field.key}
                      >
                        <Space>
                          <Form.Item
                            {...field}
                            noStyle
                            rules={[{ required: true, message: '请填写触发时间' }]}
                          >
                            <DatePicker
                              picker={periodUnit === CyclesType.None ? 'date' : 'time'}
                              format={periodUnit === CyclesType.None ? 'YYYY-MM-DD HH:mm' : 'HH:mm'}
                              showTime={periodUnit === CyclesType.None}
                              disabledDate={
                                periodUnit === CyclesType.None ? handleEndDisabledDate : undefined
                              }
                              disabledTime={
                                periodUnit === CyclesType.None ? disabledDateTime : undefined
                              }
                            />
                          </Form.Item>
                          {fields.length > 1 ? (
                            <MinusCircleOutlined onClick={() => remove(field.name)} />
                          ) : null}
                        </Space>
                      </Form.Item>
                    </>
                  ))}
                  <Form.Item {...formItemLayoutWithOutLabel}>
                    <Button
                      type="dashed"
                      style={{ width: 224 }}
                      icon={<PlusCircleOutlined />}
                      onClick={() => {
                        if (fields.length === 6) {
                          message.warning('最多支持添加6项触发时间');
                          return;
                        }
                        add();
                      }}
                    >
                      添加触发时间
                    </Button>
                    <Form.ErrorList errors={errors} />
                  </Form.Item>
                </>
              )}
            </Form.List>

            {periodUnit !== CyclesType.None && (
              <Form.Item
                label="结束时间"
                name="endTime"
                rules={[
                  () => ({
                    validator(_, value) {
                      if (value && moment(value).valueOf() < moment().startOf('day').valueOf()) {
                        return Promise.reject(new Error('结束时间不可小于今天！'));
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <DatePicker format="YYYY-MM-DD" />
              </Form.Item>
            )}

            <Form.Item
              label="人员选择"
              name="users"
              rules={[
                {
                  required: true,
                  message: '必修人员或选修人员至少选其一！',
                  type: 'boolean',
                  transform: (value: Users) => {
                    if (
                      !value.optionalUsers?.roleKeys?.length &&
                      !value.optionalUsers?.userKeys?.length &&
                      !value.optionalUsers?.deptKeys?.length &&
                      !value.requiredUsers?.roleKeys?.length &&
                      !value.requiredUsers?.userKeys?.length &&
                      !value.requiredUsers?.deptKeys?.length
                    ) {
                      return undefined;
                    }
                    return true;
                  },
                },
              ]}
            >
              <GroupUsersPicker
                disabled={!blockScope || (Array.isArray(blockScope) && blockScope.length === 0)}
                blockGuids={blockScope}
                requiredText="必修人员设置"
                optionalText="选修人员设置"
                onReset={() => form.setFieldValue('users', userData)}
                onSubmit={() => {
                  setUserData(users);
                }}
              />
            </Form.Item>

            <Form.Item
              style={{ marginBottom: 0 }}
              label={
                <>
                  开放时间
                  <Tooltip title="课程生成后供学员参与的有效时间">
                    <QuestionCircleOutlined />
                  </Tooltip>
                </>
              }
              required
            >
              <Space.Compact>
                <Form.Item name="openTime" rules={[{ required: true, message: `请输入开放时间` }]}>
                  <InputNumber min={1} precision={0} />
                </Form.Item>
                <Form.Item
                  name="openUnit"
                  rules={[{ required: true, message: `请选择开放时间单位` }]}
                  initialValue="DAY"
                >
                  <Select
                    style={{ width: 72 }}
                    options={[
                      {
                        value: 'HOUR',
                        label: '小时',
                      },
                      {
                        value: 'DAY',
                        label: '天',
                      },
                    ]}
                  />
                </Form.Item>
              </Space.Compact>
            </Form.Item>
            <Form.Item
              label="负责人"
              name="owner"
              rules={[
                {
                  required: true,
                  message: '负责人必填！',
                },
              ]}
            >
              <UserSelect style={{ width: 216 }} allowClear labelInValue userState="in-service" />
            </Form.Item>

            <Form.Item label="状态" name="status" initialValue={PlanStatus.On}>
              <Radio.Group>
                <Radio value={PlanStatus.On}>启用</Radio>
                <Radio value={PlanStatus.Off}>禁用</Radio>
              </Radio.Group>
            </Form.Item>
          </Form>
        </Card>
        <Card title="课件配置">
          <CoursewaresSelector
            btnText="添加课件"
            modelText="添加课件"
            handleAdd={handleAdd}
            coursewareKeys={coursewareKeys}
          />

          <Form form={form}>
            {courseFiles?.map((item, i) => {
              return (
                <Card style={{ width: '100%', height: '204px', marginTop: '16px' }} key={i}>
                  <div style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
                    <div>
                      <div style={{ display: 'flex', alignItems: 'baseline' }}>
                        <Form.Item
                          label="课程别名"
                          name={`${item.groupKey}-name`}
                          rules={[
                            { required: true, whitespace: true, message: '课件别名不能为空！' },
                            { max: 30, message: '课件别名不超过30位！' },
                          ]}
                          style={{ marginBottom: 8 }}
                        >
                          <Input style={{ width: 384 }} placeholder="请输入课件别名" allowClear />
                        </Form.Item>
                        <Tag
                          color={item.fileType === '.MP4' ? 'green' : 'blue'}
                          style={{ marginLeft: 8 }}
                        >
                          {item.fileType === '.MP4' ? '视频类' : '文档类'}
                        </Tag>
                      </div>
                      <Form.Item label="课程名称" style={{ marginBottom: 8 }}>{`${item.fileName}${
                        item.fileType === '.MP4' ? `(${timeFormatted(item.fileTime!)})` : ''
                      }`}</Form.Item>
                      <Form.Item label="课程分类" style={{ marginBottom: 8 }}>
                        <CoursewareCategory
                          categoryCode={Number(item.categoryCode)}
                          variant={Variant.ALL}
                        />
                      </Form.Item>
                      <Space>
                        <>
                          是否设定最小学习时间
                          <Radio.Group
                            onChange={e => {
                              onHandleValues(item.groupKey, 'checked', e.target.value);
                            }}
                            value={item.checked}
                          >
                            <Radio value={true}>是</Radio>
                            <Radio value={false}>否</Radio>
                          </Radio.Group>
                          <InputNumber
                            defaultValue={item.minSecondsAsCompleted || 1}
                            value={item.minSecondsAsCompleted}
                            min={1}
                            max={500}
                            step={1}
                            precision={0}
                            formatter={value => {
                              if (value === undefined) {
                                return '1';
                              }
                              return value.toString();
                            }}
                            parser={(displayValue?: string) => {
                              if (displayValue === undefined) {
                                return 1;
                              }

                              return Number(displayValue);
                            }}
                            disabled={!item.checked}
                            onChange={value => {
                              onHandleValues(item.groupKey, 'minSecondsAsCompleted', value);
                            }}
                          />
                          分钟
                          {item.fileType === '.MP4' && (
                            <>
                              <Divider type="vertical" />
                              离开页面是否停止播放:
                              <Radio.Group
                                onChange={e => {
                                  onHandleValues(item.groupKey, 'holdOnBlur', e.target.value);
                                }}
                                value={item.holdOnBlur}
                              >
                                <Radio value={true}>是</Radio>
                                <Radio value={false}>否</Radio>
                              </Radio.Group>
                              <Divider type="vertical" />
                              是否允许视频倍速播放:
                              <Radio.Group
                                onChange={e => {
                                  onHandleValues(item.groupKey, 'allowSeekForward', e.target.value);
                                }}
                                value={item.allowSeekForward}
                              >
                                <Radio value={true}>是</Radio>
                                <Radio value={false}>否</Radio>
                              </Radio.Group>
                            </>
                          )}
                        </>
                      </Space>
                    </div>
                    <div style={{ alignSelf: 'start', color: 'rgba(0,0,0,0.45)' }}>
                      {courseFiles.length > 1 ? (
                        <>
                          <Tooltip key="delete" title="删除">
                            <DeleteOutlined
                              onClick={() => onHandleActions(item.groupKey, 'delete')}
                            />
                          </Tooltip>
                          <Divider type="vertical" style={{ margin: '0 4px' }} />
                          <Tooltip key="up" title="向上移">
                            <ArrowUpOutlined onClick={() => onHandleActions(item.groupKey, 'up')} />
                          </Tooltip>
                          <Divider type="vertical" style={{ margin: '0 4px' }} />
                          <Tooltip key="down" title="向下移">
                            <ArrowDownOutlined
                              onClick={() => onHandleActions(item.groupKey, 'down')}
                            />
                          </Tooltip>
                        </>
                      ) : (
                        <Tooltip key="delete" title="删除">
                          <DeleteOutlined
                            onClick={() => onHandleActions(item.groupKey, 'delete')}
                          />
                        </Tooltip>
                      )}
                    </div>
                  </div>
                </Card>
              );
            })}
          </Form>
        </Card>
        <TestMutator form={form} />
        <FooterToolBar>
          <Button
            type="primary"
            style={{ marginRight: 24 }}
            loading={loading}
            onClick={() => {
              onFinish();
            }}
          >
            {id ? '保存' : '提交'}
          </Button>
          <Button
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </FooterToolBar>
      </Space>
    </LayoutContent>
  );
}

function generateCyclesParams({
  periodUnit,
  allowTriggerTime,
  period,
  isRemove,
  days,
  periodMonthWhen,
  sortNum,
  atDayType,
  periodYearWhen,
  months,
}: {
  periodUnit: CyclesType;
  allowTriggerTime: moment.MomentInput[];
  period: number;
  isRemove: number;
  days: string[] | WeekType[] | null;
  periodMonthWhen: string;
  sortNum: number;
  atDayType: MonthlyWorkDayType;
  periodYearWhen: string;
  months: string[];
}) {
  const cycles = {} as Cycle;
  switch (periodUnit) {
    case CyclesType.None:
      cycles.periodUnit = periodUnit;
      cycles.period = 0;
      cycles.dayConfig = {
        days: allowTriggerTime.map(time => moment(time).format('YYYY-MM-DD HH:mm:ss')),
        isRemove: 0,
      };

      break;
    case CyclesType.Day:
      cycles.periodUnit = periodUnit;
      cycles.period = period;
      cycles.dayConfig = {
        days: null,
        isRemove: isRemove,
      };

      break;
    case CyclesType.Week:
      cycles.periodUnit = periodUnit;
      cycles.period = period;
      cycles.dayConfig = {
        days: days,
        isRemove: isRemove,
      };

      break;
    case CyclesType.Month:
      cycles.periodUnit = periodUnit;
      cycles.period = period;

      if (periodMonthWhen) {
        cycles.dayConfig = {
          days: days,
          isRemove: isRemove,
        };
      } else {
        cycles.atDayConfig = [
          {
            month: null,
            sortNum: sortNum,
            atDayType: atDayType,
            isLast: 0,
          },
        ];
      }
      break;
    case CyclesType.Year:
      cycles.periodUnit = periodUnit;
      cycles.period = period;

      if (periodYearWhen) {
        cycles.dayConfig = {
          days: days,
          isRemove: isRemove,
        };
      } else {
        cycles.atDayConfig = months.map(month => ({
          month: month,
          sortNum: sortNum,
          atDayType: atDayType,
          isLast: 0,
        }));
      }
      break;
  }

  return cycles;
}

function RemoveWorkDays({
  value,
  onChange,
}: {
  value?: number;
  onChange?: (value: number) => void;
}) {
  return (
    <Space>
      <div>节假日排期限定</div>
      <Radio.Group
        value={value}
        options={[
          { label: '不限定', value: 0 },
          { label: '节假日不排期', value: 1 },
          { label: '顺延至工作日', value: 2 },
        ]}
        onChange={value => {
          onChange && onChange(value as unknown as number);
        }}
      />
    </Space>
  );
}

function handleEndDisabledDate(current: Moment) {
  return current.valueOf() < moment().startOf('day').valueOf();
}

function range(start: number, end: number) {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
}

function disabledDateTime(current: Moment | null): {
  disabledHours: () => number[];
  disabledMinutes: () => number[];
  disabledSeconds: () => number[];
} {
  const hours = moment().hours();
  const minute = moment().minute();
  const choseHour = moment(current).hours();
  const isToday = moment(current).isSame(moment(), 'day');
  if (isToday) {
    if (choseHour === hours) {
      return {
        disabledHours: () => range(0, hours),
        disabledMinutes: () => range(0, minute),
        disabledSeconds: () => [],
      };
    }
    return {
      disabledHours: () => range(0, hours),
      disabledMinutes: () => [],
      disabledSeconds: () => [],
    };
  }
  return {
    disabledHours: () => [],
    disabledMinutes: () => [],
    disabledSeconds: () => [],
  };
}

function generateCyclesRelateData(cycles?: Cycle, periodUnit?: CyclesType) {
  if (!cycles) {
    return {};
  }
  switch (periodUnit) {
    case CyclesType.None:
      return { period: 0, isRemove: 0 };
    case CyclesType.Day:
      return { period: cycles.period, isRemove: cycles.dayConfig.isRemove };
    case CyclesType.Week:
      return {
        period: cycles.period,
        isRemove: cycles.dayConfig.isRemove,
        days: cycles.dayConfig.days,
      };
    case CyclesType.Month:
      if (cycles.dayConfig) {
        return {
          period: cycles.period,
          isRemove: cycles.dayConfig.isRemove,
          days: cycles.dayConfig.days,
          periodMonthWhen: true,
        };
      } else {
        return {
          period: cycles.period,
          atDayType: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0].atDayType : null,
          sortNum: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0]?.sortNum : 0,
          periodMonthWhen: false,
          isRemove: 0,
        };
      }
    case CyclesType.Year:
      if (cycles.dayConfig) {
        return {
          period: cycles.period,
          isRemove: cycles.dayConfig.isRemove,
          days: cycles.dayConfig.days,
          periodYearWhen: true,
        };
      } else {
        return {
          months: cycles.atDayConfig?.map(item => item.month),
          period: cycles.period,
          atDayType: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0].atDayType : null,
          sortNum: Array.isArray(cycles.atDayConfig) ? cycles.atDayConfig[0].sortNum : 0,
          periodYearWhen: false,
          isRemove: 0,
        };
      }
    default:
      return {};
  }
}
