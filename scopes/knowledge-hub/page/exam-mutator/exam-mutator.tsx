import React from 'react';
import { useHistory, useLocation, useParams } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';

import { generateExamRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { ExamMutatorRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchExamWeb as fetchExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exam';
import { BackendExamType } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { mutateExamsWeb as mutateExam } from '@manyun/knowledge-hub.service.dcexam.mutate-exams';
import {
  ExamMutator as ExamMutatorUi,
  defaultInitialValues,
} from '@manyun/knowledge-hub.ui.exam-mutator';
import type { ExamMutatorFormValues } from '@manyun/knowledge-hub.ui.exam-mutator';

export type ExamMutatorProps = {};

type ExamExtraInfos = {
  defaultTimeLimitMax?: number;
};

// eslint-disable-next-line no-empty-pattern
export function ExamMutator({}: ExamMutatorProps) {
  const history = useHistory();
  const { id } = useParams<ExamMutatorRouteParams>();
  const { search } = useLocation<undefined>();

  // TODO @Jerry extract the logic code into a util component
  const searchParams = new URLSearchParams(search);
  const paperId = searchParams.get('paperId');
  const templateId = searchParams.get('templateId');

  const [form] = Form.useForm<ExamMutatorFormValues>();
  // 获取 `id` 对应的考试详情信息中
  const [loading, setLoading] = React.useState(true);
  // 缓存 `id` 对应的考试详情信息
  const examRef = React.useRef<ExamMutatorFormValues | undefined>(undefined);
  const examExtraInfosRef = React.useRef<ExamExtraInfos>({});
  // 创建/修改 中
  const [submitting, setSubmitting] = React.useState(false);

  const updateExamById = (id: number) => {
    fetchExam({ examId: id }).then(({ error, data }) => {
      if (error || !data) {
        message.error(error?.message);
        return;
      }
      const now = Date.now();
      const isTimeRangeValid = data.timeRange[0] > now;
      if (!isTimeRangeValid) {
        message.warning('被复制的考试的考试时间小于当前时间，请重新设定！');
      }
      const startedAtMoment = moment(data.timeRange[0]);
      const endedAtMoment = moment(data.timeRange[1]);
      const diffMinutes = endedAtMoment.diff(startedAtMoment, 'minutes');
      examRef.current = {
        type: data.type,
        name: data.name,
        categoryCode: [data.categoryCode],
        timeRange: isTimeRangeValid ? [startedAtMoment, endedAtMoment] : undefined,
        paperId: data.paperId!,
        timeLimit: data.timeLimit,
        passingScore: data.passingScore,
        users: {
          requiredUsers: {
            roleKeys: data.requiredRoleIds,
            userKeys: data.requiredUserIds,
          },
          optionalUsers: {
            roleKeys: data.optionalRoleIds,
            userKeys: data.optionalUserIds,
          },
        },
        markUsers: {
          roleKeys: data.markRoleIds,
          userKeys: data.markUserIds,
        },
        allowReviewPaperAfterHandedIn: data.allowReviewPaperAfterHandedIn,
        useSameMasterPaper: data.useSameMasterPaper,
      };
      examExtraInfosRef.current.defaultTimeLimitMax = diffMinutes;
      setLoading(false);
    });
  };

  React.useEffect(() => {
    if (id === undefined) {
      if (templateId !== '' && templateId !== null && !Number.isNaN(Number(templateId))) {
        updateExamById(Number(templateId));
      } else {
        const _paperId = Number(paperId);
        if (paperId !== '' && paperId !== null && !Number.isNaN(_paperId)) {
          examRef.current = {
            paperId: _paperId,
            ...defaultInitialValues,
          };
        }
        setLoading(false);
      }
    } else {
      updateExamById(Number(id));
    }
  }, [id, paperId, templateId]);

  if (loading) {
    return <Spin spinning />;
  }

  return (
    <Space
      style={{ width: '100%', display: 'flex', marginBottom: '30px' }}
      direction="vertical"
      size="large"
    >
      <ExamMutatorUi
        categoryCodeTrigger={id === undefined && templateId == null ? 'onFocus' : 'onDidMount'}
        form={form}
        initialValues={examRef.current}
        defaultTimeLimitMax={examExtraInfosRef.current.defaultTimeLimitMax}
      />

      <FooterToolBar>
        <Space style={{ width: '100%', justifyContent: 'center' }}>
          <Button
            type="primary"
            loading={submitting}
            onClick={async () => {
              const values = await form
                .validateFields()
                .then()
                .catch(err => {
                  form.scrollToField(err.errorFields[0].name.toString(), {
                    behavior: actions => {
                      actions.forEach(action => {
                        action.el.scrollTop = action.top - 55;
                      });
                    },
                  });
                });

              if (values) {
                setSubmitting(true);
                const { error, data: newExamId } = await mutateExam({
                  id: id !== undefined ? Number(id) : undefined,
                  type: examRef.current?.type || BackendExamType.FORMAL,
                  name: values.name!,
                  categoryCode: values.categoryCode![2],
                  timeRange: values.timeRange!.map(m => m.clone().seconds(0).valueOf()) as [
                    number,
                    number,
                  ],
                  paperId: values.paperId!,
                  timeLimit: values.timeLimit!,
                  passingScore: values.passingScore!,
                  requiredRoleIds: values.users!.requiredUsers?.roleKeys,
                  requiredUserIds: values.users!.requiredUsers?.userKeys,
                  optionalRoleIds: values.users!.optionalUsers?.roleKeys,
                  optionalUserIds: values.users!.optionalUsers?.userKeys,
                  markRoleIds: values.markUsers?.roleKeys,
                  markUserIds: values.markUsers?.userKeys,
                  scopes: values.scopes!,
                  allowReviewPaperAfterHandedIn: values.allowReviewPaperAfterHandedIn!,
                  useSameMasterPaper: values.useSameMasterPaper!,
                });
                if (error) {
                  message.error(error.message);
                } else {
                  history.push(generateExamRoutePath(id !== undefined ? Number(id) : newExamId));
                }
                // 注意这里取消 loading 的时间，假设创建成功了也不立即取消
                // 以防止用户重复点击提交按钮
                setSubmitting(false);
              }
            }}
          >
            提交
          </Button>
          <Button
            disabled={submitting}
            onClick={() => {
              history.goBack();
            }}
          >
            返回
          </Button>
        </Space>
      </FooterToolBar>
    </Space>
  );
}
