import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EDIT_SPECIFIC_EXAM_ROUTE_PATH,
  EXAM_ROUTE_PATH,
  NEW_EXAM_ROUTE_PATH,
  generateEditSpecificExamRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { ExamMutator } from './exam-mutator';

const SpecificExamMock = () => {
  const history = useHistory();

  return (
    <>
      <span>SPECIFIC EXAM</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </>
  );
};

export const NewExam = () => {
  const [initialized, setInitialized] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    setInitialized(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[NEW_EXAM_ROUTE_PATH]}>
          <Switch>
            <Route path={NEW_EXAM_ROUTE_PATH} exact>
              <ExamMutator />
            </Route>
            <Route path={EXAM_ROUTE_PATH} exact>
              <SpecificExamMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

export const EditSpecificExam = () => {
  const [initialized, setInitialized] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    setInitialized(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateEditSpecificExamRoutePath(41)]}>
          <Switch>
            <Route path={EDIT_SPECIFIC_EXAM_ROUTE_PATH} exact>
              <ExamMutator />
            </Route>
            <Route path={EXAM_ROUTE_PATH} exact>
              <SpecificExamMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
