import React from 'react';

import { List } from '@manyun/base-ui.ui.list';
import { message } from '@manyun/base-ui.ui.message';
import type { QuestionsGroup } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { mutateExamPaperWeb as mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper';
import type { QuestionMode } from '@manyun/knowledge-hub.ui.question';
import { Question } from '@manyun/knowledge-hub.ui.question';

import type { ExamPaperState } from './exam-paper-answer-sheet';
import { ExamQuestionUtil } from './exam-paper-util';
import { QuestionMarking } from './question-marking';

export type ExamQuestionsGroupListProps = {
  /** exam questions group ID */
  id: string;
  index: number;
  examPaperId: number;
  examPaperState: ExamPaperState;
  questionIds: number[];
  questionEntities: Record<number, BackendSectionQuestion>;
  questionsGroupsRef: React.MutableRefObject<QuestionsGroup[] | undefined>;
  markStatesRef: React.MutableRefObject<Record<string, boolean | undefined>>;
  forceUpdate: React.DispatchWithoutAction;
  updateUserScore(amount: number, prevAmount: number): void;
  scoreOrRankShowBlank: () => boolean;
  mark: boolean;
};

export function ExamQuestionsGroupList({
  id,
  examPaperId,
  examPaperState,
  questionIds,
  index,
  questionEntities,
  questionsGroupsRef,
  markStatesRef,
  forceUpdate,
  updateUserScore,
  scoreOrRankShowBlank,
  mark,
}: ExamQuestionsGroupListProps) {
  const mode = getQuestionMode(examPaperState);
  const allowMarking = mode === 'marking';
  const showMarkResult = examPaperState === 'reviewing';

  return (
    <List
      bordered={false}
      dataSource={questionIds}
      renderItem={questionId => {
        const question = questionEntities[questionId];
        const examQuestionUtil = new ExamQuestionUtil(question, id);

        let action: React.ReactNode = null;
        if (allowMarking || showMarkResult) {
          action = (
            <QuestionMarking
              mark={mark}
              examPaperState={examPaperState}
              localMarked={showMarkResult || !!markStatesRef.current[examQuestionUtil.uid]}
              question={question}
              scoreOrRankShowBlank={scoreOrRankShowBlank}
              onCompleted={(result, _question) => {
                const _examQuestionUtil = new ExamQuestionUtil(_question, id);
                markStatesRef.current[_examQuestionUtil.uid] = result;
                const prevUserScore =
                  questionsGroupsRef.current![index].questionEntities[_question.id].userGrade;
                questionsGroupsRef.current![index].questionEntities[_question.id] = _question;
                updateUserScore(_question.userGrade, prevUserScore);
              }}
            />
          );
        }

        return (
          <List.Item key={question.questionOrder} actions={[action]}>
            <Question
              examPaperId={examPaperId} /***考卷id */
              id={examQuestionUtil.unumber}
              question={question}
              index={question.questionOrder}
              mode={mode}
              onChange={async value => {
                const answers = Array.isArray(value) ? value : [value];
                const { error } = await mutateExamPaper('answering', {
                  id: examPaperId,
                  questionId: question.id,
                  answers,
                });
                if (error) {
                  message.error(error.message);
                  return;
                }
                const idx = questionsGroupsRef.current!.findIndex(q =>
                  q.questionIds.includes(question.id)
                );
                questionsGroupsRef.current![idx].questionEntities[question.id].userAnswers =
                  answers;
                forceUpdate();
                // Note: don't show question result saved messages.
                // see http://chandao.manyun-local.com/zentao/bug-view-3926.html
              }}
            />
          </List.Item>
        );
      }}
    />
  );
}

function getQuestionMode(examPaperState: ExamPaperState): QuestionMode {
  if (examPaperState === 'answering') {
    return 'answer';
  } else if (examPaperState === 'marking') {
    return 'marking';
  } else if (examPaperState === 'reviewing') {
    return 'finished';
  } else {
    return 'preview';
  }
}
