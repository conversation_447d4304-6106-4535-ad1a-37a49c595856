import React from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

import type {
  BackendExamPaperQuestion,
  QuestionsGroup,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';

import styles from './answer-sheet.module.less';
import { ExamQuestionUtil } from './exam-paper-util';
import { QuestionNumber, QuestionNumberLegend } from './question-number';
import type { QuestionNumberState } from './question-number';

// prettier-ignore
export type ExamPaperState = /* 作答中 */ 'answering' | /* 判卷中 */ 'marking' | /* 复盘中 */ 'reviewing';

export type ExamPaperAnswerSheetProps = {
  /** 是否系统自动判卷 */
  autoMark: boolean;
  state: ExamPaperState;
  questionsGroups: QuestionsGroup[];
};

export function ExamPaperAnswerSheet({
  autoMark,
  state,
  questionsGroups,
}: ExamPaperAnswerSheetProps) {
  return (
    <div className={styles.container}>
      <div>
        {questionsGroups.map(
          ({ id, name, questionSize, questionScore, questionIds, questionEntities }, idx) => (
            <React.Fragment key={name}>
              {idx !== 0 && <Divider />}
              <Space style={{ width: '100%' }} direction="vertical">
                <div>
                  {name}（共 {questionSize} 题，合计 {questionScore} 分）
                </div>
                <div>
                  {questionIds.map(questionId => {
                    const question = questionEntities[questionId];
                    const examQuestionUtil = new ExamQuestionUtil(question, id);

                    return (
                      <QuestionNumber
                        key={question.questionOrder}
                        state={getQuestionState(state, question)}
                        number={question.questionOrder}
                        onClick={() => {
                          document
                            .querySelector(`#${examQuestionUtil.unumber}`)
                            ?.scrollIntoView({ behavior: 'smooth' });
                        }}
                      />
                    );
                  })}
                </div>
              </Space>
            </React.Fragment>
          )
        )}
      </div>
      <Space className={styles.legendsContainer}>
        {state === 'answering' && (
          <>
            <QuestionNumberLegend state="unanswered" />
            <QuestionNumberLegend state="answered" />
          </>
        )}
        {(state === 'marking' || state === 'reviewing') && (
          <>
            {!autoMark && <QuestionNumberLegend state="pendingMark" />}
            <QuestionNumberLegend state="success" />
            <QuestionNumberLegend state="error" />
          </>
        )}
      </Space>
    </div>
  );
}

function getQuestionState(
  examPaperState: ExamPaperState,
  question: BackendExamPaperQuestion
): QuestionNumberState {
  if (examPaperState === 'answering') {
    if (!question.userAnswers || question.userAnswers.length === 0) {
      return 'unanswered';
    } else {
      return 'answered';
    }
  } else if (examPaperState === 'marking' || examPaperState === 'reviewing') {
    if (question.correct === null) {
      return 'pendingMark';
    } else if (question.correct === true) {
      return 'success';
    } else {
      return 'error';
    }
  } else {
    // FIXME: @Jerry
    // Is this an appropriate default value?
    return 'unanswered';
  }
}
