import type { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export class ExamQuestionUtil {
  static DELIMITER = '_$$_';

  private entity: BackendSectionQuestion;
  private sectionId: string | number;

  constructor(examQuestion: BackendSectionQuestion, examSectionId: string | number) {
    this.entity = examQuestion;
    this.sectionId = examSectionId;
  }

  get uid() {
    return `${this.sectionId}${ExamQuestionUtil.DELIMITER}${this.entity.id}`;
  }

  get unumber() {
    return `exam-question-${this.sectionId}-${this.entity.questionOrder}`;
  }
}
