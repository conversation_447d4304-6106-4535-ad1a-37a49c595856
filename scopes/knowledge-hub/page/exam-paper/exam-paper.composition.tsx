import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory, useParams } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { LayoutProvider } from '@manyun/dc-brain.context.layout';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EXAM_PAPER_RESULT_PAGE_URL,
  EXAM_PAPER_URL,
  generateExamPaperLocation,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { ExamPaperResultRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
// import { registerWebMocks as registerFetchExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
// import { registerWebMocks as registerMutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper';
// import { getMock } from '@manyun/service.request';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { ExamPaper } from './exam-paper';

const ExamPaperResultMock = () => {
  const history = useHistory();
  const { examId, id } = useParams<ExamPaperResultRouteParams>();

  return (
    <div>
      <span>examId: {examId}</span>
      <span>examPaperId: {id}</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </div>
  );
};

export const BasicExamPaper = () => {
  const [initialized, setInitialized] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchExamPaper(mock);
    // registerMutateExamPaper(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    setInitialized(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateExamPaperLocation({ examId: 41, id: 2098 })]}>
          <Switch>
            <Route path={EXAM_PAPER_RESULT_PAGE_URL}>
              <ExamPaperResultMock />
            </Route>
            <Route path={EXAM_PAPER_URL}>
              <LayoutProvider>
                <div style={{ height: window.innerHeight - 120 }}>
                  <ExamPaper />
                </div>
              </LayoutProvider>
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
