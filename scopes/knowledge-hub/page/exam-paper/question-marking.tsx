import { CheckOutlined, EditOutlined } from '@ant-design/icons';
import CheckCircleFilled from '@ant-design/icons/es/icons/CheckCircleFilled';
import CloseCircleFilled from '@ant-design/icons/es/icons/CloseCircleFilled';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { BackendQuestionType } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

import type { ExamPaperState } from './exam-paper-answer-sheet';

export type QuestionMarkingProps = {
  mark: boolean;
  localMarked: boolean;
  question: BackendSectionQuestion;
  examPaperState: ExamPaperState;
  onCompleted(result: boolean, question: BackendSectionQuestion): void;
  scoreOrRankShowBlank: () => boolean;
};

export function QuestionMarking({
  mark,
  localMarked,
  question,
  examPaperState,
  onCompleted,
  scoreOrRankShowBlank,
}: QuestionMarkingProps) {
  const [userScore, setUserScore] = React.useState<number>(question.userGrade);
  const userScoreChangedRef = React.useRef(question.correct !== null);
  const allowMarking = question.autoGrade === false && localMarked === false;
  const [form] = Form.useForm();

  const markIt = async (result: boolean, score: number, isConfirmed: boolean) => {
    const _userScore = userScoreChangedRef.current ? userScore : score;

    if (result === true && _userScore === 0) {
      message.error(`判题正确后，给分只能设置满分。`);
      return;
    }

    // see http://chandao.manyun-local.com/zentao/bug-view-3998.html
    if (result === false && _userScore === question.grade) {
      message.error(`判题错误后，给分需小于${question.grade}分。`);
      return;
    }

    onCompleted(true, {
      ...question,
      correct: result,
      userGrade: _userScore,
      isConfirmed,
    });
  };

  const markScore = () => {
    if (mark && !question?.isConfirmed) {
      return 'error';
    }

    if (!userScore && userScore != 0) {
      return 'error';
    }

    return '';
  };

  return (
    <Space direction="vertical" align="start">
      <Form form={form} name="useScore" layout="vertical" autoComplete="off">
        <Space>
          <label>得分：</label>
          {allowMarking ? (
            <>
              <Form.Item
                name="score"
                initialValue={userScore}
                validateStatus={markScore()}
                style={{ marginBottom: 0 }}
              >
                <InputNumber
                  style={{ width: 90 }}
                  min={0}
                  max={question.grade}
                  precision={1}
                  value={userScore}
                  onChange={value => {
                    userScoreChangedRef.current = true;
                    setUserScore(value);
                  }}
                />
              </Form.Item>
              分
              {(question.type === BackendQuestionType.ESSAY ||
                question.type === BackendQuestionType.SHORT_ANSWER) && (
                <Button
                  compact
                  type="link"
                  onClick={() => {
                    if (!userScore && userScore !== 0) {
                      return;
                    }

                    markIt(question.grade === userScore, userScore, true);
                  }}
                >
                  <CheckOutlined />
                </Button>
              )}
            </>
          ) : (
            <>
              <span>{scoreOrRankShowBlank() ? '--' : question.userGrade + '分'}</span>
              {examPaperState === 'marking' &&
                question.autoGrade === false &&
                (question.type === BackendQuestionType.ESSAY ||
                  question.type === BackendQuestionType.SHORT_ANSWER) && (
                  <EditOutlined
                    onClick={() => {
                      onCompleted(false, {
                        ...question,
                        correct: null,
                        userGrade: 0,
                        isConfirmed: false,
                      });
                    }}
                  />
                )}
            </>
          )}
        </Space>
      </Form>
      {allowMarking &&
        question.type !== BackendQuestionType.ESSAY &&
        question.type !== BackendQuestionType.SHORT_ANSWER && (
          <Space>
            <label>判题：</label>
            <span>
              <Button
                style={{
                  color: 'var(--manyun-success-color)',
                }}
                compact
                type="text"
                icon={<CheckCircleFilled />}
                onClick={() => {
                  if (!userScoreChangedRef.current) {
                    setUserScore(question.grade);
                  }
                  markIt(true, question.grade, false);
                }}
              />
              <Button
                style={{
                  color: 'var(--manyun-error-color)',
                }}
                compact
                type="text"
                icon={<CloseCircleFilled color="#ff4d4f" />}
                onClick={() => {
                  if (!userScoreChangedRef.current) {
                    setUserScore(0);
                  }
                  markIt(false, 0, false);
                }}
              />
            </span>
          </Space>
        )}
      <Space>
        <label>满分：</label>
        <span>{question.grade}分</span>
      </Space>
    </Space>
  );
}
