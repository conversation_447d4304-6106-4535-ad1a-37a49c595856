import moment from 'moment';
import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Result } from '@manyun/base-ui.ui.result';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Switch } from '@manyun/base-ui.ui.switch';
import type { Breadcrumb } from '@manyun/dc-brain.context.layout';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import {
  generateExamPaperResultLocation,
  generateTestPaperResultRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { ExamPaperRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { MutateCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { fetchCourseWeb as fetchCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { fetchExamWeb as fetchExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exam';
import { fetchExamPaperWeb as fetchExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type {
  ExamPaper as ExamPaperModel,
  QuestionsGroup,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type { Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import type { MarkedQuestion } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper';
import { mutateExamPaperWeb as mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper';

import { ExamPaperAnswerSheet } from './exam-paper-answer-sheet';
import type { ExamPaperState } from './exam-paper-answer-sheet';
import styles from './exam-paper.module.less';
import { ExamQuestionsGroupList } from './exam-questions-group-list';

export function ExamPaper() {
  const history = useHistory();
  const { courseId, examId, id } = useParams<ExamPaperRouteParams>();
  const goToResultPage = React.useCallback(() => {
    if (courseId === undefined) {
      history.push(generateExamPaperResultLocation({ id, examId }));
    } else {
      history.push(
        generateTestPaperResultRoutePath({
          courseId,
          examId,
          id,
        })
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id, examId, courseId]);

  const [loading, setLoading] = React.useState(true);
  const examRef = React.useRef<Exam | null>(null);
  const [examPaper, setExamPaper] = React.useState<ExamPaperModel | null>(null);
  const [, forceUpdate] = React.useReducer<(c: number) => number>(c => c + 1, 0);
  const questionsGroupsRef = React.useRef<QuestionsGroup[]>();
  /** See http://chandao.manyun-local.com/zentao/bug-view-3999.html */
  const markStatesRef = React.useRef<Record<string, boolean | undefined>>({});
  const [showManuallyMarkingOnly, setShowManuallyMarkingOnly] = React.useState(false);
  const [showFailedOnly, setShowFailedOnly] = React.useState(false);
  const [, { checkUserId }] = useAuthorized();
  const [course, setCourse] = React.useState<MutateCourse | null>(null);
  const [mark, setMark] = React.useState<boolean>(false); //是否应该出现确认提示

  React.useEffect(() => {
    setLoading(true);
    fetchExam({ examId: Number(examId) }).then(async examResp => {
      if (examResp.error) {
        message.error(examResp.error.message);
        return;
      }
      examRef.current = examResp.data;

      await fetchExamPaper({ examPaperId: Number(id) }).then(({ error, data }) => {
        if (error) {
          message.error(error.message);
          return;
        }
        questionsGroupsRef.current = data!.questionsGroups;
        setExamPaper(data);
        setLoading(false);
      });

      if (courseId !== undefined) {
        const { error, data } = await fetchCourse({ courseId: Number(courseId) });
        if (error) {
          message.error(error.message);
        } else {
          setCourse(data);
        }
      }
    });
  }, [courseId, examId, id, checkUserId]);

  if (loading) {
    return <Skeleton active />;
  }

  if (!examRef.current || !examPaper) {
    return <Result status="404" title="404" subTitle="抱歉，此考卷不存在！" />;
  }

  /** 当前登录用户是否是当前考卷的考生 */
  const isExaminee = checkUserId(examPaper.userId!);
  const isMarker = examRef.current.allowMark;

  const examPaperState = getExamPaperState(
    examPaper.state,
    examPaper.autoMark /**是否是系统判卷 See http://chandao.manyun-local.com/zentao/bug-view-4636.html */,
    examRef.current!.timeRange[1],
    isExaminee,
    isMarker
  );

  if (
    (!isExaminee && !isMarker) ||
    (!isExaminee && isMarker && examPaperState === 'answering') ||
    (examPaperState !== 'answering' &&
      isExaminee &&
      !examRef.current?.allowReviewPaperAfterHandedIn)
  ) {
    return <Result status="403" title="403" subTitle="抱歉，你无权查看此考卷！" />;
  }

  // 每次渲染都重新计算一遍
  const { unansweredCount, unmarkedCount, total } = calculateQuestionsCount(
    questionsGroupsRef.current!
  );
  const hasUnansweredQuestions = unansweredCount > 0;
  const hasUnmarkedQuestions = unmarkedCount > 0;

  const scoreOrRankShowBlank = () =>
    examPaper.state === BackendExamPaperStatus.WAIT_GRADE && isExaminee;

  return (
    <LayoutContent
      pageCode="page_knowledge_hub-exam-paper"
      composeBreadcrumbs={(value: Breadcrumb[]) => {
        if (courseId !== undefined) {
          return [
            ...value,
            {
              key: 'course-name',
              text: course?.name,
            },
            {
              key: 'test',
              text: '随堂测验',
            },
          ];
        }
        return [...value, { key: 'examName', text: examRef?.current?.name }];
      }}
    >
      <div className={styles.container}>
        <Space className={styles.examPaperContainer} align="start" size="middle">
          <Card title="答题卡" bordered={false}>
            <ExamPaperAnswerSheet
              autoMark={examPaper.autoMark}
              state={examPaperState}
              questionsGroups={examPaper.questionsGroups}
            />
          </Card>
          <Card
            title="考试信息"
            bordered={false}
            extra={
              <ExamPaperUserInfos
                id={examPaper.userId!}
                examPaperId={examPaper.id!}
                examPaperState={examPaperState}
                unansweredQuestionsCount={unansweredCount}
                totalQuestionsCount={total}
                willForceHandInAt={moment(examPaper.startTime)
                  .add(examPaper.timeLimit * 60 * 1000)
                  .valueOf()}
                score={examPaper.userScore}
                totalScore={examPaper.totalScore}
                ranking={examPaper.userRanking}
                passingResult={examPaper.result}
                showManuallyMarkingOnly={showManuallyMarkingOnly}
                showFailedOnly={showFailedOnly}
                setShowManuallyMarkingOnly={setShowManuallyMarkingOnly}
                setShowFailedOnly={setShowFailedOnly}
                scoreOrRankShowBlank={scoreOrRankShowBlank}
                onHandedIn={goToResultPage}
              />
            }
          >
            <Collapse defaultActiveKey={examPaper.questionsGroups.map(({ name }) => name)}>
              {examPaper.questionsGroups.map(
                (
                  {
                    id: questionsGroupId,
                    name,
                    questionSize,
                    questionScore,
                    userScore,
                    questionIds,
                    visibleQuestionIds,
                    visibleErrorQuestionIds,
                  },
                  questionGroupIdx
                ) => (
                  <Collapse.Panel
                    key={name}
                    style={{ paddingTop: 0, paddingBottom: 0 }}
                    header={name}
                    extra={
                      <span>
                        该板块共 {questionSize} 题，满分：{questionScore}分，考生得分：{' '}
                        {scoreOrRankShowBlank() ? '--' : `${userScore}分`}
                      </span>
                    }
                  >
                    <ExamQuestionsGroupList
                      id={questionsGroupId}
                      examPaperId={Number(id)}
                      examPaperState={examPaperState}
                      index={questionGroupIdx}
                      questionIds={
                        showManuallyMarkingOnly
                          ? visibleQuestionIds
                          : showFailedOnly
                            ? visibleErrorQuestionIds
                            : questionIds
                      }
                      questionEntities={
                        questionsGroupsRef.current![questionGroupIdx].questionEntities
                      }
                      questionsGroupsRef={questionsGroupsRef}
                      markStatesRef={markStatesRef}
                      forceUpdate={forceUpdate}
                      updateUserScore={(amount, prevAmount) => {
                        setExamPaper(prev => ({
                          ...prev!,
                          userScore: (prev!.userScore || 0) + amount - prevAmount,
                        }));
                      }}
                      scoreOrRankShowBlank={scoreOrRankShowBlank}
                      mark={mark}
                    />
                  </Collapse.Panel>
                )
              )}
            </Collapse>
          </Card>
        </Space>
        <FooterToolBar>
          <Space className={styles.actionsContainer} align="center">
            {examPaperState === 'answering' && (
              <ExamPaperHandIn
                id={Number(id)}
                hasUnansweredQuestions={hasUnansweredQuestions}
                onHandedIn={goToResultPage}
              />
            )}
            {examPaperState === 'marking' && (
              <ExamPaperMarkingFinish
                id={Number(id)}
                hasUnmarkedQuestions={hasUnmarkedQuestions}
                questionsGroupsRef={questionsGroupsRef}
                questionsGroups={examPaper.questionsGroups}
                setMark={setMark}
              />
            )}
            {examPaperState === 'marking' && (
              <ExamPaperMarkingFinish
                id={Number(id)}
                status="save"
                hasUnmarkedQuestions={hasUnmarkedQuestions}
                questionsGroupsRef={questionsGroupsRef}
              />
            )}
            <Button
              onClick={() => {
                history.goBack();
              }}
            >
              返回
            </Button>
          </Space>
        </FooterToolBar>
      </div>
    </LayoutContent>
  );
}

type ExamPaperUserInfosProps = {
  id: number;
  examPaperId: number;
  examPaperState: ExamPaperState;
  unansweredQuestionsCount: number;
  totalQuestionsCount: number;
  /** 最后可答题的时间戳 */
  willForceHandInAt: number;
  score: number | null;
  totalScore: number;
  ranking: number | null;
  passingResult: boolean | null;
  showManuallyMarkingOnly: boolean;
  showFailedOnly: boolean;
  setShowManuallyMarkingOnly(checked: boolean): void;
  setShowFailedOnly(checked: boolean): void;
  onHandedIn(): void;
  scoreOrRankShowBlank: () => boolean;
};

const TIME_LIMIT_WARNING_MINITES = 5 * 60 * 1000;

function ExamPaperUserInfos({
  id,
  examPaperId,
  examPaperState,
  unansweredQuestionsCount,
  totalQuestionsCount,
  willForceHandInAt,
  score,
  totalScore,
  ranking,
  passingResult,
  showManuallyMarkingOnly,
  showFailedOnly,
  setShowManuallyMarkingOnly,
  setShowFailedOnly,
  onHandedIn,
  scoreOrRankShowBlank,
}: ExamPaperUserInfosProps) {
  const autoHandIn = React.useCallback(async () => {
    const modalInst = Modal.warning({
      closable: false,
      title: '答题时间已到，正在强制交卷中...',
      okButtonProps: {
        loading: true,
      },
      okText: '',
    });
    const { error } = await mutateExamPaper('handing-in', { id: examPaperId, force: true });
    if (error) {
      message.error(error.message);
      return;
    }
    window.setTimeout(() => {
      modalInst.destroy();
      onHandedIn();
    }, 1.5 * 1000);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [examPaperId]);

  const timeLimitWarning = React.useRef(false);
  const showTimeLimitWarning = React.useCallback(() => {
    if (timeLimitWarning.current) {
      return;
    }
    const modalInst = Modal.warning({
      title: '温馨提示',
      content: '答题时间不足5分钟，请尽快答题，否则系统将在时间到时自动交卷！',
    });
    // destroy the modal if user do nothing,
    // because if not, the modal always
    // exists when auto handed in
    window.setTimeout(() => {
      modalInst.destroy();
    }, 10 * 1000);
  }, []);

  React.useEffect(() => {
    if (examPaperState === 'answering') {
      if (
        willForceHandInAt > Date.now() &&
        willForceHandInAt - Date.now() <= TIME_LIMIT_WARNING_MINITES &&
        timeLimitWarning.current === false
      ) {
        showTimeLimitWarning();
      }
      if (willForceHandInAt <= Date.now()) {
        autoHandIn();
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space>
      <Space size={0}>
        <label>考生姓名：</label>
        <User.Link id={id} />
      </Space>
      {examPaperState === 'answering' && (
        <Space size={0}>
          <label>当前进度：</label>
          <Statistic
            valueStyle={{ fontSize: 14, color: 'var(--manyun-warning-color)' }}
            value={totalQuestionsCount - unansweredQuestionsCount}
            suffix={`/ ${totalQuestionsCount}`}
          />
        </Space>
      )}
      {examPaperState === 'answering' && (
        <Space size={0}>
          <label>剩余时间：</label>
          <Statistic.Countdown
            valueStyle={{ fontSize: 14, color: 'var(--manyun-error-color)' }}
            value={willForceHandInAt}
            onChange={value => {
              if (
                (value as number) - Date.now() <= TIME_LIMIT_WARNING_MINITES &&
                timeLimitWarning.current === false
              ) {
                timeLimitWarning.current = true;
                showTimeLimitWarning();
              }
            }}
            onFinish={autoHandIn}
          />
        </Space>
      )}
      {(examPaperState === 'marking' || examPaperState === 'reviewing') && (
        <>
          <Space size={0}>
            <label>考试成绩：</label>
            <Statistic
              valueStyle={{ fontSize: 14 }}
              value={scoreOrRankShowBlank() ? '--' : showScore(score)}
              suffix={`/ ${totalScore}分`}
            />
          </Space>
          {examPaperState === 'reviewing' && (
            <Space size={0}>
              <label>排名：</label>
              <span>{scoreOrRankShowBlank() ? '--' : ranking}</span>
            </Space>
          )}
          <Space size={0}>
            <label>结果：</label>
            {passingResult === null ? (
              <span>--</span>
            ) : passingResult === true ? (
              <span style={{ color: 'var(--manyun-success-color)' }}>通过</span>
            ) : (
              <span style={{ color: 'var(--manyun-error-color)' }}>未通过</span>
            )}
          </Space>
        </>
      )}
      {examPaperState === 'marking' && (
        <Space size={0}>
          <label>仅看人工判题：</label>
          <Switch
            size="small"
            checked={showManuallyMarkingOnly}
            onChange={checked => {
              setShowManuallyMarkingOnly(checked);
            }}
          />
        </Space>
      )}
      {examPaperState === 'reviewing' && (
        <Space size={0}>
          <label>仅看错题：</label>
          <Switch
            size="small"
            checked={showFailedOnly}
            onChange={checked => {
              setShowFailedOnly(checked);
            }}
          />
        </Space>
      )}
    </Space>
  );
}

type ExamPaperHandInProps = {
  id: number;
  /** 是否存在未答题，若是，则会使用 `Popconfirm` */
  hasUnansweredQuestions: boolean;
  onHandedIn(): void;
};

const noop = () => {};

function showScore(score: number | null) {
  if (score === null) {
    return undefined;
  }
  return score?.toString().includes('.') ? score.toFixed(1) : score;
}

function ExamPaperHandIn({ id, hasUnansweredQuestions, onHandedIn }: ExamPaperHandInProps) {
  const [submitting, setSubmitting] = React.useState(false);

  const submitHandler = async () => {
    setSubmitting(true);
    const { error } = await mutateExamPaper('handing-in', { id });
    if (error) {
      message.error(error.message);
      setSubmitting(false);
      return;
    }
    onHandedIn();
  };

  const btn = (
    <Button type="primary" loading={submitting} onClick={submitHandler}>
      交卷
    </Button>
  );

  if (!hasUnansweredQuestions) {
    return btn;
  }

  return (
    <Popconfirm title="你还有未作答的试题，确定要交卷吗？" onConfirm={submitHandler}>
      {React.cloneElement(btn, { onClick: noop })}
    </Popconfirm>
  );
}

type ExamPaperMarkingFinishProps = {
  id: number;
  status?: 'save' | 'submit';
  hasUnmarkedQuestions: boolean;
  questionsGroupsRef: React.MutableRefObject<QuestionsGroup[] | undefined>;
  setMark?: (val: boolean) => void;
};

function ExamPaperMarkingFinish({
  id,
  status = 'submit',
  hasUnmarkedQuestions,
  questionsGroupsRef,
  setMark,
}: ExamPaperMarkingFinishProps) {
  const history = useHistory();
  const [submitting, setSubmitting] = React.useState(false);

  const submitHandler = async () => {
    // 提交数据需要检测问答题和填空题是不是已经确认分值
    let isAlert = false;
    if (status !== 'save') {
      //板块
      const groupList = questionsGroupsRef.current;

      groupList?.forEach(({ questionEntities }: Record<string, any>) => {
        Object.values(questionEntities).forEach((item: any) => {
          if (
            item?.autoGrade === false &&
            (item?.type === 4 || item?.type === 5) &&
            !item?.isConfirmed
          ) {
            isAlert = true;

            return;
          }
        });
      });
    }

    if (isAlert && setMark) {
      message.warning('请先确认所有试题得分！');
      setMark(true);
      return;
    }

    setSubmitting(true);
    const markedQuestions: MarkedQuestion[] = [];
    questionsGroupsRef.current!.forEach(questionsGroup => {
      questionsGroup.questionIds.forEach(questionId => {
        const question = questionsGroup.questionEntities[questionId];
        if (!question.autoGrade && typeof question.correct == 'boolean') {
          markedQuestions.push({
            id: question.id,
            score: question.userGrade,
            result: question.correct,
          });
        }
      });
    });
    if (status === 'save') {
      const { error } = await mutateExamPaper('save-marking', {
        id,
        markedQuestions,
      });
      setSubmitting(false);
      if (error) {
        message.error(error.message);
        setSubmitting(false);
      }
      message.success('保存成功！');
      return;
    }
    const { error } = await mutateExamPaper('marking', {
      id,
      markedQuestions,
    });
    if (error) {
      message.error(error.message);
      setSubmitting(false);
      return;
    }
    history.goBack();
  };

  const btn = (
    <Button type="primary" loading={submitting} onClick={submitHandler}>
      {status === 'save' ? '保存' : '提交'}
    </Button>
  );

  if (!hasUnmarkedQuestions || status !== 'save') {
    return btn;
  }

  return (
    <Popconfirm title={`还有未判的试题，确定要保存吗？`} onConfirm={submitHandler}>
      {React.cloneElement(btn, { onClick: noop })}
    </Popconfirm>
  );
}

function getExamPaperState(
  examPaperState: BackendExamPaperStatus,
  autoGrade: boolean,
  examEndedAt: number,
  isExaminee: boolean,
  isMarker: boolean
): ExamPaperState {
  const isExamEnded = moment().isAfter(moment(examEndedAt));

  if (
    examPaperState === BackendExamPaperStatus.INIT ||
    examPaperState === BackendExamPaperStatus.PROCESS
  ) {
    return 'answering';
  } else if (
    !isExaminee &&
    isMarker &&
    !autoGrade &&
    (examPaperState === BackendExamPaperStatus.SUBMIT ||
      examPaperState === BackendExamPaperStatus.WAIT_GRADE ||
      // 这个状态表示可以重新判卷
      (examPaperState === BackendExamPaperStatus.GRADED && !isExamEnded))
  ) {
    return 'marking';
  } else {
    return 'reviewing';
  }
}

function calculateQuestionsCount(questionGroups: QuestionsGroup[]) {
  let unansweredCount = 0;
  let unmarkedCount = 0;
  let total = 0;

  questionGroups.forEach(questionsGroup =>
    questionsGroup.questionIds.forEach(questionId => {
      total++;

      const { userAnswers, correct } = questionsGroup.questionEntities[questionId];
      if (!userAnswers || userAnswers.length === 0) {
        unansweredCount++;
      }
      if (correct === null) {
        unmarkedCount++;
      }
    })
  );

  return {
    unansweredCount,
    unmarkedCount,
    total,
  };
}
