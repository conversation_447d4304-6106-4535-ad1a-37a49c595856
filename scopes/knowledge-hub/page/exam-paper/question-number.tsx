import React from 'react';

import classNames from 'classnames';

import { Tag } from '@manyun/base-ui.ui.tag';

import styles from './question-number.module.less';

export type QuestionNumberState =
  | /* 未作答 */ 'unanswered'
  | /* 已作答 */ 'answered'
  | /* 答错 */ 'error'
  | /* 答对 */ 'success'
  | /* 待批改 */ 'pendingMark';

export type QuestionNumberProps = {
  className?: string;
  state: QuestionNumberState;
  number: React.ReactText;
  onClick?: React.MouseEventHandler<HTMLSpanElement>;
};

const colorsMapper: Record<QuestionNumberProps['state'], string> = {
  unanswered: 'default',
  answered: 'processing',
  error: 'error',
  success: 'success',
  pendingMark: 'warning',
};

export function QuestionNumber({ className, state, number, onClick }: QuestionNumberProps) {
  return (
    <Tag
      className={classNames(
        className,
        styles.tag,
        typeof onClick == 'function' && styles.clickableTag
      )}
      color={colorsMapper[state]}
      onClick={onClick}
    >
      {number}
    </Tag>
  );
}

export type QuestionNumberLegendProps = {
  state: QuestionNumberState;
};

const questionNumberStateTextMapper: Record<QuestionNumberState, string> = {
  unanswered: '未答',
  answered: '已答',
  success: '正确',
  error: '错误',
  pendingMark: '待判卷',
};

export function QuestionNumberLegend({ state }: QuestionNumberLegendProps) {
  return (
    <span className={styles.legendContainer}>
      <QuestionNumber className={styles.legend} state={state} number="" />
      <span>{questionNumberStateTextMapper[state]}</span>
    </span>
  );
}
