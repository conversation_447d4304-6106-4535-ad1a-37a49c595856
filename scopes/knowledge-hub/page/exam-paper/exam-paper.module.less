.container {
  width: 100%;
  height: calc(var(--content-height) - 44px);

  > * + * {
    margin-top: 8px;
  }

  .examPaperContainer {
    width: 100%;
    height: 100%;
    display: flex;

    > :global(.manyun-space-item) {
      height: 100%;

      &:first-child {
        width: 330px;
      }

      &:last-child {
        flex: 1;
      }

      > * {
        height: 100%;

        > :global(.manyun-card-body) {
          position: relative;
          height: calc(100% - /* Card Head height */ 58px);
          overflow-y: auto;
        }
      }
    }
  }

  .actionsContainer {
    width: 100%;
    justify-content: center;
  }
}
