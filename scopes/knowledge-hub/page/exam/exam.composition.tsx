import React from 'react';
import { Route, MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EXAM_ROUTE_PATH,
  generateExamRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Exam } from './exam';

export const BasicExam = () => {
  const [initialized, setInitialized] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchExamMock(mock);
    // registerFetchExamPaperUsers(mock);
    // registerRoleSelectMocks(mock);
    // registerCategoryMocks(mock);
    // registerForceHandIn(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    setInitialized(true);
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateExamRoutePath(34)]}>
          <Route path={EXAM_ROUTE_PATH}>
            <Exam />
          </Route>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
