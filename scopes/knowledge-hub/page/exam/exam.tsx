import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Link, useHistory, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { User } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import type { SheetHeader } from '@manyun/base-ui.util.xlsx';
import type { Breadcrumb as LayoutBreadcrumb } from '@manyun/dc-brain.context.layout';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { generateExamPaperLocation } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { ExamRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { addExamUsers } from '@manyun/knowledge-hub.service.dcexam.add-exam-users';
import { fetchExamWeb as fetchExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exam';
import {
  BackendExamPaperStatus,
  ExamPaperStatusMapper,
  fetchExamPaperUsersWeb as fetchExamPaperUsers,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type {
  ExamPaperUser,
  ServiceQ as FetchExamPaperUsersServiceQ,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import type { Exam as SvsExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { mutateExamPaperWeb as forceHandIn } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper';
import { ExamsCategory } from '@manyun/knowledge-hub.ui.exams-category';
import { GroupUsersPicker } from '@manyun/knowledge-hub.ui.group-users-picker';
import type { Users } from '@manyun/knowledge-hub.ui.group-users-picker';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

const exportColumns: (ColumnType<ExamPaperUser> & SheetHeader)[] = [
  {
    title: '考生姓名',
    dataIndex: 'userName',
    fixed: 'left',
    ellipsis: true,
    render(userName: string, { userId }: ExamPaperUser) {
      return <User.Link id={userId} name={userName} />;
    },
  },
  {
    ellipsis: true,
    title: '考生角色',
    dataIndex: 'roles',
    render(roles: ExamPaperUser['roles']) {
      return generateExamPaperUsers(roles) || '--';
    },
    stringify: (roles: ExamPaperUser['roles']) => generateExamPaperUsers(roles),
  },
  {
    title: '类型',
    dataIndex: 'defaultUser',
    ellipsis: true,
    render(defaultUser: boolean) {
      return defaultUser ? '必考' : '选考';
    },
    stringify: (defaultUser: ExamPaperUser['defaultUser']) => (defaultUser ? '必考' : '选考'),
  },
  {
    ellipsis: true,
    title: '所属位置',
    dataIndex: 'idcs',
    render(idcs: ExamPaperUser['idcs']) {
      return generateExamPaperUsers(idcs) || '--';
    },
    stringify: (idcs: ExamPaperUser['idcs']) => generateExamPaperUsers(idcs),
  },
  {
    title: '分数',
    dataIndex: 'grade',
    render(grade: number | null) {
      return grade !== null ? grade : '--';
    },
  },
  {
    width: 100,
    title: '排名',
    dataIndex: 'order',
    render(order: number | null) {
      return order ? order : '--';
    },
  },
  {
    width: 100,
    title: '状态',
    dataIndex: 'status',
    render(status: BackendExamPaperStatus) {
      return generateExamPaperUsersStatus(status);
    },
    stringify: (status: BackendExamPaperStatus) => generateExamPaperUsersStatus(status),
  },
  {
    width: 100,
    ellipsis: true,
    title: '考试结果',
    dataIndex: 'pass',
    render(pass: boolean | null) {
      return generateExamPaperUsersPass(pass) || '--';
    },
    stringify: (pass: boolean | null) => generateExamPaperUsersPass(pass),
  },
  {
    ellipsis: true,
    title: '强制交卷',
    dataIndex: 'forceSubmit',
    render(forceSubmit: boolean) {
      return generateExamPaperUsersForceSubmit(forceSubmit) || '--';
    },
    stringify: (forceSubmit: boolean) => generateExamPaperUsersForceSubmit(forceSubmit),
  },
  {
    ellipsis: true,
    title: '开始时间',
    dataIndex: 'startTime',
    render(startTime: number | null) {
      // 这是考生开始考试的时间，应精确到秒
      return generateExamPaperUsersTime(startTime) || '--';
    },
    stringify: (startTime: number | null) => generateExamPaperUsersTime(startTime),
  },
  {
    ellipsis: true,
    title: '结束时间',
    dataIndex: 'endTime',
    render(endTime: number | null) {
      // 这是考生结束考试的时间，应精确到秒
      return generateExamPaperUsersTime(endTime) || '--';
    },
    stringify: (endTime: number | null) => generateExamPaperUsersTime(endTime),
  },
  {
    title: '答题时长',
    dataIndex: 'duration',
    ellipsis: true,
    render(_: unknown, { startTime, endTime }: ExamPaperUser) {
      return generateExamPaperUsersDuration(startTime, endTime) || '--';
    },
    stringify: (_: unknown, { startTime, endTime }: ExamPaperUser) =>
      generateExamPaperUsersDuration(startTime, endTime),
  },
  {
    title: '判卷人',
    dataIndex: 'correctUserName',
    render(correctUserName: string, { correctUserId }: ExamPaperUser) {
      return correctUserId !== null ? (
        <User.Link id={correctUserId} name={correctUserName || undefined} />
      ) : (
        '--'
      );
    },
    stringify: (correctUserName: string) =>
      correctUserName === 'SYSTEM' ? '系统' : correctUserName,
  },
];

export type ExamProps = {};

type UsersFormValues = {
  blockGuids?: string;
  user?: { key: string; label: string };
  roleIds?: number;
  userExamPaperState?: BackendExamPaperStatus;
  userExamPaperResult?: 'passed' | 'failed';
};

export function Exam(props: ExamProps) {
  const history = useHistory();
  const [, { checkUserId }] = useAuthorized() as any;
  const { id } = useParams<ExamRouteParams>();
  const [loading, setLoading] = useState(true);
  const examRef = useRef<SvsExam | null>(null);
  const [form] = Form.useForm<UsersFormValues>();
  const [usersLoading, setUsersLoading] = useState(false);
  const [examUsers, setExamUsers] = useState<Users>({
    requiredUsers: { userKeys: [], roleKeys: [] },
    optionalUsers: { userKeys: [], roleKeys: [] },
  });
  // 前端过滤后的考生信息
  const [users, setUsers] = useState<ExamPaperUser[]>([]);
  // 缓存第一次请求得到的所有考生信息
  // 用于前端过滤
  const usersRef = useRef<ExamPaperUser[]>([]);
  const searchResultRef = useRef<string[]>(['all']);
  const searchStatusRef = useRef<string[]>(['all']);
  const columns = exportColumns.map(column =>
    Object.fromEntries(Object.entries(column).filter(([key, value]) => key !== 'stringify'))
  );

  const [resultNum, setResultNum] = useState<Record<string, number>>({
    total: 0,
    pass: 0,
    noPass: 0,
  });
  const [statusNum, setStatusNum] = useState<Record<string, number>>({
    total: 0,
    init: 0,
    process: 0,
    submit: 0,
    waitGrade: 0,
    graded: 0,
  });

  const resultOptions = useMemo(
    () => [
      {
        label: `全部 (${resultNum.total})`,
        value: 'all',
      },
      {
        label: `通过 (${resultNum.pass})`,
        value: true,
      },
      {
        label: `未通过 (${resultNum.noPass})`,
        value: false,
      },
    ],
    [statusNum]
  );

  const statusOptions = useMemo(
    () => [
      {
        label: `全部 (${statusNum.total})`,
        value: 'all',
      },
      {
        label: `未考试 (${statusNum.init})`,
        value: BackendExamPaperStatus.INIT,
      },
      {
        label: `未交卷 (${statusNum.process})`,
        value: BackendExamPaperStatus.PROCESS,
      },
      {
        label: `已交卷 (${statusNum.submit})`,
        value: BackendExamPaperStatus.SUBMIT,
      },
      {
        label: `待判卷 (${statusNum.waitGrade})`,
        value: BackendExamPaperStatus.WAIT_GRADE,
      },
      {
        label: `已完成 (${statusNum.graded})`,
        value: BackendExamPaperStatus.GRADED,
      },
    ],
    [statusNum]
  );

  const getNum = (users: Record<string, any>) => {
    const total = users?.length || 0;
    const pass = users?.filter((item: Record<string, any>) => item?.pass === true).length || 0;
    const noPass = users?.filter((item: Record<string, any>) => item?.pass === false).length || 0;
    const init =
      users?.filter((item: Record<string, any>) => item?.status === BackendExamPaperStatus.INIT)
        .length || 0;
    const process =
      users?.filter((item: Record<string, any>) => item?.status === BackendExamPaperStatus.PROCESS)
        .length || 0;
    const submit =
      users?.filter((item: Record<string, any>) => item?.status === BackendExamPaperStatus.SUBMIT)
        .length || 0;
    const waitGrade =
      users?.filter(
        (item: Record<string, any>) => item?.status === BackendExamPaperStatus.WAIT_GRADE
      ).length || 0;
    const graded =
      users?.filter((item: Record<string, any>) => item?.status === BackendExamPaperStatus.GRADED)
        .length || 0;

    setResultNum({
      total,
      pass,
      noPass,
    });
    setStatusNum({
      total,
      init,
      process,
      submit,
      waitGrade,
      graded,
    });
  };

  const getUsers = useCallback(
    async (q?: FetchExamPaperUsersServiceQ) => {
      setUsersLoading(true);
      const { error, data } = await fetchExamPaperUsers({
        examId: Number(id),
        ...q,
      });
      setUsersLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      usersRef.current = data.data as any;
      setUsers(data.data as any);
      getNum(data.data);
    },
    [id]
  );

  const addStudents = useCallback(
    async (id: number) => {
      setUsersLoading(true);
      const { error } = await addExamUsers({
        id,
        defaultUserConfig: {
          users: examUsers.requiredUsers.userKeys,
          roles: examUsers.requiredUsers.roleKeys,
        },
        optionalUserConfig: {
          users: examUsers.optionalUsers.userKeys,
          roles: examUsers.optionalUsers.roleKeys,
        },
      });
      setUsersLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setExamUsers({
        requiredUsers: { userKeys: [], roleKeys: [] },
        optionalUsers: { userKeys: [], roleKeys: [] },
      });
      getUsers();
    },
    [examUsers, getUsers]
  );

  useEffect(() => {
    getUsers();
    fetchExam({ examId: Number(id) }).then(({ error, data }) => {
      if (error) {
        message.error(error.message);
        return;
      }
      examRef.current = data;
      setLoading(false);
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  if (loading || examRef.current === null) {
    return (
      <div style={{ width: '100%', height: '100%' }}>
        <Spin style={{ margin: '0 auto' }} spinning />
      </div>
    );
  }

  const getUsersByQ = () => {
    const values = form.getFieldsValue();
    const searchResult = searchResultRef.current;
    const searchStatus = searchStatusRef.current;

    let newUsers = usersRef.current;

    if (searchResult?.length || searchStatus?.length) {
      const newResult: any[] =
        (searchResult.includes('all') || !searchResult.length
          ? [true, false, null]
          : searchResult) || []; //或的关系
      const newStatus =
        (searchStatus.includes('all') || !searchStatus.length
          ? ['INIT', 'PROCESS', 'SUBMIT', 'WAIT_GRADE', 'GRADED']
          : searchStatus) || []; //或的关系

      newUsers = newUsers.filter(user => {
        return newResult.includes(user?.pass) && newStatus.includes(user.status);
      });
    }

    console.log(newUsers, 988888);

    const filters = {
      blockGuids: values.blockGuids,
      userId: values.user?.key ? Number(values.user.key) : undefined,
      roleCodes: values.roleIds,
    };
    const filteredUsers = newUsers.filter(examUser => {
      let matchBlockGuid = true;
      let matchUserId = true;
      let matchRoleCode = true;

      if (filters.blockGuids !== undefined) {
        matchBlockGuid = !!examUser.idcIds?.includes(filters.blockGuids);
      }

      if (filters.roleCodes !== undefined) {
        matchRoleCode = !!examUser.roleIds?.includes(filters.roleCodes);
      }

      if (filters.userId !== undefined) {
        matchUserId = examUser.userId === filters.userId;
      }

      return matchBlockGuid && matchUserId && matchRoleCode;
    });
    setUsers(filteredUsers);
    getNum(filteredUsers);
  };

  return (
    <LayoutContent
      pageCode="page_knowledge_hub-exam-info"
      composeBreadcrumbs={(value: LayoutBreadcrumb[]) => [
        ...value,
        { key: 'exam-name', text: examRef.current!.name },
      ]}
    >
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
        <Card title="考试信息" bordered={false}>
          <Descriptions column={4}>
            <Descriptions.Item label="考试名称">{examRef.current.name}</Descriptions.Item>
            <Descriptions.Item label="考试分类">
              <ExamsCategory categoryCode={examRef.current.categoryCode!} />
            </Descriptions.Item>
            <Descriptions.Item label="试卷总分">{examRef.current.totalGrade}分</Descriptions.Item>
            <Descriptions.Item label="试题总数">{examRef.current.questionNum}题</Descriptions.Item>
            <Descriptions.Item label="考试人数">
              {examRef.current.totalUserCount}人
            </Descriptions.Item>
            <Descriptions.Item label="考试时间">
              {dayjs(examRef.current.timeRange[0]).format('YYYY-MM-DD HH:mm')} ~
              {dayjs(examRef.current.timeRange[1]).format('YYYY-MM-DD HH:mm')}
            </Descriptions.Item>
          </Descriptions>
        </Card>
        <Card title="考卷信息" bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space size={100}>
              <div>
                <>结果：</>
                <Checkbox.Group
                  options={resultOptions}
                  defaultValue={['all']}
                  onChange={(val: any) => {
                    searchResultRef.current = val;
                    getUsersByQ();
                  }}
                />
              </div>
              <div>
                <>状态：</>
                <Checkbox.Group
                  options={statusOptions}
                  defaultValue={['all']}
                  onChange={(val: any) => {
                    searchStatusRef.current = val;
                    getUsersByQ();
                  }}
                />
              </div>
            </Space>

            <Space style={{ justifyContent: 'space-between', width: '100%' }}>
              <Form layout="inline" form={form} colon={false}>
                <Form.Item label="位置" name="blockGuids">
                  <LocationTreeSelect style={{ width: 120 }} allowClear disabledTypes={['IDC']} />
                </Form.Item>
                <Form.Item label="考生姓名" name="user">
                  <UserSelect style={{ width: 200 }} allowClear optionLabelProp="title" />
                </Form.Item>
                <Form.Item label="考生角色" name="roleIds">
                  <RoleSelect style={{ width: 200 }} allowClear optionFilterProp="name" />
                </Form.Item>
                <Form.Item>
                  <Space>
                    <Button type="primary" loading={usersLoading} onClick={getUsersByQ}>
                      搜索
                    </Button>
                    <Button
                      disabled={usersLoading}
                      onClick={() => {
                        form.resetFields();
                        getUsers();
                      }}
                    >
                      重置
                    </Button>
                  </Space>
                </Form.Item>
              </Form>
              {examRef.current.name && (
                <FileExport
                  filename={`${examRef.current.name}.xls`}
                  disabled={users.length === 0}
                  data={async () => {
                    const newHeaders = exportColumns.map(column => ({
                      dataIndex: column.dataIndex,
                      title: column.title,
                      stringify: column.stringify,
                    }));
                    saveJSONAsXLSX<ExamPaperUser>(
                      [
                        {
                          data: users,
                          headers: newHeaders,
                        },
                      ],
                      examRef.current!.name
                    );
                  }}
                />
              )}
            </Space>

            {dayjs().diff(dayjs(examRef.current.timeRange[1]), 'minutes') +
              examRef.current.timeLimit <
              0 && (
              <GroupUsersPicker
                requiredText="必考人员设置"
                optionalText="选考人员设置"
                buttonText="添加考生"
                buttonType="primary"
                value={examUsers}
                showDepts={false}
                onChange={value => {
                  setExamUsers(value);
                }}
                onReset={() =>
                  setExamUsers({
                    requiredUsers: { userKeys: [], roleKeys: [] },
                    optionalUsers: { userKeys: [], roleKeys: [] },
                  })
                }
                onSubmit={() => addStudents(Number(id))}
              />
            )}

            <Table
              rowKey="userId"
              loading={usersLoading}
              columns={[
                ...columns,
                {
                  width: 100,
                  title: '操作',
                  fixed: 'right',
                  dataIndex: '_actions',
                  render(_, { examId, id, status, autoGrade, userId }: ExamPaperUser) {
                    const isExaminee = checkUserId(userId);

                    if (
                      (status === BackendExamPaperStatus.SUBMIT ||
                        status === BackendExamPaperStatus.WAIT_GRADE) &&
                      !autoGrade &&
                      examRef.current!.allowMark &&
                      !isExaminee
                    ) {
                      return <Link to={generateExamPaperLocation({ examId, id })}>判卷</Link>;
                    }

                    if (
                      status === BackendExamPaperStatus.GRADED &&
                      !autoGrade &&
                      examRef.current!.allowMark &&
                      !isExaminee &&
                      dayjs().isBefore(dayjs(examRef.current!.timeRange[1]))
                    ) {
                      return <Link to={generateExamPaperLocation({ examId, id })}>重新判卷</Link>;
                    }

                    if (
                      status === BackendExamPaperStatus.PROCESS &&
                      examRef.current!.allowMark &&
                      !isExaminee
                    ) {
                      return (
                        <Popconfirm
                          placement="rightTop"
                          title={
                            <>
                              <div>确认要强制交卷吗？</div>
                              <div>考生答题还未结束，强制交卷可能会影响考试成绩。</div>
                            </>
                          }
                          onConfirm={async () => {
                            const { error } = await forceHandIn('handing-in', {
                              id,
                              force: true,
                            });
                            if (error) {
                              message.error(error.message);
                              return;
                            }
                            message.success('强制交卷成功！');
                            getUsers();
                          }}
                        >
                          <Button compact type="link">
                            强制交卷
                          </Button>
                        </Popconfirm>
                      );
                    }

                    if (status === BackendExamPaperStatus.GRADED && examRef.current!.allowMark) {
                      return (
                        <Button
                          style={{ height: 'auto', padding: 0 }}
                          type="link"
                          onClick={() => {
                            history.push(generateExamPaperLocation({ id, examId: examId }));
                          }}
                        >
                          查看考卷
                        </Button>
                      );
                    }
                    return '--';
                  },
                },
              ]}
              scroll={{ x: 'max-content' }}
              dataSource={users}
              pagination={{ total: users.length }}
            />
          </Space>
        </Card>
      </Space>
    </LayoutContent>
  );
}

function generateExamPaperUsers(paper?: string[] | null) {
  return paper ? paper?.join(' | ') : '';
}

function generateExamPaperUsersStatus(status: BackendExamPaperStatus) {
  return ExamPaperStatusMapper[status];
}

function generateExamPaperUsersPass(pass: boolean | null) {
  return pass !== null ? (pass ? '通过' : '未通过') : '';
}
function generateExamPaperUsersForceSubmit(forceSubmit: boolean | null) {
  return forceSubmit !== null ? (forceSubmit ? '是' : '否') : '';
}

function generateExamPaperUsersTime(time: number | null) {
  return time !== null ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
}

function generateExamPaperUsersDuration(startTime: number | null, endTime: number | null) {
  return startTime !== null
    ? `${dayjs(endTime || Date.now()).diff(dayjs(startTime), 'minutes')}分钟`
    : '';
}
