import React, { useCallback, useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Collapse } from '@manyun/base-ui.ui.collapse';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { PaperPreviewRoutePathGenFuncParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { ExamPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { fetchPreviewPaperWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-preview-paper';
import { StatisticPaperQuestionsGroupList } from '@manyun/knowledge-hub.ui.paper-questions-group-list';
import { Question } from '@manyun/knowledge-hub.ui.question';

import styles from './paper-preview.module.less';

export function PaperPreview() {
  const { id } = useParams<PaperPreviewRoutePathGenFuncParams>();

  const [loading, setLoading] = useState(false);
  const [exsitingPaper, setExsitingPaper] = useState<Omit<ExamPaper, 'autoMark'> | null>(null);

  const _fetchPreviewPaper = useCallback(async () => {
    setLoading(true);
    const { data, error } = await fetchPreviewPaperWeb({ paperId: Number(id) });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setExsitingPaper(data);
  }, [id]);

  useEffect(() => {
    _fetchPreviewPaper();
  }, [_fetchPreviewPaper]);

  return (
    <div className={styles.previewPaperContainer}>
      <Space style={{ width: '100%' }} className={styles.previewPaperContent} align="start">
        <Card title="试卷信息" loading={loading}>
          <StatisticPaperQuestionsGroupList
            mode="preview"
            paper={{
              totalScore: exsitingPaper?.totalScore,
              totalSize: exsitingPaper?.questionSize,
              groups:
                (exsitingPaper?.questionsGroups ?? []).map(section => ({
                  id: Number(section.id),
                  name: section.name,
                  totalScore: section.questionScore || 0,
                  totalSize: section.questionSize || 0,
                })) ?? [],
            }}
          />
        </Card>
        <Card title="考试信息" loading={loading}>
          {exsitingPaper && (
            <Collapse defaultActiveKey={exsitingPaper?.questionsGroups.map(section => section.id)}>
              {exsitingPaper?.questionsGroups.map(section => (
                <Collapse.Panel key={section.id} header={section.name}>
                  <div>
                    {section.questionIds.map((id, key) => (
                      <Space
                        style={{ width: '100%', justifyContent: 'space-between' }}
                        align="start"
                      >
                        <Question
                          question={section.questionEntities[id]}
                          mode="create"
                          index={key + 1}
                          key={id}
                          style={{ marginBottom: '20px' }}
                        />
                        <Typography.Text style={{ lineHeight: '40px' }}>
                          本题分值：{section.questionEntities[id].grade}分
                        </Typography.Text>
                      </Space>
                    ))}
                  </div>
                </Collapse.Panel>
              ))}
            </Collapse>
          )}
        </Card>
      </Space>
      <FooterToolBar>
        <Button
          type="default"
          onClick={() => {
            window.close();
          }}
        >
          关闭
        </Button>
      </FooterToolBar>
    </div>
  );
}
