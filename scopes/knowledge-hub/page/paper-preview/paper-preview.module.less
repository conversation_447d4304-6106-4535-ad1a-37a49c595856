.previewPaperContainer {
  width: 100%;
  height: calc(var(--content-height) - 38px);
  display: flex;

  .previewPaperContent {
    width: 100%;
    height: 100%;
    > :global(.manyun-space-item) {
      height: 100%;
      &:first-child {
        width: 375px;
      }
      &:last-child {
        flex: 1;
      }
      > * {
        height: 100%;
        > :global(.manyun-card-body) {
          position: relative;
          height: calc(100% - /* Card Head height */ 58px);
          overflow-y: auto;
        }
      }
    }
  }
}
