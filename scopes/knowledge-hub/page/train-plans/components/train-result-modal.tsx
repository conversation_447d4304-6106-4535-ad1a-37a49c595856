import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import type { ModalProps } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import type { SheetHeader } from '@manyun/base-ui.util.xlsx';
import { fetchCourseStudent } from '@manyun/knowledge-hub.service.dcexam.fetch-course-student';
import type { BackendCourseStudent } from '@manyun/knowledge-hub.service.dcexam.fetch-course-student';
import {
  BackendCourseType,
  BackendStudyStatus,
} from '@manyun/knowledge-hub.service.dcexam.fetch-courses';

export type TaskRelateTicketsModalProps = {
  taskId: number;
  effectTime: string;
} & ModalProps;

export function TrainResultModal({ taskId, effectTime, ...props }: TaskRelateTicketsModalProps) {
  const [students, setStudents] = useState<BackendCourseStudent[]>([]);
  const [usersLoading, setUsersLoading] = useState(false);

  useEffect(() => {
    (async function () {
      setUsersLoading(true);
      const { error, data } = await fetchCourseStudent({
        scheduleId: taskId,
        triggerTime: effectTime,
      });
      setUsersLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setStudents(data.data);
    })();
  }, [taskId, effectTime]);

  const columns: (ColumnType<BackendCourseStudent> & SheetHeader)[] = [
    {
      title: '用户名',
      dataIndex: 'userName',
      fixed: 'left',
      ellipsis: true,
      render(userName: string, { userId }: BackendCourseStudent) {
        return <UserLink userId={userId} userName={userName} />;
      },
    },
    {
      title: '学习状态',
      dataIndex: 'studyStatus',
      render(studyStatus: string) {
        return (
          <Tag color={generateStudyStatusTag(studyStatus).color}>
            {generateStudyStatusTag(studyStatus).label}
          </Tag>
        );
      },
      stringify: (studyStatus: string) => generateStudyStatusTag(studyStatus).label,
    },
    {
      title: '必修/选修',
      dataIndex: 'courseType',
      render: (courseType: string) => {
        return (
          <Tag color={generateCourseTypeTag(courseType).color}>
            {generateCourseTypeTag(courseType).label}
          </Tag>
        );
      },
      stringify: (courseType: string) => generateCourseTypeTag(courseType).label,
    },
    {
      title: '测试结果',
      dataIndex: 'pass',
      ellipsis: true,
      render(pass: boolean | null) {
        return typeof pass === 'boolean' ? (
          <Tag color={pass ? 'success' : 'error'}>{pass ? '通过' : '不通过'}</Tag>
        ) : (
          '--'
        );
      },
      stringify: (pass: boolean | null) => {
        if (typeof pass === 'boolean') {
          return pass ? '通过' : '不通过';
        } else {
          return '--';
        }
      },
    },
  ];

  return (
    <Modal width={720} title="培训结果" {...props}>
      <Space style={{ width: '100%' }} direction="vertical">
        <Descriptions>
          <Descriptions.Item style={{ paddingBottom: 8 }} label="触发时间">
            {dayjs(effectTime).format('YYYY-MM-DD HH:mm')}
          </Descriptions.Item>
        </Descriptions>
        <Table
          style={{ width: '100%' }}
          loading={usersLoading}
          columns={[...columns]}
          scroll={{ x: 'max-content' }}
          dataSource={students}
          pagination={{ total: Array.isArray(students) ? students.length : 0 }}
        />
      </Space>
    </Modal>
  );
}

function generateStudyStatusTag(studyStatus: string) {
  switch (studyStatus) {
    case BackendStudyStatus.COMPLETED:
      return { label: '已完成', color: 'success' };
    case BackendStudyStatus.UNDONE:
      return { label: '未完成', color: 'processing' };
    case BackendStudyStatus.UN_INVOLVED:
      return { label: '未参与', color: 'error' };
    default:
      return { label: '--', color: 'default' };
  }
}

function generateCourseTypeTag(courseType: string) {
  switch (courseType) {
    case BackendCourseType.OBLIGATORY:
      return { label: '必修', color: 'success' };
    case BackendCourseType.ELECTIVE:
      return { label: '选修', color: 'warning' };
    default:
      return { label: '--', color: 'default' };
  }
}
