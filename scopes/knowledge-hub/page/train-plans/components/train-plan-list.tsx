import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  NEW_TRAIN_PLAN_ROUTE_PATH, // generateEditTrainPlanRoutePath,
  generateEditTrainPlanUrl,
  generateTrainPlanRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { CoursesCategory } from '@manyun/knowledge-hub.ui.courses-category';
import { FiltersCoursesModal } from '@manyun/knowledge-hub.ui.filters-courses-modal';
import { LocationTreeSelect, useSpaces } from '@manyun/resource-hub.ui.location-tree-select';
import { useDeleteTask } from '@manyun/ticket.gql.client.tickets';
import type { BackendPlanSearchParam, PlanEffect, PlanJSON } from '@manyun/ticket.model.task';
import {
  JobType,
  PLAN_EFFECT_OPTIONS,
  PLAN_EFFECT_TEXT_MAP,
  Plan,
  PlanStatus,
  SCH_MODE_TYPE_TEXT,
  SchModeType,
} from '@manyun/ticket.model.task';
import { batchUpdatePlanStatus } from '@manyun/ticket.service.batch-update-plan-status';
import { fetchTasks } from '@manyun/ticket.service.fetch-tasks';

import { TrainPlanKey } from '../train-plans';

type FiltersFormValues = {
  isInEffect?: PlanEffect;
  blockGuidList?: string[];
  taskStatus?: PlanStatus;
  schMode?: SchModeType;
  name?: string;
};

export function TrainPlanList({ activeKey }: { activeKey: TrainPlanKey }) {
  const [form] = Form.useForm<FiltersFormValues>();

  const [plans, setPlans] = useState<PlanJSON[]>([]);
  const [planLoading, setPlanLoading] = useState(false);
  const [planTotal, setPlanTotal] = useState(0);
  const [planPagination, setPlanPagination] = useState<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const [planQueryParams, setPlanQueryParams] = useState<BackendPlanSearchParam>();

  const [{ treeSpaces }] = useSpaces({
    authorizedOnly: true,
    nodeTypes: ['IDC', 'BLOCK'],
    disabledTypes: [],
    includeVirtualBlocks: false,
  });

  const getPlans = async (
    params: BackendPlanSearchParam & { pageNum: number; pageSize: number }
  ) => {
    setPlanLoading(true);
    const { error, data } = await fetchTasks({
      ...params,
      jobTypeList: [JobType.TrainPlan],
    });
    setPlanLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setPlans(data.data);
    setPlanTotal(data.total);
  };
  useEffect(() => {
    if (activeKey === TrainPlanKey.List) {
      getPlans({
        ...planQueryParams,
        pageNum: planPagination.pageNum,
        pageSize: planPagination.pageSize,
      });
    }
  }, [planQueryParams, planPagination, activeKey]);

  const [deleteTask] = useDeleteTask({
    onCompleted(data) {
      if (!data.deleteTask?.success) {
        message.error(data.deleteTask?.message ?? '删除失败');
        return;
      } else {
        message.success('删除成功');
        getPlans({
          ...planQueryParams,
          pageNum: planPagination.pageNum,
          pageSize: planPagination.pageSize,
        });
      }
    },
  });
  const columns: Array<ColumnType<PlanJSON>> = [
    {
      title: '计划名称',
      dataIndex: 'name',
      width: 280,
      render: (_, record) => {
        return (
          <Typography.Text ellipsis={{ tooltip: true }}>
            <Link target="_blank" to={generateTrainPlanRoutePath(record.id)}>
              {record.name}
            </Link>
          </Typography.Text>
        );
      },
    },
    {
      title: '计划范围',
      dataIndex: 'blockScope',
      render: (_, { blockScope }) => {
        const blockGuids = blockScope.map(item => item.blockGuid);
        return blockGuids.join(' | ');
      },
    },
    {
      title: '课程分类',
      dataIndex: 'mopType',
      render: (txt: string) => {
        return <CoursesCategory categoryCode={Number(txt)} />;
      },
    },
    {
      title: '培训方式',
      dataIndex: 'schMode',
      render: (schMode?: SchModeType) => (schMode ? SCH_MODE_TYPE_TEXT[schMode] : '--'),
    },
    {
      title: '是否有效',
      dataIndex: 'isInEffect',
      render: (_, { isInEffect }) => PLAN_EFFECT_TEXT_MAP[isInEffect],
    },
    {
      title: '上次运行时间',
      dataIndex: 'lastExecuteTime',
      render: (_, record) => Plan.fromJSON(record).getFormattedLastExecuteAt(),
    },
    {
      title: '运行结果',
      dataIndex: 'lastExecuteResult',
      render: (_, { lastExecuteResult, id }) =>
        lastExecuteResult?.length ? (
          <FiltersCoursesModal
            btnText={lastExecuteResult.length}
            courseIdList={lastExecuteResult.map(item => Number(item))}
          />
        ) : (
          0
        ),
    },
    {
      title: '更新时间',
      dataIndex: 'modifierAt',
      render: (_, record) => Plan.fromJSON(record).getFormattedModifiedAt(),
    },
    {
      title: '更新人',
      dataIndex: 'modifierId',
      render: (_, { modifyUser }) =>
        modifyUser ? <UserLink userId={modifyUser.id!} userName={modifyUser.name!} /> : '--',
    },
    {
      title: '启用状态',
      dataIndex: 'isActivated',
      render: (_, record) => {
        const isActivated = record.isActivated === PlanStatus.On;

        return (
          <Switch
            checked={isActivated}
            onChange={async () => {
              const { error } = await batchUpdatePlanStatus({
                ids: [record.id],
                status: isActivated ? PlanStatus.Off : PlanStatus.On,
                ignoreJobItems: true,
              });
              if (error) {
                message.error(error.message);
                return;
              }
              message.success('设置成功');
              getPlans({
                ...planQueryParams,
                pageNum: planPagination.pageNum,
                pageSize: planPagination.pageSize,
              });
            }}
          />
        );
      },
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <Link to={generateEditTrainPlanUrl({ id: record.id, name: record.name })}>编辑</Link>

            <Popconfirm
              key="remove"
              title="删除此计划？"
              onConfirm={async () => {
                await deleteTask({ variables: { id: record.id } });
              }}
            >
              <Button compact type="link">
                删除
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  //['EC03.G'];
  return (
    <Space style={{ width: '100%' }} direction="vertical" size={16}>
      <QueryFilter<FiltersFormValues>
        form={form}
        items={[
          {
            label: '计划名称',
            name: 'name',
            control: <Input allowClear />,
          },
          {
            label: '计划范围',
            name: 'blockGuidList',

            control: <LocationTreeSelect authorizedOnly allowClear />,
          },
          {
            label: '是否有效',
            name: 'isInvalid',
            control: <Select options={PLAN_EFFECT_OPTIONS} allowClear />,
          },
          {
            label: '状态',
            name: 'taskStatus',
            control: (
              <Select
                options={[
                  {
                    value: PlanStatus.On,
                    label: '启用',
                  },
                  {
                    value: PlanStatus.Off,
                    label: '禁用',
                  },
                ]}
                allowClear
              />
            ),
          },
          {
            label: '培训方式',
            name: 'schMode',
            control: (
              <Select
                options={[
                  {
                    value: SchModeType.Online,
                    label: '在线培训',
                  },
                  {
                    value: SchModeType.Offline,
                    label: '线下培训',
                  },
                ]}
                allowClear
              />
            ),
          },
        ]}
        onSearch={value => {
          if (value.blockGuidList) {
            const idcVal = treeSpaces.find(
              (v: Record<string, any>) => v.value === value?.blockGuidList
            ) as Record<string, any>;

            if (!idcVal?.children) {
              value.blockGuidList = [idcVal.value];
            } else {
              const blockArr = idcVal.children.map((v: Record<string, any>) => v.value);
              value.blockGuidList = blockArr;
            }
          }

          setPlanQueryParams(value);
          setPlanPagination({ pageNum: 1, pageSize: 10 });
        }}
        onReset={() => {
          setPlanQueryParams({});
          setPlanPagination({ pageNum: 1, pageSize: 10 });
        }}
      />
      <Link to={NEW_TRAIN_PLAN_ROUTE_PATH}>
        <Button type="primary">新建培训计划</Button>
      </Link>
      <Table<PlanJSON>
        style={{ width: '100%' }}
        dataSource={plans}
        scroll={{ x: 'max-content' }}
        pagination={{
          total: planTotal,
          pageSize: planPagination.pageSize,
          current: planPagination.pageNum,
          onChange: (current, size) => {
            setPlanPagination({ pageNum: current, pageSize: size });
            getPlans({ ...planQueryParams, pageNum: current, pageSize: size });
          },
        }}
        loading={planLoading}
        columns={columns}
      />
    </Space>
  );
}
