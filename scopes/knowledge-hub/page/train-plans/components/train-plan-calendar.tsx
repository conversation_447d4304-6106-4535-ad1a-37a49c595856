import { EditOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';
import type { MonthlyTaskQueryParams } from 'scopes/ticket/context/task-data';

import { Badge } from '@manyun/base-ui.ui.badge';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { FullCalendar } from '@manyun/base-ui.ui.full-calendar';
import type { FullCalendarReact } from '@manyun/base-ui.ui.full-calendar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateTrainPlanRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { JobType, SCH_MODE_TYPE_TEXT, Task, TaskEffectStatus } from '@manyun/ticket.model.task';
import type {
  BackendMonthlyTaskSearchParam,
  MonthlyTasks, // PlanType,
  SchModeType,
  TaskJSON,
} from '@manyun/ticket.model.task';
import { batchUpdateCalendarTask } from '@manyun/ticket.service.batch-update-calendar-task';
import { fetchCalendarTasks } from '@manyun/ticket.service.fetch-calendar-tasks';

import { TrainPlanKey } from '../train-plans';
import { TrainResultModal } from './train-result-modal.js';

export type CalendarEvent = {
  id: string;
  start: string;
  title: string;

  extendedProps: {
    taskId: number;
    endTime: string | null;
    effectStatus: TaskEffectStatus;
    jobSla: number;
    jumpHolidayEffectTime?: string;
    schMode?: SchModeType;
  };
};

type TargetEvent = {
  blackDateTime: string;
  whiteDateTime?: string;
  schId: number;
  eventId: string;
};

export function TrainPlanCalendar({ activeKey }: { activeKey: TrainPlanKey }) {
  const [month, setMonth] = useState<string>(dayjs().format('YYYY-MM'));

  const [monthlyTasks, setMonthlyTasks] = useState<MonthlyTasks[]>([]);
  const [effectTime, setEffectTime] = useState<string>('');
  const [taskId, setTaskId] = useState<number>();
  const [modalVisible, setModalVisible] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  const history = useHistory();

  const [monthlyTasksLoading, setMonthlyTasksLoading] = useState(false);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const calendarRef = useRef<FullCalendarReact>(null);
  const [monthlyCalendarQueryParams] = useState<BackendMonthlyTaskSearchParam>({
    jobTypeList: [],
    endDate: dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    startDate: dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
  });

  // 用于保存最原始的排期信息。构建排期id和排期最初的时间的映射关系
  const initialEventsRef = useRef<Record<string, string>>({});
  // 用于保存修改状态下排期与最新时间的映射关系，构建排期id和排期最新的时间以及关联计划id的映射关系
  const latestEventStartTimeRef = useRef<Record<string, { start: string; taskId: number }>>({});
  // 用于保存特殊类型的排期信息【配了顺延至节假日的排期】。构建排期id和原始排期时间的映射关系
  const originEventsTimeRef = useRef<Record<string, string>>({});
  const currentEventsRef = useRef<CalendarEvent[]>([]);

  const [initialDate, setInitialDate] = useState<{ start: string; end: string }>({
    start: dayjs(month).startOf('month').format('YYYY-MM-DD'),
    end: dayjs(month).add(1, 'month').startOf('month').format('YYYY-MM-DD'),
  });
  const setInitialDateCallback = useCallback((month: string) => {
    setInitialDate({
      start: dayjs(month).startOf('month').format('YYYY-MM-DD'),
      end: dayjs(month).add(1, 'month').startOf('month').format('YYYY-MM-DD'),
    });
  }, []);

  useEffect(() => {
    setInitialDateCallback(month);
  }, [month, setInitialDateCallback]);

  useEffect(() => {
    let calendarEvents: CalendarEvent[] = [];
    if (monthlyTasks.length > 0) {
      calendarEvents = monthlyTasks.reduce<CalendarEvent[]>((pre, cur) => {
        const events = cur.calendarExecuteInfoSet.map((item: TaskJSON) => ({
          id: `${item.effectTime}${item.id}`,
          start: Task.fromJSON(item).getFormattedEffectedTime(),
          title: item.name,
          extendedProps: {
            endTime: Task.fromJSON(item).getFormattedEndTime(),
            taskId: item.id,
            effectStatus: item.effectStatus,
            jobSla: item.jobSla,
            jumpHolidayEffectTime: Task.fromJSON(item).getFormattedJumpHolidayEffectTime(),
            schMode: item.schMode,
          },
          editable: true,
        }));
        return [...pre, ...events];
      }, []);
    }

    setEvents(calendarEvents);
    calendarEvents.forEach(item => {
      initialEventsRef.current[`${item.id}`] = item.start;
      if (item.extendedProps.jumpHolidayEffectTime) {
        originEventsTimeRef.current[`${item.id}`] = item.extendedProps.jumpHolidayEffectTime;
      }
    });
    currentEventsRef.current = calendarEvents;
  }, [monthlyTasks]);

  useDeepCompareEffect(() => {
    const calendarEvents = events.map(event => ({
      ...event,
      editable: event.extendedProps.effectStatus === TaskEffectStatus.Ineffective,
    }));
    setEvents(calendarEvents);
    currentEventsRef.current = calendarEvents;
  }, [events]);

  const setLatestEventStartItem = (id: string, start: string, taskId: number) => {
    latestEventStartTimeRef.current[`${id}`] = { start, taskId };
  };

  const handleEventClick = (id: string, type: 'delete' | 'edit', newStartTime?: Moment) => {
    // 获取 FullCalendar 的 API 实例
    if (calendarRef.current) {
      const calendarApi = calendarRef.current.getApi();
      // 使用 getEventById 方法获取事件对象
      const event = calendarApi.getEventById(id);
      if (event) {
        const schId = event.extendedProps.taskId;

        if (newStartTime) {
          event.setStart(newStartTime.toDate());
          event.setEnd(null);
          const [name] = event.title.split(' ');
          event.setProp('title', name);
          setLatestEventStartItem(
            event.id,
            moment(event.start).format('YYYY-MM-DD HH:mm:ss'),
            schId
          );
          currentEventsRef.current = currentEventsRef.current.map(item => {
            if (item.id === event.id) {
              return {
                ...item,
                start: moment(event.start).format('YYYY-MM-DD HH:mm'),
              };
            }
            return item;
          });
          saveChanges();
        }
      }
    }
  };

  const getMonthlyTasks = async (
    params: BackendMonthlyTaskSearchParam & { month?: string; year?: string }
  ) => {
    setMonthlyTasksLoading(true);
    const { month, year, ...rest } = params;
    const dateRange = { startDate: '', endDate: '' };
    if (month) {
      dateRange.startDate = dayjs(month).startOf('month').format('YYYY-MM-DD');
      dateRange.endDate = dayjs(month).endOf('month').format('YYYY-MM-DD');
    }
    const { error, data } = await fetchCalendarTasks({
      ...rest,
      ...dateRange,
      jobTypeList: [JobType.TrainPlan],
    });
    setMonthlyTasksLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setMonthlyTasks(data.data);
  };

  useEffect(() => {
    if (activeKey === TrainPlanKey.Calendar) {
      getMonthlyTasks({
        month: month,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [activeKey, month]);

  const saveChanges = async () => {
    // setLatestEventStartTiem(event.id, moment(event.start).format('YYYY-MM-DD HH:mm:ss'));

    if (Object.keys(latestEventStartTimeRef.current).length > 0) {
      const changeItemWithoutDeleteKeys = Object.keys(latestEventStartTimeRef.current);
      const trueChangeEventItems = changeItemWithoutDeleteKeys
        .map(key => ({
          schId: latestEventStartTimeRef.current[key].taskId,
          blackDateTime: initialEventsRef.current[key],
          whiteDateTime: latestEventStartTimeRef.current[key].start,
        }))
        .filter(item => item.blackDateTime !== item.whiteDateTime);
      const deleteOriginEvents: Omit<TargetEvent, 'eventId'>[] = changeItemWithoutDeleteKeys.reduce(
        (pre, cur) => {
          if (originEventsTimeRef.current[cur]) {
            pre.push({
              schId: latestEventStartTimeRef.current[cur].taskId,
              blackDateTime: originEventsTimeRef.current[cur],
            });
          }
          return pre;
        },
        [] as Omit<TargetEvent, 'eventId'>[]
      );
      const changeExecuteList = [...trueChangeEventItems, ...deleteOriginEvents];
      if (changeExecuteList.length > 0) {
        setSaveLoading(true);
        const { error } = await batchUpdateCalendarTask({
          changeExecuteList,
        });
        setSaveLoading(false);
        if (error) {
          message.error(error.message);
          // 接口若是调用失败，则要清空之前的操作。
          latestEventStartTimeRef.current = {};
        } else {
          message.success('保存成功');
          let month;
          if (Object.keys(latestEventStartTimeRef.current).length > 0) {
            const eventIndex = Object.keys(latestEventStartTimeRef.current)[0];
            month = moment(latestEventStartTimeRef.current[eventIndex].start).format('YYYY-MM');
          }

          getMonthlyTasks({
            ...monthlyCalendarQueryParams,
            month,
          });

          // 保存成功之后要将暂存的删除事项清空
          latestEventStartTimeRef.current = {};
        }
      } else {
        // 没有无效拖动时，点完保存按钮不会调用接口，但是也要清空一下。
        latestEventStartTimeRef.current = {};
      }
    }
  };

  const isTriggerTimeValidBySingleEvent = (value: Moment, taskId: number, eventId: string) => {
    const sameIdTasks = currentEventsRef.current.filter(
      task => task.extendedProps.taskId === taskId
    );
    // 找到原排期中与移动后排期为同一天的任务
    const sameDayTasks = sameIdTasks.filter(task => {
      const taskDay = moment(task.start).format('YYYY-MM-DD');
      return taskDay === value.format('YYYY-MM-DD');
    });

    // 过滤掉当前移动中的排期
    const sameDayExceptItsSelfTasks = sameDayTasks.filter(task => task.id !== eventId);
    // 将剩余任务转化为触发时间的数组
    const sameDayExceptItsSelfTriggerTimes = sameDayExceptItsSelfTasks.map(task => task.start);
    // 是否除当前移动排期外，目标日还存在其他触发时间的任务
    const exceptItsSelfTrrigerTimeLength = sameDayExceptItsSelfTriggerTimes.length;

    let isValid = true;
    for (let i = 0; i < exceptItsSelfTrrigerTimeLength; i++) {
      if (
        Math.abs(moment(sameDayExceptItsSelfTriggerTimes[i]).diff(moment(value), 'minute')) < 10
      ) {
        isValid = false;
      }
    }
    // 如果目标日不存在当前移动排期之外的其他触发时间的任务，则此次移动有效，否则，根据isValid的结果。
    return exceptItsSelfTrrigerTimeLength === 0 ? true : isValid;
  };

  const CalendarItem = React.memo(
    ({
      events,
      eventEditable,
      title,
      time,
      id,
      endTime,
      effectStatus,
      // planType,
      eventId,
      monthlyCalendarQueryParams,
      jobSla,
      schMode,
      toggleMonthlyTasks,
      setTaskId,
      setModalVisible,
      setEffectTime,
      // showEditIcon,
      // showDeleteIcon,
      handleEventClick,
      isTriggerTimeValidBySingleEvent,
      saveChanges,
    }: {
      events: CalendarEvent[];
      eventEditable: boolean;
      title: string;
      time: string | null;
      id: number;
      endTime: string | null;
      effectStatus: string;
      // planType: PlanType;
      eventId: string;
      monthlyCalendarQueryParams: MonthlyTaskQueryParams;
      jobSla: number;
      schMode?: SchModeType;
      toggleMonthlyTasks: (params: MonthlyTaskQueryParams) => void;
      setTaskId: (params: number) => void;
      setModalVisible: (params: boolean) => void;
      setEffectTime: (params: string) => void;
      // showEditIcon: boolean;
      // showDeleteIcon: boolean;
      handleEventClick: (id: string, type: 'delete' | 'edit', newStartTime?: Moment) => void;
      isTriggerTimeValidBySingleEvent: (value: Moment, taskId: number, eventId: string) => boolean;
      saveChanges: () => void;
    }) => {
      return (
        <div
          style={{
            width: 'calc(100% - 8px)',
            display: 'flex',
            marginLeft: 8,
            alignItems: 'center',
            alignContent: 'center',
            justifyContent: 'start',
            height: 30,
          }}
        >
          <div style={{ width: 'calc(100% - 20px)' }}>
            <div
              style={{
                display: 'flex',
                alignItems: 'center',
              }}
            >
              <Badge
                status={effectStatus !== TaskEffectStatus.Ineffective ? 'default' : 'processing'}
              />
              <Tooltip
                title={
                  <Space direction="vertical">
                    <Typography style={{ color: 'var(--component-background)' }}>
                      计划名称：{title}
                    </Typography>
                    <Typography style={{ color: 'var(--component-background)' }}>
                      触发时间：{moment(time).format('YYYY-MM-DD HH:mm')}
                    </Typography>
                    <Typography style={{ color: 'var(--component-background)' }}>
                      培训方式：{schMode ? SCH_MODE_TYPE_TEXT[schMode] : '--'}
                    </Typography>
                  </Space>
                }
              >
                <Typography.Text
                  style={{ marginLeft: 8 }}
                  ellipsis
                  onClick={() => {
                    if (effectStatus !== TaskEffectStatus.Ineffective) {
                      setEffectTime(moment(time).format('YYYY-MM-DD HH:mm:ss'));
                      setTaskId(id);
                      setModalVisible(true);
                    } else {
                      history.push(generateTrainPlanRoutePath(id));
                    }
                  }}
                >
                  {title}
                </Typography.Text>
              </Tooltip>
            </div>
          </div>
          <HandleTaskSchedule
            time={time}
            isEditable={eventEditable}
            effectStatus={effectStatus}
            id={id}
            events={events}
            eventId={eventId}
            endTime={endTime}
            monthlyCalendarQueryParams={monthlyCalendarQueryParams}
            toggleMonthlyTasks={toggleMonthlyTasks}
            // showDeleteIcon={showDeleteIcon}
            // showEditIcon={showEditIcon}
            handleEventClick={handleEventClick}
            isTriggerTimeValidBySingleEvent={isTriggerTimeValidBySingleEvent}
            saveChanges={saveChanges}
          />
        </div>
      );
    }
  );
  CalendarItem.displayName = 'CalendarItem';

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <DatePicker
        allowClear={false}
        picker="month"
        format="YYYY-MM"
        value={moment(month)}
        onChange={value =>
          value ? setMonth(moment(value).format('YYYY-MM')) : setMonth(moment().format('YYYY-MM'))
        }
      />
      <Space>
        <Badge status="default" text="已触发" />
        <Badge status="processing" text="未触发" />
      </Space>
      <Spin spinning={monthlyTasksLoading || saveLoading}>
        <FullCalendar
          ref={calendarRef}
          contentHeight="auto"
          /** 视图类型 */
          initialView="dayGridMonth"
          /** 上方工具栏 */
          headerToolbar={false}
          /** 设置要展示的事项 */
          events={events}
          /** 设置语言 */
          locale="zh-cn"
          /** 确定一周的第一天以周几开始 */
          firstDay={7}
          /** 自定义事项 */
          eventContent={({ event }: { event: CalendarEvent & { durationEditable: boolean } }) => {
            return (
              <CalendarItem
                events={events}
                eventEditable={event.durationEditable}
                title={event.title}
                time={event.start}
                schMode={event.extendedProps.schMode}
                endTime={event.extendedProps.endTime}
                eventId={event.id}
                id={event.extendedProps.taskId}
                effectStatus={event.extendedProps.effectStatus}
                toggleMonthlyTasks={getMonthlyTasks}
                jobSla={event.extendedProps.jobSla}
                monthlyCalendarQueryParams={monthlyCalendarQueryParams}
                setTaskId={setTaskId}
                setModalVisible={setModalVisible}
                setEffectTime={setEffectTime}
                // showEditIcon={checkTaskModifyCode()}
                handleEventClick={handleEventClick}
                isTriggerTimeValidBySingleEvent={isTriggerTimeValidBySingleEvent}
                saveChanges={saveChanges}
              />
            );
          }}
          /** 日期有效范围，无效时间会置灰，无法拖动 */
          validRange={initialDate}
          visibleRange={initialDate}
          /** 日期格子的高宽比 */
          aspectRatio={2.3}
        />
      </Spin>
      <TrainResultModal
        zIndex={1031}
        taskId={Number(taskId)}
        effectTime={effectTime}
        open={modalVisible}
        footer={null}
        onCancel={() => {
          setModalVisible(false);
        }}
      />
    </Space>
  );
}

const HandleTaskSchedule = React.memo(
  ({
    time,
    isEditable,
    effectStatus,
    id,
    events,
    eventId,
    endTime,
    monthlyCalendarQueryParams,
    toggleMonthlyTasks,
    // showEditIcon,
    // showDeleteIcon,
    handleEventClick,
    isTriggerTimeValidBySingleEvent,
    saveChanges,
  }: {
    time: string | null;
    isEditable: boolean;
    effectStatus: string;
    id: number;
    events: CalendarEvent[];
    eventId: string;
    endTime: string | null;
    monthlyCalendarQueryParams: MonthlyTaskQueryParams;
    toggleMonthlyTasks: (params: MonthlyTaskQueryParams) => void;
    // showEditIcon: boolean;
    // showDeleteIcon: boolean;
    handleEventClick: (id: string, type: 'delete' | 'edit', newStartTime?: Moment) => void;
    isTriggerTimeValidBySingleEvent: (value: Moment, taskId: number, eventId: string) => boolean;
    saveChanges: () => void;
  }) => {
    const [form] = Form.useForm();
    const pickerTime = moment(time);

    return (
      <Space>
        {effectStatus !== TaskEffectStatus.Effected && (
          <Popconfirm
            icon={null}
            title={
              <Space direction="vertical">
                <Typography.Title level={5}>修改时间</Typography.Title>
                <Form form={form}>
                  <Form.Item
                    label="触发时间"
                    name="newTime"
                    initialValue={pickerTime}
                    rules={[
                      () => ({
                        validator(_, value) {
                          if (!value) {
                            return Promise.reject(new Error('修改时间不能为空'));
                          } else if (
                            endTime &&
                            moment(value).diff(moment(endTime), 'seconds') > 0
                          ) {
                            return Promise.reject(new Error('修改时间不能超过结束时间'));
                          } else if (moment(value).diff(moment(), 'minutes') < 0) {
                            return Promise.reject(new Error('修改时间不能小于当前时间'));
                          } else if (!isTriggerTimeValidBySingleEvent(value, id, eventId)) {
                            return Promise.reject(new Error('同类型任务触发时间间隔至少为10分钟'));
                          }
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    <DatePicker
                      picker="date"
                      showTime
                      format="YYYY-MM-DD HH:mm"
                      // @ts-ignore getPopupContainer 存在
                      getPopupContainer={trigger => trigger.parentNode.parentNode}
                    />
                  </Form.Item>
                </Form>
              </Space>
            }
            onConfirm={() => {
              form.validateFields().then(async formValue => {
                const { newTime } = formValue;
                handleEventClick(eventId, 'edit', newTime);
              });
            }}
            onCancel={() => {
              form.resetFields();
            }}
          >
            <EditOutlined />
          </Popconfirm>
        )}
      </Space>
    );
  }
);

HandleTaskSchedule.displayName = 'HandleTaskSchedule';
