import React, { useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';

import { TrainPlanCalendar } from './components/train-plan-calendar.js';
import { TrainPlanList } from './components/train-plan-list.js';

export type TrainPlansProps = {
  /**
   * sets the component children.
   */
};

export enum TrainPlanKey {
  List = 'plan-list',
  Calendar = 'calendar',
}

export function TrainPlans() {
  const [activeKey, setActiveKey] = useState<TrainPlanKey>(TrainPlanKey.List);

  const contentList: Record<string, React.ReactNode> = {
    [TrainPlanKey.List]: <TrainPlanList activeKey={activeKey} />,
    [TrainPlanKey.Calendar]: <TrainPlanCalendar activeKey={activeKey} />,
  };

  return (
    <Card
      tabList={[
        {
          key: TrainPlanKey.List,
          tab: '培训计划管理',
        },
        {
          key: TrainPlanKey.Calendar,
          tab: '培训计划日历',
        },
      ]}
      onTabChange={key => {
        setActiveKey(key as <PERSON><PERSON><PERSON><PERSON><PERSON>);
      }}
    >
      {contentList[activeKey]}
    </Card>
  );
}
