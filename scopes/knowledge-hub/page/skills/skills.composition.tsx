import React, { useEffect, useState } from 'react';
import { Route, MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { SKILL_PAGE_URL } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Skills } from './skills';

export const BasicSkills = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <Router initialEntries={['/']}>
      <Route path="/" exact>
        <ConfigProvider>
          <FakeStore>
            <Skills />
          </FakeStore>
        </ConfigProvider>
      </Route>
      <Route path={SKILL_PAGE_URL}>
        <>SKILL PAGE</>
      </Route>
    </Router>
  );
};
