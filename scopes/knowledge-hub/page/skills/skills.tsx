import React, { useEffect } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';

import { useSkills } from '@manyun/knowledge-hub.hook.use-skills';
import { Skill as SkillCard } from '@manyun/knowledge-hub.ui.skill';
import { SkillsFilters } from '@manyun/knowledge-hub.ui.skills-filters';

export type SkillProps = {};

export function Skills() {
  const [getSkills, { ids }] = useSkills();

  useEffect(() => {
    getSkills();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Space direction="vertical" style={{ width: '100%', display: 'flex' }}>
      <Card bordered={false}>
        <SkillsFilters />
      </Card>
      <Card bordered={false}>
        <Space size={16} wrap>
          {[undefined, ...ids].map((item, index) => (
            <SkillCard id={item} key={index} />
          ))}
        </Space>
        {/* </div> */}
      </Card>
    </Space>
  );
}
