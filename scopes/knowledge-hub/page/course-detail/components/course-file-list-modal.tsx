import React, { useState } from 'react';

import { FileTextFilled } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';

import type { BackendCourseFile } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';
import { CoursewareCategory } from '@manyun/knowledge-hub.ui.coursewares-category';

type CourseFileListModalProps = { courseFileList: BackendCourseFile[]; isComplete: boolean };

const columns: ColumnType<BackendCourseFile>[] = [
  {
    title: '课件名称',
    dataIndex: 'fileName',
    ellipsis: true,
    render: (fileName: string) => (
      <Space.Compact>
        <FileTextFilled style={{ color: 'var(--manyun-warning-color)', padding: '0 6px 0 0' }} />
        {fileName}
      </Space.Compact>
    ),
  },
  {
    title: '课件分类',
    dataIndex: 'categoryCode',
    ellipsis: true,
    render: (text: string) => (
      <CoursewareCategory categoryCode={Number(text)} variant={Variant.Auth} />
    ),
  },
];

export function CourseFileListModal({ courseFileList, isComplete }: CourseFileListModalProps) {
  const [open, setOpen] = useState(false);

  const showModal = () => {
    setOpen(true);
  };
  const closeModal = () => {
    setOpen(false);
  };
  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          showModal();
        }}
      >
        {courseFileList.length}
      </Button>
      <Modal
        title={`${isComplete ? '' : '未'}完成学习课件`}
        open={open}
        onCancel={() => {
          closeModal();
        }}
        onOk={() => {
          closeModal();
        }}
      >
        <Table
          rowKey="userId"
          columns={columns}
          scroll={{ x: 'max-content' }}
          dataSource={courseFileList}
          pagination={{ total: courseFileList.length }}
        />
      </Modal>
    </>
  );
}
