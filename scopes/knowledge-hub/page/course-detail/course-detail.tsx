import { EditOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { optional } from 'mobx-state-tree/dist/internal.js';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { TableExportOutlined } from '@manyun/base-ui.icons';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import type { SheetHeader } from '@manyun/base-ui.util.xlsx';
import type { Breadcrumb as LayoutBreadcrumb } from '@manyun/dc-brain.context.layout';
import { LayoutContent } from '@manyun/dc-brain.ui.layout';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import type {
  BackendCourseFile,
  MutateCourse,
} from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { fetchCourseWeb as fetchCourse } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { fetchCourseStudent } from '@manyun/knowledge-hub.service.dcexam.fetch-course-student';
import type { BackendCourseStudent } from '@manyun/knowledge-hub.service.dcexam.fetch-course-student';
import {
  BackendCourseType,
  BackendStudyStatus,
} from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { CourseImagesViewButton } from '@manyun/knowledge-hub.ui.course-images-view-button';
import { CoursesCategory } from '@manyun/knowledge-hub.ui.courses-category';
import { TagCoursesState } from '@manyun/knowledge-hub.ui.courses-state';
import { ReviewCourseModal } from '@manyun/knowledge-hub.ui.review-course-modal';
import { UploadCourseImagesModal } from '@manyun/knowledge-hub.ui.upload-course-images-modal';
import { SCH_MODE_TYPE_TEXT } from '@manyun/ticket.model.task';

import { CourseFileListModal } from './components/course-file-list-modal.js';

export function CourseDetail() {
  const { id } = useParams<{ id: string }>();
  const [, { checkUserId }] = useAuthorized();

  const [loading, setLoading] = useState(true);
  const [usersLoading, setUsersLoading] = useState(false);
  const [course, setCourse] = useState<MutateCourse>();
  const [students, setStudents] = useState<BackendCourseStudent[]>([]);
  const [originStudents, setOriginStudents] = useState<BackendCourseStudent[]>([]);
  const isOwner = course && checkUserId(course.ownerId);
  const [searchType, setSearchType] = useState<string[]>([]);
  const [searchStatus, setSearchStatus] = useState<string[]>([]);
  const [typeNum, setTypeNum] = useState<Record<string, number>>({
    total: 0,
    required: 0,
    optional: 0,
  });
  const [statusNum, setStatusNum] = useState<Record<string, number>>({
    total: 0,
    completed: 0,
    undone: 0,
    unInvolved: 0,
  });

  const TYPE_OPTIONS = useMemo(
    () => [
      {
        label: `全部 (${typeNum.total})`,
        value: 'all',
      },
      {
        label: `必修 (${typeNum.required})`,
        value: BackendCourseType.OBLIGATORY,
      },
      {
        label: `选修 (${typeNum.optional})`,
        value: BackendCourseType.ELECTIVE,
      },
    ],
    [typeNum]
  );

  const STATUS_OPTIONS = useMemo(
    () => [
      {
        label: `全部 (${statusNum.total})`,
        value: 'all',
      },
      {
        label: `已完成 (${statusNum.completed})`,
        value: BackendStudyStatus.COMPLETED,
      },
      {
        label: `未完成 (${statusNum.undone})`,
        value: BackendStudyStatus.UNDONE,
      },
      {
        label: `未参与 (${statusNum.unInvolved})`,
        value: BackendStudyStatus.UN_INVOLVED,
      },
    ],
    [statusNum]
  );

  const getExportColumns = (
    course: MutateCourse
  ): (ColumnType<BackendCourseStudent> & SheetHeader)[] => {
    return [
      {
        title: '学生姓名',
        dataIndex: 'userName',
        fixed: 'left',
        ellipsis: true,
        render(userName: string, { userId }: BackendCourseStudent) {
          return <UserLink userId={userId} userName={userName} />;
        },
      },
      {
        ellipsis: true,
        title: '学生角色',
        dataIndex: 'roleList',
        render(roleList: BackendCourseStudent['roleList']) {
          return roleList ? roleList?.join(' | ') : '';
        },
        stringify: (roleList: BackendCourseStudent['roleList']) =>
          roleList ? roleList?.join(' | ') : '',
      },
      {
        title: '类型',
        dataIndex: 'courseType',
        render: (courseType: string) => {
          return (
            <Tag color={generateCourseTypeTag(courseType).color}>
              {generateCourseTypeTag(courseType).label}
            </Tag>
          );
        },
        stringify: (courseType: string) => generateCourseTypeTag(courseType).label,
      },
      {
        title: '合理学习时效',
        dataIndex: 'gmtCreate',
        render(gmtCreate: string, record) {
          const studyTime = gmtCreate
            ? dayjs(record.endLearnTime ?? undefined).diff(dayjs(gmtCreate), 'day')
            : 0;
          return (!course?.optNotify && record.courseType === BackendCourseType.ELECTIVE) ||
            !course?.studySla ? (
            '无限制'
          ) : (
            <>
              <Typography.Text type={studyTime > course.studySla ? 'danger' : undefined}>
                {studyTime}
              </Typography.Text>
              {`/${course.studySla}`}
            </>
          );
        },
        stringify: (gmtCreate: string, record: BackendCourseStudent) =>
          (!course?.optNotify && record.courseType === BackendCourseType.ELECTIVE) ||
          !course?.studySla
            ? '无限制'
            : `${gmtCreate ? dayjs(record.endLearnTime ?? undefined).diff(dayjs(gmtCreate), 'day') : 0}/${course?.studySla}`,
      },
      {
        title: '状态',
        dataIndex: 'studyStatus',
        render(studyStatus: string) {
          return (
            <Tag color={generateStudyStatusTag(studyStatus).color}>
              {generateStudyStatusTag(studyStatus).label}
            </Tag>
          );
        },
        stringify: (studyStatus: string) => generateStudyStatusTag(studyStatus).label,
      },
      {
        ellipsis: true,
        title: '开始时间',
        dataIndex: 'startLearnTime',
        render(startLearnTime: number | null) {
          // 这是考生开始课程的时间，应精确到秒
          return generateCourseStudentTime(startLearnTime) || '--';
        },
        stringify: (startLearnTime: number | null) => generateCourseStudentTime(startLearnTime),
      },
      {
        ellipsis: true,
        title: '结束时间',
        dataIndex: 'endLearnTime',
        render(endLearnTime: number | null) {
          // 这是考生结束课程的时间，应精确到秒
          return generateCourseStudentTime(endLearnTime) || '--';
        },
        stringify: (endLearnTime: number | null) => generateCourseStudentTime(endLearnTime),
      },
      {
        ellipsis: true,
        title: '完成学习课件',
        dataIndex: 'completeCourseFileList',
        render(completeCourseFileList: BackendCourseFile[] | null) {
          return Array.isArray(completeCourseFileList) && completeCourseFileList.length > 0 ? (
            <CourseFileListModal courseFileList={completeCourseFileList} isComplete />
          ) : (
            0
          );
        },
        stringify: (completeCourseFileList: BackendCourseFile[] | null) =>
          Array.isArray(completeCourseFileList) && completeCourseFileList.length > 0
            ? completeCourseFileList.length.toString()
            : '0',
      },
      {
        ellipsis: true,
        title: '未完成学习课件',
        dataIndex: 'unCompleteCourseFileList',
        render(unCompleteCourseFileList: BackendCourseFile[] | null) {
          return Array.isArray(unCompleteCourseFileList) && unCompleteCourseFileList.length > 0 ? (
            <CourseFileListModal courseFileList={unCompleteCourseFileList} isComplete={false} />
          ) : (
            0
          );
        },
        stringify: (unCompleteCourseFileList: BackendCourseFile[] | null) =>
          Array.isArray(unCompleteCourseFileList) && unCompleteCourseFileList.length > 0
            ? unCompleteCourseFileList.length.toString()
            : '0',
      },
    ];
  };

  const getExtraColumns = (
    course: MutateCourse
  ): (ColumnType<BackendCourseStudent> & SheetHeader)[] => {
    return [
      {
        title: '重新测试次数',
        dataIndex: 'testCount',
        render(testCount) {
          return `${testCount ? testCount - 1 : 0}/${course?.mustPass ? '无限制' : (course?.retakeExamCount ?? 0)}`;
        },
        stringify: testCount =>
          `${testCount ? testCount - 1 : 0}/${course?.mustPass ? '无限制' : (course?.retakeExamCount ?? 0)}`,
      },
      {
        title: '答题时长',
        dataIndex: 'duration',
        ellipsis: true,
        render(_: unknown, { startAnswerTime, endAnswerTime }: BackendCourseStudent) {
          return generateCourseStudentDuration(startAnswerTime, endAnswerTime) || '--';
        },
        stringify: (_: unknown, { startAnswerTime, endAnswerTime }: BackendCourseStudent) =>
          generateCourseStudentDuration(startAnswerTime, endAnswerTime),
      },
      {
        title: '测试结果',
        dataIndex: 'pass',
        ellipsis: true,
        render(pass: boolean | null) {
          return typeof pass === 'boolean' ? (
            <Tag color={pass ? 'success' : 'error'}>{pass ? '通过' : '不通过'}</Tag>
          ) : (
            '--'
          );
        },
        stringify: (pass: boolean | null) => {
          if (typeof pass === 'boolean') {
            return pass ? '通过' : '不通过';
          } else {
            return '--';
          }
        },
      },
    ];
  };

  const columns = useMemo(() => {
    if (course) {
      const studentColumns = course.paperId
        ? [...getExportColumns(course), ...getExtraColumns(course)]
        : getExportColumns(course);
      return studentColumns.map(column =>
        Object.fromEntries(Object.entries(column).filter(([key]) => key !== 'stringify'))
      );
    }
    return [];
  }, [course]);

  const getStudents = useCallback(async () => {
    setUsersLoading(true);
    const { error, data } = await fetchCourseStudent({
      courseId: Number(id),
    });
    setUsersLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setStudents(data.data);
    setOriginStudents(data.data);
    const typeTotal = data?.data?.length || 0;
    const required =
      data?.data?.filter(item => item?.courseType === BackendCourseType.OBLIGATORY)?.length || 0;
    const optional =
      data?.data?.filter(item => item?.courseType === BackendCourseType.ELECTIVE)?.length || 0;

    setTypeNum({
      total: typeTotal,
      required,
      optional,
    });

    const statusTotal = data?.data?.length || 0;
    const completed =
      data?.data?.filter(item => item?.studyStatus === BackendStudyStatus.COMPLETED)?.length || 0;
    const undone =
      data?.data?.filter(item => item?.studyStatus === BackendStudyStatus.UNDONE)?.length || 0;
    const unInvolved =
      data?.data?.filter(item => item?.studyStatus === BackendStudyStatus.UN_INVOLVED)?.length || 0;

    setStatusNum({
      total: statusTotal,
      completed,
      undone,
      unInvolved,
    });
  }, [id]);

  const getCourse = useCallback(async () => {
    const { error, data } = await fetchCourse({
      courseId: Number(id),
    });
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      setCourse(data);
    }
    setLoading(false);
  }, [id]);

  useEffect(() => {
    getStudents();
    getCourse();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  useEffect(() => {
    if (!searchType.length && !searchStatus.length) {
      setStudents(originStudents);
      return;
    }

    const newType = searchType.includes('all') ? ['OBLIGATORY', 'ELECTIVE'] : searchType; //或的关系
    const newStatus = searchStatus.includes('all')
      ? ['COMPLETED', 'UNDONE', 'UN_INVOLVED']
      : searchStatus; //或的关系

    let newStudents = [...originStudents];
    if (newType.length) {
      newStudents = originStudents.filter(item => newType.includes(item.courseType));
    }

    if (newStatus.length) {
      newStudents = newStudents.filter(item => newStatus.includes(item.studyStatus));
    }

    setStudents(newStudents);
  }, [searchType, searchStatus]);

  if (loading || !course) {
    return (
      <div style={{ width: '100%', height: '100%' }}>
        <Spin style={{ margin: '0 auto' }} spinning />
      </div>
    );
  }

  return (
    <LayoutContent
      pageCode="page_knowledge_hub-course-detail"
      composeBreadcrumbs={(value: LayoutBreadcrumb[]) => [
        ...value,
        { key: 'course-detail', text: course?.name },
      ]}
    >
      <Space style={{ width: '100%', display: 'flex' }} direction="vertical">
        <Card bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              课程信息
            </Typography.Title>
            <Descriptions column={4}>
              <Descriptions.Item label="课程名称">{course.name}</Descriptions.Item>
              <Descriptions.Item label="课程分类">
                <CoursesCategory categoryCode={Number(course.categoryCode)} />
              </Descriptions.Item>
              <Descriptions.Item label="培训方式">
                {course.trainMode ? SCH_MODE_TYPE_TEXT[course.trainMode] : '--'}
              </Descriptions.Item>
              <Descriptions.Item label="开放时间">
                <Typography.Paragraph
                  ellipsis={{
                    rows: 1,
                    expandable: true,
                  }}
                >
                  {dayjs(course.openingHours[0]).format('YYYY-MM-DD HH:mm')} ~{' '}
                  {dayjs(course.openingHours[1]).format('YYYY-MM-DD HH:mm')}
                </Typography.Paragraph>
              </Descriptions.Item>
              <Descriptions.Item label="课程状态">
                <TagCoursesState typeVal={course.status} />
              </Descriptions.Item>
              <Descriptions.Item label="课件总数">{course.courseLength}份</Descriptions.Item>
              <Descriptions.Item label="需要随堂测试">
                {course.paperId ? '是' : '否'}
              </Descriptions.Item>
              <Descriptions.Item label="负责人">
                {course.ownerId && course.ownerName ? (
                  <UserLink userId={course.ownerId} userName={course.ownerName} />
                ) : (
                  '--'
                )}
              </Descriptions.Item>
              {course.paperId && (
                <>
                  <Descriptions.Item label="必须检测通过">
                    {course.mustPass ? '是' : '否'}
                  </Descriptions.Item>
                  <Descriptions.Item label="通知上级主管">
                    {course.failedExamCount ? `未通过${course.failedExamCount}次通知` : '否'}
                  </Descriptions.Item>
                </>
              )}
              <Descriptions.Item label="现场照片">
                {course.fileNum ? (
                  <CourseImagesViewButton courseId={Number(id)} fileNum={course.fileNum} />
                ) : (
                  0
                )}
                {isOwner && (
                  <UploadCourseImagesModal
                    style={{ padding: 0, height: 11 }}
                    courseId={Number(id)}
                    onSearch={getCourse}
                  >
                    <TableExportOutlined />
                  </UploadCourseImagesModal>
                )}
              </Descriptions.Item>
              <Descriptions.Item label="复盘总结" span={4}>
                <Space style={{ width: '100%' }}>
                  <Typography.Paragraph
                    style={{ marginBottom: 0 }}
                    ellipsis={{
                      rows: 1,
                      expandable: true,
                    }}
                  >
                    {course?.review ?? ''}
                  </Typography.Paragraph>
                  {isOwner &&
                    dayjs().diff(dayjs(course.openingHours[0])) > 0 &&
                    !course?.review && (
                      <ReviewCourseModal
                        courseId={Number(id)}
                        review={course?.review ?? ''}
                        onSearch={getCourse}
                      >
                        <EditOutlined />
                      </ReviewCourseModal>
                    )}
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </Space>
        </Card>
        <Card bordered={false}>
          <Space style={{ width: '100%' }} direction="vertical" size="middle">
            <Space style={{ justifyContent: 'space-between', width: '100%' }}>
              <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                学习信息
              </Typography.Title>
              {course?.name && (
                <FileExport
                  filename={`${course.name}学习信息.xls`}
                  disabled={students.length === 0}
                  data={async () => {
                    const newHeaders = (
                      course?.paperId
                        ? [...getExportColumns(course), ...getExtraColumns(course)]
                        : getExportColumns(course)
                    ).map(column => ({
                      dataIndex: column.dataIndex,
                      title: column.title,
                      stringify: column.stringify,
                    }));
                    saveJSONAsXLSX<BackendCourseStudent>(
                      [
                        {
                          data: students,
                          headers: newHeaders,
                        },
                      ],
                      `${course.name}学习信息`
                    );
                  }}
                />
              )}
            </Space>
            <Space size={100}>
              <div>
                <>类型：</>
                <Checkbox.Group
                  options={TYPE_OPTIONS}
                  defaultValue={['all']}
                  onChange={(val: any) => setSearchType(val)}
                />
              </div>
              <div>
                <>状态：</>
                <Checkbox.Group
                  options={STATUS_OPTIONS}
                  defaultValue={['all']}
                  onChange={(val: any) => setSearchStatus(val)}
                />
              </div>
            </Space>
            <Table
              rowKey="userId"
              loading={usersLoading}
              columns={[...columns]}
              scroll={{ x: 'max-content' }}
              dataSource={students}
              pagination={{ total: Array.isArray(students) ? students.length : 0 }}
            />
          </Space>
        </Card>
      </Space>
    </LayoutContent>
  );
}

function generateCourseStudentTime(time: number | null) {
  return time !== null ? dayjs(time).format('YYYY-MM-DD HH:mm:ss') : '';
}

function generateCourseStudentDuration(startTime?: number | null, endTime?: number | null) {
  return startTime ? `${dayjs(endTime || Date.now()).diff(dayjs(startTime), 'minutes')}分钟` : '';
}

function generateStudyStatusTag(studyStatus: string) {
  switch (studyStatus) {
    case BackendStudyStatus.COMPLETED:
      return { label: '已完成', color: 'success' };
    case BackendStudyStatus.UNDONE:
      return { label: '未完成', color: 'processing' };
    case BackendStudyStatus.UN_INVOLVED:
      return { label: '未参与', color: 'error' };
    default:
      return { label: '--', color: 'default' };
  }
}

function generateCourseTypeTag(courseType: string) {
  switch (courseType) {
    case BackendCourseType.OBLIGATORY:
      return { label: '必修', color: 'success' };
    case BackendCourseType.ELECTIVE:
      return { label: '选修', color: 'warning' };
    default:
      return { label: '--', color: 'default' };
  }
}
