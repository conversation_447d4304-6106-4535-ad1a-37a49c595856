import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Form } from '@manyun/base-ui.ui.form';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { PAPERS_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { PaperMutatorRouteParams } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { Paper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  MutatePaperType,
  createPaperAction,
  getPaperAction,
  papersSliceActions,
  selectMutatePaper,
  selectMutatePaperSectionsEntities,
  updatePapersAction,
} from '@manyun/knowledge-hub.state.papers';
import {
  CollapsePaperQuestionsGroupList,
  StatisticPaperQuestionsGroupList,
} from '@manyun/knowledge-hub.ui.paper-questions-group-list';

import styles from './paper-mutator.module.less';

export type PaperMutatorProps = {};

export function PaperMutator() {
  const history = useHistory();
  const dispatch = useDispatch();
  const [form] = Form.useForm();
  const [sectionForm] = Form.useForm();

  const { id } = useParams<PaperMutatorRouteParams>();
  const mutateType: MutatePaperType =
    id !== undefined ? MutatePaperType.UpdatePaper : MutatePaperType.CreatePaper;
  const exsitingPaper = useSelector(selectMutatePaper(mutateType));
  const paperSections = useSelector(
    selectMutatePaperSectionsEntities(mutateType, exsitingPaper.sectionIds)
  );
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);

  const groupListRef = React.createRef<{
    setActiveKey: (key: string) => void;
  }>();

  useEffect(() => {
    setLoading(false);
    form.setFieldsValue({
      allowArbitraryOptionsOrder: exsitingPaper.allowArbitraryOptionsOrder,
      allowArbitraryQuestionsOrder: exsitingPaper.allowArbitraryQuestionsOrder,
      orderByQuestionTypeInSections: exsitingPaper.orderByQuestionTypeInSections,
    });
    /**刷新页面时重新加载数据 */
    if (id !== undefined && (exsitingPaper as Paper).id === -1) {
      dispatch(
        getPaperAction({
          paperId: Number(id),
          callback: res => {
            if (res != null) {
              dispatch(
                papersSliceActions.setInitialUpdatePaperFields({
                  ...res,
                })
              );
              form.setFieldsValue({
                allowArbitraryOptionsOrder: res.allowArbitraryOptionsOrder,
                allowArbitraryQuestionsOrder: res.allowArbitraryQuestionsOrder,
                orderByQuestionTypeInSections: res.orderByQuestionTypeInSections,
              });
            }
          },
        })
      );
    }
  }, [dispatch, exsitingPaper, form, id]);

  const onSubmit = useCallback(async () => {
    setSubmitting(true);
    if (id !== undefined) {
      dispatch(
        updatePapersAction({
          paper: exsitingPaper as Paper,
          callback: res => {
            setSubmitting(false);
            if (res) {
              history.push(PAPERS_ROUTE_PATH);
            }
          },
        })
      );
    } else {
      dispatch(
        createPaperAction({
          paper: exsitingPaper as Omit<Paper, 'id'>,
          callback: res => {
            setSubmitting(false);
            if (res) {
              history.push(PAPERS_ROUTE_PATH);
            }
          },
        })
      );
    }
  }, [id, dispatch, exsitingPaper, history]);

  if (loading) {
    return <Skeleton active />;
  }
  return (
    <div style={{ width: '100%', height: '100%' }} className={styles.paperMutator}>
      <Card bordered={false} bodyStyle={{ padding: '20px' }}>
        <Form
          form={form}
          layout="inline"
          initialValues={{
            allowArbitraryOptionsOrder: exsitingPaper.allowArbitraryOptionsOrder,
            allowArbitraryQuestionsOrder: exsitingPaper.allowArbitraryQuestionsOrder,
            orderByQuestionTypeInSections: exsitingPaper.orderByQuestionTypeInSections,
          }}
          onValuesChange={values => {
            dispatch(
              papersSliceActions.setMutatePaperFields({ mutateType: mutateType, ...values })
            );
          }}
        >
          <Form.Item name="allowArbitraryOptionsOrder" valuePropName="checked" noStyle>
            <Checkbox>选项乱序</Checkbox>
          </Form.Item>
          <Form.Item name="allowArbitraryQuestionsOrder" valuePropName="checked" noStyle>
            <Checkbox>试题乱序</Checkbox>
          </Form.Item>
          <Form.Item name="orderByQuestionTypeInSections" valuePropName="checked" noStyle>
            <Checkbox>按试题类型排序</Checkbox>
          </Form.Item>
        </Form>
      </Card>
      <Form
        name="mutatePaper"
        form={sectionForm}
        style={{
          height: '100%',
          width: '100%',
          display: 'flex',
          flexDirection: 'column',
          overflow: 'auto',
        }}
        scrollToFirstError
        onFinishFailed={error => {
          // 当有错误时，自动滚动到第一个错误位置
          const errorName = error.errorFields[0].name.toString();
          if (groupListRef.current) {
            /** 开启所在板块的折叠面板 */
            const str = errorName.split('_');
            const groupId = str[0];
            groupListRef.current.setActiveKey(groupId);
          }
          sectionForm.scrollToField(errorName, {
            behavior: actions => {
              actions.forEach(({ el, top, left }) => {
                el.scrollTop = top;
                el.scrollLeft = left;
              });
            },
            block: 'center',
            inline: 'center',
          });
        }}
      >
        <Space className={styles.paperContent} align="start">
          <Card title="试卷信息" bordered={false}>
            <StatisticPaperQuestionsGroupList
              form={sectionForm}
              mutateType={mutateType}
              paperType={exsitingPaper.genFun}
              paper={{
                totalScore: exsitingPaper.totalScore || 0,
                totalSize: exsitingPaper.totalQuestionSize || 0,
                groups: paperSections.map((section, key) => ({
                  id: section.id,
                  name: section.name,
                  totalScore: section.totalScore || 0,
                  totalSize: section.totalSize || 0,
                  gradeList: Array.isArray(section.questions)
                    ? section.questions.map(question => question.grade ?? 0)
                    : [],
                  questionIds: Array.isArray(section.questions)
                    ? section.questions.map(question => question.id)
                    : [],
                })),
              }}
              onDelete={id => {
                dispatch(
                  papersSliceActions.setMutatePaperFields({
                    mutateType,
                    deleteSectionId: id,
                  })
                );
              }}
              onSort={(id, type) => {
                dispatch(
                  papersSliceActions.sortOrderMutatePaperSection({
                    mutateType,
                    type: type,
                    sectionId: id,
                  })
                );
              }}
            />
          </Card>
          <Card bordered={false}>
            <CollapsePaperQuestionsGroupList
              ref={groupListRef}
              form={sectionForm}
              mutateType={mutateType}
              paperType={exsitingPaper.genFun}
              groups={paperSections}
              paperMarkingWay={exsitingPaper.markingWay.toString() === '1' ? true : undefined}
            />
          </Card>
        </Space>
        <Card style={{ textAlign: 'center' }} bordered={false} bodyStyle={{ padding: 10 }}>
          <Space style={{ display: 'inline-flex' }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={submitting}
              onClick={() => {
                onSubmit();
              }}
            >
              提交
            </Button>
            <Button
              onClick={() => {
                history.goBack();
              }}
            >
              返回
            </Button>
          </Space>
        </Card>
      </Form>
    </div>
  );
}
