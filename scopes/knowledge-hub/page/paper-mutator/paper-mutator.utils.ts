import { SectionTypeConfig } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export const initialRandomTypeConfig: Record<string, SectionTypeConfig> = {
  ESSAY: {
    score: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
      NORMAL: 0,
    },
  },
  MULTIPLE_CHOICE: {
    score: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
      NORMAL: 0,
    },
  },
  SHORT_ANSWER: {
    score: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
      NORMAL: 0,
    },
  },
  SINGLE_CHOICE: {
    score: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
      NORMAL: 0,
    },
  },
  TRUE_OR_FALSE: {
    score: 0,
    difficultCount: {
      EASIER_THAN_EASY: 0,
      EASY: 0,
      HARD: 0,
      HARDER_THAN_HARD: 0,
      NORMAL: 0,
    },
  },
};
