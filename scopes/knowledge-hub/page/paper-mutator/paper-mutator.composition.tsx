import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EDIT_SPECIFIC_PAPER_ROUTE_PATH,
  NEW_PAPER_ROUTE_PATH,
  PAPERS_ROUTE_PATH,
  generateEditSpecificPaperRoutePath,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { PaperMutator } from './paper-mutator';

const PapersMock = () => {
  const history = useHistory();
  return (
    <>
      <span>PAPERS</span>
      <button
        onClick={() => {
          history.goBack();
        }}
      >
        GO BACK
      </button>
    </>
  );
};

export const NewPaperMutator = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });

    // getQuestionsMock(mock);
    // getPaperMock(mock);
    // getCountMock(mock);
    // getCategoryMock(mock);
    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[NEW_PAPER_ROUTE_PATH]}>
          <Switch>
            <Route path={NEW_PAPER_ROUTE_PATH} exact>
              <PaperMutator />
            </Route>
            <Route path={PAPERS_ROUTE_PATH} exact>
              <PapersMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

export const EditSpecificPaper = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });

    // getQuestionsMock(mock);
    // getPaperMock(mock);
    // getCountMock(mock);
    // getCategoryMock(mock);
    // mutatePapersMock(mock);
    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateEditSpecificPaperRoutePath(23)]}>
          <Switch>
            <Route path={EDIT_SPECIFIC_PAPER_ROUTE_PATH} exact>
              <PaperMutator />
            </Route>
            <Route path={PAPERS_ROUTE_PATH} exact>
              <PapersMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

export const SelectSpecificPaper = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });

    // getQuestionsMock(mock);
    // getPaperMock(mock);
    // getCountMock(mock);
    // getCategoryMock(mock);
    // mutatePapersMock(mock);

    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateEditSpecificPaperRoutePath(22)]}>
          <Switch>
            <Route path={EDIT_SPECIFIC_PAPER_ROUTE_PATH} exact>
              <PaperMutator />
            </Route>
            <Route path={PAPERS_ROUTE_PATH} exact>
              <PapersMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

export const RandomSpecificPaper = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });

    // getQuestionsMock(mock);
    // getPaperMock(mock);
    // getCountMock(mock);
    // getCategoryMock(mock);
    // mutatePapersMock(mock);

    // mock.onAny().networkError();
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[generateEditSpecificPaperRoutePath(24)]}>
          <Switch>
            <Route path={EDIT_SPECIFIC_PAPER_ROUTE_PATH} exact>
              <PaperMutator />
            </Route>
            <Route path={PAPERS_ROUTE_PATH} exact>
              <PapersMock />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
