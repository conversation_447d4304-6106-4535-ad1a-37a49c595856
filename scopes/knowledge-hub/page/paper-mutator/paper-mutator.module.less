.paperMutator {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 8;

  .paperContent {
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: auto;
    margin: 8px 0;

    > :global(.manyun-space-item) {
      height: 100%;
      &:first-child {
        width: 375px;
        > * {
          height: 100%;
          > :global(.manyun-card-body) {
            position: relative;
            height: calc(100% - /* Card Head height */ 58px);
            overflow-y: auto;
          }
        }
      }

      &:last-child {
        flex: 1;
        height: 100%;
        > * {
          height: 100%;
          > :global(.manyun-card-body) {
            position: relative;
            height: calc(100% - /* Card Head height */ 10px);
            overflow-y: auto;
            padding: 15px 15px 5px;
          }
        }
      }
    }
  }

  :global {
    .manyun-collapse > .manyun-collapse-item > .manyun-collapse-header {
      align-items: baseline;
    }
    .manyun-form-item {
      margin-bottom: 0px !important;
    }
    .manyun-form .manyun-form-item {
      margin-right: 4px !important;
    }
  }
}
