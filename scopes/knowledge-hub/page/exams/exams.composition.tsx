import React from 'react';
import { <PERSON>, Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EDIT_SPECIFIC_EXAM_ROUTE_PATH,
  EXAMS_ROUTE_PATH,
  EXAM_ROUTE_PATH,
  NEW_EXAM_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { Exams } from './exams';

export const BasicExams = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchKnowledgeHubCategoryMocks(mock);
    // registerCancelExamMocks(mock);
    // registerFetchExamsMocks(mock);
    // registerMutateExamsMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={[EXAMS_ROUTE_PATH]}>
          <Switch>
            <Route path={EDIT_SPECIFIC_EXAM_ROUTE_PATH}>
              <h1>EDIT SPECIFIC EXAM</h1>
              <Link to={EXAMS_ROUTE_PATH}>GO BACK</Link>
            </Route>
            <Route path={EXAM_ROUTE_PATH}>
              <h1>SPECIFIC EXAM</h1>
              <Link to={EXAMS_ROUTE_PATH}>GO BACK</Link>
            </Route>
            <Route path={NEW_EXAM_ROUTE_PATH}>
              <h1>NEW EXAM</h1>
              <Link to={EXAMS_ROUTE_PATH}>GO BACK</Link>
            </Route>
            <Route path={EXAMS_ROUTE_PATH}>
              <Exams />
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
