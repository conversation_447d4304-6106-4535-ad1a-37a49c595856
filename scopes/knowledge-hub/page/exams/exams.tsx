import React, { useCallback, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import dayjs from 'dayjs';
import pick from 'lodash.pick';
import moment from 'moment';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { User } from '@manyun/auth-hub.ui.user';
import { ExamTypes, useExams } from '@manyun/knowledge-hub.hook.use-exams';
import { useExamsFields } from '@manyun/knowledge-hub.hook.use-exams-fields';
import { useExamsSelected } from '@manyun/knowledge-hub.hook.use-exams-selected';
import {
  NEW_EXAM_ROUTE_PATH,
  generateEditSpecificExamRoutePath,
  generateExamRoutePath,
  generateNewExamURL,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { addExamUsers } from '@manyun/knowledge-hub.service.dcexam.add-exam-users';
import type { BackendExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { BackendExamStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import type { ExamFields } from '@manyun/knowledge-hub.state.exams';
import { ExamsBatchUpdator } from '@manyun/knowledge-hub.ui.exams-batch-updator';
import { ExamsCategory, ExamsCategoryCascader } from '@manyun/knowledge-hub.ui.exams-category';
import { ExamsState } from '@manyun/knowledge-hub.ui.exams-state';
import { ExmasCanceller } from '@manyun/knowledge-hub.ui.exmas-canceller';
import { GroupUsersPicker } from '@manyun/knowledge-hub.ui.group-users-picker';
import type { Users } from '@manyun/knowledge-hub.ui.group-users-picker';

export type ExamsProps = {};

type ExamsFilterFormValues = {
  timeRange?: [Moment, Moment];
  examStatus?: BackendExamStatus[] | BackendExamStatus;
  examName?: string;
  categoryCode?: number[];
};

export function Exams() {
  const [form] = Form.useForm<ExamsFilterFormValues>();
  const { search } = useLocation();
  const examsStatus = getLocationSearchMap<{ ['status']?: BackendExamStatus[] }>(search)['status'];
  const [getExams, { loading, data, fields }] = useExams(ExamTypes.AllExam);
  const [setFields, { timeRange, examName, categoryCode, page, pageSize }] = useExamsFields(
    ExamTypes.AllExam
  );
  const [setSelectedIds, { selectedIds }] = useExamsSelected();
  const history = useHistory();
  const [examUsers, setExamUsers] = useState<Users>({
    requiredUsers: { userKeys: [], roleKeys: [] },
    optionalUsers: { userKeys: [], roleKeys: [] },
  });
  const [usersLoading, setUsersLoading] = useState(false);

  React.useEffect(() => {
    form.setFieldsValue({
      timeRange:
        fields.timeRange && fields.timeRange.length
          ? [moment(fields.timeRange[0]), moment(fields.timeRange[1])]
          : undefined,
      examStatus: fields.examStatus,
      examName: fields.examName,
      categoryCode: fields.categoryCode,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const addStudents = useCallback(
    async (id: number) => {
      setUsersLoading(true);
      const { error } = await addExamUsers({
        id,
        defaultUserConfig: {
          users: examUsers.requiredUsers.userKeys,
          roles: examUsers.requiredUsers.roleKeys,
        },
        optionalUserConfig: {
          users: examUsers.optionalUsers.userKeys,
          roles: examUsers.optionalUsers.roleKeys,
        },
      });
      setUsersLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setExamUsers({
        requiredUsers: { userKeys: [], roleKeys: [] },
        optionalUsers: { userKeys: [], roleKeys: [] },
      });
      getExams();
    },
    [examUsers]
  );

  useShallowCompareEffect(() => {
    setFields({ examStatus: examsStatus });
    form.setFieldsValue({
      timeRange: timeRange?.length === 2 ? [moment(timeRange[0]), moment(timeRange[1])] : undefined,
      examStatus: examsStatus,
      examName,
      categoryCode,
    });
    getExams();
  }, [examsStatus]);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card bordered={false}>
        <Form
          layout="inline"
          colon={false}
          form={form}
          onValuesChange={(changedValues: ExamsFilterFormValues) => {
            const fields: Partial<ExamFields> = pick<
              ExamsFilterFormValues,
              keyof Omit<ExamsFilterFormValues, 'timeRange'>
            >(changedValues, ['examStatus', 'examName', 'categoryCode']);
            if (changedValues.timeRange) {
              fields.timeRange = [
                changedValues.timeRange[0].clone().startOf('day').valueOf(),
                changedValues.timeRange[1].clone().endOf('day').valueOf(),
              ];
            }
            if (changedValues.timeRange === null) {
              fields.timeRange = undefined;
            }
            setFields(fields);
          }}
        >
          <Form.Item label="考试时间" name="timeRange">
            <DatePicker.RangePicker style={{ width: 270 }} allowClear />
          </Form.Item>
          <Form.Item label="状态" name="examStatus">
            <ExamsState
              style={{ width: 180 }}
              variant="select"
              allowClear
              mode="multiple"
              maxTagCount="responsive"
            />
          </Form.Item>
          <Form.Item label="考试名称" name="examName">
            <Input style={{ width: 200 }} allowClear placeholder="请输入考试名称关键字" />
          </Form.Item>
          <Form.Item label="考试分类" name="categoryCode">
            <ExamsCategoryCascader style={{ width: 200 }} allowClear />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                loading={loading}
                onClick={() => {
                  setFields({ page: 1 });
                  getExams();
                }}
              >
                搜索
              </Button>
              <Button
                disabled={loading}
                onClick={() => {
                  form.resetFields();
                  setFields({
                    page: 1,
                    timeRange: undefined,
                    examName: undefined,
                    examStatus: undefined,
                    categoryCode: undefined,
                  });
                  getExams();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      <Card bordered={false}>
        <Space style={{ width: '100%' }} direction="vertical">
          <Space>
            <Button
              type="primary"
              disabled={loading}
              onClick={() => {
                history.push(NEW_EXAM_ROUTE_PATH);
              }}
            >
              创建考试
            </Button>
            <ExamsBatchUpdator disabled={loading || selectedIds.length <= 0} />
          </Space>
          <Table
            rowKey="id"
            loading={loading}
            scroll={{ x: 'max-content' }}
            columns={[
              {
                ellipsis: true,
                title: '考试名称',
                dataIndex: 'name',
                fixed: 'left',
                render(name, { id }) {
                  return <Link to={generateExamRoutePath(id)}>{name}</Link>;
                },
              },
              {
                ellipsis: true,
                title: '考试分类',
                dataIndex: 'categoryCode',
                render(categoryCode: string) {
                  return <ExamsCategory categoryCode={Number(categoryCode)} />;
                },
              },
              {
                title: '考试时长',
                dataIndex: 'duration',
                render(duration) {
                  return `${duration}min`;
                },
              },
              {
                width: 80,
                title: '总分',
                dataIndex: 'totalGrade',
              },
              {
                title: '状态',
                dataIndex: 'status',
                render(examState) {
                  return <ExamsState variant="tag" examState={examState} />;
                },
              },
              {
                title: '考试时间',
                dataIndex: '_time',
                render(_, { startTime, endTime }) {
                  const formatStyle = 'YYYY-MM-DD HH:mm';

                  return `${moment(startTime).format(formatStyle)}~${moment(endTime).format(
                    formatStyle
                  )}`;
                },
              },
              {
                title: '更新人',
                dataIndex: 'modifierName',
                render(name, { modifierId }) {
                  return <User.Link id={modifierId} name={name} />;
                },
              },
              {
                title: '更新时间',
                dataIndex: 'gmtModified',
                sorter: true,
                render(gmtModified) {
                  return moment(gmtModified).format('YYYY-MM-DD HH:mm:ss');
                },
              },
              {
                title: '操作',
                fixed: 'right',
                dataIndex: '__actions',
                render(_, exam: BackendExam) {
                  const allowMutate = exam.status === BackendExamStatus.INIT;
                  const allowCancel = [BackendExamStatus.INIT, BackendExamStatus.PROCESS].includes(
                    exam.status
                  );
                  const allowAddUsers =
                    dayjs().diff(dayjs(exam.endTime), 'minutes') + exam.duration < 0;

                  return (
                    <>
                      {allowMutate && (
                        <Link to={generateEditSpecificExamRoutePath(exam.id)}>修改</Link>
                      )}
                      {allowMutate && allowCancel && <Divider type="vertical" />}
                      {allowCancel && <ExmasCanceller exam={exam} />}
                      {allowCancel && <Divider type="vertical" />}
                      <Link to={generateNewExamURL({ templateId: exam.id.toString() })}>复制</Link>
                      {allowAddUsers && <Divider type="vertical" />}
                      {allowAddUsers && (
                        <GroupUsersPicker
                          requiredText="必考人员设置"
                          optionalText="选考人员设置"
                          buttonText="添加考生"
                          buttonType="link"
                          value={examUsers}
                          showDepts={false}
                          buttonLoading={usersLoading}
                          onChange={value => {
                            setExamUsers(value);
                          }}
                          onReset={() =>
                            setExamUsers({
                              requiredUsers: { userKeys: [], roleKeys: [] },
                              optionalUsers: { userKeys: [], roleKeys: [] },
                            })
                          }
                          onSubmit={() => addStudents(exam.id)}
                        />
                      )}
                    </>
                  );
                },
              },
            ]}
            dataSource={data.data}
            pagination={{ total: data.total, current: page, pageSize }}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange(ids) {
                setSelectedIds(ids as number[]);
              },
            }}
            onChange={(pagination, _, sorter, { action }) => {
              if (action === 'paginate') {
                setFields({
                  page: pagination.current,
                  pageSize: pagination.pageSize,
                });
              } else if (action === 'sort') {
                if (sorter && !Array.isArray(sorter)) {
                  setFields({
                    page: pagination.current,
                    pageSize: pagination.pageSize,
                    orderByModifyTimeDesc:
                      sorter.order === 'ascend' ? false : sorter.order === 'descend' ? true : null,
                  });
                }
              }
              getExams();
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}
