import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks as fetchExamsWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
// import { registerWebMocks as registerFetchKnowledgeHubCategoryMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
// import { registerWebMocks as fetchExamFailedUsersWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users';
// import { registerWebMocks as requestMakeupTestWebMocks } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { PendingMarkExams } from './pending-mark-exams';

export const BasicPendingMarkExams = () => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // registerFetchKnowledgeHubCategoryMocks(mock);
    // fetchExamsWebMocks(mock);
    // fetchExamFailedUsersWebMocks(mock);
    // requestMakeupTestWebMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';

    update(true);
  }, []);

  if (!initialized) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router>
          <PendingMarkExams />
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
