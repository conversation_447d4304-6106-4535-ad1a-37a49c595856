import React, { useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import moment from 'moment';
import type { Moment } from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { generateExamRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import type { BackendExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  BackendExamStatus,
  Variant as ExamVariant,
  fetchExamsWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  examsSliceActions,
  selectAllowMarkingExamSearchValues,
} from '@manyun/knowledge-hub.state.exams';
import { ExamsCategory, ExamsCategoryCascader } from '@manyun/knowledge-hub.ui.exams-category';
import { ExamsState } from '@manyun/knowledge-hub.ui.exams-state';
import { MakeUpExam } from '@manyun/knowledge-hub.ui.make-up-exam';

export type PendingMarkExamsProps = {};

export function PendingMarkExams() {
  const [form] = Form.useForm();
  const [dataSource, setDataSource] = useState<{ data: BackendExam[]; total: number }>({
    data: [],
    total: 0,
  });
  const [loading, setLoading] = useState(false);
  const dispatch = useDispatch();
  const { page, pageSize, categoryCode, examName, examStatus, timeRange } = useSelector(
    selectAllowMarkingExamSearchValues()
  );

  React.useEffect(() => {
    form.setFieldsValue({
      categoryCode,
      examName,
      examStatus,
      timeRange,
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getPendingMarkExams = async () => {
    setLoading(true);
    const values = form.getFieldsValue();
    const params = {
      variant: ExamVariant.AllowMarkingExam,
      page,
      pageSize,
      timeRange:
        values.timeRange && values.timeRange.length
          ? (values.timeRange.map((m: Moment) => m.clone().seconds(0).valueOf()) as [
              number,
              number,
            ])
          : undefined,
      examStatus: values.examStatus,
      examName: values.examName?.trim(),
      categoryCode:
        values.categoryCode && values.categoryCode.length
          ? values.categoryCode[values.categoryCode.length - 1]
          : undefined,
    };
    const { data, error } = await fetchExamsWeb(params);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setDataSource({ data: data.data, total: data.total });
  };

  React.useEffect(() => {
    getPendingMarkExams();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [page, pageSize]);

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card bordered={false}>
        <Form
          layout="inline"
          colon={false}
          form={form}
          onValuesChange={values => {
            dispatch(examsSliceActions.setAllowMarkingExamSearchValues(values));
          }}
        >
          <Form.Item label="考试时间" name="timeRange">
            <DatePicker.RangePicker
              style={{ width: 350 }}
              showTime
              format="YYYY-MM-DD HH:mm"
              placeholder={['开始时间', '结束时间']}
            />
          </Form.Item>
          <Form.Item label="状态" name="examStatus">
            <ExamsState
              style={{ width: 120 }}
              variant="select"
              allowClear
              filterKeys={['INIT', 'CANCEL']}
            />
          </Form.Item>
          <Form.Item label="考试名称" name="examName">
            <Input allowClear placeholder="请输入考试名称关键字" style={{ minWidth: 180 }} />
          </Form.Item>
          <Form.Item label="考试分类" name="categoryCode">
            <ExamsCategoryCascader style={{ width: 200 }} />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                loading={loading}
                onClick={() => {
                  dispatch(
                    examsSliceActions.setAllowMarkingExamSearchValues({
                      page: 1,
                    })
                  );
                  getPendingMarkExams();
                }}
              >
                搜索
              </Button>
              <Button
                disabled={loading}
                onClick={() => {
                  dispatch(
                    examsSliceActions.setAllowMarkingExamSearchValues({
                      timeRange: undefined,
                      examStatus: undefined,
                      examName: undefined,
                      categoryCode: undefined,
                      page: 1,
                      pageSize: 10,
                    })
                  );
                  form.resetFields();
                  getPendingMarkExams();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Card>
      <Card bordered={false}>
        <Table
          size="small"
          rowKey="id"
          loading={loading}
          scroll={{ x: 'max-content' }}
          columns={[
            {
              title: '考试名称',
              dataIndex: 'name',
              fixed: 'left',
              render: (txt, { id }: BackendExam) => {
                return <Link to={generateExamRoutePath(id)}>{txt}</Link>;
              },
            },
            {
              title: '考试分类',
              dataIndex: 'categoryCode',
              render: txt => <ExamsCategory categoryCode={Number(txt)} />,
            },
            {
              title: '总分',
              dataIndex: 'totalGrade',
            },
            {
              title: '交卷人数',
              dataIndex: 'submitUserCount',
              render: (txt, { totalUserCount }) => `${txt || 0}/${totalUserCount}`,
            },
            {
              title: '通过人数',
              dataIndex: 'passUserCount',
              render: (txt, { totalUserCount }) => `${txt || 0}/${totalUserCount}`,
            },
            {
              title: '状态',
              dataIndex: 'status',
              render(examState) {
                return examState ? <ExamsState variant="tag" examState={examState} /> : '--';
              },
            },
            {
              title: '考试时间',
              dataIndex: '_time',
              render(_, { startTime, endTime }) {
                const formatStyle = 'YYYY-MM-DD HH:mm';

                return `${moment(startTime).format(formatStyle)}~${moment(endTime).format(
                  formatStyle
                )}`;
              },
            },
            {
              title: '操作',
              dataIndex: '__actions',
              fixed: 'right',
              render(
                _,
                { id, name, status, passUserCount, totalUserCount, allowCorrect }: BackendExam
              ) {
                const allowMark =
                  allowCorrect &&
                  [BackendExamStatus.PROCESS, BackendExamStatus.CORRECT].includes(status);
                return (
                  <>
                    {allowMark && <Link to={generateExamRoutePath(id)}>判卷</Link>}
                    {status === BackendExamStatus.GRADED && passUserCount !== totalUserCount && (
                      <>
                        <MakeUpExam examId={id} examName={name} />
                        <Divider type="vertical" />
                      </>
                    )}

                    {!allowMark && status !== BackendExamStatus.GRADED && '--'}
                  </>
                );
              },
            },
          ]}
          dataSource={dataSource.data}
          pagination={{
            total: dataSource.total,
            current: page,
            pageSize: pageSize,
          }}
          onChange={({ current, pageSize }) => {
            dispatch(
              examsSliceActions.setAllowMarkingExamSearchValues({
                page: current,
                pageSize: pageSize,
              })
            );
          }}
        />
      </Card>
    </Space>
  );
}
