import React, { useEffect, useState } from 'react';
import { Link, Route, MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  EXAM_PAPER_RESULT_PAGE_URL,
  generateExamPaperResultLocation,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroy as destroyMock, webRequest } from '@manyun/service.request';

import { ExamPaperResult } from './exam-paper-result';

export const BasicExamPaperResult = () => {
  const [initialized, update] = useState(false);

  useEffect(() => {
    // const mock = getMock('web', { delayResponse: 777 });
    // fetchExamResultWebMocks(mock);
    // fetchExamWebMocks(mock);
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return <span>Loading</span>;
  }

  return (
    <ConfigProvider>
      <Router initialEntries={['/']}>
        <Route path={'/'} exact>
          <Link
            to={generateExamPaperResultLocation({ id: (2109).toString(), examId: (52).toString() })}
          >
            考完不能查看试卷,通过考试
          </Link>
          <hr />
          <Link
            to={generateExamPaperResultLocation({ id: (2).toString(), examId: (2).toString() })}
          >
            考完可以查看试卷，考试结束待判卷
          </Link>
          <hr />
          <Link
            to={generateExamPaperResultLocation({ id: (2).toString(), examId: (2).toString() })}
          >
            考试结束待判卷
          </Link>
          <hr />
          <Link
            to={generateExamPaperResultLocation({ id: (2096).toString(), examId: (37).toString() })}
          >
            考试已完成，未通过
          </Link>
        </Route>
        <Route path={EXAM_PAPER_RESULT_PAGE_URL}>
          <FakeStore>
            <ExamPaperResult />
          </FakeStore>
        </Route>
      </Router>
    </ConfigProvider>
  );
};
