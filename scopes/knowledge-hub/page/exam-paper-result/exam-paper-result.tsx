import React, { useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { message } from '@manyun/base-ui.ui.message';
import { Result } from '@manyun/base-ui.ui.result';
import { Space } from '@manyun/base-ui.ui.space';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { generateExamPaperLocation } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { fetchExamWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam';
import { fetchExamPaperWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { ExamResult } from '@manyun/knowledge-hub.ui.exam-result';

export type ExamPaperResultProps = {};

export function ExamPaperResult({}: ExamPaperResultProps) {
  const [exam, setExam] = useState<any>(null);
  const [examPaper, setExamPaper] = useState<any>(null);
  const params: any = useParams();
  const history = useHistory();
  const [, { checkUserId }] = useAuthorized();

  const examPaperId = params.id;
  const examId = params.examId;

  useEffect(() => {
    if (!examPaperId && examPaperId !== 0) {
      return;
    }
    (async () => {
      const { data, error } = await fetchExamPaperWeb({ examPaperId: Number(examPaperId) });
      if (error) {
        message.error(error.message);
        return;
      }
      setExamPaper(data);
    })();
  }, [examPaperId]);

  /** 当前登录用户是否是当前考卷的考生 */
  const isExaminee = checkUserId(examPaper?.userId);

  useEffect(() => {
    if (!isExaminee || (!examId && examId !== 0)) {
      return;
    }
    (async () => {
      const { data, error } = await fetchExamWeb({ examId: Number(examId) });
      if (error) {
        message.error(error.message);
        return;
      }
      setExam(data);
    })();
  }, [examId, isExaminee]);

  if (!examPaper) {
    return <>loading...</>;
  }

  if (!isExaminee) {
    return <Result status="403" title="403" subTitle="抱歉，你无权查看当前考试的结果！" />;
  }

  return (
    <Space style={{ width: '100%' }} size="large" direction="vertical">
      <Card>
        <ExamResult id={examPaperId} examId={examId} showRank={true} />
      </Card>
      <div style={{ width: '100%', textAlign: 'center' }}>
        <Space>
          {exam && exam.allowReviewPaperAfterHandedIn && (
            <Button
              type="primary"
              onClick={() => {
                history.push(generateExamPaperLocation({ id: examPaperId, examId }));
              }}
            >
              查看试卷
            </Button>
          )}
          <Button onClick={() => history.goBack()}>返回</Button>
        </Space>
      </div>
    </Space>
  );
}
