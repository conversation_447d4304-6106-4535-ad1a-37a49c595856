/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export type ApiQ = {
  id: number;
};

export type ApiR = Response<BackendPaper>;

export type ServiceQ = {
  paperId: number;
  needDetail?: boolean /**是否需要获取试卷 试题的详情 */;
};

export type ServiceR = BackendPaper | null;
