/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-paper';
import type { ApiQ, ApiR } from './fetch-paper.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  //选题模式
  webMock
    .onPost(endpoint, {
      id: 0,
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        id: 0, //试卷id
        type: 'SELECT', //试卷类型：SELECT("选题"),DRAW("抽题"),RANDOM("随机")
        categoryCode: '1001', //试题分类
        name: '测试试卷', //试卷名称
        totalGrade: 100, //总分
        properties: {
          randomOption: false, //选项乱序
          randomQuestion: false, //试题乱序
          groupByType: false, //按照试题类型排序
        },
        sections: [
          //板块题目
          {
            section: '测试板块', //板块名称
            questions: [
              //题目列表
              {
                id: 1,
                type: 3,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
                answers: ['A', 'B', 'C'],
                userAnswers: null,
                analysis: '这个题目明显选择 A B C',
                autoGrade: false,
                correct: false,
                grade: 20,
                userGrade: 0,
              },
              {
                id: 2,
                type: 1,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['测试选项1', '测试选项2', '测试选项3', '测试选项4'],
                answers: ['测试选项1', '测试选项2', '测试选项3'],
                userAnswers: null,
                analysis: '我是分析',
                autoGrade: false,
                correct: false,
                grade: 20,
                userGrade: 0,
              },
            ] as BackendSectionQuestion[],
          },
          {
            section: '测试板块', //板块名称
            questions: [
              //题目列表
              {
                id: 1,
                type: 3,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
                answers: ['A', 'B', 'C'],
                userAnswers: null,
                analysis: '这个题目明显选择 A B C',
                autoGrade: false,
                correct: false,
                grade: 80,
                userGrade: 0,
              },
            ] as BackendSectionQuestion[],
          },
        ],
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, {
      //抽题模式
      id: 1,
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        id: 1, //试卷id
        type: 'DRAW', //试卷类型：SELECT("选题"),DRAW("抽题"),RANDOM("随机")
        categoryCode: '1001', //试题分类
        name: '测试试卷', //试卷名称
        totalGrade: 100, //总分
        properties: {
          randomOption: false, //选项乱序
          randomQuestion: false, //试题乱序
          groupByType: false, //按照试题类型排序
        },
        sections: [
          //板块题目
          {
            section: '测试板块', //板块名称
            questions: [
              //题目列表
              {
                id: 1,
                type: 3,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
                answers: ['A', 'B', 'C'],
                userAnswers: null,
                analysis: '这个题目明显选择 A B C',
                autoGrade: false,
                correct: false,
                userGrade: 0,
              },
              {
                id: 2,
                type: 3,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
                answers: ['A', 'B', 'C'],
                userAnswers: null,
                analysis: '这个题目明显选择 A B C',
                autoGrade: false,
                correct: false,
                userGrade: 0,
              },
            ] as BackendSectionQuestion[],
          },
          {
            section: '测试板块', //板块名称
            questions: [
              //题目列表
              {
                id: 1,
                type: 3,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
                answers: ['A', 'B', 'C'],
                userAnswers: null,
                analysis: '这个题目明显选择 A B C',
                autoGrade: false,
                correct: false,
                userGrade: 0,
              },
            ] as BackendSectionQuestion[],
          },
        ],
        drawRules: [
          //抽题规则
          {
            section: '测试板块', //板块名称
            questionDrawRules: [
              //试题抽题规则
              {
                type: 1, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 5, //试题数量
              },
              {
                type: 2, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 4, //试题数量
              },
              {
                type: 3, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 3, //试题数量
              },
              {
                type: 4, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 2, //试题分数
                number: 2, //试题数量
              },
              {
                type: 5, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 2, //试题分数
                number: 1, //试题数量
              },
            ],
          },
          {
            section: '测试板块', //板块名称
            questionDrawRules: [
              //试题抽题规则
              {
                type: 1, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 3, //试题数量
              },
              {
                type: 2, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 3, //试题数量
              },
              {
                type: 3, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 3, //试题数量
              },
              {
                type: 4, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 3, //试题数量
              },
              {
                type: 5, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                number: 3, //试题数量
              },
            ],
          },
        ],
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, {
      id: 2,
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        id: 2, //试卷id
        type: 'RANDOM', //试卷类型：SELECT("选题"),DRAW("抽题"),RANDOM("随机")
        categoryCode: '1001', //试题分类
        name: '测试试卷', //试卷名称
        totalGrade: 100, //总分
        properties: {
          randomOption: false, //选项乱序
          randomQuestion: false, //试题乱序
          groupByType: false, //按照试题类型排序
        },
        drawRules: [
          //抽题规则
          {
            section: '测试板块', //板块名称
            questionDrawRules: [
              //试题抽题规则
              {
                type: 1, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                randomNumber: {
                  //抽题难度及数量
                  '1': 10,
                  '2': 10,
                  '3': 10,
                  '4': 10,
                  '5': 10,
                },
              },
              {
                type: 2, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                randomNumber: {
                  //抽题难度及数量
                  '1': 10,
                  '2': 10,
                  '3': 10,
                  '4': 10,
                  '5': 10,
                },
              },
              {
                type: 3, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                randomNumber: {
                  //抽题难度及数量
                  '1': 10,
                  '2': 10,
                  '3': 10,
                  '4': 10,
                  '5': 10,
                },
              },
              {
                type: 4, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                randomNumber: {
                  //抽题难度及数量
                  '1': 10,
                  '2': 10,
                  '3': 10,
                  '4': 10,
                  '5': 10,
                },
              },
              {
                type: 5, //1-单选；2-多选；3-判断；4-填空；5-问答
                grade: 3, //试题分数
                randomNumber: {
                  //抽题难度及数量
                  '1': 10,
                  '2': 10,
                  '3': 10,
                  '4': 10,
                  '5': 10,
                },
              },
            ],
            categoryCodeList: [1, 2, 3], //题目类型
          },
        ],
      },
      errCode: null,
      errMessage: null,
    });
}
