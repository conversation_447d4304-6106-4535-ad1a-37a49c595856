/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchPaper as webService } from './fetch-paper.browser';
import { registerWebMocks } from './fetch-paper.mock';

// registerWebMocks();
test('toMatch paper detail which mode is SELECT', async () => {
  const { data, error } = await webService({ paperId: 0 });
  const selectPaper = {
    id: 0,
    name: '测试试卷',
    categoryCode: '1001',
    genFun: 'SELECT',
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    orderByQuestionTypeInSections: false,
    sectionIds: [0, 1],
    sectionEntities: {
      '0': {
        id: 0,
        name: '测试板块',
        totalSize: 1,
        totalScore: 20,
        questions: [
          {
            id: 1,
            type: 3,
            categoryCode: '1001',
            difficulty: 1,
            title: '测试题目1',
            options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
            answers: ['A', 'B', 'C'],
            userAnswers: null,
            analysis: '这个题目明显选择 A B C',
            autoGrade: false,
            correct: false,
            grade: 20,
            userGrade: 0,
          },
        ],
        questionIds: [1],
      },
      '1': {
        id: 1,
        name: '测试板块',
        totalSize: 1,
        totalScore: 80,
        questions: [
          {
            id: 1,
            type: 3,
            categoryCode: '1001',
            difficulty: 1,
            title: '测试题目1',
            options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
            answers: ['A', 'B', 'C'],
            userAnswers: null,
            analysis: '这个题目明显选择 A B C',
            autoGrade: false,
            correct: false,
            grade: 80,
            userGrade: 0,
          },
        ],
        questionIds: [1],
      },
    },
    totalScore: 100,
  };
  // expect(data).toMatchObject(selectPaper);
  // expect(error).toBe(undefined);
});

test('toMatch paper detail which mode is DRAW ', async () => {
  const { data, error } = await webService({ paperId: 1 });
  const drawPaper = {
    id: 1,
    name: '测试试卷',
    categoryCode: '1001',
    genFun: 'DRAW',
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    orderByQuestionTypeInSections: false,
    sectionIds: [0, 1],
    sectionEntities: {
      '0': {
        id: 0,
        name: '测试板块',
        totalSize: 15,
        totalScore: 45,
        questions: [
          {
            id: 1,
            type: 3,
            categoryCode: '1001',
            difficulty: 1,
            title: '测试题目1',
            options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
            answers: ['A', 'B', 'C'],
            userAnswers: null,
            analysis: '这个题目明显选择 A B C',
            autoGrade: false,
            correct: false,
            grade: 20,
            userGrade: 0,
          },
        ],
        questionIds: [1],
        typeConfig: {
          SINGLE_CHOICE: { score: 3, drawTotal: 3 },
          MULTIPLE_CHOICE: { score: 3, drawTotal: 3 },
          TRUE_OR_FALSE: { score: 3, drawTotal: 3 },
          SHORT_ANSWER: { score: 3, drawTotal: 3 },
          ESSAY: { score: 3, drawTotal: 3 },
        },
      },
      '1': {
        id: 1,
        name: '测试板块',
        totalSize: 15,
        totalScore: 45,
        questions: [
          {
            id: 1,
            type: 3,
            categoryCode: '1001',
            difficulty: 1,
            title: '测试题目1',
            options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
            answers: ['A', 'B', 'C'],
            userAnswers: null,
            analysis: '这个题目明显选择 A B C',
            autoGrade: false,
            correct: false,
            grade: 80,
            userGrade: 0,
          },
        ],
        questionIds: [1],
        typeConfig: {
          SINGLE_CHOICE: { score: 3, drawTotal: 3 },
          MULTIPLE_CHOICE: { score: 3, drawTotal: 3 },
          TRUE_OR_FALSE: { score: 3, drawTotal: 3 },
          SHORT_ANSWER: { score: 3, drawTotal: 3 },
          ESSAY: { score: 3, drawTotal: 3 },
        },
      },
    },
    totalScore: 100,
    totalQuestionSize: 30,
  };
  // expect(data).toMatchObject(drawPaper);
  // expect(error).toBe(undefined);
});

test('toMatch paper detail which mode is RANDOM ', async () => {
  const { data, error } = await webService({ paperId: 2 });
  const randomPaper = {
    id: 2,
    name: '测试试卷',
    categoryCode: '1001',
    genFun: 'RANDOM',
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    orderByQuestionTypeInSections: false,
    sectionIds: [0],
    sectionEntities: {
      '0': {
        id: 0,
        name: '测试板块',
        totalSize: 250,
        totalScore: 750,
        typeConfig: {
          SINGLE_CHOICE: {
            score: 3,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          MULTIPLE_CHOICE: {
            score: 3,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          TRUE_OR_FALSE: {
            score: 3,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SHORT_ANSWER: {
            score: 3,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          ESSAY: {
            score: 3,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
        },
      },
    },
    totalScore: 100,
    totalQuestionSize: 250,
  };
  // expect(data).toMatchObject(randomPaper);
  // expect(error).toBe(undefined);
});
