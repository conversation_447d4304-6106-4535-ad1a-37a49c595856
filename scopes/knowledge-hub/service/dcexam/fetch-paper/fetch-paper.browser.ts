/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  BackendDrawRule,
  BackendPaper,
  BackendPapersGenFun,
  Paper,
  PaperSection,
  SectionTypeConfig,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { fetchPreviewPaperWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-preview-paper';
import {
  BackendQuestion,
  BackendQuestionDifficulty,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-paper';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-paper.type';

export const addNumUtil = (num1: number, num2: number): number => {
  let sq1: number = 0;
  let sq2: number = 0;
  let m: number = 0;
  try {
    sq1 = num1.toString().split('.')[1].length;
  } catch (error) {
    sq1 = 0;
  }
  try {
    sq2 = num2.toString().split('.')[1].length;
  } catch (error) {
    sq2 = 0;
  }
  m = Math.pow(10, Math.max(sq1, sq2));
  return (num1 * m + num2 * m) / m;
};

/**
 * 知识中心-试卷管理-试卷详情.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/ipn7nw)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchPaper({ paperId, needDetail = false }: ServiceQ) {
  const apiQ: ApiQ = { id: paperId };
  const { data, error, ...rest } = await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
  if (!error && data != null) {
    const paper: Paper = convertBackendPaperToPaper(data);
    /** 选题模式下加载预览试卷 */
    if (paper.genFun == BackendPapersGenFun.SELECT && needDetail) {
      const { data, error, ...rest } = await fetchPreviewPaperWeb({ paperId });
      if (!error && data != null) {
        (data.questionsGroups || []).forEach((group, key) => {
          paper.sectionEntities[key].questions = group.questionIds.map(
            id => group.questionEntities[id]
          );
        });
      }
    }

    return {
      ...rest,
      error,
      data: paper,
    };
  } else {
    return { data: null, error, ...rest };
  }
}

/**
 *
 * @param data: BackendPaper
 * @returns Paper
 */
function convertBackendPaperToPaper(data: BackendPaper): Paper {
  const sectionEntities: Record<number, PaperSection> = {};
  const sectionIds: number[] = [];
  let totalQuestionSize: number = 0; //总试题数
  //选题、抽题模式
  (data.sections || []).map((section, key) => {
    let typeConfig: Record<string, SectionTypeConfig> = {};
    let totalScore =
      data.type === BackendPapersGenFun.SELECT
        ? (section.questions as BackendSectionQuestion[]).reduce((sum, question) => {
            return addNumUtil(sum, question.grade);
          }, 0)
        : 0;

    let totalSize = data.type === BackendPapersGenFun.SELECT ? section.questions?.length : 0;

    /**抽题模式 */
    if (data.type == BackendPapersGenFun.DRAW) {
      typeConfig = convertToTypeConfig(key, section.section, data.drawRules as BackendDrawRule[]);
      /** 抽题模式需要统计每种题型每个难度的题目数量 */
      if (data.type == BackendPapersGenFun.DRAW && section.questions !== undefined) {
        const typeConfigs = getTypeDifficultyCountByQuestions(
          section.questions as BackendQuestion[]
        );
        Object.keys(typeConfigs).forEach(key => {
          typeConfig[key].difficultCount = typeConfigs[key].difficultCount;
        });
      }

      /** 统计各个模块的总分数、总题目数量 */
      const { totalScore: sectionScore, totalSize: sectionSize } = getSectionTotalByTypeConfig(
        typeConfig,
        false
      );
      totalScore = sectionScore;
      totalSize = sectionSize;
    }

    sectionIds.push(key);
    sectionEntities[key] = {
      id: key,
      name: section.section,
      totalSize,
      totalScore,
      questions: section.questions as BackendSectionQuestion[],
      questionIds: (section.questions as BackendSectionQuestion[]).map(question => question.id),
      typeConfig,
    };

    totalQuestionSize += Number(totalSize);
  });
  //随机模式
  if (!data.sections || data.sections?.length == 0) {
    (data.drawRules || []).map((rule, key) => {
      const typeConfig = convertToTypeConfig(
        key,
        rule.section,
        data.drawRules as BackendDrawRule[]
      );
      const { totalSize, totalScore } = getSectionTotalByTypeConfig(typeConfig, true);
      sectionIds.push(key);
      sectionEntities[key] = {
        id: key,
        name: rule.section,
        totalSize,
        totalScore,
        typeConfig,
        questionCategory: (rule.categoryCodeList as number[])[0], // categoryCodeList 是个数组
      };
      totalQuestionSize += totalSize;
    });
  }
  const paper: Paper = {
    id: data.id,
    name: data.name,
    markingWay: data.autoGrade ? 1 : 0,
    categoryCode: data.categoryCode,
    genFun: (BackendPapersGenFun as any)[data.type],
    allowArbitraryOptionsOrder: data.properties?.randomOption || false,
    allowArbitraryQuestionsOrder: data.properties?.randomQuestion || false,
    orderByQuestionTypeInSections: data.properties?.groupByType || false,
    sectionIds: sectionIds,
    sectionEntities: sectionEntities,
    totalScore: data.totalGrade,
    totalQuestionSize,
  };
  return paper;
}

/**
 *
 * @param questions 试题数量统计 类型的难度数量
 * @returns
 */
export function getTypeDifficultyCountByQuestions(questions: BackendQuestion[]) {
  const typeConfigs: Record<string, SectionTypeConfig> = getInitalTypeConfig();
  (questions || []).forEach((question, key) => {
    if (question.type != null) {
      const typeConfigDifficultCount = typeConfigs[BackendQuestionType[question.type]]
        .difficultCount as any;
      typeConfigDifficultCount[BackendQuestionDifficulty[question.difficulty]] =
        typeConfigDifficultCount[BackendQuestionDifficulty[question.difficulty]] + 1;
    }
  });
  return typeConfigs;
}

/**
 * 抽题或随机模式下计算板块试题总数量、总分数
 * @param typeconfig
 * @returns totalSize
 */
export function getSectionTotalByTypeConfig(
  typeconfig: Record<string, SectionTypeConfig>,
  isDifficultCount: boolean
) {
  let totalSize: number = 0;
  let totalScore: number = 0;
  Object.keys(typeconfig).map(key => {
    const difficultCount = typeconfig[key].difficultCount;
    if (isDifficultCount && difficultCount) {
      Object.keys(difficultCount).map(countKey => {
        totalSize += Number(difficultCount[countKey]);
        totalScore = addNumUtil(
          Number((difficultCount[countKey] * typeconfig[key].score).toFixed(1)),
          totalScore
        );
      });
    } else {
      totalSize += Number(typeconfig[key].drawTotal);
      totalScore = addNumUtil(
        Number(typeconfig[key].drawTotal) * Number(typeconfig[key].score),
        totalScore
      );
    }
  });
  return { totalSize, totalScore };
}
/**
 *
 * @param key 对应的section的索引
 * @param sectionName 对应的section 名称
 * @param drawRules 后台返回的抽题规则
 * @returns SectionTypeConfig
 */
export function convertToTypeConfig(
  key: number,
  sectionName: string,
  drawRules: BackendDrawRule[]
): Record<string, SectionTypeConfig> {
  const sectionRules = drawRules[key];
  const typeConfig: Record<string, SectionTypeConfig> = getInitalTypeConfig();
  if (sectionRules.section === sectionName) {
    sectionRules.questionDrawRules.map((rule, key) => {
      (typeConfig as any)[(BackendQuestionType as any)[rule.type]] = {
        score: rule.grade || 0, // 分数
        drawTotal: rule.number || 0, //总抽题数
      };
      if (rule.randomNumber) {
        //抽题难度和数量规则
        const difficultCount: any = {};
        (Object.keys(rule.randomNumber) || []).map(key => {
          difficultCount[(BackendQuestionDifficulty as any)[key]] = rule.randomNumber
            ? rule.randomNumber[key]
            : undefined;
        });
        (typeConfig as any)[(BackendQuestionType as any)[rule.type]]['difficultCount'] =
          difficultCount;
      }
    });
  }
  return typeConfig;
}

/**
 *
 * @returns 初始化的typeConifg
 */
export function getInitalTypeConfig() {
  const initialDrwaOrRandomTypeConfig: Record<string, SectionTypeConfig> = {
    SINGLE_CHOICE: {
      score: 0,
      drawTotal: 0,
      difficultCount: {
        EASIER_THAN_EASY: 0,
        EASY: 0,
        NORMAL: 0,
        HARD: 0,
        HARDER_THAN_HARD: 0,
      },
    },
    MULTIPLE_CHOICE: {
      score: 0,
      drawTotal: 0,
      difficultCount: {
        EASIER_THAN_EASY: 0,
        EASY: 0,
        NORMAL: 0,
        HARD: 0,
        HARDER_THAN_HARD: 0,
      },
    },
    TRUE_OR_FALSE: {
      score: 0,
      drawTotal: 0,
      difficultCount: {
        EASIER_THAN_EASY: 0,
        EASY: 0,
        NORMAL: 0,
        HARD: 0,
        HARDER_THAN_HARD: 0,
      },
    },
    SHORT_ANSWER: {
      score: 0,
      drawTotal: 0,
      difficultCount: {
        EASIER_THAN_EASY: 0,
        EASY: 0,
        NORMAL: 0,
        HARD: 0,
        HARDER_THAN_HARD: 0,
      },
    },
    ESSAY: {
      score: 0,
      drawTotal: 0,
      difficultCount: {
        EASIER_THAN_EASY: 0,
        EASY: 0,
        NORMAL: 0,
        HARD: 0,
        HARDER_THAN_HARD: 0,
      },
    },
  };
  return initialDrwaOrRandomTypeConfig;
}
