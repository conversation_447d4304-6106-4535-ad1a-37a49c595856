/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiD = {
  id: number;
  defaultUserConfig: {
    mode: number;
    roles?: string[];
    users?: string[];
  };
  endTime: number | null;
  startTime: number | null;
  name: string;
};

export type UserInfoApiQ = {
  userIds: number[];
};

export type ApiR = WriteResponse;

export type ServiceD = {
  examId: number;
  roleIds?: string[];
  userIds?: string[];
  timeRange: [number, number];
  name: string;
};

export type ServiceR = boolean;
