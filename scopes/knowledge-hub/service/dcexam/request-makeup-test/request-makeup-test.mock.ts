/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { UserInfoApiQ } from '.';
import { endpoint } from './request-makeup-test';
import type { ApiD, ApiR } from './request-makeup-test.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      id: 1,
      defaultUserConfig: {
        mode: 1,
        roles: ['1'],
        users: ['1', '2'],
      },
      endTime: 1631763527582,
      startTime: 1631763527582,
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });

  webMock
    .onPost(endpoint, {
      id: 1,
      defaultUserConfig: {
        mode: 2,
        roles: ['1'],
      },
      endTime: 1631763527582,
      startTime: 1631763527582,
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });

  webMock
    .onPost(endpoint, {
      id: 1,
      defaultUserConfig: {
        mode: 3,
        users: ['1', '2'],
      },
      endTime: 1631763527582,
      startTime: 1631763527582,
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });

  webMock.onPost(endpoint).reply<ApiR>(200, {
    success: true,
    errCode: null,
    errMessage: null,
  });
}
