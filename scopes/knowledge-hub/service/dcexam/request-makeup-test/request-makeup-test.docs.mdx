---
description: '知识中心-考试管理-发起补考'
labels: ['service', 'http', 'request-makeup-test']
---

## requestMakeupTest

### Node Usage

```ts
import { requestMakeupTest } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test/dist/index.node';

const { data, error } = await requestMakeupTest();
```

### Browser Usage

#### 发起补考

```ts
import { requestMakeupTest } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test/dist/index.browser';

const { data, error } = await requestMakeupTest({
  examId: 1,
  roleIds: ['1'],
  userIds: ['1', '2'],
  timeRange: [1631763527582, 1631763527582],
});
```

```ts
import { requestMakeupTest } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test/dist/index.browser';

const { data, error } = await requestMakeupTest({
  examId: 1,
  roleIds: ['1'],
  timeRange: [1631763527582, 1631763527582],
});
```

```ts
import { requestMakeupTest } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test/dist/index.browser';

const { data, error } = await requestMakeupTest({
  examId: 1,
  userIds: ['1', '2'],
  timeRange: [1631763527582, 1631763527582],
});
```
