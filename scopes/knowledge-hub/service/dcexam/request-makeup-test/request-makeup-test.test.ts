/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
// import './request-makeup-test.mock';
import { requestMakeupTest as webService } from './request-makeup-test.browser';
import { registerWebMocks } from './request-makeup-test.mock';

// registerWebMocks();

test('request makeup test which mode is all users', async () => {
  const { data, error } = await webService({
    examId: 1,
    roleIds: ['1'],
    userIds: ['1', '2'],
    timeRange: [1631763527582, 1631763527582],
  } as any);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('request makeup test which mode is custom roles', async () => {
  const { data, error } = await webService({
    examId: 1,
    roleIds: ['1'],
    timeRange: [1631763527582, 1631763527582],
  } as any);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('request makeup test which mode is  custom users', async () => {
  const { data, error } = await webService({
    examId: 1,
    userIds: ['1', '2'],
    timeRange: [1631763527582, 1631763527582],
  } as any);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
