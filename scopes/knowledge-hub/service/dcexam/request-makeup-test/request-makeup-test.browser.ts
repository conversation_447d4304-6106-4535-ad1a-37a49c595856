/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './request-makeup-test';
import type { ApiD, ServiceD, ServiceR } from './request-makeup-test.type';

/**
 * 知识中心-考试管理-发起补考
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/czymor)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function requestMakeupTest({ examId, roleIds, userIds, timeRange, name }: ServiceD) {
  const userLen = userIds?.length || 0;
  const roleLen = roleIds?.length || 0;
  const apiQ: ApiD = {
    id: examId,
    defaultUserConfig: {
      mode: userLen > 0 && roleLen == 0 ? 3 : roleLen > 0 && userLen == 0 ? 2 : 1,
      roles: roleIds,
      users: userIds,
    },
    endTime: timeRange ? timeRange[1] : null,
    startTime: timeRange ? timeRange[0] : null,
    name,
  };
  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiQ);
}
