/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ApiD, ServiceD, ServiceR } from './request-makeup-test.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function requestMakeupTest(data: ServiceD) {
  throw new Error("requestMakeupTest's node version not implemented!");
}
