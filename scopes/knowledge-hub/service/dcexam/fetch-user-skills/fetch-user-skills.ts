/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './fetch-user-skills.type';

const endpoint = '/dcexam/cert/user/list';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/15/interface/api/26895)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    args: ApiArgs
  ): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> => {
    const { error, data, ...rest } = await request.tryPost<
      Pick<ApiResponse, 'data' | 'total'> | null,
      ApiArgs
    >(endpoint, args);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
