/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-17
 *
 * @packageDocumentation
 */

import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-skills';
import type { ApiArgs, ApiResponse } from './fetch-user-skills.type';

const executor = getExecutor(webRequest);

/**
 * @param args
* @returns
 */
export function fetchUserSkills(args: ApiArgs): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
