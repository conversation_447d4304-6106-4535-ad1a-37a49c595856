/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { SkillsLevel } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';

export type ApiArgs = {
  /**
   * 用户id
   */
  userId: number;
  /**
   * 证书分类
   */
  categoryCodeList?: string[] | null;
  /**
   * 是否拥有
   */
  own?: boolean | null;
};

export type BackendUserSkill = {
  id: number;
  /**
   * 等级
   */
  level: SkillsLevel;
  /**
   * 名称
   */
  name: string;
  /**
   * 证书分类
   */
  categoryCode: string;
  /**
   * 过期时间
   */
  expireTime?: string | null;
  /**
   * 是否过期
   */
  expire?: boolean | null;
  /**
   * 是否拥有
   */
  own: boolean;
  /**
   * 是否展示
   */
  relateExam: boolean;
};

export type ApiResponse = ListResponse<BackendUserSkill[]>;
