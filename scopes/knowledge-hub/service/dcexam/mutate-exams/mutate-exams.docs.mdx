---
description: '知识中心-考试管理-新增、更新、批量更新考试.'
labels: ['service', 'http', 'mutate-exams']
---

## mutateExams

### Node Usage

```ts
import { mutateExams } from '@manyun/knowledge-hub.service.dcexam.mutate-exams/dist/index.node';

const { data, error } = await mutateExams();
```

### Browser Usage

#### 批量更新考试

```ts
import { mutateExams } from '@manyun/knowledge-hub.service.dcexam.mutate-exams/dist/index.browser';

const { data, error } = await mutateExams([
  {
    id: 1,
    categoryCode: '1001',
  },
  {
    id: 2,
    categoryCode: '1001',
  },
  {
    id: 3,
    categoryCode: '1001',
  },
]);
```

#### 更新考试

```ts
import { mutateExams } from '@manyun/knowledge-hub.service.dcexam.mutate-exams/dist/index.browser';

const { data, error } = await mutateExams({
  allowDisplayRanking: true,
  allowDisplayRankingList: true,
  allowDisplayResult: true,
  allowReviewPaperAfterHandedIn: false,
  id: 1,
  requiredRoleIds: [],
  requiredUserIds: ['1', '2', '3'],
  markRoleIds: ['ADMIN'],
  markUserIds: [],
  optionalRoleIds: ['ADMIN'],
  optionalUserIds: [],
  paperId: 1,
  scopes: ['EC01', 'EC02'],
  timeRange: [1631768065830, 1631768065830],
  type: 'FORMAL',
  useSameMasterPaper: false,
  categoryCode: '10001',
  name: '测试考试',
  totalGrade: 100,
  passingScore: 60,
  timeLimit: 120,
});
```

#### 创建考试

```ts
import { mutateExams } from '@manyun/knowledge-hub.service.dcexam.mutate-exams/dist/index.browser';

const { data, error } = await mutateExams({
  {
      allowDisplayRanking: true,
      allowDisplayRankingList: true,
      allowDisplayResult: true,
      allowReviewPaperAfterHandedIn: false,
      requiredRoleIds: [],
      requiredUserIds: [ "1","2","3"],
      markRoleIds: ['ADMIN'],
      markUserIds: [],
      optionalRoleIds: [ "ADMIN"],
      optionalUserIds: [],
      paperId: 1,
      scopes: ['EC01', 'EC02'],
      timeRange: [1631768065830, 1631768065830],
      type: "FORMAL",
      useSameMasterPaper: false,
      categoryCode: '10001',
      name: '测试考试',
      totalGrade: 100,
      passingScore: 60,
      timeLimit: 120,
    }
});
```
