/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

import { mutateExams as webService } from './mutate-exams.browser';
import { registerWebMocks } from './mutate-exams.mock';

// registerWebMocks();

test('update exams', async () => {
  const { data, error } = await webService([
    {
      id: 1,
      categoryCode: 9,
    },
    {
      id: 2,
      categoryCode: 9,
    },
    {
      id: 3,
      categoryCode: 9,
    },
  ]);

  expect(typeof data).toBe('boolean');
  expect(error).toBe(undefined);
});

const mockExam: Omit<Exam, 'id'> = {
  allowReviewPaperAfterHandedIn: false,
  requiredRoleIds: [],
  requiredUserIds: [1],
  markRoleIds: [1],
  markUserIds: [],
  optionalRoleIds: [1],
  optionalUserIds: [],
  paperId: 1,
  scopes: ['EC01', 'EC02'],
  timeRange: [1631768065830, 1631768065830],
  type: 'FORMAL' as any,
  useSameMasterPaper: false,
  categoryCode: 9,
  name: '测试考试',
  totalGrade: 100,
  passingScore: 60,
  timeLimit: 120,
  sections: [{ questions: [{ id: 1 }, { id: 2 }], section: '测试板块' }],
  allowMark: true,
};

test('update one exam', async () => {
  const { data, error } = await webService({
    id: 1,
    ...mockExam,
  });

  expect(typeof data).toBe('boolean');
  expect(error).toBe(undefined);
});

test('create an exam', async () => {
  const { data, error } = await webService(mockExam);
  expect(typeof data).toBe('boolean');
  expect(error).toBe(undefined);
});
