/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { createExamEndpoint, updateBatchExamEndPoint, updateExamEndpoint } from './mutate-exams';
import type { ApiR } from './mutate-exams.type';

export function registerWebMocks(mock: ReturnType<typeof getMock>) {
  mock
    .onPost(new RegExp(`${updateExamEndpoint}|${updateBatchExamEndPoint}`))
    .reply(() => {
      const randomExamId = Math.floor(Math.random() * 10);
      const result = randomExamId > 5;

      return [
        200,
        {
          success: result,
          errCode: null,
          errMessage: result ? null : '更新考试失败！',
        },
      ];
    })

    .onPost(createExamEndpoint)
    .reply(() => {
      const randomExamId = Math.floor(Math.random() * 10);
      const result = randomExamId > 5;

      return [
        200,
        {
          success: result,
          data: result ? randomExamId : null,
          errCode: null,
          errMessage: result ? null : '创建考试失败！',
        },
      ];
    });
}
