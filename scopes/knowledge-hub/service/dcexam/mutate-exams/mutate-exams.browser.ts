/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import {
  BackendExamDetail,
  BackendUserConfig,
  Exam,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { webRequest } from '@manyun/service.request';

import { createExamEndpoint, updateBatchExamEndPoint, updateExamEndpoint } from './mutate-exams';
import type {
  CreateApiD,
  ServiceD,
  ServiceR,
  UpdateApiD,
  UpdateBatchApiD,
} from './mutate-exams.type';

/**
 *知识中心-考试管理-新增、更新、批量更新考试.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/uc23br、https://manyun.yuque.com/ewe5b3/zcferb/rsgfi8、https://manyun.yuque.com/ewe5b3/zcferb/cg0vz9)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateExams(data: ServiceD) {
  if (Array.isArray(data)) {
    //批量更新
    const _data: UpdateBatchApiD = {
      examIds: data.map(({ id }) => id),
      categoryCode: data[0].categoryCode,
    };
    return await webRequest.tryPost<ServiceR, UpdateBatchApiD>(updateBatchExamEndPoint, _data);
  } else {
    if (data.id !== undefined && data.id !== null) {
      //更新
      const exam: BackendExamDetail = convertExamToBackendExam(data) as BackendExamDetail;
      return await webRequest.tryPost<ServiceR, UpdateApiD>(updateExamEndpoint, exam);
    } else {
      //创建
      const exam = convertExamToBackendExam(data);
      return await webRequest.tryPost<ServiceR, CreateApiD>(createExamEndpoint, exam);
    }
  }
}

function convertExamToBackendExam(exam: Partial<Exam>): Partial<BackendExamDetail> {
  const mutateExam: Partial<BackendExamDetail> = {
    id: exam.id,
    name: exam.name,
    categoryCode: exam.categoryCode,
    type: exam.type,
    startTime: exam.timeRange![0],
    endTime: exam.timeRange![1],
    duration: exam.timeLimit,
    passGrade: exam.passingScore,
    defaultUserConfig: convertUserConfig(exam.requiredUserIds, exam.requiredRoleIds),
    optionalUserConfig: convertUserConfig(exam.optionalUserIds, exam.optionalRoleIds),
    correctUserConfig: convertUserConfig(exam.markUserIds, exam.markRoleIds),
    scope: exam.scopes,
    totalGrade: exam.totalGrade,
    paperId: exam.paperId,
    examProperties: {
      allowShowExamPaper: !!exam.allowReviewPaperAfterHandedIn,
      sameExamPaper: !!exam.useSameMasterPaper,
    },
  };

  return mutateExam;
}

function convertUserConfig(userIds?: number[], roleIds?: number[]): BackendUserConfig {
  const users = userIds?.length || 0;
  const roles = roleIds?.length || 0;
  const userConfig: BackendUserConfig = {
    // WTF? @YJX
    mode: users > 0 && roles == 0 ? 3 : roles > 0 && users == 0 ? 2 : 1,
    users: users > 0 ? userIds?.map(String) : undefined,
    roles: roles > 0 ? roleIds?.map(String) : undefined,
  };
  return userConfig;
}
