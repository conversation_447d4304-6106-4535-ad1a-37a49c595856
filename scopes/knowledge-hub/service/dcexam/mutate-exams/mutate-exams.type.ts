/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendExamDetail, Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

export type CreateApiD = Partial<BackendExamDetail>;

export type UpdateApiD = BackendExamDetail;

export type UpdateBatchApiD = {
  examIds: number[] | string[];
  categoryCode: number;
};

export type ApiR = WriteResponse;

export type ServiceD = Partial<Exam> | Pick<Exam, 'id' | 'categoryCode'>[];

export type ServiceR = /* exam id */ number;
