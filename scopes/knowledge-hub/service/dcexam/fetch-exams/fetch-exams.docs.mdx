---
description: '知识中心-考试管理-考试列表(所有考试、可判卷考试)'
labels: ['service', 'http', 'fetch-exams']
---

## fetchExams

### Node Usage

```ts
import { fetchExams } from '@manyun/knowledge-hub.service.dcexam.fetch-exams/dist/index.node';

const { data, error } = await fetchExams();
```

### Browser Usage

#### 请求所有考试

```ts
import { fetchExams } from '@manyun/knowledge-hub.service.dcexam.fetch-exams/dist/index.browser';

const { data, error } = await fetchExams({
  variant: 'allExam',
  page: 1,
  pageSize: 10,
  ...
});
```

#### 请求可判卷考试

```ts
import { fetchExams } from '@manyun/knowledge-hub.service.dcexam.fetch-exams/dist/index.browser';

const { data, error } = await fetchExams({
  variant: 'allowMarkingExam',
  page: 1,
  pageSize: 10,
  ...
});
```
