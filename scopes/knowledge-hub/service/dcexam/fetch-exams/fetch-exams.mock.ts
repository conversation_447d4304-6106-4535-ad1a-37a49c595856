/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { examsEndPoint, markExamsEndPoint } from './fetch-exams';
import { BackendExamStatus, BackendExamType } from './fetch-exams.type';
import type { ApiR, BackendExam } from './fetch-exams.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(examsEndPoint)
    .reply<ApiR<BackendExam>>(200, {
      success: true,
      data: Array(10)
        .fill(null)
        .map((_, idx) => ({
          id: idx + 1,
          name: 'test', //考试名
          categoryCode: 1, //考试类型
          type: BackendExamType.FORMAL,
          status: BackendExamStatus.INIT, //考试状态
          startTime: 1631767546720, //开始时间
          endTime: 1631767546720, //结束时间
          totalGrade: 100, //总分
          duration: 60, //考试时长
          gmtCreate: 1631767546720, //创建时间
          gmtModified: 1631767546720, //修改时间
          modifierId: 1, //修改人
          modifierName: 'Admin', //修改人姓名
          allowCorrect: true,
        })),
      total: 100,
      errCode: null,
      errMessage: null,
    })

    .onPost(markExamsEndPoint)
    .reply<ApiR<BackendExam>>(config => {
      const {
        pageNum = 1,
        pageSize = 10,
        categoryCode,
        name,
        status,
        examStartTime,
        examEndTime,
      } = JSON.parse(config.data);
      const startId = (pageNum - 1) * pageSize;
      return [
        200,
        {
          success: true,
          data: Array(pageSize)
            .fill(null)
            .map((_, idx) => ({
              id: startId + idx,
              name: name ? name : 'test', //考试名
              categoryCode: categoryCode ? categoryCode : '1001', //考试类型
              type: 'FORMAL', //考试类型：FORMAL("正式考试"),TEST("随堂测试"),RESIT("补考"),
              status: status ? status : 'INIT', //考试状态
              startTime: examStartTime ? examStartTime : 1631767546720, //开始时间
              endTime: examEndTime ? examEndTime : 1631767546720, //结束时间
              totalGrade: 100, //总分
              duration: 60, //考试时长
              totalUserCount: 110, //考试总人数
              dubmitUserCount: 20, //交卷人数
              passUserCount: 10, //通过人数
              gmtCreate: 1631767546720, //创建时间
              gmtModified: 1631767546720, //修改时间
              modifierId: 1, //修改人
              modifierName: 'Admin', //修改人姓名
            })),
          total: 100,
          errCode: null,
          errMessage: null,
        },
      ];
    });
}
