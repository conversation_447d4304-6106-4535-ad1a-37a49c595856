/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchExams as webService } from './fetch-exams.browser';
import { registerWebMocks } from './fetch-exams.mock';
import { Variant } from './fetch-exams.type';

// // registerWebMocks();

test('request all exams', async () => {
  const { data, error } = await webService({ variant: Variant.AllExam, page: 1, pageSize: 10 });
  const exams = data.data;
  expect(exams).toHaveLength(10);
  expect(exams[0]).not.toHaveProperty('totalUserCount', 110); //考试总人数
  expect(error).toBe(undefined);
});

test('request allow marking exams', async () => {
  const { data, error } = await webService({
    variant: Variant.AllowMarkingExam,
    page: 1,
    pageSize: 10,
  });
  const markexams = data.data;
  expect(markexams).toHaveLength(10);
  expect(markexams[0]).toHaveProperty('totalUserCount', 110); //考试总人数
  expect(error).toBe(undefined);
});
