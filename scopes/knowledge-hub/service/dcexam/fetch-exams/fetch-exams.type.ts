/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export enum Variant {
  AllExam = 'allExam',
  AllowMarkingExam = 'allowMarkingExam',
}

export enum BackendExamStatus {
  INIT = 'INIT',
  PROCESS = 'PROCESS',
  CORRECT = 'CORRECT',
  GRADED = 'GRADED',
  CANCEL = 'CANCEL',
}

export const BAKEND_EXAM_STATUS_MAPPER: Record<BackendExamStatus, string> = {
  [BackendExamStatus.INIT]: '待考试',
  [BackendExamStatus.PROCESS]: '进行中',
  [BackendExamStatus.CORRECT]: '判卷中',
  [BackendExamStatus.GRADED]: '已完成',
  [BackendExamStatus.CANCEL]: '已取消',
};

export const ExamStatusMapper = {
  [BackendExamStatus.INIT]: '待考试',
  [BackendExamStatus.PROCESS]: '考试中',
  [BackendExamStatus.CORRECT]: '判卷中',
  [BackendExamStatus.GRADED]: '已完成',
};

export enum BackendExamType {
  FORMAL = 'FORMAL',
  TEST = 'TEST',
  RESIT = 'RESIT',
}

export type BackendExam = {
  id: number;
  name: string; //考试名
  categoryCode: number; //考试类型
  type: BackendExamType; //考试类型：FORMAL("正式考试"),TEST("随堂测试"),RESIT("补考"
  status: BackendExamStatus; //考试状态
  startTime: number; //开始时间
  endTime: number; //结束时间
  duration: number; //考试时长
  totalGrade?: number; //总分
  gmtCreate?: number; //创建时间
  gmtModified?: number; //修改时间
  modifierId?: number; //修改人
  modifierName?: string; //修改人姓名
  totalUserCount?: number; //考试总人数
  dubmitUserCount?: number; //交卷人数
  passUserCount?: number; //通过人数
  /** 当前请求 API 的用户是否具有判卷权限 */
  allowCorrect: boolean;
  needJoin: boolean;
};

export type BackendUserConfig = {
  mode: number; //模式//1:所有用户，2：自定义角色，3：自定义人员
  users?: string[];
  roles?: string[];
};

export type BackendExamDetail = BackendExam & {
  paperId: number | null; //试卷id
  paperName?: string; //试卷名称
  sections?: {
    section: string;
    questions: { id: number }[];
  }[]; // 选择考生同卷时，预览所看试卷
  defaultUserConfig: BackendUserConfig; //必考人员
  optionalUserConfig: BackendUserConfig | null; //选考人员
  correctUserConfig: BackendUserConfig; //批改人员
  canCorrect?: boolean; //是否有权限批改试卷
  questionNum?: number; //题目数量
  totalUserCount?: number; //总人数
  scope: string[]; //考试范围
  passGrade: number; //及格分数
  examProperties: {
    //属性
    allowShowExamPaper: boolean; //交卷后是否允许展示试卷
    sameExamPaper: boolean; //考生同卷
  };
};

// 前端所需要的数据结构
export type Exam = {
  id: number;
  name: string;
  type: BackendExamType;
  categoryCode: number;
  timeRange: [number, number];
  paperId: number | null;
  paperName?: string; //试卷名称
  timeLimit: number; // 答卷时长
  passingScore: number;
  requiredUserIds?: number[];
  requiredRoleIds?: number[];
  optionalUserIds?: number[];
  optionalRoleIds?: number[];
  markUserIds?: number[];
  markRoleIds?: number[];
  scopes: string[];
  totalGrade?: number;
  canCorrect?: boolean; //是否有权限批改试卷
  questionNum?: number; //题目数量
  totalUserCount?: number; //总人数
  sections?: { questions: { id: number }[]; section: string }[];
  allowReviewPaperAfterHandedIn: boolean; // 交卷后是否允许展示试卷
  useSameMasterPaper: boolean; // 考试是否同卷
  /** 当前登录用户是否有权限判卷 */
  allowMark: boolean;
};

export type ApiQ = {
  name: string | null;
  status: string | null;
  examStartTime: number | null;
  examEndTime: number | null;
  categoryCode: number | null;
  pageNum: number;
  pageSize: number;
  examViewStatusList: BackendExamStatus[];
  idcTag: string | null;
  orderByModifyTimeDesc?: boolean | null;
  categoryCodes?: string[];
};

export type ApiR<T> = ListResponse<T[]>;

export type ServiceQ = {
  variant: Variant;
  examName?: string | null;
  timeRange?: [number, number] | null;
  examStatus?: BackendExamStatus | BackendExamStatus[] | null;
  categoryCode?: number | null;
  page: number;
  pageSize: number;
  idcTag?: string;
  orderByModifyTimeDesc?: boolean | null;
  categoryCodes?: string[];
};

export type ServiceR<T> = {
  data: T[];
  total: number;
};
