/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { examsEndPoint, markExamsEndPoint } from './fetch-exams';
import { Variant } from './fetch-exams.type';
import type { ApiQ, BackendExam, ServiceQ, ServiceR } from './fetch-exams.type';

/**
 * 知识中心-考试管理-考试列表(所有考试、可判卷考试).
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/fb9qax)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExams({
  variant,
  examName,
  timeRange,
  examStatus,
  categoryCode,
  page,
  pageSize,
  idcTag,
  orderByModifyTimeDesc,
  categoryCodes,
}: ServiceQ) {
  const apiQ: ApiQ = {
    name: examName || null,
    status: (Array.isArray(examStatus) ? null : examStatus) || null,
    examStartTime: timeRange ? timeRange[0] : null,
    examEndTime: timeRange ? timeRange[1] : null,
    categoryCode: categoryCode || null,
    idcTag: idcTag || null,
    pageNum: page,
    pageSize,
    examViewStatusList: Array.isArray(examStatus) ? examStatus : [],
    orderByModifyTimeDesc: orderByModifyTimeDesc,
    categoryCodes: categoryCodes,
  };
  if (variant === Variant.AllExam) {
    return await webRequest.tryPost<ServiceR<BackendExam>, ApiQ>(examsEndPoint, apiQ);
  } else if (variant === Variant.AllowMarkingExam) {
    return await webRequest.tryPost<ServiceR<BackendExam>, ApiQ>(markExamsEndPoint, apiQ);
  } else {
    throw new Error(`Variant(${variant}) not supported!`);
  }
}
