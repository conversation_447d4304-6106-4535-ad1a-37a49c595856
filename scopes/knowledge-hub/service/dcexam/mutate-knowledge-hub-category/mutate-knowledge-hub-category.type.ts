/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendCategory } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

export type CreateApiD = {
  name: string;
  type: string;
  parentId: number; //父级id
};
export type UpdateApiD = {
  id: number;
  name: string;
};

export type ServiceApiD = {
  id: number;
  type: string;
  parentId: number;
  name: string;
};

export type ApiR = Response<BackendCategory>;

export type ServiceD = {
  id?: number;
  type?: string;
  parentId?: number;
  name?: string;
};

export type ServiceR = BackendCategory;
