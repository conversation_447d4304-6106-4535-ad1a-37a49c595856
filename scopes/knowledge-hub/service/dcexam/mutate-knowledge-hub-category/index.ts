/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { mutateKnowledgeHubCategory as mutateKnowledgeHubCategoryWeb } from './mutate-knowledge-hub-category.browser';
import { mutateKnowledgeHubCategory as mutateKnowledgeHubCategoryNode } from './mutate-knowledge-hub-category.node';

export { mutateKnowledgeHubCategoryWeb, mutateKnowledgeHubCategoryNode };
export * from './mutate-knowledge-hub-category.type';
export { registerWebMocks } from './mutate-knowledge-hub-category.mock';
