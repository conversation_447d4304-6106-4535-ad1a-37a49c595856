/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category';

import { mutateKnowledgeHubCategory as webService } from './mutate-knowledge-hub-category.browser';
import './mutate-knowledge-hub-category.mock';
import { ServiceR } from './mutate-knowledge-hub-category.type';

// registerWebMocks();

test('create a category', async () => {
  const { data, error } = await webService({
    name: '节点名称',
    type: 'QUESTION',
    parentId: 1,
  });
  // expect(data).toHaveProperty('id');
  // expect(data).toHaveProperty('name', '课件分类');
  expect(error).toBe(undefined);
});

test('modify a category', async () => {
  const { data, error } = await webService({
    name: '修改类型名称',
    id: 2,
  });
  // expect(data).toHaveProperty('id');
  // expect(data).toHaveProperty('name', '修改类型名称');
  expect(error).toBe(undefined);
});
