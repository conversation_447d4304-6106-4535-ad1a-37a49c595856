---
description: '知识中心-分类管理-新增、修改、删除'
labels: ['service', 'http', 'mutate-knowledge-hub-category']
---

## mutateKnowledgeHubCategory

### Node Usage

```ts
import { mutateKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category/dist/index.node';

const { data, error } = await mutateKnowledgeHubCategory();
```

### Browser Usage

### 新增分类

```ts
import { mutateKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category/dist/index.browser';

const { data, error } = await mutateKnowledgeHubCategory({
  name: '',
  type: '',
  parentId: '',
});
```

### 修改分类

```ts
import { mutateKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category/dist/index.browser';

const { data, error } = await mutateKnowledgeHubCategory({
  id: '',
  name: '',
});
```

### 删除分类

```ts
import { mutateKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category/dist/index.browser';

const { data, error } = await mutateKnowledgeHubCategory({
  id: '',
});
```
