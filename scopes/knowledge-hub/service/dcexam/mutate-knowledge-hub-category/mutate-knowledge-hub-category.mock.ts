/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import {
  createCategoryEndpoint,
  deleteCategoryEndPoint,
  updateCategoryEndpoint,
} from './mutate-knowledge-hub-category';
import type { ApiR, CreateApiD, UpdateApiD } from './mutate-knowledge-hub-category.type';

export function registerWebMocks(mock: ReturnType<typeof getMock>) {
  mock
    .onPost(createCategoryEndpoint, {
      name: '节点名称',
      parentId: 1,
      type: 'QUESTION',
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        id: 2,
        name: '节点名称', //分类名称
        parentId: 1, //父级类型id
        type: 'QUESTION', //类型
        level: 1, //级别
        count: 20, //数量
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(updateCategoryEndpoint, {
      id: 2,
      name: '修改类型名称',
    } as UpdateApiD)
    .reply<ApiR>(200, {
      success: true,
      data: {
        id: 2,
        name: '修改类型名称', //分类名称
        parentId: 0, //父级类型id
        type: 'FOLDER', //类型
        level: 1, //级别
        count: 20, //数量
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(deleteCategoryEndPoint, {
      id: 2,
    })
    .reply(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
