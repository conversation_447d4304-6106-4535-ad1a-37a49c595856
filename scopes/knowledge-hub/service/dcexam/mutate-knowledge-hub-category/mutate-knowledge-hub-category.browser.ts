/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { createCategoryEndpoint, updateCategoryEndpoint } from './mutate-knowledge-hub-category';
import type {
  CreateApiD,
  ServiceD,
  ServiceR,
  UpdateApiD,
} from './mutate-knowledge-hub-category.type';

/**
 * 知识中心-分类-新增、修改
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/fd3mwh、https://manyun.yuque.com/ewe5b3/zcferb/agwgvl)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateKnowledgeHubCategory({ id, type, name, parentId }: ServiceD) {
  if (id == undefined) {
    // 创建
    const apiD: CreateApiD = {
      name: name!,
      type: type!,
      parentId: parentId!,
    };
    return await webRequest.tryPost<ServiceR, CreateApiD>(createCategoryEndpoint, apiD);
  } else {
    // 修改
    const apiD: UpdateApiD = {
      id: id,
      name: name!,
    };
    return await webRequest.tryPost<ServiceR, UpdateApiD>(updateCategoryEndpoint, apiD);
  }
}
