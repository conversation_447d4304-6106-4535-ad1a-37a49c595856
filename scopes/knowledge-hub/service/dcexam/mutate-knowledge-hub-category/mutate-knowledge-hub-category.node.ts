/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { CreateApiD, ServiceD, ServiceR } from './mutate-knowledge-hub-category.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateKnowledgeHubCategory(data: ServiceD) {
  throw new Error("mutateKnowledgeHubCategory's node version not implemented!");
}
