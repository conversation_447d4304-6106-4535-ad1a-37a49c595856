/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
// import './revoke-users-skill.mock';
import { revokeUsersSkill as webService } from './revoke-users-skill.browser';
import { registerWebMocks } from './revoke-users-skill.mock';

// registerWebMocks();

test('should resolve "revoke skill"', async () => {
  const { data, error } = await webService({ idList: [1] });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
