/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './revoke-users-skill';
import type { ApiQ, ApiR } from './revoke-users-skill.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { idList } = JSON.parse(config.data);
    if (idList && idList.length) {
      return [
        200,
        {
          success: true,
          errCode: null,
          errMessage: null,
        },
      ];
    }
    return [
      200,
      {
        success: false,
        errCode: 500,
        errMessage: 'idList should not be blank!',
      },
    ];
  });
}
