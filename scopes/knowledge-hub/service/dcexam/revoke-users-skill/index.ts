/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { revokeUsersSkill as revokeUsersSkillWeb } from './revoke-users-skill.browser';
import { revokeUsersSkill as revokeUsersSkillNode } from './revoke-users-skill.node';

export { revokeUsersSkillWeb, revokeUsersSkillNode };
export { registerWebMocks } from './revoke-users-skill.mock';
export * from './revoke-users-skill.type';
