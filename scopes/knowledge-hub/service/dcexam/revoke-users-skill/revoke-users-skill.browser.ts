/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './revoke-users-skill';
import type { ApiQ, ServiceQ, ServiceR } from './revoke-users-skill.type';

/**
 *  知识中心 - 回收技能
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/iaz3mu)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function revokeUsersSkill(data: ServiceQ) {
  const apiQ: ApiQ = data;

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
