/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
// import './fetch-exam.mock';
import { Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

import { fetchExam as webService } from './fetch-exam.browser';
import { registerWebMocks } from './fetch-exam.mock';

// // registerWebMocks();

test('toMatch exam detail', async () => {
  const { data, error } = await webService({ examId: 1 });
  const exam: Exam = {
    id: 1,
    name: 'test',
    type: 'FORMAL' as any,
    categoryCode: 1,
    timeLimit: 0,
    timeRange: [1631768065830, 1631768065830],
    paperId: 1,
    paperName: '测试试卷1',
    passingScore: 60,
    requiredUserIds: [1],
    requiredRoleIds: [1],
    optionalUserIds: [1],
    optionalRoleIds: [1],
    markUserIds: [1],
    markRoleIds: [1],
    scopes: ['EC01.A', 'EC01.B'],
    allowReviewPaperAfterHandedIn: false,
    useSameMasterPaper: false,
    totalGrade: 100,
    canCorrect: true,
    questionNum: 100,
    totalUserCount: 100,
    allowMark: true,
  };
  expect(data).toMatchObject(exam);
  expect(error).toBe(undefined);
});
