/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import {
  BackendExamStatus,
  BackendExamType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-exam';
import type { ApiR } from './fetch-exam.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint, { id: 1 }).reply<ApiR>(200, {
    success: true,
    data: {
      id: 1,
      name: 'test', //考试名
      categoryCode: 9, //考试分类
      paperId: 1, //试卷id
      paperName: '测试试卷1', //试卷名称
      type: BackendExamType.FORMAL, //考试类型
      status: BackendExamStatus.INIT, //考试状态
      startTime: 1631768065830, //开始时间
      endTime: 1631768065830, //结束时间
      duration: 60, //考试时长
      totalGrade: 100, //总分
      passGrade: 60, //及格分数
      scope: [
        //范围
        'EC01.A',
        'EC01.B',
      ],
      defaultUserConfig: {
        //必考人员
        mode: 1,
        users: ['1'],
        roles: ['1'],
      },
      optionalUserConfig: {
        //选考人员
        mode: 1,
        users: ['1'],
        roles: ['1'],
      },
      correctUserConfig: {
        //批改用户
        mode: 1,
        users: ['1'],
        roles: ['1'],
      },
      examProperties: {
        allowShowExamPaper: false, //考试后能否查看试卷
        sameExamPaper: false, //考生同卷
      },
      gmtCreate: 1631768065830,
      gmtModified: 1631768065830,
      modifierId: 1,
      modifierName: 'Admin',
      canCorrect: true,
      questionNum: 100,
      totalUserCount: 100,
      allowCorrect: true,
    },
    errCode: null,
    errMessage: null,
  });
}
