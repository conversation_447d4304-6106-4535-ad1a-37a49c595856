/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendExamDetail, Exam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { EnhancedAxiosResponse, webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-exam';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-exam.type';

/**
 * 知识中心-考试管理-考试详情.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/slglyv)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExam({ examId }: ServiceQ): Promise<EnhancedAxiosResponse<Exam | null>> {
  const apiQ: ApiQ = { id: examId };
  const { data, error, ...rest } = await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
  if (error) {
    return { data: null, error, ...rest };
  }
  const exam: Exam = convertBackendExamToExam(data);
  return { data: exam, error, ...rest };
}

/**
 *
 * @param data BackendExam
 * @returns Exam
 */
function convertBackendExamToExam(data: BackendExamDetail): Exam {
  const exam: Exam = {
    id: data.id,
    name: data.name,
    type: data.type,
    categoryCode: data.categoryCode,
    timeRange: [data.startTime, data.endTime],
    timeLimit: data.duration,
    paperId: data.paperId,
    paperName: data.paperName,
    passingScore: data.passGrade,
    requiredUserIds: data.defaultUserConfig.users?.map(Number),
    requiredRoleIds: data.defaultUserConfig.roles?.map(Number),
    optionalUserIds: data.optionalUserConfig?.users?.map(Number),
    optionalRoleIds: data.optionalUserConfig?.roles?.map(Number),
    markUserIds: data.correctUserConfig.users?.map(Number),
    markRoleIds: data.correctUserConfig.roles?.map(Number),
    scopes: data.scope,
    allowReviewPaperAfterHandedIn: data.examProperties.allowShowExamPaper,
    useSameMasterPaper: data.examProperties.sameExamPaper,
    totalGrade: data.totalGrade,
    questionNum: data.questionNum,
    canCorrect: data.canCorrect,
    totalUserCount: data.totalUserCount,
    allowMark: data.allowCorrect,
  };
  return exam;
}
