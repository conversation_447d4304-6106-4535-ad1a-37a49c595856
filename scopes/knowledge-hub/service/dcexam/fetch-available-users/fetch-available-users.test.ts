/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
// import './fetch-available-users.mock';
import { fetchAvailableUsers as webService } from './fetch-available-users.browser';
import { registerWebMocks } from './fetch-available-users.mock';

// // registerWebMocks();

test('should resolve "avaiable users"', async () => {
  const { data, error } = await webService({ certId: 1, page: 1, pageSize: 10 });
  expect(data.data.length).toBe(10);
  expect(error).toBe(undefined);
});
