/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { fetchAvailableUsers as fetchAvailableUsersWeb } from './fetch-available-users.browser';
import { fetchAvailableUsers as fetchAvailableUsersNode } from './fetch-available-users.node';

export { fetchAvailableUsersWeb, fetchAvailableUsersNode };
export { registerWebMocks } from './fetch-available-users.mock';
export * from './fetch-available-users.type';
