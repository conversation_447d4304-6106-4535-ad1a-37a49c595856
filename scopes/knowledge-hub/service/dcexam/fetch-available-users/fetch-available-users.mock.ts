/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-available-users';
import type { ApiQ, ApiR } from './fetch-available-users.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { pageNum = 1, pageSize = 10, blockGuidList, userId } = JSON.parse(config.data);
    const startId = (pageNum - 1) * pageSize;
    let blockGuids = ['EC01'];
    if (blockGuidList) {
      blockGuids = blockGuidList;
    }
    let user = 3;
    if (userId) {
      user = userId;
    }
    return [
      200,
      {
        success: true,
        data: Array(pageSize)
          .fill(null)
          .map((_, idx) => ({
            userId: startId + idx,
            accountName: 'Account Name ' + (startId + idx),
            userName: user,
            blockGuidList: blockGuids,
            roleList: ['admin'],
          })),
        total: 100,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
