/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiR = ListResponse<BackendEnableCertifiedStaffs[]>;

export type BackendEnableCertifiedStaffs = {
  accountName: string;
  userId: number;
  userName: string;
  blockGuidList: string[];
  roleList: number[];
};

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  certId: number;
  userId?: number;
  blockGuidList?: string[];
  roleList?: number[];
};

export type ServiceQ = {
  page: number;
  pageSize: number;
  certId: number;
  userId?: number;
  blockGuids?: string[];
  roleIds?: number[];
};

export type ServiceR = {
  data: BackendEnableCertifiedStaffs[];
  total: number;
};
