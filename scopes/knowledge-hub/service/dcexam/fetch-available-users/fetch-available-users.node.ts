/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './fetch-available-users.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchAvailableUsers(data: ServiceQ) {
  throw new Error("fetchAvailableUsers's node version not implemented!");
}
