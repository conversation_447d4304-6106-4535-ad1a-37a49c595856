/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-available-users';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-available-users.type';

/**
 * 知识中心 - 技能关联的发放人员查询
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/vp814k)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchAvailableUsers({ page, blockGuids, roleIds, ...rest }: ServiceQ) {
  const apiQ: ApiQ = {
    pageNum: page,
    ...rest,
  };
  if (blockGuids) {
    apiQ.blockGuidList = blockGuids;
  }
  if (roleIds) {
    apiQ.roleList = roleIds;
  }
  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
