/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './issue-users-skill';
import type { ApiQ, ServiceQ, ServiceR } from './issue-users-skill.type';

/**
 * 知识中心 - 发放证书
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/gun6n5)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function issueUsersSkill({ selectedUsers, certId }: ServiceQ) {
  const apiQ: ApiQ = {
    certId,
    userList: selectedUsers,
    awardType: 'MANUAL',
    status: 'NORMAL',
  };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
