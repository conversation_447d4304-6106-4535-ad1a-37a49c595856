/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type User = {
  userId: number;
  userName: string;
};

export type ApiQ = {
  awardType: string;
  status: string;
  userList: User[];
  certId: number;
};

export type ApiR = WriteResponse;

export type ServiceQ = {
  selectedUsers: User[];
  certId: number;
};

export type ServiceR = boolean;
