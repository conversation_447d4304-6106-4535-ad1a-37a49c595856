/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './issue-users-skill';
import type { ApiQ, ApiR } from './issue-users-skill.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { userList } = JSON.parse(config.data);
    if (!userList || !userList.length) {
      return [
        200,
        {
          success: true,
          errCode: 405,
          errMessage: 'userList should not be blank!',
        },
      ];
    }
    return [
      200,
      {
        success: true,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
