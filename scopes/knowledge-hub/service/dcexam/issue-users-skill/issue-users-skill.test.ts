/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
// import './issue-users-skill.mock';
import { issueUsersSkill as webService } from './issue-users-skill.browser';
import { registerWebMocks } from './issue-users-skill.mock';

// registerWebMocks();

test('should resolve "bar"', async () => {
  const { data, error } = await webService({
    selectedUsers: [{ userId: 1, userName: 'admin' }],
    certId: 1,
  });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
