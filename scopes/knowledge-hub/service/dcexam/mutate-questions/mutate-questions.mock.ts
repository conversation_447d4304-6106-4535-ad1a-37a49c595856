/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import {
  createQuestionsEndpoint,
  updateBatchQuestionsEndPoint,
  updateQuestionEndpoint,
} from './mutate-questions';
import type { ApiR, CreateApiD, UpdateApiD, UpdateBatchApiD } from './mutate-questions.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(createQuestionsEndpoint, {
      questions: [
        {
          title: '驾驶人有下列哪种违法行为一次记6分',
          type: 1,
          categoryCode: 1,
          difficulty: 1,
          options: [
            '使用其他车辆行驶证',
            '饮酒后驾驶机动车',
            '车速超过规定时速50%以上',
            '违法占用应急车道行驶',
          ],
          answers: ['违法占用应急车道行驶'],
          autoGrade: true,
          analysis: '请仔细阅读交规',
        },
      ],
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(createQuestionsEndpoint, {
      questions: [
        {
          analysis: '分析',
          answers: ['选项1'],
          autoGrade: true,
          categoryCode: 1,
          difficulty: 1,
          options: ['选项1', '选项2'],
          title: '<p>试题内容</p>',
          type: 1,
        },
      ],
    } as CreateApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(updateQuestionEndpoint, {
      id: 1, //试题id
      type: 1,
      categoryCode: 1, //试题分类
      difficulty: 1, //难度
      title: '<p>测试试题</p>', //题目标题
      options: [
        //选项
        '测试选项1',
        '测试选项2',
        '测试选项3',
        '测试选项4',
      ],
      answers: ['测试选项1'],
      analysis: '这个题目明显选择 A', //分析
      autoGrade: true, //是否自动判分
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(updateBatchQuestionsEndPoint, {
      questionIds: [],
      folderCodes: [1],
      categoryCode: 10001,
    } as UpdateBatchApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(updateBatchQuestionsEndPoint, {
      questionIds: [2],
      folderCodes: [1],
      categoryCode: 100001,
    } as UpdateBatchApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
