/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { mutateQuestions as webService } from './mutate-questions.browser';
import { registerWebMocks } from './mutate-questions.mock';

registerWebMocks(getMock('web'));

test('create questions', async () => {
  const { data, error } = await webService([
    {
      analysis: '分析',
      answers: ['选项1'],
      autoGrade: true,
      categoryCode: 1,
      difficulty: 1,
      options: ['选项1', '选项2'],
      title: '<p>试题内容</p>',
      type: 1,
    },
  ]);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('create questions', async () => {
  const { data, error } = await webService([
    {
      title: '驾驶人有下列哪种违法行为一次记6分',
      type: 1,
      categoryCode: 1,
      difficulty: 1,
      options: [
        '使用其他车辆行驶证',
        '饮酒后驾驶机动车',
        '车速超过规定时速50%以上',
        '违法占用应急车道行驶',
      ],
      answers: ['违法占用应急车道行驶'],
      autoGrade: true,
      analysis: '请仔细阅读交规',
    },
  ]);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
test('update a question', async () => {
  const { data, error } = await webService({
    id: 1,
    analysis: '这个题目明显选择 A',
    answers: ['测试选项1'],
    autoGrade: true,
    categoryCode: 1,
    difficulty: 1,
    options: ['测试选项1', '测试选项2', '测试选项3', '测试选项4'],
    title: '<p>测试试题</p>',
    type: 1,
  });
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('update questions which has questionIdList and folderCodes', async () => {
  const { data, error } = await webService([
    { id: 1, categoryCode: 100001, questionType: '0' },
    { id: 2, categoryCode: 100001, questionType: '1' },
  ]);
  // expect(data).toBe(true);
  expect(error).toBe(undefined);
});

// test('update questions ', async () => {
//   const { data, error } = await webService([{ id: 1, categoryCode: 100001, questionType: '0' }]);
//   expect(data).toBe(true);
//   expect(error).toBe(undefined);
// });
