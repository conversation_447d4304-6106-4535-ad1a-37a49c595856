---
description: '知识中心-试题管理-新增、更新、批量更新'
labels: ['service', 'http', 'mutate-questions']
---

## mutateQuestions

### Node Usage

```ts
import { mutateQuestions } from '@manyun/knowledge-hub.service.dcexam.mutate-questions/dist/index.node';

const { data, error } = await mutateQuestions();
```

### Browser Usage

#### 批量创建试题

```ts
import { mutateQuestions } from '@manyun/knowledge-hub.service.dcexam.mutate-questions/dist/index.browser';

const { data, error } = await mutateQuestions([
  {
    analysis: '分析',
    answers: ['A:拉拉啦', 'B:lalalala'],
    autoGrade: true,
    categoryCode: '10001',
    difficulty: 1,
    options: ['A:啦啦啦', 'B:lalala'],
    title: '题目',
    type: 1,
  },
]);
```

#### 更新试题

```ts
import { mutateQuestions } from '@manyun/knowledge-hub.service.dcexam.mutate-questions/dist/index.browser';

const { data, error } = await mutateQuestions({
  id: 0,
  analysis: '分析',
  answers: ['A:拉拉啦', 'B:lalalala'],
  autoGrade: true,
  categoryCode: '10001',
  difficulty: 1,
  options: ['A:啦啦啦', 'B:lalala'],
  title: '题目',
  type: 1,
});
```

#### 批量更新试题

```ts
import { mutateQuestions } from '@manyun/knowledge-hub.service.dcexam.mutate-questions/dist/index.browser';

const { data, error } = await mutateQuestions([
  { id: 1, categoryCode: '100001', questionType: '0' },
]);
```

#### 批量更新试题

```ts
import { mutateQuestions } from '@manyun/knowledge-hub.service.dcexam.mutate-questions/dist/index.browser';

const { data, error } = await mutateQuestions([
  { id: 1, categoryCode: '100001', questionType: '0' },
  { id: 2, categoryCode: '100001', questionType: '1' },
]);
```
