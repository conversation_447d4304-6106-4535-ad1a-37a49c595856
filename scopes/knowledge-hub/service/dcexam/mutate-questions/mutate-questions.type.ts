/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export type CreateApiD = { questions: Omit<BackendQuestion, 'id'>[] };

export type UpdateApiD = BackendQuestion;

export type UpdateBatchApiD = {
  questionIds: number[] | null; //试题id
  folderCodes: number[] | null; //分类id列表
  categoryCode: string | number;
};

export type ApiR = WriteResponse;

export type ServiceD =
  | Omit<BackendQuestion, 'id'>[]
  | BackendQuestion
  | Pick<BackendQuestion, 'id' | 'categoryCode' | 'questionType'>[];

export type ServiceR = boolean;
