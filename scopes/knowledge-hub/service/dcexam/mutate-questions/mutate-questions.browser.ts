/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { webRequest } from '@manyun/service.request';

import {
  createQuestionsEndpoint,
  updateBatchQuestionsEndPoint,
  updateQuestionEndpoint,
} from './mutate-questions';
import type {
  CreateApiD,
  ServiceD,
  ServiceR,
  UpdateApiD,
  UpdateBatchApiD,
} from './mutate-questions.type';

/**
 * 知识中心-试题管理-试题批量新增、更新、批量更新
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/ypznw2、https://manyun.yuque.com/ewe5b3/zcferb/ypznw2、https://manyun.yuque.com/ewe5b3/zcferb/wsmc49)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateQuestions(questions: ServiceD) {
  if (!Array.isArray(questions) && typeof questions == 'object') {
    //更新
    return await webRequest.tryPost<ServiceR, UpdateApiD>(updateQuestionEndpoint, questions);
  } else {
    if (questions[0].hasOwnProperty('id') && (questions[0] as { id: number }).id !== undefined) {
      //批量更新
      const batchData: UpdateBatchApiD = {
        questionIds: (questions as BackendQuestion[])
          .filter(question => question.questionType === '1')
          .map(question => question.id),
        categoryCode: questions[0].categoryCode,
        folderCodes: (questions as BackendQuestion[])
          .filter(question => question.questionType === '0')
          .map(question => question.id),
      };
      return await webRequest.tryPost<ServiceR, UpdateBatchApiD>(
        updateBatchQuestionsEndPoint,
        batchData
      );
    } else {
      //创建
      return await webRequest.tryPost<ServiceR, CreateApiD>(createQuestionsEndpoint, {
        questions: questions as Omit<BackendQuestion, 'id'>[],
      });
    }
  }
}
