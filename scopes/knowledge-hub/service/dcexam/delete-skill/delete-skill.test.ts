/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
// import './delete-skill.mock';
import { deleteSkill as webService } from './delete-skill.browser';
import { registerWebMocks } from './delete-skill.mock';

// // registerWebMocks();

test('should resolve "delete skill"', async () => {
  const { data, error } = await webService({ id: 1 });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
