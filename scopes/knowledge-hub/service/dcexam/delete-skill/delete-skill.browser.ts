/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './delete-skill';
import type { ApiQ, ServiceQ, ServiceR } from './delete-skill.type';

/**
 *  知识中心 - 删除技能
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/ypdu0k)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deleteSkill({ id }: ServiceQ) {
  const apiQ: ApiQ = { id };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
