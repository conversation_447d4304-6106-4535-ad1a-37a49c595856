/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-skill';
import type { ApiR } from './delete-skill.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { id } = JSON.parse(config.data);
    if (!id && id !== 0) {
      return [
        200,
        {
          success: false,
          errCode: 405,
          errMessage: 'id cannot be empty',
        },
      ];
    }
    return [
      200,
      {
        success: true,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
