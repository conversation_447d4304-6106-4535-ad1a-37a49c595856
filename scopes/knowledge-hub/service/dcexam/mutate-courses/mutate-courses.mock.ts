/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import {
  batchUpdateCoursesEndpoint,
  createCourseEndpoint,
  updateCourseEndpoint,
} from './mutate-courses';
import type { ApiR, BatchUpdateApiD, CreateApiD, EditApiD } from './mutate-courses.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(batchUpdateCoursesEndpoint, {
      asymmetricMatch({ categoryCode }: BatchUpdateApiD) {
        return categoryCode == '1';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(batchUpdateCoursesEndpoint, {
      asymmetricMatch({ courseIds }: BatchUpdateApiD) {
        return courseIds.includes(2);
      },
    })
    .reply<ApiR>(200, {
      success: false,
      errCode: null,
      errMessage: null,
    })
    .onPost(updateCourseEndpoint, {
      asymmetricMatch({ courseId }: EditApiD) {
        return courseId == 1;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(createCourseEndpoint, {
      asymmetricMatch({ categoryCode }: CreateApiD) {
        return categoryCode == '1';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
