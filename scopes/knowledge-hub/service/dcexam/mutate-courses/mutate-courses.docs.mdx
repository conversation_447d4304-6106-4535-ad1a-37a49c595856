---
description: '知识中心-课程管理-创建课程、修改课程、批量更新课程.'
labels: ['service', 'http', 'mutate-courses']
---

## mutateCourses

### Node Usage

```ts
import { mutateCourses } from '@manyun/knowledge-hub.service.dcexam.mutate-courses/dist/index.node';

const { data, error } = await mutateCourses();
```

### Browser Usage

#### 创建课程

```ts
import { mutateCourses } from '@manyun/knowledge-hub.service.dcexam.mutate-courses/dist/index.browser';

const { data, error } = await mutateCourses({
  categoryCode: 1,
  course: {
    name: 'a',
    categoryCode: '1',
    openingHours: [123, 234],
    enabled: true,
    roleEntities: {},
    userEntities: {},
    requiredUserIds: [],
    requiredRoleIds: [],
    optionalUserIds: [],
    optionalRoleIds: [],
    lectureEntities: {},
    lectureIds: [],
    paperId: 1,
    mustPass: true,
    passingScore: 1,
    timeLimit: 10,
  },
});
```

#### 修改课程

```ts
import { mutateCourses } from '@manyun/knowledge-hub.service.dcexam.mutate-courses/dist/index.browser';

const { data, error } = await mutateCourses({
  categoryCode: 1,
  course: {
    id: 1,
    name: 'a',
    categoryCode: '1',
    openingHours: [123, 234],
    enabled: true,
    roleEntities: {},
    userEntities: {},
    requiredUserIds: [],
    requiredRoleIds: [],
    optionalUserIds: [],
    optionalRoleIds: [],
    lectureEntities: {},
    lectureIds: [],
    paperId: 1,
    mustPass: true,
    passingScore: 1,
    timeLimit: 10,
  },
});
```

#### 批量更新课程

```ts
import { mutateCourses } from '@manyun/knowledge-hub.service.dcexam.mutate-courses/dist/index.browser';

const { data, error } = await mutateCourses({
  categoryCode: 1,
  selectedIds: [1],
});
```
