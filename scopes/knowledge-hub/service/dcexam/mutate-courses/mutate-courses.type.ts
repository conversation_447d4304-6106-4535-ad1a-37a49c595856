/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  FrontendCourse,
  MutateCourse,
} from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import type { SchModeType } from '@manyun/ticket.model.task';

export type EditApiD = FrontendCourse & {
  courseId: number; //课程ID
  courseName: string; //课程名称
  categoryCode: string; //课程分类
  startTime: number; //开始时间
  endTime: number; //结束时间
  status: string; //课程状态，启用、禁用
  trainMode: SchModeType;
  ownerId: number;
  ownerName: string;
};

export type CreateApiD = Omit<EditApiD, 'courseId'>;

export type BatchUpdateApiD = {
  categoryCode: string; //课程分类
  courseIds: number[]; //课程IDs
};

export type ApiR = WriteResponse;

export type ServiceD = {
  course?: MutateCourse;
  selectedIds?: number[];
  categoryCode?: string;
};

export type ServiceR = boolean;
