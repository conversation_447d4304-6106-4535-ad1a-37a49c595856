/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateCourses as webService } from './mutate-courses.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "create course success"', async () => {
  const { data, error } = await webService({
    course: {
      name: '123',
      categoryCode: '35',
      openingHours: [1639039980751, 1639212780751],
      enabled: true,
      lectureIds: ['.MP4_59'],
      lectureEntities: {
        '.MP4_59': {
          holdOnBlur: true,
          allowSeekForward: true,
          name: 'flower',
          order: 1,
          fileId: '59',
        },
      },
      paperId: 39,
      mustPass: true,
      passingScore: 12.4,
      timeLimit: 0,
      requiredUserIds: [],
      requiredRoleIds: [1],
      optionalUserIds: [],
      optionalRoleIds: [],
    },
  } as any);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "create course fail"', async () => {
  const { data, error } = await webService({
    course: {
      name: '1234',
      categoryCode: '35',
      openingHours: [1639039980751, 1639212780751],
      enabled: true,
      lectureIds: ['.MP4_59'],
      lectureEntities: {
        '.MP4_59': {
          holdOnBlur: true,
          allowSeekForward: true,
          name: 'flower',
          order: 1,
          fileId: '59',
        },
      },
      paperId: 39,
      mustPass: true,
      passingScore: 12.4,
      timeLimit: 0,
      requiredUserIds: [],
      requiredRoleIds: [1],
      optionalUserIds: [],
      optionalRoleIds: [],
    },
  } as any);

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "edit course success"', async () => {
  const { data, error } = await webService({
    course: {
      id: 7,
      name: '123',
      categoryCode: '35',
      openingHours: [1639039980751, 1639212780751],
      enabled: true,
      lectureIds: ['.MP4_59'],
      lectureEntities: {
        '.MP4_59': {
          holdOnBlur: true,
          allowSeekForward: true,
          name: 'flower',
          order: 1,
          fileId: '59',
        },
      },
      paperId: 39,
      mustPass: true,
      passingScore: 12.4,
      timeLimit: 0,
      requiredUserIds: [],
      requiredRoleIds: [1],
      optionalUserIds: [],
      optionalRoleIds: [],
    },
  } as any);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "edit course fail"', async () => {
  const { data, error } = await webService({
    course: {
      id: 7,
      name: '1234',
      categoryCode: '35',
      openingHours: [1639039980751, 1639212780751],
      enabled: true,
      lectureIds: ['.MP4_59'],
      lectureEntities: {
        '.MP4_59': {
          holdOnBlur: true,
          allowSeekForward: true,
          name: 'flower',
          order: 1,
          fileId: '59',
        },
      },
      paperId: 39,
      mustPass: true,
      passingScore: 12.4,
      timeLimit: 0,
      requiredUserIds: [],
      requiredRoleIds: [1],
      optionalUserIds: [],
      optionalRoleIds: [],
    },
  } as any);
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "batch update course success"', async () => {
  const { data, error } = await webService({
    categoryCode: '1',
    selectedIds: [1],
  });
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "batch update course fail"', async () => {
  const { data, error } = await webService({
    categoryCode: '2',
    selectedIds: [1],
  });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
