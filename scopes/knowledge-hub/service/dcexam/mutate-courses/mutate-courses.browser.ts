/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { CourseStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import { webRequest } from '@manyun/service.request';

import {
  batchUpdateCoursesEndpoint,
  createCourseEndpoint,
  updateCourseEndpoint,
} from './mutate-courses';
import type {
  BatchUpdateApiD,
  CreateApiD,
  EditApiD,
  ServiceD,
  ServiceR,
} from './mutate-courses.type';

/**
 * 知识中心-创建课程、修改课程、批量修改课程
 *
 * @see [Doc1](https://manyun.yuque.com/ewe5b3/zcferb/ehmze0)
 * @see [Doc2](https://manyun.yuque.com/ewe5b3/zcferb/bxwpfm)
 * @see [Doc3](https://manyun.yuque.com/ewe5b3/zcferb/md0wp1)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateCourses({ course, categoryCode, selectedIds }: ServiceD) {
  if (Array.isArray(selectedIds) && categoryCode) {
    const apiD: BatchUpdateApiD = {
      courseIds: selectedIds,
      categoryCode,
    };
    return await webRequest.tryPost<ServiceR, BatchUpdateApiD>(batchUpdateCoursesEndpoint, apiD);
  } else if (course) {
    const apiD: CreateApiD = {
      courseName: course.name,
      categoryCode: course.categoryCode,
      startTime: course.openingHours![0],
      endTime: course.openingHours![1],
      status: course.enabled ? CourseStatus.NORMAL : CourseStatus.DISABLE,
      defaultUserConfig: {
        users: course.requiredUserIds,
        roles: course.requiredRoleIds,
        departments: course.requiredDeptIds,
      },
      optionalUserConfig: {
        users: course.optionalUserIds,
        roles: course.optionalRoleIds,
        departments: course.optionalDeptIds,
      },
      courseFileList: course.lectureIds.map(lectureId => {
        const { fileId, allowSeekForward, holdOnBlur, minSecondsAsCompleted, name, order } =
          course.lectureEntities[lectureId];

        return {
          name,
          fileId,
          order,
          allowFastForward: allowSeekForward,
          stopAfterLeave: holdOnBlur,
          minTime: minSecondsAsCompleted,
        };
      }),
      examConfig: {
        paperId: course.paperId!,
        isPass: course.mustPass ?? false,
        passGrade: course.passingScore!,
        answerTime: course.timeLimit!,
        retakeExamCount: course.retakeExamCount,
        failedExamCount: course.failedExamCount,
        studySla: course.studySla,
        incompleteTimes: course.incompleteTimes,
        optNotify: course.optNotify,
      },
      trainMode: course.trainMode,
      ownerId: course.ownerId,
      ownerName: course.ownerName,
    };
    if (course.id) {
      return await webRequest.tryPost<ServiceR, EditApiD>(updateCourseEndpoint, {
        courseId: course.id,
        ...apiD,
      });
    } else {
      return await webRequest.tryPost<ServiceR, CreateApiD>(createCourseEndpoint, apiD);
    }
  } else {
    throw new Error(`mutateCourses api not supported!`);
  }
}
