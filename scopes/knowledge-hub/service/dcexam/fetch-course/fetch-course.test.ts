/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCourse as webService } from './fetch-course.browser';
import './fetch-course.mock';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "course detail success"', async () => {
  const { data, error } = await webService({ courseId: 1 });
  expect(typeof data?.id).toBe('number');
  expect(error).toBe(undefined);
});

test('should resolve "course detail fail"', async () => {
  const { data, error } = await webService({ courseId: 2 });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
