/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { Lecture } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import { CourseStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import type { Role, User } from '@manyun/knowledge-hub.service.dcexam.grant-courseware-permissions';
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-course';
import type { ApiQ, MutateCourse, ServiceQ, ServiceR } from './fetch-course.type';

/**
 * 知识中心-课程详情
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/ourod8)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchCourse({ courseId }: ServiceQ) {
  const apiQ: ApiQ = { courseId };

  const result = await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
  const { error, data } = result;
  if (error || data === null) {
    return { ...result, data: null };
  }
  const userEntities: Record<string, User> = {};
  const roleEntities: Record<string, Role> = {};
  const lectureEntities: Record<string, Lecture> = {};

  (data.defaultUserConfig.users || []).forEach(user => {
    userEntities[user.userId] = user;
  });
  (data.optionalUserConfig.users || []).forEach(user => {
    userEntities[user.userId] = user;
  });
  (data.defaultUserConfig.roles || []).forEach(role => {
    roleEntities[role.roleId] = role;
  });
  (data.optionalUserConfig.roles || []).forEach(role => {
    roleEntities[role.roleId] = role;
  });
  (data.courseFileList || []).forEach(courseFile => {
    lectureEntities[`${courseFile.fileType}_${courseFile.fileId}`] = {
      holdOnBlur: courseFile.stopAfterLeave,
      allowSeekForward: courseFile.allowFastForward,
      minSecondsAsCompleted: courseFile.minTime,
      studyTime: courseFile.studyTime,
      isFinished: courseFile.isFinished,
      name: courseFile.name,
      order: courseFile.order,
      id: courseFile.fileId,
      fileType: courseFile.fileType,
      fileName: courseFile.fileName,
      categoryCode: courseFile.categoryCode,
      groupKey: `${courseFile.fileType}_${courseFile.fileId}`,
      fileTime: courseFile.fileTime,
    };
  });
  const sort = data.courseFileList.sort((a, b) => {
    return a.order - b.order;
  });

  const d: MutateCourse = {
    id: data.courseId,
    name: data.courseName,
    categoryCode: data.categoryCode,
    openingHours: [data.startTime, data.endTime],
    enabled: data.status === CourseStatus.NORMAL,
    roleEntities,
    userEntities,
    requiredUserIds: data.defaultUserConfig.users.map(user => user.userId),
    requiredRoleIds: data.defaultUserConfig.roles.map(role => role.roleId),
    requiredDeptIds: Array.isArray(data.defaultUserConfig.departments)
      ? data.defaultUserConfig.departments.map(department => department.departmentId)
      : [],
    optionalUserIds: data.optionalUserConfig.users.map(user => user.userId),
    optionalRoleIds: data.optionalUserConfig.roles.map(role => role.roleId),
    optionalDeptIds: Array.isArray(data.optionalUserConfig.departments)
      ? data.optionalUserConfig.departments.map(department => department.departmentId)
      : [],
    lectureIds: sort.map(courseFile => `${courseFile.fileType}_${courseFile.fileId}`),
    lectureEntities,
    paperId: data.examConfig.paperId,
    mustPass: data.examConfig.isPass,
    passingScore: data.examConfig.passGrade,
    timeLimit: data.examConfig.answerTime,
    defaultUserNum: data.defaultUserNum,
    optionalUserNum: data.optionalUserNum,
    fileNum: data.fileNum,
    status: data.status,
    courseLength: data.courseFileList.length,
    retakeExamCount: data.examConfig.retakeExamCount,
    failedExamCount: data.examConfig.failedExamCount,
    studySla: data.examConfig.studySla,
    incompleteTimes: data.examConfig.incompleteTimes,
    optNotify: data.examConfig.optNotify!,
    trainMode: data.trainMode,
    ownerId: data.ownerId,
    ownerName: data.ownerName,
    review: data.review,
  };

  return { ...result, data: d };
}
