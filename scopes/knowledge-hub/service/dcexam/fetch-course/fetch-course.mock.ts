/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-course';
import type { ApiQ, ApiR } from './fetch-course.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      asymmetricMatch({ courseId }: ApiQ) {
        return courseId === 1;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        courseId: 1, //课程id
        courseName: 'aaaaa', //课程名称
        categoryCode: '1', //课程分类
        startTime: 1633946637377, //开始时间
        endTime: 1634292238377, //结束时间
        status: 'NORMAL', //课程状态，启用、禁用
        examConfig: {
          paperId: 2, //试卷id
          isPass: false, //是否必需通过
          passGrade: 60,
          answerTime: 80,
        },

        defaultUserConfig: {
          //必修人员
          users: [
            // 人员列表
            {
              userId: 1,
              userName: 'admin',
              loginName: '',
              roleList: [
                //所属角色名称列表
                '管理员',
              ],
              blockGuidList: [
                //所属机房楼栋名称列表
                'EC01.A',
              ],
              isDownload: false, //是否可以下载
            },
          ],
          roles: [
            // 角色列表
            {
              roleId: 1,
              roleName: '管理员',
              roleCode: '',
              isDownload: true, //是否可以下载
            },
          ],
        },
        optionalUserConfig: {
          //选修人员
          users: [
            // 人员列表
            {
              userId: 1,
              userName: 'admin',
              loginName: '',
              roleList: [
                //所属角色名称列表
                '管理员',
              ],
              blockGuidList: [
                //所属机房楼栋名称列表
                'EC01.A',
              ],
              isDownload: false, //是否可以下载
            },
            {
              userId: 2,
              userName: 'admin2',
              loginName: '',
              roleList: [
                //所属角色名称列表
                '管理员',
              ],
              blockGuidList: [
                //所属机房楼栋名称列表
                'EC01.A',
              ],
              isDownload: false, //是否可以下载
            },
          ],
          roles: [
            // 角色列表
            {
              roleId: 1,
              roleName: '管理员',
              roleCode: '',
              isDownload: true, //是否可以下载
            },
          ],
        },
        courseFileList: [
          //课程信息
          {
            name: 'aaaa', //名称
            minTime: 100, //最小学习时长 单位 秒
            fileId: 1, //课件id
            fileName: 'aaaa.mp4', //文件名称
            fileTime: 120, //视频时间 单位 秒
            fileType: '.MP4', //文件类型
            categoryCode: '1', //类型code
            order: 0, //序号,排序
            allowFastForward: true, //是否允许快进
            stopAfterLeave: true, //离开页面停止播放
          },
          {
            name: 'bbbb', //名称
            minTime: 100, //最小学习时长 单位 秒
            fileId: 2, //课件id
            fileName: 'aaaa.pdf', //文件名称
            fileType: '.PDF', //文件类型
            categoryCode: '1', //类型code
            order: 1, //序号,排序
          },
        ],
      },
      errCode: null,
      errMessage: null,
    });
}
