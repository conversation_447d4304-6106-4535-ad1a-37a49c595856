/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { Lecture } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning';
import type {
  BackendCoursewareConfig,
  CoursewareConfig,
  CoursewareProgress,
} from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import type {
  Role,
  User,
  UserConfigEntity,
} from '@manyun/knowledge-hub.service.dcexam.grant-courseware-permissions';
import type { SchModeType } from '@manyun/ticket.model.task';

export type ApiQ = {
  courseId: number; //课程ID
};

export type ApiR = Response<Course>;

export type ServiceQ = {
  courseId: number;
};

export type BackendCourse = {
  defaultUserConfig: UserConfigEntity; //必修人员
  optionalUserConfig: UserConfigEntity; //选修人员
  courseFileList: BackendCourseFile[]; //课程信息
};

export type FrontendCourse = {
  defaultUserConfig: UserConfig; //必修人员
  optionalUserConfig: UserConfig; //选修人员
  courseFileList: CoursewareConfig[]; //课程信息
  examConfig: ExamConfig | null; //随堂测验
};

export type UserConfig = {
  //成员选择
  users: number[]; // 人员列表
  roles: number[]; // 角色列表
  departments: number[]; // 部门列表
};

export type ExamConfig = {
  //随堂测验
  paperId: number; //试卷id
  paperName?: string; //试卷name
  isPass: boolean; //是否必需通过
  passGrade: number; //及格分数
  answerTime: number; //答卷时长 单位 分钟
  retakeExamCount?: number; //补考次数
  failedExamCount?: number; //未通过次数
  studySla?: number; //学习时效
  incompleteTimes?: number[]; //未完成学习通知时间
  optNotify: boolean; //选修是否通知
};

export type CourseFile = CoursewareConfig;

export type BackendCourseFile = BackendCoursewareConfig & CoursewareProgress;

export type Course = BackendCourse & { examConfig: Partial<ExamConfig> } & {
  courseId: number;
  courseName: string;
  categoryCode: string;
  startTime: number;
  endTime: number;
  status: string;
  defaultUserNum?: number; //必修人员数量
  optionalUserNum?: number; //选修人员数量
  fileNum?: number;
  trainMode: SchModeType;
  ownerId: number;
  ownerName: string;
  review?: string;
};

export type MutateCourse = {
  id?: number;
  name: string;
  categoryCode: string;
  openingHours: [number, number];
  enabled: boolean;
  roleEntities: Record<string, Role>;
  userEntities: Record<string, User>;
  requiredUserIds: number[];
  requiredRoleIds: number[];
  requiredDeptIds: string[];
  optionalUserIds: number[];
  optionalRoleIds: number[];
  optionalDeptIds: string[];
  lectureIds: string[];
  lectureEntities: Record<string, Lecture>;
  paperId?: number;
  mustPass?: boolean;
  passingScore?: number;
  timeLimit?: number;
  defaultUserNum?: number; //必修人员数量
  optionalUserNum?: number; //选修人员数量
  fileNum?: number;
  status: string;
  courseLength: number;
  retakeExamCount?: number; //补考次数
  failedExamCount?: number; //未通过次数
  studySla?: number; //学习时效
  incompleteTimes?: number[]; //未完成学习通知时间
  optNotify: boolean; //选修是否通知
  trainMode: SchModeType;
  ownerId: number;
  ownerName: string;
  review?: string;
};

export type ServiceR = Course;
