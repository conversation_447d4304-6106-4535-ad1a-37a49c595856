/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-21
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  courseId: number;
  fileInfoList: McUploadFileJSON[];
};

export type ApiArgs = {
  courseId: number;
  /**
   * 附件
   */
  fileInfoList: BackendMcUploadFile[];
};

export type ApiResponse = WriteResponse;
