/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './save-course-images';
import type { SvcQuery } from './save-course-images.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function saveCourseImages(args: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
