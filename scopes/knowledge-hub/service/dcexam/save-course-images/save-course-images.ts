/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-21
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, SvcQuery } from './save-course-images.type';

const endpoint = '/dcexam/course/image/save';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/15/interface/api/26743)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiArgs = {
      courseId: args.courseId,
      fileInfoList: args.fileInfoList?.map(file => McUploadFile.fromJSON(file).toApiObject()),
    };
    return await request.tryPost<boolean, ApiArgs>(endpoint, params);
  };
}
