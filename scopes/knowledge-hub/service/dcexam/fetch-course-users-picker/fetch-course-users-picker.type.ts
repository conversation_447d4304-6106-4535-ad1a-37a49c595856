/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-5
 *
 * @packageDocumentation
 */
   
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 用户
   */
  users?: string[]| null;
  /**
   * 角色
   */
  roles?: string[]| null;
  /**
   * 部门
   */
  departments?: string[]| null;
};



export type UnknownType = {
  roleCode: string;
  roleId: any;
  roleName: string;
};

export type UnknownType = {
  blockGuidList: string[];
  loginName: string;
  roleList: string[];
  userId: any;
  userName: string;
};

export type UnknownType = {
  departmentId: string;
  departmentName?: string| null;
};

export type ApiResponseData = {
  roles?: UnknownType[]| null;
  users?: UnknownType[]| null;
  departments?: UnknownType[]| null;
};


export type ApiResponse = BackendResponse<ApiResponseData>;
