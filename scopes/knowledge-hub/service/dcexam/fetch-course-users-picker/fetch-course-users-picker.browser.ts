/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-5
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-course-users-picker.js';
import type { ApiArgs, ApiResponse } from './fetch-course-users-picker.type.js';

/**
 * @param args
* @returns
 */
export function fetchCourseUsersPicker(args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
