---
description: 'A fetchCourseUsersPicker HTTP API service.'
labels: ['service', 'http']
---

查询课程人员配置信息

## Usage

### Browser

```ts
import { fetchCourseUsersPicker } from '@manyun/knowledge-hub.service.dcexam.fetch-course-users-picker';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchCourseUsersPickerService } from '@manyun/knowledge-hub.service.dcexam.fetch-course-users-picker/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchCourseUsersPickerService.from(nodeRequest);
```
