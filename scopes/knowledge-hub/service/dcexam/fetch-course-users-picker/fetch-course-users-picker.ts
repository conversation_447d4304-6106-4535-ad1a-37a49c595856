/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-5
 *
 * @packageDocumentation
 */

import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-course-users-picker.type.js';

const endpoint = '/dcexam/course/user/config/query';

/**
* @see [Doc](http://yapi.manyun-local.com/project/15/interface/api/27261)
*
* @param request
* @returns
*/
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> => {
    return await request.tryPost< ApiResponse['data'] | null, ApiArgs>(endpoint, args);

  };
}             
