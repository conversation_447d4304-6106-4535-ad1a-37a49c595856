/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { fetchAllCoursewaresEndpoint, fetchAuthCoursewaresEndpoint } from './fetch-coursewares';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-coursewares.type';

/**
 * 知识中心 - 课件列表 / 知识库 - 课件列表
 *
 * @see [Doc1](https://manyun.yuque.com/ewe5b3/zcferb/bnaari)
 * @see [Doc2](https://manyun.yuque.com/ewe5b3/zcferb/hc2f2m)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchCoursewares({ variant, categoryCode, name }: ServiceQ) {
  const apiQ: ApiQ = {
    categoryCode,
    fileName: name,
  };
  const result = await webRequest.tryPost<ServiceR, ApiQ>(
    variant === 'auth' ? fetchAuthCoursewaresEndpoint : fetchAllCoursewaresEndpoint,
    apiQ
  );
  const { error, data } = result;
  if (error || data === null) {
    return { ...result, data: null };
  }
  const coursewares = data.data.map(courseware => {
    return {
      ...courseware,
      groupKey: `${courseware.fileType}_${courseware.id}`,
    };
  });
  return { ...result, data: { data: coursewares } };
}
