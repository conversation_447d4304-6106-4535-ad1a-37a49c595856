/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiQ = {
  categoryCode: string;
  fileName?: string;
};

export type Variant = 'auth' | 'all';

export type BackendCourseware = {
  id: number;
  name?: string;
  order?: number;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  fileTime?: number;
  fileType?: string;
  isDownload?: boolean;
  gmtCreate?: number;
  gmtModified?: number;
  categoryCode?: string;
  operatorId?: number;
  operatorName?: string;
  stopAfterLeave?: boolean;
  allowFastForward?: boolean;
  minTime?: number;
  studyTime?: number;
  isFinished?: boolean;
};

export type BackendCoursewareConfig = {
  fileId: number;
  name: string;
  order: number;
  allowFastForward?: boolean;
  stopAfterLeave?: boolean;
  minTime?: number;
  fileName?: string;
  filePath?: string;
  fileSize?: number;
  fileTime?: number;
  fileType?: string;
  categoryCode: string;
};

export type CoursewareConfig = {
  fileId: number;
  name: string;
  order: number;
  allowSeekForward?: boolean;
  holdOnBlur?: boolean;
  minSecondsAsCompleted?: number;
};

export type CoursewareProgress = {
  studyTime?: number;
  isFinished?: boolean;
};

export type ServiceQ = {
  variant: Variant;
  /** 课件分类 */
  categoryCode: string;
  /** 课件名称 */
  name?: string;
};

export type ServiceR = {
  data: BackendCourseware[];
};

export type ApiR = ListResponse<BackendCourseware[]>;
