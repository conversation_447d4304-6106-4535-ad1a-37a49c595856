---
description: '知识中心-课件管理-课件列表 知识库.'
labels: ['service', 'http', 'fetch-coursewares']
---

## fetchCoursewares

### Node Usage

```ts
import { fetchCoursewares } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares/dist/index.node';

const { data, error } = await fetchCoursewares();
```

### Browser Usage

#### 课件列表

```ts
import { fetchCoursewares } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares/dist/index.browser';

const { data, error } = await fetchCoursewares({ variant: 'all', categoryCode: 1 });
```

#### 知识库

```ts
import { fetchCoursewares } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares/dist/index.browser';

const { data, error } = await fetchCoursewares({ variant: 'auth', categoryCode: 2 });
```
