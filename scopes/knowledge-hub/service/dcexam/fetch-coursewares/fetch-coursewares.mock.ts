/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { fetchAllCoursewaresEndpoint, fetchAuthCoursewaresEndpoint } from './fetch-coursewares';
import type { ApiQ, ApiR } from './fetch-coursewares.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(fetchAllCoursewaresEndpoint, {
      asymmetricMatch({ categoryCode }: ApiQ) {
        return categoryCode == '1';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: [
        {
          id: 1,
          fileName: '文件夹1',
          categoryCode: '1',
          fileSize: 22,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: 'floder',
        },
        {
          id: 2,
          fileName: '课件2',
          categoryCode: '2',
          fileSize: 2233,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.MP4',
          filePath: 'a',
          fileTime: 1,
        },
        {
          id: 3,
          fileName: '课件3',
          categoryCode: '3',
          fileSize: 2445,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.MP4',
        },
      ],
      total: 3,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchAllCoursewaresEndpoint, {
      asymmetricMatch({ fileName }: ApiQ) {
        return fileName == 'aaa';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: [
        {
          id: 10,
          fileName: '文件夹3',
          categoryCode: '10',
          fileSize: 22,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: 'floder',
        },
      ],
      total: 1,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchAllCoursewaresEndpoint, {
      asymmetricMatch({ categoryCode }: ApiQ) {
        return categoryCode == '2';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: [
        {
          id: 2,
          fileName: '文件夹2',
          categoryCode: '2',
          fileSize: 22,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: 'floder',
        },
        {
          id: 3,
          fileName: '文件夹3',
          categoryCode: '2',
          fileSize: 22,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: 'floder',
        },
        {
          id: 4,
          fileName: '课件4',
          categoryCode: '2',
          fileSize: 2233,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.MP4',
        },
        {
          id: 5,
          fileName: '课件5',
          categoryCode: '2',
          fileSize: 2445,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.MP4',
        },
        {
          id: 6,
          fileName: '课件6',
          categoryCode: '2',
          fileSize: 2445,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.PDF',
        },
      ],
      total: 5,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchAuthCoursewaresEndpoint, {
      asymmetricMatch({ categoryCode }: ApiQ) {
        return categoryCode == '2';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: [
        {
          id: 2,
          fileName: '文件夹2',
          categoryCode: '2',
          fileSize: 22,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: 'floder',
        },
        {
          id: 4,
          fileName: '课件4',
          categoryCode: '4',
          fileSize: 2233,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.MP4',
        },
        {
          id: 5,
          fileName: '课件5',
          categoryCode: '5',
          fileSize: 2445,
          operatorName: 'admin',
          gmtCreate: 1634611297738,
          fileType: '.MP4',
        },
      ],
      total: 3,
      errCode: null,
      errMessage: null,
    });
}
