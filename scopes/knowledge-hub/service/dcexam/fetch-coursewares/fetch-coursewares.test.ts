/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCoursewares as webService } from './fetch-coursewares.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "fetch coursewares success"', async () => {
  const { data, error } = await webService({ variant: 'all', categoryCode: '1' });

  expect(data!.data).toHaveLength(2);
  expect(error).toBe(undefined);
});

test('should resolve "fetch coursewares fail"', async () => {
  const { data, error } = await webService({ variant: 'all', categoryCode: '2' });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "fetch auth coursewares success"', async () => {
  const { data, error } = await webService({ variant: 'auth', categoryCode: '2' });

  expect(data!.data).toHaveLength(3);
  expect(error).toBe(undefined);
});

test('should resolve "fetch auth coursewares fail"', async () => {
  const { data, error } = await webService({ variant: 'auth', categoryCode: '1' });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
