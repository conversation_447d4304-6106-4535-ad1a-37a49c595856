/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { makeupTestUsersEndPoint, userInfoEndPoint } from './fetch-exam-failed-users';
import type { ApiQ, ApiR, UserInfoApiQ, UserInfoApiR } from './fetch-exam-failed-users.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(makeupTestUsersEndPoint).reply<ApiR>(200, {
    success: true,
    data: [1, 2, 3, 4],
    errCode: null,
    errMessage: null,
  });

  webMock.onPost(userInfoEndPoint).reply<UserInfoApiR>(200, {
    success: true,
    data: [
      {
        id: 1,
        userName: 'admin',
        loginName: 'admin',
      },
      {
        id: 2,
        userName: 'test1',
        loginName: '<EMAIL>',
      },
      {
        id: 3,
        userName: 'test2',
        loginName: '<EMAIL>',
      },
      {
        id: 4,
        userName: 'test3',
        loginName: '<EMAIL>',
      },
    ],
    errCode: null,
    errMessage: null,
  });
}
