---
description: '知识中心-考试管理-获取补考人员信息'
labels: ['service', 'http', 'fetch-exam-failed-users']
---

## fetchExamFailedUsers

### Node Usage

```ts
import { fetchExamFailedUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users/dist/index.node';

const { data, error } = await fetchExamFailedUsers();
```

### Browser Usage

```ts
import { fetchExamFailedUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-failed-users/dist/index.browser';

const { data, error } = await fetchExamFailedUsers({
  examId: 1,
});
```
