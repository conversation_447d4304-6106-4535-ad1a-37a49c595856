/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { UserInfoApiQ } from '@manyun/knowledge-hub.service.dcexam.request-makeup-test';
import { EnhancedAxiosResponse, webRequest } from '@manyun/service.request';

import { makeupTestUsersEndPoint, userInfoEndPoint } from './fetch-exam-failed-users';
import type { ApiQ, ServiceQ, ServiceR, UserInfoRequestR } from './fetch-exam-failed-users.type';

/**
 * 知识中心-考试管理-获取补考人员信息
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/cfwhii)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExamFailedUsers({
  examId,
}: ServiceQ): Promise<EnhancedAxiosResponse<UserInfoRequestR>> {
  const apiQ: ApiQ = { examId };
  const failedUsersResult = await webRequest.tryPost<ServiceR, ApiQ>(makeupTestUsersEndPoint, apiQ);
  if (
    !failedUsersResult.error &&
    failedUsersResult.data.data &&
    failedUsersResult.data.data.length > 0
  ) {
    return await webRequest.tryPost<UserInfoRequestR, UserInfoApiQ>(userInfoEndPoint, {
      userIds: failedUsersResult.data.data,
    });
  } else {
    return {
      ...failedUsersResult,
      data: null,
    };
  }
}
