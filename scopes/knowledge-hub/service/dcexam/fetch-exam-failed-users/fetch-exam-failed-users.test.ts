/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchExamFailedUsers as webService } from './fetch-exam-failed-users.browser';
import { registerWebMocks } from './fetch-exam-failed-users.mock';

// // registerWebMocks();

test('toMatch make up users', async () => {
  const { data, error } = await webService({ examId: 1 });
  const expectUsers = [
    { id: 1, userName: 'admin', loginName: 'admin' },
    { id: 2, userName: 'test1', loginName: '<EMAIL>' },
    { id: 3, userName: 'test2', loginName: '<EMAIL>' },
    { id: 4, userName: 'test3', loginName: '<EMAIL>' },
  ];
  expect(data).toHaveLength(4);
  expect(data).toMatchObject(expectUsers);
  expect(error).toBe(undefined);
});
