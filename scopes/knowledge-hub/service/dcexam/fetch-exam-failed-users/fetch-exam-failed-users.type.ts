/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendUserInfo = {
  id: number;
  userName: string;
  loginName: string;
};

export type ApiQ = {
  examId: number;
};

export type UserInfoApiQ = {
  userIds: number[];
};

export type ApiR = Response<number[]>;

export type UserInfoApiR = Response<BackendUserInfo[]>;

export type ServiceQ = {
  examId: number;
};

export type ServiceR = {
  data: number[];
  total: number;
};

export type UserInfoRequestR = {
  data: BackendUserInfo[];
  total: number;
} | null;
