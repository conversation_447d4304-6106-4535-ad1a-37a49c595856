/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchQuestionsCount as webService } from './fetch-questions-count.browser';
import { registerWebMocks } from './fetch-questions-count.mock';

// registerWebMocks();
test('get questions count', async () => {
  const { data, error } = await webService({ categoryCode: 1 });
  //  data);
  // expect(data).toHaveLength(0);
  // expect(data[0]).toHaveProperty('type', 'SINGLE_CHOICE');
  // expect(data[0]).toHaveProperty('EASIER_THAN_EASY', 1);
  // expect(data[0]).toHaveProperty('EASY', 2);
  // expect(data[0]).toHaveProperty('NORMAL', 3);
  // expect(data[0]).toHaveProperty('HARD', 4);
  // expect(data[0]).toHaveProperty('HARDER_THAN_HARD', 5);
  // expect(data[1]).toHaveProperty('type', 'MULTIPLE_CHOICE');

  // expect(error).toBe(undefined);
});
