/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */

import { webRequest } from '@manyun/service.request';
import {
  BackendQuestionDifficulty,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

import { endpoint } from './fetch-questions-count';
import type { ServiceQ, ServiceR, ApiQ, QuestionsCount } from './fetch-questions-count.type';

/**
 * 知识中心-试题管理-根据试题类型查询试题数量
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/cgxgop)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchQuestionsCount({ categoryCode, autoGrade }: ServiceQ) {
  const { data, error, ...rest } = await webRequest.tryPost<ServiceR, ApiQ>(endpoint, { categoryCode, autoGrade });

  if (!error && data != null) {
    const questionsCount: QuestionsCount[] = [];

    for (let typeIndex = 1; typeIndex < 6; typeIndex++) {
      questionsCount.push({
        type: BackendQuestionType[typeIndex] as any as BackendQuestionType,
        EASIER_THAN_EASY_ALL: 0,
        EASY_ALL: 0,
        NORMAL_ALL: 0,
        HARD_ALL: 0,
        HARDER_THAN_HARD_ALL: 0,
      });
    }

    (data.data || []).forEach((row) => {
      const rowType: BackendQuestionType = <BackendQuestionType>(
        (<unknown>BackendQuestionType[row.type])
      );
      const difficultyObj: any = questionsCount.find((qt) => qt.type == rowType);
      if (difficultyObj) {
        for (const key in row.difficultyNumber) {
          if (BackendQuestionDifficulty[key]) {
            difficultyObj[BackendQuestionDifficulty[key] + '_ALL'] = row.difficultyNumber[key] || 0;
          }
        }
      }
    });
    
    return { data: questionsCount, error, ...rest };
  } else {
    return { data: [], error, ...rest };
  }
}
