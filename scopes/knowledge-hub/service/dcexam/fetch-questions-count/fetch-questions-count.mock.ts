/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-questions-count';
import type { ApiQ, ApiR } from './fetch-questions-count.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      categoryCode: 1,
    })
    .reply<ApiR>(200, {
      success: true,
      data: [
        {
          type: 1, //题目类型。1-单选；2-多选；3-判断；4-填空；5-问答
          difficultyNumber: {
            //1-5从易到难
            1: 1,
            2: 2,
            3: 3,
            4: 4,
            5: 5,
          },
        },
        {
          type: 2, //题目类型。1-单选；2-多选；3-判断；4-填空；5-问答
          difficultyNumber: {
            //1-5从易到难
            1: 6,
            2: 7,
            3: 8,
            4: 9,
            5: 10,
          },
        },
        {
          type: 3, //题目类型。1-单选；2-多选；3-判断；4-填空；5-问答
          difficultyNumber: {
            //1-5从易到难
            1: 6,
            2: 7,
            3: 8,
            4: 9,
            5: 10,
          },
        },
        {
          type: 4, //题目类型。1-单选；2-多选；3-判断；4-填空；5-问答
          difficultyNumber: {
            //1-5从易到难
            1: 6,
            2: 7,
            3: 8,
            4: 9,
            5: 10,
          },
        },
        {
          type: 5, //题目类型。1-单选；2-多选；3-判断；4-填空；5-问答
          difficultyNumber: {
            //1-5从易到难
            1: 6,
            2: 7,
            3: 8,
            4: 9,
            5: 10,
          },
        },
      ],
      total: 0,
      errCode: null,
      errMessage: null,
    });
}
