---
description: '知识中心-试题管理-根据试题类型查询试题数量'
labels: ['service', 'http', 'fetch-questions-count']
---

## fetchQuestionsCount

### Node Usage

```ts
import { fetchQuestionsCount } from '@manyun/knowledge-hub.service.dcexam.fetch-questions-count/dist/index.node';

const { data, error } = await fetchQuestionsCount();
```

### Browser Usage

#### 获取试题数量

```ts
import { fetchQuestionsCount } from '@manyun/knowledge-hub.service.dcexam.fetch-questions-count/dist/index.browser';

const { data, error } = await fetchQuestionsCount({
  categoryCode: 1,
});
```
