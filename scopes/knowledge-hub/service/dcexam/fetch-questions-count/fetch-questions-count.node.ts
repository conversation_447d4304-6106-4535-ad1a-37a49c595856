/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './fetch-questions-count.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchQuestionsCount(data: ServiceQ) {
  throw new Error("fetchQuestionsCount's node version not implemented!");
}
