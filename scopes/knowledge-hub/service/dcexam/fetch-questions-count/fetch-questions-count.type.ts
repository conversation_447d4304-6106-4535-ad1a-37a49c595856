/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendQuestionType } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export type BackendQuestionsCount = {
  type: number; //题目类型。1-单选；2-多选；3-判断；4-填空；5-问答
  difficultyNumber: Record<number, number>;
};

export type QuestionsCount = {
  type: BackendQuestionType;
  EASIER_THAN_EASY?: number;
  EASY?: number;
  NORMAL?: number;
  HARD?: number;
  HARDER_THAN_HARD?: number;
  EASIER_THAN_EASY_ALL: number;
  EASY_ALL: number;
  NORMAL_ALL: number;
  HARD_ALL: number;
  HARDER_THAN_HARD_ALL: number;
};

export type ApiQ = {
  categoryCode: number | string;
  autoGrade?: boolean; //是否为系统判卷
};

export type ApiR = ListResponse<BackendQuestionsCount[]>;

export type ServiceQ = {
  categoryCode: number | string;
  autoGrade?: boolean; //是否为系统判卷
};

export type ServiceR = {
  data: BackendQuestionsCount[];
  total: number;
};
