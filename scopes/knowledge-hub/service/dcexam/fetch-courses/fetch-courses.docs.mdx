---
description: '知识中心-课程管理-课程列表 我的课程.'
labels: ['service', 'http', 'fetch-courses']
---

## fetchCourses

### Node Usage

```ts
import { fetchCourses } from '@manyun/knowledge-hub.service.dcexam.fetch-courses/dist/index.node';

const { data, error } = await fetchCourses();
```

### Browser Usage

#### 课程列表

```ts
import { fetchCourses } from '@manyun/knowledge-hub.service.dcexam.fetch-courses/dist/index.browser';

const { data, error } = await fetchCourses({
  variant: 'all',
  page: 1,
  pageSize: 10,
});
```

#### 我的课程

```ts
import { fetchCourses } from '@manyun/knowledge-hub.service.dcexam.fetch-courses/dist/index.browser';

const { data, error } = await fetchCourses({
  variant: 'my',
  completedVariant: 'completed',
  categoryCode: 12,
});
```
