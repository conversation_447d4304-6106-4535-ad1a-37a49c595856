/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
//  import { registerWebMocks } from './fetch-courses.mock';
import { useRemoteMock } from '@manyun/service.request';

import { fetchCourses as webService } from './fetch-courses.browser';

// registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "all courses success"', async () => {
  const { data, error } = await webService({
    variant: 'all',
    page: 1,
    pageSize: 10,
  });
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(error).toBe(undefined);
});

test('should resolve "all courses fail"', async () => {
  const { data, error } = await webService({
    variant: 'all',
    page: 2,
    pageSize: 10,
  });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "my courses required"', async () => {
  const { data, error } = await webService({
    variant: 'my',
    required: true,
    completedVariant: 'uncompleted',
  });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "my courses unrequired"', async () => {
  const { data, error } = await webService({
    variant: 'my',
    required: false,
    completedVariant: 'uncompleted',
  });
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(error).toBe(undefined);
});
