/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { fetchCoursesEndpoint, fetchMyCoursesEndpoint } from './fetch-courses';
import type { CoursesApiQ, MyCoursesApiQ, ServiceQ, ServiceR } from './fetch-courses.type';
import {
  BackendCourseType,
  BackendStudyStatus,
  CourseStatus,
  StudyStatus,
} from './fetch-courses.type';

/**
 * 知识中心-课程列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/gpga80)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchCourses({
  variant,
  completedVariant,
  categoryCode,
  timeRange,
  name,
  required,
  enabled,
  involvedState,
  page,
  pageSize,
  courseIdList,
  fileId,
}: ServiceQ) {
  if (variant === 'my' && completedVariant) {
    const apiQ: MyCoursesApiQ = {
      categoryCode: categoryCode ? categoryCode[categoryCode.length - 1] : undefined,
      startTime: timeRange ? timeRange[0] : undefined,
      endTime: timeRange ? timeRange[1] : undefined,
      courseName: name,
      status:
        completedVariant === StudyStatus.UNCOMPLETED
          ? involvedState
            ? [involvedState]
            : [BackendStudyStatus.UNDONE, BackendStudyStatus.UN_INVOLVED]
          : [BackendStudyStatus.COMPLETED],
    };
    if (required !== undefined && completedVariant === StudyStatus.UNCOMPLETED) {
      apiQ.courseType = required ? BackendCourseType.OBLIGATORY : BackendCourseType.ELECTIVE;
    }
    return await webRequest.tryPost<ServiceR, MyCoursesApiQ>(fetchMyCoursesEndpoint, apiQ);
  } else if (variant === 'all') {
    const apiQ: CoursesApiQ = {
      courseName: name,
      categoryCode: categoryCode ? categoryCode[categoryCode.length - 1] : undefined,
      pageNum: page!,
      pageSize: pageSize!,
      courseIdList: courseIdList,
      fileId,
    };
    if (enabled !== undefined) {
      apiQ.status = enabled ? CourseStatus.NORMAL : CourseStatus.DISABLE;
    }
    return await webRequest.tryPost<ServiceR, CoursesApiQ>(fetchCoursesEndpoint, apiQ);
  } else {
    throw new Error(`fetchCourses api not supported!`);
  }
}
