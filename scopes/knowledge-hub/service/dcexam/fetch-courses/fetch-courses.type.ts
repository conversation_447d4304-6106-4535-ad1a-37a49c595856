/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { SchModeType } from '@manyun/ticket.model.task';

export enum CourseStatus {
  NORMAL = 'NORMAL',
  DISABLE = 'DISABLE',
}

export enum BackendStudyStatus {
  UNDONE = 'UNDONE',
  UN_INVOLVED = 'UN_INVOLVED',
  COMPLETED = 'COMPLETED',
}

export enum BackendCourseType {
  OBLIGATORY = 'OBLIGATORY',
  ELECTIVE = 'ELECTIVE',
}

export enum StudyStatus {
  COMPLETED = 'completed',
  UNCOMPLETED = 'uncompleted',
}

export type BackendCourse = {
  id: number;
  name?: string; //课程名称
  categoryCode?: string; //课程分类
  startTime?: number; //开始时间
  endTime?: number; //结束时间
  gmtCreate?: number; //创建时间
  gmtModified?: number; //修改时间
  status?: string; //课程状态，启用、禁用
  studyStatus?: string; //学习状态
  courseType?: string; //课程类型
  operatorId?: number;
  operatorName?: string;
  trainMode: SchModeType;
  ownerId: number;
  ownerName: string;
  review?: string;
};

export type CoursesApiQ = {
  courseName?: string;
  categoryCode?: string;
  status?: CourseStatus;
  pageNum: number;
  pageSize: number;
  courseIdList?: number[];
  fileId?: number;
};

export type MyCoursesApiQ = {
  categoryCode?: string;
  startTime?: number;
  endTime?: number;
  courseName?: string;
  courseType?: BackendCourseType;
  status: BackendStudyStatus[];
};

export type ApiR = ListResponse<BackendCourse[]>;

export type Variant = 'my' | 'all';

export type CompletedVariant = 'uncompleted' | 'completed';

export type ServiceQ = {
  variant: Variant;
  completedVariant?: CompletedVariant;
  /** 课程名称 */
  name?: string;
  /** 课程分类 */
  categoryCode?: string;
  /** 课程状态 */
  enabled?: boolean;
  /**学习状态 */
  involvedState?: BackendStudyStatus;
  /**课程类型 */
  required?: boolean;
  /** 开放时间区间 */
  timeRange?: [number, number];
  page?: number;
  pageSize?: number;
  courseIdList?: number[];
  fileId?: number;
};

export type ServiceR = {
  data: BackendCourse[];
  total: number | null;
};
