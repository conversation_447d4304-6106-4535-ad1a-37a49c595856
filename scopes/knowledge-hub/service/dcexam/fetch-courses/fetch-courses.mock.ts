/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { fetchCoursesEndpoint, fetchMyCoursesEndpoint } from './fetch-courses';
import {
  ApiR,
  BackendCourseType,
  BackendStudyStatus,
  CoursesApiQ,
  MyCoursesApiQ,
} from './fetch-courses.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(fetchCoursesEndpoint, {
      asymmetricMatch({ pageNum }: CoursesApiQ) {
        return pageNum === 1;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: Array(11)
        .fill(null)
        .map((_, idx) => ({
          id: idx,
          gmtCreate: 1111111,
          name: '课程' + idx,
          categoryCode: '12',
          startTime: 122333,
          endTime: 123344,
          status: 'NORMAL',
          operatorId: 1,
          operatorName: 'admin',
        })),
      total: 11,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchCoursesEndpoint, {
      asymmetricMatch({ pageNum }: CoursesApiQ) {
        return pageNum === 2;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: [
        {
          id: 11,
          gmtCreate: 1111111,
          name: '课程11',
          categoryCode: '12',
          startTime: 122333,
          endTime: 123344,
          status: 'NORMAL',
          operatorId: 1,
          operatorName: 'admin',
        },
      ],
      total: 11,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchMyCoursesEndpoint, {
      asymmetricMatch({ status }: MyCoursesApiQ) {
        return !status.includes(BackendStudyStatus.COMPLETED);
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: Array(10)
        .fill(null)
        .map((_, idx) => ({
          id: idx + 1,
          name: 'chen',
          categoryCode: idx.toString(),
          startTime: 122333,
          endTime: 123344,
          courseType: idx / 2 === 1 ? 'OBLIGATORY' : 'ELECTIVE',
          studyStatus: idx / 2 === 1 ? 'UNDONE' : 'UN_INVOLVED',
        })),
      total: 10,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchMyCoursesEndpoint, {
      asymmetricMatch({ courseType }: MyCoursesApiQ) {
        return courseType == BackendCourseType.ELECTIVE;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: Array(6)
        .fill(null)
        .map((_, idx) => ({
          id: idx + 1,
          name: 'chen',
          categoryCode: idx.toString(),
          startTime: 122333,
          endTime: 123344,
          courseType: idx / 2 === 1 ? 'OBLIGATORY' : 'ELECTIVE',
          studyStatus: idx / 2 === 1 ? 'UNDONE' : 'UN_INVOLVED',
        })),
      total: 6,
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchMyCoursesEndpoint, {
      asymmetricMatch({ status }: MyCoursesApiQ) {
        return status.includes(BackendStudyStatus.COMPLETED);
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: Array(4)
        .fill(null)
        .map((_, idx) => ({
          id: idx + 1,
          name: 'chen',
          categoryCode: idx.toString(),
          startTime: 122333,
          endTime: 123344,
          courseType: idx / 2 === 1 ? 'OBLIGATORY' : 'ELECTIVE',
          studyStatus: 'COMPLETED',
        })),
      total: 4,
      errCode: null,
      errMessage: null,
    });
}
