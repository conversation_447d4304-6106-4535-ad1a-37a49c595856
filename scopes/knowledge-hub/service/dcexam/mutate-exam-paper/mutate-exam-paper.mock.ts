/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { getMock } from '@manyun/service.request';

import {
  answerExamPaperEndPoint,
  markExamPaperEndPoint,
  startExamPaperEndPoint,
  submitExamPaperEndPoint,
  updateCourseLearnEndPoint,
} from './mutate-exam-paper';
import type { ApiR, StartApiR } from './mutate-exam-paper.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(startExamPaperEndPoint)
    .reply(200, {
      success: true,
      data: {
        id: 1,
        examId: 1,
        userId: 1,
        userName: 'lallaa',
        paperId: 1,
        status: BackendExamPaperStatus.PROCESS,
        startTime: 1631775056806,
        endTime: 1631775056806,
        totalGrade: 100,
        grade: 50,
        pass: true,
        correctUserId: 1,
        correctUserName: '张三',
        examDuration: 60,
        paperSectionVOList: [
          {
            section: '第一板块',
            questions: [],
          },
        ],
        submitUserId: null,
      },
      errCode: null,
      errMessage: null,
    })

    .onPost(
      new RegExp(
        `${submitExamPaperEndPoint}|${markExamPaperEndPoint}|${answerExamPaperEndPoint}|${updateCourseLearnEndPoint}`
      )
    )
    .reply(() => {
      return [
        200,
        {
          success: Math.random() * 10 > 5,
          errCode: null,
          errMessage: null,
        },
      ];
    });
}
