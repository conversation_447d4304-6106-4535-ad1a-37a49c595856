/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendExamPaperDetail } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';

import { mutateExamPaper as webService } from './mutate-exam-paper.browser';
import './mutate-exam-paper.mock';

test('start exam which type is NORMAL', async () => {
  const { data, error } = await webService('creating', { examId: 1 });

  expect((data as BackendExamPaperDetail).id).toBe(1);
  expect(error).toBe(undefined);
});

test('start exam which type is TEST', async () => {
  const { data, error } = await webService('creating', { examId: 1, courseId: 1 });

  expect((data as BackendExamPaperDetail).id).toBe(1);
  expect(error).toBe(undefined);
});

test('answer exam', async () => {
  const { data, error } = await webService('answering', {
    id: 1,
    questionId: 1,
    answers: ['A', 'B', 'C'],
  });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('submit exam', async () => {
  const { data, error } = await webService('handing-in', { id: 1 });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('mark exam', async () => {
  const { data, error } = await webService('marking', {
    markedQuestions: [
      {
        result: true,
        score: 5,
        id: 1,
      },
    ],
    id: 2,
  });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
