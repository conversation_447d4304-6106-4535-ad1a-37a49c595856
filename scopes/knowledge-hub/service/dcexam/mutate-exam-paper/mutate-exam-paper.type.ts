/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response, WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendExamPaperDetail } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';

export type MarkApiD = {
  examPaperId: number;
  correctList: {
    questionId: string | number;
    grade: number;
    correct: boolean;
  }[];
};

export type StartApiD = {
  examId: number;
  currentTime: number;
};

export type UpdateCourseLearnApiD = {
  courseId: number;
  examPaperId: number;
};

export type SubmitApiD = {
  examPaperId: number;
  forceSubmit: boolean; // 是否强制交卷
  currentTime: number;
};

export type AnswerApiD = {
  examPaperId: number;
  questionId: number;
  answers: string[];
};

export type ApiR = WriteResponse;

export type StartApiR = Response<BackendExamPaperDetail>;

/** Service Variant */
export type Variant =
  | /* 创建考卷 */ 'creating'
  | /* 答题 */ 'answering'
  | /* 判题 */ 'marking'
  | /* 保存判题 */ 'save-marking'
  | /* 交卷 */ 'handing-in';

export type CreatingExamPaperSvcD = {
  /** 考试 ID */
  examId: number;
  /** 课程 ID，若传入，则向课程中保存创建的考卷 ID */
  courseId?: number;
};

export type AnsweringExamQuestionSvcD = {
  /** 考卷 ID */
  id: number;
  /** 考题 ID */
  questionId: number;
  /** 考生作答的答案 */
  answers: string[];
};

export type MarkedQuestion = {
  /** 考题 ID */
  id: number;
  /** 考生本题得分 */
  score: number;
  /** 考生本题答对或答错 */
  result: boolean;
};

export type MarkingExamQuestionSvcD = {
  /** 考卷 ID */
  id: number;
  /** 判题结果集合 */
  markedQuestions: MarkedQuestion[];
};

export type HandingInExamPaperSvcD = {
  /** 考卷 ID */
  id: number;
  /** 是否强制交卷 */
  force?: boolean;
};

export type ServiceD<V extends Variant> = V extends 'creating'
  ? CreatingExamPaperSvcD
  : V extends 'answering'
    ? AnsweringExamQuestionSvcD
    : V extends 'marking' | 'save-marking'
      ? MarkingExamQuestionSvcD
      : HandingInExamPaperSvcD;

export type ServiceRD<V> = V extends 'creating' ? BackendExamPaperDetail : boolean;
