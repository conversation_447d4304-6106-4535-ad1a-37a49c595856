/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { BackendExamPaperDetail } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import type { EnhancedAxiosResponse } from '@manyun/service.request';
import { webRequest } from '@manyun/service.request';

import {
  answerExamPaperEndPoint,
  markExamPaperEndPoint,
  saveMarkExamPaperEndPoint,
  startExamPaperEndPoint,
  submitExamPaperEndPoint,
  updateCourseLearnEndPoint,
} from './mutate-exam-paper';
import type {
  AnswerApiD,
  AnsweringExamQuestionSvcD,
  CreatingExamPaperSvcD,
  HandingInExamPaperSvcD,
  MarkApiD,
  MarkingExamQuestionSvcD,
  ServiceD,
  ServiceRD,
  StartApiD,
  SubmitApiD,
  UpdateCourseLearnApiD,
  Variant,
} from './mutate-exam-paper.type';

/**
 * 知识中心-考试管理-开始考试、开始测试、考试答题、考试交卷、判卷.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/bxu4o1、https://manyun.yuque.com/ewe5b3/zcferb/im0f6h、https://manyun.yuque.com/ewe5b3/zcferb/vf5pok、https://manyun.yuque.com/ewe5b3/zcferb/htdl04)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateExamPaper<V extends Variant>(
  variant: V,
  data: ServiceD<V>
): Promise<EnhancedAxiosResponse<ServiceRD<V>, undefined, any>> {
  if (variant === 'creating') {
    const { examId, courseId } = data as CreatingExamPaperSvcD;

    const startingAnswerResult = await webRequest.tryPost<BackendExamPaperDetail, StartApiD>(
      startExamPaperEndPoint,
      {
        examId,
        currentTime: Date.now(),
      }
    );

    if (startingAnswerResult.error) {
      return startingAnswerResult as any;
    }

    // 把考卷 ID 保存到课程中
    if (courseId !== undefined) {
      const saveExamPaperIdIntoCourseResult = await webRequest.tryPost<
        boolean,
        UpdateCourseLearnApiD
      >(updateCourseLearnEndPoint, {
        examPaperId: startingAnswerResult.data.id,
        courseId,
      });
      if (saveExamPaperIdIntoCourseResult.error) {
        return {
          ...saveExamPaperIdIntoCourseResult,
          error: saveExamPaperIdIntoCourseResult.error,
          data: null,
        } as any;
      }
    }

    return startingAnswerResult as any;
  }

  if (variant === 'answering') {
    const { id, questionId, answers } = data as AnsweringExamQuestionSvcD;

    return (await webRequest.tryPost<boolean, AnswerApiD>(answerExamPaperEndPoint, {
      examPaperId: id,
      questionId,
      answers,
    })) as any;
  }

  if (variant === 'marking' || variant === 'save-marking') {
    const { id, markedQuestions } = data as MarkingExamQuestionSvcD;

    return (await webRequest.tryPost<boolean, MarkApiD>(
      variant === 'save-marking' ? saveMarkExamPaperEndPoint : markExamPaperEndPoint,
      {
        examPaperId: id,
        correctList: markedQuestions.map(({ id, score, result }) => ({
          questionId: id,
          grade: score,
          correct: result,
        })),
      }
    )) as any;
  }

  if (variant === 'handing-in') {
    const { id, force = false } = data as HandingInExamPaperSvcD;

    return (await webRequest.tryPost<boolean, SubmitApiD>(submitExamPaperEndPoint, {
      examPaperId: id,
      forceSubmit: force,
      currentTime: Date.now(),
    })) as any;
  }

  throw new Error(`mutateExamPaper api not supported!`);
}
