---
description: '知识中心-考试管理-开始考试、开始测验、考试答题、考试交卷、判卷'
labels: ['service', 'http', 'mutate-exam-paper']
---

## mutateExamPaper

### Node Usage

```ts
import { mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper/dist/index.node';

const { data, error } = await mutateExamPaper();
```

### Browser Usage

#### 开始考试

```ts
import { mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper/dist/index.browser';

const { data, error } = await mutateExamPaper({
  examId: 1,
});
```

#### 开始测验

```ts
import { mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper/dist/index.browser';

const { data, error } = await mutateExamPaper({
  examId: 1,
  courseId: 1,
});
```

#### 考试答题

```ts
import { mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper/dist/index.browser';

const { data, error } = await mutateExamPaper({
  examPaperId: 1,
  questionId: 1,
  answers: ['A', 'B', 'C'],
});
```

#### 考试交卷

```ts
import { mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper/dist/index.browser';

const { data, error } = await mutateExamPaper({
  examPaperId: 1,
});
```

#### 考试批改

```ts
import { mutateExamPaper } from '@manyun/knowledge-hub.service.dcexam.mutate-exam-paper/dist/index.browser';

const { data, error } = await mutateExamPaper({
  examPaperId: 1,
  correctList: [{ correct: true, grade: 5, questionId: 1 }],
});
```
