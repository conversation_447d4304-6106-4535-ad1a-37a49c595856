/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import {
  exmaPaperUserEndPoint,
  myAvailableExamEndPoint,
  myHistoryExamEndPoint,
} from './fetch-exam-paper-users';
import type {
  ApiQ,
  BackendMyExam,
  ExamPaperUser,
  MyExamApiQ,
  RequestRD,
  ServiceQ,
  ServiceR,
} from './fetch-exam-paper-users.type';
import { userExamPaperStateOrderMapper } from './fetch-exam-paper-users.type';

/**
 * 知识中心-考试管理-考卷列表(某个考试的考卷、我的当前考卷、我的历史考卷).
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/rdxzmf)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExamPaperUsers({
  examId,
  variant,
  idcs,
  userId,
  roleCodes,
  userExamState,
  pass,
  timeRange,
  userExamName,
  needOrder = true,
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
}: ServiceQ): Promise<EnhancedAxiosResponse<ServiceR<BackendMyExam | ExamPaperUser[]>, any, any>> {
  if (examId) {
    const apiQ: ApiQ = {
      examId,
      pass,
      roleCodes,
      userId,
      status: userExamState,
      idcTags: idcs,
    };
    const { data, error, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(
      exmaPaperUserEndPoint,
      apiQ
    );
    if (!error && data?.data) {
      const examPaperUsers: ExamPaperUser[] = data.data
        .map(examPaperUser => ({
          ...examPaperUser,
          userName: examPaperUser.userDetail?.userName,
          userId: examPaperUser.userDetail?.id,
          idcs: examPaperUser.userDetail?.resourceList?.map(idc => idc.resourceCode),
          idcIds: examPaperUser.userDetail?.resourceList?.map(idc => idc.resourceCode),
          roles: examPaperUser.userDetail?.roleInfoList?.map(role => role.roleName),
          roleIds: examPaperUser.userDetail?.roleInfoList?.map(role => role.id),
          isDefaultUser: examPaperUser.defaultUser,
        }))
        .sort(
          (a, b) =>
            userExamPaperStateOrderMapper[a.status] - userExamPaperStateOrderMapper[b.status]
        )
        .sort((a, b) => {
          if (a.status === b.status && typeof a.order == 'number' && typeof b.order == 'number') {
            return a.order - b.order;
          }
          return 0;
        });

      // @ts-ignore types
      return { data: { data: examPaperUsers, total: examPaperUsers.length }, error, ...rest };
    }

    return { data: { data: [], total: 0 }, error, ...rest };
  } else if (variant) {
    let endpoint = myAvailableExamEndPoint;
    if (variant === 'myHistory') {
      endpoint = myHistoryExamEndPoint;
    }
    const apiQ: MyExamApiQ = {
      startTime: timeRange && timeRange[0],
      endTime: timeRange && timeRange[1],
      name: userExamName,
      needOrder,
    };
    return webRequest.tryPost<ServiceR<BackendMyExam>, MyExamApiQ>(endpoint, apiQ);
  } else {
    throw new Error(`fetchExamPaperUsers service not supported!`);
  }
}
