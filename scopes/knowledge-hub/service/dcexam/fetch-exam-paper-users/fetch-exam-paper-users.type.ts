/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Variant = 'myHistory' | 'myAvailable';

export enum BackendExamPaperStatus {
  INIT = 'INIT',
  PROCESS = 'PROCESS',
  SUBMIT = 'SUBMIT',
  WAIT_GRADE = 'WAIT_GRADE',
  GRADED = 'GRADED',
}

export const ExamPaperStatusMapper = {
  [BackendExamPaperStatus.INIT]: '未考试',
  [BackendExamPaperStatus.PROCESS]: '未交卷',
  [BackendExamPaperStatus.SUBMIT]: '已交卷',
  [BackendExamPaperStatus.WAIT_GRADE]: '待判卷',
  [BackendExamPaperStatus.GRADED]: '已完成',
};

export const userExamPaperStateOrderMapper: Record<BackendExamPaperStatus, number> = {
  [BackendExamPaperStatus.SUBMIT]: 0,
  [BackendExamPaperStatus.WAIT_GRADE]: 1,
  [BackendExamPaperStatus.PROCESS]: 2,
  [BackendExamPaperStatus.GRADED]: 3,
  [BackendExamPaperStatus.INIT]: 4,
};

/**
 *用户考卷状态
 */
export enum BackendUserExamPaperSatus {
  INIT = 'INIT',
  STARTED = 'STARTED',
  DURING = 'DURING',
  SUBMIT = 'SUBMIT',
  GRADED = 'GRADED',
}

export const UserExamPaperStatusMapper = {
  [BackendUserExamPaperSatus.INIT]: '未开始',
  [BackendUserExamPaperSatus.STARTED]: '可参加',
  [BackendUserExamPaperSatus.DURING]: '进行中',
  [BackendUserExamPaperSatus.SUBMIT]: '判卷中',
  [BackendUserExamPaperSatus.GRADED]: '已完成',
};

export type BackendExamPaperUser = {
  id: number; // 考卷id
  examId: number;
  userDetail: {
    id: number;
    userName: string;
    resourceList: { resourceCode: string }[] | null;
    roleInfoList: { id: number; roleName: string }[] | null;
  }; //用户信息
  paperId: number;
  status: BackendExamPaperStatus; //考卷状态
  startTime: number | null;
  endTime: number | null;
  order: number | null; //排名
  forceSubmit: boolean; //是否强制交卷
  /** 如果是 `null`，表示从未提交过判卷结果 */
  grade: number | null;
  pass: boolean | null; //是否通过
  correctUserId: number | null; //判卷人id
  correctUserName: string | null; //判卷人姓名
  autoGrade: boolean;
  defaultUser: boolean; //是否为必考用户
};

//组装成前端所需要的数据结构
export type ExamPaperUser = Omit<BackendExamPaperUser, 'userDetail'> & {
  userName: string;
  userId: number;
  roles?: string[];
  roleIds?: number[];
  idcs?: string[];
  idcIds?: string[];
  autoGrade: boolean;
  isDefaultUser: boolean;
};

export type BackendMyExam = {
  id: number; // 考卷id
  examId: number;
  paperId: number; //试卷id
  name: string; //考试名称
  userId: number; //用户id
  userName: string; //用户姓名
  totalGrade: number; //考试总分
  duration: number; //考试时长（分钟数）
  ownerStatus: BackendUserExamPaperSatus;
  defaultUser: boolean; //是否为必考人员
  startTime: number;
  endTime: number;
  gmtModified: number;
};

export type BackendMyExamHistory = BackendMyExam & {
  pass: number;
};

export type ApiQ = {
  examId: number;
  idcTags?: string[];
  pass?: boolean;
  roleCodes?: number[];
  status?: string;
  userId?: number;
};

export type MyExamApiQ = {
  endTime?: number | null;
  startTime?: number | null;
  name?: string;
  needOrder?: boolean;
};

export type ApiR<T> = ListResponse<T[]>;

export type RequestRD = {
  data: BackendExamPaperUser[] | null;
  total: number;
} | null;

export type ServiceQ = {
  examId?: number;
  variant?: Variant;
  idcs?: string[];
  userId?: number;
  roleCodes?: number[];
  userExamState?: BackendExamPaperStatus;
  timeRange?: [number, number] | null;
  userExamName?: string; //考试名称
  pass?: boolean;
  needOrder?: boolean;
};

export type ServiceR<T> = {
  total: number;
  data: T[];
};
