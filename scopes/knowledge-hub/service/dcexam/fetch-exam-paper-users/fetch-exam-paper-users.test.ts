/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { fetchExamPaperUsers as webService } from './fetch-exam-paper-users.browser';
import { registerWebMocks } from './fetch-exam-paper-users.mock';
import { userExamPaperStateOrderMapper } from './fetch-exam-paper-users.type';
import type { ExamPaperUser } from './fetch-exam-paper-users.type';

registerWebMocks(getMock('web'));

test('request exam paper uses by examId', async () => {
  const { data, error } = await webService({ examId: 1 });
  const uniqUserExamPaperStateOrders: number[] = [];
  const userOrders: number[] = [];
  (data.data as ExamPaperUser[]).forEach(userExamPaper => {
    const userExamPaperStateOrder = userExamPaperStateOrderMapper[userExamPaper.status];
    if (!uniqUserExamPaperStateOrders.includes(userExamPaperStateOrder)) {
      uniqUserExamPaperStateOrders.push(userExamPaperStateOrder);
    }
    if (typeof userExamPaper.order == 'number') {
      userOrders.push(userExamPaper.order);
    }
  });
  const isUserExamPaperStateOrdersASC =
    JSON.stringify([...uniqUserExamPaperStateOrders].sort((a, b) => a - b)) ===
    JSON.stringify(uniqUserExamPaperStateOrders);
  const isUserOrderASC =
    JSON.stringify([...userOrders].sort((a, b) => a - b)) === JSON.stringify(userOrders);

  expect(isUserExamPaperStateOrdersASC).toBe(true);
  expect(isUserOrderASC).toBe(true);
  expect(data.data).toHaveLength(32);
  expect(error).toBe(undefined);
});

test('request my available exams', async () => {
  const { data, error } = await webService({ variant: 'myAvailable' });
  expect(data.data).toHaveLength(1);
  expect(error).toBe(undefined);
});

test('request my available exams by serach timeRang', async () => {
  const { data, error } = await webService({ variant: 'myAvailable', timeRange: [1, 2] });

  expect(data.data).toHaveLength(1);
  expect(error).toBe(undefined);
});

// test('request my history exams', async () => {
//   const { data, error } = await webService({ variant: 'myHistory' });
//   expect(data).toBe(null);
//   expect(error).not.toBe(undefined);
// });
