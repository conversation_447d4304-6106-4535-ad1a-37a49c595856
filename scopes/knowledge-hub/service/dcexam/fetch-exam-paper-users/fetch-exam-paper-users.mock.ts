/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { BackendExamPaperStatus, BackendUserExamPaperSatus } from '.';
import {
  exmaPaperUserEndPoint,
  myAvailableExamEndPoint,
  myHistoryExamEndPoint,
} from './fetch-exam-paper-users';
import type {
  ApiQ,
  ApiR,
  BackendExamPaperUser,
  BackendMyExam,
} from './fetch-exam-paper-users.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(exmaPaperUserEndPoint)
    .reply<ApiR<BackendExamPaperUser>>(config => {
      const { examId } = JSON.parse(config.data) as ApiQ;

      const states = [
        BackendExamPaperStatus.INIT,
        BackendExamPaperStatus.PROCESS,
        BackendExamPaperStatus.SUBMIT,
        BackendExamPaperStatus.GRADED,
      ];

      return [
        200,
        {
          success: true,
          data: Array(32)
            .fill(null)
            .map((_, idx) => {
              const userExamPaperState = states[Math.floor(Math.random() * states.length)];
              const isCompleted = userExamPaperState === BackendExamPaperStatus.GRADED;
              const order = isCompleted ? Math.floor(Math.random() * 100) : null;
              const userScore = isCompleted ? Math.floor(Math.random() * 100) : null;

              return {
                id: idx + 1, //考卷id
                examId, //考试id
                userDetail: {
                  id: 777,
                  userName: 'Jerry',
                  resourceList: [
                    {
                      id: 1,
                      resourceCode: 'EC01.A',
                    },
                  ],
                  roleInfoList: [
                    {
                      id: 1,
                      roleName: '小喽啰',
                    },
                  ],
                }, //用户信息
                order,
                forceSubmit: true, //是否强制交卷
                paperId: 1, //试卷id
                status: userExamPaperState, //考卷状态
                startTime: 1631774746923, //开始时间
                endTime: 1631774746923, //结束时间
                grade: userScore, //分数
                pass: Math.floor(Math.random() * states.length) > states.length / 2, //是否通过
                correctUserId: 1, //判卷人id
                correctUserName: '张三', //判卷人姓名
              };
            }),
          total: 16,
          errCode: null,
          errMessage: null,
        },
      ];
    })

    .onPost(myAvailableExamEndPoint)
    .reply<ApiR<BackendMyExam>>(200, {
      success: true,
      data: [
        {
          id: 1, //考卷id
          name: '考试名称',
          examId: 1, //考试id
          userId: 1, //用户id
          paperId: 1, //试卷id
          startTime: 1631779810163, //考试开始时间
          endTime: 1631779810163, //考试结束时间
          duration: 50, //考试时长（分钟数）
          ownerStatus: BackendUserExamPaperSatus.INIT,
          defaultUser: true, //是否为必考人员
          userName: 'Jerry',
          totalGrade: 100,
          gmtModified: Date.now(),
        },
      ],
      total: 0,
      errCode: null,
      errMessage: null,
    });
}
