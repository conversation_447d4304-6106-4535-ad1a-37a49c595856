---
description: ' 知识中心-考试管理-考卷列表(某个考试的考卷、我的当前考卷、我的历史考卷)'
labels: ['service', 'http', 'fetch-exam-paper-users']
---

## fetchExamPaperUsers

### Node Usage

```ts
import { fetchExamPaperUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users/dist/index.node';

const { data, error } = await fetchExamPaperUsers();
```

### Browser Usage

#### 查询考试对应考卷列表

```ts
import { fetchExamPaperUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users/dist/index.browser';

const { data, error } = await fetchExamPaperUsers({
  examId: 1,
});
```

#### 用户对应的考卷列表

```ts
import { fetchExamPaperUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users/dist/index.browser';

const { data, error } = await fetchExamPaperUsers({
  variant: 'myAvailable',
});
```

#### 用户对应的历史考卷列表

```ts
import { fetchExamPaperUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users/dist/index.browser';

const { data, error } = await fetchExamPaperUsers({
  variant: 'myHistory',
});
```
