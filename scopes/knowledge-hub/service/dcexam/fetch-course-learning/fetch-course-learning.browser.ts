/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import {
  fetchCourseInfoEndpoint,
  fetchCourseLearningProgressEndpoint,
} from './fetch-course-learning';
import type {
  ApiQ,
  Lecture,
  ProgressApiQ,
  ProgressServiceR,
  ServiceQ,
  ServiceR,
} from './fetch-course-learning.type';

/**
 * 知识中心-课程学习、学习进度
 *
 * @see [Doc1](https://manyun.yuque.com/ewe5b3/zcferb/gheeiz)
 * @see [Doc2](https://manyun.yuque.com/ewe5b3/zcferb/lelkvr)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchCourseLearning({ courseId, fileId, progress }: ServiceQ) {
  if (fileId) {
    const apiQ: ProgressApiQ = {
      courseId,
      fileId,
      progress: progress!,
    };
    return await webRequest.tryPost<ProgressServiceR, ProgressApiQ>(
      fetchCourseLearningProgressEndpoint,
      apiQ
    );
  } else {
    const apiQ: ApiQ = {
      courseId,
    };

    const result = await webRequest.tryPost<ServiceR, ApiQ>(fetchCourseInfoEndpoint, apiQ);
    const { error, data } = result;
    if (error || data === null) {
      return { ...result, data: null };
    }
    const entities: Record<string, Lecture> = {};
    const sort = data.courseFileList.sort((a, b) => {
      return a.order - b.order;
    });

    data.courseFileList.map(courseware => {
      entities[courseware.fileId] = {
        categoryCode: courseware.categoryCode,
        filePath: courseware.filePath,
        fileType: courseware.fileType,
        holdOnBlur: courseware.stopAfterLeave,
        allowSeekForward: courseware.allowFastForward,
        minSecondsAsCompleted: courseware.minTime,
        studyTime: courseware.studyTime,
        isFinished: courseware.isFinished,
        name: courseware.name,
        order: courseware.order,
        id: courseware.fileId,
      };
    });

    return {
      ...result,
      data: {
        id: data.courseInfo.id,
        isFinished: data.isFinished,
        name: data.courseInfo.name,
        openingHours: [data.courseInfo.startTime, data.courseInfo.endTime],
        lectureIds: sort.map(courseFile => courseFile.fileId),
        lectureEntities: entities,
        test: {
          id: data.courseInfo.examId,
          paperId: data.examPaperId,
        },
        testCount: data.testCount,
        properties: data.courseInfo.properties,
      },
    };
  }
}
