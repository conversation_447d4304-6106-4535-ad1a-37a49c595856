/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCourseLearning as webService } from './fetch-course-learning.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "study info success"', async () => {
  const { data, error } = await webService({ courseId: 1 });

  expect(data!.isFinished).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "study info fail"', async () => {
  const { data, error } = await webService({ courseId: 2 });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "study progress info unfinished"', async () => {
  const { data, error } = await webService({ courseId: 2, fileId: 1, progress: 5 });

  expect(data!.isFinished).toBe(false);
  expect(error).toBe(undefined);
});

test('should resolve "study progress info finished"', async () => {
  const { data, error } = await webService({ courseId: 2, fileId: 2, progress: 5 });

  expect(data!.isFinished).toBe(true);
  expect(error).toBe(undefined);
});
