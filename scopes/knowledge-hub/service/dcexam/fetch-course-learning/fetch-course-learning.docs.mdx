---
description: '知识中心-课程管理-查询课程学习详情、课件学习进度.'
labels: ['service', 'http', 'fetch-course-learning']
---

## fetchCourseLearning

### Node Usage

```ts
import { fetchCourseLearning } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning/dist/index.node';

const { data, error } = await fetchCourseLearning();
```

### Browser Usage

#### 查询课程学习详情

```ts
import { fetchCourseLearning } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning/dist/index.browser';

const { data, error } = await fetchCourseLearning({ courseId: 1 });
```

#### 课件学习进度

```ts
import { fetchCourseLearning } from '@manyun/knowledge-hub.service.dcexam.fetch-course-learning/dist/index.browser';

const { data, error } = await fetchCourseLearning({ courseId: 1, fileId: 1, progress: 5 });
```
