/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import {
  fetchCourseInfoEndpoint,
  fetchCourseLearningProgressEndpoint,
} from './fetch-course-learning';
import type { ApiQ, ApiR, ProgressApiQ, ProgressApiR } from './fetch-course-learning.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(fetchCourseInfoEndpoint, {
      asymmetricMatch({ courseId }: ApiQ) {
        return courseId === 1;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        courseInfo: {
          id: 1,
          name: '课程111111',
          startTime: 122333,
          endTime: 212233,
          examId: 2,
        },
        courseFileList: [
          {
            categoryCode: '1',
            name: '课件11111111111111111111111111',
            minTime: 300,
            fileId: 2,
            order: 1,
            allowFastForward: false,
            stopAfterLeave: false,
            fileName: '课件111111.mp4',
            fileType: '.MP4',
            filePath: 'https://media.w3.org/2010/05/sintel/trailer.mp4',
            fileTime: 300,
            studyTime: 60 * Math.floor(Math.random() * 10),
            isFinished: false,
          },
          {
            categoryCode: '1',
            name: '课件22222',
            minTime: 100,
            fileId: 3,
            order: 2,
            fileName: '课件22222.pdf',
            fileType: '.PDF',
            filePath: '1',
            studyTime: 60 * Math.floor(Math.random() * 10),
            isFinished: false,
          },
          {
            categoryCode: '1',
            name: '课件33333',
            minTime: 0,
            fileId: 4,
            order: 3,
            fileName: '课件3333.pdf',
            fileType: '.PDF',
            filePath: '1',
            studyTime: 60 * Math.floor(Math.random() * 10),
            isFinished: true,
          },
        ],
        examPaperId: 1,
        isFinished: false,
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchCourseLearningProgressEndpoint, {
      asymmetricMatch({ courseId, fileId }: ProgressApiQ) {
        return courseId === 1 && fileId === 2;
      },
    })
    .reply<ProgressApiR>(200, {
      success: true,
      data: {
        fileId: 2,
        studyTime: 5,
        courseId: 1,
        isFinished: true,
      },
      errCode: null,
      errMessage: null,
    });
}
