/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendCourseFile,
  ExamConfig,
} from '@manyun/knowledge-hub.service.dcexam.fetch-course';

export type CourseInfo = {
  id: number;
  name: string; //课程名称
  startTime: number; //开始时间
  endTime: number; //结束时间
  examId: number; //考试id
  properties: ExamConfig;
};

export type ApiQ = {
  courseId: number; //课程ID
};

export type ProgressApiQ = {
  fileId: number; //课件id
  progress: number; //进度 单位秒
  courseId: number; //课程ID
};

export type ServiceQ = {
  courseId: number;
  fileId?: number;
  progress?: number;
};

export type Course = {
  courseFileList: BackendCourseFile[];
  isFinished: boolean;
  courseInfo: CourseInfo;
  examPaperId?: number; //考卷ID
  testCount: number; // 考试次数
};

export type Lecture = {
  holdOnBlur?: boolean;
  allowSeekForward?: boolean;
  minSecondsAsCompleted?: number;
  studyTime?: number;
  isFinished?: boolean;
  name: string;
  order: number;
  id: number;
  fileType?: string;
  fileName?: string;
  filePath?: string;
  categoryCode: string;
  groupKey?: string;
  fileTime?: number;
};

export type StudyProgress = {
  fileId: number;
  studyTime: number;
  courseId: number;
  isFinished: boolean;
};

export type ApiR = Response<Course>;
export type ProgressApiR = Response<StudyProgress>;

export type ServiceR = Course;

export type ProgressServiceR = StudyProgress;
