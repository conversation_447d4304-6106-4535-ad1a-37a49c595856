/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-associated-users';
import type { ApiQ, ApiR } from './fetch-associated-users.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const {
      pageNum = 1,
      pageSize = 10,
      blockGuidList,
      userId,
      awardStartTime,
    } = JSON.parse(config.data);
    const startId = (pageNum - 1) * pageSize;
    let blockGuids = ['EC01'];
    if (blockGuidList) {
      blockGuids = blockGuidList;
    }
    let user = 3;
    if (userId) {
      user = userId;
    }

    let time = '2021-09-22T07:36:35.000+0000';
    if (awardStartTime) {
      time = awardStartTime;
    }
    return [
      200,
      {
        success: true,
        data: Array(pageSize)
          .fill(null)
          .map((_, idx) => ({
            id: startId + idx,
            accountName: 'Account Name ' + (startId + idx),
            userId: user,
            userName: user,
            creatorId: 1,
            creatorName: 'admin',
            awardTime: time,
            blockGuidList: blockGuids,
            roleList: ['admin'],
          })),
        total: 100,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
