/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { fetchAssociatedUsers as webService } from './fetch-associated-users.browser';
import { registerWebMocks } from './fetch-associated-users.mock';

// // registerWebMocks();

test('should resolve "associated users list"', async () => {
  const { data, error } = await webService({
    blockGuids: ['sxdtyg'],
    certId: 1,
    page: 1,
    pageSize: 10,
  });
  expect(data.data).toHaveLength(1);
  expect(error).toBe(undefined);
});
