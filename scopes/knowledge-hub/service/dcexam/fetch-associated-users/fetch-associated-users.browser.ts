/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-associated-users';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-associated-users.type';

/**
 * 知识中心 - 技能关联的角色列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/vdgm85)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchAssociatedUsers({
  page,
  issuedAtTimeRange,
  blockGuids,
  roleIds,
  ...rest
}: ServiceQ) {
  const apiQ: ApiQ = {
    pageNum: page,
    awardStartTime: issuedAtTimeRange ? issuedAtTimeRange[0] : null,
    awardEndTime: issuedAtTimeRange ? issuedAtTimeRange[1] : null,
    blockGuidList: blockGuids,
    roleList: roleIds,
    ...rest,
  };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
