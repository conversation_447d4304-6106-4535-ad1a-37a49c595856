/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './fetch-associated-users.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchAssociatedUsers(data: ServiceQ) {
  throw new Error("fetchAssociatedUsers's node version not implemented!");
}
