/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-10
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiR = ListResponse<BackendCertifiedStaffs[]>;

export type BackendCertifiedStaffs = {
  id: number;
  accountName: string;
  userId: number;
  userName: string;
  creatorId: number;
  creatorName: string;
  awardTime: string;
  blockGuidList: string[];
  roleList: number[] | string[];
};

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  certId: number;
  userId?: number;
  awardStartTime?: number | null;
  awardEndTime?: number | null;
  blockGuidList?: string[];
  roleList?: number[];
};

export type ServiceQ = {
  page: number;
  pageSize: number;
  certId: number;
  userId?: number;
  issuedAtTimeRange?: [number, number];
  blockGuids?: string[];
  roleIds?: number[];
};

export type ServiceR = {
  data: BackendCertifiedStaffs[];
  total: number;
};
