/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-27
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './add-exam-users.type';

const endpoint = '/dcexam/exam/addUser';

/**
 * @see [Doc](http://172.16.0.17:13000/project/15/interface/api/27051)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
