/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-27
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type UserConfig = {
  /**
   *  /**      * 模式      * 1 - 所有人      * 2 - 按角色      * 3 - 按用户      */

  mode: number;
  users?: number[] | null;
  roles?: number[] | null;
};

export type ApiArgs = {
  /**
   * 考试id
   */
  id: number;
  /**
   * 必考用户
   */
  defaultUserConfig?: UserConfig | null;
  /**
   * 选考用户
   */
  optionalUserConfig?: UserConfig | null;
};

export type ApiResponse = WriteResponse;
