/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-27
 *
 * @packageDocumentation
 */

import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './add-exam-users';
import type { ApiArgs,  } from './add-exam-users.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
* @returns
 */
export function addExamUsers(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
