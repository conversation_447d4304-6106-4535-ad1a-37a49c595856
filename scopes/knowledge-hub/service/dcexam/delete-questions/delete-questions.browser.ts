/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './delete-questions';
import type { ApiD, ServiceD, ServiceR } from './delete-questions.type';

/**
 * 知识中心-试题管理-删除试题
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/qßmhvk4)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deleteQuestions(questions: ServiceD) {
  const apiD: ApiD = {
    questionIds: questions.filter(q => q.questionType == '1').map(q => q.id),
    folderCodes: questions.filter(q => q.questionType == '0').map(q => q.id),
  };
  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiD);
}
