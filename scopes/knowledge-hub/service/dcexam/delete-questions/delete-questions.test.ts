/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { deleteQuestions as webService } from './delete-questions.browser';
import { registerWebMocks } from './delete-questions.mock';

// // registerWebMocks();

test('delete one or more questions ', async () => {
  const { data, error } = await webService([
    { id: 1, categoryCode: '100001', questionType: '0' },
    { id: 2, categoryCode: '100001', questionType: '1' },
  ]);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('delete one or more questions ', async () => {
  const { data, error } = await webService([{ id: 2, categoryCode: '100001', questionType: '1' }]);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('delete one or more questions ', async () => {
  const { data, error } = await webService([{ id: 1, categoryCode: '100001', questionType: '0' }]);

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
