/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-questions';
import type { ApiD, ApiR } from './delete-questions.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      questionIds: [2],
      folderCodes: [1],
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
  webMock
    .onPost(endpoint, {
      questionIds: [2],
      folderCodes: [],
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
  webMock
    .onPost(endpoint, {
      folderCodes: [1],
      questionIds: [],
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}

// webMock
//   .onPost(endpoint, {
//     questionIds: [2],
//     folderCodes: [1],
//   } as ApiD)
//   .reply<ApiR>(200, {
//     success: true,
//     errCode: null,
//     errMessage: null,
//   });
