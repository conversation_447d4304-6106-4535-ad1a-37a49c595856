/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export type ApiD = {
  questionIds: number[] | null; //试题id
  folderCodes: number[] | null; //分类id
};

export type ApiR = WriteResponse;

export type ServiceD = Pick<BackendQuestion, 'id' | 'categoryCode' | 'questionType'>[];

export type ServiceR = boolean;
