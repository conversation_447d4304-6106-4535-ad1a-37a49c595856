---
description: 'A fecthCoursewareVersionRecords HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询课件版本替换记录

## 使用

### Browser

```ts
import { fecthCoursewareVersionRecords } from '@manyun/knowledge-hub.service.dcexam.fecth-courseware-version-records';

const { error, data } = await fecthCoursewareVersionRecords({
  fileId: 1,
});
```

### Node

```ts
import { fecthCoursewareVersionRecords } from '@manyun/knowledge-hub.service.dcexam.fecth-courseware-version-records/dist/index.node';

const { data } = await fecthCoursewareVersionRecords({
  fileId: 1,
});
```
