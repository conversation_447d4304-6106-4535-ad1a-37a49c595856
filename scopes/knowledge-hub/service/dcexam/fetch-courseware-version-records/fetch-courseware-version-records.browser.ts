/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-courseware-version-records';
import type { SvcQuery, SvcRespData } from './fetch-courseware-version-records.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchCoursewareVersionRecords(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
