/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  fileId: number;
};

export type BackendVersionRecord = {
  id: number;
  fileName: string;
  filePath: string;
  fileSize: number;
  fileTime: number;
  fileType: string;
  operatorId: number;
  operatorName: string;
  gmtCreate: number;
  gmtModified: number;
};

export type VersionRecord = BackendVersionRecord;

export type SvcRespData = {
  data: VersionRecord[];
  total: number;
};

export type RequestRespData = {
  data: BackendVersionRecord[] | null;
  total: number;
} | null;

export type ApiQ = {
  fileId: number;
};

export type ApiR = ListResponse<BackendVersionRecord[] | null>;
