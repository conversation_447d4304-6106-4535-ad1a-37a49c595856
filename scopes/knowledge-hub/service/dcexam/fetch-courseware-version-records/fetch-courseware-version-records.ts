/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-courseware-version-records.type';

const endpoint = '/dcexam/course/file/version/query';

/**
 * 查询课件版本替换记录
 * @see [Doc](http://172.16.0.17:13000/project/15/interface/api/18960)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (query: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      fileId: query.fileId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
