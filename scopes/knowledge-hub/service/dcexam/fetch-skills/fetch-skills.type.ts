/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { SkillLimitUnit } from '@manyun/knowledge-hub.service.dcexam.mutate-skill';

export enum BackendSkillsLevel {
  HIGH = 'L1',
  MIDDLE = 'L2',
  LOW = 'L3',
}

export enum SkillsLevel {
  HIGH = 'L1',
  MIDDLE = 'L2',
  LOW = 'L3',
}

export type BackendSkills = {
  id?: number | string;
  categoryCode: string | number;
  name: string;
  level: SkillsLevel;
  gmtModified?: number;
  limitTime?: number;
  limitUnit?: SkillLimitUnit;
};

export type ApiQ =
  | {
      categoryCode?: string | number;
      name?: string;
      level?: 'L1' | 'L2' | 'L3';
    }
  | undefined;

export type ServiceQ =
  | {
      categoryCode?: string | number;
      name?: string;
      level?: 'L1' | 'L2' | 'L3';
    }
  | undefined;

export type ServiceR = {
  data: BackendSkills[];
};

export type ApiR = ListResponse<BackendSkills[]>;
