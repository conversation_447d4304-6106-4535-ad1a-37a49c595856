/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-skills';
import type { ApiR } from './fetch-skills.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { categoryCode, level, name } = JSON.parse(config.data);
    return [
      200,
      {
        success: true,
        data: Array(10)
          .fill(null)
          .map((_, idx) => ({
            id: idx,
            categoryCode: categoryCode || '123',
            name: name || 'name' + idx,
            level: level || 'L1',
          })),
        total: 0,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
