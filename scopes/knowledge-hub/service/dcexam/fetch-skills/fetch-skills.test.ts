/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
// import './fetch-skills.mock';
import { fetchSkills as webService } from './fetch-skills.browser';
import { registerWebMocks } from './fetch-skills.mock';

// import { SkillsLevel } from './fetch-skills.type';

// registerWebMocks();

test('should resolve "bar"', async () => {
  const { data, error } = await webService({ level: 'L3', categoryCode: 8 });

  expect(data.data).toHaveLength(10);
  expect(error).toBe(undefined);
});

test('blank', async () => {
  const { data, error } = await webService(undefined);

  expect(data.data).toHaveLength(10);
  expect(error).toBe(undefined);
});

test('{}', async () => {
  const { data, error } = await webService({});

  expect(data.data).toHaveLength(10);
  expect(error).toBe(undefined);
});
