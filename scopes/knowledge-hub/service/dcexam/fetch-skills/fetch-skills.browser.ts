/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-skills';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-skills.type';

/**
 * 知识中心 - 技能列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/aqs6bi)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchSkills(data: ServiceQ) {
  const apiQ: ApiQ = {
    categoryCode: data?.categoryCode,
    name: data?.name,
    level: data?.level,
  };
  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
