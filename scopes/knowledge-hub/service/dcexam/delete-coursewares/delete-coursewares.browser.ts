/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './delete-coursewares';
import type { ApiD, ServiceD, ServiceR } from './delete-coursewares.type';

/**
 * 知识中心-删除课件
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/vh9y76)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deleteCoursewares({ folderIds, fileIds }: ServiceD) {
  const apiD: ApiD = { folderIds, fileIds };

  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiD);
}
