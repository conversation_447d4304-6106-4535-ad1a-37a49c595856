/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteCoursewares as webService } from './delete-coursewares.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should delete success ', async () => {
  const { data, error } = await webService({ fileIds: [1], folderIds: [] });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should delete fail ', async () => {
  const { data, error } = await webService({ fileIds: [2], folderIds: [] });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
