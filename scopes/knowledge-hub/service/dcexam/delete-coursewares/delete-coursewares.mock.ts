/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-coursewares';
import type { ApiD, ApiR } from './delete-coursewares.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      asymmetricMatch({ fileIds }: ApiD) {
        return fileIds.includes(1);
      },
    })
    .reply<ApiR>(200, {
      success: false,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, {
      asymmetricMatch({ fileIds }: ApiD) {
        return fileIds.includes(2);
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
