/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-27
 *
 * @packageDocumentation
 */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './review-course';
import type { ApiArgs } from './review-course.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function ReviewCourse(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
