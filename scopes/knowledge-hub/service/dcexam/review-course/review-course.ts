/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-27
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './review-course.type';

const endpoint = '/dcexam/course/review';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/15/interface/api/27256)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
