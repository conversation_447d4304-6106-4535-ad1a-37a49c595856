/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-6-27
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './review-course';
import type { ApiArgs } from './review-course.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */

export function reviewCourse(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
