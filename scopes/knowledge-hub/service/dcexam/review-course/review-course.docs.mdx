---
description: 'A reviewCourse HTTP API service.'
labels: ['service', 'http']
---

课程复盘

## Usage

### Browser

```ts
import { reviewCourse } from '@manyun/knowledge-hub.service.dcexam.review-course';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { ReviewCourseService } from '@manyun/knowledge-hub.service.dcexam.review-course/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ReviewCourseService.from(nodeRequest);
```
