/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-18
 *
 * @packageDocumentation
 */

import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-course-student';
import type { ApiArgs, ApiResponse } from './fetch-course-student.type';

const executor = getExecutor(webRequest);

/**
 * @param args
* @returns
 */
export function fetchCourseStudent(args: ApiArgs): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
