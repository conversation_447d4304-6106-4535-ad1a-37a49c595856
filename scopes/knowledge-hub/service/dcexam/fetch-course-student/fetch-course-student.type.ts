/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-18
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 课程id
   */
  courseId?: number;
  scheduleId?: number;
  triggerTime?: string;
};

export type BackendCourseStudent = {
  userId: number;
  userName: string;
  gmtCreate?: string;
  courseType: string;
  testCount?: number;
  /**
   * 学习状态
   */
  studyStatus: string;
  /**
   * 角色列表
   */
  roleList?: string[] | null;
  /**
   * 开始时间
   */
  startLearnTime?: number | null;
  /**
   * 结束时间
   */
  endLearnTime?: number | null;
  /**
   * 完成学习课件
   */
  completeCourseFileList?: BackendMcUploadFile[] | null;
  /**
   * 未完成学习课件
   */
  unCompleteCourseFileList?: BackendMcUploadFile[] | null;
  /**
   * 开始答题时间
   */
  startAnswerTime?: number | null;
  /**
   * 结束答题时间
   */
  endAnswerTime?: number | null;
  /**
   * 是否通过
   */
  pass?: boolean | null;
};

export type ApiResponse = ListResponse<BackendCourseStudent[]>;
