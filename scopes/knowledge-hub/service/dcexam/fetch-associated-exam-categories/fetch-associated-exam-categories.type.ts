/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendAssociatedExamCategories = {
  id: number;
  categoryCode: string;
  creatorId: number;
  creatorName: string;
  gmtCreate: number;
};

export type ApiQ = ServiceQ;

export type ServiceQ = {
  certId: number;
};

export type ServiceR = {
  data: BackendAssociatedExamCategories[];
  total: number;
};

export type ApiR = ListResponse<BackendAssociatedExamCategories[]>;
