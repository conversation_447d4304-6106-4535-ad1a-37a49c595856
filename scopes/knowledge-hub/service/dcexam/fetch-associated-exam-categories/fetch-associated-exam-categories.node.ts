/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './fetch-associated-exam-categories.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchAssociatedExamCategories(data: ServiceQ) {
  throw new Error("fetchAssociatedExamCategories's node version not implemented!");
}
