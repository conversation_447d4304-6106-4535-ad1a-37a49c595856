/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchAssociatedExamCategories as webService } from './fetch-associated-exam-categories.browser';
import { registerWebMocks } from './fetch-associated-exam-categories.mock';

// // registerWebMocks();

test('should resolve "skill associated exam categories"', async () => {
  const { data, error } = await webService({ certId: 1 });

  expect(data.data).toHaveLength(1);
  expect(error).toBe(undefined);
});
