/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-associated-exam-categories';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-associated-exam-categories.type';

/**
 * 知识中心 - 技能关联的考试类型
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/ab6fd6)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchAssociatedExamCategories({ certId }: ServiceQ) {
  const apiQ: ApiQ = { certId };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
