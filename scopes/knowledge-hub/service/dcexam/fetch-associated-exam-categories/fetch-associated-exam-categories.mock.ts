/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-associated-exam-categories';
import type { ApiQ, ApiR } from './fetch-associated-exam-categories.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(200, {
    success: true,
    data: [{ id: 1, categoryCode: '1', creatorId: 1, creatorName: 'q', gmtCreate: 123 }],
    total: 0,
    errCode: null,
    errMessage: null,
  });
}
