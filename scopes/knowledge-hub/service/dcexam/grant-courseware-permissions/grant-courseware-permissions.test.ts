/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { grantCoursewarePermissions as webService } from './grant-courseware-permissions.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "grant success"', async () => {
  const { error } = await webService({
    fileIds: [2],
    userConfig: {
      roles: [],
      users: [],
    },
  });
  expect(error).toBe(undefined);
});

test('should resolve "grant fail"', async () => {
  const { error } = await webService({
    fileIds: [1],
    userConfig: {
      roles: [],
      users: [],
    },
  });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
