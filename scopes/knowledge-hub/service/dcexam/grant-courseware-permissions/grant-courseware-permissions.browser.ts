/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { UserConfig } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { autoCoursewareEndpoint } from './grant-courseware-permissions';
import type {
  ApiD,
  RequestRespData,
  ServiceD,
  ServiceR,
} from './grant-courseware-permissions.type';

/**
 * 知识中心 - 课件/目录 授权
 *
 * @see [课件/目录 授权](http://172.16.0.17:13000/project/15/interface/api/61)

 * @param data POST Reqeust Body
 * @returns
 */
export async function grantCoursewarePermissions({
  userConfig,
  fileIds,
  folderIds,
}: ServiceD): Promise<EnhancedAxiosResponse<ServiceR>> {
  const defaultUserConfig: UserConfig = {
    users: [],
    roles: [],
  };
  const optionalUserConfig: UserConfig = {
    users: [],
    roles: [],
  };
  userConfig.roles.forEach(role =>
    role.isDownload
      ? defaultUserConfig.roles.push(role.roleId)
      : optionalUserConfig.roles.push(role.roleId)
  );
  userConfig.users.forEach(user =>
    user.isDownload
      ? defaultUserConfig.users.push(user.userId)
      : optionalUserConfig.users.push(user.userId)
  );
  const apiD: ApiD = {
    fileIds,
    folderIds,
    defaultUserConfig,
    optionalUserConfig,
  };
  const { error, data, ...rest } = await webRequest.tryPost<RequestRespData, ApiD>(
    autoCoursewareEndpoint,
    apiD
  );
  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }
  return {
    error,
    data,
    ...rest,
  };
}
