/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ServiceD } from './grant-courseware-permissions.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function grantCoursewarePermissions(data: ServiceD) {
  throw new Error("grantCoursewarePermissions's node version not implemented!");
}
