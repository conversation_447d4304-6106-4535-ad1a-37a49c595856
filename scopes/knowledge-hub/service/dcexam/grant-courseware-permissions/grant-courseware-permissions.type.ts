/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { UserConfig } from '@manyun/knowledge-hub.service.dcexam.fetch-course';
import type { BackendCourseware } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';

export type User = {
  userId: number;
  userName?: string;
  loginName?: string;
  roleList?: string[];
  blockGuidList?: string[];
  isDownload?: boolean;
};

export type Role = {
  roleId: number;
  roleName?: string;
  roleCode?: string;
  isDownload?: boolean;
};

export type Group = {
  groupId: number;
  groupName?: string;
  groupCode?: string;
  isDownload?: boolean;
  resourceCodes?: string[] | null;
};

export type Department = {
  departmentId: string;
  departmentName?: string;
};

export type UserConfigEntity = {
  users: User[];
  roles: Role[];
  departments?: Department[];
};

export type ApiD = {
  fileIds?: number[];
  folderIds?: number[];
  defaultUserConfig: UserConfig;
  optionalUserConfig: UserConfig;
};

export type ServiceD = {
  userConfig: UserConfigEntity;
  fileIds?: number[];
  folderIds?: number[];
};

export type ServiceR = {
  data: BackendCourseware[];
  total: number;
};

export type RequestRespData = {
  data: BackendCourseware[];
  total: number;
} | null;

export type ApiR = ListResponse<BackendCourseware[] | null>;
