/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { BackendExamStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import { useRemoteMock } from '@manyun/service.request';

import { fetchExamCount as webService } from './fetch-exam-count.browser';
import { fetchExamCount as nodeService } from './fetch-exam-count.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ examViewStatusList: [BackendExamStatus.CANCEL] });

  expect(error).toBe(undefined);
  expect(data).toBe(1);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ examViewStatusList: [BackendExamStatus.CORRECT] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ examViewStatusList: [BackendExamStatus.CANCEL] });

  expect(error).toBe(undefined);
  expect(data).toBe(1);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ examViewStatusList: [BackendExamStatus.CORRECT] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
