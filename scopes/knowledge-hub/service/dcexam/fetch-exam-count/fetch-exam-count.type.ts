/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendExamStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';

export type SvcQuery = {
  idcTag?: string;
  examViewStatusList?: BackendExamStatus[];
};

export type SvcRespData = number;

export type RequestRespData = number | null;

export type ApiR = Response<number | null>;
