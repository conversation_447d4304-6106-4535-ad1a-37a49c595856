---
description: 'A fetchExamCount HTTP API service.'
labels: ['service', 'http']
---

## 概述

根据机房或考试状态查询相应考试数量

## 使用

### Browser

```ts
import { fetchExamCount } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-count';

const { error, data } = await fetchExamCount({ examViewStatusList: [BackendExamStatus.CANCEL] });
const { error, data } = await fetchExamCount({ examViewStatusList: [BackendExamStatus.CORRECT] });
```

### Node

```ts
import { fetchExamCount } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-count/dist/index.node';

const { data } = await fetchExamCount({ examViewStatusList: [BackendExamStatus.CANCEL] });
const { error, data } = await fetchExamCount({ examViewStatusList: [BackendExamStatus.CORRECT] });
```
