/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-exam-count';
import type { SvcQuery, SvcRespData } from './fetch-exam-count.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchExamCount(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
