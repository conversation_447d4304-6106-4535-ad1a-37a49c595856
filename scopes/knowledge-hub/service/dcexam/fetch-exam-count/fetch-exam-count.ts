/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-exam-count.type';

const endpoint = '/dcexam/exam/count';

/**
 * 查询考试数量
 * @see [Doc](http://172.16.0.17:13000/project/15/interface/api/19005)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, SvcQuery>(
      endpoint,
      params
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: 0,
      };
    }

    return { error, data, ...rest };
  };
}
