import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';

import { fetchExamPaper as webService } from './fetch-exam-paper.browser';

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import './fetch-exam-paper.mock';
import { ExamPaper } from './fetch-exam-paper.type';

test('toMatch exam paper detail', async () => {
  const { data, error } = await webService({ examPaperId: 1 });
  const expectObject: Omit<ExamPaper, 'autoMark'> = {
    id: 1,
    examId: 1,
    paperId: 1,
    state: BackendExamPaperStatus.GRADED,
    startTime: 1631775056806,
    endTime: 1631775056806,
    timeLimit: 60,
    totalScore: 100,
    questionsGroups: [
      {
        id: 'fake-section-id-1',
        name: '第一板块',
        questionSize: 1,
        questionScore: 2,
        userScore: 1,
        questionEntities: {
          1: {
            id: 1,
            questionOrder: 1,
            type: 3,
            categoryCode: '1001',
            difficulty: 1,
            title: '测试题目1',
            options: [
              //选项
              'A:测试选项1',
              'B:测试选项2',
              'C:测试选项3',
              'D:测试选项4',
            ],
            answers: [
              //答案
              'A',
              'B',
              'C',
            ],
            userAnswers: [
              //用户答案
              'A',
              'B',
            ],
            analysis: '这个题目明显选择 A B C',
            autoGrade: false,
            correct: false,
            grade: 2,
            userGrade: 1,
          },
        },
        questionIds: [1],
        visibleQuestionIds: [1],
        visibleErrorQuestionIds: [1],
      },
    ],
    userId: 1,
    userScore: 50,
    userRanking: 1,
    result: true,
    finishedQuestionSize: 1,
    questionSize: 1,
  };
  expect(data).toMatchObject(expectObject);
  expect(error).toBe(undefined);
});
