/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import shortid from 'shortid';

import { EnhancedAxiosResponse, webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-exam-paper';
import { BackendExamPaperQuestion, QuestionsGroup } from './fetch-exam-paper.type';
import type {
  ApiQ,
  BackendExamPaperDetail,
  ExamPaper,
  ServiceQ,
  ServiceR,
} from './fetch-exam-paper.type';

/**
 * 知识中心-考试管理-考卷详情.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/dad9ou)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExamPaper({
  examPaperId,
}: ServiceQ): Promise<EnhancedAxiosResponse<ExamPaper | null>> {
  const apiQ: ApiQ = { examPaperId };
  const examPaperResp = await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
  if (examPaperResp.error) {
    return { ...examPaperResp, data: null };
  }
  const examPaper = convertBackendExamPaperToExamPaper(examPaperResp.data);
  return { ...examPaperResp, data: examPaper };
}

/**
 * BackendExamPaperDetail convert to ExamPaper
 * @param examPaper BackendExamPaperDetail
 * @returns ExamPaper
 */
function convertBackendExamPaperToExamPaper(examPaper: BackendExamPaperDetail): ExamPaper {
  const questionsGroups: QuestionsGroup[] = [];
  let finishedQuestionSize: number = 0;
  let questionSize: number = 0;
  /** See http://chandao.manyun-local.com/zentao/bug-view-3999.html */
  let totalUserScore: number = 0;
  examPaper.paperSectionVOList?.forEach(paperSection => {
    const questionEntities: Record<number, BackendExamPaperQuestion> = {};
    let questionScore: number = 0;
    let userScore: number = 0;
    let questionFinshedSize: number = 0;
    const questionIds: number[] = [];
    const visibleQuestionIds: number[] = [];
    const visibleErrorQuestionIds: number[] = [];
    paperSection.questions.forEach(question => {
      if (question.autoGrade === false) {
        visibleQuestionIds.push(question.id);
      }
      if (question.correct === false) {
        visibleErrorQuestionIds.push(question.id);
      }
      questionIds.push(question.id);
      questionEntities[question.id] = question;
      questionScore += question.grade;
      userScore += question.userGrade;
      if (question.userAnswers && question.userAnswers.length > 0) questionFinshedSize += 1;
    });
    totalUserScore += userScore;
    questionsGroups.push({
      id: shortid(),
      name: paperSection.section,
      questionSize: paperSection.questions.length,
      questionScore,
      userScore,
      questionEntities,
      questionIds,
      visibleQuestionIds, //仅人工判题id
      visibleErrorQuestionIds, //仅错题id
    });
    questionSize += paperSection.questions.length;
    finishedQuestionSize += questionFinshedSize;
  });

  return {
    id: examPaper.id,
    examId: examPaper.examId,
    paperId: examPaper.paperId,
    state: examPaper.status,
    startTime: examPaper.startTime,
    endTime: examPaper.endTime,
    timeLimit: examPaper.examDuration,
    questionsGroups: questionsGroups,
    userId: examPaper.userId,
    totalScore: examPaper.totalGrade,
    userScore: Number(totalUserScore.toFixed(1)),
    userRanking: examPaper.order!, //考试排名
    result: examPaper.pass,
    finishedQuestionSize, //已完成试题数量
    questionSize, //试题总数
    autoMark: examPaper.autoGrade,
  };
}
