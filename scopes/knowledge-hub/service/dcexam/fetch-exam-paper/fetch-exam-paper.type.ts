/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendExamPaperStatus,
  BackendExamPaperUser,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export type BackendExamPaperQuestion = BackendSectionQuestion;

export type BackendExamPaperSectionVo = {
  section: string;
  questions: BackendExamPaperQuestion[];
};

export type BackendExamPaperDetail = BackendExamPaperUser & {
  examDuration: number; ////考试时长
  userId: number;
  userName: string;
  totalGrade: number; //总分
  paperSectionVOList: BackendExamPaperSectionVo[];
  submitUserId: number | null;
};

export type ExamPaper = {
  id: number | null;
  examId: number | null; //考试id
  paperId: number | null; //试卷id
  state: BackendExamPaperStatus; //答卷状态
  startTime: number | null;
  endTime: number | null;
  timeLimit: number; //答卷时长
  displayManuallyMarkingQuestionsOnly?: boolean; // 是否只查看需要人工判题
  displayErrorQuestionsOnly?: boolean; //是否仅显示错误题目
  questionsGroups: QuestionsGroup[];
  markingResultEntities?: {};
  markingQuestionIds?: []; //需要判题的 ID 集合，应拿到考卷信息后就生成
  userId: number | null;
  totalScore: number;
  /** 如果是 `null`，表示从未提交过判卷结果 */
  userScore: number | null;
  userRanking: number | null;
  result: boolean | null; //考试结果
  finishedQuestionSize: number; //用户已作答的试题总数
  questionSize: number; //试题总数
  /** 是否系统自动判卷 */
  autoMark: boolean;
};

//试卷板块
export type QuestionsGroup = {
  /** 前端自己生成的 ID */
  id: string;
  name: string;
  questionSize: number; //总数量
  questionScore: number; //总分
  userScore: number; //考生得分
  questionEntities: Record<number, BackendExamPaperQuestion>;
  questionIds: number[];
  visibleQuestionIds: number[]; //筛选后的id 集合  如： 仅看人工判题
  visibleErrorQuestionIds: number[]; //仅错题 id集合
};

export type ApiQ = {
  examPaperId: number;
};

export type ApiR = Response<BackendExamPaperDetail>;

export type ServiceQ = {
  examPaperId: number;
};

export type ServiceR = BackendExamPaperDetail;
