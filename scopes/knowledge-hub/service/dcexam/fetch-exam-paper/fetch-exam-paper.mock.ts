/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { BackendSectionQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { BackendQuestionType } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-exam-paper';
import type { ApiQ, ApiR } from './fetch-exam-paper.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    const { examPaperId } = JSON.parse(config.data) as ApiQ;

    return [
      200,
      {
        success: true,
        data: {
          id: examPaperId, //考卷id
          examId: 1, //考试id
          userId: 777, //用户id
          userName: 'Jerry', //用户姓名
          paperId: 1, //试卷id
          status: BackendExamPaperStatus.PROCESS, //考卷状态
          startTime: 1631775056806, //开始时间
          endTime: 1631775056806, //结束时间
          totalGrade: 100, //总分
          examDuration: 60,
          grade: 50, //分数
          order: 1, //排名
          pass: true, //是否通过
          correctUserId: 1, //判卷人id
          correctUserName: '张三', //判卷人姓名
          paperSectionVOList: [
            {
              section: '板块一',
              questions: Array(10)
                .fill(null)
                .map((_, idx) => generateMockQuestion(idx + 1, { index: idx + 1 })),
            },
          ],
          submitUserId: null,
        },
        errCode: null,
        errMessage: null,
      },
    ];
  });
}

const questionTypeTitleMapper: Record<BackendQuestionType, string> = {
  [BackendQuestionType.SINGLE_CHOICE]: '这是一道单选题',
  [BackendQuestionType.MULTIPLE_CHOICE]: '这是一道多选题',
  [BackendQuestionType.TRUE_OR_FALSE]: '这是一道判断题',
  [BackendQuestionType.SHORT_ANSWER]: '这是一道填空题',
  [BackendQuestionType.ESSAY]: '这是一道简答题',
};

function generateMockQuestion(id: number, { index }: any): BackendSectionQuestion {
  const questionTypes = [
    BackendQuestionType.SINGLE_CHOICE,
    BackendQuestionType.MULTIPLE_CHOICE,
    BackendQuestionType.TRUE_OR_FALSE,
    BackendQuestionType.SHORT_ANSWER,
    BackendQuestionType.ESSAY,
  ];
  const randomQuestionTypeIdx = Math.floor(Math.random() * questionTypes.length);
  const questionType = questionTypes[randomQuestionTypeIdx];

  return {
    id,
    questionOrder: index,
    type: questionType,
    categoryCode: 9,
    difficulty: 1,
    title: questionTypeTitleMapper[questionType] + id,
    options: [BackendQuestionType.SINGLE_CHOICE, BackendQuestionType.MULTIPLE_CHOICE].includes(
      questionType
    )
      ? ['测试选项 1', '测试选项 2', '测试选项 3', '测试选项 4']
      : [],
    answers: [BackendQuestionType.SINGLE_CHOICE, BackendQuestionType.MULTIPLE_CHOICE].includes(
      questionType
    )
      ? ['测试选项 1']
      : [],
    userAnswers: ['测试选项 1'],
    analysis: '这是一个答案解析',
    autoGrade: true,
    correct: randomQuestionTypeIdx > questionTypes.length / 2 ? null : false,
    grade: 10,
    userGrade: 0,
  };
}
