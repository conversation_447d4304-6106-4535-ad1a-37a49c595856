/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { BackendQuestionDifficulty } from '.';
import { endpoint } from './fetch-questions';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-questions.type';

/**
 * 知识中心-试题管理-试题列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/noqxrn)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchQuestions({
  categoryCode,
  difficulty,
  title,
  type,
  needTotalCount,
  questionType,
  timeRange,
  page,
  pageSize,
  autoGrade,
}: ServiceQ) {
  const apiQ: ApiQ = {
    autoGrade,
    categoryCode,
    difficulty,
    type,
    title,
    needTotalCount,
    questionType,
    createStartTime: timeRange ? timeRange[0] : undefined,
    createEndTime: timeRange ? timeRange[1] : undefined,
    pageNum: page,
    pageSize: pageSize,
  };
  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
