---
description: '知识中心-试题管理-试题分页数据'
labels: ['service', 'http', 'fetch-questions']
---

## fetchQuestions

### Node Usage

```ts
import { fetchQuestions } from '@manyun/knowledge-hub.service.dcexam.fetch-questions/dist/index.node';

const { data, error } = await fetchQuestions();
```

### Browser Usage

#### 请求试题分页数据

```ts
import { fetchQuestions } from '@manyun/knowledge-hub.service.dcexam.fetch-questions/dist/index.browser';

const { data, error } = await fetchQuestions({
  page: 1,
  pageSize: 10,
});
```
