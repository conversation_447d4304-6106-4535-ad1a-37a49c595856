/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-questions';
import type { ApiQ, ApiR } from './fetch-questions.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, { pageNum: 1, pageSize: 3 })
    .reply<ApiR>(200, {
      success: true,
      data: baseData.filter((_, key) => key > 3),
      total: 3,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, { pageNum: 1, pageSize: 10, questionType: 1 })
    .reply<ApiR>(200, {
      success: true,
      data: baseData.filter(d => d.questionType !== '0'),
      total: 100,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, { pageNum: 1, pageSize: 10 })
    .reply<ApiR>(200, {
      success: true,
      data: baseData,
      total: 100,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, { pageNum: 1, pageSize: 10, type: 2 })
    .reply<ApiR>(200, {
      success: true,
      data: baseData.filter(question => question.type == 2),
      total: 100,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, { pageNum: 1, pageSize: 10, type: 1, questionType: 1 })
    .reply<ApiR>(200, {
      success: true,
      data: baseData.filter(d => d.questionType !== '0'),
      total: 100,
      errCode: null,
      errMessage: null,
    });
}

const baseData = [
  {
    id: 2,
    type: 1,
    categoryCode: '1001',
    difficulty: 1,
    questionType: '0',
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },
  {
    id: 1,
    type: 2,
    questionType: '1',
    categoryCode: '1001',
    difficulty: 1,
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },

  {
    id: 30,
    type: 3,
    categoryCode: '1001',
    questionType: '1',
    difficulty: 1,
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },
  {
    id: 3,
    type: 4,
    categoryCode: '1001',
    questionType: '1',
    difficulty: 1,
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },
  {
    id: 4,
    type: 5,
    categoryCode: '1001',
    questionType: '1',
    difficulty: 1,
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },
  {
    id: 5,
    type: 1,
    categoryCode: '1001',
    questionType: '1',
    difficulty: 1,
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },
  {
    id: 6,
    type: 1,
    categoryCode: '1001',
    questionType: '1',
    difficulty: 1,
    title: '测试试题',
    options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
    answers: ['A', 'B', 'C'],
    analysis: '这个题目明显选择 A B C',
    autoGrade: false,
    gmtModified: 123213123,
    modifierName: '张三',
    modifierId: 1,
  },
];
