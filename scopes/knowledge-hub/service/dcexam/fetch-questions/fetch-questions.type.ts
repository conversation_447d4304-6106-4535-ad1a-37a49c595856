/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export enum BackendQuestionDifficulty {
  EASIER_THAN_EASY = 1,
  EASY = 2,
  NORMAL = 3,
  HARD = 4,
  HARDER_THAN_HARD = 5,
}

export const QuestionDifficultyTextMapper = {
  [BackendQuestionDifficulty.EASIER_THAN_EASY]: '易',
  [BackendQuestionDifficulty.EASY]: '偏易',
  [BackendQuestionDifficulty.NORMAL]: '适中',
  [BackendQuestionDifficulty.HARD]: '难',
  [BackendQuestionDifficulty.HARDER_THAN_HARD]: '偏难',
};

export enum BackendQuestionType {
  SINGLE_CHOICE = 1,
  MULTIPLE_CHOICE = 2,
  TRUE_OR_FALSE = 3,
  SHORT_ANSWER = 4,
  ESSAY = 5,
}

export const QuestionTypeTextMapper = {
  [BackendQuestionType.SINGLE_CHOICE]: '单选题',
  [BackendQuestionType.MULTIPLE_CHOICE]: '多选题',
  [BackendQuestionType.TRUE_OR_FALSE]: '判断题',
  [BackendQuestionType.SHORT_ANSWER]: '填空题',
  [BackendQuestionType.ESSAY]: '问答题',
};

/** 题库：试题 */
export type BackendQuestion = {
  id: number;
  title: string;
  type: BackendQuestionType;
  categoryCode: string | number;
  questionType?: string; //题目类型：0（文件夹），1（试题）
  difficulty: number;
  options: string[];
  answers: string[];
  analysis: string;
  autoGrade: boolean;
  gmtModified?: number; //修改时间
  modifierName?: string; //修改人
  modifierId?: number;
  examCount: number;
  totalCount: number; //题目在考试中出现的总数
  correctCount: number; //题目正确的数量
};

export type ApiQ = {
  autoGrade?: boolean; //是否为系统判题
  categoryCode?: string | number;
  title?: string; //标题
  difficulty?: BackendQuestionDifficulty; //难度
  type?: BackendQuestionType; //	题型
  needTotalCount?: boolean;
  questionType?: number; //试题或文件夹
  createStartTime?: number; //创建时间开始时间
  createEndTime?: number; //创建时间结束时间
  pageNum: number; //页数
  pageSize: number; //每页大小
};

export type ApiR = ListResponse<BackendQuestion[]>;

export type ServiceQ = {
  autoGrade?: boolean; //是否为系统判题
  categoryCode?: string | number; //试题分类
  difficulty?: BackendQuestionDifficulty; //试题难度
  questionType?: number; //试题文件夹还是试题
  timeRange?: [number, number] | null; // 开始时间 - 结束时间
  title?: string; //试题内容
  type?: BackendQuestionType; //试题类型
  needTotalCount?: boolean; // 是否需要返回总数
  page: number; //页数
  pageSize: number; //页码
};

export type ServiceR = {
  data: BackendQuestion[];
  total: number;
};
