/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { fetchQuestions as webService } from './fetch-questions.browser';
import { registerWebMocks } from './fetch-questions.mock';

// registerWebMocks();
test('should request questions ', async () => {
  const { data, error } = await webService({ page: 1, pageSize: 3 });

  expect(data.data).toHaveLength(3);
  expect(error).toBe(undefined);
});

test('should request questions ', async () => {
  const { data, error } = await webService({ page: 1, pageSize: 10, questionType: 1 });
  expect(data.data).toHaveLength(6);
});
