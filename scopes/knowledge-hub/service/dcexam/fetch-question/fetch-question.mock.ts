/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { questionDetailEndpoint, questionsDetailEndpoint } from './fetch-question';
import type { ApiQ, ApiR, BatchApiQ, BatchApiR } from './fetch-question.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(questionDetailEndpoint, {
      id: 1,
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        ...baseQuestion,
        id: 1,
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(questionDetailEndpoint, {
      id: 2,
    } as ApiQ)
    .reply<ApiR>(200, {
      success: true,
      data: baseQuestion,
      errCode: null,
      errMessage: null,
    })
    .onPost(questionsDetailEndpoint, {
      idList: [1, 2],
    } as BatchApiQ)
    .reply<BatchApiR>(200, {
      success: true,
      data: new Array(2).fill('').map((_, index) => ({ ...baseQuestion, id: index })),
      errCode: null,
      errMessage: null,
    });
}

const baseQuestion = {
  id: 2, //试题id
  type: 1,
  categoryCode: '1001', //试题分类
  difficulty: 1, //难度
  title: '测试试题', //题目标题
  options: [
    //选项
    'A:测试选项1',
    'B:测试选项2',
    'C:测试选项3',
    'D:测试选项4',
  ],
  answers: [
    //答案
    'A:测试选项1',
    'B:测试选项2',
    'C:测试选项3',
  ],
  analysis: '这个题目明显选择 A B C', //分析
  autoGrade: true, //是否自动判分
};
