/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export type ApiQ = {
  id: number;
};

export type BatchApiQ = {
  idList: number[];
};

export type ApiR = Response<BackendQuestion>;

export type BatchApiR = Response<BackendQuestion[]>;

export type ServiceQ = {
  questionId: number | number[];
};

export type ServiceR<T> = T;
