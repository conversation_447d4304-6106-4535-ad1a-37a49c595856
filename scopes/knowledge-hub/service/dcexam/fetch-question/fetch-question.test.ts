/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchQuestion as webService } from './fetch-question.browser';
import { registerWebMocks } from './fetch-question.mock';

// registerWebMocks();

test('get a question by id', async () => {
  const { data, error } = await webService({ questionId: 1 });
  expect(data).toHaveProperty('id', 1);
  // expect(data).toHaveProperty('analysis', '这个题目明显选择 A B C');
  expect(error).toBe(undefined);
});

test('get  question by ids', async () => {
  const { data, error } = await webService({ questionId: [1, 2] });
  expect(data).toHaveLength(2);
  expect(error).toBe(undefined);
});
