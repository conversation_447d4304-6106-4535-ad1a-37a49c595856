---
description: '知识中心-试题管理-试题详情'
labels: ['service', 'http', 'fetch-question']
---

## fetchQuestion

### Node Usage

```ts
import { fetchQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-question/dist/index.node';

const { data, error } = await fetchQuestion();
```

### Browser Usage

#### 获取试题详情

```ts
import { fetchQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-question/dist/index.browser';

const { data, error } = await fetchQuestion({
  questionId: 1,
});
```
