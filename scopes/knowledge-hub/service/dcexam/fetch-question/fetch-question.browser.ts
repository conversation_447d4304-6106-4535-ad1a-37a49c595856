/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { webRequest } from '@manyun/service.request';

import { questionDetailEndpoint, questionsDetailEndpoint } from './fetch-question';
import type { ApiQ, BatchApiQ, ServiceQ, ServiceR } from './fetch-question.type';

/**
 * 知识中心-试题管理-试题详情 - 包含(单个、多个)
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/bxsbdv)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchQuestion({ questionId }: ServiceQ) {
  if (Array.isArray(questionId)) {
    const apiQ: BatchApiQ = { idList: questionId };

    const { data, error, ...rest } = await webRequest.tryPost<
      ServiceR<{ data: BackendQuestion[]; total: number }>,
      BatchApiQ
    >(questionsDetailEndpoint, apiQ);
    return { data: data && data.data ? data.data : [], error, ...rest };
  }

  if (typeof questionId == 'number') {
    const apiQ: ApiQ = { id: questionId };
    return await webRequest.tryPost<ServiceR<BackendQuestion>, ApiQ>(questionDetailEndpoint, apiQ);
  }

  throw new Error(`fetchQuestion api not supported!`);
}
