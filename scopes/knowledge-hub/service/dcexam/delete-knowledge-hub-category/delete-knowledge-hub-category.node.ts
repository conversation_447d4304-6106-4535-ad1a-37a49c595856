/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { ApiD, ServiceD, ServiceR } from './delete-knowledge-hub-category.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deleteKnowledgeHubCategory(data: ServiceD) {
  throw new Error("deleteKnowledgeHubCategory's node version not implemented!");
}
