/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './delete-knowledge-hub-category';
import type { ApiD, ServiceD, ServiceR } from './delete-knowledge-hub-category.type';

/**
 * 知识中心-删除分类
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/hdrill)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deleteKnowledgeHubCategory({ id }: ServiceD) {
  const apiQ: ApiD = { id };

  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiQ);
}
