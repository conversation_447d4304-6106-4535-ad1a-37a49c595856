/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-knowledge-hub-category';
import type { ApiD, ApiR } from './delete-knowledge-hub-category.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      asymmetricMatch({ id }: ApiD) {
        return id === 2;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
