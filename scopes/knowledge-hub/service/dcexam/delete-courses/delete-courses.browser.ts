/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './delete-courses';
import type { ApiD, ServiceD, ServiceR } from './delete-courses.type';

/**
 * 知识中心-删除课程
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/odn9qx)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deleteCourses({ selectedIds }: ServiceD) {
  const apiD: ApiD = { courseIds: selectedIds };

  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiD);
}
