/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-courses';
import type { ApiD, ApiR } from './delete-courses.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      asymmetricMatch({ courseIds }: ApiD) {
        return courseIds.includes(1);
      },
    })
    .reply<ApiR>(200, {
      success: false,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, {
      asymmetricMatch({ courseIds }: ApiD) {
        return courseIds.includes(2);
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
