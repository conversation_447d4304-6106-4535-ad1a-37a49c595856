/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteCourses as webService } from './delete-courses.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "delete courses success"', async () => {
  const { data, error } = await webService({ selectedIds: [1] });

  expect(data).toBe<Boolean>(true);
  expect(error).toBe(undefined);
});

test('should resolve "delete courses fail"', async () => {
  const { data, error } = await webService({ selectedIds: [2] });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
