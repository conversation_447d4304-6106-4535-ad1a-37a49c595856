/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import { getMock } from '@manyun/service.request';

import { examPaperInfoEndPoint, examRankEndPoint } from './fetch-exam-result';
import type { ApiQ } from './fetch-exam-result.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(new RegExp(`${examPaperInfoEndPoint}|${examRankEndPoint}`), { examPaperId: 1 })
    .reply<ApiQ>((config): any => {
      const { examPaperId, examId } = JSON.parse(config.data);
      return [
        200,
        {
          success: true,
          data: {
            id: 1, //考卷id
            examId: 1, //考试id
            userId: 1, //用户id
            userName: 'lallaa', //用户姓名
            paperId: 1, //试卷id
            status: BackendExamPaperStatus.GRADED, //考卷状态
            startTime: 1631775056806, //开始时间
            endTime: 1631775056806, //结束时间
            totalGrade: 100, //总分
            grade: 50, //分数
            pass: true, //是否通过
            correctUserId: 1, //判卷人id
            correctUserName: '张三', //判卷人姓名
          },
          errCode: null,
          errMessage: null,
        },
      ];
    })
    .onPost(new RegExp(`${examPaperInfoEndPoint}|${examRankEndPoint}`), { examPaperId: 2 })
    .reply<ApiQ>((config): any => {
      const { examPaperId, examId } = JSON.parse(config.data);
      return [
        200,
        {
          success: true,
          data: {
            id: 1, //考卷id
            examId: 1, //考试id
            userId: 1, //用户id
            userName: 'lallaa', //用户姓名
            paperId: 1, //试卷id
            status: BackendExamPaperStatus.SUBMIT, //考卷状态
            startTime: 1631775056806, //开始时间
            endTime: 1631775056806, //结束时间
            totalGrade: 100, //总分
            grade: 50, //分数
            pass: true, //是否通过
            correctUserId: 1, //判卷人id
            correctUserName: '张三', //判卷人姓名
          },
          errCode: null,
          errMessage: null,
        },
      ];
    })
    .onPost(new RegExp(`${examPaperInfoEndPoint}|${examRankEndPoint}`), { examPaperId: 3 })
    .reply<ApiQ>((config): any => {
      const { examPaperId, examId } = JSON.parse(config.data);
      return [
        200,
        {
          success: true,
          data: {
            id: 1, //考卷id
            examId: 1, //考试id
            userId: 1, //用户id
            userName: 'lallaa', //用户姓名
            paperId: 1, //试卷id
            status: BackendExamPaperStatus.GRADED, //考卷状态
            startTime: 1631775056806, //开始时间
            endTime: 1631775056806, //结束时间
            totalGrade: 100, //总分
            grade: 20, //分数
            pass: false, //是否通过
            correctUserId: 1, //判卷人id
            correctUserName: '张三', //判卷人姓名
          },
          errCode: null,
          errMessage: null,
        },
      ];
    })
    .onPost(new RegExp(`${examPaperInfoEndPoint}|${examRankEndPoint}`), { examId: 1 })
    .reply<ApiQ>((config): any => {
      const { examPaperId, examId } = JSON.parse(config.data);
      return [
        200,
        {
          success: true,
          data: {
            examRanks: [
              //考试排名
              {
                rankIndex: 1, //名次
                userId: 1, //用户id
                userName: 'admin', //用户姓名
                idcTagList: [
                  //机房列表
                  'EC01',
                ],
                roleNameList: [
                  //角色名列表
                  'ADMIN',
                ],
                grade: 60, //分数
              },
              {
                rankIndex: 2, //名次
                userId: 10, //用户id
                userName: 'admin', //用户姓名
                idcTagList: [
                  //机房列表
                  'EC01',
                ],
                roleNameList: [
                  //角色名列表
                  'ADMIN',
                ],
                grade: 60, //分数
              },
              {
                rankIndex: 3, //名次
                userId: 11, //用户id
                userName: 'admin', //用户姓名
                idcTagList: [
                  //机房列表
                  'EC01',
                ],
                roleNameList: [
                  //角色名列表
                  'ADMIN',
                ],
                grade: 60, //分数
              },
            ],
            userRank: {
              //当前用户名次
              rankIndex: 6,
              userId: 4,
              userName: 'admin',
              idcTagList: ['EC01'],
              roleNameList: ['ADMIN'],
              grade: 60,
              totalGrade: 100,
            },
          },
          errCode: null,
          errMessage: null,
        },
      ];
    })
    .onPost(new RegExp(`${examPaperInfoEndPoint}|${examRankEndPoint}`), { examId: 2 })
    .reply<ApiQ>((config): any => {
      const { examPaperId, examId } = JSON.parse(config.data);
      return [
        200,
        {
          success: true,
          data: {
            examRanks: [
              //考试排名
              {
                rankIndex: 1, //名次
                userId: 1, //用户id
                userName: 'admin', //用户姓名
                idcTagList: [
                  //机房列表
                  'EC01',
                ],
                roleNameList: [
                  //角色名列表
                  'ADMIN',
                ],
                grade: 60, //分数
              },
              {
                rankIndex: 2, //名次
                userId: 10, //用户id
                userName: 'admin', //用户姓名
                idcTagList: [
                  //机房列表
                  'EC01',
                ],
                roleNameList: [
                  //角色名列表
                  'ADMIN',
                ],
                grade: 60, //分数
              },
              {
                rankIndex: 3, //名次
                userId: 11, //用户id
                userName: 'admin', //用户姓名
                idcTagList: [
                  //机房列表
                  'EC01',
                ],
                roleNameList: [
                  //角色名列表
                  'ADMIN',
                ],
                grade: 60, //分数
              },
            ],
            userRank: {
              //当前用户名次
              rankIndex: 6,
              userId: 4,
              userName: 'admin',
              idcTagList: ['EC01'],
              roleNameList: ['ADMIN'],
              grade: 60,
              totalGrade: 100,
            },
          },
          errCode: null,
          errMessage: null,
        },
      ];
    });
}
