---
description: '知识中心-考试管理-考试结果(考试结果、考卷排行)'
labels: ['service', 'http', 'fetch-exam-result']
---

## fetchExamResult

### Node Usage

```ts
import { fetchExamResult } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-result/dist/index.node';

const { data, error } = await fetchExamResult();
```

### Browser Usage

#### 请求考卷结果

```ts
import { fetchExamResult } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-result/dist/index.browser';

const { data, error } = await fetchExamResult({
  examPaperId: 1,
});
```

#### 请求考试结果排行榜

```ts
import { fetchExamResult } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-result/dist/index.browser';

const { data, error } = await fetchExamResult({
  examId: 1,
});
```
