import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';

import { fetchExamResult as webService } from './fetch-exam-result.browser';

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
// import './fetch-exam-result.mock';
import { registerWebMocks } from './fetch-exam-result.mock';
import { BackendRank } from './fetch-exam-result.type';

// // registerWebMocks();

test('toMatch exam paper result ', async () => {
  const { data, error } = await webService({ examPaperId: 1 });
  const examResult = {
    id: 1, // 考卷id
    examId: 1,
    userId: 1,
    userName: 'lallaa',
    paperId: 1,
    status: BackendExamPaperStatus.GRADED,
    startTime: 1631775056806,
    endTime: 1631775056806,
    totalGrade: 100,
    grade: 50,
    pass: true,
    correctUserId: 1,
    correctUserName: '张三',
  };
  expect(data).toMatchObject(examResult);
  expect(error).toBe(undefined);
});

test('toMatchObject is called for each elements by request exam ranks ', async () => {
  const { data, error } = await webService({ examId: 1 });
  const ranks = data as BackendRank[];
  const expectRanks = [
    {
      rankIndex: 1,
      userId: 1,
      userName: 'admin',
      idcTagList: ['EC01'],
      roleNameList: ['ADMIN'],
      grade: 60,
    },
    {
      rankIndex: 2,
      userId: 10,
      userName: 'admin',
      idcTagList: ['EC01'],
      roleNameList: ['ADMIN'],
      grade: 60,
    },
    {
      rankIndex: 3,
      userId: 11,
      userName: 'admin',
      idcTagList: ['EC01'],
      roleNameList: ['ADMIN'],
      grade: 60,
    },
    {
      rankIndex: 6,
      userId: 4,
      userName: 'admin',
      idcTagList: ['EC01'],
      roleNameList: ['ADMIN'],
      grade: 60,
      totalGrade: 100,
    },
  ];
  expect(ranks).toHaveLength(4);
  expect(ranks).toMatchObject(expectRanks);
  expect(error).toBe(undefined);
});
