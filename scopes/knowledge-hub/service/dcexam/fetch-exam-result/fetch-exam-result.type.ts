/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendExamRank = {
  examRanks: BackendRank[];
  userRank: BackendRank | null;
};

export type BackendRank = {
  rankIndex: number;
  userId: number;
  userName: string;
  resourceList: any[];
  roleInfoList: any[];
  grade: number;
  totalGrade?: number;
};

export type ApiQ = {
  examPaperId: number; //考卷id
  examId: number; //考试id
};

export type ApiR<T> = Response<T>;

export type ServiceQ = {
  examPaperId?: number; //考卷id
  needRank?: boolean; //是否返回排行结果
  examId?: number; //考试id
};

export type ServiceR<T> = T;
