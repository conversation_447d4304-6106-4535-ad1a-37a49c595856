import {
  BackendExamPaperStatus,
  BackendExamPaperUser,
  BackendMyExam,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { examPaperInfoEndPoint, examRankEndPoint } from './fetch-exam-result';
import type {
  ApiQ,
  BackendExamRank,
  BackendRank,
  ServiceQ,
  ServiceR,
} from './fetch-exam-result.type';

/**
 * 知识中心-考试管理-考试结果(考试结果、考卷排行).
 *
 * @see [Doc](https://manyun.yuque.com/fet/wir2zk/nzvoem#QCVKk、https://manyun.yuque.com/ewe5b3/zcferb/liywk8)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExamResult({ examId, examPaperId, needRank = false }: ServiceQ) {
  if (examPaperId && !examId) {
    const { data, error, ...rest } = await webRequest.tryPost<
      ServiceR<BackendMyExam>,
      Pick<ApiQ, 'examPaperId'>
    >(examPaperInfoEndPoint, { examPaperId });
    return { data, error, ...rest };
  } else if (examId && !examPaperId) {
    const { data, error, ...rest } = await webRequest.tryPost<
      ServiceR<BackendExamRank>,
      Pick<ApiQ, 'examId'>
    >(examRankEndPoint, { examId });
    if (!error) {
      const mergeExamRanks = data.examRanks || []; // 合并ExamRank
      if (data.userRank !== null) {
        if (!mergeExamRanks.find(rank => rank.userId === (data.userRank as BackendRank).userId)) {
          mergeExamRanks.push(data.userRank);
        }
      }

      return { data: mergeExamRanks, error, ...rest };
    }
    return { data: [], error, ...rest };
  } else {
    throw new Error(`fetchExamResult api is not supported!`);
  }
}
