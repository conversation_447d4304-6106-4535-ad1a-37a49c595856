/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './associate-exam-categories';
import type { ApiR } from './associate-exam-categories.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { examCodeList } = JSON.parse(config.data);
    if (examCodeList && Array.isArray(examCodeList) && examCodeList.length) {
      return [
        200,
        {
          success: true,
          errCode: null,
          errMessage: null,
        },
      ];
    }
    return [
      200,
      {
        success: false,
        errCode: null,
        errMessage: 'examCodeList Should not be empty！',
      },
    ];
  });
}
