/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './associate-exam-categories';
import type { ApiQ, ServiceQ, ServiceR } from './associate-exam-categories.type';

/**
 * 知识中心 - 技能 关联考试
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/pt26p5)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function associateExamCategories({ certId, examCodeList }: ServiceQ) {
  const apiQ: ApiQ = { certId, examCodeList };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
