/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './associate-exam-categories.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function associateExamCategories(data: ServiceQ) {
  throw new Error("associateExamCategories's node version not implemented!");
}
