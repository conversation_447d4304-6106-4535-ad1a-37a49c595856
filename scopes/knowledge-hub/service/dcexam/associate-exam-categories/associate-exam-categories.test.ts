/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { associateExamCategories as webService } from './associate-exam-categories.browser';
import { registerWebMocks } from './associate-exam-categories.mock';

registerWebMocks(getMock('web'));

test('should resolve "associate exam"', async () => {
  const { data, error } = await webService({ certId: 1, examCodeList: ['8'] });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
