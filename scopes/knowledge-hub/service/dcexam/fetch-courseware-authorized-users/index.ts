/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchCoursewareAuthorizedUsers as fetchCoursewareAuthorizedUsersWeb } from './fetch-courseware-authorized-users.browser';
import { fetchCoursewareAuthorizedUsers as fetchCoursewareAuthorizedUsersNode } from './fetch-courseware-authorized-users.node';

export { fetchCoursewareAuthorizedUsersWeb, fetchCoursewareAuthorizedUsersNode };
export { registerWebMocks } from './fetch-courseware-authorized-users.mock';
export * from './fetch-courseware-authorized-users.type';
