/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCoursewareAuthorizedUsers as webService } from './fetch-courseware-authorized-users.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "fetch category auth success"', async () => {
  const { data, error } = await webService({ categoryCode: 1 });

  expect(data.roles).toHaveLength(1);
  expect(error).toBe(undefined);
});

test('should resolve "fetch category auth fail"', async () => {
  const { data, error } = await webService({ categoryCode: 2 });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "fetch courseware auth success"', async () => {
  const { data, error } = await webService({ fileId: 2 });

  expect(data.roles).toHaveLength(2);
  expect(data.users).toHaveLength(1);
  expect(error).toBe(undefined);
});

test('should resolve "fetch courseware auth fail"', async () => {
  const { data, error } = await webService({ fileId: 1 });

  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
