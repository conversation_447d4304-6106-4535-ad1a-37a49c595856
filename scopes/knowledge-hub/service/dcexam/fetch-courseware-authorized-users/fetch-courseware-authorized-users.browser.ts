/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import {
  fetchAuthCategoryEndpoint,
  fetchAuthCoursewareEndpoint,
} from './fetch-courseware-authorized-users';
import type {
  FileApiQ,
  FloderApiQ,
  ServiceQ,
  ServiceR,
} from './fetch-courseware-authorized-users.type';

/**
 * 知识中心 - 查询 类型/课件 授权
 *
 * @see [Doc1](https://manyun.yuque.com/ewe5b3/zcferb/yggu3q)
 * @see [Doc2](https://manyun.yuque.com/ewe5b3/zcferb/rihxy2)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchCoursewareAuthorizedUsers({ categoryCode, fileId }: ServiceQ) {
  if (fileId) {
    const apiQ: FileApiQ = {
      fileId,
    };
    return await webRequest.tryPost<ServiceR, FileApiQ>(fetchAuthCoursewareEndpoint, apiQ);
  } else if (categoryCode) {
    const apiQ: FloderApiQ = {
      categoryCode,
    };
    return await webRequest.tryPost<ServiceR, FloderApiQ>(fetchAuthCategoryEndpoint, apiQ);
  } else {
    throw new Error(`fetchCoursewareAuthorizedUsers api not supported!`);
  }
}
