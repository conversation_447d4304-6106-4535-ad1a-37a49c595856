/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { UserConfigEntity } from '@manyun/knowledge-hub.service.dcexam.grant-courseware-permissions';

export type FloderApiQ = {
  categoryCode: number;
};
export type FileApiQ = {
  fileId: number;
};

export type ApiR = Response<UserConfigEntity>;

export type ServiceQ = {
  categoryCode?: number;
  fileId?: number;
};

export type ServiceR = UserConfigEntity;
