---
description: '知识中心-课件管理-查询课件授权 查询分类目录授权.'
labels: ['service', 'http', 'fetch-courseware-authorized-users']
---

## fetchCoursewareAuthorizedUsers

### Node Usage

```ts
import { fetchCoursewareAuthorizedUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-courseware-authorized-users/dist/index.node';

const { data, error } = await fetchCoursewareAuthorizedUsers();
```

### Browser Usage

#### 查询课件授权

```ts
import { fetchCoursewareAuthorizedUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-courseware-authorized-users/dist/index.browser';

const { data, error } = await fetchCoursewareAuthorizedUsers({ fileId: 2 });
```

#### 查询分类目录授权

```ts
import { fetchCoursewareAuthorizedUsers } from '@manyun/knowledge-hub.service.dcexam.fetch-courseware-authorized-users/dist/index.browser';

const { data, error } = await fetchCoursewareAuthorizedUsers({ categoryCode: 1 });
```
