/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import {
  fetchAuthCategoryEndpoint,
  fetchAuthCoursewareEndpoint,
} from './fetch-courseware-authorized-users';
import type { ApiR, FileApiQ, FloderApiQ } from './fetch-courseware-authorized-users.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(fetchAuthCategoryEndpoint, {
      asymmetricMatch({ categoryCode }: FloderApiQ) {
        return categoryCode == 2;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        roles: [{ roleId: 1, roleName: 'a', isDownload: true, roleCode: '' }],
        users: [
          {
            userId: 2,
            userName: 'c',
            isDownload: false,
            loginName: 's',
            roleList: ['a'],
            blockGuidList: ['aa'],
          },
        ],
      },
      errCode: null,
      errMessage: null,
    })
    .onPost(fetchAuthCoursewareEndpoint, {
      asymmetricMatch({ fileId }: FileApiQ) {
        return fileId === 2;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        roles: [
          { roleId: 1, roleName: 'a', isDownload: true, roleCode: '' },
          { roleId: 2, roleName: 'a', isDownload: true, roleCode: '' },
        ],
        users: [
          {
            userId: 2,
            userName: 'c',
            isDownload: false,
            loginName: 's',
            roleList: ['a'],
            blockGuidList: ['aa'],
          },
          {
            userId: 3,
            userName: 'c',
            isDownload: false,
            loginName: 's',
            roleList: ['a'],
            blockGuidList: ['aa'],
          },
        ],
      },
      errCode: null,
      errMessage: null,
    });
}
