/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ServiceQ, ServiceR } from './fetch-courseware-authorized-users.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchCoursewareAuthorizedUsers(data: ServiceQ) {
  throw new Error("fetchCoursewareAuthorizedUsers's node version not implemented!");
}
