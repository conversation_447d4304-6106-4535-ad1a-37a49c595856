/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import {
  batchUpdateCoursewaresEndpoint,
  createCoursewareEndpoint,
  updateCoursewareEndpoint,
} from './mutate-coursewares';
import type {
  BatchUpdateApiD,
  CreateApiD,
  ServiceD,
  ServiceR,
  UpdateApiD,
} from './mutate-coursewares.type';

/**
 * 知识中心-创建课件、替换课件、批量更新课件
 *
 * @see [Doc1](https://manyun.yuque.com/ewe5b3/zcferb/fl8x7g)
 * @see [Doc2](https://manyun.yuque.com/ewe5b3/zcferb/xpqsba)
 * @see [Doc3](https://manyun.yuque.com/ewe5b3/zcferb/gmsl3x)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateCoursewares({
  fileInfo,
  categoryCode,
  fileIds,
  folderIds,
  fileId,
}: ServiceD) {
  if (Array.isArray(fileInfo)) {
    const apiD: CreateApiD = {
      categoryCode: categoryCode!,
      fileInfoList: fileInfo,
    };
    return await webRequest.tryPost<ServiceR, CreateApiD>(createCoursewareEndpoint, apiD);
  } else if (fileId && fileInfo) {
    const apiD: UpdateApiD = {
      fileId,
      fileInfo,
    };
    return await webRequest.tryPost<ServiceR, UpdateApiD>(updateCoursewareEndpoint, apiD);
  } else if (categoryCode) {
    const apiD: BatchUpdateApiD = {
      categoryCode,
      fileIds: fileIds!,
      folderIds: folderIds!,
    };
    return await webRequest.tryPost<ServiceR, BatchUpdateApiD>(
      batchUpdateCoursewaresEndpoint,
      apiD
    );
  } else {
    throw new Error(`mutateCoursewares api not supported!`);
  }
}
