---
description: '知识中心-课件管理-创建课件、修改课件、批量更新课件.'
labels: ['service', 'http', 'mutate-coursewares']
---

## mutateCoursewares

### Node Usage

```ts
import { mutateCoursewares } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares/dist/index.node';

const { data, error } = await mutateCoursewares();
```

### Browser Usage

#### 创建课件

```ts
import { mutateCoursewares } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares/dist/index.browser';

const { data, error } = await mutateCoursewares({
  fileInfo: [{ fileName: 'a', fileType: '.MP4', filePath: 'a', fileTime: 1, fileSize: 10 }],
  categoryCode: 1,
});
```

#### 修改课件

```ts
import { mutateCoursewares } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares/dist/index.browser';

const { data, error } = await mutateCoursewares({
  fileInfo: { fileName: 'a', fileType: '.MP4', filePath: 'a', fileTime: 1, fileSize: 10 },
  fileId: 1,
});
```

#### 批量更新课件

```ts
import { mutateCoursewares } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares/dist/index.browser';

const { data, error } = await mutateCoursewares({
  categoryCode: 1,
  fileIds: [1],
  folderIds: [],
});
```
