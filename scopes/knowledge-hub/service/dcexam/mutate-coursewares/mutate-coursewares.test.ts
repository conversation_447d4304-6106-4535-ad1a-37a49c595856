/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateCoursewares as webService } from './mutate-coursewares.browser';

// import { registerWebMocks } from './fetch-course.mock';
// // registerWebMocks();

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve "create courseware success"', async () => {
  const { data, error } = await webService({
    categoryCode: '2',
    fileInfo: [
      {
        fileName: 'flower (2).mp4',
        filePath: 'group1/M00/00/05/rBAAD2GxywmAIFh8ABE3t94d3Nw557.mp4',
        fileSize: 1128375,
        fileType: '.MP4',
        fileTime: 5,
      },
    ],
  });
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "create courseware fail"', async () => {
  const { data, error } = await webService({
    categoryCode: '1',
    fileInfo: [
      {
        fileName: 'flower (2).mp4',
        filePath: 'group1/M00/00/05/rBAAD2GxywmAIFh8ABE3t94d3Nw557.mp4',
        fileSize: 1128375,
        fileType: '.MP4',
        fileTime: 5,
      },
    ],
  });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "update courseware success"', async () => {
  const { data, error } = await webService({
    fileId: 2,
    fileInfo: {
      fileName: 'flower (2).mp4',
      filePath: 'group1/M00/00/05/rBAAD2GxywmAIFh8ABE3t94d3Nw557.mp4',
      fileSize: 1128375,
      fileType: '.MP4',
      fileTime: 5,
    },
  });
  console.log(data, error);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "update courseware fail"', async () => {
  const { data, error } = await webService({
    fileId: 1,
    fileInfo: {
      fileName: 'flower (2).mp4',
      filePath: 'group1/M00/00/05/rBAAD2GxywmAIFh8ABE3t94d3Nw557.mp4',
      fileSize: 1128375,
      fileType: '.MP4',
      fileTime: 5,
    },
  });
  console.log(data, error);
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});

test('should resolve "batch update coursewares success"', async () => {
  const { data, error } = await webService({
    categoryCode: '1',
    fileIds: [1],
    folderIds: [],
  });
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "batch update coursewares fail"', async () => {
  const { data, error } = await webService({
    categoryCode: '1',
    fileIds: [2],
    folderIds: [],
  });
  expect(typeof error?.code).toBe('string');
  expect(typeof error?.message).toBe('string');
});
