/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import {
  batchUpdateCoursewaresEndpoint,
  createCoursewareEndpoint,
  updateCoursewareEndpoint,
} from './mutate-coursewares';
import type { ApiR, BatchUpdateApiD, CreateApiD, UpdateApiD } from './mutate-coursewares.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(createCoursewareEndpoint, {
      asymmetricMatch({ fileInfoList, categoryCode }: CreateApiD) {
        return fileInfoList.length > 0 && categoryCode;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(updateCoursewareEndpoint, {
      asymmetricMatch({ fileId }: UpdateApiD) {
        return fileId === 1;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(batchUpdateCoursewaresEndpoint, {
      asymmetricMatch({ fileIds }: BatchUpdateApiD) {
        return fileIds.includes(3);
      },
    })
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(batchUpdateCoursewaresEndpoint, {
      asymmetricMatch({ fileIds }: BatchUpdateApiD) {
        return fileIds === [2];
      },
    })
    .reply<ApiR>(200, {
      success: false,
      errCode: null,
      errMessage: null,
    });
}
