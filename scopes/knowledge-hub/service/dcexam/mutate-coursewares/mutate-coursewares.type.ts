/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type FileInfo = {
  fileName: string; //文件名称
  fileType: string; //文件类型 MP4|PDF
  filePath: string; //文件路径
  fileTime?: number; //单位 秒
  fileSize: number; //bytes
};

export type BatchUpdateApiD = {
  categoryCode: string;
  folderIds: number[];
  fileIds: number[];
};
export type UpdateApiD = {
  fileId: number;
  fileInfo: FileInfo;
};

export type CreateApiD = {
  categoryCode: string;
  fileInfoList: FileInfo[]; //文件列表
};

export type ApiR = WriteResponse;

export type ServiceD = {
  categoryCode?: string;
  folderIds?: number[];
  fileIds?: number[];
  fileId?: number;
  fileInfo?: FileInfo | FileInfo[];
};

export type ServiceR = boolean;
