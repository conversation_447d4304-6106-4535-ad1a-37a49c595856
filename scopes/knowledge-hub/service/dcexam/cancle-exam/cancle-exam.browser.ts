/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './cancle-exam';
import type { ApiD, ServiceD, ServiceR } from './cancle-exam.type';

/**
 * 知识中心-考试管理-取消考试
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/kzogr6)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function cancleExam({ examId }: ServiceD) {
  const apiQ: ApiD = { id: examId };

  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiQ);
}
