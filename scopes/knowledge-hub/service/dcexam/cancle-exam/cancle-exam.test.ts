/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { cancleExam as webService } from './cancle-exam.browser';
import { registerWebMocks } from './cancle-exam.mock';

// // registerWebMocks();

test('should cancel it successfully', async () => {
  const { data, error } = await webService({ examId: 1 });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should cancel it failed', async () => {
  const { data, error } = await webService({ examId: 2 });

  expect(data).toBe(false);
  expect(error?.message).toBe('failed');
});
