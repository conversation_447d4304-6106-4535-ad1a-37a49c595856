/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './cancle-exam';
import type { ApiD, ApiR } from './cancle-exam.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, { id: 1 } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, { id: 2 } as ApiD)
    .reply<ApiR>(200, {
      success: false,
      errCode: null,
      errMessage: 'failed',
    });
}
