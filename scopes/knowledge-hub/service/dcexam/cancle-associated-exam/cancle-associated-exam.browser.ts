/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-18
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './cancle-associated-exam';
import type { ApiQ, ServiceQ, ServiceR } from './cancle-associated-exam.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function cancleAssociatedExam(data: ServiceQ) {
  const apiQ: ApiQ = { id: data.id };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
