/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-18
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './cancle-associated-exam.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function cancleAssociatedExam(data: ServiceQ) {
  throw new Error("cancleAssociatedExam's node version not implemented!");
}
