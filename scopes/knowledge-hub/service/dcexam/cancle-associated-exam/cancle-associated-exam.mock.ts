/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-18
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './cancle-associated-exam';
import type { ApiR } from './cancle-associated-exam.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply<ApiR>(config => {
    const { id } = JSON.parse(config.data);
    if (!id && id !== 0) {
      return [
        200,
        {
          success: true,
          data: true,
          errCode: 405,
          errMessage: 'id should not be blank',
        },
      ];
    }
    return [
      200,
      {
        success: true,
        data: true,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
