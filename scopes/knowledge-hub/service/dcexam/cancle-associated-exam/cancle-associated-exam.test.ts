/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-18
 *
 * @packageDocumentation
 */
import { cancleAssociatedExam as webService } from './cancle-associated-exam.browser';
import { registerWebMocks } from './cancle-associated-exam.mock';

// // registerWebMocks();

test('should resolve "bar"', async () => {
  const { data, error } = await webService({ id: 1 });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
