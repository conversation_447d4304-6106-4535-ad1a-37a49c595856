/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-preview-paper';
import type { ApiQ, ApiR } from './fetch-preview-paper.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      asymmetricMatch({ paperId }: ApiQ) {
        return paperId === 1;
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: {
        id: 1, //试卷id
        type: 'SELECT', //试卷类型：SELECT("选题"),DRAW("抽题"),RANDOM("随机")
        categoryCode: '1001', //试题分类
        name: '测试试卷', //试卷名称
        totalGrade: 100, //总分
        properties: {
          randomOption: false, //选项乱序
          randomQuestion: false, //试题乱序
          groupByType: false, //按照试题类型排序
        },
        sections: [
          //板块题目
          {
            section: '测试板块', //板块名称
            questions: [
              //题目列表
              {
                id: 1,
                questionOrder: 1,
                type: 3,
                categoryCode: '1001',
                difficulty: 1,
                title: '测试题目1',
                options: ['A:测试选项1', 'B:测试选项2', 'C:测试选项3', 'D:测试选项4'],
                answers: ['A', 'B', 'C'],
                userAnswers: null,
                analysis: '这个题目明显选择 A B C',
                autoGrade: false,
                correct: false,
                grade: 2,
                userGrade: 0,
              },
            ],
          },
        ],
      },
      errCode: null,
      errMessage: null,
    });
}
