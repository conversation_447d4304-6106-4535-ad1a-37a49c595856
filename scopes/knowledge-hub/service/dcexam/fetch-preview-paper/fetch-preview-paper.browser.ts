/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import shortid from 'shortid';

import {
  BackendExamPaperQuestion,
  ExamPaper,
  QuestionsGroup,
} from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper';
import { BackendExamPaperStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exam-paper-users';
import {
  BackendPaper,
  BackendSection,
  BackendSectionQuestion,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-preview-paper';
import type { ApiQ, RquestRD, ServiceQ } from './fetch-preview-paper.type';

/**
 * 知识中心-试卷管理-试卷预览.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/id8u51)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchPreviewPaper({
  paperId,
}: ServiceQ): Promise<EnhancedAxiosResponse<Omit<ExamPaper, 'autoMark'> | null>> {
  const apiQ: ApiQ = { paperId };
  const result = await webRequest.tryPost<RquestRD, ApiQ>(endpoint, apiQ);

  if (result.error || result.data === null) {
    return { ...result, data: null };
  }

  const examPaper: Omit<ExamPaper, 'autoMark'> = convertBackendPaperToExamPaper(result.data);

  return { ...result, data: examPaper };
}

function convertBackendPaperToExamPaper(data: BackendPaper): Omit<ExamPaper, 'autoMark'> {
  const questionsGroups: QuestionsGroup[] = [];
  let finishedQuestionSize: number = 0;
  let questionSize: number = 0;
  ((data.sections || []) as BackendSection[]).map((paperSection, key) => {
    const questions = (paperSection.questions || []) as BackendSectionQuestion[];
    const questionEntities: Record<number, BackendExamPaperQuestion> = {};
    let questionScore: number = 0;
    let userScore: number = 0;
    let questionFinshedSize: number = 0;
    questions.forEach(question => {
      questionEntities[question.id] = { ...question } as BackendExamPaperQuestion;
      questionScore += Number(question.grade);
      userScore += Number(question.userGrade);
      if (question.userAnswers && question.userAnswers.length > 0) questionFinshedSize += 1;
    });
    const questionGroup: QuestionsGroup = {
      id: shortid(),
      name: paperSection.section,
      questionSize: questions.length,
      questionScore,
      userScore,
      questionEntities,
      questionIds: questions.map(question => question.id),
      visibleQuestionIds: [],
      visibleErrorQuestionIds: [],
    };

    questionsGroups.push(questionGroup);
    questionSize += Number(questionGroup.questionSize);
    finishedQuestionSize += questionFinshedSize;
  });

  const examPaper: Omit<ExamPaper, 'autoMark'> = {
    id: null,
    examId: null,
    paperId: data.id,
    state: BackendExamPaperStatus.INIT,
    startTime: null,
    endTime: null,
    timeLimit: 0,
    questionsGroups: questionsGroups,
    userId: null,
    userScore: null, //用户得分
    totalScore: data.totalGrade,
    userRanking: null, //考试排名
    result: null,
    finishedQuestionSize, //已完成试题数量
    questionSize, //试题总数
  };

  return examPaper;
}
