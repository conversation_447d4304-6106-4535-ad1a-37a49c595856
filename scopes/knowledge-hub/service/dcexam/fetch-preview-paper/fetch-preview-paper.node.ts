/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ApiQ, RquestRD, ServiceQ } from './fetch-preview-paper.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchPreviewPaper(data: ServiceQ) {
  throw new Error("fetchPreviewPaper's node version not implemented!");
}
