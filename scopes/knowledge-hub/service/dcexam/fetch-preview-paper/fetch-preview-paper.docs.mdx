---
description: '知识中心-试卷管理-试卷预览'
labels: ['service', 'http', 'fetch-preview-paper']
---

## fetchPreviewPaper

### Node Usage

```ts
import { fetchPreviewPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-preview-paper/dist/index.node';

const { data, error } = await fetchPreviewPaper();
```

### Browser Usage

```ts
import { fetchPreviewPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-preview-paper/dist/index.browser';

const { data, error } = await fetchPreviewPaper({
  paperId: 1,
});
```
