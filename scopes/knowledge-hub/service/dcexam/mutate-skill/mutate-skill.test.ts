/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
// import './mutate-skill.mock';
import { mutateSkill as webService } from './mutate-skill.browser';
import { registerWebMocks } from './mutate-skill.mock';

// registerWebMocks();

test('should resolve "create skill"', async () => {
  const { data, error } = await webService({ categoryCode: 8, level: 'L1', name: '1' });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('should resolve "edit skill"', async () => {
  const { data, error } = await webService({
    id: 1,
    categoryCode: 8,
    level: 'L1',
    name: '1',
  });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
