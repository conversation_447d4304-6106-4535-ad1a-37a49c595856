---
description: '知识中心-技能管理-创建技能、修改技能.'
labels: ['service', 'http', 'mutate-skill']
---

## mutateSkill

### Node Usage

```ts
import { mutateSkill } from '@manyun/knowledge-hub.service.dcexam.mutate-skill/dist/index.node';

const { data, error } = await mutateSkill();
```

### Browser Usage

#### 创建技能

```ts
import { mutateSkill } from '@manyun/knowledge-hub.service.dcexam.mutate-skill/dist/index.browser';

const { data, error } = await mutateSkill({ categoryCode: 1, level: 'L1', name: 'a' });
```

#### 修改技能

```ts
import { mutateSkill } from '@manyun/knowledge-hub.service.dcexam.mutate-skill/dist/index.browser';

const { data, error } = await mutateSkill({ id: 2, categoryCode: 1, level: 'L1', name: 'a' });
```
