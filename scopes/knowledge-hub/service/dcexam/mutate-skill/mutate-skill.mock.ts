/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { createSkillEndpoint, updateSkillEndpoint } from './mutate-skill';
import type { ApiR } from './mutate-skill.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(new RegExp(`${createSkillEndpoint}|${updateSkillEndpoint}`))
    .reply<ApiR>(config => {
      const { categoryCode, level, name } = JSON.parse(config.data);
      if (!categoryCode && categoryCode !== 0) {
        return [
          200,
          {
            success: false,
            errCode: 405,
            errMessage: 'categoryCode cannot be empty',
          },
        ];
      }
      if (!level) {
        return [
          200,
          {
            success: false,
            errCode: 405,
            errMessage: 'level cannot be empty',
          },
        ];
      }
      if (!name || !name.trim()) {
        return [
          200,
          {
            success: false,
            errCode: 405,
            errMessage: 'name cannot be empty',
          },
        ];
      }
      return [
        200,
        {
          success: true,
          errCode: null,
          errMessage: null,
        },
      ];
    });
}
