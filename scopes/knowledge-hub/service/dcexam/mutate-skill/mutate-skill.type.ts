/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export enum SkillLimitUnit {
  Year = 'YEAR',
  Month = 'MONTH',
  Day = 'DAY',
}

export const SKILL_LIMIT_TIME_TEXT_MAP = {
  [SkillLimitUnit.Year]: '年',
  [SkillLimitUnit.Month]: '月',
  [SkillLimitUnit.Day]: '日',
};

export type CreateApiD = {
  categoryCode: string | number;
  level: string;
  name: string;
  tenantId?: number | null;
  limitTime: number;
  limitUnit: SkillLimitUnit;
};

export type UpdateApiD = {
  id: number | string;
  categoryCode: string | number;
  level: string;
  name: string;
  limitTime: number;
  limitUnit: SkillLimitUnit;
};

export type ApiR = WriteResponse;

export type ServiceD = {
  id?: number | string;
  tenantId?: number | null;
  categoryCode: string | number;
  level: string;
  name: string;
  limitTime: number;
  limitUnit: SkillLimitUnit;
};

export type ServiceR = boolean;
