/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { createSkillEndpoint, updateSkillEndpoint } from './mutate-skill';
import type { CreateApiD, ServiceD, ServiceR, UpdateApiD } from './mutate-skill.type';

/**
 * 知识中心 - 新建/更新 技能 
 *
 * @see [Doc1](https://manyun.yuque.com/ewe5b3/zcferb/cyuf7a)
 * @see [Doc2](https://manyun.yuque.com/ewe5b3/zcferb/zkrgsx)

 * @param data POST Reqeust Body
 * @returns
 */
export async function mutateSkill({ id, ...rest }: ServiceD) {
  const apiD: CreateApiD = rest;
  if (!id && id !== 0) {
    return await webRequest.tryPost<ServiceR, CreateApiD>(createSkillEndpoint, apiD);
  } else {
    return await webRequest.tryPost<ServiceR, UpdateApiD>(updateSkillEndpoint, { id, ...apiD });
  }
}
