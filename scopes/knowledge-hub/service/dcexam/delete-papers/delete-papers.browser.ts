/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './delete-papers';
import type { ApiD, ServiceD, ServiceR } from './delete-papers.type';

/**
 * 知识中心-试卷管理-删除试卷
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/fhdr8w)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function deletePapers({ paperIds }: ServiceD) {
  const apiD: ApiD = { paperIds };

  return await webRequest.tryPost<ServiceR, ApiD>(endpoint, apiD);
}
