/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './delete-papers';
import type { ApiD, ApiR } from './delete-papers.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      paperIds: [1, 2, 3],
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(endpoint, {
      paperIds: [1],
    } as ApiD)
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}
