/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { deletePapers as webService } from './delete-papers.browser';
import { registerWebMocks } from './delete-papers.mock';

// // registerWebMocks();

test('delete one or more papers', async () => {
  const { data, error } = await webService({ paperIds: [1, 2, 3] });

  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
