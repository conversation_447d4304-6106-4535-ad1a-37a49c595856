/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-28
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './export-question';
import type { ApiQ, ApiR } from './export-question.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {
      asymmetricMatch({ foo }: ApiQ) {
        return foo === 'foo';
      },
    })
    .reply<ApiR>(200, {
      success: true,
      data: 'bar',
      errCode: null,
      errMessage: null,
    })

    // Pass through any other mismatches.
    .onPost(endpoint)
    .passThrough();
}
