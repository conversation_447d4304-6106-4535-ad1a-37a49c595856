/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export enum BackendPapersGenFun {
  SELECT = 'SELECT',
  DRAW = 'DRAW',
  RANDOM = 'RANDOM',
}

/**判卷方式 */
export enum BackendMarkingWay {
  MIXINGRADE = 0,
  AUTOGRADE = 1,
}

export const BakendMarkingWayMapper = {
  [BackendMarkingWay.MIXINGRADE]: '系统(含人工)判卷',
  [BackendMarkingWay.AUTOGRADE]: '系统判卷',
};

export const BackendPapersGenFunMapper = {
  [BackendPapersGenFun.SELECT]: '选题组卷',
  [BackendPapersGenFun.DRAW]: '抽题组卷',
  [BackendPapersGenFun.RANDOM]: '随机组卷',
};

export type BackendPaper = {
  id: number;
  name: string;
  categoryCode: number | string;
  type: string; //试卷类型
  autoGrade?: boolean; //判卷方式: true - 系统判卷 false -系统(含人工)判卷
  totalGrade: number; //总分
  gmtCreate?: number; //创建时间
  gmtModified?: number; //修改时间
  modifierId?: number; // 修改人
  modifierName?: string; //修改人姓名
  properties?: {
    randomOption: boolean;
    randomQuestion: boolean;
    groupByType: boolean;
  };
  sections?: BackendSection[];
  drawRules?: BackendDrawRule[];
};

/** 试卷、考卷中的考题 */
export type BackendSectionQuestion = BackendQuestion & {
  questionOrder: number;
  grade: number;
  /** 如果是试卷的话，可能为 `null`，表示考生未考，如果是考卷的话，不可能为 `null` */
  userAnswers: string[] | null;
  /** `undefined` 表示未交卷, `null` 表示未批改，`true` 表示批改过，考生答对， `false` 表示批改过，考生答错 */
  correct?: boolean | null;
  /** 考生未作答时为 `0` */
  userGrade: number;
  /**是否被确认*/
  isConfirmed?: boolean;
};

export type BackendSectionQuestionsId = { id: number; grade?: number };

export type BackendSection = {
  section: string;
  questions?: BackendSectionQuestion[] | BackendSectionQuestionsId[];
};

export type BackendQuestionDrawRule = {
  type: number;
  grade: number;
  number?: number;
  randomNumber?: Record<string, number>;
};
export type BackendDrawRule = {
  section: string;
  categoryCodeList?: number | number[] | string | string[]; //随机组卷方式 需要 题目分类
  questionDrawRules: BackendQuestionDrawRule[];
};

export type Paper = {
  id: number;
  name: string;
  categoryCode: number | string;
  genFun: BackendPapersGenFun; //组卷方式
  markingWay: BackendMarkingWay; //判卷方式
  allowArbitraryOptionsOrder: boolean;
  allowArbitraryQuestionsOrder: boolean;
  orderByQuestionTypeInSections: boolean;
  totalQuestionSize?: number;
  totalScore?: number;
  sectionIds: number[];
  sectionEntities: Record<number, PaperSection>;
};

export type PaperSection = {
  id: number;
  name: string;
  totalSize?: number; //总题数
  totalScore?: number; //总分数
  questionIds?: number[];
  questions?: (BackendQuestion & { grade?: number })[] | BackendSectionQuestionsId[]; // 问题对象
  typeConfig?: Record<string, SectionTypeConfig>;
  questionCategory?: number | string | number[] | string[]; //随机模式 下 题目分类
};

export type SectionTypeConfig = {
  score: number; // 分数
  drawTotal?: number; // 抽题数
  difficultCount?: Record<string | number, number>; //抽题难度 对应的数量
};

export type ApiQ = {
  type?: string;
  name?: string;
  categoryCodes?: number[];
  createStartTime?: number | null;
  createEndTime?: number | null;
  pageNum: number;
  pageSize: number;
  autoGrade?: boolean | null;
};

export type ApiR = ListResponse<BackendPaper[]>;

export type ServiceQ = {
  markingWay?: BackendMarkingWay | null;
  genFun?: BackendPapersGenFun;
  name?: string;
  categoryCodes?: number[];
  timeRange?: [number, number];
  page: number;
  pageSize: number;
};

export type ServiceR = {
  data: BackendPaper[];
  total: number;
};
