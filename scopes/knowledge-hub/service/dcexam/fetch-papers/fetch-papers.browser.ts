/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './fetch-papers';
import type { ApiQ, ServiceQ, ServiceR } from './fetch-papers.type';

/**
 * 知识中心-试卷管理-试卷列表.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/ingv58)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchPapers({
  markingWay,
  genFun,
  name,
  categoryCodes,
  timeRange,
  page,
  pageSize,
}: ServiceQ) {
  const apiQ: ApiQ = {
    name,
    categoryCodes,
    type: genFun,
    createStartTime: timeRange ? timeRange[0] : undefined,
    createEndTime: timeRange ? timeRange[1] : undefined,
    autoGrade: markingWay == 1 ? true : markingWay == 0 ? false : markingWay,
    pageNum: page,
    pageSize: pageSize,
  };

  return await webRequest.tryPost<ServiceR, ApiQ>(endpoint, apiQ);
}
