/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { PAPERS, PAPERS2 } from './entities';
import { endpoint } from './fetch-papers';
import type { ApiQ, ApiR } from './fetch-papers.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    const { pageNum } = JSON.parse(config.data) as ApiQ;

    return [
      200,
      {
        success: true,
        data: pageNum === 1 ? PAPERS : PAPERS2,
        total: 100,
        errCode: null,
        errMessage: null,
      },
    ];
  });
}
