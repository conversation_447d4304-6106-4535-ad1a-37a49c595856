/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-08
 *
 * @packageDocumentation
 */
import { BackendPaper } from './fetch-papers.type';

export const PAPERS: BackendPaper[] = [
  {
    id: 1, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: '1001', //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 2, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: '1001', //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 3, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: '1001', //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 4, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: 10001, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 5, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 6, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 7, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 8, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 9, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 10, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
];

export const PAPERS2: BackendPaper[] = [
  {
    id: 1, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: '1001', //分类
    name: '测试试卷2', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 2, //id
    autoGrade: false, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: '1001', //分类
    name: '测试试卷2', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 3, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: '1001', //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 4, //id
    autoGrade: false, //判卷方式
    type: 'SELECT', //试卷类型
    categoryCode: 10001, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 5, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 6, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 7, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 8, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 9, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
  {
    id: 10, //id
    autoGrade: true, //判卷方式
    type: 'DRAW', //试卷类型
    categoryCode: 2, //分类
    name: '测试试卷', //试卷名称
    totalGrade: 100, //总分
    gmtCreate: 1631762517869, //创建时间
    gmtModified: 1631762517869,
    modifierId: 1, //修改人
    modifierName: 'admin', //修改人姓名
  },
];
