---
description: '知识中心-试卷管理-试卷列表'
labels: ['service', 'http', 'fetch-papers']
---

## fetchPapers

### Node Usage

```ts
import { fetchPapers } from '@manyun/knowledge-hub.service.dcexam.fetch-papers/dist/index.node';

const { data, error } = await fetchPapers();
```

### Browser Usage

#### 试卷分页列表

```ts
import { fetchPapers } from '@manyun/knowledge-hub.service.dcexam.fetch-papers/dist/index.browser';

const { data, error } = await fetchPapers({
  page: 1,
  pageSize: 10,
});
```
