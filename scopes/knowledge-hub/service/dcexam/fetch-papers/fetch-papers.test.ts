/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { fetchPapers as webService } from './fetch-papers.browser';
import { registerWebMocks } from './fetch-papers.mock';

// registerWebMocks();
test('should return papers"', async () => {
  const { data, error } = await webService({ page: 1, pageSize: 10 });
  expect(data.data).toHaveLength(10);
  expect(data.total).toBe(100);
  expect(error).toBe(undefined);
});
