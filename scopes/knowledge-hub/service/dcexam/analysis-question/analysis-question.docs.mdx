---
description: 'A analysisQuestion HTTP API service.'
labels: ['service', 'http', 'label3']
---

## analysisQuestion

### Node Usage

```ts
import { analysisQuestion } from '@manyun/knowledge-hub.service.dcexam.analysis-question/dist/index.node';

const { data, error } = await analysisQuestion();
```

### Browser Usage

```ts
import { analysisQuestion } from '@manyun/knowledge-hub.service.dcexam.analysis-question/dist/index.browser';

const { data, error } = await analysisQuestion();
```
