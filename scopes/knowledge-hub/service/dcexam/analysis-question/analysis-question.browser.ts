/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';

import { endpoint } from './analysis-question';
import type { ApiQ, ServiceQ, ServiceR } from './analysis-question.type';

/**
 * 知识中心-试题管理-试题解析.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/qgr065)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function analysisQuestion(data: ServiceQ) {
  const apiQ: ApiQ = { file: data.file };

  return await webRequest.tryPost<
    ServiceR,
    ApiQ,
    {
      headers: {
        'Content-Type': 'multipart/form-data';
      };
    }
  >(endpoint, apiQ);
}
