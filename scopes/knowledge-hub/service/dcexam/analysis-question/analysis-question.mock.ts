/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-29
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './analysis-question';
import type { ApiR } from './analysis-question.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onPost(endpoint, {})
    .reply<ApiR>(200, {
      success: true,
      data: [],
      errCode: null,
      errMessage: null,
    })

    // Pass through any other mismatches.
    .onPost(endpoint)
    .passThrough();
}
