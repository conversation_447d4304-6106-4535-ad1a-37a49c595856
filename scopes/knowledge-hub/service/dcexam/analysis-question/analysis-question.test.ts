/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-29
 *
 * @packageDocumentation
 */
import { analysisQuestion as webService } from './analysis-question.browser';
import { registerWebMocks } from './analysis-question.mock';

// mocks for tests should resolve immediately.
// // registerWebMocks();

test('should resolve "bar"', async () => {
  // const { data, error } = await webService({ foo: 'foo' });
  // expect(data).toBe('bar');
  // expect(error).toBe(undefined);
});
