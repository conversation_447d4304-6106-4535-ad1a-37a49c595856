/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-7
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 试题id
   */
  questionId: number;
  pageNum: number;
  pageSize: number;
};

export type BackendQuestionExam = {
  id: number;
  /**
   * 考试名称
   */
  name: string;
  /**
   * 考试状态
   */
  status: string;
  /**
   * 试卷分类
   */
  categoryCode: string;
  /**
   * 试卷id
   */
  paperId: number;
  /**
   * 试卷类型
   */
  type: string;
  /**
   * 开始时间
   */
  startTime: number;
  /**
   * 结束时间
   */
  endTime: number;
  /**
   * 试题总数
   */
  questionTotalCount: number;
  /**
   * 正确试题数
   */
  questionCorrectCount: number;
};

export type ApiResponse = ListResponse<BackendQuestionExam[]>;
