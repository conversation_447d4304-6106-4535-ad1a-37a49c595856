/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-5-7
 *
 * @packageDocumentation
 */

import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-question-exams';
import type { ApiArgs, ApiResponse } from './fetch-question-exams.type';

const executor = getExecutor(webRequest);

/**
 * @param args
* @returns
 */
export function fetchQuestionExams(args: ApiArgs): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  return executor(args);
}
