/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { ApiQ, ServiceQ, ServiceR } from './fetch-knowledge-hub-category.type';

/**
 * Fetches "bar" from a backend HTTP API.
 *
 * @see [Doc]()
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchKnowledgeHubCategory(data: ServiceQ) {
  throw new Error("fetchKnowledgeHubCategory's node version not implemented!");
}
