/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { fetchKnowledgeHubCategory as webService } from './fetch-knowledge-hub-category.browser';
import { BackendCategoryType } from './fetch-knowledge-hub-category.type';

test('should resolve the exact type categories', async () => {
  const { data, error } = await webService({ categoryType: BackendCategoryType.CERT });
  expect(data).toHaveLength(9);
  expect(data[0]).toHaveProperty('type', BackendCategoryType.CERT);
  expect(error).toBe(undefined);
});

test('toMatch array which request auth category ', async () => {
  const { data, error } = await webService({
    categoryType: BackendCategoryType.COURSEWARE,
  });
  expect(data).toHaveLength(9);
  expect(data[0]).toHaveProperty('type', BackendCategoryType.COURSEWARE);
  expect(error).toBe(undefined);
});

test('toMatch  array by request category which has count', async () => {
  const { data, error } = await webService({
    categoryType: BackendCategoryType.EXAM,
    needNum: true,
  });
  expect(data).toHaveLength(9);
  expect(data[0]).toHaveProperty('type', BackendCategoryType.EXAM);
  expect(data[0]).toHaveProperty('count', 10);
  expect(error).toBe(undefined);
});
