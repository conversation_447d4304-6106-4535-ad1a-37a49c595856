/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { categoryEndPoint, categoryNumberEndPoint } from './fetch-knowledge-hub-category';
import type {
  ApiQ,
  Category,
  NumServiceR,
  ServiceQ,
  ServiceR,
} from './fetch-knowledge-hub-category.type';

/**
 * 知识中心-分类列表
 *
 * @see [Doc 1](https://manyun.yuque.com/ewe5b3/zcferb/yfzhdf)
 *   [Doc 2](https://manyun.yuque.com/ewe5b3/zcferb/fvp87s)
 *   [Doc 3](https://manyun.yuque.com/ewe5b3/zcferb/bnaari)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchKnowledgeHubCategory({
  categoryType,
  userId,
  needNum = false,
  isAuth = false,
}: ServiceQ): Promise<EnhancedAxiosResponse<Category[]>> {
  const apiQ: ApiQ = {
    type: categoryType,
    userId,
    isAuth,
  };
  const { data, error, ...rest } = await webRequest.tryPost<ServiceR, ApiQ>(categoryEndPoint, apiQ);
  if (!error) {
    const categories: Category[] = (data.data || []).map(category => {
      return {
        ...category,
        code: category.id,
      };
    });
    if (needNum) {
      const { data: numDatas, error: numError } = await webRequest.tryPost<NumServiceR, ApiQ>(
        categoryNumberEndPoint,
        apiQ
      );
      if (!numError) {
        categories.forEach(category => {
          category.count = numDatas[category.code];
        });
      } else {
        throw new Error(`${numError?.message}`);
      }
    }
    return { data: categories, error, ...rest };
  } else {
    return { data: [], error, ...rest };
  }
}
