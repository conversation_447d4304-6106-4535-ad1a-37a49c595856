---
description: '知识中心-分类管理-分类列表'
labels: ['service', 'http', 'fetch-knowledge-hub-category']
---

## fetchKnowledgeHubCategory

### Node Usage

```ts
import { fetchKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category/dist/index.node';

const { data, error } = await fetchKnowledgeHubCategory();
```

### Browser Usage

#### 通过分类类型请求分类列表

```ts
import { fetchKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category/dist/index.browser';
import { BackendCategoryType } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category/dist/types';

const { data, error } = await fetchKnowledgeHubCategory({
  categoryType: BackendCategoryType.FOLDER,
});
```

#### 通过分类类型请求分类列表并返回类型数量

```ts
import { fetchKnowledgeHubCategory } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category/dist/index.browser';
import { BackendCategoryType } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category/dist/types';

const { data, error } = await fetchKnowledgeHubCategory({
  categoryType: BackendCategoryType.FOLDER,
  needNum: true,
});
```
