/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { categoryEndPoint, categoryNumberEndPoint } from './fetch-knowledge-hub-category';
import {
  ApiQ,
  ApiR,
  BackendCategory,
  BackendCategoryType,
  NumApiR,
} from './fetch-knowledge-hub-category.type';

export function registerWebMocks(mock: ReturnType<typeof getMock>) {
  mock
    .onPost(new RegExp(`${categoryEndPoint}`))
    .reply<ApiR>(config => {
      const { type } = JSON.parse(config.data) as ApiQ;
      const baseData = getCategories(type);
      let data: BackendCategory[] = baseData.concat();
      if (type === BackendCategoryType.QUESTION) {
        data = getEditCategories(type);
      } else if (type === BackendCategoryType.PAPER) {
        data = getEditCategories(type);
      }
      return [
        200,
        {
          success: true,
          data: data,
          errCode: null,
          errMessage: null,
        },
      ];
    })

    .onPost(categoryNumberEndPoint)
    .reply<NumApiR>(200, {
      success: true,
      data: {
        1: 10,
        2: 10,
        3: 10,
        4: 10,
        5: 10,
        6: 10,
        7: 10,
        8: 10,
        9: 10,
      },
      errCode: null,
      errMessage: null,
    });
}

const getCategories = (type: BackendCategoryType): BackendCategory[] => [
  {
    id: 1,
    name: '分类名称 1',
    parentId: 0,
    type,
  },
  {
    id: 2,
    name: '分类名称 1-1',
    parentId: 1,
    type,
  },
  {
    id: 8,
    name: '分类名称 1-1-1',
    parentId: 2,
    type,
  },
  {
    id: 3,
    name: '分类名称 1-2',
    parentId: 1,
    type,
  },
  {
    id: 9,
    name: '分类名称 1-2-1',
    parentId: 3,
    type,
  },
  {
    id: 4,
    name: '分类名称 2',
    parentId: 0,
    type,
  },
  {
    id: 5,
    name: '分类名称 2-1',
    parentId: 4,
    type,
  },
  {
    id: 6,
    name: '分类名称 2-2',
    parentId: 4,
    type,
  },
  {
    id: 7,
    name: '分类名称 2-3',
    parentId: 4,
    type,
  },
  {
    id: 17,
    name: '分类名称 2-3-1',
    parentId: 7,
    type,
  },
];

const getEditCategories = (type: BackendCategoryType): BackendCategory[] => [
  {
    id: 1,
    name: '全量',
    parentId: 0,
    type,
  },
  {
    id: 7,
    name: '分类名称 2-1',
    parentId: 1,
    type,
  },
  {
    id: 2,
    name: '分类名称 1-1',
    parentId: 1,
    type,
  },

  {
    id: 4,
    name: '分类名称 1-2',
    parentId: 2,
    type,
  },
  {
    id: 3,
    name: '分类名称 1-1-1',
    parentId: 4,
    type,
  },
];
