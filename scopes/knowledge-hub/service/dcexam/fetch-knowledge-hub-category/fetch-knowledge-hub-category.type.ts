/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-8
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendCategory = {
  id: number;
  name: string;
  parentId: number; // 父类型id
  type: string; //类型
  level?: number; //级别
  count?: number; //数量
};

export enum BackendCategoryType {
  QUESTION = 'QUESTION', //题目
  /** 课件 */
  COURSEWARE = 'COURSEWARE',
  PAPER = 'PAPER', //试卷
  EXAM = 'EXAM', //考试
  COURSE = 'COURSE', //课程
  CERT = 'CERT', //证书
}

export type Category = BackendCategory & {
  code: number;
};

export type ApiQ = {
  type: BackendCategoryType;
  userId?: number;
  isAuth?: boolean;
};

export type ApiR = Response<BackendCategory[]>;
export type NumApiR = Response<Record<string, number>>;

export type ServiceQ = {
  categoryType: BackendCategoryType; //分类类型
  needNum?: boolean; //是否需要分类数量 默认false
  userId?: number; //在课件列表页面时要传入此参数
  isAuth?: boolean; //是否查询有权限的文件夹
};

export type ServiceR = { data: BackendCategory[]; total: number };

export type NumServiceR = Record<string, number>;
