/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendPaper, Paper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export type CreateApiD = Omit<BackendPaper, 'id'>;

export type UpdateApiD = BackendPaper;

export type UpdateBatchApiD = {
  paperIds: number[];
  categoryCode: number | number[] | string | string[];
};

export type ApiR = WriteResponse;

export type ServiceD = Omit<Paper, 'id'> | Paper | Pick<Paper, 'id' | 'categoryCode'>[];

export type ServiceR = boolean;
