---
description: '知识中心-试卷管理-新增、更新、批量更新'
labels: ['service', 'http', 'mutate-papers']
---

## mutatePapers

### Node Usage

```ts
import { mutatePapers } from '@manyun/knowledge-hub.service.dcexam.mutate-papers/dist/index.node';

const { data, error } = await mutatePapers();
```

### Browser Usage

#### 批量更新试卷

```ts
import { mutatePapers } from '@manyun/knowledge-hub.service.dcexam.mutate-papers/dist/index.browser';

const { data, error } = await mutatePapers([
  { id: 1, categoryCode: '1001' },
  { id: 2, categoryCode: '1001' },
  { id: 3, categoryCode: '1001' },
]);
```

#### 创建试卷 - 选题模式

```ts
import { mutatePapers } from '@manyun/knowledge-hub.service.dcexam.mutate-papers/dist/index.browser';

const { data, error } = await mutatePapers({
  allowArbitraryOptionsOrder: false,
  allowArbitraryQuestionsOrder: false,
  categoryCode: '1001',
  genFun: BackendPapersGenFun.SELECT,
  markingWay: BakendMarkingWay.MIXINGRADE,
  name: '测试试卷',
  orderByQuestionTypeInSections: false,
  sectionEntities: {
    0: {
      id: 0,
      name: '测试板块',
      questions: [{ id: 1, grade: 5 }] as BackendSectionQuestionsId[],
      questionIds: [1],
    },
  },
  sectionIds: [0],
  totalScore: 100,
});
```

#### 创建试卷 - 抽题模式

```ts
import { mutatePapers } from '@manyun/knowledge-hub.service.dcexam.mutate-papers/dist/index.browser';

const { data, error } = await mutatePapers({
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    categoryCode: '1001',
    genFun: BackendPapersGenFun.DRAW,
    markingWay: BakendMarkingWay.MIXINGRADE,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1 }] as BackendSectionQuestionsId[],
        questionIds: [1],
        `typeConfig`: {
          ESSAY: { drawTotal: 5, score: 5 },
          MULTIPLE_CHOICE: { drawTotal: 5, score: 5 },
          SHORT_ANSWER: { drawTotal: 5, score: 5 },
          SINGLE_CHOICE: { drawTotal: 5, score: 5 },
          TRUE_OR_FALSE: { drawTotal: 5, score: 5 },
        },
      },
    },
    sectionIds: [0],
    totalScore: 100,
  } as Omit<Paper, 'id'>);
```

#### 创建试卷 - 随机模式

```ts
import { mutatePapers } from '@manyun/knowledge-hub.service.dcexam.mutate-papers/dist/index.browser';

const { data, error } = await mutatePapers({
    id: 1,
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    categoryCode: ['1001'],
    genFun: BackendPapersGenFun.RANDOM,
    markingWay: BakendMarkingWay.MIXINGRADE,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1 }] as BackendSectionQuestionsId[],
        questionIds: [1],
        `typeConfig`: {
          ESSAY: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          MULTIPLE_CHOICE: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SHORT_ANSWER: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SINGLE_CHOICE: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          TRUE_OR_FALSE: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
        },
      },
    },
    sectionIds: [0],
    totalScore: 100,
  } as Paper);
```
