/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendPaper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { getMock } from '@manyun/service.request';

import {
  createPaperEndpoint,
  updateBatchPaperEndPoint,
  updatePaperEndpoint,
} from './mutate-papers';
import type { ApiR, CreateApiD, UpdateApiD, UpdateBatchApiD } from './mutate-papers.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  // update batch
  webMock
    .onPost(
      updateBatchPaperEndPoint, //批量更新
      {
        paperIds: [1, 2, 3],
        categoryCode: '1001',
      } as UpdateBatchApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      updateBatchPaperEndPoint, //批量更新
      {
        paperIds: [1, 2],
        categoryCode: 3,
      } as UpdateBatchApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      createPaperEndpoint, //创建选题模式
      backendPaperForSelect as CreateApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      updatePaperEndpoint, //编辑选题模式
      {
        id: 0,
        ...backendPaperForSelect,
      }
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      createPaperEndpoint, //创建抽题模式
      backendPaperForDraw as CreateApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      updatePaperEndpoint, //编辑抽题模式
      {
        ...backendPaperForDraw,
        id: 1,
      } as UpdateApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      createPaperEndpoint, //创建随机模式
      backendPaperForRandom as CreateApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    })
    .onPost(
      updatePaperEndpoint, //编辑随机模式
      {
        id: 1,
        ...backendPaperForRandom,
      } as UpdateApiD
    )
    .reply<ApiR>(200, {
      success: true,
      errCode: null,
      errMessage: null,
    });
}

// create or edit  paper which mode is  DRAW('抽题模式')
const backendPaperForDraw: Omit<BackendPaper, 'id'> =
  //抽题模式
  {
    name: '测试试卷', //试卷名称
    autoGrade: false,
    type: 'DRAW', //试卷类型：SELECT("选题"),DRAW("抽题"),RANDOM("随机")
    categoryCode: '1001', //试卷分类
    properties: {
      randomOption: false, //选项乱序
      randomQuestion: false, //试题乱序
      groupByType: false, //按照试题类型排序
    },
    sections: [
      //板块题目
      {
        questions: [
          //题目
          {
            id: 1, //题目id
          },
        ],
        section: '测试板块', //题目板块
      },
    ],
    drawRules: [
      //抽题规则
      {
        section: '测试板块', //模块名称
        questionDrawRules: [
          //板块抽题规则
          {
            grade: 5, //分值
            number: 5, //数量
            type: 5, //类型
          },
          {
            grade: 5, //分值
            number: 5, //数量
            type: 2, //类型
          },
          {
            grade: 5, //分值
            number: 5, //数量
            type: 4, //类型
          },
          {
            grade: 5, //分值
            number: 5, //数量
            type: 1, //类型
          },
          {
            grade: 5, //分值
            number: 5, //数量
            type: 3, //类型
          },
        ],
      },
    ],
    totalGrade: 100, //总分
  };
// create or edit paper which mode is RANDOM(随机模式)
const backendPaperForRandom: Omit<BackendPaper, 'id'> = {
  name: '测试试卷', //试卷名称
  autoGrade: false,
  type: 'RANDOM', //试卷类型：SELECT("选题"),DRAW("抽题"),RANDOM("随机")
  categoryCode: 1001, //试卷分类
  properties: {
    randomOption: false, //选项乱序
    randomQuestion: false, //试题乱序
    groupByType: false, //按照试题类型排序
  },
  drawRules: [
    //抽题规则
    {
      section: '测试板块', //模块名称
      categoryCodeList: ['1001'], //分类
      questionDrawRules: [
        //板块抽题规则
        {
          grade: 5, //分值
          randomNumber: {
            //抽题难度及数量
            '1': 10,
            '2': 10,
            '3': 10,
            '4': 10,
            '5': 10,
          },
          type: 5, //类型
        },
        {
          grade: 5, //分值
          randomNumber: {
            //抽题难度及数量
            '1': 10,
            '2': 10,
            '3': 10,
            '4': 10,
            '5': 10,
          },
          type: 2, //类型
        },
        {
          grade: 5, //分值
          randomNumber: {
            //抽题难度及数量
            '1': 10,
            '2': 10,
            '3': 10,
            '4': 10,
            '5': 10,
          },
          type: 4, //类型
        },
        {
          grade: 5, //分值
          randomNumber: {
            //抽题难度及数量
            '1': 10,
            '2': 10,
            '3': 10,
            '4': 10,
            '5': 10,
          },
          type: 1, //类型
        },
        {
          grade: 5, //分值
          randomNumber: {
            //抽题难度及数量
            '1': 10,
            '2': 10,
            '3': 10,
            '4': 10,
            '5': 10,
          },
          type: 3, //类型
        },
      ],
    },
  ],
  totalGrade: 100, //总分
};
//create or edit paper which mode is SELECT(选题模式)
const backendPaperForSelect: Omit<BackendPaper, 'id'> = {
  name: '测试试卷',
  autoGrade: false,
  categoryCode: '1001',
  type: 'SELECT',
  totalGrade: 100,
  properties: {
    randomOption: false,
    randomQuestion: false,
    groupByType: false,
  },
  sections: [
    {
      section: '测试板块',
      questions: [
        {
          id: 1,
          grade: 20,
        },
      ],
    },
    {
      section: '测试板块',
      questions: [
        {
          id: 1,
          grade: 80,
        },
      ],
    },
  ],
};

const backendPaperForSelect2: Omit<BackendPaper, 'id'> = {
  name: '',
  autoGrade: false,
  categoryCode: '',
  type: 'SELECT',
  totalGrade: 0,
  properties: {
    randomOption: false,
    randomQuestion: false,
    groupByType: false,
  },
  sections: [
    {
      section: '板块名称',
      questions: [
        {
          id: 1,
          grade: 0,
        },
        {
          id: 30,
          grade: 0,
        },
      ],
    },
  ],
};
