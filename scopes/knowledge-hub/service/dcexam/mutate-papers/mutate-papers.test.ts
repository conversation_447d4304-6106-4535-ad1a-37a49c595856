/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import {
  BackendMarkingWay,
  BackendPapersGenFun,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  BackendSectionQuestionsId,
  Paper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

import { mutatePapers as webService } from './mutate-papers.browser';
import { registerWebMocks } from './mutate-papers.mock';

// registerWebMocks();
test('update batch papers', async () => {
  const { data, error } = await webService([
    { id: 1, categoryCode: '1001' },
    { id: 2, categoryCode: '1001' },
    { id: 3, categoryCode: '1001' },
  ]);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('create a paper by SELECT way', async () => {
  const { data, error } = await webService({
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    categoryCode: '1001',
    genFun: BackendPapersGenFun.SELECT,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    markingWay: 0,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1, grade: 20 }] as BackendSectionQuestionsId[],
        questionIds: [1],
      },
      1: {
        id: 1,
        name: '测试板块',
        questions: [{ id: 1, grade: 80 }] as BackendSectionQuestionsId[],
        questionIds: [1],
      },
    },
    sectionIds: [0, 1],
    totalScore: 100,
  });
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('update a paper by SELECT way', async () => {
  const { data, error } = await webService({
    id: 0,
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    categoryCode: '1001',
    genFun: BackendPapersGenFun.SELECT,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    markingWay: 0,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1, grade: 20 }] as BackendSectionQuestionsId[],
        questionIds: [1],
      },
      1: {
        id: 1,
        name: '测试板块',
        questions: [{ id: 1, grade: 80 }] as BackendSectionQuestionsId[],
        questionIds: [1],
      },
    },
    sectionIds: [0, 1],
    totalScore: 100,
  });
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('create a paper by DRWA way', async () => {
  const { data, error } = await webService({
    allowArbitraryOptionsOrder: false,
    markingWay: BackendMarkingWay.MIXINGRADE,
    allowArbitraryQuestionsOrder: false,
    categoryCode: '1001',
    genFun: BackendPapersGenFun.DRAW,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1 }] as BackendSectionQuestionsId[],
        questionIds: [1],
        typeConfig: {
          ESSAY: { drawTotal: 5, score: 5 },
          MULTIPLE_CHOICE: { drawTotal: 5, score: 5 },
          SHORT_ANSWER: { drawTotal: 5, score: 5 },
          SINGLE_CHOICE: { drawTotal: 5, score: 5 },
          TRUE_OR_FALSE: { drawTotal: 5, score: 5 },
        },
      },
    },
    sectionIds: [0],
    totalScore: 100,
  } as Omit<Paper, 'id'>);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('update a paper by DRWA way', async () => {
  const { data, error } = await webService({
    id: 1,
    allowArbitraryOptionsOrder: false,
    markingWay: BackendMarkingWay.MIXINGRADE,
    allowArbitraryQuestionsOrder: false,
    categoryCode: '1001',
    genFun: BackendPapersGenFun.DRAW,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1 }] as BackendSectionQuestionsId[],
        questionIds: [1],
        questionCategory: ['1001'],
        typeConfig: {
          ESSAY: { drawTotal: 5, score: 5 },
          MULTIPLE_CHOICE: { drawTotal: 5, score: 5 },
          SHORT_ANSWER: { drawTotal: 5, score: 5 },
          SINGLE_CHOICE: { drawTotal: 5, score: 5 },
          TRUE_OR_FALSE: { drawTotal: 5, score: 5 },
        },
      },
    },
    sectionIds: [0],
    totalScore: 100,
  } as Paper);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('create a paper by RANDOW way', async () => {
  const { data, error } = await webService({
    allowArbitraryOptionsOrder: false,
    markingWay: BackendMarkingWay.MIXINGRADE,
    allowArbitraryQuestionsOrder: false,
    categoryCode: ['1001'],
    genFun: BackendPapersGenFun.RANDOM,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1 }] as BackendSectionQuestionsId[],
        questionIds: [1],
        questionCategory: ['1001'],
        typeConfig: {
          ESSAY: {
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          MULTIPLE_CHOICE: {
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SHORT_ANSWER: {
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SINGLE_CHOICE: {
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          TRUE_OR_FALSE: {
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
        },
      },
    },
    sectionIds: [0],
    totalScore: 100,
  } as any);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});

test('update a paper by RANDOW way', async () => {
  const { data, error } = await webService({
    id: 1,
    allowArbitraryOptionsOrder: false,
    markingWay: BackendMarkingWay.MIXINGRADE,
    allowArbitraryQuestionsOrder: false,
    categoryCode: ['1001'],
    genFun: BackendPapersGenFun.RANDOM,
    name: '测试试卷',
    orderByQuestionTypeInSections: false,
    sectionEntities: {
      0: {
        id: 0,
        name: '测试板块',
        questions: [{ id: 1 }] as BackendSectionQuestionsId[],
        questionIds: [1],
        questionCategory: ['1001'],
        typeConfig: {
          ESSAY: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          MULTIPLE_CHOICE: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SHORT_ANSWER: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          SINGLE_CHOICE: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
          TRUE_OR_FALSE: {
            // drawTotal: 5,
            score: 5,
            difficultCount: {
              EASIER_THAN_EASY: 10,
              EASY: 10,
              HARD: 10,
              HARDER_THAN_HARD: 10,
              NORMAL: 10,
            },
          },
        },
      },
    },
    sectionIds: [0],
    totalScore: 100,
  } as any);
  expect(data).toBe(true);
  expect(error).toBe(undefined);
});
