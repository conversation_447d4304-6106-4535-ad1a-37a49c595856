/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-9
 *
 * @packageDocumentation
 */
import { BackendSectionQuestionsId } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  BackendDrawRule,
  BackendPaper,
  BackendPapersGenFun,
  BackendSection,
  Paper,
} from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  BackendQuestionDifficulty,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import { webRequest } from '@manyun/service.request';

import type { CreateApiD, ServiceD, ServiceR, UpdateApiD, UpdateBatchApiD } from './';
import {
  createPaperEndpoint,
  updateBatchPaperEndPoint,
  updatePaperEndpoint,
} from './mutate-papers';

/**
 * 知识中心-试卷管理-新增、更新、批量更新.
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/zcferb/lfvdm6、https://manyun.yuque.com/ewe5b3/zcferb/qywf8w、https://manyun.yuque.com/ewe5b3/zcferb/vewwpf)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function mutatePapers(papers: ServiceD) {
  if (Array.isArray(papers)) {
    //批量更新
    const batchData: UpdateBatchApiD = {
      paperIds: papers.map(paper => paper.id),
      categoryCode: papers[0].categoryCode,
    };
    return await webRequest.tryPost<ServiceR, UpdateBatchApiD>(updateBatchPaperEndPoint, batchData);
  } else {
    if (papers.hasOwnProperty('id') && (papers as Paper).id !== undefined) {
      //更新
      const data: BackendPaper = convertPaperToBackendPaper(papers as Paper) as BackendPaper;
      return await webRequest.tryPost<ServiceR, UpdateApiD>(updatePaperEndpoint, data);
    } else {
      //创建
      const data: Omit<BackendPaper, 'id'> = convertPaperToBackendPaper(
        papers as Omit<Paper, 'id'>
      ) as Omit<BackendPaper, 'id'>;
      return await webRequest.tryPost<ServiceR, CreateApiD>(createPaperEndpoint, data);
    }
  }
}

/**
 *
 * @param paper Paper|Omit<Paper,'id'>)
 * @returns backendPaper Omit<BackendPaper, 'id'>|BackendPaper
 */
function convertPaperToBackendPaper(
  paper: Paper | Omit<Paper, 'id'>
): Omit<BackendPaper, 'id'> | BackendPaper {
  const { sections, drawRules } = convertToBackendSectionsRules(paper);
  const createBackendPapaer: Omit<BackendPaper, 'id'> | BackendPaper = {
    id: (paper as Paper).id,
    name: paper.name,
    autoGrade: paper.markingWay == 1 ? true : false,
    categoryCode: paper.categoryCode, // number || numebr []
    type: paper.genFun,
    totalGrade: paper.totalScore!,
    properties: {
      randomOption: paper.allowArbitraryOptionsOrder,
      randomQuestion: paper.allowArbitraryQuestionsOrder,
      groupByType: paper.orderByQuestionTypeInSections,
    },
    sections,
    drawRules,
  };
  return createBackendPapaer;
}

/**
 * @param paper  state存储的试卷
 * @returns { setions: BackendSection[],  drawrules:  BackendDrawRule[]}
 */
function convertToBackendSectionsRules(paper: Paper | Omit<Paper, 'id'>) {
  const { sectionEntities: sections, genFun, categoryCode: paperCategoryCode } = paper;
  const backendSections: BackendSection[] = [];
  const backendDrawRules: BackendDrawRule[] = [];
  paper.sectionIds.forEach(id => {
    const paperSection = paper.sectionEntities[id];
    if (genFun !== BackendPapersGenFun.RANDOM) {
      const backendSection: BackendSection = {
        section: paperSection.name,
        questions: (paperSection.questions as BackendSectionQuestionsId[]).map(question => ({
          id: question.id,
          grade: question.grade,
        })),
      };
      backendSections.push(backendSection);
    }
    //抽题、随机组卷方式
    if (paperSection.typeConfig && genFun !== BackendPapersGenFun.SELECT) {
      const questionDrawRules: any[] = [];
      for (const [key, sectionTypeConfig] of Object.entries(paperSection.typeConfig)) {
        const rule = {
          type: (BackendQuestionType as any)[key],
          grade: sectionTypeConfig.score,
          number: sectionTypeConfig.drawTotal,
          randomNumber:
            genFun !== BackendPapersGenFun.DRAW && sectionTypeConfig.difficultCount != undefined
              ? convertDrawRuleDifficulties(sectionTypeConfig.difficultCount)
              : undefined,
        };
        questionDrawRules.push(rule);
      }
      const backendDrawRule: BackendDrawRule = {
        section: paperSection.name,
        questionDrawRules,
      };
      if (genFun === BackendPapersGenFun.RANDOM) {
        backendDrawRule.categoryCodeList = Array.isArray(paperSection.questionCategory)
          ? (paperSection.questionCategory as number[])
          : ([paperSection.questionCategory] as number[]);
      }
      backendDrawRules.push(backendDrawRule);
    }
  });
  return {
    drawRules: backendDrawRules.length == 0 ? undefined : backendDrawRules,
    sections: backendSections.length == 0 ? undefined : backendSections,
  };
}

/**
 *
 * @param difficultyCount
 * @returns Record<string,string|number>
 */
function convertDrawRuleDifficulties(difficultyCount: Record<string, string | number>) {
  const randomNumber: any = {};
  for (const [key, difficult] of Object.entries(difficultyCount)) {
    randomNumber[(BackendQuestionDifficulty as any)[key]] = difficult;
  }
  return randomNumber;
}
