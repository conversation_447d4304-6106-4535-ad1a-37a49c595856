import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';

import {
  BackendQuestion,
  BackendQuestionType,
} from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  SetCreateFieldsActionPayload,
  createQustionsAction,
  questionsSliceActions,
  selectCreateQuestions,
} from '@manyun/knowledge-hub.state.questions';
import { shortAnswerTitle as shortAnswerTitleReg } from '@manyun/knowledge-hub.ui.questions-editor';
import { isRepeatArr } from '@manyun/knowledge-hub.ui.questions-editor';

export function useQuestionsCreateFields() {
  const dispatch = useDispatch();
  const fields = useSelector(selectCreateQuestions());

  const setCreateFields = useCallback(
    ({
      mode,
      defaultCategoryCode,
      defaultDifficulty,
      text,
      addQuestion,
      deleteQuestion,
      updateQuestion,
    }: SetCreateFieldsActionPayload) => {
      dispatch(
        questionsSliceActions.setCreateFields({
          mode,
          defaultCategoryCode,
          defaultDifficulty,
          text,
          addQuestion,
          updateQuestion,
          deleteQuestion,
        })
      );
    },
    [dispatch]
  );
  return [setCreateFields, fields] as const;
}

/** 试题创建 hook */
export function useQuestionsCreation() {
  const [setCreateFields, fields] = useQuestionsCreateFields();
  const [submitLoading, setsubmitLoading] = useState(false);
  const dispatch = useDispatch();

  const createQuestions = useCallback(
    (parseData: Omit<BackendQuestion, 'id'>[] | null, callback?: (res: boolean) => void) => {
      setsubmitLoading(true);
      let questions: Omit<BackendQuestion, 'id'>[] = [];
      /**批量录入 */
      if (parseData !== null) {
        questions = parseData.map(data => ({
          ...data,
          categoryCode: data.categoryCode
            ? data.categoryCode
            : (fields.byFile.defaultCategoryCode as string),
          difficulty: data.difficulty
            ? data.difficulty
            : (fields.byFile.defaultDifficulty as number),
        }));
      } else {
        /**手工录入 */
        questions = fields.byHand.ids.map(id => {
          const entity = fields.entities[id];
          return {
            ...entity,
            autoGrade:
              entity.type == BackendQuestionType.SHORT_ANSWER
                ? entity.autoGrade
                : entity.type == BackendQuestionType.ESSAY
                ? false
                : true,
            categoryCode: fields.byHand.defaultCategoryCode as string,
            id: undefined,
          };
        });
      }
      if (questions.length == 0) {
        message.error('请正确录入数据');
        setsubmitLoading(false);
        return;
      }

      if (fields.mode == 'byHand') {
        for (let i = 0; i < questions.length; i++) {
          /**验证填空题的题干个数和答案个数是否一致 */
          const questionEntity = questions[i];
          if (questionEntity.type == 4) {
            const hit = questionEntity.title.match(shortAnswerTitleReg) || [];
            if (hit.length != questionEntity.answers.length) {
              message.error('请检查填空题题干的填空个数与答案个数是否一致！');
              setsubmitLoading(false);
              return;
            }
          }

          /*** 选择题选项不能空  选项不能重复 */
          if ([1, 2].includes(questionEntity.type)) {
            if (questionEntity.options.length == 0) {
              message.error('单/多选题选项不能为空！');
              setsubmitLoading(false);
              return;
            }
            if (isRepeatArr(questionEntity.options)) {
              message.error('单/多选题选项不能重复！');
              setsubmitLoading(false);
              return;
            }
          }

          /** 除简答题 答案不能为空 */
          if ([1, 2, 3, 4].includes(questionEntity.type)) {
            if (questionEntity.answers.length == 0) {
              message.error('答案不能为空！');
              setsubmitLoading(false);
              return;
            }
          }
        }
      }

      dispatch(
        createQustionsAction({
          questions,
          callback: res => {
            setsubmitLoading(false);
            if (callback) callback(res);
          },
        })
      );
    },
    [dispatch, fields]
  );

  return [createQuestions, submitLoading] as const;
}
