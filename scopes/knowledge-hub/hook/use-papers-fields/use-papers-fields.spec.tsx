import { act, renderHook } from '@testing-library/react-hooks';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { usePapersFields } from './use-papers-fields';

it('should loading papers`fields correct', () => {
  const { result } = renderHook(() => usePapersFields(), { wrapper: FakeStore });
  act(() => {
    result.current[0]({ page: 1, pageSize: 10, categoryCodes: [1], name: 'test' });
  });
  expect(result.current[1].fields.name).toBe('test');
  expect(result.current[1].fields.categoryCodes).toMatchObject([1]);
  expect(result.current[1].fields.page).toBe(1);
  expect(result.current[1].fields.pageSize).toBe(10);
});
