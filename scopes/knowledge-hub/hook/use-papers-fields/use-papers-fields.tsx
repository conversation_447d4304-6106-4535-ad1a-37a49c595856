import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BackendPapersGenFun } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  SetFieldsActionPayload,
  papersSliceActions,
  selectAllTypePapers,
} from '@manyun/knowledge-hub.state.papers';

export function usePapersFields(): readonly [
  ({ genFun, page, pageSize, categoryCodes, name, timeRange }: SetFieldsActionPayload) => void,
  {
    readonly loading: boolean;
    readonly fields: SetFieldsActionPayload;
  }
] {
  const dispatch = useDispatch();

  const { fields, loading } = useSelector(selectAllTypePapers());

  const setFields = useCallback(
    (data: SetFieldsActionPayload) => {
      dispatch(papersSliceActions.setFields(data));
    },
    [dispatch]
  );

  return [setFields, { loading, fields }] as const;
}
