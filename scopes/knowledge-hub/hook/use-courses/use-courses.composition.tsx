import React, { useEffect } from 'react';

// import { useCourses } from './use-courses';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';

// export const BasicUseCoursewares = () => (
//   <FakeStore>
//     <Basic />
//   </FakeStore>
// );
// const Basic = () => {
//   const [getCourses, { loading, data }] = useCourses({ variant: 'all' });
//   useEffect(() => {
//     getCourses();
//   }, []);
//   console.info('data:', data);
//   return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
// };

export const BasicUseCoursewares = () => <></>;
