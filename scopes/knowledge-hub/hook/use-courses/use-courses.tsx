import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { CompletedVariant } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import {
  getCourseAction,
  getCoursesAction,
  selectAllCourses,
  selectCourseValues,
  selectCoursesEntities,
  selectMyCourses,
} from '@manyun/knowledge-hub.state.courses';

export type UseCoursesProps = {
  completedVariant: CompletedVariant;
};

export function useAllCourses() {
  const dispatch = useDispatch();

  const getCourses = useCallback(() => {
    dispatch(
      getCoursesAction({
        variant: 'all',
      })
    );
  }, [dispatch]);

  const { loading, ids, total, selectedIds, fields } = useSelector(selectAllCourses());
  const coursewares = useSelector(selectCoursesEntities(ids));

  return [
    getCourses,
    { loading, data: { data: coursewares, total: total }, selectedIds, fields },
  ] as const;
}

export function useMyCourses({ completedVariant }: UseCoursesProps) {
  const dispatch = useDispatch();

  const getCourses = useCallback(() => {
    dispatch(
      getCoursesAction({
        variant: 'my',
        completedVariant,
      })
    );
  }, [dispatch]);

  const { loading, ids, fields } = useSelector(selectMyCourses(completedVariant));
  const coursewares = useSelector(selectCoursesEntities(ids));

  return [getCourses, { loading, data: coursewares, fields }] as const;
}
