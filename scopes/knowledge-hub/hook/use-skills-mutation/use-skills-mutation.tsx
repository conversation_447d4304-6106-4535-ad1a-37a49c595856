import { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';

import type { ServiceD as UseSkillsMutationProps } from '@manyun/knowledge-hub.service.dcexam.mutate-skill';
import { mutateSkillAction } from '@manyun/knowledge-hub.state.skills';

export function useSkillsMutation() {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [result] = useState(false);

  const mutateSkill = useCallback(
    ({
      params: { id, categoryCode, level, name, limitUnit, limitTime },
      callback,
    }: {
      params: UseSkillsMutationProps;
      callback?: () => void;
    }) => {
      setLoading(true);
      dispatch(
        mutateSkillAction({
          id,
          categoryCode,
          level,
          name,
          limitUnit,
          limitTime,
          callback,
        })
      );
      setLoading(false);
    },
    [dispatch]
  );

  return [mutateSkill, { loading, result }] as const;
}
