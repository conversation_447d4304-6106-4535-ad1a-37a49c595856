import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { useSkillsMutation } from './use-skills-mutation';

export const BasicUseSkillsMutation = () => (
  <FakeStore>
    <Basic />
  </FakeStore>
);
const Basic = () => {
  const [mutateSkill, { loading }] = useSkillsMutation();
  useEffect(() => {
    const params = {
      categoryCode: '1',
      name: '1',
      level: 'L1',
    };
    mutateSkill({
      params,
      callback: () => 1,
    });
  }, []);
  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};
