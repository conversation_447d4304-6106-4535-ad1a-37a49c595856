import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { DataNode } from '@manyun/base-ui.ui.tree';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import {
  getCoursesCategoriesAction,
  selectCourseCategory,
} from '@manyun/knowledge-hub.state.courses';

type CategoryWithCountTreeNode = DataNode & {
  key: number;
  count?: number;
};

type CascaderTreeNode = {
  label: React.ReactNode;
  title?: string;
  value: number;
  disabled?: boolean;
  children?: CascaderTreeNode[];
  isLeaf?: boolean;
};

export type UseCoursesCategoryWithoutTreeProps<WithTree> = {
  withTree?: WithTree;
  withCount?: boolean;
};

export type TreeConsumer = 'Tree' | 'Cascader';

type TreeNode<TTreeConsumer extends TreeConsumer> = TTreeConsumer extends 'Tree'
  ? CategoryWithCountTreeNode
  : CascaderTreeNode;

type Tree<WithTree extends boolean, TTreeConsumer extends TreeConsumer> = WithTree extends true
  ? TreeNode<TTreeConsumer>[]
  : undefined;

export type UseCoursesCategoryWithTreeProps<WithTree, TTreeConsumer extends TreeConsumer> = {
  withTree: WithTree;
  treeConsumer: TTreeConsumer;
  treeNodeTransformer?(node: TreeNode<TTreeConsumer>): any;
  withCount?: boolean;
};

export type UseCoursesCategoryProps<
  WithTree extends boolean = false,
  TTreeConsumer extends TreeConsumer = 'Tree'
> = WithTree extends false
  ? UseCoursesCategoryWithoutTreeProps<WithTree>
  : UseCoursesCategoryWithTreeProps<WithTree, TTreeConsumer>;

export type StaticReadPolicy = 'network-only' | 'cache-only';
export type CallbackReadPolicy = (ids: number[]) => StaticReadPolicy;
export type ReadCoursesCategoriesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type ReadCoursesCategoriesArgs = {
  /**
   * @defaultValue `'cache-only'`
   */
  policy?: ReadCoursesCategoriesPolicy;
};

export function useCoursesCategory<
  WithTree extends boolean = false,
  TTreeConsumer extends TreeConsumer = 'Tree'
>(
  props: UseCoursesCategoryProps<WithTree, TTreeConsumer> = {
    withTree: false,
    withCount: false,
  } as UseCoursesCategoryProps<WithTree, TTreeConsumer>
): readonly [
  {
    readonly loading: boolean;
    readonly ids: number[];
    readonly entities: Record<number, Category | undefined>;
    readonly tree: Tree<WithTree, TTreeConsumer>;
  },
  { readCourseCategory: (args?: ReadCoursesCategoriesArgs) => void }
] {
  const dispatch = useDispatch();
  const { entities, ids, loading } = useSelector(selectCourseCategory);

  let tree: TreeNode<TTreeConsumer>[] | undefined;
  if (props.withTree) {
    tree = generateTreeData<TreeNode<TTreeConsumer>, Category>(
      ids.map(id => entities[id]!),
      {
        key: 'id',
        parentKey: 'parentId',
        isRootNode(node) {
          return node.parentId === 0;
        },
        filterNode(_node, children, depth) {
          if (depth === 2) {
            return true;
          }
          return !!children;
        },
        getNode(node, children) {
          let _node: any;

          if (props.treeConsumer === 'Tree') {
            _node = getTreeNode(node, children);
          } else if (props.treeConsumer === 'Cascader') {
            _node = getCascaderTreeNode(node, children);
          } else {
            _node = node;
          }

          if (typeof props.treeNodeTransformer == 'function') {
            return props.treeNodeTransformer(_node);
          } else {
            return _node;
          }
        },
      }
    );
  }

  const readCourseCategory = useCallback(
    ({ policy }: ReadCoursesCategoriesArgs = {}) => {
      dispatch(getCoursesCategoriesAction({ policy, withCount: !!props.withCount }));
    },
    [dispatch, props.withCount]
  );

  return [
    {
      loading,
      ids,
      entities,
      tree: tree as any,
    },
    {
      readCourseCategory,
    },
  ];
}

function getTreeNode(node: Category, children: any[] | null): CategoryWithCountTreeNode {
  const baseNode: CategoryWithCountTreeNode = {
    key: node.id,
    title: node.name,
    checkable: !children,
    isLeaf: !children,
    count: node.count,
  };

  if (children) {
    baseNode.children = children;
  }

  return baseNode;
}

function getCascaderTreeNode(node: Category, children: any[] | null): CascaderTreeNode {
  const baseNode: CascaderTreeNode = {
    label: node.name,
    value: node.id,
    isLeaf: !children,
  };

  if (children) {
    baseNode.children = children;
  }

  return baseNode;
}
