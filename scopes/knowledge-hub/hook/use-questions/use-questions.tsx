import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BackendQuestion } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';
import {
  getQuestionsAction,
  selectAllTypeQuestions,
  selectQuestionsEntities,
} from '@manyun/knowledge-hub.state.questions';
import { QuestionsFields } from '@manyun/knowledge-hub.state.questions';

export function useQuestions(): readonly [
  () => void,
  {
    readonly loading: boolean;
    readonly data: {
      readonly data: BackendQuestion[];
      readonly total: number;
    };
    readonly fields: QuestionsFields;
    readonly selectedIds: string[];
  }
] {
  const dispatch = useDispatch();

  const getQuestions = useCallback(() => {
    dispatch(getQuestionsAction());
  }, [dispatch]);

  const { loading, ids, total, selectedIds, fields } = useSelector(selectAllTypeQuestions());
  const questions = useSelector(selectQuestionsEntities(ids));
  return [
    getQuestions,
    { loading, data: { data: questions, total: total as number }, fields, selectedIds },
  ] as const;
}
