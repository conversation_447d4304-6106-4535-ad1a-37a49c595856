import { useEffect, useState } from 'react';

import { BackendPapersGenFunMapper } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';

export function usePapersGenFunc<T extends boolean = false>(optionsStyle?: T) {
  const [options, setOptions] = useState();

  useEffect(() => {
    const genFunOptions = optionsStyle
      ? Object.keys(BackendPapersGenFunMapper).reduce((options, key) => {
          options.push({
            label: (BackendPapersGenFunMapper as any)[key],
            value: key,
          });
          return options;
        }, [] as { label: string; value: string }[])
      : (BackendPapersGenFunMapper as Record<string, string>);
    setOptions(genFunOptions as any);
  }, []);

  return [options] as const;
}
