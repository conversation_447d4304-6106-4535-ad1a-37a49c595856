import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  coursewaresSliceActions,
  selectCoursewareSelectedIds,
} from '@manyun/knowledge-hub.state.coursewares';

export function useCoursewaresSelected() {
  const dispatch = useDispatch();
  //todo 返回ids格式校验失败 A: 这里的ids是组装过的字符串作为唯一标识，是string类型
  const selectedIds = useSelector(selectCoursewareSelectedIds());

  const setSeletedIds = useCallback(
    ({ selectedIds }: { selectedIds: string[] }) => {
      dispatch(coursewaresSliceActions.setSelectedIds({ selectedIds }));
    },
    [dispatch]
  );

  return [setSeletedIds, selectedIds] as const;
}
