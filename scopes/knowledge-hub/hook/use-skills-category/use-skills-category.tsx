import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import {
  getSkillCategoriesAction,
  selectSkillsCategory,
  skillsSliceActions,
} from '@manyun/knowledge-hub.state.skills';

type CascaderTreeNode = {
  label: React.ReactNode;
  /** Customize hover title */
  title?: string;
  value: number;
  disabled?: boolean;
  children?: CascaderTreeNode[];
  isLeaf?: boolean;
};

type StaticReadPolicy = 'network-only' | 'cache-only';
type CallbackReadPolicy = (ids: number[]) => StaticReadPolicy;
export type ReadSkillsCategoriesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type ReadOptions = {
  withCount?: boolean;
  policy?: StaticReadPolicy | CallbackReadPolicy;
};

export type UseSkillsCategoryProps = {
  withTree?: boolean;
  withCount?: boolean;
};

export function useSkillsCategory({
  withTree = false,
  withCount = false,
}: UseSkillsCategoryProps = {}) {
  const dispatch = useDispatch();
  const { ids, entities, loading } = useSelector(selectSkillsCategory());

  const readSkillCategory = useCallback(
    (options?: ReadOptions) => {
      dispatch(getSkillCategoriesAction({ policy: options?.policy, withCount: withCount }));
    },
    [dispatch, ids]
  );

  return [
    {
      loading,
      entities,
      ids,
      tree: withTree
        ? generateTreeData<CascaderTreeNode, Category>(
            ids.map(id => entities[id]),
            {
              key: 'id',
              parentKey: 'parentId',
              isRootNode(node) {
                return node.parentId === 0;
              },
              filterNode(_node, children, level) {
                if (level === 2) {
                  return true;
                }
                return !!children;
              },
              getNode(node, children) {
                return getCascaderTreeNode(node, children);
              },
            }
          )
        : null,
    },
    { readSkillCategory },
  ] as const;
}

function getCascaderTreeNode(
  node: Category,
  children: CascaderTreeNode[] | null
): CascaderTreeNode {
  const baseNode: CascaderTreeNode = {
    label: node.name,
    value: node.id,
    isLeaf: !children,
  };

  if (children) {
    baseNode.children = children;
  }

  return baseNode;
}
