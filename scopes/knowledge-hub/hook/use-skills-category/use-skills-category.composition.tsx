import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { useSkillsCategory } from './use-skills-category';

const Basic = () => {
  const [{ loading }, { readSkillCategory }] = useSkillsCategory({ withTree: false });
  useEffect(() => {
    readSkillCategory();
  }, []);

  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};

export const BasicUseSkillsCategory = () => {
  return (
    <FakeStore>
      <Basic />
    </FakeStore>
  );
};
