import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { CompletedVariant, Variant } from '@manyun/knowledge-hub.service.dcexam.fetch-courses';
import {
  SetFieldsActionPayload,
  coursesSliceActions,
  selectAllCourses,
  selectMyCourses,
} from '@manyun/knowledge-hub.state.courses';

export type UseCoursesFieldsProps = {
  variant: Variant;
  completedVariant?: CompletedVariant;
};

export function useCoursesFields({ variant, completedVariant }: UseCoursesFieldsProps) {
  const dispatch = useDispatch();

  const { fields } =
    variant === 'all'
      ? useSelector(selectAllCourses())
      : useSelector(selectMyCourses(completedVariant!));

  const setFields = useCallback(
    ({
      name,
      categoryCode,
      enabled,
      required,
      timeRange,
      page,
      pageSize,
      involvedState,
    }: SetFieldsActionPayload) => {
      dispatch(
        coursesSliceActions.setFields({
          variant,
          completedVariant,
          name,
          categoryCode,
          enabled,
          required,
          timeRange,
          page,
          pageSize,
          involvedState,
        })
      );
    },
    [dispatch]
  );
  return [setFields, fields] as const;
}
