/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { getPaperCategoriesAction, selectPaperCategory } from '@manyun/knowledge-hub.state.papers';

type CascaderTreeNode = {
  label: React.ReactNode;
  /** Customize hover title */
  title?: string;
  value: number;
  disabled?: boolean;
  children?: CascaderTreeNode[];
  isLeaf?: boolean;
};

type StaticReadPolicy = 'network-only' | 'cache-only';
type CallbackReadPolicy = (ids: number[]) => StaticReadPolicy;
export type ReadPapersCategoriesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type ReadOptions = {
  policy?: StaticReadPolicy | CallbackReadPolicy;
  withCount?: boolean;
};

export type ReadCallback = (data: Category[]) => void;

export function usePapersCategory(withTree = false) {
  const dispatch = useDispatch();
  const { ids, entities, loading } = useSelector(selectPaperCategory());

  const readPaperCategory = useCallback(
    (options?: ReadOptions) => {
      dispatch(
        getPaperCategoriesAction({
          withCount: options?.withCount || false,
          policy: options?.policy,
        })
      );
    },
    [dispatch, ids]
  );

  return [
    {
      loading,
      ids,
      entities,
      tree: withTree
        ? generateTreeData<CascaderTreeNode, Category>(
            ids.map(id => entities[id]),
            {
              key: 'id',
              parentKey: 'parentId',
              isRootNode(node) {
                return node.parentId === 0;
              },
              filterNode(_node, children, level) {
                if (level === 2) {
                  return true;
                }
                return !!children;
              },
              getNode(node, children, level) {
                const baseNode: CascaderTreeNode = {
                  label: node.name,
                  value: node.id,
                  isLeaf: level == 2,
                };
                if (children) {
                  baseNode.children = children;
                }

                return baseNode;
              },
            }
          )
        : undefined,
    },
    {
      readPaperCategory,
    },
  ] as const;
}
