import { act, renderHook } from '@testing-library/react-hooks';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { destroyMock, getMock } from '@manyun/service.request';

import { usePapersCategory } from './use-papers-category';

beforeAll(() => {
  registerWebMocks(getMock('web'));
});

afterAll(() => {
  destroyMock();
});

it('should loading paper`s category', async () => {
  const { result, waitForNextUpdate } = renderHook(() => usePapersCategory(), {
    wrapper: FakeStore,
  });
  act(() => {
    result.current[1].readPaperCategory();
  });

  expect(result.current[0].loading).toBe(true);
  await waitForNextUpdate();

  expect(result.current[0].loading).toBe(false);
});
