---
description: '试卷分类 React Hook'
labels: ['hook', 'papers-category']
---

import { usePapersCategory } from './use-papers-category';

## A React Hook to increment a count

Increments the state of `count` by 1 each time `increment` is called

### Component usage

In this example clicking the button calls `increment` which increments the `count` by 1

```js
import { usePapersCategory } from './use-papers-category';

const { count, increment } = usePapersCategory();

<h1>The count is {count}</h1>
<button onClick={increment}>increment</button>
```
