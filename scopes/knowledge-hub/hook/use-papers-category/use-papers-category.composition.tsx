import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { usePapersCategory } from './use-papers-category';

const Basic = () => {
  const [{ loading, tree }, { readPaperCategory }] = usePapersCategory(true);
  useEffect(() => {
    readPaperCategory();
  }, []);
  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};

export const BasicQuestionCategory = () => {
  return (
    <FakeStore>
      <Basic />
    </FakeStore>
  );
};
