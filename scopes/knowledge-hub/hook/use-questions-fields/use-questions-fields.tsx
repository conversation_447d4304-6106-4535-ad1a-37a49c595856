import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BackendPapersGenFun } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  questionsSliceActions,
  selectAllTypeQuestions,
} from '@manyun/knowledge-hub.state.questions';
import type { SetFieldsActionPayload } from '@manyun/knowledge-hub.state.questions';

export function useQuestionsFields(): readonly [
  ({ page, pageSize, title, categoryCode, type, difficulty }: SetFieldsActionPayload) => void,
  SetFieldsActionPayload
] {
  const dispatch = useDispatch();

  const { fields } = useSelector(selectAllTypeQuestions());

  const setFields = useCallback(
    (data: SetFieldsActionPayload) => {
      dispatch(questionsSliceActions.setFields({ ...data }));
    },
    [dispatch]
  );

  return [setFields, fields] as const;
}
