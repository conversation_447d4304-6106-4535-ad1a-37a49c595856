---
description: '试题管理-试题类型 React hook'
labels: ['hook', 'use-question-type']
---

import { useQuestionType } from './use-questions-type';

## A React Hook to increment a count

Increments the state of `count` by 1 each time `increment` is called

### Component usage

In this example clicking the button calls `increment` which increments the `count` by 1

```js
import { useQuestionType } from './use-questions-type';

const [typeOptions] = useQuestionType();
```
