/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-15
 *
 * @packageDocumentation
 */
import { QuestionTypeTextMapper } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export function useQuestionType<T extends boolean = false>(optionsStyle?: T) {
  const typeOptions = optionsStyle
    ? Object.keys(QuestionTypeTextMapper).reduce((options, key) => {
        options.push({
          label: (QuestionTypeTextMapper as any)[key],
          value: Number(key),
        });
        return options;
      }, [] as { label: string; value: number }[])
    : (QuestionTypeTextMapper as Record<number, string>);
  return [typeOptions] as const;
}
