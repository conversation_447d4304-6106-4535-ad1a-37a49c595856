import { act, renderHook } from '@testing-library/react-hooks';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { destroyMock, getMock } from '@manyun/service.request';

import { useQuestionsCategory } from './use-questions-category';

beforeAll(() => {
  registerWebMocks(getMock('web'));
});

afterAll(() => {
  destroyMock();
});

test('should loading useQuestionsCategory', async () => {
  const { result, waitForNextUpdate } = renderHook(() => useQuestionsCategory(), {
    wrapper: FakeStore,
  });
  act(() => {
    result.current[1].readQuestionCategory();
  });

  expect(result.current[0].loading).toBe(true);
  await waitForNextUpdate();

  expect(result.current[0].loading).toBe(false);
});
