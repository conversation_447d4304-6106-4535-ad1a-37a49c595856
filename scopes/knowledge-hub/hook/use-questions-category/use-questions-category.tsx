/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { useQuestions } from '@manyun/knowledge-hub.hook.use-questions';
import { deleteKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.delete-knowledge-hub-category';
import {
  BackendCategoryType,
  Category,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { mutateKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category';
import {
  getQuestionsCategoryAction,
  questionsSliceActions,
  selectQuestionCategory,
} from '@manyun/knowledge-hub.state.questions';

export type CategoryTreeNode = Category & {
  title: string;
  label: string;
  value: number;
  key: string;
  isLeaf?: boolean;
  isDraft?: boolean;
  children?: CategoryTreeNode[];
};

type StaticReadPolicy = 'network-only' | 'cache-only';
type CallbackReadPolicy = (ids: number[]) => StaticReadPolicy;
export type ReadQuestionsCategoriesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type ReadOptions = {
  withCount?: boolean;
  policy?: StaticReadPolicy | CallbackReadPolicy;
};

export type ReadCallback = (data: Category[]) => void;

const defaultReadOptions: Pick<Required<ReadOptions>, 'withCount' | 'policy'> = {
  withCount: false,
  policy: 'network-only',
};

function getStaticReadPolicy(
  policy: StaticReadPolicy | CallbackReadPolicy = defaultReadOptions.policy,
  ids: number[]
): StaticReadPolicy {
  if (typeof policy == 'function') {
    if (ids.length <= 0) {
      return 'network-only';
    }
    return policy(ids);
  }
  return policy;
}

export function useQuestionsCategory(withTree = false) {
  const dispatch = useDispatch();
  const { ids, entities, expandedKeys, loading } = useSelector(selectQuestionCategory());
  const [getQuestions] = useQuestions();

  const readQuestionCategory = useCallback(
    (options?: boolean | ReadOptions) => {
      let opts: ReadOptions = defaultReadOptions;
      if (typeof options == 'boolean') {
        opts.withCount = options;
      } else if (options && typeof options == 'object') {
        opts = options;
      }
      const withCount =
        typeof opts.withCount == 'boolean' ? opts.withCount : defaultReadOptions.withCount;
      const staticPolicy = getStaticReadPolicy(opts.policy, ids);
      dispatch(getQuestionsCategoryAction({ withCount, policy: staticPolicy }));
    },
    [dispatch, ids]
  );

  const createQuestionCategory = async ({
    id,
    name,
    parentId,
  }: {
    id: number;
    name: string;
    parentId: number;
  }) => {
    const { data, error } = await mutateKnowledgeHubCategoryWeb({
      name: name,
      parentId: parentId,
      type: BackendCategoryType.QUESTION,
    });
    if (error) {
      message.error(error.message);
      dispatch(questionsSliceActions.setCategoriesDelete({ id: id }));
      return;
    }
    message.success('新建分类成功！');
    getQuestions();
    dispatch(
      questionsSliceActions.setCategoriesUpdate({
        id: id,
        updateCategory: { ...data, code: data.id },
      })
    );
  };

  const updateQuestionCategory = async ({
    id,
    name,
    callback,
  }: {
    id: number;
    name: string;
    callback?: (res: boolean) => void;
  }) => {
    const { data, error } = await mutateKnowledgeHubCategoryWeb({
      id,
      name,
    });

    if (error) {
      message.error(error.message);
      callback && callback(false);
      return;
    }
    callback && callback(true);
    message.success('修改分类成功！');
    getQuestions();
    dispatch(
      questionsSliceActions.setCategoriesUpdate({
        id: id,
        updateCategory: { ...data, code: data.id },
      })
    );
  };

  const deleteQuestionCategory = async (id: number, callback?: (res: boolean) => void) => {
    const { data, error } = await deleteKnowledgeHubCategoryWeb({ id });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除分类成功');
    if (callback) {
      callback(data);
    }
  };

  const setCategoriesExpandedKeys = (keys: string[] | string) => {
    dispatch(questionsSliceActions.setCategoriesExpandedKeys({ expandedKeys: keys }));
  };

  return [
    {
      loading,
      ids,
      expandedKeys,
      entities,
      tree: withTree
        ? generateTreeData<CategoryTreeNode, Category>(
            ids.map(id => entities[id]),
            {
              key: 'id',
              parentKey: 'parentId',
              isRootNode(node) {
                return node.parentId === 0;
              },
              getNode(node, children, level) {
                const baseNode: CategoryTreeNode = {
                  ...node,
                  title: node.name,
                  label: node.name!,
                  value: node.id!,
                  key: node.id.toString(),
                  isLeaf: level === 3 || !children,
                  count: node.count || 0,
                  level: level,
                  isDraft: node.id && node.id < 0 ? true : false,
                  children: children === null ? undefined : children,
                };
                return baseNode;
              },
            }
          )
        : null,
    },
    {
      createQuestionCategory,
      readQuestionCategory,
      updateQuestionCategory,
      deleteQuestionCategory,
      setCategoriesExpandedKeys,
    },
  ] as const;
}
