// import '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category/fetch-knowledge-hub-category.mock';
// import '@manyun/knowledge-hub.service.dcexam.fetch-question/dist/fetch-question.mock';
// import { fetchQuestionWeb } from '@manyun/knowledge-hub.service.dcexam.fetch-question';
import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import {
  BackendCategoryType,
  fetchKnowledgeHubCategoryWeb,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';

import { useQuestionsCategory } from './use-questions-category';

const Basic = () => {
  const [{ loading, tree }, { readQuestionCategory }] = useQuestionsCategory(true);
  useEffect(() => {
    readQuestionCategory();
  }, []);
  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};

export const BasicQuestionCategory = () => {
  return (
    <FakeStore>
      <Basic />
    </FakeStore>
  );
};
