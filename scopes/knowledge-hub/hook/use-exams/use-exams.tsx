import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// 注意：不要删除下面这样 import，否则 build/tag 时会产生 TS Error
import { BackendExam } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  ExamTypes,
  getExamsAction,
  selectExamsEntities,
  selectTypedExams,
} from '@manyun/knowledge-hub.state.exams';

export { ExamTypes };

export function useExams(examType: ExamTypes) {
  const dispatch = useDispatch();
  const getExams = useCallback(() => {
    dispatch(getExamsAction({ examType }));
  }, [dispatch]);

  const { loading, ids, total, fields } = useSelector(selectTypedExams(examType));
  const exams = useSelector(selectExamsEntities(ids));

  return [getExams, { loading, data: { data: exams, total: total }, fields }] as const;
}
