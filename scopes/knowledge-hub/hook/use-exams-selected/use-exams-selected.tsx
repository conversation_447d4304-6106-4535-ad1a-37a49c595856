import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { ExamTypes, examsSliceActions, selectTypedExams } from '@manyun/knowledge-hub.state.exams';

export function useExamsSelected() {
  const dispatch = useDispatch();
  const { selectedIds } = useSelector(selectTypedExams(ExamTypes.AllExam));

  const setExamsSeletedIds = useCallback(
    (selectedIds: number[]) => {
      dispatch(examsSliceActions.setExamsSeletedIds({ selectedIds }));
    },
    [dispatch]
  );

  return [setExamsSeletedIds, { selectedIds }] as const;
}
