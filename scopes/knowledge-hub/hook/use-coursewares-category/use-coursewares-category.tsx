import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { message } from '@manyun/base-ui.ui.message';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import { deleteKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.delete-knowledge-hub-category';
import type {
  BackendCategory,
  Category,
} from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { BackendCategoryType } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { mutateKnowledgeHubCategoryWeb } from '@manyun/knowledge-hub.service.dcexam.mutate-knowledge-hub-category';
import type { Variant } from '@manyun/knowledge-hub.state.coursewares';
import {
  coursewaresSliceActions,
  getCoursewaresCategoriesAction,
  selectCoursewareCategory,
} from '@manyun/knowledge-hub.state.coursewares';

export type CategoryTreeNode = Category & {
  title: string;
  label: string;
  value: number;
  key: string;
  isLeaf: boolean;
  level: number;
  isDraft: boolean;
  children?: CategoryTreeNode[];
};

export type StaticReadPolicy = 'network-only' | 'cache-only';
export type CallbackReadPolicy = (ids: number[]) => StaticReadPolicy;
export type ReadCoursewaresCategoriesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type ReadCoursewaresCategoriesArgs = {
  /**
   * @defaultValue `'cache-only'`
   */
  policy?: ReadCoursewaresCategoriesPolicy;
};

export function useCoursewaresCategory({
  variant,
  withTree = false,
  withCount = false,
  isAuth = false,
}: {
  variant: Variant;
  withTree?: boolean;
  withCount?: boolean;
  isAuth?: boolean;
}) {
  const dispatch = useDispatch();
  const { ids, entities, loading } = useSelector(selectCoursewareCategory(variant));

  const tree = withTree
    ? generateTreeData<CategoryTreeNode, Category>(
        ids.map(id => entities[id]!),
        {
          key: 'id',
          parentKey: 'parentId',
          isRootNode(node) {
            return node.parentId === 0;
          },
          getNode(node, children, depth) {
            const baseNode: CategoryTreeNode = {
              ...node,
              key: String(node.id),
              label: node.name,
              title: node.name,
              value: node.id,
              isLeaf: depth === 3 || !children,
              level: depth,
              isDraft: false,
            };
            if (children) {
              baseNode.children = children;
            }

            return baseNode;
          },
        }
      )
    : [];

  const readCoursewareCategory = useCallback(
    ({ policy }: ReadCoursewaresCategoriesArgs = {}) => {
      dispatch(getCoursewaresCategoriesAction({ variant, policy, withCount, isAuth }));
    },
    [dispatch, variant, withCount, isAuth]
  );

  const createCoursewareCategory = async (
    { name, parentId }: { name: string; parentId: number },
    callback?: (category: BackendCategory | null) => void
  ) => {
    const { data, error } = await mutateKnowledgeHubCategoryWeb({
      name: name,
      parentId: parentId,
      type: BackendCategoryType.COURSEWARE,
    });
    if (error) {
      message.error(error.message);
      if (callback) {
        callback(null);
      }
      return;
    }
    if (callback) {
      callback(data);
    }
  };

  const updateCoursewareCategory = async (
    {
      id,
      name,
    }: {
      id: number;
      name: string;
    },
    callback: (category: BackendCategory | null) => void
  ) => {
    const { data, error } = await mutateKnowledgeHubCategoryWeb({
      id,
      name,
      type: BackendCategoryType.COURSEWARE,
    });
    if (error) {
      message.error(error.message);
      if (callback) {
        callback(null);
      }
      return;
    }
    if (callback) {
      callback(data);
    }
  };

  const deleteCoursewareCategory = async (id: number, callback: (id: number | null) => void) => {
    const { error } = await deleteKnowledgeHubCategoryWeb({ id });
    if (error) {
      message.error(error.message);
      if (callback) {
        callback(null);
      }
      return;
    }
    if (callback) {
      callback(id);
    }
  };

  return [
    {
      ids,
      loading,
      entities,
      tree: tree,
    },
    {
      createCoursewareCategory,
      readCoursewareCategory,
      updateCoursewareCategory,
      deleteCoursewareCategory,
    },
  ] as const;
}

export function useQuestionsCategoryEdit() {
  const dispatch = useDispatch();
  const addCategory = useCallback(
    (category: Category) => {
      dispatch(coursewaresSliceActions.setCategoriesAdd({ category }));
    },
    [dispatch]
  );

  const deleteCategory = useCallback(
    (id: number) => {
      dispatch(coursewaresSliceActions.setCategoriesDelete({ id }));
    },
    [dispatch]
  );

  const updateCategory = useCallback(
    ({ id, name }: { id: number; name: string }) => {
      dispatch(coursewaresSliceActions.setCategoriesUpdate({ id, name }));
    },
    [dispatch]
  );

  return [addCategory, deleteCategory, updateCategory] as const;
}
