import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';

import { useCoursewaresCategory } from './use-coursewares-category';

const Basic = () => {
  const [{ loading }, { readCoursewareCategory }] = useCoursewaresCategory({
    variant: Variant.ALL,
  });
  useEffect(() => {
    readCoursewareCategory();
  }, []);
  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};

export const BasicUseCoursewaresCategory = () => (
  <FakeStore>
    <Basic />
  </FakeStore>
);
