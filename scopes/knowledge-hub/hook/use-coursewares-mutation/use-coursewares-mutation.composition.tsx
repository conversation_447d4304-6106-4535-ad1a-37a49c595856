import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.mutate-coursewares';

import { useCoursewaresMutation } from './use-coursewares-mutation';

export const BasicUseCoursewaresMutation = () => (
  <FakeStore>
    <Basic />
  </FakeStore>
);
const Basic = () => {
  const [mutateCoursewares, { loading }] = useCoursewaresMutation({
    categoryCode: '1',
    id: '1',
    // files: [],
  });
  useEffect(() => {
    mutateCoursewares();
  }, []);
  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};
