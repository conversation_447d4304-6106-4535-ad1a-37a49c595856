import React, { useCallback, useState } from 'react';
import { useDispatch } from 'react-redux';

import { mutateCoursewaresAction } from '@manyun/knowledge-hub.state.coursewares';

export type UseCoursewaresMutationProps = {
  id?: string;
  categoryCode: string;
};

export function useCoursewaresMutation({ id, categoryCode }: UseCoursewaresMutationProps) {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState(false);

  const mutateSkill = useCallback(() => {
    setLoading(true);
    dispatch(
      mutateCoursewaresAction({
        categoryCode,
        id,
        callback: result => {
          if (result) {
            setResult(result);
          }
        },
      })
    );
    setLoading(false);
  }, [dispatch, id, categoryCode]);

  return [mutateSkill, { loading, result }] as const;
}
