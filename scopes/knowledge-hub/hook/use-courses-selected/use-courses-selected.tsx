import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { coursesSliceActions, selectAllCourses } from '@manyun/knowledge-hub.state.courses';

export function useCoursesSelected() {
  const dispatch = useDispatch();
  const { selectedIds } = useSelector(selectAllCourses());

  const setSeletedIds = useCallback(
    ({ selectedIds }: { selectedIds: number[] }) => {
      dispatch(coursesSliceActions.setSelectedIds({ selectedIds }));
    },
    [dispatch]
  );

  return [setSeletedIds, selectedIds] as const;
}
