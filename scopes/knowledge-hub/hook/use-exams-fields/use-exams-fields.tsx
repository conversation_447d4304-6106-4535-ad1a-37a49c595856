import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

// 注意：不要删除以下 type imports
// bit build/tag 时依赖这些 types
import type { BackendExamStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  ExamTypes,
  SetExamsFieldsActionPayload,
  examsSliceActions,
  selectTypedExams,
} from '@manyun/knowledge-hub.state.exams';

export function useExamsFields(examsType: ExamTypes) {
  const dispatch = useDispatch();

  const { fields } = useSelector(selectTypedExams(examsType));

  const setFields = useCallback(
    (data: Omit<SetExamsFieldsActionPayload, 'examsType'>) => {
      dispatch(
        examsSliceActions.setExamsFields({
          examsType,
          ...data,
        })
      );
    },
    [dispatch]
  );

  return [setFields, { ...fields }] as const;
}
