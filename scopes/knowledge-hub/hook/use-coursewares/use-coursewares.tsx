import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  Variant,
  getCoursewaresAction,
  selectCoursewares,
  selectCoursewaresEntities,
} from '@manyun/knowledge-hub.state.coursewares';

export type UseCoursewaresProps = {
  variant: Variant;
  init?: boolean;
};

export function useCoursewares({ variant, init = false }: UseCoursewaresProps) {
  const dispatch = useDispatch();

  const getCoursewares = useCallback(() => {
    dispatch(
      getCoursewaresAction({
        variant,
      })
    );
  }, [dispatch, variant, init]);

  const { loading, ids } = useSelector(selectCoursewares(variant));
  const coursewares = useSelector(selectCoursewaresEntities(ids));

  return [getCoursewares, { loading, data: coursewares }] as const;
}
