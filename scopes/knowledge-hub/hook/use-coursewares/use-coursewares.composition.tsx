import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-coursewares';
import { Variant } from '@manyun/knowledge-hub.state.coursewares';

import { useCoursewares } from './use-coursewares';

export const BasicUseCoursewares = () => (
  <FakeStore>
    <Basic />
  </FakeStore>
);
const Basic = () => {
  const [getCoursewares, { loading, data }] = useCoursewares({ variant: Variant.ALL });
  useEffect(() => {
    getCoursewares();
  }, []);
  return <div>{loading ? '数据加载中...' : '数据已加载完成'}</div>;
};
