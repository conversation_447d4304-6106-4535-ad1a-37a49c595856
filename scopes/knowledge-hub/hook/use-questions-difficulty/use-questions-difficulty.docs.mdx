---
description: '试题管理-试题难度 React hook'
labels: ['hook', 'use-questions-difficulty']
---

import { useQuestionsDifficulty } from './use-questions-difficulty';

### Component usage

#### use in Select

```js
import { useQuestionsDifficulty } from './use-questions-difficulty';

const [difficultyOptions] = useQuestionsDifficulty(true);

<Select>
  {difficultyOptions.map(difficulty => (
  <Option key={difficulty.value}>{difficulty.label}</Option>
  )))}
</Select>;
```

#### use in Label/Text

```js
import { useQuestionsDifficulty } from './use-questions-difficulty';

const [difficultyOptions] = useQuestionsDifficulty();

<div>{difficultyOptions['1']}</div>;
```
