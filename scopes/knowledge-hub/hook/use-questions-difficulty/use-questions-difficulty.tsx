/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-11
 *
 * @packageDocumentation
 */
import { QuestionDifficultyTextMapper } from '@manyun/knowledge-hub.service.dcexam.fetch-questions';

export function useQuestionsDifficulty<T extends boolean = false>(optionsStyle?: T) {
  const difficultyOptions = optionsStyle
    ? Object.keys(QuestionDifficultyTextMapper).reduce((options, key) => {
        options.push({
          label: (QuestionDifficultyTextMapper as any)[key],
          value: Number(key),
        });
        return options;
      }, [] as { label: string; value: string | number }[])
    : (QuestionDifficultyTextMapper as Record<number, string>);
  return [difficultyOptions] as const;
}
