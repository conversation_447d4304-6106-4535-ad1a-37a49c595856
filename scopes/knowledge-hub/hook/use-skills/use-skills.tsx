import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { ServiceQ as searchSkillValues } from '@manyun/knowledge-hub.service.dcexam.fetch-skills';
import {
  getSkillsAction,
  selectSkills,
  selectSkillsEntities,
  skillsSliceActions,
} from '@manyun/knowledge-hub.state.skills';

export function useSkills() {
  const dispatch = useDispatch();
  const getSkills = useCallback(() => {
    dispatch(getSkillsAction());
  }, [dispatch]);
  const { loading, ids, fields } = useSelector(selectSkills());
  const skills = useSelector(selectSkillsEntities(ids));
  return [getSkills, { loading, ids, data: skills, fields }] as const;
}

export function useSkillsFileds() {
  const dispatch = useDispatch();
  const setFields = useCallback(
    (data: searchSkillValues) => {
      dispatch(skillsSliceActions.setFields(data));
    },
    [dispatch]
  );
  return [setFields];
}
