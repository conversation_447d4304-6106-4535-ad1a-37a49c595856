import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import {
  Variant,
  coursewaresSliceActions,
  selectCoursewares,
} from '@manyun/knowledge-hub.state.coursewares';

export type CoursewaresFields = {
  name?: string;
  categoryCode?: number;
};

export function useCoursewaresFields(variant: Variant) {
  const dispatch = useDispatch();

  const {
    fields: { name, categoryCode },
  } = useSelector(selectCoursewares(variant));

  const setFields = useCallback(
    ({ name, categoryCode }: CoursewaresFields) => {
      dispatch(
        coursewaresSliceActions.setFields({
          variant,
          name,
          categoryCode,
        })
      );
    },
    [dispatch]
  );
  return [setFields, { name, categoryCode }] as const;
}
