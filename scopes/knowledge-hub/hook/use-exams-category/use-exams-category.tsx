/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-10-15
 *
 * @packageDocumentation
 */
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { DataNode } from '@manyun/base-ui.ui.tree';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';

import type { Category } from '@manyun/knowledge-hub.service.dcexam.fetch-knowledge-hub-category';
import { getExamsCategoriesAction, selectExamsCategory } from '@manyun/knowledge-hub.state.exams';

type CategoryWithCountTreeNode = DataNode & {
  key: number;
  count?: number;
};

type CascaderTreeNode = {
  label: React.ReactNode;
  /** Customize hover title */
  title?: string;
  value: number;
  disabled?: boolean;
  children?: CascaderTreeNode[];
  isLeaf?: boolean;
};

export type UseExamsCategoryWithoutTreeProps<WithTree> = {
  /** 是否同时返回 `tree` */
  withTree?: WithTree;
  /** 是否返回节点 `children` 的计数 */
  withCount?: boolean;
};

export type TreeConsumer = 'Tree' | 'Cascader';

type TreeNode<TTreeConsumer extends TreeConsumer> = TTreeConsumer extends 'Tree'
  ? CategoryWithCountTreeNode
  : CascaderTreeNode;

type Tree<WithTree extends boolean, TTreeConsumer extends TreeConsumer> = WithTree extends true
  ? TreeNode<TTreeConsumer>[]
  : undefined;

export type UseExamsCategoryWithTreeProps<WithTree, TTreeConsumer extends TreeConsumer> = {
  /** 是否同时返回 `tree` */
  withTree: WithTree;
  /** `tree` 数据消费者 */
  treeConsumer: TTreeConsumer;
  /** 允许使用者自定义 `tree node` 的数据结构 */
  treeNodeTransformer?(node: TreeNode<TTreeConsumer>): any;
  /** 是否返回节点 `children` 的计数 */
  withCount?: boolean;
};

export type StaticReadPolicy = 'network-only' | 'cache-only';
export type CallbackReadPolicy = (ids: number[]) => StaticReadPolicy;
export type ReadExamsCategoriesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type ReadExamsCategoriesArgs = {
  /**
   * @defaultValue `'cache-only'`
   */
  policy?: ReadExamsCategoriesPolicy;
};

export type UseExamsCategoryProps<
  WithTree extends boolean = false,
  TTreeConsumer extends TreeConsumer = 'Tree'
> = WithTree extends false
  ? UseExamsCategoryWithoutTreeProps<WithTree>
  : UseExamsCategoryWithTreeProps<WithTree, TTreeConsumer>;

export function useExamsCategory<
  WithTree extends boolean = false,
  TTreeConsumer extends TreeConsumer = 'Tree'
>(
  props: UseExamsCategoryProps<WithTree, TTreeConsumer> = {
    withTree: false,
    withCount: false,
  } as UseExamsCategoryProps<WithTree, TTreeConsumer>
): readonly [
  {
    readonly loading: boolean;
    readonly ids: number[];
    readonly entities: Record<number, Category | undefined>;
    readonly tree: Tree<WithTree, TTreeConsumer>;
    readonly checkedNodes: any[];
  },
  { readExamCategory: (args?: ReadExamsCategoriesArgs) => void }
] {
  const dispatch = useDispatch();
  const { loading, ids, entities } = useSelector(selectExamsCategory);
  let _checkedNodes = [] as any;
  let tree: TreeNode<TTreeConsumer>[] | undefined;
  if (props.withTree) {
    tree = generateTreeData<TreeNode<TTreeConsumer>, Category>(
      ids.map(id => entities[id]!),
      {
        key: 'id',
        parentKey: 'parentId',
        isRootNode(node) {
          return node.parentId === 0;
        },
        filterNode(_node, children, depth) {
          if (depth === 2) {
            return true;
          }
          return !!children;
        },
        getNode(node, children) {
          let _node: any;

          if (props.treeConsumer === 'Tree') {
            _node = getTreeNode(node, children);
          } else if (props.treeConsumer === 'Cascader') {
            _node = getCascaderTreeNode(node, children);
          } else {
            _node = node;
          }

          if (typeof props.treeNodeTransformer == 'function') {
            let __node = props.treeNodeTransformer(_node);
            if (__node.disabled) {
              _checkedNodes.push(__node.key);
            }
            return __node;
          } else {
            return _node;
          }
        },
      }
    );
  }

  const readExamCategory = React.useCallback(
    ({ policy }: ReadExamsCategoriesArgs = {}) => {
      dispatch(getExamsCategoriesAction({ policy, withCount: !!props.withCount }));
    },
    [dispatch, props.withCount]
  );

  return [
    {
      loading,
      ids,
      entities,
      tree: tree as any,
      checkedNodes: _checkedNodes as any,
    },
    {
      readExamCategory,
    },
  ];
}

function getTreeNode(node: Category, children: any[] | null): CategoryWithCountTreeNode {
  const baseNode: CategoryWithCountTreeNode = {
    key: node.id,
    title: node.name,
    // 考试分类只能选择第三级
    checkable: !children,
    // disabled:node.checkable,
    isLeaf: !children,
    count: node.count,
  };

  if (children) {
    baseNode.children = children;
  }

  return baseNode;
}

function getCascaderTreeNode(node: Category, children: any[] | null): CascaderTreeNode {
  const baseNode: CascaderTreeNode = {
    label: node.name,
    value: node.id,
    isLeaf: !children,
  };

  // `Cascader` 组件要求 `children` 字段不存在才是叶子节点
  if (children) {
    baseNode.children = children;
  }

  return baseNode;
}
