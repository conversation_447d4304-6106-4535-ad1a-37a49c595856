import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BackendPaper, ServiceQ } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import {
  getPapersAction,
  selectAllTypePapers,
  selectPapersEntities,
} from '@manyun/knowledge-hub.state.papers';

export type UsePapersProps = {};

export function usePapers(): readonly [
  () => void,
  {
    readonly loading: boolean;
    readonly data: {
      readonly data: BackendPaper[];
      readonly total: number;
    };
    readonly fields: Partial<ServiceQ>;
    readonly selectedIds: number[];
  }
] {
  const dispatch = useDispatch();

  const getPapers = useCallback(() => {
    dispatch(getPapersAction());
  }, [dispatch]);

  const { loading, ids, total, selectedIds, fields } = useSelector(selectAllTypePapers());
  const papers = useSelector(selectPapersEntities(ids));

  return [
    getPapers,
    { loading, data: { data: papers, total: total }, fields, selectedIds },
  ] as const;
}
