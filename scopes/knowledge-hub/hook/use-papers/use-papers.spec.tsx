import { act, renderHook } from '@testing-library/react-hooks';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { registerWebMocks } from '@manyun/knowledge-hub.service.dcexam.fetch-papers';
import { destroyMock, getMock } from '@manyun/service.request';

import { usePapers } from './use-papers';

beforeAll(() => {
  registerWebMocks(getMock('web'));
});
afterAll(() => {
  destroyMock();
});

it('should loading papers` data', async () => {
  const { result, waitForNextUpdate } = renderHook(() => usePapers(), { wrapper: FakeStore });
  expect(result.current[1].loading).toBe(false);
  expect(result.current[1].data.data).toHaveLength(0);
  act(() => {
    result.current[0]();
  });

  expect(result.current[1].loading).toBe(true);

  await waitForNextUpdate();
  expect(result.current[1].loading).toBe(false);
  expect(result.current[1].data.data).not.toBeNull();
  expect(result.current[1].data.data).toHaveLength(10);
});
