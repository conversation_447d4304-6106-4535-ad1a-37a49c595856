import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>, Route, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Theme } from '@manyun/base-ui.theme.theme';
import { Space } from '@manyun/base-ui.ui.space';
import { LayoutProvider } from '@manyun/dc-brain.context.layout';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { CourseDetail } from '@manyun/knowledge-hub.page.course-detail';
import { CourseLearning } from '@manyun/knowledge-hub.page.course-learning';
import { CourseMutator } from '@manyun/knowledge-hub.page.course-mutator';
import { Courses } from '@manyun/knowledge-hub.page.courses';
import { Coursewares } from '@manyun/knowledge-hub.page.coursewares';
import { Exam } from '@manyun/knowledge-hub.page.exam';
import { ExamMutator } from '@manyun/knowledge-hub.page.exam-mutator';
import { ExamPaper } from '@manyun/knowledge-hub.page.exam-paper';
import { ExamPaperResult } from '@manyun/knowledge-hub.page.exam-paper-result';
import { Exams } from '@manyun/knowledge-hub.page.exams';
import { MyCourses } from '@manyun/knowledge-hub.page.my-courses';
import { MyExams } from '@manyun/knowledge-hub.page.my-exams';
import { PaperMutator } from '@manyun/knowledge-hub.page.paper-mutator';
import { Papers } from '@manyun/knowledge-hub.page.papers';
import { PendingMarkExams } from '@manyun/knowledge-hub.page.pending-mark-exams';
import { QuestionsPage } from '@manyun/knowledge-hub.page.questions';
import { QuestionsCreator } from '@manyun/knowledge-hub.page.questions-creator';
import { Skill } from '@manyun/knowledge-hub.page.skill';
import { Skills } from '@manyun/knowledge-hub.page.skills';
import { TestPaperResult } from '@manyun/knowledge-hub.page.test-paper-result';
// @ts-ignore @yxw @FIXME 不要这样引入
import { BasicTestPaperResult } from '@manyun/knowledge-hub.page.test-paper-result/dist/test-paper-result.composition';
import { TrainPlan } from '@manyun/knowledge-hub.page.train-plan';
import { TrainPlanMutator } from '@manyun/knowledge-hub.page.train-plan-mutator';
import { TrainPlans } from '@manyun/knowledge-hub.page.train-plans';
import { Wikis } from '@manyun/knowledge-hub.page.wikis';
import {
  COURSES_ROUTE_PATH,
  COURSEWARES_ROUTE_PATH,
  COURSE_DETAIL_ROUTE_PATH,
  COURSE_LEARNING_TEST_PAPER_ROUTE_PATH,
  EDIT_SPECIFIC_COURSE_ROUTE_PATH,
  EDIT_SPECIFIC_EXAM_ROUTE_PATH,
  EDIT_SPECIFIC_PAPER_ROUTE_PATH,
  EDIT_TRAIN_PLAN_ROUTE_PATH,
  EXAMS_ROUTE_PATH,
  EXAM_PAPER_RESULT_PAGE_URL,
  EXAM_PAPER_URL,
  EXAM_ROUTE_PATH,
  LEARNING_SPECIFIC_COURSE_ROUTE_PATH,
  MY_COURSES_ROUTE_PATH,
  MY_EXAMS_PAGE_URL,
  NEW_COURSE_ROUTE_PATH,
  NEW_EXAM_ROUTE_PATH,
  NEW_PAPER_ROUTE_PATH,
  NEW_TRAIN_PLAN_ROUTE_PATH,
  PAPERS_ROUTE_PATH,
  PENDING_MARK_EXAMS_ROUTE_PATH,
  QUESTIONS_CREATOR_ROUTE_PATH,
  QUESTIONS_ROUTE_PATH,
  SKILLS_PAGE_URL,
  SKILL_PAGE_URL,
  TEST_PAPER_RESULT_ROUTE_PATH,
  TRAIN_PLANS_ROUTE_PATH,
  TRAIN_PLAN_ROUTE_PATH,
  WIKIS_ROUTE_PATH,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';

function MockLayout({ children }: { children: React.ReactNode }) {
  return (
    <Space style={{ width: '100%' }} direction="vertical" size="small">
      <div style={{ paddingLeft: 22, paddingRight: 22 }} />
      <div style={{ paddingLeft: 22, paddingRight: 22 }}>{children}</div>
    </Space>
  );
}

export function KnowledgeHubApp() {
  return (
    <ConfigProvider>
      <Theme>
        <FakeStore>
          {/* <RouterProvider> */}
          <BrowserRouter>
            <Space style={{ display: 'flex' }}>
              <Link to={QUESTIONS_ROUTE_PATH}>试题列表</Link>
              <Link to={PAPERS_ROUTE_PATH}>试卷列表</Link>
              <Link to={EXAMS_ROUTE_PATH}>考试列表</Link>
              <Link to={PENDING_MARK_EXAMS_ROUTE_PATH}>待判卷考试列表</Link>
              <Link to={MY_EXAMS_PAGE_URL}>我的考试</Link>
              <Link to={COURSES_ROUTE_PATH}>课程列表</Link>
              <Link to={MY_COURSES_ROUTE_PATH}>我的课程</Link>
              <Link to={COURSEWARES_ROUTE_PATH}>课件列表</Link>
              <Link to={WIKIS_ROUTE_PATH}>知识库</Link>
              <Link to={SKILLS_PAGE_URL}>技能列表</Link>
              <Link to="/page/exam/result">考试结果</Link>
              <Link to={TRAIN_PLANS_ROUTE_PATH}>培训计划管理</Link>
            </Space>
            <LayoutProvider>
              <MockLayout>
                {/* header component */}

                <Switch>
                  <Route path="/page/exam/result">
                    <div style={{ height: 'calc(100vh - 40px)' }}>
                      <BasicTestPaperResult />
                    </div>
                  </Route>

                  <Route path={QUESTIONS_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 40px)' }}>
                      <QuestionsPage />
                    </div>
                  </Route>

                  <Route path={QUESTIONS_CREATOR_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 40px)' }}>
                      <QuestionsCreator />
                    </div>
                  </Route>

                  <Route path={EDIT_SPECIFIC_PAPER_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 60px)' }}>
                      <PaperMutator />
                    </div>
                  </Route>

                  <Route path={NEW_PAPER_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 60px)' }}>
                      <PaperMutator />
                    </div>
                  </Route>

                  <Route path={PAPERS_ROUTE_PATH}>
                    <Papers />
                  </Route>

                  <Route path={PENDING_MARK_EXAMS_ROUTE_PATH}>
                    <PendingMarkExams />
                  </Route>

                  <Route path={MY_EXAMS_PAGE_URL}>
                    <MyExams />
                  </Route>

                  <Route path={TEST_PAPER_RESULT_ROUTE_PATH}>
                    <TestPaperResult />
                  </Route>

                  <Route path={EXAM_PAPER_RESULT_PAGE_URL}>
                    <ExamPaperResult />
                  </Route>

                  <Route path={COURSE_LEARNING_TEST_PAPER_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 160px)' }}>
                      <ExamPaper />
                    </div>
                  </Route>

                  <Route path={EXAM_PAPER_URL}>
                    <div style={{ height: 'calc(100vh - 160px)' }}>
                      <ExamPaper />
                    </div>
                  </Route>

                  <Route path={EDIT_SPECIFIC_EXAM_ROUTE_PATH}>
                    <ExamMutator />
                  </Route>

                  <Route path={NEW_EXAM_ROUTE_PATH} exact>
                    <ExamMutator />
                  </Route>

                  <Route path={EXAM_ROUTE_PATH}>
                    <Exam />
                  </Route>

                  <Route path={EXAMS_ROUTE_PATH}>
                    <Exams />
                  </Route>

                  <Route path={EDIT_SPECIFIC_COURSE_ROUTE_PATH}>
                    <CourseMutator />
                  </Route>

                  <Route path={NEW_COURSE_ROUTE_PATH}>
                    <CourseMutator />
                  </Route>

                  <Route path={LEARNING_SPECIFIC_COURSE_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 40px)' }}>
                      <CourseLearning />
                    </div>
                  </Route>

                  <Route path={COURSES_ROUTE_PATH} exact>
                    <Courses />
                  </Route>

                  <Route path={COURSE_DETAIL_ROUTE_PATH}>
                    <CourseDetail />
                  </Route>

                  <Route path={MY_COURSES_ROUTE_PATH}>
                    <MyCourses />
                  </Route>

                  <Route path={COURSEWARES_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 40px)' }}>
                      <Coursewares />
                    </div>
                  </Route>

                  <Route path={WIKIS_ROUTE_PATH}>
                    <div style={{ height: 'calc(100vh - 40px)' }}>
                      <Wikis />
                    </div>
                  </Route>

                  <Route path={SKILL_PAGE_URL}>
                    <Skill />
                  </Route>

                  <Route path={SKILLS_PAGE_URL}>
                    <Skills />
                  </Route>

                  <Route path={NEW_TRAIN_PLAN_ROUTE_PATH}>
                    <TrainPlanMutator />
                  </Route>

                  <Route path={TRAIN_PLANS_ROUTE_PATH}>
                    <TrainPlans />
                  </Route>

                  <Route path={EDIT_TRAIN_PLAN_ROUTE_PATH}>
                    <TrainPlanMutator />
                  </Route>

                  <Route path={TRAIN_PLAN_ROUTE_PATH}>
                    <TrainPlan />
                  </Route>
                </Switch>

                {/* footer component */}
              </MockLayout>
            </LayoutProvider>
          </BrowserRouter>
          {/* </RouterProvider> */}
        </FakeStore>
      </Theme>
    </ConfigProvider>
  );
}
