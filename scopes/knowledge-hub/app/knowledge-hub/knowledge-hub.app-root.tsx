import React from 'react';
import ReactDOM from 'react-dom';

import { destroyMock, webRequest } from '@manyun/service.request';

import { KnowledgeHubApp } from './app';

function Root() {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    document.body.dataset.theme = 'dark';
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return <KnowledgeHubApp />;
}

ReactDOM.render(<Root />, document.getElementById('root'));
