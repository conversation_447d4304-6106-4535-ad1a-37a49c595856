/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "composition/compositions-wrapper": {
        "name": "composition/compositions-wrapper",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "composition/compositions-wrapper",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "context/layout": {
        "name": "context/layout",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "context/layout",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/excel-statement": {
        "name": "page/excel-statement",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/excel-statement",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/excel-statement-create": {
        "name": "page/excel-statement-create",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/excel-statement-create",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/excel-statement-edit": {
        "name": "page/excel-statement-edit",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/excel-statement-edit",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/excel-statements": {
        "name": "page/excel-statements",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/excel-statements",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/home": {
        "name": "page/home",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/home",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/north-bound-create": {
        "name": "page/north-bound-create",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/north-bound-create",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/north-bound-manage": {
        "name": "page/north-bound-manage",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "page/north-bound-manage",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/admin-routes": {
        "name": "route/admin-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "route/admin-routes",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "service/check-upload-file": {
        "name": "service/check-upload-file",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/check-upload-file",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-biz-file": {
        "name": "service/delete-biz-file",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/delete-biz-file",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-north": {
        "name": "service/delete-north",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/delete-north",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-collection-menus": {
        "name": "service/fetch-collection-menus",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-collection-menus",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-columns": {
        "name": "service/fetch-columns",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-columns",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-north-black-user": {
        "name": "service/fetch-north-black-user",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-north-black-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-north-by-condition": {
        "name": "service/fetch-north-by-condition",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-north-by-condition",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-north-protocol": {
        "name": "service/fetch-north-protocol",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-north-protocol",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-save-collection-menus": {
        "name": "service/fetch-save-collection-menus",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-save-collection-menus",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-switches-from-apollo": {
        "name": "service/fetch-switches-from-apollo",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-switches-from-apollo",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-system-node-channels": {
        "name": "service/fetch-system-node-channels",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-system-node-channels",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-system-node-configs": {
        "name": "service/fetch-system-node-configs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-system-node-configs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-system-time": {
        "name": "service/fetch-system-time",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-system-time",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-task-search-agg": {
        "name": "service/fetch-task-search-agg",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-task-search-agg",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.21": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/fetch-task-search-center": {
        "name": "service/fetch-task-search-center",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-task-search-center",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.21": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/update-columns": {
        "name": "service/update-columns",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/update-columns",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-north": {
        "name": "service/update-north",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/update-north",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-north-black-user": {
        "name": "service/update-north-black-user",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/update-north-black-user",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/upload-full-file": {
        "name": "service/upload-full-file",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "service/upload-full-file",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/common": {
        "name": "state/common",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "state/common",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "state/config": {
        "name": "state/config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "state/config",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "state/router": {
        "name": "state/router",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "state/router",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "store/fake-store": {
        "name": "store/fake-store",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "store/fake-store",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "store/store": {
        "name": "store/store",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "store/store",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/any-target-link": {
        "name": "ui/any-target-link",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/any-target-link",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/custom-shapes": {
        "name": "ui/custom-shapes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/custom-shapes",
        "config": {
            "teambit.pkg/pkg": {
                "packageJson": {
                    "exports": {
                        ".": "./dist/index.js",
                        "./alerting": "./dist/alerting",
                        "./alerting-marker": "./dist/alerting-marker",
                        "./highlight": "./dist/highlight"
                    },
                    "private": false,
                    "homepage": "https://code.dcbase.cn/frontend/dc-brain",
                    "publishConfig": {
                        "scope": "@manyun",
                        "registry": "https://packages.aliyun.com/5f8d28b593de78251872349a/npm/npm-registry/"
                    }
                }
            },
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/edit-columns": {
        "name": "ui/edit-columns",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "ui/edit-columns",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/essential-todos": {
        "name": "ui/essential-todos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/essential-todos",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/layout": {
        "name": "ui/layout",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/layout",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/region-cascader": {
        "name": "ui/region-cascader",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/region-cascader",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/rich-editor": {
        "name": "ui/rich-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/rich-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/upload": {
        "name": "ui/upload",
        "scope": "manyun.dc-brain",
        "version": "0.0.6",
        "mainFile": "index.ts",
        "rootDir": "ui/upload",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/watermark": {
        "name": "ui/watermark",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "ui/watermark",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "util/flatten-tree-data": {
        "name": "util/flatten-tree-data",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "util/flatten-tree-data",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/get-location-search-map": {
        "name": "util/get-location-search-map",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "util/get-location-search-map",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/number-to-chinese": {
        "name": "util/number-to-chinese",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.dc-brain",
        "mainFile": "index.ts",
        "rootDir": "util/number-to-chinese",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "util/scroll-to-field": {
        "name": "util/scroll-to-field",
        "scope": "manyun.dc-brain",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "util/scroll-to-field",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "$schema-version": "17.0.0"
}