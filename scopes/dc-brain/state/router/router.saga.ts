import { push, replace } from 'connected-react-router';
import { fork, put, take } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { LOGIN_ROUTH_PATH } from '@manyun/auth-hub.route.auth-routes';
import { getMyPermissionsAction } from '@manyun/auth-hub.state.user';
import { syncCommonDataAction } from '@manyun/dc-brain.state.common';

import { redirectAction } from './router.action';

/** Workers */

/**
 * Push/Replace location
 */
function* redirect({ location, func = 'push' }: SagaReturnType<typeof redirectAction>['payload']) {
  if (func === 'push') {
    yield put(push(location));
  } else if (func === 'replace') {
    yield put(replace(location));
  } else {
    throw new Error(`func(${func}) not supported!`);
  }
}

/** Watchers */
function* watchRedirect() {
  while (true) {
    const { payload } = yield take(redirectAction.type);
    yield fork(redirect, payload);
  }
}

function* watchLocationChange() {
  while (true) {
    const { payload } = yield take('@@router/LOCATION_CHANGE');
    const { pathname } = payload.location;
    const excludeRoutePaths = [LOGIN_ROUTH_PATH];
    if (!excludeRoutePaths.includes(pathname)) {
      yield put(getMyPermissionsAction());
      yield put(syncCommonDataAction({ strategy: { currentUser: 'FORCED' } }));
    }
  }
}

export default [fork(watchRedirect), fork(watchLocationChange)];
