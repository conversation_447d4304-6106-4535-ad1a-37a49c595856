import { normalize, schema } from 'normalizr';

function flattenTreeData(treeData: any[]) {
  const dataList: any[] = [];
  loop(treeData);
  function loop(data: any[]) {
    for (let i = 0; i < data.length; i++) {
      const { children, ...node } = data[i];
      if (children) {
        loop(children);
      }
      dataList.push(node);
    }
  }
  return dataList;
}

const getSchema = (
  options: schema.EntityOptions = {
    idAttribute: 'metaCode',
  }
) => [new schema.Entity('normalizedList', {}, options)];

/**
 * @deprecated WIP
 * @param data
 * @param schemaOpts
 * @returns
 */
export function processingReturnData(data: any[], schemaOpts?: schema.EntityOptions) {
  const parallelList = flattenTreeData(data);
  const normalizedList = normalize(parallelList, getSchema(schemaOpts)).entities.normalizedList;
  return { treeList: data, normalizedList, parallelList };
}
