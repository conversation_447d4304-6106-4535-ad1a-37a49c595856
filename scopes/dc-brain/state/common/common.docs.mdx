---
description: 'Global Common redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'global', 'common']
---

当前只迁移了 `legacy commonSlice` 中的 `action: syncCommonDataActionCreator`

## 使用

1. 集成 `reducer(slice state)`

```js
import commonSliceReducer from '@manyun/dc-brain.state.common';

const rootReducer = {
  ...commonSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { commonWatchers } from '@manyun/dc-brain.state.common';

const function* rootSaga() {
  yield all(
    ...commonWatchers,
    // other sagas...
  );
};
```
