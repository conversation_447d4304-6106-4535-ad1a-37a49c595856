import { createAction } from '@reduxjs/toolkit';

import { commonSlice } from './common.slice';

const prefix = commonSlice.name;

export const commonSliceActions = commonSlice.actions;

export type StrategyType = 'IF_NULL' | 'FORCED';

type DeviceType = string;
export type SyncCommonDataActionPayload = {
  strategy: {
    space?: StrategyType;
    allVirtualType?: StrategyType;
    deviceCategory?: StrategyType;
    /**
     * @deprecated Use `citiesTree` instead.
     */
    cities?: StrategyType;
    citiesTree?: StrategyType;
    eventTypes?: StrategyType;
    changeTypes?: StrategyType;
    ticketTypes?: StrategyType;
    accessCardTypes?: StrategyType;
    roomTypes?: StrategyType;
    currentUser?: StrategyType;
    pointsDefinition?: string[];
    /**
     * 批量获取设备类型下的所有点位定义
     */
    deviceTypesPointsDefinition?: DeviceType[];
    /**
     * 批量获取设备类型下的所有规格信息
     */
    deviceTypesSpecs?: string[];
    coords?: StrategyType;
  };
};

export const syncCommonDataAction = createAction<SyncCommonDataActionPayload>(
  prefix + '/SYNC_COMMON_DATA'
);

/**
 * @deprecated Use `syncCommonDataAction` instead
 */
export const syncCommonDataActionCreator = createAction<SyncCommonDataActionPayload>(
  'common/syncCommonDataActionCreator'
);
