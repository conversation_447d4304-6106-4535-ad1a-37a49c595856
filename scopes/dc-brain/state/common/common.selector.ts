// @ts-expect-error: TS7016 because we use ESM build for component `state/common`
import memoizeOne from 'memoize-one/dist/memoize-one.esm';
import { normalize, schema } from 'normalizr';

import { flattenTreeData } from '@manyun/dc-brain.util.flatten-tree-data';

import { commonSlice } from './common.slice';
import type { CitiesTreeNode, CommonSliceState } from './common.slice';

type StoreState = {
  [commonSlice.name]: CommonSliceState;
};

export const getCommonData = ({ common }: StoreState) => common;
export const getDeviceTypeMetaData = ({ common: { deviceCategory } }: StoreState) => deviceCategory;
export const getEventTypesMetaData = ({ common: { eventTypes } }: StoreState) => eventTypes;
export const getCitiesTree = ({ common: { citiesTree } }: StoreState) => citiesTree;
export const getCoords = ({ common: { coords } }: StoreState) => coords;

export type SimpleCityTreeNode = {
  label: string;
  value: string;
  isLeaf: boolean;
};

export type CitiesMapper =
  | {
      [key: string]: SimpleCityTreeNode;
    }
  | undefined;
const citiesSchema = [
  new schema.Entity<SimpleCityTreeNode>(
    'nodes',
    {},
    {
      idAttribute: 'value',
      processStrategy({ label, value, isLeaf }: Omit<CitiesTreeNode, 'children'>) {
        return {
          label,
          value,
          isLeaf: isLeaf ?? false,
        };
      },
    }
  ),
];
const memoizedFlattenTreeData = memoizeOne(flattenTreeData) as typeof flattenTreeData;
const memoizedNormalize = memoizeOne(normalize) as typeof normalize;
export const getCitiesMapper = ({ common: { citiesTree } }: StoreState) => {
  const {
    entities: { nodes },
  } = memoizedNormalize(memoizedFlattenTreeData(citiesTree), citiesSchema);

  return nodes;
};
