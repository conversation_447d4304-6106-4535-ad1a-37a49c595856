/* eslint-disable @typescript-eslint/no-explicit-any */
import * as Comlink from 'comlink/dist/esm/comlink';
import { normalize, schema } from 'normalizr';
import { call, delay, fork, put, race, select, take } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { getMyResourcesAction } from '@manyun/auth-hub.state.user';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { configSliceActions as configActions } from '@manyun/dc-brain.state.config';
import { getLocationSearchMap } from '@manyun/dc-brain.util.get-location-search-map';
import { fetchSpaces } from '@manyun/resource-hub.service.fetch-spaces';
import { selectSpaceCodes, spaceSliceActions } from '@manyun/resource-hub.state.space';

import {
  commonSliceActions as commonActions,
  syncCommonDataAction,
  syncCommonDataActionCreator,
} from './common.action';
import { getCommonData } from './common.selector';
import { processingReturnData } from './common.util';

const defaultStrategy = {
  space: 'IF_NULL',
  allVirtualType: 'IF_NULL',
  deviceCategory: 'IF_NULL',
  cities: 'IF_NULL',
  citiesTree: 'IF_NULL',
  eventTypes: 'IF_NULL',
  changeTypes: 'IF_NULL',
  ticketTypes: 'IF_NULL',
  roomTypes: 'IF_NULL',
  currentUser: 'IF_NULL',
  // 必须要传入要查询的 `deviceType + pointCode` 集合
  pointsDefinition: [],
  deviceTypesPointsDefinition: [],
  deviceTypesSpecs: [],
  accessCardTypes: 'IF_NULL',
  coords: 'IF_NULL',
};

/**
 * Request worker
 */
let Request: any;

function* syncCommonData({ strategy = defaultStrategy } = {}): any {
  const {
    citiesTree,
    virtualType,
    deviceCategory,
    regionCityTree,
    eventTypes,
    changeTypes,
    ticketTypes,
    roomTypes,
    currentUser,
    accessCardTypes,
    coords,
  } = yield select(getCommonData);
  const space = yield select(selectSpaceCodes);

  if (Request === undefined) {
    Request = Comlink.wrap(new Worker(`${process.env.PUBLIC_URL}/worker.js`));
  }
  const request = yield new Request();

  const headers: Record<string, string> = {};
  const extraQ: any = {};
  const { embed, tenantId } = getLocationSearchMap(window.location.search, ['embed', 'tenantId']);
  if (embed) {
    extraQ.tenantId = tenantId;
  }

  if (
    strategy.currentUser === 'FORCED' ||
    (currentUser === null && strategy.currentUser === 'IF_NULL')
  ) {
    yield put(getMyResourcesAction());
    // const [currentUserJson, timeout] = yield race([
    //   request.json('/api/pm/resource/currentUser/tree'),
    //   delay(5000),
    // ]);
    // if (currentUserJson && Array.isArray(currentUserJson.data)) {
    //   yield put(
    //     commonActions.setCurrentUser(
    //       processingReturnData(currentUserJson.data, {
    //         idAttribute: 'resourceCode',
    //       })
    //     )
    //   );
    // }
    // if (timeout) {
    //   request.abort();
    // }
  }

  if (strategy.space === 'FORCED' || (space === null && strategy.space === 'IF_NULL')) {
    const { error, data }: SagaReturnType<typeof fetchSpaces> = yield call(fetchSpaces, {
      nodeTypes: ['IDC', 'BLOCK', 'ROOM'],
      includeVirtualBlocks: true,
      authorizedOnly: false,
    });
    if (error) {
      console.error(error.message);
    } else {
      yield put(
        spaceSliceActions.setSpaces({
          data,
        })
      );
    }
  }

  if (
    strategy.allVirtualType === 'FORCED' ||
    (virtualType === null && strategy.allVirtualType === 'IF_NULL')
  ) {
    const [allVirtualTypeJson, timeout] = yield race([
      request.json('/api/dccm/query/all/virtual/type', { headers }),
      delay(5000),
    ]);
    if (allVirtualTypeJson && allVirtualTypeJson.data) {
      yield put(commonActions.setVirtualType(allVirtualTypeJson.data));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    strategy.deviceCategory === 'FORCED' ||
    (deviceCategory === null && strategy.deviceCategory === 'IF_NULL')
  ) {
    const [deviceCategoryJson, timeout] = yield race([
      request.json('/api/dccm/meta/query/device/category', {
        method: 'POST',
        body: JSON.stringify({ ...extraQ, numbered: null }),
        headers: { ...headers, 'Content-Type': 'application/json' },
      }),
      delay(5000),
    ]);
    if (deviceCategoryJson && deviceCategoryJson.data) {
      yield put(commonActions.setDeviceCategory(processingReturnData(deviceCategoryJson.data)));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (strategy.cities === 'FORCED' || (regionCityTree === null && strategy.cities === 'IF_NULL')) {
    const citiesUrl = new URL('./assets/cities.json', import.meta.url);
    const [cities, timeout] = yield race([
      request.json(citiesUrl.pathname, { headers }),
      delay(5000),
    ]);
    if (cities) {
      yield put(commonActions.setRegionTree(processingReturnData(cities)));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    strategy.citiesTree === 'FORCED' ||
    (citiesTree.length <= 0 && strategy.citiesTree === 'IF_NULL')
  ) {
    const cityOptionsUrl = new URL('./assets/city-options.json', import.meta.url);
    const [citiesTree, timeout] = yield race([
      request.json(cityOptionsUrl.pathname, { headers }),
      delay(5000),
    ]);
    if (citiesTree) {
      yield put(commonActions.setCitiesTree(citiesTree));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (strategy.coords === 'FORCED' || (!coords && strategy.coords === 'IF_NULL')) {
    const cityCoordsOptionsUrl = new URL('./assets/coords.json', import.meta.url);
    const [coords, timeout] = yield race([
      request.json(cityCoordsOptionsUrl.pathname, { headers }),
      delay(5000),
    ]);
    if (coords) {
      yield put(commonActions.setCoords(coords));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    strategy.eventTypes === 'FORCED' ||
    (eventTypes === null && strategy.eventTypes === 'IF_NULL')
  ) {
    const [eventTypesJson, timeout] = yield race([
      request.json(
        '/api/dccm/meta/query/category/tree?topCategory=EVENT_TOP_CATEGORY&secondCategory=EVENT_SECOND_CATEGORY',
        { headers }
      ),
      delay(5000),
    ]);
    if (eventTypesJson && Array.isArray(eventTypesJson.data)) {
      yield put(
        commonActions.setEventTypes(
          processingReturnData(eventTypesJson.data, {
            idAttribute: 'metaCode',
            processStrategy(value: any) {
              return [value];
            },
            mergeStrategy(entityAs: any[], entityBs: any[]) {
              return [...entityAs, ...entityBs];
            },
          })
        )
      );
    }
    if (timeout) {
      request.abort();
    }
  }

  if (strategy.roomTypes === 'FORCED' || (roomTypes === null && strategy.roomTypes === 'IF_NULL')) {
    const [roomTypesJson, timeout] = yield race([
      request.json('/api/dccm/get/room/type', { headers }),
      delay(5000),
    ]);
    if (roomTypesJson?.data) {
      yield put(commonActions.setRoomTypes(roomTypesJson.data));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    strategy.changeTypes === 'FORCED' ||
    (changeTypes === null && strategy.changeTypes === 'IF_NULL')
  ) {
    const [changeTypesJson, timeout] = yield race([
      request.json(
        '/api/dccm/meta/query/category/tree?topCategory=CHANGE_TOP_CATEGORY&secondCategory=CHANGE_SECOND_CATEGORY',
        { headers }
      ),
      delay(5000),
    ]);
    if (changeTypesJson && Array.isArray(changeTypesJson.data)) {
      yield put(
        commonActions.setChangeTypes(
          processingReturnData(changeTypesJson.data, {
            idAttribute: 'metaCode',
            processStrategy(value: any) {
              return [value];
            },
            mergeStrategy(entityAs: any[], entityBs: any[]) {
              return [...entityAs, ...entityBs];
            },
          })
        )
      );
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    strategy.accessCardTypes === 'FORCED' ||
    (accessCardTypes === null && strategy.accessCardTypes === 'IF_NULL')
  ) {
    const [accessCardTypesJson, timeout] = yield race([
      request.json(
        '/api/dccm/meta/query/category/tree?topCategory=ACCESS_CARD_TOP_CATEGORY&secondCategory=ACCESS_CARD_SECOND_CATEGORY',
        { headers }
      ),
      delay(5000),
    ]);
    if (accessCardTypesJson && Array.isArray(accessCardTypesJson.data)) {
      yield put(
        commonActions.setAccessCardTypes(
          processingReturnData(accessCardTypesJson.data, {
            idAttribute: 'metaCode',
            processStrategy(value: any) {
              return [value];
            },
            mergeStrategy(entityAs: any[], entityBs: any[]) {
              return [...entityAs, ...entityBs];
            },
          })
        )
      );
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    strategy.ticketTypes === 'FORCED' ||
    (ticketTypes === null && strategy.ticketTypes === 'IF_NULL')
  ) {
    const [ticketTypesJson, timeout] = yield race([
      request.json('/api/taskcenter/base/list/type/subType', {
        method: 'POST',
        body: JSON.stringify({ parentCode: 'WORK_ORDER' }),
        headers: { ...headers, 'Content-Type': 'application/json' },
      }),
      delay(5000),
    ]);
    if (ticketTypesJson && Array.isArray(ticketTypesJson.data)) {
      yield put(
        commonActions.setTicketTypes(
          processingReturnData(ticketTypesJson.data, {
            idAttribute: 'taskType',
          })
        )
      );
    }
    if (timeout) {
      request.abort();
    }
  }

  if (Array.isArray(strategy.pointsDefinition) && strategy.pointsDefinition.length) {
    const [pointsDefinitionJson, timeout] = yield race([
      request.json('/api/dccm/point/batch/query', {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        body: JSON.stringify({ devicePointList: strategy.pointsDefinition }),
      }),
      delay(5 * 1000),
    ]);
    if (Array.isArray(pointsDefinitionJson?.data) && pointsDefinitionJson.data.length) {
      const partialPointsDefinitionMap = normalize(pointsDefinitionJson.data, [
        new schema.Entity(
          'defs',
          {},
          {
            idAttribute: 'deviceType',
            processStrategy({ name, code, dataType, unit, validLimits, parentCode }) {
              return {
                [code]: {
                  name,
                  dataType: dataType.code,
                  unit,
                  validLimits,
                  parentCode,
                },
              };
            },
            mergeStrategy(entityA, entityB) {
              return { ...entityA, ...entityB };
            },
          }
        ),
      ]).entities.defs;
      yield put(configActions.updatePointsDefinitionMap(partialPointsDefinitionMap!));
    }
    if (timeout) {
      request.abort();
    }
  }

  if (
    Array.isArray(strategy.deviceTypesPointsDefinition) &&
    strategy.deviceTypesPointsDefinition.length
  ) {
    const [pointsDefinitionJSON, timeout] = yield race([
      // all(
      // strategy.deviceTypesPointsDefinition.map(deviceType =>
      //   request.json(`/api/dccm/point/query?deviceType=${deviceType}`)
      //   )
      //   ),
      request.json(`/api/dccm/point/query/by/device/type`, {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...extraQ,
          deviceTypeList: strategy.deviceTypesPointsDefinition,
        }),
      }),
      delay(5 * 1000),
    ]);
    if (pointsDefinitionJSON && Array.isArray(pointsDefinitionJSON.data)) {
      const partialPointsDefinitionMap = normalize(pointsDefinitionJSON.data, [
        new schema.Entity(
          'defs',
          {},
          {
            idAttribute: 'deviceType',
            processStrategy({ name, pointCode: code, dataType, unit, validLimits, parentCode }) {
              return {
                [code]: {
                  name,
                  dataType: dataType.code,
                  unit,
                  validLimits,
                  parentCode,
                },
              };
            },
            mergeStrategy(entityA, entityB) {
              return { ...entityA, ...entityB };
            },
          }
        ),
      ]).entities.defs;
      if (partialPointsDefinitionMap && Object.keys(partialPointsDefinitionMap).length) {
        yield put(configActions.updatePointsDefinitionMap(partialPointsDefinitionMap));
      }
    }
    if (timeout) {
      request.abort();
    }
  }

  if (Array.isArray(strategy.deviceTypesSpecs) && strategy.deviceTypesSpecs.length) {
    // 因为现在接口还不支持批量查多个设备类型的规格
    // 所以这里先只查第一个，将来遇到要批量查的场景时，再做重构
    const deviceType = strategy.deviceTypesSpecs[0];
    if (env.__DEV_MODE__ && strategy.deviceTypesSpecs.length > 1) {
      throw new Error(`Batch device types not supported! ${strategy.deviceTypesSpecs}`);
    }
    const [deviceTypesSpecsJSON, timeout] = yield race([
      request.json('/api/dccm/spec/query', {
        method: 'POST',
        headers: { ...headers, 'Content-Type': 'application/json' },
        body: JSON.stringify({ deviceType }),
      }),
      delay(5 * 1000),
    ]);
    if ((deviceTypesSpecsJSON?.data?.length || 0) > 0) {
      const deviceTypeSpecsMappings = normalize(deviceTypesSpecsJSON.data, [
        new schema.Entity(
          'defs',
          {},
          {
            idAttribute: 'specCode',
            mergeStrategy(specA, specB) {
              throw new Error(`Duplicated specification: ${specA}, ${specB}`);
            },
          }
        ),
      ]).entities.defs;
      if (deviceTypeSpecsMappings && Object.keys(deviceTypeSpecsMappings).length) {
        yield put(
          commonActions.updateDeviceTypeSpecsMappings({
            deviceType,
            mappings: deviceTypeSpecsMappings,
          })
        );
      }
    }
    if (timeout) {
      request.abort();
    }
  }
}

/******************************************************************************/
/******************************* WATCHERS *************************************/
/******************************************************************************/

function* watchSyncCommonData() {
  while (true) {
    const { payload } = yield take([syncCommonDataActionCreator.type, syncCommonDataAction.type]);
    yield fork(syncCommonData, payload);
  }
}

export default [fork(watchSyncCommonData)];
