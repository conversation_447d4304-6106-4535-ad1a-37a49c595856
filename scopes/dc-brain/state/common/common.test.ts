// import { expectSaga } from 'redux-saga-test-plan';
// import someService from '@manyun/service.namespace.service-name';

// import { commonSlice } from './common.slice';
// import type { CommonSliceState } from './common.slice';
// import {
//   commonSliceActions,
//   getCommonAction,
// } from './common.action';
// import { getCommonSaga } from './common.saga';

// test('should return the initial state', () => {
//   expect(commonSlice.reducer(undefined, {} as any)).toEqual<CommonSliceState>({
//     entities: {},
//     ids: [],
//     total: 0,
//     loading: false,
//   });
// });

// test('should put a `setCommon` action', () => {
//   (someService as jest.MockedFunction<typeof someService>).mockResolvedValue({
//     status: 200,
//     statusText: 'ok',
//     headers: null,
//     config: {},
//     data: {
//       data: [],
//       total: 0,
//     },
//   });
//   const initialState = commonSlice.reducer(undefined, {} as any);

//   return expectSaga(getCommonSaga, {
//     type: getCommonAction.type,
//   })
//     .withState({ [commonSlice.name]: initialState })
//     .put({
//       type: commonSliceActions.fetchCommonStart.type,
//     })
//     .put({
//       type: commonSliceActions.setCommon.type,
//       payload: {
//         data: [],
//         total: 0,
//       },
//     })
//     .run();
// });
