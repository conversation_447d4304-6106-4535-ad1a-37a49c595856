/* eslint-disable @typescript-eslint/no-explicit-any */
import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

export type CitiesTreeNode = {
  label: string;
  value: string;
  children?: CitiesTreeNode[];
  isLeaf?: boolean;
};

export type CommonSliceState = {
  devTools: boolean;
  space: any;
  spaceTree: any;
  /** @deprecated use `citiesTree` */
  regionCityTree: any;
  citiesTree: CitiesTreeNode[];
  templateType: any;
  deviceCategory: any;
  deviceType: any;
  virtualType: any;
  eventTypes: any;
  changeTypes: any;
  ticketTypes: any;
  roomTypes: any;
  currentUser: any;
  deviceTypesSpecsMappings: any;
  accessCardTypes: any;
  coords: { [regionCode: string]: string } | null;
};

type CommonSliceCaseReducers = {
  toggleDevTools: CaseReducer<CommonSliceState, PayloadAction<any>>;
  failure: CaseReducer<CommonSliceState, PayloadAction<any>>;
  space: CaseReducer<CommonSliceState, PayloadAction<any>>;
  /** @deprecated because `regionCityTree` was deprecated */
  setRegionTree: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setCitiesTree: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setCoords: CaseReducer<CommonSliceState, PayloadAction<any>>;
  templateType: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setDeviceCategory: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setVirtualType: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setDeviceType: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setSpaceTree: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setEventTypes: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setChangeTypes: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setTicketTypes: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setAccessCardTypes: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setRoomTypes: CaseReducer<CommonSliceState, PayloadAction<any>>;
  setCurrentUser: CaseReducer<CommonSliceState, PayloadAction<any>>;
  updateDeviceTypeSpecsMappings: CaseReducer<CommonSliceState, PayloadAction<any>>;
};

export const commonSlice = createSlice<CommonSliceState, CommonSliceCaseReducers, 'common'>({
  name: 'common',
  initialState: {
    /** 是否展示开发者工具 */
    devTools: false,

    space: null,
    spaceTree: null,

    regionCityTree: null,
    /** 注意，这个初始值不可改为 null */
    citiesTree: [],

    /**
     * 模板大类、小类
     */
    templateType: null,

    deviceCategory: null,

    /**
     * 目标设备对应的类型
     */
    deviceType: null,
    virtualType: null,

    /**
     * 事件类型
     */
    eventTypes: null,

    /**
     * 变更类型
     */
    changeTypes: null,

    /**
     * 工单类型
     */
    ticketTypes: null,

    /**
     * 包间类型
     */
    roomTypes: null,

    /**
     * 用户资源
     */
    currentUser: null,

    /**
     * 按设备类型分组的规格信息映射
     */
    deviceTypesSpecsMappings: null,

    /**
     * 门禁卡
     */
    accessCardTypes: null,
    /**
     * 城市坐标
     */
    coords: null,
  },
  reducers: {
    toggleDevTools(state) {
      state.devTools = !state.devTools;
    },
    failure: state => ({ ...state, loading: false }),
    space: (state, { payload }) => ({
      ...state,
      space: payload,
    }),
    setRegionTree: (state, { payload }) => {
      state.regionCityTree = payload;
    },
    setCitiesTree: (state, { payload }) => {
      state.citiesTree = payload;
    },
    setCoords: (state, { payload }) => {
      state.coords = payload;
    },
    templateType: (state, { payload }) => ({
      ...state,
      templateType: payload,
    }),
    setDeviceCategory(state, { payload }) {
      state.deviceCategory = payload;
    },
    setVirtualType(state, { payload }) {
      state.virtualType = payload;
    },
    setDeviceType(state, { payload }) {
      state.deviceType = payload;
    },
    setSpaceTree(state, { payload }) {
      state.spaceTree = payload;
    },
    setEventTypes(state, { payload }) {
      state.eventTypes = payload;
    },
    setChangeTypes(state, { payload }) {
      state.changeTypes = payload;
    },
    setTicketTypes(state, { payload }) {
      state.ticketTypes = payload;
    },
    setAccessCardTypes(state, { payload }) {
      state.accessCardTypes = payload;
    },
    setRoomTypes(state, { payload }) {
      state.roomTypes = payload;
    },
    setCurrentUser(state, { payload }) {
      state.currentUser = payload;
    },
    updateDeviceTypeSpecsMappings(state, { payload: { deviceType, mappings } }) {
      if (state.deviceTypesSpecsMappings === null) {
        state.deviceTypesSpecsMappings = {
          [deviceType]: mappings,
        };

        return;
      }

      const existingMappings = state.deviceTypesSpecsMappings[deviceType];
      if (!existingMappings) {
        state.deviceTypesSpecsMappings[deviceType] = mappings;

        return;
      }

      Object.keys(mappings).forEach(specCode => {
        const existingSpec = existingMappings[specCode];
        const incomingSpec = mappings[specCode];
        if (
          !existingSpec ||
          /** 表示此规格已发生变化，需更新缓存 */
          existingSpec.gmtModified !== incomingSpec.gmtModified
        ) {
          state.deviceTypesSpecsMappings[deviceType][specCode] = incomingSpec;
        }
      });
    },
  },
});
