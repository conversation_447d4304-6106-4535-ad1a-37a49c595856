{"version": {"base": "manyun.dc-brain/config/base@0.0.67-dev.4", "variant": "manyun.dc-brain/config/goc@0.0.67-dev.4", "timestamp": 1697641764081}, "concreteDeviceTypeMappings": {"power-system": "1", "hvac-system": "2", "elv-system": "3", "space-idc": "90101", "space-block": "90102", "space-room": "90103", "space-column": "90104", "space-grid": "90105", "hv-isolating-cabinet": "10101", "hv-incoming-switchgear": "10102", "hv-metering-cabinet": "10103", "hv-pt-cabinet": "10104", "hv-outgoing-cabinet": "10105", "hv-bus-tie-cabinet": "10106", "transformer-repair-isolating-cabinet": "10107", "generator-incoming-cabinet": "10116", "lv_backup-cabinet": "", "lv-generator_incoming-cabinet": "", "transformer": "10201", "lv-incoming-cabinet": "10301", "lv-outgoing-cabinet": "10302", "lv-bus-tie-cabinet": "10303", "lv_capacitor-compensation-cabinet": "10304", "active-filter": "10305", "power-system_devices-pdu": "10306", "lighting-control-system_pdu": "10307", "fire-fighting-system_devices-pdu": "10308", "cooling-tower-pdu": "10309", "chiller-pdu": "10310", "chilled-water-pump-pdu": "10311", "secondary-chilled-water-pump-pdu": "10312", "cooled-water-pump-pdu": "10313", "feed-water-pump-pdu": "10314", "crac-pdu": "10315", "power-system_devices-switchboard": "10316", "lighting-control-system_switchboard": "10317", "fire-fighting-system_devices-switchboard": "10319", "crac-switchboard": "10320", "ats_pdu": "10322", "lv-outgoing-cabinet-drawer-switch": "10323", "dedicated-outdoor-air-system_devices-pdu": "10324", "cooled-water-charge-pump-pdu": "10325", "lv-generator": "10401", "hv-generator": "10402", "fuel-day-tank": "10403", "fuel-tank": "10404", "fuel-supply-pdu": "10405", "generator-set-incoming-cabinet": "10407", "generator-set-outgoing-cabinet": "10408", "generator-set_pt-switchgear": "10409", "generator-set_dummy-load_outgoing-switchgear": "10410", "generator-set_fuel-supply-pump": "10414", "generator-set_dummy-load_incoming-switchgear": "10426", "fuel-return-valve": "10428", "fuel-supply-valve": "10429", "generator-set_fuel-return-pump": "10430", "generator-set_cut-off-valve": "10432", "single-phase-ups": "10501", "three-phase-ups": "10502", "mudularthree-phase-ups": "10503", "ups-outgoing-cabinet": "10506", "ups-outgoing-cabinet-drawer-switch": "10507", "ups-incoming-cabinet-drawer-switch": "10509", "hvdc": "10601", "battery-unit": "10807", "sophisticated-ac-rpp": "10901", "hvdc-rpp": "10903", "ac-smart-pdu": "11103", "mdc": "11401", "sophisticated-water-cooled-crac": "20101", "sophisticated-air-cooled-crac": "20102", "inter-column-sophisticated-air-cooled-crac": "20105", "ahu-evaporation-unit--dx": "20108", "water-cooled-chiller": "20201", "air-cooled-chiller": "20202", "cooling-tower": "20204", "chilled-water-pump": "20207", "cooled-water-pump": "20209", "constant-pressure-water-supply-system": "20214", "tes-tank": "20215", "chemical-dosing-system": "20216", "water-tank": "20218", "plate-heat-exchanger": "20219", "bypass-filtration-system": "20221", "cooling-system-unit": "20222", "cdu": "20226", "air-economizer": "20227", "thermometer": "20305", "manometer": "20306", "water-meter": "20307", "chilled-water-pipe": "20310", "chilled-water-pipe_branch--water-supply-thermometer": "20312", "chilled-water-pipe_branch--return-water-thermometer": "20313", "chilled-water-pipe_branch--water-supply-manometer": "20314", "chilled-water-pipe_branch--return-water-manometer": "20315", "t-sensor": "30101", "water-leak-detector": "30104", "positioning-water-leak-detector": "30105", "water-pressure-sensor": "30107", "single-use-flow-tube-sensor": "30108", "bidirectional-flow-tube-sensor": "30109", "water-pressure-diff-sensor": "30111", "cold-aisle-thtb-sensor": "30118", "hot-aisle-thtb-sensor": "30119", "door-access-controller": "30204", "door-proximity-reader": "30206", "bullet-cctv-camera": "30221", "smoke-detector": "40105", "thermal-detector": "40106", "air-sampler": "40401"}, "abstractDeviceTypeMappings": {"hv-isolating-cabinet": ["10101"], "hv-incoming-switchgear": ["10102"], "hv-outgoing-cabinet": ["10105"], "hv-bus-tie-cabinet": ["10106"], "transformer-repair-isolating-cabinet": ["10107"], "generator-incoming-cabinet": ["10116"], "transformer": ["10201"], "lv-incoming-cabinet": ["10301"], "lv-outgoing-cabinet": ["10302"], "lv-bus-tie-cabinet": ["10303"], "cooling-tower-pdu": ["10309"], "chilled-water-pump-pdu": ["10311"], "lv-outgoing-cabinet-drawer-switch": ["10323"], "cooled-water-charge-pump-pdu": ["10325"], "fuel-day-tank": ["10403"], "generator": ["10402"], "generator-set-incoming-cabinet": ["10407"], "generator-set-outgoing-cabinet": ["10408"], "generator-set_dummy-load_outgoing-switchgear": ["10410"], "ups": ["10501", "10502", "10503"], "ups-outgoing-cabinet": ["10506"], "ups-outgoing-cabinet-drawer-switch": ["10507"], "hvdc": ["10601"], "hvdc-rpp": ["10903"], "battery-unit": ["10807"], "rpp": ["10901"], "pdu": ["11101", "11102", "11103"], "crac": ["20101", "20102", "20105"], "ahu-evaporation-unit--dx": ["20108"], "chiller": ["20201", "20202"], "cooling-tower": ["20204"], "pump": ["20207", "20208", "20209", "20210", "20211", "20212", "20213"], "constant-pressure-water-supply-system": ["20214"], "tes-tank": ["20215"], "chemical-dosing-system": ["20216"], "water-tank": ["20218"], "plate-heat-exchanger": ["20219"], "bypass-filtration-system": ["20221"], "cooling-system-unit": ["20222"], "valve": ["20301", "20302"], "thermometer": ["20305", "20312", "20313"], "manometer": ["20306", "20314", "20315"], "water-meter": ["20307"], "sensor": ["30101", "30102", "30103", "30106", "30107", "30108", "30109", "30110", "30111", "30113", "30114", "30115", "30116", "30117", "30118", "30119", "30121", "30126", "30132", "30133", "30134", "30135"], "psychrometer": ["30110"], "thtb-sensor": ["30118", "30119"], "door-access-controller": ["30204"], "door-proximity-reader": ["30206"], "bullet-cctv-camera": ["30221"], "smoke-detector": ["40105"], "thermal-detector": ["40106"], "air-sampler": ["40401"]}, "concretePointCodeMappings": {"10102": {"hv-incoming-switchgear_line-voltage--ab": "1004000", "hv-incoming-switchgear_line-voltage--bc": "1005000", "hv-incoming-switchgear_line-voltage--ca": "1006000", "hv-incoming-switchgear_power--sum": "1011000", "apparent-power": "1019000", "hv-incoming-switchgear_circuit-breaker-off-state": "2001000", "hv-incoming-switchgear_circuit-breaker-on-state": "2002000", "hv-incoming-switchgear_load-rate": "7003000"}, "10201": {"transformer_power-sum": "1021000", "transformer_active-power-input": "1021000", "transformer_apparent-power-input": "1029000", "transformer_input-power-factor": "1033000", "transformer_active-power-output": "1047000", "transformer_apparent-power-output": "1055000", "transformer_load-rate--avg": "7001000", "transformer_efficiency": "7002000", "transformer_power-dissipation": "7003000"}, "10323": {"active-power--sum": "1011000", "circuit-breaker--on-off-state": "2001000"}, "10401": {"generator_load-rate--avg": "7001000", "generator_total-output-power": "1008000"}, "10402": {"generator_output-phase-current-a": "1004000", "generator_output-phase-current-b": "1005000", "generator_output-phase-current-c": "1006000", "generator_total-output-power": "1008000", "generator_starter-battery-voltage": "1022000", "generator_load-rate--avg": "7001000"}, "10403": {"liquid-level": "1001000"}, "10404": {"fuel-tank_liquid-level--min": "7001000", "liquid-level": "7003000"}, "10414": {"fuel-pump_working-state": "2001000"}, "10430": {"fuel-pump_working-state": "2001000"}, "10501": {"ups_total-battery-voltage": "1007000", "ups_total-battery-current": "1008000", "ups_battery-charging-state": "2002000"}, "10502": {"ups_phase-voltage-a": "1001000", "ups_phase-voltage-b": "1002000", "ups_phase-voltage-c": "1003000", "ups_phase-current-a": "1004000", "ups_phase-current-b": "1005000", "ups_phase-current-c": "1006000", "ups_frequency": "1007000", "ups_input-active-power": "1008000", "ups_power-factor-a": "1013000", "ups_power-factor-b": "1014000", "ups_power-factor-c": "1015000", "ups_bypass-phase-voltage-a": "1016000", "ups_bypass-phase-voltage-b": "1017000", "ups_bypass-phase-voltage-c": "1018000", "ups_bypass-frequency": "1022000", "ups_output-phase-voltage-a": "1027000", "ups_output-phase-voltage-b": "1028000", "ups_output-phase-voltage-c": "1029000", "ups_output-phase-current-a": "1030000", "ups_output-phase-current-b": "1031000", "ups_output-phase-current-c": "1032000", "ups_output-frequency": "1033000", "ups_active-power-a": "1035000", "ups_active-power-b": "1036000", "ups_active-power-c": "1037000", "ups_output-power-factor-a": "1043000", "ups_output-power-factor-b": "1044000", "ups_output-power-factor-c": "1045000", "ups_output-load-rate-a": "1046000", "ups_output-load-rate-b": "1047000", "ups_output-load-rate-c": "1048000", "ups_total-battery-voltage": "1049000", "ups_total-battery-current": "1050000", "ups_battery-backup-time": "1052000", "ups_t-of-environment": "1053000", "ups_power-supply-mode": "2001000", "ups_input-on-off-state": "2002000", "ups_bypass-on-off-state": "2003000", "ups_repair-bypass-on-off-state": "2004000", "ups_output-on-off-state": "2005000", "ups_battery-charging-state": "2006000", "ups_battery-on-off-state": "2007000", "ups_inverter-working-state": "2008000", "ups_rectifier-working-state": "2009000", "ups_traffic-state": "2010000", "ups_output-load-rate--max": "7001000", "ups_active-power-output--sum": "7004000", "ups_power-dissipation": "7003000", "battery_backup-time--min": "7015000", "ups_output-load-rate--avg": "7016000"}, "10503": {"ups_phase-voltage-a": "1001000", "ups_phase-voltage-b": "1002000", "ups_phase-voltage-c": "1003000", "ups_phase-current-a": "1007000", "ups_phase-current-b": "1008000", "ups_phase-current-c": "1009000", "ups_frequency": "1010000", "ups_input-active-power": "1011000", "ups_input-power-factor": "1015000", "ups_power-factor-a": "1016000", "ups_power-factor-b": "1017000", "ups_power-factor-c": "1018000", "ups_bypass-phase-voltage-a": "1019000", "ups_bypass-phase-voltage-b": "1020000", "ups_bypass-phase-voltage-c": "1021000", "ups_bypass-frequency": "1028000", "ups_output-phase-voltage-a": "1033000", "ups_output-phase-voltage-b": "1034000", "ups_output-phase-voltage-c": "1035000", "ups_output-phase-current-a": "1036000", "ups_output-phase-current-b": "1037000", "ups_output-phase-current-c": "1038000", "ups_output-frequency": "1042000", "ups_active-power-output--sum": "1043000", "ups_active-power-a": "1044000", "ups_active-power-b": "1045000", "ups_active-power-c": "1046000", "ups_apparent-power-output--sum": "1047000", "ups_output-power-factor-a": "1052000", "ups_output-power-factor-b": "1053000", "ups_output-power-factor-c": "1054000", "ups_output-load-rate-a": "1055000", "ups_output-load-rate-b": "1056000", "ups_output-load-rate-c": "1057000", "ups_total-battery-voltage": "1058000", "ups_total-battery-current": "1059000", "ups_battery-backup-time": "1061000", "ups_t-of-environment": "1062000", "ups_power-supply-mode": "2001000", "ups_input-on-off-state": "2002000", "ups_bypass-on-off-state": "2003000", "ups_repair-bypass-on-off-state": "2004000", "ups_output-on-off-state": "2005000", "ups_battery-charging-state": "2006000", "ups_battery-on-off-state": "2007000", "ups_inverter-working-state": "2008000", "ups_rectifier-working-state": "2009000", "ups_traffic-state": "2011000", "ups_output-load-rate--avg": "7001000", "ups_efficiency": "7002000", "ups_power-dissipation": "7003000", "ups_active-power--sum": "7004000", "ups_inverter-input-apparent-power--sum": "1067000", "ups_output-load-rate--max": "7010000", "battery_backup-time--min": "7011000"}, "10507": {"active-power--sum": "1011000", "circuit-breaker--on-off-state": "2001000"}, "10601": {"hvdc_direct-current_output-power": "1013000", "hvdc_load-branch_power--sum": "1019000", "hvdc_input-power-factor": "1033000", "hvdc_active-power-input--sum": "1034000", "hvdc_apparent-power-input--sum": "1035000", "hvdc_load-rate--avg": "7001000", "hvdc_efficiency": "7002000", "hvdc_power-dissipation": "7003000", "battery_backup-time--min": "7005000"}, "10801": {"battery_voltage": "1001000", "battery_electric-current": "1002000", "battery-unit_temperature-of-environment": "1003000"}, "10807": {"battery-unit_voltage": "1001000", "battery-unit_electric-current": "1002000", "battery_voltage": "1004000", "battery_internal-resistance": "1005000", "battery_temperature": "1006000", "battery-unit_capacitance": "1007000", "battery-unit_working-mode": "2001000", "battery-unit_communication-mode": "2003000", "battery-unit_temperature": "7001000"}, "10901": {"rpp_input-on-off-state": "2001000", "rpp_load-rate": "7001000", "rpp_active-power--max": "7002000", "rpp_input-phase-voltage--max": "7003000", "rpp_input-phase-current--max": "7004000", "rpp_active-power-abc": "7005000"}, "10902": {"rpp_load-rate": "7001000"}, "10903": {"rpp_input-on-off-state": "2001000", "rpp_load-rate": "7001000", "rpp_active-power--max": "1003000", "rpp_input-phase-voltage--max": "1001000", "rpp_input-phase-current--max": "1002000", "rpp_active-power-abc": "1003000"}, "10904": {"rpp_load-rate": "7001000"}, "10905": {"rpp_load-rate": "7001000"}, "11101": {"pdu_on-off-state": "7002000", "pdu_current": "7003000", "pdu_voltage": "7004000", "pdu_power--sum": "1008000"}, "11102": {"pdu_on-off-state": "2002000", "pdu_current": "1006000", "pdu_voltage": "1005000", "pdu_power--sum": "1007000"}, "11103": {"pdu_on-off-state": "2002000", "pdu_current": "1002000", "pdu_voltage": "1001000", "pdu_power--sum": "1004000"}, "20101": {"crac_t-of-return-air": "1005000", "crac_t-of-air-supply": "1006000", "crac_rh-of-air-supply": "1008000", "crac_t-of-incoming-water": "1011000", "crac_on-off-state": "2001000", "crac_fan-rpm": "7001000", "crac_running-state": "7004000", "crac_diff-t-of-snr-air": "7005000", "crac_t-of-inr-water": "7006000", "crac_cooling-capacity--sum": "7007000", "crac_power--sum": "7008000", "crac_cop": "7009000", "crac_opening-percentage-of-water-valve": "7010000"}, "20105": {"crac_t-of-return-air": "1005000", "crac_t-of-air-supply": "1006000", "crac_rh-of-air-supply": "1008000", "crac_on-off-state": "2001000", "crac_fan-rpm": "7004000", "crac_running-state": "7001000", "crac_diff-t-of-snr-air": "7005000"}, "20108": {"ahu-evaporation-unit_working-mode": "7005000", "crac_t-of-return-air": "7007000", "crac_t-of-air-supply": "7006000", "crac_rh-of-air-supply": "7009000", "crac_on-off-state": "2005000", "crac_fan-rpm": "1027000", "crac_running-state": "7005000", "crac_diff-t-of-snr-air": "7008000"}, "20201": {"chiller_electric-current-percent": "1003000", "chiller_t-of-chilled-water-supply": "1004000", "chiller_t-of-chilled-return-water": "1005000", "chiller_t-of-cooled-water-supply": "1006000", "chiller_t-of-cooled-return-water": "1007000", "chiller_small-diff-t-of-condensers": "1012000", "chiller_small-diff-t-of-evaporator": "1013000", "chiller_power--sum": "1019000", "chiller_diff-t-of-chilled-snr-water": "7001000", "chiller_diff-t-of-cooled-snr-water": "7002000", "chiller_cooling-capacity--sum": "7003000", "chiller_cop": "7004000", "chiller_working-state": "2001000", "chiller_working-mode": "2008000"}, "20204": {"cooling-tower_frequency": "1002000", "cooling-tower_t-of-water-supply": "1004000", "cooling-tower_t-of-outdoor-web-bulb": "1010000", "cooling-tower_working-state": "2001000", "cooling-tower_electric-heating-state": "2004000", "cooling-tower_power--sum": "7005000"}, "20207": {"chilled-water-pump_frequency": "1002000", "chilled-water-pump_power--sum": "1006000", "chilled-water-pump_working-state": "2001000"}, "20209": {"cooled-water-pump_frequency": "1002000", "cooled-water-pump_power--sum": "1006000", "cooled-water-pump_working-state": "2001000"}, "20215": {"tes_capacity": "1007000", "tes_backup-time--min": "1011000", "tes-tank_liquid-level--min": "7001000", "tes-tank_t-of-water--max": "7002000", "tes-tank_t-of-water--avg": "7003000"}, "20218": {"water-tank_liquid-level--min": "7001000", "water_backup-time--min": "7002000"}, "20219": {"plate-heat-exchanger_t-of-chilled-water-supply": "1001000", "plate-heat-exchanger_t-of-chilled-return-water": "1002000", "plate-heat-exchanger_t-of-cooled-water-supply": "1003000", "plate-heat-exchanger_t-of-cooled-return-water": "1004000", "plate-heat-exchanger_diff-t-of-cooled-snr-water": "7001000", "plate-heat-exchanger_diff-t-of-chilled-snr-water": "7002000", "plate-heat-exchanger_cooling-exchange--sum": "7003000", "plate-heat-exchanger_working-state": "7004000"}, "20222": {"cooling-system-unit_working-mode": "2004000"}, "20310": {"chilled-water-pipe_pressure-of-water-supply": "7001000", "chilled-water-pipe_pressure-of-return-water": "7002000", "chilled-water-pipe_t-of-water-supply": "7003000", "chilled-water-pipe_t-of-return-water": "7004000", "chilled-water-pipe_diff-t-of-snr-water": "7005000"}, "20312": {"chilled-water-pipe_t-of-water-supply": "1001000"}, "20313": {"chilled-water-pipe_t-of-return-water": "1001000"}, "20314": {"chilled-water-pipe_pressure-of-water-supply": "1001000"}, "20315": {"chilled-water-pipe_pressure-of-return-water": "1001000"}, "30105": {"water-leak-position": "1001000", "length": "1002000"}, "30110": {"psychrometer_t-of-dry-bulb": "1001000", "psychrometer_t-of-wet-bulb": "1002000"}, "30118": {"thtb-sensor_t": "1001000", "thtb-sensor_rh": "1002000"}, "30119": {"thtb-sensor_t": "1001000", "thtb-sensor_rh": "1002000"}, "90101": {"active-it-power--sum": "1009000", "pue": "1010000", "pdu_load-rate--avg": "1015000", "hv-incoming-switchgear_load-rate--avg": "1018000", "battery_backup-time--min": "1019000", "fuel-tank_fuel--sum": "1020000", "active-power-energy--sum": "1046000", "active-power--sum": "1029000", "active-it-power-energy--sum": "1027000", "water-use": "1031000", "outdoor-t-avg": "1034000", "outdoor-t-max": "1041000", "cold-aisle_t--max": "1014000", "cold-aisle_t--avg": "1037000", "it-load-rate--max": "1040000", "oil_use": "1043000"}, "90102": {"utility-grid_load-rate--max": "1001000", "utility-grid_load-rate--avg": "1002000", "generator_load-rate--max": "1003000", "generator_load-rate--avg": "1004000", "hvdc_load-rate--max": "1007000", "hvdc_load-rate--avg": "1008000", "three-phase-modular-ups_load-rate--max": "1009000", "three-phase-modular-ups_load-rate--avg": "1010000", "ac-sophisticated-rpp_load-rate--max": "1011000", "ac-sophisticated-rpp_load-rate--avg": "1012000", "pipe_pressure-of-chilled-water-supply--max": "1016000", "pipe_pressure-of-return-chilled-water--max": "1017000", "pipe_t-of-chilled-water-supply--max": "1018000", "pipe_t-of-return-chilled-water--max": "1019000", "tes-tank_liquid-level--min": "1020000", "tes-tank_t-of-water--max": "1021000", "tes-tank_t-of-water--avg": "1022000", "water-tank_liquid-level--min": "1023000", "ahu_t-of-air-supply--max": "1024000", "ahu_t-of-return-air--max": "1025000", "fuel-tank_liquid-level--min": "1026000", "hv-incoming-switchgear_power-off--count": "1027000", "hv-incoming-switchgear_power-on--count": "1028000", "generator_power-off--count": "1029000", "generator_working--count": "1030000", "transformer-isolation-switchgear_power-off--count": "1031000", "transformer-isolation-switchgear_power-on--count": "1032000", "three-phase-modular-ups_power-off--count": "1033000", "three-phase-modular-ups_battery-mode--count": "1034000", "three-phase-modular-ups_bypass-mode--count": "1035000", "cooling-system-unit_power-off--count": "1036000", "cooling-system-unit_refrigeration-mode--count": "1037000", "cooling-system-unit_precooling-mode--count": "1038000", "cooling-system-unit_eco-mode--count": "1039000", "water-cooled-chiller_power-off--count": "1040000", "water-cooled-chiller_working--count": "1041000", "cooling-tower_power-off--count": "1042000", "cooling-tower_working--count": "1043000", "chilled-water-pump_power-off--count": "1044000", "chilled-water-pump_working--count": "1045000", "cooled-water-pump_power-off--count": "1046000", "cooled-water-pump_working--count": "1047000", "pipe_diff-t-of-snr-chilled-water--max": "1050000", "rpp_load-rate--max": "1063000", "rpp_load-rate--avg": "1064000", "rpp_power-off--count": "1075000", "rpp_power-on--count": "1076000", "hv-incoming-switchgear_power--sum": "1077000", "active-it-power--sum": "1085000", "pue": "1086000", "psychrometer_t-of-dry-bulb--avg": "1087000", "ahu-evaporation-unit_t-of-air-supply--max": "1090000", "cold-aisle_t--max": "1125000", "cold-aisle_t--avg": "1092000", "cold-aisle_rh--avg": "1093000", "hv-incoming-switchgear_load-rate--avg": "1096000", "utility-grid_load-rate_line-a--avg": "1273000", "utility-grid_load-rate_line-b--avg": "1274000", "ahu-evaporation-unit_spraying-mode--count": "1098000", "ahu-evaporation-unit_spraying-n-dx-mode--count": "1099000", "ahu-evaporation-unit_dry-state-mode--count": "1100000", "ahu-evaporation-unit_working--count": "1101000", "ahu-evaporation-unit_t-of-air-supply--avg": "1102000", "ahu-evaporation-unit_dry-n-dx-mode--count": "1124000", "water-use": "1134000", "active-it-power-energy--sum": "1141000", "generator_total-output-power": "1142000", "active-power--sum": "1143000", "outdoor-t-avg": "1144000", "outdoor-t-max": "1298000", "chiller-system_power--sum": "1147000", "it-load-rate--max": "1166000", "low-voltage-power": "1169000", "active-power-energy--sum": "1123000", "hv-incoming-switchgear_power-energy--sum": "1174000", "psychrometer_t-of-wet-bulb--avg": "1175000", "psychrometer_t-of-dew-point--avg": "1176000", "outdoor-rh-avg": "1177000", "CLF": "1200000", "WCLF": "1201000", "ACLF": "1202000", "PLF": "1203000", "OLF": "1204000", "EER": "1205000", "chiller-system_cop": "1206000", "crac_cop": "1207000", "chiller_power--sum": "1209000", "cooling-tower_power--sum": "1210000", "cooled-water-pump_power--sum": "1211000", "chilled-water-pump_power--sum": "1212000", "crac_power--sum": "1213000", "crac_power-of-it-rooms--sum": "1214000", "crac_power-of-infra-rooms--sum": "1215000", "power-dissipation": "1216000", "ups_power-dissipation": "1217000", "hvdc_power-dissipation": "1218000", "transformer_power-dissipation": "1219000", "power-for-work": "1220000", "power-for-lighting-system": "1221000", "ups_active-power-output--sum": "1222000", "hvdc_active-power-output--sum": "1223000", "utility-grid_power--sum": "1224000", "chiller-system_cooling-capacity--sum": "1225000", "chiller_cooling-capacity--sum": "1226000", "plate-heat-exchanger_cooling-exchange--sum": "1227000", "chiller-system_load-rate": "1228000", "chiller-system_supply-n-demand-ratio-of-cooling-capacity": "1229000", "crac_cooling-capacity--sum": "1230000", "aisles_diff-of-t--avg": "1232000", "crac_working--count": "1233000", "crac_power-off--count": "1234000", "transformer_efficiency": "1235000", "ups_efficiency": "1236000", "hvdc_efficiency": "1237000", "transformer_load-rate--avg": "1238000", "oil_use": "1261000", "power-system_efficiency": "1262000", "load-rate--max": "1268000", "power-index": "1271000", "cooling-index": "1272000", "load-rate--min": "1275000", "ups_load-rate--max": "1276000", "ups_load-rate--avg": "1277000", "tes_capacity": "1278000", "water_capacity": "1279000", "cooling_redundancy": "1280000", "coolingutilization-rate--avg": "1281000", "it-index": "1284000", "transformer_load-rate--max": "2028000", "chiller_working-mode": "2001000", "tes_backup-time--min": "1164000", "water_backup-time--min": "1295000", "underloaded-server-racks-count": "2038000", "overloaded-server-racks-count": "2039000", "overloaded-rack-columns-count": "2040000", "rack-columns_load-rate--max": "2052000", "rpp_load-rate_of_rooms--max": "2053000"}, "90103": {"ac-smart-pdu_load-rate--avg": "1001000", "cold-aisle_t--avg": "1015000", "pdu_load-rate--avg": "1011000", "rpp_input-power--sum": "1012000", "thtb-sensor_t": "1013000", "cold-aisle_rh--avg": "1016000", "crac_power--sum": "1017000", "crac_cooling-capacity--sum": "1019000", "crac_cop": "1020000", "pue": "1021000", "crac_supply-n-demand-ratio-of-cooling-capacity": "1022000", "cooling_utilization-rate": "1025000", "aisles_diff-of-t--avg": "1076000", "crac_power-off--count": "1077000", "crac_working--count": "1078000", "crac_fan-rpm": "1079000", "crac_diff-t-of-snr-air": "1089000", "it-load-rate--max": "1094000", "rack-columns_load-rate--max": "1101000", "rack-columns_load-rate--min": "1102000"}, "90104": {"pdu_load-rate--avg": "1001000", "pdu_power--sum": "1002000"}, "90105": {"ac-smart-pdu_load-rate--avg": "1001000", "ac-smart-pdu_power--sum": "1002000", "pdu_load-rate--avg": "1007000", "pdu_power--sum": "1008000", "server-racks_load-state": "2001000"}}, "pointsDefinitionMap": {"10102": {"2001000": {"onOffStateMappings": ["0=off", "1=on"], "onOffStatesMapping": {"OFF": 0, "ON": 1}, "statesMapping": {"OFF": 0, "ON": 1}}, "2002000": {"onOffStateMappings": ["0=off", "1=on"], "onOffStatesMapping": {"OFF": 0, "ON": 1}, "statesMapping": {"OFF": 0, "ON": 1}}}, "10503": {"2001000": {"statusCodes": ["0=normal", "1=normal", "2=alarm", "3=normal", "4=normal", "5=alarm"]}, "2002000": {"statusCodes": ["0=normal", "1=alarm"]}}, "10807": {"2001000": {"statusCodes": ["0=normal", "1=alarm", "2=warning"]}, "2003000": {"statusCodes": ["0=normal", "1=alarm"]}}, "10901": {"2001000": {"statusCodes": ["0=alarm", "1=normal"]}}, "10903": {"2001000": {"statusCodes": ["0=alarm", "1=normal"]}}, "11101": {"7002000": {"statesMapping": {"OFF": 0, "ON": 1}, "statusCodes": ["0=normal", "1=alarm"]}}, "11102": {"2002000": {"statesMapping": {"OFF": 0, "ON": 1}, "statusCodes": ["0=normal", "1=alarm"]}}, "11103": {"2002000": {"onOffStatesMapping": {"OFF": 0, "ON": 1}, "statesMapping": {"OFF": 0, "ON": 1}, "statusCodes": ["0=normal", "1=alarm"]}}, "90105": {"2001000": {"statesMapping": {"UNDERLOADED": 0, "OVERLOADED": 1}}}}, "common": {"HE1.E": {"scopes": {"monitoring": {"dashboards": {"idc": {"3d": {"showCoolingSystem": false}}}}}}, "homeUrl": "/", "company": "拼多多", "showAHUOnIdcs": [], "WUEIntervalDays": 1, "dashboards": {"idc": {"assets": {"backgroundImageUrl": {"fallback": "/images/3d/goc/webgl.jpg", "HE1": "/images/3d/goc/webgl-he1.jpg"}}}}, "scopes": {"monitoring": {"dashboards": {"idc": {"3d": {"showCoolingSystem": true}}}}, "ticket": {"events": {"showRacksImpacts": false, "isTypeRequired": false, "features": {"title": "disabled", "northbound": "disabled"}}, "changes": {"features": {"version": "both", "customerAgreement": "optional"}}, "riskRegisters": {"features": {"priority": "disabled"}}, "patrols": {"features": {"allowCreateRepairTicket": false}}, "maintenances": {"features": {"allowCreateRepairTicket": false}}}}}, "topologyConfigs": {"powerSystem": {"visiblePredefinedDeviceTypes": ["generator", "generator-set-incoming-cabinet", "generator-set-outgoing-cabinet", "generator-incoming-cabinet", "generator-set_pt-switchgear", "generator-set_dummy-load_incoming-switchgear", "generator-set_dummy-load_outgoing-switchgear", "hv-isolating-cabinet", "hv-incoming-switchgear", "hv-metering-cabinet", "hv-outgoing-cabinet", "hv-bus-tie-cabinet", "hv-pt-cabinet", "transformer-repair-isolating-cabinet", "transformer", "lv-incoming-cabinet", "lv-outgoing-cabinet", "lv-bus-tie-cabinet", "lv-outgoing-cabinet-drawer-switch", "lv_capacitor-compensation-cabinet", "hvdc", "hvdc-rpp", "ups", "ups-outgoing-cabinet", "ups-outgoing-cabinet-drawer-switch", "ups-incoming-cabinet-drawer-switch", "rpp", "ahu-evaporation-unit--dx", "crac", "chiller", "cooling-tower-pdu", "cooled-water-charge-pump-pdu", "chilled-water-pump-pdu", "cooled-water-pump-pdu", "constant-pressure-water-supply-system", "chemical-dosing-system", "crac-pdu", "dedicated-outdoor-air-system_devices-pdu", "fire-fighting-system_devices-pdu", "power-system_devices-pdu", "lighting-control-system_switchboard", "chiller-pdu", "power-system_devices-switchboard", "crac-switchboard", "fire-fighting-system_devices-switchboard", "secondary-chilled-water-pump-pdu", "feed-water-pump-pdu", "lighting-control-system_pdu", "ats_pdu", "mdc", "active-filter"], "elementConfigMappings": {"10101": {"type": "image", "thumbnail": "/images/devices/switchgear-without-breaker-n-ground-wire/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1011000} > 0 and ${1012000} > 0 and ${1013000} > 0", "noOutput": "${1011000} == 0 and ${1012000} == 0 and ${1013000} == 0"}}, "10102": {"type": "image", "thumbnail": "/images/devices/switchgear-without-ground-wire/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2003000} == 1 and ${2004000} == 0", "handcartOff": "${2003000} == 0 and ${2004000} == 1"}}, "10103": {"type": "image", "thumbnail": "/images/devices/hv-metering-cabinet/no-data.png", "size": [28.041884816753928, 104], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": null, "electricityRelatedPointMap": {"working": "0 == 1", "notWorking": "1 == 0"}}, "10104": {"type": "image", "thumbnail": "/images/devices/ptc/no-data.png", "size": [88, 167], "anchorPointsConfig": [[0.56, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "notWorking": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10105": {"type": "image", "thumbnail": "/images/devices/switchgear/1x/no-data.png", "size": [64, 173.91304347826087], "anchorPointsConfig": [[0.75, 0], [0.75, 0.5714285714285714]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2004000} == 1 and ${2005000} == 0", "handcartOff": "${2004000} == 0 and ${2005000} == 1", "protectiveEarthOn": "${2003000} == 1", "protectiveEarthOff": "${2003000} == 0"}}, "10106": {"type": "image", "thumbnail": "/images/devices/switchgear-without-ground-wire/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2002000} == 1", "notWorking": "${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2003000} == 1", "handcartOff": "${2003000} == 0", "connected": "2002000 == 1"}}, "10107": {"type": "image", "thumbnail": "/images/devices/switchgear/1x/no-data.png", "size": [64, 173.91304347826087], "anchorPointsConfig": [[0.75, 0], [0.75, 0.5714285714285714]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2003000} == 1 and ${2004000} == 0", "handcartOff": "${2003000} == 0 and ${2004000} == 1", "protectiveEarthOn": "${2005000} == 1", "protectiveEarthOff": "${2005000} == 0"}}, "10116": {"type": "image", "thumbnail": "/images/devices/switchgear-without-ground-wire/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2003000} == 1 and ${2004000} == 0", "handcartOff": "${2003000} == 0 and ${2004000} == 1"}}, "10201": {"type": "image", "thumbnail": "/images/devices/transformer/no-data.png", "size": [100, 96], "anchorPointsConfig": [[0.5, 0], [0.5, 0.65]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": null, "electricityRelatedPointMap": {"working": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "notWorking": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "outputting": "${1011000} > 0 and ${1012000} > 0 and ${1013000} > 0", "noOutput": "${1011000} == 0 and ${1012000} == 0 and ${1013000} == 0"}}, "10301": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10302": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10303": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0", "connected": "${2001000} == 1"}}, "10304": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2005000", "electricityRelatedPointMap": {"working": "${1020000} > 0 or ${1021000} > 0 or ${1022000} > 0", "notWorking": "${1020000} == 0 and ${1021000} == 0 and ${1022000} == 0", "outputting": "1 == 0", "noOutput": "1 == 0"}}, "10305": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${1004000} > 0 or ${1005000} > 0 or ${1006000} > 0", "notWorking": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "outputting": "1 == 0", "noOutput": "1 == 0"}}, "10306": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10307": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10308": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 0", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 1"}}, "10309": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10310": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 0", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 1"}}, "10311": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 0", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 1"}}, "10312": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 0", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 1"}}, "10313": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10314": {"type": "image", "thumbnail": "/images/devices/electric-pdu/electric/no-data.png", "size": [75.6, 102.4], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 0", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 1"}}, "10315": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 0", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 1"}}, "10316": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10317": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10319": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10320": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10322": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2004000} == 1 or ${2006000} == 1", "notWorking": "${2004000} == 0 and ${2006000} == 0", "outputting": "${2004000} == 1 or ${2006000} == 1", "noOutput": "${2004000} == 0 and ${2006000} == 0"}}, "10323": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10324": {"type": "image", "thumbnail": "/images/devices/electric-switchboard/electric/no-data.png", "size": [102.4, 84.8], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001001", "electricityRelatedPointMap": {"working": "${2001001} == 1", "notWorking": "${2001001} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0 and ${2001001} == 1", "noOutput": "(${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0) or ${2001001} == 0"}}, "10325": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001001", "electricityRelatedPointMap": {"working": "${2001001} == 1", "notWorking": "${2001001} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0 and ${2001001} == 1", "noOutput": "(${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0) or ${2001001} == 0"}}, "10402": {"type": "image", "thumbnail": "/images/devices/generator/1x/no-data.png", "size": [50, 50], "anchorPointsConfig": [[0.5, 1]], "anchorPointsPlacementConfig": ["bottom"], "statusPointCode": "2006000", "electricityRelatedPointMap": {"working": "${2006000} == 1", "notWorking": "${2006000} == 0", "outputting": "${1027000} > 0 and ${1028000} > 0 and ${1029000} > 0", "noOutput": "${1027000} == 0 and ${1028000} == 0 and ${1029000} == 0"}}, "10407": {"type": "image", "thumbnail": "/images/devices/switchgear-without-ground-wire/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2003000} == 1 and ${2004000} == 0", "handcartOff": "${2003000} == 0 and ${2004000} == 1"}}, "10408": {"type": "image", "thumbnail": "/images/devices/switchgear/1x/no-data.png", "size": [64, 173.91304347826087], "anchorPointsConfig": [[0.75, 0], [0.75, 0.5714285714285714]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2003000} == 1 and ${2004000} == 0", "handcartOff": "${2003000} == 0 and ${2004000} == 1", "protectiveEarthOn": "${2009000} == 1", "protectiveEarthOff": "${2009000} == 0"}}, "10409": {"type": "image", "thumbnail": "/images/devices/ptc/no-data.png", "size": [88, 167], "anchorPointsConfig": [[0.56, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "notWorking": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10410": {"type": "image", "thumbnail": "/images/devices/switchgear/1x/no-data.png", "size": [64, 173.91304347826087], "anchorPointsConfig": [[0.75, 0], [0.75, 0.5714285714285714]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${2001000} == 0 and ${2002000} == 1", "notWorking": "${2001000} == 1 and ${2002000} == 0", "outputting": "${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0", "noOutput": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "handcartOn": "${2004000} == 1 and ${2005000} == 0", "handcartOff": "${2004000} == 0 and ${2005000} == 1", "protectiveEarthOn": "${2003000} == 1", "protectiveEarthOff": "${2003000} == 0"}}, "10426": {"type": "image", "thumbnail": "/images/devices/switchgear/1x/no-data.png", "size": [64, 173.91304347826087], "anchorPointsConfig": [[0.75, 0], [0.75, 0.5714285714285714]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2002000", "electricityRelatedPointMap": {"working": "${1004000} > 0 or ${1005000} > 0 or ${1006000} > 0", "notWorking": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0", "outputting": "1 == 0", "noOutput": "1 == 0", "handcartOn": "${2004000} == 1 and ${2005000} == 0", "handcartOff": "${2004000} == 0 and ${2005000} == 1", "protectiveEarthOn": "${2008000} == 1", "protectiveEarthOff": "${2008000} == 0"}}, "10501": {"type": "image", "thumbnail": "/images/devices/ups/1x/no-data.png", "size": [90, 103], "anchorPointsConfig": [[0.3333333333333333, 0], [0.6666666666666666, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "2001000 != 3", "notWorking": "2001000 == 3", "outputting": "${1003000} > 0", "noOutput": "${1003000} == 0"}}, "10502": {"type": "image", "thumbnail": "/images/devices/ups/1x/no-data.png", "size": [90, 103], "anchorPointsConfig": [[0.3333333333333333, 0], [0.6666666666666666, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "(${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0) or (${1016000} > 0 and ${1017000} > 0 and ${1018000} > 0)", "notWorking": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0 and ${1016000} == 0 and ${1017000} == 0 and ${1018000} == 0", "outputting": "${1027000} > 0 and ${1028000} > 0 and ${1029000} > 0", "noOutput": "${1027000} == 0 and ${1028000} == 0 and ${1029000} == 0"}}, "10503": {"type": "image", "thumbnail": "/images/devices/ups/1x/no-data.png", "size": [90, 103], "anchorPointsConfig": [[0.3333333333333333, 0], [0.6666666666666666, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "(${1004000} > 0 and ${1005000} > 0 and ${1006000} > 0) or (${1022000} > 0 and ${1023000} > 0 and ${1024000} > 0)", "notWorking": "${1004000} == 0 and ${1005000} == 0 and ${1006000} == 0 and ${1022000} == 0 and ${1023000} == 0 and ${1024000} == 0", "outputting": "${1036000} > 0 and ${1037000} > 0 and ${1038000} > 0", "noOutput": "${1036000} == 0 and ${1037000} == 0 and ${1038000} == 0"}}, "10506": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10507": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "noOutput": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10509": {"type": "image", "thumbnail": "/images/devices/lv-switch-cabinet/1x/no-data.png", "size": [32, 104], "anchorPointsConfig": [[0.5, 0], [0.5, 1]], "anchorPointsPlacementConfig": ["top", "bottom"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0 and ${2001000} == 1", "noOutput": "(${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0) or ${2001000} == 0"}}, "10601": {"type": "image", "thumbnail": "/images/devices/hvdc/electric/1x/no-data.png", "size": [90, 122], "anchorPointsConfig": [[0.3333333333333333, 0], [0.6666666666666666, 0], [0.3333333333333333, 1], [0.6666666666666666, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom", "bottom"], "statusPointCode": "2007000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0", "outputting": "${1011000} > 0", "noOutput": "${1011000} == 0"}}, "10901": {"type": "image", "thumbnail": "/images/devices/rpp/200w/no-data.png", "size": [50, 99.25], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${1001000} > 0 and ${1002000} > 0 and ${1003000} > 0", "notWorking": "${1001000} == 0 and ${1002000} == 0 and ${1003000} == 0"}}, "10903": {"type": "image", "thumbnail": "/images/devices/rpp/200w/no-data.png", "size": [50, 99.25], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2002000} == 1", "notWorking": "${2002000} == 0"}}, "11401": {"type": "image", "thumbnail": "/images/devices/mdc/electric/no-data.png", "size": [104, 120], "anchorPointsConfig": [[0.33, 0], [0.66, 0], [0.33, 1], [0.66, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom", "bottom"], "statusPointCode": null, "electricityRelatedPointMap": {"working": "${1007000} > 0 or ${1008000} > 0 or ${1009000} > 0 or ${1032000} > 0 or ${1033000} > 0 or ${1034000} > 0", "notWorking": "${1007000} == 0 and ${1008000} == 0 and ${1009000} == 0 and ${1032000} == 0 and ${1033000} == 0 and ${1034000} == 0", "outputting": "1 == 0", "noOutput": "1 == 0"}}, "20101": {"type": "image", "thumbnail": "/images/devices/crac/electric/SVG/no-data.svg", "size": [90, 103], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20102": {"type": "image", "thumbnail": "/images/devices/crac/electric/SVG/no-data.svg", "size": [90, 103], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20105": {"type": "image", "thumbnail": "/images/devices/crac/electric/SVG/no-data.svg", "size": [90, 103], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20108": {"type": "image", "thumbnail": "/images/devices/ahu-evaporation-unit--dx/1x/no-data.png", "size": [86, 115], "anchorPointsConfig": [[0.2, 0], [0.4, 0], [0.6, 0], [0.8, 0]], "anchorPointsPlacementConfig": ["top", "top", "top", "top"], "statusPointCode": "2005000", "electricityRelatedPointMap": {"working": "${2038000} == 0", "notWorking": "${2038000} == 1"}}, "20201": {"type": "image", "thumbnail": "/images/devices/chiller/electric/SVG/no-data.svg", "size": [130, 80], "anchorPointsConfig": [[0.6, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20202": {"type": "image", "thumbnail": "/images/devices/chiller/electric/SVG/no-data.svg", "size": [130, 80], "anchorPointsConfig": [[0.6, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20214": {"type": "image", "thumbnail": "/images/devices/constant-pressure-water-supply-system/electric/no-data.png", "size": [100.2, 98.2], "anchorPointsConfig": [[0.45, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20216": {"type": "image", "thumbnail": "/images/devices/chemical-dosing-system/electric/no-data.png", "size": [100.4, 86.6], "anchorPointsConfig": [[0.5, 0]], "anchorPointsPlacementConfig": ["top"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}}}, "hvacSystem": {"visiblePredefinedDeviceTypes": ["chiller", "cooling-tower", "pump", "tes-tank", "water-tank", "plate-heat-exchanger", "valve", "t-sensor", "water-pressure-sensor", "single-use-flow-tube-sensor", "bidirectional-flow-tube-sensor", "water-pressure-diff-sensor", "constant-pressure-water-supply-system", "chemical-dosing-system", "bypass-filtration-system", "water-meter", "thermometer", "manometer"], "elementConfigMappings": {"20201": {"type": "image", "thumbnail": "/images/devices/chiller/hvac/PNG/no-data.png", "size": [156, 156], "anchorPointsConfig": [[0, 0.375], [0, 0.6], [1, 0.375], [1, 0.6]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20202": {"type": "image", "thumbnail": "/images/devices/chiller/hvac/PNG/no-data.png", "size": [156, 156], "anchorPointsConfig": [[0, 0.375], [0, 0.6], [1, 0.375], [1, 0.6]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20203": {"type": "image", "thumbnail": "/images/devices/chiller/hvac/PNG/no-data.png", "size": [156, 156], "anchorPointsConfig": [[0, 0.375], [0, 0.6], [1, 0.375], [1, 0.6]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20204": {"type": "image", "thumbnail": "/images/devices/cooling-tower/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0, 0.375], [0, 0.7], [1, 0.375], [1, 0.7]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "statusPointCode": "2003001", "electricityRelatedPointMap": {"working": "${2003001} == 1", "notWorking": "${2003001} == 0"}}, "20207": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20208": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20209": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20210": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20211": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20212": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20213": {"type": "image", "thumbnail": "/images/devices/pump/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.245, 0], [1, 0.13]], "anchorPointsPlacementConfig": ["top", "right"], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20214": {"type": "image", "thumbnail": "/images/devices/constant-pressure-water-supply-system/hvac/no-data.png", "size": [100.2, 98.2], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20215": {"type": "image", "thumbnail": "/images/devices/tes-tank/hvac/PNG/no-data.png", "size": [230, 673], "anchorPointsConfig": [[0, 0.3], [0, 0.57], [1, 0.3], [1, 0.57]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20216": {"type": "image", "thumbnail": "/images/devices/chemical-dosing-system/hvac/no-data.png", "size": [100.4, 86.6], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": "2001000", "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "20218": {"type": "image", "thumbnail": "/images/devices/water-tank/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0, 0.225], [0.333, 0], [0.777, 0], [1, 0.333]], "anchorPointsPlacementConfig": ["top", "top", "top", "top"], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20219": {"type": "image", "thumbnail": "/images/devices/plate-heat-exchanger/hvac/PNG/no-data.png", "size": [100, 100], "anchorPointsConfig": [[0.09, 0.275], [0.09, 0.775], [0.815, 0.275], [0.815, 0.775]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20221": {"type": "image", "thumbnail": "/images/devices/bypass-filtration-system/hvac/no-data.png", "size": [95.2, 92.8], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20301": {"type": "image", "thumbnail": "/images/devices/valve/hvac/PNG/no-data.png", "size": [36, 36], "anchorPointsConfig": [[0.23, 0.825], [0.7, 0.825]], "anchorPointsPlacementConfig": ["left", "right"], "statusPointCode": "7001000", "electricityRelatedPointMap": {"working": "${1001000} > 0", "notWorking": "${1001000} == 0"}}, "20302": {"type": "image", "thumbnail": "/images/devices/valve/hvac/PNG/no-data.png", "size": [36, 36], "anchorPointsConfig": [[0.23, 0.825], [0.7, 0.825]], "anchorPointsPlacementConfig": ["left", "right"], "statusPointCode": "7001000", "electricityRelatedPointMap": {"working": "${7001000} == 1", "notWorking": "${7001000} == 0"}}, "20305": {"type": "image", "thumbnail": "/images/devices/thermometer/hvac/no-data.png", "size": [25, 31], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20306": {"type": "image", "thumbnail": "/images/devices/manometer/hvac/no-data.png", "size": [25, 31], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20307": {"type": "image", "thumbnail": "/images/devices/water-meter/hvac/no-data.png", "size": [47, 27], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20312": {"type": "image", "thumbnail": "/images/devices/thermometer/hvac/no-data.png", "size": [25, 31], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20313": {"type": "image", "thumbnail": "/images/devices/thermometer/hvac/no-data.png", "size": [25, 31], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20314": {"type": "image", "thumbnail": "/images/devices/manometer/hvac/no-data.png", "size": [25, 31], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "20315": {"type": "image", "thumbnail": "/images/devices/manometer/hvac/no-data.png", "size": [25, 31], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "30101": {"type": "image", "thumbnail": "/images/devices/sensor/hvac/no-data.png", "size": [31, 24], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "30107": {"type": "image", "thumbnail": "/images/devices/sensor/hvac/no-data.png", "size": [31, 24], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "30108": {"type": "image", "thumbnail": "/images/devices/sensor/hvac/no-data.png", "size": [31, 24], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "30109": {"type": "image", "thumbnail": "/images/devices/sensor/hvac/no-data.png", "size": [31, 24], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "30111": {"type": "image", "thumbnail": "/images/devices/sensor/hvac/no-data.png", "size": [31, 24], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statusPointCode": null, "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}}}, "fuelSystem": {"visiblePredefinedDeviceTypes": ["hv-generator", "lv-generator", "fuel-day-tank", "fuel-tank", "generator-set_fuel-supply-pump", "generator-set_fuel-return-pump", "generator-set_cut-off-valve", "fuel-supply-valve", "fuel-return-valve", "fuel-supply-pdu"], "elementConfigMappings": {"10401": {"type": "image", "thumbnail": "/images/devices/generator-set/fuel-system/no-data.png", "size": [256.5, 123.5], "anchorPointsConfig": [[0.5, 0.095], [1, 0.5], [0.5, 1], [0.03, 0.6]], "anchorPointsPlacementConfig": ["top", "right", "bottom", "left"], "electricityRelatedPointMap": {"working": "${2006000} == 1", "notWorking": "${2006000} == 0"}}, "10402": {"type": "image", "thumbnail": "/images/devices/generator-set/fuel-system/no-data.png", "size": [256.5, 123.5], "anchorPointsConfig": [[0.5, 0.095], [1, 0.5], [0.5, 1], [0.03, 0.6]], "anchorPointsPlacementConfig": ["top", "right", "bottom", "left"], "electricityRelatedPointMap": {"working": "${2006000} == 1", "notWorking": "${2006000} == 0"}}, "10403": {"type": "image", "thumbnail": "/images/devices/fuel-day-tank/fuel-system/no-data.png", "size": [100, 133.68146214099218], "anchorPointsConfig": [[0.35, 0.05], [0.675, 0.05], [0.375, 0.95]], "anchorPointsPlacementConfig": ["top", "bottom", "bottom"], "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "10404": {"type": "image", "thumbnail": "/images/devices/fuel-tank/fuel-system/no-data.png", "size": [256.5, 117.5], "anchorPointsConfig": [[0.5, 0], [1, 0.5], [0.5, 1], [0, 0.5]], "anchorPointsPlacementConfig": ["top", "right", "bottom", "left"], "electricityRelatedPointMap": {"working": "1 == 1", "notWorking": "1 == 0"}}, "10405": {"type": "image", "thumbnail": "/images/devices/fuel-supply-pdu/fuel-system/no-data.png", "size": [118.5, 256.5], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "electricityRelatedPointMap": {"working": "${2001000} == 0", "notWorking": "${2001000} == 1"}}, "10414": {"type": "group", "thumbnail": "/images/devices/fuel-pump/fuel-system/no-data.png", "size": [119, 170.66666666666666], "anchorPointsConfig": [[0.75, 0], [0.9, 0.75], [0.75, 1], [0, 0.75]], "anchorPointsPlacementConfig": ["top", "right", "bottom", "left"], "parts": [{"type": "pump", "thumbnail": "/images/devices/fuel-pump/fuel-system/parts/pumps/default.png", "x": 24, "y": 54, "width": 30, "height": 30}], "statePointsExpressions": {"pumps": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "10428": {"type": "image", "thumbnail": "/images/devices/fuel-valve/fuel-system/no-data.png", "size": [40, 20.2729044834308], "anchorPointsConfig": [[0.575, 0.5], [0, 0.5]], "anchorPointsPlacementConfig": ["right", "left"], "electricityRelatedPointMap": {"working": "${2003000} == 1", "notWorking": "${2003000} == 0"}}, "10429": {"type": "image", "thumbnail": "/images/devices/fuel-valve/fuel-system/no-data.png", "size": [40, 20.2729044834308], "anchorPointsConfig": [[0.575, 0.5], [0, 0.5]], "anchorPointsPlacementConfig": ["right", "left"], "electricityRelatedPointMap": {"working": "${2003000} == 1", "notWorking": "${2003000} == 0"}}, "10430": {"type": "group", "thumbnail": "/images/devices/fuel-pump/fuel-system/no-data.png", "size": [119, 170.66666666666666], "anchorPointsConfig": [[0.75, 0], [0.9, 0.75], [0.75, 1], [0, 0.75]], "anchorPointsPlacementConfig": ["top", "right", "bottom", "left"], "parts": [{"type": "pump", "thumbnail": "/images/devices/fuel-pump/fuel-system/parts/pumps/default.png", "x": 24, "y": 54, "width": 30, "height": 30}], "statePointsExpressions": {"pumps": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "electricityRelatedPointMap": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}, "10432": {"type": "image", "thumbnail": "/images/devices/valve/hvac/PNG/no-data.png", "size": [36, 36], "anchorPointsConfig": [[0.23, 0.825], [0.7, 0.825]], "anchorPointsPlacementConfig": ["left", "right"], "electricityRelatedPointMap": {"working": "${2003000} == 1", "notWorking": "${2003000} == 0"}}}}, "fireFightingSystem": {"elementConfigMappings": {"30206": {"type": "image", "thumbnail": "/images/devices/door-access-controller/ffs/closed.svg", "size": [32, 32], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statePointsExpressions": {"opened": "${2001000} == 1", "closed": "${2001000} == 0"}}, "30221": {"type": "image", "thumbnail": "/images/devices/bullet-cctv-camera/ffs/working.svg", "size": [16, 16], "anchorPointsConfig": [], "anchorPointsPlacementConfig": []}, "40105": {"type": "image", "thumbnail": "/images/devices/smoke-detector/ffs/working.svg", "size": [16, 16], "anchorPointsConfig": [], "anchorPointsPlacementConfig": []}, "40106": {"type": "image", "thumbnail": "/images/devices/smoke-detector/ffs/working.svg", "size": [16, 16], "anchorPointsConfig": [], "anchorPointsPlacementConfig": []}, "40401": {"type": "image", "thumbnail": "/images/devices/smoke-detector/ffs/working.svg", "size": [16, 16], "anchorPointsConfig": [], "anchorPointsPlacementConfig": []}}}, "liquidCoolingSystem": {"visiblePredefinedDeviceTypes": ["cdu", "air-economizer", "ac-smart-pdu", "inter-column-sophisticated-air-cooled-crac"], "elementConfigMappings": {"11103": {"type": "image", "thumbnail": "/images/devices/pdu/liquid-cooling-system/device/default.png", "size": [32, 32], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "statePointsExpressions": {"self": {"working": "${2002000} == 1", "notWorking": "${2002000} == 0"}}}, "20105": {"type": "group", "thumbnail": "/images/devices/crac/liquid-cooling-system/device/default.png", "size": [72, 200], "anchorPointsConfig": [], "anchorPointsPlacementConfig": [], "parts": [{"type": "fan", "thumbnail": "/images/devices/air-economizer/liquid-cooling-system/parts/fans/default.png", "x": 20, "y": 84, "width": 30, "height": 30}], "statePointsExpressions": {"fans": {"working": "${2001000} == 1", "notWorking": "${2001000} == 0"}}}, "20226": {"type": "group", "thumbnail": "/images/devices/cdu/liquid-cooling-system/device/default.png", "size": [72, 200], "anchorPointsConfig": [[0.3333333333333333, 0], [0.6666666666666666, 0], [0.3333333333333333, 1], [0.6666666666666666, 1]], "anchorPointsPlacementConfig": ["top", "top", "bottom", "bottom"], "parts": [{"type": "pump", "thumbnail": "/images/devices/air-economizer/liquid-cooling-system/parts/pumps/default.png", "x": 20, "y": 112, "width": 30, "height": 30}], "statePointsExpressions": {"pumps": {"working": "${2003000} == 1", "notWorking": "${2003000} == 0"}}}, "20227": {"type": "group", "thumbnail": "/images/devices/air-economizer/liquid-cooling-system/device/default.png", "size": [120, 120], "anchorPointsConfig": [[0, 0.23333333333333334], [0, 0.7666666666666667], [1, 0.23333333333333334], [1, 0.7666666666666667]], "anchorPointsPlacementConfig": ["left", "left", "right", "right"], "parts": [{"type": "fan", "thumbnail": "/images/devices/air-economizer/liquid-cooling-system/parts/fans/default.png", "x": 44, "y": 14, "width": 32, "height": 32}, {"type": "sprayer", "thumbnail": "/images/devices/air-economizer/liquid-cooling-system/parts/sprayers/default.png", "x": 12, "y": 60, "width": 96, "height": 24}, {"type": "pump", "thumbnail": "/images/devices/air-economizer/liquid-cooling-system/parts/pumps/default.png", "x": 44, "y": 86, "width": 30, "height": 30}], "statePointsExpressions": {"fans": {"working": "${2003000} == 1", "notWorking": "${2003000} == 0"}, "pumps": {"working": "${2004000} == 1", "notWorking": "${2004000} == 0"}, "sprayers": {"working": "${2005000} == 1", "notWorking": "${2005000} == 0"}}}, "90105": {"type": "image", "thumbnail": "/images/devices/rack/liquid-cooling-system/device/default.png", "size": [72, 200], "anchorPointsConfig": [[0.2, 1], [0.4, 1], [0.6, 1], [0.8, 1]], "anchorPointsPlacementConfig": ["bottom", "bottom", "bottom", "bottom"]}}}}, "expressionVariablePointsMap": {"10901": {"7002000": ["1012000", "1013000", "1014000"], "7003000": ["1001000", "1002000", "1003000"], "7004000": ["1007000", "1008000", "1009000"], "7005000": ["1012000", "1013000", "1014000"]}}}