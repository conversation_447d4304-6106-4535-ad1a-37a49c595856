import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import { cloneDeep, merge } from 'lodash-es';

import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { Config, DeviceTypePointsDefinitions } from '@manyun/dc-brain.config.base';
import configFromPkg from '@manyun/dc-brain.config.base/configs/default.json';

import currentConfig from './configs/default.json';

export type ConfigSliceState = {
  configMap: {
    /**
     * 当前租户的配置
     */
    current: Config;
  };
};

type ConfigSliceCaseReducers = {
  setCurrent: CaseReducer<ConfigSliceState, PayloadAction<Config>>;
  updatePointsDefinitionMap: CaseReducer<
    ConfigSliceState,
    PayloadAction<DeviceTypePointsDefinitions>
  >;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const current: any = env.BUILD_ENV === 'local' ? currentConfig : cloneDeep(configFromPkg);

export const configSlice = createSlice<ConfigSliceState, ConfigSliceCaseReducers, 'config'>({
  name: 'config',
  initialState: {
    configMap: {
      current,
    },
  },
  reducers: {
    setCurrent(state, { payload: newConfigs }) {
      state.configMap.current = newConfigs;
    },
    updatePointsDefinitionMap(state, { payload }) {
      merge(state.configMap.current.pointsDefinitionMap, payload);
    },
  },
});
