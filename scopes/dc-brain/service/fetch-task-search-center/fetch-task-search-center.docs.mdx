---
description: 'A fetchTaskSearchCenter HTTP API service.'
labels: ['service', 'http']
---

检索查询接口

## Usage

### Browser

```ts
import { fetchTaskSearchCenter } from '@manyun/dc-brain.service.fetch-task-search-center';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchTaskSearchCenterService } from '@manyun/dc-brain.service.fetch-task-search-center/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchTaskSearchCenterService.from(nodeRequest);
```
