/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-1-3
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * query词
   */
  query: string;
  /**
   * 开始时间（时间戳）
   */
  startTime: any;
  /**
   * 结束时间（时间戳）
   */
  endTime: any;
  /**
   * 业务单类型
   */
  bizType: string;
  /**
   * 滚动查询字段，首页传null即可，后续取最后一条记录的值原封不动传给后端即可
   */
  searchAfter: Record<string, any>[];
  /**
   * 每页条数
   */
  pageSize: any;
};

export type ApiResponseData = {
  /**
   * 工单/业务单编号
   */
  bizId: string;
  /**
   * 工单类型
   */
  type: string;
  /**
   * 类型
   */
  subType: string;
  /**
   * 标题
   */
  title: string;
  /**
   * 位置
   */
  position: string;
  /**
   * 专业
   */
  category: string;
  /**
   * 等级
   */
  level: string;
  /**
   * 状态
   */
  status: string;
  /**
   * 创建人id
   */
  creatorId: string;
  /**
   * 创建人
   */
  creatorName: string;
  /**
   * 创建时间
   */
  creatorDate: any;
  /**
   * 值班日期
   */
  date: any;
  /**
   * 滚动查询字段
   */
  searchAfter: Record<string, any>;
};

export type ApiResponse = BackendResponse<ApiResponseData>;
