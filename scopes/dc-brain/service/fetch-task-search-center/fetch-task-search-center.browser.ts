/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-1-3
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-task-search-center.js';
import type { ApiArgs, ApiResponse } from './fetch-task-search-center.type.js';

/**
 * @param args
* @returns
 */
export function fetchTaskSearchCenter(args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
