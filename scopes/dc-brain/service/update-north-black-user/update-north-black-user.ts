/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './update-north-black-user.type';

const endpoint = '/dccm/north/user/black/update';

/**
 * @see [新增/删除黑名单配置](http://172.16.0.17:13000/project/170/interface/api/19266)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery[]): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ[]>(
      endpoint,
      svcQuery
    );
    return { error, data, ...rest };
  };
}
