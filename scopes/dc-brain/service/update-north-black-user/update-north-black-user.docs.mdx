---
description: 'A updateNorthBlackUser HTTP API service.'
labels: ['service', 'http']
---

## 概述

TODO

## 使用

### Browser

```ts
import { updateNorthBlackUser } from '@manyun/dc-brain.service.update-north-black-user';

const { error, data } = await updateNorthBlackUser('success');
const { error, data } = await updateNorthBlackUser('error');
```

### Node

```ts
import { updateNorthBlackUser } from '@manyun/dc-brain.service.update-north-black-user/dist/index.node';

const { data } = await updateNorthBlackUser('success');
const { error, data } = await updateNorthBlackUser('error');
```
