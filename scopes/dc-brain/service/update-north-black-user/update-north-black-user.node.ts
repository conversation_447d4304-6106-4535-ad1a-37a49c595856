/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-north-black-user';
import type { SvcQuery, SvcRespData } from './update-north-black-user.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function updateNorthBlackUser(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
