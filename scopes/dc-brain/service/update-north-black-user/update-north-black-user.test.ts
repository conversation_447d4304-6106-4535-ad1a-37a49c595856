/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateNorthBlackUser as webService } from './update-north-black-user.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ id: 17 });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ id: 1 });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await webService({ id: 17 });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await webService({ id: 1 });

  expect(data).toBe(null);
});
