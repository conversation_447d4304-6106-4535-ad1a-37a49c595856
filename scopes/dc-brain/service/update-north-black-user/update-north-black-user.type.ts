/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BlackUser = {
  id?: number | null | undefined;
  type?: number;
  channelId?: number;
  channelName?: string;
  idcTag?: string;
  blockTag?: string;
  roomTag?: string;
  gridTag?: string;
  gridColumnTag?: string;
  deviceGuid?: string;
  deviceType?: string;
};

export type SvcQuery = ApiQ;

export type ApiQ = BlackUser[];

export type RequestRespData = SvcRespData;

export type SvcRespData = number | null;

export type ApiR = WriteResponse;
