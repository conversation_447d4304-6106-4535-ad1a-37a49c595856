/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './update-columns.type';

const endpoint = '/pm/store/columns/save';

/**
 * 更新用户设置的列
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/19947)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (updateD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...updateD };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
