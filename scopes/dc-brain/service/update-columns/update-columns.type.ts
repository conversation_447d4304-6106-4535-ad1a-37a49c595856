/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Column = {
  fieldName: string;
  seqId: number;
  fixed: 'left' | 'right' | null;
  show: boolean;
  disable: boolean;
};

export type SvcQuery = {
  userId: number;
  type: string;
  columns: Column[];
};

export type SvcRespData = boolean;

export type RequestRespData = boolean;

export type ApiQ = {
  userId: number;
  type: string;
  columns: Column[];
};

export type ApiR = WriteResponse;
