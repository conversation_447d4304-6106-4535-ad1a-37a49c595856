/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-columns';
import type { SvcQuery, SvcRespData } from './update-columns.type';

const executor = getExecutor(nodeRequest);

/**
 * @param updateD
 * @returns
 */
export function updateColumns(updateD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(updateD);
}
