/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type YourDataModel = string;

export type SvcRespData = {
  data: YourDataModel[];
  total: number;
};

export type RequestRespData = {
  data: YourDataModel[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<YourDataModel[] | null>;
