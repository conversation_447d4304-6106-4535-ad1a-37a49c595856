/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-north-protocol';
import type { SvcQuery, SvcRespData } from './fetch-north-protocol.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchNorthProtocol(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
