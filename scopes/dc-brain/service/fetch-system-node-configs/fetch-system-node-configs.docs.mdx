---
description: 'A fetchSystemNodeConfigs HTTP API service.'
labels: ['service', 'http', fetch-system-node-configs]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchSystemNodeConfigs } from '@manyun/dc-brain.service.fetch-system-node-configs';

const { error, data } = await fetchSystemNodeConfigs('success');
const { error, data } = await fetchSystemNodeConfigs('error');
```

### Node

```ts
import { fetchSystemNodeConfigs } from '@manyun/dc-brain.service.fetch-system-node-configs/dist/index.node';

const { data } = await fetchSystemNodeConfigs('success');

try {
  const { data } = await fetchSystemNodeConfigs('error');
} catch(error) {
  // ...
}
```
