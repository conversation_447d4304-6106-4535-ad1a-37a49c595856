/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-system-node-configs';
import type { SvcQuery, SvcRespData } from './fetch-system-node-configs.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchSystemNodeConfigs(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
