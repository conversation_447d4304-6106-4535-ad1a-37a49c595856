/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-18
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChannelConfig = {
  type: string;
  range: string;
  port: string;
  status: string;
};

export type SvcRespData = {
  data: ChannelConfig[];
  total: number;
};

export type RequestRespData = {
  data: ChannelConfig[] | null;
  total: number;
} | null;

// SvcQuery 和 apiQ相同
export type ApiQ = {
  nodeGuid: string;
  pageSize?: number;
  pageNum?: number;
};

export type ApiR = ListResponse<ChannelConfig[] | null>;
