/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-5
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type NorthBlackUser = {
  id?: number;
  channelId?: number;
  channelName?: string;
  idcTag?: string | null;
  blockTag?: string | null;
  roomTag?: string | null;
  deviceGuid?: string | null;
  deviceTag?: string | null;
  deviceName?: string | null;
  deviceType?: string | null;
  pointCode?: string | null;
  pointName?: string | null;
  pointType?: string | null;
  type?: number;
};
export type SvcQuery = ApiQ;

export type ApiQ = {
  channelId?: number;
  type?: number;
};

export type SvcRespData = {
  data: NorthBlackUser[];
  total: number;
};

export type RequestRespData = {
  data: NorthBlackUser[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<NorthBlackUser[] | null>;
