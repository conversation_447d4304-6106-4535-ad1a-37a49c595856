/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-north-by-condition';
import type { SvcQuery, SvcRespData } from './fetch-north-by-condition.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchNorthByCondition(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
