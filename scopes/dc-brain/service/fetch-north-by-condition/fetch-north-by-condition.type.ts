/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type NorthItem = {
  id: number;
  gmtCreate: string;
  gmtModified: string;
  name: string;
  path: string;
  protocol: string;
  username: string;
  password: string;
  ips: string;
  aggPush: number;
  httpUrl: string;
  socketStatus: boolean;
  description: string;
  idcTag: string;
  blockTag: string;
  config: string | null;
  operatorId: number;
  operatorName: string;
  blockGuidList: number[] | null;
  available: boolean;
  port: number;
};
export type SvcQuery = ApiQ;

export type YourDataModel = NorthItem;

export type SvcRespData = {
  data: YourDataModel[];
  total: number;
};

export type RequestRespData = {
  data: YourDataModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  id?: string;
  ips?: string;
  name?: string;
  path?: string;
  available?: boolean;
  pageNum?: number;
  pageSize?: number;
};

export type ApiR = ListResponse<YourDataModel[] | null>;
