/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-10
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = void;

export type Switches = {
  'global.rsa-encryption-public-key': string;
  'global.page-authorization-check': 'enabled' | 'disabled';
  'global.resource-authorization-check': 'enabled' | 'disabled';
  'monitoring.alarms-tts-service': 'built-in' | 'tts-aliyun';
  'monitoring.ffs-cameras-service': 'enabled' | 'disabled';
};

export type SvcRespData = Switches;

export type RequestRespData = Switches | null;

export type ApiQ = void;

export type ApiR = Response<Switches | null>;
