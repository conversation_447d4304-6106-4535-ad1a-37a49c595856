/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-10
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-switches-from-apollo';
import type { SvcRespData } from './fetch-switches-from-apollo.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function fetchSwitchesFromApollo(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
