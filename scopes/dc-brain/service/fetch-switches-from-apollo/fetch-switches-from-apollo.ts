/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-8-10
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcRespData,
  Switches,
} from './fetch-switches-from-apollo.type';

const endpoint = '/apollo/configfiles/json/dc-brain-web/default/application';

const defaultConfigs: Switches = {
  'global.rsa-encryption-public-key':
    '-----BEGIN PUBLIC KEY-----\nMIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDJ5ZZ+1XSRCV8hy5Vo4Ze8Y3WR\nR3A4+5ejaWQDQEbAsjTnAaQJ/Lk5o9V9sAHwIgY+dT0QEfL/U6wYZdC3f8NkwmcG\nDxFNjM8G8PpaizM/24213kePrEkQX4ybav8BAB7Ie9unpq/HgB6nGGrvI6cDgw8o\nYrTA+mYjZy/yEq7sjQIDAQAB-----END PUBLIC KEY-----',
  'global.page-authorization-check': 'disabled',
  'global.resource-authorization-check': 'disabled',
  'monitoring.alarms-tts-service': 'built-in',
  'monitoring.ffs-cameras-service': 'enabled',
};

/**
 * @see [Doc](https://www.apolloconfig.com/#/zh/usage/other-language-client-user-guide?id=_12-%e9%80%9a%e8%bf%87%e5%b8%a6%e7%bc%93%e5%ad%98%e7%9a%84http%e6%8e%a5%e5%8f%a3%e4%bb%8eapollo%e8%af%bb%e5%8f%96%e9%85%8d%e7%bd%ae)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: defaultConfigs,
      };
    }

    return {
      error,
      data: {
        ...defaultConfigs,
        ...data,
      },
      ...rest,
    };
  };
}
