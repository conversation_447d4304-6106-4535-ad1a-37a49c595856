/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateNorth as webService } from './update-north.browser';
import { updateNorth as nodeService } from './update-north.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ id: 1, name: '' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({ id: -1 });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ id: 1, name: '' });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({ id: -1 });

  expect(data).toBe(null);
});
