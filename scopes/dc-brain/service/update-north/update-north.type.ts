/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type RequestRespData = SvcRespData;

export type ApiQ = {
  id?: number;
  name?: string;
  path?: string;
  protocol?: string;
  username?: string;
  password?: string;
  ips?: string;
  port?: number;
  aggPush?: number;
  httpUrl?: string;
  available?: boolean;
  description?: string;
};

export type SvcRespData = boolean | null;

export type ApiR = WriteResponse;
