/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-north';
import type { SvcQuery, SvcRespData } from './update-north.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updateNorth(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
