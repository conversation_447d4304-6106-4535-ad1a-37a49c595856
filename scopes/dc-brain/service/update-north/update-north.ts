/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-1
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './update-north.type';

const endpoint = '/dccm/north/user/update';

/**
 * @see [更新北向连接](http://172.16.0.17:13000/project/170/interface/api/19185)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );
    return { error, data, ...rest };
  };
}
