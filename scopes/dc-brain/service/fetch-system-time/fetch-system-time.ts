/**
 * <AUTHOR>
 * @since 2022-7-14
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-system-time.type';

const endpoint = '/pm/tool/currentTime';

/**
 * 查询系统服务器时间
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/18600)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, undefined>(endpoint);

    return { error, data, ...rest };
  };
}
