---
description: 'A fetchSystemTime HTTP API service.'
labels: ['service', 'http', fetch-system-time]
---

## 概述

获取系统时间

## 使用

### Browser

```ts
import { fetchSystemTime } from '@manyun/dc-brain.service.fetch-system-time';

const { error, data } = await fetchSystemTime();
```

### Node

```ts
import { fetchSystemTime } from '@manyun/dc-brain.service.fetch-system-time/dist/index.node';

const { data } = await fetchSystemTime('success');

try {
  const { data } = await fetchSystemTime('error');
} catch (error) {
  // ...
}
```
