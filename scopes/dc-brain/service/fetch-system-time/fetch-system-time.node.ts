/**
 * <AUTHOR>
 * @since 2022-7-14
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-system-time';
import type { SvcRespData } from './fetch-system-time.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchSystemTime(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
