/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-1-3
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-task-search-agg.js';
import type { ApiArgs, ApiResponse } from './fetch-task-search-agg.type.js';

/**
 * @param args
* @returns
 */
export function fetchTaskSearchAgg(args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
