---
description: 'A fetchTaskSearchAgg HTTP API service.'
labels: ['service', 'http']
---

检索聚合接口

## Usage

### Browser

```ts
import { fetchTaskSearchAgg } from '@manyun/dc-brain.service.fetch-task-search-agg';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchTaskSearchAggService } from '@manyun/dc-brain.service.fetch-task-search-agg/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchTaskSearchAggService.from(nodeRequest);
```
