/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-1-3
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * query词
   */
  query: string;
  /**
   * 开始时间（时间戳）
   */
  startTime: string;
  /**
   * 结束时间（时间戳）
   */
  endTime: string;
};

export type AggInfoList = {
  /**
   * 聚合类型名称
   */
  name: string;
  /**
   * 聚合数量
   */
  count: any;
};

export type ApiResponseData = {
  /**
   * 聚合字段，默认 bizType
   */
  aggField: string;
  aggInfoList: AggInfoList[];
};

export type ApiResponse = BackendResponse<ApiResponseData>;
