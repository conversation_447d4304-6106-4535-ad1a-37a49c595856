/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-1-3
 *
 * @packageDocumentation
 */

import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-task-search-agg.type.js';

const endpoint = '/dcom/biz/data/search/agg';

/**
* @see [Doc](http://yapi.manyun-local.com/project/457/interface/api/29781)
*
* @param request
* @returns
*/
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> => {
    return await request.tryPost< ApiResponse['data'] | null, ApiArgs>(endpoint, args);

  };
}             
