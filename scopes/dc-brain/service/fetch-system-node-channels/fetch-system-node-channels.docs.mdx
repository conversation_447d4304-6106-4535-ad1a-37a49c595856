---
description: 'A fetchSystemNodeChannels HTTP API service.'
labels: ['service', 'http', fetch-system-node-channels]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchSystemNodeChannels } from '@manyun/dc-brain.service.fetch-system-node-channels';

const { error, data } = await fetchSystemNodeChannels('success');
const { error, data } = await fetchSystemNodeChannels('error');
```

### Node

```ts
import { fetchSystemNodeChannels } from '@manyun/dc-brain.service.fetch-system-node-channels/dist/index.node';

const { data } = await fetchSystemNodeChannels('success');

try {
  const { data } = await fetchSystemNodeChannels('error');
} catch(error) {
  // ...
}
```
