/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-18
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Channel = {
  id: number;
  name: string;
  ip: string;
  port: string;
  protocol: string;
  deviceNum: number;
  channelStatus: string;
};

export type SvcRespData = {
  data: Channel[];
  total: number;
};

export type RequestRespData = {
  data: Channel[] | null;
  total: number;
} | null;

// SvcQuery 和 apiQ相同
export type ApiQ = {
  nodeGuid: string;
  pageSize?: number;
  pageNum?: number;
};

export type ApiR = ListResponse<Channel[] | null>;
