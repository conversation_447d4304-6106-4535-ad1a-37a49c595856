/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-4
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = ApiQ;

export type SvcRespData = {
  data: boolean;
};

export type RequestRespData = {
  data: boolean | null;
} | null;

export type ApiQ = {
  userId: number | null;
  menus: string[];
};

export type ApiR = ListResponse<boolean | null>;
