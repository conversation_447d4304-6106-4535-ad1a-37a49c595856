---
description: 'A fetchSaveCollectionMenus HTTP API service.'
labels: ['service', 'http', fetch-save-collection-menus]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchSaveCollectionMenus } from '@manyun/dc-brain.service.fetch-save-collection-menus';

const { error, data } = await fetchSaveCollectionMenus({
  userId: 1,
  menus: ['alarm_template_configuration'],
});
```

### Node

```ts
import { fetchSaveCollectionMenus } from '@manyun/dc-brain.service.fetch-save-collection-menus/dist/index.node';

const { error, data } = await fetchSaveCollectionMenus({
  userId: 1,
  menus: ['alarm_template_configuration'],
});
```
