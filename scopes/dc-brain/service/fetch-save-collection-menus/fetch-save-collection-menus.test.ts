/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-4
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchSaveCollectionMenus as webService } from './fetch-save-collection-menus.browser';
import { fetchSaveCollectionMenus as nodeService } from './fetch-save-collection-menus.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { data } = await webService({ userId: 1, menus: ['alarm_template_configuration'] });
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ userId: 2, menus: ['alarm_template_configuration'] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { data } = await nodeService({ userId: 1, menus: ['alarm_template_configuration'] });
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ userId: 2, menus: ['alarm_template_configuration'] });

  expect(typeof error!.code).toBe(undefined);
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
