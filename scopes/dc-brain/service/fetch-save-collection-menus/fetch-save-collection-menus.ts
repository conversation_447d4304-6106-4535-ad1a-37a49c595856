/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-4
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-save-collection-menus.type';

const endpoint = '/pm/store/menu/save';

/**
 * @see [收藏菜单](http://172.16.0.17:13000/project/78/interface/api/18016)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: false,
        },
      };
    }

    return { error, data: { data: data.data }, ...rest };
  };
}
