/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-columns';
import type { SvcQuery, SvcRespData } from './fetch-columns.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchColumns(svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
