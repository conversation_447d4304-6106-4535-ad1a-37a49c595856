/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-columns';
import type { SvcQuery, SvcRespData } from './fetch-columns.type';

const executor = getExecutor(webRequest);

/**
 * @param scvQ
 * @returns
 */
export function fetchColumns(scvQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(scvQ);
}
