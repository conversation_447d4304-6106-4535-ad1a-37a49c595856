/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  userId: number;
  type: string;
};

export type Column = {
  fieldName: string;
  seqId: number;
  fixed: 'left' | 'right' | null;
  show: boolean;
  disable: boolean;
};

export type SvcRespData = {
  data: Column[];
  total: number;
};

export type RequestRespData = {
  data: Column[] | null;
  total: number;
} | null;

export type ApiQ = {
  userId: number;
  type: string;
};

export type ApiR = ListResponse<Column[] | null>;
