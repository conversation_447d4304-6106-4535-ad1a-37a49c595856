/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-27
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-columns.type';

const endpoint = '/pm/store/columns/list';

/**
 * 查询用户定制列列表
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/19938)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      ...svcQ,
    };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
