/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-28
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './upload-full-file.js';
import type { SvcQuery, SvcRespData } from './upload-full-file.type.js';

const executor = getExecutor();

/**
 *
 * @param file
 * @returns
 */
export function uploadFullFile(file: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(file);
}
