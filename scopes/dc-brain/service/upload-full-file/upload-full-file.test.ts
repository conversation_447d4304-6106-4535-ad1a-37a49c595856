/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { uploadFullFile } from './upload-full-file.browser.js';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve error response', async () => {
  const { error } = await uploadFullFile(new File([], 'test.txt'));

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
