/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-28
 *
 * @packageDocumentation
 */
import sha256 from 'crypto-js/sha256';

import { checkUploadFile } from '@manyun/dc-brain.service.check-upload-file';
import { uploadFile } from '@manyun/dc-brain.service.upload-file';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import type { ServiceR, SvcQuery } from './upload-full-file.type.js';

/**
 *
 * @returns
 */
export function getExecutor() {
  return async (file: SvcQuery): Promise<EnhancedAxiosResponse<ServiceR>> => {
    const fileMd5 = sha256(`${file.name}-${new Date()}`).toString();
    const { error: checkError, ...rest } = await checkUploadFile(fileMd5);
    if (checkError) {
      return { ...rest, error: checkError, data: null };
    }

    const fd = new FormData();
    fd.append('file', file.slice());
    fd.append('fileMd5', fileMd5);
    fd.append('fileName', file.name);
    fd.append('fileSize', `${file.size}`);
    fd.append('totalChunks', '1');
    fd.append('currChunk', '0');
    fd.append('chunkSize', `${file.size}`);

    return uploadFile(fd);
  };
}
