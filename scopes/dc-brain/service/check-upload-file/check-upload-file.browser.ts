/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './check-upload-file.js';
import type { SvcQuery, SvcRespData } from './check-upload-file.type.js';

const executor = getExecutor(webRequest);

/**
 * @param fileMd5
 * @returns
 */
export function checkUploadFile(fileMd5: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(fileMd5);
}
