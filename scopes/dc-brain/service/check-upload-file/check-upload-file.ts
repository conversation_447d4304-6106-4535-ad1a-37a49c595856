/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './check-upload-file.type.js';

const endpoint = '/dcom/file/check';

/**
 * @see [Doc](http://172.16.0.17:13000/project/26/interface/api/19377)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (fileMd5: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { fileMd5 };

    return request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
