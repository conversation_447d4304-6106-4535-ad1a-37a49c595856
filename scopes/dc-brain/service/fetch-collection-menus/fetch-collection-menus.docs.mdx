---
description: '收藏菜单列表'
labels: ['service', 'http', fetch-collection-menus]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchCollectionMenus } from '@manyun/dc-brain.service.fetch-collection-menus';

const { error, data } = await fetchCollectionMenus({ userId: 1 });
```

### Node

```ts
import { fetchCollectionMenus } from '@manyun/dc-brain.service.fetch-collection-menus/dist/index.node';

const { data } = await fetchCollectionMenus({ userId: 1 });
```
