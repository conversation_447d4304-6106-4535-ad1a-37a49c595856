/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-4
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCollectionMenus as webService } from './fetch-collection-menus.browser';
import { fetchCollectionMenus as nodeService } from './fetch-collection-menus.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ userId: 1 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ userId: 2 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ userId: 1 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ userId: 2 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
