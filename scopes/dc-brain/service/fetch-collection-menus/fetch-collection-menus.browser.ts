/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-4
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-collection-menus';
import type { SvcQuery, SvcRespData } from './fetch-collection-menus.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchCollectionMenus(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
