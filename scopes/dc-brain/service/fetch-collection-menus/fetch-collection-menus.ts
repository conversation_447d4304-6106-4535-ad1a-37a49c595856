/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-4
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-collection-menus.type';

const endpoint = '/pm/store/menu/list';

/**
 * @see [查询收藏菜单](http://172.16.0.17:13000/project/78/interface/api/18024)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = variant;

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
        },
      };
    }

    return { error, data: { data }, ...rest };
  };
}
