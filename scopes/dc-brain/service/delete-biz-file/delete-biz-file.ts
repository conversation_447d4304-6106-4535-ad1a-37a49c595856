/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './delete-biz-file.type.js';

const endpoint = '/dcom/file/delete';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/26/interface/api/459)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    return { error, data, ...rest };
  };
}
