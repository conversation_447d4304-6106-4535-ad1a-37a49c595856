/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteBizFile as webService } from './delete-biz-file.browser.js';
import { deleteBizFile as nodeService } from './delete-biz-file.node.js';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    targetId: 'REMOTE-MOCK-SUCCESS',
    targetType: 'SOME-TARGET-TYPE',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { data } = await webService({
    targetId: 'REMOTE-MOCK-FAILURE',
    targetType: 'SOME-TARGET-TYPE',
  });

  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    targetId: 'REMOTE-MOCK-SUCCESS',
    targetType: 'SOME-TARGET-TYPE',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { data } = await nodeService({
    targetId: 'REMOTE-MOCK-FAILURE',
    targetType: 'SOME-TARGET-TYPE',
  });

  expect(data).toBe(null);
});
