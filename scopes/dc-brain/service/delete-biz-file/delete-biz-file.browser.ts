/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-biz-file.js';
import type { ApiQ, SvcRespData } from './delete-biz-file.type.js';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function deleteBizFile(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
