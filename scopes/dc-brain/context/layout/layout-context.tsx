import { createContext } from 'react';

export type LayoutContextType = {
  selectedMenuCodes: string[];
  setSelectedMenuCodes: React.Dispatch<React.SetStateAction<string[]>>;
  menuVisible: boolean;
  setMenuVisible: React.Dispatch<React.SetStateAction<boolean>>;
  sideBarVisible: boolean;
  setSideBarVisible: React.Dispatch<React.SetStateAction<boolean>>;
  sideBarTab: string;
  setSideBarTab: React.Dispatch<React.SetStateAction<string>>;
  /** 当前页面的 spaceGuid */
  currentSpaceGuid: string;
  setCurrentSpaceGuid: React.Dispatch<React.SetStateAction<string>>;
};

const noop = () => {};

export const LayoutContext = createContext<LayoutContextType>({
  selectedMenuCodes: [],
  setSelectedMenuCodes: noop,
  menuVisible: false,
  setMenuVisible: noop,
  sideBarVisible: false,
  setSideBarVisible: noop,
  sideBarTab: '',
  setSideBarTab: noop,
  currentSpaceGuid: '',
  setCurrentSpaceGuid: noop,
});
