import React, { ReactNode } from 'react';

import { LayoutContext } from './layout-context';

export type LayoutProviderProps = {
  children: ReactNode;
};

export function LayoutProvider({ children }: LayoutProviderProps) {
  const [selectedMenuCodes, setSelectedMenuCodes] = React.useState<string[]>([]);

  const [menuVisible, setMenuVisible] = React.useState<boolean>(false);

  const [sideBarVisible, setSideBarVisible] = React.useState<boolean>(false);

  const [sideBarTab, setSideBarTab] = React.useState<string>('roomview'); // 默认展示监控视图

  const [currentSpaceGuid, setCurrentSpaceGuid] = React.useState<string>('');

  return (
    <LayoutContext.Provider
      value={{
        selectedMenuCodes,
        setSelectedMenuCodes,
        menuVisible,
        setMenuVisible,
        sideBarVisible,
        setSideBarVisible,
        sideBarTab,
        setSideBarTab,
        currentSpaceGuid,
        setCurrentSpaceGuid,
      }}
    >
      {children}
    </LayoutContext.Provider>
  );
}
