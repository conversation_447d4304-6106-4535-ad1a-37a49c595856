import React from 'react';
import { Provider } from 'react-redux';

import { configureStore, createSlice } from '@reduxjs/toolkit';
import createSagaMiddleware from 'redux-saga';
import { all } from 'redux-saga/effects';

import rolesReducer, { rolesWatchers } from '@manyun/auth-hub.state.roles';
import userReducer, { userWatchers } from '@manyun/auth-hub.state.user';
import userGroupsReducer, { userGroupsWatchers } from '@manyun/auth-hub.state.user-groups';
import usersReducer, { usersWatchers } from '@manyun/auth-hub.state.users';
import authRequestsReducer, { authRequestsWatchers } from '@manyun/bpm.state.auth-request';
import customerReducer, { customersWatchers } from '@manyun/crm.state.customers';
import coursesReducer, { coursesWatchers } from '@manyun/knowledge-hub.state.courses';
import coursewaresReducer, { coursewaresWatchers } from '@manyun/knowledge-hub.state.coursewares';
import examsReducer, { examsWatchers } from '@manyun/knowledge-hub.state.exams';
import papersReduceer, { papersWatchers } from '@manyun/knowledge-hub.state.papers';
import questionsReducer, { questionsWatchers } from '@manyun/knowledge-hub.state.questions';
import skillsReducer, { skillsWatchers } from '@manyun/knowledge-hub.state.skills';
import ticketReducer, { ticketWatchers } from '@manyun/monitoring.state.dashboard-idc';
import onSiteMessagesReducer, {
  onSiteMessagesWatchers,
} from '@manyun/notification-hub.state.on-site-messages';
import metaDataReducer, { metaDataWatchers } from '@manyun/resource-hub.state.meta-data';
import spacesReducer from '@manyun/resource-hub.state.space';

import citiesJSON from './data/cities';

// const mockUserSlice = createSlice({
//   name: 'user',
//   initialState: {
//     userId: 1, // Jerry
//     username: 'Jerry',
//     name: '王彦苏',
//     company: '',
//     permissions: [
//       'element_change-user-shift-schedule-request',
//       'element_fix-user-missed-punches-request',
//       'element_user-profile-allow-modify-props--admin',
//       'element_construction-request--card',
//     ],
//   },
//   reducers: {},
// });

const commonSlice = createSlice({
  name: 'common',
  initialState: {
    space: {
      treeList: [
        {
          metaCode: 'NC',
          metaName: '华北',
          metaType: 'REGION',
          parentCode: '01',
          children: [
            {
              metaCode: 'sxdtyg',
              metaName: 'sxdtyg',
              metaType: 'IDC',
              parentCode: 'NC',
              children: [
                {
                  metaCode: 'sxdtyg.1',
                  metaName: '1',
                  metaType: 'BLOCK',
                  parentCode: 'sxdtyg',
                },
                {
                  metaCode: 'sxdtyg.2',
                  metaName: '2',
                  metaType: 'BLOCK',
                  parentCode: 'sxdtyg',
                },
              ],
            },
          ],
        },
      ],
      normalizedList: null,
      parallelList: [
        {
          metaCode: 'sxdtyg',
          metaName: 'sxdtyg',
          metaType: 'IDC',
          parentCode: 'NC',
        },
        {
          metaCode: 'sxdtyg.1',
          metaName: '1',
          metaType: 'BLOCK',
          parentCode: 'sxdtyg',
        },
        {
          metaCode: 'sxdtyg.2',
          metaName: '2',
          metaType: 'BLOCK',
          parentCode: 'sxdtyg',
        },
      ],
    },
    deviceCategory: {
      normalizedList: {
        '1': {
          id: null,
          metaType: 'C0',
          metaCode: '1',
          metaName: '强电',
          parentCode: '0',
          numbered: true,
          metaStyle: 'DEVICE',
          createTime: null,
          updateTime: null,
          lastOperatorName: null,
          lastOperator: null,
          isDeleted: null,
          description: null,
        },
        '2': {
          id: null,
          metaType: 'C0',
          metaCode: '2',
          metaName: '暖通',
          parentCode: '0',
          numbered: true,
          metaStyle: 'DEVICE',
          createTime: null,
          updateTime: null,
          lastOperatorName: null,
          lastOperator: null,
          isDeleted: null,
          description: null,
        },
        '30119': {
          id: null,
          metaType: 'C2',
          metaCode: '30119',
          metaName: '热通道温湿度传感器',
          parentCode: 'C1301',
          numbered: true,
          metaStyle: 'DEVICE',
          createTime: null,
          updateTime: null,
          lastOperatorName: null,
          lastOperator: null,
          isDeleted: null,
          description: null,
        },
      },
    },
    citiesTree: citiesJSON,
  },
  reducers: {},
});

const configSlice = createSlice({
  name: 'config',
  initialState: {
    configMap: {
      current: {
        common: {
          homeUrl: '/',
        },
      },
    },
  },
  reducers: {},
});

const sagaMiddleware = createSagaMiddleware();

export const store = configureStore({
  reducer: {
    ...customerReducer,
    ...questionsReducer,
    ...rolesReducer,
    ...userGroupsReducer,
    ...authRequestsReducer,
    ...papersReduceer,
    ...examsReducer,
    ...skillsReducer,
    ...coursesReducer,
    ...coursewaresReducer,
    [commonSlice.name]: commonSlice.reducer,
    [configSlice.name]: configSlice.reducer,
    // [mockUserSlice.name]: mockUserSlice.reducer,
    ...onSiteMessagesReducer,
    ...usersReducer,
    ...ticketReducer,
    ...userReducer,
    ...spacesReducer,
    ...metaDataReducer,
  },
  middleware: getDefaultMiddlewares => [
    ...getDefaultMiddlewares({ thunk: false, immutableCheck: false, serializableCheck: false }),
    sagaMiddleware,
  ],
});

function* rootSaga() {
  yield all([
    ...customersWatchers,
    ...questionsWatchers,
    ...papersWatchers,
    ...examsWatchers,
    ...skillsWatchers,
    ...coursesWatchers,
    ...coursewaresWatchers,
    ...rolesWatchers,
    ...userGroupsWatchers,
    ...authRequestsWatchers,
    ...onSiteMessagesWatchers,
    ...usersWatchers,
    ...ticketWatchers,
    ...userWatchers,
    ...metaDataWatchers,
  ]);
}

sagaMiddleware.run(rootSaga);

/**
 * It's an [EXPERIMENTAL] feature, do not use it now.
 * See https://github.com/redux-saga/redux-saga/issues/1312#issuecomment-359683995
 *
 * @experimental
 * @returns
 */
export function runRootSaga() {
  console.error("It's an [EXPERIMENTAL] feature, do not use it now.");

  return sagaMiddleware.run(rootSaga);
}

export const FakeStore: React.FC = ({ children }) => <Provider store={store}>{children}</Provider>;
