/* eslint-disable @typescript-eslint/no-explicit-any */
import { configureStore } from '@reduxjs/toolkit';
import type { EnhancedStore, Middleware } from '@reduxjs/toolkit';
import { routerMiddleware } from 'connected-react-router';
import type { History } from 'history';
import throttle from 'lodash.throttle';
import type { AnyAction, CombinedState, Dispatch, Reducer } from 'redux';
import logger from 'redux-logger';
import createSagaMiddleware from 'redux-saga';
import type { SagaMiddleware } from 'redux-saga';

import { Modal } from '@manyun/base-ui.ui.modal';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';

import { loadState, saveState } from './cache';

let forceRefreshModalInst: ReturnType<typeof Modal.warning> | null = null;

export type Configs = {
  history: History;
  reducer: Reducer<CombinedState<any>, AnyAction>;
};

export type Store = EnhancedStore<any, AnyAction, Middleware<{}, any, Dispatch<AnyAction>>[]> & {
  runSaga: SagaMiddleware['run'];
};

export function createStore({ history, reducer }: Configs): Store {
  const persistedState = loadState();
  const preloadedState =
    ['production', 'development'].includes(env.NODE_ENV) && persistedState?.user?.userId !== null
      ? persistedState
      : undefined;

  const sagaMiddleware = createSagaMiddleware();
  const store = configureStore({
    preloadedState,
    reducer,
    middleware: getDefaultMiddleware => {
      const middlewares = [
        ...getDefaultMiddleware({ thunk: false, immutableCheck: false, serializableCheck: false }),
        routerMiddleware(history),
        env.__DEV_MODE__ && logger,
        sagaMiddleware,
      ].filter(Boolean) as Middleware<{}, any, Dispatch<AnyAction>>[];

      return middlewares;
    },
  });

  store.subscribe(
    throttle(() => {
      const sessionId = window.localStorage.getItem('sessionId');
      if (!sessionId) {
        return;
      }

      const { username } = getUserInfo();
      const { user, common, ...restState } = store.getState();
      if (!user.userId) {
        return;
      }

      /**
       * User may open another Tab to log out & log in(another account),
       * so we should compare the 2 `loginName`s in the localStorage & Redux Store are the same or not,
       * if not, we skip this cache update action and replace current user state from localStorage.
       */
      if (username !== null && username !== user.username) {
        if (forceRefreshModalInst) {
          return;
        }

        forceRefreshModalInst = Modal.warning({
          maskClosable: false,
          zIndex: Number.MAX_SAFE_INTEGER,
          title: '页面需要刷新',
          content: '检测到你可能在其他页面切换登录了其他帐号，因此需要刷新当前页面才可以继续操作！',
          okText: '刷新',
          onOk: () => {
            forceRefreshModalInst = null;
            window.location.reload();
          },
        });

        return;
      }

      if (env.NODE_ENV === 'production') {
        if (!user.userId) {
          console.error('Missing user ID while caching user ', user);
          saveState({ common });
          return;
        }
        saveState({
          user,
          common,
          ...storeKnowledhubPapersForCreatePrepare(restState),
        });
      }

      if (env.NODE_ENV === 'development') {
        if (!user.userId) {
          throw new Error(`Missing user ID while caching user: ${JSON.stringify(user)}`);
        }
        saveState({
          user,
          ...storeKnowledhubPapersForCreatePrepare(restState),
        });
      }
    }, 1000)
  );

  (store as Store).runSaga = sagaMiddleware.run;

  return store as Store;
}

const storeKnowledhubPapersForCreatePrepare = (restState: any) => {
  const papersState = restState['knowledge-hub.papers'];
  if (!papersState) {
    return;
  }
  const { create } = papersState;
  const mutatePaper = {
    name: '',
    markingWay: 0,
    genFun: 'SELECT',
    categoryCode: '',
    allowArbitraryOptionsOrder: false,
    allowArbitraryQuestionsOrder: false,
    orderByQuestionTypeInSections: false,
    sectionEntities: {},
    sectionIds: [],
    totalScore: 0,
    totalQuestionSize: 0,
  };
  return {
    'knowledge-hub.papers': {
      category: {
        entities: {},
        ids: [],
        loading: false,
      },
      entities: {},
      all: {
        loading: false,
        error: null,
        fields: {
          page: 1,
          pageSize: 10,
          categoryCodes: undefined,
          genFun: undefined,
          name: undefined,
          timeRange: undefined,
        },
        ids: [],
        selectedIds: [],
        total: 0,
      },
      create: {
        ...mutatePaper,
        name: create.name || '',
        categoryCode: create.categoryCode || '',
        markingWay: create.markingWay || '0',
        genFun: create.genFun || 'SELECT',
      },
      update: { ...mutatePaper, id: -1 },
    },
  };
};
