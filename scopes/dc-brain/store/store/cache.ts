const REDUX_STORE_STATE_KEY = 'CLOUD_BASE_UI_STATE';

export function saveState(state: any) {
  try {
    const serializedState = JSON.stringify(state);
    localStorage.setItem(REDUX_STORE_STATE_KEY, serializedState);
  } catch (error) {
    // ignore...
  }
}

export function loadState() {
  try {
    const persistedState = localStorage.getItem(REDUX_STORE_STATE_KEY);
    if (persistedState === null) {
      return;
    }
    const stateCache = JSON.parse(persistedState);

    // `v20220131` 增加了 `user.menus` 字段
    // 如果缓存中没有，则会导致 `Layout` 组件报错
    if (stateCache.user.menus === undefined || Array.isArray(stateCache.user.menus)) {
      stateCache.user.menus = {
        entities: {},
        codes: [],
        idsMapper: {},
      };
    }

    return stateCache;
  } catch (error) {
    // ignore...
  }
}

export function clearUiState() {
  window.localStorage.removeItem(REDUX_STORE_STATE_KEY);
}
