import { connectRouter } from 'connected-react-router';
import type { History } from 'history';
import { combineReducers } from 'redux';
import type { AnyAction, CombinedState, Reducer, ReducersMapObject } from 'redux';

import commonSliceReducer from '@manyun/dc-brain.state.common';
import configSliceReducer from '@manyun/dc-brain.state.config';

export type Configs = {
  history: History;
  extraReducer?: ReducersMapObject;
};

export function createRootReducer({
  history,
  extraReducer,
}: // eslint-disable-next-line @typescript-eslint/no-explicit-any
Configs): Reducer<CombinedState<any>, AnyAction> {
  return combineReducers({
    // https://github.com/supasate/connected-react-router#step-1
    router: connectRouter(history),

    ...configSliceReducer,
    ...commonSliceReducer,

    // Custom extra reducers
    ...extraReducer,
  });
}
