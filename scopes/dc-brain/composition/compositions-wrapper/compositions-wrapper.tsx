import React from 'react';

import { useRemoteMock } from '@manyun/service.request';

/**
 * Takes a list of composition components,
 * returns a combined composition component.
 *
 * @param compositions Composition components(only accept one for now)
 * @returns
 */
export function wrapCompositions<P = any>([Composition]: React.ComponentType<any>[]): React.FC<P> {
  return props => {
    const [ready, setReady] = React.useState(false);
    React.useEffect(() => {
      // eslint-disable-next-line react-hooks/rules-of-hooks
      const mockOff = useRemoteMock('web');
      setReady(true);

      return () => {
        mockOff();
      };
    }, []);

    if (ready === false) {
      return <>Loading...</>;
    }

    return (
      // @ts-ignore
      <Composition {...props} />
    );
  };
}
