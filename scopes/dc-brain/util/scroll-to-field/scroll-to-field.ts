export type CustomScrollBehaviorCallback = (
  actions: {
    el: Element;
    top: number;
    left: number;
  }[]
) => void;

export function scrollToField(
  scrollToField: (
    name: string | number | (string | number)[],
    options?: {
      behavior: 'auto' | 'smooth' | CustomScrollBehaviorCallback;
    }
  ) => void,
  errorFields: { name: string | number }[]
) {
  scrollToField(errorFields[0].name, {
    behavior: actions => {
      actions.forEach(action => {
        action.el.scroll({
          top:
            action.top +
            (document
              .getElementById(
                Array.isArray(errorFields[0].name)
                  ? errorFields[0].name.join('_')
                  : errorFields[0].name.toString()
              )!
              .getBoundingClientRect().y < action.top
              ? -55
              : 75),
          left: action.left,
          behavior: 'smooth',
        });
      });
    },
  });
}
