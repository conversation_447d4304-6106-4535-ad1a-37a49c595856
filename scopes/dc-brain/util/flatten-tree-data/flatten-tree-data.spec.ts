import { flattenTreeData } from './flatten-tree-data';

test('should return the correct value', () => {
  expect(
    flattenTreeData([
      {
        title: '1',
        children: [
          {
            title: '1-1',
          },
        ],
      },
      {
        title: '2',
        children: [
          {
            title: '2-1',
            children: [
              {
                title: '2-1-1',
              },
            ],
          },
        ],
      },
    ])
  ).toStrictEqual([
    { title: '1-1' },
    { title: '1' },
    { title: '2-1-1' },
    { title: '2-1' },
    { title: '2' },
  ]);
});
