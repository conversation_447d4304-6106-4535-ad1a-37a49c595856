export type DummyTreeNode = {
  [x: string]: any;
  children?: any[];
};

export function flattenTreeData<T extends DummyTreeNode = DummyTreeNode>(
  treeData: T[]
): Omit<T, 'children'>[] {
  const dataList: Omit<T, 'children'>[] = [];
  loop(treeData);
  function loop(data: T[]) {
    for (let i = 0; i < data.length; i++) {
      const { children, ...node } = data[i];
      if (children) {
        loop(children);
      }
      dataList.push(node);
    }
  }

  return dataList;
}
