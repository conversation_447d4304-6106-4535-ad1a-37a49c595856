/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-5
 *
 * @packageDocumentation
 */
import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';
import { PageHeader } from '@manyun/base-ui.ui.page-header';

import { generateExcelStatementListLocation } from '@manyun/dc-brain.route.admin-routes';
import type { ExcelStatementEditParams } from '@manyun/dc-brain.route.admin-routes';
import { EXCEL_STATEMENT_SUBJOB_TYPE_TEXT, PlanType } from '@manyun/ticket.model.task';
import { TicketTaskMutator } from '@manyun/ticket.ui.ticket-task-mutator';

export function ExcelStatementEdit() {
  const { id, type } = useParams<ExcelStatementEditParams>();

  const history = useHistory();

  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }}>
      <PageHeader
        title={`编辑${EXCEL_STATEMENT_SUBJOB_TYPE_TEXT[type]}报表`}
        onBack={() => history.push(generateExcelStatementListLocation({ type }))}
      />
      <TicketTaskMutator
        id={id}
        planType={PlanType.ExcelReport}
        showBasicInfoTitle={false}
        unusedFormItems={[
          'drillLevel',
          'drillMajorType',
          'manageType',
          'distribute',
          'splitors',
          'jobSla',
        ]}
        mode="edit"
        excelSubJobType={type}
        onCancel={() => {
          history.push(generateExcelStatementListLocation({ type }));
        }}
        onSuccess={() => {
          history.push(generateExcelStatementListLocation({ type }));
        }}
      />
    </Container>
  );
}
