/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-4
 *
 * @packageDocumentation
 */
import React from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';
import { PageHeader } from '@manyun/base-ui.ui.page-header';

import { generateExcelStatementListLocation } from '@manyun/dc-brain.route.admin-routes';
import type { ExcelStatementSubjobType } from '@manyun/ticket.model.task';
import { EXCEL_STATEMENT_SUBJOB_TYPE_TEXT, PlanType } from '@manyun/ticket.model.task';
import { TicketTaskMutator } from '@manyun/ticket.ui.ticket-task-mutator';

export type ExcelStatementCreateProps = {};

export function ExcelStatementCreate() {
  const { type } = useParams<{ type: ExcelStatementSubjobType }>();

  const history = useHistory();

  return (
    <Container style={{ width: '100%', height: '100%', padding: 0 }}>
      <PageHeader
        title={`新建${EXCEL_STATEMENT_SUBJOB_TYPE_TEXT[type]}报表`}
        onBack={() => history.push(generateExcelStatementListLocation({ type }))}
      />
      <TicketTaskMutator
        showBasicInfoTitle={false}
        planType={PlanType.ExcelReport}
        unusedFormItems={[
          'drillLevel',
          'drillMajorType',
          'manageType',
          'distribute',
          'splitors',
          'jobSla',
        ]}
        mode="create"
        excelSubJobType={type}
        onCancel={() => {
          history.push(generateExcelStatementListLocation({ type }));
        }}
        onSuccess={() => {
          history.push(generateExcelStatementListLocation({ type }));
        }}
      />
    </Container>
  );
}
