@import (reference) '@manyun/base-ui.theme.datav-theme/dist/datav-theme.less';

.blockCover {
  height: 15vh;
  @media (max-height: 800px) {
    height: 130px;
  }
  @media screen and (min-height: 800px) and (max-height: 1000px) {
    height: 160px;
  }
  @media screen and (min-height: 1000px) and (max-height: 1200px) {
    height: 190px;
  }
  @media (min-height: 1200px) {
    height: 20vh;
  }
  width: 100%;
}
.blockDesc {
  color: @text-color-secondary;
}

.blockStatic {
  color: @heading-color;
  font-size: 16px;
}

.blockCarousel {
  :global {
    .@{prefixCls}-carousel .slick-dots li {
      text-indent: inherit;
      width: auto;
      padding: 0 0 16px 0;
      height: auto;
      margin: 0;
    }
    .@{prefixCls}-carousel .slick-dots-bottom {
      top: 16vh;
      @media (max-height: 800px) {
        top: 92px;
      }
      @media screen and (min-height: 800px) and (max-height: 1000px) {
        top: 120px;
      }
      @media screen and (min-height: 1000px) and (max-height: 1200px) {
        top: 155px;
      }
      @media (min-height: 1200px) {
        top: 17vh;
      }
      left: 23px;
      overflow-x: scroll;
      overflow-y: hidden;
      height: 38px;
      justify-content: center;
      margin: 0 10px;
    }
  }
}
.blockCarouselFlex {
  :global {
    .@{prefixCls}-carousel .slick-dots-bottom {
      justify-content: start;
    }
  }
}

.blockCarouselPluginExtand {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  :global {
    .@{prefixCls}-timeline {
      display: flex;
    }
    .@{prefixCls}-timeline-item-tail,
    .@{prefixCls}-timeline-item-head {
      display: none;
    }
    .@{prefixCls}-timeline-item-content {
      top: 6px;
      right: 16px;
    }
    .anticon {
      top: 12px;
    }
  }
}

.blockSwitchTimeline {
  background: fade(@background-color-light, 60%);
  border-radius: 4px;
  height: 34px;
  > div {
    margin: 0 2px;
    background-size: 24px 34px;
  }
}
