import React, { useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useDeepCompareEffect } from 'react-use';

import classNames from 'classnames';
import dayjs from 'dayjs';

import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Rate } from '@manyun/base-ui.ui.rate';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';
import { formatInterval } from '@manyun/base-ui.util.date-fns.format-interval';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { BlockSwitchTimeline } from '@manyun/monitoring.datav.block-timeline';
import { fetchSpecs } from '@manyun/resource-hub.service.fetch-specs';
import { BlockCover } from '@manyun/resource-hub.ui.block-cover';

import type { BlockInfo } from '../../home.type';
import styles from './​block-carousel.module.less';

export type BlockCarouselProps = {
  blocks: BlockInfo[];
  carouseBlock: BlockInfo;
  onBlockChange: (blockId: string) => void;
};

/** 轮播楼栋 */
export function BlockCarousel({ blocks, carouseBlock, onBlockChange }: BlockCarouselProps) {
  const [activeBlock, setActiveBlock] = useState<string>(blocks[0].id);
  const [safeLevel, setSafeLevel] = useState<string | null | undefined>();
  const [designNormal, setDesignNormal] = useState<string | null | undefined>();
  const config = useSelector(selectCurrentConfig);
  const configUtil = useMemo(() => {
    return new ConfigUtil(config);
  }, [config]);
  const blockDeviceType = configUtil.getOneDeviceType(ConfigUtil.constants.deviceTypes.SPACE_BLOCK);
  const isMountedRef = useRef(true);

  useDeepCompareEffect(() => {
    if (carouseBlock?.id) {
      setActiveBlock(carouseBlock.id);
    }
    if (blockDeviceType && carouseBlock?.guid) {
      (async () => {
        const { data, error } = await fetchSpecs({
          modelId: carouseBlock.guid,
          deviceType: blockDeviceType,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        setSafeLevel(data.data.find(spec => spec.specCode === 'safeLevel')?.specValue);
        setDesignNormal(data.data.find(spec => spec.specCode === 'designNormal')?.specValue);
      })();
    }
    return () => {
      isMountedRef.current = false;
    };
  }, [carouseBlock, blockDeviceType]);

  return (
    <div
      className={classNames(styles.blockCarousel, blocks.length > 4 && styles.blockCarouselFlex)}
    >
      {blocks.map(block => (
        <div
          key={block.id}
          style={{
            position: 'relative',
            display: block.id === activeBlock ? 'block' : 'none',
          }}
        >
          <BlockCover src={block.src} className={styles.blockCover} />
          <BlockSwitchTimeline
            style={{
              position: 'absolute',
              width: '100%',
              top: 114,
            }}
            className={styles.blockSwitchTimeline}
            blocks={blocks.map(block => ({
              label: block.id,
              value: block.id,
            }))}
            activeBlock={activeBlock}
            onBlockChange={onBlockChange}
          />
          <div style={{ margin: '24px 0' }}>
            <Typography.Text className={styles.blockDesc}>{block.description}</Typography.Text>
          </div>
          <Row gutter={16}>
            <Col span={8}>
              <Statistic
                title={<Typography.Text className={styles.blockDesc}>安全等级</Typography.Text>}
                formatter={() =>
                  safeLevel ? (
                    <Rate
                      style={{ fontSize: 16 }}
                      disabled
                      defaultValue={+safeLevel}
                      count={+safeLevel}
                    />
                  ) : (
                    <Typography.Text className={styles.blockStatic}>--</Typography.Text>
                  )
                }
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={<Typography.Text className={styles.blockDesc}>设计标准</Typography.Text>}
                formatter={() => (
                  <Typography.Text className={styles.blockStatic}>
                    {designNormal || '--'}
                  </Typography.Text>
                )}
              />
            </Col>
            <Col span={8}>
              <Statistic
                title={<Typography.Text className={styles.blockDesc}>安全运行</Typography.Text>}
                formatter={() => (
                  <Typography.Text className={styles.blockStatic}>
                    {getDateDiff(block?.operationTime, new Date())}
                  </Typography.Text>
                )}
              />
            </Col>
          </Row>
        </div>
      ))}
    </div>
  );
}

/**
 * 获取间隔天数/月数/年数
 * 如： 666天 => 1年10个月
 */
function getDateDiff(diffDate: string, now: Date) {
  if (!diffDate) {
    return '--';
  }
  let diffDays = dayjs(dayjs(now).format('YYYY-MM-DD')).diff(diffDate, 'day');
  if (diffDays === 0) {
    diffDays = 1; // 单日间隔默认为1天
  }
  return formatInterval(diffDays, 'days');
}
