import React, { useCallback, useLayoutEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import debounce from 'lodash.debounce';
import get from 'lodash.get';
import uniqBy from 'lodash/uniqBy';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Badge } from '@manyun/base-ui.ui.badge';

import { getCitiesMapper, getCoords } from '@manyun/dc-brain.state.common';
import type { DataCenterList } from '@manyun/resource-hub.service.fetch-data-center-list';

import type { IdcInfo } from '../../home.type';
import { IdcPopover } from '../idc-popover';
import styles from './​region-map.module.less';

type CityPosition = { cityName?: string; x1: number; y1: number };

const latRange = 36; // 纬度跨度
const lntRange = 61; // 经度跨度  实际只到海南三亚(18)
const westLnt = 73.5; // 中国最西经度为73.5
const southLat = 18; // 三亚纬度18

export function RegionMap({
  idcInfo,
  dataCenterList,
}: {
  idcInfo: IdcInfo[];
  dataCenterList: DataCenterList[];
}) {
  const [viewWidth, setViewWidth] = useState(document.documentElement.clientWidth);
  const [viewHeight, setViewHeight] = useState(document.documentElement.clientHeight);

  const coordsMapper = useSelector(getCoords);

  const centerList = uniqBy(dataCenterList, 'cityCode');
  const citiesMapper = useSelector(getCitiesMapper);

  // 坐标
  const positionCoordinate: (CityPosition & DataCenterList)[] = useDeepCompareMemo(
    () =>
      centerList.map(item => {
        const mapWidth = +((viewWidth * 0.43) / lntRange).toFixed(2);
        const mapHeight = +((viewWidth * 0.3) / latRange).toFixed(2);
        const cityLatLnt =
          coordsMapper && get(coordsMapper, item.cityCode) // 城市经纬度
            ? get(coordsMapper, item.cityCode).split(',')
            : [0, 0];
        const left = (+cityLatLnt[0] - westLnt) * mapWidth; // 地图相对位置
        const bottom = (+cityLatLnt[1] - southLat) * mapHeight;

        let cityName = citiesMapper?.[item.cityCode]?.label;
        const city = cityName && cityName.substring(0, cityName?.length - 1);
        return {
          ...item,
          cityName: city,
          x1: +left.toFixed(4),
          y1: +bottom.toFixed(4),
        };
      }),
    [centerList, viewWidth, viewHeight, citiesMapper]
  );

  const updateViewSize = useCallback(() => {
    setViewWidth(document.documentElement.clientWidth);
    setViewHeight(document.documentElement.clientHeight);
  }, []);

  useLayoutEffect(() => {
    window.addEventListener('resize', debounce(updateViewSize, 100));
    return () => window.removeEventListener('resize', updateViewSize);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <div className={styles.regionMapContainer}>
      <div className={styles.regionMapContainerBottom} />
      <div
        style={{
          width: '100%',
          height: '100%',
        }}
        className={styles.regionMap}
      >
        <div
          style={{
            left: 0.29 * viewWidth,
            bottom: (viewHeight - viewWidth * 0.28) / 2, // 高度auto下定位取决于可视宽度
          }}
          className={styles.regionMapCities}
        >
          {positionCoordinate.map(item => {
            return (
              <div
                key={item.cityCode}
                style={{
                  position: 'absolute',
                  left: item.x1 || 'none',
                  bottom: item.y1 || 'none',
                  zIndex: 5,
                }}
              >
                <IdcPopover
                  children={<Badge status="processing" className={styles.positionBadge} />}
                  idcs={idcInfo.filter(idc => idc.cityCode === item.cityCode)}
                />
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
