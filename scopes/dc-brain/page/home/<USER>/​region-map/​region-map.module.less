@import (reference) '@manyun/base-ui.theme.datav-theme/dist/datav-theme.less';

@gradient-container-bg: linear-gradient(90deg, #ecf3fe 32.83%, #eaf1fc 56.07%, #fbfcff 100%);
@gradient-container-bottom-bg: linear-gradient(
  89.97deg,
  #ebf3fc 3.04%,
  #d3e3f8 15.72%,
  #cfe1f8 20.42%,
  #cee1f8 33.38%,
  #cee1f7 40.66%,
  #cde0f7 49.36%,
  #c9def7 55.89%,
  #c2d8f2 65.53%,
  #bfd6f1 70.54%,
  #bfd6f1 81.9%,
  #bfd6f1 99.98%
);

.regionMapContainer {
  position: fixed;
  z-index: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  width: 100%;
  height: 100%;
  background: @gradient-container-bg;
}
.regionMapContainerBottom {
  background: @gradient-container-bottom-bg;
  position: fixed;
  bottom: 0;
  height: 22vh;
  width: 100%;
}
.regionMap {
  background-image: url('../../assets/map.png');
  background-repeat: no-repeat;
  background-size: 100% auto;
  position: relative;
  image-rendering: -webkit-optimize-contrast;
  background-position-x: center;
  background-position-y: center;
}

.regionMapCities {
  position: absolute;
  width: 100%;
  height: 100%;
}

.positionBadge {
  :global {
    .@{prefixCls}-badge-status-dot {
      background: @base-primary;
      transition: all 0.3s ease;
      width: 12px;
      height: 12px;
      &:hover {
        width: 20px;
        height: 20px;
      }
    }
  }
}
