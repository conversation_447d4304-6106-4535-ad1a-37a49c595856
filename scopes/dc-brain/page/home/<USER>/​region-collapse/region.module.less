@import (reference) '@manyun/base-ui.theme.datav-theme/dist/datav-theme.less';

@gradient-card-bg: linear-gradient(
    222.69deg,
    #d5e8ff -0.96%,
    rgba(209, 231, 255, 0.515625) 40.07%,
    #edf5ff 83.76%
  ),
  #ffffff;

.regionTitle {
  font-size: 16px;
  color: @heading-color;
}
.regionContent {
  color: @text-color-secondary;
}
.regionCollapse {
  :global {
    .@{prefixCls}-collapse-content-box {
      padding: 6px;
    }
    .@{prefixCls}-collapse-icon-position-end
      > .@{prefixCls}-collapse-item
      > .@{prefixCls}-collapse-header {
      padding: 6px;
    }
  }
}
.idcTitleAlarm {
  color: var(--manyun-error-color);
}

.collapseCard {
  background: @gradient-card-bg;
}

.collapseCard {
  background: @gradient-card-bg;
  &:hover .extraDetail {
    display: block;
  }
}

.extraDetail {
  display: none;
}

.activeCard {
  background: @base-primary;
  :global {
    span {
      color: @white;
    }
  }
}
.containerContent {
  margin-top: 8px;
}
.containerExtra {
  width: 33%;
}
.containerTitle {
  width: 67%;
}
