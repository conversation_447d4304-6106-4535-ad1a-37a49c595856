export type BlockInfo = {
  idcName: string;
  idcGuid: string;
  permission: boolean;
  // EC06.A
  guid: string;
  // A
  id: string;
  description: string;
  /** 安全等级 */
  safeLevel: number;
  /** 设计标准 */
  designNormal: string;
  /** 投产日期 */
  operationTime: string;
  cityCode?: string;
  src?: string;
};

export type IdcInfo = RegionIdc & {
  blocks: BlockInfo[];
};

export type RegionIdc = {
  name: string;
  guid: string;
  provinceName: string;
  cityName: string;
  cityCode: string;
  permission: boolean;
  blockTags: string[];
  regionCode?: string;
  regionName?: string;
  carouseLocked?: boolean; // 是否轮播锁定
};

export type RegionCenterList = {
  regionName: string;
  regionIdcChilds: (RegionIdc & { alarm?: boolean })[];
};
