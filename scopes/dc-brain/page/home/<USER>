@import (reference) '@manyun/base-ui.theme.datav-theme/dist/datav-theme.less';

.home {
  .fixedContent {
    min-width: 22vw;
    @media (min-width: 1600px) {
      width: 445px;
    }
    @media (max-width: 1600px) {
      width: 380px;
    }
    position: fixed;
    top: 80px;
    bottom: 27px;
    z-index: 2;
  }
  .cardfixedLeftContent {
    .fixedContent;
    left: 20px;
  }
  .cardfixedRightContent {
    .fixedContent;
    right: 20px;
  }
  .cardfixedRightContentNoBlock {
    top: 38%;
  }
  .NoPermissionFixed {
    .fixedContent;
    right: 20px;
    top: 50%;
  }
  .homeTitle {
    font-size: 17px;
    font-weight: 500;
    color: @heading-color;
    position: absolute;
    top: 46px;
    z-index: 20;
    @media (min-width: 1600px) {
      left: 508px;
    }
    @media (max-width: 1600px) {
      left: 420px;
    }
    @media (min-width: 1920px) {
      left: 25vw;
    }
  }
  .cornerMqp {
    @media (min-width: 1600px) {
      right: 508px;
    }
    @media (max-width: 1600px) {
      right: 420px;
    }
    @media (min-width: 1920px) {
      right: 25vw;
    }
    width: 80px;
    position: fixed;
    bottom: 24px;
    z-index: 2;
  }
}
