import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { normalize, schema } from 'normalizr';

import { ThemeProvider as ChartThemeProvider } from '@manyun/base-ui.chart.theme';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { DatavTheme } from '@manyun/base-ui.theme.datav-theme';
import { message } from '@manyun/base-ui.ui.message';

import { selectMyResources } from '@manyun/auth-hub.state.user';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { CitiesMapper, getCitiesMapper, syncCommonDataAction } from '@manyun/dc-brain.state.common';
import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { AlarmIdcs, fetchAlarmIdcs } from '@manyun/monitoring.service.fetch-alarm-idcs';
import { fetchBlocks } from '@manyun/resource-hub.service.fetch-blocks';
import {
  DataCenterList,
  fetchDataCenterList,
} from '@manyun/resource-hub.service.fetch-data-center-list';
import { getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { RegionIdcsCard } from './components/region-idcs-card';
import { BlockOverviewCard } from './components/​block-overview-card';
import { RegionMap } from './components/​region-map';
import { HomeContext } from './home-context';
import styles from './home.module.less';
import type { IdcInfo, RegionIdc } from './home.type';

const DEFAULT_DELAY_S = 3 * 1000;
export type RegionCenterList = {
  regionName: string;
  regionIdcChilds: (RegionIdc & { alarm?: boolean })[];
};

type RegionalDistributionListType = {
  [key: string]: {
    [x: string]: RegionIdc[];
  };
};

/** 首页
 *  https://manyun.yuque.com/fet/wir2zk/hlp7m41qrycgiwkz
 */
export function Home() {
  const config = useSelector(selectCurrentConfig);
  const [regionalDistributionList, setRegionalDistributionList] =
    useState<RegionalDistributionListType>();
  const [dataCenterList, setDataCenterList] = useState<DataCenterList[]>([]);
  const [dataCenterListLoading, setDataCenterListLoading] = useState(true);
  const citiesMapper = useSelector(getCitiesMapper);
  const configUtil = new ConfigUtil(config);
  const homeUrl = configUtil.getHomeUrl();
  const history = useHistory();
  const dispatch = useDispatch();
  const [idcInfo, setIdcInfo] = useState<IdcInfo[]>([]);
  const [loading, setLoading] = useState(false);

  const regionCenterList: RegionCenterList[] = useDeepCompareMemo(() => {
    if (!regionalDistributionList) {
      return [];
    }

    return Object.keys(regionalDistributionList).map(regionName => ({
      regionName,
      regionIdcChilds: Object.values(regionalDistributionList[regionName]).flat(),
    }));
  }, [regionalDistributionList]);

  /** 所有机房 */
  const idcs = regionCenterList.map(region => region.regionIdcChilds).flat();

  const [selectedIdc, setSelectedIdc] = useState<RegionIdc>(idcs[0]); // 轮播机房
  const [selectedIdcIndex, setSelectedIdcIndex] = useState<number>(0);
  const myResource = useSelector(selectMyResources);
  const myBlockResource = myResource
    .filter(resource => resource.type === 'BLOCK')
    .map(resource => resource.code);

  useEffect(() => {
    (async () => {
      if (citiesMapper) {
        const { error: alarmError, data: alarmData } = await fetchAlarmIdcs();
        if (alarmError) {
          message.error(alarmError.message);
        }
        const {
          error,
          data: { data },
        } = await fetchDataCenterList();
        setDataCenterListLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        const dataCenters = data.filter(center => center.permission);

        if (dataCenters.length) {
          const _regionalDistributionList = normalize<{ [x: string]: RegionIdc[] }>(
            dataCenters,
            getDataCenterListSchema(citiesMapper, dataCenters, alarmData)
          ).entities.regionData;
          setRegionalDistributionList(_regionalDistributionList);
          setDataCenterList(
            dataCenters.map(info => ({
              ...info,
              cityName: citiesMapper[info.cityCode]?.label,
              cityCode: info.cityCode,
              provinceName: citiesMapper[info.provinceCode]?.label,
              regionName: citiesMapper[info.regionCode]?.label,
            }))
          );
        }
      }
    })();
  }, [citiesMapper]);

  useDeepCompareEffect(() => {
    if (idcs.length) {
      setSelectedIdc(idcs[0]);
    }
  }, [idcs]);

  const handleSwitchIdc = useCallback(() => {
    // 机房默认按序轮播
    if (selectedIdcIndex < idcs.length) {
      const i = selectedIdcIndex + 1;
      setSelectedIdcIndex(i);
      setSelectedIdc(idcs[i]);
    } else {
      setSelectedIdcIndex(0);
      setSelectedIdc(idcs[0]);
    }
  }, [idcs, selectedIdcIndex]);

  useDeepCompareEffect(() => {
    if (idcs.length === 1) {
      return;
    }
    const blocksDelay = selectedIdc?.blockTags?.length
      ? (selectedIdc?.blockTags?.length + 1) * DEFAULT_DELAY_S
      : DEFAULT_DELAY_S;

    const currentInterval = window.setInterval(handleSwitchIdc, blocksDelay);

    if (selectedIdc?.carouseLocked) {
      window.clearInterval(currentInterval);
    }

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [handleSwitchIdc, selectedIdc, selectedIdcIndex, idcs]);

  const blockGuids = useMemo(() => selectedIdc?.blockTags.map(block => block) || [], [selectedIdc]);

  useEffect(() => {
    dispatch(
      syncCommonDataAction({
        strategy: {
          space: 'FORCED',
          deviceCategory: 'FORCED',
          citiesTree: 'IF_NULL',
          ticketTypes: 'FORCED',
          coords: 'FORCED',
        },
      })
    );
  }, [dispatch]);

  useEffect(() => {
    if (homeUrl !== '/') {
      // 提供外部用户(阳高，首页是工作台)，通过租户配置进入指定首页
      history.replace(window.decodeURIComponent(homeUrl));
    }
  }, [history, homeUrl]);

  useDeepCompareEffect(() => {
    if (selectedIdc?.guid) {
      setLoading(true);
      fetchBlocks({ pageSize: 100, idcTag: selectedIdc.guid, operationStatus: 'ON' })
        .then(async ({ data, error }) => {
          if (error) {
            message.error(error.message);
            return;
          }
          const blocks = data.data;
          if (!blocks.length) {
            setLoading(false);
            return;
          }
          const { data: fileData, error: fileError } = await fetchBizFileInfos({
            targetId: blockGuids[0],
            targetType: 'BLOCK',
            targetIdList: blockGuids,
          });
          if (fileError) {
            message.error(fileError.message);
          }

          setIdcInfo(
            idcs.map(idc => ({
              ...idc,
              blocks: blocks
                .filter(
                  block =>
                    block.idcTag === idc.guid &&
                    block.blockType.code === 'IDC_BLOCK' &&
                    myBlockResource.includes(block.guid)
                )
                .map(block => {
                  const blockCoverInfo = fileData.data?.find(file => file.targetId === block.guid);
                  return {
                    idcName: idc.name,
                    idcGuid: idc.guid,
                    permission: idc.permission,
                    cityCode: idcs.find(idc => idc.guid === block.idcTag)?.cityCode,
                    guid: block.guid,
                    id: getSpaceGuidMap(block.guid).block!,
                    description: block.description,
                    safeLevel: block.blockProperties?.safeLevel,
                    designNormal: block.blockProperties?.designNormal,
                    operationTime: block.operationTime,
                    src: blockCoverInfo?.src,
                  };
                })
                .reverse(),
            }))
          );
          setLoading(false);
        })
        .finally(() =>
          setTimeout(() => {
            setLoading(false);
          }, 2000)
        );
    }
  }, [blockGuids, selectedIdc, myBlockResource]);

  return (
    <DatavTheme type="light">
      <ChartThemeProvider theme="datav_light">
        <HomeContext.Provider value={[{ selectedIdc }, { setSelectedIdc }]}>
          <div className={styles.home}>
            <RegionIdcsCard regionCenterList={regionCenterList} loading={dataCenterListLoading} />
            <BlockOverviewCard idcInfo={idcInfo} loading={loading} />
            <RegionMap idcInfo={idcInfo} dataCenterList={dataCenterList} />
          </div>
        </HomeContext.Provider>
      </ChartThemeProvider>
    </DatavTheme>
  );
}

const getDataCenterCitySchema = (citiesMapper: CitiesMapper, alarmData: AlarmIdcs) => {
  if (!citiesMapper) {
    return [];
  }
  return [
    new schema.Entity(
      'cityData',
      {},
      {
        idAttribute: 'cityCode',
        processStrategy: idcInfo => {
          return [
            {
              guid: idcInfo.guid,
              name: idcInfo.name,
              permission: idcInfo.permission,
              blockTags:
                idcInfo.blockTags?.map((block: string) => idcInfo.guid + '.' + block) ?? [],
              provinceName: citiesMapper[idcInfo.provinceCode]?.label,
              cityName: citiesMapper[idcInfo.cityCode]?.label,
              cityCode: idcInfo.cityCode,
              regionCode: citiesMapper[idcInfo.regionCode]?.label,
              regionName: citiesMapper[idcInfo.regionCode]?.label,
              alarm: alarmData?.[idcInfo.guid],
            },
          ];
        },
        mergeStrategy: (entityA, entityB) => {
          return [...entityA, ...entityB];
        },
      }
    ),
  ];
};

const getDataCenterListSchema = (
  citiesMapper: CitiesMapper,
  dataCenters: DataCenterList[],
  alarmData: AlarmIdcs
) => {
  if (!citiesMapper) {
    return [];
  }
  return [
    new schema.Entity(
      'regionData',
      {},
      {
        idAttribute: value => {
          return citiesMapper[value.regionCode]?.label || value.regionCode;
        },
        processStrategy: ({ provinceCode, cityCode }) => {
          const province = citiesMapper[provinceCode]?.label || provinceCode;
          const city = citiesMapper[cityCode]?.label || cityCode;
          const region = province === city ? province : province + city;
          const entities = normalize(
            dataCenters,
            getDataCenterCitySchema(citiesMapper, alarmData)
          ).entities;
          return {
            [region]: entities?.cityData ? entities.cityData[cityCode] : {},
          };
        },
      }
    ),
  ];
};
