import React from 'react';

import type { RegionIdc } from './home.type';

type Values = {
  /** 选中机房 */
  selectedIdc: RegionIdc | undefined;
};

export type Handlers = {
  setSelectedIdc?: (idc: RegionIdc) => void;
};

const noop = () => {};
const initialValue: [Values, Handlers] = [
  {
    selectedIdc: undefined,
  },
  {
    setSelectedIdc: noop,
  },
];

export const HomeContext = React.createContext<[Values, Handlers]>(initialValue);
