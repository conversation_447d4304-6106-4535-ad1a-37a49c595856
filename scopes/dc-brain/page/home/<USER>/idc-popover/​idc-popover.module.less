@import (reference) '@manyun/base-ui.theme.datav-theme/dist/datav-theme.less';

.idcLine {
  position: absolute;
  width: 320px;
  bottom: 15px;
  right: -21px;
}

.idcContainer {
  top: -54%;
  left: -70%;
  position: absolute;
}

.idcContent {
  width: 215px;
  height: 50px;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  display: flex;
  align-items: center;
  padding: 0 12px;
  justify-content: space-between;
}
.idcUnSelectedContent {
  .idcContent;
  background-image: url('../../assets/idc-unselected.png');
  span {
    color: @base-primary;
  }
}

.idcSelectedContent {
  .idcContent;
  background-image: url('../../assets/idc-selected.png');
  span {
    color: @white;
  }
}
