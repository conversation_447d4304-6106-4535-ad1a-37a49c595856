import React, { useState } from 'react';

import debounce from 'lodash.debounce';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Card } from '@manyun/base-ui.ui.card';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import styles from '../../home.module.less';
import { RegionCenterList } from '../../home.type';
import { RegionCollapse } from '../​region-collapse';

export type RegionIdcsCardProps = {
  regionCenterList: RegionCenterList[];
  loading: boolean;
};

export function RegionIdcsCard({ regionCenterList, loading }: RegionIdcsCardProps) {
  const [searchKey, setSearchKey] = useState('');

  const onSearchRegion = debounce((searchKey: string) => {
    setSearchKey(searchKey);
  }, 300);

  const centerList: RegionCenterList[] = useDeepCompareMemo(() => {
    if (!searchKey) {
      return regionCenterList;
    }

    return regionCenterList
      .filter(
        region =>
          region.regionIdcChilds.findIndex(
            idcItem =>
              idcItem.name.toLowerCase().indexOf(searchKey.toLowerCase()) > -1 ||
              idcItem.guid.toLowerCase().indexOf(searchKey.toLowerCase()) > -1
          ) > -1
      )
      .map(region => ({
        ...region,
        regionIdcChilds: region.regionIdcChilds.filter(
          idcItem =>
            idcItem.name.toLowerCase().indexOf(searchKey.toLowerCase()) > -1 ||
            idcItem.guid.toLowerCase().indexOf(searchKey.toLowerCase()) > -1
        ),
      }));
  }, [searchKey, regionCenterList]);

  return (
    <div
      style={{
        position: 'relative',
      }}
    >
      <Typography.Title showBadge className={styles.homeTitle}>
        普洛斯数据中心地域分布
      </Typography.Title>
      <Card
        style={{ minWidth: '20vw' }}
        className={styles.cardfixedLeftContent}
        bodyStyle={{
          overflowY: 'auto',
          height: '83%',
          padding: '8px',
        }}
        title={
          <Space direction="vertical" size="large" style={{ display: 'flex' }}>
            <Input
              style={{ width: '200px', marginBottom: '10px' }}
              allowClear
              placeholder="搜索机房名称及编号"
              onChange={e => onSearchRegion(e.target.value)}
            />
          </Space>
        }
        loading={loading}
      >
        {centerList.length
          ? centerList.map(region => <RegionCollapse key={region.regionName} region={region} />)
          : null}
      </Card>
    </div>
  );
}
