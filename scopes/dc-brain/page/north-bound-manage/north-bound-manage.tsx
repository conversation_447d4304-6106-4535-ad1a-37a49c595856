import React, { useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { NORTH_BOUND_CREATE, NORTH_BOUND_EDIT } from '@manyun/dc-brain.route.admin-routes';
import { deleteNorth } from '@manyun/dc-brain.service.delete-north';
import {
  NorthItem,
  SvcQuery,
  fetchNorthByCondition,
} from '@manyun/dc-brain.service.fetch-north-by-condition';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { getCloumns, getRealParams } from './utils';

type Pagination = {
  pageNum: number;
  pageSize: number;
  total: number;
};
export function NorthBoundManage() {
  const history = useHistory();
  const [loading, setLoading] = useState<boolean>(false);
  const pagination = useRef<Pagination>({ pageNum: 1, pageSize: 10, total: 0 });

  const [name, setName] = useState<string>('');
  const [path, setPath] = useState<string>();
  const [available, setAvailable] = useState<boolean>();

  const [northList, setNorthList] = useState<NorthItem[]>();

  useEffect(() => {
    getList({});
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getList = async (params: SvcQuery) => {
    const commonParams = {
      pageNum: pagination.current.pageNum,
      pageSize: pagination.current.pageSize,
      name,
      path,
      available,
      ...params,
    };
    const { pageNum, pageSize } = commonParams;
    pagination.current.pageNum = pageNum;
    pagination.current.pageSize = pageSize;

    const realParams = getRealParams(commonParams);
    setLoading(true);
    const { data, error } = await fetchNorthByCondition(realParams);
    if (error) {
      message.error(error);
      return;
    }
    setNorthList(data.data);
    pagination.current.total = data.total;
    setLoading(false);
  };

  const onSearch = (value: string) => {
    setName(value);
    getList({ name: value, pageNum: 1 });
  };

  const onPathChange = (value: string) => {
    setPath(value);
    getList({ path: value, pageNum: 1 });
  };

  const onAvailableSelect = (value: boolean) => {
    setAvailable(value);
    getList({ available: value, pageNum: 1 });
  };

  const onChangePage = (pageNum: number, pageSize: number) => {
    getList({ pageNum, pageSize });
  };

  const onToCreate = (id?: number) => {
    if (id) {
      history.push(NORTH_BOUND_EDIT.replace(':id', String(id)));
    } else {
      history.push(NORTH_BOUND_CREATE);
    }
  };

  const onHandleDelete = async (id: number) => {
    const { error } = await deleteNorth({ id });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功！');
    getList({});
  };

  return (
    <Card>
      <p style={{ fontSize: 16 }}>北向列表</p>
      <Space>
        <Button type="primary" onClick={() => onToCreate()}>
          创建北向
        </Button>
        <Input.Search placeholder="北向名称/IP" onSearch={onSearch} />
        <LocationTreeSelect
          showSearch
          allowClear
          authorizedOnly
          value={path}
          onChange={onPathChange}
          style={{ width: 216 }}
          placeholder="位置"
        />
        <Select
          style={{ width: 216 }}
          placeholder="停用状态"
          value={available}
          onSelect={onAvailableSelect}
        >
          <Select.Option value={true}>启用</Select.Option>
          <Select.Option value={false}>停用</Select.Option>
        </Select>
      </Space>
      <Table
        style={{ marginTop: 16 }}
        scroll={{ x: 'max-content' }}
        dataSource={northList}
        rowKey="id"
        loading={loading}
        columns={getCloumns({ onToCreate, onHandleDelete })}
        pagination={{
          total: pagination.current.total,
          current: pagination.current.pageNum,
          pageSize: pagination.current.pageSize,
          onChange: onChangePage,
        }}
      />
    </Card>
  );
}
