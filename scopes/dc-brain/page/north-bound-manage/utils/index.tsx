import React from 'react';

import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { NorthItem } from '@manyun/dc-brain.service.fetch-north-by-condition';

export const getCloumns = ({
  onToCreate,
  onHandleDelete,
}: {
  onToCreate: (arg: number) => void;
  onHandleDelete: (arg: number) => void;
}) => {
  const columns: ColumnType<NorthItem>[] = [
    {
      title: '北向连接名称',
      dataIndex: 'name',
      fixed: 'left',
    },
    {
      title: '位置',
      dataIndex: 'path',
    },
    {
      title: '北向协议类型',
      dataIndex: 'protocol',
    },
    {
      title: 'IP',
      dataIndex: 'ips',
    },
    {
      title: '端口',
      dataIndex: 'port',
    },
    {
      title: '通信状态',
      dataIndex: 'socketStatus',
      render: text => {
        if (text) {
          return <Tag color="green">通信正常</Tag>;
        }
        return <Tag color="red">通信中断</Tag>;
      },
    },
    {
      title: '北向状态',
      dataIndex: 'available',
      render: text => {
        if (text) {
          return <Tag color="green">启用</Tag>;
        }
        return <Tag>停用</Tag>;
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            <Typography.Link onClick={() => onToCreate(record.id)}>编辑</Typography.Link>
            <Popconfirm title="您即将删除此数据" onConfirm={() => onHandleDelete(record.id)}>
              <Typography.Link>删除</Typography.Link>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];
  return columns;
};

export const getRealParams = (params: Record<string, any>) => {
  const newParams = Object.keys(params)
    .filter(key => params[key] !== undefined && params[key] !== null && params[key] !== '')
    .reduce((result, key) => ({ ...result, [key]: params[key] }), {});

  return newParams;
};
