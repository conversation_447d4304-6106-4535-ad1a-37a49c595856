import React, { ReactElement, ReactNode, useEffect, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';

import { NORTH_BOUND_MANAGE } from '@manyun/dc-brain.route.admin-routes';
import { fetchNorthByCondition } from '@manyun/dc-brain.service.fetch-north-by-condition';
import { fetchNorthProtocol } from '@manyun/dc-brain.service.fetch-north-protocol';
import { updateNorth } from '@manyun/dc-brain.service.update-north';
import { updateNorthBlackUser } from '@manyun/dc-brain.service.update-north-black-user';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { PushSetting } from './components/push-settting';
import styles from './north-bound-create.module.less';
import { protocolMap } from './utils';

export type NorthBoundCreateProps = {
  children?: ReactNode;
};

type FormItem = {
  label: string;
  name: string;
  render: ReactElement;
  setting?: Record<string, string>;
  rules?: Record<string, any>[];
};
export type BlackUser =
  | { idcTag: string | number }
  | { blockTag: string | number }
  | { roomTag: string | number }
  | { deviceGuid: string | null; deviceType: string | null }
  | { pointCode: string };

export function NorthBoundCreate({ children }: NorthBoundCreateProps) {
  const [form] = Form.useForm();
  const [pushForm] = Form.useForm();
  const history = useHistory();
  const { id } = useParams<{ id: string }>();

  const [protocolList, setProtocolList] = useState<string[]>([]);
  const [path, setPath] = useState<string>();
  const [protocol, setProtocol] = useState<string>();

  const [blackUserList, setBlackUserList] = useState<BlackUser[]>([]);

  useEffect(() => {
    getProtocolList();
  }, []);

  const getProtocolList = async () => {
    const { data, error } = await fetchNorthProtocol();
    if (!error) {
      setProtocolList(data.data);
    }
  };

  useEffect(() => {
    (async () => {
      if (id) {
        const { data, error } = await fetchNorthByCondition({ id });
        if (error) {
          message.error(error);
          return;
        }
        if (data.data.length) {
          const { name, path, protocol, username, password, ips, port, available, aggPush } =
            data.data[0];
          setPath(path);
          setProtocol(protocol);
          form.setFieldsValue({
            name,
            path,
            protocol,
            username,
            password,
            ips,
            port: port ? String(port) : '',
            available,
          });
          pushForm.setFieldsValue({
            aggPush: aggPush === 1 ? true : false,
          });
        }
      } else {
        form.setFieldsValue({
          available: true,
        });
        pushForm.setFieldsValue({
          aggPush: true,
        });
      }
    })();
  }, [form, id, pushForm]);

  const formItems: FormItem[] = [
    {
      label: '请选择',
      name: 'name',
      rules: [
        { required: true, message: '北向名称必填!' },
        { max: 30, message: '最多输入 30 个字符！' },
      ],
      render: <Input placeholder="北向名称/IP" style={{ width: 265 }} />,
    },
    {
      label: '位置',
      name: 'path',
      rules: [{ required: true, message: '位置必填!' }],
      render: (
        <LocationTreeSelect
          disabledTypes={['IDC']}
          showSearch
          authorizedOnly
          placeholder="请选择"
          onChange={value => {
            setPath(value);
          }}
          disabled={!!id}
          style={{ width: 265 }}
        />
      ),
    },
    {
      label: '北向协议类型',
      name: 'protocol',
      rules: [{ required: true, message: '北向协议类型必填!' }],
      render: (
        <Select
          style={{ width: 265 }}
          placeholder="请选择"
          disabled={!!id}
          onSelect={(value: string) => {
            setProtocol(value);
          }}
        >
          {protocolList.map(item => {
            return (
              <Select.Option value={item} key={item}>
                {item}
              </Select.Option>
            );
          })}
        </Select>
      ),
    },
    {
      label: 'ip',
      name: 'ips',
      rules: [{ required: protocol === protocolMap.JS, message: 'ips必填!' }],
      render: <Input placeholder="请输入" style={{ width: 265 }} />,
    },
    {
      label: '端口',
      name: 'port',
      rules: [
        { required: protocol === protocolMap.JS, message: '端口必填!' },
        {
          pattern: /^[0-9]+$/,
          message: '端口必须为数字',
        },
        { max: 5, message: '最多输入 5 个字符！' },
      ],
      render: <Input placeholder="请输入" style={{ width: 265 }} />,
    },
    {
      label: '用户名',
      name: 'username',
      rules: [{ max: 30, message: '最多输入 30 个字符！' }],
      render: <Input placeholder="请输入" style={{ width: 265 }} />,
    },
    {
      label: '密码',
      name: 'password',
      rules: [{ max: 32, message: '最多输入 32 个字符！' }],
      render: <Input placeholder="请输入" style={{ width: 265 }} />,
    },
    {
      label: '北向状态',
      name: 'available',
      setting: { valuePropName: 'checked' },
      rules: [{ required: true, message: '北向状态必填!' }],
      render: <Switch checkedChildren="开启" unCheckedChildren="关闭" defaultChecked />,
    },
  ];

  const onOk = async () => {
    const { name, path, protocol, username, password, ips, port, available } =
      await form.validateFields();
    const { aggPush } = await pushForm.validateFields();

    const params = {
      id: +id,
      name,
      path,
      protocol,
      username,
      password,
      ips,
      port,
      available,
      aggPush: aggPush ? 1 : 0,
    };

    const { error, data } = await updateNorth(params);

    if (error) {
      message.error(error.message);
      return;
    }
    if (id) {
      message.success('更新成功！');
    } else {
      message.success('添加成功！');
    }

    const newBlackUserList: BlackUser[] = blackUserList.map(item => {
      return { ...item, channelId: data };
    });
    const { error: blackUserError } = await updateNorthBlackUser(newBlackUserList);
    if (blackUserError) {
      message.error(blackUserError.message);
      return;
    }
    history.push(NORTH_BOUND_MANAGE);
  };

  const getBlackUserList = (list: BlackUser[]) => {
    setBlackUserList(list);
  };

  const layout = {
    labelCol: { span: 2 },
    wrapperCol: { span: 16 },
  };

  return (
    <>
      <Card>
        <p style={{ fontSize: 16 }}>北向连接设置</p>
        <Form form={form} {...layout}>
          {formItems.map(item => {
            const { label, name, setting, rules, render } = item;
            return (
              <Form.Item label={label} key={name} name={name} rules={rules} {...setting}>
                {React.cloneElement(render, {
                  style: { ...render.props?.style },
                })}
              </Form.Item>
            );
          })}
        </Form>
      </Card>
      <PushSetting form={pushForm} getBlackUserList={getBlackUserList} spaceGuid={path} />
      <Row align="middle" justify="center" className={styles.footer}>
        <Space>
          <Button type="primary" onClick={onOk}>
            提交
          </Button>
          <Button onClick={() => history.push(NORTH_BOUND_MANAGE)}>取消</Button>
        </Space>
      </Row>
    </>
  );
}
