import React from 'react';

import get from 'lodash.get';

import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { NorthBlackUser } from '@manyun/dc-brain.service.fetch-north-black-user';
import { Point } from '@manyun/monitoring.model.point';

const emptyText = '--';
export const getSpaceCloumns = (onDelete: (arg: number) => void) => {
  const columns: ColumnType<NorthBlackUser>[] = [
    {
      title: '位置',
      dataIndex: 'roomTag',
      render: (_, record) => {
        if (record.roomTag) {
          return record.roomTag;
        }
        return record.blockTag;
      },
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => {
        return (
          <Popconfirm title="您即将删除此数据" onConfirm={() => onDelete(record)}>
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
        );
      },
    },
  ];
  return columns;
};

export const getDeviceCloumns = (onDelete: (arg: number) => void) => {
  const columns: ColumnType<NorthBlackUser>[] = [
    {
      title: '设备',
      dataIndex: 'deviceName',
      fixed: 'left',
    },
    {
      title: '位置',
      dataIndex: 'roomTag',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => {
        return (
          <Popconfirm title="您即将删除此数据" onConfirm={() => onDelete(record)}>
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
        );
      },
    },
  ];
  return columns;
};

export const getPointCloumns = (onDelete: (arg: number) => void) => {
  const columns: ColumnType<NorthBlackUser>[] = [
    {
      title: '测点名称',
      dataIndex: 'pointName',
      fixed: 'left',
    },
    {
      title: '设备',
      dataIndex: 'deviceName',
    },
    {
      title: '位置',
      dataIndex: 'roomTag',
    },
    {
      title: '操作',
      dataIndex: 'operation',
      render: (_, record) => {
        return (
          <Popconfirm title="您即将删除此数据" onConfirm={() => onDelete(record)}>
            <Typography.Link>删除</Typography.Link>
          </Popconfirm>
        );
      },
    },
  ];
  return columns;
};

export const getPointModalCloumns = () => {
  const columns: ColumnType<Point>[] = [
    {
      title: '测点id',
      dataIndex: 'code',
      fixed: 'left',
    },
    {
      title: '测点名称',
      dataIndex: 'name',
    },
    {
      title: '类型',
      dataIndex: 'dataType',
      render: (text: { code: string; name: string }) => {
        return get(text, 'name', emptyText);
      },
    },
    {
      title: '单位',
      dataIndex: 'unit',
      render: (text: string) => {
        return text || emptyText;
      },
    },
  ];
  return columns;
};

export const protocolMap = {
  BYTE: '字节私有协议',
  JS: '金山私有协议',
};
