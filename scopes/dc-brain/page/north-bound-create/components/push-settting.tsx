import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';

import get from 'lodash.get';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Table } from '@manyun/base-ui.ui.table';
import type { DataNode } from '@manyun/base-ui.ui.tree';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  NorthBlackUser,
  fetchNorthBlackUser,
} from '@manyun/dc-brain.service.fetch-north-black-user';
import { updateNorthBlackUser } from '@manyun/dc-brain.service.update-north-black-user';
import { getSpaceGuid, getSpaceGuidMap } from '@manyun/resource-hub.util.space-guid';

import { getDeviceCloumns, getPointCloumns, getSpaceCloumns } from '../utils';
import { DeviceDrawer } from './device-drawer';
import { PointBlackUser, PointDrawer } from './point-drawer';
import { SpaceDrawer } from './space-drawer';

export type PushSettingProps = {
  getBlackUserList: (arg: NorthBlackUser[]) => void;
  form: FormInstance<any>;
  spaceGuid: string | undefined;
};

type SpaceItem = DataNode & {
  type?: string;
};

export function PushSetting({ form, getBlackUserList, spaceGuid }: PushSettingProps) {
  const { id } = useParams<{ id: string }>();

  const [spaceList, setSpaceList] = useState<NorthBlackUser[]>([]);
  const [deviceList, setDeviceList] = useState<NorthBlackUser[]>([]);
  const [pointList, setPointList] = useState<NorthBlackUser[]>([]);

  useEffect(() => {
    const newBlackUserList = [...spaceList, ...deviceList, ...pointList].filter(
      item => !item.channelId
    );
    getBlackUserList(newBlackUserList);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [spaceList, deviceList, pointList]);

  useEffect(() => {
    if (id) {
      getInitBlackList();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  useEffect(() => {
    if (!id) {
      setSpaceList([]);
      setDeviceList([]);
      setPointList([]);
    }
  }, [spaceGuid, id]);

  const getInitBlackList = async () => {
    const { data: blackSpaceData, error: spaceError } = await fetchNorthBlackUser({
      channelId: +id,
      type: 2,
    });

    if (!spaceError) {
      setSpaceList(blackSpaceData.data);
    }
    const { data: blackDeviceData, error: blackDeviceError } = await fetchNorthBlackUser({
      channelId: +id,
      type: 1,
    });

    if (!blackDeviceError) {
      setDeviceList(blackDeviceData.data);
    }

    const { data: blackPointData, error: blackPointError } = await fetchNorthBlackUser({
      channelId: +id,
      type: 0,
    });
    if (!blackPointError) {
      setPointList(blackPointData.data);
    }
  };

  const onCheckSpaceList = (list: SpaceItem[]) => {
    const spaceGuids = spaceList.map(item => {
      if (!item.idcTag || !item.blockTag) {
        return null;
      }
      return getSpaceGuid(item.idcTag, item.blockTag, item.roomTag);
    });
    const newSpaceList = list
      .map(item => {
        if (spaceGuids.includes(item.key)) {
          return {};
        }
        const { idc, block, room } = getSpaceGuidMap(item.key.toString());
        return {
          idcTag: idc,
          blockTag: block,
          roomTag: room,
          flag: 2,
          type: 1,
        };
      })
      .filter(item => item.idcTag);

    setSpaceList([...newSpaceList, ...spaceList]);
  };

  const onCheckDeviceList = (list: DataNode[]) => {
    const deviceGuids = deviceList.map(item => item.deviceGuid);

    const newDeviceList: NorthBlackUser[] = list
      .map(item => {
        const { idc, block, room, cabinet } = getSpaceGuidMap(item.key.toString());
        return {
          idcTag: idc,
          blockTag: block,
          roomTag: room,
          deviceGuid: cabinet,
          deviceName: item.title,
          deviceType: get(item, ['device', 'deviceCategory', 'level3'], null),
          flag: 1,
          type: 1,
        };
      })
      .filter(item => !deviceGuids.includes(item.deviceGuid));

    setDeviceList([...deviceList, ...newDeviceList]);
  };

  const onCheckPointList = (list: PointBlackUser[]) => {
    const pointCodes = pointList.map(item => `${item.deviceGuid}${item.pointCode}`);
    const newPointList = list
      .map(item => {
        const { idc, block, room, cabinet } = getSpaceGuidMap(item.spaceGuid);
        return {
          idcTag: idc,
          blockTag: block,
          roomTag: room || '虚拟包间',
          deviceType: item.deviceType,
          deviceGuid: cabinet ?? item.spaceGuid,
          deviceName: item.deviceName,
          pointCode: item.code,
          pointName: item.name,
          flag: 0,
          type: 1,
        };
      })
      .filter(item => !pointCodes.includes(`${item.deviceGuid}${item.pointCode}`));

    setPointList([...pointList, ...newPointList]);
  };

  const onDeleteSpace = async (record: NorthBlackUser) => {
    setSpaceList(
      spaceList.filter(item => {
        const recordKey = getSpaceGuid(record.idcTag, record.blockTag, record.roomTag);
        const spaceItemKey = getSpaceGuid(item.idcTag, item.blockTag, item.roomTag);
        return recordKey !== spaceItemKey;
      })
    );
    if (!record.channelId) {
      return;
    }
    const { error } = await updateNorthBlackUser([{ ...record, type: 0 }]);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功');
  };

  const onDeleteDevice = async (record: NorthBlackUser) => {
    setDeviceList(deviceList.filter(item => item.deviceGuid !== record.deviceGuid));
    if (!record.channelId) {
      return;
    }
    const { error } = await updateNorthBlackUser([{ ...record, type: 0 }]);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功');
  };

  const onDeletePoint = async (record: NorthBlackUser) => {
    setPointList(
      pointList.filter(item => {
        const key = `${item.deviceGuid}${item.pointCode}`;
        const recordKey = `${record.deviceGuid}${record.pointCode}`;
        return recordKey !== key;
      })
    );
    if (!record.channelId) {
      return;
    }
    const { error } = await updateNorthBlackUser([{ ...record, type: 0 }]);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功');
  };

  return (
    <Card style={{ marginTop: 16, paddingBottom: 40 }}>
      <p style={{ fontSize: 16 }}>推送范围设置</p>
      <Form form={form} labelCol={{ span: 2.5 }}>
        <Form.Item
          label="加工聚合测点推送"
          name="aggPush"
          valuePropName="checked"
          rules={[{ required: true }]}
        >
          <Switch checkedChildren="开启" unCheckedChildren="关闭" defaultChecked />
        </Form.Item>
      </Form>
      <Typography.Text>
        屏蔽范围： 已设置{spaceList.length + deviceList.length + pointList.length}项
      </Typography.Text>
      <Row gutter={24} style={{ marginTop: 16 }}>
        <Col span={6}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <SpaceDrawer onSelect={onCheckSpaceList} spaceGuid={spaceGuid}>
              <Button>添加空间维度</Button>
            </SpaceDrawer>
            <Table dataSource={spaceList} columns={getSpaceCloumns(onDeleteSpace)} />
          </Space>
        </Col>
        <Col span={8}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <DeviceDrawer onSelect={onCheckDeviceList} spaceGuid={spaceGuid}>
              <Button>添加设备维度</Button>
            </DeviceDrawer>
            <Table
              rowKey="deviceGuid"
              dataSource={deviceList}
              columns={getDeviceCloumns(onDeleteDevice)}
            />
          </Space>
        </Col>
        <Col span={10}>
          <Space direction="vertical" style={{ width: '100%' }}>
            <PointDrawer onSelect={onCheckPointList} spaceGuid={spaceGuid}>
              <Button>添加测点</Button>
            </PointDrawer>
            <Table dataSource={pointList} columns={getPointCloumns(onDeletePoint)} />
          </Space>
        </Col>
      </Row>
    </Card>
  );
}
