import React, { ReactNode, useState } from 'react';

import type { DataNode, TreeProps } from 'antd/es/tree';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';

const { Text } = Typography;

export type DeviceDrawerProps = {
  children?: ReactNode;
  onSelect: (arg: DataNode[]) => void;
  spaceGuid: string | undefined;
};

export function DeviceDrawer({ children, onSelect, spaceGuid }: DeviceDrawerProps) {
  const [visible, setVisible] = useState(false);
  const [checkedNodes, setCheckedNodes] = useState<DataNode[]>([]);

  const handleOk = () => {
    onSelect(checkedNodes);
    setVisible(false);
  };

  const onClose = () => {
    setVisible(false);
  };

  const onCheck: TreeProps['onCheck'] = (_, { checkedNodes }) => {
    setCheckedNodes(checkedNodes);
  };

  const onShow = () => {
    if (!spaceGuid) {
      message.error('请先选择位置');
      return;
    }
    setVisible(true);
    setCheckedNodes([]);
  };

  return (
    <>
      <span onClick={onShow}>{children}</span>
      <Drawer
        title={
          <>
            屏蔽设备维度 <Text type="secondary"> 已选（{checkedNodes.length}）</Text>
          </>
        }
        placement="left"
        width={440}
        onClose={onClose}
        visible={visible}
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={handleOk}>
              保存
            </Button>
          </Space>
        }
      >
        <ResourceTree
          authorizedOnly
          treeMode={['BLOCK', 'ROOM', 'DEVICE']}
          checkable
          onCheck={onCheck}
          spaceGuid={spaceGuid}
          checkableNodes={['DEVICE']}
        />
      </Drawer>
    </>
  );
}
