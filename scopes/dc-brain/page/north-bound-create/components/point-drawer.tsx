import React, { Key, ReactNode, useState } from 'react';
import { useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { TreeProps } from '@manyun/base-ui.ui.tree';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectCurrentConfig } from '@manyun/dc-brain.state.config';
import { ConfigUtil } from '@manyun/dc-brain.util.config';
import { Point } from '@manyun/monitoring.model.point';
import { fetchPointsByCondition } from '@manyun/resource-hub.service.fetch-points-by-condition';
import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';

import { getPointModalCloumns } from '../utils';

const { Text } = Typography;

export type PointDrawerProps = {
  children?: ReactNode;
  onSelect: (arg: Point[]) => void;
  spaceGuid: string | undefined;
};

export type PointBlackUser = Point & {
  deviceName: string;
  spaceGuid: string;
};

export function PointDrawer({ children, onSelect: onSelectoints, spaceGuid }: PointDrawerProps) {
  const [visible, setVisible] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);

  const [selectedRowKeys, setSelectedRowKeys] = useState<Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<Point[]>([]);

  const [dataSource, setDataSource] = useState<PointBlackUser[]>([]);

  const config = useSelector(selectCurrentConfig);

  const handleOk = () => {
    onSelectoints(selectedRows);
    setVisible(false);
  };

  const onClose = () => {
    setVisible(false);
  };

  const onSelect: TreeProps['onSelect'] = async (_, event) => {
    const configUtil = new ConfigUtil(config);
    const roomDeviceType: string = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_ROOM
    );
    const blockDeviceType: string = configUtil.getOneDeviceType(
      ConfigUtil.constants.deviceTypes.SPACE_BLOCK
    );
    onClear();
    const { node } = event;
    const { key, title, device, type } = node;

    setLoading(true);
    const deviceType =
      type === 'DEVICE'
        ? device.deviceCategory.level3
        : type === 'ROOM'
        ? roomDeviceType
        : type === 'BLOCK'
        ? blockDeviceType
        : '';
    const {
      data: { data },
    } = await fetchPointsByCondition({
      spaceGuid: String(key),
      deviceType,
      dataTypeList: ['AI', 'DI'],
      pointTypeList: ['ORI', 'CAL_SPACE', 'AGG_SPACE', 'CAL_DEVICE', 'AGG_DEVICE', 'CUSTOM'],
    });
    setLoading(false);

    setDataSource(
      data.map(item => {
        return {
          ...item,
          deviceName: title,
          spaceGuid: key,
        };
      })
    );
  };

  const rowSelection = {
    selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: Point[]) => {
      setSelectedRowKeys(selectedRowKeys);
      setSelectedRows(selectedRows);
    },
  };

  const onShow = () => {
    if (!spaceGuid) {
      message.error('请先选择位置');
      return;
    }
    setVisible(true);
    onClear();
  };

  const onClear = () => {
    setSelectedRowKeys([]);
    setSelectedRows([]);
  };

  return (
    <>
      <span onClick={onShow}>{children}</span>
      <Drawer
        title={
          <>
            屏蔽测点维度 <Text type="secondary"> 已选（{selectedRowKeys.length}）</Text>
          </>
        }
        placement="left"
        width={808}
        onClose={onClose}
        visible={visible}
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={handleOk}>
              保存
            </Button>
          </Space>
        }
      >
        <Row>
          <ResourceTree
            authorizedOnly
            treeMode={['BLOCK', 'ROOM', 'DEVICE']}
            onSelect={onSelect}
            spaceGuid={spaceGuid}
            style={{ width: 300, height: 'calc(100vh - 85px)', overflow: 'auto' }}
          />
          <Table
            style={{ marginTop: 15, width: 450 }}
            scroll={{ x: 'max-content' }}
            loading={loading}
            rowKey="code"
            rowSelection={rowSelection}
            dataSource={dataSource}
            columns={getPointModalCloumns()}
          />
        </Row>
      </Drawer>
    </>
  );
}
