import React, { ReactNode, useState } from 'react';

import type { DataNode, TreeProps } from 'antd/es/tree';

import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { ResourceTree } from '@manyun/resource-hub.ui.resource-tree';

const { Text } = Typography;

export type SpaceDrawerProps = {
  children?: ReactNode;
  onSelect: (arg: DataNode[]) => void;
  spaceGuid: string | undefined;
};

export function SpaceDrawer({ children, onSelect, spaceGuid }: SpaceDrawerProps) {
  const [visible, setVisible] = useState<boolean>(false);

  const [checkedNodes, setCheckedNodes] = useState<DataNode[]>([]);

  const handleOk = () => {
    const blockCheckNode = checkedNodes.filter(item => item.type === 'BLOCK');
    if (blockCheckNode.length) {
      onSelect(blockCheckNode);
    } else {
      onSelect(checkedNodes);
    }
    setVisible(false);
  };

  const onClose = () => {
    setVisible(false);
  };

  const onCheck: TreeProps['onCheck'] = (_, { checkedNodes }) => {
    setCheckedNodes(checkedNodes);
  };

  const onShow = () => {
    if (!spaceGuid) {
      message.error('请先选择位置');
      return;
    }
    setVisible(true);
  };

  return (
    <>
      <span onClick={onShow}>{children}</span>
      <Drawer
        title={
          <>
            屏蔽空间维度 <Text type="secondary"> 已选（{checkedNodes.length}）</Text>
          </>
        }
        placement="left"
        width={440}
        onClose={onClose}
        visible={visible}
        extra={
          <Space>
            <Button onClick={onClose}>取消</Button>
            <Button type="primary" onClick={handleOk}>
              保存
            </Button>
          </Space>
        }
      >
        <ResourceTree
          authorizedOnly
          checkable
          treeMode={['BLOCK', 'ROOM']}
          spaceGuid={spaceGuid}
          onCheck={onCheck}
        />
      </Drawer>
    </>
  );
}
