/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-5
 *
 * @packageDocumentation
 */
import LeftOutlined from '@ant-design/icons/es/icons/LeftOutlined';
import RightOutlined from '@ant-design/icons/es/icons/RightOutlined';
import moment from 'moment';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useHistory, useParams } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import type { AuditLogJSON } from '@manyun/auth-hub.model.audit-log';
import { fetchAuditLogList } from '@manyun/auth-hub.service.fetch-audit-log-list';
import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { PageHeader } from '@manyun/base-ui.ui.page-header';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { FileDownload } from '@manyun/base-ui.util.file-download';
import type { File } from '@manyun/base-ui.util.guess-mime-type';
import { getCycsTxt } from '@manyun/dc-brain.page.excel-statements';
import {
  EXCEL_STATEMENT_LIST_ROUTE_PATH,
  type ExcelStatementDeteilParams,
  generateExcelStatementDeteilLocation,
} from '@manyun/dc-brain.route.admin-routes';
import { webRequest } from '@manyun/service.request';
import {
  useLazyFetchSchedulePageTurningId,
  useLazyTaskDetail,
  useTriggerExcelSchedule,
} from '@manyun/ticket.gql.client.tickets';
import {
  EXCEL_STATEMENT_SUBJOB_TYPE_TEXT,
  ExcelStatementSubjobType,
  PlanStatus,
  PlanType,
} from '@manyun/ticket.model.task';
import type { Cycle } from '@manyun/ticket.model.task';

export function ExcelStatement() {
  const [form] = Form.useForm();

  const columns: Array<ColumnType<AuditLogJSON>> = [
    {
      title: '报表时间',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (_, { createdAt }) => moment(createdAt).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      render: (_, record) => {
        const src = `${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=${
          record.filePath
        }&fileName=${window.encodeURIComponent(record.fileName)}`;
        return (
          <Button type="link" href={src} compact>
            下载
          </Button>
        );
      },
    },
  ];

  // 表格分页相关
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });

  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<AuditLogJSON[]>([]);
  const [selectedCodes, setSelectedCodes] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<AuditLogJSON[]>([]);
  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);
  const [triggerBtnLoading, setTriggerBtnLoading] = useState(false);

  const { id } = useParams<ExcelStatementDeteilParams>();
  const history = useHistory();
  const [getchSchedulePageTurningId, { data: idData }] = useLazyFetchSchedulePageTurningId();
  const [, { checkCode }] = useAuthorized();

  const interval = useRef<NodeJS.Timer | null>(null);

  const [triggerExcelSchedule] = useTriggerExcelSchedule({
    onCompleted(data) {
      if (data.triggerExcelSchedule.message) {
        message.error(data.triggerExcelSchedule.message);
        setTriggerBtnLoading(false);
        return;
      }
      interval.current = setInterval(() => {
        getList(1, 10);
        getTaskDetail({ variables: { id } });
      }, 3000);
    },
  });

  const fileDownloadObject = React.useMemo(() => new FileDownload(), []);
  const [getTaskDetail, { data: taskDetailData }] = useLazyTaskDetail({
    onCompleted(data) {
      if (data.taskDetail) {
        // 执行状态 0 为任务空闲状态
        if (data.taskDetail.executionStatus === '0') {
          setTriggerBtnLoading(false);
          interval.current && clearInterval(interval.current);
        }
        getchSchedulePageTurningId({
          variables: {
            query: {
              curId: Number(id),
              jobTypeList: [PlanType.ExcelReport],
              subJobType: data.taskDetail.mopType,
            },
          },
        });
        return;
      }

      message.error('初始化数据失败');
      return;
    },
  });
  const handleFilesDownload = useCallback(() => {
    const files = selectedRows.map(file => ({
      name: file.fileName,
      src: `${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=${
        file.filePath
      }&fileName=${window.encodeURIComponent(file.fileName)}`,
      ext: file.fileFormat,
    })) as File[];
    setDownloadLoading(true);
    try {
      fileDownloadObject.downloadFiles(files, moment().format('YYYY-MM-DD'));
    } catch {
      setDownloadLoading(false);
      return;
    }
    setDownloadLoading(false);
  }, [selectedRows, fileDownloadObject]);

  useEffect(() => {
    getList(1, 10);
    getTaskDetail({ variables: { id } });

    return () => {
      if (interval.current) {
        clearInterval(interval.current);
      }
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [id]);

  const getList = useCallback(
    async (page: number, pageSize: number) => {
      setSelectedCodes([]);
      setSelectedRows([]);
      setLoading(true);
      const { error, data } = await fetchAuditLogList({
        page: page,
        pageSize: pageSize,
        targetType: 'EXCEL_POINT_REPORT',
        fileType: 'EXCEL_POINT_REPORT_RES',
        targetId: id,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setTableData(data.data);
      setPagination({ page, pageSize, total: data.total });
    },
    [id]
  );
  return (
    <Container style={{ width: '100%', height: '100%' }}>
      <Space size="middle" direction="vertical" style={{ width: '100%' }}>
        <PageHeader
          style={{ padding: 0 }}
          title={taskDetailData?.taskDetail?.name}
          subTitle={
            <Space>
              <Typography.Text>
                {
                  EXCEL_STATEMENT_SUBJOB_TYPE_TEXT[
                    taskDetailData?.taskDetail?.mopType as ExcelStatementSubjobType
                  ]
                }
              </Typography.Text>

              <Tag
                color={
                  taskDetailData?.taskDetail?.isActivated?.value === PlanStatus.On
                    ? 'success'
                    : 'default'
                }
              >
                {taskDetailData?.taskDetail?.isActivated?.value === PlanStatus.On ? '启用' : '停用'}
              </Tag>
            </Space>
          }
          extra={
            <Space>
              {idData?.fetchSchedulePageTurningId?.lastId && (
                <Button
                  icon={<LeftOutlined />}
                  onClick={() => {
                    history.push(
                      generateExcelStatementDeteilLocation({
                        id: idData!.fetchSchedulePageTurningId!.lastId!.toString(),
                      })
                    );
                  }}
                />
              )}
              {idData?.fetchSchedulePageTurningId?.nextId && (
                <Button
                  icon={<RightOutlined />}
                  onClick={() => {
                    history.push(
                      generateExcelStatementDeteilLocation({
                        id: idData!.fetchSchedulePageTurningId!.nextId!.toString(),
                      })
                    );
                  }}
                />
              )}
            </Space>
          }
          onBack={() => history.push(EXCEL_STATEMENT_LIST_ROUTE_PATH)}
        />
        <Container style={{ width: '100%' }} color="default">
          <Typography.Text type="secondary">位置：</Typography.Text>
          <Typography.Text>{taskDetailData?.taskDetail?.blockGuid}</Typography.Text>
          <Divider type="vertical" />
          <Typography.Text type="secondary">报表排期：</Typography.Text>
          <Typography.Text>
            {taskDetailData?.taskDetail?.repeatCycle &&
              getCycsTxt(taskDetailData.taskDetail.repeatCycle as Cycle)}
          </Typography.Text>
          <Divider type="vertical" />
          <Typography.Text type="secondary">报表数量：</Typography.Text>
          <Typography.Text>{taskDetailData?.taskDetail?.taskResultNums}</Typography.Text>
          <Divider type="vertical" />
          <Typography.Text type="secondary">更新时间：</Typography.Text>
          <Typography.Text>
            {taskDetailData?.taskDetail?.modifyTime
              ? moment(taskDetailData.taskDetail.modifyTime).format('YYYY-MM-DD HH:mm:ss')
              : '--'}
          </Typography.Text>
          <Divider type="vertical" />
          <Typography.Text type="secondary">更新人：</Typography.Text>
          <Typography.Text>{taskDetailData?.taskDetail?.modifyUser?.name}</Typography.Text>
        </Container>
        <Space direction="horizontal">
          <Button
            disabled={selectedCodes.length === 0}
            loading={downloadLoading}
            onClick={() => handleFilesDownload()}
          >
            批量下载
          </Button>

          {taskDetailData?.taskDetail?.mopType &&
            checkCode(
              ExcelStatementSubjobType.ExcelReportCommon === taskDetailData.taskDetail.mopType
                ? 'element_excel-statement-trigger-common'
                : 'element_excel-statement-trigger-person'
            ) && (
              <Popconfirm
                key="remove"
                title={
                  <Space direction="vertical" size="large">
                    <Space direction="vertical">
                      <>您即将启动该报表生成任务，请耐心等待报表结果。</>
                      <>如需指定报表数据时间，请设置以下参数:</>
                    </Space>
                    <Form form={form}>
                      <Form.Item label="数据时间(T)" name="contextTime">
                        <DatePicker
                          style={{ width: 216 }}
                          format="YYYY-MM-DD HH:mm:ss"
                          disabledDate={currentDate => {
                            return currentDate && currentDate.diff(moment().endOf('day')) > 0;
                          }}
                          disabledTime={current => {
                            return {
                              disabledHours: () => {
                                if (current?.get('date') === moment().get('date')) {
                                  return range(moment().get('hour') + 1, 24);
                                }
                                return [];
                              },
                              disabledMinutes: selectedHour => {
                                if (
                                  current?.get('date') === moment().get('date') &&
                                  selectedHour === moment().get('hour')
                                ) {
                                  return range(moment().get('minute'), 60);
                                }
                                return [];
                              },
                            };
                          }}
                          showTime
                        />
                      </Form.Item>
                    </Form>
                  </Space>
                }
                okButtonProps={{ loading: triggerBtnLoading }}
                okText="确定"
                onConfirm={async () => {
                  const { contextTime } = await form.validateFields();
                  setTriggerBtnLoading(true);
                  triggerExcelSchedule({
                    variables: {
                      schId: id,
                      contextTime: contextTime?.valueOf(),
                    },
                  });
                }}
              >
                <Button type="primary" loading={triggerBtnLoading}>
                  {triggerBtnLoading ? '报表生成中' : '立即生成报表'}
                </Button>
              </Popconfirm>
            )}
        </Space>
        <Table<AuditLogJSON>
          columns={columns}
          scroll={{ x: 'max-content' }}
          style={{ width: '100%' }}
          rowKey="id"
          loading={loading}
          dataSource={tableData}
          pagination={{
            total: pagination.total,
            current: pagination.page,
            pageSize: pagination.pageSize,
            onChange: (page, pageSize) => {
              getList(page, pageSize);
            },
          }}
          rowSelection={{
            selectedRowKeys: selectedCodes,
            onChange: (selectedRowKeys, selectedRows) => {
              setSelectedCodes(keys => [
                ...keys.filter(key => !tableData.find(item => item.id === key)),
                ...selectedRowKeys,
              ]);
              setSelectedRows(rows => [
                ...rows.filter(row => !tableData.find(item => item.id === row.id)),
                ...selectedRows,
              ]);
            },
          }}
        />
      </Space>
    </Container>
  );
}

const range = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
