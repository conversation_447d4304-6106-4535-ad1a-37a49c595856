import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Container } from '@manyun/base-ui.ui.container';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType as BasicColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import {
  generateExcelStatementDeteilLocation,
  generateExcelStatementEditLocation,
  generateExcelStatementNewLocation,
} from '@manyun/dc-brain.route.admin-routes';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { useDeleteTask, useLazyTasks } from '@manyun/ticket.gql.client.tickets';
import type {
  Cycle,
  ExcelStatementSubjobType,
  MonthlyWorkDayType,
  PlanJSON,
  WeekType,
} from '@manyun/ticket.model.task';
import {
  CyclesType,
  DAY_WORK_WEEK_TEXT_MAP,
  EXCEL_STATEMENT_SUBJOB_TYPE_TEXT,
  Plan,
  PlanStatus,
  ExcelStatementSubjobType as SubJobType,
  WEEK_DAY_TEXT_MAP,
} from '@manyun/ticket.model.task';

import { ExcelStatementFilter } from './excel-statement-filter';
import type { FiltersFormValues } from './excel-statement-filter';

export type TaskFilters = { status?: string };

const createCode = {
  [SubJobType.ExcelReportCommon]: 'element_excel-statement-create-commom',
  [SubJobType.ExcelReportPerson]: 'element_excel-statement-create-person',
};

const editCode = {
  [SubJobType.ExcelReportCommon]: 'element_excel-statement-edit-commom',
  [SubJobType.ExcelReportPerson]: 'element_excel-statement-edit-person',
};

const deleteCode = {
  [SubJobType.ExcelReportCommon]: 'element_excel-statement-delete-commom',
  [SubJobType.ExcelReportPerson]: 'element_excel-statement-delete-person',
};

export function TaskList({ subJobType }: { subJobType: ExcelStatementSubjobType }) {
  const [getTasks, { data, loading }] = useLazyTasks();
  const [taskFilters, setTaskFilters] = useState<TaskFilters>({});
  const [, { checkCode }] = useAuthorized();

  const [columns, setColumns] = useState<Array<BasicColumnType<PlanJSON>>>([
    {
      title: '机房楼栋',
      dataIndex: 'blockGuid',
    },
    {
      title: '报表名称',
      dataIndex: 'name',
    },
    {
      title: '状态',
      dataIndex: 'status',
      filters: [
        {
          text: '启用',
          value: PlanStatus.On,
        },
        {
          text: '停用',
          value: PlanStatus.Off,
        },
      ],
      filterMultiple: false,
      render: (_, record) => {
        const isTaskActivated = record.isActivated === PlanStatus.On;
        const titlePrefix = isTaskActivated ? '启用' : '停用';
        return <Tag color={isTaskActivated ? 'success' : 'default'}>{titlePrefix}</Tag>;
      },
    },
    {
      title: '报表排期',
      dataIndex: 'repeatCycle',
      render: (_, { repeatCycle }) => (repeatCycle ? getCycsTxt(repeatCycle) : '--'),
    },
    {
      title: '最近一次运行时间',
      dataIndex: 'lastExecuteTime',
      render: (_, record) => Plan.fromJSON(record).getFormattedLastExecuteAt(),
    },

    {
      title: '创建人',
      dataIndex: 'creatorName',
      render: (_, { creator }) => <UserLink external userId={creator.id} userName={creator.name} />,
    },
    {
      title: '报表数量',
      dataIndex: 'taskResultNums',
    },
    {
      title: '操作',
      dataIndex: 'action',
      width: 180,
      disabled: true,
      fixed: 'right',
      render: (_, record) => {
        const id = record.id.toString();
        return (
          <Space>
            <Link target="_blank" to={generateExcelStatementDeteilLocation({ id })}>
              查看报表
            </Link>
            {checkCode(editCode[subJobType]) && (
              <Link
                to={generateExcelStatementEditLocation({
                  id,
                  type: subJobType,
                })}
              >
                编辑
              </Link>
            )}
            {checkCode(deleteCode[subJobType]) && (
              <Popconfirm
                key="remove"
                title="您即将删除该报表，删除后不可恢复
              是否要删除？"
                okButtonProps={{ loading: deleteLoading }}
                okText="确认删除"
                onConfirm={async () => {
                  await deleteTask({ variables: { id: record.id } });
                }}
              >
                <Button compact type="link" disabled={record.isActivated === PlanStatus.On}>
                  删除
                </Button>
              </Popconfirm>
            )}
          </Space>
        );
      },
    },
  ]);
  const filterSearchParamsRef = useRef<FiltersFormValues | undefined>();
  const history = useHistory();

  const [pagination, setPagination] = useState<{
    pageNum: number;
    pageSize: number;
    total: number;
  }>({
    pageNum: 1,
    pageSize: 10,
    total: 0,
  });
  const [deleteTask, { loading: deleteLoading }] = useDeleteTask({
    onCompleted(data) {
      if (!data.deleteTask?.success) {
        message.error(data.deleteTask?.message ?? '删除失败');
        return;
      } else {
        message.success('删除成功');
        fetchTasks({
          pageNum: pagination.pageNum,
          pageSize: pagination.pageSize,
          filters: taskFilters,
        });
      }
    },
  });
  const fetchTasks = useCallback(
    async ({
      pageNum = 1,
      pageSize = 10,
      filters,
    }: {
      pageNum?: number;
      pageSize?: number;
      filters?: { status?: string };
    }) => {
      let blockGuidList =
        filterSearchParamsRef.current?.blockGuidList?.length === 2
          ? [filterSearchParamsRef.current?.blockGuidList[1]]
          : null;
      if (filterSearchParamsRef.current?.blockGuidList?.length === 1) {
        blockGuidList = [filterSearchParamsRef.current?.blockGuidList[0]];
      }

      const queryParams = {
        name: filterSearchParamsRef.current?.name,
        creatorId: filterSearchParamsRef.current?.creatorId?.value
          ? `${filterSearchParamsRef.current.creatorId.value}`
          : undefined,
        blockGuidList,
        taskStatus: filters?.status?.[0],
      };
      setPagination({ ...pagination, pageNum, pageSize });

      await getTasks({
        variables: {
          ...queryParams,
          jobTypeList: ['EXCEL_REPORT'],
          subJobType,
          pageNum,
          pageSize,
        },
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  useEffect(() => {
    fetchTasks({});

    if (subJobType === 'EXCEL_REPORT_PERSON') {
      const list = columns.filter(item => item.dataIndex !== 'creatorName');
      setColumns(list);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <Container style={{ width: '100%', height: '100%' }}>
      <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
        <ExcelStatementFilter
          subJobType={subJobType}
          onChange={(params: FiltersFormValues | undefined) => {
            filterSearchParamsRef.current = params;
            fetchTasks({
              pageNum: 1,
              pageSize: pagination.pageSize,
              filters: taskFilters,
            });
          }}
        />
        <Space style={{ display: 'flex', width: '100%' }} direction="vertical">
          <Space style={{ display: 'flex', width: '100%', justifyContent: 'space-between' }}>
            {checkCode(createCode[subJobType]) ? (
              <Button
                type="primary"
                onClick={() => {
                  history.push(generateExcelStatementNewLocation({ type: subJobType }));
                }}
              >
                {`新建${EXCEL_STATEMENT_SUBJOB_TYPE_TEXT[subJobType]}报表`}
              </Button>
            ) : (
              <span /> // 占位符不然没权限是列设置会串位
            )}
            <EditColumns
              uniqKey={`INDEPENDENT_TICKETS_EXCEL_TASK_KEY_${subJobType}`}
              defaultValue={columns}
              onChange={columns => {
                setColumns(columns);
              }}
            />
          </Space>

          <Table<PlanJSON>
            rowKey="id"
            dataSource={(data?.tasks?.data as PlanJSON[]) ?? []}
            pagination={{
              total: data?.tasks?.total,
              pageSize: pagination.pageSize,
              current: pagination.pageNum,
            }}
            scroll={{ x: 'max-content' }}
            loading={loading}
            columns={columns}
            onChange={(pagination, filters, _, { action }) => {
              if (action === 'paginate') {
                fetchTasks({
                  pageNum: pagination.current!,
                  pageSize: pagination.pageSize!,
                  filters: taskFilters,
                });
              }

              if (action === 'filter') {
                fetchTasks({
                  pageNum: 1,
                  pageSize: pagination.pageSize!,
                  filters: filters,
                });
                setTaskFilters(filters);
              }
            }}
          />
        </Space>
      </Space>
    </Container>
  );
}
export function getCycsTxt(cycles: Cycle) {
  if (!cycles) {
    return;
  }
  if (cycles.periodUnit === CyclesType.Year) {
    let txt = '';
    // 每逢
    if (cycles.dayConfig) {
      const days = cycles.dayConfig?.days?.map(item => {
        return item.substr(0, 2) + '月' + item.substr(2, 3) + '日';
      });
      txt = `每${cycles.period}年的${days?.join('、')}${getSchedulePostText(
        cycles.dayConfig.isRemove
      )}`;
    }
    // 在
    if (cycles.atDayConfig) {
      const days = cycles.atDayConfig.map(({ month }) => month + '月');
      txt = `每${cycles.period}年的${days.join('、')}的第${cycles.atDayConfig[0].sortNum}个${
        DAY_WORK_WEEK_TEXT_MAP[cycles.atDayConfig[0].atDayType as MonthlyWorkDayType]
      }`;
    }

    return txt;
  }
  if (cycles.periodUnit === CyclesType.Month) {
    let txt = '';

    // 每逢
    if (cycles.dayConfig) {
      txt = `每${cycles.period}月的${cycles.dayConfig.days?.join('、')}${getSchedulePostText(
        cycles.dayConfig.isRemove
      )}`;
    }

    // 在
    if (cycles.atDayConfig) {
      txt = `每${cycles.period}月的第${cycles.atDayConfig[0].sortNum}个${
        DAY_WORK_WEEK_TEXT_MAP[cycles.atDayConfig[0].atDayType as MonthlyWorkDayType]
      }`;
    }
    return txt;
  }
  if (cycles.periodUnit === CyclesType.Week) {
    let txt = '';
    const days = cycles.dayConfig.days?.map(item => WEEK_DAY_TEXT_MAP[item as WeekType]);
    txt = `每${cycles.period}周的${days?.join('、')}${getSchedulePostText(
      cycles.dayConfig.isRemove
    )}`;
    return txt;
  }
  if (cycles.periodUnit === CyclesType.Day) {
    const txt = `每${cycles.period}天${getSchedulePostText(cycles.dayConfig.isRemove)}`;
    return txt;
  }
  if (cycles.periodUnit === CyclesType.None) {
    return '无';
  }
  return;
}
function getSchedulePostText(isRemove: number) {
  if (isRemove === 0) {
    return '';
  } else if (isRemove === 1) {
    return '（如遇节假日，则自动取消当日排期）';
  } else {
    return '（如遇节假日，则自动顺延至下一个工作日）';
  }
}
