import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import type { QueryFilterProps } from '@manyun/base-ui.ui.query-filter';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

export type FiltersFormValues = {
  name?: string;
  jobTypeList?: string[];
  blockGuidList?: string;
  taskStatus?: string;
  creatorId?: { value: string; label: string };
};

export type ExcelStatementFilterProps = {
  subJobType: string;
  onChange: (params?: FiltersFormValues) => void;
} & Omit<QueryFilterProps<FiltersFormValues>, 'form' | 'items' | 'defaultExpanded'>;

export function ExcelStatementFilter({ subJobType, onChange, ...rest }: ExcelStatementFilterProps) {
  const [form] = Form.useForm();
  const items = [
    {
      label: '报表名称',
      name: 'name',
      control: <Input allowClear />,
    },
    {
      label: '机房楼栋',
      name: 'blockGuidList',
      control: (
        <LocationCascader
          authorizedOnly
          nodeTypes={['IDC', 'BLOCK']}
          maxTagCount="responsive"
          disabledNoChildsNodes={['IDC']}
          allowClear
        />
      ),
    },
    {
      label: '创建人',
      name: 'creatorId',
      control: <UserSelect allowClear />,
    },
  ];

  return (
    <Card>
      <QueryFilter<FiltersFormValues>
        {...rest}
        form={form}
        items={
          subJobType === 'EXCEL_REPORT_PERSON'
            ? items.filter(item => item.name !== 'creatorId')
            : items
        }
        onSearch={filterFormValues => {
          onChange(filterFormValues);
        }}
        onReset={() => {
          form.resetFields();
          onChange();
        }}
      />
    </Card>
  );
}
