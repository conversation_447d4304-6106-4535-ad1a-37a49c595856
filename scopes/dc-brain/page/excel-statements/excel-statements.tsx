/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-4
 *
 * @packageDocumentation
 */
import React, { useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { ExcelStatementSubjobType } from '@manyun/ticket.model.task';

import { TaskList } from './components/excel-statement-list';

export function ExcelStatements() {
  const [currentTab, setCurrentTab] = useState<
    ExcelStatementSubjobType.ExcelReportCommon | ExcelStatementSubjobType.ExcelReportPerson
  >(ExcelStatementSubjobType.ExcelReportCommon);
  const { search } = useLocation();

  useEffect(() => {
    const searchParams = getLocationSearchMap<{
      type?: ExcelStatementSubjobType;
    }>(search);
    if (searchParams && searchParams?.type) {
      setCurrentTab(searchParams.type);
    }
  }, [search]);

  const tabs = [
    {
      key: ExcelStatementSubjobType.ExcelReportCommon,
      label: '公共报表',
      children: <TaskList subJobType={ExcelStatementSubjobType.ExcelReportCommon} />,
    },
    {
      key: ExcelStatementSubjobType.ExcelReportPerson,
      label: (
        <Space align="baseline" size={0} style={{ alignItems: 'center' }}>
          个人报表
          <Explanation
            style={{ display: 'flex' }}
            iconType="question"
            tooltip={{ title: ' “个人报表”只对创建人开放' }}
          />
        </Space>
      ),
      children: <TaskList subJobType={ExcelStatementSubjobType.ExcelReportPerson} />,
    },
  ];
  return (
    <Container style={{ width: '100%', height: '100%' }}>
      <Tabs
        defaultActiveKey={ExcelStatementSubjobType.ExcelReportCommon}
        activeKey={currentTab}
        items={tabs}
        onChange={activeKey =>
          setCurrentTab(
            activeKey as
              | ExcelStatementSubjobType.ExcelReportCommon
              | ExcelStatementSubjobType.ExcelReportPerson
          )
        }
      />
    </Container>
  );
}
