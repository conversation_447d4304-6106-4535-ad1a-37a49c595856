import React from 'react';
import { useSelector } from 'react-redux';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps } from '@manyun/base-ui.ui.cascader';

export type RegionCascaderProps = CascaderProps;

export const RegionCascader = React.forwardRef<any, RegionCascaderProps>((props, ref?) => {
  const citiesTree = useSelector(selectCitiesTree);

  return <Cascader ref={ref} {...props} options={citiesTree} />;
});

RegionCascader.displayName = 'RegionCascader';

interface DataNode {
  label: React.ReactNode;
  /** Customize hover title */
  title?: string;
  value: string | number;
  disabled?: boolean;
  children?: DataNode[];
  isLeaf?: boolean;
}

type RootStoreState = {
  common: {
    citiesTree: DataNode[];
  };
};

function selectCitiesTree({ common: { citiesTree } }: RootStoreState) {
  return citiesTree;
}
