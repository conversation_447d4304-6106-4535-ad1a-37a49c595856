import React from 'react';

import { fireEvent, render, waitFor } from '@testing-library/react';

import { BasicAnyTargetLink } from './any-target-link.composition';

test('should redirect to `ticket` page', async () => {
  const { getByText } = render(<BasicAnyTargetLink />);
  fireEvent.click(getByText('YS21120900062'));
  await waitFor(() => {
    const rendered = getByText('path: /page/tickets/accept/YS21120900062');
    expect(rendered).toBeTruthy();
  });
});

test('should redirect to `event-detail` page ', async () => {
  const { getByText } = render(<BasicAnyTargetLink />);
  fireEvent.click(getByText('31'));
  await waitFor(() => {
    const rendered = getByText('path: /page/event-center/31');
    expect(rendered).toBeTruthy();
  });
});

test('should redirect to `borrow-return` page ', async () => {
  const { getByText } = render(<BasicAnyTargetLink />);
  fireEvent.click(getByText('222'));
  await waitFor(() => {
    const rendered = getByText('path: /page/borrows-and-return/1/detail');
    expect(rendered).toBeTruthy();
  });
});

test('should redirect to `notice-detail` page ', async () => {
  const { getByText } = render(<BasicAnyTargetLink />);
  fireEvent.click(getByText('123123'));
  await waitFor(() => {
    const rendered = getByText('path: /page/notice/1');
    expect(rendered).toBeTruthy();
  });
});

test('should redirect to `approval` page ', async () => {
  const { getByText } = render(<BasicAnyTargetLink />);
  fireEvent.click(getByText('123371003106557952'));
  await waitFor(() => {
    const rendered = getByText('path: /page/approve-center/detail?id=123371003106557952');
    expect(rendered).toBeTruthy();
  });
});
