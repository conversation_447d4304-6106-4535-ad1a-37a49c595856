import React from 'react';
import { Route, MemoryRouter as Router, useHistory, useParams } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { BPM_INSTANCE_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { TargetType } from '@manyun/notification-hub.model.on-site-messages';
import { NOTIFICATION_ROUTE_PATH } from '@manyun/notification-hub.route.notification-routes';
import { BORROWS_AND_RETURN_DETAIL } from '@manyun/resource-hub.route.resource-routes';
import { EVENT_DETAIL_ROUTE_PATH, TICKET_ROUTE_PATH } from '@manyun/ticket.route.ticket-routes';

import { AnyTargetLink } from './any-target-link';

export const BasicAnyTargetLink = () => {
  return (
    <ConfigProvider>
      <Router initialEntries={['/link']}>
        {/* <Route path={'/user'}>
          <AnyTargetLink type={TargetType.PERSON} id='1' name="admin" />
        </Route> */}
        <Route path="/link">
          <AnyTargetLink type={TargetType.ACCEPT} id="YS21120900062" name="YS21120900062" />
          <br />
          <AnyTargetLink type={TargetType.EVENT_DETAIL} id="31" name="31" />
          <br />
          <AnyTargetLink type={TargetType.BORROW_RETURN_DETAIL} id="1" name="222" />
          <br />
          <AnyTargetLink type={TargetType.NOTICE_DETAIL} id="1" name="123123" />
          <br />
          <AnyTargetLink
            type={TargetType.APPROVAL_DETAIL}
            id="123371003106557952"
            name="123371003106557952"
          />
        </Route>
        <Route path={TICKET_ROUTE_PATH}>
          <Show />
        </Route>
        <Route path={EVENT_DETAIL_ROUTE_PATH}>
          <Show />
        </Route>
        <Route path={BORROWS_AND_RETURN_DETAIL}>
          <Show />
        </Route>
        <Route path={NOTIFICATION_ROUTE_PATH}>
          <Show />
        </Route>
        <Route path={BPM_INSTANCE_ROUTE_PATH}>
          <Show />
        </Route>
      </Router>
    </ConfigProvider>
  );
};

function Show() {
  const data = useParams();
  const history = useHistory();
  return (
    <>
      <p>path: {history.location.pathname}</p>
      <p>params: {JSON.stringify(data)}</p>
    </>
  );
}
