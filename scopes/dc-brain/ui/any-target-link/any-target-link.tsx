import React, { useEffect, useState } from 'react';
import { Link } from 'react-router-dom';

import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { User } from '@manyun/auth-hub.ui.user';
import {
  AUTH_REQUEST_CREATOR_ROUTE_PATH,
  generateBPMRoutePath,
} from '@manyun/bpm.route.bpm-routes';
import { generateAlarmTransmissionUrl } from '@manyun/dc-brain.route.admin-routes';
import {
  generateCourseDetailRoutePath,
  generateExamRoutePath,
  generateMyCoursesLocation,
  generateMyExamPaperLocation,
} from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { MyExamTypes } from '@manyun/knowledge-hub.state.exams';
import { TargetType } from '@manyun/notification-hub.model.on-site-messages';
import { generateNoticeLocation } from '@manyun/notification-hub.route.notification-routes';
import {
  generateBorrowAndReturnDetailLocation,
  generateDeviceListUrl,
  generateSpareListUrl,
} from '@manyun/resource-hub.route.resource-routes';
import { fetchRooms } from '@manyun/resource-hub.service.fetch-rooms';
import { ENTRANCE_GUARD_CARD_MANAGEMENT_ROUTE_PATH } from '@manyun/sentry.route.routes';
import {
  generateChangeTicketDetail,
  generateDrillOrderRoutePath,
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateInspectionPlanDetailRoutePath,
  generateInventoryPlanDetailRoutePath,
  generateMaintainPlanDetailRoutePath,
  generateRiskRegisterDetailLocation,
  generateTicketLocation,
} from '@manyun/ticket.route.ticket-routes';

export type AnyTargetLinkProps = {
  id: string;
  name: string;
  text?: string;
  type: TargetType;
  render?: (data: { type: TargetType }) => React.ReactElement;
  useNativeLink?: boolean;
  target?: '_blank' | '_self' | '_parent' | '_top';
};

export function AnyTargetLink({
  type,
  id,
  name,
  text = name,
  render,
  useNativeLink,
  target = '_self',
}: AnyTargetLinkProps): JSX.Element {
  const [roomGuid, setRoomGuid] = useState('');
  const [roomTag, setRoomTag] = useState(false);

  useEffect(() => {
    const fetchRoomsbyDeviceType = async (id: string) => {
      const { error, data } = await fetchRooms({
        blockTag: id.split('=')[0].split('.')[1],
        idcTag: id.split('=')[0].split('.')[0],
        roomTypeList: ['WAREHOUSE'],
      });
      if (error) {
        return;
      } else {
        let roomGuids = '';
        for (const item of data.data) {
          roomGuids = roomGuids + item.guid + ',';
        }
        roomGuids = roomGuids.substring(0, roomGuids.length - 1);
        // console.log('roomGuids', roomGuids);

        setRoomGuid(roomGuids);
        setRoomTag(true);
      }
    };
    if (type === TargetType.DEVICE_INVENTORY_LIST) {
      fetchRoomsbyDeviceType(id);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  switch (type) {
    case TargetType.PERSON:
    case TargetType.PERSON_EDIT: {
      if ([undefined, null, ''].includes(id)) {
        return <>{text}</>;
      }
      return (
        <User.Link
          id={Number(id)}
          name={text}
          application={type === TargetType.PERSON_EDIT ? 'edit' : 'read'}
        />
      );
    }

    //工单
    case TargetType.INSPECTION:
    case TargetType.INVENTORY:
    case TargetType.MAINTENANCE:
    case TargetType.POWER:
    case TargetType.REPAIR:
    case TargetType.WAREHOUSE:
    case TargetType.ACCESS:
    case TargetType.DEVICE_GENERAL:
    case TargetType.ACCEPT:
    case TargetType.ON_OFF:
    case TargetType.RISK_CHECK:
    case TargetType.ACCESS_CARD_AUTH:
    case TargetType.IT_SERVICE:
    case TargetType.VISITOR: {
      return (
        <NativeLink
          path={generateTicketLocation({
            ticketType: type.toLowerCase(),
            id: id as string,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    // 演练单详情
    case TargetType.DRILL_SERVICE_DETAIL:
    case TargetType.EME: {
      return (
        <NativeLink
          path={generateDrillOrderRoutePath({ id: id })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    //事件详情、事件通报
    case TargetType.EVENT_REPORT:
    case TargetType.EVENT_DETAIL: {
      return (
        <NativeLink
          path={generateEventDetailRoutePath({
            id: id as string,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    case TargetType.N_EVENT_REPORT: {
      return (
        <NativeLink
          path={generateEvnetLocation({
            id: id,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    //公告详情
    case TargetType.NOTICE_DETAIL: {
      return (
        <NativeLink
          path={generateNoticeLocation({
            id: id,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    case TargetType.MAINTENANCE_POOL:
      if (typeof render !== 'function') {
        return <>{text}</>;
      }

      return render({ type });

    //详情
    case TargetType.BORROW_RETURN_DETAIL: {
      return (
        <NativeLink
          path={generateBorrowAndReturnDetailLocation({
            id: id as string,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    //审批详情
    case TargetType.APPROVAL_DETAIL: {
      return (
        <NativeLink
          path={generateBPMRoutePath({
            id,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    //告警传输
    case TargetType.ALARM_TRANSFER: {
      return (
        <NativeLink
          path={generateAlarmTransmissionUrl({
            id: String(id),
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 巡检计划
    case TargetType.FACILITY_XJ: {
      return (
        <NativeLink
          path={generateInspectionPlanDetailRoutePath({
            id: String(id),
            name: text,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 维护计划
    case TargetType.FACILITY_WB: {
      return (
        <NativeLink
          path={generateMaintainPlanDetailRoutePath({
            id: String(id),
            name: text,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 盘点计划
    case TargetType.FACILITY_PD: {
      return (
        <NativeLink
          path={generateInventoryPlanDetailRoutePath({
            id: String(id),
            name: text,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 设备库存
    case TargetType.DEVICE_INVENTORY_LIST: {
      return roomTag ? (
        <NativeLink
          path={generateDeviceListUrl({
            spaceGuidList: roomGuid,
            deviceType: id.split('=')[1],
            vendor: id.split('=')[2],
            productModel: id.split('=')[3],
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      ) : (
        <></>
      );
    }

    // 耗材库存
    case TargetType.SPARE_INVENTORY_LIST: {
      return (
        <NativeLink
          path={generateSpareListUrl({
            blockGuid: id.split('=')[0],
            deviceType: id.split('=')[1],
            vendor: id.split('=')[2],
            productModel: id.split('=')[3],
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 考试管理 - 考试详情
    case TargetType.EXAM_MANAGE_LIST: {
      return (
        <NativeLink
          path={generateExamRoutePath(Number(id))}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 当前考试列表
    case TargetType.EXAM_PARTICIPATE_LIST: {
      return (
        <NativeLink
          path={generateMyExamPaperLocation({
            type: MyExamTypes.MyAvailableExams,
            examName: text,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    // 历史考试列表
    case TargetType.EXAM_HISTORY_LIST: {
      return (
        <NativeLink
          path={generateMyExamPaperLocation({
            type: MyExamTypes.MyHistoryExams,
            examName: text,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    case TargetType.COURSE_OWNER: {
      return (
        <NativeLink
          path={generateMyCoursesLocation({
            courseName: text,
          })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    //课程详情
    case TargetType.COURSE_DETAIL: {
      return (
        <NativeLink
          path={generateCourseDetailRoutePath(Number(id))}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    //个人中心
    case TargetType.CERT_PERSONAL_CENTER: {
      return (
        <NativeLink
          path={generateUserProfileRoutePath({ id: id, tabKey: 'skill' })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    case TargetType.USER_CENTER: {
      return (
        <NativeLink
          path={generateUserProfileRoutePath({ id: id })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    // 风险登记册
    case TargetType.NEW_RISK_REGISTER: {
      return (
        <NativeLink
          path={generateRiskRegisterDetailLocation({ id: id })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    //权限申请
    case TargetType.AUTH_APPLY: {
      return (
        <NativeLink
          path={AUTH_REQUEST_CREATOR_ROUTE_PATH}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    // 变更详情
    case TargetType.CHANGE: {
      return (
        <NativeLink
          path={generateChangeTicketDetail({ id: id })}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }
    //门禁卡管理列表
    case TargetType.ENTRANCE_CARD_MANAGEMENT: {
      return (
        <NativeLink
          path={ENTRANCE_GUARD_CARD_MANAGEMENT_ROUTE_PATH}
          text={text}
          useNativeLink={useNativeLink}
        />
      );
    }

    default:
      return <>{text}</>;
  }

  function NativeLink({
    useNativeLink,
    path,
    text,
  }: {
    useNativeLink?: boolean;
    path:
      | string
      | {
          pathname: string;
          search: string;
        };
    text: string;
  }) {
    if (!useNativeLink) {
      return (
        <Link target={target} to={path}>
          {text}
        </Link>
      );
    }
    const href: string = typeof path == 'object' ? path.pathname + path.search : path;
    return (
      <a href={href} target={target}>
        {text}
      </a>
    );
  }
}
