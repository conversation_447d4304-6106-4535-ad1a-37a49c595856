import React, { useCallback, useEffect, useState } from 'react';
import { useLatest } from 'react-use';

import sortBy from 'lodash.sortby';

import { EditColumns as BasicEditColumns, genColumnKey } from '@manyun/base-ui.ui.edit-columns';
import type {
  EditColumnsProps as BasicEditColumnsProps,
  ColumnType,
} from '@manyun/base-ui.ui.edit-columns';
import { message } from '@manyun/base-ui.ui.message';

import { getUserInfo } from '@manyun/auth-hub.cache.user';
import { fetchColumns } from '@manyun/dc-brain.service.fetch-columns';
import { updateColumns } from '@manyun/dc-brain.service.update-columns';
import type { Column } from '@manyun/dc-brain.service.update-columns';

export type EditColumnsProps<RecordType = unknown> = {
  /**前端定义 全局唯一key */
  uniqKey: string;
} & Omit<BasicEditColumnsProps<RecordType>, 'value'>;

export function EditColumns<RecordType>({
  uniqKey,
  defaultValue,
  onChange,
  ...resetProps
}: EditColumnsProps<RecordType>) {
  const { userId } = getUserInfo();
  const [internalValue, setInternalValue] = useState<ColumnType<RecordType>[]>(defaultValue);
  const _defaultValue = useLatest(defaultValue);
  const onChangeRef = useLatest(onChange);

  const _loadColumns = useCallback(async () => {
    if (!userId) {
      return;
    }
    const { error, data } = await fetchColumns({
      type: uniqKey,
      userId: userId,
    });

    if (error) {
      message.error(error.message);
      return;
    }
    //数组为空则代表用户未进行定制，前端进行默认展示
    if (data.data.length > 0) {
      const columns = _defaultValue.current?.map((defaultCol, currentIndex) => {
        const exitCol = data.data.find(col => col.fieldName === genColumnKey(defaultCol));
        return exitCol
          ? {
              ...defaultCol,
              show: exitCol.show,
              disabled: exitCol.disable,
              fixed: exitCol.fixed ?? undefined,
              sortIndex: exitCol.seqId,
            }
          : { ...defaultCol, sortIndex: currentIndex };
      });
      setInternalValue(sortBy(columns, 'sortIndex'));
      onChangeRef.current(sortBy(columns, 'sortIndex'));
    } else {
      onChangeRef.current(_defaultValue.current);
    }
  }, [_defaultValue, onChangeRef, uniqKey, userId]);

  const _updateColumns = useCallback(
    async (columns: ColumnType<RecordType>[]) => {
      if (!userId) {
        return;
      }
      const { error } = await updateColumns({
        userId,
        type: uniqKey,
        columns: columns.map(
          (
            {
              dataIndex,
              key,
              fixed = null,
              show = true,
              disable = false,
              disabled = disable,
              title,
            },
            index
          ) => ({
            fieldName: genColumnKey({ dataIndex, key, title }),
            seqId: index,
            fixed,
            show,
            disable: disabled,
          })
        ) as Column[],
      });
      if (error) {
        message.error(error.message);
        return;
      }
    },
    [uniqKey, userId]
  );

  useEffect(() => {
    _loadColumns();
  }, [_loadColumns]);

  return (
    <BasicEditColumns
      {...resetProps}
      defaultValue={defaultValue}
      value={internalValue}
      onChange={value => {
        _updateColumns(value);
        setInternalValue(value);
        onChange(value);
      }}
    />
  );
}
