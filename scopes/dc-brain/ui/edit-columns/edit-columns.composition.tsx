import React, { useState } from 'react';

import type { ColumnType } from './';
import { EditColumns } from './edit-columns';

export const BasicEditColumns = () => {
  const [value, setValue] = useState<ColumnType[]>([]);
  return (
    <div style={{ margin: 100, textAlign: 'center' }}>
      <EditColumns
        uniqKey="EVENT_LIST_TEST"
        defaultValue={[
          { title: '姓名', dataIndex: 'name' },
          {
            title: '年龄',
            dataIndex: 'age',
          },
          {
            title: '性别',
            dataIndex: 'sex',
          },
        ]}
        onChange={value => setValue(value)}
      />
      <p>当前列状态(设置缓存)：</p>
      <div>
        {/* 需要手动过show 为false的数据滤掉 */}
        {value
          .filter(v => v.show !== false)
          .map(v => {
            return <span key={v.dataIndex as string}>{v.title} </span>;
          })}
      </div>
    </div>
  );
};
