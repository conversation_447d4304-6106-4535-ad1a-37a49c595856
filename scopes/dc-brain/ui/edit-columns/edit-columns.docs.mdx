---
description: ‘A EditColumns component.’
labels: ['label1', 'label2', 'label3']
---

### 用途

列设置可保存至服务端

```js
import { EditColumns } from './edit-columns';

<EditColumns
  {/* 前端定义 全局唯一key eg：HRM_PAGE_ATT_STATISTIC_BY_DAY */}
  uniqKey="HRM_PAGE_ATT_STATISTIC_BY_DAY"
  {/* 必传项（默认值、重置后的值） */}
  defaultValue={[
    { title: '姓名', dataIndex: 'name' },
    {
      title: '年龄',
      dataIndex: 'age',
    },
    {
      title: '性别',
      dataIndex: 'sex',
    },
  ]}
  onChange={value => {
    //....
  }}
/>;
```
