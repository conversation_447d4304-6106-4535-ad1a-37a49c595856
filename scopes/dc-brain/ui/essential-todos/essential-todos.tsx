import React from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Link } from 'react-router-dom';

import ClockCircleOutlined from '@ant-design/icons/es/icons/ClockCircleOutlined';
import dayjs from 'dayjs';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Typography } from '@manyun/base-ui.ui.typography';

import { generateExamRoutePath } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { BackendExamStatus } from '@manyun/knowledge-hub.service.dcexam.fetch-exams';
import {
  TodoType,
  getToDosAction,
  selectTodosTimes,
  selectTodosWithTime,
} from '@manyun/monitoring.state.dashboard-idc';
import type { Todo, TodoTypeText } from '@manyun/monitoring.state.dashboard-idc';
import { JobType } from '@manyun/ticket.model.task';
import {
  CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH,
  generateInspectionPlanDetailRoutePath,
  generateInventoryPlanDetailRoutePath,
  generateMaintainPlanDetailRoutePath,
  generateTicketUrl,
} from '@manyun/ticket.route.ticket-routes';

import { VisitRecordsTimeline } from './components/visit-records-timeline';

export type EssentialTodosProps = { idc: string };
const tagInfoMap = {
  [TodoType.EXAM]: {
    shouldChangeTime: true,
  },
  [TodoType.VISITORS]: { shouldChangeTime: true },
  [TodoType.TASK]: { shouldChangeTime: false },
};

const examStatusMap = {
  [BackendExamStatus.INIT]: { text: '待考试', type: 'success' },
  [BackendExamStatus.PROCESS]: { text: '进行中', type: 'processing' },
  [BackendExamStatus.CORRECT]: { text: '判卷中', type: 'processing' },
  [BackendExamStatus.GRADED]: { text: '已完成', type: 'processing' },
  [BackendExamStatus.CANCEL]: { text: '已取消', type: 'processing' },
};

const getTagInfo = (data: {
  time: number;
  title: string;
  id: string | number;
  type: Exclude<TodoType, TodoType.VistRecords>;
  typeText: TodoTypeText;
  examStatus?: BackendExamStatus;
  isStartTime?: boolean;
  isEndTime?: boolean;
  triggerTime?: string;
}) => {
  const { time, type, examStatus, isStartTime, isEndTime, triggerTime } = data;
  const timeTxt = dayjs(time).format('HH:mm');

  const tagInfo: {
    tagType: string;
    tagText: string | null;
    tagTime: string;
    triggerTime?: string;
  } = {
    tagType: '',
    tagText: '',
    tagTime: timeTxt,
  };
  if (isStartTime) {
    if (tagInfoMap[type].shouldChangeTime) {
      tagInfo.tagTime = `${tagInfo.tagTime}起`;
    }
  } else if (isEndTime) {
    if (tagInfoMap[type].shouldChangeTime) {
      tagInfo.tagTime = `至${tagInfo.tagTime}`;
    }
  } else {
    if (tagInfoMap[type].shouldChangeTime) {
      tagInfo.tagTime = `全天`;
    }
  }
  switch (type) {
    case TodoType.EXAM:
      if (examStatus) {
        tagInfo.tagText = examStatusMap[examStatus].text;
        tagInfo.tagType = examStatusMap[examStatus].type;
        return tagInfo;
      }
      return tagInfo;
    case TodoType.VISITORS:
      return tagInfo;
    case TodoType.TASK:
      tagInfo.tagText = null;
      tagInfo.tagType = 'processing';
      tagInfo.triggerTime = triggerTime;
      return tagInfo;
  }
};

export function EssentialTodos(props: EssentialTodosProps) {
  const { idc } = props;
  const dispatch = useDispatch();
  const [page, setPage] = React.useState(1);
  const [selectedDate, setSelectedDate] = React.useState<string>(dayjs().format('YYYY-MM-DD'));
  const [activeKey, setActiveKey] = React.useState<string>();

  const times = useSelector(selectTodosTimes());
  const todos = useSelector(selectTodosWithTime(selectedDate));
  const tabsStr = todos.map(({ type }) => type).join('###');

  const timesStr = times.join('$$');

  const [startedAt, setStartedAt] = React.useState(Date.now());
  React.useEffect(() => {
    const endTime = startedAt + 30 * 24 * 60 * 60 * 1000;
    dispatch(getToDosAction({ startTime: startedAt, endTime, idc: idc }));
  }, [dispatch, idc, startedAt]);

  React.useEffect(() => {
    if (!times[0] || times[0] === selectedDate) {
      return;
    }
    setSelectedDate(times[0]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [timesStr]);
  const firstTodo = todos[0];
  React.useEffect(() => {
    if (!firstTodo?.type) {
      return;
    }
    setActiveKey(firstTodo.type);
  }, [tabsStr, selectedDate, firstTodo?.type]);

  return (
    <Card
      title="待办事项"
      size="small"
      bordered
      bodyStyle={{
        height: 'calc((var(--content-height) - 1rem) / 2 - 40px)',
        overflowY: 'auto',
      }}
      extra={
        <Space>
          <Button
            type="link"
            compact
            onClick={() => {
              if (times.length <= 0) {
                return;
              }
              setPage(1);
              setSelectedDate(times[0]);
              setStartedAt(Date.now());
            }}
          >
            刷新
          </Button>
          <Link to={CREATE_MAINTAIN_TASK_CONFIGURATION_ROUTE_PATH}>
            <Button type="link" compact>
              新增
            </Button>
          </Link>
        </Space>
      }
      {...props}
    >
      {times.length > 0 ? (
        <Space direction="vertical" size={16} style={{ width: '100%', height: '100%' }}>
          <Timeline
            type="horizontal"
            mode="bottom"
            pageSize={5}
            page={page}
            selected={selectedDate}
            onChange={changed => {
              if (changed.page !== undefined) {
                setPage(changed.page);
              }
              if (changed.selected !== undefined) {
                setSelectedDate(changed.selected.toString());
              }
            }}
          >
            {times.map(time => {
              return (
                <Timeline.Item
                  key={time}
                  style={{
                    color:
                      time === selectedDate
                        ? `var(--${prefixCls}-primary-color)`
                        : `var(--${prefixCls}-text-color)`,
                    cursor: 'pointer',
                  }}
                  id={time}
                >
                  {dayjs(time).format('MM.DD')}
                </Timeline.Item>
              );
            })}
          </Timeline>
          <div style={{ whiteSpace: 'nowrap', overflowX: 'auto' }}>
            <Radio.Group
              size="small"
              value={activeKey}
              onChange={e => {
                setActiveKey(e.target.value);
              }}
            >
              {todos.map(({ type, typeText, data }) => {
                if (
                  type === TodoType.VistRecords &&
                  (data.length <= 0 || dayjs(selectedDate).isAfter(Date.now(), 'day'))
                ) {
                  return null;
                }

                return (
                  <Radio.Button
                    key={type}
                    value={type}
                  >{`${typeText}(${data.length})`}</Radio.Button>
                );
              })}
            </Radio.Group>
          </div>
          <Card
            bordered={false}
            size="small"
            bodyStyle={{
              height: '100%',
              overflowY: 'auto',
            }}
          >
            <>
              {todos
                .filter(({ type }) => activeKey === type)
                .map(todo => {
                  const { type, data = [] } = todo;
                  if (type === 'task') {
                    const tasks: (Omit<Todo, 'type'> & {
                      type: TodoType.TASK;
                      jobType: JobType;
                    })[] = data;
                    const sortTasks = tasks.sort(
                      (front, behind) =>
                        changeTriggerTimeIntoInt(front.triggerTime as string) -
                        changeTriggerTimeIntoInt(behind.triggerTime as string)
                    );
                    return (
                      <Timeline key={type} mode="left">
                        {sortTasks.map(task => {
                          const { title, id, triggerTime, jobType } = task;
                          const { tagType, tagText, tagTime } = getTagInfo(task);

                          return (
                            <TimeLineItem
                              key={`${id}${triggerTime}`}
                              id={id}
                              tagTime={tagTime}
                              tagText={tagText}
                              tagType={tagType}
                              path={generateTaskPath(jobType, String(id), title)}
                              title={title}
                              triggerTime={triggerTime}
                            />
                          );
                        })}
                      </Timeline>
                    );
                  }

                  if (type === TodoType.VistRecords) {
                    return <VisitRecordsTimeline key="type" data={data} />;
                  }

                  return (
                    <Timeline key={type} mode="left">
                      {data.map(todoData => {
                        const { title, id, triggerTime, ...rest } = todoData;
                        const { tagType, tagText, tagTime } = getTagInfo({ title, id, ...rest });
                        let path: { search: string; pathname: string } | string = '';
                        if (type === 'visitors') {
                          path = generateTicketUrl({
                            ticketType: 'visitor',
                            id: String(id),
                          });
                        }

                        if (type === 'exam') {
                          path = generateExamRoutePath(Number(id));
                        }

                        return (
                          <TimeLineItem
                            key={id}
                            id={id}
                            tagTime={tagTime}
                            tagText={tagText}
                            tagType={tagType}
                            path={path}
                            title={title}
                          />
                        );
                      })}
                    </Timeline>
                  );
                })}
            </>
          </Card>
        </Space>
      ) : (
        <Empty />
      )}
    </Card>
  );
}

const TimeLineItem = ({
  id,
  tagTime,
  tagText,
  tagType,
  path,
  title,
  triggerTime,
}: {
  id: string | number;
  tagTime: string;
  tagText: string | null;
  tagType: string;
  path: { pathname: string; search: string } | string;
  title: string;
  triggerTime?: string;
}) => {
  return (
    <Timeline.Item
      key={`${tagTime}.${id}`}
      id={`${tagTime}.${id}`}
      dot={<ClockCircleOutlined />}
      color="gray"
    >
      <Space style={{ display: 'flex' }} direction="vertical">
        <Space>
          <Typography.Text type="secondary">{triggerTime ?? tagTime}</Typography.Text>
          {tagText && <Tag color={tagType}>{tagText}</Tag>}
        </Space>
        <Link to={path}>{title}</Link>
      </Space>
    </Timeline.Item>
  );
};
const changeTriggerTimeIntoInt = (triggerTime: string) => {
  return parseInt(triggerTime.split(':').join(''));
};

function generateTaskPath(jobType: JobType, id: string, name: string) {
  switch (jobType) {
    case JobType.InventoryCount:
      return generateInventoryPlanDetailRoutePath({ id, name });
    case JobType.DeviceInspection:
      return generateInspectionPlanDetailRoutePath({ id, name });
    case JobType.ToolsMaintenance:
    case JobType.DeviceMaintenance:
      return generateMaintainPlanDetailRoutePath({ id, name });
    default:
      return '';
  }
}
