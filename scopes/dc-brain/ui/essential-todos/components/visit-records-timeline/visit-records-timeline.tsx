import React from 'react';
import { Link } from 'react-router-dom';

import ClockCircleOutlined from '@ant-design/icons/es/icons/ClockCircleOutlined';
import dayjs from 'dayjs';

import { Timeline } from '@manyun/base-ui.ui.timeline';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { VisitRecordJSON } from '@manyun/sentry.model.visit-record';
import { generateVisitsRecordsRoutePath } from '@manyun/sentry.route.routes';

export type VisitRecordsTimelineProps = {
  data: VisitRecordJSON[];
};

export function VisitRecordsTimeline({ data }: VisitRecordsTimelineProps) {
  return (
    <Timeline mode="left">
      {data.map(({ id, enteredAt, user, typeText, idc, status, authorizedArea }) => {
        const _enteredAt = dayjs(enteredAt).millisecond(0);

        return (
          <Timeline.Item key={id} id={id} dot={<ClockCircleOutlined />} color="gray">
            <Typography.Paragraph type="secondary">
              {_enteredAt.format('HH:mm:ss')}
            </Typography.Paragraph>
            <Typography.Paragraph>
              <Link
                target="_blank"
                to={generateVisitsRecordsRoutePath({
                  name: user.name,
                  // 这里的证件号码是脱敏的，无法用于查询
                  // ICN,
                  idc,
                  status,
                  enteredAtRange: [
                    _enteredAt.subtract(1, 'second').valueOf(),
                    _enteredAt.add(1, 'second').valueOf(),
                  ],
                })}
              >
                {user.name}（{typeText}）登记{idc}.{authorizedArea.join(`,${idc}.`)}
                入室超过12小时未办理离园
              </Link>
            </Typography.Paragraph>
          </Timeline.Item>
        );
      })}
    </Timeline>
  );
}
