import React from 'react';
import { Route, MemoryRouter as Router, useHistory, useParams } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { EXAM_ROUTE_PATH } from '@manyun/knowledge-hub.route.knowledge-hub-routes';
import { destroyMock, webRequest } from '@manyun/service.request';
import {
  TASK_CREATE_ROUTE_PATH,
  TASK_ROUTE_PATH,
  TICKET_ROUTE_PATH,
} from '@manyun/ticket.route.ticket-routes';

import { EssentialTodos } from './essential-todos';

export const BasicEssentialTodos = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    setReady(true);
  }, []);

  if (!ready) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={['/link']}>
          <Route path={'/link'}>
            <EssentialTodos idc="EC01" />
            <br />
          </Route>
          <Route path={TASK_CREATE_ROUTE_PATH}>
            <Show />
          </Route>
          <Route path={TASK_ROUTE_PATH}>
            <Show />
          </Route>
          <Route path={TICKET_ROUTE_PATH}>
            <Show />
          </Route>
          <Route path={EXAM_ROUTE_PATH}>
            <Show />
          </Route>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

function Show() {
  const data = useParams();
  const history = useHistory();
  return (
    <>
      <p>path: {history.location.pathname}</p>
      <p>params: {JSON.stringify(data)}</p>
    </>
  );
}
