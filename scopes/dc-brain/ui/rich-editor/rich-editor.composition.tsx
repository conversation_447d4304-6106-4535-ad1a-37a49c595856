import React, { useState } from 'react';

import { IDomEditor as DomEditor } from '@wangeditor/editor';

import { webRequest } from '@manyun/service.request';

import { RichEditor } from './rich-editor';

export const BasicRichEditor = () => {
  const [initialized, update] = React.useState(false);

  const [, setEditor] = useState<DomEditor | null>(null);
  const [value, setValue] = useState('<p>111</p>');

  React.useEffect(() => {
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <>
      <RichEditor
        style={{ margin: '10px' }}
        initialize={editor => {
          setEditor(editor);
        }}
        value={value}
        onChange={setValue}
      />
      <div>富文本框html: {value}</div>
    </>
  );
};

export const BasicOnlyEditor = () => {
  return (
    <>
      <p>不展示ToolBar, 且只读</p>
      <RichEditor
        showToolBar={false}
        style={{ margin: '10px' }}
        value={
          '<p><span style="color: rgb(216, 68, 147);"><em><strong>11111</strong></em></span></p>'
        }
        editorConfig={{ readOnly: true }}
      />
    </>
  );
};
