import React, { useCallback, useEffect, useMemo, useState } from 'react';

import {
  <PERSON>omE<PERSON>or as DomEditor,
  IEditorConfig as EditorConfig,
  IToolbarConfig as ToolbarConfig,
} from '@wangeditor/editor';
import { Editor, Toolbar } from '@wangeditor/editor-for-react';
import '@wangeditor/editor/dist/css/style.css';

import { message } from '@manyun/base-ui.ui.message';

import { uploadFullFile } from '@manyun/dc-brain.service.upload-full-file';

import { DEFAULT_TOOLBAR_CONFIG } from './rich-editor.util';

export type RichEditotMode = 'default' | 'simple';

export type RichEditorProps = {
  initialize?: (editor: DomEditor | null) => void;
  value?: string;
  onChange?: (value: string) => void;
  toolbarMode?: RichEditotMode;
  toolbarConfig?: Partial<ToolbarConfig> /**工具栏配置 */;
  editorMode?: RichEditotMode;
  editorConfig?: Partial<EditorConfig> /**编辑器配置 */;
  editorStyle?: React.CSSProperties;
  showToolBar?: boolean;
  toolbarStyle?: React.CSSProperties;
  uploadImgMaxSize?: number;
  uploadVideoMaxSize?: number;
  maxNumberOfFiles?: number;
  style?: React.CSSProperties;
  className?: string;
};

type InsertFnType = (url: string, alt: string, href: string) => void;

export const RichEditor = ({
  initialize,
  value,
  onChange,
  showToolBar = true,
  toolbarMode = 'default',
  toolbarConfig,
  editorMode = 'default',
  editorConfig,
  style,
  className,
  toolbarStyle,
  editorStyle,
  uploadImgMaxSize = 5 /**图片上传最大限制 默认5M */,
  uploadVideoMaxSize = 20 /**视频上传最大限制 默认20M */,
  maxNumberOfFiles = 10 /**文件最多上传数量 默认10 */,
}: RichEditorProps) => {
  const [editor, setEditor] = useState<DomEditor | null>(null); // 存储 editor 实例

  useEffect(() => {
    return () => {
      if (editor == null) {
        return;
      }
      editor.destroy();
      setEditor(null);
    };
  }, [editor]);

  const _handleUploadFile = useCallback(
    async (file: File, insertFn: InsertFnType, maxFileSize: number) => {
      if (!checkFileSizeLimit(maxFileSize, file)) {
        return;
      }
      const { error, data } = await uploadFullFile(file);
      if (error) {
        message.error(error.message);
        return;
      }
      const uploadedFileSrc = data?.[0].src;
      const location = window.location;
      const downloadImgUrl =
        process.env.NODE_ENV === 'development'
          ? uploadedFileSrc
          : `${location.origin}${uploadedFileSrc} `;
      if (downloadImgUrl) {
        insertFn(downloadImgUrl, file.name, '');
      }
    },
    []
  );

  const _editorConfig = useMemo(() => {
    return {
      ...editorConfig,
      customAlert: (s: string, t: 'success' | 'info' | 'warning' | 'error') => {
        switch (t) {
          case 'success':
            message.success(s);
            break;
          case 'info':
            message.info(s);
            break;
          case 'warning':
            message.warning(s);
            break;
          case 'error':
            message.error(s);
            break;
          default:
            message.info(s);
            break;
        }
      },
      MENU_CONF: {
        ...editorConfig?.MENU_CONF,
        uploadImage: {
          maxFileSize: uploadImgMaxSize,
          maxNumberOfFiles: maxNumberOfFiles,
          customUpload: (file: File, insertFn: InsertFnType) => {
            _handleUploadFile(file, insertFn, uploadImgMaxSize);
          },
        },
        uploadVideo: {
          maxFileSize: uploadVideoMaxSize,
          maxNumberOfFiles: maxNumberOfFiles,
          customUpload: (file: File, insertFn: InsertFnType) => {
            _handleUploadFile(file, insertFn, uploadVideoMaxSize);
          },
        },
      },
    };
  }, [_handleUploadFile, editorConfig, maxNumberOfFiles, uploadImgMaxSize, uploadVideoMaxSize]);

  return (
    <div
      data-testid="editor-container"
      style={{ border: '1px solid var(--border-color-base)', zIndex: 999, ...style }}
      className={className}
    >
      {showToolBar && (
        <Toolbar
          editor={editor}
          defaultConfig={toolbarConfig ?? DEFAULT_TOOLBAR_CONFIG}
          mode={toolbarMode}
          style={{ borderBottom: '1px solid var(--border-color-base)', ...toolbarStyle }}
        />
      )}
      <Editor
        defaultConfig={_editorConfig}
        mode={editorMode}
        value={value}
        onCreated={editor => {
          if (initialize) {
            initialize(editor);
          }
          setEditor(editor);
        }}
        onChange={editor => {
          const htmlValue = editor.getHtml();
          if (onChange) {
            onChange(htmlValue);
          }
        }}
        style={{ height: '320px', ...editorStyle }}
      />
    </div>
  );
};

function checkFileSizeLimit(maxFileSizeInMB = Number.POSITIVE_INFINITY, file: File) {
  const fileSizeMB = file.size / 1024 / 1024;
  if (fileSizeMB > maxFileSizeInMB) {
    const errMessage = `上传文件不得超过 ${maxFileSizeInMB}MB`;
    message.error(errMessage);
    return false;
  }
  return true;
}
