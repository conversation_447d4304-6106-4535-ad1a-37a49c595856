---
description: '富文本编辑器'
labels: ['react', 'wangEditor', 'richer-editor']
---

## 概述

在 [wangEditor v5](https://www.wangeditor.com/) 的基础上封装富文本编辑器;

- `RichEditor`：内部实现了图片上传、视频上传的能力，不应再配置编辑器的 MENU_CONF['uploadImage']、MENU_CONF['uploadVideo']
- `RichEditor`：在 wangEditor 的基础上，封装通用 API

| 参数               | 说明                   | 类型                                 | 默认值    | 版本  |
| ------------------ | ---------------------- | ------------------------------------ | --------- | ----- |
| initialize         | 编辑器初始化的回调函数 | `(editor: DomEditor 或 null )=>void` | undefined | 0.0.1 |
| value              | 当前编辑器输入值       | `string`                             | undefined | 0.0.1 |
| onChange           | 输入值的回掉函数       | `(value: string )=>void`             | undefined | 0.0.1 |
| editorMode         | 编辑器的模式           | ` default 或 simple`                 | default   | 0.0.1 |
| editorConfig       | 编辑器配置             | `Partial<EditorConfig>`              | undefined | 0.0.1 |
| editorStyle        | 编辑器样式             | `React.CSSProperties`                | undefined | 0.0.1 |
| showToolBar        | 是否显示工具栏         | `boolean`                            | true      | 0.0.1 |
| toolbarStyle       | 工具栏样式             | `React.CSSProperties`                | undefined | 0.0.1 |
| toolbarMode        | 工具栏模式             | ` default 或 simple`                 | default   | 0.0.1 |
| toolbarConfig      | 工具栏配置             | `Partial<ToolbarConfig> `            | undefined | 0.0.1 |
| uploadImgMaxSize   | 图片上传最大限制       | `number`                             | 5M        | 0.0.1 |
| uploadVideoMaxSize | 视频上传最大限制       | `number`                             | 100M      | 0.0.1 |
| maxNumberOfFiles   | 文件上传最大数量       | `number`                             | 10        | 0.0.1 |
| style              | 最外层样式             | `React.CSSPropertie`                 | undefined | 0.0.1 |
| className          | 最外层类名             | `string`                             | undefined | 0.0.1 |
