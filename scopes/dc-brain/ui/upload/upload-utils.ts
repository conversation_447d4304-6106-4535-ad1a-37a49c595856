/* eslint-disable @typescript-eslint/no-explicit-any */
import type { UploadChangeParam } from 'antd/es/upload/index.js';
import type { UploadFile as AntUploadFile } from 'antd/es/upload/interface.js';
import attrAccept from 'rc-upload/es/attr-accept.js';

import { message } from '@manyun/base-ui.ui.message';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

export { attrAccept };

export type MixedUploadFile = AntUploadFile & McUploadFile;
/**
 * Transform `File` into `AntUploadFile`
 *
 * @param info AntUpload.onChange info
 */
export function transformFiles(info: UploadChangeParam<AntUploadFile<any>>) {
  if (info.fileList.length > 0) {
    if ((info.fileList[info.fileList.length - 1].originFileObj as any)?.error !== undefined) {
      delete (info.fileList[info.fileList.length - 1].originFileObj as any)?.error;
    }
    if ((info.fileList[info.fileList.length - 1].originFileObj as any)?.status !== undefined) {
      delete (info.fileList[info.fileList.length - 1].originFileObj as any)?.status;
    }
  }
}

export function checkFileType(accept: string | undefined, file: File) {
  if (accept && !attrAccept(file as any, accept)) {
    const errMessage = '文件类型错误！';
    message.error(errMessage);
    (file as unknown as AntUploadFile).status = 'error';
    (file as unknown as AntUploadFile).error = new Error(errMessage);
    return false;
  }

  return true;
}

export function checkFileSizeLimit(maxFileSizeInMB: number | undefined, file: File) {
  const _maxFileSizeInMB = maxFileSizeInMB ?? Number.POSITIVE_INFINITY;
  const fileSizeMB = file.size / 1024 / 1024;
  if (fileSizeMB > _maxFileSizeInMB) {
    const errMessage = `上传文件不得超过 ${maxFileSizeInMB}MB`;
    message.error(errMessage);
    (file as unknown as AntUploadFile).status = 'error';
    (file as unknown as AntUploadFile).error = new Error(errMessage);
    return false;
  }

  return true;
}

export function mutateFile(file: any, mcUploadFile: McUploadFile): MixedUploadFile {
  // McUploadFile attrs
  file.ext = mcUploadFile.ext;
  file.patialPath = mcUploadFile.patialPath;
  file.src = mcUploadFile.src;
  file.uploadedAt = mcUploadFile.uploadedAt;
  file.uploadUser = mcUploadFile.uploadUser;

  // AntUploadFile attrs
  file.url = mcUploadFile.src;

  return file;
}

/** 服务于 Upload 的串行请求池，可中断重启 */
export class Pool<T> {
  aborted = false;

  queue: T[] = [];

  loadedIndex: number = -1;

  controller: AbortController | null = null;

  constructor(
    private _runTask: (task: T, controller: AbortController) => Promise<void>,
    private _initLoadedIndex: () => Promise<number>
  ) {}

  enqueue(task: T) {
    this.queue.push(task);
  }

  async run() {
    const loadedIndex = await this._initLoadedIndex();
    this.loadedIndex = loadedIndex;
    const queue = loadedIndex === -1 ? this.queue : this.queue.slice(loadedIndex + 1);
    queue.reduce(
      (acc, task, index) =>
        acc.then(async () => {
          const controller = new AbortController();
          this.controller = controller;
          await this._runTask(task, controller);
          this.loadedIndex = index;
        }),
      Promise.resolve()
    );
  }

  abort() {
    this.controller?.abort();
  }
}

/** 自 Upload fileList 获取已上传完成的 fileList` */
export function getCompletedFileList(fileList: AntUploadFile[]) {
  return fileList.filter(file => file.status === 'done');
}
