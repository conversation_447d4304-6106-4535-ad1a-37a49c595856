/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  DeleteOutlined,
  LoadingOutlined,
  PaperClipOutlined,
  PauseOutlined,
  ReloadOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import AntUpload from 'antd/es/upload/index.js';
import type {
  UploadProps as AntUploadProps,
  RcFile,
  UploadChangeParam,
} from 'antd/es/upload/index.js';
import type { UploadFile } from 'antd/es/upload/interface.js';
import classNames from 'classnames';
import sha256 from 'crypto-js/sha256.js';
import type { UploadProgressEvent } from 'rc-upload/es/interface.js';
import React from 'react';

import { FilePreview } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Progress } from '@manyun/base-ui.ui.progress';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { checkUploadFile } from '@manyun/dc-brain.service.check-upload-file';
import { uploadFileWeb as uploadFile } from '@manyun/dc-brain.service.upload-file';

import {
  Pool,
  checkFileSizeLimit,
  checkFileType,
  mutateFile,
  transformFiles,
} from './upload-utils.js';
import type { MixedUploadFile } from './upload-utils.js';
import styles from './upload.module.less';
import { useRequest } from './use-request.js';

export type { MixedUploadFile };

type CustomShowUploadListInterface = { showAction: boolean };

export type McUploadProps = (
  | {
      listType?: 'text';
      showUploadList?: boolean | CustomShowUploadListInterface;
    }
  | {
      listType: 'picture' | 'picture-card';
      showUploadList: AntUploadProps['showUploadList'];
    }
) & {
  targetId?: string /**业务id */;
  targetType?: string /**业务类型 */;
  showAccept?: boolean /**是否显示 accept, 默认不开启 */;
  allowDelete?: boolean /** 是否允许删除 */;
  maxFileSize?: number /** 单个文件大小限制（单位：MB） */;
  children?: React.ReactNode;
  fileList?: MixedUploadFile[];
  onChange?(info: {
    file: MixedUploadFile;
    fileList: MixedUploadFile[];
    event?: UploadProgressEvent;
  }): void;
  // todo：等之后过滤 fileList 行为均改为使用方处理后，可删除该 api
  /** 是否支持失败重试，默认为 false */
  retryOnError?: boolean;
} & Omit<AntUploadProps, 'fileList' | 'onChange' | 'showUploadList' | 'listType'>;

/** 分片上传时每片的大小，单位为 byte */
const chunkSize = 4 * 1024 * 1024;

export const McUpload = React.forwardRef<any, McUploadProps>(
  (
    {
      targetId,
      targetType,
      className,
      accept,
      showAccept = false,
      allowDelete = true,
      maxFileSize = 100,
      children,
      onChange,
      showUploadList = true,
      listType,
      retryOnError,
      ...restProps
    },
    ref?
  ) => {
    const [innerFileList, setInnerFileList] = React.useState<MixedUploadFile[]>([]);
    const { fetchBizFiles, deleteBizFile, saveBizFiles } = useRequest({
      targetId: targetId,
      targetType: targetType,
    });

    React.useEffect(() => {
      if (targetId && targetType) {
        fetchBizFiles(files => {
          if (files.length > 0) {
            onChange?.({ fileList: files, file: files[0] });
            if (!('fileList' in restProps)) {
              setInnerFileList(files);
            }
          }
        });
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [targetId, targetType]);

    React.useEffect(() => {
      if ('fileList' in restProps) {
        setInnerFileList(
          (restProps?.fileList ?? []).map(
            d => ({ ...d, src: d.src, thumbUrl: d.url }) as MixedUploadFile
          )
        );
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [restProps?.fileList]);

    const [previewFile, setPreviewFile] = React.useState<
      Partial<MixedUploadFile> & { visible: boolean }
    >({ visible: false });

    const beforeUploadHandler = React.useCallback(
      (file: RcFile) => {
        const accepted = checkFileType(accept, file) && checkFileSizeLimit(maxFileSize, file);
        return accepted;
      },
      [accept, maxFileSize]
    );

    const saveBizHandler = React.useCallback(
      (files: McUploadFile[]) => {
        if (targetId && targetType) {
          saveBizFiles(files);
        }
      },
      [saveBizFiles, targetId, targetType]
    );

    const deleteBizHandler = React.useCallback(
      (file: McUploadFile) => {
        if (targetId && targetType) {
          deleteBizFile({ targetId, targetType, filePath: file.patialPath });
        }
      },
      [deleteBizFile, targetId, targetType]
    );

    const changeHandler = React.useCallback(
      (info: UploadChangeParam) => {
        if (!retryOnError) {
          info.fileList = info.fileList.filter(file => file && file.status !== 'error');
          transformFiles(info);
        }
        if (!('fileList' in restProps)) {
          setInnerFileList(info.fileList as MixedUploadFile[]);
        }
        if (info.file.status === 'done' && info.file.response) {
          saveBizHandler(info.file.response);
        }
        if (info.file.status === 'removed') {
          deleteBizHandler(info.file as McUploadFile);
        }
        onChange?.(info as any);
      },
      [deleteBizHandler, onChange, restProps, retryOnError, saveBizHandler]
    );

    const previewHandler = React.useCallback((file: any) => {
      setPreviewFile({
        ...file,
        visible: true,
      });
    }, []);

    const dropHandler = React.useCallback(
      (e: React.DragEvent<HTMLDivElement>) => {
        const files = e.dataTransfer.files;
        for (let index = 0; index < files.length; index++) {
          const file = files[index];
          if (!checkFileType(accept, file)) {
            return;
          }
        }
      },
      [accept]
    );

    /** key 为暂停上传的文件对应的 uid */
    const pausedFileUidMapRef = React.useRef<Map<string, true>>(new Map());
    /** key 为上传文件对应的 uid，value 为该文件对应的 pool */
    const fileUidPoolMapRef = React.useRef<Map<string, Pool<number>>>(new Map());
    const customRequestHandler = React.useCallback<Required<AntUploadProps>['customRequest']>(
      async ({ file, onProgress, onError, onSuccess }) => {
        const typedFile = file as RcFile;
        const count = Math.ceil(typedFile.size / chunkSize);
        const pool = new Pool<number>(
          async (index: number, controller: AbortController) => {
            const fd = new FormData();
            fd.append('file', typedFile.slice(index * chunkSize, (index + 1) * chunkSize));
            fd.append('fileMd5', sha256(`${typedFile.uid}`).toString());
            fd.append('fileName', typedFile.name);
            fd.append('fileSize', `${typedFile.size}`);
            fd.append('totalChunks', `${count}`);
            fd.append('currChunk', `${index}`);
            fd.append(
              'chunkSize',
              `${index === count - 1 ? typedFile.size - index * chunkSize : chunkSize}`
            );
            const { error, data } = await uploadFile(
              fd,
              (percent, progressEvent) => {
                onProgress?.({
                  ...progressEvent,
                  percent: Number(Number((index * 100 + percent) / count).toFixed(2)),
                });
              },
              controller.signal
            );
            if (error) {
              if (error.message === 'canceled') {
                throw new Error(error.message);
              }
              message.error(error.message);
              onError!(new Error(error.message));
              throw new Error(error.message);
            } else if (index === count - 1) {
              const uploadedFile = data![0];
              mutateFile(typedFile, uploadedFile);
              fileUidPoolMapRef.current.delete(typedFile.uid);
              onSuccess!(data);
            }
          },
          async () => {
            const { data, error } = await checkUploadFile(sha256(`${typedFile.uid}`).toString());
            if (error) {
              message.error(error.message);
              onError!(new Error(error.message));
              throw new Error(error.message);
            }
            // data 为准备上传的文件 chunkIndex，这里 -1 以返回 loadedIndex
            return data - 1;
          }
        );
        fileUidPoolMapRef.current.set(typedFile.uid, pool);
        for (let i = 0; i < count; i++) {
          pool.enqueue(i);
        }
        pool.run();
      },
      []
    );

    const [, forceUpdate] = React.useReducer(x => x + 1, 0);
    const getActionIcons = React.useCallback(
      (file: UploadFile, remove: () => void) => {
        const mutableIcon = (() => {
          const key = file.uid;
          if (!file?.status || file.status === 'done') {
            return null;
          }
          if (file.status === 'error') {
            return <ReloadOutlined onClick={() => fileUidPoolMapRef.current.get(key)?.run()} />;
          }
          if (pausedFileUidMapRef.current.has(key)) {
            return (
              <UploadOutlined
                onClick={() => {
                  pausedFileUidMapRef.current.delete(key);
                  fileUidPoolMapRef.current.get(key)?.run();
                  forceUpdate();
                }}
              />
            );
          }
          return (
            <PauseOutlined
              onClick={() => {
                pausedFileUidMapRef.current.set(key, true);
                fileUidPoolMapRef.current.get(key)?.abort();
                forceUpdate();
              }}
            />
          );
        })();
        return (
          <Space>
            {mutableIcon}
            {allowDelete && <DeleteOutlined onClick={() => remove()} />}
          </Space>
        );
      },
      [allowDelete]
    );
    // 仿照 ant-design 重新实现 item 以支持断点续传
    const itemRender: AntUploadProps['itemRender'] = React.useMemo(() => {
      // todo：picture、picture-card 先使用默认的 item
      if (!listTypeIsText(listType)) {
        return undefined;
      }
      // eslint-disable-next-line react/display-name
      return (_node, file, _fileList, { preview, remove }) => {
        if (!showUploadList) {
          return null;
        }
        const showAction =
          showUploadList === true
            ? true
            : (showUploadList as CustomShowUploadListInterface).showAction;
        const item = (
          <div className={styles.item}>
            <div
              className={classNames(
                styles.itemInfo,
                file?.status === 'error' && styles.error,
                !children && styles.onlyRead
              )}
            >
              {file?.status === 'uploading' ? <LoadingOutlined /> : <PaperClipOutlined />}
              <Tooltip title={file.name}>
                {!file?.status || file.status === 'done' ? (
                  <Typography.Link ellipsis className={styles.text} onClick={() => preview()}>
                    {file.name}
                  </Typography.Link>
                ) : (
                  <Typography.Text ellipsis className={styles.text}>
                    {file.name}
                  </Typography.Text>
                )}
              </Tooltip>
              {showAction && getActionIcons(file, remove)}
            </div>
            {file.status === 'uploading' && (
              <Progress className={styles.progress} size="small" percent={file.percent} />
            )}
          </div>
        );
        let message;
        if (file.response && typeof file.response === 'string') {
          message = file.response;
        } else {
          message = file.error?.statusText || file.error?.message || '上传失败';
        }
        return file.status === 'error' ? (
          <Tooltip title={message} getPopupContainer={node => node.parentNode as HTMLElement}>
            {item}
          </Tooltip>
        ) : (
          item
        );
      };
    }, [children, getActionIcons, listType, showUploadList]);

    return (
      <>
        <AntUpload
          ref={ref}
          fileList={innerFileList}
          className={classNames(className, !allowDelete && styles.uploadListActionsDisabled)}
          accept={accept}
          beforeUpload={beforeUploadHandler}
          customRequest={customRequestHandler}
          itemRender={itemRender}
          showUploadList={
            !listTypeIsText(listType)
              ? (showUploadList as AntUploadProps['showUploadList'])
              : undefined
          }
          listType={listType}
          onChange={changeHandler}
          onPreview={previewHandler}
          onDrop={dropHandler}
          {...restProps}
        >
          {children && (
            <Space direction="vertical" size={8}>
              {children}
              {showAccept && accept && (
                <Typography.Text type="secondary">上传格式支持：{accept}</Typography.Text>
              )}
            </Space>
          )}
        </AntUpload>
        <FilePreview
          file={previewFile as any}
          visible={previewFile.visible}
          onClose={() => {
            setPreviewFile({ visible: false });
          }}
        />
      </>
    );
  }
);

function listTypeIsText(listType: McUploadProps['listType']) {
  if (listType && listType !== 'text') {
    return false;
  }
  return true;
}

McUpload.displayName = 'McUpload';
