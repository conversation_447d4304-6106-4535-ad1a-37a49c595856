import { useCallback } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { deleteBizFile } from '@manyun/dc-brain.service.delete-biz-file';
import type { ApiQ as DeleteFileParams } from '@manyun/dc-brain.service.delete-biz-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { saveBizFiles } from '@manyun/dc-brain.service.save-biz-files';

import type { MixedUploadFile } from './upload-utils.js';

export type RequestProps = {
  targetId?: string;
  targetType?: string;
};

export const useRequest = ({ targetId, targetType }: RequestProps) => {
  /**请求获取已保存的业务附件 */
  const _fetchBizFiles = useCallback(
    async (callback?: (fileList: MixedUploadFile[]) => void) => {
      if (!targetId || !targetType) {
        return;
      }
      const { error, data } = await fetchBizFileInfos({
        targetId: targetId,
        targetType: targetType,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (callback) {
        callback(
          data.data.map(d => ({
            ...d,
            status: 'done',
            src: d.src,
            url: d.src,
          })) as MixedUploadFile[]
        );
      }
    },
    [targetId, targetType]
  );

  /**将附件和业务关联 */
  const _saveBizFiles = useCallback(
    async (fileList: McUploadFile[]) => {
      if (!targetId || !targetType) {
        return;
      }
      const { error } = await saveBizFiles({
        fileInfos: fileList.map(file => ({
          ...file,
          src: file.src,
          type: targetType,
          targetId: targetId,
          targetType: targetType,
        })),
      });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('操作成功!');
    },
    [targetId, targetType]
  );

  /** 删除附件和业务的关联关系 */
  const _deleteBizFile = useCallback(
    async (svd: DeleteFileParams) => {
      if (!targetId || !targetType) {
        return;
      }
      const { error } = await deleteBizFile({
        ...svd,
        targetId: targetId,
        targetType: targetType,
      });
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('删除成功!');
    },
    [targetId, targetType]
  );

  return {
    fetchBizFiles: _fetchBizFiles,
    deleteBizFile: _deleteBizFile,
    saveBizFiles: _saveBizFiles,
  } as const;
};
