@import (reference) '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.uploadListActionsDisabled {
  :global(.@{prefixCls}-upload-list-item-card-actions) {
    display: none;
  }
}

.item {
  .itemInfo {
    display: flex;
    align-items: center;
    margin-top: @margin-xs;
    color: @text-color-secondary;

    &.onlyRead {
      margin-top: 0;
      margin-bottom: @margin-xs;
    }

    &:hover {
      background-color: @item-hover-bg;
    }

    &.error {
      color: @error-color;

      .text {
        color: @error-color;
      }
    }

    .text {
      flex: 1;
      max-width: max-content;
      margin: 0 @margin-xs;
    }
  }

  .progress {
    :global {
      .@{prefixCls}-progress-outer {
        margin-right: calc(-3em - @margin-xs);
        padding-right: calc(3em + @padding-xs);
      }

      .@{prefixCls}-progress-text {
        width: 3em;
      }
    }
  }
}
