import React from 'react';
import { Circle, Rect, RegularPolygon } from 'react-konva';

import { a, useSpring } from '@react-spring/konva';
import { observer } from 'mobx-react-lite';

import { registerShapeComponent } from '@manyun/dc-brain.aura-graphix';

// @ts-ignore TS2339
const AlertingMarkerElement = ({ element }: unknown) => {
  const [styles, api] = useSpring(() => ({
    loop: { reverse: true },
    config: {
      duration: 250,
    },
    from: {
      opacity: 0,
    },
    to: {
      opacity: 1,
    },
  }));

  const animating = element.animating;
  React.useEffect(() => {
    if (animating) {
      api.resume();
    } else {
      api.pause();
    }
  }, [animating, api]);

  return (
    // @ts-ignore TS2589
    <a.Group
      name="element"
      element={element}
      x={element.x}
      y={element.y}
      visible={element.animating}
      opacity={styles.opacity}
    >
      <RegularPolygon
        visible
        x={0}
        y={0}
        sides={3}
        radius={12}
        strokeWidth={2}
        stroke="#d89614"
        fill="#d89614"
        lineJoin="round"
      />
      <Rect x={-2} y={-8} width={4} height={8} fill="white" />
      <Circle x={0} y={4} radius={2} fill="white" />
    </a.Group>
  );
};

registerShapeComponent('alerting-marker', observer(AlertingMarkerElement));
