import React from 'react';

import { a, useSpring } from '@react-spring/konva';
import type Konva from 'konva';
import { observer } from 'mobx-react-lite';

import { registerShapeComponent } from '@manyun/dc-brain.aura-graphix';

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const Highlight = ({ element }: any) => {
  const isRect = (element.shape ?? 'rect') === 'rect';
  const Shape: typeof a.Rect | typeof a.Circle = isRect ? a.Rect : a.Circle;
  const props: Konva.RectConfig | Konva.CircleConfig = {
    x: element.x,
    y: element.y,
    fill: element.fill,
    stroke: element.stroke,
    strokeWidth: element.strokeWidth,
    visible: element.animating,
  };
  if (isRect) {
    (props as Konva.RectConfig).width = element.width;
    (props as Konva.RectConfig).height = element.height;
  } else {
    (props as Konva.CircleConfig).radius = element.radius;
  }

  const [styles, api] = useSpring(() => ({
    loop: { reverse: true },
    config: {
      duration: 250,
    },
    from: {
      opacity: 0,
    },
    to: {
      opacity: 0.375,
    },
  }));

  const animating = element.animating;
  React.useEffect(() => {
    if (animating) {
      api.resume();
    } else {
      api.pause();
    }
  }, [animating, api]);

  return (
    // @ts-ignore TS2589
    <Shape {...props} {...styles} />
  );
};

const ObserveredHighlight = observer(Highlight);

registerShapeComponent('highlight', ObserveredHighlight);
