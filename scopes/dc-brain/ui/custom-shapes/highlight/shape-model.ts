import { registerShapeModel } from '@manyun/dc-brain.aura-graphix';

const DEFAULT_COLOR = '#52c41a';

const highlight = {
  __DEFAULT_COLOR: DEFAULT_COLOR,
  name: 'Highlight',
  type: 'highlight',
  /**
   * - `rect`
   * - `circle`
   */
  shape: 'rect',
  radius: 5,
  x: 0,
  y: 0,
  width: 0,
  height: 0,
  fill: DEFAULT_COLOR,
  stroke: DEFAULT_COLOR,
  strokeWidth: 0,
  shadowColor: DEFAULT_COLOR,
  shadowBlur: 12,
  scaleX: 1,
  scaleY: 1,
  opacity: 1,
  animating: true,
};

registerShapeModel(highlight);
