import React from 'react';

import { a, useSpring } from '@react-spring/konva';
import { observer } from 'mobx-react-lite';

import { registerShapeComponent } from '@manyun/dc-brain.aura-graphix';

// @ts-ignore TS2339
const Highlight = ({ element }) => {
  const style = useSpring({
    loop: true,
    from: {
      x: 0,
      y: 0,
      scaleX: 1,
      scaleY: 1,
      opacity: 0.75,
      shadowOpacity: 0.25,
    },
    to: {
      x: (-element.width * 0.1) / 2,
      y: (-element.height * 0.1) / 2,
      scaleX: 1.1,
      scaleY: 1.1,
      opacity: 0.1,
      shadowOpacity: 1,
    },
  });

  return (
    // @ts-ignore TS2589
    <a.Rect
      width={element.width}
      height={element.height}
      fill={element.fill}
      stroke={element.stroke}
      strokeWidth={element.strokeWidth}
      shadowColor={element.shadowColor}
      shadowBlur={element.shadowBlur}
      {...style}
    />
  );
};

registerShapeComponent('highlight', observer(Highlight));
