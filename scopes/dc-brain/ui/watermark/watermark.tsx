import React from 'react';
import { useSelector } from 'react-redux';

import { Watermark as GaliojsWatermark } from '@galiojs/react-watermark/lib';
import dayjs from 'dayjs';

import { selectMe } from '@manyun/auth-hub.state.user';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';

export function Watermark() {
  const { name, username } = useSelector(
    selectMe,
    (left, right) => left.username === right.username
  );

  if (env.WATERMARK === 'off') {
    return null;
  }

  return (
    <GaliojsWatermark
      opacity={0.085}
      texts={[
        {
          translateX: 65,
          translateY: 35,
          rotate: -15,
          text: `${name} ${username || ''}`,
        },
        {
          translateX: 110,
          translateY: 55,
          rotate: -15,
          text: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        },
      ]}
      width={350}
      height={200}
    />
  );
}
