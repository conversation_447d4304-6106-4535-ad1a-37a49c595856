@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.layoutDrawer {
  z-index: @zindex-modal + 1;
  .layoutDrawerContent {
    height: calc(100vh - 50px);
    display: flex;
    position: relative;
    > div:nth-child(1) {
      width: 208px;
      height: 100%;
      border-right: 1px solid @border-color-split;
      overflow: hidden auto;
      .dragMenuItem {
        width: 208px;
        height: 50px;
        padding: 0 8px 0 18px;
        cursor: pointer;
        display: flex;
        align-items: center;
        justify-content: space-between;
        color: @text-color;
        &:hover {
          background-color: @primary-1;
        }
        > div:nth-child(1) {
          flex-grow: 1;
        }
        > div:nth-child(2) {
          visibility: hidden;
        }
        &:hover > div:nth-child(2) {
          visibility: inherit;
        }
      }
      .dragMenuItemMain {
        height: 50px;
        padding: 0 8px 0 18px;
        display: flex;
        align-items: center;
        background-color: @background-color-base;
        cursor: pointer;
      }
    }
    > div:nth-child(2) {
      width: 640px;
      height: 100%;
      > span:nth-child(1) {
        height: 60px;
      }
      .drawerContent {
        height: calc(100% - 100px);
        padding: 0 16px;
        overflow-y: scroll;
      }
    }
    > div:nth-child(3) {
      width: 160px;
      margin-top: 60px;
      padding-bottom: 20px;
      overflow-y: scroll;
      .tabText {
        color: @text-color;
        &:hover {
          color: @primary-color;
        }
      }
      :global(.@{prefixCls}-tabs-tab-active) {
        .tabText {
          color: @primary-color;
        }
      }
    }
    .drawerWrapperClose {
      position: absolute;
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
    .lately {
      margin-top: 20px;
      color: @text-color;
      text-indent: 8px;
    }
    .latelyList {
      display: flex;
      flex-wrap: wrap;
      > div {
        flex-basis: 33.33%;
      }
    }
  }
}

.menuMain {
  display: flex;
  line-height: 18px;
  margin-top: 10px;
  .menuLayOut {
    flex-basis: 33.33%;
  }
}
.menuL1 {
  color: @primary-color;
  > div:nth-child(1) {
    height: 32px;
    line-height: 32px;
    margin: 11px 0;
    text-indent: 8px;
    font-weight: 600;
  }
  .menuL1Active {
    background-color: @primary-color;
    color: @white;
  }
}
.menuL2 {
  color: @text-color;
  > div:nth-child(1) {
    height: 30px;
    line-height: 30px;
    text-indent: 8px;
  }
  .menuL2Link {
    cursor: pointer;
  }
}
.menuL3 {
  padding: 0 8px;
  height: 32px;
  color: @text-color-secondary;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  .menuL3icon {
    visibility: hidden;
  }
  &:hover {
    background-color: @primary-1;
  }
  &:hover .menuL3icon {
    visibility: inherit;
  }
}

:global(.@{prefixCls}-layout).layout {
  position: relative;
  min-height: 100vh;

  .header {
    z-index: 999;
    display: flex;
    justify-content: space-between;
    align-items: center;
    position: fixed;
    top: 0;
    right: 0;
    left: 0;
    padding: 0 22px 0 0;
    height: 50px;
    line-height: 50px;
    background: @component-background;
    border-bottom: 1px solid @border-color-split;
    .headerIcon {
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      width: 50px;
      height: 50px;
      background-color: @primary-color;
    }
  }

  .content {
    position: relative;
    padding-right: var(--content-padding-right);
    padding-bottom: var(--content-padding-bottom);
    padding-left: var(--content-padding-left);

    &.contentFixed {
      overflow-y: auto;

      .contentContainer {
        height: var(--content-height);
      }
    }

    &.contentWithoutBreadcrumbs {
      .contentContainer {
        height: calc(
          var(--content-height) + var(--breadcrumb-margin-top) + var(--breadcrumb-height) +
            var(--breadcrumb-margin-bottom)
        );
      }
    }

    &.contentFluid {
      padding-left: 0;
      padding-right: 0;
    }
  }

  .breadcrumbs {
    margin-top: var(--breadcrumb-margin-top);
    margin-bottom: var(--breadcrumb-margin-bottom);
    ol {
      align-items: center;
    }
  }
  :global(.dc-base-layout_popup-container) {
    z-index: 9999;
    position: fixed;
    top: 0;
    :global(.@{prefixCls}-dropdown) {
      max-height: 80vh;
      overflow-y: auto;

      &::before {
        display: none;
      }

      :global(.@{prefixCls}-dropdown-menu) {
        padding: 0;
        border-radius: 0;

        :global(.@{prefixCls}-dropdown-menu-submenu) {
          :global(.@{prefixCls}-dropdown-menu-submenu-expand-icon) {
            display: none;
          }
        }
      }
    }

    :global(.@{prefixCls}-dropdown-menu-submenu-popup) {
      :global(.@{prefixCls}-dropdown-menu-sub) {
        margin-left: calc(-0.3em + 1px);
      }
    }

    :global(.@{prefixCls}-menu-submenu-popup) {
      top: 49px !important;
      :global(.@{prefixCls}-menu) {
        margin-left: -20px;
        border-radius: 0;

        &:global(.@{prefixCls}-menu-sub) {
          min-width: 124px;
        }

        :global(.@{prefixCls}-menu-item) {
          margin: 0;
          padding: 9px 20px 7px;
          min-width: 124px;
          height: 40px;
          line-height: 22px;
          border-top: 0;
          border-bottom: 2px solid transparent;

          > a {
            width: auto;
          }
        }
      }
    }
  }
  .menusContainer {
    flex: 1;
  }
}

.topMenu {
  height: 50px;
  display: flex;
  align-items: center;
}

.topSubMenu {
  :global(.@{prefixCls}-menu-submenu) {
    top: 0;
    margin-top: 0;
    border-top: 2px solid transparent;
    border-bottom: 0;

    &::after {
      display: none;
    }

    &:hover {
      border-bottom: 0;
    }

    :global(.@{prefixCls}-menu-submenu-title) {
      padding: 0;
      cursor: default;

      span:global(.anticon.anticon-down) {
        margin-left: 8px;
        margin-right: 0;
        font-size: 12px;
        min-width: 12px;
        max-width: 12px;
      }
    }
  }
}

.topMenuItem {
  &:global(.@{prefixCls}-menu-item) {
    top: 0;
    margin: 0;
    border-bottom: 0;

    &:hover {
      border-bottom: 0;
    }
  }
}

.userMenuItem {
  padding: 12px;
  cursor: pointer;

  > * + * {
    margin-left: 8px;
  }
}
.userMenuContent {
  width: 182px;
  background-color: @component-background;
}
