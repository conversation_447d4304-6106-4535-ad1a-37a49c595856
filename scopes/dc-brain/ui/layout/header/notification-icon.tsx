import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { BellOutlined as NotificationsIcon } from '@ant-design/icons';
import difference from 'lodash.difference';

import { Badge } from '@manyun/base-ui.ui.badge';
import { message } from '@manyun/base-ui.ui.message';

import { useLayout } from '@manyun/dc-brain.context.layout';
import {
  OnSiteMessageState,
  OnSiteMessageType,
} from '@manyun/notification-hub.model.on-site-messages';
import { generateOnsiteMessageUrl } from '@manyun/notification-hub.route.notification-routes';
import { fetchOnSiteMessages } from '@manyun/notification-hub.service.fetch-on-site-messages';
import {
  onSiteMessagesSliceActions,
  selecOnSiteMessagesUnreadNum,
  selectStateTypedOnSiteMessages,
} from '@manyun/notification-hub.state.on-site-messages';

export default function NotificationIcon() {
  const res = useSelector(
    selectStateTypedOnSiteMessages(OnSiteMessageState.Unread, OnSiteMessageType.All),
    (left, right) =>
      difference(left.ids, right.ids).length === 0 &&
      left.page === right.page &&
      left.pageSize === right.pageSize
  );

  const { page, pageSize } = res;

  const { setMenuVisible } = useLayout();
  const dispatch = useDispatch();
  const fetchUnreadMessages = React.useCallback(async () => {
    const { error, data } = await fetchOnSiteMessages({
      type: OnSiteMessageType.All,
      state: OnSiteMessageState.Unread,
      page,
      pageSize,
    });
    if (error) {
      message.error(error.message);
      return;
    }
    dispatch(
      onSiteMessagesSliceActions.setMessages({
        type: OnSiteMessageType.All,
        state: OnSiteMessageState.Unread,
        messages: data.data,
        total: data.total,
      })
    );
  }, [dispatch, page, pageSize]);

  React.useEffect(() => {
    fetchUnreadMessages();

    const currentInterval = window.setInterval(fetchUnreadMessages, 1 * 60 * 1000);

    return () => {
      window.clearInterval(currentInterval);
    };
  }, [fetchUnreadMessages]);
  const { all } = useSelector(selecOnSiteMessagesUnreadNum());

  return (
    <div style={{ marginRight: 28 }}>
      <Badge count={all} style={{ top: '-4px', right: '-10px' }} size="small">
        <NotificationsIcon
          style={{ fontSize: 16 }}
          onClick={() => {
            setMenuVisible(false);
            // @REFACTORME @Jerry
            dispatch({
              type: 'router/redirect',
              payload: {
                location: generateOnsiteMessageUrl({
                  state: OnSiteMessageState.Unread,
                  type: OnSiteMessageType.All,
                }),
              },
            });
          }}
        />
      </Badge>
    </div>
  );
}
