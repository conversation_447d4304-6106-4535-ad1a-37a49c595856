import { useApolloClient } from '@apollo/client';
import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { DEFAULT_USER_PASSWORD_VALIDATION_RULES } from '@manyun/auth-hub.model.user';
import {
  selectUpdateMyPasswordModalVisible,
  updateMyPasswordAction,
  userSliceActions,
} from '@manyun/auth-hub.state.user';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

const ORIGINAL_PASSWORD = 'originalPassword';
const NEW_PASSWORD = 'newPassword';
const CONFIRMED_NEW_PASSWORD = 'confirmedNewPassword';

function UpdateMyPasswordForm({ form }: { form: FormInstance }) {
  const { getFieldValue, setFieldsValue, validateFields } = form;
  const formNewPassword = Form.useWatch(NEW_PASSWORD, form);

  return (
    <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
      <Form.Item
        label="原密码"
        name={ORIGINAL_PASSWORD}
        rules={[{ required: true, message: '原密码必填!' }]}
      >
        <Input.Password
          onBlur={() => {
            const newPassword = getFieldValue(NEW_PASSWORD);
            const confirmedNewPassword = getFieldValue(CONFIRMED_NEW_PASSWORD);
            const fieldNames = [];
            if (newPassword) {
              fieldNames.push(NEW_PASSWORD);
            }
            if (confirmedNewPassword) {
              fieldNames.push(CONFIRMED_NEW_PASSWORD);
            }
            if (fieldNames.length <= 0) {
              return;
            }
            validateFields(fieldNames);
          }}
        />
      </Form.Item>
      <Form.Item
        label="新密码"
        name={NEW_PASSWORD}
        extra={
          !formNewPassword
            ? '长度至少 8 位，至多 64 位，必须同时包含大小写字母、特殊字符和数字，不允许有空格'
            : undefined
        }
        rules={[
          ...DEFAULT_USER_PASSWORD_VALIDATION_RULES,
          _form => ({
            validator: (_rule, newPassword, callback) => {
              if (newPassword === _form.getFieldValue(ORIGINAL_PASSWORD)) {
                callback('新密码不能和原密码相同！');
                return;
              }
              callback();
            },
          }),
        ]}
      >
        <Input.Password
          onChange={({ target: { value } }) => {
            if (value === '') {
              setFieldsValue({ confirmedNewPassword: '' });
            }
          }}
          onBlur={() => {
            const confirmedNewPassword = getFieldValue(CONFIRMED_NEW_PASSWORD);
            if (confirmedNewPassword) {
              validateFields([CONFIRMED_NEW_PASSWORD]);
            }
          }}
        />
      </Form.Item>
      <Form.Item
        label="确认密码"
        name={CONFIRMED_NEW_PASSWORD}
        validateFirst
        rules={[
          { required: true, message: '确认密码必填!' },
          _form => ({
            validator: (_rule, confirmedNewPassword, callback) => {
              if (confirmedNewPassword !== _form.getFieldValue(NEW_PASSWORD)) {
                callback('确认密码必须和新密码相同！');
                return;
              }
              callback();
            },
          }),
        ]}
      >
        <Input.Password />
      </Form.Item>
    </Form>
  );
}

export function UpdateMyPasswordModal() {
  const [form] = Form.useForm<{ originalPassword: string; confirmedNewPassword: string }>();
  const [loading, setLoading] = React.useState(false);

  const open = useSelector(selectUpdateMyPasswordModalVisible);
  const dispatch = useDispatch();

  const client = useApolloClient();

  return (
    <Modal
      title="修改密码"
      // 因为密码信息是隐私信息，不做缓存
      destroyOnClose
      open={open}
      cancelButtonProps={{ loading }}
      okButtonProps={{ loading }}
      onCancel={() => {
        dispatch(userSliceActions.toggleUpdateMyPasswordModalVisible());
      }}
      onOk={() => {
        form.validateFields().then(values => {
          setLoading(true);
          dispatch(
            updateMyPasswordAction({
              originalPassword: values.originalPassword,
              newPassword: values.confirmedNewPassword,
              callback: (result: boolean) => {
                setLoading(false);
                if (result) {
                  dispatch(userSliceActions.toggleUpdateMyPasswordModalVisible());
                  client.resetStore();
                  window.localStorage.removeItem('token');
                  window.localStorage.removeItem('sessionId');
                }
              },
            })
          );
        });
      }}
    >
      <UpdateMyPasswordForm form={form} />
    </Modal>
  );
}
