/* eslint-disable @typescript-eslint/no-explicit-any */
import {
  CloseOutlined,
  DownOutlined,
  SearchOutlined,
  StarFilled,
  StarOutlined,
} from '@ant-design/icons';
import BaseLayout from 'antd/es/layout';
import classNames from 'classnames';
import debounce from 'lodash.debounce';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { Link, useHistory } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Input } from '@manyun/base-ui.ui.input';
import type { MenuProps } from '@manyun/base-ui.ui.menu';
import { Menu } from '@manyun/base-ui.ui.menu';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { fetchCollectionMenus } from '@manyun/dc-brain.service.fetch-collection-menus';
import { fetchSaveCollectionMenus } from '@manyun/dc-brain.service.fetch-save-collection-menus';

import styles from '../layout.module.less';
import { layoutRegistry } from '../registry/layout-registry';
import {
  ANY_REPORTS_MENU_CODE,
  ANY_REPORTS_MENU_CODE_PREFIX,
  routesMapper,
} from '../routes-mapper';
import DragMenus from './drag-menus';
import NotificationIcon from './notification-icon';
import UserMenus from './user-menus';

export type MenuTreeNode = {
  metaName: string;
  metaCode: string;
  remarks: string | null;
  routePath: string | null;
  children?: MenuTreeNode[];
  collectStatus?: boolean;
  Level1MetaCode?: string;
};

const LinkItem = ({
  item,
  collections,
  getLinkTo,
  getData,
}: {
  item: MenuTreeNode;
  collections: string[];
  getLinkTo: Function;
  getData: () => void;
}) => {
  const history = useHistory();
  const { setMenuVisible, setSideBarVisible, setSideBarTab } = useLayout();
  const { userId } = useSelector(selectMe);

  // 判断url 是否是一个可用的 url
  const isValidURL = (url: string): boolean => {
    const pattern = new RegExp(
      '^(https?:\\/\\/)?' + // protocol
        '((([a-z\\d]([a-z\\d-]*[a-z\\d])*)\\.)+[a-z]{2,}|' + // domain name and extension
        '((\\d{1,3}\\.){3}\\d{1,3}))' + // OR ip (v4) address
        '(\\:\\d+)?' + // port
        '(\\/[-a-z\\d%_.~+]*)*' + // path
        '(\\?[;&a-z\\d%_.~+=-]*)?' + // query string
        '(\\#[-a-z\\d_]*)?$',
      'i'
    ); // fragment locator
    return !!pattern.test(url);
  };

  const onHandleClick = () => {
    const menuCode = item.metaCode.startsWith(ANY_REPORTS_MENU_CODE_PREFIX)
      ? ANY_REPORTS_MENU_CODE
      : item.metaCode;
    const url = getLinkTo(routesMapper[menuCode]?.link, item);
    if (item.routePath && isValidURL(item.routePath)) {
      window.open(item.routePath);
    } else {
      history.push(url);
    }
    setMenuVisible(false);
    //隐藏对比小工具
    setSideBarVisible(false);
    setSideBarTab('roomview');
  };

  const onCollection = async (
    e: React.MouseEvent<HTMLSpanElement, MouseEvent>,
    item: MenuTreeNode
  ) => {
    e.stopPropagation();
    const { metaCode } = item;
    await fetchSaveCollectionMenus({
      userId,
      menus: Array.from(new Set([...collections, metaCode])),
    });
    getData();
  };

  const onCancelCollection = async (
    e: React.MouseEvent<HTMLSpanElement, MouseEvent>,
    item: MenuTreeNode
  ) => {
    e.stopPropagation();
    const { metaCode } = item;
    const metaCodes = collections.filter(item => item !== metaCode);
    await fetchSaveCollectionMenus({ userId, menus: Array.from(new Set(metaCodes)) });
    getData();
  };

  return (
    <div className={styles.menuL3} onClick={onHandleClick}>
      <div>{item.metaName}</div>
      <div>
        {item.collectStatus ? (
          <StarFilled
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={e => onCancelCollection(e, item)}
          />
        ) : (
          <StarOutlined
            className={styles.menuL3icon}
            style={{ color: `var(--${prefixCls}-primary-color)` }}
            onClick={e => onCollection(e, item)}
          />
        )}
      </div>
    </div>
  );
};

//所有menus
const List = ({
  menus,
  collections,
  activeMenuKey,
  getLinkTo,
  getData,
}: {
  menus: MenuTreeNode[];
  collections: string[];
  activeMenuKey: string | undefined;
  getLinkTo: Function;
  getData: () => void;
}) => {
  const newMenus = useDeepCompareMemo(() => {
    //计算1、2、3级菜单长度
    const getMenusLength = (menus: MenuTreeNode[]) => {
      let sum = 0;
      const getMenuLength = function (arr: MenuTreeNode[]) {
        arr.forEach(item => {
          if (Array.isArray(item.children)) {
            getMenuLength(item.children);
          }
          sum = sum + 1;
        });
      };
      getMenuLength(menus);
      return sum;
    };

    const average = Math.round(getMenusLength(menus) / 3);
    const newMenusArr = [];

    let eachRowSum = 0;
    let eachRowMenu: MenuTreeNode[] = [];
    menus.forEach(item => {
      eachRowSum = eachRowSum + 1;
      if (Array.isArray(item.children)) {
        eachRowSum = eachRowSum + getMenusLength(item.children);
      }

      if (eachRowSum - average > 12 && newMenusArr.length < 2) {
        newMenusArr.push(eachRowMenu);
        eachRowMenu = [];
        eachRowSum = 1;
        if (Array.isArray(item.children)) {
          eachRowSum = eachRowSum + getMenusLength(item.children);
        }
        eachRowMenu.push(item);
        return;
      }
      if (eachRowSum - average > 0 && eachRowSum - average < 12 && newMenusArr.length < 2) {
        eachRowMenu.push(item);
        newMenusArr.push(eachRowMenu);
        eachRowMenu = [];
        eachRowSum = 0;
        return;
      }
      eachRowMenu.push(item);
    });
    newMenusArr.push(eachRowMenu);
    return newMenusArr;
  }, [menus]);

  const { setMenuVisible, setSideBarVisible, setSideBarTab } = useLayout();
  const history = useHistory();

  const onHandleClick = (item: MenuTreeNode) => {
    if (routesMapper[item.metaCode]?.link) {
      const url = getLinkTo(routesMapper[item.metaCode].link, item);
      history.push(url);
      setMenuVisible(false);
      //跳转新route隐藏对比小工具
      setSideBarVisible(false);
      setSideBarTab('roomview');
    }
  };

  return (
    <div className={styles.menuMain}>
      {Array.isArray(newMenus) &&
        newMenus.map(menuItems => {
          if (menuItems.length <= 0) {
            return null;
          }

          return (
            <div key={menuItems[0].metaCode} className={styles.menuLayOut}>
              {menuItems.map(menuLevel1Item => {
                return (
                  <div
                    key={menuLevel1Item.metaCode}
                    className={styles.menuL1}
                    id={menuLevel1Item.metaCode}
                  >
                    <div
                      className={
                        activeMenuKey === menuLevel1Item.metaCode ? styles.menuL1Active : ''
                      }
                    >
                      {menuLevel1Item.metaName}
                    </div>
                    {Array.isArray(menuLevel1Item.children) &&
                      menuLevel1Item.children.map(menuLevel2Item => {
                        return (
                          <div key={menuLevel2Item.metaCode} className={styles.menuL2}>
                            {routesMapper[menuLevel2Item.metaCode]?.link ? (
                              <div
                                className={styles.menuL2Link}
                                onClick={() => {
                                  onHandleClick(menuLevel2Item);
                                }}
                              >
                                {menuLevel2Item.metaName}
                              </div>
                            ) : (
                              <div>{menuLevel2Item.metaName}</div>
                            )}

                            {Array.isArray(menuLevel2Item.children) &&
                              menuLevel2Item.children.map((menuLevel3Item: MenuTreeNode) => {
                                menuLevel3Item.Level1MetaCode = menuLevel1Item.metaCode;
                                if (collections.includes(menuLevel3Item.metaCode)) {
                                  menuLevel3Item.collectStatus = true;
                                } else {
                                  menuLevel3Item.collectStatus = false;
                                }
                                return (
                                  <LinkItem
                                    key={menuLevel3Item.metaCode}
                                    item={menuLevel3Item}
                                    collections={collections}
                                    getLinkTo={getLinkTo}
                                    getData={getData}
                                  />
                                );
                              })}
                          </div>
                        );
                      })}
                  </div>
                );
              })}
            </div>
          );
        })}
    </div>
  );
};

const Content = ({ menus, getLinkTo }: { menus: MenuTreeNode[]; getLinkTo: Function }) => {
  const { menuVisible, setMenuVisible } = useLayout();
  const { userId } = useSelector(selectMe);

  const [value, setValue] = useState<string>();

  const [allMenuL3s, setAllMenuL3s] = useState<MenuTreeNode[]>([]);

  const [searchMenus, setSearchMenus] = useState<MenuTreeNode[]>([]);

  const [collections, setCollections] = useState<string[]>([]);
  const [collectionMenus, setCollectionMenus] = useState<MenuTreeNode[]>([]);

  const [activeMenuKey, setActiveMenuKey] = useState<string>();

  const getData = useCallback(
    async (originalMenus: MenuTreeNode[]) => {
      const { data, error } = await fetchCollectionMenus({ userId });
      if (error) {
        message.error(error.message);
        return;
      }

      const allMenuL2 = originalMenus.reduce((result: MenuTreeNode[], item: MenuTreeNode) => {
        if (item.children) {
          return result.concat(item.children);
        } else {
          return result;
        }
      }, []);

      const allMenuL3s = allMenuL2
        .reduce((result: MenuTreeNode[], item: MenuTreeNode) => {
          if (item.children) {
            return result.concat(item.children);
          } else {
            return result;
          }
        }, [])
        .map(item => {
          if (data.data.includes(item.metaCode)) {
            item.collectStatus = true;
            return {
              ...item,
              collectStatus: true,
            };
          }
          return item;
        });
      //收藏按顺序
      const collectionMenus = data.data
        .map((collectItem: string) => {
          return allMenuL3s.find(item => item.metaCode === collectItem);
        })
        .filter((item: MenuTreeNode | undefined) => typeof item !== 'undefined');

      setAllMenuL3s(allMenuL3s);
      setCollections(data.data);
      setCollectionMenus(collectionMenus);
    },
    [userId]
  );

  useEffect(() => {
    if (menuVisible) {
      getData(menus);
    }
  }, [getData, menuVisible, menus]);

  const onTabChange = (value: string) => {
    setValue(undefined);
    setActiveMenuKey(value);
    document.getElementById(value)?.scrollIntoView();
  };

  const onChange = debounce((value: string) => {
    if (!value) {
      setSearchMenus([]);
      return;
    }
    const list = allMenuL3s.filter(item => {
      return item.metaName.indexOf(value) > -1;
    });
    setSearchMenus(list);
  }, 200);

  const onSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setValue(e.target.value);
    setActiveMenuKey(undefined);
    onChange(e.target.value);
  };

  return (
    <div className={styles.layoutDrawerContent}>
      <div>
        <DragMenus
          collectionMenus={collectionMenus}
          collections={collections}
          getData={() => getData(menus)}
          getLinkTo={getLinkTo}
          onClear={() => setValue(undefined)}
        />
      </div>
      <div>
        <Input
          placeholder="请输入关键字"
          bordered={false}
          prefix={<SearchOutlined />}
          value={value}
          onChange={onSearch}
        />
        <Divider style={{ margin: '0' }} />
        <div className={styles.drawerContent}>
          {value ? (
            <div>
              <div className={styles.lately}>
                {searchMenus.length ? (
                  <>
                    共找到
                    <span style={{ color: `var(--${prefixCls}-primary-color)` }}>
                      {searchMenus.length}
                    </span>
                    个与<span style={{ color: `var(--${prefixCls}-primary-color)` }}>{value}</span>
                    相关功能
                  </>
                ) : (
                  <Empty />
                )}
              </div>
              <div className={styles.latelyList}>
                {searchMenus.map(item => {
                  return (
                    <LinkItem
                      key={item.metaCode}
                      item={{ ...item, collectStatus: collections.includes(item.metaCode) }}
                      collections={collections}
                      getLinkTo={getLinkTo}
                      getData={() => getData(menus)}
                    />
                  );
                })}
              </div>
            </div>
          ) : (
            <List
              menus={menus}
              collections={collections}
              activeMenuKey={activeMenuKey}
              getLinkTo={getLinkTo}
              getData={() => getData(menus)}
            />
          )}
        </div>
      </div>
      <div>
        <Tabs
          activeKey={activeMenuKey}
          items={menus.map(item => {
            return {
              key: item.metaCode,
              label: <span className={styles.tabText}>{item.metaName}</span>,
            };
          })}
          tabPosition="right"
          tabBarGutter={0}
          onChange={onTabChange}
        />
      </div>
      <CloseOutlined className={styles.drawerWrapperClose} onClick={() => setMenuVisible(false)} />
    </div>
  );
};

type LayoutHeaderProps = {
  homeUrl: string;
  showMenuLevel2s: boolean;
  menus: MenuTreeNode[];
  currentKeys: string[];
  getLinkTo: Function;
  getText: Function;
};

export function Header({
  homeUrl,
  showMenuLevel2s,
  menus,
  currentKeys,
  getLinkTo,
  getText,
}: LayoutHeaderProps) {
  const { menuVisible, setMenuVisible, setSideBarVisible, setSideBarTab } = useLayout();

  const timer = useRef<number | null>(null);

  const onHandleClick = () => {
    if (!menuVisible) {
      setMenuVisible(true);
    } else {
      setMenuVisible(false);
    }
  };

  const onClose = useCallback(() => {
    setMenuVisible(false);
    //隐藏对比小工具
    setSideBarVisible(false);
    setSideBarTab('roomview');
  }, [setMenuVisible, setSideBarTab, setSideBarVisible]);

  const onMouseEnter = () => {
    if (!menuVisible) {
      timer.current = window.setTimeout(function () {
        setMenuVisible(true);
      }, 500);
    }
  };

  const onMouseLeave = () => {
    if (timer.current) {
      clearTimeout(timer.current);
    }
  };

  const menuLevel2s = useDeepCompareMemo(() => {
    if (currentKeys.length > 0) {
      const currentMenuLevel2s = menus.find(item => item.metaCode === currentKeys[0]);
      return currentMenuLevel2s?.children ?? [];
    }
    return [];
  }, [currentKeys, menus]);

  const currentApp = useDeepCompareMemo(() => {
    if (currentKeys.length > 0) {
      return menus.filter(item => currentKeys.includes(item.metaCode))[0];
    }
    return null;
  }, [currentKeys, menus]);

  const menuItems = useDeepCompareMemo(() => {
    if (currentApp && showMenuLevel2s) {
      const items: MenuProps['items'] = menuLevel2s.map(menuLevel2 => {
        if (menuLevel2.children && menuLevel2.children.length > 0) {
          return {
            key: menuLevel2.metaCode,
            label: (
              <>
                {menuLevel2.metaName} <DownOutlined />
              </>
            ),
            children: menuLevel2.children.map(menuLevel3 => {
              const menuCode = menuLevel3.metaCode.startsWith(ANY_REPORTS_MENU_CODE_PREFIX)
                ? ANY_REPORTS_MENU_CODE
                : menuLevel3.metaCode;
              const link = getLinkTo(routesMapper[menuCode]?.link, menuLevel3);
              return {
                key: menuLevel3.metaCode,
                label: link ? (
                  <Link to={link} onClick={onClose}>
                    {menuLevel3.metaName}
                  </Link>
                ) : (
                  menuLevel3.metaName
                ),
              };
            }),
          };
        } else {
          return {
            key: menuLevel2.metaCode,
            label: getText(menuLevel2),
          };
        }
      });

      return items;
    }
    return [];
  }, [currentApp, getLinkTo, getText, menuLevel2s, onClose, showMenuLevel2s]);

  return (
    <BaseLayout.Header className={styles.header}>
      <div style={{ display: 'flex', gap: 16, alignItems: 'center' }}>
        <div
          className={styles.headerIcon}
          onMouseEnter={onMouseEnter}
          onMouseLeave={onMouseLeave}
          onClick={onHandleClick}
        >
          {menuVisible ? (
            <CloseOutlined style={{ color: 'white' }} />
          ) : (
            <img height={36} alt="Logo APP" src="/images/logo-app.png" />
          )}
        </div>
        {env.LOGO_URI && (
          <Link to={homeUrl} onClick={onClose}>
            <img height={50} alt="Logo" src={env.LOGO_URI} />
          </Link>
        )}

        <Drawer
          style={{ top: 50 }}
          headerStyle={{ display: 'none' }}
          bodyStyle={{ padding: 0 }}
          contentWrapperStyle={{ boxShadow: 'none', width: '1008px' }}
          className={styles.layoutDrawer}
          placement="left"
          open={menuVisible}
          onClose={onClose}
        >
          <Content menus={menus} getLinkTo={getLinkTo} />
        </Drawer>
      </div>
      {currentApp && showMenuLevel2s && (
        <Menu
          className={classNames(
            styles.topMenu,
            styles.topSubMenu,
            styles.topMenuItem,
            styles.menusContainer
          )}
          mode="horizontal"
          getPopupContainer={() => document.querySelector('.dc-base-layout_popup-container')!}
          selectedKeys={currentKeys.slice(1)}
          items={menuItems}
        />
      )}
      <Space>
        <CustomHeadMenuItems />
        <NotificationIcon />
        <UserMenus />
      </Space>
    </BaseLayout.Header>
  );
}

function CustomHeadMenuItems() {
  const { selectedMenuCodes } = useLayout();
  const [nodes, setNodes] = React.useState<React.ReactNode[]>([]);
  React.useEffect(() => {
    const headMenuItems = layoutRegistry.getFabricsByVariant('head-menu-item');
    const _nodes = headMenuItems.map(([key, [Fabric, { prepare }]]) => {
      const _fabricProps = prepare?.({ selectedMenuCodes });
      return <Fabric key={key} {..._fabricProps} />;
    });
    setNodes(_nodes);
  }, [selectedMenuCodes]);
  return <>{nodes}</>;
}
