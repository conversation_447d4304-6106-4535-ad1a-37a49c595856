import React from 'react';
import { Drag<PERSON><PERSON><PERSON>ontext, Draggable, Droppable } from 'react-beautiful-dnd';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { AppstoreOutlined, CloseOutlined, HolderOutlined } from '@ant-design/icons';

import { Space } from '@manyun/base-ui.ui.space';

import { selectMe } from '@manyun/auth-hub.state.user';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { fetchSaveCollectionMenus } from '@manyun/dc-brain.service.fetch-save-collection-menus';

import styles from '../layout.module.less';
import {
  ANY_REPORTS_MENU_CODE,
  ANY_REPORTS_MENU_CODE_PREFIX,
  routesMapper,
} from '../routes-mapper';
import type { MenuTreeNode } from './index';

export default function DragMenus({
  collectionMenus,
  collections,
  getLinkTo,
  getData,
  onClear,
}: {
  collectionMenus: MenuTreeNode[];
  collections: string[];
  getLinkTo: Function;
  getData: Function;
  onClear: Function;
}) {
  const history = useHistory();
  const { setMenuVisible } = useLayout();
  const { userId } = useSelector(selectMe);

  const onMenuClick = (item: MenuTreeNode) => {
    const menuCode = item.metaCode.startsWith(ANY_REPORTS_MENU_CODE_PREFIX)
      ? ANY_REPORTS_MENU_CODE
      : item.metaCode;
    const url = getLinkTo(routesMapper[menuCode]?.link, item);
    setMenuVisible(false);
    history.push(url);
  };

  const onCancelCollection = async (e: unknown, item: MenuTreeNode) => {
    e.stopPropagation();
    const { metaCode } = item;
    const metaCodes = collections.filter(item => item !== metaCode);
    await fetchSaveCollectionMenus({ userId, menus: metaCodes });
    getData();
  };

  const onDragEnd = async (result: unknown) => {
    const { source, destination } = result;
    if (!destination) {
      return;
    }
    const destinationIndex = destination.index ?? 0;
    const sourceIndex = source.index ?? 0;

    const [deleteItem] = collectionMenus.splice(sourceIndex, 1);

    collectionMenus.splice(destinationIndex, 0, deleteItem);

    const metaCodes = collectionMenus.map((item: MenuTreeNode) => item.metaCode);
    await fetchSaveCollectionMenus({ userId, menus: metaCodes });
    getData();
  };
  return (
    <>
      <div
        className={styles.dragMenuItemMain}
        onClick={() => {
          onClear();
        }}
      >
        <Space>
          <AppstoreOutlined />
          全部
        </Space>
      </div>
      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable droppableId="drag-menu">
          {(provided: unknown) => (
            <div ref={provided.innerRef} {...provided.droppableProps}>
              {collectionMenus.map((item: MenuTreeNode, index: number) => {
                const { metaCode, metaName, Level1MetaCode = 'menu_crm' } = item;
                const customIcon = routesMapper[Level1MetaCode]?.icon;
                return (
                  <Draggable key={metaCode} draggableId={metaCode} index={index}>
                    {(provided: unknown) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        {...provided.dragHandleProps}
                      >
                        <div className={styles.dragMenuItem}>
                          <Space onClick={() => onMenuClick(item)}>
                            {/* <StarFilled style={{ fontSize: '14px' }} /> */}
                            {customIcon &&
                              React.cloneElement(customIcon, { style: { marginRight: 8 } })}
                            <span>{metaName}</span>
                          </Space>
                          <Space>
                            <CloseOutlined
                              style={{ fontSize: '12px' }}
                              onClick={e => onCancelCollection(e, item)}
                            />
                            <HolderOutlined style={{ fontSize: '12px' }} />
                          </Space>
                        </div>
                      </div>
                    )}
                  </Draggable>
                );
              })}
              {provided.placeholder}
            </div>
          )}
        </Droppable>
      </DragDropContext>
    </>
  );
}
