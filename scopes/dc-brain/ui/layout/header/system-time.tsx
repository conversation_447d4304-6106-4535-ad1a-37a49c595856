import React, { useCallback, useEffect, useState } from 'react';

import dayjs from 'dayjs';

import { Typography } from '@manyun/base-ui.ui.typography';

import { fetchSystemTime } from '@manyun/dc-brain.service.fetch-system-time';

const delay: number | undefined = 1000; /**每1s刷新 */

export const SystemTime = () => {
  const [timestamp, setTimestamp] = useState<number | null>(null);

  const _fetchSystemTime = useCallback(async () => {
    const { error, data } = await fetchSystemTime();
    if (error) {
      return;
    }
    setTimestamp(data);
  }, []);

  useEffect(() => {
    _fetchSystemTime();

    const interval = window.setInterval(() => {
      _fetchSystemTime();
    }, delay);

    return () => {
      if (interval) {
        window.clearInterval(interval);
      }
    };
  }, [_fetchSystemTime]);

  return (
    <Typography.Text type="secondary">
      {timestamp ? dayjs(timestamp).format('HH:mm:ss') : '--'}
    </Typography.Text>
  );
};
