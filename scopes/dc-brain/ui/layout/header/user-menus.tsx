import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { LogoutOutlined, SettingOutlined, UserOutlined } from '@ant-design/icons';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';

import { clearLocalStorage } from '@manyun/auth-hub.cache.user';
import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { generateUserProfileRoutePath } from '@manyun/auth-hub.route.auth-routes';
import {
  selectMe, // selectUpdateSoundPlay,
  selectShouldAlarmPop,
  userSliceActions,
} from '@manyun/auth-hub.state.user';
import { User } from '@manyun/auth-hub.ui.user';
import { useLayout } from '@manyun/dc-brain.context.layout';
import { updateClockInSchedule } from '@manyun/hrm.service.update-clock-in-schedule';

import styles from '../layout.module.less';
import { SystemTime } from './system-time';

type ClockCheckType = 'ON' | 'OFF' | 'OTHER';

const RenderClockButton = (props: { title: string; checkType: ClockCheckType; userId: number }) => {
  const { title, checkType, userId } = props;
  const [loading, setLoading] = useState(false);

  const onPunchClock = useCallback(async () => {
    setLoading(true);
    const { error, data } = await updateClockInSchedule({
      staffId: userId,
      checkTime: Date.now(),
      checkChannel: 'LOCAL',
      checkType,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (data) {
      message.success(data);
    }
  }, [checkType, userId]);

  return (
    <Button type="link" loading={loading} compact onClick={() => onPunchClock()}>
      {title}
    </Button>
  );
};

function UserMenus() {
  const dispatch = useDispatch();
  const { userId, username } = useSelector(
    selectMe,
    (left, right) => left.userId === right.userId && left.username === right.username
  );
  // const soundPlay = useSelector(selectUpdateSoundPlay);
  const shouldAlarmPop = useSelector(selectShouldAlarmPop);
  const [checkAuthorized] = useAuthorized({ checkByCode: 'ele_check' });
  const { setMenuVisible } = useLayout();

  if (!(userId && username)) {
    return null;
  }

  return (
    <Dropdown
      trigger={['click']}
      destroyPopupOnHide
      dropdownRender={() => (
        <div className={styles.userMenuContent}>
          <div
            className={styles.userMenuItem}
            style={{
              display: 'flex',
              justifyContent: 'space-between',
            }}
          >
            <div
              style={{
                flex: 1,
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis',
              }}
              title={username ?? ''}
            >
              {username ?? ''}
            </div>
            <SystemTime />
          </div>
          {checkAuthorized && (
            <>
              <Divider style={{ margin: 0 }} />
              <div
                className={styles.userMenuItem}
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                }}
              >
                <RenderClockButton title="上班打卡" checkType="ON" userId={userId} />
                <RenderClockButton title="下班打卡" checkType="OFF" userId={userId} />
              </div>
            </>
          )}
          <Divider style={{ margin: 0 }} />
          <div
            className={styles.userMenuItem}
            role="button"
            onClick={() => {
              setMenuVisible(false);
              window.open(generateUserProfileRoutePath({ id: userId.toString() }));
            }}
          >
            <UserOutlined />
            <span role="button">个人中心</span>
          </div>
          <Divider style={{ margin: 0 }} />
          <div
            className={styles.userMenuItem}
            role="button"
            onClick={() => {
              dispatch(userSliceActions.toggleUpdateMyPasswordModalVisible());
            }}
          >
            <SettingOutlined />
            <span role="button">修改密码</span>
          </div>
          <Divider style={{ margin: 0 }} />

          {/* Disabled for now */}
          {/* <div className={styles.userMenuItem}>
            <Space align="center" size={'large'}>
              <span role="button">告警语音播报</span>
              <Switch
                checked={soundPlay}
                onChange={() => {
                  dispatch(userSliceActions.toggleSoundPlay());
                }}
                onClick={(_, event) => {
                  event.stopPropagation();
                }}
                size="small"
              />
            </Space>
          </div> */}

          <div className={styles.userMenuItem}>
            <Space align="center" size="large">
              <span role="button">告警弹窗模式</span>
              <Switch
                checked={shouldAlarmPop}
                size="small"
                onChange={() => {
                  dispatch(userSliceActions.toggleShouldAlarmPop());
                }}
                onClick={(_, event) => {
                  event.stopPropagation();
                }}
              />
            </Space>
          </div>
          <Divider style={{ margin: 0 }} />

          <div
            role="button"
            onClick={() => {
              clearLocalStorage();
              window.location.assign('/logout');
            }}
          >
            <div className={styles.userMenuItem}>
              <LogoutOutlined />
              <span>退出</span>
            </div>
          </div>

          {window.location.search.includes('debug') && (
            <>
              <Divider style={{ margin: 0 }} />
              <div className={styles.userMenuItem}>
                <Space align="center" size="large">
                  <div>开发者模式</div>
                  <Switch
                    size="small"
                    onChange={() => {
                      // @REFACTORME @Jerry
                      dispatch({ type: 'common/toggleDevTools' });
                    }}
                    onClick={(_, event) => {
                      event.stopPropagation();
                    }}
                  />
                </Space>
              </div>
            </>
          )}
        </div>
      )}
      arrow
    >
      <div>
        <User id={userId} direction="horizontal" size={24} />
      </div>
    </Dropdown>
  );
}

export default UserMenus;
