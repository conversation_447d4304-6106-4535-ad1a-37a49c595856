import React from 'react';
import { Link, Route, MemoryRouter as Router, Switch } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';

import { Layout } from './layout';

export function BaseLayout() {
  return (
    <ConfigProvider>
      <FakeStore>
        <Router initialEntries={['/']}>
          <Switch>
            <Route path="/idc">
              <Layout homeUrl="/">IDC</Layout>
            </Route>
            <Route path="/block">
              <Layout homeUrl="/">BLOCK</Layout>
            </Route>
            <Route path="/room">
              <Layout homeUrl="/">ROOM</Layout>
            </Route>
            <Route path="/grid">
              <Layout homeUrl="/">GRID</Layout>
            </Route>
            <Route path="/">
              <Layout homeUrl="/">
                <Link to="/idc">IDC</Link>
                <br />
                <Link to="/block">BLOCK</Link>
                <br />
                <Link to="/room">ROOM</Link>
                <br />
                <Link to="/grid">GRID</Link>
              </Layout>
            </Route>
          </Switch>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
}
