import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { notification } from '@manyun/base-ui.ui.notification';

import { selectMe } from '@manyun/auth-hub.state.user';
import {
  launchNotificationsWebSocketAction,
  selectNotificationsMessages,
  subscriptionsSliceActions,
} from '@manyun/monitoring.state.subscriptions';
import { alarmTransmissionStateLocale } from '@manyun/monitoring.state.subscriptions';

export type NotificationVariant =
  | /* 告警盯屏 */ 'alarms-subscription'
  | /* 告警传输 */ 'alarms-transmission';

export type NotificationsSubscriptionProps = {
  /** 最多同时显示的 `notification` 的数量 */
  maxCount?: number;
  allowNotificationVariants?: NotificationVariant[];
};

export function NotificationsSubscription({
  maxCount = 5,
  allowNotificationVariants = ['alarms-subscription'],
}: NotificationsSubscriptionProps) {
  const dispatch = useDispatch();
  const { idc } = useSelector(selectMe, (left, right) => left.idc === right.idc);

  React.useEffect(() => {
    if (idc) {
      dispatch(launchNotificationsWebSocketAction({ idc }));
    }
  }, [dispatch, idc]);

  const { entities, ids } = useSelector(selectNotificationsMessages);
  const pendingIds = React.useMemo(() => ids.slice(0, maxCount), [ids, maxCount]);
  const pendingMessages = React.useMemo(
    () =>
      pendingIds
        .map(id => entities[id])
        .filter(
          message =>
            (allowNotificationVariants.includes('alarms-subscription') &&
              message.businessScene === 'ALARM_SCREEN') ||
            (allowNotificationVariants.includes('alarms-transmission') &&
              message.businessScene === 'ALARM_TRANSMISSION')
        ),
    [allowNotificationVariants, entities, pendingIds]
  );

  React.useEffect(() => {
    notification.config({
      top: 55,
      maxCount,
    });
  }, [maxCount]);

  React.useEffect(() => {
    if (pendingIds.length > 0) {
      pendingMessages.forEach(pendingMessage => {
        let message: string | null = null;
        let description: React.ReactNode = null;
        if (pendingMessage.businessScene === 'ALARM_TRANSMISSION') {
          message = '告警传输';
          description = (
            <>
              【{pendingMessage.alarmLevelName} 】【
              {alarmTransmissionStateLocale[pendingMessage.alarmStatus]}】
              {pendingMessage.idcTag + pendingMessage.blockTag ?? '' + pendingMessage.roomTag ?? ''}{' '}
              {pendingMessage.deviceName ?? pendingMessage.triggerGuid}{' '}
              {pendingMessage.pointName ?? pendingMessage.pointCode} {pendingMessage.alarmContent}
            </>
          );
        } else if (pendingMessage.businessScene === 'ALARM_SCREEN') {
          message = '告警盯屏';
          description = pendingMessage.alarmContent;
        }
        if (description === null) {
          return;
        }
        notification.warning({
          duration: 5,
          message,
          description,
        });
      });
      dispatch(subscriptionsSliceActions.removeNotificationsMessagesByIds(pendingIds));
    }
  }, [dispatch, pendingIds, pendingMessages]);

  return null;
}
