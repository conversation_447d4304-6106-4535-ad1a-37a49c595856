import React from 'react';

export type LayoutFabricType<P = any> = React.FunctionComponent<P>;
export type LayoutFabricVariant /* 自定义顶部菜单元素，类似于用户头像 */ =
  | 'head-menu-item'
  | 'side-bar'
  | 'breadcrumb';
export type LayoutProvided = {
  SideBar?: LayoutFabricType;
  selectedMenuCodes: string[];
};
export type LayoutFabricOptions = {
  variant: LayoutFabricVariant;

  /**
   * Prepare `props` for `Fabric` component
   */
  prepare?: (provided: LayoutProvided) => any;
};

/**
 * 管理需要在 Layout 上实现的自定义 UI 组件
 */
class LayoutRegistry {
  protected fabric: Record<string, [LayoutFabricType, LayoutFabricOptions]> = {};

  register<FabricProps>(
    key: string,
    fabric: LayoutFabricType<FabricProps>,
    options: LayoutFabricOptions
  ) {
    this.fabric[key] = [fabric, options];
  }

  unregister(key: string) {
    delete this.fabric[key];
  }

  getFabricByKey(key: string) {
    return this.fabric[key];
  }

  getFabricsByVariant(variant: LayoutFabricVariant) {
    const fabrics: [string, [LayoutFabricType, LayoutFabricOptions]][] = [];
    Object.keys(this.fabric).forEach(key => {
      const _fabric = this.fabric[key];
      if (_fabric[1].variant === variant) {
        fabrics.push([key, _fabric]);
      }
    });

    return fabrics;
  }
}

export const layoutRegistry = new LayoutRegistry();
