import BaseLayout from 'antd/es/layout';
import classNames from 'classnames';
import React from 'react';
import { useSelector } from 'react-redux';
import { Link, useParams } from 'react-router-dom';
import { useLatest } from 'react-use';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import {
  selectMyMenus,
  selectMyResourceCodes,
  selectShouldAlarmPop,
} from '@manyun/auth-hub.state.user';
import { Breadcrumb } from '@manyun/base-ui.ui.breadcrumb';
import { Result } from '@manyun/base-ui.ui.result';
import { Typography } from '@manyun/base-ui.ui.typography';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { useLayout } from '@manyun/dc-brain.context.layout';
import type { Breadcrumb as LayoutBreadcrumb } from '@manyun/dc-brain.context.layout';
import type { PermissionJSON } from '@manyun/iam.model.permission';
import { getSpaceGuid } from '@manyun/resource-hub.util.space-guid';

import styles from './layout.module.less';
import type { NotificationVariant } from './notifications-subscription';
import { NotificationsSubscription } from './notifications-subscription';
import { layoutRegistry } from './registry/layout-registry';

type MaybeURLParams = {
  idc?: string;
  block?: string;
  room?: string;
};

export type LayoutContentProps = {
  style?: React.CSSProperties;
  contentStyle?: React.CSSProperties;
  fixedContent?: boolean;
  fluid?: boolean;
  /** 是否开启通知消息订阅 */
  subscribeNotifications?: boolean;
  allowNotificationVariants?: NotificationVariant[];
  /**
   * 使用 `page code` 自动计算面包屑导航数据
   */
  pageCode?: string;
  /**
   * 使用 pathname计算面包屑导航，页面权限配置时需配置url，和pageCode，二传一
   */
  pathname?: string;
  /**
   * 用于自定义内容区上方的面包屑导航（需要 `memoize`）
   */
  composeBreadcrumbs?: (breadcrumbs: LayoutBreadcrumb[]) => LayoutBreadcrumb[];
  /**
   * 是否需要鉴权，不需要则展示，首页和403页面需要展示页面
   */
  pageAuthorizationCheck?: boolean;
  children: React.ReactNode;
};

export function LayoutContent({
  style,
  contentStyle,
  fixedContent,
  fluid,
  subscribeNotifications,
  allowNotificationVariants,
  pageCode,
  pathname,
  composeBreadcrumbs,
  pageAuthorizationCheck = true,
  children,
}: LayoutContentProps) {
  const { entities, idsMapper } = useSelector(selectMyMenus);
  const shouldAlarmPop = useSelector(selectShouldAlarmPop);
  const { setSelectedMenuCodes, selectedMenuCodes } = useLayout();

  const urlParams = useParams<MaybeURLParams>();
  const myResourceCodes = useSelector(selectMyResourceCodes);
  const { authorized: isResourceAuthorized, resourceCode } = checkResourcesAuthorization(
    urlParams,
    myResourceCodes
  );
  const [, { checkCode }] = useAuthorized();

  const _pageCode = React.useMemo(() => {
    if (pageCode) {
      return pageCode;
    }
    if (pathname) {
      const entitiesValues = Object.values(entities);
      const page = entitiesValues.find(
        item => item.routePath && pathname.includes(item.routePath) && item.type === 'PAGE'
      );
      return page?.code;
    }

    return undefined;
  }, [entities, pageCode, pathname]);

  const isPageAuthorized = checkPageAuthorization(pageAuthorizationCheck, _pageCode, checkCode);
  const hasPagePermission = isResourceAuthorized && isPageAuthorized;

  const [customBreadcrumbItems, setCustomBreadcrumbItems] = React.useState<React.ReactNode[]>([]);

  React.useEffect(() => {
    const headMenuItems = layoutRegistry.getFabricsByVariant('breadcrumb');

    const _nodes = headMenuItems.map(([key, [Fabric, { prepare }]]) => {
      const _fabricProps = prepare?.({ selectedMenuCodes });
      const { visible, showSeparator = true } = _fabricProps;

      if (visible) {
        return (
          <Breadcrumb.Item key={key} separator={showSeparator ? '/' : null}>
            <Fabric key={key} {..._fabricProps} />
          </Breadcrumb.Item>
        );
      }
      return null;
    });
    setCustomBreadcrumbItems(_nodes);
  }, [selectedMenuCodes]);

  const loopBreadcrumbs = React.useCallback(
    (entity: PermissionJSON | undefined, breadcrumbs: LayoutBreadcrumb[]): LayoutBreadcrumb[] => {
      if (!entity?.parentId) {
        return breadcrumbs;
      }
      const parentEntity = entities[idsMapper[entity.parentId]] as PermissionJSON | undefined;
      if (!parentEntity) {
        return breadcrumbs;
      }
      if (parentEntity.code === 'menuRoot') {
        return breadcrumbs;
      }
      return loopBreadcrumbs(parentEntity, [
        {
          key: parentEntity.code,
          text: parentEntity.name,
        },
        ...breadcrumbs,
      ]);
    },
    [entities, idsMapper]
  );

  const breadcrumbs: LayoutBreadcrumb[] = React.useMemo(() => {
    if (!_pageCode) {
      return [];
    }

    // `pageCode` is possibliy not in `entities`
    const page = entities[_pageCode] as PermissionJSON | undefined;
    if (!page?.parentId) {
      return [];
    }

    return loopBreadcrumbs(page, []);
  }, [entities, loopBreadcrumbs, _pageCode]);

  const setSelectedMenuCodesRef = useLatest(setSelectedMenuCodes);
  React.useEffect(() => {
    setSelectedMenuCodesRef.current(breadcrumbs.map(({ key }) => key!.toString()));
  }, [breadcrumbs, setSelectedMenuCodesRef]);

  const finalBreadcrumbs = composeBreadcrumbs ? composeBreadcrumbs(breadcrumbs) : breadcrumbs;

  return (
    <BaseLayout.Content
      style={style}
      className={classNames(
        styles.content,
        fixedContent && styles.contentFixed,
        fluid && styles.contentFluid,
        finalBreadcrumbs.length <= 0 && styles.contentWithoutBreadcrumbs
      )}
      prefixCls="manyun-layout-content"
    >
      {subscribeNotifications && shouldAlarmPop && (
        <NotificationsSubscription allowNotificationVariants={allowNotificationVariants} />
      )}

      {finalBreadcrumbs.length > 0 && (
        <Breadcrumb className={styles.breadcrumbs}>
          {customBreadcrumbItems}
          {finalBreadcrumbs
            .filter(bread => bread.text)
            .map(breadcrumb => (
              <Breadcrumb.Item key={breadcrumb.key}>
                {typeof breadcrumb?.onClick === 'function' ? (
                  <Typography.Text
                    style={{
                      cursor: 'pointer',
                    }}
                    onClick={() => {
                      if (breadcrumb?.onClick) {
                        breadcrumb.onClick();
                      }
                    }}
                  >
                    {breadcrumb.text}
                  </Typography.Text>
                ) : breadcrumb.linkTo ? (
                  <Link to={breadcrumb.linkTo}>{breadcrumb.text}</Link>
                ) : (
                  breadcrumb.text
                )}
              </Breadcrumb.Item>
            ))}
        </Breadcrumb>
      )}
      <div style={contentStyle} className={styles.contentContainer}>
        {hasPagePermission ? (
          children
        ) : (
          <UnAuthorized
            isPageAuthorized={isPageAuthorized}
            pageCode={_pageCode ?? null}
            isResourceAuthorized={isResourceAuthorized}
            resourceCode={resourceCode}
          />
        )}
      </div>
    </BaseLayout.Content>
  );
}

function checkResourcesAuthorization(resourcesToCheck: MaybeURLParams, myResourceCodes: string[]) {
  if (env.GLOBAL_RESOURCE_AUTHORIZATION_CHECK !== 'enabled') {
    return { authorized: true, resourceCode: null };
  }
  if (!resourcesToCheck.idc) {
    // If no `idc` to check, it means no need to check.
    return { authorized: true, resourceCode: null };
  }
  let spaceGuid = getSpaceGuid(resourcesToCheck.idc, resourcesToCheck.idc)!;
  if (resourcesToCheck.block) {
    spaceGuid = getSpaceGuid(resourcesToCheck.idc, resourcesToCheck.block)!;
  }
  const authorized = myResourceCodes.includes(spaceGuid);

  return { authorized, resourceCode: spaceGuid };
}

function checkPageAuthorization(
  pageAuthorizationCheck: boolean,
  pageCode: string | undefined,
  checkCode: (_checkByCode: string) => boolean
) {
  if (!pageAuthorizationCheck) {
    return true;
  }
  if (env.GLOBAL_PAGE_AUTHORIZATION_CHECK !== 'enabled') {
    return true;
  }
  if (!pageCode) {
    return true;
  }
  const authorized = checkCode(pageCode);

  return authorized;
}

type UnAuthorizedProps = {
  isPageAuthorized: boolean;
  pageCode: string | null;
  isResourceAuthorized: boolean;
  resourceCode: string | null;
};

function UnAuthorized({
  isPageAuthorized,
  pageCode,
  isResourceAuthorized,
  resourceCode,
}: UnAuthorizedProps) {
  let message = '';
  if (!isPageAuthorized) {
    message = '抱歉，你无权查看此页面！';
    if (pageCode) {
      message += `(Page Code: ${pageCode})`;
    }
  } else if (!isResourceAuthorized) {
    message = `抱歉，你缺少资源权限：${resourceCode}`;
  }

  return <Result status="403" title="403" subTitle={message} />;
}
