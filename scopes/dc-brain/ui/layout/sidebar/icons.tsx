import React from 'react';

import { AlertFilled, ControlFilled, HddFilled } from '@ant-design/icons';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';

export type IconConfig = {
  icon: React.ReactElement;
};

export const iconList: Record<string, IconConfig> = {
  pointdifftool: {
    icon: (
      <ControlFilled
        style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: '14px', marginLeft: '8px' }}
      />
    ),
  },
  devicedifftool: {
    icon: (
      <HddFilled
        style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: '14px', marginLeft: '8px' }}
      />
    ),
  },
  roomview: {
    icon: (
      <AlertFilled
        style={{ color: `var(--${prefixCls}-primary-color)`, fontSize: '14px', marginLeft: '8px' }}
      />
    ),
  },
};
