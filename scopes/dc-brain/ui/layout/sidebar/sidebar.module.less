@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.sideDrawer {
  top: 50px;
  :global(.manyun-drawer-header) {
    display: none;
  }
  :global(.manyun-drawer-body) {
    padding: 0;
  }
  :global(.manyun-drawer-content) {
    overflow: visible;
  }
}

.container {
  height: 100%;
  > :global(.manyun-tabs-nav) {
    > :global(.manyun-tabs-nav-wrap) {
      > :global(.manyun-tabs-nav-list) {
        > :global(.manyun-tabs-tab) {
          width: 76px;
          height: 72px;
          font-size: 12px;
          margin-top: 0;
          &:hover {
            background-color: @primary-1;
          }
        }
        > :global(.manyun-tabs-tab-active) {
          border-right-color: @border-color-split;
          margin-top: 0;
        }
      }
    }
  }

  > :global(.manyun-tabs-content-holder) {
    > :global(.manyun-tabs-content) {
      > :global(.manyun-tabs-tabpane) {
        padding-left: 0px;
      }
    }
  }
}
