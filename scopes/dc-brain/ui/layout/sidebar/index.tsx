import React from 'react';

import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { ToggleHandle } from '@manyun/base-ui.ui.toggle-handle';

import { useLayout } from '@manyun/dc-brain.context.layout';
import type { LayoutFabricType } from '@manyun/dc-brain.ui.layout';

import { iconList } from './icons';
import styles from './sidebar.module.less';

type SideBarTabItem = {
  Content: LayoutFabricType;
  title: string;
  key: string;
};

export function SideBar({ tabList }: { tabList: SideBarTabItem[] }) {
  const { sideBarVisible, setSideBarVisible, sideBarTab, setSideBarTab } = useLayout();

  const onChange = (value: string) => {
    setSideBarTab(value);
  };

  const onClose = () => {
    setSideBarVisible(false);
    setSideBarTab('roomview');
  };
  return (
    <>
      <Drawer
        className={styles.sideDrawer}
        placement="left"
        contentWrapperStyle={{ boxShadow: 'none', width: 'auto' }}
        open={sideBarVisible}
        onClose={onClose}
      >
        <Tabs
          className={styles.container}
          type="card"
          tabPosition="left"
          activeKey={sideBarTab}
          items={tabList.map(item => {
            const { Content, title, key } = item;
            const icon = iconList[key].icon;
            return {
              key,
              label: (
                <Space direction="vertical" size={6}>
                  {icon}
                  {title}
                </Space>
              ),
              children: <Content title={title} />,
            };
          })}
          onChange={onChange}
        />

        {sideBarVisible && (
          <ToggleHandle
            style={
              {
                '--toggle-handle-bg': 'var(--manyun-primary-color)',
                '--toggle-handle-color': 'var(--manyun-primary-1)',
              } as React.CSSProperties
            }
            visible
            position={() => ({
              right: -16,
            })}
            arrowType={() => 'left'}
            onClick={onClose}
          />
        )}
      </Drawer>
      {sideBarVisible || (
        <ToggleHandle
          style={
            {
              position: 'fixed',
              '--toggle-handle-bg': 'var(--manyun-primary-color)',
              '--toggle-handle-color': 'var(--manyun-primary-1)',
              zIndex: 1,
            } as React.CSSProperties
          }
          visible
          position={() => ({
            left: 0,
          })}
          arrowType={() => 'right'}
          onClick={() => setSideBarVisible(true)}
        />
      )}
    </>
  );
}
