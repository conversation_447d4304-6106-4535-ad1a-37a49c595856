const MENUS_KEY = 'DC_BASE_COLLECT_MENUS';

type MenuTreeNode = {
  metaName: string;
  metaCode: string;
  remarks: string | null;
  children?: MenuTreeNode[];
  collectStatus?: boolean;
};

export function saveState(item: MenuTreeNode) {
  try {
    const collections = getState() || {};
    item.collectStatus = true;
    collections[item.metaCode] = item;
    localStorage.setItem(MENUS_KEY, JSON.stringify(collections));
  } catch (error) {}
}

export function saveStateALL(obj: any) {
  try {
    localStorage.setItem(MENUS_KEY, JSON.stringify(obj));
  } catch (error) {}
}

export function getState() {
  try {
    const collections = localStorage.getItem(MENUS_KEY);
    if (collections === null) {
      return {};
    }
    return JSON.parse(collections);
  } catch (error) {}
}

export function deleteState(key: string) {
  try {
    const collections = getState() || {};
    delete collections[key];
    localStorage.setItem(MENUS_KEY, JSON.stringify(collections));
  } catch (error) {}
}

export function clearUiState() {
  window.localStorage.removeItem(MENUS_KEY);
}
