import { generatePath } from 'react-router-dom';

export const ATTENDANCE_STATISTICS_MONTHLY_AND_DAILY =
  '/page/staff-shift-arrangement/staff/attendance-records/:byFunc';
export const generateAttendanceStatisticsLocation = ({ byFunc }: { byFunc: 'byMonth' | 'byDay' }) =>
  ATTENDANCE_STATISTICS_MONTHLY_AND_DAILY.replace(':byFunc', byFunc);

/**班次 */
export const SHIFTS_ROUTE_PATH = '/page/staff-shift-arrangement/shifts';
export const SHIFTS_ROUTE_AUTH_CODE = 'page_shifts';

export const SHIFT_SYS_MANAGEMENT_LIST = '/page/staff-shift-arrangement/shift-systems';
export const DUTY_GROUP_MANAGEMENT_LIST = '/page/staff-shift-arrangement/teams';
export const GROUP_SCHEDULE_MANAGEMENT_LIST = '/page/staff-shift-arrangement/shift-rules';
export const ATT_GROUP_MANAGEMENT_LIST = '/page/staff-shift-arrangement/attendance-groups';
export const ATT_GROUP_MANAGEMENT_CREATE = '/page/staff-shift-arrangement/attendance-groups/new';
export const ATT_GROUP_MANAGEMENT_EDIT = '/page/staff-shift-arrangement/attendance-group/:id/edit';
export const ATT_GROUP_MANAGEMENT_SCHEDULE =
  '/page/staff-shift-arrangement/attendance-groups/:id/shift-schedule/new';
export const ATT_GROUP_MANAGEMENT_SCHEDULE_DETAIL =
  '/page/staff-shift-arrangement/attendance-groups/:id/shift-schedule';
export const ATT_GROUP_MANAGEMENT_SCHEDULE_EDIT =
  '/page/staff-shift-arrangement/attendance-groups/:id/shift-schedule/edit';

export const generateAttGroupManagementScheduleDetailRoutePath = ({
  id,
  groupName,
}: {
  id: string;
  groupName: string;
}) => {
  return `${generatePath(ATT_GROUP_MANAGEMENT_SCHEDULE_DETAIL, { id })}?groupName=${groupName}`;
};

/**原始记录 */
export const PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH =
  '/page/staff-shift-arrangement/staff/clock-on-off-records/history';
export const PUNCH_CLOCK_IN_RECORDS_ROUTE_AUTH_CODE = 'page_staff-records';

/**考勤补卡列表页 */
export const SUPPLY_CHECKS_ROUTE_PATH = '/page/supply-check/list';
export const SUPPLY_CHECKS_ROUTE_AUTH_CODE = 'page_supply-check';
/**补卡详情 */
export const SUPPLY_CHECK_DETAIL_ROUTE_PATH = '/page/supply-check/:id/detail';
export const SUPPLY_CHECK_DETAIL_ROUTE_AUTH_CODE = 'page_supply-check-info';

/**创建补卡 */
export const SUPPLY_CHECK_NEW_ROUTE_PATH = '/page/supply-check/create';
export const SUPPLY_CHECK_NEW_ROUTE_AUTH_CODE = 'page_supply-check-create';

export const ALTER_INFO = '/page/alter-info/list';
export const ALTER_INFO_NEW_ROUTE_PATH = '/page/alter-info/create';
export const ALTER_INFO_DETAIL = '/page/alter-info/:bizId/detail';

/**加班记录 */
export const OT_REQUESTS_ROUTE_PATH = '/page/hrm/ot-requests';
export const OT_REQUESTS_ROUTE_AUTH_CODE = 'page-ot_requests';

//假期余额路由、页面权限编码
export const HOLIDAY_BALANCES_ROUTE_PATH = '/page/hrm/holiday-balances';
export const HOLIDAY_BALANCES_ROUTE_AUTH_CODE = 'page_holiday-balances';
// 假期余额测算
export const HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH = '/page/hrm/holiday-balance-calculate';
export const HOLIDAY_BALANCE_CALCULATE_ROUTE_AUTH_CODE = 'page_hrm-cal-holiday-balance';

//用户调休余额明细路由、页面权限编码
export const BREAK_OFF_BALANCE_ROUTE_PATH = '/page/hrm/break-off-balance/:id';
export const BREAK_OFF_BALANCE_ROUTE_AUTH_CODE = 'page_break-off-balance';
export type BreakOffBalacneRouteParams = {
  id: string /** 用户id **/;
};
export const generateBreakOffBalanceRoutePath = (params: BreakOffBalacneRouteParams) =>
  generatePath(BREAK_OFF_BALANCE_ROUTE_PATH, params);

//外勤申请路由、页面权限编码
export const GO_OUT_REQUEST_CREATOR_ROUTE_PATH = '/page/hrm/go-out-request-creator';
export const GO_OUT_REQUEST_CREATOR_ROUTE_AUTH_CODE = 'page_go-out-request-creator';

export const generateAlterInfoDetaillocation = ({ id }: { id: string }) => ({
  pathname: ALTER_INFO_DETAIL.replace(':bizId', id),
});

export const generateSupplyCheckDetaillocation = (params: { id: string }) =>
  generatePath(SUPPLY_CHECK_DETAIL_ROUTE_PATH, params);

/**试用期个人绩效页**/
export const PP_PERFORMANCES_ROUTE_PATH = '/page/hrm/pp-user-performances';
export const PP_PERFORMANCE_ROUTE_AUTH_CODE = 'page_hrm-pp-user-performances';

/**试用期目标创建、编辑页**/
export const PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH =
  '/page/hrm/pp-performance-goals-setting/:id/:key';
export const PP_PERFORMANCE_GOALS_SETTING_ROUTE_AUTH_CODE = 'page_hrm-pp-goals-setting';

export type GoalSettingParams = {
  /**绩效id为null时传 ‘create' */
  id: string;
  /** 绩效的rowKey */
  key: string;
};

export const generatePerformanceGoalsSetting = (params: GoalSettingParams) =>
  generatePath(PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH, params);

/**我团队的绩效页***/
export const TEAM_PP_PERFORMANCES_ROUTE_PATH = '/page/hrm/team-performances/test';
export const TEAM_PP_PERFORMANCES_ROUTE_AUTH_CODE = 'page_hrm-pp-team-performances';

/**试用期绩效详情页(个人版)**/
export const PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH =
  '/page/hrm/pp-performance-detail/:type/:id';
export const PP_PERFORMANCE_DETAIL_ROUTE_AUTH_CODE = 'page_hrm-pp-performance-detail';

/**试用期绩效详情页(团队版)**/
export const TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH =
  '/page/hrm/team-pp-performance-detail/:type/:id';
export const TEAM_PP_PERFORMANCE_DETAIL_ROUTE_AUTH_CODE = 'page_hrm-pp-team-performance-detail';

export type EvaluationDetailParams = {
  /**
   * 绩效id
   */
  id: string;
  type: 'byGoal' | 'byEval';
};

export const generateUserPerformanceDetail = (params: EvaluationDetailParams) =>
  generatePath(PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH, params);

export const generateTestPerformanceDetail = (params: EvaluationDetailParams) =>
  generatePath(TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH, params);

/**年度绩效 - 指标库列表页 */
export const ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH = '/page/hrm/annual-performance-objectives';
export const ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-objectives';

/**年度绩效 - 指标库新增页 */
export const ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH =
  '/page/hrm/annual-performance-objective-create/:way';
export const ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-objective-creator';

/**年度绩效 - 指标库编辑页 */
export const ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_PATH =
  '/page/hrm/annual-performance-objective-editor/:way/:id';
export const ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-objective-editor';

export type AnnualPerformanceObjectiveEditParams = {
  way: 'normal' | 'custom';
  id: string;
};
export const generateAnnualPerformanceObjectiveCreate = (params: { way: string }) =>
  generatePath(ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH, params);

export const generateAnnualPerformanceObjectiveEdit = (
  params: AnnualPerformanceObjectiveEditParams
) => generatePath(ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_PATH, params);

/**年度绩效 - 日常评分列表 */
export const PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH =
  '/page/hrm/daily-performance-grade-records';
export const PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_AUTH_CODE =
  'page_hrm-daily-performance-grade-records';

/**年度绩效 - 日常评分创建 */
export const PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH = '/page/hrm/daily-performance-grade-create';
export const PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_AUTH_CODE =
  'page_hrm-daily-performance-grade-creator';

/**年度绩效 - 日常评分编辑 */
export const PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_PATH =
  '/page/hrm/daily-performance-grade-editor/:id';
export const PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_AUTH_CODE =
  'page_hrm-daily-performance-grade-editor';
export type PerformanceDailyGradeEditParams = {
  id: string;
};
export const generatePerformanceDailyGradeEdit = (params: PerformanceDailyGradeEditParams) =>
  generatePath(PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_PATH, params);

/**年度绩效 - 年度考核计划列表 */
export const ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH = '/page/hrm/annual-performance-plans';
export const ANNUAL_PERFORMANCE_PLANS_ROUTE_AUTH_CODE = 'page_hrm-annual-performance-plans';

/**年度绩效 - 年度考核计划创建 */
export const ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH =
  '/page/hrm/annual-performance-plan-creator';
export const ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-plan-creator';

/**年度绩效 - 年度考核计划修改 */
export const ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH =
  '/page/hrm/annual-performance-plan-editor/:id';
export const ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-plan-editor';

export const ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH =
  '/page/hrm/annual-performance-plan-objectives/:id';
export const ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-plan-objectives-detail';

export type AnnualPerformancePlanEditParams = {
  id: string;
};
export const generateAnnualPerformancePlanEdit = (params: AnnualPerformancePlanEditParams) =>
  generatePath(ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH, params);

export const generateAnnualPerformancePlanObjectiveDetail = (
  params: AnnualPerformancePlanEditParams
) => generatePath(ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH, params);

/**年度绩效 - 年度目标预览 */
export const ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH =
  '/page/hrm/annual-performance-objectives-preview';
export const ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-objectives-preview';

/**年度绩效 - 个人年度绩效页 */
export const USER_ANNUAL_PERFORMANCES_ROUTE_PATH = '/page/hrm/user-annual-performances';
export const USER_ANNUAL_PERFORMANCES_ROUTE_AUTH_CODE = 'page_hrm-user-annual-performances';

/**年度绩效 - 年度目标详情(个人版) */
export const USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH =
  '/page/hrm/user-annual-performance-objectives/:user/:planid/:position/:id/:key';
export const USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_AUTH_CODE =
  'page_hrm-user-annual-performance-objectives-detail';

/**年度绩效 - 年度目标详情(团队版) */
export const TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH =
  '/page/hrm/team-annual-performance-objectives/:user/:planid/:position/:id/:key';
export const TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_AUTH_CODE =
  'page_hrm-team-annual-performance-objectives-detail';

export type AnnualPerformanceObjectivesDetailParams = {
  /**
   * 用户id
   */
  user: string;
  /**
   * 关联的计划id
   */
  planid: string;
  /**
   * 绩效id为null时传 ‘create'
   */
  id: string;
  /**
   *  绩效的rowKey
   * */
  key: string;
  /**
   * 考核岗位
   */
  position: string;
};

/**
 *
 * @param type  个人/团队
 * @param params  router params
 * @returns
 */
export const generateAnnualPerformanceObjectivesDetail = (
  type: 'user' | 'team',
  params: AnnualPerformanceObjectivesDetailParams
) =>
  type === 'user'
    ? generatePath(USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH, params)
    : generatePath(TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH, params);

/**年度绩效 - 年度考核详情(个人版) */
export const USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH =
  '/page/hrm/user-annual-performance-detail/:year/:position/:user/:period/:id';
export const USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_AUTH_CODE =
  'page_hrm-user-annual-performance-detail';

/**年度绩效 - 年度考核详情(团队版) */
export const TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH =
  '/page/hrm/team-annual-performance-detail/:year/:position/:period/:user/:id';
export const TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_AUTH_CODE =
  'page_hrm-team-annual-performance-detail';

export type AnnualPerformanceDetailParams = {
  year: string;
  position: string;
  user: string;
  period: string;
  /**
   * 绩效ID - 用于锚定选中
   */
  id: string;
};

export const generateAnnualPerformanceDetail = (
  type: 'user' | 'team',
  params: AnnualPerformanceDetailParams
) =>
  type === 'user'
    ? generatePath(USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH, params)
    : generatePath(TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH, params);

export const TEAM_ANNUAL_PERFORMANCES_ROUTE_PATH = '/page/hrm/team-performances/annual';
export const TEAM_ANNUAL_PERFORMANCES_ROUTE_AUTH_CODE = 'page_hrm-team-annual-performances';

export type TeamPerformancesParams = {
  type: 'test' | 'annual';
};
export const TEAM_PERFORMANCES_ROUTE_PATH = '/page/hrm/team-performances/:type';

export const generateTeamPerformancesLocation = (params: TeamPerformancesParams) =>
  generatePath(TEAM_PERFORMANCES_ROUTE_PATH, params);

export const ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH = '/page/hrm/annual-performance-statistics';
export const ANNUAL_PERFORMANCES_STATISTICS_ROUTE_AUTH_CODE =
  'page_hrm-annual-performance-statistics';

export const SCHEDULE_STAFF_STATISTIC_ROUTE_PATH = '/page/hrm/schedule-staff-statistic';
export const SCHEDULE_STAFF_STATISTIC_ROUTE_AUTH_CODE = 'page_hrm-scheduler-staffs';

export const CALENDAR_CONFIGURE_ROUTE_PATH = '/page/hrm/calendar-configure';
export const CALENDAR_CONFIGURE_ROUTE_AUTH_CODE = 'page_calendar-configure';

export const HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH = '/page/hrm/performance-block-evaluation';
export const HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_AUTH_CODE =
  'page_hrm-performance-block-evaluation';

export const HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH = '/page/hrm/performance-red-line-records';
export const HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_AUTH_CODE = 'page_hrm-red-line-records';
export const HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH = '/page/hrm/performance-red-line-creator';
export const HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_AUTH_CODE = 'page_hrm-red-line-creator';
export const HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_PATH =
  '/page/hrm/performance-red-line-editor/:id';
export const HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_AUTH_CODE = 'page_hrm-red-line-editor';
export const generateEditRedLineRecord = (params: { id: string }) =>
  generatePath(HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_PATH, params);

export const HRM_IDC_MATCH_DEPART_ROUTE_PATH = '/page/hrm/idc-match-depart';
export const HRM_IDC_MATCH_DEPART_ROUTE_AUTH_CODE = 'page_hrm-idc-match-depart';
export const HRM_STAFF_INFORMATION_LIST_ROUTE_PATH = '/page/hrm/staff-information-list';
export const HRM_STAFF_INFORMATION_LIST_ROUTE_AUTH_CODE = 'page_hrm-staff-information-list';
export const HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH = '/page/hrm/part-time-jobs-manage';
export const HRM_PART_TIME_JOBS_MANAGE_ROUTE_AUTH_CODE = 'page_hrm-part-time-jobs-manage';

export const HRM_STAFF_PROFILE_LIST_ROUTE_PATH = '/page/hrm/staff-profile-list';
export const HRM_STAFF_PROFILE_LIST_AUTH_CODE = 'page_hrm-staff-profile-list';
export const HRM_STAFF_CERTIFICATION_STANDARDS_LIST_ROUTE_PATH =
  '/page/hrm/staff-certification-standards-list';
export const HRM_STAFF_CERTIFICATION_STANDARDS_LIST_AUTH_CODE =
  'page_hrm-staff-certification-standards-list';
export const HRM_STAFF_CERTIFICATION_LIST_ROUTE_PATH = '/page/hrm/staff-certifications-list';
export const HRM_STAFF_CERTIFICATION_LIST_AUTH_CODE = 'page_hrm-staff-certifications-list';

/**end */

export const HRM_ROUTER_PATH_AUTH_CODE_MAPPER = {
  [HOLIDAY_BALANCES_ROUTE_PATH]: HOLIDAY_BALANCES_ROUTE_AUTH_CODE,
  [BREAK_OFF_BALANCE_ROUTE_PATH]: BREAK_OFF_BALANCE_ROUTE_AUTH_CODE,
  [GO_OUT_REQUEST_CREATOR_ROUTE_PATH]: GO_OUT_REQUEST_CREATOR_ROUTE_AUTH_CODE,
  [SUPPLY_CHECK_NEW_ROUTE_PATH]: SUPPLY_CHECK_NEW_ROUTE_AUTH_CODE,
  [SUPPLY_CHECKS_ROUTE_PATH]: SUPPLY_CHECKS_ROUTE_AUTH_CODE,
  [SUPPLY_CHECK_DETAIL_ROUTE_PATH]: SUPPLY_CHECK_DETAIL_ROUTE_AUTH_CODE,
  [PUNCH_CLOCK_IN_RECORDS_ROUTE_PATH]: PUNCH_CLOCK_IN_RECORDS_ROUTE_AUTH_CODE,
  [SHIFTS_ROUTE_PATH]: SHIFTS_ROUTE_AUTH_CODE,
  [OT_REQUESTS_ROUTE_PATH]: OT_REQUESTS_ROUTE_AUTH_CODE,
  [PP_PERFORMANCES_ROUTE_PATH]: PP_PERFORMANCE_ROUTE_AUTH_CODE,
  [PP_PERFORMANCE_GOALS_SETTING_ROUTE_PATH]: PP_PERFORMANCE_GOALS_SETTING_ROUTE_AUTH_CODE,
  [TEAM_PP_PERFORMANCES_ROUTE_PATH]: TEAM_PP_PERFORMANCES_ROUTE_AUTH_CODE,
  [PP_PERFORMANCE_EVALUATION_DETAIL_ROUTE_PATH]: PP_PERFORMANCE_DETAIL_ROUTE_AUTH_CODE,
  [TEAM_PP_PERFORMANCE_DETAIL_ROUTE_PATH]: TEAM_PP_PERFORMANCE_DETAIL_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_PATH]: ANNUAL_PERFORMANCE_OBJECTIVES_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_PATH]:
    ANNUAL_PERFORMANCE_OBJECTIVE_CREATE_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_PATH]:
    ANNUAL_PERFORMANCE_OBJECTIVE_EDITOR_ROUTE_AUTH_CODE,
  [PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH]: PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_AUTH_CODE,
  [PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH]: PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_AUTH_CODE,
  [PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_PATH]: PERFORMANCE_DAILY_GRADE_EDIT_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_PLANS_ROUTE_PATH]: ANNUAL_PERFORMANCE_PLANS_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH]: ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_PATH]: ANNUAL_PERFORMANCE_PLAN_EDIT_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH]:
    ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_AUTH_CODE,
  [USER_ANNUAL_PERFORMANCES_ROUTE_PATH]: USER_ANNUAL_PERFORMANCES_ROUTE_AUTH_CODE,
  [USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH]:
    USER_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_AUTH_CODE,
  [USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_PATH]: USER_ANNUAL_PERFORMANCES_DETAIL_ROUTE_AUTH_CODE,
  [TEAM_ANNUAL_PERFORMANCES_ROUTE_PATH]: TEAM_ANNUAL_PERFORMANCES_ROUTE_AUTH_CODE,
  [TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_PATH]:
    TEAM_ANNUAL_PERFORMANCE_OBJECTIVES_DETAIL_ROUTE_AUTH_CODE,
  [TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_PATH]: TEAM_ANNUAL_PERFORMANCE_DETAIL_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_PATH]:
    ANNUAL_PERFORMANCE_PLAN_OBJECTIVE_DETAIL_ROUTE_AUTH_CODE,
  [ANNUAL_PERFORMANCES_STATISTICS_ROUTE_PATH]: ANNUAL_PERFORMANCES_STATISTICS_ROUTE_AUTH_CODE,
  [HOLIDAY_BALANCE_CALCULATE_ROUTE_PATH]: HOLIDAY_BALANCE_CALCULATE_ROUTE_AUTH_CODE,
  [SCHEDULE_STAFF_STATISTIC_ROUTE_PATH]: SCHEDULE_STAFF_STATISTIC_ROUTE_AUTH_CODE,
  [CALENDAR_CONFIGURE_ROUTE_PATH]: CALENDAR_CONFIGURE_ROUTE_AUTH_CODE,
  [HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_PATH]: HRM_PERFORMANCE_BLOCK_EVALUATION_ROUTE_AUTH_CODE,
  [HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_PATH]: HRM_PERFORMANCE_RED_LINE_RECORDS_ROUTE_AUTH_CODE,
  [HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH]: HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_AUTH_CODE,
  [HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_PATH]: HRM_PERFORMANCE_RED_LINE_EDITOR_ROUTE_AUTH_CODE,
  [HRM_IDC_MATCH_DEPART_ROUTE_PATH]: HRM_IDC_MATCH_DEPART_ROUTE_AUTH_CODE,
  [HRM_STAFF_INFORMATION_LIST_ROUTE_PATH]: HRM_STAFF_INFORMATION_LIST_ROUTE_AUTH_CODE,
  [HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH]: HRM_PART_TIME_JOBS_MANAGE_ROUTE_AUTH_CODE,
  [HRM_STAFF_PROFILE_LIST_ROUTE_PATH]: HRM_STAFF_PROFILE_LIST_AUTH_CODE,
  [HRM_STAFF_CERTIFICATION_STANDARDS_LIST_ROUTE_PATH]:
    HRM_STAFF_CERTIFICATION_STANDARDS_LIST_AUTH_CODE,
  [HRM_STAFF_CERTIFICATION_LIST_ROUTE_PATH]: HRM_STAFF_CERTIFICATION_LIST_AUTH_CODE,
} as const;
