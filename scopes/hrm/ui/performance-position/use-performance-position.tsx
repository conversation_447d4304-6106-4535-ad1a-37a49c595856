import { useCallback, useState } from 'react';
import { useMountedState } from 'react-use';

import { message } from '@manyun/base-ui.ui.message';
import { MetaType } from '@manyun/resource-hub.model.metadata';
import { fetchMetaDataByType } from '@manyun/resource-hub.service.fetch-meta-data-by-type';

export const usePerformancePositions = (showDelete?: boolean) => {
  const isMounted = useMountedState();

  const [loading, setLoading] = useState(false);
  const [positions, setPositions] = useState<
    { label: string; value: string; description?: string | null }[]
  >([]);

  const getPerformancePositions = useCallback(
    async (callback?: (positions: { label: string; value: string }[]) => void) => {
      setLoading(true);
      const { error, data } = await fetchMetaDataByType({
        type: MetaType.EVAL_POSITION ?? 'EVAL_POSITION',
        queryDeleted: showDelete,
      });
      if (isMounted()) {
        setLoading(false);
      }
      if (error) {
        message.error(error.message);
        return;
      }
      const options = (data.data ?? []).map(mateData => ({
        label: mateData.name,
        value: mateData.code,
        description: mateData.description,
      }));
      if (isMounted()) {
        setPositions(options);
      }
      callback?.(options);
    },
    [showDelete, isMounted]
  );

  return [
    {
      loading,
      options: positions,
    },
    getPerformancePositions,
  ] as const;
};
