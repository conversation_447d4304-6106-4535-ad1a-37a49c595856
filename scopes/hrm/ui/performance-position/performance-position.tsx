import React, { useEffect } from 'react';
import { useLatest } from 'react-use';

import { RoleFilled } from '@manyun/base-ui.icons';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import type { CheckboxGroupProps } from '@manyun/base-ui.ui.checkbox';
import { List } from '@manyun/base-ui.ui.list';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';

import { usePerformancePositions } from './use-performance-position';

type PerformancePositionSelectProps = {
  /**是否展示软删除的岗位 */
  showDelete?: boolean;
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps;
export const PerformancePositionSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', showDelete, ...props }: PerformancePositionSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ loading, options }, getPerformancePositions] = usePerformancePositions(showDelete);
    useEffect(() => {
      if (trigger === 'onDidMount') {
        getPerformancePositions();
      }
    }, [getPerformancePositions, trigger]);

    return (
      <Select
        ref={ref}
        loading={loading}
        options={options}
        showSearch
        {...props}
        onFocus={() => {
          if (trigger === 'onFocus') {
            getPerformancePositions();
          }
        }}
      />
    );
  }
);
PerformancePositionSelect.displayName = 'PerformancePositionSelect';

export type PerformancePositionCheckboxGroupProps = {
  /**是否展示软删除的岗位 */
  showDelete?: boolean;
  onDataChange?: (value: string[]) => void;
} & CheckboxGroupProps;

export const PerformancePositionCheckboxGroup = ({
  showDelete,
  onDataChange,
  ...restProps
}: PerformancePositionCheckboxGroupProps) => {
  const [{ options }, getPerformancePositions] = usePerformancePositions(showDelete);

  const onDataChangeRef = useLatest(onDataChange);

  useEffect(() => {
    if (!restProps.options) {
      getPerformancePositions(positions => {
        onDataChangeRef.current?.(positions.map(({ value }) => value));
      });
    }
  }, [getPerformancePositions, onDataChangeRef, restProps.options]);

  return (
    <Checkbox.Group {...restProps}>
      <Space wrap>
        {options.map(option => (
          <Checkbox key={option.value} value={option.value}>
            {option.label}
          </Checkbox>
        ))}
      </Space>
    </Checkbox.Group>
  );
};

export const PerformancePositionTabs = ({
  value,
  onChange,
  className,
}: {
  value?: string;
  onChange?: (value: string) => void;
  className?: string;
}) => {
  const [{ options }, getPerformancePositions] = usePerformancePositions();
  const onChangeRef = useLatest(onChange);

  useEffect(() => {
    getPerformancePositions(positions => {
      if (positions[0]) {
        onChangeRef.current?.(positions[0].value);
      }
    });
  }, [getPerformancePositions, onChangeRef]);

  return (
    <Tabs
      className={className}
      items={options.map(opt => ({ ...opt, key: opt.value }))}
      activeKey={value}
      onChange={onChange}
    />
  );
};

const colors = ['#016EFF', '#21B1FF', '#FF835C', '#00DDD3', '#5FE6F9', '#FFB707'];

export const PerformancePositionList = ({ onChange }: { onChange?: (value: string) => void }) => {
  const [{ options }, getPerformancePositions] = usePerformancePositions();

  useEffect(() => {
    getPerformancePositions();
  }, [getPerformancePositions]);

  return (
    <List
      grid={{
        gutter: 24,
        column: 2,
      }}
      dataSource={options}
      renderItem={(item, index) => {
        return (
          <List.Item
            style={{ cursor: 'pointer' }}
            onClick={() => {
              onChange?.(item.value);
            }}
          >
            <Card>
              <Space size="large">
                <RoleFilled style={{ fontSize: '56px', color: colors[index % 6] }} />
                <Space style={{ width: '100%' }} direction="vertical">
                  <Typography.Title style={{ marginBottom: 0 }} level={5}>
                    {item.label}
                  </Typography.Title>
                  <Typography.Text type="secondary">{item.description}</Typography.Text>
                </Space>
              </Space>
            </Card>
          </List.Item>
        );
      }}
    />
  );
};
