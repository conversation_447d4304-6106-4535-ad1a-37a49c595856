import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { deleteUserCertificate } from '@manyun/hrm.service.delete-user-certificate';

import pdfPng from './assets/pdf.png';
import styles from './certificate-preview-upload.module.less';

export type CertificatePreviewUploadProps = {
  id: number;
  userId: number;
  fileId: number;
  fileType: string;
  isAuthEdit: boolean;
  fetchCertList?: Function;
  style?: Record<string, any>;
  itemRender?: Function;
  iconRender?: Function;
  showUploadList?: Record<string, any>;
};

type CertUploadType = Partial<McUploadFile & { url: string }>[];

export function CertificatePreviewUpload({
  id,
  userId,
  fileId,
  fileType,
  isAuthEdit,
  fetchCertList,
  style,
  ...rest
}: CertificatePreviewUploadProps) {
  const [fileList, setFileList] = useState<CertUploadType>([]);
  const [deleteConfirmLoading, setDeleteConfirmLoading] = useState<boolean>(false);

  useEffect(() => {
    if (fileId) {
      const file = McUploadFile.fromApiObject({
        fileFormat: fileType as string,
        filePath: String(fileId),
        id: fileId,
        fileType: null,
        fileName: '证书图片',
        fileSize: 0,
        gmtCreate: null,
        gmtModified: null,
        targetId: null,
        targetType: null,
        uploadBy: 0,
        uploadByName: '',
        uploadTime: '',
      });

      setFileList([{ ...file, url: file.ext === '.pdf' ? pdfPng : file.src, src: file.src }]);
    }
  }, [fileId, fileType]);

  const _deleteUserCertificate = async () => {
    setDeleteConfirmLoading(true);

    const { error } = await deleteUserCertificate({
      userId: userId,
      id: id,
    });

    setDeleteConfirmLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }

    message.success('删除成功');
    fetchCertList?.();
  };

  const onHandleDelete = () => {
    Modal.confirm({
      title: '确认删除该资质证书？',
      okText: '确定删除',
      cancelText: '取消',
      okButtonProps: { loading: deleteConfirmLoading },
      onOk: close => {
        return new Promise(async (res, rej) => {
          await _deleteUserCertificate();
          close();
        }).catch(e => message.error(e.message));
      },
    });
  };

  return (
    <div
      className={fileType === '.pdf' ? styles.certificatePreviewUploadCover : undefined}
      style={style}
    >
      <Upload
        listType="picture-card"
        fileList={fileList}
        showUploadList={
          isAuthEdit
            ? { showRemoveIcon: true, showDownloadIcon: true }
            : { showRemoveIcon: false, showPreviewIcon: true }
        }
        onRemove={onHandleDelete}
        {...rest}
      />
    </div>
  );
}
