import React from 'react';

import { Link } from '@manyun/dc-brain.navigation.link';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import { generateAnnualPerformanceObjectivesDetail } from '@manyun/hrm.route.hrm-routes';

export type ChangePerformanceTargetLinkProps = {
  selectedPerformance: PerformanceJSON;
  directRouter?: 'user' | 'team';
};

export const ChangePerformanceTargetLink = ({
  selectedPerformance,
  directRouter,
}: ChangePerformanceTargetLinkProps) => {
  if (selectedPerformance.plan === null) {
    return null;
  }
  return (
    <Link
      href={generateAnnualPerformanceObjectivesDetail(directRouter ?? 'user', {
        user: selectedPerformance.user.id!.toString(),
        planid: selectedPerformance.plan!.id!.toString()!,
        id: selectedPerformance.id!.toString(),
        key: selectedPerformance.rowKey,
        position: selectedPerformance.evaluationJob?.value!,
      })}
      target="_blank"
    >
      查看目标
    </Link>
  );
};
