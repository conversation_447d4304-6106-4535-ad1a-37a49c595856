import React from 'react';

import { ChangePerformanceTargetLink } from './change-performance-target-link';

export const BasicChangePerformanceTargetLink = () => {
  return (
    <ChangePerformanceTargetLink
      selectedPerformance={{
        rowKey: '',
        id: null,
        type: 'TP',
        year: null,
        subType: 'TARGET',
        user: {
          id: 1,
          name: 'admin',
          idc: '111',
          region: { label: 'EC01', value: 'EC01' },
          job: null,
          hiredAt: 0,
        },
        evaluationJob: null,
        period: 'Q1',
        startedAt: 0,
        expiredAt: 0,
        status: null,
        objectives: [],
        objectiveStatus: null,
        objectiveStartedAt: 0,
        objectiveExpiredAt: 0,
        currentStepIdx: 0,
        isLoggedInUserInCurrentStepUsers: false,
        evaluations: [],
        evaluationStatus: null,
        evaluationStartedAt: 0,
        evaluationExpiredAt: 0,
        planId: null,
        plan: null,
        result: null,
        grade: null,
        gradeAvg: null,
        gradesSummaries: null,
        lineManagers: null,
        secondLineManagers: null,
        hrs: null,
        currentHandlers: null,
        allowEvaluationStartedAt: null,
        canModifyEvaluationJob: null,
        objectivesGrade: null,
        haveTarget: null,
        targetChangeType: null,
        targetChangeValidityDateStr: null,
      }}
    />
  );
};
