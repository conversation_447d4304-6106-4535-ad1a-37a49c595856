import {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_SECOND_VERSION,
} from '@manyun/hrm.util.performances';

import type { AnnualPerformanceScoringPopoverLocales } from './type.js';

export const enUS: AnnualPerformanceScoringPopoverLocales = {
  title: '计分规则',
  rules: [
    '季度绩效得分 = 100 - 日常扣分项1（分数绝对值） - 日常扣分项2（分数绝对值）- …… - 日常扣分项n（分数绝对值）',
    '年度指标得分：年度加分项1（分数绝对值） + 年度加分项2（分数绝对值）+ …… + 年度加分项n（分数绝对值）；加分上限10分',
    '年度最终得分 = 各季度绩效得分平均值 + 年度指标得分',
  ],
  rulesTextMapper: {
    [PERFORMANCE_BASIC_VERSION]: [
      '季度绩效得分 = 100 - 日常扣分项1（分数绝对值） - 日常扣分项2（分数绝对值）- …… - 日常扣分项n（分数绝对值）',
      '年度指标得分：年度加分项1（分数绝对值） + 年度加分项2（分数绝对值）+ …… + 年度加分项n（分数绝对值）；加分上限10分',
      '年度最终得分 = 各季度绩效得分平均值 + 年度指标得分',
    ],
    [PERFORMANCE_SECOND_VERSION]: [
      '季度绩效得分 = 100 - 季度扣分 + 季度加分',
      '年度最终得分 = 季度绩效得分平均值',
      '备注：每个季度加分累计不超过10分',
    ],
  },
};
export default enUS;
