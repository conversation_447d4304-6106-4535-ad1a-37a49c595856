import { i18n } from '@teammc/i18n';

import enUS from './en-US.js';
import { LOCALE_SCOPE_NAME, LOCALE_UNIQ_KEY } from './index.js';
import zhCN from './zh-CN.js';

i18n.current.changeLanguage('en-US');
i18n.current.addResourceBundle(
  'zh-CN',
  LOCALE_SCOPE_NAME,
  {
    [LOCALE_UNIQ_KEY]: zhCN,
  },
  true,
  true
);
i18n.current.addResourceBundle(
  'en-US',
  LOCALE_SCOPE_NAME,
  {
    [LOCALE_UNIQ_KEY]: enUS,
  },
  true,
  true
);
