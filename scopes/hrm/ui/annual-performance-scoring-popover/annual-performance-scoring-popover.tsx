import { i18n } from '@teammc/i18n';
import React from 'react';
import { useTranslation } from 'react-i18next';

import { Popover } from '@manyun/base-ui.ui.popover';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_SECOND_VERSION,
} from '@manyun/hrm.util.performances';

import type { AnnualPerformanceScoringPopoverLocales } from './locales/index.js';
import { LOCALE_SCOPE_NAME, LOCALE_UNIQ_KEY, generateFiledKey, zhCN } from './locales/index.js';

export type AnnualPerformanceScoringPopoverProps = {
  style?: React.CSSProperties;
  children?: React.ReactNode;
  ruleTextVersion?: keyof AnnualPerformanceScoringPopoverLocales['rulesTextMapper'];
};

i18n.current.addResourceBundle('zh-CN', LOCALE_SCOPE_NAME, {
  [LOCALE_UNIQ_KEY]: zhCN,
});

export function AnnualPerformanceScoringPopover({
  style,
  children,
  ruleTextVersion = PERFORMANCE_BASIC_VERSION,
}: AnnualPerformanceScoringPopoverProps) {
  const { t } = useTranslation(LOCALE_SCOPE_NAME);

  const rules: string[] = React.useMemo(() => {
    return t(generateFiledKey(`rulesTextMapper.${ruleTextVersion}`), { returnObjects: true });
  }, [ruleTextVersion, t]);

  return (
    <Popover
      title={t(generateFiledKey('title'))}
      content={
        <ul
          style={{
            width: ruleTextVersion === PERFORMANCE_SECOND_VERSION ? 344 : 580,
            paddingLeft: 16,
            marginBottom: 0,
            ...style,
          }}
        >
          {rules.map(rule => (
            <li key={rule}>
              <Typography.Text> {rule}</Typography.Text>
            </li>
          ))}
        </ul>
      }
      placement="bottomRight"
    >
      <Typography.Link>{children ?? t(generateFiledKey('title'))}</Typography.Link>
    </Popover>
  );
}
