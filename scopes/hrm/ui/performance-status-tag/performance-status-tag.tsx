import React from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';

import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import type { BackendPerformanceType, PerformanceStatus } from '@manyun/hrm.model.performance';

export type PerformanceStatusTagProps = {
  pfType: BackendPerformanceType;
  status: PerformanceStatus;
};

const colorMapper: Record<string, TagProps['color']> = {
  TARGET_WAIT_SETTING: 'warning',
  TARGET_WAIT_APPROVING: 'processing',
  TARGET_WAIT_SEC_SUPERIOR: 'processing',
  TARGET_PASS: 'success',
  TARGET_REFUSE: 'error',
  EVAL_INIT: 'default',
  EVAL_WAIT_SETTING: 'warning',
  EVAL_WAIT_APPROVING: 'processing',
  EVAL_PASS: 'success',
  EVAL_REFUSE: 'error',
  EVAL_SELF_CONFIRM: 'warning',
} as const;

export function PerformanceStatusTag({ pfType, status }: PerformanceStatusTagProps) {
  const locales = getPerformanceLocales();

  if (!status) {
    return <>--</>;
  }

  return (
    <Tag style={{ marginRight: 0 }} color={colorMapper[status]}>
      {locales.status[pfType === 'TP' ? 'test' : 'annual'][status]}
    </Tag>
  );
}
