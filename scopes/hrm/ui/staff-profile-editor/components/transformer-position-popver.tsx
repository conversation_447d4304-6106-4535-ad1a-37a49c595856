/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

import { useMutateUser } from './../use-mutate-user.js';

type TransformerPositionPopoverProps = {
  staffId: number;
  currentPositionCode?: string;
  onSubmit?: (newPositionCode: string) => void;
};
export const TransformerPositionPopover = ({
  staffId,
  currentPositionCode,
  onSubmit,
}: TransformerPositionPopoverProps) => {
  const [open, setOpen] = React.useState(false);
  const [form] = Form.useForm();
  const [loading, onUpdateUser] = useMutateUser();

  return (
    <Popover
      style={{ width: 280 }}
      trigger="click"
      placement="topLeft"
      open={open}
      title="调岗"
      content={
        <Form colon form={form}>
          <Form.Item
            label="岗位调整为"
            name="newPositionCode"
            rules={[{ required: true, message: '请选择新的岗位' }]}
          >
            {/*Tips: 从元数据中引入select  ，不可选当前岗位  提交后立即生效（就算员工信息编辑弹窗点击了【取消】，调岗结果也生效），且需回填到弹窗中 */}
            <MetaTypeSelect
              style={{ width: '152px' }}
              metaType={'POSITION_YG' as any}
              disabledMateCodes={currentPositionCode ? [currentPositionCode.toString()] : undefined}
            />
          </Form.Item>
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Space style={{ width: '100%' }}>
              <Button
                disabled={loading}
                size="small"
                onClick={() => {
                  setOpen(false);
                  form.resetFields();
                }}
              >
                取消
              </Button>
              <Button
                size="small"
                type="primary"
                loading={loading}
                onClick={async () => {
                  const values = await form.validateFields();
                  const newPositionCode = values.newPositionCode;
                  onUpdateUser(
                    {
                      id: staffId,
                      title: newPositionCode,
                    },
                    res => {
                      if (res) {
                        message.success('操作成功！');
                        setOpen(false);
                        //提交成功后需要更新到外层的数据中
                        onSubmit?.(newPositionCode!);
                      }
                    }
                  );
                }}
              >
                提交
              </Button>
            </Space>
          </Space>
        </Form>
      }
      onOpenChange={next => {
        form.resetFields();
        setOpen(next);
      }}
    >
      <Button type="link" compact>
        调岗
      </Button>
    </Popover>
  );
};
