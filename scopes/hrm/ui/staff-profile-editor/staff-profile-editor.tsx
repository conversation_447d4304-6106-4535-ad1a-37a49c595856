/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-4
 *
 * @packageDocumentation
 */
import { MinusCircleOutlined, PlusOutlined } from '@ant-design/icons';
import moment from 'moment';
import React from 'react';

import type { SvcRespData as User } from '@manyun/auth-hub.service.fetch-user';
import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { isIdCard } from '@manyun/base-ui.ui.certificate-type-select';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import styled, { css } from '@manyun/dc-brain.theme.theme';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

import { TransformerPositionPopover } from './components/transformer-position-popver';
import { useMutateUser } from './use-mutate-user.js';

export type StaffProfileEditorProps = {
  staffId: number;
  children: React.ReactNode;
  defaultOpen?: boolean;
  /**是否为管理员视角，否则为员工视角 */
  isManager?: boolean;
  onSuccess?: () => void;
  onClose?: () => void;
};

const useDateRangeWithPresent = (form: FormInstance, fieldPath: (string | number)[]) => {
  const [presentMap, setPresentMap] = React.useState<{ [key: number]: boolean }>({});

  // 设置"至今"状态
  const setPresent = React.useCallback((index: number, present: boolean) => {
    setPresentMap(prev => ({
      ...prev,
      [index]: present,
    }));
  }, []);

  const handleDateChange = React.useCallback(
    (index: number, dates: any) => {
      if (!dates?.[1]) {
        setPresent(index, false);
      }
    },
    [setPresent]
  );

  return {
    presentMap,
    setPresent,
    setPresentMap,
    handleDateChange,
  } as const;
};

export function StaffProfileEditor({
  staffId,
  isManager,
  defaultOpen,
  children,
  onSuccess,
  onClose,
}: StaffProfileEditorProps) {
  const [form] = Form.useForm();
  const [open, setOpen] = React.useState(defaultOpen);
  const [step, setStep] = React.useState(1);
  const [politicalStatus, setPoliticalStatus] = React.useState<string | undefined>(undefined);

  const showTransferPositionEntry = React.useMemo(() => {
    return isManager;
  }, [isManager]);
  const [{ loading: fetchLoading, user }, { onLoadUser, setUser }] = useLazyUser();
  const [loading, onUpdateUser] = useMutateUser();

  const { presentMap, setPresent, setPresentMap, handleDateChange } = useDateRangeWithPresent(
    form,
    ['workExperiences']
  );

  const onSetFormFields = React.useCallback(
    (user: User) => {
      form.setFieldsValue({
        ...user,
        maritalStatus:
          user?.married !== null && user?.married !== undefined
            ? user?.married
              ? 'married'
              : 'single'
            : undefined,
        birthday: user?.birthday ? toMoment(user.birthday) : undefined,
        ptBlockGuids: user?.ptBlockGuids
          ? user.ptBlockGuids?.map(blockGuid => {
              const [idc] = blockGuid.split('.');
              return [idc, blockGuid];
            }) || []
          : undefined,
        hiredDate: user?.hiredDate ? moment(user.hiredDate) : undefined,
        confirmationDate: user?.confirmationDate
          ? moment(user.confirmationDate).startOf('date')
          : undefined,
        joinWorkingDate: user?.joinWorkingDate
          ? moment(user.joinWorkingDate).startOf('date')
          : undefined,
        academicQualifications:
          (user?.academicQualifications ?? []).length > 0
            ? (user?.academicQualifications ?? []).map(item => ({
                ...item,
                entryDate:
                  item.admissionDate && item.graduationDate
                    ? [moment(item.admissionDate), moment(item.graduationDate)]
                    : undefined,
              }))
            : [{}],
        workExperiences:
          (user?.workExperiences ?? []).length > 0
            ? (user?.workExperiences ?? []).map(item => ({
                ...item,
                entryDate:
                  item.hiredDate && item.resignDate
                    ? [
                        moment(item.hiredDate),
                        isLongTerm(item.resignDate) ? null : moment(item.resignDate),
                      ]
                    : undefined,
              }))
            : [{}],
        partyEntryDate: user?.partyMemberInfo?.joinDate
          ? moment(user.partyMemberInfo.joinDate)
          : undefined,
        partyBranchName: user?.partyMemberInfo?.partyBranchName ?? undefined,
        partyPosition: user?.partyMemberInfo?.partyPosition ?? undefined,
        partyMemberStatus:
          user?.partyMemberInfo?.floating !== undefined && user?.partyMemberInfo?.floating !== null
            ? user.partyMemberInfo.floating
              ? 'yes'
              : 'no'
            : undefined,
        partyRelationshipInfo: user?.partyMemberInfo?.partyRelationshipInfo ?? undefined,
        remark: user?.partyMemberInfo?.remark ?? undefined,
      });

      // 根据工作经历设置 presentMap
      const newPresentMap = (user?.workExperiences ?? []).reduce(
        (acc, item, index) => {
          // 如果 resignDate 是长期，则设置对应索引为 true
          if (item.resignDate && isLongTerm(item.resignDate)) {
            acc[index] = true;
          }
          return acc;
        },
        {} as { [key: number]: boolean }
      );

      setPresentMap(newPresentMap);
    },
    [form, setPresentMap]
  );

  const onSave = React.useCallback(
    async (onClose = false) => {
      const values = await form.getFieldsValue();
      const ptBlockGuids = (values.ptBlockGuids ?? [])
        .map(([_, subPosition]: [string, string][]) => subPosition)
        .filter((position: string): position is string => Boolean(position));
      let params = {};
      if (step === 1) {
        params = {
          ...values,
          ptBlockGuids: values.ptBlockGuids ? ptBlockGuids : undefined,
          married:
            values?.maritalStatus === 'married'
              ? true
              : values?.maritalStatus === 'single'
                ? false
                : undefined,
          birthday: values.birthday?.valueOf(),
          hiredDate: values.hiredDate?.valueOf(),
          confirmationDate: values.confirmationDate?.valueOf(),
          joinWorkingDate: values.joinWorkingDate?.valueOf(),
        };
      } else {
        params = {
          academicQualifications: values.academicQualifications
            ?.map(
              (item: {
                entryDate?: any[];
                school?: string;
                educationalDegree?: string;
                major?: string;
              }) => ({
                admissionDate: item?.entryDate
                  ? item.entryDate?.[0]?.startOf('date').valueOf()
                  : undefined,
                graduationDate: item?.entryDate
                  ? item.entryDate?.[1]?.startOf('date').valueOf()
                  : undefined,
                school: item?.school,
                educationalDegree: item?.educationalDegree,
                major: item?.major,
              })
            )
            .filter(Boolean),
          workExperiences: values.workExperiences
            ?.map(
              (item: {
                company?: string;
                position?: string;
                jobDescription?: string;
                entryDate?: any[];
              }) => ({
                company: item?.company,
                position: item?.position,
                jobDescription: item?.jobDescription,
                hiredDate: item?.entryDate
                  ? item.entryDate?.[0]?.startOf('date').valueOf()
                  : undefined,
                resignDate: item?.entryDate
                  ? item.entryDate[0] && !item.entryDate?.[1]
                    ? convertToLongTermValueOf()
                    : item.entryDate?.[1]?.startOf('date').valueOf()
                  : undefined,
              })
            )
            .filter(Boolean),
          partyMemberInfo:
            politicalStatus === 'PARTY_MEMBER'
              ? {
                  joinDate: values?.partyEntryDate
                    ? values.partyEntryDate?.startOf('date').valueOf()
                    : undefined,
                  partyBranchName: values.partyBranchName,
                  partyPosition: values.partyPosition,
                  floating:
                    values.partyMemberStatus !== undefined
                      ? values.partyMemberStatus === 'yes'
                      : undefined,
                  partyRelationshipInfo: values.partyRelationshipInfo,
                  remark: values.remark,
                }
              : undefined,
        };
      }

      onUpdateUser(
        {
          id: staffId,
          ...params,
        },
        res => {
          if (res && onClose) {
            message.success('操作成功！');
            onSuccess?.();
            setOpen(false);
          }
        }
      );
    },
    [form, onSuccess, onUpdateUser, politicalStatus, staffId, step]
  );

  React.useEffect(() => {
    if (open) {
      onLoadUser(staffId, user => {
        if (user) {
          setPoliticalStatus(user.politicalStatus);
          onSetFormFields(user);
        }
      });
    }
  }, [onLoadUser, onSetFormFields, open, staffId]);

  return (
    <>
      <div
        onClick={() => {
          setStep(1);
          setOpen(true);
        }}
      >
        {children}
      </div>
      <Modal
        title="个人信息"
        width={1200}
        open={open}
        bodyStyle={{ maxHeight: '75vh', overflowY: 'auto' }}
        destroyOnClose
        footer={
          <Space style={{ width: '100%', justifyContent: 'flex-end' }}>
            <Button
              disabled={loading}
              onClick={() => {
                onClose?.();
                setOpen(false);
                onSuccess?.();
              }}
            >
              取消
            </Button>
            <Button
              disabled={fetchLoading || loading}
              onClick={() => {
                setStep(step === 1 ? 2 : 1);
                onSave();
                onClose?.();
              }}
            >
              {step === 1 ? '下' : '上'}一步
            </Button>
            <Button
              type="primary"
              disabled={fetchLoading}
              loading={loading}
              onClick={() => {
                onSave(true);
              }}
            >
              保存
            </Button>
          </Space>
        }
        onCancel={() => {
          form.resetFields();
          setPoliticalStatus('');
          setOpen(false);
          onSuccess?.();
          onClose?.();
        }}
      >
        <Spin spinning={fetchLoading}>
          <Form style={{ width: '100%' }} form={form} layout="vertical">
            {step === 1 && (
              <Space style={{ width: '100%' }} direction="vertical" size="large">
                <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                  基本信息
                </Typography.Title>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item label="姓名" name="name" required>
                      <Input disabled />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="身份证号"
                      name="certNo"
                      required
                      rules={[
                        {
                          validator: async (_, cardNo) => {
                            if (!cardNo) {
                              return Promise.reject('身份证号必填！');
                            }
                            if (!isIdCard(cardNo)) {
                              return Promise.reject('身份证号格式不正确');
                            } else {
                              return Promise.resolve();
                            }
                          },
                        },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="联系方式"
                      name="mobileNumber"
                      rules={[
                        { required: true, message: '请输入手机号码' },
                        {
                          pattern: /^1[3456789]\d{9}$/,
                          message: '手机号格式错误，请重新输入',
                        },
                        { max: 11, message: '最多输入 11 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="邮箱"
                      name="email"
                      rules={[
                        { required: true, message: '请输入邮箱' },
                        {
                          type: 'email',
                          message: '请输入正确邮箱',
                        },
                        { max: 40, message: '最多输入 40 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="婚姻状况"
                      name="maritalStatus"
                      rules={[{ required: true, message: '请选择婚姻状况' }]}
                    >
                      <Select
                        options={[
                          {
                            label: '未婚',
                            value: 'single',
                          },
                          {
                            label: '已婚',
                            value: 'married',
                          },
                        ]}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="出生年月"
                      name="birthday"
                      rules={[
                        {
                          required: true,
                          message: '请选择出生年月',
                        },
                      ]}
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        picker="month"
                        disabledDate={current => {
                          if (!current) {
                            return false;
                          }
                          const currentFirstDay = current.clone().startOf('month');
                          const todayFirstDay = moment().startOf('month');
                          return currentFirstDay.isAfter(todayFirstDay);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="户籍类型"
                      name="householdType"
                      rules={[
                        {
                          required: true,
                          message: '请选择户籍类型',
                        },
                      ]}
                    >
                      <Select
                        options={['城镇户口', '农村户口'].map(item => ({
                          label: item,
                          value: item,
                        }))}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="政治面貌"
                      name="politicalStatus"
                      rules={[
                        {
                          required: true,
                          message: '请选择政治面貌',
                        },
                      ]}
                    >
                      <Select
                        options={[
                          {
                            label: '党员',
                            value: 'PARTY_MEMBER',
                          },
                          {
                            label: '群众',
                            value: 'MASSES',
                          },
                          {
                            label: '团员',
                            value: 'LEAGUE_MEMBER',
                          },
                        ]}
                        onChange={value => {
                          setPoliticalStatus(value);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="民族"
                      name="ethnicGroup"
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: '请输入民族',
                        },
                        { max: 10, message: '最多输入 10 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="紧急联系人姓名"
                      name="emergencyContactName"
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: '请输入紧急联系人姓名',
                        },
                        { max: 10, message: '最多输入 10 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="紧急联系人电话"
                      name="emergencyContactMobile"
                      rules={[
                        { required: true, message: '请输入手机号码' },
                        {
                          pattern: /^1[3456789]\d{9}$/,
                          message: '手机号格式错误，请重新输入',
                        },
                        { max: 11, message: '最多输入 11 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="家庭住址"
                      name="homeAddress"
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: '请输入家庭住址',
                        },
                        { max: 50, message: '最多输入 50 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>
                <Space size={16}>
                  <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                    岗位信息
                  </Typography.Title>
                  {showTransferPositionEntry && (
                    <TransformerPositionPopover
                      staffId={staffId}
                      currentPositionCode={user?.title ?? undefined}
                      onSubmit={newPositionCode => {
                        form.setFieldsValue({ title: newPositionCode });
                        setUser(pre => ({ ...pre, title: newPositionCode }) as User);
                      }}
                    />
                  )}
                </Space>
                <Row gutter={24}>
                  <Col span={6}>
                    <Form.Item
                      label="所属楼栋"
                      name="ptBlockGuids"
                      rules={[{ required: true, message: '请选择所属楼栋' }]}
                    >
                      <LocationCascader
                        style={{ width: '100%' }}
                        nodeTypes={['IDC', 'BLOCK']}
                        idc={user?.idc}
                        changeOnSelect={false}
                        multiple
                        maxTagCount="responsive"
                        showCheckedStrategy="SHOW_CHILD"
                        disabled={!isManager}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="岗位"
                      name="title"
                      rules={[{ required: true, message: '请选择岗位' }]}
                    >
                      <MetaTypeSelect metaType={'POSITION_YG' as any} disabled />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="直线经理"
                      name="supervisorUid"
                      rules={[{ required: true, message: '请选择直线经理' }]}
                    >
                      <UserSelect
                        labelInValue={false}
                        disabled={!isManager}
                        userState="in-service"
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      label="职称"
                      name="jobDescriptions"
                      rules={[
                        { required: true, whitespace: true, message: '请输入职称' },
                        { max: 20, message: '最多输入 20 个字符！' },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label="工号"
                      name="jobNumber"
                      rules={[
                        { required: true, whitespace: true, message: '请输入工号' },
                        { max: 20, message: '最多输入 20 个字符！' },
                      ]}
                    >
                      <Input disabled={!isManager} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label="入职日期"
                      name="hiredDate"
                      rules={[
                        {
                          required: true,
                          message: '请选择入职日期',
                        },
                      ]}
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        disabled={!isManager}
                        disabledDate={current => {
                          if (!current) {
                            return false;
                          }
                          const today = moment().endOf('day');
                          return current.isAfter(today);
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label="转正日期"
                      name="confirmationDate"
                      rules={[
                        {
                          required: true,
                          message: '请选择转正日期',
                        },
                      ]}
                    >
                      <DatePicker style={{ width: '100%' }} disabled={!isManager} />
                    </Form.Item>
                  </Col>
                  <Col span={6}>
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      label="开始工作时间"
                      name="joinWorkingDate"
                      rules={[
                        {
                          required: true,
                          message: '请选择开始工作时间',
                        },
                      ]}
                    >
                      <DatePicker
                        style={{ width: '100%' }}
                        picker="month"
                        disabledDate={current => {
                          if (!current) {
                            return false;
                          }
                          const now = new Date();
                          const currentDate = current.toDate();
                          now.setDate(1);
                          now.setHours(0, 0, 0, 0);
                          currentDate.setDate(1);
                          currentDate.setHours(0, 0, 0, 0);

                          return currentDate.valueOf() > now.valueOf();
                        }}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </Space>
            )}
            {step === 2 && (
              <Space style={{ width: '100%' }} direction="vertical" size="large">
                <Space size={0} align="center">
                  <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                    学历信息
                  </Typography.Title>
                  <Typography.Text type="secondary">（必填）</Typography.Text>
                </Space>
                <Form.List name="academicQualifications">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Space
                          key={key}
                          style={{ display: 'flex', marginBottom: 0 }}
                          align="baseline"
                        >
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'entryDate']}
                            rules={[{ required: true, message: '时间必填' }]}
                          >
                            <DatePicker.RangePicker
                              style={{ width: 256 }}
                              picker="month"
                              placeholder={['入学时间', '毕业时间']}
                              disabledDate={current => {
                                if (!current) {
                                  return false;
                                }
                                const currentFirstDay = current.clone().startOf('month');
                                const todayFirstDay = moment().startOf('month');
                                return currentFirstDay.isAfter(todayFirstDay);
                              }}
                            />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'school']}
                            rules={[
                              { required: true, whitespace: true, message: '请输入学校名称' },
                              { max: 20, message: '最多输入 20 个字符！' },
                            ]}
                          >
                            <Input style={{ width: 216 }} placeholder="学校" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'educationalDegree']}
                            rules={[{ required: true, message: '请选择学历' }]}
                          >
                            <Select
                              style={{ width: 160 }}
                              showSearch
                              placeholder="学历"
                              options={['高中', '中专', '大专', '本科', '硕士', '博士', '其他'].map(
                                level => ({
                                  label: level,
                                  value: level,
                                })
                              )}
                            />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'major']}
                            rules={[
                              { required: true, whitespace: true, message: '请输入专业' },
                              { max: 20, message: '最多输入 20 个字符！' },
                            ]}
                          >
                            <Input style={{ width: 216 }} placeholder="专业" />
                          </Form.Item>
                          {fields.length > 1 && (
                            <MinusCircleOutlined onClick={() => remove(name)} />
                          )}
                        </Space>
                      ))}
                      <Form.Item style={{ marginBottom: 0 }}>
                        <Button
                          style={{ width: 872 }}
                          type="dashed"
                          block
                          icon={<PlusOutlined />}
                          onClick={() => add()}
                        >
                          添加学历信息
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>
                <Space size={0} align="center">
                  <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                    工作经历
                  </Typography.Title>
                  <Typography.Text type="secondary">（必填）</Typography.Text>
                </Space>
                <Form.List name="workExperiences">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Space
                          key={key}
                          style={{ display: 'flex', marginBottom: 0 }}
                          align="baseline"
                        >
                          <CustomerRangePicker>
                            <Form.Item
                              {...restField}
                              style={{ marginBottom: 16 }}
                              name={[name, 'entryDate']}
                              rules={[{ required: true, message: '时间必填' }]}
                            >
                              <DatePicker.RangePicker
                                className={presentMap[name] ? 'custom-range-picker' : ''}
                                style={{ width: 256 }}
                                picker="month"
                                placeholder={['入职时间', presentMap[name] ? '至今' : '离职时间']}
                                allowEmpty={[false, true]}
                                disabledDate={current => {
                                  if (!current) {
                                    return false;
                                  }
                                  const currentFirstDay = current.clone().startOf('month');
                                  const todayFirstDay = moment().startOf('month');
                                  return currentFirstDay.isAfter(todayFirstDay);
                                }}
                                renderExtraFooter={() => {
                                  const [startDate] =
                                    form.getFieldValue(['workExperiences', name, 'entryDate']) ||
                                    [];
                                  if (!startDate) {
                                    return null;
                                  }

                                  return (
                                    <Button
                                      type="link"
                                      onClick={() => {
                                        setPresent(name, true);
                                        form.setFieldValue(
                                          ['workExperiences', name, 'entryDate'],
                                          [startDate, null]
                                        );
                                        // setQueryParams(pre => {
                                        //   return {
                                        //     ...pre,
                                        //     endTimePickerPlaceholder: pre.endTimePickerPlaceholder
                                        //       ? undefined
                                        //       : '至今',
                                        //   };
                                        // });
                                        // form.setFieldValue('endDate', null);
                                      }}
                                    >
                                      至今
                                    </Button>
                                  );
                                }}
                                onChange={dates => {
                                  handleDateChange(name, dates);
                                  form.setFieldValue(['workExperiences', name, 'entryDate'], dates);
                                }}
                              />
                            </Form.Item>
                          </CustomerRangePicker>
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'company']}
                            rules={[
                              { required: true, whitespace: true, message: '请输入公司名称' },
                              { max: 20, message: '最多输入 20 个字符！' },
                            ]}
                          >
                            <Input style={{ width: 216 }} placeholder="公司" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'position']}
                            rules={[
                              { required: true, message: '请输入职位' },
                              { max: 20, message: '最多输入 20 个字符！' },
                            ]}
                          >
                            <Input style={{ width: 160 }} placeholder="职位" />
                          </Form.Item>
                          <Form.Item
                            {...restField}
                            style={{ marginBottom: 16 }}
                            name={[name, 'jobDescription']}
                            rules={[
                              { required: true, whitespace: true, message: '请输入工作内容' },
                              { max: 1000, message: '最多输入 1000 个字符！' },
                            ]}
                          >
                            <Input style={{ width: 458 }} placeholder="工作内容" />
                          </Form.Item>
                          {fields.length > 1 && (
                            <MinusCircleOutlined onClick={() => remove(name)} />
                          )}
                        </Space>
                      ))}
                      <Form.Item style={{ marginBottom: 0 }}>
                        <Button
                          style={{ width: 1114 }}
                          type="dashed"
                          block
                          icon={<PlusOutlined />}
                          onClick={() => add()}
                        >
                          添加工作经历
                        </Button>
                      </Form.Item>
                    </>
                  )}
                </Form.List>
                {politicalStatus === 'PARTY_MEMBER' && (
                  <Space style={{ width: '100%' }} direction="vertical" size="large">
                    <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                      党员信息
                    </Typography.Title>
                    <Row gutter={24}>
                      <Col span={6}>
                        <Form.Item
                          label="入党时间"
                          name="partyEntryDate"
                          rules={[{ required: true, message: '请选择入党时间' }]}
                        >
                          <DatePicker
                            style={{ width: '100%' }}
                            disabledDate={current => {
                              if (!current) {
                                return false;
                              }
                              const today = moment().endOf('day');
                              return current.isAfter(today);
                            }}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="所在党支部名称	"
                          name="partyBranchName"
                          rules={[
                            {
                              required: true,
                              whitespace: true,
                              message: '请输入所在党支部名称	',
                            },
                            { max: 100, message: '最多输入 100 个字符！' },
                          ]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="党内职务	"
                          name="partyPosition"
                          rules={[
                            {
                              required: true,
                              whitespace: true,
                              message: '请输入党内职务',
                            },
                            { max: 100, message: '最多输入 100 个字符！' },
                          ]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          label="是否流动党员	"
                          name="partyMemberStatus"
                          rules={[
                            {
                              required: true,
                              message: '请选择是否流动党员',
                            },
                          ]}
                        >
                          <Select
                            options={[
                              {
                                label: '是',
                                value: 'yes',
                              },
                              {
                                label: '否',
                                value: 'no',
                              },
                            ]}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          style={{ marginBottom: 0 }}
                          label="组织关系转入/转出"
                          name="partyRelationshipInfo"
                          rules={[
                            {
                              required: true,
                              whitespace: true,
                              message: '请输入组织关系转入/转出',
                            },
                            { max: 100, message: '最多输入 100 个字符！' },
                          ]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          style={{ marginBottom: 0 }}
                          label="备注		"
                          name="remark"
                          rules={[{ max: 100, message: '最多输入 100 个字符！' }]}
                        >
                          <Input />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Space>
                )}
              </Space>
            )}
          </Form>
        </Spin>
      </Modal>
    </>
  );
}

const useLazyUser = () => {
  const [user, setUser] = React.useState<User | undefined>(undefined);
  const [loading, setLoading] = React.useState(false);

  const onGetUser = React.useCallback(
    async (id: string | number, callback?: (user: User | undefined) => void) => {
      setLoading(true);
      const { data, error } = await fetchUser({ id: Number(id), needValid: false });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      if (data === null) {
        message.error('不存在该用户！');
        return;
      }
      setUser(data);
      callback?.(data);
    },
    []
  );

  return [
    { loading, user },
    { onLoadUser: onGetUser, setUser },
  ] as const;
};

const toMoment = (date: string | number | undefined): moment.Moment | undefined => {
  if (!date) {
    return undefined;
  }

  // 如果是数字或数字字符串，当作时间戳处理
  if (!isNaN(Number(date))) {
    return moment(Number(date));
  }

  // 尝试解析日期字符串
  const parsed = moment(date);
  return parsed.isValid() ? parsed : undefined;
};

const CustomerRangePicker = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .custom-range-picker {
        .${prefixCls}-picker-input > input::placeholder {
          color: var(--text-color);
        }
      }
    `;
  }}
`;

export const LONG_TERM = '2099-01-01';
// 是否为至今
export const isLongTerm = (endTime: number) => {
  const endStr = moment(endTime).startOf('month').format('YYYY-MM-DD');
  return endStr === LONG_TERM;
};

export const convertToLongTermValueOf = () => {
  return moment(LONG_TERM).startOf('month').valueOf();
};
