import React from 'react';

import type { SelectProps, SelectValue } from '@manyun/base-ui.ui.select';
import { Select } from '@manyun/base-ui.ui.select';

import type { UserShifts } from '@manyun/auth-hub.model.user';

export type UserShiftsSelectProps = SelectProps<SelectValue> & { excludeShift?: UserShifts[] };

type RefSelectProps = {
  focus: () => void;
  blur: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  scrollTo: any;
};

export type UserShiftsProps = {
  userShifts: UserShifts;
  style?: React.CSSProperties;
};

export const USER_SHIFTS_TEXT: Record<UserShifts, string> = {
  SIMPLE_SHIFTS: '标准工时制',
  QUARTER_SHIFTS: '季度综合工时制',
  FLEXIBLE_SHIFTS: '不定时工时制',
};

const options: { label: string; value: UserShifts }[] = [
  { label: USER_SHIFTS_TEXT.SIMPLE_SHIFTS, value: 'SIMPLE_SHIFTS' },
  { label: USER_SHIFTS_TEXT.QUARTER_SHIFTS, value: 'QUARTER_SHIFTS' },
  { label: USER_SHIFTS_TEXT.FLEXIBLE_SHIFTS, value: 'FLEXIBLE_SHIFTS' },
];

export const UserShiftsSelect = React.forwardRef(
  (
    { value, defaultValue, excludeShift, style, ...rest }: UserShiftsSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    return (
      <Select
        ref={ref}
        style={style}
        options={options.filter(opt => !(excludeShift && excludeShift.includes(opt.value)))}
        value={value}
        defaultValue={defaultValue}
        {...rest}
      />
    );
  }
);
UserShiftsSelect.displayName = 'UserShiftsSelect';

export function UserShiftsText({ userShifts, style }: UserShiftsProps) {
  return <span style={style}>{USER_SHIFTS_TEXT[userShifts]}</span>;
}
