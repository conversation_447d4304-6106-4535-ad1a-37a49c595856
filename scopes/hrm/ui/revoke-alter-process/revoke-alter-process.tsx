import React, { useCallback } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { useRevokeAlterRequest } from '@manyun/hrm.hook.use-revoke-alter-request';
import type { AlterType } from '@manyun/hrm.service.revoke-alter-request';

const EMLEMENT_REVOKE_MISSED_PUNCHES_CODE = 'element_revoke-fix-user-missed-punches-request';

const ELEMENT_REVOKE_ALTER_CODE = 'element_revoke-change-user-shift-schedule-request';

export type RevokeAlterProcessProps = {
  bizId: string;
  bizType: AlterType;
  recordUser: {
    /*记录申请人 */
    applyId: number;
    /**创建人 */
    createId?: number;
  };
  footer?: boolean;
  onOk?: () => void;
} & Pick<ButtonProps, 'type' | 'compact' | 'loading'>;

export function RevokeAlterProcess({
  bizId,
  bizType,
  recordUser,
  onOk,
  footer,
  ...restProps
}: RevokeAlterProcessProps) {
  const [submitLoading, onRevoke] = useRevokeAlterRequest();
  const [, { checkUserId, checkCode }] = useAuthorized();

  const _onHandleRevoke = useCallback(() => {
    Modal.confirm({
      title: `确认要撤回吗？`,
      content: '撤回之后需重新发起申请！',
      okText: '确定',
      cancelText: '取消',
      onOk: () => {
        onRevoke({ bizId, type: bizType }, () => {
          onOk?.();
        });
      },
      onCancel() {},
    });
  }, [onRevoke, bizId, bizType, onOk]);

  /**补卡 */
  if (bizType === 'SUPPLY') {
    if (!checkUserId(recordUser.applyId) && !checkCode(EMLEMENT_REVOKE_MISSED_PUNCHES_CODE)) {
      return null;
    }
  }

  /**除补卡外的其它排班调整 */
  if (bizType !== 'SUPPLY' && recordUser.createId) {
    if (
      !checkUserId(recordUser.applyId) &&
      !checkUserId(recordUser.createId) &&
      !checkCode(ELEMENT_REVOKE_ALTER_CODE)
    ) {
      return null;
    }
  }

  const revokeButtonRender = () => {
    return (
      <Button
        loading={submitLoading || restProps.loading}
        onClick={() => {
          _onHandleRevoke();
        }}
        {...restProps}
      >
        撤回
      </Button>
    );
  };

  return footer ? <FooterToolBar>{revokeButtonRender()}</FooterToolBar> : revokeButtonRender();
}
