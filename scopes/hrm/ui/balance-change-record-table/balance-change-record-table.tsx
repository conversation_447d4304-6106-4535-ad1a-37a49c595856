import dayjs from 'dayjs';
import React from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { AnyTargetLink } from '@manyun/dc-brain.ui.any-target-link';
import type { ChangeRecord } from '@manyun/hrm.service.fetch-balance-change-record';
import { BALANCE_TYPE_MAPPER, BalanceType } from '@manyun/hrm.service.fetch-balance-change-record';
import { TargetType } from '@manyun/notification-hub.model.on-site-messages';

import { FilesDetail } from './componets/files-detail';

export type ShowColumn =
  | 'balanceType'
  | 'startBalance'
  | 'endBalance'
  | 'reason'
  | 'createTime'
  | 'operatorId'
  | 'fileList';

export type BalanceChangeRecordTableProps = {
  showColumns: ShowColumn[];
  /**仅展示调休余额 */
  breakOffOnly?: boolean;
} & Omit<TableProps<ChangeRecord>, 'columns'>;

export function BalanceChangeRecordTable({
  showColumns,
  breakOffOnly = false,
  ...props
}: BalanceChangeRecordTableProps) {
  return (
    <Table
      scroll={{ x: 'max-content' }}
      columns={getColumns({ showColumns: showColumns, breakOffOnly })}
      {...props}
    />
  );
}

export const getColumns = ({
  showColumns,
  operation,
  breakOffOnly,
}: {
  showColumns: ShowColumn[];
  operation?: ColumnType<ChangeRecord>;
  breakOffOnly?: boolean;
}) => {
  let columns: ColumnType<ChangeRecord>[] = [
    {
      title: '假期名称',
      fixed: 'left',
      dataIndex: 'balanceType',
      render: val => BALANCE_TYPE_MAPPER[val],
    },
    {
      title: '变更额度',
      dataIndex: 'startBalance',
      render: (_val, record) => {
        const diffValue = record.endBalance - record.startBalance;
        return (
          <>
            {diffValue > 0 ? '+' : ''}
            {diffValue.toFixed(1)}
            {record.balanceType === BalanceType.PAID_LEAVE || breakOffOnly ? '小时' : '天'}
          </>
        );
      },
    },
    {
      title: '余额快照',
      dataIndex: 'endBalance',
      render: (val, record) => {
        return (
          <>
            {val} {record.balanceType === BalanceType.PAID_LEAVE || breakOffOnly ? '小时' : '天'}
          </>
        );
      },
    },
    {
      title: '变更原因',
      dataIndex: 'reason',
      render: val => {
        const listRegex = /\((.+?)\)/gi;
        const valSplitArr = (val ?? '').split(listRegex);
        /*** 用于截取审批ID eg: 请假扣减余额(280402363884240896) context=280402363884240896 */
        const context = valSplitArr[1];
        return context ? (
          <>
            {valSplitArr[0]}（审批ID:
            <AnyTargetLink name={context} id={context} type={TargetType.APPROVAL_DETAIL} />）
          </>
        ) : (
          val
        );
      },
    },
    {
      title: '变更时间',
      dataIndex: 'createTime',
      render: val => <>{dayjs(val).format('YYYY-MM-DD HH:mm:ss')}</>,
    },
    {
      title: '操作人',
      dataIndex: 'operatorId',
      render: val =>
        val !== null ? <UserLink id={val} name={val === 0 ? 'SYSTEM' : undefined} /> : '--',
    },
    {
      title: '附件',
      fixed: 'right',
      dataIndex: 'fileList',
      render: (_, record) => <FilesDetail targetId={record.bizId} />,
    },
  ];
  if (showColumns.length) {
    columns = columns.filter(
      column =>
        column.dataIndex &&
        (showColumns as string[]).includes(
          Array.isArray(column.dataIndex) ? column.dataIndex.join('.') : column.dataIndex.toString()
        )
    );
  }
  if (operation) {
    columns.push(operation);
  }
  return columns;
};
