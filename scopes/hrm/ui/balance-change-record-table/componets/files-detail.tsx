import React, { useEffect, useState } from 'react';

import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { File } from '@manyun/base-ui.util.guess-mime-type';

import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

export type FilesDetailProps = {
  targetId: string;
};
export const FilesDetail = ({ targetId }: FilesDetailProps) => {
  const [files, setFiles] = useState<File[]>([]);

  useEffect(() => {
    if (targetId) {
      (async () => {
        const { data, error } = await fetchBizFileInfos({
          targetType: 'LEAVE_BALANCE_CHANGE',
          targetId: targetId,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        setFiles(
          data.data.map(file => ({
            name: file.name,
            src: file.src,
            ext: file.ext,
          }))
        );
      })();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return files.length > 0 ? (
    <SimpleFileList files={files}>
      <Typography.Link>查看</Typography.Link>
    </SimpleFileList>
  ) : (
    <>--</>
  );
};
