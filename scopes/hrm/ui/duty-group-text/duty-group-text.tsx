/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-18
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Spin } from '@manyun/base-ui.ui.spin';

import { fetchPagedDutyGroup } from '@manyun/hrm.service.fetch-paged-duty-group';
import type { BackendDutyGroup } from '@manyun/hrm.service.fetch-paged-duty-group';

export type DutyGroupTextProps = {
  dutyGroupIds: number[];
  blockGuidList?: string[];
};

export function DutyGroupText({ dutyGroupIds, blockGuidList }: DutyGroupTextProps) {
  const [dutyGroupEntities, setDutyGroupEntities] = useState<Record<number, BackendDutyGroup>>({});
  const [loading, setLoading] = useState(false);

  const getDutyGroup = useCallback(async () => {
    setLoading(true);
    const { data, error } = await fetchPagedDutyGroup({
      pageNum: 1,
      pageSize: 100,
      blockGuids: blockGuidList,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    const _entities: Record<number, BackendDutyGroup> = {};
    data.data.forEach(item => {
      _entities[item.id] = item;
    });
    setDutyGroupEntities(_entities);
  }, [blockGuidList]);

  useEffect(() => {
    getDutyGroup();
  }, [getDutyGroup]);

  if (loading) {
    return <Spin />;
  }

  return dutyGroupIds.map(id => dutyGroupEntities[id]?.groupName ?? '未知').join('｜');
}
