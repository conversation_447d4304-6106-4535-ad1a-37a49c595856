import React, { useCallback, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import type { SvcQuery } from '@manyun/hrm.service.create-resumption-leave-request';
import { createResumptionLeaveRequest } from '@manyun/hrm.service.create-resumption-leave-request';

/**@todo  重构排班调整列表和详情页时 use model/shift-adjustment-ticket */
export type AlterInfo = {
  startTime: number /**调班开始时间 */;
  alterType: string /*调班类型 */;
  applyStaffId: number /**申请人id */;
  creatorId: number /**创建人 */;
  bizStatus: string /**审批状态 */;
  bizId: string /**业务id */;
  rollBackAlter: string | null /**销假状态 */;
  canRollBack: boolean;
};

export type ResumptionAlterSchedlueRequestProps = {
  alterInfo: AlterInfo;
  onSuccess: () => void;
  footer?: boolean;
} & ButtonProps;

export function ResumptionAlterSchedlueRequest({
  alterInfo,
  onSuccess,
  footer,
  ...restProps
}: ResumptionAlterSchedlueRequestProps) {
  const [, { checkUserId }] = useAuthorized();
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm<SvcQuery>();

  const onSubmit = useCallback(async () => {
    await form.validateFields();
    const formValues = form.getFieldsValue();
    setLoading(true);
    const { error } = await createResumptionLeaveRequest({
      bizId: alterInfo.bizId,
      reason: formValues.reason,
      attachments: formValues?.attachments ?? [],
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('提交成功');
    setVisible(false);
    onSuccess();
  }, [alterInfo.bizId, form, onSuccess]);

  /** 已销假、销假中的不能提交销假，后端控制是否控制最早班次发生之后不能销假 */
  if (
    (alterInfo.rollBackAlter === null || ['REFUSE', 'REVOKE'].includes(alterInfo.rollBackAlter)) &&
    alterInfo.alterType === 'LEAVE' &&
    alterInfo.bizStatus === 'PASS' &&
    alterInfo.canRollBack &&
    (checkUserId(alterInfo.applyStaffId) || checkUserId(alterInfo.creatorId))
  ) {
    return (
      <>
        {footer ? (
          <FooterToolBar>
            <ResumptionAlterButton
              {...restProps}
              onClick={() => {
                form.resetFields();
                setVisible(true);
              }}
            />
          </FooterToolBar>
        ) : (
          <ResumptionAlterButton
            {...restProps}
            onClick={() => {
              form.resetFields();
              setVisible(true);
            }}
          />
        )}
        <Modal
          visible={visible}
          title="销假申请"
          width={560}
          okButtonProps={{ loading: loading }}
          destroyOnClose
          onCancel={() => {
            setVisible(false);
          }}
          onOk={() => {
            onSubmit();
          }}
        >
          <Form form={form} labelCol={{ span: 4 }}>
            <Form.Item
              name="reason"
              label="销假原因"
              required
              rules={[
                { required: true, message: '请输入销假原因' },
                {
                  max: 200,
                  message: '最多输入 200 个字符！',
                },
              ]}
            >
              <Input.TextArea rows={3} />
            </Form.Item>
            <Form.Item
              label="附件"
              name="attachments"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <McUpload
                accept=".png,.jpg,.jpeg,.pdf,.doc,.docx,.xls,.xlsx"
                maxCount={5}
                maxFileSize={20}
                showUploadList
                showAccept
              >
                <Button>上传</Button>
              </McUpload>
            </Form.Item>
          </Form>
        </Modal>
      </>
    );
  }
  return null;
}

export const ResumptionAlterButton = ({ ...restProps }: ButtonProps) => {
  return <Button {...restProps}>销假</Button>;
};
