import React from 'react';

import { ResumptionAlterSchedlueRequest } from './resumption-alter-schedlue-request';

export const BasicResumptionAlterSchedlueRequest = () => {
  return (
    <ResumptionAlterSchedlueRequest
      alterInfo={{
        startTime: 0,
        alterType: '',
        applyStaffId: 0,
        creatorId: 0,
        bizStatus: '',
        bizId: '123',
        rollBackAlter: null,
      }}
      onSuccess={function (): void {
        throw new Error('Function not implemented.');
      }}
    />
  );
};
