import React, { useMemo } from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';
import type {
  AnnualPerformanceObjectiveLocales,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import { PERFORMANCE_BASIC_VERSION } from '@manyun/hrm.util.performances';

export type AnnualPerformanceObjectiveTypeProps = {
  type: BackendAnnualPerformanceObjectiveType;
  textVersion?: keyof AnnualPerformanceObjectiveLocales['type']['textMapper'];
};

const colorMapper: Record<BackendAnnualPerformanceObjectiveType, TagProps['color']> = {
  DAILY: 'geekblue',
  YEAR: 'cyan',
  RED_LINE: 'error',
  DAY_PLUS: 'cyan',
};

export const AnnualPerformanceObjectiveType = ({
  type,
  textVersion = PERFORMANCE_BASIC_VERSION,
}: AnnualPerformanceObjectiveTypeProps) => {
  const locales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);

  return (
    <Tag color={colorMapper[type]}>
      {locales.type.textMapper[textVersion][type] ??
        locales.type.textMapper[PERFORMANCE_BASIC_VERSION][type]}
    </Tag>
  );
};
