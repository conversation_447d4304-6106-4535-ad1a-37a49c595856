import dayjs from 'dayjs';
import React, { useCallback, useMemo } from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type {
  AnnualPerformanceObjectiveJSON,
  AnnualPerformanceObjectiveLocales,
  MergedMetricAnnualPerformanceObjectiveJSON,
} from '@manyun/hrm.model.annual-performance-objective';
import {
  AnnualPerformanceObjective,
  getAnnualPerformanceObjectiveLocales,
} from '@manyun/hrm.model.annual-performance-objective';
import { PERFORMANCE_BASIC_VERSION } from '@manyun/hrm.util.performances';
import { UserLink } from '@manyun/iam.ui.user-link';

import { AnnualPerformanceObjectiveType } from './components/performance-annual-object-type';
import { TableContainer } from './styles';

export type ColumnDataIndex =
  | 'index'
  | 'name'
  | 'type'
  | 'positions'
  | 'resources'
  | 'measurements'
  | 'gradeCriteria'
  | 'status'
  | 'createdBy'
  | 'createdAt'
  | 'modifiedBy'
  | 'modifiedAt';

export type AnnualPerformanceObjectiveTableProps = {
  showColumns: ColumnDataIndex[];
  operation?: ColumnType<MergedMetricAnnualPerformanceObjectiveJSON>;
  dataSource?: AnnualPerformanceObjectiveJSON[];
  maxWidth?: boolean;
  /**
   *指标类型文案版本
   */
  typeTextVersion?: keyof AnnualPerformanceObjectiveLocales['type']['textMapper'];
} & Omit<
  TableProps<AnnualPerformanceObjectiveJSON>,
  'columns' | 'expandedRowRender' | 'dataSource'
>;

export const useAnnualPerformanceObjectiveTableColumns = (
  showColumns: ColumnDataIndex[],
  filterColumns?: ColumnDataIndex[],
  maxWidth?: boolean,
  typeTextVersion?: keyof AnnualPerformanceObjectiveLocales['type']['textMapper']
) => {
  const locales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);
  const basicColumns: ColumnType<MergedMetricAnnualPerformanceObjectiveJSON>[] = React.useMemo(
    () => [
      {
        title: '序号',
        dataIndex: 'index',
        fixed: 'left',
        width: 64,
        render: (_, record, index) => `${record.index ?? index + 1}`,
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
      {
        title: locales.name,
        width: 148,
        dataIndex: 'name',
        disabled: true,
        fixed: 'left',
        render: (_, record) => (
          <Space direction="vertical">
            {record.name}
            {showColumns.includes('type') && (
              <AnnualPerformanceObjectiveType type={record.type} textVersion={typeTextVersion} />
            )}
          </Space>
        ),
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
      {
        title: locales.measurements,
        dataIndex: 'measurements',
        className: maxWidth ? 'max-width-measurements' : undefined,
        disabled: true,
        width: maxWidth ? undefined : '35%',
        render: (_, record) =>
          record.mergedMetrics?.context ? (
            <div style={{ whiteSpace: 'pre-line' }}> {record.mergedMetrics?.context}</div>
          ) : (record?.mergedMetrics?.legacyContexts ?? []).length > 0 ? (
            // 兼容历史数据结构的数据
            <Space
              style={{ width: '100%' }}
              size="middle"
              direction="vertical"
              split={<Divider style={{ width: '100%', padding: 0, margin: 0 }} type="horizontal" />}
            >
              {(record?.mergedMetrics?.legacyContexts ?? []).map(measurement => (
                <Space
                  key={measurement}
                  style={{ width: '100%', justifyContent: 'space-between' }}
                  align="center"
                >
                  <Typography.Title
                    style={{ fontWeight: 400, marginBottom: 0, fontSize: 14 }}
                    level={5}
                    ellipsis={{ rows: 2, tooltip: { title: measurement } }}
                  >
                    <div style={{ whiteSpace: 'pre-line' }}>{measurement}</div>
                  </Typography.Title>
                </Space>
              ))}
            </Space>
          ) : (
            '--'
          ),
      },
      {
        title: locales.gradeCriteria,
        dataIndex: 'gradeCriteria',
        className: maxWidth ? 'max-width-criteria' : undefined,
        disabled: true,
        render: (_, record) => {
          return (
            <Space
              style={{ width: maxWidth ? '748px' : '100%' }}
              size="middle"
              direction="vertical"
              split={<Divider style={{ width: '100%', padding: 0, margin: 0 }} type="horizontal" />}
            >
              {(record?.mergedMetrics?.gradeCriteria ?? []).map(item => {
                const _gradeCriteria = `单次${record.type === 'DAILY' ? '减' : '加'}${(item.defaultGrade ?? []).map(grade => `${Math.abs(grade)}分`).join('或')}`;
                return (
                  <Space
                    key={item.id}
                    style={{ width: '100%', justifyContent: 'space-between' }}
                    align="center"
                  >
                    <div style={{ whiteSpace: 'pre-line' }}>{item.gradeCriteria}</div>
                    <Space>
                      {['YEAR', 'DAILY', 'DAY_PLUS'].includes(record.type) &&
                        item.defaultGrade &&
                        item.defaultGrade.length > 0 && (
                          <Tag style={{ marginRight: 0 }} color="default">
                            <Typography.Text
                              style={{ fontSize: 12, maxWidth: 96 }}
                              ellipsis={{ tooltip: _gradeCriteria }}
                            >
                              {_gradeCriteria}
                            </Typography.Text>
                          </Tag>
                        )}
                      {item.ceiling && (
                        <Tag style={{ marginRight: 0 }} color="default">
                          封顶加{item.ceiling}分
                        </Tag>
                      )}
                    </Space>
                  </Space>
                );
              })}
            </Space>
          );
        },
      },
      {
        title: locales.evalPositions,
        dataIndex: 'positions',
        disabled: true,
        width: 190,
        render: (_, record) => record.positions.map(({ label }) => label).join('｜'),
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
      {
        title: locales.createdBy.__self,
        dataIndex: 'createdBy',
        render: (val, record) => (
          <UserLink userId={record.createdBy.id} userName={record.createdBy.name} />
        ),
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
      {
        title: locales.createdAt,
        dataIndex: 'createdAt',
        render: (val, record) => dayjs(record.createdAt).format('YYYY-MM-DD HH:mm:ss'),
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
      {
        title: locales.modifiedBy.__self,
        dataIndex: 'modifiedBy',
        render: (val, record) => (
          <UserLink userId={record.modifiedBy.id} userName={record.modifiedBy.name} />
        ),
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
      {
        title: locales.modifiedAt,
        dataIndex: 'modifiedAt',
        render: (val, record) =>
          record.modifiedAt ? dayjs(record.modifiedAt).format('YYYY-MM-DD HH:mm:ss') : '--',
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      },
    ],
    [
      locales.createdAt,
      locales.createdBy.__self,
      locales.evalPositions,
      locales.gradeCriteria,
      locales.measurements,
      locales.modifiedAt,
      locales.modifiedBy.__self,
      locales.name,
      maxWidth,
      showColumns,
      typeTextVersion,
    ]
  );

  return [
    basicColumns.filter(item =>
      item.dataIndex ? !(filterColumns ?? []).includes(item.dataIndex as ColumnDataIndex) : true
    ),
  ] as const;
};

export function AnnualPerformanceObjectiveTable({
  showColumns,
  operation,
  dataSource,
  maxWidth = false,
  typeTextVersion = PERFORMANCE_BASIC_VERSION,
  ...tableProps
}: AnnualPerformanceObjectiveTableProps) {
  const [basicColumns] = useAnnualPerformanceObjectiveTableColumns(
    showColumns,
    [],
    maxWidth,
    typeTextVersion
  );

  const getColumns = useCallback(() => {
    const columns = showColumns.reduce(
      (columns: ColumnType<AnnualPerformanceObjectiveJSON>[], clnKey) => {
        const column = basicColumns.find(cln => cln.dataIndex === clnKey);
        if (column) {
          columns.push(column);
        }
        return columns;
      },
      []
    );
    if (operation) {
      columns.push(operation);
    }
    return columns;
  }, [showColumns, operation, basicColumns]);

  return (
    <TableContainer>
      <Table
        rowKey="rowKey"
        {...tableProps}
        dataSource={AnnualPerformanceObjective.generateMergedMetricAnnualPerformanceObjectives(
          dataSource ?? []
        )}
        columns={getColumns()}
      />
    </TableContainer>
  );
}
