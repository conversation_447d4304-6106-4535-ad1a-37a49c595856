import styled from 'styled-components';

export const UserShiftScheduleContainer = styled.div`
  .userShiftScheduleContainer {
    .userPunchesContainer {
      margin: 18px 0 10px;
      display: grid;
      grid-template-columns: repeat(5, 1fr);
      grid-column-gap: 10px;

      .manyun-card-body {
        cursor: pointer;
      }

      .manyun-statistic-content {
        display: flex;
        align-items: center;
      }

      .manyun-statistic-content-value {
        font-size: 30px;
      }

      .manyun-statistic-content-prefix {
        display: flex;
        align-items: center;
      }
    }

    .fc-scrollgrid-section-liquid .fc-scroller-liquid-absolute {
      overflow: visible;
    }
    .fc-daygrid-event {
      cursor: text;
    }

    .userShiftScheduleCell {
      .cellRow {
      }
    }
  }
`;
