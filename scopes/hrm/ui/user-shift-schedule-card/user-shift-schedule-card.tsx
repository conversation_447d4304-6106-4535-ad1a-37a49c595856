/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-12
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useRef, useState } from 'react';

import ClockCircleOutlined from '@ant-design/icons/es/icons/ClockCircleOutlined';
import classNames from 'classnames';
import type { Moment } from 'moment';
import moment from 'moment';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { FullCalendarReact } from '@manyun/base-ui.ui.full-calendar';
import { FullCalendar } from '@manyun/base-ui.ui.full-calendar';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import type { StatisticProps } from '@manyun/base-ui.ui.statistic';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { SvcQuery } from '@manyun/auth-hub.service.fetch-user-punches';
import type { BackendPunchesStatistics } from '@manyun/auth-hub.service.fetch-user-punches-statistics-by-month';
import { fetchUserPunchesStatisticsByMonth } from '@manyun/auth-hub.service.fetch-user-punches-statistics-by-month';
import type { BackendUserShiftSchedule } from '@manyun/auth-hub.service.fetch-user-shift-schedule';
import {
  PunchTimeType,
  ScheduleSceneStatus,
  fetchUserShiftSchedule,
} from '@manyun/auth-hub.service.fetch-user-shift-schedule';
import { BPM_INSTANCES_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { Link, useNavigate } from '@manyun/dc-brain.navigation.link';
import { SUPPLY_CHECK_NEW_ROUTE_PATH } from '@manyun/hrm.route.hrm-routes';

import type { StatisticType } from './components/user-punches-statistic-modal';
import { UserPunchesStatisticModal } from './components/user-punches-statistic-modal';
import { UserShiftScheduleContainer } from './styles';

export type UserShiftScheduleCardProps = {
  userId: number;
};
export function UserShiftScheduleCard({ userId: id }: UserShiftScheduleCardProps) {
  const calendarRef = useRef<FullCalendarReact>(null);
  const [userScheduleList, setUserScheduleList] = useState<BackendUserShiftSchedule[]>([]);
  const [userPunchesStatistic, setUserPunchesStatistic] = useState<BackendPunchesStatistics[]>([]);
  const [loadingStatistic, setLoadingStatistic] = useState(false);
  const [loadingShift, setLoadingShift] = useState(false);

  const [currentDate, setCurrentDate] = useState<moment.Moment>(moment());

  const dateStr = currentDate.format('YYYYMM');

  const startTime = moment(dateStr)
    .startOf('month')
    .add('-6', 'days')
    .valueOf(); /**当前月开始时间 - 6 */
  const endTime = moment(dateStr)
    .endOf('month')
    .add('13', 'days')
    .valueOf(); /**当前月结束时间 + 13*/

  const monthStartTime = moment(dateStr).startOf('month').valueOf(); /**当前月开始时间  */
  const monthEndTime =
    dateStr === moment(new Date()).format('YYYYMM')
      ? moment(new Date()).endOf('day').valueOf()
      : moment(dateStr)
          .endOf('month')
          .valueOf(); /** 当前月结束时间 当月逻辑:统计截至日期应该为当前日期前一天 */

  const _fetchUserShift = useCallback(async () => {
    setLoadingShift(true);
    const { error, data } = await fetchUserShiftSchedule({
      startDate: startTime,
      endDate: endTime,
      staffId: id,
    });

    if (error) {
      message.error(error?.message);
      setLoadingShift(false);
      return;
    }

    setLoadingShift(false);
    setUserScheduleList(data.data);
  }, [endTime, id, startTime]);

  const _fetchUserPunchesStatic = useCallback(async () => {
    setLoadingStatistic(true);
    const { error, data } = await fetchUserPunchesStatisticsByMonth({
      startTime: monthStartTime,
      endTime: monthEndTime,
      staffId: id,
    });

    if (error) {
      message.error(error?.message);
      setLoadingStatistic(false);
      return;
    }
    setLoadingStatistic(false);
    setUserPunchesStatistic(data.data);
  }, [id, monthEndTime, monthStartTime]);

  useEffect(() => {
    if (currentDate && id) {
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentDate, id]);

  const loadData = useCallback(() => {
    _fetchUserShift();
    _fetchUserPunchesStatic();
  }, [_fetchUserPunchesStatic, _fetchUserShift]);

  const generateEvents = useCallback(() => {
    const evensArray: {
      title: string;
      id: string;
      start: string;
      end?: string;
      borderColor: string;
      backgroundColor: string;
      display: string;
    }[] = [];

    const _startDate = moment(startTime);
    while (_startDate.valueOf() <= endTime) {
      const _date = _startDate.clone().format('YYYY-MM-DD HH:mm');

      const existingSpanScheduleDate = userScheduleList.some(
        item =>
          moment(item.scheduleEndTime).format('YYYY-MM-DD') ===
          _startDate.clone().format('YYYY-MM-DD')
      );
      const schedule = userScheduleList.find(
        item =>
          moment(item.scheduleDate).format('YYYY-MM-DD') === _startDate.clone().format('YYYY-MM-DD')
      );

      if (existingSpanScheduleDate && !schedule) {
        // 如果只是其他排班的结束日期，不需要生成事件
      } else {
        evensArray.push(
          schedule
            ? {
                title: schedule.dutyName,
                id: schedule.id.toString(),
                start: moment(schedule.scheduleStartTime).format('YYYY-MM-DD HH:mm'),
                end: moment(schedule.scheduleEndTime).format('YYYY-MM-DD HH:mm'),
                borderColor: 'transparent',
                backgroundColor: 'transparent',
                display: 'block',
              }
            : {
                title: _date,
                id: _date,
                start: _date,
                end: _date,
                borderColor: 'transparent',
                backgroundColor: 'transparent',
                display: 'block',
              }
        );
      }
      _startDate.add(1, 'day'); // 增加一天
    }
    return evensArray;
  }, [endTime, startTime, userScheduleList]);
  // const generateEvents = useCallback(() => {
  //   const eventsArray = [];

  //   const _currentDate = moment(startTime);
  //   const endDate = moment(endTime);

  //   while (_currentDate.isSameOrBefore(endDate, 'day')) {
  //     const formattedDate = _currentDate.format('YYYY-MM-DD');
  //     const isSpanEndToday = userScheduleList.some(item =>
  //       moment(item.scheduleEndTime).isSame(formattedDate, 'day')
  //     );

  //     if (isSpanEndToday) {
  //       // 如果只是其他排班的结束日期，不需要生成事件
  //       // 注意：如果需要在此类情况下也生成特殊标记，可在这里处理
  //     } else {
  //       const schedule = userScheduleList.find(item =>
  //         moment(item.scheduleDate).isSame(formattedDate, 'day')
  //       );

  //       eventsArray.push(
  //         schedule
  //           ? {
  //               title: schedule.dutyName,
  //               id: schedule.id.toString(),
  //               start: schedule.scheduleStartTime,
  //               end: schedule.scheduleEndTime,
  //               borderColor: 'transparent',
  //               backgroundColor: 'transparent',
  //               display: 'block',
  //             }
  //           : {
  //               title: formattedDate,
  //               id: formattedDate,
  //               start: _currentDate.format('YYYY-MM-DD HH:mm'),
  //               end: _currentDate.add(1, 'day').format('YYYY-MM-DD HH:mm'), // 结束时间为次日开始，确保全天事件
  //               borderColor: 'transparent',
  //               backgroundColor: 'transparent',
  //               display: 'block',
  //             }
  //       );
  //     }
  //     _currentDate.add(1, 'day');
  //   }

  //   return eventsArray;
  // }, [endTime, startTime, userScheduleList]);

  if (!id) {
    return <Spin />;
  }

  return (
    <Card>
      <UserShiftScheduleContainer>
        <Space
          className="userShiftScheduleContainer"
          style={{ width: '100%' }}
          direction="vertical"
        >
          <Typography.Title level={5}>排班考勤</Typography.Title>
          <UserShiftCalenderHeader
            value={currentDate}
            endTime={monthEndTime}
            children={
              <UserPunchesStatistic
                loading={loadingStatistic}
                fields={{ startDate: monthStartTime, endDate: monthEndTime, staffId: id }}
                statisticInfo={
                  (userPunchesStatistic || []).length > 0
                    ? userPunchesStatistic.reduce(
                        (sum, data) => {
                          sum.attendanceDay += Number(data.attendanceDay ?? 0);
                          sum.absentTimes += Number(data.absentTimes ?? 0);
                          sum.lateTimes += Number(data.lateTimes ?? 0);
                          sum.earlyOffTimes += Number(data.earlyOffTimes ?? 0);
                          sum.missOffCardTimes += Number(data.missOffCardTimes ?? 0);
                          sum.missOnCardTimes += Number(data.missOnCardTimes ?? 0);
                          return sum;
                        },
                        {
                          attendanceDay: 0,
                          absentTimes: 0,
                          lateTimes: 0,
                          earlyOffTimes: 0,
                          missOffCardTimes: 0,
                          missOnCardTimes: 0,
                        } as BackendPunchesStatistics
                      )
                    : null
                }
              />
            }
            onChange={date => {
              if (calendarRef.current) {
                calendarRef.current.getApi().gotoDate(date.format('YYYY-MM-DD'));
              }
              setCurrentDate(date);
            }}
          />
          <FullCalendar
            ref={calendarRef}
            initialView="dayGridMonth"
            headerToolbar={false}
            /** 设置要展示的事项 */
            events={generateEvents()}
            /** 设置语言 */
            locale="zh-cn"
            /** 自定义cell */
            eventContent={({ event }: { event: { start: Moment } }) => {
              if (!event?.start) {
                return null;
              }
              return (
                <UserShiftCalenderDataCell
                  value={moment(event.start)}
                  list={userScheduleList}
                  currentDate={currentDate}
                  loadingData={loadingShift}
                />
              );
            }}
            editable={false}
            aspectRatio={2}
          />
        </Space>
      </UserShiftScheduleContainer>
    </Card>
  );
}

/**
 * CalenderCell
 * 日历单元格
 */
type MakeUpRequestConfrimPorps = {
  title: string;
  record: BackendUserShiftSchedule;
  type: 'ON' | 'OFF';
  children?: React.ReactNode;
};
function MakeUpRequestConfrim({ title, record, type, children }: MakeUpRequestConfrimPorps) {
  const navigate = useNavigate();

  return (
    <Popconfirm
      title={title}
      okText="补卡"
      onConfirm={() => {
        const path = generateSupplyCheckURL({
          scheduleDate: record.scheduleDate,
          dutyId: record.dutyId,
          type: type,
        });
        navigate(path);
      }}
    >
      {children}
    </Popconfirm>
  );
}

const ColorsMapper: Record<string, string> = {
  error: 'var(--manyun-error-color)',
  warning: 'var(--manyun-warning-color)',
  abnormal: 'var(--manyun-warning-color)',
  success: 'var(--manyun-success-color)',
  default: '#BFBFBF',
};

type CheckStatus = 'success' | 'default' | 'warning' | 'error' | 'abnormal';
type UserShiftCalenderDataCellProps = {
  value: moment.Moment;
  list: BackendUserShiftSchedule[];
  currentDate: moment.Moment /**当前选中的年月 */;
  loadingData?: boolean;
};

export function UserShiftCalenderDataCell({
  value,
  list,
  loadingData,
}: UserShiftCalenderDataCellProps) {
  const str = value.format('YYYYMMDD');
  const cellDate = moment(str).unix() * 1000;
  const cellData = list.filter(item => item.scheduleDate === cellDate);
  const nowDate = moment(moment().format('YYYYMMDD')).unix() * 1000;

  const onCountTime = (cell: BackendUserShiftSchedule, type: 'on' | 'off') => {
    let diff = 0;
    if (type === 'on') {
      diff = Number(cell.onDutyTime || 0) - Number(cell.scheduleStartTime);
    } else {
      diff = Number(cell.scheduleEndTime) - Number(cell.offDutyTime || 0);
    }
    return diff / 60000; /**毫秒转分钟 */
  };

  // const disableCell = value.format('YYYYMM') !== currentDate.format('YYYYMM');

  const getSpecialDesc = (status: ScheduleSceneStatus) => {
    let desc: React.ReactNode = null;
    if (ScheduleSceneStatus.LEAVE_OUT_PROCESS === status) {
      desc = (
        <Space style={{ margin: '4px 0' }} size={4}>
          <Typography>休班</Typography>
          <Tag color="warning">审批中</Tag>
        </Space>
      );
    }
    if (ScheduleSceneStatus.EXCHANGE_PROCESS === status) {
      desc = (
        <Space style={{ margin: '4px 0' }} size={4}>
          <Typography>换班</Typography>
          <Tag color="warning">审批中</Tag>
        </Space>
      );
    }
    return desc;
  };

  /**单条考勤数据渲染 */
  const SingleCellDataRender = (cell: BackendUserShiftSchedule) => {
    const isPastTime = cellDate <= nowDate; /**是否是过去的时间 */
    let onDutyLateMinutes = 0; /**上班迟到时差 */
    let offDutyEarlyMimutes = 0; /**下班早退时差 */
    let status: CheckStatus = 'default';
    let needCheckTime = false;
    if (
      cell.scheduleScene?.code &&
      [ScheduleSceneStatus.LEAVE_OUT_PROCESS, ScheduleSceneStatus.EXCHANGE_PROCESS].includes(
        cell.scheduleScene?.code
      )
    ) {
      status = 'warning'; /**待审核 */
    } else if (
      cell.scheduleScene?.code &&
      [ScheduleSceneStatus.EXCHANGE, ScheduleSceneStatus.LEAVE_OUT].includes(
        cell.scheduleScene?.code
      )
    ) {
      status = 'success'; /**休息 */
    } else {
      if (isPastTime) {
        onDutyLateMinutes = onCountTime(cell, 'on');
        offDutyEarlyMimutes = onCountTime(cell, 'off');
        if (onDutyLateMinutes > 0 || offDutyEarlyMimutes > 0) {
          status = 'error';
        } else {
          status = 'success';
        }
        needCheckTime = true;
      } else {
        status = 'default';
      }
    }

    const sceneName =
      isPastTime && [ScheduleSceneStatus.LEAVE_IN].includes(cell.scheduleScene?.code)
        ? cell.scheduleScene?.desc
        : '';
    /**
     * 是否为休班或换班
     */
    const isRest = ScheduleSceneStatus.LEAVE_OUT === cell.scheduleScene?.code && !cell.enable;
    const isSpanDay =
      moment(cell.scheduleStartTime).startOf('day').valueOf() !==
      moment(cell.scheduleEndTime).startOf('day').valueOf();

    const dutyName = `${cell.dutyName}: ${
      isPastLeaveOut(cell)
        ? isSpanDay
          ? moment(cell.scheduleStartTime).format('HH:mm') +
            '- 次日' +
            moment(cell.scheduleEndTime).format('HH:mm')
          : moment(cell.scheduleStartTime).format('HH:mm') +
            '-' +
            moment(cell.scheduleEndTime).format('HH:mm')
        : cell.timeInterval
    }${sceneName}`;

    let onDutyPunchTimeContent;
    let onDutyPunchTimeState: CheckStatus = 'default';
    let offDutyPunchTimeContent;
    let offDutyPunchTimeState: CheckStatus = 'default';
    if (needCheckTime === true) {
      /**上班考勤状态 */
      if (cell.onDutySupplyCheckScene != null) {
        switch (cell.onDutySupplyCheckScene.code) {
          case PunchTimeType.APPROVING:
            onDutyPunchTimeState = 'warning';
            break;
          case PunchTimeType.PASS:
            onDutyPunchTimeState = 'success';
            break;
          default:
            break;
        }
        onDutyPunchTimeContent =
          getSpecialDesc(cell.scheduleScene?.code) ?? cell.onDutySupplyCheckScene.desc;
      } else {
        onDutyPunchTimeContent =
          cell.onDutyTime != null ? moment(cell.onDutyTime).format('HH:mm') : '未打卡';

        if (!cell.onDutyTime) {
          onDutyPunchTimeState = 'error';
        } else if (onDutyLateMinutes > 0) {
          onDutyPunchTimeState = 'abnormal';
        } else {
          onDutyPunchTimeState = 'success';
        }
      }

      /**下班考勤状态 */
      if (cell.offDutySupplyCheckScene != null) {
        switch (cell.offDutySupplyCheckScene.code) {
          case PunchTimeType.APPROVING:
            offDutyPunchTimeState = 'warning';
            break;
          case PunchTimeType.PASS:
            offDutyPunchTimeState = 'success';
            break;
          default:
            break;
        }
        offDutyPunchTimeContent =
          getSpecialDesc(cell.scheduleScene?.code) ?? cell.offDutySupplyCheckScene.desc;
      } else {
        offDutyPunchTimeContent =
          cell.offDutyTime != null ? moment(cell.offDutyTime).format('HH:mm') : '未打卡';

        if (!cell.offDutyTime) {
          offDutyPunchTimeState = 'error';
        } else if (offDutyEarlyMimutes > 0) {
          offDutyPunchTimeState = 'abnormal';
        } else {
          offDutyPunchTimeState = 'success';
        }
      }
    }

    return (
      <>
        <div className="cellRow">
          <Tooltip title={dutyName}>
            <Tag
              style={{ width: '100%' }}
              color={isPastTime ? (isRest ? 'default' : 'processing') : 'default'}
            >
              {dutyName}
            </Tag>
          </Tooltip>
        </div>

        {needCheckTime === false && (
          <>
            {['warning', 'success'].includes(status) ? (
              <Space style={{ padding: '0 8px' }} size={8}>
                <Badge color={ColorsMapper[status]} />
                <Link href={generateApproveURL(BIZ_TYPE_TRANSFORM[cell.scheduleScene?.code])}>
                  <Typography.Text>
                    {isPastLeaveOut(cell) ? (
                      `部分请假${cell.systemChange ? '(订正)' : ''}`
                    ) : (
                      <>
                        {getSpecialDesc(cell.scheduleScene?.code) ?? cell.scheduleScene?.desc}
                        {cell.systemChange ? '(订正)' : ''}
                      </>
                    )}
                  </Typography.Text>
                </Link>
              </Space>
            ) : null}
          </>
        )}

        {needCheckTime === true && (
          <div className="cellRow">
            <Space style={{ padding: '0 8px' }} size={8}>
              <Badge color={ColorsMapper[onDutyPunchTimeState]} />
              <Typography.Text>上班:</Typography.Text>
              <div>
                {['abnormal', 'error'].includes(onDutyPunchTimeState) ? (
                  <MakeUpRequestConfrim
                    title={
                      cell.onDutyTime == null
                        ? '未打卡,需要补卡？'
                        : `迟到${onDutyLateMinutes.toFixed(0)}分钟，需要补卡？`
                    }
                    type="ON"
                    record={cell}
                  >
                    <Typography.Text>{onDutyPunchTimeContent}</Typography.Text>
                  </MakeUpRequestConfrim>
                ) : ['warning', 'success'].includes(onDutyPunchTimeState) &&
                  cell.onDutySupplyCheckScene ? (
                  <Link
                    href={generateApproveURL(BIZ_TYPE_TRANSFORM[cell.onDutySupplyCheckScene.code])}
                  >
                    <Typography.Text>{onDutyPunchTimeContent}</Typography.Text>
                  </Link>
                ) : (
                  <Typography.Text>{onDutyPunchTimeContent}</Typography.Text>
                )}
              </div>
            </Space>
          </div>
        )}

        {needCheckTime === true && (
          <div className="cellRow">
            <Space style={{ padding: '0 8px' }} size={8}>
              <Badge color={ColorsMapper[offDutyPunchTimeState]} />
              <Typography.Text>下班:</Typography.Text>
              <Space size={2}>
                <div>
                  {['abnormal', 'error'].includes(offDutyPunchTimeState) ? (
                    <MakeUpRequestConfrim
                      title={
                        cell.offDutyTime == null
                          ? '未打卡,需要补卡？'
                          : `早退${offDutyEarlyMimutes.toFixed(0)}分钟，需要补卡？`
                      }
                      record={cell}
                      type="OFF"
                    >
                      <Typography.Text>{offDutyPunchTimeContent}</Typography.Text>
                    </MakeUpRequestConfrim>
                  ) : ['warning', 'success'].includes(offDutyPunchTimeState) &&
                    cell.offDutySupplyCheckScene ? (
                    <Link
                      href={generateApproveURL(
                        BIZ_TYPE_TRANSFORM[cell.offDutySupplyCheckScene.code]
                      )}
                      // style={{
                      //   color: disableCell ? 'inherit' : ColorsMapper[offDutyPunchTimeState],
                      // }}
                    >
                      <Typography.Text>{offDutyPunchTimeContent}</Typography.Text>
                    </Link>
                  ) : (
                    <Typography.Text>{offDutyPunchTimeContent}</Typography.Text>
                  )}
                </div>
              </Space>
            </Space>
          </div>
        )}

        {cell.outWorkRecordVOs?.length && (
          <div className="cellRow">
            <Space style={{ padding: '0 8px' }} size={8}>
              <Tag color="success">外勤</Tag>
            </Space>
          </div>
        )}
      </>
    );
  };

  if (loadingData) {
    return <Spin spinning />;
  }
  if (cellData.length === 0) {
    return (
      <div className="userShiftScheduleCell">
        <Space style={{ padding: '0 8px' }} size={8}>
          <Badge status="default" />
          <Typography.Text>休息</Typography.Text>
        </Space>
      </div>
    );
  }
  return (
    <div className={classNames('userShiftScheduleCell')}>
      {cellData.map(cell => (
        <div key={cell.id} style={{ marginBottom: '4px' }}>
          {SingleCellDataRender(cell)}
        </div>
      ))}
    </div>
  );
}

const isPastLeaveOut = (cell: BackendUserShiftSchedule) => {
  return cell.scheduleScene.code === ScheduleSceneStatus.LEAVE_OUT && cell.enable;
};

type UserShiftCalenderHeaderProps = {
  value: moment.Moment;
  endTime: number /**统计日期 */;
  children?: React.ReactNode;
  onChange: (date: moment.Moment) => void;
};
export function UserShiftCalenderHeader({
  value,
  endTime,
  onChange,
  children,
}: UserShiftCalenderHeaderProps) {
  const month = value.month();
  const year = value.year();

  return (
    <div style={{ padding: 8 }}>
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: 5,
        }}
      >
        <Space>
          <DatePicker
            style={{ width: 100 }}
            picker="year"
            format={value => `${value.format('YYYY')}年`}
            value={moment().set('year', Number(year))}
            allowClear={false}
            onChange={value => {
              const nextYear = moment(value).get('year');
              const newDate = new Date(nextYear, month, 1);
              onChange(moment(newDate));
            }}
          />
          <Select
            style={{ width: 58 }}
            value={month}
            options={Array.from({ length: 12 }, (_, index) => index).map(key => ({
              label: key + 1,
              value: key,
            }))}
            allowClear={false}
            onChange={value => {
              const nextMonth = value;
              const newDate = new Date(year, nextMonth, 1);
              onChange(moment(newDate));
            }}
          />
        </Space>
        <div style={{ flex: 1 }} />
        <ClockCircleOutlined style={{ color: 'var(--text-color-secondary)' }} />
        <Typography.Text type="secondary">
          统计截止日期 {moment(endTime).format('YYYY-MM-DD')}
        </Typography.Text>
      </div>
      {children}
    </div>
  );
}

/***
 *  Statistic
 *  统计信息
 */
type UserPunchesStatisticProps = {
  loading?: boolean;
  statisticInfo: BackendPunchesStatistics | null;
  fields: SvcQuery;
};
type StatisticItem = {
  title: string;
  formatter: StatisticProps['formatter'];
  value: number;
  type: StatisticType;
};

export function UserPunchesStatistic({
  loading,
  statisticInfo,
  fields,
}: UserPunchesStatisticProps) {
  const [visible, setVisible] = useState(false);
  const [selectedType, setSelectedType] = useState<StatisticType>('attendance');
  const StatisticItems: StatisticItem[] = [
    {
      title: '出勤次数',
      value: statisticInfo?.attendanceDay || 0,
      type: 'attendance',
      formatter: value => <Typography.Text type="success">{value}</Typography.Text>,
    },
    {
      title: '旷工次数',
      value: statisticInfo?.absentTimes || 0,
      type: 'absent',
      formatter: value => <Typography.Text type="danger">{value}</Typography.Text>,
    },
    {
      title: '迟到次数',
      value: statisticInfo?.lateTimes || 0,
      type: 'arriving-late',
      formatter: value => <Typography.Text type="warning">{value}</Typography.Text>,
    },
    {
      title: '早退次数',
      value: statisticInfo?.earlyOffTimes || 0,
      type: 'leave-early',
      formatter: value => <Typography.Text type="warning">{value}</Typography.Text>,
    },
    {
      title: '缺卡次数',
      value: statisticInfo
        ? Number(statisticInfo.missOffCardTimes || 0) + Number(statisticInfo.missOnCardTimes || 0)
        : 0,
      type: 'missing-card',
      formatter: value => <Typography.Text type="danger">{value}</Typography.Text>,
    },
  ];

  const onClickCard = (item: StatisticItem) => {
    if (item.value === 0) {
      return;
    }
    setVisible(true);
    setSelectedType(item.type);
  };

  return (
    <div className="userPunchesContainer">
      {StatisticItems.map(item => (
        <Card
          key={item.title}
          bodyStyle={{ padding: '10px 24px' }}
          onClick={() => onClickCard(item)}
        >
          <Statistic
            loading={loading}
            title={item.title}
            formatter={item.formatter}
            value={item.value}
            valueStyle={{
              color:
                item.value === 0 ? 'var(--text-color-secondary)' : 'var(--manyun-primary-color)',
            }}
          />
        </Card>
      ))}
      <UserPunchesStatisticModal
        visible={visible}
        type={selectedType}
        fields={fields}
        onClose={() => {
          setVisible(false);
        }}
      />
    </div>
  );
}

export const generateSupplyCheckURL = ({
  scheduleDate,
  dutyId,
  type,
}: {
  scheduleDate: number;
  dutyId: number;
  type: 'ON' | 'OFF';
}) => {
  return `${SUPPLY_CHECK_NEW_ROUTE_PATH}?type=${type}`;
};

export const generateApproveURL = (
  bizType: 'SUPPLY_CHECK_PROCESS' | 'LEAVE_PROCESS' | 'EXCHANGE_PROCESS',
  status?: 'ALL' | 'COMPLETE' | 'APPROVING' | 'REVOKE' | 'PASS'
) => {
  let search = `?type=MINE&bizType=${bizType}`;
  if (status) {
    search += `&status=${status}`;
  }
  return BPM_INSTANCES_ROUTE_PATH + search;
};

/**
 * @constant  考勤状态和审批类型的转换变量
 */
const BIZ_TYPE_TRANSFORM: Record<
  string,
  'SUPPLY_CHECK_PROCESS' | 'LEAVE_PROCESS' | 'EXCHANGE_PROCESS'
> = {
  APPROVING: 'SUPPLY_CHECK_PROCESS',
  PASS: 'SUPPLY_CHECK_PROCESS',
  LEAVE_OUT: 'LEAVE_PROCESS',
  LEAVE_OUT_PROCESS: 'LEAVE_PROCESS',
  EXCHANGE: 'EXCHANGE_PROCESS',
  EXCHANGE_PROCESS: 'EXCHANGE_PROCESS',
};
