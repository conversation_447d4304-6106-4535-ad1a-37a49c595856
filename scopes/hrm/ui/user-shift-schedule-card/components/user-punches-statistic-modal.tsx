import React, { useEffect, useState } from 'react';

import moment from 'moment';
import shortid from 'shortid';

import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType } from '@manyun/base-ui.ui.table';

import type { BackendUserPunche, SvcQuery } from '@manyun/auth-hub.service.fetch-user-punches';
import { fetchUserPunches } from '@manyun/auth-hub.service.fetch-user-punches';
import { Link } from '@manyun/dc-brain.navigation.link';

import { generateSupplyCheckURL } from '../user-shift-schedule-card';

enum PuncheType {
  ONDUTY = 'ONDUTY',
  OFFDUTY = 'OFFDYTU',
}

const PUNCHETYPE_MAPPER: Record<PuncheType, string> = {
  [PuncheType.ONDUTY]: '上班',
  [PuncheType.OFFDUTY]: '下班',
};

type UserPunche = Pick<
  BackendUserPunche,
  | 'id'
  | 'blockTag'
  | 'scheduleDate'
  | 'attGroupId'
  | 'scheduleEndTime'
  | 'scheduleStartTime'
  | 'dutyId'
> & {
  uniqId?: string /**唯一 */;
  puncheTime?: number | null /**打卡时间 */;
  puncheType?: PuncheType /**打卡类型 */;
  differTime?: number /**相差时间 */;
};

export type StatisticType =
  | 'attendance'
  | 'absent'
  | 'arriving-late'
  | 'leave-early'
  | 'missing-card';

export type UserPunchesStatisticModalProps = {
  visible: boolean;
  type: StatisticType;
  fields?: SvcQuery;
  onClose: () => void;
};

export function UserPunchesStatisticModal({
  visible,
  type,
  fields,
  onClose,
}: UserPunchesStatisticModalProps) {
  const [list, setList] = useState<UserPunche[]>([]);
  const [loaing, setLoaing] = useState(false);

  let title = '';
  switch (type) {
    case 'attendance':
      title = '出勤次数';
      break;
    case 'absent':
      title = '旷工次数';
      break;
    case 'arriving-late':
      title = '迟到次数';
      break;
    case 'leave-early':
      title = '早退次数';
      break;
    case 'missing-card':
      title = '缺卡次数';
      break;
    default:
      break;
  }

  useEffect(() => {
    if (visible) {
      setList([]);
      loadData();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [visible, fields]);

  const loadData = async () => {
    setLoaing(true);
    const { data, error } = await fetchUserPunches({ ...fields });
    if (error) {
      message.error(error.message);
      setLoaing(false);
      return;
    }
    setLoaing(false);
    onHandleFilterRecord(data.data);
  };

  const onHandleFilterRecord = (records: BackendUserPunche[]) => {
    const __list: UserPunche[] = [];
    switch (type) {
      case 'attendance' /**出勤 */:
        records.forEach(record => {
          if (record.onDutyTime != null) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
              uniqId: shortid(),
              puncheTime: record.onDutyTime,
              puncheType: PuncheType.ONDUTY,
            };
            __list.push(__record);
          }
          if (record.offDutyTime != null) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
              uniqId: shortid(),
              puncheTime: record.offDutyTime,
              puncheType: PuncheType.OFFDUTY,
            };
            __list.push(__record);
          }
        });
        break;
      case 'absent' /**矿工 */:
        records.forEach(record => {
          if (record.missOffCard && record.missOnCard) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              uniqId: shortid(),
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
            };
            __list.push(__record);
          }
        });
        break;
      case 'arriving-late' /**迟到次数 */:
        records.forEach(record => {
          if (record.lateOnMinutes > 0) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
              puncheTime: record.onDutyTime,
              differTime: record.lateOnMinutes,
              puncheType: PuncheType.ONDUTY,
              uniqId: shortid(),
            };
            __list.push(__record);
          }
        });
        break;
      case 'leave-early' /**早退次数 */:
        records.forEach(record => {
          if (record.earlyOffMinutes > 0) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
              puncheTime: record.offDutyTime,
              differTime: record.earlyOffMinutes,
              puncheType: PuncheType.OFFDUTY,
              uniqId: shortid(),
            };
            __list.push(__record);
          }
        });
        break;
      case 'missing-card' /**缺卡次数 */:
        records.forEach(record => {
          if (record.missOnCard) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
              uniqId: record.id + 'ONDUTY',
              puncheType: PuncheType.ONDUTY,
            };
            __list.push(__record);
          }
          if (record.missOffCard) {
            const __record: UserPunche = {
              id: record.id,
              dutyId: record.dutyId,
              attGroupId: record.attGroupId,
              blockTag: record.blockTag,
              scheduleDate: record.scheduleDate,
              scheduleStartTime: record.scheduleStartTime,
              scheduleEndTime: record.scheduleEndTime,
              uniqId: shortid(),
              puncheType: PuncheType.OFFDUTY,
            };
            __list.push(__record);
          }
        });
        break;
      default:
        break;
    }
    setList(__list);
  };

  const getColumns = () => {
    switch (type) {
      case 'attendance':
        return BaseColumns.concat(AttendanceColumns);
      case 'absent':
        return BaseColumns.concat(AbsentColumns);
      case 'leave-early':
        return BaseColumns.concat(LeaveEarlyColumns);
      case 'arriving-late':
        return BaseColumns.concat(LateColumns);
      case 'missing-card':
        return BaseColumns.concat(MissingColumns);
      default:
        return [];
    }
  };

  return (
    <Modal width={920} title={title} visible={visible} footer={null} onCancel={onClose}>
      <Table
        loading={loaing}
        size="small"
        rowKey="uniqId"
        dataSource={list}
        columns={getColumns()}
      />
    </Modal>
  );
}

const BaseColumns: ColumnsType<UserPunche> = [
  {
    title: '编号',
    dataIndex: '_action',
    ellipsis: true,
    render: (value, record, index) => `${index + 1}`,
  },
  {
    title: '位置',
    dataIndex: 'blockTag',
    ellipsis: true,
  },
  {
    title: '考勤日期',
    dataIndex: 'scheduleDate',
    ellipsis: true,
    render: value => moment(value).format('YYYY-MM-DD'),
  },
];

const BaseOperateColumn: ColumnsType<UserPunche> = [
  {
    title: '操作',
    width: 100,
    dataIndex: '__action',
    render: (value, record) => (
      <Link
        href={generateSupplyCheckURL({
          scheduleDate: record.scheduleDate,
          dutyId: record.dutyId,
          type: record.puncheType === PuncheType.ONDUTY ? 'ON' : 'OFF',
        })}
      >
        补卡
      </Link>
    ),
  },
];

const AttendanceColumns: ColumnsType<UserPunche> = [
  {
    title: '打卡时间',
    dataIndex: 'puncheTime',
    ellipsis: true,
    width: 170,
    render: value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : null),
  },
  {
    title: '打卡类型',
    dataIndex: 'puncheType',
    ellipsis: true,
    render: (value: PuncheType) => PUNCHETYPE_MAPPER[value],
  },
];

const AbsentColumns: ColumnsType<UserPunche> = [];

const LateColumns: ColumnsType<UserPunche> = [
  {
    title: '上班时间',
    dataIndex: 'scheduleStartTime',
    ellipsis: true,
    width: 170,
    render: value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : ''),
  },
  {
    title: '打卡时间',
    dataIndex: 'puncheTime',
    ellipsis: true,
    width: 170,
    render: value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : null),
  },
  {
    title: '迟到时长',
    dataIndex: 'differTime',
    ellipsis: true,
    render: value => `${value}分钟`,
  },
  ...BaseOperateColumn,
];

const LeaveEarlyColumns: ColumnsType<UserPunche> = [
  {
    title: '下班时间',
    dataIndex: 'scheduleEndTime',
    ellipsis: true,
    width: 170,
    render: value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : ''),
  },
  {
    title: '打卡时间',
    dataIndex: 'puncheTime',
    ellipsis: true,
    width: 170,
    render: value => (value ? moment(value).format('YYYY-MM-DD HH:mm:ss') : ''),
  },
  {
    title: '早退时长',
    dataIndex: 'differTime',
    ellipsis: true,
    render: value => `${value}分钟`,
  },
  ...BaseOperateColumn,
];

const MissingColumns: ColumnsType<UserPunche> = [
  {
    title: '缺卡类型',
    dataIndex: 'puncheType',
    ellipsis: true,
    render: (value: PuncheType) => PUNCHETYPE_MAPPER[value],
  },
  ...BaseOperateColumn,
];
