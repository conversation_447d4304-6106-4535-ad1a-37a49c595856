/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-4
 *
 * @packageDocumentation
 */
import React from 'react';

import { AlterType } from '@manyun/hrm.service.fetch-shifts-for-leave-request';

import { ScheduleShiftByUserSelect } from './schedule-shift-by-user-select';

export function BasicScheduleShiftByUserSelect() {
  return (
    <ScheduleShiftByUserSelect
      params={{
        type: AlterType.Leave,
        shiftDate: 0,
        staffId: 0,
      }}
    />
  );
}
