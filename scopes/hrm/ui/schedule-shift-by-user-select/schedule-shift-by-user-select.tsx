/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-4
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchShiftsForLeaveRequest } from '@manyun/hrm.service.fetch-shifts-for-leave-request';
import type { SvcQuery } from '@manyun/hrm.service.fetch-shifts-for-leave-request';

export type ScheduleShiftByUserSelectProps = {
  trigger?: 'onFocus' | 'onDidMount';
  params: SvcQuery;
} & Omit<SelectProps, 'options'>;

export function ScheduleShiftByUserSelect({
  trigger = 'onDidMount',
  params,
  ...props
}: ScheduleShiftByUserSelectProps) {
  const [data, setData] = useState<{ label: string; value: number }[]>([]);
  const [loading, setLoading] = useState(false);

  const getScheduleShifts = useCallback(async () => {
    if (!params.type || !params.shiftDate || !params.staffId) {
      return;
    }
    setLoading(true);
    const { error, data } = await fetchShiftsForLeaveRequest({ ...params });
    setLoading(false);
    if (error) {
      message.error(error?.message);
      setData([]);
      return;
    }
    setData(data.map(shift => ({ label: shift.name ?? '', value: shift.id })));
  }, [params]);

  useEffect(() => {
    if (trigger === 'onDidMount') {
      getScheduleShifts();
    }
  }, [getScheduleShifts, trigger]);

  return (
    <Select
      {...props}
      loading={loading}
      options={data}
      onFocus={() => {
        if (trigger === 'onFocus') {
          getScheduleShifts();
        }
      }}
    />
  );
}
