import React, { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { AttGroup, fetchAttGroups } from '@manyun/hrm.service.fetch-att-groups';

type RefSelectProps = {
  focus: () => void;
  blur: () => void;
};

export type AttGroupSelectProps<VT = any> = {
  trigger?: 'onFocus' | 'onDidMount';
  blockGuids?: string[];
} & SelectProps<VT>;

export const AttGroupSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', blockGuids, ...selectProps }: AttGroupSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [loading, setLoaing] = useState(false);
    const [data, setData] = useState<AttGroup[]>([]);

    const _fetchAttGroups = useCallback(async () => {
      setLoaing(true);
      const { error, data } = await fetchAttGroups({ blockGuids });
      setLoaing(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setData(data.data);
    }, [blockGuids]);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        _fetchAttGroups();
      }
    }, [_fetchAttGroups, trigger]);

    return (
      <Select
        ref={ref as any}
        showSearch
        optionLabelProp="attName"
        optionFilterProp="attName"
        {...selectProps}
        loading={loading}
        options={data as any}
        onFocus={() => {
          if (trigger === 'onFocus') {
            _fetchAttGroups();
          }
        }}
      />
    );
  }
);

AttGroupSelect.displayName = 'AttGroupSelect';

AttGroupSelect.defaultProps = {
  fieldNames: {
    value: 'id',
    label: 'attName',
  },
};
