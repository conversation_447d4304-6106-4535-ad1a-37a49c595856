import React, { useMemo } from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';

import type { BackendPerformanceResult } from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';

const colorMapper: Record<BackendPerformanceResult, TagProps['color']> = {
  UN_PASS: 'error',
  PASS: 'success',
};

export const PerformanceResultTag = ({ result }: { result: BackendPerformanceResult }) => {
  const locales = useMemo(() => getPerformanceLocales(), []);
  return <Tag color={colorMapper[result]}>{locales.result.enum[result]}</Tag>;
};
