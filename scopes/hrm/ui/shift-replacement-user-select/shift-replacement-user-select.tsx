/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-4
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchShiftReplacementUsers } from '@manyun/hrm.service.fetch-shift-replacement-users';
import type { SvcQuery } from '@manyun/hrm.service.fetch-shift-replacement-users';

export type ShiftReplacementUserSelectProps = {
  trigger?: 'onFocus' | 'onDidMount';
  params: SvcQuery;
} & Omit<SelectProps, 'options'>;

export function ShiftReplacementUserSelect({
  trigger = 'onDidMount',
  params,
  ...props
}: ShiftReplacementUserSelectProps) {
  const [data, setData] = useState<{ label: string; value: number }[]>([]);
  const [loading, setLoading] = useState(false);

  const getReplacementUsers = useCallback(async () => {
    if (!params.type || !params.shiftDate || !params.staffId || !params.shiftId) {
      return;
    }
    setLoading(true);
    const { error, data } = await fetchShiftReplacementUsers({ ...params });
    setLoading(false);
    if (error) {
      message.error(error?.message);
      setData([]);
      return;
    }
    setData(data.map(user => ({ label: user.name, value: user.id })));
  }, [params]);

  useEffect(() => {
    if (trigger === 'onDidMount') {
      getReplacementUsers();
    }
  }, [getReplacementUsers, trigger]);

  return (
    <Select
      {...props}
      loading={loading}
      options={data}
      onFocus={() => {
        if (trigger === 'onFocus') {
          getReplacementUsers();
        }
      }}
    />
  );
}
