import orderBy from 'lodash.orderby';
import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import type { CardProps } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type {
  AnnualPerformanceObjectiveJSON,
  AnnualPerformanceObjectiveLocales,
} from '@manyun/hrm.model.annual-performance-objective';
import { AnnualPerformanceObjectiveTable } from '@manyun/hrm.ui.annual-performance-objective-table';
import { PERFORMANCE_BASIC_VERSION } from '@manyun/hrm.util.performances';

export type AnnualPerformanceObjectiveCardProps = {
  children?: React.ReactNode;
  dataSource: AnnualPerformanceObjectiveJSON[];
  /**
   * 指标类型文案版本
   */
  typeTextVersion?: keyof AnnualPerformanceObjectiveLocales['type']['textMapper'];
} & Pick<CardProps, 'title' | 'extra' | 'style' | 'className' | 'loading' | 'bodyStyle'>;

export function AnnualPerformanceObjectiveCard({
  dataSource,
  children,
  title,
  extra,
  loading,
  typeTextVersion = PERFORMANCE_BASIC_VERSION,
  ...cardProps
}: AnnualPerformanceObjectiveCardProps) {
  return (
    <Card {...cardProps} bordered={false}>
      <Space style={{ width: '100%', display: 'flex' }} size="middle" direction="vertical">
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            {title}
          </Typography.Title>
          {extra}
        </Space>
        {children}
        <AnnualPerformanceObjectiveTable
          loading={loading}
          dataSource={orderBy(dataSource, ['type'], ['asc'])}
          showColumns={['index', 'type', 'name', 'measurements', 'gradeCriteria']}
          typeTextVersion={typeTextVersion}
          pagination={false}
        />
      </Space>
    </Card>
  );
}
