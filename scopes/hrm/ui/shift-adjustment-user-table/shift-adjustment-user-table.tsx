import React from 'react';

import dayjs from 'dayjs';

import { Table } from '@manyun/base-ui.ui.table';

import { UserLink } from '@manyun/auth-hub.ui.user';

export type ShiftAdjustmentUserInfo = {
  scheduleDate: number;
  staffId?: number;
  staffName?: string;
  dutyName: string;
};

export type ShiftAdjustmentUserInfoTableProps = {
  data: ShiftAdjustmentUserInfo[];
};

export function ShiftAdjustmentUserTable({ data }: ShiftAdjustmentUserInfoTableProps) {
  return (
    <Table
      columns={[
        {
          title: '日期',
          dataIndex: 'scheduleDate',
          render: val => dayjs(val).format('YYYY-MM-DD'),
        },
        {
          title: '班次',
          dataIndex: 'dutyName',
        },
        {
          title: '顶班人',
          dataIndex: 'staffName',
          render: (val, record) =>
            record.staffId ? <UserLink id={record.staffId} name={val} /> : '--',
        },
      ]}
      dataSource={data}
    />
  );
}
