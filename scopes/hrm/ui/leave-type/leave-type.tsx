import React from 'react';

import { getShiftAdjustmentTicketLocales } from '@manyun/hrm.model.shift-adjustment-ticket';
import { LeaveRequestType } from '@manyun/hrm.model.shift-adjustment-ticket';

export type LeaveTypeTextProps = {
  code: LeaveRequestType;
};

export function LeaveTypeText({ code }: LeaveTypeTextProps) {
  const mapper = React.useMemo(() => {
    const locales = getShiftAdjustmentTicketLocales();
    return locales.leaveType.enum;
  }, []);

  if (!mapper[code]) {
    return <>--</>;
  }
  return <>{mapper[code]}</>;
}
