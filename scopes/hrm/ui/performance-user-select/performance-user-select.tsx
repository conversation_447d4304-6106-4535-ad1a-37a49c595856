import debounce from 'lodash.debounce';
import uniqBy from 'lodash.uniqby';
import React, { useCallback, useEffect, useState } from 'react';
import { useSelector } from 'react-redux';

import type { UserJSON } from '@manyun/auth-hub.model.user';
import { selectMe } from '@manyun/auth-hub.state.user';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchPerformanceUsers } from '@manyun/hrm.service.fetch-performance-users';

export type PerformanceUserSelectProps<T = string | { value: string; label: string }> = {
  includeCurrentUser?: boolean;
} & SelectProps<T>;

type UserInfo = Pick<UserJSON, 'id' | 'name' | 'login'>;

export const PerformanceUserSelect = React.forwardRef(
  (
    {
      onChange,
      value,
      labelInValue,
      includeCurrentUser = true,
      ...selectProps
    }: PerformanceUserSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
    const [users, setUsers] = useState<UserInfo[]>([]);

    const searchHandler = useCallback(
      async (keyword?: string) => {
        const { error, data } = await fetchPerformanceUsers({ key: keyword });

        if (error) {
          message.error(error.message);
          return;
        }

        setUsers(
          uniqBy(data, 'id').filter(user => (!includeCurrentUser ? user.id !== userId : true))
        );
      },
      [includeCurrentUser, userId]
    );

    useEffect(() => {
      if (value) {
        searchHandler(typeof value === 'object' && 'label' in value ? value.label : value);
      }
    }, [searchHandler, value]);

    const onSearch = debounce(searchHandler, 200);

    return (
      <Select
        ref={ref}
        placeholder="请根据用户id,用户名或邮箱搜索"
        {...selectProps}
        labelInValue={labelInValue}
        value={value}
        showSearch
        filterOption={false}
        optionLabelProp="title"
        onSearch={onSearch}
        onChange={onChange}
      >
        {users.map(user => (
          <Select.Option key={user.id} value={user.id} title={user.name}>
            <div>
              <Typography.Text>{user.name}</Typography.Text>
              {user.login && <br />}
              {user.login && <Typography.Text type="secondary">{user.login}</Typography.Text>}
            </div>
          </Select.Option>
        ))}
      </Select>
    );
  }
);

PerformanceUserSelect.displayName = 'PerformanceUserSelect';
