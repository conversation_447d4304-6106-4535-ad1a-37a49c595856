import React, { useMemo } from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';

import type {
  BackendPerformanceObjectiveStatus,
  BackendPerformanceType,
} from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';

export type GoalsStatusTagProps = {
  pfType?: BackendPerformanceType;
  status: BackendPerformanceObjectiveStatus;
};

const colorMapper: Record<string, TagProps['color']> = {
  TARGET_FINISHED: 'success',
  TARGET_WAIT_SUPERIOR: 'processing',
  TARGET_BACK: 'error',
  TARGET_SELF_SETTING: 'warning',
  TARGET_WAIT_SEC_SUPERIOR: 'warning',
  TARGET_INIT: 'default',
} as const;

export function GoalsStatusTag({ pfType, status }: GoalsStatusTagProps) {
  const locales = useMemo(() => getPerformanceLocales(), []);

  if (!status) {
    return null;
  }

  return (
    <Tag color={colorMapper[status]}>
      {locales.pfType[pfType === 'TP' ? 'test' : 'annual'].objectiveStatus.enum[status]}
    </Tag>
  );
}
