/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-12-18
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchPagedDutyGroup } from '@manyun/hrm.service.fetch-paged-duty-group';
import type { BackendDutyGroup } from '@manyun/hrm.service.fetch-paged-duty-group';

export type DutyGroupSelectProps = {
  blockGuidList?: string[];
  trigger?: 'onFocus' | 'onDidMount';
} & Omit<SelectProps, 'options' | 'onFocus' | 'loading'>;

export const DutyGroupSelect = React.forwardRef(
  (
    { blockGuidList, trigger = 'onFocus', ...restProps }: DutyGroupSelectProps,
    ref?: React.ForwardedRef<RefSelectProps>
  ) => {
    const [dutyGroups, setDutyGroups] = useState<BackendDutyGroup[]>([]);
    const [loading, setLoading] = useState(false);

    const getDutyGroup = useCallback(async () => {
      setLoading(true);
      const { data, error } = await fetchPagedDutyGroup({
        pageNum: 1,
        pageSize: 100,
        blockGuids: blockGuidList,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setDutyGroups(data.data);
    }, [blockGuidList]);

    useEffect(() => {
      if (trigger === 'onDidMount') {
        getDutyGroup();
      }
    }, [getDutyGroup, trigger]);

    return (
      <Select
        ref={ref}
        style={{ width: 312 }}
        showSearch
        optionFilterProp="label"
        {...restProps}
        loading={loading}
        options={dutyGroups.map(dutyGroup => ({
          label: dutyGroup.groupName,
          value: dutyGroup.id,
        }))}
        onFocus={() => {
          if (trigger === 'onFocus') {
            getDutyGroup();
          }
        }}
      />
    );
  }
);

DutyGroupSelect.displayName = 'DutyGroupSelect';
