import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import type { PopconfirmProps } from '@manyun/base-ui.ui.popconfirm';

import type { BackendPerformanceSubType } from '@manyun/hrm.model.performance';
import { revokePerformance } from '@manyun/hrm.service.revoke-performance';

export type RevokePerformanceButtonProps = {
  /**
   * 绩效id
   */
  id: number;
  /**
   * 目标 、绩效
   */
  subType: BackendPerformanceSubType;
  onSuccess?: () => void;
  confirmTitle: PopconfirmProps['title'];
};

/**绩效目标/自评撤回 */
export const RevokePerformanceButton = ({
  id,
  subType,
  onSuccess,
  confirmTitle,
}: RevokePerformanceButtonProps) => {
  return (
    <Popconfirm
      title={confirmTitle}
      okText="确认撤回"
      onConfirm={async () => {
        const { error } = await revokePerformance({
          id,
          subType,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('撤回成功');
        onSuccess?.();
      }}
    >
      <Button type="primary">撤回</Button>
    </Popconfirm>
  );
};
