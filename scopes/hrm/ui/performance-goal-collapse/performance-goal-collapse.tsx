import React, { useEffect, useMemo, useState } from 'react';

import dayjs from 'dayjs';

import { Collapse } from '@manyun/base-ui.ui.collapse';
import { Space } from '@manyun/base-ui.ui.space';
import type { SpaceProps } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';

import styles from './performance-goal-collapse.module.less';

export type PerformanceGoalCollapseProps = {
  goal: PerformanceObjectiveJSON;
  /**
   * 是否展开
   */
  active?: boolean;
  hideDefaultExtra?: boolean;
  extra?: React.ReactNode;
};

export const PerformanceGoalCollapse = ({
  goal,
  active = false,
  hideDefaultExtra = false,
  extra,
}: PerformanceGoalCollapseProps) => {
  const locales = useMemo(() => getPerformanceObjectiveLocales(), []);
  const [activeKey, setActiveKey] = useState<string[]>([]);

  useEffect(() => {
    setActiveKey(active ? [goal.id.toString()] : []);
  }, [active, goal.id]);

  return (
    <Collapse
      className={styles.goalCollapse}
      collapsible="header"
      activeKey={activeKey}
      ghost
      onChange={key => {
        setActiveKey(key as string[]);
      }}
    >
      <Collapse.Panel
        key={goal.id}
        header={
          <Typography.Title style={{ marginBottom: 0 }} level={5}>
            {goal.title}
          </Typography.Title>
        }
        extra={
          <Space size="large">
            {!hideDefaultExtra && (
              <Space size="middle">
                <DescItem label={locales.percent} value={`${goal.percent ?? 0}%`} />
                {goal.status && (
                  <DescItem label={locales.status._self} value={locales.status.enum[goal.status]} />
                )}
              </Space>
            )}
            {extra}
          </Space>
        }
      >
        <Space direction="vertical" size="middle">
          <DescItem direction="vertical" label={locales.content} value={goal.content} />
          {goal.measurements && (
            <DescItem direction="vertical" label={locales.measurements} value={goal.measurements} />
          )}

          <Space size={40}>
            <DescItem
              label={locales.finishedAt}
              value={dayjs(goal.finishedAt).format('YYYY-MM-DD')}
            />
            <DescItem
              label={locales.modifiedAt}
              value={dayjs(goal.modifiedAt).format('YYYY-MM-DD')}
            />
            <DescItem
              label={locales.startedAt}
              value={dayjs(goal.startedAt).format('YYYY-MM-DD')}
            />
          </Space>
        </Space>
      </Collapse.Panel>
    </Collapse>
  );
};

const DescItem = ({
  label,
  value,
  direction,
}: { label: React.ReactNode; value: string } & Pick<SpaceProps, 'direction'>) => {
  return (
    <Space direction={direction}>
      <Typography.Text>{label}:</Typography.Text>
      <Typography.Text style={{ whiteSpace: 'pre-line' }} type="secondary">
        {value}
      </Typography.Text>
    </Space>
  );
};
