import React, { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { ShiftJSON } from '@manyun/hrm.model.shift';
import { fetchShifts } from '@manyun/hrm.service.fetch-shifts';

export type ShiftSelectProps<VT = unknown> = {
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps<VT>;

export const ShiftSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', ...selectProps }: ShiftSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [loading, setLoaing] = useState(false);
    const [data, setData] = useState<ShiftJSON[]>([]);

    const _fetchShifts = useCallback(async () => {
      setLoaing(true);
      const { error, data } = await fetchShifts();
      setLoaing(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setData(data.data);
    }, []);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        _fetchShifts();
      }
    }, [_fetchShifts, trigger]);

    return (
      <Select
        ref={ref}
        showSearch
        optionLabelProp="name"
        optionFilterProp="name"
        {...selectProps}
        loading={loading}
        options={data as { id: number; name: string }[]}
        onFocus={() => {
          if (trigger === 'onFocus') {
            _fetchShifts();
          }
        }}
      />
    );
  }
);

ShiftSelect.displayName = 'ShiftSelect';

ShiftSelect.defaultProps = {
  fieldNames: {
    value: 'id',
    label: 'name',
  },
};
