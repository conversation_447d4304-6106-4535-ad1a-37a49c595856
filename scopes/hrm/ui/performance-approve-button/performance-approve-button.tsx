import React, { useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { useLazyValidExistingApprovingDailyGrade } from '@manyun/hrm.gql.client.hrm';
import type {
  BackendEvaluationStepType,
  BackendPerformanceSubType,
} from '@manyun/hrm.model.performance';
import { handOverPerformance } from '@manyun/hrm.service.hand-over-performance';
import type { SvcQuery as ApprovePerformanceParams } from '@manyun/hrm.service.submit-performance-evaluation';

import { useApprovePerformance } from './use-approve-performance';

type PartialParams = Pick<
  ApprovePerformanceParams,
  'content' | 'result' | 'goalGrades' | 'goalGrades' | 'needAttention' | 'attentionContent'
>;

export type ApproveStatus =
  | 'TARGET_WAIT_SUPERIOR'
  | 'TARGET_WAIT_SEC_SUPERIOR'
  | 'TARGET_BACK'
  | 'TARGET_SELF_SETTING'
  | 'SELF_EVAL'
  | 'EVAL_WAIT_SUPERIOR'
  | 'EVAL_WAIT_SEC_SUPERIOR'
  | 'EVAL_WAIT_HR'
  | 'EVAL_WAIT_AREA_HR'
  | 'SELF_CONFIRM';

const statusMapper: Record<ApproveStatus, BackendEvaluationStepType> = {
  TARGET_BACK: 'TARGET_SELF_SETTING',
  TARGET_SELF_SETTING: 'TARGET_SELF_SETTING',
  TARGET_WAIT_SUPERIOR: 'TARGET_SUPERIOR',
  TARGET_WAIT_SEC_SUPERIOR: 'TARGET_SEC_SUPERIOR',
  SELF_EVAL: 'SELF_EVAL',
  EVAL_WAIT_SUPERIOR: 'EVAL_SUPERIOR',
  EVAL_WAIT_SEC_SUPERIOR: 'EVAL_SEC_SUPERIOR',
  EVAL_WAIT_HR: 'EVAL_HR',
  EVAL_WAIT_AREA_HR: 'EVAL_AREA_HR',
  SELF_CONFIRM: 'SELF_CONFIRM',
};

export type PerformanceButtonProps = {
  /**
   * 绩效id
   */
  bizId: number;
  /**
   * 当前绩效所在流程阶段
   */
  currentStep: ApproveStatus;
  text?: string;
  pfSubType?: BackendPerformanceSubType;
  onValidate?: () => Promise<PartialParams>;
  onSuccess: () => void;
} & Omit<ButtonProps, 'onClick'>;

/**
 * 绩效评估
 */
export const AgreePerformanceButton = ({
  bizId,
  currentStep,
  text = '同意',
  validExistingApprovingDailyGrade,
  existingApprovingDailyGradeWarningOnly,
  onValidate,
  onSuccess,
  ...restProps
}: PerformanceButtonProps & {
  /**
   * 是否校验是否存在审批中的日常评分
   */
  validExistingApprovingDailyGrade?: boolean;
  /**
   * 存在审批中的日常评分仅仅提示, 不阻止提交
   */
  existingApprovingDailyGradeWarningOnly?: boolean;
}) => {
  const [{ submitLoading }, { onSubmit }] = useApprovePerformance();
  const [fetchValidExistingApprovingDailyGrade, { loading, data }] =
    useLazyValidExistingApprovingDailyGrade();

  const existingApprovingDailyGrade = React.useMemo(
    () => data?.validExistingApprovingDailyGrade?.data,
    [data]
  );

  React.useEffect(() => {
    if (validExistingApprovingDailyGrade) {
      fetchValidExistingApprovingDailyGrade({
        variables: {
          performanceId: bizId.toString(),
        },
      });
    }
  }, [bizId, fetchValidExistingApprovingDailyGrade, validExistingApprovingDailyGrade]);

  const onHandleSubmit = async () => {
    if (
      validExistingApprovingDailyGrade &&
      existingApprovingDailyGrade &&
      !existingApprovingDailyGradeWarningOnly
    ) {
      message.error('该员工存在审批中的季度加分，请等待审批完成后再操作');
      return;
    }
    if (onValidate) {
      onValidate()
        .then(values => {
          onSubmit(
            {
              ...values,
              id: bizId,
              status: 'PASS',
              currentStep: statusMapper[currentStep],
            },
            onSuccess
          );
        })
        .catch(console.error);
    } else {
      onSubmit(
        {
          id: bizId,
          status: 'PASS',
          currentStep: statusMapper[currentStep],
        },
        onSuccess
      );
    }
  };

  if (existingApprovingDailyGrade && existingApprovingDailyGradeWarningOnly) {
    return (
      <Popconfirm
        placement="topRight"
        title={<div style={{ width: 260 }}>该员工存在审批中的季度加分，是否确认提交评估？</div>}
        okText="确认提交"
        cancelText="取消"
        okButtonProps={{
          loading: submitLoading || loading,
        }}
        onConfirm={() => {
          onHandleSubmit();
        }}
      >
        <Button {...restProps} loading={submitLoading || loading}>
          {text}
        </Button>
      </Popconfirm>
    );
  }

  return (
    <Button {...restProps} loading={submitLoading} onClick={() => onHandleSubmit()}>
      {text}
    </Button>
  );
};

/**
 * 绩效退回
 */
export const RefusePerformanceButton = ({
  bizId,
  currentStep,
  onSuccess,
  text = '退回',
  ...restProps
}: PerformanceButtonProps) => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [{ submitLoading }, { onSubmit }] = useApprovePerformance();

  return (
    <>
      <Button
        {...restProps}
        loading={submitLoading}
        onClick={() => {
          setOpen(true);
        }}
      >
        {text}
      </Button>
      <Modal
        title="退回"
        open={open}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          onSubmit(
            {
              id: bizId,
              status: 'REFUSE',
              content: {
                reason: form.getFieldValue('reason'),
              },
              currentStep: statusMapper[currentStep],
            },
            () => {
              setOpen(false);
              onSuccess();
            }
          );
        }}
      >
        <Form form={form}>
          <Form.Item
            label="退回原因"
            name="reason"
            rules={[{ whitespace: true, max: 100, message: '最多输入 100 个字符！' }]}
          >
            <Input.TextArea rows={3} showCount maxLength={100} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

/**
 * 绩效评估保存
 */
export const SavePerformanceButton = ({
  bizId,
  currentStep,
  text = '保存草稿',
  onValidate,
  onSuccess,
  ...restProps
}: PerformanceButtonProps) => {
  const [{ saveLoading }, { onSave }] = useApprovePerformance();

  return (
    <Button
      {...restProps}
      loading={saveLoading}
      onClick={() => {
        if (onValidate) {
          onValidate()
            .then(values => {
              onSave(
                {
                  ...values,
                  id: bizId,
                  currentStep: statusMapper[currentStep],
                },
                onSuccess
              );
            })
            .catch(console.error);
        } else {
          onSave(
            {
              id: bizId,
              currentStep: statusMapper[currentStep],
            },
            onSuccess
          );
        }
      }}
    >
      {text}
    </Button>
  );
};

/**
 * @description 转交
 */
export const TransformPerformanceButton = ({
  bizId,
  currentStep,
  onSuccess,
  pfSubType,
  ...restProps
}: PerformanceButtonProps) => {
  const [visible, setVisible] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<number | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);

  return (
    <>
      <Button
        {...restProps}
        onClick={() => {
          setVisible(true);
        }}
      >
        转交
      </Button>
      <Modal
        title="转交"
        open={visible}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setVisible(false);
        }}
        onOk={async () => {
          if (!selectedUserId) {
            message.error('转交人必填');
            return;
          }
          setSubmitLoading(true);
          const { error } = await handOverPerformance({
            infoIds: [bizId],
            transfereeId: selectedUserId,
            pfSubType: pfSubType!,
          });
          setSubmitLoading(false);

          if (error) {
            message.error(error.message);
            return;
          }
          setVisible(false);
          onSuccess();
        }}
      >
        <UserSelect
          style={{ width: 400 }}
          includeCurrentUser={false}
          labelInValue={false}
          authorized
          resourceTypes={['AREA', 'IDC', 'BUILDING']}
          onChange={value => {
            setSelectedUserId(value as number);
          }}
        />
      </Modal>
    </>
  );
};
