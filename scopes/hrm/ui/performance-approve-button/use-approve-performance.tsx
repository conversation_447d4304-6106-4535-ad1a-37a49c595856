import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { savePerformanceEvaluation } from '@manyun/hrm.service.save-performance-evaluation';
import type { SvcQuery as SavePerformanceParams } from '@manyun/hrm.service.save-performance-evaluation';
import { submitSubmitPerformanceEvaluation } from '@manyun/hrm.service.submit-performance-evaluation';
import type { SvcQuery as ApprovePerformanceParams } from '@manyun/hrm.service.submit-performance-evaluation';

export const useApprovePerformance = () => {
  const [submitLoading, setSubmitLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  const onSubmit = useCallback(async (params: ApprovePerformanceParams, onSuccess: () => void) => {
    setSubmitLoading(true);
    const { error } = await submitSubmitPerformanceEvaluation(params);
    setSubmitLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    onSuccess();
  }, []);

  const onSave = useCallback(async (params: SavePerformanceParams, onSuccess: () => void) => {
    setSaveLoading(true);
    const { error } = await savePerformanceEvaluation(params);
    setSaveLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    onSuccess();
  }, []);

  return [
    { submitLoading, saveLoading },
    { onSubmit, onSave },
  ] as const;
};
