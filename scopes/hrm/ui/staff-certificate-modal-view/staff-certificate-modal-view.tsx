/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-13
 *
 * @packageDocumentation
 */

/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { Empty } from '@manyun/base-ui.ui.empty';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { fetchCertList } from '@manyun/hrm.service.fetch-cert-list';
import { CertificatePreviewUpload } from '@manyun/hrm.ui.certificate-preview-upload';

import styles from './certificate-modal.module.less';

export type StaffCertificateModalViewProps = {
  userId: number;
  validCertCount?: number | undefined;
  allCertCount: number | undefined;
  excludeCustom?: boolean;
};

export type CoverEditCardProps = {
  id: number;
  userId: number;
  required?: boolean;
  certName: string;
  effectStartDate: string;
  effectEndDate: string;
  fileId: number;
  checkDate?: string;
  effect: boolean;
  fileType: string;
};

export const StaffCertificateModalView = ({
  userId,
  validCertCount,
  allCertCount,
  excludeCustom,
}: StaffCertificateModalViewProps) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const [certData, setCertData] = useState<CoverEditCardProps[]>([]);

  const _fetchCertList = async () => {
    const info = {
      userId: userId,
      excludeCustom,
    };
    setLoading(true);
    const { error, data } = await fetchCertList(info);
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }

    setCertData(data.data);
  };

  const showModal = () => {
    setVisible(true);
    _fetchCertList();
  };

  const handleOk = () => {
    setVisible(false);
  };
  const handleCancel = () => {
    setVisible(false);
  };
  return (
    <>
      <Button type="link" disabled={allCertCount === 0} onClick={showModal}>
        {`${validCertCount !== undefined ? `${validCertCount} /` : ''}${allCertCount ?? 0}`}
      </Button>
      <Modal
        title="资质证书"
        width={786}
        bodyStyle={{ maxHeight: '70vh', overflowY: 'auto' }}
        open={visible}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Spin spinning={loading}>
          <Space size={16} wrap>
            {certData?.map(item => {
              const status = getCertificationStatus(item);
              return (
                <div key={item.id} className={styles.certificateModalCover}>
                  <Card
                    style={{ width: 232, height: 213 }}
                    cover={
                      item.fileId !== null ? (
                        <CertificatePreviewUpload
                          id={item.id}
                          userId={item.userId}
                          fileId={item.fileId}
                          fileType={item.fileType}
                          isAuthEdit={false}
                        />
                      ) : (
                        <Container color="default" size="small">
                          <Empty description="暂无证书" />
                        </Container>
                      )
                    }
                  >
                    <Space style={{ width: '100%' }} direction="vertical">
                      <div
                        style={{
                          width: '100%',
                          justifyContent: 'space-between',
                          display: 'flex',
                          alignItems: 'center',
                        }}
                      >
                        <div style={{ width: 0, flex: 1, display: 'flex' }}>
                          {item?.required && <Typography.Text type="danger">*</Typography.Text>}
                          <Typography.Text
                            style={{ flex: 1, width: 0 }}
                            ellipsis={{ tooltip: item.certName }}
                          >
                            {item.certName}
                          </Typography.Text>
                        </div>
                        <Tag style={{ margin: 0 }} color={CertificationStatusColor[status]}>
                          {CertificationStatusText[status]}
                        </Tag>
                      </div>
                      <Typography.Text style={{ fontSize: 12 }} type="secondary">
                        有效期:
                        {item.effectStartDate && item.effectEndDate
                          ? `${item.effectStartDate}~${item.effectEndDate}`
                          : '--'}
                      </Typography.Text>
                    </Space>
                  </Card>
                </div>
              );
            })}
          </Space>
        </Spin>
      </Modal>
    </>
  );
};

enum CertificationStatus {
  /** 有效 */
  VALID = 'valid',
  /** 无效 */
  INVALID = 'invalid',
  /** 待上传 */
  PENDING = 'pending',
  /** 待审批 */
  TO_BE_APPROVED = 'approving',
}

const CertificationStatusColor: Record<string, string> = {
  [CertificationStatus.VALID]: 'success',
  [CertificationStatus.INVALID]: 'error',
  [CertificationStatus.PENDING]: 'warning',
  [CertificationStatus.TO_BE_APPROVED]: 'processing',
};

const CertificationStatusText: Record<string, string> = {
  [CertificationStatus.VALID]: '有效',
  [CertificationStatus.INVALID]: '无效',
  [CertificationStatus.PENDING]: '待上传',
  [CertificationStatus.TO_BE_APPROVED]: '审批中',
};

const getCertificationStatus = (cert: any): string => {
  if (cert.id === null) {
    return 'pending';
  }
  if (cert.id && cert.instStatus === 'APPROVING') {
    return 'approving';
  }
  if (cert.effectStartDate != null && cert.effectEndDate != null) {
    if (cert.effect) {
      return 'valid';
    } else {
      return 'invalid';
    }
  }
  return '';
};
