/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-15
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import moment from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';

import { BPM_INSTANCES_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { Link } from '@manyun/dc-brain.navigation.link';
import { generateBreakOffBalanceRoutePath } from '@manyun/hrm.route.hrm-routes';
import { fetchUserBalance } from '@manyun/hrm.service.fetch-user-balance';
import type { UserBalance } from '@manyun/hrm.service.fetch-user-balance';

export type UserHolidayBalanceCardProps = { userId: number; style?: React.CSSProperties };

type HolidayType = 'annual' | 'sickSalaryBalance' | 'paidLeaveBalance';
export function UserHolidayBalanceCard({ userId, style }: UserHolidayBalanceCardProps) {
  const [loading, setLoading] = useState<boolean>(false);
  const [userBalanceInfo, setUserBalanceInfo] = useState<UserBalance | null>(null);
  const [selectedType, setSelectedType] = useState<HolidayType>('annual');

  const _fetchUserBalance = useCallback(async () => {
    if (!userId) {
      return;
    }
    setLoading(true);
    const { error, data } = await fetchUserBalance({ staffId: userId });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setUserBalanceInfo(data);
  }, [userId]);

  useEffect(() => {
    _fetchUserBalance();
  }, [_fetchUserBalance]);

  const list: {
    title: string;
    key: HolidayType;
    unit: string;
    children: {
      title: string;
      span: number;
      value: number;
      approvalValue?: number;
      closeExpire?: boolean;
      formatter?: (value: number, unit: string, approvalValue?: number) => React.ReactNode;
      children?: React.ReactNode;
    }[];
  }[] = useMemo(
    () => [
      {
        title: ' 年假',
        key: 'annual',
        unit: '天',
        children: [
          {
            title: '当前余额',
            span: 12,
            value: userBalanceInfo?.totalAvailableAnnualLeave ?? 0,
            approvalValue: userBalanceInfo?.freezeAnnualLeave,
            closeExpire: (userBalanceInfo?.totalAvailableAnnualLeave ?? 0) > 0 && calcCloseExpire(),
            formatter: (value: number, unit: string, approvalValue?: number) => (
              <GenerateCurrentBalanceLink value={value} unit={unit} approvalValue={approvalValue} />
            ),
          },
          {
            title: '已使用',
            span: 6,
            value: userBalanceInfo?.alreadyUseAnnualLeave ?? 0,
            formatter: (value: number, unit: string) => (
              <GenerateUsedBalanceLink value={value} unit={unit} />
            ),
          },
          {
            title: '应享',
            span: 6,
            value: userBalanceInfo?.alreadyGiveAnnualLeave ?? 0,
            children: (
              <Space size={4} style={{ width: '100%' }} direction="vertical">
                <div>上年结转公司年假：{userBalanceInfo?.carryOverAnnualLeave ?? '--'}天</div>
                <div>今年发放法定年假：{userBalanceInfo?.alreadyGiveStatutory ?? '--'}天</div>
                <div>今年发放公司年假：{userBalanceInfo?.alreadyGiveCompany ?? '--'}天</div>
              </Space>
            ),
            formatter: (value: number, unit: string) => (
              <Space size={0}>
                {value}
                <Typography.Text style={{ fontSize: 14 }}> {unit}</Typography.Text>
              </Space>
            ),
          },
        ],
      },
      {
        title: '全薪病假',
        key: 'sickSalaryBalance',
        unit: '天',
        children: [
          {
            title: '当前余额',
            span: 12,
            value: userBalanceInfo?.sickSalaryBalance ?? 0,
            approvalValue: userBalanceInfo?.freezeSalaryBalance,
            closeExpire: (userBalanceInfo?.sickSalaryBalance ?? 0) > 0 && calcCloseExpire(),
            formatter: (value: number, unit: string, approvalValue?: number) => (
              <GenerateCurrentBalanceLink value={value} unit={unit} approvalValue={approvalValue} />
            ),
          },
          {
            title: '已使用',
            span: 6,
            value: userBalanceInfo?.alreadyUseSickSalary ?? 0,
            formatter: (value: number, unit: string) => (
              <GenerateUsedBalanceLink value={value} unit={unit} />
            ),
          },
          {
            title: '应享',
            span: 6,
            value: userBalanceInfo?.alreadyGiveSickSalary ?? 0,
            formatter: (value: number, unit: string) => (
              <Space size={0}>
                {value}
                <Typography.Text style={{ fontSize: 14 }}> {unit}</Typography.Text>
              </Space>
            ),
          },
        ],
      },
      {
        title: '调休',
        key: 'paidLeaveBalance',
        unit: '小时',
        children: [
          {
            title: '当前余额',
            span: 12,
            value: userBalanceInfo?.paidLeaveBalance ?? 0,
            approvalValue: userBalanceInfo?.freezePaidLeaveBalance,
            closeExpire: userBalanceInfo?.closeExpire,
            formatter: (value: number, unit: string, approvalValue?: number) => (
              <GenerateCurrentBalanceLink value={value} unit={unit} approvalValue={approvalValue} />
            ),
          },
          {
            title: '累计使用',
            span: 6,
            value: userBalanceInfo?.alreadyUsePaidLeave ?? 0,
            formatter: (value: number, unit: string) => (
              <GenerateUsedBalanceLink value={value} unit={unit} />
            ),
          },
          {
            title: '累计发放',
            span: 6,
            value: userBalanceInfo?.alreadyGivePaidLeave ?? 0,
            formatter: (value: number, unit: string) => {
              return Number(value) > 0 ? (
                <Link href={generateBreakOffBalanceRoutePath({ id: userId.toString() })}>
                  {value}
                  <Typography.Text style={{ fontSize: 14 }}> {unit}</Typography.Text>
                </Link>
              ) : (
                <Space size={0}>
                  {value}
                  <Typography.Text style={{ fontSize: 14 }}>{unit}</Typography.Text>
                </Space>
              );
            },
          },
        ],
      },
    ],
    [
      userBalanceInfo?.alreadyGiveAnnualLeave,
      userBalanceInfo?.alreadyGiveCompany,
      userBalanceInfo?.alreadyGivePaidLeave,
      userBalanceInfo?.alreadyGiveSickSalary,
      userBalanceInfo?.alreadyGiveStatutory,
      userBalanceInfo?.alreadyUseAnnualLeave,
      userBalanceInfo?.alreadyUsePaidLeave,
      userBalanceInfo?.alreadyUseSickSalary,
      userBalanceInfo?.carryOverAnnualLeave,
      userBalanceInfo?.closeExpire,
      userBalanceInfo?.freezeAnnualLeave,
      userBalanceInfo?.freezePaidLeaveBalance,
      userBalanceInfo?.freezeSalaryBalance,
      userBalanceInfo?.paidLeaveBalance,
      userBalanceInfo?.sickSalaryBalance,
      userBalanceInfo?.totalAvailableAnnualLeave,
      userId,
    ]
  );

  const currentContent = useMemo(
    () => list.find(item => item.key === selectedType),
    [list, selectedType]
  );

  if (loading) {
    return <Spin />;
  }

  return (
    <Card style={{ height: 192, ...style }} loading={!userId}>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Space style={{ width: '100%', justifyContent: 'space-between' }} align="center">
          <Typography.Title style={{ marginBottom: 0 }} level={5}>
            假期余额
          </Typography.Title>
          <Radio.Group
            size="small"
            value={selectedType}
            onChange={e => {
              if (e.target.value) {
                setSelectedType(e.target.value);
              }
            }}
          >
            {list.map(item => (
              <Radio.Button key={item.key} value={item.key}>
                {item.title}
              </Radio.Button>
            ))}
          </Radio.Group>
        </Space>
        {currentContent && (
          <Row gutter={16}>
            {currentContent.children.map(detail => (
              <Col key={detail.title} span={detail.span}>
                <Card
                  bodyStyle={{ height: 104, display: 'flex', alignItems: 'center' }}
                  size="small"
                >
                  <div
                    style={{
                      display: 'flex',
                      width: '100%',
                      justifyContent: 'space-between',
                    }}
                  >
                    <div
                      style={{
                        flex: 1,
                        width: '100%',
                        justifyContent: 'space-between',
                        display: 'flex',
                        alignItems: 'start',
                      }}
                    >
                      <Statistic
                        style={{ flex: 1 }}
                        title={
                          detail.children ? (
                            <Explanation iconType="question" tooltip={{ title: detail.children }}>
                              {detail.title}
                            </Explanation>
                          ) : (
                            <Space align="center">
                              {detail.title}
                              {detail.closeExpire && <Tag color="warning">快过期</Tag>}
                            </Space>
                          )
                        }
                        value={detail.value}
                        formatter={value =>
                          detail.formatter
                            ? detail.formatter(
                                Number(value),
                                currentContent.unit,
                                detail.approvalValue
                              )
                            : undefined
                        }
                      />
                    </div>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        )}
      </Space>
    </Card>
  );
}

const GenerateCurrentBalanceLink = ({
  value,
  unit,
  approvalValue,
}: {
  value: number;
  unit: string;
  approvalValue?: number;
}) => {
  return (
    <div
      style={{
        display: 'flex',
        maxWidth: '280px',
        alignItems: 'baseline',
        flexWrap: 'wrap',
        gap: '8px',
      }}
    >
      <Space size={0}>
        <Typography.Text type="warning">{value}</Typography.Text>
        <Typography.Text style={{ fontSize: 14 }}>{unit}</Typography.Text>
      </Space>
      {(approvalValue ?? 0) > 0 && (
        <Typography.Text style={{ fontSize: 12 }} type="secondary">
          含审批中{approvalValue}
          {unit}
        </Typography.Text>
      )}
    </div>
  );
};

const GenerateUsedBalanceLink = ({ value, unit }: { value: number; unit: string }) => {
  return Number(value) > 0 ? (
    <Link target="_blank" href={generateApproveURL('LEAVE_PROCESS', 'PASS')}>
      {value}
      <Typography.Text style={{ fontSize: 14 }}>{unit}</Typography.Text>
    </Link>
  ) : (
    <>
      {value}
      <Typography.Text style={{ fontSize: 14 }}>{unit}</Typography.Text>
    </>
  );
};

export function calcCloseExpire() {
  const nowDate = moment(new Date());
  const expireDate = moment(new Date()).endOf('year');
  return expireDate.diff(nowDate, 'day') <= 30;
}

export const generateApproveURL = (
  bizType: 'SUPPLY_CHECK_PROCESS' | 'LEAVE_PROCESS' | 'EXCHANGE_PROCESS',
  status?: 'ALL' | 'COMPLETE' | 'APPROVING' | 'REVOKE' | 'PASS'
) => {
  let search = `?type=MINE&bizType=${bizType}`;
  if (status) {
    search += `&status=${status}`;
  }
  return BPM_INSTANCES_ROUTE_PATH + search;
};
