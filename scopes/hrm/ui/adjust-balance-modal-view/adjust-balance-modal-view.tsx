import { EditOutlined, UploadOutlined } from '@ant-design/icons';
import { Typography } from 'antd';
import moment from 'moment';
import { nanoid } from 'nanoid';
import React, { useCallback, useMemo, useRef, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { BALANCE_TYPE_MAPPER } from '@manyun/hrm.service.fetch-balance-change-record';
import type { UserBalance } from '@manyun/hrm.service.fetch-paged-balance';
import { fetchUserBalanceByType } from '@manyun/hrm.service.fetch-user-balance-by-type';
import type { Balance } from '@manyun/hrm.service.fetch-user-balance-by-type';
import { updateBalance } from '@manyun/hrm.service.update-balance';

export type AdjustBalanceModalViewProps = {
  userId: number /**用户id**/;
  children?: React.ReactNode;
  currentBalance: number /**当前余额**/;
  className?: string;
  userBalance?: UserBalance;
  onSuccess?: (expireDate?: string) => void /**成功的回调函数**/;
} & (
  | {
      type: /**全薪病假*/ 'SICK_SALARY' | /**年假**/ 'ANNUAL_LEAVE';
    }
  | {
      type: 'PAID_LEAVE' /**调休 */;
      expireDate?: string;
    }
);

export function AdjustBalanceModalView({
  className,
  type,
  userId,
  userBalance,
  currentBalance,
  children,
  onSuccess,
  ...props
}: AdjustBalanceModalViewProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [authorized] = useAuthorized({ checkByCode: 'element_adjust-balance' });
  const [form] = Form.useForm();
  const attachments: McUploadFile[] | undefined = Form.useWatch('attachments', form);
  const balanceUnit = useMemo(() => (type === 'PAID_LEAVE' ? '小时' : '天'), [type]);
  const formIdRef = useRef<string | null>(null);
  const [balanceInfos, setBalanceInfos] = useState<Balance>({
    availableBalance: 0,
    balance: 0,
    unit: 'DAY',
  });
  const subBalanceType = Form.useWatch('subBalanceType', form);

  // 特殊逻辑：增加调休余额
  const isAddedPaidLeave = useMemo(() => {
    return type === 'PAID_LEAVE' && (!('expireDate' in props) || props.expireDate === undefined);
  }, [props, type]);

  const onLoadBalance = useCallback(
    async ({ type, expireDate }: { type: string; expireDate?: string }) => {
      const { error, data } = await fetchUserBalanceByType({ staffId: userId, type, expireDate });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data) {
        setBalanceInfos(data);
      }
    },
    [userId]
  );

  const availableAddMaxValue = useMemo(() => {
    if (type === 'ANNUAL_LEAVE') {
      switch (subBalanceType) {
        case 'COMPANY_LEAVE':
          return (userBalance?.companyBalance ?? 0) - (userBalance?.companyAvailableBalance ?? 0);
        case 'STATUTORY_ANNUAL_LEAVE':
          return (
            (userBalance?.statutoryBalance ?? 0) - (userBalance?.statutoryAvailableBalance ?? 0)
          );
        case 'CARRY_OVER_COMPANY_LEAVE':
          return (
            (userBalance?.carryOverBalance ?? 0) - (userBalance?.carryOverAvailableBalance ?? 0)
          );
        default:
          return undefined;
      }
    } else if (type === 'SICK_SALARY') {
      return (userBalance?.sickSalaryBalance ?? 0) - (userBalance?.sickSalaryAvailableBalance ?? 0);
    }

    return undefined;
  }, [
    subBalanceType,
    type,
    userBalance?.carryOverAvailableBalance,
    userBalance?.carryOverBalance,
    userBalance?.companyAvailableBalance,
    userBalance?.companyBalance,
    userBalance?.sickSalaryAvailableBalance,
    userBalance?.sickSalaryBalance,
    userBalance?.statutoryAvailableBalance,
    userBalance?.statutoryBalance,
  ]);

  return (
    <>
      {authorized && (
        <div
          onClick={e => {
            e.stopPropagation();
            e.nativeEvent.stopImmediatePropagation();
            formIdRef.current = generateFormId(userId.toString());

            if (!isAddedPaidLeave) {
              onLoadBalance({
                type,
                expireDate: 'expireDate' in props ? props.expireDate : undefined,
              });
            } else {
              form.setFieldsValue({
                adjustType: 'add',
              });
            }
            setVisible(true);
          }}
        >
          {isAddedPaidLeave ? (
            <Typography.Link>增加余额</Typography.Link>
          ) : (
            <EditOutlined style={{ color: 'var(--manyun-primary-color)' }} className={className} />
          )}
        </div>
      )}
      <Modal
        width={584}
        maskClosable={false}
        title={isAddedPaidLeave ? '增加调休余额' : `修改${type === 'PAID_LEAVE' ? '调休' : ''}余额`}
        open={visible}
        okButtonProps={{ loading: loading }}
        cancelButtonProps={{ disabled: loading }}
        afterClose={() => {
          form.resetFields();
        }}
        onOk={async e => {
          e.stopPropagation();
          form.validateFields().then(async values => {
            const balance =
              values.adjustType === 'reduce' ? -values.changedBalance : values.changedBalance;
            let expireDate;
            if (type === 'PAID_LEAVE') {
              expireDate = values.expireDate
                ? moment(values.expireDate).add(6, 'month').subtract(1, 'day').format('YYYY-MM-DD')
                : 'expireDate' in props
                  ? props.expireDate
                  : undefined;
            }
            if (!formIdRef.current) {
              return;
            }
            setLoading(true);
            const { error } = await updateBalance({
              staffId: userId,
              formId: formIdRef.current,
              changeBalance: balance,
              balanceType: type === 'ANNUAL_LEAVE' ? values.subBalanceType : type,
              expireDate,
              memo: values.memo,
              unit: balanceInfos.unit,
              originalBalance: balanceInfos.balance,
              fileInfoList: values.attachments,
            });
            setLoading(false);
            if (error) {
              message.error(error.message);
              return;
            }
            message.success('操作成功！');
            setVisible(false);
            onSuccess?.(expireDate);
          });
        }}
        onCancel={e => {
          e.stopPropagation();
          setVisible(false);
        }}
      >
        <Form form={form} labelCol={{ span: 4 }}>
          <Form.Item label="假期类型" name="subBalanceType" required>
            {type === 'ANNUAL_LEAVE' ? (
              <Select
                style={{ width: 216 }}
                options={[
                  { label: '当年公司年假', value: 'COMPANY_LEAVE' },
                  { label: '当年法定年假', value: 'STATUTORY_ANNUAL_LEAVE' },
                  {
                    label: '上年结转年假',
                    value: 'CARRY_OVER_COMPANY_LEAVE',
                  },
                ]}
                onChange={val => {
                  onLoadBalance({ type: val });
                }}
              />
            ) : (
              <>{BALANCE_TYPE_MAPPER[type] ?? type}</>
            )}
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }} name="content" label="修改余额" required>
            <Space>
              <Form.Item name="adjustType" rules={[{ required: true, message: '必选项' }]}>
                <Select
                  style={{ width: 104 }}
                  disabled={isAddedPaidLeave}
                  options={[
                    { label: '增加', value: 'add' },
                    { label: '减少', value: 'reduce' },
                  ]}
                />
              </Form.Item>
              <Form.Item
                name="changedBalance"
                dependencies={['adjustType', 'subBalanceType']}
                rules={[
                  { required: true, message: '必填项' },
                  ({ getFieldValue }) => {
                    return {
                      validator: (_, value) => {
                        if (
                          getFieldValue('adjustType') === 'reduce' &&
                          value &&
                          value > balanceInfos.availableBalance
                        ) {
                          const approvingBalance =
                            balanceInfos.balance - balanceInfos.availableBalance;
                          return Promise.reject(
                            `可用余额：${balanceInfos.availableBalance}${balanceUnit}
                            ${
                              approvingBalance > 0
                                ? `(审批中${approvingBalance}${balanceUnit})`
                                : ''
                            }`
                          );
                        } else if (
                          getFieldValue('adjustType') === 'add' &&
                          availableAddMaxValue !== undefined &&
                          value &&
                          value > availableAddMaxValue
                        ) {
                          return Promise.reject('增加后的总余额，不可大于该假期应享额度');
                        } else if (value && (value * 2) % 1 !== 0) {
                          return Promise.reject(`仅能输入0.5的整数倍的正数`);
                        }
                        return Promise.resolve();
                      },
                    };
                  },
                ]}
              >
                <InputNumber
                  style={{ width: 128 }}
                  max={20}
                  min={0.5}
                  precision={1}
                  step={0.5}
                  addonAfter={balanceUnit}
                />
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(pre, next) => {
              return pre.adjustType !== next.adjustType;
            }}
          >
            {({ getFieldValue }) => {
              if (isAddedPaidLeave && getFieldValue('adjustType') === 'add') {
                return (
                  <Form.Item
                    label="生效日期"
                    name="expireDate"
                    rules={[{ required: true, message: '生效日期必填' }]}
                  >
                    <DatePicker
                      style={{ width: 219 }}
                      disabledDate={current => {
                        return (
                          current &&
                          !current.isBetween(moment().subtract(6, 'month'), moment().endOf('day'))
                        );
                      }}
                      onChange={val => {
                        if (val) {
                          onLoadBalance({
                            type,
                            expireDate: val
                              .clone()
                              .add(6, 'month')
                              .subtract(1, 'day')
                              .format('YYYY-MM-DD'),
                          });
                        }
                      }}
                    />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
          <Form.Item
            name="memo"
            label="修改原因"
            rules={[
              {
                required: true,
                whitespace: true,
                message: '修改原因必填',
              },
              {
                type: 'string',
                max: 50,
                message: '最多输入 50 个字符！',
              },
            ]}
          >
            <Input.TextArea style={{ width: 344 }} />
          </Form.Item>
          <Form.Item
            label="附件"
            name="attachments"
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <McUpload
              showAccept
              accept=".png,.jpeg,.jpg,.xls,.xlsx,.doc,.docx,.pdf,.txt"
              showUploadList
              allowDelete
              maxCount={3}
            >
              <Button disabled={(attachments ?? []).length >= 3} icon={<UploadOutlined />}>
                点此上传
              </Button>
            </McUpload>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}

const generateFormId = (userId: string) => `${nanoid()}_${moment().valueOf()}_${userId}`;
