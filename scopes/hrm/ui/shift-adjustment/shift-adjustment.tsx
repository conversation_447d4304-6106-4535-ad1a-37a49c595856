import React from 'react';

import dayjs from 'dayjs';

import { Space } from '@manyun/base-ui.ui.space';

import type { ShiftAdjustmentTicketJSON } from '@manyun/hrm.model.shift-adjustment-ticket';
import { getShiftAdjustmentTicketLocales } from '@manyun/hrm.model.shift-adjustment-ticket';
import { LeaveTypeText } from '@manyun/hrm.ui.leave-type';

export type ShiftAdjustmentProps = {
  shiftAdjustment: ShiftAdjustmentTicketJSON;
};

export function ShiftAdjustment({ shiftAdjustment }: ShiftAdjustmentProps) {
  const locales = React.useMemo(() => getShiftAdjustmentTicketLocales(), []);
  const leaveInfo = shiftAdjustment.leaveInfo;

  const combineLeaveText = shiftAdjustment.combineLeaveTimeInfo
    ? shiftAdjustment.combineLeaveTimeInfo
        .map(
          index =>
            locales.leaveType.enum[index.leaveType] +
            index.takeTime +
            locales.leaveTimeUnit[index.unit]
        )
        .join('+')
    : '';

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={0}>
      {/* 请假 */}
      {leaveInfo && shiftAdjustment.type === 'LEAVE' && (
        <>
          {/* 请假统计信息 */}
          <Space size={2}>
            {shiftAdjustment.subType && (
              <>
                {locales.leaveType._self}：<LeaveTypeText code={shiftAdjustment.subType} />
              </>
            )}
            {shiftAdjustment.birthday && <>，子女出生日期：{shiftAdjustment.birthday}</>}
            {leaveInfo.startTimeStr &&
              leaveInfo.endTimeStr &&
              `，${locales.leaveTime}：${leaveInfo.startTimeStr}~${leaveInfo.endTimeStr}`}
            {(leaveInfo.totalTime ?? 0) > 0 &&
              `，${locales.leaveTimeLong}：${leaveInfo.totalTime}${
                leaveInfo.unit && locales.leaveTimeUnit[leaveInfo.unit]
              }`}
            {shiftAdjustment.combineLeave && `，${locales.useCombinedLeave}：${combineLeaveText}`}
          </Space>
        </>
      )}
      {/* 顶班 */}
      {shiftAdjustment.type === 'REST' && (
        <Space size={2}>
          {(shiftAdjustment.shiftAdjustments ?? [])
            .map(shift => dayjs(shift.replaceDate).format('YYYY-MM-DD'))
            .join('，')}
        </Space>
      )}
      {/*  换班 */}
      {shiftAdjustment.type === 'EXCHANGE' &&
        (shiftAdjustment.shiftAdjustments ?? []).map(shift => {
          const shiftTitle =
            locales.type.enum[
              ['REST'].includes(shiftAdjustment.type) ? shiftAdjustment.type : shift.applyType
            ];
          const replaceUserTitle =
            locales.type.enum[['REST'].includes(shiftAdjustment.type) ? 'REST' : shift.applyType];

          return (
            <Space key={`${shiftAdjustment.bizId}.${JSON.stringify(shift)}`} size={2}>
              {/* 班次时间 */}
              {`${shiftTitle}${locales.shiftTime}：${dayjs(shift.replaceDate).format(
                'YYYY-MM-DD'
              )}`}
              {/* 班次名称 */}
              {`，${shiftTitle}${locales.shiftName}：${shift.dutyName}`}
              {/* 班次顶替人 */}
              {shift.inUserName &&
                `，${replaceUserTitle}${locales.replacePerson}：${shift.inUserName}`}
            </Space>
          );
        })}
    </Space>
  );
}
