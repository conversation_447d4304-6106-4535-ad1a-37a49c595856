import React, { useMemo } from 'react';

import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';

import type {
  BackendPerformanceEvaluationStatus,
  BackendPerformanceType,
} from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';

export type EvaluateStatusTagProps = {
  pfType?: BackendPerformanceType;
  status: BackendPerformanceEvaluationStatus;
};

const colorMapper: Record<string, TagProps['color']> = {
  EVAL_FINISHED: 'success',
  EVAL_WAIT_SUPERIOR: 'processing',
  EVAL_WAIT_SEC_SUPERIOR: 'processing',
  EVAL_WAIT_HR: 'processing',
  EVAL_WAIT_AREA_HR: 'processing',
  EVAL_BACK: 'error',
  SELF_EVAL: 'warning',
  EVAL_INIT: 'default',
  SELF_CONFIRM: 'warning',
} as const;

export function EvaluateStatusTag({ pfType = 'TP', status }: EvaluateStatusTagProps) {
  const locales = useMemo(() => getPerformanceLocales(), []);

  if (!status) {
    return null;
  }

  return (
    <Tag color={colorMapper[status]}>
      {locales.pfType[pfType === 'TP' ? 'test' : 'annual'].evaluationStatus.enum[status]}
    </Tag>
  );
}
