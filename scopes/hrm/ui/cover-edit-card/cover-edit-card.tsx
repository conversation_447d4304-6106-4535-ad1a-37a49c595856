import { DeleteOutlined, EyeOutlined, PlusCircleFilled } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { FilePreview } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { deleteUserCertificate } from '@manyun/hrm.service.delete-user-certificate';
import { CertificatePreviewUpload } from '@manyun/hrm.ui.certificate-preview-upload';

import { CertificateEditModal } from './components/certificate-edit-modal';
import styles from './cover-edit-card.module.less';

type CoverEditCardListType = {
  userId: number;
  certLength: number;
  certDataList: Record<string, any>[];
  isAuthEdit: boolean;
  fetchCertList: Function;
};

export const CoverEditCardList = (props: CoverEditCardListType) => {
  const { certLength, certDataList = [], fetchCertList, isAuthEdit, userId } = props;
  const [list, setList] = useState<Record<string, any>[]>([]);

  useEffect(() => {
    const item = certDataList?.filter((val: Record<string, any>) => val?.userId && !val?.certName);

    const arr = [];
    if (certLength < 100 && isAuthEdit && !item.length) {
      arr.push({ userId });
    }
    setList([...certDataList, ...arr]);
  }, [JSON.stringify(certDataList)]);

  const cardData = list.map(item => {
    if (!item?.id && item?.certName) {
      return {
        ...item,
        status: '待上传',
        color: 'warning',
      };
    }

    if (item?.id && item?.instStatus === 'APPROVING') {
      return {
        ...item,
        status: '审批中',
        color: 'processing',
      };
    }

    if (item?.id && item?.effectStartDate && item?.effectEndDate) {
      return {
        ...item,
        status: item?.effect ? '有效' : '无效',
        color: item?.effect ? 'success' : 'error',
      };
    }
    return { ...item, status: '' };
  });

  return (
    <Space size={16} wrap>
      {cardData.map(item => (
        <CoverEditCard
          certData={{ ...item }}
          isAuthEdit={isAuthEdit}
          fetchCertList={fetchCertList}
        />
      ))}
    </Space>
  );
};

export function CoverEditCard({ certData, isAuthEdit, fetchCertList }: Record<string, any>) {
  const certificateEditModalRef = useRef();

  const [previewVisible, setPreviewVisible] = useState(false);
  const [previewImage, setPreviewImage] = useState('');
  const [cardData, setCardData] = useState<Record<string, any>>({});

  const handlePreview = (file: any) => {
    setPreviewImage({
      ...file,
    });

    setPreviewVisible(true);
  };

  const _deleteUserCertificate = async () => {
    const { error } = await deleteUserCertificate({
      userId: cardData?.userId,
      id: cardData.id,
    });

    if (error) {
      message.error(error.message);
      return;
    }

    message.success('删除成功');
    fetchCertList?.();
  };

  const itemRender = (originNode: any, file: any, fileList: any, actions: any) => {
    return (
      <div className={styles.iconContainer}>
        {React.cloneElement(originNode as React.ReactElement, {
          style: {
            width: '100%',
            height: '100%',
            border: '0px',
            padding: '0px',
            position: 'relative',
            left: 1,
          },
        })}
        <div className={styles.iconMask}>
          <EyeOutlined className={styles.icon} onClick={() => handlePreview(file)} />
          {certData?.status !== '审批中' ? (
            <>
              {/* <EditOutlined
                className={styles.icon}
                onClick={() => certificateEditModalRef.current.focus()}
              /> */}
              <Popconfirm
                placement="top"
                title="确认删除该资质证书"
                onConfirm={() => _deleteUserCertificate()}
                okText="确认删除"
                cancelText="取消"
              >
                <DeleteOutlined className={styles.icon} />
              </Popconfirm>
            </>
          ) : null}
        </div>
      </div>
    );
  };

  useEffect(() => {
    setCardData({ ...certData });
  }, [JSON.stringify(certData)]);

  return (
    <div>
      <Card
        style={{
          border: '0px',
          background: ' rgba(233, 233, 233, 0.15)',
          height: '257px',
          width: '232px',
        }}
        bodyStyle={{ padding: '0px' }}
        cover={
          <>
            {cardData?.fileId ? (
              <>
                <CertificatePreviewUpload
                  id={cardData.id}
                  userId={cardData?.userId}
                  fileId={cardData?.fileId}
                  fileType={cardData?.fileType}
                  isAuthEdit={isAuthEdit}
                  fetchCertList={fetchCertList}
                  style={{ height: '130px', width: '100%' }}
                  showUploadList={{
                    showRemoveIcon: false,
                    showPreviewIcon: false,
                  }}
                  itemRender={itemRender}
                />
              </>
            ) : (
              <div className={styles.uploadContainer}>
                <div
                  className={styles.addIcon}
                  onClick={() => certificateEditModalRef.current.focus()}
                >
                  <PlusCircleFilled
                    style={{
                      fontSize: '32px',
                      color: 'var(--manyun-primary-color)',
                      marginBottom: '8px',
                    }}
                  />
                  <br />
                  <Typography.Text type="secondary" style={{ fontSize: '12px' }}>
                    上传资质证书
                  </Typography.Text>
                </div>
              </div>
            )}
            <CertificateEditModal
              ref={certificateEditModalRef}
              userId={cardData?.userId}
              // type={cardData?.fileId ? 'edit' : 'new'}//目前先把编辑去掉
              type="new"
              fetchCertList={fetchCertList}
              data={cardData}
            />
          </>
        }
      >
        <Space direction="vertical" className={styles.cardDetailContainer} size={0}>
          <div className={styles.cardDetailName}>
            <Typography.Text ellipsis={{ tooltip: cardData?.certName }} style={{ width: '152px' }}>
              {cardData?.required ? <span className={styles.requiredStar}>*</span> : null}
              {cardData?.certName || '--'}
            </Typography.Text>
            {cardData?.status && (
              <Tag color={cardData?.color} style={{ margin: '0' }}>
                {cardData?.status}
              </Tag>
            )}
          </div>
          <Typography.Text className={styles.detailTime} type="secondary">
            开始日期：{cardData?.effectStartDate || '--'}
          </Typography.Text>
          <Typography.Text className={styles.detailTime} type="secondary">
            结束日期：{cardData?.effectEndDate || '--'}
          </Typography.Text>
          <Typography.Text className={styles.detailTime} type="secondary">
            复审日期：{cardData?.checkDate ?? '--'}
          </Typography.Text>
        </Space>
      </Card>
      <FilePreview
        file={previewImage as any}
        visible={previewVisible}
        onClose={() => {
          setPreviewVisible(false);
        }}
      />
    </div>
  );
}
