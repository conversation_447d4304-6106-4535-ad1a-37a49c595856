import { PlusOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import type { Dayjs } from 'dayjs';
import moment from 'moment';
import React, { useEffect, useImperativeHandle, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { createUserCertificate } from '@manyun/hrm.service.create-user-certificate';
import { fetchPagedStaffCertificationStandardsList } from '@manyun/hrm.service.fetch-paged-staff-certification-standards-list';
import { updateUserCertificate } from '@manyun/hrm.service.update-user-certificate';

import pdfPng from '../assets/pdf.png';

export type CertificateEditModalProps = {
  userId: number;
  data?: Record<string, any>;
  type: 'new' | 'edit';
  fetchCertList?: Function;
};

type FormType = {
  certName: string;
  effectDate: Dayjs[];
  certPicture: any;
  checkDate?: Dayjs;
};

export const CertificateEditModal = React.forwardRef((props: CertificateEditModalProps, ref) => {
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [form] = Form.useForm();
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const fileList = Form.useWatch('certPicture', form);
  const [uploading, setUploading] = useState<boolean>(false);
  const required = props.data?.required;
  const certName = props.data?.certName;
  const [checked, setChecked] = useState<boolean>();

  const toggleModalVisible = () => {
    setIsModalVisible(!isModalVisible);
  };

  const getCheckVal = async () => {
    if (!props.data?.certName) {
      setChecked(false);
    }

    const { error, data } = await fetchPagedStaffCertificationStandardsList({
      positionCertId: props?.data?.positionCertId,
      pageNum: 1,
      pageSize: 10,
    });

    if (error) {
      setChecked(false);
    }

    setChecked(data?.data[0]?.checked);
  };

  useEffect(() => {
    if (!(props.data?.certName && isModalVisible)) {
      return;
    }

    let fileVal = null;
    if (props.data.fileId) {
      const file = McUploadFile.fromApiObject({
        fileFormat: props.data.fileType,
        filePath: String(props.data.fileId),
        id: props.data.fileId,
        fileType: null,
        fileName: '证书图片',
        fileSize: 0,
        gmtCreate: null,
        gmtModified: null,
        targetId: null,
        targetType: null,
        uploadBy: 0,
        uploadByName: '',
        uploadTime: '',
      });
      fileVal = { ...file, url: file.ext === '.pdf' ? pdfPng : file.src, src: file.src };
    }

    getCheckVal();

    form.setFieldsValue({
      certName: props.data?.certName,
      effectDate: [
        props.data?.effectStartDate ? moment(props.data?.effectStartDate, 'YYYY-MM-DD') : undefined,
        props.data?.effectEndDate ? moment(props.data?.effectEndDate, 'YYYY-MM-DD') : undefined,
      ],
      certPicture: fileVal ? [fileVal] : [],
      checkDate: props.data?.checkDate ? moment(props.data?.checkDate, 'YYYY-MM-DD') : undefined,
    });
  }, [
    isModalVisible,
    props.data?.certName,
    props.data?.checkDate,
    props.data?.effectEndDate,
    props.data?.effectStartDate,
    props.data?.fileId,
    props.data?.fileType,
  ]);

  useImperativeHandle(ref, () => ({
    focus: () => {
      toggleModalVisible();
    },
  }));

  const _createUserCertificate = async (values: FormType) => {
    const info = {
      userId: props.userId,
      certName: values.certName,
      effectStartDate: dayjs(values.effectDate[0]).format('YYYY-MM-DD'),
      effectEndDate: dayjs(values.effectDate[1]).format('YYYY-MM-DD'),
      fileId: Number(values.certPicture[0].patialPath),
      fileType: values.certPicture[0].ext,
      checkDate: values.checkDate ? dayjs(values.checkDate).format('YYYY-MM-DD') : undefined,
      positionCertId: props.data?.positionCertId,
    };

    setSubmitLoading(true);
    const { error } = await createUserCertificate(info);
    setSubmitLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    message.success('操作成功');
  };

  const _updateUserCertificate = async (values: FormType) => {
    const info = {
      id: props.data?.id as number,
      userId: props.data?.userId as number,
      certName: values.certName,
      effectStartDate: dayjs(values.effectDate[0]).format('YYYY-MM-DD'),
      effectEndDate: dayjs(values.effectDate[1]).format('YYYY-MM-DD'),
      fileId: Number(values.certPicture[0].patialPath),
      fileType: values.certPicture[0].ext,
      checkDate: values.checkDate ? dayjs(values.checkDate).format('YYYY-MM-DD') : undefined,
    };

    setSubmitLoading(true);

    const { error } = await updateUserCertificate(info);
    setSubmitLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }

    message.success('修改成功');
  };

  const handleOk = () => {
    form.validateFields().then(async (values: FormType) => {
      props.type === 'new'
        ? await _createUserCertificate(values)
        : await _updateUserCertificate(values);

      toggleModalVisible();
      props.fetchCertList?.();
    });
  };

  return (
    <Modal
      title="上传资质证书"
      open={isModalVisible}
      width={560}
      footer={[
        <Button key="back" onClick={toggleModalVisible}>
          取消
        </Button>,
        <Button
          key="submit"
          type="primary"
          loading={submitLoading || uploading}
          onClick={() => {
            handleOk();
          }}
        >
          确定
        </Button>,
      ]}
      destroyOnClose
      onCancel={toggleModalVisible}
    >
      <Form
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        preserve={false}
        form={form}
        onValuesChange={changedValues => {
          if (changedValues.certPicture && changedValues.certPicture[0]?.ext === '.pdf') {
            const obj = [
              {
                ...changedValues.certPicture[0],
                url: pdfPng,
                thumbUrl: pdfPng,
              },
            ];
            form.setFieldsValue({ certPicture: obj });
          }
        }}
      >
        <Form.Item
          label="证书名称"
          name="certName"
          rules={[
            { required: true, message: '请输入证书名称！', whitespace: true },
            { max: 30, message: '最多输入 30 个字符！' },
          ]}
        >
          {required ? <>{certName}</> : <Input style={{ width: 250 }} allowClear />}
        </Form.Item>
        <Form.Item
          label="有效日期"
          name="effectDate"
          rules={[
            {
              required: true,
              message: '请选择证书有效日期！',
            },
          ]}
        >
          <DatePicker.RangePicker style={{ width: 250 }} allowClear />
        </Form.Item>
        <Form.Item
          label="复审日期"
          name="checkDate"
          rules={[
            {
              required: checked,
              message: '请选择复审日期！',
            },
          ]}
        >
          <DatePicker style={{ width: 250 }} format="YYYY-MM-DD" />
        </Form.Item>
        <Form.Item
          label="证书图片"
          name="certPicture"
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
          rules={[{ required: true, message: '请上传证书图片！' }]}
          extra={
            <Typography.Text type="secondary">
              图片格式支持：png、jpg、jpeg、pdf; 大小上限：2M
            </Typography.Text>
          }
        >
          <Upload
            accept=".png,.jpg,.jpeg,.pdf"
            showUploadList
            allowDelete
            listType="picture-card"
            maxFileSize={2}
            maxCount={1}
            onChange={info => {
              if (info.file) {
                setUploading(info.file.status === 'uploading');
              }
            }}
          >
            {fileList?.length >= 1 ? null : (
              <Button type="text" style={{ width: 104, height: 104 }}>
                <PlusOutlined />
                <br />
                <Typography.Text type="secondary">上传图片</Typography.Text>
              </Button>
            )}
          </Upload>
        </Form.Item>
      </Form>
    </Modal>
  );
});

CertificateEditModal.displayName = 'CertificateEditModal';
