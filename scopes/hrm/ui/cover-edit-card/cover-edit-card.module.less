@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.coverEditCardCover {
  :global {
    .manyun-upload-list-picture-card-container {
      width: 100%;
      height: 134px;
    }
    .manyun-upload-list-picture-card .manyun-upload-list-item {
      padding: 0px;
      border: 0px;
    }
    .manyun-card.manyun-card-bordered {
      height: 342px;
    }
    .manyun-card-body {
      padding: @padding-md;
    }
    .manyun-space.manyun-space-vertical {
      width: 100%;
    }
    .manyun-modal-content {
      max-width: 85%;
      max-height: 80%;
    }
  }
}

.coverEditCardBackground {
  :global {
    .manyun-card.manyun-card-bordered {
      height: 342px;
      background-color: @descriptions-bg;
      border-style: dashed;
    }
  }
}

.iconMask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: none;
  align-items: center;
  justify-content: center;
  gap: 16px;
  background: rgba(0, 0, 0, 0.65);
  color: white;
  font-size: 16px;
  z-index: 988;
}

.iconContainer {
  width: 232px;
  height: 130px;
  border: 0px;
  padding: 0px;
  position: relative;
  overflow: hidden;

  &:hover .iconMask {
    display: flex;
  }
}

.iconMask .icon:hover {
  color: #1890ff;
}
k .icon:hover {
  color: #1890ff;
}

.mask-icon {
  color: white;
  font-size: 16px;
  pointer-events: auto;
  cursor: pointer;
}

.uploadContainer {
  text-align: center;
  height: 130px;
  position: relative;
  top: 24px;

  .addIcon {
    &:hover {
      cursor: pointer;
    }
  }
}

.cardDetailContainer {
  padding: 16px;
  width: 100%;
  height: 127px;

  .cardDetailName {
    display: flex;
    justify-content: space-between;
  }

  .requiredStar {
    color: #ff4d4f;
    display: inline-block;
    font-family: SimSun, sans-serif;
    font-size: 14px;
    line-height: 1;
    margin-right: 2px;
  }

  .detailTime {
    font-size: 12px;
  }
}
