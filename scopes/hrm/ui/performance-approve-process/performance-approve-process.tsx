import dayjs from 'dayjs';
import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import type { CardProps } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Space } from '@manyun/base-ui.ui.space';
import { Steps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { BackendPerformanceType, EvaluationStep } from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';

export type PerformanceApproveProcessProps = Pick<CardProps, 'title'> & {
  pfType: BackendPerformanceType;
  steps: EvaluationStep[];
  loading?: boolean;
};

export function PerformanceApproveProcess({
  pfType,
  title,
  loading,
  steps,
}: PerformanceApproveProcessProps) {
  const locales = getPerformanceLocales();

  return (
    <Card style={{ height: '100%' }} loading={loading}>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          {title}
        </Typography.Title>
        {steps.length > 0 ? (
          <Steps
            direction="vertical"
            items={steps?.map(step => ({
              title: locales.evaluationStepType[pfType][step.type],
              description: (
                <Space direction="vertical">
                  {step.users.map(item => (
                    <Space
                      key={`${step.id}_${item.evaluatedAt}_${item.user?.id}`}
                      direction="vertical"
                    >
                      <Space>
                        {item.user && (
                          <Typography.Text
                            type={step?.status !== 'process' ? 'secondary' : undefined}
                          >
                            {item.user.name}
                          </Typography.Text>
                        )}
                        {item.status !== 'PASS' && item.status !== 'REFUSE' && !item.user && '--'}
                        {item.evaluatedAt && (
                          <Typography.Text
                            type={step?.status !== 'process' ? 'secondary' : undefined}
                          >
                            {dayjs(item.evaluatedAt).format('YYYY-MM-DD HH:mm')}
                          </Typography.Text>
                        )}
                      </Space>
                      {item.status === 'REFUSE' && (
                        <Typography.Text type="danger">
                          审批退回
                          {item.comments && item.comments.reason ? `：${item.comments.reason}` : ''}
                        </Typography.Text>
                      )}
                    </Space>
                  ))}
                </Space>
              ),
              status: step.status,
            }))}
          />
        ) : (
          <Empty />
        )}
      </Space>
    </Card>
  );
}
