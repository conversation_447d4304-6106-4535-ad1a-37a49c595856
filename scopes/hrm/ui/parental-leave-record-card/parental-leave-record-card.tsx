/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-6
 *
 * @packageDocumentation
 */
import React from 'react';

import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useLazyShiftAdjustmentTicketsByChildLeave } from '@manyun/hrm.gql.client.hrm';

export type ParentalLeaveRecordCardProps = {
  bizId: string;
};

export function ParentalLeaveRecordCard({ bizId }: ParentalLeaveRecordCardProps) {
  const [fetchShiftAdjustmentTicketsByChildLeave, { data, loading }] =
    useLazyShiftAdjustmentTicketsByChildLeave();

  React.useEffect(() => {
    fetchShiftAdjustmentTicketsByChildLeave({
      variables: {
        params: {
          bizId: bizId,
        },
      },
    });
  }, [bizId, fetchShiftAdjustmentTicketsByChildLeave]);

  if (data?.shiftAdjustmentTicketsByChildLeave?.leaveType !== 'CHILDCARE_LEAVE') {
    return;
  }
  return (
    <Card title="育儿假请假记录">
      <Table
        loading={loading}
        columns={[
          {
            title: '请假起止日期',
            dataIndex: 'leaveTime',
            render: (_, record) => {
              return `${record.leaveInfo?.startTimeStr ?? ''} ~ ${
                record.leaveInfo?.endTimeStr ?? ''
              }`;
            },
          },
          {
            title: '请假时长',
            dataIndex: 'totalTime',
            render: (_, record) => {
              return `${record.leaveInfo?.totalTime ?? ''}${
                record.leaveInfo?.unit === 'HOUR' ? '小时' : '天'
              }`;
            },
          },
          {
            title: '班次明细',
            dataIndex: 'details',
            render: (_, record) => {
              if ((record.shiftAdjustments ?? []).length === 0) {
                return '--';
              }
              return (
                <Space split={<Divider type="vertical" />} size="small" wrap>
                  {(record.shiftAdjustments ?? []).map(adjustment => (
                    <Space key={adjustment.dutyId} size="small">
                      <Space size={0}>
                        {dayjs(adjustment.scheduleStartTime).format('YYYY-MM-DD')}
                        {adjustment.dutyName}({dayjs(adjustment.scheduleStartTime).format('HH:mm')}~
                        {dayjs(adjustment.scheduleEndTime).format('HH:mm')})
                      </Space>
                      {(adjustment.changeScheduleStartTime || adjustment.changeScheduleEndTime) && (
                        <Typography.Text type="secondary">
                          {adjustment.changeScheduleStartTime
                            ? `${dayjs(adjustment.scheduleStartTime).format('HH:mm')}-${dayjs(
                                adjustment.changeScheduleStartTime
                              ).format('HH:mm')}`
                            : `${dayjs(adjustment.changeScheduleEndTime).format('HH:mm')}-${dayjs(
                                adjustment.scheduleEndTime
                              ).format('HH:mm')}`}
                          请假
                        </Typography.Text>
                      )}
                    </Space>
                  ))}
                </Space>
              );
            },
          },
        ]}
        dataSource={data?.shiftAdjustmentTicketsByChildLeave?.shiftAdjustmentTickets ?? []}
        pagination={false}
        locale={{ emptyText: '当前周期暂无请假记录' }}
      />
    </Card>
  );
}
