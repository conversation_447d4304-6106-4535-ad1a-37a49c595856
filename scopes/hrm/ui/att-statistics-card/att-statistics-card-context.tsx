import React, { createContext, useCallback, useContext, useMemo, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import type { AttStatistics, SvcQuery } from '@manyun/hrm.service.fetch-att-statistics';
import { fetchAttStatistics } from '@manyun/hrm.service.fetch-att-statistics';

export type AttStatisticsCardContextConsumerProps = [
  {
    loading: boolean;
    attStatisticsValue: AttStatistics | null;
  },
  {
    onLoadAttStatisticValue: (params: SvcQuery) => void;
  }
];

export const AttStatisticContext = createContext<AttStatisticsCardContextConsumerProps>([
  {
    loading: false,
    attStatisticsValue: null,
  },
  {
    onLoadAttStatisticValue: () => {},
  },
]);

export type AttStatisticCardProviderProps = {
  children: React.ReactNode;
};
export const AttStatisticProvider = ({ children }: AttStatisticCardProviderProps) => {
  const [attStatisticsValue, setAttStatisticsValue] = useState<AttStatistics | null>(null);
  const [loading, setLoading] = useState(false);

  const onLoadAttStatisticValue = useCallback(async (query: SvcQuery) => {
    setLoading(true);
    const { error, data } = await fetchAttStatistics(query);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setAttStatisticsValue(data);
  }, []);

  const value = useMemo<AttStatisticsCardContextConsumerProps>(
    () => [{ loading, attStatisticsValue }, { onLoadAttStatisticValue }],
    [attStatisticsValue, loading, onLoadAttStatisticValue]
  );

  return <AttStatisticContext.Provider value={value}>{children}</AttStatisticContext.Provider>;
};

export const useAttStatistic = () => useContext(AttStatisticContext);
