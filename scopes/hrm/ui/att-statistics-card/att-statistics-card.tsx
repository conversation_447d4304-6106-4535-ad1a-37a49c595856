import React, { useCallback, useMemo } from 'react';

import classNames from 'classnames';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import type { StatisticProps } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAttStatistic } from './att-statistics-card-context';
import styles from './att-statistics-card.module.less';

export type AttStatisticsKey =
  | 'attStandardTimes'
  | 'attActualTimes'
  | 'lateOnTimes'
  | 'earlyOffTimes'
  | 'missOnTimes'
  | 'missOffTimes'
  | 'absentTimes'
  | 'outWorkTimes';

export type AttStatisticsCardProps = {
  showColumns?: AttStatisticsKey[];
  searchValue?: AttStatisticsKey | null;
  onClick?: (value: AttStatisticsKey | null) => void;
};

const filterTypes: AttStatisticsKey[] = [
  'attStandardTimes',
  'attActualTimes',
  'lateOnTimes',
  'earlyOffTimes',
  'missOnTimes',
  'missOffTimes',
  'absentTimes',
  'outWorkTimes',
];

type StatisticInfos = Pick<StatisticProps, 'title' | 'value' | 'formatter'> & {
  key: AttStatisticsKey;
};

export const AttStatisticsCard = ({
  showColumns,
  searchValue,
  onClick,
}: AttStatisticsCardProps) => {
  const [{ attStatisticsValue }] = useAttStatistic();

  const statisticsArray = useMemo(() => {
    const array: StatisticInfos[] = [
      {
        title: '应出勤(人次)',
        key: 'attStandardTimes',
        value: attStatisticsValue?.attStandardTimes,
        formatter: value => (
          <div style={{ color: `var(--${prefixCls}-primary-color)` }}>{value}</div>
        ),
      },
      {
        title: '实际出勤(人次)',
        key: 'attActualTimes',
        value: attStatisticsValue?.attActualTimes,
        formatter: value => (
          <div style={{ color: `var(--${prefixCls}-primary-color)` }}>{value}</div>
        ),
      },
      {
        title: '迟到(人次)',
        key: 'lateOnTimes',
        value: attStatisticsValue?.lateOnTimes,
        formatter: value => <Typography.Text type="warning">{value}</Typography.Text>,
      },
      {
        title: '早退(人次)',
        key: 'earlyOffTimes',
        value: attStatisticsValue?.earlyOffTimes,
        formatter: value => <Typography.Text type="warning">{value}</Typography.Text>,
      },
      {
        title: '仅上班缺卡(人次)',
        key: 'missOnTimes',
        value: attStatisticsValue?.missOnTimes,
        formatter: value => <Typography.Text type="danger">{value}</Typography.Text>,
      },
      {
        title: '仅下班缺卡(人次)',
        key: 'missOffTimes',
        value: attStatisticsValue?.missOffTimes,
        formatter: value => <Typography.Text type="danger">{value}</Typography.Text>,
      },
      {
        title: '旷工(人次)',
        key: 'absentTimes',
        value: attStatisticsValue?.absentTimes,
        formatter: value => <Typography.Text type="danger">{value}</Typography.Text>,
      },
      {
        title: '外勤(人次)',
        key: 'outWorkTimes',
        value: attStatisticsValue?.outWorkTimes,
      },
    ];
    if (showColumns) {
      return array.filter(d => showColumns.includes(d.key));
    }
    return array;
  }, [
    attStatisticsValue?.absentTimes,
    attStatisticsValue?.attActualTimes,
    attStatisticsValue?.attStandardTimes,
    attStatisticsValue?.earlyOffTimes,
    attStatisticsValue?.lateOnTimes,
    attStatisticsValue?.missOffTimes,
    attStatisticsValue?.missOnTimes,
    attStatisticsValue?.outWorkTimes,
    showColumns,
  ]);

  const handleClick = useCallback(
    (record: StatisticInfos) => {
      if (filterTypes.includes(record.key)) {
        onClick?.(searchValue === record.key ? null : record.key);
      }
    },
    [onClick, searchValue]
  );

  return (
    <Space className={styles.attStatisticsContainer} size="middle">
      {statisticsArray.map(data => (
        <Card
          key={data.title as string}
          className={classNames(searchValue === data.key && styles.selectedCard)}
          hoverable={Number(data.value ?? 0) > 0}
          onClick={() => {
            handleClick(data);
          }}
        >
          <Statistic
            style={{
              cursor:
                Number(data.value ?? 0) > 0 && filterTypes.includes(data.key)
                  ? 'pointer'
                  : 'inherit',
            }}
            title={data.title}
            value={data.value}
            formatter={data.formatter}
          />
        </Card>
      ))}
    </Space>
  );
};
