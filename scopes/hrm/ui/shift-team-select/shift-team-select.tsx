import React, { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchShiftTeams } from '@manyun/hrm.service.fetch-shift-teams';
import type { ShiftTeam } from '@manyun/hrm.service.fetch-shift-teams';

export type ShiftTeamSelectProps<VT = string> = {
  authorizedOnly?: boolean;
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps<VT>;

export const ShiftTeamSelect = React.forwardRef(
  (
    { trigger = 'onDidMount', authorizedOnly, ...selectProps }: ShiftTeamSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [loading, setLoaing] = useState(false);
    const [data, setData] = useState<ShiftTeam[]>([]);

    const _fetchShiftTeams = useCallback(async () => {
      setLoaing(true);
      const { error, data } = await fetchShiftTeams({ needResources: authorizedOnly });
      setLoaing(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setData(data.data);
    }, [authorizedOnly]);

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        _fetchShiftTeams();
      }
    }, [_fetchShiftTeams, trigger]);

    return (
      <Select
        ref={ref}
        showSearch
        optionLabelProp="name"
        optionFilterProp="name"
        {...selectProps}
        loading={loading}
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        options={data as any}
        onFocus={() => {
          if (trigger === 'onFocus') {
            _fetchShiftTeams();
          }
        }}
      />
    );
  }
);

ShiftTeamSelect.displayName = 'ShiftTeamSelect';

ShiftTeamSelect.defaultProps = {
  fieldNames: {
    value: 'id',
    label: 'name',
  },
};
