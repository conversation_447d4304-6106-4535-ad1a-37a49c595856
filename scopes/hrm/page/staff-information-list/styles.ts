import styled, { css } from '@manyun/dc-brain.theme.theme';

export const QueryFilterWrapper = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-form-item {
        .${prefixCls}-form-item-label {
          > label {
            width: 75px;
          }
        }
      }

      .${prefixCls}-form-item:first-child {
        .${prefixCls}-form-item-label {
          > label {
            width: 50px;
          }
        }
        .${prefixCls}-input-affix-wrapper {
          width: 240px !important;
        }
      }
    `;
  }}
`;

export const TableStyle = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-table-content .${prefixCls}-table-tbody {
        .max-width-content {
          max-width: 256px;
        }
      }
    `;
  }}
`;
