/* eslint-disable @typescript-eslint/no-explicit-any */
import { useApolloClient } from '@apollo/client';
import React, { useState } from 'react';
import { Link } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import type { BackendUser } from '@manyun/auth-hub.model.user';
import { useLazyDeptText } from '@manyun/auth-hub.ui.dept-text';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH } from '@manyun/hrm.route.hrm-routes';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

import { MessageAlert } from './message-alert';
import { useMutateUser } from './use-mutate-user';

export const EditStaffModelView: React.FC<{ record?: BackendUser; onSuccess?: () => void }> = ({
  record,
  onSuccess,
}) => {
  const client = useApolloClient();
  const [visible, setVisible] = useState(false);
  const [form] = Form.useForm();
  const [loading, updateUser] = useMutateUser();
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-staff-information-mutator',
  });
  const [deptText, onLoadDeptText] = useLazyDeptText();
  const onClose = () => {
    form.resetFields();
    setVisible(false);
  };

  const transformBlockGuidsToArray = (blockGuids?: string[]) => {
    if (!blockGuids?.length) {
      return undefined;
    }

    return blockGuids.map(guid => [guid]);
  };

  if (!authorized) {
    return null;
  }

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          const currentPartTimeBlocks = (record?.partTimeJobBlockGuids ?? [])
            .map(guid => {
              const blockLabel = readSpace(client, guid);
              return blockLabel?.label;
            })
            .join('｜');
          onLoadDeptText(record?.departmentId, true);
          form.setFieldsValue({
            name: record?.userName,
            staffNo: record?.jobNumber,
            idc: record?.idc,
            mainBlock: record?.blockGuid ? [record.blockGuid] : undefined,
            currentPartTimeBlocks,
            managementLabel: record?.jobLabel,
            managementBlocks: transformBlockGuidsToArray(record?.blockGuids),
            remarks: record?.remarks,
          });
          setVisible(true);
        }}
      >
        编辑
      </Button>
      <Drawer
        title="编辑员工信息"
        width={448}
        open={visible}
        extra={
          <Space>
            <Button disabled={loading} onClick={onClose}>
              取消
            </Button>
            <Button
              loading={loading}
              type="primary"
              onClick={() => {
                if (!record) {
                  return;
                }
                form.validateFields().then(values => {
                  updateUser(
                    {
                      id: record.id,
                      blockGuid: values.mainBlock
                        ? (values.mainBlock[0] ?? values.mainBlock)
                        : undefined,
                      jobLabel: values.managementLabel,
                      blockGuids: values.managementBlocks ? values.managementBlocks.flat() : null,
                      remarks: values.remarks,
                    },
                    () => {
                      onSuccess?.();
                      onClose();
                    }
                  );
                });
              }}
            >
              提交
            </Button>
          </Space>
        }
        onClose={onClose}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={24}>
          <MessageAlert />
          <Form form={form} layout="vertical" initialValues={{ record }}>
            <Form.Item name="name" label="人员姓名" required>
              <Input style={{ width: 224 }} disabled />
            </Form.Item>

            <Form.Item name="staffNo" label="员工工号" required>
              <Input style={{ width: 224 }} disabled />
            </Form.Item>
            <Form.Item label="部门" required>
              <Input style={{ width: 397 }} value={deptText} disabled />
            </Form.Item>
            <Form.Item name="idc" label="所属机房" required>
              <LocationCascader style={{ width: 224 }} nodeTypes={['IDC']} allowClear disabled />
            </Form.Item>
            <Form.Item name="mainBlock" label="主楼栋">
              <LocationCascader
                style={{ width: 224 }}
                nodeTypes={['BLOCK']}
                allowClear
                idc={record?.idc}
                changeOnSelect={false}
              />
            </Form.Item>

            <Form.Item
              name="currentPartTimeBlocks"
              label="当前兼岗楼栋"
              extra={
                <Space size={0}>
                  如需维护兼岗楼栋，请进入
                  <Link target="_blank" to={HRM_PART_TIME_JOBS_MANAGE_ROUTE_PATH}>
                    兼岗记录管理
                  </Link>
                  进行操作
                </Space>
              }
            >
              <Input.TextArea style={{ width: 400 }} disabled autoSize />
            </Form.Item>
            <Form.Item name="managementLabel" label="管理岗标签">
              <MetaTypeSelect
                style={{ width: 224 }}
                allowClear
                metaType={'JOB_LABEL' as any}
                onChange={value => {
                  if (value === 'BLOCK_MANAGER') {
                    form.setFieldsValue({ managementBlocks: [] });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prev, current) => prev.managementLabel !== current.managementLabel}
            >
              {({ getFieldValue }) => {
                const managementLabel = getFieldValue('managementLabel');
                if (managementLabel === 'BLOCK_MANAGER') {
                  return (
                    <Form.Item
                      name="managementBlocks"
                      label="管理楼栋"
                      rules={[{ required: true, message: '请选择管理楼栋' }]}
                    >
                      <LocationCascader
                        style={{ width: 400 }}
                        idc={record?.idc}
                        changeOnSelect={false}
                        allowClear
                        showSearch
                        multiple
                        nodeTypes={['BLOCK']}
                      />
                    </Form.Item>
                  );
                }
              }}
            </Form.Item>

            <Form.Item
              name="remarks"
              label="备注说明"
              rules={[
                {
                  whitespace: true,
                  message: '备注说明不能为空白字符',
                },
              ]}
            >
              <Input.TextArea style={{ whiteSpace: 'pre-wrap' }} maxLength={100} showCount />
            </Form.Item>
          </Form>
        </Space>
      </Drawer>
    </>
  );
};
