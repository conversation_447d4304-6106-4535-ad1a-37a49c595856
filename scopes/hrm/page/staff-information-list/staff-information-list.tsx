/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-28
 *
 * @packageDocumentation
 */
import { useApolloClient } from '@apollo/client';
import React, { useState } from 'react';

import type { BackendUser } from '@manyun/auth-hub.model.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { exportInvolvedPerforamnceStaffInfos } from '@manyun/hrm.service.export-involved-perforamnce-staff-infos';
import { fetchInvolvedPerforamnceStaffInfos } from '@manyun/hrm.service.fetch-involved-perforamnce-staff-infos';
import { UserLink } from '@manyun/iam.ui.user-link';
import { readSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { EditStaffModelView } from './components/edit-staff-modal-view';
import { MessageAlert } from './components/message-alert';
import { OperationLogModelView } from './components/operation-log-modal-view';
import { QueryFilterWrapper, TableStyle } from './styles';

interface FilterFormValues {
  onlyOnJob?: boolean;
  userId?: number;
  idc?: string;
  managementTag?: string;
  blockGuids?: string[];
  orderByJobNoDesc?: boolean;
}

interface QueryParams extends FilterFormValues {
  page: number;
  pageSize: number;
}

const useLazyQueryStaffInfo = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<BackendUser[]>([]);
  const [total, setTotal] = useState(0);

  const onFetchData = React.useCallback(async (queryParams: QueryParams) => {
    setLoading(true);
    const { data, error } = await fetchInvolvedPerforamnceStaffInfos({
      pageNum: queryParams.page,
      pageSize: queryParams.pageSize,
      userId: queryParams.userId,
      idcList: queryParams.idc,
      jobLabels: queryParams.managementTag ? [queryParams.managementTag] : undefined,
      blockGuids: queryParams.blockGuids?.filter(item => item.split('.')[1]),
      enable: queryParams.onlyOnJob ? true : undefined,
      orderByJobNoDesc: queryParams.orderByJobNoDesc,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      setData([]);
      setTotal(0);
      return;
    }
    setData(data.data);
    setTotal(data.total);
  }, []);

  return [{ loading, data, total }, onFetchData] as const;
};
export function StaffInformationList() {
  const client = useApolloClient();
  const [form] = Form.useForm<FilterFormValues>();
  const [queryParams, setQueryParams] = useState<QueryParams>({
    page: 1,
    pageSize: 10,
    onlyOnJob: true,
  });
  const [{ loading, data, total }, onFetchData] = useLazyQueryStaffInfo();
  const [exportLoading, setExportLoading] = useState(false);

  useSpaces({
    variables: {
      nodeTypes: ['IDC', 'BLOCK'],
    },
    fetchPolicy: 'network-only',
  });

  const handleSearch = (filterFormValues: FilterFormValues) => {
    setQueryParams({
      onlyOnJob: queryParams.onlyOnJob,
      ...filterFormValues,
      page: 1,
      pageSize: queryParams.pageSize,
    });
  };

  const onExport = React.useCallback(async () => {
    setExportLoading(true);
    const { data: exportData, error } = await exportInvolvedPerforamnceStaffInfos({
      userId: queryParams.userId,
      idcList: queryParams.idc,
      jobLabels: queryParams.managementTag ? [queryParams.managementTag] : undefined,
      blockGuids: queryParams.blockGuids?.filter(item => item.split('.')[1]),
      enable: queryParams.onlyOnJob ? true : undefined,
      orderByJobNoDesc: queryParams.orderByJobNoDesc,
    });
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    return exportData;
  }, [
    queryParams.blockGuids,
    queryParams.idc,
    queryParams.managementTag,
    queryParams.onlyOnJob,
    queryParams.orderByJobNoDesc,
    queryParams.userId,
  ]);

  React.useEffect(() => {
    onFetchData(queryParams);
  }, [onFetchData, queryParams]);

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <MessageAlert />
        <Space style={{ width: '100%' }} direction="vertical">
          <QueryFilterWrapper>
            <QueryFilter<FilterFormValues>
              form={form}
              items={[
                {
                  label: '姓名',
                  name: 'userId',
                  span: 1,
                  control: (
                    <UserSelect style={{ width: 240 }} labelInValue={false} showSearch allowClear />
                  ),
                },
                {
                  label: '所属机房',
                  name: 'idc',
                  span: 1,
                  control: (
                    <LocationCascader
                      style={{ width: '217px' }}
                      nodeTypes={['IDC']}
                      allowClear
                      showSearch
                    />
                  ),
                },
                {
                  label: '管理岗标签',
                  name: 'managementTag',
                  span: 1,
                  control: (
                    <MetaTypeSelect
                      style={{ width: 217 }}
                      allowClear
                      metaType={'JOB_LABEL' as any}
                    />
                  ),
                },
                {
                  label: '管理楼栋',
                  name: 'blockGuids',
                  span: 1,
                  control: (
                    <LocationCascader
                      style={{ width: '217px' }}
                      allowClear
                      disableCheckboxNodeTypes={['IDC']}
                      showSearch
                      changeOnSelect={false}
                    />
                  ),
                },
              ]}
              onSearch={values => {
                handleSearch({
                  ...values,
                  onlyOnJob: queryParams.onlyOnJob,
                  orderByJobNoDesc: queryParams.orderByJobNoDesc,
                });
              }}
              onReset={() => {
                form.resetFields();
                handleSearch({
                  onlyOnJob: queryParams.onlyOnJob,
                  orderByJobNoDesc: queryParams.orderByJobNoDesc,
                });
              }}
            />
          </QueryFilterWrapper>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Checkbox
              checked={queryParams.onlyOnJob}
              onChange={e => {
                setQueryParams(prev => ({
                  ...prev,
                  page: 1,
                  onlyOnJob: e.target.checked,
                }));
              }}
            >
              仅看在职
            </Checkbox>
            <FileExport
              text=""
              filename="员工信息表"
              disabled={exportLoading}
              data={() => {
                return onExport();
              }}
            />
          </Space>
          <TableStyle>
            <Table<BackendUser>
              size="middle"
              rowKey="id"
              scroll={{ x: 'max-content' }}
              loading={loading}
              columns={[
                {
                  title: '员工号',
                  dataIndex: 'jobNumber',
                  fixed: 'left',
                  sorter: true,
                  render: value => {
                    return value ?? '--';
                  },
                },
                {
                  title: '姓名',
                  dataIndex: 'name',
                  fixed: 'left',
                  render: (_, record) => (
                    <UserLink
                      target="_blank"
                      userId={Number(record.id)}
                      userName={record.userName}
                    />
                  ),
                },
                {
                  title: '状态',
                  dataIndex: 'enable',
                  ellipsis: true,
                  render: value => {
                    return value ? '在职' : '离职';
                  },
                },
                {
                  title: '所属机房',
                  dataIndex: 'idc',
                  render: (_, record) => (record.idc ? <SpaceText guid={record.idc} /> : '--'),
                },
                {
                  title: '主楼栋',
                  dataIndex: 'blockGuid',
                  render: (_, record) => {
                    if (record.blockGuid) {
                      const blockLabel = readSpace(client, record.blockGuid);
                      return blockLabel?.label ?? '--';
                    }
                    return '--';
                  },
                },
                {
                  title: '当前兼岗楼栋',
                  dataIndex: 'partTimeJobBlockGuids',
                  className: 'max-width-content',
                  ellipsis: true,
                  render: (_, record) => {
                    const ctx =
                      (record.partTimeJobBlockGuids ?? []).length > 0
                        ? (record.partTimeJobBlockGuids ?? [])
                            .map(guid => {
                              const blockLabel = readSpace(client, guid);
                              return blockLabel?.label;
                            })
                            .join('｜')
                        : '--';
                    return ctx;
                  },
                },
                {
                  title: '管理岗标签',
                  dataIndex: 'jobLabel',
                  render: (_, record) =>
                    record.jobLabel ? (
                      <MetaTypeText metaType={'JOB_LABEL' as any} code={record.jobLabel} />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '管理楼栋',
                  dataIndex: 'blockGuids',
                  className: 'max-width-content',
                  ellipsis: true,
                  render: (_, record) => {
                    const ctx =
                      (record.blockGuids ?? []).length > 0
                        ? (record.blockGuids ?? [])
                            .map(guid => {
                              const blockLabel = readSpace(client, guid);
                              return blockLabel?.label;
                            })
                            .join('｜')
                        : '--';
                    return ctx;
                  },
                },
                {
                  title: '备注说明',
                  className: 'max-width-content',
                  dataIndex: 'remarks',
                  ellipsis: true,
                  render: (_, record) => record.remarks ?? '--',
                },
                {
                  title: '操作',
                  key: 'action',
                  width: 110,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size={4}>
                      <EditStaffModelView
                        record={record}
                        onSuccess={() => {
                          onFetchData(queryParams);
                        }}
                      />
                      <OperationLogModelView staffId={record.id} />
                    </Space>
                  ),
                },
              ]}
              dataSource={data}
              pagination={{
                current: queryParams.page,
                pageSize: queryParams.pageSize,
                total: total,
                onChange: (page, pageSize) => {
                  setQueryParams(prev => ({
                    ...prev,
                    page,
                    pageSize,
                  }));
                },
              }}
              onChange={(_, __, sorter, { action }) => {
                if (action === 'sort') {
                  const { order } = sorter as { order: 'ascend' | 'descend' | undefined };
                  setQueryParams(prev => ({
                    ...prev,
                    orderByJobNoDesc:
                      order === 'descend' ? true : order === 'ascend' ? false : undefined,
                    page: 1,
                  }));
                }
              }}
            />
          </TableStyle>
        </Space>
      </Space>
    </Card>
  );
}
