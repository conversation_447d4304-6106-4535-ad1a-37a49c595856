import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormItemProps } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectMe } from '@manyun/auth-hub.state.user';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { useUserAttendanceGroup } from '@manyun/hrm.hook.use-user-attendance-group';
import { createGoOutRequest } from '@manyun/hrm.service.create-go-out-request';
import { fetchGoOutWorkTime } from '@manyun/hrm.service.fetch-go-out-work-time';

export function GoOutRequestCreator() {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const history = useHistory();
  const [{ attendance }, onLoadAttendance] = useUserAttendanceGroup();
  const [totalTime, setTotalTime] = useState<number | null>(null); /**单位分钟 */

  useEffect(() => {
    onLoadAttendance(userId!);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  /**限制是否可提交历史月份的外勤 */
  const limitAddHistoryMonth = useMemo(() => {
    const limitAdditionalDays = attendance?.rule.outWorkNotValidStartDay;
    if (limitAdditionalDays) {
      const limitAdditionalDate = moment().date(limitAdditionalDays).startOf('day');
      return moment().isAfter(limitAdditionalDate);
    }
    return false;
  }, [attendance?.rule.outWorkNotValidStartDay]);

  /**超出外勤单次申请总时长 */
  const overOutWorkSettingTotalTime = useMemo(() => {
    return (
      attendance !== null &&
      attendance.rule.outWorkTotalHours !== undefined &&
      attendance.rule.outWorkTotalHours !== null &&
      Number(((totalTime ?? 0) / 60).toFixed(2)) > attendance.rule.outWorkTotalHours
    );
  }, [attendance, totalTime]);

  const totalTimeValidateInfo = useMemo(() => {
    if (overOutWorkSettingTotalTime && attendance) {
      return {
        validateStatus: 'error',
        help: `外勤时长不得超过${attendance.rule.outWorkTotalHours}小时`,
      } as Pick<FormItemProps, 'help' | 'validateStatus'>;
    }
    return undefined;
  }, [attendance, overOutWorkSettingTotalTime]);

  const onSubmit = useCallback(async () => {
    form
      .validateFields()
      .then(async values => {
        if (overOutWorkSettingTotalTime) {
          return;
        }
        if (attendance?.rule.outWorkTotalHours === 0) {
          message.error('您所在考勤组不支持申请外勤，请联系考勤管理员');
          return;
        }
        if (!totalTime || totalTime <= 0) {
          message.error('外勤时长需大于0');
          return;
        }
        setLoading(true);
        const { error, data } = await createGoOutRequest({
          applyId: userId!,
          totalTime,
          timeRange: [
            moment(values.timeRange[0]).startOf('minute').valueOf(),
            moment(values.timeRange[1]).startOf('minute').valueOf(),
          ],
          reason: values.reason,
          attachements: (values.attachements ?? []).map((attachement: McUploadFile) => ({
            ...attachement,
            type: null,
          })),
        });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('操作成功');
        history.push(generateBPMRoutePath({ id: data! }));
      })
      .catch(err => {
        form.scrollToField(err.errorFields[0].name.toString());
      });
  }, [
    attendance?.rule.outWorkTotalHours,
    form,
    history,
    overOutWorkSettingTotalTime,
    totalTime,
    userId,
  ]);

  return (
    <Card title="基本信息">
      <Form
        form={form}
        colon={false}
        labelCol={{ span: 4 }}
        wrapperCol={{ span: 20 }}
        style={{ width: 640 }}
      >
        <Form.Item
          label="外勤时间"
          rules={[
            {
              required: true,
              message: '请选择外勤时间',
            },
          ]}
          extra={`外勤时长 ${
            totalTime ? formatMinutes(totalTime) : '0小时'
          } (外勤时长为实际覆盖的排班时长)`}
          name="timeRange"
          {...totalTimeValidateInfo}
        >
          <DatePicker.RangePicker
            style={{ width: 440 }}
            placeholder={['开始时间', '结束时间']}
            format="YYYY-MM-DD HH:mm"
            showTime
            disabledDate={current => {
              return (
                current &&
                ((attendance !== null &&
                  attendance.rule.outWorkBeforeDays !== null &&
                  attendance.rule.outWorkBeforeDays !== undefined &&
                  current <
                    moment().subtract(attendance.rule.outWorkBeforeDays, 'day').startOf('day')) ||
                  //限制不能为历史月份数据
                  (limitAddHistoryMonth && moment().startOf('month').diff(current) > 0))
              );
            }}
            onChange={async timeRange => {
              if (!timeRange || !timeRange[0] || !timeRange[1] || !userId) {
                setTotalTime(0);
                return;
              }
              const { error, data } = await fetchGoOutWorkTime({
                startTime: moment(timeRange[0]).startOf('minute').valueOf(),
                endTime: moment(timeRange[1]).startOf('minute').valueOf(),
                applyStaffId: userId,
              });
              if (error) {
                message.error(error.message);
                return;
              }
              setTotalTime(data ?? 0);
            }}
          />
        </Form.Item>
        <Form.Item style={{ display: 'none' }} name="totalTime" />
        <Form.Item
          label="外勤事由"
          required
          name="reason"
          rules={[
            { required: true, whitespace: true, message: '请输入外勤事由' },
            {
              max: 200,
              message: '最多输入 200 个字符！',
            },
          ]}
        >
          <Input.TextArea placeholder="请输入外勤事由" rows={3} style={{ width: 440 }} />
        </Form.Item>
        <Form.Item
          label="附件"
          name="attachements"
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
        >
          <Upload accept=".jpg,.png" showUploadList allowDelete maxFileSize={20} maxCount={1}>
            <Space direction="vertical">
              <Button icon={<UploadOutlined />}>上传附件</Button>
              <Typography.Text type="secondary">支持扩展名：.jpg,.png</Typography.Text>
            </Space>
          </Upload>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 4, span: 20 }}>
          <Space size={24}>
            <Button type="primary" loading={loading} onClick={onSubmit}>
              提交
            </Button>
            <Button
              disabled={loading}
              onClick={() => {
                history.goBack();
              }}
            >
              取消
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}

const formatMinutes = (totalMinutes: number) => {
  const hour = Math.floor(totalMinutes / 60);
  const minutes = totalMinutes % 60;
  return `${hour}小时${minutes > 0 ? `${minutes}分钟` : ''}`;
};
