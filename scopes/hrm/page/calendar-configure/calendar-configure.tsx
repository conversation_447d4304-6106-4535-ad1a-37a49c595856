/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-7
 *
 * @packageDocumentation
 */
import React, { useCallback, useEffect, useMemo, useState } from 'react';

import classNames from 'classnames';
import moment from 'moment';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Calendar } from '@manyun/base-ui.ui.calendar';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { useUpdateHolidayConfiguration } from '@manyun/hrm.gql.client.hrm';
import type {
  HolidayDateType,
  MutationUpdateHolidayConfigurationArgs,
} from '@manyun/hrm.gql.client.hrm';
import { fetchHolidayConfiguration } from '@manyun/hrm.service.fetch-holiday-configuration';
import type { CalendarConfiguration } from '@manyun/hrm.service.fetch-holiday-configuration';

import { BatchConfigureDate } from './components/batch-configure-date';
import type { CheckboxValue } from './components/batch-configure-date';
import { CalendarStyle, DateCellStyle } from './styles';

const monthArr: string[] = ['01', '02', '03', '04', '05', '06', '07', '08', '09', '10', '11', '12'];

export function CalendarConfigure() {
  const [year, setYear] = useState<string>(moment().get('year').toString());
  const [configuration, setConfiguration] = useState<CalendarConfiguration | null>(null);
  const [selectedDate, setSelectedDate] = useState<string[]>([]);
  const [authorized] = useAuthorized({ checkByCode: 'element_calendar-configure' });
  const [updateHolidayConfiguration] = useUpdateHolidayConfiguration();

  const onLoadData = useCallback(async () => {
    const { error, data } = await fetchHolidayConfiguration({ year: year });
    if (error) {
      message.error(error.message);
      return;
    }

    setConfiguration(data);
  }, [year]);

  useEffect(() => {
    onLoadData();
  }, [onLoadData]);

  const onSelectDate = useCallback((dateStr: string) => {
    setSelectedDate(prevArray => {
      const index = prevArray.indexOf(dateStr);
      if (index !== -1) {
        const newArray = [...prevArray];
        newArray.splice(index, 1);
        return newArray;
      } else {
        return [...prevArray, dateStr];
      }
    });
  }, []);

  const onUpdateConfiguration = useCallback(
    async ({
      params,
    }: {
      params: MutationUpdateHolidayConfigurationArgs['afterConfiguration'];
      isBatchUpdate?: boolean;
    }) => {
      const { data } = await updateHolidayConfiguration({
        variables: {
          beforeConfiguration: {
            id: Number(year),
            statutoryHolidays: configuration?.statutoryHolidays,
            statutoryWorkdays: configuration?.statutoryWorkdays,
            statutoryRestDays: configuration?.statutoryRestDays,
            protectionDays: configuration?.protectionDays,
          },
          afterConfiguration: params,
        },
      });

      if (data?.updateHolidayConfiguration?.message) {
        message.error(data.updateHolidayConfiguration.message);
        return;
      }
      message.success('日期类型修改成功!');
      onLoadData();
      setSelectedDate([]);
    },
    [configuration, onLoadData, updateHolidayConfiguration, year]
  );

  const defaultCheckboxValue: CheckboxValue | undefined = useMemo(() => {
    if (selectedDate.length === 0) {
      return undefined;
    }
    if (selectedDate.every(date => (configuration?.protectionDays ?? []).includes(date))) {
      return 'checked';
    } else if (selectedDate.every(date => !(configuration?.protectionDays ?? []).includes(date))) {
      return 'unchecked';
    } else {
      return 'indeterminate';
    }
  }, [configuration?.protectionDays, selectedDate]);

  const dateFullCellRender = (value: moment.Moment, item: string) => {
    const panelDate = value.format('YYYY-MM-DD');
    const currentDate = value.valueOf();
    const startDate = moment(`${year}-${item}`).startOf('month').valueOf();
    const endDate = moment(`${year}-${item}`).endOf('month').valueOf();
    /**历史日期仅支持修改上个自然月的数据  */
    const editable =
      value.startOf('day').diff(moment().subtract(1, 'month').startOf('month').valueOf()) >= 0;

    const isReinsuranceDate = configuration?.protectionDays?.includes(panelDate);
    let dateType: HolidayDateType = 'WORK_DATE';
    if (configuration?.statutoryHolidays?.includes(panelDate)) {
      dateType = 'HOLIDAY_DATE';
    } else if (configuration?.statutoryRestDays?.includes(panelDate)) {
      dateType = 'REST_DATE';
    } else if (configuration?.statutoryWorkdays?.includes(panelDate)) {
      dateType = 'WORK_DATE';
    } else {
      if (value.weekday() === 5 || value.weekday() === 6) {
        dateType = 'REST_DATE';
      }
    }

    const isSelectedDate = selectedDate.includes(panelDate);

    const dateContext = (
      <DateCellStyle
        className={classNames(
          'dateCell',
          dateType === 'REST_DATE' && 'restDate',
          dateType === 'WORK_DATE' && 'workDate',
          dateType === 'HOLIDAY_DATE' && 'holidayDate',
          isSelectedDate && 'selectedDate',
          isReinsuranceDate && 'reinsuranceDate'
        )}
        onClick={() => {
          if (!authorized) {
            return;
          }
          if (editable) {
            onSelectDate(panelDate);
          } else {
            message.warning('仅支持修改当前日期上个自然月的数据');
          }
        }}
      >
        {panelDate.slice(-2)}
      </DateCellStyle>
    );

    const isToday = value.startOf('day').valueOf() === moment().startOf('date').valueOf();

    return currentDate >= startDate && currentDate <= endDate ? (
      isToday ? (
        <Badge color="blue" dot offset={[-16, 32]} size="default">
          {dateContext}
        </Badge>
      ) : (
        dateContext
      )
    ) : null;
  };

  return (
    <Card bodyStyle={{ height: 'var(--content-height)' }}>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            日历配置
          </Typography.Title>
          <DatePicker
            style={{ width: 224 }}
            picker="year"
            value={year ? moment().set('year', Number(year)) : undefined}
            format={value => `${value.format('YYYY')}年`}
            allowClear={false}
            disabledDate={current => current && current.diff(moment(), 'year') > 0}
            onChange={val => {
              setYear(moment(val).get('year').toString());
              setSelectedDate([]);
            }}
          />
        </Space>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space size="middle">
            <Tooltip title="法定节假日文字颜色为红色">
              <Badge status="error" text="法定节假日" />
            </Tooltip>
            <Tooltip title="休息日文字颜色为灰色">
              <Badge color="var(--text-color-secondary)" text="休息日" />
            </Tooltip>
            <Tooltip title="工作日文字颜色为黑色">
              <Badge color="var(--text-color)" text="工作日" />
            </Tooltip>
            <Tooltip title="重保日底色为黄色">
              <Badge status="warning" text="重保日" />
            </Tooltip>
          </Space>
          {authorized && (
            <BatchConfigureDate
              selectedDate={selectedDate}
              defaultCheckboxValue={defaultCheckboxValue}
              onOk={params => {
                onUpdateConfiguration({ params });
              }}
            />
          )}
        </Space>
        <Row
          style={{
            maxHeight: 'calc(var(--content-height) - 134px)',
            overflowY: 'auto',
          }}
          gutter={[16, 16]}
        >
          {monthArr.map(item => {
            return (
              <Col key={item} span={6}>
                <CalendarStyle>
                  <Calendar
                    style={{ border: '1px solid var(--border-color-split)' }}
                    className="calendarContainer"
                    fullscreen={false}
                    headerRender={() => (
                      <Container>
                        <Typography.Title
                          style={{ marginBottom: 0 }}
                          level={5}
                        >{`${year}年${item}月`}</Typography.Title>
                      </Container>
                    )}
                    dateFullCellRender={value => {
                      return dateFullCellRender(value, item);
                    }}
                    value={moment(`${year}-${item}`)}
                    validRange={[
                      moment(`${year}-${item}`).startOf('month'),
                      moment(`${year}-${item}`).endOf('month'),
                    ]}
                  />
                </CalendarStyle>
              </Col>
            );
          })}
        </Row>
      </Space>
    </Card>
  );
}
