import styled from 'styled-components';

export const CalendarStyle = styled.div`
  .calendarContainer {
    .manyun-picker-cell-disabled::before {
      background: transparent;
    }
    .manyun-picker-content th,
    .manyun-picker-content td {
      height: 34px;
      line-height: 34px;
      padding: 0;
    }
  }
`;
export const DateCellStyle = styled.div`
  &.dateCell {
    width: 34px;
    height: 24px;
    line-height: 24px;
    border-radius: 2px;
    box-sizing: content-box;
    margin: 0 auto;
    &.workDate {
      color: var(--text-color);
    }
    &.restDate {
      color: var(--text-color-secondary);
    }
    &.holidayDate {
      color: var(--manyun-error-color);
    }
    &.reinsuranceDate {
      background-color: var(--manyun-warning-color-deprecated-bg);
      background-color: #fffbe6;
    }
    &.selectedDate {
      border: 1px solid var(--manyun-primary-color);
    }
  }
`;
