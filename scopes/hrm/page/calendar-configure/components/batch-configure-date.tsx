import React, { useEffect } from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Form } from '@manyun/base-ui.ui.form';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { MutationUpdateHolidayConfigurationArgs } from '@manyun/hrm.gql.client.hrm';

import { DateTypeRadio } from './date-type-radio';

export type CheckboxValue = 'checked' | 'unchecked' | 'indeterminate';
export type BatchConfigureDateProps = {
  selectedDate: string[];
  defaultCheckboxValue?: CheckboxValue;
  onOk?: (params: MutationUpdateHolidayConfigurationArgs['afterConfiguration']) => void;
};

export const BatchConfigureDate = ({
  selectedDate,
  defaultCheckboxValue,
  onOk,
}: BatchConfigureDateProps) => {
  const [form] = Form.useForm();

  useEffect(() => {
    if (defaultCheckboxValue === 'checked') {
      form.setFieldValue('isReinsertionDate', true);
    }
  }, [defaultCheckboxValue, form]);

  return (
    <Space style={{ width: '100%' }} size="middle">
      <Typography.Text>
        已选 <Typography.Link>{selectedDate.length}</Typography.Link> 项
      </Typography.Text>
      批量设为
      <Form style={{ width: '100%' }} form={form} layout="inline">
        <Form.Item name="dateType" style={{ marginBottom: 0 }}>
          <DateTypeRadio
            disabled={selectedDate.length === 0}
            onChange={e => {
              const value = e.target.value;
              onOk?.({
                updateDateList: selectedDate,
                updateDateType: value,
              });
              form.resetFields();
            }}
          />
        </Form.Item>
        <Form.Item style={{ marginBottom: 0 }}>
          <Space size={0}>
            <Form.Item
              style={{ margin: 0 }}
              name="isReinsertionDate"
              noStyle
              valuePropName="checked"
            >
              <Checkbox
                disabled={selectedDate.length === 0}
                indeterminate={defaultCheckboxValue === 'indeterminate'}
                onChange={e => {
                  const isReinsertionDate = e.target.checked;
                  onOk?.({
                    isReinsertionDate: isReinsertionDate,
                    updateDateList: selectedDate,
                  });
                  form.resetFields();
                }}
              >
                设为重保日
              </Checkbox>
            </Form.Item>
            <Explanation
              style={{ marginTop: 6 }}
              iconType="question"
              tooltip={{ title: '重点保护期间执行线上业务会在必要环节有提示' }}
            />
          </Space>
        </Form.Item>
      </Form>
    </Space>
  );
};
