import React from 'react';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Form } from '@manyun/base-ui.ui.form';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type {
  HolidayDateType,
  MutationUpdateHolidayConfigurationArgs,
} from '@manyun/hrm.gql.client.hrm';

import { DateTypeRadio } from './date-type-radio';

export type DateCellFormValues = {
  dateType: HolidayDateType;
  isReinsertionDate?: boolean;
};

export type DateCellPopverProps = {
  panelDate: string;
  formInitValues?: DateCellFormValues;
  children?: React.ReactNode;
  onOk?: (params: MutationUpdateHolidayConfigurationArgs['afterConfiguration']) => void;
};

/**
 * @deprecated ⚠️废弃使用
 */
export const DateCellPopconfirm = ({
  panelDate,
  formInitValues,
  children,
  onOk,
}: DateCellPopverProps) => {
  const [form] = Form.useForm<DateCellFormValues>();

  return (
    <Popconfirm
      placement="topRight"
      icon={null}
      title={
        <Space style={{ width: 378 }} direction="vertical">
          <Typography.Text strong>修改日期类型</Typography.Text>
          <Form labelCol={{ span: 5 }} form={form} initialValues={formInitValues}>
            <Form.Item style={{ marginBottom: 0 }} label="具体日期">
              {panelDate}
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }} label="日期类型" name="dateType">
              <DateTypeRadio />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0, marginLeft: 8 }}>
              <Space size={0}>
                <Form.Item
                  style={{ margin: 0 }}
                  name="isReinsertionDate"
                  noStyle
                  valuePropName="checked"
                >
                  <Checkbox style={{ marginLeft: 4 }}>设为重保日</Checkbox>
                </Form.Item>
                <Explanation
                  style={{ marginTop: 6 }}
                  iconType="question"
                  tooltip={{ title: '重点保护期间执行线上业务会在必要环节有提示' }}
                />
              </Space>
            </Form.Item>
          </Form>
        </Space>
      }
      trigger="click"
      onConfirm={() => {
        form.validateFields().then(values => {
          onOk?.({
            updateDateList: [panelDate],
            updateDateType: values.dateType,
            isReinsertionDate: values.isReinsertionDate,
          });
        });
      }}
    >
      {children}
    </Popconfirm>
  );
};
