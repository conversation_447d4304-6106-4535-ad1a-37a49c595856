import React from 'react';

import type { RadioProps } from '@manyun/base-ui.ui.radio';
import { Radio } from '@manyun/base-ui.ui.radio';

import type { HolidayDateType } from '@manyun/hrm.gql.client.hrm';

export type DateTypeRadioProps = Omit<RadioProps, 'options'>;

export const DateTypeRadio = ({ ...props }: DateTypeRadioProps) => {
  return (
    <Radio.Group
      options={
        [
          { label: '法定节假日', value: 'HOLIDAY_DATE' },
          { label: '休息日', value: 'REST_DATE' },
          { label: '工作日', value: 'WORK_DATE' },
        ] as { label: React.ReactNode; value: HolidayDateType }[]
      }
      {...props}
    />
  );
};
