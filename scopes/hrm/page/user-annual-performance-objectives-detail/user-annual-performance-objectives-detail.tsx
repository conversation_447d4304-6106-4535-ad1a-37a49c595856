import CalendarOutlined from '@ant-design/icons/es/icons/CalendarOutlined';
import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import UserOutlined from '@ant-design/icons/es/icons/UserOutlined';
import { gql, useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { useLocation } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { Button } from '@manyun/base-ui.ui.button';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import {
  useLazyPaginatedPerformancePlans,
  useLazyPerformancePlanById,
  useLazyValidAnnualPerformanceTargetChange,
} from '@manyun/hrm.gql.client.hrm';
import type { AnnualPerformanceObjectiveJSON } from '@manyun/hrm.model.annual-performance-objective';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import type { AnnualPerformanceObjectivesDetailParams } from '@manyun/hrm.route.hrm-routes';
import { generateAnnualPerformanceObjectivesDetail } from '@manyun/hrm.route.hrm-routes';
import { confirmAnnualPerformanceObjectives } from '@manyun/hrm.service.confirm-annual-performance-objectives';
import { AnnualPerformanceObjectiveCard } from '@manyun/hrm.ui.annual-performance-objective-card';
import { AnnualPerformanceScoringPopover } from '@manyun/hrm.ui.annual-performance-scoring-popover';
import { GoalsStatusTag } from '@manyun/hrm.ui.goals-status-tag';
import {
  AgreePerformanceButton,
  RefusePerformanceButton,
  TransformPerformanceButton,
} from '@manyun/hrm.ui.performance-approve-button';
import { PerformanceApproveProcess } from '@manyun/hrm.ui.performance-approve-process';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { RevokePerformanceButton } from '@manyun/hrm.ui.revoke-performance-button';
import {
  PERFORMANCE_SECOND_VERSION,
  generateChangeTargetValidityOptions,
  getPerformanceVersion,
} from '@manyun/hrm.util.performances';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { useLazyPerformance } from './use-performance-objectives';
import styles from './user-annual-performance-objectives-detail.module.less';

const GET_CACHE_PERFORMANCE = gql`
  fragment MyAnnualPerformance on Performance {
    id
    user {
      id
      name
      idc
    }
    year
    evaluationJob {
      label
      value
    }
    objectiveStatus
    plan {
      id
    }
    evaluations {
      id
      type
      status
      users {
        user {
          id
          name
        }
        comments {
          summary
          improve
          infoSummary
          infoImprove
          reason
        }
        status
        result
        evaluatedAt
        isEvaluated
      }
      isCurrentStep
    }
  }
`;

export function UserAnnualPerformanceObjectivesDetail() {
  const history = useHistory();
  const { search } = useLocation();
  const client = useApolloClient();
  const locationMap = getLocationSearchMap<{ idc?: string; position?: string; period?: string }>(
    search
  );

  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const { planid, id, key, user, position } = useParams<AnnualPerformanceObjectivesDetailParams>();
  const [performance, setPerformance] = useState<PerformanceJSON | null>(null);
  //目标变更周期
  const [period, setPeriod] = useState(locationMap.period);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [execExporting, setExecExporting] = useState(false);
  const [confirmObjectives, setConfirmObjectives] = useState<AnnualPerformanceObjectiveJSON[]>([]);

  const [fetch, { loading, refetch }] = useLazyPerformance();
  const [fetchPlans] = useLazyPaginatedPerformancePlans();
  const [fetchRelatedObjective, { data: planInfo, loading: fetchRelatedObjectiveLoading }] =
    useLazyPerformancePlanById();

  //是否新增目标确认（首次确认）
  const isCreate = useMemo(() => id === 'new', [id]);
  //是否是目标变更操作
  const isChangeTarget = useMemo(
    () => !!locationMap.idc && !!locationMap.period && !!locationMap.position,
    [locationMap.idc, locationMap.period, locationMap.position]
  );
  //根据目标状态判断是否可修改岗位
  const judgePerformanceCanModify = useCallback(
    (performance: PerformanceJSON | null) => {
      return (
        !!performance &&
        !!performance.objectiveStatus &&
        Number(performance.user.id) === userId! &&
        ['TARGET_SELF_SETTING', 'TARGET_BACK'].includes(performance.objectiveStatus) &&
        !!performance.canModifyEvaluationJob
      );
    },
    [userId]
  );

  const [validAnnualPerformanceTargetChange, { data: validAnnualPerformanceTargetChangeRes }] =
    useLazyValidAnnualPerformanceTargetChange();

  useEffect(() => {
    if (period && isChangeTarget && performance?.year) {
      validAnnualPerformanceTargetChange({
        variables: {
          year: performance.year,
          changeType: period,
        },
        onCompleted: res => {
          if (res.validAnnualPerformanceTargetChange.message) {
            message.error(res.validAnnualPerformanceTargetChange.message);
          }
        },
      });
    }
  }, [isChangeTarget, performance?.year, period, validAnnualPerformanceTargetChange]);

  useEffect(() => {
    if (isCreate) {
      const performance: PerformanceJSON | null = client.readFragment({
        id: `Performance:${key}`,
        fragment: GET_CACHE_PERFORMANCE,
      });
      if (!performance) {
        message.warning('未获取到缓存信息！');
        history.goBack();
        return;
      }
      setPerformance(performance);
    } else {
      fetch({
        variables: {
          type: 'REGULAR',
          subType: 'TARGET',
          id: Number(id),
          isChange: isChangeTarget,
        },
      })
        .then(result => {
          const _performance = result.data?.performance;
          const _canModify = _performance ? judgePerformanceCanModify(_performance) : false;
          if (_performance) {
            setPerformance({
              ..._performance,
              user: {
                ..._performance.user,
                idc: isChangeTarget
                  ? locationMap.idc ?? _performance.user.idc
                  : _performance.user.idc,
              },
              evaluationJob: isChangeTarget
                ? { value: locationMap.position!, label: locationMap.position! }
                : _canModify
                  ? { value: position, label: position }
                  : _performance.evaluationJob,
            });
            if (_canModify) {
              setPeriod(_performance.targetChangeType ?? undefined);
            }
          }
        })
        .catch(console.error);
    }
  }, [
    client,
    fetch,
    history,
    id,
    isChangeTarget,
    isCreate,
    judgePerformanceCanModify,
    key,
    locationMap.idc,
    locationMap.position,
    position,
  ]);

  //是否修改岗位和机房
  const canModifyJobAndIdc = useMemo(() => {
    return isCreate || judgePerformanceCanModify(performance);
  }, [isCreate, judgePerformanceCanModify, performance]);

  //是否可以修改周期 - 目标为变更且状态为撤回/退回
  const canModifyPeriod = useMemo(() => {
    return (
      !isCreate &&
      !!performance &&
      !!performance.objectiveStatus &&
      Number(performance.user.id) === userId! &&
      ['TARGET_SELF_SETTING', 'TARGET_BACK'].includes(performance.objectiveStatus) &&
      performance.targetChangeType !== 'NORMAL'
    );
  }, [isCreate, performance, userId]);

  useEffect(() => {
    //根据计划id、用户所属机房、用户手选/绑定的考核岗位查询对应的目标
    (async () => {
      if (performance?.evaluationJob && performance.user.idc) {
        let _planId = performance?.plan?.id ?? Number(planid);
        if ((isChangeTarget || canModifyJobAndIdc) && performance.year) {
          //目标变更时需要查询变更后的计划id
          const { data } = await fetchPlans({
            variables: {
              query: {
                page: 1,
                pageSize: 10,
                year: performance.year,
                resourceCodes: [performance.user.idc],
                positions: [performance.evaluationJob.value],
              },
            },
          });
          if (data?.paginatedPerformancePlans.data[0]) {
            _planId = data?.paginatedPerformancePlans.data[0].id;
          } else {
            message.warning('未查询到对应的年度考核计划');
            setConfirmObjectives([]);
            return;
          }
        }
        fetchRelatedObjective({
          variables: {
            id: _planId,
            includeRelatedObjectives: true,
            relatedObjectivesQuery: {
              resourceCode: performance.user.idc,
              evalPosition: performance.evaluationJob.value,
            },
          },
          onCompleted: res => {
            setConfirmObjectives(res.performancePlanById?.relatedObjectives ?? []);
          },
        });
      }
    })();
  }, [canModifyJobAndIdc, fetchPlans, fetchRelatedObjective, isChangeTarget, performance, planid]);

  const onReload = useCallback(() => {
    refetch()
      .then(result => {
        const _performance = result.data?.performance;
        if (_performance) {
          setPerformance(_performance);
          setPeriod(_performance.targetChangeType ?? undefined);
        }
      })
      .catch(console.error);
  }, [refetch]);

  //是否为登陆人的绩效
  const isLoginUserPerformance = useMemo(
    () => !!performance && Number(performance.user.id) === userId!,
    [performance, userId]
  );

  const generateActions = useCallback(() => {
    let action: React.ReactNode[] = [];
    if (!performance) {
      return action;
    }

    if (performance.objectiveStatus === 'TARGET_FINISHED' && !isChangeTarget) {
      action = [
        <DownloadPdfButton
          key="download"
          compact={false}
          disabled={execExporting}
          pdfName="年度目标详情"
          exportElement={document.getElementById('annual-performance_content')}
          beforeDownload={() => {
            setExecExporting(true);
            return new Promise(resolve => {
              setTimeout(() => {
                resolve();
              }, 500);
            });
          }}
          onFinish={() => {
            setExecExporting(false);
          }}
        />,
      ];
      return action;
    }

    /**当前用户为绩效归属人 */
    if (isLoginUserPerformance) {
      if (
        isChangeTarget ||
        isCreate ||
        (performance.objectiveStatus &&
          ['TARGET_SELF_SETTING', 'TARGET_BACK'].includes(performance.objectiveStatus) &&
          performance.isLoggedInUserInCurrentStepUsers)
      ) {
        action = [
          <Button
            key="confirm"
            disabled={confirmObjectives.length === 0}
            loading={confirmLoading}
            type="primary"
            onClick={async () => {
              if (!performance) {
                return;
              }
              if (
                validAnnualPerformanceTargetChangeRes?.validAnnualPerformanceTargetChange.message
              ) {
                message.error(
                  validAnnualPerformanceTargetChangeRes.validAnnualPerformanceTargetChange.message
                );
                return;
              }
              setConfirmLoading(true);
              const { error, data: confirmData } = await confirmAnnualPerformanceObjectives({
                staffId: Number(user),
                year: planInfo?.performancePlanById?.year!,
                evalPosition: performance.evaluationJob?.value!,
                objectives: confirmObjectives,
                infoId: performance.id ?? undefined,
                idc: performance.user.idc ?? undefined,
                changeType: period,
              });
              setConfirmLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
              message.success('目标已确认，请等待审批');
              if (confirmData) {
                if (isCreate || isChangeTarget) {
                  history.replace(
                    generateAnnualPerformanceObjectivesDetail('user', {
                      user: userId!.toString(),
                      planid: performance?.plan?.id?.toString() ?? planid,
                      id: confirmData.toString(),
                      key: `${confirmData}_TARGET`,
                      position: performance.evaluationJob?.value!,
                    })
                  );
                } else {
                  onReload();
                }
              }
            }}
          >
            确认目标
          </Button>,
        ];
      } else if (performance.id && performance.objectiveStatus === 'TARGET_WAIT_SUPERIOR') {
        action = [
          <RevokePerformanceButton
            key="revoke"
            subType="TARGET"
            id={performance.id}
            confirmTitle={
              <div>
                确定撤回该审批？您的绩效目标已在审批中，
                <br />
                撤回后直线经理将无法看到您的年度目标
              </div>
            }
            onSuccess={() => {
              onReload();
            }}
          />,
        ];
      }
    } else if (
      performance.isLoggedInUserInCurrentStepUsers &&
      performance.id &&
      performance.objectiveStatus &&
      ['TARGET_WAIT_SUPERIOR', 'TARGET_WAIT_SEC_SUPERIOR'].includes(performance.objectiveStatus)
    ) {
      const currentStep =
        performance.objectiveStatus === 'TARGET_WAIT_SUPERIOR'
          ? 'TARGET_WAIT_SUPERIOR'
          : 'TARGET_WAIT_SEC_SUPERIOR';
      action = [
        <AgreePerformanceButton
          key="agree"
          type="primary"
          bizId={performance.id}
          currentStep={currentStep}
          onSuccess={() => {
            message.success(`您已通过${performance.user.name}的个人绩效目标`);
            history.goBack();
          }}
        />,
        <RefusePerformanceButton
          key="refuse"
          type="default"
          bizId={performance.id}
          currentStep={currentStep}
          onSuccess={() => {
            message.success(`您已退回${performance.user.name}的个人绩效目标`);
            history.goBack();
          }}
        />,
        <TransformPerformanceButton
          key="transform"
          type="default"
          pfSubType="TARGET"
          bizId={performance.id}
          currentStep={currentStep}
          onSuccess={() => {
            message.success('转交成功');
            history.goBack();
          }}
        />,
      ];
    }

    return action;
  }, [
    performance,
    isChangeTarget,
    isLoginUserPerformance,
    execExporting,
    isCreate,
    confirmObjectives,
    confirmLoading,
    validAnnualPerformanceTargetChangeRes?.validAnnualPerformanceTargetChange.message,
    user,
    planInfo?.performancePlanById?.year,
    period,
    history,
    userId,
    planid,
    onReload,
  ]);

  const performanceYearMoment = useMemo(() => {
    return planInfo?.performancePlanById
      ? dayjs().set('year', Number(planInfo.performancePlanById.year))
      : dayjs();
  }, [planInfo?.performancePlanById]);

  //@YJX TODO: 是否后端控制版本
  const performanceVersion = React.useMemo(() => {
    const yearNum = performanceYearMoment.get('year');

    return getPerformanceVersion(yearNum);
  }, [performanceYearMoment]);

  return (
    <>
      <Row
        id="annual-performance_content"
        style={{
          width: '100%',
          height: execExporting
            ? 'auto'
            : generateActions().length > 0
              ? 'calc(var(--content-height) - 48px)'
              : 'calc(var(--content-height))',
        }}
        gutter={[16, 24]}
      >
        <Col
          style={{ padding: '0px', overflowX: 'hidden', height: '100%', overflowY: 'auto' }}
          span={18}
        >
          <AnnualPerformanceObjectiveCard
            title={
              <Space>
                <div>{`${performance?.year ?? ''}年度目标设定`}</div>
                {performance?.objectiveStatus && (
                  <GoalsStatusTag
                    pfType="REGULAR"
                    status={isChangeTarget ? 'TARGET_SELF_SETTING' : performance.objectiveStatus}
                  />
                )}
              </Space>
            }
            extra={
              <Space size="large">
                {performance?.user.idc && (
                  <Space size={0}>
                    <Tooltip title="所属机房">
                      <EnvironmentOutlined />
                    </Tooltip>
                    {isChangeTarget || canModifyJobAndIdc ? (
                      <LocationCascader
                        style={{ width: 'auto' }}
                        allowClear={false}
                        value={performance.user.idc ? [performance.user.idc] : undefined}
                        nodeTypes={['IDC']}
                        bordered={false}
                        authorizedOnly
                        getPopupContainer={trigger => trigger.parentNode.parentNode}
                        onChange={value => {
                          setPerformance(pre =>
                            pre ? { ...pre, user: { ...pre.user, idc: value[0] as string } } : pre
                          );
                        }}
                      />
                    ) : (
                      <div style={{ marginLeft: 8 }}>
                        <SpaceText guid={performance.user.idc} />
                      </div>
                    )}
                  </Space>
                )}
                {performance && (
                  <Space size={0}>
                    <Tooltip title="考核岗位">
                      <UserOutlined />
                    </Tooltip>
                    {canModifyJobAndIdc || isChangeTarget ? (
                      <PerformancePositionSelect
                        style={{ width: 'auto' }}
                        value={performance.evaluationJob?.value}
                        bordered={false}
                        showArrow={canModifyJobAndIdc || isChangeTarget}
                        showSearch={false}
                        allowClear={false}
                        getPopupContainer={trigger => trigger.parentNode.parentNode}
                        onChange={val => {
                          setPerformance(pre =>
                            pre ? { ...pre, evaluationJob: { value: val, label: val } } : pre
                          );
                        }}
                      />
                    ) : (
                      <div style={{ marginLeft: 8 }}>{performance.evaluationJob?.label}</div>
                    )}
                  </Space>
                )}
                <Space size={0}>
                  <Tooltip title="目标有效期">
                    <CalendarOutlined />
                  </Tooltip>
                  {isChangeTarget || canModifyPeriod ? (
                    <Select
                      style={{ width: 'auto' }}
                      defaultValue={locationMap.period ?? performance?.targetChangeType}
                      bordered={false}
                      optionLabelProp="timeRange"
                      getPopupContainer={trigger => trigger.parentNode.parentNode}
                      options={generateChangeTargetValidityOptions()}
                      onChange={(value, options) => {
                        setPeriod(value);
                      }}
                    />
                  ) : (
                    <div style={{ marginLeft: 8 }}>
                      {performance?.targetChangeValidityDateStr
                        ? performance.targetChangeValidityDateStr
                        : `${performanceYearMoment
                            .startOf('year')
                            .format('YYYY.MM.DD')} ~ ${performanceYearMoment
                            .endOf('year')
                            .format('YYYY.MM.DD')}`}
                    </div>
                  )}
                </Space>
              </Space>
            }
            loading={loading || fetchRelatedObjectiveLoading}
            dataSource={confirmObjectives}
            typeTextVersion={performanceVersion}
          >
            <Space style={{ width: '100%', justifyContent: 'space-between' }} align="center">
              <Typography.Title level={5}>年度目标</Typography.Title>
              <AnnualPerformanceScoringPopover
                style={
                  performanceVersion === PERFORMANCE_SECOND_VERSION ? { width: 344 } : undefined
                }
                ruleTextVersion={performanceVersion}
              />
            </Space>
          </AnnualPerformanceObjectiveCard>
        </Col>
        <Col style={{ height: '100%', overflowY: 'auto' }} span={6}>
          <Space
            style={{ width: '100%', height: '100%', display: 'flex' }}
            className={styles.rightSider}
            size="middle"
            direction="vertical"
          >
            <UserInfoCard
              style={{ width: '100%' }}
              userId={Number(user)!}
              editable={false}
              needValid={false}
            />
            <PerformanceApproveProcess
              loading={loading}
              pfType="REGULAR"
              title="目标设定流程"
              steps={performance?.evaluations ?? []}
            />
          </Space>
        </Col>
      </Row>
      {generateActions().length > 0 && (
        <FooterToolBar>
          <Space size="middle">{generateActions()}</Space>
        </FooterToolBar>
      )}
    </>
  );
}
