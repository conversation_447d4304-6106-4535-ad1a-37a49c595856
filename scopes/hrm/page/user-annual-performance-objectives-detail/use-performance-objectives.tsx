import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type {
  BackendPerformanceSubType,
  BackendPerformanceType,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';

type PerformanceResolverArgs = {
  type: BackendPerformanceType;
  subType: BackendPerformanceSubType;
  id: string | number;
  isChange?: boolean;
};

export type PerformanceQueryResultData = {
  performance: PerformanceJSON | null;
};

export function useLazyPerformance(
  options?: QueryHookOptions<PerformanceQueryResultData, PerformanceResolverArgs>
): LazyQueryResultTuple<PerformanceQueryResultData, PerformanceResolverArgs> {
  return useLazyQuery<PerformanceQueryResultData, PerformanceResolverArgs>(
    gql`
      query GetPerformance($type: String!, $subType: String!, $id: Int!, $isChange: <PERSON>olean) {
        performance(type: $type, subType: $subType, id: $id, isChange: $isChange) {
          rowKey
          id
          year
          type
          subType
          user {
            id
            name
            idc
          }
          evaluationJob {
            label
            value
          }
          evaluations {
            id
            type
            status
            users {
              user {
                id
                name
              }
              comments {
                summary
                improve
                infoSummary
                infoImprove
                reason
              }
              status
              result
              evaluatedAt
              isEvaluated
            }
            isCurrentStep
          }
          objectiveStatus
          evaluationStatus
          isLoggedInUserInCurrentStepUsers
          canModifyEvaluationJob
          objectiveStartedAt
          objectiveExpiredAt
          targetChangeValidityDateStr
          targetChangeType
          plan {
            id
          }
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
