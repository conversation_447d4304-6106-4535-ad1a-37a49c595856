import React, { useMemo } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';

import type { BackendPerformanceSubType, PerformanceStatus } from '@manyun/hrm.model.performance';
import {
  generatePerformanceGoalsSetting,
  generateUserPerformanceDetail,
} from '@manyun/hrm.route.hrm-routes';

export type PpPerformanceLinkProps = {
  type: BackendPerformanceSubType;
  status: PerformanceStatus;
  rowKey: string;
  id: number | null;
};

export const PpPerformanceLink = ({ type, status, rowKey, id }: PpPerformanceLinkProps) => {
  const history = useHistory();
  const linkInfos = useMemo(() => {
    let to: string | undefined;
    let text: string | undefined;
    switch (status) {
      case 'TARGET_REFUSE':
      case 'TARGET_WAIT_SETTING':
        to = generatePerformanceGoalsSetting({
          id: !id ? 'create' : id.toString(),
          key: rowKey,
        });
        text = '去完成';
        break;
      case 'TARGET_WAIT_APPROVING':
      case 'TARGET_PASS':
        if (id) {
          to = generateUserPerformanceDetail({
            type: 'byGoal',
            id: id.toString(),
          });
        }
        text = '查看';
        break;
      case 'EVAL_WAIT_SETTING':
      case 'EVAL_REFUSE':
        if (id) {
          to = generateUserPerformanceDetail({
            type: 'byEval',
            id: id.toString(),
          });
        }
        text = '去完成';
        break;
      case 'EVAL_WAIT_APPROVING':
      case 'EVAL_PASS':
        if (id) {
          to = generateUserPerformanceDetail({
            type: 'byEval',
            id: id.toString(),
          });
        }
        text = '查看';
        break;
      case 'EVAL_INIT':
      case 'TARGET_INIT':
        text = '--';
        break;
      default:
        break;
    }
    return {
      to,
      text,
    };
  }, [id, rowKey, status]);

  if (linkInfos.text === '--') {
    return <>--</>;
  }

  return (
    <Button
      type="link"
      compact
      onClick={() => {
        if (linkInfos.to) {
          history.push(linkInfos.to);
        } else {
          message.warning(`暂无您的${type === 'TARGET' ? '试用期目标记录' : '试用期考核记录'}`);
        }
      }}
    >
      {linkInfos?.text ?? '查看'}
    </Button>
  );
};
