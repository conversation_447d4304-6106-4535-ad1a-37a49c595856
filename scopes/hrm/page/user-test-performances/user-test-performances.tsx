import groupBy from 'lodash.groupby';
import moment from 'moment';
import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserGroupsCard } from '@manyun/auth-hub.ui.user-groups-card';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { PerformanceColorful, TargetColorful } from '@manyun/base-ui.icons';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useUserTestPerformances } from '@manyun/hrm.gql.client.hrm';
import type { PerformanceJSON, PerformanceStatus } from '@manyun/hrm.model.performance';
import { PerformanceStatusTag } from '@manyun/hrm.ui.performance-status-tag';

import { PpPerformanceLink } from './components/pp-performance-link';
import styles from './user-test-performances.module.less';

type SimplePerformanceJSON = {
  title: string;
  hiredAt: number;
  status: PerformanceStatus;
} & Pick<
  PerformanceJSON,
  'objectiveStatus' | 'evaluationStatus' | 'id' | 'rowKey' | 'lineManagers' | 'subType'
>;

export function UserTestPerformances() {
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const { loading, data } = useUserTestPerformances();

  const groupedData = useMemo(() => {
    if (!data || !data.myPerformances) {
      return [];
    }
    const groupByMapper = groupBy(data.myPerformances, 'subType');

    return Object.keys(groupByMapper).reduce((newData: SimplePerformanceJSON[], key) => {
      const dataLen = groupByMapper[key].length;
      groupByMapper[key].forEach((record, index) => {
        newData.push({
          title: `${record.subType === 'TARGET' ? '试用期目标设定' : '试用期考核自评'}${
            dataLen > 1 ? `${index + 1}` : ''
          }`,
          lineManagers: record.lineManagers,
          hiredAt: record.user.hiredAt,
          objectiveStatus: record.objectiveStatus,
          evaluationStatus: record.evaluationStatus,
          id: record.id,
          rowKey: record.rowKey,
          subType: record.subType,
          status: record.status,
        });
      });
      return newData;
    }, []);
  }, [data]);

  return (
    <Row
      style={{
        height: 'calc(var(--content-height)',
      }}
      gutter={[16, 24]}
    >
      <Col style={{ height: '100%', overflowY: 'auto' }} span={18}>
        <Card
          style={{ height: '100%', overflowY: 'auto' }}
          bodyStyle={{ height: '100%' }}
          loading={loading}
        >
          <Space style={{ width: '100%', height: '100%' }} direction="vertical" size="middle">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              试用期绩效
            </Typography.Title>
            {groupedData.length > 0 ? (
              groupedData.map(pf => (
                <Card key={`${pf.subType}__${pf.rowKey}`}>
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <Space size="large">
                      {pf.subType === 'TARGET' ? (
                        <TargetColorful style={{ fontSize: 56 }} />
                      ) : (
                        <PerformanceColorful style={{ fontSize: 56 }} />
                      )}
                      <Space direction="vertical">
                        <Space>
                          <Typography.Title style={{ marginBottom: 0 }} level={5}>
                            {pf.title}
                          </Typography.Title>
                          {pf.lineManagers?.map(superior => (
                            <Tag key={superior.id}>直线经理：{superior.name}</Tag>
                          ))}
                        </Space>
                        <Space>
                          <Typography.Text type="secondary">
                            {pf.subType === 'TARGET'
                              ? '请设定您的试用期目标'
                              : '请填写您的试用期考核自评'}
                          </Typography.Text>
                          <Typography.Text type="secondary">
                            截止时间：
                            {pf.hiredAt
                              ? pf.subType === 'TARGET'
                                ? moment(pf.hiredAt).add(30, 'day').format('YYYY-MM-DD')
                                : moment(pf.hiredAt)
                                    .add(6, 'month')
                                    .subtract(1, 'day')
                                    .format('YYYY-MM-DD')
                              : '--'}
                          </Typography.Text>
                        </Space>
                      </Space>
                    </Space>
                    <Space size="large">
                      <PerformanceStatusTag pfType="TP" status={pf.status} />
                      <div style={{ width: '100px', textAlign: 'right' }}>
                        <PpPerformanceLink
                          type={pf.subType}
                          rowKey={pf.rowKey}
                          id={pf.id}
                          status={pf.status}
                        />
                      </div>
                    </Space>
                  </Space>
                </Card>
              ))
            ) : (
              <Empty
                description={
                  <div>
                    您无需在 DC Base 进行试用期绩效考核
                    <br />
                    具体可与您的 HR 确认
                  </div>
                }
                style={{
                  margin: 'auto',
                  position: 'absolute',
                  top: '50%',
                  left: '50%',
                  transform: 'translate(-50%, -50%)',
                }}
              />
            )}
          </Space>
        </Card>
      </Col>
      <Col style={{ height: '100%', overflowY: 'auto' }} span={6}>
        <Space
          style={{ width: '100%', height: '100%', display: 'flex' }}
          className={styles.rightSider}
          size="middle"
          direction="vertical"
        >
          <UserInfoCard
            key="userInfos"
            style={{ width: '100%' }}
            userId={userId!}
            editable={false}
            needValid={false}
          />
          <UserGroupsCard
            key="userGroups"
            style={{ height: '100%', width: '100%' }}
            userId={userId!}
          />
        </Space>
      </Col>
    </Row>
  );
}
