import { gql, useApolloClient } from '@apollo/client';
import orderBy from 'lodash.orderby';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation, useParams } from 'react-router';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import type { GoalSettingParams } from '@manyun/hrm.route.hrm-routes';
import {
  generatePerformanceGoalsSetting,
  generateUserPerformanceDetail,
} from '@manyun/hrm.route.hrm-routes';
import { submitPerformance } from '@manyun/hrm.service.submit-performance';
import type { SimpleGoalJSON } from '@manyun/hrm.service.submit-performance';
import { GoalsStatusTag } from '@manyun/hrm.ui.goals-status-tag';
import { PerformanceApproveProcess } from '@manyun/hrm.ui.performance-approve-process';
import { PerformanceGoalCollapse } from '@manyun/hrm.ui.performance-goal-collapse';

import { defaultValuesGoals } from './components/constant';
import { GoalsForm } from './components/goals-form';
import type { GoalsFormRefProps } from './components/goals-form';
import { Guidance } from './components/guidance';
import { SortSelect } from './components/sort-select';
import { GoalItemDeleter } from './components/​goal-item-deleter';
import { GoalItemMutator } from './components/​goal-item-mutator';
import { useLazyPerformance } from './use-performance';
import styles from './user-test-performance-goals-setting.module.less';

const GET_CACHE_PERFORMANCE = gql`
  fragment MyPerformance on Performance {
    id
    user {
      id
      name
      hiredAt
    }
    lineManagers {
      id
      name
    }
    evaluations {
      id
      type
      status
      users {
        user {
          id
          name
        }
        comments {
          summary
          improve
          infoSummary
          infoImprove
          reason
        }
        status
        result
        evaluatedAt
        isEvaluated
      }
      isCurrentStep
    }
  }
`;

export function UserTestPerformanceGoalsSettingPage() {
  const history = useHistory();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const { search } = useLocation();
  const params = useParams<GoalSettingParams>();
  /**是否仅支持提交审批*/
  const { onlySubmit } = getLocationSearchMap<{ onlySubmit: boolean }>(search);
  const [sortField, setSortField] = useState('createdAt');
  const [mutateTargetId, setMutateTargetId] = useState<number | 'add' | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);
  const goalsFormRef = useRef<GoalsFormRefProps>();
  const [performanceInfos, setPerformanceInfos] = useState<{
    userId: number;
    superiorIds: number[];
    hiredDate: number;
    performanceId?: number;
    evaluations: PerformanceJSON['evaluations'];
  } | null>(null);

  const [fetch, { loading, data, refetch }] = useLazyPerformance({});

  const client = useApolloClient();

  const sortedGoals = useDeepCompareMemo(() => {
    return data && data.performance
      ? (orderBy(
          data.performance.objectives.filter(obj => obj.type === 'BIZ'),
          [sortField],
          [sortField === 'createdAt' ? 'asc' : 'desc']
        ) as PerformanceObjectiveJSON[])
      : [];
  }, [data, sortField]);

  const weightSum = useDeepCompareMemo(() => {
    return sortedGoals.reduce((sum, goal) => {
      if (goal.percent) {
        sum += goal.percent;
      }
      return sum;
    }, 0);
  }, [sortedGoals]);

  useEffect(() => {
    if (params.id === 'create') {
      const performance: PerformanceJSON | null = client.readFragment({
        id: `Performance:${params.key}`,
        fragment: GET_CACHE_PERFORMANCE,
      });
      if (!performance) {
        message.warning('未获取到缓存信息！');
        history.goBack();
        return;
      }
      setPerformanceInfos({
        hiredDate: performance.user.hiredAt,
        userId: performance.user.id,
        superiorIds: performance.lineManagers?.map(user => user.id) ?? [],
        performanceId: performance.id ?? undefined,
        evaluations: performance.evaluations,
      });
    } else {
      fetch({
        variables: {
          type: 'TP',
          subType: 'TARGET',
          id: Number(params.id),
        },
      }).then(result => {
        const _performance = result.data?.performance;
        if (_performance) {
          setPerformanceInfos({
            hiredDate: _performance.user.hiredAt,
            userId: _performance.user.id,
            superiorIds: _performance.lineManagers?.map(user => user.id) ?? [],
            performanceId: _performance.id ?? undefined,
            evaluations: _performance.evaluations,
          });
        }
      });
    }
  }, [client, fetch, history, params.id, params.key]);

  const onSubmit = useCallback(async () => {
    if (weightSum !== 100) {
      message.error('请确保目标权重总和为100%，再提交审批');
      return;
    }
    if (!data || !data.performance || !performanceInfos) {
      return;
    }
    setSubmitLoading(true);
    const _valuesGoals = data.performance.objectives.filter(obj => obj.type === 'VALUES');
    const { error } = await submitPerformance({
      ...performanceInfos,
      type: 'TARGET',
      goals: [
        ...(sortedGoals as SimpleGoalJSON[]),
        ...(_valuesGoals.length > 0 ? _valuesGoals : defaultValuesGoals),
      ].map(goal => ({
        ...goal,
        /**modifiedAt 为null 表示新增数据 */
        id: goal.modifiedAt === null ? undefined : goal.id,
        selfEvaluation: undefined,
      })),
      id: data.performance.id!,
    });
    setSubmitLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('试用期目标已提交');
    history.replace(
      generateUserPerformanceDetail({
        type: 'byGoal',
        id: data.performance.id!.toString(),
      })
    );
  }, [data, history, performanceInfos, sortedGoals, weightSum]);

  /**当目标审批中或审批完成 且不是从详情页修改目标计划跳转来的需屏蔽操作、提交按钮 */
  const hiddenOperate = useMemo(() => {
    return (
      data?.performance &&
      data.performance.objectiveStatus &&
      ['TARGET_WAIT_SUPERIOR', 'TARGET_FINISHED'].includes(data.performance.objectiveStatus) &&
      !onlySubmit
    );
  }, [data?.performance, onlySubmit]);

  return (
    <>
      <Row
        style={{
          height: hiddenOperate
            ? 'calc(var(--content-height))'
            : 'calc(var(--content-height) - 48px)',
        }}
        gutter={[16, 24]}
      >
        <Col style={{ height: '100%', overflowY: 'auto' }} span={18}>
          <Card style={{ height: '100%', overflowY: 'auto' }} loading={loading}>
            {performanceInfos && (
              <Space style={{ width: '100%', display: 'flex' }} size="middle" direction="vertical">
                <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                  <Space>
                    试用期目标
                    <GoalsStatusTag
                      status={
                        data && data.performance
                          ? data.performance.objectiveStatus
                          : 'TARGET_SELF_SETTING'
                      }
                    />
                  </Space>
                </Typography.Title>
                {!onlySubmit && <Guidance />}
                {!hiddenOperate && (
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <Explanation
                      iconType="exclamation"
                      tooltip={{
                        title: '目标最多可设定5个，各目标权重合计为100%时，方可提交',
                      }}
                    >
                      <Button
                        type="primary"
                        disabled={!!mutateTargetId || sortedGoals.length >= 5}
                        onClick={() => {
                          if (onlySubmit) {
                            if (data?.performance) {
                              client.cache.modify({
                                id: client.cache.identify(data.performance),
                                fields: {
                                  objectives(cachedObjectivesRefs: PerformanceObjectiveJSON[]) {
                                    const newObjectiveRef = client.cache.writeFragment({
                                      data: {
                                        __typename: 'PerformanceObjective',
                                        id: cachedObjectivesRefs.length,
                                        type: 'BIZ',
                                        title: '',
                                        percent: 100 - weightSum,
                                        content: '',
                                        measurements: null,
                                        status: null,
                                        startedAt: null,
                                        finishedAt: null,
                                        selfEvaluation: null,
                                        grade: null,
                                        modifiedAt: null,
                                      },
                                      fragment: gql`
                                        fragment NewObjective on PerformanceObjective {
                                          id
                                          type
                                          title
                                          percent
                                          content
                                          measurements
                                          status
                                          startedAt
                                          finishedAt
                                          selfEvaluation
                                          grade
                                          modifiedAt
                                        }
                                      `,
                                    });
                                    return [newObjectiveRef, ...cachedObjectivesRefs];
                                  },
                                },
                              });
                            }
                          } else {
                            setMutateTargetId('add');
                          }
                        }}
                      >
                        添加目标
                      </Button>
                    </Explanation>
                    {sortedGoals.length > 0 && mutateTargetId !== 'add' && !onlySubmit && (
                      <SortSelect
                        value={sortField}
                        onChange={value => {
                          if (value) {
                            setSortField(value);
                          }
                        }}
                      />
                    )}
                  </Space>
                )}
                {mutateTargetId === 'add' && (
                  <GoalItemMutator
                    maxWeight={100 - weightSum}
                    performanceInfos={performanceInfos}
                    onOK={bizId => {
                      setMutateTargetId(null);
                      /**创建后跳转至编辑页面 */
                      if (params.id === 'create') {
                        history.replace(
                          generatePerformanceGoalsSetting({
                            id: bizId.toString(),
                            key: `${bizId}_TARGET`,
                          })
                        );
                      } else {
                        refetch();
                      }
                    }}
                    onCancel={() => {
                      setMutateTargetId(null);
                    }}
                  />
                )}
                {onlySubmit ? (
                  <GoalsForm
                    ref={goalsFormRef}
                    defaultValues={sortedGoals}
                    performanceInfos={performanceInfos}
                    onItemChangeValue={newItemValues => {
                      client.cache.updateFragment(
                        {
                          id: `PerformanceObjective:${newItemValues.id}`,
                          fragment: gql`
                            fragment MyPerformanceObjective on PerformanceObjective {
                              title
                              percent
                              content
                              measurements
                              status
                              startedAt
                              finishedAt
                            }
                          `,
                        },
                        objective => {
                          return { ...objective, ...newItemValues };
                        }
                      );
                    }}
                    onItemDelete={id => {
                      if (data?.performance) {
                        client.cache.modify({
                          id: client.cache.identify(data.performance),
                          fields: {
                            objectives(
                              existingCommentRefs: PerformanceObjectiveJSON[],
                              { readField }
                            ) {
                              return existingCommentRefs.filter(
                                objectRef => id !== readField('id', objectRef)
                              );
                            },
                          },
                        });
                      }
                    }}
                  />
                ) : (
                  sortedGoals.map(goal => (
                    <div key={`edit_${goal.id}`}>
                      {mutateTargetId === goal.id ? (
                        <GoalItemMutator
                          maxWeight={100 - weightSum + goal.percent}
                          goal={goal}
                          performanceInfos={performanceInfos}
                          onOK={() => {
                            refetch();
                            setMutateTargetId(null);
                          }}
                          onCancel={() => {
                            setMutateTargetId(null);
                          }}
                        />
                      ) : (
                        <Card size="small" bodyStyle={{ padding: '12px 8px' }}>
                          <PerformanceGoalCollapse
                            active
                            goal={goal}
                            extra={
                              hiddenOperate ? null : (
                                <Space>
                                  <Button
                                    type="link"
                                    compact
                                    disabled={!!mutateTargetId}
                                    onClick={() => {
                                      setMutateTargetId(goal.id);
                                    }}
                                  >
                                    编辑
                                  </Button>
                                  <GoalItemDeleter
                                    disabled={!!mutateTargetId}
                                    goal={goal}
                                    onSuccess={() => {
                                      refetch();
                                    }}
                                  />
                                </Space>
                              )
                            }
                          />
                        </Card>
                      )}
                    </div>
                  ))
                )}
                {mutateTargetId === null && (sortedGoals.length === 0 || !data) && <Empty />}
              </Space>
            )}
          </Card>
        </Col>
        <Col style={{ height: '100%', overflowY: 'auto' }} span={6}>
          <Space
            style={{ width: '100%', height: '100%', display: 'flex' }}
            className={styles.sider}
            size="middle"
            direction="vertical"
          >
            {userId && (
              <UserInfoCard key="info" userId={Number(userId)} editable={false} needValid={false} />
            )}
            <PerformanceApproveProcess
              key="process"
              pfType="TP"
              title="目标设定流程"
              loading={loading}
              steps={performanceInfos?.evaluations ?? []}
            />
          </Space>
        </Col>
      </Row>
      {!hiddenOperate && (
        <FooterToolBar>
          <Space size="middle">
            <Button
              type="primary"
              loading={submitLoading || loading}
              disabled={(data?.performance?.objectives ?? []).length === 0}
              onClick={() => {
                if (!!mutateTargetId) {
                  message.warning('当前目标未保存，请保存后在提交');
                  return;
                }
                if (onlySubmit) {
                  goalsFormRef.current
                    ?.validateFields()
                    .then(() => {
                      onSubmit();
                    })
                    .catch(error => {
                      if (error.values.id) {
                        document
                          .querySelector(`#form_${error.values.id}`)
                          ?.scrollIntoView({ behavior: 'smooth' });
                      }
                    });
                } else {
                  onSubmit();
                }
              }}
            >
              提交审批
            </Button>
            {onlySubmit && (
              <Button
                onClick={() => {
                  history.goBack();
                }}
              >
                取消修改
              </Button>
            )}
          </Space>
        </FooterToolBar>
      )}
    </>
  );
}
