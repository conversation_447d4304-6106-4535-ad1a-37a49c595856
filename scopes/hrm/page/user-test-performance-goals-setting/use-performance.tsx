import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type {
  BackendPerformanceSubType,
  BackendPerformanceType,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';

export const GET_PERFORMANCE_QUERY = gql`
  query GetPerformance($type: String!, $subType: String!, $id: Int!) {
    performance(type: $type, subType: $subType, id: $id) {
      rowKey
      id
      type
      subType
      user {
        id
        name
        hiredAt
      }
      objectives {
        id
        type
        title
        percent
        content
        measurements
        status
        startedAt
        finishedAt
        selfEvaluation
        grade
        createdAt
        modifiedAt
      }
      evaluations {
        id
        type
        status
        users {
          user {
            id
            name
          }
          comments {
            summary
            improve
            infoSummary
            infoImprove
            reason
          }
          status
          result
          evaluatedAt
          isEvaluated
        }
        isCurrentStep
      }
      lineManagers {
        id
        name
      }
      objectiveStatus
      objectiveStartedAt
      objectiveExpiredAt
    }
  }
`;

type PerformanceResolverArgs = {
  type: BackendPerformanceType;
  subType: BackendPerformanceSubType;
  id: string | number;
};

export type PerformanceQueryResultData = {
  performance: PerformanceJSON | null;
};

export function useLazyPerformance(
  options?: QueryHookOptions<PerformanceQueryResultData, PerformanceResolverArgs>
): LazyQueryResultTuple<PerformanceQueryResultData, PerformanceResolverArgs> {
  return useLazyQuery<PerformanceQueryResultData, PerformanceResolverArgs>(GET_PERFORMANCE_QUERY, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
