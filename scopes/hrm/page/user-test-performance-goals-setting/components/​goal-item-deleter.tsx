import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';

import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import { deletePerformanceGoal } from '@manyun/hrm.service.delete-performance-goal';

export type GoalItemDeleterProps = {
  /**
   * 目标项详情
   */
  goal: PerformanceObjectiveJSON;
  /**
   * 是否调用接口
   */
  isRequest?: boolean;
  onSuccess?: (goal: PerformanceObjectiveJSON) => void;
} & Pick<ButtonProps, 'disabled'>;

export const GoalItemDeleter = ({
  goal,
  isRequest = true,
  disabled,
  onSuccess,
}: GoalItemDeleterProps) => {
  return (
    <Popconfirm
      title="确定删除该目标？删除后不可恢复"
      okText="确认删除"
      onConfirm={async () => {
        if (isRequest) {
          const { error } = await deletePerformanceGoal({ goalId: goal.id });
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('目标已删除');
        }
        onSuccess?.(goal);
      }}
    >
      <Button type="link" disabled={disabled} compact>
        删除
      </Button>
    </Popconfirm>
  );
};
