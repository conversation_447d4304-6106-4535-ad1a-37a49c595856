import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

export const Guidance = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  return (
    <>
      <Alert
        message="温馨提示：建议每更新完一项目标后先点击“保存”，待所有目标都完成更新后再统一“提交”，以避免多次发出审批请求"
        action={
          <Button
            type="link"
            compact
            onClick={() => {
              setIsModalOpen(true);
            }}
          >
            目标填写指引
          </Button>
        }
      />
      <Modal
        width={720}
        title="目标填写指引"
        open={isModalOpen}
        footer={false}
        onCancel={() => {
          setIsModalOpen(false);
        }}
      >
        <Space direction="vertical" size={0}>
          <div>1. 员工需要在入职一个月内完成试用期目标的填写和审批，目标设定须符合SMART原则</div>
          <div>2. 目标设定流程如下:</div>
          <Space style={{ alignItems: 'flex-start' }} size={0}>
            <div>&nbsp;&nbsp;&nbsp;a) </div>
            <div>
              员工填写：请入职三周内提前与直线经理对焦部门目标，并填写本人试用期目标、衡量标准、权重、预估目标完成日期（预估目标完成日期需在试用期之内）
            </div>
          </Space>
          <Space style={{ alignItems: 'flex-start' }} size={0}>
            <div>&nbsp;&nbsp;&nbsp;b) </div>
            <div>
              审批：直线经理在员工入职一个月内对员工目标设定的合理性进行审批，如对目标有异议，则员工需修改后重新提交审批
            </div>
          </Space>
        </Space>
      </Modal>
    </>
  );
};
