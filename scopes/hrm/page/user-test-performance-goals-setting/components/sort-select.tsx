import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

export type SortSelectProps = {
  value?: string;
  onChange?: (value?: string) => void;
};

export const SortSelect = ({ value, onChange }: SortSelectProps) => {
  return (
    <>
      <Typography.Text type="secondary">排序:</Typography.Text>
      <Select
        defaultValue="modifiedAt"
        options={[
          {
            label: '创建时间',
            value: 'createdAt',
          },
          {
            label: '更新时间',
            value: 'modifiedAt',
          },
          {
            label: '权重占比',
            value: 'percent',
          },
        ]}
        getPopupContainer={trigger => trigger.parentNode.parentNode}
        bordered={false}
        value={value}
        onChange={onChange}
      />
    </>
  );
};
