import type { SimpleGoalJSON } from '@manyun/hrm.service.submit-performance';

/**价值观目标  */
export const defaultValuesGoals: SimpleGoalJSON[] = [
  {
    type: 'VALUES',
    title: '工作表现',
    content: '包括工作中的主动性、积极性、准确性、及时性以及工作中与他人的沟通合作能力',
    percent: 0,
    measurements: '包括工作中的主动性、积极性、准确性、及时性以及工作中与他人的沟通合作能力',
    status: 'FINISHED',
    startedAt: new Date().getTime(),
    finishedAt: new Date().getTime(),
    modifiedAt: new Date().getTime(),
  },
  {
    type: 'VALUES',
    title: '普洛斯价值观',
    content:
      '包括对“赢”的文化、主人翁精神、值得信赖、开放交流、普洛斯大家庭这五大价值观和工作中的体现',
    percent: 0,
    measurements:
      '包括对“赢”的文化、主人翁精神、值得信赖、开放交流、普洛斯大家庭这五大价值观和工作中的体现',
    status: 'FINISHED',
    startedAt: new Date().getTime(),
    finishedAt: new Date().getTime(),
    modifiedAt: new Date().getTime(),
  },
];
