import React, { use<PERSON><PERSON>back, useMemo, useState } from 'react';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import type {
  BackendObjectiveStatus,
  PerformanceObjectiveJSON,
} from '@manyun/hrm.model.performance-objective';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';
import { savePerformance } from '@manyun/hrm.service.save-performance';

export type GoalItemMutatorProps = {
  performanceInfos: {
    userId: number;
    superiorIds: number[];
    hiredDate: number;
    performanceId?: number;
  };
  goal?: PerformanceObjectiveJSON;
  /**
   * 目标项可设置的最大权重
   */
  maxWeight?: number;
  footer?: boolean;
  onOK?: (id: number) => void;
  onCancel?: () => void;
  onValuesChange?: (values: Omit<PerformanceObjectiveJSON, 'grade' | 'selfEvaluation'>) => void;
  extra?: React.ReactNode;
  index?: number;
};

const layout = {
  labelCol: {
    xxl: { span: 2 },
    xl: {
      span: 3,
    },
  },
  wrapperCol: {
    xxl: { span: 22 },
    xl: {
      span: 21,
    },
  },
};
const tailLayout = {
  wrapperCol: {
    xl: {
      offset: 3,
      span: 21,
    },
    xxl: {
      offset: 2,
      span: 22,
    },
  },
};

const itemLayout = {
  labelCol: {
    xl: {
      span: 6,
    },
    xxl: {
      span: 6,
    },
  },
};

const colSpan = {
  xl: 12,
  xxl: 8,
};

export const GoalItemMutator = React.forwardRef(
  (
    {
      performanceInfos,
      goal,
      footer = true,
      maxWeight,
      onOK,
      onCancel,
      onValuesChange,
      extra,
      index,
    }: GoalItemMutatorProps,
    ref: React.Ref<FormInstance>
  ) => {
    const [form] = Form.useForm();
    const { userId, superiorIds, hiredDate, performanceId } = performanceInfos;
    const goalLocales = useMemo(() => getPerformanceObjectiveLocales(), []);
    const [loading, setLoading] = useState(false);

    const isOutTestPeriod = useCallback(
      (currentDate: moment.Moment) => {
        return !moment(currentDate).isBetween(
          hiredDate,
          moment(hiredDate).add(6, 'month'),
          'day',
          '[)'
        );
      },
      [hiredDate]
    );

    return (
      <Card
        title={
          extra ? `目标${index !== undefined ? index + 1 : ''}` : !goal ? '添加新目标' : '编辑目标'
        }
        extra={extra}
      >
        <Form
          ref={ref}
          {...layout}
          id={goal ? `form_${goal.id}` : undefined}
          name={goal?.id.toString()}
          initialValues={{
            ...goal,
            percent: goal?.percent ?? maxWeight,
            startedAt: goal && goal.startedAt ? moment(goal.startedAt) : undefined,
            finishedAt: goal && goal.finishedAt ? moment(goal.finishedAt) : undefined,
          }}
          form={form}
          scrollToFirstError
          onValuesChange={(_, values) => {
            onValuesChange?.({
              ...values,
              startedAt: moment(values.startedAt).startOf('day').valueOf(),
              finishedAt: moment(values.finishedAt).startOf('day').valueOf(),
            });
          }}
        >
          <Form.Item style={{ display: 'none' }} name="id">
            <Input />
          </Form.Item>
          <Form.Item
            name="title"
            label={goalLocales.title}
            rules={[
              {
                required: true,
                whitespace: true,
                message: `${goalLocales.title}必填`,
              },
              { max: 50, message: '最多输入 50 个字符！' },
            ]}
          >
            <Input />
          </Form.Item>
          <Row>
            <Col {...colSpan}>
              <Form.Item
                {...itemLayout}
                name="percent"
                label={goalLocales.percent}
                rules={[
                  {
                    required: true,
                    message: `${goalLocales.percent}必填`,
                  },
                  {
                    validator: (_, val) => {
                      if (
                        val !== undefined &&
                        val !== null &&
                        (val <= 0 || (maxWeight !== undefined && val > maxWeight))
                      ) {
                        return Promise.reject('请调整已有目标的权重');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber
                  style={{ width: 216 }}
                  min={1}
                  max={100}
                  precision={0}
                  addonAfter="%"
                />
              </Form.Item>
            </Col>
            <Col {...colSpan}>
              <Form.Item
                {...itemLayout}
                name="status"
                label={goalLocales.status._self}
                rules={[
                  {
                    required: true,
                    message: `${goalLocales.status._self}必选`,
                  },
                ]}
              >
                <Select
                  style={{ width: 216 }}
                  getPopupContainer={trigger => trigger.parentNode.parentNode}
                  options={Object.keys(goalLocales.status.enum).map(key => ({
                    label: goalLocales.status.enum[key as BackendObjectiveStatus],
                    value: key,
                  }))}
                />
              </Form.Item>
            </Col>
          </Row>
          <Form.Item
            name="content"
            label={goalLocales.content}
            rules={[
              {
                required: true,
                whitespace: true,
                message: `${goalLocales.content}必填`,
              },
              { max: 10000, message: '最多输入 10000 个字符！' },
            ]}
          >
            <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
          </Form.Item>
          <Form.Item
            name="measurements"
            label={goalLocales.measurements}
            rules={[
              {
                required: true,
                whitespace: true,
                message: `${goalLocales.measurements}必填`,
              },
              { max: 10000, message: '最多输入 10000 个字符！' },
            ]}
          >
            <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
          </Form.Item>
          <Row>
            <Col {...colSpan}>
              <Form.Item
                {...itemLayout}
                name="startedAt"
                label={goalLocales.startedAt}
                dependencies={['finishedAt']}
                rules={[
                  {
                    required: true,
                    message: `${goalLocales.startedAt}必选`,
                  },
                  ({ getFieldValue }) => ({
                    validator: (_, val) => {
                      const finishedAt = getFieldValue('finishedAt');
                      if (val && finishedAt && moment(val).diff(finishedAt) > 0) {
                        return Promise.reject('开始日期不可晚于目标完成日期');
                      }
                      return Promise.resolve();
                    },
                  }),
                ]}
              >
                <DatePicker
                  style={{ width: 216 }}
                  disabledDate={current => {
                    /**不早于入职日期，不晚于试用期截止日期 */
                    return current && !!hiredDate && isOutTestPeriod(current);
                  }}
                  onChange={() => {
                    form.setFieldsValue({ finishDate: undefined });
                  }}
                />
              </Form.Item>
            </Col>
            <Col {...colSpan}>
              <Form.Item shouldUpdate={(pre, next) => pre.startDate !== next.startDate} noStyle>
                {({ getFieldValue }) => {
                  return (
                    <Form.Item
                      labelCol={{ ...colSpan }}
                      name="finishedAt"
                      label={goalLocales.finishedAt}
                      rules={[
                        {
                          required: true,
                          message: `${goalLocales.finishedAt}必选`,
                        },
                      ]}
                    >
                      <DatePicker
                        style={{ width: 216 }}
                        disabledDate={current => {
                          /**不早于入职日期和开始日期，不晚于试用期截止日期 */
                          return (
                            current &&
                            !!hiredDate &&
                            ((getFieldValue('startedAt') &&
                              getFieldValue('startedAt').diff(current) > 0) ||
                              isOutTestPeriod(current))
                          );
                        }}
                      />
                    </Form.Item>
                  );
                }}
              </Form.Item>
            </Col>
          </Row>
          {footer && (
            <Form.Item {...tailLayout}>
              <Space size="middle">
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    const now = Date.now();
                    form
                      .validateFields()
                      .then(async values => {
                        setLoading(true);
                        const { error, data } = await savePerformance({
                          userId,
                          superiorIds,
                          hiredDate,
                          id: performanceId,
                          type: 'TARGET',
                          goals: [
                            {
                              ...values,
                              type: 'BIZ',
                              createdAt: now,
                              modifiedAt: now,
                              startDate: moment(values.startDate).startOf('day').valueOf(),
                              finishDate: moment(values.finishDate).startOf('day').valueOf(),
                            },
                          ],
                        });
                        setLoading(false);
                        if (error) {
                          message.error(error.message);
                          return;
                        }
                        message.success('保存成功！');
                        if (data) {
                          onOK?.(data);
                        }
                      })
                      .catch(console.error);
                  }}
                >
                  保存
                </Button>
                {!goal ? (
                  <Popconfirm
                    title="取消填写后填写内容将被清空"
                    okText="确认取消"
                    cancelText="我再想想"
                    onConfirm={onCancel}
                  >
                    <Button htmlType="button" disabled={loading}>
                      取消
                    </Button>
                  </Popconfirm>
                ) : (
                  <Button htmlType="button" disabled={loading} onClick={onCancel}>
                    取消
                  </Button>
                )}
              </Space>
            </Form.Item>
          )}
        </Form>
      </Card>
    );
  }
);

GoalItemMutator.displayName = 'GoalItemMutator';
