import React, { useImperativeHandle } from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Space } from '@manyun/base-ui.ui.space';

import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';

import { GoalItemDeleter } from './​goal-item-deleter';
import { GoalItemMutator } from './​goal-item-mutator';

export type GoalsFormRefProps = {
  validateFields: () => Promise<PerformanceObjectiveJSON[]>;
};

export type GoalsFormProps = {
  performanceInfos: {
    userId: number;
    superiorIds: number[];
    hiredDate: number;
    performanceId?: number;
  };
  defaultValues: PerformanceObjectiveJSON[];
  onItemChangeValue: (newItemValues: Partial<PerformanceObjectiveJSON>) => void;
  onItemDelete: (itemId: number | string) => void;
};

export const GoalsForm = React.forwardRef(
  (
    { performanceInfos, defaultValues, onItemChangeValue, onItemDelete }: GoalsFormProps,
    ref?: React.Ref<GoalsFormRefProps | undefined>
  ) => {
    const refMapper = new Map(
      defaultValues.map(defaultValues => [
        defaultValues.id,
        React.createRef<FormInstance<PerformanceObjectiveJSON>>(),
      ])
    );

    useImperativeHandle(ref, () => ({
      validateFields: () => {
        const promiseAll: FormInstance<PerformanceObjectiveJSON>[] = [];
        refMapper.forEach(ref => {
          if (ref.current) {
            promiseAll.push(ref.current);
          }
        });
        return Promise.all(
          promiseAll.map(formInstance => {
            return formInstance.validateFields();
          })
        );
      },
    }));

    return (
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        {defaultValues.map((goal, index) => (
          <GoalItemMutator
            ref={refMapper.get(goal.id)}
            key={`goal_${goal.id}`}
            index={index}
            performanceInfos={performanceInfos}
            footer={false}
            extra={
              <GoalItemDeleter
                goal={goal}
                isRequest={false}
                onSuccess={deleteItem => {
                  onItemDelete(deleteItem.id);
                }}
              />
            }
            goal={goal}
            onValuesChange={newItemValues => {
              onItemChangeValue(newItemValues);
            }}
          />
        ))}
      </Space>
    );
  }
);

GoalsForm.displayName = 'GoalsForm';
