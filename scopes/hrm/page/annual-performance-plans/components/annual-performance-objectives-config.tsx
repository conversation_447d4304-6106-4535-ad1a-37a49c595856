import moment from 'moment';
import React, { useMemo, useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Form } from '@manyun/base-ui.ui.form';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import { fetchAnnualPerformanceObjectiveConfig } from '@manyun/hrm.service.fetch-annual-performance-objective-config';
import { updateAnnualPerformanceObjectiveConfig } from '@manyun/hrm.service.update-annual-performance-objective-config';

export type AnnualPerformanceObjectivesConfigButtonProps = ButtonProps;

export const AnnualPerformanceObjectivesConfigButton = ({
  ...restProps
}: AnnualPerformanceObjectivesConfigButtonProps) => {
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-annual-performance-objectives-config',
  });

  return (
    <>
      <Button
        {...restProps}
        onClick={() => {
          setOpen(true);
          /**请求详情接口 */
          (async () => {
            setLoading(true);
            const { error, data } = await fetchAnnualPerformanceObjectiveConfig();
            setLoading(false);

            if (error) {
              message.error(error.message);
              return;
            }

            if (data) {
              form.setFieldsValue({
                confirmDateRange:
                  data.confirmStartTime && data.confirmEndTime
                    ? [
                        moment(`${moment().format('YYYY')}-${data.confirmStartTime}`),
                        moment(`${moment().format('YYYY')}-${data.confirmEndTime}`),
                      ]
                    : undefined,
                confirmDays: data.confirmNotifyDays ?? [],
                notEvalHiredDateLaterThanDate: moment(
                  `${moment().format('YYYY')}-${data.needNotEvalHiredDate}`
                ),
                needNotEvalDay: data.needNotEvalDay,
              });
            }
          })();
        }}
      >
        全局配置
      </Button>
      <Modal
        title="全局配置"
        footer={authorized ? undefined : null}
        open={open}
        width={840}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          form.resetFields();
          setOpen(false);
        }}
        onOk={() => {
          form
            .validateFields()
            .then(async values => {
              setSubmitLoading(true);
              const confirmStartTime = moment(values.confirmDateRange?.[0]).format('MM-DD');
              const confirmEndTime = moment(values.confirmDateRange?.[1]).format('MM-DD');

              if (confirmStartTime === '02-29' || confirmEndTime === '02-29') {
                message.warning('不支持设置每年的 02-29 ');
                setSubmitLoading(false);
                return;
              }
              const { error } = await updateAnnualPerformanceObjectiveConfig({
                confirmStartTime,
                confirmEndTime,
                confirmNotifyDays: values.confirmDays,
                needNotEvalHiredDate: moment(values.notEvalHiredDateLaterThanDate).format('MM-DD'),
                needNotEvalDay: values.needNotEvalDay,
              });
              setSubmitLoading(false);
              if (error) {
                message.error(error.message);
                return;
              }
              message.success('保存成功');
              setOpen(false);
            })
            .catch(console.error);
        }}
      >
        {loading ? (
          <Skeleton />
        ) : (
          <Form form={form} labelCol={{ span: 5 }}>
            <Form.Item label="目标确认期限" required>
              <Space size={4}>
                <span>&nbsp;员工需在每年的</span>
                <Form.Item
                  name="confirmDateRange"
                  rules={[
                    {
                      required: true,
                      message: '目标确认期限必填',
                    },
                  ]}
                  noStyle
                >
                  <DatePicker.RangePicker
                    style={{ width: 216 }}
                    disabled={!authorized}
                    format="MM-DD"
                    onChange={() => {
                      form.setFieldsValue({
                        confirmDays: undefined,
                      });
                    }}
                  />
                </Form.Item>
                <Explanation
                  style={{ color: 'inherit' }}
                  iconType="question"
                  tooltip={{ title: '如重新发起考核，则会根据新的发起时间重新计算截止时间' }}
                >
                  完成年度目标确认
                </Explanation>
              </Space>
            </Form.Item>
            <Form.Item
              label="目标确认提醒"
              shouldUpdate={(pre, next) => {
                return pre.confirmDateRange !== next.confirmDateRange;
              }}
            >
              {({ getFieldValue }) => {
                const confirmDateRange: [moment.Moment, moment.Moment] | undefined =
                  getFieldValue('confirmDateRange');
                const diffDays = confirmDateRange
                  ? confirmDateRange[1].diff(confirmDateRange[0], 'day')
                  : 0;
                return (
                  <Space size={4}>
                    <span>&nbsp;距目标确认截止日期</span>

                    <Form.Item name="confirmDays" noStyle>
                      <Select
                        style={{ width: 216 }}
                        disabled={!authorized}
                        maxTagCount="responsive"
                        mode="multiple"
                        options={[20, 15, 7, 3, 1]
                          .filter(key => key <= diffDays)
                          .map(key => ({ label: `${key}天`, value: key }))}
                      />
                    </Form.Item>
                    <span>时，员工仍未确认，则发送消息提醒</span>
                  </Space>
                );
              }}
            </Form.Item>
            <Form.Item label={locales.notEvalHiredDateLaterThanDate} required>
              <Space size={4}>
                <span>&nbsp;入职日期晚于当年</span>
                <Form.Item
                  name="notEvalHiredDateLaterThanDate"
                  noStyle
                  rules={[
                    {
                      required: true,
                      message: `${locales.notEvalHiredDateLaterThanDate}必填`,
                    },
                  ]}
                >
                  <DatePicker style={{ width: 216 }} disabled={!authorized} format="MM-DD" />
                </Form.Item>
                <span>的人员，无需参与当年的年度绩效考核</span>
              </Space>
            </Form.Item>
            <Form.Item label={locales.notEvalHiredDateLaterThanDateByDaily} required>
              <Space size={4}>
                <span>&nbsp;员工在当季度在职不满</span>
                <Form.Item
                  name="needNotEvalDay"
                  noStyle
                  rules={[
                    {
                      required: true,
                      message: `${locales.notEvalHiredDateLaterThanDateByDaily}必填`,
                    },
                  ]}
                >
                  <InputNumber
                    style={{ width: 88 }}
                    precision={0}
                    min={1}
                    max={90}
                    disabled={!authorized}
                    formatter={value => `${value}天`}
                    parser={value => value!.replace('天', '')}
                  />
                </Form.Item>
                <Explanation
                  style={{ color: 'inherit' }}
                  iconType="question"
                  tooltip={{
                    title: (
                      <>
                        1.员工请产假+病假+事假后，剩余在岗不满所设置的天数，无需参与该季度考核
                        <br />
                        2.无需参与考核的季度，无季度得分，不计入年度最终得分计算
                        <br />
                        3.若员工在本年度内，无需参与任一季度考核，则不需要参与该年度绩效考核
                      </>
                    ),
                  }}
                >
                  ，则无需参与该季度绩效考核
                </Explanation>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Modal>
    </>
  );
};
