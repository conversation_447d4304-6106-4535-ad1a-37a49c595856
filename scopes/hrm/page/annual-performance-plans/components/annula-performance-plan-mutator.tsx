import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import type { AnnualPerformancePlanJSON } from '@manyun/hrm.model.annual-performance-plan';
import {
  ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH,
  generateAnnualPerformancePlanEdit,
} from '@manyun/hrm.route.hrm-routes';

export type AnnualPerformancePlanButtonProps = {
  record?: AnnualPerformancePlanJSON;
} & Omit<ButtonProps, 'onClick'>;

export const AnnualPerformancePlanMutatorButton = ({
  record,
  ...restProps
}: AnnualPerformancePlanButtonProps) => {
  const history = useHistory();
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-annual-performance-plan-mutator',
  });
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);

  if (!authorized && record?.createdBy.id !== userId) {
    return null;
  }

  return (
    <Button
      {...restProps}
      onClick={() => {
        history.push(
          record
            ? generateAnnualPerformancePlanEdit({ id: record.id.toString() })
            : ANNUAL_PERFORMANCE_PLAN_CREATE_ROUTE_PATH
        );
      }}
    >
      {record ? '修改配置' : '发起考核计划'}
    </Button>
  );
};
