import uniq from 'lodash.uniq';
import moment from 'moment';
import React from 'react';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { fetchUsersByIds } from '@manyun/auth-hub.service.fetch-users-by-ids';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import type { UserSelectProps } from '@manyun/auth-hub.ui.user-select/user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import {
  useCreatePerformanceWhiteList,
  useDeletePerformanceWhiteList,
  useLazyPerformanceWhiteList,
  useLazyPerformanceWhiteListYears,
  useUpdatePerformanceWhiteList,
} from '@manyun/hrm.gql.client.hrm';
import type { PerformanceWhiteListItem } from '@manyun/hrm.gql.client.hrm';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import { PerformanceUserSelect } from '@manyun/hrm.ui.performance-user-select';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';

const QUARTES = ['Q1', 'Q2', 'Q3', 'Q4'];
export const WhiteListConfiguration = () => {
  const [open, setOpen] = React.useState(false);
  const [year, setYear] = React.useState(moment().get('year').toString());
  const [staffId, setStaffId] = React.useState<number | undefined>(undefined);
  const [currentPage, setCurrentPage] = React.useState(1);
  const planLocales = React.useMemo(() => getAnnualPerformancePlanLocales(), []);
  const [, { checkCode }] = useAuthorized();
  const [staffJobNoMapper, onLoadUserByIds] = useFetchStaffsJobNo();
  const [onGetPerformanceWhiteList, { loading, data, refetch }] = useLazyPerformanceWhiteList();
  const [onDelete, { loading: deleteLoading }] = useDeletePerformanceWhiteList();
  const [onLoadPerformanceWhiteListYears, { data: yearsResult, refetch: refetchYears }] =
    useLazyPerformanceWhiteListYears();

  React.useEffect(() => {
    onGetPerformanceWhiteList({
      variables: {
        query: {
          year,
          staffId,
        },
      },
    });
  }, [year, staffId, onGetPerformanceWhiteList]);

  const includeYears = React.useMemo(() => {
    return yearsResult?.performanceWhiteListYears?.data ?? [];
  }, [yearsResult?.performanceWhiteListYears?.data]);

  const hasOperationPermission = React.useMemo(
    () => checkCode('element_hrm-performance-white-list-operate'),
    [checkCode]
  );

  const staffIds = React.useMemo(() => {
    return uniq(data?.performanceWhiteList?.data.map(item => item.staffId) ?? []);
  }, [data?.performanceWhiteList?.data]);

  useDeepCompareEffect(() => {
    if (staffIds.length > 0) {
      onLoadUserByIds(staffIds);
    }
  }, [onLoadUserByIds, staffIds]);

  if (!checkCode('element_hrm-performance-white-list-view')) {
    return null;
  }
  return (
    <>
      <Button
        onClick={() => {
          onLoadPerformanceWhiteListYears();
          setOpen(true);
        }}
      >
        白名单
      </Button>
      <Drawer
        title="白名单"
        width={960}
        open={open}
        onClose={() => {
          setYear(moment().get('year').toString());
          setStaffId(undefined);
          setOpen(false);
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            {hasOperationPermission && (
              <WhiteListItemMutator
                onOk={() => {
                  refetchYears();
                  refetch();
                }}
              />
            )}
            <Space>
              <Select
                style={{ width: 216 }}
                placeholder="输入员工姓名查询"
                allowClear
                value={staffId}
                options={(data?.performanceWhiteList?.data ?? []).map(item => ({
                  label: item.staffName,
                  value: item.staffId,
                }))}
                showSearch
                filterOption={(input, option) =>
                  (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                }
                onChange={value => {
                  setStaffId(value as unknown as number | undefined);
                  setCurrentPage(1);
                }}
              />
              <DatePicker
                style={{ width: 140 }}
                picker="year"
                format={value => `${value.format('YYYY')}年度`}
                value={moment().set('year', Number(year))}
                allowClear={false}
                disabledDate={current =>
                  current && !includeYears.includes(current.year().toString())
                }
                onChange={value => {
                  value && setYear(moment(value).get('year').toString()); /* 只能选择有数据的年份 */
                  setCurrentPage(1);
                }}
              />
            </Space>
          </Space>
          <Table
            size="middle"
            loading={loading}
            dataSource={data?.performanceWhiteList?.data}
            columns={[
              {
                title: '员工姓名',
                dataIndex: 'staffName',
                render: (_, record) => {
                  return (
                    <UserLink
                      userId={record.staffId}
                      userName={record.staffName}
                      native
                      target="_blank"
                    />
                  );
                },
              },
              {
                title: '员工工号',
                dataIndex: 'staffJobNo',
                render: (_, record) => {
                  return staffJobNoMapper[record.staffId];
                },
              },
              {
                title: '无需考核周期',
                dataIndex: 'periods',
                render: value => {
                  return (value ?? [])
                    .map(
                      (period: BackendPerformancePeriod) =>
                        planLocales.performancePeriod.enum[period]
                    )
                    .join('｜');
                },
              },
              {
                title: '添加人',
                dataIndex: 'createdBy',
                render: val => val.name ?? '--',
              },
              {
                title: '添加时间',
                dataIndex: 'createdAt',
                render: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
              },
              {
                title: '更新时间',
                dataIndex: 'modifiedAt',
                render: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
              },
              {
                title: '操作',
                dataIndex: 'action',
                width: 111,
                render: (_, record) => {
                  const currentYear = moment().set('year', Number(record?.year));
                  if (
                    currentYear.isBetween(
                      moment().subtract(1, 'year'),
                      moment().add(1, 'year'),
                      'year',
                      '[]'
                    ) &&
                    hasOperationPermission
                  ) {
                    return (
                      <Space size={16}>
                        <WhiteListItemMutator
                          record={record}
                          onOk={() => {
                            refetch();
                          }}
                        />
                        <Popconfirm
                          title={
                            <>
                              确认删除该白名单？删除后不可恢
                              <br />
                              复，请谨慎操作！
                            </>
                          }
                          placement="topRight"
                          okText="确认删除"
                          cancelText="取消"
                          okButtonProps={{ loading: deleteLoading }}
                          onConfirm={async () => {
                            const { data } = await onDelete({
                              variables: {
                                id: record.id,
                              },
                            });
                            if (data?.deletePerformanceWhiteList?.message) {
                              message.error(data.deletePerformanceWhiteList.message);
                              return;
                            }
                            message.success('删除成功');
                            refetch();
                            refetchYears();
                          }}
                        >
                          <Button type="link" compact>
                            删除
                          </Button>
                        </Popconfirm>
                      </Space>
                    );
                  }

                  return '--';
                },
              },
            ]}
            pagination={{
              current: currentPage,
              onChange: page => setCurrentPage(page),
            }}
          />
        </Space>
      </Drawer>
    </>
  );
};

type WhiteListItemMutatorProps = {
  record?: PerformanceWhiteListItem;
  onOk?: () => void;
};
const WhiteListItemMutator = ({ record, onOk }: WhiteListItemMutatorProps) => {
  const isEditable = React.useMemo(() => !!record, [record]);
  const [open, setOpen] = React.useState(false);
  const [form] = Form.useForm();
  const year = Form.useWatch('year', form);

  const planLocales = React.useMemo(() => getAnnualPerformancePlanLocales(), []);

  const [onCreate, { loading: createLoading }] = useCreatePerformanceWhiteList();
  const [onUpdate, { loading: updateLoading }] = useUpdatePerformanceWhiteList();
  const [onGetPerformanceWhiteList] = useLazyPerformanceWhiteList();

  return (
    <>
      <Button
        type={isEditable ? 'link' : 'default'}
        compact={isEditable}
        onClick={() => {
          form.resetFields();
          setOpen(true);
          if (record) {
            form.setFieldsValue({
              staffId: record.staffId,
              year: moment().set('year', Number(record?.year)),
              periods: record.periods,
            });
          }
        }}
      >
        {isEditable ? '编辑' : '添加白名单'}
      </Button>
      <Modal
        title={isEditable ? '编辑白名单' : '添加白名单'}
        open={open}
        width={590}
        okButtonProps={{ loading: createLoading || updateLoading }}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          form.validateFields().then(async values => {
            if (record) {
              const { data: update } = await onUpdate({
                variables: {
                  params: {
                    id: record.id,
                    staffId: record.staffId,
                    year: record.year,
                    periods: values.periods,
                  },
                },
              });
              if (update?.updatePerformanceWhiteList?.message) {
                message.error(update.updatePerformanceWhiteList.message);
                return;
              }
            } else {
              const { data: create } = await onCreate({
                variables: {
                  params: {
                    staffId: values.staffId,
                    year: moment(values.year).get('year').toString(),
                    periods: values.periods,
                  },
                },
              });
              if (create?.createPerformanceWhiteList?.message) {
                message.error(create.createPerformanceWhiteList.message);
                return;
              }
            }
            message.success(isEditable ? '编辑成功' : '添加成功');
            setOpen(false);
            onOk?.();
          });
        }}
      >
        {!record && (
          <Alert
            style={{ marginBottom: 24 }}
            message="添加白名单后，则该员工该周期无需参与绩效考核。"
            type="info"
            showIcon
          />
        )}
        <Form
          form={form}
          labelCol={{ span: 7 }}
          initialValues={{
            year: moment(),
          }}
        >
          <Form.Item label="员工姓名" required>
            <Space style={{ width: '100%' }} align="center">
              <Form.Item
                style={{ marginBottom: 0 }}
                name="staffId"
                noStyle
                dependencies={['year']}
                rules={[
                  { required: true, message: '请选择员工姓名' },
                  {
                    validator: async (_, val) => {
                      if (!isEditable && val && year) {
                        // 验证员工是否已在该年度白名单中
                        const { data } = await onGetPerformanceWhiteList({
                          variables: {
                            query: {
                              staffId: val,
                              year: moment(year).get('year').toString(),
                            },
                          },
                        });
                        if ((data?.performanceWhiteList?.data ?? []).length > 0) {
                          return Promise.reject('员工已在该年度白名单中，如需修改请在列表中编辑');
                        }
                        return Promise.resolve();
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <UserSelectWrapper
                  style={{ width: 104 }}
                  disabled={!!record}
                  userName={record?.staffName}
                  labelInValue={false}
                />
              </Form.Item>
            </Space>
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }} label="无需参与考核的周期" required>
            <Space style={{ width: '100%' }}>
              <Form.Item name="year" rules={[{ required: true, message: '请设置年份' }]}>
                <DatePicker
                  style={{ width: 104 }}
                  picker="year"
                  format={value => `${value.format('YYYY')}年`}
                  allowClear={false}
                  disabledDate={current =>
                    current &&
                    !current.isBetween(
                      moment().subtract(1, 'year'),
                      moment().add(1, 'year'),
                      'year',
                      '[]'
                    )
                  }
                  disabled={!!record}
                />
              </Form.Item>
              <Form.Item
                name="periods"
                rules={[{ required: true, message: '请设置无需参与考核的周期' }]}
              >
                <Select
                  style={{ width: 278 }}
                  mode="multiple"
                  placeholder="请设置无需参与考核的周期"
                  options={Object.keys(planLocales.performancePeriod.enum)
                    .filter(key => !['FIR_HALF_YEAR', 'SEC_HALF_YEAR'].includes(key as string))
                    .map(key => ({
                      label: planLocales.performancePeriod.enum[key as BackendPerformancePeriod],
                      value: key,
                    }))}
                  onChange={value => {
                    //tips: 选中Q1~Q4时，自动选中YEAR
                    if (
                      value &&
                      Array.isArray(value) &&
                      value.length === QUARTES.length &&
                      value.every(v => QUARTES.includes(v))
                    ) {
                      form.setFieldValue('periods', [...value, 'YEAR']);
                    }
                  }}
                />
              </Form.Item>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

type UserSelectWrapperProps = Pick<
  UserSelectProps,
  'value' | 'onChange' | 'disabled' | 'style' | 'labelInValue'
> & {
  id?: string;
  userName?: string;
};
const UserSelectWrapper = ({ id, value, userName, ...restProps }: UserSelectWrapperProps) => {
  const [staffJobNoMapper, onLoadUserByIds] = useFetchStaffsJobNo();
  React.useEffect(() => {
    if (value) {
      onLoadUserByIds([value]);
    }
  }, [onLoadUserByIds, value]);

  return (
    <Space id={id}>
      {userName && restProps.disabled ? (
        <Input {...restProps} value={userName} />
      ) : (
        <PerformanceUserSelect allowClear {...restProps} value={value} />
      )}
      {value && staffJobNoMapper[value] && (
        <Typography.Text type="secondary">工号: {staffJobNoMapper[value]}</Typography.Text>
      )}
    </Space>
  );
};

const useFetchStaffsJobNo = () => {
  const [staffJobNoMapper, setStaffJobNoMapper] = React.useState<{
    [key: number]: string;
  }>({});

  const onLoadUserByIds = React.useCallback(async (userIds: number[]) => {
    const { data, error } = await fetchUsersByIds({
      ids: userIds,
    });
    if (error) {
      setStaffJobNoMapper({});
      message.error(error.message);
      return;
    }
    setStaffJobNoMapper(
      data.reduce(
        (acc, cur) => {
          acc[cur.id] = cur.jobNumber;
          return acc;
        },
        {} as {
          [key: number]: string;
        }
      )
    );
  }, []);

  return [staffJobNoMapper, onLoadUserByIds] as const;
};
