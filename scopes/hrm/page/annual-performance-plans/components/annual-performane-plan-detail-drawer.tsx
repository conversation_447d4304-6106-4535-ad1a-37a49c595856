import moment from 'moment';
import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyPerformancePlanById } from '@manyun/hrm.gql.client.hrm';
import type { AnnualPerformancePlanJSON } from '@manyun/hrm.model.annual-performance-plan';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';

import { TextDivider } from './text-divider';

export type AnnualPerformancePlanDetailButtonProps = {
  record: Pick<AnnualPerformancePlanJSON, 'id' | 'createdBy'>;
  onClose?: () => void;
};

export const AnnualPerformancePlanDetailButton = ({
  record,
  onClose,
}: AnnualPerformancePlanDetailButtonProps) => {
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const [open, setOpen] = useState(false);
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-annual-performance-plan-detail',
  });
  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  const [fetch, { loading, data }] = useLazyPerformancePlanById();

  const descItemsGroup = useDeepCompareMemo(
    () => [
      {
        key: 'basic',
        title: '基本信息',
        children: [
          {
            label: locales.year,
            value: `${data?.performancePlanById?.year ?? ''}年度`,
          },
          {
            label: locales.name,
            value: data?.performancePlanById?.name,
          },
          {
            label: locales.resourcesScope,
            value: <TextDivider values={data?.performancePlanById?.resourcesScope ?? []} />,
          },
          {
            label: locales.positionScope,
            value: <TextDivider values={data?.performancePlanById?.positionScope ?? []} />,
          },
        ],
      },
      {
        key: 'config',
        title: '考核配置',
        children: [
          // {
          //   label: locales.splitRule.__self,
          //   value: data?.performancePlanById?.splitRule
          //     ? locales.splitRule.enum[data?.performancePlanById.splitRule]
          //     : [],
          // },
          {
            label: locales.subTasks,
            value: (
              <Space style={{ width: '100%' }} size="middle" direction="vertical">
                {(data?.performancePlanById?.subConfigs ?? []).map(config => (
                  <Space key={config.period}>
                    <Typography.Text>
                      {`${locales.performancePeriod.enum[config.period]}自评起止日期：`}
                    </Typography.Text>
                    <Explanation
                      style={{ color: 'inherit' }}
                      iconType="question"
                      tooltip={{
                        title: `${
                          locales.performancePeriodDesc.enum[config.period]
                        }考核周期为${moment(config.natureStartTimeRange[0]).format(
                          'YYYY-MM-DD'
                        )} ~ ${moment(config.natureStartTimeRange[1]).format('YYYY-MM-DD')}`,
                      }}
                    >
                      {`${moment(config.evalStartTimeRange[0]).format('YYYY-MM-DD')} ~ ${moment(
                        config.evalStartTimeRange[1]
                      ).format('YYYY-MM-DD')}`}
                    </Explanation>
                  </Space>
                ))}
              </Space>
            ),
          },
          {
            label: locales.beforeSubTaskDeadlineNotifyDays,
            value: (
              <Space size={4} wrap>
                <span>距子任务自评截止日期</span>
                {data?.performancePlanById?.beforeSubTaskDeadlineNotifyDays.map(day => (
                  <Tag key={day}>{`${day}天`}</Tag>
                ))}
                <span>时，员工仍未确认，则发送消息提醒</span>
              </Space>
            ),
          },
          {
            label: locales.canSetResultDate,
            value: (
              <Space size={4}>
                <span>直线经理可在</span>
                {moment(data?.performancePlanById?.canSetResultDate).format('YYYY-MM-DD')}
                <span>起，填写年度考核结果评分</span>
              </Space>
            ),
          },
        ],
      },
    ],
    [data, locales]
  );

  if (!authorized && record.createdBy.id !== userId) {
    return null;
  }

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          fetch({
            variables: {
              id: record.id,
            },
          });
        }}
      >
        查看
      </Button>
      <Drawer
        width={612}
        open={open}
        title="查看考核计划"
        onClose={() => {
          setOpen(false);
          onClose?.();
        }}
      >
        {loading ? (
          <Skeleton />
        ) : (
          <Space direction="vertical" size="large">
            {descItemsGroup.map(group => (
              <Descriptions key={group.key} title={group.title} column={1}>
                {group.children.map(item => (
                  <Descriptions.Item key={item.label} label={item.label}>
                    {item.value}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            ))}
          </Space>
        )}
      </Drawer>
    </>
  );
};
