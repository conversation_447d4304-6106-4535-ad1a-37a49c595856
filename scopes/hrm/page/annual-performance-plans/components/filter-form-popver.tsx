import React, { useMemo, useState } from 'react';

import { FilterOutlined } from '@ant-design/icons';
import chunk from 'lodash.chunk';
import flatten from 'lodash.flatten';
import type { Moment } from 'moment';
import moment from 'moment';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormItemProps } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import type { Fields } from '../annual-performance-plans';

type SearchValues = Omit<Fields, 'name' | 'page' | 'pageSize'>;

export type FilterFormPopoverProps = {
  values: SearchValues;
  onSearch: (params: SearchValues) => void;
};

type FilterFormValue = Omit<
  SearchValues,
  'createTimeRange' | 'modifiedTimeRange' | 'resourceCodes' | 'year'
> & {
  createTimeRange?: [Moment, Moment];
  year?: Moment;
  resourceCodes?: string[][];
};

type ItemFieldName = keyof FilterFormValue;

type FormItem = {
  label: React.ReactNode;
  name: ItemFieldName;
  colSpan: number;
  children: JSX.Element;
} & FormItemProps;

export function FilterFormPopover({ values, onSearch }: FilterFormPopoverProps) {
  const [form] = Form.useForm<FilterFormValue>();
  const [visible, setVisible] = useState(false);
  const hasFilter = useDeepCompareMemo(() => {
    return (
      values &&
      Object.values(values).some(value =>
        Array.isArray(value) ? value.length !== 0 : value !== undefined
      )
    );
  }, [values]);

  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  const formItemProps = useMemo(() => {
    return [
      {
        label: locales.year,
        name: 'year',
        colSpan: 12,
        children: <DatePicker style={{ width: '100%' }} picker="year" allowClear />,
      },
      // {
      //   label: locales.splitRule.__self,
      //   name: 'splitRule',
      //   colSpan: 12,
      //   children: (
      //     <Select
      //       style={{ width: '100%' }}
      //       options={Object.keys(locales.splitRule.enum).map(key => ({
      //         label: locales.splitRule.enum[key as BackendAnnualPerformancePlanSplitRule],
      //         value: key,
      //       }))}
      //       allowClear
      //     />
      //   ),
      // },
      {
        label: locales.positionScope,
        name: 'positions',
        colSpan: 12,
        children: <PerformancePositionSelect allowClear showDelete />,
      },
      {
        label: locales.resourcesScope,
        name: 'resourceCodes',
        colSpan: 24,
        children: (
          <LocationCascader
            showSearch
            authorizedOnly
            maxTagCount="responsive"
            nodeTypes={['IDC']}
            multiple
            allowClear
          />
        ),
      },
      {
        label: locales.createdBy.__self,
        name: 'createUserId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: locales.createdAt,
        name: 'createTimeRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
    ] as FormItem[];
  }, [
    locales.createdAt,
    locales.createdBy.__self,
    locales.positionScope,
    locales.resourcesScope,
    locales.year,
  ]);

  const onHandleSearch = () => {
    const formValue = form.getFieldsValue();
    const _values: Partial<Fields> = {
      ...formValue,
      resourceCodes: formValue.resourceCodes ? flatten(formValue.resourceCodes) : undefined,
      year: formValue.year ? moment(formValue.year).format('YYYY') : undefined,
      createTimeRange:
        formValue.createTimeRange && formValue.createTimeRange.length === 2
          ? [
              formValue.createTimeRange[0].startOf('day').valueOf(),
              formValue.createTimeRange[1].endOf('day').valueOf(),
            ]
          : undefined,
    };
    onSearch(_values);
  };

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => (
        <Card style={{ width: 544 }}>
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              {formItemProps.map(itemProps => {
                const { label, name, colSpan, children, ...rest } = itemProps;
                return (
                  <Col key={`${name}`} span={colSpan}>
                    <Form.Item label={label} name={name} {...rest}>
                      {children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Row justify="end">
              <Space>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setVisible(false);
                    onHandleSearch();
                  }}
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    onHandleSearch();
                    setVisible(false);
                  }}
                >
                  搜索
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={_visible => {
        if (_visible === true && values) {
          form.setFieldsValue({
            ...values,
            resourceCodes: values.resourceCodes ? chunk(values.resourceCodes) : undefined,
            year: values.year ? moment().set('year', Number(values.year)) : undefined,
            createTimeRange:
              values.createTimeRange && values.createTimeRange.length === 2
                ? [moment(values.createTimeRange[0]), moment(values.createTimeRange[1])]
                : undefined,
          });
        } else {
          onHandleSearch();
        }
        setVisible(_visible);
      }}
    >
      <Tooltip title="筛选">
        <Button
          size="small"
          icon={<FilterOutlined />}
          ghost={hasFilter}
          type={hasFilter ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}
