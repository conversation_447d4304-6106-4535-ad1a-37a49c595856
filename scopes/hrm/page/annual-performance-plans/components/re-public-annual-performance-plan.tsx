import React, { useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMe } from '@manyun/auth-hub.state.user';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { AnnualPerformancePlanJSON } from '@manyun/hrm.model.annual-performance-plan';
import { rePublicAnnualPerformancePlan } from '@manyun/hrm.service.re-public-annual-performance-plan';
import { PerformancePositionCheckboxGroup } from '@manyun/hrm.ui.performance-position';

export type RePublicAnnualPerformancePlanProps = {
  record: AnnualPerformancePlanJSON;
  onSuccss?: () => void;
};

export const RePublicAnnualPerformancePlan = ({
  record,
  onSuccss: onSuccess,
}: RePublicAnnualPerformancePlanProps) => {
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const [open, setOpen] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm();

  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-relaunch-annual-performance-plan',
  });

  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  if (!authorized && record.createdBy.id !== userId) {
    return null;
  }

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          form.resetFields();
        }}
      >
        重新发起考核
      </Button>
      <Modal
        title="重新发起考核"
        open={open}
        width={780}
        okButtonProps={{ loading: submitLoading }}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          form
            .validateFields()
            .then(async values => {
              setSubmitLoading(true);
              const { error } = await rePublicAnnualPerformancePlan({
                id: record.id,
                resourceCodes: values.resourcesScope,
                evalPositions: values.positionScope,
              });

              setSubmitLoading(false);

              if (error) {
                message.error(error.message);
                return;
              }
              message.success('重新发起成功');
              setOpen(false);
              onSuccess?.();
            })
            .catch(console.error);
        }}
      >
        <Space style={{ width: '100%' }} size="large" direction="vertical">
          <Alert
            message="确认重新发起考核？"
            description={
              <div>
                1.重新发起考核后，年度目标需重新确认和审批，请谨慎操作！
                <br />
                2.审批通过后，将对未开启的子考核任务生效，历史和进行中的任务不受影响。
              </div>
            }
            type="warning"
            showIcon
          />
          <Form form={form} labelCol={{ span: 3 }}>
            <Form.Item
              label={locales.resourcesScope}
              name="resourcesScope"
              rules={[
                {
                  required: true,
                  message: `${locales.resourcesScope}必选`,
                },
              ]}
            >
              <Select
                style={{ width: 400 }}
                mode="multiple"
                options={record.resourcesScope.map(({ label, value }) => ({
                  value,
                  label: `${value} ${label}`,
                }))}
                allowClear
              />
            </Form.Item>
            <Form.Item
              label={locales.positionScope}
              name="positionScope"
              rules={[
                {
                  required: true,
                  message: `${locales.positionScope}必选`,
                },
              ]}
            >
              <PerformancePositionCheckboxGroup options={record.positionScope} />
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
};
