import React from 'react';

import { Divider } from '@manyun/base-ui.ui.divider';
import { Space } from '@manyun/base-ui.ui.space';

export const TextDivider = ({ values }: { values: { label: string; value: string }[] }) => {
  return (
    <Space size={0} split={<Divider type="vertical" />} wrap>
      {values.map(({ label, value }) => (
        <span key={value}>{label}</span>
      ))}
    </Space>
  );
};
