import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { selectMe } from '@manyun/auth-hub.state.user';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { useUserAttendanceGroup } from '@manyun/hrm.hook.use-user-attendance-group';
import type { PunchType } from '@manyun/hrm.model.clock-in';
import { getClockInLocales } from '@manyun/hrm.model.clock-in';
import { generateSupplyCheckDetaillocation } from '@manyun/hrm.route.hrm-routes';
import { createPunchAShiftClock } from '@manyun/hrm.service.create-punch-a-shift-clock';
import { useLoggedInUser } from '@manyun/iam.gql.client.iam';

export function FixedMissedPunchCreator() {
  const [form] = Form.useForm();
  const { userId, name } = useSelector(selectMe);
  const history = useHistory();
  const [loading, setLoading] = useState(false);
  const { search } = useLocation();
  const { type } = getLocationSearchMap<{ type?: string }>(search);
  const locales = useMemo(() => getClockInLocales(), []);
  const { data } = useLoggedInUser({ fetchPolicy: 'no-cache' });
  const [{ attendance }, onLoadAttendance] = useUserAttendanceGroup();

  /**限制是否可提交历史月份的补卡 */
  const limitAddHistoryMonth = useMemo(() => {
    const limitAdditionalDays = attendance?.rule.supplyNotValidStartDay;
    if (attendance?.rule.enableSupplyTime && limitAdditionalDays) {
      const limitAdditionalDate = moment().date(limitAdditionalDays).startOf('day');
      return moment().isAfter(limitAdditionalDate);
    }
    return false;
  }, [attendance?.rule.enableSupplyTime, attendance?.rule.supplyNotValidStartDay]);

  useEffect(() => {
    if (data?.me?.id) {
      onLoadAttendance(data.me.id);
    }
  }, [data?.me?.id, onLoadAttendance]);

  const submit = useCallback(async () => {
    const values = await form.validateFields();
    setLoading(true);

    const { error, data } = await createPunchAShiftClock({
      ...values,
      attachments: (values?.attachments ?? []).map((attachment: McUploadFile) => ({
        ...attachment,
        type: null,
      })),
      checkTime: moment(values.checkTime).startOf('minute').valueOf(),
      staffId: userId,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success(locales.missedPunchesFix.submit.success ?? '创建成功！');

    if (data && data.bizId) {
      history.push(generateSupplyCheckDetaillocation({ id: data.bizId.toString() }));
    }
  }, [form, history, locales.missedPunchesFix.submit.success, userId]);

  useEffect(() => {
    if (type) {
      form.setFieldsValue({ type });
    }
  }, [form, type]);

  const typeOptions = useMemo(() => {
    const mapper = locales.punchType.enum;
    return Object.keys(mapper).map(key => ({
      label: mapper[key as PunchType],
      value: key,
    }));
  }, [locales.punchType.enum]);

  return (
    <Card title={locales.missedPunchesFix.basicInfo ?? '基本信息'}>
      <Form
        style={{ width: 640 }}
        form={form}
        colon={false}
        labelCol={{ span: 3 }}
        wrapperCol={{ span: 21 }}
      >
        <Form.Item label={locales.applyBy._self ?? '申请人'}>{name}</Form.Item>
        <Form.Item
          name="checkTime"
          label={locales.missedPunchesFix.punchTime._self ?? '补卡时间'}
          required
          rules={[
            {
              validator: (_, value) => {
                if (!value) {
                  return Promise.reject(
                    locales.missedPunchesFix.punchTime.validateMsg._self ?? '补卡时间必选'
                  );
                }
                if (value && value.diff(moment()) > 0) {
                  return Promise.reject(
                    locales.missedPunchesFix.punchTime.validateMsg.limit ?? '仅支持选择≤当前的时间'
                  );
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <DatePicker
            showTime={{ format: 'HH:mm' }}
            format="YYYY-MM-DD HH:mm"
            disabledDate={current => {
              if (!current) {
                return false;
              }

              return (
                (attendance !== null &&
                  // 限制补卡时间
                  attendance.rule.enableSupplyTime &&
                  attendance.rule.supplyValidDays !== undefined &&
                  moment()
                    .subtract(attendance.rule.supplyValidDays, 'day')
                    .startOf('day')
                    .diff(current) > 0) ||
                //限制不能为历史月份数据
                (limitAddHistoryMonth && moment().startOf('month').diff(current) > 0) ||
                //不能为未来时间
                current.diff(moment().endOf('day')) > 0
              );
            }}
            disabledTime={current => {
              return {
                disabledHours: () => {
                  if (current?.format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')) {
                    return range(moment().get('hour') + 1, 24);
                  }
                  return [];
                },
                disabledMinutes: selectedHour => {
                  if (
                    current?.get('date') === moment().get('date') &&
                    selectedHour === moment().get('hour')
                  ) {
                    return range(moment().get('minute'), 60);
                  }
                  return [];
                },
              };
            }}
          />
        </Form.Item>
        <Form.Item
          name="type"
          label={locales.punchType._self ?? '上班/下班'}
          rules={[
            {
              required: true,
              message: locales.missedPunchesFix.punchType.validateMsg._self ?? '上班/下班必选',
            },
          ]}
        >
          <Select style={{ width: 200 }} options={typeOptions} />
        </Form.Item>
        <Form.Item
          name="reason"
          label={locales.missedPunchesFix.reason._self ?? '补卡原因'}
          rules={[
            {
              required: true,
              message: locales.missedPunchesFix.reason.validateMsg._self ?? '补卡原因必填！',
            },
            {
              max: 200,
              message: locales.missedPunchesFix.reason.validateMsg.limit ?? '最多输入 200 个字符！',
            },
          ]}
        >
          <Input.TextArea style={{ width: 300 }} />
        </Form.Item>
        <Form.Item
          name="attachments"
          label={locales.missedPunchesFix.files ?? '附件'}
          valuePropName="fileList"
          getValueFromEvent={value => {
            if (typeof value === 'object') {
              return value.fileList;
            }
          }}
        >
          <Upload accept=".png,.jpg" maxFileSize={20} allowDelete showAccept showUploadList>
            <Button type="primary">上传</Button>
          </Upload>
        </Form.Item>
        <Form.Item wrapperCol={{ offset: 3, span: 21 }}>
          <Space size={16} style={{ marginTop: 20 }}>
            <Button
              type="primary"
              htmlType="submit"
              loading={loading}
              disabled={!attendance?.id}
              onClick={() => {
                submit();
              }}
            >
              {locales.missedPunchesFix.submit._self ?? '提交'}
            </Button>
            <Button
              htmlType="button"
              disabled={loading}
              onClick={() => {
                history.goBack();
              }}
            >
              {locales.missedPunchesFix.cancle ?? '取消'}
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Card>
  );
}

const range = (start: number, end: number) => {
  const result = [];
  for (let i = start; i < end; i++) {
    result.push(i);
  }
  return result;
};
