import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import type { ApolloClient } from '@apollo/client';
import { useApolloClient } from '@apollo/client';
import groupby from 'lodash.groupby';
import uniq from 'lodash.uniq';
import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useHistory, useLocation, useParams } from 'react-router';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { Divider } from '@manyun/base-ui.ui.divider';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import type { Space as SpaceModel } from '@manyun/dc-brain.gql.client';
import { scrollToField } from '@manyun/dc-brain.util.scroll-to-field';
import { useLazyAnnualPerformanceObjective } from '@manyun/hrm.gql.client.hrm';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
  BackendAnnualPerformanceObjectiveWay,
} from '@manyun/hrm.model.annual-performance-objective';
import type { AnnualPerformanceObjectiveEditParams } from '@manyun/hrm.route.hrm-routes';
import { createPerformanceAnnualObjective } from '@manyun/hrm.service.create-performance-annual-objective';
import { updatePerformanceAnnualObjective } from '@manyun/hrm.service.update-performance-annual-objective';
import type { SvcQuery as UpdateParams } from '@manyun/hrm.service.update-performance-annual-objective';
import { PerformancePositionCheckboxGroup } from '@manyun/hrm.ui.performance-position';
import { readSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { SpaceWarper } from './styles';

const formItem = {
  labelCol: {
    span: 2,
  },
  wrapperCol: {
    span: 22,
  },
};

type FormValues = {
  type: BackendAnnualPerformanceObjectiveType;
  evalPositions: string[];
  name: string;
  measurements: {
    context: string;
    gradeCriteria: {
      id?: number;
      kpiId?: number;
      context: string;
      scoreOptions: number[];
      celling?: number;
    }[];
  }[];
};

export function AnnualPerformanceObjectiveMutator() {
  const history = useHistory();
  const { search } = useLocation();
  const { type: defaultType } = getLocationSearchMap<{
    type?: string;
  }>(search, {
    parseNumbers: true,
  });
  const { way, id } = useParams<AnnualPerformanceObjectiveEditParams>();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [scoreOptions, setScoreOptions] = useState([
    1, 2, 3, 4, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50,
  ]);
  const customerScoreRef = useRef<number | null>(null);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const inputRef = useRef<any>(null);

  const client = useApolloClient();
  const [form] = Form.useForm<FormValues>();
  const type = Form.useWatch('type', form);

  const isCreate = useMemo(() => !id, [id]);
  const [fetch, { loading, data: detail }] = useLazyAnnualPerformanceObjective();
  const { data: space } = useSpaces({
    variables: {
      authorizedOnly: false,
      nodeTypes: ['IDC'],
    },
  });
  const locales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);

  useEffect(() => {
    if (id) {
      fetch({ variables: { id: Number(id) } });
    }
  }, [fetch, form, id]);

  const onSubmit = useCallback(async () => {
    form
      .validateFields()
      .then(async values => {
        const type = values.type;
        setSubmitLoading(true);
        const dismantleMeasurements: {
          context: string;
          gradeCriteria: string;
          defaultGrade: number[];
          id: number | undefined;
          kipId: number | undefined;
          ceiling: number | undefined;
        }[] = [];
        (values.measurements ?? []).forEach(measurement => {
          measurement.gradeCriteria.forEach(gradeCriteriaItem => {
            dismantleMeasurements.push({
              context: measurement.context,
              gradeCriteria: gradeCriteriaItem.context,
              //红线指标默认设置为0分
              defaultGrade:
                type === 'RED_LINE'
                  ? [0]
                  : gradeCriteriaItem.scoreOptions.map(score =>
                      type === 'DAILY' ? -score : score
                    ),
              id: gradeCriteriaItem.id,
              kipId: gradeCriteriaItem.kpiId,
              ceiling: gradeCriteriaItem.celling,
            });
          });
        });
        const _params = {
          ...values,
          measurements: dismantleMeasurements,
          subType: 'BIZ' as BackendAnnualPerformanceObjectiveSubType,
          way: (way === 'normal' ? 'STANDARD' : 'CUSTOM') as BackendAnnualPerformanceObjectiveWay,
          resources: (space?.spaces ?? []).map(space => space.value),
        };

        const { error, data } = id
          ? await updatePerformanceAnnualObjective({
              ..._params,
              id: Number(id),
            } as unknown as UpdateParams)
          : await createPerformanceAnnualObjective(_params);

        setSubmitLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        if (way === 'normal') {
          message.success(id ? '修改指标成功' : '新建指标成功');
          history.goBack();
        } else {
          message.success('提交审批成功');
          //定制指标 跳转至审批详情页
          if (data && Number(data) !== 0) {
            history.push(generateBPMRoutePath({ id: data }));
          } else {
            history.goBack();
          }
        }
      })
      .catch(err => {
        console.error(err);
        scrollToField(form!.scrollToField, err.errorFields);
      });
  }, [form, history, id, space?.spaces, way]);

  const generateEditDetail = useMemo(() => {
    if (detail?.annualPerformanceObjectiveById) {
      const record = detail?.annualPerformanceObjectiveById;
      const mergedMeasurements = groupby(record.measurements, 'context');
      let scoreList: number[] = [];
      const measurementsList = Object.keys(mergedMeasurements).map(mergedMeasurement => {
        return {
          context: mergedMeasurement,
          gradeCriteria: mergedMeasurements[mergedMeasurement].map(item => {
            const _itemScoreList: number[] = (item.defaultGrade ?? []).map(score =>
              Math.abs(score)
            );
            scoreList = [...scoreList, ..._itemScoreList];
            return {
              id: item.id,
              kpiId: item.kpiId,
              context: item.gradeCriteria,
              scoreOptions: _itemScoreList,
              celling: item.ceiling,
            };
          }),
        };
      });
      setScoreOptions(pre => [...pre, ...scoreList]);
      return { ...record, measurements: measurementsList };
    }
    return null;
  }, [detail?.annualPerformanceObjectiveById]);

  const isGradePlus = React.useMemo(() => {
    return ['YEAR', 'DAY_PLUS'].includes(type);
  }, [type]);

  return (
    <Card
      loading={loading}
      style={{
        height: 'calc(var(--content-height) - 48px)',
        overflowY: 'auto',
      }}
    >
      <Space style={{ width: '1340px' }} direction="vertical" size="middle">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          {isCreate ? '新建' : '修改'}指标
        </Typography.Title>
        {id && detail?.annualPerformanceObjectiveById?.type !== 'RED_LINE' && (
          <Alert
            type="warning"
            showIcon
            message={
              way === 'normal'
                ? '修改后不影响已下发的考核，如需应用在已下发的考核中，修改后请重新下发考核计划'
                : '修改后需重新审批，修改结果不影响已下发的考核，如需应用在已下发的考核中，修改后请重新下发考核计划'
            }
          />
        )}
        {!loading && (
          <Form
            form={form}
            {...formItem}
            initialValues={
              id
                ? generateEditDetail
                  ? {
                      ...generateEditDetail,
                      measurements: generateEditDetail.measurements,
                      resources:
                        way === 'custom'
                          ? transformSpaceLocationDefaultValue(
                              client,
                              (generateEditDetail.resources ?? []).map(
                                resource => resource?.value
                              ) as string[]
                            )
                          : undefined,
                      evalPositions: (generateEditDetail.positions ?? []).map(
                        position => position.value
                      ),
                    }
                  : undefined
                : {
                    type: defaultType ?? 'DAILY',
                    measurements: [
                      {
                        context: '',
                        gradeCriteria: [
                          {
                            context: '',
                            scoreOptions: [],
                            ceiling: undefined,
                          },
                        ],
                      },
                    ],
                  }
            }
          >
            {way === 'custom' && (
              <Form.Item
                label={locales.resources}
                name="resources"
                rules={[
                  {
                    validator: (_, val) => {
                      if (
                        !val ||
                        !val?.value ||
                        (val.value && Array.isArray(val.value) && val.value.length === 0)
                      ) {
                        return Promise.reject(`${locales.resources}必选`);
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                getValueFromEvent={(value, selectOptions) => {
                  let idcTags: string[] = [];
                  ((selectOptions ?? []) as SpaceModel[][]).forEach(nodes => {
                    const onlySelectedRegion =
                      nodes.length === 1 && nodes.every(node => node.type === 'REGION');
                    if (onlySelectedRegion) {
                      const _regionChildren = nodes.reduce((acc: string[], region) => {
                        acc = (region.children ?? []).map(idc => idc.value);
                        return acc;
                      }, []);

                      idcTags = [...idcTags, ..._regionChildren];
                    } else {
                      idcTags = [
                        ...idcTags,
                        ...nodes.filter(node => node.type === 'IDC').map(({ value }) => value),
                      ];
                    }
                  });

                  return {
                    value,
                    idcTags: uniq(idcTags),
                  };
                }}
                getValueProps={value => {
                  return { value: value?.value };
                }}
              >
                <LocationCascader
                  style={{ width: 334 }}
                  nodeTypes={['REGION', 'IDC']}
                  multiple
                  maxTagCount="responsive"
                />
              </Form.Item>
            )}
            <Form.Item
              label={locales.evalPositions}
              name="evalPositions"
              rules={[{ required: true, message: `${locales.evalPositions}必选` }]}
            >
              <PerformancePositionCheckboxGroup style={{ width: '60%' }} />
            </Form.Item>
            <Form.Item
              label={locales.type.__self}
              name="type"
              rules={[{ required: true, message: `${locales.type.__self}必选` }]}
            >
              <Radio.Group
                options={Object.keys(locales.type.textMapper.v2).map(key => ({
                  label: locales.type.textMapper.v2[key as BackendAnnualPerformanceObjectiveType],
                  value: key,
                }))}
              />
            </Form.Item>
            <Form.Item
              label={locales.name}
              name="name"
              rules={[
                { required: true, whitespace: true, message: `${locales.name}必选` },
                {
                  max: 30,
                  message: '最多输入 30 个字符！',
                },
              ]}
            >
              <Input style={{ width: 344 }} />
            </Form.Item>
            <Form.List
              name="measurements"
              rules={[
                {
                  validator: async (_, names) => {
                    if (!names || names.length < 1) {
                      return Promise.reject(new Error('至少添加一项'));
                    }
                    if (names) {
                      const nameCounts = new Map();
                      for (const obj of names) {
                        if (obj.context) {
                          const count = nameCounts.get(obj.context) || 0;
                          nameCounts.set(obj.context, count + 1);
                        }
                      }
                      for (const count of nameCounts.values()) {
                        if (count > 1) {
                          return Promise.reject(new Error('衡量标准请勿重复输入'));
                        }
                      }
                    }

                    return Promise.resolve();
                  },
                },
              ]}
            >
              {(fields, { add, remove }, { errors }) => (
                <>
                  <Space style={{ width: '100%' }} direction="vertical">
                    {fields.map((field, measurementIdx) => (
                      <SpaceWarper key={field.key}>
                        <Space style={{ width: '100%' }} align="center" size="middle">
                          <Container style={{ padding: '16px 0' }} color="default">
                            <Form.Item
                              {...field}
                              {...formItem}
                              label={locales.measurements}
                              name={[field.name, 'context']}
                              rules={[
                                {
                                  required: true,
                                  whitespace: true,
                                  message: `${locales.measurements}必填`,
                                },
                                {
                                  max: 200,
                                  message: '最多输入 200 个字符！',
                                },
                              ]}
                            >
                              <Input.TextArea
                                style={{ width: 766, resize: 'none' }}
                                placeholder={`请输入${locales.measurements}`}
                                autoSize
                              />
                            </Form.Item>
                            <Form.List
                              name={[field.name, 'gradeCriteria']}
                              rules={[
                                {
                                  validator: async (_, names) => {
                                    if (!names || names.length < 1) {
                                      return Promise.reject(new Error('至少添加一项'));
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              {(fields, { add, remove }, { errors }) => (
                                <>
                                  {fields.map(({ key, name, ...restField }) => (
                                    <Space key={key} style={{ width: '100%' }} align="start">
                                      <Form.Item
                                        {...restField}
                                        style={{ width: 876 }}
                                        labelCol={{ span: 3 }}
                                        wrapperCol={{ span: 21 }}
                                        label={locales.gradeCriteria}
                                        name={[name, 'context']}
                                        rules={[
                                          {
                                            required: true,
                                            whitespace: true,
                                            message: `${locales.gradeCriteria}必填`,
                                          },
                                          {
                                            max: 2000,
                                            message: '最多输入 2000 个字符！',
                                          },
                                        ]}
                                      >
                                        <Input.TextArea
                                          style={{ width: 766, resize: 'none' }}
                                          placeholder={`请输入${locales.gradeCriteria}`}
                                          autoSize
                                        />
                                      </Form.Item>
                                      {type !== 'RED_LINE' && (
                                        <Space size={0} align="start">
                                          <Button>{`单次${isGradePlus ? '加' : '减'}`}</Button>
                                          <Form.Item
                                            {...restField}
                                            name={[name, 'scoreOptions']}
                                            rules={[
                                              {
                                                required: true,
                                                message: `分值必填`,
                                              },
                                            ]}
                                          >
                                            <Select
                                              style={{ width: 170 }}
                                              mode="multiple"
                                              maxTagCount="responsive"
                                              options={uniq(scoreOptions).map(number => ({
                                                label: `${number}分`,
                                                value: number,
                                              }))}
                                              dropdownRender={menu => (
                                                <>
                                                  {menu}
                                                  <Divider style={{ margin: '8px 0' }} />
                                                  <Space style={{ padding: '0 8px 4px' }}>
                                                    <InputNumber
                                                      ref={inputRef}
                                                      style={{ width: 80 }}
                                                      status={
                                                        customerScoreRef.current &&
                                                        customerScoreRef.current > 100
                                                          ? 'error'
                                                          : undefined
                                                      }
                                                      placeholder="请输入"
                                                      min={1}
                                                      precision={2}
                                                      value={customerScoreRef.current}
                                                      onChange={value => {
                                                        if (value) {
                                                          customerScoreRef.current = Number(value);
                                                        }
                                                      }}
                                                    />
                                                    <Button
                                                      compact
                                                      type="text"
                                                      icon={<PlusOutlined />}
                                                      onClick={e => {
                                                        e.preventDefault();
                                                        if (customerScoreRef.current) {
                                                          if (customerScoreRef.current > 100) {
                                                            return;
                                                          }
                                                          setScoreOptions(prev =>
                                                            uniq([
                                                              ...prev,
                                                              customerScoreRef.current as number,
                                                            ])
                                                          );
                                                          customerScoreRef.current = null;
                                                          setTimeout(() => {
                                                            inputRef.current?.focus();
                                                          }, 0);
                                                        }
                                                      }}
                                                    >
                                                      添加
                                                    </Button>
                                                  </Space>
                                                </>
                                              )}
                                              getPopupContainer={trigger =>
                                                trigger.parentNode.parentNode
                                              }
                                            />
                                          </Form.Item>
                                        </Space>
                                      )}
                                      {isGradePlus && (
                                        <Form.Item {...restField} name={[name, 'celling']}>
                                          <InputNumber
                                            style={{ width: 140 }}
                                            addonBefore="封顶加"
                                            min={1}
                                            max={100}
                                            precision={0}
                                          />
                                        </Form.Item>
                                      )}

                                      {fields.length > 1 ? (
                                        <Form.Item>
                                          <MinusCircleOutlined onClick={() => remove(name)} />
                                        </Form.Item>
                                      ) : null}
                                    </Space>
                                  ))}

                                  <Space style={{ marginLeft: 90 }} direction="vertical" size={0}>
                                    <Button
                                      type="link"
                                      icon={<PlusOutlined />}
                                      onClick={() => add()}
                                    >
                                      添加评分标准
                                    </Button>
                                    <Form.ErrorList errors={errors} />
                                  </Space>
                                </>
                              )}
                            </Form.List>
                          </Container>
                          {fields.length > 1 ? (
                            <MinusCircleOutlined onClick={() => remove(field.name)} />
                          ) : null}
                        </Space>
                      </SpaceWarper>
                    ))}
                  </Space>
                  <Button
                    style={{ width: '100%', marginTop: 16 }}
                    type="dashed"
                    disabled={fields.length >= 10}
                    icon={<PlusOutlined />}
                    onClick={() => {
                      add({
                        context: '',
                        gradeCriteria: [
                          {
                            context: '',
                            scoreOptions: [],
                            ceiling: undefined,
                          },
                        ],
                      });
                    }}
                  >
                    添加
                  </Button>
                  <Form.ErrorList errors={errors} />
                </>
              )}
            </Form.List>
            {way === 'custom' && (
              <Form.Item
                label={locales.reason}
                name="reason"
                rules={[
                  { required: true, message: `${locales.reason}必填` },
                  {
                    max: 100,
                    message: '最多输入 100 个字符！',
                  },
                ]}
              >
                <Input style={{ width: 344 }} />
              </Form.Item>
            )}
          </Form>
        )}
      </Space>
      <FooterToolBar>
        <Space size="middle">
          <Button
            type="primary"
            loading={submitLoading}
            onClick={() => {
              onSubmit();
            }}
          >
            {way === 'custom' || detail?.annualPerformanceObjectiveById?.way === 'custom'
              ? '提交审批'
              : '提交'}
          </Button>
          <Button
            disabled={submitLoading}
            onClick={() => {
              history.goBack();
            }}
          >
            取消
          </Button>
        </Space>
      </FooterToolBar>
    </Card>
  );
}

const transformSpaceLocationDefaultValue = (client: ApolloClient<Object>, guidList: string[]) => {
  return {
    value: guidList.map(guid => {
      const space = readSpace(client, guid);
      if (space) {
        return [space.parentValue, space.value];
      }
      return [guid];
    }),
    idcTags: guidList,
  };
};
