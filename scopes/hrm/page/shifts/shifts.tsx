import React, { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';

import dayjs from 'dayjs';

import { Card } from '@manyun/base-ui.ui.card';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { UserLink } from '@manyun/auth-hub.ui.user';
import type { ShiftJSON } from '@manyun/hrm.model.shift';
import { getShiftLocales } from '@manyun/hrm.model.shift';
import { deleteShift } from '@manyun/hrm.service.delete-shift';
import { fetchPagedShifts } from '@manyun/hrm.service.fetch-paged-shifts';
import { whetherShiftIsBeUsed } from '@manyun/hrm.service.whether-shift-is-be-used';

import { ShiftMutator } from './components/shift-mutator';

export function Shifts() {
  const [state, setState] = useState<{
    loading: boolean;
    fields: {
      page: number;
      pageSize: number;
      name?: string;
    };
    data: ShiftJSON[];
    total: number;
  }>({
    loading: false,
    fields: {
      page: 1,
      pageSize: 10,
    },
    data: [],
    total: 0,
  });

  const locales = useMemo(() => getShiftLocales(), []);

  const loadData = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const { error, data } = await fetchPagedShifts({ ...state.fields });
    setState(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }
    setState(pre => ({ ...pre, data: data.data, total: data.total }));
  }, [state.fields]);

  const _deleteShift = useCallback(
    async (id: number) => {
      const {
        total,
        fields: { page, pageSize },
      } = state;
      const { error } = await deleteShift({ id });
      if (error) {
        message.error(error.message);
        return;
      }
      if (page > 1 && (page - 1) * pageSize + 1 === total) {
        setState(pre => ({ ...pre, fields: { ...pre.fields, page: pre.fields.page - 1 } }));
      } else {
        loadData();
      }
      message.success(locales.deleteText.success ?? '删除成功');
    },
    [loadData, locales.deleteText.success, state]
  );

  const onHandleDeleteShift = useCallback(
    async (id: number) => {
      const { error, data } = await whetherShiftIsBeUsed({ id });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data) {
        Modal.confirm({
          title: locales.deleteText.againConfirm ?? '当前班次已被排班使用，请谨慎删除！',
          onOk: () => {
            _deleteShift(id);
          },
        });
        return;
      }
      _deleteShift(id);
    },
    [_deleteShift, locales.deleteText.againConfirm]
  );

  useEffect(() => {
    loadData();
  }, [loadData]);

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <ShiftMutator
            onOk={() => {
              loadData();
            }}
          />
          <Input.Search
            placeholder={locales.searchFields.name.placeholder ?? '请输入班次名称'}
            allowClear
            onSearch={value => {
              setState(pre => ({ ...pre, fields: { page: 1, pageSize: 10, name: value?.trim() } }));
            }}
          />
        </Space>
        <Table
          rowKey="id"
          loading={state.loading}
          dataSource={state.data}
          columns={[
            {
              title: locales.name._self ?? '班次名称',
              dataIndex: 'name',
              render: (val, record) => {
                return (
                  <ShiftMutator
                    record={record}
                    onOk={() => {
                      loadData();
                    }}
                  >
                    {val}
                  </ShiftMutator>
                );
              },
            },
            {
              title: locales.timeRange ?? '班次时间',
              dataIndex: 'timeRang',
            },
            {
              title: locales.modifiedAt ?? '修改时间',
              dataIndex: 'gmtModified',
              render: val => dayjs(val).format('YYYY-MM-DD HH:mm:ss'),
            },
            {
              title: locales.modifyUser.name ?? '修改人',
              dataIndex: ['modifyUser', 'name'],
              render: (_, record) => (
                <UserLink id={record.modifyUser?.id} name={record.modifyUser?.name} />
              ),
            },
            {
              title: locales.action ?? '操作',
              dataIndex: ' action',
              render: (_, record) => {
                return (
                  <Space>
                    <ShiftMutator
                      record={record}
                      onOk={() => {
                        loadData();
                      }}
                    />
                    <Popconfirm
                      title={`${locales.deleteText.confirm} ${record.name}？`}
                      onConfirm={() => {
                        onHandleDeleteShift(record.id);
                      }}
                    >
                      <Typography.Link>{locales.deleteText._self}</Typography.Link>
                    </Popconfirm>
                  </Space>
                );
              },
            },
          ]}
          pagination={{
            total: state.total,
            current: state.fields.page,
            pageSize: state.fields.pageSize,
            onChange: (page, pageSize) => {
              setState(pre => ({ ...pre, fields: { ...pre.fields, page, pageSize } }));
            },
          }}
        />
      </Space>
    </Card>
  );
}
