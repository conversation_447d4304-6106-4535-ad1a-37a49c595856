import React, { useCallback, useMemo, useState } from 'react';

import PlusOutlined from '@ant-design/icons/es/icons/PlusOutlined';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { TimePicker } from '@manyun/base-ui.ui.time-picker';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { DEFAULT_CLOSED_VALUE, getShiftLocales } from '@manyun/hrm.model.shift';
import type { ShiftJSON } from '@manyun/hrm.model.shift';
import { createShift } from '@manyun/hrm.service.create-shift';
import { updateShift } from '@manyun/hrm.service.update-shift';

import { HourSelect } from './hour-select';
import { TimeCascader } from './time-cascader';
import { range } from './util';

export type ShiftMutatorProps = {
  record?: ShiftJSON;
  children?: React.ReactNode;
  onOk?: () => void;
};

const LATER_THAN_HOUR = 2;

/**编辑班次元素权限编码 */
const ELEMENT_CODE = 'element_edit-shift';

export function ShiftMutator({ record, children, onOk }: ShiftMutatorProps) {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);

  const locales = useMemo(() => getShiftLocales(), []);
  const [form] = Form.useForm();
  const [, { checkCode }] = useAuthorized();

  const onSubmit = useCallback(async () => {
    form.validateFields().then(async values => {
      setLoading(true);
      const params: ShiftJSON = { id: record?.id, ...values, dutySetting: { ...values } };
      const { error } = record ? await updateShift(params) : await createShift(params);
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      onOk?.();
      setVisible(false);
      message.success('操作成功');
    });
  }, [form, onOk, record]);

  const hasPermission = useMemo(() => checkCode(ELEMENT_CODE), [checkCode]);

  if (record && !children && !hasPermission) {
    return null;
  }

  return (
    <Space>
      <Button
        type={record ? (hasPermission ? 'link' : 'text') : 'primary'}
        compact={record !== undefined}
        onClick={() => {
          if (!hasPermission) {
            return;
          }
          setVisible(true);
          if (record) {
            form.setFieldsValue({
              name: record.name,
              offIsNextDay: record.offIsNextDay,
              onDutyTime: moment(record.onDutyTime, 'HH:mm'),
              offDutyTime: moment(record.offDutyTime, 'HH:mm'),
              ...record.dutySetting,
              punchOutStartTime: record.dutySetting?.punchOutStartTime
                ? moment(record.dutySetting?.punchOutStartTime, 'HH:mm')
                : undefined,
              punchOutEndTime: record.dutySetting?.punchOutEndTime
                ? moment(record.dutySetting?.punchOutEndTime, 'HH:mm')
                : undefined,
              punchInStartTime: record.dutySetting?.punchInStartTime
                ? moment(record.dutySetting?.punchInStartTime, 'HH:mm')
                : undefined,
              punchInEndTime: record.dutySetting?.punchInEndTime
                ? moment(record.dutySetting?.punchInEndTime, 'HH:mm')
                : undefined,
              restStartTime: record.dutySetting?.restStartTime
                ? moment(record.dutySetting?.restStartTime, 'HH:mm')
                : undefined,
              restEndTime: record.dutySetting?.restEndTime
                ? moment(record.dutySetting?.restEndTime, 'HH:mm')
                : undefined,
              leaveRule: record.dutySetting?.isNeedPunchIn || record.dutySetting?.isNeedPunchOut,
            });
          }
        }}
      >
        {children
          ? children
          : record
          ? locales.editText._self ?? '编辑'
          : locales.addText ?? '新建班次'}
      </Button>
      {record && <Divider type="vertical" />}

      <Modal
        width={960}
        bodyStyle={{
          maxHeight: 'calc(80vh - 109px)',
          overflowY: 'auto',
        }}
        title={record ? locales.editText.title : locales.addText}
        open={visible}
        destroyOnClose
        okButtonProps={{
          loading: loading,
        }}
        onCancel={() => setVisible(false)}
        onOk={() => {
          onSubmit();
        }}
      >
        <Form colon={false} form={form}>
          <Form.Item
            name="name"
            label={locales.name._self}
            rules={[
              { required: true, message: locales.name.validateMsg._self ?? '班次名称必填' },
              {
                type: 'string',
                max: 20,
                message: locales.name.validateMsg.maxLen ?? '最多输入 20 个字符！',
              },
            ]}
          >
            <Input style={{ width: '464px' }} allowClear />
          </Form.Item>
          <Typography.Title style={{ marginBottom: '16px' }} level={5}>
            {locales.basicSetting ?? '班次设置'}
          </Typography.Title>
          <Space size={16} style={{ width: '100%' }}>
            <Space style={{ width: 250 }}>
              <Form.Item
                label={locales.onDutyTime._self ?? '上班时间'}
                name="onDutyTime"
                rules={[{ required: true, message: locales.onDutyTime.validateMsg }]}
              >
                <TimePicker
                  style={{ width: 128 }}
                  showNow={false}
                  format="HH:mm"
                  allowClear={false}
                  getPopupContainer={trigger => trigger.parentNode?.parentNode as HTMLElement}
                  inputReadOnly
                  onChange={() => {
                    form.setFieldsValue({
                      offDutyTime: undefined,
                      offIsNextDay: false,
                      punchInStartTime: undefined,
                      punchInEndTime: undefined,
                      punchOutStartTime: undefined,
                      punchOutEndTime: undefined,
                      punchInStartTimeIsNextDay: undefined,
                      punchInEndTimeIsNextDay: undefined,
                      punchOutStartTimeIsNextDay: undefined,
                      punchOutEndTimeIsNextDay: undefined,
                      restStartTime: undefined,
                      restEndTime: undefined,
                    });
                  }}
                />
              </Form.Item>
            </Space>
            <Form.Item name="isNeedPunchIn" valuePropName="checked">
              <Checkbox
                onChange={e => {
                  form.setFieldsValue({
                    leaveRule:
                      form.getFieldValue('isNeedPunchIn') || form.getFieldValue('isNeedPunchOut'),
                  });
                }}
              >
                <Explanation iconType="question" tooltip={{ title: locales.isNeedPunchIn.toolTip }}>
                  {locales.isNeedPunchIn._self ?? '发起打卡'}
                </Explanation>
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isNeedPunchIn !== currentValues.isNeedPunchIn ||
                prevValues.onDutyTime !== currentValues.onDutyTime ||
                prevValues.offDutyTime !== currentValues.offDutyTime
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isNeedPunchIn') && (
                  <Space size={8}>
                    <Space size={4}>
                      <Form.Item
                        name="punchInStartTime"
                        label={locales.punchInTimeRang._self}
                        rules={[
                          { required: true, message: '上班打卡开始时间必选' },
                          {
                            validator: (_, value) => {
                              const onDutyTime = form.getFieldValue('onDutyTime');
                              const offDutyTime = form.getFieldValue('offDutyTime');
                              const offDutyMoment: moment.Moment = form.getFieldValue(
                                'offIsNextDay'
                              )
                                ? moment(offDutyTime).add(1, 'day')
                                : offDutyTime;

                              const valueMoment = form.getFieldValue('punchInStartTimeIsNextDay')
                                ? moment(value).add(1, 'day')
                                : value;

                              if (
                                value &&
                                onDutyTime &&
                                offDutyTime &&
                                (moment(valueMoment).diff(
                                  moment(onDutyTime).subtract(2, 'hour'),
                                  'minute'
                                ) < 0 ||
                                  moment(valueMoment).diff(offDutyMoment, 'minute') > 0)
                              ) {
                                return Promise.reject('上班前2小时至下班时间');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <TimePicker
                          style={{ width: 128 }}
                          format="HH:mm"
                          allowClear={false}
                          showNow={false}
                          getPopupContainer={trigger =>
                            trigger.parentNode?.parentNode as HTMLElement
                          }
                          disabledTime={() => {
                            const onDutyTime = getFieldValue('onDutyTime');
                            const offDutyTime = getFieldValue('offDutyTime');
                            const onDutyTimeHour = moment(onDutyTime).get('hour');
                            const offDutyTimeHour = moment(offDutyTime).get('hour');
                            const offIsNextDay = form.getFieldValue('offIsNextDay');

                            return {
                              disabledHours: () => {
                                if (onDutyTime && offDutyTime) {
                                  if (onDutyTimeHour < LATER_THAN_HOUR) {
                                    return range(offDutyTimeHour + 1, 24);
                                  } else {
                                    const limitHour = onDutyTimeHour - LATER_THAN_HOUR;
                                    return !offIsNextDay
                                      ? [...range(0, limitHour), ...range(offDutyTimeHour + 1, 24)]
                                      : [...range(offDutyTimeHour + 1, limitHour)];
                                  }
                                }
                                return [];
                              },
                              disabledMinutes: selectedHour => {
                                if (onDutyTime && offDutyTime) {
                                  const onDutyTimeMinute = moment(onDutyTime).get('minute');
                                  if (selectedHour === onDutyTimeHour - LATER_THAN_HOUR) {
                                    return range(0, onDutyTimeMinute);
                                  }
                                  if (selectedHour === offDutyTimeHour) {
                                    const offDutyTimeMinute = moment(offDutyTime).get('minute');
                                    return range(offDutyTimeMinute + 1, 60);
                                  }
                                }
                                return [];
                              },
                            };
                          }}
                          inputReadOnly
                          onChange={value => {
                            const onDutyTime = form.getFieldValue('onDutyTime');
                            const offDutyTime = form.getFieldValue('offDutyTime');
                            form.setFieldsValue({
                              punchInStartTimeIsNextDay:
                                onDutyTime &&
                                offDutyTime &&
                                value &&
                                moment(onDutyTime).diff(offDutyTime, 'minute') >= 0 &&
                                moment(value).diff(offDutyTime, 'minute') <= 0,
                              punchInEndTime: undefined,
                              punchInEndTimeIsNextDay: undefined,
                            });
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                          prevValues.punchInStartTimeIsNextDay !==
                          currentValues.punchInStartTimeIsNextDay
                        }
                      >
                        {({ getFieldValue }) =>
                          getFieldValue('punchInStartTimeIsNextDay') && (
                            <Form.Item name="punchInStartTimeIsNextDay">
                              {locales.offIsNextDay ?? '次日'}
                            </Form.Item>
                          )
                        }
                      </Form.Item>
                    </Space>
                    <Space size={4}>
                      <Form.Item
                        name="punchInEndTime"
                        label={locales.punchOutTimeRange.tip.divider}
                        rules={[
                          {
                            required: true,
                            message:
                              locales.punchInStartTime.validateMsg._self ?? '上班打卡结束时间必选',
                          },
                          {
                            validator: (_, value) => {
                              const punchInStartTime = form.getFieldValue('punchInStartTime');
                              const offDutyTime = form.getFieldValue('offDutyTime');
                              const offDutyMoment: moment.Moment = form.getFieldValue(
                                'offIsNextDay'
                              )
                                ? moment(offDutyTime).add(1, 'day')
                                : offDutyTime;

                              const valueMoment = form.getFieldValue('punchInEndTimeIsNextDay')
                                ? moment(value).add(1, 'day')
                                : value;
                              if (
                                value &&
                                punchInStartTime &&
                                offDutyTime &&
                                (moment(valueMoment).diff(moment(punchInStartTime), 'minute') < 0 ||
                                  moment(valueMoment).diff(offDutyMoment, 'minute') > 0)
                              ) {
                                return Promise.reject('打卡开始时间至下班时间');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <TimePicker
                          style={{ width: 128 }}
                          format="HH:mm"
                          allowClear={false}
                          showNow={false}
                          getPopupContainer={trigger =>
                            trigger.parentNode?.parentNode as HTMLElement
                          }
                          disabledTime={() => {
                            /**上班时间 */
                            const onDutyTime = getFieldValue('onDutyTime');
                            /**下班时间 */
                            const offDutyTime = getFieldValue('offDutyTime');
                            /**下班时间是否跨日 */
                            const offIsNextDay = getFieldValue('offIsNextDay');
                            const offDutyTimeHour = moment(offDutyTime).get('hour');

                            /**打卡开始时间 */
                            const punchInStartTime = getFieldValue('punchInStartTime');
                            const punchInStartTimeHour = moment(punchInStartTime).get('hour');
                            const punchInStartTimeIsNextDay = form.getFieldValue(
                              'punchInStartTimeIsNextDay'
                            );

                            return {
                              disabledHours: () => {
                                if (onDutyTime && offDutyTime && punchInStartTime) {
                                  if (offIsNextDay && !punchInStartTimeIsNextDay) {
                                    /**班次结束时间跨日 且上班打卡开始时间不跨日 */
                                    return range(offDutyTimeHour + 1, punchInStartTimeHour);
                                  }
                                  return [
                                    ...range(0, punchInStartTimeHour),
                                    ...range(offDutyTimeHour + 1, 24),
                                  ];
                                }
                                return [];
                              },
                              disabledMinutes: selectedHour => {
                                if (onDutyTime && offDutyTime && punchInStartTime) {
                                  if (selectedHour === punchInStartTimeHour) {
                                    const punchInStartTimeMinute =
                                      moment(punchInStartTime).get('minute');
                                    return range(0, punchInStartTimeMinute + 1);
                                  }
                                  if (selectedHour === offDutyTimeHour) {
                                    const offDutyTimeMinute = moment(offDutyTime).get('minute');
                                    return range(offDutyTimeMinute + 1, 60);
                                  }
                                  return [];
                                }
                                return [];
                              },
                            };
                          }}
                          inputReadOnly
                          onChange={value => {
                            const onDutyTime = form.getFieldValue('onDutyTime');
                            const offDutyTime = form.getFieldValue('offDutyTime');
                            form.setFieldsValue({
                              punchInEndTimeIsNextDay:
                                onDutyTime &&
                                offDutyTime &&
                                value &&
                                moment(onDutyTime).diff(offDutyTime, 'minute') >= 0 &&
                                moment(value).diff(offDutyTime, 'minute') <= 0,
                            });
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                          prevValues.punchInEndTimeIsNextDay !==
                          currentValues.punchInEndTimeIsNextDay
                        }
                      >
                        {({ getFieldValue }) =>
                          getFieldValue('punchInEndTimeIsNextDay') && (
                            <Form.Item name="punchInEndTimeIsNextDay">
                              {locales.offIsNextDay ?? '次日'}
                            </Form.Item>
                          )
                        }
                      </Form.Item>
                    </Space>
                  </Space>
                )
              }
            </Form.Item>
          </Space>

          <Space style={{ width: '100%' }} size={16}>
            <Space style={{ width: 250 }} size={8}>
              <Form.Item
                label={locales.offDutyTime._self ?? '下班时间'}
                name="offDutyTime"
                rules={[{ required: true, message: locales.offDutyTime.validateMsg }]}
              >
                <TimePicker
                  style={{ width: 128 }}
                  format="HH:mm"
                  showNow={false}
                  allowClear={false}
                  getPopupContainer={trigger => trigger.parentNode?.parentNode as HTMLElement}
                  inputReadOnly
                  onChange={value => {
                    const onDutyTime = form.getFieldValue('onDutyTime');
                    const offIsNextDay =
                      onDutyTime && value && moment(onDutyTime).diff(value, 'minute') >= 0;
                    const offDutyTime = offIsNextDay ? moment(value).add(1, 'day') : moment(value);
                    const diffHours = Number(
                      (moment(offDutyTime).diff(onDutyTime, 'minute') / 60).toFixed(2)
                    );
                    form.setFieldsValue({
                      offIsNextDay,
                      punchOutStartTime: undefined,
                      punchOutEndTime: undefined,
                      punchInStartTime: undefined,
                      punchInEndTime: undefined,
                      punchOutStartTimeIsNextDay: undefined,
                      punchOutEndTimeIsNextDay: undefined,
                      punchInStartTimeIsNextDay: undefined,
                      punchInEndTimeIsNextDay: undefined,
                      isAllowRest: onDutyTime && diffHours >= 9,
                      restStartTime: undefined,
                      restEndTime: undefined,
                    });
                  }}
                />
              </Form.Item>
              <Form.Item
                noStyle
                shouldUpdate={(prevValues, currentValues) =>
                  prevValues.offDutyTime !== currentValues.offDutyTime
                }
              >
                {({ getFieldValue }) =>
                  getFieldValue('offIsNextDay') && (
                    <Form.Item name="offIsNextDay">{locales.offIsNextDay ?? '次日'}</Form.Item>
                  )
                }
              </Form.Item>
            </Space>
            <Form.Item name="isNeedPunchOut" valuePropName="checked">
              <Checkbox
                onChange={() => {
                  form.setFieldsValue({
                    leaveRule:
                      form.getFieldValue('isNeedPunchIn') || form.getFieldValue('isNeedPunchOut'),
                  });
                }}
              >
                <Explanation
                  iconType="question"
                  tooltip={{ title: locales.isNeedPunchOut.toolTip }}
                >
                  {locales.isNeedPunchOut._self ?? '发起打卡'}
                </Explanation>
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isNeedPunchOut !== currentValues.isNeedPunchOut
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isNeedPunchOut') && (
                  <Space size={8}>
                    <Space size={4}>
                      <Form.Item
                        name="punchOutStartTime"
                        label={locales.punchInTimeRang._self}
                        rules={[
                          { required: true, message: '下班打卡开始时间必选' },
                          {
                            validator: (_, value) => {
                              const onDutyTime = form.getFieldValue('onDutyTime');
                              const offDutyTime = form.getFieldValue('offDutyTime');
                              const offDutyMoment: moment.Moment = form.getFieldValue(
                                'offIsNextDay'
                              )
                                ? moment(offDutyTime).add(1, 'day')
                                : offDutyTime;

                              const valueMoment = form.getFieldValue('punchOutStartTimeIsNextDay')
                                ? moment(value).add(1, 'day')
                                : value;
                              if (
                                value &&
                                onDutyTime &&
                                offDutyTime &&
                                (valueMoment.diff(moment(offDutyMoment).add(2, 'hour'), 'minute') >
                                  0 ||
                                  moment(valueMoment).diff(onDutyTime, 'minute') < 0)
                              ) {
                                return Promise.reject('上班时间至下班后2小时');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <TimePicker
                          style={{ width: 128 }}
                          getPopupContainer={trigger =>
                            trigger.parentNode?.parentNode as HTMLElement
                          }
                          format="HH:mm"
                          showNow={false}
                          allowClear={false}
                          disabledTime={() => {
                            /**上班时间 */
                            const onDutyTime = getFieldValue('onDutyTime');
                            /**下班时间 */
                            const offDutyTime = getFieldValue('offDutyTime');
                            /**下班时间是否跨日 */
                            const offIsNextDay = getFieldValue('offIsNextDay');
                            const onDutyTimeHour = moment(onDutyTime).get('hour');
                            const offDutyTimeHour = moment(offDutyTime).get('hour');

                            return {
                              disabledHours: () => {
                                if (onDutyTime && offDutyTime) {
                                  if (offDutyTimeHour + LATER_THAN_HOUR >= 24) {
                                    return range(0, onDutyTimeHour);
                                  } else {
                                    const limitHour = offDutyTimeHour + 2;
                                    return offIsNextDay
                                      ? range(limitHour + 1, onDutyTimeHour)
                                      : [...range(0, onDutyTimeHour), ...range(limitHour + 1, 24)];
                                  }
                                }
                                return [];
                              },
                              disabledMinutes: (selectedHour: number) => {
                                if (onDutyTime && offDutyTime) {
                                  if (selectedHour === onDutyTimeHour) {
                                    const onDutyTimeMinute = moment(onDutyTime).get('minute');
                                    return range(0, onDutyTimeMinute);
                                  }
                                  if (selectedHour === offDutyTimeHour + LATER_THAN_HOUR) {
                                    const offDutyTimeMinute = moment(offDutyTime).get('minute');

                                    return range(offDutyTimeMinute + 1, 60);
                                  }
                                  return [];
                                }
                                return [];
                              },
                            };
                          }}
                          inputReadOnly
                          onChange={value => {
                            const onDutyTime = form.getFieldValue('onDutyTime');
                            const offDutyTime = form.getFieldValue('offDutyTime');
                            form.setFieldsValue({
                              punchOutStartTimeIsNextDay:
                                onDutyTime &&
                                offDutyTime &&
                                value &&
                                moment(onDutyTime).diff(offDutyTime, 'minute') >= 0 &&
                                moment(value).diff(onDutyTime, 'minute') < 0,
                              punchOutEndTime: undefined,
                              punchOutEndTimeIsNextDay: undefined,
                            });
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                          prevValues.punchOutStartTimeIsNextDay !==
                          currentValues.punchOutStartTimeIsNextDay
                        }
                      >
                        {({ getFieldValue }) =>
                          getFieldValue('punchOutStartTimeIsNextDay') && (
                            <Form.Item name="punchOutStartTimeIsNextDay">
                              {locales.offIsNextDay ?? '次日'}
                            </Form.Item>
                          )
                        }
                      </Form.Item>
                    </Space>
                    <Space size={4}>
                      <Form.Item
                        name="punchOutEndTime"
                        label={locales.punchOutTimeRange.tip.divider}
                        rules={[{ required: true, message: '下班打卡结束时间必选' }]}
                      >
                        <TimePicker
                          style={{ width: 128 }}
                          format="HH:mm"
                          showNow={false}
                          allowClear={false}
                          getPopupContainer={trigger =>
                            trigger.parentNode?.parentNode as HTMLElement
                          }
                          disabledTime={() => {
                            const onDutyTime = getFieldValue('onDutyTime');
                            const offDutyTime = getFieldValue('offDutyTime');
                            const offDutyTimeHour = moment(offDutyTime).get('hour');
                            const offIsNextDay = getFieldValue('offIsNextDay');
                            /**下班打卡开始时间 */
                            const punchOutStartTime = getFieldValue('punchOutStartTime');
                            const punchOutStartTimeHour = moment(punchOutStartTime).get('hour');
                            const punchOutStartTimeIsNextDay = form.getFieldValue(
                              'punchOutStartTimeIsNextDay'
                            );
                            const limitHour = offDutyTimeHour + LATER_THAN_HOUR;

                            return {
                              disabledHours: () => {
                                if (onDutyTime && offDutyTime && punchOutStartTime) {
                                  if (offIsNextDay && !punchOutStartTimeIsNextDay) {
                                    return range(limitHour + 1, punchOutStartTimeHour);
                                  } else {
                                    return [
                                      ...range(0, punchOutStartTimeHour),
                                      ...range(limitHour + 1, 24),
                                    ];
                                  }
                                }
                                return [];
                              },
                              disabledMinutes: selectedHour => {
                                if (onDutyTime && offDutyTime && punchOutStartTime) {
                                  if (selectedHour === limitHour) {
                                    const offDutyTimeMinutes = moment(offDutyTime).get('minute');

                                    return range(offDutyTimeMinutes + 1, 60);
                                  }
                                  if (selectedHour === punchOutStartTimeHour) {
                                    const punchOutStartTimeMinute =
                                      moment(punchOutStartTime).get('minute');

                                    return range(0, punchOutStartTimeMinute + 1);
                                  }
                                }
                                return [];
                              },
                            };
                          }}
                          inputReadOnly
                          onChange={value => {
                            const onDutyTime = form.getFieldValue('onDutyTime');
                            const offDutyTime = form.getFieldValue('offDutyTime');

                            form.setFieldsValue({
                              punchOutEndTimeIsNextDay:
                                onDutyTime &&
                                offDutyTime &&
                                value &&
                                moment(onDutyTime).diff(offDutyTime, 'minute') >= 0 &&
                                moment(value).diff(onDutyTime, 'minute') < 0,
                            });
                          }}
                        />
                      </Form.Item>
                      <Form.Item
                        noStyle
                        shouldUpdate={(prevValues, currentValues) =>
                          prevValues.punchOutEndTimeIsNextDay !==
                          currentValues.punchOutEndTimeIsNextDay
                        }
                      >
                        {({ getFieldValue }) =>
                          getFieldValue('punchOutEndTimeIsNextDay') && (
                            <Form.Item name="punchOutEndTimeIsNextDay">
                              {locales.offIsNextDay ?? '次日'}
                            </Form.Item>
                          )
                        }
                      </Form.Item>
                    </Space>
                  </Space>
                )
              }
            </Form.Item>
          </Space>
          {/* 前端纯展示 */}
          <Form.Item name="leaveRule" valuePropName="checked">
            <Checkbox disabled>
              {locales.leaveRule ??
                '非整班请假时，遵循默认打卡时间范围：上班时间点的前2小时至后2小时，下班时间点的前2小时至后2小时'}
            </Checkbox>
          </Form.Item>

          <Space style={{ width: '100%' }} size={16}>
            <Form.Item name="isAllowRest" valuePropName="checked">
              <Checkbox>
                <Explanation
                  iconType="question"
                  tooltip={{ title: locales.restTimeRange.tip._self }}
                >
                  {locales.isAllowRest ?? '是否包含休息时段'}
                </Explanation>
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isAllowRest !== currentValues.isAllowRest
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isAllowRest') && (
                  <Space size={4}>
                    <Form.Item
                      name="restStartTime"
                      rules={[
                        {
                          required: true,
                          message: locales.restTimeRange.validateMsg ?? '请完整设置休息时间',
                        },
                        {
                          validator: (_, value) => {
                            const onDutyTime = form.getFieldValue('onDutyTime');
                            const offDutyTime = form.getFieldValue('offDutyTime');
                            const offDutyMoment: moment.Moment = form.getFieldValue('offIsNextDay')
                              ? moment(offDutyTime).add(1, 'day')
                              : offDutyTime;
                            const valueMoment = form.getFieldValue('restStartTimeIsNextDay')
                              ? moment(value).add(1, 'day')
                              : value;

                            if (value && onDutyTime && offDutyTime) {
                              if (moment(valueMoment).diff(onDutyTime, 'minute') < 0) {
                                return Promise.reject('不早于班次开始时间');
                              }
                              if (moment(offDutyMoment).diff(valueMoment, 'minute') < 0) {
                                return Promise.reject('不晚于班次结束时间');
                              }
                              return Promise.resolve();
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <TimePicker
                        style={{ width: 128 }}
                        format="HH:mm"
                        showNow={false}
                        allowClear={false}
                        disabledTime={() => {
                          const onDutyTime = form.getFieldValue('onDutyTime');
                          const offDutyTime = form.getFieldValue('offDutyTime');
                          const offDutyTimeHour = moment(offDutyTime).get('hour');
                          const onDutyTimeHour = moment(onDutyTime).get('hour');
                          /**下班时间是否跨日 */
                          const offIsNextDay = getFieldValue('offIsNextDay');
                          return {
                            disabledHours: () => {
                              if (onDutyTime && offDutyTime) {
                                if (offIsNextDay) {
                                  return range(offDutyTimeHour + 1, onDutyTimeHour);
                                } else {
                                  return [
                                    ...range(0, onDutyTimeHour),
                                    ...range(offDutyTimeHour + 1, 24),
                                  ];
                                }
                              }
                              return [];
                            },
                            disabledMinutes: selectedHour => {
                              if (onDutyTime && offDutyTime) {
                                if (selectedHour === onDutyTimeHour) {
                                  return range(0, moment(onDutyTime).get('minute'));
                                }
                                if (selectedHour === offDutyTimeHour) {
                                  return range(moment(offDutyTime).get('minute') + 1, 60);
                                }
                              }
                              return [];
                            },
                          };
                        }}
                        inputReadOnly
                        onChange={value => {
                          const onDutyTime = form.getFieldValue('onDutyTime');
                          const offIsNextDay = getFieldValue('offIsNextDay');
                          form.setFieldsValue({
                            restEndTime: false,
                            restEndTimeIsNextDay: undefined,
                            restStartTimeIsNextDay:
                              onDutyTime &&
                              offIsNextDay &&
                              value &&
                              moment(onDutyTime).diff(value, 'minute') > 0,
                          });
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.restStartTimeIsNextDay !== currentValues.restStartTimeIsNextDay
                      }
                    >
                      {({ getFieldValue }) =>
                        getFieldValue('restStartTimeIsNextDay') && (
                          <Form.Item name="restStartTimeIsNextDay">
                            {locales.offIsNextDay ?? '次日'}
                          </Form.Item>
                        )
                      }
                    </Form.Item>
                    <Form.Item
                      label={locales.restTimeRange.tip.divider ?? '至'}
                      name="restEndTime"
                      rules={[
                        {
                          required: true,
                          message: locales.restTimeRange.validateMsg ?? '请完整设置休息时间',
                        },
                        {
                          validator: (_, value) => {
                            const restStartTime = form.getFieldValue('restStartTime');
                            const restStartTimeMoment = form.getFieldValue('restStartTimeIsNextDay')
                              ? moment(restStartTime).add(1, 'day')
                              : restStartTime;
                            const offDutyTime = form.getFieldValue('offDutyTime');
                            const offDutyMoment: moment.Moment = form.getFieldValue('offIsNextDay')
                              ? moment(offDutyTime).add(1, 'day')
                              : offDutyTime;
                            const valueMoment = form.getFieldValue('restEndTimeIsNextDay')
                              ? moment(value).add(1, 'day')
                              : value;

                            if (value && restStartTime && offDutyTime) {
                              if (moment(valueMoment).diff(restStartTimeMoment, 'minute') < 0) {
                                return Promise.reject('不早于休息开始时间');
                              }
                              if (moment(offDutyMoment).diff(valueMoment, 'minute') < 0) {
                                return Promise.reject('不晚于班次结束时间');
                              }
                              return Promise.resolve();
                            }
                            return Promise.resolve();
                          },
                        },
                      ]}
                    >
                      <TimePicker
                        style={{ width: 128 }}
                        format="HH:mm"
                        showNow={false}
                        allowClear={false}
                        getPopupContainer={trigger => trigger.parentNode?.parentNode as HTMLElement}
                        disabledTime={() => {
                          const onDutyTime = form.getFieldValue('onDutyTime');
                          const offDutyTime = form.getFieldValue('offDutyTime');
                          const offDutyTimeHour = moment(offDutyTime).get('hour');
                          const restStartTime = getFieldValue('restStartTime');
                          const restStartTimeHour = moment(restStartTime).get('hour');
                          /**休息开始时间是否跨天 */
                          const restStartTimeIsNextDay = getFieldValue('restStartTimeIsNextDay');

                          return {
                            disabledHours: () => {
                              if (restStartTime && onDutyTime && offDutyTime) {
                                if (restStartTimeIsNextDay) {
                                  return [
                                    ...range(0, restStartTimeHour),
                                    ...range(offDutyTimeHour + 1, 24),
                                  ];
                                } else {
                                  if (offDutyTimeHour >= restStartTimeHour) {
                                    return [
                                      ...range(0, restStartTimeHour),
                                      ...range(offDutyTimeHour + 1, 24),
                                    ];
                                  }
                                  return [...range(offDutyTimeHour + 1, restStartTimeHour)];
                                }
                              }
                              return [];
                            },
                            disabledMinutes: selectedHour => {
                              if (restStartTime && onDutyTime && offDutyTime) {
                                if (selectedHour === offDutyTimeHour) {
                                  return range(moment(offDutyTime).get('minute') + 1, 60);
                                }
                                if (selectedHour === restStartTimeHour) {
                                  return range(0, moment(restStartTime).get('minute') + 1);
                                }
                              }
                              return [];
                            },
                          };
                        }}
                        inputReadOnly
                        onChange={value => {
                          const onDutyTime = form.getFieldValue('onDutyTime');
                          const offIsNextDay = getFieldValue('offIsNextDay');
                          form.setFieldsValue({
                            restEndTimeIsNextDay:
                              onDutyTime &&
                              offIsNextDay &&
                              value &&
                              moment(onDutyTime).diff(value, 'minute') > 0,
                          });
                        }}
                      />
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.restEndTimeIsNextDay !== currentValues.restEndTimeIsNextDay
                      }
                    >
                      {({ getFieldValue }) =>
                        getFieldValue('restEndTimeIsNextDay') && (
                          <Form.Item name="restEndTimeIsNextDay">
                            {locales.offIsNextDay ?? '次日'}
                          </Form.Item>
                        )
                      }
                    </Form.Item>
                  </Space>
                )
              }
            </Form.Item>
          </Space>

          {/* 不允许连续上班 */}
          <Space style={{ width: '100%' }} size={8}>
            <Form.Item name="notAllowContinuousWork" valuePropName="checked">
              <Checkbox
                onChange={e => {
                  const checked = e.target.checked;
                  form.setFieldsValue({ notAllowContinuousTime: checked ? 8 : undefined });
                }}
              >
                <Explanation
                  iconType="question"
                  tooltip={{ title: locales.notAllowContinuousWork.tip }}
                >
                  {locales.notAllowContinuousWork._self ?? '不允许连续上班'}
                </Explanation>
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.notAllowContinuousWork !== currentValues.notAllowContinuousWork
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('notAllowContinuousWork') && (
                  <Form.Item
                    name="notAllowContinuousTime"
                    label={locales.notAllowContinuousTime._self}
                    rules={[{ required: true, message: locales.notAllowContinuousTime.validteMsg }]}
                  >
                    <InputNumber
                      style={{ width: '120px' }}
                      min={1}
                      max={24}
                      precision={1}
                      addonAfter={locales.notAllowContinuousTime.unit}
                    />
                  </Form.Item>
                )
              }
            </Form.Item>
          </Space>

          {/* 弹性设置 */}
          <Typography.Title style={{ marginBottom: '16px' }} level={5}>
            {locales.flexibleSetting}
          </Typography.Title>

          <Space style={{ width: '100%' }} size={16}>
            <Form.Item name="isAllowEarlyToLeaveEarlyOrLateToLeaveLate" valuePropName="checked">
              <Checkbox
                onChange={e => {
                  const checked = e.target.checked;
                  const isAllowLateWorkOrEarlyLeaveAsNornal = form.getFieldValue(
                    'isAllowLateWorkOrEarlyLeaveAsNornal'
                  );
                  form.setFieldsValue({
                    maxLateWorkMinutes: checked ? [60] : undefined,
                    maxEarlyWorkMinutes: checked ? [60] : undefined,
                  });
                  if (isAllowLateWorkOrEarlyLeaveAsNornal && checked) {
                    Modal.confirm({
                      title: '确认开启',
                      content:
                        locales.isAllowEarlyToLeaveEarlyOrLateToLeaveLate.confirmText ??
                        '开启后，已设置的「晚到、早走几分钟不记为异常」将被取消，因为这两种弹性规则，只能选择一种.',
                      okText: locales.isAllowEarlyToLeaveEarlyOrLateToLeaveLate.confirmOk ?? '开启',
                      cancelText:
                        locales.isAllowEarlyToLeaveEarlyOrLateToLeaveLate.confirmCancle ?? '取消',
                      onOk: () => {
                        form.setFieldsValue({
                          isAllowLateWorkOrEarlyLeaveAsNornal: false,
                        });
                      },
                      onCancel: () => {
                        form.setFieldsValue({
                          isAllowEarlyToLeaveEarlyOrLateToLeaveLate: false,
                        });
                      },
                    });
                  }
                }}
              >
                {locales.isAllowEarlyToLeaveEarlyOrLateToLeaveLate._self ?? '允许晚到晚走/早到早走'}
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isAllowEarlyToLeaveEarlyOrLateToLeaveLate !==
                currentValues.isAllowEarlyToLeaveEarlyOrLateToLeaveLate
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isAllowEarlyToLeaveEarlyOrLateToLeaveLate') && (
                  <Space style={{ width: '100%' }} size={20}>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.maxEarlyWorkMinutes !== currentValues.maxEarlyWorkMinutes
                      }
                    >
                      {({ getFieldValue }) => (
                        <Form.Item
                          name="maxLateWorkMinutes"
                          label={locales.maxLateWorkMinutes._self ?? '上班最多晚到'}
                          tooltip={locales.maxLateWorkMinutes.tip}
                          rules={[
                            { required: true, message: `${locales.maxLateWorkMinutes._self}必选` },
                          ]}
                        >
                          <TimeCascader
                            disabledValues={
                              getFieldValue('maxEarlyWorkMinutes') &&
                              getFieldValue('maxEarlyWorkMinutes')[0] === DEFAULT_CLOSED_VALUE
                                ? [DEFAULT_CLOSED_VALUE]
                                : undefined
                            }
                          />
                        </Form.Item>
                      )}
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.maxLateWorkMinutes !== currentValues.maxLateWorkMinutes
                      }
                    >
                      {({ getFieldValue }) => (
                        <Form.Item
                          name="maxEarlyWorkMinutes"
                          label={locales.maxEarlyWorkMinutes._self}
                          tooltip={locales.maxEarlyWorkMinutes.tip}
                          rules={[
                            { required: true, message: `${locales.maxEarlyWorkMinutes._self}必选` },
                          ]}
                        >
                          <TimeCascader
                            disabledValues={
                              getFieldValue('maxLateWorkMinutes') &&
                              getFieldValue('maxLateWorkMinutes')[0] === DEFAULT_CLOSED_VALUE
                                ? [DEFAULT_CLOSED_VALUE]
                                : undefined
                            }
                          />
                        </Form.Item>
                      )}
                    </Form.Item>
                  </Space>
                )
              }
            </Form.Item>
          </Space>

          <Space style={{ width: '100%' }} size={16}>
            <Form.Item name="isAllowLateWorkOrEarlyLeaveAsNornal" valuePropName="checked">
              <Checkbox
                onChange={e => {
                  const checked = e.target.checked;
                  const isAllowEarlyToLeaveEarlyOrLateToLeaveLate = form.getFieldValue(
                    'isAllowEarlyToLeaveEarlyOrLateToLeaveLate'
                  );
                  form.setFieldsValue({
                    canLateWorkMinutes: checked ? [60] : undefined,
                    canEarlyWorkMinutes: checked ? [60] : undefined,
                  });
                  if (isAllowEarlyToLeaveEarlyOrLateToLeaveLate && checked) {
                    Modal.confirm({
                      title: '确认开启',
                      content:
                        locales.isAllowLateWorkOrEarlyLeaveAsNornal.confirmText ??
                        '开启后，已设置的「允许晚到晚走、早到早走」将被取消，因为这两种弹性规则，只能选择一种。',
                      okText: locales.isAllowLateWorkOrEarlyLeaveAsNornal.confirmOk ?? '开启',
                      cancelText:
                        locales.isAllowLateWorkOrEarlyLeaveAsNornal.confirmCancle ?? '取消',
                      onOk: () => {
                        form.setFieldsValue({
                          isAllowEarlyToLeaveEarlyOrLateToLeaveLate: false,
                        });
                      },
                      onCancel: () => {
                        form.setFieldsValue({
                          isAllowLateWorkOrEarlyLeaveAsNornal: false,
                        });
                      },
                    });
                  }
                }}
              >
                {locales.isAllowLateWorkOrEarlyLeaveAsNornal._self}
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isAllowLateWorkOrEarlyLeaveAsNornal !==
                currentValues.isAllowLateWorkOrEarlyLeaveAsNornal
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isAllowLateWorkOrEarlyLeaveAsNornal') && (
                  <Space style={{ width: '100%' }} size={20}>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.canEarlyWorkMinutes !== currentValues.canEarlyWorkMinutes
                      }
                    >
                      {({ getFieldValue }) => (
                        <Form.Item
                          name="canLateWorkMinutes"
                          label={locales.canLateWorkMinutes._self}
                          tooltip={locales.canLateWorkMinutes.tip}
                          rules={[
                            { required: true, message: `${locales.canLateWorkMinutes._self}必选` },
                          ]}
                        >
                          <TimeCascader
                            disabledValues={
                              getFieldValue('canEarlyWorkMinutes') &&
                              getFieldValue('canEarlyWorkMinutes')[0] === DEFAULT_CLOSED_VALUE
                                ? [DEFAULT_CLOSED_VALUE]
                                : undefined
                            }
                          />
                        </Form.Item>
                      )}
                    </Form.Item>
                    <Form.Item
                      noStyle
                      shouldUpdate={(prevValues, currentValues) =>
                        prevValues.canLateWorkMinutes !== currentValues.canLateWorkMinutes
                      }
                    >
                      {({ getFieldValue }) => (
                        <Form.Item
                          name="canEarlyWorkMinutes"
                          label={locales.canEarlyWorkMinutes._self}
                          tooltip={locales.canEarlyWorkMinutes.tip}
                          rules={[
                            { required: true, message: `${locales.canEarlyWorkMinutes._self}必选` },
                          ]}
                        >
                          <TimeCascader
                            disabledValues={
                              getFieldValue('canLateWorkMinutes') &&
                              getFieldValue('canLateWorkMinutes')[0] === DEFAULT_CLOSED_VALUE
                                ? [DEFAULT_CLOSED_VALUE]
                                : undefined
                            }
                          />
                        </Form.Item>
                      )}
                    </Form.Item>
                  </Space>
                )
              }
            </Form.Item>
          </Space>

          <Space direction="vertical">
            <Form.Item
              style={{ marginBottom: '6px' }}
              name="isAllowLateLeaveAndLateWork"
              valuePropName="checked"
            >
              <Checkbox
                onChange={e => {
                  const checked = e.target.checked;
                  if (checked) {
                    form.setFieldsValue({
                      lateLeaveAndLateWorkRules: [
                        {
                          lateLeaveHours: 3,
                          lateWorkHours: 1,
                        },
                      ],
                    });
                  } else {
                    form.setFieldsValue({ lateLeaveAndLateWorkRules: undefined });
                  }
                }}
              >
                {locales.isAllowLateLeaveAndLateWork}
              </Checkbox>
            </Form.Item>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, currentValues) =>
                prevValues.isAllowLateLeaveAndLateWork !== currentValues.isAllowLateLeaveAndLateWork
              }
            >
              {({ getFieldValue }) =>
                getFieldValue('isAllowLateLeaveAndLateWork') && (
                  <Form.List name="lateLeaveAndLateWorkRules">
                    {(fields, { add, remove }) => (
                      <>
                        {fields.map((field, index) => (
                          <Space
                            key={field.key}
                            style={{ marginBottom: 10, width: '100%', marginLeft: '16px' }}
                            align="center"
                          >
                            <Space>{`${locales.lateLeaveAndLateWorkRules._self ?? '规则'}${
                              index + 1
                            }`}</Space>
                            <Space>
                              {locales.lateLeaveAndLateWorkRules.lateLeaveHours ?? '当日下班后晚走'}
                              <Form.Item
                                style={{ marginBottom: '0' }}
                                name={[field.name, 'lateLeaveHours']}
                              >
                                <HourSelect />
                              </Form.Item>
                              {locales.lateLeaveAndLateWorkRules.hourText ?? '小时'},
                            </Space>
                            <Space>
                              {locales.lateLeaveAndLateWorkRules.lateWorkHours ??
                                '次日上班可以晚到'}
                              <Form.Item
                                style={{ marginBottom: '0' }}
                                name={[field.name, 'lateWorkHours']}
                              >
                                <HourSelect />
                              </Form.Item>
                              {locales.lateLeaveAndLateWorkRules.hourText ?? '小时'}
                            </Space>
                            {fields.length > 1 && (
                              <Button type="link" compact onClick={() => remove(field.name)}>
                                {locales.lateLeaveAndLateWorkRules.deleteText ?? '删除'}
                              </Button>
                            )}
                          </Space>
                        ))}
                        {fields.length < 3 && (
                          <Space style={{ marginLeft: '16px' }}>
                            <Form.Item>
                              <Button
                                type="link"
                                block
                                icon={<PlusOutlined />}
                                compact
                                onClick={() => {
                                  const preRules = form.getFieldValue('lateLeaveAndLateWorkRules');
                                  const defultValue: {
                                    lateLeaveHours: number;
                                    lateWorkHours: number;
                                  } = { lateLeaveHours: 3, lateWorkHours: 1 };
                                  if (preRules) {
                                    const lastRule = preRules[preRules.length - 1];
                                    if (lastRule) {
                                      if (
                                        lastRule?.lateLeaveHours === 10 ||
                                        lastRule?.lateWorkHours === 10
                                      ) {
                                        message.error(
                                          locales.lateLeaveAndLateWorkRules.limitMsg ??
                                            '已经设置了晚走晚到得最大时间，无需再新增时段'
                                        );
                                        return;
                                      }
                                      defultValue.lateLeaveHours = Number(
                                        ((lastRule?.lateLeaveHours ?? 0) + 0.5).toFixed(1)
                                      );
                                      defultValue.lateWorkHours = Number(
                                        ((lastRule?.lateWorkHours ?? 0) + 0.5).toFixed(1)
                                      );
                                    }
                                  }
                                  add(defultValue);
                                }}
                              >
                                {locales.lateLeaveAndLateWorkRules.addText ?? '添加晚到晚走'}
                              </Button>
                            </Form.Item>
                          </Space>
                        )}
                      </>
                    )}
                  </Form.List>
                )
              }
            </Form.Item>
          </Space>
        </Form>
      </Modal>
    </Space>
  );
}
