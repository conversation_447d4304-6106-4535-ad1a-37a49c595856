import React, { useMemo } from 'react';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps, CascaderRef } from '@manyun/base-ui.ui.cascader';

import { DEFAULT_CLOSED_VALUE } from '@manyun/hrm.model.shift';

export type Option = {
  label: string;
  value: number | string;
  disabled?: boolean;
  children?: Option[];
};

const MINUTES: number[] = [-1, 0, 60, 120, 180, 240, 300, 360, 420, 480, 540];

export type TimeCascaderProps = {
  disabledValues?: (number | string)[];
} & CascaderProps<Option>;

export const TimeCascader = React.forwardRef(
  ({ disabledValues, ...props }: TimeCascaderProps, ref: React.Ref<CascaderRef>) => {
    const options = useMemo(() => {
      const _options: Option[] = MINUTES.reduce((accArr: Option[], minute) => {
        const value: string | number = minute >= 0 ? minute : DEFAULT_CLOSED_VALUE;
        accArr.push({
          value,
          label: minute >= 0 ? `${Number(minute / 60).toFixed(0)}小时` : '关闭',
          disabled: disabledValues?.includes(value),
          children: minute >= 0 ? generateMinutes() : undefined,
        });
        return accArr;
      }, []);
      return _options;
    }, [disabledValues]);

    return (
      <Cascader
        ref={ref}
        style={{ width: 200 }}
        allowClear={false}
        changeOnSelect
        getPopupContainer={trigger => trigger.parentNode.parentNode}
        options={options}
        {...props}
      />
    );
  }
);

TimeCascader.displayName = 'TimeCascader';

function generateMinutes() {
  let minutes: Option[] = [];
  for (let i = 1; i <= 59; i++) {
    minutes.push({
      value: i,
      label: `${i}分钟`,
    });
  }
  return minutes;
}
