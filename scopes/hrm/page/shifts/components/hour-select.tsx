import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type HourSelectProps = Omit<SelectProps, 'options'>;

export const TIME_HALF_AN_HOUR_INTERVAL = [
  0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10,
];

export const HourSelect = React.forwardRef(
  (props: HourSelectProps, ref: React.Ref<RefSelectProps>) => {
    return (
      <Select
        ref={ref}
        style={{ width: 90 }}
        {...props}
        options={TIME_HALF_AN_HOUR_INTERVAL.map(hour => ({ label: hour, value: hour }))}
        getPopupContainer={trigger => trigger.parentNode.parentNode}
      />
    );
  }
);

HourSelect.displayName = 'HourSelect';
