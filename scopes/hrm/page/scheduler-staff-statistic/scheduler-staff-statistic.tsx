/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-29
 *
 * @packageDocumentation
 */
import React, { useCallback, useRef, useState } from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import type {
  QueryScheduleStaffStatisticArgs,
  ScheduleStaffInfos,
} from '@manyun/hrm.gql.client.hrm';
import { usePushToAttMachine, useScheduleStaffStatistic } from '@manyun/hrm.gql.client.hrm';
import { ShiftTeamSelect } from '@manyun/hrm.ui.shift-team-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import type { Space as SpaceNode } from '@manyun/resource-hub.ui.location-cascader';

type TableSorter = {
  order: 'ascend' | 'descend';
  field: string;
};

export function SchedulerStaffStatistic() {
  const queryFieldsRef = useRef<QueryScheduleStaffStatisticArgs>({
    page: 1,
    pageSize: 10,
  });
  const [form] = Form.useForm();
  const [selectedRows, setSelectedRows] = useState<ScheduleStaffInfos[]>([]);
  const { loading, data, refetch } = useScheduleStaffStatistic({
    variables: queryFieldsRef.current,
  });
  const [pushToAttMachine, { loading: pushLoading }] = usePushToAttMachine();
  const [authorized] = useAuthorized({ checkByCode: 'element_hrm-push-to-att-machine' });

  const onSearch = useCallback(() => {
    refetch(queryFieldsRef.current);
    setSelectedRows([]);
  }, [refetch]);

  const onExecPushToAttMachine = useCallback(
    async (params: ScheduleStaffInfos[]) => {
      const { data: result } = await pushToAttMachine({
        variables: {
          staffInfos: params.map(row => ({
            staffId: row.userId,
            blockGuid: row.blockGuid,
          })),
        },
      });
      if (result?.pushToAttMachine?.message) {
        message.error(result.pushToAttMachine.message);
        return;
      }
      message.success('已推送，建议3分钟后刷新页面，查看推送结果');
      refetch();
    },
    [pushToAttMachine, refetch]
  );

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Typography.Title level={5} showBadge>
          排班人员
        </Typography.Title>
        <Space style={{ width: '100%' }} align="center" size="middle">
          <Button
            type="primary"
            disabled={selectedRows.length === 0 || pushLoading}
            loading={pushLoading}
            onClick={() => {
              onExecPushToAttMachine(selectedRows);
            }}
          >
            批量推送至考勤机
          </Button>
          <Form form={form} layout="inline">
            <Form.Item
              name="location"
              getValueFromEvent={(value, selectOptions) => {
                const blockGuidList: string[] = [];
                selectOptions.forEach((option: SpaceNode[]) => {
                  if (option[1]) {
                    /**选中到楼栋 */
                    blockGuidList.push(option[1].value);
                  } else if (option[0]) {
                    /**到机房,取机房下的楼栋*/
                    if ((option[0].children ?? []).length === 0) {
                      //解决无楼栋机房参数为空的情况
                      blockGuidList.push(`${option[0].value}.${option[0].label}`);
                    } else {
                      (option[0].children ?? []).forEach(block => {
                        blockGuidList.push(block.value);
                      });
                    }
                  }
                });
                return { value, blockGuidList };
              }}
              getValueProps={value => {
                return { value: value?.value };
              }}
            >
              <LocationCascader
                style={{ width: 210 }}
                placeholder="位置"
                multiple
                maxTagCount="responsive"
                nodeTypes={['IDC', 'BLOCK']}
                authorizedOnly
              />
            </Form.Item>
            <Form.Item name="dutyGroupId">
              <ShiftTeamSelect
                style={{ width: 210 }}
                placeholder="班组"
                allowClear
                authorizedOnly
              />
            </Form.Item>
            <Form.Item name="userId">
              <UserSelect
                style={{ width: 210 }}
                labelInValue={false}
                allowClear
                userState="in-service"
              />
            </Form.Item>
            <Form.Item name="alreadyPush">
              <Select
                style={{ width: 210 }}
                placeholder="是否推送至考勤机"
                options={[
                  {
                    label: '是',
                    value: 'yes',
                  },
                  {
                    label: '否',
                    value: 'no',
                  },
                ]}
                allowClear
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    const values = form.getFieldsValue();
                    queryFieldsRef.current = {
                      page: 1,
                      pageSize: 10,
                      ...values,
                      blockGuidList: values?.location?.blockGuidList,
                    };
                    onSearch();
                  }}
                >
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    queryFieldsRef.current = {
                      page: 1,
                      pageSize: 10,
                      sortedFiled: queryFieldsRef.current.sortedFiled,
                      sortedType: queryFieldsRef.current.sortedType,
                    };
                    onSearch();
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Space>
        <Table
          scroll={{ x: 'max-content' }}
          rowKey="userId"
          loading={loading}
          dataSource={data?.scheduleStaffStatistic?.data ?? []}
          columns={[
            {
              title: '姓名',
              dataIndex: 'userId',
              render: val => <UserLink userId={val} native target="_blank" />,
              fixed: 'left',
            },
            {
              title: '考勤ID',
              dataIndex: 'userId',
            },
            {
              title: '类型',
              dataIndex: 'userType',
              render: val => <UserTypeText userType={val} />,
            },
            {
              title: '位置',
              dataIndex: 'blockGuid',
              sorter: true,
            },
            {
              title: '考勤组',
              dataIndex: 'attGroupName',
              sorter: true,
            },
            {
              title: '班组',
              dataIndex: 'dutyGroupName',
              sorter: true,
            },
            {
              title: '班制',
              dataIndex: 'shiftsName',
            },
            {
              title: '打卡方式',
              dataIndex: 'clockInTypes',
              render: (clockInTypes: string[]) => {
                return clockInTypes
                  .map(type => {
                    switch (type) {
                      case 'MACHINE':
                        return '考勤机打卡';
                      case 'MOBILE':
                        return 'GPS打卡';
                      case 'LOCAL':
                        return '系统登录打卡';
                      default:
                        return '--';
                    }
                  })
                  .join('|');
              },
            },
            {
              title: (
                <Explanation
                  style={{ color: 'inherit', fontSize: 'inherit' }}
                  size="large"
                  iconType="question"
                  tooltip={{ title: '人员信息已在当前班组对应的考勤机中' }}
                >
                  在考勤机中
                </Explanation>
              ),
              dataIndex: 'alreadyPush',
              render: (val, record) => {
                if (!val) {
                  return '--';
                }
                return (
                  <Space>
                    <Badge color={val === 'yes' ? 'green' : 'red'} />
                    {val === 'yes' ? '是' : '否'}
                  </Space>
                );
              },
            },
            {
              title: '操作',
              dataIndex: 'option',
              fixed: 'right',
              render: (_, record) => {
                return record.alreadyPush === 'no' && record.clockInTypes?.includes('MACHINE') ? (
                  <Button
                    type="link"
                    disabled={!authorized || pushLoading}
                    compact
                    onClick={() => {
                      onExecPushToAttMachine([record]);
                    }}
                  >
                    推送至考勤机
                  </Button>
                ) : (
                  '--'
                );
              },
            },
          ]}
          rowSelection={{
            selectedRowKeys: selectedRows.map(staff => staff.userId),
            getCheckboxProps: record => ({
              disabled: record.alreadyPush !== 'no' || !record.clockInTypes?.includes('MACHINE'),
            }),
            onChange: (_, selectedRows) => {
              setSelectedRows(selectedRows);
            },
          }}
          pagination={{
            total: data?.scheduleStaffStatistic?.total ?? 0,
            current: queryFieldsRef.current.page ?? 1,
            pageSize: queryFieldsRef.current.pageSize ?? 10,
            onChange: (current, size) => {
              queryFieldsRef.current.page = current;
              queryFieldsRef.current.pageSize = size;
              onSearch();
            },
          }}
          onChange={(__, _, sorter, { action }) => {
            if (action === 'sort') {
              queryFieldsRef.current.page = 1;
              queryFieldsRef.current.sortedFiled = (sorter as TableSorter)?.order
                ? (sorter as TableSorter)?.field
                : undefined;
              queryFieldsRef.current.sortedType = (sorter as TableSorter)?.order;
              onSearch();
            }
          }}
        />
      </Space>
    </Card>
  );
}
