import React, { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import type {
  AnnualPerformanceObjectiveJSON,
  BackendAnnualPerformanceObjectiveType,
  BackendAnnualPerformanceObjectiveWay,
} from '@manyun/hrm.model.annual-performance-objective';
import {
  generateAnnualPerformanceObjectiveCreate,
  generateAnnualPerformanceObjectiveEdit,
} from '@manyun/hrm.route.hrm-routes';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';

export type AnnualPerformanceObjectivesMutatorButtonProps = {
  way: BackendAnnualPerformanceObjectiveWay;
  objectiveType?: BackendAnnualPerformanceObjectiveType;
  record?: AnnualPerformanceObjectiveJSON;
} & Omit<ButtonProps, 'onClick'>;
export const AnnualPerformanceObjectivesMutatorButton = ({
  record,
  way,
  objectiveType,
  ...restProps
}: AnnualPerformanceObjectivesMutatorButtonProps) => {
  const history = useHistory();

  const [, { checkCode }] = useAuthorized();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);

  const isCreator = useMemo(() => !record, [record]);
  if (
    way === 'STANDARD'
      ? !checkCode('element_hrm-normal-annual-objective-operate')
      : record
        ? userId !== record.createdBy.id
        : !checkCode(
            'element_hrm-customer-annual-objective-mutator'
          ) /**定制指标 已通过状态且仅可修改本人创建的指标  元素权限控制创建定制指标 */
  ) {
    return null;
  }

  return (
    <Button
      type={isCreator ? 'primary' : 'link'}
      {...restProps}
      onClick={() => {
        history.push(
          record
            ? generateAnnualPerformanceObjectiveEdit({
                way: way === 'CUSTOM' ? 'custom' : 'normal',
                id: record.id.toString(),
              })
            : `${generateAnnualPerformanceObjectiveCreate({
                way: way === 'CUSTOM' ? 'custom' : 'normal',
              })}?type=${objectiveType}`
        );
      }}
    >
      {isCreator ? (objectiveType === 'RED_LINE' ? '新建红线指标' : `新建指标`) : '编辑'}
    </Button>
  );
};
