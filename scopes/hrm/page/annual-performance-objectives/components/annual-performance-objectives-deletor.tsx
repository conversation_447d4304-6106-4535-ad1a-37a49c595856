import React from 'react';
import { useSelector } from 'react-redux';

import { selectMe } from '@manyun/auth-hub.state.user';
import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import type { AnnualPerformanceObjectiveJSON } from '@manyun/hrm.model.annual-performance-objective';
import { deletePerformanceAnnualObjective } from '@manyun/hrm.service.delete-performance-annual-objective';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';

export type AnnualPerformanceObjectivesDeleteButtonProps = {
  record: AnnualPerformanceObjectiveJSON;
  onSuccess?: () => void;
} & Omit<ButtonProps, 'onClick'>;
export const AnnualPerformanceObjectivesDeleteButton = ({
  record,
  onSuccess,
  ...restProps
}: AnnualPerformanceObjectivesDeleteButtonProps) => {
  const [, { checkCode }] = useAuthorized();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);

  if (
    record.way === 'STANDARD'
      ? !checkCode('element_hrm-normal-annual-objective-operate')
      : userId !== record.createdBy.id /**定制指标 非审批中状态且可修改本人创建的 */
  ) {
    return null;
  }

  return (
    <Popconfirm
      key="delete"
      placement="rightBottom"
      title={
        record.type === 'RED_LINE' ? (
          '删除后不可恢复，是否确认删除？'
        ) : (
          <Space direction="vertical">
            <div>
              1. 删除后不影响已下发的考核，如需应用在已下发的考核中，
              <br />
              &nbsp;&nbsp;&nbsp; 删除后请重新下发考核计划。
            </div>
            <div>2. 删除后不可恢复，是否确认删除？</div>
          </Space>
        )
      }
      cancelText="我再想想"
      okText="确认删除"
      onConfirm={async () => {
        const { error } = await deletePerformanceAnnualObjective({ id: record.id });
        if (error) {
          message.error(error.message);
          return;
        }
        onSuccess?.();
      }}
    >
      <Button type="link" {...restProps}>
        删除
      </Button>
    </Popconfirm>
  );
};
