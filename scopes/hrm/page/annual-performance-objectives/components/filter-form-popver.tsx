import { FilterOutlined } from '@ant-design/icons';
import chunk from 'lodash.chunk';
import flatten from 'lodash.flatten';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormItemProps } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { ApprovalStatus, getBpmInstanceLocales } from '@manyun/bpm.model.bpm-instance';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import type { BackendAnnualPerformanceObjectiveWay } from '@manyun/hrm.model.annual-performance-objective';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import type { Fields } from '../annual-performance-objectives';

type SearchValues = Omit<Fields, 'name' | 'page' | 'pageSize'>;

export type FilterFormPopoverProps = {
  way: BackendAnnualPerformanceObjectiveWay;
  values: SearchValues;
  onSearch: (params: SearchValues) => void;
};

type FilterFormValue = Omit<
  SearchValues,
  'createTimeRange' | 'modifiedTimeRange' | 'resourceCodes'
> & {
  resourceCodes?: string[][];
  createTimeRange?: [Moment, Moment];
  modifiedTimeRange?: [Moment, Moment];
};

type ItemFieldName = keyof FilterFormValue;

type FormItem = {
  label: React.ReactNode;
  name: ItemFieldName;
  colSpan: number;
  children: JSX.Element;
} & FormItemProps;

export function FilterFormPopover({ way, values, onSearch }: FilterFormPopoverProps) {
  const [form] = Form.useForm<FilterFormValue>();
  const [visible, setVisible] = useState(false);
  const hasFilter = useDeepCompareMemo(() => {
    return (
      values &&
      Object.values(values).some(value =>
        Array.isArray(value) ? value.length !== 0 : value !== undefined
      )
    );
  }, [values]);

  const locales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);
  const statusOptions = useMemo(() => {
    const bpmLocales = getBpmInstanceLocales();
    return [
      {
        label: bpmLocales.approvalStatus.APPROVING,
        value: ApprovalStatus.Approving,
      },
      {
        label: bpmLocales.approvalStatus.PASS,
        value: ApprovalStatus.Pass,
      },
    ];
  }, []);

  const formItemProps = useMemo(() => {
    return [
      {
        label: locales.resources,
        name: 'resourceCodes',
        colSpan: 24,
        children: (
          <LocationCascader
            authorizedOnly
            showSearch
            maxTagCount="responsive"
            nodeTypes={['IDC']}
            multiple
            allowClear
            showArrow
          />
        ),
        hidden: way === 'STANDARD',
      },
      {
        label: locales.evalPositions,
        name: 'performancePosition',
        colSpan: 12,
        children: <PerformancePositionSelect allowClear showDelete />,
      },
      {
        label: locales.status,
        name: 'status',
        colSpan: 12,
        children: <Select allowClear options={statusOptions} />,
        hidden: way === 'STANDARD',
      },
      // {
      //   label: locales.type.__self,
      //   name: 'type',
      //   colSpan: 12,
      //   children: (
      //     <Select
      //       options={Object.keys(locales.type.enum).map(key => ({
      //         label: locales.type.enum[key as BackendAnnualPerformanceObjectiveType],
      //         value: key,
      //       }))}
      //       allowClear
      //     />
      //   ),
      // },
      {
        label: locales.createdBy.__self,
        name: 'createUserId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: locales.createdAt,
        name: 'createTimeRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
      {
        label: locales.modifiedAt,
        name: 'modifiedTimeRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
    ].filter(item => !item.hidden) as FormItem[];
  }, [
    locales.createdAt,
    locales.createdBy.__self,
    locales.evalPositions,
    locales.modifiedAt,
    locales.resources,
    locales.status,
    statusOptions,
    way,
  ]);

  const onHandleSearch = () => {
    const formValue = form.getFieldsValue();
    const _values: Partial<Fields> = {
      ...formValue,
      resourceCodes: formValue.resourceCodes ? flatten(formValue.resourceCodes) : undefined,
      createTimeRange:
        formValue.createTimeRange && formValue.createTimeRange.length === 2
          ? [
              formValue.createTimeRange[0].startOf('day').valueOf(),
              formValue.createTimeRange[1].endOf('day').valueOf(),
            ]
          : undefined,
      modifiedTimeRange:
        formValue.modifiedTimeRange && formValue.modifiedTimeRange.length === 2
          ? [
              formValue.modifiedTimeRange[0].startOf('day').valueOf(),
              formValue.modifiedTimeRange[1].endOf('day').valueOf(),
            ]
          : undefined,
    };
    onSearch(_values);
  };

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => (
        <Card style={{ width: 544 }}>
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              {formItemProps.map(itemProps => {
                const { label, name, colSpan, children, ...rest } = itemProps;
                return (
                  <Col key={`${name}`} span={colSpan}>
                    <Form.Item label={label} name={name} {...rest}>
                      {children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Row justify="end">
              <Space>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setVisible(false);
                    onHandleSearch();
                  }}
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    onHandleSearch();
                    setVisible(false);
                  }}
                >
                  搜索
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={_visible => {
        if (_visible === true && values) {
          form.setFieldsValue({
            ...values,
            resourceCodes: values.resourceCodes ? chunk(values.resourceCodes) : undefined,
            createTimeRange:
              values.createTimeRange && values.createTimeRange.length === 2
                ? [moment(values.createTimeRange[0]), moment(values.createTimeRange[1])]
                : undefined,
            modifiedTimeRange:
              values.modifiedTimeRange && values.modifiedTimeRange.length === 2
                ? [moment(values.modifiedTimeRange[0]), moment(values.modifiedTimeRange[1])]
                : undefined,
          });
        } else {
          onHandleSearch();
        }
        setVisible(_visible);
      }}
    >
      <Tooltip title="筛选">
        <Button
          size="small"
          icon={<FilterOutlined />}
          ghost={hasFilter}
          type={hasFilter ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}
