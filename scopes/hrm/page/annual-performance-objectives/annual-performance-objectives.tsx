import omit from 'lodash.omit';
import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useLocation } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { withdrawTodo } from '@manyun/bpm.service.withdraw-todo';
import type { ColumnType } from '@manyun/dc-brain.ui.edit-columns';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { useAnnualPerformanceObjectives } from '@manyun/hrm.gql.client.hrm';
import type {
  AnnualPerformanceObjectiveJSON,
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
  BackendAnnualPerformanceObjectiveWay,
  MergedMetricAnnualPerformanceObjectiveJSON,
} from '@manyun/hrm.model.annual-performance-objective';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import { ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH } from '@manyun/hrm.route.hrm-routes';
import { exportPerformanceAnnualObjectives } from '@manyun/hrm.service.export-performance-annual-objectives';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-annual-performance-objectives';
import type { ColumnDataIndex } from '@manyun/hrm.ui.annual-performance-objective-table';
import {
  AnnualPerformanceObjectiveTable,
  useAnnualPerformanceObjectiveTableColumns,
} from '@manyun/hrm.ui.annual-performance-objective-table';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';

import { AnnualPerformanceObjectivesDeleteButton } from './components/annual-performance-objectives-deletor';
import { AnnualPerformanceObjectivesMutatorButton } from './components/annual-performance-objectives-mutator';
import { FilterFormPopover } from './components/filter-form-popver';

export type Fields = SvcQuery;

const DEFAULT_SHOW_COLUMNS: ColumnDataIndex[] = [
  'name',
  'type',
  'measurements',
  'gradeCriteria',
  'positions',
];
export function AnnualPerformanceObjectives() {
  const { search } = useLocation();
  const history = useHistory();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const { page, pageSize, ...restDefaultFields } = getLocationSearchMap<Partial<SvcQuery>>(search, {
    parseNumbers: true,
    arrayKeys: ['resourceCodes'],
  });
  const [basicColumns] = useAnnualPerformanceObjectiveTableColumns(DEFAULT_SHOW_COLUMNS, ['index']);
  const [showColumns, setShowColumns] = useState<ColumnDataIndex[]>(DEFAULT_SHOW_COLUMNS);
  const [fields, setFields] = useState<Fields>({
    page: page ?? 1,
    pageSize: pageSize ?? 1000,
    type: 'DAILY',
    way: 'STANDARD',
    subType: 'BIZ',
    ...restDefaultFields,
  });

  const locales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);
  const { loading, data, refetch } = useAnnualPerformanceObjectives({
    variables: {
      query: {
        page: page ?? 1,
        pageSize: pageSize ?? 1000,
        way: 'STANDARD',
        type: 'DAILY',
        subType: 'BIZ',
        ...restDefaultFields,
      },
    },
    fetchPolicy: 'network-only',
  });

  const onSearch = useCallback(
    (params: Fields) => {
      setFields(params);
      setLocationSearch({ ...params });
      refetch({ query: params });
    },
    [refetch]
  );

  const operationColumn: ColumnType<MergedMetricAnnualPerformanceObjectiveJSON> =
    React.useMemo(() => {
      return {
        title: '操作',
        fixed: 'right',
        disabled: true,
        width: 110,
        dataIndex: '_action',
        render: (_, record) => {
          return (
            <Space size="middle">
              {record.status === 'PASS' && (
                <>
                  <AnnualPerformanceObjectivesMutatorButton
                    record={record}
                    way={record.way}
                    compact
                  />
                  <AnnualPerformanceObjectivesDeleteButton
                    record={record}
                    compact
                    onSuccess={() => {
                      const mergedValues = fields;
                      onSearch(mergedValues);
                    }}
                  />
                </>
              )}
              {record.status === 'APPROVING' && record.createdBy.id === userId && (
                <Popconfirm
                  title="撤回后需重新提交审批，是否确认撤回？"
                  placement="topRight"
                  okText="确认撤回"
                  cancelText="我再想想"
                  onConfirm={async () => {
                    const { error } = await withdrawTodo({
                      operator: record.createdBy.id.toString(),
                      instId: record.processInstanceCode,
                    });

                    if (error) {
                      message.error(error.message);
                      return;
                    }

                    message.success('操作成功');
                    const mergedValues = fields;
                    if (
                      fields.page !== 1 &&
                      (fields.page - 1) * fields.pageSize + 1 ===
                        data?.paginatedAnnualPerformanceObjectives.total
                    ) {
                      mergedValues.page = mergedValues.page - 1;
                    }
                    onSearch(mergedValues);
                  }}
                >
                  <Button compact type="link">
                    撤回
                  </Button>
                </Popconfirm>
              )}
            </Space>
          );
        },
        onCell: record => {
          return { rowSpan: record.rowSpan };
        },
      };
    }, [data?.paginatedAnnualPerformanceObjectives.total, fields, onSearch, userId]);

  return (
    <Card
      activeTabKey={fields.way}
      tabBarExtraContent={
        <Radio.Group
          value={fields.subType}
          onChange={e => {
            const mergedValues = { ...fields, page: 1, pageSize: 1000, subType: e.target.value };
            onSearch(mergedValues);
          }}
        >
          {Object.keys(locales.subType.enum).map(key => (
            <Radio.Button key={key} value={key}>
              {locales.subType.enum[key as BackendAnnualPerformanceObjectiveSubType]}
            </Radio.Button>
          ))}
        </Radio.Group>
      }
    >
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Space style={{ width: '100%', justifyContent: 'space-between' }} align="center">
          <Typography.Title level={5} showBadge>
            {locales.title}
          </Typography.Title>
          <Radio.Group
            size="middle"
            value={fields.type}
            options={Object.keys(locales.type.textMapper[PERFORMANCE_SECOND_VERSION]).map(key => ({
              label:
                locales.type.textMapper[PERFORMANCE_SECOND_VERSION][
                  key as BackendAnnualPerformanceObjectiveType
                ],
              value: key,
            }))}
            optionType="button"
            onChange={e => {
              onSearch({
                ...fields,
                type: e.target.value,
                page: 1,
                pageSize: 1000,
              });
            }}
          />
        </Space>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space size="middle">
            <AnnualPerformanceObjectivesMutatorButton
              objectiveType={fields.type}
              way={fields.way ?? 'STANDARD'}
            />
            {fields.type !== 'RED_LINE' && (
              <Button
                onClick={() => {
                  history.push(ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH);
                }}
              >
                预览目标
              </Button>
            )}

            <Input.Search
              style={{ width: 272 }}
              value={fields.name}
              allowClear
              placeholder="请输入指标名称"
              onChange={e => {
                onSearch({
                  ...fields,
                  name: e.target.value,
                  page: 1,
                  pageSize: 1000,
                });
              }}
            />
          </Space>
          <Space>
            <FilterFormPopover
              way={fields.way! as BackendAnnualPerformanceObjectiveWay}
              values={omit(fields, ['page', 'pageSize', 'name', 'subType', 'way', 'type'])}
              onSearch={values => {
                const mergedValues = {
                  ...fields,
                  ...values,
                  page: 1,
                  pageSize: 1000,
                };

                onSearch(mergedValues);
              }}
            />
            <FileExport
              text="导出"
              filename="指标库"
              showExportFiltered={false}
              data={async () => {
                const { error, data } = await exportPerformanceAnnualObjectives(fields);
                if (error) {
                  message.error(error.message);
                  return;
                }
                return data;
              }}
            />
            <EditColumns<AnnualPerformanceObjectiveJSON>
              listsHeight={360}
              uniqKey="HRM_PAGE_ANNUAL_PERFORMANCE_OBJECTIVES_TABLE_COLUMNS"
              defaultValue={[
                ...basicColumns.map(item => ({
                  ...item,
                  show: DEFAULT_SHOW_COLUMNS.includes(item.dataIndex as ColumnDataIndex),
                })),
                operationColumn,
              ]}
              onChange={function (value: ColumnType<AnnualPerformanceObjectiveJSON>[]): void {
                setShowColumns(
                  value
                    .filter(item => item.show !== false)
                    .map(cln => cln.dataIndex) as ColumnDataIndex[]
                );
              }}
            />
          </Space>
        </Space>
        <AnnualPerformanceObjectiveTable
          loading={loading}
          scroll={{ x: 'max-content' }}
          dataSource={
            data?.paginatedAnnualPerformanceObjectives
              ?.data as unknown as AnnualPerformanceObjectiveJSON[]
          }
          showColumns={[...showColumns, 'type']}
          operation={operationColumn}
          pagination={false}
          typeTextVersion="v2"
          maxWidth
        />
      </Space>
    </Card>
  );
}
