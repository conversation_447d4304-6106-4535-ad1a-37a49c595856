import React, { useCallback, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';

import dayjs from 'dayjs';
import moment from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import type { TagProps } from '@manyun/base-ui.ui.tag';
import { formatInterval } from '@manyun/base-ui.util.date-fns.format-interval';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { fetchPagedAttStatisticsByDay } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';
import type {
  DailyAttStatistics,
  SvcQuery as QueryDailyAttStatisticField,
  SortField,
} from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';
import {
  AttStatisticProvider,
  AttStatisticsCard,
  useAttStatistic,
} from '@manyun/hrm.ui.att-statistics-card';
import type { AttStatisticsKey } from '@manyun/hrm.ui.att-statistics-card';

import { CorrectDataModalView } from './components/correct-data-modal-view';
import { CorrectRecordsModalView } from './components/correct-records-modal-view';
import { QueryFilter } from './components/query-filter';
import type { DateValue, FilterFormValue } from './components/query-filter';
import { AttStatisticTableTools } from './components/table-tools';

type TableSorter = {
  order: 'ascend' | 'descend';
  field: SortField;
};

const generateBasicTableColumns = (onLoad: () => void): ColumnType<DailyAttStatistics>[] => {
  return [
    {
      title: '人员ID',
      dataIndex: 'staffId',
      fixed: 'left',
      disable: true,
    },
    {
      title: '姓名',
      fixed: 'left',
      dataIndex: 'staffName',
      disable: true,
    },
    {
      title: '位置',
      dataIndex: 'blockTag',
      disable: true,
      render: blockTag => {
        return <>{blockTag ? blockTag : '--'}</>;
      },
    },
    {
      title: '类型',
      dataIndex: 'staffType',
      render: val => {
        return <UserTypeText userType={val} />;
      },
    },
    {
      title: '日期',
      dataIndex: 'scheduleDate',
      disable: true,
      render: scheduleDate => dayjs(scheduleDate).format('YYYY-MM-DD'),
    },
    {
      title: '班组',
      dataIndex: 'dutyGroupName',
      disable: true,
      render: dutyGroupName => dutyGroupName ?? '--',
    },
    {
      title: '班次时间',
      disable: true,
      dataIndex: 'dutyName',
    },
    {
      title: '班次类型',
      dataIndex: 'bizType',
    },
    {
      title: '上班打卡',
      dataIndex: 'onDutyTime',
      render: (val, record) => {
        return (
          <Space size={4}>
            <div style={{ minWidth: 38 }}>{val ? dayjs(val).format('HH:mm') : ' --'}</div>
            {getClockInTag({
              type: 'on',
              value: val,
              isMissClockIn: record.isMissOnCard === 1,
              isLate: record.lateOnMinutes > 0,
            })}
          </Space>
        );
      },
      sorter: (a, b) => dayjs(a?.onDutyTime ?? 0).diff(b?.onDutyTime ?? 0),
    },
    {
      title: '下班打卡',
      dataIndex: 'offDutyTime',
      sorter: (a, b) => {
        return dayjs(a?.offDutyTime ?? 0).diff(b?.offDutyTime ?? 0);
      },
      render: (val, record) => {
        return (
          <Space size={4}>
            <div style={{ minWidth: 38 }}>{val ? dayjs(val).format('HH:mm') : ' --'}</div>
            {getClockInTag({
              type: 'off',
              value: val,
              isMissClockIn: record.isMissOffCard === 1,
              isLate: record.earlyOffMinutes > 0,
            })}
          </Space>
        );
      },
    },
    {
      title: '迟到时长',
      dataIndex: 'lateOnMinutes',
      show: false,
      render: lateOnMinutes =>
        lateOnMinutes >= 0 ? `${lateOnMinutes}分钟` : lateOnMinutes ?? '--',
      sorter: (a, b) => Number(a?.lateOnMinutes ?? 0) - Number(b?.lateOnMinutes ?? 0),
    },
    {
      title: '早退时长',
      dataIndex: 'earlyOffMinutes',
      show: false,
      render: earlyOffMinutes =>
        earlyOffMinutes >= 0 ? `${earlyOffMinutes}分钟` : earlyOffMinutes ?? '--',
      sorter: (a, b) => Number(a?.earlyOffMinutes ?? 0) - Number(b?.earlyOffMinutes ?? 0),
    },
    {
      title: '应出勤时长',
      dataIndex: 'standardWorkMinutes',
      render: standardWorkMinutes => {
        if (typeof standardWorkMinutes != 'number' || standardWorkMinutes === 0) {
          return '0 小时';
        }

        return formatInterval(standardWorkMinutes, 'minutes');
      },
    },
    {
      title: '班内工作时长',
      dataIndex: 'workTime',
      sorter: (a, b) => Number(a.workTime ?? 0) - Number(b.workTime ?? 0),
      render: workTime => {
        if (typeof workTime != 'number' || workTime === 0) {
          return '0 小时';
        }
        return formatInterval(workTime, 'minutes');
      },
    },
    {
      title: '外勤时长',
      dataIndex: 'outWorkMinutes',
      sorter: (a, b) => Number(a?.outWorkMinutes ?? 0) - Number(b?.outWorkMinutes ?? 0),
      render: outWorkMinutes => {
        if (typeof outWorkMinutes != 'number' || outWorkMinutes === 0) {
          return '0 小时';
        }
        return formatInterval(outWorkMinutes, 'minutes');
      },
    },
    {
      title: '请假类型',
      dataIndex: 'leaveType',
      render: leaveType => leaveType ?? '--',
    },
    {
      title: '操作',
      fixed: 'right',
      disable: true,
      dataIndex: 'operation',
      render: (_, record) => (
        <Space>
          <CorrectDataModalView
            record={record}
            onSuccess={() => {
              onLoad();
            }}
          />
          <CorrectRecordsModalView record={record} />
        </Space>
      ),
    },
  ];
};

export type QueryFields = Omit<FilterFormValue, 'staffId'> &
  Pick<QueryDailyAttStatisticField, 'staffId'> & {
    statisticFilterField: AttStatisticsKey | null;
  };

export function AttStatisticsByDay() {
  const { search } = useLocation();
  const { pageSize, page, statisticFilterField } = getLocationSearchMap<{
    page?: number;
    pageSize?: number;
    statisticFilterField?: AttStatisticsKey;
  }>(search, {
    parseNumbers: true,
  });
  const queryFieldsRef = useRef<QueryFields>({
    page: page ?? 1,
    pageSize: pageSize ?? 10,
    searchDate: 'today',
    location: {
      value: undefined,
    },
    statisticFilterField: statisticFilterField ?? 'attStandardTimes',
  });
  const [form] = Form.useForm<FilterFormValue>();
  const [tableLoading, setTableLoading] = useState(false);
  const [tableColumns, setTableColumns] = useState<ColumnType<DailyAttStatistics>[]>(
    generateBasicTableColumns(() => {
      onLoadData();
    })
  );
  const [data, setData] = useState<{ data: DailyAttStatistics[]; total: number }>({
    data: [],
    total: 0,
  });
  const [, { onLoadAttStatisticValue }] = useAttStatistic();

  const onLoadTableData = useCallback(async (params: QueryDailyAttStatisticField) => {
    setTableLoading(true);
    const { data, error } = await fetchPagedAttStatisticsByDay({
      ...params,
      needChangeRecord: true,
    });
    setTableLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(data);
  }, []);

  const onLoadData = useCallback(() => {
    setLocationSearch({
      ...queryFieldsRef.current,
      blockTags: queryFieldsRef.current.blockTags?.filter(
        guid => guid.split('.').length === 2 && guid.split('.')[0] !== guid.split('.')[1]
      ),
      location: undefined,
      customDateRange: undefined,
    });
    onLoadTableData(transformParams(queryFieldsRef.current));
    onLoadAttStatisticValue({
      ...queryFieldsRef.current,
      needAttendance:
        queryFieldsRef.current.statisticFilterField === 'attStandardTimes' ? true : undefined,
      type: 'DAILY',
    });
  }, [onLoadTableData, onLoadAttStatisticValue]);

  return (
    <Card bordered={false} title="每日统计">
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Space
          style={{ width: '100%', justifyContent: 'space-between', alignItems: 'flex-end' }}
          direction="horizontal"
          size={16}
        >
          <QueryFilter
            form={form}
            onSearch={resetPagination => {
              const filterFormValues = form.getFieldsValue();
              const timeRange = getTimeRange(
                filterFormValues.searchDate,
                filterFormValues.customDateRange
              );

              queryFieldsRef.current = {
                ...queryFieldsRef.current,
                ...filterFormValues,
                statisticFilterField: resetPagination
                  ? filterFormValues.needLeave === 'TRUE'
                    ? null
                    : 'attStandardTimes'
                  : queryFieldsRef.current.statisticFilterField,
                staffId: filterFormValues.staffId?.value,
                staffName: filterFormValues.staffId?.label,
                startDate: timeRange[0],
                endDate: timeRange[1],
                idcTags: undefined,
                blockTags: filterFormValues.location
                  ? [
                      ...(filterFormValues.location.idcTags ?? []).map(idc => `${idc}.${idc}`),
                      ...(filterFormValues.location.blockTags ?? []),
                    ]
                  : undefined,
                page: resetPagination ? 1 : queryFieldsRef.current.page,
              };
              onLoadData();
            }}
          />
          <AttStatisticTableTools
            searchFields={transformParams(queryFieldsRef.current)}
            defaultTableColumns={tableColumns}
            tableColumns={tableColumns}
            onColumnsChange={setTableColumns}
          />
        </Space>
        <AttStatisticsCard
          showColumns={[
            'attStandardTimes',
            'lateOnTimes',
            'earlyOffTimes',
            'missOnTimes',
            'missOffTimes',
            'absentTimes',
            'outWorkTimes',
          ]}
          searchValue={queryFieldsRef.current.statisticFilterField}
          onClick={value => {
            queryFieldsRef.current.page = 1;
            queryFieldsRef.current.pageSize = 10;
            queryFieldsRef.current.statisticFilterField = value;
            onLoadData();
          }}
        />
        <Table
          loading={tableLoading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          dataSource={data.data}
          columns={tableColumns}
          pagination={{
            total: data.total,
            current: queryFieldsRef.current.page,
            pageSize: queryFieldsRef.current.pageSize,
          }}
          onChange={(pagination, _, sorter, { action }) => {
            if (action === 'paginate') {
              queryFieldsRef.current.page = pagination.current!;
              queryFieldsRef.current.pageSize = pagination.pageSize!;
            } else if (action === 'sort') {
              queryFieldsRef.current.page = 1;
              queryFieldsRef.current.sortField = (sorter as TableSorter)?.order
                ? (sorter as TableSorter)?.field
                : undefined;
              queryFieldsRef.current.sortType = (sorter as TableSorter)?.order;
            }
            onLoadData();
          }}
        />
      </Space>
    </Card>
  );
}

export const AttStatisticsByDayPage = () => {
  return (
    <AttStatisticProvider>
      <AttStatisticsByDay />
    </AttStatisticProvider>
  );
};

const getTimeRange = (value: DateValue, customDateRang?: moment.Moment[]) => {
  let startDate: number = 0;
  let endDate: number = 0;
  switch (value) {
    case 'today':
      startDate = moment().startOf('day').valueOf();
      endDate = moment().endOf('day').valueOf();
      break;
    case 'yesterday':
      startDate = moment().subtract(1, 'days').startOf('day').valueOf();
      endDate = moment().subtract(1, 'days').endOf('day').valueOf();
      break;
    case 'thisWeek':
      startDate = moment().weekday(0).startOf('day').valueOf();
      endDate = moment().weekday(6).endOf('day').valueOf();
      break;
    case 'lastWeek':
      const weekday = new Date().getDay();
      startDate = moment()
        .subtract(weekday + 6, 'day')
        .startOf('day')
        .valueOf();
      endDate = moment().subtract(weekday, 'day').endOf('day').valueOf();
      break;
    case 'custom':
      startDate =
        customDateRang && customDateRang[0]
          ? customDateRang[0].startOf('day').valueOf() ?? 0
          : moment().startOf('day').valueOf();
      endDate =
        customDateRang && customDateRang[1]
          ? customDateRang[1].endOf('day').valueOf() ?? 0
          : moment().endOf('day').valueOf();
      break;
  }
  return [startDate, endDate];
};

type ClockInProps = {
  type: 'on' | 'off';
  value: string | null;
  isMissClockIn: boolean;
  isLate: boolean;
};
export const getClockInTypeValue = ({ type, value, isMissClockIn, isLate }: ClockInProps) => {
  let color: TagProps['color'] = 'default';
  let text: string = '正常';
  if (!isMissClockIn && !value) {
    //不缺卡且打卡时间为空 不显示tag
    return;
  }
  if (isMissClockIn) {
    color = 'error';
    text = '缺卡';
  } else if (isLate) {
    color = 'warning';
    text = type === 'on' ? '迟到' : '早退';
  }
  return { text, color };
};
export const getClockInTag = ({ type, value, isMissClockIn, isLate }: ClockInProps) => {
  const values = getClockInTypeValue({ type, value, isMissClockIn, isLate });

  if (!values) {
    return;
  }
  return <Tag color={values.color}>{values.text}</Tag>;
};

const transformParams = (fields: QueryFields) => {
  return {
    ...fields,
    isAbsent: fields.statisticFilterField === 'absentTimes' ? true : undefined,
    isEarlyOff: fields.statisticFilterField === 'earlyOffTimes' ? true : undefined,
    isLateOn: fields.statisticFilterField === 'lateOnTimes' ? true : undefined,
    isMisOff: fields.statisticFilterField === 'missOffTimes' ? true : undefined,
    isMisOn: fields.statisticFilterField === 'missOnTimes' ? true : undefined,
    isOutWork: fields.statisticFilterField === 'outWorkTimes' ? true : undefined,
    needAttendance: fields.statisticFilterField === 'attStandardTimes' ? true : undefined,
  } as QueryDailyAttStatisticField;
};
