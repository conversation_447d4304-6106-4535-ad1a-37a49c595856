import React, { useCallback, useMemo, useState } from 'react';

import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons';
import { Typography } from 'antd';
import dayjs from 'dayjs';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Container } from '@manyun/base-ui.ui.container';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Result } from '@manyun/base-ui.ui.result';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import { generateBreakOffBalanceRoutePath } from '@manyun/hrm.route.hrm-routes';
import { checkOvertimeIsRepeate } from '@manyun/hrm.service.check-overtime-is-repeate';
import { fetchOtHoursByMonth } from '@manyun/hrm.service.fetch-ot-hours-by-month';
import type { DailyAttStatistics } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';
import { AlterType } from '@manyun/hrm.service.fetch-shifts-for-leave-request';
import { updateAttStatisticData } from '@manyun/hrm.service.update-att-statistic-data';
import type { ApiArgs } from '@manyun/hrm.service.update-att-statistic-data';
import { whetherIsRestDay } from '@manyun/hrm.service.whether-is-rest-day';
import { ScheduleShiftByUserSelect } from '@manyun/hrm.ui.schedule-shift-by-user-select';
import { ShiftReplacementUserSelect } from '@manyun/hrm.ui.shift-replacement-user-select';

import { getClockInTag, getClockInTypeValue } from '../att-statistics-by-day';
import { CHANGE_TYPE_TEXT_MAPPER } from './constant';
import { CorrectRecord } from './correct-records-modal-view';
import { UserLeaveTypeSelect } from './user-leave-type-select';

export type CorrectDataModalViewProps = {
  record: DailyAttStatistics;
  onSuccess?: () => void;
};

export const CorrectDataModalView = ({ record, onSuccess }: CorrectDataModalViewProps) => {
  const [authorized] = useAuthorized({ checkByCode: 'element_att-data-correction' });
  const [open, setOpen] = useState(false);
  const [openSuccess, setOpenSuccess] = useState(false);
  const [loading, setLoading] = useState(false);
  const [hasChangeOverTime, setHasChangeOverTime] = useState(false);
  const [userTotalOvertimeByMonth, setUserTotalOvertimeByMonth] = useState(0);
  const [overtimeError, setOvertimeError] = useState<string | null>(null);
  const [overtimeIsOverDay, setOvertimeIsOverDay] = useState(false);

  const [form] = Form.useForm();
  const correctType = Form.useWatch('correctType', form);
  const balanceType = Form.useWatch('balanceType', form);
  const leaveType = Form.useWatch('leaveType', form);
  const leaveTimeRange = Form.useWatch('leaveTimeRange', form);
  const overTimeRange = Form.useWatch('overTimeRange', form);
  const fileList = Form.useWatch('attachments', form);

  const correctTypesOptions = useMemo(() => {
    if (record.bizType === '排班') {
      return [
        { value: 'CHECK_CHANGE' },
        {
          value: 'LEAVE',
        },
        {
          value: 'EXCHANGE',
          disabled: record.checkResultChangeRecordModels
            ?.map(item => item.changeType)
            .includes('LEAVE'), // 订正为请假后则不再支持换班
        },
        {
          value: 'REPLACE',
          hidden: record.staffType !== 'OUTSOURCE_STAFF', //仅外包人员支持顶班
        },
        {
          value: 'REST',
        },
      ];
    } else {
      return [
        { value: 'CHECK_CHANGE' },
        {
          value: 'OVERTIME_CHANGE',
        },
        {
          value: 'OVERTIME_DELETE',
        },
      ];
    }
  }, [record.bizType, record.checkResultChangeRecordModels, record.staffType]);

  /**
   * 上班考勤状态
   */
  const clockOnValues = useMemo(
    () =>
      getClockInTypeValue({
        type: 'on',
        value: record.onDutyTime,
        isMissClockIn: record.isMissOnCard === 1,
        isLate: record.lateOnMinutes > 0,
      }),
    [record.isMissOnCard, record.lateOnMinutes, record.onDutyTime]
  );
  /**
   * 下班考勤状态
   */
  const clockOffValues = useMemo(
    () =>
      getClockInTypeValue({
        type: 'off',
        value: record.offDutyTime,
        isMissClockIn: record.isMissOffCard === 1,
        isLate: record.earlyOffMinutes > 0,
      }),
    [record.earlyOffMinutes, record.isMissOffCard, record.offDutyTime]
  );

  const generateCorrectTypeExtra = useMemo(() => {
    if (correctType === 'EXCHANGE') {
      return '选择“换班”，则员工当前班次与替班人的班次对调';
    } else if (correctType === 'REPLACE') {
      return '选择“顶班”，则由顶班人进行当前排班的考勤';
    } else if (correctType === 'REST') {
      return '选择“休班”，提交后将删除员工该条排班记录，无需考勤';
    } else if (correctType === 'OVERTIME_DELETE') {
      return '选择“删除加班”，提交后将删除员工该条加班记录，无需考勤';
    }
    return;
  }, [correctType]);

  /**
   * 上班/下班打卡均异常
   */
  const isClockInAbnormal = useMemo(
    () =>
      clockOnValues &&
      clockOnValues?.color !== 'default' &&
      clockOffValues &&
      clockOffValues?.color !== 'default',
    [clockOffValues, clockOnValues]
  );

  /**
   * 扣除余额时需要计算请假时长
   */
  const calcLeaveTime = useMemo(() => {
    if (leaveType && leaveTimeRange && leaveTimeRange[0] && leaveTimeRange[1]) {
      /**
       * tips:
       * 1. 年假/病假 - 系统按照4小时=0.5天这样折算，不足0.5天按照0.5天计算
       * 2. 调休 - 调休按照小时计算，不足0.5小时按0.5小时计
       * 3. 如请假时段包含休息时间，需扣除后计算时长
       */
      const startTime = moment(leaveTimeRange[0]).startOf('minute');
      const endTime = moment(leaveTimeRange[1]).startOf('minute');
      //计算小时数，保留小数部分
      let diffHours = endTime.diff(startTime, 'hours', true);

      const dutyInfos = record.dutyProperties;
      if (dutyInfos.allowRest && dutyInfos.restRange) {
        const startTimeStr = `${
          dutyInfos.restRange.startIsNextDay
            ? dayjs(record.scheduleDate).add(1, 'day').format('YYYY-MM-DD')
            : dayjs(record.scheduleDate).format('YYYY-MM-DD')
        }  ${dutyInfos.restRange.startTime}`;
        const endTimeStr = `${
          dutyInfos.restRange.endIsNextDay
            ? dayjs(record.scheduleDate).add(1, 'day').format('YYYY-MM-DD')
            : dayjs(record.scheduleDate).format('YYYY-MM-DD')
        } ${dutyInfos.restRange.endTime}`;

        const restStartTime = dayjs(startTimeStr).valueOf();
        const restEndTime = dayjs(endTimeStr).valueOf();
        const scheduleRestMinute = dayjs(restEndTime).diff(dayjs(restStartTime), 'minute');
        if (startTime.valueOf() <= restStartTime && endTime.valueOf() >= restEndTime) {
          diffHours = diffHours - scheduleRestMinute / 60;
        } else if (restEndTime > startTime.valueOf() && startTime.valueOf() > restStartTime) {
          diffHours = endTime.diff(restEndTime, 'hours', true);
        } else if (restStartTime < endTime.valueOf() && endTime.valueOf() < restEndTime) {
          diffHours = moment(restStartTime).diff(startTime, 'hours', true);
        }
      }

      if (leaveType.value === 'BREAK_OFF') {
        return Math.ceil(diffHours * 2) / 2;
      }
      return Math.ceil(diffHours / 4) / 2;
    }
    return null;
  }, [leaveTimeRange, leaveType, record.dutyProperties, record.scheduleDate]);

  /**
   * 计算加班时长 不足0.5小时按0.5小时计
   */
  const calcOvertime = useMemo(() => {
    if (
      correctType === 'OVERTIME_CHANGE' &&
      overTimeRange &&
      overTimeRange[0] &&
      overTimeRange[1]
    ) {
      const startTime = moment(overTimeRange[0]).startOf('minute');
      const endTime = moment(overTimeRange[1]).startOf('minute');
      //计算小时数，保留小数部分
      const diffHours = endTime.diff(startTime, 'hours', true);
      return Math.ceil(diffHours * 2) / 2;
    }
    return null;
  }, [correctType, overTimeRange]);

  const hasChangeInOverTime = useMemo(() => {
    /**
     * 员工类型为标准工时 + 休息日(原加班时间或新的加班时间覆盖休息日)+ 加班变动时长发生变化
     */
    return (
      (record.userShift === 'SIMPLE_SHIFTS' &&
        correctType === 'OVERTIME_CHANGE' &&
        calcOvertime !== record.totalTime &&
        (record.restDay || overtimeIsOverDay)) ||
      (correctType === 'OVERTIME_DELETE' && record.restDay)
    );
  }, [
    calcOvertime,
    correctType,
    overtimeIsOverDay,
    record.restDay,
    record.totalTime,
    record.userShift,
  ]);

  const isQuarterUser = useMemo(() => {
    return record.userShift === 'QUARTER_SHIFTS';
  }, [record.userShift]);

  const validOvertimeError = useCallback(
    async (value: [moment.Moment, moment.Moment]) => {
      const startTime = moment(value[0]).startOf('minute');
      const endTime = moment(value[1]).startOf('minute');
      const { error } = await checkOvertimeIsRepeate({
        applyStaffId: Number(record.staffId),
        startTime: startTime.valueOf(),
        endTime: endTime.valueOf(),
        totalTime: Math.ceil(endTime.diff(startTime, 'hours', true) * 2) / 2,
        overtimeType: 'OVERTIME_CONFIRM',
        blockGuids: record.blockTag?.split('|'),
        bizId: record.bizId,
      });
      if (error) {
        setOvertimeError(error.message);
        return;
      }
      setOvertimeError(null);
    },
    [record.bizId, record.blockTag, record.staffId]
  );

  const validIsRestDay = useCallback(async (value: [moment.Moment, moment.Moment]) => {
    const { error, data } = await whetherIsRestDay({
      startTime: moment(value[0]).startOf('minute').valueOf(),
      endTime: moment(value[1]).startOf('minute').valueOf(),
    });
    if (error) {
      message.error(error.message);
      setOvertimeIsOverDay(false);
      return;
    }
    setOvertimeIsOverDay(data);
  }, []);

  const getUserTotalOverTime = useCallback(async () => {
    const { error, data } = await fetchOtHoursByMonth({
      apply: Number(record.staffId),
      startTime: dayjs(record.scheduleDate).startOf('month').valueOf(),
      endTime: dayjs(record.scheduleDate).endOf('month').valueOf(),
    });
    if (error) {
      message.error(error.message);
      setUserTotalOvertimeByMonth(0);
      return;
    }
    setUserTotalOvertimeByMonth(data);
  }, [record.scheduleDate, record.staffId]);

  const onSubmit = useCallback(
    async (alter: boolean) => {
      if (overtimeError) {
        return;
      }
      const values = form.getFieldsValue();
      const params: ApiArgs = {
        changeType: values.correctType,
        checkResultId: record.id,
        reason: values.reason,
        files: values.attachments?.map((attachment: McUploadFileJSON) =>
          McUploadFile.fromJSON(attachment).toApiObject()
        ),
      };
      switch (values.correctType) {
        case 'CHECK_CHANGE':
          //如都异常则透出子选项：上班卡正常、下班卡正常，如仅有一个卡异常默认提交
          const checkChangeType = values.clockType;
          params.checkResultParam = {
            checkChangeType: checkChangeType
              ? checkChangeType[1]
                ? 'ALL'
                : checkChangeType[0]
              : clockOnValues?.color !== 'default'
                ? 'ON'
                : 'OFF',
          };
          break;
        case 'LEAVE':
          params.checkResultParam = {
            reduceBalance: values.balanceType === 'deduct',
            leaveType: values.leaveType.value,
            startTime: moment(values.leaveTimeRange[0]).startOf('minute').valueOf(),
            endTime: moment(values.leaveTimeRange[1]).startOf('minute').valueOf(),
            totalTime: calcLeaveTime,
            unit: leaveType?.unit ?? 'DAY',
          };
          break;
        case 'EXCHANGE':
          params.checkResultParam = {
            changeInfo: {
              replaceStaffId: values.replaceStaffId,
              dutyId: values.dutyId.value,
              dutyName: values.dutyId.label,
              scheduleDate: moment(values.scheduleDate).startOf('day').valueOf(),
            },
          };
          break;
        case 'REPLACE':
          params.checkResultParam = {
            changeInfo: {
              replaceStaffId: values.replaceStaffId,
            },
          };
          break;
        case 'REST':
          break;
        case 'OVERTIME_CHANGE':
          params.checkResultParam = {
            startTime: moment(values.overTimeRange[0]).startOf('minute').valueOf(),
            endTime: moment(values.overTimeRange[1]).startOf('minute').valueOf(),
            totalTime: calcOvertime,
            unit: 'HOUR',
          };
          break;
        default:
          break;
      }
      setLoading(true);
      const { error } = await updateAttStatisticData(params);

      if (error) {
        message.error(error.message);
        setLoading(false);
        return;
      }
      if (alter) {
        message.success('订正考勤结果成功');
        onSuccess?.();
        setOpen(false);
      } else {
        setOpen(false);
        setHasChangeOverTime(hasChangeInOverTime);
        setOpenSuccess(true);
      }
      setLoading(false);
    },
    [
      calcLeaveTime,
      calcOvertime,
      clockOnValues?.color,
      form,
      hasChangeInOverTime,
      leaveType?.unit,
      onSuccess,
      overtimeError,
      record.id,
    ]
  );

  //只针对100天内存在的异常考勤结果的班次进行数据订正（异常：迟到、早退、缺卡）
  if (
    !record ||
    !authorized ||
    dayjs().startOf('day').diff(record.scheduleDate, 'day') > 100 ||
    (clockOnValues?.color === 'default' && clockOffValues?.color === 'default') ||
    !clockOnValues
  ) {
    return null;
  }

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          form.resetFields();
          setOpen(true);
          setHasChangeOverTime(false);
          setOvertimeError(null);
        }}
      >
        订正
      </Button>
      <Modal
        title="订正考勤结果"
        okText="提交"
        open={open}
        width={660}
        bodyStyle={{ maxHeight: 'calc(80vh - 55px)', overflowY: 'auto' }}
        destroyOnClose
        okButtonProps={{ loading: loading, disabled: loading }}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          form.validateFields().then(() => {
            if (hasChangeInOverTime) {
              Modal.confirm({
                width: 425,
                title: '休息日的加班时长存在变动，是否确认订正？',
                icon: <ExclamationCircleOutlined />,
                content: (
                  <Space size={0} wrap>
                    <div>
                      当前员工为标准工时制，休息日的加班时长已折算为调休，
                      <Typography.Text type="danger">
                        订正后将导致休息日加班时长变动，请务必同步调整调休余额，
                      </Typography.Text>
                      避免出现调休与实际加班不匹配的情况！
                    </div>
                    <a
                      target="_blank"
                      href={generateBreakOffBalanceRoutePath({ id: record.staffId.toString() })}
                      rel="noreferrer"
                    >
                      点此调整调休余额
                    </a>
                  </Space>
                ),
                okText: '确认订正',
                cancelText: '取消',
                okButtonProps: {
                  loading: loading,
                },
                onOk: () => {
                  onSubmit(false);
                },
              });
              return;
            }
            onSubmit(correctType !== 'OVERTIME_CHANGE');
          });
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <Container size="large" color="default">
            <Space style={{ width: '100%' }} direction="vertical">
              <User
                style={{ flexDirection: 'row' }}
                id={Number(record.staffId)}
                name={record.staffName}
              />
              <Space style={{ width: '100%' }} size={40}>
                <Space size={0} direction="vertical" align="baseline">
                  <Typography.Text type="secondary">应出勤班次</Typography.Text>
                  <Space size={4} align="baseline">
                    <Typography.Title level={5}>
                      {record.bizType === '排班' ? (
                        <>
                          {`${dayjs(record.scheduleDate).format('YYYY-MM-DD')} ${
                            record.startTime ? dayjs(record.startTime).format('HH:mm') : '--'
                          }-${
                            record.endTime
                              ? `${
                                  dayjs(record.startTime).startOf('day').valueOf() !==
                                  dayjs(record.endTime).startOf('day').valueOf()
                                    ? '次日'
                                    : ''
                                }${dayjs(record.endTime).format('HH:mm')}`
                              : '--'
                          }`}
                        </>
                      ) : (
                        `${dayjs(record.scheduleDate).format('YYYY-MM-DD')} ${record.dutyName}`
                      )}
                    </Typography.Title>
                    <Tag>{record.bizType}</Tag>
                  </Space>
                </Space>
                <Space size={0} direction="vertical" align="baseline">
                  <Typography.Text type="secondary">上班卡</Typography.Text>
                  <Space size={4} align="baseline">
                    <Typography.Title level={5}>
                      {record.onDutyTime ? dayjs(record.onDutyTime).format('HH:mm') : '--'}
                    </Typography.Title>
                    {getClockInTag({
                      type: 'on',
                      value: record.onDutyTime,
                      isMissClockIn: record.isMissOnCard === 1,
                      isLate: record.lateOnMinutes > 0,
                    })}
                  </Space>
                </Space>
                <Space size={0} direction="vertical" align="baseline">
                  <Typography.Text type="secondary">下班卡</Typography.Text>
                  <Space size={4} align="baseline">
                    <Typography.Title level={5}>
                      {record.offDutyTime ? dayjs(record.offDutyTime).format('HH:mm') : '--'}
                    </Typography.Title>
                    {getClockInTag({
                      type: 'off',
                      value: record.offDutyTime,
                      isMissClockIn: record.isMissOffCard === 1,
                      isLate: record.earlyOffMinutes > 0,
                    })}
                  </Space>
                </Space>
              </Space>
            </Space>
          </Container>
          {record.checkResultChangeRecordModels && (
            <Container size="large" color="default">
              <Space style={{ width: '100%' }} direction="vertical">
                <Typography.Title level={5}>订正记录</Typography.Title>
                {record.checkResultChangeRecordModels.map(item => (
                  <CorrectRecord key={item.checkResultId} record={item} />
                ))}
              </Space>
            </Container>
          )}
          <Form form={form} layout="horizontal" labelCol={{ span: 4 }}>
            <Form.Item
              name="correctType"
              label="订正方式"
              rules={[
                {
                  required: true,
                  message: '订正方式必选',
                },
              ]}
              extra={generateCorrectTypeExtra}
            >
              <Radio.Group
                options={correctTypesOptions
                  .filter(item => item.hidden !== true)
                  .map(item => ({ ...item, label: CHANGE_TYPE_TEXT_MAPPER[item.value] }))}
                onChange={e => {
                  const value = e.target.value;
                  //清空换班班次信息
                  form.setFieldsValue({
                    replaceStaffId: undefined,
                    scheduleDate: undefined,
                    dutyId: undefined,
                  });
                  if (value === 'OVERTIME_CHANGE') {
                    if (record.startTime && record.endTime) {
                      form.setFieldValue('overTimeRange', [
                        moment(record.startTime),
                        moment(record.endTime),
                      ]);
                    }
                    //获取季度工时用户当月累计加班
                    if (isQuarterUser) {
                      getUserTotalOverTime();
                    }
                  }
                }}
              />
            </Form.Item>
            {correctType === 'CHECK_CHANGE' && isClockInAbnormal && (
              <Form.Item
                wrapperCol={{ offset: 4 }}
                rules={[
                  {
                    required: true,
                    message: '订正方式子项必选',
                  },
                ]}
                name="clockType"
                valuePropName="checked"
              >
                <Checkbox.Group
                  options={[
                    {
                      label: '上班卡正常',
                      value: 'ON',
                    },
                    {
                      label: '下班卡正常',
                      value: 'OFF',
                    },
                  ]}
                />
              </Form.Item>
            )}
            {correctType === 'LEAVE' && (
              <>
                <Form.Item
                  name="balanceType"
                  label="假期余额"
                  rules={[
                    {
                      required: true,
                      message: '假期余额必选',
                    },
                  ]}
                >
                  <Radio.Group
                    options={[
                      {
                        label: '提交后自动扣除余额',
                        value: 'deduct',
                      },
                      {
                        label: '不扣除余额',
                        value: 'noDeduct',
                      },
                    ]}
                    onChange={() => {
                      form.setFieldsValue({ leaveType: undefined });
                    }}
                  />
                </Form.Item>
                <Form.Item
                  noStyle
                  shouldUpdate={(pre, next) => pre.balanceType !== next.balanceType}
                >
                  {({ getFieldValue }) => {
                    const balanceType = getFieldValue('balanceType');
                    return (
                      <Form.Item
                        name="leaveType"
                        label="请假类型"
                        rules={[
                          {
                            required: true,
                            message: '请假类型必选',
                          },
                        ]}
                      >
                        <UserLeaveTypeSelect
                          style={{ width: 228 }}
                          userId={Number(record.staffId)}
                          showBalance={balanceType === 'deduct'}
                        />
                      </Form.Item>
                    );
                  }}
                </Form.Item>
                <Form.Item
                  name="leaveTimeRange"
                  label="请假时间"
                  dependencies={['leaveType', 'balanceType']}
                  rules={[
                    {
                      validator: (_, rang) => {
                        if (!rang) {
                          return Promise.reject('请假时间必填');
                        }
                        if (
                          rang &&
                          moment(rang[0]).startOf('minute').valueOf() !== record.startTime &&
                          moment(rang[1]).startOf('minute').valueOf() !== record.endTime
                        ) {
                          return Promise.reject(
                            '请修改请假时间，至少包含班次的上班时间点或下班时间点'
                          );
                        } else if (
                          moment(rang[0]).startOf('minute').valueOf() < record.startTime ||
                          moment(rang[1]).startOf('minute').valueOf() > record.endTime
                        ) {
                          return Promise.reject('请修改请假时间，请假时间不能超出班次时间');
                        } else if (
                          rang &&
                          moment(rang[0]).startOf('minute').valueOf() ===
                            moment(rang[1]).startOf('minute').valueOf()
                        ) {
                          return Promise.reject('开始时间不能等于结束时间');
                        } else if (balanceType === 'deduct' && calcLeaveTime !== null) {
                          if (calcLeaveTime === 0) {
                            return Promise.reject('请假时长不能为0');
                          } else if (leaveType.balance < calcLeaveTime) {
                            return Promise.reject(
                              '请假时长不可超过所选假期的可用余额，请修改请假时间'
                            );
                          }
                        }

                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <DatePicker.RangePicker
                    style={{ width: '100%' }}
                    showTime={{ format: 'HH:mm' }}
                    defaultPickerValue={
                      record.startTime && record.endTime
                        ? [
                            moment(record.startTime).startOf('day'),
                            moment(record.endTime).endOf('day'),
                          ]
                        : undefined
                    }
                    format="YYYY-MM-DD HH:mm"
                    disabledDate={current => {
                      return (
                        current &&
                        (current < moment(record.startTime).startOf('day') ||
                          current > moment(record.endTime).endOf('day'))
                      );
                    }}
                    disabledTime={current => {
                      if (record.startTime && record.endTime && current) {
                        const startMoment = moment(record.startTime);
                        const endMoment = moment(record.endTime);
                        if (
                          current.isSame(startMoment, 'day') &&
                          current.isSame(endMoment, 'day')
                        ) {
                          const disabledHours = [
                            ...Array.from({ length: startMoment.hours() }, (_, i) => i),
                            ...Array.from(
                              { length: 24 - endMoment.hours() - 1 },
                              (_, i) => i + endMoment.hours() + 1
                            ),
                          ];
                          return {
                            disabledHours: () => disabledHours,
                            disabledMinutes: hour =>
                              hour === startMoment.hours()
                                ? Array.from({ length: startMoment.minutes() }, (_, i) => i)
                                : hour === endMoment.hours()
                                  ? Array.from(
                                      { length: 60 - endMoment.minutes() },
                                      (_, i) => i + endMoment.minutes() + 1
                                    )
                                  : [],
                          };
                        } else {
                          //班次跨天
                          if (current.isSame(startMoment, 'day')) {
                            return {
                              disabledHours: () =>
                                Array.from({ length: startMoment.hours() }, (_, i) => i),
                              disabledMinutes: hour =>
                                hour === startMoment.hours()
                                  ? Array.from({ length: startMoment.minutes() }, (_, i) => i)
                                  : [],
                            };
                          } else {
                            return {
                              disabledHours: () =>
                                Array.from(
                                  { length: 24 - endMoment.hours() - 1 },
                                  (_, i) => i + endMoment.hours() + 1
                                ),
                              disabledMinutes: hour =>
                                hour === endMoment.hours()
                                  ? Array.from(
                                      { length: 60 - endMoment.minutes() },
                                      (_, i) => i + endMoment.minutes() + 1
                                    )
                                  : [],
                            };
                          }
                        }
                      } else {
                        return {
                          disabledHours: () => Array.from({ length: 24 }, (_, i) => i),
                          disabledMinutes: () => Array.from({ length: 60 }, (_, i) => i),
                        };
                      }
                    }}
                  />
                </Form.Item>
                {balanceType === 'deduct' && (
                  <Form.Item name="leaveTimeHour" label="请假时长">
                    <Typography.Text>
                      {calcLeaveTime ? `${calcLeaveTime}${leaveType?.unitName}` : '--'}
                    </Typography.Text>
                  </Form.Item>
                )}
              </>
            )}
            {correctType === 'EXCHANGE' && (
              <Form.Item label="还班班次" required>
                <Space>
                  <Form.Item
                    name="replaceStaffId"
                    rules={[
                      {
                        required: true,
                        message: '替班人必选',
                      },
                    ]}
                  >
                    <ShiftReplacementUserSelect
                      style={{ width: 96 }}
                      placeholder="替班人"
                      params={{
                        type: 'EXCHANGE',
                        shiftId: record.dutyId,
                        shiftDate: record.scheduleDate,
                        staffId: Number(record.staffId),
                      }}
                      trigger="onFocus"
                      onChange={() => {
                        form.setFieldsValue({
                          scheduleDate: undefined,
                          dutyId: undefined,
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    name="scheduleDate"
                    rules={[
                      {
                        required: true,
                        message: '时间必选',
                      },
                    ]}
                  >
                    <DatePicker
                      style={{ width: 128 }}
                      format="YYYY-MM-DD"
                      disabledDate={current => {
                        return current && current < moment().subtract(100, 'days').startOf('day');
                      }}
                      onChange={() => {
                        form.setFieldsValue({
                          dutyId: undefined,
                        });
                      }}
                    />
                  </Form.Item>
                  <Form.Item
                    noStyle
                    shouldUpdate={(pre, next) => {
                      return (
                        pre.replaceStaffId !== next.replaceStaffId ||
                        pre.scheduleDate !== next.scheduleDate
                      );
                    }}
                  >
                    {({ getFieldValue }) => {
                      const replaceStaffId = getFieldValue('replaceStaffId');
                      const scheduleDate = getFieldValue('scheduleDate');
                      return (
                        <Form.Item
                          name="dutyId"
                          rules={[
                            {
                              required: true,
                              message: '班次必选',
                            },
                          ]}
                        >
                          <ScheduleShiftByUserSelect
                            style={{ width: 216 }}
                            labelInValue
                            disabled={!replaceStaffId || !scheduleDate}
                            placeholder="班次"
                            params={{
                              type: AlterType.Exchange,
                              shiftDate: scheduleDate?.startOf('day').valueOf(),
                              staffId: replaceStaffId,
                            }}
                          />
                        </Form.Item>
                      );
                    }}
                  </Form.Item>
                </Space>
              </Form.Item>
            )}
            {correctType === 'REPLACE' && (
              <Form.Item
                label="顶班人"
                name="replaceStaffId"
                rules={[
                  {
                    required: true,
                    message: '顶班人必选',
                  },
                ]}
              >
                <ShiftReplacementUserSelect
                  style={{ width: 216 }}
                  trigger="onFocus"
                  params={{
                    type: 'REST',
                    shiftId: record.dutyId,
                    shiftDate: record.scheduleDate,
                    staffId: Number(record.staffId),
                  }}
                />
              </Form.Item>
            )}
            {correctType === 'OVERTIME_CHANGE' && (
              <Form.Item
                name="overTimeRange"
                label="加班时间"
                rules={[
                  {
                    required: true,
                    message: '加班时间必填',
                  },
                  {
                    validator: (_, rang) => {
                      //季度工时 加班累计时长超过了108h  需要获取用户当月累计加班时长
                      if (
                        isQuarterUser &&
                        rang &&
                        calcOvertime &&
                        userTotalOvertimeByMonth - record.totalTime + calcOvertime > 108
                      ) {
                        return Promise.reject(
                          '季度工时员工，加班累计时长不可超过108小时/季度，请修改加班时间'
                        );
                      } else if (calcOvertime && calcOvertime > 12) {
                        return Promise.reject('所选加班区间需≤12h');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
                validateStatus={overtimeError ? 'error' : undefined}
                help={overtimeError ? overtimeError : undefined}
              >
                <DatePicker.RangePicker
                  style={{ width: '100%' }}
                  status={overtimeError ? 'error' : undefined}
                  showTime={{ format: 'HH:mm' }}
                  format="YYYY-MM-DD HH:mm"
                  minuteStep={30}
                  defaultPickerValue={
                    record.startTime && record.endTime
                      ? [
                          moment(record.startTime).startOf('day'),
                          moment(record.endTime).endOf('day'),
                        ]
                      : undefined
                  }
                  disabledDate={current => {
                    return (
                      current &&
                      (current < moment(record.scheduleDate).subtract(1, 'day').startOf('day') ||
                        current > moment(record.scheduleDate).add(1, 'day').endOf('day'))
                    );
                  }}
                  onChange={value => {
                    if (value && value[0] && value[1]) {
                      validOvertimeError(value as [moment.Moment, moment.Moment]);
                      validIsRestDay(value as [moment.Moment, moment.Moment]);
                    }
                  }}
                />
              </Form.Item>
            )}
            <Form.Item
              name="reason"
              label="订正原因"
              rules={[
                {
                  required: true,
                  message: '订正原因必填',
                },
                {
                  whitespace: true,
                  max: 300,
                  message: '最多输入 300 个字符！',
                },
              ]}
            >
              <Input.TextArea rows={3} maxLength={300} />
            </Form.Item>
            <Form.Item
              name="attachments"
              label="订正附件"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <Upload
                maxCount={5}
                maxFileSize={10}
                accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
                onChange={info => {
                  if (
                    info.file.status === 'uploading' &&
                    (fileList ?? []).filter(
                      (file: { status: string }) => file.status !== 'uploading'
                    ).length === 5
                  ) {
                    message.error('图片上传数量不能超过5张');
                    return;
                  }
                }}
              >
                <Space direction="vertical" size="small">
                  <Button type="default" icon={<UploadOutlined />}>
                    点此上传
                  </Button>
                  <Typography.Text type="secondary">
                    支持扩展名：.jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf
                  </Typography.Text>
                </Space>
              </Upload>
            </Form.Item>
          </Form>
        </Space>
      </Modal>
      <Modal
        width={648}
        open={openSuccess}
        footer={null}
        onCancel={() => {
          setOpenSuccess(false);
          onSuccess?.();
        }}
      >
        <Result
          status="success"
          title="订正成功！"
          subTitle={hasChangeOverTime ? '请确保调休余额已调整，如已调整请忽略。' : ''}
          extra={[
            hasChangeOverTime && (
              <Button
                key="adjust"
                type="primary"
                onClick={() => {
                  window.open(generateBreakOffBalanceRoutePath({ id: record.staffId }));
                }}
              >
                调整调休余额
              </Button>
            ),
            <Button
              key="finish"
              onClick={() => {
                setOpenSuccess(false);
                onSuccess?.();
              }}
            >
              完成
            </Button>,
          ]}
        />
      </Modal>
    </>
  );
};
