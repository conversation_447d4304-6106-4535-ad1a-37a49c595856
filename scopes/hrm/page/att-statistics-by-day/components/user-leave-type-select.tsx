import React, { useCallback, useEffect, useMemo, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import type { LeaveRequestType } from '@manyun/hrm.model.shift-adjustment-ticket';
import { getShiftAdjustmentTicketLocales } from '@manyun/hrm.model.shift-adjustment-ticket';
import {
  BALANCE_TYPE,
  BALANCE_UNIT,
  BALANCE_UNIT_MAP,
  fetchMyLeaveBalance,
} from '@manyun/hrm.service.fetch-my-leave-balance';
import type { LeaveBalance } from '@manyun/hrm.service.fetch-my-leave-balance';

export type LabelInValue = {
  label: string;
  value: string;
  balance?: number;
  unit: string;
  unitName: string;
};
export type UserLeaveTypeSelectProps = {
  userId: number;
  showBalance: boolean;
  value?: LabelInValue;
  onChange?: (value?: LabelInValue) => void;
} & Omit<SelectProps, 'options' | 'value' | 'onChange'>;

export const UserLeaveTypeSelect = ({
  userId,
  showBalance,
  value,
  onChange,
  ...props
}: UserLeaveTypeSelectProps) => {
  const [balanceEntities, setBalanceEntities] = useState<Record<string, LeaveBalance>>({});
  const [loading, setLoading] = useState(false);
  /**获取假期额度 */
  const _fetchMyLeaveBalance = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchMyLeaveBalance({ staffId: userId });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    /**年假，全新病假，调休需要计算余额 */
    const entities: Record<string, LeaveBalance> = {};
    /**公司年假 */
    const companyBalance = filterBalance(BALANCE_TYPE.COMPANY_LEAVE, data);
    /**法定年假 */
    const statutoryBalance = filterBalance(BALANCE_TYPE.STATUTORY_ANNUAL_LEAVE, data);
    /**上年结转公司年假 */
    const carryOverBalance = filterBalance(BALANCE_TYPE.CARRY_OVER_COMPANY_LEAVE, data);

    /**全新病假 */
    entities['FULL_PAY_SICK_LEAVE'] = filterBalance(BALANCE_TYPE.SICK_SALARY, data);
    /**调休 */
    entities['BREAK_OFF'] = filterBalance(BALANCE_TYPE.PAID_LEAVE, data);
    /** 年假 =  公司年假 + 法定年假 + 上年结转年假 */
    entities['ANNUAL_LEAVE'] = {
      type: BALANCE_TYPE.COMPANY_LEAVE,
      balance: Number(
        (companyBalance.balance + statutoryBalance.balance + carryOverBalance.balance).toFixed(1)
      ),
      totalBalance: Number(
        (
          (companyBalance.totalBalance ?? 0) +
          (statutoryBalance.totalBalance ?? 0) +
          (carryOverBalance.totalBalance ?? 0)
        ).toFixed(1)
      ),
      unit: companyBalance.unit,
      unitName: companyBalance.unitName,
    };
    setBalanceEntities(entities);
  }, [userId]);

  useEffect(() => {
    if (showBalance) {
      _fetchMyLeaveBalance();
    }
  }, [_fetchMyLeaveBalance, showBalance]);

  const options = useMemo(() => {
    let types: { label: string; value: string }[] = [];
    const textMapper = getShiftAdjustmentTicketLocales().leaveType.enum;
    if (showBalance) {
      types = Object.keys(balanceEntities).map(key => ({
        label: `${textMapper[key as LeaveRequestType]}(${balanceEntities[key].balance}${
          balanceEntities[key].unitName
        })`,
        value: key,
        disabled: balanceEntities[key].balance === 0,
      }));
    } else {
      types = Object.keys(textMapper).map(key => ({
        label: textMapper[key as LeaveRequestType],
        value: key,
      }));
    }
    return types.filter(type => type.value.indexOf('COMBINED') === -1);
  }, [balanceEntities, showBalance]);

  return (
    <Select
      {...props}
      loading={loading}
      options={options}
      labelInValue
      value={value}
      onChange={value => {
        onChange?.({
          ...value,
          balance: balanceEntities[value.value]?.balance,
          unit:
            balanceEntities[value.value]?.unit ?? (value.value === 'BREAK_OFF' ? 'HOUR' : 'DAY'),
          unitName: balanceEntities[value.value]?.unitName,
        });
      }}
    />
  );
};

function filterBalance(balanceType: BALANCE_TYPE, data: LeaveBalance[]): LeaveBalance {
  const findItem = data.find(balance => balance.type === balanceType);
  const unit = balanceType === BALANCE_TYPE.PAID_LEAVE ? BALANCE_UNIT.HOUR : BALANCE_UNIT.DAY;
  return (
    findItem ?? {
      type: balanceType,
      unit,
      unitName: BALANCE_UNIT_MAP[unit],
      balance: 0,
      totalBalance: 0,
    }
  );
}
