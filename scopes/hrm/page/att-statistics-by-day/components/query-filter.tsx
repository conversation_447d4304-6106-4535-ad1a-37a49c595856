import React, { useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useLatest } from 'react-use';

import uniq from 'lodash.uniq';
import moment from 'moment';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMyResourceCodes } from '@manyun/auth-hub.state.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserTypeSelect } from '@manyun/auth-hub.ui.user-type';
import type { SvcQuery as QueryDailyAttStatisticField } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';
import { ShiftSelect } from '@manyun/hrm.ui.shift-select';
import { ShiftTeamSelect } from '@manyun/hrm.ui.shift-team-select';
import type { SpaceTreeNode } from '@manyun/resource-hub.ui.location-tree-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { ShiftTypeSelect } from './shift-type';

const itemLayout = {
  labelCol: { span: 4 },
  wrapperCol: { span: 20 },
};
const colSpan = {
  xxl: 6,
  xl: 8,
};

export type DateValue = 'today' | 'yesterday' | 'thisWeek' | 'lastWeek' | 'custom';

export type FilterFormValue = Omit<QueryDailyAttStatisticField, 'staffId'> & {
  staffId?: { label: string; value: number };
  staffName?: string;
  searchDate: DateValue;
  customDateRange?: moment.Moment[];
  location: {
    value?: string[];
    blockTags?: string[];
    idcTags?: string[];
  };
};
const dateOptions: {
  label: string;
  value: DateValue;
}[] = [
  { label: '今日', value: 'today' },
  { label: '昨日', value: 'yesterday' },
  { label: '本周', value: 'thisWeek' },
  { label: '上周', value: 'lastWeek' },
  { label: '自定义日期', value: 'custom' },
];

export type QueryFilterProps = {
  initialValues?: FilterFormValue;
  form: FormInstance<FilterFormValue>;
  onSearch: (resetPagination?: boolean) => void;
};

export const QueryFilter = ({ initialValues, form, onSearch }: QueryFilterProps) => {
  const [authorized] = useAuthorized({ checkByCode: 'element_location-filter' });
  const { search } = useLocation();
  const {
    startDate,
    endDate,
    staffId,
    staffName,
    blockTags,
    idcTags,
    searchDate,
    dutyIds,
    dutyGroupIds,
    staffType,
    bizType,
    needLeave,
  } = getLocationSearchMap<
    Partial<
      FilterFormValue & {
        dutyIds: string[];
        dutyGroupIds: string[];
        idcTags: string[];
        blockTags: string[];
      }
    >
  >(search, {
    arrayKeys: ['dutyIds', 'dutyGroupIds', 'idcTags', 'blockTags'],
    parseNumbers: true,
  });
  const myResourceCodes = useSelector(selectMyResourceCodes);
  const _onSearchRef = useLatest(onSearch);
  const hasDefaultUrlSearch = useDeepCompareMemo(
    () =>
      startDate !== undefined ||
      endDate !== undefined ||
      staffId !== undefined ||
      staffName !== undefined ||
      blockTags !== undefined ||
      idcTags !== undefined ||
      dutyGroupIds !== undefined ||
      dutyIds !== undefined,
    [blockTags, dutyGroupIds, dutyIds, endDate, idcTags, staffId, staffName, startDate]
  );

  const onResetAndSearch = useCallback(() => {
    form.setFieldsValue({
      location: {
        value: undefined,
        idcTags: undefined,
        blockTags: !authorized
          ? (myResourceCodes ?? []).filter((d: string) => d.split('.').length === 2)
          : undefined,
      },
      idcTags: undefined,
      blockTags: undefined,
      dutyIds: undefined,
      dutyGroupIds: undefined,
      staffId: undefined,
      staffName: undefined,
      bizType: undefined,
      needLeave: undefined,
      staffType: undefined,
    });
    _onSearchRef.current(true);
  }, [form, authorized, myResourceCodes, _onSearchRef]);

  useEffect(() => {
    if (!authorized) {
      form.setFieldsValue({
        location: {
          blockTags: (myResourceCodes ?? []).filter((d: string) => d.split('.').length === 2),
        },
      });
      _onSearchRef.current();
    } else if (hasDefaultUrlSearch) {
      _onSearchRef.current();
    }
  }, [_onSearchRef, authorized, form, hasDefaultUrlSearch, myResourceCodes]);

  return (
    <Form
      layout="horizontal"
      form={form}
      colon={false}
      initialValues={{
        bizType,
        needLeave,
        staffType,
        searchDate: searchDate ?? 'today',
        customDateRange:
          startDate && endDate
            ? [moment(Number(startDate)), moment(Number(endDate))]
            : [moment().startOf('day'), moment().endOf('day')],
        staffId: staffId && staffName ? { label: staffName, value: staffId } : undefined,
        location: {
          value: [...(idcTags ?? []), ...(blockTags ?? [])],
          idcTags: idcTags,
          blockTags: blockTags,
        },
        dutyIds: dutyIds,
        dutyGroupIds: dutyGroupIds,
      }}
    >
      <Row gutter={[16, 24]}>
        <Col span={24}>
          <Space>
            <Form.Item
              style={{ marginBottom: 0, width: '440px' }}
              labelCol={{ span: 3 }}
              label="周期"
              name="searchDate"
            >
              <Radio.Group
                options={dateOptions}
                onChange={() => {
                  onResetAndSearch();
                }}
              />
            </Form.Item>
            <Form.Item
              shouldUpdate={(pre, next) => {
                return pre.searchDate !== next.searchDate;
              }}
              noStyle
            >
              {({ getFieldValue }) => {
                if (getFieldValue('searchDate') === 'custom') {
                  return (
                    <Form.Item name="customDateRange" style={{ marginBottom: 0 }}>
                      <DatePicker.RangePicker
                        allowClear={false}
                        onChange={() => {
                          onResetAndSearch();
                        }}
                      />
                    </Form.Item>
                  );
                }
                return null;
              }}
            </Form.Item>
          </Space>
        </Col>
        <Col {...colSpan}>
          <Form.Item {...itemLayout} label="姓名" name="staffId" style={{ marginBottom: 0 }}>
            <UserSelect style={{ width: 284 }} allowClear labelInValue />
          </Form.Item>
        </Col>
        {authorized ? (
          <Col {...colSpan}>
            <Form.Item
              style={{ marginBottom: 0 }}
              labelCol={{ span: 4 }}
              label="位置"
              name="location"
              getValueFromEvent={(value, _, { dataRef }) => {
                const _idcTags: string[] = [];
                let _blockTags: string[] = [];
                ((dataRef ?? []) as SpaceTreeNode[]).forEach(node => {
                  if (node.type === 'IDC') {
                    _idcTags.push(node.value);
                    _blockTags = [..._blockTags, ...(node.children ?? []).map(n => n.value)];
                  } else if (node.type === 'BLOCK') {
                    _blockTags.push(node.value);
                  }
                });
                return {
                  value,
                  idcTags: _idcTags,
                  blockTags: uniq(_blockTags),
                };
              }}
              getValueProps={value => {
                return { value: value?.value };
              }}
            >
              <LocationTreeSelect
                maxTagCount="responsive"
                multiple
                style={{ width: 284 }}
                allowClear
                showCheckedStrategy={TreeSelect.SHOW_PARENT}
                onTreeDataChange={treeNode => {
                  if (treeNode.length > 0 && !hasDefaultUrlSearch) {
                    /**从菜单进入默认选中第一个机房 */
                    const defaultIdc = [treeNode[0].value];
                    const defaultBlocks = (treeNode[0].children ?? []).map(block => block.value);
                    form.setFieldsValue({
                      location: {
                        value: defaultIdc,
                        idcTags: defaultIdc,
                        blockTags: defaultBlocks,
                      },
                      idcTags: defaultIdc,
                      blockTags: defaultBlocks,
                    });
                    _onSearchRef.current();
                  }
                }}
              />
            </Form.Item>
          </Col>
        ) : (
          <Form.Item name="location" style={{ display: 'none' }} />
        )}
        <Col {...colSpan}>
          <Form.Item {...itemLayout} label="类型" name="staffType" style={{ marginBottom: 0 }}>
            <UserTypeSelect
              style={{ width: 284 }}
              omitOptions={['CUSTOMER', 'VENDOR']}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col {...colSpan}>
          <Form.Item {...itemLayout} label="班组" name="dutyGroupIds" style={{ marginBottom: 0 }}>
            <ShiftTeamSelect
              placeholder="请选择"
              style={{ width: 284 }}
              maxTagCount={1}
              mode="multiple"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col {...colSpan}>
          <Form.Item {...itemLayout} label="班次" name="dutyIds" style={{ marginBottom: 0 }}>
            <ShiftSelect
              placeholder="请选择"
              style={{ width: 284 }}
              maxTagCount={1}
              mode="multiple"
              allowClear
            />
          </Form.Item>
        </Col>
        <Col {...colSpan}>
          <Form.Item {...itemLayout} label="班次类型" name="bizType" style={{ marginBottom: 0 }}>
            <ShiftTypeSelect style={{ width: 284 }} allowClear />
          </Form.Item>
        </Col>
        <Col {...colSpan}>
          <Form.Item {...itemLayout} label="是否请假" name="needLeave" style={{ marginBottom: 0 }}>
            <Select
              style={{ width: 284 }}
              options={[
                { label: '是', value: 'TRUE' },
                { label: '否', value: 'FALSE' },
              ]}
              allowClear
            />
          </Form.Item>
        </Col>
        <Col {...colSpan}>
          <Form.Item style={{ marginBottom: 0 }} wrapperCol={{ offset: 4 }}>
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  _onSearchRef.current(true);
                }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  onResetAndSearch();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Col>
      </Row>
    </Form>
  );
};
