import React, { useEffect, useMemo, useState } from 'react';

import dayjs from 'dayjs';

import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { User } from '@manyun/auth-hub.ui.user';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import type { LeaveRequestType } from '@manyun/hrm.model.shift-adjustment-ticket';
import { getShiftAdjustmentTicketLocales } from '@manyun/hrm.model.shift-adjustment-ticket';
import type {
  CheckResultChangeRecord,
  DailyAttStatistics,
} from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';

import { getClockInTypeValue } from '../att-statistics-by-day';
import { CHANGE_TYPE_TEXT_MAPPER } from './constant';

export type CorrectRecordsModalViewProps = {
  record: DailyAttStatistics;
};
export const CorrectRecordsModalView = ({ record }: CorrectRecordsModalViewProps) => {
  const [authorized] = useAuthorized({ checkByCode: 'element_location-filter' });
  const [open, setOpen] = useState(false);

  if (!authorized || !record.checkResultChangeRecordModels) {
    return null;
  }
  return (
    <Popover
      trigger="click"
      open={open}
      title="订正记录"
      content={
        <Space
          style={{ width: '100%', maxHeight: '500px', overflowY: 'auto', maxWidth: 390 }}
          direction="vertical"
          size="large"
        >
          {record.checkResultChangeRecordModels?.map(item => {
            return (
              <CorrectRecord
                key={item.checkResultId}
                record={item}
                onClickAttachment={() => {
                  setOpen(false);
                }}
              />
            );
          })}
        </Space>
      }
      placement="topRight"
      onOpenChange={newOpen => {
        setOpen(newOpen);
      }}
    >
      <Button type="link" compact>
        订正记录
      </Button>
    </Popover>
  );
};

export type CorrectRecordProps = {
  record: CheckResultChangeRecord;
  onClickAttachment?: () => void;
};
export const CorrectRecord = ({ record, onClickAttachment }: CorrectRecordProps) => {
  const [attachments, setAttachments] = useState<McUploadFile[]>([]);
  const leaveTypeMapper = useMemo(() => getShiftAdjustmentTicketLocales().leaveType.enum, []);

  useEffect(() => {
    (async () => {
      const { error, data } = await fetchBizFileInfos({
        targetId: record.bizId,
        targetType: 'CHECK_RESULT',
      });
      if (error) {
        message.error(error.message);
        setAttachments([]);
        return;
      }
      setAttachments(data.data);
    })();
  }, [record.bizId, record.bizType]);

  const items = [
    {
      label: '原始结果',
      value: <OnOffDutyStatusInfos record={record.checkResultCompare.oldCheckResult} />,
    },
    {
      label: '订正结果',
      value: <OnOffDutyStatusInfos record={record.checkResultCompare.newCheckResult} />,
    },
    {
      label: '订正方式',
      value: <CorrectTypeInfos record={record} />,
    },
    {
      label: '请假类型',
      value: record.changeParam.leaveType
        ? leaveTypeMapper[record.changeParam.leaveType as LeaveRequestType]
        : '--',
      show: record.changeType === 'LEAVE',
    },
    {
      label: '请假时间',
      value:
        record.changeParam.startTime && record.changeParam.endTime
          ? `${dayjs(record.changeParam.startTime).format('YYYY.MM.DD HH:mm')} - ${dayjs(
              record.changeParam.endTime
            ).format('YYYY.MM.DD HH:mm')}`
          : '--``',
      show: record.changeType === 'LEAVE',
    },
    {
      label: '请假时长',
      value: record.changeParam.totalTime
        ? `${record.changeParam.totalTime}${record.changeParam.unit === 'DAY' ? '天' : '小时'}`
        : '--',
      show: record.changeType === 'LEAVE' && record.changeParam.reduceBalance,
    },
    {
      label: '替班人',
      value: record.checkResultCompare.newCheckResult.staffName
        ? `${record.checkResultCompare.newCheckResult.staffName}（原排班人员：${record.checkResultCompare.oldCheckResult.staffName}）`
        : '--',
      show: record.changeType === 'EXCHANGE',
    },
    {
      label: '还班班次',
      value: record.changeParam.changeInfo?.scheduleDate
        ? `${dayjs(record.changeParam.changeInfo.scheduleDate).format('YYYY.MM.DD')} ${
            record.changeParam.changeInfo?.dutyName ?? ''
          }`
        : '--',
      show: record.changeType === 'EXCHANGE',
    },
    {
      label: '顶班人',
      value: record.checkResultCompare.newCheckResult.staffName
        ? `${record.checkResultCompare.newCheckResult.staffName}（原排班人员：${record.checkResultCompare.oldCheckResult.staffName}）`
        : '--',
      show: record.changeType === 'REPLACE',
    },
    {
      label: '加班时间',
      value:
        record.changeParam.startTime && record.changeParam.endTime
          ? `${dayjs(record.changeParam.startTime).format('YYYY.MM.DD HH:mm')} - ${dayjs(
              record.changeParam.endTime
            ).format('YYYY.MM.DD HH:mm')}`
          : '--``',
      show: record.changeType === 'OVERTIME_CHANGE',
    },
    {
      label: '订正原因',
      value: record.reason ?? '--',
    },
    {
      label: '订正附件',
      key: 'fileList',
      value:
        attachments.length > 0 ? (
          <SimpleFileList
            files={attachments}
            children={
              <Button type="link" compact>
                查看
              </Button>
            }
          />
        ) : (
          '--'
        ),
      show: attachments.length > 0,
    },

    {
      label: '操作用户',
      value: <User id={record.modifierId} showAvatar={false} />,
    },
    {
      label: '订正时间',
      value: dayjs(record.gmtModified).format('YYYY.MM.DD HH:mm:ss'),
    },
  ];

  return (
    <Space style={{ width: '100%' }} size={0} direction="vertical" wrap>
      {items
        .filter(item => item.show !== false)
        .map(item => (
          <div key={item.label} style={{ display: 'flex', gap: 4, alignItems: 'flex-start' }}>
            <Typography.Text>{item.label}:</Typography.Text>
            <Typography.Text
              style={{ flex: 1 }}
              onClick={e => {
                if (item?.key === 'fileList') {
                  onClickAttachment?.();
                }
              }}
            >
              {item.value}
            </Typography.Text>
          </div>
        ))}
    </Space>
  );
};

export const OnOffDutyStatusInfos = ({ record }: { record: DailyAttStatistics }) => {
  /**
   * 上班考勤状态
   */
  const clockOnValues = useMemo(
    () =>
      getClockInTypeValue({
        type: 'on',
        value: record.onDutyTime,
        isMissClockIn: record.isMissOnCard === 1,
        isLate: record.lateOnMinutes > 0,
      }),
    [record.isMissOnCard, record.lateOnMinutes, record.onDutyTime]
  );
  /**
   * 下班考勤状态
   */
  const clockOffValues = useMemo(
    () =>
      getClockInTypeValue({
        type: 'off',
        value: record.offDutyTime,
        isMissClockIn: record.isMissOffCard === 1,
        isLate: record.earlyOffMinutes > 0,
      }),
    [record.earlyOffMinutes, record.isMissOffCard, record.offDutyTime]
  );
  return (
    <Space size="small">
      <Typography.Text>
        上班卡 {record.onDutyTime ? dayjs(record.onDutyTime).format('HH:mm') : '--'}{' '}
        {clockOnValues ? `(${clockOnValues.text})` : ''}
      </Typography.Text>
      <Typography.Text>
        下班卡 {record.offDutyTime ? dayjs(record.offDutyTime).format('HH:mm') : '--'}
        {clockOffValues ? `(${clockOffValues.text})` : ''}
      </Typography.Text>
    </Space>
  );
};

export const CorrectTypeInfos = ({
  record,
}: {
  record: Pick<CheckResultChangeRecord, 'changeType' | 'changeParam'>;
}) => {
  const extra = useMemo(() => {
    const changeParam = record.changeParam;

    switch (record.changeType) {
      case 'CHECK_CHANGE':
        if (changeParam.checkChangeType === 'ALL') {
          return '(上班卡正常 ｜ 下班卡正常)';
        } else if (changeParam.checkChangeType === 'ON') {
          return '(上班卡正常)';
        } else {
          return '(下班卡正常)';
        }
      case 'LEAVE':
        return `(${changeParam.reduceBalance ? '提交后自动扣除余额' : '不扣除余额'})`;
      case 'EXCHANGE':
      case 'REPLACE':
      case 'REST':
      case 'OVERTIME_CHANGE':
      case 'OVERTIME_DELETE':
      default:
        return null;
    }
  }, [record.changeParam, record.changeType]);
  return (
    <Space size="small">
      {CHANGE_TYPE_TEXT_MAPPER[record.changeType]}
      {extra}
    </Space>
  );
};
