import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

export type ShiftTypeSelectProps = Omit<SelectProps, 'options'>;

export const ShiftTypeSelect = React.forwardRef(
  (props: ShiftTypeSelectProps, ref: React.Ref<RefSelectProps>) => {
    return (
      <Select
        ref={ref}
        style={{ width: 228 }}
        {...props}
        options={[
          { label: '排班', value: 'SCHEDULE' },
          { label: '加班', value: 'OVERTIME' },
        ]}
      />
    );
  }
);
ShiftTypeSelect.displayName = 'ShiftTypeSelect';
