import React, { useCallback } from 'react';

import { saveAs } from 'file-saver';

import { TableExportOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import type { ColumnType } from '@manyun/base-ui.ui.edit-columns';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type { IncludeColumnField, SvcQuery } from '@manyun/hrm.service.export-daily-att-statistics';
import { exportDailyAttStatistics } from '@manyun/hrm.service.export-daily-att-statistics';
import type { DailyAttStatistics } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';

export type AttStatisticTableToolsProps = {
  searchFields: SvcQuery;
  defaultTableColumns: ColumnType<DailyAttStatistics>[];
  tableColumns: ColumnType<DailyAttStatistics>[];
  onColumnsChange: (tableColumns: ColumnType<DailyAttStatistics>[]) => void;
};
export function AttStatisticTableTools({
  searchFields,
  defaultTableColumns,
  tableColumns,
  onColumnsChange,
}: AttStatisticTableToolsProps) {
  const _onHandleExport = useCallback(async () => {
    const { error, data } = await exportDailyAttStatistics({
      ...searchFields,
      includeFileds: tableColumns
        .filter(column => column.show !== false)
        .map(column => column.dataIndex) as IncludeColumnField[],
    });

    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '人员每日考勤统计');
  }, [searchFields, tableColumns]);

  return (
    <Space size={16}>
      <Button
        icon={<TableExportOutlined style={{ fontSize: 18 }} />}
        compact
        type="text"
        onClick={() => _onHandleExport()}
      />
      <EditColumns
        uniqKey="HRM_PAGE_ATT_STATISTIC_BY_DAY"
        listsHeight={300}
        defaultValue={defaultTableColumns}
        allowSetAsFixed={false}
        onChange={value => {
          onColumnsChange(value);
        }}
      />
    </Space>
  );
}
