import uniq from 'lodash.uniq';
import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useHistory, useParams } from 'react-router';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { Space as SpaceModel } from '@manyun/dc-brain.gql.client';
import { useLazyPerformancePlanById } from '@manyun/hrm.gql.client.hrm';
import {
  AnnualPerformancePlan,
  getAnnualPerformancePlanLocales,
} from '@manyun/hrm.model.annual-performance-plan';
import type { AnnualPerformancePlanConfig } from '@manyun/hrm.model.annual-performance-plan/annual-performance-plan';
import { ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH } from '@manyun/hrm.route.hrm-routes';
import type { AnnualPerformanceObjectiveEditParams } from '@manyun/hrm.route.hrm-routes';
import { createAndPublicAnnualPerformancePlan } from '@manyun/hrm.service.create-and-public-annual-performance-plan';
import { updateAnnualPerformancePlan } from '@manyun/hrm.service.update-annual-performance-plan';
import { PerformancePositionCheckboxGroup } from '@manyun/hrm.ui.performance-position';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

const formItem = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 20,
  },
};

const formItemWithOutLabel = {
  wrapperCol: {
    offset: 8,
  },
};

export function AnnualPerformancePlanMutator() {
  const params = useParams<AnnualPerformanceObjectiveEditParams>();
  const { id } = params;
  const [form] = Form.useForm();
  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);
  const history = useHistory();
  const isEdit = useMemo(() => !!id, [id]);
  const year = Form.useWatch('year', form);
  const splitRule = 'QUARTER'; //Form.useWatch('splitRule', form);

  const [submitLoading, setSubmitLoading] = useState(false);
  const [fetch, { loading, data }] = useLazyPerformancePlanById();

  useEffect(() => {
    if (isEdit) {
      /**请求详情 - 注意: 子任务自评起止日期： 未到开始日期时可修改，已开始后置灰不可修改   */
      fetch({
        variables: {
          id: Number(id),
        },
      }).then(result => {
        if (result.data?.performancePlanById) {
          const record = result.data.performancePlanById;
          form.setFieldsValue({
            ...record,
            year: moment(record.year),
            resourcesScope: {
              /**cascader 所需要的默认值 */
              value: record.resourcesScope.map(({ value, parentCode }) => [parentCode, value]),
              idcTags: record.resourcesScope.map(({ value }) => value),
            },
            positionScope: record.positionScope.map(({ value }) => value),
            canSetResultDate: moment(record.canSetResultDate),
            subConfigs: record.subConfigs.map(config => ({
              ...config,
              natureStartTimeRange: config.natureStartTimeRange.map(time => moment(time)),
              evalStartTimeRange: config.evalStartTimeRange.map(time => moment(time)),
            })),
          });
        }
      });
    } else {
      /**新建 设置默认值 */
      form.setFieldsValue({
        year: moment(),
        name: `${moment().format('YYYY')}年度绩效考核`,
      });
    }
  }, [fetch, form, id, isEdit]);

  useEffect(() => {
    if (!isEdit && year && splitRule) {
      const subConfigs = AnnualPerformancePlan.getSubConfigs(splitRule, moment(year).year());
      form.setFieldsValue({
        subConfigs: subConfigs.map(config => ({
          ...config,
          evalStartTimeRange: [
            moment(config.evalStartTimeRange[0]),
            moment(config.evalStartTimeRange[1]),
          ],
        })),
      });
    }
  }, [form, isEdit, splitRule, year]);

  const onSubmit = useCallback(() => {
    form
      .validateFields()
      .then(async values => {
        setSubmitLoading(true);
        const _values = {
          ...values,
          splitRule: splitRule,
          subConfigs: values.subConfigs.map(
            (config: {
              evalStartTimeRange: [moment.Moment, moment.Moment];
              natureStartTimeRange: [moment.Moment, moment.Moment];
            }) => ({
              ...config,
              evalStartTimeRange: config.evalStartTimeRange.map(time =>
                time.startOf('day').valueOf()
              ),
              natureStartTimeRange: config.natureStartTimeRange.map(time => time.valueOf()),
            })
          ),
          resourcesScope: values?.resourcesScope?.idcTags,
          year: moment(values.year).format('YYYY'),
          canSetResultDate: moment(values.canSetResultDate).startOf('day').valueOf(),
        };

        const { error } = isEdit
          ? await updateAnnualPerformancePlan({
              ..._values,
              id: Number(id),
            })
          : await createAndPublicAnnualPerformancePlan(_values);

        setSubmitLoading(false);

        if (error) {
          message.error(error.message);
          return;
        }

        message.success(isEdit ? '保存成功' : '发起考核成功');

        history.goBack();
      })
      .catch(console.error);
  }, [form, history, id, isEdit]);

  return (
    <Card loading={loading}>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            {isEdit ? '修改配置' : '发起考核计划'}
          </Typography.Title>
          {!isEdit && (
            <Button
              type="primary"
              onClick={() => {
                window.open(ANNUAL_PERFORMANCE_OBJECTIVES_PREVIEW_ROUTE_PATH, '_blank');
              }}
            >
              预览目标
            </Button>
          )}
        </Space>
        {!loading && (
          <Form form={form} {...formItem}>
            <Form.Item
              colon={false}
              label={
                <Typography.Title style={{ marginBottom: 0, marginRight: 33 }} level={5}>
                  基本信息
                </Typography.Title>
              }
            />
            <Form.Item
              name="year"
              label={locales.year}
              rules={[
                {
                  required: true,
                  message: `${locales.year}必选`,
                },
              ]}
            >
              <DatePicker
                style={{ width: 216 }}
                disabled={isEdit}
                picker="year"
                disabledDate={current => {
                  return (
                    current &&
                    !current.isBetween(moment().subtract(1, 'year'), moment().add(1, 'year'))
                  );
                }}
                format={value => `${value.format('YYYY')}年度`}
                onChange={value => {
                  if (value) {
                    form.setFieldsValue({
                      name: `${value.format('YYYY')}年度绩效考核`,
                    });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              name="name"
              label={locales.name}
              rules={[
                {
                  required: true,
                  message: `${locales.name}必填`,
                },
                {
                  max: 30,
                  message: '最多输入 30 个字符！',
                },
              ]}
            >
              <Input style={{ width: 344 }} />
            </Form.Item>
            <Form.Item
              name="resourcesScope"
              label={locales.resourcesScope}
              getValueFromEvent={(value, selectOptions) => {
                let idcTags: string[] = [];

                ((selectOptions ?? []) as SpaceModel[][]).forEach(nodes => {
                  const onlySelectedRegion =
                    nodes.length === 1 && nodes.every(node => node.type === 'REGION');
                  if (onlySelectedRegion) {
                    const _regionChildren = nodes.reduce((acc: string[], region) => {
                      acc = (region.children ?? []).map(idc => idc.value);
                      return acc;
                    }, []);

                    idcTags = [...idcTags, ..._regionChildren];
                  } else {
                    idcTags = [
                      ...idcTags,
                      ...nodes.filter(node => node.type === 'IDC').map(({ value }) => value),
                    ];
                  }
                });
                return {
                  value,
                  idcTags: uniq(idcTags),
                };
              }}
              getValueProps={value => {
                return { value: value?.value };
              }}
              rules={[
                {
                  required: true,
                  message: `${locales.resourcesScope}必选`,
                },
                {
                  validator: (_, val) => {
                    if (
                      !val ||
                      !val?.value ||
                      (val.value && Array.isArray(val.value) && val.value.length === 0)
                    ) {
                      return Promise.reject(`${locales.resourcesScope}必选`);
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <LocationCascader
                style={{ width: 344 }}
                disabled={isEdit && !loading}
                showSearch
                showCheckedStrategy={Cascader.SHOW_PARENT}
                maxTagCount="responsive"
                nodeTypes={['REGION', 'IDC']}
                multiple
                allowClear
                onTreeDataChange={data => {
                  /**新建默认选中所有机房 */
                  if (!isEdit) {
                    //eg: [["086EC",'EC01']]
                    const defaultValues: string[][] = data.reduce((acc: string[][], regionNode) => {
                      if (regionNode.children) {
                        acc = [
                          ...acc,
                          ...regionNode.children.map(idc => [regionNode.value, idc.value]),
                        ];
                      }
                      return acc;
                    }, []);

                    form.setFieldsValue({
                      resourcesScope: {
                        value: defaultValues,
                        idcTags: data.reduce((idcTags: string[], space) => {
                          idcTags = idcTags.concat((space.children ?? []).map(idc => idc.value));
                          return idcTags;
                        }, []),
                      },
                    });
                  }
                }}
              />
            </Form.Item>
            {isEdit ? (
              <Form.Item
                name="positionScope"
                label={locales.positionScope}
                rules={[
                  {
                    required: true,
                    message: `${locales.positionScope}必选`,
                  },
                ]}
              >
                <PerformancePositionCheckboxGroup
                  style={{ maxWidth: '60%' }}
                  disabled
                  options={data?.performancePlanById?.positionScope}
                />
              </Form.Item>
            ) : (
              <Form.Item
                name="positionScope"
                label={locales.positionScope}
                rules={[
                  {
                    required: true,
                    message: `${locales.positionScope}必选`,
                  },
                ]}
              >
                <PerformancePositionCheckboxGroup
                  style={{ maxWidth: '60%' }}
                  disabled={isEdit && !loading}
                  onDataChange={data => {
                    if (!isEdit && data) {
                      form.setFieldsValue({
                        positionScope: data,
                      });
                    }
                  }}
                />
              </Form.Item>
            )}
            <Form.Item
              colon={false}
              label={
                <Typography.Title
                  style={{ marginBottom: 0, marginRight: 33, marginTop: 16 }}
                  level={5}
                >
                  考核配置
                </Typography.Title>
              }
            />
            {/* <Form.Item
              name="splitRule"
              label={locales.splitRule.__self}
              rules={[
                {
                  required: true,
                  message: `${locales.splitRule.__self}必选`,
                },
              ]}
            >
              <Radio.Group
                disabled={isEdit}
                options={Object.keys(locales.splitRule.enum).map(key => ({
                  label: (
                    <Explanation
                      style={{ color: 'inherit' }}
                      iconType="question"
                      tooltip={{
                        title: locales.splitRuleDesc[key as BackendAnnualPerformancePlanSplitRule],
                      }}
                    >
                      {locales.splitRule.enum[key as BackendAnnualPerformancePlanSplitRule]}
                    </Explanation>
                  ),
                  value: key,
                }))}
              />
            </Form.Item> */}
            <Form.Item
              shouldUpdate={(pre, next) =>
                pre.splitRule !== next.splitRule || pre.year !== next.year
              }
              noStyle
            >
              {({ getFieldValue }) => {
                if (splitRule && getFieldValue('year')) {
                  return (
                    <Form.List name="subConfigs">
                      {(fields, { add, remove }, { errors }) => {
                        return (
                          <>
                            {fields.map((field, index) => {
                              const config = getFieldValue('subConfigs')[
                                index
                              ] as AnnualPerformancePlanConfig;
                              return (
                                <Form.Item
                                  {...(index === 0 ? formItem : formItemWithOutLabel)}
                                  key={field.key}
                                  label={index === 0 ? '子考核任务' : ''}
                                  required
                                >
                                  <Form.Item
                                    style={{ marginBottom: 0 }}
                                    {...field}
                                    label={`${
                                      locales.performancePeriod.enum[config.period]
                                    }自评起止日期`}
                                    tooltip={`${
                                      locales.performancePeriodDesc.enum[config.period]
                                    } 考核周期为${moment(config.natureStartTimeRange[0]).format(
                                      'YYYY-MM-DD'
                                    )} ~ ${moment(config.natureStartTimeRange[1]).format(
                                      'YYYY-MM-DD'
                                    )}`}
                                    name={[field.name, 'evalStartTimeRange']}
                                    validateTrigger={['onChange', 'onBlur']}
                                    rules={[
                                      {
                                        validator: (_, val) => {
                                          if (!val) {
                                            return Promise.reject('自评起至时间必填');
                                          }
                                          return Promise.resolve();
                                        },
                                      },
                                    ]}
                                  >
                                    <DatePicker.RangePicker
                                      style={{ width: 240 }}
                                      disabled={
                                        /**编辑时，已到自评开始时间 不可修改  */
                                        isEdit &&
                                        config.evalStartTimeRange &&
                                        moment()
                                          .startOf('day')
                                          .diff(config.evalStartTimeRange[0]) >= 0
                                      }
                                      disabledDate={current => {
                                        /**不可早于周期开始时间 */
                                        return (
                                          current &&
                                          current.diff(config.natureStartTimeRange[0]) < 0
                                        );
                                      }}
                                      allowClear={false}
                                    />
                                  </Form.Item>
                                </Form.Item>
                              );
                            })}
                          </>
                        );
                      }}
                    </Form.List>
                  );
                }
                return null;
              }}
            </Form.Item>
            <Form.Item label={locales.beforeSubTaskDeadlineNotifyDays} required>
              <Space size={4}>
                <span>&nbsp;距子任务自评截止日期</span>
                <Form.Item
                  name="beforeSubTaskDeadlineNotifyDays"
                  noStyle
                  rules={[
                    {
                      required: true,
                      message: `${locales.beforeSubTaskDeadlineNotifyDays}必选`,
                    },
                  ]}
                >
                  <Select
                    disabled={isEdit}
                    style={{ width: 216 }}
                    maxTagCount="responsive"
                    mode="multiple"
                    options={[20, 15, 7, 3, 1].map(key => ({ label: `${key}天`, value: key }))}
                  />
                </Form.Item>
                <span>时，员工仍未自评，则发送消息提醒</span>
              </Space>
            </Form.Item>
            <Form.Item noStyle shouldUpdate>
              {({ getFieldValue }) => {
                const yearConfig = getFieldValue('subConfigs')?.find(
                  (config: { period: string }) => config.period === 'YEAR'
                );
                const yearEvalStartTime = yearConfig ? yearConfig.evalStartTimeRange[0] : undefined;

                return (
                  <Form.Item label={locales.canSetResultDate} required>
                    <Space size={4}>
                      <span>&nbsp;直线经理可在</span>
                      <Form.Item
                        name="canSetResultDate"
                        noStyle
                        rules={[
                          {
                            required: true,
                            message: `${locales.canSetResultDate}必选`,
                          },
                          {
                            validator: (_, val) => {
                              if (
                                val &&
                                yearEvalStartTime &&
                                moment(val).diff(yearEvalStartTime) < 0
                              ) {
                                return Promise.reject('不可早于年度子考核任务的自评开始日期');
                              }
                              return Promise.resolve();
                            },
                          },
                        ]}
                      >
                        <DatePicker
                          style={{ width: 216 }}
                          disabledDate={current =>
                            current &&
                            yearEvalStartTime &&
                            moment(current).diff(yearEvalStartTime) < 0
                          }
                        />
                      </Form.Item>
                      <span>起，填写年度考核结果评分</span>
                    </Space>
                  </Form.Item>
                );
              }}
            </Form.Item>
            <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
              <Space size="middle">
                <Button
                  type="primary"
                  loading={submitLoading}
                  disabled={
                    submitLoading ||
                    loading ||
                    (isEdit && data?.performancePlanById?.canModified === false)
                  }
                  onClick={() => {
                    onSubmit();
                  }}
                >
                  {isEdit ? '保存' : '发起考核'}
                </Button>
                <Button
                  disabled={submitLoading}
                  onClick={() => {
                    history.goBack();
                  }}
                >
                  {isEdit ? '取消修改' : '取消'}
                </Button>
              </Space>
            </Form.Item>
          </Form>
        )}
      </Space>
    </Card>
  );
}
