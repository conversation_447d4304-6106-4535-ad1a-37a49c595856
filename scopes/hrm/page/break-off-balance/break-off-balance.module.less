@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.breakOffBalaceContainer {
  display: flex;
  width: 100%;
  height: 100%;
  > :global(.manyun-space-item) {
    height: 100%;
    overflow-y: auto;
    &:first-child {
      width: 420px;
    }
    &:last-child {
      flex: 1;
    }
  }
  .balanceList {
    .descriptionContainer {
      padding: @padding-md @padding-lg;
      &:not(.activeDescriptionContainer) {
        .goSetting {
          display: none;
        }
        &:hover {
          background-color: @item-hover-bg;
          .goSetting {
            display: flex;
          }
        }
      }
    }
  }
  .activeDescriptionContainer {
    background-color: @alert-info-bg-color;
  }
}
