import React from 'react';

import { RouterProvider } from '@manyun/base-ui-web.context.router-context';
import { StoreProvider, history } from '@manyun/base-ui-web.context.store-context';
import { ConfigProvider } from '@manyun/base-ui.context.config';
import { LightTheme } from '@manyun/base-ui.theme.light-theme';

import { createRootReducer, createRootSaga, createStore } from '@manyun/dc-brain.store.store';
import { useRemoteMock } from '@manyun/service.request';

import { BreakOffBalancePage } from './break-off-balance';

const reducer = createRootReducer({ history });
const store = createStore({ history, reducer });
const saga = createRootSaga();
store.runSaga(saga);

export const BasicBreakOffBalance = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    // eslint-disable-next-line react-hooks/rules-of-hooks
    const mockOff = useRemoteMock('web');
    setReady(true);
    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }
  return (
    <ConfigProvider>
      <LightTheme>
        <StoreProvider store={store}>
          <RouterProvider>
            <BreakOffBalancePage />
          </RouterProvider>
        </StoreProvider>
      </LightTheme>
    </ConfigProvider>
  );
};
