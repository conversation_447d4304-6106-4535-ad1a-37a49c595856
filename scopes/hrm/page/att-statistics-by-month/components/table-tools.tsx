import React, { useCallback } from 'react';

import { saveAs } from 'file-saver';

import { TableExportOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type { ColumnType } from '@manyun/dc-brain.ui.edit-columns';
import { exportMonthlyAttStatistics } from '@manyun/hrm.service.export-monthly-att-statistics';
import type {
  IncludeColumnField,
  SvcQuery,
} from '@manyun/hrm.service.export-monthly-att-statistics';
import type { MonthlyAttStatistics } from '@manyun/hrm.service.fetch-paged-att-statistics-by-month';

export type AttStatisticTableToolsProps = {
  searchFields: SvcQuery;
  defaultTableColumns: ColumnType<MonthlyAttStatistics>[];
  tableColumns: ColumnType<MonthlyAttStatistics>[];
  onColumnsChange: (value: ColumnType<MonthlyAttStatistics>[]) => void;
};
export function AttStatisticTableTools({
  searchFields,
  defaultTableColumns,
  tableColumns,
  onColumnsChange,
}: AttStatisticTableToolsProps) {
  const _onHandleExport = useCallback(async () => {
    const { error, data } = await exportMonthlyAttStatistics({
      ...searchFields,
      includeColumnFiledNames: tableColumns
        .filter(column => column.show !== false)
        .map(column => column.dataIndex) as IncludeColumnField[],
    });
    if (error) {
      message.error(error.message);
      return;
    }
    saveAs(data, '人员月度考勤统计');
  }, [searchFields, tableColumns]);

  return (
    <Space size={16}>
      <Button
        icon={<TableExportOutlined style={{ fontSize: 18 }} />}
        compact
        type="text"
        onClick={() => _onHandleExport()}
      />
      <EditColumns
        uniqKey="HRM_PAGE_ATT_STATISC_BY_MONTH"
        listsHeight={300}
        defaultValue={defaultTableColumns}
        allowSetAsFixed={false}
        onChange={value => {
          onColumnsChange(value);
        }}
      />
    </Space>
  );
}
