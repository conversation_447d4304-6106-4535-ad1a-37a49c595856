import React, { useCallback, useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';
import { useLatest } from 'react-use';

import uniq from 'lodash.uniq';
import moment from 'moment';

import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import { TreeSelect } from '@manyun/base-ui.ui.tree-select';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMyResourceCodes } from '@manyun/auth-hub.state.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserTypeSelect } from '@manyun/auth-hub.ui.user-type';
import type { SvcQuery as QueryMonthlyAttStatisticsFields } from '@manyun/hrm.service.fetch-paged-att-statistics-by-month';
import type { SpaceTreeNode } from '@manyun/resource-hub.ui.location-tree-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

export type DateValue = 'thisMonth' | 'lastMonth' | 'custom';

const dateOptions: {
  label: string;
  value: DateValue;
}[] = [
  { label: '本月', value: 'thisMonth' },
  { label: '上月', value: 'lastMonth' },
  { label: '自定义日期', value: 'custom' },
];

export type FilterFormValue = Omit<QueryMonthlyAttStatisticsFields, 'staffId'> & {
  staffId?: { label: string; value: number };
  staffName?: string;
  searchDate: DateValue;
  customDateRange?: moment.Moment[];
  location: {
    value?: string[];
    blockTags?: string[];
    idcTags?: string[];
  };
};

export type QueryFilterProps = {
  form: FormInstance<FilterFormValue>;
  onSearch: (resetPagination?: boolean) => void;
};

export const QueryFilter = ({ form, onSearch }: QueryFilterProps) => {
  const [authorized] = useAuthorized({ checkByCode: 'element_location-filter' });
  const { search } = useLocation();
  const {
    staffType,
    startDate,
    endDate,
    staffId,
    searchDate,
    staffName,
    blockTags,
    idcTags,
    emptyLocation,
  } = getLocationSearchMap<
    Partial<
      FilterFormValue & {
        staffId: number;
        emptyLocation: boolean;
        idcTags: string[];
        blockTags: string[];
      }
    >
  >(search, {
    arrayKeys: ['blockTags', 'idcTags'],
    parseNumbers: true,
  });
  const myResourceCodes = useSelector(selectMyResourceCodes);
  const _onSearchRef = useLatest(onSearch);

  const hasDefaultUrlSearch = useDeepCompareMemo(
    () =>
      staffType !== undefined ||
      idcTags !== undefined ||
      blockTags !== undefined ||
      emptyLocation ||
      staffId !== undefined ||
      startDate !== undefined ||
      endDate !== undefined,
    [idcTags, blockTags, emptyLocation, staffId, startDate, endDate]
  );

  const onResetAndSearch = useCallback(() => {
    form.setFieldsValue({
      location: {
        value: undefined,
        idcTags: undefined,
        blockTags: !authorized
          ? (myResourceCodes ?? []).filter((d: string) => d.split('.').length === 2)
          : undefined,
      },
      staffId: undefined,
      staffType: undefined,
    });
    _onSearchRef.current(true);
  }, [_onSearchRef, authorized, form, myResourceCodes]);

  useEffect(() => {
    if (!authorized) {
      form.setFieldsValue({
        location: {
          blockTags: (myResourceCodes ?? []).filter((d: string) => d.split('.').length === 2),
        },
      });
      _onSearchRef.current(true);
    } else if (hasDefaultUrlSearch) {
      _onSearchRef.current();
    }
  }, [_onSearchRef, authorized, form, hasDefaultUrlSearch, myResourceCodes]);

  return (
    <Form
      form={form}
      layout="horizontal"
      colon={false}
      initialValues={{
        staffType,
        searchDate: searchDate ?? 'thisMonth',
        customDateRange:
          startDate && endDate
            ? [moment(Number(startDate)), moment(Number(endDate))]
            : [moment().startOf('month'), moment().endOf('month')],
        staffId: staffId && staffName ? { label: staffName, value: staffId } : undefined,
        location: {
          value: [...(idcTags ?? []), ...(blockTags ?? [])],
          idcTags: idcTags,
          blockTags: blockTags,
        },
      }}
    >
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Space size={16}>
          <Form.Item style={{ marginBottom: 0 }} label="周期" name="searchDate">
            <Radio.Group
              options={dateOptions}
              onChange={() => {
                onResetAndSearch();
              }}
            />
          </Form.Item>
          <Form.Item shouldUpdate={(pre, next) => pre.searchDate !== next.searchDate} noStyle>
            {({ getFieldValue }) => {
              if (getFieldValue('searchDate') === 'custom') {
                return (
                  <Form.Item style={{ marginBottom: 0 }} name="customDateRange">
                    <DatePicker.RangePicker
                      allowClear={false}
                      onChange={() => {
                        onResetAndSearch();
                      }}
                    />
                  </Form.Item>
                );
              }
              return null;
            }}
          </Form.Item>
        </Space>
        <Space size="large">
          <Form.Item style={{ marginBottom: 0 }} label="姓名" name="staffId">
            <UserSelect style={{ width: 200 }} allowClear labelInValue />
          </Form.Item>
          {authorized ? (
            <Form.Item
              style={{ marginBottom: 0 }}
              label="位置"
              name="location"
              getValueFromEvent={(value, _, { dataRef }) => {
                const _idcTags: string[] = [];
                let _blockTags: string[] = [];
                ((dataRef ?? []) as SpaceTreeNode[]).forEach(node => {
                  if (node.type === 'IDC') {
                    _idcTags.push(node.value);
                    _blockTags = [..._blockTags, ...(node.children ?? []).map(n => n.value)];
                  } else if (node.type === 'BLOCK') {
                    _blockTags.push(node.value);
                  }
                });
                return {
                  value,
                  idcTags: _idcTags,
                  blockTags: uniq(_blockTags),
                };
              }}
              getValueProps={value => {
                return { value: value?.value };
              }}
            >
              <LocationTreeSelect
                style={{ width: 328 }}
                maxTagCount={1}
                showCheckedStrategy={TreeSelect.SHOW_PARENT}
                multiple
                onTreeDataChange={treeNode => {
                  if (treeNode.length > 0 && !hasDefaultUrlSearch) {
                    /**从菜单进入默认选中第一个机房 */
                    const defaultIdc = [treeNode[0].value];
                    const defaultBlocks = (treeNode[0].children ?? []).map(block => block.value);
                    form.setFieldsValue({
                      location: {
                        value: defaultIdc,
                        idcTags: defaultIdc,
                        blockTags: defaultBlocks,
                      },
                    });
                    _onSearchRef.current();
                  }
                }}
              />
            </Form.Item>
          ) : (
            <Form.Item style={{ display: 'none' }} name="location" />
          )}
          <Form.Item style={{ marginBottom: 0 }} label="类型" name="staffType">
            <UserTypeSelect
              style={{ width: 284 }}
              omitOptions={['CUSTOMER', 'VENDOR']}
              allowClear
            />
          </Form.Item>
          <Form.Item style={{ marginBottom: 0 }}>
            <Space size={16}>
              <Button
                type="primary"
                onClick={() => {
                  _onSearchRef.current(true);
                }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  onResetAndSearch();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Space>
      </Space>
    </Form>
  );
};
