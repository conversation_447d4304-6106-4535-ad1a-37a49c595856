import React, { useCallback, useRef, useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

import moment from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, qs, setLocationSearch } from '@manyun/base-ui.util.query-string';

import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { generateAttendanceStatisticsLocation } from '@manyun/hrm.route.hrm-routes';
import { fetchPagedAttStatisticsByMonth } from '@manyun/hrm.service.fetch-paged-att-statistics-by-month';
import type {
  MonthlyAttStatistics,
  SvcQuery as QueryMonthlyAttStatisticsFields,
  SortField,
} from '@manyun/hrm.service.fetch-paged-att-statistics-by-month';
import type { AttStatisticsKey } from '@manyun/hrm.ui.att-statistics-card';
import {
  AttStatisticProvider,
  AttStatisticsCard,
  useAttStatistic,
} from '@manyun/hrm.ui.att-statistics-card';

import { QueryFilter } from './components/query-filter';
import type { DateValue, FilterFormValue } from './components/query-filter';
import { AttStatisticTableTools } from './components/table-tools';

type SearchFields = Omit<FilterFormValue, 'staffId'> &
  Pick<QueryMonthlyAttStatisticsFields, 'staffId'> & {
    statisticFilterField: AttStatisticsKey | null;
  };

type TableSorter = {
  order: 'ascend' | 'descend';
  field: SortField;
};

const generateBasicTableColumns = (
  stateRef: React.MutableRefObject<SearchFields>
): ColumnType<MonthlyAttStatistics>[] => {
  return [
    {
      title: '人员ID',
      dataIndex: 'staffId',
      fixed: 'left',
      disable: true,
    },
    {
      title: '姓名',
      dataIndex: 'staffName',
      fixed: 'left',
      disable: true,
    },
    {
      title: '位置',
      dataIndex: 'blockTag',
      disable: true,
      render: blockTag => {
        if (!blockTag) {
          return '';
        }
        return blockTag;
      },
    },
    {
      title: '类型',
      dataIndex: 'staffType',
      render: val => {
        return <UserTypeText userType={val} />;
      },
    },
    {
      title: '应出勤次数',
      dataIndex: 'totalAttendanceDay',
      sorter: (a, b) => Number(a.totalAttendanceDay ?? 0) - Number(b.totalAttendanceDay ?? 0),
    },
    {
      title: '实际出勤次数',
      dataIndex: 'attendanceDay',
      sorter: (a, b) => Number(a.attendanceDay ?? 0) - Number(b.attendanceDay ?? 0),
    },
    {
      title: '应出勤时长',
      dataIndex: 'standardWorkMinutes',
      sorter: (a, b) => Number(a.standardWorkMinutes ?? 0) - Number(b.standardWorkMinutes ?? 0),
      render: standardWorkMinutes => {
        if (typeof standardWorkMinutes != 'number' || standardWorkMinutes === 0) {
          return '0 小时';
        }
        const hours = Math.floor(standardWorkMinutes / 60);
        const minutes = standardWorkMinutes % 60;
        return `${hours}小时${minutes}分钟`;
      },
    },
    {
      title: '班内工作时长',
      dataIndex: 'workTime',
      sorter: (a, b) => Number(a.workTime ?? 0) - Number(b.workTime ?? 0),
      render: workTime => {
        if (typeof workTime != 'number' || workTime === 0) {
          return '0 小时';
        }
        const hours = Math.floor(workTime / 60);
        const minutes = workTime % 60;
        return `${hours}小时${minutes}分钟`;
      },
    },
    {
      title: '迟到次数',
      dataIndex: 'lateTimes',
      sorter: (a, b) => Number(a.lateTimes ?? 0) - Number(b.lateTimes ?? 0),
      render: lateTimes =>
        lateTimes > 0 ? (
          <Typography.Text type="danger">{lateTimes}</Typography.Text>
        ) : (
          lateTimes ?? '--'
        ),
    },
    {
      title: '迟到时长',
      dataIndex: 'lateOnMinutes',
      show: false,
      sorter: (a, b) => Number(a.lateOnMinutes ?? 0) - Number(b.lateOnMinutes ?? 0),
      render: lateOnMinutes => (lateOnMinutes >= 0 ? `${lateOnMinutes}分钟` : '--'),
    },

    {
      title: '早退次数',
      dataIndex: 'earlyOffTimes',
      sorter: (a, b) => Number(a.earlyOffTimes ?? 0) - Number(b.earlyOffTimes ?? 0),
      render: earlyOffTimes =>
        earlyOffTimes > 0 ? (
          <Typography.Text type="danger">{earlyOffTimes}</Typography.Text>
        ) : (
          earlyOffTimes ?? '--'
        ),
    },
    {
      title: '早退时长',
      dataIndex: 'earlyOffMinutes',
      show: false,
      sorter: (a, b) => Number(a.earlyOffMinutes ?? 0) - Number(b.earlyOffMinutes ?? 0),
      render: earlyOffMinutes => (earlyOffMinutes >= 0 ? `${earlyOffMinutes}分钟` : '--'),
    },

    {
      title: '上班缺卡次数',
      dataIndex: 'missOnCardTimes',
      sorter: (a, b) => Number(a.missOnCardTimes ?? 0) - Number(b.missOnCardTimes ?? 0),
      render: missOnCardTimes =>
        missOnCardTimes > 0 ? (
          <Typography.Text type="danger">{missOnCardTimes}</Typography.Text>
        ) : (
          missOnCardTimes ?? '--'
        ),
    },
    {
      title: '下班缺卡次数',
      dataIndex: 'missOffCardTimes',
      sorter: (a, b) => Number(a.missOffCardTimes ?? 0) - Number(b.missOffCardTimes ?? 0),
      render: missOffCardTimes =>
        missOffCardTimes > 0 ? (
          <Typography.Text type="danger">{missOffCardTimes}</Typography.Text>
        ) : (
          missOffCardTimes ?? '--'
        ),
    },
    {
      title: '旷工次数',
      dataIndex: 'absentTimes',
      sorter: (a, b) => Number(a.absentTimes ?? 0) - Number(b.absentTimes ?? 0),
      render: value =>
        value > 0 ? <Typography.Text type="danger">{value}</Typography.Text> : value ?? '--',
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      disable: true,
      render: (__, record) => {
        if (!stateRef.current.startDate || !stateRef.current.endDate) {
          return;
        }
        const searchParams = qs.stringify({
          startDate: stateRef.current.startDate,
          endDate: stateRef.current.endDate,
          blockTags: record.blockTag,
          staffId: record.staffId,
          staffName: record.staffName,
          searchDate: 'custom',
        });
        return (
          <Link
            to={{
              pathname: generateAttendanceStatisticsLocation({ byFunc: 'byDay' }),
              search: `?${searchParams}`,
            }}
          >
            查看
          </Link>
        );
      },
    },
  ];
};

export function AttStatisticsByMonth() {
  const { search } = useLocation();
  const { pageSize, page, statisticFilterField } = getLocationSearchMap<
    Partial<{ page: number; pageSize: number; statisticFilterField: AttStatisticsKey }>
  >(search, {
    parseNumbers: true,
  });
  const queryFieldsRef = useRef<SearchFields>({
    page: page ?? 1,
    pageSize: pageSize ?? 10,
    searchDate: 'thisMonth',
    location: { value: undefined },
    statisticFilterField: statisticFilterField ?? 'attStandardTimes',
  });
  const [form] = Form.useForm<FilterFormValue>();
  const [tableLoading, setTableLoading] = useState(false);
  const [data, setData] = useState<{ data: MonthlyAttStatistics[]; total: number }>({
    data: [],
    total: 0,
  });
  const [tableColumns, setTableColumns] = useState<ColumnType<MonthlyAttStatistics>[]>(
    generateBasicTableColumns(queryFieldsRef)
  );
  const [, { onLoadAttStatisticValue }] = useAttStatistic();

  const onLoadTableData = useCallback(async (params: QueryMonthlyAttStatisticsFields) => {
    setTableLoading(true);
    const { data, error } = await fetchPagedAttStatisticsByMonth(params);
    setTableLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(data);
  }, []);

  const onLoadData = useCallback(() => {
    setLocationSearch({
      ...queryFieldsRef.current,
      location: undefined,
      customDateRange: undefined,
      blockTags: queryFieldsRef.current.blockTags?.filter(
        guid => guid.split('.').length === 2 && guid.split('.')[0] !== guid.split('.')[1]
      ),
      emptyLocation:
        (queryFieldsRef.current.blockTags ?? []).length === 0 &&
        (queryFieldsRef.current.idcTags ?? []).length === 0
          ? 'true'
          : undefined,
    });
    onLoadTableData(transformParams(queryFieldsRef.current));
    onLoadAttStatisticValue({ ...queryFieldsRef.current, type: 'MONTHLY' });
  }, [onLoadTableData, onLoadAttStatisticValue]);

  return (
    <Card bordered={false} title="月度汇总">
      <Space style={{ width: '100%' }} direction="vertical" size={16}>
        <Space
          style={{ width: '100%', justifyContent: 'space-between', alignItems: 'flex-end' }}
          direction="horizontal"
          size={16}
        >
          <QueryFilter
            form={form}
            onSearch={resetPagination => {
              const filterFormValues = form.getFieldsValue();
              const timeRange = getTimeRange(
                filterFormValues.searchDate,
                filterFormValues.customDateRange
              );
              queryFieldsRef.current = {
                ...queryFieldsRef.current,
                ...filterFormValues,
                statisticFilterField: resetPagination
                  ? 'attStandardTimes'
                  : queryFieldsRef.current.statisticFilterField,
                staffId: filterFormValues.staffId?.value,
                staffName: filterFormValues.staffId?.label,
                startDate: timeRange[0],
                endDate: timeRange[1],
                idcTags: undefined,
                blockTags: filterFormValues.location
                  ? [
                      ...(filterFormValues.location.idcTags ?? []).map(idc => `${idc}.${idc}`),
                      ...(filterFormValues.location.blockTags ?? []),
                    ]
                  : undefined,
                page: resetPagination ? 1 : queryFieldsRef.current.page,
              };
              onLoadData();
            }}
          />
          <AttStatisticTableTools
            searchFields={transformParams(queryFieldsRef.current)}
            defaultTableColumns={tableColumns}
            tableColumns={tableColumns}
            onColumnsChange={value => {
              setTableColumns(value);
            }}
          />
        </Space>
        <AttStatisticsCard
          showColumns={[
            'attStandardTimes',
            'attActualTimes',
            'lateOnTimes',
            'earlyOffTimes',
            'missOnTimes',
            'missOffTimes',
            'absentTimes',
          ]}
          searchValue={queryFieldsRef.current.statisticFilterField}
          onClick={value => {
            queryFieldsRef.current.page = 1;
            queryFieldsRef.current.pageSize = 10;
            queryFieldsRef.current.statisticFilterField = value;
            onLoadData();
          }}
        />
        <Table
          loading={tableLoading}
          rowKey="id"
          scroll={{ x: 'max-content' }}
          dataSource={data.data}
          columns={tableColumns}
          pagination={{
            total: data.total,
            current: queryFieldsRef.current.page,
            pageSize: queryFieldsRef.current.pageSize,
          }}
          onChange={(pagination, _, sorter, { action }) => {
            if (action === 'paginate') {
              queryFieldsRef.current.page = pagination.current!;
              queryFieldsRef.current.pageSize = pagination.pageSize!;
            } else if (action === 'sort') {
              queryFieldsRef.current = {
                ...queryFieldsRef.current,
                page: 1,
                sortField: (sorter as TableSorter)?.order
                  ? (sorter as TableSorter)?.field
                  : undefined,
                sortType: (sorter as TableSorter)?.order,
              };
            }
            onLoadData();
          }}
        />
      </Space>
    </Card>
  );
}

export const AttStatisticsByMonthPage = () => {
  return (
    <AttStatisticProvider>
      <AttStatisticsByMonth />
    </AttStatisticProvider>
  );
};

const getTimeRange = (value: DateValue, customDateRang?: moment.Moment[]) => {
  let startDate: number = 0;
  let endDate: number = 0;
  switch (value) {
    case 'thisMonth':
      startDate = moment().startOf('month').valueOf();
      endDate = moment().endOf('month').valueOf();
      break;
    case 'lastMonth':
      startDate = moment().subtract(1, 'months').startOf('month').valueOf();
      endDate = moment().subtract(1, 'months').endOf('month').valueOf();
      break;
    case 'custom':
      startDate =
        customDateRang && customDateRang[0]
          ? customDateRang[0].startOf('day').valueOf() ?? 0
          : moment().startOf('month').valueOf();
      endDate =
        customDateRang && customDateRang[1]
          ? customDateRang[1].endOf('day').valueOf() ?? 0
          : moment().endOf('month').valueOf();
      break;
    default:
      break;
  }
  return [startDate, endDate];
};

const transformParams = (fields: SearchFields) => {
  return {
    ...fields,
    isAbsent:
      fields.statisticFilterField === 'absentTimes'
        ? true
        : fields.statisticFilterField === 'attActualTimes'
          ? false
          : undefined,
    isEarlyOff: fields.statisticFilterField === 'earlyOffTimes' ? true : undefined,
    isLateOn: fields.statisticFilterField === 'lateOnTimes' ? true : undefined,
    isMisOff: fields.statisticFilterField === 'missOffTimes' ? true : undefined,
    isMisOn: fields.statisticFilterField === 'missOnTimes' ? true : undefined,
    needLeave:
      fields.statisticFilterField &&
      ['attStandardTimes', 'attActualTimes'].includes(fields.statisticFilterField)
        ? 'FALSE'
        : undefined,
  } as QueryMonthlyAttStatisticsFields;
};
