import get from 'lodash.get';
import React, { useCallback, useContext, useState } from 'react';

import { Bar } from '@manyun/base-ui.chart.bar';
import { ThemeContext } from '@manyun/base-ui.chart.theme';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { PerformanceStaticIntervalInfos } from '@manyun/hrm.gql.client.hrm';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';

export function StatisticPeriodBar(params: {
  usersPeriodIntervalRecords: PerformanceStaticIntervalInfos[];
  chartLocations: string[];
  chartLeft: number;
  period: string;
  locationType: string;
}) {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const isShowExtra = params.chartLocations.length > 6;
  const handleModalOpen = useCallback(() => {
    setLoading(true);
    setIsModalOpen(prev => !prev);
    window.setTimeout(() => setLoading(false), 1000);
  }, []);
  const [loading, setLoading] = useState(false);
  const { period, locationType } = params;
  const title = `${period === 'YEAR' ? '年度' : period}考核状态分布（按${locationType === 'idc' ? '机房' : '区域'}）`;

  return (
    <Space style={{ width: '100%' }} direction="vertical" size={0}>
      {isShowExtra && (
        <Space style={{ display: 'flex', justifyContent: 'space-between' }}>
          <Typography.Title style={{ margin: '4px 0', fontSize: 14 }} level={5}>
            {title}
          </Typography.Title>
          <Button type="link" compact onClick={() => handleModalOpen()}>
            查看全部
          </Button>
        </Space>
      )}
      <PeriodBar {...params} isOuter isShowExtra={isShowExtra} title={title} />
      <Modal
        bodyStyle={{ maxHeight: 'calc(80vh)', overflowY: 'auto' }}
        width={1200}
        title="详情"
        open={isModalOpen}
        footer={null}
        onCancel={() => handleModalOpen()}
      >
        <Spin style={{ width: '100%', height: '100%' }} spinning={loading}>
          <PeriodBar {...params} isShowExtra={isShowExtra} title={title} />
        </Spin>
      </Modal>
    </Space>
  );
}

function PeriodBar({
  isOuter,
  isShowExtra,
  chartLeft,
  chartLocations,
  title,
  usersPeriodIntervalRecords,
}: {
  isOuter?: boolean;
  isShowExtra: boolean;
  usersPeriodIntervalRecords: PerformanceStaticIntervalInfos[];
  chartLocations: string[];
  chartLeft: number;
  title: string;
}) {
  const performanceLocales = getPerformanceLocales();
  const { json } = useContext(ThemeContext);
  const outerChartLocations = isShowExtra ? chartLocations.slice(0, 6) : chartLocations;
  const labelTextColor = get(json, ['common', 'labelTextColor']);

  return (
    <Bar
      variant="dashboard"
      opts={{ renderer: 'svg' }}
      style={{
        width: isOuter ? '100%' : 1152,
        height: 76 + (isOuter ? outerChartLocations.length : chartLocations.length) * 30,
      }}
      option={{
        color: [
          '#72CCFF',
          '#AFE56A',
          '#81F1AE',
          '#33DE96',
          '#00B578',
          '#1D8B52',
          '#006B4A',
          '#C7D0DD',
        ],
        tooltip: {
          trigger: 'item',
          textStyle: {
            fontSize: 12,
          },
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          formatter: function (params: any) {
            const name = params.seriesName;
            const percent: number = get(params, ['value', 0]);
            const value = get(params, ['value', 2]);
            const displayValue =
              percent < 5 ? `${value}(${percent.toFixed(0)}%)` : `${value}(${percent}%)`;
            return ` <span style="color:#a8a8a8;">${name}</span>：<b>${displayValue}</b>`;
          },
        },
        title: {
          show: !isOuter || !isShowExtra,
          text: title,
          left: '0%',
          textStyle: {
            fontSize: 14,
          },
        },
        grid: {
          left: chartLeft,
          top: isOuter && isShowExtra ? 0 : 32,
          right: 0,
        },
        legend: {
          left: 'center',
          top: 'bottom',
        },
        xAxis: { type: 'value', show: false, min: 0, max: 100 },
        yAxis: {
          type: 'category',
          inverse: true,
          splitLine: {
            show: false,
          },
          axisLabel: {
            // overflow: 'breakAll',
            width: chartLeft,
            fontSize: 14,
            color: labelTextColor,
          },
          data: isOuter ? outerChartLocations : chartLocations,
        },
        toolbox: {
          show: !isOuter || !isShowExtra,
          right: 20,
          feature: {
            saveAsImage: {},
          },
        },
        series: usersPeriodIntervalRecords.map(record => {
          return {
            name: performanceLocales.pfType.test.evaluationStatus.enum[record.key] ?? '待提交目标',
            stack: 'bar',
            type: 'bar',
            barWidth: 12,
            itemStyle: {
              borderWidth: 1,
              borderColor: 'white',
            },
            label: {
              show: true,
              color: 'white',
              formatter: function (params: object) {
                const percent: number = get(params, ['value', 0]);
                return percent < 5
                  ? `${percent.toFixed(0)}%`
                  : `${get(params, ['value', 2])}(${percent}%)`;
              },
            },
            data: record.data.map(record => [record?.percent!, record?.label!, record?.value!]),
          };
        }),
      }}
    />
  );
}
