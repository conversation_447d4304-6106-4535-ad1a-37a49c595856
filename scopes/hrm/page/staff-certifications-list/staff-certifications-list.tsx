/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-4
 *
 * @packageDocumentation
 */
import { useApolloClient } from '@apollo/client';
import { omit } from 'lodash-es';
import moment from 'moment';
import { nanoid } from 'nanoid';
import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { exportStaffCertificationsList } from '@manyun/hrm.service.export-staff-certifications-list';
import type {
  ApiArgs,
  StaffCertInfos,
} from '@manyun/hrm.service.fetch-paged-staff-certifications-list';
import { fetchPagedStaffCertificationsList } from '@manyun/hrm.service.fetch-paged-staff-certifications-list';
import { fetchStaffCertificationsStaticCount } from '@manyun/hrm.service.fetch-staff-certifications-static-count';
import type { StaticCount } from '@manyun/hrm.service.fetch-staff-certifications-static-count';
import { UserLink } from '@manyun/iam.ui.user-link';
import { readSpace, useSpaces } from '@manyun/resource-hub.gql.client.spaces';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

import { StyledCard, TableStyle } from './styles';

export type StaffCertificationsListProps = {};

type Filed = {
  staffName?: string;
  blockGuid?: string[];
  position?: string;
  certificationName?: string;
  orderByJobNoDesc?: boolean;
  certStatus?: string;
};

const useStaffCertifications = () => {
  const [loading, setLoading] = React.useState(false);
  const [exportLoading, setExportLoading] = React.useState(false);
  const [staticLoading, setStaticLoading] = React.useState(false);

  const [data, setData] = React.useState<{
    data: StaffCertInfos[];
    total: number;
  }>({
    data: [],
    total: 0,
  });
  const [staticCount, setStaticCount] = React.useState<StaticCount | null>(null);

  const transformParams = React.useCallback((params: Partial<Filed>) => {
    return {
      userName: params.staffName,
      positionCode: params.position,
      certName: params.certificationName,
      jobNumberSort:
        params.orderByJobNoDesc !== undefined
          ? params.orderByJobNoDesc
            ? 'DESC'
            : 'ASC'
          : undefined,
      blockGuidList: params.blockGuid
        ? params.blockGuid?.filter(item => item.split('.')[1])
        : undefined,
      certEffectStatus:
        params.certStatus && ['valid', 'invalid'].includes(params.certStatus)
          ? params.certStatus === 'valid'
            ? 1
            : 0
          : undefined,
      uploadStatus:
        params.certStatus && ['uploaded', 'pending'].includes(params.certStatus)
          ? params.certStatus === 'uploaded'
            ? 1
            : 0
          : undefined,
      instStatus: params.certStatus && params.certStatus === 'approving' ? 'APPROVING' : undefined,
      willExpire: params.certStatus
        ? params.certStatus === 'atExpired'
          ? 1
          : undefined
        : undefined,
      willCheck: params.certStatus ? (params.certStatus === 'atCheck' ? 1 : undefined) : undefined,
    } as ApiArgs;
  }, []);

  const onLoadData = React.useCallback(
    async (params: Filed) => {
      setLoading(true);
      const { error, data } = await fetchPagedStaffCertificationsList(transformParams(params));
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setData(data);
    },
    [transformParams]
  );

  const onLoadStaticCount = React.useCallback(
    async (params: Partial<Filed>) => {
      setStaticLoading(true);
      const _params = transformParams(params);
      const { error, data } = await fetchStaffCertificationsStaticCount({
        userName: _params.userName,
        positionCode: _params.positionCode,
        certName: _params.certName,
        blockGuidList: _params.blockGuidList,
      });
      setStaticLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setStaticCount(data);
    },
    [transformParams]
  );

  const onExport = React.useCallback(
    async (params: Partial<Filed>) => {
      setExportLoading(true);
      const _params = transformParams(params);
      const { error, data } = await exportStaffCertificationsList(
        omit(_params, ['pageNum', 'pageSize'])
      );
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      return data;
    },
    [transformParams]
  );

  return [
    { data, loading, exportLoading, staticCount, staticLoading },
    { onLoadData, onExport, onLoadStaticCount },
  ] as const;
};

export function StaffCertificationsList() {
  const client = useApolloClient();

  const [form] = Form.useForm();
  const [fields, setFields] = React.useState<Filed>({});

  const [
    { loading, data, exportLoading, staticCount, staticLoading },
    { onExport, onLoadData, onLoadStaticCount },
  ] = useStaffCertifications();

  React.useEffect(() => {
    onLoadData({});
    onLoadStaticCount({});
  }, [onLoadData, onLoadStaticCount]);

  const onReloadData = React.useCallback(
    (params: Partial<Filed>) => {
      onLoadData({ ...fields, ...params });
      onLoadStaticCount({ ...fields, ...params });
    },
    [fields, onLoadData, onLoadStaticCount]
  );

  useSpaces({
    variables: {
      nodeTypes: ['IDC', 'BLOCK'],
    },
    fetchPolicy: 'network-only',
  });

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          员工资质证书档案
        </Typography.Title>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Form form={form}>
            <Space size="middle">
              <Form.Item style={{ marginBottom: 0 }} label="姓名" name="staffName">
                <Input style={{ width: 240 }} allowClear />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }} label="所属楼栋" name="blockGuid">
                <LocationCascader
                  style={{ width: 170 }}
                  changeOnSelect={false}
                  showSearch
                  allowClear
                  authorizedOnly
                  disabledNoChildsNodes={['IDC']}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }} label="岗位" name="position">
                <MetaTypeSelect
                  style={{ width: 160 }}
                  metaType={'POSITION_YG' as any}
                  showSearch
                  allowClear
                  filterOption={(input: string, option?: { label: string; value: string }) => {
                    if (!option?.label) {
                      return false;
                    }
                    return option.label.toLowerCase().includes(input.toLowerCase());
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }} label="证书" name="certificationName">
                <Input style={{ width: 200 }} allowClear />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <Space>
                  <Button
                    type="primary"
                    onClick={() => {
                      const { position, certificationName, blockGuid, staffName } =
                        form.getFieldsValue();
                      onReloadData({
                        position,
                        certificationName,
                        staffName,
                        blockGuid,
                      });
                      setFields(pre => ({
                        ...pre,
                        position,
                        certificationName,
                        staffName,
                        blockGuid,
                      }));
                    }}
                  >
                    搜索
                  </Button>
                  <Button
                    onClick={() => {
                      form.resetFields();
                      onReloadData({
                        position: undefined,
                        certificationName: undefined,
                        staffName: undefined,
                        blockGuid: undefined,
                      });
                      setFields(pre => ({
                        ...pre,
                        position: undefined,
                        certificationName: undefined,
                        staffName: undefined,
                        blockGuid: undefined,
                      }));
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Space>
          </Form>
          <FileExport
            text=""
            filename="员工资质证书档案"
            disabled={exportLoading}
            data={() => {
              return onExport(fields);
            }}
          />
        </Space>
        <Spin spinning={staticLoading}>
          <StyledCard>
            <div style={{ width: '100%', display: 'flex', gap: '16px' }}>
              {[
                {
                  key: 'uploaded',
                  title: '已上传证书',
                  value: staticCount?.uploadNum ?? '--',
                  valueText: 'link',
                },
                {
                  key: 'valid',
                  title: '有效证书',
                  valueText: 'success',
                  value: staticCount?.effectNum ?? '--',
                },
                {
                  key: 'invalid',
                  title: '无效证书',
                  valueText: 'danger',
                  value: staticCount?.unEffectNum ?? '--',
                },
                {
                  key: 'approving',
                  title: '审批中',
                  valueText: 'link',
                  value: staticCount?.approvingNum ?? '--',
                },
                {
                  key: 'pending',
                  title: '待上传',
                  value: staticCount?.waitedUploadNum ?? '--',
                  valueText: 'warning',
                },
                {
                  key: 'atExpired',
                  title: '即将到期',
                  value: staticCount?.willExpireNum ?? '--',
                  valueText: 'warning',
                },
                {
                  key: 'atCheck',
                  title: '即将复审',
                  value: staticCount?.willCheckNum ?? '--',
                  valueText: 'warning',
                },
              ].map(card => (
                <Card
                  key={card.key}
                  className={fields.certStatus === card.key ? 'selectedCard' : ''}
                  style={{
                    cursor: 'pointer',
                    flex: 1,
                    borderRadius: 8,
                  }}
                  hoverable
                  size="small"
                  onClick={() => {
                    onLoadData({
                      ...fields,
                      certStatus:
                        fields.certStatus && fields.certStatus === card.key ? undefined : card.key,
                    });
                    setFields(pre => ({
                      ...pre,
                      certStatus:
                        pre.certStatus && pre.certStatus === card.key ? undefined : card.key,
                    }));
                  }}
                >
                  <Space direction="vertical">
                    <Typography.Text type="secondary">{card.title}</Typography.Text>
                    {card.valueText === 'link' ? (
                      <Typography.Link
                        style={{
                          fontSize: 28,
                        }}
                      >
                        {card.value}
                      </Typography.Link>
                    ) : (
                      <Typography.Text
                        style={{
                          fontSize: 28,
                        }}
                        type={card.valueText as 'success' | 'danger' | 'warning'}
                      >
                        {card.value}
                      </Typography.Text>
                    )}
                  </Space>
                </Card>
              ))}
            </div>
          </StyledCard>
        </Spin>
        <TableStyle>
          <Table<StaffCertInfos>
            scroll={{ x: 'max-content' }}
            rowKey="userId"
            loading={loading}
            columns={[
              {
                title: '工号',
                dataIndex: 'jobNumber',
                render: val => val ?? '--',
                fixed: 'left',
                sorter: true,
              },
              {
                title: '姓名',
                dataIndex: 'userName',
                fixed: 'left',
                render: (_, record) => (
                  <UserLink
                    target="_blank"
                    userId={Number(record.userId)}
                    userName={record.userName}
                  />
                ),
              },
              {
                title: '所属楼栋',
                dataIndex: 'blockGuid',
                className: 'max-width-content',
                ellipsis: true,
                render: (_, record) => {
                  const blockGuids = record.blockGuids?.split('|');
                  const ctx =
                    (blockGuids ?? []).length > 0
                      ? (blockGuids ?? [])
                          .map(guid => {
                            const blockLabel = readSpace(client, guid);
                            return blockLabel?.label;
                          })
                          .join('｜')
                      : '--';
                  return ctx;
                },
              },
              {
                title: '岗位',
                dataIndex: 'position',
              },
              {
                title: '资质证书',
                dataIndex: 'positionCertDoList',
                render: (_, { positionCertDoList }) => {
                  return (
                    <Space size={[8, 8]} wrap>
                      {(positionCertDoList ?? []).map(cert => {
                        const status = getCertificationStatus(cert);
                        const validTime =
                          cert.effectEndDate && cert.effectStartDate
                            ? `${moment(cert.effectStartDate).format('YYYY-MM-DD')} ~ ${moment(cert.effectEndDate).format('YYYY-MM-DD')}`
                            : undefined;
                        return (
                          <Container
                            key={nanoid()}
                            style={{
                              borderRadius: '4px',
                              padding: '4px 8px',
                              backgroundColor:
                                fields.certificationName &&
                                cert.certName.includes(fields.certificationName)
                                  ? '#E6F7FF'
                                  : undefined,
                            }}
                            color="default"
                          >
                            <Badge
                              style={{ marginRight: 8 }}
                              color={CertificationStatusColor[status]}
                            />
                            <Tooltip
                              title={
                                <>
                                  {`${
                                    CertificationStatusText[status]
                                  }${validTime && ['valid', 'invalid'].includes(status) ? `:${validTime}` : ''}`}
                                </>
                              }
                            >
                              <Typography.Text>{cert.certName}</Typography.Text>
                            </Tooltip>
                          </Container>
                        );
                      })}
                    </Space>
                  );
                },
              },
            ]}
            dataSource={data.data}
            onChange={(_, __, sorter, { action }) => {
              if (action === 'sort') {
                const { order } = sorter as { order: 'ascend' | 'descend' | undefined };
                setFields(prev => ({
                  ...prev,
                  orderByJobNoDesc:
                    order === 'descend' ? true : order === 'ascend' ? false : undefined,
                  page: 1,
                }));
                onLoadData({
                  ...fields,
                  orderByJobNoDesc:
                    order === 'descend' ? true : order === 'ascend' ? false : undefined,
                });
              }
            }}
          />
        </TableStyle>
      </Space>
    </Card>
  );
}

enum CertificationStatus {
  /** 有效 */
  VALID = 'valid',
  /** 无效 */
  INVALID = 'invalid',
  /** 待上传 */
  PENDING = 'pending',
  /** 待审批 */
  TO_BE_APPROVED = 'approving',
}

const CertificationStatusColor: Record<string, string> = {
  [CertificationStatus.VALID]: 'green',
  [CertificationStatus.INVALID]: 'red',
  [CertificationStatus.PENDING]: 'orange',
  [CertificationStatus.TO_BE_APPROVED]: 'blue',
};

const CertificationStatusText: Record<string, string> = {
  [CertificationStatus.VALID]: '有效',
  [CertificationStatus.INVALID]: '无效',
  [CertificationStatus.PENDING]: '待上传',
  [CertificationStatus.TO_BE_APPROVED]: '审批中',
};

const getCertificationStatus = (cert: any): string => {
  if (cert.instStatus === 'APPROVING') {
    return 'approving';
  }
  if (cert.uploadStatus === 0) {
    return 'pending';
  }
  if (cert.certEffectStatus === 0) {
    return 'invalid';
  }
  if (cert.certEffectStatus === 1) {
    return 'valid';
  }
  return '';
};
