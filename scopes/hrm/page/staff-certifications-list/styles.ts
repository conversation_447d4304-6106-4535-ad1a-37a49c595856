import styled, { css } from '@manyun/dc-brain.theme.theme';

export const TableStyle = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-table-content .${prefixCls}-table-tbody {
        .max-width-content {
          max-width: 256px;
        }
      }
    `;
  }}
`;

export const StyledCard = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .${prefixCls}-card {
        &.selectedCard {
          border: 1px solid var(--border-color-base);
          box-shadow: ${props.theme.tokens.boxShadowCard};
        }
      }
    `;
  }}
`;
