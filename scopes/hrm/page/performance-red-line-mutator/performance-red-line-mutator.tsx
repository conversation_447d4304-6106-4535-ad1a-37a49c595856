/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-8-7
 *
 * @packageDocumentation
 */
import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import groupby from 'lodash.groupby';
import moment from 'moment';
import React, { useState } from 'react';
import { useHistory, useParams } from 'react-router';

import { fetchUser } from '@manyun/auth-hub.service.fetch-user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import {
  useLazyAnnualPerformanceObjective,
  useLazyPerformanceGrade,
} from '@manyun/hrm.gql.client.hrm';
import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';
import type { AnnualPerformanceObjectiveEditParams } from '@manyun/hrm.route.hrm-routes';
import { createPerformanceDailyGrade } from '@manyun/hrm.service.create-performance-daily-grade';
import { fetchUserBindPerformancePositionByYear } from '@manyun/hrm.service.fetch-user-bind-performance-position-by-year';
import { updatePerformanceDailyGrade } from '@manyun/hrm.service.update-performance-daily-grade';
import { whetherCanAddDailyPerformanceGrade } from '@manyun/hrm.service.whether-can-add-daily-performance-grade';
import { PerformanceUserSelect } from '@manyun/hrm.ui.performance-user-select';

import { RedLineObjectivesSelect } from './components/red-line-objectives-select.js';

const RED_LINE_TYPE_KEY: BackendAnnualPerformanceObjectiveType = 'RED_LINE';
const formItem = {
  labelCol: {
    span: 8,
  },
  wrapperCol: {
    span: 20,
  },
};

type GradeCriteria = {
  id: number;
  defaultGrade: number[];
  gradeCriteria: string;
};
export function PerformanceRedLineMutator() {
  const params = useParams<AnnualPerformanceObjectiveEditParams>();
  const { id } = params;

  const [form] = Form.useForm();
  const history = useHistory();
  const user = Form.useWatch('user', form);
  const occurTime = Form.useWatch('occurTime', form);
  const attachments = Form.useWatch('attachments', form);

  const [validateCanAddErrorCode, setValidateCanAddErrorCode] = useState<number>(0);
  const [userEvalPosition, setUserEvalPosition] = useState<string | undefined>(undefined);
  const [userHireDate, setUserHireDate] = useState<number | undefined>(undefined);
  const [preOccurTime, setPreOccurTime] = useState<moment.Moment | null>(null);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [gradeCriteriaList, setGradeCriteriaList] = useState<{ id: number; context: string }[]>([]);

  const [fetchRelatedObjective] = useLazyAnnualPerformanceObjective();
  const [fetchDetail, { loading }] = useLazyPerformanceGrade();

  /**编辑时查询详情 */
  React.useEffect(() => {
    if (id) {
      fetchDetail({ variables: { id: Number(id) } })
        .then(result => {
          if (result.data?.performanceGradeById) {
            const record = result.data.performanceGradeById;
            form.setFieldsValue({
              ...record,
              user: {
                label: record.user.name,
                value: record.user.id,
              },
              occurTime: moment(record.occurTime),
              name: {
                label: record.name,
                value: record.relatedObjectiveId,
              },
              grade: Math.abs(record.grade),
            });
            fetchRelatedObjective({
              variables: { id: result.data.performanceGradeById.relatedObjectiveId },
            }).then(res => {
              const objectiveRecord = res.data?.annualPerformanceObjectiveById;
              if (objectiveRecord) {
                const _mergedMeasurements = groupby(objectiveRecord.measurements, 'context');
                const gradeCriteriaList = _mergedMeasurements[record.measurements];
                setGradeCriteriaList(
                  gradeCriteriaList.map(item => ({ id: item.id!, context: item.gradeCriteria! }))
                );
                form.setFieldsValue({
                  /**指标名称 */
                  name: {
                    measurements: objectiveRecord.measurements,
                    label: objectiveRecord.name,
                    value: objectiveRecord.id,
                  },
                  /**衡量标准 */
                  measurements: {
                    gradeCriteriaList: gradeCriteriaList,
                    label: record.measurements,
                    value: record.measurements,
                  },
                  /**评分标准 */
                  gradeCriteria: record.gradeCriteria.map(item => item.id),
                });
              }
            });
          }
        })
        .catch(console.error);
    }
  }, [fetchDetail, fetchRelatedObjective, form, id]);

  React.useEffect(() => {
    //获取用户入职日期
    if (user?.value) {
      (async () => {
        const { error, data } = await fetchUser({ id: user.value, needValid: false });
        if (error) {
          message.error(error.message);
          return;
        }
        setUserHireDate(data?.hiredDate);
      })();
    }
  }, [id, user?.value]);

  React.useEffect(() => {
    //判断用户是否可以添加红线指标
    if (user && user.value && occurTime) {
      (async () => {
        const { error, data } = await whetherCanAddDailyPerformanceGrade({
          staffId: user.value,
          occurTime: moment(occurTime).startOf('day').valueOf(),
          type: RED_LINE_TYPE_KEY,
        });
        if (error) {
          message.error(error.message);
          return;
        }
        setValidateCanAddErrorCode(data);
        form.validateFields(['occurTime']);
        const { error: err, data: evalPosition } = await fetchUserBindPerformancePositionByYear({
          userId: user.value,
          year: moment(occurTime).year().toString(),
        });

        if (err) {
          message.error(err.message);
          return;
        }
        setUserEvalPosition(evalPosition ?? undefined);
      })();
    }
  }, [form, occurTime, user]);

  const onSubmit = React.useCallback(() => {
    form
      .validateFields()
      .then(async values => {
        setSubmitLoading(true);
        const _values = {
          type: RED_LINE_TYPE_KEY,
          measurements: values.measurements?.value,
          subType: 'BIZ' as BackendAnnualPerformanceObjectiveSubType,
          staffId: values.user.value,
          staffName: values.user.label,
          relatedObjectiveId: values.name.value,
          name: values.name.label,
          occurTime: moment(values.occurTime).startOf('day').valueOf(),
          grade: 0,
          attachments: values.attachments,
          gradeCriteria: values.gradeCriteria.map((id: number) => ({
            id,
            criteria: gradeCriteriaList.find(item => item.id === id)?.context,
            defaultGrade: 0,
          })),
          gradeDesc: values.gradeDesc,
        };
        const { error } = id
          ? await updatePerformanceDailyGrade({ ..._values, id: Number(id) })
          : await createPerformanceDailyGrade(_values);
        setSubmitLoading(false);

        if (error) {
          message.error(error.message);
          return;
        }
        message.success(`${id ? '修改' : '添加'}红线记录成功`);
        history.goBack();
      })
      .catch(console.error);
  }, [form, gradeCriteriaList, history, id]);

  const uploadLoading = useDeepCompareMemo(() => {
    return (attachments ?? []).some(
      (attachment: { status: string }) => attachment.status === 'uploading'
    );
  }, [attachments]);

  return (
    <Card loading={loading}>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          {id ? '修改' : '新增'}红线记录
        </Typography.Title>
        <Form form={form} {...formItem}>
          <Form.Item
            name="user"
            label="人员"
            rules={[
              {
                required: true,
                message: `人员必填`,
              },
            ]}
          >
            <PerformanceUserSelect
              style={{ width: 210 }}
              includeCurrentUser={false}
              labelInValue
              disabled={!!id}
              onChange={() => {
                setGradeCriteriaList([]);
                form.setFieldsValue({
                  occurTime: undefined,
                  name: undefined,
                  measurements: undefined,
                });
              }}
            />
          </Form.Item>
          <Form.Item
            name="occurTime"
            label="发生日期"
            rules={[
              {
                required: true,
                validator: (_, value) => {
                  if (!value) {
                    return Promise.reject(new Error(`发生日期必选`));
                  }
                  if (validateCanAddErrorCode === 1 && value) {
                    return Promise.reject(
                      new Error(
                        `该员工${occurTime.format('YYYY')}年度目标暂未审批通过，请通过后再添加评分`
                      )
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <DatePicker
              style={{ width: 210 }}
              disabledDate={current =>
                current &&
                ((userHireDate && current.diff(moment(userHireDate).startOf('day')) < 0) ||
                  /** 仅可选当日和历史日期（最早可选到上一自然年的1.1日），前年和未来日期置灰不可选*/
                  !current.isBetween(
                    moment().subtract(1, 'year').startOf('year'),
                    moment().endOf('date')
                  ) ||
                  /**编辑时不可跨年 */
                  (!!id && !current.isBetween(moment().startOf('year'), moment().endOf('year'))))
              }
              onChange={val => {
                /**跨年清空指标内容 */
                if (val && preOccurTime && !val.isSame(preOccurTime, 'year')) {
                  form.setFieldsValue({ name: undefined, measurements: undefined });
                }
                setPreOccurTime(val);
              }}
            />
          </Form.Item>
          <Form.Item
            shouldUpdate={(pre, next) => pre.type !== next.type || pre.occurTime !== next.occurTime}
            noStyle
          >
            {() => {
              return (
                <Form.Item
                  name="name"
                  label="红线维度"
                  rules={[
                    {
                      required: true,
                      message: `红线维度必选`,
                    },
                  ]}
                >
                  <RedLineObjectivesSelect
                    style={{ width: 344 }}
                    trigger="onFocus"
                    showSearch
                    labelInValue
                    type={RED_LINE_TYPE_KEY}
                    disabled={!user || !occurTime}
                    staffId={user?.value}
                    occurTime={occurTime ? moment(occurTime).startOf('day').valueOf() : undefined}
                    performancePosition={userEvalPosition}
                    filterOption={(input, option) =>
                      (option?.label as string).toLowerCase().includes(input.toLowerCase())
                    }
                    onChange={value => {
                      const _mergedMeasurements = value?.measurements
                        ? groupby(value.measurements, 'context')
                        : {};
                      /**
                       *仅有一条衡量标准，则默认选中该条
                       */
                      const keys = Object.keys(_mergedMeasurements);
                      if (keys.length === 1) {
                        const gradeCriteriaList: GradeCriteria[] = _mergedMeasurements[keys[0]];
                        form.setFieldsValue({
                          measurements: {
                            gradeCriteriaList: gradeCriteriaList,
                            value: keys[0],
                            label: keys[0],
                          },
                        });
                        if (gradeCriteriaList.length === 1) {
                          form.setFieldValue('gradeCriteria', [gradeCriteriaList[0].id]);
                        }
                        setGradeCriteriaList(
                          gradeCriteriaList.map(item => ({
                            id: item.id,
                            context: item.gradeCriteria,
                          }))
                        );
                        /*只对应一条评分标准，则默认选中置灰不可修改 */
                      } else {
                        form.setFieldsValue({ measurements: undefined, gradeCriteria: undefined });
                        setGradeCriteriaList([]);
                      }
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item shouldUpdate={(pre, next) => pre.name !== next.name} noStyle>
            {({ getFieldValue }) => {
              const _mergedMeasurements = getFieldValue('name')?.measurements
                ? groupby(getFieldValue('name').measurements, 'context')
                : {};

              return (
                <Form.Item
                  name="measurements"
                  label="衡量标准"
                  getValueFromEvent={(value, _option) => {
                    return typeof value === 'object'
                      ? {
                          ...value,
                          gradeCriteriaList: _option.gradeCriteriaList,
                        }
                      : {
                          value,
                          gradeCriteriaList: _option.gradeCriteriaList,
                        };
                  }}
                  rules={[
                    {
                      required: true,
                      message: `衡量标准必选`,
                    },
                  ]}
                >
                  <Select
                    style={{ width: 766 }}
                    labelInValue
                    disabled={!getFieldValue('name')}
                    options={Object.keys(_mergedMeasurements).map(context => ({
                      gradeCriteriaList: _mergedMeasurements[context],
                      value: context,
                      label: context,
                    }))}
                    onChange={(_, option) => {
                      const _option = option as {
                        gradeCriteriaList: GradeCriteria[];
                      };

                      /**
                       * 当仅有一条评分标准需要默认选中该评分标准
                       */
                      if ((_option.gradeCriteriaList ?? []).length === 1) {
                        form.setFieldValue('gradeCriteria', [_option.gradeCriteriaList[0].id]);
                      } else {
                        form.setFieldValue('gradeCriteria', undefined);
                      }
                      setGradeCriteriaList(
                        (_option.gradeCriteriaList ?? []).map(item => ({
                          id: item.id,
                          context: item.gradeCriteria,
                        }))
                      );
                    }}
                  />
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item
            shouldUpdate={(pre, next) =>
              pre.name !== next.name || pre.measurements !== next.measurements
            }
            noStyle
          >
            {({ getFieldValue }) => {
              const measurements = getFieldValue('measurements');
              if (!measurements) {
                return null;
              }
              const data: GradeCriteria[] = measurements?.gradeCriteriaList ?? [];
              return (
                <Form.Item
                  name="gradeCriteria"
                  label="评分标准"
                  rules={[
                    {
                      required: true,
                      message: `评分标准必选`,
                    },
                  ]}
                >
                  <Checkbox.Group>
                    <Space style={{ width: '100%', maxWidth: '766px' }} direction="vertical">
                      {data.map(item => {
                        return (
                          <Checkbox key={item.id} value={item.id} disabled={data.length === 1}>
                            <div style={{ whiteSpace: 'pre-line', color: 'var(--text-color)' }}>
                              {item.gradeCriteria}
                            </div>
                          </Checkbox>
                        );
                      })}
                    </Space>
                  </Checkbox.Group>
                </Form.Item>
              );
            }}
          </Form.Item>
          <Form.Item
            name="gradeDesc"
            label="红线行为描述"
            rules={[
              {
                required: true,
                whitespace: true,
                message: `红线行为描述必填`,
              },
              {
                max: 300,
                whitespace: true,
                message: '最多输入 300 个字符！',
              },
            ]}
          >
            <Input.TextArea
              style={{ width: 584 }}
              autoSize={{ minRows: 3, maxRows: 5 }}
              maxLength={301}
            />
          </Form.Item>
          <Form.Item
            name="attachments"
            label="附件"
            wrapperCol={{ span: 16 }}
            valuePropName="fileList"
            getValueFromEvent={value => {
              if (typeof value === 'object') {
                return value.fileList;
              }
            }}
          >
            <Upload
              accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
              showUploadList
              allowDelete
              maxFileSize={20}
              maxCount={5}
              showAccept
              disabled={(attachments ?? []).length >= 5}
            >
              <Button disabled={(attachments ?? []).length >= 5} icon={<UploadOutlined />}>
                点此上传
              </Button>
            </Upload>
          </Form.Item>
          <Form.Item wrapperCol={{ offset: 8, span: 16 }}>
            <Space size="middle">
              <Button
                type="primary"
                loading={submitLoading}
                disabled={uploadLoading}
                onClick={() => {
                  onSubmit();
                }}
              >
                提交
              </Button>
              <Button
                disabled={submitLoading || uploadLoading}
                onClick={() => {
                  history.goBack();
                }}
              >
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Space>
    </Card>
  );
}
