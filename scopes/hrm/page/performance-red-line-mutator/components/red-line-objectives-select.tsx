import React, { useEffect } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { useLazyAnnualPerformanceObjectivesByPlan } from '@manyun/hrm.gql.client.hrm';
import type { AnnualPerformanceObjectiveJSON } from '@manyun/hrm.model.annual-performance-objective';

type LabelInValue = AnnualPerformanceObjectiveJSON & { value: string; label: string };

export type RedLineObjectivesSelectProps = {
  type: string;
  trigger?: 'onFocus' | 'onDidMount';
  staffId?: number;
  occurTime?: number;
  performancePosition?: string;
  onChange?: (value: string | LabelInValue) => void;
} & SelectProps;

export const RedLineObjectivesSelect = ({
  trigger = 'onDidMount',
  labelInValue = true,
  staffId,
  occurTime,
  type,
  performancePosition,
  onChange,
  ...restProps
}: RedLineObjectivesSelectProps) => {
  const [fetch, { loading, data }] = useLazyAnnualPerformanceObjectivesByPlan();

  useEffect(() => {
    if (trigger === 'onDidMount' && staffId && occurTime && performancePosition) {
      fetch({
        variables: {
          query: {
            staffId,
            planTime: occurTime,
            performancePosition,
            type,
          },
        },
      });
    }
  }, [fetch, occurTime, performancePosition, staffId, trigger, type]);

  const changeHandler = (value: string | LabelInValue) => {
    if (!labelInValue || value === undefined) {
      onChange?.(value);
      return;
    }
    if (typeof value === 'object') {
      const record = (data?.annualPerformanceObjectivesByPlan ?? []).find(
        obj => obj.id.toString() === value.value.toString()
      );
      onChange?.({ ...value, ...record } as LabelInValue);
    }
  };

  return (
    <Select
      loading={loading}
      {...restProps}
      labelInValue={labelInValue}
      options={data?.annualPerformanceObjectivesByPlan?.map(({ id, name }) => ({
        label: name,
        value: id,
      }))}
      onFocus={() => {
        if (trigger === 'onFocus' && staffId && occurTime) {
          fetch({
            variables: {
              query: {
                staffId,
                planTime: occurTime,
                type,
                performancePosition,
              },
            },
          });
        }
      }}
      onChange={changeHandler}
    />
  );
};
