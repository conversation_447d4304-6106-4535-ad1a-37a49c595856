/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-11
 *
 * @packageDocumentation
 */
import React, { useMemo, useState } from 'react';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Typography } from '@manyun/base-ui.ui.typography';

import { User as UserAvatar } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import type { HolidayBalance } from '@manyun/hrm.gql.client.hrm';
import { useLazyHolidayBalanceCalculate } from '@manyun/hrm.gql.client.hrm';

export function HolidayBalanceCalculate() {
  const [form] = Form.useForm();
  const user = Form.useWatch('user', form);
  const [holidayBalanceCalculate, { loading, data }] = useLazyHolidayBalanceCalculate();
  const [empty, setEmpty] = useState(false);

  const balanceInfos = useMemo(() => {
    if (empty) {
      return null;
    }
    return data?.holidayBalanceCalculate;
  }, [data?.holidayBalanceCalculate, empty]);

  return (
    <Card style={{ height: '100%' }} bodyStyle={{ display: 'flex', justifyContent: 'center' }}>
      <Space style={{ width: 1024 }} direction="vertical" size={48}>
        <Space style={{ width: '100%' }} direction="vertical" size="large">
          <Typography.Title level={5} showBadge>
            假期额度测算工具
          </Typography.Title>
          <Form layout="inline" form={form}>
            <Form.Item
              label="姓名"
              name="user"
              rules={[
                {
                  required: true,
                  message: '请选择员工',
                },
              ]}
            >
              <UserSelect style={{ width: 160 }} userType="STAFF" />
            </Form.Item>
            <Form.Item
              label="离职日期"
              name="leaveDate"
              rules={[
                {
                  required: true,
                  message: '请选择离职日期',
                },
              ]}
            >
              <DatePicker
                style={{ width: 160 }}
                disabledDate={current => {
                  const disabled =
                    current && !current.isBetween(moment().startOf('year'), moment().endOf('year'));
                  if (user && user.hiredDate) {
                    return disabled || user.hiredDate > current;
                  }
                  return disabled;
                }}
                onChange={value => {
                  if (value) {
                    form.setFieldValue('year', value);
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              label="预测年份"
              name="year"
              rules={[
                {
                  required: true,
                  message: '请选择测算年份',
                },
              ]}
            >
              <DatePicker
                style={{ width: 160 }}
                picker="year"
                disabled
                disabledDate={current => {
                  return (
                    current &&
                    !current.isBetween(moment().subtract(1, 'year'), moment().add(1, 'year'))
                  );
                }}
              />
            </Form.Item>
            <Form.Item>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    form.validateFields().then(async values => {
                      if (!user.hiredDate || !user.joinWorkingDate) {
                        message.error('员工入职日期或参加工作日期为空，无法测算!');
                        setEmpty(true);
                        return;
                      }
                      const { error } = await holidayBalanceCalculate({
                        variables: {
                          staffId: user.id,
                          hiredDate: moment(user.hiredDate).format('YYYY-MM-DD'),
                          joinWorkingTime: moment(user.joinWorkingDate).format('YYYY-MM-DD'),
                          leaveDate: moment(values.leaveDate).format('YYYY-MM-DD'),
                        },
                      });
                      if (error) {
                        setEmpty(true);
                      } else {
                        setEmpty(false);
                      }
                    });
                  }}
                >
                  开始测算
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setEmpty(true);
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
        </Space>
        <Space style={{ width: 900 }} direction="vertical" size="large">
          <Typography.Title level={5} showBadge>
            测算结果
          </Typography.Title>
          {user && (
            <Space size="large">
              <UserAvatar
                style={{ display: 'flex', flexDirection: 'row' }}
                id={user.id}
                size={24}
                editable={false}
              />
              <Space size="small">
                <Typography.Text type="secondary">入职日期：</Typography.Text>
                <Typography.Text>
                  {user.hiredDate ? moment(user.hiredDate).format('YYYY-MM-DD') : '--'}
                </Typography.Text>
              </Space>
              <Space size="small">
                <Typography.Text type="secondary">参加工作日期：</Typography.Text>
                <Typography.Text>
                  {user.joinWorkingDate ? moment(user.joinWorkingDate).format('YYYY-MM-DD') : '--'}
                </Typography.Text>
              </Space>
            </Space>
          )}
          {loading ? (
            <Spin />
          ) : (
            <Space style={{ width: '100%' }} direction="vertical" size="large">
              <Descriptions column={3} bordered>
                {[
                  {
                    label: '当年年假(天)',
                    key: 'totalBalance',
                    span: 2,
                    bold: true,
                  },
                  {
                    label: '已请',
                    key: 'totalUsedBalance',
                    bold: true,
                  },
                  {
                    label: '可请',
                    key: 'totalAvailableBalance',
                    bold: true,
                  },
                  {
                    label: '上年结转年假(天)',
                    span: 2,
                    key: 'carryOverBalance',
                  },
                  {
                    label: '已请',
                    key: 'carryOverUsedBalance',
                  },
                  {
                    label: '可请',
                    key: 'carryOverAvailableBalance',
                  },
                  {
                    label: '当年法定年假(天)',
                    span: 2,
                    key: 'statutoryBalance',
                  },

                  {
                    label: '已请',
                    key: 'statutoryUsedBalance',
                  },
                  {
                    label: '可请',
                    key: 'statutoryAvailableBalance',
                  },
                  {
                    label: '当年公司年假(天)',
                    span: 2,
                    key: 'companyBalance',
                  },
                  {
                    label: '已请',
                    key: 'companyUsedBalance',
                  },
                  {
                    label: '可请',
                    key: 'companyAvailableBalance',
                  },
                ].map(item => (
                  <Descriptions.Item
                    key={item.key}
                    labelStyle={{ width: item.span === 2 ? 225 : undefined }}
                    contentStyle={{ width: item.span === 2 ? undefined : 225 }}
                    label={
                      item.span === 2 ? (
                        <Typography.Text strong={item.bold}>{item.label}</Typography.Text>
                      ) : undefined
                    }
                  >
                    <Space size={4}>
                      <Typography.Text
                        type={!item.bold ? 'secondary' : undefined}
                        strong={item.bold}
                      >
                        {item.span !== 2 ? item.label : '应享'}
                      </Typography.Text>
                      <Typography.Text
                        strong={item.bold}
                        type={
                          balanceInfos &&
                          balanceInfos[item.key as keyof HolidayBalance] &&
                          (balanceInfos[item.key as keyof HolidayBalance] as number) < 0
                            ? 'danger'
                            : !item.bold
                              ? 'secondary'
                              : undefined
                        }
                      >
                        {balanceInfos ? balanceInfos[item.key as keyof HolidayBalance] : '--'}
                      </Typography.Text>
                    </Space>
                  </Descriptions.Item>
                ))}
              </Descriptions>
              <Descriptions column={3} bordered>
                {[
                  {
                    label: '全薪病假(天)',
                    span: 2,
                    key: 'sickSalaryBalance',
                  },
                  {
                    label: '已请',
                    key: 'sickSalaryUsedBalance',
                  },
                  {
                    label: '可请',
                    key: 'sickSalaryAvailableBalance',
                  },
                ].map(item => (
                  <Descriptions.Item
                    key={item.key}
                    labelStyle={{ width: item.span === 2 ? 225 : undefined }}
                    contentStyle={{ width: item.span === 2 ? undefined : 225 }}
                    label={
                      item.span === 2 ? (
                        <Typography.Text strong>{item.label}</Typography.Text>
                      ) : undefined
                    }
                  >
                    <Space size={4}>
                      <Typography.Text strong>
                        {item.span !== 2 ? item.label : '应享'}
                      </Typography.Text>
                      <Typography.Text
                        strong
                        type={
                          balanceInfos &&
                          balanceInfos[item.key as keyof HolidayBalance] &&
                          (balanceInfos[item.key as keyof HolidayBalance] as number) < 0
                            ? 'danger'
                            : undefined
                        }
                      >
                        {balanceInfos ? balanceInfos[item.key as keyof HolidayBalance] : '--'}
                      </Typography.Text>
                    </Space>
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </Space>
          )}
        </Space>
      </Space>
    </Card>
  );
}
