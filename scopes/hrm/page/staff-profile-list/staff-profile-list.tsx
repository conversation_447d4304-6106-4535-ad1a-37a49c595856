/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-4
 *
 * @packageDocumentation
 */
import moment from 'moment';
import React, { useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { fetchUsersByIds } from '@manyun/auth-hub.service.fetch-users-by-ids';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Spin } from '@manyun/base-ui.ui.spin';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { exportStaffList } from '@manyun/hrm.service.export-staff-list';
import { fetchPagedStaffList } from '@manyun/hrm.service.fetch-paged-staff-list';
import type { PagedUserJSON } from '@manyun/hrm.service.fetch-paged-staff-list';
import { fetchStaffStaticCounts } from '@manyun/hrm.service.fetch-staff-static-counts';
import { StaffCertificateModalView } from '@manyun/hrm.ui.staff-certificate-modal-view';
import { StaffProfileEditor } from '@manyun/hrm.ui.staff-profile-editor';
import { UserLink } from '@manyun/iam.ui.user-link';
import { MetaTypeText } from '@manyun/resource-hub.ui.meta-type-text';

import { OperationLogModelView } from './components/operation-log-modal-view';
import { RegularizedModalView } from './components/regularized-modal-view';
import { TableStyle } from './styles';

export type StaffProfileListProps = {};

type SearchFields = {
  staffId?: number;
  supervisorUid?: number;
  onlyInService?: boolean;
  blockGuids?: string[];
  positions?: string[];
  orderByJobNoDesc?: boolean;
};
type SearchParams = SearchFields & {
  page: number;
  pageSize: number;
};

export interface StaffStatistics {
  blockGuids?: Array<{
    value: string;
    count: number;
  }>;
  positions?: Array<{
    value: string;
    count: number;
  }>;
}

const ALL_CODE = 'ALL';

export const useLazyQueryStaffStaticCount = (params: Omit<SearchParams, 'page' | 'pageSize'>) => {
  const [data, setData] = useState<StaffStatistics>({
    blockGuids: [],
    positions: [],
  });
  const [loading, setLoading] = useState(false);
  const fetchStaticCount = async (_params: Omit<SearchParams, 'page' | 'pageSize'>) => {
    setLoading(true);
    const { error, data: newData } = await fetchStaffStaticCounts({
      userId: _params.staffId,
      directorId: _params.supervisorUid,
      blockGuids: _params.blockGuids?.includes(ALL_CODE)
        ? undefined
        : _params.blockGuids?.filter(item => item.split('.')[1]),
      positions: _params.positions?.includes(ALL_CODE) ? undefined : _params.positions,
      enable: _params.onlyInService === true ? true : undefined,
    });

    if (error) {
      setLoading(false);
      message.error(error.message);
      return;
    }
    setData({
      positions: newData.positions,
      blockGuids: newData.blocks,
    });
    setLoading(false);
  };

  React.useEffect(() => {
    fetchStaticCount(params);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return [{ data, loading }, fetchStaticCount] as const;
};

const useLazyQueryStaffList = (params: SearchParams) => {
  const [data, setData] = useState<(PagedUserJSON & { directorName?: string })[]>([]);
  const [total, setTotal] = useState(0);
  const [loading, setLoading] = useState(false);

  const fetchData = async (_params: SearchParams) => {
    setLoading(true);
    const { error, data } = await fetchPagedStaffList({
      pageNum: _params.page,
      pageSize: _params.pageSize,
      userId: _params.staffId,
      directorId: _params.supervisorUid,
      blockGuids: _params.blockGuids?.includes(ALL_CODE)
        ? undefined
        : _params.blockGuids?.filter(item => item.split('.')[1]),
      positions: _params.positions?.includes(ALL_CODE) ? undefined : _params.positions,
      orderByJobNoDesc: _params.orderByJobNoDesc,
      enable: _params.onlyInService === true ? true : undefined,
    });

    if (error) {
      setLoading(false);
      message.error(error.message);
      setData([]);
      setTotal(0);
      return;
    }
    let _data: (PagedUserJSON & { directorName?: string })[] = data.data;
    const userIds = Array.from(new Set(data?.data.flatMap(item => item.director) ?? []));

    if (userIds.length > 0) {
      const { data: userData } = await fetchUsersByIds({
        ids: userIds,
      });
      const userMap = new Map(userData.map(user => [user.id, user]));
      _data = data.data.map(item => {
        return {
          ...item,
          directorName: userMap.get(item.director)?.name,
        };
      });
    }
    setLoading(false);
    setData(_data);
    setTotal(data.total);
  };

  React.useEffect(() => {
    fetchData(params);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return [{ data, total, loading }, fetchData] as const;
};

const useExportData = () => {
  const [exportLoading, setExportLoading] = useState(false);

  const onExport = React.useCallback(async (params: SearchParams) => {
    setExportLoading(true);
    const { data: exportData, error } = await exportStaffList({
      userId: params.staffId,
      directorId: params.supervisorUid,
      blockGuids: params.blockGuids?.includes(ALL_CODE)
        ? undefined
        : params.blockGuids?.filter(item => item.split('.')[1]),
      positions: params.positions?.includes(ALL_CODE) ? undefined : params.positions,
      enable: params.onlyInService === true ? true : undefined,
    });
    setExportLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    return exportData;
  }, []);
  return [exportLoading, onExport] as const;
};

export function StaffProfileList() {
  const [authorized] = useAuthorized({
    checkByCode: 'element_staff-profile-list-operate',
  });
  const [queryParams, setQueryParams] = useState<SearchParams>({
    page: 1,
    pageSize: 10,
    onlyInService: true,
    positions: [ALL_CODE],
    blockGuids: [ALL_CODE],
  });

  const [form] = Form.useForm<SearchFields>();
  const [{ data, loading, total }, onReloadList] = useLazyQueryStaffList({
    page: 1,
    pageSize: 10,
    onlyInService: true,
  });
  const [{ data: statistics, loading: statisticsLoading }, onReloadStaticCount] =
    useLazyQueryStaffStaticCount({
      onlyInService: true,
    });
  const [exportLoading, onExport] = useExportData();

  const onRefreshData = React.useCallback(
    ({
      params,
      refreshList = true,
      refreshStatic = true,
    }: {
      params?: SearchParams;
      refreshList?: boolean;
      refreshStatic?: boolean;
    }) => {
      if (refreshList) {
        onReloadList({ ...queryParams, ...params });
      }
      if (refreshStatic) {
        onReloadStaticCount({ ...queryParams, ...params });
      }
    },
    [onReloadList, onReloadStaticCount, queryParams]
  );

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size={24}>
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          员工信息档案
        </Typography.Title>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Space direction="horizontal" align="start" size={4}>
            <Typography.Text>楼栋:</Typography.Text>
            <Space style={{ maxWidth: 'calc(100vw - 160px)' }} direction="horizontal" wrap>
              <Spin spinning={statisticsLoading}>
                <Checkbox.Group
                  value={queryParams.blockGuids}
                  onChange={values => {
                    const hasAll = values.includes(ALL_CODE);
                    const hasOtherSelected = values.some(v => v !== ALL_CODE);
                    const currentHasAll = (queryParams.blockGuids ?? []).includes(ALL_CODE);

                    let nextValues: string[];
                    if (hasAll) {
                      if (!currentHasAll) {
                        nextValues = [ALL_CODE];
                      } else if (hasOtherSelected) {
                        nextValues = values.filter(v => v !== ALL_CODE) as string[];
                      } else {
                        nextValues = [ALL_CODE];
                      }
                    } else if (values.length === 0) {
                      nextValues = [ALL_CODE];
                    } else {
                      nextValues = values as string[];
                    }
                    setQueryParams(prev => ({
                      ...prev,
                      blockGuids: nextValues,
                      page: 1,
                    }));
                    onRefreshData({
                      params: {
                        blockGuids: nextValues,
                        pageSize: queryParams.pageSize,
                        page: 1,
                      },
                    });
                  }}
                >
                  <Checkbox value="ALL">
                    全部 ({statistics.blockGuids?.reduce((acc, cur) => acc + cur.count, 0) ?? 0})
                  </Checkbox>
                  {statistics.blockGuids?.map(item => (
                    <Checkbox key={item.value} value={item.value}>
                      {item.value} ({item.count ?? 0})
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              </Spin>
            </Space>
          </Space>
          <Space direction="horizontal" align="start">
            <Typography.Text>岗位:</Typography.Text>
            <Space style={{ maxWidth: 'calc(100vw - 160px)' }} direction="horizontal" wrap>
              <Spin spinning={statisticsLoading}>
                <Checkbox.Group
                  value={queryParams.positions}
                  onChange={values => {
                    const hasAll = values.includes(ALL_CODE);
                    const hasOtherSelected = values.some(v => v !== ALL_CODE);
                    const currentHasAll = (queryParams.positions ?? []).includes(ALL_CODE);
                    let nextValues: string[];
                    if (hasAll) {
                      if (!currentHasAll) {
                        nextValues = [ALL_CODE];
                      } else if (hasOtherSelected) {
                        nextValues = values.filter(v => v !== ALL_CODE) as string[];
                      } else {
                        nextValues = [ALL_CODE];
                      }
                    } else if (values.length === 0) {
                      nextValues = [ALL_CODE];
                    } else {
                      nextValues = values as string[];
                    }

                    setQueryParams(prev => ({ ...prev, positions: nextValues, page: 1 }));
                    onRefreshData({
                      params: {
                        positions: nextValues,
                        pageSize: queryParams.pageSize,
                        page: 1,
                      },
                    });
                  }}
                >
                  <Checkbox value="ALL">
                    全部 ({statistics.positions?.reduce((acc, cur) => acc + cur.count, 0) ?? 0})
                  </Checkbox>
                  {statistics.positions?.map(item => (
                    <Checkbox key={item.value} value={item.value}>
                      <MetaTypeText metaType={'POSITION_YG' as any} code={item.value} defaultShow />
                      ({item.count ?? 0})
                    </Checkbox>
                  ))}
                </Checkbox.Group>
              </Spin>
            </Space>
          </Space>
        </Space>
        <Space style={{ width: '100%' }} direction="vertical" size={16}>
          <Space>
            <Form form={form} colon layout="inline">
              <Form.Item label="姓名" name="staffId">
                <UserSelect style={{ width: 372 }} labelInValue={false} allowClear />
              </Form.Item>
              <Form.Item label="直线经理" name="supervisorUid">
                <UserSelect style={{ width: 372 }} labelInValue={false} allowClear />
              </Form.Item>
              <Form.Item>
                <Space>
                  <Button
                    type="primary"
                    onClick={() => {
                      const values = form.getFieldsValue();
                      setQueryParams(prev => ({
                        ...prev,
                        ...values,
                        page: 1,
                      }));
                      onRefreshData({ params: { ...queryParams, ...values, page: 1 } });
                    }}
                  >
                    搜索
                  </Button>
                  <Button
                    onClick={() => {
                      form.resetFields();
                      setQueryParams(prev => ({
                        ...prev,
                        staffId: undefined,
                        supervisorUid: undefined,
                        page: 1,
                      }));
                      onRefreshData({
                        params: {
                          ...queryParams,
                          staffId: undefined,
                          supervisorUid: undefined,
                          page: 1,
                        },
                      });
                    }}
                  >
                    重置
                  </Button>
                </Space>
              </Form.Item>
            </Form>
          </Space>
          <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Checkbox
              checked={queryParams.onlyInService}
              onChange={e => {
                setQueryParams(prev => ({
                  ...prev,
                  page: 1,
                  onlyInService: e.target.checked,
                }));
                onRefreshData({
                  params: { ...queryParams, page: 1, onlyInService: e.target.checked },
                });
              }}
            >
              仅看在职
            </Checkbox>
            <FileExport
              text=""
              filename="员工信息档案"
              disabled={exportLoading}
              data={() => {
                return onExport(queryParams);
              }}
            />
          </div>
          <TableStyle>
            <Table<PagedUserJSON>
              loading={loading}
              rowKey="id"
              columns={[
                {
                  title: '工号',
                  dataIndex: 'jobNumber',
                  fixed: 'left',
                  sorter: true,
                  render: val => val ?? '--',
                },
                {
                  title: '姓名',
                  dataIndex: 'name',
                  fixed: 'left',
                  render: (_, record) => (
                    <UserLink
                      target="_blank"
                      userId={Number(record.id)}
                      userName={record.userName}
                    />
                  ),
                },
                {
                  title: '性别',
                  dataIndex: 'gender',
                  render: sexType => {
                    if (sexType !== null || sexType !== undefined) {
                      return sexType === 'FEMALE' ? '女性' : '男性';
                    }
                    return '--';
                  },
                },
                {
                  title: '状态',
                  dataIndex: 'enable',
                  render: (value, record) => {
                    return (
                      <>
                        {value ? '在职' : '离职'}
                        {record.confirmationDate ? '' : ' (试用期)'}
                      </>
                    );
                  },
                },
                {
                  title: '所属楼栋',
                  dataIndex: 'ptBlockGuids',
                  className: 'max-width-content',
                  ellipsis: true,
                  render: (_, record) => {
                    const ctx =
                      (record.ptBlockGuids ?? []).length > 0
                        ? (record.ptBlockGuids ?? []).join('｜')
                        : '--';
                    return ctx;
                  },
                },
                {
                  title: '岗位',
                  dataIndex: 'position',
                  render: (_, record) =>
                    record.position ? (
                      // eslint-disable-next-line @typescript-eslint/no-explicit-any
                      <MetaTypeText
                        metaType={'POSITION_YG' as any}
                        code={record.position}
                        defaultShow
                      />
                    ) : (
                      '--'
                    ),
                },
                {
                  title: '有效/需上传证书',
                  dataIndex: 'certification',
                  render: (_, record) => {
                    return (
                      <StaffCertificateModalView
                        userId={record.id}
                        allCertCount={Number(record.allCertCount)}
                        validCertCount={Number(record.validCertCount)}
                        excludeCustom
                      />
                    );
                  },
                },
                {
                  title: '入职日期',
                  dataIndex: 'hiredDate',
                  render: val => {
                    return val ? moment(val).format('YYYY-MM-DD') : '--';
                  },
                },
                {
                  title: '直线经理',
                  dataIndex: 'directorName',
                  render: val => {
                    return val ?? '--';
                  },
                },
                {
                  title: '工龄',
                  dataIndex: 'workingAge',
                  render: val => {
                    if (val !== null || val !== undefined) {
                      return formatMonthsToYearMonth(val);
                    }
                    return '--';
                  },
                },
                {
                  title: '司龄',
                  dataIndex: 'serviceAge',
                  render: val => {
                    if (val !== null || val !== undefined) {
                      return formatMonthsToYearMonth(val);
                    }
                    return '--';
                  },
                },
                {
                  title: '联系方式',
                  dataIndex: 'mobile',
                  render: val => val ?? '--',
                },
                {
                  title: '邮箱',
                  dataIndex: 'email',
                  render: val => val ?? '--',
                },
                {
                  title: '操作',
                  dataIndex: 'operation',
                  width: 120,
                  fixed: 'right',
                  render: (_, record) => (
                    <Space size={16}>
                      {authorized && (
                        <StaffProfileEditor
                          staffId={record.id}
                          isManager
                          onSuccess={() => {
                            onRefreshData({ params: queryParams });
                          }}
                        >
                          <Button type="link" compact>
                            编辑
                          </Button>
                        </StaffProfileEditor>
                      )}
                      <OperationLogModelView staffId={record.id} />
                      {/*tips: 试用期员工透出转正按钮   转正日期为空才透出这个字段*/}
                      {!record.confirmationDate && authorized && (
                        <RegularizedModalView
                          record={{ userId: record.id, userName: record.userName }}
                          onOk={() => {
                            onReloadList(queryParams);
                          }}
                        />
                      )}
                    </Space>
                  ),
                },
              ]}
              scroll={{ x: 'max-content' }}
              dataSource={data}
              pagination={{
                total: total,
                current: queryParams.page,
                pageSize: queryParams.pageSize,
                onChange: (page, pageSize) => {
                  setQueryParams(pre => ({
                    ...pre,
                    page,
                    pageSize,
                  }));
                  onReloadList({
                    ...queryParams,
                    page,
                    pageSize,
                  });
                },
              }}
              onChange={(_, __, sorter, { action }) => {
                if (action === 'sort') {
                  const { order } = sorter as { order: 'ascend' | 'descend' | undefined };
                  setQueryParams(prev => ({
                    ...prev,
                    orderByJobNoDesc:
                      order === 'descend' ? true : order === 'ascend' ? false : undefined,
                    page: 1,
                  }));
                  onReloadList({
                    ...queryParams,
                    orderByJobNoDesc:
                      order === 'descend' ? true : order === 'ascend' ? false : undefined,
                    page: 1,
                  });
                }
              }}
            />
          </TableStyle>
        </Space>
      </Space>
    </Card>
  );
}

/**
 * 将月份转换为年月显示
 * @param months 月份数
 * @returns 格式化后的年月字符串
 */
const formatMonthsToYearMonth = (months: number | undefined): string => {
  if (months === undefined || months === null) {
    return '--';
  }

  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;

  if (years && remainingMonths) {
    return `${years}年${remainingMonths}个月`;
  } else if (years) {
    return `${years}年`;
  } else {
    return `${remainingMonths}个月`;
  }
};
