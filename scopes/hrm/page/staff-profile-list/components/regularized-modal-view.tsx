import moment from 'moment';
import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useMutateUser } from './use-mutate-user.js';

interface RegularizedFormData {
  userId: number;
  userName: string;
  regularizedDate?: moment.Moment;
}

interface RegularizedModalViewProps {
  record: RegularizedFormData;
  onOk?: () => void;
}

export function RegularizedModalView({ record, onOk }: RegularizedModalViewProps) {
  const [form] = Form.useForm<RegularizedFormData>();
  const [open, setOpen] = React.useState(false);
  const [loading, onUpdateUser] = useMutateUser();

  const handleOk = async () => {
    const values = await form.validateFields();
    onUpdateUser(
      {
        id: Number(record.userId),
        confirmationDate: values.regularizedDate!.startOf('date').valueOf(),
      },
      res => {
        if (res) {
          setOpen(false);
          onOk?.();
          form.resetFields();
        }
      }
    );
  };

  const handleCancel = () => {
    form.resetFields();
    setOpen(false);
  };

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
          form.setFieldsValue({
            userName: record.userName,
          });
        }}
      >
        转正
      </Button>
      <Modal
        title="转正"
        width={480}
        open={open}
        destroyOnClose
        okText="提交"
        okButtonProps={{
          loading,
        }}
        cancelButtonProps={{
          disabled: loading,
        }}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Form form={form} labelCol={{ span: 8 }} colon>
          <Form.Item label="人员姓名" name="userName" required>
            <Input style={{ width: 216 }} disabled />
          </Form.Item>
          <Form.Item
            label="转正日期"
            name="regularizedDate"
            rules={[{ required: true, message: '请选择转正日期' }]}
          >
            <DatePicker style={{ width: 216 }} />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
}
