import { useCallback, useState } from 'react';

import { mutateUser } from '@manyun/auth-hub.service.mutate-user';
import type { SvcQuery } from '@manyun/auth-hub.service.mutate-user';
import { message } from '@manyun/base-ui.ui.message';

export function useMutateUser() {
  const [loading, setLoading] = useState(false);

  const updateUser = useCallback(async (svcData: SvcQuery, callback?: (res: boolean) => void) => {
    setLoading(true);
    const { error, data } = await mutateUser(svcData);
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (callback) {
      callback(data);
    } else {
      message.success('操作成功！');
    }
  }, []);
  return [loading, updateUser] as const;
}
