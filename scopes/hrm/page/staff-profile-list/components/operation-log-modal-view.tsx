import React, { useState } from 'react';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';

export const OperationLogModelView: React.FC<{ staffId: number }> = ({ staffId }) => {
  const [visible, setVisible] = useState(false);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setVisible(true);
        }}
      >
        操作日志
      </Button>
      <Modal
        title="操作日志"
        width={1080}
        bodyStyle={{ maxHeight: '75vh', overflowY: 'auto' }}
        open={visible}
        footer={null}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <OperationLogTable
          defaultSearchParams={{
            targetId: staffId.toString(),
            targetType: 'USER',
          }}
          showColumns={['serialNumber', 'modifyType', 'targetType']}
          isTargetIdEqual={targetId => {
            return targetId === staffId.toString();
          }}
        />
      </Modal>
    </>
  );
};
