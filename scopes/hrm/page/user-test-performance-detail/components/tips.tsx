import React from 'react';

import ExclamationCircleFilled from '@ant-design/icons/es/icons/ExclamationCircleFilled';

import { prefixCls } from '@manyun/base-ui.style.style/dist/prefix';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { usePerformanceDetail } from '../performance-detail-context';

/**员工视角 考核未完成时显示提示 */
export const StaffTips = () => {
  const [{ performance, isGoal, isLoginUserKpi }] = usePerformanceDetail();
  if (
    !isGoal &&
    isLoginUserKpi &&
    performance &&
    performance.evaluationStatus !== 'EVAL_FINISHED'
  ) {
    return (
      <Space>
        <ExclamationCircleFilled style={{ color: `var(--${prefixCls}-warning-color)` }} />
        <Typography.Text type="warning">
          各项目运维管理经理将于试用期到期前1个月内完成述职答辩，届时将由你的HR组织安排，如有任何问题可与你的HR沟通。
        </Typography.Text>
      </Space>
    );
  }
  return null;
};
