import React, { useCallback, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import type { BackendGrade } from '@manyun/hrm.model.performance-objective';
import { savePerformance } from '@manyun/hrm.service.save-performance';
import type { SvcQuery as SelfEvaluateParams } from '@manyun/hrm.service.save-performance';
import { submitPerformance } from '@manyun/hrm.service.submit-performance';

import { usePerformanceDetail } from '../performance-detail-context';

/**员工自评按钮组 */
export const SelfEvaluateButtons = () => {
  const [{ performance, formInstance: form }, { onLoadData }] = usePerformanceDetail();
  const [submitLoading, setSubmitLoading] = useState(false);
  const [saveLoading, setSaveLoading] = useState(false);

  const onHandleSelfEvaluate = useCallback(
    async (validate: boolean) => {
      if (!performance || !form || !performance.id) {
        return;
      }
      if (validate) {
        try {
          await form.validateFields();
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
        } catch (err: any) {
          form.scrollToField(err.errorFields[0].name.toString(), {
            behavior: actions => {
              actions.forEach(action => {
                action.el.scrollTop =
                  action.top +
                  (document
                    .getElementById(err.errorFields[0].name.toString())!
                    .getBoundingClientRect().y > action.top
                    ? 75
                    : 0);
              });
            },
          });
          return;
        }
      }

      validate ? setSubmitLoading(true) : setSaveLoading(true);
      const params: SelfEvaluateParams = {
        id: performance.id,
        userId: performance.user.id,
        type: 'EVAL',
        superiorIds: performance.lineManagers?.map(user => user.id) ?? [],
        selfEvaluation: form.getFieldValue('selfComprehensive') ?? undefined,
        hiredDate: performance.user.hiredAt,
        goals: performance.objectives
          .filter(goal => goal.type === 'BIZ')
          .map(goal => ({
            ...goal,
            selfEvaluation: goal.selfEvaluation ?? undefined,
            supervisorRate: (goal.grade as BackendGrade) ?? undefined,
          })),
      };
      const { error } = validate ? await submitPerformance(params) : await savePerformance(params);
      validate ? setSubmitLoading(false) : setSaveLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      message.success(validate ? '试用期考核自评已提交' : '保存成功');

      if (validate) {
        onLoadData();
      }
    },
    [form, onLoadData, performance]
  );

  return (
    <Space size="middle">
      <Button
        type="primary"
        loading={submitLoading}
        disabled={saveLoading}
        onClick={() => {
          onHandleSelfEvaluate(true);
        }}
      >
        提交审批
      </Button>
      <Button
        disabled={submitLoading}
        loading={saveLoading}
        onClick={() => {
          onHandleSelfEvaluate(false);
        }}
      >
        保存草稿
      </Button>
    </Space>
  );
};
