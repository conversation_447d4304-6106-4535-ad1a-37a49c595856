import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';

export const RateSelect = (props: Omit<SelectProps, 'options'>) => {
  const locales = useMemo(() => getPerformanceObjectiveLocales(), []);

  return (
    <Select
      style={{ width: 216 }}
      showSearch
      {...props}
      options={Object.keys(locales.grade.enum).map(key => ({
        label: locales.grade.enum[Number(key)],
        value: Number(key),
      }))}
      getPopupContainer={trigger => trigger.parentNode.parentNode}
    />
  );
};
