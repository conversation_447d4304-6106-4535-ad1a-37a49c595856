import React, { useMemo } from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import type { BackendGrade } from '@manyun/hrm.model.performance-objective';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';

export const RateLevelTag = ({ level }: { level: BackendGrade }) => {
  const locales = useMemo(() => getPerformanceObjectiveLocales(), []);
  return (
    <Space>
      {`${locales.grade._self}:`}
      <Tag color="processing">{locales.grade.enum[level]}</Tag>
    </Space>
  );
};
