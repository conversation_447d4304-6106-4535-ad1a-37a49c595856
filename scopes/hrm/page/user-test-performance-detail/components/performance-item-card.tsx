import classNames from 'classnames';
import React, { useMemo } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type {
  BackendGrade,
  BackendObjectiveStatus,
  PerformanceObjectiveJSON,
} from '@manyun/hrm.model.performance-objective';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';
import { PerformanceGoalCollapse } from '@manyun/hrm.ui.performance-goal-collapse';

import { usePerformanceDetail } from '../performance-detail-context';
import styles from '../user-test-performance-detail.module.less';
import { DescriptionContainer } from './description-container';
import { RateLevelTag } from './rate-level-tag';
import { RateSelect } from './rate-select';
import { generateNamePath } from './util';

export type PerformanceGoalCardProps = {
  goal: PerformanceObjectiveJSON;
};

export const PerformanceItemCard = ({ goal }: PerformanceGoalCardProps) => {
  const locales = useMemo(() => getPerformanceObjectiveLocales(), []);

  const [{ selfEvaluate, isSupervisorEvaluate, isGoal, hasSuperiorEvaluated }] =
    usePerformanceDetail();

  /**员工自定义目标 */
  if (goal.type === 'BIZ') {
    return (
      <Card
        size="small"
        bodyStyle={{ padding: '24px 12p 24px 24px' }}
        className={styles.objectiveCard}
      >
        <PerformanceGoalCollapse active={isGoal} goal={goal} hideDefaultExtra={selfEvaluate} />
        {!isGoal && (
          <Space
            style={{ width: '100%' }}
            className={classNames(!selfEvaluate && styles.evaluationInfos)}
            size="middle"
            direction="vertical"
          >
            {selfEvaluate ? (
              <GoalSelfEvalFormItems goal={goal} />
            ) : (
              <>
                <DescriptionContainer label={locales.selfEvaluation} value={goal.selfEvaluation} />
                {isSupervisorEvaluate ? (
                  <Form.Item
                    label={locales.grade._self}
                    name={generateNamePath(goal.id, 'grade')}
                    rules={[
                      {
                        required: true,
                        message: `${locales.grade._self}必填`,
                      },
                    ]}
                  >
                    <RateSelect />
                  </Form.Item>
                ) : (
                  hasSuperiorEvaluated &&
                  goal.grade && <RateLevelTag level={goal.grade as BackendGrade} />
                )}
              </>
            )}
          </Space>
        )}
      </Card>
    );
  }

  /**软性能力 */
  return (
    <Card>
      <Space direction="vertical" size="middle">
        <Typography.Text>{goal.title}</Typography.Text>
        <Typography.Text style={{ whiteSpace: 'pre-line' }}>{goal.content}</Typography.Text>
        {isSupervisorEvaluate ? (
          <Form.Item
            name={generateNamePath(goal.id, 'grade')}
            label={locales.grade._self}
            rules={[
              {
                required: true,
                message: `${locales.grade._self}必填`,
              },
            ]}
          >
            <RateSelect />
          </Form.Item>
        ) : (
          goal.grade && <RateLevelTag level={goal.grade as BackendGrade} />
        )}
      </Space>
    </Card>
  );
};

const GoalSelfEvalFormItems = ({ goal }: { goal: PerformanceObjectiveJSON }) => {
  const locales = useMemo(() => getPerformanceObjectiveLocales(), []);

  return (
    <Container style={{ padding: '12px' }}>
      <Space size="large">
        <Form.Item label="权重(%)" labelCol={{ span: 24 }} required>
          <InputNumber style={{ width: 216 }} value={goal.percent} disabled addonAfter="%" />
        </Form.Item>
        <Form.Item
          name={generateNamePath(goal.id, 'status')}
          label={locales.status._self}
          labelCol={{ span: 24 }}
          rules={[
            {
              required: true,
              message: `${locales.status._self}必填`,
            },
          ]}
        >
          <Select
            style={{ width: 224 }}
            options={Object.keys(locales.status.enum).map(key => ({
              label: locales.status.enum[key as BackendObjectiveStatus],
              value: key,
            }))}
          />
        </Form.Item>
      </Space>
      <Form.Item
        style={{ marginBottom: 0 }}
        name={generateNamePath(goal.id, 'selfEvaluation')}
        labelCol={{ span: 24 }}
        label={locales.selfEvaluation}
        rules={[
          {
            required: true,
            whitespace: true,
            message: `${locales.selfEvaluation}必填`,
          },
          { max: 10000, message: '最多输入 10000 个字符！' },
        ]}
      >
        <Input.TextArea placeholder="请填写自评内容" autoSize={{ minRows: 5, maxRows: 20 }} />
      </Form.Item>
    </Container>
  );
};
