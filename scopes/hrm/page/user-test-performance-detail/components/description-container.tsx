import React from 'react';

import { Container } from '@manyun/base-ui.ui.container';
import { Space } from '@manyun/base-ui.ui.space';

export const DescriptionContainer = ({
  label,
  value,
}: {
  label: React.ReactNode;
  value: React.ReactNode;
}) => {
  return (
    <Container color="default" size="large">
      <Space direction="vertical">
        {`${label}：`}
        <div style={{ whiteSpace: 'pre-line' }}>{value}</div>
      </Space>
    </Container>
  );
};
