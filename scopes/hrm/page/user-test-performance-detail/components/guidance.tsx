import React, { useMemo, useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { usePerformanceDetail } from '../performance-detail-context';

export const Guidance = ({ type }: { type: 'BIZ' | 'VALUES' }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const [{ performance, selfEvaluate, isSupervisorEvaluate }] = usePerformanceDetail();

  const readyFinishedLen = useMemo(() => {
    if (selfEvaluate && type === 'VALUES') {
      return;
    }
    const goals = performance?.objectives.filter(goal => goal.type === type);
    const finishedGoals = goals?.filter(goal =>
      selfEvaluate ? !!goal.status && goal.selfEvaluation : !!goal.grade
    );
    return finishedGoals?.length;
  }, [selfEvaluate, type, performance?.objectives]);

  if (!isSupervisorEvaluate && !selfEvaluate) {
    return null;
  }

  return (
    <Space style={{ width: '100%' }} size="middle" direction="vertical">
      <Alert
        message={`${type === 'BIZ' ? '评估权重合计的80%' : '评估权重合计的20%'} ${
          readyFinishedLen !== undefined
            ? `${
                performance?.objectives.filter(goal => goal.type === type).length
              }项中${readyFinishedLen}项${selfEvaluate ? '已填写' : '已提供意见'}`
            : ''
        }`}
        action={
          type === 'BIZ' && selfEvaluate ? (
            <Button
              type="link"
              compact
              onClick={() => {
                setIsModalOpen(true);
              }}
            >
              考核填写指引
            </Button>
          ) : undefined
        }
      />
      <Modal
        width={720}
        title="考核填写指引"
        open={isModalOpen}
        footer={false}
        onCancel={() => {
          setIsModalOpen(false);
        }}
      >
        <Space direction="vertical" size={0}>
          <div>1. 原则上,在试用期到期前一个月需要对新员工进行试用期考核</div>
          <div>2. 考核流程如下：</div>
          <Space style={{ alignItems: 'flex-start' }} size={0}>
            <div>&nbsp;&nbsp;&nbsp;a) </div>
            <div>
              员工填写：对试用期目标完成情况进行逐一阐述，并从突出的工作能力和表现、需改进的方面，职业发展要求进行自评
            </div>
          </Space>
          <Space style={{ alignItems: 'flex-start' }} size={0}>
            <div>&nbsp;&nbsp;&nbsp;b) </div>
            <div>
              评分：直线经理从员工试用期目标完成情况（占比80%）和试用期期间软性能力（占比20%）两方面进行评分，并对员工试用期期间的工作表现进行总体评价，并确认是否通过试用期
            </div>
          </Space>
          <Space style={{ alignItems: 'flex-start' }} size={0}>
            <div>&nbsp;&nbsp;&nbsp;c) </div>
            <div>考核结果审批：二级经理、HR根据直线经理评估来审批员工试用期是否通过考核</div>
          </Space>
        </Space>
      </Modal>
    </Space>
  );
};
