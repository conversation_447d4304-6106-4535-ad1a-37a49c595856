import React from 'react';
import { useHistory } from 'react-router';

import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { scrollToField } from '@manyun/dc-brain.util.scroll-to-field';
import type { BackendGrade } from '@manyun/hrm.model.performance-objective';
import {
  AgreePerformanceButton,
  RefusePerformanceButton,
  SavePerformanceButton,
  TransformPerformanceButton,
} from '@manyun/hrm.ui.performance-approve-button';
import type { ApproveStatus } from '@manyun/hrm.ui.performance-approve-button';

import { usePerformanceDetail } from '../performance-detail-context';

export type PerformanceApproveButtonGroupProps = {
  id: number;
  /**
   * 目标 、绩效
   */
  subType: 'GOAL' | 'EVAL';
  currentStep: ApproveStatus;
};

/**审批按钮组 */
export const PerformanceApproveButtonGroup = ({
  id,
  subType,
  currentStep,
}: PerformanceApproveButtonGroupProps) => {
  const history = useHistory();
  const [{ formInstance: form, performance }, { onLoadData }] = usePerformanceDetail();

  const onValidate = async (warningOnly: boolean) => {
    try {
      const values = warningOnly ? form!.getFieldsValue() : await form!.validateFields();
      return Promise.resolve({
        content: {
          summary: values.supervisorComprehensive,
        },
        result: values.result,
        goalGrades: performance?.objectives.map(goal => ({
          id: goal.id,
          grade: goal.grade! as BackendGrade,
        })),
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      scrollToField(form!.scrollToField, err.errorFields);
      return Promise.reject(err);
    }
  };

  return (
    <Space size="middle">
      <AgreePerformanceButton
        type="primary"
        bizId={id}
        currentStep={currentStep}
        text={currentStep === 'EVAL_WAIT_SUPERIOR' ? '提交评估' : undefined}
        onValidate={currentStep === 'EVAL_WAIT_SUPERIOR' ? () => onValidate(false) : undefined}
        onSuccess={() => {
          message.success(`审批通过${subType === 'GOAL' ? '，目标已生效' : ''}`);
          history.goBack();
        }}
      />
      {currentStep === 'EVAL_WAIT_SUPERIOR' && (
        <SavePerformanceButton
          bizId={id}
          currentStep="EVAL_WAIT_SUPERIOR"
          onValidate={() => onValidate(true)}
          onSuccess={() => {
            message.success('保存草稿成功');
          }}
        />
      )}
      <RefusePerformanceButton
        bizId={id}
        currentStep={currentStep}
        onSuccess={() => {
          message.success(`${subType === 'GOAL' ? '目标' : '员工绩效考核'}已退回`);
          onLoadData();
        }}
      />
      <TransformPerformanceButton
        bizId={id}
        pfSubType={subType === 'GOAL' ? 'TARGET' : 'EVAL'}
        currentStep={currentStep}
        onSuccess={() => {
          message.success(`转交成功`);
          history.goBack();
        }}
      />
    </Space>
  );
};
