import React, { useMemo } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type {
  BackendEvaluationStepType,
  BackendPerformanceResult,
  SyntheticEvaluationStepType,
} from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import { PerformanceResultTag } from '@manyun/hrm.ui.performance-result-tag';

import { usePerformanceDetail } from '../performance-detail-context';
import { DescriptionContainer } from './description-container';
import { Guidance } from './guidance';

export const ComprehensiveCard = () => {
  const [{ selfEvaluate, isSupervisorEvaluate, hasSuperiorEvaluated, performance }] =
    usePerformanceDetail();

  const locales = useMemo(() => getPerformanceLocales(), []);

  const evaluationNodes = useMemo(() => {
    if (!performance) {
      return [];
    }
    const evaluationNodes: {
      label: string;
      type: BackendEvaluationStepType | SyntheticEvaluationStepType;
      value: React.ReactNode;
      result?: BackendPerformanceResult;
    }[] = [];
    performance.evaluations.forEach(evaluation => {
      if (evaluation.type === 'SELF_EVAL') {
        evaluationNodes.push({
          type: evaluation.type,
          label: locales.evaluationStepText.enum[evaluation.type],
          value: evaluation.users.map(user => user.comments?.summary).join('\n'),
        });
      } else if (
        [
          'EVAL_SUPERIOR',
          'EVAL_SEC_SUPERIOR',
          'EVAL_HR',
          'EVAL_AREA_HR',
          'EVAL_WAIT_AREA_HR',
        ].includes(evaluation.type)
      ) {
        if (
          hasSuperiorEvaluated &&
          evaluation.users?.some(user => user.status === 'PASS' || user.status === 'REFUSE')
        ) {
          evaluationNodes.push({
            type: evaluation.type,
            label: locales.evaluationStepText.enum[evaluation.type],
            value:
              evaluation.type === 'EVAL_SUPERIOR'
                ? evaluation.users.map(user => user.comments?.summary).join('\n')
                : evaluation.users
                    .filter(user => user.status === 'PASS' || user.status === 'REFUSE')
                    .map(
                      user =>
                        `${user.user?.name ?? '--'}${user.status === 'PASS' ? '同意' : '拒绝'}`
                    )
                    .join('\n'),
            result:
              evaluation.type === 'EVAL_SUPERIOR'
                ? evaluation.users.every(user => user.result === 'PASS')
                  ? 'PASS'
                  : 'UN_PASS'
                : undefined,
          });
        }
      }
    });
    return evaluationNodes;
  }, [hasSuperiorEvaluated, locales.evaluationStepText.enum, performance]);

  if (!performance) {
    return null;
  }

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title level={5} showBadge>
          综合评价
        </Typography.Title>
        {selfEvaluate ? (
          <>
            <Guidance type="VALUES" />
            <Form.Item
              style={{ marginBottom: 0 }}
              label="员工自评"
              labelCol={{ span: 24 }}
              name="selfComprehensive"
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: `员工自评必填`,
                },
                { max: 10000, message: '最多输入 10000 个字符！' },
              ]}
            >
              <Input.TextArea placeholder="请填写自评内容" autoSize={{ minRows: 5, maxRows: 20 }} />
            </Form.Item>
          </>
        ) : (
          <>
            {evaluationNodes.map(node => {
              return (
                <React.Fragment key={`${node.label}`}>
                  {node.result && (
                    <Space>
                      <div>考核结果:</div>
                      <PerformanceResultTag result={node.result} />
                    </Space>
                  )}
                  <DescriptionContainer label={node.label} value={node.value} />
                </React.Fragment>
              );
            })}

            {isSupervisorEvaluate && (
              <>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  label={locales.result.__self}
                  name="result"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: `${locales.result.__self}必填`,
                    },
                  ]}
                >
                  <Select
                    style={{ width: 216 }}
                    getPopupContainer={trigger => trigger.parentNode.parentNode}
                    options={Object.keys(locales.result.enum).map(key => ({
                      label: locales.result.enum[key as BackendPerformanceResult],
                      value: key,
                    }))}
                  />
                </Form.Item>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 24 }}
                  label={locales.supervisorComprehensive}
                  name="supervisorComprehensive"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: `${locales.supervisorComprehensive}必填`,
                    },
                    { max: 10000, message: '最多输入 10000 个字符！' },
                  ]}
                >
                  <Input.TextArea
                    placeholder="请填写综合评价"
                    autoSize={{ minRows: 5, maxRows: 20 }}
                  />
                </Form.Item>
              </>
            )}
          </>
        )}
      </Space>
    </Card>
  );
};
