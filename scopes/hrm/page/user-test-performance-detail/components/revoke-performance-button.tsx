import React from 'react';
import { useHistory } from 'react-router';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';

import { generatePerformanceGoalsSetting } from '@manyun/hrm.route.hrm-routes';
import { revokePerformance } from '@manyun/hrm.service.revoke-performance';

import { usePerformanceDetail } from '../performance-detail-context';

export type RevokePerformanceButtonProps = {
  /**
   * 绩效id
   */
  id: number;
  /**
   * 目标 、绩效
   */
  subType: 'GOAL' | 'EVAL';
};

/**目标撤回/自评撤回 */
export const RevokePerformanceButton = ({ id, subType }: RevokePerformanceButtonProps) => {
  const [, { onLoadData }] = usePerformanceDetail();
  const history = useHistory();

  return (
    <Popconfirm
      title={
        <>
          <div>
            {`确定撤回该审批？您的${subType === 'GOAL' ? '绩效目标' : '考核自评'}已在审批中，`}
          </div>
          <div>{`撤回后直线经理将无法看到您的试用期${subType === 'GOAL' ? '目标' : '自评'}`}</div>
        </>
      }
      okText="确认撤回"
      onConfirm={async () => {
        const { error } = await revokePerformance({
          id,
          subType: subType === 'GOAL' ? 'TARGET' : 'EVAL',
        });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('撤回成功');

        if (subType === 'GOAL') {
          history.replace(
            generatePerformanceGoalsSetting({
              id: id.toString(),
              key: `${id}_TARGET`,
            })
          );
        } else {
          onLoadData();
        }
      }}
    >
      <Button type="primary">撤回</Button>
    </Popconfirm>
  );
};
