import React from 'react';
import { useHistory } from 'react-router';

import { Button } from '@manyun/base-ui.ui.button';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';

import { generatePerformanceGoalsSetting } from '@manyun/hrm.route.hrm-routes';

import { usePerformanceDetail } from '../performance-detail-context';

export const ChangeGoalsButton = ({ userId }: { userId: string }) => {
  const history = useHistory();
  const [{ performance }] = usePerformanceDetail();

  if (!performance) {
    return null;
  }
  return (
    <Popconfirm
      title={
        <Space style={{ width: 300 }}>
          是否修改目标计划?修改目标计划后，需直线经理重新审批，请谨慎操作！
        </Space>
      }
      okText="确认修改"
      onConfirm={() => {
        history.push(
          `${generatePerformanceGoalsSetting({
            id: performance.id!.toString(),
            key: `${performance.id}_TARGET`,
          })}?onlySubmit=true`
        );
      }}
    >
      <Button key="edit">修改目标计划</Button>
    </Popconfirm>
  );
};
