import { gql, useQuery } from '@apollo/client';
import type { QueryHookOptions, QueryResult } from '@apollo/client';

import type {
  BackendPerformanceSubType,
  BackendPerformanceType,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';

export const GET_PERFORMANCE_QUERY = gql`
  query GetPerformance($type: String!, $subType: String!, $id: Int!) {
    performance(type: $type, subType: $subType, id: $id) {
      rowKey
      id
      type
      subType
      user {
        id
        name
        hiredAt
      }
      period
      startedAt
      expiredAt
      objectives {
        id
        type
        title
        percent
        content
        measurements
        status
        startedAt
        finishedAt
        selfEvaluation
        grade
      }
      objectiveStatus
      objectiveStartedAt
      objectiveExpiredAt
      evaluations {
        id
        type
        status
        users {
          user {
            id
            name
          }
          comments {
            summary
            reason
          }
          status
          result
          evaluatedAt
          isEvaluated
        }
        isCurrentStep
      }
      evaluationStatus
      evaluationStartedAt
      evaluationExpiredAt
      planId
      result
      grade
      lineManagers {
        id
        name
      }
      secondLineManagers {
        id
        name
      }
      hrs {
        id
        name
      }
      isLoggedInUserInCurrentStepUsers
      currentHandlers {
        id
        name
      }
    }
  }
`;

type PerformanceResolverArgs = {
  type: BackendPerformanceType;
  subType: BackendPerformanceSubType;
  id: string | number;
};

export type PerformanceQueryResultData = {
  performance: PerformanceJSON | null;
};

export function usePerformance(
  options?: QueryHookOptions<PerformanceQueryResultData, PerformanceResolverArgs>
): QueryResult<PerformanceQueryResultData, PerformanceResolverArgs> {
  return useQuery<PerformanceQueryResultData, PerformanceResolverArgs>(GET_PERFORMANCE_QUERY, {
    ...options,
    fetchPolicy: 'network-only',
  });
}
