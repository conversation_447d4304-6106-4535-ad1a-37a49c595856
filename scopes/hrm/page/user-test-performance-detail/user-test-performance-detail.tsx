import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router';

import { gql, useApolloClient } from '@apollo/client';
import classNames from 'classnames';
import moment from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Empty } from '@manyun/base-ui.ui.empty';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import type { EvaluationDetailParams } from '@manyun/hrm.route.hrm-routes';
import { EvaluateStatusTag } from '@manyun/hrm.ui.evalute-status-tag';
import { GoalsStatusTag } from '@manyun/hrm.ui.goals-status-tag';
import type { ApproveStatus } from '@manyun/hrm.ui.performance-approve-button';
import { PerformanceApproveProcess } from '@manyun/hrm.ui.performance-approve-process';

import { ChangeGoalsButton } from './components/change-goals-button';
import { ComprehensiveCard } from './components/comprehensiv-card';
import { Guidance } from './components/guidance';
import { PerformanceApproveButtonGroup } from './components/performance-approve-button-group';
import { PerformanceItemCard } from './components/performance-item-card';
import { RevokePerformanceButton } from './components/revoke-performance-button';
import { SelfEvaluateButtons } from './components/self-evaluate-buttons';
import { StaffTips } from './components/tips';
import { generateNamePath, parsedNamePath } from './components/util';
import { PerformanceDetailContext } from './performance-detail-context';
import { usePerformance } from './use-performance';
import styles from './user-test-performance-detail.module.less';

export function UserTestPerformanceDetailPage() {
  const [pdfExporting, setPdfExporting] = useState(false);

  const { id, type } = useParams<EvaluationDetailParams>();
  const loginUser = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const [form] = Form.useForm();
  const client = useApolloClient();

  /**目标详情还是考核详情 */
  const isGoal = type === 'byGoal';

  const { loading, data, refetch } = usePerformance({
    variables: {
      type: 'TP',
      subType: isGoal ? 'TARGET' : 'EVAL',
      id: Number(id),
    },
  });

  /**是否为登陆人的绩效 */
  const isLoginUserKpi = useMemo(
    () => !!data && !!data.performance && Number(data.performance.user.id) === loginUser.userId!,
    [data, loginUser.userId]
  );

  /**当前处理人为登录人 */
  const isCurrentHandler = useMemo(
    () => !!data && !!data.performance && data.performance.isLoggedInUserInCurrentStepUsers,
    [data]
  );

  /**员工自评视角 */
  const selfEvaluate = useMemo(
    () => !isGoal && isLoginUserKpi && isCurrentHandler,
    [isCurrentHandler, isGoal, isLoginUserKpi]
  );

  /**经理评估绩效视角 */
  const isSupervisorEvaluate = useMemo(
    () =>
      !isGoal &&
      isCurrentHandler &&
      !!data &&
      !!data.performance &&
      data?.performance?.evaluationStatus === 'EVAL_WAIT_SUPERIOR',
    [data, isCurrentHandler, isGoal]
  );

  /**
   * 直线已审批完(可展示直线经理评价)
   */
  const hasSuperiorEvaluated = useMemo(
    () =>
      !!data &&
      !!data.performance &&
      !!data.performance.evaluationStatus &&
      ['EVAL_WAIT_SEC_SUPERIOR', 'EVAL_WAIT_HR', 'EVAL_WAIT_AREA_HR', 'EVAL_FINISHED'].includes(
        data.performance.evaluationStatus
      ),
    [data]
  );

  const surpervisorEvaluationInfos = useMemo(() => {
    const nodes =
      data &&
      data.performance &&
      data.performance?.evaluations?.find(node => node.type === 'EVAL_SUPERIOR');
    if (nodes && nodes.users.length > 0) {
      return nodes.users[0];
    }
    return;
  }, [data]);

  const selfComprehensive = useMemo(() => {
    if (!data || !data.performance) {
      return;
    }
    const evaluation = data.performance?.evaluations?.find(node => node.type === 'SELF_EVAL');
    if (evaluation) {
      return evaluation && evaluation.users[0] ? evaluation.users[0].comments?.summary : '';
    }
    return;
  }, [data]);

  const generateActions = useCallback(() => {
    if (!data || !data.performance) {
      return [];
    }
    /**
     *  绩效员工为登录人
     *  1. 目标待审批 、 评价待直线审批   显示撤回
     *  2. 目标已通过且考核未提交审批且未过试用期 显示目标计划修改
     *  3. 绩效待自评且为处理人 显示提交/保存自评
     */
    if (isLoginUserKpi) {
      if (
        isGoal
          ? data.performance.objectiveStatus === 'TARGET_WAIT_SUPERIOR'
          : data.performance.evaluationStatus === 'EVAL_WAIT_SUPERIOR'
      ) {
        return [
          <RevokePerformanceButton
            key="revoke"
            id={Number(id)}
            subType={isGoal ? 'GOAL' : 'EVAL'}
          />,
        ];
      }
      if (
        isGoal &&
        data.performance.objectiveStatus === 'TARGET_FINISHED' &&
        data.performance.evaluationStatus &&
        ['INIT', 'SELF_EVAL', 'EVAL_BACK'].includes(data.performance.evaluationStatus) &&
        moment(data.performance.user.hiredAt).add(6, 'month').diff(moment()) > 0
      ) {
        return [<ChangeGoalsButton key="edit" userId={loginUser.userId!.toString()} />];
      }
      if (selfEvaluate) {
        return [<SelfEvaluateButtons key="self-evaluate" />];
      }
      return [];
    }
    /**
     *  绩效当前处理人为登录人
     * 1. 显示 审批(同意/退回)按钮组
     */
    if (isCurrentHandler) {
      return [
        <PerformanceApproveButtonGroup
          key="approve-buttons"
          id={data.performance.id!}
          subType={isGoal ? 'GOAL' : 'EVAL'}
          currentStep={
            (isGoal
              ? data.performance.objectiveStatus
              : data.performance.evaluationStatus) as ApproveStatus
          }
        />,
      ];
    }

    /**
     * 目标、考核完成 显示导出PDF按钮
     */
    if (
      isGoal
        ? data.performance.objectiveStatus === 'TARGET_FINISHED'
        : data.performance.evaluationStatus === 'EVAL_FINISHED'
    ) {
      return [
        <DownloadPdfButton
          key="download"
          pdfName={isGoal ? '目标详情' : '考核详情'}
          exportElement={document.getElementById('page_test-performance_content')}
          disabled={pdfExporting}
          beforeDownload={() => {
            setPdfExporting(true);
            return new Promise(resolve => {
              setTimeout(() => {
                resolve();
              }, 200);
            });
          }}
          onFinish={() => {
            setPdfExporting(false);
          }}
        />,
      ];
    }

    return [];
  }, [
    data,
    isGoal,
    isLoginUserKpi,
    isCurrentHandler,
    pdfExporting,
    selfEvaluate,
    id,
    loginUser.userId,
  ]);

  return (
    <PerformanceDetailContext.Provider
      value={[
        {
          performance: data?.performance ?? null,
          formInstance: form,
          selfEvaluate,
          isSupervisorEvaluate,
          isGoal,
          isLoginUserKpi,
          hasSuperiorEvaluated,
        },
        {
          onLoadData: refetch,
        },
      ]}
    >
      <div id="page_test-performance_content">
        <Row
          style={{
            height: pdfExporting
              ? 'auto'
              : generateActions().length > 0
                ? 'calc(var(--content-height) - 48px)'
                : 'calc(var(--content-height)',
          }}
          gutter={[16, 24]}
        >
          <Col style={{ height: '100%', overflowY: 'auto' }} span={18}>
            {data && data.performance ? (
              <Form
                style={{ width: '100%', height: '100%', overflowY: 'auto', display: 'flex' }}
                layout="vertical"
                form={form}
                colon
                initialValues={{
                  ...data.performance.objectives.reduce(
                    (mapper: Record<string, string | number | null | undefined>, goal) => {
                      Object.keys(goal).forEach(field => {
                        mapper[generateNamePath(goal.id, field)] =
                          goal[field as keyof PerformanceObjectiveJSON];
                      });
                      return mapper;
                    },
                    {}
                  ),
                  selfComprehensive: selfComprehensive,
                  result: surpervisorEvaluationInfos?.result,
                  supervisorComprehensive: surpervisorEvaluationInfos?.comments?.summary,
                }}
                onValuesChange={values => {
                  Object.keys(values).forEach(namePath => {
                    const { goalId, name } = parsedNamePath(namePath);
                    if (goalId && name) {
                      client.cache.updateFragment(
                        {
                          id: `PerformanceObjective:${goalId.trim()}`,
                          fragment: gql`
                            fragment PerformanceObjectiveInfos on PerformanceObjective {
                              status
                              selfEvaluation
                              grade
                            }
                          `,
                        },
                        objective => {
                          return { ...objective, [name]: values[namePath] };
                        }
                      );
                    }
                  });
                }}
              >
                <Space
                  style={{ width: '100%', height: '100%' }}
                  className={classNames(isGoal && styles.performanceContainer)}
                  direction="vertical"
                  size="middle"
                >
                  <Card style={{ height: isGoal ? '100%' : 'auto' }}>
                    <Space
                      style={{ width: '100%', display: 'flex' }}
                      size="middle"
                      direction="vertical"
                    >
                      <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                        <Space align="center">
                          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                            {isLoginUserKpi
                              ? isGoal
                                ? '试用期目标'
                                : '试用期绩效'
                              : `员工${data?.performance?.user.name}${
                                  isGoal ? '目标' : '绩效'
                                }审批请求`}
                          </Typography.Title>
                          {isGoal ? (
                            <GoalsStatusTag status={data.performance.objectiveStatus} />
                          ) : (
                            <EvaluateStatusTag
                              pfType="TP"
                              status={data.performance.evaluationStatus}
                            />
                          )}
                        </Space>
                        {!isLoginUserKpi && (
                          <Space>
                            <Typography.Text type="secondary">复核期间:</Typography.Text>
                            试用期
                            <Typography.Text type="secondary">
                              {isGoal ? '目标计划' : '绩效考核'}:
                            </Typography.Text>
                            {`普洛斯试用期${isGoal ? '目标计划' : '绩效考核'}`}
                          </Space>
                        )}
                      </Space>
                      <StaffTips />
                      <Guidance type="BIZ" />
                      {data.performance.objectives
                        .filter(goal => goal.type === 'BIZ')
                        .map(goal => (
                          <PerformanceItemCard key={goal.id} goal={goal} />
                        ))}
                    </Space>
                  </Card>
                  {!isGoal && (
                    <>
                      {(hasSuperiorEvaluated || isSupervisorEvaluate) && (
                        <Card>
                          <Space style={{ width: '100%' }} size="middle" direction="vertical">
                            <Typography.Title level={5} showBadge>
                              试用期软性能力
                            </Typography.Title>
                            <Guidance type="VALUES" />
                            {data.performance.objectives
                              .filter(goal => goal.type === 'VALUES')
                              .map(goal => (
                                <PerformanceItemCard key={goal.id} goal={goal} />
                              ))}
                          </Space>
                        </Card>
                      )}
                      <ComprehensiveCard />
                    </>
                  )}
                </Space>
              </Form>
            ) : loading ? (
              <Skeleton active />
            ) : (
              <Card style={{ height: '100%' }}>
                <Empty />
              </Card>
            )}
          </Col>
          <Col style={{ height: '100%', overflowY: 'auto' }} span={6}>
            <Space
              style={{ width: '100%', height: '100%', display: 'flex' }}
              className={styles.rightSider}
              size="middle"
              direction="vertical"
            >
              {data && data.performance && (
                <UserInfoCard
                  key="info"
                  userId={Number(data.performance.user.id)}
                  editable={false}
                  needValid={false}
                />
              )}
              <PerformanceApproveProcess
                key="userGroups"
                pfType="TP"
                loading={loading}
                steps={data?.performance?.evaluations ?? []}
                title={isGoal ? '目标设定流程' : '绩效考核流程'}
              />
            </Space>
          </Col>
        </Row>
      </div>
      {generateActions().length > 0 && (
        <FooterToolBar>
          <Space size="middle">{generateActions()}</Space>
        </FooterToolBar>
      )}
    </PerformanceDetailContext.Provider>
  );
}
