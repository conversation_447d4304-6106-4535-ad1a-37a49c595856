import { createContext, useContext } from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';

import type { PerformanceJSON } from '@manyun/hrm.model.performance';

export type PerformanceDetailConsumerProps = [
  {
    performance: PerformanceJSON | null;
    formInstance: FormInstance | null;
    /**
     * 是否是目标详情
     */
    isGoal: boolean;
    /**
     * 是否是员工自评
     */
    selfEvaluate: boolean;
    /**
     * 是否是经理评价
     */
    isSupervisorEvaluate: boolean;
    /**
     * 登陆人是否是绩效归属人
     */
    isLoginUserKpi: boolean;

    /**
     * 直线已审批完
     */
    hasSuperiorEvaluated: boolean;
  },
  {
    onLoadData: () => void;
  }
];

export const PerformanceDetailContext = createContext<PerformanceDetailConsumerProps>([
  {
    performance: null,
    formInstance: null,
    selfEvaluate: false,
    isSupervisorEvaluate: false,
    isGoal: false,
    isLoginUserKpi: false,
    hasSuperiorEvaluated: false,
  },
  {
    onLoadData: () => {},
  },
]);

export const usePerformanceDetail = () => useContext(PerformanceDetailContext);
