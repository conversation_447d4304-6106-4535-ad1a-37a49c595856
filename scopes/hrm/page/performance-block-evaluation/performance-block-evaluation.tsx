/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-8-7
 *
 * @packageDocumentation
 */
import { useApolloClient } from '@apollo/client';
import moment from 'moment';
import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { qs } from '@manyun/base-ui.util.query-string';
import type { SheetHeader } from '@manyun/base-ui.util.xlsx';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import { Link } from '@manyun/dc-brain.navigation.link';
import { usePerformanceBlockEvaluationRecords } from '@manyun/hrm.gql.client.hrm';
import type { PagedPerformanceBlockEvaluation } from '@manyun/hrm.gql.client.hrm';
import { PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH } from '@manyun/hrm.route.hrm-routes';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import type { Space as SpaceNodeType } from '@manyun/resource-hub.ui.location-cascader';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';
import { EVENT_LIST_ROUTE_PATH, generateEventsRoutePath } from '@manyun/ticket.route.ticket-routes';

import { OperationModalView } from './components/operation-modal-view.js';
import { UpdateModalView } from './components/update-status-modal-view.js';

export function PerformanceBlockEvaluation() {
  const client = useApolloClient();

  const [year, setYear] = React.useState<moment.Moment>(moment());
  const [onlyTopEvaluation, setOnlyEvaluation] = React.useState<boolean | undefined>(undefined);
  const [status, setStatus] = React.useState<string | undefined>(undefined);
  const [blockGuidList, setBlockGuidList] = React.useState<string[] | undefined>(undefined);
  const [, { checkCode }] = useAuthorized();

  const { data, loading, refetch } = usePerformanceBlockEvaluationRecords({
    variables: {
      year: year.format('YYYY'),
      qualified: onlyTopEvaluation,
      status: status,
      blockGuidList,
    },
  });

  const tableColumns: (ColumnType<PagedPerformanceBlockEvaluation> & {
    stringify?: SheetHeader<PagedPerformanceBlockEvaluation>['stringify'];
  })[] = [
    {
      title: '机房',
      dataIndex: 'idc',
      fixed: 'left',
      render: val => <SpaceText guid={val} />,
      stringify: val => {
        if (val) {
          const idcLabel = readSpace(client, val);
          return idcLabel?.label ?? '--';
        }
        return '--';
      },
    },
    {
      title: '楼栋',
      fixed: 'left',
      dataIndex: 'blockLabelInValue',
      render: (val, record) => val?.label ?? record.blockGuid ?? '--',
      stringify: (val, record) => val?.label ?? record.blockGuid ?? '--',
    },
    {
      title: '满意度扣分条数',
      dataIndex: 'customerSatisfactionCount',
      sorter: (a, b) =>
        Number(a?.customerSatisfactionCount ?? 0) - Number(b?.customerSatisfactionCount ?? 0),
      stringify: val => val ?? '--',
      render: (val, record) =>
        val > 0 ? (
          <Link
            target="_blank"
            href={`${PERFORMANCE_DAILY_GRADE_RECORD_ROUTE_PATH}?year=${year.format('YYYY')}&type=DAILY&blockGuids=${record.blockGuid}&name=客户满意度`}
          >
            {val}
          </Link>
        ) : (
          val ?? '--'
        ),
    },
    {
      title: 'l2及以上事故数',
      dataIndex: 'accidentsCount',
      sorter: (a, b) => Number(a?.accidentsCount ?? 0) - Number(b?.accidentsCount ?? 0),
      stringify: val => val ?? '--',
      render: val => val ?? '--',
    },
    {
      title: 'l2及以上事件数量',
      dataIndex: 'eventCount',
      sorter: (a, b) => Number(a?.eventCount ?? 0) - Number(b?.eventCount ?? 0),
      stringify: val => val ?? '--',
      render: (val, record) => {
        //事件内测版跳转至内测版本否则跳转事件现行版本
        let path = generateEventsRoutePath({
          blocks: record.blockGuid,
          occurBeginTime: JSON.stringify([
            moment().set('year', Number(record.year)).startOf('year'),
            moment().set('year', Number(record.year)).endOf('year'),
          ]),
          eventLevelList: ['24', '25', '12', '13'],
        });
        if (record.newEvent) {
          path = `${EVENT_LIST_ROUTE_PATH}?${qs.stringify(
            {
              blockTag: record.blockGuid,
              occurBeginTime: moment().set('year', Number(record.year)).startOf('year').valueOf(),
              occurEndTime: moment().set('year', Number(record.year)).endOf('year').valueOf(),
              eventLevelCodeList: ['I1', 'I2', 'S'],
            },
            { arrayFormat: 'comma' }
          )}`;
        }
        return val > 0 ? (
          <Link target="_blank" href={path}>
            {val}
          </Link>
        ) : (
          val ?? '--'
        );
      },
    },
    {
      title: '运维飞检综合评分',
      dataIndex: 'grade',
      sorter: (a, b) => Number(a?.grade ?? 0) - Number(b?.grade ?? 0),
      render: val => val ?? '--',
      stringify: val => val ?? '--',
    },
    {
      title: '符合评优条件',
      dataIndex: 'qualified',
      render: val => (
        <Space>
          <Badge status={val ? 'success' : 'default'} />
          {val ? '是' : '否'}
        </Space>
      ),
      stringify: val => (val ? '是' : '否'),
    },
    {
      title: '状态',
      dataIndex: 'evaluationStatus',
      render: val => (
        <Space>
          <Badge status={val === '3' ? 'success' : 'warning'} />
          {StatusTextMapper[val]}
        </Space>
      ),
      stringify: val => StatusTextMapper[val],
    },
    {
      title: '确认人',
      dataIndex: 'confirmStaffBy',
      render: val => (val ? val.name ?? '--' : '--'),
      stringify: val => (val ? val.name ?? '--' : '--'),
    },
    {
      title: '确认时间',
      dataIndex: 'confirmTime',
      render: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
      stringify: val => (val ? moment(val).format('YYYY-MM-DD HH:mm:ss') : '--'),
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      render: (_, record) => {
        return (
          <Space>
            {(record.accidentsCount === null || record.accidentsCount === undefined) &&
              checkCode('element_hrm-performance-block-evaluation-accident-count') && (
                <UpdateModalView
                  type="accidentCount"
                  record={record}
                  onSuccess={() => {
                    refetch();
                  }}
                />
              )}
            {(record.grade === null || record.grade === undefined) &&
            checkCode('element_hrm-performance-block-evaluation-grade') && (
                <UpdateModalView
                  type="grade"
                  record={record}
                  onSuccess={() => {
                    refetch();
                  }}
                />
              )}
            {checkCode('element_hrm-performance-block-evaluation-operate') && (
              <>
                {record.evaluationStatus === '3' && (
                  <UpdateModalView
                    type="update"
                    record={record}
                    onSuccess={() => {
                      refetch();
                    }}
                  />
                )}
                {record.evaluationStatus === '2' && (
                  <UpdateModalView
                    type="confirm"
                    record={record}
                    onSuccess={() => {
                      refetch();
                    }}
                  />
                )}
              </>
            )}

            {record.id && <OperationModalView id={record.id} />}
          </Space>
        );
      },
    },
  ];

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Typography.Title level={5} showBadge>
          楼栋评优管理
        </Typography.Title>
        <Space
          style={{ justifyContent: 'space-between', width: '100%' }}
          size="middle"
          align="center"
        >
          <Space size="large" align="center">
            <DatePicker
              style={{ width: 118 }}
              picker="year"
              defaultValue={moment()}
              disabledDate={current => {
                return (
                  current && !current.isBetween(moment().set('year', 2023), moment(), 'year', '(]')
                  // 2024年上线，因此之前的数据不允许查询
                );
              }}
              format={value => `${value.format('YYYY')}年度`}
              allowClear={false}
              onChange={value => {
                if (value) {
                  setYear(value);
                }
              }}
            />
            <LocationCascader
              style={{ width: 240 }}
              placeholder="机房楼栋"
              nodeTypes={['IDC', 'BLOCK']}
              allowClear
              authorizedOnly
              onChange={(_, options) => {
                const _options = options as SpaceNodeType[];
                if (_options && _options.length > 0) {
                  setBlockGuidList(
                    _options.length === 1
                      ? [
                          ...(_options[0]?.children ?? []).map(space => space.value),
                          `${_options[0].value}.${_options[0].value}`,
                        ]
                      : [_options[1].value]
                  );
                  return;
                }
                setBlockGuidList(undefined);
              }}
            />
            <Select
              style={{ width: 240 }}
              placeholder="状态"
              options={['1', '2', '3'].map(key => ({
                value: key,
                label: StatusTextMapper[key],
              }))}
              allowClear
              onChange={val => {
                setStatus(val);
              }}
            />
            <Space align="center">
              <Typography.Text>仅看符合评优条件楼栋</Typography.Text>
              <Switch
                size="small"
                onChange={value => {
                  setOnlyEvaluation(value);
                }}
              />
            </Space>
          </Space>
          <FileExport
            showExportFiltered={false}
            data={async () => {
              const newHeaders: SheetHeader<PagedPerformanceBlockEvaluation>[] = tableColumns
                .filter(item => item.dataIndex !== 'action')
                .map(column => ({
                  dataIndex: column.dataIndex as string,
                  title: column.title as string,
                  stringify: column.stringify,
                }));
              saveJSONAsXLSX<PagedPerformanceBlockEvaluation>(
                [
                  {
                    data: data?.performanceBlockEvaluationList.data ?? [],
                    headers: newHeaders,
                  },
                ],
                '楼栋评优管理'
              );
            }}
          />
        </Space>
        <Table<PagedPerformanceBlockEvaluation>
          scroll={{ x: 'max-content' }}
          rowKey="blockGuid"
          loading={loading}
          dataSource={data?.performanceBlockEvaluationList.data ?? []}
          columns={tableColumns}
        />
      </Space>
    </Card>
  );
}

const StatusTextMapper: Record<string, string> = {
  '1': '未填写',
  '2': '待确认',
  '3': '已确认',
};
