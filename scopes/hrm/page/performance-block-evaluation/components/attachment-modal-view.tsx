import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { message } from '@manyun/base-ui.ui.message';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';

export type AttachmentModalViewProps = {
  bizId: string;
  bizType: string;
  extensionType?: string;
};
export const AttachmentModalView = ({
  bizId,
  bizType,
  extensionType,
}: AttachmentModalViewProps) => {
  const [files, setFiles] = React.useState<McUploadFileJSON[]>([]);

  React.useEffect(() => {
    (async () => {
      if (bizId && bizType) {
        const { error, data } = await fetchBizFileInfos({
          targetId: bizId,
          targetType: bizType,
          extensionType: extensionType,
        });

        if (error) {
          message.success(error.message);
          return;
        }
        setFiles(data.data);
      }
    })();
  }, [bizId, bizType, extensionType]);

  if (files.length === 0) {
    return '--';
  }

  return (
    <SimpleFileList
      files={files ?? []}
      children={
        <Button type="link" compact>
          查看
        </Button>
      }
    />
  );
};
