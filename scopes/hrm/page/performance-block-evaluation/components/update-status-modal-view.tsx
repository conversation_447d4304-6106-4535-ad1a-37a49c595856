import UploadOutlined from '@ant-design/icons/es/icons/UploadOutlined';
import React from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { InputNumber } from '@manyun/base-ui.ui.input-number';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload as Upload } from '@manyun/dc-brain.ui.upload';
import type { PagedPerformanceBlockEvaluation } from '@manyun/hrm.gql.client.hrm';
import {
  useConfirmPerformanceBlockEvaluation,
  useMarkPerformanceBlockEvaluation,
  useUpdatePerformanceBlockEvaluation,
  useUpdatePerformanceBlockEvaluationAccidentCount,
} from '@manyun/hrm.gql.client.hrm';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

type OperateType = 'grade' | 'update' | 'confirm' | 'accidentCount';

const OperateTypeTextMapper: Record<OperateType, string> = {
  grade: '填写飞检评分',
  update: '更正',
  confirm: '确认',
  accidentCount: '填写事故数量',
};
export type UpdateModalViewProps = {
  type: OperateType;
  record: PagedPerformanceBlockEvaluation;
  onSuccess?: () => void;
};
export const UpdateModalView = ({ type, record, onSuccess }: UpdateModalViewProps) => {
  const [open, setOpen] = React.useState(false);
  const [form] = Form.useForm();
  const attachments = Form.useWatch('attachments', form);

  const [confirmPerformanceBlockEvaluation, { loading: confirmLoading }] =
    useConfirmPerformanceBlockEvaluation();
  const [markPerformanceBlockEvaluation, { loading: markLoading }] =
    useMarkPerformanceBlockEvaluation();
  const [updatePerformanceBlockEvaluation, { loading: updateLoading }] =
    useUpdatePerformanceBlockEvaluation();
  const [updatePerformanceBlockEvaluationAccidentCount, { loading: updateCountLoading }] =
    useUpdatePerformanceBlockEvaluationAccidentCount();

  const onHandleSuccess = React.useCallback(() => {
    setOpen(false);
    message.success('操作成功');
    onSuccess?.();
  }, [onSuccess]);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
        }}
      >
        {OperateTypeTextMapper[type]}
      </Button>
      <Modal
        title={OperateTypeTextMapper[type]}
        width={740}
        open={open}
        okText="提交"
        okButtonProps={{
          loading: confirmLoading || markLoading || updateLoading || updateCountLoading,
        }}
        cancelButtonProps={{
          disabled: confirmLoading || markLoading || updateLoading || updateCountLoading,
        }}
        onCancel={() => {
          form.resetFields();
          setOpen(false);
        }}
        onOk={() => {
          form.validateFields().then(async values => {
            const files = (values.attachments ?? []).map((json: McUploadFileJSON) =>
              McUploadFile.fromJSON(json).toJSON()
            );
            switch (type) {
              case 'confirm':
                const { data: confirmResult } = await confirmPerformanceBlockEvaluation({
                  variables: {
                    params: {
                      id: record.id!,
                      customerSatisfactionCount: values.customerSatisfactionCount,
                      accidentsCount: values.accidentsCount,
                      eventCount: values.eventCount,
                      grade: values.grade,
                      content: values.note,
                      files: files.length > 0 ? files : undefined,
                    },
                  },
                });
                if (confirmResult?.confirmPerformanceBlockEvaluation?.success) {
                  onHandleSuccess();
                }
                break;
              case 'grade':
                const { data: markResult } = await markPerformanceBlockEvaluation({
                  variables: {
                    params: {
                      year: record.year,
                      idc: record.idc!,
                      blockGuid: record.blockGuid!,
                      grade: values.grade,
                      content: values.note,
                      files: files.length > 0 ? files : undefined,
                    },
                  },
                });
                if (markResult?.markPerformanceBlockEvaluation?.success) {
                  onHandleSuccess();
                }
                break;
              case 'update':
                const { data: updateResult } = await updatePerformanceBlockEvaluation({
                  variables: {
                    params: {
                      id: record.id!,
                      customerSatisfactionCount: values.customerSatisfactionCount,
                      eventCount: values.eventCount,
                      accidentsCount: values.accidentsCount,
                      grade: values.grade,
                      content: values.note,
                      files: files.length > 0 ? files : undefined,
                    },
                  },
                });
                if (updateResult?.updatePerformanceBlockEvaluation?.success) {
                  onHandleSuccess();
                }
                break;
              case 'accidentCount':
                const { data: updatePerformanceBlockEvaluationAccidentCountResult } =
                  await updatePerformanceBlockEvaluationAccidentCount({
                    variables: {
                      params: {
                        year: record.year,
                        idc: record.idc!,
                        blockGuid: record.blockGuid!,
                        content: values.note,
                        files: files.length > 0 ? files : undefined,
                        accidentsCount: values.accidentsCount,
                      },
                    },
                  });
                if (
                  updatePerformanceBlockEvaluationAccidentCountResult
                    ?.updatePerformanceBlockEvaluationAccidentCount?.success
                ) {
                  onHandleSuccess();
                }
                break;
            }
          });
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size="large">
          <Alert description={<SpaceText guid={record.blockGuid ?? record.idc ?? ''} />} />
          <Form
            form={form}
            labelCol={{ span: 5 }}
            initialValues={{
              customerSatisfactionCount: record.customerSatisfactionCount,
              accidentsCount: record.accidentsCount,
              eventCount: record.eventCount,
              grade: record.grade,
            }}
          >
            {['update', 'confirm'].includes(type) && (
              <>
                <Form.Item
                  label="满意度扣分条数"
                  name="customerSatisfactionCount"
                  rules={[{ required: true, message: '请输入满意度扣分条数' }]}
                >
                  <InputNumber style={{ width: 110 }} precision={0} min={0} max={100} />
                </Form.Item>
                <Form.Item
                  label="l2及以上事故数"
                  name="accidentsCount"
                  rules={[{ required: true, message: '请输入l2及以上事故数' }]}
                >
                  <InputNumber style={{ width: 110 }} precision={0} min={0} max={100} />
                </Form.Item>
                <Form.Item
                  label="l2及以上事件数量"
                  name="eventCount"
                  rules={[{ required: true, message: '请输入l2及以上事件数量' }]}
                >
                  <InputNumber style={{ width: 110 }} precision={0} min={0} max={1000} />
                </Form.Item>
              </>
            )}
            {type === 'accidentCount' ? (
              <Form.Item
                label="l2及以上事故数"
                name="accidentsCount"
                rules={[{ required: true, message: '请输入l2及以上事故数' }]}
              >
                <InputNumber style={{ width: 110 }} precision={0} min={0} max={100} />
              </Form.Item>
            ) : (
              <Form.Item
                label="运维飞检综合评分"
                name="grade"
                rules={[{ required: true, message: '请输入运维飞检综合评分' }]}
              >
                <InputNumber style={{ width: 110 }} precision={2} min={0} max={100} />
              </Form.Item>
            )}

            <Form.Item
              name="attachments"
              label="附件"
              valuePropName="fileList"
              getValueFromEvent={value => {
                if (typeof value === 'object') {
                  return value.fileList;
                }
              }}
            >
              <Upload
                accept=".jpg,.png,.jpeg,.gif,.txt,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.pdf"
                maxFileSize={20}
                maxCount={5}
                allowDelete
                showAccept
                showUploadList
                disabled={(attachments ?? []).length >= 5}
              >
                <Button
                  type="default"
                  disabled={(attachments ?? []).length >= 5}
                  icon={<UploadOutlined />}
                >
                  点此上传
                </Button>
              </Upload>
            </Form.Item>
            <Form.Item
              label="备注"
              name="note"
              rules={[{ max: 300, message: '最多输入 300 个字符！' }]}
            >
              <Input.TextArea style={{ width: 372 }} />
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
};
