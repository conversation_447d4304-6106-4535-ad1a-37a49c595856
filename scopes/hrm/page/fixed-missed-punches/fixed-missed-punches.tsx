import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useHistory, useLocation } from 'react-router-dom';
import { useShallowCompareEffect } from 'react-use';

import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { getClockInLocales } from '@manyun/hrm.model.clock-in';
import type { ClockInJSON } from '@manyun/hrm.model.clock-in';
import {
  SUPPLY_CHECK_NEW_ROUTE_PATH,
  generateSupplyCheckDetaillocation,
} from '@manyun/hrm.route.hrm-routes';
import { fetchPagedFixedMissedPunches } from '@manyun/hrm.service.fetch-paged-fixed-missed-punches';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-fixed-missed-punches';
import { RevokeAlterProcess } from '@manyun/hrm.ui.revoke-alter-process';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

type Fields = SvcQuery & {
  applyName?: string;
  idcTags?: string[];
  blockTags?: string[];
};

export function FixedMissedPunches() {
  const locales = useMemo(() => getClockInLocales(), []);
  const { search } = useLocation();
  const { page, pageSize, bizId, applyId, applyName, applyTime, checkTime, idcTags, blockTags } =
    getLocationSearchMap<Partial<Fields>>(search, {
      parseNumbers: true,
      arrayKeys: ['blockTags', 'idcTags'],
    });

  const history = useHistory();
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);
  const [fields, setFields] = useState<Fields>({
    page: page ?? 1,
    pageSize: pageSize ?? 10,
    bizId,
    applyId,
    applyName,
    applyTime: applyTime,
    checkTime: checkTime,
    idcTags: idcTags,
    blockTags: blockTags,
  });
  const [state, setState] = useState<{
    data: ClockInJSON[];
    total: number;
  }>({
    data: [],
    total: 0,
  });

  const onLoadData = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchPagedFixedMissedPunches({
      ...fields,
      blockTags:
        fields.idcTags && fields.idcTags[0] && (fields.blockTags ?? []).length === 0
          ? [`${fields.idcTags[0]}.${fields.idcTags[0]}`]
          : fields.blockTags,
      checkWay: 'SUPPLY',
    });
    setLoading(false);

    if (error) {
      message.error(error.message);
      return;
    }
    setState({ data: data.data, total: data.total });
  }, [fields]);

  useEffect(() => {
    setLocationSearch({ ...fields });
    onLoadData();
  }, [onLoadData, fields]);

  useShallowCompareEffect(() => {
    form.setFieldsValue({
      bizId: bizId,
      blockTags:
        idcTags || blockTags
          ? {
              value: idcTags ?? blockTags,
              idcTags: idcTags,
              blockTags: blockTags,
            }
          : undefined,
      applyId: applyId ? { name: applyName, lable: applyName, value: applyId } : undefined,
      applyTime: applyTime ? [moment(applyTime[0]), moment(applyTime[1])] : undefined,
      checkTime: checkTime ? [moment(checkTime[0]), moment(checkTime[1])] : undefined,
    });
  }, [applyId, applyName, applyTime, bizId, blockTags, checkTime, form, idcTags]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card>
        <QueryFilter
          form={form}
          items={[
            {
              label: locales.missedPunchesFix.bizId ?? '补卡ID',
              name: 'bizId',
              control: <Input allowClear />,
            },
            {
              label: locales.location ?? '位置',
              name: 'blockTags',
              control: <LocationTreeSelect allowClear authorizedOnly />,
              getValueFromEvent: (value, _, { dataRef }) => {
                let idcTags: string[] | undefined = [],
                  blockTags: string[] = [];
                if (typeof dataRef === 'object') {
                  if (dataRef.type === 'IDC') {
                    if (dataRef.value) {
                      idcTags.push(dataRef.value);
                    }
                    if (dataRef.children) {
                      blockTags = dataRef.children.map((child: { value: string }) => child.value);
                    }
                  }
                  if (dataRef.type === 'BLOCK' && dataRef.value) {
                    blockTags.push(dataRef.value);
                    idcTags = undefined;
                  }
                }
                return {
                  value,
                  idcTags,
                  blockTags,
                };
              },
              getValueProps: value => {
                return { value: value?.value };
              },
            },
            {
              label: locales.applyBy._self ?? '申请人',
              name: 'applyId',
              control: <UserSelect allowClear />,
            },
            {
              label: locales.applyTime._self ?? '申请时间',
              name: 'applyTime',
              span: 2,
              control: <DatePicker.RangePicker format="YYYY-MM-DD HH:mm:ss" showTime />,
            },
            {
              label: locales.missedPunchesFix.punchTime._self ?? '补卡时间',
              name: 'checkTime',
              span: 2,
              control: <DatePicker.RangePicker format="YYYY-MM-DD HH:mm:ss" showTime />,
            },
          ]}
          onSearch={values => {
            const searchField: Record<string, string | number[] | undefined> = {};
            Object.keys(values).forEach(key => {
              const value = values[key];
              if (Array.isArray(value) && value.length === 2) {
                searchField[key] = value
                  ? [moment(value[0]).valueOf(), moment(value[1]).valueOf()]
                  : undefined;
              } else if (key === 'applyId') {
                searchField.applyId = value?.value;
                searchField.applyName = value?.label;
              } else if (key === 'blockTags') {
                searchField.blockTags = value?.blockTags;
                searchField.idcTags = value?.idcTags;
              } else {
                searchField[key] = value ? value.trim() : undefined;
              }
            });
            setFields(pre => ({ ...pre, page: 1, pageSize: 10, ...searchField }));
          }}
          onReset={() => {
            setFields({ page: 1, pageSize: 10 });
          }}
        />
      </Card>
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <Button
            type="primary"
            onClick={() => {
              history.push(SUPPLY_CHECK_NEW_ROUTE_PATH);
            }}
          >
            {locales.missedPunchesFix.addText ?? '新建'}
          </Button>
          <Table
            rowKey="id"
            loading={loading}
            scroll={{ x: 'max-content' }}
            dataSource={state.data}
            pagination={{
              total: state.total,
              current: fields.page,
              pageSize: fields.pageSize,
            }}
            columns={[
              {
                title: locales.missedPunchesFix.bizId ?? '补卡ID',
                dataIndex: 'bizId',
                fixed: 'left',
                render: val => (
                  <Link to={generateSupplyCheckDetaillocation({ id: val })}>{val}</Link>
                ),
              },
              {
                title: locales.missedPunchesFix.title._self ?? '标题',
                dataIndex: 'title',
              },
              {
                title: locales.missedPunchesFix.punchTime._self ?? '补卡时间',
                dataIndex: 'punchTime',
              },
              {
                title: locales.location ?? '位置',
                dataIndex: 'location',
              },
              {
                title: locales.applyBy._self ?? '申请人',
                dataIndex: 'applyUser',
                render: (_, record) => (
                  <UserLink id={record.applyUser?.id as number} name={record.applyUser?.name} />
                ),
              },
              {
                title: locales.applyTime._self ?? '申请时间',
                dataIndex: 'applyTime',
              },
              {
                title: locales.missedPunchesFix.approveStatus ?? '状态',
                dataIndex: 'approveStatus',
                render: val => <ApprovalStatusText approveStatus={val} />,
              },
              {
                title: locales.action ?? '操作',
                dataIndex: 'action',
                fixed: 'right',
                render: (_, record) =>
                  record.approveStatus === 'APPROVING' && (
                    <RevokeAlterProcess
                      type="link"
                      bizId={record.bizId!}
                      bizType="SUPPLY"
                      recordUser={{ applyId: record.applyUser?.id! }}
                      compact
                      onOk={() => onLoadData()}
                    />
                  ),
              },
            ]}
            onChange={pagination => {
              setFields(pre => ({
                ...pre,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}
