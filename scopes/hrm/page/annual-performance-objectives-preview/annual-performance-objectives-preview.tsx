import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import React, { useEffect, useState } from 'react';

import { Container } from '@manyun/base-ui.ui.container';
import { Space } from '@manyun/base-ui.ui.space';
import { useLazyAnnualPerformanceObjectives } from '@manyun/hrm.gql.client.hrm';
import { AnnualPerformanceObjectiveCard } from '@manyun/hrm.ui.annual-performance-objective-card';
import { AnnualPerformanceScoringPopover } from '@manyun/hrm.ui.annual-performance-scoring-popover';
import { PerformancePositionTabs } from '@manyun/hrm.ui.performance-position';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import styles from './annual-performance-objectives-preview.module.less';

export function AnnualPerformanceObjectivesPreview() {
  const [idcTag, setIdcTag] = useState<string | undefined>();
  const [position, setPosition] = useState<string | undefined>();

  const [fetch, { loading, data }] = useLazyAnnualPerformanceObjectives();

  useEffect(() => {
    if (idcTag && position) {
      fetch({
        variables: {
          query: {
            page: 1,
            pageSize: 2000,
            resourceCodes: ['ALL', idcTag],
            performancePosition: position,
            types: ['DAILY', 'DAY_PLUS'],
          },
        },
      });
    }
  }, [fetch, idcTag, position]);

  return (
    <Space
      style={{ width: '100%' }}
      className={styles.objectivesPreview}
      direction="vertical"
      size="middle"
    >
      <Container size="large" className={styles.headerContainer}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            <EnvironmentOutlined />
            <LocationCascader
              style={{ width: 238 }}
              authorizedOnly
              showSearch
              allowClear={false}
              value={idcTag ? [idcTag] : undefined}
              nodeTypes={['IDC']}
              onChange={value => {
                if (Array.isArray(value)) {
                  setIdcTag(value[0] as string);
                }
              }}
              onTreeDataChange={values => {
                if (values && values[0]) {
                  setIdcTag(values[0].value);
                }
              }}
            />
          </Space>
          <PerformancePositionTabs
            className={styles.tabs}
            value={position}
            onChange={value => {
              setPosition(value);
            }}
          />
        </Space>
      </Container>
      <AnnualPerformanceObjectiveCard
        title="目标预览"
        loading={loading}
        dataSource={data?.paginatedAnnualPerformanceObjectives.data ?? []}
        extra={
          <AnnualPerformanceScoringPopover
            style={{ width: 344 }}
            ruleTextVersion={PERFORMANCE_SECOND_VERSION}
          />
        }
        typeTextVersion={PERFORMANCE_SECOND_VERSION}
      />
    </Space>
  );
}
