import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { BALANCE_TYPE_MAPPER, BalanceType } from '@manyun/hrm.service.fetch-balance-change-record';

export const BALANCE_TYPE_OPTIONS: { label: string; value: BalanceType }[] = [
  {
    value: BalanceType.CARRY_OVER_COMPANY_LEAVE,
    label: BALANCE_TYPE_MAPPER[BalanceType.CARRY_OVER_COMPANY_LEAVE],
  },
  {
    value: BalanceType.COMPANY_LEAVE,
    label: BALANCE_TYPE_MAPPER[BalanceType.COMPANY_LEAVE],
  },
  {
    value: BalanceType.PAID_LEAVE,
    label: BALANCE_TYPE_MAPPER[BalanceType.PAID_LEAVE],
  },
  {
    value: BalanceType.SICK_SALARY,
    label: BALANCE_TYPE_MAPPER[BalanceType.SICK_SALARY],
  },
  {
    value: BalanceType.STATUTORY_ANNUAL_LEAVE,
    label: BALANCE_TYPE_MAPPER[BalanceType.STATUTORY_ANNUAL_LEAVE],
  },
];

export const BalanceTypeSelect = React.forwardRef(
  ({ ...props }: SelectProps, ref?: React.Ref<RefSelectProps>) => {
    return (
      <Select
        ref={ref}
        showSearch
        allowClear
        mode="multiple"
        maxTagCount={1}
        style={{ width: 210 }}
        options={BALANCE_TYPE_OPTIONS}
        filterOption={(input, option) =>
          (option?.label as string).toLowerCase().includes(input.toLowerCase())
        }
        {...props}
      />
    );
  }
);

BalanceTypeSelect.displayName = 'BalanceTypeSelect';
