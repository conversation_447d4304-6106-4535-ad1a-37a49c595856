import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import { selectMe } from '@manyun/auth-hub.state.user';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { Upload, checkFileType } from '@manyun/dc-brain.ui.upload';
import { useBatchAddHolidayBalance } from '@manyun/hrm.gql.client.hrm';
import type {
  MutationBatchAddHolidayBalanceArgs,
  UserHolidayBalance,
} from '@manyun/hrm.gql.client.hrm';
import { importHolidayBalance } from '@manyun/hrm.service.import-holiday-balance';
import type {
  ApiResponseData,
  UserHolidayBalanceInfos,
} from '@manyun/hrm.service.import-holiday-balance';
import { webRequest } from '@manyun/service.request';

export type ImportModalViewProps = {
  onSuccess?: () => void;
};
export const ImportModalView = ({ onSuccess }: ImportModalViewProps) => {
  const [open, setOpen] = useState(false);
  const [importLoading, setImportLoading] = useState(false);
  const [data, setData] = useState<ApiResponseData | null>(null);
  const [batchAddHolidayBalance, { loading }] = useBatchAddHolidayBalance();

  const { userId, username } = useSelector(
    selectMe,
    (left, right) => left.userId === right.userId && left.username === right.username
  );

  const canSubmit = useMemo(() => {
    return (data?.errorCheckDtoList ?? []).length === 0 && (data?.correctDtoList ?? []).length > 0;
  }, [data?.correctDtoList, data?.errorCheckDtoList]);

  const dataSet = useMemo(
    () =>
      [
        ...(data?.correctDtoList ?? []).map(data => ({ dto: data })),
        ...(data?.errorCheckDtoList ?? []).map(dt => ({ ...dt, dto: dt.errDto ?? {} })),
      ] as { dto: UserHolidayBalanceInfos; errMessage?: UserHolidayBalanceInfos }[],
    [data?.correctDtoList, data?.errorCheckDtoList]
  );

  const getToolTip = useCallback(
    ({
      errMessage,
      field,
      value,
    }: {
      field: keyof UserHolidayBalanceInfos;
      errMessage?: UserHolidayBalanceInfos;
      value: string | number;
    }) => {
      if (errMessage?.[field]) {
        return (
          <Explanation
            style={{ color: 'inherit', fontSize: 'inherit' }}
            iconType="question"
            tooltip={{ title: errMessage?.[field] }}
          >
            <Typography.Text type="danger">{value}</Typography.Text>
          </Explanation>
        );
      }
      return value;
    },
    []
  );

  return (
    <>
      <Button
        type="primary"
        onClick={() => {
          setOpen(true);
        }}
      >
        导入
      </Button>
      <Modal
        width="85%"
        open={open}
        title="假期额度导入"
        okButtonProps={{
          disabled: !canSubmit,
          loading: loading,
        }}
        afterClose={() => {
          setData(null);
        }}
        destroyOnClose
        onCancel={() => {
          setOpen(false);
        }}
        onOk={async () => {
          const { data: result } = await batchAddHolidayBalance({
            variables: {
              data: dataSet.map(dt => dt.dto) as UserHolidayBalance[],
              fileIdList: (data?.fileInfoList ?? []).map(backendFile =>
                McUploadFile.fromApiObject({
                  ...backendFile,
                  uploadBy: userId!,
                  uploadByName: username!,
                }).toJSON()
              ),
            } as MutationBatchAddHolidayBalanceArgs,
          });
          if (result?.batchAddHolidayBalance?.success) {
            message.success('导入成功');
            setOpen(false);
            onSuccess?.();
            return;
          }
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size="middle">
          {canSubmit && (
            <Alert
              message="是否确认导入假期?"
              description="导入后，将覆盖对应人员原有的假期额度，请谨慎操作。"
              type="warning"
              showIcon
            />
          )}

          <Space
            style={{ width: '100%', justifyContent: 'space-between' }}
            size="middle"
            align="center"
          >
            <Space size="middle" align="center">
              <Upload
                accept=".xls,.xlsx"
                beforeUpload={file => {
                  const accepted = checkFileType('.xls,.xlsx', file);
                  return accepted;
                }}
                maxCount={1}
                customRequest={async ({ file }) => {
                  setImportLoading(true);
                  const fd = new FormData();
                  fd.append('file', file);
                  const { data, error } = await importHolidayBalance(fd);
                  setImportLoading(false);
                  if (error) {
                    message.error(error.message);
                    setData(null);
                    return;
                  }
                  message.success('操作成功');
                  setData(data);
                }}
                showUploadList={false}
              >
                <Button type="primary">导入</Button>
              </Upload>
              <Button
                onClick={() => {
                  window.open(
                    `${webRequest.axiosInstance.defaults.baseURL}/dctrans/balance/glp/staff/download`
                  );
                }}
              >
                下载模板
              </Button>
            </Space>
            {(data?.errorCheckDtoList ?? []).length > 0 && (
              <Typography.Text>
                <Typography.Text type="danger">
                  {(data?.errorCheckDtoList ?? []).length}
                </Typography.Text>
                条不符合填写规范，请修改文件后重新导入
              </Typography.Text>
            )}
          </Space>
          <Table
            size="middle"
            scroll={{ x: 'max-content', y: '500px' }}
            loading={importLoading}
            dataSource={dataSet}
            columns={[
              {
                title: '员工工号',
                fixed: 'left',
                dataIndex: ['dto', 'jobNumber'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'jobNumber',
                  });
                },
              },
              {
                title: '姓名',
                fixed: 'left',
                dataIndex: ['dto', 'userName'],
                width: 80,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'userName',
                  });
                },
              },
              {
                title: '当年享受法定年休假天数',
                dataIndex: ['dto', 'statutoryBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'statutoryBalance',
                  });
                },
              },
              {
                title: '当年享受公司年休假天数',
                dataIndex: ['dto', 'companyBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'companyBalance',
                  });
                },
              },
              {
                title: '上年结转年假天数',
                dataIndex: ['dto', 'carryOverBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'carryOverBalance',
                  });
                },
              },
              {
                title: '当年已请年假天数',
                dataIndex: ['dto', 'totalUsedBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'totalUsedBalance',
                  });
                },
              },
              {
                title: '当年可请年假天数',
                dataIndex: ['dto', 'totalAvailableBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'totalAvailableBalance',
                  });
                },
              },
              {
                title: '当年已请法定年假天数',
                dataIndex: ['dto', 'statutoryUsedBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'statutoryUsedBalance',
                  });
                },
              },
              {
                title: '当年已请上年结转公司年假天数',
                dataIndex: ['dto', 'carryOverUsedBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'carryOverUsedBalance',
                  });
                },
              },
              {
                title: '当年已请公司年假天数',
                dataIndex: ['dto', 'companyUsedBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'companyUsedBalance',
                  });
                },
              },
              {
                title: '当年可请法定年假天数',
                dataIndex: ['dto', 'statutoryAvailableBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'statutoryAvailableBalance',
                  });
                },
              },
              {
                title: '当年可请上年结转公司年假天数',
                dataIndex: ['dto', 'carryOverAvailableBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'carryOverAvailableBalance',
                  });
                },
              },
              {
                title: '当年可请公司年假天数',
                dataIndex: ['dto', 'companyAvailableBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'companyAvailableBalance',
                  });
                },
              },
              {
                title: '当年享受全薪病假天数',
                dataIndex: ['dto', 'sickSalaryBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'sickSalaryBalance',
                  });
                },
              },
              {
                title: '当年已请全薪病假天数',
                dataIndex: ['dto', 'sickSalaryUsedBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'sickSalaryUsedBalance',
                  });
                },
              },
              {
                title: '当年可请全薪病假天数',
                dataIndex: ['dto', 'sickSalaryAvailableBalance'],
                width: 96,
                render: (val, { errMessage }: { errMessage?: UserHolidayBalanceInfos }) => {
                  return getToolTip({
                    errMessage,
                    value: val,
                    field: 'sickSalaryAvailableBalance',
                  });
                },
              },
            ]}
          />
        </Space>
      </Modal>
    </>
  );
};
