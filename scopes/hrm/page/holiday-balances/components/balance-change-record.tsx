import React, { useCallback, useEffect, useState } from 'react';

import dayjs from 'dayjs';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import type { BalanceType, ChangeRecord } from '@manyun/hrm.service.fetch-balance-change-record';
import { fetchBalanceChangeRecord } from '@manyun/hrm.service.fetch-balance-change-record';
import { BalanceChangeRecordTable } from '@manyun/hrm.ui.balance-change-record-table';

import { BALANCE_TYPE_OPTIONS, BalanceTypeSelect } from './balance-type-select';

export type ChangeRecordProps = {
  userId: number;
  year: string;
};

export const BalanceChangeRecord = ({ userId, year }: ChangeRecordProps) => {
  const [visible, setVisible] = useState(false);
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<ChangeRecord[]>([]);
  const [fields, setFields] = useState<{ types?: BalanceType[]; timeRange?: [number, number] }>({});
  const [form] = Form.useForm();

  const _fetchChangeRecords = useCallback(async () => {
    setLoading(true);
    const { error, data } = await fetchBalanceChangeRecord({
      staffId: userId,
      balanceTypes: fields.types ?? BALANCE_TYPE_OPTIONS.map(opt => opt.value),
      timeRange: fields.timeRange ?? [
        moment().year(Number(year)).startOf('year').valueOf(),
        moment().year(Number(year)).endOf('year').valueOf(),
      ],
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setData(data.data);
  }, [fields.timeRange, fields.types, userId, year]);

  useEffect(() => {
    if (visible) {
      _fetchChangeRecords();
    }
  }, [_fetchChangeRecords, visible]);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setVisible(true);
          form.resetFields();
          setFields({});
        }}
      >
        额度变更记录
      </Button>
      <Drawer
        width={952}
        title="额度变更记录"
        open={visible}
        footer={null}
        destroyOnClose
        onClose={() => {
          setVisible(false);
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }} size={16}>
          <Form layout="inline" form={form} colon={false}>
            <Form.Item label="假期名称" name="types">
              <BalanceTypeSelect />
            </Form.Item>
            {year && (
              <Form.Item label="变更时间" name="timeRange">
                <DatePicker.RangePicker
                  style={{ width: 240 }}
                  defaultValue={[
                    moment().year(Number(year)).startOf('year'),
                    moment().year(Number(year)).endOf('year'),
                  ]}
                  format="YYYY-MM-DD"
                  allowClear={false}
                />
              </Form.Item>
            )}

            <Form.Item>
              <Button
                type="primary"
                onClick={() => {
                  const searchValues = form.getFieldsValue();
                  setFields({
                    types:
                      Array.isArray(searchValues.types) && searchValues.types.length === 0
                        ? undefined
                        : searchValues.types,
                    timeRange: searchValues.timeRange
                      ? [
                          dayjs(searchValues.timeRange[0]).startOf('day').valueOf(),
                          dayjs(searchValues.timeRange[1]).endOf('day').valueOf(),
                        ]
                      : undefined,
                  });
                }}
              >
                搜索
              </Button>
            </Form.Item>
          </Form>
          <BalanceChangeRecordTable
            rowKey="id"
            showColumns={[
              'balanceType',
              'startBalance',
              'reason',
              'createTime',
              'operatorId',
              'fileList',
            ]}
            loading={loading}
            dataSource={data}
            pagination={false}
          />
        </Space>
      </Drawer>
    </>
  );
};
