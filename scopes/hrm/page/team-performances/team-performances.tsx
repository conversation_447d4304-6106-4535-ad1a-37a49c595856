import moment from 'moment';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useParams } from 'react-router';
import { useLocation } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { usePaginatedPerformances, usePerformancesCountStatic } from '@manyun/hrm.gql.client.hrm';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import type { TeamPerformancesParams } from '@manyun/hrm.route.hrm-routes';

// import { HandOverButton } from './components/hand-over-button';
import { EvaluationsList } from './components/performance-evaluations';
import { GoalsList } from './components/performance-goals';
import { TeamPerformancesContext } from './team-performances-context';
import type { Fields, Pagination, SearchProps, TabKey } from './team-performances-context';
import { transformQuery } from './util';

export type SearchParams = {
  tabKey: TabKey;
} & Pagination &
  Fields;

export function TeamPerformances() {
  const { type } = useParams<TeamPerformancesParams>();
  const { search } = useLocation();
  const {
    tabKey,
    page: defaultPage,
    pageSize: defaultPageSize,
    onlyMe,
    ...restFields
  } = getLocationSearchMap<
    Partial<
      SearchParams & {
        onlyMe: boolean;
      }
    >
  >(search, {
    parseNumbers: true,
    arrayKeys: ['goalsStatus', 'evaluationStatus', 'region', 'idcTags', 'blockGuids'],
  });
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const defaultActiveTabKey = tabKey ?? 'goals';
  const defaultFields = useDeepCompareMemo(
    () => ({
      ...restFields,
      year: type === 'annual' ? restFields.year ?? moment().year().toString() : undefined,
      currentHandlerId: onlyMe ? userId! : restFields.currentHandlerId,
    }),
    [onlyMe, restFields, type, userId]
  );
  const defaultPagination = useDeepCompareMemo(
    () => ({ page: defaultPage ?? 1, pageSize: defaultPageSize ?? 10 }),
    [defaultPage, defaultPageSize]
  );
  const [activeTabKey, setActiveTabKey] = useState<TabKey>(defaultActiveTabKey);
  const [pagination, setPagination] = useState<Pagination>(defaultPagination);
  const [fields, setFields] = useState<Fields | undefined>({
    ...defaultFields,
  });
  const [selectedRows, setSelectedRows] = useState<PerformanceJSON[]>([]);
  const { loading, data, refetch } = usePaginatedPerformances({
    variables: {
      query: transformQuery({
        type: type === 'test' ? 'TP' : 'REGULAR',
        tabKey: defaultActiveTabKey,
        ...defaultPagination,
        ...defaultFields,
      }),
    },
    //ToSee https://www.apollographql.com/docs/react/data/queries#inspecting-loading-states
    notifyOnNetworkStatusChange: true,
  });

  const { data: countStatic, refetch: refetchCountStatic } = usePerformancesCountStatic({
    variables: {
      query: transformQuery({
        type: type === 'test' ? 'TP' : 'REGULAR',
        tabKey: defaultActiveTabKey,
        ...defaultPagination,
        ...defaultFields,
      }),
    },
  });

  useEffect(() => {
    /**在试用期团队绩效、年度团队绩效来回切换时，需重置state */
    if (!search) {
      setActiveTabKey('goals');
      setPagination({
        page: 1,
        pageSize: 10,
      });
      setFields({
        year: type === 'annual' ? moment().year().toString() : undefined,
      });
    }
  }, [search, type]);

  const locales = useMemo(() => getPerformanceLocales(), []);

  const onSearch = useCallback(
    (params?: SearchProps, refetchCounts = true) => {
      const currentPagination = params?.partialFields
        ? defaultPagination
        : params?.nextPagination ?? pagination;
      const currentFields = params?.partialFields ? { ...fields, ...params.partialFields } : fields;
      setPagination(currentPagination);
      setFields(currentFields);
      const query = {
        tabKey: activeTabKey,
        ...currentPagination,
        ...currentFields,
      };
      const searchParams = transformQuery({ ...query, type: type === 'test' ? 'TP' : 'REGULAR' });
      refetch({
        query: searchParams,
      });
      if (refetchCounts) {
        refetchCountStatic({
          query: searchParams,
        });
      }
      setLocationSearch(query);
      /**清空选中的row */
      setSelectedRows([]);
    },
    [activeTabKey, defaultPagination, fields, pagination, refetch, refetchCountStatic, type]
  );

  return (
    <TeamPerformancesContext.Provider
      value={[
        {
          type,
          activeTabKey,
          pagination,
          fields,
          loading,
          data: {
            data: data?.paginatedPerformances?.data ?? [],
            total: data?.paginatedPerformances?.total ?? 0,
          },
          statusOptions: countStatic?.performancesStaticCounts?.statusCounts ?? [],
          periodOptions: countStatic?.performancesStaticCounts?.periodsCounts ?? [],
          selectedRows,
        },
        {
          setActiveTabKey,
          setPagination,
          setFields,
          setSelectedRows,
          onSearch,
        },
      ]}
    >
      <Card
        activeTabKey={activeTabKey}
        tabList={[
          {
            key: 'goals',
            tab: `${locales.pfType.enum[type]}目标`,
          },
          {
            key: 'evaluations',
            tab: `${locales.pfType.enum[type]}考核`,
          },
        ]}
        tabBarExtraContent={
          <DatePicker
            style={{ width: 216 }}
            picker="year"
            value={fields?.year ? moment().set('year', Number(fields.year)) : undefined}
            format={value => `${value.format('YYYY')}年度`}
            allowClear={false}
            onChange={val => {
              const year = moment(val).get('year');
              onSearch({
                partialFields: { year: year.toString() },
              });
            }}
          />
        }
        onTabChange={key => {
          const _tabKey = key as TabKey;
          const params: SearchParams = {
            tabKey: _tabKey,
            ...defaultPagination,
            year: type === 'annual' ? fields?.year ?? moment().get('year').toString() : undefined,
          };
          setFields({ year: params.year });
          setActiveTabKey(_tabKey);
          setPagination(defaultPagination);
          const refetchParams = transformQuery({
            ...params,
            type: type === 'test' ? 'TP' : 'REGULAR',
          });
          refetch({
            query: refetchParams,
          });
          refetchCountStatic({
            query: refetchParams,
          });
          setLocationSearch(params);
          setSelectedRows([]);
        }}
      >
        {activeTabKey === 'goals' ? <GoalsList /> : <EvaluationsList />}
      </Card>
    </TeamPerformancesContext.Provider>
  );
}
