import type { PaginatedPerformancesResolverArgs } from '@manyun/hrm.gql.client.hrm';
import type { BackendPerformanceType } from '@manyun/hrm.model.performance';

import type { SearchParams } from './team-performances';

const orderFieldMapper: Record<string, string> = {
  userId: 'userId',
  hiredAt: 'hiredAt',
  region: 'region',
  idcTag: 'idc',
  job: 'job',
  supervisor: 'lineManager',
  goalsDeadLine: 'goalsDeadline',
  evaluateDeadLine: 'evalDeadline',
  goalsStatus: 'goalsStatus',
  evaluationStatus: 'evaluationStatus',
  supervisorScore: 'grade',
  finalScore: 'gradeAvg',
  result: 'result',
  performancePosition: 'evaluationJob',
  period: 'period',
  objectivesGrade: 'objectivesGrade',
} as const;

export const transformQuery = (params: SearchParams & { type: BackendPerformanceType }) => {
  const orderBy =
    params.sortField && orderFieldMapper[params.sortField]
      ? {
          [orderFieldMapper[params.sortField]]: params?.sortOrder
            ? params?.sortOrder === 'descend'
              ? 'desc'
              : 'asc'
            : undefined,
        }
      : undefined;
  const _params: PaginatedPerformancesResolverArgs['query'] = {
    year: params.year?.toString(),
    period: params.period,
    periods: params.periods,
    evaluationJob: params.performancePosition,
    type: params.type,
    page: params.page,
    pageSize: params.pageSize,
    kpiSubType: params.tabKey === 'goals' ? 'TARGET' : 'EVAL',
    hiredAtRange: params.hiredAtRange,
    goalsDeadlineRange: params.goalsDeadLineRange,
    evalDeadlineRange: params.evalDeadlineRange,
    result: params.result,
    currentHandlerId: params.currentHandlerId,
    job: params.job,
    userIds: params.userId ? [params.userId] : undefined,
    regionCodes: params.region,
    idcs: params.idcTags,
    blocks: params.blockGuids,
    goalsStatus:
      params.tabKey === 'goals' && (!params.goalsStatus || !params.goalsStatus.length)
        ? [
            'TARGET_SELF_SETTING',
            'TARGET_WAIT_SUPERIOR',
            'TARGET_BACK',
            'TARGET_FINISHED',
            'TARGET_WAIT_SEC_SUPERIOR',
          ]
        : (params.goalsStatus as string[]),
    evaluationStatus:
      params.tabKey === 'evaluations' &&
      (!params.evaluationStatus || !params.evaluationStatus.length)
        ? [
            'EVAL_INIT',
            'SELF_EVAL',
            'EVAL_BACK',
            'EVAL_FINISHED',
            'EVAL_WAIT_HR',
            'EVAL_WAIT_AREA_HR',
            'EVAL_WAIT_SEC_SUPERIOR',
            'EVAL_WAIT_SUPERIOR',
            'SELF_CONFIRM',
          ]
        : (params.evaluationStatus as string[]),
    lineManagerIds: params.supervisorId ? [params.supervisorId] : undefined,
    secondLineManagerIds: params.secSuperiorId ? [params.secSuperiorId] : undefined,
    orderBy,
  };
  return _params;
};
