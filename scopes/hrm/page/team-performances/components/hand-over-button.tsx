import moment from 'moment';
import React, { useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.gql.use-authorized';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { useBatchHandOverPerformances } from '@manyun/hrm.gql.client.hrm';

import { useTeamPerformancesContext } from '../team-performances-context';

export type HandOverButtonProps = {
  onSuccess?: () => void;
};
export const HandOverButton = ({ onSuccess }: HandOverButtonProps) => {
  const [{ type, fields }] = useTeamPerformancesContext();
  const [visible, setVisible] = useState(false);
  const [authorized] = useAuthorized({ checkByCode: 'element_hrm-performance-eval-hand-over' });

  const [form] = Form.useForm();
  const [batchHandOverPerformances, { loading }] = useBatchHandOverPerformances();

  if (!authorized) {
    return null;
  }

  return (
    <>
      <Button
        onClick={() => {
          setVisible(true);
        }}
      >
        移交
      </Button>
      <Modal
        width={560}
        open={visible}
        title="移交绩效考核任务"
        okText="确认移交"
        okButtonProps={{ loading, disabled: loading }}
        destroyOnClose
        afterClose={() => {
          form.resetFields();
        }}
        onOk={() => {
          form
            .validateFields()
            .then(values => {
              batchHandOverPerformances({
                variables: {
                  year: fields?.year?.toString() ?? moment().get('year').toString(),
                  type: type === 'test' ? 'TP' : 'REGULAR',
                  transfereeId: values.handler,
                },
                onCompleted: res => {
                  if (res.batchHandOverPerformances?.message) {
                    message.error(res.batchHandOverPerformances.message);
                    return;
                  }
                  onSuccess?.();
                  setVisible(false);
                  message.success('移交成功');
                },
              });
            })
            .catch(console.error);
        }}
        onCancel={() => {
          setVisible(false);
        }}
      >
        <Space direction="vertical" size="middle">
          <Alert
            type="warning"
            showIcon
            message="确认移交？"
            description="移交后，未开启自评的考核任务，将全部移交给接手人。注意：已开启自评的考核任务，请您正常完成审批。"
          />
          <Form form={form}>
            <Form.Item
              name="handler"
              label="接手人"
              rules={[{ required: true, message: '接手人必选' }]}
            >
              <UserSelect
                style={{ width: 210 }}
                allowClear
                includeCurrentUser={false}
                labelInValue={false}
                userState="in-service"
              />
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
};
