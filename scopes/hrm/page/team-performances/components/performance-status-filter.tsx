import React, { useMemo, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Space } from '@manyun/base-ui.ui.space';

import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import type {
  BackendPerformanceEvaluationStatus,
  BackendPerformanceObjectiveStatus,
} from '@manyun/hrm.model.performance';

import { useTeamPerformancesContext } from '../team-performances-context';

export type StatusFilterProps<
  T extends BackendPerformanceEvaluationStatus | BackendPerformanceObjectiveStatus
> = {
  type: 'objectiveStatus' | 'evaluationStatus';
  value?: T[];
  onChange?: (value: T[]) => void;
};

export function StatusFilter<
  T extends BackendPerformanceEvaluationStatus | BackendPerformanceObjectiveStatus
>({ type, value, onChange }: StatusFilterProps<T>) {
  const [{ type: pfType, statusOptions }] = useTeamPerformancesContext();
  const locales = useMemo(() => getPerformanceLocales(), []);
  const [checkedList, setCheckedList] = useState<string[]>();

  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);

  const plainOptions = useMemo(() => {
    return statusOptions.map(info => ({
      value: info.status ?? '',
      label: info.status
        ? `${
            type === 'objectiveStatus'
              ? locales.pfType[pfType].objectiveStatus.enum[info.status] ?? ''
              : locales.pfType[pfType].evaluationStatus.enum[info.status] ?? ''
          } (${info.count ?? 0})`
        : '',
    }));
  }, [locales.pfType, pfType, statusOptions, type]);

  useShallowCompareEffect(() => {
    setIndeterminate(!!value?.length && value.length < plainOptions.length);
    setCheckAll(value?.length === plainOptions.length);
    setCheckedList(value as string[]);
  }, [value, plainOptions]);

  return (
    <Space>
      <div>{locales[type].__self}:</div>
      <Checkbox
        indeterminate={indeterminate}
        checked={checkAll}
        onChange={e => {
          const checkedValue = e.target.checked ? plainOptions.map(opt => opt.value) : [];
          setIndeterminate(false);
          setCheckAll(e.target.checked);
          setCheckedList(checkedValue);
          onChange?.(checkedValue as T[]);
        }}
      >
        全部
      </Checkbox>
      <Checkbox.Group
        options={plainOptions}
        value={checkedList}
        onChange={checkedValue => {
          setCheckedList(checkedValue as string[]);
          setIndeterminate(!!checkedValue?.length && checkedValue.length < plainOptions.length);
          setCheckAll(checkedValue?.length === plainOptions.length);
          onChange?.(checkedValue as T[]);
        }}
      />
    </Space>
  );
}
