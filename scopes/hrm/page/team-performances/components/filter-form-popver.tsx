import { FilterOutlined } from '@ant-design/icons';
import chunk from 'lodash.chunk';
import flatten from 'lodash.flatten';
import omit from 'lodash.omit';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import type { FormItemProps } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import type { BackendPerformanceResult } from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { useTeamPerformancesContext } from '../team-performances-context';
import type { Fields, SearchProps } from '../team-performances-context';
import { JobSelect } from './job-select';

export type FilterFormPopoverProps = {
  showItemKeys: ItemFieldName[];
  onSearch: (params: SearchProps) => void;
};

type FilterFormValue = Omit<
  Fields,
  | 'goalsStatus'
  | 'evaluationStatus'
  | 'staffUserId'
  | 'hiredAtRange'
  | 'goalsDeadLineRange'
  | 'evalDeadlineRange'
  | 'hiredAtSort'
  | 'goalDeadlineSort'
  | 'evaluateDeadlineSort'
  | 'region'
  | 'idcTags'
  | 'blockGuids'
> & {
  region?: string[][];
  idcTags?: string[][];
  blockGuids?: string[][];
  hiredAtRange?: [Moment, Moment];
  goalsDeadLineRange?: [Moment, Moment];
  evalDeadlineRange?: [Moment, Moment];
};

type ItemFieldName = keyof FilterFormValue;

type FormItem = {
  label: React.ReactNode;
  name: ItemFieldName;
  colSpan: number;
  children: JSX.Element;
} & FormItemProps;

export function FilterFormPopover({ showItemKeys, onSearch }: FilterFormPopoverProps) {
  const planLocales = useMemo(() => getAnnualPerformancePlanLocales(), []);
  const [{ fields, activeTabKey }] = useTeamPerformancesContext();
  const [form] = Form.useForm<FilterFormValue>();
  const [visible, setVisible] = useState(false);

  const hasFilter = useDeepCompareMemo(() => {
    const filterValues = omit(fields, [
      'goalsStatus',
      'evaluationStatus',
      'userId',
      'sortField',
      'sortOrder',
      'year',
      activeTabKey === 'evaluations' ? 'goalsDeadLineRange' : 'evalDeadlineRange',
    ]);

    return (
      filterValues &&
      Object.values(filterValues).some(value =>
        Array.isArray(value) ? value.length !== 0 : value !== undefined
      )
    );
  }, [fields, activeTabKey]);

  const locales = useMemo(() => getPerformanceLocales(), []);

  const formItemProps = useMemo(() => {
    const basicItems = [
      {
        label: '所属机房',
        name: 'idcTags',
        colSpan: 24,
        children: (
          // eslint-disable-next-line react/jsx-filename-extension
          <LocationCascader
            authorizedOnly
            maxTagCount="responsive"
            nodeTypes={['IDC']}
            multiple
            allowClear
          />
        ),
      },
      {
        label: '所属楼栋',
        name: 'blockGuids',
        colSpan: 24,
        children: (
          <LocationCascader
            maxTagCount="responsive"
            nodeTypes={['IDC', 'BLOCK']}
            multiple
            showCheckedStrategy="SHOW_CHILD"
            allowClear
          />
        ),
      },
      {
        label: '岗位',
        name: 'job',
        colSpan: 12,
        children: <JobSelect allowClear />,
      },
      {
        label: '考核结果',
        name: 'result',
        colSpan: 12,
        children: (
          <Select
            options={Object.keys(locales.result.enum).map(key => ({
              label: locales.result.enum[key as BackendPerformanceResult],
              value: key,
            }))}
            allowClear
          />
        ),
      },
      {
        label: '待办人',
        name: 'currentHandlerId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: '区域',
        name: 'region',
        colSpan: 12,
        children: (
          <LocationCascader
            authorizedOnly
            maxTagCount="responsive"
            nodeTypes={['REGION']}
            multiple
            allowClear
          />
        ),
      },
      {
        label: '考核岗位',
        name: 'performancePosition',
        colSpan: 12,
        children: <PerformancePositionSelect allowClear showDelete />,
      },
      {
        label: '直线经理',
        name: 'supervisorId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: '二级经理',
        name: 'secSuperiorId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: '入职时间',
        name: 'hiredAtRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
      {
        label: '截止时间',
        name: 'goalsDeadLineRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
      {
        label: '截止时间',
        name: 'evalDeadlineRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
      {
        label: '考核阶段',
        name: 'period',
        colSpan: 12,
        children: (
          <Select
            options={Object.keys(planLocales.performancePeriod.enum).map(key => ({
              label: planLocales.performancePeriod.enum[key as BackendPerformancePeriod],
              value: key,
            }))}
            allowClear
          />
        ),
      },
    ] as FormItem[];
    if (showItemKeys) {
      return showItemKeys.reduce((items: FormItem[], key) => {
        const item = basicItems.find(cur => cur.name === key);
        if (item) {
          items.push(item);
        }
        return items;
      }, []);
    }
    return basicItems;
  }, [locales.result.enum, planLocales.performancePeriod.enum, showItemKeys]);

  const onHandleSearch = () => {
    const formValue = form.getFieldsValue();

    const partialFields: Partial<Fields> = {
      ...formValue,
      region: formValue.region ? flatten(formValue.region) : undefined,
      idcTags: formValue.idcTags ? flatten(formValue.idcTags) : undefined,
      blockGuids: formValue.blockGuids
        ? formValue.blockGuids.filter(item => item.length === 2).map(item => item[1])
        : undefined,
      hiredAtRange:
        formValue.hiredAtRange && formValue.hiredAtRange.length === 2
          ? [
              formValue.hiredAtRange[0].startOf('day').valueOf(),
              formValue.hiredAtRange[1].endOf('day').valueOf(),
            ]
          : undefined,
      goalsDeadLineRange:
        formValue.goalsDeadLineRange && formValue.goalsDeadLineRange.length === 2
          ? [
              formValue.goalsDeadLineRange[0].startOf('day').valueOf(),
              formValue.goalsDeadLineRange[1].endOf('day').valueOf(),
            ]
          : undefined,
      evalDeadlineRange:
        formValue.evalDeadlineRange && formValue.evalDeadlineRange.length === 2
          ? [
              formValue.evalDeadlineRange[0].startOf('day').valueOf(),
              formValue.evalDeadlineRange[1].endOf('day').valueOf(),
            ]
          : undefined,
    };
    onSearch({ partialFields });
  };

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => (
        <Card style={{ width: 544 }}>
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              {formItemProps.map(itemProps => {
                const { label, name, colSpan, children, ...rest } = itemProps;
                return (
                  <Col key={`${name}`} span={colSpan}>
                    <Form.Item label={label} name={name} {...rest}>
                      {children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Row justify="end">
              <Space>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setVisible(false);
                    onHandleSearch();
                  }}
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    onHandleSearch();
                    setVisible(false);
                  }}
                >
                  搜索
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={_visible => {
        if (_visible === true && fields) {
          form.setFieldsValue({
            ...fields,
            region: fields.region ? chunk(fields.region) : undefined,
            idcTags: fields.idcTags ? chunk(fields.idcTags) : undefined,
            blockGuids: Array.isArray(fields.blockGuids)
              ? fields.blockGuids
                  .map(item => item.split('.'))
                  .reduce((acc, cur) => {
                    if (cur.length === 2) {
                      cur[1] = cur.join('.');
                    }
                    return [...acc, cur];
                  }, [] as string[][])
              : undefined,
            hiredAtRange:
              fields.hiredAtRange && fields.hiredAtRange.length === 2
                ? [moment(fields.hiredAtRange[0]), moment(fields.hiredAtRange[1])]
                : undefined,
            goalsDeadLineRange:
              fields.goalsDeadLineRange && fields.goalsDeadLineRange.length === 2
                ? [moment(fields.goalsDeadLineRange[0]), moment(fields.goalsDeadLineRange[1])]
                : undefined,
            evalDeadlineRange:
              fields.evalDeadlineRange && fields.evalDeadlineRange.length === 2
                ? [moment(fields.evalDeadlineRange[0]), moment(fields.evalDeadlineRange[1])]
                : undefined,
          });
        } else {
          onHandleSearch();
        }
        setVisible(_visible);
      }}
    >
      <Tooltip title="筛选">
        <Button
          size="small"
          icon={<FilterOutlined />}
          ghost={hasFilter}
          type={hasFilter ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}
