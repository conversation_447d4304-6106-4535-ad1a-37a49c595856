import { useApolloClient } from '@apollo/client';
import dayjs from 'dayjs';
import React, { useMemo } from 'react';

import { UserLink } from '@manyun/auth-hub.ui.user';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { BackendPerformanceResult, PerformanceJSON } from '@manyun/hrm.model.performance';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';
import type { BackendGrade } from '@manyun/hrm.model.performance-objective';
import { EvaluateStatusTag } from '@manyun/hrm.ui.evalute-status-tag';
import { GoalsStatusTag } from '@manyun/hrm.ui.goals-status-tag';
import { PerformanceResultTag } from '@manyun/hrm.ui.performance-result-tag';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';
import { readSpace } from '@manyun/resource-hub.gql.client.spaces';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { useTeamPerformancesContext } from '../team-performances-context';

export type ShowColumn =
  | 'userId'
  | 'jobNo'
  | 'hiredAt'
  | 'region'
  | 'idcTag'
  | 'blockGuid'
  | 'job'
  | 'supervisor'
  | 'secSuperior'
  | 'goalsDeadLine'
  | 'evaluateDeadLine'
  | 'goalsStatus'
  | 'evaluationStatus'
  | 'agent'
  | 'supervisorScore'
  | 'finalScore'
  | 'objectivesGrade'
  | 'result'
  | 'performancePosition'
  | 'period'
  | 'options';

export type PerformancesTableProps = TableProps<PerformanceJSON>;

export const PerformancesTable = ({ ...props }: PerformancesTableProps) => {
  return <Table {...props} rowKey="id" scroll={{ x: 'max-content' }} />;
};

export const usePerformanceColumns = ({
  showColumns,
  defaultSortOrderMapper,
  renderActions,
}: {
  showColumns: ShowColumn[];
  defaultSortOrderMapper?: Partial<Record<ShowColumn, ColumnType['defaultSortOrder']>>;
  renderActions?: (record: PerformanceJSON) => React.ReactNode;
}) => {
  const client = useApolloClient();

  const [{ type, activeTabKey }] = useTeamPerformancesContext();
  const planLocales = useMemo(() => getAnnualPerformancePlanLocales(), []);
  const performanceObjectiveLocales = useMemo(() => getPerformanceObjectiveLocales(), []);

  const basicColumns = useMemo(
    () =>
      [
        {
          title: '人员',
          dataIndex: 'userId',
          fixed: 'left',
          disabled: true,
          defaultSortOrder: defaultSortOrderMapper?.userId,
          sorter: true,
          render: (_, record) => <UserLink id={record.user.id} name={record.user.name} />,
        },
        {
          title: '员工工号',
          dataIndex: 'jobNo',
          show: activeTabKey === 'goals',
          render: (_, record) => record.user.jobNo ?? '--',
        },
        {
          title: '入职日期',
          dataIndex: 'hiredAt',
          fixed: type === 'test' ? 'left' : undefined,
          disabled: type === 'test',
          show: (type === 'test' && activeTabKey === 'evaluations') || activeTabKey === 'goals',
          defaultSortOrder: defaultSortOrderMapper?.hiredAt,
          sorter: true,
          render: (_, record) => dayjs(record.user.hiredAt).format('YYYY-MM-DD'),
        },
        {
          title: '区域',
          dataIndex: 'region',
          sorter: true,
          defaultSortOrder: defaultSortOrderMapper?.region,
          render: (_, record) => record.user.region?.label,
        },
        {
          title: '所属机房',
          dataIndex: 'idcTag',
          defaultSortOrder: defaultSortOrderMapper?.idcTag,
          sorter: true,
          render: (_, record) => record.user.idc && <SpaceText guid={record.user.idc} />,
        },
        {
          title: '所属楼栋',
          dataIndex: 'blockGuid',
          render: (_, record) => {
            if (record.user.blockGuid) {
              const blockLabel = readSpace(client, record.user.blockGuid);
              return blockLabel?.label ?? '--';
            }
            return '--';
          },
        },
        {
          title: '岗位',
          dataIndex: 'job',
          defaultSortOrder: defaultSortOrderMapper?.job,
          sorter: true,
          render: (_, record) => record.user.job,
        },
        {
          title: '考核岗位',
          dataIndex: 'performancePosition',
          sorter: true,
          defaultSortOrder: defaultSortOrderMapper?.performancePosition,
          render: (_, record) => record.evaluationJob?.label,
        },
        {
          title: '直线经理',
          dataIndex: 'supervisor',
          defaultSortOrder: defaultSortOrderMapper?.supervisor,
          sorter: true,
          render: (_, record) => record.lineManagers?.map(user => user.name).join('｜'),
        },
        {
          title: '二级经理',
          dataIndex: 'secSuperior',
          show: activeTabKey === 'goals',
          render: (_, record) =>
            record.secondLineManagers?.map(user => user.name).join('｜') ?? '--',
        },
        {
          title: '目标落地截止时间',
          dataIndex: 'goalsDeadLine',
          defaultSortOrder: defaultSortOrderMapper?.goalsDeadLine,
          sorter: true,
          render: (_, record) =>
            record.objectiveExpiredAt
              ? dayjs(record.objectiveExpiredAt).format('YYYY-MM-DD')
              : '--',
        },
        {
          title: '目标状态',
          dataIndex: 'goalsStatus',
          defaultSortOrder: defaultSortOrderMapper?.goalsStatus,
          sorter: true,
          render: (_, record) => {
            return <GoalsStatusTag pfType={record?.type} status={record.objectiveStatus} />;
          },
        },
        {
          title: '考核截止时间',
          dataIndex: 'evaluateDeadLine',
          defaultSortOrder: defaultSortOrderMapper?.evaluateDeadLine,
          sorter: true,
          render: (_, record) =>
            record.evaluationExpiredAt
              ? dayjs(record.evaluationExpiredAt).format('YYYY-MM-DD')
              : '--',
        },
        {
          title: '考核阶段',
          dataIndex: 'period',
          defaultSortOrder: defaultSortOrderMapper?.period,
          sorter: true,
          render: val => {
            return planLocales.performancePeriod.enum[val as BackendPerformancePeriod];
          },
        },
        {
          title: '考核状态',
          dataIndex: 'evaluationStatus',
          defaultSortOrder: defaultSortOrderMapper?.evaluationStatus,
          sorter: true,
          render: (val, record) => {
            return <EvaluateStatusTag pfType={record.type} status={val} />;
          },
        },
        {
          title: '待办人',
          dataIndex: 'agent',
          render: (_, record) =>
            record.currentHandlers && record.currentHandlers.length > 0
              ? record.currentHandlers.map(user => user.name).join('｜')
              : '--',
        },
        {
          title: '直线经理评分',
          dataIndex: 'supervisorScore',
          defaultSortOrder: defaultSortOrderMapper?.supervisorScore,
          sorter: true,
          render: (_, record) => record.grade ?? '--',
        },
        {
          title: '最终绩效得分',
          dataIndex: 'finalScore',
          defaultSortOrder: defaultSortOrderMapper?.finalScore,
          sorter: true,
          render: (_, record) => record.gradeAvg ?? '--',
        },
        {
          title: '得分', //年度绩效的目标得分
          dataIndex: 'objectivesGrade',
          sorter: true,
          defaultSortOrder: defaultSortOrderMapper?.objectivesGrade,
          disabled: true,
          render: (_, record) => (
            <>
              {record.period !== 'YEAR'
                ? record.objectivesGrade !== null && record.objectivesGrade >= 0
                  ? record.objectivesGrade
                  : '--'
                : (record.objectivesGrade ?? '--')}
              {record.evaluationStatus === 'EVAL_INIT' && (
                <Explanation
                  style={{ color: 'inherit' }}
                  iconType="question"
                  tooltip={{ title: '“未开始”状态的考核，得分为预估分数' }}
                />
              )}
            </>
          ),
        },
        {
          title: '考核结果',
          dataIndex: 'result',
          disabled: type === 'test',
          defaultSortOrder: defaultSortOrderMapper?.result,
          sorter: true,
          render: (_, record) => {
            return record.type === 'TP' ? (
              record.result ? (
                <PerformanceResultTag result={record.result as BackendPerformanceResult} />
              ) : (
                '--'
              )
            ) : record.grade ? (
              performanceObjectiveLocales.grade.annual[PERFORMANCE_SECOND_VERSION][
                record.grade as BackendGrade
              ]
            ) : (
              '--'
            );
          },
        },
        {
          title: '操作',
          dataIndex: 'options',
          fixed: 'right',
          disabled: true,
          render: (_, record) => renderActions?.(record),
        },
      ] as ColumnType<PerformanceJSON>[],
    [
      defaultSortOrderMapper?.userId,
      defaultSortOrderMapper?.hiredAt,
      defaultSortOrderMapper?.region,
      defaultSortOrderMapper?.idcTag,
      defaultSortOrderMapper?.job,
      defaultSortOrderMapper?.performancePosition,
      defaultSortOrderMapper?.supervisor,
      defaultSortOrderMapper?.goalsDeadLine,
      defaultSortOrderMapper?.goalsStatus,
      defaultSortOrderMapper?.evaluateDeadLine,
      defaultSortOrderMapper?.period,
      defaultSortOrderMapper?.evaluationStatus,
      defaultSortOrderMapper?.supervisorScore,
      defaultSortOrderMapper?.finalScore,
      defaultSortOrderMapper?.objectivesGrade,
      defaultSortOrderMapper?.result,
      activeTabKey,
      type,
      client,
      planLocales.performancePeriod.enum,
      performanceObjectiveLocales.grade.annual,
      renderActions,
    ]
  );

  return showColumns.reduce((defaultColumns: ColumnType<PerformanceJSON>[], key) => {
    const column = basicColumns.find(column => column.dataIndex === key);
    if (column) {
      defaultColumns.push(column);
    }
    return defaultColumns;
  }, []);
};
