import moment from 'moment';
import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import type {
  BackendPerformanceEvaluationStatus,
  BackendPerformancePeriod,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';
import {
  generateAnnualPerformanceDetail,
  generateTestPerformanceDetail,
} from '@manyun/hrm.route.hrm-routes';
import type { ApproveStatus } from '@manyun/hrm.ui.performance-approve-button';
import {
  AgreePerformanceButton,
  RefusePerformanceButton,
} from '@manyun/hrm.ui.performance-approve-button';

import type { SortField } from '../team-performances-context';
import { useTeamPerformancesContext } from '../team-performances-context';
import { FileExport } from './file-export';
import { FilterFormPopover } from './filter-form-popver';
import { PeriodFilter } from './performance-period-filter';
import { StatusFilter } from './performance-status-filter';
import { PerformancesTable, usePerformanceColumns } from './performances-table';
import { PublicResultModalView } from './public-result-modal-view';
import { TableFooterBar } from './table-footer-tool-bar';

export function EvaluationsList() {
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const [{ type, fields, pagination, data, loading, selectedRows }, { onSearch, setSelectedRows }] =
    useTeamPerformancesContext();
  const defaultColumns = usePerformanceColumns({
    showColumns:
      type === 'test'
        ? [
            'userId',
            'jobNo',
            'hiredAt',
            'region',
            'idcTag',
            'job',
            'supervisor',
            'secSuperior',
            'evaluateDeadLine',
            'evaluationStatus',
            'agent',
            'supervisorScore',
            'finalScore',
            'result',
            'options',
          ]
        : [
            'userId',
            'jobNo',
            'hiredAt',
            'region',
            'idcTag',
            'blockGuid',
            'performancePosition',
            'supervisor',
            'secSuperior',
            'period',
            'evaluationStatus',
            'agent',
            'objectivesGrade',
            'result',
            'options',
          ],
    defaultSortOrderMapper: fields?.sortField
      ? {
          [fields.sortField]: fields?.sortOrder,
        }
      : undefined,
    renderActions: record => <PerformanceActions record={record} />,
  });
  const [tableColumns, setTableColumns] = useState(defaultColumns);

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="middle">
      {type === 'annual' && (
        <PeriodFilter<BackendPerformancePeriod>
          value={fields?.periods}
          onChange={value => {
            onSearch(
              {
                partialFields: { periods: value },
              },
              false
            );
          }}
        />
      )}
      <StatusFilter<BackendPerformanceEvaluationStatus>
        type="evaluationStatus"
        value={fields?.evaluationStatus}
        onChange={value => {
          onSearch(
            {
              partialFields: { evaluationStatus: value },
            },
            false
          );
        }}
      />
      <Space style={{ justifyContent: 'space-between', width: '100%' }}>
        <Space style={{ width: '100%' }} size="large">
          {type === 'annual' && <PublicResultModalView year={fields?.year} />}
          <UserSelect
            style={{ width: 272 }}
            allowClear
            value={fields?.userId}
            labelInValue={false}
            onChange={value => {
              onSearch({
                partialFields: { userId: value },
              });
            }}
          />
          <Space>
            仅看待我处理
            <Switch
              checked={fields?.currentHandlerId === userId!}
              onChange={check => {
                onSearch({
                  partialFields: {
                    currentHandlerId: check ? userId! : undefined,
                  },
                });
              }}
            />
          </Space>
        </Space>
        <Space size="middle">
          <FilterFormPopover
            showItemKeys={
              type === 'test'
                ? [
                    'idcTags',
                    'job',
                    'result',
                    'currentHandlerId',
                    'region',
                    'supervisorId',
                    'secSuperiorId',
                    'hiredAtRange',
                    'evalDeadlineRange',
                  ]
                : [
                    'idcTags',
                    'blockGuids',
                    'region',
                    'performancePosition',
                    'currentHandlerId',
                    'supervisorId',
                    'secSuperiorId',
                  ]
            }
            onSearch={onSearch}
          />
          <FileExport />
          <EditColumns
            uniqKey={`HRM_${type.toLocaleUpperCase()}_PERFORMANCES`}
            defaultValue={defaultColumns}
            onChange={setTableColumns}
          />
        </Space>
      </Space>
      <PerformancesTable
        loading={loading}
        rowSelection={
          type === 'annual'
            ? {
                fixed: 'left',
                selectedRowKeys: selectedRows.filter(row => row.id).map(row => row.id!),
                onChange: (_, rows) => {
                  setSelectedRows(rows);
                },
                getCheckboxProps: record => ({
                  disabled: !(
                    record.evaluationStatus &&
                    ['EVAL_WAIT_SUPERIOR', 'EVAL_WAIT_SEC_SUPERIOR'].includes(
                      record.evaluationStatus
                    ) &&
                    userId &&
                    (record.currentHandlers ?? []).map(user => user.id).includes(userId)
                  ),
                }),
              }
            : undefined
        }
        columns={[...tableColumns]}
        dataSource={
          data.data.map(pf => ({
            ...pf,
            year: fields?.year ?? moment().format('YYYY'),
          })) as PerformanceJSON[]
        }
        pagination={{
          total: data.total,
          current: pagination.page,
          pageSize: pagination.pageSize,
        }}
        onChange={({ current, pageSize }, _, sorter, { action }) => {
          if (action === 'paginate') {
            onSearch({
              nextPagination: { page: current!, pageSize: pageSize! },
            });
          } else if (action === 'sort') {
            if (sorter && !Array.isArray(sorter)) {
              onSearch({
                partialFields: {
                  sortField: sorter.field as SortField,
                  sortOrder: sorter.order,
                },
              });
            }
          }
        }}
      />
      {type === 'annual' && <TableFooterBar />}
    </Space>
  );
}

const PerformanceActions = ({ record }: { record: PerformanceJSON }) => {
  const history = useHistory();
  const [{ type }, { onSearch }] = useTeamPerformancesContext();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);

  return (
    <Space>
      {/* 待自评状态 无法查看 */}
      {record.evaluationStatus && !['SELF_EVAL'].includes(record.evaluationStatus) && record.id && (
        <Button
          type="link"
          compact
          onClick={() => {
            history.push(
              type === 'test'
                ? generateTestPerformanceDetail({
                    type: 'byEval',
                    id: record.id!.toString(),
                  })
                : generateAnnualPerformanceDetail('team', {
                    year: record.year!,
                    position: record.evaluationJob?.value!,
                    user: record.user.id.toString(),
                    period: record.period,
                    id: record.id!.toString(),
                  })
            );
          }}
        >
          查看
        </Button>
      )}
      {/* 待二线或待HR审批的绩效  审批人可直接审批 */}
      {record.evaluationStatus &&
        ['EVAL_WAIT_SEC_SUPERIOR', 'EVAL_WAIT_HR', 'EVAL_WAIT_AREA_HR'].includes(
          record.evaluationStatus
        ) &&
        record.currentHandlers?.map(user => user.id).includes(userId!) &&
        record.id && (
          <>
            <AgreePerformanceButton
              type="link"
              bizId={record.id}
              compact
              currentStep={record.evaluationStatus as ApproveStatus}
              onSuccess={() => {
                message.success('审批通过');
                onSearch();
              }}
            />
            <RefusePerformanceButton
              type="link"
              bizId={record.id}
              compact
              currentStep={record.evaluationStatus as ApproveStatus}
              onSuccess={() => {
                message.success(`您已退回${record.user.name}的个人年度绩效`);
                onSearch();
              }}
            />
          </>
        )}
    </Space>
  );
};
