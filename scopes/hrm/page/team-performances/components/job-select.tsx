import React, { useEffect, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import { fetchJobsByPerformances } from '@manyun/hrm.service.fetch-jobs-by-performances';

export type JobSelectProps = Omit<SelectProps, 'options'>;
export const JobSelect = ({ ...props }: JobSelectProps) => {
  const [options, setOptions] = useState<{ label: string; value: string }[]>([]);

  useEffect(() => {
    (async () => {
      const { error, data } = await fetchJobsByPerformances();
      if (error) {
        message.error(error.message);
        return;
      }
      setOptions(data.map(jobName => ({ label: jobName, value: jobName })));
    })();
  }, []);

  return <Select {...props} options={options} />;
};
