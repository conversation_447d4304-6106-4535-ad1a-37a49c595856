import React, { useCallback, useMemo, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { batchApprovePerformances } from '@manyun/hrm.service.batch-approve-performances';

import { useTeamPerformancesContext } from '../team-performances-context';

export const TableFooterBar = () => {
  const [{ activeTabKey, selectedRows }, { setSelectedRows, onSearch }] =
    useTeamPerformancesContext();
  const [open, setOpen] = useState(false);
  const [submitLoading, setSubmitLoading] = useState(false);
  const [form] = Form.useForm();

  const len = useMemo(() => {
    return (selectedRows ?? []).length;
  }, [selectedRows]);

  /**
   * 年度考核直线经理不能批量操作同意
   */
  const disabledBatchAgree = useMemo(() => {
    return (
      activeTabKey === 'evaluations' &&
      selectedRows.some(
        row => row.evaluationStatus && row.evaluationStatus === 'EVAL_WAIT_SUPERIOR'
      )
    );
  }, [activeTabKey, selectedRows]);

  const handleApprove = useCallback(
    async (status: 'PASS' | 'REFUSE') => {
      setSubmitLoading(true);
      const { error } = await batchApprovePerformances({
        infoIds: selectedRows.map(row => row.id!),
        status: status,
        reason: form.getFieldValue('reason'),
      });
      setSubmitLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }

      message.success('批量操作成功');
      setOpen(false);
      setSelectedRows([]);
      onSearch();
    },
    [form, onSearch, selectedRows, setSelectedRows]
  );

  return (
    <FooterToolBar>
      <Space style={{ width: '100%', justifyContent: 'space-between', padding: '5px 24px' }}>
        <Space size={16}>
          <Typography.Text>已选择{len} 项 </Typography.Text>
          {len > 0 && (
            <Button
              type="link"
              onClick={() => {
                setSelectedRows([]);
              }}
            >
              取消选择
            </Button>
          )}
        </Space>
        <Space size={24}>
          <Popconfirm
            title={`确认批量同意当前${len}条${activeTabKey === 'goals' ? '年度目标' : '年度考核'}`}
            onConfirm={() => {
              handleApprove('PASS');
            }}
          >
            <Button disabled={len === 0 || disabledBatchAgree} type="primary">
              批量同意
            </Button>
          </Popconfirm>
          <>
            <Button
              disabled={len === 0}
              onClick={() => {
                setOpen(true);
              }}
            >
              批量退回
            </Button>
            <Modal
              title="批量退回"
              open={open}
              okText="确定退回"
              okButtonProps={{ loading: submitLoading }}
              onCancel={() => {
                setOpen(false);
              }}
              onOk={() => {
                handleApprove('REFUSE');
              }}
            >
              <Form form={form}>
                <Form.Item
                  label="退回原因"
                  name="reason"
                  rules={[{ whitespace: true, max: 100, message: '最多输入 100 个字符！' }]}
                >
                  <Input.TextArea rows={3} showCount maxLength={100} />
                </Form.Item>
              </Form>
            </Modal>
          </>
        </Space>
      </Space>
    </FooterToolBar>
  );
};
