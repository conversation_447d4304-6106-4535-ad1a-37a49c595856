import moment from 'moment';
import React from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Space } from '@manyun/base-ui.ui.space';
import {
  useLazyPerformancePublicResultConfigure,
  usePublicPerformanceResultConfigure,
} from '@manyun/hrm.gql.client.hrm';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';

export type PublicResultModalViewProps = {
  year?: string;
};
export const PublicResultModalView = ({ year }: PublicResultModalViewProps) => {
  const [open, setOpen] = React.useState(false);
  const [form] = Form.useForm();
  const [{ authorized }] = useAuthorized({ code: 'element_hrm-performance-publish-configure' });
  const [getPerformancePublicResultConfigure, { data }] = useLazyPerformancePublicResultConfigure();
  const [updatePerformanceResultConfigure, { loading }] = usePublicPerformanceResultConfigure();

  const options = React.useMemo(() => {
    const _opts = [
      { label: 'Q1', value: 'Q1' },
      { label: 'Q2', value: 'Q2' },
      { label: 'Q3', value: 'Q3' },
      { label: 'Q4', value: 'Q4' },
      { label: '年度', value: 'YEAR' },
    ];
    return _opts.map(opt => {
      const existingOpt = (data?.performancePublicityConfigure ?? []).find(
        item => item.period === opt.value
      );
      return {
        label: `${opt.label}${existingOpt?.published ? '(已公示)' : ''}`,
        value: opt.value,
        defaultChecked: existingOpt?.published,
        disabled: existingOpt?.published,
      };
    });
  }, [data?.performancePublicityConfigure]);

  if (!authorized) {
    return null;
  }
  return (
    <>
      <Button
        onClick={() => {
          form.resetFields();
          form.setFieldValue('year', year ? moment().set('year', Number(year)) : moment());
          getPerformancePublicResultConfigure({
            variables: {
              year: year?.toString() ?? moment().year().toString(),
            },
          });
          setOpen(true);
        }}
      >
        公示考核结果
      </Button>
      <Modal
        title="公示考核结果"
        width={679}
        open={open}
        okButtonProps={{
          loading: loading,
          disabled: loading,
        }}
        cancelButtonProps={{
          disabled: loading,
        }}
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          form.validateFields().then(values => {
            updatePerformanceResultConfigure({
              variables: {
                year: year?.toString() ?? moment().year().toString(),
                period: values.period,
              },
              onCompleted: data => {
                if (data.updatePerformancePublicityConfigure.message) {
                  message.error(data.updatePerformancePublicityConfigure.message);
                  return;
                }
                if (data.updatePerformancePublicityConfigure.success) {
                  message.success('已公示考核结果');
                  setOpen(false);
                }
              },
            });
          });
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size="middle">
          <Alert
            message="公示后，员工才可看到直线经理评价、二级经理审批结果及考核结果得分，并可进行考核结果确认。"
            type="warning"
            showIcon
          />
          <Form form={form} labelCol={{ span: 3 }}>
            <Form.Item name="year" label="年度">
              <DatePicker
                style={{ width: 216 }}
                picker="year"
                format={value => `${value.format('YYYY')}年度`}
                disabled
              />
            </Form.Item>
            <Form.Item
              name="period"
              label="考核周期"
              rules={[{ required: true, message: '请选择考核周期' }]}
            >
              <Radio.Group>
                {options.map(opt => (
                  <Radio key={opt.value} value={opt.value} disabled={opt.disabled}>
                    {opt.label}
                  </Radio>
                ))}
              </Radio.Group>
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
};
