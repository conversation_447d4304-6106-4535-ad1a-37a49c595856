import React, { useMemo, useState } from 'react';
import { useShallowCompareEffect } from 'react-use';

import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Space } from '@manyun/base-ui.ui.space';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.performance';

import { useTeamPerformancesContext } from '../team-performances-context';

export type PeriodFilterProps<T extends BackendPerformancePeriod> = {
  value?: T[];
  onChange?: (value: T[]) => void;
};

export function PeriodFilter<T extends BackendPerformancePeriod>({
  value,
  onChange,
}: PeriodFilterProps<T>) {
  const [{ periodOptions }] = useTeamPerformancesContext();
  const locales = useMemo(() => getAnnualPerformancePlanLocales(), []);
  const [checkedList, setCheckedList] = useState<string[]>();

  const [indeterminate, setIndeterminate] = useState(false);
  const [checkAll, setCheckAll] = useState(false);

  const plainOptions = useMemo(() => {
    return periodOptions
      .filter(({ period }) => !!locales.performancePeriod.enum[period as BackendPerformancePeriod])
      .map(({ period, count }) => ({
        value: period ?? '',
        label: period
          ? `${locales.performancePeriod.enum[period as BackendPerformancePeriod] ?? ''} (${count ?? 0})`
          : '',
      }));
  }, [locales.performancePeriod, periodOptions]);

  useShallowCompareEffect(() => {
    setIndeterminate(!!value?.length && value.length < plainOptions.length);
    setCheckAll(value?.length === plainOptions.length);
    setCheckedList(value as string[]);
  }, [value, plainOptions]);

  return (
    <Space>
      <div>考核阶段:</div>
      <Checkbox
        indeterminate={indeterminate}
        checked={checkAll}
        onChange={e => {
          const checkedValue = e.target.checked ? plainOptions.map(opt => opt.value) : [];
          setIndeterminate(false);
          setCheckAll(e.target.checked);
          setCheckedList(checkedValue);
          onChange?.(checkedValue as T[]);
        }}
      >
        全部
      </Checkbox>
      <Checkbox.Group
        options={plainOptions}
        value={checkedList}
        onChange={checkedValue => {
          setCheckedList(checkedValue as string[]);
          setIndeterminate(!!checkedValue?.length && checkedValue.length < plainOptions.length);
          setCheckAll(checkedValue?.length === plainOptions.length);
          onChange?.(checkedValue as T[]);
        }}
      />
    </Space>
  );
}
