import React from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Switch } from '@manyun/base-ui.ui.switch';
import type {
  BackendPerformanceObjectiveStatus,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';
import {
  generateAnnualPerformanceObjectivesDetail,
  generateTestPerformanceDetail,
} from '@manyun/hrm.route.hrm-routes';
import type { ApproveStatus } from '@manyun/hrm.ui.performance-approve-button';
import {
  AgreePerformanceButton,
  RefusePerformanceButton,
} from '@manyun/hrm.ui.performance-approve-button';

import { useTeamPerformancesContext } from '../team-performances-context';
import type { SortField } from '../team-performances-context';
import { FileExport } from './file-export';
import { FilterFormPopover } from './filter-form-popver';
import { StatusFilter } from './performance-status-filter';
import { PerformancesTable, usePerformanceColumns } from './performances-table';
import { TableFooterBar } from './table-footer-tool-bar';

export function GoalsList() {
  const [{ type, fields, pagination, loading, data, selectedRows }, { onSearch, setSelectedRows }] =
    useTeamPerformancesContext();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);

  const defaultColumns = usePerformanceColumns({
    showColumns:
      type === 'test'
        ? [
            'userId',
            'jobNo',
            'hiredAt',
            'region',
            'idcTag',
            'job',
            'supervisor',
            'goalsDeadLine',
            'goalsStatus',
            'options',
          ]
        : [
            'userId',
            'jobNo',
            'hiredAt',
            'region',
            'idcTag',
            'blockGuid',
            'performancePosition',
            'supervisor',
            'secSuperior',
            'goalsStatus',
            'options',
          ],
    defaultSortOrderMapper: fields?.sortField
      ? {
          [fields.sortField]: fields.sortOrder,
        }
      : undefined,
    renderActions: record => <GoalActions record={record} />,
  });

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="middle">
      <StatusFilter<BackendPerformanceObjectiveStatus>
        type="objectiveStatus"
        value={fields?.goalsStatus}
        onChange={value => {
          onSearch({ partialFields: { goalsStatus: value } }, false);
        }}
      />
      <Space style={{ justifyContent: 'space-between', width: '100%' }}>
        <Space style={{ width: '100%' }} size="large">
          <UserSelect
            style={{ width: 272 }}
            allowClear
            labelInValue={false}
            value={fields?.userId}
            onChange={value => {
              onSearch({ partialFields: { userId: value } });
            }}
          />
          {type === 'annual' && (
            <Space>
              仅看待我处理
              <Switch
                checked={fields?.currentHandlerId === userId!}
                onChange={check => {
                  onSearch({
                    partialFields: {
                      currentHandlerId: check ? userId! : undefined,
                    },
                  });
                }}
              />
            </Space>
          )}
        </Space>
        <Space size="middle">
          <FilterFormPopover
            showItemKeys={
              type === 'test'
                ? ['idcTags', 'job', 'region', 'hiredAtRange', 'goalsDeadLineRange']
                : [
                    'idcTags',
                    'blockGuids',
                    'region',
                    'performancePosition',
                    'supervisorId',
                    'secSuperiorId',
                  ]
            }
            onSearch={onSearch}
          />
          <FileExport />
        </Space>
      </Space>
      <PerformancesTable
        loading={loading}
        columns={defaultColumns}
        rowSelection={
          type === 'annual'
            ? {
                fixed: 'left',
                selectedRowKeys: selectedRows.filter(row => row.id).map(row => row.id!),
                onChange: (_, rows) => {
                  setSelectedRows(rows);
                },
                getCheckboxProps: record => ({
                  disabled: !(
                    record.objectiveStatus &&
                    ['TARGET_WAIT_SUPERIOR', 'TARGET_WAIT_SEC_SUPERIOR'].includes(
                      record.objectiveStatus
                    ) &&
                    userId &&
                    (record.currentHandlers ?? []).map(user => user.id).includes(userId)
                  ),
                }),
              }
            : undefined
        }
        dataSource={data.data as PerformanceJSON[]}
        pagination={{
          total: data.total,
          current: pagination.page,
          pageSize: pagination.pageSize,
        }}
        onChange={({ current, pageSize }, _, sorter, { action }) => {
          if (action === 'paginate') {
            onSearch({
              nextPagination: { page: current!, pageSize: pageSize! },
            });
          } else if (action === 'sort') {
            if (sorter && !Array.isArray(sorter)) {
              onSearch({
                partialFields: {
                  sortField: sorter.field as SortField,
                  sortOrder: sorter.order,
                },
              });
            }
          }
        }}
      />
      {type === 'annual' && <TableFooterBar />}
    </Space>
  );
}

const GoalActions = ({ record }: { record: PerformanceJSON }) => {
  const history = useHistory();
  const [{ type }, { onSearch }] = useTeamPerformancesContext();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);

  return (
    <Space>
      {record.objectiveStatus &&
        !['TARGET_INIT', 'TARGET_SELF_SETTING'].includes(record.objectiveStatus) && (
          <Button
            type="link"
            compact
            onClick={() => {
              if (record.id) {
                history.push(
                  type === 'test'
                    ? generateTestPerformanceDetail({
                        type: 'byGoal',
                        id: record.id!.toString(),
                      })
                    : generateAnnualPerformanceObjectivesDetail('team', {
                        planid: record.plan!.id!.toString(),
                        user: record.user.id.toString(),
                        id: record.id.toString(),
                        key: `TARGET_${record.id}`,
                        position: record.evaluationJob?.value!,
                      })
                );
              }
            }}
          >
            查看
          </Button>
        )}
      {record.objectiveStatus &&
        ['TARGET_WAIT_SUPERIOR', 'TARGET_WAIT_SEC_SUPERIOR'].includes(record.objectiveStatus) &&
        record.currentHandlers?.map(user => user.id).includes(userId!) &&
        record.id && (
          <>
            <AgreePerformanceButton
              type="link"
              bizId={record.id}
              compact
              currentStep={record.objectiveStatus as ApproveStatus}
              onSuccess={() => {
                message.success(
                  `您已经通过${record.user.name}的${
                    record.type === 'TP' ? '试用期目标设定' : '个人年度绩效目标'
                  }`
                );
                onSearch();
              }}
            />
            <RefusePerformanceButton
              type="link"
              bizId={record.id}
              currentStep={record.objectiveStatus as ApproveStatus}
              compact
              onSuccess={() => {
                message.success(
                  record.type === 'TP' ? '目标已退回' : `您已退回${record.user.name}的个人绩效目标`
                );
                onSearch();
              }}
            />
          </>
        )}
    </Space>
  );
};
