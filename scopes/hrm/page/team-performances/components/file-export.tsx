import React, { useCallback, useMemo } from 'react';

import dayjs from 'dayjs';

import { FileExport as BasicFileExport } from '@manyun/base-ui.ui.file-export';
import { message } from '@manyun/base-ui.ui.message';

import { exportPerformances } from '@manyun/hrm.service.export-performances';
import type { SvcQuery } from '@manyun/hrm.service.export-performances';

import { useTeamPerformancesContext } from '../team-performances-context';
import { transformQuery } from '../util';

export const FileExport = () => {
  const [{ type, fields, pagination, activeTabKey }] = useTeamPerformancesContext();

  const menuItems = useMemo(() => {
    if (type === 'test' || activeTabKey === 'evaluations') {
      return [
        { text: '导出状态数据', type: 'list' },
        { text: '导出明细数据', type: 'detail' },
      ];
    }
    return [{ text: '导出筛选后的列表数据', type: 'list' }];
  }, [activeTabKey, type]);

  const generateFileName = useCallback(
    (itemType: string) => {
      return `员工${type === 'test' ? '试用期' : '年度'}${
        activeTabKey === 'goals' ? '目标' : '考核'
      }${itemType === 'list' ? '状态' : '详情'}_${dayjs().format('YYYYMMDD')}`;
    },
    [activeTabKey, type]
  );

  return (
    <BasicFileExport
      showExportFiltered
      customMenuItems={menuItems}
      data={async itemType => {
        const params: SvcQuery = {
          ...transformQuery({
            type: type === 'test' ? 'TP' : 'REGULAR',
            tabKey: activeTabKey,
            ...fields,
            ...pagination,
          }),
          exportType: itemType === 'list' ? 'INFO' : 'DETAIL',
        };
        const { error, data } = await exportPerformances(params);
        if (error) {
          message.error(error.message);
          return;
        }

        return Promise.resolve({ filename: generateFileName(itemType), data: data });
      }}
    />
  );
};
