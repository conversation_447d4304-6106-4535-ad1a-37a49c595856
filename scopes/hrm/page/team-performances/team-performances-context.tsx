import { createContext, useContext } from 'react';

import type { ColumnType } from '@manyun/base-ui.ui.table';
import type {
  PaginatedPerformances,
  PerformancesStaticCountsResponse,
} from '@manyun/hrm.gql.client.hrm';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import type {
  BackendPerformanceEvaluationStatus,
  BackendPerformanceObjectiveStatus,
  BackendPerformanceResult,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';
import type { TeamPerformancesParams } from '@manyun/hrm.route.hrm-routes';

import type { ShowColumn } from './components/performances-table';

export type StatusInfos = {
  status: BackendPerformanceEvaluationStatus | BackendPerformanceObjectiveStatus;
  count: number;
};

export type TabKey = 'goals' | 'evaluations';

export type OrderType = ColumnType['defaultSortOrder'];
export type SortField = ShowColumn;

export type LabelValue<T = string | number> = {
  label: string;
  value: T;
};

export type Fields = {
  year?: string;
  period?: BackendPerformancePeriod;
  periods?: BackendPerformancePeriod[];
  performancePosition?: string;
  goalsStatus?: BackendPerformanceObjectiveStatus[];
  evaluationStatus?: BackendPerformanceEvaluationStatus[];
  idcTags?: string[];
  blockGuids?: string[];
  region?: string[];
  job?: string;
  result?: BackendPerformanceResult;
  userId?: number;
  currentHandlerId?: number;
  supervisorId?: number;
  secSuperiorId?: number;
  hiredAtRange?: [number, number];
  goalsDeadLineRange?: [number, number];
  evalDeadlineRange?: [number, number];
  goalDeadlineSort?: OrderType;
  evaluateDeadlineSort?: OrderType;
  sortField?: ShowColumn;
  sortOrder?: OrderType;
};

export type Pagination = {
  page: number;
  pageSize: number;
};

export const defaultPagination: Pagination = {
  page: 1,
  pageSize: 10,
};

export type SearchProps = {
  partialFields?: Partial<Fields>;
  nextPagination?: Pagination;
};

export type TeamPerformancesConsumerProps = [
  {
    type: TeamPerformancesParams['type'];
    activeTabKey: TabKey;
    fields?: Fields;
    pagination: Pagination;
    statusOptions: PerformancesStaticCountsResponse['statusCounts'];
    periodOptions: PerformancesStaticCountsResponse['periodsCounts'];
    loading: boolean;
    data: {
      data: PaginatedPerformances['data'];
      total: PaginatedPerformances['total'];
    };
    selectedRows: PerformanceJSON[];
  },
  {
    setActiveTabKey: React.Dispatch<React.SetStateAction<TabKey>>;
    setFields: React.Dispatch<React.SetStateAction<Fields | undefined>>;
    setPagination: React.Dispatch<React.SetStateAction<Pagination>>;
    onSearch: (params?: SearchProps, refetchCounts?: boolean) => void;
    setSelectedRows: React.Dispatch<React.SetStateAction<PerformanceJSON[]>>;
  },
];

export const noop = () => {};

export const TeamPerformancesContext = createContext<TeamPerformancesConsumerProps>([
  {
    type: 'test',
    activeTabKey: 'goals',
    pagination: defaultPagination,
    loading: false,
    statusOptions: [],
    periodOptions: [],
    data: {
      data: [],
      total: 0,
    },
    selectedRows: [],
  },
  {
    setActiveTabKey: noop,
    setFields: noop,
    setPagination: noop,
    onSearch: noop,
    setSelectedRows: noop,
  },
]);

TeamPerformancesContext.displayName = 'TeamPerformancesContext';

export function useTeamPerformancesContext() {
  return useContext(TeamPerformancesContext);
}
