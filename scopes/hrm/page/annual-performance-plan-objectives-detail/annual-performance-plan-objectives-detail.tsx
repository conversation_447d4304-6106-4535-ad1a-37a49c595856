import CalendarOutlined from '@ant-design/icons/es/icons/CalendarOutlined';
import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import dayjs from 'dayjs';
import React, { useEffect, useMemo, useState } from 'react';
import { useParams } from 'react-router-dom';

import { Container } from '@manyun/base-ui.ui.container';
import { Select } from '@manyun/base-ui.ui.select';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useLazyPerformancePlanById } from '@manyun/hrm.gql.client.hrm';
import type { AnnualPerformancePlanEditParams } from '@manyun/hrm.route.hrm-routes';
import { AnnualPerformanceObjectiveCard } from '@manyun/hrm.ui.annual-performance-objective-card';
import { AnnualPerformanceScoringPopover } from '@manyun/hrm.ui.annual-performance-scoring-popover';
import { getPerformanceVersion } from '@manyun/hrm.util.performances';

import styles from './annual-performance-plan-objectives-detail.module.less';

export function AnnualPerformancePlanObjectivesDetail() {
  const { id } = useParams<AnnualPerformancePlanEditParams>();
  const [idcTag, setIdcTag] = useState<string | undefined>();
  const [position, setPosition] = useState<string | undefined>();

  const [fetch, { data, loading, refetch }] = useLazyPerformancePlanById();

  useEffect(() => {
    fetch({
      variables: {
        id: Number(id),
      },
    }).then(result => {
      if (result.data?.performancePlanById) {
        const record = result.data.performancePlanById;
        //选中第一个机房，第一个岗位
        if (record.resourcesScope[0]) {
          setIdcTag(record.resourcesScope[0].value);
        }
        if (record.positionScope[0]) {
          setPosition(record.positionScope[0].value);
        }
      }
    });
  }, [fetch, id]);

  useEffect(() => {
    if (idcTag && position) {
      refetch({
        id: Number(id),
        includeRelatedObjectives: true,
        relatedObjectivesQuery: {
          resourceCode: idcTag,
          evalPosition: position,
        },
      });
    }
  }, [id, idcTag, position, refetch]);

  const planYearMoment = useMemo(() => {
    return data?.performancePlanById
      ? dayjs().set('year', Number(data.performancePlanById.year))
      : dayjs();
  }, [data?.performancePlanById]);

  if (loading) {
    return <Skeleton />;
  }
  if (!data?.performancePlanById) {
    return null;
  }
  return (
    <Space
      style={{ width: '100%' }}
      className={styles.objectivesPreview}
      direction="vertical"
      size="middle"
    >
      <Container size="large" className={styles.headerContainer}>
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Space>
            <EnvironmentOutlined />
            <Select
              style={{ width: 238 }}
              value={idcTag}
              options={data.performancePlanById.resourcesScope.map(({ label, value }) => ({
                label: `${value} ${label}`,
                value: value,
              }))}
              showSearch
              onChange={(value: string) => {
                setIdcTag(value);
              }}
            />
          </Space>
          <Tabs
            className={styles.tabs}
            items={data.performancePlanById.positionScope.map(({ label, value }) => ({
              label,
              key: value,
            }))}
            activeKey={position}
            onTabClick={value => {
              setPosition(value);
            }}
          />
        </Space>
      </Container>
      <AnnualPerformanceObjectiveCard
        title="年度考核目标详情"
        loading={loading}
        dataSource={data.performancePlanById.relatedObjectives}
        extra={
          <Space>
            <CalendarOutlined />
            {`${planYearMoment.startOf('year').format('YYYY.MM.DD')} ~ ${planYearMoment
              .endOf('year')
              .format('YYYY.MM.DD')}`}
          </Space>
        }
        typeTextVersion={getPerformanceVersion(Number(data.performancePlanById.year))}
      >
        <Space style={{ width: '100%', justifyContent: 'space-between' }} align="center">
          <Typography.Title level={5}>年度目标</Typography.Title>
          <AnnualPerformanceScoringPopover
            ruleTextVersion={getPerformanceVersion(Number(data.performancePlanById.year))}
          />
        </Space>
      </AnnualPerformanceObjectiveCard>
    </Space>
  );
}
