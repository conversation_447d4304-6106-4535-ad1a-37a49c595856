import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';
import { usePerformanceGradesNames } from '@manyun/hrm.gql.client.hrm';
import type { AnnualPerformanceObjectiveJSON } from '@manyun/hrm.model.annual-performance-objective';

type LabelInValue = AnnualPerformanceObjectiveJSON & { value: string; label: string };

export type RedLineObjectivesSelectProps = {
  trigger?: 'onFocus' | 'onDidMount';
  onChange?: (value: string | LabelInValue) => void;
} & SelectProps;

export const RedLineObjectivesSelect = ({
  trigger = 'onDidMount',
  ...restProps
}: RedLineObjectivesSelectProps) => {
  const { loading, data } = usePerformanceGradesNames({
    variables: {
      type: 'RED_LINE',
    },
  });

  return (
    <Select
      loading={loading}
      {...restProps}
      options={(data?.performanceGradesNames?.data ?? []).map(name => ({
        label: name,
        value: name,
      }))}
    />
  );
};
