import React from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';
import {
  HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH,
  generateEditRedLineRecord,
} from '@manyun/hrm.route.hrm-routes';

export type RedlineRecordMutatorButtonProps = {
  id?: number;
} & Omit<ButtonProps, 'onClick' | 'id'>;

export const RedlineRecordMutatorButton = ({
  id,
  ...restProps
}: RedlineRecordMutatorButtonProps) => {
  const history = useHistory();

  return (
    <Button
      {...restProps}
      onClick={() => {
        history.push(
          id
            ? generateEditRedLineRecord({ id: id.toString() })
            : HRM_PERFORMANCE_RED_LINE_CREATOR_ROUTE_PATH
        );
      }}
    >
      {id ? '修改' : '新增红线记录'}
    </Button>
  );
};
