import React, { useState } from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { deleteDailyPerformanceGradeRecord } from '@manyun/hrm.service.delete-daily-performance-grade-record';

export type DailyPerformanceGradeDeleteButtonProps = {
  id: number;
  onSuccess?: () => void;
};

export const RedLineRecordDeleteButton = ({
  id,
  onSuccess,
}: DailyPerformanceGradeDeleteButtonProps) => {
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-daily-performance-grade-delete',
  });

  const [loading, setLoading] = useState(false);

  if (!authorized) {
    return null;
  }

  return (
    <Popconfirm
      placement="topRight"
      title="删除该条红线记录？删除后不可恢复"
      okText="确认删除"
      cancelText="取消"
      okButtonProps={{ loading }}
      onConfirm={async () => {
        setLoading(true);
        const { error } = await deleteDailyPerformanceGradeRecord({ id: id });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功');
        onSuccess?.();
      }}
    >
      <Button type="link" compact>
        删除
      </Button>
    </Popconfirm>
  );
};
