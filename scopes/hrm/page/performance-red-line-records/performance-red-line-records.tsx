/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-8-7
 *
 * @packageDocumentation
 */
import omit from 'lodash.omit';
import uniq from 'lodash.uniq';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useCallback } from 'react';
import { useLocation } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import type { ColumnType } from '@manyun/base-ui.ui.edit-columns';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { EditColumns } from '@manyun/dc-brain.ui.edit-columns';
import { usePaginatedPerformanceGrades } from '@manyun/hrm.gql.client.hrm';
import { getDailyPerformanceGradeLocales } from '@manyun/hrm.model.daily-performance-grade';
import type {
  DailyPerformanceGradeJSON,
  DailyPerformanceGradeLocales,
} from '@manyun/hrm.model.daily-performance-grade';
import { exportPerformanceDailyGradeRecords } from '@manyun/hrm.service.export-performance-daily-grade-records';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-performance-daily-grade-records';
import { useAuthorized } from '@manyun/iam.hook.use-authorized';
import { UserLink } from '@manyun/iam.ui.user-link';
import { UserSelect } from '@manyun/iam.ui.user-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';
import type { Space as SpaceNode } from '@manyun/resource-hub.ui.location-cascader';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { RedLineObjectivesSelect } from './components/red-line-objective-select.js';
import { RedLineRecordDeleteButton } from './components/red-line-record-deleter.js';
import { RedlineRecordMutatorButton } from './components/red-line-record-mutator.js';

type FiltersFormValues = {
  staffId?: number;
  createTimeRange?: [Moment, Moment];
  occurTimeRange?: [Moment, Moment];
  location?: string[];
  eqName?: string;
};

const getDefaultTableColumns = ({
  locales,
  defaultSortOrderMapper,
  authorized,
  refetch,
}: {
  locales: DailyPerformanceGradeLocales;
  defaultSortOrderMapper?: Record<string, ColumnType['defaultSortOrder']>;
  authorized?: boolean;
  refetch: () => void;
}) => {
  return [
    {
      title: '直接责任人',
      dataIndex: 'userId',
      fixed: 'left',
      disabled: true,
      defaultSortOrder: defaultSortOrderMapper?.userId,
      sorter: true,
      render: (_, record) => <UserLink userId={record.user.id} userName={record.user.name} />,
    },
    {
      title: locales.gradedUser.jobNo,
      dataIndex: 'jobNo',
      show: false,
      render: (_, record) => record.user.jobNo ?? '--',
    },
    {
      title: locales.gradedUser.region,
      dataIndex: 'region',
      show: false,
      sorter: true,
      defaultSortOrder: defaultSortOrderMapper?.region,
      render: (_, record) => record.user.region?.label,
    },
    {
      title: locales.gradedUser.idc,
      dataIndex: 'idc',
      sorter: true,
      defaultSortOrder: defaultSortOrderMapper?.idc,
      render: (_, record) =>
        record.user.idc?.value ? <SpaceText guid={record.user.idc.value} /> : '--',
    },
    {
      title: locales.gradedUser.block,
      dataIndex: 'blockGuid',
      render: (_, record) => record.user.block?.label ?? '--',
    },
    {
      title: '红线维度',
      dataIndex: 'name',
    },
    {
      title: locales.measurements,
      dataIndex: 'measurements',
      render: val => (
        <Typography.Paragraph
          style={{ maxWidth: 344, marginBottom: 0 }}
          ellipsis={{ rows: 1, tooltip: val }}
        >
          {val}
        </Typography.Paragraph>
      ),
    },
    {
      title: '红线行为描述',
      dataIndex: 'gradeDesc',
      render: val => (
        <Typography.Paragraph
          style={{ maxWidth: 344, marginBottom: 0 }}
          ellipsis={{ rows: 1, tooltip: val }}
        >
          {val}
        </Typography.Paragraph>
      ),
    },
    {
      title: locales.occurTime,
      dataIndex: 'occurTime',
      sorter: true,
      defaultSortOrder: defaultSortOrderMapper?.occurTime,
      render: val => moment(val).format('YYYY-MM-DD'),
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      render: (_, record) => record.createdBy.name,
    },
    {
      title: '创建时间',
      dataIndex: 'createdAt',
      sorter: true,
      defaultSortOrder: defaultSortOrderMapper?.createdAt,
      render: val => moment(val).format('YYYY-MM-DD HH:mm:ss'),
    },
    {
      title: '操作',
      dataIndex: 'options',
      fixed: 'right',
      disabled: true,
      render: (_, record) => {
        return (
          <Space>
            {(record.attachments ?? []).length > 0 ? (
              <SimpleFileList
                files={record.attachments ?? []}
                children={
                  <Button type="link" compact>
                    附件
                  </Button>
                }
              />
            ) : null}
            {authorized && (
              <>
                <RedlineRecordMutatorButton type="link" compact id={record.id} />
                <RedLineRecordDeleteButton
                  id={record.id}
                  onSuccess={() => {
                    refetch();
                  }}
                />
              </>
            )}
          </Space>
        );
      },
    },
  ] as ColumnType<DailyPerformanceGradeJSON>[];
};

type OrderField =
  | 'userId'
  | 'region'
  | 'idc'
  | 'name'
  | 'type'
  | 'occurTime'
  | 'createdAt'
  | 'modifiedAt';

export type Field = Omit<
  SvcQuery,
  'orderBy' | 'regionCodes' | 'idcTags' | 'blockGuids' | 'modifiedTimeRange'
> & {
  orderField?: OrderField;
  orderType?: ColumnType['defaultSortOrder'];
  location?: string[];
  blockGuidList?: string[];
};

const defaultPagination = { page: 1, pageSize: 10 };

export function PerformanceRedLineRecords() {
  const [form] = Form.useForm();
  const [{ authorized }] = useAuthorized({ code: 'element_hrm-red-line-record-operate' });
  const { search } = useLocation();
  const { page, pageSize, location, ...restDefaultFields } = getLocationSearchMap<
    Partial<Omit<Field, 'location'>> & { location?: string }
  >(search, {
    parseNumbers: true,
    arrayKeys: ['createTimeRange', 'occurTimeRange', 'blockGuidList'],
    arrayFormatSeparator: '|',
  });
  const [blockGuidList, setBlockGuidList] = React.useState<string[]>(
    restDefaultFields?.blockGuidList ?? []
  );

  const locales = React.useMemo(() => getDailyPerformanceGradeLocales(), []);
  const defaultLocation = React.useMemo(() => {
    if (typeof location == 'string') {
      return JSON.parse(location);
    }
  }, [location]);

  const [fields, setFields] = React.useState<Field>({
    page: page ?? defaultPagination.page,
    pageSize: pageSize ?? defaultPagination.pageSize,
    location: defaultLocation,
    ...restDefaultFields,
  });

  const [tableColumns, setTableColumns] = React.useState<ColumnType<DailyPerformanceGradeJSON>[]>(
    getDefaultTableColumns({
      defaultSortOrderMapper: restDefaultFields.orderField
        ? { [restDefaultFields.orderField]: restDefaultFields.orderType }
        : undefined,
      locales,
      authorized,
      refetch: () => {
        onSearch({ ...fields, page: 1 });
      },
    })
  );

  const filterItems = React.useMemo(
    () => [
      {
        label: '所属位置',
        name: 'location',
        control: (
          <LocationCascader
            nodeTypes={['REGION', 'IDC', 'BLOCK']}
            maxTagCount="responsive"
            multiple
            allowClear
            authorizedOnly
            onChange={(value, options) => {
              const _options = options as SpaceNode[][];
              let blockGuidList: string[] = [];
              if (_options) {
                blockGuidList = generateBlockGuidList(_options);
              }
              setBlockGuidList(uniq(blockGuidList));
            }}
          />
        ),
      },
      {
        label: '红线维度',
        name: 'eqName',
        control: <RedLineObjectivesSelect allowClear />,
      },
      {
        label: '发生日期',
        name: 'occurTimeRange',
        span: 1,
        control: <DatePicker.RangePicker allowClear />,
      },
      {
        label: '员工姓名',
        name: 'staffId',
        control: <UserSelect allowClear labelInValue={false} userState="in-service" />,
      },
      {
        label: '创建时间',
        name: 'createTimeRange',
        span: 1,
        control: <DatePicker.RangePicker allowClear />,
      },
    ],
    []
  );

  const { loading, data, refetch } = usePaginatedPerformanceGrades({
    variables: {
      query: transformQuery({
        page: page ?? defaultPagination.page,
        pageSize: pageSize ?? defaultPagination.pageSize,
        location: defaultLocation,
        ...restDefaultFields,
      }),
    },
  });

  const onSearch = useCallback(
    (params: Field) => {
      setFields(params);
      setLocationSearch(
        {
          ...params,
          location: params.location ? JSON.stringify(params.location) : undefined,
        },
        {
          arrayFormat: 'separator',
          arrayFormatSeparator: '|',
        }
      );
      refetch({
        query: transformQuery(params),
      });
    },
    [refetch]
  );

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Typography.Title level={5} showBadge>
          运维红线记录
        </Typography.Title>
        <QueryFilter<FiltersFormValues>
          initialValues={
            {
              ...fields,
              staffId: fields.staffId,
              occurTimeRange: fields.occurTimeRange
                ? [moment(fields.occurTimeRange[0]), moment(fields.occurTimeRange[1])]
                : undefined,
              createTimeRange: fields.createTimeRange
                ? [moment(fields.createTimeRange[0]), moment(fields.createTimeRange[1])]
                : undefined,
            } as FiltersFormValues
          }
          form={form}
          items={filterItems}
          onSearch={filterFormValues => {
            onSearch({
              ...fields,
              ...defaultPagination,
              ...filterFormValues,
              blockGuidList,
              occurTimeRange: filterFormValues.occurTimeRange
                ? [
                    moment(filterFormValues.occurTimeRange[0]).startOf('day').valueOf(),
                    moment(filterFormValues.occurTimeRange[1]).endOf('day').valueOf(),
                  ]
                : undefined,
              createTimeRange: filterFormValues.createTimeRange
                ? [
                    moment(filterFormValues.createTimeRange[0]).startOf('day').valueOf(),
                    moment(filterFormValues.createTimeRange[1]).endOf('day').valueOf(),
                  ]
                : undefined,
            });
          }}
          onReset={() => {
            onSearch(defaultPagination);
            form.setFieldsValue({
              staffId: undefined,
              eqName: undefined,
              occurTimeRange: undefined,
              createTimeRange: undefined,
              location: undefined,
            });
          }}
        />
        <Space
          style={{ justifyContent: 'space-between', width: '100%' }}
          size="middle"
          align="center"
        >
          <div>{authorized && <RedlineRecordMutatorButton type="primary" />}</div>
          <Space align="center" size="middle">
            <EditColumns
              listsHeight={360}
              uniqKey="HRM_PAGE_PERFORMANCE_RED_LINE_RECORDS"
              defaultValue={getDefaultTableColumns({
                locales,
                authorized,
                refetch: () => {
                  onSearch({ ...fields, page: 1 });
                },
              })}
              onChange={columns => {
                setTableColumns(columns);
              }}
            />
            <FileExport
              text="导出"
              filename="运维红线记录"
              showExportFiltered={false}
              data={async type => {
                const { error, data } = await exportPerformanceDailyGradeRecords(
                  transformQuery(fields)
                );

                if (error) {
                  message.error(error.message);
                  return;
                }
                return data;
              }}
            />
          </Space>
        </Space>
        <Table
          scroll={{ x: 'max-content' }}
          loading={loading}
          dataSource={data?.paginatedPerformanceGrades?.data}
          columns={tableColumns}
          pagination={{
            total: data?.paginatedPerformanceGrades.total,
            current: fields.page,
            pageSize: fields.pageSize,
          }}
          onChange={({ current, pageSize }, _, sorter, { action }) => {
            if (action === 'paginate') {
              const mergedValues = { ...fields, page: current!, pageSize: pageSize! };
              onSearch(mergedValues);
            } else if (action === 'sort') {
              if (sorter && !Array.isArray(sorter)) {
                const mergedValues = {
                  ...fields,
                  ...defaultPagination,
                  orderField: sorter.field as OrderField,
                  orderType: sorter.order,
                };
                onSearch(mergedValues);
              }
            }
          }}
        />
      </Space>
    </Card>
  );
}
const transformQuery = (fields: Field) => {
  const location = fields.location;
  const regionCodes: string[] = [],
    idcTags: string[] = [],
    blockGuidList: string[] = [];
  if (location) {
    location.forEach(codes => {
      if (codes.length === 1) {
        regionCodes.push(codes[0]);
      } else if (codes.length === 2) {
        idcTags.push(codes[1]);
      } else if (codes.length === 3) {
        blockGuidList.push(codes[2]);
      }
    });
  }
  const _query: SvcQuery = {
    ...omit(fields, 'location', 'blockGuidList'),
    blockGuids: (fields.blockGuidList ?? []).length > 0 ? fields.blockGuidList : undefined,
    type: 'RED_LINE',
    orderBy: fields.orderField ? { [fields.orderField]: fields.orderType ?? undefined } : undefined,
  };

  return omit(_query, ['year', 'orderField', 'orderType', 'period']) as SvcQuery;
};

const generateBlockGuidList = (spaceNodes: SpaceNode[][]) => {
  const blockGuidList: string[] = [];
  const loopNode = (node: SpaceNode) => {
    if (node.type === 'BLOCK') {
      blockGuidList.push(node.value);
    } else if (node.type === 'IDC' && (node.children ?? []).length === 0) {
      blockGuidList.push(`${node.value}.${node.value}`);
    } else if (node.children) {
      node.children.forEach(_node => {
        loopNode(_node);
      });
    }
  };
  spaceNodes.forEach(nodes => {
    const lastedNode = nodes[nodes.length - 1];
    if (lastedNode) {
      loopNode(lastedNode);
    }
  });
  return blockGuidList;
};
