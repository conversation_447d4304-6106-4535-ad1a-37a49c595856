import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import uniq from 'lodash.uniq';
import moment from 'moment';
import useDeepCompareEffect from 'use-deep-compare-effect';

import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import type { QueryFilterProps } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { selectMyResourceCodes } from '@manyun/auth-hub.state.user';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { getClockInLocales } from '@manyun/hrm.model.clock-in';
import type { ClockInJSON } from '@manyun/hrm.model.clock-in';
import { exportPunchClockRecords } from '@manyun/hrm.service.export-punch-clock-records';
import type { SvcQuery as ExportSvcQuery } from '@manyun/hrm.service.export-punch-clock-records';
import { fetchPagedPunchClocks } from '@manyun/hrm.service.fetch-paged-punch-clocks';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-punch-clocks';
import { AttGroupSelect } from '@manyun/hrm.ui.att-group-select';
import type { SpaceTreeNode } from '@manyun/resource-hub.ui.location-tree-select';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { ClockInResultSelect, ClockInResultTag } from './components/clock-in-result';
import { ClockInWaySelect, ClockInWayText } from './components/clock-in-way';

const defaultField: SvcQuery = {
  page: 1,
  pageSize: 10,
  /**默认有效 */
  checkResult: '0',
  punchTime: [moment().startOf('month').valueOf(), moment().endOf('day').valueOf()],
};

export function PunchClockRecords() {
  const [form] = Form.useForm();
  const [authorized] = useAuthorized({ checkByCode: 'element_location-filter' });
  const myResourceCodes = useSelector(selectMyResourceCodes);

  const [state, setState] = useState<{
    loading: boolean;
    data: ClockInJSON[];
    total: number;
    fields: SvcQuery;
    exportLoading: boolean;
  }>({
    loading: false,
    data: [],
    total: 0,
    fields: defaultField,
    exportLoading: false,
  });

  const locales = useMemo(() => getClockInLocales(), []);

  const getSearchParams = useCallback(() => {
    return {
      ...state.fields,
      blockTags: authorized
        ? state.fields.blockTags
        : (myResourceCodes ?? []).filter((d: string) => d.split('.').length === 2),
      idcTags:
        (state.fields.idcTags ?? []).length > 0 && (state.fields.blockTags ?? []).length === 0
          ? state.fields.idcTags
          : undefined,
    };
  }, [authorized, myResourceCodes, state.fields]);

  const loadData = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const params = getSearchParams();
    const { error, data } = await fetchPagedPunchClocks(params);
    setState(pre => ({ ...pre, loading: false }));

    if (error) {
      message.error(error.message);
      return;
    }
    setState(pre => ({ ...pre, data: data.data, total: data.total }));
  }, [getSearchParams]);

  useDeepCompareEffect(() => {
    loadData();
  }, [loadData]);

  const handleFileExport = useCallback(
    async (type: 'all' | 'selected' | 'filtered') => {
      let params: ExportSvcQuery = {};
      if (type !== 'all') {
        params = getSearchParams();
      }
      setState(pre => ({ ...pre, exportLoading: true }));
      const { error, data } = await exportPunchClockRecords(params);
      setState(pre => ({ ...pre, exportLoading: false }));
      if (error) {
        message.error(error.message);
        return error.message;
      }
      return data;
    },
    [getSearchParams]
  );

  const items = React.useMemo(
    () =>
      [
        {
          label: locales.applyBy.name ?? '姓名',
          name: 'staffId',
          control: <UserSelect allowClear />,
        },
        {
          label: locales.attGroupName ?? '考勤组',
          name: 'attGroupId',
          control: <AttGroupSelect trigger="onDidMount" showArrow={false} showSearch allowClear />,
        },
        {
          label: locales.result._slef ?? '打卡结果',
          name: 'checkResult',
          control: <ClockInResultSelect allowClear style={{ width: 200 }} />,
        },
        {
          label: locales.punchWay._self ?? '打卡类型',
          name: 'checkWays',
          control: (
            <ClockInWaySelect mode="multiple" maxTagCount={1} style={{ width: 200 }} allowClear />
          ),
        },
        authorized && {
          label: locales.location ?? '位置',
          name: 'blockTags',
          control: <LocationTreeSelect multiple maxTagCount={1} authorizedOnly />,
          getValueFromEvent: (
            value: string | string[],
            _: string,
            { dataRef }: { dataRef: SpaceTreeNode[] }
          ) => {
            const idcTags: string[] = [];
            let blockTags: string[] = [];
            (dataRef ?? []).forEach(node => {
              if (node.type === 'IDC') {
                idcTags.push(node.value);
                blockTags = [...blockTags, ...(node.children ?? []).map(n => n.value)];
              } else if (node.type === 'BLOCK') {
                blockTags.push(node.value);
              }
            });
            return {
              value,
              idcTags: idcTags,
              blockTags: uniq(blockTags),
            };
          },
          getValueProps: (value: { value: string | string[] }) => {
            return { value: value?.value };
          },
        },
        {
          label: locales.punchDate ?? '打卡日期',
          name: 'punchTime',
          span: 2,
          control: (
            <DatePicker.RangePicker
              allowClear={false}
              format="YYYY-MM-DD"
              disabledDate={current =>
                current.clone().startOf('day').diff(moment().startOf('day')) > 0
              }
            />
          ),
        },
      ].filter(Boolean) as QueryFilterProps['items'],
    [
      authorized,
      locales.applyBy.name,
      locales.attGroupName,
      locales.location,
      locales.punchDate,
      locales.punchWay._self,
      locales.result._slef,
    ]
  );

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card title={locales.title ?? '原始记录'}>
        <QueryFilter
          initialValues={{
            checkResult: '0',
            punchTime: [moment().startOf('month'), moment().endOf('day')],
          }}
          form={form}
          items={items}
          onSearch={(values: any) => {
            const searchField: Record<string, string | number[] | undefined> = {};
            Object.keys(values).forEach((key: string) => {
              const value = values[key];
              if (key === 'blockTags') {
                searchField.blockTags = value?.blockTags;
                searchField.idcTags = value?.idcTags;
              } else if (key === 'punchTime') {
                searchField[key] = value
                  ? [
                      moment(value[0]).startOf('day').valueOf(),
                      moment(value[1]).endOf('day').valueOf(),
                    ]
                  : undefined;
              } else if (key === 'staffId') {
                searchField[key] = value?.value;
              } else {
                searchField[key] = value;
              }
            });
            setState(pre => ({
              ...pre,
              fields: {
                ...defaultField,
                ...searchField,
                page: 1,
                pageSize: 10,
              },
            }));
          }}
          onReset={() => {
            setState(pre => ({ ...pre, fields: defaultField }));
          }}
        />
      </Card>
      <Card>
        <Space direction="vertical" style={{ width: '100%' }}>
          <FileExport
            text={locales.export._self ?? '导出'}
            filename={locales.export.fileName ?? '人员原始考勤记录'}
            disabled={state.exportLoading || state.total === 0}
            data={type => {
              return handleFileExport(type);
            }}
            showExportFiltered
          />

          <Table
            rowKey="id"
            loading={state.loading}
            scroll={{ x: 'max-content' }}
            dataSource={state.data}
            pagination={{
              total: state.total,
              current: state.fields.page,
              pageSize: state.fields.pageSize,
            }}
            columns={[
              {
                title: locales.applyBy.id ?? '人员ID',
                dataIndex: 'applyUser.id',
                fixed: 'left',
                render: (_, record) => record.applyUser?.id,
              },
              {
                title: locales.applyBy.name ?? '姓名',
                dataIndex: 'applyUser.name',
                fixed: 'left',
                render: (_, record) => record.applyUser?.name,
              },
              {
                title: locales.location ?? '位置',
                dataIndex: 'location',
                render: val => val ?? '--',
              },
              {
                title: locales.attGroupName ?? '考勤组',
                dataIndex: 'attGroupName',
                render: attGroupName => attGroupName ?? '--',
              },
              {
                title: locales.scheduleDate ?? '考勤日期',
                dataIndex: 'scheduleDate',
              },
              {
                title: locales.punchTime ?? '打卡时间',
                dataIndex: 'punchTime',
              },
              {
                title: locales.punchWay._self ?? '打卡类型',
                dataIndex: 'way',
                render: value => <ClockInWayText way={value} />,
              },
              {
                title: locales.punchType._self ?? '上/下班卡',
                dataIndex: 'type',
              },
              {
                title: locales.punchChannel ?? '打卡方式',
                dataIndex: 'channel',
              },
              {
                title: (
                  <Explanation
                    iconType="question"
                    tooltip={{
                      title:
                        locales.result.tooltip ??
                        '有对应班次的打卡会被计为有效打卡，否则为无效打卡',
                    }}
                  >
                    {locales.result._slef ?? '打卡结果'}
                  </Explanation>
                ),
                dataIndex: 'result',
                render: (value, record) => (
                  <ClockInResultTag value={value} failReason={record.reason} />
                ),
              },
            ]}
            onChange={pagination => {
              setState(pre => ({
                ...pre,
                fields: {
                  ...pre.fields,
                  page: pagination.current!,
                  pageSize: pagination.pageSize!,
                },
              }));
            }}
          />
        </Space>
      </Card>
    </Space>
  );
}
