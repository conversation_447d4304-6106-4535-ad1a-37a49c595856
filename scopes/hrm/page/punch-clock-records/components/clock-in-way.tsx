import React, { useMemo } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { getClockInLocales } from '@manyun/hrm.model.clock-in';
import { PunchWay } from '@manyun/hrm.model.clock-in';

const usePunchWapOptions = () => {
  const locales = useMemo(() => getClockInLocales(), []);
  const data = useMemo(() => {
    const mapper = locales.punchWay.enum;
    return {
      mapper,
      options: Object.keys(mapper).map(key => ({ label: mapper[key as PunchWay], value: key })),
    };
  }, [locales.punchWay.enum]);
  return [data] as const;
};

export const ClockInWaySelect = React.forwardRef(
  ({ ...props }: SelectProps, ref?: React.Ref<RefSelectProps>) => {
    const [{ options }] = usePunchWapOptions();
    return <Select {...props} ref={ref} options={options} />;
  }
);
ClockInWaySelect.displayName = 'ClockInWaySelect';

export const ClockInWayText = ({ way }: { way: PunchWay }) => {
  const [{ mapper }] = usePunchWapOptions();

  return <>{mapper[way] ? mapper[way] : way}</>;
};
