import React, { useMemo } from 'react';

import { Explanation } from '@manyun/base-ui.ui.explanation';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import { Typography } from '@manyun/base-ui.ui.typography';

import { PunchResult, getClockInLocales } from '@manyun/hrm.model.clock-in';

const usePunchWapOptions = () => {
  const locales = useMemo(() => getClockInLocales(), []);
  const data = useMemo(() => {
    const mapper = locales.result.enum;
    return {
      mapper,
      options: Object.keys(mapper).map(key => ({ label: mapper[key as PunchResult], value: key })),
    };
  }, [locales.result.enum]);
  return [data] as const;
};

export type ClockInResultSelectProps = Omit<SelectProps, 'options'>;

export const ClockInResultSelect = React.forwardRef(
  ({ ...props }: ClockInResultSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const [{ options }] = usePunchWapOptions();
    return <Select {...props} ref={ref} options={options} />;
  }
);
ClockInResultSelect.displayName = 'ClockInResultSelect';

export type ClockInResultTagProps = { value: PunchResult; failReason?: string };

export const ClockInResultTag = ({ value, failReason }: ClockInResultTagProps) => {
  const [{ mapper }] = usePunchWapOptions();

  const isValid = mapper[value] ? value === '0' : value === mapper['0'];
  const title = mapper[value] ? mapper[value] : value;

  const renderTitle = () => {
    return <Typography.Text type={isValid ? 'success' : 'danger'}>{title}</Typography.Text>;
  };

  return isValid ? (
    renderTitle()
  ) : (
    <Explanation iconType="question" tooltip={{ title: failReason }}>
      {renderTitle()}
    </Explanation>
  );
};
