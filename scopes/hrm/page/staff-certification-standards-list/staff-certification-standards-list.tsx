/* eslint-disable @typescript-eslint/no-explicit-any */

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-4
 *
 * @packageDocumentation
 */
import { nanoid } from 'nanoid';
import React from 'react';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { createStaffCertificationStandards } from '@manyun/hrm.service.create-staff-certification-standards';
import { deleteStaffCertificationStandards } from '@manyun/hrm.service.delete-staff-certification-standards';
import { fetchPagedStaffCertificationStandardsList } from '@manyun/hrm.service.fetch-paged-staff-certification-standards-list';
import { fetchStaffCertificationNames } from '@manyun/hrm.service.fetch-staff-certification-names';
import { updateStaffCertificationStandards } from '@manyun/hrm.service.update-staff-certification-standards';
import { MetaTypeSelect } from '@manyun/resource-hub.ui.meta-type-select';

export type StaffCertificationStandardsListProps = {};

const useStaffCertificationStandards = () => {
  const [loading, setLoading] = React.useState(false);
  const [operateLoading, setOperateLoading] = React.useState(false);
  const [deleteLoading, setDeleteLoading] = React.useState(false);
  const [certNames, setCertNames] = React.useState<string[]>([]);
  const [data, setData] = React.useState<{
    data: StaffCertificationStandards[];
    total: number;
  }>({
    data: [],
    total: 0,
  });

  const onLoadData = React.useCallback(
    async (params: {
      position?: string;
      certification?: string;
      page: number;
      pageSize: number;
    }) => {
      setLoading(true);
      const { error, data } = await fetchPagedStaffCertificationStandardsList({
        pageNum: params.page,
        pageSize: params.pageSize,
        positionCode: params.position,
        certName: params.certification,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        setData({
          data: [],
          total: 0,
        });
        return;
      }
      setData({
        data: data.data.map(item => ({
          ...item,
          certification: item.certName,
          positionCode: item.positionCode,
          positionName: item.position,
          required: item.required === true ? 'yes' : 'no',
          needReview: item.checked === true ? 'yes' : 'no',
        })),
        total: data.total,
      });
    },
    []
  );

  const onLoadName = React.useCallback(async () => {
    const { error, data } = await fetchStaffCertificationNames({});
    if (error) {
      message.error(error.message);
      return;
    }
    setCertNames(data.data);
  }, []);

  // 创建
  const onCreate = React.useCallback(
    async (values: Omit<StaffCertificationStandards, 'id'>, callback?: (res: boolean) => void) => {
      setOperateLoading(true);
      const { error } = await createStaffCertificationStandards({
        certName: values.certification!,
        positionCode: values.positionCode!,
        required: values.required === 'yes',
        checked: values.needReview === 'yes',
      });
      setOperateLoading(false);
      if (error) {
        message.error(error.message);
        callback?.(false);
        return;
      }
      callback?.(true);
    },
    []
  );

  // 更新
  const onUpdate = React.useCallback(
    async (
      values: Partial<StaffCertificationStandards> & { id: number },
      callback?: (res: boolean) => void
    ) => {
      setOperateLoading(true);
      const { error } = await updateStaffCertificationStandards({
        id: values.id,
        certName: values.certification!,
        positionCode: values.positionCode!,
        required: values.required === 'yes',
        checked: values.needReview === 'yes',
      });
      setOperateLoading(false);
      if (error) {
        callback?.(false);
        message.error(error.message);
        return;
      }
      callback?.(true);
    },
    []
  );

  // 删除
  const onDelete = React.useCallback(async (id: number, onSuccess?: () => void) => {
    setDeleteLoading(true);
    const { error } = await deleteStaffCertificationStandards({ id });
    setDeleteLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('删除成功');
    onSuccess?.();
  }, []);

  return [
    {
      loading,
      data,
      operateLoading,
      deleteLoading,
      certNames,
    },
    { onLoadData, onCreate, onUpdate, onDelete, onLoadName },
  ] as const;
};

type StaffCertificationStandards = {
  id: string | number;
  positionCode?: string;
  positionName?: string;
  certification?: string;
  required?: string;
  needReview?: string;
  isCreate?: boolean;
};

type Fields = {
  position?: string;
  positionName?: string;
  certification?: string;
  page: number;
  pageSize: number;
};

export const StaffCertificationStandardsList: React.FC = () => {
  const [form] = Form.useForm();
  const [editableKeys, setEditableKeys] = React.useState<React.Key[]>([]);
  const [fields, setFields] = React.useState<Fields>({ page: 1, pageSize: 10 });
  const [
    { loading, data, deleteLoading, certNames },
    { onLoadData, onCreate, onUpdate, onDelete, onLoadName },
  ] = useStaffCertificationStandards();

  const onReloadData = React.useCallback(
    (params: Partial<Fields>) => {
      onLoadName();
      onLoadData({ ...fields, ...params });
    },
    [fields, onLoadData, onLoadName]
  );

  React.useEffect(() => {
    onLoadName();
    onLoadData({ page: 1, pageSize: 10 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [authorized] = useAuthorized({
    checkByCode: 'element_certification-standards-operate',
  });

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          岗位资质认证标准
        </Typography.Title>
        <Form form={form} colon>
          <Space size="middle">
            <Form.Item style={{ marginBottom: 0 }} label="岗位" name="position">
              <MetaTypeSelect
                style={{ width: 278 }}
                metaType={'POSITION_YG' as any}
                showSearch
                allowClear
                filterOption={(input: string, option?: { label: string; value: string }) => {
                  if (!option?.label) {
                    return false;
                  }
                  return option.label.toLowerCase().includes(input.toLowerCase());
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }} label="证书" name="certification">
              <Select
                style={{ width: 278 }}
                showSearch
                allowClear
                options={certNames.map(name => ({
                  label: name,
                  value: name,
                }))}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <Space>
                <Button
                  type="primary"
                  onClick={() => {
                    const { position, certification } = form.getFieldsValue();
                    setFields(pre => ({ ...pre, page: 1, position, certification }));
                    setEditableKeys([]);
                    onLoadData({
                      ...fields,
                      page: 1,
                      position,
                      certification,
                    });
                  }}
                >
                  搜索
                </Button>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setFields({ page: 1, pageSize: fields.pageSize });
                    setEditableKeys([]);
                    onLoadData({
                      page: 1,
                      pageSize: fields.pageSize,
                    });
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Space>
        </Form>
        <EditableProTable<StaffCertificationStandards>
          rowKey="id"
          loading={loading}
          recordCreatorProps={
            authorized
              ? {
                  position: 'top',
                  record: () => ({
                    id: nanoid(),
                    isCreate: true,
                    required: 'yes',
                    needReview: 'yes',
                  }),
                  creatorButtonText: '添加资质认证标准',
                }
              : false
          }
          columns={[
            {
              title: '岗位',
              dataIndex: 'positionCode',
              readonly: true,
              formItemProps() {
                return {
                  rules: [
                    {
                      required: true,
                      message: '岗位必选',
                    },
                  ],
                };
              },
              renderFormItem: (_, { record }) => {
                const isNew = record?.isCreate;
                return (
                  <MetaTypeSelect
                    metaType={'POSITION_YG' as any}
                    disabled={!isNew}
                    filterOption={(input: string, option?: { label: string; value: string }) => {
                      if (!option?.label) {
                        return false;
                      }
                      return option.label.toLowerCase().includes(input.toLowerCase());
                    }}
                    showSearch
                  />
                );
              },
              render: (val, record) => {
                return record.positionName ?? '--';
              },
            },
            {
              title: '资质证书',
              dataIndex: 'certification',
              render: val => {
                return val;
              },
              formItemProps() {
                return {
                  rules: [
                    {
                      required: true,
                      whitespace: true,
                      message: '资质证书必填',
                    },
                    {
                      max: 30,
                      message: '最多输入 30 个字符！',
                    },
                  ],
                };
              },
              renderFormItem: () => {
                return <Input />;
              },
            },
            {
              title: '是否必传',
              dataIndex: 'required',
              width: 160,
              formItemProps() {
                return {
                  rules: [
                    {
                      required: true,
                      message: '是否必传必选',
                    },
                  ],
                };
              },
              render: val => {
                return val === 'yes' ? '是' : ' 否';
              },
              renderFormItem: () => {
                return (
                  <Radio.Group>
                    <Radio value="yes">是</Radio>
                    <Radio value="no">否</Radio>
                  </Radio.Group>
                );
              },
            },
            {
              title: '需要复审',
              dataIndex: 'needReview',
              width: 160,
              formItemProps() {
                return {
                  rules: [
                    {
                      required: true,
                      message: '需要复审必选',
                    },
                  ],
                };
              },
              render: val => {
                return val === 'yes' ? '是' : '否';
              },
              renderFormItem: () => {
                return (
                  <Radio.Group>
                    <Radio value="yes">是</Radio>
                    <Radio value="no">否</Radio>
                  </Radio.Group>
                );
              },
            },
            {
              title: '操作',
              valueType: 'option',
              width: 110,
              render: (_, record: StaffCertificationStandards, __, action: any) => {
                if (authorized) {
                  return [
                    <Button
                      key="edit"
                      type="link"
                      compact
                      onClick={() => {
                        action?.startEditable?.(record.id);
                      }}
                    >
                      编辑
                    </Button>,
                    <Popconfirm
                      key="delete"
                      placement="topRight"
                      title="确认删除该认证标准？"
                      okText="确认删除"
                      okButtonProps={{
                        disabled: deleteLoading,
                      }}
                      cancelText="取消"
                      onConfirm={() => {
                        onDelete(Number(record.id), () => {
                          setFields(pre => ({ ...pre, page: 1 }));
                          onReloadData({
                            page: 1,
                          });
                        });
                      }}
                    >
                      <Button key="deleteBtn" type="link" compact>
                        删除
                      </Button>
                    </Popconfirm>,
                  ];
                }
                return '--';
              },
            },
          ]}
          value={data.data}
          editable={{
            type: 'single',
            editableKeys,
            onChange: setEditableKeys,
            actionRender: (row, config, defaultDom) => [defaultDom.save, defaultDom.cancel],
            onSave: async (rowKey, data, row) => {
              return new Promise((resolve, reject) => {
                const params = {
                  certification: data.certification,
                  positionCode: data.positionCode,
                  required: data.required,
                  needReview: data.needReview,
                };
                const onHandleResult = (res: boolean) => {
                  if (res) {
                    message.success('保存成功');
                    setEditableKeys([]);
                    setFields(pre => ({ ...pre, page: 1 }));
                    onReloadData({ page: 1 });
                    resolve(true);
                  } else {
                    reject(new Error('保存失败'));
                  }
                };
                if (data.isCreate) {
                  onCreate(params, onHandleResult);
                } else {
                  onUpdate({ ...params, id: data.id as number }, onHandleResult);
                }
              });
            },
          }}
          pagination={{
            current: fields.page,
            pageSize: fields.pageSize,
            total: data.total,
            showTotal: total => `共 ${total} 条`,
            pageSizeOptions: ['10', '20', '30', '50', '100', '200'],
            showSizeChanger: true,
            showQuickJumper: data.total > 10,
            onChange: (page, pageSize) => {
              setFields(prev => ({
                ...prev,
                page,
                pageSize,
              }));
              onLoadData({
                ...fields,
                page,
                pageSize,
              });
            },
          }}
        />
      </Space>
    </Card>
  );
};
