import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import UserOutlined from '@ant-design/icons/es/icons/UserOutlined';
import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useLocation } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserGroupsCard } from '@manyun/auth-hub.ui.user-groups-card';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { PerformanceColorful, TargetColorful } from '@manyun/base-ui.icons';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import type { StepsProps } from '@manyun/base-ui.ui.steps';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getLocationSearchMap, setLocationSearch } from '@manyun/base-ui.util.query-string';
import { useLazyUserPerformances } from '@manyun/hrm.gql.client.hrm';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type { AnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import type {
  BackendPerformanceEvaluationStatus,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';
import { fetchUserBindPerformancePositionByYear } from '@manyun/hrm.service.fetch-user-bind-performance-position-by-year';
import { whetherUserNeedAnnualEval } from '@manyun/hrm.service.whether-user-need-annual-eval';
import { ChangePerformanceTargetLink } from '@manyun/hrm.ui.change-performance-target-link';
import { PerformancePositionList } from '@manyun/hrm.ui.performance-position';
import { PerformanceStatusTag } from '@manyun/hrm.ui.performance-status-tag';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { ChangeTargetModalView } from './components/change-target-modal-view';
import { PerformanceLink } from './components/performance-link';
import { AnnualPerformanceSteps } from './components/​annual-performance-steps';
import styles from './user-annual-performances.module.less';

export function UserAnnualPerformances() {
  const { search } = useLocation();

  const defaultFields = getLocationSearchMap<{ year?: number }>(search, { parseNumbers: true });
  const planLocales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const [year, setYear] = useState<number>(defaultFields.year ?? moment().year());
  const [positionLoading, setPositionLoading] = useState(false);
  //用户是否需要进行考核
  const [userNeedPerformance, setUserNeedPerformance] = useState(false);
  //用户绑定的考核岗位
  const [userPerformancePosition, setUserPerformancePosition] = useState<string | null>(null);
  const [fetch, { loading, data }] = useLazyUserPerformances();

  const [stepItems, setStepItems] = useState<StepsProps['items']>([]);
  const [currentStep, setCurrentStep] = useState<number | undefined>();
  const selectedPerformance = useDeepCompareMemo(
    () => (currentStep !== undefined ? data?.myPerformances[currentStep] : undefined),
    [currentStep, data?.myPerformances]
  );
  const [changeTarget, setChangeTarget] = useState<PerformanceJSON | null>(null);

  useEffect(() => {
    (async () => {
      const { error, data } = await whetherUserNeedAnnualEval();
      if (error) {
        message.error(error.message);
        return;
      }
      setUserNeedPerformance(data);
    })();
  }, []);

  useEffect(() => {
    if (year && userNeedPerformance) {
      (async () => {
        setPositionLoading(true);
        const { error, data } = await fetchUserBindPerformancePositionByYear({
          userId: userId!,
          year: year.toString(),
        });
        setPositionLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }

        setUserPerformancePosition(data);
        // setHasSettingPerformancePosition(data ? true : false);
      })();
    }
  }, [userId, userNeedPerformance, year]);

  useEffect(() => {
    if (userPerformancePosition && year) {
      /**获取用户绩效列表 */
      fetch({
        variables: {
          type: 'REGULAR',
          year: year.toString(),
          userId: userId!,
          performancePosition: userPerformancePosition,
        },
      }).then(result => {
        if (result.data?.myPerformances) {
          const targetPerformance = result.data.myPerformances.find(pf => pf.subType === 'TARGET');

          if (
            targetPerformance &&
            targetPerformance.objectiveStatus &&
            targetPerformance.objectiveStatus === 'TARGET_FINISHED' &&
            year === moment().get('year')
          ) {
            /**自然年内（即12月31日前，含12.31），且年度目标为「已完成」状态 显示目标变更 */
            setChangeTarget(targetPerformance);
          } else {
            setChangeTarget(null);
          }

          const stepItems = generateSteps({
            data: result.data.myPerformances,
            locales: planLocales,
          });
          setStepItems(stepItems);

          /**
           *首先锚定在未完成且当前待办人为员工本人的子任务
           */
          const currentNodeIndex = stepItems?.findIndex(step =>
            step.evaluationStatus
              ? step.status === 'process' &&
                ['SELF_EVAL', 'SELF_CONFIRM'].includes(step.evaluationStatus)
              : step.status === 'process'
          );

          const nextCurrentNodeIndex = stepItems?.findIndex(step => step.status === 'process');
          setCurrentStep(
            currentNodeIndex > -1
              ? currentNodeIndex
              : nextCurrentNodeIndex > -1
                ? nextCurrentNodeIndex
                : stepItems?.findIndex(step => step.status === 'wait')
          );
        }
      });
    }
  }, [fetch, planLocales, userId, userPerformancePosition, year]);

  return (
    <Row
      style={{
        height: 'calc(var(--content-height)',
      }}
      gutter={[16, 24]}
    >
      <Col style={{ height: '100%', overflowY: 'auto' }} span={18}>
        <Card
          style={{ height: '100%', overflowY: 'auto' }}
          bodyStyle={{ height: '100%' }}
          loading={loading || positionLoading}
        >
          <Space
            className={styles.performanceContent}
            style={{ width: '100%', height: '100%' }}
            direction="vertical"
          >
            <Space style={{ width: '100%' }} direction="vertical" size="large">
              <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
                个人年度绩效
              </Typography.Title>
              {!userNeedPerformance ? (
                <Empty
                  description={
                    <div>
                      您无需在 DC Base 进行年度绩效考核
                      <br />
                      具体可与您的 HR 确认
                    </div>
                  }
                  style={{
                    margin: 'auto',
                    position: 'absolute',
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)',
                  }}
                />
              ) : positionLoading ? (
                <Skeleton />
              ) : (
                <Space style={{ width: '100%' }} direction="vertical" size="large">
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <DatePicker
                      style={{ width: 207 }}
                      picker="year"
                      format={value => `${value.format('YYYY')}年度`}
                      allowClear={false}
                      value={moment().set('year', year)}
                      onChange={value => {
                        if (value) {
                          setYear(value.get('year'));
                        }
                        setLocationSearch({ year: value?.get('year') });
                      }}
                    />
                  </Space>
                  {!userPerformancePosition ? (
                    <Space style={{ width: '100%' }} size="middle" direction="vertical">
                      <Typography.Text>请选择您的考核岗位 </Typography.Text>
                      <PerformancePositionList
                        onChange={value => {
                          setUserPerformancePosition(value);
                        }}
                      />
                    </Space>
                  ) : (
                    <>
                      {(data?.myPerformances ?? []).length > 0 ? (
                        <>
                          <AnnualPerformanceSteps
                            items={stepItems}
                            current={currentStep}
                            onChange={current => {
                              setCurrentStep(current);
                            }}
                          />
                          {selectedPerformance && (
                            <Card
                              title={
                                <Space size="large">
                                  <Space>
                                    <EnvironmentOutlined />
                                    <Typography.Text style={{ fontWeight: 400 }}>
                                      <SpaceText guid={selectedPerformance.user.idc ?? ''} />
                                    </Typography.Text>
                                  </Space>
                                  <Space>
                                    <UserOutlined />
                                    <Typography.Text style={{ fontWeight: 400 }}>
                                      {selectedPerformance.evaluationJob?.label}
                                    </Typography.Text>
                                  </Space>
                                </Space>
                              }
                            >
                              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                                <Space size="large">
                                  {selectedPerformance.subType === 'TARGET' ? (
                                    <TargetColorful style={{ fontSize: 56 }} />
                                  ) : (
                                    <PerformanceColorful style={{ fontSize: 56 }} />
                                  )}
                                  <Space direction="vertical">
                                    <Space>
                                      <Typography.Title style={{ marginBottom: 0 }} level={5}>
                                        {`${year}年度${
                                          selectedPerformance.subType === 'TARGET'
                                            ? '目标设定'
                                            : `绩效考核${
                                                selectedPerformance.period !== 'YEAR'
                                                  ? ` _${
                                                      planLocales.performancePeriod.enum[
                                                        selectedPerformance.period
                                                      ]
                                                    }`
                                                  : ''
                                              }`
                                        }`}
                                      </Typography.Title>
                                    </Space>
                                    <Space>
                                      <Typography.Text type="secondary">
                                        {selectedPerformance.subType === 'TARGET'
                                          ? '请完成您的年度目标设定'
                                          : `请完成您的${
                                              planLocales.performancePeriod.enum[
                                                selectedPerformance.period
                                              ]
                                            }绩效考核自评`}
                                      </Typography.Text>
                                      <Typography.Text type="secondary">
                                        截止日期：
                                        {moment(
                                          selectedPerformance.subType === 'TARGET'
                                            ? selectedPerformance.objectiveExpiredAt
                                            : selectedPerformance.evaluationExpiredAt
                                        ).format('YYYY-MM-DD')}
                                      </Typography.Text>
                                    </Space>
                                    {selectedPerformance.subType === 'EVAL' &&
                                      selectedPerformance.haveTarget && (
                                        <ChangePerformanceTargetLink
                                          selectedPerformance={selectedPerformance}
                                        />
                                      )}
                                  </Space>
                                </Space>
                                <Space size="large">
                                  <PerformanceStatusTag
                                    pfType="REGULAR"
                                    status={selectedPerformance.status}
                                  />
                                  <div style={{ width: '100px', textAlign: 'right' }}>
                                    <PerformanceLink
                                      position={userPerformancePosition}
                                      userId={userId!}
                                      performance={selectedPerformance}
                                    />
                                  </div>
                                </Space>
                              </Space>
                            </Card>
                          )}
                        </>
                      ) : (
                        <Empty
                          description="当前年度暂无考核计划"
                          style={{
                            margin: 'auto',
                            position: 'absolute',
                            top: '50%',
                            left: '50%',
                            transform: 'translate(-50%, -50%)',
                          }}
                        />
                      )}
                    </>
                  )}
                </Space>
              )}
            </Space>
            {changeTarget && <ChangeTargetModalView performance={changeTarget} />}
          </Space>
        </Card>
      </Col>
      <Col style={{ height: '100%', overflowY: 'auto' }} span={6}>
        <Space
          style={{ width: '100%', height: '100%', display: 'flex' }}
          className={styles.rightSider}
          size="middle"
          direction="vertical"
        >
          <UserInfoCard
            key="userInfos"
            style={{ width: '100%' }}
            userId={userId!}
            editable={false}
            needValid={false}
          />
          <UserGroupsCard
            key="userGroups"
            style={{ height: '100%', width: '100%' }}
            userId={userId!}
          />
        </Space>
      </Col>
    </Row>
  );
}

type StepItem = {
  title: string;
  description: string;
  status: StepsProps['status'];
  evaluationStatus?: BackendPerformanceEvaluationStatus;
};

function generateSteps({
  data,
  locales,
}: {
  data: PerformanceJSON[];
  locales: AnnualPerformancePlanLocales;
}): StepItem[] {
  return data.map(pf => {
    let status: StepsProps['status'] = 'wait';
    if (pf.subType === 'TARGET') {
      if (pf.objectiveStatus === 'TARGET_FINISHED') {
        status = 'finish';
      } else {
        status = 'process';
      }
    } else {
      if (pf.evaluationStatus === 'EVAL_FINISHED') {
        status = 'finish';
      } else if (pf.evaluationStatus === 'EVAL_INIT') {
        status = 'wait';
      } else {
        status = 'process';
      }
    }
    const format = pf.subType === 'TARGET' ? 'MM.DD' : 'YY.MM.DD';
    return {
      title:
        pf.subType === 'TARGET'
          ? '确认年度目标'
          : `${locales.performancePeriod.enum[pf.period]}考核`,
      status,
      evaluationStatus: pf.subType === 'EVAL' ? pf.evaluationStatus : null,
      description: `${
        pf.subType === 'TARGET'
          ? `目标确认期限：${moment(pf.objectiveStartedAt).format(format)}~${moment(
              pf.objectiveExpiredAt
            ).format(format)}`
          : `自评起止日期：${moment(pf.evaluationStartedAt).format(format)}~${moment(
              pf.evaluationExpiredAt
            ).format(format)}`
      }`,
    };
  });
}
