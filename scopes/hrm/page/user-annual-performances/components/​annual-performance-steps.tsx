import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Steps } from '@manyun/base-ui.ui.steps';
import type { StepsProps } from '@manyun/base-ui.ui.steps';

import styles from './​annual-performance-steps.module.less';

export type AnnualPerformanceStepsProps = StepsProps;

export const AnnualPerformanceSteps = ({
  items,
  current,
  onChange,
}: AnnualPerformanceStepsProps) => {
  return (
    <Card bodyStyle={{ overflowX: 'auto' }} className={styles.stepsCard}>
      <Steps
        type="navigation"
        current={current}
        items={items?.map(item => ({
          status: item.status,
          title: item.title,
          description: <div style={{ whiteSpace: 'pre-line' }}>{item.description}</div>,
        }))}
        onChange={onChange}
      />
    </Card>
  );
};
