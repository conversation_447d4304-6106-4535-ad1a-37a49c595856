@import (reference) '@manyun/base-ui.theme.theme/dist/theme.less';

.stepsCard {
  @media screen and  (max-width: 1600px) {
    :global {
      .@{prefixCls}-steps-horizontal:not(.@{prefixCls}-steps-label-vertical)
        .@{prefixCls}-steps-item {
        padding-left: @padding-xs;
        padding-right: @padding-xs;
      }
      .@{prefixCls}-steps-navigation .@{prefixCls}-steps-item-container{
        margin-left:@margin-xs;
      }
    }
  }
  @media screen and  (min-width: 1600px) {
    :global{
      .@{prefixCls}-steps-navigation .@{prefixCls}-steps-item-container{
        margin-left: 0;
      }
    }
  }
  
}
