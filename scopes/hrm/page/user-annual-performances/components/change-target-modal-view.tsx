import moment from 'moment';
import React, { useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';

import { selectMe } from '@manyun/auth-hub.state.user';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import type { FormItemProps } from '@manyun/base-ui.ui.form';
import { Form } from '@manyun/base-ui.ui.form';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { qs } from '@manyun/base-ui.util.query-string';
import { useLazyValidAnnualPerformanceTargetChange } from '@manyun/hrm.gql.client.hrm';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import { generateAnnualPerformanceObjectivesDetail } from '@manyun/hrm.route.hrm-routes';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { generateChangeTargetValidityOptions } from '@manyun/hrm.util.performances';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

export type ChangeTargetModalViewProps = {
  performance: PerformanceJSON;
};
export const ChangeTargetModalView = ({ performance }: ChangeTargetModalViewProps) => {
  const history = useHistory();
  const { userId } = useSelector(selectMe, (left, right) => left.userId === right.userId);
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const period = Form.useWatch('period', form);
  const [validAnnualPerformanceTargetChange, { data }] =
    useLazyValidAnnualPerformanceTargetChange();

  useEffect(() => {
    if (period) {
      validAnnualPerformanceTargetChange({
        variables: {
          year: moment().get('year').toString(),
          changeType: period,
        },
      });
    }
  }, [period, validAnnualPerformanceTargetChange]);

  const validateInfo = useMemo(() => {
    if (period && data?.validAnnualPerformanceTargetChange.message) {
      return {
        validateStatus: 'error',
        help: (
          <div style={{ marginLeft: '55px' }}>
            {data.validAnnualPerformanceTargetChange.message}
          </div>
        ),
      } as Pick<FormItemProps, 'help' | 'validateStatus'>;
    }
    return undefined;
  }, [data?.validAnnualPerformanceTargetChange.message, period]);

  return (
    <>
      <Typography.Text type="secondary">
        温馨提示：如您的机房或岗位发生变更，点击
        <Button
          type="link"
          compact
          onClick={() => {
            setOpen(true);
          }}
        >
          变更目标
        </Button>
      </Typography.Text>
      <Modal
        width={728}
        title="变更目标"
        open={open}
        okText="下一步"
        onCancel={() => {
          setOpen(false);
        }}
        onOk={() => {
          form
            .validateFields()
            .then(values => {
              if (data?.validAnnualPerformanceTargetChange.message) {
                return;
              }
              history.push(
                qs.stringifyUrl({
                  url: generateAnnualPerformanceObjectivesDetail('user', {
                    user: userId!.toString(),
                    planid: performance.plan!.id!.toString(),
                    id: performance.id!.toString(),
                    key: performance.rowKey!,
                    position: values.position,
                  }),
                  query: {
                    idc: values.idc[0],
                    position: values.position,
                    period: values.period,
                  },
                })
              );
            })
            .catch(console.error);
        }}
      >
        <Space direction="vertical" size="middle">
          <Alert
            type="warning"
            showIcon
            message="变更目标提醒"
            description={
              <div>
                <div>
                  1.如您的机房或岗位发生变更，请在下方选择最新的机房、岗位，重新提交目标确认；
                </div>
                <div>
                  2.审批通过后，将从所选考核周期（含所选的考核周期）开始，按照新机房岗位的目标进行考核。
                </div>
              </div>
            }
          />
          <Form form={form}>
            <Form.Item name="idc" label="机房" rules={[{ required: true, message: '机房必选' }]}>
              <LocationCascader
                style={{ width: 334 }}
                nodeTypes={['IDC']}
                authorizedOnly
                allowClear={false}
                onTreeDataChange={data => {
                  if (data.length === 1) {
                    form.setFieldsValue({ idc: [data[0].value] });
                  }
                }}
              />
            </Form.Item>
            <Form.Item
              name="position"
              label="岗位"
              rules={[{ required: true, message: '岗位必选' }]}
            >
              <PerformancePositionSelect style={{ width: 334 }} allowClear={false} />
            </Form.Item>

            <Form.Item style={{ marginBottom: 0 }} {...validateInfo}>
              <Space style={{ alignItems: 'center' }}>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  label="考核"
                  name="period"
                  rules={[{ required: true, message: '考核必选' }]}
                  wrapperCol={{ span: 10 }}
                  validateStatus={validateInfo?.validateStatus}
                >
                  <Select
                    style={{ width: 128 }}
                    options={generateChangeTargetValidityOptions()}
                    allowClear={false}
                  />
                </Form.Item>
                <span> 按照新机房岗位的目标进行考核</span>
              </Space>
            </Form.Item>
          </Form>
        </Space>
      </Modal>
    </>
  );
};
