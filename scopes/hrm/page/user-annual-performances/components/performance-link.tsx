import React, { useMemo } from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';

import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import {
  generateAnnualPerformanceDetail,
  generateAnnualPerformanceObjectivesDetail,
} from '@manyun/hrm.route.hrm-routes';

export type PerformanceLinkProps = {
  position: string | null;
  userId: number;
  performance: PerformanceJSON;
};

export const PerformanceLink = ({ userId, performance, position }: PerformanceLinkProps) => {
  const history = useHistory();

  const linkInfos = useMemo(() => {
    const to: string =
      performance.subType === 'TARGET'
        ? generateAnnualPerformanceObjectivesDetail('user', {
            user: userId.toString(),
            planid: performance.plan!.id!.toString()!,
            id: performance.id ? performance.id.toString() : 'new',
            key: performance.rowKey,
            position: position ?? performance.evaluationJob?.value!,
          })
        : performance.id
        ? generateAnnualPerformanceDetail('user', {
            year: performance.year!,
            position: performance.evaluationJob?.value!,
            user: userId.toString(),
            period: performance.period,
            id: performance.id!.toString(),
          })
        : '';

    let text: string | undefined;
    switch (performance.status) {
      case 'TARGET_REFUSE':
      case 'TARGET_WAIT_SETTING':
      case 'EVAL_SELF_CONFIRM':
        text = '去完成';
        break;
      case 'TARGET_WAIT_APPROVING':
      case 'TARGET_PASS':
        text = '查看';
        break;
      case 'EVAL_WAIT_SETTING':
      case 'EVAL_REFUSE':
        text = '去完成';
        break;
      case 'EVAL_WAIT_APPROVING':
      case 'EVAL_PASS':
        text = '查看';
        break;
      case 'EVAL_INIT':
      case 'TARGET_INIT':
        text = '--';
        break;
      default:
        break;
    }
    return {
      to,
      text,
    };
  }, [
    performance.evaluationJob?.value,
    performance.id,
    performance.period,
    performance.plan,
    performance.rowKey,
    performance.status,
    performance.subType,
    performance.year,
    position,
    userId,
  ]);

  if (linkInfos.text === '--') {
    return <>--</>;
  }

  return (
    <Button
      type="link"
      compact
      onClick={() => {
        history.push(linkInfos.to);
      }}
    >
      {linkInfos?.text ?? '查看'}
    </Button>
  );
};
