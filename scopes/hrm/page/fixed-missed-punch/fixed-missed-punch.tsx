import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { Link, useParams } from 'react-router-dom';

import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { OperationLogTable } from '@manyun/auth-hub.ui.operation-log-table';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { McUpload } from '@manyun/dc-brain.ui.upload';
import { getClockInLocales } from '@manyun/hrm.model.clock-in';
import { ClockInJSON } from '@manyun/hrm.model.clock-in';
import { fetchFixedMissedPunch } from '@manyun/hrm.service.fetch-fixed-missed-punch';
import { RevokeAlterProcess } from '@manyun/hrm.ui.revoke-alter-process';

export function FixedMissedPunch() {
  const { id } = useParams<{ id: string }>();
  const locales = useMemo(() => getClockInLocales(), []);

  const [state, setState] = useState<{
    loading: boolean;
    data: ClockInJSON | null;
    files: McUploadFile[];
  }>({
    loading: false,
    data: null,
    files: [],
  });

  const loadData = useCallback(async () => {
    setState(pre => ({ ...pre, loading: true }));
    const { error, data } = await fetchFixedMissedPunch({ bizId: id });
    setState(pre => ({ ...pre, loading: false }));
    if (error) {
      message.error(error.message);
      return;
    }

    if (data) {
      setState(pre => ({ ...pre, data }));
    }
  }, [id]);

  useEffect(() => {
    loadData();
  }, [loadData]);

  const descriptionItems = useMemo(() => {
    return [
      {
        label: locales.missedPunchesFix.bizId ?? '补卡ID',
        value: state.data?.bizId,
      },
      {
        label: locales.missedPunchesFix.title._self ?? '标题',
        value: state.data?.title,
      },
      {
        label: locales.applyBy._self ?? '申请人',
        value: (
          <UserLink id={state.data?.applyUser?.id as number} name={state.data?.applyUser?.name} />
        ),
      },
      {
        label: locales.missedPunchesFix.approveStatus ?? '状态',
        value: state.data?.approveStatus ? (
          <ApprovalStatusText approveStatus={state.data?.approveStatus} />
        ) : (
          '--'
        ),
      },
      {
        label: locales.missedPunchesFix.approveId ?? '对应审批单号',
        value: state.data?.approveId ? (
          /**@TODO 是否考虑重构ApproveLink */
          <Link to={generateBPMRoutePath({ id: state.data?.approveId })}>
            {state.data?.approveId}
          </Link>
        ) : (
          '--'
        ),
      },
      {
        label: locales.location ?? '位置',
        value: state.data?.location ?? '--',
      },
      {
        label: locales.missedPunchesFix.punchTime._self ?? '补卡时间',
        value: state.data?.punchTime,
      },
      {
        label: locales.missedPunchesFix.reason._self ?? '补卡原因',
        value: state.data?.reason ?? '--',
      },
      {
        label: locales.missedPunchesFix.files ?? '附件',
        value: <McUpload targetId={id} targetType="SUPPLY_CHECK" allowDelete={false} disabled />,
      },
    ] as {
      label: string;
      value: React.ReactNode;
    }[];
  }, [
    id,
    locales.applyBy._self,
    locales.location,
    locales.missedPunchesFix.approveId,
    locales.missedPunchesFix.approveStatus,
    locales.missedPunchesFix.bizId,
    locales.missedPunchesFix.files,
    locales.missedPunchesFix.punchTime._self,
    locales.missedPunchesFix.reason._self,
    locales.missedPunchesFix.title._self,
    state.data?.applyUser?.id,
    state.data?.applyUser?.name,
    state.data?.approveId,
    state.data?.approveStatus,
    state.data?.bizId,
    state.data?.location,
    state.data?.punchTime,
    state.data?.reason,
    state.data?.title,
  ]);

  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      <Card title={locales.missedPunchesFix.basicInfo ?? '基本信息'} loading={state.loading}>
        <Descriptions column={4}>
          {descriptionItems.map(item => (
            <Descriptions.Item label={item.label} key={item.label}>
              {item.value}
            </Descriptions.Item>
          ))}
        </Descriptions>
      </Card>
      <Card title="操作记录">
        <OperationLogTable
          defaultSearchParams={{
            targetId: id,
            targetType: 'SUPPLY',
          }}
          showColumns={['serialNumber', 'modifyType']}
          isTargetIdEqual={(targetId: string) => {
            return targetId === id;
          }}
        />
      </Card>
      {state.data?.approveStatus === 'APPROVING' && (
        <RevokeAlterProcess
          type="primary"
          footer={true}
          loading={state.loading}
          bizId={id}
          bizType="SUPPLY"
          recordUser={{ applyId: state.data?.applyUser?.id! }}
          onOk={() => {
            loadData();
          }}
        />
      )}
    </Space>
  );
}
