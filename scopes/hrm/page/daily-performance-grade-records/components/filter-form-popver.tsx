import { FilterOutlined } from '@ant-design/icons';
import chunk from 'lodash.chunk';
import flatten from 'lodash.flatten';
import type { Moment } from 'moment';
import moment from 'moment';
import React, { useMemo, useState } from 'react';

import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormItemProps } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import type {
  AnnualPerformanceObjectiveLocales,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import { getDailyPerformanceGradeLocales } from '@manyun/hrm.model.daily-performance-grade';
import { PerformancePositionSelect } from '@manyun/hrm.ui.performance-position';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import type { Field } from '../daily-performance-grade-records';

type FormSearchValues = Omit<
  Field,
  | 'name'
  | 'page'
  | 'pageSize'
  | 'orderField'
  | 'orderType'
  | 'createTimeRange'
  | 'modifiedTimeRange'
  | 'blockGuids'
> & {
  blockGuids?: string[][];
  createTimeRange: [moment.Moment, moment.Moment];
  modifiedTimeRange: [moment.Moment, moment.Moment];
};

export type SearchValues = Omit<
  FormSearchValues,
  'createTimeRange' | 'modifiedTimeRange' | 'blockGuids'
> & {
  blockGuids: string[];
  createTimeRange?: [number, number];
  modifiedTimeRange?: [number, number];
};

export type FilterFormPopoverProps = {
  typeTextVersion: keyof AnnualPerformanceObjectiveLocales['type']['textMapper'];
  values: SearchValues;
  onSearch: (params: SearchValues) => void;
};

type FilterFormValue = Omit<
  FormSearchValues,
  'createTimeRange' | 'modifiedTimeRange' | 'regionCodes' | 'idcTags'
> & {
  createTimeRange?: [Moment, Moment];
  modifiedTimeRange?: [Moment, Moment];
  regionCodes?: string[][];
  idcTags?: string[][];
};

type ItemFieldName = keyof FilterFormValue;

type FormItem = {
  label: React.ReactNode;
  name: ItemFieldName;
  colSpan: number;
  children: JSX.Element;
} & FormItemProps;

export function FilterFormPopover({ typeTextVersion, values, onSearch }: FilterFormPopoverProps) {
  const [form] = Form.useForm<FilterFormValue>();
  const [visible, setVisible] = useState(false);

  const hasFilter = useDeepCompareMemo(() => {
    return (
      values &&
      Object.values(values).some(value =>
        Array.isArray(value) ? value.length !== 0 : value !== undefined
      )
    );
  }, [values]);

  const locales = useMemo(() => getDailyPerformanceGradeLocales(), []);
  const objectiveLocales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);

  const formItemProps = useMemo(() => {
    return [
      {
        label: locales.gradedUser.idc,
        name: 'idcTags',
        colSpan: 24,
        children: (
          <LocationCascader
            authorizedOnly
            showSearch
            maxTagCount="responsive"
            nodeTypes={['IDC']}
            multiple
            allowClear
          />
        ),
      },
      {
        label: locales.gradedUser.block,
        name: 'blockGuids',
        colSpan: 24,
        children: (
          <LocationCascader
            authorizedOnly
            showSearch
            maxTagCount="responsive"
            nodeTypes={['IDC', 'BLOCK']}
            multiple
            showCheckedStrategy="SHOW_CHILD"
            allowClear
          />
        ),
      },
      {
        label: locales.gradedUser.region,
        name: 'regionCodes',
        colSpan: 12,
        children: (
          <LocationCascader
            showSearch
            authorizedOnly
            maxTagCount="responsive"
            nodeTypes={['REGION']}
            multiple
            allowClear
          />
        ),
      },
      {
        label: locales.position,
        name: 'position',
        colSpan: 12,
        children: <PerformancePositionSelect allowClear showDelete />,
      },

      {
        label: locales.type,
        name: 'type',
        colSpan: 12,
        children: (
          <Select
            options={Object.keys(objectiveLocales.type.textMapper[typeTextVersion])
              .filter(key => key !== 'RED_LINE')
              .map(key => ({
                label:
                  objectiveLocales.type.textMapper[typeTextVersion][
                    key as BackendAnnualPerformanceObjectiveType
                  ],
                value: key,
              }))}
            allowClear
          />
        ),
      },
      {
        label: locales.name,
        name: 'name',
        colSpan: 12,
        children: <Input allowClear />,
      },
      {
        label: locales.instStatus.__self,
        name: 'instStatusList',
        colSpan: 12,
        children: (
          <Select
            mode="multiple"
            options={Object.keys(locales.instStatus.enum).map(key => ({
              label: locales.instStatus.enum[key as keyof typeof locales.instStatus.enum],
              value: key,
            }))}
            allowClear
          />
        ),
      },
      {
        label: locales.gradedUser.superiors,
        name: 'superiorId',
        colSpan: 12,
        children: <UserSelect labelInValue={false} allowClear />,
      },
      {
        label: locales.createdBy.__self,
        name: 'createUserId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: locales.createdAt,
        name: 'createTimeRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
      {
        label: locales.modifiedBy.__self,
        name: 'modifiedUserId',
        colSpan: 12,
        children: <UserSelect allowClear labelInValue={false} />,
      },
      {
        label: locales.modifiedAt,
        name: 'modifiedTimeRange',
        colSpan: 12,
        children: <DatePicker.RangePicker style={{ width: '100%' }} allowClear />,
      },
    ] as FormItem[];
  }, [locales, objectiveLocales.type.textMapper, typeTextVersion]);

  const onHandleSearch = () => {
    const formValue = form.getFieldsValue();
    const _values: Partial<Field> = {
      ...formValue,
      regionCodes: formValue.regionCodes ? flatten(formValue.regionCodes) : undefined,
      idcTags: formValue.idcTags ? flatten(formValue.idcTags) : undefined,
      blockGuids: formValue.blockGuids
        ? formValue.blockGuids.filter(item => item.length === 2).map(item => item[1])
        : undefined,
      createTimeRange:
        formValue.createTimeRange && formValue.createTimeRange.length === 2
          ? [
              formValue.createTimeRange[0].startOf('day').valueOf(),
              formValue.createTimeRange[1].endOf('day').valueOf(),
            ]
          : undefined,
      modifiedTimeRange:
        formValue.modifiedTimeRange && formValue.modifiedTimeRange.length === 2
          ? [
              formValue.modifiedTimeRange[0].startOf('day').valueOf(),
              formValue.modifiedTimeRange[1].endOf('day').valueOf(),
            ]
          : undefined,
    };
    onSearch(_values as SearchValues);
  };

  return (
    <Dropdown
      open={visible}
      dropdownRender={() => (
        <Card style={{ width: 544 }}>
          <Form form={form} layout="vertical">
            <Row gutter={16}>
              {formItemProps.map(itemProps => {
                const { label, name, colSpan, children, ...rest } = itemProps;
                return (
                  <Col key={`${name}`} span={colSpan}>
                    <Form.Item label={label} name={name} {...rest}>
                      {children}
                    </Form.Item>
                  </Col>
                );
              })}
            </Row>
            <Row justify="end">
              <Space>
                <Button
                  onClick={() => {
                    form.resetFields();
                    setVisible(false);
                    onHandleSearch();
                  }}
                >
                  重置
                </Button>
                <Button
                  type="primary"
                  onClick={() => {
                    onHandleSearch();
                    setVisible(false);
                  }}
                >
                  搜索
                </Button>
              </Space>
            </Row>
          </Form>
        </Card>
      )}
      placement="bottomRight"
      trigger={['click']}
      arrow={{ pointAtCenter: true }}
      onOpenChange={_visible => {
        if (_visible === true && values) {
          form.setFieldsValue({
            ...values,
            blockGuids: Array.isArray(values.blockGuids)
              ? values.blockGuids
                  .map(item => item.split('.'))
                  .reduce((acc, cur) => {
                    if (cur.length === 2) {
                      cur[1] = cur.join('.');
                    }
                    return [...acc, cur];
                  }, [] as string[][])
              : undefined,
            regionCodes: values.regionCodes ? chunk(values.regionCodes) : undefined,
            idcTags: values.idcTags ? chunk(values.idcTags) : undefined,
            createTimeRange:
              values.createTimeRange && values.createTimeRange.length === 2
                ? [moment(values.createTimeRange[0]), moment(values.createTimeRange[1])]
                : undefined,
            modifiedTimeRange:
              values.modifiedTimeRange && values.modifiedTimeRange.length === 2
                ? [moment(values.modifiedTimeRange[0]), moment(values.modifiedTimeRange[1])]
                : undefined,
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
          } as any);
        } else {
          onHandleSearch();
        }
        setVisible(_visible);
      }}
    >
      <Tooltip title="筛选">
        <Button
          size="small"
          icon={<FilterOutlined />}
          ghost={hasFilter}
          type={hasFilter ? 'primary' : 'default'}
        />
      </Tooltip>
    </Dropdown>
  );
}
