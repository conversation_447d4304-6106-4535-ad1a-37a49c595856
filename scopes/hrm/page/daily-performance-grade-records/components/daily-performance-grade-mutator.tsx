import React from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import {
  PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH,
  generatePerformanceDailyGradeEdit,
} from '@manyun/hrm.route.hrm-routes';

export type DailyPerformanceGradeMutatorButtonProps = {
  id?: number;
} & Omit<ButtonProps, 'onClick' | 'id'>;

export const DailyPerformanceGradeMutatorButton = ({
  id,
  ...restProps
}: DailyPerformanceGradeMutatorButtonProps) => {
  const history = useHistory();
  const [authorized] = useAuthorized({
    checkByCode: 'element_hrm-daily-performance-grade-mutator',
  });

  if (!authorized) {
    return null;
  }

  return (
    <Button
      {...restProps}
      onClick={() => {
        history.push(
          id
            ? generatePerformanceDailyGradeEdit({ id: id.toString() })
            : PERFORMANCE_DAILY_GRADE_CREATE_ROUTE_PATH
        );
      }}
    >
      {id ? '修改' : '添加评分记录'}
    </Button>
  );
};
