import dayjs from 'dayjs';
import moment from 'moment';
import React from 'react';
import { Link } from 'react-router-dom';

import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { Button } from '@manyun/base-ui.ui.button';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import { SimpleFileList } from '@manyun/base-ui.ui.file-list';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import type { DailyPerformanceGradeJSON } from '@manyun/hrm.model.daily-performance-grade';
import { getDailyPerformanceGradeLocales } from '@manyun/hrm.model.daily-performance-grade';
import { PERFORMANCE_SECOND_VERSION, getPerformanceVersion } from '@manyun/hrm.util.performances';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

import { ApproveStatusTag } from './approve-status-tag';

export type DailyPerformanceGradeDetailProps = {
  record: DailyPerformanceGradeJSON;
};
export const DailyPerformanceGradeDetail = ({ record }: DailyPerformanceGradeDetailProps) => {
  const [open, setOpen] = React.useState(false);
  const loading = false;
  const locales = React.useMemo(() => getDailyPerformanceGradeLocales(), []);
  const objectiveLocales = React.useMemo(() => getAnnualPerformanceObjectiveLocales(), []);

  const typeTypeVersion = React.useMemo(() => {
    const year = moment(record.occurTime).year();
    return getPerformanceVersion(year);
  }, [record.occurTime]);

  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
        }}
      >
        查看
      </Button>
      <Drawer
        width={612}
        open={open}
        title="查看详情"
        onClose={() => {
          setOpen(false);
        }}
      >
        {loading ? (
          <Skeleton />
        ) : (
          <Space style={{ width: '100%' }} direction="vertical" size="large">
            <UserInfoCard
              userId={record.user.id}
              showTitle={false}
              showDivider={false}
              needValid={false}
              extra={
                <Space>
                  考评分：
                  <Tag color={record.grade >= 0 ? 'success' : 'error'}>
                    {record.grade >= 0 ? '加' : '减'}
                    {Math.abs(record.grade)}分
                  </Tag>
                </Space>
              }
            />
            <Descriptions column={1}>
              {[
                {
                  key: 'instStatus',
                  label: locales.instStatus.__self,
                  value: (
                    <Space align="center">
                      {typeTypeVersion === PERFORMANCE_SECOND_VERSION ? (
                        <ApproveStatusTag
                          status={record.instStatus ?? locales.instStatus.enum.PASS}
                        />
                      ) : (
                        <ApproveStatusTag status={record.instStatus ?? '--'} />
                      )}
                      {record.instId && (
                        <Link target="_blank" to={generateBPMRoutePath({ id: record.instId })}>
                          审批ID：{record.instId}
                        </Link>
                      )}
                    </Space>
                  ),
                },
                {
                  key: 'idc',
                  label: locales.gradedUser.idc,
                  value: record.user.idc?.value ? <SpaceText guid={record.user.idc.value} /> : '--',
                },
                {
                  key: 'block',
                  label: locales.gradedUser.block,
                  value: record.user.block?.label ?? '--',
                },
                {
                  key: 'position',
                  label: locales.position,
                  value: record.performancePosition?.label,
                },
                {
                  key: 'date',
                  label: locales.occurTime,
                  value: dayjs(record.occurTime).format('YYYY-MM-DD'),
                },
                {
                  key: 'type',
                  label: locales.type,
                  value: objectiveLocales.type.textMapper[typeTypeVersion][record.type],
                },
                { key: 'name', label: locales.name, value: record.name },
                { key: 'measurements', label: locales.measurements, value: record.measurements },
                {
                  key: 'list',
                  label: '评分标准',
                  value: (
                    <Space style={{ width: '100%' }} direction="vertical">
                      {record.gradeCriteria.map(item => (
                        <Space key={item.id} align="start">
                          <div style={{ minWidth: 64 }}>
                            <Tag
                              style={{ marginRight: 0 }}
                              color={item.defaultGrade >= 0 ? 'success' : 'error'}
                            >
                              {item.defaultGrade >= 0 ? '加' : '减'}
                              {Math.abs(item.defaultGrade)}分
                            </Tag>
                          </div>
                          <Typography.Text style={{ whiteSpace: 'pre-line' }}>
                            {item.criteria}
                          </Typography.Text>
                        </Space>
                      ))}
                      {record.gradeCriteria.length === 0 && '--'}
                    </Space>
                  ),
                },
                { key: 'gradeDesc', label: locales.gradeDesc, value: record.gradeDesc },
                {
                  key: 'attachments',
                  label: '附件',
                  value:
                    (record.attachments ?? []).length > 0 ? (
                      <SimpleFileList
                        files={record.attachments ?? []}
                        children={
                          <Button type="link" compact>
                            查看
                          </Button>
                        }
                      />
                    ) : (
                      '--'
                    ),
                },
              ].map(item => (
                <Descriptions.Item key={item.key} label={item.label}>
                  {item.value}
                </Descriptions.Item>
              ))}
            </Descriptions>
          </Space>
        )}
      </Drawer>
    </>
  );
};
