import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import { getDailyPerformanceGradeLocales } from '@manyun/hrm.model.daily-performance-grade';

export type ApproveStatusTagProps = {
  status: string;
};
export const ApproveStatusTag = ({ status }: ApproveStatusTagProps) => {
  const locales = React.useMemo(() => getDailyPerformanceGradeLocales(), []);
  const color = React.useMemo(() => {
    switch (status) {
      case 'APPROVING':
        return 'blue';
      case 'PASS':
        return 'green';
      case 'UN_PASS':
        return 'red';
    }
  }, [status]);

  if (locales.instStatus.enum[status as keyof typeof locales.instStatus.enum]) {
    return (
      <Badge
        color={color}
        text={locales.instStatus.enum[status as keyof typeof locales.instStatus.enum]}
      />
    );
  }
  return status;
};
