import styled, { css } from '@manyun/dc-brain.theme.theme';

export const SpaceWarper = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      > .${prefixCls}-space {
        > .${prefixCls}-space-item:first-child {
          flex: 1;
        }
      }
    `;
  }}
`;

export const SpaceContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      > .${prefixCls}-space {
        > .${prefixCls}-space-item {
          width: 100%;
        }
      }
    `;
  }}
`;

export const ErrorStatusContainer = styled.div`
  ${props => {
    const { prefixCls } = props.theme;
    return css`
      .customerErrorItem {
        .${prefixCls}-form-item-explain-error {
          width: 135px;
        }
      }
    `;
  }}
`;
