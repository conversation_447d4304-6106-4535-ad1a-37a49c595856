import { gql, useQuery } from '@apollo/client';
import type { QueryHookOptions, QueryResult } from '@apollo/client';

import type {
  BackendPerformanceSubType,
  BackendPerformanceType,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';

type PerformanceResolverArgs = {
  type: BackendPerformanceType;
  subType: BackendPerformanceSubType;
  id: number;
};

export type PerformanceQueryResultData = {
  performance: PerformanceJSON | null;
};

export function usePerformance(
  options?: QueryHookOptions<PerformanceQueryResultData, PerformanceResolverArgs>
): QueryResult<PerformanceQueryResultData, PerformanceResolverArgs> {
  return useQuery<PerformanceQueryResultData, PerformanceResolverArgs>(
    gql`
      # fragment MyGradeSummary on GradeSummary {
      #   BIZ
      #   DEVELOP
      #   grade
      # }

      query GetPerformance($type: String!, $subType: String!, $id: Int!) {
        performance(type: $type, subType: $subType, id: $id) {
          rowKey
          id
          period
          year
          type
          subType
          user {
            id
            name
            idc
          }
          plan {
            id
            splitRule
          }
          evaluationJob {
            label
            value
          }
          evaluations {
            id
            type
            status
            users {
              user {
                id
                name
              }
              comments {
                summary
                improve
                infoSummary
                infoImprove
                reason
              }
              status
              result
              evaluatedAt
              isEvaluated
              interviewed
              evaluationRequired
              needAttention
              attentionContent
            }
            isCurrentStep
          }
          currentHandlers {
            id
            name
          }
          objectiveStatus
          evaluationStatus
          startedAt
          isLoggedInUserInCurrentStepUsers
          allowEvaluationStartedAt
          objectives {
            id
            type
            title
            measurements
            grade
            gradeDescriptions
          }
          grade
          gradesSummaries {
            type
            Q1
            Q2
            Q3
            Q4
            FIR_HALF_YEAR
            SEC_HALF_YEAR
            YEAR
          }
          gradesSummariesRecords {
            type
            Q1
            Q2
            Q3
            Q4
            FIR_HALF_YEAR
            SEC_HALF_YEAR
            YEAR
          }
        }
      }
    `,
    {
      ...options,
      fetchPolicy: 'network-only',
    }
  );
}
