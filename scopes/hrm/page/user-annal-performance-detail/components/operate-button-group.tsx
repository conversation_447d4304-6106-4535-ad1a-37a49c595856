import React, { useMemo } from 'react';

import type { FormInstance } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { scrollToField } from '@manyun/dc-brain.util.scroll-to-field';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import {
  AgreePerformanceButton,
  RefusePerformanceButton,
  SavePerformanceButton, // TransformPerformanceButton,
} from '@manyun/hrm.ui.performance-approve-button';
import type { ApproveStatus } from '@manyun/hrm.ui.performance-approve-button';

export type ApproveButtonGroupProps = {
  performance: PerformanceJSON;
  form: FormInstance | null;
  onSuccess?: () => void;
};

/**
 * 主管审批按钮组
 */
export const ManagerApproveButtonGroup = ({
  performance,
  form,
  onSuccess,
}: ApproveButtonGroupProps) => {
  const isLineManagerApprove = useMemo(
    () => performance.evaluationStatus === 'EVAL_WAIT_SUPERIOR',
    [performance.evaluationStatus]
  );
  const onValidate = async (warningOnly: boolean) => {
    try {
      const values = warningOnly ? form!.getFieldsValue() : await form!.validateFields();

      return await Promise.resolve({
        content: {
          summary: values.lineManagerSummary,
          improve: values.lineManagerImprove,
        },
        interviewed: values.lineManagerInvited,
        result: values.lineManagerResult,
        needAttention: !!values.needAttention,
        attentionContent: values.attentionContent,
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      scrollToField(form!.scrollToField, err.errorFields);
      return Promise.reject(err);
    }
  };

  return (
    <Space size="middle">
      <AgreePerformanceButton
        type="primary"
        bizId={performance.id!}
        text={isLineManagerApprove ? '提交评估' : '同意'}
        validExistingApprovingDailyGrade={isLineManagerApprove}
        existingApprovingDailyGradeWarningOnly={isLineManagerApprove}
        currentStep={performance.evaluationStatus as ApproveStatus}
        onValidate={isLineManagerApprove ? () => onValidate(false) : undefined}
        onSuccess={() => {
          if (isLineManagerApprove) {
            message.success('提交成功');
          } else {
            message.success(`您已通过${performance.user.name}的个人年度绩效`);
          }
          onSuccess?.();
        }}
      />
      {isLineManagerApprove && (
        <SavePerformanceButton
          bizId={performance.id!}
          currentStep={performance.evaluationStatus as ApproveStatus}
          onValidate={() => onValidate(true)}
          onSuccess={() => {
            message.success('保存草稿成功');
          }}
        />
      )}

      <RefusePerformanceButton
        bizId={performance.id!}
        currentStep={performance.evaluationStatus as ApproveStatus}
        onSuccess={() => {
          message.success(`您已退回${performance.user.name}的个人年度绩效`);
          onSuccess?.();
        }}
      />
      {/* <TransformPerformanceButton
        bizId={performance.id!}
        pfSubType="EVAL"
        currentStep={performance.evaluationStatus as ApproveStatus}
        onSuccess={() => {
          message.success('转交成功');
          onSuccess?.();
        }}
      /> */}
    </Space>
  );
};

export type SelfEvaluateButtonGroupProps = {
  performance: PerformanceJSON;
  showSave: boolean;
  form: FormInstance | null;
  onSuccess?: () => void;
};
/**员工自评按钮组 */
export const SelfEvaluateButtonGroup = ({
  performance,
  form,
  showSave,
  onSuccess,
}: SelfEvaluateButtonGroupProps) => {
  const onValidate = async (warningOnly: boolean) => {
    try {
      const values = warningOnly ? form!.getFieldsValue() : await form!.validateFields();
      return await Promise.resolve({
        content: {
          summary: values.employeeSummary,
          improve: values.employeeImprove,
          infoSummary: values.employeeSelfSummary,
          infoImprove: values.employeeSelfImprove,
        },
      });
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (err: any) {
      scrollToField(form!.scrollToField, err.errorFields);
      return Promise.reject(err);
    }
  };

  return (
    <Space size="middle">
      <AgreePerformanceButton
        type="primary"
        bizId={performance.id!}
        text="提交审批"
        currentStep="SELF_EVAL"
        onValidate={() => onValidate(false)}
        onSuccess={() => {
          message.success('提交成功，请等待审批');
          onSuccess?.();
        }}
      />
      {showSave && (
        <SavePerformanceButton
          bizId={performance.id!}
          currentStep="SELF_EVAL"
          onValidate={() => onValidate(true)}
          onSuccess={() => {
            message.success('保存草稿成功');
            onSuccess?.();
          }}
        />
      )}
    </Space>
  );
};
