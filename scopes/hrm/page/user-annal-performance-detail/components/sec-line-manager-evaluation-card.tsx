import React from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { PerformanceJSON } from '@manyun/hrm.model.performance';

export type SecLineManagerEvaluationCardProps = {
  /**
   * 当前登录人id
   */
  loginUserId: number;
  /**
   * 当二线同意 需对经理展示本人的评价
   */
  showCurrentSecLineManagerEvaluation: boolean;
  performance: PerformanceJSON;
};

export const SecLineManagerEvaluationCard = ({
  loginUserId,
  showCurrentSecLineManagerEvaluation,
  performance,
}: SecLineManagerEvaluationCardProps) => {
  const evaluation = performance.evaluations.find(node => node.type === 'EVAL_SEC_SUPERIOR');

  if (
    performance.evaluationStatus &&
    !['SELF_CONFIRM', 'EVAL_FINISHED'].includes(performance.evaluationStatus) &&
    !showCurrentSecLineManagerEvaluation
  ) {
    return null;
  }

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          二级经理意见
        </Typography.Title>
        {evaluation &&
          evaluation.users
            .filter(evaluationUser =>
              showCurrentSecLineManagerEvaluation ? loginUserId === evaluationUser.user?.id : true
            )
            .map(evaluation => (
              <Typography.Text key={`${evaluation.id}_${evaluation.user?.id}`}>
                二级经理({evaluation.user?.name})同意
              </Typography.Text>
            ))}
      </Space>
    </Card>
  );
};
