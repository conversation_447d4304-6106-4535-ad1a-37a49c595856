import React, { useState } from 'react';

import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { GradesSummaries, PerformanceLocales } from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';

import { PerformanceGradTable } from './performance-grade-table';

export type YearGradeCardProps = {
  performanceVersion: keyof PerformanceLocales['annualPerformanceGradeRules']['daily'];
  totalGrade: number | undefined;
  periods: string[];
  gradesSummaries: GradesSummaries | null;
  gradesSummariesRecords: GradesSummaries[] | null;
  objectives: PerformanceObjectiveJSON[];
};

const TYPE_TEXT_MAPPER: Record<string, string> = {
  DAILY: '季度扣分',
  DAY_PLUS: '季度加分',
  TOTAL: '得分',
};

const renderGradeValueStyle = (type: string, grade: number | null) => {
  if (type === 'TOTAL') {
    return grade !== null && grade >= 0 ? grade : '--';
  }
  return grade !== null ? (
    grade === 0 ? (
      grade
    ) : (
      <Typography.Text type={grade > 0 ? 'success' : 'danger'}>
        {grade > 0 ? '+' : ''}
        {grade >= 10 ? 10 : grade}
      </Typography.Text>
    )
  ) : (
    '--'
  );
};
export const YearGradeCard = ({
  performanceVersion,
  totalGrade,
  periods,
  gradesSummaries,
  gradesSummariesRecords,
  objectives,
}: YearGradeCardProps) => {
  const gradeRules = React.useMemo(() => {
    return getPerformanceLocales().annualPerformanceGradeRules.year[performanceVersion];
  }, [performanceVersion]);

  const isSecondPerformanceVersion = React.useMemo(
    () => performanceVersion === PERFORMANCE_SECOND_VERSION,
    [performanceVersion]
  );

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <Table
          rowKey="type"
          columns={[
            {
              title: '周期',
              dataIndex: 'type',
              render: val => <>{TYPE_TEXT_MAPPER[val]}</>,
            },
            {
              title: 'Q1得分',
              dataIndex: 'Q1',
              render: (val, record) => renderGradeValueStyle(record.type, val),
            },
            {
              title: 'Q2得分',
              dataIndex: 'Q2',
              render: (val, record) => renderGradeValueStyle(record.type, val),
            },
            {
              title: 'Q3得分',
              dataIndex: 'Q3',
              render: (val, record) => renderGradeValueStyle(record.type, val),
            },
            {
              title: 'Q4得分',
              dataIndex: 'Q4',
              render: (val, record) => renderGradeValueStyle(record.type, val),
            },
            {
              title: '年度指标得分',
              dataIndex: 'YEAR_GRADE',
              show: !isSecondPerformanceVersion,
              render: val => {
                return (
                  <Typography.Text type={val >= 0 ? 'success' : 'danger'}>
                    {val > 0 ? (val > 10 ? '+10' : `+${val}`) : val}
                  </Typography.Text>
                );
              },
            },
            {
              title: '年度指标评分记录',
              dataIndex: 'operate',
              show: !isSecondPerformanceVersion,
              render: () => {
                return <YearGradeDetailButton records={objectives} />;
              },
            },
          ]}
          dataSource={(gradesSummariesRecords ?? []).map(record => ({
            ...record,
            // 兼容历史绩效的展示
            YEAR_GRADE: objectives.reduce((acc, cur) => acc + (cur.grade ?? 0), 0),
          }))}
          pagination={false}
        />
        <Space style={{ width: '100%', justifyContent: 'space-between' }}>
          <Typography.Text>
            年度最终得分： <Tag color="processing">{totalGrade}分</Tag>
          </Typography.Text>
          <Popover
            title="计分规则"
            content={
              <ul
                style={{
                  padding: '0 16px',
                  margin: 0,
                  width: isSecondPerformanceVersion ? 344 : 596,
                }}
              >
                {gradeRules.map((rule, index) => (
                  <li key={rule}>
                    <Typography.Text>{rule} </Typography.Text>
                  </li>
                ))}
              </ul>
            }
            placement="topRight"
            trigger="hover"
          >
            <Button type="link" compact>
              计分规则
            </Button>
          </Popover>
        </Space>
      </Space>
    </>
  );
};

export const YearGradeDetailButton = ({ records }: { records: PerformanceObjectiveJSON[] }) => {
  const [open, setOpen] = useState(false);
  return (
    <>
      <Button
        type="link"
        compact
        onClick={() => {
          setOpen(true);
        }}
      >
        查看
      </Button>
      <Modal
        width={860}
        open={open}
        title="年度指标评分记录"
        footer={false}
        bodyStyle={{
          maxHeight: 780,
          overflowY: 'auto',
        }}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <Alert type="warning" description="同一年度内，年度指标加分计入上限为10分" />
          <PerformanceGradTable type="YEAR" data={records} />
        </Space>
      </Modal>
    </>
  );
};
