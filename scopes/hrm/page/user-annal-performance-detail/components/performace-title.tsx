import EnvironmentOutlined from '@ant-design/icons/es/icons/EnvironmentOutlined';
import React from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import { EvaluateStatusTag } from '@manyun/hrm.ui.evalute-status-tag';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

export const PerformanceTitle = ({
  performance,
  performancePublicResultConfigureStatus,
}: {
  performance: PerformanceJSON;
  performancePublicResultConfigureStatus?: boolean;
}) => {
  return (
    <Space style={{ width: '100%', justifyContent: 'space-between' }}>
      <Space size="small">
        <Typography.Title
          style={{ marginBottom: 0 }}
          level={5}
          showBadge
        >{`${performance.year}年度绩效考核_${performance.evaluationJob?.label}`}</Typography.Title>
        <EvaluateStatusTag pfType="REGULAR" status={performance.evaluationStatus} />
        {!performancePublicResultConfigureStatus &&
          performance.evaluationStatus === 'SELF_CONFIRM' && <Tag>待公示</Tag>}
      </Space>
      {performance.user.idc && (
        <Space>
          <EnvironmentOutlined />
          <SpaceText guid={performance.user.idc} />
        </Space>
      )}
    </Space>
  );
};
