import React, { useMemo } from 'react';

import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';
import { getAnnualPerformanceObjectiveLocales } from '@manyun/hrm.model.annual-performance-objective';
import type { BackendAnnualPerformanceObjectiveType } from '@manyun/hrm.model.annual-performance-objective';
import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';

export type DailyGradeCardProps = {
  type: BackendAnnualPerformanceObjectiveType;
  data: PerformanceObjectiveJSON[];
};

export const PerformanceGradTable = ({ type, data }: DailyGradeCardProps) => {
  const locales = useMemo(() => getAnnualPerformanceObjectiveLocales(), []);

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical">
        <Table
          rowKey="id"
          size={type === 'YEAR' ? 'small' : undefined}
          locale={{
            emptyText: `暂无考评分记录`,
          }}
          columns={[
            {
              title: '序号',
              dataIndex: 'index',
              fixed: 'left',
              width: 60,
              render: (_, __, index) => index + 1,
            },
            {
              title: locales.name,
              dataIndex: 'title',
              width: '12%',
              ellipsis: {
                showTitle: false,
              },
              render: val => (
                <Tooltip placement="topLeft" title={val}>
                  {val}
                </Tooltip>
              ),
            },
            {
              title: `评分描述`,
              dataIndex: 'gradeDescriptions',
              ellipsis: {
                showTitle: false,
              },
              render: val => (
                <Tooltip placement="topLeft" title={val}>
                  {val}
                </Tooltip>
              ),
            },
            {
              title: `考评分`,
              dataIndex: 'grade',
              width: 120,
              render: val => (
                <Typography.Text type={val >= 0 ? 'success' : 'danger'}>
                  {val > 0 ? `+${val}` : val}
                </Typography.Text>
              ),
            },
          ]}
          dataSource={data}
          pagination={false}
        />
      </Space>
    </>
  );
};
