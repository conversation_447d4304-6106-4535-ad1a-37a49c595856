import dayjs from 'dayjs';
import { sortBy } from 'lodash-es';
import React, { useMemo } from 'react';

import { User as UserAvatar } from '@manyun/auth-hub.ui.user';
import { Card } from '@manyun/base-ui.ui.card';
import { Checkbox } from '@manyun/base-ui.ui.checkbox';
import { Container } from '@manyun/base-ui.ui.container';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { PerformanceJSON } from '@manyun/hrm.model.performance';
import type {
  BackendGrade,
  PerformanceObjectiveLocales,
} from '@manyun/hrm.model.performance-objective';
import { getPerformanceObjectiveLocales } from '@manyun/hrm.model.performance-objective';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';

export type LineManagerEvaluationCardProps = {
  /**
   * 当前绩效版本，2025之前版本v1, 2025之后版本v2
   */
  performanceVersion: keyof PerformanceObjectiveLocales['grade']['annual'];
  /**
   * 是否可编辑
   */
  editable: boolean;
  /**
   * 是否为登录人的绩效
   */
  isLoginUserPerformance: boolean;
  /**
   * 是否需要打分
   */
  needEvalResult: boolean;
  performance: PerformanceJSON;
  /**
   * 当直线通过审批后 需对经理展示本人的评价
   */
  showCurrentLineManagerEvaluation: boolean;
  /**
   * 当前登录人id
   */
  loginUserId: number;
};

export const LineManagerEvaluationCard = ({
  performanceVersion,
  editable,
  isLoginUserPerformance,
  needEvalResult,
  performance,
  loginUserId,
  showCurrentLineManagerEvaluation,
}: LineManagerEvaluationCardProps) => {
  const isDailyEval = useMemo(() => {
    return performance.period !== 'YEAR';
  }, [performance.period]);

  const locales = useMemo(() => getPerformanceObjectiveLocales(), []);

  const evaluation = performance.evaluations.find(node => node.type === 'EVAL_SUPERIOR');

  const canSettingResult = useMemo(
    () =>
      performance.allowEvaluationStartedAt
        ? dayjs(performance.allowEvaluationStartedAt).diff(dayjs()) <= 0
        : false,
    [performance.allowEvaluationStartedAt]
  );

  if (
    !editable &&
    performance.evaluationStatus &&
    !['EVAL_WAIT_SEC_SUPERIOR', 'SELF_CONFIRM', 'EVAL_FINISHED'].includes(
      performance.evaluationStatus
    ) &&
    !showCurrentLineManagerEvaluation
  ) {
    return null;
  }

  return (
    <Card>
      <Space style={{ width: '100%' }} size="middle" direction="vertical">
        <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
          直线经理评价
        </Typography.Title>
        {editable ? (
          <>
            <Form.Item
              style={{ marginBottom: 0 }}
              labelCol={{ span: 24 }}
              name="lineManagerSummary"
              label={`${
                isDailyEval ? '优势或工作中表现突出的方面' : '对员工全年绩效表现进行整体客观评价'
              }`}
              rules={[
                {
                  required: true,
                  whitespace: true,
                  message: `${
                    isDailyEval
                      ? '优势或工作中表现突出的方面'
                      : '对员工全年绩效表现进行整体客观评价'
                  }必填`,
                },
                { max: 10000, message: '最多输入 10000 个字符！' },
              ]}
            >
              <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
            </Form.Item>
            {isDailyEval && (
              <Form.Item
                style={{ marginBottom: 0 }}
                labelCol={{ span: 24 }}
                name="lineManagerImprove"
                label="需改进的方面"
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    message: '需改进的方面必填',
                  },
                  { max: 10000, message: '最多输入 10000 个字符！' },
                ]}
              >
                <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
              </Form.Item>
            )}

            <Form.Item
              style={{ marginBottom: 0 }}
              valuePropName="checked"
              name="lineManagerInvited"
              label="面谈确认"
              rules={[
                {
                  required: true,
                  message: '需勾选确认面谈',
                },
                {
                  validator: (_, val) => {
                    if (val === false) {
                      return Promise.reject('需勾选确认面谈');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Checkbox>已确认面谈</Checkbox>
            </Form.Item>
            {performance.period === 'YEAR' && needEvalResult && (
              <Form.Item
                style={{ marginBottom: 0 }}
                name="lineManagerResult"
                label="考核结果"
                tooltip={
                  canSettingResult && performance.allowEvaluationStartedAt === null
                    ? undefined
                    : `您可于${dayjs(performance.allowEvaluationStartedAt).format(
                        'YYYY-MM-DD'
                      )}起，填写考核结果评分`
                }
                rules={[
                  {
                    required: true,
                    message: '考核结果必选',
                  },
                ]}
              >
                <Select
                  style={{ width: 224 }}
                  disabled={!canSettingResult}
                  showSearch
                  options={sortBy(Object.keys(locales.grade.annual[performanceVersion])).map(
                    key => ({
                      label: locales.grade.annual[performanceVersion][Number(key) as BackendGrade],
                      value: Number(key),
                    })
                  )}
                  getPopupContainer={trigger => trigger.parentNode.parentNode}
                />
              </Form.Item>
            )}
            <Form.Item
              style={{ marginBottom: 0 }}
              label="是否建议 HRBP 关注"
              name="needAttention"
              rules={[
                {
                  required: true,
                  message: '是否建议 HRBP 关注必选',
                },
              ]}
            >
              <Radio.Group>
                <Radio value={1}>是</Radio>
                <Radio value={0}>否</Radio>
              </Radio.Group>
            </Form.Item>
            <Form.Item
              shouldUpdate={(pre, next) => pre.needAttention !== next.needAttention}
              noStyle
            >
              {({ getFieldValue }) => {
                if (getFieldValue('needAttention')) {
                  return (
                    <Form.Item
                      style={{ marginBottom: 0 }}
                      name="attentionContent"
                      rules={[
                        {
                          required: true,
                          whitespace: true,
                          message: '跟进内容必填',
                        },
                        { max: 200, message: '最多输入 200 个字符！' },
                      ]}
                    >
                      <Input.TextArea
                        placeholder="请填写需要 HRBP 跟进的内容（被考核人不可见）"
                        autoSize={{ minRows: 3 }}
                      />
                    </Form.Item>
                  );
                }
                return null;
              }}
            </Form.Item>
          </>
        ) : (
          <>
            {evaluation?.users
              .filter(evaluationUser =>
                showCurrentLineManagerEvaluation ? loginUserId === evaluationUser.user?.id : true
              )
              .map(evaluation => (
                <Space
                  key={`${evaluation.id}_${evaluation.user?.id}`}
                  style={{ width: '100%' }}
                  direction="vertical"
                >
                  <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                    <Space>
                      {evaluation.user?.id && (
                        <UserAvatar
                          id={evaluation.user.id}
                          size={24}
                          showName={false}
                          editable={false}
                        />
                      )}
                      <span>{evaluation.user?.name} 评价：</span>
                    </Space>
                    {performance.period === 'YEAR' && evaluation.evaluationRequired && (
                      <Space>
                        <Typography.Text>考核结果：</Typography.Text>
                        <Tag color="processing">
                          {evaluation.result
                            ? locales.grade.annual[PERFORMANCE_SECOND_VERSION][
                                evaluation.result as unknown as BackendGrade
                              ]
                            : '--'}
                        </Tag>
                      </Space>
                    )}
                  </Space>
                  <Container color="default">
                    <Space style={{ width: '100%' }} direction="vertical" size="large">
                      <Space style={{ width: '100%' }} direction="vertical">
                        {isDailyEval && (
                          <Typography.Text strong>优势或工作中表现突出的方面：</Typography.Text>
                        )}
                        <Typography.Text style={{ whiteSpace: 'pre-wrap' }}>
                          {evaluation.comments?.summary}
                        </Typography.Text>
                      </Space>
                      {isDailyEval && (
                        <Space style={{ width: '100%' }} direction="vertical">
                          <Typography.Text strong>需改进的方面：</Typography.Text>
                          <Typography.Text style={{ whiteSpace: 'pre-wrap' }}>
                            {evaluation.comments?.improve}
                          </Typography.Text>
                        </Space>
                      )}
                      <Space>
                        <Typography.Text>面谈确认:</Typography.Text>
                        <Typography.Text strong>已确认面谈</Typography.Text>
                      </Space>
                      {/* 关注内容无需对绩效员工展示 */}
                      {!isLoginUserPerformance && evaluation.needAttention && (
                        <Space>{`建议 HRBP 关注：${evaluation.attentionContent?.trim()}`}</Space>
                      )}
                    </Space>
                  </Container>
                </Space>
              ))}
          </>
        )}
      </Space>
    </Card>
  );
};
