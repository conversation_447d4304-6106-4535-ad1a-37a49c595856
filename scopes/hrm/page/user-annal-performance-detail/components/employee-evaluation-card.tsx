import React, { useMemo } from 'react';

import { Card } from '@manyun/base-ui.ui.card';
import { Container } from '@manyun/base-ui.ui.container';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { PerformanceJSON } from '@manyun/hrm.model.performance';

export type EmployeeEvaluationCardProps = { editable: boolean; performance: PerformanceJSON };

export const EmployeeEvaluationCard = ({ editable, performance }: EmployeeEvaluationCardProps) => {
  const isDailyEval = useMemo(() => {
    return performance.period !== 'YEAR';
  }, [performance.period]);

  const employeeEvaluation = useMemo(
    () => performance.evaluations.find(node => node.type === 'SELF_EVAL'),
    [performance.evaluations]
  );

  /**
   * 员工未设定完 节点信息不可见
   */
  if (
    performance.evaluationStatus &&
    !['EVAL_WAIT_SUPERIOR', 'EVAL_WAIT_SEC_SUPERIOR', 'SELF_CONFIRM', 'EVAL_FINISHED'].includes(
      performance.evaluationStatus
    ) &&
    !editable
  ) {
    return null;
  }

  return (
    <Space style={{ width: '100%' }} direction="vertical" size="middle">
      <Card>
        <Space style={{ width: '100%' }} size="middle" direction="vertical">
          <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
            {isDailyEval ? '员工绩效完成情况总结' : '员工全年工作总结'}
          </Typography.Title>
          {editable ? (
            <>
              <Form.Item
                style={{ marginBottom: 0 }}
                labelCol={{ span: 24 }}
                name="employeeSummary"
                label={
                  isDailyEval
                    ? '根据上半年绩效计划完成情况进行详细工作总结'
                    : '根据年度绩效目标整体完成情况进行总结，重点分析取得的进步、未达成目标的具体原因和对应改进措施及个人职业发展需求等'
                }
                rules={[
                  {
                    required: true,
                    whitespace: true,
                    message: isDailyEval
                      ? '根据上半年绩效计划完成情况进行详细工作总结必填'
                      : '根据年度绩效目标整体完成情况进行总结，重点分析取得的进步、未达成目标的具体原因和对应改进措施及个人职业发展需求等必填',
                  },
                  { max: 10000, message: '最多输入 10000 个字符！' },
                ]}
              >
                <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
              </Form.Item>
              {isDailyEval && (
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 24 }}
                  name="employeeImprove"
                  label="绩效执行过程中存在的问题或风险，提出具体解决方案及所需资源支持"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: '绩效执行过程中存在的问题或风险，提出具体解决方案及所需资源支持必填',
                    },
                    { max: 10000, message: '最多输入 10000 个字符！' },
                  ]}
                >
                  <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
                </Form.Item>
              )}
            </>
          ) : employeeEvaluation ? (
            <>
              <Container color="default">
                <Space style={{ width: '100%' }} direction="vertical" size="middle">
                  <Space style={{ width: '100%' }} direction="vertical">
                    <Typography.Text strong>
                      {isDailyEval
                        ? '根据上半年绩效计划完成情况进行详细工作总结：'
                        : '根据年度绩效目标整体完成情况进行总结：'}
                    </Typography.Text>
                    <Typography.Text style={{ whiteSpace: 'pre-wrap' }}>
                      {employeeEvaluation.users.map(({ comments }) => comments?.summary).join('\n')}
                    </Typography.Text>
                  </Space>
                  {isDailyEval && (
                    <Space style={{ width: '100%' }} direction="vertical">
                      <Typography.Text strong>
                        绩效执行过程中存在的问题或风险，提出具体解决方案及所需资源支持：
                      </Typography.Text>
                      <Typography.Text style={{ whiteSpace: 'pre-wrap' }}>
                        {employeeEvaluation.users
                          .map(({ comments }) => comments?.improve)
                          .join('\n')}
                      </Typography.Text>
                    </Space>
                  )}
                </Space>
              </Container>
            </>
          ) : null}
        </Space>
      </Card>
      {isDailyEval && (
        <Card>
          <Space style={{ width: '100%' }} size="middle" direction="vertical">
            <Typography.Title style={{ marginBottom: 0 }} level={5} showBadge>
              员工自评
            </Typography.Title>
            {editable ? (
              <>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 24 }}
                  name="employeeSelfSummary"
                  label="优势或工作中表现突出的方面"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: '优势或工作中表现突出的方面必填',
                    },
                    { max: 10000, message: '最多输入 10000 个字符！' },
                  ]}
                >
                  <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
                </Form.Item>
                <Form.Item
                  style={{ marginBottom: 0 }}
                  labelCol={{ span: 24 }}
                  name="employeeSelfImprove"
                  label="需改进的方面"
                  rules={[
                    {
                      required: true,
                      whitespace: true,
                      message: '需改进的方面必填',
                    },
                    { max: 10000, message: '最多输入 10000 个字符！' },
                  ]}
                >
                  <Input.TextArea autoSize={{ minRows: 5, maxRows: 20 }} />
                </Form.Item>
              </>
            ) : employeeEvaluation ? (
              <Container color="default">
                <Space style={{ width: '100%' }} direction="vertical" size="middle">
                  <Space style={{ width: '100%' }} direction="vertical">
                    <Typography.Text strong>优势或工作中表现突出的方面：</Typography.Text>
                    <Typography.Text style={{ whiteSpace: 'pre-wrap' }}>
                      {employeeEvaluation.users
                        .map(({ comments }) => comments?.infoSummary)
                        .join('\n')}
                    </Typography.Text>
                  </Space>
                  <Space style={{ width: '100%' }} direction="vertical">
                    <Typography.Text strong>需改进的方面：</Typography.Text>
                    <Typography.Text style={{ whiteSpace: 'pre-wrap' }}>
                      {employeeEvaluation.users
                        .map(({ comments }) => comments?.infoImprove)
                        .join('\n')}
                    </Typography.Text>
                  </Space>
                </Space>
              </Container>
            ) : null}
          </Space>
        </Card>
      )}
    </Space>
  );
};
