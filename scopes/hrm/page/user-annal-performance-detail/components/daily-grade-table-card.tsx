import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { PerformanceLocales } from '@manyun/hrm.model.performance';
import { getPerformanceLocales } from '@manyun/hrm.model.performance';
import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import { PERFORMANCE_SECOND_VERSION } from '@manyun/hrm.util.performances';

import { PerformanceGradTable } from './performance-grade-table';

export type DailyGradeCardProps = {
  performanceVersion: keyof PerformanceLocales['annualPerformanceGradeRules']['daily'];
  totalGrade?: number;
  records: PerformanceObjectiveJSON[];
};

export const DailyGradeCard = ({
  performanceVersion,
  totalGrade,
  records,
}: DailyGradeCardProps) => {
  const gradeRules = React.useMemo(() => {
    return getPerformanceLocales().annualPerformanceGradeRules.daily[performanceVersion];
  }, [performanceVersion]);

  return (
    <>
      <Space style={{ width: '100%' }} direction="vertical" size="large">
        <PerformanceGradTable type="DAILY" data={records} />
        {totalGrade !== undefined && (
          <Space style={{ width: '100%', justifyContent: 'space-between' }}>
            <Typography.Text>
              季度得分：<Tag color="processing">{totalGrade}分</Tag>
            </Typography.Text>
            <Popover
              title="计分规则"
              content={
                <ul
                  style={{
                    margin: 0,
                    padding: '0 16px',
                    width: performanceVersion === PERFORMANCE_SECOND_VERSION ? 344 : 556,
                  }}
                >
                  {gradeRules.map(rule => (
                    <li key={rule}>
                      <Typography.Text>{rule} </Typography.Text>
                    </li>
                  ))}
                </ul>
              }
              placement="topRight"
              trigger="hover"
            >
              <Button type="link" compact>
                计分规则
              </Button>
            </Popover>
          </Space>
        )}
      </Space>
    </>
  );
};
