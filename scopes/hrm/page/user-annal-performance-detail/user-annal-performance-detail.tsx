import dayjs from 'dayjs';
import React, { useCallback, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';
import { useHistory, useParams } from 'react-router';
import { useLocation } from 'react-router-dom';
import { useDeepCompareEffect } from 'react-use';

import { selectMe } from '@manyun/auth-hub.state.user';
import { UserInfoCard } from '@manyun/auth-hub.ui.user-info-card';
import { Card } from '@manyun/base-ui.ui.card';
import { DownloadPdfButton } from '@manyun/base-ui.ui.download-pdf-button';
import { Empty } from '@manyun/base-ui.ui.empty';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import {
  usePerformancePublicResultConfigure,
  useUserPerformances,
} from '@manyun/hrm.gql.client.hrm';
import { getAnnualPerformancePlanLocales } from '@manyun/hrm.model.annual-performance-plan';
import { getPeriodsBySplitRule } from '@manyun/hrm.model.performance';
import type { AnnualPerformanceDetailParams } from '@manyun/hrm.route.hrm-routes';
import { ChangePerformanceTargetLink } from '@manyun/hrm.ui.change-performance-target-link';
import { AgreePerformanceButton } from '@manyun/hrm.ui.performance-approve-button';
import { PerformanceApproveProcess } from '@manyun/hrm.ui.performance-approve-process';
import { RevokePerformanceButton } from '@manyun/hrm.ui.revoke-performance-button';
import { getPerformanceVersion } from '@manyun/hrm.util.performances';

import { DailyGradeCard } from './components/daily-grade-table-card';
import { EmployeeEvaluationCard } from './components/employee-evaluation-card';
import { LineManagerEvaluationCard } from './components/line-manager-evaluation-card';
import {
  ManagerApproveButtonGroup,
  SelfEvaluateButtonGroup,
} from './components/operate-button-group';
import { PerformanceTitle } from './components/performace-title';
import { SecLineManagerEvaluationCard } from './components/sec-line-manager-evaluation-card';
import { YearGradeCard } from './components/year-grade-table-card';
import { usePerformance } from './use-performance';
import styles from './user-annal-performance-detail.module.less';

export function UserAnnalPerformanceDetail() {
  const history = useHistory();
  const { pathname } = useLocation();

  const { year, id, position, user } = useParams<AnnualPerformanceDetailParams>();
  const [form] = Form.useForm();
  const [activeTabKey, setActiveTabKey] = useState(id);
  const [execExporting, setExecExporting] = useState(false);

  const loginUser = useSelector(selectMe, (left, right) => left.userId === right.userId);

  const { loading, data: myPerformancesResult } = useUserPerformances({
    variables: {
      type: 'REGULAR',
      year: year,
      performancePosition: position,
      userId: Number(user),
      subType: 'EVAL',
    },
  });
  const {
    loading: fetchPerformanceLoading,
    data: performanceResult,
    refetch: refetchPerformance,
  } = usePerformance({
    variables: {
      type: 'REGULAR',
      subType: 'EVAL',
      id: Number(id),
    },
  });
  const { data: performancePublicResultConfigure } = usePerformancePublicResultConfigure({
    variables: {
      year: year,
    },
  });

  /**
   * 是否公示考核结果
   */
  const performancePublicResultConfigureStatus = useMemo(() => {
    return performancePublicResultConfigure?.performancePublicityConfigure?.find(
      configure => configure.period === performanceResult?.performance?.period
    )?.published;
  }, [
    performancePublicResultConfigure?.performancePublicityConfigure,
    performanceResult?.performance?.period,
  ]);

  useDeepCompareEffect(() => {
    if (performanceResult && performanceResult.performance) {
      const performance = performanceResult.performance;
      const employeeEvaluationComments = performance.evaluations
        .find(evaluation => evaluation.type === 'SELF_EVAL')
        ?.users.find(nodeUser => nodeUser.user?.id === loginUser.userId)?.comments;

      const currentLineManagerEvaluation = performance.evaluations
        .find(evaluation => evaluation.type === 'EVAL_SUPERIOR')
        ?.users.find(nodeUser => nodeUser.user?.id === loginUser.userId);

      form.setFieldsValue({
        employeeSummary: employeeEvaluationComments
          ? employeeEvaluationComments.summary
          : undefined,
        employeeImprove: employeeEvaluationComments
          ? employeeEvaluationComments.improve
          : undefined,
        employeeSelfSummary: employeeEvaluationComments
          ? employeeEvaluationComments.infoSummary
          : undefined,
        employeeSelfImprove: employeeEvaluationComments
          ? employeeEvaluationComments.infoImprove
          : undefined,
        lineManagerSummary: currentLineManagerEvaluation?.comments?.summary,
        lineManagerImprove: currentLineManagerEvaluation?.comments?.improve,
        lineManagerInvited: currentLineManagerEvaluation?.interviewed,
        lineManagerResult: performance.grade,
        needAttention: currentLineManagerEvaluation?.needAttention === true ? 1 : 0,
        attentionContent: currentLineManagerEvaluation?.attentionContent,
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [performanceResult]);

  const onLoadPerformance = useCallback(
    (id?: number) => {
      refetchPerformance({
        type: 'REGULAR',
        subType: 'EVAL',
        id: id ?? Number(activeTabKey),
      });
    },
    [activeTabKey, refetchPerformance]
  );

  const planLocales = useMemo(() => getAnnualPerformancePlanLocales(), []);

  /**
   * 考核周期是否开始
   */
  const enable = useMemo(
    () =>
      performanceResult?.performance
        ? dayjs(performanceResult.performance.startedAt).diff(dayjs()) <= 0
        : false,
    [performanceResult?.performance]
  );

  /**
   * 是否为登陆人的绩效
   */
  const isLoginUserPerformance = useMemo(
    () =>
      !!performanceResult &&
      !!performanceResult.performance &&
      Number(performanceResult.performance.user.id) === loginUser.userId!,
    [performanceResult, loginUser.userId]
  );

  /**当前处理人为登录人 */
  const isCurrentHandler = useMemo(
    () =>
      !!performanceResult &&
      !!performanceResult.performance &&
      performanceResult.performance.isLoggedInUserInCurrentStepUsers,
    [performanceResult]
  );

  /**
   * 员工自评视角
   */
  const isSelfEvaluate = useMemo(
    () =>
      isLoginUserPerformance &&
      isCurrentHandler &&
      !!performanceResult &&
      !!performanceResult.performance &&
      !!performanceResult.performance.evaluationStatus &&
      ['SELF_EVAL', 'EVAL_BACK'].includes(performanceResult.performance.evaluationStatus),
    [isCurrentHandler, isLoginUserPerformance, performanceResult]
  );

  /**
   *  是否需要显示员工自评(Q2、上半年、年度需要)
   */
  const requiredEmployeeEvaluateComments = useMemo(() => {
    return (
      !!performanceResult &&
      !!performanceResult.performance &&
      ['Q2', 'FIR_HALF_YEAR', 'YEAR'].includes(performanceResult.performance.period)
    );
  }, [performanceResult]);

  /**
   * 直线审批视角
   */
  const isLineManagerEvaluate = useMemo(
    () =>
      isCurrentHandler &&
      !!performanceResult &&
      !!performanceResult.performance &&
      !!performanceResult.performance.evaluationStatus &&
      performanceResult.performance.evaluationStatus === 'EVAL_WAIT_SUPERIOR',
    [performanceResult, isCurrentHandler]
  );

  const generateActions = () => {
    if (
      !performanceResult ||
      !performanceResult.performance ||
      !performanceResult.performance.evaluationStatus
    ) {
      return [];
    }
    /**
     * 考核完成显示
     * 1.导出pdf
     */
    if (performanceResult.performance.evaluationStatus === 'EVAL_FINISHED') {
      return [
        <DownloadPdfButton
          key="download"
          compact={false}
          disabled={execExporting}
          pdfName="年度绩效详情"
          exportElement={document.getElementById('page_annual-performance_content')}
          beforeDownload={() => {
            setExecExporting(true);
            return new Promise(resolve => {
              setTimeout(() => {
                resolve();
              }, 500);
            });
          }}
          onFinish={() => {
            setExecExporting(false);
          }}
        />,
      ];
    }

    /**
     *  绩效员工为登录人
     *  1. 绩效待自评且为处理人 显示 提交/保存
     *  2. 绩效待直线审批  显示撤回
     *  3. 绩效待员工确认 显示确认按钮
     */
    if (isLoginUserPerformance) {
      if (isSelfEvaluate) {
        return [
          <SelfEvaluateButtonGroup
            key="self-eval"
            showSave={requiredEmployeeEvaluateComments}
            performance={performanceResult.performance}
            form={form}
            onSuccess={() => {
              onLoadPerformance();
            }}
          />,
        ];
      }
      if (performanceResult.performance.evaluationStatus === 'EVAL_WAIT_SUPERIOR') {
        return [
          <RevokePerformanceButton
            key="revoke"
            subType="EVAL"
            id={performanceResult.performance.id!}
            confirmTitle={
              <div>
                确定撤回该审批？您的考核自评已在审批中，
                <br />
                撤回后直线经理将无法看到您的考核自评内容
              </div>
            }
            onSuccess={() => {
              onLoadPerformance();
            }}
          />,
        ];
      }
      if (
        performanceResult.performance.evaluationStatus === 'SELF_CONFIRM' &&
        performancePublicResultConfigureStatus
      ) {
        return [
          <AgreePerformanceButton
            key="self-confirm"
            type="primary"
            text="确认考核结果"
            bizId={performanceResult.performance.id!}
            currentStep="SELF_CONFIRM"
            onSuccess={() => {
              message.success('确认成功');
              onLoadPerformance();
            }}
          />,
        ];
      }
    }

    /**
     *  绩效当前处理人为登录人
     * 1. 直线经理 显示 提交评估/保持草稿/退回
     * 2. 二线经理 显示 同意/退回
     */
    if (
      isCurrentHandler &&
      ['EVAL_WAIT_SUPERIOR', 'EVAL_WAIT_SEC_SUPERIOR'].includes(
        performanceResult.performance.evaluationStatus
      )
    ) {
      return [
        <ManagerApproveButtonGroup
          key="approve-buttons"
          performance={performanceResult.performance}
          form={form}
          onSuccess={() => {
            history.goBack();
          }}
        />,
      ];
    }

    return [];
  };

  const currentLineManagerEvaluation = useMemo(() => {
    return performanceResult?.performance?.evaluations
      .find(evaluation => evaluation.type === 'EVAL_SUPERIOR')
      ?.users.find(nodeUser => nodeUser.user?.id === loginUser.userId);
  }, [performanceResult, loginUser.userId]);

  const currentSecLineManagerEvaluation = useMemo(() => {
    return performanceResult?.performance?.evaluations
      .find(evaluation => evaluation.type === 'EVAL_SEC_SUPERIOR')
      ?.users.find(nodeUser => nodeUser.user?.id === loginUser.userId);
  }, [performanceResult, loginUser.userId]);

  const totalGrade = useMemo(() => {
    return performanceResult && performanceResult?.performance?.gradesSummaries
      ? performanceResult.performance.gradesSummaries[performanceResult.performance.period]
      : 0;
  }, [performanceResult]);

  const tabList = useMemo(
    () => myPerformancesResult?.myPerformances.filter(pf => pf.subType === 'EVAL' && pf.id) ?? [],
    [myPerformancesResult?.myPerformances]
  );

  const selectedPerformance = useMemo(
    () => tabList.find(pf => pf.id?.toString() === activeTabKey),
    [activeTabKey, tabList]
  );

  const performanceVersion = React.useMemo(() => {
    const yearNum = Number(year);

    return getPerformanceVersion(yearNum);
  }, [year]);

  return (
    <>
      <Space
        id="page_annual-performance_content"
        style={{ width: '100%', display: 'flex' }}
        direction="vertical"
        size="middle"
      >
        <Card loading={loading} bodyStyle={{ paddingBottom: 0, paddingTop: 0 }}>
          <Tabs
            className={styles.tabs}
            activeKey={activeTabKey}
            items={tabList.map(({ id, period }) => ({
              key: id?.toString()!,
              label: `${planLocales.performancePeriod.enum[period]}考核`,
            }))}
            tabBarExtraContent={
              selectedPerformance && selectedPerformance.haveTarget ? (
                <ChangePerformanceTargetLink
                  selectedPerformance={selectedPerformance}
                  directRouter={
                    pathname.includes('team-annual-performance-detail') ? 'team' : 'user'
                  }
                />
              ) : null
            }
            onChange={value => {
              form.resetFields();
              setActiveTabKey(value);
              onLoadPerformance(Number(value));
            }}
          />
        </Card>
        <Row
          style={{
            height: execExporting
              ? 'auto'
              : generateActions().length > 0
                ? 'calc(var(--content-height) - 48px - 62px)'
                : 'calc(var(--content-height) - 62px)',
          }}
          gutter={[16, 24]}
        >
          <Col style={{ height: '100%', overflowY: 'auto' }} span={18}>
            {loading || fetchPerformanceLoading ? (
              <Skeleton />
            ) : (
              <>
                {performanceResult?.performance && (
                  <Form form={form} colon>
                    <Space
                      style={{ width: '100%', height: '100%' }}
                      direction="vertical"
                      size="middle"
                    >
                      <Card style={{ height: !enable ? '81vh' : 'auto' }}>
                        <Space style={{ width: '100%' }} direction="vertical" size="large">
                          <PerformanceTitle
                            performancePublicResultConfigureStatus={
                              performancePublicResultConfigureStatus
                            }
                            performance={{ ...performanceResult.performance, year }}
                          />
                          {enable ? (
                            <>
                              {performanceResult.performance.period === 'YEAR' ? (
                                <YearGradeCard
                                  performanceVersion={performanceVersion}
                                  totalGrade={totalGrade ?? undefined}
                                  periods={
                                    performanceResult.performance.plan
                                      ? getPeriodsBySplitRule(
                                          performanceResult.performance.plan.splitRule
                                        )
                                      : []
                                  }
                                  objectives={performanceResult.performance.objectives}
                                  gradesSummaries={performanceResult.performance.gradesSummaries}
                                  gradesSummariesRecords={
                                    performanceResult.performance.gradesSummariesRecords
                                  }
                                />
                              ) : (
                                <DailyGradeCard
                                  performanceVersion={performanceVersion}
                                  records={performanceResult.performance.objectives}
                                  totalGrade={totalGrade ?? undefined}
                                />
                              )}
                            </>
                          ) : (
                            <Empty
                              style={{
                                margin: 'auto',
                                position: 'absolute',
                                top: '50%',
                                left: '50%',
                                transform: 'translate(-50%, -50%)',
                              }}
                              description="当前不在考核周期内"
                            />
                          )}
                        </Space>
                      </Card>
                      {enable && performanceResult.performance.evaluationStatus !== 'EVAL_INIT' && (
                        <>
                          {/* 员工自评 */}
                          {requiredEmployeeEvaluateComments && (
                            <EmployeeEvaluationCard
                              performance={performanceResult.performance}
                              editable={isSelfEvaluate}
                            />
                          )}
                          {/*登录人为绩效员工，经理审批内容  仅在二线审批通过且绩效考核已公示 才展示 */}
                          {(isLoginUserPerformance &&
                            performanceResult.performance &&
                            performanceResult.performance.evaluationStatus &&
                            ['EVAL_WAIT_SUPERIOR', 'EVAL_WAIT_SEC_SUPERIOR'].includes(
                              performanceResult.performance.evaluationStatus
                            )) ||
                          (isLoginUserPerformance &&
                            !performancePublicResultConfigureStatus) ? null : (
                            <>
                              <LineManagerEvaluationCard
                                performanceVersion={performanceVersion}
                                loginUserId={loginUser.userId!}
                                showCurrentLineManagerEvaluation={
                                  currentLineManagerEvaluation?.status === 'PASS' &&
                                  performanceResult.performance.evaluationStatus ===
                                    'EVAL_WAIT_SUPERIOR'
                                }
                                isLoginUserPerformance={isLoginUserPerformance}
                                needEvalResult={
                                  currentLineManagerEvaluation?.evaluationRequired ?? false
                                }
                                performance={performanceResult.performance}
                                editable={isLineManagerEvaluate}
                              />
                              <SecLineManagerEvaluationCard
                                loginUserId={loginUser.userId!}
                                showCurrentSecLineManagerEvaluation={
                                  currentSecLineManagerEvaluation?.status === 'PASS' &&
                                  performanceResult.performance.evaluationStatus ===
                                    'EVAL_WAIT_SEC_SUPERIOR'
                                }
                                performance={performanceResult.performance}
                              />
                            </>
                          )}
                        </>
                      )}
                    </Space>
                  </Form>
                )}
              </>
            )}
          </Col>
          <Col style={{ height: '100%', overflowY: 'auto' }} span={6}>
            <Space
              style={{ width: '100%', height: '100%', display: 'flex' }}
              className={styles.rightSider}
              size="middle"
              direction="vertical"
            >
              <UserInfoCard
                style={{ width: '100%' }}
                userId={Number(user)!}
                editable={false}
                needValid={false}
              />
              {performanceResult?.performance && (
                <PerformanceApproveProcess
                  pfType="REGULAR"
                  loading={loading || fetchPerformanceLoading}
                  title={`${
                    planLocales.performancePeriod.enum[performanceResult.performance.period]
                  }考核流程`}
                  steps={performanceResult.performance.evaluations}
                />
              )}
            </Space>
          </Col>
        </Row>
      </Space>
      {generateActions().length > 0 && (
        <FooterToolBar>
          <Space size="middle">{generateActions()}</Space>
        </FooterToolBar>
      )}
    </>
  );
}
