import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BPMStatus, BackendClockIn } from './backend-clock-in';
import { getClockInLocales } from './locales';
import type { ClockInLocales } from './locales';

export type PunchType = /**上班 */ 'ON' | /**下班**/ 'OFF';
export type PunchWay =
  | /**加班打卡 */ 'EXTRA'
  | /**补卡**/ 'SUPPLY'
  | /**普通打卡**/ 'NORMAL'
  | /**外勤打卡 */ 'OUTWORK'
  | /**系统补卡 */ 'SYS_SUPPLY';
export type PunchResult = /**有效 */ '0' | /**无效**/ '1';

type SimpleUser = {
  id: number;
  name: string;
};

export type ClockInJSON = {
  id: number;
  location: string;
  type: PunchType;
  /**打卡时间 */
  punchTime: string;
  way: PunchWay;
  applyUser: SimpleUser | null;
  bizId?: string;
  title?: string;
  channel?: string;
  reason?: string;
  /**考勤组 */
  attGroupName?: string;
  /**考勤日期 */
  scheduleDate?: string;
  /**打卡结果 */
  result?: string;
  approveStatus?: BPMStatus;
  approveId?: string | null;
  applyTime?: string;
};

export class ClockIn {
  static fromApiObject(object: BackendClockIn) {
    const copy = cloneDeep(object);

    return new ClockIn(
      copy.id,
      copy.blockTag,
      copy.checkType,
      copy.checkTime,
      copy.checkWay,
      copy.staffId && copy.staffName ? { id: copy.staffId, name: copy.staffName } : null,
      copy.bizId,
      copy.checkChannel,
      copy.checkReason,
      copy.attGroupName,
      copy.scheduleDate,
      copy.checkoutResult,
      copy.checkStatus,
      copy.procInstanceId,
      copy.gmtCreate
    );
  }

  static fromJSON(json: ClockInJSON) {
    const copy = cloneDeep(json);

    return new ClockIn(
      copy.id,
      copy.location,
      copy.type,
      copy.punchTime,
      copy.way,
      copy.applyUser,
      copy.bizId,
      copy.channel,
      copy.reason,
      copy.attGroupName,
      copy.scheduleDate,
      copy.result,
      copy.approveStatus,
      copy.approveId,
      copy.applyTime
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: ClockInLocales;

  constructor(
    public id: number,
    public location: string,
    public type: PunchType,
    public punchTime: string,
    public way: PunchWay,
    public applyUser: SimpleUser | null,
    public bizId?: string,
    public channel?: string,
    public reason?: string,
    public attGroupName?: string,
    public scheduleDate?: string,
    public result?: string,
    public approveStatus?: BPMStatus,
    public approveId?: string | null,
    public applyTime?: string | null
  ) {
    this._locales = getClockInLocales(this._localeCode);
  }

  public set locales(locales: ClockInLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getTitle() {
    if (this.applyUser && this.way === 'SUPPLY') {
      return `${this.applyUser.name}${this.locales.missedPunchesFix.title.context}`;
    }
    return undefined;
  }

  public formatePunchTime() {
    return dayjs(this.punchTime).format('YYYY-MM-DD HH:mm:ss');
  }

  public formateScheduleDate() {
    if (this.scheduleDate) {
      return dayjs(this.scheduleDate).format('YYYY-MM-DD HH:mm:ss');
    }
    return undefined;
  }

  public formateApplyTime() {
    if (this.applyTime) {
      return dayjs(this.applyTime).format('YYYY-MM-DD HH:mm:ss');
    }
    return undefined;
  }

  public toApiObject(): BackendClockIn {
    return cloneDeep({
      id: this.id,
      checkType: this.type,
      checkTime: this.punchTime,
      checkWay: this.way,
      blockTag: this.location,
      checkChannel: this.channel,
      bizId: this.bizId,
      checkStatus: this.approveStatus,
      checkReason: this.reason,
      checkoutResult: this.result,
    });
  }

  public toJSON(): ClockInJSON {
    return cloneDeep({
      id: this.id,
      location: this.location,
      type: this.type,
      punchTime: this.formatePunchTime(),
      way: this.way,
      applyUser: this.applyUser,
      bizId: this.bizId,
      title: this.getTitle(),
      channel: this.channel,
      reason: this.reason,
      attGroupName: this.attGroupName,
      scheduleDate: this.formateScheduleDate(),
      result: this.result,
      approveStatus: this.approveStatus,
      approveId: this.approveId,
      applyTime: this.formateApplyTime(),
    });
  }
}
