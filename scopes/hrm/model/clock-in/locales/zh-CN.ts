import type { ClockInLocales } from './type';

export const zhCN: ClockInLocales = {
  title: '原始记录',
  location: '位置',
  punchType: {
    _self: '上/下班卡',
    enum: {
      ON: '上班',
      OFF: '下班',
    },
  },
  punchWay: {
    _self: '打卡类型',
    enum: {
      NORMAL: '普通打卡',
      EXTRA: '加班打卡',
      SUPPLY: '补卡',
      OUTWORK: '外勤打卡',
      SYS_SUPPLY: '订正打卡',
    },
  },
  punchChannel: '打卡方式',
  punchTime: '打卡时间',
  punchDate: '打卡日期',
  attGroupName: '考勤组',
  scheduleDate: '考勤日期',
  export: {
    _self: '导出',
    fileName: '人员原始考勤记录',
  },
  applyTime: {
    _self: '申请时间',
  },
  applyBy: {
    _self: '申请人',
    id: '人员ID',
    name: '姓名',
  },
  result: {
    _slef: '打卡结果',
    tooltip: '有对应班次的打卡会被计为有效打卡，否则为无效打卡',
    enum: { '0': '有效', '1': '无效' },
  },
  action: '操作',
  missedPunchesFix: {
    bizId: '补卡ID',
    addText: '新建',
    basicInfo: '基本信息',
    approveId: '对应审批单号',
    approveStatus: '状态',
    title: {
      _self: '标题',
      context: '提交的补卡',
    },
    files: '附件',
    punchType: {
      validateMsg: {
        _self: '补卡时间必选',
        limit: '仅支持选择≤当前的时间',
      },
    },
    punchTime: {
      _self: '补卡时间',
      validateMsg: {
        _self: '补卡时间必选',
        limit: '仅支持选择≤当前的时间',
      },
    },
    reason: {
      _self: '补卡原因',
      validateMsg: {
        _self: '补卡原因必填！',
        limit: '最多输入 200 个字符!',
      },
    },
    submit: {
      _self: '提交',
      success: '创建成功',
    },
    cancle: '取消',
  },
};

export default zhCN;
