export type PuncheTypeLocales = {
  ON: string;
  OFF: string;
};

export type PuncheWayLocales = {
  EXTRA: string;
  SUPPLY: string;
  NORMAL: string;
  OUTWORK: string;
  SYS_SUPPLY: string;
};

export type PuncheResultLocales = {
  '0': string;
  '1': string;
};

export type ClockInLocales = {
  title: string;
  location: string;
  punchType: {
    _self: string;
    enum: PuncheTypeLocales;
  };
  punchWay: {
    _self: string;
    enum: PuncheWayLocales;
  };
  punchTime: string;
  punchDate: string;
  punchChannel: string;
  scheduleDate: string;
  attGroupName: string;
  export: {
    _self: string;
    fileName: string;
  };
  result: {
    _slef: string;
    tooltip: string;
    enum: PuncheResultLocales;
  };
  applyTime: {
    _self: string;
  };
  applyBy: {
    _self: string;
    id: string;
    name: string;
  };
  action: string;
  missedPunchesFix: {
    bizId: string;
    title: {
      _self: string;
      context: string;
    };
    addText: string;
    approveId: string;
    approveStatus: string;
    basicInfo: string;
    files: string;
    punchType: {
      validateMsg: {
        _self: string;
        limit: string;
      };
    };
    punchTime: {
      _self: string;
      validateMsg: {
        _self: string;
        limit: string;
      };
    };
    reason: {
      _self: string;
      validateMsg: {
        _self: string;
        limit: string;
      };
    };
    submit: {
      _self: string;
      success: string;
    };
    cancle: string;
  };
};
