import type { PunchType, PunchWay } from './clock-in';

/**@todo  import form model/bpm-instance*/
export type BPMStatus = 'APPROVING' | 'PASS' | 'REFUSE' | 'REVOKE';
export type BackendClockIn = {
  id: number;
  /**上/下班 */
  checkType: PunchType;
  /**打卡时间 */
  checkTime: string;
  /**打卡类型 */
  checkWay: PunchWay;
  /**打卡位置 */
  blockTag: string;
  /**打卡通道 */
  checkChannel?: string;
  bizId?: string;
  /**审批状态 */
  checkStatus?: BPMStatus;
  /**打卡理由 */
  checkReason?: string;
  /**打卡结果 */
  checkoutResult?: string;
  dateType?: string;
  attGroupId?: number;
  attGroupName?: string;
  dutyGroupId?: number;
  dutyGroupName?: string;
  dutyId?: number;
  dutyName?: string;
  gmtCreate?: string;
  gmtModified?: string;
  /**审批ID */
  procInstanceId?: string;
  staffId?: number;
  staffName?: string;
  scheduleDate?: string;
};
