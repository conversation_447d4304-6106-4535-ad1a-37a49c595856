import type { BackendAnnualPerformanceObjectiveSubType } from '@manyun/hrm.model.annual-performance-objective';
import type {
  BackendAnnualPerformancePlanSplitRule,
  BackendPerformancePeriod,
} from '@manyun/hrm.model.annual-performance-plan';
import type {
  BackendPerformanceObjective,
  PerformanceObjectiveType,
} from '@manyun/hrm.model.performance-objective';

export { BackendPerformancePeriod, BackendAnnualPerformanceObjectiveSubType };

export type BackendPerformanceObjectiveStatus =
  | /* 初始化 */ 'TARGET_INIT'
  | /* 待设定 */ 'TARGET_SELF_SETTING'
  | /* 待审批 */ 'TARGET_WAIT_SUPERIOR'
  | /* 已通过 */ 'TARGET_FINISHED'
  | /* 已退回 */ 'TARGET_BACK'
  | /* 待二线经理审批 */ 'TARGET_WAIT_SEC_SUPERIOR'
  | /* 入职时间超过 6 个月 */ null;

export type BackendPerformanceEvaluationStatus =
  | /* 未开始 */ 'EVAL_INIT'
  | /* 待设定 */ 'SELF_EVAL'
  | /* 待直线经理审批 */ 'EVAL_WAIT_SUPERIOR'
  | /* 待二线经理审批 **/ 'EVAL_WAIT_SEC_SUPERIOR'
  | /* 待项目HR审批 **/ 'EVAL_WAIT_HR'
  | /* 待区域HR审批 **/ 'EVAL_WAIT_AREA_HR'
  | /* 考核完成 */ 'EVAL_FINISHED'
  | /* 已退回 */ 'EVAL_BACK'
  | /* 自我确认*/ 'SELF_CONFIRM'
  | /* 入职时间超过 6 个月且未完成自评 */ null;

export type BackendPerformanceResult = /* 不通过 */ 'UN_PASS' | /* 通过 */ 'PASS';

export type BackendPerformanceType = /**试用期 */ 'TP' | /**正式 */ 'REGULAR';

export type BackendPerformanceSubType = /**目标*/ 'TARGET' | /**考核**/ 'EVAL';

export type BackendEvaluationStepStatus =
  /**未开始**/
  'INIT' | /**通过 */ 'PASS' | /**退回*/ 'REFUSE' | /**进行中*/ 'APPROVING';

export type BackendEvaluationStepType =
  | 'TARGET_SELF_SETTING'
  | 'TARGET_SUPERIOR'
  | 'TARGET_SEC_SUPERIOR'
  | 'SELF_EVAL'
  | 'EVAL_SUPERIOR'
  | 'EVAL_SEC_SUPERIOR'
  | 'EVAL_HR'
  | 'EVAL_AREA_HR'
  | 'SELF_CONFIRM';

export type BackendGradeSummary = {
  period: BackendPerformancePeriod;
  grade: number;
  subType: BackendAnnualPerformanceObjectiveSubType;
  /**
   * 日常评分类型
   *  - DAILY
   *  - DAY_PLUS
   *  - YEAR
   */
  type: string | null;
};

export type BackendEvaluationContent = {
  /**
   * （员工/经理）评价总结
   */
  summary: string | null;
  /**
   * 员工提出总体改善
   */
  improve: string | null;
  /**
   * 员工自评突出内容
   */
  infoSummary: string | null;
  /**
   * 员工自评需提升内容
   */
  infoImprove: string | null;
  /**
   * 驳回理由
   */
  reason?: string | null;
};

export type BackendEvaluationStep = {
  /**
   * - 后端返回的是 `number`，在数据库中真实存在
   * - `bff` 人工合成的是 `string`
   */
  id: number | string;
  /**
   * 评价人的用户 ID
   */
  staffId: number | null;
  staffName: string | null;
  /**
   * 评价节点步骤 ID
   */
  pfPhase: BackendEvaluationStepType;
  /**评价内容 / 退回原因 */
  content: BackendEvaluationContent | null;
  status: BackendEvaluationStepStatus;
  result: BackendPerformanceResult | null;
  /**评价时间 */
  evalTime: number | null;
  /**是否是当前节点 */
  currHandle: boolean;
  /**是否审批过 */
  evaluated: boolean;
  /**
   * 是否和员工面谈过
   */
  interviewed: boolean | null;
  /**
   * 当前节点用户是否需要打分
   */
  needEvalResult: boolean | null;

  /**
   * 当前节点用户是否建议HR关注
   */
  needAttention: boolean | null;
  /**
   * HR 需要关注的内容
   */
  attentionContent: string | null;
};

export type SimpleUser = {
  id: number;
  name: string;
};

export type LabelInValue = {
  label: string;
  value: string;
};

export type BackendPerformanceSection = Omit<BackendPerformanceObjective, 'sectionType'> & {
  sectionType: PerformanceObjectiveType;
};

export type SimpleBackendAnnualPerformancePlan = {
  id: number;
  splitRule: BackendAnnualPerformancePlanSplitRule;
};

export type BackendPerformance<T extends BackendPerformanceType = 'TP'> = {
  id: number | null;
  pfType: T;
  pfSubType: BackendPerformanceSubType;
  year: string | null;
  hiredDate: number;
  staff: SimpleUser;
  idc: string | null;
  blockGuid: string | null;
  region: {
    value: string;
    label: string;
  } | null;
  position: string | null;
  jobNo: string | null;
  period: BackendPerformancePeriod;
  startDate: number;
  expireDate: number;
  targetStatus: BackendPerformanceObjectiveStatus;
  evalStatus: BackendPerformanceEvaluationStatus;
  /** @deprecated Replaced by `plan` */
  planId: number | null;
  plan: SimpleBackendAnnualPerformancePlan | null;
  /**
   * 试用期：通过/不通过 正式：直线经理打分
   */
  result: (T extends 'TP' ? BackendPerformanceResult : number) | null;
  grade: number | null;
  finalGrade: number | null;
  targetStartDate: number;
  targetExpireDate: number;
  evalStartDate: number;
  evalExpireDate: number;

  /**
   * 正式：考核岗位
   */
  evalPosition: LabelInValue | null;
  /**
   * 一级主管用户集合
   */
  superiors: SimpleUser[] | null;
  /**
   * 二级主管用户集合
   */
  secSuperiors: SimpleUser[] | null;
  hrs: SimpleUser[] | null;
  /**
   * 当前评估人集合
   */
  currentEvalStaffs: SimpleUser[] | null;
  /**
   * 目标集合
   */
  sections: BackendPerformanceSection[];
  /**
   * 评价流程集合
   */
  evaluations: BackendEvaluationStep[];

  /**
   * 正式绩效评分记录汇总
   */
  totalKpiRecords: BackendGradeSummary[] | null;
  /**
   * 各季度加减分记录（年度绩效会返回）
   */
  periodsKpiRecords: BackendGradeSummary[] | null;
  /**
   * 正式绩效考核结果设定开启时间
   */
  resultSetTime: number | null;

  /**
   * 正式绩效 是否可以修改考核岗位
   */
  canUpdateEvalPosition: boolean | null;

  /**
   * 正式绩效目标得分
   */
  kpiGrade: number | null;

  haveTarget: boolean | null;
  /**
   * 目标变更有效期
   * @example 2023.01.01~2023.12.31
   */
  targetChangeValidityDateStr: string | null;
  /**
   * 目标变更类型
   */
  targetChangeType: string | null;
};
