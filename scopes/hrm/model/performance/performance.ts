import md5 from 'crypto-js/md5.js';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BackendAnnualPerformancePlanSplitRule } from '@manyun/hrm.model.annual-performance-plan';
import type { PerformanceObjectiveJSON } from '@manyun/hrm.model.performance-objective';
import { PerformanceObjective } from '@manyun/hrm.model.performance-objective';

import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendEvaluationContent,
  BackendEvaluationStep,
  BackendEvaluationStepStatus,
  BackendEvaluationStepType,
  BackendPerformance,
  BackendPerformanceEvaluationStatus,
  BackendPerformanceObjectiveStatus,
  BackendPerformancePeriod,
  BackendPerformanceResult,
  BackendPerformanceSubType,
  BackendPerformanceType,
  LabelInValue,
  SimpleBackendAnnualPerformancePlan,
  SimpleUser,
} from './backend-performance.js';
import { getPerformanceLocales } from './locales/index.js';
import type { PerformanceLocales } from './locales/index.js';

export type PerformanceStatus =
  | 'TARGET_INIT'
  | 'TARGET_WAIT_SETTING'
  | 'TARGET_WAIT_APPROVING'
  | 'TARGET_PASS'
  | 'TARGET_REFUSE'
  | 'EVAL_INIT'
  | 'EVAL_WAIT_SETTING'
  | 'EVAL_WAIT_APPROVING'
  | 'EVAL_PASS'
  | 'EVAL_REFUSE'
  | 'EVAL_SELF_CONFIRM'
  | null;

export type SyntheticEvaluationStepType = 'TARGET_FINISHED' | 'EVAL_FINISHED';

export type EvaluationStepUser = {
  id: string;
  user: SimpleUser | null;
  comments: BackendEvaluationContent | null;
  status: BackendEvaluationStepStatus;
  result: BackendPerformanceResult | null;
  evaluatedAt: number | null;
  isEvaluated: boolean;
  interviewed: boolean | null;
  evaluationRequired: boolean | null;
  needAttention: boolean | null;
  attentionContent: string | null;
};

export type StepStatus = 'wait' | 'process' | 'finish' | 'error';

export type EvaluationStep = {
  id: string;
  type: BackendEvaluationStepType | SyntheticEvaluationStepType;
  status: StepStatus;
  isCurrentStep: boolean;
  users: EvaluationStepUser[];
};

export type PerformanceUser = SimpleUser & {
  region: {
    value: string;
    label: string;
  } | null;
  idc: string | null;
  blockGuid: string | null;
  job: string | null;
  jobNo: string | null;
  hiredAt: number;
};

export type PeriodGradeSummary = Record<BackendAnnualPerformanceObjectiveSubType | 'grade', number>;
export type GradesSummaries = Record<
  Partial<BackendPerformancePeriod>,
  number | null | undefined
> & {
  /**
   * - TOTAL 总得分
   * - DAILY 季度扣分
   * - DAY_PLUS 季度加分
   */
  type: string;
};

export type PerformanceJSON = {
  rowKey: string;
  id: number | null;
  type: BackendPerformanceType;
  year: string | null;
  subType: BackendPerformanceSubType;
  user: PerformanceUser;
  evaluationJob: LabelInValue | null;
  period: BackendPerformancePeriod;
  startedAt: number;
  expiredAt: number;
  status: PerformanceStatus;
  objectives: PerformanceObjectiveJSON[];
  objectiveStatus: BackendPerformanceObjectiveStatus;
  objectiveStartedAt: number;
  objectiveExpiredAt: number;
  currentStepIdx: number;
  isLoggedInUserInCurrentStepUsers: boolean;
  evaluations: EvaluationStep[];
  evaluationStatus: BackendPerformanceEvaluationStatus;
  evaluationStartedAt: number;
  evaluationExpiredAt: number;
  /** @deprecated Replaced by `` */
  planId: number | null;
  plan: SimpleBackendAnnualPerformancePlan | null;
  result: BackendPerformanceResult | null;
  grade: number | null;
  gradeAvg: number | null;
  /**
   * @deprecated
   */
  gradesSummaries: GradesSummaries | null;
  gradesSummariesRecords: GradesSummaries[] | null;
  lineManagers: SimpleUser[] | null;
  secondLineManagers: SimpleUser[] | null;
  hrs: SimpleUser[] | null;
  currentHandlers: SimpleUser[] | null;
  /**
   * 允许绩效评价的开始时间
   */
  allowEvaluationStartedAt: number | null;

  /*
   *  是否允许修改考核岗位
   */
  canModifyEvaluationJob: boolean | null;
  /**
   * 绩效目标得分
   */
  objectivesGrade: number | null;
  haveTarget: boolean | null;
  targetChangeValidityDateStr: string | null;
  targetChangeType: string | null;
};

export class Performance<T extends BackendPerformanceType = 'TP'> {
  static generateRowKey<T extends BackendPerformanceType = 'TP'>(object: BackendPerformance<T>) {
    return object.id !== null
      ? `${object.id}_${object.pfSubType}`
      : md5(JSON.stringify(object)).toString();
  }

  static fromApiObject<T extends BackendPerformanceType = 'TP'>(
    object: BackendPerformance<T>,
    loggedInUser: SimpleUser
  ) {
    const copy = cloneDeep(object);
    const rowKey = Performance.generateRowKey(copy);
    const { currentStepIdx, steps } = groupByEvaluations(rowKey, {
      type: copy.pfType,
      subType: copy.pfSubType,
      evaluations: copy.evaluations,
      // startedAt: copy.startDate,
      evaluationStartedAt: copy.evalStartDate,
    });
    const _isLoggedInUserInCurrentStepUsers = isLoggedInUserInCurrentStepUsers(
      copy.currentEvalStaffs ?? [],
      loggedInUser
    );
    const objectives = copy.sections.map(PerformanceObjective.fromApiObject);

    return new Performance(
      rowKey,
      copy.id,
      copy.pfType,
      copy.pfSubType,
      copy.year,
      {
        ...copy.staff,
        region: copy.region,
        idc: copy.idc,
        blockGuid: copy.blockGuid,
        job: copy.position,
        hiredAt: copy.hiredDate,
        jobNo: copy.jobNo,
      },
      copy.period,
      copy.startDate,
      copy.expireDate,
      objectives,
      copy.targetStatus,
      copy.targetStartDate,
      copy.targetExpireDate,
      currentStepIdx,
      _isLoggedInUserInCurrentStepUsers,
      steps,
      copy.evalStatus,
      copy.evalStartDate,
      copy.evalExpireDate,
      copy.evalPosition,
      copy.planId,
      copy.plan,
      copy.pfType === 'TP' ? (copy as BackendPerformance<'TP'>).result : null,
      copy.pfType === 'REGULAR' ? (copy as BackendPerformance<'REGULAR'>).result : copy.grade,
      copy.finalGrade,
      copy.totalKpiRecords
        ? normalizeGradesSummaries(
            copy.period,
            copy.totalKpiRecords,
            copy.period === 'YEAR' ? copy.plan : null
          )
        : null,
      copy.totalKpiRecords
        ? normalizeGradesSummariesRecords(
            copy.period,
            copy.totalKpiRecords,
            copy.periodsKpiRecords,
            copy.period === 'YEAR' ? copy.plan : null
          )
        : null,

      copy.superiors,
      copy.secSuperiors,
      copy.hrs,
      copy.currentEvalStaffs,
      copy.resultSetTime,
      copy.canUpdateEvalPosition,
      copy.kpiGrade,
      copy.haveTarget,
      copy.targetChangeValidityDateStr,
      copy.targetChangeType
    );
  }

  static fromJSON(json: PerformanceJSON) {
    const copy = cloneDeep(json);

    return new Performance(
      copy.rowKey,
      copy.id,
      copy.type,
      copy.subType,
      copy.year,
      copy.user,
      copy.period,
      copy.startedAt,
      copy.expiredAt,
      copy.objectives.map(PerformanceObjective.fromJSON),
      copy.objectiveStatus,
      copy.objectiveStartedAt,
      copy.objectiveExpiredAt,
      copy.currentStepIdx,
      copy.isLoggedInUserInCurrentStepUsers,
      copy.evaluations,
      copy.evaluationStatus,
      copy.evaluationStartedAt,
      copy.evaluationExpiredAt,
      copy.evaluationJob,
      copy.planId,
      copy.plan,
      copy.result,
      copy.grade,
      copy.gradeAvg,
      copy.gradesSummaries,
      copy.gradesSummariesRecords,
      copy.lineManagers,
      copy.secondLineManagers,
      copy.hrs,
      copy.currentHandlers,
      copy.allowEvaluationStartedAt,
      copy.canModifyEvaluationJob,
      copy.objectivesGrade,
      copy.haveTarget,
      copy.targetChangeValidityDateStr,
      copy.targetChangeType
    );
  }

  private _localeCode: string = 'zh-CN';

  private _locales: PerformanceLocales;

  constructor(
    public rowKey: string,
    public id: number | null,
    public type: T,
    public subType: BackendPerformanceSubType,
    public year: string | null,
    public user: PerformanceUser,
    public period: BackendPerformancePeriod,
    public startedAt: number,
    public expiredAt: number,
    public objectives: PerformanceObjective[],
    public objectiveStatus: BackendPerformanceObjectiveStatus,
    public objectiveStartedAt: number,
    public objectiveExpiredAt: number,
    public currentStepIdx: number,
    public isLoggedInUserInCurrentStepUsers: boolean,
    public evaluations: EvaluationStep[],
    public evaluationStatus: BackendPerformanceEvaluationStatus,
    public evaluationStartedAt: number,
    public evaluationExpiredAt: number,
    public evaluationJob: LabelInValue | null,
    /** @deprecated Replaced by `plan` */
    public planId: number | null,
    public plan: SimpleBackendAnnualPerformancePlan | null,
    public result: BackendPerformanceResult | null,
    public grade: number | null,
    public gradeAvg: number | null,
    public gradesSummaries: GradesSummaries | null,
    public gradesSummariesRecords:
      | (GradesSummaries & {
          type: string;
        })[]
      | null,
    public lineManagers: SimpleUser[] | null,
    public secondLineManagers: SimpleUser[] | null,
    public hrs: SimpleUser[] | null,
    public currentHandlers: SimpleUser[] | null,
    public allowEvaluationStartedAt: number | null,
    public canModifyEvaluationJob: boolean | null,
    public objectivesGrade: number | null,
    public haveTarget: boolean | null,
    public targetChangeValidityDateStr: string | null,
    public targetChangeType: string | null
  ) {
    this._locales = getPerformanceLocales(this._localeCode);
  }

  public set locales(locales: PerformanceLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public get status(): PerformanceStatus {
    if (this.subType === 'TARGET') {
      switch (this.objectiveStatus) {
        case 'TARGET_INIT':
          return 'TARGET_INIT';
        case 'TARGET_SELF_SETTING':
          return 'TARGET_WAIT_SETTING';
        case 'TARGET_WAIT_SUPERIOR':
        case 'TARGET_WAIT_SEC_SUPERIOR':
          return 'TARGET_WAIT_APPROVING';
        case 'TARGET_FINISHED':
          return 'TARGET_PASS';
        case 'TARGET_BACK':
          return 'TARGET_REFUSE';
        default:
          return null;
      }
    } else if (this.subType === 'EVAL') {
      switch (this.evaluationStatus) {
        case 'EVAL_INIT':
          return 'EVAL_INIT';
        case 'SELF_EVAL':
          return 'EVAL_WAIT_SETTING';
        case 'EVAL_WAIT_SUPERIOR':
        case 'EVAL_WAIT_SEC_SUPERIOR':
        case 'EVAL_WAIT_HR':
        case 'EVAL_WAIT_AREA_HR':
          return 'EVAL_WAIT_APPROVING';
        case 'EVAL_FINISHED':
          return 'EVAL_PASS';
        case 'EVAL_BACK':
          return 'EVAL_REFUSE';
        case 'SELF_CONFIRM':
          return 'EVAL_SELF_CONFIRM';
        default:
          return null;
      }
    }

    return null;
  }

  public getFormattedStartedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.startedAt).format(template);
  }

  public getFormattedExpiredAt(template = 'YYYY-MM-DD') {
    return dayjs(this.expiredAt).format(template);
  }

  public getFormattedObjectiveStartedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.objectiveStartedAt).format(template);
  }

  public getFormattedObjectiveExpiredAt(template = 'YYYY-MM-DD') {
    return dayjs(this.objectiveExpiredAt).format(template);
  }

  public getFormattedEvaluationStartedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.evaluationStartedAt).format(template);
  }

  public getFormattedEvaluationExpiredAt(template = 'YYYY-MM-DD') {
    return dayjs(this.evaluationExpiredAt).format(template);
  }

  public toApiObject(): BackendPerformance<T> {
    return cloneDeep({
      id: this.id,
      pfType: this.type,
      pfSubType: this.subType,
      year: this.year,
      hiredDate: this.user.hiredAt,
      staff: {
        id: this.user.id,
        name: this.user.name,
      },
      idc: this.user.idc,
      blockGuid: this.user.blockGuid,
      region: this.user.region,
      position: this.user.job,
      jobNo: this.user.jobNo,
      period: this.period,
      startDate: this.startedAt,
      expireDate: this.expiredAt,
      sections: this.objectives.map(objective => objective.toApiObject()),
      targetStatus: this.objectiveStatus,
      evaluations: splitEvaluations(this.evaluations),
      evalStatus: this.evaluationStatus,
      planId: this.planId,
      plan: this.plan,
      result: (this.type === 'TP' ? this.result : null) as BackendPerformance<T>['result'],
      grade: this.grade,
      finalGrade: this.gradeAvg,
      targetStartDate: this.objectiveStartedAt,
      targetExpireDate: this.objectiveExpiredAt,
      evalStartDate: this.evaluationStartedAt,
      evalExpireDate: this.evaluationExpiredAt,
      evalPosition: this.evaluationJob,
      superiors: this.lineManagers,
      secSuperiors: this.secondLineManagers,
      hrs: this.hrs,
      currentEvalStaffs: this.currentHandlers,
      totalKpiRecords: this.gradesSummaries
        ? unNormalizeGradesSummaries(this.gradesSummaries)
        : null,
      periodsKpiRecords: null,
      resultSetTime: this.allowEvaluationStartedAt,
      canUpdateEvalPosition: this.canModifyEvaluationJob,
      kpiGrade: this.objectivesGrade,
      haveTarget: this.haveTarget,
      targetChangeValidityDateStr: this.targetChangeValidityDateStr,
      targetChangeType: this.targetChangeType,
    });
  }

  public toJSON(): PerformanceJSON {
    return cloneDeep({
      rowKey: this.rowKey,
      id: this.id,
      type: this.type,
      subType: this.subType,
      year: this.year,
      user: this.user,
      period: this.period,
      startedAt: this.startedAt,
      expiredAt: this.expiredAt,
      status: this.status,
      objectives: this.objectives.map(objective => objective.toJSON()),
      objectiveStatus: this.objectiveStatus,
      objectiveStartedAt: this.objectiveStartedAt,
      objectiveExpiredAt: this.objectiveExpiredAt,
      currentStepIdx: this.currentStepIdx,
      isLoggedInUserInCurrentStepUsers: this.isLoggedInUserInCurrentStepUsers,
      evaluations: this.evaluations,
      evaluationStatus: this.evaluationStatus,
      evaluationStartedAt: this.evaluationStartedAt,
      evaluationExpiredAt: this.evaluationExpiredAt,
      evaluationJob: this.evaluationJob,
      planId: this.planId,
      plan: this.plan,
      result: this.result,
      grade: this.grade,
      gradeAvg: this.gradeAvg,
      gradesSummaries: this.gradesSummaries,
      gradesSummariesRecords: this.gradesSummariesRecords,
      lineManagers: this.lineManagers,
      secondLineManagers: this.secondLineManagers,
      hrs: this.hrs,
      currentHandlers: this.currentHandlers,
      allowEvaluationStartedAt: this.allowEvaluationStartedAt,
      canModifyEvaluationJob: this.canModifyEvaluationJob,
      objectivesGrade: this.objectivesGrade,
      haveTarget: this.haveTarget,
      targetChangeValidityDateStr: this.targetChangeValidityDateStr,
      targetChangeType: this.targetChangeType,
    });
  }
}

function isLoggedInUserInCurrentStepUsers(users: SimpleUser[], loggedInUser: SimpleUser) {
  return users.some(user => user.id === loggedInUser.id);
}

function getSyntheticLastStep(
  performanceRowKey: string,
  subType: BackendPerformanceSubType,
  prevStep: EvaluationStep
): EvaluationStep {
  const isPrevStepFinished = prevStep.status === 'finish';
  const status = isPrevStepFinished ? 'finish' : 'wait';
  const isCurrentStep = status === 'finish';
  const latestEvaluatedStepUser =
    prevStep.users.length <= 0
      ? null
      : prevStep.users.sort((a, b) => (b.evaluatedAt ?? 0) - (a.evaluatedAt ?? 0))[0];
  const isLatestEvaluatedStepUserPassed = latestEvaluatedStepUser?.status === 'PASS';
  // because `performanceRowKey` is like `36d839a3c52733b6d3893d0f6a32e79a` long,
  // so use abbreviations here: `esl` stands for `Evaluation Stynthetic Last`
  const stepId = `${performanceRowKey}_esl`;
  const step: EvaluationStep = {
    id: stepId,
    type: 'TARGET_FINISHED',
    status,
    isCurrentStep,
    users: [
      {
        // `u` stands for `user`
        id: `${stepId}_u`,
        user: null,
        comments: null,
        status: isPrevStepFinished && isLatestEvaluatedStepUserPassed ? 'PASS' : 'INIT',
        result: null,
        evaluatedAt:
          isPrevStepFinished && latestEvaluatedStepUser
            ? latestEvaluatedStepUser.evaluatedAt
            : null,
        isEvaluated: isLatestEvaluatedStepUserPassed,
        interviewed: false,
        evaluationRequired: false,
        needAttention: null,
        attentionContent: null,
      },
    ],
  };

  if (subType === 'EVAL') {
    step.type = 'EVAL_FINISHED';
  }

  return step;
}

type SimplePerformance = {
  type: BackendPerformanceType;
  subType: BackendPerformanceSubType;
  evaluations: BackendEvaluationStep[];
  evaluationStartedAt: number;
};

function groupByEvaluations(
  performanceRowKey: string,
  { type, subType, evaluations, evaluationStartedAt }: SimplePerformance
): { currentStepIdx: number; steps: EvaluationStep[] } {
  const isAnnualPerformanceNotStarted =
    type === 'REGULAR' && subType === 'EVAL' && evaluationStartedAt > Date.now();

  let currentStepIdx = -1;
  const steps = evaluations.reduce<EvaluationStep[]>((array, evaluation, idx) => {
    const getUser = (): EvaluationStepUser => {
      return {
        id: evaluation.id?.toString(),
        user:
          evaluation.staffId !== null && evaluation.staffName !== null
            ? {
                id: evaluation.staffId,
                name: evaluation.staffName,
              }
            : null,
        comments: evaluation.content,
        status: isAnnualPerformanceNotStarted ? 'INIT' : evaluation.status,
        result: evaluation.result,
        evaluatedAt:
          !evaluation.evaluated && evaluation.status === 'REFUSE' ? null : evaluation.evalTime,
        isEvaluated: evaluation.evaluated,
        interviewed: evaluation.interviewed,
        evaluationRequired: evaluation.needEvalResult,
        needAttention: evaluation.needAttention,
        attentionContent: evaluation.attentionContent,
      };
    };

    const getEvaluationStep = (): EvaluationStep => {
      const stepUser = getUser();

      return {
        id: `${performanceRowKey}_evaluation_${idx}`,
        type: evaluation.pfPhase,
        status: getStepStatus([stepUser]),
        isCurrentStep: isAnnualPerformanceNotStarted ? false : evaluation.currHandle,
        users: [stepUser],
      };
    };

    if (idx === 0) {
      const step = getEvaluationStep();
      if (step.isCurrentStep) {
        currentStepIdx = 0;
      }
      array.push(step);

      return array;
    }

    const prevIndex = array.length - 1;
    const prevStep = array[prevIndex];
    if (prevStep.type === evaluation.pfPhase) {
      const stepUser = getUser();
      array[prevIndex].users.push(stepUser);
      array[prevIndex].status = getStepStatus(array[prevIndex].users);
    } else {
      const step = getEvaluationStep();
      if (step.isCurrentStep) {
        currentStepIdx = idx;
      }
      array.push(step);
    }

    return array;
  }, []);

  if (steps.length <= 0) {
    return { currentStepIdx, steps };
  }

  const lastStep = getSyntheticLastStep(performanceRowKey, subType, steps[steps.length - 1]);
  const newSteps = [...steps, lastStep];
  if (lastStep.isCurrentStep) {
    newSteps[newSteps.length - 2].isCurrentStep = false;
    currentStepIdx = newSteps.length - 1;
  }

  return { currentStepIdx, steps: newSteps };
}

function getStepStatus(stepUsers: EvaluationStepUser[]): StepStatus {
  let status: StepStatus = 'wait';
  if (stepUsers.some(stepUser => stepUser.status === 'REFUSE')) {
    status = 'wait';
  } else if (stepUsers.some(stepUser => stepUser.status === 'APPROVING')) {
    status = 'process';
  } else if (stepUsers.some(stepUser => stepUser.status === 'PASS')) {
    status = 'finish';
  }
  return status;
}

function splitEvaluations(evaluations: EvaluationStep[]): BackendEvaluationStep[] {
  return evaluations.reduce<BackendEvaluationStep[]>((array, backendEvaluation) => {
    if (
      backendEvaluation.type === 'TARGET_FINISHED' ||
      backendEvaluation.type === 'EVAL_FINISHED'
    ) {
      return array;
    }

    backendEvaluation.users.forEach(userStep => {
      array.push({
        id: userStep.id,
        staffId: userStep.user?.id ?? null,
        staffName: userStep.user?.name ?? null,
        // @ts-expect-error: TS2322 because of type `SyntheticEvaluationStepType`
        pfPhase: backendEvaluation.type,
        content: userStep.comments,
        status: userStep.status,
        result: userStep.result,
        evalTime: userStep.evaluatedAt,
        currHandle: userStep.isEvaluated,
        evaluated: userStep.isEvaluated,
      });
    });

    return array;
  }, []);
}

function unNormalizeGradesSummaries(
  gradesSummaries: GradesSummaries
): Exclude<BackendPerformance['totalKpiRecords'], null> {
  return [];
}
export function getPeriodsBySplitRule(
  splitRule: BackendAnnualPerformancePlanSplitRule
): BackendPerformancePeriod[] {
  switch (splitRule) {
    // case 'YEAR':
    //   return ['YEAR'];
    // case 'HALF_YEAR':
    //   return ['FIR_HALF_YEAR', 'SEC_HALF_YEAR', 'YEAR'];
    case 'QUARTER':
      return ['Q1', 'Q2', 'Q3', 'Q4', 'YEAR'];
    default:
      return [];
  }
}

function normalizeGradesSummaries(
  period: BackendPerformancePeriod,
  totalKpiRecords: Exclude<BackendPerformance['totalKpiRecords'], null>,
  plan: BackendPerformance['plan']
): GradesSummaries | null {
  const periods = plan ? getPeriodsBySplitRule(plan.splitRule) : [period];

  const summaries: Partial<GradesSummaries> = {};

  for (const period of periods) {
    const record = totalKpiRecords.find(record => record.period === period);
    summaries[period] = record?.grade;
    summaries.type = 'TOTAL';
  }
  return summaries as GradesSummaries;
}

function normalizeGradesSummariesRecords(
  period: BackendPerformancePeriod,
  totalKpiRecords: Exclude<BackendPerformance['totalKpiRecords'], null>,
  periodsKpiRecords: Exclude<BackendPerformance['periodsKpiRecords'], null> | null,
  plan: BackendPerformance['plan']
): GradesSummaries[] {
  const periods = plan ? getPeriodsBySplitRule(plan.splitRule) : [period];

  const summariesRecords: GradesSummaries[] = [];

  // create a summary object with default values
  const createSummary = (type: string): GradesSummaries => ({
    type,
    Q1: null,
    Q2: null,
    Q3: null,
    Q4: null,
    YEAR: null,
  });
  // Process periods KPI records if available
  if (periodsKpiRecords) {
    ['DAILY', 'DAY_PLUS'].forEach(type => {
      const periodSummary = createSummary(type);
      for (const period of periods) {
        const records = periodsKpiRecords.filter(
          record => record.period === period && record.type === type
        );
        periodSummary[period] = records.reduce((acc, curr) => acc + curr.grade, 0);
      }
      summariesRecords.push(periodSummary);
    });
  }

  // Process total KPI records
  const summary = createSummary('TOTAL');
  for (const period of periods) {
    const record = totalKpiRecords.find(record => record.period === period);
    if (record) {
      summary[period] = record.grade;
    }
  }
  summariesRecords.push(summary);

  return summariesRecords;
}
