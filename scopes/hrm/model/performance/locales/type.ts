import type { BackendPerformanceResult } from '../index.js';

export type PerformanceLocales = {
  title: string;
  content: string;
  evaluationStatus: {
    __self: string;
    // enum: Record<string, string>;
  };

  objectiveStatus: {
    __self: string;
    // test: Record<string, string>;
    // annual: Record<string, string>;
  };
  result: {
    __self: string;
    enum: Record<BackendPerformanceResult, string>;
  };
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };

  selfComprehensive: string;
  supervisorComprehensive: string;
  createdAt: string;
  modifiedAt: string;
  evaluationStepType: {
    __self: string;
    TP: Record<string, string>;
    REGULAR: Record<string, string>;
  };
  status: {
    __self: string;
    test: Record<string, string>;
    annual: Record<string, string>;
    enum: Record<string, string>;
  };
  evaluationStepText: {
    enum: Record<string, string>;
  };

  pfType: {
    enum: Record<string, string>;
    test: {
      objectiveStatus: {
        enum: Record<string, string>;
      };
      evaluationStatus: {
        enum: Record<string, string>;
      };
    };
    annual: {
      objectiveStatus: {
        enum: Record<string, string>;
      };
      evaluationStatus: {
        enum: Record<string, string>;
      };
    };
  };
  annualPerformanceGradeRules: {
    daily: Record<string, string[]>;
    year: Record<string, string[]>;
  };
};
