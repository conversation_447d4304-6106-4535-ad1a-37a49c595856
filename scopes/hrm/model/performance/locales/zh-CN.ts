import {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_SECOND_VERSION,
} from '@manyun/hrm.util.performances';

import type { PerformanceLocales } from './type.js';

export const zhCN: PerformanceLocales = {
  title: '标题',
  content: '内容',
  evaluationStatus: {
    __self: '考核状态',
  },
  objectiveStatus: {
    __self: '目标状态',
  },
  result: {
    __self: '考核结果',
    enum: {
      UN_PASS: '不通过',
      PASS: '通过',
    },
  },
  selfComprehensive: '员工自评',
  supervisorComprehensive: '经理综合评价',
  evaluationStepType: {
    __self: '流程',
    TP: {
      EVAL_HR: '项目HR审批',
      EVAL_AREA_HR: '区域HR审批',
      EVAL_SEC_SUPERIOR: '二级经理审批',
      EVAL_SUPERIOR: '直线经理审批',
      SELF_EVAL: '员工自评',
      TARGET_SELF_SETTING: '员工设定目标',
      TARGET_SUPERIOR: '直线经理审批',
      TARGET_SEC_SUPERIOR: '二级经理审批',
      TARGET_FINISHED: '试用期目标落地',
      EVAL_FINISHED: '考核完成',
    },
    REGULAR: {
      EVAL_HR: 'HR审批',
      EVAL_SEC_SUPERIOR: '二级经理审批',
      EVAL_SUPERIOR: '直线经理审批',
      SELF_EVAL: '员工自评',
      TARGET_SELF_SETTING: '员工确认目标',
      TARGET_SUPERIOR: '直线经理审批',
      TARGET_SEC_SUPERIOR: '二级经理审批',
      TARGET_FINISHED: '年度目标落地',
      EVAL_FINISHED: '考核完成',
      SELF_CONFIRM: ' 待确认考核结果',
    },
  },
  createdBy: {
    __self: '创建人',
    id: '创建人 ID',
    name: '创建人姓名',
  },
  createdAt: '创建于',
  modifiedAt: '修改于',
  status: {
    __self: '状态',
    enum: {
      TARGET_INIT: '初始化',
      TARGET_WAIT_SETTING: '待设定',
      TARGET_WAIT_APPROVING: '待审批',
      TARGET_WAIT_SEC_SUPERIOR: '待审批',
      TARGET_PASS: '已通过',
      TARGET_REFUSE: '已退回',
      EVAL_INIT: '未开始',
      EVAL_WAIT_SETTING: '待自评',
      EVAL_WAIT_APPROVING: '待审批',
      EVAL_PASS: '考核完成',
      EVAL_REFUSE: '已退回',
      EVAL_SELF_CONFIRM: '待确认结果',
    },
    test: {
      TARGET_INIT: '初始化',
      TARGET_WAIT_SETTING: '待设定',
      TARGET_WAIT_APPROVING: '待审批',
      TARGET_WAIT_SEC_SUPERIOR: '待审批',
      TARGET_PASS: '已通过',
      TARGET_REFUSE: '已退回',
      EVAL_INIT: '未开始',
      EVAL_WAIT_SETTING: '待自评',
      EVAL_WAIT_APPROVING: '待审批',
      EVAL_PASS: '考核完成',
      EVAL_REFUSE: '已退回',
      EVAL_SELF_CONFIRM: '待确认结果',
    },
    annual: {
      TARGET_INIT: '初始化',
      TARGET_WAIT_SETTING: '待确认',
      TARGET_WAIT_APPROVING: '待审批',
      TARGET_WAIT_SEC_SUPERIOR: '待审批',
      TARGET_PASS: '已通过',
      TARGET_REFUSE: '已退回',
      EVAL_INIT: '未开始',
      EVAL_WAIT_SETTING: '待自评',
      EVAL_WAIT_APPROVING: '待审批',
      EVAL_PASS: '考核完成',
      EVAL_REFUSE: '已退回',
      EVAL_SELF_CONFIRM: '待确认结果',
    },
  },
  evaluationStepText: {
    enum: {
      SELF_EVAL: '员工自评',
      EVAL_SUPERIOR: '经理综合评价',
      EVAL_SEC_SUPERIOR: '二级经理意见',
      EVAL_HR: '项目HR意见',
      EVAL_AREA_HR: '区域HR意见',
    },
  },
  pfType: {
    enum: {
      test: '试用期',
      annual: '年度',
    },
    test: {
      objectiveStatus: {
        enum: {
          TARGET_INIT: '未开始',
          TARGET_SELF_SETTING: '待设定',
          TARGET_WAIT_SUPERIOR: '待审批',
          TARGET_BACK: '已退回',
          TARGET_FINISHED: '已通过',
        },
      },
      evaluationStatus: {
        enum: {
          EVAL_INIT: '未开始',
          SELF_EVAL: '待自评',
          EVAL_WAIT_SUPERIOR: '待直线经理审批',
          EVAL_WAIT_SEC_SUPERIOR: '待二级经理审批',
          EVAL_WAIT_HR: '待项目HR审批',
          EVAL_WAIT_AREA_HR: '待区域HR审批',
          EVAL_BACK: '已退回',
          EVAL_FINISHED: '考核完成',
          SELF_CONFIRM: ' 待确认考核结果',
        },
      },
    },
    annual: {
      objectiveStatus: {
        enum: {
          TARGET_INIT: '未开始',
          TARGET_SELF_SETTING: '待确认',
          TARGET_WAIT_SUPERIOR: '待直线经理审批',
          TARGET_WAIT_SEC_SUPERIOR: '待二级经理审批',
          TARGET_BACK: '已退回',
          TARGET_FINISHED: '已完成',
        },
      },
      evaluationStatus: {
        enum: {
          EVAL_INIT: '未开始',
          SELF_EVAL: '待自评',
          EVAL_WAIT_SUPERIOR: '待直线经理审批',
          EVAL_WAIT_SEC_SUPERIOR: '待二级经理审批',
          EVAL_WAIT_HR: '待HR审批',
          EVAL_BACK: '已退回',
          EVAL_FINISHED: '考核完成',
          SELF_CONFIRM: ' 待确认考核结果',
        },
      },
    },
  },
  annualPerformanceGradeRules: {
    daily: {
      [PERFORMANCE_BASIC_VERSION]: [
        '季度绩效得分 = 100 - 日常扣分项1（分数绝对值） - 日常扣分项2（分数绝对值）- …… -日常扣分项n（分数绝对值）',
      ],
      [PERFORMANCE_SECOND_VERSION]: [
        '季度绩效得分 = 100 - 季度扣分 + 季度加分',
        '备注：每个季度加分累计不超过10分',
      ],
    },
    year: {
      [PERFORMANCE_BASIC_VERSION]: [
        '季度绩效得分 = 100 - 日常扣分项1（分数绝对值） - 日常扣分项2（分数绝对值）- …… -日常扣分项n（分数绝对值）',
        '年度指标得分：年度加分项1（分数绝对值） + 年度加分项2（分数绝对值）+ …… +年度加分项n（分数绝对值）；加分上限10分',
        '年度最终得分 = 各季度绩效得分平均值 + 年度指标得分',
      ],
      [PERFORMANCE_SECOND_VERSION]: [
        '季度绩效得分 = 100 - 季度扣分 + 季度加分',
        '年度最终得分 = 季度绩效得分平均值',
        '备注：每个季度加分累计不超过10分',
      ],
    },
  },
};

export default zhCN;
