import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

import type {
  BPMStatus,
  BackendShiftAdjustmentTicket,
  CombineLeaveTime,
  LeaveRequestType,
  ShiftAdjustment,
  ShiftAdjustmentTicketType,
} from './backend-shift-adjustment-ticket';
import { getShiftAdjustmentTicketLocales } from './locales';
import type { ShiftAdjustmentTicketLocales } from './locales';

type SimpleUser = {
  id: number;
  name: string;
};
type LeaveInfo = {
  startTime: number | null;
  startTimeStr: string | null;
  endTime: number | null;
  endTimeStr: string | null;
  totalTime: number | null;
  unit: 'DAY' | 'HOUR' | null;
};

export type ShiftAdjustmentTicketJSON = {
  id: number;
  title: string;
  type: ShiftAdjustmentTicketType;
  subType: LeaveRequestType | null;
  reason: string | null;
  bizId: string;
  approveStatus: BPMStatus;
  approveId: string | null;
  location: string;
  applyBy: SimpleUser;
  createBy: SimpleUser;
  files: McUploadFile[];
  finishAt: string | null;
  createdAt: string;
  modifiedAt: string;
  rollBackApproveStatus: BPMStatus | null;
  leaveInfo: LeaveInfo | null;
  shiftAdjustments: ShiftAdjustment[];
  combineLeave: boolean;
  combineLeaveTimeInfo: CombineLeaveTime[] | null;
  canRollBack?: boolean;
  birthday?: string;
};

export class ShiftAdjustmentTicket {
  static fromApiObject(object: BackendShiftAdjustmentTicket) {
    const copy = cloneDeep(object);

    return new ShiftAdjustmentTicket(
      copy.id,
      copy.alterType,
      copy.alterSubType,
      copy.applyReason,
      copy.bizId,
      copy.bizStatus,
      copy.procInstanceId,
      copy.blockTag,
      {
        id: copy.applyStaffId,
        name: copy.applyStaffName,
      },
      {
        id: copy.creatorId,
        name: copy.creatorName,
      },
      (copy.files ?? []).map(file => McUploadFile.fromApiObject(file)),
      copy.finishTime,
      copy.gmtCreate,
      copy.gmtModified,
      copy.rollBackAlter,
      copy.alterType === 'LEAVE'
        ? {
            startTime: copy.startTime,
            startTimeStr: copy.startTimeDesc,
            endTime: copy.endTime,
            endTimeStr: copy.endTimeDesc,
            totalTime: copy.totalTime,
            unit: copy.unit,
          }
        : null,
      copy.alterDetail,
      copy.combineLeave,
      copy.combineLeaveTimeInfo,
      copy.canRollBack,
      copy.birthday
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: ShiftAdjustmentTicketLocales;

  constructor(
    public id: number,
    public type: ShiftAdjustmentTicketType,
    public subType: LeaveRequestType | null,
    public reason: string | null,
    public bizId: string,
    public approveStatus: BPMStatus,
    public approveId: string | null,
    public location: string,
    public applyBy: SimpleUser,
    public createBy: SimpleUser,
    public files: McUploadFile[],
    public finishAt: number | null,
    public createdAt: number,
    public modifiedAt: number,
    public rollBackApproveStatus: BPMStatus | null,
    public leaveInfo: LeaveInfo | null,
    public shiftAdjustments: ShiftAdjustment[],
    public combineLeave: boolean,
    public combineLeaveTimeInfo: CombineLeaveTime[] | null,
    public canRollBack?: boolean,
    public birthday?: string
  ) {
    this._locales = getShiftAdjustmentTicketLocales(this._localeCode);
  }

  public set locales(locales: ShiftAdjustmentTicketLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.modifiedAt).format(template);
  }

  public getFormattedFinishAt(template = 'YYYY-MM-DD HH:mm:ss') {
    if (this.finishAt) {
      return null;
    }
    return dayjs(this.finishAt).format(template);
  }

  public getTitle() {
    return `${this.applyBy.name}${this.locales.title.divider}${this.locales.type.enum[this.type]}`;
  }

  public toJSON(): ShiftAdjustmentTicketJSON {
    return cloneDeep({
      id: this.id,
      title: this.getTitle(),
      type: this.type,
      subType: this.subType,
      reason: this.reason,
      bizId: this.bizId,
      approveStatus: this.approveStatus,
      approveId: this.approveId,
      location: this.location,
      applyBy: this.applyBy,
      createBy: this.createBy,
      files: this.files,
      finishAt: this.getFormattedFinishAt(),
      createdAt: this.getFormattedCreatedAt(),
      modifiedAt: this.getFormattedModifiedAt(),
      rollBackApproveStatus: this.rollBackApproveStatus,
      leaveInfo: this.leaveInfo,
      shiftAdjustments: this.shiftAdjustments,
      combineLeave: this.combineLeave,
      combineLeaveTimeInfo: this.combineLeaveTimeInfo,
      canRollBack: this.canRollBack,
      birthday: this.birthday,
    });
  }
}
