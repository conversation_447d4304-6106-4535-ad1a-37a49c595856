import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';

import type {
  ShiftAdjustmentLeaveTimeUnitLocales,
  ShiftAdjustmentLeaveTypeLocales,
  ShiftAdjustmentTypeLocales,
} from './locales';

/**请假类型 */
export type LeaveRequestType = keyof ShiftAdjustmentLeaveTypeLocales;

/**请假时长单位 */
export type LeaveTimeUnit = keyof ShiftAdjustmentLeaveTimeUnitLocales;

/**排班调整类型 */
export type ShiftAdjustmentTicketType = keyof ShiftAdjustmentTypeLocales;

/**@todo  import form model/bpm-instance*/
export type BPMStatus = 'APPROVING' | 'PASS' | 'REFUSE' | 'REVOKE';

export type CombineLeaveTime = {
  leaveType: LeaveRequestType;
  takeTime: number;
  unit: LeaveTimeUnit;
};

export type ShiftAdjustment = {
  applyType: ShiftAdjustmentTicketType;
  changeScheduleEndTime: number | null;
  changeScheduleStartTime: number | null;
  dutyGroupId: number;
  dutyId: number;
  dutyName: number;
  inUserId: number;
  inUserName: string;
  needReplace: boolean;
  oaInUserId: number | null;
  oaOutUserId: number | null;
  outUserId: number | null;
  outUserName: string | null;
  replaceDate: string;
  scheduleEndTime: number | null;
  scheduleId: number;
  scheduleStartTime: number | null;
  timeRange: string;
};

export type BackendShiftAdjustmentTicket = {
  id: number;
  alterDetail: ShiftAdjustment[];

  alterSubType: LeaveRequestType | null;
  alterType: ShiftAdjustmentTicketType;
  applyReason: string | null;
  applyStaffId: number;
  applyStaffName: string;
  /**业务id */
  bizId: string;
  /**审批状态 */
  bizStatus: BPMStatus;
  /**位置 */
  blockTag: string;
  /**是否为组合假 */
  combineLeave: boolean;
  /**组合假拼假额度：请假类型、请假时长、时间单位 */
  combineLeaveTimeInfo: CombineLeaveTime[] | null;
  creatorId: number;
  creatorName: string;
  /**请假结束时间 */
  endTime: number | null;
  endTimeDesc: string | null;
  files: BackendMcUploadFile[];
  finishTime: number | null;
  gmtCreate: number;
  gmtModified: number;
  /**审批ID */
  procInstanceId: string | null;
  /**销假审批状态 */
  rollBackAlter: BPMStatus | null;
  /**请假开始时间 */
  startTime: number | null;
  startTimeDesc: string | null;
  /**请假时长 */
  totalTime: number | null;
  /**请假时长单位 */
  unit: LeaveTimeUnit | null;
  /**是否可以撤销 */
  canRollBack?: boolean;
  /**子女出生日期 */
  birthday?: string;
};
