import type { LocaleCode } from '@teammc/react-intl';

import zhCN from './zh-CN';

export type {
  ShiftAdjustmentTicketLocales,
  ShiftAdjustmentTypeLocales,
  ShiftAdjustmentLeaveTypeLocales,
  ShiftAdjustmentLeaveTimeUnitLocales,
} from './type';

export function getShiftAdjustmentTicketLocales(localeCode: LocaleCode = 'zh-CN') {
  switch (localeCode) {
    case 'zh-CN':
      return zhCN;
    default:
      return zhCN;
  }
}
