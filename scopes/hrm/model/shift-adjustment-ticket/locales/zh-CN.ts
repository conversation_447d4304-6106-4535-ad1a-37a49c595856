import type { ShiftAdjustmentTicketLocales } from './type';

export const zhCN: ShiftAdjustmentTicketLocales = {
  title: {
    _self: '标题',
    divider: '发起的',
  },
  type: {
    _self: '调班类型',
    enum: {
      LEAVE: '请假',
      REST: '顶班',
      EXCHANGE: ' 换班',
      RETURN_EXCHANGE: '还班',
    },
  },
  bizId: '编号ID',
  content: '内容',
  createdBy: {
    __self: '创建人',
    id: '创建人 ID',
    name: '创建人姓名',
  },
  createdAt: '申请时间',
  applyBy: {
    __self: '申请人',
    id: '申请人 ID',
    name: '申请人 姓名',
  },
  finishAt: '完成时间',
  approveStatus: {
    _self: '状态',
  },
  approveId: '对应审批单号',
  location: '位置',
  files: '附件',
  leaveType: {
    _self: '请假类型',
    enum: {
      ANNUAL_LEAVE: '年假',
      PERSONAL_LEAVE: '事假',
      FULL_PAY_SICK_LEAVE: '全薪病假',
      SICK_LEAVE: '病假',
      BREAK_OFF: '调休',
      ANNUAL_COMBINED_LEAVE: '组合假（年假+调休+事假）',
      BREAK_OFF_COMBINED_LEAVE: '组合假（年假+调休）',
      SICK_COMBINED_LEAVE: '组合假（全薪病假+病假）',
      MATERNITY_LEAVE: '产假',
      PATERNITY_LEAVE: '陪产假',
      MARRIAGE_HOLIDAY: '婚假',
      FUNERAL_LEAVE: '丧假',
      CHILDCARE_LEAVE: '育儿假',
      PRENATAL_CHECKUP_LEAVE: '产检假',
    },
  },
  leaveTime: '请假时间',
  leaveTimeLong: '请假时长',
  leaveTimeUnit: {
    DAY: '天',
    HOUR: '小时',
  },
  shiftName: '班次',
  shiftTime: '时间',
  replacePerson: '人',
  rollBackAlter: {
    _self: '销假状态',
    enum: {
      APPROVING: '销假中',
      PASS: '已销假',
    },
  },
  useCombinedLeave: '使用组合假',
};

export default zhCN;
