export type ShiftAdjustmentTypeLocales = {
  LEAVE: string;
  REST: string;
  EXCHANGE: string;
  RETURN_EXCHANGE: string;
};

export type ShiftAdjustmentLeaveTypeLocales = {
  ANNUAL_LEAVE: string;
  PERSONAL_LEAVE: string;
  FULL_PAY_SICK_LEAVE: string;
  SICK_LEAVE: string;
  BREAK_OFF: string;
  MATERNITY_LEAVE: string;
  PATERNITY_LEAVE: string;
  MARRIAGE_HOLIDAY: string;
  FUNERAL_LEAVE: string;
  CHILDCARE_LEAVE: string;
  SICK_COMBINED_LEAVE: string;
  ANNUAL_COMBINED_LEAVE: string;
  PRENATAL_CHECKUP_LEAVE: string;
  BREAK_OFF_COMBINED_LEAVE: string;
};

export type ShiftAdjustmentResumptionStatusLocales = {
  APPROVING: string;
  PASS: string;
};

export type ShiftAdjustmentLeaveTimeUnitLocales = {
  DAY: string;
  HOUR: string;
};

export type ShiftAdjustmentTicketLocales = {
  title: {
    _self: string;
    divider: string;
  };
  type: {
    _self: string;
    enum: ShiftAdjustmentTypeLocales;
  };
  bizId: string;
  content: string;
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  applyBy: {
    __self: string;
    id: string;
    name: string;
  };
  finishAt: string;
  approveStatus: {
    _self: string;
  };
  approveId: string;
  location: string;
  files: string;
  leaveType: {
    _self: string;
    enum: ShiftAdjustmentLeaveTypeLocales;
  };
  leaveTime: string;
  leaveTimeLong: string;
  leaveTimeUnit: {
    DAY: string;
    HOUR: string;
  };
  shiftName: string;
  shiftTime: string;
  replacePerson: string;
  rollBackAlter: {
    _self: string;
    enum: ShiftAdjustmentResumptionStatusLocales;
  };
  useCombinedLeave: string;
};
