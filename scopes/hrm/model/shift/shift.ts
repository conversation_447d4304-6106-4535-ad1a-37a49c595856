import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { BackendShift } from './backend-shift';
import { getShiftLocales } from './locales';
import type { ShiftLocales } from './locales';

export const DEFAULT_CLOSED_VALUE = 'closed';

export type TimeRangeSetting = {
  startTime: string;
  endTime: string;
  endIsNextDay: boolean;
  startIsNextDay: boolean;
  timeRangeStr?: string;
  chTimeRangeStr?: string;
};

export type ShiftDutySetting = {
  /**是否需要打上班卡 */
  isNeedPunchIn?: boolean;
  /**上班打卡时间范围 */
  punchInStartTime?: string;
  punchInStartTimeIsNextDay?: boolean;
  punchInEndTime?: string;
  punchInEndTimeIsNextDay?: boolean;

  /**是否需要打下班卡 */
  isNeedPunchOut: boolean;
  /**下班打卡时间范围 */
  punchOutStartTime?: string;
  punchOutStartTimeIsNextDay?: boolean;
  punchOutEndTime?: string;
  punchOutEndTimeIsNextDay?: boolean;

  // punchOutTimeRange?: TimeRangeSetting | null;
  /**是否允许休息 */
  isAllowRest: boolean;
  /**休息时长 - 兼容之前的需求，后续不在维护时长，而是维护时间段 */
  restMinutes?: number | null;
  /**休息时间段 */
  restStartTime?: string;
  restStartTimeIsNextDay?: boolean;
  restEndTime?: string;
  restEndTimeIsNextDay?: boolean;

  // restTimeRange?: TimeRangeSetting | null;
  /**不允许连续上班 */
  notAllowContinuousWork: boolean;
  /** 强制休息时长  单位：分钟*/
  notAllowContinuousTime?: number | null;

  /**是否允许晚到晚走/早到早走 */
  isAllowEarlyToLeaveEarlyOrLateToLeaveLate: boolean;
  /** 上班最多迟到分钟*/
  maxLateWorkMinutes?: (number | string)[] | null;
  /**下班最多早走分钟 */
  maxEarlyWorkMinutes?: (number | string)[] | null;
  /**允许晚到/早走不记为异常 */
  isAllowLateWorkOrEarlyLeaveAsNornal: boolean;
  /** 上班晚到多少分钟不计为异常 */
  canLateWorkMinutes?: (number | string)[] | null;
  /**下班早走分钟不计异常 */
  canEarlyWorkMinutes?: (number | string)[] | null;
  /**允许下班晚走，第二天可晚到  晚走晚到 */
  isAllowLateLeaveAndLateWork: boolean;
  /**晚走晚到规则 */
  lateLeaveAndLateWorkRules?: {
    /**晚走小时 */
    lateLeaveHours: number;
    /**晚到小时 */
    lateWorkHours: number;
  }[];
};

export type ShiftJSON = {
  id: number;
  name: string;
  /**上班时间 */
  onDutyTime?: string;
  /**下班时间 */
  offDutyTime?: string;
  /**班次全名：名称+时间范围 */
  fullName?: string;
  /**时间范围 */
  timeRang?: string;
  /**下班时间是否跨天 */
  offIsNextDay?: boolean;
  /**更新时间 */
  gmtModified?: number | null;
  /**更新人信息 */
  modifyUser?: {
    id?: number;
    name?: string;
  };
  /**班次设置属性 */
  dutySetting?: ShiftDutySetting;
};

export class Shift {
  static fromApiObject(object: BackendShift) {
    const copy = cloneDeep(object);
    return new Shift(
      copy.id,
      copy.dutyName,
      copy.onDutyTime,
      copy.offDutyTime,
      copy.offIsNextDay,
      copy.dutyProperties
        ? {
            notAllowContinuousWork: !copy.dutyProperties.allowContinuousWork,
            isAllowRest: copy.dutyProperties.allowRest,
            restStartTime: copy.dutyProperties.restRange?.startTime,
            restStartTimeIsNextDay: copy.dutyProperties.restRange?.startIsNextDay,
            restEndTime: copy.dutyProperties.restRange?.endTime,
            restEndTimeIsNextDay: copy.dutyProperties.restRange?.endIsNextDay,

            isAllowLateWorkOrEarlyLeaveAsNornal: copy.dutyProperties.enableBuffer,
            isAllowLateLeaveAndLateWork: copy.dutyProperties.enableCompensate,
            isAllowEarlyToLeaveEarlyOrLateToLeaveLate: copy.dutyProperties.enableOffset,

            isNeedPunchIn: copy.dutyProperties.enableOnDutyCheckRange,
            punchInStartTime: copy.dutyProperties.onDutyCheckRange?.startTime,
            punchInStartTimeIsNextDay: copy.dutyProperties.onDutyCheckRange?.startIsNextDay,
            punchInEndTime: copy.dutyProperties.onDutyCheckRange?.endTime,
            punchInEndTimeIsNextDay: copy.dutyProperties.onDutyCheckRange?.endIsNextDay,

            isNeedPunchOut: copy.dutyProperties.enableOffDutyCheckRange,
            punchOutStartTime: copy.dutyProperties.offDutyCheckRange?.startTime,
            punchOutStartTimeIsNextDay: copy.dutyProperties.offDutyCheckRange?.startIsNextDay,
            punchOutEndTime: copy.dutyProperties.offDutyCheckRange?.endTime,
            punchOutEndTimeIsNextDay: copy.dutyProperties.offDutyCheckRange?.endIsNextDay,

            lateLeaveAndLateWorkRules: (copy.dutyProperties.compensateHours ?? []).map(rule => {
              const hourArr = rule.split(',');
              return {
                lateLeaveHours: Number(hourArr[0] ?? 0),
                lateWorkHours: Number(hourArr[1] ?? 0),
              };
            }),
            notAllowContinuousTime: Number(
              ((copy.dutyProperties.notAllowContinuousTime ?? 0) / 60).toFixed(0)
            ),
            canLateWorkMinutes: calculateMiutesToHoursAndMiutes(
              copy.dutyProperties.onDutyBufferMinutes,
              copy.dutyProperties.enableBuffer
            ),
            canEarlyWorkMinutes: calculateMiutesToHoursAndMiutes(
              copy.dutyProperties.offDutyBufferMinutes,
              copy.dutyProperties.enableBuffer
            ),
            maxLateWorkMinutes: calculateMiutesToHoursAndMiutes(
              copy.dutyProperties.onDutyOffsetMinutes,
              copy.dutyProperties.enableOffset
            ),
            maxEarlyWorkMinutes: calculateMiutesToHoursAndMiutes(
              copy.dutyProperties.offDutyOffsetMinutes,
              copy.dutyProperties.enableOffset
            ),
            restMinutes: copy.dutyProperties.restMinutes,
          }
        : undefined,
      copy.gmtModified,
      {
        id: copy.modifierId,
        name: copy.modifierName,
      }
    );
  }

  static fromJSON(json: ShiftJSON) {
    const copy = cloneDeep(json);

    return new Shift(
      copy.id,
      copy.name,
      copy.onDutyTime,
      copy.offDutyTime,
      copy.offIsNextDay,
      copy.dutySetting,
      copy.gmtModified,
      copy.modifyUser
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: ShiftLocales;

  constructor(
    public id: number,
    public name: string,
    public onDutyTime?: string,
    public offDutyTime?: string,
    public offIsNextDay?: boolean,
    public dutySetting?: ShiftDutySetting,
    public gmtModified?: number | null,
    public modifyUser?: {
      id?: number;
      name?: string;
    }
  ) {
    this._locales = getShiftLocales(this._localeCode);
  }

  public set locales(locales: ShiftLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public toApiObject(): BackendShift {
    return cloneDeep({
      id: this.id,
      dutyName: this.name.trim(),
      onDutyTime: dayjs(this.onDutyTime).format('HH:mm'),
      offDutyTime: dayjs(this.offDutyTime).format('HH:mm'),
      offIsNextDay: this.offIsNextDay ?? false,
      gmtModified: dayjs(this.gmtModified).valueOf(),
      modifierName: this.modifyUser?.name,
      modifierId: this.modifyUser?.id,
      dutyProperties: this.dutySetting
        ? {
            allowContinuousWork: this.dutySetting.notAllowContinuousWork ? false : true,
            allowRest: this.dutySetting.isAllowRest,
            enableBuffer: this.dutySetting.isAllowLateWorkOrEarlyLeaveAsNornal,
            enableCompensate: this.dutySetting.isAllowLateLeaveAndLateWork,
            enableOffDutyCheckRange: this.dutySetting.isNeedPunchOut,
            enableOffset: this.dutySetting.isAllowEarlyToLeaveEarlyOrLateToLeaveLate,

            enableOnDutyCheckRange: this.dutySetting.isNeedPunchIn ?? false,
            compensateHours: this.dutySetting.lateLeaveAndLateWorkRules?.map(
              rule => rule.lateLeaveHours + ',' + rule.lateWorkHours
            ),
            notAllowContinuousTime: Number(
              ((this.dutySetting.notAllowContinuousTime ?? 0) * 60).toFixed(0)
            ),
            offDutyBufferMinutes: calculateHoursAndMiutesToMiutes(
              this.dutySetting.canEarlyWorkMinutes
            ),
            onDutyBufferMinutes: calculateHoursAndMiutesToMiutes(
              this.dutySetting.canLateWorkMinutes
            ),
            offDutyCheckRange: {
              endIsNextDay: this.dutySetting.punchOutEndTimeIsNextDay ?? false,
              endTime: dayjs(this.dutySetting.punchOutEndTime).format('HH:mm'),
              startIsNextDay: this.dutySetting.punchOutStartTimeIsNextDay ?? false,
              startTime: dayjs(this.dutySetting.punchOutStartTime).format('HH:mm'),
            },
            onDutyCheckRange: {
              endIsNextDay: this.dutySetting.punchInEndTimeIsNextDay ?? false,
              endTime: dayjs(this.dutySetting.punchInEndTime).format('HH:mm'),
              startIsNextDay: this.dutySetting.punchInStartTimeIsNextDay ?? false,
              startTime: dayjs(this.dutySetting.punchInStartTime).format('HH:mm'),
            },
            offDutyOffsetMinutes: calculateHoursAndMiutesToMiutes(
              this.dutySetting.maxEarlyWorkMinutes
            ),
            onDutyOffsetMinutes: calculateHoursAndMiutesToMiutes(
              this.dutySetting.maxLateWorkMinutes
            ),
            restMinutes: this.dutySetting.restMinutes,
            restRange: {
              endIsNextDay: this.dutySetting.restEndTimeIsNextDay ?? false,
              endTime: dayjs(this.dutySetting.restEndTime).format('HH:mm'),
              startIsNextDay: this.dutySetting.restStartTimeIsNextDay ?? false,
              startTime: dayjs(this.dutySetting.restStartTime).format('HH:mm'),
            },
          }
        : undefined,
    });
  }

  public toJSON(): ShiftJSON {
    return cloneDeep({
      id: this.id,
      name: this.name,
      onDutyTime: this.onDutyTime,
      offDutyTime: this.offDutyTime,
      offIsNextDay: this.offIsNextDay,
      timeRang: this.getTimeRange(),
      fullName: this.getFullName(),
      gmtModified: this.gmtModified,
      modifyUser: this.modifyUser,
      dutySetting: this.dutySetting,
    });
  }

  public getFullName() {
    return `${this.name}：${this.onDutyTime}-${
      this.offIsNextDay ? `${this.locales.offIsNextDay}` : ''
    }${this.offDutyTime}`;
  }

  public getTimeRange() {
    return `${this.onDutyTime}-${this.offIsNextDay ? `${this.locales.offIsNextDay}` : ''}${
      this.offDutyTime
    }`;
  }
}

function calculateMiutesToHoursAndMiutes(
  miutes?: number | null,
  required?: boolean /**用于判断是否为关闭 */
): (number | string)[] | undefined {
  if (miutes !== undefined && miutes !== null) {
    const hour = Math.floor(miutes / 60);
    const miute = miutes % 60;
    return miute > 0 ? [hour * 60, miute] : [hour * 60];
  }
  if (required) {
    return [DEFAULT_CLOSED_VALUE];
  }
  return undefined;
}

function calculateHoursAndMiutesToMiutes(times?: (number | string)[] | null): number | undefined {
  if (times !== undefined && times !== null) {
    if (times[0] !== undefined && typeof times[0] === 'number') {
      return Number((Number(times[0] ?? 0) + Number(times[1] ?? 0)).toFixed(0));
    }
    return undefined;
  }

  return undefined;
}
