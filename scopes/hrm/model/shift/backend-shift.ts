/**班次时间范围设置 */
export type BackendTimeRangeSetting = {
  /**时间范围，含次日 */
  chTimeRangeStr?: string;
  /** 结束时间是否为第二天 */
  endIsNextDay: boolean;
  /**结束时间 */
  endTime: string;
  /** 开始时间是否为第二天 */
  startIsNextDay: boolean;
  /**开始时间 */
  startTime: string;
  /** 时间范围，不含次日  */
  timeRangeStr?: string;
};

/**班次考勤相关属性 */
export type BackedShiftAttSetting = {
  /** 允许连续上班*/
  allowContinuousWork: boolean;
  /**是否允许休息 */
  allowRest: boolean;
  /**允许上下班时间宽松 */
  enableBuffer: boolean;
  /** 支持跨班次补偿*/
  enableCompensate: boolean;
  /** 是否支持下班打卡范围*/
  enableOffDutyCheckRange: boolean;
  /**支持上下班时间偏移 */
  enableOffset: boolean;
  /**是否支持上班打卡范围 */
  enableOnDutyCheckRange: boolean;
  /** 支持跨班次补偿小时列表*/
  compensateHours?: string[] | null;
  /**不允许连续上班时长，单位： 小时*/
  notAllowContinuousTime?: number | null;
  /**下班时间宽松分钟 */
  offDutyBufferMinutes?: number | null;
  /**下班打卡时间范围 */
  offDutyCheckRange?: BackendTimeRangeSetting | null;
  /**上班打卡时间范围 */
  onDutyCheckRange?: BackendTimeRangeSetting | null;
  /**下班时间偏移范围 */
  offDutyOffsetMinutes?: number | null;
  /**上班时间宽松分钟 */
  onDutyBufferMinutes?: number | null;
  /**上班时间偏移范围 */
  onDutyOffsetMinutes?: number | null;
  /**休息分钟数 */
  restMinutes?: number | null;
  /**休息时间范围 */
  restRange?: BackendTimeRangeSetting | null;
};

export type BackendShift = {
  id: number;
  dutyName: string;
  onDutyTime?: string;
  offDutyTime?: string;
  offIsNextDay?: boolean;
  modifierName?: string;
  modifierId?: number;
  gmtModified?: number | null;
  /**班次设置信息 */
  dutyProperties?: BackedShiftAttSetting;
};
