import type { ShiftLocales } from './type';

export const zhCN: ShiftLocales = {
  title: '班次',
  addText: '新建班次',
  editText: {
    _self: '编辑',
    title: '编辑班次',
  },
  basicSetting: '班次设置',
  flexibleSetting: '弹性设置',
  searchFields: {
    name: {
      _self: '班次名称',
      placeholder: '请输入班次名称',
    },
  },
  name: {
    _self: '班次名称',
    validateMsg: {
      _self: '班次名称必填',
      maxLen: '最多输入 20 个字符！',
    },
  },
  timeRange: '班次时间',
  offIsNextDay: '次日',
  onDutyTime: {
    _self: '上班时间',
    validateMsg: '上班时间必选',
  },
  isNeedPunchIn: {
    _self: '发起打卡',
    toolTip: '如未设置，遵循默认打卡时间范围：上班时间点的前2小时至后2小时',
  },
  punchInTimeRang: {
    _self: '打卡时间范围',
    tip: {
      divider: '至',
    },
  },
  offDutyTime: {
    _self: '下班时间',
    validateMsg: '下班时间必选',
  },
  isNeedPunchOut: {
    _self: '发起打卡',
    toolTip: '如未设置，遵循默认打卡时间范围：下班时间点的前2小时至后2小时',
  },
  punchOutTimeRange: {
    _self: '打卡时间范围',
    tip: {
      divider: '至',
    },
  },
  isAllowRest: '包含休息时间',
  restTimeRange: {
    _self: '休息时间段',
    tip: {
      _self: '工时统计、请假、外勤，均会扣除包含的休息时长',
      divider: '至',
    },
    validateMsg: '请完整设置休息时间',
  },
  notAllowContinuousWork: { _self: '不允许连续上班', tip: '针对换班和请假顶班生效' },
  notAllowContinuousTime: { _self: '强制休息时长', validteMsg: '强制休息时长必填', unit: '小时' },
  isAllowEarlyToLeaveEarlyOrLateToLeaveLate: {
    _self: '允许晚到晚走/早到早走',
    confirmOk: '开启',
    confirmCancle: '取消',
    confirmText:
      '开启后，已设置的「晚到、早走几分钟不记为异常」将被取消，因为这两种弹性规则，只能选择一种.',
  },
  maxLateWorkMinutes: {
    _self: '上班最多晚到',
    tip: '上班晚到几分钟，下班须晚走几分钟',
  },
  maxEarlyWorkMinutes: {
    _self: '下班最多早走',
    tip: '上班早到几分钟，下班可早走几分钟',
  },
  isAllowLateWorkOrEarlyLeaveAsNornal: {
    _self: '允许晚到/早走不记为异常',
    confirmOk: '开启',
    confirmCancle: '取消',
    confirmText:
      '开启后，已设置的「允许晚到晚走、早到早走」将被取消，因为这两种弹性规则，只能选择一种。',
  },
  canLateWorkMinutes: {
    _self: '上班最多晚到',
    tip: '设置的时间内晚到不算迟到',
  },
  canEarlyWorkMinutes: {
    _self: '下班最多早走',
    tip: '设置的时间内早走不算早退',
  },
  isAllowLateLeaveAndLateWork: '下班晚走，第二天可晚到',
  lateLeaveAndLateWorkRules: {
    _self: '规则',
    addText: '添加晚到晚走',
    lateLeaveHours: '当日下班后晚走',
    lateWorkHours: '次日上班可以晚到',
    hourText: '小时',
    limitMsg: '已经设置了晚走晚到得最大时间，无需再新增时段',
    deleteText: '删除',
  },
  modifiedAt: '修改时间',
  modifyUser: {
    id: '修改人ID',
    name: '修改人',
  },
  action: '操作',
  deleteText: {
    _self: '删除',
    confirm: '您确定要删除',
    againConfirm: '当前班次已被排班使用，请谨慎删除！',
    success: '删除成功',
  },
  leaveRule:
    '非整班请假时，遵循默认打卡时间范围：上班时间点的前2小时至后2小时，下班时间点的前2小时至后2小时',
  punchInStartTime: {
    validateMsg: {
      _self: '上班打卡开始时间必选',
    },
  },
};

export default zhCN;
