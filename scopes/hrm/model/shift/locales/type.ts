export type ShiftLocales = {
  title: string;
  addText: string;
  editText: {
    _self: string;
    title: string;
  };
  basicSetting: string;
  flexibleSetting: string;
  searchFields: {
    name: {
      _self: string;
      placeholder: string;
    };
  };
  name: {
    _self: string;
    validateMsg: {
      _self: string;
      maxLen: string;
    };
  };
  timeRange: string;
  offIsNextDay: string;
  onDutyTime: {
    _self: string;
    validateMsg: string;
  };
  isNeedPunchIn: {
    _self: string;
    toolTip: string;
  };
  punchInTimeRang: {
    _self: string;
    tip: {
      divider: string;
    };
  };
  offDutyTime: {
    _self: string;
    validateMsg: string;
  };
  isNeedPunchOut: {
    _self: string;
    toolTip: string;
  };
  punchOutTimeRange: {
    _self: string;
    tip: {
      divider: string;
    };
  };
  isAllowRest: string;
  restTimeRange: {
    _self: string;
    tip: {
      _self: string;
      divider: string;
    };
    validateMsg: string;
  };
  notAllowContinuousWork: {
    _self: string;
    tip: string;
  };
  notAllowContinuousTime: {
    _self: string;
    validteMsg: string;
    unit: string;
  };
  isAllowEarlyToLeaveEarlyOrLateToLeaveLate: {
    _self: string;
    confirmOk: string;
    confirmCancle: string;
    confirmText: string;
  };
  maxLateWorkMinutes: {
    _self: string;
    tip: string;
  };
  maxEarlyWorkMinutes: {
    _self: string;
    tip: string;
  };
  isAllowLateWorkOrEarlyLeaveAsNornal: {
    _self: string;
    confirmOk: string;
    confirmCancle: string;
    confirmText: string;
  };
  canLateWorkMinutes: {
    _self: string;
    tip: string;
  };
  canEarlyWorkMinutes: {
    _self: string;
    tip: string;
  };
  isAllowLateLeaveAndLateWork: string;
  lateLeaveAndLateWorkRules: {
    _self: string;
    addText: string;
    lateLeaveHours: string;
    lateWorkHours: string;
    hourText: string;
    limitMsg: string;
    deleteText: string;
  };
  modifiedAt: string;
  modifyUser: {
    id: string;
    name: string;
  };
  action: string;
  deleteText: {
    _self: string;
    confirm: string;
    againConfirm: string;
    success: string;
  };
  leaveRule: string;
  punchInStartTime: {
    validateMsg: {
      _self: string;
    };
  };
};
