import type { DailyPerformanceGradeLocales } from './type.js';

export const zhCN: DailyPerformanceGradeLocales = {
  gradedUser: {
    __self: '人员',
    id: '人员ID',
    name: '人员姓名',
    region: '区域',
    idc: '所属机房',
    superiors: '直线经理',
    block: '所属楼栋',
    jobNo: '员工工号',
  },
  position: '考核岗位',
  type: '指标分类',
  subType: '指标子类',
  name: '指标名称',
  measurements: '衡量标准',
  gradeDesc: '评分描述',
  grade: '考评分',
  occurTime: '发生日期',
  createdBy: {
    __self: '提交人',
    id: '提交人ID',
    name: '提交人姓名',
  },
  modifiedBy: {
    __self: '更新人',
    id: '更新人ID',
    name: '更新人姓名',
  },
  createdAt: '提交时间',
  modifiedAt: '更新时间',
  instStatus: {
    __self: '状态',
    enum: {
      APPROVING: '审批中',
      PASS: '生效',
      UN_PASS: '拒绝',
    },
  },
};

export default zhCN;
