export type DailyPerformanceGradeLocales = {
  gradedUser: {
    __self: string;
    id: string;
    name: string;
    region: string;
    idc: string;
    superiors: string;
    block: string;
    jobNo: string;
  };
  position: string;
  type: string;
  subType: string;
  name: string;
  measurements: string;
  gradeDesc: string;
  grade: string;
  occurTime: string;
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };
  modifiedBy: {
    __self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  modifiedAt: string;
  instStatus: {
    __self: string;
    enum: Record<string, string>;
  };
};
