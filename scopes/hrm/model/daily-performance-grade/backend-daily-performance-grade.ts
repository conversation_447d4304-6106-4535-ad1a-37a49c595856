import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';

export type SimpleUser = {
  id: number;
  name: string;
};

export type BackendDailyPerformanceGrade = {
  id: number;
  staff: SimpleUser;
  /**
   * 用户所属机房
   */
  resource: {
    value: string;
    label: string;
  } | null;
  /**
   * 所属机房对应区域
   */
  region: {
    value: string;
    label: string;
  } | null;

  /**
   * 所属楼栋
   */
  block: {
    value: string;
    label: string;
  } | null;
  /**
   * 一级主管用户集合
   */
  superiors: SimpleUser[];
  /**
   * 用户对应的考核岗位
   */
  evalPosition: {
    value: string;
    label: string;
  } | null;

  type: BackendAnnualPerformanceObjectiveType;
  subType: BackendAnnualPerformanceObjectiveSubType;
  /**
   * 关联的指标ID
   */
  kpiId: number;
  /**
   * 指标名称
   */
  name: string;

  /**
   * 衡量标准
   */
  metrics: string;
  /**
   * 评分标准
   */
  gradeCriteria: {
    id: number;
    criteria: string;
    defaultGrade: number;
  }[];
  /**
   * 评分描述
   */
  content: string;
  /**
   * 发生日期
   */
  occurTime: number;
  /**
   * 考评分
   */
  grade: number;
  /**
   * 记录是否可编辑/删除
   */
  canModified: boolean;
  createUser: SimpleUser;
  modifiedUser: SimpleUser | null;
  gmtCreate: number;
  gmtModified: number | null;
  files: BackendMcUploadFile[] | null;
  jobNo: string;
  /**
   * 审批状态
   *  - APPROVING: 审批中
   *  - PASS: 生效
   *  - UN_PASS: 拒绝
   *
   */
  instStatus: string | null;
  /**
   * 审批实例id
   */
  instId: string | null;
  /**
   * 是否需要审批
   */
  needApproval: boolean;
  /**
   * 审批节点id
   */
  taskId: string | null;
};
