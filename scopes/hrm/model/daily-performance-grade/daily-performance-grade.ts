import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';

import type {
  BackendDailyPerformanceGrade,
  SimpleUser,
} from './backend-daily-performance-grade.js';
import { getDailyPerformanceGradeLocales } from './locales/index.js';
import type { DailyPerformanceGradeLocales } from './locales/index.js';

export type GradedUser = SimpleUser & {
  region: {
    value: string;
    label: string;
  } | null;
  idc: {
    value: string;
    label: string;
  } | null;
  block: {
    value: string;
    label: string;
  } | null;
  superiors: SimpleUser[];
  jobNo: string;
};

export type DailyPerformanceGradeJSON = {
  id: number;
  user: GradedUser;
  performancePosition: { label: string; value: string } | null;
  relatedObjectiveId: number;
  name: string;
  type: BackendAnnualPerformanceObjectiveType;
  subType: BackendAnnualPerformanceObjectiveSubType;
  measurements: string;
  gradeDesc: string;
  grade: number;
  occurTime: number;
  createdBy: SimpleUser;
  modifiedBy: SimpleUser | null;
  canModified: boolean;
  createdAt: number;
  modifiedAt: number | null;
  attachments: McUploadFileJSON[] | null;
  gradeCriteria: {
    id: number;
    criteria: string;
    defaultGrade: number;
  }[];
  instStatus: string | null;
  instId: string | null;
  taskId: string | null;
  needApproval: boolean;
};

export class DailyPerformanceGrade {
  static fromApiObject(object: BackendDailyPerformanceGrade) {
    const copy = cloneDeep(object);

    return new DailyPerformanceGrade(
      copy.id,
      {
        id: copy.staff.id,
        name: copy.staff.name,
        region: copy.region,
        idc: copy.resource,
        superiors: copy.superiors,
        block: copy.block,
        jobNo: copy.jobNo,
      },
      copy.evalPosition,
      copy.kpiId,
      copy.name,
      copy.type,
      copy.subType,
      copy.metrics,
      copy.content,
      copy.grade,
      copy.occurTime,
      copy.canModified,
      copy.createUser,
      copy.modifiedUser,
      dayjs(copy.gmtCreate).valueOf(),
      dayjs(copy.gmtModified).valueOf(),
      copy.files?.map(file => McUploadFile.fromApiObject(file).toJSON()) ?? null,
      copy.gradeCriteria,
      copy.instStatus,
      copy.instId,
      copy.taskId,
      copy.needApproval
    );
  }

  static fromJSON(json: DailyPerformanceGradeJSON) {
    const copy = cloneDeep(json);

    return new DailyPerformanceGrade(
      copy.id,
      copy.user,
      copy.performancePosition,
      copy.relatedObjectiveId,
      copy.name,
      copy.type,
      copy.subType,
      copy.measurements,
      copy.gradeDesc,
      copy.grade,
      copy.occurTime,
      copy.canModified,
      copy.createdBy,
      copy.modifiedBy,
      copy.createdAt,
      copy.modifiedAt,
      copy.attachments,
      copy.gradeCriteria,
      copy.instStatus,
      copy.instId,
      copy.taskId,
      copy.needApproval
    );
  }

  private _localeCode: string = 'zh-CN';

  private _locales: DailyPerformanceGradeLocales;

  constructor(
    public id: number,
    public user: GradedUser,
    public performancePosition: { label: string; value: string } | null,
    public relatedObjectiveId: number,
    public name: string,
    public type: BackendAnnualPerformanceObjectiveType,
    public subType: BackendAnnualPerformanceObjectiveSubType,
    public measurements: string,
    public gradeDesc: string,
    public grade: number,
    public occurTime: number,
    public canModified: boolean,
    public createdBy: SimpleUser,
    public modifiedBy: SimpleUser | null,
    public createdAt: number,
    public modifiedAt: number | null,
    public attachments: McUploadFileJSON[] | null,
    public gradeCriteria: {
      id: number;
      criteria: string;
      defaultGrade: number;
    }[],
    public instStatus: string | null,
    public instId: string | null,
    public taskId: string | null,
    public needApproval: boolean
  ) {
    this._locales = getDailyPerformanceGradeLocales(this._localeCode);
  }

  public set locales(locales: DailyPerformanceGradeLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendDailyPerformanceGrade {
    return cloneDeep({
      id: this.id,
      jobNo: this.user.jobNo,
      staff: {
        id: this.user.id,
        name: this.user.name,
      },
      kpiId: this.relatedObjectiveId,
      evalPosition: this.performancePosition,
      resource: this.user.idc,
      region: this.user.region,
      block: this.user.block,
      superiors: this.user.superiors,
      type: this.type,
      subType: this.subType,
      name: this.name,
      metrics: this.measurements,
      content: this.gradeDesc,
      grade: this.grade,
      occurTime: this.occurTime,
      createUser: this.createdBy,
      gmtCreate: this.createdAt,
      modifiedUser: this.modifiedBy,
      gmtModified: this.modifiedAt,
      canModified: this.canModified,
      files:
        this.attachments?.map(attachment => McUploadFile.fromJSON(attachment).toApiObject()) ??
        null,
      gradeCriteria: this.gradeCriteria,
      instStatus: this.instStatus,
      instId: this.instId,
      taskId: this.taskId,
      needApproval: this.needApproval,
    });
  }

  public toJSON(): DailyPerformanceGradeJSON {
    return cloneDeep({
      id: this.id,
      user: this.user,
      performancePosition: this.performancePosition,
      relatedObjectiveId: this.relatedObjectiveId,
      type: this.type,
      name: this.name,
      subType: this.subType,
      measurements: this.measurements,
      gradeDesc: this.gradeDesc,
      grade: this.grade,
      occurTime: this.occurTime,
      createdBy: this.createdBy,
      modifiedBy: this.modifiedBy,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      canModified: this.canModified,
      attachments: this.attachments,
      gradeCriteria: this.gradeCriteria,
      instStatus: this.instStatus,
      instId: this.instId,
      taskId: this.taskId,
      needApproval: this.needApproval,
    });
  }
}
