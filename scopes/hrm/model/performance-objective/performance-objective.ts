import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type {
  BackendObjectiveStatus,
  BackendPerformanceObjective,
  PerformanceObjectiveType,
} from './backend-performance-objective.js';
import { getPerformanceObjectiveLocales } from './locales/index.js';
import type { PerformanceObjectiveLocales } from './locales/index.js';

export type PerformanceObjectiveJSON = {
  id: number;
  type: PerformanceObjectiveType;
  title: string;
  /**
   * @example
   * ```ts
   * const percent = 9; // 9%
   * ```
   */
  percent: number;
  content: string;
  measurements: string | null;
  status: BackendObjectiveStatus | null;
  startedAt: number | null;
  finishedAt: number | null;
  selfEvaluation: string | null;
  grade: number | null;
  gradeDescriptions: string | null;
  createdAt: number;
  modifiedAt: number;
};

export class PerformanceObjective {
  static fromApiObject(object: BackendPerformanceObjective) {
    const copy = cloneDeep(object);

    return new PerformanceObjective(
      copy.id,
      copy.sectionType,
      copy.title,
      copy.radio,
      copy.content,
      copy.metrics,
      copy.sectionStatus,
      copy.startTime === null ? null : dayjs(copy.startTime).valueOf(),
      copy.finishTime === null ? null : dayjs(copy.finishTime).valueOf(),
      copy.selfEvaluation,
      copy.grade,
      copy.gradeDescriptions,
      copy.gmtCreate,
      copy.gmtModified ?? copy.gmtCreate
    );
  }

  static fromJSON(json: PerformanceObjectiveJSON) {
    const copy = cloneDeep(json);

    return new PerformanceObjective(
      copy.id,
      copy.type,
      copy.title,
      copy.percent,
      copy.content,
      copy.measurements,
      copy.status,
      copy.startedAt,
      copy.finishedAt,
      copy.selfEvaluation,
      copy.grade,
      copy.gradeDescriptions,
      copy.createdAt,
      copy.modifiedAt
    );
  }

  private _localeCode: string = 'zh-CN';

  private _locales: PerformanceObjectiveLocales;

  constructor(
    public id: number,
    public type: PerformanceObjectiveType,
    public title: string,
    public percent: number,
    public content: string,
    public measurements: string | null,
    public status: BackendObjectiveStatus | null,
    public startedAt: number | null,
    public finishedAt: number | null,
    public selfEvaluation: string | null,
    public grade: number | null,
    public gradeDescriptions: string | null,
    public createdAt: number,
    public modifiedAt: number
  ) {
    this._locales = getPerformanceObjectiveLocales(this._localeCode);
  }

  public set locales(locales: PerformanceObjectiveLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedStartedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.startedAt).format(template);
  }

  public getFormattedFinishedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.finishedAt).format(template);
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendPerformanceObjective {
    return cloneDeep({
      id: this.id,
      sectionType: this.type,
      title: this.title,
      radio: this.percent,
      content: this.content,
      metrics: this.measurements,
      sectionStatus: this.status,
      startTime: this.startedAt,
      finishTime: this.finishedAt,
      selfEvaluation: this.selfEvaluation,
      grade: this.grade,
      gradeDescriptions: this.gradeDescriptions,
      gmtCreate: this.createdAt,
      gmtModified: this.modifiedAt,
    });
  }

  public toJSON(): PerformanceObjectiveJSON {
    return cloneDeep({
      id: this.id,
      type: this.type,
      title: this.title,
      percent: this.percent,
      content: this.content,
      measurements: this.measurements,
      status: this.status,
      startedAt: this.startedAt,
      finishedAt: this.finishedAt,
      selfEvaluation: this.selfEvaluation,
      grade: this.grade,
      gradeDescriptions: this.gradeDescriptions,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
    });
  }
}
