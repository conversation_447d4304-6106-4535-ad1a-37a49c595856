import type { BackendAnnualPerformanceObjectiveSubType } from '@manyun/hrm.model.annual-performance-objective';

export type BackendObjectiveType = /* 业务 */ 'BIZ' | /* 价值观 */ 'VALUES';
export type PerformanceObjectiveType =
  | BackendObjectiveType
  | BackendAnnualPerformanceObjectiveSubType;

export type BackendObjectiveStatus =
  | /**未开始**/ 'NOT_START'
  | /**进行中*/ 'PROCESSING'
  | /**已完成 */ 'FINISHED';
export type BackendGrade = 1 | 2 | 3 | 3.5 | 4 | 5;

export type BackendPerformanceObjective = {
  id: number;
  sectionType: PerformanceObjectiveType;
  title: string;
  /**
   * 占比
   *
   * @example
   * ```ts
   * const ratio = 9; // 9%
   * ```
   */
  radio: number;
  content: string;
  /**
   * 考核标准
   */
  metrics: string | null;
  sectionStatus: BackendObjectiveStatus | null;
  startTime: number | null;
  finishTime: number | null;
  selfEvaluation: string | null;
  grade: number | null;
  gradeDescriptions: string | null;
  gmtCreate: number;
  gmtModified: number | null;
};
