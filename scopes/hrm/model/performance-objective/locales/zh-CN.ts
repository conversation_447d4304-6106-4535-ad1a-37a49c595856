import {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_SECOND_VERSION,
} from '@manyun/hrm.util.performances';

import type { PerformanceObjectiveLocales } from './type.js';

export const zhCN: PerformanceObjectiveLocales = {
  title: '目标名称',
  percent: '权重',
  content: '目标说明',
  measurements: '衡量标准',
  status: {
    _self: '状态',
    enum: {
      NOT_START: '未开始',
      PROCESSING: '进行中',
      FINISHED: '已完成',
    },
  },
  startedAt: '开始日期',
  finishedAt: '目标完成日期',
  selfEvaluation: '员工自评',
  grade: {
    _self: '经理评级',
    enum: {
      1: '1分 不合格',
      2: '2分 不符合预期',
      3: '3分 满意',
      4: '4分 优秀',
      5: '5分 卓越',
    },
    annual: {
      [PERFORMANCE_BASIC_VERSION]: {
        1: '1分 不合格',
        2: '2分 不符合预期',
        3: '3分 满意',
        4: '4分 优秀',
        5: '5分 卓越',
      },
      [PERFORMANCE_SECOND_VERSION]: {
        1: '1分 不合格',
        2: '2分 不符合预期',
        3: '3分 满意',
        3.5: '3.5分 满意',
        4: '4分 优秀',
        5: '5分 卓越',
      },
    },
  },
  createdBy: {
    _self: '创建人',
    id: 'ID',
    name: '名称',
  },
  createdAt: '创建时间',
  modifiedAt: '更新时间',
};

export default zhCN;
