import type {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_SECOND_VERSION,
} from '@manyun/hrm.util.performances';

import type { BackendObjectiveStatus } from '../backend-performance-objective.js';

export type PerformanceObjectiveLocales = {
  title: string;
  percent: string;
  content: string;
  measurements: string;
  status: {
    _self: string;
    enum: Record<BackendObjectiveStatus, string>;
  };
  startedAt: string;
  finishedAt: string;
  selfEvaluation: string;
  grade: {
    _self: string;
    enum: Record<number, string>;
    // 年度绩效目标
    annual: {
      [PERFORMANCE_BASIC_VERSION]: Record<number, string>;
      [PERFORMANCE_SECOND_VERSION]: Record<number, string>;
    };
  };
  createdBy: {
    _self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  modifiedAt: string;
};
