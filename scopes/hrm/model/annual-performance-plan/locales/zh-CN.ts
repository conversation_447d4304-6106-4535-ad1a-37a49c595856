import type { AnnualPerformancePlanLocales } from './type.js';

export const zhCN: AnnualPerformancePlanLocales = {
  name: '考核计划名称',
  year: '考核年度',
  splitRule: {
    __self: '考核方式',
    enum: {
      QUARTER: '按季度',
      // HALF_YEAR: '按半年度',
      // YEAR: '按年度',
    },
  },
  splitRuleDesc: {
    QUARTER: '选择按季度考核，则会生成4个自然季度考核任务+1个年度考核任务',
    // HALF_YEAR: '选择按半年度考核，则会生成2个半年度考核任务+1个年度考核任务',
    // YEAR: '选择按年度考核，则只会生成1个年度考核任务',
  },
  notEvalHiredDateLaterThanDate: '无需参与年度考核人员',
  notEvalHiredDateLaterThanDateByDaily: '无需参与季度考核人员',
  beforeSubTaskDeadlineNotifyDays: '考核自评提醒',
  canSetResultDate: '考核结果填写',
  subTasks: '子考核任务',
  resourcesScope: '考核范围',
  positionScope: '考核岗位',
  content: '内容',
  createdBy: {
    __self: '发起人',
    id: '创建人 ID',
    name: '创建人姓名',
  },
  createdAt: '考核发起时间',
  modifiedAt: '更新时间',

  performancePeriod: {
    enum: {
      Q1: 'Q1',
      Q2: 'Q2',
      Q3: 'Q3',
      Q4: 'Q4',
      // FIR_HALF_YEAR: '上半年',
      // SEC_HALF_YEAR: '下半年',
      YEAR: '年度',
    },
  },
  performancePeriodDesc: {
    enum: {
      Q1: 'Q1代表第一季度',
      Q2: 'Q2代表第二季度',
      Q3: 'Q3代表第三季度',
      Q4: 'Q4代表第四季度',
      // SEC_HALF_YEAR: '上半年',
      // FIR_HALF_YEAR: '下半年',
      YEAR: '年度',
    },
  },
  confirmDateRange: '目标确认期限',
};

export default zhCN;
