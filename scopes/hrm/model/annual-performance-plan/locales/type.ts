import type {
  BackendAnnualPerformancePlanSplitRule,
  BackendPerformancePeriod,
} from '../backend-annual-performance-plan.js';

export type AnnualPerformancePlanLocales = {
  name: string;
  year: string;
  splitRule: {
    __self: string;
    enum: Record<BackendAnnualPerformancePlanSplitRule, string>;
  };
  splitRuleDesc: Record<BackendAnnualPerformancePlanSplitRule, string>;
  resourcesScope: string;
  positionScope: string;
  notEvalHiredDateLaterThanDate: string;
  notEvalHiredDateLaterThanDateByDaily: string;
  beforeSubTaskDeadlineNotifyDays: string;
  subTasks: string;
  canSetResultDate: string;
  content: string;
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  modifiedAt: string;
  performancePeriod: {
    enum: Record<BackendPerformancePeriod, string>;
  };
  performancePeriodDesc: {
    enum: Record<BackendPerformancePeriod, string>;
  };
  confirmDateRange: string;
};
