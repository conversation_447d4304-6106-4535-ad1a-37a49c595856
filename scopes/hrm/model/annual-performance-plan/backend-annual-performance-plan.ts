export type SimpleUser = {
  id: number;
  name: string;
};

/**
 * 拆分规则
 *  - `YEAR`: 不拆分
 *  - `QUARTER`: 按季度
 *  - `HALF_YEAR`: 按半年度
 */
export type BackendAnnualPerformancePlanSplitRule = 'QUARTER'; //'YEAR' 'HALF_YEAR'

export type BackendAnnualPerformancePlanTaskType = 'TARGET' | 'EVAL';

export type BackendAnnualPerformancePlanTask = {
  title: string;
  period: BackendPerformancePeriod;
  /**
   * 任务自评开始时间
   */
  startTime: number;
  /**
   * 任务自评结束时间
   */
  endTime: number;
  /**
   * 任务周期开始时间
   */
  ruleStartTime: number;
  /**
   * 任务周期结束时间
   */
  ruleEndTime: number;
};

export type BackendPerformancePeriod =
  | 'Q1'
  | 'Q2'
  | 'Q3'
  | 'Q4'
  // | 'FIR_HALF_YEAR'
  // | 'SEC_HALF_YEAR'
  | 'YEAR';

export type BackendAnnualPerformancePlan = {
  id: number;
  /**
   * 考核年份
   *  @example
   * ```ts
   * const year = 2023
   * ```
   */
  year: string;
  /**
   * 考核范围
   */
  resources: { label: string; value: string; parentCode: string }[];
  /**
   * 考核计划名称
   */
  name: string;
  /**
   * 考核岗位
   */
  evalPositions: { label: string; value: string }[];
  /**
   * 拆分规则
   */
  splitRule: BackendAnnualPerformancePlanSplitRule;
  /**
   * 子任务拆分
   */
  tasks: BackendAnnualPerformancePlanTask[] | null;
  /**
   * 无须考核的入职时间
   */
  needNotEvalHiredDate: number | null;
  /**
   *距子任务自评截止日期提醒天数
   */
  evalSetExpireNotifyDays: number[];
  /**
   *考核结果设置开始时间
   */
  resultSetTime: number;
  /**
   * 创建人
   */
  createUser: SimpleUser;
  /**
   * 修改人
   */
  // modifyUser: SimpleUser;
  /**
   * 考核发起时间
   */
  gmtCreate: number;
  gmtModified: number | null;
  /**
   * 是否可更新计划
   */
  canUpdate: boolean;
};
