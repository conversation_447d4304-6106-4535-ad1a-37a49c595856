import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type {
  BackendAnnualPerformancePlan,
  BackendAnnualPerformancePlanSplitRule,
  BackendPerformancePeriod,
  SimpleUser,
} from './backend-annual-performance-plan.js';
import type { AnnualPerformancePlanLocales } from './locales/index.js';
import { getAnnualPerformancePlanLocales } from './locales/index.js';

export type AnnualPerformancePlanConfig = {
  title: string;
  period: BackendPerformancePeriod;
  /**
   * 自评起至时间
   */
  evalStartTimeRange: [number, number];

  /**
   * 周期起至时间
   */
  natureStartTimeRange: [number, number];
};

export type AnnualPerformancePlanJSON = {
  id: number;
  year: string;
  name: string;
  resourcesScope: { label: string; value: string; parentCode: string }[];
  positionScope: { label: string; value: string }[];
  splitRule: BackendAnnualPerformancePlanSplitRule;
  subConfigs: AnnualPerformancePlanConfig[];
  beforeSubTaskDeadlineNotifyDays: number[];
  canSetResultDate: number;
  createdBy: SimpleUser;
  // modifiedBy: SimpleUser;
  createdAt: number;
  modifiedAt: number | null;
  notEvalHiredDateLaterThanDate: number | null;
  canModified: boolean;
};

export class AnnualPerformancePlan {
  static fromApiObject(object: BackendAnnualPerformancePlan) {
    const copy = cloneDeep(object);

    return new AnnualPerformancePlan(
      copy.id,
      copy.year,
      copy.name,
      copy.resources,
      copy.evalPositions,
      copy.splitRule,
      (copy.tasks ?? []).map(task => ({
        title: task.title,
        period: task.period,
        evalStartTimeRange: [task.startTime, task.endTime],
        natureStartTimeRange: [task.ruleStartTime, task.ruleEndTime],
      })),
      copy.evalSetExpireNotifyDays,
      copy.resultSetTime,
      copy.createUser,
      // copy.modifyUser,
      dayjs(copy.gmtCreate).valueOf(),
      dayjs(copy.gmtModified).valueOf(),
      copy.needNotEvalHiredDate,
      copy.canUpdate
    );
  }

  static fromJSON(json: AnnualPerformancePlanJSON) {
    const copy = cloneDeep(json);

    return new AnnualPerformancePlan(
      copy.id,
      copy.year,
      copy.name,
      copy.resourcesScope,
      copy.positionScope,
      copy.splitRule,
      copy.subConfigs,
      copy.beforeSubTaskDeadlineNotifyDays,
      copy.canSetResultDate,
      copy.createdBy,
      // copy.modifiedBy,
      copy.createdAt,
      copy.modifiedAt,
      copy.notEvalHiredDateLaterThanDate,
      copy.canModified
    );
  }

  static getSubConfigs(splitRule: BackendAnnualPerformancePlanSplitRule, year: number) {
    let configs: AnnualPerformancePlanConfig[] = [];
    const generateDefaultConfig = (
      period: BackendPerformancePeriod,
      multiple: number,
      index: number
    ) => {
      const natureStartTimeRange: [number, number] = [
        dayjs()
          .set('year', year)
          .set('month', index * multiple)
          .startOf('month')
          .valueOf(),
        dayjs()
          .set('year', year)
          .set('month', (index + 1) * multiple - 1)
          .endOf('month')
          .valueOf(),
      ];
      const natureEndTimeNextMonth: dayjs.Dayjs = dayjs(natureStartTimeRange[1]).add(1, 'month');
      return {
        title: period,
        period: period,
        evalStartTimeRange: [
          natureEndTimeNextMonth.month() === 9
            ? natureEndTimeNextMonth.set('date', 8).valueOf()
            : natureEndTimeNextMonth.startOf('month').valueOf(),
          natureEndTimeNextMonth.endOf('month').valueOf(),
        ],
        natureStartTimeRange,
      } as AnnualPerformancePlanConfig;
    };
    /**
     * 季度配置
     */
    if (splitRule === 'QUARTER' || splitRule === 'HALF_YEAR') {
      const dailyConfigGroupMapper: Record<
        'QUARTER' | 'HALF_YEAR',
        {
          periods: string[];
          multiple: number;
        }
      > = {
        QUARTER: {
          periods: ['Q1', 'Q2', 'Q3', 'Q4'],
          multiple: 3,
        },
        HALF_YEAR: {
          periods: ['FIR_HALF_YEAR', 'SEC_HALF_YEAR'],
          multiple: 6,
        },
      };
      const dailyConfig = dailyConfigGroupMapper[splitRule];
      configs = dailyConfig.periods.map((period, index) => {
        return generateDefaultConfig(
          period as BackendPerformancePeriod,
          dailyConfig.multiple,
          index
        );
      });
    }
    /**
     * 年度配置
     */

    configs.push(generateDefaultConfig('YEAR', 12, 0));

    return configs;
  }

  private _localeCode: string = 'zh-CN';

  private _locales: AnnualPerformancePlanLocales;

  constructor(
    public id: number,
    public year: string,
    public name: string,
    public resourcesScope: { label: string; value: string; parentCode: string }[],
    public positionScope: { label: string; value: string }[],
    public splitRule: BackendAnnualPerformancePlanSplitRule,
    public subConfigs: AnnualPerformancePlanConfig[],
    public beforeSubTaskDeadlineNotifyDays: number[],
    public canSetResultDate: number,
    public createdBy: SimpleUser,
    // public modifiedBy: SimpleUser,
    public createdAt: number,
    public modifiedAt: number | null,
    public notEvalHiredDateLaterThanDate: number | null,
    public canModified: boolean
  ) {
    this._locales = getAnnualPerformancePlanLocales(this._localeCode);
  }

  public set locales(locales: AnnualPerformancePlanLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendAnnualPerformancePlan {
    return cloneDeep({
      id: this.id,
      year: this.year,
      name: this.name,
      resources: this.resourcesScope,
      evalPositions: this.positionScope,
      needNotEvalHiredDate: this.notEvalHiredDateLaterThanDate,
      splitRule: this.splitRule,
      tasks: this.subConfigs.map(config => ({
        title: config.title,
        period: config.period,
        startTime: config.evalStartTimeRange[0],
        endTime: config.evalStartTimeRange[1],
        ruleStartTime: config.natureStartTimeRange[0],
        ruleEndTime: config.natureStartTimeRange[1],
      })),
      evalSetExpireNotifyDays: this.beforeSubTaskDeadlineNotifyDays,
      resultSetTime: this.canSetResultDate,
      createUser: this.createdBy,
      // modifyUser: this.modifiedBy,
      gmtCreate: this.createdAt,
      gmtModified: this.modifiedAt,
      canUpdate: this.canModified,
    });
  }

  public toJSON(): AnnualPerformancePlanJSON {
    return cloneDeep({
      id: this.id,
      year: this.year,
      name: this.name,
      resourcesScope: this.resourcesScope,
      positionScope: this.positionScope,
      splitRule: this.splitRule,
      subConfigs: this.subConfigs,
      notEvalHiredDateLaterThanDate: this.notEvalHiredDateLaterThanDate,
      beforeSubTaskDeadlineNotifyDays: this.beforeSubTaskDeadlineNotifyDays,
      canSetResultDate: this.canSetResultDate,
      createdBy: this.createdBy,
      // modifiedBy: this.modifiedBy,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      canModified: this.canModified,
    });
  }
}
