/**
 * 指标归类
 *  - `STANDARD`: 日常指标
 *  - `CUSTOM`: 制定指标
 */
export type BackendAnnualPerformanceObjectiveWay = 'STANDARD' | 'CUSTOM';

/**
 * 指标大类
 *  - `DAILY`: 季度扣分指标
 *  - `YEAR`: 年度指标
 *  - `RED_LINE`: 红线指标
 *  - `DAY_PLUS`:季度加分指标
 */
export type BackendAnnualPerformanceObjectiveType = 'DAILY' | 'YEAR' | 'RED_LINE' | 'DAY_PLUS';

/**
 *  指标子类
 *   - `BIZ`: 关键业务指标
 *   - `DEVELOP`: 拓展提升目标
 */
export type BackendAnnualPerformanceObjectiveSubType = 'BIZ';

export type SimpleUser = {
  id: number;
  name: string;
};

export type MetricsModel = {
  /**
   * 评分标准id
   */
  id: number;
  kpiId: number;
  /**
   * 衡量标准
   */
  context: string;
  /**
   * 评分标准内容
   */
  gradeCriteria: string;
  /**
   * 评分标准分数
   */
  defaultGrade: number[];
  /**
   * 评分标准上限
   */
  ceiling?: number;
};

export type BackendAnnualPerformanceObjective = {
  id: number;
  name: string;
  resources:
    | {
        value: string;
        label: string;
      }[]
    | null;
  evalPositions: { value: string; label: string }[];
  kpiWay: BackendAnnualPerformanceObjectiveWay;
  type: BackendAnnualPerformanceObjectiveType;
  subType: BackendAnnualPerformanceObjectiveSubType;
  metricsModels: MetricsModel[];
  metrics: string[];
  /**
   * @deprecated
   */
  gradeCriteria: string;
  createUser: SimpleUser;
  modifierUser: SimpleUser;
  gmtCreate: number;
  gmtModified: number | null;
  reason: string | null;
  kpiStatus: string;
  processNo: string;
};
