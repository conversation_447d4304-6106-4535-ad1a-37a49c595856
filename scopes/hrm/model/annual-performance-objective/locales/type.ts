import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
  BackendAnnualPerformanceObjectiveWay,
} from '../index.js';

export type AnnualPerformanceObjectiveLocales = {
  title: string;
  name: string;
  region: string;
  resources: string;
  evalPositions: string;
  way: {
    __self: string;
    enum: Record<BackendAnnualPerformanceObjectiveWay, string>;
  };
  type: {
    __self: string;
    enum: Partial<Record<BackendAnnualPerformanceObjectiveType, string>>;
    textMapper: Record<string, Partial<Record<BackendAnnualPerformanceObjectiveType, string>>>;
  };
  subType: {
    __self: string;
    enum: Record<BackendAnnualPerformanceObjectiveSubType, string>;
  };
  measurements: string;
  gradeCriteria: string;
  createdBy: {
    __self: string;
    id: string;
    name: string;
  };
  modifiedBy: {
    __self: string;
    id: string;
    name: string;
  };
  createdAt: string;
  modifiedAt: string;
  reason: string;
  status: string;
};
