import {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_SECOND_VERSION,
} from '@manyun/hrm.util.performances';

import type { AnnualPerformanceObjectiveLocales } from './type.js';

export const zhCN: AnnualPerformanceObjectiveLocales = {
  title: '指标库',
  resources: '适用范围',
  region: '区域',
  evalPositions: '考核岗位',
  way: {
    __self: '指标所属方式',
    enum: {
      STANDARD: '标准指标',
      CUSTOM: '定制指标',
    },
  },
  type: {
    __self: '指标分类',
    enum: {
      DAILY: '季度考核指标',
      YEAR: '年度加分项',
      RED_LINE: '红线指标',
    },
    textMapper: {
      [PERFORMANCE_BASIC_VERSION]: {
        DAILY: '季度考核指标',
        YEAR: '年度加分项',
        RED_LINE: '红线指标',
      },
      [PERFORMANCE_SECOND_VERSION]: {
        DAILY: '季度扣分指标',
        DAY_PLUS: '季度加分指标',
        RED_LINE: '红线指标',
      },
    },
  },
  subType: {
    __self: '指标子类',
    enum: {
      BIZ: '关键业务目标',
    },
  },
  name: '指标名称',
  measurements: '衡量标准',
  gradeCriteria: '评分标准',
  createdBy: {
    __self: '创建人',
    id: '创建人ID',
    name: '创建人姓名',
  },
  modifiedBy: {
    __self: '更新人',
    id: '更新人ID',
    name: '更新人姓名',
  },
  createdAt: '创建时间',
  modifiedAt: '更新时间',
  reason: '添加原因',
  status: '审批状态',
};

export default zhCN;
