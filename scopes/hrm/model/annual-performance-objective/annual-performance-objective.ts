import md5 from 'crypto-js/md5.js';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';
import groupby from 'lodash.groupby';

import type {
  BackendAnnualPerformanceObjective,
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
  BackendAnnualPerformanceObjectiveWay,
  MetricsModel,
  SimpleUser,
} from './backend-annual-performance-objective.js';
import { getAnnualPerformanceObjectiveLocales } from './locales/index.js';
import type { AnnualPerformanceObjectiveLocales } from './locales/index.js';

type LocaleCode = 'zh-CN';

export type AnnualPerformanceObjectiveJSON = {
  rowKey: string;
  id: number;
  name: string;
  resources:
    | {
        value: string;
        label: string;
      }[]
    | null;
  positions: { value: string; label: string }[];
  way: BackendAnnualPerformanceObjectiveWay;
  type: BackendAnnualPerformanceObjectiveType;
  subType: BackendAnnualPerformanceObjectiveSubType;
  measurements: MetricsModel[];
  /**
   * 历史衡量指标
   */
  measurementsLegacy: string[];
  gradeCriteria: string;
  createdBy: SimpleUser;
  modifiedBy: SimpleUser;
  createdAt: number;
  modifiedAt: number | null;
  reason: string | null;
  status: string;
  processInstanceCode: string;
};

export type MergedMetricAnnualPerformanceObjectiveJSON = AnnualPerformanceObjectiveJSON & {
  index?: number;
  mergedMetrics?: {
    /**
     * 衡量标准内容
     */
    context: string | null;
    gradeCriteria: MetricsModel[];
    /**
     * 历史衡量标准内容
     */
    legacyContexts?: string[];
  };
  /**
   * 合并列
   */
  rowSpan?: number;
};

export class AnnualPerformanceObjective {
  static generateRowKey(object: BackendAnnualPerformanceObjective) {
    return md5(JSON.stringify(object)).toString();
  }

  /**
   *
   * @param data 以评分标准为最小粒度的指标项
   * @returns 合并评分标准，进行合并行的指标项
   */
  static generateMergedMetricAnnualPerformanceObjectives(
    data: AnnualPerformanceObjectiveJSON[]
  ): MergedMetricAnnualPerformanceObjectiveJSON[] {
    const transformedData: MergedMetricAnnualPerformanceObjectiveJSON[] = [];
    data.forEach((item, index) => {
      if (item.measurements) {
        const mergedMeasurements = groupby(item.measurements, 'context');
        Object.keys(mergedMeasurements).forEach((measurement, idx) => {
          transformedData.push({
            ...item,
            index: index + 1,
            //生成Table 唯一键
            rowKey: `${item.rowKey}_${item.id}_${measurement}_${idx}`,
            mergedMetrics: {
              context: measurement,
              gradeCriteria: mergedMeasurements[measurement],
            },
            //判断是否已经存在id一样的数据项,存在则rowSpan设置为0
            rowSpan: transformedData.find(_item => _item.id === item.id)
              ? 0
              : Object.keys(mergedMeasurements).length,
          });
        });
      } else {
        //兼容历史数据
        if (item.measurementsLegacy) {
          transformedData.push({
            ...item,
            index: index + 1,
            mergedMetrics: {
              context: null,
              gradeCriteria: [
                {
                  kpiId: index,
                  id: index,
                  context: item.measurementsLegacy.join(','),
                  gradeCriteria: item.gradeCriteria,
                  defaultGrade: [],
                },
              ],
              legacyContexts: item.measurementsLegacy,
            },
            rowSpan: undefined,
          });
        }
      }
    });

    return transformedData;
  }

  static fromApiObject(object: BackendAnnualPerformanceObjective) {
    const copy = cloneDeep(object);
    const rowKey = AnnualPerformanceObjective.generateRowKey(copy);

    return new AnnualPerformanceObjective(
      rowKey,
      copy.id,
      copy.name,
      copy.resources,
      copy.evalPositions,
      copy.kpiWay,
      copy.type,
      copy.subType,
      copy.metricsModels,
      copy.metrics,
      copy.gradeCriteria,
      copy.createUser,
      copy.modifierUser,
      copy.gmtCreate,
      copy.gmtModified,
      copy.reason,
      copy.kpiStatus,
      copy.processNo
    );
  }

  static fromJSON(json: AnnualPerformanceObjectiveJSON) {
    const copy = cloneDeep(json);

    return new AnnualPerformanceObjective(
      copy.rowKey,
      copy.id,
      copy.name,
      copy.resources,
      copy.positions,
      copy.way,
      copy.type,
      copy.subType,
      copy.measurements,
      copy.measurementsLegacy,
      copy.gradeCriteria,
      copy.createdBy,
      copy.modifiedBy,
      copy.createdAt,
      copy.modifiedAt,
      copy.reason,
      copy.status,
      copy.processInstanceCode
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';

  private _locales: AnnualPerformanceObjectiveLocales;

  constructor(
    public rowKey: string,
    public id: number,
    public name: string,
    public resources:
      | {
          value: string;
          label: string;
        }[]
      | null,
    public positions: { value: string; label: string }[],
    public way: BackendAnnualPerformanceObjectiveWay,
    public type: BackendAnnualPerformanceObjectiveType,
    public subType: BackendAnnualPerformanceObjectiveSubType,
    public measurements: MetricsModel[],
    public measurementsLegacy: string[],
    public gradeCriteria: string,
    public createdBy: SimpleUser,
    public modifiedBy: SimpleUser,
    public createdAt: number,
    public modifiedAt: number | null,
    public reason: string | null,
    public status: string,
    public processInstanceCode: string
  ) {
    this._locales = getAnnualPerformanceObjectiveLocales(this._localeCode);
  }

  public set locales(locales: AnnualPerformanceObjectiveLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendAnnualPerformanceObjective {
    return cloneDeep({
      id: this.id,
      name: this.name,
      resources: this.resources,
      evalPositions: this.positions,
      kpiWay: this.way,
      type: this.type,
      subType: this.subType,
      metricsModels: this.measurements,
      gradeCriteria: this.gradeCriteria,
      reason: this.reason,
      createUser: this.createdBy,
      modifierUser: this.modifiedBy,
      gmtCreate: this.createdAt,
      gmtModified: this.modifiedAt,
      kpiStatus: this.status,
      processNo: this.processInstanceCode,
      metrics: this.measurementsLegacy,
    });
  }

  public toJSON(): AnnualPerformanceObjectiveJSON {
    return cloneDeep({
      rowKey: this.rowKey,
      id: this.id,
      name: this.name,
      resources: this.resources,
      positions: this.positions,
      way: this.way,
      type: this.type,
      subType: this.subType,
      measurements: this.measurements,
      gradeCriteria: this.gradeCriteria,
      reason: this.reason,
      createdBy: this.createdBy,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      modifiedBy: this.modifiedBy,
      status: this.status,
      processInstanceCode: this.processInstanceCode,
      measurementsLegacy: this.measurementsLegacy,
    });
  }
}
