/* THIS IS A BIT-AUTO-GENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY. */

/**
 * The Bitmap file is an auto generated file used by Bit to track all your Bit components. It maps the component to a folder in your file system.
 * This file should be committed to VCS(version control).
 * Components are listed using their component ID (https://bit.dev/reference/components/component-id).
 * If you want to delete components you can use the "bit remove <component-id>" command.
 * See the docs (https://bit.dev/reference/components/removing-components) for more information, or use "bit remove --help".
 */

{
    "gql/client/hrm": {
        "name": "gql/client/hrm",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "gql/client/hrm",
        "config": {
            "teammc.snowcone/gql-react-env@2.0.13": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/gql-react-env"
            }
        }
    },
    "hook/use-block-schedule": {
        "name": "hook/use-block-schedule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "hook/use-block-schedule",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "hook/use-revoke-alter-request": {
        "name": "hook/use-revoke-alter-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "hook/use-revoke-alter-request",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "hook/use-user-attendance-group": {
        "name": "hook/use-user-attendance-group",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "hook/use-user-attendance-group",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "model/annual-performance-objective": {
        "name": "model/annual-performance-objective",
        "scope": "manyun.hrm",
        "version": "1.1.3",
        "mainFile": "index.ts",
        "rootDir": "model/annual-performance-objective"
    },
    "model/annual-performance-plan": {
        "name": "model/annual-performance-plan",
        "scope": "manyun.hrm",
        "version": "1.1.1",
        "mainFile": "index.ts",
        "rootDir": "model/annual-performance-plan"
    },
    "model/clock-in": {
        "name": "model/clock-in",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "model/clock-in",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/daily-performance-grade": {
        "name": "model/daily-performance-grade",
        "scope": "manyun.hrm",
        "version": "1.1.3",
        "mainFile": "index.ts",
        "rootDir": "model/daily-performance-grade"
    },
    "model/performance": {
        "name": "model/performance",
        "scope": "manyun.hrm",
        "version": "1.1.2",
        "mainFile": "index.ts",
        "rootDir": "model/performance"
    },
    "model/performance-objective": {
        "name": "model/performance-objective",
        "scope": "manyun.hrm",
        "version": "1.1.2",
        "mainFile": "index.ts",
        "rootDir": "model/performance-objective"
    },
    "model/shift": {
        "name": "model/shift",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "model/shift",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "model/shift-adjustment-ticket": {
        "name": "model/shift-adjustment-ticket",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "model/shift-adjustment-ticket",
        "config": {
            "teammc.snowcone/node-env@2.2.9": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-env"
            }
        }
    },
    "page/annual-performance-objective-mutator": {
        "name": "page/annual-performance-objective-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performance-objective-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/annual-performance-objectives": {
        "name": "page/annual-performance-objectives",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performance-objectives",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/annual-performance-objectives-preview": {
        "name": "page/annual-performance-objectives-preview",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performance-objectives-preview",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/annual-performance-plan-mutator": {
        "name": "page/annual-performance-plan-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performance-plan-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/annual-performance-plan-objectives-detail": {
        "name": "page/annual-performance-plan-objectives-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performance-plan-objectives-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/annual-performance-plans": {
        "name": "page/annual-performance-plans",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performance-plans",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/annual-performances-statistics": {
        "name": "page/annual-performances-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/annual-performances-statistics",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/att-statistics-by-day": {
        "name": "page/att-statistics-by-day",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/att-statistics-by-day",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/att-statistics-by-month": {
        "name": "page/att-statistics-by-month",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/att-statistics-by-month",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/break-off-balance": {
        "name": "page/break-off-balance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/break-off-balance",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/calendar-configure": {
        "name": "page/calendar-configure",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/calendar-configure",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/daily-performance-grade-mutator": {
        "name": "page/daily-performance-grade-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/daily-performance-grade-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/daily-performance-grade-records": {
        "name": "page/daily-performance-grade-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/daily-performance-grade-records",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/fixed-missed-punch": {
        "name": "page/fixed-missed-punch",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/fixed-missed-punch",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/fixed-missed-punch-creator": {
        "name": "page/fixed-missed-punch-creator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/fixed-missed-punch-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/fixed-missed-punches": {
        "name": "page/fixed-missed-punches",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/fixed-missed-punches",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/go-out-request-creator": {
        "name": "page/go-out-request-creator",
        "scope": "manyun.hrm",
        "version": "69991edf5eeae9f94b24cd4cf22cb0a5fae77585",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/go-out-request-creator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/holiday-balance-calculate": {
        "name": "page/holiday-balance-calculate",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/holiday-balance-calculate",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/holiday-balances": {
        "name": "page/holiday-balances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/holiday-balances",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/idc-match-depart": {
        "name": "page/idc-match-depart",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/idc-match-depart",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/ot-requests": {
        "name": "page/ot-requests",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/ot-requests",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/part-time-jobs-manage": {
        "name": "page/part-time-jobs-manage",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/part-time-jobs-manage",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/performance-block-evaluation": {
        "name": "page/performance-block-evaluation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/performance-block-evaluation",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/performance-red-line-mutator": {
        "name": "page/performance-red-line-mutator",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/performance-red-line-mutator",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/performance-red-line-records": {
        "name": "page/performance-red-line-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/performance-red-line-records",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/punch-clock-records": {
        "name": "page/punch-clock-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/punch-clock-records",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/scheduler-staff-statistic": {
        "name": "page/scheduler-staff-statistic",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/scheduler-staff-statistic",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/shifts": {
        "name": "page/shifts",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/shifts",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/staff-certification-standards-list": {
        "name": "page/staff-certification-standards-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/staff-certification-standards-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/staff-certifications-list": {
        "name": "page/staff-certifications-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/staff-certifications-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/staff-information-list": {
        "name": "page/staff-information-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/staff-information-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/staff-profile-list": {
        "name": "page/staff-profile-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/staff-profile-list",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/team-performances": {
        "name": "page/team-performances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/team-performances",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-annal-performance-detail": {
        "name": "page/user-annal-performance-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/user-annal-performance-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-annual-performance-objectives-detail": {
        "name": "page/user-annual-performance-objectives-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/user-annual-performance-objectives-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-annual-performances": {
        "name": "page/user-annual-performances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/user-annual-performances",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-test-performance-detail": {
        "name": "page/user-test-performance-detail",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/user-test-performance-detail",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-test-performance-goals-setting": {
        "name": "page/user-test-performance-goals-setting",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/user-test-performance-goals-setting",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "page/user-test-performances": {
        "name": "page/user-test-performances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "page/user-test-performances",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "route/hrm-routes": {
        "name": "route/hrm-routes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "route/hrm-routes",
        "config": {
            "teammc.snowcone/react-esm-env@2.0.12": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/react-esm-env"
            }
        }
    },
    "service/batch-approve-performances": {
        "name": "service/batch-approve-performances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/batch-approve-performances",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/cancle-ot": {
        "name": "service/cancle-ot",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/cancle-ot",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/check-overtime-is-repeate": {
        "name": "service/check-overtime-is-repeate",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/check-overtime-is-repeate",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/confirm-annual-performance-objectives": {
        "name": "service/confirm-annual-performance-objectives",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/confirm-annual-performance-objectives",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-and-public-annual-performance-plan": {
        "name": "service/create-and-public-annual-performance-plan",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-and-public-annual-performance-plan",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-go-out-request": {
        "name": "service/create-go-out-request",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/create-go-out-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-leave-request": {
        "name": "service/create-leave-request",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/create-leave-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-ot-request": {
        "name": "service/create-ot-request",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/create-ot-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-part-time-jobs": {
        "name": "service/create-part-time-jobs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-part-time-jobs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-performance-annual-objective": {
        "name": "service/create-performance-annual-objective",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-performance-annual-objective",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-performance-daily-grade": {
        "name": "service/create-performance-daily-grade",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-performance-daily-grade",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-punch-a-shift-clock": {
        "name": "service/create-punch-a-shift-clock",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/create-punch-a-shift-clock",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-rest-request": {
        "name": "service/create-rest-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-rest-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-resumption-leave-request": {
        "name": "service/create-resumption-leave-request",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/create-resumption-leave-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-shift": {
        "name": "service/create-shift",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-shift",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-shift-exchange-request": {
        "name": "service/create-shift-exchange-request",
        "scope": "manyun.hrm",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/create-shift-exchange-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-staff-certification-standards": {
        "name": "service/create-staff-certification-standards",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-staff-certification-standards",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/create-user-certificate": {
        "name": "service/create-user-certificate",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/create-user-certificate",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-daily-performance-grade-record": {
        "name": "service/delete-daily-performance-grade-record",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-daily-performance-grade-record",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-performance-annual-objective": {
        "name": "service/delete-performance-annual-objective",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-performance-annual-objective",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-performance-goal": {
        "name": "service/delete-performance-goal",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-performance-goal",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-shift": {
        "name": "service/delete-shift",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-shift",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-staff-certification-standards": {
        "name": "service/delete-staff-certification-standards",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-staff-certification-standards",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/delete-user-certificate": {
        "name": "service/delete-user-certificate",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/delete-user-certificate",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-daily-att-statistics": {
        "name": "service/export-daily-att-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-daily-att-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-holiday-balance": {
        "name": "service/export-holiday-balance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-holiday-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-involved-perforamnce-staff-infos": {
        "name": "service/export-involved-perforamnce-staff-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-involved-perforamnce-staff-infos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-monthly-att-statistics": {
        "name": "service/export-monthly-att-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "mayun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-monthly-att-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-ot-requests": {
        "name": "service/export-ot-requests",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-ot-requests",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-part-time-jobs": {
        "name": "service/export-part-time-jobs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-part-time-jobs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-performance-annual-objectives": {
        "name": "service/export-performance-annual-objectives",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-performance-annual-objectives",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-performance-daily-grade-records": {
        "name": "service/export-performance-daily-grade-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-performance-daily-grade-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-performances": {
        "name": "service/export-performances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-performances",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-punch-clock-records": {
        "name": "service/export-punch-clock-records",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-punch-clock-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-staff-certifications-list": {
        "name": "service/export-staff-certifications-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-staff-certifications-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/export-staff-list": {
        "name": "service/export-staff-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/export-staff-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-annual-performance": {
        "name": "service/fetch-annual-performance",
        "scope": "manyun.hrm",
        "version": "ffd1bb24100d2c015a68ae826ad985bb24c4c609",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-annual-performance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-annual-performance-objective": {
        "name": "service/fetch-annual-performance-objective",
        "scope": "manyun.hrm",
        "version": "45477e93d3eb28362484a829769a960e61e00c52",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-annual-performance-objective",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-annual-performance-objective-config": {
        "name": "service/fetch-annual-performance-objective-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-annual-performance-objective-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-annual-performance-plan": {
        "name": "service/fetch-annual-performance-plan",
        "scope": "manyun.hrm",
        "version": "9856fee6dc124253de652b6a690e3d566a4e83ed",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-annual-performance-plan",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-associated-pre-ot-data": {
        "name": "service/fetch-associated-pre-ot-data",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-associated-pre-ot-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-att-groups": {
        "name": "service/fetch-att-groups",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-att-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-att-statistics": {
        "name": "service/fetch-att-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-att-statistic",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-available-shift-users": {
        "name": "service/fetch-available-shift-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-available-shift-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-balance-change-record": {
        "name": "service/fetch-balance-change-record",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-balance-change-record",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-can-revoke-shift-adjustments": {
        "name": "service/fetch-can-revoke-shift-adjustments",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-can-revoke-shift-adjustments",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-cert-info": {
        "name": "service/fetch-cert-info",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-cert-info",
        "config": {
            "teammc.snowcone/node-esm-env@2.0.22": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/node-esm-env"
            }
        }
    },
    "service/fetch-cert-list": {
        "name": "service/fetch-cert-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-cert-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-clock-in-schedule": {
        "name": "service/fetch-clock-in-schedule",
        "scope": "manyun.hrm",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-clock-in-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-contacts": {
        "name": "service/fetch-contacts",
        "scope": "manyun.hrm",
        "version": "0.0.4",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-contacts",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-current-schedule": {
        "name": "service/fetch-current-schedule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-current-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-daily-grade-lock-infos": {
        "name": "service/fetch-daily-grade-lock-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-daily-grade-lock-infos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-fixed-missed-punch": {
        "name": "service/fetch-fixed-missed-punch",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-fixed-missed-punch",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-go-out-work-time": {
        "name": "service/fetch-go-out-work-time",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-go-out-work-time",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-groups-by-resource-code-and-role-codes": {
        "name": "service/fetch-groups-by-resource-code-and-role-codes",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-groups-by-resource-code-and-role-codes",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-holiday-configuration": {
        "name": "service/fetch-holiday-configuration",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-holiday-configuration",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-idc-match-department-list": {
        "name": "service/fetch-idc-match-department-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-idc-match-department-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-involved-perforamnce-staff-infos": {
        "name": "service/fetch-involved-perforamnce-staff-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-involved-perforamnce-staff-infos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-involved-performance-staff-department": {
        "name": "service/fetch-involved-performance-staff-department",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-involved-performance-staff-department",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-jobs-by-performances": {
        "name": "service/fetch-jobs-by-performances",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-jobs-by-performances",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-missed-punches-schedules": {
        "name": "service/fetch-missed-punches-schedules",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-missed-punches-schedules",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-missed-punches-shifts": {
        "name": "service/fetch-missed-punches-shifts",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-missed-punches-shifts",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-missed-punches-types": {
        "name": "service/fetch-missed-punches-types",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-missed-punches-types",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-my-leave-balance": {
        "name": "service/fetch-my-leave-balance",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-my-leave-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-next-schedule-by-id": {
        "name": "service/fetch-next-schedule-by-id",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-next-schedule-by-id",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ot": {
        "name": "service/fetch-ot",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ot",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ot-data": {
        "name": "service/fetch-ot-data",
        "scope": "manyun.hrm",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ot-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-ot-hours-by-month": {
        "name": "service/fetch-ot-hours-by-month",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-ot-hours-by-month",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-annual-performance-objectives": {
        "name": "service/fetch-paged-annual-performance-objectives",
        "scope": "manyun.hrm",
        "version": "3708c45ec923d63f3207a42de8959cefc07e2beb",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-annual-performance-objectives",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-annual-performance-plans": {
        "name": "service/fetch-paged-annual-performance-plans",
        "scope": "manyun.hrm",
        "version": "6f6386a123065393e01692c2c4ec4a70826171e9",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-annual-performance-plans",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-att-statistics-by-day": {
        "name": "service/fetch-paged-att-statistics-by-day",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-att-statistics-by-day",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-att-statistics-by-month": {
        "name": "service/fetch-paged-att-statistics-by-month",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-att-statistics-by-month",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-balance": {
        "name": "service/fetch-paged-balance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-duty-group": {
        "name": "service/fetch-paged-duty-group",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-duty-group",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-fixed-missed-punches": {
        "name": "service/fetch-paged-fixed-missed-punches",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-fixed-missed-punches",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-ot-requests-statistics": {
        "name": "service/fetch-paged-ot-requests-statistics",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-ot-requests-statistics",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-part-time-jobs": {
        "name": "service/fetch-paged-part-time-jobs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-part-time-jobs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-performance-daily-grade-records": {
        "name": "service/fetch-paged-performance-daily-grade-records",
        "scope": "manyun.hrm",
        "version": "ccbe24966c512a8788c6c9d00fdd672e054a2ed7",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-performance-daily-grade-records",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-performances": {
        "name": "service/fetch-paged-performances",
        "scope": "manyun.hrm",
        "version": "2170fa773f94b6072de2e2ffd071866f1e94bbb6",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-performances",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-punch-clocks": {
        "name": "service/fetch-paged-punch-clocks",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-punch-clocks",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-shifts": {
        "name": "service/fetch-paged-shifts",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-shifts",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-staff-certification-standards-list": {
        "name": "service/fetch-paged-staff-certification-standards-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-staff-certification-standards-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-staff-certifications-list": {
        "name": "service/fetch-paged-staff-certifications-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-staff-certifications-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-staff-list": {
        "name": "service/fetch-paged-staff-list",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-staff-list",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-paged-users-shift-schedule": {
        "name": "service/fetch-paged-users-shift-schedule",
        "scope": "manyun.hrm",
        "version": "0.0.11",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-paged-users-shift-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-part-time-jobs-logs": {
        "name": "service/fetch-part-time-jobs-logs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-part-time-jobs-logs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-performance-daily-grade-record": {
        "name": "service/fetch-performance-daily-grade-record",
        "scope": "manyun.hrm",
        "version": "0976b86016c130cdf5118203df0bf5aeb7042764",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-performance-daily-grade-record",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-performance-users": {
        "name": "service/fetch-performance-users",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-performance-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-schedule": {
        "name": "service/fetch-schedule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-schedules-for-leave-request": {
        "name": "service/fetch-schedules-for-leave-request",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-schedules-for-leave-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-shift-replacement-users": {
        "name": "service/fetch-shift-replacement-users",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-shift-replacement-users",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-shift-teams": {
        "name": "service/fetch-shift-teams",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-shift-teams",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-shifts": {
        "name": "service/fetch-shifts",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-shifts",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-shifts-for-leave-request": {
        "name": "service/fetch-shifts-for-leave-request",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-shifts-for-leave-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-staff-certification-names": {
        "name": "service/fetch-staff-certification-names",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-staff-certification-names",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-staff-certifications-static-count": {
        "name": "service/fetch-staff-certifications-static-count",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-staff-certifications-static-count",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-staff-schedule": {
        "name": "service/fetch-staff-schedule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-staff-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-staff-static-counts": {
        "name": "service/fetch-staff-static-counts",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-staff-static-counts",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-annual-performances": {
        "name": "service/fetch-user-annual-performances",
        "scope": "manyun.hrm",
        "version": "f75f0c79c3eed679f52b1fcdc932acee7f7d6ff1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-annual-performances",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-attendance-groups": {
        "name": "service/fetch-user-attendance-groups",
        "scope": "manyun.hrm",
        "version": "0.0.3",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-attendance-groups",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-balance": {
        "name": "service/fetch-user-balance",
        "scope": "manyun.hrm",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-balance-by-type": {
        "name": "service/fetch-user-balance-by-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-balance-by-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-bind-performance-position-by-year": {
        "name": "service/fetch-user-bind-performance-position-by-year",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-bind-performance-position-by-year",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-break-off-balance": {
        "name": "service/fetch-user-break-off-balance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-break-off-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-current-schedule-duty": {
        "name": "service/fetch-user-current-schedule-duty",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-current-schedule-duty",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-current-schedule-duty-group": {
        "name": "service/fetch-user-current-schedule-duty-group",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-current-schedule-duty-group",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-processing-events": {
        "name": "service/fetch-user-processing-events",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-processing-events",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-schedule-type": {
        "name": "service/fetch-user-schedule-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-schedule-type",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-shift-schedule": {
        "name": "service/fetch-user-shift-schedule",
        "scope": "manyun.hrm",
        "version": "0.0.1",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-shift-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-user-shifts-by-schedule-date": {
        "name": "service/fetch-user-shifts-by-schedule-date",
        "scope": "manyun.hrm",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-user-shifts-by-schedule-date",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/fetch-workdays": {
        "name": "service/fetch-workdays",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/fetch-workdays",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/hand-over-performance": {
        "name": "service/hand-over-performance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/hand-over-performance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/import-holiday-balance": {
        "name": "service/import-holiday-balance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/import-holiday-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/re-public-annual-performance-plan": {
        "name": "service/re-public-annual-performance-plan",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/re-public-annual-performance-plan",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/revoke-alter-request": {
        "name": "service/revoke-alter-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/revoke-alter-request",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/revoke-performance": {
        "name": "service/revoke-performance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/revoke-performance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/save-performance": {
        "name": "service/save-performance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/save-performance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/save-performance-evaluation": {
        "name": "service/save-performance-evaluation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/save-performance-evaluation",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/submit-performance": {
        "name": "service/submit-performance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/submit-performance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/submit-performance-evaluation": {
        "name": "service/submit-performance-evaluation",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/submit-performance-evaluation",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-annual-performance-objective-config": {
        "name": "service/update-annual-performance-objective-config",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-annual-performance-objective-config",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-annual-performance-plan": {
        "name": "service/update-annual-performance-plan",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-annual-performance-plan",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-att-statistic-data": {
        "name": "service/update-att-statistic-data",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-att-statistic-data",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-balance": {
        "name": "service/update-balance",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-balance",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-clock-in-schedule": {
        "name": "service/update-clock-in-schedule",
        "scope": "manyun.hrm",
        "version": "0.0.2",
        "mainFile": "index.ts",
        "rootDir": "service/update-clock-in-schedule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-daily-grade-lock-infos": {
        "name": "service/update-daily-grade-lock-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-daily-grade-lock-infos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-idc-match-department": {
        "name": "service/update-idc-match-department",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-idc-match-department",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-involved-performance-staff-infos": {
        "name": "service/update-involved-performance-staff-infos",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-involved-performance-staff-infos",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-part-time-jobs": {
        "name": "service/update-part-time-jobs",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-part-time-jobs",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-performance-annual-objective": {
        "name": "service/update-performance-annual-objective",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-performance-annual-objective",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-performance-daily-grade": {
        "name": "service/update-performance-daily-grade",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-performance-daily-grade",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-shift": {
        "name": "service/update-shift",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-shift",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-staff-certification-standards": {
        "name": "service/update-staff-certification-standards",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-staff-certification-standards",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/update-user-certificate": {
        "name": "service/update-user-certificate",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/update-user-certificate",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/valid-can-update-schedule-rule": {
        "name": "service/valid-can-update-schedule-rule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/valid-can-update-schedule-rule",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/whether-can-add-daily-performance-grade": {
        "name": "service/whether-can-add-daily-performance-grade",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/whether-can-add-daily-performance-grade",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/whether-is-rest-day": {
        "name": "service/whether-is-rest-day",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/whether-is-rest-day",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/whether-shift-is-be-used": {
        "name": "service/whether-shift-is-be-used",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/whether-shift-is-be-used",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "service/whether-user-need-annual-eval": {
        "name": "service/whether-user-need-annual-eval",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "service/whether-user-need-annual-eval",
        "config": {
            "teammc.snowcone/service-env@2.3.7": {},
            "teambit.envs/envs": {
                "env": "teammc.snowcone/service-env"
            }
        }
    },
    "state/staff-schedule": {
        "name": "state/staff-schedule",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "state/staff-schedule"
    },
    "ui/adjust-balance-modal-view": {
        "name": "ui/adjust-balance-modal-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/adjust-balance-modal-view",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/annual-performance-objective-card": {
        "name": "ui/annual-performance-objective-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/annual-performance-objective-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/annual-performance-objective-table": {
        "name": "ui/annual-performance-objective-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/annual-performance-objective-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/annual-performance-scoring-popover": {
        "name": "ui/annual-performance-scoring-popover",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/annual-performance-scoring-popover",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/att-group-select": {
        "name": "ui/att-group-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/att-group-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/att-statistics-card": {
        "name": "ui/att-statistics-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/att-statistics-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/balance-change-record-table": {
        "name": "ui/balance-change-record-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/balance-change-record-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/certificate-preview-upload": {
        "name": "ui/certificate-preview-upload",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/certificate-preview-upload",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/change-performance-target-link": {
        "name": "ui/change-performance-target-link",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/change-performance-target-link",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/cover-edit-card": {
        "name": "ui/cover-edit-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/cover-edit-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/duty-group-select": {
        "name": "ui/duty-group-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/duty-group-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/duty-group-text": {
        "name": "ui/duty-group-text",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/duty-group-text",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/evalute-status-tag": {
        "name": "ui/evalute-status-tag",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/evalute-status-tag",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/goals-status-tag": {
        "name": "ui/goals-status-tag",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/goals-status-tag",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/leave-type": {
        "name": "ui/leave-type",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/leave-type",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/parental-leave-record-card": {
        "name": "ui/parental-leave-record-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/parental-leave-record-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-approve-button": {
        "name": "ui/performance-approve-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-approve-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-approve-process": {
        "name": "ui/performance-approve-process",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-approve-process",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-goal-collapse": {
        "name": "ui/performance-goal-collapse",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-goal-collapse",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-position": {
        "name": "ui/performance-position",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-position",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-result-tag": {
        "name": "ui/performance-result-tag",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-result-tag",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-status-tag": {
        "name": "ui/performance-status-tag",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-status-tag",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/performance-user-select": {
        "name": "ui/performance-user-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/performance-user-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/resumption-alter-schedlue-request": {
        "name": "ui/resumption-alter-schedlue-request",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/resumption-alter-schedlue-request",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/revoke-alter-process": {
        "name": "ui/revoke-alter-process",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/revoke-alter-process",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/revoke-performance-button": {
        "name": "ui/revoke-performance-button",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/revoke-performance-button",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/schedule-shift-by-user-select": {
        "name": "ui/schedule-shift-by-user-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/schedule-shift-by-user-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/shift-adjustment": {
        "name": "ui/shift-adjustment",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/shift-adjustment",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/shift-adjustment-user-table": {
        "name": "ui/shift-adjustment-user-table",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/shift-adjustment-user-table",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/shift-replacement-user-select": {
        "name": "ui/shift-replacement-user-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/shift-replacement-user-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/shift-select": {
        "name": "ui/shift-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/shift-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/shift-team-select": {
        "name": "ui/shift-team-select",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/shift-team-select",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/staff-certificate-modal-view": {
        "name": "ui/staff-certificate-modal-view",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/staff-certificate-modal-view",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/staff-profile-editor": {
        "name": "ui/staff-profile-editor",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/staff-profile-editor",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-holiday-balance-card": {
        "name": "ui/user-holiday-balance-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/user-holiday-balance-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-shift-schedule-card": {
        "name": "ui/user-shift-schedule-card",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/user-shift-schedule-card",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "ui/user-shifts": {
        "name": "ui/user-shifts",
        "scope": "",
        "version": "",
        "defaultScope": "manyun.hrm",
        "mainFile": "index.ts",
        "rootDir": "ui/user-shifts",
        "config": {
            "manyun.base-ui/env/react-antd-4-esm-env@4.3.1": {},
            "teambit.envs/envs": {
                "env": "manyun.base-ui/env/react-antd-4-esm-env"
            }
        }
    },
    "util/performances": {
        "name": "util/performances",
        "scope": "manyun.hrm",
        "version": "1.0.1",
        "mainFile": "index.ts",
        "rootDir": "util/performances"
    },
    "$schema-version": "17.0.0"
}