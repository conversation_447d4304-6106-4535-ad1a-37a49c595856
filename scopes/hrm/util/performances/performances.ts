import moment from 'moment';

import {
  PERFORMANCE_BASIC_VERSION,
  PERFORMANCE_CHANGED_YEAR,
  PERFORMANCE_SECOND_VERSION,
} from './constant.js';

export const generateChangeTargetValidityOptions = () => {
  const nowEndDate = moment().endOf('day').valueOf();
  const formatStr = 'YYYY.MM.DD';
  let options: string[] = ['Q1', 'Q2', 'Q3', 'Q4'];
  const optionMapper: Record<string, { label: string; timeRange: string }> = {
    Q1: {
      label: 'Q1至年底',
      timeRange: `${moment().startOf('year').format(formatStr)}~${moment()
        .endOf('year')
        .format(formatStr)}`,
    },
    Q2: {
      label: 'Q2至年底',
      timeRange: `${moment().set('month', 3).startOf('month').format(formatStr)}~${moment()
        .endOf('year')
        .format(formatStr)}`,
    },
    Q3: {
      label: 'Q3至年底',
      timeRange: `${moment().set('month', 6).startOf('month').format(formatStr)}~${moment()
        .endOf('year')
        .format(formatStr)}`,
    },
    Q4: {
      label: 'Q4至年底',
      timeRange: `${moment().set('month', 9).startOf('month').format(formatStr)}~${moment()
        .endOf('year')
        .format(formatStr)}`,
    },
  };
  const q1EndDate = moment().set('month', 2).endOf('month').valueOf();
  const q2EndDate = moment().set('month', 5).endOf('month').valueOf();
  const q3EndDate = moment().set('month', 8).endOf('month').valueOf();

  if (nowEndDate > q3EndDate) {
    options = ['Q4'];
  } else if (nowEndDate > q2EndDate) {
    options = ['Q3', 'Q4'];
  } else if (nowEndDate > q1EndDate) {
    options = ['Q2', 'Q3', 'Q4'];
  }

  return options.map(opt => ({
    label: optionMapper[opt].label,
    timeRange: optionMapper[opt].timeRange,
    value: opt,
  }));
};

/**
 *
 * @param year 根据年份判断绩效版本
 * @returns
 */
export function getPerformanceVersion(year: string | number) {
  if (Number(year) >= PERFORMANCE_CHANGED_YEAR) {
    return PERFORMANCE_SECOND_VERSION;
  }
  return PERFORMANCE_BASIC_VERSION;
}
