// Auto-generated Types

export type {
  MutationBatchAddHolidayBalanceArgs,
  HolidayBalance,
  UserHolidayBalance,
  ShiftAdjustment,
  ShiftAdjustmentTicket,
  QueryScheduleStaffStatisticArgs,
  ScheduleStaffInfos,
  HolidayDateType,
  MutationUpdateHolidayConfigurationArgs,
  UserAttendanceGroupsResponseInfo,
  PaginatedPerformancePlansQuery,
  AnnualPerformanceCompletedProcess,
  AnnualPerformanceObjective,
  AnnualPerformanceObjectivesByPlanQuery,
  PaginatedPerformanceObjectives,
  PaginatedPerformances,
  AnnualPerformanceUsersGrades,
  PagedPerformanceBlockEvaluation,
  AnnualPerformanceUsersGradeInterval,
  PerformanceWhiteListItem,
  PerformanceStaticIntervalInfos,
  PerformancesStaticCountsResponse,
} from './generated-types/graphql';

// Queries
// ------

export {
  useHolidayBalanceCalculate,
  useLazyHolidayBalanceCalculate,
} from './queries/use-holiday-balance-calculate.query';
export type { QueryHolidayBalanceCalData } from './queries/use-holiday-balance-calculate.query';

export {
  useLazyShiftAdjustmentTicketsByChildLeave,
  useShiftAdjustmentTicketsByChildLeave,
} from './queries/use-shift-adjustment-ticket.query';
export type { QueryShiftAdjustmentTicketsByChildLeave } from './queries/use-shift-adjustment-ticket.query';

export {
  useLazyUserLastedChildBirthday,
  useUserLastedChildBirthday,
} from './queries/use-user-lasted-child-birthday.query';
export type { QueryUserLastedChildBirthday } from './queries/use-user-lasted-child-birthday.query';

export type { UserShiftScheduleData } from './queries/user-shift-schedule.query';
export {
  useLazyUserShiftSchedule,
  useUserShiftSchedule,
  USER_SHIFT_SCHEDULE,
} from './queries/user-shift-schedule.query';
export {
  useLazyPersonalAttendanceRules,
  usePersonalAttendanceRules,
  PERSONAL_ATTENDANCE_RULES,
} from './queries/use-personal-attendance-rules.query';

export type { PersonalAttendanceRulesInfo } from './queries/use-personal-attendance-rules.query';

export {
  useLazyUserAttendanceGroupsData,
  useUserAttendanceGroupsData,
  USER_ATTENDANCE_GROUPS,
} from './queries/user-attendance-groups.query';

export {
  useLazySupplementAttendanceCardTimes,
  useSupplementAttendanceCardTimes,
  SUPPLEMENT_ATTENDANCE_CARD_TIMES,
} from './queries/use-supplement-attendance-card-times.query';
export {
  SCHEDULE_STAFF_STATISTIC,
  useLazyScheduleStaffStatistic,
  useScheduleStaffStatistic,
} from './queries/use-schedule-staff-statistic.query';

export {
  GET_PAGINATED_ANNUAL_PERFORMANCE_OBJECTIVES_QUERY,
  useLazyAnnualPerformanceObjectives,
  useAnnualPerformanceObjectives,
} from './queries/use-annual-performance-objectives.query';

export {
  GET_ANNUAL_PERFORMANCE_OBJECTIVE_BY_ID_QUERY,
  useLazyAnnualPerformanceObjective,
} from './queries/use-annual-performance-objective-by-id.query';

export {
  useLazyPerformanceGrade,
  usePaginatedPerformanceGrades,
} from './queries/use-performances-grades.query';

export {
  GET_PERFORMANCE_PUBLIC_RESULT_CONFIGURE,
  useLazyPerformancePublicResultConfigure,
  usePerformancePublicResultConfigure,
} from './queries/use-performance-public-result-configure.query';

export {
  GET_USER_PERFORMANCE_ADDED_GRADE_QUERY,
  useLazyUserPerformanceAlreadyAddedScore,
  useUserPerformanceAlreadyAddedScore,
} from './queries/use-user-performance-already-added-score.query';

export {
  GET_PERFORMANCE_PLAN_QUERY,
  usePerformancePlanById,
  useLazyPerformancePlanById,
} from './queries/use-performance-plan-by-id.query';

export {
  GET_PAGINATED_PERFORMANCE_PLANS_QUERY,
  usePaginatedPerformancePlans,
  useLazyPaginatedPerformancePlans,
} from './queries/use-paginated-performance-plans.query';

export type { PaginatedPerformancesResolverArgs } from './queries/use-performances';
export {
  GET_PAGINATED_PERFORMANCES_QUERY,
  usePaginatedPerformances,
  useUserPerformances,
  useLazyUserPerformances,
  useUserTestPerformances,
  useLazyValidAnnualPerformanceTargetChange,
  usePerformancesCountStatic,
} from './queries/use-performances';

export { useLazyAnnualPerformanceObjectivesByPlan } from './queries/use-annual-performance-objectives-by-plan.query';

export {
  useAnnualPerformancesStatistics,
  useAnnualPerformancesUsersStatistics,
  useAnnualPerformancesStatisticsChartInfos,
} from './queries/use-performances-statistics';

export {
  GET_PERFORMANCES_DEFAULT_DEPRECATES_QUERY,
  useLazyPerformanceDefaultDepartments,
  usePerformanceDefaultDepartments,
} from './queries/use-performance-default-departments.query';

export {
  GET_PERFORMANCE_BLOCK_EVALUATION_LOGS,
  useLazyPerformanceBlockEvaluationChangedLogs,
  usePerformanceBlockEvaluationChangedLogs,
} from './queries/use-performance-block-evaluation-changed-logs.query';

export {
  GET_PERFORMANCE_BLOCK_EVALUATION_RECORDS,
  useLazyPerformanceBlockEvaluationRecords,
  usePerformanceBlockEvaluationRecords,
} from './queries/use-performance-block-evaluation.query';

export {
  GET_PERFORMANCE_GRADE_NAME_QUERY,
  usePerformanceGradesNames,
} from './queries/use-performances-grades-names.query';

export {
  PAGINATED_PERFORMANCE_WHITE_LIST,
  PERFORMANCE_WHITE_LIST_YEARS,
  useLazyPerformanceWhiteList,
  useLazyPerformanceWhiteListYears,
  usePerformanceWhiteListYears,
  usePerformanceWhiteList,
} from './queries/use-performance-white-list.query.js';
export {
  useValidExistingApprovingDailyGrade,
  useLazyValidExistingApprovingDailyGrade,
} from './queries/use-valid-existing-approving-daily-grade.query';

// Mutations
// ------
export {
  BATCH_ADD_HOLIDAY_BALANCE,
  useBatchAddHolidayBalance,
} from './mutations/use-batch-add-holiday-balance.mutation';

export {
  usePushToAttMachine,
  PUSH_TO_ATT_MACHINE,
} from './mutations/use-push-to-att-machine.mutation';
export {
  UPDATE_HOLIDAY_CONFIGURATION,
  useUpdateHolidayConfiguration,
} from './mutations/use-update-holiday-configuration.mutation';

export {
  useUpdatePersonalAttendanceRules,
  UPDATE_PERSONAL_ATTENDANCE_RULES,
} from './mutations/update-personal-attendance-rules.mutation';

export type { UpdatePersonalAttendanceRulesParams } from './mutations/update-personal-attendance-rules.mutation';

export {
  usePublicPerformanceResultConfigure,
  PUBLIC_PERFORMANCE_RESULT_CONFIGURE,
} from './mutations/use-public-performance-result-configure.mutation';

export { useBatchHandOverPerformances } from './mutations/batch-hand-over-performances';

export {
  useConfirmPerformanceBlockEvaluation,
  useMarkPerformanceBlockEvaluation,
  useUpdatePerformanceBlockEvaluation,
  useUpdatePerformanceBlockEvaluationAccidentCount,
} from './mutations/update-performance-block-evaluation.mutation';

export {
  useCreatePerformanceWhiteList,
  useDeletePerformanceWhiteList,
  useUpdatePerformanceWhiteList,
} from './mutations/use-use-performance-white-list.mutation';
