import type { CodegenConfig } from '@graphql-codegen/cli';

const config: CodegenConfig = {
  schema: [
    {
      [`${process.env.BFF_URL}/graphql`]: {
        headers: {
          Authorization: `Bearer ${process.env.AUTH_TOKEN}`,
        },
      },
    },
  ],
  generates: {
    [`${__dirname}/generated-types/`]: {
      preset: 'client',
      config: {
        useTypeImports: true,
        enumsAsTypes: true,
        scalars: {
          Date: 'number',
          Long: 'number',
          JSON: 'Record<keyof any, any>',
          JSONObject: 'Record<keyof any, any>',
        },
      },
      plugins: [
        {
          add: {
            content: '// @ts-nocheck',
          },
        },
      ],
    },
  },
};

export default config;
