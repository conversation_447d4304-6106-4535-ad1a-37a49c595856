import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

export const PUBLIC_PERFORMANCE_RESULT_CONFIGURE: DocumentNode = gql`
  mutation UpdatePerformancePublicityConfigure($year: String!, $period: String!) {
    updatePerformancePublicityConfigure(year: $year, period: $period) {
      code
      message
      success
    }
  }
`;

export type MutationUpdatePerformancePublicityConfigureArgs = {
  year: string;
  period: string;
};

export type MutationUpdatePerformancePublicityConfigureDataResult = {
  updatePerformancePublicityConfigure: {
    code?: string;
    message?: string;
    success: boolean;
  };
};

export function usePublicPerformanceResultConfigure(
  options?: MutationHookOptions<
    MutationUpdatePerformancePublicityConfigureDataResult,
    MutationUpdatePerformancePublicityConfigureArgs
  >
): MutationTuple<
  MutationUpdatePerformancePublicityConfigureDataResult,
  MutationUpdatePerformancePublicityConfigureArgs
> {
  return useMutation(PUBLIC_PERFORMANCE_RESULT_CONFIGURE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
