import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Mutation,
  MutationConfirmPerformanceBlockEvaluationArgs,
  MutationMarkPerformanceBlockEvaluationArgs,
  MutationUpdatePerformanceBlockEvaluationAccidentCountArgs,
  MutationUpdatePerformanceBlockEvaluationArgs,
} from '../generated-types/graphql';

export type MutateUpdateBlockEvaluationRulesData = Pick<
  Mutation,
  'updatePerformanceBlockEvaluation'
>;

export type MutateConfirmBlockEvaluationRulesData = Pick<
  Mutation,
  'confirmPerformanceBlockEvaluation'
>;

export type MutateUpdateBlockEvaluationAccidentCountRulesData = Pick<
  Mutation,
  'updatePerformanceBlockEvaluationAccidentCount'
>;

export type MutateMarkBlockEvaluationRulesData = Pick<Mutation, 'markPerformanceBlockEvaluation'>;

export const UPDATE_PERFORMANCE_BLOCK_EVALUATION: DocumentNode = gql`
  mutation UpdatePerformanceBlockEvaluation($params: UpdateBlockEvaluationParams!) {
    updatePerformanceBlockEvaluation(params: $params) {
      success
      message
      code
    }
  }
`;

export function useUpdatePerformanceBlockEvaluation(
  options?: MutationHookOptions<
    MutateUpdateBlockEvaluationRulesData,
    MutationUpdatePerformanceBlockEvaluationArgs
  >
): MutationTuple<
  MutateUpdateBlockEvaluationRulesData,
  MutationUpdatePerformanceBlockEvaluationArgs
> {
  return useMutation(UPDATE_PERFORMANCE_BLOCK_EVALUATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export const CONFIRM_PERFORMANCE_BLOCK_EVALUATION: DocumentNode = gql`
  mutation ConfirmPerformanceBlockEvaluation($params: UpdateBlockEvaluationParams!) {
    confirmPerformanceBlockEvaluation(params: $params) {
      success
      message
      code
    }
  }
`;

export function useConfirmPerformanceBlockEvaluation(
  options?: MutationHookOptions<
    MutateConfirmBlockEvaluationRulesData,
    MutationConfirmPerformanceBlockEvaluationArgs
  >
): MutationTuple<
  MutateConfirmBlockEvaluationRulesData,
  MutationConfirmPerformanceBlockEvaluationArgs
> {
  return useMutation(CONFIRM_PERFORMANCE_BLOCK_EVALUATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export const MARK_PERFORMANCE_BLOCK_EVALUATION: DocumentNode = gql`
  mutation MarkPerformanceBlockEvaluation($params: MarkBlockEvaluationParams!) {
    markPerformanceBlockEvaluation(params: $params) {
      success
      message
      code
    }
  }
`;

export function useMarkPerformanceBlockEvaluation(
  options?: MutationHookOptions<
    MutateMarkBlockEvaluationRulesData,
    MutationMarkPerformanceBlockEvaluationArgs
  >
): MutationTuple<MutateMarkBlockEvaluationRulesData, MutationMarkPerformanceBlockEvaluationArgs> {
  return useMutation(MARK_PERFORMANCE_BLOCK_EVALUATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export const UPDATE_PERFORMANCE_BLOCK_EVALUATION_ACCIDENT_COUNT: DocumentNode = gql`
  mutation UpdatePerformanceBlockEvaluationAccidentCount(
    $params: UpdatePerformanceBlockEvaluationAccidentCountParams!
  ) {
    updatePerformanceBlockEvaluationAccidentCount(params: $params) {
      success
      message
      code
    }
  }
`;

export function useUpdatePerformanceBlockEvaluationAccidentCount(
  options?: MutationHookOptions<
    MutateUpdateBlockEvaluationAccidentCountRulesData,
    MutationUpdatePerformanceBlockEvaluationAccidentCountArgs
  >
): MutationTuple<
  MutateUpdateBlockEvaluationAccidentCountRulesData,
  MutationUpdatePerformanceBlockEvaluationAccidentCountArgs
> {
  return useMutation(UPDATE_PERFORMANCE_BLOCK_EVALUATION_ACCIDENT_COUNT, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
