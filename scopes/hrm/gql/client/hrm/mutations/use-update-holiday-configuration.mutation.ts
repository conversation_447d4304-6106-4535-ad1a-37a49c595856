import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Mutation, MutationUpdateHolidayConfigurationArgs } from '../generated-types/graphql';

export type MutateUpdateHolidayConfigurationData = Pick<Mutation, 'updateHolidayConfiguration'>;

export const UPDATE_HOLIDAY_CONFIGURATION: DocumentNode = gql`
  mutation UpdateHolidayConfiguration(
    $beforeConfiguration: BeforeHolidayConfiguration!
    $afterConfiguration: AfterHolidayConfiguration!
  ) {
    updateHolidayConfiguration(
      beforeConfiguration: $beforeConfiguration
      afterConfiguration: $afterConfiguration
    ) {
      code
      message
      success
    }
  }
`;

export function useUpdateHolidayConfiguration(
  options?: MutationHookOptions<
    MutateUpdateHolidayConfigurationData,
    MutationUpdateHolidayConfigurationArgs
  >
): MutationTuple<MutateUpdateHolidayConfigurationData, MutationUpdateHolidayConfigurationArgs> {
  return useMutation(UPDATE_HOLIDAY_CONFIGURATION, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
