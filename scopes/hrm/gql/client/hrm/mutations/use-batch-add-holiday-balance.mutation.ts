import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Mutation, MutationBatchAddHolidayBalanceArgs } from '../generated-types/graphql';

export type MutateBatchAddHolidayBalanceData = Pick<Mutation, 'batchAddHolidayBalance'>;

export const BATCH_ADD_HOLIDAY_BALANCE: DocumentNode = gql`
  mutation BatchAddHolidayBalance(
    $data: [UserHolidayBalance!]!
    $fileIdList: [McUploadFileInput!]!
  ) {
    batchAddHolidayBalance(data: $data, fileIdList: $fileIdList) {
      code
      message
      success
    }
  }
`;

export function useBatchAddHolidayBalance(
  options?: MutationHookOptions<
    MutateBatchAddHolidayBalanceData,
    MutationBatchAddHolidayBalanceArgs
  >
): MutationTuple<MutateBatchAddHolidayBalanceData, MutationBatchAddHolidayBalanceArgs> {
  return useMutation(BATCH_ADD_HOLIDAY_BALANCE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
