import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Mutation,
  MutationCreatePerformanceWhiteListArgs,
  MutationDeletePerformanceWhiteListArgs,
  MutationUpdatePerformanceWhiteListArgs,
} from '../generated-types/graphql';

export type MutationCreatePerformanceWhiteListData = Pick<Mutation, 'createPerformanceWhiteList'>;
export type MutationDeletePerformanceWhiteListData = Pick<Mutation, 'deletePerformanceWhiteList'>;
export type MutationUpdatePerformanceWhiteListAData = Pick<Mutation, 'updatePerformanceWhiteList'>;

export const UPDATE_PERFORMANCE_WHITE_LIST: DocumentNode = gql`
  mutation UpdatePerformanceWhiteList($params: UpdatePerformanceWhiteListItemInput!) {
    updatePerformanceWhiteList(params: $params) {
      code
      message
      success
    }
  }
`;

export function useUpdatePerformanceWhiteList(
  options?: MutationHookOptions<
    MutationUpdatePerformanceWhiteListAData,
    MutationUpdatePerformanceWhiteListArgs
  >
): MutationTuple<MutationUpdatePerformanceWhiteListAData, MutationUpdatePerformanceWhiteListArgs> {
  return useMutation(UPDATE_PERFORMANCE_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export const CREATE_PERFORMANCE_WHITE_LIST: DocumentNode = gql`
  mutation CreatePerformanceWhiteList($params: CreatePerformanceWhiteListItemInput!) {
    createPerformanceWhiteList(params: $params) {
      code
      message
      success
    }
  }
`;

export function useCreatePerformanceWhiteList(
  options?: MutationHookOptions<
    MutationCreatePerformanceWhiteListData,
    MutationCreatePerformanceWhiteListArgs
  >
): MutationTuple<MutationCreatePerformanceWhiteListData, MutationCreatePerformanceWhiteListArgs> {
  return useMutation(CREATE_PERFORMANCE_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export const DELETE_PERFORMANCE_WHITE_LIST: DocumentNode = gql`
  mutation deletePerformanceWhiteList($id: Long!) {
    deletePerformanceWhiteList(id: $id) {
      code
      message
      success
    }
  }
`;

export function useDeletePerformanceWhiteList(
  options?: MutationHookOptions<
    MutationDeletePerformanceWhiteListData,
    MutationDeletePerformanceWhiteListArgs
  >
): MutationTuple<MutationDeletePerformanceWhiteListData, MutationDeletePerformanceWhiteListArgs> {
  return useMutation(DELETE_PERFORMANCE_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
