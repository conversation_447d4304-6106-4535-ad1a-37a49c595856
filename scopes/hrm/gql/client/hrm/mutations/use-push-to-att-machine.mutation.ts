import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type { Mutation, MutationPushToAttMachineArgs } from '../generated-types/graphql';

export type MutatePushToAttMachineData = Pick<Mutation, 'pushToAttMachine'>;

export const PUSH_TO_ATT_MACHINE: DocumentNode = gql`
  mutation PushToAttMachine($staffInfos: [StaffInfos!]!) {
    pushToAttMachine(staffInfos: $staffInfos) {
      code
      message
      success
    }
  }
`;

export function usePushToAttMachine(
  options?: MutationHookOptions<MutatePushToAttMachineData, MutationPushToAttMachineArgs>
): MutationTuple<MutatePushToAttMachineData, MutationPushToAttMachineArgs> {
  return useMutation(PUSH_TO_ATT_MACHINE, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
