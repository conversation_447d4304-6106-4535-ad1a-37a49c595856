import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Mutation,
  MutationUpdatePersonalAttendanceRulesArgs,
} from '../generated-types/graphql';

export type { UpdatePersonalAttendanceRulesParams } from '../generated-types/graphql';
export type MutateUpdatePersonalAttendanceRulesData = Pick<
  Mutation,
  'updatePersonalAttendanceRules'
>;

export const UPDATE_PERSONAL_ATTENDANCE_RULES: DocumentNode = gql`
  mutation UpdatePersonalAttendanceRules($params: UpdatePersonalAttendanceRulesParams!) {
    updatePersonalAttendanceRules(params: $params) {
      success
      message
      code
    }
  }
`;

export function useUpdatePersonalAttendanceRules(
  options?: MutationHookOptions<
    MutateUpdatePersonalAttendanceRulesData,
    MutationUpdatePersonalAttendanceRulesArgs
  >
): MutationTuple<
  MutateUpdatePersonalAttendanceRulesData,
  MutationUpdatePersonalAttendanceRulesArgs
> {
  return useMutation(UPDATE_PERSONAL_ATTENDANCE_RULES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
