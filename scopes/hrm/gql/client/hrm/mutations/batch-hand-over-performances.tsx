import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  BatchHandOverPerformancesResponse,
  Maybe,
  MutationBatchHandOverPerformancesArgs,
} from '../generated-types/graphql';

export const BATCH_HAND_OVER_PERFORMANCES: DocumentNode = gql`
  mutation BatchHandOverPerformances($year: String!, $type: String!, $transfereeId: Int!) {
    batchHandOverPerformances(year: $year, type: $type, transfereeId: $transfereeId) {
      code
      message
      success
    }
  }
`;

export type BatchHandOverPerformancesData = {
  batchHandOverPerformances: Maybe<BatchHandOverPerformancesResponse>;
};
export function useBatchHandOverPerformances(
  options?: MutationHookOptions<
    BatchHandOverPerformancesData,
    MutationBatchHandOverPerformancesArgs
  >
): MutationTuple<BatchHandOverPerformancesData, MutationBatchHandOverPerformancesArgs> {
  return useMutation(BATCH_HAND_OVER_PERFORMANCES, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
