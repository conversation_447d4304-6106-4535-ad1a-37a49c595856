import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QuerySupplementAttendanceCardTimesArgs } from '../generated-types/graphql';

export type SupplementAttendanceCardTimesData = Pick<Query, 'supplementAttendanceCardTimes'>;

export const SUPPLEMENT_ATTENDANCE_CARD_TIMES: DocumentNode = gql`
  query SupplementAttendanceCardTimes($params: SupplementAttendanceCardTimesParams!) {
    supplementAttendanceCardTimes(params: $params) {
      data
    }
  }
`;

export const useSupplementAttendanceCardTimes = (
  options?: QueryHookOptions<
    SupplementAttendanceCardTimesData,
    QuerySupplementAttendanceCardTimesArgs
  >
): QueryResult<SupplementAttendanceCardTimesData, QuerySupplementAttendanceCardTimesArgs> =>
  useQuery(SUPPLEMENT_ATTENDANCE_CARD_TIMES, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazySupplementAttendanceCardTimes = (
  options?: LazyQueryHookOptions<
    SupplementAttendanceCardTimesData,
    QuerySupplementAttendanceCardTimesArgs
  >
): LazyQueryResultTuple<
  SupplementAttendanceCardTimesData,
  QuerySupplementAttendanceCardTimesArgs
> =>
  useLazyQuery(SUPPLEMENT_ATTENDANCE_CARD_TIMES, {
    fetchPolicy: 'network-only',
    ...options,
  });
