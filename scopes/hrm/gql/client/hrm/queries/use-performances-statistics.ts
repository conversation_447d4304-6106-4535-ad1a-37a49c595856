import { gql, useQuery } from '@apollo/client';
import type { DocumentNode, QueryHookOptions, QueryResult } from '@apollo/client';

import type {
  AnnualPerformanceStatistics,
  AnnualPerformanceStatisticsQueryArgs,
  AnnualPerformanceUsersStatistics,
  AnnualPerformanceUsersStatisticsQueryArgs,
} from '../generated-types/graphql';

export const GET_PERFORMANCES_STATISTICS_QUERY: DocumentNode = gql`
  query GetAnnualPerformancesStatistics($query: AnnualPerformanceStatisticsQueryArgs!) {
    annualPerformanceStatistics(query: $query) {
      data {
        year
        region {
          label
          value
        }
        idc {
          label
          value
        }
        regionPICs {
          id
          name
        }
        hrs {
          id
          name
        }
        targetFinishedPercent
        q1FinishedPercent
        q2FinishedPercent
        q3FinishedPercent
        q4FinishedPercent
        yearFinishedPercent
      }
      hrbps {
        id
        name
      }
    }
  }
`;

export const GET_PERFORMANCES_STATISTICS_CHART_QUERY: DocumentNode = gql`
  query GetAnnualPerformancesStatistics($query: AnnualPerformanceStatisticsQueryArgs!) {
    annualPerformanceStatistics(query: $query) {
      gradeIntervalResult {
        key
        minGrade
        maxGrade
        data {
          key
          label
          value
          percent
        }
      }
      statusIntervalResult {
        key
        minGrade
        maxGrade
        data {
          key
          label
          value
          percent
        }
      }
    }
  }
`;

export type AnnualPerformancesStatisticsResultData = {
  annualPerformanceStatistics: AnnualPerformanceStatistics;
};

export function useAnnualPerformancesStatistics(
  options?: QueryHookOptions<
    AnnualPerformancesStatisticsResultData,
    { query: AnnualPerformanceStatisticsQueryArgs }
  >
): QueryResult<
  AnnualPerformancesStatisticsResultData,
  { query: AnnualPerformanceStatisticsQueryArgs }
> {
  return useQuery<
    AnnualPerformancesStatisticsResultData,
    { query: AnnualPerformanceStatisticsQueryArgs }
  >(GET_PERFORMANCES_STATISTICS_QUERY, { fetchPolicy: 'network-only', ...options });
}

export function useAnnualPerformancesStatisticsChartInfos(
  options?: QueryHookOptions<
    AnnualPerformancesStatisticsResultData,
    { query: AnnualPerformanceStatisticsQueryArgs }
  >
): QueryResult<
  AnnualPerformancesStatisticsResultData,
  { query: AnnualPerformanceStatisticsQueryArgs }
> {
  return useQuery<
    AnnualPerformancesStatisticsResultData,
    { query: AnnualPerformanceStatisticsQueryArgs }
  >(GET_PERFORMANCES_STATISTICS_CHART_QUERY, { fetchPolicy: 'network-only', ...options });
}

export const GET_PERFORMANCES_STATISTICS_USERS_QUERY = gql`
  query GetAnnualPerformancesUsersStatistics($query: AnnualPerformanceUsersStatisticsQueryArgs!) {
    annualPerformanceUsersStatistics(query: $query) {
      data {
        user {
          id
          name
          idc
          region {
            value
            label
          }
          jobNo
        }
        year
        idc {
          label
          value
        }
        blockGuid
        evaluationJob {
          label
          value
        }
        hrs {
          id
          name
        }
        lineManagers {
          id
          name
        }
        secondLineManagers {
          id
          name
        }
        q1Grade
        q2Grade
        q3Grade
        q4Grade
        yearGrade
        result
        q1Id
        q2Id
        q3Id
        q4Id
        yearId
        yearFinished
        q1Finished
        q2Finished
        q3Finished
        q4Finished
      }
      hrbps {
        id
        name
      }
      userRegions {
        label
        value
      }
    }
  }
`;

export type AnnualPerformancesUsersStatisticsResultData = {
  annualPerformanceUsersStatistics: AnnualPerformanceUsersStatistics;
};

export function useAnnualPerformancesUsersStatistics(
  options?: QueryHookOptions<
    AnnualPerformancesUsersStatisticsResultData,
    { query: AnnualPerformanceUsersStatisticsQueryArgs }
  >
): QueryResult<
  AnnualPerformancesUsersStatisticsResultData,
  { query: AnnualPerformanceUsersStatisticsQueryArgs }
> {
  return useQuery<
    AnnualPerformancesUsersStatisticsResultData,
    { query: AnnualPerformanceUsersStatisticsQueryArgs }
  >(GET_PERFORMANCES_STATISTICS_USERS_QUERY, { fetchPolicy: 'network-only', ...options });
}
