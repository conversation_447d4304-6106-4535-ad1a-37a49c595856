import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { AnnualPerformanceObjectiveJSON } from '@manyun/hrm.model.annual-performance-objective';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-annual-performance-objectives';

export type PaginatedPerformancesQueryResultData = {
  paginatedAnnualPerformanceObjectives: {
    data: AnnualPerformanceObjectiveJSON[];
    total: number;
  };
};

export const GET_PAGINATED_ANNUAL_PERFORMANCE_OBJECTIVES_QUERY = gql`
  query GetPaginatedAnnualPerformanceObjectives(
    $query: PaginatedAnnualPerformanceObjectivesQuery!
  ) {
    paginatedAnnualPerformanceObjectives(query: $query) {
      data {
        rowKey
        id
        name
        subType
        type
        way
        measurements {
          id
          kpiId
          context
          ceiling
          gradeCriteria
          defaultGrade
        }
        measurementsLegacy
        gradeCriteria
        createdBy {
          id
          name
        }
        modifiedBy {
          id
          name
        }
        resources {
          label
          value
        }
        positions {
          label
          value
        }
        status
        processInstanceCode
        createdAt
        modifiedAt
      }
      total
    }
  }
`;

export function useLazyAnnualPerformanceObjectives(
  options?: QueryHookOptions<
    PaginatedPerformancesQueryResultData,
    {
      query: SvcQuery;
    }
  >
): LazyQueryResultTuple<
  PaginatedPerformancesQueryResultData,
  {
    query: SvcQuery;
  }
> {
  return useLazyQuery<
    PaginatedPerformancesQueryResultData,
    {
      query: SvcQuery;
    }
  >(GET_PAGINATED_ANNUAL_PERFORMANCE_OBJECTIVES_QUERY, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export const useAnnualPerformanceObjectives = (
  options?: QueryHookOptions<
    PaginatedPerformancesQueryResultData,
    {
      query: SvcQuery;
    }
  >
): QueryResult<
  PaginatedPerformancesQueryResultData,
  {
    query: SvcQuery;
  }
> =>
  useQuery(GET_PAGINATED_ANNUAL_PERFORMANCE_OBJECTIVES_QUERY, {
    fetchPolicy: 'network-only',
    ...options,
  });
