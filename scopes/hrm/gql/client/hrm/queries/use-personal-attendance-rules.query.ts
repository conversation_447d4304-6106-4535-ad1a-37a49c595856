import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query } from '../generated-types/graphql';

export type { PersonalAttendanceRulesInfo } from '../generated-types/graphql';

export type PersonalAttendanceRulesData = Pick<Query, 'personalAttendanceRules'>;
export const PERSONAL_ATTENDANCE_RULES: DocumentNode = gql`
  query PersonalAttendanceRules {
    personalAttendanceRules {
      data {
        id
        staffId
        onCheckNotify
        onCheckNotifyRules
        offCheckNotify
        offCheckNotifyRules
        checkErrorNotify
        errorNotifyChannels
        checkNotifyChannels
      }
    }
  }
`;

export const usePersonalAttendanceRules = (
  options?: QueryHookOptions<PersonalAttendanceRulesData, {}>
): QueryResult<PersonalAttendanceRulesData, {}> =>
  useQuery(PERSONAL_ATTENDANCE_RULES, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyPersonalAttendanceRules = (
  options?: LazyQueryHookOptions<PersonalAttendanceRulesData, {}>
): LazyQueryResultTuple<PersonalAttendanceRulesData, {}> =>
  useLazyQuery(PERSONAL_ATTENDANCE_RULES, {
    fetchPolicy: 'network-only',
    ...options,
  });
