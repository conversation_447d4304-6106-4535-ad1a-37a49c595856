import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type {
  Query,
  QueryPerformanceBlockEvaluationChangedLogsArgs,
} from '../generated-types/graphql.js';

export const GET_PERFORMANCE_BLOCK_EVALUATION_LOGS = gql`
  query PerformanceBlockEvaluationChangedLogs($blockEvalId: Long!) {
    performanceBlockEvaluationChangedLogs(blockEvalId: $blockEvalId) {
      data {
        id
        changeType
        blockEvaluationId
        reason
        gmtCreate
        gmtModified
        modifierId
        modifierBy {
          id
          name
        }
        bizId
        evaluationCompare {
          name
          desc
          oldContent
          newContent
        }
      }
    }
  }
`;

export type PerformanceBlockEvaluationLogsResultData = Pick<
  Query,
  'performanceBlockEvaluationChangedLogs'
>;

export function usePerformanceBlockEvaluationChangedLogs(
  options?: QueryHookOptions<
    PerformanceBlockEvaluationLogsResultData,
    QueryPerformanceBlockEvaluationChangedLogsArgs
  >
): QueryResult<
  PerformanceBlockEvaluationLogsResultData,
  QueryPerformanceBlockEvaluationChangedLogsArgs
> {
  return useQuery<
    PerformanceBlockEvaluationLogsResultData,
    QueryPerformanceBlockEvaluationChangedLogsArgs
  >(GET_PERFORMANCE_BLOCK_EVALUATION_LOGS, { fetchPolicy: 'network-only', ...options });
}

export function useLazyPerformanceBlockEvaluationChangedLogs(
  options?: QueryHookOptions<
    PerformanceBlockEvaluationLogsResultData,
    QueryPerformanceBlockEvaluationChangedLogsArgs
  >
): LazyQueryResultTuple<
  PerformanceBlockEvaluationLogsResultData,
  QueryPerformanceBlockEvaluationChangedLogsArgs
> {
  return useLazyQuery<
    PerformanceBlockEvaluationLogsResultData,
    QueryPerformanceBlockEvaluationChangedLogsArgs
  >(GET_PERFORMANCE_BLOCK_EVALUATION_LOGS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
