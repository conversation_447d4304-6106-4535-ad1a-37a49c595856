import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { Query, QueryPerformanceBlockEvaluationListArgs } from '../generated-types/graphql.js';

export const GET_PERFORMANCE_BLOCK_EVALUATION_RECORDS = gql`
  query PerformanceBlockEvaluationList(
    $year: String!
    $blockGuidList: [String!]
    $status: String
    $qualified: Boolean
  ) {
    performanceBlockEvaluationList(
      year: $year
      blockGuidList: $blockGuidList
      status: $status
      qualified: $qualified
    ) {
      data {
        id
        year
        idc
        blockGuid
        #满意度扣分条数
        customerSatisfactionCount
        #事件条数
        eventCount
        #飞检分数
        grade
        #符合评优条件
        qualified
        #状态
        evaluationStatus
        # 是否为测试时间机房
        newEvent
        accidentsCount
        # 创建人
        modifierId
        # 创建时间
        gmtModified
        blockLabelInValue {
          label
          value
        }
        modifierBy {
          id
          name
        }
        confirmStaffBy {
          id
          name
        }
        confirmTime 
      }
      total
    }
  }
`;

export type PerformanceBlockEvaluationListResultData = Pick<
  Query,
  'performanceBlockEvaluationList'
>;

export function usePerformanceBlockEvaluationRecords(
  options?: QueryHookOptions<
    PerformanceBlockEvaluationListResultData,
    QueryPerformanceBlockEvaluationListArgs
  >
): QueryResult<PerformanceBlockEvaluationListResultData, QueryPerformanceBlockEvaluationListArgs> {
  return useQuery<
    PerformanceBlockEvaluationListResultData,
    QueryPerformanceBlockEvaluationListArgs
  >(GET_PERFORMANCE_BLOCK_EVALUATION_RECORDS, { fetchPolicy: 'network-only', ...options });
}

export function useLazyPerformanceBlockEvaluationRecords(
  options?: QueryHookOptions<
    PerformanceBlockEvaluationListResultData,
    QueryPerformanceBlockEvaluationListArgs
  >
): LazyQueryResultTuple<
  PerformanceBlockEvaluationListResultData,
  QueryPerformanceBlockEvaluationListArgs
> {
  return useLazyQuery<
    PerformanceBlockEvaluationListResultData,
    QueryPerformanceBlockEvaluationListArgs
  >(GET_PERFORMANCE_BLOCK_EVALUATION_RECORDS, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
