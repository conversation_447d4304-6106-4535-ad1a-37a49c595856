import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryValidExistingApprovingDailyGradeArgs } from '../generated-types/graphql';

export type ValidExistingApprovingDailyGradeData = Pick<Query, 'validExistingApprovingDailyGrade'>;

export const VALID_EXISTING_APPROVING_DAILY_GRADE: DocumentNode = gql`
  query ValidExistingApprovingDailyGrade($performanceId: String!) {
    validExistingApprovingDailyGrade(performanceId: $performanceId) {
      data
    }
  }
`;

export const useValidExistingApprovingDailyGrade = (
  options?: QueryHookOptions<
    ValidExistingApprovingDailyGradeData,
    QueryValidExistingApprovingDailyGradeArgs
  >
): QueryResult<ValidExistingApprovingDailyGradeData, QueryValidExistingApprovingDailyGradeArgs> =>
  useQuery(VALID_EXISTING_APPROVING_DAILY_GRADE, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyValidExistingApprovingDailyGrade = (
  options?: LazyQueryHookOptions<
    ValidExistingApprovingDailyGradeData,
    QueryValidExistingApprovingDailyGradeArgs
  >
): LazyQueryResultTuple<
  ValidExistingApprovingDailyGradeData,
  QueryValidExistingApprovingDailyGradeArgs
> =>
  useLazyQuery(VALID_EXISTING_APPROVING_DAILY_GRADE, {
    fetchPolicy: 'network-only',
    ...options,
  });
