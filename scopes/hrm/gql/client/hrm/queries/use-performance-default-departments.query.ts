import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { Query } from '../generated-types/graphql.js';

export type PerformanceDefaultDepartmentsResultData = Pick<Query, 'performanceDefaultDepartments'>;

export const GET_PERFORMANCES_DEFAULT_DEPRECATES_QUERY = gql`
  query PerformanceDefaultDepartments {
    performanceDefaultDepartments {
      data
    }
  }
`;

export function usePerformanceDefaultDepartments(
  options?: QueryHookOptions<PerformanceDefaultDepartmentsResultData, {}>
): QueryResult<PerformanceDefaultDepartmentsResultData, {}> {
  return useQuery<PerformanceDefaultDepartmentsResultData, {}>(
    GET_PERFORMANCES_DEFAULT_DEPRECATES_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
export function useLazyPerformanceDefaultDepartments(
  options?: QueryHookOptions<PerformanceDefaultDepartmentsResultData, {}>
): LazyQueryResultTuple<PerformanceDefaultDepartmentsResultData, {}> {
  return useLazyQuery<PerformanceDefaultDepartmentsResultData, {}>(
    GET_PERFORMANCES_DEFAULT_DEPRECATES_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
