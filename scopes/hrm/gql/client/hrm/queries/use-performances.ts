import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  BackendPerformanceSubType,
  BackendPerformanceType,
  PerformanceJSON,
} from '@manyun/hrm.model.performance';

import type {
  PaginatedPerformances,
  PaginatedPerformancesQuery,
  Query,
  QueryValidAnnualPerformanceTargetChangeArgs,
  ValidAnnualPerformanceTargetChangeResponse,
} from '../generated-types/graphql';

export type PaginatedPerformancesQueryResultData = {
  paginatedPerformances: PaginatedPerformances;
};

type PerformancesCountStaticQueryResultData = Pick<Query, 'performancesStaticCounts'>;

export type PaginatedPerformancesResolverArgs = { query: PaginatedPerformancesQuery };

export const GET_PAGINATED_PERFORMANCES_QUERY: DocumentNode = gql`
  query GetPaginatedPerformances($query: PaginatedPerformancesQuery!) {
    paginatedPerformances(query: $query) {
      data {
        id
        year
        type
        plan {
          id
        }
        rowKey
        result
        period
        user {
          region {
            value
            label
          }
          idc
          blockGuid
          id
          name
          job
          hiredAt
          jobNo
        }
        lineManagers {
          id
          name
        }
        secondLineManagers {
          id
          name
        }
        hrs {
          id
          name
        }
        startedAt
        objectiveExpiredAt
        evaluationExpiredAt
        objectiveStatus
        evaluationStatus
        currentHandlers {
          id
          name
        }
        evaluationJob {
          label
          value
        }
        grade
        gradeAvg
        result
        objectivesGrade
      }
      total
    }
  }
`;

export function usePaginatedPerformances(
  options?: QueryHookOptions<
    PaginatedPerformancesQueryResultData,
    PaginatedPerformancesResolverArgs
  >
): QueryResult<PaginatedPerformancesQueryResultData, PaginatedPerformancesResolverArgs> {
  return useQuery<PaginatedPerformancesQueryResultData, PaginatedPerformancesResolverArgs>(
    GET_PAGINATED_PERFORMANCES_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}

export const GET_PERFORMANCE_COUNT_STATIC_QUERY: DocumentNode = gql`
  query GetPerformancesStaticCounts($query: PaginatedPerformancesQuery!) {
    performancesStaticCounts(query: $query) {
      statusCounts {
        status
        count
      }
      periodsCounts {
        period
        count
      }
    }
  }
`;

export function usePerformancesCountStatic(
  options?: QueryHookOptions<
    PerformancesCountStaticQueryResultData,
    PaginatedPerformancesResolverArgs
  >
): QueryResult<PerformancesCountStaticQueryResultData, PaginatedPerformancesResolverArgs> {
  return useQuery<PerformancesCountStaticQueryResultData, PaginatedPerformancesResolverArgs>(
    GET_PERFORMANCE_COUNT_STATIC_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}

export type UserPerformancesQueryResultData = { myPerformances: PerformanceJSON[] };

export type MyAnnualPerformancesResolverArgs = {
  type: BackendPerformanceType;
  subType?: BackendPerformanceSubType;
  year: string;
  performancePosition: string;
  userId: number;
};

export const GET_USER_PERFORMANCES_QUERY = gql`
  query GetMyAnnualPerformance(
    $type: String!
    $year: String
    $performancePosition: String
    $userId: Int
    $subType: String
  ) {
    myPerformances(
      type: $type
      year: $year
      performancePosition: $performancePosition
      userId: $userId
      subType: $subType
    ) {
      rowKey
      id
      year
      plan {
        id
      }
      period
      subType
      status
      objectiveStatus
      evaluationStatus
      objectiveStartedAt
      objectiveExpiredAt
      evaluationStartedAt
      evaluationExpiredAt
      canModifyEvaluationJob
      evaluationJob {
        label
        value
      }
      user {
        id
        name
        idc
      }
      evaluations {
        id
        type
        status
        users {
          user {
            id
            name
          }
          comments {
            summary
            improve
            infoSummary
            infoImprove
            reason
          }
          status
          result
          evaluatedAt
          isEvaluated
        }
        isCurrentStep
      }
      haveTarget
    }
  }
`;

export function useUserPerformances(
  options?: QueryHookOptions<UserPerformancesQueryResultData, MyAnnualPerformancesResolverArgs>
): QueryResult<UserPerformancesQueryResultData, MyAnnualPerformancesResolverArgs> {
  return useQuery<UserPerformancesQueryResultData, MyAnnualPerformancesResolverArgs>(
    GET_USER_PERFORMANCES_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}

export function useLazyUserPerformances(
  options?: QueryHookOptions<UserPerformancesQueryResultData, MyAnnualPerformancesResolverArgs>
): LazyQueryResultTuple<UserPerformancesQueryResultData, MyAnnualPerformancesResolverArgs> {
  return useLazyQuery<UserPerformancesQueryResultData, MyAnnualPerformancesResolverArgs>(
    GET_USER_PERFORMANCES_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}

export function useUserTestPerformances(
  options?: QueryHookOptions<UserPerformancesQueryResultData, {}>
): QueryResult<UserPerformancesQueryResultData, {}> {
  return useQuery<UserPerformancesQueryResultData, {}>(
    gql`
      query GetMyPerformance($type: String!) {
        myPerformances(type: $type) {
          rowKey
          id
          subType
          user {
            id
            name
            hiredAt
          }
          status
          objectiveStatus
          evaluationStatus
          lineManagers {
            id
            name
          }
          evaluations {
            id
            type
            status
            users {
              user {
                id
                name
              }
              comments {
                summary
                improve
                infoSummary
                infoImprove
                reason
              }
              status
              result
              evaluatedAt
              isEvaluated
            }
            isCurrentStep
          }
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
      variables: {
        type: 'TP',
      },
    }
  );
}

export type ValidAnnualPerformanceTargetChangeResultData = {
  validAnnualPerformanceTargetChange: ValidAnnualPerformanceTargetChangeResponse;
};
export function useLazyValidAnnualPerformanceTargetChange(
  options?: QueryHookOptions<
    ValidAnnualPerformanceTargetChangeResultData,
    QueryValidAnnualPerformanceTargetChangeArgs
  >
) {
  return useLazyQuery<
    ValidAnnualPerformanceTargetChangeResultData,
    QueryValidAnnualPerformanceTargetChangeArgs
  >(
    gql`
      query ValidAnnualPerformanceTargetChange($year: String!, $changeType: String!) {
        validAnnualPerformanceTargetChange(year: $year, changeType: $changeType) {
          code
          message
          success
          data
        }
      }
    `,
    { fetchPolicy: 'network-only', ...options }
  );
}
