import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryUserShiftScheduleArgs } from '../generated-types/graphql';

export type UserShiftScheduleData = Pick<Query, 'userShiftSchedule'>;

export const USER_SHIFT_SCHEDULE: DocumentNode = gql`
  query UserShiftSchedule($params: UserShiftScheduleParams!) {
    userShiftSchedule(params: $params) {
      data {
        blockTag
        dutyGroupId
        dutyGroupName
        dutyId
        dutyName
        groupScheduleUnicode
        id
        offDutySupplyCheckScene
        offDutySupplyProcInstanceId
        offDutyTime
        onDutySupplyCheckScene
        onDutySupplyProcInstanceId
        onDutyTime
        scheduleDate
        scheduleScene {
          code
          desc
          scene
        }
        shiftsId
        staffId
        timeInterval
        scheduleStartTime
        scheduleEndTime
        systemChange
        outWorkRecordVOs {
          bizId
          processNo
          staffScheduleId
          startTime
          endTime
          totalTime
          processStatus
        }
        lateOn
        earlyOff
        missOnCard
        missOffCard
      }
      total
    }
  }
`;

export const useUserShiftSchedule = (
  options?: QueryHookOptions<UserShiftScheduleData, QueryUserShiftScheduleArgs>
): QueryResult<UserShiftScheduleData, QueryUserShiftScheduleArgs> =>
  useQuery(USER_SHIFT_SCHEDULE, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyUserShiftSchedule = (
  options?: LazyQueryHookOptions<UserShiftScheduleData, QueryUserShiftScheduleArgs>
): LazyQueryResultTuple<UserShiftScheduleData, QueryUserShiftScheduleArgs> =>
  useLazyQuery(USER_SHIFT_SCHEDULE, {
    fetchPolicy: 'network-only',
    ...options,
  });
