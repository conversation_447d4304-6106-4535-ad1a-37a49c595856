import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

export const GET_PERFORMANCE_PUBLIC_RESULT_CONFIGURE = gql`
  query GetPerformancePublicResultConfigure($year: String!) {
    performancePublicityConfigure(year: $year) {
      year
      period
      published
    }
  }
`;

export type PerformancePublicResultConfigureResultData = {
  performancePublicityConfigure?: { year: string; period: string; published: boolean }[];
};

export function usePerformancePublicResultConfigure(
  options?: QueryHookOptions<PerformancePublicResultConfigureResultData, { year: string }>
): QueryResult<PerformancePublicResultConfigureResultData, { year: string }> {
  return useQuery<PerformancePublicResultConfigureResultData, { year: string }>(
    GET_PERFORMANCE_PUBLIC_RESULT_CONFIGURE,
    { fetchPolicy: 'network-only', ...options }
  );
}

export function useLazyPerformancePublicResultConfigure(
  options?: QueryHookOptions<PerformancePublicResultConfigureResultData, { year: string }>
): LazyQueryResultTuple<PerformancePublicResultConfigureResultData, { year: string }> {
  return useLazyQuery<PerformancePublicResultConfigureResultData, { year: string }>(
    GET_PERFORMANCE_PUBLIC_RESULT_CONFIGURE,
    { fetchPolicy: 'network-only', ...options }
  );
}
