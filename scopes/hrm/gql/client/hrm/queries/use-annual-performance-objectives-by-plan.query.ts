import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type {
  AnnualPerformanceObjective,
  AnnualPerformanceObjectivesByPlanQuery,
  PaginatedPerformanceObjectives,
} from '../generated-types/graphql';

export type PaginatedPerformancesQueryResultData = {
  paginatedAnnualPerformanceObjectives: PaginatedPerformanceObjectives;
};

export type PlanRelatedAnnualPerformanceObjectivesResult = {
  annualPerformanceObjectivesByPlan: AnnualPerformanceObjective[];
};

export type PlanRelatedAnnualPerformanceObjectivesQueryArgs = {
  query: Omit<AnnualPerformanceObjectivesByPlanQuery, 'type' | 'subType'> & {
    type?: string;
    subType?: string;
  };
};

export function useLazyAnnualPerformanceObjectivesByPlan(
  options?: QueryHookOptions<
    PlanRelatedAnnualPerformanceObjectivesResult,
    PlanRelatedAnnualPerformanceObjectivesQueryArgs
  >
): LazyQueryResultTuple<
  PlanRelatedAnnualPerformanceObjectivesResult,
  PlanRelatedAnnualPerformanceObjectivesQueryArgs
> {
  return useLazyQuery<
    PlanRelatedAnnualPerformanceObjectivesResult,
    PlanRelatedAnnualPerformanceObjectivesQueryArgs
  >(
    gql`
      query GetAnnualPerformanceObjectivesByPlan($query: AnnualPerformanceObjectivesByPlanQuery!) {
        annualPerformanceObjectivesByPlan(query: $query) {
          rowKey
          id
          name
          subType
          type
          way
          measurements {
            id
            kpiId
            context
            ceiling
            gradeCriteria
            defaultGrade
          }
          gradeCriteria
        }
      }
    `,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
