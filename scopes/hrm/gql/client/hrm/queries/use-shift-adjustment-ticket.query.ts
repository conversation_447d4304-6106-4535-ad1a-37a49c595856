import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Query,
  QueryShiftAdjustmentTicketsByChildLeaveArgs,
} from '../generated-types/graphql';

export type QueryShiftAdjustmentTicketsByChildLeave = Pick<
  Query,
  'shiftAdjustmentTicketsByChildLeave'
>;

export const SHIFT_ADJUSTMENT_TICKETS_BY_CHILD_LEAVE: DocumentNode = gql`
  query ShiftAdjustmentTicketsByChildLeave($params: ShiftAdjustmentTicketsByChildLeaveParams!) {
    shiftAdjustmentTicketsByChildLeave(params: $params) {
      leaveType
      shiftAdjustmentTickets {
        approveStatus
        subType
        leaveInfo {
          startTime
          endTime
          startTimeStr
          endTimeStr
          totalTime
          unit
        }
        shiftAdjustments {
          timeRange
          dutyName
          dutyId
          replaceDate
          changeScheduleEndTime
          changeScheduleStartTime
          scheduleEndTime
          scheduleStartTime
        }
      }
    }
  }
`;

export const useShiftAdjustmentTicketsByChildLeave = (
  options?: QueryHookOptions<
    QueryShiftAdjustmentTicketsByChildLeave,
    QueryShiftAdjustmentTicketsByChildLeaveArgs
  >
): QueryResult<
  QueryShiftAdjustmentTicketsByChildLeave,
  QueryShiftAdjustmentTicketsByChildLeaveArgs
> =>
  useQuery(SHIFT_ADJUSTMENT_TICKETS_BY_CHILD_LEAVE, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyShiftAdjustmentTicketsByChildLeave = (
  options?: LazyQueryHookOptions<
    QueryShiftAdjustmentTicketsByChildLeave,
    QueryShiftAdjustmentTicketsByChildLeaveArgs
  >
): LazyQueryResultTuple<
  QueryShiftAdjustmentTicketsByChildLeave,
  QueryShiftAdjustmentTicketsByChildLeaveArgs
> =>
  useLazyQuery(SHIFT_ADJUSTMENT_TICKETS_BY_CHILD_LEAVE, {
    fetchPolicy: 'network-only',
    ...options,
  });
