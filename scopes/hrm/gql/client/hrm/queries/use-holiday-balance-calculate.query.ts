import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryHolidayBalanceCalculateArgs } from '../generated-types/graphql';

export type QueryHolidayBalanceCalData = Pick<Query, 'holidayBalanceCalculate'>;

export const HOLIDAY_BALANCE_CALCULATE: DocumentNode = gql`
  query HolidayBalanceCalculate(
    $staffId: Long!
    $hiredDate: String!
    $joinWorkingTime: String!
    $leaveDate: String!
  ) {
    holidayBalanceCalculate(
      staffId: $staffId
      hiredDate: $hiredDate
      joinWorkingTime: $joinWorkingTime
      leaveDate: $leaveDate
    ) {
      totalBalance
      statutoryBalance
      companyBalance
      carryOverBalance
      totalUsedBalance
      carryOverUsedBalance
      statutoryUsedBalance
      companyUsedBalance
      totalAvailableBalance
      carryOverAvailableBalance
      statutoryAvailableBalance
      companyAvailableBalance
      sickSalaryBalance
      sickSalaryUsedBalance
      sickSalaryAvailableBalance
    }
  }
`;

export const useHolidayBalanceCalculate = (
  options?: QueryHookOptions<QueryHolidayBalanceCalData, QueryHolidayBalanceCalculateArgs>
): QueryResult<QueryHolidayBalanceCalData, QueryHolidayBalanceCalculateArgs> =>
  useQuery(HOLIDAY_BALANCE_CALCULATE, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyHolidayBalanceCalculate = (
  options?: LazyQueryHookOptions<QueryHolidayBalanceCalData, QueryHolidayBalanceCalculateArgs>
): LazyQueryResultTuple<QueryHolidayBalanceCalData, QueryHolidayBalanceCalculateArgs> =>
  useLazyQuery(HOLIDAY_BALANCE_CALCULATE, {
    fetchPolicy: 'network-only',
    ...options,
  });
