import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryScheduleStaffStatisticArgs } from '../generated-types/graphql';

export type QueryScheduleStaffStatisticData = Pick<Query, 'scheduleStaffStatistic'>;

export const SCHEDULE_STAFF_STATISTIC: DocumentNode = gql`
  query scheduleStaffStatistic(
    $blockGuidList: [String!]
    $dutyGroupId: Long
    $userId: Long
    $alreadyPush: String
    $sortedFiled: String
    $sortedType: String
    $page: Long
    $pageSize: Long
  ) {
    scheduleStaffStatistic(
      blockGuidList: $blockGuidList
      dutyGroupId: $dutyGroupId
      userId: $userId
      alreadyPush: $alreadyPush
      sortedFiled: $sortedFiled
      sortedType: $sortedType
      page: $page
      pageSize: $pageSize
    ) {
      data {
        userId
        attId
        userType
        blockGuid
        attGroupName
        dutyGroupName
        shiftsName
        clockInTypes
        alreadyPush
      }
      total
    }
  }
`;

export const useScheduleStaffStatistic = (
  options?: QueryHookOptions<QueryScheduleStaffStatisticData, QueryScheduleStaffStatisticArgs>
): QueryResult<QueryScheduleStaffStatisticData, QueryScheduleStaffStatisticArgs> =>
  useQuery(SCHEDULE_STAFF_STATISTIC, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyScheduleStaffStatistic = (
  options?: LazyQueryHookOptions<QueryScheduleStaffStatisticData, QueryScheduleStaffStatisticArgs>
): LazyQueryResultTuple<QueryScheduleStaffStatisticData, QueryScheduleStaffStatisticArgs> =>
  useLazyQuery(SCHEDULE_STAFF_STATISTIC, {
    fetchPolicy: 'network-only',
    ...options,
  });
