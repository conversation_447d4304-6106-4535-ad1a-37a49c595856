import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { AnnualPerformancePlanJSON } from '@manyun/hrm.model.annual-performance-plan';

import type { PaginatedPerformancePlansQuery } from '../generated-types/graphql';

export type PaginatedPerformancesQueryResultData = {
  paginatedPerformancePlans: {
    data: AnnualPerformancePlanJSON[];
    total: number;
  };
};

export type PaginatedPerformancePlansArgs = {
  query: PaginatedPerformancePlansQuery;
};

export const GET_PAGINATED_PERFORMANCE_PLANS_QUERY = gql`
  query GetPaginatedPerformancePlans($query: PaginatedPerformancePlansQuery!) {
    paginatedPerformancePlans(query: $query) {
      data {
        id
        name
        year
        splitRule
        positionScope {
          label
          value
        }
        resourcesScope {
          label
          value
        }
        createdBy {
          id
          name
        }
        createdAt
        modifiedAt
        canModified
      }
      total
    }
  }
`;

export function usePaginatedPerformancePlans(
  options?: QueryHookOptions<PaginatedPerformancesQueryResultData, PaginatedPerformancePlansArgs>
): QueryResult<PaginatedPerformancesQueryResultData, PaginatedPerformancePlansArgs> {
  return useQuery<PaginatedPerformancesQueryResultData, PaginatedPerformancePlansArgs>(
    GET_PAGINATED_PERFORMANCE_PLANS_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}

export function useLazyPaginatedPerformancePlans(
  options?: QueryHookOptions<PaginatedPerformancesQueryResultData, PaginatedPerformancePlansArgs>
): LazyQueryResultTuple<PaginatedPerformancesQueryResultData, PaginatedPerformancePlansArgs> {
  return useLazyQuery<PaginatedPerformancesQueryResultData, PaginatedPerformancePlansArgs>(
    GET_PAGINATED_PERFORMANCE_PLANS_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
