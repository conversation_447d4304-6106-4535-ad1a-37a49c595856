import { gql, useLazyQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions } from '@apollo/client';

import type { AnnualPerformanceObjectiveNext } from '../generated-types/graphql';

export type AnnualObjectiveResultData = {
  annualPerformanceObjectiveById: AnnualPerformanceObjectiveNext | null;
};

export const GET_ANNUAL_PERFORMANCE_OBJECTIVE_BY_ID_QUERY = gql`
  query GetAnnualPerformanceObjective($id: Int!) {
    annualPerformanceObjectiveById(id: $id) {
      rowKey
      id
      name
      resources {
        label
        value
      }
      positions {
        label
        value
      }
      way
      type
      subType
      measurements {
        id
        kpiId
        context
        ceiling
        gradeCriteria
        defaultGrade
      }
      measurementsLegacy
      gradeCriteria
      reason
    }
  }
`;

/**
 * @description 获取年度指标详情
 * @param options
 * @returns
 */
export function useLazyAnnualPerformanceObjective(
  options?: QueryHookOptions<AnnualObjectiveResultData, { id: number }>
): LazyQueryResultTuple<AnnualObjectiveResultData, { id: number }> {
  return useLazyQuery<AnnualObjectiveResultData, { id: number }>(
    GET_ANNUAL_PERFORMANCE_OBJECTIVE_BY_ID_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
