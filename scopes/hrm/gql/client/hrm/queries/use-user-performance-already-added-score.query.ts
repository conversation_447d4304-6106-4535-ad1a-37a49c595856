import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { Query, QueryUserPerformanceAlreadyAddedScoreArgs } from '../generated-types/graphql';

export type UserPerformanceAlreadyAddedScoreResultData = Pick<
  Query,
  'userPerformanceAlreadyAddedScore'
>;

export const GET_USER_PERFORMANCE_ADDED_GRADE_QUERY = gql`
  query UserPerformanceAlreadyAddedScore($userId: Long!, $metricsId: Long!, $occurTime: Long!) {
    userPerformanceAlreadyAddedScore(
      userId: $userId
      metricsId: $metricsId
      occurTime: $occurTime
    ) {
      data
    }
  }
`;

export function useUserPerformanceAlreadyAddedScore(
  options?: QueryHookOptions<
    UserPerformanceAlreadyAddedScoreResultData,
    QueryUserPerformanceAlreadyAddedScoreArgs
  >
): QueryResult<
  UserPerformanceAlreadyAddedScoreResultData,
  QueryUserPerformanceAlreadyAddedScoreArgs
> {
  return useQuery<
    UserPerformanceAlreadyAddedScoreResultData,
    QueryUserPerformanceAlreadyAddedScoreArgs
  >(GET_USER_PERFORMANCE_ADDED_GRADE_QUERY, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
export function useLazyUserPerformanceAlreadyAddedScore(
  options?: QueryHookOptions<
    UserPerformanceAlreadyAddedScoreResultData,
    QueryUserPerformanceAlreadyAddedScoreArgs
  >
): LazyQueryResultTuple<
  UserPerformanceAlreadyAddedScoreResultData,
  QueryUserPerformanceAlreadyAddedScoreArgs
> {
  return useLazyQuery<
    UserPerformanceAlreadyAddedScoreResultData,
    QueryUserPerformanceAlreadyAddedScoreArgs
  >(GET_USER_PERFORMANCE_ADDED_GRADE_QUERY, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
