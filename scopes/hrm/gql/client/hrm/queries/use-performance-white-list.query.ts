import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryPerformanceWhiteListArgs } from '../generated-types/graphql';

export type PerformanceWhiteListData = Pick<Query, 'performanceWhiteList'>;
export type PerformanceWhiteListYearsData = Pick<Query, 'performanceWhiteListYears'>;

export const PAGINATED_PERFORMANCE_WHITE_LIST: DocumentNode = gql`
  query PerformanceWhiteList($query: PerformanceWhiteListQuery!) {
    performanceWhiteList(query: $query) {
      data {
        id
        year
        staffId
        staffName
        staffJobNo
        periods
        createdBy {
          id
          name
        }
        modifiedBy {
          id
          name
        }
        createdAt
        modifiedAt
      }
      total
    }
  }
`;

export const usePerformanceWhiteList = (
  options?: QueryHookOptions<PerformanceWhiteListData, QueryPerformanceWhiteListArgs>
): QueryResult<PerformanceWhiteListData, QueryPerformanceWhiteListArgs> =>
  useQuery(PAGINATED_PERFORMANCE_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyPerformanceWhiteList = (
  options?: LazyQueryHookOptions<PerformanceWhiteListData, QueryPerformanceWhiteListArgs>
): LazyQueryResultTuple<PerformanceWhiteListData, QueryPerformanceWhiteListArgs> =>
  useLazyQuery(PAGINATED_PERFORMANCE_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const PERFORMANCE_WHITE_LIST_YEARS: DocumentNode = gql`
  query PerformanceWhiteListYears {
    performanceWhiteListYears {
      data
    }
  }
`;

export const usePerformanceWhiteListYears = (
  options?: QueryHookOptions<PerformanceWhiteListYearsData, {}>
): QueryResult<PerformanceWhiteListYearsData, {}> =>
  useQuery(PERFORMANCE_WHITE_LIST_YEARS, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyPerformanceWhiteListYears = (
  options?: LazyQueryHookOptions<PerformanceWhiteListYearsData, {}>
): LazyQueryResultTuple<PerformanceWhiteListYearsData, {}> =>
  useLazyQuery(PERFORMANCE_WHITE_LIST_YEARS, {
    fetchPolicy: 'network-only',
    ...options,
  });
