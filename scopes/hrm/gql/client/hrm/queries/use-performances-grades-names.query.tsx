import { gql, useQuery } from '@apollo/client';
import type { QueryHookOptions, QueryResult } from '@apollo/client';

import type { Query, QueryPerformanceGradesNamesArgs } from '../generated-types/graphql';

export type PerformanceGradesNamesResultData = Pick<Query, 'performanceGradesNames'>;

export const GET_PERFORMANCE_GRADE_NAME_QUERY = gql`
  query PerformanceGradesNames($type: BackendAnnualPerformanceObjectiveType!) {
    performanceGradesNames(type: $type) {
      data
    }
  }
`;

export function usePerformanceGradesNames(
  options?: QueryHookOptions<PerformanceGradesNamesResultData, QueryPerformanceGradesNamesArgs>
): QueryResult<PerformanceGradesNamesResultData, QueryPerformanceGradesNamesArgs> {
  return useQuery<PerformanceGradesNamesResultData, QueryPerformanceGradesNamesArgs>(
    GET_PERFORMANCE_GRADE_NAME_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}
