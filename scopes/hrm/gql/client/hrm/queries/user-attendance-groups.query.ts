import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query } from '../generated-types/graphql';

export type { PersonalAttendanceRulesInfo } from '../generated-types/graphql';

export type UserAttendanceGroupsData = Pick<Query, 'userAttendanceGroups'>;
export const USER_ATTENDANCE_GROUPS: DocumentNode = gql`
  query UserAttendanceGroups {
    userAttendanceGroups {
      data {
        id
        attName
        idcTag
        blockGuid
        checkChannel
        rule {
          overtimeBeforeDays
          leaveBeforeDays
          outWorkBeforeDays
          enableSupplyTime
          supplyValidDays
          outWorkTotalHours
          supplyCountByMonth
          enableSupply
          enableSupplyCount
          leaveNotValidStartDay
          overtimeNotValidStartDay
          overtimeConfirmValidDays
          overtimeConfirmNotValidStartDay
          outWorkNotValidStartDay
          supplyNotValidStartDay
          combineLeaves
        }
        blockCoordinate {
          blockGuid
          area
          coordinate
          longitude
          latitude
        }
      }
    }
  }
`;

export const useUserAttendanceGroupsData = (
  options?: QueryHookOptions<UserAttendanceGroupsData, {}>
): QueryResult<UserAttendanceGroupsData, {}> =>
  useQuery(USER_ATTENDANCE_GROUPS, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyUserAttendanceGroupsData = (
  options?: LazyQueryHookOptions<UserAttendanceGroupsData, {}>
): LazyQueryResultTuple<UserAttendanceGroupsData, {}> =>
  useLazyQuery(USER_ATTENDANCE_GROUPS, {
    fetchPolicy: 'network-only',
    ...options,
  });
