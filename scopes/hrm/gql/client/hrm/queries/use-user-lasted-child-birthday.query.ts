import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryUserLastedChildBirthdayArgs } from '../generated-types/graphql';

export type QueryUserLastedChildBirthday = Pick<Query, 'userLastedChildBirthday'>;

export const USER_LASTED_CHILD_BIRTHDAY: DocumentNode = gql`
  query UserLastedChildBirthday($userId: String!) {
    userLastedChildBirthday(userId: $userId) {
      birthday
    }
  }
`;

export const useUserLastedChildBirthday = (
  options?: QueryHookOptions<QueryUserLastedChildBirthday, QueryUserLastedChildBirthdayArgs>
): QueryResult<QueryUserLastedChildBirthday, QueryUserLastedChildBirthdayArgs> =>
  useQuery(USER_LASTED_CHILD_BIRTHDAY, {
    fetchPolicy: 'network-only',
    ...options,
  });

export const useLazyUserLastedChildBirthday = (
  options?: LazyQueryHookOptions<QueryUserLastedChildBirthday, QueryUserLastedChildBirthdayArgs>
): LazyQueryResultTuple<QueryUserLastedChildBirthday, QueryUserLastedChildBirthdayArgs> =>
  useLazyQuery(USER_LASTED_CHILD_BIRTHDAY, {
    fetchPolicy: 'network-only',
    ...options,
  });
