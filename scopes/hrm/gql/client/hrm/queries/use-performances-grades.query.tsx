import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { DailyPerformanceGradeJSON } from '@manyun/hrm.model.daily-performance-grade';
import type { SvcQuery } from '@manyun/hrm.service.fetch-paged-performance-daily-grade-records';

export type PaginatedPerformancesQueryResultData = {
  paginatedPerformanceGrades: {
    data: DailyPerformanceGradeJSON[];
    total: number;
  };
};

export const GET_PAGINATED_PERFORMANCE_GRADES_QUERY = gql`
  fragment MyMcUploadFile on McUploadFile {
    uid
    name
    patialPath
    uploadUser {
      id
      name
    }
    uploadedAt
    ext
    src
    id
    size
    type
    modifiedAt
    targetId
    targetType
  }

  query GetPaginatedPerformanceGrades($query: PaginatedPerformanceGradesQuery!) {
    paginatedPerformanceGrades(query: $query) {
      data {
        id
        user {
          id
          name
          superiors {
            id
            name
          }
          region {
            label
            value
          }
          idc {
            label
            value
          }
          block {
            label
            value
          }
          jobNo
        }
        performancePosition {
          label
          value
        }
        name
        type
        subType
        measurements
        gradeCriteria {
          id
          defaultGrade
          criteria
        }
        gradeDesc
        grade
        occurTime
        createdBy {
          id
          name
        }
        modifiedBy {
          id
          name
        }
        canModified
        createdAt
        modifiedAt
        attachments {
          ...MyMcUploadFile
        }
        instStatus
        instId
        taskId
        needApproval
      }
      total
    }
  }
`;

export function usePaginatedPerformanceGrades(
  options?: QueryHookOptions<PaginatedPerformancesQueryResultData, { query: SvcQuery }>
): QueryResult<PaginatedPerformancesQueryResultData, { query: SvcQuery }> {
  return useQuery<PaginatedPerformancesQueryResultData, { query: SvcQuery }>(
    GET_PAGINATED_PERFORMANCE_GRADES_QUERY,
    { fetchPolicy: 'network-only', ...options }
  );
}

export type PerformanceGradeQueryResultData = {
  performanceGradeById: DailyPerformanceGradeJSON | null;
};

export const GET_PERFORMANCE_GRADE_QUERY = gql`
  fragment MyMcUploadFile on McUploadFile {
    uid
    name
    patialPath
    uploadUser {
      id
      name
    }
    uploadedAt
    ext
    src
    id
    size
    type
    modifiedAt
    targetId
    targetType
  }
  query GetPerformanceGrade($id: Int!) {
    performanceGradeById(id: $id) {
      id
      user {
        id
        name
      }
      relatedObjectiveId
      name
      type
      subType
      measurements
      gradeCriteria {
        id
        defaultGrade
        criteria
      }
      gradeDesc
      grade
      occurTime
      attachments {
        ...MyMcUploadFile
      }
      instStatus
    }
  }
`;

export function useLazyPerformanceGrade(
  options?: QueryHookOptions<PerformanceGradeQueryResultData, { id: number }>
): LazyQueryResultTuple<PerformanceGradeQueryResultData, { id: number }> {
  return useLazyQuery<PerformanceGradeQueryResultData, { id: number }>(
    GET_PERFORMANCE_GRADE_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
