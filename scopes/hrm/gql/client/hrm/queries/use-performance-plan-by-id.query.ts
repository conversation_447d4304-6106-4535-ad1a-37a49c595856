import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type { LazyQueryResultTuple, QueryHookOptions, QueryResult } from '@apollo/client';

import type { AnnualPerformanceObjectiveJSON } from '@manyun/hrm.model.annual-performance-objective';
import type { AnnualPerformancePlanJSON } from '@manyun/hrm.model.annual-performance-plan';

export type PerformanceGradeQueryResultData = {
  performancePlanById:
    | (AnnualPerformancePlanJSON & { relatedObjectives: AnnualPerformanceObjectiveJSON[] })
    | null;
};

export const GET_PERFORMANCE_PLAN_QUERY = gql`
  query GetPerformancePlanById(
    $id: Int
    $year: String
    $includeRelatedObjectives: Boolean
    $relatedObjectivesQuery: PerformancePlanRelatedObjectivesQuery
  ) {
    performancePlanById(
      id: $id
      year: $year
      includeRelatedObjectives: $includeRelatedObjectives
      relatedObjectivesQuery: $relatedObjectivesQuery
    ) {
      id
      year
      name
      splitRule
      resourcesScope {
        label
        value
        parentCode
      }
      positionScope {
        label
        value
      }
      notEvalHiredDateLaterThanDate
      subConfigs {
        title
        period
        natureStartTimeRange
        evalStartTimeRange
      }
      beforeSubTaskDeadlineNotifyDays
      canSetResultDate
      canModified
      relatedObjectives {
        id
        rowKey
        name
        subType
        type
        way
        measurements {
          id
          kpiId
          context
          ceiling
          gradeCriteria
          defaultGrade
        }
        measurementsLegacy
        gradeCriteria
        resources {
          label
          value
        }
        positions {
          label
          value
        }
      }
    }
  }
`;

type PerformancePlanByIdResolverArgs = {
  id?: number;
  year?: string;
  includeRelatedObjectives?: boolean;
  relatedObjectivesQuery?: {
    resourceCode?: string;
    evalPosition?: string;
  };
};

export function useLazyPerformancePlanById(
  options?: QueryHookOptions<PerformanceGradeQueryResultData, PerformancePlanByIdResolverArgs>
): LazyQueryResultTuple<PerformanceGradeQueryResultData, PerformancePlanByIdResolverArgs> {
  return useLazyQuery<PerformanceGradeQueryResultData, PerformancePlanByIdResolverArgs>(
    GET_PERFORMANCE_PLAN_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}

export function usePerformancePlanById(
  options?: QueryHookOptions<PerformanceGradeQueryResultData, PerformancePlanByIdResolverArgs>
): QueryResult<PerformanceGradeQueryResultData, PerformancePlanByIdResolverArgs> {
  return useQuery<PerformanceGradeQueryResultData, PerformancePlanByIdResolverArgs>(
    GET_PERFORMANCE_PLAN_QUERY,
    {
      fetchPolicy: 'network-only',
      ...options,
    }
  );
}
