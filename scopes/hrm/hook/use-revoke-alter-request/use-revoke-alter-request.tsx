import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { revokeAlterRequest } from '@manyun/hrm.service.revoke-alter-request';
import type { SvcQuery } from '@manyun/hrm.service.revoke-alter-request';

export function useRevokeAlterRequest() {
  const [loading, setLoading] = useState(false);

  const _revokeAlterRequest = useCallback(async (fields: SvcQuery, success?: () => void) => {
    setLoading(true);
    const { error } = await revokeAlterRequest({ ...fields });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    if (success) {
      success();
    }
    message.success('操作成功！');
  }, []);

  return [loading, _revokeAlterRequest] as const;
}
