import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { fetchUserAttendanceGroups } from '@manyun/hrm.service.fetch-user-attendance-groups';
import type { BackendAttendanceGroupInfos } from '@manyun/hrm.service.fetch-user-attendance-groups';

export function useUserAttendanceGroup() {
  const [attendance, setAttendance] = useState<BackendAttendanceGroupInfos | null>(null);
  const [loading, setLoading] = useState(false);

  const onLoadAttendance = useCallback(
    async (userId: number, callback?: (data: BackendAttendanceGroupInfos | null) => void) => {
      setLoading(true);
      const { error, data } = await fetchUserAttendanceGroups({ staffId: userId });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setAttendance(data);
      callback?.(data);
    },
    []
  );

  return [{ loading, attendance }, onLoadAttendance] as const;
}
