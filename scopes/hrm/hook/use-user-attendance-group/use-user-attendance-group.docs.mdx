---
description: 'A React Hook that increments a count by 1 each time it is called.'
labels: ['hook', 'counter']
---

## A React Hook to increment a count

### Component usage

```js
import { useUserAttendanceGroup } from './use-user-attendance-group';

import { useUserAttendanceGroup } from './use-user-attendance-group';

const [{loading},onLoadAttendance] = useUserAttendanceGroup();

<h1>The count is {loading}</h1>
<button onClick={()=>{
  onLoadAttendance(1)
  }}>加载数据</button>
```
