import React from 'react';

import { Typography } from '@manyun/base-ui.ui.typography';

import { useBlockSchedule } from './use-block-schedule';

export const BasicuseBlockSchedule = () => {
  const { userScheduleDataSource } = useBlockSchedule({
    idc: 'EC06',
    dayType: 'today',
    block: 'A',
  });

  return (
    <>
      {userScheduleDataSource.map(data => (
        <Typography.Text style={{ fontSize: 14 }} ellipsis>
          {data.staffName}
        </Typography.Text>
      ))}
    </>
  );
};
