import React, { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import dayjs from 'dayjs';
import sortBy from 'lodash.sortby';

import type { StaffScheduleDayType } from '@manyun/hrm.state.staff-schedule';
import { getStaffScheduleAction, selectStaffSchedule } from '@manyun/hrm.state.staff-schedule';

export type BlockScheduleType = {
  idc: string;
  block: string;
  /** 排班： 今日｜明日 */
  dayType: StaffScheduleDayType;
};

/**
 * 指定楼栋下的用户排班
 * @returns
 */
export function useBlockSchedule(props: BlockScheduleType) {
  const { idc, dayType = 'today', block } = props;
  const blockGuid = idc + '.' + block;
  const staffSchedule = useSelector(selectStaffSchedule(dayType));
  const dispatch = useDispatch();

  /** 获取用户排班 */
  const userScheduleDataSource = React.useMemo(() => {
    if (!block) {
      return [];
    }
    if (staffSchedule) {
      if (dayType === 'today') {
        return sortBy(staffSchedule, userShift => userShift.inDuty === '缺岗');
      }

      return staffSchedule;
    }
    return [];
  }, [block, staffSchedule, dayType]);

  const handleQuerySchedule = useCallback(() => {
    dispatch(
      getStaffScheduleAction(
        dayType === 'tomorrow'
          ? {
              idc,
              type: dayType,
              blockGuid,
              scheduleDate: dayjs(dayjs().add(1, 'day')).startOf('day').valueOf(),
            }
          : {
              idc,
              type: dayType,
              blockGuid,
            }
      )
    );
  }, [blockGuid, dayType, dispatch, idc]);

  return { userScheduleDataSource, handleQuerySchedule };
}
