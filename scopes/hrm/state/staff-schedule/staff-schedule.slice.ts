import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import { BackendSchedule } from '@manyun/hrm.service.fetch-paged-users-shift-schedule';
import { StaffScheduleModel } from '@manyun/hrm.service.fetch-staff-schedule';

export type StaffScheduleDayType = 'today' | 'tomorrow';

export type AllScheduleType = Partial<StaffScheduleModel> & Partial<BackendSchedule>;

export type StaffScheduleSliceState = {
  total: number;
  loading: boolean;
  staffSchedule: Record<StaffScheduleDayType, AllScheduleType[]>;
};

type StaffScheduleSliceCaseReducers = {
  fetchStaffScheduleStart: CaseReducer<StaffScheduleSliceState, PayloadAction<undefined>>;
  setStaffSchedule: CaseReducer<
    StaffScheduleSliceState,
    PayloadAction<{
      staffScheduleList: AllScheduleType[];
      type: StaffScheduleDayType;
      blockGuid: string;
    }>
  >;
  fetchStaffScheduleError: CaseReducer<StaffScheduleSliceState, PayloadAction<undefined>>;
};

export const staffScheduleSlice = createSlice<
  StaffScheduleSliceState,
  StaffScheduleSliceCaseReducers,
  'hrm.staff-schedule'
>({
  name: 'hrm.staff-schedule',
  initialState: {
    staffSchedule: {
      today: [],
      tomorrow: [],
    },
    total: 0,
    loading: false,
  },
  reducers: {
    fetchStaffScheduleStart(sliceState) {
      sliceState.loading = true;
    },

    setStaffSchedule(sliceState, { payload: { blockGuid, type, staffScheduleList } }) {
      staffScheduleList = staffScheduleList.filter(
        schedule =>
          (schedule.blockGuids ?? []).includes(blockGuid) || blockGuid === schedule.blockTag
      );

      sliceState.staffSchedule[type] = staffScheduleList;

      sliceState.loading = false;
    },
    fetchStaffScheduleError(sliceState) {
      sliceState.loading = false;
    },
  },
});
