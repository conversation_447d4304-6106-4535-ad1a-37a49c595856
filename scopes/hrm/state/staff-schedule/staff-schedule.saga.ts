import { call, fork, put, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { fetchPagedUsersShiftSchedule } from '@manyun/hrm.service.fetch-paged-users-shift-schedule';
import { fetchStaffSchedule } from '@manyun/hrm.service.fetch-staff-schedule';

import { getStaffScheduleAction, staffScheduleSliceActions } from './staff-schedule.action';

/** Workers */

export function* getStaffScheduleSaga({
  payload: { idc, type, scheduleDate, blockGuid },
}: ReturnType<typeof getStaffScheduleAction>) {
  yield put(staffScheduleSliceActions.fetchStaffScheduleStart());

  const params: {
    idcTag: string;
    scheduleDate?: number;
    needOvertime?: boolean;
  } = { idcTag: idc };

  if (type === 'tomorrow') {
    params.scheduleDate = scheduleDate;
    /**明日需显示加班班次 */
    params.needOvertime = true;
  }

  const { error, data }: SagaReturnType<typeof fetchStaffSchedule> = yield call(
    type === 'today' ? fetchStaffSchedule : fetchPagedUsersShiftSchedule,
    params
  );

  if (error) {
    yield put(staffScheduleSliceActions.fetchStaffScheduleError());
    return;
  }

  yield put(
    staffScheduleSliceActions.setStaffSchedule({
      staffScheduleList: data.data,
      type,
      blockGuid,
    })
  );
}

/** Watchers */

function* watchGetStaffSchedule() {
  yield takeLatest(getStaffScheduleAction.type, getStaffScheduleSaga);
}

export default [fork(watchGetStaffSchedule)];
