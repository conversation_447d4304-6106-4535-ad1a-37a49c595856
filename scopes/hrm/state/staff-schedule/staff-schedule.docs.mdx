---
description: 'StaffSchedule redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import staffScheduleSliceReducer from '@manyun/[scope].state.staff-schedule';

const rootReducer = {
  ...staffScheduleSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { staffScheduleWatchers } from '@manyun/[scope].state.staff-schedule';

const function* rootSaga() {
  yield all(
    ...staffScheduleWatchers,
    // other sagas...
  );
};
```