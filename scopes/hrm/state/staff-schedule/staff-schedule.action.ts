import { createAction } from '@reduxjs/toolkit';

import { StaffScheduleDayType, staffScheduleSlice } from './staff-schedule.slice';

const prefix = `${staffScheduleSlice.name}/` as const;
const postfix = '--async' as const;

export const staffScheduleSliceActions = staffScheduleSlice.actions;

export type StaffScheduleType = {
  idc: string;
  type: StaffScheduleDayType;
  blockGuid: string;
  scheduleDate?: number;
};
const getStaffScheduleActionType = `${prefix}get-staff-schedule${postfix}` as const;
/**
 * 获取用户排班
 */
export const getStaffScheduleAction = createAction<StaffScheduleType>(getStaffScheduleActionType);
