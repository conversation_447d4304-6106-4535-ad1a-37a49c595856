/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-23
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-cert-list.type';

const endpoint = '/pm/cert/page';

/**
 * 证书查询
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18879)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    // const params: ApiQ = { variant };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
