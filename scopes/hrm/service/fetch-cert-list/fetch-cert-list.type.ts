/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-23
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = { userId: number };

export type CertificateList = BackCertificate;
export type BackCertificate = {
  id: number;
  userId: number;
  certName: string;
  effectStartDate: string;
  effectEndDate: string;
  fileId: number;
  checkDate?: string;
  effect: boolean;
  fileType: string;
};

export type SvcRespData = {
  data: CertificateList[];
  total: number;
};

export type RequestRespData = {
  data: BackCertificate[];
  total: number;
} | null;

export type ApiQ = {
  userId: number;
  excludeCustom?: boolean;
};

export type ApiR = ListResponse<BackCertificate[] | null>;
