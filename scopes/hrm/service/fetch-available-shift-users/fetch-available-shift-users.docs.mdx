---
description: 'A fetchAvailableShiftUsers HTTP API service.'
labels: ['service', 'http', fetch-available-shift-users]
---

## 概述

查询可进行排班的用户

## 使用

### Browser

```ts
import { fetchAvailableShiftUsers } from '@manyun/dc-brain.service.fetch-available-shift-users';

const { error, data } = await fetchAvailableShiftUsers('success');
const { error, data } = await fetchAvailableShiftUsers('error');
```

### Node

```ts
import { fetchAvailableShiftUsers } from '@manyun/dc-brain.service.fetch-available-shift-users/dist/index.node';

const { data } = await fetchAvailableShiftUsers('success');

try {
  const { data } = await fetchAvailableShiftUsers('error');
} catch (error) {
  // ...
}
```
