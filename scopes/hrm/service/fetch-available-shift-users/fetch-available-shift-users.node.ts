/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-28
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-available-shift-users';
import type { SvcQuery, SvcRespData } from './fetch-available-shift-users.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAvailableShiftUsers(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
