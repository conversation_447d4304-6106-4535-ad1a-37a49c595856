/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-28
 *
 * @packageDocumentation
 */
import { User } from '@manyun/auth-hub.model.user';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-available-shift-users.type';

const endpoint = '/pm/schedule/scheduleUsers';

/**
 *  查询可进行排班的用户
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/17880)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      resourceCode: variant.resourceCode,
      dutyGroupId: variant.dutyGroupId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => User.fromApiObject(d)), total: data.total },
      ...rest,
    };
  };
}
