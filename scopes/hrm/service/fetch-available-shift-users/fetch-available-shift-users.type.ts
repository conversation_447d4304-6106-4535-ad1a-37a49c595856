/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-28
 *
 * @packageDocumentation
 */
import { BackendUser, User } from '@manyun/auth-hub.model.user';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  resourceCode: string; //资源code（楼栋guid）
  dutyGroupId?: string;
};

export type SvcRespData = {
  data: User[];
  total: number;
};

export type RequestRespData = {
  data: BackendUser[] | null;
  total: number;
} | null;

export type ApiQ = {
  resourceCode: string;
  dutyGroupId?: string;
};

export type ApiR = ListResponse<BackendUser[] | null>;
