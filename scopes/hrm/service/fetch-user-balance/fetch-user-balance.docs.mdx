---
description: 'A fetchUserBalance HTTP API service.'
labels: ['service', 'http', fetch-user-balance]
---

## 概述

个人账户余额查询

## 使用

### Browser

```ts
import { fetchUserBalance } from '@manyun/hrm.service.fetch-user-balance';

const { error, data } = await fetchUserBalance('success');
const { error, data } = await fetchUserBalance('error');
```

### Node

```ts
import { fetchUserBalance } from '@manyun/hrm.service.fetch-user-balance/dist/index.node';

const { data } = await fetchUserBalance('success');

try {
  const { data } = await fetchUserBalance('error');
} catch (error) {
  // ...
}
```
