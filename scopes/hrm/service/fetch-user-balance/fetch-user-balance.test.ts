/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserBalance as webService } from './fetch-user-balance.browser';
import { fetchUserBalance as nodeService } from './fetch-user-balance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ staffId: 1 });
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('totalAvailableAnnualLeave');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ staffId: 1 });
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('totalAvailableAnnualLeave');
});
