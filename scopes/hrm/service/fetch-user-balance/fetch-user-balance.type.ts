/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendUserBalance = {
  staffId: number;
  totalAvailableAnnualLeave: number /**可用年假 =可用: 法定年假 + 公司年假 + 上年结转年假 */;
  alreadyUseAnnualLeave: number /** 已使用年假*/;
  carryOverAnnualLeave: number /** 上年结转年假*/;
  alreadyGiveAnnualLeave: number /**今年发放年假 */;
  sickSalaryBalance: number /**带薪病假余额 */;
  alreadyUseSickSalary: number /**已使用带薪病假 */;
  alreadyGiveSickSalary: number /** 今年发放带薪病假*/;
  paidLeaveBalance: number /** 调休余额*/;
  alreadyUsePaidLeave: number /**累计使用 */;
  alreadyGivePaidLeave: number /**累计发放 */;
  closeExpire: boolean /**是否过期 */;
  freezeAnnualLeave: number /** 审批中年假*/;
  freezeSalaryBalance: number /**审批中病假 */;
  freezePaidLeaveBalance: number /**审批中调休 */;
  alreadyGiveCompany: number /** 今年发放公司年假*/;
  alreadyGiveStatutory: number /**今年发放法定年假 */;
};

export type UserBalance = {
  staffId: number;
  totalAvailableAnnualLeave: number;
  alreadyUseAnnualLeave: number;
  carryOverAnnualLeave: number;
  alreadyGiveAnnualLeave: number;
  sickSalaryBalance: number;
  alreadyUseSickSalary: number;
  alreadyGiveSickSalary: number;
  paidLeaveBalance: number;
  alreadyUsePaidLeave: number;
  alreadyGivePaidLeave: number;
  closeExpire: boolean;
  freezeAnnualLeave: number;
  freezeSalaryBalance: number;
  freezePaidLeaveBalance: number;
  alreadyGiveCompany: number;
  alreadyGiveStatutory: number;
};

export type SvcQuery = {
  staffId: number;
};

export type SvcRespData = UserBalance | null;

export type RequestRespData = BackendUserBalance | null;

export type ApiQ = {
  staffId: number;
};

export type ApiR = Response<RequestRespData>;
