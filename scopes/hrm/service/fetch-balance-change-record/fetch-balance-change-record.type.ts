/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export enum BalanceType {
  /**调休 */ PAID_LEAVE = 'PAID_LEAVE',
  /** 全薪病假*/ SICK_SALARY = 'SICK_SALARY',
  /**公司年假*/ COMPANY_LEAVE = 'COMPANY_LEAVE',
  /**法定年假*/ STATUTORY_ANNUAL_LEAVE = 'STATUTORY_ANNUAL_LEAVE',
  /**结转年假*/ CARRY_OVER_COMPANY_LEAVE = 'CARRY_OVER_COMPANY_LEAVE',
}
export const BALANCE_TYPE_MAPPER: Record<string, string> = {
  [BalanceType.PAID_LEAVE]: '调休',
  [BalanceType.SICK_SALARY]: '全薪病假',
  [BalanceType.COMPANY_LEAVE]: '公司年假',
  [BalanceType.STATUTORY_ANNUAL_LEAVE]: '法定年假',
  [BalanceType.CARRY_OVER_COMPANY_LEAVE]: '结转年假',
};

export type SvcQuery = {
  staffId: number;
  balanceTypes: BalanceType[];
  timeRange?: [number, number];
};

export type BackendChangeRecord = {
  id: number;
  bizId: string;
  balanceType: BalanceType /**假期类型 */;
  startBalance: number /**开始余额 */;
  endBalance: number /**结束余额 */;
  reason: string /**变更专业 */;
  createTime: number /**创建时间 */;
  operatorId: null | number /**操作人 */;
  fileList: null | McUploadFile[] /**附件 */;
};

export type ChangeRecord = BackendChangeRecord;

export type SvcRespData = {
  data: ChangeRecord[];
  total: number;
};

export type RequestRespData = {
  data: BackendChangeRecord[];
  total: number;
} | null;

export type ApiQ = {
  staffId: number;
  balanceTypeList: BalanceType[];
  startDate?: number;
  endDate?: number;
};

export type ApiR = ListResponse<BackendChangeRecord[] | null>;
