/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-balance-change-record.type';

const endpoint = '/dctrans/balance/changeRecord';

/**
 * 查询余额变更记录
 * @see [Doc](YAPI http://172.16.0.17:13000/project/124/interface/api/18080)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      staffId: variant.staffId,
      balanceTypeList: variant.balanceTypes,
      startDate: variant.timeRange && variant.timeRange[0],
      endDate: variant.timeRange && variant.timeRange[1],
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data, total: data.total },
      ...rest,
    };
  };
}
