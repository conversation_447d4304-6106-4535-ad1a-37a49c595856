---
description: 'A fetchBalanceChangeRecord HTTP API service.'
labels: ['service', 'http', fetch-balance-change-record]
---

## 概述

查询余额变更记录

## 使用

### Browser

```ts
import { fetchBalanceChangeRecord } from '@manyun/hrm.service.fetch-balance-change-record';

const { error, data } = await fetchBalanceChangeRecord('success');
const { error, data } = await fetchBalanceChangeRecord('error');
```

### Node

```ts
import { fetchBalanceChangeRecord } from '@manyun/hrm.service.fetch-balance-change-record/dist/index.node';

const { data } = await fetchBalanceChangeRecord('success');

try {
  const { data } = await fetchBalanceChangeRecord('error');
} catch (error) {
  // ...
}
```
