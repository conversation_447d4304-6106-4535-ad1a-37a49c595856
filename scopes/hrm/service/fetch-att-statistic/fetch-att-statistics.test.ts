/**
 * <AUTHOR>
 * @since 2022-6-21
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchAttStatistics as webService } from './fetch-att-statistics.browser';
import { fetchAttStatistics as nodeService } from './fetch-att-statistics.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    type: 'DAILY',
    startDate: 1657382400000,
    endDate: 1657468799999,
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('attStandardTimes');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ type: 'DAILY', startDate: 0, endDate: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    type: 'DAILY',
    startDate: 1657382400000,
    endDate: 1657468799999,
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('attStandardTimes');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ type: 'DAILY', startDate: 0, endDate: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
