/**
 * <AUTHOR>
 * @since 2022-6-21
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type StatisticType = 'MONTHLY' | 'DAILY';

export type SvcQuery = {
  type: StatisticType;
  blockTags?: string[];
  idcTags?: string[];
  dutyGroupName?: string;
  dutyName?: string /**班次名称 */;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  staffId?: number;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  dutyIds?: number[] /**班次id*/;
  dutyGroupIds?: number[] /**班组id  */;
  bizType?: string;
  needLeave?: 'TRUE' | 'FALSE';
  staffType?: string;
  needAttendance?: boolean;
};

export type AttStatistics = BacknedAttStatistics;
export type BacknedAttStatistics = {
  attStandardTimes: number /**应出勤天数 */;
  attActualTimes: number /**实际出勤天数 */;
  lateOnTimes: number /**迟到次数 */;
  earlyOffTimes: number /**早退次数 */;
  missOnTimes: number /**缺上班卡次数 */;
  missOffTimes: number /**缺下班卡次数 */;
  absentTimes: number /**矿工次数 */;
  outWorkTimes: number /**外勤次数 */;
};

export type SvcRespData = AttStatistics | null;

export type RequestRespData = BacknedAttStatistics | null;

export type ApiQ = {
  staffId?: number /**员工id */;
  blockTagList?: string[] /**楼栋列表 */;
  idcTagList?: string[] /**机房列表 */;
  dutyGroupName?: string /**班组名称 */;
  dutyName?: string /**班次名称 */;
  startDate?: number /**开始时间 */;
  endDate?: number /**结束时间 */;
  isAbsent?: boolean /**是否矿工 */;
  isMisOff?: boolean /**是否缺下班卡 */;
  isMisOn?: boolean /**是否缺上班卡 */;
  isLateOn?: boolean /**是否迟到 */;
  isEarlyOff?: boolean /**是否早退 */;
  dutyIdList?: number[] /**班次id*/;
  dutyGroupIdList?: number[] /**班组id  */;
  bizType?: string;
  needLeave?: boolean;
  staffType?: string;
  scene: StatisticType;
  needAttendance?: boolean;
};

export type ApiR = Response<BacknedAttStatistics | null>;
