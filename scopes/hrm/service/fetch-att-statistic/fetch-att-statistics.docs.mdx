---
description: 'A fetchAttStatistics HTTP API service.'
labels: ['service', 'http', fetch-att-statistics]
---

## 概述

查询考勤统计数据

## 使用

### Browser

```ts
import { fetchAttStatistics } from '@manyun/hrm.fetch-att-statistics';

const { error, data } = await fetchAttStatistics('success');
const { error, data } = await fetchAttStatistics('error');
```

### Node

```ts
import { fetchAttStatistics } from '@manyun/hrm.fetch-att-statistics/dist/index.node';

const { data } = await fetchAttStatistics('success');

try {
  const { data } = await fetchAttStatistics('error');
} catch (error) {
  // ...
}
```
