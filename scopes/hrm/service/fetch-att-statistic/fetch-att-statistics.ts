/**
 * <AUTHOR>
 * @since 2022-6-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-att-statistics.type';

const endpoint = '/pm/attStatistics/header/detail';

/**
 * 查询考勤统计数据
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18376)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      scene: variant.type,
      staffId: variant?.staffId,
      blockTagList: variant?.blockTags,
      idcTagList: variant?.idcTags,
      dutyGroupName: variant?.dutyGroupName,
      dutyName: variant?.dutyName,
      startDate: variant?.startDate,
      endDate: variant?.endDate,
      isAbsent: variant?.isAbsent,
      isMisOff: variant?.isMisOn,
      isMisOn: variant?.isMisOn,
      isLateOn: variant?.isLateOn,
      isEarlyOff: variant?.isEarlyOff,
      dutyGroupIdList: variant?.dutyGroupIds,
      dutyIdList: variant?.dutyIds,
      bizType: variant?.bizType,
      needLeave: variant?.needLeave !== undefined ? variant.needLeave === 'TRUE' : undefined,
      staffType: variant?.staffType,
      needAttendance: variant.needAttendance,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
