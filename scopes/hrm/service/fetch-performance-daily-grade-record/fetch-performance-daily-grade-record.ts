/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiDailyPerformanceGrade,
  ApiQ,
  SvcQuery,
} from './fetch-performance-daily-grade-record.type';

const endpoint = '/pm/pf/kpiRecordDetail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23547)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery
  ): Promise<EnhancedAxiosResponse<ApiDailyPerformanceGrade | null>> => {
    const params: ApiQ = { id: svcQuery.id };

    return await request.tryPost<ApiDailyPerformanceGrade | null, ApiQ>(endpoint, params);
  };
}
