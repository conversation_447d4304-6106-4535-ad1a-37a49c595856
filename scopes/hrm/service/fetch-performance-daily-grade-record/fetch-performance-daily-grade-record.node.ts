/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-performance-daily-grade-record';
import type {
  ApiDailyPerformanceGrade,
  SvcQuery,
} from './fetch-performance-daily-grade-record.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPerformanceDailyGradeRecord(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<ApiDailyPerformanceGrade | null>> {
  return executor(svcQuery);
}
