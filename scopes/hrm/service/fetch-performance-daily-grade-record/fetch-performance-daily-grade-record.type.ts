/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendDailyPerformanceGrade } from '@manyun/hrm.model.daily-performance-grade';

export type SvcQuery = {
  id: number;
};

export type ApiQ = {
  id: number;
};

export type ApiDailyPerformanceGrade = Omit<
  BackendDailyPerformanceGrade,
  'resource' | 'staff' | 'region' | 'superiors' | 'createUser' | 'modifiedUser'
> & {
  staffId: number;
};

export type ApiR = Response<ApiDailyPerformanceGrade[] | null>;
