/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-performance-annual-objectives';
import type { SvcQuery } from './export-performance-annual-objectives.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportPerformanceAnnualObjectives(
  svcQuery?: SvcQuery
): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(svcQuery);
}
