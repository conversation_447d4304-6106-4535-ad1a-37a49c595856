/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './export-performance-annual-objectives.type';

const endpoint = '/pm/pf/exportKpi';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23331)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<Blob>> => {
    const params: ApiQ = {
      name: svcQuery?.name,
      resources: svcQuery?.resourceCodes,
      evalPosition: svcQuery?.performancePosition,
      kpiWay: svcQuery?.way,
      type: svcQuery?.type,
      subType: svcQuery?.subType,
      creatorId: svcQuery?.createUserId,
      createStartTime: svcQuery?.createTimeRange ? svcQuery.createTimeRange[0] : undefined,
      createEndTime: svcQuery?.createTimeRange ? svcQuery.createTimeRange[1] : undefined,
      modifiedStartTime: svcQuery?.modifiedTimeRange ? svcQuery.modifiedTimeRange[0] : undefined,
      modifiedEndTime: svcQuery?.modifiedTimeRange ? svcQuery.modifiedTimeRange[1] : undefined,
    };

    return await request.tryPost<Blob, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
