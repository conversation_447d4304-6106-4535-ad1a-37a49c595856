/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiAnnualPerformanceObjective,
  ApiQ,
  SvcQuery,
} from './fetch-annual-performance-objective.type';

const endpoint = '/pm/pf/kpiDetail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23556)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery
  ): Promise<EnhancedAxiosResponse<ApiAnnualPerformanceObjective | null>> => {
    const params: ApiQ = { id: svcQuery.id };

    return await request.tryPost<ApiAnnualPerformanceObjective | null, ApiQ>(endpoint, params);
  };
}
