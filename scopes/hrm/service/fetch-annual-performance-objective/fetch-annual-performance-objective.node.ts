/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-annual-performance-objective';
import type {
  ApiAnnualPerformanceObjective,
  SvcQuery,
} from './fetch-annual-performance-objective.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAnnualPerformanceObjective(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<ApiAnnualPerformanceObjective | null>> {
  return executor(svcQuery);
}
