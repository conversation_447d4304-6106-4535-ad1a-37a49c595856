/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendAnnualPerformanceObjective } from '@manyun/hrm.model.annual-performance-objective';

export type SvcQuery = {
  id: number;
};

export type ApiAnnualPerformanceObjective = Omit<
  BackendAnnualPerformanceObjective,
  'resources' | 'createUser' | 'modifierUser'
> & {
  creatorId: number;
  modifierId: number;
};

export type ApiQ = {
  id: number;
};

export type ApiR = Response<ApiAnnualPerformanceObjective[] | null>;
