---
description: 'A fetchPagedStaffList HTTP API service.'
labels: ['service', 'http']
---

分页查询用户档案

## Usage

### Browser

```ts
import { fetchPagedStaffList } from '@manyun/hrm.service.fetch-paged-staff-list';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchPagedStaffListService } from '@manyun/hrm.service.fetch-paged-staff-list/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchPagedStaffListService.from(nodeRequest);
```
