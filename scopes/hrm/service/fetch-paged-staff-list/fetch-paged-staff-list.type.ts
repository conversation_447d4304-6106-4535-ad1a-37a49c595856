/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  userId?: number | null;
  directorId?: number | null;
  /**
   * 楼栋guid
   */
  blockGuids?: string[] | null;
  /**
   * 岗位列表
   */
  positions?: string[] | null;
  /**
   * 是否在职
   */
  enable?: boolean | null;
  /**
   * 是否根据工号倒序排列
   */
  orderByJobNoDesc?: boolean | null;
  pageNum: number;
  pageSize: number;
};

export type PagedUserJSON = {
  /**
   * 用户id
   */
  id: number;
  /**
   * 姓名
   */
  userName: string;
  /**
   * 性别
   */
  sexType: string;
  /**
   * 手机号
   */
  mobile: string;
  /**
   * 邮箱
   */
  email: string;
  /**
   * 是否在职
   */
  enable: boolean;
  /**
   * 入职日期
   */
  hiredDate?: number | null;
  /**
   * 参加工作时间
   */
  joinWorkingTime?: number | null;
  /**
   * 工号
   */
  jobNumber?: string | null;
  /**
   * 所属机房
   */
  idc?: string | null;
  /**
   * 所属楼栋列表
   */
  ptBlockGuids?: string[] | null;
  /**
   * 岗位code，元数据
   */
  position: string;
  /**
   * 直线经理1
   */
  director: number;
  /**
   * 工龄 单位：月
   */
  workingAge?: number | null;
  /**
   * 司龄 单位：月
   */
  serviceAge?: number | null;
  /**
   * 所有证书数量
   */
  allCertCount?: number | null;
  /**
   * 有效证书数量
   */
  validCertCount?: number | null;

  /** 转正日期 */
  confirmationDate?: number;
};

export type ApiResponse = ListBackendResponse<PagedUserJSON[]>;
