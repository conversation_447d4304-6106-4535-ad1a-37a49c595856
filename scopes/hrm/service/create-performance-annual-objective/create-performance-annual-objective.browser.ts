/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-performance-annual-objective';
import type { SvcQuery } from './create-performance-annual-objective.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createPerformanceAnnualObjective(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<string | null>> {
  return executor(svcQuery);
}
