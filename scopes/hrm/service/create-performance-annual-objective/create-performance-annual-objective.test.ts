/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createPerformanceAnnualObjective as webService } from './create-performance-annual-objective.browser';
import { createPerformanceAnnualObjective as nodeService } from './create-performance-annual-objective.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    name: '',
    way: 'STANDARD',
    measurements: [],
    type: 'DAILY',
    subType: 'BIZ',
    gradeCriteria: '',
    resources: [],
    evalPositions: [],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    name: '',
    way: 'STANDARD',
    measurements: [],
    type: 'DAILY',
    subType: 'BIZ',
    gradeCriteria: '',
    resources: [],
    evalPositions: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(true);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    name: '',
    way: 'STANDARD',
    measurements: [],
    type: 'DAILY',
    subType: 'BIZ',
    gradeCriteria: '',
    resources: [],
    evalPositions: [],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    name: '',
    way: 'STANDARD',
    measurements: [],
    type: 'DAILY',
    subType: 'BIZ',
    gradeCriteria: '',
    resources: [],
    evalPositions: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(typeof data).toBe('number');
});
