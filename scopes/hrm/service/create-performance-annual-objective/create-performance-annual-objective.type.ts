/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  AnnualPerformanceObjectiveJSON,
  BackendAnnualPerformanceObjective,
} from '@manyun/hrm.model.annual-performance-objective';

export type SvcQuery = Pick<AnnualPerformanceObjectiveJSON, 'name' | 'way' | 'type' | 'subType'> & {
  resources: string[];
  evalPositions: string[];
  reason?: string;
  measurements: {
    context: string;
    gradeCriteria: string;
    defaultGrade: number[];
    ceiling?: number;
  }[];
};

export type ApiQ = Pick<
  BackendAnnualPerformanceObjective,
  'name' | 'kpiWay' | 'type' | 'subType'
> & {
  resources: string[];
  evalPositions: string[];
  reason?: string;
  metrics: {
    context: string;
    gradeCriteria: string;
    defaultGrade: number[];
    ceiling?: number;
  }[];
};

export type ApiR = Response<string | null>;
