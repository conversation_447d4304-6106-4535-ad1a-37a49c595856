/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './save-performance-evaluation';
import type { SvcQuery } from './save-performance-evaluation.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function savePerformanceEvaluation(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
