/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './save-performance-evaluation.type';

const endpoint = '/pm/pf/evaluationSave';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23772)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = {
      infoId: svcQuery.id,
      pfPhase: svcQuery.currentStep,
      result: svcQuery.result ?? undefined,
      content: {
        summary: svcQuery.content?.summary?.trim(),
        improve: svcQuery.content?.improve?.trim(),
        infoSummary: svcQuery.content?.infoSummary?.trim(),
        infoImprove: svcQuery.content?.infoImprove?.trim(),
      },
      sectionGrade: svcQuery.goalGrades
        ?.filter(goal => goal.grade !== null)
        .map(goal => ({
          sectionId: goal.id,
          grade: goal.grade,
        })),
      interviewed: svcQuery.interviewed,
      needAttention: svcQuery.needAttention,
      attentionContent: svcQuery.attentionContent?.trim(),
    };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
