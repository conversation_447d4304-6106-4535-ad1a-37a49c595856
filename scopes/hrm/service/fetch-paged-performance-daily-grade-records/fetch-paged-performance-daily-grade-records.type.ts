/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
} from '@manyun/hrm.model.annual-performance-objective';
import type { BackendDailyPerformanceGrade } from '@manyun/hrm.model.daily-performance-grade';

export type SvcQuery = {
  page: number;
  pageSize: number;
  regionCodes?: string[] | null;
  idcTags?: string[] | null;
  blockGuids?: string[] | null;
  name?: string | null;
  position?: string | null;
  type?: BackendAnnualPerformanceObjectiveType | null;
  types?: BackendAnnualPerformanceObjectiveType[];
  subType?: BackendAnnualPerformanceObjectiveSubType | null;
  superiorId?: number | null;
  createUserId?: number | null;
  modifiedUserId?: number | null;
  staffId?: number | null;
  createTimeRange?: [number, number] | null;
  occurTimeRange?: [number, number] | null;
  modifiedTimeRange?: [number, number] | null;
  orderBy?: {
    userId?: Order | null;
    region?: Order | null;
    idc?: Order | null;
    name?: Order | null;
    type?: Order | null;
    subType?: Order | null;
    occurTime?: Order | null;
    position?: Order | null;
    grade?: Order | null;
    createdAt?: Order | null;
    modifiedAt?: Order | null;
  } | null;
  eqName?: string;
  instStatusList?: string[];
};
export type Order = 'ascend' | 'descend';

export type ApiDailyPerformanceGrade = Omit<
  BackendDailyPerformanceGrade,
  'resource' | 'staff' | 'region' | 'superiors' | 'createUser' | 'modifiedUser' | 'evalPosition'
> & {
  regionCode: string | null;
  /**
   * 员工所属机房
   */
  resource: string | null;
  evalPosition: string | null;
  staffId: number;
  staffName: string;
  superiorIds: number[];
  creatorId: number;
  modifierId: number;
  /**
   * 用于判断是否展示操作按钮
   */
  canModified: boolean;
  jobNo: string;
};

export type PagedDailyPerformanceGrade = {
  data: ApiDailyPerformanceGrade[];
  total: number;
};

export type ApiQ = {
  pageSize: number;
  pageNum: number;
  evalPositions?: string[];
  regionCodes?: string[];
  resources?: string[];
  blockGuids?: string[] | null;
  name?: string;
  evalPosition?: string;
  type?: BackendAnnualPerformanceObjectiveType;
  types?: BackendAnnualPerformanceObjectiveType[];
  subType?: BackendAnnualPerformanceObjectiveSubType;
  superiorId?: number;
  staffId?: number;
  creatorId?: number;
  createStartTime?: number;
  createEndTime?: number;
  occurStartTime?: number;
  occurEndTime?: number;
  regionCodeDesc?: boolean;
  resourceDesc?: boolean;
  nameDesc?: boolean;
  typeDesc?: boolean;
  subTypeDesc?: boolean;
  staffIdDesc?: boolean;
  occurTimeDesc?: boolean;
  gradeDesc?: boolean;
  gmtCreateDesc?: boolean;
  gmtModifiedDesc?: boolean;
  evalPositionDesc?: boolean;
  modifyStartTime?: number;
  modifyEndTime?: number;
  modifierId?: number;
  eqName?: string;
  instStatusList?: string[];
};

export type ApiR = ListResponse<ApiDailyPerformanceGrade[] | null>;
