/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  PagedDailyPerformanceGrade,
  SvcQuery,
} from './fetch-paged-performance-daily-grade-records.type';

const endpoint = '/pm/pf/kpiRecordPage';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/216/interface/api/23376)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<PagedDailyPerformanceGrade>> => {
    const params: ApiQ = {
      pageSize: svcQuery.pageSize,
      pageNum: svcQuery.page,
      name: svcQuery.name ?? undefined,
      eqName: svcQuery.eqName ?? undefined,
      regionCodes: svcQuery.regionCodes ?? undefined,
      resources: svcQuery.idcTags ?? undefined,
      evalPositions: svcQuery.position ? [svcQuery.position] : undefined,
      type: svcQuery.type ?? undefined,
      types: svcQuery.types ?? undefined,
      subType: svcQuery.subType ?? undefined,
      staffId: svcQuery.staffId ?? undefined,
      creatorId: svcQuery.createUserId ?? undefined,
      modifierId: svcQuery.modifiedUserId ?? undefined,
      createStartTime: svcQuery.createTimeRange ? svcQuery.createTimeRange[0] : undefined,
      createEndTime: svcQuery.createTimeRange ? svcQuery.createTimeRange[1] : undefined,
      modifyStartTime: svcQuery.modifiedTimeRange ? svcQuery.modifiedTimeRange[0] : undefined,
      modifyEndTime: svcQuery.modifiedTimeRange ? svcQuery.modifiedTimeRange[1] : undefined,
      occurStartTime: svcQuery.occurTimeRange ? svcQuery.occurTimeRange[0] : undefined,
      occurEndTime: svcQuery.occurTimeRange ? svcQuery.occurTimeRange[1] : undefined,
      superiorId: svcQuery.superiorId ?? undefined,
      regionCodeDesc: svcQuery?.orderBy?.region ? svcQuery.orderBy.region === 'descend' : undefined,
      resourceDesc: svcQuery?.orderBy?.idc ? svcQuery.orderBy.idc === 'descend' : undefined,
      nameDesc: svcQuery?.orderBy?.name ? svcQuery.orderBy.name === 'descend' : undefined,
      typeDesc: svcQuery?.orderBy?.type ? svcQuery.orderBy.type === 'descend' : undefined,
      subTypeDesc: svcQuery?.orderBy?.subType ? svcQuery.orderBy.subType === 'descend' : undefined,
      occurTimeDesc: svcQuery?.orderBy?.occurTime
        ? svcQuery.orderBy.occurTime === 'descend'
        : undefined,
      gradeDesc: svcQuery?.orderBy?.grade ? svcQuery.orderBy.grade === 'descend' : undefined,
      gmtCreateDesc: svcQuery?.orderBy?.createdAt
        ? svcQuery.orderBy.createdAt === 'descend'
        : undefined,
      gmtModifiedDesc: svcQuery?.orderBy?.modifiedAt
        ? svcQuery.orderBy.modifiedAt === 'descend'
        : undefined,
      staffIdDesc: svcQuery?.orderBy?.userId ? svcQuery.orderBy.userId === 'descend' : undefined,
      evalPositionDesc: svcQuery?.orderBy?.position
        ? svcQuery.orderBy.position === 'descend'
        : undefined,
      blockGuids: svcQuery.blockGuids,
      instStatusList: svcQuery.instStatusList,
    };

    const { error, data, ...rest } = await request.tryPost<PagedDailyPerformanceGrade | null, ApiQ>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
