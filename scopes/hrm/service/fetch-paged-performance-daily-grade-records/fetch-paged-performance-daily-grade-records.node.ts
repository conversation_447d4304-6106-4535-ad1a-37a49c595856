/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-performance-daily-grade-records';
import type {
  PagedDailyPerformanceGrade,
  SvcQuery,
} from './fetch-paged-performance-daily-grade-records.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPagedPerformanceDailyGradeRecords(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<PagedDailyPerformanceGrade>> {
  return executor(svcQuery);
}
