---
description: 'A fetchUserCurrentScheduleDutyGroup HTTP API service.'
labels: ['service', 'http', user-current-schedule-duty-group]
---

## 概述

当前员工班组

## 使用

### Browser

```ts
import { fetchUserCurrentScheduleDutyGroup } from '@hrm/service.fetch.user-current-schedule-duty-group';

const { error, data } = await fetchUserCurrentScheduleDutyGroup({ scheduleDate: 11 });
const { error, data } = await fetchUserCurrentScheduleDutyGroup({ scheduleDate: 22 });
```

### Node

```ts
import { fetchUserCurrentScheduleDutyGroup } from '@hrm/service.fetch.user-current-schedule-duty-group/dist/index.node';

const { data } = await fetchUserCurrentScheduleDutyGroup({ scheduleDate: 11 });

try {
  const { data } = await fetchUserCurrentScheduleDutyGroup({ scheduleDate: 22 });
} catch (error) {
  // ...
}
```
