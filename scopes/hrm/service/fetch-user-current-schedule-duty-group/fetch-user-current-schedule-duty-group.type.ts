/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type UserDutyGroupJSON = {
  id: number;
  groupName: string;
};

export type SvcRespData = UserDutyGroupJSON[] | null;

export type RequestRespData = { data: UserDutyGroupJSON[]; total: number } | null;

export type ApiQ = {
  scheduleDate: number;
  dutyGroupName?: string;
};

export type ApiR = ListResponse<UserDutyGroupJSON[] | null>;
