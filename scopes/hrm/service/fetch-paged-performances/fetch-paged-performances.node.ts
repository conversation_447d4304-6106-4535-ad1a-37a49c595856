/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-performances';
import type { PaginatedPerformances, SvcQuery } from './fetch-paged-performances.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPagedPerformances(
  svcQuery?: SvcQuery
): Promise<EnhancedAxiosResponse<PaginatedPerformances>> {
  return executor(svcQuery);
}
