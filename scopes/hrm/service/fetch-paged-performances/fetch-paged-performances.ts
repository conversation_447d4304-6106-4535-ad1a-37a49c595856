/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, ApiR, PaginatedPerformances, SvcQuery } from './fetch-paged-performances.type';

const endpoint = '/pm/pf/pfList';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/22746)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<PaginatedPerformances>> => {
    const params: ApiQ = {
      pfType: svcQuery?.type ?? 'TP',
      infoSubType: svcQuery?.kpiSubType ?? 'TARGET',
      staffIds: svcQuer<PERSON>?.userIds,
      idcTags: svcQuery?.idcs,
      position: svcQuery?.job,
      regionCodes: svcQuery?.regionCodes,
      targetStatus: svcQuery?.goalsStatus,
      evalStatus: svcQuery?.evaluationStatus,
      superior: svcQuery?.lineManagerIds,
      secSuperior: svcQuery?.secondLineManagerIds,
      hiredBeginDate: svcQuery?.hiredAtRange?.[0],
      hiredEndDate: svcQuery?.hiredAtRange?.[1],
      targetExpireBeginDate: svcQuery?.goalsDeadlineRange?.[0],
      targetExpireEndDate: svcQuery?.goalsDeadlineRange?.[1],
      evalExpireBeginDate: svcQuery?.evalDeadlineRange?.[0],
      evalExpireEndDate: svcQuery?.evalDeadlineRange?.[1],
      result: svcQuery?.result,
      currentEvalStaffId: svcQuery?.currentHandlerId,
      orderByTargetExpireDateDesc: svcQuery?.orderBy?.goalsDeadline
        ? svcQuery.orderBy.goalsDeadline === 'desc'
        : undefined,
      orderByEvalExpireDateDesc: svcQuery?.orderBy?.evalDeadline
        ? svcQuery.orderBy.evalDeadline === 'desc'
        : undefined,
      orderByStaffIdDesc: svcQuery?.orderBy?.userId
        ? svcQuery.orderBy.userId === 'desc'
        : undefined,
      orderByHiredDateDesc: svcQuery?.orderBy?.hiredAt
        ? svcQuery.orderBy.hiredAt === 'desc'
        : undefined,
      orderByRegionCodeDesc: svcQuery?.orderBy?.region
        ? svcQuery.orderBy.region === 'desc'
        : undefined,
      orderByIdcDesc: svcQuery?.orderBy?.idc ? svcQuery.orderBy.idc === 'desc' : undefined,
      orderBySuperiorIdsDesc: svcQuery?.orderBy?.lineManager
        ? svcQuery.orderBy.lineManager === 'desc'
        : undefined,
      orderByTargetStatusDesc: svcQuery?.orderBy?.goalsStatus
        ? svcQuery.orderBy.goalsStatus === 'desc'
        : undefined,
      orderByEvalStatusDesc: svcQuery?.orderBy?.evaluationStatus
        ? svcQuery.orderBy.evaluationStatus === 'desc'
        : undefined,
      orderByGradeDesc: svcQuery?.orderBy?.grade ? svcQuery.orderBy.grade === 'desc' : undefined,
      orderByFinalGradeDesc: svcQuery?.orderBy?.gradeAvg
        ? svcQuery.orderBy.gradeAvg === 'desc'
        : undefined,
      orderByResultDesc: svcQuery?.orderBy?.result ? svcQuery.orderBy.result === 'desc' : undefined,
      orderByPositionDesc: svcQuery?.orderBy?.job ? svcQuery.orderBy.job === 'desc' : undefined,
      orderByEvalPositionDesc: svcQuery?.orderBy?.evaluationJob
        ? svcQuery.orderBy.evaluationJob === 'desc'
        : undefined,
      orderByPeriodDesc: svcQuery?.orderBy?.period ? svcQuery.orderBy.period === 'desc' : undefined,
      orderByKpiGradeDesc: svcQuery?.orderBy?.objectivesGrade
        ? svcQuery.orderBy.objectivesGrade === 'desc'
        : undefined,
      pageNum: svcQuery?.page ?? 1,
      pageSize: svcQuery?.pageSize ?? 10,
      year: svcQuery?.year,
      period: svcQuery?.period,
      evalPosition: svcQuery?.evaluationJob,
      periods: svcQuery?.periods,
    };

    const { error, data, ...rest } = await request.tryPost<
      Pick<ApiR, 'data' | 'total'> | null,
      ApiQ
    >(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data,
        total: data.total,
      },
      ...rest,
    };
  };
}
