/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2023-4-13
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendPerformance,
  BackendPerformanceEvaluationStatus,
  BackendPerformanceObjectiveStatus,
  BackendPerformanceResult,
  BackendPerformanceSubType,
  BackendPerformanceType,
} from '@manyun/hrm.model.performance';

export type Order = 'asc' | 'desc';

export type BackendPfType = /**试用期**/ 'TP' | /**正式**/ 'REGULAR';

export type SvcQuery = {
  type: BackendPfType;
  kpiSubType: BackendPerformanceSubType;
  userIds?: number[];
  regionCodes?: string[];
  idcs?: string[];
  blocks?: string[];
  job?: string;
  goalsStatus?: BackendPerformanceObjectiveStatus[];
  evaluationStatus?: BackendPerformanceEvaluationStatus[];
  lineManagerIds?: number[];
  secondLineManagerIds?: number[];
  hiredAtRange?: [number | null, number | null];
  goalsDeadlineRange?: [number | null, number | null];
  evalDeadlineRange?: [number | null, number | null];
  result?: BackendPerformanceResult;
  currentHandlerId?: number;
  orderBy?: {
    goalsDeadline?: Order;
    evalDeadline?: Order;
    userId?: Order;
    hiredAt?: Order;
    region?: Order;
    idc?: Order;
    lineManager?: Order;
    goalsStatus?: Order;
    evaluationStatus?: Order;
    grade?: Order;
    gradeAvg?: Order;
    result?: Order;
    job?: Order;
    evaluationJob?: Order;
    period?: Order;
    objectivesGrade?: Order;
  };
  page?: number;
  pageSize?: number;
  year?: string;
  period?: string;
  evaluationJob?: string;
  periods?: string[];
};

export type ApiPerformance<T extends BackendPerformanceType = 'TP'> = Pick<
  BackendPerformance<T>,
  | 'id'
  | 'idc'
  | 'position'
  | 'pfType'
  | 'targetStatus'
  | 'evalStatus'
  | 'hiredDate'
  | 'targetExpireDate'
  | 'evalExpireDate'
  | 'result'
  | 'grade'
  | 'period'
  | 'year'
  | 'kpiGrade'
> & {
  staffId: number;
  regionCode: string | null;
  superiors: number[] | null;
  secSuperiors: number[] | null;
  hrs: number[] | null;
  currentEvalStaffIds: number[] | null;
  /**
   * 当用户有多个直线经理时，会按每个直线经理生成一条绩效记录
   * 最终得分为多个绩效记录的平均值
   */
  finalGrade: number | null;
  evalPosition: string;
  planId: number;
  jobNo: string;
};

export type PaginatedPerformances = {
  data: ApiPerformance[];
  total: number;
};

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  infoSubType: BackendPerformanceSubType;
  staffIds?: number[] | null;
  idcTags?: string[] | null;
  blockGuids?: string[] | null;
  position?: string | null;
  regionCodes?: string[] | null;
  targetStatus?: BackendPerformanceObjectiveStatus[] | null;
  evalStatus?: BackendPerformanceEvaluationStatus[] | null;
  superior?: number[] | null;
  secSuperior?: number[] | null;
  hiredBeginDate?: number | null;
  hiredEndDate?: number | null;
  targetExpireBeginDate?: number | null;
  targetExpireEndDate?: number | null;
  evalExpireBeginDate?: number | null;
  evalExpireEndDate?: number | null;
  result?: BackendPerformanceResult | null;
  currentEvalStaffId?: number | null;
  orderByTargetExpireDateDesc?: boolean | null;
  orderByEvalExpireDateDesc?: boolean | null;
  orderByStaffIdDesc?: boolean | null;
  orderByHiredDateDesc?: boolean | null;
  orderByRegionCodeDesc?: boolean | null;
  orderByIdcDesc?: boolean | null;
  orderBySuperiorIdsDesc?: boolean | null;
  orderByTargetStatusDesc?: boolean | null;
  orderByEvalStatusDesc?: boolean | null;
  orderByGradeDesc?: boolean | null;
  orderByFinalGradeDesc?: boolean | null;
  orderByResultDesc?: boolean | null;
  orderByPositionDesc?: boolean | null;
  orderByEvalPositionDesc?: boolean | null;
  orderByPeriodDesc?: boolean | null;
  orderByKpiGradeDesc?: boolean | null;
  pfType: BackendPfType;
  year?: string;
  period?: string;
  evalPosition?: string;
  periods?: string[];
};

export type ApiR = ListResponse<ApiPerformance[] | null>;
