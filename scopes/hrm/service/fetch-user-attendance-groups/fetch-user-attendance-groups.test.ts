/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserAttendanceGroups as webService } from './fetch-user-attendance-groups.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ staffId: 1 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('rule');
});
