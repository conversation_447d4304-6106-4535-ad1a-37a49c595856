/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  staffId: number;
};

export type BackendAttendanceGroupInfos = {
  /** 员工排班id */
  id?: number;
  /** 考勤组名 */
  attName?: string;
  /** 机房tag*/
  idcTag?: string;
  /** 楼栋tag  */
  blockGuid?: string;
  /** 打卡渠道 */
  checkChannel?: CheckChannels[];
  /** 楼栋坐标 */
  blockCoordinate: {
    /** 楼栋 */
    blockGuid: string;
    /** 打卡有效范围 */
    area: number; //
    /** 经纬度 */
    coordinate: string;
    /** 经度 */
    longitude: string;
    /** 纬度 */
    latitude: string;
  };
  /**考勤规则 */
  rule: {
    /**可提前申请加班天数 */
    overtimeBeforeDays: number | null;
    /** 可提前申请请假天数*/
    leaveBeforeDays: number | null;
    /**可提前申请外勤天数 */
    outWorkBeforeDays: number | null;
    /**是否限制补卡时间 */
    enableSupplyTime: boolean;
    /**是否限制补卡次数			*/
    enableSupplyCount: boolean;
    /**可提前申请补卡天数 */
    supplyValidDays: number | null;
    /**是否允许补卡		*/
    enableSupply: boolean;
    /**每月可补卡天数	*/
    supplyCountByMonth: number | null;
    /**单次外勤最多申请时长*/
    outWorkTotalHours: number | null;
    /**不可后补请假开始时间 */
    leaveNotValidStartDay: number | null;
    /**不可后补 （无加班申请）加班确认开始时间 */
    overtimeNotValidStartDay: number | null;
    /**（有加班申请）提前加班确认天数 */
    overtimeConfirmValidDays: number | null;
    /** 不可后补（有加班申请）加班确认开始时间*/
    overtimeConfirmNotValidStartDay: number | null;
    /**不可后补 外勤不允许的开始日期 */
    outWorkNotValidStartDay: number | null;
    /** 不可后补 补卡不允许的开始日期*/
    supplyNotValidStartDay: number | null;
    combineLeaves: string[] | null;
  };
};

export type SvcRespData = BackendAttendanceGroupInfos | null;

export type RequestRespData = BackendAttendanceGroupInfos | null;

export type ApiQ = {
  staffId: number;
};

export type ApiR = Response<RequestRespData>;

/** 打卡方式枚举 */
enum CheckChannels {
  /** 钉钉 */
  'DINGDING' = 'DINGDING',
  /** 本地-系统登录 */
  'LOCAL' = 'LOCAL',
  /** 考勤机打卡 */
  'MACHINE' = 'MACHINE',
  /** GPS打卡 */
  'MOBILE' = 'MOBILE',
}
