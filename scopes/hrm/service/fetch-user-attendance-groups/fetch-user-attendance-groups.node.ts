/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-6-23
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-attendance-groups';
import type { SvcQuery, SvcRespData } from './fetch-user-attendance-groups.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchUserAttendanceGroups(
  svcQ: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
