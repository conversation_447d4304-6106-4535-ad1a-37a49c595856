---
description: 'A fetchUserAttendanceGroups HTTP API service.'
labels: ['service', 'http', fetch-user-attendance-groups]
---

## 概述

获取用户考勤组信息

## 使用

### Browser

```ts
import { fetchUserAttendanceGroups } from '@hrm/service.fetch-user-attendance-groups';

const { error, data } = await fetchUserAttendanceGroups({ staffId: 1 });
```

### Node

```ts
import { fetchUserAttendanceGroups } from '@hrm/service.fetch-user-attendance-groups/dist/index.node';

const { data } = await fetchUserAttendanceGroups({ staffId: 1 });

try {
  const { data } = await fetchUserAttendanceGroups({ staffId: 1 });
} catch (error) {
  // ...
}
```
