/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-29
 *
 * @packageDocumentation
 */
import { StatisticOTRequest } from '@manyun/hrm.service.fetch-paged-ot-requests-statistics';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './export-ot-requests.type';

const endpoint = '/pm/overtimeWork/overtimeParamExport';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/9512)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      bizId: variant?.bizId,
      applyStaffId: variant?.apply,
      holiday: StatisticOTRequest.toApiDayType(variant?.dayType),
      startTime: variant?.timeRange ? variant.timeRange[0] : undefined,
      endTime: variant?.timeRange ? variant.timeRange[1] : undefined,
      activeStartTime: variant?.activeTimeRange ? variant.activeTimeRange[0] : undefined,
      activeEndTime: variant?.activeTimeRange ? variant.activeTimeRange[1] : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      params,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
