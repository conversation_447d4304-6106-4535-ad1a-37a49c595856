/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-ot-requests';
import type { SvcQuery, SvcRespData } from './export-ot-requests.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function exportOtRequests(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
