/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-29
 *
 * @packageDocumentation
 */
import type { DAY_TYPE } from '@manyun/hrm.service.fetch-paged-ot-requests-statistics';

export type SvcQuery = {
  bizId?: string;
  apply?: number;
  timeRange?: [number, number];
  activeTimeRange?: [number, number];
  dayType?: DAY_TYPE;
};

export type SvcRespData = Blob;

export type RequestRespData = Blob;

export type ApiQ = {
  bizId?: string;
  startTime?: number;
  endTime?: number;
  activeStartTime?: number;
  activeEndTime?: number;
  applyStaffId?: number;
  holiday?: boolean;
};
