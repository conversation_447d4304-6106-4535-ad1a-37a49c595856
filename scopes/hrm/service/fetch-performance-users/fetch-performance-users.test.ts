/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPerformanceUsers as webService } from './fetch-performance-users.browser';
import { fetchPerformanceUsers as nodeService } from './fetch-performance-users.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ key: 'error' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toHaveProperty('length');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService();

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ key: 'error' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toHaveProperty('length');
});
