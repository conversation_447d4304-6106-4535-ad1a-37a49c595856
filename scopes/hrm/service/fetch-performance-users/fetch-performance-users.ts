/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import type { BackendUser, UserJSON } from '@manyun/auth-hub.model.user';
import { User } from '@manyun/auth-hub.model.user';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ } from './fetch-performance-users.type';

const endpoint = '/pm/pf/year/kpiRecordUsers';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/24474)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<UserJSON[]>> => {
    const { error, data, ...rest } = await request.tryPost<{ data: BackendUser[] }, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return {
      error,
      data: data.data.map(backendUser => User.fromApiObject(backendUser).toJSON()),
      ...rest,
    };
  };
}
