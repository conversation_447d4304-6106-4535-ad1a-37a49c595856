/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import type { UserJSON } from '@manyun/auth-hub.model.user';
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-performance-users';
import type { ApiQ } from './fetch-performance-users.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPerformanceUsers(svcQuery?: ApiQ): Promise<EnhancedAxiosResponse<UserJSON[]>> {
  return executor(svcQuery);
}
