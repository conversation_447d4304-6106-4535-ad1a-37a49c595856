/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type DeptType = 'PERSON' | 'DEPT';
export type SvcQuery = {
  deptId: string;
  resourceCode?: string;
};

export type DeptModel = {
  id: string;
  type: DeptType;
  name: string;
  nameEn?: string;
  parentId: string;
  positionName?: string;
  positionEnName?: string;
  fullDeptName: string;
};

export type SvcRespData = {
  data: DeptModel[];
  total: number;
};

export type RequestRespData = {
  data: DeptModel[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<DeptModel[] | null>;
