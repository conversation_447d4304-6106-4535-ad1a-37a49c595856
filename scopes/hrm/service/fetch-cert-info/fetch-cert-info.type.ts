/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-20
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  id?: number;
};

export type ApiResponseData = {
  id?: number;
  certName?: string;
  /**
   * 附件id
   */
  fileId?: number;
  /**
   * 附件类型
   */
  fileType?: string;
};

export type ApiResponse = BackendResponse<ApiResponseData>;
