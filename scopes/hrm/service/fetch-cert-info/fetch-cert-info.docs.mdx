---
description: 'A fetchCertInfo HTTP API service.'
labels: ['service', 'http']
---

审批详情获取证件信息

## Usage

### Browser

```ts
import { fetchCertInfo } from '@manyun/hrm.service.fetch-cert-info';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchCertInfoService } from '@manyun/hrm.service.fetch-cert-info/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchCertInfoService.from(nodeRequest);
```
