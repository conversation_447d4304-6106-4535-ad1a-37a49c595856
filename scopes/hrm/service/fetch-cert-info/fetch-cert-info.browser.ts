/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-20
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-cert-info.js';
import type { ApiArgs, ApiResponse } from './fetch-cert-info.type.js';

/**
 * @param args
* @returns
 */
export function fetchCertInfo(args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
