/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-20
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-cert-info.type.js';

const endpoint = '/pm/cert/info';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30932)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'], ApiArgs>(endpoint, args);
  };
}
