/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './export-part-time-jobs.type.js';

const endpoint = '/pm/partTimeJobRecord/Export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30508)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> => {
    return request.tryPost<Blob, ApiArgs>(endpoint, args, {
      responseType: 'blob',
    });
  };
}
