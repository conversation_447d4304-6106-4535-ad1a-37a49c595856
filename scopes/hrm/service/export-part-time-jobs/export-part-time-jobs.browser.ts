/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './export-part-time-jobs.js';
import type { ApiArgs } from './export-part-time-jobs.type.js';

/**
 * @param args
 * @returns
 */
export function exportPartTimeJobs(args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
