/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './export-involved-perforamnce-staff-infos.type.js';

const endpoint = '/pm/deptUser/Export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30492)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> => {
    return request.tryPost<Blob, ApiArgs>(endpoint, args, {
      responseType: 'blob',
    });
  };
}
