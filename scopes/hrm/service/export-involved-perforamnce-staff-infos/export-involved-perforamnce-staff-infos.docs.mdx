---
description: 'A exportInvolvedPerforamnceStaffInfos HTTP API service.'
labels: ['service', 'http']
---

部门用户导出

## Usage

### Browser

```ts
import { exportInvolvedPerforamnceStaffInfos } from '@manyun/hrm.service.export-involved-perforamnce-staff-infos';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { ExportInvolvedPerforamnceStaffInfosService } from '@manyun/hrm.service.export-involved-perforamnce-staff-infos/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = ExportInvolvedPerforamnceStaffInfosService.from(nodeRequest);
```
