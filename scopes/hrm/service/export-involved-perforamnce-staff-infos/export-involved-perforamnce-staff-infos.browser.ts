/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './export-involved-perforamnce-staff-infos.js';
import type { ApiArgs } from './export-involved-perforamnce-staff-infos.type.js';

/**
 * @param args
 * @returns
 */
export function exportInvolvedPerforamnceStaffInfos(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Blob>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
