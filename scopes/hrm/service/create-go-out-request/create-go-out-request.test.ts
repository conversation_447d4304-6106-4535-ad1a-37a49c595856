/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createGoOutRequest as webService } from './create-go-out-request.browser';
import { createGoOutRequest as nodeService } from './create-go-out-request.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    applyId: 1,
    timeRange: [1656573722715, 1656573722715],
    totalTime: 1,
    reason: '请假事由',
  });

  expect(error).toBe(undefined);
  expect(typeof data).toBe('string');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    applyId: 0,
    timeRange: [1656573722715, 1656573722715],
    totalTime: 0,
    reason: '请假事由',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(typeof data).toBe('object');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    applyId: 1,
    timeRange: [1656573722715, 1656573722715],
    totalTime: 1,
    reason: '请假事由',
  });

  expect(error).toBe(undefined);
  expect(typeof data).toBe('string');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    applyId: 0,
    timeRange: [1656573722715, 1656573722715],
    totalTime: 0,
    reason: '请假事由',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(typeof data).toBe('object');
});
