/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-go-out-request';
import type { SvcQuery, SvcRespData } from './create-go-out-request.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function createGoOutRequest(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
