---
description: 'A createGoOutRequest HTTP API service.'
labels: ['service', 'http', create-go-out-request]
---

## 概述

创建外勤申请

## 使用

### Browser

```ts
import { createGoOutRequest } from '@manyun/hrm.service.create-go-out-request';

const { error, data } = await createGoOutRequest({
  applyId: 1,
  timeRange: [1656573722715, 1656573722715],
  totalTime: 1,
  reason: '请假事由',
});
const { error, data } = await createGoOutRequest({
  applyId: 1,
  timeRange: [1656573722715, 1656573722715],
  totalTime: 1,
  reason: '请假事由',
});
```
