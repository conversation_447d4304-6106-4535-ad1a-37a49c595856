/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './create-go-out-request.type';

const endpoint = '/pm/outwork/add';

/**
 * 新建外勤申请
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18400)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (createD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyStaffId: createD.applyId,
      startTime: createD.timeRange[0],
      endTime: createD.timeRange[1],
      totalTime: createD.totalTime,
      reason: createD.reason,
      files: createD.attachements?.map(obj => McUploadFile.toApiObject(obj)),
    };

    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
