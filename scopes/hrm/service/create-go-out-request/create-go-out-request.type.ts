/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  applyId: number;
  timeRange: number[] /**外出时间 */;
  totalTime: number;
  reason: string;
  attachements?: McUploadFile[];
};

export type SvcRespData = string | null;

export type ApiQ = {
  applyStaffId: number;
  startTime: number /**开始时间 */;
  endTime: number /**结束时间 */;
  totalTime: number /**总时长（分钟） */;
  reason: string /**请假事由 */;
  files?: BackendMcUploadFile[] /**附件 */;
};

export type ApiR = Response<string>;
