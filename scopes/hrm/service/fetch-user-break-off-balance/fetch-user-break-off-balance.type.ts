/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendChangeRecord } from '@manyun/hrm.service.fetch-balance-change-record';

export type SvcQuery = {
  staffId: number;
};

export type BreakOffBalance = BackendBreakOffBalance;

export type BackendBreakOffBalance = {
  staffId: number;
  balanceType: string /**余额类型 */;
  expireDate: string /**过期时间 */;
  availablePaidLeave: number /**可用调休 */;
  closeExpire: boolean /**是否过期 */;
  changeRecordDtoList: BackendChangeRecord[] /**变更记录 */;
  paidLeaveBalance: number /**总调休小时 */;
  freezePaidLeave: number /**审批中 */;
};

export type SvcRespData = {
  data: BackendBreakOffBalance[];
  total: number;
};

export type RequestRespData = {
  data: BreakOffBalance[];
  total: number;
} | null;

export type ApiQ = {
  staffId: number;
};

export type ApiR = ListResponse<SvcRespData>;
