---
description: 'A fetchUserBreakOffBalance HTTP API service.'
labels: ['service', 'http', fetch-user-break-off-balance]
---

## 概述

查询用户调休余额记录

## 使用

### Browser

```ts
import { fetchUserBreakOffBalance } from '@manyun/hrm.service.fetch-user-break-off-balance';

const { error, data } = await fetchUserBreakOffBalance('success');
const { error, data } = await fetchUserBreakOffBalance('error');
```

### Node

```ts
import { fetchUserBreakOffBalance } from '@manyun/hrm.service.fetch-user-break-off-balance/dist/index.node';

const { data } = await fetchUserBreakOffBalance('success');

try {
  const { data } = await fetchUserBreakOffBalance('error');
} catch (error) {
  // ...
}
```
