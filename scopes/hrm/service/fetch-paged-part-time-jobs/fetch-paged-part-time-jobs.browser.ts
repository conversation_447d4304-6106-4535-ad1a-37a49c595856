/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-paged-part-time-jobs.js';
import type { ApiArgs, ApiResponse } from './fetch-paged-part-time-jobs.type.js';

/**
 * @param args
 * @returns
 */
export function fetchPagedPartTimeJobs(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
