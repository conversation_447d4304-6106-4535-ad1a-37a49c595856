/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 员工id
   */
  staffId?: number | null;
  /**
   * 兼岗楼栋
   */
  partTimeBlocks?: string[];
  /**
   * 楼栋列表
   */
  blockGuids?: string[] | null;
  /**
   * 机房
   */
  idc?: string | null;
  /**
   * 是否按照更新时间倒序
   */
  orderByModifyDesc?: boolean | null;
  /**
   * 开始时间
   */
  startTime?: number | null;
  /**
   * 结束时间
   */
  endTime?: number | null;
  pageNum: number;
  pageSize: number;
};

export type PartTimeJobInfos = {
  id: number;
  /**
   * 创建时间
   */
  gmtCreate: number;
  /**
   * 修改时间
   */
  gmtModified: number;
  /**
   * 员工id
   */
  staffId: number;
  /**
   * 工号
   */
  jobNumber: string;
  /**
   * 用户名
   */
  userName?: string;
  /**
   * 开始时间
   */
  startTime: number;
  /**
   * 结束时间
   */
  endTime: number;
  /**
   * 修改人id
   */
  modifierId: number;
  /**
   * 机房
   */
  idc: string;
  /**
   * 楼栋
   */
  blockGuid: string;
  /**
   * 兼岗楼栋
   */
  partTimeBlocks: string[];
};

export type ApiResponse = ListBackendResponse<PartTimeJobInfos[]>;
