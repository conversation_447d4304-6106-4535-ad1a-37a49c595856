---
description: 'A fetchPagedPartTimeJobs HTTP API service.'
labels: ['service', 'http']
---

兼岗记录分页查询

## Usage

### Browser

```ts
import { fetchPagedPartTimeJobs } from '@manyun/hrm.service.fetch-paged-part-time-jobs';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchPagedPartTimeJobsService } from '@manyun/hrm.service.fetch-paged-part-time-jobs/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchPagedPartTimeJobsService.from(nodeRequest);
```
