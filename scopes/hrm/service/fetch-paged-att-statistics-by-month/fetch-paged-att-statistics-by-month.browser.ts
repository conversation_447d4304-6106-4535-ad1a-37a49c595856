/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-att-statistics-by-month';
import type { SvcQuery, SvcRespData } from './fetch-paged-att-statistics-by-month.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPagedAttStatisticsByMonth(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
