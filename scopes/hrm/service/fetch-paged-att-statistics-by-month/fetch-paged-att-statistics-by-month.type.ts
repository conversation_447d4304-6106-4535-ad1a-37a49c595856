/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SortType = 'descend' | 'ascend';
export type SortField =
  | 'totalAttendanceDay'
  | 'attendanceDay'
  | 'standardWorkMinutes'
  | 'workTime'
  | 'lateTimes'
  | 'lateOnMinutes'
  | 'earlyOffTimes'
  | 'earlyOffMinutes'
  | 'missOnCardTimes'
  | 'missOffCardTimes'
  | 'absentTimes';

export type BackendSortField = SortField;
export type BackendSortType = 'ASC' | 'DESC';

export const SORT_TYPE_MAPPER: Record<SortType, BackendSortType> = {
  descend: 'DESC',
  ascend: 'ASC',
};

export type SvcQuery = {
  page: number;
  pageSize: number;
  blockTags?: string[];
  idcTags?: string[];
  dutyGroupName?: string /**班组名称 */;
  dutyName?: string /**班次名称 */;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  staffId?: number;
  sortField?: SortField;
  sortType?: SortType;
  staffType?: string;
  needLeave?: 'TRUE' | 'FALSE';
};

export type BackendMonthlyAttStatistics = {
  absentTimes: number /**矿工次数 */;
  attendanceDay: number /**实际考勤天数 */;
  blockTag: string /**位置 */;
  lateTimes: number /**迟到次数 */;
  missOffCardTimes: number /**下班缺卡次数 */;
  missOnCardTimes: number /**上班缺卡次数 */;
  staffId: number /**员工ID */;
  staffName: number /**员工名称 */;
  workTime: number /**班内工作时长 */;
  totalAttendanceDay: number /**应考勤天数 */;
  earlyOffTimes: number /**早退次数 */;
  standardWorkMinutes: number /*应出勤时长*/;
  lateOnMinutes: number /**迟到时长 */;
  earlyOffMinutes: number /**早退时长 */;
};
export type MonthlyAttStatistics = BackendMonthlyAttStatistics;

export type SvcRespData = {
  data: MonthlyAttStatistics[];
  total: number;
};

export type RequestRespData = {
  data: BackendMonthlyAttStatistics[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  staffId?: number /**员工id */;
  blockTagList?: string[] /**楼栋列表 */;
  idcTagList?: string[] /**机房列表 */;
  dutyGroupName?: string /**班组名称 */;
  dutyName?: string /**班次名称 */;
  startDate?: number /**开始时间 */;
  endDate?: number /**结束时间 */;
  isAbsent?: boolean /**是否矿工 */;
  isMisOff?: boolean /**是否缺下班卡 */;
  isMisOn?: boolean /**是否缺上班卡 */;
  isLateOn?: boolean /**是否迟到 */;
  isEarlyOff?: boolean /**是否早退 */;
  sortField?: BackendSortField;
  sortType?: BackendSortType;
  staffType?: string;
  needLeave?: boolean /**是否请假 */;
};

export type ApiR = ListResponse<BackendMonthlyAttStatistics[] | null>;
