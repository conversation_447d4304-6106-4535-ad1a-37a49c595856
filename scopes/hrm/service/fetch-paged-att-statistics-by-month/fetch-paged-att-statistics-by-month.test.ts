/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPagedAttStatisticsByMonth as webService } from './fetch-paged-att-statistics-by-month.browser';
import { fetchPagedAttStatisticsByMonth as nodeService } from './fetch-paged-att-statistics-by-month.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    page: 1,
    pageSize: 10,
    startDate: 1638288000000,
    endDate: 1639324799999,
    staffId: 11,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    page: 1,
    pageSize: 10,
    startDate: 0,
    endDate: 0,
    staffId: 1,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    page: 1,
    pageSize: 10,
    startDate: 1638288000000,
    endDate: 1639324799999,
    staffId: 11,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    page: 1,
    pageSize: 10,
    startDate: 0,
    endDate: 0,
    staffId: 1,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
