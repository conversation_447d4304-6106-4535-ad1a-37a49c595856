/**
 * <AUTHOR>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  needResources?: boolean;
};

export type BackendShiftTeam = {
  id: number;
  groupName: string;
};

export type ShiftTeam = {
  id: number;
  name: string;
};

export type SvcRespData = {
  data: ShiftTeam[];
  total: number;
};

export type RequestRespData = {
  data: BackendShiftTeam[] | null;
  total: number;
} | null;

export type ApiQ = {
  needResources?: boolean;
};

export type ApiR = ListResponse<BackendShiftTeam[] | null>;
