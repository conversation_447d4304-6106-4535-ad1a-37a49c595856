---
description: 'A fetchShiftTeams HTTP API service.'
labels: ['service', 'http', fetch-shift-teams]
---

## 概述

查询班组列表

## 使用

### Browser

```ts
import { fetchShiftTeams } from '@manyun/hrm.service.fetch-shift-teams';

const { error, data } = await fetchShiftTeams('success');
const { error, data } = await fetchShiftTeams('error');
```

### Node

```ts
import { fetchShiftTeams } from '@manyun/hrm.service.fetch-shift-teams/dist/index.node';

const { data } = await fetchShiftTeams('success');

try {
  const { data } = await fetchShiftTeams('error');
} catch (error) {
  // ...
}
```
