/**
 * <AUTHOR>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-shift-teams';
import type { SvcQuery, SvcRespData } from './fetch-shift-teams.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchShiftTeams(variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
