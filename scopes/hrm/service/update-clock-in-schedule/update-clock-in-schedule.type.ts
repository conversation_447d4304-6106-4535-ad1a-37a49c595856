/**
 * <AUTHOR>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ClockCheckWay = 'EXTRA' | 'SUPPLY' | 'NORMAL';
type TCheckType = 'ON' | 'OFF' | 'OTHER';
type CheckChannel = 'DINGDING' | 'LOCAL' | 'MACHINE' | 'MOBILE';

export type SvcQ = {
  /** 枚举: DINGDING,LOCAL,MACHINE */
  checkChannel: CheckChannel;
  /** 打卡时间 */
  checkTime: number;
  /** 员工id */
  staffId: number;
  /** 排班id */
  scheduleId?: number;
  /** 打卡类型：ON("上班卡"),  OFF("下班卡"),  OTHER("其他") */
  checkType?: TCheckType;
  /** 精度 */
  longitude?: string;
  /** 纬度 */
  latitude?: string;
  /** 更新打卡时传, 打卡方式(EXTRA 加班,SUPPLY 补卡,NORMAL 普通")*/
  checkWay?: ClockCheckWay;
  /** 业务id */
  bizId?: string;
};

export type SvcRespData = string | null;

export type RequestRespData = string | null;

export type ApiQ = SvcQ;

export type ApiR = Response<string>;
