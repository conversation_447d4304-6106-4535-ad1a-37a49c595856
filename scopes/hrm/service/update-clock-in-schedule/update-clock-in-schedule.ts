/**
 * <AUTHOR>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQ, SvcRespData } from './update-clock-in-schedule.type';

const endpoint = '/pm/check/addCheckRecord';

/**
 * 更新打卡
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/case/116)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = variant;

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
