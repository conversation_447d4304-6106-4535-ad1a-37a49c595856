/**
 * <AUTHOR>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateClockInSchedule as webService } from './update-clock-in-schedule.browser';
import { updateClockInSchedule as nodeService } from './update-clock-in-schedule.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    checkChannel: 'LOCAL',
    checkTime: 1671179734,
    staffId: 1,
    checkType: 'ON',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    checkChannel: 'LOCAL',
    checkTime: 1111,
    staffId: 0,
    checkType: 'ON',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    checkChannel: 'LOCAL',
    checkTime: 1671179734,
    staffId: 1,
    checkType: 'ON',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    checkChannel: 'LOCAL',
    checkTime: 1111,
    staffId: 0,
    checkType: 'ON',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
