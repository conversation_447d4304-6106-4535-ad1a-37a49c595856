/**
 * <AUTHOR>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-clock-in-schedule';
import type { SvcQ, SvcRespData } from './update-clock-in-schedule.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function updateClockInSchedule(svcQ: SvcQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
