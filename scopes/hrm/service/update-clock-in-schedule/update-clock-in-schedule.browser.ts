/**
 * <AUTHOR>
 * @since 2022-12-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-clock-in-schedule';
import type { SvcQ, SvcRespData } from './update-clock-in-schedule.type';

const executor = getExecutor(webRequest);

/**
 * @param SvcQ
 * @returns
 */
export function updateClockInSchedule(svcQ: SvcQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
