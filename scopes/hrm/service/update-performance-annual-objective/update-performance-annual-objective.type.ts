/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  AnnualPerformanceObjectiveJSON,
  BackendAnnualPerformanceObjective,
} from '@manyun/hrm.model.annual-performance-objective';

export type SvcQuery = Pick<
  AnnualPerformanceObjectiveJSON,
  'id' | 'name' | 'way' | 'type' | 'subType'
> & {
  resources: string[];
  evalPositions: string[];
  reason?: string;
  measurements: {
    id: number;
    kpiId: number;
    context: string;
    defaultGrade: number[];
    ceiling?: number;
    gradeCriteria: string;
  }[];
};

export type ApiQ = Pick<
  BackendAnnualPerformanceObjective,
  'name' | 'kpiWay' | 'type' | 'subType' | 'id'
> & {
  resources: string[];
  evalPositions: string[];
  reason?: string;
  metrics: {
    /**
     * 评分标准id
     */
    id: number;
    kpiId: number;
    context: string;
    defaultGrade: number[];
    ceiling?: number;
    gradeCriteria: string;
  }[];
};

export type ApiR = Response<string | null>;
