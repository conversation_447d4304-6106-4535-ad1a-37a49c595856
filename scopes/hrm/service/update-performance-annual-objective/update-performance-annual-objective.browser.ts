/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-performance-annual-objective';
import type { SvcQuery } from './update-performance-annual-objective.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updatePerformanceAnnualObjective(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<string | null>> {
  return executor(svcQuery);
}
