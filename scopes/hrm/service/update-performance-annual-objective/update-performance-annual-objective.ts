/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './update-performance-annual-objective.type';

const endpoint = '/pm/pf/updateKpi';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23304)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<string | null>> => {
    const params: ApiQ = {
      name: svcQuery.name,
      resources: svcQuery.resources,
      evalPositions: svcQuery.evalPositions,
      kpiWay: svcQuery.way,
      metrics: svcQuery.measurements,
      type: svcQuery.type,
      subType: svcQuery.subType,
      reason: svcQuery.reason,
      id: svcQuery.id,
    };

    return request.tryPost<string | null, ApiQ>(endpoint, params);
  };
}
