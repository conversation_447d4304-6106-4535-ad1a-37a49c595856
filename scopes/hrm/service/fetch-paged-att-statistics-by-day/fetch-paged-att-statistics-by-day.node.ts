/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-att-statistics-by-day';
import type { SvcQuery, SvcRespData } from './fetch-paged-att-statistics-by-day.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPagedAttStatisticsByDay(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
