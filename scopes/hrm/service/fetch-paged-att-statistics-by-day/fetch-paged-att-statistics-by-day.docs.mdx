---
description: 'A fetchPagedAttStatisticsByDay HTTP API service.'
labels: ['service', 'http', fetch-paged-att-statistics-by-day]
---

## 概述

查询每日统计分页数据

## 使用

### Browser

```ts
import { fetchPagedAttStatisticsByDay } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';

const { error, data } = await fetchPagedAttStatisticsByDay('success');
const { error, data } = await fetchPagedAttStatisticsByDay('error');
```

### Node

```ts
import { fetchPagedAttStatisticsByDay } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day/dist/index.node';

const { data } = await fetchPagedAttStatisticsByDay('success');

try {
  const { data } = await fetchPagedAttStatisticsByDay('error');
} catch (error) {
  // ...
}
```
