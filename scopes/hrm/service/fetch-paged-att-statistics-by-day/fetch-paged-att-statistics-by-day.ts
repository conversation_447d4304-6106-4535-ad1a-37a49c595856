/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-paged-att-statistics-by-day.type';
import { SORT_TYPE_MAPPER } from './fetch-paged-att-statistics-by-day.type';

const endpoint = '/pm/attStatistics/daily/page';

/**
 * 查询每日统计分页数据
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1080)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum: variant.page,
      pageSize: variant.pageSize,
      staffId: variant.staffId,
      blockTagList: variant.blockTags,
      idcTagList: variant.idcTags,
      dutyGroupName: variant.dutyGroupName,
      dutyName: variant.dutyName,
      startDate: variant.startDate,
      endDate: variant.endDate,
      isAbsent: variant.isAbsent,
      isMisOff: variant.isMisOff,
      isMisOn: variant.isMisOn,
      isLateOn: variant.isLateOn,
      isEarlyOff: variant.isEarlyOff,
      isOutWork: variant.isOutWork,
      dutyGroupIdList: variant.dutyGroupIds,
      dutyIdList: variant.dutyIds,
      sortField: variant?.sortField,
      sortType: variant?.sortType ? SORT_TYPE_MAPPER[variant?.sortType] : undefined,
      bizType: variant.bizType,
      needLeave: variant.needLeave !== undefined ? variant.needLeave === 'TRUE' : undefined,
      staffType: variant.staffType,
      needChangeRecord: variant.needChangeRecord,
      needAttendance: variant.needAttendance,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: (data.data ?? []).map(d => ({ ...d })), total: data.total },
      ...rest,
    };
  };
}
