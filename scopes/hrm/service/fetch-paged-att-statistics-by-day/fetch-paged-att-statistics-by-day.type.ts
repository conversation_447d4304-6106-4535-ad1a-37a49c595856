/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SortType = 'descend' | 'ascend';
export type SortField =
  | 'onDutyTime'
  | 'offDutyTime'
  | 'lateOnMinutes'
  | 'earlyOffMinutes'
  | 'workTime'
  | 'outWorkMinutes';

export type BackendSortField = SortField;
export type BackendSortType = 'ASC' | 'DESC';

export const SORT_TYPE_MAPPER: Record<SortType, BackendSortType> = {
  descend: 'DESC',
  ascend: 'ASC',
};

export type SvcQuery = {
  page: number;
  pageSize: number;
  blockTags?: string[];
  idcTags?: string[];
  dutyGroupName?: string;
  dutyName?: string /**班次名称 */;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  isOutWork?: boolean;
  staffId?: number;
  dutyIds?: number[] /**班次id*/;
  dutyGroupIds?: number[] /**班组id  */;
  sortField?: SortField;
  sortType?: SortType;
  bizType?: 'SCHEDULE' | 'OVERTIME';
  needLeave?: 'TRUE' | 'FALSE' /**是否请假 */;
  staffType?: string;
  needChangeRecord?: boolean;
  needAttendance?: boolean; //是否应出勤
};

export type ChangeInfo = {
  replaceStaffId?: number | null;
  dutyId?: string | null;
  dutyName?: string | null;
  scheduleDate?: number | null;
};

export type CheckResultChangeRecord = {
  changeType: string;
  reason: string;
  // 考勤结果id
  checkResultId: number;
  //业务类型
  bizType: string;
  //排班id
  staffScheduleId: string;
  //业务id
  bizId: string;
  changeParam: {
    /**
     * -  ON 上班卡
     * -  OFF 下班卡
     * -  ALL 所有打卡
     */
    checkChangeType?: 'ON' | 'OFF' | 'ALL' | null;
    /**
     * 是否减少假期余额
     */
    reduceBalance?: boolean | null;
    /**
     * 请假类型
     */
    leaveType?: string | null;
    /**
     * 开始时间
     */
    startTime?: number | null;
    /**
     * 结束时间
     */
    endTime?: number | null;
    /**
     * 总时长
     */
    totalTime?: number | null;
    /**
     * 单位：DAY("天"),     HOUR("小时");
     */
    unit?: string | null;
    /**
     * 请假用户或替班人
     */
    changeInfo?: ChangeInfo | null;
  };
  //考勤结果对比
  checkResultCompare: {
    oldCheckResult: BackendDailyAttStatistics;
    newCheckResult: BackendDailyAttStatistics;
  };
  gmtModified: number;
  modifierId: number;
};
export type BackendDailyAttStatistics = {
  blockTag: string;
  dutyGroupName: string;
  bizId?: string;
  dutyName: string;
  id: number;
  idcTag: string;
  offDutyTime: string | null;
  onDutyTime: string | null;
  scheduleDate: number;
  staffId: string;
  staffName: string;
  isMissOnCard: number;
  isMissOffCard: number;
  lateOnMinutes: number;
  earlyOffMinutes: number;
  /**对应班内工作时长 */
  workTime?: number;
  /**应出勤时长 */
  standardWorkMinutes?: number;
  outWorkMinutes?: number;
  bizType?: string;
  leaveType: string;
  /**班次id */
  dutyId: number;
  /**班次开始时间 */
  startTime: number;
  /**班次结束时间 */
  endTime: number;
  /**总时长 */
  totalTime: number;
  /**排班或加班id */
  staffScheduleId: number;
  checkResultChangeRecordModels?: CheckResultChangeRecord[];
  staffType?: string;
  userShift?: string;
  /**
   * 班次信息
   */
  dutyProperties: {
    /**是否允许休息 */
    allowRest: boolean;
    scheduleDate: number;
    /**休息时间段 */
    restRange?: {
      /**时间范围，含次日 */
      chTimeRangeStr?: string;
      /** 结束时间是否为第二天 */
      endIsNextDay: boolean;
      /**结束时间 */
      endTime: string;
      /** 开始时间是否为第二天 */
      startIsNextDay: boolean;
      /**开始时间 */
      startTime: string;
      /** 时间范围，不含次日  */
      timeRangeStr?: string;
    };
  };
  /**班次时间是否覆盖休息日 */
  restDay: boolean;
};

export type DailyAttStatistics = BackendDailyAttStatistics;

export type SvcRespData = {
  data: DailyAttStatistics[];
  total: number;
};

export type RequestRespData = {
  data: BackendDailyAttStatistics[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  staffId?: number /**员工id */;
  blockTagList?: string[] /**楼栋列表 */;
  idcTagList?: string[] /**机房列表 */;
  dutyGroupName?: string /**班组名称 */;
  dutyName?: string /**班次名称 */;
  startDate?: number /**开始时间 */;
  endDate?: number /**结束时间 */;
  isAbsent?: boolean /**是否矿工 */;
  isMisOff?: boolean /**是否缺下班卡 */;
  isMisOn?: boolean /**是否缺上班卡 */;
  isLateOn?: boolean /**是否迟到 */;
  isEarlyOff?: boolean /**是否早退 */;
  isOutWork?: boolean;
  dutyIdList?: number[] /**班次id*/;
  dutyGroupIdList?: number[] /**班组id  */;
  sortField?: BackendSortField;
  sortType?: BackendSortType;
  bizType?: 'SCHEDULE' | 'OVERTIME';
  needLeave?: boolean;
  staffType?: string;
  needChangeRecord?: boolean;
  needAttendance?: boolean /**是否应出勤 */;
};

export type ApiR = ListResponse<BackendDailyAttStatistics[] | null>;
