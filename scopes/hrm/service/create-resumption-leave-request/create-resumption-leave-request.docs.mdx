---
description: 'A createResumptionLeaveRequest HTTP API service.'
labels: ['service', 'http']
---

## 概述

创建请假销假申请

## 使用

### Browser

```ts
import { createResumptionLeaveRequest } from '@manyun/hrm.service.create-resumption-leave-request';

const { error, data } = await createResumptionLeaveRequest({
  bizId: '111',
  reason: '测试数据',
});
const { error, data } = await createResumptionLeaveRequest({
  bizId: '0',
  reason: '测试',
});
```

### Node

```ts
import { createResumptionLeaveRequest } from '@manyun/hrm.service.create-resumption-leave-request/dist/index.node';

const { data } = await createResumptionLeaveRequest({
  bizId: '111',
  reason: '测试数据',
});
const { error, data } = await createResumptionLeaveRequest({
  bizId: '0',
  reason: '测试',
});
```
