/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createResumptionLeaveRequest as webService } from './create-resumption-leave-request.browser';
import { createResumptionLeaveRequest as nodeService } from './create-resumption-leave-request.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    bizId: '111',
    reason: '测试数据',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    bizId: '0',
    reason: '测试',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    bizId: '111',
    reason: '测试数据',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    bizId: '0',
    reason: '测试',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
