/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-resumption-leave-request';
import type { SvcQuery, SvcRespData } from './create-resumption-leave-request.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createResumptionLeaveRequest(
  creareD: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(creareD);
}
