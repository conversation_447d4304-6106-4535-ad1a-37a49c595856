/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-13
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './create-resumption-leave-request.type';

const endpoint = '/pm/check/rollbackAlter';

/**
 * 创建请假销假申请
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/19350)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (createD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      bizId: createD.bizId,
      reason: createD.reason,
      files: createD.attachments?.map(attachement =>
        // @ts-expect-error: TS2345 because of using deprecated method. FIXME @YJX
        McUploadFile.toApiObject({ ...attachement, type: null, src: attachement.src })
      ),
    };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
