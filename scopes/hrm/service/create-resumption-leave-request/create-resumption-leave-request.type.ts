/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-13
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  bizId: string;
  reason: string;
  attachments?: McUploadFile[];
};

/**审批id */
export type SvcRespData = string | null;

export type RequestRespData = string | null;

export type ApiQ = {
  bizId: string /**请假业务id */;
  reason: string /**销假原因 */;
  files?: BackendMcUploadFile[] /**附件 */;
};

export type ApiR = Response<string | null>;
