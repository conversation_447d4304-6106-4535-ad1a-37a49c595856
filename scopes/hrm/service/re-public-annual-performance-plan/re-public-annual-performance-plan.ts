/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './re-public-annual-performance-plan.type';

const endpoint = '/pm/pf/rePublishPlan';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23412)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = {
      id: svcQuery.id,
      evalPositions: svcQuery.evalPositions,
      resources: svcQuery.resourceCodes,
    };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
