/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './re-public-annual-performance-plan';
import type { SvcQuery } from './re-public-annual-performance-plan.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function rePublicAnnualPerformancePlan(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
