---
description: 'A createPartTimeJobs HTTP API service.'
labels: ['service', 'http']
---

兼岗记录添加

## Usage

### Browser

```ts
import { createPartTimeJobs } from '@manyun/hrm.service.create-part-time-jobs';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { CreatePartTimeJobsService } from '@manyun/hrm.service.create-part-time-jobs/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = CreatePartTimeJobsService.from(nodeRequest);
```
