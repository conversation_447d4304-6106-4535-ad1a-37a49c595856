/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import {
  WebRequest,
  request,
  setupRemoteMocks,
  setupRequestSingleton,
} from '@glpdev/symphony.services.request';
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';

import { createPartTimeJobs as webService } from './create-part-time-jobs.browser.js';
import { CreatePartTimeJobsService } from './create-part-time-jobs.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = CreatePartTimeJobsService.from(nodeRequest);

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  setupRequestSingleton(WebRequest.from());
  webMockOff = setupRemoteMocks(request!);
  nodeMockOff = setupRemoteMocks(nodeRequest);
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService();

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService();

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService();

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
