/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import type { WriteBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 员工id
   */
  staffId: number;
  /**
   * 开始时间，每月1月1日
   */
  startTime: number;
  /**
   * 结束时间，未来则为2099年12月01日，时分秒都为0
   */
  endTime: number;
  /**
   * 兼岗楼栋
   */
  partTimeBlocks: string[];
};

export type ApiResponse = WriteBackendResponse;
