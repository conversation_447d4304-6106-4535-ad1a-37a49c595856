/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchNextScheduleById as webService } from './fetch-next-schedule-by-id.browser';
import { fetchNextScheduleById as nodeService } from './fetch-next-schedule-by-id.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    scheduleDate: 1,
    dutyGroupId: 1,
    dutyId: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    scheduleDate: 2,
    dutyGroupId: 2,
    dutyId: 2,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    scheduleDate: 1,
    dutyGroupId: 1,
    dutyId: 1,
  });
  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    scheduleDate: 2,
    dutyGroupId: 2,
    dutyId: 2,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
