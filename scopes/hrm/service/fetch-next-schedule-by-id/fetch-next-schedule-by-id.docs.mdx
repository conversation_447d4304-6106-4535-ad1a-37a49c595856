---
description: 'A fetchNextScheduleById HTTP API service.'
labels: ['service', 'http', next-schedule-by-id]
---

## 概述

根据排班 id 查询下一次排班

## 使用

### Browser

```ts
import { fetchNextScheduleById } from '@hrm/service.fetch-next-schedule-by-id';

const { error, data } = await fetchNextScheduleById({
  scheduleDate: 1,
  dutyGroupId: 1,
  dutyId: 1,
});
const { error, data } = await fetchNextScheduleById({
  scheduleDate: 2,
  dutyGroupId: 2,
  dutyId: 2,
});
```

### Node

```ts
import { fetchNextScheduleById } from '@hrm/service.fetch-next-schedule-by-id/dist/index.node';

const { data } = await fetchNextScheduleById({
  scheduleDate: 1,
  dutyGroupId: 1,
  dutyId: 1,
});

try {
  const { data } = await fetchNextScheduleById({
    scheduleDate: 2,
    dutyGroupId: 2,
    dutyId: 2,
  });
} catch (error) {
  // ...
}
```
