/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { CurrentSchedule } from '@manyun/hrm.service.fetch-current-schedule';

export type SvcRespData = { data: ScheduleJson[]; total: number };
export type ScheduleJson = CurrentSchedule & { sameAttGroup: boolean };
export type RequestRespData = {
  data: ScheduleJson[];
  total: number;
} | null;

export type ApiQ = {
  scheduleDate: number;
  dutyGroupId: number;
  dutyId: number;
};

export type ApiR = ListResponse<ScheduleJson[] | null>;
