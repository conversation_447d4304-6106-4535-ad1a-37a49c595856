/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 有效 1|无效 0
   */
  certEffectStatus?: number | null;
  /**
   * 已上传 1| 待上传 0
   */
  uploadStatus?: number | null;
  /**
   * 审批中: APPROVING
   */
  instStatus?: string | null;
  /**
   * 1:即将过期
   */
  willExpire?: number | null;
  /**
   *  1:待复审
   */
  willCheck?: number | null;
  /**
   * 用户名称
   */
  userName?: string | null;
  /**
   * 岗位code
   */
  positionCode?: string | null;
  /**
   * 证书名称
   */
  certName?: string | null;
  /**
   * 楼栋
   */
  blockGuidList?: string[] | null;
};

export type StaticCount = {
  uploadNum: number;
  waitedUploadNum: number;
  effectNum: number;
  unEffectNum: number;
  approvingNum: number;
  willExpireNum: number;
  willCheckNum: number;
};

export type ApiResponse = BackendResponse<StaticCount | null>;
