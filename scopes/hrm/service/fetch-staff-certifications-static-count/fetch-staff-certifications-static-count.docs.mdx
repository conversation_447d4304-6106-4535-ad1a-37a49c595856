---
description: 'A fetchStaffCertificationsStaticCount HTTP API service.'
labels: ['service', 'http']
---

员工资质证书档案数量统计

## Usage

### Browser

```ts
import { fetchStaffCertificationsStaticCount } from '@manyun/hrm.service.fetch-staff-certifications-static-count';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchStaffCertificationsStaticCountService } from '@manyun/hrm.service.fetch-staff-certifications-static-count/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchStaffCertificationsStaticCountService.from(nodeRequest);
```
