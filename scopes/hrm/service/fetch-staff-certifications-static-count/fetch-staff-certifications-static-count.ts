/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-staff-certifications-static-count.type.js';

const endpoint = '/pm/cert/position/header/statistic';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30772)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return request.tryPost<ApiResponse['data'], ApiArgs>(endpoint, args);
  };
}
