/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-staff-certifications-static-count.js';
import type { ApiArgs, ApiResponse } from './fetch-staff-certifications-static-count.type.js';

/**
 * @param args
 * @returns
 */
export function fetchStaffCertificationsStaticCount(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
