/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './delete-staff-certification-standards.js';
import type { ApiArgs, ApiResponse } from './delete-staff-certification-standards.type.js';

/**
 * @param args
 * @returns
 */
export function deleteStaffCertificationStandards(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
