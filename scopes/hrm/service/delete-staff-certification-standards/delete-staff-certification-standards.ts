/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './delete-staff-certification-standards.type.js';

const endpoint = '/pm/cert/position/delete';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30756)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse>> => {
    return request.tryPost<ApiResponse, ApiArgs>(endpoint, args);
  };
}
