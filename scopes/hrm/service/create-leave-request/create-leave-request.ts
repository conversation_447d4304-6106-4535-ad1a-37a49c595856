/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, LeaveRecord, SvcQuery, SvcRespData } from './create-leave-request.type';

const endpoint = '/pm/check/addLeaveRecord';
const detailEndPoint = '/pm/check/alterDetail';

/**
 *  添加请假记录
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1128)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      leaveType: variant.type,
      leaveReason: variant.reason,
      totalTime: variant.totalTime,
      unit: variant.timeUnit,
      startTime: variant.startTime,
      startTimeDesc: variant.startTimeDesc,
      endTimeDesc: variant.endTimeDesc,
      endTime: variant.endTime,
      applyStaffId: variant.apply,
      files: variant.attachments?.map(obj => McUploadFile.toApiObject(obj)),
      leaveInfoList: variant.additionalShifts,
      alterContexts: variant.subLeaveInfos?.map(d => ({
        totalTime: d.totalTime,
        unit: d.timeUnit,
        alterSubType: d.subType,
      })),
    };

    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, params);

    /**@YJX Fix */
    if (data) {
      const detail = await request.tryPost<LeaveRecord, { bizId: string }>(detailEndPoint, {
        bizId: data,
      });
      if (detail.error || detail.data === null) {
        return { error, data: null, ...rest };
      }
      return { error, data: detail.data, ...rest };
    }
    return { error, data: null, ...rest };
  };
}
