/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createLeaveRequest as webService } from './create-leave-request.browser';
import { createLeaveRequest as nodeService } from './create-leave-request.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    type: 'ANNUAL_LEAVE',
    reason: '1',
    totalTime: 1,
    timeUnit: 'DAY',
    startTime: 1669132800000,
    endTime: 1669737599999,
    startTimeDesc: '2022-11-23',
    endTimeDesc: '2022-11-29',
    apply: 1,
    additionalShifts: [
      {
        dutyId: 15,
        scheduleDate: 1669219200000,
        replaceStaffId: 5,
      },
    ],
    subLeaveInfos: [
      {
        totalTime: 1,
        timeUnit: 'DAY',
        subType: 'ANNUAL_LEAVE',
      },
    ],
  });
  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    type: 'ANNUAL_LEAVE',
    reason: '1',
    totalTime: 0,
    timeUnit: 'DAY',
    startTime: 0,
    endTime: 0,
    apply: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    type: 'ANNUAL_LEAVE',
    reason: '1',
    totalTime: 1,
    timeUnit: 'DAY',
    startTime: 1669132800000,
    endTime: 1669737599999,
    startTimeDesc: '2022-11-23',
    endTimeDesc: '2022-11-29',
    apply: 1,
    additionalShifts: [
      {
        dutyId: 15,
        scheduleDate: 1669219200000,
        replaceStaffId: 5,
      },
    ],
    subLeaveInfos: [
      {
        totalTime: 1,
        timeUnit: 'DAY',
        subType: 'ANNUAL_LEAVE',
      },
    ],
  });
  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    type: 'ANNUAL_LEAVE',
    reason: '1',
    totalTime: 0,
    timeUnit: 'DAY',
    startTime: 0,
    endTime: 0,
    apply: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
