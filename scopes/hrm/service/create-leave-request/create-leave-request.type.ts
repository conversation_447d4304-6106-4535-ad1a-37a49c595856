/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { LeaveRequestType, LeaveTimeUnit } from '@manyun/hrm.model.shift-adjustment-ticket';

export type AdditionalShift = {
  dutyId: number;
  scheduleDate: number;
  replaceStaffId?: number;
};

export type SubLeaveInfo = {
  subType: LeaveRequestType;
  totalTime: number;
  timeUnit: LeaveTimeUnit;
};

export type BacknedSubLeaveInfo = {
  alterSubType: LeaveRequestType;
  totalTime: number;
  unit: LeaveTimeUnit;
};

export type SvcQuery = {
  type: LeaveRequestType;
  reason: string;
  totalTime: number;
  timeUnit: LeaveTimeUnit;
  startTime: number;
  startTimeDesc?: string;
  endTime: number;
  endTimeDesc?: string;
  apply: number;
  additionalShifts?: AdditionalShift[];
  attachments?: McUploadFile[];
  subLeaveInfos?: SubLeaveInfo[];
};

export type LeaveRecord = {
  alterSubType: null;
  alterType: string;
  applyReason: string;
  applyStaffId: number;
  applyStaffName: string;
  bizId: string;
  bizStatus: string;
  blockTag: string;
  creatorId: number;
  creatorName: string;
  endTime: null;
  finishTime: number | null;
  gmtCreate: number;
  gmtModified: number;
  id: number;
  procInstanceId: string;
};

export type SvcRespData = LeaveRecord | null;

export type ApiQ = {
  leaveType: LeaveRequestType;
  leaveReason: string;
  totalTime: number;
  unit: LeaveTimeUnit;
  startTime: number;
  startTimeDesc?: string;
  endTime: number;
  endTimeDesc?: string;
  leaveInfoList?: AdditionalShift[];
  applyStaffId: number;
  files?: BackendMcUploadFile[];
  alterContexts?: BacknedSubLeaveInfo[];
};

export type ApiR = Response<string>;
