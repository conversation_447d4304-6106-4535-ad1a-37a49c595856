/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 证书名称
   */
  certName?: string | null;
  /**
   * 岗位code
   */
  positionCode?: string | null;
  /**
   * 岗位证书id
   */
  positionCertId?: number | null;
  pageNum: number;
  pageSize: number;
};

export type ApiResponseData = {
  /**
   * 主键id
   */
  id: number;
  /**
   * 证书名称
   */
  certName: string;
  /**
   * 岗位code
   */
  positionCode: string;
  /**
   * 岗位
   */
  position: string;
  /**
   * 是否必填
   */
  required: boolean;
  /**
   * 是否复审
   */
  checked: boolean;
  /**
   * 创建时间
   */
  gmtCreate: string;
  /**
   * 修改时间
   */
  gmtModified: string;
};

export type ApiResponse = ListBackendResponse<ApiResponseData[]>;
