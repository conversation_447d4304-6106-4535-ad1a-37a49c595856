/**
 * <AUTHOR>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-shifts.type';

const endpoint = '/pm/duty/list';

/**
 *
 * 查询班次列表
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1264)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { dutyName: svcQ?.dutyName, timeLimit: svcQ?.timeLimit };

    const { error, data, ...rest } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => ({ id: d.id, name: d.dutyName })), total: data.total },
      ...rest,
    };
  };
}
