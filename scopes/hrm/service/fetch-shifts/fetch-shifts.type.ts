/**
 * <AUTHOR>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendShift, ShiftJSON } from '@manyun/hrm.model.shift';

export type SvcQuery = {
  dutyName?: string;
  timeLimit?: boolean; //是否需要限制班次时长
};

export type SvcRespData = {
  data: ShiftJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendShift[] | null;
  total: number;
};

export type ApiQ = {
  dutyName?: string;
  timeLimit?: boolean; //是否需要限制班次时长
};

export type ApiR = ListResponse<BackendShift[] | null>;
