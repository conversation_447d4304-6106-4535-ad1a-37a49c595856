/**
 * <AUTHOR>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-shifts';
import type { SvcQuery, SvcRespData } from './fetch-shifts.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchShifts(svcQ?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
