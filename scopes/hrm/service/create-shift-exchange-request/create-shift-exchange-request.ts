/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  ShiftExchangeRecord,
  SvcQuery,
  SvcRespData,
} from './create-shift-exchange-request.type';

const endpoint = '/pm/check/addExchangeRecord';
const detailEndPoint = '/pm/check/alterDetail';

/**
 * 添加换班记录
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1120)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyDutyId: variant.applyShiftId,
      applyScheduleDate: variant.applayShiftDate,
      applyStaffId: variant.applyStaffId,
      targetDutyId: variant.targetShiftId,
      targetScheduleDate: variant.targetShiftDate,
      targetStaffId: variant.targetStaffId,
      exchangeReason: variant.reason,
      files: variant.attachments?.map(obj => McUploadFile.toApiObject(obj)),
      procInstanceId: variant.proInstanceId,
    };

    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, params);

    /**@YJX Fix */
    if (data) {
      const detail = await request.tryPost<ShiftExchangeRecord, { bizId: string }>(detailEndPoint, {
        bizId: data,
      });
      if (detail.error || detail.data === null) {
        return { error, data: null, ...rest };
      }
      return { error, data: detail.data, ...rest };
    }
    return { error, data: null, ...rest };
  };
}
