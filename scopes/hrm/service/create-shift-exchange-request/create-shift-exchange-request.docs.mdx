---
description: 'A createShiftExchangeRequest HTTP API service.'
labels: ['service', 'http', create-shift-exchange-request]
---

## 概述

TODO

## 使用

### Browser

```ts
import { createShiftExchangeRequest } from '@manyun/hrm.service.create-shift-exchange-request';

const { error, data } = await createShiftExchangeRequest('success');
const { error, data } = await createShiftExchangeRequest('error');
```

### Node

```ts
import { createShiftExchangeRequest } from '@manyun/hrm.service.create-shift-exchange-request/dist/index.node';

const { data } = await createShiftExchangeRequest('success');

try {
  const { data } = await createShiftExchangeRequest('error');
} catch (error) {
  // ...
}
```
