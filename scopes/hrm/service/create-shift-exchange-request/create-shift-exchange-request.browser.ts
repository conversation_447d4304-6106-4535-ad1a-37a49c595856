/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-shift-exchange-request';
import type { SvcQuery, SvcRespData } from './create-shift-exchange-request.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createShiftExchangeRequest(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
