/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ShiftExchangeRecord = {
  alterSubType: null;
  alterType: string;
  applyReason: string;
  applyStaffId: number;
  applyStaffName: string;
  bizId: string;
  bizStatus: string;
  blockTag: string;
  creatorId: number;
  creatorName: string;
  endTime: null;
  files: any[];
  finishTime: number | null;
  gmtCreate: number;
  gmtModified: number;
  id: number;
  procInstanceId: string;
};
export type SvcQuery = {
  applyShiftId: number;
  applayShiftDate: number;
  applyStaffId: number;
  proInstanceId?: string | number;
  targetShiftId: number;
  targetShiftDate: number;
  targetStaffId: number;
  reason?: string;
  attachments?: McUploadFile[];
};

export type SvcRespData = ShiftExchangeRecord | null;

export type ApiQ = {
  applyDutyId: number;
  applyScheduleDate: number;
  applyStaffId: number;
  exchangeReason?: string;
  files?: BackendMcUploadFile[];
  procInstanceId?: string | number;
  targetDutyId: number;
  targetScheduleDate: number;
  targetStaffId: number;
};

export type ApiR = Response<string>;
