/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-balance';
import type { SvcQuery, SvcRespData } from './fetch-paged-balance.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPagedBalance(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
