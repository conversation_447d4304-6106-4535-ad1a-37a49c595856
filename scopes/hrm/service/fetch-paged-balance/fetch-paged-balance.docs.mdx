---
description: 'A fetchPagedBalance HTTP API service.'
labels: ['service', 'http', fetch-paged-balance]
---

## 概述

余额分页列表查询

## 使用

### Browser

```ts
import { fetchPagedBalance } from '@manyun/hrm.service.fetch-paged-balance';

const { error, data } = await fetchPagedBalance({
  staffId: '1',
  page: 1,
  pageSize: 10,
});
const { error, data } = await fetchPagedBalance({
  staffId: '-1',
  page: 1,
  pageSize: 10,
});
```

### Node

```ts
import { fetchPagedBalance } from '@manyun/hrm.service.fetch-paged-balance/dist/index.node';

const { data } = await fetchPagedBalance({
  staffId: '1',
  page: 1,
  pageSize: 10,
});

try {
  const { data } = await fetchPagedBalance({
    staffId: '-1',
    page: 1,
    pageSize: 10,
  });
} catch (error) {
  // ...
}
```
