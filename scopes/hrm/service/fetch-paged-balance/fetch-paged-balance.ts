/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-paged-balance.type';

const endpoint = '/dctrans/balance/page';

/**
 * 余额分页列表查询
 * @see [Doc](http://yapi.manyun-local.com/project/124/interface/api/26421)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      staffId: variant.staffId,
      maintenanceFlag: variant.onlyMaintenanceUser,
      pageNum: variant.page,
      pageSize: variant.pageSize,
      userShiftsList: variant.userShifts,
      blockGuidList: variant.blockGuids,
      yearDate: variant.yearDate,
      onJob: variant.userState ? (variant.userState === 'in-service' ? true : false) : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => ({ ...d, userId: d.staffId })), total: data.total },
      ...rest,
    };
  };
}
