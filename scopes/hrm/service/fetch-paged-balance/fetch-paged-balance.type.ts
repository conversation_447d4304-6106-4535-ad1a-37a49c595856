/**
 * <AUTHOR>
 * @since 2022-6-9
 *
 * @packageDocumentation
 */
import type { UserShifts } from '@manyun/auth-hub.model.user';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendUserBalance = {
  id: number;
  staffId: number;
  userName: string;
  hireDate: string /**入职日期 */;
  workDate: string /**工作日期 */;
  leaveDate?: string | null /**离职日期 */;
  paidLeaveAvailableBalance: number /**可用调休 */;
  closeExpire: boolean /**是否临近到期 */;
  blockGuid: string /**所属机房 */;
  userShifts: UserShifts;
  year: string;
  //享受年假
  totalBalance: number;
  //享受法定年假
  statutoryBalance: number;
  //享受公司年假
  companyBalance: number;
  //享受结转年假
  carryOverBalance: number;
  //已用总年假
  totalUsedBalance: number;
  //已用结转年假
  carryOverUsedBalance: number;
  //已用法定年假
  statutoryUsedBalance: number;
  //已用公司年假
  companyUsedBalance: number;
  //总可用年假
  totalAvailableBalance: number;
  //可用结账年假
  carryOverAvailableBalance: number;
  //可用法定年假
  statutoryAvailableBalance: number;
  //可用公司年假
  companyAvailableBalance: number;
  //享受病假
  sickSalaryBalance: number;
  //已用病假
  sickSalaryUsedBalance: number;
  //可用病假
  sickSalaryAvailableBalance: number;
};

export type UserBalance = {
  id: number;
  userId: number;
  userName: string;
  hireDate: string;
  workDate: string;
  leaveDate?: string | null;
  paidLeaveAvailableBalance: number;
  closeExpire: boolean;
  blockGuid: string;
  userShifts: UserShifts;
  year: string;
  //享受年假
  totalBalance: number;
  //享受法定年假
  statutoryBalance: number;
  //享受公司年假
  companyBalance: number;
  //享受结转年假
  carryOverBalance: number;
  //已用总年假
  totalUsedBalance: number;
  //已用结转年假
  carryOverUsedBalance: number;
  //已用法定年假
  statutoryUsedBalance: number;
  //已用公司年假
  companyUsedBalance: number;
  //总可用年假
  totalAvailableBalance: number;
  //可用结账年假
  carryOverAvailableBalance: number;
  //可用法定年假
  statutoryAvailableBalance: number;
  //可用公司年假
  companyAvailableBalance: number;
  //享受病假
  sickSalaryBalance: number;
  //已用病假
  sickSalaryUsedBalance: number;
  //可用病假
  sickSalaryAvailableBalance: number;
};

export type SvcQuery = {
  staffId?: string;
  onlyMaintenanceUser?: boolean;
  userShifts: UserShifts[];
  page: number;
  pageSize: number;
  blockGuids?: string[];
  yearDate?: number;
  userState?: string;
};

export type SvcRespData = {
  data: UserBalance[];
  total: number;
};

export type RequestRespData = {
  data: BackendUserBalance[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  userShiftsList: UserShifts[];
  staffId?: string;
  maintenanceFlag?: boolean;
  blockGuidList?: string[];
  yearDate?: number;
  onJob?: boolean;
};

export type ApiR = ListResponse<BackendUserBalance[] | null>;
