---
description: 'A createRestRequest HTTP API service.'
labels: ['service', 'http', create-rest-request]
---

## 概述

创建顶班申请

## 使用

### Browser

```ts
import { createRestRequest } from '@manyun/hrm.service.create-rest-request';

const { error, data } = await createRestRequest('success');
const { error, data } = await createRestRequest('error');
```

### Node

```ts
import { createRestRequest } from '@manyun/hrm.service.create-rest-request/dist/index.node';

const { data } = await createRestRequest('success');

try {
  const { data } = await createRestRequest('error');
} catch (error) {
  // ...
}
```
