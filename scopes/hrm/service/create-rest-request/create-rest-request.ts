/**
 * <AUTHOR>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './create-rest-request.type';

const endpoint = '/pm/check/addRestRecord';

/**
 * 添加顶班记录
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18128)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyStaffId: variant.applyStaffId,
      leaveReason: variant.leaveReason,
      leaveInfoList: variant.leaveInfoList,
      files: variant.files,
    };

    const { error, data, ...rest } = await request.tryPost<string | null, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
