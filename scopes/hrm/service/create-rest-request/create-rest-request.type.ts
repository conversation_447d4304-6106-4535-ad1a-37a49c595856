/**
 * <AUTHOR>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type AdditionalShift = {
  dutyId: number;
  scheduleDate: number;
  replaceStaffId?: number;
};

export type SvcQuery = {
  applyStaffId: number;
  leaveReason: string;
  leaveInfoList?: AdditionalShift[];
  files?: BackendMcUploadFile[];
};

export type SvcRespData = string | null;

export type ApiQ = {
  applyStaffId: number;
  leaveReason: string;
  leaveInfoList?: AdditionalShift[];
  files?: BackendMcUploadFile[];
};

export type ApiR = Response<string>;
