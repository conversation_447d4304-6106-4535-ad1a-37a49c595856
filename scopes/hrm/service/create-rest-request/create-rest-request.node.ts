/**
 * <AUTHOR>
 * @since 2022-6-8
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-rest-request';
import type { SvcQuery, SvcRespData } from './create-rest-request.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function createRestRequest(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
