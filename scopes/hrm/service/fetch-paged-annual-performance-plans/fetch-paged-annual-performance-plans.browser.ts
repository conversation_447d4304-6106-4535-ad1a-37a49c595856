/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-annual-performance-plans';
import type {
  PagedAnnualPerformancePlan,
  SvcQuery,
} from './fetch-paged-annual-performance-plans.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPagedAnnualPerformancePlans(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<PagedAnnualPerformancePlan>> {
  return executor(svcQuery);
}
