/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  PagedAnnualPerformancePlan,
  SvcQuery,
} from './fetch-paged-annual-performance-plans.type';

const endpoint = 'pm/pf/planPage';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23421)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<PagedAnnualPerformancePlan>> => {
    const params: ApiQ = {
      pageNum: svcQuery.page,
      pageSize: svcQuery.pageSize,
      planName: svcQuery.name,
      year: svcQuery.year,
      resources: svcQuery.resourceCodes,
      evalPositions: svcQuery.positions,
      splitRule: svcQuery.splitRule,
      creatorId: svcQuery.createUserId,
      createStartTime: svcQuery.createTimeRange ? svcQuery.createTimeRange[0] : undefined,
      createEndTime: svcQuery.createTimeRange ? svcQuery.createTimeRange[1] : undefined,
      gmtCreateDesc: svcQuery.orderBy?.createdAt
        ? svcQuery.orderBy.createdAt === 'descend'
        : undefined,
      gmtModifiedDesc: svcQuery.orderBy?.modifiedAt
        ? svcQuery.orderBy.modifiedAt === 'descend'
        : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<PagedAnnualPerformancePlan | null, ApiQ>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
