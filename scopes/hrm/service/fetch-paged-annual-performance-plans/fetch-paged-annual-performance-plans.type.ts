/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendAnnualPerformancePlan,
  BackendAnnualPerformancePlanSplitRule,
} from '@manyun/hrm.model.annual-performance-plan';

export type Order = 'ascend' | 'descend';

export type SvcQuery = {
  page: number;
  pageSize: number;
  name?: string;
  year?: string;
  resourceCodes?: string[];
  positions?: string[];
  splitRule?: BackendAnnualPerformancePlanSplitRule;
  createUserId?: number;
  createTimeRange?: [number, number];
  orderBy?: {
    createdAt?: Order;
    modifiedAt?: Order;
  };
};

export type ApiAnnualPerformancePlan = Omit<
  BackendAnnualPerformancePlan,
  'resources' | 'createUser' | 'evalPositions'
> & {
  resources: string[];
  evalPositions: string[];
  creatorId: number;
};

export type PagedAnnualPerformancePlan = {
  data: ApiAnnualPerformancePlan[];
  total: number;
};

export type ApiQ = {
  pageSize: number;
  pageNum: number;
  planName?: string;
  year?: string;
  resources?: string[];
  evalPositions?: string[];
  splitRule?: BackendAnnualPerformancePlanSplitRule;
  creatorId?: number;
  createStartTime?: number;
  createEndTime?: number;
  gmtCreateDesc?: boolean;
  gmtModifiedDesc?: boolean;
};

export type ApiR = ListResponse<ApiAnnualPerformancePlan[] | null>;
