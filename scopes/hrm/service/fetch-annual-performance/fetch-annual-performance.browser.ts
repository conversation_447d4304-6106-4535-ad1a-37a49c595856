/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-annual-performance';
import type { ApiAnnualPerformance, SvcQuery } from './fetch-annual-performance.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAnnualPerformance(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<ApiAnnualPerformance | null>> {
  return executor(svcQuery);
}
