/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiAnnualPerformance, ApiQ, SvcQuery } from './fetch-annual-performance.type';

const endpoint = '/pm/pf/year/detail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23448)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery
  ): Promise<EnhancedAxiosResponse<ApiAnnualPerformance | null>> => {
    const params: ApiQ = {
      year: svcQuery.year,
      evalPosition: svcQuery.performancePosition,
      staffId: svcQuery.staffId,
      type: svcQuery.type,
      period: svcQuery.period,
      id: svcQuery.id,
      infoSubType: svcQuery.infoSubType,
    };

    return await request.tryPost<ApiAnnualPerformance | null, ApiQ>(endpoint, params);
  };
}
