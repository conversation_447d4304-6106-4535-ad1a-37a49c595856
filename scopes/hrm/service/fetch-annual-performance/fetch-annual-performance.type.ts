/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import type { BackendPerformance, BackendPerformanceType } from '@manyun/hrm.model.performance';

export type SvcQuery = {
  year?: string;
  performancePosition?: string;
  staffId?: number;
  type?: BackendPerformanceType;
  period?: BackendPerformancePeriod;
  infoSubType?: string;
  id?: number;
};

export type ApiAnnualPerformance = Omit<
  BackendPerformance,
  | 'staff'
  | 'region'
  | 'superiors'
  | 'secSuperiors'
  | 'hrs'
  | 'currentEvalStaffs'
  | 'evaluations'
  | 'pfSubType'
> & {
  staffId: number;
  superiors: number[] | null;
  secSuperiors: number[] | null;
  hrs: number[] | null;
  currentEvalStaffIds: number[] | null;
};

export type ApiQ = {
  year?: string;
  evalPosition?: string;
  staffId?: number;
  type?: BackendPerformanceType;
  period?: BackendPerformancePeriod;
  infoSubType?: string;
  id?: number;
};

export type ApiR = Response<ApiAnnualPerformance | null>;
