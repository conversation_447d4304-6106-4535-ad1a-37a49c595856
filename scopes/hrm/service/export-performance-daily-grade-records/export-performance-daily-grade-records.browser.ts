/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-performance-daily-grade-records';
import type { SvcQuery } from './export-performance-daily-grade-records.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportPerformanceDailyGradeRecords(
  svcQuery?: SvcQuery
): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(svcQuery);
}
