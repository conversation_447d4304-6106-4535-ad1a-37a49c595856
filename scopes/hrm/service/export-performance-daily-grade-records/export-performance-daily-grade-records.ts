/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './export-performance-daily-grade-records.type';

const endpoint = '/pm/pf/kpiRecordExport';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23790)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<Blob>> => {
    const params: ApiQ = {
      name: svcQuery?.name ?? undefined,
      eqName: svcQuery?.eqName ?? undefined,
      regionCodes: svcQuery?.regionCodes ?? undefined,
      resources: svcQuery?.idcTags ?? undefined,
      evalPositions: svcQuery?.position ? [svcQuery.position] : undefined,
      type: svcQuery?.type ?? undefined,
      types: svcQuery?.types ?? undefined,
      // subType: svcQuery?.subType,
      createStartTime: svcQuery?.createTimeRange ? svcQuery.createTimeRange[0] : undefined,
      createEndTime: svcQuery?.createTimeRange ? svcQuery.createTimeRange[1] : undefined,
      occurStartTime: svcQuery?.occurTimeRange ? svcQuery.occurTimeRange[0] : undefined,
      occurEndTime: svcQuery?.occurTimeRange ? svcQuery.occurTimeRange[1] : undefined,
      superiorId: svcQuery?.superiorId ?? undefined,
      regionCodeDesc: svcQuery?.orderBy?.region ? svcQuery.orderBy.region === 'descend' : undefined,
      resourceDesc: svcQuery?.orderBy?.idc ? svcQuery.orderBy.idc === 'descend' : undefined,
      nameDesc: svcQuery?.orderBy?.name ? svcQuery.orderBy.name === 'descend' : undefined,
      typeDesc: svcQuery?.orderBy?.type ? svcQuery.orderBy.type === 'descend' : undefined,
      subTypeDesc: svcQuery?.orderBy?.subType ? svcQuery.orderBy.subType === 'descend' : undefined,
      occurTimeDesc: svcQuery?.orderBy?.occurTime
        ? svcQuery.orderBy.occurTime === 'descend'
        : undefined,
      gradeDesc: svcQuery?.orderBy?.grade ? svcQuery.orderBy.grade === 'descend' : undefined,
      gmtCreateDesc: svcQuery?.orderBy?.createdAt
        ? svcQuery.orderBy.createdAt === 'descend'
        : undefined,
      gmtModifiedDesc: svcQuery?.orderBy?.modifiedAt
        ? svcQuery.orderBy.modifiedAt === 'descend'
        : undefined,
      staffIdDesc: svcQuery?.orderBy?.userId ? svcQuery.orderBy.userId === 'descend' : undefined,
      evalPositionDesc: svcQuery?.orderBy?.position
        ? svcQuery.orderBy.position === 'descend'
        : undefined,
      blockGuids: svcQuery?.blockGuids,
      instStatusList: svcQuery?.instStatusList,
    };

    return request.tryPost<Blob, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
