---
description: 'A updateIdcMatchDepartment HTTP API service.'
labels: ['service', 'http']
---

机房与部门映射更新

## Usage

### Browser

```ts
import { updateIdcMatchDepartment } from '@manyun/hrm.service.update-idc-match-department';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { UpdateIdcMatchDepartmentService } from '@manyun/hrm.service.update-idc-match-department/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = UpdateIdcMatchDepartmentService.from(nodeRequest);
```
