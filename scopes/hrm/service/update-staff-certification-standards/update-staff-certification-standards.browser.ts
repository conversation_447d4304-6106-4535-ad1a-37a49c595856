/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './update-staff-certification-standards.js';
import type { ApiArgs, ApiResponse } from './update-staff-certification-standards.type.js';

/**
 * @param args
 * @returns
 */
export function updateStaffCertificationStandards(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
