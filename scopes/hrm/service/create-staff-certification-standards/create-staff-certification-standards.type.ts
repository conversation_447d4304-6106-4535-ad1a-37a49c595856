/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 证书名称
   */
  certName: string;
  /**
   * 岗位code
   */
  positionCode: string;
  /**
   * 是否必填 true: 是
   */
  required: boolean;
  /**
   * 是否复审 true: 是
   */
  checked: boolean;
};

export type ApiResponse = BackendResponse<null>;
