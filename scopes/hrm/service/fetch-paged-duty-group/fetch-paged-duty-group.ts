/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-paged-duty-group.type';

const endpoint = '/pm/dutyGroup/page';

/**
 * @see [Doc](YAPI http://yapi.manyun-local.com/project/78/interface/api/1320 )
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum: svcQuery.pageNum,
      pageSize: svcQuery.pageSize,
      staffId: svcQuery.staffId,
      staffName: svcQuery.staffName,
      blockGuids: svcQuery.blockGuids,
      dutyGroupName: svcQuery.dutyGroupName,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
