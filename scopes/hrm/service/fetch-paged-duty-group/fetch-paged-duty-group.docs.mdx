---
description: 'A fetchPagedDutyGroup HTTP API service.'
labels: ['service', 'http', fetch-paged-duty-group]
---

## 概述

分页查询班组接口

## 使用

### Browser

```ts
import { fetchPagedDutyGroup } from '@hrm/service.fetch-paged-duty-group';

const { error, data } = await fetchPagedDutyGroup({ pageNum: 1, pageSize: 10 });
const { error, data } = await fetchPagedDutyGroup({ pageNum: 2, pageSize: 10 });
```

### Node

```ts
import { fetchPagedDutyGroup } from '@hrm/service.fetch-paged-duty-group/dist/index.node';

const { data } = await fetchPagedDutyGroup({ pageNum: 1, pageSize: 10 });

try {
  const { data } = await fetchPagedDutyGroup({ pageNum: 2, pageSize: 10 });
} catch (error) {
  // ...
}
```
