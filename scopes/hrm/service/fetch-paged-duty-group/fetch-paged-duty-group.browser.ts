/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-22
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-duty-group';
import type { SvcQuery, SvcRespData } from './fetch-paged-duty-group.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPagedDutyGroup(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
