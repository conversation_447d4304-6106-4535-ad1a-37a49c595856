/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  pageNum: number;
  pageSize: number;
  staffId?: number;
  staffName?: string;
  blockGuids?: string[];
  dutyGroupName?: string;
};

export type BackendDutyGroup = {
  attGroupId: number;
  blockTag: string;
  gmtCreate: number;
  gmtModified: number;
  groupName: string;
  id: number;
  idcTag: string;
  modifierId: number;
  modifierName: string;
  staffList: Staff[];
};

export type Staff = {
  id: number;
  loginName: string;
  userName: string;
};

export type SvcRespData = {
  data: BackendDutyGroup[];
  total: number;
};

export type RequestRespData = {
  data: BackendDutyGroup[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  staffId?: number;
  staffName?: string;
  blockGuids?: string[];
  dutyGroupName?: string;
};

export type ApiR = ListResponse<BackendDutyGroup[] | null>;
