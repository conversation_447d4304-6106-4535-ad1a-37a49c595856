/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './delete-shift.type';

const endpoint = '/pm/duty/delete';

/**
 * 删除班次
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1248)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...variant };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
