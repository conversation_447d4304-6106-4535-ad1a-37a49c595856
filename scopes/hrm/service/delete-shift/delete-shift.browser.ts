/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-shift';
import type { SvcQuery, SvcRespData } from './delete-shift.type';

const executor = getExecutor(webRequest);

/**
 * @param deleteParams
 * @returns
 */
export function deleteShift(deleteParams: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(deleteParams);
}
