/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-12
 *
 * @packageDocumentation
 */
import { ShiftAdjustmentTicket } from '@manyun/hrm.model.shift-adjustment-ticket';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-can-revoke-shift-adjustments.type';

const endpoint = '/pm/check/rollbackSchedule';

/**
 * 查询可进行销假的排班调整记录
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/19758)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, {});

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(d => ShiftAdjustmentTicket.fromApiObject(d).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
