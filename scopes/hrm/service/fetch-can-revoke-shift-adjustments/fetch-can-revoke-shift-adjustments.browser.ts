/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-can-revoke-shift-adjustments';
import type { SvcRespData } from './fetch-can-revoke-shift-adjustments.type';

const executor = getExecutor(webRequest);

/**
 * @param
 * @returns
 */
export function fetchCanRevokeShiftAdjustments(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
