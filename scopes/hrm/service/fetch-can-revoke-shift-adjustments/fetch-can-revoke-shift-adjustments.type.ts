/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import {
  BackendShiftAdjustmentTicket,
  ShiftAdjustmentTicketJSON,
} from '@manyun/hrm.model.shift-adjustment-ticket';

export type SvcQuery = {};

export type SvcRespData = {
  data: ShiftAdjustmentTicketJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendShiftAdjustmentTicket[] | null;
  total: number;
} | null;

export type ApiQ = {};

export type ApiR = ListResponse<BackendShiftAdjustmentTicket[] | null>;
