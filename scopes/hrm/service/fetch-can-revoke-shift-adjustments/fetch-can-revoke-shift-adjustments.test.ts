/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCanRevokeShiftAdjustments as webService } from './fetch-can-revoke-shift-adjustments.browser';
import { fetchCanRevokeShiftAdjustments as nodeService } from './fetch-can-revoke-shift-adjustments.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService();

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
