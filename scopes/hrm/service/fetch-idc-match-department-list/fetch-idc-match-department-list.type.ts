/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';
import type { DeptInfo } from '@manyun/hrm.service.fetch-involved-performance-staff-department';

export type ApiArgs = {
  /**
   * 机房
   */
  idc?: string | null;
  /**
   * 部门id
   */
  deptId?: string | null;
};

export type IdcMatchDepartments = {
  /**
   * 机房
   */
  idc: string;
  /**
   * 部门信息
   */
  deptInfos: DeptInfo[];
};

export type ApiResponse = ListBackendResponse<IdcMatchDepartments[]>;
