---
description: 'A fetchIdcMatchDepartmentList HTTP API service.'
labels: ['service', 'http']
---

机房与部门映射查询

## Usage

### Browser

```ts
import { fetchIdcMatchDepartmentList } from '@manyun/hrm.service.fetch-idc-match-department-list';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchIdcMatchDepartmentListService } from '@manyun/hrm.service.fetch-idc-match-department-list/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchIdcMatchDepartmentListService.from(nodeRequest);
```
