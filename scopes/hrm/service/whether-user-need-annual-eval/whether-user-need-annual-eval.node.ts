/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './whether-user-need-annual-eval';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function whetherUserNeedAnnualEval(): Promise<EnhancedAxiosResponse<boolean>> {
  return executor();
}
