/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

const endpoint = '/pm/pf/year/needEval';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23286)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean>(endpoint, {});
  };
}
