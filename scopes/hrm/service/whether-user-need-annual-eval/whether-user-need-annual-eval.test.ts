/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-29
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { whetherUserNeedAnnualEval as webService } from './whether-user-need-annual-eval.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService();

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
