/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './whether-user-need-annual-eval';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function whetherUserNeedAnnualEval(): Promise<EnhancedAxiosResponse<boolean>> {
  return executor();
}
