/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchShiftsForLeaveRequest as webService } from './fetch-shifts-for-leave-request.browser';
// import { fetchShiftsForLeaveRequest as nodeService } from './fetch-shifts-for-leave-request.node';
import { AlterType } from './fetch-shifts-for-leave-request.type';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    type: AlterType.Exchange,
    shiftDate: 20202010,
    staffId: 1,
  });

  expect(error).toBe(undefined);
});
