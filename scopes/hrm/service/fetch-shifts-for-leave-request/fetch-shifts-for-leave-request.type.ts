/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendShift, ShiftJSON } from '@manyun/hrm.model.shift';

export enum AlterType {
  Leave = 'LEAVE',
  Exchange = 'EXCHANGE',
  Rest = 'REST',
}

export type SvcQuery = {
  type: AlterType;
  shiftDate: number;
  staffId: number;
};

export type SvcRespData = ShiftJSON[];

export type RequestRespData = {
  data: BackendShift[];
} | null;

export type ApiQ = {
  alterType: AlterType;
  scheduleDate: number;
  staffId: number;
};

export type ApiR = ListResponse<BackendShift[] | null>;
