---
description: 'A fetchShiftsForLeaveRequest HTTP API service.'
labels: ['service', 'http', fetch-shifts-for-leave-request]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchShiftsForLeaveRequest } from '@manyun/hrm.service.fetch-shifts-for-leave-request';

const { error, data } = await fetchShiftsForLeaveRequest('success');
const { error, data } = await fetchShiftsForLeaveRequest('error');
```

### Node

```ts
import { fetchShiftsForLeaveRequest } from '@manyun/hrm.service.fetch-shifts-for-leave-request/dist/index.node';

const { data } = await fetchShiftsForLeaveRequest('success');

try {
  const { data } = await fetchShiftsForLeaveRequest('error');
} catch (error) {
  // ...
}
```
