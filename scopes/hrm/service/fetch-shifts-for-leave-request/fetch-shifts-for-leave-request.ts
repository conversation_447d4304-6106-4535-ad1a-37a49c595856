/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { Shift } from '@manyun/hrm.model.shift';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-shifts-for-leave-request.type';

const endpoint = '/pm/duty/scheduleDutyList';

/**
 *  查询请假排班班次
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1280)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      alterType: variant.type,
      scheduleDate: variant.shiftDate,
      staffId: variant.staffId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || !data.data) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data.map(d => Shift.fromApiObject(d).toJSON()), ...rest };
  };
}
