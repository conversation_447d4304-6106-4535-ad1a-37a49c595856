/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendShift, ShiftJSON } from '@manyun/hrm.model.shift';

export type SvcQuery = {
  shiftDate: number;
  staffId: number;
};

export type SvcRespData = ShiftJSON[];

export type RequestRespData = {
  data: BackendShift[];
} | null;

export type ApiQ = {
  scheduleDate: number;
  staffId: number;
};

export type ApiR = ListResponse<BackendShift[]>;
