---
description: 'A fetchMissedPunchesShifts HTTP API service.'
labels: ['service', 'http', fetch-missed-punches-shifts]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchMissedPunchesShifts } from '@manyun/hrm.service.fetch-missed-punches-shifts';

const { error, data } = await fetchMissedPunchesShifts('success');
const { error, data } = await fetchMissedPunchesShifts('error');
```

### Node

```ts
import { fetchMissedPunchesShifts } from '@manyun/hrm.service.fetch-missed-punches-shifts/dist/index.node';

const { data } = await fetchMissedPunchesShifts('success');

try {
  const { data } = await fetchMissedPunchesShifts('error');
} catch (error) {
  // ...
}
```
