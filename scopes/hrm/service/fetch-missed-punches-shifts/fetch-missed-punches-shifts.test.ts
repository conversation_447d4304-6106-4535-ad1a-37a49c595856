/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchMissedPunchesShifts as webService } from './fetch-missed-punches-shifts.browser';

// import { fetchMissedPunchesShifts as nodeService } from './fetch-missed-punches-shifts.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    shiftDate: 1646928000000,
    staffId: 1,
  });
  expect(error).toBe(undefined);
});
