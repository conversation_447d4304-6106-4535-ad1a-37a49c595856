/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { Shift } from '@manyun/hrm.model.shift';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-missed-punches-shifts.type';

const endpoint = '/pm/duty/supplyScheduleDutyList';

/**
 * 查询补卡员工排班班次
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1288)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ shiftDate, staffId }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      scheduleDate: shiftDate,
      staffId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || !data.data) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data.map(d => Shift.fromApiObject(d).toJSON()), ...rest };
  };
}
