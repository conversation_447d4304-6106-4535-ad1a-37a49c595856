/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-24
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQ = {
  type: string;
  staffId: number;
  expireDate?: string;
};

export type Balance = {
  /**可用余额 */
  availableBalance: number;
  /**余额 */
  balance: number;
  unit: 'DAY' | 'HOUR';
};

export type ApiQ = {
  balanceType: string;
  staffId: number;
  /**过期日期 */
  expireDate?: string;
};

export type SvcRespData = Balance | null;

export type ApiR = Response<Balance | null>;
