/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserBalanceByType as webService } from './fetch-user-balance-by-type.browser';
import { fetchUserBalanceByType as nodeService } from './fetch-user-balance-by-type.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ type: 'SICK_SALARY', staffId: 1 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('availableBalance');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ type: 'SICK_SALARY', staffId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ type: 'SICK_SALARY', staffId: 1 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('availableBalance');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ type: 'SICK_SALARY', staffId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
