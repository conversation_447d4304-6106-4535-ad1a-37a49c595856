/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-24
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-balance-by-type';
import type { SvcQ, SvcRespData } from './fetch-user-balance-by-type.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchUserBalanceByType(
  svcQuery: SvcQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
