/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, Balance, SvcQ, SvcRespData } from './fetch-user-balance-by-type.type';

const endpoint = '/dctrans/balance/account/balance/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/124/interface/api/20739)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      balanceType: svcQuery.type,
      staffId: svcQuery.staffId,
      expireDate: svcQuery.expireDate,
    };
    return await request.tryPost<Balance | null, ApiQ>(endpoint, params);
  };
}
