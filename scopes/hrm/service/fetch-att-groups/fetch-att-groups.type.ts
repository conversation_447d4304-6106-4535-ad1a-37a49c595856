/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-20
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type AttGroup = {
  attName: string;
  id: number;
  blockGuid: string;
};

export type SvcRespData = {
  data: AttGroup[];
  total: number;
};

export type RequestRespData = {
  data: AttGroup[] | null;
  total: number;
} | null;

export type ApiQ = {
  attGroupName?: string;
  blockGuids?: string[];
};

export type ApiR = ListResponse<AttGroup[] | null>;
