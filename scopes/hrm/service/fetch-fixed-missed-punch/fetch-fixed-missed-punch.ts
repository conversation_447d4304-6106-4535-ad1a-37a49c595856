/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { ClockIn } from '@manyun/hrm.model.clock-in';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-fixed-missed-punch.type';

const endpoint = '/pm/check/checkDetail';

/**
 * 查询考勤补卡详情
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1168)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQ };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data: data ? ClockIn.fromApiObject(data).toJSON() : null, ...rest };
  };
}
