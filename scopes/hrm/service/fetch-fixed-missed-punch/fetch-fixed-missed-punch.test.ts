/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchFixedMissedPunch as webService } from './fetch-fixed-missed-punch.browser';
import { fetchFixedMissedPunch as nodeService } from './fetch-fixed-missed-punch.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ bizId: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('type');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService('error');

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ bizId: '1' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('type');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ bizId: '0' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
