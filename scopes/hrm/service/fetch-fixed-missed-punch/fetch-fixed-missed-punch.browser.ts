/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-fixed-missed-punch';
import type { SvcQuery, SvcRespData } from './fetch-fixed-missed-punch.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchFixedMissedPunch(svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
