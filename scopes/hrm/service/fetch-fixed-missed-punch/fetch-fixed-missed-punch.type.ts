/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-9
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendClockIn, ClockInJSON } from '@manyun/hrm.model.clock-in';

export type SvcQuery = {
  bizId: string;
};

export type SvcRespData = ClockInJSON | null;

export type RequestRespData = BackendClockIn | null;

export type ApiQ = {
  bizId: string;
};

export type ApiR = Response<RequestRespData>;
