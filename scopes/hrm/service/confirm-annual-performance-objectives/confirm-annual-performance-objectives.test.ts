/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-22
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { confirmAnnualPerformanceObjectives as webService } from './confirm-annual-performance-objectives.browser';
import { confirmAnnualPerformanceObjectives as nodeService } from './confirm-annual-performance-objectives.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    staffId: 1,
    year: '2023',
    evalPosition: 'ac',
    objectives: [],
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    staffId: 1,
    year: '2023',
    evalPosition: 'ac',
    objectives: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    staffId: 1,
    year: '2023',
    evalPosition: 'ac',
    objectives: [],
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    staffId: 1,
    year: '2023',
    evalPosition: 'ac',
    objectives: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
