/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-22
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  AnnualPerformanceObjectiveJSON,
  BackendAnnualPerformanceObjective,
} from '@manyun/hrm.model.annual-performance-objective';

export type SvcQuery = {
  year: string;
  evalPosition: string;
  staffId: number;
  objectives: AnnualPerformanceObjectiveJSON[];
  infoId?: number;
  changeType?: string; //目标变更时变更有效期标识
  idc?: string; //目标所属机房
};

export type ApiBackendAnnualPerformanceObjective = Omit<
  BackendAnnualPerformanceObjective,
  'id' | 'gmtCreate' | 'gmtModified' | 'createUser' | 'modifierUser' | 'evalPositions' | 'resources'
> & {
  evalPositions: string[];
  resources: string[];
};

export type ApiQ = {
  year: string;
  evalPosition: string;
  staffId: number;
  kpis: ApiBackendAnnualPerformanceObjective[];
  infoId?: number;
  changeType?: string; //目标变更时变更有效期标识
  idc?: string; //目标所属机房
};

export type ApiR = WriteResponse;
