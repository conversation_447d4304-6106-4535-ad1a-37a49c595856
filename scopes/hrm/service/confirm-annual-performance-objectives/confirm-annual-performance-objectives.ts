/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-22
 *
 * @packageDocumentation
 */
import cloneDeep from 'lodash.clonedeep';
import omit from 'lodash.omit';

import { AnnualPerformanceObjective } from '@manyun/hrm.model.annual-performance-objective';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiBackendAnnualPerformanceObjective,
  ApiQ,
  SvcQuery,
} from './confirm-annual-performance-objectives.type';

const endpoint = '/pm/pf/year/confirm';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23475)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<number | null>> => {
    const params: ApiQ = {
      staffId: svcQuery.staffId,
      year: svcQuery.year,
      evalPosition: svcQuery.evalPosition,
      kpis: svcQuery.objectives.map(obj => {
        const copy = cloneDeep(AnnualPerformanceObjective.fromJSON(obj).toApiObject());

        const backendObj: ApiBackendAnnualPerformanceObjective = {
          ...omit(copy, ['gmtCreate', 'gmtModified', 'createUser', 'modifierUser']),
          resources: (copy.resources ?? []).map(({ value }) => value),
          evalPositions: copy.evalPositions.map(({ value }) => value),
        };
        return backendObj;
      }),
      infoId: svcQuery.infoId,
      changeType: svcQuery.changeType,
      idc: svcQuery.idc,
    };

    return await request.tryPost<number | null, ApiQ>(endpoint, params);
  };
}
