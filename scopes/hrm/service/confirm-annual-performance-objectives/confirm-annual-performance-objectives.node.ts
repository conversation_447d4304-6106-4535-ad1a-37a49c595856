/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-22
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './confirm-annual-performance-objectives';
import type { SvcQuery } from './confirm-annual-performance-objectives.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function confirmAnnualPerformanceObjectives(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<number | null>> {
  return executor(svcQuery);
}
