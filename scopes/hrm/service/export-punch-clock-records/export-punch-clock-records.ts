/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './export-punch-clock-records.type';

const endpoint = '/pm/export/timely_check_record/list';

/**
 * 导出原始记录
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1368)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      staffId: svcQ?.staffId,
      checkWayList: svcQ?.checkWays,
      isDeleted: svcQ?.checkResult ? Number(svcQ?.checkResult) : undefined,
      endDate: svcQ?.punchTime && svcQ?.punchTime[1],
      startDate: svcQ?.punchTime && svcQ?.punchTime[0],
      blockTagList: svcQ?.blockTags,
      idcTagList: svcQ?.idcTags,
      attGroupId: svcQ?.attGroupId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      params,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
