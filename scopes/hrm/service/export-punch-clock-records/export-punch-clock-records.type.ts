/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import type { PunchWay } from '@manyun/hrm.model.clock-in';

export type SvcQuery = {
  punchTime?: number[];
  staffId?: number;
  checkWays?: PunchWay[];
  checkResult?: string;
  blockTags?: string[];
  idcTags?: string[];
  attGroupId?: number;
};

export type SvcRespData = Blob;
export type RequestRespData = Blob;

export type ApiQ = {
  staffId?: number;
  checkWayList?: PunchWay[];
  endDate?: number;
  startDate?: number;
  isDeleted?: number;
  blockTagList?: string[];
  idcTagList?: string[];
  attGroupId?: number;
};

export type ApiR = Blob;
