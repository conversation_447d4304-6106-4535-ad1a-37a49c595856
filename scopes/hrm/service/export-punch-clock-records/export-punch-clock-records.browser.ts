/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-punch-clock-records';
import type { SvcQuery, SvcRespData } from './export-punch-clock-records.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQ
 * @returns
 */
export function exportPunchClockRecords(
  svcQ?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
