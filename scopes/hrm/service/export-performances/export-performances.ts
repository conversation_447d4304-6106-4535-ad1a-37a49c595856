/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './export-performances.type';

const endpoint = '/pm/pf/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23133)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<Blob>> => {
    const params: ApiQ = {
      pfType: svcQuery.type,
      exportType: svcQuery?.exportType,
      infoSubType: svcQuery?.kpiSubType ?? 'TARGET',
      staffIds: svcQuery?.userIds,
      idcTags: svcQuery?.idcs,
      blockGuids: svcQuery?.blocks,
      position: svcQuery?.job,
      regionCodes: svcQuery?.regionCodes,
      targetStatus: svcQuery?.goalsStatus,
      evalStatus: svcQuery?.evaluationStatus,
      superior: svcQuery?.lineManagerIds,
      secSuperior: svcQuery?.secondLineManagerIds,
      hiredBeginDate: svcQuery?.hiredAtRange?.[0],
      hiredEndDate: svcQuery?.hiredAtRange?.[1],
      targetExpireBeginDate: svcQuery?.goalsDeadlineRange?.[0],
      targetExpireEndDate: svcQuery?.goalsDeadlineRange?.[1],
      evalExpireBeginDate: svcQuery?.evalDeadlineRange?.[0],
      evalExpireEndDate: svcQuery?.evalDeadlineRange?.[1],
      result: svcQuery?.result,
      currentEvalStaffId: svcQuery?.currentHandlerId,
      orderByTargetExpireDateDesc: svcQuery?.orderBy?.goalsDeadline
        ? svcQuery.orderBy.goalsDeadline === 'desc'
        : undefined,
      orderByEvalExpireDateDesc: svcQuery?.orderBy?.evalDeadline
        ? svcQuery.orderBy.evalDeadline === 'desc'
        : undefined,
      orderByStaffIdDesc: svcQuery?.orderBy?.userId
        ? svcQuery.orderBy.userId === 'desc'
        : undefined,
      orderByHiredDateDesc: svcQuery?.orderBy?.hiredAt
        ? svcQuery.orderBy.hiredAt === 'desc'
        : undefined,
      orderByRegionCodeDesc: svcQuery?.orderBy?.region
        ? svcQuery.orderBy.region === 'desc'
        : undefined,
      orderByIdcDesc: svcQuery?.orderBy?.idc ? svcQuery.orderBy.idc === 'desc' : undefined,
      orderBySuperiorIdsDesc: svcQuery?.orderBy?.lineManager
        ? svcQuery.orderBy.lineManager === 'desc'
        : undefined,
      orderByTargetStatusDesc: svcQuery?.orderBy?.goalsStatus
        ? svcQuery.orderBy.goalsStatus === 'desc'
        : undefined,
      orderByEvalStatusDesc: svcQuery?.orderBy?.evaluationStatus
        ? svcQuery.orderBy.evaluationStatus === 'desc'
        : undefined,
      orderByGradeDesc: svcQuery?.orderBy?.grade ? svcQuery.orderBy.grade === 'desc' : undefined,
      orderByFinalGradeDesc: svcQuery?.orderBy?.gradeAvg
        ? svcQuery.orderBy.gradeAvg === 'desc'
        : undefined,
      orderByResultDesc: svcQuery?.orderBy?.result ? svcQuery.orderBy.result === 'desc' : undefined,
      orderByPositionDesc: svcQuery?.orderBy?.job ? svcQuery.orderBy.job === 'desc' : undefined,

      year: svcQuery?.year,
      period: svcQuery?.period,
      evalPosition: svcQuery?.evaluationJob,
      periods: svcQuery?.periods,
    };

    const { error, data, ...rest } = await request.tryPost<Blob, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });

    return { error, data, ...rest };
  };
}
