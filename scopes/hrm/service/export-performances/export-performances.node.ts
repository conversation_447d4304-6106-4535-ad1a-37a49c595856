/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-performances';
import type { SvcQuery } from './export-performances.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function exportPerformances(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<Blob>> {
  return executor(svcQuery);
}
