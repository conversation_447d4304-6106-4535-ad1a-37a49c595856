/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportPerformances as webService } from './export-performances.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    kpiSubType: 'TARGET',
    exportType: 'all',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await webService({
    kpiSubType: 'TARGET',
    exportType: 'all',
  });

  expect(error).toBe(undefined);
});
