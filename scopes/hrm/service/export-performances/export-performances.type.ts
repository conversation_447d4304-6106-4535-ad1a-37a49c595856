/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import type {
  ApiQ as BackendPagedFilterParams,
  SvcQuery as PagedFilterParams,
} from '@manyun/hrm.service.fetch-paged-performances';

export type ExportType = 'all' | 'detail';

export type SvcQuery = Omit<PagedFilterParams, 'page' | 'pageSize'> & {
  exportType?: 'INFO' | 'DETAIL';
};

export type ApiQ = Omit<BackendPagedFilterParams, 'pageNum' | 'pageSize'> & {
  exportType?: 'INFO' | 'DETAIL';
};

export type ApiR = Blob;
