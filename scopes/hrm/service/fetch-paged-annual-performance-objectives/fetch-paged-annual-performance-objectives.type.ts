/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendAnnualPerformanceObjective,
  BackendAnnualPerformanceObjectiveSubType,
  BackendAnnualPerformanceObjectiveType,
  BackendAnnualPerformanceObjectiveWay,
} from '@manyun/hrm.model.annual-performance-objective';

export type SvcQuery = {
  page: number;
  pageSize: number;
  name?: string;
  resourceCodes?: string[];
  performancePosition?: string;
  way?: BackendAnnualPerformanceObjectiveWay;
  type?: BackendAnnualPerformanceObjectiveType;
  types?: BackendAnnualPerformanceObjectiveType[];
  subType?: BackendAnnualPerformanceObjectiveSubType;
  createUserId?: number;
  createTimeRange?: [number, number];
  modifiedTimeRange?: [number, number];
};

export type ApiAnnualPerformanceObjective = Omit<
  BackendAnnualPerformanceObjective,
  'resources' | 'createUser' | 'modifierUser'
> & {
  creatorId: number;
  modifierId: number;
};

export type PagedApiAnnualPerformanceObjective = {
  data: ApiAnnualPerformanceObjective[];
  total: number;
};

export type ApiQ = {
  pageSize: number;
  pageNum: number;
  name?: string;
  resources?: string[];
  evalPosition?: string;
  kpiWay?: BackendAnnualPerformanceObjectiveWay;
  type?: BackendAnnualPerformanceObjectiveType;
  types?: BackendAnnualPerformanceObjectiveType[];
  subType?: BackendAnnualPerformanceObjectiveSubType;
  creatorId?: number;
  createStartTime?: number;
  createEndTime?: number;
  modifiedStartTime?: number;
  modifiedEndTime?: number;
};

export type ApiR = ListResponse<ApiAnnualPerformanceObjective[] | null>;
