/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-annual-performance-objectives';
import type {
  PagedApiAnnualPerformanceObjective,
  SvcQuery,
} from './fetch-paged-annual-performance-objectives.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchPagedAnnualPerformanceObjectives(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<PagedApiAnnualPerformanceObjective>> {
  return executor(svcQuery);
}
