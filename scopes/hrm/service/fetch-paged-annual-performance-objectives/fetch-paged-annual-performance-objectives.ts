/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  PagedApiAnnualPerformanceObjective,
  SvcQuery,
} from './fetch-paged-annual-performance-objectives.type';

const endpoint = '/pm/pf/kpiPage';

/**
 * @see [Doc](http://***********:13000/project/216/interface/api/23322)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery
  ): Promise<EnhancedAxiosResponse<PagedApiAnnualPerformanceObjective>> => {
    const params: ApiQ = {
      pageSize: svcQuery.pageSize,
      pageNum: svcQuery.page,
      name: svcQuery.name,
      resources: svcQuery.resourceCodes,
      evalPosition: svcQuery.performancePosition,
      kpiWay: svcQuery.way,
      type: svcQuery.type,
      types: svcQuery.types,
      subType: svcQuery.subType,
      creatorId: svcQuery.createUserId,
      createStartTime: svcQuery.createTimeRange ? svcQuery.createTimeRange[0] : undefined,
      createEndTime: svcQuery.createTimeRange ? svcQuery.createTimeRange[1] : undefined,
      modifiedStartTime: svcQuery.modifiedTimeRange ? svcQuery.modifiedTimeRange[0] : undefined,
      modifiedEndTime: svcQuery.modifiedTimeRange ? svcQuery.modifiedTimeRange[1] : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<
      PagedApiAnnualPerformanceObjective | null,
      ApiQ
    >(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return { error, data: { data: data.data, total: data.total }, ...rest };
  };
}
