---
description: 'A updateDailyGradeLockInfos HTTP API service.'
labels: ['service', 'http']
---

绩效评分管控配置更新

## Usage

### Browser

```ts
import { updateDailyGradeLockInfos } from '@manyun/hrm.service.update-daily-grade-lock-infos';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { UpdateDailyGradeLockInfosService } from '@manyun/hrm.service.update-daily-grade-lock-infos/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = UpdateDailyGradeLockInfosService.from(nodeRequest);
```
