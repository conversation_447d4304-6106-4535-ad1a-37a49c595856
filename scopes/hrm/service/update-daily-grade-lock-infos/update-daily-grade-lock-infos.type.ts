/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-30
 *
 * @packageDocumentation
 */
import type { WriteBackendResponse } from '@glpdev/symphony.services.request';

export type Infos = {
  /**
   * 年度
   */
  year: string;
  /**
   * 周期：Q1，Q2，Q3，Q4，YEAR
   */
  period: string;
  /**
   * 是否锁定
   */
  locked: boolean;
};

export type ApiArgs = {
  /**
   * 评分管控列表
   */
  kpiLockConfigs: Infos[];
};

export type ApiResponse = WriteBackendResponse;
