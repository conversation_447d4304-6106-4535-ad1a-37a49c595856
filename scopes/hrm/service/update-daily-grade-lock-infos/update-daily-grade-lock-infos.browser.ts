/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-30
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './update-daily-grade-lock-infos.js';
import type { ApiArgs } from './update-daily-grade-lock-infos.type.js';

/**
 * @param args
 * @returns
 */
export function updateDailyGradeLockInfos(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
