/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './update-daily-grade-lock-infos.type.js';

const endpoint = '/pm/pf/kpiLockConfigUpdate';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/216/interface/api/30332)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
