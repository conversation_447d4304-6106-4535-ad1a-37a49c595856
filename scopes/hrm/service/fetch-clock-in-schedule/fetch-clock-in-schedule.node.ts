/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-clock-in-schedule';
import type { SvcQuery, SvcRespData } from './fetch-clock-in-schedule.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchClockInSchedule(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
