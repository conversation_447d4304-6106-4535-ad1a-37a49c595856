/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  SvcBlockQuery,
  SvcBlockRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-clock-in-schedule.type';

const endpoint = '/pm/schedule/checkSchedules';
const blockQueryApi = '/dccm/block/query';

/**
 * @see [用户可打卡排班查询](http://172.16.0.17:13000/project/78/interface/api/17936)
 * @param request
 * @returns
 *
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: SvcQuery = { currentTime: variant.currentTime };

    const { error, data, ...rest } = await request.tryPost<SvcRespData, SvcQuery>(endpoint, params);

    return { error, data, ...rest };
  };
}

/**
 * @see [楼栋查](http://172.16.0.17:13000/project/140/interface/api/13888)
 * @param request
 * @returns
 *
 */
export function getBlockQueryApiExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcBlockQuery): Promise<EnhancedAxiosResponse<SvcBlockRespData>> => {
    const params: SvcBlockQuery = {
      tag: variant.tag,
      idcTag: variant.idcTag,
    };

    const { error, data, ...rest } = await request.tryPost<SvcBlockRespData, SvcBlockQuery>(
      blockQueryApi,
      params
    );

    return { error, data, ...rest };
  };
}
