/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getBlockQueryApiExecutor, getExecutor } from './fetch-clock-in-schedule';
import type {
  SvcBlockQuery,
  SvcBlockRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-clock-in-schedule.type';

const executor = getExecutor(webRequest);
const blockQueryApiExecutor = getBlockQueryApiExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchClockInSchedule(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}

/**
 * 获取楼栋详情
 * @param variant
 * @returns
 */
export function fetchClockBlockInfo(
  variant: Svc<PERSON>lockQuery
): Promise<EnhancedAxiosResponse<SvcBlockRespData>> {
  return blockQueryApiExecutor(variant);
}
