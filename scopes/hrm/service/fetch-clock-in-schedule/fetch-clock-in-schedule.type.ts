/**
 * <AUTHOR> Name <<EMAIL>>
 * @since 2022-5-12
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ClockCheckWay = 'EXTRA' | 'SUPPLY' | 'NORMAL';

export type SvcQuery = {
  currentTime: number;
};

export type SvcBlockQuery = {
  tag: string;
  idcTag: string;
};

export type SvcRespData = {
  data: UserClockInScheduleData[];
  total: number;
};

export type SvcBlockRespData = {
  data: IBlockData[];
  total: number;
};

export type RequestRespData = {
  data: UserClockInScheduleData[] | null;
  total: number;
} | null;

export type ApiQ = {
  variant?: 'success' | 'error';
};

export type ApiR = ListResponse<UserClockInScheduleData[] | null>;

export type IOnDutyCheckRange = {
  /** 开始时间 */
  startTime: string;
  /** 开始时间是否为第二天 */
  startIsNextDay: boolean;
  /** 结束时间 */
  endTime: string;
  /** 结束时间是否为第二天 */
  endIsNextDay: boolean;
  /** 时间范围 */
  timeRangeStr: string;
  chTimeRangeStr: string;
};

export type IOffDutyCheckRange = {
  /** 开始时间 */
  startTime: string;
  /** 开始时间是否为第二天 */
  startIsNextDay: boolean;
  /** 结束时间 */
  endTime: string;
  /** 结束时间是否为第二天 */
  endIsNextDay: boolean;
  /** 时间范围 */
  timeRangeStr: string;
  chTimeRangeStr: string;
};

/** 班次详情 */
export interface IUserDuty {
  /**班次id */
  id: number;
  /** 班次名 */
  dutyName: string;
  /** 上班时间 */
  onDutyTime: string;
  /** 下班时间 */
  offDutyTime: string;
  /** 下班是否为第二天 */
  offIsNextDay: boolean;
  /** 班次属性 */
  dutyProperties: {
    /** 是否开启了上班打卡范围 */
    enableOnDutyCheckRange: boolean;
    /** 上班打卡范围 */
    onDutyCheckRange: IOnDutyCheckRange;
    /** 是否开启了下班打卡范围 */
    enableOffDutyCheckRange: boolean;
    /** 下班打卡范围 */
    offDutyCheckRange: IOffDutyCheckRange;
    /** 是否允许休息 */
    allowRest: boolean;
    restMinutes: number;
    restRange: null;
    enableOffset: boolean;
    onDutyOffsetMinutes: null;
    offDutyOffsetMinutes: null;
    enableBuffer: boolean;
    onDutyBufferMinutes: null;
    offDutyBufferMinutes: null;
    enableCompensate: boolean;
    compensateHours: null;
    allowContinuousWork: boolean;
    notAllowContinuousTime: null;
  };
  gmtModified: null;
  modifierId: null;
  modifierName: null;
}

export interface IBlockData {
  area: number;
  coordinate: string;
  idcName: string;
  idcTag: string;
  tag: string;
}

// 当前时刻可进行打卡的排班
export interface UserClockInScheduleData {
  bizId: string;
  /** 员工排班id */
  id: number;
  /** 员工id */
  staffId: number;
  /** 员工姓名 */
  staffName: string;
  /** 排班唯一值 */
  unicode: string;
  /** 班组排班id */
  groupScheduleId: number;
  groupScheduleUnicode: null;
  /** 机房标签 */
  idcTag: string;
  /** 楼栋标签 */
  blockTag: string;
  /** 班组id */
  dutyGroupId: number;
  /** 班组 */
  dutyGroupName: string;
  /** 班制id */
  shiftsId: number;
  /** 排班日期 */
  scheduleDate: number;
  duty: Partial<IUserDuty>;
  /** 考勤组 */
  attGroup: {
    id: number;
    /** 考勤组 */
    attName: string;
    /** 机房标签 */
    idcTag: string;
    /** 楼栋 */
    blockGuid: string;
    /** 打卡方式 */
    checkChannels: string[];
  };
  /** 打卡经纬度 */
  blockCoordinate: IBlockCoordinate;
  // 检查打卡结果: 打卡之后才有
  checkResult: ICheckResult;
  /** 实际需上班时间 */
  scheduleStartTime: number;
  /** 实际需下班时间 */
  scheduleEndTime: number;
  /**  打卡方式(EXTRA 加班,SUPPLY 补卡,NORMAL 普通")*/
  checkWay?: ClockCheckWay;
}
// 检查打卡结果
export interface ICheckResult {
  /** 考勤id */
  id: number;
  /** 员工id */
  staffId: number;
  /** 排班id */
  staffScheduleId: number;
  /** 排班日期（时间戳） */
  scheduleDate: number;
  /** 实际上班打卡时间（时间戳）,可用于是否打卡判断 */
  onDutyTime: number;
  /** 实际下班打卡时间（时间戳） */
  offDutyTime: number;
  /** 工作时长 */
  workTime: number;
  /** 是否上班迟到	 */
  isLateOn: boolean;
  /** 是否下班早退	 */
  isEarlyOff: boolean;
  /** 是否缺上班卡	 */
  isMissOnCard: boolean;
  /** 是否缺下班卡 */
  isMissOffCard: boolean;
  /** 上班卡打卡结果：：/**
   * * 未打卡  UN_CHECK /**  * 正常  NORMAL /**  * 迟到  LATE_ON /**  * 早退  EARLY_OFF /**  * 缺卡  MISS_CARD */
  onCheckResult: string;
  /* * 未打卡  UN_CHECK /**  * 正常  NORMAL /**  * 迟到  LATE_ON /**  * 早退  EARLY_OFF /**  * 缺卡  MISS_CARD */
  offCheckResult: string;
}

/** 打卡经纬度 */
export interface IBlockCoordinate {
  /** 打卡位置 */
  blockGuid: string;
  /** 打卡有效范围 */
  area: number;
  /** 经纬度 */
  coordinate: string;
  /** 经度 */
  longitude: string;
  /** 纬度 */
  latitude: string;
}
