---
description: 'A fetchClockInSchedule HTTP API service.'
labels: ['service', 'http', fetch-clock-in-schedule]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchClockInSchedule } from '@hrm/service.fetch-clock-in-schedule';

const { error, data } = await fetchClockInSchedule('success');
const { error, data } = await fetchClockInSchedule('error');
```

### Node

```ts
import { fetchClockInSchedule } from '@hrm/service.fetch-clock-in-schedule/dist/index.node';

const { data } = await fetchClockInSchedule('success');

try {
  const { data } = await fetchClockInSchedule('error');
} catch (error) {
  // ...
}
```
