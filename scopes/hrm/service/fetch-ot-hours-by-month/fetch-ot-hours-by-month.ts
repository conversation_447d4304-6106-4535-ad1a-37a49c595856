/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-ot-hours-by-month.type';

const endpoint = '/pm/overtimeWork/totalTimeByCurrentMonth';

/**
 * 本月累计加班时间查询
 * @see [Doc](https://manyun.yuque.com/ewe5b3/se3fdm/en45v6#Qr3TD)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyStaffId: variant.apply,
      endTime: variant.endTime,
      startTime: variant.startTime,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data: data ?? 0, ...rest };
  };
}
