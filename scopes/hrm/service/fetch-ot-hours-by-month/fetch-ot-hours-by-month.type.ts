/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  apply: number;
  startTime: number;
  endTime: number;
};

export type SvcRespData = number;

export type RequestRespData = number | null;

export type ApiQ = {
  applyStaffId: number;
  startTime?: number;
  endTime?: number;
};

export type ApiR = Response<number | null>;
