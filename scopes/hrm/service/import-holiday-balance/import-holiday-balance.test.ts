/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { importHolidayBalance as webService } from './import-holiday-balance.browser';
import { importHolidayBalance as nodeService } from './import-holiday-balance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService(new FormData());

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService(new FormData());

  expect(error).toBe(undefined);
});
