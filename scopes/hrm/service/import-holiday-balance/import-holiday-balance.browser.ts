/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './import-holiday-balance';
import type { ApiResponse } from './import-holiday-balance.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function importHolidayBalance(
  svcQ: FormData
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(svcQ);
}
