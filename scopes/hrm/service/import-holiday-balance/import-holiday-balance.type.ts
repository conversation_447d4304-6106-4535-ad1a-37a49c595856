/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-11
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type UserHolidayBalanceInfos = {
  /**
   * 员工工号
   */
  jobNumber?: string | null;
  /**
   * 员工名称
   */
  userName?: string | null;
  /**
   * 年份
   */
  year?: string | null;
  /**
   * 法定假期
   */
  statutoryBalance?: number | null;
  /**
   * 公司假期
   */
  companyBalance?: number | null;
  /**
   * 结转假期
   */
  carryOverBalance?: number | null;
  /**
   * 已用年假假期余额
   */
  totalUsedBalance?: number | null;
  /**
   * 可用年假余额
   */
  totalAvailableBalance?: number | null;
  /**
   * 已用法定年假余额
   */
  statutoryUsedBalance?: number | null;
  /**
   * 已用结转年假余额
   */
  carryOverUsedBalance?: number | null;
  /**
   * 已用公司年假余额
   */
  companyUsedBalance?: number | null;
  /**
   * 可用法定年假余额
   */
  statutoryAvailableBalance?: number | null;
  /**
   * 可用结转年假余额
   */
  carryOverAvailableBalance?: number | null;
  /**
   * 可用公司年假余额
   */
  companyAvailableBalance?: number | null;
  /**
   * 享受带薪病假
   */
  sickSalaryBalance?: number | null;
  /**
   * 已用带薪病假
   */
  sickSalaryUsedBalance?: number | null;
  /**
   * 可用带薪病假
   */
  sickSalaryAvailableBalance?: number | null;
  /**
   * 员工id
   */
  staffId?: string | null;
};

export type ApiResponseData = {
  /**
   * 错误条数
   */
  errorCheckDtoList?: {
    errDto?: UserHolidayBalanceInfos;
    errMessage?: Partial<Record<keyof UserHolidayBalanceInfos, string>>;
    totalErrMessage?: string | null;
  }[];
  /**
   * 正确条数
   */
  correctDtoList?: UserHolidayBalanceInfos[] | null;
  fileInfoList?: BackendMcUploadFile[];
};

export type ApiResponse = Response<ApiResponseData | null>;
