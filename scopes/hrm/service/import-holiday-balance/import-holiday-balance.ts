/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-1-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiResponse } from './import-holiday-balance.type';

const endpoint = '/dctrans/balance/balance/import';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/124/interface/api/26400)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: FormData): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'] | null, FormData>(endpoint, svcQ);
  };
}
