/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './valid-can-update-schedule-rule';
import type { ApiArgs } from './valid-can-update-schedule-rule.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function validCanUpdateScheduleRule(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
