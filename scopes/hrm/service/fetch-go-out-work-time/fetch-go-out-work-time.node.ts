/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-go-out-work-time';
import type { SvcQuery, SvcRespData } from './fetch-go-out-work-time.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchGoOutWorkTime(svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
