/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchGoOutWorkTime as webService } from './fetch-go-out-work-time.browser';
import { fetchGoOutWorkTime as nodeService } from './fetch-go-out-work-time.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    startTime: 1677809891000,
    endTime: 1677896291000,
    applyStaffId: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(8);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    startTime: 0,
    endTime: 0,
    applyStaffId: 1,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    startTime: 1677809891000,
    endTime: 1677896291000,
    applyStaffId: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(8);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    startTime: 0,
    endTime: 0,
    applyStaffId: 1,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
