/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-go-out-work-time.type';

const endpoint = '/pm/outwork/outworkTime';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/20352)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = svcQ;

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
