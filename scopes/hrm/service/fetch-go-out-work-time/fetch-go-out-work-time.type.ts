/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-6
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  startTime: number;
  endTime: number;
  applyStaffId: number;
};

export type SvcRespData = number | null;

export type ApiQ = {
  startTime: number;
  endTime: number;
  applyStaffId: number;
};

export type RequestRespData = number | null;
export type ApiR = Response<RequestRespData>;
