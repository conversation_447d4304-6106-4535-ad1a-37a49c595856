/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-24
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendShift, ShiftJSON } from '@manyun/hrm.model.shift';

export type SvcQuery = {
  name?: string;
  page: number;
  pageSize: number;
};

export type SvcRespData = {
  data: ShiftJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendShift[] | null;
  total: number;
} | null;

export type ApiQ = {
  dutyName?: string;
  pageSize: number;
  pageNum: number;
};

export type ApiR = ListResponse<BackendShift[] | null>;
