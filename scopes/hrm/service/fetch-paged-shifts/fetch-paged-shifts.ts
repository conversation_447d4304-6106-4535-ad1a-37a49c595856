/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-24
 *
 * @packageDocumentation
 */
import { Shift } from '@manyun/hrm.model.shift';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-paged-shifts.type';

const endpoint = '/pm/duty/page';

/**
 * 班次分页列表
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1272)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum: svcQuery.page,
      pageSize: svcQuery.pageSize,
      dutyName: svcQuery.name,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => Shift.fromApiObject(d).toJSON()), total: data.total },
      ...rest,
    };
  };
}
