/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import moment from 'moment';

import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-ot-request.type';

const endpoint = '/pm/overtimeWork/add';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/9368)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyStaffId: variant.apply,
      startTime: moment(variant.startTime).second(0).millisecond(0).valueOf(),
      endTime: moment(variant.endTime).second(0).millisecond(0).valueOf(),
      totalTime: variant.totalTime,
      idcTag: variant.idc,
      blockGuids: variant.blocks,
      bizId: variant.bizId,
      overtimeType: variant.type,
      reason: variant.reason,
      files: variant.attachments?.map(obj => McUploadFile.toApiObject(obj)),
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
