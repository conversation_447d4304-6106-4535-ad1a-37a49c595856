/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export enum OT_TYPE {
  OVERTIME_PRE = 'OVERTIME_PRE',
  OVERTIME_CONFIRM = 'OVERTIME_CONFIRM',
}

export const OT_TYPE_MAPPER: Record<string, string> = {
  [OT_TYPE.OVERTIME_PRE]: '加班申请',
  [OT_TYPE.OVERTIME_CONFIRM]: '加班确认',
};

export type SvcQuery = {
  apply: number;
  startTime: number;
  endTime: number;
  totalTime: number;
  idc: string;
  blocks: string[];
  bizId?: string;
  type: OT_TYPE;
  reason: string;
  attachments?: McUploadFile[];
};

export type SvcRespData = string;

export type RequestRespData = string;

export type ApiQ = {
  applyStaffId: number;
  startTime: number;
  endTime: number;
  totalTime: number;
  idcTag: string;
  blockGuids: string[];
  bizId?: string; //加班业务id，如果在原业务上确认需要
  overtimeType: OT_TYPE;
  reason: string;
  files?: BackendMcUploadFile[];
};

export type ApiR = Response<string | null>;
