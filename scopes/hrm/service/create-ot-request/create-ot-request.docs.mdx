---
description: 'A createOtRequest HTTP API service.'
labels: ['service', 'http', create-ot-request]
---

## 概述

TODO

## 使用

### Browser

```ts
import { createOtRequest } from '@manyun/hrm.service.create-ot-request';

const { error, data } = await createOtRequest('success');
const { error, data } = await createOtRequest('error');
```

### Node

```ts
import { createOtRequest } from '@manyun/hrm.service.create-ot-request/dist/index.node';

const { data } = await createOtRequest('success');

try {
  const { data } = await createOtRequest('error');
} catch (error) {
  // ...
}
```
