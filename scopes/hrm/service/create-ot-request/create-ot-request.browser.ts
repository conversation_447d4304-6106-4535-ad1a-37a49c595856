/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-ot-request';
import type { SvcQuery, SvcRespData } from './create-ot-request.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createOtRequest(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
