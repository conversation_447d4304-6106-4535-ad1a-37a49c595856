/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './export-staff-list.type.js';

const endpoint = '/pm/userFiles/Export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30692)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return request.tryPost<boolean, ApiArgs>(endpoint, args, {
      responseType: 'blob',
    });
  };
}
