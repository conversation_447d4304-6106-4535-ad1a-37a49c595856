/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 兼岗记录id
   */
  recordId: number;
};

export type PartTimeJobLogs = {
  /**
   * 记录id
   */
  targetId: number;
  /**
   * 修改类型：ADD,MODIFY
   */
  modifyType: string;
  /**
   * 修改时间
   */
  gmtModified: number;
  /**
   * 添加时间
   */
  gmtCreate: number;
  /**
   * 修改人id
   */
  modifierId: number;
  /**
   * 开始时间
   */
  startTime: number;
  /**
   * 结束时间
   */
  endTime: number;
  /**
   * 兼岗楼栋
   */
  partTimeBlocks: string[];
};

export type ApiResponse = ListBackendResponse<PartTimeJobLogs[]>;
