---
description: 'A fetchPartTimeJobsLogs HTTP API service.'
labels: ['service', 'http']
---

兼岗操作记录查询

## Usage

### Browser

```ts
import { fetchPartTimeJobsLogs } from '@manyun/hrm.service.fetch-part-time-jobs-logs';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchPartTimeJobsLogsService } from '@manyun/hrm.service.fetch-part-time-jobs-logs/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchPartTimeJobsLogsService.from(nodeRequest);
```
