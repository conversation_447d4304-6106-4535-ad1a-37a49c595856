/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './submit-performance-evaluation';
import type { SvcQuery } from './submit-performance-evaluation.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function submitSubmitPerformanceEvaluation(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
