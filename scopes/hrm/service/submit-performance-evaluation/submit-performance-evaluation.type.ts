/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendEvaluationContent,
  BackendEvaluationStepStatus,
  BackendEvaluationStepType,
  BackendPerformanceResult,
} from '@manyun/hrm.model.performance';
import type { BackendGrade } from '@manyun/hrm.model.performance-objective';

export type SvcQuery = {
  /**
   * 绩效Id
   */
  id: number;
  /**
   * 审批状态
   */
  status: BackendEvaluationStepStatus;
  /**
   * 绩效结果 试用期使用枚举，年度使用number
   */
  result?: BackendPerformanceResult | number;
  /**
   * 审批备注
   */
  content?: Partial<BackendEvaluationContent>;
  /**
   * 绩效对应目标项评分
   */
  goalGrades?: {
    id: number;
    grade: BackendGrade;
  }[];
  /**
   * 当前所处阶段
   */
  currentStep: BackendEvaluationStepType;
  interviewed?: boolean;
  needAttention?: boolean;
  attentionContent?: string;
};

export type ApiQ = {
  infoId: number;
  status: BackendEvaluationStepStatus;
  result?: BackendPerformanceResult | number;
  content?: Partial<BackendEvaluationContent>;
  sectionGrade?: {
    sectionId: number;
    grade: BackendGrade;
  }[];
  pfPhase: BackendEvaluationStepType;
  /**
   * 年度绩效 直线经理是否面谈
   */
  interviewed?: boolean;
  needAttention?: boolean;
  attentionContent?: string;
};

export type ApiR = WriteResponse;
