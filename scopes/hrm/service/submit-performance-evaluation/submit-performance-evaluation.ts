/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './submit-performance-evaluation.type';

const endpoint = '/pm/pf/evaluation';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23754)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = {
      infoId: svcQuery.id,
      pfPhase: svcQuery.currentStep,
      status: svcQuery.status,
      result: svcQuery.result,
      content: {
        summary: svcQuery.content?.summary?.trim(),
        improve: svcQuery.content?.improve?.trim(),
        infoSummary: svcQuery.content?.infoSummary?.trim(),
        infoImprove: svcQuery.content?.infoImprove?.trim(),
        reason: svcQuery.content?.reason?.trim(),
      },
      sectionGrade: svcQuery.goalGrades?.map(goal => ({
        sectionId: goal.id,
        grade: goal.grade,
      })),
      interviewed: svcQuery.interviewed,
      needAttention: svcQuery.needAttention,
      attentionContent: svcQuery.attentionContent?.trim(),
    };

    return request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
