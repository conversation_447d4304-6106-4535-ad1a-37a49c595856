---
description: 'A cancleOt HTTP API service.'
labels: ['service', 'http', cancle-ot]
---

## 概述

TODO

## 使用

### Browser

```ts
import { cancleOt } from '@manyun/hrm.service.cancle-ot';

const { error, data } = await cancleOt('success');
const { error, data } = await cancleOt('error');
```

### Node

```ts
import { cancleOt } from '@manyun/hrm.service.cancle-ot/dist/index.node';

const { data } = await cancleOt('success');

try {
  const { data } = await cancleOt('error');
} catch (error) {
  // ...
}
```
