/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './cancle-ot';
import type { SvcQuery, SvcRespData } from './cancle-ot.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function cancleOt(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
