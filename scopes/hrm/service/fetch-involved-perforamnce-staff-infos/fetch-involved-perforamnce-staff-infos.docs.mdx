---
description: 'A fetchInvolvedPerforamnceStaffInfos HTTP API service.'
labels: ['service', 'http']
---

部门用户查询

## Usage

### Browser

```ts
import { fetchInvolvedPerforamnceStaffInfos } from '@manyun/hrm.service.fetch-involved-perforamnce-staff-infos';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchInvolvedPerforamnceStaffInfosService } from '@manyun/hrm.service.fetch-involved-perforamnce-staff-infos/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchInvolvedPerforamnceStaffInfosService.from(nodeRequest);
```
