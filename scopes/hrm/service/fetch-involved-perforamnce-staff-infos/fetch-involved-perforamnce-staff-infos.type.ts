/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';
import type { BackendUser } from '@manyun/auth-hub.model.user';

export type ApiArgs = {
  pageNum: number;
  pageSize: number;
  /**
   * 用户id
   */
  userId?: number | null;
  /**
   * 机房列表
   */
  idcList?: string | null;
  /**
   * 管理岗标签：元数据 JOB_LABEL
   */
  jobLabels?: string[] | null;
  /**
   * 楼栋guid
   */
  blockGuids?: string[];
  /**
   * 是否在职
   */
  enable?: boolean;
  /**是否按照工号排序**/
  orderByJobNoDesc?: boolean;
};

export type ApiResponse = ListBackendResponse<BackendUser[]>;
