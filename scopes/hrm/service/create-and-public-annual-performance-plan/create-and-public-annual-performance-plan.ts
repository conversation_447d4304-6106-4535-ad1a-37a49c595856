/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './create-and-public-annual-performance-plan.type';

const endpoint = '/pm/pf/createPublishPlan';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23385)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = {
      name: svcQuery.name,
      year: svcQuery.year,
      resources: svcQuery.resourcesScope,
      evalPositions: svcQuery.positionScope,
      splitRule: svcQuery.splitRule,
      tasks: svcQuery.subConfigs.map(config => ({
        title: config.title,
        period: config.period,
        startTime: config.evalStartTimeRange[0],
        endTime: config.evalStartTimeRange[1],
        ruleStartTime: config.natureStartTimeRange[0],
        ruleEndTime: config.natureStartTimeRange[1],
      })),
      needNotEvalHiredDate: svcQuery.notEvalHiredDateLaterThanDate,
      evalSetExpireNotifyDays: svcQuery.beforeSubTaskDeadlineNotifyDays,
      resultSetTime: svcQuery.canSetResultDate,
    };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
