/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-and-public-annual-performance-plan';
import type { SvcQuery } from './create-and-public-annual-performance-plan.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createAndPublicAnnualPerformancePlan(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
