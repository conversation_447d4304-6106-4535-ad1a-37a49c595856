/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  AnnualPerformancePlanJSON,
  BackendAnnualPerformancePlan,
} from '@manyun/hrm.model.annual-performance-plan';

export type SvcQuery = Pick<
  AnnualPerformancePlanJSON,
  | 'year'
  | 'name'
  | 'resourcesScope'
  | 'positionScope'
  | 'splitRule'
  | 'subConfigs'
  | 'notEvalHiredDateLaterThanDate'
  | 'beforeSubTaskDeadlineNotifyDays'
  | 'canSetResultDate'
>;

export type ApiQ = Pick<
  BackendAnnualPerformancePlan,
  | 'year'
  | 'name'
  | 'resources'
  | 'evalPositions'
  | 'splitRule'
  | 'tasks'
  | 'needNotEvalHiredDate'
  | 'evalSetExpireNotifyDays'
  | 'resultSetTime'
>;

export type ApiR = WriteResponse;
