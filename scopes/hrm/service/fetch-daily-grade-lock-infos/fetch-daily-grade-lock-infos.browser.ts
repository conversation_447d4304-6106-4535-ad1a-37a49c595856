/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-30
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-daily-grade-lock-infos.js';
import type { ApiArgs, ApiResponse } from './fetch-daily-grade-lock-infos.type.js';

/**
 * @param args
 * @returns
 */
export function fetchDailyGradeLockInfos(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
