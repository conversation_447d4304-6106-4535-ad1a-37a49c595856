---
description: 'A fetchDailyGradeLockInfos HTTP API service.'
labels: ['service', 'http']
---

绩效评分管控配置查询

## Usage

### Browser

```ts
import { fetchDailyGradeLockInfos } from '@manyun/hrm.service.fetch-daily-grade-lock-infos';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchDailyGradeLockInfosService } from '@manyun/hrm.service.fetch-daily-grade-lock-infos/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchDailyGradeLockInfosService.from(nodeRequest);
```
