/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-4-30
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 年份
   */
  year: string;
};

export type DailyGradeLockInfos = {
  /**
   * 年份
   */
  year: string;
  /**
   * 周期
   */
  period: string;
  /**
   * 是否锁定
   */
  locked: boolean;
};

export type ApiResponse = ListBackendResponse<DailyGradeLockInfos[]>;
