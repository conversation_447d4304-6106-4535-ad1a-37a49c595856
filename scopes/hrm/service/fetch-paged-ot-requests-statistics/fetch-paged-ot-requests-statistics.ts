/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-29
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-paged-ot-requests-statistics.type';
import { StatisticOTRequest } from './fetch-paged-ot-requests-statistics.type';

const endpoint = '/pm/overtimeWork/overtimeParamPassPage';

/**
 * 分页查询已通过的加班记录
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/9504)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum: variant.page,
      pageSize: variant.pageSize,
      bizId: variant.bizId,
      applyStaffId: variant.apply,
      holiday: StatisticOTRequest.toApiDayType(variant.dayType),
      startTime: variant.timeRange ? variant.timeRange[0] : undefined,
      endTime: variant.timeRange ? variant.timeRange[1] : undefined,
      activeStartTime: variant.activeTimeRange ? variant.activeTimeRange[0] : undefined,
      activeEndTime: variant.activeTimeRange ? variant.activeTimeRange[1] : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => StatisticOTRequest.fromApiObject(d)), total: data.total },
      ...rest,
    };
  };
}
