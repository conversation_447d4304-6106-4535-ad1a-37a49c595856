---
description: 'A fetchPagedOtRequestsStatistics HTTP API service.'
labels: ['service', 'http', fetch-paged-ot-requests-statistics]
---

## 概述

分页查询已通过的加班记录

## 使用

### Browser

```ts
import { fetchPagedOtRequestsStatistics } from '@manyun/hrm.service.fetch-paged-ot-requests-statistics';

const { error, data } = await fetchPagedOtRequestsStatistics('success');
const { error, data } = await fetchPagedOtRequestsStatistics('error');
```

### Node

```ts
import { fetchPagedOtRequestsStatistics } from '@manyun/hrm.service.fetch-paged-ot-requests-statistics/dist/index.node';

const { data } = await fetchPagedOtRequestsStatistics('success');

try {
  const { data } = await fetchPagedOtRequestsStatistics('error');
} catch (error) {
  // ...
}
```
