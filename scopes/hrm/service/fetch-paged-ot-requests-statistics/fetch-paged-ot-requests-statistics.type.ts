/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  page: number;
  pageSize: number;
  bizId?: string;
  apply?: number;
  timeRange?: [number, number];
  activeTimeRange?: [number, number];
  dayType?: DAY_TYPE;
};

export enum DAY_TYPE {
  WORK_DAY = 'WORK_DAY',
  // WEEK_DAY = 'WEEK_DAY',
  HOLIDAY = 'HOLIDAY',
}

export const DAY_TYPE_MAPPER: Record<string, string> = {
  [DAY_TYPE.WORK_DAY]: '非休息日',
  [DAY_TYPE.HOLIDAY]: '休息日',
};

export type BackendStatisticOTRequest = {
  applyStaffId: number;
  bizId: string;
  startTime: number;
  endTime: number;
  totalTime: number;
  gmtModified: number;
  holiday: boolean;
  onCheckTime: number | null;
  offCheckTime: number | null;
  overtimeConfirmId: string;
};

export class StatisticOTRequest {
  constructor(
    public id: string,
    public apply: number,
    public startTime: number,
    public endTime: number,
    public totalTime: number,
    public gmtModified: number | null,
    public dayType: DAY_TYPE,
    public onCheckTime: number | null,
    public offCheckTime: number | null,
    public overtimeConfirmId: string
  ) {}

  static fromApiObject(object: BackendStatisticOTRequest) {
    return new StatisticOTRequest(
      object.bizId,
      object.applyStaffId,
      object.startTime,
      object.endTime,
      object.totalTime,
      object.gmtModified,
      StatisticOTRequest.fromApiDayType(object),
      object.onCheckTime,
      object.offCheckTime,
      object.overtimeConfirmId
    );
  }

  static fromApiDayType(backendUser: BackendStatisticOTRequest): DAY_TYPE {
    if (backendUser.holiday) {
      return DAY_TYPE.HOLIDAY;
    }
    return DAY_TYPE.WORK_DAY;
  }

  static toApiDayType(dayType: DAY_TYPE | undefined): boolean | undefined {
    if (dayType === DAY_TYPE.HOLIDAY) {
      return true;
    }

    if (dayType === undefined) {
      return undefined;
    }
    return false;
  }
}

export type SvcRespData = {
  data: StatisticOTRequest[];
  total: number;
};

export type RequestRespData = {
  data: BackendStatisticOTRequest[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  bizId?: string;
  startTime?: number;
  endTime?: number;
  activeStartTime?: number;
  activeEndTime?: number;
  applyStaffId?: number;
  holiday?: boolean;
};

export type ApiR = ListResponse<BackendStatisticOTRequest[] | null>;
