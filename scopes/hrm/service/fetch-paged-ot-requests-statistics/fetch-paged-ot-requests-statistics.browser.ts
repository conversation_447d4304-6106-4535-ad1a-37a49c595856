/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-29
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-ot-requests-statistics';
import type { SvcQuery, SvcRespData } from './fetch-paged-ot-requests-statistics.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPagedOtRequestsStatistics(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
