/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './whether-shift-is-be-used.type';

const endpoint = '/pm/duty/dutyIsBeUsed';

/**
 * 查询班次是否被排班使用
 * @see [Doc](YAPI https://manyun.yuque.com/ewe5b3/lgi8mf/we1v98)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQ };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
