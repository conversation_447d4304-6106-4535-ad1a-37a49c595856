/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './whether-shift-is-be-used';
import type { SvcQuery, SvcRespData } from './whether-shift-is-be-used.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQ
 * @returns
 */
export function whetherShiftIsBeUsed(svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
