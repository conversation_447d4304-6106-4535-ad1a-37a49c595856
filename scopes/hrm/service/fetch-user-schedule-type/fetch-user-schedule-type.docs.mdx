---
description: 'A fetchUserScheduleType HTTP API service.'
labels: ['service', 'http', fetch-user-schedule-type]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchUserScheduleType } from '@manyun/hrm.service.fetch-user-schedule-type';

const { error, data } = await fetchUserScheduleType('success');
const { error, data } = await fetchUserScheduleType('error');
```

### Node

```ts
import { fetchUserScheduleType } from '@manyun/hrm.service.fetch-user-schedule-type/dist/index.node';

const { data } = await fetchUserScheduleType('success');

try {
  const { data } = await fetchUserScheduleType('error');
} catch (error) {
  // ...
}
```
