/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  staffId: number;
};

export type SCHEDULE_TYPE = 'PERIOD' | 'WEEK'; //PERIOD("排班制"),WEEK("固定班制")

export type SvcRespData = SCHEDULE_TYPE | null;

export type RequestRespData = SCHEDULE_TYPE | null;

export type ApiQ = SvcQuery;

export type ApiR = Response<RequestRespData>;
