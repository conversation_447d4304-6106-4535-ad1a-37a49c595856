/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './batch-approve-performances';
import type { ApiQ } from './batch-approve-performances.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function batchApprovePerformances(svcQuery: ApiQ): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
