/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ } from './batch-approve-performances.type';

const endpoint = '/pm/pf/batchEvaluation';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/24438)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiQ>(endpoint, svcQuery);
  };
}
