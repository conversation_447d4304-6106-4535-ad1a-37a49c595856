/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import dayjs from 'dayjs';

import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  Schedule,
  SvcQuery,
  SvcRespData,
} from './fetch-user-shift-schedule.type';

const endpoint = '/pm/schedule/attBizSchedules';

/**
 * 查询员工排班
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/20208)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = svcQ;

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: (data.data ?? []).map(backendSchedule => {
          let schedule: Schedule = {
            ...backendSchedule,
            scheduleWorkMinute: dayjs(backendSchedule.scheduleEndTime).diff(
              backendSchedule.scheduleStartTime,
              'minute'
            ),
          };
          /**排班休息时间段 */
          if (backendSchedule.allowRest && backendSchedule.restRange) {
            const startTimeStr = `${
              backendSchedule.restRange.startIsNextDay
                ? dayjs(backendSchedule.scheduleDate).add(1, 'day').format('YYYY-MM-DD')
                : dayjs(backendSchedule.scheduleDate).format('YYYY-MM-DD')
            }  ${backendSchedule.restRange.startTime}`;
            const endTimeStr = `${
              backendSchedule.restRange.endIsNextDay
                ? dayjs(backendSchedule.scheduleDate).add(1, 'day').format('YYYY-MM-DD')
                : dayjs(backendSchedule.scheduleDate).format('YYYY-MM-DD')
            } ${backendSchedule.restRange.endTime}`;

            schedule.restStartTime = dayjs(startTimeStr).valueOf();
            schedule.restEndTime = dayjs(endTimeStr).valueOf();
            schedule.scheduleRestMinute = dayjs(schedule.restEndTime).diff(
              dayjs(schedule.restStartTime),
              'minute'
            );
          }
          return schedule;
        }),
        total: data.total,
      },
      ...rest,
    };
  };
}
