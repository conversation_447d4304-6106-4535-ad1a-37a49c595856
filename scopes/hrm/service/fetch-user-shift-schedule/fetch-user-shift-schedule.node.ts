/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-shift-schedule';
import type { SvcQuery, SvcRespData } from './fetch-user-shift-schedule.type';

const executor = getExecutor(nodeRequest);

/**
 * @param scvQ
 * @returns
 */
export function fetchUserShiftSchedule(
  scvQ: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(scvQ);
}
