/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-shift-schedule';
import type { SvcQuery, SvcRespData } from './fetch-user-shift-schedule.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchUserShiftSchedule(
  svcQ: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
