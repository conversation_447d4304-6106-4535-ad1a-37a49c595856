/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-6
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendSchedule = {
  attGroupId?: number;
  attGroupName?: string;
  blockTag?: string;
  dutyGroupId?: number;
  dutyGroupName?: string;
  dutyId?: number;
  dutyName?: string;
  groupScheduleId?: number;
  groupScheduleUnicode?: string;
  id?: number;
  idcTag?: string;
  scheduleDate?: number;
  scheduleEndTime?: number /**排班结束时间 */;
  scheduleScene?: string;
  scheduleStartTime?: number /**排班开始时间 */;
  shiftsId?: number;
  staffId?: number;
  staffName?: string;
  timeInterval?: string;
  unicode?: string;
  groupScheduleStartTime?: number /**考勤组考勤开始时间 */;
  groupScheduleEndTime?: number /**考勤组考勤结束时间 */;
  allowRest?: boolean /**是否允许休息 */;
  restRange?: {
    startTime: string;
    endTime: string;
    startIsNextDay: boolean /**开始时间是否为次日 */;
    endIsNextDay: boolean /**结束时间是否为次日 */;
  } /** 休息范围*/;
  attBizId: string;
  attBizType: string;
};

export type Schedule = BackendSchedule & {
  scheduleWorkMinute: number /**排班工作时长,单位：分钟 */;
  restStartTime?: number /**休息开始时间 */;
  restEndTime?: number /***休息结束时间 */;
  scheduleRestMinute?: number /**休息时长， 单位分钟 */;
};

export type SvcQuery = {
  bizType: 'SCHEDULE' | 'OVERTIME';
  bizId: string;
};

export type SvcRespData = {
  data: Schedule[];
  total: number;
};

export type RequestRespData = {
  data: BackendSchedule[] | null;
  total: number;
} | null;

export type ApiQ = {
  bizType: 'SCHEDULE' | 'OVERTIME';
  bizId: string;
};

export type ApiR = ListResponse<BackendSchedule[] | null>;
