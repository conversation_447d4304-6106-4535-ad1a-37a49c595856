/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-shift';
import type { SvcQuery, SvcRespData } from './update-shift.type';

const executor = getExecutor(webRequest);

/**
 * @param updateD
 * @returns
 */
export function updateShift(updateD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(updateD);
}
