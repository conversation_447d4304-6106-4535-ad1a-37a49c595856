/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-24
 *
 * @packageDocumentation
 */
import { Shift } from '@manyun/hrm.model.shift';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './update-shift.type';

const endpoint = '/pm/duty/update';

/**
 * 更新班次
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1296)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (updateD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = Shift.fromJSON(updateD).toApiObject();

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
