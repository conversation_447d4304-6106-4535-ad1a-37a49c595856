/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-shift-replacement-users';
import type { SvcQuery, SvcRespData } from './fetch-shift-replacement-users.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchShiftReplacementUsers(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
