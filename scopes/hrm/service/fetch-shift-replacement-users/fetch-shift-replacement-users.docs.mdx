---
description: 'A fetchShiftReplacementUsers HTTP API service.'
labels: ['service', 'http', fetch-shift-replacement-users]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchShiftReplacementUsers } from '@manyun/hrm.service.fetch-shift-replacement-users';

const { error, data } = await fetchShiftReplacementUsers('success');
const { error, data } = await fetchShiftReplacementUsers('error');
```

### Node

```ts
import { fetchShiftReplacementUsers } from '@manyun/hrm.service.fetch-shift-replacement-users/dist/index.node';

const { data } = await fetchShiftReplacementUsers('success');

try {
  const { data } = await fetchShiftReplacementUsers('error');
} catch (error) {
  // ...
}
```
