/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-shift-replacement-users.type';

const endpoint = '/pm/check/replaceUserList';

/**
 * 查询请假顶班人/换班人
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1224)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      alterType: variant.type,
      dutyId: variant.shiftId,
      scheduleDate: variant.shiftDate,
      staffId: variant.staffId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || !data.data) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return {
      error,
      data: data.data.map(d => ({
        id: d.id,
        login: d.loginName,
        name: d.userName,
        title: d.position,
      })),
      ...rest,
    };
  };
}
