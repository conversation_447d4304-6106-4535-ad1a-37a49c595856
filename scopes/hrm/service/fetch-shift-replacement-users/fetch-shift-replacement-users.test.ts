/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { AlterType } from '@manyun/hrm.service.fetch-shifts-for-leave-request';
import { useRemoteMock } from '@manyun/service.request';

import { fetchShiftReplacementUsers as webService } from './fetch-shift-replacement-users.browser';

// import { fetchShiftReplacementUsers as nodeService } from './fetch-shift-replacement-users.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    type: AlterType.Exchange,
    shiftDate: 20202,
    staffId: 1,
    shiftId: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

// test('[web] should resolve error response', async () => {
//   const { error, data } = await webService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[node] should resolve success response', async () => {
//   const { error, data } = await nodeService('success');

//   expect(error).toBe(undefined);
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[node] should resolve error response', async () => {
//   const { error, data } = await nodeService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });
