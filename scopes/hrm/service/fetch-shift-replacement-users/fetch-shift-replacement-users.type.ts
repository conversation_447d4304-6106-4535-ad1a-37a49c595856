/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { BackendUser, User } from '@manyun/auth-hub.model.user';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  type: 'LEAVE' | 'EXCHANGE' | 'REST';
  shiftId: number;
  shiftDate: number;
  staffId: number;
};

export type ReplacementUser = Pick<User, 'id' | 'name' | 'login' | 'title'>;

export type SvcRespData = ReplacementUser[];

export type RequestRespData = {
  data: Pick<BackendUser, 'id' | 'userName' | 'loginName' | 'position'>[];
} | null;

export type ApiQ = {
  alterType: 'LEAVE' | 'EXCHANGE' | 'REST';
  dutyId: number;
  scheduleDate: number;
  staffId: number;
};

export type ApiR = ListResponse<Pick<BackendUser, 'id' | 'userName' | 'loginName'>[] | null>;
