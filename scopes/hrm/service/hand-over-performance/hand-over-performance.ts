/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './hand-over-performance.type';

const endpoint = '/pm/pf/handover';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/24429)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = {
      infoIds: svcQuery.infoIds,
      handoverStaffId: svcQuery.transfereeId,
      pfInfoSubType: svcQuery.pfSubType,
    };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
