/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { handOverPerformance as webService } from './hand-over-performance.browser';
import { handOverPerformance as nodeService } from './hand-over-performance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    infoIds: [1],
    transfereeId: 1,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ infoIds: [1], transfereeId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ infoIds: [1], transfereeId: 1 });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ infoIds: [1], transfereeId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
