/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './hand-over-performance';
import type { SvcQuery } from './hand-over-performance.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function handOverPerformance(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
