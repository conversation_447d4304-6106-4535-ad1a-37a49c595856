/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-12
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPerformanceSubType } from '@manyun/hrm.model.performance';

export type SvcQuery = {
  infoIds: number[];
  transfereeId: number;
  pfSubType: BackendPerformanceSubType;
};

export type ApiQ = {
  infoIds: number[];
  handoverStaffId: number;
  pfInfoSubType: BackendPerformanceSubType;
};

export type ApiR = WriteResponse;
