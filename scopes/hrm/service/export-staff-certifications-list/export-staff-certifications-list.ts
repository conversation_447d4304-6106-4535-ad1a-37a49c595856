/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './export-staff-certifications-list.type.js';

const endpoint = '/pm/cert/position/statistic/user/export';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30860)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> => {
    return request.tryPost<Blob, ApiArgs>(endpoint, args, {
      responseType: 'blob',
    });
  };
}
