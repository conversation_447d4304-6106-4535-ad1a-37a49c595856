/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './export-staff-certifications-list.js';
import type { ApiArgs } from './export-staff-certifications-list.type.js';

/**
 * @param args
 * @returns
 */
export function exportStaffCertificationsList(args: ApiArgs): Promise<EnhancedAxiosResponse<Blob>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
