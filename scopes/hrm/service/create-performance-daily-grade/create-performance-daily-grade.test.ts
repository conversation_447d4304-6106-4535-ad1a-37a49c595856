/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createPerformanceDailyGrade as webService } from './create-performance-daily-grade.browser';
import { createPerformanceDailyGrade as nodeService } from './create-performance-daily-grade.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');

  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
