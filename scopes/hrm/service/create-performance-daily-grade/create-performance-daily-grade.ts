/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './create-performance-daily-grade.type';

const endpoint = '/pm/pf/createKpiRecord';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/216/interface/api/23340)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      type: svcQuery.type,
      subType: svcQuery.subType,
      metrics: svcQuery.measurements,
      grade: svcQuery.grade,
      occurTime: svcQuery.occurTime,
      content: svcQuery.gradeDesc,
      staffId: svcQuery.staffId,
      staffName: svcQuery.staffName,
      name: svcQuery.name,
      kpiId: svcQuery.relatedObjectiveId,
      gradeCriteria: svcQuery.gradeCriteria,
      files: svcQuery.attachments?.map(file => ({
        ...McUploadFile.fromJSON(file).toApiObject(),
        targetType: 'KPI_RECORD',
      })),
    };

    return request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
