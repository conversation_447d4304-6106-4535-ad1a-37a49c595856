/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-performance-daily-grade';
import type { SvcQuery, SvcRespData } from './create-performance-daily-grade.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function createPerformanceDailyGrade(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
