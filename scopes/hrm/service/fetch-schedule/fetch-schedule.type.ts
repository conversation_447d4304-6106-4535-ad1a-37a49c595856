/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-28
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type YourDataModel = {
  attGroupId: number;
  attGroupName: string;
  blockTag: string;
  duty: Duty[];
  dutyGroup: DutyGroup[];
  id: number;
  idcTag: string;
  scheduleDate: number;
  scheduleEndTime: number;
  scheduleStaffInfos: ScheduleStaffInfos[];
  scheduleStartTime: number;
  scheduleUnicode: string;
  timeInterval: string;
};

export type ScheduleStaffInfos = {
  id: number;
  locked: boolean;
  loginName: string;
  scheduleScene:
    | 'LEAVE_IN'
    | 'LEAVE_OUT'
    | 'LEAVE_OUT_PROCESS'
    | 'EXCHANGE'
    | 'EXCHANGE_PROCESS'
    | 'DUTY_GROUP_IN'
    | 'INIT_SCHEDULE'
    | 'ADD_STAFF_SCHEDULE'
    | 'DUTY_GROUP_OUT'
    | 'DELETE_OLD_SCHEDULE'
    | 'DELETE_STAFF_SCHEDULE'
    | 'DELETE_DUTY_GROUP'
    | 'REMOVE_DUTY_GROUP'
    | 'DELETE_ATT_GROUP';

  userName: string;
};

export type DutyGroup = {
  attGroupId: number;
  blockTag: string;
  groupName: string;
  id: number;
  idcTag: string;
  staffList: StaffList[];
};

export type StaffList = {
  id: number;
  loginName: string;
  userName: string;
};
export type Duty = {
  dutyName: string;
  dutyProperties: DutyProperties[];
  id: number;
  offDutyTime: string;
  offIsNextDay: boolean;
  onDutyTime: string;
};

export type DutyProperties = {
  allowContinuousWork: boolean;
  allowRest: boolean;
  compensateHours: string[];
  enableBuffer: boolean;
  enableCompensate: boolean;
  enableOffDutyCheckRange: boolean;
  enableOffset: boolean;
  enableOnDutyCheckRange: boolean;
  notAllowContinuousTime: number;
  offDutyBufferMinutes: number;
  offDutyCheckRange: CheckRange;
  offDutyOffsetMinutes: number;
  onDutyBufferMinutes: number;
  onDutyCheckRange: CheckRange;
  onDutyOffsetMinutes: number;
  restRange: CheckRange;
};

export type CheckRange = {
  chTimeRangeStr: string;
  endIsNextDay: boolean;
  endTime: string;
  startIsNextDay: boolean;
  startTime: string;
  timeRangeStr: string;
};
export type SvcRespData = {
  data: YourDataModel[];
  total: number;
};

export type RequestRespData = {
  data: YourDataModel[] | null;
  total: number;
} | null;

export type ApiQ = {
  attGroupId?: number;
  beginDate?: string;
  blockTag?: string;
  dutyGroupId?: number;
  endDate?: string;
  idcTag?: string;
  tenantId?: string;
  id?: number;
};

export type ApiR = ListResponse<YourDataModel[] | null>;
