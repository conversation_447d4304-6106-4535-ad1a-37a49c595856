---
description: 'A fetchSchedule HTTP API service.'
labels: ['service', 'http', fetch-schedule]
---

## 概述

查询班组排班

## 使用

### Browser

```ts
import { fetchSchedule } from '@hrm/service.fetch-schedule';

const { error, data } = await fetchSchedule({ dutyGroupId: 1 });
const { error, data } = await fetchSchedule({ dutyGroupId: 2 });
```

### Node

```ts
import { fetchSchedule } from '@hrm/service.fetch-schedule/dist/index.node';

const { data } = await fetchSchedule({ dutyGroupId: 1 });

try {
  const { data } = await fetchSchedule({ dutyGroupId: 2 });
} catch (error) {
  // ...
}
```
