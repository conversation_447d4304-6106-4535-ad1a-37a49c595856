/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-21
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

/**
 * 待转交事项类型
 * - `SCHEDULE` 排班
 * - `DAILY_TASK` 日常工单
 */
export type TransferItemType = 'SCHEDULE' | 'DAILY_TASK';

export type SvcRespData = TransferItemType[];

export type RequestRespData = {
  data: SvcRespData | null;
};

export type ApiQ = {
  employeeId: number;
};

export type ApiR = Response<SvcRespData | null>;
