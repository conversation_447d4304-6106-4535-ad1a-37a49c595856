/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-21
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-processing-events';
import type { ApiQ, SvcRespData } from './fetch-user-processing-events.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchUserProcessingEvents(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
