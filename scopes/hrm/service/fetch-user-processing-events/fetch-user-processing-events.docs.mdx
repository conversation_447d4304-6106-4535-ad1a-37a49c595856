---
description: 'A fetchUserProcessingEvents HTTP API service.'
labels: ['service', 'http', fetch-user-processing-events]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchUserProcessingEvents } from '@manyun/dc-brain.service.fetch-user-processing-events';

const { error, data } = await fetchUserProcessingEvents('success');
const { error, data } = await fetchUserProcessingEvents('error');
```

### Node

```ts
import { fetchUserProcessingEvents } from '@manyun/dc-brain.service.fetch-user-processing-events/dist/index.node';

const { data } = await fetchUserProcessingEvents('success');

try {
  const { data } = await fetchUserProcessingEvents('error');
} catch(error) {
  // ...
}
```
