/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-4-21
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-user-processing-events.type';

const endpoint = '/pm/user/leaves/matters/qry';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/16480)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }
    return { error, data: data.data, ...rest };
  };
}
