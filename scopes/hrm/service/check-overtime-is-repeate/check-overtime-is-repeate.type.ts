/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-20
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ApiArgs = {
  /**
   * 申请人id
   */
  applyStaffId: number;
  blockGuids: string[];
  /**
   * 结束时间
   */
  endTime: number;
  /**
   * 加班类型：OVERTIME_PRE("加班申请"), OVERTIME_CONFIRM("加班确认");
   */
  overtimeType: string;
  /**
   * 开始时间
   */
  startTime: number;
  /**
   * 总时长（小时）
   */
  totalTime: number;
  /**
   * 加班业务id，如果在原业务上确认需要
   */
  bizId?: string | null;
};

export type ApiResponse = Response<boolean>;
