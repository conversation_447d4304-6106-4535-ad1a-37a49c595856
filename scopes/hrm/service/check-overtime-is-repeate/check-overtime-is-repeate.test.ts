/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { checkOvertimeIsRepeate as webService } from './check-overtime-is-repeate.browser';
import { checkOvertimeIsRepeate as nodeService } from './check-overtime-is-repeate.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    applyStaffId: 0,
    blockGuids: [],
    endTime: 0,
    overtimeType: '',
    startTime: 0,
    totalTime: 0,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    applyStaffId: 1,
    blockGuids: [],
    endTime: 0,
    overtimeType: '',
    startTime: 0,
    totalTime: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    applyStaffId: 0,
    blockGuids: [],
    endTime: 0,
    overtimeType: '',
    startTime: 0,
    totalTime: 0,
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    applyStaffId: 1,
    blockGuids: [],
    endTime: 0,
    overtimeType: '',
    startTime: 0,
    totalTime: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
