/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-20
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './check-overtime-is-repeate.type';

const endpoint = '/pm/overtimeWork/valid';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/26736)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'] | null, ApiArgs>(endpoint, args);
  };
}
