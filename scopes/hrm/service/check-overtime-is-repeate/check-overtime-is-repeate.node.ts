/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-20
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './check-overtime-is-repeate';
import type { ApiArgs, ApiResponse } from './check-overtime-is-repeate.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function checkOvertimeIsRepeate(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(args);
}
