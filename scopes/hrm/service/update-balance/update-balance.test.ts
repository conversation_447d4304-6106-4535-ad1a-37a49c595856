/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateBalance as webService } from './update-balance.browser';
import { updateBalance as nodeService } from './update-balance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    staffId: 1,
    formId: '12',
    changeBalance: 1,
    balanceType: 'PAID_LEAVE',
    memo: 'sss',
    unit: 'DAY',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    staffId: 0,
    formId: '2',
    changeBalance: 3,
    balanceType: 'PAID_LEAVE',
    memo: '345',
    unit: 'DAY',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    staffId: 1,
    formId: '12',
    changeBalance: 1,
    balanceType: 'PAID_LEAVE',
    memo: 'sss',
    unit: 'DAY',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    staffId: 0,
    formId: '2',
    changeBalance: 3,
    balanceType: 'PAID_LEAVE',
    memo: '345',
    unit: 'DAY',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
