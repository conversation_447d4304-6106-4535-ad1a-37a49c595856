/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-15
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './update-balance.type';

const endpoint = '/dctrans/balance/manual_update';

/**
 * @see [Doc](http://172.16.0.17:13000/project/124/interface/api/20406)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (updateV: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      balanceType: updateV.balanceType,
      unit: updateV.unit,
      formId: updateV.formId,
      staffId: updateV.staffId,
      changeBalance: updateV.changeBalance,
      expireDate: updateV.expireDate,
      memo: updateV.memo,
      balance: updateV.originalBalance,
      fileInfoList: updateV.fileInfoList?.map(file => McUploadFile.toApiObject(file)),
    };

    return request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
