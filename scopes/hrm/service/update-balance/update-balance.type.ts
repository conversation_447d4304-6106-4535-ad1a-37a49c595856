/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-15
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  staffId: number;
  formId: string;
  changeBalance: number /**变更金额 :正数 :+ 负数: - */;
  balanceType: string;
  expireDate?: string /**过期日期：调休假必传 */;
  memo: string /**原因 */;
  fileInfoList?: McUploadFile[] /**附件*/;
  unit: 'DAY' | 'HOUR';
  originalBalance: number;
};

export type SvcRespData = boolean;

export type RequestRespData = boolean;

export type ApiQ = {
  staffId: number;
  formId: string;
  changeBalance: number /**变更金额 :正数 :+ 负数: - */;
  balanceType: string;
  expireDate?: string /**过期日期：调休假必传 */;
  memo: string /**原因 */;
  fileInfoList?: BackendMcUploadFile[] /**附件 */;
  unit: 'DAY' | 'HOUR';
  /**原始余额：用于校验 */
  balance: number;
};

export type ApiR = WriteResponse;
