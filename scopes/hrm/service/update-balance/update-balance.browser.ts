/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-3-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-balance';
import type { SvcQuery, SvcRespData } from './update-balance.type';

const executor = getExecutor(webRequest);

/**
 * @param updateV
 * @returns
 */
export function updateBalance(updateV: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(updateV);
}
