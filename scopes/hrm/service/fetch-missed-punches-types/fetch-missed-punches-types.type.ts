/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  staffId: number;
  scheduleDate: number;
  dutyId: number;
};

export type SvcRespData = string[];

export type RequestRespData = {
  data: string[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<string[] | null>;
