/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-missed-punches-types';
import type { SvcQuery, SvcRespData } from './fetch-missed-punches-types.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchMissedPunchesTypes(
  svcQ: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
