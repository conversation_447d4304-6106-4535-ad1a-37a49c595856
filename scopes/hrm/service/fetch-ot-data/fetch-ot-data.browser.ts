/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-ot-data';
import type { SvcQuery, SvcRespData } from './fetch-ot-data.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchOtData(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
