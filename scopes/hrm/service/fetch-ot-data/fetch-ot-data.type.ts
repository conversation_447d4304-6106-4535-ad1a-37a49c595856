/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export enum OVER_TIME_STATUS {
  PROCESS = 'PROCESS',
  UN_CONFIRM = 'UN_CONFIRM',
  PASS = 'PASS',
  REFUSE = 'REFUSE',
  CLOSE = 'CLOSE',
}

export const OVER_TIME_STATUS_MAPPER: Record<string, string> = {
  [OVER_TIME_STATUS.PROCESS]: '审批中',
  [OVER_TIME_STATUS.UN_CONFIRM]: '待确认',
  [OVER_TIME_STATUS.PASS]: '已通过',
  [OVER_TIME_STATUS.REFUSE]: '已拒绝',
  [OVER_TIME_STATUS.CLOSE]: '已关闭',
};

export type SvcQuery = {
  apply: number;
  page: number;
  pageSize: number;
  status: string[];
};

export type BackendOvertimeRecord = {
  title: string;
  applyStaffId: number;
  status: OVER_TIME_STATUS;
  bizId: string;
  idcTag: string;
  blockGuids: string[];
  startTime: number;
  endTime: number;
  totalTime: number;
  creatorId: number;
  gmtCreate: number;
  overtimePreId?: number;
  overtimeConfirmId?: number;
};

export type OvertimeRecord = {
  title: string;
  apply: number;
  status: OVER_TIME_STATUS;
  bizId: string;
  idc: string;
  blocks: string[];
  startTime: number;
  endTime: number;
  totalTime: number;
  creatorId: number;
  preId?: number;
  confirId?: number;
  gmtCreate?: number;
};

export type SvcRespData = {
  data: OvertimeRecord[];
  total: number;
};

export type RequestRespData = {
  data: BackendOvertimeRecord[] | null;
  total: number;
} | null;

export type ApiQ = {
  applyStaffId: number;
  pageSize: number;
  pageNum: number;
  statusList: string[];
};

export type ApiR = ListResponse<BackendOvertimeRecord[]>;
