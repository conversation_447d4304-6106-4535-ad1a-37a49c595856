---
description: 'A fetchOtData HTTP API service.'
labels: ['service', 'http', fetch-ot-data]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchOtData } from '@manyun/hrm.service.fetch-ot-data';

const { error, data } = await fetchOtData('success');
const { error, data } = await fetchOtData('error');
```

### Node

```ts
import { fetchOtData } from '@manyun/hrm.service.fetch-ot-data/dist/index.node';

const { data } = await fetchOtData('success');

try {
  const { data } = await fetchOtData('error');
} catch (error) {
  // ...
}
```
