/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-ot-data.type';

const endpoint = '/pm/overtimeWork/page';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/9392)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyStaffId: variant.apply,
      pageNum: variant.page,
      pageSize: variant.pageSize,
      statusList: variant.status,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(d => ({
          title: d.title,
          apply: d.applyStaffId,
          status: d.status,
          bizId: d.bizId,
          idc: d.idcTag,
          blocks: d.blockGuids,
          startTime: d.startTime,
          endTime: d.endTime,
          totalTime: d.totalTime,
          creatorId: d.creatorId,
          gmtCreate: d.gmtCreate,
        })),
        total: data.total,
      },
      ...rest,
    };
  };
}
