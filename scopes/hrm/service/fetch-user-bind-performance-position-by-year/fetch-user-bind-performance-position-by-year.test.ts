/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-30
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserBindPerformancePositionByYear as webService } from './fetch-user-bind-performance-position-by-year.browser';
import { fetchUserBindPerformancePositionByYear as nodeService } from './fetch-user-bind-performance-position-by-year.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ userId: 1, year: '2023' });

  expect(error).toBe(undefined);
  expect(data).toBe('code');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ userId: 1, year: '2023' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ userId: 1, year: '2023' });

  expect(error).toBe(undefined);
  expect(data).toBe('code');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ userId: 1, year: '2023' });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
