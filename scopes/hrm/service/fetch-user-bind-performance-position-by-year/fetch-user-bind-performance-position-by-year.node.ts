/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-30
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-bind-performance-position-by-year';
import type { SvcQuery } from './fetch-user-bind-performance-position-by-year.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchUserBindPerformancePositionByYear(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<string | null>> {
  return executor(svcQuery);
}
