/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-30
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './fetch-user-bind-performance-position-by-year.type';

const endpoint = '/pm/pf/year/staffEvalPosition';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23484)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<string | null>> => {
    const params: ApiQ = {
      staffId: svcQuery.userId,
      year: svcQuery.year,
    };

    return await request.tryPost<string | null, ApiQ>(endpoint, params);
  };
}
