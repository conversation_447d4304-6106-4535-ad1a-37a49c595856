/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateUserCertificate as webService } from './update-user-certificate.browser';
import { updateUserCertificate as nodeService } from './update-user-certificate.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    id: 0,
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    id: 0,
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    id: 0,
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    id: 0,
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
