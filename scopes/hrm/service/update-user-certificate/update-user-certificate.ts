/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './update-user-certificate.type';

const endpoint = '/pm/cert/update';

/**
 * 证书修改
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18861)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, variant);

    return { error, data, ...rest };
  };
}
