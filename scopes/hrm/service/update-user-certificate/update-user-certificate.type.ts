/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  id: number;
  userId: number;
  certName: string;
  effectStartDate: string;
  effectEndDate: string;
  checkDate?: string;
  fileId: number;
  fileType: string;
};

// @FIXME renaming me

export type SvcRespData = string;

export type RequestRespData = string | null;

export type ApiQ = {
  id: number;
  userId: number;
  certName: string;
  effectStartDate: string;
  effectEndDate: string;
  checkDate?: string;
  fileId: number;
  fileType: string;
};

export type ApiR = Response<string | null>;
