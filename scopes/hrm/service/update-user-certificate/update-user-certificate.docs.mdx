---
description: 'A updateUserCertificate HTTP API service.'
labels: ['service', 'http']
---

## 概述

修改用户证书信息

## 使用

### Browser

```ts
import { updateUserCertificate } from '@manyun/hrm.service.update-user-certificate';

const { error, data } = await updateUserCertificate('success');
const { error, data } = await updateUserCertificate('error');
```

### Node

```ts
import { updateUserCertificate } from '@manyun/hrm.service.update-user-certificate/dist/index.node';

const { data } = await updateUserCertificate('success');
const { error, data } = await updateUserCertificate('error');
```
