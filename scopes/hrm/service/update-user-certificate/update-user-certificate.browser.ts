/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-user-certificate';
import type { SvcQuery, SvcRespData } from './update-user-certificate.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function updateUserCertificate(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
