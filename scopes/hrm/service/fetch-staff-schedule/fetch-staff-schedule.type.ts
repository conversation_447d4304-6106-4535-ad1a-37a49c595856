/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  idcTag: string;
  tenantId?: string;
  currentTime?: number;
};

export type StaffScheduleModel = {
  blockGuids: string[];
  dutyName: string;
  groupName: string;
  inDuty: string;
  staffId: number;
  format: number;
  staffName: string;
};

export type SvcRespData = {
  data: StaffScheduleModel[];
  total: number;
};

export type RequestRespData = {
  data: StaffScheduleModel[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<StaffScheduleModel[] | null>;
