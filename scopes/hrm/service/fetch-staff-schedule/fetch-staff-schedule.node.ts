/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-staff-schedule';
import type { SvcQuery, SvcRespData } from './fetch-staff-schedule.type';

const executor = getExecutor(nodeRequest);

/**
 * @returns
 */
export function fetchStaffSchedule(
  staffVariant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(staffVariant);
}
