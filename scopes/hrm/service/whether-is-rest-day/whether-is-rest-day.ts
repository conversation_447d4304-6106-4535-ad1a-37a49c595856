/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-28
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs } from './whether-is-rest-day.type';

const endpoint = '/pm/overtimeWork/restDayValid';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/26769)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
