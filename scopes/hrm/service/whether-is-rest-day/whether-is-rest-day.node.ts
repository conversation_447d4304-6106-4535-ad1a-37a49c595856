/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-28
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './whether-is-rest-day';
import type { ApiArgs } from './whether-is-rest-day.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function whetherIsRestDay(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
