/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-28
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { whetherIsRestDay as webService } from './whether-is-rest-day.browser';
import { whetherIsRestDay as nodeService } from './whether-is-rest-day.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    startTime: 1709101421085,
    endTime: 1709101421085,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ startTime: 1709101479099, endTime: 1709101479099 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ startTime: 1709101421085, endTime: 1709101421085 });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ startTime: 1709101479099, endTime: 1709101479099 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');

  expect(data).toBe(false);
});
