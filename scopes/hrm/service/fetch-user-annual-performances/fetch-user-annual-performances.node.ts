/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-annual-performances';
import type { ApiSimpleAnnualPerformance, SvcQuery } from './fetch-user-annual-performances.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchUserAnnualPerformances(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<ApiSimpleAnnualPerformance[]>> {
  return executor(svcQuery);
}
