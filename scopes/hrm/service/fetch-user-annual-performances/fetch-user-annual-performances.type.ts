/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPerformancePeriod } from '@manyun/hrm.model.annual-performance-plan';
import type {
  BackendPerformanceEvaluationStatus,
  BackendPerformanceObjectiveStatus,
  BackendPerformanceSubType,
} from '@manyun/hrm.model.performance';

export type SvcQuery = {
  year: string;
  performancePosition: string;
  userId: number;
  subType?: BackendPerformanceSubType;
};

export type ApiSimpleAnnualPerformance = {
  id?: number;
  title: string;
  status: string;
  type: BackendPerformanceSubType;
  period: BackendPerformancePeriod;
  targetStatus: BackendPerformanceObjectiveStatus;
  evalStatus: BackendPerformanceEvaluationStatus;
  superiors: number[];
  startTime: number;
  endTime: number;
};

export type ApiQ = {
  year: string;
  evalPosition: string;
  staffId: number;
  infoSubType?: BackendPerformanceSubType;
};

export type ApiR = ListResponse<ApiSimpleAnnualPerformance[] | null>;
