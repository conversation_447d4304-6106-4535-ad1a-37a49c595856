/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  ApiSimpleAnnualPerformance,
  SvcQuery,
} from './fetch-user-annual-performances.type';

const endpoint = '/pm/pf/year/selfPfList';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23439)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery
  ): Promise<EnhancedAxiosResponse<ApiSimpleAnnualPerformance[]>> => {
    const params: ApiQ = {
      year: svcQuery.year,
      staffId: svcQuery.userId,
      evalPosition: svcQuery.performancePosition,
      infoSubType: svcQuery.subType,
    };

    const { error, data, ...rest } = await request.tryPost<
      { data: ApiSimpleAnnualPerformance[]; total: number } | null,
      ApiQ
    >(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data, ...rest };
  };
}
