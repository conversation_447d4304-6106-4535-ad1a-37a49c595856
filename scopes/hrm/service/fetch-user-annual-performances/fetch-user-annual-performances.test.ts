/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserAnnualPerformances as webService } from './fetch-user-annual-performances.browser';
import { fetchUserAnnualPerformances as nodeService } from './fetch-user-annual-performances.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    userId: 1,
    performancePosition: '工程师',
    year: '2023',
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    userId: 0,
    performancePosition: '工程师',
    year: '2023',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    userId: 1,
    performancePosition: '工程师',
    year: '2023',
  });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    userId: 0,
    performancePosition: '工程师',
    year: '2023',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
