/**
 * <AUTHOR>
 * @since 2022-6-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  scheduleDate: number /**排班日期 */;
};
export type BackendShift = {
  dutyId: number;
  dutyName: string /**班次名称 */;
  bizType: 'SCHEDULE' /**排班 */ | 'OVERTIME' /**加班 */;
  bizId: string; //班次对应的业务id
};

export type Shift = {
  id: number;
  name: string;
  bizType: 'SCHEDULE' /**排班 */ | 'OVERTIME' /**加班 */;
  bizId: string; //班次对应的业务id
};

export type SvcRespData = {
  data: Shift[];
  total: number;
};

export type RequestRespData = {
  data: BackendShift[] | null;
  total: number;
} | null;

export type ApiQ = {
  scheduleDate: number /**排班日期 */;
};

export type ApiR = ListResponse<BackendShift[] | null>;
