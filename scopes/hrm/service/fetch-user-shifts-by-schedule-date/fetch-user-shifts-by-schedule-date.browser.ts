/**
 * <AUTHOR>
 * @since 2022-6-22
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-shifts-by-schedule-date';
import type { SvcQuery, SvcRespData } from './fetch-user-shifts-by-schedule-date.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchUserShiftsByScheduleDate(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
