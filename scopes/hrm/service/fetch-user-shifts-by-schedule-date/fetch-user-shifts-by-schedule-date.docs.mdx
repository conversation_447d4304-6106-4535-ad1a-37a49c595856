---
description: 'A fetchUserShiftsByScheduleDate HTTP API service.'
labels: ['service', 'http', fetch-user-shifts-by-schedule-date]
---

## 概述

根据排班日期查询员工可选的外勤申请班次

## 使用

### Browser

```ts
import { fetchUserShiftsByScheduleDate } from '@manyun/hrm.service.fetch-user-shifts-by-schedule-date';

const { error, data } = await fetchUserShiftsByScheduleDate('success');
const { error, data } = await fetchUserShiftsByScheduleDate('error');
```

### Node

```ts
import { fetchUserShiftsByScheduleDate } from '@manyun/hrm.service.fetch-user-shifts-by-schedule-date/dist/index.node';

const { data } = await fetchUserShiftsByScheduleDate('success');

try {
  const { data } = await fetchUserShiftsByScheduleDate('error');
} catch (error) {
  // ...
}
```
