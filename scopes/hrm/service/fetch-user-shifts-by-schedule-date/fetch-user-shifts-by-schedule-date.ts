/**
 * <AUTHOR>
 * @since 2022-6-22
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-user-shifts-by-schedule-date.type';

const endpoint = '/pm/schedule/bizDuty';

/**
 *  根据排班日期查询员工可选的外勤申请班次
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/20028)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      scheduleDate: variant.scheduleDate,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(shift => ({
          id: shift.dutyId,
          name: shift.dutyName,
          bizId: shift.bizId,
          bizType: shift.bizType,
        })),
        total: data.total,
      },
      ...rest,
    };
  };
}
