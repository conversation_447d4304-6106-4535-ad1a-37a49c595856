/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchCurrentSchedule as webService } from './fetch-current-schedule.browser';
import { fetchCurrentSchedule as nodeService } from './fetch-current-schedule.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ currentTime: 111111111 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ currentTime: 2222222222 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ currentTime: 111111111 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('id');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ currentTime: 2222222222 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
