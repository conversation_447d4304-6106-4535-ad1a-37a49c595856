/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-current-schedule.type';

const endpoint = '/pm/schedule/currentSchedule';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/154/interface/api/18312 )
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      svcQuery
    );

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return {
      error,
      data,
      ...rest,
    };
  };
}
