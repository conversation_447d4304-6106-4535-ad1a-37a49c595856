---
description: 'A fetchCurrentSchedule HTTP API service.'
labels: ['service', 'http', current-schedule]
---

## 概述

查询用户当前排班

## 使用

### Browser

```ts
import { fetchCurrentSchedule } from '@hrm/service.current-schedule';

const { error, data } = await fetchCurrentSchedule({ currentTime: 111111111 });
const { error, data } = await fetchCurrentSchedule({ currentTime: 2222222222 });
```

### Node

```ts
import { fetchCurrentSchedule } from '@hrm/service.current-schedule/dist/index.node';

const { data } = await fetchCurrentSchedule({ currentTime: 111111111 });

try {
  const { data } = await fetchCurrentSchedule({ currentTime: 2222222222 });
} catch (error) {
  // ...
}
```
