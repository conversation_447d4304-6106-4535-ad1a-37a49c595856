/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type CurrentSchedule = {
  id: number;
  scheduleUnicode: string;
  scheduleDate: number;
  idcTag: string;
  blockTag: string;
  attGroupId: number;
  attGroupName: string;
  duty: Duty;
  dutyGroup: DutyGroup;
  timeInterval: string;
  scheduleStartTime: number;
  scheduleEndTime: number;
  scheduleStaffInfos: ScheduleStaffInfos[];
};

export type ScheduleStaffInfos = {
  id: number;
  userName: string;
  loginName: string | null;
  locked: boolean;
  scheduleScene?: ScheduleScene;
  teamLeader: boolean;
};

export type ScheduleScene = {
  code: string;
  desc: string;
  scene: string;
};
export type DutyGroup = {
  id: number;
  groupName: string;
  idcTag: null;
  blockTag: null;
  attGroupId: null;
  staffList: null;
};

export type Duty = {
  id: number;
  dutyName: string;
  onDutyTime: string;
  offDutyTime: string;
  offIsNextDay: boolean;
  dutyProperties: string | null;
  optionalDutyProperties: string | null;
};

export type SvcRespData = CurrentSchedule | null;

export type RequestRespData = CurrentSchedule | null;

export type ApiQ = {
  currentTime: number;
};

export type ApiR = ListResponse<CurrentSchedule | null>;
