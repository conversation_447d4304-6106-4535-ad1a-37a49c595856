/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './revoke-performance';
import type { SvcQuery } from './revoke-performance.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function revokePerformance(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
