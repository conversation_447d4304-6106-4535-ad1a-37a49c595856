/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './revoke-performance.type';

const endpoint = '/pm/pf/revoke';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/22719)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = { infoId: svcQuery.id, infoSubType: svcQuery.subType };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
