/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPerformanceSubType } from '@manyun/hrm.model.performance';

export type SvcQuery = {
  id: number;
  subType: BackendPerformanceSubType;
};

export type ApiQ = {
  infoId: number;
  infoSubType: BackendPerformanceSubType;
};

export type ApiR = WriteResponse;
