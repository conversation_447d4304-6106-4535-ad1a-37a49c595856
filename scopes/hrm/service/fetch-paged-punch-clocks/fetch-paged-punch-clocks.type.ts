/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-8
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendClockIn,
  ClockInJSON,
  PunchR<PERSON>ult,
  PunchWay,
} from '@manyun/hrm.model.clock-in';

export type SvcQuery = {
  page: number;
  pageSize: number;
  punchTime?: number[];
  staffId?: number;
  checkWays?: PunchWay[];
  checkResult?: PunchResult;
  blockTags?: string[];
  idcTags?: string[];
  attGroupId?: number;
};

export type SvcRespData = {
  data: ClockInJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendClockIn[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageSize: number;
  pageNum: number;
  staffId?: number;
  checkWayList?: PunchWay[];
  endDate?: number;
  startDate?: number;
  isDeleted?: number;
  blockTagList?: string[];
  idcTagList?: string[];
  attGroupId?: number;
};

export type ApiR = ListResponse<BackendClockIn[] | null>;
