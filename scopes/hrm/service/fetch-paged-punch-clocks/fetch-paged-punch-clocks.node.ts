/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-8
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-punch-clocks';
import type { SvcQuery, SvcRespData } from './fetch-paged-punch-clocks.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchPagedPunchClocks(svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
