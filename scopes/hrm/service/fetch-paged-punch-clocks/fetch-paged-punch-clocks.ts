/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-8
 *
 * @packageDocumentation
 */
import { ClockIn } from '@manyun/hrm.model.clock-in';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-paged-punch-clocks.type';

const endpoint = '/pm/check/record/list';

/**
 * 考勤打卡分页列表
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1216)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageSize: svcQ.pageSize,
      pageNum: svcQ.page,
      staffId: svcQ.staffId,
      checkWayList: svcQ.checkWays,
      isDeleted: svcQ.checkResult ? Number(svcQ.checkResult) : undefined,
      endDate: svcQ.punchTime && svcQ.punchTime[1],
      startDate: svcQ.punchTime && svcQ.punchTime[0],
      blockTagList: svcQ.blockTags,
      idcTagList: svcQ.idcTags,
      attGroupId: svcQ.attGroupId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => ClockIn.fromApiObject(d).toJSON()), total: data.total },
      ...rest,
    };
  };
}
