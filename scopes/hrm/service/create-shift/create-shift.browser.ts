/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-shift';
import type { SvcQuery, SvcRespData } from './create-shift.type';

const executor = getExecutor(webRequest);

/**
 * @param createD
 * @returns
 */
export function createShift(createD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(createD);
}
