/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-25
 *
 * @packageDocumentation
 */
import { Shift } from '@manyun/hrm.model.shift';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './create-shift.type';

const endpoint = '/pm/duty/create';

/**
 * 创建班次
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1240)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (createD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = Shift.fromJSON(createD).toApiObject();

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, { ...params });
  };
}
