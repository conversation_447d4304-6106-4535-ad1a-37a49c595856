/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendGrade,
  BackendPerformanceObjective,
  PerformanceObjectiveJSON,
} from '@manyun/hrm.model.performance-objective';

export type SimpleGoalJSON = Omit<
  PerformanceObjectiveJSON,
  'id' | 'supervisorRate' | 'selfEvaluation' | 'gmtModified' | 'createdAt' | 'gradeDescriptions'
> & {
  id?: number;
  selfEvaluation?: string;
  supervisorRate?: BackendGrade;
};
export type SvcQuery = {
  userId: number;
  type: 'TARGET' | 'EVAL';
  superiorIds: number[];
  goals: SimpleGoalJSON[];
  hiredDate: number;
  id?: number;
  selfEvaluation?: string;
};

export type ApiQ = {
  staffId: number /**员工id */;
  infoSubType: 'TARGET' | 'EVAL';
  superiorIds: number[] /**直线经理id */;
  sections: (Omit<
    BackendPerformanceObjective,
    'id' | 'gmtCreate' | 'gmtModified' | 'grade' | 'selfEvaluation' | 'gradeDescriptions'
  > & {
    id?: number;
    grade?: BackendGrade;
    selfEvaluation?: string;
  })[];
  hiredDate?: number;
  id?: number /**绩效id, 新增时为空 */;
  infoSelfEvaluation?: string /**自我评价 */;
};

export type SvcRespData = number | null;

export type RequestRespData = number | null;

export type ApiR = Response<RequestRespData>;
