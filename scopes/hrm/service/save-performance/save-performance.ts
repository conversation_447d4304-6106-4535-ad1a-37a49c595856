/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './save-performance.type';

const endpoint = '/pm/pf/save';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/22701)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      staffId: svcQuery.userId,
      infoSubType: svcQuery.type,
      superiorIds: svcQuery.superiorIds,
      id: svcQuery.id,
      hiredDate: svcQuery.hiredDate,
      sections: svcQuery.goals.map(goal => {
        return {
          sectionType: goal.type,
          title: goal.title,
          radio: goal.percent,
          metrics: goal.measurements,
          sectionStatus: goal.status,
          startTime: goal.startedAt,
          finishTime: goal.finishedAt,
          selfEvaluation: goal.selfEvaluation,
          grade: goal.supervisorRate,
          content: goal.content,
          id: goal.id,
        };
      }),
      infoSelfEvaluation: svcQuery.selfEvaluation?.trim(),
    };

    return await request.tryPost<RequestRespData, ApiQ>(endpoint, params);
  };
}
