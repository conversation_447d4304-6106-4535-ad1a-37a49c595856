/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { savePerformance as webService } from './save-performance.browser';
import { savePerformance as nodeService } from './save-performance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    userId: 0,
    type: 'TARGET',
    superiorIds: [],
    goals: [],
    hiredDate: 0,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(1);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    userId: 0,
    type: 'TARGET',
    superiorIds: [],
    goals: [],
    hiredDate: 0,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(1);
});
