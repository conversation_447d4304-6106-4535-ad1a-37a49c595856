/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-performance-annual-objective';
import type { SvcQuery } from './delete-performance-annual-objective.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deletePerformanceAnnualObjective(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
