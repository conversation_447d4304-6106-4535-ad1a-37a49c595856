/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-7-6
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './delete-performance-annual-objective.type';

const endpoint = '/pm/pf/deleteKpi';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23313)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = { id: svcQuery.id };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
