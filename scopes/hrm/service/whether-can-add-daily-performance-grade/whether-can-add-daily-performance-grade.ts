/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ } from './whether-can-add-daily-performance-grade.type';

const endpoint = '/pm/pf/year/validKpiRecord';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23511)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<number>> => {
    const params: ApiQ = svcQuery;

    return await request.tryPost<number, ApiQ>(endpoint, params);
  };
}
