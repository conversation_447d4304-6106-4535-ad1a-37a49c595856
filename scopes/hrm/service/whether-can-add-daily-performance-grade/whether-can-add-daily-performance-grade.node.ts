/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './whether-can-add-daily-performance-grade';
import type { ApiQ } from './whether-can-add-daily-performance-grade.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function whetherCanAddDailyPerformanceGrade(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<number>> {
  return executor(svcQuery);
}
