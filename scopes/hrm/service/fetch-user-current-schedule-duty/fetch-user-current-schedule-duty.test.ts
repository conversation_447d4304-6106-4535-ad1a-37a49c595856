/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserCurrentScheduleDuty as webService } from './fetch-user-current-schedule-duty.browser';
import { fetchUserCurrentScheduleDuty as nodeService } from './fetch-user-current-schedule-duty.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ scheduleDate: 111, dutyGroupId: 111 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ scheduleDate: 22, dutyGroupId: 22 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ scheduleDate: 111, dutyGroupId: 111 });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ scheduleDate: 22, dutyGroupId: 22 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
