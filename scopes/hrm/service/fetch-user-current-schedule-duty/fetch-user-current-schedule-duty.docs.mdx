---
description: 'A fetchUserCurrentScheduleDuty HTTP API service.'
labels: ['service', 'http', user-current-schedule-duty]
---

## 概述

查询当前员工班次

## 使用

### Browser

```ts
import { fetchUserCurrentScheduleDuty } from '@hrm/service.fetch-user-current-schedule-duty';

const { error, data } = await fetchUserCurrentScheduleDuty({ scheduleDate: 111, dutyGroupId: 111 });
const { error, data } = await fetchUserCurrentScheduleDuty({ scheduleDate: 22, dutyGroupId: 22 });
```

### Node

```ts
import { fetchUserCurrentScheduleDuty } from '@hrm/service.fetch-user-current-schedule-duty/dist/index.node';

const { data } = await fetchUserCurrentScheduleDuty({ scheduleDate: 111, dutyGroupId: 111 });

try {
  const { data } = await fetchUserCurrentScheduleDuty({ scheduleDate: 22, dutyGroupId: 22 });
} catch (error) {
  // ...
}
```
