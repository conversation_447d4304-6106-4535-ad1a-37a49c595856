/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type UserDutyJSON = {
  id: number;
  dutyName: string;
  scheduleId: number;
};

export type SvcRespData = UserDutyJSON[] | null;

export type RequestRespData = { data: UserDutyJSON[]; total: number } | null;

export type ApiQ = {
  scheduleDate: number;
  dutyGroupId: number;
};

export type ApiR = ListResponse<UserDutyJSON[] | null>;
