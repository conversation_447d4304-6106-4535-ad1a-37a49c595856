/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-26
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-current-schedule-duty';
import type { ApiQ, SvcRespData } from './fetch-user-current-schedule-duty.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchUserCurrentScheduleDuty(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
