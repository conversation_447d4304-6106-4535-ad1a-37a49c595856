/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './delete-daily-performance-grade-record.type';

const endpoint = '/pm/pf/deleteKpiRecord';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23367)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (deleteParams: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = { id: deleteParams.id };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
