/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-daily-performance-grade-record';
import type { SvcQuery } from './delete-daily-performance-grade-record.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deleteDailyPerformanceGradeRecord(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
