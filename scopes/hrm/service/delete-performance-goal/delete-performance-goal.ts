/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './delete-performance-goal.type';

const endpoint = '/pm/pf/sectionDelete';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/22827)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> => {
    const params: ApiQ = { sectionId: svcQuery.goalId };

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
