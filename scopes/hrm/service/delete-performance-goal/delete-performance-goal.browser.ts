/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-performance-goal';
import type { SvcQuery } from './delete-performance-goal.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function deletePerformanceGoal(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
