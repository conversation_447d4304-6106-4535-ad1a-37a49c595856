/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deletePerformanceGoal as webService } from './delete-performance-goal.browser';
import { deletePerformanceGoal as nodeService } from './delete-performance-goal.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ goalId: 1 });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ goalId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ goalId: 1 });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ goalId: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
