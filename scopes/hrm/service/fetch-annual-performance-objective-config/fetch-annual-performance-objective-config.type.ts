/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type BackendAnnualPerformanceObjectiveConfig = {
  confirmStartTime: string;
  confirmEndTime: string;
  confirmNotifyDays: number[];
  needNotEvalHiredDate: string;
  needNotEvalDay: number;
};

export type ApiR = Response<BackendAnnualPerformanceObjectiveConfig | null>;
