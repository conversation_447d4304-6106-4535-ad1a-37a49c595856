/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { BackendAnnualPerformanceObjectiveConfig } from './fetch-annual-performance-objective-config.type';

const endpoint = '/pm/pf/config/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23493)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<
    EnhancedAxiosResponse<BackendAnnualPerformanceObjectiveConfig | null>
  > => {
    return await request.tryPost<BackendAnnualPerformanceObjectiveConfig | null, {}>(endpoint, {});
  };
}
