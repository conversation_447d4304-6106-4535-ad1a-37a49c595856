/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-annual-performance-objective-config';
import type { BackendAnnualPerformanceObjectiveConfig } from './fetch-annual-performance-objective-config.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAnnualPerformanceObjectiveConfig(): Promise<
  EnhancedAxiosResponse<BackendAnnualPerformanceObjectiveConfig | null>
> {
  return executor();
}
