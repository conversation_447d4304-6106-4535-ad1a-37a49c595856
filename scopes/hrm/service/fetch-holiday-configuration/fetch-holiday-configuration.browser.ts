/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-holiday-configuration';
import type { ApiResponse, SvcQuery } from './fetch-holiday-configuration.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function fetchHolidayConfiguration(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(params);
}
