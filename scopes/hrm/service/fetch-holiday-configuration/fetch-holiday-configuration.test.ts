/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchHolidayConfiguration as webService } from './fetch-holiday-configuration.browser';
import { fetchHolidayConfiguration as nodeService } from './fetch-holiday-configuration.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ year: '2024' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('statutoryHolidays');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ year: '2024' });

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('statutoryHolidays');
});
