/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-11
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  year: string;
};
export type CalendarConfiguration = {
  gmtCreate?: number | null;
  gmtModified?: number | null;
  id: number;
  /**
   * 法定节假日
   */
  statutoryHolidays?: string[] | null;
  /**
   * 工作日
   */
  statutoryWorkdays?: string[] | null;
  /**
   * 休息日
   */
  statutoryRestDays?: string[] | null;
  /**
   * 重保日
   */
  protectionDays?: string[] | null;
  year?: string | null;
};

export type ApiResponse = Response<CalendarConfiguration | null>;
