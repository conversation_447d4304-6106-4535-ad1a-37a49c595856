/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-3-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiResponse, SvcQuery } from './fetch-holiday-configuration.type';

const endpoint = '/pm/statutoryHoliday/list';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/1864)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryGet<ApiResponse['data']>(endpoint, { params: params });
  };
}
