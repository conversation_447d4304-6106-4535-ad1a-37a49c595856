---
description: 'A updatePartTimeJobs HTTP API service.'
labels: ['service', 'http']
---

兼岗记录更新

## Usage

### Browser

```ts
import { updatePartTimeJobs } from '@manyun/hrm.service.update-part-time-jobs';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { UpdatePartTimeJobsService } from '@manyun/hrm.service.update-part-time-jobs/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = UpdatePartTimeJobsService.from(nodeRequest);
```
