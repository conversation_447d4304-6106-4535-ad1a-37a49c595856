/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-8
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './update-part-time-jobs.js';
import type { ApiArgs } from './update-part-time-jobs.type.js';

/**
 * @param args
 * @returns
 */
export function updatePartTimeJobs(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
