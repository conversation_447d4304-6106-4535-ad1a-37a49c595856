/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-annual-performance-objective-config';
import type { ApiQ } from './update-annual-performance-objective-config.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateAnnualPerformanceObjectiveConfig(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(svcQuery);
}
