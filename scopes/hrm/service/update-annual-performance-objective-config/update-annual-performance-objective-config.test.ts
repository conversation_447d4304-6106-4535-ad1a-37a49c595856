/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateAnnualPerformanceObjectiveConfig as webService } from './update-annual-performance-objective-config.browser';
import { updateAnnualPerformanceObjectiveConfig as nodeService } from './update-annual-performance-objective-config.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    confirmStartTime: '01-10',
    confirmEndTime: '10-10',
    confirmNotifyDays: [1, 2],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    confirmStartTime: '01-10',
    confirmEndTime: '10-10',
    confirmNotifyDays: [1, 2],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    confirmStartTime: '01-10',
    confirmEndTime: '10-10',
    confirmNotifyDays: [1, 2],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    confirmStartTime: '01-10',
    confirmEndTime: '10-10',
    confirmNotifyDays: [1, 2],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
