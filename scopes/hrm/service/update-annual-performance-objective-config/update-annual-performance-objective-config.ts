/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ } from './update-annual-performance-objective-config.type';

const endpoint = '/pm/pf/config/createAndModify';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23430)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: ApiQ): Promise<EnhancedAxiosResponse<boolean>> => {
    return await request.tryPost<boolean, ApiQ>(endpoint, svcQuery);
  };
}
