/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchSchedulesForLeaveRequest as webService } from './fetch-schedules-for-leave-request.browser';

// import { fetchSchedulesForLeaveRequest as nodeService } from './fetch-schedules-for-leave-request.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    staffId: 0,
    startDate: 0,
    endDate: 0,
    alterType: 'LEAVE',
  });

  expect(error).toBe(undefined);
});

// test('[web] should resolve error response', async () => {
//   const { error, data } = await webService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[node] should resolve success response', async () => {
//   const { error, data } = await nodeService('success');

//   expect(error).toBe(undefined);
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[node] should resolve error response', async () => {
//   const { error, data } = await nodeService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });
