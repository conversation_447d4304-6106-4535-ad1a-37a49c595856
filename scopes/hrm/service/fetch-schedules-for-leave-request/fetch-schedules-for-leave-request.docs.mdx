---
description: 'A fetchSchedulesForLeaveRequest HTTP API service.'
labels: ['service', 'http', fetch-schedules-for-leave-request]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchSchedulesForLeaveRequest } from '@manyun/hrm.service.fetch-schedules-for-leave-request';

const { error, data } = await fetchSchedulesForLeaveRequest('success');
const { error, data } = await fetchSchedulesForLeaveRequest('error');
```

### Node

```ts
import { fetchSchedulesForLeaveRequest } from '@manyun/hrm.service.fetch-schedules-for-leave-request/dist/index.node';

const { data } = await fetchSchedulesForLeaveRequest('success');

try {
  const { data } = await fetchSchedulesForLeaveRequest('error');
} catch (error) {
  // ...
}
```
