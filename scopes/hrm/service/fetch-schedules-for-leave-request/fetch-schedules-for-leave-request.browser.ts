/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-schedules-for-leave-request';
import type { SvcQuery, SvcRespData } from './fetch-schedules-for-leave-request.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchSchedulesForLeaveRequest(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
