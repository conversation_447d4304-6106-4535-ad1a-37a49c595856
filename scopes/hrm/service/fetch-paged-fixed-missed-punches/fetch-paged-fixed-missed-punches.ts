/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { ClockIn } from '@manyun/hrm.model.clock-in';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-paged-fixed-missed-punches.type';

const endpoint = '/pm/check/checkPage';

/**
 * 分页查询打卡记录
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1176)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQ: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      pageNum: svcQ.page,
      pageSize: svcQ.pageSize,
      applyStartTime: svcQ.applyTime && svcQ.applyTime[0],
      applyEndTime: svcQ.applyTime && svcQ.applyTime[1],
      bizId: svcQ.bizId,
      checkEndTime: svcQ.checkTime && svcQ.checkTime[1],
      checkStartTime: svcQ.checkTime && svcQ.checkTime[0],
      checkWay: svcQ.checkWay,
      resourcesCodes: svcQ.blockTags,
      staffId: svcQ.applyId,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(d => ClockIn.fromApiObject(d).toJSON()), total: data.total },
      ...rest,
    };
  };
}
