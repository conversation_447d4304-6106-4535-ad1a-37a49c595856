/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPagedFixedMissedPunches as webService } from './fetch-paged-fixed-missed-punches.browser';
import { fetchPagedFixedMissedPunches as nodeService } from './fetch-paged-fixed-missed-punches.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    page: 1,
    pageSize: 10,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    page: 1,
    pageSize: 10,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
