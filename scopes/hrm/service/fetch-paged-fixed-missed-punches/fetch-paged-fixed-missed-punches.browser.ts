/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-fixed-missed-punches';
import type { SvcQuery, SvcRespData } from './fetch-paged-fixed-missed-punches.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQ
 * @returns
 */
export function fetchPagedFixedMissedPunches(
  svcQ: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQ);
}
