/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-8
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendClockIn, ClockInJSON, PunchWay } from '@manyun/hrm.model.clock-in';

export type SvcQuery = {
  page: number;
  pageSize: number;
  checkWay?: PunchWay;
  bizId?: string;
  applyTime?: number[];
  checkTime?: number[];
  blockTags?: string[];
  applyId?: number;
};

export type SvcRespData = {
  data: ClockInJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendClockIn[] | null;
  total: number;
} | null;

export type ApiQ = {
  pageNum: number;
  pageSize: number;
  applyEndTime?: number;
  applyStartTime?: number;
  bizId?: string;
  checkEndTime?: number;
  checkStartTime?: number;
  checkWay?: PunchWay;
  resourcesCodes?: string[];
  staffId?: number;
};

export type ApiR = ListResponse<BackendClockIn[] | null>;
