---
description: 'A fetchPagedStaffCertificationsList HTTP API service.'
labels: ['service', 'http']
---

员工资质证书档案分页接口

## Usage

### Browser

```ts
import { fetchPagedStaffCertificationsList } from '@manyun/hrm.service.fetch-paged-staff-certifications-list';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchPagedStaffCertificationsListService } from '@manyun/hrm.service.fetch-paged-staff-certifications-list/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchPagedStaffCertificationsListService.from(nodeRequest);
```
