/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 有效 1|无效 0
   */
  certEffectStatus?: number | null;
  /**
   * 已上传 1| 待上传 0
   */
  uploadStatus?: number | null;
  /**
   * 审批中: APPROVING
   */
  instStatus?: string | null;
  /**
   * 1:即将过期
   */
  willExpire?: number | null;
  /**
   *  1:待复审
   */
  willCheck?: number | null;
  /**
   * 用户名称
   */
  userName?: string | null;
  /**
   * 岗位code
   */
  positionCode?: string | null;
  /**
   * 证书名称
   */
  certName?: string | null;
  /**
   * 楼栋
   */
  blockGuidList?: string[] | null;
  /**
   * 升序:ASC/降序 :DESC
   */
  jobNumberSort?: string;
};

export type CertInfos = {
  /**
   * 证书名称
   */
  certName: string;
  /**
   * 已上传 1|  待上传 0
   */
  uploadStatus: number;
  /**
   * 有效 1|无效 0
   */
  certEffectStatus: number;
  /**
   * 1:即将过期
   */
  willExpire: number;
  /**
   *  1:待复审
   */
  willCheck: number;
  /**
   * 证书开始日期
   */
  effectStartDate?: string | null;
  /**
   * 证书结束日期
   */
  effectEndDate?: string | null;

  instStatus?: string;
};

export type StaffCertInfos = {
  /**
   * 主键id
   */
  jobNumber?: string | null;
  /**
   * 用户ID
   */
  userId: number;
  /**
   * 证书名称
   */
  userName: string;
  /**
   * 岗位
   */
  position: string;
  /**
   * 岗位Code
   */
  positionCode: string;
  /**
   * 楼栋
   */
  blockGuids?: string | null;
  positionCertDoList?: CertInfos[] | null;
};

export type ApiResponse = ListBackendResponse<StaffCertInfos[]>;
