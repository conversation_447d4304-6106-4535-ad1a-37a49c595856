---
description: 'A updateInvolvedPerformanceStaffInfos HTTP API service.'
labels: ['service', 'http']
---

更新用户接口(选择性更新字段)

## Usage

### Browser

```ts
import { updateInvolvedPerformanceStaffInfos } from '@manyun/hrm.service.update-involved-performance-staff-infos';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { UpdateInvolvedPerformanceStaffInfosService } from '@manyun/hrm.service.update-involved-performance-staff-infos/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = UpdateInvolvedPerformanceStaffInfosService.from(nodeRequest);
```
