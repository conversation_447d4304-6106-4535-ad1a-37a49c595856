/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs } from './update-involved-performance-staff-infos.type.js';

const endpoint = '/pm/user/updateSelective';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30500)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> => {
    return request.tryPost<boolean, ApiArgs>(endpoint, args);
  };
}
