/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import type { WriteBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  id: number;
  /**
   * 楼栋
   */
  blockGuid?: string | null;
  /**
   * 管理岗标签
   */
  jobLabel?: string | null;
  /**
   * 楼栋guid列表
   */
  blockGuids?: string[] | null;
};

export type ApiResponse = WriteBackendResponse;
