/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './update-involved-performance-staff-infos.js';
import type { ApiArgs } from './update-involved-performance-staff-infos.type.js';

/**
 * @param args
 * @returns
 */
export function updateInvolvedPerformanceStaffInfos(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<boolean>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
