/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { SORT_TYPE_MAPPER } from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './export-daily-att-statistics.type';
import { COLUMNS_FIELD_TRANSFOR_MAPPER } from './export-daily-att-statistics.type';

const endpoint = '/pm/export/daily_statistics/list';

/**
 * 导出每日统计
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18360)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      blockTagList: svcQuery?.blockTags,
      idcTagList: svcQuery?.idcTags,
      dutyGroupName: svcQuery?.dutyGroupName,
      endDate: svcQuery?.endDate,
      startDate: svcQuery?.startDate,
      isAbsent: svcQuery?.isAbsent,
      isMisOff: svcQuery?.isMisOff,
      isMisOn: svcQuery?.isMisOn,
      staffId: svcQuery?.staffId,
      isLateOn: svcQuery?.isLateOn,
      isOutWork: svcQuery?.isOutWork,
      isEarlyOff: svcQuery?.isEarlyOff,
      dutyIdList: svcQuery?.dutyIds,
      dutyGroupIdList: svcQuery?.dutyGroupIds,
      includeColumnFiledNames: svcQuery?.includeFileds?.map(
        field => COLUMNS_FIELD_TRANSFOR_MAPPER[field]
      ),
      sortField: svcQuery?.sortField,
      sortType: svcQuery?.sortType ? SORT_TYPE_MAPPER[svcQuery?.sortType] : undefined,
      bizType: svcQuery?.bizType,
      needLeave: svcQuery?.needLeave !== undefined ? svcQuery.needLeave === 'TRUE' : undefined,
      staffType: svcQuery?.staffType,
      needAttendance: svcQuery?.needAttendance,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      params,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
