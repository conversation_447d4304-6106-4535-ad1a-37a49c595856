/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportDailyAttStatistics as webService } from './export-daily-att-statistics.browser';
import { exportDailyAttStatistics as nodeService } from './export-daily-att-statistics.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService();

  expect(error).toBe(undefined);
});
