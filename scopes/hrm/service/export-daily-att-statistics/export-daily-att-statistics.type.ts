/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import type {
  BackendSortField,
  BackendSortType,
  SortField,
  SortType,
} from '@manyun/hrm.service.fetch-paged-att-statistics-by-day';

export type BackendIncludeColumnField =
  | 'staffId'
  | 'staffName'
  | 'blockTag'
  | 'scheduleDate'
  | 'dutyGroupName'
  | 'dutyName'
  | 'aggOnDutyTime'
  | 'aggOffDutyTime'
  | 'lateOnMinutes'
  | 'earlyOffMinutes'
  | 'standardWorkMinutes'
  | 'workTime'
  | 'outWorkMinutes'
  | 'bizType'
  | 'leaveType'
  | 'staffType';

export type IncludeColumnField =
  | 'staffId'
  | 'staffName'
  | 'blockTag'
  | 'scheduleDate'
  | 'dutyGroupName'
  | 'dutyName'
  | 'onDutyTime'
  | 'offDutyTime'
  | 'standardWorkMinutes'
  | 'workTime'
  | 'earlyOffMinutes'
  | 'lateOnMinutes'
  | 'outWorkMinutes'
  | 'bizType'
  | 'leaveType'
  | 'staffType';

export const COLUMNS_FIELD_TRANSFOR_MAPPER: Record<IncludeColumnField, BackendIncludeColumnField> =
  {
    staffId: 'staffId',
    staffName: 'staffName',
    blockTag: 'blockTag',
    scheduleDate: 'scheduleDate',
    dutyGroupName: 'dutyGroupName',
    onDutyTime: 'aggOnDutyTime',
    dutyName: 'dutyName',
    offDutyTime: 'aggOffDutyTime',
    lateOnMinutes: 'lateOnMinutes',
    earlyOffMinutes: 'earlyOffMinutes',
    standardWorkMinutes: 'standardWorkMinutes',
    workTime: 'workTime',
    outWorkMinutes: 'outWorkMinutes',
    bizType: 'bizType',
    leaveType: 'leaveType',
    staffType: 'staffType',
  };

export type SvcQuery = {
  blockTags?: string[];
  idcTags?: string[];
  dutyGroupName?: string;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  staffId?: number;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  isOutWork?: boolean;
  dutyIds?: number[] /**班次id*/;
  dutyGroupIds?: number[] /**班组id  */;
  includeFileds?: IncludeColumnField[];
  sortField?: SortField;
  sortType?: SortType;
  bizType?: string;
  needLeave?: 'TRUE' | 'FALSE';
  staffType?: string;
  needAttendance?: boolean; //是否出勤
};

export type ApiQ = {
  blockTagList?: string[];
  idcTagList?: string[];
  dutyGroupName?: string;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  isOutWork?: boolean;
  staffId?: number;
  dutyIdList?: number[] /**班次id*/;
  dutyGroupIdList?: number[] /**班组id  */;
  includeColumnFiledNames?: BackendIncludeColumnField[]; //导出的定制列字段名称
  sortField?: BackendSortField;
  sortType?: BackendSortType;
  bizType?: string;
  needLeave?: boolean;
  staffType?: string;
  needAttendance?: boolean; //是否出勤
};

export type SvcRespData = Blob;
export type RequestRespData = Blob;
