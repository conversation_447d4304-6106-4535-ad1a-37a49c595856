/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-19
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createUserCertificate as webService } from './create-user-certificate.browser';
import { createUserCertificate as nodeService } from './create-user-certificate.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    userId: 0,
    certName: '',
    effectStartDate: '2022-08-29',
    effectEndDate: '2022-08-29',
    fileType: '.jpg',
    fileId: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
