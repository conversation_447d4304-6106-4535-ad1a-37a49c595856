/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-19
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-user-certificate';
import type { SvcQuery, SvcRespData } from './create-user-certificate.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function createUserCertificate(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
