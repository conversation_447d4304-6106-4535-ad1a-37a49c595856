/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-19
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  userId: number;
  certName: string;
  effectStartDate: string;
  effectEndDate: string;
  checkDate?: string;
  fileType: string;
  fileId: number;
};

export type SvcRespData = string;

export type RequestRespData = string;

export type ApiQ = {
  userId: number;
  certName: string;
  effectStartDate: string;
  effectEndDate: string;
  checkDate?: string;
  fileId: number;
  fileType: string;
};

export type ApiR = Response<string | null>;
