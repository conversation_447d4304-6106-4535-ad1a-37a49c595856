/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-19
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './create-user-certificate.type';

const endpoint = '/pm/cert/add';

/**
 * 证书新增
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18852)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, variant);

    return { error, data, ...rest };
  };
}
