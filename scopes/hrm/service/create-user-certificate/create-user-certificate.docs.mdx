---
description: 'A createUserCertificate HTTP API service.'
labels: ['service', 'http']
---

## 概述

新建用户证书信息

## 使用

### Browser

```ts
import { createUserCertificate } from '@manyun/hrm.service.create-user-certificate';

const { error, data } = await createUserCertificate('success');
const { error, data } = await createUserCertificate('error');
```

### Node

```ts
import { createUserCertificate } from '@manyun/hrm.service.create-user-certificate/dist/index.node';

const { data } = await createUserCertificate('success');
const { error, data } = await createUserCertificate('error');
```
