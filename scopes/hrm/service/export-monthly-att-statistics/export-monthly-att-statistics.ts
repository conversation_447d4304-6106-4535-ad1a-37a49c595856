/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { SORT_TYPE_MAPPER } from '@manyun/hrm.service.fetch-paged-att-statistics-by-month';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './export-monthly-att-statistics.type';

const endpoint = '/pm/export/monthly_statistics/list';

/**
 * 导出月度考勤
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18368)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      blockTagList: svcQuery?.blockTags,
      idcTagList: svcQuery?.idcTags,
      dutyGroupName: svcQuery?.dutyGroupName,
      endDate: svcQuery?.endDate,
      startDate: svcQuery?.startDate,
      isAbsent: svcQuery?.isAbsent,
      isMisOff: svcQuery?.isMisOff,
      isMisOn: svcQuery?.isMisOn,
      staffId: svcQuery?.staffId,
      isLateOn: svcQuery?.isLateOn,
      isEarlyOff: svcQuery?.isEarlyOff,
      pageNum: svcQuery?.page,
      pageSize: svcQuery?.pageSize,
      includeColumnFiledNames: svcQuery?.includeColumnFiledNames,
      sortField: svcQuery?.sortField,
      sortType: svcQuery?.sortType ? SORT_TYPE_MAPPER[svcQuery?.sortType] : undefined,
      staffType: svcQuery?.staffType,
      needLeave: svcQuery?.needLeave !== undefined ? svcQuery.needLeave === 'TRUE' : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      params,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
