/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-monthly-att-statistics';
import type { SvcQuery, SvcRespData } from './export-monthly-att-statistics.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function exportMonthlyAttStatistics(
  variant?: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
