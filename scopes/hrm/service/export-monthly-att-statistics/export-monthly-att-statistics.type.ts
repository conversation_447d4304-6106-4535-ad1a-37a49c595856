/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import type {
  BackendSortField,
  BackendSortType,
  SortField,
  SortType,
} from '@manyun/hrm.service.fetch-paged-att-statistics-by-month';

export type BackendIncludeColumnField =
  | 'staffId'
  | 'staffName'
  | 'blockTag'
  | 'totalAttendanceDay'
  | 'attendanceDay'
  | 'standardWorkMinutes'
  | 'workTime'
  | 'lateTimes'
  | 'lateOnMinutes'
  | 'earlyOffTimes'
  | 'earlyOffMinutes'
  | 'missOnCardTimes'
  | 'missOffCardTimes'
  | 'absentTimes';

export type IncludeColumnField = BackendIncludeColumnField;

export type SvcQuery = {
  blockTags?: string[];
  idcTags?: string[];
  dutyGroupName?: string;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  staffId?: number;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  page?: number;
  pageSize?: number;
  includeColumnFiledNames?: IncludeColumnField[];
  sortField?: SortField;
  sortType?: SortType;
  staffType?: string;
  needLeave?: string;
};

export type ApiQ = {
  blockTagList?: string[];
  idcTagList?: string[];
  dutyGroupName?: string;
  endDate?: number;
  startDate?: number;
  isAbsent?: boolean;
  isMisOff?: boolean;
  isMisOn?: boolean;
  staffId?: number;
  isLateOn?: boolean;
  isEarlyOff?: boolean;
  pageNum?: number;
  pageSize?: number;
  includeColumnFiledNames?: BackendIncludeColumnField[];
  sortField?: BackendSortField;
  sortType?: BackendSortType;
  staffType?: string;
  needLeave?: boolean;
};

export type SvcRespData = Blob;
export type RequestRespData = Blob;
