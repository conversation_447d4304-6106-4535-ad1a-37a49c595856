/**
 * <AUTHOR>
 * @since 2022-6-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportMonthlyAttStatistics as webService } from './export-monthly-att-statistics.browser';
import { exportMonthlyAttStatistics as nodeService } from './export-monthly-att-statistics.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService();

  expect(error).toBe(undefined);
});
