/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-5
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ChangeInfo = {
  replaceStaffId?: number | null;
  dutyId?: string | null;
  scheduleDate?: number | null;
  dutyName?: string;
};

export type CheckResultParam = {
  /**
   * -  ON 上班卡
   * -  OFF 下班卡
   * -  ALL 所有打卡
   */
  checkChangeType?: 'ON' | 'OFF' | 'ALL' | null;
  /**
   * 是否减少假期余额
   */
  reduceBalance?: boolean | null;
  /**
   * 请假类型
   */
  leaveType?: string | null;
  /**
   * 开始时间
   */
  startTime?: number | null;
  /**
   * 结束时间
   */
  endTime?: number | null;
  /**
   * 总时长
   */
  totalTime?: number | null;
  /**
   * 单位：DAY("天"),     HOUR("小时");
   */
  unit?: string | null;
  /**
   * 请假用户或替班人
   */
  changeInfo?: ChangeInfo | null;
};

export type CheckType =
  | 'CHECK_CHANGE'
  | 'LEAVE'
  | 'EXCHANGE'
  | 'REPLACE'
  | 'REST'
  | 'OVERTIME_CHANGE'
  | 'OVERTIME_DELETE';

export type ApiArgs = {
  /**
   * 考勤结果id
   */
  checkResultId: number;
  /**
   * - CHECK_CHANGE 修改打卡记录
   * - LEAVE 请假
   * - EXCHANGE 换班
   * - REPLACE 顶班
   * - REST 修班
   * - OVERTIME_CHANGE 修改加班
   * - OVERTIME_DELETE 删除加班
   */
  changeType: CheckType;
  /**
   * 原因
   */
  reason: string;
  checkResultParam?: CheckResultParam;
  /**
   * 文件列表
   */
  files?: BackendMcUploadFile[];
};

export type ApiResponse = WriteResponse;
