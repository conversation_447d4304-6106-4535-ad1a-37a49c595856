/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-5
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-att-statistic-data';
import type { ApiArgs } from './update-att-statistic-data.type';

const executor = getExecutor(webRequest);

/**
 * @param args
 * @returns
 */
export function updateAttStatisticData(args: ApiArgs): Promise<EnhancedAxiosResponse<boolean>> {
  return executor(args);
}
