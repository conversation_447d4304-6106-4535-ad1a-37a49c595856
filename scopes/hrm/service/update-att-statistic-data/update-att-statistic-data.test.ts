/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-2-5
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updateAttStatisticData as webService } from './update-att-statistic-data.browser';
import { updateAttStatisticData as nodeService } from './update-att-statistic-data.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    checkResultId: 0,
    changeType: 'CHECK_CHANGE',
    reason: '',
  });

  expect(error).toBe(undefined);
  expect(typeof data).toBe('boolean');
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    checkResultId: 0,
    changeType: 'CHECK_CHANGE',
    reason: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    checkResultId: 0,
    changeType: 'CHECK_CHANGE',
    reason: '',
  });

  expect(error).toBe(undefined);
  expect(typeof data).toBe('boolean');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    checkResultId: 0,
    changeType: 'CHECK_CHANGE',
    reason: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
