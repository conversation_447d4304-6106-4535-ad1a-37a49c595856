/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-groups-by-resource-code-and-role-codes';
import type { ApiQ, SvcRespData } from './fetch-groups-by-resource-code-and-role-codes.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchGroupsByResourceCodeAndRoleCodes(
  svcQuery: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
