/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-29
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type Group = {
  creatorId: string | null;
  creatorName: string | null;
  gmtCreate: number;
  gmtModified: string | null;
  groupCode: string;
  groupName: string;
  id: number;
  isDeleted: string;
  modifierId: string | null;
  modifierName: string | null;
  remarks: string | null;
  resourceCodes: string | null;
};

export type SvcRespData = { data: Group[]; total: number };

export type RequestRespData = { data: Group[]; total: number };

export type ApiQ = {
  resourceCode?: string;
  roleCodes?: string[];
};

export type ApiR = ListResponse<{ data: Group[]; total: number } | null>;
