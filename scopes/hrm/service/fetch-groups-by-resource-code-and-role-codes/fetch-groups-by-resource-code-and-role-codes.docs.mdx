---
description: 'A fetchGroupsByResourceCodeAndRoleCodes HTTP API service.'
labels: ['service', 'http', groups-by-resource-code-and-role-codes]
---

## 概述

根据资源和角色 code 查询用户组

## 使用

### Browser

```ts
import { fetchGroupsByResourceCodeAndRoleCodes } from '@hrm/service.fetch-groups-by-resource-code-and-role-codes';

const { error, data } = await fetchGroupsByResourceCodeAndRoleCodes({ resourceCode: 'EC01.A' });
const { error, data } = await fetchGroupsByResourceCodeAndRoleCodes({ resourceCode: 'EC01.B' });
```

### Node

```ts
import { fetchGroupsByResourceCodeAndRoleCodes } from '@hrm/service.fetch-groups-by-resource-code-and-role-codes/dist/index.node';

const { data } = await fetchGroupsByResourceCodeAndRoleCodes({ resourceCode: 'EC01.A' });

try {
  const { data } = await fetchGroupsByResourceCodeAndRoleCodes({ resourceCode: 'EC01.B' });
} catch (error) {
  // ...
}
```
