/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-10-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-workdays';
import type { ApiArgs, ApiResponse } from './fetch-workdays.type';

const executor = getExecutor(nodeRequest);

/**
 * @param args
 * @returns
 */
export function fetchWorkdays(args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> {
  return executor(args);
}
