/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-10-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchWorkdays as webService } from './fetch-workdays.browser';
import { fetchWorkdays as nodeService } from './fetch-workdays.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    startTime: 12,
    endTime: 12,
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ startTime: 0, endTime: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({ startTime: 12, endTime: 12 });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ startTime: 0, endTime: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
