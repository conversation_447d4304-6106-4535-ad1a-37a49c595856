/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-10-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiArgs, ApiResponse } from './fetch-workdays.type';

const endpoint = '/pm/statutoryHoliday/workday/query';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/25343)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return await request.tryPost<ApiResponse['data'], ApiArgs>(endpoint, args);
  };
}
