/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendAnnualPerformancePlan } from '@manyun/hrm.model.annual-performance-plan';

export type SvcQuery = {
  id: number;
};

export type ApiAnnualPerformancePlan = Omit<
  BackendAnnualPerformancePlan,
  'resources' | 'createUser' | 'evalPositions'
> & {
  resources: string[];
  evalPositions: string[];
  creatorId: number;
};

export type ApiQ = {
  id: number;
};

export type ApiR = Response<ApiAnnualPerformancePlan[] | null>;
