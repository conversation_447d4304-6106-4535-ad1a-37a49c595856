/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiAnnualPerformancePlan,
  ApiQ,
  SvcQuery,
} from './fetch-annual-performance-plan.type';

const endpoint = '/pm/pf/planDetail';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23538)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (
    svcQuery: SvcQuery
  ): Promise<EnhancedAxiosResponse<ApiAnnualPerformancePlan | null>> => {
    const params: ApiQ = { id: svcQuery.id };

    return await request.tryPost<ApiAnnualPerformancePlan | null, ApiQ>(endpoint, params);
  };
}
