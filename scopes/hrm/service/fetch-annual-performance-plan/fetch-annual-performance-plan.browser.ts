/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-annual-performance-plan';
import type { ApiAnnualPerformancePlan, SvcQuery } from './fetch-annual-performance-plan.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchAnnualPerformancePlan(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<ApiAnnualPerformancePlan | null>> {
  return executor(svcQuery);
}
