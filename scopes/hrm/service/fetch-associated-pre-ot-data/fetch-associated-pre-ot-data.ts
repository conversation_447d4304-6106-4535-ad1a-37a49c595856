/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-associated-pre-ot-data.type';

const endpoint = '/pm/overtimeWork/overtimePreList';

/**
 * 查询关联加班申请记录
 * @see [Doc](https://manyun.yuque.com/ewe5b3/se3fdm/en45v6#KvPDC)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      applyStaffId: variant.apply,
      startTime: variant.startTime,
      endTime: variant.endTime,
      statusList: variant.status,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return {
      error,
      data: data.data.map(d => ({
        title: d.title,
        apply: d.applyStaffId,
        status: d.status,
        bizId: d.bizId,
        idc: d.idcTag,
        blocks: d.blockGuids,
        startTime: d.startTime,
        endTime: d.endTime,
        totalTime: d.totalTime,
        creatorId: d.creatorId,
      })),
      ...rest,
    };
  };
}
