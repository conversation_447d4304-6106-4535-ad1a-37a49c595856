/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

// import { fetchAssociatedPreOtData as webService } from './fetch-associated-pre-ot-data.browser';
// import { fetchAssociatedPreOtData as nodeService } from './fetch-associated-pre-ot-data.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

// test('[web] should resolve success response', async () => {
//   const { error, data } = await webService('success');

//   expect(error).toBe(undefined);
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[web] should resolve error response', async () => {
//   const { error, data } = await webService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[node] should resolve success response', async () => {
//   const { error, data } = await nodeService('success');

//   expect(error).toBe(undefined);
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });

// test('[node] should resolve error response', async () => {
//   const { error, data } = await nodeService('error');

//   expect(typeof error!.code).toBe('string');
//   expect(typeof error!.message).toBe('string');
//   expect(data.data).toHaveProperty('length');
//   expect(typeof data.total).toBe('number');
// });
