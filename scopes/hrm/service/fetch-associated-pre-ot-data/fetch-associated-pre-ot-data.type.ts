/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendOvertimeRecord, OvertimeRecord } from '@manyun/hrm.service.fetch-ot-data';

export type SvcQuery = {
  apply: number;
  startTime: number;
  endTime: number;
  status?: string[];
};

export type SvcRespData = OvertimeRecord[];

export type RequestRespData = {
  data: BackendOvertimeRecord[] | null;
  total: number;
} | null;

export type ApiQ = {
  applyStaffId: number;
  startTime: number;
  endTime: number;
  statusList?: string[];
};

export type ApiR = ListResponse<BackendOvertimeRecord[] | null>;
