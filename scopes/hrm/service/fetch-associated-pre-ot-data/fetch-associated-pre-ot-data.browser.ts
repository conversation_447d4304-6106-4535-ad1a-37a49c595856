/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-associated-pre-ot-data';
import type { SvcQuery, SvcRespData } from './fetch-associated-pre-ot-data.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchAssociatedPreOtData(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
