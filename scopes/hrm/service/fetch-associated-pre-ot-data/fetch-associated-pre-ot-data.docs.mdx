---
description: 'A fetchAssociatedPreOtData HTTP API service.'
labels: ['service', 'http', fetch-associated-pre-ot-data]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchAssociatedPreOtData } from '@manyun/hrm.service.fetch-associated-pre-ot-data';

const { error, data } = await fetchAssociatedPreOtData('success');
const { error, data } = await fetchAssociatedPreOtData('error');
```

### Node

```ts
import { fetchAssociatedPreOtData } from '@manyun/hrm.service.fetch-associated-pre-ot-data/dist/index.node';

const { data } = await fetchAssociatedPreOtData('success');

try {
  const { data } = await fetchAssociatedPreOtData('error');
} catch (error) {
  // ...
}
```
