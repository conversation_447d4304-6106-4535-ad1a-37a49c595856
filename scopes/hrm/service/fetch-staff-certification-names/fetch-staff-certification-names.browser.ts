/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-10
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-staff-certification-names.js';
import type { ApiArgs, ApiResponse } from './fetch-staff-certification-names.type.js';

/**
 * @param args
 * @returns
 */
export function fetchStaffCertificationNames(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
