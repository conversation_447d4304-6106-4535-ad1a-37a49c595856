---
description: 'A fetchStaffCertificationNames HTTP API service.'
labels: ['service', 'http']
---

证书下拉查询列表

## Usage

### Browser

```ts
import { fetchStaffCertificationNames } from '@manyun/hrm.service.fetch-staff-certification-names';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchStaffCertificationNamesService } from '@manyun/hrm.service.fetch-staff-certification-names/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchStaffCertificationNamesService.from(nodeRequest);
```
