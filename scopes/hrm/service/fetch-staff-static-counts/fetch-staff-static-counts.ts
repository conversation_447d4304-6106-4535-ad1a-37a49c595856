/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, Request } from '@glpdev/symphony.services.request';

import type { ApiArgs, ApiResponse } from './fetch-staff-static-counts.type.js';

const endpoint = '/pm/userFiles/count';

/**
 * @see [Doc](http://yapi.manyun-local.com/project/78/interface/api/30676)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends Request = Request>(request: T) {
  return async (args: ApiArgs): Promise<EnhancedAxiosResponse<ApiResponse['data']>> => {
    return request.tryPost<ApiResponse['data'] | null, ApiArgs>(endpoint, args);
  };
}
