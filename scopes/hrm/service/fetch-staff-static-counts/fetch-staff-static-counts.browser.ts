/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */

import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-staff-static-counts.js';
import type { ApiArgs, ApiResponse } from './fetch-staff-static-counts.type.js';

/**
 * @param args
* @returns
 */
export function fetchStaffStaticCounts(args: ApiArgs): Promise<EnhancedAxiosResponse< ApiResponse['data']>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
