---
description: 'A fetchStaffStaticCounts HTTP API service.'
labels: ['service', 'http']
---

查询用户档案对应岗位和楼栋数量

## Usage

### Browser

```ts
import { fetchStaffStaticCounts } from '@manyun/hrm.service.fetch-staff-static-counts';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchStaffStaticCountsService } from '@manyun/hrm.service.fetch-staff-static-counts/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchStaffStaticCountsService.from(nodeRequest);
```
