/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-6-9
 *
 * @packageDocumentation
 */
import type { BackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {
  /**
   * 用户id
   */
  userId?: number | null;
  /**
   * 直线经理
   */
  directorId?: number | null;
  /**
   * 楼栋列表
   */
  blockGuids?: string[] | null;
  /**
   * 岗位列表
   */
  positions?: string[] | null;
  /**
   * 是否在职
   */
  enable?: boolean | null;
};

export type StaticInfos = {
  type: string;
  value: string;
  count: number;
};

export type ApiResponseData = {
  /**
   * 楼栋
   */
  blocks: StaticInfos[];
  /**
   * 岗位
   */
  positions: StaticInfos[];
};

export type ApiResponse = BackendResponse<ApiResponseData>;
