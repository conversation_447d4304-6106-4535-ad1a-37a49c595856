/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import type { BackendMcUploadFile, McUploadFileJSON } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendDailyPerformanceGrade,
  DailyPerformanceGradeJSON,
} from '@manyun/hrm.model.daily-performance-grade';

export type SvcQuery = Pick<
  DailyPerformanceGradeJSON,
  | 'id'
  | 'type'
  | 'subType'
  | 'grade'
  | 'gradeDesc'
  | 'occurTime'
  | 'measurements'
  | 'relatedObjectiveId'
  | 'name'
  | 'gradeCriteria'
> & {
  staffId: number;
  staffName: string;
  attachments?: McUploadFileJSON[];
};

export type ApiQ = Pick<
  BackendDailyPerformanceGrade,
  | 'id'
  | 'type'
  | 'subType'
  | 'metrics'
  | 'grade'
  | 'occurTime'
  | 'content'
  | 'kpiId'
  | 'name'
  | 'gradeCriteria'
> & {
  staffId: number;
  staffName: string;
  files?: BackendMcUploadFile[];
};

export type ApiR = Response<number | null>;

export type SvcRespData = number | null;
