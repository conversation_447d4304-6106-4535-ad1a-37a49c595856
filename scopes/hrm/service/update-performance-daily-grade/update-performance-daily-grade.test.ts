/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-25
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { updatePerformanceDailyGrade as webService } from './update-performance-daily-grade.browser';
import { updatePerformanceDailyGrade as nodeService } from './update-performance-daily-grade.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
    id: 1,
    name: '',
    relatedObjectiveId: 0,
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
    id: 1,
    relatedObjectiveId: 0,
    name: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
    id: 1,
    relatedObjectiveId: 0,
    name: '',
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    type: 'DAILY',
    subType: 'BIZ',
    grade: 0,
    gradeDesc: '11',
    occurTime: 0,
    measurements: '11',
    staffId: 0,
    staffName: '2',
    id: 1,
    relatedObjectiveId: 0,
    name: '',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(false);
});
