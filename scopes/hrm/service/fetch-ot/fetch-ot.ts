/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-ot.type';

const endpoint = '/pm/overtimeWork/detail';

/**
 * 加班详情
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/9376)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { bizId: variant.bizId };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return { error, data: null, ...rest };
    }

    return {
      error,
      data: {
        title: data.title,
        apply: data.applyStaffId,
        status: data.status,
        bizId: data.bizId,
        idc: data.idcTag,
        blocks: data.blockGuids,
        startTime: data.startTime,
        endTime: data.endTime,
        totalTime: data.totalTime,
        creatorId: data.creatorId,
        preId: data.overtimePreId,
        confirId: data.overtimeConfirmId,
      },
      ...rest,
    };
  };
}
