/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-15
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { BackendOvertimeRecord, OvertimeRecord } from '@manyun/hrm.service.fetch-ot-data';

export type SvcQuery = {
  bizId: number | string;
};

export type SvcRespData = OvertimeRecord | null;

export type RequestRespData = BackendOvertimeRecord | null;

export type ApiQ = {
  bizId: number | string;
};

export type ApiR = Response<RequestRespData>;
