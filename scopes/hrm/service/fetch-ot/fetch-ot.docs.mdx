---
description: 'A fetchOt HTTP API service.'
labels: ['service', 'http', fetch-ot]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchOt } from '@manyun/hrm.service.fetch-ot';

const { error, data } = await fetchOt('success');
const { error, data } = await fetchOt('error');
```

### Node

```ts
import { fetchOt } from '@manyun/hrm.service.fetch-ot/dist/index.node';

const { data } = await fetchOt('success');

try {
  const { data } = await fetchOt('error');
} catch (error) {
  // ...
}
```
