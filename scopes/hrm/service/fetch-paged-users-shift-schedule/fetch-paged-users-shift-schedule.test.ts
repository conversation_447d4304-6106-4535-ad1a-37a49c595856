/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-20
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './fetch-paged-users-shift-schedule.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// import moment from 'moment';
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchPagedUsersShiftSchedule as webService } from './fetch-paged-users-shift-schedule.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    blockTag: '1',
    idcTag: 'sxdtyg',
    // scheduleDate: moment().format('YYYY-MM-DD'),
  });
  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({
    blockTag: '2',
    idcTag: 'sxdtyg',
    // scheduleDate: moment().format('YYYY-MM-DD'),
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
