/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-20
 *
 * @packageDocumentation
 */

export type BackendSchedule = {
  attGroupId?: number;
  attGroupName?: string;
  blockTag?: string;
  dutyGroupId?: number;
  dutyGroupName?: string;
  dutyId?: number;
  dutyName?: string;
  groupScheduleId?: number;
  groupScheduleUnicode?: string;
  id?: number;
  idcTag?: string;
  scheduleDate?: number;
  scheduleEndTime: number /**排班结束时间 */;
  scheduleScene?: string;
  scheduleStartTime: number /**排班开始时间 */;
  shiftsId?: number;
  staffId?: number;
  staffName?: string;
  timeInterval?: string;
  unicode?: string;
  groupScheduleStartTime?: number /**考勤组考勤开始时间 */;
  groupScheduleEndTime?: number /**考勤组考勤结束时间 */;
  allowRest?: boolean /**是否允许休息 */;
  restRange?: {
    startTime: string;
    endTime: string;
    startIsNextDay: boolean /**开始时间是否为次日 */;
    endIsNextDay: boolean /**结束时间是否为次日 */;
  } /** 休息范围*/;
  blockGuids: string[];
};

export type Schedule = BackendSchedule & {
  scheduleWorkMinute: number /**排班工作时长,单位：分钟 */;
  restStartTime?: number /**休息开始时间 */;
  restEndTime?: number /***休息结束时间 */;
  scheduleRestMinute?: number /**休息时长， 单位分钟 */;
};

export type SvcRespData = {
  data: Schedule[];
  total: number;
};

export type RequestRespData = {
  data: BackendSchedule[] | null;
  total: number;
};

export type SvcQuery = {
  attGroupId?: number;
  beginDate?: string;
  blockTag?: string;
  dutyGroupId?: number;
  endDate?: string;
  idcTag?: string;
  scheduleDate?: string;
  staffId?: number;
  dutyId?: number;
  needRestRange?: boolean;
  needOvertime?: boolean;
  haveToFuture?: boolean;
  notContainsProcess?: boolean;
};

export type ApiQ = {
  attGroupId?: number;
  startTime?: string;
  blockTag?: string;
  dutyGroupId?: number;
  endTime?: string;
  idcTag?: string;
  scheduleDate?: string;
  staffId?: number;
  dutyId?: number;
  /**是否需要休息范围 */
  needRestRange?: boolean;
  /**是否需要加班信息 */
  needOvertime?: boolean;
  /**是否必须为未来排班 */
  haveToFuture?: boolean;
  /**是否不包含审批中的排班：如果不包含则为true */
  notContainsProcess?: boolean;
};
