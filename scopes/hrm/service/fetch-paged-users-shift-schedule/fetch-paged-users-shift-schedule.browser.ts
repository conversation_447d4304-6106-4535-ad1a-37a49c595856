/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-20
 *
 * @packageDocumentation
 */
import dayjs from 'dayjs';

import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-paged-users-shift-schedule';
import type {
  ApiQ,
  RequestRespData,
  Schedule,
  SvcQuery,
  SvcRespData,
} from './fetch-paged-users-shift-schedule.type';

/**
 * 查询用户排班
 *
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1768)
 *
 * @param variant
 * @returns
 */
export async function fetchPagedUsersShiftSchedule(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = {
    attGroupId: query.attGroupId,
    blockTag: query.blockTag,
    dutyGroupId: query.dutyGroupId,
    idcTag: query.idcTag,
    scheduleDate: query.scheduleDate,
    staffId: query.staffId,
    startTime: query.beginDate,
    endTime: query.endDate,
    dutyId: query.dutyId,
    needRestRange: query.needRestRange,
    needOvertime: query.needOvertime,
    haveToFuture: query.haveToFuture,
    notContainsProcess: query.notContainsProcess,
  };

  const {
    error,
    data: { data, total },
    ...rest
  } = await webRequest.tryPost<RequestRespData, ApiQ>(endpoint, params);

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total: 0,
      },
    };
  }

  return {
    error,
    data: {
      data: data.map(backendSchedule => {
        let schedule: Schedule = {
          ...backendSchedule,
          scheduleWorkMinute: dayjs(backendSchedule.scheduleEndTime).diff(
            backendSchedule.scheduleStartTime,
            'minute'
          ),
        };
        /**排班休息时间段 */
        if (backendSchedule.allowRest && backendSchedule.restRange) {
          const startTimeStr = `${
            backendSchedule.restRange.startIsNextDay
              ? dayjs(backendSchedule.scheduleDate).add(1, 'day').format('YYYY-MM-DD')
              : dayjs(backendSchedule.scheduleDate).format('YYYY-MM-DD')
          }  ${backendSchedule.restRange.startTime}`;
          const endTimeStr = `${
            backendSchedule.restRange.endIsNextDay
              ? dayjs(backendSchedule.scheduleDate).add(1, 'day').format('YYYY-MM-DD')
              : dayjs(backendSchedule.scheduleDate).format('YYYY-MM-DD')
          } ${backendSchedule.restRange.endTime}`;

          schedule.restStartTime = dayjs(startTimeStr).valueOf();
          schedule.restEndTime = dayjs(endTimeStr).valueOf();
          schedule.scheduleRestMinute = dayjs(schedule.restEndTime).diff(
            dayjs(schedule.restStartTime),
            'minute'
          );
        }
        return schedule;
      }),
      total,
    },
    ...rest,
  };
}
