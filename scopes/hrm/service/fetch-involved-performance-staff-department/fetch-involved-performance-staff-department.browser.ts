/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import { request } from '@glpdev/symphony.services.request';
import type { EnhancedAxiosResponse } from '@glpdev/symphony.services.request';

import { getExecutor } from './fetch-involved-performance-staff-department.js';
import type { ApiArgs, ApiResponse } from './fetch-involved-performance-staff-department.type.js';

/**
 * @param args
 * @returns
 */
export function fetchInvolvedPerformanceStaffDepartment(
  args: ApiArgs
): Promise<EnhancedAxiosResponse<Pick<ApiResponse, 'data' | 'total'>>> {
  if (!request) {
    throw new Error('request instance expected.');
  }
  const executor = getExecutor(request);

  return executor(args);
}
