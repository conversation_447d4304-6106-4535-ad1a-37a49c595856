---
description: 'A fetchInvolvedPerformanceStaffDepartment HTTP API service.'
labels: ['service', 'http']
---

部门树查询

## Usage

### Browser

```ts
import { fetchInvolvedPerformanceStaffDepartment } from '@manyun/hrm.service.fetch-involved-performance-staff-department';
```

### Node

```ts
import { NodeRequest } from '@glpdev/symphony.services.request/index.node.js';
import { FetchInvolvedPerformanceStaffDepartmentService } from '@manyun/hrm.service.fetch-involved-performance-staff-department/index.node.js';

const nodeRequest = NodeRequest.from();
const nodeService = FetchInvolvedPerformanceStaffDepartmentService.from(nodeRequest);
```
