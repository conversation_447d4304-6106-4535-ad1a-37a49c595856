/**
 * <AUTHOR> <<EMAIL>>
 * @since 2025-5-7
 *
 * @packageDocumentation
 */
import type { ListBackendResponse } from '@glpdev/symphony.services.request';

export type ApiArgs = {};

export type DeptInfo = {
  /**
   * 父级id
   */
  parentId: string;
  /**
   * 部门id
   */
  id: string;
  /**
   * 子部门
   */
  children: DeptInfo[];
  /**
   * 部门id
   */
  deptId: string;
  /**
   * 中文名
   */
  nameZh: string;
  nameEng: string;
  channel: string;
  status: string;
  /**
   * 全称
   */
  fullDeptName: string;
  /**
   * 部门路径
   */
  deptPath: string;
};

export type ApiResponse = ListBackendResponse<DeptInfo[]>;
