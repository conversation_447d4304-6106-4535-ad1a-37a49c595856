/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { deleteUserCertificate as webService } from './delete-user-certificate.browser';
import { deleteUserCertificate as nodeService } from './delete-user-certificate.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ userId: 0, id: 0 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ userId: 0, id: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ userId: 0, id: 0 });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ userId: 0, id: 0 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
