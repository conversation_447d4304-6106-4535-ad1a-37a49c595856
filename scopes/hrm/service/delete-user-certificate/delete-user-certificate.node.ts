/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './delete-user-certificate';
import type { SvcQuery, SvcRespData } from './delete-user-certificate.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function deleteUserCertificate(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
