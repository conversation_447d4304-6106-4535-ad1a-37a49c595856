/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './delete-user-certificate.type';

const endpoint = '/pm/cert/delete';

/**
 * 删除用户证书
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/18870)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    // const params: ApiQ = { variant };

    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, variant);

    return { error, data, ...rest };
  };
}
