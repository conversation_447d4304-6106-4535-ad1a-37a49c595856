/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

const endpoint = '/pm/pf/positionList';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/22683)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<string[]>> => {
    const { error, data, ...rest } = await request.tryPost<{
      data: string[] | null;
      total: number;
    }>(endpoint, {});

    if (error || data.data === null || data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data, ...rest };
  };
}
