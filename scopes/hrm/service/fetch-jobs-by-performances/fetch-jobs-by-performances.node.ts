/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-jobs-by-performances';

const executor = getExecutor(nodeRequest);

/**
 * @param
 * @returns
 */
export function fetchJobsByPerformances(): Promise<EnhancedAxiosResponse<string[]>> {
  return executor();
}
