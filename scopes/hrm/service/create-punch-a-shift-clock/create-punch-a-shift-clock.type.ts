/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { BackendMcUploadFile, McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import { ClockInJSON, PunchType } from '@manyun/hrm.model.clock-in';

export type SvcQuery = {
  type: PunchType;
  checkTime: number;
  reason: string;
  staffId: number;
  punchChannel?: string;
  attachments?: McUploadFile[];
};

export type SvcRespData = ClockInJSON | null;

export type ApiQ = {
  checkType: PunchType;
  checkTime: number;
  reason: string;
  staffId: number;
  checkChannel?: string;
  files?: BackendMcUploadFile[];
};

export type ApiR = Response<string>;
