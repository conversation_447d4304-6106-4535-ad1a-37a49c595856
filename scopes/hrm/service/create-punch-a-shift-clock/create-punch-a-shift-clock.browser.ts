/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './create-punch-a-shift-clock';
import type { SvcQuery, SvcRespData } from './create-punch-a-shift-clock.type';

const executor = getExecutor(webRequest);

/**
 * @param createD
 * @returns
 */
export function createPunchAShiftClock(
  createD: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(createD);
}
