---
description: 'A createPunchAShiftClock HTTP API service.'
labels: ['service', 'http', create-punch-a-shift-clock]
---

## 概述

## 使用

补卡

### Browser

```ts
import { createPunchAShiftClock } from '@manyun/hrm.service.create-punch-a-shift-clock';

const { error, data } = await createPunchAShiftClock({
    type: 'ON',
    checkTime: 1670391870,
    staffId: 1,
    reason: '',
    punchChannel: 'LOCAl',
  });
const { error, data } = await createPunchAShiftClock({
    type: 'ON',
    checkTime: 1670391870,
    staffId: 1,
    reason: '',
    punchChannel: 'LOCAl',
  });
```
