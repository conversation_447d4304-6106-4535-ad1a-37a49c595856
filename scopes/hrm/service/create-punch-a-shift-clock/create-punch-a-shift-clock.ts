/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { BackendClockIn, ClockIn } from '@manyun/hrm.model.clock-in';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './create-punch-a-shift-clock.type';

const endpoint = '/pm/check/supplyCheck';

const detailEndPoint = '/pm/check/checkDetail';

/**
 * 补卡、打卡
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1232)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (createD: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      checkTime: createD.checkTime,
      checkType: createD.type,
      staffId: createD.staffId,
      reason: createD.reason,
      checkChannel: createD.punchChannel,
      files: createD.attachments?.map(obj => McUploadFile.toApiObject(obj)),
    };

    const { error, data, ...rest } = await request.tryPost<string, ApiQ>(endpoint, params);

    if (data) {
      const detail = await request.tryPost<BackendClockIn | null, { bizId: string }>(
        detailEndPoint,
        {
          bizId: data,
        }
      );
      if (detail.error || detail.data === null) {
        return { error, data: null, ...rest };
      }
      return { error, data: ClockIn.fromApiObject(detail.data).toJSON(), ...rest };
    }

    return { error, data: null, ...rest };
  };
}
