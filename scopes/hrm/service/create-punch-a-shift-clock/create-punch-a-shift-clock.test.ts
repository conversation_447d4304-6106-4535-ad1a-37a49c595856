/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { createPunchAShiftClock as webService } from './create-punch-a-shift-clock.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    checkTime: 1670386320000,
    type: 'ON',
    staffId: 1,
    reason: '8888',
    punchChannel: 'LOCAl',
  });
  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    type: 'ON',
    checkTime: 1670391870,
    staffId: 1,
    reason: '123',
    punchChannel: 'LOCAl',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
