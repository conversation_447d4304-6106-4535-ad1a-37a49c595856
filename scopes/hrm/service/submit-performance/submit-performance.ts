/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery } from './submit-performance.type';

const endpoint = '/pm/pf/saveAndSubmit';

/**
 * @see [Doc](http://172.16.0.17:13000/project/216/interface/api/23070)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<number | null>> => {
    const params: ApiQ = {
      staffId: svcQuery.userId,
      infoSubType: svcQuery.type,
      superiorIds: svcQuery.superiorIds,
      id: svcQuery.id,
      hiredDate: svcQuery.hiredDate,
      sections: svcQuery.goals.map(goal => ({
        sectionType: goal.type,
        title: goal.title,
        radio: goal.percent,
        metrics: goal.measurements,
        sectionStatus: goal.status,
        startTime: goal.startedAt,
        finishTime: goal.finishedAt,
        selfEvaluation: goal.selfEvaluation,
        grade: goal.supervisorRate,
        content: goal.content,
        id: goal.id,
      })),
      infoSelfEvaluation: svcQuery.selfEvaluation?.trim(),
    };

    return await request.tryPost<number | null, ApiQ>(endpoint, params);
  };
}
