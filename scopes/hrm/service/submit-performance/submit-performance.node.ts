/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './submit-performance';
import type { SvcQuery } from './submit-performance.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function submitPerformance(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<number | null>> {
  return executor(svcQuery);
}
