/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-12
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';
import type { BackendPerformanceSubType } from '@manyun/hrm.model.performance';
import type {
  BackendGrade,
  BackendPerformanceObjective,
  PerformanceObjectiveJSON,
} from '@manyun/hrm.model.performance-objective';

export type SimpleGoalJSON = Omit<
  PerformanceObjectiveJSON,
  'id' | 'selfEvaluation' | 'grade' | 'createdAt' | 'gradeDescriptions'
> & {
  /**
   * 绩效id, 新增时为空
   */
  id?: number;
  selfEvaluation?: string;
  supervisorRate?: BackendGrade;
};

export type SvcQuery = {
  userId: number;
  type: BackendPerformanceSubType;
  superiorIds: number[];
  goals: SimpleGoalJSON[];
  id?: number;
  selfEvaluation?: string;
  hiredDate: number;
};

export type ApiQ = {
  staffId: number;
  infoSubType: BackendPerformanceSubType;
  superiorIds: number[];
  sections: (Omit<
    BackendPerformanceObjective,
    'id' | 'gmtCreate' | 'gmtModified' | 'grade' | 'selfEvaluation' | 'gradeDescriptions'
  > & {
    id?: number;
    grade?: BackendGrade;
    selfEvaluation?: string;
  })[];
  id?: number;
  infoSelfEvaluation?: string;
  hiredDate: number;
};

export type SvcRespData = number | null;

export type RequestRespData = number | null;

export type ApiR = Response<RequestRespData>;
