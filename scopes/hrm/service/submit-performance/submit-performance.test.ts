/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-17
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { submitPerformance as webService } from './submit-performance.browser';
import { submitPerformance as nodeService } from './submit-performance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    userId: 1,
    type: 'TARGET',
    superiorIds: [],
    hiredDate: 1,
    goals: [],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(1);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    userId: 0,
    type: 'TARGET',
    superiorIds: [],
    hiredDate: 1,
    goals: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({
    userId: 1,
    type: 'TARGET',
    superiorIds: [],
    hiredDate: 1,
    goals: [],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(1);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({
    userId: 0,
    type: 'TARGET',
    superiorIds: [],
    hiredDate: 1,
    goals: [],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
