/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  type?: string;
  staffId?: number;
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum BALANCE_UNIT {
  DAY = 'DAY',
  HOUR = 'HOUR',
  NATURAL_DAT = 'NATURAL_DAY',
}

export const BALANCE_UNIT_MAP: Record<string, string> = {
  [BALANCE_UNIT.DAY]: '天',
  [BALANCE_UNIT.HOUR]: '小时',
  [BALANCE_UNIT.NATURAL_DAT]: '自然日',
};

// eslint-disable-next-line @typescript-eslint/naming-convention
export enum BALANCE_TYPE {
  PAID_LEAVE = 'PAID_LEAVE', //调休
  SICK_SALARY = 'SICK_SALARY', //带薪病假
  COMPANY_LEAVE = 'COMPANY_LEAVE', //公司年假
  STATUTORY_ANNUAL_LEAVE = 'STATUTORY_ANNUAL_LEAVE', //法定年假
  CARRY_OVER_COMPANY_LEAVE = 'CARRY_OVER_COMPANY_LEAVE', //上年结转年假
}
// 注： 年假 = 公司年假 + 法定年假

export type BackendLeaveBalance = {
  balanceType: BALANCE_TYPE;
  balance: number /**总余额 */;
  availableBalance: number /**可用余额 */;
  unit: BALANCE_UNIT;
};

export type LeaveBalance = {
  type: BALANCE_TYPE;
  balance: number /**可用余额 */;
  totalBalance?: number /**总余额 */;
  unit: BALANCE_UNIT;
  unitName: string;
};

export type SvcRespData = LeaveBalance[];

export type RequestRespData = {
  data: BackendLeaveBalance[] | null;
  total: number;
} | null;

export type ApiQ = {
  balanceType?: string;
  staffId?: number;
};

export type ApiR = ListResponse<BackendLeaveBalance[] | null>;
