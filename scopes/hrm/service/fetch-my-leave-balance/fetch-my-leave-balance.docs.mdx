---
description: 'A fetchMyLeaveBalance HTTP API service.'
labels: ['service', 'http', fetch-my-leave-balance]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchMyLeaveBalance } from '@manyun/hrm.service.fetch-my-leave-balance';

const { error, data } = await fetchMyLeaveBalance('success');
const { error, data } = await fetchMyLeaveBalance('error');
```

### Node

```ts
import { fetchMyLeaveBalance } from '@manyun/hrm.service.fetch-my-leave-balance/dist/index.node';

const { data } = await fetchMyLeaveBalance('success');

try {
  const { data } = await fetchMyLeaveBalance('error');
} catch (error) {
  // ...
}
```
