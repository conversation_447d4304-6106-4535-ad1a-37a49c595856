/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-11
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import { BALANCE_UNIT_MAP } from './fetch-my-leave-balance.type';
import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-my-leave-balance.type';

const endpoint = '/dctrans/balance/query';

/**
 * 请假时查询假期余额
 * @see [Doc](http://172.16.0.17:13000/project/124/interface/api/9456)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { balanceType: variant?.type, staffId: variant?.staffId };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return {
      error,
      data: data.data.map(d => ({
        type: d.balanceType,
        balance: d.availableBalance,
        totalBalance: d.balance,
        unit: d.unit,
        unitName: BALANCE_UNIT_MAP[d.unit],
      })),
      ...rest,
    };
  };
}
