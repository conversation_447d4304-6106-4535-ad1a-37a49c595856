/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './export-holiday-balance.type';

const endpoint = '/dctrans/balance/export/list';

/**
 * 导出余额
 * @see [Doc](YAPI http://172.16.0.17:13000/project/124/interface/api/18232)
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      staffId: variant?.staffId,
      maintenanceFlag: variant?.onlyMaintenanceUser,
      userShiftsList: variant?.userShifts,
      blockGuidList: variant?.blockGuids,
      yearDate: variant.yearDate,
      onJob: variant.userState ? (variant.userState === 'in-service' ? true : false) : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      params,
      {
        responseType: 'blob',
      }
    );

    return { error, data, ...rest };
  };
}
