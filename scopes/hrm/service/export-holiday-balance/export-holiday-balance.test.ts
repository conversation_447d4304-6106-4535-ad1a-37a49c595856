/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportHolidayBalance as webService } from './export-holiday-balance.browser';
import { exportHolidayBalance as nodeService } from './export-holiday-balance.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService();

  expect(error).toBe(undefined);
});
