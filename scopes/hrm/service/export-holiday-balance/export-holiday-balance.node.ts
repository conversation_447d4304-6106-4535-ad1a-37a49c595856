/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './export-holiday-balance';
import type { SvcQuery, SvcRespData } from './export-holiday-balance.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function exportHolidayBalance(
  variant: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
