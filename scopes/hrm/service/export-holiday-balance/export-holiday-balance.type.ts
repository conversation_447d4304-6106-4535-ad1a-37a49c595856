/**
 * <AUTHOR>
 * @since 2022-6-15
 *
 * @packageDocumentation
 */
import type { UserShifts } from '@manyun/auth-hub.model.user';

export type SvcQuery = {
  staffId?: string;
  onlyMaintenanceUser?: boolean;
  blockGuids?: string[];
  userShifts: UserShifts[];
  yearDate?: number;
  userState?: string;
};

export type SvcRespData = Blob;

export type RequestRespData = Blob;

export type ApiQ = {
  staffId?: string;
  maintenanceFlag?: boolean;
  blockGuidList?: string[];
  userShiftsList: UserShifts[];
  yearDate?: number;
  onJob?: boolean;
};
