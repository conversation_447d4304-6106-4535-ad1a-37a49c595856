/**
 * <AUTHOR>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './revoke-alter-request';
import type { SvcQuery, SvcRespData } from './revoke-alter-request.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function revokeAlterRequest(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
