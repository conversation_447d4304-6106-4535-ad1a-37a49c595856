/**
 * <AUTHOR>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { revokeAlterRequest as webService } from './revoke-alter-request.browser';
import { revokeAlterRequest as nodeService } from './revoke-alter-request.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await webService({
    bizId: '1',
    type: 'REST',
  });

  expect(error).toBe(undefined);
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({
    bizId: '-1',
    type: 'REST',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  const { error } = await nodeService({
    bizId: '1',
    type: 'REST',
  });

  expect(error).toBe(undefined);
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({
    bizId: '-1',
    type: 'REST',
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
