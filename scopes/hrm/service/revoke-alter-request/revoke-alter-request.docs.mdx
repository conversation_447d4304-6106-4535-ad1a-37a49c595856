---
description: 'A revokeAlterRequest HTTP API service.'
labels: ['service', 'http', revoke-alter-request]
---

## 概述

撤销调班实例 【包含换班、补卡、顶班、请假】

## 使用

### Browser

```ts
import { revokeAlterRequest } from '@manyun/hrm.service.revoke-alter-request';

const { error, data } = await revokeAlterRequest({
  bizId: '1',
  type: 'REST',
});
```

### Node

```ts
import { revokeAlterRequest } from '@manyun/hrm.service.revoke-alter-request/dist/index.node';

const { data } = await revokeAlterRequest({
  bizId: '1',
  type: 'REST',
});
```
