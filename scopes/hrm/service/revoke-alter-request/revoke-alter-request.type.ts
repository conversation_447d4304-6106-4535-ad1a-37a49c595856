/**
 * <AUTHOR>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';
import { ShiftAdjustmentTicketType } from '@manyun/hrm.model.shift-adjustment-ticket';

export type AlterType = ShiftAdjustmentTicketType | /**补卡**/ 'SUPPLY';

export type SvcQuery = {
  bizId: string; //业务id
  type: AlterType;
  reason?: string; //理由
};

export type SvcRespData = boolean;

export type ApiQ = SvcQuery;

export type ApiR = WriteResponse;
