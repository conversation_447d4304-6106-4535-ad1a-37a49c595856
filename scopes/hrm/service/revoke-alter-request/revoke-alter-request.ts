/**
 * <AUTHOR>
 * @since 2022-7-12
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './revoke-alter-request.type';

const endpoint = '/pm/check/cancelAlterProcess';

/**
 * 撤销调班实例
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1160)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = variant;

    return await request.tryPost<boolean, ApiQ>(endpoint, params);
  };
}
