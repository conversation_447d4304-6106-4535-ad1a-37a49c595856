import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  QueryCustomerWhiteListArgs,
  SingleCustomerWhiteList,
} from '../generated-types/graphql';

export type QueryCustomerWhiteListData = {
  customerWhiteList: Maybe<{
    data: SingleCustomerWhiteList[];
    total: number;
  }>;
};

export const GET_CUSTOMER_WHITE_LIST: DocumentNode = gql`
  query CustomerWhiteList(
    $pageSize: Long!
    $pageNum: Long!
    $phoneNo: String
    $username: String
    $company: String
    $email: String
    $userStatus: String
    $startDate: Long
    $blockGuidList: [String!]
    $endDate: Long
  ) {
    customerWhiteList(
      pageSize: $pageSize
      pageNum: $pageNum
      phoneNo: $phoneNo
      username: $username
      company: $company
      email: $email
      userStatus: $userStatus
      startDate: $startDate
      endDate: $endDate
      blockGuidList: $blockGuidList
    ) {
      data {
        id
        company {
          name
          value
        }
        createTime
        modifier {
          id
          name
        }
        modifiedTime
        spaceGuidList
        approvingSpaceGuidList
        userName
        mobile
        email
        position
        creator {
          id
          name
        }
      }
      total
    }
  }
`;

export function useCustomerWhiteList(
  options?: QueryHookOptions<QueryCustomerWhiteListData, QueryCustomerWhiteListArgs>
): QueryResult<QueryCustomerWhiteListData, QueryCustomerWhiteListArgs> {
  return useQuery(GET_CUSTOMER_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyCustomerWhiteList(
  options?: LazyQueryHookOptions<QueryCustomerWhiteListData, QueryCustomerWhiteListArgs>
): LazyQueryResultTuple<QueryCustomerWhiteListData, QueryCustomerWhiteListArgs> {
  return useLazyQuery(GET_CUSTOMER_WHITE_LIST, { fetchPolicy: 'network-only', ...options });
}
