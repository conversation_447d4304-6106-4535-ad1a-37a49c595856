import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type {
  Maybe,
  QueryCustomerWhiteListDetailArgs,
  SingleCustomerWhiteList,
} from '../generated-types/graphql';

export type QueryCustomerWhiteListDetailData = {
  customerWhiteListDetail: Maybe<{
    data: SingleCustomerWhiteList;
  }>;
};

export const GET_CUSTOMER_WHITE_LIST_DETAIL: DocumentNode = gql`
  query CustomerWhiteListDetail($id: Long!) {
    customerWhiteListDetail(id: $id) {
      data {
        id
        company {
          name
          value
        }
        createTime
        creator {
          id
          name
        }
        modifier {
          id
          name
        }
        modifiedTime
        userName
        mobile
        email
        type
        approvingSpaceDetails {
          id
          blockGuid
          status
          approvalId
          description
          creator {
            id
            name
          }
          createTime
        }
      }
    }
  }
`;

export function useCustomerWhiteListDetail(
  options?: QueryHookOptions<QueryCustomerWhiteListDetailData, QueryCustomerWhiteListDetailArgs>
): QueryResult<QueryCustomerWhiteListDetailData, QueryCustomerWhiteListDetailArgs> {
  return useQuery(GET_CUSTOMER_WHITE_LIST_DETAIL, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyCustomerWhiteListDetail(
  options?: LazyQueryHookOptions<QueryCustomerWhiteListDetailData, QueryCustomerWhiteListDetailArgs>
): LazyQueryResultTuple<QueryCustomerWhiteListDetailData, QueryCustomerWhiteListDetailArgs> {
  return useLazyQuery(GET_CUSTOMER_WHITE_LIST_DETAIL, { fetchPolicy: 'network-only', ...options });
}
