import { gql, useLazyQuery, useQuery } from '@apollo/client';
import type {
  DocumentNode,
  LazyQueryHookOptions,
  LazyQueryResultTuple,
  QueryHookOptions,
  QueryResult,
} from '@apollo/client';

import type { Query, QueryCustomerWhiteStaffArgs } from '../generated-types/graphql';

export type QueryWhiteCustomerStaffResult = Pick<Query, 'customerWhiteStaff'>;

export const GET_CUSTOMER_WHITE_STAFF: DocumentNode = gql`
  query CustomerWhiteStaff($phoneNo: String!) {
    customerWhiteStaff(phoneNo: $phoneNo) {
      data {
        id
        type
        company {
          name
          value
        }
        userName
        mobile
        email
        position
      }
    }
  }
`;

export function useCustomerWhiteStaff(
  options?: QueryHookOptions<QueryWhiteCustomerStaffResult, QueryCustomerWhiteStaffArgs>
): QueryResult<QueryWhiteCustomerStaffResult, QueryCustomerWhiteStaffArgs> {
  return useQuery(GET_CUSTOMER_WHITE_STAFF, {
    fetchPolicy: 'network-only',
    ...options,
  });
}

export function useLazyCustomerWhiteStaff(
  options?: LazyQueryHookOptions<QueryWhiteCustomerStaffResult, QueryCustomerWhiteStaffArgs>
): LazyQueryResultTuple<QueryWhiteCustomerStaffResult, QueryCustomerWhiteStaffArgs> {
  return useLazyQuery(GET_CUSTOMER_WHITE_STAFF, { fetchPolicy: 'network-only', ...options });
}
