/* eslint-disable */
// @ts-nocheck
export type Maybe<T> = T | null;
export type InputMaybe<T> = Maybe<T>;
export type Exact<T extends { [key: string]: unknown }> = { [K in keyof T]: T[K] };
export type MakeOptional<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]?: Maybe<T[SubKey]> };
export type MakeMaybe<T, K extends keyof T> = Omit<T, K> & { [SubKey in K]: Maybe<T[SubKey]> };
export type MakeEmpty<T extends { [key: string]: unknown }, K extends keyof T> = { [_ in K]?: never };
export type Incremental<T> = T | { [P in keyof T]?: P extends ' $fragmentName' | '__typename' ? T[P] : never };
/** All built-in and custom scalars, mapped to their actual values */
export type Scalars = {
  ID: { input: string; output: string; }
  String: { input: string; output: string; }
  Boolean: { input: boolean; output: boolean; }
  Int: { input: number; output: number; }
  Float: { input: number; output: number; }
  Any: { input: any; output: any; }
  /** Date custom scalar type */
  Date: { input: any; output: any; }
  Double: { input: any; output: any; }
  /** The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSON: { input: any; output: any; }
  /** The `JSONObject` scalar type represents JSON objects as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf). */
  JSONObject: { input: any; output: any; }
  /** The `Long` scalar type represents 52-bit integers */
  Long: { input: any; output: any; }
  /** The `Upload` scalar type represents a file upload. */
  Upload: { input: any; output: any; }
};

export type AccountRelateRackTicket = {
  __typename?: 'AccountRelateRackTicket';
  checkStatus: RackTicketCheckStatus;
  gridGuid: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  powerOnTime: Scalars['String']['output'];
  taskNo: Scalars['String']['output'];
  taskType: RackTicketTaskType;
};

export type AccountRelateRackTicketsResponse = {
  __typename?: 'AccountRelateRackTicketsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Array<AccountRelateRackTicket>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total?: Maybe<Scalars['Long']['output']>;
};

export type AddedService = {
  id?: InputMaybe<Scalars['Long']['input']>;
  quotationAmount?: InputMaybe<Scalars['Float']['input']>;
  serviceContent?: InputMaybe<Scalars['String']['input']>;
  serviceRemark?: InputMaybe<Scalars['String']['input']>;
  serviceType: ServiceType;
};

export type AdjustFeeResponse = MutationResponse & {
  __typename?: 'AdjustFeeResponse';
  approvalId?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type AdjustFeesResponse = {
  __typename?: 'AdjustFeesResponse';
  data?: Maybe<Array<AdjustSecondFee>>;
};

export type AdjustInfo = {
  __typename?: 'AdjustInfo';
  adjustDirection: Scalars['Boolean']['output'];
  adjustItemType?: Maybe<Scalars['String']['output']>;
  adjustNo: Scalars['String']['output'];
  adjustReason?: Maybe<Scalars['String']['output']>;
  adjustResourceType?: Maybe<Scalars['String']['output']>;
  adjustSource: Scalars['Int']['output'];
  adjustType: Scalars['String']['output'];
  adjustTypeName: Scalars['String']['output'];
  adjustValue: Scalars['String']['output'];
  applyStaffId: Scalars['String']['output'];
  applyStaffName: Scalars['String']['output'];
  batchAdjust: Scalars['Boolean']['output'];
  batchAdjustNum?: Maybe<Scalars['Int']['output']>;
  createAt: Scalars['Long']['output'];
  id: Scalars['Int']['output'];
  instStatus: Scalars['String']['output'];
  originValue: Scalars['String']['output'];
  procInstanceId: Scalars['String']['output'];
  resourceNo?: Maybe<Scalars['String']['output']>;
};

export type AdjustObjectRequire = {
  adjustId: Scalars['Int']['input'];
  adjustResourceType?: InputMaybe<Scalars['String']['input']>;
  adjustUseAmount: Scalars['Float']['input'];
  adjustValue: Scalars['String']['input'];
  originValue: Scalars['String']['input'];
  resourceNo: Scalars['String']['input'];
};

export type AdjustSecondFee = {
  __typename?: 'AdjustSecondFee';
  adjustInfoList: Array<AdjustInfo>;
  feeCode: Scalars['String']['output'];
  feeId: Scalars['Int']['output'];
  feeName: Scalars['String']['output'];
  newlyAdd: Scalars['Boolean']['output'];
};

export type AggBlock = {
  __typename?: 'AggBlock';
  blockGuidList: Array<Maybe<Scalars['String']['output']>>;
  scopeFlag?: Maybe<Scalars['String']['output']>;
  scopeFlagInfoList?: Maybe<Array<ScopeFlagInfo>>;
  scopeType: Scalars['String']['output'];
};

export type AlarmCountByBiz = {
  __typename?: 'AlarmCountByBiz';
  RECOVER?: Maybe<Scalars['Long']['output']>;
  TRIGGER?: Maybe<Scalars['Long']['output']>;
  totalCount?: Maybe<Scalars['Long']['output']>;
};

export type AlarmCountByBizQ = {
  bizId: Scalars['String']['input'];
  bizType: Scalars['String']['input'];
  idcTag: Scalars['String']['input'];
};

export type AlarmCountByBizResponse = {
  __typename?: 'AlarmCountByBizResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<AlarmCountByBiz>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type AlarmItem = {
  __typename?: 'AlarmItem';
  alarmLevel?: Maybe<Scalars['Long']['output']>;
  alarmReason?: Maybe<Scalars['String']['output']>;
  alarmStatus?: Maybe<Type>;
  alarmType?: Maybe<Type>;
  blockTag?: Maybe<Scalars['String']['output']>;
  columnTag?: Maybe<Scalars['String']['output']>;
  confirmBy?: Maybe<Scalars['Long']['output']>;
  confirmByName?: Maybe<Scalars['String']['output']>;
  customerCount?: Maybe<Scalars['Long']['output']>;
  dataType?: Maybe<Type>;
  deviceCount?: Maybe<Scalars['Long']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  eventId?: Maybe<Scalars['String']['output']>;
  expectStatus?: Maybe<Scalars['String']['output']>;
  gmtCreate?: Maybe<Scalars['String']['output']>;
  gridCount?: Maybe<Scalars['Long']['output']>;
  gridTag?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  itemId?: Maybe<Scalars['Long']['output']>;
  mergeCount?: Maybe<Scalars['Long']['output']>;
  mergeRuleId?: Maybe<Scalars['Long']['output']>;
  notifyContent?: Maybe<Scalars['String']['output']>;
  notifyCount?: Maybe<Scalars['Long']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointCodeName?: Maybe<Scalars['String']['output']>;
  pointValue?: Maybe<Scalars['Long']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  threshold?: Maybe<Scalars['String']['output']>;
  triggerContion?: Maybe<Type>;
  triggerGuid?: Maybe<Scalars['String']['output']>;
  triggerSnapshot?: Maybe<Scalars['String']['output']>;
  triggerStatus?: Maybe<Type>;
  triggerTime?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
  validLimits?: Maybe<Scalars['JSONObject']['output']>;
};

export type AlarmListResponse = {
  __typename?: 'AlarmListResponse';
  alarmList?: Maybe<Array<Maybe<AlarmItem>>>;
  pageNum?: Maybe<Scalars['Long']['output']>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type AlarmShield = {
  __typename?: 'AlarmShield';
  blockGuid: Scalars['String']['output'];
  effectStatus: AlarmShieldStatus;
  enable: Scalars['Boolean']['output'];
  endTime: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  gmtModified?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
  operatorId: Scalars['Long']['output'];
  operatorName: Scalars['String']['output'];
  startTime: Scalars['String']['output'];
};

export type AlarmShieldDetail = {
  __typename?: 'AlarmShieldDetail';
  blockGuid: Scalars['String']['output'];
  deviceShieldScopes?: Maybe<Array<Maybe<ShieldScope>>>;
  effectStatus: AlarmShieldStatus;
  enable: Scalars['Boolean']['output'];
  endTime: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  gmtModified?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
  operatorId: Scalars['Long']['output'];
  operatorName: Scalars['String']['output'];
  pointShieldScopes?: Maybe<Array<Maybe<ShieldScope>>>;
  spaceShieldScopes?: Maybe<Array<Maybe<ShieldScope>>>;
  startTime: Scalars['String']['output'];
};

export type AlarmShieldStatus = {
  __typename?: 'AlarmShieldStatus';
  code: AlarmShieldStatusCode;
  name: Scalars['String']['output'];
};

export enum AlarmShieldStatusCode {
  Active = 'ACTIVE',
  Expired = 'EXPIRED',
  Pending = 'PENDING'
}

export type AlarmShieldsResponse = {
  __typename?: 'AlarmShieldsResponse';
  data?: Maybe<Array<AlarmShield>>;
  total: Scalars['Long']['output'];
};

export type AnnualPerformanceCompletedProcess = {
  __typename?: 'AnnualPerformanceCompletedProcess';
  firHalfYearFinishedPercent?: Maybe<Scalars['Float']['output']>;
  hrs?: Maybe<Array<SimpleUser>>;
  idc: SimpleSpace;
  q1FinishedPercent?: Maybe<Scalars['Float']['output']>;
  q2FinishedPercent?: Maybe<Scalars['Float']['output']>;
  q3FinishedPercent?: Maybe<Scalars['Float']['output']>;
  q4FinishedPercent?: Maybe<Scalars['Float']['output']>;
  region: SimpleSpace;
  regionPICs?: Maybe<Array<SimpleUser>>;
  secondHalfYearFinishedPercent?: Maybe<Scalars['Float']['output']>;
  targetFinishedPercent?: Maybe<Scalars['Float']['output']>;
  year: Scalars['String']['output'];
  yearFinishedPercent?: Maybe<Scalars['Float']['output']>;
};

export type AnnualPerformanceObjective = {
  __typename?: 'AnnualPerformanceObjective';
  createdAt: Scalars['Float']['output'];
  createdBy: SimpleUser;
  gradeCriteria?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  measurements?: Maybe<Array<Scalars['String']['output']>>;
  modifiedAt?: Maybe<Scalars['Float']['output']>;
  modifiedBy: SimpleUser;
  name: Scalars['String']['output'];
  positions?: Maybe<Array<PositionLabelInValue>>;
  processInstanceCode?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  resources?: Maybe<Array<Maybe<SpaceLabelInValue>>>;
  rowKey: Scalars['String']['output'];
  status?: Maybe<Scalars['String']['output']>;
  subType: Scalars['String']['output'];
  type: Scalars['String']['output'];
  way: Scalars['String']['output'];
};

export type AnnualPerformanceObjectivesByPlanQuery = {
  performancePosition?: InputMaybe<Scalars['String']['input']>;
  planTime: Scalars['Float']['input'];
  staffId: Scalars['Float']['input'];
  subType?: InputMaybe<BackendAnnualPerformanceObjectiveSubType>;
  type?: InputMaybe<BackendAnnualPerformanceObjectiveType>;
};

export type AnnualPerformanceStatistics = {
  __typename?: 'AnnualPerformanceStatistics';
  data?: Maybe<Array<AnnualPerformanceCompletedProcess>>;
  hrbps?: Maybe<Array<SimpleUser>>;
  territoryProcessRecords?: Maybe<Array<AnnualPerformanceTerritoryProgressDetail>>;
  usersGradeIntervalRecords?: Maybe<Array<AnnualPerformanceUsersGradeInterval>>;
};

export type AnnualPerformanceStatisticsQueryArgs = {
  gradeIntervalIdc?: InputMaybe<Scalars['String']['input']>;
  hrIds?: InputMaybe<Array<Scalars['Float']['input']>>;
  idcTags?: InputMaybe<Array<Scalars['String']['input']>>;
  period?: InputMaybe<Scalars['String']['input']>;
  regionTags?: InputMaybe<Array<Scalars['String']['input']>>;
  year: Scalars['String']['input'];
};

export type AnnualPerformanceTerritoryProgressDetail = {
  __typename?: 'AnnualPerformanceTerritoryProgressDetail';
  finishedCount?: Maybe<Scalars['Float']['output']>;
  idc?: Maybe<SimpleSpace>;
  period: Scalars['String']['output'];
  totalCount?: Maybe<Scalars['Float']['output']>;
  unFinishedCount?: Maybe<Scalars['Float']['output']>;
};

export type AnnualPerformanceUsersGradeInterval = {
  __typename?: 'AnnualPerformanceUsersGradeInterval';
  interval: Scalars['String']['output'];
  number?: Maybe<Scalars['Float']['output']>;
  percent?: Maybe<Scalars['String']['output']>;
};

export type AnnualPerformanceUsersGrades = {
  __typename?: 'AnnualPerformanceUsersGrades';
  blockGuid?: Maybe<Scalars['String']['output']>;
  evaluationJob: EvaluationJob;
  hrs: Array<SimpleUser>;
  idc: SimpleSpace;
  lineManagers: Array<SimpleUser>;
  q1Finished?: Maybe<Scalars['Boolean']['output']>;
  q1Grade?: Maybe<Scalars['Float']['output']>;
  q1Id?: Maybe<Scalars['Float']['output']>;
  q2Finished?: Maybe<Scalars['Boolean']['output']>;
  q2Grade?: Maybe<Scalars['Float']['output']>;
  q2Id?: Maybe<Scalars['Float']['output']>;
  q3Finished?: Maybe<Scalars['Boolean']['output']>;
  q3Grade?: Maybe<Scalars['Float']['output']>;
  q3Id?: Maybe<Scalars['Float']['output']>;
  q4Finished?: Maybe<Scalars['Boolean']['output']>;
  q4Grade?: Maybe<Scalars['Float']['output']>;
  q4Id?: Maybe<Scalars['Float']['output']>;
  result?: Maybe<Scalars['Int']['output']>;
  secondLineManagers: Array<SimpleUser>;
  user: PerformanceUser;
  year: Scalars['String']['output'];
  yearFinished?: Maybe<Scalars['Boolean']['output']>;
  yearGrade?: Maybe<Scalars['Float']['output']>;
  yearId?: Maybe<Scalars['Float']['output']>;
};

export type AnnualPerformanceUsersStatistics = {
  __typename?: 'AnnualPerformanceUsersStatistics';
  data?: Maybe<Array<AnnualPerformanceUsersGrades>>;
  hrbps?: Maybe<Array<SimpleUser>>;
};

export type AnnualPerformanceUsersStatisticsQueryArgs = {
  blockGuids?: InputMaybe<Array<Scalars['String']['input']>>;
  hrIds?: InputMaybe<Array<Scalars['Float']['input']>>;
  idcTags?: InputMaybe<Array<Scalars['String']['input']>>;
  position?: InputMaybe<Scalars['String']['input']>;
  regionTags?: InputMaybe<Array<Scalars['String']['input']>>;
  userId?: InputMaybe<Scalars['Float']['input']>;
  year: Scalars['String']['input'];
};

export type AnySimpleUser = {
  __typename?: 'AnySimpleUser';
  id?: Maybe<Scalars['Int']['output']>;
  name: Scalars['String']['output'];
  type: AnySimpleUserType;
};

export enum AnySimpleUserType {
  External = 'external',
  Internal = 'internal'
}

export type AppIdentity = {
  __typename?: 'AppIdentity';
  roleCode?: Maybe<Scalars['String']['output']>;
  site: Scalars['String']['output'];
  userId: Scalars['Long']['output'];
};

export type ApplyCommentDrillOrderParams = {
  commentUserId: Scalars['Long']['input'];
  commentUserName: Scalars['String']['input'];
  execNo: Scalars['String']['input'];
};

export type ApproveSpaceDetail = {
  __typename?: 'ApproveSpaceDetail';
  approvalId?: Maybe<Scalars['String']['output']>;
  blockGuid: Scalars['String']['output'];
  createTime?: Maybe<Scalars['String']['output']>;
  creator?: Maybe<SingleCustomerWhiteListSimperUser>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  status: Scalars['String']['output'];
};

export type AssigneeInfo = {
  id: Scalars['Long']['input'];
  userName: Scalars['String']['input'];
};

export type AssigneeList = {
  __typename?: 'AssigneeList';
  id?: Maybe<Scalars['Long']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type Assist = {
  __typename?: 'Assist';
  id?: Maybe<Scalars['Long']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type AtDayConfig = {
  __typename?: 'AtDayConfig';
  atDayType: Scalars['String']['output'];
  isLast?: Maybe<Scalars['Int']['output']>;
  month?: Maybe<Scalars['String']['output']>;
  sortNum: Scalars['Int']['output'];
};

export type AtDayConfigInput = {
  atDayType: Scalars['String']['input'];
  isLast?: InputMaybe<Scalars['Int']['input']>;
  month?: InputMaybe<Scalars['String']['input']>;
  sortNum: Scalars['Int']['input'];
};

export type AuthResource = {
  __typename?: 'AuthResource';
  children?: Maybe<Array<AuthResource>>;
  displayText: Scalars['String']['output'];
  label: Scalars['String']['output'];
  parentValue?: Maybe<Scalars['String']['output']>;
  type: AuthResourceNodeType;
  value: Scalars['String']['output'];
};

export enum AuthResourceNodeType {
  Area = 'AREA',
  Block = 'BLOCK',
  Customer = 'CUSTOMER',
  Dept = 'DEPT',
  Idc = 'IDC',
  Team = 'TEAM'
}

export type AuthorizationRecord = {
  __typename?: 'AuthorizationRecord';
  expiredAt: Scalars['Long']['output'];
  id: Scalars['Long']['output'];
  isDeleted: Scalars['Boolean']['output'];
  modifiedAt: Scalars['Long']['output'];
  modifiedBy: SimpleUser;
  resourceList: Array<AuthorizationResource>;
  resourceType?: Maybe<AuthorizationResourceType>;
  roleCode: Scalars['String']['output'];
  roleName: Scalars['String']['output'];
  userId: Scalars['Long']['output'];
  userName: Scalars['String']['output'];
  website: WebsiteCode;
};

export enum AuthorizationRecordStatus {
  Invalid = 'INVALID',
  Valid = 'VALID'
}

export type AuthorizationRecordsQueryParams = {
  expiredCurrentMonthRange?: InputMaybe<Array<Scalars['Long']['input']>>;
  expiredTimeRange?: InputMaybe<Array<Scalars['Long']['input']>>;
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
  roleName?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<AuthorizationRecordStatus>;
  userName?: InputMaybe<Scalars['String']['input']>;
};

export type AuthorizationRecordsResponse = {
  __typename?: 'AuthorizationRecordsResponse';
  data: Array<AuthorizationRecord>;
  total: Scalars['Long']['output'];
};

export type AuthorizationResource = {
  __typename?: 'AuthorizationResource';
  parentCode?: Maybe<Scalars['String']['output']>;
  resourceCode: Scalars['String']['output'];
  resourceName: Scalars['String']['output'];
  resourceType: AuthorizationResourceType;
};

export enum AuthorizationResourceType {
  Area = 'AREA',
  Building = 'BUILDING',
  Customer = 'CUSTOMER',
  Dept = 'DEPT',
  Idc = 'IDC',
  Team = 'TEAM'
}

export type AuthorizationStatistic = {
  __typename?: 'AuthorizationStatistic';
  closeExpireNum: Scalars['Long']['output'];
  continueNum: Scalars['Long']['output'];
  expireNum: Scalars['Long']['output'];
  totalNum: Scalars['Long']['output'];
};

export type AvailableDevicesInBlock = {
  __typename?: 'AvailableDevicesInBlock';
  avaliableCount: Scalars['Int']['output'];
  deviceType: Scalars['String']['output'];
  productModel: Scalars['String']['output'];
  roomTag: Scalars['String']['output'];
  secondCategory: Scalars['String']['output'];
  topCategory: Scalars['String']['output'];
  vendor: Scalars['String']['output'];
};

export type AvailableDevicesInBlockResponse = {
  __typename?: 'AvailableDevicesInBlockResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Array<Maybe<AvailableDevicesInBlock>>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total?: Maybe<Scalars['Long']['output']>;
};

export enum BackendAnnualPerformanceObjectiveSubType {
  Biz = 'BIZ',
  Develop = 'DEVELOP'
}

export enum BackendAnnualPerformanceObjectiveType {
  Daily = 'DAILY',
  Year = 'YEAR'
}

export enum BackendAnnualPerformanceObjectiveWay {
  Custom = 'CUSTOM',
  Standard = 'STANDARD'
}

export enum BackendAnnualPerformancePlanSplitRule {
  HalfYear = 'HALF_YEAR',
  Quarter = 'QUARTER',
  Year = 'YEAR'
}

export type BackendFileInfo = {
  __typename?: 'BackendFileInfo';
  fileFormat?: Maybe<Scalars['String']['output']>;
  fileName?: Maybe<Scalars['String']['output']>;
  filePath?: Maybe<Scalars['String']['output']>;
  fileSize?: Maybe<Scalars['Long']['output']>;
  gmtCreate?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  uploadBy?: Maybe<Scalars['Long']['output']>;
  uploadByName?: Maybe<Scalars['String']['output']>;
  uploadTime?: Maybe<Scalars['Long']['output']>;
};

export type BackendMcUploadFile = {
  __typename?: 'BackendMcUploadFile';
  fileFormat: Scalars['String']['output'];
  fileName: Scalars['String']['output'];
  filePath: Scalars['String']['output'];
  fileSize: Scalars['Long']['output'];
  fileType?: Maybe<Scalars['String']['output']>;
  gmtCreate?: Maybe<Scalars['String']['output']>;
  gmtModified?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  targetId?: Maybe<Scalars['String']['output']>;
  targetType?: Maybe<Scalars['String']['output']>;
  uploadBy: Scalars['Long']['output'];
  uploadByName: Scalars['String']['output'];
  uploadTime: Scalars['String']['output'];
};

export type BackendRackOnPowerTicket = {
  __typename?: 'BackendRackOnPowerTicket';
  blockTag: Scalars['String']['output'];
  checkStatus: CheckStatus;
  columnTag: Scalars['String']['output'];
  executeTime?: Maybe<Scalars['String']['output']>;
  frontGrids?: Maybe<Array<Maybe<FrontRack>>>;
  gridGuid: Scalars['String']['output'];
  gridTag: Scalars['String']['output'];
  gridType: RackType;
  id: Scalars['Long']['output'];
  idcTag: Scalars['String']['output'];
  modReason?: Maybe<Scalars['String']['output']>;
  modified: Scalars['Boolean']['output'];
  operatorBy?: Maybe<Scalars['Int']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  pduDevices?: Maybe<Array<Maybe<PduDevice>>>;
  powerOnTime?: Maybe<Scalars['String']['output']>;
  ratedPower: Scalars['Int']['output'];
  roomTag: Scalars['String']['output'];
  taskNo: Scalars['String']['output'];
};

export type BankInfo = {
  __typename?: 'BankInfo';
  account: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type BankInfoInput = {
  account: Scalars['String']['input'];
  name: Scalars['String']['input'];
};

export type BaseHistoryTrade = {
  historical?: InputMaybe<Scalars['Boolean']['input']>;
  tradeDetail?: InputMaybe<Scalars['String']['input']>;
};

export type BatchAddHolidayBalanceResponse = MutationResponse & {
  __typename?: 'BatchAddHolidayBalanceResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchAddHrmBalanceResponse = MutationResponse & {
  __typename?: 'BatchAddHrmBalanceResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchAdjustApprovalGrid = {
  __typename?: 'BatchAdjustApprovalGrid';
  adjustMaxPower: Scalars['Float']['output'];
  gridGuid: Scalars['String']['output'];
  gridPower: Scalars['Long']['output'];
  maxPowerTime: Scalars['String']['output'];
  originMaxPower: Scalars['Float']['output'];
  roomTag: Scalars['String']['output'];
};

export type BatchAdjustApprovalGridsResponse = {
  __typename?: 'BatchAdjustApprovalGridsResponse';
  data?: Maybe<Array<BatchAdjustApprovalGrid>>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type BatchAdjustFeesRecordsResponse = {
  __typename?: 'BatchAdjustFeesRecordsResponse';
  data?: Maybe<Array<BatchAdjustInfo>>;
  total: Scalars['Long']['output'];
};

export type BatchAdjustFeesResponse = MutationResponse & {
  __typename?: 'BatchAdjustFeesResponse';
  approvalId?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchAdjustInfo = {
  __typename?: 'BatchAdjustInfo';
  adjustDirection: Scalars['Boolean']['output'];
  adjustItemType?: Maybe<Scalars['String']['output']>;
  adjustNo: Scalars['String']['output'];
  adjustResourceType?: Maybe<Scalars['String']['output']>;
  adjustSource: Scalars['Int']['output'];
  adjustType: Scalars['String']['output'];
  adjustTypeName: Scalars['String']['output'];
  adjustValue: Scalars['String']['output'];
  applyStaffId: Scalars['String']['output'];
  applyStaffName: Scalars['String']['output'];
  batchAdjust: Scalars['Boolean']['output'];
  batchAdjustNum?: Maybe<Scalars['Int']['output']>;
  createAt: Scalars['Long']['output'];
  id: Scalars['Int']['output'];
  instStatus: Scalars['String']['output'];
  originValue: Scalars['String']['output'];
  procInstanceId: Scalars['String']['output'];
  resourceNo?: Maybe<Scalars['String']['output']>;
};

export type BatchHandOverPerformancesResponse = {
  __typename?: 'BatchHandOverPerformancesResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  opportunityCode?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchMaintenanceCheckItemsResponse = MutationResponse & {
  __typename?: 'BatchMaintenanceCheckItemsResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  riskTicketNumber?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchOperateRiskPoolsResponse = MutationResponse & {
  __typename?: 'BatchOperateRiskPoolsResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchPointDataDurationResponse = {
  __typename?: 'BatchPointDataDurationResponse';
  data?: Maybe<Array<PointDataDuration>>;
  total: Scalars['Long']['output'];
};

export type BatchUpdateTaskStatusResponse = MutationResponse & {
  __typename?: 'BatchUpdateTaskStatusResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type BatchUrgeRiskCheckTicketsResponse = MutationResponse & {
  __typename?: 'BatchUrgeRiskCheckTicketsResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Bill = {
  __typename?: 'Bill';
  accountNo?: Maybe<Scalars['String']['output']>;
  billNo: Scalars['String']['output'];
  billStatus: BillStatusTag;
  billTag: Scalars['String']['output'];
  chargeFee?: Maybe<Scalars['Float']['output']>;
  curCheckCode?: Maybe<Scalars['String']['output']>;
  customerNo: Scalars['String']['output'];
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  existRecheck: Scalars['Boolean']['output'];
  idcTag: Scalars['String']['output'];
  originFee?: Maybe<Scalars['Float']['output']>;
  paidFee?: Maybe<Scalars['Float']['output']>;
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  waitHandleNum: Scalars['Int']['output'];
};

export type BillDetail = {
  __typename?: 'BillDetail';
  accountNo: Scalars['String']['output'];
  billNo: Scalars['String']['output'];
  billStatus: BillStatusTag;
  billTag: Scalars['String']['output'];
  chargeFee?: Maybe<Scalars['Float']['output']>;
  customerNo: Scalars['String']['output'];
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  existRecheck: Scalars['Boolean']['output'];
  idcTag: Scalars['String']['output'];
  needOpCheck?: Maybe<Scalars['Boolean']['output']>;
  originFee?: Maybe<Scalars['Float']['output']>;
  paidFee?: Maybe<Scalars['Float']['output']>;
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  returnTime?: Maybe<Scalars['Long']['output']>;
  waitHandleNum: Scalars['Int']['output'];
};

export type BillFeeDetailAddInfoInput = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  endAmount?: InputMaybe<Scalars['Float']['input']>;
  fee?: InputMaybe<Scalars['Float']['input']>;
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  price?: InputMaybe<Scalars['Float']['input']>;
  remark: Scalars['String']['input'];
  startAmount?: InputMaybe<Scalars['Float']['input']>;
  useAmount?: InputMaybe<Scalars['Float']['input']>;
};

export type BillResponse = {
  __typename?: 'BillResponse';
  billDetail: BillDetail;
  secondFees: SecondFees;
};

export type BillStatusTag = {
  __typename?: 'BillStatusTag';
  code: Scalars['String']['output'];
  color: Scalars['String']['output'];
  text: Scalars['String']['output'];
};

export type BillsResponse = {
  __typename?: 'BillsResponse';
  data?: Maybe<Array<Bill>>;
  total: Scalars['Long']['output'];
};

export type BizStatus = {
  __typename?: 'BizStatus';
  color: Scalars['String']['output'];
  name: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type BpmInstanceJson = {
  __typename?: 'BpmInstanceJSON';
  applyTime: Scalars['String']['output'];
  applyUser: Scalars['Int']['output'];
  applyUserName: Scalars['String']['output'];
  attachmentType?: Maybe<Scalars['String']['output']>;
  bizId: Scalars['String']['output'];
  bizType: Scalars['String']['output'];
  bizTypeSla?: Maybe<Scalars['Long']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  content?: Maybe<Scalars['String']['output']>;
  creatorId: Scalars['Int']['output'];
  creatorName: Scalars['String']['output'];
  formJson?: Maybe<Scalars['Any']['output']>;
  formattedContent?: Maybe<Array<Maybe<FormattedContent>>>;
  idc?: Maybe<Scalars['String']['output']>;
  msgId?: Maybe<Scalars['String']['output']>;
  msgParentType?: Maybe<Scalars['String']['output']>;
  msgSecondType?: Maybe<Scalars['String']['output']>;
  oaType: Scalars['String']['output'];
  operationRecords?: Maybe<Array<ProcessRecord>>;
  reason?: Maybe<Scalars['String']['output']>;
  room?: Maybe<Scalars['String']['output']>;
  sla?: Maybe<Scalars['Long']['output']>;
  status: Scalars['String']['output'];
  title: Scalars['String']['output'];
  xml?: Maybe<Scalars['String']['output']>;
};

export type BusinessIndicatorSortInput = {
  field: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type BusinessIndicators = {
  __typename?: 'BusinessIndicators';
  accountsReceivable?: Maybe<Scalars['Float']['output']>;
  accountsReceivableRatio?: Maybe<Scalars['Float']['output']>;
  allAccountsReceivableRatio?: Maybe<Scalars['Float']['output']>;
  allIncomeRatio?: Maybe<Scalars['Float']['output']>;
  allRepaymentRatio?: Maybe<Scalars['Float']['output']>;
  avgRepaymentCycle?: Maybe<Scalars['Float']['output']>;
  billableRackRatio?: Maybe<Scalars['Float']['output']>;
  billingCabinetCount?: Maybe<Scalars['Float']['output']>;
  billingLoad?: Maybe<Scalars['Float']['output']>;
  billingLoadRatio?: Maybe<Scalars['Float']['output']>;
  billingRate?: Maybe<Scalars['Float']['output']>;
  blockShow?: Maybe<Scalars['String']['output']>;
  blockTag?: Maybe<Scalars['String']['output']>;
  businessOpportunities?: Maybe<Scalars['Float']['output']>;
  contractRate?: Maybe<Scalars['Float']['output']>;
  contractedCabinetCount?: Maybe<Scalars['Float']['output']>;
  contractedLoad?: Maybe<Scalars['Float']['output']>;
  contractedLoadRatio?: Maybe<Scalars['Float']['output']>;
  curAccountsReceivable?: Maybe<Scalars['Float']['output']>;
  curAccountsReceivableRatio?: Maybe<Scalars['Float']['output']>;
  curIncome?: Maybe<Scalars['Float']['output']>;
  curIncomeRatio?: Maybe<Scalars['Float']['output']>;
  curOverdue?: Maybe<Scalars['Float']['output']>;
  curOverdueRatio?: Maybe<Scalars['Float']['output']>;
  curRepayment?: Maybe<Scalars['Float']['output']>;
  curRepaymentRatio?: Maybe<Scalars['Float']['output']>;
  customerIndustry?: Maybe<Scalars['String']['output']>;
  customerIndustryName?: Maybe<Scalars['String']['output']>;
  customerName?: Maybe<Scalars['String']['output']>;
  customerNo?: Maybe<Scalars['String']['output']>;
  customerType?: Maybe<Scalars['String']['output']>;
  deliveredCabinetCount?: Maybe<Scalars['Float']['output']>;
  deliveredLoad?: Maybe<Scalars['Float']['output']>;
  deliveryRackRatio?: Maybe<Scalars['Float']['output']>;
  deliveryRate?: Maybe<Scalars['Float']['output']>;
  deployedRackRatio?: Maybe<Scalars['Float']['output']>;
  dt: Scalars['String']['output'];
  expectedMonthlyIncome?: Maybe<Scalars['Float']['output']>;
  expectedTotalAmount?: Maybe<Scalars['Float']['output']>;
  feeCode?: Maybe<Scalars['String']['output']>;
  feeName?: Maybe<Scalars['String']['output']>;
  fullyLaunchedProjCount?: Maybe<Scalars['Float']['output']>;
  gridPower?: Maybe<Scalars['Float']['output']>;
  incAccountsReceivable?: Maybe<Scalars['Float']['output']>;
  incAvgRepaymentCycle?: Maybe<Scalars['Float']['output']>;
  incBillingCabinetCount?: Maybe<Scalars['Float']['output']>;
  incBillingLoad?: Maybe<Scalars['Float']['output']>;
  incBillingRate?: Maybe<Scalars['Float']['output']>;
  incBusinessOpportunities?: Maybe<Scalars['Float']['output']>;
  incContractRate?: Maybe<Scalars['Float']['output']>;
  incContractedCabinetCount?: Maybe<Scalars['Float']['output']>;
  incContractedLoad?: Maybe<Scalars['Float']['output']>;
  incCurAccountsReceivable?: Maybe<Scalars['Float']['output']>;
  incCurIncome?: Maybe<Scalars['Float']['output']>;
  incCurOverdue?: Maybe<Scalars['Float']['output']>;
  incCurRepayment?: Maybe<Scalars['Float']['output']>;
  incDeliveredCabinetCount?: Maybe<Scalars['Float']['output']>;
  incDeliveredLoad?: Maybe<Scalars['Float']['output']>;
  incDeliveryRate?: Maybe<Scalars['Float']['output']>;
  incExpectedMonthlyIncome?: Maybe<Scalars['Float']['output']>;
  incExpectedTotalAmount?: Maybe<Scalars['Float']['output']>;
  incFullyLaunchedProjCount?: Maybe<Scalars['Float']['output']>;
  incIncome?: Maybe<Scalars['Float']['output']>;
  incItCapacityRequirement?: Maybe<Scalars['Float']['output']>;
  incOverdue?: Maybe<Scalars['Float']['output']>;
  incPartLaunchedProjCount?: Maybe<Scalars['Float']['output']>;
  incPlanCabinetCount?: Maybe<Scalars['Float']['output']>;
  incPlanLoad?: Maybe<Scalars['Float']['output']>;
  incPlanProjCount?: Maybe<Scalars['Float']['output']>;
  incRepayment?: Maybe<Scalars['Float']['output']>;
  incRepaymentCycle?: Maybe<Scalars['Float']['output']>;
  incRepaymentRate?: Maybe<Scalars['Float']['output']>;
  incShelfCabinetCount?: Maybe<Scalars['Float']['output']>;
  incShelfDeliveryRate?: Maybe<Scalars['Float']['output']>;
  incShelfLoad?: Maybe<Scalars['Float']['output']>;
  incShelfRate?: Maybe<Scalars['Float']['output']>;
  incShelfSignedRate?: Maybe<Scalars['Float']['output']>;
  incUnderConstProjCount?: Maybe<Scalars['Float']['output']>;
  income?: Maybe<Scalars['Float']['output']>;
  incomeRatio?: Maybe<Scalars['Float']['output']>;
  itCapacityRequirement?: Maybe<Scalars['Float']['output']>;
  orderBillingRate?: Maybe<Scalars['Float']['output']>;
  orderRepaymentRate?: Maybe<Scalars['Float']['output']>;
  orderShelfRate?: Maybe<Scalars['Float']['output']>;
  overdue?: Maybe<Scalars['Float']['output']>;
  overdueRatio?: Maybe<Scalars['Float']['output']>;
  partLaunchedProjCount?: Maybe<Scalars['Float']['output']>;
  planCabinetCount?: Maybe<Scalars['Float']['output']>;
  planLoad?: Maybe<Scalars['Float']['output']>;
  planProjCount?: Maybe<Scalars['Float']['output']>;
  projCount?: Maybe<Scalars['Float']['output']>;
  projectId?: Maybe<Scalars['String']['output']>;
  projectName?: Maybe<Scalars['String']['output']>;
  projectStatus?: Maybe<Scalars['String']['output']>;
  rateAccountsReceivable?: Maybe<Scalars['Float']['output']>;
  rateAvgRepaymentCycle?: Maybe<Scalars['Float']['output']>;
  rateBillingCabinetCount?: Maybe<Scalars['Float']['output']>;
  rateBillingLoad?: Maybe<Scalars['Float']['output']>;
  rateBillingRate?: Maybe<Scalars['Float']['output']>;
  rateBusinessOpportunities?: Maybe<Scalars['Float']['output']>;
  rateContractRate?: Maybe<Scalars['Float']['output']>;
  rateContractedCabinetCount?: Maybe<Scalars['Float']['output']>;
  rateContractedLoad?: Maybe<Scalars['Float']['output']>;
  rateCurAccountsReceivable?: Maybe<Scalars['Float']['output']>;
  rateCurIncome?: Maybe<Scalars['Float']['output']>;
  rateCurOverdue?: Maybe<Scalars['Float']['output']>;
  rateCurRepayment?: Maybe<Scalars['Float']['output']>;
  rateDeliveredCabinetCount?: Maybe<Scalars['Float']['output']>;
  rateDeliveredLoad?: Maybe<Scalars['Float']['output']>;
  rateDeliveryRate?: Maybe<Scalars['Float']['output']>;
  rateExpectedMonthlyIncome?: Maybe<Scalars['Float']['output']>;
  rateExpectedTotalAmount?: Maybe<Scalars['Float']['output']>;
  rateFullyLaunchedProjCount?: Maybe<Scalars['Float']['output']>;
  rateIncome?: Maybe<Scalars['Float']['output']>;
  rateItCapacityRequirement?: Maybe<Scalars['Float']['output']>;
  rateOverdue?: Maybe<Scalars['Float']['output']>;
  ratePartLaunchedProjCount?: Maybe<Scalars['Float']['output']>;
  ratePlanCabinetCount?: Maybe<Scalars['Float']['output']>;
  ratePlanLoad?: Maybe<Scalars['Float']['output']>;
  ratePlanProjCount?: Maybe<Scalars['Float']['output']>;
  rateRepayment?: Maybe<Scalars['Float']['output']>;
  rateRepaymentCycle?: Maybe<Scalars['Float']['output']>;
  rateRepaymentRate?: Maybe<Scalars['Float']['output']>;
  rateShelfCabinetCount?: Maybe<Scalars['Float']['output']>;
  rateShelfDeliveryRate?: Maybe<Scalars['Float']['output']>;
  rateShelfLoad?: Maybe<Scalars['Float']['output']>;
  rateShelfRate?: Maybe<Scalars['Float']['output']>;
  rateShelfSignedRate?: Maybe<Scalars['Float']['output']>;
  rateUnderConstProjCount?: Maybe<Scalars['Float']['output']>;
  repayment?: Maybe<Scalars['Float']['output']>;
  repaymentCycle?: Maybe<Scalars['Float']['output']>;
  repaymentRate?: Maybe<Scalars['Float']['output']>;
  repaymentRatio?: Maybe<Scalars['Float']['output']>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  shelfCabinetCount?: Maybe<Scalars['Float']['output']>;
  shelfDeliveryRate?: Maybe<Scalars['Float']['output']>;
  shelfLoad?: Maybe<Scalars['Float']['output']>;
  shelfLoadRatio?: Maybe<Scalars['Float']['output']>;
  shelfRate?: Maybe<Scalars['Float']['output']>;
  shelfSignedRate?: Maybe<Scalars['Float']['output']>;
  signedRackRatio?: Maybe<Scalars['Float']['output']>;
  underConstProjCount?: Maybe<Scalars['Float']['output']>;
};

export type BusinessIndicatorsResponse = {
  __typename?: 'BusinessIndicatorsResponse';
  customerStatistic?: Maybe<Array<CustomerStatisticInfos>>;
  data?: Maybe<Array<BusinessIndicators>>;
  lastedData?: Maybe<BusinessIndicators>;
};

export type BusinessIndicatorsSearchParams = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  customerNo?: InputMaybe<Scalars['String']['input']>;
  customerType?: InputMaybe<Scalars['String']['input']>;
  dataType: Scalars['String']['input'];
  endDate: Scalars['String']['input'];
  needCustomerStatistic?: InputMaybe<Scalars['Boolean']['input']>;
  projectId?: InputMaybe<Scalars['String']['input']>;
  projectStatus?: InputMaybe<Array<Scalars['String']['input']>>;
  queryMaxTime?: InputMaybe<Scalars['Boolean']['input']>;
  regionCode?: InputMaybe<Scalars['String']['input']>;
  startDate: Scalars['String']['input'];
};

export type CancelCollectProjectMapFavoiteResponse = {
  __typename?: 'CancelCollectProjectMapFavoiteResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Scalars['Boolean']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CcUser = {
  __typename?: 'CcUser';
  ccMsgIsRead: Scalars['Boolean']['output'];
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  jobNo?: Maybe<Scalars['String']['output']>;
  mobile?: Maybe<Scalars['String']['output']>;
  userName: Scalars['String']['output'];
};

export type ChangeAlarmCountResponse = {
  __typename?: 'ChangeAlarmCountResponse';
  expected?: Maybe<Expect>;
  outsideExpected?: Maybe<Expect>;
};

export type ChangeCheckItemArtificialValidationQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  checkItemId?: InputMaybe<Scalars['Long']['input']>;
  itemStatus?: InputMaybe<Scalars['String']['input']>;
};

export type ChangeCloseResponse = MutationResponse & {
  __typename?: 'ChangeCloseResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeDetailInfoResponse = {
  __typename?: 'ChangeDetailInfoResponse';
  changeInfo?: Maybe<ChangeInfo>;
  deviceGuids?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  planList?: Maybe<Array<Maybe<PlanItem>>>;
  stepInfo?: Maybe<Scalars['JSONObject']['output']>;
};

export type ChangeDetailStepInfoResponse = {
  __typename?: 'ChangeDetailStepInfoResponse';
  checkMethod?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['Long']['output']>;
  exceptionReason?: Maybe<Scalars['String']['output']>;
  exeWay?: Maybe<Scalars['String']['output']>;
  expectedValue?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  identifyWay?: Maybe<Scalars['String']['output']>;
  ola?: Maybe<Scalars['Long']['output']>;
  olaUnit?: Maybe<Scalars['String']['output']>;
  opObjectCode?: Maybe<Scalars['String']['output']>;
  opObjectName?: Maybe<Scalars['String']['output']>;
  opType?: Maybe<Scalars['String']['output']>;
  operate?: Maybe<Scalars['String']['output']>;
  operatorId?: Maybe<Scalars['Long']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  pointValueText?: Maybe<Scalars['String']['output']>;
  startTime?: Maybe<Scalars['Long']['output']>;
  stepDesc?: Maybe<Scalars['String']['output']>;
  stepName?: Maybe<Scalars['String']['output']>;
  stepOrder?: Maybe<Scalars['Long']['output']>;
  stepStatus?: Maybe<Scalars['String']['output']>;
  stepType?: Maybe<Scalars['String']['output']>;
};

export type ChangeDetailStepOptionInfoResponse = {
  __typename?: 'ChangeDetailStepOptionInfoResponse';
  checkMethod?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['Long']['output']>;
  exceptionReason?: Maybe<Scalars['String']['output']>;
  exeWay?: Maybe<Scalars['String']['output']>;
  expectedValue?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  identifyWay?: Maybe<Scalars['String']['output']>;
  ola?: Maybe<Scalars['Long']['output']>;
  olaUnit?: Maybe<Scalars['String']['output']>;
  opObjectCode?: Maybe<Scalars['String']['output']>;
  opObjectName?: Maybe<Scalars['String']['output']>;
  opType?: Maybe<Scalars['String']['output']>;
  operate?: Maybe<Scalars['String']['output']>;
  operatorId?: Maybe<Scalars['Long']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  pointValueText?: Maybe<Scalars['String']['output']>;
  startTime?: Maybe<Scalars['Long']['output']>;
  stepDesc?: Maybe<Scalars['String']['output']>;
  stepDeviceTypeInfoList?: Maybe<Array<Maybe<StepDeviceTypeInfoItem>>>;
  stepName?: Maybe<Scalars['String']['output']>;
  stepOpItemInfoList?: Maybe<Array<Maybe<StepOpItemInfoItem>>>;
  stepOrder?: Maybe<Scalars['Long']['output']>;
  stepStatus?: Maybe<Scalars['String']['output']>;
  stepType?: Maybe<Scalars['String']['output']>;
};

export type ChangeDeviceInfo = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  deviceGuid: Scalars['String']['input'];
  deviceName: Scalars['String']['input'];
  deviceType: Scalars['String']['input'];
  idcTag?: InputMaybe<Scalars['String']['input']>;
  inhibition: Scalars['Boolean']['input'];
  roomTag?: InputMaybe<Scalars['String']['input']>;
};

export type ChangeDeviceList = {
  __typename?: 'ChangeDeviceList';
  deviceGuid: Scalars['String']['output'];
  deviceName: Scalars['String']['output'];
  deviceType: Scalars['String']['output'];
  inhibition: Scalars['Boolean']['output'];
  roomTag?: Maybe<Scalars['String']['output']>;
};

export type ChangeInfo = {
  __typename?: 'ChangeInfo';
  associateEvents?: Maybe<Array<Maybe<Scalars['Long']['output']>>>;
  blockTag?: Maybe<Scalars['String']['output']>;
  changeInfluence?: Maybe<Scalars['String']['output']>;
  changeOrderId?: Maybe<Scalars['String']['output']>;
  changeStatus?: Maybe<Scalars['String']['output']>;
  changeTypeName?: Maybe<Scalars['String']['output']>;
  creatorId?: Maybe<Scalars['Long']['output']>;
  creatorName?: Maybe<Scalars['String']['output']>;
  customerFileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  exeUserGroupName?: Maybe<Scalars['String']['output']>;
  exeWay?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  gmtCreate?: Maybe<Scalars['Long']['output']>;
  gmtModified?: Maybe<Scalars['Long']['output']>;
  hasPermission?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  operatorId?: Maybe<Scalars['Long']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  planEndTime?: Maybe<Scalars['Long']['output']>;
  planStartTime?: Maybe<Scalars['Long']['output']>;
  realEndTime?: Maybe<Scalars['Long']['output']>;
  realStartTime?: Maybe<Scalars['Long']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  riskLevel?: Maybe<Scalars['String']['output']>;
  stopReason?: Maybe<Scalars['String']['output']>;
  summarizePersonId?: Maybe<Scalars['Long']['output']>;
  summarizePersonName?: Maybe<Scalars['String']['output']>;
  summery?: Maybe<Scalars['String']['output']>;
  templateName?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  workFlowId?: Maybe<Scalars['String']['output']>;
};

export type ChangeListItem = {
  __typename?: 'ChangeListItem';
  blockTag: Scalars['String']['output'];
  changeOrderId: Scalars['String']['output'];
  changeStatus: Scalars['String']['output'];
  changeType: Scalars['String']['output'];
  changeTypeName?: Maybe<Scalars['String']['output']>;
  creatorId: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  exeWay?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  idcTag: Scalars['String']['output'];
  planEndTime: Scalars['Long']['output'];
  planStartTime: Scalars['Long']['output'];
  realEndTime?: Maybe<Scalars['Long']['output']>;
  realStartTime?: Maybe<Scalars['Long']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  riskLevel: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type ChangeNotificationQuery = {
  changeId: Scalars['String']['input'];
  changeStatus: Scalars['String']['input'];
  reportChannel: Scalars['String']['input'];
  reportContent: Scalars['String']['input'];
  reportObject: Array<ReportObjectInput>;
};

export type ChangeNotificationResponse = MutationResponse & {
  __typename?: 'ChangeNotificationResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeOfflineDeviceInhibitionStatusResponse = {
  __typename?: 'ChangeOfflineDeviceInhibitionStatusResponse';
  data?: Maybe<Scalars['Long']['output']>;
};

export type ChangeOfflineRelateTicket = {
  __typename?: 'ChangeOfflineRelateTicket';
  creatorId: Scalars['Long']['output'];
  desc: Scalars['String']['output'];
  gmtCreate: Scalars['Long']['output'];
  relateNo: Scalars['String']['output'];
  relateType: Scalars['String']['output'];
  status: Scalars['String']['output'];
};

export type ChangeOfflineRelateTicketsResponse = {
  __typename?: 'ChangeOfflineRelateTicketsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<ChangeOfflineRelateTicket>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type ChangeOrderDevicesQuery = {
  changeOrderId: Scalars['String']['input'];
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
};

export type ChangeOrderDevicesResponse = {
  __typename?: 'ChangeOrderDevicesResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Array<Maybe<ChangeDeviceList>>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total?: Maybe<Scalars['Long']['output']>;
};

export type ChangeOrderExeLogListData = {
  __typename?: 'ChangeOrderExeLogListData';
  changeId?: Maybe<Scalars['String']['output']>;
  exeCode?: Maybe<Scalars['String']['output']>;
  exeContent?: Maybe<Scalars['String']['output']>;
  exeDuration?: Maybe<Scalars['Double']['output']>;
  exeName?: Maybe<Scalars['String']['output']>;
  exeStatus?: Maybe<Scalars['String']['output']>;
  exeType?: Maybe<Scalars['String']['output']>;
  finishTime?: Maybe<Scalars['Long']['output']>;
  gmtCreate?: Maybe<Scalars['Long']['output']>;
  gmtModified?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  operatorId?: Maybe<Scalars['Long']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  startTime?: Maybe<Scalars['Long']['output']>;
  stepId?: Maybe<Scalars['Long']['output']>;
};

export type ChangeShiftApplyOwner = {
  __typename?: 'ChangeShiftApplyOwner';
  enable?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  locked?: Maybe<Scalars['Boolean']['output']>;
  scheduleScene?: Maybe<ScheduleScene>;
  teamLeader?: Maybe<Scalars['Boolean']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type ChangeSkipQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  jumpReason?: InputMaybe<Scalars['String']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
  stepOrder?: InputMaybe<Scalars['Long']['input']>;
};

export type ChangeSkipResponse = MutationResponse & {
  __typename?: 'ChangeSkipResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeStartStepQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
  stepOrder?: InputMaybe<Scalars['Long']['input']>;
};

export type ChangeStartStepResponse = MutationResponse & {
  __typename?: 'ChangeStartStepResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<ChangeStartStepResponseData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeStartStepResponseData = {
  __typename?: 'ChangeStartStepResponseData';
  stepId?: Maybe<Scalars['Long']['output']>;
  stepOrder?: Maybe<Scalars['Long']['output']>;
};

export type ChangeStepCheckQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
};

export type ChangeStepCheckResponse = MutationResponse & {
  __typename?: 'ChangeStepCheckResponse';
  code?: Maybe<Scalars['String']['output']>;
  isOrderEnd?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeStepItemConfirmQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  itemCode?: InputMaybe<Scalars['String']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
};

export type ChangeStepItemConfirmResponse = MutationResponse & {
  __typename?: 'ChangeStepItemConfirmResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeStepItemExceptionQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  opItemId?: InputMaybe<Scalars['Long']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
};

export type ChangeStopQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  stopReason?: InputMaybe<Scalars['String']['input']>;
};

export type ChangeStopResponse = MutationResponse & {
  __typename?: 'ChangeStopResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeStopStepQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  customerStepStatus?: InputMaybe<Scalars['String']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
  stepOrder?: InputMaybe<Scalars['Long']['input']>;
};

export type ChangeStopStepResponse = MutationResponse & {
  __typename?: 'ChangeStopStepResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeSummeryInfoQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  exeResult?: InputMaybe<Scalars['String']['input']>;
  fileInfoList?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  realEndTime?: InputMaybe<Scalars['Long']['input']>;
  realStartTime?: InputMaybe<Scalars['Long']['input']>;
  summery?: InputMaybe<Scalars['String']['input']>;
};

export type ChangeSummeryInfoResponse = MutationResponse & {
  __typename?: 'ChangeSummeryInfoResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ChangeTimeInfo = {
  endDate: Scalars['String']['input'];
  endHour: Scalars['String']['input'];
  startDate: Scalars['String']['input'];
  startHour: Scalars['String']['input'];
};

export type CheckItem = {
  __typename?: 'CheckItem';
  checkItemName: Scalars['String']['output'];
  exceptionNum: Scalars['Int']['output'];
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  id: Scalars['ID']['output'];
  needCheckNum?: Maybe<Scalars['Long']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  standards: Array<Maybe<Standard>>;
  standardsFinishedNum: Scalars['Int']['output'];
};

export type CheckItemInfo = {
  __typename?: 'CheckItemInfo';
  dataType?: Maybe<Scalars['String']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceInfoList?: Maybe<Array<Maybe<MatchObjectInfoList>>>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  exceptionHandle?: Maybe<Scalars['String']['output']>;
  expectedResult?: Maybe<Scalars['String']['output']>;
  expectedValue?: Maybe<Scalars['String']['output']>;
  identifyWay?: Maybe<Scalars['String']['output']>;
  itemStatus?: Maybe<Scalars['String']['output']>;
  maxInfluencesStep?: Maybe<Scalars['String']['output']>;
  operatorList?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointData?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  pointValueText?: Maybe<Scalars['String']['output']>;
  rowSpan?: Maybe<Scalars['Long']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type CheckItemInfoItem = {
  __typename?: 'CheckItemInfoItem';
  checkItemId?: Maybe<Scalars['Long']['output']>;
  dataType?: Maybe<Scalars['String']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  exceptionHandle?: Maybe<Scalars['String']['output']>;
  expectedResult?: Maybe<Scalars['String']['output']>;
  expectedValue?: Maybe<Scalars['String']['output']>;
  identifyWay?: Maybe<Scalars['String']['output']>;
  item?: Maybe<Scalars['String']['output']>;
  itemStatus?: Maybe<Scalars['String']['output']>;
  maxInfluencesStep?: Maybe<Scalars['String']['output']>;
  operatorList?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointData?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  pointValueText?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type CheckPowerDeviceQ = {
  deviceGuid: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};

export type CheckPowerDeviceResponse = MutationResponse & {
  __typename?: 'CheckPowerDeviceResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<CheckPowerDeviceResponseDate>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CheckPowerDeviceResponseDate = {
  __typename?: 'CheckPowerDeviceResponseDate';
  branchCoolInterval?: Maybe<Scalars['Long']['output']>;
  branchCoolIntervalUnit?: Maybe<Scalars['String']['output']>;
  checkTargetId?: Maybe<Scalars['String']['output']>;
  checkTargetType?: Maybe<Scalars['String']['output']>;
  checkTime?: Maybe<Scalars['Long']['output']>;
  gridCoolInterval?: Maybe<Scalars['Long']['output']>;
  gridCoolIntervalUnit?: Maybe<Scalars['String']['output']>;
};

export type CheckRackPowerOnOffTicketPdUsQuery = {
  idc?: InputMaybe<Scalars['String']['input']>;
  pdus: Array<InputMaybe<CheckRackPowerOnOffTicketPdUsQueryPdu>>;
  taskSubType: Scalars['String']['input'];
};

export type CheckRackPowerOnOffTicketPdUsQueryPdu = {
  deviceGuid: Scalars['String']['input'];
  deviceType: Scalars['String']['input'];
};

export type CheckResultInfo = {
  checkNormal?: InputMaybe<Scalars['String']['input']>;
  checkValue?: InputMaybe<Scalars['Float']['input']>;
  inspectItemId: Scalars['Long']['input'];
  opTime?: InputMaybe<Scalars['Long']['input']>;
  taskNo: Scalars['String']['input'];
};

export type CheckStatus = {
  __typename?: 'CheckStatus';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type CheckValueJson = {
  __typename?: 'CheckValueJson';
  dataType?: Maybe<Scalars['String']['output']>;
  diValueText?: Maybe<Scalars['String']['output']>;
  maxValue?: Maybe<Scalars['Double']['output']>;
  minValue?: Maybe<Scalars['Double']['output']>;
  relateNo?: Maybe<Scalars['String']['output']>;
  relateOpType?: Maybe<Scalars['String']['output']>;
  relateType?: Maybe<Scalars['String']['output']>;
  resultSource?: Maybe<Scalars['String']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type CloseFailedTicketsQ = {
  delayDesc?: InputMaybe<Scalars['String']['input']>;
  delayReason?: InputMaybe<Scalars['String']['input']>;
  failedDesc: Scalars['String']['input'];
  failedReason: Scalars['String']['input'];
  taskNos: Array<InputMaybe<Scalars['String']['input']>>;
  taskType: Scalars['String']['input'];
};

export type CloseTicketQ = {
  delayDesc?: InputMaybe<Scalars['String']['input']>;
  delayReason?: InputMaybe<Scalars['String']['input']>;
  failedDesc?: InputMaybe<Scalars['String']['input']>;
  failedReason?: InputMaybe<Scalars['String']['input']>;
  ignoreCheckEnd?: InputMaybe<Scalars['Boolean']['input']>;
  routeEndWay?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
  taskType: Scalars['String']['input'];
};

export type CloseTicketResponse = MutationResponse & {
  __typename?: 'CloseTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CloseTicketsByQ = {
  taskNos: Array<InputMaybe<Scalars['String']['input']>>;
  ticketsQ: TicketsQuery;
  total: Scalars['Long']['input'];
};

export type CloseTicketsByQCountMap = {
  __typename?: 'CloseTicketsByQCountMap';
  approveNum?: Maybe<Scalars['Long']['output']>;
  endNum?: Maybe<Scalars['Long']['output']>;
  sumNum?: Maybe<Scalars['Long']['output']>;
};

export type CloseTicketsByResponse = MutationResponse & {
  __typename?: 'CloseTicketsByResponse';
  code?: Maybe<Scalars['String']['output']>;
  countMap?: Maybe<CloseTicketsByQCountMap>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CloseTicketsQ = {
  delayDesc?: InputMaybe<Scalars['String']['input']>;
  delayReason?: InputMaybe<Scalars['String']['input']>;
  taskNos: Array<InputMaybe<Scalars['String']['input']>>;
  taskType: Scalars['String']['input'];
};

export type CollectProjectMapFavoiteResponse = {
  __typename?: 'CollectProjectMapFavoiteResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Scalars['Boolean']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CombineLeaveTime = {
  __typename?: 'CombineLeaveTime';
  leaveType?: Maybe<Scalars['String']['output']>;
  takeTime?: Maybe<Scalars['Float']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type CommentDrillOrderParams = {
  commentContent: Scalars['String']['input'];
  execNo: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
};

export type CompanyInfo = {
  idcCompanyName: Scalars['String']['input'];
  province?: InputMaybe<Scalars['String']['input']>;
};

export type CompleteOfflineChangeResponse = MutationResponse & {
  __typename?: 'CompleteOfflineChangeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ConfirmFeeResponse = MutationResponse & {
  __typename?: 'ConfirmFeeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Contract = {
  __typename?: 'Contract';
  batchDelivery?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['Long']['output']>;
  creator?: Maybe<SimpleCreator>;
  customer?: Maybe<SimpleCustomer>;
  endAt?: Maybe<Scalars['Long']['output']>;
  gridNum?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  idcName?: Maybe<Scalars['String']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  idcTagName?: Maybe<Scalars['String']['output']>;
  itCapacity?: Maybe<Scalars['Long']['output']>;
  leaseMethod?: Maybe<Scalars['String']['output']>;
  modifiedAt?: Maybe<Scalars['Long']['output']>;
  resp?: Maybe<SimpleResp>;
  startAt?: Maybe<Scalars['Long']['output']>;
  startNEndTime?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type ContractsResponse = {
  __typename?: 'ContractsResponse';
  data?: Maybe<Array<Maybe<Contract>>>;
  total: Scalars['Long']['output'];
};

export type CountdownInfo = {
  __typename?: 'CountdownInfo';
  branchCoolInterval?: Maybe<Scalars['Long']['output']>;
  branchCoolIntervalUnit?: Maybe<Scalars['String']['output']>;
  checkTargetId?: Maybe<Scalars['String']['output']>;
  checkTargetType?: Maybe<Scalars['String']['output']>;
  checkTime?: Maybe<Scalars['Long']['output']>;
  gridCoolInterval?: Maybe<Scalars['Long']['output']>;
  gridCoolIntervalUnit?: Maybe<Scalars['String']['output']>;
};

export type CreateAlarmShieldParams = {
  blockGuid: Scalars['String']['input'];
  endTime: Scalars['Long']['input'];
  name: Scalars['String']['input'];
  shieldScopeList?: InputMaybe<Array<ShieldScopeInput>>;
  startTime: Scalars['Long']['input'];
};

export type CreateAlarmShieldResponse = {
  __typename?: 'CreateAlarmShieldResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateChangeByRiskRegisterQ = {
  blockGuid: Scalars['String']['input'];
  changeDeviceList?: InputMaybe<Array<ChangeDeviceInfo>>;
  changeLevel: Scalars['String']['input'];
  changeMajorCode: Scalars['String']['input'];
  changeTimeList?: InputMaybe<Array<ChangeTimeInfo>>;
  changeTypeCode: Scalars['String']['input'];
  customerFiles?: InputMaybe<Array<McUploadFileInput>>;
  fileInfoList: Array<McUploadFileInput>;
  idcTag: Scalars['String']['input'];
  influenceDesc: Scalars['String']['input'];
  planEndTime: Scalars['Long']['input'];
  planStartTime: Scalars['Long']['input'];
  purpose?: InputMaybe<Scalars['String']['input']>;
  riskId: Scalars['String']['input'];
  sourceChangeOrderId?: InputMaybe<Scalars['String']['input']>;
  sourceNo?: InputMaybe<Scalars['String']['input']>;
  sourceType?: InputMaybe<Scalars['String']['input']>;
  stepList?: InputMaybe<Array<StepList>>;
  title: Scalars['String']['input'];
};

export type CreateChangeByRiskRegisterResponse = MutationResponse & {
  __typename?: 'CreateChangeByRiskRegisterResponse';
  changeOrderId?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateCustomerMutationResponse = {
  __typename?: 'CreateCustomerMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateCustomerParams = {
  abbreviation?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<CustomerAddressInput>;
  assistList?: InputMaybe<Array<Scalars['Long']['input']>>;
  contacts?: InputMaybe<Array<CustomerContactInput>>;
  deptId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  industry?: InputMaybe<Scalars['String']['input']>;
  invoices?: InputMaybe<Array<InvoiceInput>>;
  knowOrganizationalStructure: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  owner: SimpleUserInput;
  source?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type CreateCustomerWhiteListMutationResponse = MutationResponse & {
  __typename?: 'CreateCustomerWhiteListMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateDrillConfigResponse = MutationResponse & {
  __typename?: 'CreateDrillConfigResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Long']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateDrillOrderParams = {
  assigneeInfo: AssigneeInfo;
  blockGuid: Scalars['String']['input'];
  desc?: InputMaybe<Scalars['String']['input']>;
  excLevel: Scalars['String']['input'];
  excLevelName: Scalars['String']['input'];
  excMajor: Scalars['String']['input'];
  excMajorName: Scalars['String']['input'];
  exerciseId: Scalars['Long']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  idcTag: Scalars['String']['input'];
  principalId: Scalars['Long']['input'];
  principalName: Scalars['String']['input'];
  taskSla: Scalars['Long']['input'];
  title: Scalars['String']['input'];
  unit: Scalars['String']['input'];
};

export type CreateDrillOrderRemarkParams = {
  execNo: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  itemId: Scalars['Long']['input'];
  remarkDesc?: InputMaybe<Scalars['String']['input']>;
};

export type CreateDrillOrderResponse = {
  __typename?: 'CreateDrillOrderResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Scalars['String']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateEventByChangeQ = {
  blockTag: Scalars['String']['input'];
  causeBy?: InputMaybe<Scalars['String']['input']>;
  causeDesc?: InputMaybe<Scalars['String']['input']>;
  causeDevice?: InputMaybe<Scalars['String']['input']>;
  changeCode: Scalars['String']['input'];
  changeOrderId: Scalars['String']['input'];
  detectTime?: InputMaybe<Scalars['Long']['input']>;
  eventDesc: Scalars['String']['input'];
  eventInfluence?: InputMaybe<EventInfluenceInput>;
  eventLevel?: InputMaybe<Scalars['String']['input']>;
  eventLevelName?: InputMaybe<Scalars['String']['input']>;
  eventOwnerId: Scalars['Long']['input'];
  eventOwnerName: Scalars['String']['input'];
  eventSource: Scalars['String']['input'];
  eventSourceName: Scalars['String']['input'];
  eventTitle: Scalars['String']['input'];
  files?: InputMaybe<Array<McUploadFileInput>>;
  idcTag: Scalars['String']['input'];
  infoType: Scalars['String']['input'];
  isChangeAlarm: Scalars['Boolean']['input'];
  isFalseAlarm: Scalars['Long']['input'];
  northSync: Scalars['Boolean']['input'];
  occurTime?: InputMaybe<Scalars['Long']['input']>;
  secondCategory?: InputMaybe<Scalars['String']['input']>;
  secondCategoryName?: InputMaybe<Scalars['String']['input']>;
  topCategory: Scalars['String']['input'];
  topCategoryName: Scalars['String']['input'];
};

export type CreateEventByChangeResponse = MutationResponse & {
  __typename?: 'CreateEventByChangeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateEventProcessRecordResponse = MutationResponse & {
  __typename?: 'CreateEventProcessRecordResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateEventRelateRiskTicketResponse = MutationResponse & {
  __typename?: 'CreateEventRelateRiskTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  riskTicketNumber?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateFeeResponse = MutationResponse & {
  __typename?: 'CreateFeeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateNonstandardInspectItemQ = {
  exDesc: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  roomGuid: Scalars['String']['input'];
  subjectName: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};

export type CreateNonstandardInspectItemResponse = MutationResponse & {
  __typename?: 'CreateNonstandardInspectItemResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateProjectMapFavoiteResponse = {
  __typename?: 'CreateProjectMapFavoiteResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Scalars['Boolean']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateProjectMapResponse = {
  __typename?: 'CreateProjectMapResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<CreateProjectMapResponseData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateProjectMapResponseData = {
  __typename?: 'CreateProjectMapResponseData';
  idcProjectId?: Maybe<Scalars['Long']['output']>;
  processInstId?: Maybe<Scalars['String']['output']>;
};

export type CreateRiskCheckTicketResponse = MutationResponse & {
  __typename?: 'CreateRiskCheckTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateRiskPoolQ = {
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  riskCategory: Scalars['String']['input'];
  riskDesc: Scalars['String']['input'];
  riskLevel: RiskLevel;
  riskType: Scalars['String']['input'];
  verifyList: Array<Scalars['String']['input']>;
};

export type CreateRiskPoolResponse = MutationResponse & {
  __typename?: 'CreateRiskPoolResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateRiskRegisterByChangeQ = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  changeOrderId: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  idcTag: Scalars['String']['input'];
  relatePersonId?: InputMaybe<Scalars['Long']['input']>;
  relatePersonName?: InputMaybe<Scalars['String']['input']>;
  riskDesc?: InputMaybe<Scalars['String']['input']>;
  riskInfluenceDesc?: InputMaybe<Scalars['String']['input']>;
  riskLevel?: InputMaybe<Scalars['String']['input']>;
  riskObjectName?: InputMaybe<Scalars['String']['input']>;
  riskObjectType?: InputMaybe<Scalars['String']['input']>;
  riskOwnerId?: InputMaybe<Scalars['Long']['input']>;
  riskPriorityCode?: InputMaybe<Scalars['String']['input']>;
  riskResourceCode?: InputMaybe<Scalars['String']['input']>;
  riskSecTypeCode?: InputMaybe<Scalars['String']['input']>;
  riskTopTypeCode?: InputMaybe<Scalars['String']['input']>;
};

export type CreateRiskRegisterByChangeResponse = MutationResponse & {
  __typename?: 'CreateRiskRegisterByChangeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type CreateSalesOpportunityTaskMutationResponse = {
  __typename?: 'CreateSalesOpportunityTaskMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  task?: Maybe<SalesOpportunityTask>;
  taskCode?: Maybe<Scalars['String']['output']>;
};

export type CreateTaskResponse = MutationResponse & {
  __typename?: 'CreateTaskResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  taskId?: Maybe<Scalars['Int']['output']>;
};

export type CreateTicketNotificationResponse = MutationResponse & {
  __typename?: 'CreateTicketNotificationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Customer = {
  __typename?: 'Customer';
  abbreviation?: Maybe<Scalars['String']['output']>;
  address?: Maybe<CustomerAddress>;
  assists?: Maybe<Array<Assist>>;
  code: Scalars['String']['output'];
  contacts?: Maybe<Array<CustomerContact>>;
  createdAt: Scalars['Long']['output'];
  department?: Maybe<CustomerDepartment>;
  description?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  invoices?: Maybe<Array<Invoice>>;
  knowOrganizationalStructure: Scalars['Boolean']['output'];
  modifiedAt?: Maybe<Scalars['Long']['output']>;
  name: Scalars['String']['output'];
  owner: SimpleUser;
  source?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  typeText: Scalars['String']['output'];
};

export type CustomerAddress = {
  __typename?: 'CustomerAddress';
  detail?: Maybe<Scalars['String']['output']>;
  region?: Maybe<Scalars['String']['output']>;
};

export type CustomerAddressInput = {
  detail?: InputMaybe<Scalars['String']['input']>;
  region?: InputMaybe<Scalars['String']['input']>;
};

export type CustomerBusiness = {
  __typename?: 'CustomerBusiness';
  approvedAt?: Maybe<Scalars['Long']['output']>;
  businessTerm?: Maybe<Scalars['String']['output']>;
  companyName: Scalars['String']['output'];
  companyType?: Maybe<Scalars['String']['output']>;
  createdAt: Scalars['Long']['output'];
  creditCode?: Maybe<Scalars['String']['output']>;
  establishedAt?: Maybe<Scalars['Long']['output']>;
  id: Scalars['Long']['output'];
  industry?: Maybe<Scalars['String']['output']>;
  legalRepresentative?: Maybe<Scalars['String']['output']>;
  modifiedAt?: Maybe<Scalars['Long']['output']>;
  organizationCode?: Maybe<Scalars['String']['output']>;
  personnelSize?: Maybe<Scalars['String']['output']>;
  registeredCapital?: Maybe<Scalars['String']['output']>;
  registrationAuthority?: Maybe<Scalars['String']['output']>;
  registrationNumber?: Maybe<Scalars['String']['output']>;
  taxpayerIdentificationNumber?: Maybe<Scalars['String']['output']>;
};

export type CustomerCompany = {
  __typename?: 'CustomerCompany';
  name: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type CustomerContact = {
  __typename?: 'CustomerContact';
  email?: Maybe<Scalars['String']['output']>;
  mobile?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  position?: Maybe<Scalars['String']['output']>;
};

export type CustomerContactInput = {
  email?: InputMaybe<Scalars['String']['input']>;
  mobile?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  position?: InputMaybe<Scalars['String']['input']>;
};

export type CustomerDepartment = {
  __typename?: 'CustomerDepartment';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type CustomerInfluenceItem = {
  __typename?: 'CustomerInfluenceItem';
  belongsCabinet?: Maybe<Scalars['Long']['output']>;
  customerName?: Maybe<Scalars['String']['output']>;
  impactCabinet?: Maybe<Scalars['Long']['output']>;
  proportion?: Maybe<Scalars['Long']['output']>;
};

export type CustomerInput = {
  address?: InputMaybe<CustomerAddressInput>;
  /**
   * Optional
   * ------
   */
  code?: InputMaybe<Scalars['String']['input']>;
  contacts?: InputMaybe<Array<CustomerContactInput>>;
  hasOrgStructure: Scalars['Boolean']['input'];
  industry?: InputMaybe<Scalars['String']['input']>;
  /**
   * Required
   * ------
   */
  name: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type CustomerInvoicesResponse = {
  __typename?: 'CustomerInvoicesResponse';
  invoices: Array<Invoice>;
};

export type CustomerNamesResponse = {
  __typename?: 'CustomerNamesResponse';
  names: Array<Scalars['String']['output']>;
};

export type CustomerOnRack = {
  __typename?: 'CustomerOnRack';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type CustomerOwner = {
  __typename?: 'CustomerOwner';
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
};

export type CustomerPerson = {
  email: Scalars['String']['input'];
  phone: Scalars['String']['input'];
  position?: InputMaybe<Scalars['String']['input']>;
  userName: Scalars['String']['input'];
};

export type CustomerRequirementInfo = {
  __typename?: 'CustomerRequirementInfo';
  gridRequirementInfoList?: Maybe<Array<GridRequirementInfo>>;
  itCapacity?: Maybe<Scalars['String']['output']>;
  itCapacityUnit?: Maybe<Scalars['String']['output']>;
  requirementTypeList?: Maybe<Array<Scalars['String']['output']>>;
};

export type CustomerRequirementInfoInput = {
  gridRequirementInfoList?: InputMaybe<Array<InputMaybe<GridRequirementInfoInput>>>;
  itCapacity?: InputMaybe<Scalars['String']['input']>;
  itCapacityUnit?: InputMaybe<Scalars['String']['input']>;
  requirementTypeList?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type CustomerStatisticInfos = {
  __typename?: 'CustomerStatisticInfos';
  amount?: Maybe<Scalars['Int']['output']>;
  customerIndustry: Scalars['String']['output'];
  customerIndustryName?: Maybe<Scalars['String']['output']>;
  percent?: Maybe<Scalars['Float']['output']>;
};

export type CustomerWhiteListDetailResponse = {
  __typename?: 'CustomerWhiteListDetailResponse';
  data?: Maybe<SingleCustomerWhiteList>;
};

export type CustomerWhiteListResponse = {
  __typename?: 'CustomerWhiteListResponse';
  data?: Maybe<Array<SingleCustomerWhiteList>>;
  total: Scalars['Long']['output'];
};

export type CustomerWhiteStaffResponse = {
  __typename?: 'CustomerWhiteStaffResponse';
  data?: Maybe<SingleCustomerWhiteList>;
};

export type CustomersResponse = {
  __typename?: 'CustomersResponse';
  data?: Maybe<Array<Customer>>;
  total: Scalars['Long']['output'];
};

export type CustomizedIdc = {
  __typename?: 'CustomizedIdc';
  customerClear?: Maybe<Scalars['Boolean']['output']>;
  customers?: Maybe<Array<Scalars['String']['output']>>;
  customized?: Maybe<Scalars['Boolean']['output']>;
};

export type CustomizedIdcRoom = {
  customerClear?: InputMaybe<Scalars['Boolean']['input']>;
  customers?: InputMaybe<Array<Scalars['String']['input']>>;
  customized?: InputMaybe<Scalars['Boolean']['input']>;
};

export type Cycle = {
  __typename?: 'Cycle';
  atDayConfig?: Maybe<Array<AtDayConfig>>;
  dayConfig?: Maybe<PlanDayConfig>;
  period: Scalars['Long']['output'];
  periodUnit: Scalars['String']['output'];
};

export type CycleInput = {
  atDayConfig?: InputMaybe<Array<AtDayConfigInput>>;
  dayConfig?: InputMaybe<PlanDayConfigInput>;
  period: Scalars['Long']['input'];
  periodUnit: Scalars['String']['input'];
};

export enum DataSetKey {
  Customer = 'CUSTOMER',
  CustomerStatistic = 'CUSTOMER_STATISTIC',
  Project = 'PROJECT',
  ProjectCustomer = 'PROJECT_CUSTOMER',
  ProjectCustomerFee = 'PROJECT_CUSTOMER_FEE',
  ProjectCustomerGrid = 'PROJECT_CUSTOMER_GRID',
  Region = 'REGION'
}

export type DeleteAlarmShieldResponse = {
  __typename?: 'DeleteAlarmShieldResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteChangeOfflineExecuteLogResponse = MutationResponse & {
  __typename?: 'DeleteChangeOfflineExecuteLogResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteChangeOrderDeviceQ = {
  changeOrderId: Scalars['String']['input'];
  deviceGuid: Scalars['String']['input'];
  deviceName: Scalars['String']['input'];
};

export type DeleteChangeOrderDeviceResponse = MutationResponse & {
  __typename?: 'DeleteChangeOrderDeviceResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteCustomerWhiteListMutationResponse = MutationResponse & {
  __typename?: 'DeleteCustomerWhiteListMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteDrillConfigResponse = MutationResponse & {
  __typename?: 'DeleteDrillConfigResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteProjectMapFavoiteResponse = {
  __typename?: 'DeleteProjectMapFavoiteResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Scalars['Boolean']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteRiskPoolResponse = MutationResponse & {
  __typename?: 'DeleteRiskPoolResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteRiskRegisterResponse = MutationResponse & {
  __typename?: 'DeleteRiskRegisterResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DeleteTaskResponse = MutationResponse & {
  __typename?: 'DeleteTaskResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Device = {
  __typename?: 'Device';
  deviceAssetSN?: Maybe<Scalars['String']['output']>;
  deviceGuid: Scalars['String']['output'];
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceSN?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType: Scalars['String']['output'];
  extendPosition?: Maybe<Scalars['String']['output']>;
  fullLevelsDeviceTypesRecord?: Maybe<FullLevelsDeviceTypesRecord>;
  manufacturer?: Maybe<Scalars['String']['output']>;
  model?: Maybe<Scalars['String']['output']>;
  operationStatus?: Maybe<Scalars['String']['output']>;
  operationStatusName?: Maybe<Scalars['String']['output']>;
  powerLine?: Maybe<Scalars['String']['output']>;
  spaceGuids?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type DeviceByBlockQuery = {
  assetNoList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  blockTag?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
};

export type DeviceByBlockResponse = {
  __typename?: 'DeviceByBlockResponse';
  assetNo?: Maybe<Scalars['String']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  idcGuid?: Maybe<Scalars['String']['output']>;
  operationStatus?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  roomGuid?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  serialNo?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
};

export type DeviceInfluence = {
  __typename?: 'DeviceInfluence';
  existingRoom?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  list?: Maybe<Array<Maybe<DeviceInfluenceItem>>>;
};

export type DeviceInfluenceItem = {
  __typename?: 'DeviceInfluenceItem';
  blockTag?: Maybe<Scalars['String']['output']>;
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  extendPosition?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
};

export type DeviceInfoItem = {
  __typename?: 'DeviceInfoItem';
  checkItemInfoList?: Maybe<Array<Maybe<CheckItemInfoItem>>>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  exceptionNum?: Maybe<Scalars['Long']['output']>;
  normalNum?: Maybe<Scalars['Long']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  totalNum?: Maybe<Scalars['Long']['output']>;
};

export type DeviceInput = {
  deviceGuid: Scalars['String']['input'];
  deviceName?: InputMaybe<Scalars['String']['input']>;
  deviceSN?: InputMaybe<Scalars['String']['input']>;
  deviceTag?: InputMaybe<Scalars['String']['input']>;
  deviceType?: InputMaybe<Scalars['String']['input']>;
  spaceGuids?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type DeviceModel = {
  assetNo?: InputMaybe<Scalars['String']['input']>;
  deviceType?: InputMaybe<Scalars['String']['input']>;
  productModel?: InputMaybe<Scalars['String']['input']>;
  secondCategory?: InputMaybe<Scalars['String']['input']>;
  serialNo?: InputMaybe<Scalars['String']['input']>;
  supplyVendor?: InputMaybe<Scalars['String']['input']>;
  topCategory?: InputMaybe<Scalars['String']['input']>;
  vendor?: InputMaybe<Scalars['String']['input']>;
  warehouseCount?: InputMaybe<Scalars['Long']['input']>;
};

export type DeviceModelInput = {
  deviceType: Scalars['String']['input'];
  productModel: Scalars['String']['input'];
  secondCategory: Scalars['String']['input'];
  topCategory: Scalars['String']['input'];
  vendor: Scalars['String']['input'];
  warehouseCount: Scalars['Long']['input'];
};

export type DeviceOnRack = {
  __typename?: 'DeviceOnRack';
  gridGuid: Scalars['String']['output'];
  relateDeviceList?: Maybe<Array<RelateDevice>>;
};

export type DeviceTypeTreeNode = {
  __typename?: 'DeviceTypeTreeNode';
  children?: Maybe<Array<Maybe<DeviceTypeTreeNode>>>;
  metaCode?: Maybe<Scalars['String']['output']>;
  metaName?: Maybe<Scalars['String']['output']>;
};

export type DiscardFeeResponse = MutationResponse & {
  __typename?: 'DiscardFeeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type DrillConfig = {
  __typename?: 'DrillConfig';
  bizId: Scalars['String']['output'];
  count: Scalars['Int']['output'];
  creatorId: Scalars['Int']['output'];
  creatorName: Scalars['String']['output'];
  effectDomain: Scalars['String']['output'];
  effectType: Scalars['String']['output'];
  excLevel: Scalars['String']['output'];
  excMajor: Scalars['String']['output'];
  excName: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  id: Scalars['Long']['output'];
};

export type DrillConfigByBizInfo = {
  __typename?: 'DrillConfigByBizInfo';
  bizId: Scalars['String']['output'];
  blockGuid?: Maybe<Scalars['String']['output']>;
  effectType: Scalars['String']['output'];
  excLevel: Scalars['String']['output'];
  excMajor: Scalars['String']['output'];
  excName: Scalars['String']['output'];
  exercisePlanStepList: Array<ExercisePlanStepByBiz>;
  id: Scalars['Long']['output'];
};

export type DrillConfigByBizResponse = {
  __typename?: 'DrillConfigByBizResponse';
  data?: Maybe<DrillConfigByBizInfo>;
};

export type DrillConfigInfo = {
  __typename?: 'DrillConfigInfo';
  bizId: Scalars['String']['output'];
  blockGuid?: Maybe<Scalars['String']['output']>;
  effectType: Scalars['String']['output'];
  excLevel: Scalars['String']['output'];
  excMajor: Scalars['String']['output'];
  excName: Scalars['String']['output'];
  exercisePlanStepList: Array<ExercisePlanStep>;
  id: Scalars['Long']['output'];
};

export type DrillConfigResponse = {
  __typename?: 'DrillConfigResponse';
  data?: Maybe<DrillConfigInfo>;
};

export type DrillConfigsResponse = {
  __typename?: 'DrillConfigsResponse';
  data?: Maybe<Array<DrillConfig>>;
  total: Scalars['Long']['output'];
};

export type DrillOrder = {
  __typename?: 'DrillOrder';
  approveCloseTime?: Maybe<Scalars['String']['output']>;
  assigneeList?: Maybe<Array<DrillOrderAssignee>>;
  bizProcessId: Scalars['String']['output'];
  blockGuid: Scalars['String']['output'];
  closeProcessId?: Maybe<Scalars['String']['output']>;
  closeTime?: Maybe<Scalars['String']['output']>;
  commentContent?: Maybe<Scalars['String']['output']>;
  commentTime?: Maybe<Scalars['String']['output']>;
  commentUser?: Maybe<Scalars['Long']['output']>;
  commitCommentTime?: Maybe<Scalars['String']['output']>;
  commitCommentUser?: Maybe<Scalars['Long']['output']>;
  creatorId: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  desc?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['String']['output']>;
  excLevel: Scalars['String']['output'];
  excLevelName: Scalars['String']['output'];
  excMajor: Scalars['String']['output'];
  excMajorName: Scalars['String']['output'];
  excNo: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  gmtModified?: Maybe<Scalars['String']['output']>;
  idcTag: Scalars['String']['output'];
  modifierId?: Maybe<Scalars['String']['output']>;
  modifierName?: Maybe<Scalars['String']['output']>;
  principalId?: Maybe<Scalars['Long']['output']>;
  principalName?: Maybe<Scalars['String']['output']>;
  reviewTime?: Maybe<Scalars['String']['output']>;
  scheduleId?: Maybe<Scalars['Long']['output']>;
  slaSeconds?: Maybe<Scalars['Long']['output']>;
  startTime?: Maybe<Scalars['String']['output']>;
  stopTime?: Maybe<Scalars['String']['output']>;
  takeTime?: Maybe<Scalars['String']['output']>;
  taskAssignee?: Maybe<Scalars['Long']['output']>;
  taskAssigneeName?: Maybe<Scalars['String']['output']>;
  taskSla: Scalars['Long']['output'];
  taskStatus: DrillOrderTaskStatus;
  title: Scalars['String']['output'];
  unit: DrillOrderUnit;
};

export type DrillOrderApproval = {
  __typename?: 'DrillOrderApproval';
  applicantId: Scalars['Long']['output'];
  applicantName: Scalars['String']['output'];
  approvalReason: Scalars['String']['output'];
  bizId: Scalars['String']['output'];
  bizType: Scalars['String']['output'];
  blockGuid: Scalars['String']['output'];
  content: Scalars['String']['output'];
  creatorId: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  gmtCreate: Scalars['Long']['output'];
  gmtEnd?: Maybe<Scalars['Long']['output']>;
  gmtModified: Scalars['Long']['output'];
  idcTag: Scalars['String']['output'];
  instId: Scalars['String']['output'];
  instStatus: Scalars['String']['output'];
  nodeList?: Maybe<Array<DrillOrderApprovalNode>>;
  revokeReason?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  wfProperties: Scalars['String']['output'];
};

export type DrillOrderApprovalInfoParams = {
  execNo: Scalars['String']['input'];
  processId: Scalars['String']['input'];
};

export type DrillOrderApprovalInfoRes = {
  __typename?: 'DrillOrderApprovalInfoRes';
  data?: Maybe<Scalars['JSONObject']['output']>;
};

export type DrillOrderApprovalNode = {
  __typename?: 'DrillOrderApprovalNode';
  cancelReason?: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  createTime: Scalars['Long']['output'];
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type DrillOrderApprovalParams = {
  instId: Scalars['String']['input'];
  needNodes: Scalars['Boolean']['input'];
  userId?: InputMaybe<Scalars['Long']['input']>;
};

export type DrillOrderAssignee = {
  __typename?: 'DrillOrderAssignee';
  id: Scalars['Long']['output'];
  userName: Scalars['String']['output'];
};

export type DrillOrderInfo = {
  __typename?: 'DrillOrderInfo';
  approveCloseTime?: Maybe<Scalars['String']['output']>;
  assigneeList?: Maybe<Array<DrillOrderAssignee>>;
  bizProcessId: Scalars['String']['output'];
  blockGuid: Scalars['String']['output'];
  closeProcessId?: Maybe<Scalars['String']['output']>;
  closeTime?: Maybe<Scalars['String']['output']>;
  commentContent?: Maybe<Scalars['String']['output']>;
  commentFileInfoList?: Maybe<Array<McUploadFile>>;
  commentTime?: Maybe<Scalars['String']['output']>;
  commentUser?: Maybe<Scalars['Long']['output']>;
  commitCommentTime?: Maybe<Scalars['String']['output']>;
  commitCommentUser?: Maybe<Scalars['Long']['output']>;
  creatorId: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  desc?: Maybe<Scalars['String']['output']>;
  endTime?: Maybe<Scalars['String']['output']>;
  excLevel: Scalars['String']['output'];
  excLevelName: Scalars['String']['output'];
  excMajor: Scalars['String']['output'];
  excMajorName: Scalars['String']['output'];
  excNo: Scalars['String']['output'];
  exerciseOrderReviewList?: Maybe<Array<ExerciseOrderReview>>;
  exerciseOrderStepItemDoList: Array<ExerciseOrderStepItem>;
  fileInfoList?: Maybe<Array<McUploadFile>>;
  gmtCreate: Scalars['String']['output'];
  gmtModified?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  idcTag: Scalars['String']['output'];
  modifierId?: Maybe<Scalars['String']['output']>;
  modifierName?: Maybe<Scalars['String']['output']>;
  principalId?: Maybe<Scalars['Long']['output']>;
  principalName?: Maybe<Scalars['String']['output']>;
  reviewTime?: Maybe<Scalars['String']['output']>;
  scheduleId?: Maybe<Scalars['Long']['output']>;
  slaSeconds?: Maybe<Scalars['Long']['output']>;
  startTime?: Maybe<Scalars['String']['output']>;
  stopTime?: Maybe<Scalars['String']['output']>;
  takeTime?: Maybe<Scalars['String']['output']>;
  taskAssignee?: Maybe<Scalars['Long']['output']>;
  taskAssigneeName?: Maybe<Scalars['String']['output']>;
  taskSla: Scalars['Long']['output'];
  taskStatus: DrillOrderTaskStatus;
  title: Scalars['String']['output'];
  unit: DrillOrderUnit;
};

export type DrillOrderStep = {
  __typename?: 'DrillOrderStep';
  avatarUrl?: Maybe<Scalars['String']['output']>;
  creatorId: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  id: Scalars['String']['output'];
  itemId: Scalars['String']['output'];
  remark: Scalars['String']['output'];
};

export type DrillOrderStepResponse = {
  __typename?: 'DrillOrderStepResponse';
  data?: Maybe<Array<DrillOrderStep>>;
  total?: Maybe<Scalars['Long']['output']>;
};

export enum DrillOrderTaskStatus {
  AlreadyAccept = 'ALREADY_ACCEPT',
  ApproveToClose = 'APPROVE_TO_CLOSE',
  Close = 'CLOSE',
  Stop = 'STOP',
  WaitAccept = 'WAIT_ACCEPT',
  WaitComment = 'WAIT_COMMENT',
  WaitReview = 'WAIT_REVIEW'
}

export enum DrillOrderUnit {
  Day = 'DAY',
  Hour = 'HOUR',
  Minutes = 'MINUTES',
  Second = 'SECOND'
}

export type DrillOrdersResponse = {
  __typename?: 'DrillOrdersResponse';
  data?: Maybe<Array<DrillOrder>>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type DrillPlanStep = {
  excPhase: Scalars['String']['input'];
  followMethod: Scalars['String']['input'];
  orderNo: Scalars['Int']['input'];
  roles: Scalars['String']['input'];
  sla: Scalars['Float']['input'];
  slaUnit: Scalars['String']['input'];
  stationPosition: Scalars['String']['input'];
  stepDetail: Scalars['String']['input'];
  stepFlag: Scalars['String']['input'];
  stepMeasure: Scalars['String']['input'];
};

export type EditDrillConfigResponse = MutationResponse & {
  __typename?: 'EditDrillConfigResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Long']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ElectricityFee = {
  __typename?: 'ElectricityFee';
  adjustInstId?: Maybe<Scalars['String']['output']>;
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  blockGuid: Scalars['String']['output'];
  chargeFee?: Maybe<Scalars['Float']['output']>;
  endUseAmount?: Maybe<Scalars['Float']['output']>;
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Int']['output'];
  originFee: Scalars['Float']['output'];
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  startUseAmount?: Maybe<Scalars['Float']['output']>;
  taxFee?: Maybe<Scalars['Float']['output']>;
  useAmount?: Maybe<Scalars['Float']['output']>;
};

export type ElectricityFeesResponse = {
  __typename?: 'ElectricityFeesResponse';
  data?: Maybe<Array<ElectricityFee>>;
};

export type EvaluationJob = {
  __typename?: 'EvaluationJob';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type EvaluationStep = {
  __typename?: 'EvaluationStep';
  id: Scalars['String']['output'];
  isCurrentStep: Scalars['Boolean']['output'];
  status: Scalars['String']['output'];
  type: Scalars['String']['output'];
  users?: Maybe<Array<EvaluationStepUser>>;
};

export type EvaluationStepUser = {
  __typename?: 'EvaluationStepUser';
  attentionContent?: Maybe<Scalars['String']['output']>;
  comments?: Maybe<EvaluationStepUserComments>;
  evaluatedAt?: Maybe<Scalars['Long']['output']>;
  evaluationRequired?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  interviewed?: Maybe<Scalars['Boolean']['output']>;
  isEvaluated: Scalars['Boolean']['output'];
  needAttention?: Maybe<Scalars['Boolean']['output']>;
  result?: Maybe<Scalars['String']['output']>;
  status: Scalars['String']['output'];
  user?: Maybe<SimpleUser>;
};

export type EvaluationStepUserComments = {
  __typename?: 'EvaluationStepUserComments';
  improve?: Maybe<Scalars['String']['output']>;
  infoImprove?: Maybe<Scalars['String']['output']>;
  infoSummary?: Maybe<Scalars['String']['output']>;
  summary?: Maybe<Scalars['String']['output']>;
};

export type EventInfluenceInput = {
  gridInfluence: Scalars['String']['input'];
  influenceScope: Array<InfluenceScopeInput>;
};

export type EventJson = {
  __typename?: 'EventJson';
  blockTag?: Maybe<Scalars['String']['output']>;
  closeTime?: Maybe<Scalars['Long']['output']>;
  createUserId?: Maybe<Scalars['String']['output']>;
  eventDesc?: Maybe<Scalars['String']['output']>;
  eventLevel?: Maybe<Scalars['String']['output']>;
  eventLevelName?: Maybe<Scalars['String']['output']>;
  eventOwnerId?: Maybe<Scalars['Long']['output']>;
  eventOwnerName?: Maybe<Scalars['String']['output']>;
  eventSource?: Maybe<Scalars['String']['output']>;
  eventSourceName?: Maybe<Scalars['String']['output']>;
  eventStatus?: Maybe<EventStatus>;
  id?: Maybe<Scalars['Long']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  infoType?: Maybe<InfoType>;
  isFalseAlarm?: Maybe<Scalars['String']['output']>;
  occurTime?: Maybe<Scalars['Long']['output']>;
  relieveDesc?: Maybe<Scalars['String']['output']>;
  resolveDesc?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
};

export type EventNotification = {
  __typename?: 'EventNotification';
  blockTag: Scalars['String']['output'];
  causeDesc?: Maybe<Scalars['String']['output']>;
  causeDevices?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  creatorId?: Maybe<Scalars['Long']['output']>;
  creatorName?: Maybe<Scalars['String']['output']>;
  eventCategory: Scalars['String']['output'];
  eventDesc: Scalars['String']['output'];
  eventFirstLine?: Maybe<Scalars['String']['output']>;
  eventId: Scalars['Long']['output'];
  eventInfluence?: Maybe<Scalars['String']['output']>;
  eventLevel: Scalars['String']['output'];
  eventProgress?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  eventSource: Scalars['String']['output'];
  eventStatus: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  idcTag: Scalars['String']['output'];
  infoType?: Maybe<Scalars['String']['output']>;
  occurTime: Scalars['Long']['output'];
  reportChannel?: Maybe<Scalars['String']['output']>;
  reportContent?: Maybe<Scalars['String']['output']>;
  reportTime?: Maybe<Scalars['Long']['output']>;
  reportToObjects?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  reportToPersons?: Maybe<Scalars['String']['output']>;
  reportToRoleCodes?: Maybe<Scalars['String']['output']>;
  reportToRoleNames?: Maybe<Scalars['String']['output']>;
  subscribeCode?: Maybe<Scalars['String']['output']>;
};

export type EventNotificationsResponse = {
  __typename?: 'EventNotificationsResponse';
  data: Array<EventNotification>;
  total: Scalars['Long']['output'];
};

export type EventProcessRecordJson = {
  __typename?: 'EventProcessRecordJson';
  eventId: Scalars['Long']['output'];
  eventPhase: Scalars['String']['output'];
  handleContent: Scalars['String']['output'];
  handleTime: Scalars['Long']['output'];
  handlerId: Scalars['Int']['output'];
  spareInfo?: Maybe<EventRelateSpare>;
  wareHouseNo?: Maybe<Scalars['String']['output']>;
};

export type EventProcessRecordsResponse = {
  __typename?: 'EventProcessRecordsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<EventProcessRecordJson>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total?: Maybe<Scalars['Long']['output']>;
};

export type EventQuery = {
  blockTags?: InputMaybe<Array<Scalars['String']['input']>>;
  closeBeginTime?: InputMaybe<Scalars['String']['input']>;
  closeEndTime?: InputMaybe<Scalars['String']['input']>;
  createBeginTime?: InputMaybe<Scalars['String']['input']>;
  createEndTime?: InputMaybe<Scalars['String']['input']>;
  createUserId?: InputMaybe<Scalars['String']['input']>;
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
  eventId?: InputMaybe<Scalars['String']['input']>;
  eventIdVague?: InputMaybe<Scalars['Boolean']['input']>;
  eventLevel?: InputMaybe<Scalars['String']['input']>;
  eventSource?: InputMaybe<Scalars['String']['input']>;
  eventStatus?: InputMaybe<Array<EventStatusCode>>;
  eventTitle?: InputMaybe<Scalars['String']['input']>;
  falseAlarms?: InputMaybe<Array<Scalars['Long']['input']>>;
  idcTags?: InputMaybe<Array<Scalars['String']['input']>>;
  infoType?: InputMaybe<InfoType>;
  key?: InputMaybe<Scalars['String']['input']>;
  northSync?: InputMaybe<Scalars['Boolean']['input']>;
  occurBeginTime?: InputMaybe<Scalars['Long']['input']>;
  occurEndTime?: InputMaybe<Scalars['Long']['input']>;
  orderByCloseTimeDesc?: InputMaybe<Scalars['Boolean']['input']>;
  orderByEventLevelDesc?: InputMaybe<Scalars['String']['input']>;
  orderByGmtCreateDesc?: InputMaybe<Scalars['String']['input']>;
  orderByOccurTimeDesc?: InputMaybe<Scalars['Boolean']['input']>;
  ownerName?: InputMaybe<Scalars['String']['input']>;
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
  relateName?: InputMaybe<Scalars['String']['input']>;
  secondCategorys?: InputMaybe<Array<Scalars['String']['input']>>;
  topCategorys?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type EventRelateDeviceModel = {
  __typename?: 'EventRelateDeviceModel';
  deviceType: Scalars['String']['output'];
  productModel: Scalars['String']['output'];
  secondCategory: Scalars['String']['output'];
  topCategory: Scalars['String']['output'];
  vendor: Scalars['String']['output'];
  warehouseCount: Scalars['Long']['output'];
};

export type EventRelateSpare = {
  __typename?: 'EventRelateSpare';
  deviceModelList?: Maybe<Array<EventRelateDeviceModel>>;
  specific?: Maybe<Scalars['String']['output']>;
  targetRoom?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type EventRelateTicketsResponse = {
  __typename?: 'EventRelateTicketsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Array<Maybe<RelateTicket>>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type EventSourceRecord = {
  __typename?: 'EventSourceRecord';
  exceptionSubjectGuid: Scalars['String']['output'];
  exceptionSubjectMergeRows: Scalars['Long']['output'];
  exceptionSubjectTag: Scalars['String']['output'];
  exceptionSubjectType: Scalars['String']['output'];
  operatorId: Scalars['Long']['output'];
  operatorName: Scalars['String']['output'];
  subjectItemName: Scalars['String']['output'];
  subjectItemRelateTime: Scalars['String']['output'];
  taskNo: Scalars['String']['output'];
  taskNoMergeRows: Scalars['Long']['output'];
  taskType: Scalars['String']['output'];
};

export type EventSourceRecordsResponse = {
  __typename?: 'EventSourceRecordsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Array<EventSourceRecord>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total?: Maybe<Scalars['Long']['output']>;
};

export type EventStatus = {
  __typename?: 'EventStatus';
  code?: Maybe<EventStatusCode>;
  desc?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusCode?: Maybe<Scalars['String']['output']>;
};

export enum EventStatusCode {
  Audited = 'AUDITED',
  Auditing = 'AUDITING',
  Closed = 'CLOSED',
  Created = 'CREATED',
  Processing = 'PROCESSING',
  Relieved = 'RELIEVED',
  Resolved = 'RESOLVED'
}

export type EventsResponse = {
  __typename?: 'EventsResponse';
  data: Array<EventJson>;
  total: Scalars['Long']['output'];
};

export type ExWarehouseCancelQ = {
  id?: InputMaybe<Scalars['Long']['input']>;
  numbered: Scalars['Boolean']['input'];
  taskNo: Scalars['String']['input'];
};

export type ExWarehouseCancelResponse = MutationResponse & {
  __typename?: 'ExWarehouseCancelResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ExWarehouseDetailResponse = {
  __typename?: 'ExWarehouseDetailResponse';
  deviceType?: Maybe<Array<Maybe<LabelInValue>>>;
  exWarehouseDeviceTypeList?: Maybe<Array<Maybe<ExWarehouseDeviceType>>>;
};

export type ExWarehouseDeviceItem = {
  __typename?: 'ExWarehouseDeviceItem';
  assetNo?: Maybe<Scalars['String']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  serialNo?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
  warehouseCount?: Maybe<Scalars['Long']['output']>;
  warehouseStatus?: Maybe<Scalars['String']['output']>;
};

export type ExWarehouseDeviceQ = {
  deviceModelList?: InputMaybe<Array<InputMaybe<DeviceModel>>>;
  deviceType?: InputMaybe<Scalars['String']['input']>;
  exWarehouseCount?: InputMaybe<Scalars['Long']['input']>;
  numbered: Scalars['Boolean']['input'];
  productModel: Scalars['String']['input'];
  roomGuid?: InputMaybe<Scalars['String']['input']>;
  spareGuid?: InputMaybe<Scalars['Long']['input']>;
  taskNo: Scalars['String']['input'];
  taskTitle?: InputMaybe<Scalars['String']['input']>;
  vendor: Scalars['String']['input'];
};

export type ExWarehouseDeviceResponse = MutationResponse & {
  __typename?: 'ExWarehouseDeviceResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ExWarehouseDeviceType = {
  __typename?: 'ExWarehouseDeviceType';
  deviceCount?: Maybe<Scalars['Long']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  exWarehouseCount?: Maybe<Scalars['Long']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  remainCount?: Maybe<Scalars['Long']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
  warehouseDeviceVoList?: Maybe<Array<Maybe<ExWarehouseDeviceItem>>>;
};

export type ExerciseOrderReview = {
  __typename?: 'ExerciseOrderReview';
  creatorId: Scalars['String']['output'];
  creatorName?: Maybe<Scalars['String']['output']>;
  excNo: Scalars['String']['output'];
  fileInfoList?: Maybe<Array<McUploadFile>>;
  gmtCreate: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  reviewDesc?: Maybe<Scalars['String']['output']>;
};

export type ExerciseOrderStepItem = {
  __typename?: 'ExerciseOrderStepItem';
  creatorId: Scalars['String']['output'];
  creatorName: Scalars['String']['output'];
  excNo: Scalars['String']['output'];
  excPhase?: Maybe<Scalars['String']['output']>;
  exerciseId: Scalars['Long']['output'];
  finishTime?: Maybe<Scalars['String']['output']>;
  followMethod: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  isRemark?: Maybe<Scalars['Boolean']['output']>;
  orderNo: Scalars['Long']['output'];
  reckonStartTime?: Maybe<Scalars['String']['output']>;
  roles: Scalars['String']['output'];
  sla: Scalars['Long']['output'];
  slaSeconds: Scalars['Long']['output'];
  slaUnit: Scalars['String']['output'];
  stationPosition: Scalars['String']['output'];
  stepDetail: Scalars['String']['output'];
  stepFlag: Scalars['String']['output'];
  stepMeasure: Scalars['String']['output'];
};

export type ExercisePlanStep = {
  __typename?: 'ExercisePlanStep';
  creatorId: Scalars['Int']['output'];
  creatorName: Scalars['String']['output'];
  excPhase: Scalars['String']['output'];
  exerciseId: Scalars['String']['output'];
  followMethod: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  orderNo: Scalars['Int']['output'];
  roles: Scalars['String']['output'];
  sla: Scalars['Float']['output'];
  slaSeconds: Scalars['Long']['output'];
  slaUnit: Scalars['String']['output'];
  stationPosition: Scalars['String']['output'];
  stepDetail: Scalars['String']['output'];
  stepFlag: Scalars['String']['output'];
  stepMeasure: Scalars['String']['output'];
};

export type ExercisePlanStepByBiz = {
  __typename?: 'ExercisePlanStepByBiz';
  excPhase: Scalars['String']['output'];
  followMethod: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  orderNo: Scalars['Int']['output'];
  roles: Scalars['String']['output'];
  sla: Scalars['Float']['output'];
  slaUnit: Scalars['String']['output'];
  stationPosition: Scalars['String']['output'];
  stepDetail: Scalars['String']['output'];
  stepFlag: Scalars['String']['output'];
  stepMeasure: Scalars['String']['output'];
};

export type Expect = {
  __typename?: 'Expect';
  firstLevel?: Maybe<Scalars['Long']['output']>;
  secondLevel?: Maybe<Scalars['Long']['output']>;
  thirdLevel?: Maybe<Scalars['Long']['output']>;
  totalCount?: Maybe<Scalars['Long']['output']>;
};

export type FeeInvoice = {
  __typename?: 'FeeInvoice';
  bankAccount: Scalars['String']['output'];
  bankName: Scalars['String']['output'];
  companyAddress: Scalars['String']['output'];
  invoiceTitle: Scalars['String']['output'];
  phoneNo: Scalars['String']['output'];
  taxpayerIdentityNo: Scalars['String']['output'];
};

export type FeeInvoicingResponse = MutationResponse & {
  __typename?: 'FeeInvoicingResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type FetchChangeApprovalQ = {
  changeOrderId: Scalars['String']['input'];
  instId: Scalars['String']['input'];
};

export type FetchChangeApprovalResponse = {
  __typename?: 'FetchChangeApprovalResponse';
  data?: Maybe<Array<Scalars['JSONObject']['output']>>;
};

export type FetchChangeShiftApplyOwnerResponse = {
  __typename?: 'FetchChangeShiftApplyOwnerResponse';
  data: Array<Maybe<ChangeShiftApplyOwner>>;
  success?: Maybe<Scalars['Boolean']['output']>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type FetchChangeShiftAttendanceResult = {
  __typename?: 'FetchChangeShiftAttendanceResult';
  offDutyTime?: Maybe<Scalars['Long']['output']>;
  onDutyTime?: Maybe<Scalars['Long']['output']>;
  staffId: Scalars['Long']['output'];
};

export type FetchChangeShiftAttendanceResultQ = {
  groupScheduleId: Scalars['Long']['input'];
  staffId?: InputMaybe<Scalars['Long']['input']>;
};

export type FetchChangeShiftAttendanceResultResponse = {
  __typename?: 'FetchChangeShiftAttendanceResultResponse';
  data: Array<Maybe<FetchChangeShiftAttendanceResult>>;
  success?: Maybe<Scalars['Boolean']['output']>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type FetchChangeShiftConfigResponse = {
  __typename?: 'FetchChangeShiftConfigResponse';
  handoverDutyCheck?: Maybe<Scalars['Long']['output']>;
  handoverDutyCheckType?: Maybe<Scalars['Long']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type FetchFuzzyChange = {
  __typename?: 'FetchFuzzyChange';
  changeOrderId: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type FetchFuzzyChangesResponse = {
  __typename?: 'FetchFuzzyChangesResponse';
  data: Array<FetchFuzzyChange>;
  total: Scalars['Long']['output'];
};

export type FetchFuzzyEventsQuery = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  keyword: Scalars['String']['input'];
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
};

export type FetchFuzzyEventsResponse = {
  __typename?: 'FetchFuzzyEventsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<FuzzyEvent>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type FetchNonstandardInspectItem = {
  __typename?: 'FetchNonstandardInspectItem';
  exDesc?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<McUploadFile>>;
  gmtCreate?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  roomGuid?: Maybe<Scalars['String']['output']>;
  subjectName?: Maybe<Scalars['String']['output']>;
  taskNo?: Maybe<Scalars['String']['output']>;
};

export type FetchNonstandardInspectItemQ = {
  roomGuid: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};

export type FetchNonstandardInspectItemResponse = {
  __typename?: 'FetchNonstandardInspectItemResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<Maybe<FetchNonstandardInspectItem>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type FetchSchedulePageTurningIdQ = {
  curId: Scalars['Int']['input'];
  jobTypeList: Array<Scalars['String']['input']>;
  subJobType?: InputMaybe<Scalars['String']['input']>;
};

export type FetchSchedulePageTurningIdResponse = {
  __typename?: 'FetchSchedulePageTurningIdResponse';
  lastId?: Maybe<Scalars['Int']['output']>;
  nextId?: Maybe<Scalars['Int']['output']>;
};

export type FetchTicketsByRoomQuery = {
  roomGuid: Scalars['String']['input'];
  taskType: Scalars['String']['input'];
};

export type FetchTicketsByRoomResponse = {
  __typename?: 'FetchTicketsByRoomResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<Maybe<Scalars['String']['output']>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type File = {
  __typename?: 'File';
  encoding: Scalars['String']['output'];
  fileFormat?: Maybe<Scalars['String']['output']>;
  filePath?: Maybe<Scalars['String']['output']>;
  fileSize?: Maybe<Scalars['Long']['output']>;
  filename: Scalars['String']['output'];
  mimetype: Scalars['String']['output'];
  uploadBy?: Maybe<Scalars['Long']['output']>;
  uploadByName?: Maybe<Scalars['String']['output']>;
  uploadedAt?: Maybe<Scalars['Long']['output']>;
};

export type FileInfo = {
  fileFormat?: InputMaybe<Scalars['String']['input']>;
  fileName?: InputMaybe<Scalars['String']['input']>;
  filePath?: InputMaybe<Scalars['String']['input']>;
  fileSize?: InputMaybe<Scalars['Long']['input']>;
  gmtCreate?: InputMaybe<Scalars['Long']['input']>;
  uploadBy?: InputMaybe<Scalars['Long']['input']>;
  uploadByName?: InputMaybe<Scalars['String']['input']>;
  uploadTime?: InputMaybe<Scalars['Long']['input']>;
};

export type FileItem = {
  __typename?: 'FileItem';
  encoding: Scalars['String']['output'];
  fileFormat?: Maybe<Scalars['String']['output']>;
  fileName: Scalars['String']['output'];
  filePath?: Maybe<Scalars['String']['output']>;
  fileSize?: Maybe<Scalars['Long']['output']>;
  uploadBy?: Maybe<Scalars['Long']['output']>;
  uploadByName?: Maybe<Scalars['String']['output']>;
  uploadedAt?: Maybe<Scalars['Long']['output']>;
};

export type FinishAlarmShieldResponse = {
  __typename?: 'FinishAlarmShieldResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type FinishDrillOrderRemarkParams = {
  execNo: Scalars['String']['input'];
  id: Scalars['Long']['input'];
};

export type FinishEventCurrentPhaseResponse = MutationResponse & {
  __typename?: 'FinishEventCurrentPhaseResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type FinishRackPowerOnOffData = {
  errorDescription?: InputMaybe<Scalars['String']['input']>;
  errorType?: InputMaybe<Scalars['String']['input']>;
  /** 使用发起请求时的时间即可（检查未通过时应传 null） */
  finishedAt?: InputMaybe<Scalars['Long']['input']>;
  gridGuid: Scalars['String']['input'];
  /** 机柜上下电检查未通过时传 false */
  success: Scalars['Boolean']['input'];
  taskNo: Scalars['String']['input'];
};

export type FinishRackPowerOnOffResponse = MutationResponse & {
  __typename?: 'FinishRackPowerOnOffResponse';
  code?: Maybe<Scalars['String']['output']>;
  countdown?: Maybe<CountdownInfo>;
  finishedAt?: Maybe<Scalars['Long']['output']>;
  gridGuid: Scalars['String']['output'];
  gridSuccess: Scalars['Boolean']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type FormattedContent = {
  __typename?: 'FormattedContent';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type FrontRack = {
  __typename?: 'FrontRack';
  deviceGuid: Scalars['String']['output'];
  deviceName: Scalars['String']['output'];
  deviceTag: Scalars['String']['output'];
  deviceType: Scalars['String']['output'];
  extendPosition?: Maybe<Scalars['String']['output']>;
  serialNumber?: Maybe<Scalars['String']['output']>;
};

export type FullLevelsDeviceTypesRecord = {
  __typename?: 'FullLevelsDeviceTypesRecord';
  level1?: Maybe<SimpleDeviceTypeRecord>;
  level2?: Maybe<SimpleDeviceTypeRecord>;
  self?: Maybe<SimpleDeviceTypeRecord>;
};

export type FuzzyEvent = {
  __typename?: 'FuzzyEvent';
  blockTag?: Maybe<Scalars['String']['output']>;
  closeTime?: Maybe<Scalars['Long']['output']>;
  createUserId?: Maybe<Scalars['String']['output']>;
  eventDesc?: Maybe<Scalars['String']['output']>;
  eventLevel?: Maybe<Scalars['String']['output']>;
  eventLevelName?: Maybe<Scalars['String']['output']>;
  eventNo?: Maybe<Scalars['String']['output']>;
  eventOwnerId?: Maybe<Scalars['Long']['output']>;
  eventOwnerName?: Maybe<Scalars['String']['output']>;
  eventSource?: Maybe<Scalars['String']['output']>;
  eventSourceName?: Maybe<Scalars['String']['output']>;
  eventStatus?: Maybe<FuzzyEventStatus>;
  eventTitle?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  isFalseAlarm?: Maybe<Scalars['String']['output']>;
  northSync?: Maybe<Scalars['Boolean']['output']>;
  occurTime?: Maybe<Scalars['Long']['output']>;
  relieveDesc?: Maybe<Scalars['String']['output']>;
  resolveDesc?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
};

export type FuzzyEventStatus = {
  __typename?: 'FuzzyEventStatus';
  code?: Maybe<Scalars['String']['output']>;
  desc?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusCode?: Maybe<Scalars['String']['output']>;
};

export enum Gender {
  Female = 'female',
  Male = 'male'
}

export type GetAlarmListQ = {
  alarmLevel?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  bizId?: InputMaybe<Scalars['String']['input']>;
  bizType?: InputMaybe<Scalars['String']['input']>;
  blockTags?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  changeId?: InputMaybe<Scalars['String']['input']>;
  deviceName?: InputMaybe<Scalars['String']['input']>;
  deviceTypeList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  isExpected?: InputMaybe<Scalars['Boolean']['input']>;
  isMerge?: InputMaybe<Scalars['Boolean']['input']>;
  isQueryData?: InputMaybe<Scalars['Boolean']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  triggerStatus?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
};

export type GetChangeAlarmCountQ = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  changeId?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
};

export type GetChangeAlarmDeviceTypeQ = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  changeId?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
};

export type GetChangeDetailStepOptionInfoQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  deviceType?: InputMaybe<Scalars['String']['input']>;
  stepId?: InputMaybe<Scalars['Long']['input']>;
};

export type GetChangeListQ = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  changeLevelList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  changeReasonList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  changeType?: InputMaybe<Scalars['String']['input']>;
  creatorId?: InputMaybe<Scalars['Long']['input']>;
  exeWay?: InputMaybe<Scalars['String']['input']>;
  executeEndTime?: InputMaybe<Scalars['Long']['input']>;
  executeStartTime?: InputMaybe<Scalars['Long']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  planEndTime?: InputMaybe<Scalars['Long']['input']>;
  planStartTime?: InputMaybe<Scalars['Long']['input']>;
  reason?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  statusList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type GetChangeListResponse = {
  __typename?: 'GetChangeListResponse';
  data?: Maybe<Array<Maybe<ChangeListItem>>>;
  pageNum?: Maybe<Scalars['Long']['output']>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type GetChangeOrderExeLogListQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
};

export type GetChangeOrderExeLogListResponse = {
  __typename?: 'GetChangeOrderExeLogListResponse';
  data?: Maybe<Array<Maybe<ChangeOrderExeLogListData>>>;
  pageNum?: Maybe<Scalars['Long']['output']>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type GetChangeOrderOfflineExeLogListQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
};

export type GetChangeOrderOfflineExeLogListResponse = {
  __typename?: 'GetChangeOrderOfflineExeLogListResponse';
  changeId?: Maybe<Scalars['String']['output']>;
  exeContent?: Maybe<Scalars['String']['output']>;
  exeTime?: Maybe<Scalars['Long']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  operatorId?: Maybe<Scalars['Long']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  order?: Maybe<Scalars['Long']['output']>;
};

export type GetFileInfosQ = {
  extensionType?: InputMaybe<Scalars['String']['input']>;
  targetId?: InputMaybe<Scalars['String']['input']>;
  targetType?: InputMaybe<Scalars['String']['input']>;
};

export type GetInfluenceQ = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  deviceGuidList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
};

export type GetRealtimeDataByPointGuidsQ = {
  deviceGuidMapping?: InputMaybe<Scalars['JSONObject']['input']>;
  idc?: InputMaybe<Scalars['String']['input']>;
  pointGuidMappings?: InputMaybe<Scalars['JSONObject']['input']>;
};

export type GetSingleTicketByTaskNoQ = {
  blockGuidList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  idcTagList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
  taskStatusList?: InputMaybe<Array<InputMaybe<TaskStatus>>>;
};

export type GetSingleTicketByTaskNoResponse = {
  __typename?: 'GetSingleTicketByTaskNoResponse';
  taskNo?: Maybe<Scalars['String']['output']>;
  taskStatus?: Maybe<Scalars['String']['output']>;
  taskType?: Maybe<Scalars['String']['output']>;
};

export type GetUrlResponse = {
  __typename?: 'GetUrlResponse';
  data?: Maybe<Scalars['String']['output']>;
};

export type GetUsersByResourceCodeQ = {
  containOneself?: InputMaybe<Scalars['Boolean']['input']>;
  resourceCode?: InputMaybe<Scalars['String']['input']>;
};

export type GetUsersByResourceCodeResponse = {
  __typename?: 'GetUsersByResourceCodeResponse';
  id?: Maybe<Scalars['Long']['output']>;
  loginName?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type GradeSummary = {
  __typename?: 'GradeSummary';
  BIZ?: Maybe<Scalars['Float']['output']>;
  DEVELOP?: Maybe<Scalars['Float']['output']>;
  grade?: Maybe<Scalars['Float']['output']>;
};

export type GradedUser = {
  __typename?: 'GradedUser';
  id: Scalars['Float']['output'];
  idc?: Maybe<SpaceLabelInValue>;
  name: Scalars['String']['output'];
  region?: Maybe<SpaceLabelInValue>;
  superiors?: Maybe<Array<SimpleUser>>;
};

export type GradesSummaries = {
  __typename?: 'GradesSummaries';
  FIR_HALF_YEAR?: Maybe<GradeSummary>;
  Q1?: Maybe<GradeSummary>;
  Q2?: Maybe<GradeSummary>;
  Q3?: Maybe<GradeSummary>;
  Q4?: Maybe<GradeSummary>;
  SEC_HALF_YEAR?: Maybe<GradeSummary>;
  YEAR?: Maybe<GradeSummary>;
  annualGrade?: Maybe<Scalars['Float']['output']>;
};

export type GridFee = {
  __typename?: 'GridFee';
  accountNo?: Maybe<Scalars['String']['output']>;
  adjustInstId?: Maybe<Scalars['String']['output']>;
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  chargeFee?: Maybe<Scalars['Float']['output']>;
  consumerDays?: Maybe<Scalars['Int']['output']>;
  endDate?: Maybe<Scalars['String']['output']>;
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  gridGuid: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  maxPower?: Maybe<Scalars['Float']['output']>;
  maxPowerTime?: Maybe<Scalars['String']['output']>;
  newlyAdd: Scalars['Boolean']['output'];
  originFee?: Maybe<Scalars['Float']['output']>;
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  ratedPower?: Maybe<Scalars['Float']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  roomGuid: Scalars['String']['output'];
  signedPower?: Maybe<Scalars['Float']['output']>;
  startDate?: Maybe<Scalars['String']['output']>;
  taxFee?: Maybe<Scalars['Float']['output']>;
  useAmount?: Maybe<Scalars['Float']['output']>;
};

export type GridFeesResponse = {
  __typename?: 'GridFeesResponse';
  data?: Maybe<Array<GridFee>>;
  total: Scalars['Long']['output'];
};

export type GridInfluence = {
  __typename?: 'GridInfluence';
  existingRoom?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  list?: Maybe<Array<Maybe<GridInfluenceItem>>>;
};

export type GridInfluenceItem = {
  __typename?: 'GridInfluenceItem';
  blockTag?: Maybe<Scalars['String']['output']>;
  gridCustomer?: Maybe<Scalars['String']['output']>;
  gridTag?: Maybe<Scalars['String']['output']>;
  gridType?: Maybe<Type>;
  roomTag?: Maybe<Scalars['String']['output']>;
};

export type GridRelateDevice = {
  __typename?: 'GridRelateDevice';
  assetNo?: Maybe<Scalars['String']['output']>;
  blockTag: Scalars['String']['output'];
  deviceGuid: Scalars['String']['output'];
  deviceLabel: Scalars['String']['output'];
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType: Scalars['String']['output'];
  extendPosition?: Maybe<Scalars['String']['output']>;
  idcTag: Scalars['String']['output'];
  powerLine?: Maybe<Scalars['String']['output']>;
  roomTag: Scalars['String']['output'];
};

export type GridRequirementInfo = {
  __typename?: 'GridRequirementInfo';
  num?: Maybe<Scalars['Float']['output']>;
  price?: Maybe<Scalars['String']['output']>;
  ratedPower?: Maybe<Scalars['Float']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type GridRequirementInfoInput = {
  num: Scalars['Float']['input'];
  price: Scalars['String']['input'];
  ratedPower: Scalars['Float']['input'];
  unit: Scalars['String']['input'];
};

export type GridType = {
  __typename?: 'GridType';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type GuaranteedFee = {
  __typename?: 'GuaranteedFee';
  adjustInstId?: Maybe<Scalars['String']['output']>;
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  chargeFee: Scalars['Float']['output'];
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Int']['output'];
  originFee: Scalars['Float']['output'];
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  taxFee?: Maybe<Scalars['Float']['output']>;
  useAmount: Scalars['Float']['output'];
};

export type GuaranteedFeesResponse = {
  __typename?: 'GuaranteedFeesResponse';
  data?: Maybe<Array<GuaranteedFee>>;
};

export type HistoryTrade = {
  __typename?: 'HistoryTrade';
  cooperateAmount?: Maybe<Scalars['Float']['output']>;
  cooperateBeginTime?: Maybe<Scalars['String']['output']>;
  cooperateEndTime?: Maybe<Scalars['String']['output']>;
  idcCompanyName: Scalars['String']['output'];
  itCap?: Maybe<Scalars['Float']['output']>;
  leaseNums?: Maybe<Scalars['Long']['output']>;
  projectAddress?: Maybe<Scalars['String']['output']>;
  projectName: Scalars['String']['output'];
  quotationTime?: Maybe<Scalars['String']['output']>;
  singleKwPriceAvg?: Maybe<Scalars['Float']['output']>;
  winBid?: Maybe<Scalars['Boolean']['output']>;
};

export type HistoryTradesResponse = {
  __typename?: 'HistoryTradesResponse';
  data: Array<HistoryTrade>;
  total: Scalars['Long']['output'];
};

export type HitRiskPointResponse = MutationResponse & {
  __typename?: 'HitRiskPointResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type HolidayBalance = {
  __typename?: 'HolidayBalance';
  carryOverAvailableBalance?: Maybe<Scalars['Float']['output']>;
  carryOverBalance?: Maybe<Scalars['Float']['output']>;
  carryOverUsedBalance?: Maybe<Scalars['Float']['output']>;
  companyAvailableBalance?: Maybe<Scalars['Float']['output']>;
  companyBalance?: Maybe<Scalars['Float']['output']>;
  companyUsedBalance?: Maybe<Scalars['Float']['output']>;
  sickSalaryAvailableBalance?: Maybe<Scalars['Float']['output']>;
  sickSalaryBalance?: Maybe<Scalars['Float']['output']>;
  sickSalaryUsedBalance?: Maybe<Scalars['Float']['output']>;
  statutoryAvailableBalance?: Maybe<Scalars['Float']['output']>;
  statutoryBalance?: Maybe<Scalars['Float']['output']>;
  statutoryUsedBalance?: Maybe<Scalars['Float']['output']>;
  totalAvailableBalance?: Maybe<Scalars['Float']['output']>;
  totalBalance?: Maybe<Scalars['Float']['output']>;
  totalUsedBalance?: Maybe<Scalars['Float']['output']>;
};

export type HrmBalance = {
  __typename?: 'HrmBalance';
  carryOverAvailableBalance?: Maybe<Scalars['Float']['output']>;
  carryOverBalance?: Maybe<Scalars['Float']['output']>;
  carryOverUsedBalance?: Maybe<Scalars['Float']['output']>;
  companyAvailableBalance?: Maybe<Scalars['Float']['output']>;
  companyBalance?: Maybe<Scalars['Float']['output']>;
  companyUsedBalance?: Maybe<Scalars['Float']['output']>;
  sickSalaryAvailableBalance?: Maybe<Scalars['Float']['output']>;
  sickSalaryBalance?: Maybe<Scalars['Float']['output']>;
  sickSalaryUsedBalance?: Maybe<Scalars['Float']['output']>;
  statutoryAvailableBalance?: Maybe<Scalars['Float']['output']>;
  statutoryBalance?: Maybe<Scalars['Float']['output']>;
  statutoryUsedBalance?: Maybe<Scalars['Float']['output']>;
  totalAvailableBalance?: Maybe<Scalars['Float']['output']>;
  totalBalance?: Maybe<Scalars['Float']['output']>;
  totalUsedBalance?: Maybe<Scalars['Float']['output']>;
};

export enum Isp {
  Mobile = 'MOBILE',
  Telecom = 'TELECOM',
  Unicom = 'UNICOM'
}

export type Idc = {
  __typename?: 'Idc';
  address: Scalars['String']['output'];
  backupOwners?: Maybe<Array<SimpleUser>>;
  city: LabelInValue;
  constructedAt: Scalars['Long']['output'];
  district: LabelInValue;
  guid: Scalars['String']['output'];
  name: Scalars['String']['output'];
  nation: LabelInValue;
  operationalAt: Scalars['Long']['output'];
  owner: SimpleUser;
  province: LabelInValue;
  region: LabelInValue;
  specs?: Maybe<Array<Scalars['JSONObject']['output']>>;
  status: IdcOperatingStatus;
  statusName: Scalars['String']['output'];
  type: Scalars['String']['output'];
  typeName: Scalars['String']['output'];
};

export type IdcCompanyDiffData = {
  __typename?: 'IdcCompanyDiffData';
  customerNums?: Maybe<Scalars['Long']['output']>;
  idcCompanyName: Scalars['String']['output'];
  projectNums?: Maybe<Scalars['Long']['output']>;
  province?: Maybe<Scalars['String']['output']>;
  provinceName?: Maybe<Scalars['String']['output']>;
  singleKwPriceAvg?: Maybe<Scalars['Float']['output']>;
};

export type IdcCompanyDiffDataResponse = {
  __typename?: 'IdcCompanyDiffDataResponse';
  data?: Maybe<Array<IdcCompanyDiffData>>;
};

export enum IdcOperatingStatus {
  Off = 'OFF',
  On = 'ON',
  PartOn = 'PART_ON'
}

export type IdcRacksStatistics = {
  __typename?: 'IdcRacksStatistics';
  ISP?: Maybe<Scalars['String']['output']>;
  authApprovalPrice?: Maybe<Scalars['Float']['output']>;
  authApprovalPriceAuthorized: Scalars['Boolean']['output'];
  availableITCapacity?: Maybe<Scalars['Float']['output']>;
  availableRacksCount?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  salesGuidePrice?: Maybe<Scalars['Float']['output']>;
  salesGuidePriceAuthorized: Scalars['Boolean']['output'];
  status: Scalars['String']['output'];
  statusName: Scalars['String']['output'];
  tag: Scalars['String']['output'];
};

export type InfluenceCount = {
  __typename?: 'InfluenceCount';
  customAccounted?: Maybe<Scalars['Long']['output']>;
  customerInfluence?: Maybe<Scalars['Long']['output']>;
  gridAccounted?: Maybe<Scalars['Long']['output']>;
  gridInfluence?: Maybe<Scalars['Long']['output']>;
};

export type InfluenceResponse = {
  __typename?: 'InfluenceResponse';
  customerInfluence?: Maybe<Array<Maybe<CustomerInfluenceItem>>>;
  deviceInfluence?: Maybe<DeviceInfluence>;
  gridInfluence?: Maybe<GridInfluence>;
  influenceCount?: Maybe<InfluenceCount>;
};

export type InfluenceScopeInput = {
  influenceGuid: Scalars['String']['input'];
  influenceType: Scalars['String']['input'];
};

export enum InfoType {
  Device = 'DEVICE',
  Other = 'OTHER',
  Room = 'ROOM'
}

export type InspectBatchCheckNormalQ = {
  checkItemName?: InputMaybe<Scalars['String']['input']>;
  checkSubjectGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type InspectBatchCheckNormalResponse = MutationResponse & {
  __typename?: 'InspectBatchCheckNormalResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<InspectBatchCheckNormalResponseData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InspectBatchCheckNormalResponseData = {
  __typename?: 'InspectBatchCheckNormalResponseData';
  checkItemName?: Maybe<Scalars['String']['output']>;
  checkSubjectGuid?: Maybe<Scalars['String']['output']>;
};

export type InspectionBatchCheckPointNormalQ = {
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type InspectionBatchCheckPointNormalResponse = MutationResponse & {
  __typename?: 'InspectionBatchCheckPointNormalResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  result?: Maybe<Result>;
  success: Scalars['Boolean']['output'];
};

export type InspectionItem = {
  __typename?: 'InspectionItem';
  assetNo?: Maybe<Scalars['String']['output']>;
  checkItems: Array<Maybe<CheckItem>>;
  checkItemsFinishedNum: Scalars['Long']['output'];
  checkSubjectGuid: Scalars['String']['output'];
  checkSubjectTag: Scalars['String']['output'];
  checkSubjectType: Scalars['String']['output'];
  deviceStatus?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  needCheckNum?: Maybe<Scalars['Long']['output']>;
  subTypeName: Scalars['String']['output'];
  supportCheckScenes?: Maybe<Scalars['Boolean']['output']>;
};

export type InspectionRoomDetailQuery = {
  roomGuid: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};

export type InspectionSaveCheckValueQuery = {
  checkSubjectGuid: Scalars['String']['input'];
  inspectItemId: Scalars['Int']['input'];
  isMeterRead: Scalars['Int']['input'];
  taskNo: Scalars['String']['input'];
  value: Scalars['String']['input'];
};

export type InspectionSaveCheckValueResponse = MutationResponse & {
  __typename?: 'InspectionSaveCheckValueResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  saveResult?: Maybe<InspectionSaveResult>;
  success: Scalars['Boolean']['output'];
};

export type InspectionSaveCustomizeChackValueQ = {
  inspectItemId?: InputMaybe<Scalars['Long']['input']>;
  isNormal?: InputMaybe<Scalars['Boolean']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
  value?: InputMaybe<Scalars['Double']['input']>;
};

export type InspectionSaveCustomizeCheckValueResponse = MutationResponse & {
  __typename?: 'InspectionSaveCustomizeCheckValueResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  saveResult?: Maybe<InspectionSaveCustomizeResult>;
  success: Scalars['Boolean']['output'];
};

export type InspectionSaveCustomizeResult = {
  __typename?: 'InspectionSaveCustomizeResult';
  checkSubjectGuid?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isNormal?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type InspectionSaveExceptionCheckValueQuery = {
  blockGuid: Scalars['String']['input'];
  checkSubjectGuid?: InputMaybe<Scalars['String']['input']>;
  checkSubjectTag?: InputMaybe<Scalars['String']['input']>;
  checkSubjectType?: InputMaybe<Scalars['String']['input']>;
  desc?: InputMaybe<Scalars['String']['input']>;
  deviceTag?: InputMaybe<Scalars['String']['input']>;
  eventId?: InputMaybe<Scalars['Long']['input']>;
  eventLevel?: InputMaybe<Scalars['String']['input']>;
  eventLevelName?: InputMaybe<Scalars['String']['input']>;
  fileInfoList?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  handleName?: InputMaybe<Scalars['String']['input']>;
  handleType?: InputMaybe<Scalars['String']['input']>;
  idcTag: Scalars['String']['input'];
  inspectItemId: Scalars['Int']['input'];
  itemName?: InputMaybe<Scalars['String']['input']>;
  pointValue?: InputMaybe<Scalars['Double']['input']>;
  secondHandleName?: InputMaybe<Scalars['String']['input']>;
  secondHandleType?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
  taskType?: InputMaybe<Scalars['String']['input']>;
  taskTypeName?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type InspectionSaveResult = {
  __typename?: 'InspectionSaveResult';
  checkSubjectGuid?: Maybe<Scalars['String']['output']>;
  errMsg?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  isNormal?: Maybe<Scalars['Boolean']['output']>;
  relateNo?: Maybe<Scalars['String']['output']>;
  relateOpType?: Maybe<Scalars['String']['output']>;
  relateType?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type InspectionScanQrLogQ = {
  subjectGuid?: InputMaybe<Scalars['String']['input']>;
  subjectType?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type InspectionScanQrLogResponse = MutationResponse & {
  __typename?: 'InspectionScanQrLogResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InspectionTicketRoomDetail = {
  __typename?: 'InspectionTicketRoomDetail';
  deviceTypes: Array<Maybe<LabelInValue>>;
  id: Scalars['ID']['output'];
  inspectionItems: Array<Maybe<InspectionItem>>;
};

export type InventoryCardFinishQ = {
  inventorySubType?: InputMaybe<Scalars['String']['input']>;
  roomGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type InventoryCardFinishResponse = MutationResponse & {
  __typename?: 'InventoryCardFinishResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InventoryCardValidFinishQ = {
  roomGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type InventoryCardValidFinishResponse = MutationResponse & {
  __typename?: 'InventoryCardValidFinishResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InventoryDevice = {
  __typename?: 'InventoryDevice';
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  haveException?: Maybe<Scalars['Boolean']['output']>;
  inventoryNum?: Maybe<Scalars['Long']['output']>;
  inventorySubjects?: Maybe<Array<Maybe<InventorySubjects>>>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type InventoryDeviceQ = {
  inventorySubType?: InputMaybe<Scalars['String']['input']>;
  numbered?: InputMaybe<Scalars['Boolean']['input']>;
  onlyException?: InputMaybe<Scalars['Boolean']['input']>;
  roomGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type InventoryDeviceResponse = {
  __typename?: 'InventoryDeviceResponse';
  deviceStatus?: Maybe<Array<Maybe<LabelInValue>>>;
  deviceTypes?: Maybe<Array<Maybe<LabelInValue>>>;
  inventorys?: Maybe<Array<Maybe<InventoryDevice>>>;
};

export type InventoryDeviceStatusInfo = {
  __typename?: 'InventoryDeviceStatusInfo';
  assetNo?: Maybe<Scalars['String']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceStatus?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  roomGuid?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  roomType?: Maybe<Scalars['String']['output']>;
  roomTypeName?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  serialNo?: Maybe<Scalars['String']['output']>;
  taskNo?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
};

export type InventoryDeviceStatusResponse = MutationResponse & {
  __typename?: 'InventoryDeviceStatusResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<InventoryDeviceStatusInfo>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InventoryDeviceUpdateQ = {
  assetNo?: InputMaybe<Scalars['String']['input']>;
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
  deviceType?: InputMaybe<Scalars['String']['input']>;
  fileInfos?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  inventoryStatus?: InputMaybe<Scalars['String']['input']>;
  productModel?: InputMaybe<Scalars['String']['input']>;
  remark?: InputMaybe<Scalars['String']['input']>;
  roomGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
  vendor?: InputMaybe<Scalars['String']['input']>;
};

export type InventoryDeviceUpdateResponse = MutationResponse & {
  __typename?: 'InventoryDeviceUpdateResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InventoryDeviceValidRangeQ = {
  assetNo: Scalars['String']['input'];
  productModel?: InputMaybe<Scalars['String']['input']>;
  roomGuid: Scalars['String']['input'];
  serialNo?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
  vendor?: InputMaybe<Scalars['String']['input']>;
};

export type InventoryDeviceValidRangeResponse = MutationResponse & {
  __typename?: 'InventoryDeviceValidRangeResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InventorySpareDeviceCountQ = {
  deviceType: Scalars['String']['input'];
  inventoryCount?: InputMaybe<Scalars['Long']['input']>;
  productModel?: InputMaybe<Scalars['String']['input']>;
  remark?: InputMaybe<Scalars['String']['input']>;
  roomGuid: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
  vendor?: InputMaybe<Scalars['String']['input']>;
};

export type InventorySpareDeviceCountResponse = MutationResponse & {
  __typename?: 'InventorySpareDeviceCountResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type InventorySubjects = {
  __typename?: 'InventorySubjects';
  assetNo?: Maybe<Scalars['String']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceStatus?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  files?: Maybe<Array<Maybe<FileItem>>>;
  idcTag?: Maybe<Scalars['String']['output']>;
  inventoryCount?: Maybe<Scalars['Long']['output']>;
  picUrl?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  roomGuid?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  roomType?: Maybe<Scalars['String']['output']>;
  roomTypeName?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  serialNo?: Maybe<Scalars['String']['output']>;
  shelves?: Maybe<Scalars['String']['output']>;
  taskNo: Scalars['String']['output'];
  topCategory?: Maybe<Scalars['String']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
};

export type Invoice = {
  __typename?: 'Invoice';
  address?: Maybe<Scalars['String']['output']>;
  bank?: Maybe<BankInfo>;
  id?: Maybe<Scalars['Long']['output']>;
  mobile?: Maybe<Scalars['String']['output']>;
  taxpayerIdentificationNumber: Scalars['String']['output'];
  title: Scalars['String']['output'];
};

export type InvoiceInput = {
  address?: InputMaybe<Scalars['String']['input']>;
  bank?: InputMaybe<BankInfoInput>;
  id?: InputMaybe<Scalars['Long']['input']>;
  mobile?: InputMaybe<Scalars['String']['input']>;
  taxpayerIdentificationNumber: Scalars['String']['input'];
  title: Scalars['String']['input'];
};

export type InvoiceRecord = {
  __typename?: 'InvoiceRecord';
  feeId: Scalars['Int']['output'];
  id: Scalars['Int']['output'];
  invoiceMethod: Scalars['String']['output'];
  relateNoList: Array<Scalars['String']['output']>;
};

export type InvoiceRecordsResponse = {
  __typename?: 'InvoiceRecordsResponse';
  data?: Maybe<Array<InvoiceRecord>>;
  total: Scalars['Long']['output'];
};

export type IsActivatedKeyMap = {
  __typename?: 'IsActivatedKeyMap';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type ItCapacity = {
  __typename?: 'ItCapacity';
  currentValue: Scalars['Float']['output'];
  previousValue?: Maybe<Scalars['Float']['output']>;
  status: Scalars['String']['output'];
};

export type ItemName = {
  __typename?: 'ItemName';
  exceptionNum?: Maybe<Scalars['Int']['output']>;
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  finishedNum?: Maybe<Scalars['Int']['output']>;
  id: Scalars['String']['output'];
  itemName?: Maybe<Scalars['String']['output']>;
  maintenanceMethods?: Maybe<Array<Maybe<MaintenanceMethod>>>;
  photosResult?: Maybe<Scalars['Long']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
};

export type JobItem = {
  __typename?: 'JobItem';
  gmtCreate: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  itemType: Scalars['String']['output'];
  jobId: Scalars['Int']['output'];
  jobTypeCode: Scalars['String']['output'];
  jobTypeName: Scalars['String']['output'];
  name: Scalars['String']['output'];
  scheduleId: Scalars['Int']['output'];
  scheduleType: Scalars['String']['output'];
  subJobTypeCode: Scalars['String']['output'];
  subJobTypeName: Scalars['String']['output'];
};

export type JobItemInput = {
  gmtCreate?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['Int']['input']>;
  itemType: Scalars['String']['input'];
  jobId: Scalars['Int']['input'];
  jobTypeCode: Scalars['String']['input'];
  jobTypeName: Scalars['String']['input'];
  name: Scalars['String']['input'];
  scheduleId?: InputMaybe<Scalars['Int']['input']>;
  scheduleType: Scalars['String']['input'];
  subJobTypeCode: Scalars['String']['input'];
  subJobTypeName: Scalars['String']['input'];
};

export type KwGuaranteedFee = {
  __typename?: 'KwGuaranteedFee';
  adjustInstId?: Maybe<Scalars['String']['output']>;
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  chargeFee: Scalars['Float']['output'];
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Int']['output'];
  originFee: Scalars['Float']['output'];
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo: Scalars['String']['output'];
  taxFee?: Maybe<Scalars['Float']['output']>;
  useAmount: Scalars['Float']['output'];
};

export type KwGuaranteedFeesResponse = {
  __typename?: 'KwGuaranteedFeesResponse';
  data?: Maybe<Array<KwGuaranteedFee>>;
};

export type LabelInValue = {
  __typename?: 'LabelInValue';
  key: Scalars['String']['output'];
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type LeaveInfo = {
  __typename?: 'LeaveInfo';
  endTime?: Maybe<Scalars['Float']['output']>;
  endTimeStr?: Maybe<Scalars['String']['output']>;
  startTime?: Maybe<Scalars['Float']['output']>;
  startTimeStr?: Maybe<Scalars['String']['output']>;
  totalTime?: Maybe<Scalars['Float']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export type LocateEventResponse = MutationResponse & {
  __typename?: 'LocateEventResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export enum LogLevel {
  Debug = 'debug',
  Error = 'error',
  Info = 'info',
  Warn = 'warn'
}

export type LoggedInUser = {
  __typename?: 'LoggedInUser';
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
};

export type LoggerMutationResponse = MutationResponse & {
  __typename?: 'LoggerMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Login = {
  __typename?: 'Login';
  company?: Maybe<Scalars['String']['output']>;
  deptId?: Maybe<Scalars['String']['output']>;
  deptName?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  loginName: Scalars['String']['output'];
  mobile?: Maybe<Scalars['String']['output']>;
  roleCode?: Maybe<Scalars['String']['output']>;
  roleId?: Maybe<Scalars['Long']['output']>;
  userName: Scalars['String']['output'];
};

export type LoginByTokenMutationResponse = MutationResponse & {
  __typename?: 'LoginByTokenMutationResponse';
  appIdentity?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  sessionId?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  token?: Maybe<Scalars['String']['output']>;
  user?: Maybe<Login>;
};

export type LoginMutationResponse = MutationResponse & {
  __typename?: 'LoginMutationResponse';
  appIdentity?: Maybe<Scalars['String']['output']>;
  code?: Maybe<Scalars['String']['output']>;
  expiredAt?: Maybe<Scalars['Long']['output']>;
  isRequiredChangePassword?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  sessionId?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  token?: Maybe<Scalars['String']['output']>;
  user?: Maybe<Login>;
};

export type LogoutMutationResponse = MutationResponse & {
  __typename?: 'LogoutMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type MaintenanceBatchCheckNormalQ = {
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
  itemName?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type MaintenanceBatchCheckNormalResponse = MutationResponse & {
  __typename?: 'MaintenanceBatchCheckNormalResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<MaintenanceBatchCheckNormalResponseData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type MaintenanceBatchCheckNormalResponseData = {
  __typename?: 'MaintenanceBatchCheckNormalResponseData';
  deviceGuid?: Maybe<Scalars['String']['output']>;
  itemName?: Maybe<Scalars['String']['output']>;
};

export type MaintenanceItem = {
  __typename?: 'MaintenanceItem';
  assetNo?: Maybe<Scalars['String']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  finishedNum?: Maybe<Scalars['Int']['output']>;
  id?: Maybe<Scalars['ID']['output']>;
  maintenanceMethodNum: Scalars['Long']['output'];
  maintenanceTypes?: Maybe<Array<Maybe<MaintenanceType>>>;
  roomGuid?: Maybe<Scalars['String']['output']>;
};

export type MaintenanceMethod = {
  __typename?: 'MaintenanceMethod';
  id: Scalars['String']['output'];
  lowerLimitValue?: Maybe<Scalars['Double']['output']>;
  maintenanceMethod?: Maybe<Scalars['String']['output']>;
  maintenanceStd?: Maybe<Scalars['String']['output']>;
  maintenanceValue?: Maybe<Scalars['Double']['output']>;
  recordMethod?: Maybe<Scalars['String']['output']>;
  tools?: Maybe<Scalars['String']['output']>;
  upperLimitValue?: Maybe<Scalars['Double']['output']>;
  valueUnit?: Maybe<Scalars['String']['output']>;
};

export type MaintenanceSaveCheckValueQuery = {
  maintenanceItemId: Scalars['Long']['input'];
  maintenanceType?: InputMaybe<Scalars['String']['input']>;
  maintenanceValue: Scalars['Double']['input'];
  taskNo: Scalars['String']['input'];
};

export type MaintenanceSaveCheckValueResponse = MutationResponse & {
  __typename?: 'MaintenanceSaveCheckValueResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  saveResult?: Maybe<MaintenanceSaveResult>;
  success: Scalars['Boolean']['output'];
};

export type MaintenanceSaveExceptionCheckValueQuery = {
  blockGuid: Scalars['String']['input'];
  checkSubjectTag?: InputMaybe<Scalars['String']['input']>;
  desc?: InputMaybe<Scalars['String']['input']>;
  deviceGuid: Scalars['String']['input'];
  deviceTag?: InputMaybe<Scalars['String']['input']>;
  deviceType: Scalars['String']['input'];
  eventId?: InputMaybe<Scalars['Long']['input']>;
  eventLevel?: InputMaybe<Scalars['String']['input']>;
  eventLevelName?: InputMaybe<Scalars['String']['input']>;
  fileInfoList?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  handleName?: InputMaybe<Scalars['String']['input']>;
  handleType?: InputMaybe<Scalars['String']['input']>;
  idcTag: Scalars['String']['input'];
  infoType?: InputMaybe<Scalars['String']['input']>;
  itemName?: InputMaybe<Scalars['String']['input']>;
  maintenanceItemId: Scalars['Int']['input'];
  maintenanceType?: InputMaybe<Scalars['String']['input']>;
  maintenanceValue: Scalars['Double']['input'];
  secondHandleName?: InputMaybe<Scalars['String']['input']>;
  secondHandleType?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
  taskType?: InputMaybe<Scalars['String']['input']>;
  taskTypeName?: InputMaybe<Scalars['String']['input']>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type MaintenanceSaveResult = {
  __typename?: 'MaintenanceSaveResult';
  isNormal: Scalars['Boolean']['output'];
  maintenanceItemId?: Maybe<Scalars['Long']['output']>;
  maintenanceValue: Scalars['String']['output'];
};

export type MaintenanceTicketRoomDetail = {
  __typename?: 'MaintenanceTicketRoomDetail';
  deviceTypes: Array<Maybe<LabelInValue>>;
  roomDetail?: Maybe<Array<Maybe<RoomDetail>>>;
};

export type MaintenanceType = {
  __typename?: 'MaintenanceType';
  id: Scalars['String']['output'];
  itemNames?: Maybe<Array<Maybe<ItemName>>>;
  maintenanceType?: Maybe<Scalars['String']['output']>;
};

export type ManageTypeKeyMap = {
  __typename?: 'ManageTypeKeyMap';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type MarkSalesOpportunityTaskAsCompletedResponse = {
  __typename?: 'MarkSalesOpportunityTaskAsCompletedResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type MatchObjectInfoList = {
  __typename?: 'MatchObjectInfoList';
  code?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type McUploadFile = {
  __typename?: 'McUploadFile';
  ext: Scalars['String']['output'];
  id?: Maybe<Scalars['Int']['output']>;
  modifiedAt?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  patialPath: Scalars['String']['output'];
  size?: Maybe<Scalars['Long']['output']>;
  src: Scalars['String']['output'];
  targetId?: Maybe<Scalars['String']['output']>;
  targetType?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  uid: Scalars['String']['output'];
  uploadUser: SimpleUser;
  uploadedAt: Scalars['Long']['output'];
};

export type McUploadFileInput = {
  ext: Scalars['String']['input'];
  id?: InputMaybe<Scalars['Int']['input']>;
  modifiedAt?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  patialPath: Scalars['String']['input'];
  size?: InputMaybe<Scalars['Long']['input']>;
  src: Scalars['String']['input'];
  targetId?: InputMaybe<Scalars['String']['input']>;
  targetType?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  uid: Scalars['String']['input'];
  uploadUser: SimpleUserInput;
  uploadedAt: Scalars['Long']['input'];
};

export type MeasureJson = {
  __typename?: 'MeasureJson';
  completeTime?: Maybe<Scalars['String']['output']>;
  followUserId: Scalars['Long']['output'];
  measureDesc: Scalars['String']['output'];
  measureId: Scalars['Long']['output'];
  measureStatus: MeasureStatus;
  measureType: MeasureType;
  planCompleteTime: Scalars['String']['output'];
  riskId: Scalars['String']['output'];
};

export enum MeasureStatus {
  Done = 'DONE',
  Undone = 'UNDONE'
}

export enum MeasureType {
  Long = 'LONG',
  Short = 'SHORT'
}

export type Menu = {
  __typename?: 'Menu';
  children?: Maybe<Array<Menu>>;
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
  parentCode?: Maybe<Scalars['String']['output']>;
};

export type Metadata = {
  __typename?: 'Metadata';
  code: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  parentCode?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type MetadataTree = {
  __typename?: 'MetadataTree';
  children?: Maybe<Array<MetadataTree>>;
  code: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
  parentCode?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type MissUser = {
  __typename?: 'MissUser';
  currId: Scalars['String']['output'];
  missId: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type MutateSalesOpportunityResponse = {
  __typename?: 'MutateSalesOpportunityResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  opportunityCode?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type MutateStorePropertiesParams = {
  type: Scalars['String']['input'];
  value?: InputMaybe<Scalars['String']['input']>;
};

export type Mutation = {
  __typename?: 'Mutation';
  adjustFee?: Maybe<AdjustFeeResponse>;
  applyCommentDrillOrder: UpdateDrillOrderResponse;
  batchAddHolidayBalance?: Maybe<BatchAddHolidayBalanceResponse>;
  batchAddHrmBalance?: Maybe<BatchAddHrmBalanceResponse>;
  batchAdjustFees?: Maybe<BatchAdjustFeesResponse>;
  batchHandOverPerformances?: Maybe<BatchHandOverPerformancesResponse>;
  batchMaintenanceCheckItems?: Maybe<BatchMaintenanceCheckItemsResponse>;
  batchOperateRiskPools?: Maybe<BatchOperateRiskPoolsResponse>;
  batchUpdateTaskStatus?: Maybe<BatchUpdateTaskStatusResponse>;
  batchUrgeRiskCheckTickets?: Maybe<BatchUrgeRiskCheckTicketsResponse>;
  cancelCollectProjectMapFavoite: CancelCollectProjectMapFavoiteResponse;
  /** 检查项人工验证 */
  changeCheckItemArtificialValidation?: Maybe<ChangeStepCheckResponse>;
  changeClose?: Maybe<ChangeCloseResponse>;
  /** 变更强行跳过步骤 */
  changeSkip?: Maybe<ChangeSkipResponse>;
  /** 变更开始操作 */
  changeStartStep?: Maybe<ChangeStartStepResponse>;
  /** 变更检查 */
  changeStepCheck?: Maybe<ChangeStepCheckResponse>;
  changeStepItemConfirm?: Maybe<ChangeStepItemConfirmResponse>;
  /** 扫码异常 */
  changeStepItemException?: Maybe<ChangeStepItemExceptionResponse>;
  /** 变更终止 */
  changeStop?: Maybe<ChangeStopResponse>;
  /** 变更结束步骤 */
  changeStopStep?: Maybe<ChangeStopStepResponse>;
  changeSummeryInfo?: Maybe<ChangeSummeryInfoResponse>;
  checkPowerDevice?: Maybe<CheckPowerDeviceResponse>;
  closeFailedTicketsByTaskNos?: Maybe<CloseTicketsByResponse>;
  closeTicket?: Maybe<CloseTicketResponse>;
  closeTicketsByQ?: Maybe<CloseTicketsByResponse>;
  closeTicketsByTaskNos?: Maybe<CloseTicketsByResponse>;
  collectProjectMapFavoite?: Maybe<CollectProjectMapFavoiteResponse>;
  commentDrillOrder: UpdateDrillOrderResponse;
  completeDrillOrder: UpdateDrillOrderResponse;
  /** 手动完成线下变更 */
  completeOfflineChange?: Maybe<CompleteOfflineChangeResponse>;
  confirmAllFees?: Maybe<ConfirmFeeResponse>;
  confirmFee?: Maybe<ConfirmFeeResponse>;
  createAlarmShield: CreateAlarmShieldResponse;
  createChangeByRiskRegister?: Maybe<CreateChangeByRiskRegisterResponse>;
  createChangeNotification?: Maybe<ChangeNotificationResponse>;
  createCustomer?: Maybe<CreateCustomerMutationResponse>;
  createCustomerWhiteList?: Maybe<CreateCustomerWhiteListMutationResponse>;
  createDrillConfig?: Maybe<CreateDrillConfigResponse>;
  createDrillOrder: CreateDrillOrderResponse;
  createDrillOrderRemark: UpdateDrillOrderRemarkResponse;
  createEventByChange?: Maybe<CreateEventByChangeResponse>;
  createEventProcessRecord?: Maybe<CreateEventRelateRiskTicketResponse>;
  createEventRelateRiskTicket?: Maybe<CreateEventRelateRiskTicketResponse>;
  createFee?: Maybe<CreateFeeResponse>;
  createNonstandardInspectItem?: Maybe<CreateNonstandardInspectItemResponse>;
  createProjectMap: CreateProjectMapResponse;
  createProjectMapFavoite: CreateProjectMapFavoiteResponse;
  createRiskCheckTicket?: Maybe<CreateRiskCheckTicketResponse>;
  createRiskPool?: Maybe<CreateRiskPoolResponse>;
  createRiskRegisterByChange?: Maybe<CreateRiskRegisterByChangeResponse>;
  createSalesOpportunityTask?: Maybe<CreateSalesOpportunityTaskMutationResponse>;
  createTask?: Maybe<CreateTaskResponse>;
  createTicketNotification?: Maybe<CreateTicketNotificationResponse>;
  deleteAlarmShield: DeleteAlarmShieldResponse;
  /** 删除线下变更添加执行记录 */
  deleteChangeOfflineExecuteLog?: Maybe<DeleteChangeOfflineExecuteLogResponse>;
  deleteChangeOrderDevice?: Maybe<DeleteChangeOrderDeviceResponse>;
  deleteCustomerWhiteList?: Maybe<DeleteCustomerWhiteListMutationResponse>;
  deleteDrillConfig?: Maybe<DeleteDrillConfigResponse>;
  deleteProjectMapFavoite: DeleteProjectMapFavoiteResponse;
  deleteRiskPool?: Maybe<DeleteRiskPoolResponse>;
  deleteRiskRegister?: Maybe<DeleteRiskRegisterResponse>;
  deleteTask?: Maybe<DeleteTaskResponse>;
  discardFee?: Maybe<DiscardFeeResponse>;
  downloadImg?: Maybe<Scalars['String']['output']>;
  editDrillConfig?: Maybe<EditDrillConfigResponse>;
  editFeeInvoice?: Maybe<FeeInvoicingResponse>;
  exWarehouseCancel?: Maybe<ExWarehouseCancelResponse>;
  exWarehouseDevice?: Maybe<ExWarehouseDeviceResponse>;
  feeInvoicing?: Maybe<FeeInvoicingResponse>;
  finishAlarmShield?: Maybe<FinishAlarmShieldResponse>;
  finishDrillOrderRemark: UpdateDrillOrderRemarkResponse;
  finishEventCurrentPhase?: Maybe<FinishEventCurrentPhaseResponse>;
  finishRackPowerOnOff?: Maybe<FinishRackPowerOnOffResponse>;
  hitRiskPoint?: Maybe<HitRiskPointResponse>;
  inspectBatchCheckNormal?: Maybe<InspectBatchCheckNormalResponse>;
  /** 抄表测点巡检项批量检查 */
  inspectionBatchCheckPointNormal?: Maybe<InspectionBatchCheckPointNormalResponse>;
  inspectionSaveCheckValue?: Maybe<InspectionSaveCheckValueResponse>;
  /** 巡检自定义抄表检查项结果保存 */
  inspectionSaveCustomizeCheckValue?: Maybe<InspectionSaveCustomizeCheckValueResponse>;
  inspectionSaveExceptionCheckValue?: Maybe<InspectionSaveCheckValueResponse>;
  /** 扫码日志记录 */
  inspectionScanQrLog?: Maybe<InspectionScanQrLogResponse>;
  inventoryCardFinish?: Maybe<InventoryCardFinishResponse>;
  inventoryCardValidFinish?: Maybe<InventoryCardValidFinishResponse>;
  inventoryDeviceStatus?: Maybe<InventoryDeviceStatusResponse>;
  inventoryDeviceUpdate?: Maybe<InventoryDeviceUpdateResponse>;
  inventoryDeviceValidRange?: Maybe<InventoryDeviceValidRangeResponse>;
  inventorySpareDeviceCount?: Maybe<InventorySpareDeviceCountResponse>;
  locateEvent?: Maybe<LocateEventResponse>;
  logger: LoggerMutationResponse;
  login?: Maybe<LoginMutationResponse>;
  loginByToken: LoginByTokenMutationResponse;
  logout?: Maybe<LogoutMutationResponse>;
  /** 维护批量设置正常 */
  maintenanceBatchCheckNormal?: Maybe<MaintenanceBatchCheckNormalResponse>;
  maintenanceSaveCheckValue?: Maybe<MaintenanceSaveCheckValueResponse>;
  maintenanceSaveExceptionCheckValue?: Maybe<MaintenanceSaveCheckValueResponse>;
  markSalesOpportunityTaskAsCompleted?: Maybe<MarkSalesOpportunityTaskAsCompletedResponse>;
  mutateSalesOpportunity?: Maybe<MutateSalesOpportunityResponse>;
  mutateVisitTicket: VisitTicketMutationResponse;
  needUploadChangeCustomerAgreement?: Maybe<NeedUploadChangeCustomerAgreementResponse>;
  objectionFee?: Maybe<ObjectionFeeResponse>;
  operateCustomerWhiteList?: Maybe<OperateCustomerWhiteListMutationResponse>;
  progressSalesOpportunity?: Maybe<ProgressSalesOpportunityMutationResponse>;
  redirectEvent?: Maybe<RedirectEventResponse>;
  renameProjectMapFavoite: RenameProjectMapFavoiteResponse;
  requestNewDeviceOnOffTicket?: Maybe<RequestNewDeviceOnOffTicketResponse>;
  /** 转派工单 */
  resendTicket?: Maybe<ResendTicketResponse>;
  returnedFee?: Maybe<ReturnedFeeResponse>;
  reviewDrillOrder: UpdateDrillOrderResponse;
  revokeAdjustFee?: Maybe<RevokeFeeResponse>;
  revokeChangeOfflineSummery?: Maybe<RevokeChangeOfflineSummeryResponse>;
  revokeFee?: Maybe<RevokeFeeResponse>;
  /** 保存文件 */
  saveFiles?: Maybe<SaveFilesResponse>;
  saveLatestUsedRole?: Maybe<SaveLatestUsedRoleMutationResponse>;
  /** 维护项添加文件 */
  saveMaintenanceItemFile?: Maybe<SaveMaintenanceItemFileResponse>;
  /** 维护项添加备注 */
  saveMaintenanceItemRemark?: Maybe<SaveMaintenanceItemRemarkResponse>;
  saveStoreProperties?: Maybe<StorePropertiesResponse>;
  singleTakeTicket?: Maybe<SingleTakeTicketResponse>;
  singleUpload: SingleUploadMutationResponse;
  skipEventCurrentPhase?: Maybe<SkipEventCurrentPhaseResponse>;
  startDrillOrder: UpdateDrillOrderResponse;
  startExecutionPowerGrid?: Maybe<StartExecutionPowerGridResponse>;
  stopDrillOrder: UpdateDrillOrderResponse;
  /** 线下变更添加执行记录 */
  submitChangeOfflineExecuteLog?: Maybe<SubmitChangeOfflineExecuteLogResponse>;
  submitChangeShiftCountersignDescription?: Maybe<SubmitChangeShiftCountersignDescriptionResponse>;
  switchInspectOfflineModel?: Maybe<SwitchInspectOfflineModelResponse>;
  switchRole: SwitchRoleMutationResponse;
  takeDrillOrder: UpdateDrillOrderResponse;
  takeTicketsByQ?: Maybe<TakeTicketsResponse>;
  takeTicketsByTaskNos?: Maybe<TakeTicketsResponse>;
  transferDrillOrder: UpdateDrillOrderResponse;
  transferInvoice?: Maybe<TransferInvoiceResponse>;
  transferSalesOpportunityOwner?: Maybe<TransferSalesOpportunityOwnerMutationResponse>;
  transferTicket?: Maybe<TransferTicketResponse>;
  updateAlarmShield: UpdateAlarmShieldResponse;
  updateAuthorizationRecord?: Maybe<UpdateAuthorizationRecordResponse>;
  updateChangeOrderDeviceAlarmStatus?: Maybe<UpdateChangeOrderDeviceAlarmStatusResponse>;
  updateChangeShiftConfig: UpdateChangeShiftConfigResponse;
  updateCustomer?: Maybe<UpdateCustomerMutationResponse>;
  updateCustomerWhiteList?: Maybe<UpdateCustomerWhiteListMutationResponse>;
  updateFeeAdditionalInfo?: Maybe<UpdateFeeAdditionalInfoResponse>;
  updateInspectOfflineData?: Maybe<UpdateInspectOfflineDataResponse>;
  updateInspectScanQrLog?: Maybe<UpdateInspectScanQrLogResponse>;
  /** 更新巡检对象开关机状态 */
  updateInspectionDeviceStatus?: Maybe<UpdateInspectionDeviceStatusResponse>;
  updateOrDeleteNonstandardInspectItem?: Maybe<UpdateOrDeleteNonstandardInspectItemResponse>;
  updatePowerGridTime?: Maybe<UpdatePowerGridTimeResponse>;
  updateProjectMap: UpdateProjectMapResponse;
  updateProjectMapQuotation: UpdateProjectMapQuotationResponse;
  updateRiskPool?: Maybe<UpdateRiskPoolResponse>;
  updateRiskPoolStatus?: Maybe<UpdateRiskPoolStatusResponse>;
  updateSalesRack?: Maybe<UpdateSalesRackResponse>;
  updateTask?: Maybe<UpdateTaskResponse>;
  uploadInspectItemFile?: Maybe<UploadInspectItemFileResponse>;
  uploadMaintenanceItemFile?: Maybe<UploadMaintenanceItemFileResponse>;
  warehouseInCreate?: Maybe<WarehouseInCreateResponse>;
  withoutReviewDrillOrder: UpdateDrillOrderResponse;
};


export type MutationAdjustFeeArgs = {
  adjustEndUseAmount?: InputMaybe<Scalars['Float']['input']>;
  adjustId: Scalars['Int']['input'];
  adjustItemType?: InputMaybe<Scalars['String']['input']>;
  adjustOriginFee?: InputMaybe<Scalars['Float']['input']>;
  adjustPrice?: InputMaybe<Scalars['Float']['input']>;
  adjustReason: Scalars['String']['input'];
  adjustResourceType?: InputMaybe<Scalars['String']['input']>;
  adjustStartUseAmount?: InputMaybe<Scalars['Float']['input']>;
  adjustType: Scalars['String']['input'];
  adjustUseAmount?: InputMaybe<Scalars['Float']['input']>;
  adjustValue: Scalars['String']['input'];
  billNo: Scalars['String']['input'];
  clearStartAndEndUseAmount?: InputMaybe<Scalars['Boolean']['input']>;
  epFormFile?: InputMaybe<Array<McUploadFileInput>>;
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  originValue: Scalars['String']['input'];
  resourceNo?: InputMaybe<Scalars['String']['input']>;
};


export type MutationApplyCommentDrillOrderArgs = {
  params: ApplyCommentDrillOrderParams;
};


export type MutationBatchAddHolidayBalanceArgs = {
  data: Array<UserHolidayBalance>;
  fileIdList: Array<McUploadFileInput>;
};


export type MutationBatchAddHrmBalanceArgs = {
  data: Array<UserHrmBalance>;
  fileIdList: Array<McUploadFileInput>;
};


export type MutationBatchAdjustFeesArgs = {
  adjustObjectRequireList: Array<AdjustObjectRequire>;
  adjustReason: Scalars['String']['input'];
  billNo: Scalars['String']['input'];
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  feeId: Scalars['Int']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
};


export type MutationBatchHandOverPerformancesArgs = {
  transfereeId: Scalars['Int']['input'];
  type: Scalars['String']['input'];
  year: Scalars['String']['input'];
};


export type MutationBatchMaintenanceCheckItemsArgs = {
  roomGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
};


export type MutationBatchOperateRiskPoolsArgs = {
  assigneeId: Scalars['Int']['input'];
  checkIdList: Array<Scalars['Int']['input']>;
};


export type MutationBatchUpdateTaskStatusArgs = {
  ids: Array<Scalars['Int']['input']>;
  status: Scalars['String']['input'];
};


export type MutationBatchUrgeRiskCheckTicketsArgs = {
  taskNos: Array<Scalars['String']['input']>;
};


export type MutationCancelCollectProjectMapFavoiteArgs = {
  folderId: Scalars['Long']['input'];
  idcProjectIds: Array<Scalars['Long']['input']>;
};


export type MutationChangeCheckItemArtificialValidationArgs = {
  changeCheckItemArtificialValidationQ?: InputMaybe<ChangeCheckItemArtificialValidationQ>;
};


export type MutationChangeCloseArgs = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationChangeSkipArgs = {
  changeSkipQ?: InputMaybe<ChangeSkipQ>;
};


export type MutationChangeStartStepArgs = {
  changeStartStepQ?: InputMaybe<ChangeStartStepQ>;
};


export type MutationChangeStepCheckArgs = {
  changeStepCheckQ?: InputMaybe<ChangeStepCheckQ>;
};


export type MutationChangeStepItemConfirmArgs = {
  changeStepItemConfirmQ?: InputMaybe<ChangeStepItemConfirmQ>;
};


export type MutationChangeStepItemExceptionArgs = {
  changeStepItemExceptionQ?: InputMaybe<ChangeStepItemExceptionQ>;
};


export type MutationChangeStopArgs = {
  changeStopQ?: InputMaybe<ChangeStopQ>;
};


export type MutationChangeStopStepArgs = {
  changeStopStepQ?: InputMaybe<ChangeStopStepQ>;
};


export type MutationChangeSummeryInfoArgs = {
  changeSummeryInfoQ?: InputMaybe<ChangeSummeryInfoQ>;
};


export type MutationCheckPowerDeviceArgs = {
  query: CheckPowerDeviceQ;
};


export type MutationCloseFailedTicketsByTaskNosArgs = {
  closeFailedTicketsQ?: InputMaybe<CloseFailedTicketsQ>;
};


export type MutationCloseTicketArgs = {
  closeTicketQ?: InputMaybe<CloseTicketQ>;
};


export type MutationCloseTicketsByQArgs = {
  closeTicketsByQ?: InputMaybe<CloseTicketsByQ>;
};


export type MutationCloseTicketsByTaskNosArgs = {
  closeTicketsQ?: InputMaybe<CloseTicketsQ>;
};


export type MutationCollectProjectMapFavoiteArgs = {
  folderIds?: InputMaybe<Array<Scalars['Long']['input']>>;
  folderNames?: InputMaybe<Array<Scalars['String']['input']>>;
  idcProjectIds: Array<Scalars['Long']['input']>;
};


export type MutationCommentDrillOrderArgs = {
  params: CommentDrillOrderParams;
};


export type MutationCompleteDrillOrderArgs = {
  execNo: Scalars['String']['input'];
};


export type MutationCompleteOfflineChangeArgs = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
};


export type MutationConfirmAllFeesArgs = {
  billNo: Scalars['String']['input'];
};


export type MutationConfirmFeeArgs = {
  feeId: Scalars['Int']['input'];
};


export type MutationCreateAlarmShieldArgs = {
  params: CreateAlarmShieldParams;
};


export type MutationCreateChangeByRiskRegisterArgs = {
  query: CreateChangeByRiskRegisterQ;
};


export type MutationCreateChangeNotificationArgs = {
  query: ChangeNotificationQuery;
};


export type MutationCreateCustomerArgs = {
  params: CreateCustomerParams;
};


export type MutationCreateCustomerWhiteListArgs = {
  blockGuidList: Array<Scalars['String']['input']>;
  company: Scalars['String']['input'];
  companyName: Scalars['String']['input'];
  customerType: Scalars['String']['input'];
  note?: InputMaybe<Scalars['String']['input']>;
  persons: Array<CustomerPerson>;
};


export type MutationCreateDrillConfigArgs = {
  effectDomain: Scalars['String']['input'];
  effectType: Scalars['String']['input'];
  excLevel: Scalars['String']['input'];
  excMajor: Scalars['String']['input'];
  excName: Scalars['String']['input'];
  exercisePlanStepList: Array<DrillPlanStep>;
};


export type MutationCreateDrillOrderArgs = {
  params: CreateDrillOrderParams;
};


export type MutationCreateDrillOrderRemarkArgs = {
  params: CreateDrillOrderRemarkParams;
};


export type MutationCreateEventByChangeArgs = {
  query: CreateEventByChangeQ;
};


export type MutationCreateEventProcessRecordArgs = {
  eventId: Scalars['Long']['input'];
  eventPhase: Scalars['String']['input'];
  handleContent: Scalars['String']['input'];
  handleTime: Scalars['Long']['input'];
  handlerId: Scalars['Int']['input'];
  spareInfo?: InputMaybe<SpareInput>;
};


export type MutationCreateEventRelateRiskTicketArgs = {
  blockGuid: Scalars['String']['input'];
  eventId: Scalars['Int']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  idcTag: Scalars['String']['input'];
  riskDesc: Scalars['String']['input'];
  riskLevel: Scalars['String']['input'];
  riskObject: RiskObjectInput;
  riskOwnerId: Scalars['Int']['input'];
  riskPriorityCode?: InputMaybe<Scalars['String']['input']>;
  riskResourceCode: Scalars['String']['input'];
  riskSecTypeCode: Scalars['String']['input'];
  riskTopTypeCode: Scalars['String']['input'];
};


export type MutationCreateFeeArgs = {
  amountUnit?: InputMaybe<Scalars['String']['input']>;
  billFeeDetailAddInfoList: Array<BillFeeDetailAddInfoInput>;
  billNo: Scalars['String']['input'];
  feeCode: Scalars['String']['input'];
  feeName: Scalars['String']['input'];
};


export type MutationCreateNonstandardInspectItemArgs = {
  query: CreateNonstandardInspectItemQ;
};


export type MutationCreateProjectMapArgs = {
  params: UpdateProjectMapParams;
};


export type MutationCreateProjectMapFavoiteArgs = {
  folderNames: Array<Scalars['String']['input']>;
};


export type MutationCreateRiskCheckTicketArgs = {
  blockGuid: Scalars['String']['input'];
  riskPointList: Array<Scalars['Int']['input']>;
  scopeFlag?: InputMaybe<Scalars['Int']['input']>;
  scopeType: Scalars['String']['input'];
  slaUnit: Scalars['String']['input'];
  taskSla: Scalars['Int']['input'];
  title: Scalars['String']['input'];
};


export type MutationCreateRiskPoolArgs = {
  query: CreateRiskPoolQ;
};


export type MutationCreateRiskRegisterByChangeArgs = {
  query: CreateRiskRegisterByChangeQ;
};


export type MutationCreateSalesOpportunityTaskArgs = {
  attachments?: InputMaybe<Array<McUploadFileInput>>;
  content: Scalars['String']['input'];
  deadline: Scalars['Long']['input'];
  executor: SalesOpportunityTaskExecutorInput;
  opportunityCode: Scalars['String']['input'];
  target: Scalars['String']['input'];
  type: SalesOpportunityTaskType;
};


export type MutationCreateTaskArgs = {
  allowTriggerTime: Array<Scalars['String']['input']>;
  blockScope: Array<PlanBlockInput>;
  cycles: CycleInput;
  endTime?: InputMaybe<Scalars['Long']['input']>;
  guidePeriod?: InputMaybe<Scalars['String']['input']>;
  jobItems: Array<JobItemInput>;
  jobSla: Scalars['Int']['input'];
  jobType: Scalars['String']['input'];
  manageType?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  periodUnit?: InputMaybe<Scalars['String']['input']>;
  schLevel?: InputMaybe<Scalars['String']['input']>;
  slaUnit: Scalars['String']['input'];
  splitors: Array<SplitorInput>;
  status: Scalars['String']['input'];
  subJobType: Scalars['String']['input'];
};


export type MutationCreateTicketNotificationArgs = {
  bizId: Scalars['String']['input'];
  bizStatus: Scalars['String']['input'];
  bizType: Scalars['String']['input'];
  blockGuidList?: InputMaybe<Array<Scalars['String']['input']>>;
  eventCode: Scalars['String']['input'];
  idcTag?: InputMaybe<Scalars['String']['input']>;
  reportChannel: Scalars['String']['input'];
  reportContent: Scalars['String']['input'];
  reportObjectList: Array<ReportObjectInput>;
};


export type MutationDeleteAlarmShieldArgs = {
  ids: Array<Scalars['Long']['input']>;
};


export type MutationDeleteChangeOfflineExecuteLogArgs = {
  id?: InputMaybe<Scalars['Long']['input']>;
};


export type MutationDeleteChangeOrderDeviceArgs = {
  query: DeleteChangeOrderDeviceQ;
};


export type MutationDeleteCustomerWhiteListArgs = {
  id: Scalars['Long']['input'];
};


export type MutationDeleteDrillConfigArgs = {
  id: Scalars['Long']['input'];
};


export type MutationDeleteProjectMapFavoiteArgs = {
  folderIds: Array<Scalars['Long']['input']>;
};


export type MutationDeleteRiskPoolArgs = {
  id: Scalars['Long']['input'];
};


export type MutationDeleteRiskRegisterArgs = {
  riskId: Scalars['String']['input'];
};


export type MutationDeleteTaskArgs = {
  id: Scalars['Long']['input'];
};


export type MutationDiscardFeeArgs = {
  feeId: Scalars['Int']['input'];
};


export type MutationDownloadImgArgs = {
  filePath?: InputMaybe<Scalars['String']['input']>;
};


export type MutationEditDrillConfigArgs = {
  effectDomain: Scalars['String']['input'];
  effectType: Scalars['String']['input'];
  excLevel: Scalars['String']['input'];
  excMajor: Scalars['String']['input'];
  excName: Scalars['String']['input'];
  exercisePlanStepList: Array<DrillPlanStep>;
  id: Scalars['Long']['input'];
};


export type MutationEditFeeInvoiceArgs = {
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  id: Scalars['Int']['input'];
  invoiceMethod: Scalars['String']['input'];
  relateNoList: Array<Scalars['String']['input']>;
};


export type MutationExWarehouseCancelArgs = {
  exWarehouseCancelQ?: InputMaybe<ExWarehouseCancelQ>;
};


export type MutationExWarehouseDeviceArgs = {
  exWarehouseDeviceQ?: InputMaybe<ExWarehouseDeviceQ>;
};


export type MutationFeeInvoicingArgs = {
  feeId: Scalars['Int']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  invoiceMethod: Scalars['String']['input'];
  relateNoList: Array<Scalars['String']['input']>;
};


export type MutationFinishAlarmShieldArgs = {
  ids: Array<Scalars['Long']['input']>;
};


export type MutationFinishDrillOrderRemarkArgs = {
  params: FinishDrillOrderRemarkParams;
};


export type MutationFinishEventCurrentPhaseArgs = {
  endTime: Scalars['Long']['input'];
  eventId: Scalars['Long']['input'];
  eventPhase: Scalars['String']['input'];
  handleContent: Scalars['String']['input'];
  handlerId: Scalars['Int']['input'];
  startTime: Scalars['Long']['input'];
};


export type MutationFinishRackPowerOnOffArgs = {
  data?: InputMaybe<FinishRackPowerOnOffData>;
};


export type MutationHitRiskPointArgs = {
  assigneeId: Scalars['Int']['input'];
  blockGuid: Scalars['String']['input'];
  checkId: Scalars['Int']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  idcTag: Scalars['String']['input'];
  riskCategory: Scalars['String']['input'];
  riskDesc: Scalars['String']['input'];
  riskLevel: Scalars['String']['input'];
  riskObjectName: Scalars['String']['input'];
  riskObjectType: Scalars['String']['input'];
  riskOwnerId: Scalars['Int']['input'];
  riskResourceCode: Scalars['String']['input'];
  riskType: Scalars['String']['input'];
};


export type MutationInspectBatchCheckNormalArgs = {
  inspectBatchCheckNormalQ?: InputMaybe<InspectBatchCheckNormalQ>;
};


export type MutationInspectionBatchCheckPointNormalArgs = {
  inspectionBatchCheckPointNormalQ?: InputMaybe<InspectionBatchCheckPointNormalQ>;
};


export type MutationInspectionSaveCheckValueArgs = {
  params: InspectionSaveCheckValueQuery;
};


export type MutationInspectionSaveCustomizeCheckValueArgs = {
  inspectionSaveCustomizeCheckValueQ?: InputMaybe<InspectionSaveCustomizeChackValueQ>;
};


export type MutationInspectionSaveExceptionCheckValueArgs = {
  params: InspectionSaveExceptionCheckValueQuery;
};


export type MutationInspectionScanQrLogArgs = {
  inspectionScanQrLogQ?: InputMaybe<InspectionScanQrLogQ>;
};


export type MutationInventoryCardFinishArgs = {
  inventoryCardFinishQ?: InputMaybe<InventoryCardFinishQ>;
};


export type MutationInventoryCardValidFinishArgs = {
  inventoryCardValidFinishQ?: InputMaybe<InventoryCardValidFinishQ>;
};


export type MutationInventoryDeviceStatusArgs = {
  inventoryDeviceStatusQ?: InputMaybe<InventoryDeviceValidRangeQ>;
};


export type MutationInventoryDeviceUpdateArgs = {
  inventoryDeviceUpdateQ?: InputMaybe<InventoryDeviceUpdateQ>;
};


export type MutationInventoryDeviceValidRangeArgs = {
  inventoryDeviceValidRangeQ?: InputMaybe<InventoryDeviceValidRangeQ>;
};


export type MutationInventorySpareDeviceCountArgs = {
  inventorySpareDeviceCountQ?: InputMaybe<InventorySpareDeviceCountQ>;
};


export type MutationLocateEventArgs = {
  detectReason: Scalars['String']['input'];
  detectTime: Scalars['Long']['input'];
  detectUserId: Scalars['Int']['input'];
  eventId: Scalars['Long']['input'];
};


export type MutationLoggerArgs = {
  message: Scalars['String']['input'];
  metas: Array<Scalars['String']['input']>;
  type: LogLevel;
};


export type MutationLoginArgs = {
  client?: InputMaybe<Scalars['String']['input']>;
  location?: InputMaybe<Scalars['String']['input']>;
  login?: InputMaybe<Scalars['String']['input']>;
  loginName?: InputMaybe<Scalars['String']['input']>;
  password?: InputMaybe<Scalars['String']['input']>;
  rsaPassword?: InputMaybe<Scalars['String']['input']>;
  serviceTicket?: InputMaybe<Scalars['String']['input']>;
  timestamp?: InputMaybe<Scalars['Long']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
  website?: InputMaybe<Scalars['String']['input']>;
};


export type MutationLoginByTokenArgs = {
  isRoleRequiredToGenerateAppIdentity?: InputMaybe<Scalars['Boolean']['input']>;
  website: Scalars['String']['input'];
};


export type MutationLogoutArgs = {
  location?: InputMaybe<Scalars['String']['input']>;
};


export type MutationMaintenanceBatchCheckNormalArgs = {
  maintenanceBatchCheckNormalQ?: InputMaybe<MaintenanceBatchCheckNormalQ>;
};


export type MutationMaintenanceSaveCheckValueArgs = {
  params?: InputMaybe<MaintenanceSaveCheckValueQuery>;
};


export type MutationMaintenanceSaveExceptionCheckValueArgs = {
  params?: InputMaybe<MaintenanceSaveExceptionCheckValueQuery>;
};


export type MutationMarkSalesOpportunityTaskAsCompletedArgs = {
  attachments?: InputMaybe<Array<McUploadFileInput>>;
  code: Scalars['String']['input'];
  executionMethod?: InputMaybe<Scalars['String']['input']>;
  feedbacks: Scalars['String']['input'];
};


export type MutationMutateSalesOpportunityArgs = {
  customer: CustomerInput;
  opportunity: OpportunityInput;
  owner: OwnerInput;
};


export type MutationMutateVisitTicketArgs = {
  allowedTimeRange: Array<Scalars['Long']['input']>;
  authorizedArea: Array<Scalars['String']['input']>;
  formId?: InputMaybe<Scalars['String']['input']>;
  idc: Scalars['String']['input'];
  purpose: Scalars['String']['input'];
  receptionistId?: InputMaybe<Scalars['Int']['input']>;
  receptionistName: Scalars['String']['input'];
  receptionistType: AnySimpleUserType;
  relatedTicketNumber?: InputMaybe<Scalars['String']['input']>;
  relatedTicketType?: InputMaybe<Scalars['String']['input']>;
  ticketNumber?: InputMaybe<Scalars['String']['input']>;
  title: Scalars['String']['input'];
  visitSecondaryType: Scalars['String']['input'];
  visitType: Scalars['String']['input'];
  visitorNotices?: InputMaybe<Array<VisitorNotifyInfoInput>>;
  visitors: Array<VisitorInput>;
};


export type MutationNeedUploadChangeCustomerAgreementArgs = {
  changeLevel: Scalars['String']['input'];
  type: NeedUploadChangeCustomerType;
};


export type MutationObjectionFeeArgs = {
  feeId: Scalars['Int']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  objectionNodeCode: Scalars['Int']['input'];
  objectionReason: Scalars['String']['input'];
};


export type MutationOperateCustomerWhiteListArgs = {
  id: Scalars['Long']['input'];
  type: Scalars['String']['input'];
};


export type MutationProgressSalesOpportunityArgs = {
  params?: InputMaybe<ProgressSalesOpportunityParams>;
};


export type MutationRedirectEventArgs = {
  eventId: Scalars['Long']['input'];
  eventPhase: Scalars['String']['input'];
  reason?: InputMaybe<Scalars['String']['input']>;
  targetUserId: Scalars['Int']['input'];
};


export type MutationRenameProjectMapFavoiteArgs = {
  folderId: Scalars['Long']['input'];
  renameFolderName: Scalars['String']['input'];
};


export type MutationRequestNewDeviceOnOffTicketArgs = {
  data?: InputMaybe<RequestNewDeviceOnOffTicketData>;
};


export type MutationResendTicketArgs = {
  resendTicketQ?: InputMaybe<ResendTicketQ>;
};


export type MutationReturnedFeeArgs = {
  feeId: Scalars['Int']['input'];
  returnedDate?: InputMaybe<Scalars['String']['input']>;
  returnedFee: Scalars['Float']['input'];
  returnedType: Scalars['String']['input'];
};


export type MutationReviewDrillOrderArgs = {
  params: ReviewDrillOrderParams;
};


export type MutationRevokeAdjustFeeArgs = {
  adjustId: Scalars['String']['input'];
};


export type MutationRevokeChangeOfflineSummeryArgs = {
  changeOrderId: Scalars['String']['input'];
};


export type MutationRevokeFeeArgs = {
  approvalInstId: Scalars['String']['input'];
  feeId: Scalars['Int']['input'];
};


export type MutationSaveFilesArgs = {
  saveFilesQ?: InputMaybe<SaveFilesQ>;
};


export type MutationSaveLatestUsedRoleArgs = {
  code: Scalars['String']['input'];
};


export type MutationSaveMaintenanceItemFileArgs = {
  saveMaintenanceItemFileQ?: InputMaybe<SaveMaintenanceItemFileQ>;
};


export type MutationSaveMaintenanceItemRemarkArgs = {
  saveMaintenanceItemRemarkQ?: InputMaybe<SaveMaintenanceItemRemarkQ>;
};


export type MutationSaveStorePropertiesArgs = {
  params: MutateStorePropertiesParams;
};


export type MutationSingleTakeTicketArgs = {
  singleTakeTicketQ?: InputMaybe<SingleTakeTicketQ>;
};


export type MutationSingleUploadArgs = {
  file: Scalars['Upload']['input'];
};


export type MutationSkipEventCurrentPhaseArgs = {
  eventId: Scalars['Long']['input'];
  eventPhase: Scalars['String']['input'];
};


export type MutationStartDrillOrderArgs = {
  execNo: Scalars['String']['input'];
};


export type MutationStartExecutionPowerGridArgs = {
  query: StartExecutionPowerGridQ;
};


export type MutationStopDrillOrderArgs = {
  params: StopDrillOrderParams;
};


export type MutationSubmitChangeOfflineExecuteLogArgs = {
  submitChangeOfflineExecuteLogQ?: InputMaybe<SubmitChangeOfflineExecuteLogQ>;
};


export type MutationSubmitChangeShiftCountersignDescriptionArgs = {
  query: SubmitChangeShiftCountersignDescriptionQ;
};


export type MutationSwitchInspectOfflineModelArgs = {
  query: SwitchInspectOfflineModelQ;
};


export type MutationSwitchRoleArgs = {
  code: Scalars['String']['input'];
};


export type MutationTakeDrillOrderArgs = {
  execNoList: Array<Scalars['String']['input']>;
};


export type MutationTakeTicketsByQArgs = {
  takeTicketsByQ?: InputMaybe<TakeTicketsByQ>;
};


export type MutationTakeTicketsByTaskNosArgs = {
  takeTicketsQ?: InputMaybe<TakeTicketsQ>;
};


export type MutationTransferDrillOrderArgs = {
  params: TransferDrillOrderParams;
};


export type MutationTransferInvoiceArgs = {
  feeId: Scalars['Int']['input'];
  targetUserId: Scalars['Int']['input'];
  targetUserName: Scalars['String']['input'];
};


export type MutationTransferSalesOpportunityOwnerArgs = {
  redirectInfoList: Array<RedirectInfo>;
};


export type MutationTransferTicketArgs = {
  params?: InputMaybe<TransferTicketQuery>;
};


export type MutationUpdateAlarmShieldArgs = {
  params: UpdateAlarmShieldParams;
};


export type MutationUpdateAuthorizationRecordArgs = {
  id: Scalars['Long']['input'];
  localDate: Scalars['String']['input'];
  resourceList?: InputMaybe<Array<Scalars['String']['input']>>;
  resourceType?: InputMaybe<Scalars['String']['input']>;
};


export type MutationUpdateChangeOrderDeviceAlarmStatusArgs = {
  query: UpdateChangeOrderDeviceAlarmStatusQ;
};


export type MutationUpdateChangeShiftConfigArgs = {
  query: UpdateChangeShiftConfigQ;
};


export type MutationUpdateCustomerArgs = {
  params: UpdateCustomerParams;
};


export type MutationUpdateCustomerWhiteListArgs = {
  company: Scalars['String']['input'];
  companyName: Scalars['String']['input'];
  email: Scalars['String']['input'];
  id: Scalars['Long']['input'];
  phoneNo: Scalars['String']['input'];
  resourceList: Array<Scalars['String']['input']>;
  username: Scalars['String']['input'];
};


export type MutationUpdateFeeAdditionalInfoArgs = {
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  id: Scalars['Int']['input'];
  remark?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};


export type MutationUpdateInspectOfflineDataArgs = {
  query: UpdateInspectOfflineDataQ;
};


export type MutationUpdateInspectScanQrLogArgs = {
  query: UpdateInspectScanQrLogQ;
};


export type MutationUpdateInspectionDeviceStatusArgs = {
  updateInspectionDeviceStatusQ?: InputMaybe<UpdateInspectionDeviceStatusQ>;
};


export type MutationUpdateOrDeleteNonstandardInspectItemArgs = {
  query: UpdateOrDeleteNonstandardInspectItemQ;
};


export type MutationUpdatePowerGridTimeArgs = {
  query: UpdatePowerGridTimeQ;
};


export type MutationUpdateProjectMapArgs = {
  params: UpdateProjectMapParams;
};


export type MutationUpdateProjectMapQuotationArgs = {
  params: UpdateProjectMapQuotationParams;
};


export type MutationUpdateRiskPoolArgs = {
  query: UpdateRiskPoolQ;
};


export type MutationUpdateRiskPoolStatusArgs = {
  query: UpdateRiskPoolStatusQ;
};


export type MutationUpdateSalesRackArgs = {
  attachments?: InputMaybe<Array<McUploadFileInput>>;
  authApprovalPrice?: InputMaybe<Scalars['Float']['input']>;
  availableITCapacity?: InputMaybe<Scalars['Float']['input']>;
  availableRacksCount?: InputMaybe<Scalars['Int']['input']>;
  idc: Scalars['String']['input'];
  saleGuidePrice?: InputMaybe<Scalars['Float']['input']>;
};


export type MutationUpdateTaskArgs = {
  allowTriggerTime: Array<Scalars['String']['input']>;
  blockScope: Array<PlanBlockInput>;
  cycles: CycleInput;
  endTime?: InputMaybe<Scalars['Long']['input']>;
  guidePeriod?: InputMaybe<Scalars['String']['input']>;
  id: Scalars['String']['input'];
  jobItems: Array<JobItemInput>;
  jobSla: Scalars['Int']['input'];
  jobType: Scalars['String']['input'];
  manageType?: InputMaybe<Scalars['String']['input']>;
  name: Scalars['String']['input'];
  periodUnit?: InputMaybe<Scalars['String']['input']>;
  schLevel?: InputMaybe<Scalars['String']['input']>;
  slaUnit: Scalars['String']['input'];
  splitors: Array<SplitorInput>;
  status: Scalars['String']['input'];
  subJobType: Scalars['String']['input'];
};


export type MutationUploadInspectItemFileArgs = {
  query: UploadInspectItemFileQ;
};


export type MutationUploadMaintenanceItemFileArgs = {
  query: UploadMaintenanceItemFileQ;
};


export type MutationWarehouseInCreateArgs = {
  warehouseInCreateQ?: InputMaybe<WarehouseInCreateQ>;
};


export type MutationWithoutReviewDrillOrderArgs = {
  execNo: Scalars['String']['input'];
};

export type MutationResponse = {
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type NeedUploadChangeCustomerAgreementResponse = MutationResponse & {
  __typename?: 'NeedUploadChangeCustomerAgreementResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export enum NeedUploadChangeCustomerType {
  CustomerAgree = 'CUSTOMER_AGREE',
  PlanTime = 'PLAN_TIME'
}

export type Notification = {
  __typename?: 'Notification';
  changeStatus?: Maybe<Scalars['String']['output']>;
  creatorId?: Maybe<Scalars['Long']['output']>;
  creatorName?: Maybe<Scalars['String']['output']>;
  gmtCreate?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  reportChannel?: Maybe<Scalars['String']['output']>;
  reportContent?: Maybe<Scalars['String']['output']>;
  reportObject?: Maybe<Array<Maybe<ReportObject>>>;
};

export type NotificationsResponse = {
  __typename?: 'NotificationsResponse';
  data?: Maybe<Array<Maybe<Notification>>>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type OpItem = {
  __typename?: 'OPItem';
  checkMethod?: Maybe<Scalars['String']['output']>;
  deviceInfoList?: Maybe<Array<Maybe<MatchObjectInfoList>>>;
  exeWay?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  identifyWay?: Maybe<Scalars['String']['output']>;
  ola?: Maybe<Scalars['Long']['output']>;
  olaUnit?: Maybe<Scalars['String']['output']>;
  opObjectCode?: Maybe<Scalars['String']['output']>;
  opObjectName?: Maybe<Scalars['String']['output']>;
  opType?: Maybe<Scalars['String']['output']>;
  operate?: Maybe<Scalars['String']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  pointValueText?: Maybe<Scalars['String']['output']>;
  roomList?: Maybe<Array<Maybe<MatchObjectInfoList>>>;
  stepDesc?: Maybe<Scalars['String']['output']>;
  stepName?: Maybe<Scalars['String']['output']>;
  stepOrder?: Maybe<Scalars['Long']['output']>;
  stepType?: Maybe<Scalars['String']['output']>;
};

export type ObjectionBackNodesResponse = {
  __typename?: 'ObjectionBackNodesResponse';
  data?: Maybe<Array<Scalars['Int']['output']>>;
};

export type ObjectionFeeResponse = MutationResponse & {
  __typename?: 'ObjectionFeeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ObjectionInfo = {
  __typename?: 'ObjectionInfo';
  applyPost: Scalars['String']['output'];
  applyStaffId: Scalars['String']['output'];
  applyStaffName: Scalars['String']['output'];
  createAt: Scalars['Long']['output'];
  id: Scalars['String']['output'];
  objectionReason: Scalars['String']['output'];
  sourceCheckStatus: Scalars['Int']['output'];
  targetCheckStatus: Scalars['Int']['output'];
};

export type ObjectionRecord = {
  __typename?: 'ObjectionRecord';
  feeCode: Scalars['String']['output'];
  feeId: Scalars['Int']['output'];
  feeName: Scalars['String']['output'];
  feeSource: Scalars['Int']['output'];
  objectionList: Array<ObjectionInfo>;
};

export type ObjectionRecordsResponse = {
  __typename?: 'ObjectionRecordsResponse';
  data?: Maybe<Array<ObjectionRecord>>;
  total: Scalars['Long']['output'];
};

export type OpenProjectMapFavoiteResponse = {
  __typename?: 'OpenProjectMapFavoiteResponse';
  data?: Maybe<Array<ProjectMap>>;
  total?: Maybe<Scalars['Long']['output']>;
};

export type OperateCustomerWhiteListMutationResponse = MutationResponse & {
  __typename?: 'OperateCustomerWhiteListMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type OperatorCoop = {
  __typename?: 'OperatorCoop';
  cooperated?: Maybe<Scalars['Boolean']['output']>;
  operatorClear?: Maybe<Scalars['Boolean']['output']>;
  operators?: Maybe<Array<ProjectMapOperatorCoopInfoOperators>>;
};

export type OperatorCoopInfo = {
  cooperated?: InputMaybe<Scalars['Boolean']['input']>;
  operatorClear?: InputMaybe<Scalars['Boolean']['input']>;
  operators?: InputMaybe<Array<Scalars['String']['input']>>;
};

export type OperatorInfo = {
  __typename?: 'OperatorInfo';
  departmentName?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type OpportunityInput = {
  chargeDate?: InputMaybe<Scalars['Long']['input']>;
  clueNo?: InputMaybe<Scalars['String']['input']>;
  code?: InputMaybe<Scalars['String']['input']>;
  contractFilingDate?: InputMaybe<Scalars['Long']['input']>;
  contractModel?: InputMaybe<Scalars['String']['input']>;
  contractTotalAmount?: InputMaybe<Scalars['Float']['input']>;
  customerRequirementInfo?: InputMaybe<CustomerRequirementInfoInput>;
  fileInfoList?: InputMaybe<Array<Scalars['JSONObject']['input']>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  monthlyIncome?: InputMaybe<Scalars['Float']['input']>;
  name: Scalars['String']['input'];
  regionCode?: InputMaybe<Scalars['String']['input']>;
  remark?: InputMaybe<Scalars['String']['input']>;
  source?: InputMaybe<Scalars['String']['input']>;
  status: Scalars['String']['input'];
  version?: InputMaybe<Scalars['Long']['input']>;
};

export enum OptStatus {
  Construct = 'CONSTRUCT',
  Operation = 'OPERATION',
  Plan = 'PLAN'
}

export enum Order {
  Asc = 'asc',
  Ascend = 'ascend',
  Desc = 'desc',
  Descend = 'descend'
}

export type OthersFee = {
  __typename?: 'OthersFee';
  addFeeInstId?: Maybe<Scalars['String']['output']>;
  addFeeInstStatus?: Maybe<Scalars['String']['output']>;
  adjustInstId?: Maybe<Scalars['String']['output']>;
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  amountUnit?: Maybe<Scalars['String']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  chargeFee?: Maybe<Scalars['Float']['output']>;
  endUseAmount?: Maybe<Scalars['Float']['output']>;
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  id: Scalars['Int']['output'];
  idcTag?: Maybe<Scalars['String']['output']>;
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  startUseAmount?: Maybe<Scalars['Float']['output']>;
  taxFee?: Maybe<Scalars['Float']['output']>;
  taxRate?: Maybe<Scalars['Float']['output']>;
  useAmount?: Maybe<Scalars['Float']['output']>;
};

export type OthersFeeResponse = {
  __typename?: 'OthersFeeResponse';
  data?: Maybe<Array<OthersFee>>;
};

export type OwnerInput = {
  deptId: Scalars['String']['input'];
  id: Scalars['Int']['input'];
  name: Scalars['String']['input'];
};

export type PaginatedAnnualPerformanceObjectivesQuery = {
  createTimeRange?: InputMaybe<Array<Scalars['Float']['input']>>;
  createUserId?: InputMaybe<Scalars['Int']['input']>;
  modifiedTimeRange?: InputMaybe<Array<Scalars['Float']['input']>>;
  name?: InputMaybe<Scalars['String']['input']>;
  page: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  performancePosition?: InputMaybe<Scalars['String']['input']>;
  resourceCodes?: InputMaybe<Array<Scalars['String']['input']>>;
  status?: InputMaybe<Scalars['String']['input']>;
  subType?: InputMaybe<BackendAnnualPerformanceObjectiveSubType>;
  type?: InputMaybe<BackendAnnualPerformanceObjectiveType>;
  way?: InputMaybe<BackendAnnualPerformanceObjectiveWay>;
};

export type PaginatedPerformanceGrade = {
  __typename?: 'PaginatedPerformanceGrade';
  data: Array<PerformanceGrade>;
  total: Scalars['Float']['output'];
};

export type PaginatedPerformanceGradesQuery = {
  createTimeRange?: InputMaybe<Array<Scalars['Float']['input']>>;
  createUserId?: InputMaybe<Scalars['Float']['input']>;
  idcTags?: InputMaybe<Array<Scalars['String']['input']>>;
  modifiedTimeRange?: InputMaybe<Array<Scalars['Float']['input']>>;
  modifiedUserId?: InputMaybe<Scalars['Float']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  occurTimeRange?: InputMaybe<Array<Scalars['Float']['input']>>;
  orderBy?: InputMaybe<PaginatedPerformanceGradesQueryArgOrderBy>;
  page: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  position?: InputMaybe<Scalars['String']['input']>;
  regionCodes?: InputMaybe<Array<Scalars['String']['input']>>;
  staffId?: InputMaybe<Scalars['Float']['input']>;
  subType?: InputMaybe<BackendAnnualPerformanceObjectiveSubType>;
  superiorId?: InputMaybe<Scalars['Float']['input']>;
  type?: InputMaybe<BackendAnnualPerformanceObjectiveType>;
};

export type PaginatedPerformanceGradesQueryArgOrderBy = {
  createdAt?: InputMaybe<SortOrder>;
  grade?: InputMaybe<SortOrder>;
  idc?: InputMaybe<SortOrder>;
  modifiedAt?: InputMaybe<SortOrder>;
  name?: InputMaybe<SortOrder>;
  occurTime?: InputMaybe<SortOrder>;
  position?: InputMaybe<SortOrder>;
  region?: InputMaybe<SortOrder>;
  subType?: InputMaybe<SortOrder>;
  type?: InputMaybe<SortOrder>;
  userId?: InputMaybe<SortOrder>;
};

export type PaginatedPerformanceObjectives = {
  __typename?: 'PaginatedPerformanceObjectives';
  data: Array<AnnualPerformanceObjective>;
  total?: Maybe<Scalars['Float']['output']>;
};

export type PaginatedPerformancePlans = {
  __typename?: 'PaginatedPerformancePlans';
  data: Array<PerformancePlan>;
  total: Scalars['Float']['output'];
};

export type PaginatedPerformancePlansOrderBy = {
  createdAt?: InputMaybe<Order>;
  modifiedAt?: InputMaybe<Order>;
};

export type PaginatedPerformancePlansQuery = {
  createTimeRange?: InputMaybe<Array<Scalars['Float']['input']>>;
  createUserId?: InputMaybe<Scalars['Int']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  orderBy?: InputMaybe<PaginatedPerformancePlansOrderBy>;
  page: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  positions?: InputMaybe<Array<Scalars['String']['input']>>;
  resourceCodes?: InputMaybe<Array<Scalars['String']['input']>>;
  splitRule?: InputMaybe<BackendAnnualPerformancePlanSplitRule>;
  year?: InputMaybe<Scalars['String']['input']>;
};

export type PaginatedPerformances = {
  __typename?: 'PaginatedPerformances';
  counts: Array<PerformancesCount>;
  data: Array<Performance>;
  total: Scalars['Long']['output'];
};

export type PaginatedPerformancesQuery = {
  blocks?: InputMaybe<Array<Scalars['String']['input']>>;
  currentHandlerId?: InputMaybe<Scalars['Int']['input']>;
  evalDeadlineRange?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  evaluationJob?: InputMaybe<Scalars['String']['input']>;
  evaluationStatus?: InputMaybe<Array<Scalars['String']['input']>>;
  goalsDeadlineRange?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  goalsStatus?: InputMaybe<Array<Scalars['String']['input']>>;
  hiredAtRange?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  idcs?: InputMaybe<Array<Scalars['String']['input']>>;
  job?: InputMaybe<Scalars['String']['input']>;
  kpiSubType: Scalars['String']['input'];
  lineManagerIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  orderBy?: InputMaybe<PaginatedPerformancesQueryArgOrderBy>;
  page?: InputMaybe<Scalars['Int']['input']>;
  pageSize?: InputMaybe<Scalars['Int']['input']>;
  period?: InputMaybe<Scalars['String']['input']>;
  regionCodes?: InputMaybe<Array<Scalars['String']['input']>>;
  result?: InputMaybe<Scalars['String']['input']>;
  secondLineManagerIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  type: Scalars['String']['input'];
  userIds?: InputMaybe<Array<Scalars['Int']['input']>>;
  year?: InputMaybe<Scalars['String']['input']>;
};

export type PaginatedPerformancesQueryArgOrderBy = {
  evalDeadline?: InputMaybe<Order>;
  evaluationJob?: InputMaybe<Order>;
  evaluationStatus?: InputMaybe<Order>;
  goalsDeadline?: InputMaybe<Order>;
  goalsStatus?: InputMaybe<Order>;
  grade?: InputMaybe<Order>;
  gradeAvg?: InputMaybe<Order>;
  hiredAt?: InputMaybe<Order>;
  idc?: InputMaybe<Order>;
  job?: InputMaybe<Order>;
  lineManager?: InputMaybe<Order>;
  objectivesGrade?: InputMaybe<Order>;
  period?: InputMaybe<Order>;
  region?: InputMaybe<Order>;
  result?: InputMaybe<Order>;
  userId?: InputMaybe<Order>;
};

export type PaginatedSalesOpportunityTasks = {
  __typename?: 'PaginatedSalesOpportunityTasks';
  data: Array<SalesOpportunityTask>;
  total: Scalars['Int']['output'];
};

export type PduDevice = {
  __typename?: 'PduDevice';
  deviceGuid: Scalars['String']['output'];
  deviceName: Scalars['String']['output'];
  deviceTag: Scalars['String']['output'];
  deviceType: Scalars['String']['output'];
  extendPosition?: Maybe<Scalars['String']['output']>;
  serialNumber?: Maybe<Scalars['String']['output']>;
};

export type PduDevicesOnRackResponse = {
  __typename?: 'PduDevicesOnRackResponse';
  data?: Maybe<Array<DeviceOnRack>>;
  total: Scalars['Long']['output'];
};

export type Performance = {
  __typename?: 'Performance';
  allowEvaluationStartedAt?: Maybe<Scalars['Long']['output']>;
  canModifyEvaluationJob?: Maybe<Scalars['Boolean']['output']>;
  /** 以下字段仅在 paginatedPerformances resolver 中返回 */
  currentHandlers?: Maybe<Array<SimpleUser>>;
  currentStepIdx: Scalars['Int']['output'];
  evaluationExpiredAt: Scalars['Long']['output'];
  evaluationJob?: Maybe<EvaluationJob>;
  evaluationStartedAt: Scalars['Long']['output'];
  evaluationStatus?: Maybe<Scalars['String']['output']>;
  evaluations: Array<EvaluationStep>;
  expiredAt: Scalars['Long']['output'];
  grade?: Maybe<Scalars['Float']['output']>;
  gradeAvg?: Maybe<Scalars['Float']['output']>;
  gradesSummaries?: Maybe<GradesSummaries>;
  /** 以下字段仅在 myPerformances userAnnualPerformancesResolver 中返回 */
  haveTarget?: Maybe<Scalars['Boolean']['output']>;
  hrs?: Maybe<Array<SimpleUser>>;
  /** ID 可能为 null，表示后端组装出来的未落库的绩效 */
  id?: Maybe<Scalars['Int']['output']>;
  isLoggedInUserInCurrentStepUsers: Scalars['Boolean']['output'];
  lineManagers?: Maybe<Array<SimpleUser>>;
  objectiveExpiredAt: Scalars['Long']['output'];
  objectiveStartedAt: Scalars['Long']['output'];
  objectiveStatus?: Maybe<Scalars['String']['output']>;
  objectives: Array<PerformanceObjective>;
  objectivesGrade?: Maybe<Scalars['Float']['output']>;
  period?: Maybe<Scalars['String']['output']>;
  plan?: Maybe<SimplePerformancePlan>;
  /** @deprecated Replaced by 'plan' */
  planId?: Maybe<Scalars['Int']['output']>;
  result?: Maybe<Scalars['String']['output']>;
  rowKey: Scalars['String']['output'];
  secondLineManagers?: Maybe<Array<SimpleUser>>;
  startedAt: Scalars['Long']['output'];
  status?: Maybe<Scalars['String']['output']>;
  subType: Scalars['String']['output'];
  targetChangeType?: Maybe<Scalars['String']['output']>;
  /** 以下字段仅在 performance annualPerformanceResolver 中返回 */
  targetChangeValidityDateStr?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  user: PerformanceUser;
  year?: Maybe<Scalars['String']['output']>;
};

export type PerformanceGrade = {
  __typename?: 'PerformanceGrade';
  attachments?: Maybe<Array<McUploadFile>>;
  canModified?: Maybe<Scalars['Boolean']['output']>;
  createdAt?: Maybe<Scalars['Float']['output']>;
  createdBy?: Maybe<SimpleUser>;
  grade?: Maybe<Scalars['Float']['output']>;
  gradeDesc: Scalars['String']['output'];
  id?: Maybe<Scalars['Float']['output']>;
  measurements: Scalars['String']['output'];
  modifiedAt?: Maybe<Scalars['Float']['output']>;
  modifiedBy?: Maybe<SimpleUser>;
  name?: Maybe<Scalars['String']['output']>;
  occurTime?: Maybe<Scalars['Float']['output']>;
  performancePosition?: Maybe<PositionLabelInValue>;
  relatedObjectiveId?: Maybe<Scalars['Float']['output']>;
  subType: Scalars['String']['output'];
  type: Scalars['String']['output'];
  user: GradedUser;
};

export type PerformanceObjective = {
  __typename?: 'PerformanceObjective';
  content: Scalars['String']['output'];
  createdAt: Scalars['Long']['output'];
  finishedAt?: Maybe<Scalars['Long']['output']>;
  grade?: Maybe<Scalars['Float']['output']>;
  gradeDescriptions?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  measurements?: Maybe<Scalars['String']['output']>;
  modifiedAt: Scalars['Long']['output'];
  percent: Scalars['Float']['output'];
  selfEvaluation?: Maybe<Scalars['String']['output']>;
  startedAt?: Maybe<Scalars['Long']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type PerformancePlan = {
  __typename?: 'PerformancePlan';
  beforeSubTaskDeadlineNotifyDays?: Maybe<Array<Scalars['Int']['output']>>;
  canModified?: Maybe<Scalars['Boolean']['output']>;
  canSetResultDate: Scalars['Float']['output'];
  createdAt: Scalars['Float']['output'];
  createdBy: SimpleUser;
  id: Scalars['Int']['output'];
  modifiedAt?: Maybe<Scalars['Float']['output']>;
  name: Scalars['String']['output'];
  notEvalHiredDateLaterThanDate?: Maybe<Scalars['Float']['output']>;
  positionScope?: Maybe<Array<PositionLabelInValue>>;
  relatedObjectives?: Maybe<Array<Maybe<AnnualPerformanceObjective>>>;
  resourcesScope?: Maybe<Array<SpaceLabelInValue>>;
  splitRule: Scalars['String']['output'];
  subConfigs?: Maybe<Array<Maybe<PerformancePlanConfig>>>;
  year: Scalars['String']['output'];
};

export type PerformancePlanConfig = {
  __typename?: 'PerformancePlanConfig';
  evalStartTimeRange?: Maybe<Array<Maybe<Scalars['Float']['output']>>>;
  natureStartTimeRange?: Maybe<Array<Maybe<Scalars['Float']['output']>>>;
  period?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
};

export type PerformancePlanRelatedObjectivesQuery = {
  evalPosition?: InputMaybe<Scalars['String']['input']>;
  resourceCode?: InputMaybe<Scalars['String']['input']>;
};

export type PerformanceUser = {
  __typename?: 'PerformanceUser';
  blockGuid?: Maybe<Scalars['String']['output']>;
  hiredAt?: Maybe<Scalars['Long']['output']>;
  id: Scalars['Long']['output'];
  idc?: Maybe<Scalars['String']['output']>;
  job?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  region?: Maybe<Region>;
};

export type PerformancesCount = {
  __typename?: 'PerformancesCount';
  count: Scalars['Int']['output'];
  status?: Maybe<Scalars['String']['output']>;
};

export type Permission = {
  __typename?: 'Permission';
  allowAnonymous: Scalars['Boolean']['output'];
  code: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
  parentId?: Maybe<Scalars['Long']['output']>;
  remarks?: Maybe<Scalars['String']['output']>;
  routePath?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export enum PhasedIn {
  PhaseI = 'PHASE_I',
  PhaseIi = 'PHASE_II',
  PhaseIii = 'PHASE_III',
  PhaseIv = 'PHASE_IV',
  PhaseV = 'PHASE_V',
  Unknown = 'UNKNOWN'
}

export type PicTeam = {
  __typename?: 'PicTeam';
  teamCode: Scalars['String']['output'];
  teamName: Scalars['String']['output'];
};

export type PlanBlock = {
  __typename?: 'PlanBlock';
  blockGuid: Scalars['String']['output'];
  scopeFlag?: Maybe<Scalars['Int']['output']>;
  scopeType: Scalars['String']['output'];
};

export type PlanBlockInput = {
  blockGuid: Scalars['String']['input'];
  scopeFlag?: InputMaybe<Scalars['Int']['input']>;
  scopeType: Scalars['String']['input'];
};

export type PlanCreator = {
  __typename?: 'PlanCreator';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type PlanDayConfig = {
  __typename?: 'PlanDayConfig';
  days?: Maybe<Array<Scalars['String']['output']>>;
  isRemove: Scalars['Int']['output'];
};

export type PlanDayConfigInput = {
  days?: InputMaybe<Array<Scalars['String']['input']>>;
  isRemove: Scalars['Int']['input'];
};

export type PlanItem = {
  __typename?: 'PlanItem';
  checkItemList?: Maybe<Array<Maybe<CheckItemInfo>>>;
  opItem?: Maybe<OpItem>;
};

export type PlanModifyUser = {
  __typename?: 'PlanModifyUser';
  id?: Maybe<Scalars['Long']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type PointDataDuration = {
  __typename?: 'PointDataDuration';
  dataValue?: Maybe<Scalars['Float']['output']>;
  deviceGuid: Scalars['String']['output'];
  pointCode: Scalars['String']['output'];
  time: Scalars['String']['output'];
};

export type PointGuid = {
  __typename?: 'PointGuid';
  deviceGuid: Scalars['String']['output'];
  pointCode: Scalars['String']['output'];
};

export type PointGuidInput = {
  deviceGuid: Scalars['String']['input'];
  pointCode: Scalars['String']['input'];
};

export type Position = {
  __typename?: 'Position';
  latitude: Scalars['Float']['output'];
  longitude: Scalars['Float']['output'];
};

export type PositionLabelInValue = {
  __typename?: 'PositionLabelInValue';
  label?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export enum PowerPackStatus {
  ElecInclu = 'ELEC_INCLU',
  ElecNotInclu = 'ELEC_NOT_INCLU',
  Unknow = 'UNKNOW'
}

export type ProcessCommentInfo = {
  __typename?: 'ProcessCommentInfo';
  commentTime: Scalars['Long']['output'];
  content: Scalars['String']['output'];
  deleteTime?: Maybe<Scalars['Long']['output']>;
  id: Scalars['Int']['output'];
  isDeleted: Scalars['Boolean']['output'];
  personId: Scalars['Int']['output'];
  personName: Scalars['String']['output'];
};

export type ProcessRecord = {
  __typename?: 'ProcessRecord';
  ccUserList?: Maybe<Array<Maybe<CcUser>>>;
  isCountersignNode: Scalars['Boolean']['output'];
  nodeId: Scalars['String']['output'];
  nodeType: Scalars['String']['output'];
  operationName?: Maybe<Scalars['String']['output']>;
  operationTasks?: Maybe<Array<Maybe<ProcessTask>>>;
  operationType: Scalars['String']['output'];
  processCommentInfo?: Maybe<ProcessCommentInfo>;
};

export type ProcessTask = {
  __typename?: 'ProcessTask';
  date?: Maybe<Scalars['String']['output']>;
  missUser: Scalars['Boolean']['output'];
  missUserInfo?: Maybe<Array<Maybe<MissUser>>>;
  remark?: Maybe<Scalars['String']['output']>;
  taskFileInfoList?: Maybe<Array<Maybe<BackendMcUploadFile>>>;
  taskId?: Maybe<Scalars['String']['output']>;
  taskRedirectInfoList?: Maybe<Array<Maybe<TaskRedirectInfo>>>;
  taskResult?: Maybe<Scalars['String']['output']>;
  userList?: Maybe<Array<Maybe<ProcessUser>>>;
};

export type ProcessUser = {
  __typename?: 'ProcessUser';
  email?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  jobNo?: Maybe<Scalars['String']['output']>;
  mobile?: Maybe<Scalars['String']['output']>;
  userName: Scalars['String']['output'];
};

export type ProcessingReacrdResponse = {
  __typename?: 'ProcessingReacrdResponse';
  fileInfoList?: Maybe<Array<McUploadFile>>;
  handleContent: Scalars['String']['output'];
  handleTime: Scalars['String']['output'];
  handlerUserId: Scalars['Long']['output'];
  measureDesc: Scalars['String']['output'];
  measureId: Scalars['Long']['output'];
  measureStatus?: Maybe<MeasureStatus>;
  measureType?: Maybe<MeasureType>;
  optRecordId: Scalars['Long']['output'];
  riskId: Scalars['String']['output'];
};

export type ProgressSalesOpportunityMutationResponse = {
  __typename?: 'ProgressSalesOpportunityMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  progressionId?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ProgressSalesOpportunityParams = {
  content: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<Scalars['JSONObject']['input']>>;
  method?: InputMaybe<Scalars['String']['input']>;
  progressAt?: InputMaybe<Scalars['Long']['input']>;
  relateCode: Scalars['String']['input'];
};

export type ProjectDiffData = {
  __typename?: 'ProjectDiffData';
  companyPriceAvg?: Maybe<Scalars['Float']['output']>;
  idcCompanyName: Scalars['String']['output'];
  projectName: Scalars['String']['output'];
  provincePriceAvg?: Maybe<Scalars['Float']['output']>;
  sellableItLoad?: Maybe<Scalars['Float']['output']>;
  singleKwPrice?: Maybe<Scalars['Float']['output']>;
};

export type ProjectDiffDataResponse = {
  __typename?: 'ProjectDiffDataResponse';
  data: Array<ProjectDiffData>;
};

export type ProjectMap = {
  __typename?: 'ProjectMap';
  actualRackRate?: Maybe<Scalars['Long']['output']>;
  belongsGlp?: Maybe<Scalars['Boolean']['output']>;
  contractOccuRate?: Maybe<Scalars['Long']['output']>;
  county: Scalars['String']['output'];
  customizedIdcRoom?: Maybe<CustomizedIdc>;
  geoPosition?: Maybe<Scalars['String']['output']>;
  glpIdcGuid?: Maybe<Scalars['String']['output']>;
  idcCompanyName: Scalars['String']['output'];
  idcProjectId?: Maybe<Scalars['Long']['output']>;
  operatorCoopInfo?: Maybe<OperatorCoop>;
  optStatus: OptStatus;
  phased?: Maybe<Scalars['Boolean']['output']>;
  projectAddress: Scalars['String']['output'];
  projectArea: Scalars['String']['output'];
  projectName: Scalars['String']['output'];
  province: Scalars['String']['output'];
  quotationEditable?: Maybe<Scalars['Boolean']['output']>;
  sellStatus?: Maybe<SellStatus>;
  sellableItLoad?: Maybe<Scalars['Long']['output']>;
  sensitive?: Maybe<Scalars['Boolean']['output']>;
  singleKwPriceAvg?: Maybe<Scalars['Long']['output']>;
  starLevel?: Maybe<Scalars['String']['output']>;
};

export type ProjectMapAddedService = {
  __typename?: 'ProjectMapAddedService';
  id: Scalars['Long']['output'];
  quotationAmount?: Maybe<Scalars['Float']['output']>;
  serviceContent?: Maybe<Scalars['String']['output']>;
  serviceRemark?: Maybe<Scalars['String']['output']>;
  serviceType: ProjectMapServiceType;
};

export type ProjectMapAddress = {
  __typename?: 'ProjectMapAddress';
  city?: Maybe<Scalars['String']['output']>;
  county?: Maybe<Scalars['String']['output']>;
  countyName?: Maybe<Scalars['String']['output']>;
  projectAddress?: Maybe<Scalars['String']['output']>;
  projectArea?: Maybe<Scalars['String']['output']>;
  projectAreaName?: Maybe<Scalars['String']['output']>;
  province?: Maybe<Scalars['String']['output']>;
};

export type ProjectMapArea = {
  __typename?: 'ProjectMapArea';
  metaCode: Scalars['String']['output'];
  metaName: Scalars['String']['output'];
  metaType: Scalars['String']['output'];
  parentCode: Scalars['String']['output'];
};

export type ProjectMapAreaInfoResponse = {
  __typename?: 'ProjectMapAreaInfoResponse';
  areas?: Maybe<Array<ProjectMapArea>>;
  cities?: Maybe<Array<ProjectMapArea>>;
  provinces?: Maybe<Array<ProjectMapArea>>;
  usedCities?: Maybe<Array<ProjectMapArea>>;
};

export type ProjectMapBaseHistoryTrade = {
  __typename?: 'ProjectMapBaseHistoryTrade';
  historical?: Maybe<Scalars['Boolean']['output']>;
  tradeDetail?: Maybe<Scalars['String']['output']>;
};

export type ProjectMapBaseInfo = {
  __typename?: 'ProjectMapBaseInfo';
  addressMap: ProjectMapAddress;
  baseHistoryTrade?: Maybe<ProjectMapBaseHistoryTrade>;
  customizedIdcRoom?: Maybe<CustomizedIdc>;
  designLevel?: Maybe<ProjectMapDesignLevel>;
  idcCompanyName: Scalars['String']['output'];
  operationTime?: Maybe<Scalars['String']['output']>;
  operatorCoopInfo?: Maybe<OperatorCoop>;
  optStatus?: Maybe<OptStatus>;
  optStatusDetail?: Maybe<Scalars['String']['output']>;
  passCert?: Maybe<Scalars['Boolean']['output']>;
  phased?: Maybe<Scalars['Boolean']['output']>;
  phasedIn?: Maybe<PhasedIn>;
  projectName: Scalars['String']['output'];
  starLevel?: Maybe<ProjectMapStarLevel>;
  uptimeLevel?: Maybe<ProjectMapUptimeLevel>;
};

export type ProjectMapCityNode = {
  __typename?: 'ProjectMapCityNode';
  belongsGlp?: Maybe<Scalars['Boolean']['output']>;
  coord?: Maybe<Scalars['String']['output']>;
  glpIdcGuid?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  metaCode: Scalars['String']['output'];
  metaName: Scalars['String']['output'];
  metaType: Scalars['String']['output'];
  num?: Maybe<Scalars['Long']['output']>;
  parentCode: Scalars['String']['output'];
  position?: Maybe<Position>;
  quotationEditable?: Maybe<Scalars['Boolean']['output']>;
};

export type ProjectMapCompanysByNameResponse = {
  __typename?: 'ProjectMapCompanysByNameResponse';
  data: Array<Scalars['String']['output']>;
};

export type ProjectMapCustomersResponse = {
  __typename?: 'ProjectMapCustomersResponse';
  data: Array<Scalars['String']['output']>;
};

export type ProjectMapDesignInfo = {
  __typename?: 'ProjectMapDesignInfo';
  computerRoomArea?: Maybe<Scalars['Float']['output']>;
  constructArea?: Maybe<Scalars['Float']['output']>;
  designPue?: Maybe<Scalars['Float']['output']>;
  natureProp?: Maybe<ProjectMapNatureProp>;
  singleTransCap?: Maybe<Scalars['Float']['output']>;
  standardTons?: Maybe<Scalars['Float']['output']>;
  substationDetail?: Maybe<Array<ProjectMapSubstationDetail>>;
  substationNums?: Maybe<Scalars['Long']['output']>;
  tonsCanMulMerge?: Maybe<Scalars['Boolean']['output']>;
  totalItLoad?: Maybe<Scalars['Float']['output']>;
  totalOptCabinets?: Maybe<Scalars['Long']['output']>;
  transCanMulMerge?: Maybe<Scalars['Boolean']['output']>;
};

export enum ProjectMapDesignLevel {
  A = 'A',
  B = 'B',
  C = 'C'
}

export type ProjectMapDetail = {
  __typename?: 'ProjectMapDetail';
  baseInfo: ProjectMapBaseInfo;
  belongsGlp: Scalars['Boolean']['output'];
  designInfo: ProjectMapDesignInfo;
  geoPosition?: Maybe<Scalars['String']['output']>;
  glpIdcGuid?: Maybe<Scalars['String']['output']>;
  quotationInfo: ProjectMapQuotationInfo;
  saleInfo: ProjectMapSaleInfo;
};

export type ProjectMapDetailResponse = {
  __typename?: 'ProjectMapDetailResponse';
  data?: Maybe<ProjectMapDetail>;
};

export enum ProjectMapElecSupplyModel {
  WayI = 'WAY_I',
  WayIi = 'WAY_II',
  WayIii = 'WAY_III',
  WayIv = 'WAY_IV'
}

export type ProjectMapFavorite = {
  __typename?: 'ProjectMapFavorite';
  folderId: Scalars['Long']['output'];
  folderName: Scalars['String']['output'];
  projectNums: Scalars['Long']['output'];
};

export type ProjectMapFavoritesResponse = {
  __typename?: 'ProjectMapFavoritesResponse';
  data?: Maybe<Array<ProjectMapFavorite>>;
  total?: Maybe<Scalars['Long']['output']>;
};

export enum ProjectMapNatureProp {
  Lease = 'LEASE',
  Own = 'OWN'
}

export enum ProjectMapOperatorCoopInfoOperators {
  Cmcc = 'CMCC',
  Ctcc = 'CTCC',
  Cucc = 'CUCC'
}

export enum ProjectMapPowerPackStatus {
  ElecInclu = 'ELEC_INCLU',
  ElecNotInclu = 'ELEC_NOT_INCLU',
  Unknow = 'UNKNOW'
}

export type ProjectMapQuotationInfo = {
  __typename?: 'ProjectMapQuotationInfo';
  addedServices?: Maybe<Array<ProjectMapAddedService>>;
  quotationEditable: Scalars['Boolean']['output'];
  quotationRecords?: Maybe<Array<ProjectMapQuotationRecord>>;
  singleKwCost?: Maybe<Scalars['Float']['output']>;
};

export type ProjectMapQuotationRecord = {
  __typename?: 'ProjectMapQuotationRecord';
  cooperateAmount?: Maybe<Scalars['Float']['output']>;
  cooperateBeginTime?: Maybe<Scalars['String']['output']>;
  cooperateEndTime?: Maybe<Scalars['String']['output']>;
  cooperateItCap?: Maybe<Scalars['Float']['output']>;
  createUserId?: Maybe<Scalars['Long']['output']>;
  customerName?: Maybe<Scalars['String']['output']>;
  featureSpec?: Maybe<Scalars['Float']['output']>;
  gmtCreate?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  leaseNums?: Maybe<Scalars['Long']['output']>;
  powerPackStatus?: Maybe<ProjectMapPowerPackStatus>;
  quotationTime?: Maybe<Scalars['String']['output']>;
  singleCabinetPrice?: Maybe<Array<ProjectMapSingleCabinetPrice>>;
  singleKwPrice?: Maybe<Scalars['Float']['output']>;
  stationType?: Maybe<ProjectMapStationType>;
  winBid?: Maybe<Scalars['Boolean']['output']>;
};

export type ProjectMapResponse = {
  __typename?: 'ProjectMapResponse';
  cityMarkers: Array<ProjectMapCityNode>;
  countyMarkers: Array<ProjectMapCityNode>;
  projectMapMarkers: Array<ProjectMapCityNode>;
  projectMaps: Array<ProjectMap>;
  provinceMarkers: Array<ProjectMapCityNode>;
  regionMarkers: Array<ProjectMapCityNode>;
};

export type ProjectMapSaleInfo = {
  __typename?: 'ProjectMapSaleInfo';
  actualRackRate?: Maybe<Scalars['Float']['output']>;
  actualRentedCabinets?: Maybe<Scalars['Long']['output']>;
  contractCabinets?: Maybe<Scalars['Long']['output']>;
  contractOccuRate?: Maybe<Scalars['Float']['output']>;
  salesHistoryTrade?: Maybe<ProjectMapSalesHistoryTrade>;
  sellStatus?: Maybe<SellStatus>;
  sellableItCabinetSpecs?: Maybe<Array<ProjectMapSellableIt>>;
  sellableItLoad?: Maybe<Scalars['Long']['output']>;
};

export type ProjectMapSalesHistoryTrade = {
  __typename?: 'ProjectMapSalesHistoryTrade';
  historical?: Maybe<Scalars['Boolean']['output']>;
  tradeDetail?: Maybe<Scalars['String']['output']>;
};

export type ProjectMapSellableIt = {
  __typename?: 'ProjectMapSellableIt';
  cabinetSpec?: Maybe<Scalars['Float']['output']>;
  nums?: Maybe<Scalars['Long']['output']>;
  unit?: Maybe<Scalars['String']['output']>;
};

export enum ProjectMapServiceType {
  BaseOptService = 'BASE_OPT_SERVICE',
  Other = 'OTHER',
  SimpleItService = 'SIMPLE_IT_SERVICE'
}

export type ProjectMapSingleCabinetPrice = {
  __typename?: 'ProjectMapSingleCabinetPrice';
  cabinetSepc?: Maybe<Scalars['Long']['output']>;
  sellPrice?: Maybe<Scalars['Long']['output']>;
};

export enum ProjectMapStarLevel {
  StarI = 'STAR_I',
  StarIi = 'STAR_II',
  StarIii = 'STAR_III',
  StarIv = 'STAR_IV',
  StarV = 'STAR_V'
}

export enum ProjectMapStationType {
  Cdn = 'CDN',
  Master = 'MASTER',
  Pop = 'POP'
}

export type ProjectMapStatistics = {
  __typename?: 'ProjectMapStatistics';
  occuRate?: Maybe<Scalars['Float']['output']>;
  projectNums: Scalars['Long']['output'];
  rackRate?: Maybe<Scalars['Float']['output']>;
  singleKwPriceAvg?: Maybe<Scalars['Float']['output']>;
};

export type ProjectMapStatisticsByNameResponse = {
  __typename?: 'ProjectMapStatisticsByNameResponse';
  areas?: Maybe<Array<ProjectStatistics>>;
  customers?: Maybe<Array<ProjectStatistics>>;
  idcCompanys?: Maybe<Array<ProjectStatistics>>;
  projects?: Maybe<Array<Scalars['String']['output']>>;
};

export type ProjectMapStatisticsResponse = {
  __typename?: 'ProjectMapStatisticsResponse';
  data: ProjectMapStatistics;
};

export type ProjectMapSubstationDetail = {
  __typename?: 'ProjectMapSubstationDetail';
  elecSupplyModel?: Maybe<ProjectMapElecSupplyModel>;
  substationName?: Maybe<Scalars['String']['output']>;
};

export enum ProjectMapUptimeLevel {
  TireI = 'TIRE_I',
  TireIi = 'TIRE_II',
  TireIii = 'TIRE_III',
  TireIv = 'TIRE_IV'
}

export type ProjectStatistics = {
  __typename?: 'ProjectStatistics';
  name: Scalars['String']['output'];
  nums: Scalars['Long']['output'];
};

export type Query = {
  __typename?: 'Query';
  accountRelateRackTickets?: Maybe<AccountRelateRackTicketsResponse>;
  adjustSecondFees?: Maybe<AdjustFeesResponse>;
  alarmCountByBiz: AlarmCountByBizResponse;
  alarmShield?: Maybe<AlarmShieldDetail>;
  alarmShields?: Maybe<AlarmShieldsResponse>;
  annualPerformanceObjectiveById?: Maybe<AnnualPerformanceObjective>;
  annualPerformanceObjectivesByPlan: Array<AnnualPerformanceObjective>;
  annualPerformanceStatistics: AnnualPerformanceStatistics;
  annualPerformanceUsersStatistics: AnnualPerformanceUsersStatistics;
  appAwareRoles: Array<Role>;
  appIdentity?: Maybe<AppIdentity>;
  authResources?: Maybe<Array<AuthResource>>;
  authorizationRecords?: Maybe<AuthorizationRecordsResponse>;
  authorizationStatistics?: Maybe<AuthorizationStatistic>;
  availableDevicesInBlock?: Maybe<AvailableDevicesInBlockResponse>;
  batchAdjustApprovalGrids?: Maybe<BatchAdjustApprovalGridsResponse>;
  batchAdjustFeesRecords?: Maybe<BatchAdjustFeesRecordsResponse>;
  batchPointDataDuration?: Maybe<BatchPointDataDurationResponse>;
  bill?: Maybe<BillResponse>;
  bills?: Maybe<BillsResponse>;
  businessIndicators?: Maybe<BusinessIndicatorsResponse>;
  changeNotifications?: Maybe<NotificationsResponse>;
  changeOfflineDeviceInhibitionStatus?: Maybe<ChangeOfflineDeviceInhibitionStatusResponse>;
  changeOfflineRelateTickets: ChangeOfflineRelateTicketsResponse;
  changeOrderDevices: ChangeOrderDevicesResponse;
  /** 机柜上下电工单 - 检查列头柜对应的 PDU 的上下电状态 */
  checkRackPowerOnOffTicketPDUs?: Maybe<Array<Maybe<RackPowerOnOffTicketPduCheckResult>>>;
  contracts?: Maybe<ContractsResponse>;
  customerBusiness?: Maybe<CustomerBusiness>;
  customerDetail?: Maybe<Customer>;
  customerInvoices?: Maybe<CustomerInvoicesResponse>;
  customerNames?: Maybe<CustomerNamesResponse>;
  customerWhiteList?: Maybe<CustomerWhiteListResponse>;
  customerWhiteListDetail?: Maybe<CustomerWhiteListDetailResponse>;
  customerWhiteStaff?: Maybe<CustomerWhiteStaffResponse>;
  customers?: Maybe<CustomersResponse>;
  customersOnRacks?: Maybe<Array<CustomerOnRack>>;
  /** 查询某个机房某幢楼下的设备 */
  deviceByBlock?: Maybe<Array<Maybe<DeviceByBlockResponse>>>;
  drillConfig?: Maybe<DrillConfigResponse>;
  drillConfigByBiz?: Maybe<DrillConfigByBizResponse>;
  drillConfigs?: Maybe<DrillConfigsResponse>;
  drillOrderApproval?: Maybe<DrillOrderApproval>;
  drillOrderApprovalInfo?: Maybe<DrillOrderApprovalInfoRes>;
  drillOrderInfo?: Maybe<DrillOrderInfo>;
  drillOrderStep: DrillOrderStepResponse;
  drillOrders?: Maybe<DrillOrdersResponse>;
  electricityFees?: Maybe<ElectricityFeesResponse>;
  eventNotifications: EventNotificationsResponse;
  eventProcessRecords: EventProcessRecordsResponse;
  eventRelateApproval?: Maybe<BpmInstanceJson>;
  eventRelateTickets?: Maybe<EventRelateTicketsResponse>;
  eventSourceRecords?: Maybe<EventSourceRecordsResponse>;
  events: EventsResponse;
  /** 出库工单 */
  exWarehouseDetail?: Maybe<ExWarehouseDetailResponse>;
  feeInvoice?: Maybe<FeeInvoice>;
  fetchChangeApproval: FetchChangeApprovalResponse;
  fetchChangeShiftApplyOwner: FetchChangeShiftApplyOwnerResponse;
  fetchChangeShiftAttendanceResult: FetchChangeShiftAttendanceResultResponse;
  fetchChangeShiftConfig: FetchChangeShiftConfigResponse;
  fetchFuzzyChanges: FetchFuzzyChangesResponse;
  fetchFuzzyEvents: FetchFuzzyEventsResponse;
  fetchNonstandardInspectItem: FetchNonstandardInspectItemResponse;
  fetchSchedulePageTurningId?: Maybe<FetchSchedulePageTurningIdResponse>;
  fetchTicketsByRoom: FetchTicketsByRoomResponse;
  /** 告警列表 */
  getAlarmList?: Maybe<AlarmListResponse>;
  /** 变更告警看板数量 */
  getChangeAlarmCount?: Maybe<ChangeAlarmCountResponse>;
  /** 变更告警中存在的设备类型 */
  getChangeAlarmDeviceType?: Maybe<Array<Maybe<LabelInValue>>>;
  /** 变更详情 */
  getChangeDetailInfo?: Maybe<ChangeDetailInfoResponse>;
  /** 变更步骤看板列表 */
  getChangeDetailStepInfoList?: Maybe<Array<Maybe<ChangeDetailStepInfoResponse>>>;
  /** 变更步骤看板右侧详情 */
  getChangeDetailStepOptionInfo?: Maybe<ChangeDetailStepOptionInfoResponse>;
  /** 变更列表 */
  getChangeList?: Maybe<GetChangeListResponse>;
  /** 变更执行记录列表 */
  getChangeOrderExeLogList?: Maybe<GetChangeOrderExeLogListResponse>;
  /** 线下变更执行记录列表 */
  getChangeOrderOfflineExeLogList?: Maybe<Array<Maybe<GetChangeOrderOfflineExeLogListResponse>>>;
  /** 查询设备类型 */
  getDeviceTypesTree?: Maybe<Array<Maybe<DeviceTypeTreeNode>>>;
  /** 查询工单附件 */
  getFileInfos?: Maybe<Array<Maybe<FileItem>>>;
  /** 机柜类型 */
  getGridTypes?: Maybe<Array<Maybe<LabelInValue>>>;
  /** 预算影响面 */
  getInfluence?: Maybe<InfluenceResponse>;
  /** 厂商型号查设备类型 */
  getModel?: Maybe<GetModelQueryResponse>;
  getProjectMapCompanysByName: ProjectMapCompanysByNameResponse;
  /** 测点实时值 */
  getRealtimeDataByPointGuids?: Maybe<Scalars['JSONObject']['output']>;
  /** 通过taskNo全局搜索工单 */
  getSingleTicketByTaskNo?: Maybe<GetSingleTicketByTaskNoResponse>;
  /** 变更总结记录 */
  getSummeryInfo?: Maybe<SummeryInfoResponse>;
  getUpdateProjectMapLog: UpdateProjectMapLogResponse;
  /** 获取环境 */
  getUrl?: Maybe<GetUrlResponse>;
  /** 根据资源楼栋获取人员信息 */
  getUsersByResourceCode?: Maybe<Array<Maybe<GetUsersByResourceCodeResponse>>>;
  /** 厂商型号 */
  getVendorModelTree?: Maybe<Array<Maybe<VendorModalTreeResponse>>>;
  gridFees?: Maybe<GridFeesResponse>;
  guaranteedFees?: Maybe<GuaranteedFeesResponse>;
  historyTrades: HistoryTradesResponse;
  holidayBalanceCalculate?: Maybe<HolidayBalance>;
  hrmBalanceCalculate?: Maybe<HrmBalance>;
  idcCompanyDiffData: IdcCompanyDiffDataResponse;
  idcs?: Maybe<Array<Idc>>;
  /** 盘点 */
  inventoryDevice?: Maybe<InventoryDeviceResponse>;
  invoiceRecords?: Maybe<InvoiceRecordsResponse>;
  /** 判断是否有权限 */
  isAuthorized?: Maybe<Scalars['Boolean']['output']>;
  kwGuaranteedFees?: Maybe<KwGuaranteedFeesResponse>;
  me?: Maybe<LoggedInUser>;
  menus?: Maybe<Array<Menu>>;
  metadata?: Maybe<Array<Metadata>>;
  metadataTree?: Maybe<Array<MetadataTree>>;
  myPerformances?: Maybe<Array<Maybe<Performance>>>;
  mySpacePermissionsTree?: Maybe<Array<Maybe<SpacePermissionsTreeNode>>>;
  objectionBackNodes?: Maybe<ObjectionBackNodesResponse>;
  objectionRecords?: Maybe<ObjectionRecordsResponse>;
  openProjectMapFavoite: OpenProjectMapFavoiteResponse;
  othersFee?: Maybe<OthersFeeResponse>;
  paginatedAnnualPerformanceObjectives?: Maybe<PaginatedPerformanceObjectives>;
  paginatedPerformanceGrades?: Maybe<PaginatedPerformanceGrade>;
  paginatedPerformancePlans: PaginatedPerformancePlans;
  paginatedPerformances: PaginatedPerformances;
  pduDevicesOnRack: PduDevicesOnRackResponse;
  performance?: Maybe<Performance>;
  performanceGradeById?: Maybe<PerformanceGrade>;
  performancePlanById?: Maybe<PerformancePlan>;
  permissions?: Maybe<Array<Permission>>;
  projectDiffData: ProjectDiffDataResponse;
  projectMapAreaInfo: ProjectMapAreaInfoResponse;
  projectMapCustomers: ProjectMapCustomersResponse;
  projectMapDetail: ProjectMapDetailResponse;
  projectMapFavorites: ProjectMapFavoritesResponse;
  projectMapStatistics: ProjectMapStatisticsResponse;
  projectMapStatisticsByName: ProjectMapStatisticsByNameResponse;
  projectMaps: ProjectMapResponse;
  /** 根据 设备名称 三级分类 模糊查询设备 */
  queryDeviceByKeyword?: Maybe<Array<Maybe<QueryDeviceByKwordData>>>;
  /** 模糊查询供应商简称 */
  querySupplyVendors?: Maybe<Array<Maybe<QuerySupplyVendorResponse>>>;
  rackPowerOnOffTicketGrids?: Maybe<RackPowerOnOffTicketGrids>;
  racksOnPowerTicket?: Maybe<RacksOnPowerTicketResponse>;
  realOverloadGrids?: Maybe<RealOverloadGridsResponse>;
  repairFeedbacks: RepairFeedbacksResponse;
  riskOrderCheckItems: RiskOrderCheckItemsResponse;
  riskPools: RiskPoolsResponse;
  riskRegister?: Maybe<RiskRegisterBaseInfoResponse>;
  riskRegisterLatestDevelopment: Array<Maybe<ProcessingReacrdResponse>>;
  riskRegisterMeasure: RiskRegisterMeasureInfoResponse;
  riskTaskCheckItems: RiskTaskCheckItemsResponse;
  roles?: Maybe<RolesResponse>;
  rooms?: Maybe<Array<Maybe<Room>>>;
  salesOpportunities?: Maybe<SalesOpportunitiesResponse>;
  salesOpportunitiesStatistics?: Maybe<SalesOpportunitiesStatisticsResponse>;
  salesOpportunity?: Maybe<SalesOpportunity>;
  salesOpportunityActivities: Array<SalesOpportunityActivity>;
  salesOpportunityProgressions?: Maybe<SalesOpportunityProgressionsResponse>;
  salesOpportunityTask?: Maybe<SalesOpportunityTask>;
  salesOpportunityTasks: PaginatedSalesOpportunityTasks;
  salesOpportunityTurnPage?: Maybe<SalesOpportunityTurnPageResponse>;
  /** 可售资源统计 */
  salesRacksStatistics?: Maybe<SalesRacksStatistics>;
  secondFee?: Maybe<SecondFeeDetailResponse>;
  shiftAdjustmentTicketsByChildLeave?: Maybe<ShiftAdjustmentTicketsByChildLeaveResponse>;
  spaces?: Maybe<Array<Space>>;
  /** 耗材数量 */
  spareUniqueQuery?: Maybe<SpareUniqueQueryResponse>;
  specificDevice?: Maybe<Device>;
  storeProperties?: Maybe<StorePropertiesResponse>;
  /** 订阅级别 */
  subscribeUsableList?: Maybe<Array<Maybe<LabelInValue>>>;
  taskDetail?: Maybe<TaskDetailJson>;
  tasks: TasksResponse;
  teamUsers?: Maybe<TeamUsersResponse>;
  ticketBaseInfos?: Maybe<TicketBaseInfos>;
  ticketChecklist?: Maybe<TicketChecklist>;
  ticketFailtureType?: Maybe<Array<Maybe<LabelInValue>>>;
  ticketFirstTimeRecipientInfo?: Maybe<TicketFirstTimeRecipientInfo>;
  /** 工单异常处理 - 类型 */
  ticketHandleType?: Maybe<Array<Maybe<TicketHandleTypeResponse>>>;
  /** 巡检工单 - 包间详情-巡检项 */
  ticketInspectionRoomDetail?: Maybe<InspectionTicketRoomDetail>;
  /** 维护工单 - 包间详情-维护项 入参和巡检一样 */
  ticketMaintenanceRoomDetail?: Maybe<MaintenanceTicketRoomDetail>;
  ticketNotifications: TicketNotificationsResponse;
  ticketOperationalLogs?: Maybe<TicketOperationalLogs>;
  /**
   * _按包间统计工单进度信息_
   *
   * 支持的工单类型：
   *   * 机柜上下电工单
   */
  ticketProgressByRoom?: Maybe<TicketProgressByRoomResponse>;
  ticketRelateApprovalDetail?: Maybe<BpmInstanceJson>;
  ticketRelateApprovals: TicketRelateApprovalIdsResponse;
  ticketTimeoutType?: Maybe<Array<Maybe<LabelInValue>>>;
  /** 工单转交对象查询 */
  ticketTransferUserByBlock?: Maybe<Array<Maybe<LabelInValue>>>;
  ticketTypeTree?: Maybe<Array<Maybe<TicketTypeTreeNode>>>;
  ticketTypes?: Maybe<Array<TicketTypeNode>>;
  tickets?: Maybe<TicketsResponse>;
  ticketsStatistics?: Maybe<TicketsStatistics>;
  transferInvoiceUsers?: Maybe<TransferInvoiceUsersResponse>;
  /** 告警状态 */
  triggerStatusList?: Maybe<Array<Maybe<LabelInValue>>>;
  userLastedChildBirthday?: Maybe<UserLastedChildBirthdayResponse>;
  userResources?: Maybe<Array<UserResource>>;
  users?: Maybe<Array<Maybe<User>>>;
  usersForUserSelect?: Maybe<Array<UserInfo>>;
  validAnnualPerformanceTargetChange?: Maybe<ValidAnnualPerformanceTargetChangeResponse>;
  version: Version;
  visitTicket?: Maybe<VisitTicket>;
  warehouseFees?: Maybe<WarehouseFeesResponse>;
  workplaceFee?: Maybe<WorkplaceFeeResponse>;
};


export type QueryAccountRelateRackTicketsArgs = {
  endTime?: InputMaybe<Scalars['Long']['input']>;
  gridGuid: Scalars['String']['input'];
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  queryLatest?: InputMaybe<Scalars['Boolean']['input']>;
  startTime?: InputMaybe<Scalars['Long']['input']>;
};


export type QueryAdjustSecondFeesArgs = {
  billNo: Scalars['String']['input'];
};


export type QueryAlarmCountByBizArgs = {
  query: AlarmCountByBizQ;
};


export type QueryAlarmShieldArgs = {
  id: Scalars['Long']['input'];
};


export type QueryAlarmShieldsArgs = {
  params: QueryAlarmShieldsParams;
};


export type QueryAnnualPerformanceObjectiveByIdArgs = {
  id: Scalars['Int']['input'];
};


export type QueryAnnualPerformanceObjectivesByPlanArgs = {
  query: AnnualPerformanceObjectivesByPlanQuery;
};


export type QueryAnnualPerformanceStatisticsArgs = {
  query: AnnualPerformanceStatisticsQueryArgs;
};


export type QueryAnnualPerformanceUsersStatisticsArgs = {
  query: AnnualPerformanceUsersStatisticsQueryArgs;
};


export type QueryAppAwareRolesArgs = {
  website?: InputMaybe<Scalars['String']['input']>;
};


export type QueryAuthResourcesArgs = {
  resourceType?: InputMaybe<AuthResourceNodeType>;
  roleCode: Scalars['String']['input'];
};


export type QueryAuthorizationRecordsArgs = {
  params: AuthorizationRecordsQueryParams;
};


export type QueryAvailableDevicesInBlockArgs = {
  blockGuid: Scalars['String']['input'];
  deviceType?: InputMaybe<Scalars['String']['input']>;
  numbered: Scalars['Boolean']['input'];
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  productModel?: InputMaybe<Scalars['String']['input']>;
  vendor?: InputMaybe<Scalars['String']['input']>;
};


export type QueryBatchAdjustApprovalGridsArgs = {
  adjustNo: Scalars['String']['input'];
};


export type QueryBatchAdjustFeesRecordsArgs = {
  adjustNo: Scalars['String']['input'];
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
};


export type QueryBatchPointDataDurationArgs = {
  dateType: Scalars['String']['input'];
  endTime: Scalars['String']['input'];
  pointGuidList: Array<PointGuidInput>;
  startTime: Scalars['String']['input'];
};


export type QueryBillArgs = {
  billNo: Scalars['String']['input'];
  feeId?: InputMaybe<Scalars['Int']['input']>;
};


export type QueryBillsArgs = {
  billNo?: InputMaybe<Scalars['String']['input']>;
  billStatusList?: InputMaybe<Array<Scalars['String']['input']>>;
  billTag?: InputMaybe<Scalars['String']['input']>;
  customerName?: InputMaybe<Scalars['String']['input']>;
  customerNo?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  sortField?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  waitHandle?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryBusinessIndicatorsArgs = {
  dataSetKey: DataSetKey;
  params: BusinessIndicatorsSearchParams;
  sortBy?: InputMaybe<Array<BusinessIndicatorSortInput>>;
};


export type QueryChangeNotificationsArgs = {
  changeId: Scalars['String']['input'];
};


export type QueryChangeOfflineDeviceInhibitionStatusArgs = {
  changeOrderId: Scalars['String']['input'];
};


export type QueryChangeOfflineRelateTicketsArgs = {
  changeOrderId: Scalars['String']['input'];
};


export type QueryChangeOrderDevicesArgs = {
  query: ChangeOrderDevicesQuery;
};


export type QueryCheckRackPowerOnOffTicketPdUsArgs = {
  query: CheckRackPowerOnOffTicketPdUsQuery;
};


export type QueryContractsArgs = {
  params: QueryContractsParams;
};


export type QueryCustomerBusinessArgs = {
  customerName: Scalars['String']['input'];
};


export type QueryCustomerDetailArgs = {
  code: Scalars['String']['input'];
};


export type QueryCustomerInvoicesArgs = {
  customerName: Scalars['String']['input'];
};


export type QueryCustomerNamesArgs = {
  name: Scalars['String']['input'];
};


export type QueryCustomerWhiteListArgs = {
  blockGuidList?: InputMaybe<Array<Scalars['String']['input']>>;
  company?: InputMaybe<Scalars['String']['input']>;
  email?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Long']['input']>;
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
  phoneNo?: InputMaybe<Scalars['String']['input']>;
  startDate?: InputMaybe<Scalars['Long']['input']>;
  userStatus?: InputMaybe<Scalars['String']['input']>;
  username?: InputMaybe<Scalars['String']['input']>;
};


export type QueryCustomerWhiteListDetailArgs = {
  id: Scalars['Long']['input'];
};


export type QueryCustomerWhiteStaffArgs = {
  phoneNo: Scalars['String']['input'];
};


export type QueryCustomersArgs = {
  params: QueryCustomersParams;
};


export type QueryCustomersOnRacksArgs = {
  name?: InputMaybe<Scalars['String']['input']>;
};


export type QueryDeviceByBlockArgs = {
  params?: InputMaybe<DeviceByBlockQuery>;
};


export type QueryDrillConfigArgs = {
  id: Scalars['Long']['input'];
};


export type QueryDrillConfigByBizArgs = {
  bizId: Scalars['String']['input'];
};


export type QueryDrillConfigsArgs = {
  blockGuidList?: InputMaybe<Array<Scalars['String']['input']>>;
  creatorId?: InputMaybe<Scalars['Int']['input']>;
  effectType?: InputMaybe<Scalars['String']['input']>;
  endDate?: InputMaybe<Scalars['Long']['input']>;
  excLevelList?: InputMaybe<Array<Scalars['String']['input']>>;
  excMajor?: InputMaybe<Scalars['String']['input']>;
  excName?: InputMaybe<Scalars['String']['input']>;
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  startDate?: InputMaybe<Scalars['Long']['input']>;
};


export type QueryDrillOrderApprovalArgs = {
  params: DrillOrderApprovalParams;
};


export type QueryDrillOrderApprovalInfoArgs = {
  params: DrillOrderApprovalInfoParams;
};


export type QueryDrillOrderInfoArgs = {
  execNo: Scalars['String']['input'];
};


export type QueryDrillOrderStepArgs = {
  itemId: Scalars['Long']['input'];
};


export type QueryDrillOrdersArgs = {
  params: QueryDrillOrdersParams;
};


export type QueryElectricityFeesArgs = {
  billNo: Scalars['String']['input'];
};


export type QueryEventNotificationsArgs = {
  eventId: Scalars['Long']['input'];
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
};


export type QueryEventProcessRecordsArgs = {
  eventId: Scalars['Long']['input'];
  eventPhase: Scalars['String']['input'];
  mode: Scalars['String']['input'];
};


export type QueryEventRelateApprovalArgs = {
  eventId: Scalars['Long']['input'];
  processNo: Scalars['String']['input'];
};


export type QueryEventRelateTicketsArgs = {
  eventId: Scalars['Int']['input'];
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  sort: Sort;
};


export type QueryEventSourceRecordsArgs = {
  eventId: Scalars['Long']['input'];
};


export type QueryEventsArgs = {
  query: EventQuery;
};


export type QueryExWarehouseDetailArgs = {
  taskNo?: InputMaybe<Scalars['String']['input']>;
};


export type QueryFeeInvoiceArgs = {
  accountNo: Scalars['String']['input'];
  feeCode: Scalars['String']['input'];
};


export type QueryFetchChangeApprovalArgs = {
  query: FetchChangeApprovalQ;
};


export type QueryFetchChangeShiftApplyOwnerArgs = {
  scheduleId: Scalars['Long']['input'];
};


export type QueryFetchChangeShiftAttendanceResultArgs = {
  query: FetchChangeShiftAttendanceResultQ;
};


export type QueryFetchFuzzyChangesArgs = {
  blockGuid: Scalars['String']['input'];
  queryCondition: Scalars['String']['input'];
};


export type QueryFetchFuzzyEventsArgs = {
  query: FetchFuzzyEventsQuery;
};


export type QueryFetchNonstandardInspectItemArgs = {
  query: FetchNonstandardInspectItemQ;
};


export type QueryFetchSchedulePageTurningIdArgs = {
  query: FetchSchedulePageTurningIdQ;
};


export type QueryFetchTicketsByRoomArgs = {
  query: FetchTicketsByRoomQuery;
};


export type QueryGetAlarmListArgs = {
  getAlarmListQ?: InputMaybe<GetAlarmListQ>;
};


export type QueryGetChangeAlarmCountArgs = {
  getChangeAlarmCountQ?: InputMaybe<GetChangeAlarmCountQ>;
};


export type QueryGetChangeAlarmDeviceTypeArgs = {
  getChangeAlarmDeviceTypeQ?: InputMaybe<GetChangeAlarmDeviceTypeQ>;
};


export type QueryGetChangeDetailInfoArgs = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetChangeDetailStepInfoListArgs = {
  changeOrderId: Scalars['String']['input'];
};


export type QueryGetChangeDetailStepOptionInfoArgs = {
  getChangeDetailStepOptionInfoQ: GetChangeDetailStepOptionInfoQ;
};


export type QueryGetChangeListArgs = {
  getChangeListQ?: InputMaybe<GetChangeListQ>;
};


export type QueryGetChangeOrderExeLogListArgs = {
  getChangeOrderExeLogListQ?: InputMaybe<GetChangeOrderExeLogListQ>;
};


export type QueryGetChangeOrderOfflineExeLogListArgs = {
  getChangeOrderOfflineExeLogListQ?: InputMaybe<GetChangeOrderOfflineExeLogListQ>;
};


export type QueryGetDeviceTypesTreeArgs = {
  numbered?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryGetFileInfosArgs = {
  getFileInfosQ?: InputMaybe<GetFileInfosQ>;
};


export type QueryGetInfluenceArgs = {
  getInfluenceQ?: InputMaybe<GetInfluenceQ>;
};


export type QueryGetModelArgs = {
  modelCode?: InputMaybe<Scalars['String']['input']>;
  vendorCode?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGetProjectMapCompanysByNameArgs = {
  params: QueryProjectMapCompanysByNameParams;
};


export type QueryGetRealtimeDataByPointGuidsArgs = {
  getRealtimeDataByPointGuidsQ?: InputMaybe<GetRealtimeDataByPointGuidsQ>;
};


export type QueryGetSingleTicketByTaskNoArgs = {
  getSingleTicketByTaskNoQ?: InputMaybe<GetSingleTicketByTaskNoQ>;
};


export type QueryGetSummeryInfoArgs = {
  changeOrderId: Scalars['String']['input'];
};


export type QueryGetUpdateProjectMapLogArgs = {
  params: QueryProjectMapLogParams;
};


export type QueryGetUsersByResourceCodeArgs = {
  getUsersByResourceCodeQ?: InputMaybe<GetUsersByResourceCodeQ>;
};


export type QueryGetVendorModelTreeArgs = {
  deviceType?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGridFeesArgs = {
  billNo: Scalars['String']['input'];
  blockGuid: Scalars['String']['input'];
  feeCode: Scalars['String']['input'];
  gridGuid?: InputMaybe<Scalars['String']['input']>;
  newlyAdd?: InputMaybe<Scalars['Boolean']['input']>;
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  roomGuid?: InputMaybe<Scalars['String']['input']>;
};


export type QueryGuaranteedFeesArgs = {
  billNo: Scalars['String']['input'];
};


export type QueryHistoryTradesArgs = {
  params: QueryHistoryTradesParams;
};


export type QueryHolidayBalanceCalculateArgs = {
  hiredDate: Scalars['String']['input'];
  joinWorkingTime: Scalars['String']['input'];
  leaveDate: Scalars['String']['input'];
  staffId: Scalars['Long']['input'];
};


export type QueryHrmBalanceCalculateArgs = {
  hiredDate: Scalars['String']['input'];
  joinWorkingTime: Scalars['String']['input'];
  leaveDate: Scalars['String']['input'];
  staffId: Scalars['Long']['input'];
};


export type QueryIdcCompanyDiffDataArgs = {
  params: QueryIdcCompanyDiffDataParams;
};


export type QueryIdcsArgs = {
  authorizedOnly?: InputMaybe<Scalars['Boolean']['input']>;
  cityCode?: InputMaybe<Scalars['String']['input']>;
  constructedAtFrom?: InputMaybe<Scalars['Long']['input']>;
  constructedAtTo?: InputMaybe<Scalars['Long']['input']>;
  guid?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  nationCode?: InputMaybe<Scalars['String']['input']>;
  operatingStatus?: InputMaybe<Scalars['String']['input']>;
  operationalAtFrom?: InputMaybe<Scalars['Long']['input']>;
  operationalAtTo?: InputMaybe<Scalars['Long']['input']>;
  provinceCode?: InputMaybe<Scalars['String']['input']>;
  regionCode?: InputMaybe<Scalars['String']['input']>;
};


export type QueryInventoryDeviceArgs = {
  inventoryDeviceQ?: InputMaybe<InventoryDeviceQ>;
};


export type QueryInvoiceRecordsArgs = {
  feeId: Scalars['Int']['input'];
};


export type QueryIsAuthorizedArgs = {
  code?: InputMaybe<Scalars['String']['input']>;
};


export type QueryKwGuaranteedFeesArgs = {
  billNo: Scalars['String']['input'];
};


export type QueryMenusArgs = {
  keyword?: InputMaybe<Scalars['String']['input']>;
  tree?: InputMaybe<Scalars['Boolean']['input']>;
};


export type QueryMetadataArgs = {
  queryDelete?: InputMaybe<Scalars['Boolean']['input']>;
  type: Scalars['String']['input'];
};


export type QueryMetadataTreeArgs = {
  queryDelete?: InputMaybe<Scalars['Boolean']['input']>;
  type: Scalars['String']['input'];
};


export type QueryMyPerformancesArgs = {
  performancePosition?: InputMaybe<Scalars['String']['input']>;
  subType?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
  userId?: InputMaybe<Scalars['Int']['input']>;
  year?: InputMaybe<Scalars['String']['input']>;
};


export type QueryObjectionBackNodesArgs = {
  feeDetailId: Scalars['Int']['input'];
};


export type QueryObjectionRecordsArgs = {
  billNo: Scalars['String']['input'];
};


export type QueryOpenProjectMapFavoiteArgs = {
  folderId: Scalars['Long']['input'];
};


export type QueryOthersFeeArgs = {
  billNo: Scalars['String']['input'];
  feeId: Scalars['Int']['input'];
};


export type QueryPaginatedAnnualPerformanceObjectivesArgs = {
  query: PaginatedAnnualPerformanceObjectivesQuery;
};


export type QueryPaginatedPerformanceGradesArgs = {
  query: PaginatedPerformanceGradesQuery;
};


export type QueryPaginatedPerformancePlansArgs = {
  query: PaginatedPerformancePlansQuery;
};


export type QueryPaginatedPerformancesArgs = {
  query: PaginatedPerformancesQuery;
};


export type QueryPduDevicesOnRackArgs = {
  blockTag: Scalars['String']['input'];
  deviceTypeList: Array<Scalars['String']['input']>;
  gridGuidList: Array<Scalars['String']['input']>;
  idcTag: Scalars['String']['input'];
};


export type QueryPerformanceArgs = {
  id: Scalars['Int']['input'];
  isChange?: InputMaybe<Scalars['Boolean']['input']>;
  subType: Scalars['String']['input'];
  type: Scalars['String']['input'];
};


export type QueryPerformanceGradeByIdArgs = {
  id: Scalars['Int']['input'];
};


export type QueryPerformancePlanByIdArgs = {
  id: Scalars['Int']['input'];
  includeRelatedObjectives?: InputMaybe<Scalars['Boolean']['input']>;
  relatedObjectivesQuery?: InputMaybe<PerformancePlanRelatedObjectivesQuery>;
};


export type QueryPermissionsArgs = {
  params?: InputMaybe<QueryPermissionsParams>;
  types?: InputMaybe<Array<Scalars['String']['input']>>;
};


export type QueryProjectDiffDataArgs = {
  idcProjectIds: Array<Scalars['Long']['input']>;
};


export type QueryProjectMapCustomersArgs = {
  keyword?: InputMaybe<Scalars['String']['input']>;
};


export type QueryProjectMapDetailArgs = {
  idcProjectId: Scalars['Long']['input'];
};


export type QueryProjectMapFavoritesArgs = {
  params?: InputMaybe<QueryProjectMapsFavoritesParams>;
};


export type QueryProjectMapStatisticsArgs = {
  params?: InputMaybe<QueryProjectMapsParams>;
};


export type QueryProjectMapStatisticsByNameArgs = {
  keyword: Scalars['String']['input'];
};


export type QueryProjectMapsArgs = {
  params?: InputMaybe<QueryProjectMapsParams>;
};


export type QueryQueryDeviceByKeywordArgs = {
  queryDeviceByKeywordQ?: InputMaybe<QueryDeviceByKeywordQ>;
};


export type QueryQuerySupplyVendorsArgs = {
  querySupplyVendorsQ?: InputMaybe<QuerySupplyVendorsQ>;
};


export type QueryRackPowerOnOffTicketGridsArgs = {
  query: RackPowerOnOffTicketGridsQuery;
};


export type QueryRacksOnPowerTicketArgs = {
  blockTag: Scalars['String']['input'];
  gridTag?: InputMaybe<Scalars['String']['input']>;
  idcTag: Scalars['String']['input'];
  roomTag: Scalars['String']['input'];
  sortModel?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
};


export type QueryRealOverloadGridsArgs = {
  blockTag: Scalars['String']['input'];
  deviceTypeList?: InputMaybe<Array<Scalars['String']['input']>>;
  idcTag: Scalars['String']['input'];
  pointCode: Scalars['String']['input'];
  roomTag: Scalars['String']['input'];
};


export type QueryRepairFeedbacksArgs = {
  taskNo: Scalars['String']['input'];
};


export type QueryRiskOrderCheckItemsArgs = {
  query: RiskOrderCheckItems;
};


export type QueryRiskPoolsArgs = {
  query?: InputMaybe<RiskPoolsQ>;
};


export type QueryRiskRegisterArgs = {
  query: RiskRegisterQuery;
};


export type QueryRiskRegisterLatestDevelopmentArgs = {
  query: RiskRegisterLatestDevelopmentQuery;
};


export type QueryRiskRegisterMeasureArgs = {
  query: RiskRegisterQuery;
};


export type QueryRiskTaskCheckItemsArgs = {
  isFlatten?: InputMaybe<Scalars['Boolean']['input']>;
  schId: Scalars['Long']['input'];
};


export type QueryRolesArgs = {
  authorized: Scalars['Boolean']['input'];
  website: Website;
};


export type QueryRoomsArgs = {
  roomsQ?: InputMaybe<RoomsQuery>;
};


export type QuerySalesOpportunitiesArgs = {
  params: QueryOpportunitiesParams;
};


export type QuerySalesOpportunitiesStatisticsArgs = {
  params: QueryOpportunitiesStatisticsParams;
};


export type QuerySalesOpportunityArgs = {
  id: Scalars['String']['input'];
};


export type QuerySalesOpportunityActivitiesArgs = {
  limit?: InputMaybe<Scalars['Int']['input']>;
  opportunityCode: Scalars['String']['input'];
};


export type QuerySalesOpportunityProgressionsArgs = {
  opportunityCode: Scalars['String']['input'];
};


export type QuerySalesOpportunityTaskArgs = {
  code: Scalars['String']['input'];
};


export type QuerySalesOpportunityTasksArgs = {
  content?: InputMaybe<Scalars['String']['input']>;
  deadline?: InputMaybe<Scalars['Long']['input']>;
  isOverTime?: InputMaybe<Scalars['Boolean']['input']>;
  page: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  relatedNumber?: InputMaybe<Scalars['String']['input']>;
  statusList?: InputMaybe<Array<Scalars['String']['input']>>;
  taskType?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySalesOpportunityTurnPageArgs = {
  code?: InputMaybe<Scalars['String']['input']>;
  creatorName?: InputMaybe<Scalars['String']['input']>;
  customerCode?: InputMaybe<Scalars['String']['input']>;
  customerName?: InputMaybe<Scalars['String']['input']>;
  customerType?: InputMaybe<Scalars['String']['input']>;
  endAt?: InputMaybe<Scalars['Long']['input']>;
  id: Scalars['Int']['input'];
  idcTag?: InputMaybe<Scalars['String']['input']>;
  modifiedEndAt?: InputMaybe<Scalars['Long']['input']>;
  modifiedStartAt?: InputMaybe<Scalars['Long']['input']>;
  picName?: InputMaybe<Scalars['String']['input']>;
  regionCode?: InputMaybe<Scalars['String']['input']>;
  resourceCode?: InputMaybe<Scalars['String']['input']>;
  sortField?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  startAt?: InputMaybe<Scalars['Long']['input']>;
  statusList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  teamId?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySalesRacksStatisticsArgs = {
  ISPs?: InputMaybe<Array<Scalars['String']['input']>>;
  availableITCapacityExpr?: InputMaybe<Scalars['String']['input']>;
  availableRacksExpr?: InputMaybe<Scalars['String']['input']>;
  city?: InputMaybe<Scalars['String']['input']>;
  idc?: InputMaybe<Scalars['String']['input']>;
  idcOperatingStatuses?: InputMaybe<Array<Scalars['String']['input']>>;
  idcOperationalAt?: InputMaybe<Scalars['Long']['input']>;
  rackDesignPowers?: InputMaybe<Array<Scalars['String']['input']>>;
  rackPowerDistributionModes?: InputMaybe<Array<Scalars['String']['input']>>;
  rackUnitCounts?: InputMaybe<Array<Scalars['String']['input']>>;
  region?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySecondFeeArgs = {
  billNo: Scalars['String']['input'];
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  countGrid: Scalars['Boolean']['input'];
  feeId: Scalars['Int']['input'];
};


export type QueryShiftAdjustmentTicketsByChildLeaveArgs = {
  params: ShiftAdjustmentTicketsByChildLeaveParams;
};


export type QuerySpacesArgs = {
  authorizedOnly?: InputMaybe<Scalars['Boolean']['input']>;
  blockTypes?: InputMaybe<Array<Scalars['String']['input']>>;
  blocks?: InputMaybe<Array<Scalars['String']['input']>>;
  idc?: InputMaybe<Scalars['String']['input']>;
  includeNoIdcRegions?: InputMaybe<Scalars['Boolean']['input']>;
  includeVirtualBlocks?: InputMaybe<Scalars['Boolean']['input']>;
  nodeTypes?: InputMaybe<Array<SpaceNodeType>>;
  roomTypes?: InputMaybe<Array<Scalars['String']['input']>>;
  userName?: InputMaybe<Scalars['String']['input']>;
};


export type QuerySpareUniqueQueryArgs = {
  spareUniqueQueryQ?: InputMaybe<SpareUniqueQueryQ>;
};


export type QuerySpecificDeviceArgs = {
  query: SpecificDeviceQuery;
};


export type QueryStorePropertiesArgs = {
  type: Scalars['String']['input'];
};


export type QuerySubscribeUsableListArgs = {
  subscribeType: Scalars['String']['input'];
};


export type QueryTaskDetailArgs = {
  id: Scalars['String']['input'];
};


export type QueryTasksArgs = {
  blockGuidList?: InputMaybe<Array<Scalars['String']['input']>>;
  creatorId?: InputMaybe<Scalars['String']['input']>;
  defineDesc?: InputMaybe<Scalars['Boolean']['input']>;
  endDateOfUpdate?: InputMaybe<Scalars['Long']['input']>;
  endTime?: InputMaybe<Scalars['Long']['input']>;
  isInvalid?: InputMaybe<Scalars['String']['input']>;
  jobTypeList?: InputMaybe<Array<Scalars['String']['input']>>;
  manageType?: InputMaybe<Scalars['String']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
  periodUnit?: InputMaybe<Scalars['String']['input']>;
  schLevel?: InputMaybe<Scalars['String']['input']>;
  sortByField?: InputMaybe<Scalars['String']['input']>;
  sortInfo?: InputMaybe<Scalars['String']['input']>;
  startDateOfUpdate?: InputMaybe<Scalars['Long']['input']>;
  startTime?: InputMaybe<Scalars['Long']['input']>;
  subJobType?: InputMaybe<Scalars['String']['input']>;
  taskStatus?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTeamUsersArgs = {
  key?: InputMaybe<Scalars['String']['input']>;
  userId: Scalars['Long']['input'];
};


export type QueryTicketBaseInfosArgs = {
  taskNo: Scalars['String']['input'];
};


export type QueryTicketChecklistArgs = {
  taskSubType?: InputMaybe<Scalars['String']['input']>;
  taskType: Scalars['String']['input'];
};


export type QueryTicketFirstTimeRecipientInfoArgs = {
  taskNo: Scalars['String']['input'];
};


export type QueryTicketHandleTypeArgs = {
  params?: InputMaybe<TicketHandleTypeQuery>;
};


export type QueryTicketInspectionRoomDetailArgs = {
  params: InspectionRoomDetailQuery;
};


export type QueryTicketMaintenanceRoomDetailArgs = {
  params?: InputMaybe<InspectionRoomDetailQuery>;
};


export type QueryTicketNotificationsArgs = {
  bizId: Scalars['String']['input'];
  bizType: Scalars['String']['input'];
  eventCode: Scalars['String']['input'];
  pageNum: Scalars['Int']['input'];
  pageSize: Scalars['Int']['input'];
};


export type QueryTicketOperationalLogsArgs = {
  query: TicketOperationalLogsQuery;
};


export type QueryTicketProgressByRoomArgs = {
  ticketProgressByRoomQ: TicketProgressByRoomQ;
};


export type QueryTicketRelateApprovalDetailArgs = {
  processId: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};


export type QueryTicketRelateApprovalsArgs = {
  taskNo: Scalars['String']['input'];
  taskNode?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTicketTransferUserByBlockArgs = {
  resourceCode: Scalars['String']['input'];
};


export type QueryTicketTypeTreeArgs = {
  typeCode?: InputMaybe<Scalars['String']['input']>;
};


export type QueryTicketsArgs = {
  ticketsQ: TicketsQuery;
};


export type QueryTicketsStatisticsArgs = {
  statisticsQ?: InputMaybe<TicketsStatisticsQuery>;
};


export type QueryTransferInvoiceUsersArgs = {
  feeId: Scalars['Int']['input'];
};


export type QueryUserLastedChildBirthdayArgs = {
  userId: Scalars['String']['input'];
};


export type QueryUserResourcesArgs = {
  params: ResourceSearchParam;
};


export type QueryUsersArgs = {
  keyword: Scalars['String']['input'];
};


export type QueryUsersForUserSelectArgs = {
  authorized?: InputMaybe<Scalars['Boolean']['input']>;
  key?: InputMaybe<Scalars['String']['input']>;
  resourceParams?: InputMaybe<Array<ResourceParam>>;
  resourceTypes?: InputMaybe<Array<Scalars['String']['input']>>;
  userState?: InputMaybe<Scalars['String']['input']>;
};


export type QueryValidAnnualPerformanceTargetChangeArgs = {
  changeType: Scalars['String']['input'];
  year: Scalars['String']['input'];
};


export type QueryVisitTicketArgs = {
  ticketNumber: Scalars['String']['input'];
};


export type QueryWarehouseFeesArgs = {
  billNo: Scalars['String']['input'];
  blockGuid: Scalars['String']['input'];
};


export type QueryWorkplaceFeeArgs = {
  billNo: Scalars['String']['input'];
};

export type QueryAlarmShieldsParams = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  effectStatus?: InputMaybe<AlarmShieldStatusCode>;
  endTime?: InputMaybe<Scalars['Long']['input']>;
  finishEndTime?: InputMaybe<Scalars['Long']['input']>;
  finishStartTime?: InputMaybe<Scalars['Long']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
  startTime?: InputMaybe<Scalars['Long']['input']>;
};

export type QueryContractsParams = {
  customerName?: InputMaybe<Scalars['String']['input']>;
  endTimeBegin?: InputMaybe<Scalars['Long']['input']>;
  endTimeEnd?: InputMaybe<Scalars['Long']['input']>;
  id?: InputMaybe<Scalars['String']['input']>;
  idcTagList?: InputMaybe<Array<Scalars['String']['input']>>;
  isFinance?: InputMaybe<Scalars['Boolean']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  startTimeBegin?: InputMaybe<Scalars['Long']['input']>;
  startTimeEnd?: InputMaybe<Scalars['Long']['input']>;
  statusList?: InputMaybe<Array<Scalars['String']['input']>>;
  title?: InputMaybe<Scalars['String']['input']>;
};

export type QueryCustomersParams = {
  address?: InputMaybe<Scalars['String']['input']>;
  customer?: InputMaybe<Scalars['String']['input']>;
  owner?: InputMaybe<Scalars['String']['input']>;
  ownerId?: InputMaybe<Scalars['Long']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  type?: InputMaybe<Scalars['String']['input']>;
};

export type QueryDeviceByKeywordQ = {
  assertStatusList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  blockTag?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  deviceGuidList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  operationStatus?: InputMaybe<Scalars['String']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
};

export type QueryDeviceByKwordData = {
  __typename?: 'QueryDeviceByKwordData';
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  guid?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  spaceGuid?: Maybe<SpaceGuid>;
  vendor?: Maybe<Scalars['String']['output']>;
};

export type QueryDrillOrdersParams = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  creatorId?: InputMaybe<Scalars['Long']['input']>;
  endTime?: InputMaybe<Scalars['Long']['input']>;
  excLevelList?: InputMaybe<Array<Scalars['String']['input']>>;
  excMajor?: InputMaybe<Scalars['String']['input']>;
  execNo?: InputMaybe<Scalars['String']['input']>;
  finishEndTime?: InputMaybe<Scalars['Long']['input']>;
  finishStartTime?: InputMaybe<Scalars['Long']['input']>;
  gmtCreate?: InputMaybe<Scalars['String']['input']>;
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
  principalId?: InputMaybe<Scalars['Long']['input']>;
  scheduleId?: InputMaybe<Scalars['Long']['input']>;
  sortByField?: InputMaybe<Scalars['String']['input']>;
  sortInfo?: InputMaybe<Scalars['String']['input']>;
  startTime?: InputMaybe<Scalars['Long']['input']>;
  taskAssignee?: InputMaybe<Scalars['Long']['input']>;
  taskNoList?: InputMaybe<Array<Scalars['String']['input']>>;
  taskStatusList?: InputMaybe<Array<DrillOrderTaskStatus>>;
  titleOrExecNo?: InputMaybe<Scalars['String']['input']>;
};

export type QueryHistoryTradesParams = {
  cooperateBeginTime?: InputMaybe<Scalars['String']['input']>;
  cooperateEndTime?: InputMaybe<Scalars['String']['input']>;
  customerName: Scalars['String']['input'];
  idcCompanyName?: InputMaybe<Scalars['String']['input']>;
  projectName?: InputMaybe<Scalars['String']['input']>;
  quotationBeginTime?: InputMaybe<Scalars['String']['input']>;
  quotationEndTime?: InputMaybe<Scalars['String']['input']>;
  sortField?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  winBid?: InputMaybe<Scalars['Boolean']['input']>;
};

export type QueryIdcCompanyDiffDataParams = {
  companyInfos: Array<CompanyInfo>;
};

export type QueryOpportunitiesParams = {
  code?: InputMaybe<Scalars['String']['input']>;
  creatorName?: InputMaybe<Scalars['String']['input']>;
  customerCode?: InputMaybe<Scalars['String']['input']>;
  customerName?: InputMaybe<Scalars['String']['input']>;
  customerType?: InputMaybe<Scalars['String']['input']>;
  endAt?: InputMaybe<Scalars['Long']['input']>;
  idList?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  modifiedEndAt?: InputMaybe<Scalars['Long']['input']>;
  modifiedStartAt?: InputMaybe<Scalars['Long']['input']>;
  myAttention?: InputMaybe<Scalars['Boolean']['input']>;
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
  picName?: InputMaybe<Scalars['String']['input']>;
  regionCode?: InputMaybe<Scalars['String']['input']>;
  resourceCode?: InputMaybe<Scalars['String']['input']>;
  sortField?: InputMaybe<Scalars['String']['input']>;
  sortOrder?: InputMaybe<Scalars['String']['input']>;
  startAt?: InputMaybe<Scalars['Long']['input']>;
  statusList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  teamId?: InputMaybe<Scalars['String']['input']>;
};

export type QueryOpportunitiesStatisticsParams = {
  code?: InputMaybe<Scalars['String']['input']>;
  creatorName?: InputMaybe<Scalars['String']['input']>;
  customerCode?: InputMaybe<Scalars['String']['input']>;
  customerName?: InputMaybe<Scalars['String']['input']>;
  customerType?: InputMaybe<Scalars['String']['input']>;
  endAt?: InputMaybe<Scalars['Long']['input']>;
  idList?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  isExcludeDiscard?: InputMaybe<Scalars['Boolean']['input']>;
  modifiedEndAt?: InputMaybe<Scalars['Long']['input']>;
  modifiedStartAt?: InputMaybe<Scalars['Long']['input']>;
  myAttention?: InputMaybe<Scalars['Boolean']['input']>;
  picName?: InputMaybe<Scalars['String']['input']>;
  queryDateType: Scalars['String']['input'];
  regionCode?: InputMaybe<Scalars['String']['input']>;
  resourceCode?: InputMaybe<Scalars['String']['input']>;
  startAt?: InputMaybe<Scalars['Long']['input']>;
  statusList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  teamId?: InputMaybe<Scalars['String']['input']>;
};

export type QueryPermissionsParams = {
  permissionTypes?: InputMaybe<Array<Scalars['String']['input']>>;
  roleId?: InputMaybe<Scalars['Long']['input']>;
};

export type QueryProjectMapCompanysByNameParams = {
  name?: InputMaybe<Scalars['String']['input']>;
  province?: InputMaybe<Scalars['String']['input']>;
};

export type QueryProjectMapLogParams = {
  idcProjectId: Scalars['Long']['input'];
  pageNum: Scalars['Long']['input'];
  pageSize: Scalars['Long']['input'];
};

export type QueryProjectMapsFavoritesParams = {
  customerName?: InputMaybe<Array<Scalars['String']['input']>>;
  idcCompanyName?: InputMaybe<Array<Scalars['String']['input']>>;
  projectAreaName?: InputMaybe<Array<Scalars['String']['input']>>;
  projectName?: InputMaybe<Scalars['String']['input']>;
};

export type QueryProjectMapsParams = {
  cities?: InputMaybe<Array<Scalars['String']['input']>>;
  customerName?: InputMaybe<Array<Scalars['String']['input']>>;
  idcCompanyName?: InputMaybe<Array<Scalars['String']['input']>>;
  nameFuzzy?: InputMaybe<Scalars['Boolean']['input']>;
  operationBeginTime?: InputMaybe<Scalars['String']['input']>;
  operationEndTime?: InputMaybe<Scalars['String']['input']>;
  optStatusList?: InputMaybe<Array<OptStatus>>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  projectArea?: InputMaybe<Array<Scalars['String']['input']>>;
  projectAreaName?: InputMaybe<Array<Scalars['String']['input']>>;
  projectName?: InputMaybe<Scalars['String']['input']>;
  provinces?: InputMaybe<Array<Scalars['String']['input']>>;
  sellableItLoadRanges?: InputMaybe<Array<SellableItLoadRange>>;
  singleKwPriceRanges?: InputMaybe<Array<SingleKwPriceRange>>;
};

export type QuerySupplyVendorResponse = {
  __typename?: 'QuerySupplyVendorResponse';
  id?: Maybe<Scalars['Long']['output']>;
  vendorCode?: Maybe<Scalars['String']['output']>;
};

export type QuerySupplyVendorsQ = {
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  vendorCode?: InputMaybe<Scalars['String']['input']>;
};

export type QuotationRecord = {
  cooperateAmount?: InputMaybe<Scalars['Float']['input']>;
  cooperateBeginTime?: InputMaybe<Scalars['String']['input']>;
  cooperateEndTime?: InputMaybe<Scalars['String']['input']>;
  cooperateItCap?: InputMaybe<Scalars['Float']['input']>;
  createUserId?: InputMaybe<Scalars['Long']['input']>;
  customerName: Scalars['String']['input'];
  featureSpec?: InputMaybe<Scalars['Float']['input']>;
  id?: InputMaybe<Scalars['Long']['input']>;
  leaseNums?: InputMaybe<Scalars['Long']['input']>;
  powerPackStatus?: InputMaybe<PowerPackStatus>;
  quotationTime?: InputMaybe<Scalars['String']['input']>;
  singleCabinetPrice?: InputMaybe<Array<InputMaybe<SingleCabinetPrice>>>;
  singleKwPrice?: InputMaybe<Scalars['Float']['input']>;
  stationType?: InputMaybe<StationType>;
  winBid: Scalars['Boolean']['input'];
};

export type RackOnPowerTicket = {
  __typename?: 'RackOnPowerTicket';
  blockTag: Scalars['String']['output'];
  checkStatus: CheckStatus;
  columnTag: Scalars['String']['output'];
  executeTime?: Maybe<Scalars['String']['output']>;
  frontGrids?: Maybe<Array<Maybe<FrontRack>>>;
  frontRack?: Maybe<FrontRack>;
  gridGuid: Scalars['String']['output'];
  gridTag: Scalars['String']['output'];
  gridType: RackType;
  id: Scalars['String']['output'];
  idcTag: Scalars['String']['output'];
  modReason?: Maybe<Scalars['String']['output']>;
  modified: Scalars['Boolean']['output'];
  operatorBy?: Maybe<Scalars['Int']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  pduDevice?: Maybe<PduDevice>;
  pduDevices?: Maybe<Array<Maybe<PduDevice>>>;
  powerOnTime?: Maybe<Scalars['String']['output']>;
  rackRowSpan?: Maybe<Scalars['Int']['output']>;
  ratedPower: Scalars['Int']['output'];
  roomRowSpan?: Maybe<Scalars['Int']['output']>;
  roomTag: Scalars['String']['output'];
  taskNo: Scalars['String']['output'];
};

export type RackPowerOnOffTicketGrid = {
  __typename?: 'RackPowerOnOffTicketGrid';
  checkStatus: Scalars['String']['output'];
  finishedAt?: Maybe<Scalars['Long']['output']>;
  finishedBy?: Maybe<User>;
  gridTag: Scalars['String']['output'];
  gridType: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  pdus?: Maybe<RackPowerOnOffTicketGridPdu>;
  powerRating?: Maybe<Scalars['Float']['output']>;
  roomTag: Scalars['String']['output'];
  rpps?: Maybe<RackPowerOnOffTicketGridRpp>;
};

export type RackPowerOnOffTicketGridPdu = {
  __typename?: 'RackPowerOnOffTicketGridPDU';
  powerLineAs?: Maybe<Array<Maybe<Device>>>;
  powerLineBs?: Maybe<Array<Maybe<Device>>>;
};

export type RackPowerOnOffTicketGridRpp = {
  __typename?: 'RackPowerOnOffTicketGridRPP';
  powerLineA?: Maybe<Device>;
  powerLineB?: Maybe<Device>;
};

export type RackPowerOnOffTicketGrids = {
  __typename?: 'RackPowerOnOffTicketGrids';
  gridTypes?: Maybe<Array<Maybe<LabelInValue>>>;
  grids: Array<Maybe<RackPowerOnOffTicketGrid>>;
};

export type RackPowerOnOffTicketGridsQuery = {
  roomGuid: Scalars['String']['input'];
  sortModel: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};

export type RackPowerOnOffTicketPduCheckResult = {
  __typename?: 'RackPowerOnOffTicketPDUCheckResult';
  /** 支路开关电流 */
  current?: Maybe<SinglePointRealtimeData>;
  deviceGuid: Scalars['String']['output'];
  ok: Scalars['Boolean']['output'];
  /** 支路开关通过检查的时间 */
  okTime?: Maybe<Scalars['Long']['output']>;
  /** 支路开关状态（label） */
  onOffStateText?: Maybe<Scalars['String']['output']>;
  /** 支路开关功率 */
  power?: Maybe<SinglePointRealtimeData>;
  /** 支路开关电压 */
  voltage?: Maybe<SinglePointRealtimeData>;
};

export enum RackSaleStatus {
  Lock = 'LOCK',
  Sale = 'SALE',
  Sold = 'SOLD'
}

export type RackTicketCheckStatus = {
  __typename?: 'RackTicketCheckStatus';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type RackTicketTaskType = {
  __typename?: 'RackTicketTaskType';
  name: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type RackType = {
  __typename?: 'RackType';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
};

export type RacksOnPowerTicketResponse = {
  __typename?: 'RacksOnPowerTicketResponse';
  errorPduDeviceGuids?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
  racks?: Maybe<Array<RackOnPowerTicket>>;
};

export type RealOverloadGrid = {
  __typename?: 'RealOverloadGrid';
  avgPowerMonth?: Maybe<Scalars['Float']['output']>;
  avgPowerWeek?: Maybe<Scalars['Float']['output']>;
  blockTag: Scalars['String']['output'];
  columnTag: Scalars['String']['output'];
  customerId?: Maybe<Scalars['String']['output']>;
  customerName?: Maybe<Scalars['String']['output']>;
  gridGuid: Scalars['String']['output'];
  gridTag: Scalars['String']['output'];
  gridType: GridType;
  idcTag: Scalars['String']['output'];
  ratedPower?: Maybe<Scalars['Float']['output']>;
  relateDeviceList: Array<GridRelateDevice>;
  roomTag: Scalars['String']['output'];
  signedPower?: Maybe<Scalars['Float']['output']>;
  unitCount?: Maybe<Scalars['Float']['output']>;
};

export type RealOverloadGridsResponse = {
  __typename?: 'RealOverloadGridsResponse';
  data?: Maybe<Array<RealOverloadGrid>>;
  total: Scalars['Long']['output'];
};

export type RedirectEventResponse = MutationResponse & {
  __typename?: 'RedirectEventResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type RedirectInfo = {
  opportunityCode: Scalars['String']['input'];
  sourceOwner: SimpleUserInput;
  targetOwner: OwnerInput;
};

export type Region = {
  __typename?: 'Region';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type RelateDevice = {
  __typename?: 'RelateDevice';
  assetNo: Scalars['String']['output'];
  blockTag: Scalars['String']['output'];
  deviceGuid: Scalars['String']['output'];
  deviceLabel?: Maybe<Scalars['String']['output']>;
  deviceName: Scalars['String']['output'];
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  extendPosition?: Maybe<Scalars['String']['output']>;
  idcTag: Scalars['String']['output'];
  roomTag: Scalars['String']['output'];
};

export type RelatePerson = {
  __typename?: 'RelatePerson';
  id?: Maybe<Scalars['Int']['output']>;
  label: Scalars['String']['output'];
  name: Scalars['String']['output'];
  type: AnySimpleUserType;
  value?: Maybe<Scalars['String']['output']>;
};

export type RelateTicket = {
  __typename?: 'RelateTicket';
  relateTicket: RelateTicketBasicInfo;
  relateTicketCreateTime: Scalars['String']['output'];
  relateTicketCreateUserId: Scalars['String']['output'];
  relateTicketDesc: Scalars['String']['output'];
  relateTicketStatus: RelateTicketStatus;
};

export type RelateTicketBasicInfo = {
  __typename?: 'RelateTicketBasicInfo';
  id: Scalars['String']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type RelateTicketStatus = {
  __typename?: 'RelateTicketStatus';
  name: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type RenameProjectMapFavoiteResponse = {
  __typename?: 'RenameProjectMapFavoiteResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Scalars['Boolean']['output'];
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type RepairFeedback = {
  __typename?: 'RepairFeedback';
  feedback: Scalars['String']['output'];
  fileNum?: Maybe<Scalars['Int']['output']>;
  gmtCreate: Scalars['String']['output'];
  gmtModified?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  operatorId: Scalars['Int']['output'];
  operatorName: Scalars['String']['output'];
  resolved: Scalars['Boolean']['output'];
};

export type RepairFeedbacksResponse = {
  __typename?: 'RepairFeedbacksResponse';
  data: Array<RepairFeedback>;
  total: Scalars['Long']['output'];
};

export type ReportObject = {
  __typename?: 'ReportObject';
  code: Scalars['String']['output'];
  name: Scalars['String']['output'];
  type: Scalars['String']['output'];
};

export type ReportObjectInput = {
  code: Scalars['String']['input'];
  name: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export type RequestNewDeviceOnOffTicketData = {
  device: DeviceInput;
  fileInfoList?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  reason: Scalars['String']['input'];
  taskSubType: Scalars['String']['input'];
};

export type RequestNewDeviceOnOffTicketResponse = MutationResponse & {
  __typename?: 'RequestNewDeviceOnOffTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  taskNo?: Maybe<Scalars['String']['output']>;
};

export type ResendTicketQ = {
  assigneeInfo?: InputMaybe<AssigneeInfo>;
  resendDesc?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
  taskType?: InputMaybe<Scalars['String']['input']>;
};

export type ResendTicketResponse = MutationResponse & {
  __typename?: 'ResendTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ResourceParam = {
  resourceCodes: Array<Scalars['String']['input']>;
  resourceType: Scalars['String']['input'];
};

export type ResourceSearchParam = {
  needVirtualBlock?: InputMaybe<Scalars['Boolean']['input']>;
  parentCode?: InputMaybe<Scalars['String']['input']>;
  resourceTypes?: InputMaybe<Array<AuthorizationResourceType>>;
  searchName?: InputMaybe<Scalars['String']['input']>;
  userId?: InputMaybe<Scalars['Long']['input']>;
};

export type Result = {
  __typename?: 'Result';
  data?: Maybe<Scalars['JSONObject']['output']>;
  deviceGuid?: Maybe<Scalars['String']['output']>;
  exeNum?: Maybe<Scalars['Long']['output']>;
  nolNum?: Maybe<Scalars['Long']['output']>;
};

export type ReturnedFeeResponse = MutationResponse & {
  __typename?: 'ReturnedFeeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ReviewDrillOrderParams = {
  execNo: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  reviewDesc?: InputMaybe<Scalars['String']['input']>;
};

export type RevokeChangeOfflineSummeryResponse = MutationResponse & {
  __typename?: 'RevokeChangeOfflineSummeryResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type RevokeFeeResponse = MutationResponse & {
  __typename?: 'RevokeFeeResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export enum RiskClearStatus {
  Cleared = 'CLEARED',
  Uncleared = 'UNCLEARED'
}

export enum RiskLevel {
  High = 'HIGH',
  Low = 'LOW',
  Mid = 'MID'
}

export type RiskObject = {
  __typename?: 'RiskObject';
  blockTag?: Maybe<Scalars['String']['output']>;
  deviceTag?: Maybe<Scalars['String']['output']>;
  deviceType?: Maybe<Scalars['String']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  key?: Maybe<Scalars['String']['output']>;
  label: Scalars['String']['output'];
  objectGuid?: Maybe<Scalars['String']['output']>;
  objectName: Scalars['String']['output'];
  roomTag?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export type RiskObjectInput = {
  names: Scalars['String']['input'];
  type: Scalars['String']['input'];
};

export enum RiskObjectType {
  Device = 'DEVICE',
  Other = 'OTHER',
  Room = 'ROOM'
}

export type RiskOrderCheckItems = {
  isFlatten?: InputMaybe<Scalars['Boolean']['input']>;
  riskCategoryList?: InputMaybe<Array<Scalars['String']['input']>>;
  riskDesc?: InputMaybe<Scalars['String']['input']>;
  riskLevelList?: InputMaybe<Array<Scalars['String']['input']>>;
  riskTypeList?: InputMaybe<Array<Scalars['String']['input']>>;
  taskNo: Scalars['String']['input'];
};

export type RiskOrderCheckItemsResponse = {
  __typename?: 'RiskOrderCheckItemsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<RiskPool>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type RiskOrderVerify = {
  __typename?: 'RiskOrderVerify';
  id?: Maybe<Scalars['Long']['output']>;
  taskNo: Scalars['String']['output'];
  verify: Scalars['String']['output'];
  verifyResult?: Maybe<Scalars['String']['output']>;
};

export type RiskPointVerify = {
  __typename?: 'RiskPointVerify';
  id?: Maybe<Scalars['Long']['output']>;
  riskPointId?: Maybe<Scalars['Long']['output']>;
  taskNo?: Maybe<Scalars['String']['output']>;
  verify?: Maybe<Scalars['String']['output']>;
  verifyResult?: Maybe<Scalars['String']['output']>;
};

export type RiskPool = {
  __typename?: 'RiskPool';
  bizId: Scalars['Long']['output'];
  creatorId: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  fileId?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<McUploadFile>>;
  gmtCreate: Scalars['String']['output'];
  gmtModified?: Maybe<Scalars['String']['output']>;
  id: Scalars['String']['output'];
  isDelete?: Maybe<Scalars['String']['output']>;
  isEnable: Scalars['String']['output'];
  mergeRowsNum?: Maybe<Scalars['Long']['output']>;
  modifiedId?: Maybe<Scalars['Long']['output']>;
  modifiedName?: Maybe<Scalars['String']['output']>;
  riskCategory: Scalars['String']['output'];
  riskDesc: Scalars['String']['output'];
  riskLevel: RiskLevel;
  riskPointId?: Maybe<Scalars['Long']['output']>;
  riskPointVerifyList: Array<RiskPointVerify>;
  riskType: Scalars['String']['output'];
  taskNo?: Maybe<Scalars['String']['output']>;
  verify?: Maybe<Scalars['String']['output']>;
  verifyId?: Maybe<Scalars['Long']['output']>;
  verifyResult?: Maybe<Scalars['String']['output']>;
};

export type RiskPoolsQ = {
  creatorId?: InputMaybe<Scalars['Long']['input']>;
  defineOrder?: InputMaybe<Scalars['Boolean']['input']>;
  endDate?: InputMaybe<Scalars['Long']['input']>;
  endDateOfUpdate?: InputMaybe<Scalars['Long']['input']>;
  isEnable?: InputMaybe<Scalars['String']['input']>;
  isFlatten?: InputMaybe<Scalars['Boolean']['input']>;
  orderFiled?: InputMaybe<Scalars['String']['input']>;
  orderInfo?: InputMaybe<Scalars['String']['input']>;
  riskCategoryList?: InputMaybe<Array<Scalars['String']['input']>>;
  riskDesc?: InputMaybe<Scalars['String']['input']>;
  riskLevelList?: InputMaybe<Array<Scalars['String']['input']>>;
  riskTypeList?: InputMaybe<Array<Scalars['String']['input']>>;
  startDate?: InputMaybe<Scalars['Long']['input']>;
  startDateOfUpdate?: InputMaybe<Scalars['Long']['input']>;
};

export type RiskPoolsResponse = {
  __typename?: 'RiskPoolsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<RiskPool>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type RiskRegisterBaseInfoResponse = {
  __typename?: 'RiskRegisterBaseInfoResponse';
  auditInstId?: Maybe<Scalars['String']['output']>;
  blockGuid?: Maybe<Scalars['String']['output']>;
  createUser: Scalars['Long']['output'];
  createdAt: Scalars['Long']['output'];
  evaluateInstId?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<McUploadFile>>;
  id: Scalars['String']['output'];
  idcTag?: Maybe<Scalars['String']['output']>;
  measureProgress?: Maybe<Scalars['Long']['output']>;
  modifiedAt?: Maybe<Scalars['Long']['output']>;
  rectifyInfoList?: Maybe<Array<McUploadFile>>;
  relatePerson?: Maybe<RelatePerson>;
  riskAuditTime?: Maybe<Scalars['String']['output']>;
  riskCategoryCode?: Maybe<Scalars['String']['output']>;
  riskClearReason?: Maybe<Scalars['String']['output']>;
  riskClearStatus?: Maybe<RiskClearStatus>;
  riskCloseTime?: Maybe<Scalars['String']['output']>;
  riskDesc?: Maybe<Scalars['String']['output']>;
  riskEvaluateTime?: Maybe<Scalars['String']['output']>;
  riskHandleTime?: Maybe<Scalars['String']['output']>;
  riskIdentifyTime?: Maybe<Scalars['String']['output']>;
  riskInfluenceDesc?: Maybe<Scalars['String']['output']>;
  riskLevel?: Maybe<RiskLevel>;
  riskObjectType?: Maybe<RiskObjectType>;
  riskObjects?: Maybe<Array<RiskObject>>;
  riskOwner?: Maybe<Scalars['Long']['output']>;
  riskPriorityCode?: Maybe<Scalars['String']['output']>;
  riskResourceCode?: Maybe<Scalars['String']['output']>;
  riskStatus: RiskStatus;
  riskTypeCode?: Maybe<Scalars['String']['output']>;
};

export type RiskRegisterLatestDevelopmentQuery = {
  measureId?: InputMaybe<Scalars['Long']['input']>;
  riskId: Scalars['String']['input'];
};

export type RiskRegisterMeasureInfoResponse = {
  __typename?: 'RiskRegisterMeasureInfoResponse';
  deletedLongMeasures: Array<MeasureJson>;
  deletedShortMeasures: Array<MeasureJson>;
  longMeasures: Array<MeasureJson>;
  shortMeasures: Array<MeasureJson>;
};

export type RiskRegisterQuery = {
  riskId: Scalars['String']['input'];
};

export enum RiskStatus {
  Approving = 'APPROVING',
  Closed = 'CLOSED',
  Draft = 'DRAFT',
  Handling = 'HANDLING',
  WaitingEvaluate = 'WAITING_EVALUATE',
  WaitingIdentify = 'WAITING_IDENTIFY'
}

export type RiskTaskCheckItemsResponse = {
  __typename?: 'RiskTaskCheckItemsResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<RiskPool>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  total: Scalars['Long']['output'];
};

export type Role = {
  __typename?: 'Role';
  approveId?: Maybe<Scalars['Long']['output']>;
  code: Scalars['String']['output'];
  createBy?: Maybe<SimpleUser>;
  createdAt?: Maybe<Scalars['Long']['output']>;
  id: Scalars['Long']['output'];
  modifiedAt?: Maybe<Scalars['Long']['output']>;
  modifyBy?: Maybe<SimpleUser>;
  name: Scalars['String']['output'];
  remarks?: Maybe<Scalars['String']['output']>;
  resourceType?: Maybe<Scalars['String']['output']>;
};

export type RolesResponse = {
  __typename?: 'RolesResponse';
  data?: Maybe<Array<Role>>;
  total: Scalars['Long']['output'];
};

export type Room = {
  __typename?: 'Room';
  guid?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  roomType?: Maybe<Scalars['String']['output']>;
  roomTypeName?: Maybe<Scalars['String']['output']>;
  tag?: Maybe<Scalars['String']['output']>;
};

export type RoomDetail = {
  __typename?: 'RoomDetail';
  maintenanceItems: Array<Maybe<MaintenanceItem>>;
  securityStds?: Maybe<Scalars['String']['output']>;
};

export type RoomsQuery = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  content?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  operationStatus?: InputMaybe<Scalars['String']['input']>;
  operationTimeEnd?: InputMaybe<Scalars['Long']['input']>;
  operationTimeStart?: InputMaybe<Scalars['Long']['input']>;
  pageNum?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  roomType?: InputMaybe<Scalars['String']['input']>;
};

export type SalesHistoryTrade = {
  historical?: InputMaybe<Scalars['Boolean']['input']>;
  tradeDetail?: InputMaybe<Scalars['String']['input']>;
};

export type SalesOpportunitiesResponse = {
  __typename?: 'SalesOpportunitiesResponse';
  data?: Maybe<Array<SalesOpportunity>>;
  total: Scalars['Long']['output'];
};

export type SalesOpportunitiesStatisticsResponse = {
  __typename?: 'SalesOpportunitiesStatisticsResponse';
  contractAmount?: Maybe<Array<ItCapacity>>;
  itCapacity?: Maybe<Array<ItCapacity>>;
  monthIncome?: Maybe<Array<ItCapacity>>;
  status?: Maybe<Array<ItCapacity>>;
};

export type SalesOpportunity = {
  __typename?: 'SalesOpportunity';
  chargeDate?: Maybe<Scalars['String']['output']>;
  clueCode?: Maybe<Scalars['String']['output']>;
  code: Scalars['String']['output'];
  contractFilingDate?: Maybe<Scalars['String']['output']>;
  contractModel?: Maybe<Scalars['String']['output']>;
  contractModelText?: Maybe<Scalars['String']['output']>;
  contractTotalAmount?: Maybe<Scalars['Float']['output']>;
  createdAt: Scalars['Long']['output'];
  createdBy: SimpleUser;
  customer: Customer;
  customerRequirementInfo?: Maybe<CustomerRequirementInfo>;
  fileInfoList?: Maybe<Array<Maybe<Scalars['JSONObject']['output']>>>;
  id: Scalars['Int']['output'];
  idcName?: Maybe<Scalars['String']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  modifiedAt: Scalars['Long']['output'];
  modifiedBy?: Maybe<SimpleUser>;
  monthlyIncome?: Maybe<Scalars['Float']['output']>;
  name: Scalars['String']['output'];
  owner: SalesOpportunityOwner;
  picTeamInfoList?: Maybe<Array<PicTeam>>;
  regionCode?: Maybe<Scalars['String']['output']>;
  remark?: Maybe<Scalars['String']['output']>;
  source?: Maybe<Scalars['String']['output']>;
  sourceText?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  statusText?: Maybe<Scalars['String']['output']>;
  version?: Maybe<Scalars['Long']['output']>;
};

export type SalesOpportunityActivity = {
  __typename?: 'SalesOpportunityActivity';
  code: Scalars['String']['output'];
  content: Scalars['String']['output'];
  createdAt: Scalars['Long']['output'];
  createdUser: SimpleUser;
  type: SalesOpportunityActivityType;
};

export enum SalesOpportunityActivityType {
  FollowUp = 'FOLLOW_UP',
  Task = 'TASK'
}

export type SalesOpportunityOwner = {
  __typename?: 'SalesOpportunityOwner';
  departmentId?: Maybe<Scalars['String']['output']>;
  departmentName?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type SalesOpportunityProgression = {
  __typename?: 'SalesOpportunityProgression';
  code: Scalars['String']['output'];
  content: Scalars['String']['output'];
  createdAt: Scalars['Long']['output'];
  fileInfoList?: Maybe<Array<Scalars['JSONObject']['output']>>;
  method?: Maybe<Scalars['String']['output']>;
  modifiedAt?: Maybe<Scalars['Long']['output']>;
  operator?: Maybe<OperatorInfo>;
  progressAt: Scalars['Long']['output'];
  relateCode: Scalars['String']['output'];
  relateType: Scalars['String']['output'];
};

export type SalesOpportunityProgressionsResponse = {
  __typename?: 'SalesOpportunityProgressionsResponse';
  progressions?: Maybe<Array<Maybe<SalesOpportunityProgression>>>;
};

export type SalesOpportunityTask = {
  __typename?: 'SalesOpportunityTask';
  attachments?: Maybe<Array<McUploadFile>>;
  attachmentsFromExecution?: Maybe<Array<McUploadFile>>;
  code: Scalars['String']['output'];
  completedAt?: Maybe<Scalars['Long']['output']>;
  content: Scalars['String']['output'];
  createdAt: Scalars['Long']['output'];
  createdBy: SimpleUser;
  deadline: Scalars['Long']['output'];
  executionMethod?: Maybe<Scalars['String']['output']>;
  executionMethodText?: Maybe<Scalars['String']['output']>;
  executor: SimpleUser;
  feedbacks?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  isOverdue: Scalars['Boolean']['output'];
  modifiedAt: Scalars['Long']['output'];
  opportunityCode: Scalars['String']['output'];
  status: Scalars['String']['output'];
  statusText: Scalars['String']['output'];
  target: Scalars['String']['output'];
  type: SalesOpportunityTaskType;
};

export type SalesOpportunityTaskExecutorInput = {
  deptId: Scalars['String']['input'];
  id: Scalars['Int']['input'];
  name: Scalars['String']['input'];
};

export enum SalesOpportunityTaskType {
  BusinessOpportunity = 'BUSINESS_OPPORTUNITY'
}

export type SalesOpportunityTurnPageResponse = {
  __typename?: 'SalesOpportunityTurnPageResponse';
  lastId?: Maybe<Scalars['Int']['output']>;
  lastOpportunityNo?: Maybe<Scalars['String']['output']>;
  nextId?: Maybe<Scalars['Int']['output']>;
  nextOpportunityNo?: Maybe<Scalars['String']['output']>;
};

export type SalesRacksStatistics = {
  __typename?: 'SalesRacksStatistics';
  deidentified: Scalars['Boolean']['output'];
  idcs?: Maybe<Array<IdcRacksStatistics>>;
  totalAvailableITCapacity?: Maybe<Scalars['Float']['output']>;
  totalAvailableRacksCount?: Maybe<Scalars['Int']['output']>;
};

export type SaveFile = {
  fileFormat?: InputMaybe<Scalars['String']['input']>;
  fileName?: InputMaybe<Scalars['String']['input']>;
  filePath?: InputMaybe<Scalars['String']['input']>;
  fileSize?: InputMaybe<Scalars['Long']['input']>;
  fileType?: InputMaybe<Scalars['String']['input']>;
  gmtCreate?: InputMaybe<Scalars['String']['input']>;
  gmtModified?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['Long']['input']>;
  targetId?: InputMaybe<Scalars['String']['input']>;
  targetType?: InputMaybe<Scalars['String']['input']>;
  uploadBy?: InputMaybe<Scalars['Long']['input']>;
  uploadByName?: InputMaybe<Scalars['String']['input']>;
  uploadTime?: InputMaybe<Scalars['Long']['input']>;
};

export type SaveFileResponse = {
  __typename?: 'SaveFileResponse';
  fileFormat?: Maybe<Scalars['String']['output']>;
  fileName?: Maybe<Scalars['String']['output']>;
  filePath?: Maybe<Scalars['String']['output']>;
  fileSize?: Maybe<Scalars['Long']['output']>;
  fileType?: Maybe<Scalars['String']['output']>;
  gmtCreate?: Maybe<Scalars['String']['output']>;
  gmtModified?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  targetId?: Maybe<Scalars['String']['output']>;
  targetType?: Maybe<Scalars['String']['output']>;
  uploadBy?: Maybe<Scalars['Long']['output']>;
  uploadByName?: Maybe<Scalars['String']['output']>;
  uploadTime?: Maybe<Scalars['Long']['output']>;
};

export type SaveFilesQ = {
  fileInfos?: InputMaybe<Array<InputMaybe<SaveFile>>>;
  fileType?: InputMaybe<Scalars['String']['input']>;
  targetId?: InputMaybe<Scalars['String']['input']>;
  targetType?: InputMaybe<Scalars['String']['input']>;
};

export type SaveFilesResponse = MutationResponse & {
  __typename?: 'SaveFilesResponse';
  code?: Maybe<Scalars['String']['output']>;
  files?: Maybe<Array<Maybe<SaveFileResponse>>>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SaveLatestUsedRoleMutationResponse = {
  __typename?: 'SaveLatestUsedRoleMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success?: Maybe<Scalars['Boolean']['output']>;
};

export type SaveMaintenanceItemFileData = {
  __typename?: 'SaveMaintenanceItemFileData';
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  itemId?: Maybe<Scalars['String']['output']>;
};

export type SaveMaintenanceItemFileQ = {
  fileInfoList?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  itemId?: InputMaybe<Scalars['String']['input']>;
  maintenanceItemId?: InputMaybe<Scalars['Long']['input']>;
};

export type SaveMaintenanceItemFileResponse = MutationResponse & {
  __typename?: 'SaveMaintenanceItemFileResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<SaveMaintenanceItemFileData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SaveMaintenanceItemRemarkQ = {
  maintenanceItemIdList?: InputMaybe<Array<InputMaybe<Scalars['Long']['input']>>>;
  remark?: InputMaybe<Scalars['String']['input']>;
};

export type SaveMaintenanceItemRemarkResponse = MutationResponse & {
  __typename?: 'SaveMaintenanceItemRemarkResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type ScanQrCodeInfo = {
  scanTime: Scalars['Long']['input'];
  subjectGuid: Scalars['String']['input'];
  subjectType: Scalars['String']['input'];
  taskNo: Scalars['String']['input'];
};

export type ScheduleScene = {
  __typename?: 'ScheduleScene';
  code?: Maybe<Scalars['String']['output']>;
  desc?: Maybe<Scalars['String']['output']>;
  scene?: Maybe<Scalars['String']['output']>;
};

export type ScopeFlagInfo = {
  __typename?: 'ScopeFlagInfo';
  flagName: Scalars['String']['output'];
  scopeFlag?: Maybe<Scalars['Int']['output']>;
};

export type SecondFee = {
  __typename?: 'SecondFee';
  addFeeConfirm?: Maybe<Scalars['Boolean']['output']>;
  addFeeInstId?: Maybe<Scalars['String']['output']>;
  addFeeInstStatus?: Maybe<Scalars['String']['output']>;
  approvalInstId?: Maybe<Scalars['String']['output']>;
  approvalType?: Maybe<Scalars['String']['output']>;
  blockGuidList?: Maybe<Array<Scalars['String']['output']>>;
  creatorId?: Maybe<Scalars['Int']['output']>;
  feeCode: Scalars['String']['output'];
  feeName: Scalars['String']['output'];
  feeSource: Scalars['Int']['output'];
  feeStatus: BillStatusTag;
  id: Scalars['Int']['output'];
  invoiceUserId?: Maybe<Scalars['Int']['output']>;
  measureMethod?: Maybe<Scalars['String']['output']>;
  objectionApplyStaffId?: Maybe<Scalars['Int']['output']>;
  objectionApplyStaffName?: Maybe<Scalars['String']['output']>;
  objectionCheckStatus?: Maybe<Scalars['Int']['output']>;
  objectionContent?: Maybe<Scalars['String']['output']>;
  objectionMessage?: Maybe<Scalars['String']['output']>;
  recheck: Scalars['Boolean']['output'];
};

export type SecondFeeDetailResponse = {
  __typename?: 'SecondFeeDetailResponse';
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  amountUnit?: Maybe<Scalars['String']['output']>;
  chargeFee?: Maybe<Scalars['Float']['output']>;
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  feeGridAmountInfoList?: Maybe<Array<FeeGridAmountInfo>>;
  originFee?: Maybe<Scalars['Float']['output']>;
  paidFee?: Maybe<Scalars['Float']['output']>;
  preferentialFee?: Maybe<Scalars['Float']['output']>;
  taxFee?: Maybe<Scalars['Float']['output']>;
  taxRate?: Maybe<Scalars['Float']['output']>;
  useAmount?: Maybe<Scalars['Float']['output']>;
};

export type SecondFees = {
  __typename?: 'SecondFees';
  data?: Maybe<Array<SecondFee>>;
  total: Scalars['Long']['output'];
};

export enum SellStatus {
  NoSale = 'NO_SALE',
  Sale = 'SALE'
}

export type SellableItCabinetSpec = {
  cabinetSpec?: InputMaybe<Scalars['Float']['input']>;
  nums?: InputMaybe<Scalars['Long']['input']>;
  unit?: InputMaybe<Scalars['String']['input']>;
};

export type SellableItLoadRange = {
  sellableItLoadMax?: InputMaybe<Scalars['Long']['input']>;
  sellableItLoadMin?: InputMaybe<Scalars['Long']['input']>;
};

export enum ServiceType {
  BaseOptService = 'BASE_OPT_SERVICE',
  Other = 'OTHER',
  SimpleItService = 'SIMPLE_IT_SERVICE'
}

export type ShieldScope = {
  __typename?: 'ShieldScope';
  deviceLabel?: Maybe<Scalars['String']['output']>;
  deviceName?: Maybe<Scalars['String']['output']>;
  deviceOperationStatus?: Maybe<Scalars['String']['output']>;
  floorName?: Maybe<Scalars['String']['output']>;
  floorTag?: Maybe<Scalars['String']['output']>;
  guid: Scalars['String']['output'];
  pointCode?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  roomName?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  roomType?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
  vendorCode?: Maybe<Scalars['String']['output']>;
};

export type ShieldScopeInput = {
  guid: Scalars['String']['input'];
  pointCode?: InputMaybe<Scalars['String']['input']>;
  pointName?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type ShiftAdjustment = {
  __typename?: 'ShiftAdjustment';
  applyType?: Maybe<Scalars['String']['output']>;
  changeScheduleEndTime?: Maybe<Scalars['Float']['output']>;
  changeScheduleStartTime?: Maybe<Scalars['Float']['output']>;
  dutyGroupId?: Maybe<Scalars['Float']['output']>;
  dutyId?: Maybe<Scalars['Float']['output']>;
  dutyName?: Maybe<Scalars['String']['output']>;
  inUserId?: Maybe<Scalars['Float']['output']>;
  inUserName?: Maybe<Scalars['String']['output']>;
  needReplace?: Maybe<Scalars['Boolean']['output']>;
  oaInUserId?: Maybe<Scalars['Float']['output']>;
  oaOutUserId?: Maybe<Scalars['Float']['output']>;
  outUserId?: Maybe<Scalars['Float']['output']>;
  outUserName?: Maybe<Scalars['String']['output']>;
  replaceDate?: Maybe<Scalars['String']['output']>;
  scheduleEndTime?: Maybe<Scalars['Float']['output']>;
  scheduleId?: Maybe<Scalars['Float']['output']>;
  scheduleStartTime?: Maybe<Scalars['Float']['output']>;
  timeRange?: Maybe<Scalars['String']['output']>;
};

export type ShiftAdjustmentTicket = {
  __typename?: 'ShiftAdjustmentTicket';
  applyBy?: Maybe<SimpleUser>;
  approveId?: Maybe<Scalars['String']['output']>;
  approveStatus?: Maybe<Scalars['String']['output']>;
  bizId?: Maybe<Scalars['String']['output']>;
  canRollBack?: Maybe<Scalars['Boolean']['output']>;
  combineLeave?: Maybe<Scalars['Boolean']['output']>;
  combineLeaveTimeInfo?: Maybe<Array<CombineLeaveTime>>;
  createBy?: Maybe<SimpleUser>;
  createdAt?: Maybe<Scalars['String']['output']>;
  files?: Maybe<Array<McUploadFile>>;
  finishAt?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Float']['output']>;
  leaveInfo?: Maybe<LeaveInfo>;
  location?: Maybe<Scalars['String']['output']>;
  modifiedAt?: Maybe<Scalars['String']['output']>;
  reason?: Maybe<Scalars['String']['output']>;
  rollBackApproveStatus?: Maybe<Scalars['String']['output']>;
  shiftAdjustments?: Maybe<Array<ShiftAdjustment>>;
  subType?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type ShiftAdjustmentTicketsByChildLeaveParams = {
  birthday?: InputMaybe<Scalars['Float']['input']>;
  bizId?: InputMaybe<Scalars['String']['input']>;
  leaveEndTime?: InputMaybe<Scalars['Float']['input']>;
  leaveStartTime?: InputMaybe<Scalars['Float']['input']>;
  staffId?: InputMaybe<Scalars['Float']['input']>;
};

export type ShiftAdjustmentTicketsByChildLeaveResponse = {
  __typename?: 'ShiftAdjustmentTicketsByChildLeaveResponse';
  leaveType?: Maybe<Scalars['String']['output']>;
  shiftAdjustmentTickets?: Maybe<Array<ShiftAdjustmentTicket>>;
};

export type SimpleCreator = {
  __typename?: 'SimpleCreator';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SimpleCustomer = {
  __typename?: 'SimpleCustomer';
  id?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SimpleDeviceTypeRecord = {
  __typename?: 'SimpleDeviceTypeRecord';
  deviceType: Scalars['String']['output'];
  deviceTypeName: Scalars['String']['output'];
};

export type SimplePerformancePlan = {
  __typename?: 'SimplePerformancePlan';
  id: Scalars['Int']['output'];
  splitRule: Scalars['String']['output'];
};

export type SimpleResp = {
  __typename?: 'SimpleResp';
  id?: Maybe<Scalars['Int']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type SimpleSpace = {
  __typename?: 'SimpleSpace';
  label: Scalars['String']['output'];
  value: Scalars['String']['output'];
};

export type SimpleUser = {
  __typename?: 'SimpleUser';
  departmentId?: Maybe<Scalars['String']['output']>;
  departmentName?: Maybe<Scalars['String']['output']>;
  /** @deprecated Replaced by "departmentId" */
  deptId?: Maybe<Scalars['String']['output']>;
  /** @deprecated Replaced by "departmentName" */
  deptName?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  name: Scalars['String']['output'];
};

export type SimpleUserInput = {
  id: Scalars['Int']['input'];
  name: Scalars['String']['input'];
};

export type SingleCabinetPrice = {
  cabinetSepc?: InputMaybe<Scalars['Long']['input']>;
  sellPrice?: InputMaybe<Scalars['Long']['input']>;
};

export type SingleCustomerWhiteList = {
  __typename?: 'SingleCustomerWhiteList';
  approvingSpaceDetails?: Maybe<Array<ApproveSpaceDetail>>;
  approvingSpaceGuidList?: Maybe<Scalars['String']['output']>;
  company: CustomerCompany;
  createTime?: Maybe<Scalars['String']['output']>;
  creator?: Maybe<SingleCustomerWhiteListSimperUser>;
  email: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  mobile: Scalars['String']['output'];
  modifiedTime?: Maybe<Scalars['String']['output']>;
  modifier?: Maybe<SingleCustomerWhiteListSimperUser>;
  position?: Maybe<Scalars['String']['output']>;
  spaceGuidList?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type SingleCustomerWhiteListSimperUser = {
  __typename?: 'SingleCustomerWhiteListSimperUser';
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
};

export type SingleKwPriceRange = {
  singleKwPriceMax?: InputMaybe<Scalars['Long']['input']>;
  singleKwPriceMin?: InputMaybe<Scalars['Long']['input']>;
};

export type SinglePointRealtimeData = {
  __typename?: 'SinglePointRealtimeData';
  unit?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['Float']['output']>;
};

export type SingleTakeTicketQ = {
  taskNo: Scalars['String']['input'];
  taskType: Scalars['String']['input'];
};

export type SingleTakeTicketResponse = MutationResponse & {
  __typename?: 'SingleTakeTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SingleUploadMutationResponse = MutationResponse & {
  __typename?: 'SingleUploadMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  file: File;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SkipEventCurrentPhaseResponse = MutationResponse & {
  __typename?: 'SkipEventCurrentPhaseResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type Sort = {
  sortField: Scalars['String']['input'];
  sortOrder: Scalars['String']['input'];
};

export enum SortOrder {
  Ascend = 'ascend',
  Descend = 'descend'
}

export type Space = {
  __typename?: 'Space';
  children?: Maybe<Array<Space>>;
  custom?: Maybe<Scalars['JSONObject']['output']>;
  isVirtual?: Maybe<Scalars['Boolean']['output']>;
  label: Scalars['String']['output'];
  parentValue?: Maybe<Scalars['String']['output']>;
  type: SpaceNodeType;
  value: Scalars['String']['output'];
};

export type SpaceGuid = {
  __typename?: 'SpaceGuid';
  blockGuid?: Maybe<Scalars['String']['output']>;
  blockTag?: Maybe<Scalars['String']['output']>;
  idcGuid?: Maybe<Scalars['String']['output']>;
  roomGuid?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
};

export type SpaceLabelInValue = {
  __typename?: 'SpaceLabelInValue';
  label?: Maybe<Scalars['String']['output']>;
  parentCode?: Maybe<Scalars['String']['output']>;
  value?: Maybe<Scalars['String']['output']>;
};

export enum SpaceNodeType {
  Block = 'BLOCK',
  Floor = 'FLOOR',
  Idc = 'IDC',
  Region = 'REGION',
  Room = 'ROOM',
  RoomType = 'ROOM_TYPE'
}

export type SpacePermissionsTreeNode = {
  __typename?: 'SpacePermissionsTreeNode';
  children?: Maybe<Array<Maybe<SpacePermissionsTreeNode>>>;
  code?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  name?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
};

export type SpareInput = {
  deviceModelList?: InputMaybe<Array<DeviceModelInput>>;
  specific?: InputMaybe<Scalars['String']['input']>;
  targetRoom?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type SpareUniqueQueryQ = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  productModel?: InputMaybe<Scalars['String']['input']>;
  roomTag?: InputMaybe<Scalars['String']['input']>;
  spareType?: InputMaybe<Scalars['String']['input']>;
  vendor?: InputMaybe<Scalars['String']['input']>;
};

export type SpareUniqueQueryResponse = {
  __typename?: 'SpareUniqueQueryResponse';
  blockGuid?: Maybe<Scalars['String']['output']>;
  creatorId?: Maybe<Scalars['Long']['output']>;
  creatorName?: Maybe<Scalars['String']['output']>;
  gmtCreate?: Maybe<Scalars['String']['output']>;
  gmtModified?: Maybe<Scalars['String']['output']>;
  id?: Maybe<Scalars['Long']['output']>;
  idcTag?: Maybe<Scalars['String']['output']>;
  lockNum?: Maybe<Scalars['Long']['output']>;
  modifyPersonId?: Maybe<Scalars['Long']['output']>;
  modifyPersonName?: Maybe<Scalars['String']['output']>;
  productModel?: Maybe<Scalars['String']['output']>;
  roomTag?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  spareType?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  totalNum?: Maybe<Scalars['Long']['output']>;
  vendor?: Maybe<Scalars['String']['output']>;
};

export type SpecificDeviceQuery = {
  SN?: InputMaybe<Scalars['String']['input']>;
  assetSN?: InputMaybe<Scalars['String']['input']>;
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
};

export type Splitor = {
  __typename?: 'Splitor';
  splitParam?: Maybe<Scalars['String']['output']>;
  splitType: Scalars['String']['output'];
};

export type SplitorInput = {
  splitParam?: InputMaybe<Scalars['String']['input']>;
  splitType: Scalars['String']['input'];
};

export type Standard = {
  __typename?: 'Standard';
  checkNormal?: Maybe<Scalars['String']['output']>;
  checkScenes?: Maybe<Scalars['String']['output']>;
  checkStd?: Maybe<Scalars['String']['output']>;
  checkValueJson?: Maybe<CheckValueJson>;
  earliestOpTime?: Maybe<Scalars['Long']['output']>;
  id: Scalars['ID']['output'];
  isMeterRead: Scalars['Int']['output'];
  itemType: Scalars['String']['output'];
  roomGuid: Scalars['String']['output'];
  roomTag: Scalars['String']['output'];
};

export type StartExecutionPowerGridQ = {
  gridGuid: Scalars['String']['input'];
  pduDeviceGuidList: Array<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
};

export type StartExecutionPowerGridResponse = MutationResponse & {
  __typename?: 'StartExecutionPowerGridResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export enum StationType {
  Cdn = 'CDN',
  Master = 'MASTER',
  Pop = 'POP'
}

export type StepDeviceTypeInfoItem = {
  __typename?: 'StepDeviceTypeInfoItem';
  deviceInfoList?: Maybe<Array<Maybe<DeviceInfoItem>>>;
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  exceptionNum?: Maybe<Scalars['Long']['output']>;
  normalNum?: Maybe<Scalars['Long']['output']>;
  waitOpNum?: Maybe<Scalars['Long']['output']>;
  waitStartNum?: Maybe<Scalars['Long']['output']>;
};

export type StepList = {
  operatorId: Scalars['Long']['input'];
  operatorName: Scalars['String']['input'];
  stepDesc: Scalars['String']['input'];
  stepName: Scalars['String']['input'];
  stepOrder: Scalars['Long']['input'];
};

export type StepOpItemInfoItem = {
  __typename?: 'StepOpItemInfoItem';
  confirmResult?: Maybe<Scalars['String']['output']>;
  expectedValue?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  identifyWay?: Maybe<Scalars['String']['output']>;
  itemCode?: Maybe<Scalars['String']['output']>;
  itemName?: Maybe<Scalars['String']['output']>;
  itemStatus?: Maybe<Scalars['String']['output']>;
  itemType?: Maybe<Scalars['String']['output']>;
  opItemId?: Maybe<Scalars['Long']['output']>;
  opTime?: Maybe<Scalars['Long']['output']>;
  operatorId?: Maybe<Scalars['Long']['output']>;
  operatorName?: Maybe<Scalars['String']['output']>;
  pointCode?: Maybe<Scalars['String']['output']>;
  pointData?: Maybe<Scalars['String']['output']>;
  pointName?: Maybe<Scalars['String']['output']>;
  pointValueText?: Maybe<Scalars['String']['output']>;
  stepOrder?: Maybe<Scalars['Long']['output']>;
  validLimits?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type StopDrillOrderParams = {
  execNo: Scalars['String']['input'];
  stopReason: Scalars['String']['input'];
};

export type StorePropertiesResponse = {
  __typename?: 'StorePropertiesResponse';
  data?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SubmitChangeOfflineExecuteLogQ = {
  changeOrderId?: InputMaybe<Scalars['String']['input']>;
  exeContent?: InputMaybe<Scalars['String']['input']>;
  exeTime?: InputMaybe<Scalars['String']['input']>;
  id?: InputMaybe<Scalars['Long']['input']>;
  operatorId?: InputMaybe<Scalars['Long']['input']>;
  operatorName?: InputMaybe<Scalars['String']['input']>;
};

export type SubmitChangeOfflineExecuteLogResponse = MutationResponse & {
  __typename?: 'SubmitChangeOfflineExecuteLogResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SubmitChangeShiftCountersignDescriptionQ = {
  bizNo: Scalars['String']['input'];
  content: Scalars['String']['input'];
  files?: InputMaybe<Array<McUploadFileInput>>;
};

export type SubmitChangeShiftCountersignDescriptionResponse = MutationResponse & {
  __typename?: 'SubmitChangeShiftCountersignDescriptionResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SubstationDetail = {
  elecSupplyModel?: InputMaybe<Scalars['String']['input']>;
  substationName?: InputMaybe<Scalars['String']['input']>;
};

export type SummeryInfoResponse = {
  __typename?: 'SummeryInfoResponse';
  exeResult?: Maybe<Scalars['String']['output']>;
  fileInfoList?: Maybe<Array<Maybe<BackendFileInfo>>>;
  realEndTime?: Maybe<Scalars['Long']['output']>;
  realStartTime?: Maybe<Scalars['Long']['output']>;
  summarizePersonId?: Maybe<Scalars['Long']['output']>;
  summarizePersonName?: Maybe<Scalars['String']['output']>;
  summery?: Maybe<Scalars['String']['output']>;
};

export type SwitchInspectOfflineModelInputTaskInfo = {
  taskNo: Scalars['String']['input'];
  taskType: Scalars['String']['input'];
};

export type SwitchInspectOfflineModelQ = {
  taskInfoList: Array<SwitchInspectOfflineModelInputTaskInfo>;
};

export type SwitchInspectOfflineModelResponse = MutationResponse & {
  __typename?: 'SwitchInspectOfflineModelResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type SwitchRoleMutationResponse = MutationResponse & {
  __typename?: 'SwitchRoleMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type TakeTicketsByQ = {
  taskNos: Array<InputMaybe<Scalars['String']['input']>>;
  ticketsQ: TicketsQuery;
  total: Scalars['Long']['input'];
};

export type TakeTicketsQ = {
  taskNos: Array<InputMaybe<Scalars['String']['input']>>;
  taskType: Scalars['String']['input'];
};

export type TakeTicketsResponse = MutationResponse & {
  __typename?: 'TakeTicketsResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  tookTaskNos?: Maybe<Array<Maybe<Scalars['String']['output']>>>;
};

export type TaskDetailJson = {
  __typename?: 'TaskDetailJson';
  aggBlockScope?: Maybe<Array<AggBlock>>;
  allowTriggerTime?: Maybe<Array<Scalars['String']['output']>>;
  blockGuid: Scalars['String']['output'];
  blockScope: Array<PlanBlock>;
  createTime: Scalars['Long']['output'];
  creator: PlanCreator;
  endTime?: Maybe<Scalars['Long']['output']>;
  fileInfoList?: Maybe<Array<McUploadFile>>;
  finishTaskNoList?: Maybe<Array<Scalars['String']['output']>>;
  guidePeriod?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  isActivated: IsActivatedKeyMap;
  isInEffect?: Maybe<Scalars['String']['output']>;
  jobItemList?: Maybe<Array<Maybe<JobItem>>>;
  jobSla?: Maybe<Scalars['Int']['output']>;
  lastExecuteResult: Array<Scalars['String']['output']>;
  lastExecuteTime?: Maybe<Scalars['Long']['output']>;
  manageType?: Maybe<ManageTypeKeyMap>;
  modifyTime?: Maybe<Scalars['Long']['output']>;
  modifyUser?: Maybe<PlanModifyUser>;
  mopCount?: Maybe<Scalars['Int']['output']>;
  mopType: Scalars['String']['output'];
  name: Scalars['String']['output'];
  periodUnit?: Maybe<Scalars['String']['output']>;
  planType: Scalars['String']['output'];
  repeatCycle?: Maybe<Cycle>;
  schLevel?: Maybe<Scalars['String']['output']>;
  slaUnit?: Maybe<Scalars['String']['output']>;
  splitor?: Maybe<Array<Splitor>>;
  subJobType?: Maybe<Scalars['String']['output']>;
  taskResultNums?: Maybe<Scalars['Int']['output']>;
  unFinishTaskNoList?: Maybe<Array<Scalars['String']['output']>>;
};

export type TaskJson = {
  __typename?: 'TaskJson';
  aggBlockScope?: Maybe<Array<AggBlock>>;
  allowTriggerTime?: Maybe<Array<Scalars['String']['output']>>;
  blockGuid: Scalars['String']['output'];
  blockScope: Array<PlanBlock>;
  createTime: Scalars['Long']['output'];
  creator: PlanCreator;
  endTime?: Maybe<Scalars['Long']['output']>;
  finishTaskNoList?: Maybe<Array<Scalars['String']['output']>>;
  guidePeriod?: Maybe<Scalars['String']['output']>;
  id: Scalars['Int']['output'];
  isActivated: Scalars['String']['output'];
  isInEffect?: Maybe<Scalars['String']['output']>;
  jobItemList?: Maybe<Array<Maybe<JobItem>>>;
  jobSla?: Maybe<Scalars['Int']['output']>;
  lastExecuteResult: Array<Scalars['String']['output']>;
  lastExecuteTime?: Maybe<Scalars['Long']['output']>;
  manageType?: Maybe<Scalars['String']['output']>;
  modifyTime?: Maybe<Scalars['Long']['output']>;
  mopCount?: Maybe<Scalars['Int']['output']>;
  mopType: Scalars['String']['output'];
  name: Scalars['String']['output'];
  periodUnit?: Maybe<Scalars['String']['output']>;
  planType: Scalars['String']['output'];
  repeatCycle?: Maybe<Cycle>;
  schLevel?: Maybe<Scalars['String']['output']>;
  slaUnit?: Maybe<Scalars['String']['output']>;
  splitor?: Maybe<Array<Splitor>>;
  taskResultNums?: Maybe<Scalars['Int']['output']>;
  unFinishTaskNoList?: Maybe<Array<Scalars['String']['output']>>;
};

export type TaskRedirectInfo = {
  __typename?: 'TaskRedirectInfo';
  redirectTime: Scalars['Long']['output'];
  sourceUserId: Scalars['Int']['output'];
  targetUserId: Scalars['Int']['output'];
};

export type TaskStatus = {
  currentAssignee?: InputMaybe<Scalars['Boolean']['input']>;
  taskStatus?: InputMaybe<Scalars['String']['input']>;
};

export type TasksResponse = {
  __typename?: 'TasksResponse';
  data: Array<TaskJson>;
  total: Scalars['Long']['output'];
};

export type TeamUser = {
  __typename?: 'TeamUser';
  deleted: Scalars['Boolean']['output'];
  departmentId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  fullDeptName?: Maybe<Scalars['String']['output']>;
  id: Scalars['Long']['output'];
  login: Scalars['String']['output'];
  mobile?: Maybe<Scalars['String']['output']>;
  name: Scalars['String']['output'];
  title?: Maybe<Scalars['String']['output']>;
};

export type TeamUsersResponse = {
  __typename?: 'TeamUsersResponse';
  data?: Maybe<Array<TeamUser>>;
  total: Scalars['Long']['output'];
};

export type Ticket = {
  __typename?: 'Ticket';
  assigneeList?: Maybe<Array<Maybe<AssigneeList>>>;
  createdAt: Scalars['Long']['output'];
  creatorName: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  location?: Maybe<Scalars['String']['output']>;
  slaInfos?: Maybe<TicketSlaInfos>;
  sourceOfOrder?: Maybe<Scalars['String']['output']>;
  status?: Maybe<Scalars['String']['output']>;
  subType?: Maybe<Scalars['String']['output']>;
  taskProperties?: Maybe<Scalars['String']['output']>;
  taskType?: Maybe<Scalars['String']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  tookEffectAt: Scalars['Long']['output'];
};

export type TicketBaseInfos = {
  __typename?: 'TicketBaseInfos';
  approvalCode?: Maybe<Scalars['String']['output']>;
  assignee?: Maybe<User>;
  assigneeList?: Maybe<Array<Maybe<AssigneeList>>>;
  blockGuid: Scalars['String']['output'];
  createdAt: Scalars['Long']['output'];
  failedDesc?: Maybe<Scalars['String']['output']>;
  failedReason?: Maybe<Scalars['String']['output']>;
  id: Scalars['ID']['output'];
  relateBizType?: Maybe<Scalars['String']['output']>;
  relateTaskNo?: Maybe<Scalars['String']['output']>;
  roomGuid?: Maybe<Scalars['String']['output']>;
  sla: Scalars['Long']['output'];
  taskProperties?: Maybe<Scalars['String']['output']>;
  taskStatus: Scalars['String']['output'];
  taskSubType?: Maybe<Scalars['String']['output']>;
  tookEffectAt?: Maybe<Scalars['Long']['output']>;
};

export type TicketChecklist = {
  __typename?: 'TicketChecklist';
  postItems?: Maybe<Array<Maybe<TicketChecklistItem>>>;
  preItems?: Maybe<Array<Maybe<TicketChecklistItem>>>;
};

export type TicketChecklistItem = {
  __typename?: 'TicketChecklistItem';
  id: Scalars['ID']['output'];
  methods: Array<Maybe<TicketChecklistItemMethod>>;
  name: Scalars['String']['output'];
};

export type TicketChecklistItemMethod = {
  __typename?: 'TicketChecklistItemMethod';
  dataRange?: Maybe<TicketChecklistItemMethodDataRange>;
  dataRequired: Scalars['Boolean']['output'];
  /** 检查方法名称 */
  name: Scalars['String']['output'];
  /** 检查标准 */
  spec?: Maybe<Scalars['String']['output']>;
};

export type TicketChecklistItemMethodDataRange = {
  __typename?: 'TicketChecklistItemMethodDataRange';
  max?: Maybe<Scalars['Float']['output']>;
  min?: Maybe<Scalars['Float']['output']>;
};

export type TicketFirstTimeRecipientInfo = {
  __typename?: 'TicketFirstTimeRecipientInfo';
  takeTime?: Maybe<Scalars['String']['output']>;
  taskAssignee?: Maybe<Scalars['Int']['output']>;
  taskAssigneeName?: Maybe<Scalars['String']['output']>;
};

export type TicketHandleTypeQuery = {
  type: Scalars['String']['input'];
};

export type TicketHandleTypeResponse = {
  __typename?: 'TicketHandleTypeResponse';
  children?: Maybe<Array<Maybe<LabelInValue>>>;
  key: Scalars['String']['output'];
  label: Scalars['String']['output'];
};

export type TicketNotification = {
  __typename?: 'TicketNotification';
  bizId: Scalars['String']['output'];
  bizStatus: BizStatus;
  bizType: Scalars['String']['output'];
  creatorId: Scalars['Int']['output'];
  creatorName: Scalars['String']['output'];
  eventCode: Scalars['String']['output'];
  gmtCreate: Scalars['String']['output'];
  reportChannel: Scalars['String']['output'];
  reportContent: Scalars['String']['output'];
  reportObjectList: Array<Maybe<ReportObject>>;
};

export type TicketNotificationsResponse = {
  __typename?: 'TicketNotificationsResponse';
  data: Array<TicketNotification>;
  total: Scalars['Long']['output'];
};

export type TicketOperationalLogItem = {
  __typename?: 'TicketOperationalLogItem';
  content: Scalars['String']['output'];
  id: Scalars['ID']['output'];
  module: Scalars['String']['output'];
  time: Scalars['Long']['output'];
  user: User;
};

export type TicketOperationalLogSection = {
  __typename?: 'TicketOperationalLogSection';
  data: Array<Maybe<TicketOperationalLogItem>>;
  id: Scalars['ID']['output'];
  title: Scalars['String']['output'];
};

export type TicketOperationalLogs = {
  __typename?: 'TicketOperationalLogs';
  data: Array<Maybe<TicketOperationalLogSection>>;
  hasMore: Scalars['Boolean']['output'];
  id: Scalars['ID']['output'];
};

export type TicketOperationalLogsQuery = {
  page?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  taskNo: Scalars['String']['input'];
  taskType: Scalars['String']['input'];
};

export type TicketProgressByRoom = {
  __typename?: 'TicketProgressByRoom';
  countMap: TicketProgressByRoomCountMap;
  finished?: Maybe<Scalars['Boolean']['output']>;
  id?: Maybe<Scalars['String']['output']>;
  roomGuid: Scalars['String']['output'];
  roomTag: Scalars['String']['output'];
  roomType?: Maybe<Scalars['String']['output']>;
  roomTypeName?: Maybe<Scalars['String']['output']>;
  timeElapsedMs?: Maybe<Scalars['String']['output']>;
};

export type TicketProgressByRoomCountMap = {
  __typename?: 'TicketProgressByRoomCountMap';
  exceptionCount: Scalars['Long']['output'];
  finishedCount: Scalars['Long']['output'];
  pendingCount: Scalars['Long']['output'];
};

export type TicketProgressByRoomQ = {
  blockGuid?: InputMaybe<Scalars['String']['input']>;
  taskNo: Scalars['String']['input'];
  taskType: Scalars['String']['input'];
};

export type TicketProgressByRoomResponse = {
  __typename?: 'TicketProgressByRoomResponse';
  roomTypes: Array<Maybe<LabelInValue>>;
  rooms: Array<Maybe<TicketProgressByRoom>>;
};

export type TicketRelateApproval = {
  __typename?: 'TicketRelateApproval';
  gmtCreate: Scalars['String']['output'];
  gmtModified: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  processId: Scalars['String']['output'];
  taskNo: Scalars['String']['output'];
  taskNode: Scalars['String']['output'];
};

export type TicketRelateApprovalIdsResponse = {
  __typename?: 'TicketRelateApprovalIdsResponse';
  data: Array<TicketRelateApproval>;
  total: Scalars['Long']['output'];
};

export type TicketSlaInfos = {
  __typename?: 'TicketSlaInfos';
  isTimeOut?: Maybe<Scalars['Boolean']['output']>;
  timeDenominator?: Maybe<Scalars['String']['output']>;
  timeNumerator?: Maybe<Scalars['String']['output']>;
};

export type TicketTypeNode = {
  __typename?: 'TicketTypeNode';
  children?: Maybe<Array<TicketTypeNode>>;
  isDeleted: Scalars['Boolean']['output'];
  label: Scalars['String']['output'];
  parentValue?: Maybe<Scalars['String']['output']>;
  value: Scalars['String']['output'];
};

export type TicketTypeTreeNode = {
  __typename?: 'TicketTypeTreeNode';
  children?: Maybe<Array<Maybe<TicketTypeTreeNode>>>;
  taskType: Scalars['String']['output'];
  taskValue: Scalars['String']['output'];
};

export type TicketsQuery = {
  blockTag?: InputMaybe<Scalars['String']['input']>;
  configRoomGuid?: InputMaybe<Scalars['String']['input']>;
  creatorName?: InputMaybe<Scalars['String']['input']>;
  effectEndTime?: InputMaybe<Scalars['Long']['input']>;
  effectStartTime?: InputMaybe<Scalars['Long']['input']>;
  endTime?: InputMaybe<Scalars['Long']['input']>;
  idcTag?: InputMaybe<Scalars['String']['input']>;
  isDelay?: InputMaybe<Scalars['String']['input']>;
  multiField?: InputMaybe<Scalars['String']['input']>;
  page?: InputMaybe<Scalars['Long']['input']>;
  pageSize?: InputMaybe<Scalars['Long']['input']>;
  roomGuidList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  startTime?: InputMaybe<Scalars['Long']['input']>;
  taskAssignee?: InputMaybe<Scalars['Long']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
  taskNoList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  taskStatus?: InputMaybe<Scalars['String']['input']>;
  taskStatusList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  taskSubTypeList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  taskType?: InputMaybe<Scalars['String']['input']>;
};

export type TicketsResponse = {
  __typename?: 'TicketsResponse';
  data: Array<Maybe<Ticket>>;
  id: Scalars['ID']['output'];
  total: Scalars['Long']['output'];
};

export type TicketsStatistics = {
  __typename?: 'TicketsStatistics';
  countMap?: Maybe<Scalars['JSONObject']['output']>;
  onProcessingTotal?: Maybe<Scalars['Long']['output']>;
  pendingTakeTotal?: Maybe<Scalars['Long']['output']>;
};

export type TicketsStatisticsQuery = {
  blockGuidList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  idcTagList?: InputMaybe<Array<InputMaybe<Scalars['String']['input']>>>;
  taskStatusList: Array<InputMaybe<TicketsStatisticsQueryTaskStatusList>>;
};

export type TicketsStatisticsQueryTaskStatusList = {
  currentAssignee: Scalars['Boolean']['input'];
  taskStatus: Scalars['String']['input'];
};

export type TransferDrillOrderParams = {
  assignReason?: InputMaybe<Scalars['String']['input']>;
  execNo: Scalars['String']['input'];
  taskAssignee: Scalars['Long']['input'];
};

export type TransferInvoiceResponse = MutationResponse & {
  __typename?: 'TransferInvoiceResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type TransferInvoiceUser = {
  __typename?: 'TransferInvoiceUser';
  invoiceUserId: Scalars['Int']['output'];
  invoiceUserName: Scalars['String']['output'];
};

export type TransferInvoiceUsersResponse = {
  __typename?: 'TransferInvoiceUsersResponse';
  data?: Maybe<Array<TransferInvoiceUser>>;
  total: Scalars['Long']['output'];
};

export type TransferSalesOpportunityOwnerMutationResponse = {
  __typename?: 'TransferSalesOpportunityOwnerMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type TransferTicketQuery = {
  reassigneeDesc?: InputMaybe<Scalars['String']['input']>;
  taskAssignee?: InputMaybe<Scalars['Int']['input']>;
  taskAssigneeName?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
  taskType?: InputMaybe<Scalars['String']['input']>;
};

export type TransferTicketResponse = MutationResponse & {
  __typename?: 'TransferTicketResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  result: Scalars['String']['output'];
  success: Scalars['Boolean']['output'];
};

export type Type = {
  __typename?: 'Type';
  code?: Maybe<Scalars['String']['output']>;
  name?: Maybe<Scalars['String']['output']>;
};

export type UpdateAlarmShieldParams = {
  blockGuid: Scalars['String']['input'];
  endTime: Scalars['Long']['input'];
  id?: InputMaybe<Scalars['Long']['input']>;
  name: Scalars['String']['input'];
  shieldScopeList?: InputMaybe<Array<ShieldScopeInput>>;
  startTime: Scalars['Long']['input'];
};

export type UpdateAlarmShieldResponse = {
  __typename?: 'UpdateAlarmShieldResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateAuthorizationRecordResponse = MutationResponse & {
  __typename?: 'UpdateAuthorizationRecordResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateChangeOrderDeviceAlarmStatusQ = {
  changeOrderId: Scalars['String']['input'];
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
  deviceName?: InputMaybe<Scalars['String']['input']>;
  inhibition: Scalars['Boolean']['input'];
};

export type UpdateChangeOrderDeviceAlarmStatusResponse = MutationResponse & {
  __typename?: 'UpdateChangeOrderDeviceAlarmStatusResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateChangeShiftConfigQ = {
  handoverDutyCheck: Scalars['Long']['input'];
  handoverDutyCheckType?: InputMaybe<Scalars['Long']['input']>;
};

export type UpdateChangeShiftConfigResponse = MutationResponse & {
  __typename?: 'UpdateChangeShiftConfigResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateCustomerMutationResponse = {
  __typename?: 'UpdateCustomerMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateCustomerParams = {
  abbreviation?: InputMaybe<Scalars['String']['input']>;
  address?: InputMaybe<CustomerAddressInput>;
  assistList?: InputMaybe<Array<Scalars['Long']['input']>>;
  code: Scalars['String']['input'];
  contacts?: InputMaybe<Array<CustomerContactInput>>;
  deptId: Scalars['String']['input'];
  description?: InputMaybe<Scalars['String']['input']>;
  industry?: InputMaybe<Scalars['String']['input']>;
  invoices?: InputMaybe<Array<InvoiceInput>>;
  knowOrganizationalStructure: Scalars['Boolean']['input'];
  name: Scalars['String']['input'];
  owner: SimpleUserInput;
  source?: InputMaybe<Scalars['String']['input']>;
  status?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type UpdateCustomerWhiteListMutationResponse = MutationResponse & {
  __typename?: 'UpdateCustomerWhiteListMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateDrillOrderRemarkResponse = {
  __typename?: 'UpdateDrillOrderRemarkResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateDrillOrderResponse = {
  __typename?: 'UpdateDrillOrderResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateFeeAdditionalInfoResponse = MutationResponse & {
  __typename?: 'UpdateFeeAdditionalInfoResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateInspectOfflineData = {
  __typename?: 'UpdateInspectOfflineData';
  errorMsg?: Maybe<Scalars['String']['output']>;
  taskNo?: Maybe<Scalars['String']['output']>;
  uploadResult?: Maybe<Scalars['String']['output']>;
};

export type UpdateInspectOfflineDataQ = {
  checkResultInfoList: Array<CheckResultInfo>;
  isFinish: Scalars['Boolean']['input'];
  uploadId: Scalars['String']['input'];
};

export type UpdateInspectOfflineDataResponse = MutationResponse & {
  __typename?: 'UpdateInspectOfflineDataResponse';
  code?: Maybe<Scalars['String']['output']>;
  data: Array<UpdateInspectOfflineData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateInspectScanQrLogQ = {
  scanQrCodeInfoList: Array<ScanQrCodeInfo>;
};

export type UpdateInspectScanQrLogResponse = MutationResponse & {
  __typename?: 'UpdateInspectScanQrLogResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateInspectionDeviceStatusData = {
  __typename?: 'UpdateInspectionDeviceStatusData';
  deviceGuid?: Maybe<Scalars['String']['output']>;
  deviceStatus?: Maybe<Scalars['String']['output']>;
};

export type UpdateInspectionDeviceStatusQ = {
  deviceGuid?: InputMaybe<Scalars['String']['input']>;
  deviceStatus?: InputMaybe<Scalars['String']['input']>;
  taskNo?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateInspectionDeviceStatusResponse = MutationResponse & {
  __typename?: 'UpdateInspectionDeviceStatusResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<UpdateInspectionDeviceStatusData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateOrDeleteNonstandardInspectItemQ = {
  exDesc: Scalars['String']['input'];
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  isDelete?: InputMaybe<Scalars['Boolean']['input']>;
  nsItemId: Scalars['Long']['input'];
  subjectName: Scalars['String']['input'];
};

export type UpdateOrDeleteNonstandardInspectItemResponse = MutationResponse & {
  __typename?: 'UpdateOrDeleteNonstandardInspectItemResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdatePowerGridTimeQ = {
  gridGuid: Scalars['String']['input'];
  modReason: Scalars['String']['input'];
  powerRealTime: Scalars['Long']['input'];
  taskNo: Scalars['String']['input'];
};

export type UpdatePowerGridTimeResponse = MutationResponse & {
  __typename?: 'UpdatePowerGridTimeResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateProjectMapLog = {
  __typename?: 'UpdateProjectMapLog';
  authInstId?: Maybe<Scalars['String']['output']>;
  effectiveTime?: Maybe<Scalars['String']['output']>;
  gmtCreate: Scalars['String']['output'];
  updateContentAfter?: Maybe<Scalars['JSONObject']['output']>;
  updateContentBefore?: Maybe<Scalars['JSONObject']['output']>;
  updateModel: UpdateProjectMapLogModel;
  updateModule: UpdateProjectMapLogModule;
  updateStatus: UpdateProjectMapLogUpdateStatus;
  updateUserId: Scalars['Long']['output'];
};

export enum UpdateProjectMapLogModel {
  Add = 'ADD',
  Update = 'UPDATE'
}

export enum UpdateProjectMapLogModule {
  BaseInfo = 'BASE_INFO',
  DesignInfo = 'DESIGN_INFO',
  QuotationInfo = 'QUOTATION_INFO',
  SalesInfo = 'SALES_INFO'
}

export type UpdateProjectMapLogResponse = {
  __typename?: 'UpdateProjectMapLogResponse';
  data: Array<UpdateProjectMapLog>;
  total: Scalars['Long']['output'];
};

export enum UpdateProjectMapLogUpdateStatus {
  Approving = 'APPROVING',
  Failed = 'FAILED',
  Revoke = 'REVOKE',
  Succ = 'SUCC'
}

export type UpdateProjectMapParams = {
  actualRackRate?: InputMaybe<Scalars['Long']['input']>;
  actualRentedCabinets?: InputMaybe<Scalars['Long']['input']>;
  addedServices?: InputMaybe<Array<InputMaybe<AddedService>>>;
  baseHistoryTrade?: InputMaybe<BaseHistoryTrade>;
  computerRoomArea?: InputMaybe<Scalars['Long']['input']>;
  constructArea?: InputMaybe<Scalars['Long']['input']>;
  contractCabinets?: InputMaybe<Scalars['Long']['input']>;
  contractOccuRate?: InputMaybe<Scalars['Long']['input']>;
  countyName: Scalars['String']['input'];
  customizedIdcRoom: CustomizedIdcRoom;
  designLevel?: InputMaybe<Scalars['String']['input']>;
  designPue?: InputMaybe<Scalars['Long']['input']>;
  geoPosition: Scalars['String']['input'];
  idcCompanyName: Scalars['String']['input'];
  idcProjectId?: InputMaybe<Scalars['Long']['input']>;
  natureProp?: InputMaybe<Scalars['String']['input']>;
  operationTime?: InputMaybe<Scalars['String']['input']>;
  operatorCoopInfo: OperatorCoopInfo;
  optStatus: Scalars['String']['input'];
  optStatusDetail?: InputMaybe<Scalars['String']['input']>;
  passCert?: InputMaybe<Scalars['Boolean']['input']>;
  phased: Scalars['Boolean']['input'];
  phasedIn?: InputMaybe<Scalars['String']['input']>;
  projectAddress: Scalars['String']['input'];
  projectName: Scalars['String']['input'];
  quotationRecords?: InputMaybe<Array<InputMaybe<QuotationRecord>>>;
  salesHistoryTrade?: InputMaybe<SalesHistoryTrade>;
  sellStatus: Scalars['String']['input'];
  sellableItCabinetSpecs?: InputMaybe<Array<SellableItCabinetSpec>>;
  sellableItLoad?: InputMaybe<Scalars['Long']['input']>;
  singleKwCost?: InputMaybe<Scalars['Long']['input']>;
  singleTransCap?: InputMaybe<Scalars['Long']['input']>;
  standardTons?: InputMaybe<Scalars['Long']['input']>;
  starLevel?: InputMaybe<Scalars['String']['input']>;
  substationDetail?: InputMaybe<Array<InputMaybe<SubstationDetail>>>;
  substationNums?: InputMaybe<Scalars['Long']['input']>;
  tonsCanMulMerge?: InputMaybe<Scalars['Boolean']['input']>;
  totalItLoad?: InputMaybe<Scalars['Long']['input']>;
  totalOptCabinets?: InputMaybe<Scalars['Long']['input']>;
  transCanMulMerge?: InputMaybe<Scalars['Boolean']['input']>;
  uptimeLevel?: InputMaybe<Scalars['String']['input']>;
};

export type UpdateProjectMapQuotationParams = {
  addedServices?: InputMaybe<Array<InputMaybe<AddedService>>>;
  idcProjectId: Scalars['Long']['input'];
  quotationRecords?: InputMaybe<Array<InputMaybe<QuotationRecord>>>;
  singleKwCost?: InputMaybe<Scalars['Long']['input']>;
};

export type UpdateProjectMapQuotationResponse = {
  __typename?: 'UpdateProjectMapQuotationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateProjectMapResponse = {
  __typename?: 'UpdateProjectMapResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateRiskPoolQ = {
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  id: Scalars['Long']['input'];
  riskCategory: Scalars['String']['input'];
  riskDesc: Scalars['String']['input'];
  riskLevel: RiskLevel;
  riskType: Scalars['String']['input'];
  verifyList: Array<Scalars['String']['input']>;
};

export type UpdateRiskPoolResponse = MutationResponse & {
  __typename?: 'UpdateRiskPoolResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateRiskPoolStatusQ = {
  id: Scalars['Long']['input'];
  isEnable: Scalars['String']['input'];
};

export type UpdateRiskPoolStatusResponse = MutationResponse & {
  __typename?: 'UpdateRiskPoolStatusResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateSalesRackResponse = MutationResponse & {
  __typename?: 'UpdateSalesRackResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UpdateTaskResponse = MutationResponse & {
  __typename?: 'UpdateTaskResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UploadInspectItemFileQ = {
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  inspectItemId: Scalars['Long']['input'];
};

export type UploadInspectItemFileResponse = MutationResponse & {
  __typename?: 'UploadInspectItemFileResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UploadMaintenanceItemFileQ = {
  fileInfoList?: InputMaybe<Array<McUploadFileInput>>;
  maintenanceItemId: Scalars['Long']['input'];
};

export type UploadMaintenanceItemFileResponse = MutationResponse & {
  __typename?: 'UploadMaintenanceItemFileResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type UploadUser = {
  id?: InputMaybe<Scalars['Long']['input']>;
  name?: InputMaybe<Scalars['String']['input']>;
};

export type User = {
  __typename?: 'User';
  id?: Maybe<Scalars['Long']['output']>;
  loginName?: Maybe<Scalars['String']['output']>;
  userName?: Maybe<Scalars['String']['output']>;
};

export type UserHolidayBalance = {
  carryOverAvailableBalance: Scalars['Float']['input'];
  carryOverBalance: Scalars['Float']['input'];
  carryOverUsedBalance: Scalars['Float']['input'];
  companyAvailableBalance: Scalars['Float']['input'];
  companyBalance: Scalars['Float']['input'];
  companyUsedBalance: Scalars['Float']['input'];
  jobNumber: Scalars['String']['input'];
  sickSalaryAvailableBalance: Scalars['Float']['input'];
  sickSalaryBalance: Scalars['Float']['input'];
  sickSalaryUsedBalance: Scalars['Float']['input'];
  staffId: Scalars['String']['input'];
  statutoryAvailableBalance: Scalars['Float']['input'];
  statutoryBalance: Scalars['Float']['input'];
  statutoryUsedBalance: Scalars['Float']['input'];
  totalAvailableBalance: Scalars['Float']['input'];
  totalUsedBalance: Scalars['Float']['input'];
  userName: Scalars['String']['input'];
};

export type UserHrmBalance = {
  carryOverAvailableBalance: Scalars['Float']['input'];
  carryOverBalance: Scalars['Float']['input'];
  carryOverUsedBalance: Scalars['Float']['input'];
  companyAvailableBalance: Scalars['Float']['input'];
  companyBalance: Scalars['Float']['input'];
  companyUsedBalance: Scalars['Float']['input'];
  jobNumber: Scalars['String']['input'];
  sickSalaryAvailableBalance: Scalars['Float']['input'];
  sickSalaryBalance: Scalars['Float']['input'];
  sickSalaryUsedBalance: Scalars['Float']['input'];
  staffId: Scalars['String']['input'];
  statutoryAvailableBalance: Scalars['Float']['input'];
  statutoryBalance: Scalars['Float']['input'];
  statutoryUsedBalance: Scalars['Float']['input'];
  totalAvailableBalance: Scalars['Float']['input'];
  totalUsedBalance: Scalars['Float']['input'];
  userName: Scalars['String']['input'];
};

export type UserInfo = {
  __typename?: 'UserInfo';
  birthPlace?: Maybe<Scalars['String']['output']>;
  birthday?: Maybe<Scalars['String']['output']>;
  certCount?: Maybe<Scalars['Long']['output']>;
  company?: Maybe<Scalars['String']['output']>;
  createUser?: Maybe<SimpleUser>;
  department?: Maybe<Scalars['String']['output']>;
  departmentId?: Maybe<Scalars['String']['output']>;
  email?: Maybe<Scalars['String']['output']>;
  gender?: Maybe<Scalars['String']['output']>;
  gmtCreate?: Maybe<Scalars['Long']['output']>;
  gmtModified?: Maybe<Scalars['Long']['output']>;
  hiredDate?: Maybe<Scalars['Long']['output']>;
  id: Scalars['Long']['output'];
  idc?: Maybe<Scalars['String']['output']>;
  jobDescriptions?: Maybe<Scalars['String']['output']>;
  joinWorkingDate?: Maybe<Scalars['Long']['output']>;
  lastLoginTime?: Maybe<Scalars['Long']['output']>;
  login?: Maybe<Scalars['String']['output']>;
  mobileNumber?: Maybe<Scalars['String']['output']>;
  modifyUser?: Maybe<SimpleUser>;
  name?: Maybe<Scalars['String']['output']>;
  nameEn?: Maybe<Scalars['String']['output']>;
  remarks?: Maybe<Scalars['String']['output']>;
  resourceCodes?: Maybe<Array<Scalars['String']['output']>>;
  signature?: Maybe<Scalars['String']['output']>;
  state?: Maybe<Scalars['String']['output']>;
  subSupervisorUid?: Maybe<Scalars['Long']['output']>;
  supervisorUid?: Maybe<Scalars['Long']['output']>;
  title?: Maybe<Scalars['String']['output']>;
  type?: Maybe<Scalars['String']['output']>;
  userShifts?: Maybe<Scalars['String']['output']>;
  workplace?: Maybe<Scalars['String']['output']>;
};

export type UserLastedChildBirthdayResponse = {
  __typename?: 'UserLastedChildBirthdayResponse';
  birthday?: Maybe<Scalars['String']['output']>;
};

export type UserResource = {
  __typename?: 'UserResource';
  children?: Maybe<Array<UserResource>>;
  code: Scalars['String']['output'];
  id: Scalars['Long']['output'];
  name: Scalars['String']['output'];
  parentId: Scalars['Long']['output'];
  type: AuthorizationResourceType;
};

export type ValidAnnualPerformanceTargetChangeResponse = {
  __typename?: 'ValidAnnualPerformanceTargetChangeResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<Scalars['Boolean']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type VendorModalTreeResponse = {
  __typename?: 'VendorModalTreeResponse';
  children?: Maybe<Array<Maybe<LabelInValue>>>;
  key: Scalars['String']['output'];
  label: Scalars['String']['output'];
};

export type Version = {
  __typename?: 'Version';
  builtAt?: Maybe<Scalars['String']['output']>;
  version: Scalars['String']['output'];
};

export type VisitTicket = {
  __typename?: 'VisitTicket';
  allowedTimeRage: Array<Scalars['Long']['output']>;
  authorizedArea: Array<Scalars['String']['output']>;
  bpmInstanceId: Scalars['String']['output'];
  idc: Scalars['String']['output'];
  purpose: Scalars['String']['output'];
  receptionist: AnySimpleUser;
  relatedTicketNumber?: Maybe<Scalars['String']['output']>;
  relatedTicketType?: Maybe<Scalars['String']['output']>;
  subType: Scalars['String']['output'];
  thirdType: Scalars['String']['output'];
  ticketNumber: Scalars['String']['output'];
  title: Scalars['String']['output'];
  type: Scalars['String']['output'];
  visitors: Array<Visitor>;
};

export type VisitTicketMutationResponse = {
  __typename?: 'VisitTicketMutationResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
  ticketNumber?: Maybe<Scalars['String']['output']>;
};

export type Visitor = {
  __typename?: 'Visitor';
  ICN?: Maybe<Scalars['String']['output']>;
  LPN?: Maybe<Scalars['String']['output']>;
  companyName?: Maybe<Scalars['String']['output']>;
  identificationType?: Maybe<Scalars['String']['output']>;
  mobile: Scalars['String']['output'];
  mobileAreaCode: Scalars['String']['output'];
  name: Scalars['String']['output'];
  operationalAuthorization?: Maybe<YesOrNo>;
  personalGoods?: Maybe<Array<Scalars['String']['output']>>;
  position?: Maybe<Scalars['String']['output']>;
  type: Scalars['String']['output'];
};

export type VisitorInput = {
  ICN?: InputMaybe<Scalars['String']['input']>;
  LPN?: InputMaybe<Scalars['String']['input']>;
  companyName?: InputMaybe<Scalars['String']['input']>;
  identificationType?: InputMaybe<Scalars['String']['input']>;
  mobile: Scalars['String']['input'];
  mobileAreaCode: Scalars['String']['input'];
  name: Scalars['String']['input'];
  operationalAuthorization?: InputMaybe<YesOrNo>;
  personalGoods?: InputMaybe<Array<Scalars['String']['input']>>;
  position?: InputMaybe<Scalars['String']['input']>;
  type: Scalars['String']['input'];
};

export type VisitorNotifyInfoInput = {
  email: Scalars['String']['input'];
  type: AnySimpleUserType;
  userId?: InputMaybe<Scalars['Long']['input']>;
  userName?: InputMaybe<Scalars['String']['input']>;
};

export type WarehouseFee = {
  __typename?: 'WarehouseFee';
  adjustInstId?: Maybe<Scalars['String']['output']>;
  area?: Maybe<Scalars['Float']['output']>;
  blockGuid: Scalars['String']['output'];
  chargeFee: Scalars['Float']['output'];
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  feeId: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  originFee: Scalars['Float']['output'];
  preferentialFee: Scalars['Float']['output'];
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  roomGuid: Scalars['String']['output'];
  roomTag: Scalars['String']['output'];
  taxFee?: Maybe<Scalars['Float']['output']>;
  useAmount?: Maybe<Scalars['Float']['output']>;
};

export type WarehouseFeesResponse = {
  __typename?: 'WarehouseFeesResponse';
  data?: Maybe<Array<WarehouseFee>>;
};

export type WarehouseInCreateQ = {
  blockGuid: Scalars['String']['input'];
  deviceModelList: Array<InputMaybe<DeviceModel>>;
  fileInfoList?: InputMaybe<Array<InputMaybe<FileInfo>>>;
  idcTag: Scalars['String']['input'];
  inWarehouseReason: Scalars['String']['input'];
  numbered: Scalars['Boolean']['input'];
  relateTaskNo?: InputMaybe<Scalars['String']['input']>;
  roomGuid: Scalars['String']['input'];
  taskTitle: Scalars['String']['input'];
};

export type WarehouseInCreateResponse = MutationResponse & {
  __typename?: 'WarehouseInCreateResponse';
  code?: Maybe<Scalars['String']['output']>;
  data?: Maybe<WarehouseInCreateResponseData>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type WarehouseInCreateResponseData = {
  __typename?: 'WarehouseInCreateResponseData';
  respMsg?: Maybe<Scalars['String']['output']>;
  taskNo?: Maybe<Scalars['String']['output']>;
};

export enum Website {
  Config = 'CONFIG',
  Customer = 'CUSTOMER',
  Dcbase = 'DCBASE',
  Finance = 'FINANCE',
  Maintenance = 'MAINTENANCE',
  Monitor = 'MONITOR',
  Sale = 'SALE'
}

export enum WebsiteCode {
  Config = 'CONFIG',
  Customer = 'CUSTOMER',
  Dcbase = 'DCBASE',
  Finance = 'FINANCE',
  Maintenance = 'MAINTENANCE',
  Monitor = 'MONITOR',
  Sale = 'SALE'
}

export type WorkplaceFee = {
  __typename?: 'WorkplaceFee';
  adjustInstId?: Maybe<Scalars['String']['output']>;
  adjustPrice?: Maybe<Scalars['Float']['output']>;
  chargeFee: Scalars['Float']['output'];
  excludeTaxFee?: Maybe<Scalars['Float']['output']>;
  feeId: Scalars['String']['output'];
  id: Scalars['Int']['output'];
  originFee: Scalars['Float']['output'];
  preferentialFee: Scalars['Float']['output'];
  remark?: Maybe<Scalars['String']['output']>;
  remarkList?: Maybe<Array<Scalars['String']['output']>>;
  resourceNo?: Maybe<Scalars['String']['output']>;
  taxFee?: Maybe<Scalars['Float']['output']>;
  useAmount: Scalars['Float']['output'];
};

export type WorkplaceFeeResponse = {
  __typename?: 'WorkplaceFeeResponse';
  data?: Maybe<Array<WorkplaceFee>>;
};

export enum YesOrNo {
  No = 'no',
  Yes = 'yes'
}

export type ChangeStepItemExceptionResponse = MutationResponse & {
  __typename?: 'changeStepItemExceptionResponse';
  code?: Maybe<Scalars['String']['output']>;
  message?: Maybe<Scalars['String']['output']>;
  success: Scalars['Boolean']['output'];
};

export type FeeGridAmountInfo = {
  __typename?: 'feeGridAmountInfo';
  amount: Scalars['Float']['output'];
  ratedPower: Scalars['Float']['output'];
};

export type GetModelQueryResponse = {
  __typename?: 'getModelQueryResponse';
  deviceType?: Maybe<Scalars['String']['output']>;
  deviceTypeName?: Maybe<Scalars['String']['output']>;
  secondCategory?: Maybe<Scalars['String']['output']>;
  secondCategoryName?: Maybe<Scalars['String']['output']>;
  topCategory?: Maybe<Scalars['String']['output']>;
  topCategoryName?: Maybe<Scalars['String']['output']>;
};
