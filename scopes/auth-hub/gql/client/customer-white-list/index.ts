export type { Customer<PERSON>erson, ApproveSpaceDetail } from './generated-types/graphql';

// Mutations
// ------
export { useCustomerWhiteListUpdate } from './mutations/customer-white-list-update';
export { useCustomerWhiteListCreation } from './mutations/customer-white-list-creator';
export { useDeleteCustomerWhiteList } from './mutations/delete-customer-white-list';
export { useOperateCustomerWhiteList } from './mutations/operate-customer-white-list';
// Queries
// ------

export {
  GET_CUSTOMER_WHITE_LIST,
  useCustomerWhiteList,
  useLazyCustomerWhiteList,
} from './queries/customer-white-list.query';

export {
  GET_CUSTOMER_WHITE_LIST_DETAIL,
  useCustomerWhiteListDetail,
  useLazyCustomerWhiteListDetail,
} from './queries/customer-white-list-detail.query';

export {
  GET_CUSTOMER_WHITE_STAFF,
  useCustomerWhiteStaff,
  useLazyCustomerWhiteStaff,
} from './queries/customer-white-staff.query';
