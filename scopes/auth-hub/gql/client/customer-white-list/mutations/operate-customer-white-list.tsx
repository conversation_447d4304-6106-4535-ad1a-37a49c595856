import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationOperateCustomerWhiteListArgs,
  OperateCustomerWhiteListMutationResponse,
} from '../generated-types/graphql';

export type OperateCustomerWhiteListData = {
  operateCustomerWhiteList: Maybe<OperateCustomerWhiteListMutationResponse>;
};

export const DELETE_CUSTOMER_WHITE_LIST: DocumentNode = gql`
  mutation OperateCustomerWhiteList($id: Long!, $type: String!) {
    operateCustomerWhiteList(id: $id, type: $type) {
      code
      message
      success
    }
  }
`;

export function useOperateCustomerWhiteList(
  options?: MutationHookOptions<OperateCustomerWhiteListData, MutationOperateCustomerWhiteListArgs>
): MutationTuple<OperateCustomerWhiteListData, MutationOperateCustomerWhiteListArgs> {
  return useMutation(DELETE_CUSTOMER_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
