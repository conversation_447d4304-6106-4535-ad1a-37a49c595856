import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  CreateCustomerWhiteListMutationResponse,
  Maybe,
  MutationCreateCustomerWhiteListArgs,
} from '../generated-types/graphql';

export type CustomerWhiteListCreatorData = {
  createCustomerWhiteList: Maybe<CreateCustomerWhiteListMutationResponse>;
};

export const CREATE_CUSTOMER_WHITE_LIST: DocumentNode = gql`
  mutation CreateCustomerWhiteList(
    $customerType: String!
    $company: String!
    $companyName: String!
    $note: String
    $blockGuidList: [String!]!
    $persons: [CustomerPerson!]!
  ) {
    createCustomerWhiteList(
      customerType: $customerType
      company: $company
      companyName: $companyName
      note: $note
      blockGuidList: $blockGuidList
      persons: $persons
    ) {
      code
      message
      success
    }
  }
`;

export function useCustomerWhiteListCreation(
  options?: MutationHookOptions<CustomerWhiteListCreatorData, MutationCreateCustomerWhiteListArgs>
): MutationTuple<CustomerWhiteListCreatorData, MutationCreateCustomerWhiteListArgs> {
  return useMutation(CREATE_CUSTOMER_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
