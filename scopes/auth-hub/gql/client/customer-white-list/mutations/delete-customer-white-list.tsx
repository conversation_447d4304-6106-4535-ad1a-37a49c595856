import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  DeleteCustomerWhiteListMutationResponse,
  Maybe,
  MutationDeleteCustomerWhiteListArgs,
} from '../generated-types/graphql';

export type DeleteCustomerWhiteListData = {
  deleteCustomerWhiteList: Maybe<DeleteCustomerWhiteListMutationResponse>;
};

export const DELETE_CUSTOMER_WHITE_LIST: DocumentNode = gql`
  mutation DeleteCustomerWhiteList($id: Long!) {
    deleteCustomerWhiteList(id: $id) {
      code
      message
      success
    }
  }
`;

export function useDeleteCustomerWhiteList(
  options?: MutationHookOptions<DeleteCustomerWhiteListData, MutationDeleteCustomerWhiteListArgs>
): MutationTuple<DeleteCustomerWhiteListData, MutationDeleteCustomerWhiteListArgs> {
  return useMutation(DELETE_CUSTOMER_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
