import { gql, useMutation } from '@apollo/client';
import type { DocumentNode, MutationHookOptions, MutationTuple } from '@apollo/client';

import type {
  Maybe,
  MutationUpdateCustomerWhiteListArgs,
  UpdateCustomerMutationResponse,
} from '../generated-types/graphql';

export type CustomerWhiteListUpdateData = {
  updateCustomerWhiteList: Maybe<UpdateCustomerMutationResponse>;
};

export const UPDATE_CUSTOMER_WHITE_LIST: DocumentNode = gql`
  mutation UpdateCustomerWhiteList(
    $id: Long!
    $phoneNo: String!
    $username: String!
    $company: String!
    $companyName: String!
    $email: String!
    $resourceList: [String!]!
  ) {
    updateCustomerWhiteList(
      id: $id
      phoneNo: $phoneNo
      username: $username
      company: $company
      companyName: $companyName
      email: $email
      resourceList: $resourceList
    ) {
      code
      message
      success
    }
  }
`;

export function useCustomerWhiteListUpdate(
  options?: MutationHookOptions<CustomerWhiteListUpdateData, MutationUpdateCustomerWhiteListArgs>
): MutationTuple<CustomerWhiteListUpdateData, MutationUpdateCustomerWhiteListArgs> {
  return useMutation(UPDATE_CUSTOMER_WHITE_LIST, {
    fetchPolicy: 'network-only',
    ...options,
  });
}
