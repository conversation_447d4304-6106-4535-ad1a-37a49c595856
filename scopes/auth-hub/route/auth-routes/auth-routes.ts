import type { AuditLogType } from '@manyun/auth-hub.model.audit-log';
import { utilities } from '@manyun/dc-brain.navigation.link';

const { generatePath } = utilities;

export const Error403_ROUTE_PATH = '/403';

export const USERS_ROUTE_PATH = '/page/auth/users';
export const USER_PROFILE_ROUTE_PATH = '/page/auth/user-profile/:id';
export const USERS_CREATOR_ROUTE_PATH = '/page/auth/users/creator';

export type UserProfileParams = {
  /** USER ID */
  id: string;
  tabKey?: 'default' | 'skill';
};

export const generateUserProfileRoutePath = ({ id, tabKey = 'default' }: UserProfileParams) =>
  `${USER_PROFILE_ROUTE_PATH.replace(':id', id)}?tabKey=${tabKey}`;

export const LOGIN_ROUTH_PATH = '/login';

// 查看某个用户的详细信息
export const USER_ROUTE_PATH = '/page/auth/user/detail';

export type UserDetailParams = {
  id: number;
  loginName?: string;
  defaultTab?: number | 'authorization-record';
  showDrawer?: boolean;
};

export const generateUserDetailRoutePath = ({
  id,
  loginName = '',
  defaultTab = 1,
  showDrawer = false,
}: UserDetailParams) =>
  `${USER_ROUTE_PATH}?id=${id}&loginName=${loginName}&defaultTab=${defaultTab}&showDrawer=${showDrawer}`;

// 用户组列表
export const USER_GROUPS_ROUTE_PATH = '/page/auth/user-group/list';

// 查看某个用户组的详细信息
export const USER_GROUP_ROUTE_PATH = '/page/auth/user-group/:id';

export const generateUserGroupRoutePath = ({ id }: { id: number }) =>
  generatePath(USER_GROUP_ROUTE_PATH, { id: id.toString() });

// 权限列表
export const USER_PERMISSIONS_ROUTE_PATH = '/page/auth/list';

// 角色列表
export const ROLES_ROUTE_PATH = '/page/auth/role/list';

// 查看某个角色的详细信息
export const ROLE_ROUTE_PATH = '/page/auth/role/detail';

export const generateRoleRoutePath = ({
  roleId,
  roleCode,
}: {
  roleId?: string;
  roleCode?: string;
}) => {
  if (roleId) {
    if (roleCode) {
      return `${ROLE_ROUTE_PATH}?role-id=${roleId}&role-code=${roleCode}`;
    } else {
      return `${ROLE_ROUTE_PATH}?role-id=${roleId}`;
    }
  }
  if (roleCode) {
    return `${ROLE_ROUTE_PATH}?role-code=${roleCode}`;
  }
  return ROLE_ROUTE_PATH;
};

// 审计日志
export const AUDIT_LOG_LIST_ROUTE_PATH = '/page/auth/log-center/audit-log-list';
export const AUDIT_LOG_LIST_ROUTE_AUTH_CODE = 'page_audit-log-list';

export const generateAuditLogListRoutePath = ({ logType }: { logType?: AuditLogType }) => {
  if (logType) {
    // 为符合地址规范，故将参数转为小写，且更换连接符
    const logTypeForUrl = logType.toLowerCase().replace(/_/g, '-');
    return `${AUDIT_LOG_LIST_ROUTE_PATH}?log-type=${logTypeForUrl}`;
  }
  return AUDIT_LOG_LIST_ROUTE_PATH;
};

// 客户白名单
// 列表页
export const CUSTOMER_WHITE_LIST_ROUTE_PATH = '/page/auth/customer-white-list';
export const CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE = 'page_customer-white-list';
// 新建页
export const CREATE_CUSTOMER_WHITE_LIST_ROUTE_PATH = '/page/auth/customer-white-list/create';
export const CREATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE = 'page_customer-white-list-create';
// 编辑页 - 已废弃使用
export const UPDATE_CUSTOMER_WHITE_LIST_ROUTE_PATH = '/page/auth/customer-white-list/edit/:id';
export const UPDATE_CUSTOMER_WHITE_LIST_ROUTE_AUTH_CODE = 'page_customer-white-list-edit';
export const generateUpdateCustomerWhiteListRoutePath = ({ id }: { id: string }) =>
  generatePath(UPDATE_CUSTOMER_WHITE_LIST_ROUTE_PATH, { id: id.toString() });
//详情页
export const CUSTOMER_WHITE_DETAIL_ROUTE_PATH = '/page/auth/customer-white-detail/:id';
export const CUSTOMER_WHITE_DETAIL_ROUTE_AUTH_CODE = 'page_customer-white-detail';
export const generateCustomerWhiteDetailRoutePath = ({ id }: { id: string }) =>
  generatePath(CUSTOMER_WHITE_DETAIL_ROUTE_PATH, { id: id.toString() });

//用户角色授权记录
export const USER_ROLE_AUTHORIZATION_ROUTE_PATH = '/page/auth/user-role-authorization';
export const USER_ROLE_AUTHORIZATION_ROUTE_AUTH_CODE = 'page_user-role-authorization';
