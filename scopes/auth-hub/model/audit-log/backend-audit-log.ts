/**
 * 审计日志-日志类型
 * - INSPECT_OFFLINE_DATA: 巡检
 * - APPROVE_OFFLINE_DATA：审批
 * - MAINTENANCE_OFFLINE_DATA： 维护
 * */
export type AuditLogType =
  | 'INSPECT_OFFLINE_DATA'
  | 'APPROVE_OFFLINE_DATA'
  | 'MAINTENANCE_OFFLINE_DATA'
  | 'EXCEL_POINT_REPORT';

export type BackendAuditLog = {
  /** 机房楼栋 */
  blockGuid: string;
  /** 文件名称 */
  fileName: string;
  /** 文件路径 */
  filePath: string;
  /** 文件大小，单位为 Byte */
  fileSize: number;
  id: number;
  /** 生成时间 */
  uploadTime: Date;
  /** 剩余有效时间，单位为秒 */
  effectDuration: number;
  /** 日志类型 */
  targetType: AuditLogType;
  fileFormat: string;
};
