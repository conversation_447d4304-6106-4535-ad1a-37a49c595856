import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { AuditLogType, BackendAuditLog } from './backend-audit-log';
import { getAuditLogLocales } from './locales';
import type { AuditLogLocales } from './locales';

export type AuditLogJSON = {
  /** 机房楼栋 */
  blockGuid: string;
  /** 文件名称 */
  fileName: string;
  /** 文件名称 */
  filePath: string;
  /** 文件大小，单位为 Byte */
  fileSize: number;
  id: number;
  /** 生成时间，精确到毫秒 */
  createdAt: number;
  /** 剩余有效时间，单位为秒 */
  remainingTime: number;
  /** 日志类型 */
  targetType: AuditLogType;
  /** 文件格式 */
  fileFormat: string;
};

export class AuditLog {
  static fromApiObject(object: BackendAuditLog) {
    const copy = cloneDeep(object);
    return new AuditLog(
      copy.blockGuid,
      copy.fileName,
      copy.filePath,
      copy.fileSize,
      copy.id,
      dayjs(copy.uploadTime).valueOf(),
      copy.effectDuration,
      copy.targetType,
      copy.fileFormat
    );
  }

  static fromJSON(json: AuditLogJSON) {
    const copy = cloneDeep(json);

    return new AuditLog(
      copy.blockGuid,
      copy.fileName,
      copy.filePath,
      copy.fileSize,
      copy.id,
      copy.createdAt,
      copy.remainingTime,
      copy.targetType,
      copy.fileFormat
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: AuditLogLocales;

  constructor(
    public blockGuid: string,
    public fileName: string,
    public filePath: string,
    public fileSize: number,
    public id: number,
    public createdAt: number,
    public remainingTime: number,
    public targetType: AuditLogType,
    public fileFormat: string
  ) {
    this._locales = getAuditLogLocales(this._localeCode);
  }

  public set locales(locales: AuditLogLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createdAt).format(template);
  }

  public toApiObject(): BackendAuditLog {
    return cloneDeep({
      blockGuid: this.blockGuid,
      fileName: this.fileName,
      filePath: this.filePath,
      fileSize: this.fileSize,
      id: this.id,
      uploadTime: dayjs(this.createdAt).toDate(),
      effectDuration: this.remainingTime,
      targetType: this.targetType,
      fileFormat: this.fileFormat,
    });
  }

  public toJSON(): AuditLogJSON {
    return cloneDeep({
      blockGuid: this.blockGuid,
      fileName: this.fileName,
      filePath: this.filePath,
      fileSize: this.fileSize,
      id: this.id,
      createdAt: this.createdAt,
      remainingTime: this.remainingTime,
      targetType: this.targetType,
      fileFormat: this.fileFormat,
    });
  }
}
