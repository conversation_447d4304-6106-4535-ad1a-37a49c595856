import type { WebsiteCode } from '@manyun/dc-brain.model.website';
import type { BackendAuthResource, BackendResourceType } from '@manyun/iam.model.auth-resource';

export type BackendAuthorizationRecord = {
  /** 授权记录id	 */
  id: number;
  /** 用户id */
  userId: string;
  /** 用户姓名 */
  userName: string;
  /**角色code	 */
  roleCode: string;
  /** 角色名称*/
  roleName: string;
  /** 过期时间 */
  expireTime: string;
  /** 资源类型 */
  resourceType: BackendResourceType;
  /** 是否删除 */
  isDeleted: boolean;
  /** 最新操作时间 */
  gmtModified: string;
  /** 操作人 */
  modifierName: string;
  /** 操作人id */
  modifierId: number;
  /** 站点 */
  site: WebsiteCode;
  resourceList: BackendAuthResource[];
};
