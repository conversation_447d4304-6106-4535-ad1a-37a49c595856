import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type { WebsiteCode } from '@manyun/dc-brain.model.website';
import type { BackendAuthResource, BackendResourceType } from '@manyun/iam.model.auth-resource';

import type { BackendAuthorizationRecord } from './backend-authorization-record';
import { getAuthorizationRecordLocales } from './locales';
import type { AuthorizationRecordLocales } from './locales';

export type SimpleUser = {
  id: number;
  name: string;
};

export type AuthorizationRecordJSON = {
  /** 授权记录id	 */
  id: number;
  /** 用户id */
  userId: string;
  /**用户姓名 */
  userName: string;
  /**角色code	 */
  roleCode: string;
  /** 角色名称*/
  roleName: string;
  /** 过期时间 */
  expiredAt: number;
  /** 资源类型 */
  resourceType: BackendResourceType;
  /** 是否删除 */
  isDeleted: boolean;
  /** 最新操作时间 */
  modifiedAt: number;
  modifiedBy: SimpleUser;
  /** 站点 */
  website: WebsiteCode;
  resourceList: BackendAuthResource[];
};

export class AuthorizationRecord {
  static fromApiObject(object: BackendAuthorizationRecord) {
    const copy = cloneDeep(object);

    return new AuthorizationRecord(
      copy.id,
      copy.userId,
      copy.userName,
      copy.roleCode,
      copy.roleName,
      dayjs(copy.expireTime).valueOf(),
      copy.resourceType,
      copy.isDeleted,
      dayjs(copy.gmtModified).valueOf(),
      {
        id: copy.modifierId,
        name: copy.modifierName,
      },
      copy.site,
      copy.resourceList
    );
  }

  static fromJSON(json: AuthorizationRecordJSON) {
    const copy = cloneDeep(json);

    return new AuthorizationRecord(
      copy.id,
      copy.userId,
      copy.userName,
      copy.roleCode,
      copy.roleName,
      copy.expiredAt,
      copy.resourceType,
      copy.isDeleted,
      copy.modifiedAt,
      copy.modifiedBy,
      copy.website,
      copy.resourceList
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: AuthorizationRecordLocales;

  constructor(
    public id: number,
    public userId: string,
    public userName: string,
    public roleCode: string,
    public roleName: string,
    public expiredAt: number,
    public resourceType: BackendResourceType,
    public isDeleted: boolean,
    public modifiedAt: number,
    public modifiedBy: SimpleUser,
    public website: WebsiteCode,
    public resourceList: BackendAuthResource[]
  ) {
    this._locales = getAuthorizationRecordLocales(this._localeCode);
  }

  public set locales(locales: AuthorizationRecordLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedExpiredAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.expiredAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendAuthorizationRecord {
    return cloneDeep({
      id: this.id,
      userId: this.userId,
      userName: this.userName,
      roleCode: this.roleCode,
      roleName: this.roleName,
      expireTime: dayjs(this.expiredAt).format(),
      resourceType: this.resourceType,
      isDeleted: this.isDeleted,
      gmtModified: dayjs(this.modifiedAt).format(),
      modifierId: this.modifiedBy.id,
      modifierName: this.modifiedBy.name,
      site: this.website,
      resourceList: this.resourceList,
    });
  }

  public toJSON(): AuthorizationRecordJSON {
    return cloneDeep({
      id: this.id,
      userId: this.userId,
      userName: this.userName,
      roleCode: this.roleCode,
      roleName: this.roleName,
      expiredAt: this.expiredAt,
      resourceType: this.resourceType,
      isDeleted: this.isDeleted,
      modifiedAt: this.modifiedAt,
      modifiedBy: {
        id: this.modifiedBy.id,
        name: this.modifiedBy.name,
      },
      website: this.website,
      resourceList: this.resourceList,
    });
  }
}
