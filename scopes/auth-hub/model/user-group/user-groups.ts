/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
export type BackendUserGroup = {
  id: number; //用户组id
  groupName: string; //用户组名
  groupCode: string; //用户组code
  resourceCodes?: string[]; //资源code列表
  roleCodes?: string[] | null; //角色code列表
  remark?: string; //备注
};

export class UserGroup {
  constructor(
    public code: string,
    public name: string,
    public id?: number,
    public descriptions?: string,
    public resourceCodes?: string[],
    public roleCodes?: string[] | null
  ) {}

  static fromApiObject(backendUserGroup: BackendUserGroup) {
    return new UserGroup(
      backendUserGroup.groupCode,
      backendUserGroup.groupName,
      backendUserGroup.id,
      backendUserGroup.remark,
      backendUserGroup.resourceCodes,
      backendUserGroup.roleCodes
    );
  }

  static toApiObject(userGroup: UserGroup): BackendUserGroup {
    return {
      id: userGroup.id as number,
      groupName: userGroup.name,
      groupCode: userGroup.code,
      resourceCodes: userGroup.resourceCodes,
      roleCodes: userGroup.roleCodes,
      remark: userGroup.descriptions,
    };
  }
}
