import type { BackendUserGroup } from './index';

export const mockUserGroupList: BackendUserGroup[] = [
  {
    id: 0,
    groupName: '',
    groupCode: '',
    resourceCodes: [],
    roleCodes: [],
  },
  {
    id: 2,
    groupName: '',
    groupCode: '',
    resourceCodes: [],
    roleCodes: [],
  },
];

export function getRandomMockUserGroup() {
  const userGroupIdx = Math.floor(Math.random() * mockUserGroupList.length);
  return mockUserGroupList[userGroupIdx];
}
