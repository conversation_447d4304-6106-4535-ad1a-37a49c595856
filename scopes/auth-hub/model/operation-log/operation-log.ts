import type { LocaleCode } from '@teammc/react-intl';
import dayjs from 'dayjs';
import cloneDeep from 'lodash.clonedeep';

import type {
  BackendOperationLog,
  DisplayType,
  ModifyType,
  TargetType,
} from './backend-operation-log';
import { getOperationLogLocales } from './locales';
import type { OperationLogLocales } from './locales';

type TargetInfo = {
  /** 操作对象 ID */
  id: string;
  /** 操作模块 */
  type: TargetType;
  content?: string;
};

type OperationInfo = {
  /** 操作个数 */
  count?: number;
  /** 操作备注 */
  notes?: string;
  /** 操作内容 */
  content: string[];
  /** 操作时间 */
  time?: string;
};

type ModifyByInfo = {
  /** 操作人 ID */
  id: number;
  /** 操作人名称 */
  name: string;
};

export type OperationLogJSON = {
  /** 日志 ID */
  id: number;
  createdAt: number;
  modifiedAt: number;
  displayType: DisplayType;
  target: TargetInfo;
  operation: OperationInfo;
  modifyBy: ModifyByInfo;
  /** 操作类型 */
  modifyType: ModifyType;
  /** 操作内容 */
  modifyContent?: string;
};

export class OperationLog {
  static fromApiObject(object: BackendOperationLog) {
    const copy = cloneDeep(object);

    return new OperationLog(
      copy.id,
      dayjs(copy.gmtCreate).valueOf(),
      dayjs(copy.gmtModified).valueOf(),
      copy.displayType,
      {
        id: copy.targetId,
        type: copy.targetType,
        content: copy.targetContent ?? undefined,
      },
      {
        count: copy.operatorCount ?? undefined,
        notes: copy.operatorNotes ?? undefined,
        content: copy.operationContent,
        time: copy.operatorTime ?? undefined,
      },
      {
        id: copy.modifyBy,
        name: copy.modifyByName,
      },
      copy.modifyType,
      copy.modifyContent ?? undefined
    );
  }

  static fromJSON(json: OperationLogJSON) {
    const copy = cloneDeep(json);

    return new OperationLog(
      copy.id,
      copy.createdAt,
      copy.modifiedAt,
      copy.displayType,
      copy.target,
      copy.operation,
      copy.modifyBy,
      copy.modifyType,
      copy.modifyContent
    );
  }

  private _localeCode: LocaleCode = 'zh-CN';
  private _locales: OperationLogLocales;

  constructor(
    public id: number,
    public createdAt: number,
    public modifiedAt: number,
    public displayType: DisplayType,
    public target: TargetInfo,
    public operation: OperationInfo,
    public modifyBy: ModifyByInfo,
    public modifyType: ModifyType,
    public modifyContent?: string
  ) {
    this._locales = getOperationLogLocales(this._localeCode);
  }

  public set locales(locales: OperationLogLocales) {
    this._locales = locales;
  }

  public get locales() {
    return this._locales;
  }

  public getFormattedCreatedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.createdAt).format(template);
  }

  public getFormattedModifiedAt(template = 'YYYY-MM-DD HH:mm:ss') {
    return dayjs(this.modifiedAt).format(template);
  }

  public toApiObject(): BackendOperationLog {
    return cloneDeep({
      id: this.id,
      gmtCreate: this.getFormattedCreatedAt(),
      gmtModified: this.getFormattedModifiedAt(),
      displayType: this.displayType,
      targetId: this.target.id,
      targetType: this.target.type,
      targetContent: this.target.content ?? null,
      operatorCount: this.operation.count ?? null,
      operatorNotes: this.operation.notes ?? null,
      operationContent: this.operation.content,
      operatorTime: this.operation.time ?? null,
      modifyBy: this.modifyBy.id,
      modifyByName: this.modifyBy.name,
      modifyType: this.modifyType,
      modifyContent: this.modifyContent ?? null,
    });
  }

  public toJSON(): OperationLogJSON {
    return cloneDeep({
      id: this.id,
      createdAt: this.createdAt,
      modifiedAt: this.modifiedAt,
      displayType: this.displayType,
      target: this.target,
      operation: this.operation,
      modifyBy: this.modifyBy,
      modifyType: this.modifyType,
      modifyContent: this.modifyContent,
    });
  }
}
