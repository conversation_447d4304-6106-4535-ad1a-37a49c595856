export type OperationLogLocales = {
  id: string;
  serialNumber: string;
  createdAt: string;
  modifiedAt: string;
  displayType: string;
  target: {
    __self: string;
    id: string;
    type: string;
    content: string;
  };
  operation: {
    __self: string;
    count: string;
    notes: string;
    content: string;
    time: string;
  };
  modifyBy: {
    __self: string;
    id: string;
    name: string;
  };
  modifyType: string;
  modifyContent: string;
};
