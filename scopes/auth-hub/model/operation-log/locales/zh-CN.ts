import type { OperationLogLocales } from './type';

export const zhCN: OperationLogLocales = {
  id: '日志 ID',
  serialNumber: '序号',
  createdAt: '创建时间',
  modifiedAt: '修改时间',
  displayType: '展示类型',
  target: {
    __self: '业务',
    id: '操作对象 ID',
    type: '操作模块',
    content: '操作内容',
  },
  operation: {
    __self: '操作',
    count: '操作数量',
    notes: '操作备注',
    content: '操作内容',
    time: '操作时间',
  },
  modifyBy: {
    __self: '操作人',
    id: '操作人 ID',
    name: '操作人',
  },
  modifyType: '操作类型',
  modifyContent: '操作内容',
};

export default zhCN;
