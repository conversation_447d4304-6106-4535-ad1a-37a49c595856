export enum TargetTypeCode {
  /** 设备管理 */
  Device = 'DEVICE',
  /** 事件配置 */
  Event = 'EVENT',
  /** 机柜 */
  Grid = 'GRID',
  /** 导入 */
  Import = 'IMPORT',
  /** 巡检工单 */
  Inspection = 'INSPECTION',
  /** 盘点工单 */
  Inventory = 'INVENTORY',
  /** 维保工单 */
  Maintenance = 'MAINTENANCE',
  /** 人员  */
  Person = 'PERSON',
  /** 上下电工单 */
  Power = 'POWER',
  /** 维修工单 */
  Repair = 'REPAIR',
  /** 出入库工单 */
  Warehouse = 'WAREHOUSE',
  /** 附件 */
  File = 'FILE',
  /** 巡检配置 */
  InspectionConfig = 'INSPECTION_CONFIG',
  /** 资产出入门工单 */
  Access = 'ACCESS',
  /** 上下线工单 */
  OnOff = 'ON_OFF',
  /** 维保配置 */
  MaintenanceConfig = 'MAINTENANCE_CONFIG',
  /** 厂商 */
  Vendor = 'VENDOR',
  /** 调班 */
  Alter = 'ALTER',
  /** 补卡 */
  Supply = 'SUPPLY',
  /** 到货验收 */
  Accept = 'ACCEPT',
  /** 借用归还 */
  Borrow = 'BORROW',
  /** 门禁授权 */
  AccessCardAuth = 'ACCESS_CARD_AUTH',
  /** 审批 */
  Approval = 'APPROVAL',
  /** 风险登记册 */
  RiskRegister = 'NEW_RISK_REGISTER',
  /** 事件（内测版） */
  NewEvent = 'NEW_EVENT',
}

export type TargetType = {
  code: string;
  name: string;
};

export type ModifyType = {
  code: string;
  name: string;
};

export enum DisplayType {
  /** 默认类型 */
  Default = 'DEFAULT',
  /** 展示对象 */
  Target = 'TARGET',
}

export type BackendOperationLog = {
  /** 日志 ID */
  id: number;
  gmtCreate: string;
  gmtModified: string;
  displayType: DisplayType;
  /** 操作对象 ID */
  targetId: string;
  /** 操作模块 */
  targetType: TargetType;
  targetContent: string | null;
  /** 操作个数 */
  operatorCount: number | null;
  /** 操作备注 */
  operatorNotes: string | null;
  /** 操作内容 */
  operationContent: string[];
  /** 操作时间 */
  operatorTime: string | null;
  /** 操作人 ID */
  modifyBy: number;
  /** 操作人名称 */
  modifyByName: string;
  /** 操作类型 */
  modifyType: ModifyType;
  /** 操作内容 */
  modifyContent: string | null;
};
