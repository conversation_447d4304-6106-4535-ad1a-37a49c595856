import type { BackendUser } from './index';

export const mockUserList: BackendUser[] = [
  {
    id: 0,
    userName: 'System',
    loginName: 'system',
    mobile: '000000000',
    email: '<EMAIL>',
    enable: true,
  },
  {
    id: 777,
    userName: 'Jerry',
    loginName: 'jerry_w',
    mobile: '18711001100',
    email: '<EMAIL>',
    enable: false,
  },
];

export function getRandomMockUser() {
  const userIdx = Math.floor(Math.random() * mockUserList.length);

  return mockUserList[userIdx];
}

export function getRandomMockBackendUser() {
  const userIdx = Math.floor(Math.random() * mockUserList.length);

  return mockUserList[userIdx];
}
