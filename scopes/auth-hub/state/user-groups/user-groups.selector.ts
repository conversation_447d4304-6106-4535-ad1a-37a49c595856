import { userGroupsSlice } from './user-groups.slice';
import type { UserGroupsSliceState } from './user-groups.slice';

type StoreState = {
  [userGroupsSlice.name]: UserGroupsSliceState;
};

export const selectUserGroupsByCodes = (codes?: string[]) => (storeState: StoreState) => {
  const { entities } = storeState[userGroupsSlice.name];

  if (codes === undefined) {
    return Object.keys(entities).map(code => entities[code]);
  }

  return codes.map(code => entities[code]);
};

export const selectUserGroupsState =
  () =>
  (storeState: StoreState): UserGroupsSliceState =>
    storeState[userGroupsSlice.name];
