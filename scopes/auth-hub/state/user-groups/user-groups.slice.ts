/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import { UserGroup } from '@manyun/auth-hub.model.user-group';

export type UserGroupsSliceState = {
  /**
   * a `code: entity` object.
   */
  entities: Record<string, UserGroup>;

  /**
   * a `code` array that exist in `entities`.
   */
  codes: string[];

  /**
   * 映射表id -> code
   */
  keyMapper: Record<number, string>;

  /**
   * an indicator can be used to indicate whether is fetching data from a remote API.
   */
  loading: boolean;
};

type UserGroupsSliceCaseReducers = {
  fetchUserGroupsStart: CaseReducer<UserGroupsSliceState, PayloadAction<undefined>>;
  setUserGroups: CaseReducer<UserGroupsSliceState, PayloadAction<{ userGroups: UserGroup[] }>>;
  fetchUserGroupsError: CaseReducer<UserGroupsSliceState, PayloadAction<undefined>>;
};

export const userGroupsSlice = createSlice<
  UserGroupsSliceState,
  UserGroupsSliceCaseReducers,
  'auth-hub.user-groups'
>({
  name: 'auth-hub.user-groups',
  initialState: {
    entities: {},
    codes: [],
    loading: false,
    keyMapper: {},
  },
  reducers: {
    fetchUserGroupsStart(sliceState) {
      sliceState.loading = true;
    },
    setUserGroups(sliceState, { payload: { userGroups } }) {
      const codes: string[] = [];
      userGroups.forEach(userGroup => {
        codes.push(userGroup.code);
        sliceState.entities[userGroup.code] = userGroup;
        sliceState.keyMapper[userGroup.id!] = userGroup.code;
      });
      sliceState.codes = codes;
      sliceState.loading = false;
    },
    fetchUserGroupsError(sliceState) {
      sliceState.loading = false;
    },
  },
});
