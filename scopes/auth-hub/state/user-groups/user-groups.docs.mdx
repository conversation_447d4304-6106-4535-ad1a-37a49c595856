---
description: 'UserGroups redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import userGroupsSliceReducer from '@manyun/[scope].state.user-groups';

const rootReducer = {
  ...userGroupsSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { userGroupsWatchers } from '@manyun/[scope].state.user-groups';

const function* rootSaga() {
  yield all(
    ...userGroupsWatchers,
    // other sagas...
  );
};
```