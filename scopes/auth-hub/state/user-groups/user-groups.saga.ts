import { call, fork, put, select, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { fetchUserGroupsWeb } from '@manyun/auth-hub.service.pm.fetch-user-groups';

import { getUserGroupsAction, userGroupsSliceActions } from './user-groups.action';

/** Workers */

export function* getUserGroupsSaga({
  payload: { userId },
}: ReturnType<typeof getUserGroupsAction>) {
  yield put(userGroupsSliceActions.fetchUserGroupsStart());
  const { error, data }: SagaReturnType<typeof fetchUserGroupsWeb> = yield call(
    fetchUserGroupsWeb,
    { userId }
  );

  if (error) {
    message.error(error.message);
    yield put(userGroupsSliceActions.fetchUserGroupsError());
    return;
  }

  yield put(userGroupsSliceActions.setUserGroups({ userGroups: data.data! }));
}

/** Watchers */

function* watchGetUserGroups() {
  yield takeLatest(getUserGroupsAction.type, getUserGroupsSaga);
}

export default [fork(watchGetUserGroups)];
