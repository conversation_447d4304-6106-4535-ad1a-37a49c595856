import { expectSaga } from 'redux-saga-test-plan';

import { fetchUserGroupsWeb } from '@manyun/auth-hub.service.pm.fetch-user-groups';
import { destroyMock, useRemoteMock, webRequest } from '@manyun/service.request';

import { getUserGroupsAction, userGroupsSliceActions } from './user-groups.action';
import { getUserGroupsSaga } from './user-groups.saga';
import { userGroupsSlice } from './user-groups.slice';
import type { UserGroupsSliceState } from './user-groups.slice';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});

afterAll(() => {
  mockOff();
});

test('should return the initial state', () => {
  expect(userGroupsSlice.reducer(undefined, {} as any)).toEqual<UserGroupsSliceState>({
    entities: {},
    codes: [],
    keyMapper: {},
    loading: false,
  });
});

test('sholue put a `setUserGroups` action', () => {
  const initialState = userGroupsSlice.reducer(undefined, {} as any);
  const changeState = userGroupsSlice.reducer(
    initialState,
    userGroupsSlice.actions.setUserGroups({
      userGroups: [
        {
          id: 1,
          name: '用户组',
          code: 'code01',
          resourceCodes: [],
          roleCodes: [],
        },
        {
          id: 2,
          name: '用户组2',
          code: 'code02',
          resourceCodes: [],
          roleCodes: [],
        },
      ],
    })
  );
  expect(changeState.codes).toHaveLength(2);
});

// test('should put a `setUserGroups` action', () => {
//   (fetchUserGroupsWeb as jest.MockedFunction<typeof fetchUserGroupsWeb>).mockResolvedValue({
//     status: 200,
//     statusText: 'ok',
//     headers: null,
//     config: {},
//     data: {
//       data: [],
//       total: 0,
//     },
//   });
//   const initialState = userGroupsSlice.reducer(undefined, {} as any);

//   return expectSaga(getUserGroupsSaga, {
//     type: getUserGroupsAction.type,
//     payload: {},
//   })
//     .withState({ [userGroupsSlice.name]: initialState })
//     .put({
//       type: userGroupsSliceActions.fetchUserGroupsStart.type,
//     })
//     .put({
//       type: userGroupsSliceActions.setUserGroups.type,
//       payload: {
//         data: [],
//         total: 0,
//       },
//     })
//     .run();
// });
