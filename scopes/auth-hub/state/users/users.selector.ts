import { usersSlice } from './users.slice';
import type { UsersSliceState } from './users.slice';

export const selectUsersEntities =
  (ids?: number[]) => (storeState: { [usersSlice.name]: UsersSliceState }) => {
    const { entities } = storeState[usersSlice.name];

    if (ids === undefined) {
      return Object.keys(entities).map(id => entities[Number(id)]);
    }

    return ids.map(id => entities[id]);
  };

export const selectUsersCreate = () => (storeState: { [usersSlice.name]: UsersSliceState }) => {
  return storeState[usersSlice.name].create;
};
