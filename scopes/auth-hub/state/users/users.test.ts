import { expectSaga } from 'redux-saga-test-plan';

import { getUsersAction, usersSliceActions } from './users.action';
import { getUsersSaga } from './users.saga';
import { usersSlice } from './users.slice';
import type { UsersSliceState } from './users.slice';

test('should return the initial state', () => {
  expect(usersSlice.reducer(undefined, {} as any)).toEqual<UsersSliceState>({
    entities: {},
    create: { passward: undefined, drafts: {}, draftKeys: [] },
  });
});

// test('should put a `setUsers` action', () => {
//   (someService as jest.MockedFunction<typeof someService>).mockResolvedValue({
//     status: 200,
//     statusText: 'ok',
//     headers: null,
//     config: {},
//     data: {
//       data: [],
//       total: 0,
//     },
//   });
//   const initialState = usersSlice.reducer(undefined, {} as any);

//   return expectSaga(getUsersSaga, {
//     type: getUsersAction.type,
//   })
//     .withState({ [usersSlice.name]: initialState })
//     .put({
//       type: usersSliceActions.fetchUsersStart.type,
//     })
//     .put({
//       type: usersSliceActions.setUsers.type,
//       payload: {
//         data: [],
//         total: 0,
//       },
//     })
//     .run();
// });
