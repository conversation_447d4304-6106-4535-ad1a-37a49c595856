import { call, fork, put, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { fetchPagedUsers } from '@manyun/auth-hub.service.fetch-paged-users';

import { getUsersAction, usersSliceActions } from './users.action';

/** Workers */

export function* getUsersSaga({
  payload: { fields, callback },
}: ReturnType<typeof getUsersAction>) {
  yield put(usersSliceActions.fetchUsersStart({ fields }));
  const { error, data }: SagaReturnType<typeof fetchPagedUsers> = yield call(
    fetchPagedUsers,
    fields
  );

  if (error) {
    message.error(error.message);
    callback(null);
    return;
  }
  yield put(usersSliceActions.setUsers({ data: data.data, total: data.total }));
  callback({ data: data.data, total: data.total });
}

/** Watchers */

function* watchGetUsers() {
  yield takeLatest(getUsersAction.type, getUsersSaga);
}

export default [fork(watchGetUsers)];
