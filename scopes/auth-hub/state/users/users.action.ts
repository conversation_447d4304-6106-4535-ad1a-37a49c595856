import { createAction } from '@reduxjs/toolkit';

import type { CustomizedUser, SvcQuery } from '@manyun/auth-hub.service.fetch-paged-users';

import { usersSlice } from './users.slice';

const prefix = usersSlice.name;

export const usersSliceActions = usersSlice.actions;

export const getUsersAction = createAction<{
  fields: SvcQuery;
  callback: (result: { data: CustomizedUser[]; total: number } | null) => void;
}>(prefix + '/GET_USERS');
