---
description: 'Users redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import usersSliceReducer from '@manyun/[scope].state.users';

const rootReducer = {
  ...usersSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { usersWatchers } from '@manyun/[scope].state.users';

const function* rootSaga() {
  yield all(
    ...usersWatchers,
    // other sagas...
  );
};
```