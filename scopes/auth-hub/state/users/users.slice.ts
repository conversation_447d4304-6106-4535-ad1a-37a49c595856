import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import type { CustomizedUser, SvcQuery } from '@manyun/auth-hub.service.fetch-paged-users';

export type UsersSliceState = {
  entities: Record<number, CustomizedUser>;
  create: {
    passward: string | undefined;
    drafts: Record<string, CustomizedUser>;
    draftKeys: string[];
  };
};

type UsersSliceCaseReducers = {
  fetchUsersStart: CaseReducer<UsersSliceState, PayloadAction<{ fields: SvcQuery }>>;
  setUsers: CaseReducer<
    UsersSliceState,
    PayloadAction<{
      data: CustomizedUser[];
      total: number;
    }>
  >;
};

export const usersSlice = createSlice<UsersSliceState, UsersSliceCaseReducers, 'auth-hub.users'>({
  name: 'auth-hub.users',
  initialState: {
    entities: {},
    create: {
      passward: undefined,
      drafts: {},
      draftKeys: [],
    },
  },
  reducers: {
    fetchUsersStart(sliceState, { payload: { fields } }) {},
    setUsers(sliceState, { payload: { data } }) {
      data.forEach(user => {
        sliceState.entities[user.id] = user;
      });
    },
  },
});
