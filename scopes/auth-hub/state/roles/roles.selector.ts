import { Role } from '@manyun/auth-hub.model.role';

import { rolesSlice } from './roles.slice';
import type { RolesSliceState } from './roles.slice';

type StoreState = {
  [rolesSlice.name]: RolesSliceState;
};

export const selectRolesState = (storeState: StoreState): RolesSliceState =>
  storeState[rolesSlice.name];

export const selectRolesByCodes =
  (codes?: string[]) =>
  (storeState: StoreState): Role[] => {
    const { entities } = storeState[rolesSlice.name];
    if (codes === undefined) {
      return Object.keys(entities)
        .map(code => entities[code])
        .filter(entity => entity !== undefined);
    }
    return codes.map(code => entities[code]).filter(entity => entity !== undefined);
  };
