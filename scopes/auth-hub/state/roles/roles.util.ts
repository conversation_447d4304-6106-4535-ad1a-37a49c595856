export type StaticReadPolicy = 'network-only' | 'cache-only';
export type CallbackReadPolicy<T extends keyof any> = (codes: T[]) => StaticReadPolicy;

export function getStaticReadPolicy<T extends keyof any>(
  policy: StaticReadPolicy | CallbackReadPolicy<T> = 'network-only',
  codes: T[]
): StaticReadPolicy {
  if (codes.length <= 0) {
    return 'network-only';
  }

  if (typeof policy == 'function') {
    return policy(codes);
  }
  return policy;
}
