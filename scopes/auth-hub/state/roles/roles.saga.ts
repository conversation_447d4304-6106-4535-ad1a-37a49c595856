import { Task } from 'redux-saga';
import { call, cancel, fork, put, select, take } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { message } from '@manyun/base-ui.ui.message';

import { fetchRolesWeb } from '@manyun/auth-hub.service.pm.fetch-roles';

import { getRolesAction, rolesSliceActions } from './roles.action';
import { selectRolesState } from './roles.selector';
import { getStaticReadPolicy } from './roles.util';

/** Workers */

export function* getRolesSaga({
  payload: { name, page, pageSize, callback },
}: ReturnType<typeof getRolesAction>) {
  const { error, data }: SagaReturnType<typeof fetchRolesWeb> = yield call<typeof fetchRolesWeb>(
    fetchRolesWeb,
    {
      name,
      page,
      pageSize,
    }
  );

  if (callback) {
    callback(error, data);
  }

  if (error) {
    message.error(error.message);
    yield put(rolesSliceActions.fetchRolesError());
    return;
  }

  yield put(rolesSliceActions.setRoles({ roles: data.data, total: data.total }));
}

/** Watchers */

function* watchGetRoles() {
  let lastTask: Task | undefined = undefined;
  while (true) {
    const action: SagaReturnType<typeof getRolesAction> = yield take(getRolesAction.type);
    const existing: SagaReturnType<typeof selectRolesState> = yield select(selectRolesState);
    const staitcPolicy = getStaticReadPolicy(action.payload.policy, existing.codes);
    if (!existing.loading && staitcPolicy == 'network-only') {
      if (lastTask) {
        yield cancel(lastTask);
      }
      yield put(rolesSliceActions.fetchRolesStart());
      lastTask = yield fork(getRolesSaga, action);
    }
  }
}

export default [fork(watchGetRoles)];
