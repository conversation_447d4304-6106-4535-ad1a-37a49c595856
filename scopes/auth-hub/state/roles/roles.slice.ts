/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';

import { Role } from '@manyun/auth-hub.model.role';

export type RolesSliceState = {
  /**
   * an `code: entity` object.
   */
  entities: Record<string, Role>;

  /**
   * an `code` array that exist in `entities`.
   */
  codes: string[];

  total: number;
  /**
   * 映射表 id -> code
   */
  keyMapper: Record<number, string>;

  /**
   * an indicator can be used to indicate whether is fetching data from a remote API.
   */
  loading: boolean;
};

type RolesSliceCaseReducers = {
  fetchRolesStart: CaseReducer<RolesSliceState, PayloadAction<undefined>>;
  setRoles: CaseReducer<
    RolesSliceState,
    PayloadAction<{
      roles: Role[];
      total: number;
    }>
  >;
  fetchRolesError: CaseReducer<RolesSliceState, PayloadAction<undefined>>;
};

export const rolesSlice = createSlice<RolesSliceState, RolesSliceCaseReducers, 'auth-hub.roles'>({
  name: 'auth-hub.roles',
  initialState: {
    entities: {},
    codes: [],
    keyMapper: {},
    loading: false,
    total: 0,
  },
  reducers: {
    fetchRolesStart(sliceState) {
      sliceState.loading = true;
    },

    setRoles(sliceState, { payload: { roles, total } }) {
      const codes: string[] = [];
      roles.forEach(role => {
        codes.push(role.code!);
        sliceState.keyMapper[role.id!] = role.code!;
        sliceState.entities[role.code!] = role;
      });
      sliceState.codes = codes;
      sliceState.total = total;
      sliceState.loading = false;
    },

    fetchRolesError(sliceState) {
      sliceState.loading = false;
    },
  },
});
