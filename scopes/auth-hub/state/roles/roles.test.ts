import { expectSaga } from 'redux-saga-test-plan';

import { getRolesAction, rolesSliceActions } from './roles.action';
import { getRolesSaga } from './roles.saga';
import { rolesSlice } from './roles.slice';
import type { RolesSliceState } from './roles.slice';

test('should return the initial state', () => {
  expect(rolesSlice.reducer(undefined, {} as any)).toEqual<RolesSliceState>({
    entities: {},
    codes: [],
    keyMapper: {},
    loading: false,
    total: 0,
  });
});

test('should put a `setRoles` action', () => {
  const initialState = rolesSlice.reducer(undefined, {} as any);
  const changeState = rolesSlice.reducer(
    initialState,
    rolesSlice.actions.setRoles({
      roles: [
        {
          id: 1,
          code: '1',
          name: '角色1',
        },
      ],
      total: 1,
    })
  );
  expect(changeState.codes).toHaveLength(1);
});
