import { createAction } from '@reduxjs/toolkit';

import { ReadRolesPolicy } from '@manyun/auth-hub.hook.use-roles';
import { ServiceRD } from '@manyun/auth-hub.service.pm.fetch-roles';
import { RequestError } from '@manyun/service.request';

import { rolesSlice } from './roles.slice';

const prefix = rolesSlice.name;

export const rolesSliceActions = rolesSlice.actions;

export const getRolesAction = createAction<{
  page?: number;
  pageSize?: number;
  name?: string;
  policy?: ReadRolesPolicy;
  callback: (error?: RequestError, data?: ServiceRD) => void | undefined;
}>(prefix + '/GET_ROLES');
