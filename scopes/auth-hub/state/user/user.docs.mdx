---
description: 'User redux(slice state, selector, action, saga) 组件.'
labels: ['redux', 'slice-state', 'label3']
---

## 使用

1. 集成 `reducer(slice state)`

```js
import userSliceReducer from '@manyun/[scope].state.user';

const rootReducer = {
  ...userSliceReducer,
  // other slice reducers...
};
```

2. 集成 `saga`

```js
import { all } from 'redux-saga/effects';
import { userWatchers } from '@manyun/[scope].state.user';

const function* rootSaga() {
  yield all(
    ...userWatchers,
    // other sagas...
  );
};
```