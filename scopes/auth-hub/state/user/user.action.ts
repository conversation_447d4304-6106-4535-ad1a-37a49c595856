import { createAction } from '@reduxjs/toolkit';

import type { SvcQuery as UpdateMyPasswordQuery } from '@manyun/auth-hub.service.mutate-my-password';

import { userSlice } from './user.slice';

const prefix = userSlice.name;

export const userSliceActions = userSlice.actions;

export const getMyPermissionsAction = createAction(prefix + '/GET_MY_PERMISSIONS');

export const getMyResourcesAction = createAction(prefix + '/GET_MY_RESOURCES');

export const updateMyPasswordAction = createAction<
  UpdateMyPasswordQuery & {
    callback?: (result: boolean) => void;
  }
>(prefix + '/UPDATE_MY_PASSWORD');
