import { createSlice } from '@reduxjs/toolkit';
import type { CaseReducer, PayloadAction } from '@reduxjs/toolkit';
import shallowequal from 'shallowequal';

import type { PermissionJSON } from '@manyun/iam.model.permission';

type AuthorizedMenus = {
  entities: Record<string, PermissionJSON>;
  codes: string[];
  idsMapper: Record<number, string>;
};

export type UserSliceState = {
  /**
   * an indicator can be used to indicate whether is fetching data from a remote API.
   */
  loading: boolean;
  /**
   * user`s unique key
   */
  userId: number | null;
  /**
   * user`s login name
   */
  username: string | null;
  /**
   * user`s full name
   */
  name: string | null;

  /**
   *  user`s company
   */
  company: string | null;
  /**
   *  user`s idc
   */
  idc: string | null;
  /**
   * login error message
   */
  errMsg: string | null;
  /**
   * user`s permission codes
   */
  permissions: string[];
  /**
   * user`s authorized resources code
   */
  resourceCodes: string[];
  /**
   * toggle state for alarm voice broadcast
   */
  soundPlay: boolean;
  /**
   * toggle state for alarm notification pop
   */
  shouldAlarmPop: boolean;

  /**
   * visibe state for update my password`s modal
   */
  updateMyPasswordModalVisible: boolean;
  /***
   * login position string eg: `lat=120,lng=120;`
   */
  geolocation: string | null;
  /** The menus that the current user can see */
  menus: AuthorizedMenus;
};

type UserSliceCaseReducers = {
  loginStart: CaseReducer<UserSliceState, PayloadAction<undefined>>;
  loginSuccess: CaseReducer<
    UserSliceState,
    PayloadAction<
      Pick<UserSliceState, 'userId' | 'username' | 'name' | 'company'> & { callback?: () => void }
    >
  >;
  loginFail: CaseReducer<UserSliceState, PayloadAction<Pick<UserSliceState, 'errMsg'>>>;
  setUserCompany: CaseReducer<UserSliceState, PayloadAction<UserSliceState['company']>>;

  tryResetLoginFailedAttrs: CaseReducer<UserSliceState, PayloadAction<undefined>>;
  setUserIdc: CaseReducer<UserSliceState, PayloadAction<UserSliceState['idc']>>;
  setCurrentPosition: CaseReducer<UserSliceState, PayloadAction<UserSliceState['geolocation']>>;
  updateUserPermissions: CaseReducer<UserSliceState, PayloadAction<UserSliceState['permissions']>>;
  updateUserResourceCodes: CaseReducer<
    UserSliceState,
    PayloadAction<UserSliceState['resourceCodes']>
  >;
  toggleSoundPlay: CaseReducer<UserSliceState, PayloadAction<undefined>>;
  toggleShouldAlarmPop: CaseReducer<UserSliceState, PayloadAction<undefined>>;
  toggleUpdateMyPasswordModalVisible: CaseReducer<
    UserSliceState,
    PayloadAction<UserSliceState['updateMyPasswordModalVisible'] | undefined>
  >;
  setMenus: CaseReducer<UserSliceState, PayloadAction<PermissionJSON[]>>;
};

export const userSlice = createSlice<UserSliceState, UserSliceCaseReducers, 'user'>({
  name: 'user',
  initialState: getInitialUser(),
  reducers: {
    loginStart(sliceState) {
      sliceState.loading = true;
    },

    loginSuccess(sliceState, { payload: { userId, username, name, company, callback } }) {
      sliceState.company = company;
      sliceState.loading = false;
      sliceState.name = name;
      sliceState.userId = userId;
      sliceState.username = username;
      callback && callback();
    },

    loginFail(sliceState, { payload: { errMsg } }) {
      sliceState.loading = false;
      sliceState.errMsg = errMsg;
    },

    tryResetLoginFailedAttrs(sliceState) {
      sliceState.errMsg = null;
    },

    setUserCompany(sliceState, { payload }) {
      sliceState.company = payload;
    },

    setUserIdc(sliceState, { payload }) {
      sliceState.idc = payload;
    },

    setCurrentPosition(sliceState, { payload }) {
      sliceState.geolocation = payload;
    },

    updateUserPermissions(sliceState, { payload }) {
      sliceState.permissions = payload;
    },

    updateUserResourceCodes(sliceState, { payload }) {
      sliceState.resourceCodes = payload;
    },

    toggleSoundPlay(sliceState) {
      sliceState.soundPlay = !sliceState.soundPlay;
    },

    toggleShouldAlarmPop(sliceState) {
      sliceState.shouldAlarmPop = !sliceState.shouldAlarmPop;
    },

    toggleUpdateMyPasswordModalVisible(sliceState, { payload }) {
      if (payload === undefined) {
        sliceState.updateMyPasswordModalVisible = !sliceState.updateMyPasswordModalVisible;
      } else {
        sliceState.updateMyPasswordModalVisible = payload;
      }
    },

    setMenus(sliceState, { payload: permissionJSONs }) {
      const oneShortAuthorizedCodes: string[] = [];
      permissionJSONs.forEach(permissionJSON => {
        oneShortAuthorizedCodes.push(permissionJSON.code);

        const existing: PermissionJSON | undefined = sliceState.menus.entities[permissionJSON.code];
        if (existing !== undefined && shallowequal(existing, permissionJSON)) {
          return;
        }

        sliceState.menus.idsMapper[permissionJSON.id] = permissionJSON.code;
        sliceState.menus.entities[permissionJSON.code] = permissionJSON;
      });
      sliceState.menus.codes = oneShortAuthorizedCodes;
    },
  },
});

export function getInitialUser(): UserSliceState {
  return {
    loading: false,
    userId: null,
    username: null,
    name: null,
    company: null,
    geolocation: null,
    idc: null,
    errMsg: null,
    soundPlay: false,
    shouldAlarmPop: false,
    updateMyPasswordModalVisible: false,
    permissions: [],
    resourceCodes: [],
    menus: {
      entities: {},
      codes: [],
      idsMapper: {},
    },
  };
}
