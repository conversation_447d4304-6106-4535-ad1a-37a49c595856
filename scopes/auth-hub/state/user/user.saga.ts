import { call, fork, put, takeLatest } from 'redux-saga/effects';
import type { SagaReturnType } from 'redux-saga/effects';

import { mutateMyPassword } from '@manyun/auth-hub.service.mutate-my-password';
import { message } from '@manyun/base-ui.ui.message';
import { fetchPermissionsByRoleId } from '@manyun/iam.service.fetch-permissions-by-role-id';
import { fetchSpaces } from '@manyun/resource-hub.service.fetch-spaces';
import { spaceSliceActions } from '@manyun/resource-hub.state.space';

import {
  getMyPermissionsAction,
  getMyResourcesAction,
  updateMyPasswordAction,
  userSliceActions,
} from './user.action';

/** Workers */

export function* getMyPermissionsSaga() {
  const roleCode = localStorage.getItem('roleCode');
  const { error, data }: SagaReturnType<typeof fetchPermissionsByRoleId> = yield call(
    fetchPermissionsByRoleId,
    { webSite: 'DCBASE', roleCode }
  );
  if (error) {
    return;
  }
  yield put(userSliceActions.updateUserPermissions(data.map(permission => permission.code)));
}

export function* getMyResourcesSaga() {
  const { error, data }: SagaReturnType<typeof fetchSpaces> = yield call(fetchSpaces, {
    includeVirtualBlocks: true,
    authorizedOnly: true,
    nodeTypes: ['IDC', 'BLOCK', 'ROOM'],
  });
  if (error) {
    return;
  }
  yield put(spaceSliceActions.setSpaces({ data }));
  yield put(userSliceActions.updateUserResourceCodes(data.map(space => space.code)));
}

export function* mutateMyPasswordSaga({
  payload: { originalPassword, newPassword, callback },
}: ReturnType<typeof updateMyPasswordAction>) {
  const { error }: SagaReturnType<typeof mutateMyPassword> = yield call(mutateMyPassword, {
    originalPassword,
    newPassword,
  });
  const result = error ? false : true;
  if (callback) {
    callback(result);
  }
  if (error) {
    message.error(error.message);
    return;
  }

  message.success('密码已修改，请重新登录！');
}

function* watchMyPermissions() {
  yield takeLatest(getMyPermissionsAction.type, getMyPermissionsSaga);
}

function* watchMyResources() {
  yield takeLatest(getMyResourcesAction.type, getMyResourcesSaga);
}

function* watchUpdateMyPassword() {
  yield takeLatest(updateMyPasswordAction.type, mutateMyPasswordSaga);
}

export default [fork(watchMyPermissions), fork(watchMyResources), fork(watchUpdateMyPassword)];
