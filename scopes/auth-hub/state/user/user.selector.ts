import memoizeOne from 'memoize-one';

import type { Space } from '@manyun/resource-hub.service.fetch-spaces';
import { spaceSlice } from '@manyun/resource-hub.state.space';
import type { SpaceSliceState } from '@manyun/resource-hub.state.space';

import { userSlice } from './user.slice';
import type { UserSliceState } from './user.slice';

export const selectMe = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name];

export const selectMyCompany = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name].company;

export const selectMyPermissions = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name].permissions;
export const selectMyResourceCodes = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name].resourceCodes;

const selectEntitiesByCode = (codes: string[], entities: Record<string, Space>) => {
  return codes
    .map(code => entities[code])
    .filter(entity => entity !== undefined && entity !== null);
};
const memoizeOneSelectEntitiesByCode = memoizeOne(selectEntitiesByCode);
export const selectMyResources = (storeState: {
  [userSlice.name]: UserSliceState;
  [spaceSlice.name]: SpaceSliceState;
}) => {
  const codes = storeState[userSlice.name].resourceCodes;
  const entities = storeState[spaceSlice.name].entities;
  if (Object.keys(entities).length === 0 || codes.length === 0) {
    return [];
  }
  return memoizeOneSelectEntitiesByCode(codes, entities);
};

export const selectUpdateMyPasswordModalVisible = (storeState: {
  [userSlice.name]: UserSliceState;
}) => storeState[userSlice.name].updateMyPasswordModalVisible;

export const selectUpdateSoundPlay = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name].soundPlay;

export const selectShouldAlarmPop = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name].shouldAlarmPop;

export const selectMyMenus = (storeState: { [userSlice.name]: UserSliceState }) =>
  storeState[userSlice.name].menus;
