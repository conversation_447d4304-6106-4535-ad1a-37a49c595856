import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';

import { fetchUserEmail } from '@manyun/auth-hub.service.fetch-user-email';

export type UserEmailTextProps = {
  email: string;
  applyId?: number;
};

export function UserEmailText({ email, applyId }: UserEmailTextProps) {
  const [isClick, setIsClick] = useState(false);
  const [fullEmail, setFullEmail] = useState<string | null>();
  const getEmail = async () => {
    const { data, error } = await fetchUserEmail({ userId: applyId! });
    if (error) {
      message.error(error.message);
      return;
    }
    setFullEmail(data);
  };
  return (
    <Space>
      <span>{isClick ? fullEmail : email}</span>
      {applyId && !isClick && (
        <Button
          type="link"
          compact
          onClick={() => {
            setIsClick(true);
            getEmail();
          }}
        >
          查看
        </Button>
      )}
    </Space>
  );
}
