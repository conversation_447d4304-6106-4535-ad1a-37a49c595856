import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { registerMocks } from '@manyun/auth-hub.hook.use-users';
import { getMock } from '@manyun/service.request';

import { UserMentions } from './user-mentions';

export const BasicUserMentions = () => {
  const [init, setInit] = React.useState(false);
  const [form] = Form.useForm();

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerMocks(mock);
    setInit(true);
  }, []);

  if (!init) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <Form form={form}>
        <Form.Item label="内容" name="content">
          <UserMentions style={{ width: 200 }} />
        </Form.Item>
      </Form>
    </ConfigProvider>
  );
};
