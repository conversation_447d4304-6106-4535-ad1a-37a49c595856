import React from 'react';
import { useLatest } from 'react-use';

import debounce from 'lodash.debounce';

import { Mention, Mentions } from '@manyun/base-ui.ui.mentions';
import type { MentionProps, MentionsProps } from '@manyun/base-ui.ui.mentions';
import { message } from '@manyun/base-ui.ui.message';

import type { UserState } from '@manyun/auth-hub.model.user';
import { fetchPagedUsers } from '@manyun/auth-hub.service.fetch-paged-users';

type SimpleUserInfo = {
  id: number;
  name: string;
};

export type UserMentionsProps = {
  value?: string;
  onChange?: (value: string, valueForDisplay: string, users: SimpleUserInfo[]) => void;
  /** 用户状态过滤参数，默认为 undefined，即不过滤 */
  userState?: UserState;
} & Omit<MentionsProps, 'value' | 'onChange' | 'children'>;

export function UserMentions({ value, onChange, userState, ...rest }: UserMentionsProps) {
  const onChangeRef = useLatest(onChange);
  const handleChange = React.useMemo(() => {
    const func: MentionsProps['onChange'] = (e, _, valueForDisplay, items) => {
      const users = items.map(item => ({
        id: Number(item.id),
        name: item.display,
      }));
      onChangeRef.current?.(e.target.value, valueForDisplay, users);
    };
    return func;
  }, [onChangeRef]);

  const [loading, setLoading] = React.useState(false);

  const getUsersForMention = React.useMemo(() => {
    const func: MentionProps['data'] = async (keyword, callback) => {
      setLoading(true);
      const { error, data } = await fetchPagedUsers({
        key: keyword,
        pageNum: 1,
        pageSize: 10,
        needResource: false,
        enable: userState,
      });
      setLoading(false);
      if (error) {
        message.error(error);
        return;
      }
      callback(
        data.data.map(user => ({
          id: user.id,
          display: user.name,
        }))
      );
    };
    return debounce(func, 100);
  }, [userState]);

  return (
    <Mentions {...rest} value={value} onChange={handleChange}>
      <Mention
        trigger={/(?:^@)*(@([^@]*))$/}
        displayTransform={(_, name) => `@${name}`}
        data={getUsersForMention}
        isLoading={loading}
        appendSpaceOnAdd
      />
    </Mentions>
  );
}
