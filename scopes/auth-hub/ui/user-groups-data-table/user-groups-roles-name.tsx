import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { Spin } from '@manyun/base-ui.ui.spin';

import { useRoles } from '@manyun/auth-hub.hook.use-roles';
import { selectRolesByCodes } from '@manyun/auth-hub.state.roles';

export type UserGroupsRolesNameProps = {
  codes: string[] | null;
};

/**
 *  根据角色code展示角色name
 *
 */
export function UserGroupsRolesName({ codes }: UserGroupsRolesNameProps) {
  const [{ loading }, getRoles] = useRoles();
  const roles = useSelector(selectRolesByCodes(codes || []));

  useEffect(() => {
    getRoles({
      policy: storeCodes =>
        (codes || []).every(code => storeCodes.includes(code)) ? 'cache-only' : 'network-only',
    });
  }, [codes]);

  if (loading) {
    return <Spin spinning={loading} size="small" />;
  }
  return <span>{roles.map(role => role.name).join('｜')}</span>;
}
