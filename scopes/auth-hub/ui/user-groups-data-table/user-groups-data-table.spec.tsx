import React from 'react';

import { act, render, screen } from '@testing-library/react';

import {
  BasicUserGroupsDataTable,
  LocalGroupsDataTable,
} from './user-groups-data-table.composition';

test('should render with the correct text by async request', async () => {
  await act(async () => {
    render(<BasicUserGroupsDataTable delayResponse={0} />);
  });
  const tableContainer = screen.getByRole('table');
  expect(tableContainer).toHaveTextContent('name_0');
  expect(tableContainer).toHaveTextContent('name_1');
  expect(tableContainer).toHaveTextContent('name_2');
});

test('should render with the correct text by local data', () => {
  const { getByText } = render(<LocalGroupsDataTable />);
  const rendered = getByText('用户组1');
  expect(rendered).toBeTruthy();
});
