import React from 'react';

import { ColumnType, ColumnsType, Table, TableProps } from '@manyun/base-ui.ui.table';

import { UserGroup } from '@manyun/auth-hub.model.user-group';

import { UserGroupsRolesName } from './user-groups-roles-name';

export type UserGroupsDataTableProps = {
  data: UserGroup[];
  operateColumns?: ColumnType<UserGroup>;
} & Pick<TableProps<UserGroup>, 'loading' | 'onChange' | 'rowSelection' | 'pagination'>;

export function UserGroupsDataTable({ data, loading, ...rest }: UserGroupsDataTableProps) {
  const baseColumns: ColumnsType<UserGroup> = [
    {
      title: '用户组名称',
      dataIndex: 'name',
      ellipsis: true,
    },
    {
      title: '资源位置',
      dataIndex: 'resourceCodes',
      ellipsis: true,
      render: resourceCodes => {
        if (Array.isArray(resourceCodes)) {
          return resourceCodes.join('｜');
        }
        return null;
      },
    },
    {
      title: '角色',
      dataIndex: 'roleCodes',
      ellipsis: true,
      render: roleCodes => <UserGroupsRolesName codes={roleCodes} />,
    },
  ];

  return (
    <Table
      {...rest}
      size="small"
      rowKey="code"
      dataSource={data}
      columns={rest.operateColumns ? [...baseColumns, rest.operateColumns] : [...baseColumns]}
      loading={loading}
    />
  );
}
