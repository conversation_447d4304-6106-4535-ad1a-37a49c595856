import React, { useEffect } from 'react';
import { useSelector } from 'react-redux';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';

import { useUserGroups } from '@manyun/auth-hub.hook.use-user-groups';
// @ts-ignore
import { registerWebMocks as registerFetchRoesWebMocks } from '@manyun/auth-hub.service.pm.fetch-roles/dist/fetch-roles.mock';
import { registerWebMocks as registerFetchUserGroupsWebMocks } from '@manyun/auth-hub.service.pm.fetch-user-groups';
import { selectUserGroupsByCodes } from '@manyun/auth-hub.state.user-groups';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroyMock, getMock, useRemoteMock, webRequest } from '@manyun/service.request';

import { UserGroupsDataTable } from './user-groups-data-table';

const TableDemo = () => {
  const [userGroupsState, getUserGroups] = useUserGroups();
  const userGroupData = useSelector(selectUserGroupsByCodes());
  useEffect(() => {
    getUserGroups();
  }, []);

  return (
    <UserGroupsDataTable
      data={userGroupData}
      loading={userGroupsState.loading}
      pagination={false}
    />
  );
};

export const BasicUserGroupsDataTable = ({
  delayResponse = 2 * 1000,
}: {
  delayResponse?: number;
}) => {
  const [initialized, update] = React.useState(false);

  useEffect(() => {
    // const mockOff = useRemoteMock('web', { adapter: 'xhr' });
    const mock = getMock('web', { delayResponse: delayResponse });
    registerFetchRoesWebMocks(mock);
    registerFetchUserGroupsWebMocks(mock);
    // destroyMock();
    // webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
    return () => {
      // mockOff();
      destroyMock();
    };
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <TableDemo />
      </FakeStore>
    </ConfigProvider>
  );
};

const TableDemo2 = () => {
  const [userGroupsState, getUserGroups] = useUserGroups();
  const userGroupData = useSelector(selectUserGroupsByCodes());
  useEffect(() => {
    getUserGroups();
  }, []);

  return (
    <UserGroupsDataTable
      data={userGroupData}
      loading={userGroupsState.loading}
      pagination={false}
      operateColumns={{
        title: '操作',
        width: 50,
        dataIndex: 'action',
        render: (_, {}) => (
          <Button type="link" compact>
            移除
          </Button>
        ),
      }}
    />
  );
};

export const BasicUserGroupsDataTable2 = () => {
  const [initialized, update] = React.useState(false);

  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerFetchRoesWebMocks(mock);
    registerFetchUserGroupsWebMocks(mock);
    // destroyMock();
    // webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <TableDemo2 />
      </FakeStore>
    </ConfigProvider>
  );
};

export const LocalGroupsDataTable = () => {
  return (
    <ConfigProvider>
      <FakeStore>
        <UserGroupsDataTable
          data={[{ id: 1, name: '用户组1', code: 'code01', resourceCodes: [], roleCodes: [] }]}
        />
      </FakeStore>
    </ConfigProvider>
  );
};
