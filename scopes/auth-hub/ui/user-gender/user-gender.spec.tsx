import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicUserGenderSelect } from './user-gender.composition';

it('user-gender select', async () => {
  const { getByRole } = render(<BasicUserGenderSelect />);
  userEvent.click(getByRole('combobox'));
  await waitFor(() => {
    expect(screen.getByText('男性')).toBeInTheDocument();
  });
});
