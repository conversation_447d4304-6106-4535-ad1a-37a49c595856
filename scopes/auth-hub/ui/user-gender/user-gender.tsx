import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { UserGender } from '@manyun/auth-hub.model.user';

export const userGenderTextMapper = {
  MALE: '男性',
  FEMALE: '女性',
} as Record<UserGender, string>;

const options = Object.entries(userGenderTextMapper).map(([key, value]) => ({
  label: value,
  value: key,
}));

export type UserGenderSelectProps = SelectProps;

export const UserGenderSelect = React.forwardRef(
  (props: SelectProps, ref?: React.Ref<RefSelectProps>) => {
    const selectProps = { ...props, options: options };

    return <Select ref={ref} style={{ width: 200 }} {...selectProps} />;
  }
);
UserGenderSelect.displayName = 'UserGenderSelect';

export type UserGenderTextProps = {
  style?: React.CSSProperties;
  userGender: UserGender;
};

export function UserGenderText({ userGender, style }: UserGenderTextProps) {
  return <span style={style}>{userGenderTextMapper[userGender]}</span>;
}
