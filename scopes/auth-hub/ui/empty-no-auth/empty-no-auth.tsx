import React from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Typography } from '@manyun/base-ui.ui.typography';

import { AUTH_REQUEST_CREATOR_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';

import NoAthPng from './assets/empty_no_auth.png';

export type EmptyNoAuthProps = {
  /** 透出申请权限按钮，默认透出 */
  showApplyAuthButton?: boolean;
  /** 图片宽度 */
  picWidth?: number;
  /** 申请权限外部回调 */
  applyAuthCallback?: () => void;
  /** button */
  buttonStyle?: Record<string, string | number>;
};

/**
 * 无权限按钮
 * demo: https://www.figma.com/file/JxtLuN4DEszpOaoESNPB24/%E8%AE%BE%E5%A4%87%E6%80%81%E5%8A%BF?node-id=1694%3A128951
 */
export function EmptyNoAuth({
  showApplyAuthButton = true,
  picWidth = 160,
  applyAuthCallback,
  buttonStyle,
}: EmptyNoAuthProps) {
  const history = useHistory();
  const handleApplyAuth = () => {
    if (typeof applyAuthCallback === 'function') {
      applyAuthCallback();
    } else {
      history.push(AUTH_REQUEST_CREATOR_ROUTE_PATH);
    }
  };
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <img alt="申请权限" width={picWidth} src={NoAthPng} />
      <div>
        <Typography.Text type="secondary">暂无权限</Typography.Text>
      </div>
      {showApplyAuthButton && (
        <Button
          type="primary"
          onClick={handleApplyAuth}
          style={
            buttonStyle || {
              width: 72,
              display: 'flex',
              justifyContent: 'center',
              borderRadius: 2,
              marginTop: 8,
            }
          }
        >
          申请权限
        </Button>
      )}
    </div>
  );
}
