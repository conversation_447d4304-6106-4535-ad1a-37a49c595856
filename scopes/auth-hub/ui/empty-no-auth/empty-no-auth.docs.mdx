---
description: A EmptyNoAuth component.
无权限组件
属性：

 - 透出申请权限按钮，默认透出

 `showApplyAuthButton?: boolean;`
 - 图片宽度

  `picWidth?: number; `
 - 图片高度

  `picHeight?: number;`

 - 申请权限外部回调
  `applyAuthCallback?: () => void;`

---

import { EmptyNoAuth } from './empty-no-auth';

A component that does something special and renders text in a div.

### Component usage

```js
<EmptyNoAuth>Hello world!</EmptyNoAuth>
```

### Render hello world!

```js live
<EmptyNoAuth>Hello world!</EmptyNoAuth>
```
