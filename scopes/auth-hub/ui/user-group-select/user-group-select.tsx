import React, { useEffect, useState } from 'react';

import { Select, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchPagedUserGroups } from '@manyun/auth-hub.service.fetch-paged-user-groups';

export type UserGroupSelectProps<T = any> = {
  trigger?: 'onFocus' | 'onDidMount';
} & SelectProps<T>;

// TODO: @Jerry import this type from `@manyun/base-ui.ui.select`
type RefSelectProps = {
  focus: () => void;
  blur: () => void;
};
export const UserGroupSelect = React.forwardRef(
  ({ trigger, ...selectProps }: UserGroupSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const [loading, setLoading] = useState(false);
    const [data, setData] = useState([] as any);

    useEffect(() => {
      const fetchUserGroupData = async () => {
        const res = await fetchPagedUserGroups({ pageNum: 1, pageSize: 10 });

        setData(res.data.data);
        setLoading(true);
      };
      fetchUserGroupData();
    }, []);
    if (!loading) {
      return null;
    }
    return (
      <Select
        ref={ref}
        showSearch
        optionLabelProp="name"
        optionFilterProp="name"
        {...selectProps}
        // loading={loading}
        // options={data as any[]}
        // onFocus={() => {
        //   console.log('onFocus');

        //   // fetchUserGroupData();
        //   console.log('onFocus-data', data);
        // }}
      >
        {data.length ? (
          data?.map((item: any) => (
            <Select.Option value={item.groupName} key={item.groupName}>
              {item.groupName}
            </Select.Option>
          ))
        ) : (
          <></>
        )}
      </Select>
    );
  }
);

// UserGroupSelect.displayName = 'UserGroupSelect';

// UserGroupSelect.defaultProps = {
//   fieldNames: {
//     value: 'id',
//     label: 'name',
//   },
// };
