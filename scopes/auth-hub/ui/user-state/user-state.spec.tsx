import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicUserStateSelect, BasicUserStateText } from './user-state.composition';

it('user-state select', async () => {
  const { getByRole } = render(<BasicUserStateSelect />);
  userEvent.click(getByRole('combobox'));
  await waitFor(() => {
    expect(screen.getByText('离职')).toBeInTheDocument();
  });
});

it('user-state text', async () => {
  const { getByText } = render(<BasicUserStateText />);
  const rendered = getByText('在职');
  expect(rendered).toBeTruthy();
});
