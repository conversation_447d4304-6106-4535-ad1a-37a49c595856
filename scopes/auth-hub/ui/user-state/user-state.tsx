import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { UserState } from '@manyun/auth-hub.model.user';

export const userStateTextMapper = {
  'in-service': '在职',
  leaving: '离职',
} as Record<UserState, string>;

const options = Object.entries(userStateTextMapper).map(([key, value]) => ({
  label: value,
  value: key,
}));

export type UserStateSelectProps = SelectProps;

export const UserStateSelect = React.forwardRef(
  (props: UserStateSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const selectProps = { ...props, options: options };

    return <Select ref={ref} style={{ width: 200 }} {...selectProps} />;
  }
);
UserStateSelect.displayName = 'UserStateSelect';

export type UserStateTextProps = {
  style?: React.CSSProperties;
  userState: UserState;
};

export function UserStateText({ userState, style }: UserStateTextProps) {
  return <span style={style}>{userStateTextMapper[userState]}</span>;
}
