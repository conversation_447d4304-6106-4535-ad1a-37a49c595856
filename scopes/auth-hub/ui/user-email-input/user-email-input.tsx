import React, { useCallback, useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { Input } from '@manyun/base-ui.ui.input';
import type { InputProps } from '@manyun/base-ui.ui.input';

import { fetchUserEmail } from '@manyun/auth-hub.service.fetch-user-email';

import styles from './user-email-input.module.less';

export type UserEmailInputProps = {
  userId: number;
} & InputProps;

export function UserEmailInput({
  userId,
  value: _value,
  onChange,
  ...otherProps
}: UserEmailInputProps) {
  const [disabled, setDisabled] = useState(true);
  const [email, setEmail] = useState<InputProps['value']>(); // 根据 userId 获取的未脱敏邮箱

  const handleClick = useCallback(() => {
    fetchUserEmail({
      userId,
    }).then(({ data, error }) => {
      if (!error && data !== null) {
        setEmail(data);
        setDisabled(false);
      }
    });
  }, [userId]);

  const [changed, setChanged] = useState(false);
  const handleChange = useCallback(
    e => {
      setChanged(true);
      if (onChange) {
        onChange(e);
      }
    },
    [onChange]
  );

  // email 仅在点击编辑后作默认值
  const value = changed ? _value : email || _value;
  return (
    <div className={styles.wrapper}>
      <Input {...otherProps} disabled={disabled} value={value} onChange={handleChange} />
      <Button className={styles.btn} type="link" onClick={handleClick}>
        编辑
      </Button>
    </div>
  );
}
