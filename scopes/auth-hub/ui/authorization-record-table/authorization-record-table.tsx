import React from 'react';
import { Link } from 'react-router-dom';

import { message } from '@manyun/base-ui.ui.message';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Table } from '@manyun/base-ui.ui.table';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { reactNodeToText } from '@manyun/base-ui.util.react-node-to-text';

import type { AuthorizationRecordJSON } from '@manyun/auth-hub.model.authorization-record';
import {
  AuthorizationRecord,
  getAuthorizationRecordLocales,
} from '@manyun/auth-hub.model.authorization-record';
import { generateRoleRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { fetchAuthorizationRecordList } from '@manyun/auth-hub.service.fetch-authorization-record-list';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Website } from '@manyun/dc-brain.model.website';
import { SpaceText } from '@manyun/resource-hub.ui.space-text';

export type Column =
  | 'userName'
  | 'website'
  | 'roleName'
  | 'resourceList'
  | 'expiredAt'
  | 'isDeleted'
  | 'modifiedAt'
  | 'modifierId';

export type AuthorizationRecordTableProps = Omit<TableProps<AuthorizationRecordJSON>, 'columns'> & {
  userId?: number;
  showColumns?: Column[];
  operationColumn?: ColumnType<AuthorizationRecordJSON>;
};

export function AuthorizationRecordTable({
  userId,
  showColumns = [
    'website',
    'roleName',
    'resourceList',
    'expiredAt',
    'isDeleted',
    'modifiedAt',
    'modifierId',
  ],
  operationColumn,
  ...rest
}: AuthorizationRecordTableProps) {
  const [records, setRecords] = React.useState<AuthorizationRecordJSON[]>([]);
  const [loading, setLoading] = React.useState(false);
  const locales = React.useMemo(() => getAuthorizationRecordLocales(), []);

  const loadData = React.useCallback(async userId => {
    setLoading(true);
    const { error, data } = await fetchAuthorizationRecordList({ userId });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    setRecords(data.data);
  }, []);

  React.useEffect(() => {
    if (typeof userId !== 'number' || !userId) {
      return;
    }
    loadData(userId);
  }, [loadData, userId]);

  const columns = React.useMemo(() => {
    const websiteObject = new Website();
    const basicColumns: ColumnType<AuthorizationRecordJSON>[] = [
      {
        title: locales.userName,
        dataIndex: 'userName',
        width: 130,
        render: (_, { userId, userName }) => (
          <UserLink userId={Number(userId)} userName={userName} native target="_blank" />
        ),
      },
      {
        title: locales.website,
        dataIndex: 'website',
        width: 150,
        render: (_, { website }) => websiteObject.getName(website),
      },
      {
        title: locales.roleName,
        dataIndex: 'roleName',
        width: 160,
        render: (_, { roleCode, roleName }) => (
          <Link to={generateRoleRoutePath({ roleCode })}>{roleName}</Link>
        ),
      },
      {
        title: locales.resourceList,
        dataIndex: 'resourceList',
        ellipsis: {
          showTitle: true,
        },
        render: (_, { resourceType, resourceList }) => {
          const content = resourceList.map((resource, index) => {
            const separator = index === 0 ? '' : '｜';
            return (
              <React.Fragment key={resource.resourceCode}>
                {separator}
                {resourceType === 'BUILDING' ? (
                  <SpaceText guid={resource.resourceCode} />
                ) : (
                  resource.resourceName
                )}
              </React.Fragment>
            );
          });
          const contentStr = reactNodeToText(content);

          return (
            <Typography.Text
              ellipsis={{
                tooltip: {
                  title: contentStr,
                  overlayInnerStyle: { width: 700 },
                },
              }}
            >
              {content}
            </Typography.Text>
          );
        },
      },
      {
        title: locales.expiredAt,
        dataIndex: 'expiredAt',
        width: 180,
        render: (_, record) => AuthorizationRecord.fromJSON(record).getFormattedExpiredAt(),
      },
      {
        title: locales.status['__self'],
        dataIndex: 'isDeleted',
        width: 80,
        render: (_, { expiredAt }) =>
          new Date().valueOf() >= expiredAt ? (
            <Tag color="error">{locales.status.invalid}</Tag>
          ) : (
            <Tag color="success">{locales.status.valid}</Tag>
          ),
      },
      {
        title: locales.modifiedAt,
        dataIndex: 'modifiedAt',
        width: 180,
        render: (_, record) => AuthorizationRecord.fromJSON(record).getFormattedModifiedAt(),
      },
      {
        title: locales.modifierId,
        dataIndex: 'modifierId',
        width: 150,
        render: (_, { modifiedBy }) => (
          <UserLink userId={Number(modifiedBy.id)} native target="_blank" />
        ),
      },
    ];
    let _columns: ColumnType<AuthorizationRecordJSON>[] = [];
    if (showColumns) {
      showColumns.forEach(clnKey => {
        const column = basicColumns.find(cln => cln!.dataIndex === clnKey);
        if (column) {
          _columns.push(column);
        }
      });
    } else {
      _columns = basicColumns;
    }
    if (operationColumn) {
      _columns.push(operationColumn);
    }
    return _columns;
  }, [
    locales.expiredAt,
    locales.modifiedAt,
    locales.modifierId,
    locales.resourceList,
    locales.roleName,
    locales.status,
    locales.userName,
    locales.website,
    operationColumn,
    showColumns,
  ]);

  return (
    <Table<AuthorizationRecordJSON>
      rowKey="id"
      {...rest}
      columns={columns}
      loading={'loading' in rest ? rest.loading : loading}
      dataSource={'dataSource' in rest ? rest.dataSource : records}
    />
  );
}
