/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-7
 *
 * @packageDocumentation
 */
import React, { useCallback } from 'react';

import { nanoid } from 'nanoid';

import type { EditableFormInstance, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';

import type { CustomerPerson } from '@manyun/auth-hub.gql.client.customer-white-list';
import { useLazyCustomerWhiteStaff } from '@manyun/auth-hub.gql.client.customer-white-list';

export type WhiteStaff = CustomerPerson & { id: string; disabled?: boolean };
const phoneNumberPattern = /^1[3456789]\d{9}$/;

export type CustomerWhiteStaffTableProps = {
  editableFormRef?: React.MutableRefObject<EditableFormInstance<WhiteStaff> | undefined>;
  type?: string;
  company?: string;
  allowAddRow?: boolean;
  value?: WhiteStaff[];
  onChange?: (value: WhiteStaff[]) => void;
};

export function CustomerWhiteStaffTable({
  editableFormRef,
  value,
  type,
  company,
  allowAddRow = !!type && !!company,
  onChange,
}: CustomerWhiteStaffTableProps) {
  const [fetchCustomerWhiteStaff] = useLazyCustomerWhiteStaff();

  const onHandlePhoneNumberChange = useCallback(
    async (inputValue: string, rowKey: string) => {
      if (inputValue && inputValue.length === 11 && phoneNumberPattern.test(inputValue)) {
        const { data } = await fetchCustomerWhiteStaff({
          variables: {
            phoneNo: inputValue,
          },
        });
        const exitingStaff = data?.customerWhiteStaff?.data;
        const newValues = (value ?? []).map(item => {
          if (rowKey && item.id === rowKey) {
            return exitingStaff
              ? {
                  ...item,
                  position: exitingStaff.position,
                  userName: exitingStaff.userName ?? '',
                  email: exitingStaff.email,
                  disabled: true,
                }
              : { ...item, disabled: false };
          }
          return item;
        });
        onChange?.(newValues);
      }
    },
    [fetchCustomerWhiteStaff, onChange, value]
  );

  return (
    <EditableProTable<WhiteStaff>
      controlled
      rowKey="id"
      editableFormRef={editableFormRef}
      scroll={{ x: 'max-content' }}
      recordCreatorProps={
        allowAddRow
          ? {
              position: 'top',
              creatorButtonText: '添加人员',
              newRecordType: 'dataSource',
              record(index, dataSource) {
                return {
                  id: nanoid(6),
                  userName: '',
                  phone: '',
                  email: '',
                };
              },
            }
          : false
      }
      editable={{
        type: 'multiple',
        deletePopconfirmMessage: '删除此行？',
        editableKeys: value?.map(({ id }) => id),
        onValuesChange(_record, dataSource) {
          onChange?.(dataSource);
        },
        actionRender: (_row, _config, dom) => [dom.delete],
      }}
      columns={
        [
          {
            title: '手机号码',
            dataIndex: 'phone',
            fixed: 'left',
            formItemProps() {
              return {
                rules: [
                  {
                    validator: async (_, val) => {
                      if (!val) {
                        return Promise.reject('手机号码必填');
                      } else if (val.length > 11) {
                        return Promise.reject('最多输入 11 个字符！');
                      } else if (!phoneNumberPattern.test(val)) {
                        return Promise.reject('手机号格式错误，请重新输入');
                      } else {
                        const { data } = await fetchCustomerWhiteStaff({
                          variables: { phoneNo: val },
                        });
                        const exitingStaff = data?.customerWhiteStaff?.data;
                        if (exitingStaff) {
                          if (exitingStaff.type !== type) {
                            return Promise.reject('该人员已存在不同的类型申请记录');
                          } else if (exitingStaff.company.value !== company) {
                            return Promise.reject('该人员已存在不同的所属公司申请记录');
                          }
                        }
                        return Promise.resolve();
                      }
                    },
                  },
                ],
              };
            },
            renderFormItem(schema, config, form) {
              return (
                <Input
                  onBlur={async e => {
                    const inputValue = e.target.value;
                    const rowKey = config.recordKey as string;
                    onHandlePhoneNumberChange(inputValue, rowKey);
                  }}
                />
              );
            },
          },
          {
            title: '姓名',
            dataIndex: 'userName',
            formItemProps(form, config) {
              return {
                rules: [
                  {
                    required: true,
                    message: '姓名必填',
                  },
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              };
            },
            renderFormItem(schema, config, form) {
              return <Input disabled={config.record?.disabled} />;
            },
          },
          {
            title: '岗位',
            dataIndex: 'position',
            formItemProps(form, config) {
              return {
                rules: [
                  {
                    max: 20,
                    message: '最多输入 20 个字符！',
                  },
                ],
              };
            },
            renderFormItem(schema, config, form) {
              return <Input />;
            },
          },
          {
            title: '邮箱',
            dataIndex: 'email',
            formItemProps(form, config) {
              return {
                rules: [
                  { required: true, message: '邮箱必填' },
                  {
                    type: 'email',
                    message: '邮箱格式错误，请重新输入',
                  },
                ],
              };
            },
            renderFormItem(schema, config, form) {
              return <Input disabled={config.record?.disabled} />;
            },
          },
          {
            width: 60,
            fixed: 'right',
            title: '操作',
            dataIndex: 'option',
            valueType: 'option',
          },
        ] as ProColumns<WhiteStaff>[]
      }
      value={value}
      onChange={onChange}
    />
  );
}
