import React, { useEffect } from 'react';
import { useParams } from 'react-router';

import omit from 'lodash.omit';

import { Button } from '@manyun/base-ui.ui.button';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import { Container } from '@manyun/base-ui.ui.container';
import type { EditableFormInstance } from '@manyun/base-ui.ui.editable-pro-table';
import { Explanation } from '@manyun/base-ui.ui.explanation';
import { FooterToolBar } from '@manyun/base-ui.ui.footer-tool-bar';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import type { CustomerPerson } from '@manyun/auth-hub.gql.client.customer-white-list';
import {
  useCustomerWhiteListCreation,
  useLazyCustomerWhiteListDetail,
} from '@manyun/auth-hub.gql.client.customer-white-list';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import type { WhiteStaff } from '@manyun/iam.ui.customer-white-staff-table';
import { CustomersOnRacksSelect } from '@manyun/resource-hub.ui.customers-on-racks-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

import { CustomerWhiteStaffTable } from './components/customer-white-staff-table';

const keys = ['type', 'company', 'blockGuidList', 'note'] as const;

export type FieldName = (typeof keys)[number];
export type FormValues = {
  type: string;
  note?: string;
  blockGuidList: string[][];
  company: { label: string; value: string };
  persons: WhiteStaff[];
};

export type CustomerWhiteListApplicationMutatorProps = {
  mode: 'edit' | 'create';
  initialValues?: FormValues;
  unusedFormItems?: FieldName[];

  /**
   * 创建成功
   */
  onSuccess?: () => void;
  onCancel?: () => void;
};

export function CustomerWhiteListApplicationMutator({
  mode,
  initialValues,
  unusedFormItems,
  onSuccess,
  onCancel,
}: CustomerWhiteListApplicationMutatorProps) {
  const editableFormRef = React.useRef<EditableFormInstance<WhiteStaff>>();

  const [getCustomerWhiteListDetail, { loading: createLoading }] = useLazyCustomerWhiteListDetail();
  const [createCustomerWhiteList, { loading: updateLoading }] = useCustomerWhiteListCreation({
    onCompleted(data) {
      if (!data.createCustomerWhiteList?.success) {
        message.error(data.createCustomerWhiteList?.message);
        return;
      }
      message.success('新建成功');
      onSuccess && onSuccess();
    },
  });

  const { id } = useParams<{ id: string }>();
  const [form] = Form.useForm<FormValues>();
  const type = Form.useWatch('type', form);
  const company = Form.useWatch('company', form);

  useEffect(() => {
    if (mode === 'edit' && id) {
      (async function () {
        const { error, data } = await getCustomerWhiteListDetail({ variables: { id: Number(id) } });
        if (error) {
          message.error(error.message);
          return;
        }
        if (data && data.customerWhiteListDetail?.data) {
          const { data: detailData } = data.customerWhiteListDetail;
          const composedBlockGuidList = detailData.spaceGuidList
            ? detailData.spaceGuidList.split(',').map(item => [item.split('.')[0], item])
            : [];
          form.setFieldsValue({
            blockGuidList: composedBlockGuidList,
            company: {
              label: detailData.company.name,
              value: detailData.company.value,
            },
          });
        }
      })();
    }
  }, [mode, form, id, getCustomerWhiteListDetail]);
  return (
    <>
      <Form<FormValues> labelCol={{ span: 4 }} wrapperCol={{ span: 20 }} form={form}>
        <Space
          style={{ display: 'flex', height: '100%', marginBottom: '68px' }}
          direction="vertical"
          size="middle"
        >
          <Container size="large">
            <Space style={{ display: 'flex', height: '100%' }} direction="vertical" size="middle">
              <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                基本信息
              </Typography.Title>

              <div style={{ width: 710 }}>
                {!unusedFormItems?.includes('type') && (
                  <Form.Item
                    label="类型"
                    name="type"
                    rules={[{ required: true, message: '类型必选' }]}
                  >
                    <Select
                      style={{ width: 280 }}
                      options={[
                        {
                          label: '客户',
                          value: 'CUSTOMER',
                        },
                        {
                          label: '供应商',
                          value: 'SUPPLIER',
                        },
                      ]}
                      onChange={nextType => {
                        if (type === nextType) {
                          return;
                        }
                        form.setFieldValue('company', undefined);
                        form.setFieldValue('persons', []);
                      }}
                    />
                  </Form.Item>
                )}
                {!unusedFormItems?.includes('company') && (
                  <Form.Item
                    label="所属公司"
                    name="company"
                    rules={[{ required: true, message: '所属公司必选' }]}
                  >
                    {type === 'CUSTOMER' ? (
                      <CustomersOnRacksSelect
                        style={{ width: 280 }}
                        disabled={!type}
                        labelInValue
                        allowClear
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        onChange={(nextCompany: any) => {
                          if (company?.value === nextCompany?.value) {
                            return;
                          }
                          form.setFieldValue('persons', []);
                        }}
                      />
                    ) : (
                      <VendorSelect
                        style={{ width: 280 }}
                        labelInValue
                        allowClear
                        disabled={!type}
                        // eslint-disable-next-line @typescript-eslint/no-explicit-any
                        onChange={(nextCompany: any) => {
                          if (company?.value === nextCompany?.value) {
                            return;
                          }
                          form.setFieldValue('persons', []);
                        }}
                      />
                    )}
                  </Form.Item>
                )}
                {!unusedFormItems?.includes('blockGuidList') && (
                  <Form.Item
                    label="授权机房楼栋"
                    name="blockGuidList"
                    rules={[{ required: true, message: '授权机房楼栋必选！' }]}
                  >
                    <LocationCascader
                      style={{ width: 280 }}
                      multiple
                      disabledNoChildsNodes={['IDC']}
                      maxTagCount="responsive"
                      showCheckedStrategy={Cascader.SHOW_CHILD}
                      changeOnSelect={false}
                      authorizedOnly
                      allowClear
                    />
                  </Form.Item>
                )}
                {!unusedFormItems?.includes('note') && (
                  <Form.Item
                    label="备注说明"
                    name="note"
                    rules={[{ type: 'string', max: 500, message: '最多输入 500 个字符！' }]}
                  >
                    <Input.TextArea style={{ width: 395 }} rows={4} />
                  </Form.Item>
                )}
              </div>
            </Space>
          </Container>
          <Container size="large">
            <Space style={{ display: 'flex', height: '100%' }} direction="vertical" size="middle">
              <Typography.Title style={{ marginBottom: 0 }} showBadge level={5}>
                人员信息
                <Explanation
                  iconType="question"
                  tooltip={{ title: '以下人员可有权限通过微信小程序等外部渠道发起访客申请' }}
                />
              </Typography.Title>
              <Form.Item
                name="persons"
                wrapperCol={{ span: 24 }}
                rules={[
                  {
                    message: '至少添加一个人员',
                    type: 'number',
                    min: 1,
                    transform(value?: CustomerPerson[]) {
                      return value?.length ?? 0;
                    },
                  },
                ]}
              >
                <CustomerWhiteStaffTable
                  editableFormRef={editableFormRef}
                  type={type}
                  company={company?.value}
                />
              </Form.Item>
            </Space>
          </Container>
        </Space>
        <FooterToolBar>
          <Space>
            <Button
              type="primary"
              loading={createLoading || updateLoading}
              onClick={async () => {
                try {
                  const [values] = await Promise.all([
                    form.validateFields(),
                    editableFormRef.current?.validateFields(),
                  ]);
                  createCustomerWhiteList({
                    variables: {
                      customerType: values.type,
                      company: values.company.value,
                      companyName: values.company.label,
                      note: values.note,
                      blockGuidList: values.blockGuidList
                        .filter(blockGuid => blockGuid.length > 1)
                        .map(item => item[1]),
                      persons: values.persons.map(person =>
                        omit(person, ['id', 'disabled'])
                      ) as CustomerPerson[],
                    },
                  });
                } catch (error) {
                  // eslint-disable-next-line no-console
                  console.log(error);
                }
              }}
            >
              提交
            </Button>
            {onCancel && <Button onClick={onCancel}>取消</Button>}
          </Space>
        </FooterToolBar>
      </Form>
    </>
  );
}
