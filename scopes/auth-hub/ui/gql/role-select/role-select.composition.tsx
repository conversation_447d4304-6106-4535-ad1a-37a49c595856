import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Divider } from '@manyun/base-ui.ui.divider';

import { webRequest } from '@manyun/service.request';

import { RoleSelect } from './role-select';

webRequest.axiosInstance.defaults.baseURL = 'http://***********:13000';
webRequest.mockOn();

export const BasicRoleSelect = () => {
  return (
    <ConfigProvider>
      <Divider orientation="left">Default</Divider>
      <RoleSelect<string>
        style={{ width: 200 }}
        onChange={(value, option) => {
          // eslint-disable-next-line no-console
          console.log('value: ', value, ' option: ', option);
        }}
      />
    </ConfigProvider>
  );
};

export const SelectMultipleRoleSelect = () => {
  return (
    <ConfigProvider>
      <Divider orientation="left">Multiple</Divider>
      <RoleSelect<string[]>
        style={{ width: 200 }}
        mode="multiple"
        onChange={(values, options) => {
          // eslint-disable-next-line no-console
          console.log('values: ', values, ' options: ', options);
        }}
      />
    </ConfigProvider>
  );
};
