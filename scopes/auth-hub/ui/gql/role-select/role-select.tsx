import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { useGetRoles } from '@manyun/auth-hub.gql.client.roles';
import type { Role } from '@manyun/auth-hub.gql.client.roles';
import type { WebsiteCode } from '@manyun/dc-brain.model.website';

export type Option = {
  label: string;
  value: string;
};

export type RoleSelectProps<Value extends string | string[] = string> = {
  website: WebsiteCode;
  authorized?: boolean;
  onChange?: (
    value: Value,
    option: Value extends string ? Option : Option[],
    role: Value extends string ? Role : Role[]
  ) => void;
} & Omit<SelectProps<Value, Option>, 'options' | 'onChange'>;

function _RoleSelect<Value extends string | string[] = string>(
  { website, authorized = false, onChange, ...restProps }: RoleSelectProps<Value>,
  ref?: React.ForwardedRef<RefSelectProps>
) {
  const { data, refetch } = useGetRoles({ website, authorized });
  const roles = React.useMemo(() => data?.roles.data || [], [data?.roles.data]);
  const options: Option[] = React.useMemo(
    () =>
      roles.map(role => ({
        label: role.name,
        value: role.code,
      })),
    [roles]
  );

  React.useEffect(() => {
    refetch({ website, authorized });
  }, [website, authorized, refetch]);

  return (
    <Select<Value, Option>
      {...restProps}
      ref={ref}
      options={options}
      onChange={(value, option) => {
        const currentRoles = Array.isArray(option)
          ? roles.filter(role => option.some(item => item.value === role.code))
          : roles.find(role => role.code === option.value)!;
        onChange?.(
          value,
          option as Value extends string ? Option : Option[],
          currentRoles as Value extends string ? Role : Role[]
        );
      }}
    />
  );
}

interface GenericRoleSelect {
  <Value extends string | string[] = string>(
    props: RoleSelectProps<Value>,
    ref: React.ForwardedRef<RefSelectProps>
  ): JSX.Element;
  displayName: string;
}

export const RoleSelect = React.forwardRef(_RoleSelect) as GenericRoleSelect;

RoleSelect.displayName = 'RoleSelect';
