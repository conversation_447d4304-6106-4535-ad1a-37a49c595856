import React from 'react';

import { useGetResources } from '@manyun/auth-hub.gql.client.resources';
import type { AuthResourceNodeType, Resource } from '@manyun/auth-hub.gql.client.resources';
import { Cascader } from '@manyun/base-ui.ui.cascader';
import type { CascaderProps, CascaderRef } from '@manyun/base-ui.ui.cascader';

function uniqueArrayObject(arr: Resource[]) {
  const map = new Map();
  return arr.reduce((acc: Resource[], item: Resource) => {
    if (!map.has(item.value)) {
      map.set(item.value, true);
      acc.push(item);
    }
    return acc;
  }, []);
}
export type ResourceCascaderProps = CascaderProps<Resource> & {
  roleCode: string;
  resourceType?: AuthResourceNodeType;
  resourceName?: string;
  fuzzyQuery?: boolean;
  onTreeDataChange?: (resources: Resource[]) => void;
};

export const ResourceCascader = React.forwardRef<CascaderRef, ResourceCascaderProps>(
  (
    {
      roleCode,
      resourceType,
      fuzzyQuery,
      resourceName,
      options: _options,
      onTreeDataChange,
      ...props
    },
    ref
  ) => {
    const [resourcesData, setResourcesData] = React.useState<Resource[]>([]);
    const [searchResourcesData, setSearchResourcesData] = React.useState<Resource[]>([]);

    const { data, refetch } = useGetResources(
      { roleCode, resourceType, resourceName },
      {
        onCompleted: data => {
          onTreeDataChange?.(data.authResources ?? []);
          if (!resourceName) {
            setResourcesData(data.authResources ?? []);
          }
          if (resourceName) {
            setSearchResourcesData(
              uniqueArrayObject(preChecklist.concat(data.authResources || []))
            );
          }
        },
      }
    );

    const preChecklist = React.useMemo(
      () => resourcesData.filter(i => props.value?.flat()?.includes(i.value)),
      [resourcesData, props.value]
    );

    const resources = React.useMemo(() => {
      if (fuzzyQuery) {
        if (searchResourcesData.length >= 1) {
          // 根据搜索结果展示
          return searchResourcesData;
        }

        if (resourceType === 'PROJECT_COMPANY') {
          // 初始化默认展示全部
          return [
            {
              displayText: '全部',
              label: '全部',
              parentValue: 'root',
              type: 'PROJECT_COMPANY' as AuthResourceNodeType.ProjectCompany,
              value: '_PROJECT_COMPANY_ALL_',
              children: null,
            },
          ];
        }
        if (resourceType === 'CUSTOMER') {
          // 初始化默认不展示
          return [];
        }
      }
      // 默认展示所有
      return resourcesData || [];
    }, [resourcesData, searchResourcesData, fuzzyQuery, resourceType]);
    React.useEffect(() => {
      if (!!!resourceName?.length && !!!props.value?.flat().length) {
        setSearchResourcesData([]);
      }
    }, [resourceName, props.value]);

    React.useEffect(() => {
      refetch({ roleCode, resourceType });
    }, [refetch, resourceType, roleCode]);

    React.useEffect(() => {
      if (resourceName) {
        refetch({ roleCode, resourceType, resourceName });
      }
    }, [refetch, resourceType, roleCode, resourceName]);

    return (
      <Cascader<Resource>
        ref={ref}
        options={resources}
        {...props}
        onChange={(_, values) => {
          props.onChange?.(_, values);
        }}
      />
    );
  }
);

ResourceCascader.displayName = 'ResourceCascader';

export default ResourceCascader;
