import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Form } from '@manyun/base-ui.ui.form';

import { registerMocks } from '@manyun/auth-hub.hook.use-users';
import { getMock } from '@manyun/service.request';

import { UserSelect } from './user-select';

export const BasicUserSelect = () => {
  const [init, setInit] = React.useState(false);
  const [form] = Form.useForm();

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerMocks(mock);
    setInit(true);
  });

  if (!init) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <Form form={form} onValuesChange={console.log}>
        <Form.Item label="用户" name="ids">
          <UserSelect style={{ width: 200 }} showSystem={true} mode="multiple" />
        </Form.Item>
      </Form>
    </ConfigProvider>
  );
};
