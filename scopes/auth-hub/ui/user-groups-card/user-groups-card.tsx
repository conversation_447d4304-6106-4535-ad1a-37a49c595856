import classNames from 'classnames';
import dayjs from 'dayjs';
import groupBy from 'lodash.groupby';
import orderBy from 'lodash.orderby';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { useSelector } from 'react-redux';

import type { AuthorizationRecordJSON } from '@manyun/auth-hub.model.authorization-record';
import { fetchAuthorizationRecordList } from '@manyun/auth-hub.service.fetch-authorization-record-list';
import { selectMe } from '@manyun/auth-hub.state.user';
import { AuthorizationRecordTable } from '@manyun/auth-hub.ui.authorization-record-table';
import { RoleColorful } from '@manyun/base-ui.icons';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Card } from '@manyun/base-ui.ui.card';
import { Empty } from '@manyun/base-ui.ui.empty';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { qs } from '@manyun/base-ui.util.query-string';
import { AUTH_REQUEST_CREATOR_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';
import { Link } from '@manyun/dc-brain.navigation.link';

import styles from './user-groups-card.module.less';

const colors = [
  '#016EFF',
  '#21B1FF',
  '#5FE7F9',
  '#00DDD3',
  '#5CCC00',
  '#7BE498',
  '#FDE400',
  '#FFB707',
  '#FF835C',
  '#FB5E83',
  '#E560E8',
  '#A247FD',
  '#564FE4',
  '#F65855',
];
const SPLIT_ROLE_KEY_SYMBOL = '_$$_';

export type UserGroupsCardProps = {
  style?: React.CSSProperties;
  className?: string;
  userId: number;
  /**
   * 是否可点击icon展示弹窗
   */
  showModal?: boolean;
};

export function UserGroupsCard({
  style,
  className,
  userId,
  showModal = false,
}: UserGroupsCardProps) {
  const [loading, setLoading] = useState(false);
  const [recordsMapper, setRecordsMapper] = useState<Record<string, AuthorizationRecordJSON[]>>({});
  const [roleAuthorizationRecords, setRoleAuthorizationRecords] = useState<
    AuthorizationRecordJSON[]
  >([]);

  const [open, setOpen] = useState(false);
  const [selectedKey, setSelectedKey] = useState<string | null>(null);

  const { userId: loginUserId } = useSelector(
    selectMe,
    (left, right) => left.userId === right.userId
  );

  useEffect(() => {
    (async () => {
      if (typeof userId !== 'number') {
        return;
      }
      setLoading(true);
      const { error, data } = await fetchAuthorizationRecordList({ userId });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      //不展示失效角色
      const validData = data.data.filter(record => record.expiredAt >= new Date().valueOf());
      setRecordsMapper(
        groupBy(validData, item => `${item.roleCode}${SPLIT_ROLE_KEY_SYMBOL}${item.roleName}`)
      );
    })();
  }, [userId]);

  const isLoginUser = useMemo(() => {
    return loginUserId === userId;
  }, [loginUserId, userId]);

  const getRoleNameByKey = useCallback((key: string) => {
    return key.split(SPLIT_ROLE_KEY_SYMBOL)[1];
  }, []);

  const getRoleExpireDay = useCallback(
    (key: string) => {
      if (!isLoginUser) {
        return 0;
      }
      const records = recordsMapper[key];
      const sortedRecords = orderBy(records, 'expiredAt', 'desc');
      const maxExpiredAt = sortedRecords[0].expiredAt;
      const expireDay = Math.ceil(dayjs(maxExpiredAt).diff(dayjs(), 'day', true));
      return expireDay <= 7 ? `剩${expireDay}天` : 0;
    },
    [isLoginUser, recordsMapper]
  );

  return (
    // eslint-disable-next-line react/jsx-filename-extension
    <>
      <Card
        style={style}
        className={classNames(className, styles.card)}
        bordered={false}
        loading={loading}
      >
        <Typography.Title style={{ marginBottom: '16px' }} level={5} showBadge>
          授权角色
        </Typography.Title>
        {Object.keys(recordsMapper).length === 0 && <Empty description="暂无数据" />}
        <Row gutter={[16, 16]}>
          {Object.keys(recordsMapper).map((key, index) => (
            <Col key={key} span={8}>
              <Space className={styles.group} direction="vertical" align="center">
                <Badge
                  size="small"
                  count={getRoleExpireDay(key)}
                  color="var(--manyun-warning-color)"
                  offset={[12, 4]}
                >
                  <RoleColorful
                    style={{
                      fontSize: '40px',
                      color: colors[index % 10],
                      cursor: !isLoginUser || !showModal ? 'auto' : 'pointer',
                    }}
                    onClick={async () => {
                      if (!isLoginUser || !showModal) {
                        return;
                      }

                      setOpen(true);
                      setSelectedKey(key);
                      const { error, data } = await fetchAuthorizationRecordList({
                        userId,
                        roleCode: key.split(SPLIT_ROLE_KEY_SYMBOL)[0],
                      });
                      if (error) {
                        message.error(error.message);
                        setRoleAuthorizationRecords([]);
                        return;
                      }
                      //不展示失效角色
                      setRoleAuthorizationRecords(
                        data.data.filter(record => record.expiredAt >= new Date().valueOf())
                      );
                    }}
                  />
                </Badge>
                <Typography.Text ellipsis={{ tooltip: getRoleNameByKey(key) }}>
                  {getRoleNameByKey(key)}
                </Typography.Text>
              </Space>
            </Col>
          ))}
        </Row>
      </Card>
      <Modal
        title={selectedKey ? getRoleNameByKey(selectedKey) : '--'}
        width="85%"
        bodyStyle={{ maxHeight: 'calc(80vh - 55px)', overflowY: 'auto' }}
        centered
        open={open}
        footer={null}
        onCancel={() => {
          setOpen(false);
        }}
      >
        <AuthorizationRecordTable
          size="small"
          rowKey="id"
          pagination={false}
          dataSource={roleAuthorizationRecords}
          showColumns={['resourceList', 'expiredAt']}
          operationColumn={{
            title: '操作',
            width: 80,
            render: (_, record) => [
              <Link
                key={record.id}
                href={`${AUTH_REQUEST_CREATOR_ROUTE_PATH}?${qs.stringify({
                  roleCode: record.roleCode,
                  roleName: record.roleName,
                  resourceType: record.resourceType,
                })}`}
              >
                续期
              </Link>,
            ],
          }}
        />
      </Modal>
    </>
  );
}
