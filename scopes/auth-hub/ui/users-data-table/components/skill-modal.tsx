import React, { useState } from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';

import { fetchUserSkills } from '@manyun/knowledge-hub.service.dcexam.fetch-user-skills';
import type { BackendUserSkill } from '@manyun/knowledge-hub.service.dcexam.fetch-user-skills';
import { Skill as SkillCard } from '@manyun/knowledge-hub.ui.skill';

export type SkillModalProps = {
  userId: number;
  professionalCertNum: number | undefined;
};

export const SkillModal = ({ userId, professionalCertNum }: SkillModalProps) => {
  const [visible, setVisible] = useState(false);
  const [skillsData, setSkillsData] = useState<BackendUserSkill[]>();

  const fetchSkillList = async () => {
    const { error, data } = await fetchUserSkills({ userId, own: true });

    if (error) {
      message.error(error.message);
      return;
    }
    setSkillsData(data.data);
  };

  const showModal = () => {
    setVisible(true);
    fetchSkillList();
  };

  const handleOk = () => {
    setVisible(false);
  };
  const handleCancel = () => {
    setVisible(false);
  };
  return (
    <>
      <Button type="link" disabled={!professionalCertNum} onClick={showModal}>
        {professionalCertNum ? professionalCertNum : 0}
      </Button>
      <Modal
        title="技能认证"
        width={950}
        bodyStyle={{ maxHeight: 'calc(80vh - 109px)', overflowY: 'auto' }}
        open={visible}
        footer={null}
        onOk={handleOk}
        onCancel={handleCancel}
      >
        <Space size={16} wrap>
          {Array.isArray(skillsData) &&
            skillsData.length > 0 &&
            skillsData.map(item => (
              <SkillCard
                key={item.id}
                style={{ height: 172, width: 290 }}
                id={item.id}
                userSkillsData={item}
                type="userManagement"
              />
            ))}
        </Space>
      </Modal>
    </>
  );
};
