import React, { useEffect, useState } from 'react';

import { TreeSelect, TreeSelectProps } from '@manyun/base-ui.ui.tree-select';
import { generateTreeData } from '@manyun/base-ui.util.generate-tree-data';
import type { GenerateTreeDataOptions } from '@manyun/base-ui.util.generate-tree-data';

import { fetchDeptList } from '@manyun/auth-hub.service.fetch-dept-list';
import type { DeptInfo } from '@manyun/auth-hub.service.fetch-dept-list';

type RefProps = {
  focus: () => void;
  blur: () => void;
  scrollTo: () => void;
};

export type DeptTreeSelectProps = TreeSelectProps<DeptInfo[]>;

type DeptTreeNode = DeptInfo & {
  label: string;
  children: DeptTreeNode[] | undefined;
};

export const DeptTreeSelect = React.forwardRef(
  (
    { placeholder = '请选择部门', ...otherProps }: DeptTreeSelectProps,
    ref: React.Ref<RefProps>
  ) => {
    const [treeData, setTreeData] = useState<DeptTreeNode[]>([]);

    useEffect(() => {
      fetchDeptList().then(({ data }) => setTreeData(arrayToTreeData(data.data)));
    }, []);

    return (
      <TreeSelect
        ref={ref}
        style={{ width: '100%' }}
        dropdownStyle={{ maxHeight: 400, overflow: 'auto' }}
        placeholder={placeholder}
        allowClear
        {...otherProps}
        treeData={treeData}
      />
    );
  }
);

function arrayToTreeData(arr: DeptInfo[]) {
  const options: GenerateTreeDataOptions<DeptTreeNode, DeptInfo> = {
    key: 'deptId',
    parentKey: 'parentId',
    isRootNode(node) {
      return node.parentId === '0';
    },
    getNode(node, children) {
      return {
        ...node,
        label: node.nameZh,
        value: node.deptId,
        children: children || undefined,
      };
    },
  };
  return generateTreeData<DeptTreeNode, DeptInfo>(arr, options);
}

DeptTreeSelect.displayName = 'DeptTreeSelect';
