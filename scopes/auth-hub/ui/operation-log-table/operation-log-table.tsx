import React, { useContext, useEffect, useMemo, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import { useLatest } from 'react-use';

import { TargetTypeCode, getOperationLogLocales } from '@manyun/auth-hub.model.operation-log';
import type { OperationLogJSON, TargetType } from '@manyun/auth-hub.model.operation-log';
import type { SvcQuery } from '@manyun/auth-hub.service.fetch-operation-log-list';
import { fetchOperationLogList } from '@manyun/auth-hub.service.fetch-operation-log-list';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { ExportOutlined } from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { FilePreviewWithContainer } from '@manyun/base-ui.ui.file-preview';
import { message } from '@manyun/base-ui.ui.message';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { reactNodeToText } from '@manyun/base-ui.util.react-node-to-text';
import { saveJSONAsXLSX } from '@manyun/base-ui.util.xlsx';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { generateSpaceOrDeviceRoutePath } from '@manyun/monitoring.route.monitoring-routes';
import {
  generateBorrowAndReturnDetailLocation,
  generateCabinetListRoutePath,
  generateDeviceListUrl,
} from '@manyun/resource-hub.route.resource-routes';
import { SpaceOrDeviceLink } from '@manyun/resource-hub.ui.space-or-device-link';
import {
  generateEventDetailRoutePath,
  generateEvnetLocation,
  generateInspectionConfigDetailRoutePath,
  generateMaintainConfigDetailRoutePath,
  generateRiskRegisterDetailLocation,
  generateTicketLocation,
} from '@manyun/ticket.route.ticket-routes';

import { OperationLogTableContext } from './operation-log-table-context';
import styles from './operation-log-table.module.less';

/** 可由外部控制是否显示的表格 column dataIndex */
export type ColumnDataIndex =
  | 'id'
  | 'serialNumber'
  | 'modifyType'
  | 'targetType'
  | 'operationCount';

export type OperationLogTableProps = {
  showColumns?: ColumnDataIndex[];
  isTargetIdEqual?: (targetId: string) => boolean;
  defaultSearchParams?: Omit<SvcQuery, 'page' | 'pageSize'>;
  showExportButton?: boolean;
};

/** reactNodeToText custom resolvers */
const resolvers = new Map([
  [UserLink, ({ name }: { name: string }) => (name === 'SYSTEM' ? '系统' : name)],
]);

export function OperationLogTable(props: OperationLogTableProps) {
  const {
    showColumns = [],
    isTargetIdEqual,
    defaultSearchParams,
    showExportButton = false,
  } = props;

  // 表格分页相关
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
  const paginationConfig: TableProps<OperationLogJSON[]>['pagination'] = {
    total: pagination.total,
    current: pagination.page,
    pageSize: pagination.pageSize,
    onChange: (page, pageSize) => {
      setPagination({
        ...pagination,
        page,
        pageSize,
      });
    },
  };
  const containerRef = useRef<HTMLDivElement>(null);

  const locales = getOperationLogLocales();
  const columns: Array<ColumnType<OperationLogJSON>> = useMemo(() => {
    const defaultList: Array<ColumnType<OperationLogJSON>> = [
      {
        title: locales['target']['content'],
        dataIndex: 'operationContent',
        ellipsis: true,
        className: 'max-width-content',
        render: (_, { operation, target }) => {
          const content = operation.content;
          const targetContent = target.content;
          const targetType = target.type;
          if (!content) {
            return '';
          }
          if (!targetContent) {
            return (
              <Popover
                overlayInnerStyle={{ wordWrap: 'break-word' }}
                content={content}
                getPopupContainer={() => containerRef?.current as HTMLElement}
              >
                <Typography.Text ellipsis={{ tooltip: false }}>{content}</Typography.Text>
              </Popover>
            );
          }
          const targetList = JSON.parse(targetContent);
          const listRegex = /\[\d+\]/g;
          const numberRegex = /\d+/g;
          const text: string = content.join(';');
          const mainText = text.split(listRegex);
          if (!text.match(listRegex)?.length) {
            return (
              <Popover
                overlayInnerStyle={{ wordWrap: 'break-word' }}
                content={content}
                getPopupContainer={() => containerRef?.current as HTMLElement}
              >
                <Typography.Text ellipsis={{ tooltip: false }}>{content}</Typography.Text>
              </Popover>
            );
          }
          const numberList = text.match(listRegex)!.join().match(numberRegex)!;
          const _dom = numberList.map((item, index) => {
            const name = targetList[parseInt(item)]?.targetName;
            const id = targetList[parseInt(item)]?.targetId;
            const typeCode = targetList[parseInt(item)]?.targetType;
            if (index === numberList.length - 1) {
              return (
                <span key={id}>
                  {mainText[index]}
                  <SwitchLinkToUrl
                    targetName={name}
                    targetId={id}
                    targetType={targetType}
                    targetTypeCode={typeCode}
                    isTargetIdEqual={isTargetIdEqual}
                  />
                  {mainText[index + 1]}
                </span>
              );
            }
            return (
              <span key={id}>
                {mainText[index]}
                <SwitchLinkToUrl
                  targetName={name}
                  targetId={id}
                  targetType={targetType}
                  targetTypeCode={typeCode}
                  isTargetIdEqual={isTargetIdEqual}
                />
              </span>
            );
          });
          return (
            <Popover
              overlayInnerStyle={{ wordWrap: 'break-word' }}
              content={_dom}
              getPopupContainer={() => containerRef?.current as HTMLElement}
            >
              <Typography.Text ellipsis={{ tooltip: false }}>{_dom}</Typography.Text>
            </Popover>
          );
        },
      },
      {
        title: locales['operation']['notes'],
        dataIndex: 'operationNotes',
        ellipsis: true,
        className: 'max-width-notes',
        render: (_, record) => (
          <Popover
            content={record.operation.notes}
            getPopupContainer={() => containerRef?.current as HTMLElement}
          >
            <Typography.Text ellipsis={{ tooltip: false }}>
              {record.operation.notes}
            </Typography.Text>
          </Popover>
        ),
      },
      {
        title: locales['modifyBy']['name'],
        dataIndex: 'modifyByName',
        render: (_, record) => record.modifyBy.name,
      },
      {
        title: locales['operation']['time'],
        dataIndex: 'operationTime',
        fixed: 'right',
        render: (_, record) => record.operation.time,
      },
    ];

    const list: Array<ColumnType<OperationLogJSON>> = (
      [
        {
          title: locales['id'],
          dataIndex: 'id',
          ellipsis: true,
        },
        {
          title: locales['serialNumber'],
          dataIndex: 'serialNumber',
          render: (_value, _record, index) =>
            serialNumberInReverse({
              total: pagination.total,
              page: pagination.page,
              pageSize: pagination.pageSize,
              index,
            }),
        },
        {
          title: locales['modifyType'],
          dataIndex: 'modifyType',
          render: (_, record) => record.modifyType.name,
        },
        {
          title: locales['target']['type'],
          dataIndex: 'targetType',
          render: (_, record) => record.target.type.name,
        },
        {
          title: locales['operation']['count'],
          dataIndex: 'operationCount',
          render: (_, record) => record.operation.count,
        },
      ] as Array<ColumnType<OperationLogJSON>>
    ).filter(item => showColumns.includes(item.dataIndex as ColumnDataIndex));

    return [...list, ...defaultList];
  }, [
    isTargetIdEqual,
    locales,
    pagination.page,
    pagination.pageSize,
    pagination.total,
    showColumns,
  ]);

  const [list, setList] = useState<OperationLogJSON[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultSearchParamsRef = useLatest(defaultSearchParams);
  const contextValue = useContext(OperationLogTableContext);
  useEffect(() => {
    setLoading(true);
    fetchOperationLogList({
      ...defaultSearchParamsRef.current,
      ...contextValue?.searchParams,
      page: pagination.page,
      pageSize: pagination.pageSize,
    }).then(({ data, error }) => {
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setList(data.data);
      setPagination({
        page: pagination.page,
        pageSize: pagination.pageSize,
        total: data.total,
      });
    });
  }, [contextValue?.searchParams, defaultSearchParamsRef, pagination.page, pagination.pageSize]);

  const [selectedRows, setSelectedRows] = useState<OperationLogJSON[]>([]);
  const handleExport = useMemo(
    () => () => {
      saveJSONAsXLSX(
        [
          {
            data: selectedRows,
            headers: columns.map(column => ({
              title: column.title as string,
              dataIndex: column.dataIndex as string,
              stringify: (text: unknown, record: OperationLogJSON, index: number) =>
                column.render
                  ? reactNodeToText(column.render(text, record, index), resolvers)
                  : `${text}`,
            })),
          },
        ],
        '操作日志'
      );
    },
    [columns, selectedRows]
  );

  const rowSelection: TableProps<OperationLogJSON>['rowSelection'] = showExportButton
    ? {
        preserveSelectedRowKeys: true,
        onChange: (_, rows) => setSelectedRows(rows),
      }
    : undefined;
  return (
    <Space direction="vertical" style={{ width: '100%' }}>
      {showExportButton && (
        <Button
          icon={<ExportOutlined />}
          type="link"
          compact
          disabled={selectedRows.length === 0}
          style={{ float: 'right' }}
          onClick={handleExport}
        >
          导出
        </Button>
      )}
      <div ref={containerRef} className={styles.operationLogTableStyle}>
        <Table
          rowKey="id"
          columns={columns}
          scroll={{ x: 'max-content' }}
          dataSource={list}
          loading={loading}
          pagination={paginationConfig}
          rowSelection={rowSelection}
        />
      </div>
    </Space>
  );
}

/**
 * 序号 倒序
 */
function serialNumberInReverse({
  total,
  page,
  pageSize,
  index,
}: {
  total: number;
  page: number;
  pageSize: number;
  index: number;
}) {
  const number = total - ((page - 1) * pageSize + index);
  return number;
}

type SwitchLinkToUrlProps = {
  targetName: string;
  targetId: string;
  targetTypeCode: TargetTypeCode;
  targetType: TargetType;
  isTargetIdEqual?: (targetId: string) => boolean;
};

function SwitchLinkToUrl(props: SwitchLinkToUrlProps) {
  const { targetName, targetId, targetTypeCode, targetType, isTargetIdEqual } = props;
  // 若本身就在对应 targetId 对应的详情页，则无需展示
  if (typeof isTargetIdEqual === 'function' && isTargetIdEqual(targetId)) {
    return null;
  }
  switch (targetTypeCode) {
    case TargetTypeCode.Device:
      if (targetType.code === TargetTypeCode.Borrow) {
        return <SpaceOrDeviceLink id={targetId} text={targetName} type="DEVICE_ASSET_NO" />;
      }
      return (
        <Link
          to={generateSpaceOrDeviceRoutePath({
            guid: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.RiskRegister:
      return (
        <Link
          to={generateRiskRegisterDetailLocation({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.Event:
      return (
        <Link
          to={generateEventDetailRoutePath({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.Grid:
      return (
        <Link
          to={generateCabinetListRoutePath({
            guid: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.Person:
      return <UserLink id={Number(targetId)} name={targetName} />;
    case TargetTypeCode.Inspection:
    case TargetTypeCode.Inventory:
    case TargetTypeCode.Maintenance:
    case TargetTypeCode.Power:
    case TargetTypeCode.Repair:
    case TargetTypeCode.Warehouse:
    case TargetTypeCode.Access:
    case TargetTypeCode.OnOff:
    case TargetTypeCode.Alter:
    case TargetTypeCode.Supply:
    case TargetTypeCode.Accept:
    case TargetTypeCode.AccessCardAuth:
      return (
        <Link
          to={generateTicketLocation({
            ticketType: targetTypeCode.toLowerCase(),
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.File:
      return (
        <FilePreviewWithContainer
          file={{
            ext: '.' + targetName?.split('.')?.[1],
            name: targetName || targetId,
            src: McUploadFile.generateSrc(targetId, targetName),
          }}
        >
          <Button type="link" compact>
            {targetName || targetId}
          </Button>
        </FilePreviewWithContainer>
      );
    case TargetTypeCode.InspectionConfig:
      return (
        <Link
          to={generateInspectionConfigDetailRoutePath({
            name: targetName,
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.Import:
      return targetType.code === 'DEVICE' ? (
        <Link
          to={generateDeviceListUrl({
            insertVersion: targetId,
          })}
        >
          {targetName}
        </Link>
      ) : (
        <Link
          to={generateCabinetListRoutePath({
            insertVersion: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.MaintenanceConfig:
      return (
        <Link
          to={generateMaintainConfigDetailRoutePath({
            id: targetId,
            name: targetName,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.Borrow:
      return (
        <Link
          to={generateBorrowAndReturnDetailLocation({
            id: targetId,
          })}
        >
          {targetName}
        </Link>
      );
    case TargetTypeCode.Approval:
      return (
        <Link
          to={generateBPMRoutePath({
            id: targetId,
          })}
        >
          {targetId}
        </Link>
      );
    case TargetTypeCode.NewEvent:
      return (
        <Link
          to={generateEvnetLocation({
            id: targetId,
          })}
        >
          {targetId}
        </Link>
      );
    default:
      return <>{targetName}</>;
  }
}
