import { createContext } from 'react';

import { SvcQuery } from '@manyun/auth-hub.service.fetch-operation-log-list';

type SearchParams = Omit<SvcQuery, 'page' | 'pageSize'>;

export type OperationLogTableContextValue = {
  searchParams: SearchParams;
  setSearchParams: (searchParams: SearchParams) => void;
} | null;

export const OperationLogTableContext = createContext<OperationLogTableContextValue>(null);
