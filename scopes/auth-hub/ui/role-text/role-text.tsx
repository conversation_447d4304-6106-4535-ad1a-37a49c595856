import React from 'react';

import { useRoles } from '@manyun/auth-hub.hook.use-roles';

export type RoleTextProps = {
  /**
   * 角色code
   */
  code?: string | number;
  id?: number;
};

export function RoleText({ code, id }: RoleTextProps) {
  const [{ entities, keyMapper }, getRoles] = useRoles();
  let _code = code;
  if (!Object.keys(entities).length) {
    getRoles();
  }
  // 按照原先code是必填的props，怎么可能能进入这个if判断
  if (id && !code) {
    _code = keyMapper[id];
  }
  const name = _code ? (entities[_code] ? entities[_code].name : '未知') : '未知';

  return <>{name}</>;
}
