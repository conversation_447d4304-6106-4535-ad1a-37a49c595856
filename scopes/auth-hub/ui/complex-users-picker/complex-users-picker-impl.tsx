import React from 'react';

import { useComplexUsersPicker } from './complex-users-picker-context';
import { ComplexUsersPickerModal, ComplexUsersPickerTabs } from './complex-users-picker-modal';

export type ClickableChildren = React.ReactElement<{
  loading?: boolean;
  onClick: Function;
  [x: string]: any;
}>;

export type ComplexUsersPickerImplProps = {
  modalTitle?: React.ReactNode;
  children?: ClickableChildren;
};

export function ComplexUsersPickerImpl({ modalTitle, children }: ComplexUsersPickerImplProps) {
  const [{ showModal }, { setVisible }] = useComplexUsersPicker();
  const [loading, setLoading] = React.useState(false);

  return showModal && children ? (
    <>
      <ComplexUsersPickerModal title={modalTitle} />
      {React.cloneElement(children, {
        loading,
        onClick: async (...args: any[]) => {
          let canOpen = true;
          if (children.props.onClick) {
            setLoading(true);
            const result = await children.props.onClick(...args);
            setLoading(false);
            if (typeof result == 'boolean') {
              canOpen = result;
            }
          }
          if (canOpen) {
            setVisible(v => !v);
          }
        },
      })}
    </>
  ) : (
    <ComplexUsersPickerTabs />
  );
}
