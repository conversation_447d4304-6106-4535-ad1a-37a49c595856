import React from 'react';

import { Badge } from '@manyun/base-ui.ui.badge';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { useRoles } from '@manyun/auth-hub.hook.use-roles';
import type { OnCompletedCallback } from '@manyun/auth-hub.hook.use-roles';
import { Role } from '@manyun/auth-hub.model.role';

import { AddButton, RemoveButton } from './complex-users-picker-action-button';
import { useComplexUsersPicker } from './complex-users-picker-context';
import type { RoleEntities } from './complex-users-picker-context';
import { ComplexUsersPickerTable } from './complex-users-picker-table';

const rolesColumns: ColumnsType<Role> = [
  {
    title: '角色名称',
    dataIndex: 'name',
  },
  {
    title: '角色 ID',
    dataIndex: 'code',
  },
];

export function ComplexUsersPickerRoles({ defaultActiveKey }: { defaultActiveKey?: string }) {
  const [
    {
      visible,
      selectedRoleKeys,
      disabledRoleKeys,
      roleEntities,
      selectedRolesTableExtraColumns,
      showModal,
    },
    { setSelectedRoleKeys, setRoleEntities, onSelectedRolesChange, onChange },
  ] = useComplexUsersPicker();
  const [rolePagination, setPagination] = React.useState<{
    page: number;
    pageSize: number;
  }>({ page: 1, pageSize: 10 });
  const [selectedRolesPagination, setSelectedRolesPagination] = React.useState<{
    page: number;
    pageSize: number;
  }>({ page: 1, pageSize: 10 });
  const onGetRolesCompleted = React.useCallback<OnCompletedCallback>(
    (error, data) => {
      if (error) {
        return;
      }
      const { entities } = data!.data.reduce<{ entities: RoleEntities; result: number[] }>(
        (acc, role) => {
          const id = role.id!;
          acc.entities[id] = role;
          acc.result.push(id);

          return acc;
        },
        { entities: [], result: [] }
      );
      setPagination(prevRolePagination => ({ ...prevRolePagination, total: data!.total }));
      setRoleEntities(prevRoleEntities => ({ ...prevRoleEntities, ...entities }));
    },
    [setRoleEntities]
  );
  const [roles, getRoles] = useRoles({
    onCompleted: onGetRolesCompleted,
  });

  React.useEffect(() => {
    if (visible || !showModal) {
      getRoles();
    }
  }, [getRoles, visible, showModal]);

  const cadidates =
    roles.data?.data.filter(
      ({ id }) => !selectedRoleKeys.includes(id!) && !disabledRoleKeys.includes(id!)
    ) || [];
  const selectedRoles = selectedRoleKeys.map(
    id => roleEntities[id] || new Role(id, '未知', '未知', null, null, null, null, null)
  );

  const selectedRoleKeysChangeHandler = React.useCallback(
    (selected: boolean, keys: number[]) => {
      let roleKeys: number[] = [];
      setSelectedRoleKeys(prevKeys => {
        roleKeys = selected ? [...prevKeys, ...keys] : prevKeys.filter(k => !keys.includes(k));

        return roleKeys;
      });
      if (onSelectedRolesChange) {
        onSelectedRolesChange(selected, keys, roleEntities);
      }
      if (onChange) {
        onChange(roleKeys, { selected, keys, variant: 'role' });
      }
    },
    [setSelectedRoleKeys, onSelectedRolesChange, onChange, roleEntities]
  );

  return (
    <Tabs defaultActiveKey={defaultActiveKey}>
      <Tabs.TabPane key="roles" tab="未选">
        <ComplexUsersPickerTable
          rowKey="id"
          loading={roles.loading}
          columns={rolesColumns}
          data={cadidates}
          pagination={{
            total: cadidates.length,
            current: rolePagination.page,
            pageSize: rolePagination.pageSize,
          }}
          selectedRowKeys={selectedRoleKeys}
          renderActionButton={(selected, record) => {
            // rendering `Column Title`
            if (selected === undefined) {
              return (
                <AddButton
                  disabled={
                    !cadidates
                      .slice(
                        (rolePagination.page - 1) * rolePagination.pageSize,
                        rolePagination.page * rolePagination.pageSize
                      )
                      .some(record => !selectedRoleKeys.includes(record.id!))
                  }
                  onClick={() => {
                    const currentPageData = cadidates.slice(
                      (rolePagination.page - 1) * rolePagination.pageSize,
                      rolePagination.page * rolePagination.pageSize
                    );
                    const currentPageRowKeys: number[] = [];
                    currentPageData.forEach(record => {
                      if (!selectedRoleKeys.includes(record.id!)) {
                        currentPageRowKeys.push(record.id!);
                      }
                    });
                    selectedRoleKeysChangeHandler(true, currentPageRowKeys);
                    // To fix pagination will update on next render.
                    // Here we just update it manually in order to
                    // calculate the correct current page data.
                    setPagination(prev => ({
                      ...prev,
                      page: prev.page > 1 ? prev.page - 1 : 1,
                    }));
                  }}
                />
              );
            }

            return (
              <AddButton
                disabled={selected}
                onClick={() => {
                  const currentRoleId = record!.id!;
                  selectedRoleKeysChangeHandler(true, [currentRoleId]);
                  const currentPageData = cadidates.slice(
                    (rolePagination.page - 1) * rolePagination.pageSize,
                    rolePagination.page * rolePagination.pageSize
                  );
                  const rolesLeft = currentPageData.filter(role => role.id !== currentRoleId);
                  if (rolesLeft.length === 0) {
                    setPagination(prev => ({
                      ...prev,
                      page: prev.page > 1 ? prev.page - 1 : 1,
                    }));
                  }
                }}
              />
            );
          }}
          onChange={pagination => {
            setPagination(prevRolePagination => ({
              ...prevRolePagination,
              page: pagination.current!,
              pageSize: pagination.pageSize!,
            }));
          }}
        />
      </Tabs.TabPane>
      <Tabs.TabPane
        key="selected-roles"
        tab={
          <Badge size="small" offset={[16, 0]} count={selectedRoleKeys.length}>
            <span>已选</span>
          </Badge>
        }
      >
        <ComplexUsersPickerTable
          rowKey="id"
          loading={false}
          columns={[...rolesColumns, ...(selectedRolesTableExtraColumns || [])]}
          data={selectedRoles}
          pagination={{
            total: selectedRoleKeys.length,
            current: selectedRolesPagination.page,
            pageSize: selectedRolesPagination.pageSize,
          }}
          selectedRowKeys={[]}
          renderActionButton={(selected, record) => {
            // rendering `Column Title`
            if (selected === undefined) {
              return (
                <RemoveButton
                  disabled={selectedRoleKeys.length <= 0}
                  onClick={() => {
                    const currentPageRoles = selectedRoles.slice(
                      (selectedRolesPagination.page - 1) * selectedRolesPagination.pageSize,
                      selectedRolesPagination.page * selectedRolesPagination.pageSize
                    );
                    const currentPageRoleIds = currentPageRoles.map(role => role.id!);
                    setSelectedRolesPagination(prev => ({
                      ...prev,
                      page: prev.page > 1 ? prev.page - 1 : 1,
                    }));
                    selectedRoleKeysChangeHandler(false, currentPageRoleIds);
                  }}
                />
              );
            }

            return (
              <RemoveButton
                disabled={false}
                onClick={() => {
                  const currentRoleId = record!.id!;
                  const currentPageData = selectedRoles.slice(
                    (selectedRolesPagination.page - 1) * selectedRolesPagination.pageSize,
                    selectedRolesPagination.page * selectedRolesPagination.pageSize
                  );
                  const rolesLeft = currentPageData.filter(role => role.id !== currentRoleId);
                  if (rolesLeft.length === 0) {
                    setSelectedRolesPagination(prev => ({
                      ...prev,
                      page: prev.page > 1 ? prev.page - 1 : 1,
                    }));
                  }
                  selectedRoleKeysChangeHandler(false, [currentRoleId]);
                }}
              />
            );
          }}
          onChange={pagination => {
            setSelectedRolesPagination({
              page: pagination.current!,
              pageSize: pagination.pageSize!,
            });
          }}
        />
      </Tabs.TabPane>
    </Tabs>
  );
}
