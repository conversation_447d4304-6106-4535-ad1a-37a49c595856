import React from 'react';

import MinusCircleOutlined from '@ant-design/icons/es/icons/MinusCircleOutlined';
import PlusCircleOutlined from '@ant-design/icons/es/icons/PlusCircleOutlined';

import { Button } from '@manyun/base-ui.ui.button';
import type { ButtonProps } from '@manyun/base-ui.ui.button';

export type ActionButtonProps = Required<Pick<ButtonProps, 'disabled' | 'onClick'>>;

export function AddButton({ disabled, onClick }: ActionButtonProps) {
  return (
    <Button
      size="small"
      type="text"
      icon={<PlusCircleOutlined />}
      disabled={disabled}
      onClick={onClick}
    />
  );
}

export function RemoveButton({ disabled, onClick }: ActionButtonProps) {
  return (
    <Button
      size="small"
      type="text"
      icon={<MinusCircleOutlined />}
      disabled={disabled}
      onClick={onClick}
    />
  );
}
