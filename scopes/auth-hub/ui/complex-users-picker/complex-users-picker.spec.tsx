import React from 'react';

import { render } from '@testing-library/react';

import { useRemoteMock } from '@manyun/service.request';

import { BasicComplexUsersPicker } from './complex-users-picker.composition';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should render with the correct text', async () => {
  const { getByText } = render(<BasicComplexUsersPicker />);
  const rendered = getByText('CLICK ME');
  expect(rendered).toBeTruthy();
});
