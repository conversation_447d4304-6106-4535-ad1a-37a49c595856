import React from 'react';

import type { Role } from '@manyun/auth-hub.model.role';
import type { UserJSON } from '@manyun/auth-hub.model.user';
import type { GroupModel } from '@manyun/auth-hub.service.fetch-paged-user-groups';
import type { ColumnsType } from '@manyun/base-ui.ui.table';

export type Value = {
  roleKeys?: number[];
  userKeys?: number[];
  userGroupKeys?: number[];
  deptKeys?: string[];
};

export type Callbacks = {
  onSelectedRolesChange(
    selected: boolean,
    selectedRoleKeys: number[],
    entities: RoleEntities
  ): void | Promise<boolean>;
  onSelectedUsersChange(
    selected: boolean,
    selectedUserKeys: number[],
    entities: UserEntities
  ): void | Promise<boolean>;
  onSelectedUserGroupsChange(
    selected: boolean,
    selectedUserGroupKeys: number[],
    entities: UserGroupEntities
  ): void | Promise<boolean>;
  onChange(
    value: Value,
    infos: {
      selected: boolean;
      keys: (number | string)[];
      variant: 'role' | 'user' | 'user-group' | 'dept';
    }
  ): void;
};

export type UsersRecordValue = UserJSON & {
  roles?: Role[];
  /**
   * 用户所关联的楼
   */
  resources?: {
    id: number;
    /**
     * BLOCK GUID
     */
    code: string;
  }[];
};

export type RoleEntities = Record<number, Role | undefined>;

export type UserEntities = Record<number, UsersRecordValue | undefined>;

export type UserGroupEntities = Record<number, GroupModel | undefined>;

export type ComplexUsersPickerContextConsumerProps = [
  {
    visible: boolean;
    selectedRoleKeys: number[];
    roleEntities: RoleEntities;
    disabledUserKeys: number[];
    disabledRoleKeys: number[];
    selectedUserKeys: number[];
    userEntities: UserEntities;
    selectedUserGroupKeys: number[];
    selectedDeptKeys: string[];
    userGroupEntities: UserGroupEntities;
    selectedRolesTableExtraColumns?: ColumnsType<any>;
    selectedUsersTableExtraColumns?: ColumnsType<any>;
    selectedUserGroupsTableExtraColumns?: ColumnsType<any>;
    showUserGroups?: boolean;
    showDepts?: boolean;
    showModal?: boolean;
    blockGuids?: string[];
  },
  {
    /** 重置 `defaultSelectedRoleKeys, defaultSelectedUserKeys` */
    reset(): void;
    setVisible: React.Dispatch<React.SetStateAction<boolean>>;
    setSelectedRoleKeys: React.Dispatch<React.SetStateAction<number[]>>;
    setSelectedUserKeys: React.Dispatch<React.SetStateAction<number[]>>;
    setSelectedUserGroupKeys: React.Dispatch<React.SetStateAction<number[]>>;
    setSelectedDeptKeys: React.Dispatch<React.SetStateAction<string[]>>;
    setRoleEntities: React.Dispatch<React.SetStateAction<RoleEntities>>;
    setUserEntities: React.Dispatch<React.SetStateAction<UserEntities>>;
    setUserGroupEntities: React.Dispatch<React.SetStateAction<UserGroupEntities>>;
    onSelectedRolesChange?: Callbacks['onSelectedRolesChange'];
    onSelectedUsersChange?: Callbacks['onSelectedUsersChange'];
    onSelectedUserGroupsChange?: Callbacks['onSelectedUserGroupsChange'];
    onChange?(
      allSelectedKeys: (number | string)[],
      infos: {
        selected: boolean;
        keys: (number | string)[];
        variant: 'role' | 'user' | 'user-group' | 'dept';
      }
    ): void;
  },
];

const noop = () => {};
const ComplexUsersPickerContext = React.createContext<ComplexUsersPickerContextConsumerProps>([
  {
    visible: false,
    selectedRoleKeys: [],
    roleEntities: {},
    disabledUserKeys: [],
    disabledRoleKeys: [],
    selectedUserKeys: [],
    userEntities: {},
    selectedUserGroupKeys: [],
    selectedDeptKeys: [],
    userGroupEntities: {},
    selectedRolesTableExtraColumns: [],
    selectedUsersTableExtraColumns: [],
    selectedUserGroupsTableExtraColumns: [],
  },
  {
    reset: noop,
    setVisible: noop,
    setSelectedRoleKeys: noop,
    setSelectedUserKeys: noop,
    setSelectedUserGroupKeys: noop,
    setSelectedDeptKeys: noop,
    setRoleEntities: noop,
    setUserEntities: noop,
    setUserGroupEntities: noop,
  },
]);
ComplexUsersPickerContext.displayName = 'ComplexUsersPickerContext';

export function useComplexUsersPicker() {
  return React.useContext(ComplexUsersPickerContext);
}

export type ComplexUsersPickerProviderProps = {
  children: React.ReactNode;
  defaultSelectedRoleKeys?: number[];
  defaultSelectedUserKeys?: number[];
  defaultSelectedUserGroupKeys?: number[];
  disabledUserKeys?: number[];
  disabledRoleKeys?: number[];
  value?: Value;
  selectedRolesTableExtraColumns?: ColumnsType<any>;
  selectedUsersTableExtraColumns?: ColumnsType<any>;
  selectedUserGroupsTableExtraColumns?: ColumnsType<any>;
  onSelectedRolesChange?: Callbacks['onSelectedRolesChange'];
  onSelectedUsersChange?: Callbacks['onSelectedUsersChange'];
  onSelectedUserGroupsChange?: Callbacks['onSelectedUserGroupsChange'];
  onChange?: Callbacks['onChange'];
  showUserGroups?: boolean;
  showDepts?: boolean;
  showModal?: boolean;
  blockGuids?: string[];
};

export function ComplexUsersPickerProvider({
  children,
  defaultSelectedRoleKeys = [],
  defaultSelectedUserKeys = [],
  defaultSelectedUserGroupKeys = [],
  disabledUserKeys = [],
  disabledRoleKeys = [],
  value,
  selectedRolesTableExtraColumns = [],
  selectedUsersTableExtraColumns = [],
  selectedUserGroupsTableExtraColumns = [],
  onSelectedRolesChange,
  onSelectedUsersChange,
  onSelectedUserGroupsChange,
  onChange,
  showUserGroups,
  showDepts,
  showModal = true,
  blockGuids = [],
}: ComplexUsersPickerProviderProps) {
  const [visible, setVisible] = React.useState(false);
  const [selectedRoleKeys, setSelectedRoleKeys] = React.useState<number[]>(
    value?.roleKeys || defaultSelectedRoleKeys || []
  );
  const [roleEntities, setRoleEntities] = React.useState<RoleEntities>({});
  const [selectedUserKeys, setSelectedUserKeys] = React.useState<number[]>(
    value?.userKeys || defaultSelectedUserKeys || []
  );
  const [userEntities, setUserEntities] = React.useState<UserEntities>({});

  const [selectedUserGroupKeys, setSelectedUserGroupKeys] = React.useState<number[]>(
    value?.userGroupKeys || defaultSelectedUserGroupKeys || []
  );
  const [userGroupEntities, setUserGroupEntities] = React.useState<UserGroupEntities>({});

  const [selectedDeptKeys, setSelectedDeptKeys] = React.useState<string[]>(value?.deptKeys || []);

  const defaultValuesRef = React.useRef({
    defaultSelectedRoleKeys,
    defaultSelectedUserKeys,
    defaultSelectedUserGroupKeys,
  });
  const reset = React.useCallback(() => {
    setSelectedRoleKeys(value?.roleKeys || defaultValuesRef.current.defaultSelectedRoleKeys);
    setSelectedUserKeys(value?.userKeys || defaultValuesRef.current.defaultSelectedUserKeys);
    setSelectedUserGroupKeys(
      value?.userGroupKeys || defaultValuesRef.current.defaultSelectedUserGroupKeys
    );
  }, [value?.roleKeys, value?.userGroupKeys, value?.userKeys]);
  const ctxOnChange = React.useCallback<
    Required<ComplexUsersPickerContextConsumerProps[1]>['onChange']
  >(
    (allSelectedKeys, infos) => {
      if (infos.variant === 'role') {
        onChange?.(
          {
            roleKeys: allSelectedKeys as number[],
            userKeys: selectedUserKeys,
            userGroupKeys: selectedUserGroupKeys,
            deptKeys: selectedDeptKeys,
          },
          infos
        );
      } else if (infos.variant === 'user') {
        onChange?.(
          {
            roleKeys: selectedRoleKeys,
            userKeys: allSelectedKeys as number[],
            userGroupKeys: selectedUserGroupKeys,
            deptKeys: selectedDeptKeys,
          },
          infos
        );
      } else if (infos.variant === 'user-group') {
        onChange?.(
          {
            userGroupKeys: allSelectedKeys as number[],
            userKeys: selectedUserKeys,
            roleKeys: selectedRoleKeys,
            deptKeys: selectedDeptKeys,
          },
          infos
        );
      } else if (infos.variant === 'dept') {
        onChange?.(
          {
            deptKeys: allSelectedKeys as string[],
            userKeys: selectedUserKeys,
            roleKeys: selectedRoleKeys,
            userGroupKeys: selectedUserGroupKeys,
          },
          infos
        );
      }
    },
    [onChange, selectedUserKeys, selectedUserGroupKeys, selectedRoleKeys, selectedDeptKeys]
  );

  React.useEffect(() => {
    // 这种情况的话，认为是非受控的
    if (value === undefined) {
      return;
    }
    if (value.roleKeys) {
      setSelectedRoleKeys(value.roleKeys);
    }
    if (value.userKeys) {
      setSelectedUserKeys(value.userKeys);
    }
    if (value.userGroupKeys) {
      setSelectedUserGroupKeys(value.userGroupKeys);
    }
    if (value.deptKeys) {
      setSelectedDeptKeys(value.deptKeys);
    }
  }, [value]);

  const ctxValue: ComplexUsersPickerContextConsumerProps = [
    {
      visible,
      selectedRoleKeys,
      roleEntities,
      disabledUserKeys,
      disabledRoleKeys,
      selectedUserKeys,
      userEntities,
      selectedUserGroupKeys,
      selectedDeptKeys,
      userGroupEntities,
      selectedRolesTableExtraColumns,
      selectedUsersTableExtraColumns,
      selectedUserGroupsTableExtraColumns,
      showUserGroups,
      showDepts,
      showModal,
      blockGuids,
    },
    {
      reset,
      setVisible,
      setSelectedRoleKeys,
      setRoleEntities,
      setSelectedUserGroupKeys,
      setSelectedDeptKeys,
      setUserGroupEntities,
      setSelectedUserKeys,
      setUserEntities,
      onSelectedRolesChange,
      onSelectedUsersChange,
      onSelectedUserGroupsChange,
      onChange: ctxOnChange,
    },
  ];

  return (
    <ComplexUsersPickerContext.Provider value={ctxValue}>
      {children}
    </ComplexUsersPickerContext.Provider>
  );
}
