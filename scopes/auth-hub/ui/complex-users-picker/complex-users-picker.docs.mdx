---
description: '一个复杂的角色、用户弹窗选择组件'
labels: ['react', 'modal', 'role', 'user', 'select']
---

## 概述

选择角色、用户需要进行实时保存，不提供批量保存的能力！

## 使用

```js
<ComplexUsersPicker
  defaultSelectedRoleKeys={[777]}
  selectedRolesTableExtraColumns={[
    {
      title: 'ID',
      dataIndex: 'id',
    },
  ]}
  defaultSelectedUserKeys={[17]}
  onSelectedRolesChange={(selected, selectedIds, entities) => {
    console.log(selected, selectedIds, entities);
  }}
  onSelectedUsersChange={(selected, selectedIds, entities) => {
    console.log(selected, selectedIds, entities);
  }}
>
  <Button type="primary">CLICK ME</Button>
</ComplexUsersPicker>
```

### 使用此组件依赖的 MOCK 数据

```js
import { registerRoleMocks, registerUserMocks } from '@manyun/auth-hub.ui.complex-users-picker';
import { getMock } from '@manyun/service.request';

const [initialized, update] = React.useState(false);

React.useEffect(() => {
  const mock = getMock('web', { delayResponse: 777 });
  registerRoleMocks(mock);
  registerUserMocks(mock);
  update(true);
}, []);

if (!initialized) {
  return <span>Loading...</span>;
}

// ...
```
