import React from 'react';
import { Link } from 'react-router-dom';

import { Badge } from '@manyun/base-ui.ui.badge';
import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { generateUserGroupRoutePath } from '@manyun/auth-hub.route.auth-routes';
import { fetchPagedUserGroups } from '@manyun/auth-hub.service.fetch-paged-user-groups';
import type {
  GroupModel,
  SvcRespData as UserGroupsData,
} from '@manyun/auth-hub.service.fetch-paged-user-groups';
import { IdcSelect } from '@manyun/resource-hub.ui.idc-select';

import { AddButton, RemoveButton } from './complex-users-picker-action-button';
import { UserGroupEntities, useComplexUsersPicker } from './complex-users-picker-context';
import { ComplexUsersPickerTable } from './complex-users-picker-table';

const userGroupsColumns: ColumnsType<GroupModel> = [
  {
    title: '用户组名称',
    dataIndex: 'groupName',
  },
  {
    width: 200,
    ellipsis: true,
    title: '所属位置',
    dataIndex: 'resourceCodes',
    render(codes?: string[]) {
      return codes?.join(' | ') || '--';
    },
  },
  {
    width: 200,
    title: '所属人员',
    dataIndex: 'users',
    render(_, record: GroupModel) {
      return (
        <Link to={generateUserGroupRoutePath({ id: record.id })} target="_blank">
          查看
        </Link>
      );
    },
  },
];

export function ComplexUsersPickerUserGroups({ defaultActiveKey }: { defaultActiveKey?: string }) {
  const [
    { visible, selectedUserGroupKeys, userGroupEntities, selectedUserGroupsTableExtraColumns },
    { setSelectedUserGroupKeys, setUserGroupEntities, onSelectedUserGroupsChange, onChange },
  ] = useComplexUsersPicker();
  const [form] = Form.useForm<FiltersFormValues>();
  const [selectedForm] = Form.useForm<FiltersFormValues>();
  const shouldFilterSelectedRef = React.useRef(false);
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  const [loading, setLoading] = React.useState(false);
  const [ids, setIds] = React.useState<number[]>([]);
  const [pagination, setPagination] = React.useState<{
    total: number;
    page: number;
    pageSize: number;
  }>({ total: 0, page: 1, pageSize: 10 });
  const [selectedPagination, setSelectedPagination] = React.useState<{
    page: number;
    pageSize: number;
  }>({ page: 1, pageSize: 10 });

  React.useEffect(() => {
    if (selectedUserGroupKeys.length > 0) {
      fetchPagedUserGroups({
        groupIdList: selectedUserGroupKeys,
        needResources: true,
        pageNum: pagination.page,
        pageSize: pagination.pageSize,
      }).then(({ error, data }) => {
        if (error) {
          message.error(error.message);
          return;
        }
        const { entities } = normalize(data);
        setUserGroupEntities(prev => ({ ...prev, ...entities }));
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getUserGroups = React.useCallback(async () => {
    setLoading(true);
    const values = form.getFieldsValue();
    const { error, data } = await fetchPagedUserGroups({
      searchName: values.groupName,
      searchType: 'GROUP_NAME',
      notAllowGroupSet: selectedUserGroupKeys,
      idcTags: values.idcs,
      needResources: true,
      pageNum: pagination.page,
      pageSize: pagination.pageSize,
    });
    setLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    const { entities, keys } = normalize(data);
    setPagination(prev => ({ ...prev, total: data!.total }));
    setUserGroupEntities(prev => ({ ...prev, ...entities }));
    setIds(keys);
  }, [form, pagination.page, pagination.pageSize, selectedUserGroupKeys, setUserGroupEntities]);

  React.useEffect(() => {
    if (visible) {
      getUserGroups();
    }
  }, [getUserGroups, visible]);

  const handleChangeSelectedKeys = React.useCallback(
    (selected: boolean, keys: number[]) => {
      let userGroupKeys: number[] = [];
      setSelectedUserGroupKeys(prevKeys => {
        userGroupKeys = selected ? [...prevKeys, ...keys] : prevKeys.filter(k => !keys.includes(k));
        return userGroupKeys;
      });
      onSelectedUserGroupsChange?.(selected, keys, userGroupEntities);
      onChange?.(userGroupKeys, { selected, keys, variant: 'user-group' });
    },
    [setSelectedUserGroupKeys, onSelectedUserGroupsChange, userGroupEntities, onChange]
  );

  const userGroups = ids.map(id => userGroupEntities[id]!);
  const selectedUserGroups = selectedUserGroupKeys.map(id => userGroupEntities[id]);

  if (selectedUserGroups.includes(undefined)) {
    return null;
  }

  const filteredSelectedUserGroups: GroupModel[] = shouldFilterSelectedRef.current
    ? (selectedUserGroups as GroupModel[]).filter(({ groupName: name, resourceCodes }) => {
        const { groupName, idcs } = selectedForm.getFieldsValue();

        const nameMatched = name.toLowerCase().includes(groupName?.toLowerCase() || '');
        const resourceCodesMatched = !!idcs?.some(
          idc => !!resourceCodes?.some(code => code.split('.')[0] === idc)
        );

        if (groupName !== undefined && idcs === undefined) {
          return nameMatched;
        } else if (idcs !== undefined && groupName === undefined) {
          return resourceCodesMatched;
        } else if (groupName !== undefined && idcs !== undefined) {
          return nameMatched && resourceCodesMatched;
        } else {
          return true;
        }
      })
    : (selectedUserGroups as GroupModel[]);

  return (
    <Tabs defaultActiveKey={defaultActiveKey}>
      <Tabs.TabPane key="user-groups" tab="未选">
        <Space style={{ width: '100%' }} direction="vertical">
          <FiltersForm
            loading={loading}
            form={form}
            onSearch={() => {
              if (pagination.page === 1) {
                getUserGroups();
              } else {
                setPagination(prev => ({ ...prev, page: 1 }));
              }
            }}
            onReset={() => {
              if (pagination.page === 1 && pagination.pageSize === 10) {
                getUserGroups();
              } else {
                setPagination(prev => ({ ...prev, page: 1, pageSize: 10 }));
              }
            }}
          />
          <ComplexUsersPickerTable<GroupModel>
            rowKey="id"
            loading={loading}
            columns={userGroupsColumns}
            data={userGroups}
            pagination={{
              total: pagination.total,
              current: pagination.page,
              pageSize: pagination.pageSize,
            }}
            selectedRowKeys={selectedUserGroupKeys}
            renderActionButton={(selected, record) => {
              // rendering `Column Title`
              if (selected === undefined) {
                return (
                  <AddButton
                    disabled={
                      !userGroups.some(record => !selectedUserGroupKeys.includes(record!.id!))
                    }
                    onClick={() => {
                      const currentPageRowKeys: number[] = [];
                      userGroups.forEach(record => {
                        if (!selectedUserGroupKeys.includes(record!.id!)) {
                          currentPageRowKeys.push(record!.id!);
                        }
                      });
                      handleChangeSelectedKeys(true, currentPageRowKeys);
                      setPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                    }}
                  />
                );
              }

              return (
                <AddButton
                  disabled={selected}
                  onClick={() => {
                    const currentId = record!.id!;
                    handleChangeSelectedKeys(true, [currentId]);
                    const filterUserGroups = userGroups.filter(
                      userGroup => userGroup.id !== currentId
                    );
                    if (filterUserGroups.length === 0) {
                      setPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                    }
                  }}
                />
              );
            }}
            onChange={pagination => {
              setPagination(prev => ({
                ...prev,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }}
          />
        </Space>
      </Tabs.TabPane>
      <Tabs.TabPane
        key="selected-user-groups"
        tab={
          <Badge size="small" offset={[16, 0]} count={selectedUserGroupKeys.length}>
            <span>已选</span>
          </Badge>
        }
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <FiltersForm
            loading={loading}
            form={selectedForm}
            onSearch={() => {
              shouldFilterSelectedRef.current = true;
              forceUpdate();
            }}
            onReset={() => {
              shouldFilterSelectedRef.current = false;
              forceUpdate();
            }}
          />
          <ComplexUsersPickerTable
            rowKey="id"
            loading={false}
            columns={[...userGroupsColumns, ...(selectedUserGroupsTableExtraColumns || [])]}
            data={filteredSelectedUserGroups}
            pagination={{
              total: filteredSelectedUserGroups.length,
              current: selectedPagination.page,
              pageSize: selectedPagination.pageSize,
            }}
            selectedRowKeys={[]}
            renderActionButton={(selected, record) => {
              // rendering `Column Title`
              if (selected === undefined) {
                return (
                  <RemoveButton
                    disabled={filteredSelectedUserGroups.length <= 0}
                    onClick={() => {
                      const currentPageUserGroups = filteredSelectedUserGroups.slice(
                        (selectedPagination.page - 1) * selectedPagination.pageSize,
                        selectedPagination.page * selectedPagination.pageSize
                      );
                      const currentPageUserGroupIds = currentPageUserGroups.map(
                        userGroup => userGroup.id!
                      );
                      setSelectedPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                      handleChangeSelectedKeys(false, currentPageUserGroupIds);
                    }}
                  />
                );
              }

              return (
                <RemoveButton
                  disabled={false}
                  onClick={() => {
                    const currentUserId = record!.id;
                    const currentPageUserGroups = filteredSelectedUserGroups.slice(
                      (selectedPagination.page - 1) * selectedPagination.pageSize,
                      selectedPagination.page * selectedPagination.pageSize
                    );
                    const filterUserGroups = currentPageUserGroups.filter(
                      userGroup => userGroup.id !== currentUserId
                    );
                    if (filterUserGroups.length === 0) {
                      setSelectedPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                    }
                    handleChangeSelectedKeys(false, [currentUserId]);
                  }}
                />
              );
            }}
            onChange={pagination => {
              setSelectedPagination(prev => ({
                ...prev,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }}
          />
        </Space>
      </Tabs.TabPane>
    </Tabs>
  );
}

type FiltersFormValues = {
  idcs?: string[];
  groupName?: string;
};

type FiltersFormProps = {
  loading: boolean;
  form: FormInstance<FiltersFormValues>;
  onSearch(values: FiltersFormValues): void;
  onReset(): void;
};

function FiltersForm({ loading, form, onSearch, onReset }: FiltersFormProps) {
  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Form
        layout="inline"
        form={form}
        onFinish={onSearch}
        onReset={e => {
          e.preventDefault();
          e.stopPropagation();
          onReset();
        }}
      >
        <Form.Item name="groupName" label="用户组名称">
          <Input style={{ width: 200 }} />
        </Form.Item>
        <Form.Item name="idcs" label="位置">
          <IdcSelect style={{ width: 200 }} mode="multiple" maxTagCount={1} />
        </Form.Item>
        <Form.Item>
          <Space>
            <Button type="primary" htmlType="submit" loading={loading}>
              搜索
            </Button>
            <Button htmlType="reset" disabled={loading}>
              重置
            </Button>
          </Space>
        </Form.Item>
      </Form>
    </Space>
  );
}

function normalize(data: UserGroupsData): { entities: UserGroupEntities; keys: number[] } {
  return data.data.reduce<{ entities: UserGroupEntities; keys: number[] }>(
    (acc, userGroup) => {
      const id = userGroup.id!;
      acc.entities[id] = userGroup;
      acc.keys.push(id);
      return acc;
    },
    { entities: {}, keys: [] }
  );
}
