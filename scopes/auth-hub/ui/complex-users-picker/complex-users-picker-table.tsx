import React from 'react';

import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnsType, TablePaginationConfig, TableProps } from '@manyun/base-ui.ui.table';

export type ActionButtonProps = { onClick(...args: any[]): void; [x: string]: any };

export type ComplexUsersPickerTableProps<RecordType = any> = {
  rowKey: string;
  loading: boolean;
  columns: ColumnsType<RecordType>;
  data: RecordType[];
  pagination: TablePaginationConfig;
  selectedRowKeys: number[];
  renderActionButton: (
    selected?: boolean,
    record?: RecordType
  ) => React.ReactElement<ActionButtonProps>;
  onChange: TableProps<RecordType>['onChange'];
};

export function ComplexUsersPickerTable<RecordType = any>({
  rowKey,
  loading,
  columns,
  data,
  pagination,
  selectedRowKeys,
  renderActionButton,
  onChange,
}: ComplexUsersPickerTableProps<RecordType>) {
  return (
    <Table
      rowKey={rowKey}
      loading={loading}
      size="small"
      columns={columns}
      rowSelection={{
        selectedRowKeys,
        columnTitle: renderActionButton(),
        renderCell: renderActionButton,
      }}
      dataSource={data}
      pagination={pagination}
      onChange={onChange}
    />
  );
}
