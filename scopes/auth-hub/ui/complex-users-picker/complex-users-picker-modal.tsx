import React from 'react';

import { Modal } from '@manyun/base-ui.ui.modal';
import { Tabs } from '@manyun/base-ui.ui.tabs';

import { useComplexUsersPicker } from './complex-users-picker-context';
import { ComplexUsersPickerDepts } from './complex-users-picker-depts';
import { ComplexUsersPickerRoles } from './complex-users-picker-roles';
import { ComplexUsersPickerUserGroups } from './complex-users-picker-user-groups';
import { ComplexUsersPickerUsers } from './complex-users-picker-users';

export type ComplexUsersPickerModalProps = {
  title?: React.ReactNode;
};

export function ComplexUsersPickerModal({ title }: ComplexUsersPickerModalProps) {
  const [{ visible }, { reset, setVisible }] = useComplexUsersPicker();

  return (
    <Modal
      destroyOnClose
      title={title}
      width={1080}
      bodyStyle={{ maxHeight: '450px', overflow: 'hidden', overflowY: 'scroll' }}
      footer={null}
      visible={visible}
      onCancel={() => {
        reset();
        setVisible(false);
      }}
    >
      <ComplexUsersPickerTabs />
    </Modal>
  );
}

export function ComplexUsersPickerTabs() {
  const [{ selectedRoleKeys, selectedUserKeys, selectedUserGroupKeys, showUserGroups, showDepts }] =
    useComplexUsersPicker();
  let defaultTabActiveKey = 'role';
  let defaultSelectUserTabActiveKey = 'users';
  let defaultSelectRoleTabActiveKey = 'roles';
  const defaultSelectUserGroupTabActiveKey = 'user-groups';
  if (selectedRoleKeys.length) {
    defaultTabActiveKey = 'role';
    defaultSelectRoleTabActiveKey = 'selected-roles';
  } else if (selectedUserGroupKeys.length) {
    defaultTabActiveKey = 'user-group';
    defaultSelectUserTabActiveKey = 'selected-user-groups';
  } else if (selectedUserKeys.length) {
    defaultTabActiveKey = 'user';
    defaultSelectUserTabActiveKey = 'selected-users';
  }

  return (
    <Tabs type="card" defaultActiveKey={defaultTabActiveKey}>
      <Tabs.TabPane key="role" tab="角色">
        <ComplexUsersPickerRoles defaultActiveKey={defaultSelectRoleTabActiveKey} />
      </Tabs.TabPane>
      <Tabs.TabPane key="user" tab="人员">
        <ComplexUsersPickerUsers defaultActiveKey={defaultSelectUserTabActiveKey} />
      </Tabs.TabPane>
      {showDepts && (
        <Tabs.TabPane key="dept" tab="部门">
          <ComplexUsersPickerDepts />
        </Tabs.TabPane>
      )}
      {showUserGroups && (
        <Tabs.TabPane key="user-group" tab="用户组">
          <ComplexUsersPickerUserGroups defaultActiveKey={defaultSelectUserGroupTabActiveKey} />
        </Tabs.TabPane>
      )}
    </Tabs>
  );
}
