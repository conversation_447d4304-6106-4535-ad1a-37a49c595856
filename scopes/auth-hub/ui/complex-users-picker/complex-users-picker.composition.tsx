import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { ComplexUsersPicker } from './index';
import type { Value } from './index';

export const BasicComplexUsersPicker = () => {
  const [initialized, update] = React.useState(false);
  const [selectedTargetName, setSelectedTargetName] = React.useState<string>('UNSELECTED');

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    update(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router>
          <ComplexUsersPicker
            defaultSelectedRoleKeys={[0]}
            selectedRolesTableExtraColumns={[
              {
                title: 'ID',
                dataIndex: 'id',
              },
            ]}
            defaultSelectedUserKeys={[17]}
            onSelectedRolesChange={(selected, [id], entities) => {
              setSelectedTargetName(selected ? entities[id]!.name! : 'UNSELECTED');
            }}
            onSelectedUsersChange={(selected, selectedUserIds, entities) => {
              setSelectedTargetName(selected ? entities[selectedUserIds[0]]!.name! : 'UNSELECTED');
            }}
          >
            <Button
              type="primary"
              onClick={async () =>
                new Promise(resolve => {
                  setTimeout(resolve, 1.5 * 1000, true);
                })
              }
            >
              CLICK ME
            </Button>
          </ComplexUsersPicker>
          <div>Last Selected Target Name: {selectedTargetName}</div>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};

export const ControlledComplexUsersPicker = () => {
  const [initialized, update] = React.useState(false);
  const [value, onChange] = React.useState<Value>({ roleKeys: [], userKeys: [2] });

  React.useEffect(() => {
    const mockOff = useRemoteMock('web');
    update(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!initialized) {
    return <span>Loading...</span>;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <Router>
          <ComplexUsersPicker modalTitle="复杂人员选择" value={value} onChange={onChange}>
            <Button type="primary">CLICK ME</Button>
          </ComplexUsersPicker>
          <br />
          <br />
          <Button
            onClick={() => {
              console.log('Controlled ComplexUsersPicker value: ', value);
            }}
          >
            PRINT CURRENT VALUE INTO CONSOLE
          </Button>
        </Router>
      </FakeStore>
    </ConfigProvider>
  );
};
