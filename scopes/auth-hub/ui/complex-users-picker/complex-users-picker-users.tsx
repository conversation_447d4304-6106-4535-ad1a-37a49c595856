import React from 'react';
import { useDeepCompareEffect } from 'react-use';

import type { Role } from '@manyun/auth-hub.model.role';
import { fetchExclusiveUsersWeb as fetchExclusiveUsers } from '@manyun/auth-hub.service.pm.fetch-exclusive-users';
import type { ServiceRD as AuthUsersListData } from '@manyun/auth-hub.service.pm.fetch-exclusive-users';
import { RoleSelect } from '@manyun/auth-hub.ui.role-select';
import { Badge } from '@manyun/base-ui.ui.badge';
import { Form } from '@manyun/base-ui.ui.form';
import type { FormInstance } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import type { QueryFilterProps } from '@manyun/base-ui.ui.query-filter';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';
import { Space } from '@manyun/base-ui.ui.space';
import type { ColumnsType } from '@manyun/base-ui.ui.table';
import { Tabs } from '@manyun/base-ui.ui.tabs';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import { AddButton, RemoveButton } from './complex-users-picker-action-button';
import { useComplexUsersPicker } from './complex-users-picker-context';
import type { UserEntities, UsersRecordValue } from './complex-users-picker-context';
import { ComplexUsersPickerTable } from './complex-users-picker-table';

const usersColumns: ColumnsType<UsersRecordValue> = [
  {
    title: '人员姓名',
    dataIndex: 'name',
  },
  {
    title: '人员账号',
    dataIndex: 'login',
  },
  {
    width: 200,
    ellipsis: true,
    title: '所属角色',
    dataIndex: 'roles',
    render(roles: Role[]) {
      return roles?.map(role => role.name!).join(' | ') || '--';
    },
  },
  {
    width: 200,
    ellipsis: true,
    title: '所属位置',
    dataIndex: 'resources',
    render(resources: UsersRecordValue['resources']) {
      return resources?.map(resource => resource.code).join(' | ') || '--';
    },
  },
];

export function ComplexUsersPickerUsers({ defaultActiveKey }: { defaultActiveKey?: string }) {
  const [
    {
      disabledUserKeys,
      selectedUserKeys,
      userEntities,
      selectedUsersTableExtraColumns,
      blockGuids,
    },
    { setSelectedUserKeys, setUserEntities, onSelectedUsersChange, onChange },
  ] = useComplexUsersPicker();
  const [form] = Form.useForm<ComplexUsersPickerUsersFiltersFormValues>();
  const [selectedUsersFiltersForm] =
    Form.useForm<ComplexUsersPickerSelectedUsersFiltersFormValues>();
  const shouldFilterSelectedUsersRef = React.useRef(false);
  const [, forceUpdate] = React.useReducer(x => x + 1, 0);
  const [loading, setLoading] = React.useState(false);
  const [ids, setIds] = React.useState<number[]>([]);
  const [pagination, setPagination] = React.useState<{
    total: number;
    page: number;
    pageSize: number;
  }>({ total: 0, page: 1, pageSize: 10 });
  const [selectedPagination, setSelectedPagination] = React.useState<{
    page: number;
    pageSize: number;
  }>({ page: 1, pageSize: 10 });

  React.useEffect(() => {
    if (selectedUserKeys.length > 0) {
      fetchExclusiveUsers({
        withDeleted: false,
        ids: selectedUserKeys,
        blockGuids: blockGuids,
        page: 1,
        pageSize: selectedUserKeys.length,
      }).then(({ error, data }) => {
        if (error) {
          message.error(error.message);
          return;
        }
        const { entities } = normalize(data);
        setUserEntities(prev => ({ ...prev, ...entities }));
      });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const getUsers = React.useCallback(
    async (blockGuids?: string[]) => {
      setLoading(true);
      const values = form.getFieldsValue();
      const spaces =
        Array.isArray(values.spaces) && values.spaces.length > 0 ? values.spaces : undefined;
      const idcs = spaces ? spaces.filter(space => space.indexOf('.') === -1) : undefined;
      const blocks = spaces ? spaces.filter(space => space.indexOf('.') !== -1) : undefined;

      const { error, data } = await fetchExclusiveUsers({
        withDeleted: false,
        idcs: idcs,
        blockGuids: blockGuids ? blockGuids : (blocks ?? []),
        roleIds: values.roleIds,
        exclusiveIds: [...disabledUserKeys, ...selectedUserKeys],
        name: values.userName,
        page: pagination.page,
        pageSize: pagination.pageSize,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      const { entities, result } = normalize(data);
      setPagination(prev => ({ ...prev, total: data!.total }));
      setUserEntities(prev => ({ ...prev, ...entities }));
      setIds(result);
      // eslint-disable-next-line react-hooks/exhaustive-deps
    },
    [
      // FIXME: @Jerry shallow compare it with `prevDisabledUserKeys`
      disabledUserKeys.length,
      selectedUserKeys,
      pagination.page,
      pagination.pageSize,
    ]
  );

  useDeepCompareEffect(() => {
    getUsers(blockGuids);
  }, [getUsers, blockGuids]);

  const selectedUserKeysChangeHandler = React.useCallback(
    (selected: boolean, keys: number[]) => {
      let userKeys: number[] = [];
      setSelectedUserKeys(prevKeys => {
        userKeys = selected ? [...prevKeys, ...keys] : prevKeys.filter(k => !keys.includes(k));

        return userKeys;
      });
      if (onSelectedUsersChange) {
        onSelectedUsersChange(selected, keys, userEntities);
      }
      if (onChange) {
        onChange(userKeys, { selected, keys, variant: 'user' });
      }
    },
    [setSelectedUserKeys, onSelectedUsersChange, onChange, userEntities]
  );

  const users: UsersRecordValue[] = ids.map(id => userEntities[id]!);
  const selectedUsers: (UsersRecordValue | undefined)[] = selectedUserKeys
    .map(id => userEntities[id])
    .filter(_ => _);

  if (selectedUsers.includes(undefined)) {
    return null;
  }

  const filteredSelectedUsers: UsersRecordValue[] = shouldFilterSelectedUsersRef.current
    ? (selectedUsers as UsersRecordValue[]).filter(({ name, login }) => {
        const { userName, loginName } = selectedUsersFiltersForm.getFieldsValue();

        const nameMatched = name.toLowerCase().includes(userName?.toLowerCase() || '');
        const loginMatched = !!login?.toLowerCase().includes(loginName?.toLowerCase() || '');

        if (userName !== undefined && loginName === undefined) {
          return nameMatched;
        } else if (loginName !== undefined && userName === undefined && login) {
          return loginMatched;
        } else if (userName !== undefined && loginName !== undefined) {
          return nameMatched && loginMatched;
        } else {
          return true;
        }
      })
    : (selectedUsers as UsersRecordValue[]);

  return (
    <Tabs defaultActiveKey={defaultActiveKey}>
      <Tabs.TabPane key="users" tab="未选">
        <Space style={{ width: '100%' }} direction="vertical">
          <ComplexUsersPickerUsersFiltersForm
            form={form}
            blockGuids={blockGuids}
            onSearch={() => {
              if (pagination.page === 1) {
                getUsers();
              } else {
                setPagination(prev => ({ ...prev, page: 1 }));
              }
            }}
            onReset={() => {
              if (pagination.page === 1 && pagination.pageSize === 10) {
                getUsers();
              } else {
                setPagination(prev => ({ ...prev, page: 1, pageSize: 10 }));
              }
            }}
          />
          <ComplexUsersPickerTable<UsersRecordValue>
            rowKey="id"
            loading={loading}
            columns={usersColumns}
            data={users}
            pagination={{
              total: pagination.total,
              current: pagination.page,
              pageSize: pagination.pageSize,
            }}
            selectedRowKeys={selectedUserKeys}
            renderActionButton={(selected, record) => {
              // rendering `Column Title`
              if (selected === undefined) {
                return (
                  <AddButton
                    disabled={!users.some(record => !selectedUserKeys.includes(record!.id!))}
                    onClick={() => {
                      const currentPageRowKeys: number[] = [];
                      users.forEach(record => {
                        if (!selectedUserKeys.includes(record!.id!)) {
                          currentPageRowKeys.push(record!.id!);
                        }
                      });
                      selectedUserKeysChangeHandler(true, currentPageRowKeys);
                      setPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                    }}
                  />
                );
              }

              return (
                <AddButton
                  disabled={selected}
                  onClick={() => {
                    const currentUserId = record!.id;
                    selectedUserKeysChangeHandler(true, [currentUserId]);
                    const usersLeft = users.filter(user => user.id !== currentUserId);
                    if (usersLeft.length === 0) {
                      setPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                    }
                  }}
                />
              );
            }}
            onChange={pagination => {
              setPagination(prev => ({
                ...prev,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }}
          />
        </Space>
      </Tabs.TabPane>
      <Tabs.TabPane
        key="selected-users"
        tab={
          <Badge size="small" offset={[16, 0]} count={selectedUsers.length}>
            <span>已选</span>
          </Badge>
        }
      >
        <Space style={{ width: '100%' }} direction="vertical">
          <ComplexUsersPickerSelectedUsersFiltersForm
            form={selectedUsersFiltersForm}
            onSearch={() => {
              shouldFilterSelectedUsersRef.current = true;
              forceUpdate();
            }}
            onReset={() => {
              shouldFilterSelectedUsersRef.current = false;
              forceUpdate();
            }}
          />
          <ComplexUsersPickerTable
            rowKey="id"
            loading={false}
            columns={[...usersColumns, ...(selectedUsersTableExtraColumns || [])]}
            data={filteredSelectedUsers}
            pagination={{
              total: filteredSelectedUsers.length,
              current: selectedPagination.page,
              pageSize: selectedPagination.pageSize,
            }}
            selectedRowKeys={[]}
            renderActionButton={(selected, record) => {
              // rendering `Column Title`
              if (selected === undefined) {
                return (
                  <RemoveButton
                    disabled={filteredSelectedUsers.length <= 0}
                    onClick={() => {
                      const currentPageUsers = filteredSelectedUsers.slice(
                        (selectedPagination.page - 1) * selectedPagination.pageSize,
                        selectedPagination.page * selectedPagination.pageSize
                      );
                      const currentPageUserIds = currentPageUsers.map(user => user.id!);
                      setSelectedPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                      selectedUserKeysChangeHandler(false, currentPageUserIds);
                    }}
                  />
                );
              }

              return (
                <RemoveButton
                  disabled={false}
                  onClick={() => {
                    const currentUserId = record!.id;
                    const currentPageUsers = filteredSelectedUsers.slice(
                      (selectedPagination.page - 1) * selectedPagination.pageSize,
                      selectedPagination.page * selectedPagination.pageSize
                    );
                    const usersLeft = currentPageUsers.filter(user => user.id !== currentUserId);
                    if (usersLeft.length === 0) {
                      setSelectedPagination(prev => ({
                        ...prev,
                        page: prev.page > 1 ? prev.page - 1 : 1,
                      }));
                    }
                    selectedUserKeysChangeHandler(false, [currentUserId]);
                  }}
                />
              );
            }}
            onChange={pagination => {
              setSelectedPagination(prev => ({
                ...prev,
                page: pagination.current!,
                pageSize: pagination.pageSize!,
              }));
            }}
          />
        </Space>
      </Tabs.TabPane>
    </Tabs>
  );
}

type ComplexUsersPickerUsersFiltersFormValues = {
  spaces?: string[];
  roleIds?: number[];
  userName?: string;
};

type ComplexUsersPickerUsersFiltersFormProps = {
  form: FormInstance<ComplexUsersPickerUsersFiltersFormValues>;
  blockGuids?: string[];
  onSearch(values: ComplexUsersPickerUsersFiltersFormValues): void;
  onReset(): void;
};

function ComplexUsersPickerUsersFiltersForm({
  form,
  blockGuids,
  onSearch,
  onReset,
}: ComplexUsersPickerUsersFiltersFormProps) {
  const formItems: QueryFilterProps['items'] = [
    {
      name: 'roleIds',
      label: '角色',
      control: <RoleSelect style={{ width: 150 }} mode="multiple" maxTagCount={1} />,
    },
    {
      name: 'spaces',
      label: '位置',
      initialValue: blockGuids,
      control: (
        <LocationTreeSelect
          style={{ width: 150 }}
          multiple
          maxTagCount={1}
          disabled={Array.isArray(blockGuids) && blockGuids.length > 0}
        />
      ),
    },
    {
      name: 'userName',
      label: '姓名',
      control: <Input style={{ width: 150 }} />,
    },
  ];

  return <QueryFilter form={form} items={formItems} onSearch={onSearch} onReset={onReset} />;
}

type ComplexUsersPickerSelectedUsersFiltersFormValues = {
  userName?: string;
  loginName?: string;
};

type ComplexUsersPickerSelectedUsersFiltersFormProps = {
  form: FormInstance<ComplexUsersPickerSelectedUsersFiltersFormValues>;
  onSearch(values: ComplexUsersPickerUsersFiltersFormValues): void;
  onReset(): void;
};

function ComplexUsersPickerSelectedUsersFiltersForm({
  form,
  onSearch,
  onReset,
}: ComplexUsersPickerSelectedUsersFiltersFormProps) {
  const formItems: QueryFilterProps['items'] = [
    {
      name: 'userName',
      label: '姓名',
      control: <Input />,
    },
    {
      name: 'loginName',
      label: '账号',
      control: <Input />,
    },
  ];

  return <QueryFilter form={form} items={formItems} onSearch={onSearch} onReset={onReset} />;
}

function normalize(data: AuthUsersListData): { entities: UserEntities; result: number[] } {
  return data.data.reduce<{ entities: UserEntities; result: number[] }>(
    (acc, user) => {
      const id = user.id!;
      acc.entities[id] = user;
      acc.result.push(id);

      return acc;
    },
    { entities: {}, result: [] }
  );
}
