import React from 'react';

import type { ColumnsType } from '@manyun/base-ui.ui.table';

import { ComplexUsersPickerProvider } from './complex-users-picker-context';
import type { Callbacks, Value } from './complex-users-picker-context';
import { ComplexUsersPickerImpl } from './complex-users-picker-impl';
import type { ClickableChildren } from './complex-users-picker-impl';

export type ComplexUsersPickerProps = {
  modalTitle?: React.ReactNode;
  /**
   * 已选角色的 ID 集合
   */
  defaultSelectedRoleKeys?: number[];
  /**
   * 已选角色表格的额外自定义列配置
   */
  selectedRolesTableExtraColumns?: ColumnsType<any>;
  /**
   * 已选用户的 ID 集合
   */
  defaultSelectedUserKeys?: number[];
  /**
   * 已选用户表格的额外自定义列配置
   */
  selectedUsersTableExtraColumns?: ColumnsType<any>;
  /**
   * 已选用户组的 ID 集合
   */
  defaultSelectedUserGroupKeys?: number[];
  /**
   * 已选用户组表格的额外自定义列配置
   */
  selectedUserGroupsTableExtraColumns?: ColumnsType<any>;
  /**
   * 禁用的用户
   *
   * 注意：
   *   1. 被禁用后，将不会渲染在未选标签页中
   *   2. 不需要将已选的用户通过此 `prop` 传入
   * */
  disabledUserKeys?: number[];
  /**
   * 禁用的角色
   *
   * 注意：
   *   1. 被禁用后，对应角色将不会渲染在未选标签页中
   *   2. 已选的角色已进行禁用处理，不需要再通过此 `prop` 传入
   */
  disabledRoleKeys?: number[];
  /** 受控模式，通常用于表单中 */
  value?: Value;
  /** 需要传入能够接收 `onClick prop` 的 `ReactElement`，比如 `<Button />` */
  children?: ClickableChildren;
  onSelectedRolesChange?: Callbacks['onSelectedRolesChange'];
  onSelectedUsersChange?: Callbacks['onSelectedUsersChange'];
  onSelectedUserGroupsChange?: Callbacks['onSelectedUserGroupsChange'];
  /**
   * 通常配合 `value` 一起使用
   *
   * 需要注意的是：最好传入 `memoized` 的版本来避免性能问题
   */
  onChange?: Callbacks['onChange'];
  // todo: 目前仅课件管理-授权支持了用户组，后续其他页面也支持后，可删除此属性
  /** 是否展示用户组 */
  showUserGroups?: boolean;
  /** 是否展示部门 */
  showDepts?: boolean;
  /** 是否去除modal */
  showModal?: boolean;
  /** 筛选有对应楼栋资源的用户 */
  blockGuids?: string[];
};

export function ComplexUsersPicker({
  modalTitle,
  defaultSelectedRoleKeys,
  defaultSelectedUserKeys,
  defaultSelectedUserGroupKeys,
  disabledUserKeys,
  disabledRoleKeys,
  value,
  selectedRolesTableExtraColumns,
  selectedUsersTableExtraColumns,
  selectedUserGroupsTableExtraColumns,
  children,
  onSelectedRolesChange,
  onSelectedUsersChange,
  onSelectedUserGroupsChange,
  onChange,
  showUserGroups,
  showDepts,
  showModal,
  blockGuids,
}: ComplexUsersPickerProps) {
  return (
    <ComplexUsersPickerProvider
      defaultSelectedRoleKeys={defaultSelectedRoleKeys}
      defaultSelectedUserKeys={defaultSelectedUserKeys}
      defaultSelectedUserGroupKeys={defaultSelectedUserGroupKeys}
      disabledUserKeys={disabledUserKeys}
      disabledRoleKeys={disabledRoleKeys}
      value={value}
      selectedRolesTableExtraColumns={selectedRolesTableExtraColumns}
      selectedUsersTableExtraColumns={selectedUsersTableExtraColumns}
      selectedUserGroupsTableExtraColumns={selectedUserGroupsTableExtraColumns}
      showUserGroups={showUserGroups}
      showDepts={showDepts}
      showModal={showModal}
      blockGuids={blockGuids}
      onChange={onChange}
      onSelectedRolesChange={onSelectedRolesChange}
      onSelectedUsersChange={onSelectedUsersChange}
      onSelectedUserGroupsChange={onSelectedUserGroupsChange}
    >
      <ComplexUsersPickerImpl modalTitle={modalTitle}>{children}</ComplexUsersPickerImpl>
    </ComplexUsersPickerProvider>
  );
}
