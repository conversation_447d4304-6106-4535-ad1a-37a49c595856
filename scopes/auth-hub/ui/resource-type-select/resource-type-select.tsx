import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';
import type { BackendResourceType } from '@manyun/iam.model.auth-resource';
import { getAuthResourceLocales } from '@manyun/iam.model.auth-resource';

export type Option = {
  label: string;
  value: BackendResourceType;
};

export type ResourceTypeSelectProps<Value extends string | string[] = string> = {
  hiddenTypes?: BackendResourceType[];
  onChange?: (value: Value, option: Value extends string ? Option : Option[]) => void;
} & Omit<SelectProps<Value, Option>, 'options' | 'onChange'>;

function _ResourceTypeSelect<Value extends string | string[] = string>(
  { hiddenTypes = [], onChange, ...restProps }: ResourceTypeSelectProps<Value>,
  ref?: React.ForwardedRef<RefSelectProps>
) {
  const locales = getAuthResourceLocales();
  const options = (
    [
      {
        label: locales.resourceType.AREA,
        value: 'AREA',
      },
      {
        label: locales.resourceType.IDC,
        value: 'IDC',
      },
      {
        label: locales.resourceType.BUILDING,
        value: 'BUILDING',
      },
      {
        label: locales.resourceType.CUSTOMER,
        value: 'CUSTOMER',
      },
      {
        label: locales.resourceType.DEPT,
        value: 'DEPT',
      },
      {
        label: locales.resourceType.ROOM,
        value: 'ROOM',
      },
      {
        label: locales.resourceType.TEAM,
        value: 'TEAM',
      },
      {
        label: locales.resourceType.PROJECT_COMPANY,
        value: 'PROJECT_COMPANY',
      },
    ] as Option[]
  ).filter(option => !hiddenTypes.includes(option.value));

  return (
    <Select<Value, Option>
      style={{ width: 200 }}
      {...restProps}
      ref={ref}
      options={options}
      onChange={(value, option) => {
        onChange?.(value, option as Value extends string ? Option : Option[]);
      }}
    />
  );
}

interface GenericResourceTypeSelect {
  <Value extends string | string[] = string>(
    props: ResourceTypeSelectProps<Value>,
    ref: React.ForwardedRef<RefSelectProps>
  ): JSX.Element;
  displayName: string;
}

export const ResourceTypeSelect = React.forwardRef(
  _ResourceTypeSelect
) as GenericResourceTypeSelect;

ResourceTypeSelect.displayName = 'ResourceTypeSelect';
