import React, { useEffect, useState } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroyMock, webRequest } from '@manyun/service.request';

import { UserCompanyConnector } from './user-company-connector';

export const BasicUserCompanyConnector = () => {
  const [initialize, update] = useState(false);

  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialize) return <>loaing</>;
  return (
    <ConfigProvider>
      <FakeStore>
        <UserCompanyConnector userType="CUSTOMER" />
      </FakeStore>
    </ConfigProvider>
  );
};

export const VendorUserCompanyConnector = () => {
  const [initialize, update] = useState(false);

  useEffect(() => {
    destroyMock();
    webRequest.axiosInstance.defaults.baseURL = 'http://localhost:3400/api';
    update(true);
  }, []);

  if (!initialize) return <>loaing</>;

  return (
    <ConfigProvider>
      <FakeStore>
        <UserCompanyConnector userType="VENDOR" />
      </FakeStore>
    </ConfigProvider>
  );
};
