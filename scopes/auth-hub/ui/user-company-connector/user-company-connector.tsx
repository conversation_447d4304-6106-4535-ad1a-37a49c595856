import React, { useCallback, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { Button } from '@manyun/base-ui.ui.button';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useUsers } from '@manyun/auth-hub.hook.use-users';
import type { UserType } from '@manyun/auth-hub.model.user';
import { mutateUserCompanyOnUser } from '@manyun/auth-hub.service.mutate-user-company-on-user';
import { selectMe, userSliceActions } from '@manyun/auth-hub.state.user';
import { UsersDataTable } from '@manyun/auth-hub.ui.users-data-table';

export type UserCompanyConnectorProps = {
  company?: string /**公司简称 */;
  userType?: UserType /**用户类型 */;
  onOk?: () => void;
};

export function UserCompanyConnector({ company, userType, onOk }: UserCompanyConnectorProps) {
  const [visible, setVisible] = useState(false);
  const [confirmLoaidng, setConfirmLoading] = useState(false);
  const [selectedKeys, setSelectedKeys] = useState<number[]>([]);
  const [pagination, setPagination] = React.useState({ pageNum: 1, pageSize: 10 });
  const [{ loading, total, data }, getUsers] = useUsers();
  const [form] = Form.useForm();

  const dispatch = useDispatch();
  const { userId } = useSelector(selectMe);

  const onHandleClick = () => {
    setVisible(true);
    onRestUser();
  };

  const onRestUser = useCallback(() => {
    form.resetFields();
    setSelectedKeys([]);
    onLoadUser();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [form, userType]);

  const onLoadUser = useCallback(
    (pageNum?: number, pageSize?: number) => {
      setPagination({ pageNum: pageNum ?? 1, pageSize: pageSize ?? 10 });
      getUsers({
        fields: {
          pageNum: pageNum ?? 1,
          pageSize: pageSize ?? 10,
          userName: form.getFieldValue('userName'),
          userType,
        },
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form, userType]
  );

  const pageHandler = useCallback(
    (_page: number, _pageSize: number) => {
      onLoadUser(_page, _pageSize);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [form, userType]
  );

  const onHandleSubmit = useCallback(async () => {
    if (selectedKeys.length === 0) {
      message.error('请选择用户');
      return;
    }
    setConfirmLoading(true);
    const { error } = await mutateUserCompanyOnUser({
      userIds: selectedKeys,
      userType,
      company,
    });
    setConfirmLoading(false);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('关联成功！');
    setVisible(false);

    /**
     * see task[#614] http://chandao.manyun-local.com/zentao/task-view-614.html
     */
    if (userId && selectedKeys.includes(userId)) {
      dispatch(userSliceActions.setUserCompany(company!));
    }
    onOk && onOk();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [selectedKeys, userType, company, dispatch]);

  return (
    <>
      <Typography.Link onClick={onHandleClick}>关联用户</Typography.Link>
      <Modal
        title="关联用户"
        visible={visible}
        confirmLoading={confirmLoaidng}
        onCancel={() => setVisible(false)}
        onOk={onHandleSubmit}
        width={820}
        destroyOnClose
      >
        <Space direction="vertical" size={15} style={{ width: '100%' }}>
          <Form
            colon={false}
            layout="inline"
            form={form}
            labelCol={{ span: 5 }}
            wrapperCol={{ span: 10 }}
          >
            <Form.Item label="姓名" name="userName">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>
            <Form.Item>
              <Space direction="horizontal">
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    setSelectedKeys([]);
                    onLoadUser();
                  }}
                >
                  搜索
                </Button>
                <Button disabled={loading} onClick={onRestUser}>
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Form>
          <UsersDataTable
            loading={loading}
            columnPicks={['name', 'mobileNumber', 'email']}
            data={data}
            rowSelection={{
              selectedRowKeys: selectedKeys,
              getCheckboxProps: record => ({
                // eslint-disable-next-line eqeqeq
                disabled: record.company == company,
              }),
              onChange: selectedRowKeys => {
                setSelectedKeys(selectedRowKeys as number[]);
              },
            }}
            pagination={{
              total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: pageHandler,
            }}
          />
        </Space>
      </Modal>
    </>
  );
}
