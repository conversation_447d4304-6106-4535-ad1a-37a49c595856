/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-7
 *
 * @packageDocumentation
 */
import React from 'react';

import { nanoid } from 'nanoid';

import type { EditableFormInstance, ProColumns } from '@manyun/base-ui.ui.editable-pro-table';
import { EditableProTable } from '@manyun/base-ui.ui.editable-pro-table';
import { Input } from '@manyun/base-ui.ui.input';

import type { CustomerPerson } from '@manyun/auth-hub.gql.client.customer-white-list';

export type WhiteStaff = CustomerPerson & { id: string };

export type CustomerWhiteStaffTableProps = {
  editableFormRef?: React.MutableRefObject<EditableFormInstance<WhiteStaff> | undefined>;
  mode?: 'edit' | 'view';
  value?: WhiteStaff[];
  onChange?: (value: WhiteStaff[]) => void;
};

export function CustomerWhiteStaffTable({
  mode = 'edit',
  editableFormRef,
  value,
  onChange,
}: CustomerWhiteStaffTableProps) {
  const isEdit = React.useMemo(() => mode === 'edit', [mode]);

  return (
    <EditableProTable<WhiteStaff>
      rowKey="id"
      editableFormRef={editableFormRef}
      scroll={{ x: 'max-content' }}
      recordCreatorProps={
        mode === 'edit'
          ? {
              position: 'top',
              creatorButtonText: '添加人员',
              newRecordType: 'dataSource',
              record(index, dataSource) {
                return {
                  id: nanoid(6),
                  userName: '',
                  phone: '',
                  email: '',
                };
              },
            }
          : false
      }
      editable={{
        type: 'multiple',
        deletePopconfirmMessage: '删除此行？',
        editableKeys: isEdit ? value?.map(({ id }) => id) : [],
        onValuesChange(_record, datasource) {
          onChange?.(datasource);
        },
        actionRender: (_row, _config, dom) => [dom.delete],
      }}
      columns={
        (
          [
            {
              title: '手机号码',
              dataIndex: 'phone',
              fixed: 'left',
              formItemProps() {
                return {
                  rules: [
                    {
                      required: true,
                      message: '手机号码必填',
                    },
                    {
                      pattern: /^1[3456789]\d{9}$/,
                      message: '手机号格式错误，请重新输入',
                    },
                    {
                      max: 11,
                      message: '最多输入 11 个字符！',
                    },
                  ],
                };
              },
              renderFormItem(schema, config, form) {
                return <Input />;
              },
            },
            {
              title: '姓名',
              dataIndex: 'userName',
              formItemProps(form, config) {
                return {
                  rules: [
                    {
                      required: true,
                      message: '姓名必填',
                    },
                    {
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                };
              },
              renderFormItem(schema, config, form) {
                return <Input />;
              },
            },
            {
              title: '岗位',
              dataIndex: 'position',
              formItemProps(form, config) {
                return {
                  rules: [
                    {
                      required: true,
                      message: '岗位必填',
                    },

                    {
                      max: 20,
                      message: '最多输入 20 个字符！',
                    },
                  ],
                };
              },
              renderFormItem(schema, config, form) {
                return <Input />;
              },
            },
            {
              title: '邮箱',
              dataIndex: 'email',
              formItemProps(form, config) {
                return {
                  rules: [
                    { required: true, message: '邮箱必填' },
                    {
                      type: 'email',
                      message: '邮箱格式错误，请重新输入',
                    },
                  ],
                };
              },
              renderFormItem(schema, config, form) {
                return <Input />;
              },
            },
            {
              width: 60,
              fixed: 'right',
              title: '操作',
              dataIndex: 'option',
              valueType: 'option',
            },
          ] as ProColumns<WhiteStaff>[]
        ).filter(item => (!isEdit ? item.dataIndex !== 'option' : true)) as ProColumns<WhiteStaff>[]
      }
      pagination={isEdit ? false : { pageSize: 10, current: 1 }}
      value={value}
      onChange={onChange}
    />
  );
}
