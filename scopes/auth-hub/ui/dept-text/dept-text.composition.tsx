import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { registerMocks } from '@manyun/auth-hub.hook.use-users';
import { getMock } from '@manyun/service.request';

import { DeptText } from './dept-text';

export const BasicDeptText = () => {
  const [init, setInit] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerMocks(mock);
    setInit(true);
  }, []);

  if (!init) {
    return <>loading</>;
  }

  return (
    <ConfigProvider>
      <DeptText deptId="D00401" />
    </ConfigProvider>
  );
};
