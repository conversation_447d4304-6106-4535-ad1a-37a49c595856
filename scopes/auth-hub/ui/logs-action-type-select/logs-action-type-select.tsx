import React, { useEffect, useState } from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { LabeledValue, RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import { fetchModifyType } from '@manyun/auth-hub.service.fetch-modify-type';

export type LogsActionTypeSelectProps = SelectProps;

export const LogsActionTypeSelect = React.forwardRef(
  (props: LogsActionTypeSelectProps, ref: React.Ref<RefSelectProps>) => {
    const [loading, setLoading] = useState(false);
    const [options, setOptions] = useState<LabeledValue[]>([]);

    useEffect(() => {
      setLoading(true);
      fetchModifyType().then(({ data }) => {
        setLoading(false);
        if (data) {
          const options = Object.keys(data).map(key => ({
            key,
            value: key,
            label: data[key],
          }));
          setOptions(options);
        }
      });
    }, []);

    return (
      <Select
        ref={ref}
        showSearch
        allowClear
        {...props}
        options={options}
        loading={loading}
        optionFilterProp="label"
      />
    );
  }
);

LogsActionTypeSelect.displayName = 'LogsActionTypeSelect';
