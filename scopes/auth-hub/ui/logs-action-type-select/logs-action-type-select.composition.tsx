import React, { useEffect } from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

// @ts-ignore
import { registerWebMocks } from '@manyun/auth-hub.service.pm.fetch-roles/dist/fetch-roles.mock';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { getMock } from '@manyun/service.request';

import { LogsActionTypeSelect } from './logs-action-type-select';

export const BasicLogsActionTypeSelect = () => {
  const [initialized, update] = React.useState(false);

  useEffect(() => {
    const mock = getMock('web', { delayResponse: 777 });
    registerWebMocks(mock);
    update(true);
  }, []);

  if (!initialized) {
    return null;
  }

  return (
    <ConfigProvider>
      <FakeStore>
        <LogsActionTypeSelect />
      </FakeStore>
    </ConfigProvider>
  );
};
