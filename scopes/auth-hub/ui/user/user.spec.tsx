import React from 'react';

import { render, waitFor } from '@testing-library/react';

import { LocalUserLink, RemoteUserLink } from './user.composition';

test('should render a user link with local data', () => {
  const { getByText } = render(<LocalUserLink />);
  const rendered = getByText('Jerry');
  expect(rendered).toBeTruthy();
});

test('should render a user link with remote data', async () => {
  const { getByText } = render(<RemoteUserLink delayResponse={0} />);
  await waitFor(() => {
    const rendered = getByText('Name 1');
    expect(rendered).toBeTruthy();
  });
});
