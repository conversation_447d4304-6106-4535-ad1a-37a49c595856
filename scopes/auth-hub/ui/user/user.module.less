@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

.userAvatarContainer {
  position: relative;

  // overflow: hidden;
  &:hover {
    .userEditorContainer {
      display: block;
    }
  }

  .userEditorContainer {
    position: absolute;
    display: none;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 999;
    border-radius: 50%;
    text-align: center;

    &:hover {
      background-color: fade(@black, 65%);

      .editOutlined {
        color: @white;
        cursor: pointer;
      }
    }
  }

}
