---
description: '用户 UI 组件'
labels: ['ui', 'user', 'avatar', 'link']
---

## 概述

将会提供 2 种形式的渲染：

- 渲染用户的头像及用户名

- 超链接 🔗：链接至用户管理下的某个用户的详情页面（后期会替换成用户资料页面）

### 使用

#### User

```js
<User size={64} id={777} />
<User size={64} id={777} name="<PERSON>" />
```

#### User.Link

```js
// 仅提供 ID 时，会触发一次 HTTP 请求获取 ID 对应的用户信息
<User.Link id={0} />

// 同时提供 name 时，则直接渲染，不触发 HTTP 请求
<User.Link id={0} name="admin" />
```
