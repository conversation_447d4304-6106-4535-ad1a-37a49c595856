import React from 'react';

import type { UserJSON } from '@manyun/auth-hub.model.user';
import { fetchUsersByIdsWeb } from '@manyun/auth-hub.service.pm.fetch-users-by-ids';

export function useUser(object: Partial<UserJSON>) {
  const [user, setUser] = React.useState<UserJSON | null>(null);

  React.useEffect(() => {
    const { id, name, ...restUserProps } = object;
    const fallbackUser: UserJSON = {
      id: id === undefined ? -1 : id,
      name: name === undefined ? `未知（${id}）` : name,
      ...restUserProps,
    };
    if (object.id !== undefined && !object.name) {
      (async () => {
        const { data, error } = await fetchUsersByIdsWeb({ userIds: [object.id!] });
        if (error) {
          setUser(fallbackUser);
        } else {
          setUser(data.data[0]);
        }
      })();
    } else {
      setUser(fallbackUser);
    }
  }, [object.id]);

  return [user] as const;
}
