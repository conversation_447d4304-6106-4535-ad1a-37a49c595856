import React from 'react';
import { MemoryRouter as Router } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';

// @ts-ignore
import { registerWebMocks } from '@manyun/auth-hub.service.pm.fetch-users-by-ids/dist/fetch-users-by-ids.mock';
import { getMock } from '@manyun/service.request';

import { User } from './user';

export const LocalUser = () => (
  <ConfigProvider>
    <User size={64} id={777} name="<PERSON>" />
  </ConfigProvider>
);

export const LocalUserLink = () => (
  <ConfigProvider>
    <Router>
      <User.Link id={777} name="<PERSON>" />
    </Router>
  </ConfigProvider>
);

export const RemoteUserLink = ({ delayResponse = 2 * 1000 }: { delayResponse?: number }) => {
  const [initialized, update] = React.useState(false);

  React.useEffect(() => {
    const mock = getMock('web', { delayResponse });
    registerWebMocks(mock);
    update(true);
  }, []);

  if (!initialized) {
    return <span>INITIALIZING...</span>;
  }

  return (
    <ConfigProvider>
      <Router>
        <User.Link id={1} />
      </Router>
    </ConfigProvider>
  );
};
