import React from 'react';
import { Link } from 'react-router-dom';

import {
  generateUserDetailRoutePath,
  generateUserProfileRoutePath,
} from '@manyun/auth-hub.route.auth-routes';

import { useUser } from './user.hook';

export type UserLinkProps = {
  /**
   * 用户 ID（数据库自增 ID）
   */
  id?: number | null; // TODO：目前外部有的使用会传 null，后续等修正后移出 null 类型
  /**
   * 用户名
   */
  name?: string;
  useNativeLink?: boolean;
  /**
   * 应用场景
   *
   * 根据应用场景分配不同的链接
   *
   * - 查看用户信息，跳转到个人中心
   * - 编辑用户信息，跳转到用户编辑页面
   *
   * 默认跳转到个人中心
   * */
  application?: 'read' | 'edit';
} & Record<string, any>;

/** @deprecated Replaced by `@manyun/auth-hub.ui.user-link` */
export function UserLink({
  id,
  name,
  useNativeLink = true,
  application = 'read',
  ...restProps
}: UserLinkProps) {
  const [user] = useUser({
    id: id !== null ? id : undefined,
    name,
  });
  if (user === null) {
    return <span>Loading...</span>;
  }

  if (!user?.name || id === undefined || id === null) {
    return <span>--</span>;
  }

  if (user?.name === 'SYSTEM') {
    return <span>系统</span>;
  }
  const url =
    application === 'read'
      ? generateUserProfileRoutePath({ id: id.toString() })
      : generateUserDetailRoutePath({ id, showDrawer: true });

  if (useNativeLink) {
    return (
      <a href={url} target="_blank" rel="noreferrer" {...restProps}>
        {user.name}
      </a>
    );
  } else {
    return (
      <Link to={url} {...restProps}>
        {user.name}
      </Link>
    );
  }
}
