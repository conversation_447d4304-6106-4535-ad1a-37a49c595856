import { EditOutlined } from '@ant-design/icons';
import type { ChangeEvent } from 'react';
import React, { useCallback, useEffect, useRef, useState } from 'react';

import { mutateUser } from '@manyun/auth-hub.service.mutate-user';
import { Avatar } from '@manyun/base-ui.ui.avatar';
import type { AvatarProps } from '@manyun/base-ui.ui.avatar';
import { ImageCropper } from '@manyun/base-ui.ui.image-cropper';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import type { SpaceProps } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { McUploadFile } from '@manyun/dc-brain.model.mc-upload-file';
import { fetchBizFileInfos } from '@manyun/dc-brain.service.fetch-biz-file-infos';
import { uploadFullFile } from '@manyun/dc-brain.service.upload-full-file';

import { useUser } from './user.hook';
import { UserLink } from './user.link';
import styles from './user.module.less';

export type UserProps = {
  style?: React.CSSProperties;
  className?: string;
  avatarStyle?: React.CSSProperties;
  avatarClassName?: string;
  /**
   * 用户 ID（数据库自增 ID）
   */
  id: number;
  /**
   * 用户名
   */
  name?: string;
  /**
   * 同时显示用户名
   */
  showName?: boolean;
  /**
   * 是否可编辑
   */
  editable?: boolean;
  /**
   * 方向
   */
  direction?: SpaceProps['direction'];
  size?: number;
  showAvatar?: boolean;
  disabled?: boolean;
  nameStyle?: Record<string, any>;
  nameType?: any;
} & Pick<AvatarProps, 'gap' | 'shape'>;

export function User({
  style,
  className,
  avatarStyle,
  avatarClassName,
  id,
  name,
  nameType = 'default',
  showName = true,
  editable = false,
  direction = 'vertical',
  size = 32,
  showAvatar = true,
  disabled = false,
  nameStyle = {},
  ...avatarProps
}: UserProps) {
  const [user] = useUser({ id, name });
  const [avatarUrl, setAvatarUrl] = useState<string>();

  useEffect(() => {
    (async () => {
      if (!id) {
        return;
      }
      const { error, data } = await fetchBizFileInfos({
        targetId: id.toString(),
        targetType: 'USER',
      });
      if (error) {
        message.error(error.message);
        return;
      }
      if (data.data.length > 0) {
        setAvatarUrl(data.data[0].src);
      }
    })();
  }, [id]);

  if (user === null) {
    return <span>Loading...</span>;
  }

  const firstLetter = user?.name?.substring(0, 1);
  const avatar = (
    <div className={styles.userAvatarContainer}>
      <Avatar
        style={{
          backgroundColor: disabled ? '#BFBFBF' : 'var(--manyun-primary-color)',
          ...avatarStyle,
        }}
        className={avatarClassName}
        src={avatarUrl}
        size={size}
        {...avatarProps}
      >
        {firstLetter}
      </Avatar>
      {editable && (
        <div
          style={{ width: size, height: size, lineHeight: `${size}px` }}
          className={styles.userEditorContainer}
        >
          <UserAvatarEdit id={id} onChange={setAvatarUrl} />
        </div>
      )}
    </div>
  );

  if (!showName) {
    return avatar;
  }

  if (!showAvatar) {
    return user.name;
  }

  return (
    <Space
      style={style}
      className={className}
      size="small"
      direction={direction ?? 'vertical'}
      align="center"
    >
      {avatar}
      {showName && (
        <Typography.Text
          type={nameType}
          style={{ width: '100%', ...nameStyle }}
          ellipsis={{ tooltip: true }}
        >
          {user.name}
        </Typography.Text>
      )}
    </Space>
  );
}

const MAX_FILE_SIZE = 5 * 1024 * 1024; // 文件最大限制为5M
export type UserAvatarEditProps = {
  id: number;
  onChange?: (url: string) => void;
};

export function UserAvatarEdit({ id, onChange }: UserAvatarEditProps) {
  const [file, setFile] = useState<File>();
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const fileRef = useRef<HTMLInputElement | null>(null);
  const instanceRef = useRef<Cropper | null>(null);

  const handleFileChange = (e: ChangeEvent<HTMLInputElement>) => {
    if (e.target.files !== null) {
      const file = e.target.files[0];
      if (file) {
        if (file.size <= MAX_FILE_SIZE) {
          setModalVisible(true);
          setFile(pre => file);
        } else {
          message.error('文件过大');
        }
      }
      if (fileRef.current) {
        fileRef.current.value = '';
      }
    }
  };

  const updateUserAvatarService = useCallback(
    async (avatar: McUploadFile) => {
      const { error } = await mutateUser({
        id,
        avatar,
      });
      setConfirmLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setModalVisible(false);
      message.success('头像修改成功!');
      onChange && onChange(avatar.src);
    },
    [id, onChange]
  );

  const handleSubmitFile = useCallback(async () => {
    if (instanceRef.current != null) {
      instanceRef.current.getCroppedCanvas().toBlob(async (blob: Blob | null) => {
        setConfirmLoading(true);
        if (blob == null) {
          return;
        }

        const param = new File([blob], (file as File).name);
        const { error, data } = await uploadFullFile(param);

        if (error) {
          message.error(error.message);
          setConfirmLoading(false);
          return;
        }
        if (data && data?.length > 0) {
          const file = data[0];
          file.targetId = `${id}`;
          file.targetType = 'USER';
          file.type = 'AVATAR';
          updateUserAvatarService(file);
        }
      });
    }
  }, [file, id, updateUserAvatarService]);

  return (
    <div>
      <label>
        <EditOutlined size={24} className={styles.editOutlined} />
        <input
          ref={fileRef}
          type="file"
          accept="image/jpeg,image/jpg,image/png"
          style={{ display: 'none' }}
          onChange={handleFileChange}
        />
      </label>
      <Modal
        style={{ top: 10 }}
        width={900}
        title="修改头像"
        open={modalVisible}
        confirmLoading={confirmLoading}
        onCancel={() => {
          setModalVisible(false);
        }}
        onOk={handleSubmitFile}
      >
        <ImageCropper
          src={file as File}
          previewStyle={{
            borderRadius: '50%',
          }}
          onInitialized={cropper => {
            instanceRef.current = cropper;
          }}
        />
      </Modal>
    </div>
  );
}

User.Link = UserLink;
