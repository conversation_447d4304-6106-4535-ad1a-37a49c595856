import React from 'react';

import { Select } from '@manyun/base-ui.ui.select';
import type { SelectProps } from '@manyun/base-ui.ui.select';

import type { AuditLogType } from '@manyun/auth-hub.model.audit-log';

/** 审计日志-日志类型 map */
export const auditLogTypeMap: Record<AuditLogType, string> = {
  INSPECT_OFFLINE_DATA: '巡检日志',
  APPROVE_OFFLINE_DATA: '审批日志',
  MAINTENANCE_OFFLINE_DATA: '维护日志',
};

export type AuditLogTypeSelectProps = Pick<
  SelectProps,
  'mode' | 'allowClear' | 'className' | 'placement' | 'size' | 'disabled' | 'style' | 'className'
>;

export function AuditLogTypeSelect(props: AuditLogTypeSelectProps) {
  const options = [
    {
      key: 'INSPECT_OFFLINE_DATA',
      label: auditLogTypeMap.INSPECT_OFFLINE_DATA,
      value: 'INSPECT_OFFLINE_DATA',
    },
    {
      key: 'APPROVE_OFFLINE_DATA',
      label: auditLogTypeMap.APPROVE_OFFLINE_DATA,
      value: 'APPROVE_OFFLINE_DATA',
    },
    {
      key: 'MAINTENANCE_OFFLINE_DATA',
      label: auditLogTypeMap.MAINTENANCE_OFFLINE_DATA,
      value: 'MAINTENANCE_OFFLINE_DATA',
    },
  ];
  return <Select {...props} options={options} />;
}

export type AuditLogTypeTextProps = {
  style?: React.CSSProperties;
  className?: string;
  logType: AuditLogType;
};

export function AuditLogTypeText({ logType, ...rest }: AuditLogTypeTextProps) {
  return <span {...rest}>{auditLogTypeMap[logType]}</span>;
}
