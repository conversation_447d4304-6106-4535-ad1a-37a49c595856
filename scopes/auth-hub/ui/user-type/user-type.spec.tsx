import React from 'react';

import { render, screen, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { BasicUserTypeSelect, BasicUserTypeText } from './user-type.composition';

it('user-type select', async () => {
  const { getByRole } = render(<BasicUserTypeSelect />);
  userEvent.click(getByRole('combobox'));
  await waitFor(() => {
    expect(screen.getByText('供应商')).toBeInTheDocument();
  });
});

it('user-type text', async () => {
  const { getByText } = render(<BasicUserTypeText />);
  const rendered = getByText('客户');
  expect(rendered).toBeTruthy();
});
