import React from 'react';

import type { RadioGroupProps } from 'antd/es/radio';

import { Radio } from '@manyun/base-ui.ui.radio';
import { Select } from '@manyun/base-ui.ui.select';
import type { RefSelectProps, SelectProps } from '@manyun/base-ui.ui.select';

import type { UserType } from '@manyun/auth-hub.model.user';

export const userTypeTextMapper = {
  CUSTOMER: '客户',
  VENDOR: '供应商',
  STAFF: '员工',
  OUTSOURCE_STAFF: '外包人员',
} as Record<UserType, string>;

const options = Object.entries(userTypeTextMapper).map(([key, value]) => ({
  label: value,
  value: key,
}));

export type UserTypeSelectProps = { omitOptions?: UserType[] } & SelectProps;

export const UserTypeSelect = React.forwardRef(
  ({ omitOptions, ...props }: UserTypeSelectProps, ref?: React.Ref<RefSelectProps>) => {
    const selectProps = {
      ...props,
      options: options.filter(opt => !omitOptions?.includes(opt.value as UserType)),
    };

    return <Select ref={ref} style={{ width: 200 }} {...selectProps} />;
  }
);
UserTypeSelect.displayName = 'UserTypeSelect';

export type UserTypeTextProps = {
  style?: React.CSSProperties;
  userType: UserType;
};

export function UserTypeText({ userType, style }: UserTypeTextProps) {
  return <span style={style}>{userTypeTextMapper[userType]}</span>;
}

export type UserTypeRadioProps = RadioGroupProps;

export function UserTypeRadio(props: UserTypeRadioProps) {
  return <Radio.Group {...props} options={options} />;
}
