import React from 'react';

import { useRoles } from '@manyun/auth-hub.hook.use-roles';
import { useDeepCompareMemo } from '@manyun/base-ui.hook.use-deep-compare-memo';
import { Select, SelectProps } from '@manyun/base-ui.ui.select';

// TODO: @Jerry import this type from `@manyun/base-ui.ui.select`
type RefSelectProps = {
  focus: () => void;
  blur: () => void;
};

export type RoleSelectProps<VT = any> = {
  trigger?: 'onFocus' | 'onDidMount';
  disabledKeys?: string[];
} & SelectProps<VT>;

export const RoleSelect = React.forwardRef(
  (
    { trigger, disabledKeys = [], ...selectProps }: RoleSelectProps,
    ref?: React.Ref<RefSelectProps>
  ) => {
    const [{ loading, data }, getRoles] = useRoles({});
    const options = useDeepCompareMemo(
      () =>
        (data?.data || []).map(({ code, ...rest }) => ({
          disabled: disabledKeys.includes(code),
          code,
          ...rest,
        })),
      [data?.data, disabledKeys]
    );

    React.useEffect(() => {
      if (trigger === 'onDidMount') {
        getRoles();
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [trigger]);

    return (
      <Select
        ref={ref}
        showSearch
        optionLabelProp="name"
        optionFilterProp="name"
        {...selectProps}
        loading={loading}
        options={options as any[]}
        onFocus={() => {
          getRoles();
        }}
      />
    );
  }
);

RoleSelect.displayName = 'RoleSelect';

RoleSelect.defaultProps = {
  fieldNames: {
    value: 'id',
    label: 'name',
  },
};
