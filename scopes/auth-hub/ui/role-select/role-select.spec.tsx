import React from 'react';

import { render, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// @ts-ignore
import { registerWebMocks } from '@manyun/auth-hub.service.pm.fetch-roles/dist/fetch-roles.mock';
import { getMock } from '@manyun/service.request';

import { BasicRoleSelect } from './role-select.composition';

const mock = getMock('web', { delayResponse: 777 });
registerWebMocks(mock);

test('should render with the correct text', async () => {
  const { getByRole, getByText } = render(<BasicRoleSelect />);
  userEvent.click(getByRole('combobox'));
  await waitFor(() => {
    const rendered = getByText('角色名_0');
    expect(rendered).toBeTruthy();
  });
});
