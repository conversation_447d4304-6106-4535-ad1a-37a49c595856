---
description: '角色选择器'
labels: ['role', 'select']
---

### 使用

```js
// 默认组件会在 `onFocus` 时获取角色数据
<RoleSelect />

// 组将将在 `did mount` 时获取角色数据
// 一般用于已知选中项的 `value`，想要
// 在组件在渲染出对应的 `name`
<RoleSelect trigger="onDidMount" value={1} />
```

### ⚠️&nbsp;默认的 `select option value`

已选项的 `value` 为角色 `ID`，若要使用角色 `code` 作为 `value`，请使用 `fieldNames` 指定：

```js
<RoleSelect fieldNames={{ value: 'code', label: 'name' }} />
```
