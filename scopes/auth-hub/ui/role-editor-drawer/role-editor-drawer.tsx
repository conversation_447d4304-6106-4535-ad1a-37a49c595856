import React from 'react';

import { createRole } from '@manyun/auth-hub.service.create-role';
import { updateRole } from '@manyun/auth-hub.service.update-role';
import { ResourceTypeSelect } from '@manyun/auth-hub.ui.resource-type-select';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { Button } from '@manyun/base-ui.ui.button';
import { Drawer } from '@manyun/base-ui.ui.drawer';
import type { DrawerProps } from '@manyun/base-ui.ui.drawer';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import type { WebsiteCode } from '@manyun/dc-brain.model.website';
import { AppsSelect } from '@manyun/dc-brain.ui.apps-select';
import type { BackendResourceType } from '@manyun/iam.model.auth-resource';
import { fetchRole } from '@manyun/iam.service.fetch-role';

type FormValue = {
  code: string;
  name: string;
  website: WebsiteCode;
  approveId: number;
  resourceType?: BackendResourceType;
  remarks?: string;
};

export type RoleEditorDrawerProps = Omit<
  DrawerProps,
  'visible' | 'children' | 'extra' | 'onClose'
> & {
  id?: number;
  afterSubmit?: () => void;
  onClose?: () => void;
};

export function RoleEditorDrawer({
  id,
  onClose,
  afterSubmit,
  open,
  ...rest
}: RoleEditorDrawerProps) {
  const [form] = Form.useForm<FormValue>();
  const mode = id !== undefined ? 'edit' : 'new';
  const [loading, setLoading] = React.useState(false);

  const [initialValues, setInitialValues] = React.useState<FormValue>();
  React.useEffect(() => {
    (async () => {
      if (mode === 'edit' && open) {
        setLoading(true);
        const { error, data: role } = await fetchRole({ id });
        setLoading(false);
        if (error) {
          message.error(error.message);
          return;
        }
        if (!role) {
          message.error('获取角色详情失败');
          return;
        }
        const values = {
          code: role.code,
          name: role.name,
          website: role.website,
          approveId: role.approveId,
          resourceType: role.resourceType?.split(','),
          remarks: role.remarks,
        };
        setInitialValues(values);
        form.setFieldsValue(values);
      }
    })();
  }, [form, id, mode, open]);

  const onSubmit = React.useCallback(async () => {
    const value = await form.validateFields();
    setLoading(true);
    if (mode === 'edit') {
      const { error } = await updateRole({
        ...value,
        id: id!,
        resourceType: Array.isArray(value.resourceType)
          ? value.resourceType?.join(',')
          : value.resourceType,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('编辑角色成功');
    } else {
      const { error } = await createRole({
        ...value,
        resourceType: (Array.isArray(value.resourceType)
          ? value.resourceType?.join(',')
          : value.resourceType) as string,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('新建角色成功');
    }
    form.resetFields();
    afterSubmit?.();
  }, [afterSubmit, form, id, mode]);

  const handleClose = React.useCallback(() => {
    form.resetFields();
    onClose?.();
  }, [form, onClose]);

  return (
    <Drawer
      width={600}
      title={`${mode === 'edit' ? '编辑' : '新建'}角色`}
      placement="right"
      open={open}
      onClose={handleClose}
      {...rest}
      extra={
        <Space>
          <Button onClick={handleClose}>取消</Button>
          <Button type="primary" loading={loading} onClick={onSubmit}>
            提交
          </Button>
        </Space>
      }
    >
      <Form
        initialValues={initialValues}
        form={form}
        labelCol={{ flex: '0 0 110px' }}
        wrapperCol={{ flex: 1 }}
      >
        <Form.Item
          label="角色 ID"
          name="code"
          rules={[
            { required: true, message: '必须输入角色 ID' },
            {
              max: 64,
              message: '最多输入 64 个字符！',
            },
            {
              pattern: /^[a-zA-Z0-9-]+$/,
              message: '角色 ID 只能由英文、数字或 “-” 组成',
            },
          ]}
        >
          <Input style={{ width: 240 }} disabled={mode === 'edit'} />
        </Form.Item>
        <Form.Item
          label="角色名称"
          name="name"
          rules={[
            { required: true, whitespace: true, message: '必须输入角色名称' },
            {
              max: 24,
              message: '最多输入 24 个字符！',
            },
          ]}
        >
          <Input style={{ width: 240 }} />
        </Form.Item>
        <Form.Item
          label="角色负责人"
          name="approveId"
          rules={[{ required: true, message: '必须选择角色负责人' }]}
        >
          <UserSelect style={{ width: 240 }} labelInValue={false} />
        </Form.Item>
        <Form.Item label="资源类型" name="resourceType">
          <ResourceTypeSelect
            mode="multiple"
            style={{ width: 240 }}
            allowClear
            hiddenTypes={['DEPT']}
            disabled={!!id}
          />
        </Form.Item>
        <Form.Item
          label="备注"
          name="remarks"
          rules={[
            {
              max: 128,
              message: '最多输入 128 个字符！',
            },
          ]}
        >
          <Input.TextArea style={{ width: '100%' }} rows={4} />
        </Form.Item>
      </Form>
    </Drawer>
  );
}
