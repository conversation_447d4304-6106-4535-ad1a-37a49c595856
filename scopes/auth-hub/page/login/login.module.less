@import '~@manyun/base-ui.theme.theme/dist/theme.variable.less';

@card-font-color: #151d3d;
@tabs-color: rgba(56, 63, 90, 0.78);
@tabs-hover-color: #151d3d;
@tabs-active-color: #151d3d;
@taba-nav-top-border-color: rgba(56, 63, 90, 0.1);
@input-bottom-border-color: rgba(56, 63, 90, 0.1);
@form-margin-left: 9px;
@title-color: #383f5a;

.loginContainer {
  margin: 0px;
  padding: 0px;
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  background-image: url('./asset/login-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  position: relative;

  .waveLeftTop {
    z-index: 9;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    img {
      object-fit: cover;
    }
  }

  .waveRightBottom {
    z-index: 9;
    position: absolute;
    bottom: 0;
    right: 0;
    img {
      object-fit: cover;
    }
  }

  .loginWarp {
    width: 981px;
    height: 588px;
    background-color: transparent;
    background-repeat: no-repeat;
    background-size: cover;
    background-position: center;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 34px 90px 16px 95px;

    z-index: 99;
    .loginContent {
      .loginLogo {
        display: flex;
        justify-content: center;
        align-items: center;

        .loginTitile {
          margin-left: 16px;
          font-size: 28px;
          font-family:
            PingFangSC-Semibold,
            PingFang SC;
          font-weight: 600;
          color: @title-color;
        }
      }

      .loginCard {
        width: 334px;
        height: 426px;
        background: @white;
        box-shadow: 0px 0px 8px 2px rgba(21, 29, 61, 0.05);
        padding: 24px;
      }
    }
  }

  .loginForm {
    margin-top: 24px;

    :global {
      // tabs
      .manyun-tabs {
        color: @tabs-color;
      }
      .manyun-tabs > .manyun-tabs-nav :hover,
      .manyun-tabs > div > .manyun-tabs-nav :hover {
        color: #151d3d;
      }
      .manyun-tabs-large > .manyun-tabs-nav .manyun-tabs-tab {
        padding: 8px 0;
      }
      .manyun-tabs-top > .manyun-tabs-nav::before,
      .manyun-tabs-bottom > .manyun-tabs-nav::before,
      .manyun-tabs-top > div > .manyun-tabs-nav::before,
      .manyun-tabs-bottom > div > .manyun-tabs-nav::before {
        border-color: @taba-nav-top-border-color;
      }
      .manyun-tabs-tab.manyun-tabs-tab-active .manyun-tabs-tab-btn {
        color: @tabs-active-color;
      }
      .manyun-tabs-ink-bar {
        background-color: @tabs-active-color;
      }
    }
    .formContainer {
      margin-top: 6px;
      :global {
        //input
        .manyun-form-item {
          margin-bottom: 12px;
        }
        .manyun-input,
        .manyun-input-affix-wrapper {
          background-color: @white;
          color: @card-font-color;
          border-color: @white;
          padding: 4px @form-margin-left 7px;
          border-bottom-color: @input-bottom-border-color;
          border-radius: 0;
        }

        .manyun-input-affix-wrapper {
          padding: 0;
        }

        .manyun-input:focus,
        .manyun-input-wrapper-focused,
        .manyun-input-affix-wrapper:focus,
        .manyun-input-affix-wrapper-focused {
          box-shadow: none;
          border-bottom-color: rgba(21, 29, 61, 0.7);
        }

        input:-webkit-autofill,
        input:-webkit-autofill:hover,
        input:-webkit-autofill:focus,
        .manyun-form-item-has-error
          :not(.manyun-input-disabled):not(.manyun-input-borderless).manyun-input:focus,
        .manyun-form-item-has-error
          :not(.manyun-input-affix-wrapper-disabled):not(
            .manyun-input-affix-wrapper-borderless
          ).manyun-input-affix-wrapper:focus,
        .manyun-form-item-has-error
          :not(.manyun-input-disabled):not(.manyun-input-borderless).manyun-input-focused,
        .manyun-form-item-has-error
          :not(.manyun-input-affix-wrapper-disabled):not(
            .manyun-input-affix-wrapper-borderless
          ).manyun-input-affix-wrapper-focused {
          border-color: @white;
          border-bottom-color: @input-bottom-border-color;
          caret-color: @card-font-color;
          -webkit-text-fill-color: @card-font-color;
          box-shadow: 0 0 0px 1000px @white inset;
          transition: @white 5000s ease-in-out 0s;
          border-radius: 0;
        }

        .manyun-form-item-has-error
          :not(.manyun-input-disabled):not(.manyun-input-borderless).manyun-input,
        .manyun-form-item-has-error
          :not(.manyun-input-affix-wrapper-disabled):not(
            .manyun-input-affix-wrapper-borderless
          ).manyun-input-affix-wrapper {
          border-color: @white;
          border-bottom-color: @input-bottom-border-color;
          border-radius: 0;
        }
        .manyun-form-item-has-error
          :not(.manyun-input-disabled):not(.manyun-input-borderless).manyun-input:hover,
        .manyun-form-item-has-error
          :not(.manyun-input-affix-wrapper-disabled):not(
            .manyun-input-affix-wrapper-borderless
          ).manyun-input-affix-wrapper:hover {
          border-color: @white;
          border-bottom-color: var(--manyun-error-color);
          border-radius: 0;
        }

        .manyun-form-item-has-error .manyun-input,
        .manyun-form-item-has-error .manyun-input-affix-wrapper,
        .manyun-form-item-has-error .manyun-input:hover,
        .manyun-form-item-has-error .manyun-input-affix-wrapper:hover {
          background-color: @white;
        }

        .manyun-form-item-explain-error {
          margin-left: @form-margin-left;
        }

        .manyun-input-password-icon {
          color: rgba(21, 29, 61, 0.85);
        }

        //checkbox
        .manyun-checkbox {
          color: @card-font-color;
        }

        .manyun-checkbox-inner,
        .manyun-checkbox-checked .manyun-checkbox-inner {
          background-color: @white;
          border: 1px solid #151d3d;
        }

        .manyun-checkbox-checked .manyun-checkbox-inner::after,
        .manyun-checkbox-checked::after {
          border-color: @card-font-color;
        }
      }

      .formInputTips {
        font-size: 12px;
        font-family:
          PingFangSC-Regular,
          PingFang SC;
        font-weight: 400;
        color: rgba(56, 63, 90, 0.78);
        margin-left: @form-margin-left;
      }

      .formForgot {
        float: right;
        font-size: 12px;
        font-weight: 400;
        color: @tabs-color;
        padding-right: 10px;
        margin-top: 2px;
      }

      .formCheckBox {
        margin-left: @form-margin-left;
        color: @card-font-color;
      }

      .formBtn {
        width: 100%;
        background: rgba(21, 29, 61, 0.85);
        color: @white;
        height: 36px;
        border-radius: 36px;
        margin-top: 20px;
      }
    }
  }

  .footer {
    position: absolute;
    bottom: 24px;
    width: 100%;
    box-sizing: border-box;
    text-align: center;
    font-size: 12px;
    color: @text-color-secondary-dark;
  }
}
