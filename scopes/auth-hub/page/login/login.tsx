import dayjs from 'dayjs';
import React, { useCallback, useEffect } from 'react';
import { useDispatch } from 'react-redux';

import { userSliceActions } from '@manyun/auth-hub.state.user';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import configFromPkg from '@manyun/dc-brain.config.base/configs/default.json';
import { ConfigUtil } from '@manyun/dc-brain.util.config';

import WaveLeftTopFirst from './asset/left-top-first.png';
import WavebeLeftTopSecond from './asset/left-top-second.png';
import WaveRightBottom from './asset/right-bottom.png';
import { LoginForm } from './login-form';
import styles from './login.module.less';

export type LoginProps = {};

export function Login() {
  const dispatch = useDispatch();
  // @ts-ignore ts(2345)
  const configUtil = new ConfigUtil(configFromPkg);

  useEffect(() => {
    getCurrentPosition();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [navigator]);

  const getCurrentPosition = useCallback(() => {
    if ('geolocation' in navigator) {
      navigator.geolocation.getCurrentPosition(
        position => {
          const geolocation = `lat:${position.coords.latitude},lng:${position.coords.longitude}`;
          dispatch(userSliceActions.setCurrentPosition(geolocation));
        },
        error => {
          if (error.code === 1) {
            console.warn(error.message);
          }
        }
      );
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, navigator]);

  return (
    <div className={styles.loginContainer}>
      <div className={styles.waveLeftTop}>
        <img src={WaveLeftTopFirst} alt="wave" width={455} height={285} />
      </div>
      <div className={styles.waveLeftTop}>
        <img src={WavebeLeftTopSecond} alt="wave" width={1221} height={488} />
      </div>
      <div className={styles.waveRightBottom}>
        <img src={WaveRightBottom} alt="wave" height="auto" width={1280} />
      </div>
      <div className={styles.loginWarp} style={{ backgroundImage: 'url(/images/login-warp.png)' }}>
        <div style={{ flex: 1 }} />
        <div className={styles.loginContent}>
          <div className={styles.loginCard}>
            <div className={styles.loginLogo}>
              <img src="/images/logo-login.png" alt="Logo" height={32} />
            </div>
            <LoginForm />
          </div>
        </div>
      </div>
      {configUtil.showCopyright() && (
        <div className={styles.footer}>
          Copyright ©{dayjs().year()} Produced by {env.APP_NAME} Department
        </div>
      )}
    </div>
  );
}
