import React from 'react';

export function EditIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="manyun-icon"
      viewBox="0 0 1024 1024"
      version="1.2"
      width="1em"
      height="1em"
    >
      <path
        d="M234.666667 928A138.389333 138.389333 0 0 1 96 789.333333V234.666667A138.389333 138.389333 0 0 1 234.666667 96h277.333333a53.333333 53.333333 0 0 1 0 106.666667H234.666667A32.810667 32.810667 0 0 0 202.666667 234.666667v554.666666a32.810667 32.810667 0 0 0 32 32h554.666666a32.810667 32.810667 0 0 0 32-32V512a53.333333 53.333333 0 0 1 106.666667 0v277.333333a138.389333 138.389333 0 0 1-138.666667 138.666667z"
        fill="#ffffff"
      />
      <path
        d="M522.666667 554.666667a54.762667 54.762667 0 0 1-38.4-14.933334 51.541333 51.541333 0 0 1 0-74.666666l298.666666-298.666667a52.8 52.8 0 0 1 74.666667 74.666667l-298.666667 298.666666a46.784 46.784 0 0 1-36.266666 14.933334z"
        fill="#ffffff"
      />
    </svg>
  );
}

export function OnTimeCircleOutlinedIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="manyun-icon"
      viewBox="0 0 1024 1024"
      version="1.2"
      width="1.5em"
      height="1.5em"
    >
      <path
        fill="#778693"
        d="M512 544h-128a32 32 0 1 1 0-64h96v-128a32 32 0 1 1 64 0v160a32 32 0 0 1-32 32z m0-320a32 32 0 1 1 0 64 224 224 0 1 0 224 224 32 32 0 0 1 64 0 288 288 0 1 1-288-288z m267.52 172.48a32 32 0 1 1-56 31.04l-15.488-28a32 32 0 1 1 55.968-31.04l15.52 28z m-114.464-128.48a32 32 0 0 1-34.048 54.176l-27.104-17.024a32 32 0 1 1 34.08-54.176l27.072 17.024z"
      />
    </svg>
  );
}

export function ChecklistOutlinedIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="manyun-icon"
      viewBox="0 0 1024 1024"
      version="1.2"
      width="1.5em"
      height="1.5em"
    >
      <path
        fill="#778693"
        d="M672 288h64a64 64 0 0 1 64 64v384a64 64 0 0 1-64 64H288a64 64 0 0 1-64-64V352a64 64 0 0 1 64-64h64V256a32 32 0 1 1 64 0v32h192V256a32 32 0 0 1 64 0v32zM288 352v384h448V352H288z m227.872 150.624l45.28-45.248a32 32 0 0 1 45.248 45.248l-45.248 45.248 45.248 45.28a32 32 0 0 1-45.248 45.248l-45.28-45.248-45.248 45.248a32 32 0 1 1-45.248-45.248l45.248-45.28-45.248-45.248a32 32 0 0 1 45.248-45.248l45.248 45.248z"
      />
    </svg>
  );
}

export function LineChartOutlinedIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="manyun-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      width="1em"
      height="1em"
    >
      <path
        fill="#778693"
        d="M143.06 875.725h782.919c24.3 0 44 19.7 44 44s-19.7 44-44 44H99.059c-24.3 0-44-19.7-44-44V107.303c0-24.3 19.7-44 44-44 24.301 0 44 19.7 44 44v768.422z m344.207-383.337L422.44 707.446c-10.815 35.877-58.663 42.679-79.048 11.237L192.08 485.288c-13.22-20.39-7.406-47.636 12.984-60.856 20.39-13.219 47.637-7.406 60.856 12.985l99.666 153.731 42.953-142.495a44 44 0 0 1 22.714-26.786l179.456-88.232c19.512-9.593 43.106-3.496 55.527 14.35l85.234 122.458 123.205-358.735c7.893-22.983 32.923-35.215 55.906-27.322 22.983 7.893 35.215 32.923 27.322 55.906l-151.38 440.772c-11.701 34.07-57.148 40.41-77.728 10.844l-113.188-162.62-128.34 63.1z"
      />
    </svg>
  );
}

export function CloseCircleOutlined() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 1024 1024"
      version="1.1"
      className="manyun-icon"
      width="1em"
      height="1em"
    >
      <path
        d="M942.792571 235.113605c-73.898726-114.998017-188.296753-194.296649-321.894449-223.296149-133.597696-28.9995-270.495335-4.199928-385.493352 69.698798-21.299633 13.699764-27.499526 42.099274-13.799762 63.398907 13.699764 21.299633 42.099274 27.499526 63.398907 13.799762 94.298374-60.698953 206.696435-80.998603 316.294545-57.199014 109.59811 23.79959 203.396492 88.898467 264.095445 183.196841 125.297839 194.796641 68.698815 455.19215-125.997827 580.489989-94.298374 60.698953-206.696435 80.998603-316.294545 57.199013-109.59811-23.79959-203.49649-88.898467-264.095445-183.19684-93.198393-144.997499-88.998465-329.594316 10.799814-470.191891 14.699746-20.699643 9.799831-49.29915-10.899813-63.998897-20.699643-14.699746-49.29915-9.799831-63.998896 10.899812-121.597903 171.397044-126.697815 396.293165-12.999776 572.990118 73.998724 114.998017 188.296753 194.296649 321.894449 223.29615 36.499371 7.899864 73.198738 11.799796 109.59811 11.799796 97.098325 0 192.196685-27.799521 275.795243-81.498594 237.295908-152.697367 306.194719-469.991894 153.597352-707.387801z"
        fill="#778693"
      />
      <path
        d="M356.302685 667.706145c8.999845 8.999845 20.699643 13.399769 32.49944 13.399769s23.499595-4.499922 32.499439-13.399769l90.798434-90.798434 90.798434 90.798434c8.999845 8.999845 20.699643 13.399769 32.49944 13.399769s23.499595-4.499922 32.499439-13.399769c17.899691-17.899691 17.899691-46.999189 0-64.898881l-90.99843-90.798434 90.798434-90.798434c17.899691-17.899691 17.899691-46.999189 0-64.898881-17.899691-17.899691-46.999189-17.899691-64.898881 0L512 447.109949l-90.798434-90.798434c-17.899691-17.899691-46.999189-17.899691-64.898881 0-17.899691 17.899691-17.899691 46.999189 0 64.898881l90.798434 90.798434-90.798434 90.798434c-17.899691 17.899691-17.899691 46.899191 0 64.898881z"
        fill="#778693"
      />
    </svg>
  );
}

export function ConcatMobileIcon() {
  return (
    <svg
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      className="manyun-icon"
      viewBox="0 0 1024 1024"
      width="1.6em"
      height="1.6em"
    >
      <path
        d="M512.000024 0C229.230775 0 0.000024 229.230751 0.000024 512S229.230775 1024 512.000024 1024 1024.000024 794.769249 1024.000024 512A511.990002 511.990002 0 0 0 512.000024 0z"
        fill="#49AA19"
      />
      <path
        d="M567.858023 250.856105a11.377556 11.377556 0 0 0-11.237586-11.937435h-89.240827A11.877448 11.877448 0 0 0 460.011193 242.057995a10.717697 10.717697 0 0 0-3.849173 8.778114 11.397551 11.397551 0 0 0 11.237586 11.937436h89.100857a11.407549 11.407549 0 0 0 11.247584-11.937436z m-56.177931 481.766496a35.65234 35.65234 0 0 0-25.624494 10.527738 36.7721 36.7721 0 1 0 25.634492-10.527738zM670.395993 310.543282H352.904204V712.956826h317.491789z m56.827791 436.82615a76.393587 76.393587 0 0 1-6.998496 32.652985 86.561403 86.561403 0 0 1-19.305853 26.694265A91.500342 91.500342 0 0 1 672.08563 824.932768a93.819843 93.819843 0 0 1-35.502372 6.678566H387.326809a93.899826 93.899826 0 0 1-35.512371-6.678566A91.450352 91.450352 0 0 1 323.04062 806.716682a86.591396 86.591396 0 0 1-19.31585-26.694265 76.533557 76.533557 0 0 1-6.998496-32.652985V276.840523a78.853059 78.853059 0 0 1 6.998496-33.062897 85.401652 85.401652 0 0 1 19.31585-26.994201 91.340376 91.340376 0 0 1 28.833806-18.216086 93.859835 93.859835 0 0 1 35.51237-6.668567H636.673238a93.859835 93.859835 0 0 1 35.512371 6.668567A91.640312 91.640312 0 0 1 700.959427 216.793423a85.371658 85.371658 0 0 1 19.305852 26.994201 78.673098 78.673098 0 0 1 6.998497 33.062896v470.518912z"
        fill="#FFFFFF"
      />
    </svg>
  );
}

export function ConcatEmailIcon() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      className="manyun-icon"
      viewBox="0 0 1024 1024"
      version="1.1"
      width="1.6em"
      height="1.6em"
    >
      <path d="M512.000024 512m-512 0a512 512 0 1 0 1024 0 512 512 0 1 0-1024 0Z" fill="#642AB5" />
      <path
        d="M788.200684 414.510945v243.147761a78.553123 78.553123 0 0 1-78.80307 78.193201H314.602433a78.553123 78.553123 0 0 1-78.803069-78.193201V418.650056l260.124114 148.748042a25.184589 25.184589 0 0 0 27.334127-1.529671l1.20974-0.599871z m-0.399914-57.217707L508.380801 516.998926 235.799364 361.242389a78.80307 78.80307 0 0 1 78.803069-73.094296h394.795181a78.483138 78.483138 0 0 1 78.403156 69.145145z"
        fill="#FFFFFF"
      />
    </svg>
  );
}
