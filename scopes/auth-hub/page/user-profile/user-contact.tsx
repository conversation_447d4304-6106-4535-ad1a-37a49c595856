import React from 'react';

import Icon from '@ant-design/icons';

import { FeiShuColorful } from '@manyun/base-ui.icons';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';

import type { UserJSON } from '@manyun/auth-hub.model.user';

import { ConcatEmailIcon, ConcatMobileIcon } from './manyun-icon';
import styles from './user-profile.module.less';

type UserContactProps = {
  user?: UserJSON;
  mobileNumber?: string;
  email?: string;
  feiShu?: string;
  editable?: boolean;
  children?: React.ReactNode;
  style?: React.CSSProperties;
};

export function UserContact({
  user,
  mobileNumber,
  email,
  feiShu,
  editable = false,
  children,
  style,
}: UserContactProps) {
  return (
    <div className={styles.userConcatRow} style={{ ...style }}>
      <Tooltip title={user?.mobileNumber || mobileNumber}>
        <Icon component={ConcatMobileIcon} />
      </Tooltip>
      <Tooltip title={user?.email || email}>
        <Icon component={ConcatEmailIcon} style={{ margin: '0 6px' }} />
      </Tooltip>
      <Tooltip title={feiShu}>
        <FeiShuColorful style={{ fontSize: '1.6em' }} />
      </Tooltip>
      {editable && children && children}
    </div>
  );
}
