/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useCallback, useState } from 'react';

import EditOutlined from '@ant-design/icons/es/icons/EditOutlined';
import moment from 'moment';

import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { message } from '@manyun/base-ui.ui.message';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Tooltip } from '@manyun/base-ui.ui.tooltip';
import { Typography } from '@manyun/base-ui.ui.typography';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import type { UserJSON } from '@manyun/auth-hub.model.user';
import type { SvcRespData as UserDetail } from '@manyun/auth-hub.service.fetch-user';
import { DeptText } from '@manyun/auth-hub.ui.dept-text';
import { UserLink } from '@manyun/auth-hub.ui.user';
import { UserGenderSelect, UserGenderText } from '@manyun/auth-hub.ui.user-gender';
import { UserTypeText } from '@manyun/auth-hub.ui.user-type';
import { RegionCascader } from '@manyun/dc-brain.ui.region-cascader';

import { useMutateUser } from './use-mutate-user';
import { UserContact } from './user-contact';
import styles from './user-profile.module.less';

const { Text } = Typography;

export type UserBioProps = {
  user: UserDetail;
  onChange?: (value: string, field: string) => void;
};

type CustomEditType = 'type' | 'gender' | 'birthday' | 'birthPlace' | 'company';

export function UserBio({ user, onChange }: UserBioProps) {
  const [updateUser] = useMutateUser();
  const [, { checkUserId }] = useAuthorized();
  const [editKey, setEditKey] = useState<null | CustomEditType>(null);

  const onUpdateUser = useCallback(
    (newTxt: string, field: string) => {
      if (user === null) {
        return;
      }
      if (field in user) {
        const oldVal = (user as any)[field];

        if (field === 'name' && newTxt === '') {
          message.error('姓名不能为空');
          return;
        }

        if (oldVal === newTxt) {
          return;
        }

        (user as any)[field] = newTxt;

        updateUser({
          id: user.id,
          [field]: newTxt,
        });

        setEditKey(null);

        if (['name', 'gender'].includes(field)) {
          onChange && onChange(newTxt, field);
        }
      }
    },
    [onChange, updateUser, user]
  );

  if (user === null) {
    return null;
  }
  /*** 是否有编辑权限 */
  const userEditable = checkUserId(user.id);

  return (
    <div className={styles.userBioContainer}>
      <Card title="基础信息" bordered={false}>
        <Descriptions column={2}>
          <Descriptions.Item label="姓名">
            <EditText
              value={user.name}
              maxLength={10}
              editable={userEditable}
              onChange={str => {
                onUpdateUser(str, 'name');
              }}
              onStart={() => setEditKey(null)}
            />
            {user.state === 'leaving' && <Tag style={{ marginLeft: '6px' }}>离职</Tag>}
          </Descriptions.Item>
          <Descriptions.Item label="英文名">
            <EditText
              value={user.nameEn}
              maxLength={30}
              editable={userEditable}
              onChange={str => {
                onUpdateUser(str, 'nameEn');
              }}
              onStart={() => setEditKey(null)}
            />
          </Descriptions.Item>
          <Descriptions.Item label="生日" contentStyle={{ alignItems: 'center' }}>
            <CustomEdit
              field="birthday"
              editKey={editKey}
              user={user}
              editable={userEditable}
              editableNode={
                <DatePicker
                  size="small"
                  allowClear={false}
                  defaultValue={
                    user.birthday && user.birthday !== 'Invalid date'
                      ? moment(user.birthday)
                      : undefined
                  }
                  onChange={value => {
                    const birthday = moment(value).format('YYYY-MM-DD');
                    onUpdateUser(birthday, 'birthday');
                  }}
                />
              }
              onEdit={setEditKey}
            />
          </Descriptions.Item>
          <Descriptions.Item label="性别" contentStyle={{ alignItems: 'center' }}>
            <CustomEdit
              user={user}
              field="gender"
              editKey={editKey}
              editable={userEditable}
              editableNode={
                <UserGenderSelect
                  defaultValue={user.gender}
                  size="small"
                  style={{ width: '80px' }}
                  onChange={val => {
                    onUpdateUser(val as string, 'gender');
                  }}
                  onBlur={() => {
                    setEditKey(null);
                  }}
                />
              }
              onEdit={setEditKey}
            />
          </Descriptions.Item>
          <Descriptions.Item label="家乡" contentStyle={{ alignItems: 'center' }}>
            <CustomEdit
              field="birthPlace"
              editKey={editKey}
              user={user}
              editable={userEditable}
              editableNode={
                <RegionCascader
                  defaultValue={user.birthPlace ? [user.birthPlace] : undefined}
                  size="small"
                  style={{ width: '170px' }}
                  onChange={(_: any, selectedOptions: any) => {
                    const place = selectedOptions.map((option: any) => option.label).join('/');
                    onUpdateUser(place, 'birthPlace');
                  }}
                />
              }
              onEdit={setEditKey}
            />
          </Descriptions.Item>
          <Descriptions.Item label="所属机房">{user.idc || '--'}</Descriptions.Item>
          <Descriptions.Item label="办公地点">
            <EditText
              value={user.workplace}
              maxLength={32}
              autoSize={{ minRows: 3 }}
              editable={userEditable}
              onChange={str => {
                onUpdateUser(str, 'workplace');
              }}
              onStart={() => setEditKey(null)}
            />
          </Descriptions.Item>
          <Descriptions.Item label="类型" contentStyle={{ alignItems: 'center' }}>
            <CustomEdit user={user} field="type" editKey={editKey} editable={false} />
          </Descriptions.Item>
          <Descriptions.Item label="联系方式" contentStyle={{ alignItems: 'center' }}>
            <UserContact user={user} editable={false} />
          </Descriptions.Item>
        </Descriptions>
      </Card>
      <Card title="工作信息" bordered={false} style={{ marginTop: '14px' }}>
        <Descriptions column={2}>
          {['CUSTOMER', 'VENDOR'].includes(user.type || '') && (
            <Descriptions.Item label="所属公司">
              <CustomEdit field="company" editKey={editKey} user={user} editable={false} />
            </Descriptions.Item>
          )}
          <Descriptions.Item label="部门">
            <DeptText deptId={user.departmentId} fromMdm={user.fromMdm} />
          </Descriptions.Item>
          <Descriptions.Item label="岗位">{user.title || '--'}</Descriptions.Item>
          <Descriptions.Item label="直线经理1" contentStyle={{ alignItems: 'center' }}>
            {user.supervisorUid ? <UserLink id={user.supervisorUid} /> : '--'}
          </Descriptions.Item>
          <Descriptions.Item label="直线经理2" contentStyle={{ alignItems: 'center' }}>
            <UserLink id={user.subSupervisorUid} />
          </Descriptions.Item>
          <Descriptions.Item label="工作职责">
            <EditText
              value={user.jobDescriptions}
              maxLength={50}
              autoSize={{ minRows: 3 }}
              editable={userEditable}
              onChange={str => {
                onUpdateUser(str, 'jobDescriptions');
              }}
              onStart={() => setEditKey(null)}
            />
          </Descriptions.Item>
          {user.resignDate && (
            <Descriptions.Item label="离职日期">
              {user.resignDate ? moment(user.resignDate).format('YYYY-MM-DD') : '--'}
            </Descriptions.Item>
          )}
        </Descriptions>
      </Card>
    </div>
  );
}

/**Custom Edit */
export type CustomEditProps = {
  field: CustomEditType;
  editKey: CustomEditType | null;
  user: UserJSON;
  editable?: boolean;
  editableNode?: React.ReactNode;
  onEdit?: (editKey: CustomEditType) => void;
};
export function CustomEdit({
  field,
  editKey,
  user,
  editable = true,
  editableNode,
  onEdit,
}: CustomEditProps) {
  const value = (user as any)[field];
  let editingContent = value ? value : '--';
  if (field === 'gender' && value) {
    editingContent = <UserGenderText userGender={value} />;
  } else if (field === 'type' && value) {
    editingContent = <UserTypeText userType={value} />;
  }

  if (editKey === field) {
    editingContent = editableNode;
  }
  return (
    <>
      {editingContent}
      {editable && editKey !== field && (
        <Tooltip title="编辑">
          <EditOutlined
            style={{ marginLeft: '4px', color: 'var(--manyun-primary-color)' }}
            onClick={() => {
              onEdit && onEdit(field);
            }}
          />
        </Tooltip>
      )}
    </>
  );
}
/**Custom Edit end */

/** EditText */
export type EditTextProps = {
  value: string | null | undefined;
  editable?: boolean;
  maxLength?: number;
  autoSize?: boolean | { maxRows?: number; minRows?: number };
  onChange?: (newStr: string) => void;
  onStart?: () => void;
};
export function EditText({
  value,
  editable = false,
  maxLength = 10,
  autoSize,
  onChange,
  onStart,
}: EditTextProps) {
  return (
    <>
      {!value && '--'}
      <Text
        editable={
          editable
            ? {
                tooltip: '编辑',
                onChange: onChange,
                onStart: onStart,
                maxLength: maxLength,
                autoSize: autoSize,
              }
            : false
        }
      >
        {value ?? ''}
      </Text>
    </>
  );
}
/** end*/
