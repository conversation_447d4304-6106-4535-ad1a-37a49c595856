import { useCallback, useState } from 'react';

import { message } from '@manyun/base-ui.ui.message';

import { mutateUser } from '@manyun/auth-hub.service.mutate-user';
import type { SvcQuery } from '@manyun/auth-hub.service.mutate-user';

export function useMutateUser() {
  const [loading, setLoaing] = useState(false);

  const updateUser = useCallback(async (svcData: SvcQuery, callback?: (res: boolean) => void) => {
    setLoaing(true);
    const { error, data } = await mutateUser(svcData);
    setLoaing(false);
    callback && callback(data);
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('修改成功！');
  }, []);
  return [updateUser, loading] as const;
}
