/**
 * <AUTHOR> <<EMAIL>>
 * @since 2024-4-8
 *
 * @packageDocumentation
 */
import dayjs from 'dayjs';
import groupBy from 'lodash.groupby';
import React from 'react';
import { useParams } from 'react-router';
import { Link } from 'react-router-dom';

import { useCustomerWhiteListDetail } from '@manyun/auth-hub.gql.client.customer-white-list';
import type { ApproveSpaceDetail } from '@manyun/auth-hub.gql.client.customer-white-list';
import { UserLink } from '@manyun/auth-hub.ui.user-link';
import { Card } from '@manyun/base-ui.ui.card';
import { Descriptions } from '@manyun/base-ui.ui.descriptions';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Skeleton } from '@manyun/base-ui.ui.skeleton';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { generateBPMRoutePath } from '@manyun/bpm.route.bpm-routes';
import { ApprovalStatusText } from '@manyun/bpm.ui.approval-status-text';
import { useSpace } from '@manyun/resource-hub.gql.client.spaces';

export function CustomerWhiteDetail() {
  const { id } = useParams<{ id: string }>();
  const { loading, data } = useCustomerWhiteListDetail({
    variables: {
      id: Number(id),
    },
  });

  const basicData = React.useMemo(
    () => data?.customerWhiteListDetail?.data,
    [data?.customerWhiteListDetail?.data]
  );

  const dataSource = React.useMemo(() => {
    const _data = data?.customerWhiteListDetail?.data?.approvingSpaceDetails ?? [];
    const groupedByApproveId = groupBy(_data, 'approvalId');
    const mergedData = Object.entries(groupedByApproveId).map(([key, items]) => {
      const blockGuidList = items.map(item => item.blockGuid);
      return {
        ...items[0],
        blockGuidList,
      };
    });

    return mergedData;
  }, [data?.customerWhiteListDetail?.data?.approvingSpaceDetails]);

  if (loading) {
    return <Skeleton />;
  }
  return (
    <Space style={{ width: '100%', overflowY: 'auto' }} direction="vertical" size="middle">
      <Card>
        <Space style={{ width: '100%' }} direction="vertical">
          <Typography.Title level={5} showBadge>
            基本信息
          </Typography.Title>
          <Descriptions column={4}>
            {(
              [
                {
                  key: 'userName',
                  label: '姓名',
                  value: basicData?.userName ?? '--',
                },
                {
                  key: 'type',
                  label: '类型',
                  value: basicData?.type
                    ? basicData?.type === 'CUSTOMER'
                      ? '客户'
                      : '供应商'
                    : '--',
                },
                {
                  key: 'phoneNo',
                  label: '手机号',
                  value: basicData?.mobile ?? '--',
                },
                {
                  key: 'email',
                  label: '邮箱',
                  value: basicData?.email ?? '--',
                },
                {
                  key: 'compony',
                  label: '所属公司',
                  value: basicData?.company.name ?? '--',
                },
                {
                  key: 'creator',
                  label: '创建人',
                  value: basicData?.creator?.id ? (
                    <UserLink
                      userId={basicData?.creator.id}
                      userName={basicData?.creator?.name}
                      native
                    />
                  ) : (
                    '--'
                  ),
                },
                {
                  key: 'createTime',
                  label: '创建时间',
                  value: basicData?.createTime
                    ? dayjs(basicData?.createTime).format('YYYY-MM-DD HH:mm:ss')
                    : '--',
                },
                {
                  key: 'modifier',
                  label: '更新人',
                  value: basicData?.modifier?.id ? (
                    <UserLink
                      userId={basicData?.modifier.id}
                      userName={basicData?.modifier?.name}
                      native
                    />
                  ) : (
                    '--'
                  ),
                },
                {
                  key: 'modifier',
                  label: '更新时间',
                  value: basicData?.modifiedTime
                    ? dayjs(basicData?.modifiedTime).format('YYYY-MM-DD HH:mm:ss')
                    : '--',
                },
              ] as { label: string; key: string; value: React.ReactNode }[]
            ).map(item => (
              <Descriptions.Item key={item.key} label={item.label}>
                {item.value}
              </Descriptions.Item>
            ))}
          </Descriptions>
        </Space>
      </Card>
      <Card>
        <Space style={{ width: '100%' }} direction="vertical">
          <Typography.Title level={5} showBadge>
            授权记录
          </Typography.Title>
          <Table<ApproveSpaceDetail & { blockGuidList: string[] }>
            dataSource={dataSource}
            columns={[
              {
                title: '审批ID',
                dataIndex: 'approvalId',
                render: val => (
                  <Link target="_blank" to={generateBPMRoutePath({ id: val })}>
                    {val}
                  </Link>
                ),
              },
              {
                title: '授权机房楼栋',
                dataIndex: 'blockGuidList',
                width: 500,
                render: (_, record) => {
                  return (
                    <Popover
                      content={
                        <Space
                          style={{ width: 600 }}
                          split={<Divider type="vertical" spaceSize="mini" />}
                          wrap
                        >
                          {record.blockGuidList.map(blockGuid => (
                            <SpaceText key={blockGuid} guid={blockGuid} />
                          ))}
                        </Space>
                      }
                    >
                      <Typography.Text
                        style={{ width: 500 }}
                        ellipsis={{
                          tooltip: false,
                        }}
                      >
                        {record.blockGuidList.map((blockGuid, index) => (
                          <Space key={blockGuid} size={0}>
                            <SpaceText guid={blockGuid} />
                            {index !== record.blockGuidList.length - 1 && (
                              <Divider type="vertical" spaceSize="mini" />
                            )}
                          </Space>
                        ))}
                      </Typography.Text>
                    </Popover>
                  );
                },
              },
              {
                title: '备注说明',
                dataIndex: 'description',
                width: 200,
                render: val => {
                  return (
                    <Popover content={val}>
                      <Typography.Text
                        style={{ width: 200 }}
                        ellipsis={{
                          tooltip: false,
                        }}
                      >
                        {val}
                      </Typography.Text>
                    </Popover>
                  );
                },
              },
              {
                title: '申请日期',
                dataIndex: 'approvalDate',
                render: (_, record) =>
                  record.createTime ? dayjs(record.createTime).format('YYYY-MM-DD HH:mm:ss') : '--',
              },
              {
                title: '申请人',
                dataIndex: 'creator',
                render: (_, record) => (
                  <UserLink userId={record.creator?.id} userName={record.creator?.name} native />
                ),
              },
              {
                title: '状态',
                dataIndex: 'status',
                render: val => <ApprovalStatusText approveStatus={val} />,
              },
            ]}
            pagination={false}
          />
        </Space>
      </Card>
    </Space>
  );
}

function SpaceText({ guid }: { guid: string }) {
  const [spaceIdc, spaceBlock, spaceRoom] = useSpace(guid);

  let text = '';
  if (spaceIdc) {
    text += spaceIdc.label;
  }
  if (spaceBlock) {
    text += ` / ${spaceBlock.label}`;
  }
  if (spaceRoom) {
    text += ` / ${spaceRoom.label}`;
  }
  return text ? text : guid;
}
