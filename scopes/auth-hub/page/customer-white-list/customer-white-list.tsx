import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useHistory } from 'react-router';
import { Link } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Divider } from '@manyun/base-ui.ui.divider';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Popover } from '@manyun/base-ui.ui.popover';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';

import {
  useDeleteCustomerWhiteList,
  useLazyCustomerWhiteList,
} from '@manyun/auth-hub.gql.client.customer-white-list';
import {
  CREATE_CUSTOMER_WHITE_LIST_ROUTE_PATH,
  generateCustomerWhiteDetailRoutePath,
} from '@manyun/auth-hub.route.auth-routes';

import { CustomerWhiteListFilter } from './components/customer-white-list-filter';
import type { FiltersFormValues } from './components/customer-white-list-filter';
import styles from './customer-white-list.module.less';

export type SingleCustomerListJSON = {
  id: number;
  userName: string;
  mobile: string;
  email: string;
  position?: string | null;
  /** 键值对 */
  company: { name: string; value: string };
  /** 经过resolver处理过后的机房楼栋名称拼接 */
  spaceGuidList?: string | null;
  approvingSpaceGuidList?: string | null;
};

export function CustomerWhiteList() {
  const [getCustomerWhiteList] = useLazyCustomerWhiteList({ fetchPolicy: 'network-only' });
  const [deleteCustomerWhiteList, { loading: deleteLoading }] = useDeleteCustomerWhiteList({});

  const history = useHistory();
  const [pagination, setPagination] = useState<{ pageNum: number; pageSize: number }>({
    pageNum: 1,
    pageSize: 10,
  });
  const [total, setTotal] = useState<number>(0);
  const [dataSource, setDataSource] = useState<SingleCustomerListJSON[]>([]);
  const [tableLoading, setTableLoading] = useState<boolean>(false);
  const filterSearchParamsRef = useRef<FiltersFormValues | undefined>();
  const fetchCustomerWhiteList = useCallback(
    async (params: { pageNum: number; pageSize: number }) => {
      setTableLoading(true);
      const { pageNum, pageSize } = params;

      const queryParams = {
        username: filterSearchParamsRef.current?.userName,
        company: filterSearchParamsRef.current?.company,
        phoneNo: filterSearchParamsRef.current?.phoneNo,
        userStatus: filterSearchParamsRef.current?.userStatus,
        blockGuidList:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.blockGuidList) &&
          filterSearchParamsRef.current.blockGuidList.length > 0
            ? [
                ...filterSearchParamsRef.current.blockGuidList
                  .filter(blockGuid => blockGuid.length > 1)
                  .map(item => item[1]),
              ]
            : undefined,
        startDate:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.createdTimeRange) &&
          filterSearchParamsRef.current.createdTimeRange.length > 0
            ? filterSearchParamsRef.current.createdTimeRange[0].startOf('day')!.valueOf()
            : undefined,
        endDate:
          filterSearchParamsRef.current &&
          Array.isArray(filterSearchParamsRef.current.createdTimeRange) &&
          filterSearchParamsRef.current.createdTimeRange.length > 0
            ? filterSearchParamsRef.current.createdTimeRange[1].endOf('day').valueOf()
            : undefined,
      };
      const { error, data } = await getCustomerWhiteList({
        variables: { pageNum, pageSize, ...queryParams },
      });
      setTableLoading(false);

      if (error) {
        message.error(error.message);
        return;
      }
      setPagination({ pageNum, pageSize });
      setTotal(data?.customerWhiteList?.total ?? 0);
      setDataSource(data?.customerWhiteList?.data ?? []);
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    []
  );

  useEffect(() => {
    fetchCustomerWhiteList({ pageNum: 1, pageSize: 10 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  return (
    <Space size="middle" direction="vertical" className={styles.wrapper}>
      <Button
        type="primary"
        onClick={() => {
          history.push(CREATE_CUSTOMER_WHITE_LIST_ROUTE_PATH);
        }}
      >
        新建
      </Button>
      <CustomerWhiteListFilter
        onChange={params => {
          filterSearchParamsRef.current = params;
          fetchCustomerWhiteList({ pageNum: 1, pageSize: pagination.pageSize });
        }}
      />
      <Table<SingleCustomerListJSON>
        rowKey="id"
        loading={tableLoading}
        scroll={{ x: 'max-content' }}
        columns={[
          {
            title: '姓名',
            dataIndex: 'userName',
            key: 'userName',
            render: (val, record) => (
              <Link
                to={`${generateCustomerWhiteDetailRoutePath({
                  id: record.id.toString(),
                })}?userName=${encodeURIComponent(val)}`}
                target="_blank"
              >
                {val}
              </Link>
            ),
          },
          {
            title: '手机号码',
            dataIndex: 'mobile',
            key: 'mobile',
          },
          {
            title: '邮箱',
            dataIndex: 'email',
            key: 'email',
          },
          {
            title: '所属公司',
            dataIndex: 'company',
            key: 'company',

            render: (_, { company }) => company.name,
          },
          {
            title: '岗位',
            dataIndex: 'position',
            key: 'position',
          },
          {
            title: '有效授权区域',
            dataIndex: 'spaceGuidList',
            width: 400,
            key: 'spaceGuidList',
            render: (_, { spaceGuidList }) => {
              const blockNames = (spaceGuidList ?? '').split('|');
              return (
                <Popover
                  content={
                    <Space
                      style={{ maxWidth: 400 }}
                      split={<Divider type="vertical" spaceSize="mini" />}
                      wrap
                    >
                      {getLocationContent(blockNames, true)}
                    </Space>
                  }
                >
                  <Typography.Text
                    ellipsis={{
                      tooltip: false,
                    }}
                    style={{ width: 400 }}
                  >
                    {getLocationContent(blockNames, false)}
                  </Typography.Text>
                </Popover>
              );
            },
          },
          {
            title: '审核中授权区域',
            dataIndex: 'approvingSpaceGuidList',
            width: 400,
            key: 'approvingSpaceGuidList',
            render: (_, { approvingSpaceGuidList }) => {
              const blockNames = (approvingSpaceGuidList ?? '').split('|');
              return (
                <Popover
                  content={
                    <Space
                      style={{ maxWidth: 400 }}
                      split={<Divider type="vertical" spaceSize="mini" />}
                      wrap
                    >
                      {getLocationContent(blockNames, true)}
                    </Space>
                  }
                >
                  <Typography.Text
                    ellipsis={{
                      tooltip: false,
                    }}
                    style={{ width: 400 }}
                  >
                    {getLocationContent(blockNames, false)}
                  </Typography.Text>
                </Popover>
              );
            },
          },
          {
            title: '操作',
            dataIndex: 'operation',
            key: 'operation',
            width: 112,
            fixed: 'right',
            render: (_, record) => {
              /**approvingSpaceGuidList存在数据表示审批中，不可删除*/
              if ((record.approvingSpaceGuidList ?? []).length === 0) {
                return (
                  <Popconfirm
                    title={`删除${record.userName}?`}
                    okButtonProps={{ loading: deleteLoading }}
                    onConfirm={async () => {
                      const { data } = await deleteCustomerWhiteList({
                        variables: {
                          id: record.id,
                        },
                      });
                      if (data?.deleteCustomerWhiteList?.message) {
                        message.error(data.deleteCustomerWhiteList.message);
                        return;
                      }
                      message.success('删除成功');
                      fetchCustomerWhiteList({
                        pageSize: pagination.pageSize,
                        pageNum: pagination.pageNum,
                      });
                    }}
                  >
                    <Button type="link" compact>
                      删除
                    </Button>
                  </Popconfirm>
                );
              }
              return '';
            },
          },
        ]}
        dataSource={dataSource}
        pagination={{
          total: total,
          pageSize: pagination.pageSize,
          current: pagination.pageNum,
          onChange: (current, size) => {
            fetchCustomerWhiteList({
              pageNum: current,
              pageSize: size,
            });
          },
        }}
      />
    </Space>
  );
}

function getLocationContent(blockGuidNames: string[], isInPopover: boolean) {
  return blockGuidNames.map((item, index) => (
    <span key={item}>
      <Typography.Text>{item}</Typography.Text>
      {!isInPopover && index !== blockGuidNames.length - 1 && <Divider type="vertical" />}
    </span>
  ));
}
