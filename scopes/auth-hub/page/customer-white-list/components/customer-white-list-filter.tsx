import React, { useMemo } from 'react';

import type { Moment } from 'moment';

import { Cascader } from '@manyun/base-ui.ui.cascader';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { QueryFilter } from '@manyun/base-ui.ui.query-filter';

import { CustomersOnRacksSelect } from '@manyun/resource-hub.ui.customers-on-racks-select';
import { LocationCascader } from '@manyun/resource-hub.ui.location-cascader';

export type FiltersFormValues = {
  userName?: string;
  company?: string;
  phoneNo?: string;
  createdTimeRange?: Moment[];
  blockGuidList?: string[][];
  userStatus?: string;
};
export type CustomerWhiteListFilterProps = { onChange: (params?: FiltersFormValues) => void };

export function CustomerWhiteListFilter({ onChange }: CustomerWhiteListFilterProps) {
  const [form] = Form.useForm<FiltersFormValues>();

  const items = useMemo(
    () => [
      {
        label: '姓名',
        name: 'userName',
        control: <Input aria-label="userName" allowClear />,
      },
      {
        label: '所属公司',
        name: 'company',
        control: <CustomersOnRacksSelect allowClear />,
      },
      {
        label: '手机号码',
        name: 'phoneNo',
        control: <Input aria-label="mobile" allowClear />,
      },
      {
        label: '创建时间',
        name: 'createdTimeRange',
        span: 2,
        control: <DatePicker.RangePicker format="YYYY-MM-DD" allowClear />,
      },
      {
        label: '授权区域',
        name: 'blockGuidList',
        span: 2,
        control: (
          <LocationCascader
            multiple
            authorizedOnly
            showCheckedStrategy={Cascader.SHOW_CHILD}
            changeOnSelect={false}
            allowClear
            maxTagCount="responsive"
            disabledNoChildsNodes={['IDC']}
          />
        ),
      },
    ],
    []
  );

  return (
    <QueryFilter<FiltersFormValues>
      form={form}
      items={items}
      onSearch={filterFormValues => {
        onChange(filterFormValues);
      }}
      onReset={() => {
        form.resetFields();
        onChange();
      }}
    />
  );
}
