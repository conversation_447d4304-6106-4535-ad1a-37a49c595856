import React from 'react';
import { Route, MemoryRouter as Router, Switch, useHistory } from 'react-router-dom';

import { ConfigProvider } from '@manyun/base-ui.context.config';
import { Button } from '@manyun/base-ui.ui.button';

import { Error403_ROUTE_PATH } from '@manyun/auth-hub.route.auth-routes';
import { AUTH_REQUEST_CREATOR_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';

import { ErrorPage403 } from './403';

const AuthApplyPageMock = () => {
  const history = useHistory();
  return (
    <>
      <span>权限申请界面</span>
      <Button
        onClick={() => {
          history.goBack();
        }}
      >
        Go Back
      </Button>
    </>
  );
};

export const Basic403 = () => (
  <ConfigProvider>
    <Router initialEntries={[Error403_ROUTE_PATH]}>
      <Switch>
        <Route path={Error403_ROUTE_PATH} exact>
          <ErrorPage403 />
        </Route>
        <Route path={AUTH_REQUEST_CREATOR_ROUTE_PATH} exact>
          <AuthApplyPageMock />
        </Route>
      </Switch>
    </Router>
  </ConfigProvider>
);
