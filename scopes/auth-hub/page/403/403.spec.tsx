import React from 'react';

import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

import { Basic403 } from './403.composition';

test('should render with the correct text', () => {
  const { getByText } = render(<Basic403 />);
  const rendered = getByText('申请权限');
  expect(rendered).toBeTruthy();
});

test('should render with the correct text by click  button', () => {
  render(<Basic403 />);
  const clickBtn = screen.getByText('申请权限');

  expect(clickBtn).toBeInTheDocument();
  userEvent.click(clickBtn);

  const gobackBtn = screen.getByText('Go Back');
  expect(gobackBtn).toBeInTheDocument();
});
