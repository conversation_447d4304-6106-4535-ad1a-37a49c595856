/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-29
 *
 * @packageDocumentation
 */
import React from 'react';
import { useHistory } from 'react-router-dom';

import { Button } from '@manyun/base-ui.ui.button';
import { Result } from '@manyun/base-ui.ui.result';

import { AUTH_REQUEST_CREATOR_ROUTE_PATH } from '@manyun/bpm.route.bpm-routes';

export type ErrorPage403Props = { subTitleContent?: string; hasExtra?: boolean };

export function ErrorPage403({
  subTitleContent = '抱歉，暂无权限！可点击按钮进行申请',
  hasExtra = true,
}: ErrorPage403Props) {
  const history = useHistory();

  const onClickApply = () => {
    history.push(AUTH_REQUEST_CREATOR_ROUTE_PATH);
  };

  return (
    <Result
      status="403"
      title="403"
      subTitle={subTitleContent}
      extra={hasExtra ? <Button onClick={onClickApply}>申请权限</Button> : <></>}
    />
  );
}
