import React from 'react';
import { useHistory } from 'react-router';

import { CUSTOMER_WHITE_LIST_ROUTE_PATH } from '@manyun/auth-hub.route.auth-routes';
import { CustomerWhiteListApplicationMutator } from '@manyun/auth-hub.ui.customer-white-list-application-mutator';

export type CustomerWhiteListMutatorProps = {
  mode: 'create' | 'edit';
};

export function CustomerWhiteListMutator({ mode }: CustomerWhiteListMutatorProps) {
  const history = useHistory();
  return (
    <CustomerWhiteListApplicationMutator
      mode={mode}
      onSuccess={() => {
        history.push(CUSTOMER_WHITE_LIST_ROUTE_PATH);
      }}
      onCancel={() => {
        history.goBack();
      }}
    />
  );
}
