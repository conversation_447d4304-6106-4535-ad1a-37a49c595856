import dayjs from 'dayjs';
import React, { use<PERSON>allback, useMemo, useRef, useState } from 'react';

import type { AuthorizationRecordJSON } from '@manyun/auth-hub.model.authorization-record';
import { AuthorizationRecordTable } from '@manyun/auth-hub.ui.authorization-record-table';
import {
  FileCheckColorful,
  FileEditColorful,
  FileHourglassColorful,
  FileSignetColorful,
} from '@manyun/base-ui.icons';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Select } from '@manyun/base-ui.ui.select';
import { Space } from '@manyun/base-ui.ui.space';
import type { StatisticProps } from '@manyun/base-ui.ui.statistic';
import { Statistic } from '@manyun/base-ui.ui.statistic';
import { Typography } from '@manyun/base-ui.ui.typography';
import type { AuthorizationRecordStatus } from '@manyun/iam.gql.client.user';
import {
  useAuthorizationStatistics,
  usePaginatedAuthorizationRecords,
  useUpdateAuthorizationRecord,
} from '@manyun/iam.gql.client.user';

import { UserRoleAuthorizationEditor } from './components/user-role-authorization-editor';
import styles from './user-role-authorization.module.less';

const defaultPagination = {
  pageNum: 1,
  pageSize: 10,
};

const defaultField = {
  status: undefined,
  expiredTimeRange: undefined,
  currentMonthExpiredTimeRange: undefined,
};
export function UserRoleAuthorization() {
  const fieldsRef = useRef<{
    searchBy: string;
    keyWord?: string;
    status?: string;
    expiredTimeRange?: [number, number];
    currentMonthExpiredTimeRange?: [number, number];
  }>({
    searchBy: 'roleName',
  });
  const [pagination, setPagination] = useState<{ pageNum: number; pageSize: number }>(
    defaultPagination
  );
  const [selectedStatistics, setSelectedStatistics] = useState<React.Key>();
  const [form] = Form.useForm();
  const {
    data: statistic,
    loading: statisticLoading,
    refetch: refetchStatistic,
  } = useAuthorizationStatistics();
  const { data, loading, refetch } = usePaginatedAuthorizationRecords({
    variables: {
      params: defaultPagination,
    },
  });
  const [updateAuthorizationRecord, { loading: updateLoading }] = useUpdateAuthorizationRecord();

  const statisticsGroups = useMemo(() => {
    const groups: (StatisticProps & { icon?: React.ReactNode; key: React.Key })[] = [
      {
        title: '授权有效记录',
        key: 'totalNum',
        value: statistic?.authorizationStatistics.totalNum,
        icon: <FileEditColorful style={{ fontSize: 48 }} />,
      },
      {
        title: '本月临近过期',
        key: 'closeExpireNum',
        value: statistic?.authorizationStatistics.closeExpireNum,
        icon: <FileHourglassColorful style={{ fontSize: 48 }} />,
      },
      {
        title: '授权过期',
        key: 'expireNum',
        value: statistic?.authorizationStatistics.expireNum,
        icon: <FileSignetColorful style={{ fontSize: 48 }} />,
      },
      {
        title: '本月续期数',
        key: 'continueNum',
        value: statistic?.authorizationStatistics.continueNum,
        icon: <FileCheckColorful style={{ fontSize: 48 }} />,
      },
    ];
    return groups;
  }, [
    statistic?.authorizationStatistics.closeExpireNum,
    statistic?.authorizationStatistics.continueNum,
    statistic?.authorizationStatistics.expireNum,
    statistic?.authorizationStatistics.totalNum,
  ]);

  const onSearch = useCallback(
    (params?: { pageSize?: number; pageNum: number }) => {
      let _pagination = defaultPagination;
      if (params?.pageNum && params?.pageSize) {
        _pagination = { pageNum: params.pageNum, pageSize: params.pageSize };
      }
      setPagination(_pagination);
      refetch({
        params: {
          ..._pagination,
          roleName:
            fieldsRef.current.searchBy === 'roleName'
              ? fieldsRef.current.keyWord?.trim()
              : undefined,
          userName:
            fieldsRef.current.searchBy === 'userName'
              ? fieldsRef.current.keyWord?.trim()
              : undefined,
          expiredTimeRange: fieldsRef.current.expiredTimeRange,
          expiredCurrentMonthRange: fieldsRef.current.currentMonthExpiredTimeRange,
          status: fieldsRef.current.status
            ? (fieldsRef.current.status as AuthorizationRecordStatus)
            : undefined,
        },
      });
    },
    [refetch]
  );

  return (
    <Card>
      <Space style={{ width: '100%' }} direction="vertical" size="middle">
        <Space className={styles.statisticRow} style={{ width: '100%' }} size="large">
          {statisticsGroups.map(info => (
            <Card
              key={info.key}
              className={selectedStatistics === info.key ? styles.checkedCard : undefined}
              loading={statisticLoading}
              onClick={() => {
                form.resetFields();
                if (selectedStatistics === info.key) {
                  setSelectedStatistics(undefined);
                  fieldsRef.current = { ...fieldsRef.current, ...defaultField };
                  onSearch();
                } else {
                  setSelectedStatistics(info.key);
                  if (info.key === 'expireNum') {
                    fieldsRef.current = {
                      ...fieldsRef.current,
                      ...defaultField,
                      status: 'INVALID',
                    };
                    form.setFieldsValue({
                      status: 'INVALID',
                    });
                  } else if (info.key === 'totalNum') {
                    fieldsRef.current = {
                      ...fieldsRef.current,
                      ...defaultField,
                      status: 'VALID',
                    };
                    form.setFieldsValue({
                      status: 'VALID',
                    });
                  } else if (info.key === 'closeExpireNum') {
                    fieldsRef.current = {
                      ...fieldsRef.current,
                      ...defaultField,
                      status: 'VALID',
                      expiredTimeRange: [
                        dayjs().startOf('day').valueOf(),
                        dayjs().endOf('month').valueOf(),
                      ],
                    };
                    form.setFieldsValue({
                      status: 'VALID',
                    });
                  } else if (info.key === 'continueNum') {
                    fieldsRef.current = {
                      ...fieldsRef.current,
                      ...defaultField,
                      status: 'VALID',
                      currentMonthExpiredTimeRange: [
                        dayjs().startOf('month').valueOf(),
                        dayjs().endOf('month').valueOf(),
                      ],
                    };
                    form.setFieldsValue({
                      status: 'VALID',
                    });
                  }
                  onSearch();
                }
              }}
            >
              <Space style={{ width: '100%', justifyContent: 'space-between' }}>
                <Statistic title={info.title} value={info.value} formatter={info.formatter} />
                {info.icon}
              </Space>
            </Card>
          ))}
        </Space>
        <Input.Group compact>
          <Select
            style={{ width: '80px' }}
            defaultValue="roleName"
            options={[
              { label: '角色', value: 'roleName' },
              { label: '姓名', value: 'userName' },
            ]}
            onChange={val => {
              fieldsRef.current = {
                ...fieldsRef.current,
                searchBy: val,
                keyWord: undefined,
              };
              onSearch();
            }}
          />
          <Input.Search
            style={{ width: '228px' }}
            allowClear
            onSearch={val => {
              fieldsRef.current = {
                ...fieldsRef.current,
                keyWord: val ?? undefined,
              };
              onSearch();
            }}
          />
        </Input.Group>
        <Form form={form} layout="inline" colon={false}>
          <Form.Item label="授权过期时间" name="expiredTimeRange">
            <DatePicker.RangePicker style={{ width: 260 }} />
          </Form.Item>
          <Form.Item label="状态" name="status">
            <Select
              style={{ width: 260 }}
              options={[
                { label: '有效', value: 'VALID' },
                { label: '失效', value: 'INVALID' },
              ]}
              allowClear
            />
          </Form.Item>
          <Form.Item>
            <Space>
              <Button
                type="primary"
                onClick={() => {
                  const values = form.getFieldsValue();
                  fieldsRef.current = {
                    ...fieldsRef.current,
                    currentMonthExpiredTimeRange: undefined,
                    status: values.status,
                    expiredTimeRange:
                      values.expiredTimeRange &&
                      values.expiredTimeRange[0] &&
                      values.expiredTimeRange[1]
                        ? [
                            dayjs(values.expiredTimeRange[0]).startOf('day').valueOf(),
                            dayjs(values.expiredTimeRange[1]).endOf('day').valueOf(),
                          ]
                        : undefined,
                  };
                  setSelectedStatistics(undefined);

                  onSearch();
                }}
              >
                搜索
              </Button>
              <Button
                onClick={() => {
                  setSelectedStatistics(undefined);
                  form.resetFields();
                  fieldsRef.current = {
                    ...fieldsRef.current,
                    currentMonthExpiredTimeRange: undefined,
                    expiredTimeRange: undefined,
                    status: undefined,
                  };
                  onSearch();
                }}
              >
                重置
              </Button>
            </Space>
          </Form.Item>
        </Form>
        <AuthorizationRecordTable
          rowKey="id"
          loading={loading}
          dataSource={
            (data?.authorizationRecords.data ?? []) as unknown as AuthorizationRecordJSON[]
          }
          showColumns={[
            'userName',
            'roleName',
            'resourceList',
            'expiredAt',
            'isDeleted',
            'modifiedAt',
          ]}
          pagination={{
            total: data?.authorizationRecords.total ?? 0,
            current: pagination.pageNum,
            pageSize: pagination.pageSize,
            onChange: (page, pageSize) => {
              onSearch({ pageNum: page, pageSize: pageSize });
            },
          }}
          operationColumn={{
            title: '操作',
            width: 120,
            fixed: 'right',
            render: (_, record) => {
              return (
                <Space size="large">
                  {record.expiredAt >= new Date().valueOf() && (
                    <Popconfirm
                      title="失效后用户不再拥有角色对应的权限，确定吗？"
                      placement="topRight"
                      okButtonProps={{
                        loading: updateLoading,
                      }}
                      onConfirm={() => {
                        updateAuthorizationRecord({
                          variables: {
                            id: record.id,
                            localDate: dayjs().format('YYYY-MM-DD'),
                          },
                          onCompleted: res => {
                            if (res.updateAuthorizationRecord.message) {
                              message.error(res.updateAuthorizationRecord.message);
                              return;
                            }
                            message.success('操作成功');
                            refetchStatistic();
                            onSearch();
                          },
                        });
                      }}
                    >
                      <Typography.Link>失效</Typography.Link>
                    </Popconfirm>
                  )}
                  <UserRoleAuthorizationEditor
                    record={record}
                    onSuccess={() => {
                      refetchStatistic();
                      onSearch();
                    }}
                  />
                </Space>
              );
            },
          }}
        />
      </Space>
    </Card>
  );
}
