import { gql, useApolloClient } from '@apollo/client';
import type { ApolloClient } from '@apollo/client';
import moment from 'moment';
import React, { useState } from 'react';

import type { Resource } from '@manyun/auth-hub.gql.client.resources';
import type { AuthorizationRecordJSON } from '@manyun/auth-hub.model.authorization-record';
import { ResourceCascader } from '@manyun/auth-hub.ui.gql.resource-cascader';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Space } from '@manyun/base-ui.ui.space';
import { Tag } from '@manyun/base-ui.ui.tag';
import { Typography } from '@manyun/base-ui.ui.typography';
import { useUpdateAuthorizationRecord } from '@manyun/iam.gql.client.user';
import { getAuthResourceLocales } from '@manyun/iam.model.auth-resource';
import type { BackendAuthResource } from '@manyun/iam.model.auth-resource';
import { fetchRole } from '@manyun/iam.service.fetch-role';

import styles from '../user-role-authorization.module.less';

export type UserRoleAuthorizationEditorProps = {
  record: AuthorizationRecordJSON;
  onSuccess?: () => void;
};
export const UserRoleAuthorizationEditor = ({
  record,
  onSuccess,
}: UserRoleAuthorizationEditorProps) => {
  const [updateAuthorizationRecord, { loading: updateLoading }] = useUpdateAuthorizationRecord();
  const [open, setOpen] = useState(false);
  const [form] = Form.useForm();
  const [resourceCodes, setResourceCodes] = React.useState({});
  const [resourceTypes, setResourceTypes] = React.useState<string | undefined>();
  const client = useApolloClient();

  const handleResourcesChange = React.useCallback(
    (_: (string | number)[], options: Resource[][], type: string) => {
      /** 获取是否应该更新数据 */
      function getShouldUpdate(resources: Resource[]) {
        const len = resources.length;
        if (len === 0) {
          return true;
        }
        const resource = resources[len - 1];
        // 仅当为部门层级或者叶子节点时，才应更新数据
        return (
          record?.resourceType === 'DEPT' ||
          resource.children === null ||
          resource.children.length === 0
        );
      }
      if (type === 'AREA' && options.length === 1 && options[0].length === 1) {
        setResourceCodes({
          ...resourceCodes,
          // @ts-ignore
          [type]: [options[0][0].value],
        });
        return;
      }
      if (options.every(resources => getShouldUpdate(resources))) {
        const selectedOptions = options.reduce((acc, cur) => [...acc, cur[cur.length - 1]], []);
        setResourceCodes({ ...resourceCodes, [type]: selectedOptions.map(opt => opt.value) });
      }
    },
    [record?.resourceType, resourceCodes]
  );

  const onTreeDataChange = (tree: any, type: string) => {
    const filterCodes = record.resourceList
      .map(resource => {
        if (resource.resourceType.replace('BUILDING', 'BLOCK') === type) {
          return resource.resourceCode;
        }
      })
      .filter(Boolean);
    setResourceCodes({ ...resourceCodes, [type]: filterCodes });
    form.setFieldsValue({
      [`resources_${type}`]: filterCodes.map(i => mapObjectsToPaths(i, tree)).flat(),
    });
  };
  const onGetRoleInfo = async (roleCode: string) => {
    const { error, data: roleData } = await fetchRole({ code: roleCode });
    if (error) {
      message.error(error.message);
      return;
    }
    if (!roleData) {
      message.error('获取角色详情失败');
      return;
    }
    const values = {
      code: roleData.code,
      name: roleData.name,
      website: roleData.website,
      approveId: roleData.approveId,
      resourceType: roleData.resourceType,
      remarks: roleData.remarks,
    };
    setResourceTypes(values.resourceType);
  };
  return (
    <>
      <Typography.Link
        onClick={() => {
          setOpen(true);
          form.setFieldValue('expiredAt', moment(record.expiredAt));
          onGetRoleInfo(record.roleCode);
        }}
      >
        编辑
      </Typography.Link>
      <Modal
        title="编辑"
        open={open}
        width={435}
        okText="保存"
        cancelText="取消"
        okButtonProps={{
          loading: updateLoading,
        }}
        destroyOnClose
        afterClose={() => {
          form.resetFields();
          setResourceTypes(undefined);
        }}
        onCancel={() => {
          setOpen(false);
          setResourceTypes(undefined);
        }}
        onOk={() => {
          form
            .validateFields()
            .then(values => {
              updateAuthorizationRecord({
                variables: {
                  id: record.id,
                  localDate: values.expiredAt.format('YYYY-MM-DD'),
                  resourceType: Object.keys(resourceCodes).join(','),
                  resourceList: [
                    ...new Set([
                      ...Object.keys(resourceCodes)
                        // @ts-ignore
                        .map(resourceKey => resourceCodes[resourceKey])
                        .flat(),
                    ]),
                  ],
                },
                onCompleted: res => {
                  if (res.updateAuthorizationRecord.message) {
                    message.error(res.updateAuthorizationRecord.message);
                    return;
                  }
                  message.success('操作成功');
                  onSuccess?.();
                  setOpen(false);
                },
              });
            })
            .catch(console.error);
        }}
      >
        <Form form={form} labelCol={{ span: 7 }}>
          <Form.Item
            name="expiredAt"
            label="授权过期时间"
            rules={[
              {
                required: true,
                message: '授权过期时间必选',
              },
            ]}
          >
            <DatePicker
              style={{ width: '100%' }}
              disabledDate={current =>
                current &&
                (current <= moment().endOf('day') || current > moment().add(1, 'year').endOf('day'))
              }
              showToday={false}
              format="YYYY-MM-DD 00:00:00"
              renderExtraFooter={() => (
                <Space className={styles.datePickerExtraFooter}>
                  <Tag
                    color="processing"
                    onClick={() => {
                      form.setFieldValue('expiredAt', moment().add(1, 'day'));
                      form.validateFields(['expiredAt']); // 触发校验
                    }}
                  >
                    明天
                  </Tag>
                  <Tag
                    color="processing"
                    onClick={() => {
                      form.setFieldValue('expiredAt', moment().add(7, 'day'));
                      form.validateFields(['expiredAt']); // 触发校验
                    }}
                  >
                    7天
                  </Tag>
                  <Tag
                    color="processing"
                    onClick={() => {
                      form.setFieldValue('expiredAt', moment().add(1, 'month'));
                      form.validateFields(['expiredAt']); // 触发校验
                    }}
                  >
                    1月
                  </Tag>
                  <Tag
                    color="processing"
                    onClick={() => {
                      form.setFieldValue('expiredAt', moment().add(6, 'month'));
                      form.validateFields(['expiredAt']); // 触发校验
                    }}
                  >
                    半年
                  </Tag>
                  <Tag
                    color="processing"
                    onClick={() => {
                      form.setFieldValue('expiredAt', moment().add(1, 'year'));
                      form.validateFields(['expiredAt']); // 触发校验
                    }}
                  >
                    一年
                  </Tag>
                </Space>
              )}
            />
          </Form.Item>
          {resourceTypes &&
            resourceTypes
              .replace('BUILDING', 'BLOCK')
              .split(',')
              .map(type => (
                <Form.Item
                  name={`resources_${type}`}
                  // @ts-ignore
                  label={`申请${getAuthResourceLocales().resourceType[type] ?? ''}资源`}
                >
                  <ResourceCascader
                    roleCode={record.roleCode}
                    // @ts-ignore
                    resourceType={type}
                    multiple
                    maxTagCount="responsive"
                    showCheckedStrategy={type === 'AREA' ? 'SHOW_PARENT' : 'SHOW_CHILD'}
                    onChange={(_, values) => {
                      // @ts-ignore
                      handleResourcesChange(_, values, type) as any;
                    }}
                    onTreeDataChange={tree => {
                      onTreeDataChange(tree, type);
                    }}
                  />
                </Form.Item>
              ))}
        </Form>
      </Modal>
    </>
  );
};

function findPathAndOptions(node, targetValue, currentPath = []) {
  // 将当前节点的 value 添加到路径中
  const newPath = [...currentPath, node.value];

  // 如果找到目标值，立即返回路径和节点对象
  if (node.value === targetValue) {
    return { path: newPath, option: node };
  }

  // 如果有子节点，递归遍历子节点
  if (node.children) {
    for (const child of node.children) {
      const result = findPathAndOptions(child, targetValue, newPath);
      if (result) return result; // 找到结果后立即返回，停止进一步递归
    }
  }

  return null;
}
function mapObjectsToPaths(guid, tree) {
  const spaceGuids = []; // 存储找到的路径的二维数组

  // 遍历 rooms 数组，逐个匹配路径和对象

  for (const root of tree) {
    const result = findPathAndOptions(root, guid);
    if (result) {
      spaceGuids.push(result.path); // 将找到的路径作为子数组添加到 spaceGuids

      break;
    }
  }
  return spaceGuids;
}
