import React from 'react';

import { Button } from '@manyun/base-ui.ui.button';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { syncUsers } from '@manyun/auth-hub.service.sync-users';

type Props = {
  afterSyncUsers: () => void;
};

export default function SyncUsersButton({ afterSyncUsers }: Props) {
  const [authorized] = useAuthorized({ checkByCode: 'element_sync-users' });
  const handleClick = () => {
    const title =
      '同步 MyGLP 通讯录成员。' +
      '同步成功后系统将会生成员工类型的新用户，并且更新已有员工部分信息，具体以同步结果为准。是否确定同步？';
    Modal.confirm({
      title,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await syncUsers();
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('同步成功！');
        afterSyncUsers();
      },
    });
  };
  return authorized ? (
    <Button type="primary" onClick={handleClick}>
      同步用户
    </Button>
  ) : null;
}
