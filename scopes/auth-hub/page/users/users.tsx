import uniq from 'lodash.uniq';
import React, { useState } from 'react';
import { useDispatch } from 'react-redux';
import { Link, useHistory } from 'react-router-dom';

import { useAuthorized } from '@manyun/auth-hub.hook.use-authorized';
import { useUsers } from '@manyun/auth-hub.hook.use-users';
import type { UserGender, UserState, UserType } from '@manyun/auth-hub.model.user';
import {
  USERS_CREATOR_ROUTE_PATH,
  generateUserDetailRoutePath,
} from '@manyun/auth-hub.route.auth-routes';
import { deleteUser } from '@manyun/auth-hub.service.delete-user';
import { exportUsers } from '@manyun/auth-hub.service.export-users';
import type { CustomizedUser } from '@manyun/auth-hub.service.fetch-paged-users';
import { mutateUserCompanyOnUser } from '@manyun/auth-hub.service.mutate-user-company-on-user';
import { mutateUserGroupsOnUser } from '@manyun/auth-hub.service.mutate-user-groups-on-user';
import { updateUserState } from '@manyun/auth-hub.service.update-user-state';
import { UserGenderSelect } from '@manyun/auth-hub.ui.user-gender';
import { UserSelect } from '@manyun/auth-hub.ui.user-select';
import { UserStateSelect, UserStateText } from '@manyun/auth-hub.ui.user-state';
import { UserTypeSelect } from '@manyun/auth-hub.ui.user-type';
import { UsersDataTable } from '@manyun/auth-hub.ui.users-data-table';
import { Alert } from '@manyun/base-ui.ui.alert';
import { Button } from '@manyun/base-ui.ui.button';
import { Card } from '@manyun/base-ui.ui.card';
import { Divider } from '@manyun/base-ui.ui.divider';
import { Dropdown } from '@manyun/base-ui.ui.dropdown';
import { FileExport } from '@manyun/base-ui.ui.file-export';
import type { FileExportProps } from '@manyun/base-ui.ui.file-export';
import { Form } from '@manyun/base-ui.ui.form';
import { Col, Row } from '@manyun/base-ui.ui.grid';
import { Input } from '@manyun/base-ui.ui.input';
import { message } from '@manyun/base-ui.ui.message';
import { Modal } from '@manyun/base-ui.ui.modal';
import { Popconfirm } from '@manyun/base-ui.ui.popconfirm';
import { Space } from '@manyun/base-ui.ui.space';
import { Typography } from '@manyun/base-ui.ui.typography';
import { CustomersSelect } from '@manyun/crm.ui.customers-select';
import { VendorSelect } from '@manyun/crm.ui.vendor-select';
import { fetchUserProcessingEvents } from '@manyun/hrm.service.fetch-user-processing-events';
import type { TransferItemType } from '@manyun/hrm.service.fetch-user-processing-events';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';

import SyncUsersButton from './SyncUsersButton';

// 根据不同条件展示离职弹窗
const ConfirmModal = async ({
  record,
  handleSearch,
  setResignModalVisible,
  setSelectedUser,
  setNeedTransferItems,
}: {
  record: CustomizedUser;
  handleSearch: (pageNum: number, pageSize?: number) => void;
  setResignModalVisible: (visible: boolean) => void;
  setSelectedUser: (record: CustomizedUser) => void;
  setNeedTransferItems: (transferItems: TransferItemType[]) => void;
}) => {
  const { data, error } = await fetchUserProcessingEvents({ employeeId: record.id });

  if (error?.message) {
    message.error(error.message);
    return;
  }

  if (data) {
    const type = data.length > 0 ? '3' : '1';

    switch (type) {
      case '1':
        setNeedTransferItems([]);
        return Modal.confirm({
          title: `确定要更新${record.name}为${record.state === 'in-service' ? '离职' : '在职'}吗？`,
          okText: '确定',
          cancelText: '取消',
          onOk: async () => {
            const { error } = await updateUserState({
              userIds: [record.id],
              enable: record.state === 'leaving',
            });
            if (error) {
              message.error(error.message);
              return;
            }
            message.success('更新成功！');
            handleSearch(1);
          },
        });
      case '3':
        setNeedTransferItems(data);
        setResignModalVisible(true);
        setSelectedUser(record);
        return;
      default:
        return;
    }
  } else {
    return;
  }
};

type FormValue = Partial<{
  userName: string;
  sexType: UserGender;
  userType: UserType;
  mobile: string;
  email: string;
  company: string;
  resourceCode: string;
  enable: UserState;
}>;

export function Users() {
  const dispatch = useDispatch();
  const history = useHistory();
  const [, { checkCode }] = useAuthorized();
  const [form] = Form.useForm<FormValue>();
  const [modalForm] = Form.useForm();
  const typeRef = React.useRef<UserType>('CUSTOMER');
  const [{ loading, total, data }, getUsers] = useUsers();
  const [pagination, setPagination] = React.useState({ pageNum: 1, pageSize: 10 });
  const [selectedIds, setSelectedIds] = React.useState([] as number[]);
  const [selectedRows, setSelectedRows] = React.useState([] as CustomizedUser[]);
  const [visible, setVisible] = React.useState(false);
  const [confirmLoading, setConfirmLoading] = React.useState(false);

  const [resignModalVisible, setResignModalVisible] = React.useState(false);
  const [selectedUser, setSelectedUser] = React.useState<CustomizedUser>();

  const [needTransferItems, setNeedTransferItems] = React.useState<TransferItemType[]>([]);
  const [leaveForm] = Form.useForm();
  const [submitDemissionLoading, setSubmitDemissionLoading] = useState(false);

  const handleSearch = React.useCallback((pageNum: number, pageSize?: number) => {
    setSelectedIds([]);
    setPagination({ pageNum, pageSize: pageSize || 10 });
    getUsers({
      fields: { pageNum: pageNum, pageSize: pageSize || 10, ...form.getFieldsValue() },
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const pageHandler = React.useCallback(
    (_page: number, _pageSize: number) => {
      handleSearch(_page, _pageSize);
    },
    [handleSearch]
  );
  const onHandleEnable = (record: CustomizedUser) => {
    Modal.confirm({
      title: `确定将${record.name}设为${record.state === 'in-service' ? '离职' : '在职'}吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await updateUserState({
          userIds: [record.id],
          enable: record.state === 'leaving',
        });
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('更新成功！');
        handleSearch(1);
      },
    });
  };
  const onHandleUnbind = async (record: CustomizedUser) => {
    const { error } = await mutateUserGroupsOnUser({
      userIds: [record.id],
    });
    if (error) {
      message.error(error.message);
      return;
    }
    message.success('更新成功！');
    handleSearch(1);
  };

  const onHandleEnables = React.useCallback(
    (userState: UserState) => {
      Modal.confirm({
        title: (
          <>
            {`确定将${selectedIds.length}位用户设为`}
            <UserStateText userState={userState} />
            吗？
          </>
        ),
        content: (
          <>
            用户状态设为「
            <UserStateText userState={userState} />」{userState === 'leaving' && '，不可登录系统'}
          </>
        ),
        okText: '确定',
        cancelText: '取消',
        onOk: async () => {
          const { error } = await updateUserState({
            userIds: selectedIds,
            enable: userState === 'in-service',
          });
          if (error) {
            message.error(error.message);
            return;
          }
          message.success('更新成功！');
          handleSearch(1);
        },
      });
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [selectedIds]
  );

  const onHandleDelete = (record: CustomizedUser) => {
    Modal.confirm({
      title: `确定删除用户${record.name}吗？`,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        const { error } = await deleteUser(record.id);
        if (error) {
          message.error(error.message);
          return;
        }
        message.success('删除成功！');
        handleSearch(1);
      },
    });
  };

  const onHandleMutateCompany = React.useCallback(() => {
    modalForm.validateFields().then(async values => {
      setConfirmLoading(true);
      const { error } = await mutateUserCompanyOnUser({
        userIds: selectedIds,
        userType: typeRef.current,
        company: values.company,
      });
      setConfirmLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      message.success('更新成功！');
      setVisible(false);
      handleSearch(1);
      modalForm.resetFields();
    });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [dispatch, selectedIds]);

  React.useEffect(() => {
    handleSearch(1);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  const [exportLoading, setExportLoading] = React.useState(false);
  const generateExportedData = React.useMemo(() => {
    const func: FileExportProps['data'] = async type => {
      setExportLoading(true);
      const params =
        type === 'filtered'
          ? { ...form.getFieldsValue() }
          : type === 'selected'
            ? { userIds: selectedIds }
            : {};
      const { error, data } = await exportUsers(params);
      setExportLoading(false);
      if (error) {
        message.error(error.message);
        throw new Error(error.message);
      }
      return data;
    };
    return func;
  }, [form, selectedIds]);
  const [disableExportFiltered, setDisableExportFiltered] = React.useState(
    Object.values(form.getFieldsValue()).every(value => !value)
  );

  return (
    <Space style={{ width: '100%' }} direction="vertical">
      <Card bordered={false}>{UsersFilter()}</Card>
      <Card bordered={false}>
        <Space size="middle" style={{ width: '100%' }} direction="vertical">
          <Row justify="space-between">
            <Space>
              <Button type="primary" onClick={() => history.push(USERS_CREATOR_ROUTE_PATH)}>
                新建用户
              </Button>
              <SyncUsersButton afterSyncUsers={() => handleSearch(1)} />
              <Dropdown.Button
                type="primary"
                disabled={selectedIds.length === 0}
                menu={{
                  items: [
                    {
                      key: 'in-service',
                      label: (
                        <Typography.Link onClick={() => onHandleEnables('in-service')}>
                          在职
                        </Typography.Link>
                      ),
                    },
                  ],
                }}
                onClick={() => {
                  const types = selectedRows.map(selectedRow => selectedRow.type);
                  const selectedTypes = uniq(types);
                  if (selectedTypes.length > 1 || selectedTypes[0] === 'STAFF') {
                    message.error('选中项不能包含员工类型且类型唯一');
                  } else {
                    setVisible(true);
                    typeRef.current = selectedTypes[0]!;
                  }
                }}
              >
                关联公司
              </Dropdown.Button>
            </Space>
            <FileExport
              text="导出"
              filename="用户列表.xls"
              disabled={exportLoading}
              data={generateExportedData}
              showExportFiltered
              disableExportFiltered={disableExportFiltered}
              showExportSelected
              disableExportSelected={selectedIds.length === 0}
            />
          </Row>
          <UsersDataTable
            data={data}
            loading={loading}
            rowSelection={{
              selectedRowKeys: selectedIds,
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedIds(selectedRowKeys as number[]);
                setSelectedRows(selectedRows);
              },
            }}
            pagination={{
              total,
              current: pagination.pageNum,
              pageSize: pagination.pageSize,
              onChange: pageHandler,
            }}
            operateColumn={{
              column: {
                title: '操作',
                dataIndex: 'action',
                fixed: 'right',
                render: (_, record: CustomizedUser) => {
                  return [
                    <Popconfirm
                      key="remove"
                      title={`确定要收回${record.name}所有权限吗？`}
                      onConfirm={() => onHandleUnbind(record)}
                    >
                      <Typography.Link key="unbind">一键收权</Typography.Link>
                    </Popconfirm>,
                    checkCode('element_user_viewing_ownership_configuration_information') && (
                      <>
                        <Divider key="divide1" type="vertical" />
                        <Link
                          key="edit"
                          target="_blank"
                          to={generateUserDetailRoutePath({ id: record.id })}
                        >
                          查看
                        </Link>
                      </>
                    ),
                    <Divider key="divide2" type="vertical" />,
                    <Dropdown
                      key="dropdown"
                      placement="bottomRight"
                      menu={{
                        items: [
                          {
                            key: 'unbind-all',
                            label: (
                              <Typography.Link
                                onClick={() => {
                                  if (record.state === 'leaving') {
                                    return onHandleEnable(record);
                                  } else {
                                    return ConfirmModal({
                                      record: record,
                                      handleSearch,
                                      setResignModalVisible,
                                      setSelectedUser,
                                      setNeedTransferItems,
                                    });
                                  }
                                }}
                              >
                                {record.state === 'in-service' ? '离职' : '在职'}
                              </Typography.Link>
                            ),
                          },
                          {
                            key: 'delete',
                            label: (
                              <Typography.Link onClick={() => onHandleDelete(record)}>
                                删除
                              </Typography.Link>
                            ),
                          },
                        ],
                      }}
                    >
                      <Button type="link" style={{ padding: 0 }}>
                        ...
                      </Button>
                    </Dropdown>,
                  ];
                },
              },
            }}
          />
        </Space>
      </Card>
      <Modal
        title="关联公司"
        open={visible}
        width={480}
        confirmLoading={confirmLoading}
        destroyOnClose
        okText="确定"
        cancelText="取消"
        onOk={onHandleMutateCompany}
        onCancel={() => setVisible(false)}
      >
        <Form colon={false} form={modalForm} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          <Form.Item
            label="公司名称"
            name="company"
            rules={[{ required: true, message: '请选择公司名称！' }]}
          >
            {typeRef.current === 'CUSTOMER' ? (
              <CustomersSelect style={{ width: 200 }} />
            ) : (
              <VendorSelect style={{ width: 200 }} />
            )}
          </Form.Item>
        </Form>
      </Modal>

      <Modal
        title="离职提示"
        width={480}
        open={resignModalVisible}
        okButtonProps={{
          loading: submitDemissionLoading,
        }}
        afterClose={() => {
          leaveForm.resetFields();
        }}
        cancelButtonProps={{
          disabled: submitDemissionLoading,
        }}
        onCancel={() => {
          setResignModalVisible(false);
        }}
        onOk={async () => {
          leaveForm.validateFields().then(async values => {
            const receiverStaffs: { leavesType: string; handlerId?: number }[] = [];
            if (needTransferItems.includes('SCHEDULE')) {
              receiverStaffs.push({
                leavesType: 'SCHEDULE',
              });
            }
            if ('dailyHandlerUserId' in values) {
              receiverStaffs.push({
                leavesType: 'DAILY_TASK',
                handlerId: values.dailyHandlerUserId,
              });
            }
            setSubmitDemissionLoading(true);
            const { error } = await updateUserState({
              userIds: selectedUser ? [selectedUser.id] : [],
              enable: false,
              leavesStaffs: receiverStaffs,
            });
            setSubmitDemissionLoading(false);
            if (error) {
              message.error(error.message);
              return;
            }
            message.success('更新成功！');
            setResignModalVisible(false);
            handleSearch(1);
          });
        }}
      >
        <Space style={{ width: '100%' }} direction="vertical" size="large">
          <Alert
            type="warning"
            message={
              <Space style={{ marginLeft: 8 }} direction="vertical">
                {[
                  {
                    key: 'SCHEDULE',
                    show: needTransferItems.includes('SCHEDULE'),
                    text: `${selectedUser?.name}有排班记录，离职后无需再考勤`,
                  },
                  {
                    key: 'HANDLER',
                    show: needTransferItems.includes('DAILY_TASK'),
                    text: `${selectedUser?.name}有未处理事项，请移交给交接人`,
                  },
                ]
                  .filter(item => item.show)
                  .map((item, index) => (
                    <Typography.Text key={item.key}>
                      {index + 1}. {item.text}
                    </Typography.Text>
                  ))}
              </Space>
            }
            showIcon
          />
          <Form form={leaveForm}>
            {needTransferItems.includes('DAILY_TASK') && (
              <Form.Item
                label="日常事务交接人"
                name="dailyHandlerUserId"
                rules={[
                  {
                    required: true,
                    message: '日常事务交接人必选',
                  },
                ]}
              >
                <UserSelect
                  style={{ width: 216 }}
                  labelInValue={false}
                  disabledKeys={selectedUser && [selectedUser.id]}
                  userState="in-service"
                />
              </Form.Item>
            )}
          </Form>
        </Space>
      </Modal>
    </Space>
  );

  function UsersFilter() {
    return (
      <Form
        wrapperCol={{ span: 18 }}
        labelCol={{ span: 6 }}
        colon={false}
        form={form}
        onValuesChange={(_, values: FormValue) => {
          setDisableExportFiltered(Object.values(values).every(value => !value));
        }}
      >
        <Row style={{ width: '100%' }}>
          <Col xl={5}>
            <Form.Item label="姓名" name="userName">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="性别" name="sexType">
              <UserGenderSelect allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="类型" name="userType">
              <UserTypeSelect allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="手机号码" name="mobile">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="邮箱" name="email">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="所属公司" name="company">
              <Input allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="所属位置" name="resourceCode">
              <LocationTreeSelect
                disabledTypes={['IDC']}
                showSearch
                allowClear
                authorizedOnly
                style={{ width: 200 }}
              />
            </Form.Item>
          </Col>
          <Col xl={5}>
            <Form.Item label="状态" name="enable">
              <UserStateSelect allowClear style={{ width: 200 }} />
            </Form.Item>
          </Col>
          <Col xl={5} offset={15}>
            <Form.Item label=" ">
              <Space style={{ display: 'flex', justifyContent: 'flex-end', width: 200 }}>
                <Button
                  type="primary"
                  loading={loading}
                  onClick={() => {
                    handleSearch(1, pagination.pageSize);
                  }}
                >
                  搜索
                </Button>
                <Button
                  disabled={loading}
                  onClick={() => {
                    form.resetFields();
                    setPagination({ pageSize: 10, pageNum: 1 });
                    getUsers({ fields: { pageNum: 1, pageSize: 10 } });
                  }}
                >
                  重置
                </Button>
              </Space>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    );
  }
}
