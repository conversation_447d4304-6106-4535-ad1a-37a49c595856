import React from 'react';

import { ConfigProvider } from '@manyun/base-ui.context.config';

import { useRemoteMock } from '@manyun/service.request';

import { Users } from './users';

export const BasicUsers = () => {
  const [ready, setReady] = React.useState(false);

  React.useEffect(() => {
    const mockOff = useRemoteMock('web', {
      adapter: 'default',
    });
    setReady(true);

    return () => {
      mockOff();
    };
  }, []);

  if (!ready) {
    return <span>loading</span>;
  }

  return (
    <ConfigProvider>
      <Users />
    </ConfigProvider>
  );
};
