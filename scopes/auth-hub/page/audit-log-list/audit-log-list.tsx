import React, { useCallback, useEffect, useState } from 'react';
import { useLocation } from 'react-router-dom';
import { useUpdateEffect } from 'react-use';

import type { Moment } from 'moment';
import moment from 'moment';

import { Button } from '@manyun/base-ui.ui.button';
import { DatePicker } from '@manyun/base-ui.ui.date-picker';
import { Form } from '@manyun/base-ui.ui.form';
import { Row } from '@manyun/base-ui.ui.grid';
import { message } from '@manyun/base-ui.ui.message';
import { Space } from '@manyun/base-ui.ui.space';
import { Table } from '@manyun/base-ui.ui.table';
import type { ColumnType, TableProps } from '@manyun/base-ui.ui.table';
import { Typography } from '@manyun/base-ui.ui.typography';
import { FileDownload } from '@manyun/base-ui.util.file-download';
import type { File } from '@manyun/base-ui.util.guess-mime-type';
import { getLocationSearchMap } from '@manyun/base-ui.util.query-string';

import { AuditLog, getAuditLogLocales } from '@manyun/auth-hub.model.audit-log';
import type { AuditLogJSON, AuditLogType } from '@manyun/auth-hub.model.audit-log';
import { fetchAuditLogList } from '@manyun/auth-hub.service.fetch-audit-log-list';
import { AuditLogTypeSelect, AuditLogTypeText } from '@manyun/auth-hub.ui.audit-log-type';
import { LocationTreeSelect } from '@manyun/resource-hub.ui.location-tree-select';
import { webRequest } from '@manyun/service.request';

import styles from './audit-log-list.module.less';

type FormValues = {
  blockGuids?: string[];
  createdAt: Moment[];
  targetType?: AuditLogType;
};

const BLANK_PLACEHOLDER = '--';

export function AuditLogList() {
  const locales = getAuditLogLocales();

  const columns: Array<ColumnType<AuditLogJSON>> = [
    {
      title: locales['fileName'],
      dataIndex: 'fileName',
      key: 'fileName',
      width: 350,
      render: value => (
        <Typography.Text ellipsis={{ tooltip: value }}>
          {value || BLANK_PLACEHOLDER}
        </Typography.Text>
      ),
    },
    {
      title: locales['fileSize'],
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 120,
      render: (_, record) => `${Number(record.fileSize / 1024 / 1024).toFixed(2)} MB`,
    },
    {
      title: locales['createdAt'],
      dataIndex: 'createdAt',
      key: 'createdAt',
      width: 180,
      render: (_, record) => AuditLog.fromJSON(record).getFormattedCreatedAt(),
    },
    {
      title: locales['targetType'],
      dataIndex: 'targetType',
      key: 'targetType',
      width: 180,
      render: (_, record) => <AuditLogTypeText logType={record.targetType} />,
    },
    {
      title: locales['blockGuid'],
      dataIndex: 'blockGuid',
      key: 'blockGuid',
      width: 120,
      render: value => value || BLANK_PLACEHOLDER,
    },
    {
      title: locales['remainingTime'],
      dataIndex: 'remainingTime',
      key: 'remainingTime',
      width: 120,
      render: (_, record) => `${Math.floor(record.remainingTime / 60 / 60 / 24)} 天`,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 80,
      fixed: 'right',
      render: (_, record) => {
        const src = `${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=${
          record.filePath
        }&fileName=${window.encodeURIComponent(record.fileName)}`;
        return (
          <Button type="link" href={src} compact>
            下载
          </Button>
        );
      },
    },
  ];

  const [form] = Form.useForm<FormValues>();
  const { search } = useLocation();
  const logType = getLocationSearchMap<{ ['log-type']?: string }>(search)['log-type'];

  // 表格分页相关
  const [pagination, setPagination] = useState({ page: 1, pageSize: 10, total: 0 });
  const paginationConfig: TableProps<AuditLogJSON>['pagination'] = {
    total: pagination.total,
    current: pagination.page,
    pageSize: pagination.pageSize,
    onChange: (page, pageSize) => {
      setPagination({
        ...pagination,
        page,
        pageSize,
      });
    },
  };

  // 请求数据
  const [shouldSearch, setShouldSearch] = useState<boolean>(true);
  const [loading, setLoading] = useState(false);
  const [tableData, setTableData] = useState<AuditLogJSON[]>([]);
  const [selectedCodes, setSelectedCodes] = useState<React.Key[]>([]);
  const [selectedRows, setSelectedRows] = useState<AuditLogJSON[]>([]);
  const [downloadLoading, setDownloadLoading] = useState<boolean>(false);

  const fileDownloadObject = React.useMemo(() => new FileDownload(), []);

  const handleFilesDownload = useCallback(() => {
    const files = selectedRows.map(file => ({
      name: file.fileName,
      src: `${webRequest.axiosInstance.defaults.baseURL}/dcom/file/download?filePath=${
        file.filePath
      }&fileName=${window.encodeURIComponent(file.fileName)}`,
      ext: file.fileFormat,
    })) as File[];
    setDownloadLoading(true);
    try {
      fileDownloadObject.downloadFiles(files, moment().format('YYYY-MM-DD'));
    } catch {
      setDownloadLoading(false);
      return;
    }
    setDownloadLoading(false);
  }, [selectedRows, fileDownloadObject]);

  useEffect(() => {
    (async () => {
      if (!shouldSearch) {
        return;
      }
      setShouldSearch(false);
      setSelectedCodes([]);
      setSelectedRows([]);
      setLoading(true);
      const values = await form.validateFields();
      const { error, data } = await fetchAuditLogList({
        blockGuids: values.blockGuids,
        startedAt: values.createdAt[0].startOf('day').valueOf(),
        endedAt: values.createdAt[1].endOf('day').valueOf(),
        targetType: values.targetType,
        page: pagination.page,
        pageSize: pagination.pageSize,
      });
      setLoading(false);
      if (error) {
        message.error(error.message);
        return;
      }
      setTableData(data.data);
      setPagination({ ...pagination, total: data.total });
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [shouldSearch]);

  // 每次切换分页，则重新请求一次数据
  useUpdateEffect(() => {
    setShouldSearch(true);
  }, [pagination.page, pagination.pageSize]);

  return (
    <Space size="middle" direction="vertical" className={styles.wrapper}>
      <Row>
        <Form form={form} layout="inline">
          <Form.Item label={locales['blockGuid']} name="blockGuids">
            <LocationTreeSelect
              allowClear
              multiple
              maxTagCount="responsive"
              treeCheckable="true"
              disabledTypes={['IDC']}
              authorizedOnly
              style={{ width: 200 }}
            />
          </Form.Item>
          <Form.Item
            label={locales['createdAt']}
            name="createdAt"
            initialValue={[moment().subtract(1, 'day'), moment()]}
          >
            <DatePicker.RangePicker allowClear={false} />
          </Form.Item>
          <Form.Item
            label={locales['targetType']}
            name="targetType"
            initialValue={
              logType ? logType.replace(/-/g, '_').toUpperCase() : 'INSPECT_OFFLINE_DATA'
            }
          >
            <AuditLogTypeSelect style={{ width: 200 }} />
          </Form.Item>
          <Form.Item>
            <Button type="primary" onClick={() => setShouldSearch(true)}>
              搜索
            </Button>
          </Form.Item>
          <Form.Item>
            <Button
              onClick={() => {
                form.resetFields();
                setShouldSearch(true);
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </Row>
      <Button
        disabled={selectedCodes.length === 0}
        loading={downloadLoading}
        onClick={() => handleFilesDownload()}
      >
        批量下载
      </Button>
      <Table<AuditLogJSON>
        columns={columns}
        scroll={{ x: 'max-content' }}
        style={{ width: '100%' }}
        rowKey="id"
        loading={loading}
        dataSource={tableData}
        pagination={paginationConfig}
        rowSelection={{
          selectedRowKeys: selectedCodes,
          onChange: (selectedRowKeys, selectedRows) => {
            setSelectedCodes(keys => [
              ...keys.filter(key => !tableData.find(item => item.id === key)),
              ...selectedRowKeys,
            ]);
            setSelectedRows(rows => [
              ...rows.filter(row => !tableData.find(item => item.id === row.id)),
              ...selectedRows,
            ]);
          },
        }}
      />
    </Space>
  );
}
