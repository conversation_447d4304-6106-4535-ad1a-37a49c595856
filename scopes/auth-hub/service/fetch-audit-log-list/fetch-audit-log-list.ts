/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import { AuditLog } from '@manyun/auth-hub.model.audit-log';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-audit-log-list.type';

const endpoint = '/dcom/file/list/page/query';

/**
 * @see [Doc](http://172.16.0.17:13000/project/26/interface/api/19596)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      blockGuidList: svcQuery.blockGuids,
      targetType: svcQuery.targetType,
      startTime: svcQuery.startedAt,
      endTime: svcQuery.endedAt,
      pageNum: svcQuery.page,
      pageSize: svcQuery.pageSize,
      fileType: svcQuery.fileType,
      targetId: svcQuery.targetId,
    };

    const { error, data, ...others } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...others,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: { data: data.data.map(AuditLog.fromApiObject), total: data.total },
      ...others,
    };
  };
}
