/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-11-28
 *
 * @packageDocumentation
 */
import type { AuditLogJSON, AuditLogType, BackendAuditLog } from '@manyun/auth-hub.model.audit-log';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  blockGuids?: string[];
  targetType?: AuditLogType;
  startedAt?: number;
  endedAt?: number;
  page: number;
  pageSize: number;
  fileType?: string;
  targetId?: string;
};

export type SvcRespData = {
  data: AuditLogJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendAuditLog[] | null;
  total: number;
} | null;

export type ApiQ = {
  blockGuidList?: string[];
  targetType?: AuditLogType;
  startTime?: number;
  endTime?: number;
  pageNum: number;
  pageSize: number;
  fileType?: string;
  targetId?: string;
};

export type ApiR = ListResponse<RequestRespData>;
