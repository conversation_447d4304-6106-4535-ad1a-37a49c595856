/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-paged-user-groups';
import type { ApiQ, SvcRespData } from './fetch-paged-user-groups.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchPagedUserGroups(variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
