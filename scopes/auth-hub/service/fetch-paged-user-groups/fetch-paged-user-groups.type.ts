/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type GroupModel = {
  id: number;
  groupName: string;
  groupCode: string;
  gmtCreate: number;
  gmtModified: number;
  creatorId: number;
  creatorName: string;
  modifierId: number;
  modifierName: string;
  remarks: string;
  /** 资源 code 列表 */
  resourceCodes?: string[];
};

export type SvcRespData = {
  data: GroupModel[];
  total: number;
};

export type RequestRespData = {
  data: GroupModel[] | null;
  total: number;
} | null;

// SvcQuery 与 ApiQ 相同
export type ApiQ = {
  searchName?: string;
  pageNum: number;
  pageSize: number;
  searchType?: 'GROUP_NAME' | 'CREATOR_NAME';
  needResources?: boolean;
  /** 资源 code 列表 */
  resourceCodes?: string[];
  /** 需要排除掉的用户组 ID 集合 */
  notAllowGroupSet?: number[] | null;
  groupIdList?: number[] | null;
  /** 机房列表 */
  idcTags?: string[];
};

export type ApiR = ListResponse<GroupModel[] | null>;
