---
description: 'A fetchPagedUserGroups HTTP API service.'
labels: ['service', 'http', fetch-paged-user-groups]
---

## 概述

TODO

## 使用

### Browser

```ts
import { fetchPagedUserGroups } from '@auth-hub/service.fetch-paged-user-groups';

const { error, data } = await fetchPagedUserGroups('success');
const { error, data } = await fetchPagedUserGroups('error');
```

### Node

```ts
import { fetchPagedUserGroups } from '@auth-hub/service.fetch-paged-user-groups/dist/index.node';

const { data } = await fetchPagedUserGroups('success');

try {
  const { data } = await fetchPagedUserGroups('error');
} catch(error) {
  // ...
}
```
