/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchPagedUserGroups as webService } from './fetch-paged-user-groups.browser';
import { fetchPagedUserGroups as nodeService } from './fetch-paged-user-groups.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ pageNum: 1, pageSize: 10 });

  expect(error).toBe(undefined);
  expect(data).not.toBeNull();
});
test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ pageNum: 1, pageSize: 10 });

  expect(error).toBe(undefined);
  expect(data).not.toBeNull();
});
