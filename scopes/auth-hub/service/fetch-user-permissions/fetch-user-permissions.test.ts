/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2022-1-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserPermissions as webService } from './fetch-user-permissions.browser';
import { fetchUserPermissions as nodeService } from './fetch-user-permissions.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService();

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService();

  expect(error).toBe(undefined);
  expect(data).toHaveProperty('length');
});
