/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-18
 *
 * @packageDocumentation
 */
import type { WebsiteCode } from '@manyun/dc-brain.model.website';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';
import type {
  BackendPermission,
  BackendPermissionType,
  Permission,
} from '@manyun/iam.model.permission';

export type SvcQuery = {
  types?: BackendPermissionType[];
  websites?: WebsiteCode[];
};

export type SvcRespData = Permission[];

export type RequestRespData = {
  data: BackendPermission[] | null;
  total: number;
} | null;

export type ApiQ = {
  permissionTypes?: BackendPermissionType[] | null;
  siteList?: WebsiteCode[];
};

export type ApiR = ListResponse<BackendPermission[] | null>;
