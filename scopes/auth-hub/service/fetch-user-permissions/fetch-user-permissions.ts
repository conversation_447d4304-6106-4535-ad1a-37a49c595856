/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-18
 *
 * @packageDocumentation
 */
import { Permission } from '@manyun/iam.model.permission';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-user-permissions.type';

const endpoint = '/pm/permission/userPermissions';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/5720)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery?: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const apiQ: ApiQ = {
      permissionTypes: svcQuery?.types ?? [],
      siteList: svcQuery?.websites ?? [],
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data.map(Permission.fromApiObject), ...rest };
  };
}
