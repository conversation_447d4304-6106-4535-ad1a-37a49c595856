/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcRespData } from './fetch-users-by-resource-code.type';

const endpoint = '/taskcenter/base/list/assignee';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/98/interface/api/19023)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (variant: ApiQ): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(
      endpoint,
      variant
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(userI => {
          return {
            ...userI,
            name: userI.userName,
          };
        }),
        total: data.total,
      },
      ...rest,
    };
  };
}
