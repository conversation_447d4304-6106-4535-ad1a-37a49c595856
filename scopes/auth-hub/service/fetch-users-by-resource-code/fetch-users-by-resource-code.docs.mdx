---
description: 'A fetchUsersByResourceCode HTTP API service.'
labels: ['service', 'http']
---

## 概述

查询某楼栋下的用户

## 使用

### Browser

```ts
import { fetchUsersByResourceCode } from '@auth-hub/service.fetch-users-by-resource-code';

const { error, data } = await fetchUsersByResourceCode({ resourceCode: 'EC01.A' });
const { error, data } = await fetchUsersByResourceCode({ resourceCode: '000' });
```

### Node

```ts
import { fetchUsersByResourceCode } from '@auth-hub/service.fetch-users-by-resource-code/dist/index.node';
```
