/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-8-9
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type UserInfo = {
  id: number;
  name: string;
  loginName: string;
};

export type BackendUser = {
  id: number;
  userName: string;
  loginName: string;
};

export type SvcRespData = {
  data: UserInfo[];
  total: number;
};

export type RequestRespData = {
  data: BackendUser[] | null;
  total: number;
} | null;

export type ApiQ = {
  resourceCode: string;
  containOneself?: boolean;
};

export type ApiR = ListResponse<BackendUser[] | null>;
