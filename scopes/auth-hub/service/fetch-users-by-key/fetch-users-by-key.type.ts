/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-16
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  key?: string | number;
  needDeptTree?: boolean;
  resourceCode?: string;
};

export type User = {
  deleted?: boolean;
  email?: string;
  id: number;
  loginName?: string;
  mobile?: string;
  name?: string;
  fullDeptName?: string;
  positionName?: string;
};

export type BackendUser = {
  deleted?: boolean;
  email?: string;
  id: number;
  loginName?: string;
  mobile?: string;
  userName?: string;
  fullDeptName?: string;
  position?: string;
};

export type SvcRespData = {
  data: User[];
  total: number;
};

export type RequestRespData = {
  data: BackendUser[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<BackendUser[] | null>;
