/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-16
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-users-by-key';
import type { SvcQuery, SvcRespData } from './fetch-users-by-key.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function fetchUsersByKey(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
