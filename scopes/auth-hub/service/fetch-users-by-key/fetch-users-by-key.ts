/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-3-16
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcQuery, SvcRespData } from './fetch-users-by-key.type';

const endpoint = '/pm/user/userListByKey';

/**
 * @see [关键词查询用户列表](http://172.16.0.17:13000/project/78/interface/api/2024)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, SvcQuery>(endpoint, {
      params,
    });

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(d => ({ ...d, name: d.userName, positionName: d.position })),
        total: data.total,
      },
      ...rest,
    };
  };
}
