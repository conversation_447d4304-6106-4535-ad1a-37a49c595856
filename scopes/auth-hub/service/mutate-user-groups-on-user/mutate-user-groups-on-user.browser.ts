/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { bindEndpoint, unbindAllEndpoint } from './mutate-user-groups-on-user';
import type { ApiQ, SvcQuery, SvcRespData } from './mutate-user-groups-on-user.type';

/**
 * 在这里补充 Service 说明
 *
 * @see [绑定用户组]](http://172.16.0.17:13000/project/78/interface/api/1896)
 * @see [解绑用户组](http://172.16.0.17:13000/project/78/interface/api/1952) 暂不实现
 * @see [一键解除所有用户组](http://172.16.0.17:13000/project/78/interface/api/2744)
 * @param query
 * @returns
 */
export async function mutateUserGroupsOnUser(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  if ('groupIds' in query) {
    const apiQ: ApiQ = {
      groupIds: query.groupIds!,
      userIds: query.userIds,
    };

    return await webRequest.tryPost<SvcRespData, ApiQ>(bindEndpoint, apiQ);
  }
  const apiQ: ApiQ = query;

  return await webRequest.tryPost<SvcRespData, ApiQ>(unbindAllEndpoint, apiQ);
}
