/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './mutate-user-groups-on-user.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { mutateUserGroupsOnUser as webService } from './mutate-user-groups-on-user.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve bind success response', async () => {
  const { error, data } = await webService({
    userIds: [1],
    groupIds: [1],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('should resolve bind error response', async () => {
  const { error, data } = await webService({
    userIds: [1],
    groupIds: [2],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('should resolve unbind all success response', async () => {
  const { error, data } = await webService({
    userIds: [1],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('should resolve unbind all error response', async () => {
  const { error, data } = await webService({
    userIds: [2],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
