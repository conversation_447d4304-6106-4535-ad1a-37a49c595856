/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-21
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './update-role';
import type { SvcQuery, SvcRespData } from './update-role.type';

const executor = getExecutor(webRequest);

/**
 * @param svcQuery
 * @returns
 */
export function updateRole(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
