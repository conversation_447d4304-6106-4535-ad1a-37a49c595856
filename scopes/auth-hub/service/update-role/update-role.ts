/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-21
 *
 * @packageDocumentation
 */
import { Role } from '@manyun/auth-hub.model.role';
import { WebsiteCode } from '@manyun/dc-brain.model.website';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './update-role.type';

const endpoint = '/pm/role/update';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1752)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const {
      id,
      gmtCreate,
      gmtModified,
      creatorId,
      creatorName,
      modifierId,
      modifierName,
      site,
      ...params
    } = Role.fromJSON({
      ...svcQuery,
      website: WebsiteCode.DcBase,
    }).toApiObject();

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, { ...params, roleId: id });
  };
}
