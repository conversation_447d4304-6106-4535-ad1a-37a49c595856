/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-21
 *
 * @packageDocumentation
 */
import type { BackendRole, RoleJSON } from '@manyun/auth-hub.model.role';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = Omit<
  RoleJSON,
  'createdAt' | 'modifiedAt' | 'createBy' | 'modifyBy' | 'website'
>;

export type SvcRespData = number | null;

export type ApiQ = Omit<
  BackendRole,
  | 'id'
  | 'gmtCreate'
  | 'gmtModified'
  | 'creatorId'
  | 'creatorName'
  | 'modifierId'
  | 'modifierName'
  | 'site'
> & {
  roleId: number;
};

export type ApiR = Response<number | null>;
