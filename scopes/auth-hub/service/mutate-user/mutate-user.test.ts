/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateUser as webService } from './mutate-user.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ id: 1, jobDescriptions: '我是职位描述' });
  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('should resolve error response', async () => {
  const { error, data } = await webService({
    id: 0,
    jobDescriptions: '我是职位描述',
  });
});
