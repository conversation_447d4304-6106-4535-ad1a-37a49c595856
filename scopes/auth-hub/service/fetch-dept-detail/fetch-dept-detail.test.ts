/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchDeptDetail as webService } from './fetch-dept-detail.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ deptId: 'D00401' });
  expect(error).toBe(undefined);
  expect(data !== null).toBe(true);
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ deptId: '' });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBe(null);
});
