/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  deptId: string;
};

export type DeptDetailInfo = {
  id: number;
  deptId: string;
  nameZh: string;
  nameEng: string;
  channel: string;
  parentId: string;
  status: string;
  fullDeptName: string;
};

export type SvcRespData = DeptDetailInfo | null;

export type RequestRespData = DeptDetailInfo | null;

export type ApiQ = {
  deptId: string;
};

export type ApiR = Response<RequestRespData>;
