/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-27
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-dept-detail';
import type { SvcQuery, SvcRespData } from './fetch-dept-detail.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchDeptDetail(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
