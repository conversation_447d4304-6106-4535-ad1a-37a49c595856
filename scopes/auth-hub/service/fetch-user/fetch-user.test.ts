/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUser as webService } from './fetch-user.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({ name: 'admin' });
  expect(error).toBe(undefined);
  expect(data).toHaveProperty('name');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ id: 2 });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data).toBeNull();
});
