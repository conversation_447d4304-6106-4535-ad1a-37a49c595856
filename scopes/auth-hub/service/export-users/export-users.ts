/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-3
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './export-users.type';

const endpoint = '/pm/user/export';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/20217)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const enable = typeof svcQuery.enable == 'undefined' ? undefined : svcQuery.enable === 'in-service';
    const params: ApiQ = { ...svcQuery, enable };

    return request.tryPost<RequestRespData, ApiQ>(endpoint, params, {
      responseType: 'blob',
    });
  };
}
