/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-3
 *
 * @packageDocumentation
 */
import type { SvcQuery as FetchUsersParams } from '@manyun/auth-hub.service.fetch-paged-users';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

// 支持选中的用户数组以及筛选后的用户（和分页查询用户信息接口参数保持一致）
export type SvcQuery = Omit<FetchUsersParams, 'pageSize' | 'pageNum'> & {
  userIds?: number[];
};

export type SvcRespData = Blob;

export type RequestRespData = Blob;

export type ApiQ = Omit<SvcQuery, 'enable'> & {
  enable?: boolean | null;
};

export type ApiR = Response<Blob>;
