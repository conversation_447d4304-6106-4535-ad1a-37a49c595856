/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-2-3
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { exportUsers } from './export-users.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error } = await exportUsers({});

  expect(error).toBe(undefined);
});
