/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { UserType } from '@manyun/auth-hub.model.user';
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  company?: string;
  userIds: number[];
  userType?: UserType;
};

export type SvcRespData = boolean;

export type BindApiQ = {
  company: string;
  userIds: number[];
  userType: UserType;
};

export type UnbindApiQ = {
  userIds: number[];
};

export type ApiR = WriteResponse;
