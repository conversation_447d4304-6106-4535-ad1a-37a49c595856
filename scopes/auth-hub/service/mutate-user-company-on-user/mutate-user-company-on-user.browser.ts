/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { bindEndpoint, unbindEndpoint } from './mutate-user-company-on-user';
import type {
  BindApiQ,
  SvcQuery,
  SvcRespData,
  UnbindApiQ,
} from './mutate-user-company-on-user.type';

/**
 * 用户管理 用户绑定/解绑公司
 *
 * @see [公司绑定](http://172.16.0.17:13000/project/78/interface/api/1992)
 * @see [公司解绑](http://172.16.0.17:13000/project/78/interface/api/1992)
 *
 * @param variant
 * @returns
 */
export async function mutateUserCompanyOnUser(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  if ('company' in query) {
    const apiQ: BindApiQ = {
      company: query.company!,
      userIds: query.userIds,
      userType: query.userType!,
    };
    return await webRequest.tryPost<SvcRespData, BindApiQ>(bindEndpoint, apiQ);
  }
  const apiQ: UnbindApiQ = query;
  return await webRequest.tryPost<SvcRespData, UnbindApiQ>(unbindEndpoint, apiQ);
}
