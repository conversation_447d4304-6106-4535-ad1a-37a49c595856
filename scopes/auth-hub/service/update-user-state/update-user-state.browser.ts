/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-15
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './update-user-state';
import type { ApiQ, SvcQuery, SvcRespData } from './update-user-state.type';

/**
 * 用户管理 批量更新用户是否在职
 *
 * @see [批量更新用户是否在职](http://yapi.manyun-local.com/project/78/interface/api/2800)
 *
 * @param query
 * @returns
 */
export async function updateUserState(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const apiQ: ApiQ = query;

  return await webRequest.tryPost<SvcRespData, ApiQ>(endpoint, apiQ);
}
