/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-15
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type LeaveStaffInfo = { leavesType: string; handlerId?: number };
export type SvcQuery = {
  enable: boolean;
  userIds: number[];
  leavesStaffs?: LeaveStaffInfo[];
};

export type SvcRespData = boolean;

export type ApiQ = {
  enable: boolean;
  userIds: number[];
  leavesStaffs?: LeaveStaffInfo[]; //离职后转交事项接手人信息
};

export type ApiR = WriteResponse;
