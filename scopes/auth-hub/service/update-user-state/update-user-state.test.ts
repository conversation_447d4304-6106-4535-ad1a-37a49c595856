/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-15
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './update-user-state.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { updateUserState as webService } from './update-user-state.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    enable: true,
    userIds: [1],
  });

  expect(error).toBe(undefined);
  expect(data).toBe(true);
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ enable: false, userIds: [1] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
