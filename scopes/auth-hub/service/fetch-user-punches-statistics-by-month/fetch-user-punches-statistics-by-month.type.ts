/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  startTime?: number;
  endTime?: number;
  staffId?: number;
  tenantId?: number;
  page?: number;
  pageSize?: number;
  needTotalCount?: boolean;
  bizScenario?: string;
  idcs?: string[];
  blocks?: string[];
  orderDescs?: {
    asc?: boolean;
    col?: string;
  };
};

export type BackendPunchesStatistics = {
  absentTimes: number;
  attendanceDay: number;
  blockTag: string;
  earlyOffMinutes: number;
  earlyOffTimes: number;
  lateOnMinutes: number;
  lateTimes: number;
  missOffCardTimes: number;
  missOnCardTimes: number;
  staffId: number;
  staffName: string;
  workTime: number;
};

export type SvcRespData = {
  data: BackendPunchesStatistics[];
  total: number;
};

export type RequestRespData = {
  data: BackendPunchesStatistics[] | null;
  total: number;
};

export type ApiQ = {
  bizScenario?: { uniqueIdentity: string } /**BizScenario */;
  endDate?: number /**结束时间 */;
  startDate?: number /**开始时间 */;
  staffId?: number /**员工id */;
  tenantId?: number /**租户id */;
  pageSize?: number /** 分页大小 */;
  pageNum?: number /**分页码 */;
  needTotalCount?: boolean /**是否需要数量 */;
  blockTagList?: string[];
  idcTagList?: string[];
  orderDescs?: {
    asc?: boolean;
    col?: string;
  };
};

export type ApiR = ListResponse<RequestRespData['data']>;
