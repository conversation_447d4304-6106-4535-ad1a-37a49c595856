/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-user-punches-statistics-by-month';
import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-user-punches-statistics-by-month.type';

/**
 * 用户考勤月统计
 *
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1096)
 *
 * @param variant
 * @returns
 */
export async function fetchUserPunchesStatisticsByMonth(
  svcData: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = {
    pageSize: svcData.pageSize,
    pageNum: svcData.page,
    startDate: svcData.startTime,
    endDate: svcData.endTime,
    staffId: svcData.staffId,
    tenantId: svcData.tenantId,
    needTotalCount: svcData.needTotalCount,
    bizScenario:
      svcData.bizScenario == undefined ? undefined : { uniqueIdentity: svcData.bizScenario },
    blockTagList: svcData.blocks,
    idcTagList: svcData.idcs,
    orderDescs: svcData.orderDescs,
  };
  const {
    error,
    data: { data, total },
    ...rest
  } = await webRequest.tryPost<RequestRespData, ApiQ>(endpoint, params);

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total,
      },
    };
  }

  return { error, data: { data, total }, ...rest };
}
