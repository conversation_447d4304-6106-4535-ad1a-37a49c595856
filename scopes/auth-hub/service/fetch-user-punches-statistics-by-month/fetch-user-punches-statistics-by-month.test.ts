/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserPunchesStatisticsByMonth as webService } from './fetch-user-punches-statistics-by-month.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    endTime: 1639324799999,
    startTime: 1638288000000,
    staffId: 11,
  });
  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(2);
});

test('should resolve null response', async () => {
  const { error, data } = await webService({
    staffId: 1,
    startTime: 0,
    endTime: 0,
  });
  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(0);
});

test('should resolve error response', async () => {
  const { error, data } = await webService({
    staffId: 11,
    startTime: 0,
    endTime: 0,
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(0);
});
