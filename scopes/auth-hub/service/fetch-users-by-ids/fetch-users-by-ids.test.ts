/**
 * <AUTHOR> W <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUsersByIds as webService } from './fetch-users-by-ids.browser';
import { fetchUsersByIds as nodeService } from './fetch-users-by-ids.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({ ids: [0, 1, 2] });

  expect(error).toBe(undefined);
  expect(data.length).toBe(3);
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({ ids: [999, 1000] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.length).toBe(0);
});

test('[node] should resolve success response', async () => {
  const { error, data } = await nodeService({ ids: [0, 1, 2] });

  expect(error).toBe(undefined);
  expect(data.length).toBe(3);
});

test('[node] should resolve error response', async () => {
  const { error, data } = await nodeService({ ids: [999, 1000] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.length).toBe(0);
});
