/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, ApiR, SimpleUser, SvcQuery } from './fetch-users-by-ids.type';

const endpoint = '/pm/user/userListByUserIds';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/2040)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SimpleUser[]>> => {
    const params: ApiQ = { userIds: svcQuery.ids };

    const { error, data, ...rest } = await request.tryPost<Pick<ApiR, 'data' | 'total'>, ApiQ>(
      endpoint,
      params
    );

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return {
      error,
      data: data.data.map(backendSimpleUser => ({
        id: backendSimpleUser.id,
        name: backendSimpleUser.userName,
        login: backendSimpleUser.loginName,
        mobile: backendSimpleUser.mobile,
        email: backendSimpleUser.email,
        isDeleted: backendSimpleUser.deleted,
        jobNumber: backendSimpleUser.jobNumber,
      })),
      ...rest,
    };
  };
}
