/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  ids: number[];
};

export type SimpleUser = {
  id: number;
  name: string;
  login: string;
  mobile: string;
  email: string;
  isDeleted: boolean;
  jobNumber: string;
};

export type BackendSimpleUser = {
  deleted: boolean;
  email: string;
  id: number;
  loginName: string;
  mobile: string;
  userName: string;
  jobNumber: string;
};

export type PaginatedBackendSimpleUser = {
  data: BackendSimpleUser[];
  total: number;
};

export type ApiQ = {
  userIds: number[];
};

export type ApiR = ListResponse<BackendSimpleUser[] | null>;
