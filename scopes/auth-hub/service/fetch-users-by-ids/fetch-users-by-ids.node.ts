/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-4-18
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-users-by-ids';
import type { SimpleUser, SvcQuery } from './fetch-users-by-ids.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function fetchUsersByIds(svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SimpleUser[]>> {
  return executor(svcQuery);
}
