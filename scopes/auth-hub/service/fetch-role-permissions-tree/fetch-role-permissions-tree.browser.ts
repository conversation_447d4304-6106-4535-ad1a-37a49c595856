/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-role-permissions-tree';
import type { SvcQuery, SvcRespData } from './fetch-role-permissions-tree.type';

const executor = getExecutor(webRequest);

/**
 *
 * @param params
 * @returns
 */
export function fetchRolePermissionsTree(
  params: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
