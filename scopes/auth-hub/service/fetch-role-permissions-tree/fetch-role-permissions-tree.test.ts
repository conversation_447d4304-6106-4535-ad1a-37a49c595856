/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-24
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchRolePermissionsTree } from './fetch-role-permissions-tree.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error } = await fetchRolePermissionsTree({ roleId: 1 });

  expect(error).toBe(undefined);
});
