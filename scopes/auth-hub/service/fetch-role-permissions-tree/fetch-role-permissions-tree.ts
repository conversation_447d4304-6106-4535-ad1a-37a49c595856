/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-24
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-role-permissions-tree.type';

const endpoint = '/pm/role/permissionsTree';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1728)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const {
      error,
      data: { data, total },
      ...rest
    } = await request.tryGet<RequestRespData, ApiQ>(endpoint, {
      params,
    });
    if (error || data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total,
        },
      };
    }

    return { error, data: { data, total }, ...rest };
  };
}
