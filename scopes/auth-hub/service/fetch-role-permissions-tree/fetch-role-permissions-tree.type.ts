/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-10-24
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  roleId?: number;
};

export type BackendPermissionNode = {
  id: number;
  parentId: number | null;
  chlidren: BackendPermissionNode[] | null;
};

export type SvcRespData = {
  data: BackendPermissionNode[];
  total: number;
};

export type RequestRespData = {
  data: BackendPermissionNode[] | null;
  total: number;
};

export type ApiQ = {
  roleId?: number;
};

export type ApiR = ListResponse<BackendPermissionNode[] | null>;
