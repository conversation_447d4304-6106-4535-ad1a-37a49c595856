/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-7
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-modify-type';
import type { SvcRespData } from './fetch-modify-type.type';

const executor = getExecutor(webRequest);

export function fetchModifyType(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
