/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-7
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-modify-type.type';

const endpoint = '/dcom/modify/get/modify/type';

/**
 * @see [Doc](http://172.16.0.17:13000/project/172/interface/api/19304)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    return request.tryGet<RequestRespData>(endpoint);
  };
}
