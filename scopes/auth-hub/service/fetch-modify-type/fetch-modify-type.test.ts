/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-9-7
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchModifyType as webService } from './fetch-modify-type.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error } = await webService();

  expect(error).toBe(undefined);
});
