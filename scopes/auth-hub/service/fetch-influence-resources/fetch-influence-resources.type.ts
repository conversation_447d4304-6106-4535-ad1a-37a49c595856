/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-10
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcRespData = {
  data: string[];
  total: number;
};

export type RequestRespData = {
  data: string[] | null;
  total: number;
} | null;

//apiQ 与svcQ相同
export type ApiQ = {
  userId: string;
  groupId: number;
};

export type ApiR = ListResponse<string[] | null>;
