/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-6-10
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-influence-resources';
import type { ApiQ, SvcRespData } from './fetch-influence-resources.type';

const executor = getExecutor(nodeRequest);

/**
 * @param variant
 * @returns
 */
export function fetchInfluenceResources(
  variant: ApiQ
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
