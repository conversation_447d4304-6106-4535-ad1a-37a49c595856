/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-22
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-dept-list';
import type { SvcRespData } from './fetch-dept-list.type';

const executor = getExecutor(webRequest);

export function fetchDeptList(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
