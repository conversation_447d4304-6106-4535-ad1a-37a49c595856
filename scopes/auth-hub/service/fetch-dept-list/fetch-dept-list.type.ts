/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-22
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type DeptInfo = {
  id: number;
  deptId: string;
  nameZh: string;
  nameEng: string;
  channel: string;
  parentId: string;
  status: string;
  fullDeptName: string;
};

export type SvcRespData = {
  data: DeptInfo[];
  total: number;
};

export type RequestRespData = {
  data: DeptInfo[] | null;
  total: number;
} | null;

export type ApiR = ListResponse<DeptInfo[] | null>;
