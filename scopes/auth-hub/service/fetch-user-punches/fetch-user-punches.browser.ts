/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-user-punches';
import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-user-punches.type';

/**
 * 考勤记录
 *
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/2784)
 *
 * @param variant
 * @returns
 */
export async function fetchUserPunches(
  svcData: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = {
    startDate: svcData.startDate,
    endDate: svcData.endDate,
    staffId: svcData.staffId,
  };

  const {
    error,
    data: { data, total },
    ...rest
  } = await webRequest.tryPost<RequestRespData, ApiQ>(endpoint, params);

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total,
      },
    };
  }

  return { error, data: { data, total }, ...rest };
}
