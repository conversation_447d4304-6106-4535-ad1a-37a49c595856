/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  endDate?: number;
  startDate?: number;
  staffId?: number;
};

export type BackendUserPunche = {
  id: number;
  staffId: number /**员工id */;
  attGroupId: number /**考勤组id */;
  dutyGroupId: number /**班组id */;
  shiftsId: number | null /**班制id */;
  jobNumber: null /**员工工号 */;
  dutyId: number /**班次id */;
  staffScheduleId: number /**员工排班id */;
  staffName: string /**员工名称 */;
  attGroupName: string /**考勤组名 */;
  idcTag: string /**机房tag */;
  blockTag: string /**楼栋tag */;
  dutyName: string /**班次名 */;
  scheduleDate: number /**排班日期 */;
  onDutyTime: number | null /**上班时间 */;
  offDutyTime: number | null /**下班时间 */;
  workTime: number /**工作时长 */;
  lateOnMinutes: number /**上班迟到时长 */;
  earlyOffMinutes: number /**早退时长 */;
  lateOn: boolean /**是否迟到 */;
  earlyOff: boolean /**是否早退 */;
  absent: boolean /**是否缺卡 */;
  leave: boolean /**是否请假 */;
  missOnCard: boolean /**是否缺上班卡 */;
  missOffCard: boolean /**是否缺下班卡 */;
  resultDesc: string | null;
  isDeleted: boolean;
  lateOffMinutes: number | null /**下班迟到时长 */;
  dutyGroupName: string /** 班组名*/;
  scheduleStartTime: number /**排班开始时间 */;
  scheduleEndTime: number /**排班结束时间 */;
};

export type SvcRespData = {
  data: BackendUserPunche[];
  total: number;
};

export type RequestRespData = {
  data: BackendUserPunche[] | null;
  total: number;
};

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<RequestRespData['data']>;
