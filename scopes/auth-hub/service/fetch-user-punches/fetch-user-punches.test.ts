/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserPunches as webService } from './fetch-user-punches.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});
/**@Yjx fixed */
test('should resolve success response', async () => {
  const { error, data } = await webService({
    endDate: 1639497599999,
    startDate: 1638288000000,
    staffId: 1,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(3);
});

test('should resolve null response', async () => {
  const { error, data } = await webService({ endDate: 0, startDate: 0, staffId: 1 });
  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(0);
});
