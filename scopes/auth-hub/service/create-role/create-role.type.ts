/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-15
 *
 * @packageDocumentation
 */
import type { BackendRole, RoleJSON } from '@manyun/auth-hub.model.role';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = Omit<RoleJSON, 'id' | 'createdAt' | 'modifiedAt' | 'createBy' | 'modifyBy'>;

export type SvcRespData = number | null;

export type ApiQ = Omit<
  BackendRole,
  'id' | 'gmtCreate' | 'gmtModified' | 'creatorId' | 'creatorName' | 'modifierId' | 'modifierName'
>;

export type ApiR = Response<number | null>;
