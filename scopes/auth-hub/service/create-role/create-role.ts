/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-6-15
 *
 * @packageDocumentation
 */
import { Role } from '@manyun/auth-hub.model.role';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './create-role.type';

const endpoint = '/pm/role/add';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1664)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const {
      id,
      gmtCreate,
      gmtModified,
      creatorId,
      creatorName,
      modifierId,
      modifierName,
      ...params
    } = Role.fromJSON({
      ...svcQuery,
      id: 0,
    }).toApiObject();

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
