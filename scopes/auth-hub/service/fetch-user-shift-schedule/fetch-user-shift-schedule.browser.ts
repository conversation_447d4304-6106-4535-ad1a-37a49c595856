/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-user-shift-schedule';
import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-user-shift-schedule.type';

/**
 * 用户排班考勤查询
 *
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1784)
 *
 * @param variant
 * @returns
 */
export async function fetchUserShiftSchedule(
  svcData: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = { ...svcData };

  const {
    error,
    data: { data, total },
    ...rest
  } = await webRequest.tryPost<RequestRespData, ApiQ>(endpoint, params);

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total,
      },
    };
  }

  return { error, data: { data, total }, ...rest };
}
