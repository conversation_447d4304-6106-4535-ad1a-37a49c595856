/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  startDate: number;
  endDate: number;
  staffId: number;
};

export enum PunchTimeType {
  APPROVING = 'APPROVING',
  PASS = 'PASS',
}

export const PUNCH_TIME_TYPE_MAPPER: Record<PunchTimeType, string> = {
  [PunchTimeType.APPROVING]: '补卡(审)',
  [PunchTimeType.PASS]: '补卡',
};

/**@Yjx 是否需要列出所有的枚举 */
export enum ScheduleSceneStatus {
  LEAVE_IN = 'LEAVE_IN',
  LEAVE_OUT = 'LEAVE_OUT',
  LEAVE_OUT_PROCESS = 'LEAVE_OUT_PROCESS',
  EXCHANGE = 'EXCHANGE',
  EXCHANGE_PROCESS = 'EXCHANGE_PROCESS',
}

export const SCHEDULE_SCENE_STATUS_MAPPER: Record<ScheduleSceneStatus, string> = {
  [ScheduleSceneStatus.LEAVE_IN]: '顶班',
  [ScheduleSceneStatus.LEAVE_OUT]: '请假',
  [ScheduleSceneStatus.LEAVE_OUT_PROCESS]: '请假(审)',
  [ScheduleSceneStatus.EXCHANGE]: '换班',
  [ScheduleSceneStatus.EXCHANGE_PROCESS]: '换班(审)',
};

export type BackendUserShiftSchedule = {
  blockTag: string /**楼栋guid */;
  dutyGroupId: number /**班组id */;
  dutyGroupName: string /**班组名 */;
  dutyId: number /**班次id */;
  dutyName: string /**班次名 */;
  groupScheduleUnicode: string /**排班唯一标志 */;
  id: number /**排班id */;
  offDutySupplyCheckScene: {
    code: PunchTimeType;
    desc: string;
    scene: string;
  } | null /**下班补卡场景  APPROVING("补卡(审)"),   PASS("补卡") */;
  offDutyTime: number /**下班时间 */;
  onDutySupplyCheckScene: {
    code: PunchTimeType;
    desc: string;
    scene: string;
  } | null /**上班补卡场景 APPROVING("补卡(审)"),   PASS("补卡") */;
  onDutyTime: number /**上班时间 */;
  scheduleDate: number /**排班日期 */;
  scheduleScene: {
    code: ScheduleSceneStatus;
    desc: string;
    scene: string;
  } /**排班场景 */;
  shiftsId: number /**班制id */;
  staffId: number /**员工id */;
  timeInterval: string /**排班时间段 */;
  scheduleStartTime: number /**排班开始时间 */;
  scheduleEndTime: number /**排班结束时间 */;
  enable?: boolean /**是否是全部请假 还是部分请假 */;
  outWorkRecordVOs?: {
    bizId: string /**业务id */;
    processNo: string /**审批流程id */;
    staffScheduleId: string /***排班id */;
    startTime: number /**开始时间 */;
    endTime: number /**结束时间 */;
    totalTime: number /**外勤时长 */;
    processStatus: string /**流程状态 */;
  }[] /**外勤记录列表 */;
  systemChange: boolean /**是否为系统订正 */;
};

export type SvcRespData = {
  data: BackendUserShiftSchedule[];
  total: number;
};

export type RequestRespData = {
  data: BackendUserShiftSchedule[] | null;
  total: number;
};

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<RequestRespData['data']>;
