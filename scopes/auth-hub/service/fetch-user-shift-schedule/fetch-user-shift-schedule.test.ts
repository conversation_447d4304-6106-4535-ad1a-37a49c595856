/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserShiftSchedule as webService } from './fetch-user-shift-schedule.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    startDate: 1638288000000,
    endDate: 1639324799999,
    staffId: 1,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(2);
});

test('should resolve null response', async () => {
  const { error, data } = await webService({
    startDate: 0,
    endDate: 0,
    staffId: 1,
  });
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
  expect(data.data).toHaveLength(0);
});
