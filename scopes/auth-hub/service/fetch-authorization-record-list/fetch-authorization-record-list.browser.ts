/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-authorization-record-list';
import type { SvcQuery, SvcRespData } from './fetch-authorization-record-list.type';

const executor = getExecutor(webRequest);

/**
 *
 * @param svcQuery
 * @returns
 */
export function fetchAuthorizationRecordList(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
