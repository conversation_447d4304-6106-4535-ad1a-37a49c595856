/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-24
 *
 * @packageDocumentation
 */
import type {
  AuthorizationRecordJSON,
  BackendAuthorizationRecord,
} from '@manyun/auth-hub.model.authorization-record';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  userId: number;
  roleCode?: string;
};

export type SvcRespData = {
  data: AuthorizationRecordJSON[];
  total: number;
};

export type RequestRespData = {
  data: BackendAuthorizationRecord[] | null;
  total: number;
} | null;

export type ApiQ = SvcQuery;

export type ApiR = ListResponse<AuthorizationRecordJSON[] | null>;
