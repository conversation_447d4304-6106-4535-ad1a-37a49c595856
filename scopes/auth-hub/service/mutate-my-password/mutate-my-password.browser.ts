/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './mutate-my-password';
import type { SvcQuery, SvcRespData } from './mutate-my-password.type';

const executor = getExecutor(webRequest);

/**
 * @param variant
 * @returns
 */
export function mutateMyPassword(variant: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(variant);
}
