/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-13
 *
 * @packageDocumentation
 */
import { Encrypt, generateEncryptPassword } from '@manyun/auth-hub.util.encrypt-password';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './mutate-my-password.type';

const endpoint = '/pm/user/resetPassWord';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1944)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({
    newPassword,
    originalPassword,
  }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    // In-case the `env.RSA_ENCRYPTION_PUBLIC_KEY` changes
    const encryptPassword = generateEncryptPassword(new Encrypt(env.RSA_ENCRYPTION_PUBLIC_KEY!));
    const params: ApiQ = {
      passWord: encryptPassword(newPassword, 'SHA256_RSA'),
      oldPassWord: encryptPassword(originalPassword, 'SHA256_RSA'),
    };

    const { error, data, ...rest } = await request.tryPost<boolean, ApiQ>(endpoint, params);

    return { error, data, ...rest };
  };
}
