/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-13
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { mutateMyPassword as webService } from './mutate-my-password.browser';
import { mutateMyPassword as nodeService } from './mutate-my-password.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve success response', async () => {
  const { error, data } = await webService({
    originalPassword: '6b86b273ff34fce19d6b804eff5a3f5747ada4eaa22f1d49c01e52ddb7875b4b',
    newPassword: 'ef797c8118f02dfb649607dd5d3f8c7623048c9c063d532cc95c5ed7a898a64f',
  });

  expect(error).toBe(undefined);
  expect(data).toBeTruthy();
});

test('[web] should resolve error response', async () => {
  const { error, data } = await webService({
    originalPassword: '1',
    newPassword: '2',
  });
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve success response', async () => {
  // const { error, data } = await nodeService('success');
  // expect(error).toBe(undefined);
  // expect(data.data).toHaveProperty('length');
  // expect(typeof data.total).toBe('number');
});

test('[node] should resolve error response', async () => {
  // const { error, data } = await nodeService('error');
  // expect(typeof error!.code).toBe('string');
  // expect(typeof error!.message).toBe('string');
  // expect(data.data).toHaveProperty('length');
  // expect(typeof data.total).toBe('number');
});
