---
description: 'A createUsers HTTP API service.'
labels: ['service', 'http', 'label3']
---

## 概述

TODO

## 使用

### Browser

```ts
import { createUsers } from '@auth-hub/service.create-users';

const { error, data } = await createUsers({
      passwordType: 'DEFAULT',
    password: '03ac674216f3e15c761ee1a5e255f067953623c8b388b4459e13f978d7c846f4',
    userInfos: [
      {
        company: '1234',
        department: '123',
        departmentId: '123',
        supervisorUid: 1,
        subSupervisorUid: 2,
        email: '<EMAIL>',
        login: 'clf',
        mobileNumber: '18042436344',
        title: '...',
        gender: 'MALE',
        name: 'C<PERSON>',
        type: 'CUSTOMER',
      },
    ],
});
```
