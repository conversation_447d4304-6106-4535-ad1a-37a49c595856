/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './create-users.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { createUsers as webService } from './create-users.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    passwordType: 'DEFAULT',
    password: '03ac674216f3e15c761ee1a5e255f067953623c8b388b4459e13f978d7c846f4',
    userInfos: [
      {
        company: '1234',
        department: '123',
        departmentId: '123',
        supervisorUid: 1,
        subSupervisorUid: 2,
        email: '<EMAIL>',
        login: 'clf',
        mobileNumber: '18042436344',
        title: '...',
        gender: 'MALE',
        name: 'CLF',
        type: 'CUSTOMER',
      },
    ],
  });

  expect(error).toBe(undefined);
  expect(typeof data).toBe('boolean');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({
    passwordType: 'CUSTOM',
    password: '03ac674216f3e15c761ee1a5e255f067953623c8b388b4459e13f978d7c846f4',
    userInfos: [
      {
        company: '1234',
        department: '123',
        departmentId: '123',
        supervisorUid: 1,
        subSupervisorUid: 2,
        email: '<EMAIL>',
        login: 'clf',
        mobileNumber: '18042436344',
        title: '...',
        gender: 'MALE',
        name: 'CLF',
        type: 'CUSTOMER',
      },
    ],
  });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
