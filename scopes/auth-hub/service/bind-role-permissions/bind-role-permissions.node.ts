/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-9
 *
 * @packageDocumentation
 */
import { nodeRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './bind-role-permissions';
import type { SvcQuery, SvcRespData } from './bind-role-permissions.type';

const executor = getExecutor(nodeRequest);

/**
 * @param svcQuery
 * @returns
 */
export function bindRolePermissions(
  svcQuery: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(svcQuery);
}
