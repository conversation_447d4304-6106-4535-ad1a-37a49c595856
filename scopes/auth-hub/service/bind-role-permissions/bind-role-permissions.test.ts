/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-9
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { bindRolePermissions as webService } from './bind-role-permissions.browser';
import { bindRolePermissions as nodeService } from './bind-role-permissions.node';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('[web] should resolve error response', async () => {
  const { error } = await webService({ roleIds: [Number.NaN], permissionIds: [] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});

test('[node] should resolve error response', async () => {
  const { error } = await nodeService({ roleIds: [Number.NaN], permissionIds: [] });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
