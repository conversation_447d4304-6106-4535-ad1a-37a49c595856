/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-20
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './sync-users';
import type { SvcRespData } from './sync-users.type';

const executor = getExecutor(webRequest);

/**
 *
 * @returns
 */
export function syncUsers(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
