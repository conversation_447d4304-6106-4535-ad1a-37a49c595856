/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-20
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcRespData } from './sync-users.type';

const endpoint = '/pm/user/sync';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/9488)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = {
      channel: 'MDM',
      companyNo: '21ACDFC959-IDC',
    };

    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
