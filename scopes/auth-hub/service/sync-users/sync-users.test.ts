/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-20
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { syncUsers } from './sync-users.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await syncUsers();

  expect(error).toBe(undefined);
  expect(data).toBe(null);
});
