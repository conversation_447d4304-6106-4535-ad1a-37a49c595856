/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './delete-user';
import type { SvcQuery, SvcRespData } from './delete-user.type';

/**
 * 用户管理 删除用户
 *
 * @see [删除用户](http://172.16.0.17:13000/project/78/interface/api/1912)
 *
 * @param query Service query object
 * @returns
 */
export async function deleteUser(query: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return await webRequest.tryPost<SvcRespData, string>(endpoint, `userId=${query}`);
}
