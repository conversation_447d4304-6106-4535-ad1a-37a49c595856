/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './delete-user.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { deleteUser as webService } from './delete-user.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService(1);

  expect(error).toBe(undefined);
  expect(typeof data).toBe('boolean');
});

test('should resolve error response', async () => {
  const { error, data } = await webService(2);
  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
});
