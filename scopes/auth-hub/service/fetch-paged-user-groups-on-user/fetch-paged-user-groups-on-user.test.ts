/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './fetch-paged-user-groups-on-user.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { fetchPagedUserGroupsOnUser as webService } from './fetch-paged-user-groups-on-user.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  const { error, data } = await webService({
    pageNo: 1,
    pageSize: 10,
    userId: 1,
  });

  expect(error).toBe(undefined);
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});

test('should resolve error response', async () => {
  const { error, data } = await webService({ pageNo: 1, pageSize: 10, userId: 2 });

  expect(typeof error!.code).toBe('string');
  expect(typeof error!.message).toBe('string');
  expect(data.data).toHaveProperty('length');
  expect(typeof data.total).toBe('number');
});
