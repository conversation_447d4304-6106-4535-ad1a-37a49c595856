/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-paged-user-groups-on-user';
import type {
  ApiQ,
  RequestRespData,
  SvcQuery,
  SvcRespData,
} from './fetch-paged-user-groups-on-user.type';

/**
 * 用户管理 分页查询用户关联的用户组
 * @deprecated
 * @see [分页查询用户关联的用户组](http://172.16.0.17:13000/project/78/interface/api/1920)
 *
 * @param variant
 * @returns
 */
export async function fetchPagedUserGroupsOnUser({
  groupName,
  pageNo,
  pageSize,
  userId,
}: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  const params: ApiQ = { groupName, pageNo, pageSize, userId };

  const {
    error,
    data: { data, total },
    ...rest
  } = await webRequest.tryGet<RequestRespData, ApiQ>(endpoint, { params });

  if (error || data === null) {
    return {
      ...rest,
      error,
      data: {
        data: [],
        total,
      },
    };
  }

  return { error, data: { data, total }, ...rest };
}
