/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  groupName?: string;
  pageNo: number;
  pageSize: number;
  userId?: number;
};

export type GroupModel = {
  id: number;
  groupName: string;
  groupCode: string;
  gmtCreate: number;
  gmtModified: number;
  creatorId: number;
  creatorName: string;
  modifierId: number;
  modifierName: string;
  remarks: string;
};

export type SvcRespData = {
  data: GroupModel[];
  total: number;
};

export type RequestRespData = {
  data: GroupModel[] | null;
  total: number;
};

export type ApiQ = {
  groupName?: string;
  pageNo: number;
  pageSize: number;
  userId?: number;
};

export type ApiR = ListResponse<RequestRespData['data']>;
