/**
 * <AUTHOR> <<EMAIL>>
 * @since 2023-5-9
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, SvcQuery, SvcRespData } from './add-role-permissions.type';

const endpoint = '/pm/role/bindInsertPermissions';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/23214)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { ...svcQuery };
    return await request.tryPost<SvcRespData, ApiQ>(endpoint, params);
  };
}
