/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-13
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-my-permissions';
import type { SvcRespData } from './fetch-my-permissions.type';

const executor = getExecutor(webRequest);

/**
 * @returns
 */
export function fetchMyPermissions(): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor();
}
