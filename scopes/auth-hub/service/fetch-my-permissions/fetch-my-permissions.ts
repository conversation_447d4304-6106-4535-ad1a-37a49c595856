/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-1-13
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { RequestRespData, SvcRespData } from './fetch-my-permissions.type';

const endpoint = '/pm/permission/currentPermissions';

/**
 * @see [Doc](YAPI http://172.16.0.17:13000/project/78/interface/api/1608)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { error, data, ...rest } = await request.tryGet<RequestRespData, undefined>(endpoint);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: [],
      };
    }

    return { error, data: data.data, ...rest };
  };
}
