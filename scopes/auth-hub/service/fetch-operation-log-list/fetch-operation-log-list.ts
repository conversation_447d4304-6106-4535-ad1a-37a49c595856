/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-12-21
 *
 * @packageDocumentation
 */
import { OperationLog } from '@manyun/auth-hub.model.operation-log';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-operation-log-list.type';

const endpoint = '/dcom/modify/query/list';

/**
 * @see [Doc](http://172.16.0.17:13000/project/172/interface/api/19284)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { startedAt, endedAt, page, ...others } = svcQuery;

    const params: ApiQ = {
      ...others,
      operatorTimeStart: startedAt,
      operatorTimeEnd: endedAt,
      pageNum: page,
    };
    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(item => OperationLog.fromApiObject(item).toJSON()),
        total: data.total,
      },
      ...rest,
    };
  };
}
