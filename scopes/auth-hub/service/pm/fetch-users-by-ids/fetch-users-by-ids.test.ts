/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-19
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { fetchUsersByIds as webService } from './fetch-users-by-ids.browser';
import { registerWebMocks } from './fetch-users-by-ids.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('should resolve users: 1, 2, 3', async () => {
  const { data, error } = await webService({ userIds: [1, 2, 3] });
  expect(data.data.length).toBe(3);
  expect(error).toBe(undefined);
});
