/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-19
 *
 * @packageDocumentation
 */
import { User } from '@manyun/auth-hub.model.user';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-users-by-ids';
import type { ApiQ, RequestRD, ServiceQ, ServiceRD } from './fetch-users-by-ids.type';

/**
 * @deprecated Use `@manyun/auth-hub.service.fetch-users-by-ids` instead
 *
 * 根据用户名或邮箱或工号查询用户信息列表
 *
 * @see [Doc](https://manyun.yuque.com/ewe5b3/pkinbk/eto76h)
 *
 * @param query POST Reqeust Body
 * @returns
 */
export async function fetchUsersByIds(query: ServiceQ): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = query;
  const { error, data, ...rest } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);
  if (error || data === null || data.data === null) {
    return {
      error,
      ...rest,
      data: {
        total: 0,
        data: [],
      },
    };
  }
  return { error, ...rest, data: { total: data.total, data: data.data.map(User.fromApiObject) } };
}
