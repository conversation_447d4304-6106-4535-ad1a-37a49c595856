/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-19
 *
 * @packageDocumentation
 */
import type { BackendUser } from '@manyun/auth-hub.model.user';
import { User } from '@manyun/auth-hub.model.user';
import type { Response } from '@manyun/dcbrain-types/lib/backend/common';

export type ServiceQ = {
  userIds: number[];
};

export type RequestRD = {
  data: BackendUser[] | null;
  total: number;
} | null;

export type ServiceRD = {
  data: User[];
  total: number;
};

export type ApiQ = {
  userIds: number[];
};

export type ApiR = Response<RequestRD>;
