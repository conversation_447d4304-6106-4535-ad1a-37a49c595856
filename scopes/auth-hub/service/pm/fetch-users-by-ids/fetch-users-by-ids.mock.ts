/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-19
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-users-by-ids';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    // POST 请求需要 parse config.data
    const { userIds } = JSON.parse(config.data);
    return [
      200,
      {
        success: true,
        errCode: null,
        errMessage: null,
        data: Array(userIds.length)
          .fill(null)
          .map((_, idx) => ({
            id: idx + 1,
            userName: 'userName' + idx,
            loginName: 'loginName' + idx,
            mobile: 'mobile' + idx,
            email: 'email' + idx,
          })),
        total: userIds.length,
      },
    ];
  });
}
