/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { BackendUserGroup } from '@manyun/auth-hub.model.user-group';
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-user-groups';
import type { ApiQ, ApiR } from './fetch-user-groups.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    const { userId } = JSON.parse(config.data) as ApiQ;
    const data: BackendUserGroup[] = Array(3)
      .fill(null)
      .map((_, idx) => ({
        id: idx,
        groupCode: `admin_${idx}`,
        groupName: `name_${idx}`,
        resourceCodes: ['EC06.A', 'EC07'],
        roleCodes: ['admin', 'role'],
      }));
    return [
      200,
      {
        success: true,
        data: data,
        total: 100,
        errCode: null,
        errMessage: null,
      } as ApiR,
    ];
  });
}
