/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { UserGroup } from '@manyun/auth-hub.model.user-group';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-user-groups';
import type { ApiQ, RequestRD, ServiceQ, ServiceRD } from './fetch-user-groups.type';

/**
 * 用户可申请用户组列表查询
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/1384)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchUserGroups(
  serviceQ: ServiceQ
): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = { userId: serviceQ.userId };

  const {
    error,
    data: { data, total },
    ...rest
  } = await webRequest.tryPost<RequestRD, ApiQ>(endpoint, apiQ);

  if (error || data == null) {
    return { error, ...rest, data: { data: [], total: 0 } };
  }

  return {
    error,
    ...rest,
    data: {
      data: data.map(backendUserGroup => UserGroup.fromApiObject(backendUserGroup)),
      total: total,
    },
  };
}
