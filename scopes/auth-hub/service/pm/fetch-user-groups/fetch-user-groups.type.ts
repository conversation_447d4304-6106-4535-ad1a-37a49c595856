/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { BackendUserGroup, UserGroup } from '@manyun/auth-hub.model.user-group';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ServiceQ = {
  userId?: number;
};

export type ServiceRD = {
  data: UserGroup[] | null;
  total: number;
};

export type ApiQ = {
  userId?: number;
};

export type RequestRD = {
  data: BackendUserGroup[] | null;
  total: number;
};

export type ApiR = ListResponse<BackendUserGroup[]>;
