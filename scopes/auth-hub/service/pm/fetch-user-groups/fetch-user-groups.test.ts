/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { getMock, useRemoteMock } from '@manyun/service.request';

import { fetchUserGroups as webService } from './fetch-user-groups.browser';
import { registerWebMocks } from './fetch-user-groups.mock';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));

test('should resolve with the userGroups data', async () => {
  const { data, error } = await webService({});
  expect(data).toHaveProperty('total');
  expect(data.data).toHaveLength(3);
  expect(error).toBe(undefined);
});

test('should resolve with the userGroups data by search userId = 51', async () => {
  const { data, error } = await webService({ userId: 51 });
  expect(data).toHaveProperty('total');
  expect(data.data).toHaveLength(0);
  expect(error).toBe(undefined);
});
