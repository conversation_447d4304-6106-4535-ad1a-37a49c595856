/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { BackendRole } from '@manyun/auth-hub.model.role';
import { getMock } from '@manyun/service.request';

import { rolesListEndPoint, rolesPageEndPoint } from './fetch-roles';
import type { ApiR } from './fetch-roles.type';

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock
    .onGet(rolesPageEndPoint)
    .reply(config => {
      const { pageSize = 10 } = config.params;
      const data = Array(pageSize)
        .fill(null)
        .map((_, index) => ({
          id: index,
          roleName: `角色名_${index}`,
          roleCode: `Role_${index}`,
        }));
      return [
        200,
        {
          success: true,
          data: data,
          total: 200,
          errCode: null,
          errMessage: null,
        } as ApiR,
      ];
    })
    .onGet(rolesListEndPoint)
    .reply(() => {
      const data: BackendRole[] = Array(15)
        .fill(null)
        .map((_, index) => ({
          id: index,
          roleName: `角色名_${index}`,
          roleCode: `role-code-${index}`,
        }));
      const resp: ApiR = {
        success: true,
        data: data,
        total: 2,
        errCode: null,
        errMessage: null,
      };

      return [200, resp];
    });
}
