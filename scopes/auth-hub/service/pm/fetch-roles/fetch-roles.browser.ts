/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { Role } from '@manyun/auth-hub.model.role';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { rolesListEndPoint, rolesPageEndPoint } from './fetch-roles';
import type { ListApiQ, PageApiQ, RequestRD, ServiceQ, ServiceRD } from './fetch-roles.type';

/**
 * 请求获取用户角色列表(分页、全部数据)
 *
 * @see [Doc](http://172.16.0.17:13000/project/19/interface/api/752)
 * @see [Doc](http://172.16.0.17:13000/project/19/interface/api/749)
 *
 * @param data POST Reqeust Body
 * @returns
 */
/** todo 去掉分页 */
export async function fetchRoles(serviceQ: ServiceQ): Promise<EnhancedAxiosResponse<ServiceRD>> {
  if (serviceQ.page != undefined && serviceQ.pageSize != undefined) {
    const apiQ: PageApiQ = {
      pageNo: serviceQ.page,
      pageSize: serviceQ.pageSize,
      searchName: serviceQ.name,
      searchType: serviceQ.type,
    };

    const { error, data, ...rest } = await webRequest.tryGet<RequestRD, PageApiQ>(
      rolesPageEndPoint,
      {
        params: apiQ,
      }
    );

    if (error || data === null || data.data === null) {
      return { error, ...rest, data: { data: [], total: 0 } };
    }

    return {
      error,
      ...rest,
      data: { data: data.data.map(Role.fromApiObject), total: data.total },
    };
  } else {
    const apiQ: ListApiQ = {
      roleName: serviceQ.name,
    };

    const { error, data, ...rest } = await webRequest.tryGet<RequestRD, ListApiQ>(
      rolesListEndPoint,
      {
        params: apiQ,
      }
    );

    if (error || data === null || data.data === null) {
      return { error, ...rest, data: { data: [], total: 0 } };
    }

    return {
      error,
      ...rest,
      data: { data: data.data.map(Role.fromApiObject), total: data.total },
    };
  }
}
