/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchRoles as webService } from './fetch-roles.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve role`s page ', async () => {
  const { data, error } = await webService({ page: 1, pageSize: 10 });
  expect(data.data).toHaveLength(3);
  expect(error).toBe(undefined);
});

test('should resolve role`s page empty', async () => {
  const { data, error } = await webService({ page: 0, pageSize: 10 });
  expect(data.data).toHaveLength(0);
  expect(error).toBe(undefined);
});

test('should resolve role`s list', async () => {
  const { data, error } = await webService({});
});
