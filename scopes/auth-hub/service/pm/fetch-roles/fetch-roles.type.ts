import { BackendRole, Role } from '@manyun/auth-hub.model.role';

/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ServiceQ = {
  name?: string;
  type?: string;
  page?: number;
  pageSize?: number;
};

export type ServiceRD = {
  data: Role[];
  total: number;
};

export type PageApiQ = {
  pageNo: number;
  pageSize: number;
  searchName?: string /**搜索名称 */;
  searchType?: string /**搜索类型  */;
};

export type ListApiQ = {
  roleName?: string /**角色名称 */;
};

export type RequestRD =
  | (Omit<ServiceRD, 'data'> & {
      data: BackendRole[] | null;
    })
  | null;

export type ApiR = ListResponse<BackendRole[] | null>;
