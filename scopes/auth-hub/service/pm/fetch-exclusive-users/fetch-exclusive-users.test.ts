/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-5
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { fetchExclusiveUsers as webService } from './fetch-exclusive-users.browser';
import { registerWebMocks } from './fetch-exclusive-users.mock';

// mocks for tests should resolve immediately.
registerWebMocks(getMock('web'));

test('should resolve with the 1st page data', async () => {
  const { data, error } = await webService({ withDeleted: false, page: 1, pageSize: 10 });

  expect(data).toHaveProperty('total');
  expect(data.data).toHaveLength(10);
  expect(error).toBe(undefined);
});
