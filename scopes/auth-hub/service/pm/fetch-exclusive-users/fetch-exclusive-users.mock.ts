/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-5
 *
 * @packageDocumentation
 */
import { getMock } from '@manyun/service.request';

import { endpoint } from './fetch-exclusive-users';
import type { ApiQ, ApiR } from './fetch-exclusive-users.type';

function getAvailableId(existingIds: number[], startId: number) {
  let id: number = startId;

  while (existingIds.includes(id)) {
    id++;
  }

  return id;
}

export function registerWebMocks(webMock: ReturnType<typeof getMock>) {
  webMock.onPost(endpoint).reply(config => {
    const {
      userName,
      pageNum = 1,
      pageSize = 10,
      userIdList,
      roleIdList = [1],
      idcTagList = ['sxdtyg'],
      notAllowUserSet,
    } = JSON.parse(config.data) as ApiQ;
    let possibleStartId: number = (pageNum - 1) * pageSize + 1;
    const data = Array(pageSize)
      .fill(null)
      .map((_, idx) => {
        const id = userIdList
          ? userIdList[idx]
          : getAvailableId(notAllowUserSet || [], possibleStartId + idx);
        possibleStartId = id;

        return {
          id,
          loginName: 'login_name',
          userName: (userName || 'Theo') + id,
          mobile: '13333333333',
          email: '<EMAIL>',
          resourceList: (idcTagList as any[]).map(idc => ({
            id: idc,
            resourceCode: idc,
          })),
          roleInfoList: (roleIdList as any[]).map(roleId => ({
            id: roleId,
            roleName: '角色名-' + roleId,
            roleCode: roleId,
          })),
        };
      });

    return [
      200,
      {
        success: true,
        data: data,
        total: 100,
        errCode: null,
        errMessage: null,
      } as ApiR,
    ];
  });
}
