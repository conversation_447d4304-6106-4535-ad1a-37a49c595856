/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-5
 *
 * @packageDocumentation
 */
import type { BackendRole, Role } from '@manyun/auth-hub.model.role';
import type { BackendUser, UserJSON } from '@manyun/auth-hub.model.user';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type ServiceQ = {
  withDeleted: boolean;
  name?: string;
  ids?: number[];
  idcs?: string[];
  blockGuids?: string[];
  roleIds?: number[];
  roleCodes?: string[];
  exclusiveIds?: number[];
  page: number;
  pageSize: number;
};

type BackendAuthUser = BackendUser & {
  resourceList: Array<{
    id: number;
    resourceCode: string;
  }> | null;
  roleInfoList: BackendRole[] | null;
};

export type ReqeustR = { data: BackendAuthUser[]; total: number } | null;

export type ServiceRD = {
  data: Array<
    UserJSON & {
      resources: Array<{
        id: number;
        code: string;
      }>;
      roles: Role[];
    }
  >;
  total: number;
};

export type ApiQ = {
  /** 是否包含已删除用户 */
  containDelete?: boolean | null;
  userName?: string | null;
  userIdList?: number[] | null;
  idcTagList?: string[] | null;
  blockGuidList?: string[] | null;
  roleIdList?: number[] | null;
  roleCodeList?: string[];

  /** 需要排除掉的用户 ID 集合 */
  notAllowUserSet?: number[] | null;
  pageNum: number;
  pageSize: number;
};

export type ApiR = ListResponse<BackendAuthUser[] | null>;
