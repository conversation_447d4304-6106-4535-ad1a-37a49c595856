/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-5
 *
 * @packageDocumentation
 */
import { Role } from '@manyun/auth-hub.model.role';
import { User } from '@manyun/auth-hub.model.user';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './fetch-exclusive-users';
import type { ApiQ, ReqeustR, ServiceQ, ServiceRD } from './fetch-exclusive-users.type';

/**
 * 分页查询用户信息（包含关联的角色、资源），并且可以剔除某些用户
 *
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/2000)
 *
 * @param data POST Reqeust Body
 * @returns
 */
export async function fetchExclusiveUsers(
  data: ServiceQ
): Promise<EnhancedAxiosResponse<ServiceRD>> {
  const apiQ: ApiQ = {
    containDelete: data.withDeleted,
    userName: data.name,
    userIdList: data.ids,
    idcTagList: data.idcs,
    blockGuidList: data.blockGuids,
    roleIdList: data.roleIds,
    roleCodeList: data.roleCodes,

    notAllowUserSet: data.exclusiveIds,
    pageNum: data.page,
    pageSize: data.pageSize,
  };

  const { error, ...rest } = await webRequest.tryPost<ReqeustR, ApiQ>(endpoint, apiQ);

  if (error) {
    return { error, ...rest, data: { data: [], total: 0 } };
  }

  return {
    error,
    ...rest,
    data: {
      data: rest.data!.data.map(backendAuthUser => {
        const user = User.fromApiObject(backendAuthUser);
        const resources =
          backendAuthUser.resourceList?.map(({ id, resourceCode }) => ({
            id,
            code: resourceCode,
          })) || [];
        const roles =
          backendAuthUser.roleInfoList?.map(backendRole => Role.fromApiObject(backendRole)) || [];

        return {
          ...user.toJSON(),
          resources,
          roles,
        };
      }),
      total: rest.data!.total,
    },
  };
}
