/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-26
 *
 * @packageDocumentation
 */
import { useRemoteMock } from '@manyun/service.request';

import { fetchUserEmail as webService } from './fetch-user-email.browser';

let webMockOff: () => void;
let nodeMockOff: () => void;
beforeAll(() => {
  webMockOff = useRemoteMock('web');
  nodeMockOff = useRemoteMock('node');
});
afterAll(() => {
  webMockOff();
  nodeMockOff();
});

test('should resolve success response', async () => {
  const { error } = await webService({ userId: 2 });
  expect(error).toBe(undefined);
});
