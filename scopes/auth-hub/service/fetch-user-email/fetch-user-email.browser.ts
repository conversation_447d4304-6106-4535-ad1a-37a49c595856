/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-26
 *
 * @packageDocumentation
 */
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { getExecutor } from './fetch-user-email';
import type { SvcQuery, SvcRespData } from './fetch-user-email.type';

const executor = getExecutor(webRequest);

/**
 * @param params
 * @returns
 */
export function fetchUserEmail(params: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> {
  return executor(params);
}
