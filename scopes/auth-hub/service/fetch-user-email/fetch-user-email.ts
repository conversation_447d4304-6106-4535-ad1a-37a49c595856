/**
 * <AUTHOR> <<EMAIL>>
 * @since 2022-7-26
 *
 * @packageDocumentation
 */
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-user-email.type';

const endpoint = '/pm/user/email';

/**
 * @see [Doc](http://172.16.0.17:13000/project/78/interface/api/18735)
 *
 * @param request
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async ({ userId }: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const params: ApiQ = { userId };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, params);

    if (error || data === null) {
      return {
        ...rest,
        error,
        data: null,
      };
    }

    return { error, data, ...rest };
  };
}
