/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { Encrypt, generateEncryptPassword } from '@manyun/auth-hub.util.encrypt-password';
import { env } from '@manyun/dc-brain.app-env.react-app-envs';
import { webRequest } from '@manyun/service.request';
import type { EnhancedAxiosResponse } from '@manyun/service.request';

import { endpoint } from './update-user-login-settings';
import type { ApiQ, SvcQuery, SvcRespData } from './update-user-login-settings.type';

/**
 * 用户管理 修改登录设置
 *
 * @see [修改登录设置](http://172.16.0.17:13000/project/78/interface/api/1976)
 *
 * @param query
 * @returns
 */
export async function updateUserLoginSettings(
  query: SvcQuery
): Promise<EnhancedAxiosResponse<SvcRespData>> {
  let password: string | undefined;
  if (query.passwordType !== 'UNCHANGED') {
    if (query.password) {
      // In-case the `env.RSA_ENCRYPTION_PUBLIC_KEY` changes
      const encryptPassword = generateEncryptPassword(new Encrypt(env.RSA_ENCRYPTION_PUBLIC_KEY!));
      password = encryptPassword(query.password, 'SHA256_RSA');
    } else {
      throw new Error('"password" is required while "passwordType" is not "UNCHANGED"!');
    }
  }
  const apiQ: ApiQ = {
    ...query,
    password,
  };

  return await webRequest.tryPost<SvcRespData, ApiQ>(endpoint, apiQ);
}
