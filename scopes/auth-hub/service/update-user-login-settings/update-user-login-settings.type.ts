/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { WriteResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  multiFactorAuthentication?: boolean;
  password?: string;
  passwordType?: string;
  resetPassword?: boolean;
  userId: number;
  whetherEnable?: boolean;
};

export type SvcRespData = boolean;

export type ApiQ = {
  multiFactorAuthentication?: boolean;
  password?: string;
  passwordType?: string;
  resetPassword?: boolean;
  userId: number;
  whetherEnable?: boolean;
};

export type ApiR = WriteResponse;
