/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
// 使用以下代码注册本地 Mocks
// import { getMock } from '@manyun/service.request';
// import { registerWebMocks } from './update-user-login-settings.mock';
// mocks for tests should resolve immediately.
// registerWebMocks(getMock('web'));
// 使用以下代码注册远程 Mocks
import { useRemoteMock } from '@manyun/service.request';

import { updateUserLoginSettings as webService } from './update-user-login-settings.browser';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web');
});
afterAll(() => {
  mockOff();
});

test('should resolve success response', async () => {
  // const { error, data } = await webService({
  //   userId: 1,
  //   whetherEnable: true,
  // });
  // expect(error).toBe(undefined);
  // expect(data).toBe(true);
});

test('should resolve error response', async () => {
  // const { error, data } = await webService({ userId: 1, whetherEnable: false });
  // expect(typeof error!.code).toBe('string');
  // expect(typeof error!.message).toBe('string');
});
