/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import { Role } from '@manyun/auth-hub.model.role';
import { User } from '@manyun/auth-hub.model.user';
import type { EnhancedAxiosResponse, NodeRequest, WebRequest } from '@manyun/service.request';

import type { ApiQ, RequestRespData, SvcQuery, SvcRespData } from './fetch-paged-users.type';

const endpoint = '/pm/user/page';

/**
 * 用户管理 分页查询用户信息
 *
 * @see [分页查询用户信息](http://172.16.0.17:13000/project/78/interface/api/2512)
 *
 * @param variant
 * @returns
 */
export function getExecutor<T extends WebRequest | NodeRequest>(request: T) {
  return async (svcQuery: SvcQuery): Promise<EnhancedAxiosResponse<SvcRespData>> => {
    const { company, creatorName, email, enable, loginName, mobile, userName, ...others } =
      svcQuery;
    const apiQ: ApiQ = {
      company: company?.trim(),
      creatorName: creatorName?.trim(),
      email: email?.trim(),
      loginName: loginName?.trim(),
      mobile: mobile?.trim(),
      userName: userName?.trim(),
      ...others,
      enable: enable !== undefined ? enable === 'in-service' : undefined,
    };

    const { error, data, ...rest } = await request.tryPost<RequestRespData, ApiQ>(endpoint, apiQ);

    if (error || data === null || data.data === null) {
      return {
        ...rest,
        error,
        data: {
          data: [],
          total: 0,
        },
      };
    }

    return {
      error,
      data: {
        data: data.data.map(backendUser => {
          const roles =
            backendUser.roleInfoList?.map(backendRole => Role.fromApiObject(backendRole)) || [];
          return {
            ...User.fromApiObject(backendUser).toJSON(),
            resourceCodes: backendUser.resourceCodes,
            roles,
          };
        }),
        total: data.total,
      },
      ...rest,
    };
  };
}
