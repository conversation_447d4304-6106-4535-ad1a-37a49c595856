/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-12-14
 *
 * @packageDocumentation
 */
import type { BackendRole, Role } from '@manyun/auth-hub.model.role';
import type {
  BackendUser,
  UserGender,
  UserJSON,
  UserState,
  UserType,
} from '@manyun/auth-hub.model.user';
import type { ListResponse } from '@manyun/dcbrain-types/lib/backend/common';

export type SvcQuery = {
  company?: string;
  creatorName?: string;
  email?: string;
  enable?: UserState;
  loginName?: string;
  mobile?: string;
  sexType?: UserGender;
  userId?: number;
  userIds?: number[];
  userName?: string;
  userType?: UserType;
  key?: string;
  needResource?: boolean;
  resourceCode?: string;
  needRole?: boolean;
  pageNum: number;
  pageSize: number;
};

export type CustomizedUser = UserJSON & {
  resourceCodes: string[] | null;
  roles: Role[];
};

export type SvcRespData = {
  data: CustomizedUser[];
  total: number;
};

type ApiRD =
  | (BackendUser & {
      resourceCodes: string[];
      roleInfoList: BackendRole[] | null;
    })[]
  | null;

export type RequestRespData = {
  data: ApiRD;
  total: number;
} | null;

export type ApiQ = {
  company?: string;
  creatorName?: string;
  email?: string;
  enable?: boolean;
  loginName?: string;
  mobile?: string;
  sexType?: string;
  userId?: number;
  userIds?: number[];
  userName?: string;
  userType?: string;
  needRole?: boolean;
  pageNum: number;
  pageSize: number;
};

export type ApiR = ListResponse<ApiRD>;
