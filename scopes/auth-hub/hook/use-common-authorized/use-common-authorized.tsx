import React from 'react';

export type UseCommonAuthorizedProps = {
  userId: number | null;
  permissions: string[];
  checkByUserId?: number;
  checkByCode?: string;
};

/**
 * @deprecated Use `@manyun/auth-hub.hook.gql.use-authorized` instead
 */
export function useCommonAuthorized(props: UseCommonAuthorizedProps) {
  const { userId, permissions, checkByUserId, checkByCode } = props;
  const checkUserId = React.useCallback(
    (_checkByUserId: number) => _checkByUserId === userId,
    [userId]
  );
  const checkCode = React.useCallback(
    (_checkByCode: string) => permissions.includes(_checkByCode),
    [permissions]
  );

  const authorized = React.useMemo(() => {
    if (checkByUserId === undefined && checkByCode === undefined) {
      return undefined;
    }
    const userIdAuthorized = checkByUserId !== undefined ? checkUserId(checkByUserId) : true;
    const codeAuthorized = checkByCode !== undefined ? checkCode(checkByCode) : true;
    return userIdAuthorized && codeAuthorized;
  }, [checkByCode, checkByUserId, checkCode, checkUserId]);

  return [authorized, { checkUserId, checkCode }] as const;
}
