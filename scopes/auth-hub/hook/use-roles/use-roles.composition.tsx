import React from 'react';

// @ts-ignore
import { registerWebMocks } from '@manyun/auth-hub.service.pm.fetch-roles/dist/fetch-roles.mock';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { getMock } from '@manyun/service.request';

import { useRoles } from './use-roles';

const RolesDemo = () => {
  const [{ loading, codes, entities }, getRoles] = useRoles();

  React.useEffect(() => {
    registerWebMocks(getMock('web', { delayResponse: 777 }));
    getRoles({ page: 1, pageSize: 10 });
  }, []);

  if (loading) {
    return <span>Loading...</span>;
  }

  if (!codes || codes.length <= 0) {
    return <span>No roles to display</span>;
  }

  const role = entities[codes[0]];

  return (
    <>
      <h1>{role.name}</h1>
      <p>{role.id}</p>
      <p>{role.code}</p>
    </>
  );
};

export const BasicuseRoles = () => (
  <FakeStore>
    <RolesDemo />
  </FakeStore>
);
