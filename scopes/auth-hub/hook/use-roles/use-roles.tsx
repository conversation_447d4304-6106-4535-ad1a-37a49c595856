import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

// Note:
// DONOT REMOVE THIS IMPORT
// To fix `error TS2742: The inferred type of 'useRoles' cannot be named without a reference to '../auth-hub_state_roles@0.0.4/node_modules/@manyun/auth-hub.model.role'. This is likely not portable. A type annotation is necessary.` on tagging process
// eslint-disable-next-line @typescript-eslint/no-unused-vars
import type { Role } from '@manyun/auth-hub.model.role';
import { ServiceQ, ServiceRD } from '@manyun/auth-hub.service.pm.fetch-roles';
import { getRolesAction, selectRolesByCodes, selectRolesState } from '@manyun/auth-hub.state.roles';
import { RequestError } from '@manyun/service.request';

export type OnCompletedCallback = (error?: RequestError, data?: ServiceRD) => void;

type StaticReadPolicy = 'network-only' | 'cache-only';
type CallbackReadPolicy = (codes: string[]) => StaticReadPolicy;
export type ReadRolesPolicy = StaticReadPolicy | CallbackReadPolicy;

export type GetRolesArgs = ServiceQ & {
  policy?: ReadRolesPolicy;
};

export type UseRolesProps = {
  /** HTTP 请求完成时的回调函数 */
  onCompleted?: OnCompletedCallback;
};

const noop = () => {};

export function useRoles({ onCompleted: onCompeleted = noop }: UseRolesProps = {}) {
  const storeState = useSelector(selectRolesState);
  const rolesData = useSelector(selectRolesByCodes(storeState.codes));

  const dispatch = useDispatch();

  const getRoles = React.useCallback(
    (data?: GetRolesArgs) => {
      dispatch(getRolesAction({ ...data, callback: onCompeleted }));
    },
    // eslint-disable-next-line react-hooks/exhaustive-deps
    [dispatch]
  );
  return [{ ...storeState, data: { data: rolesData, total: storeState.total } }, getRoles] as const;
}
