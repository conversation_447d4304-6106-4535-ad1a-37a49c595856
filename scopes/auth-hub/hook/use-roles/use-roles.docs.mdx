---
description: '获取角色列表数据'
labels: ['hook', 'roles']
---

import { useRoles } from './use-roles';

## 概述

获取角色列表数据，并存储在 `hook redux` 内部。

## TODOs

[-] `/pm/role/page` API 抽成 `Service Component`

## 使用

```js
import { useRoles } from './use-roles';

const [{ loading, data }, getRoles] = useRoles();

React.useEffect(() => {
  getRoles({ page: 1, pageSize: 1 });
}, []);

if (loading) {
  return <span>Loading...</span>;
}

if (!data || data.data.length <= 0) {
  return <span>No roles to display</span>;
}

const role = data.data[0];

return (
  <>
    <h1>{role.name}</h1>
    <p>{role.id}</p>
    <p>{role.code}</p>
  </>
);
```
