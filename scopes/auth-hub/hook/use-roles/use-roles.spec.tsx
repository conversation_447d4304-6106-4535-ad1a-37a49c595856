import { act, renderHook } from '@testing-library/react-hooks';

// @ts-ignore
import { registerWebMocks } from '@manyun/auth-hub.service.pm.fetch-roles/dist/fetch-roles.mock';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroyMock, getMock } from '@manyun/service.request';

import { useRoles } from './use-roles';

const mock = getMock('web');

beforeAll(() => {
  registerWebMocks(mock);
});

afterAll(() => {
  destroyMock();
});

test('should display loading roles right by redux  and redux saga', async () => {
  const { result, waitForNextUpdate } = renderHook(() => useRoles(), {
    wrapper: FakeStore,
  });
  act(() => {
    result.current[1]({ page: 1, pageSize: 1 });
  });
  expect(result.current[0].loading).toBe(true);
  expect(result.current[0].total).toBe(0);

  await waitForNextUpdate();

  expect(result.current[0].loading).toBe(false);
  expect(result.current[0].total).toBe(200);
});
