import React from 'react';
import { useDispatch, useSelector } from 'react-redux';

import type { SvcQuery } from '@manyun/auth-hub.service.fetch-paged-users';
// import { RequestError, webRequest } from '@manyun/service.request';
import { getUsersAction, selectUsersEntities } from '@manyun/auth-hub.state.users';

export type State = { loading: boolean; total: number; ids: number[] };

// export type ServiceQ = {
//   /** 角色名称模糊查询 */
//   name?: string;
//   page?: number;
//   pageSize?: number;
// };

export type UseUsersProps = {
  fields?: SvcQuery;
};

export function useUsers() {
  const [state, setState] = React.useState<State>({ loading: false, total: 0, ids: [] });
  const dispatch = useDispatch();

  const getUsers = React.useCallback(
    ({ fields = { pageNum: 1, pageSize: 10 } }: UseUsersProps) => {
      setState(prev => ({ ...prev, loading: true }));
      dispatch(
        getUsersAction({
          fields,
          callback: result => {
            if (result) {
              setState(prev => ({
                ...prev,
                total: result.total,
                ids: result.data.map(user => user.id),
                loading: false,
              }));
            } else {
              setState(prev => ({ ...prev, loading: false }));
            }
          },
        })
      );
    },
    [dispatch]
  );
  const data = useSelector(selectUsersEntities(state.ids));

  return [{ ...state, data }, getUsers] as const;
}
