import { getMock } from '@manyun/service.request';

export function registerMocks(mock: ReturnType<typeof getMock> = getMock('web')) {
  // mock.onGet('/pm/user/userListBy<PERSON>ey').reply((config) => {
  //   const { key } = config.params;
  //   return [
  //     200,
  //     {
  //       errCode: null,
  //       errMessage: null,
  //       total: 19,
  //       data: Array(19)
  //         .fill(null)
  //         .map((_, idx) => ({
  //           id: idx + 1,
  //           deleted: false,
  //           email: null,
  //           loginName: `${key} ${idx}`,
  //           mobile: `13333333333 ${idx}`,
  //           userName: `${key} ${idx}`,
  //         })),
  //       success: true,
  //     },
  //   ];
  // });
}
