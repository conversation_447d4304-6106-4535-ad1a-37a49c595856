import React from 'react';

import { getMock } from '@manyun/service.request';

import { useUsers } from './use-users';
import { registerMocks } from './use-users.service.mock';

registerMocks(getMock('web', { delayResponse: 777 }));

export const BasicUseUsers = () => {
  const [{ loading, data }, getUsers] = useUsers();

  React.useEffect(() => {
    getUsers({ fields: { pageNum: 1, pageSize: 10 } });
  }, []);

  if (loading) {
    return <span>Loading...</span>;
  }

  if (!data || data.length <= 0) {
    return <span>No data</span>;
  }

  const user = data[0];

  return (
    <>
      <h1>{user.name}</h1>
      <p>{user.id}</p>
      <p>{user.mobileNumber}</p>
    </>
  );
};
