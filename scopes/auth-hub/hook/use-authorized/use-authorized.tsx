import { useAuthorized as useNewAuthorized } from '@manyun/iam.hook.use-authorized';

export type UseAuthorizedProps = {
  checkByUserId?: number;
  checkByCode?: string;
};

export function useAuthorized({ checkByUserId, checkByCode }: UseAuthorizedProps = {}) {
  const [{ authorized }, { checkUserId, checkCode }] = useNewAuthorized({
    userId: checkByUserId,
    code: checkByCode,
  });
  return [authorized, { checkUserId, checkCode }];
}
