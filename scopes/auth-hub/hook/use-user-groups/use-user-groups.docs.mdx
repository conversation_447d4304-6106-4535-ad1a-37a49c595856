---
description: '获取用户可申请用户组列表'
labels: ['hook', 'userGroups']
---

import { useUserGroups } from './use-user-groups';

## 概述

获取用户可申请用户组列表，并存储在 `redux` 内部。

## 使用

```js
import { useUserGroups } from './use-user-groups';

const [{ loading, data }, getUserGroups] = useUserGroups();

React.useEffect(() => {
  getUserGroups();
}, []);

if (loading) {
  return <span>Loading...</span>;
}

if (!data || data.data.length <= 0) {
  return <span>No roles to display</span>;
}

const userGroup = data.data[0];

return (
  <>
    <h1>{userGroup.name}</h1>
    <p>{userGroup.id}</p>
    <p>{userGroup.code}</p>
  </>
);
```
