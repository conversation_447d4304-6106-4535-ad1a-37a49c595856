/**
 * <AUTHOR> <<EMAIL>>
 * @since 2021-11-26
 *
 * @packageDocumentation
 */
import { useCallback } from 'react';
import { useDispatch, useSelector } from 'react-redux';

import { UserGroup } from '@manyun/auth-hub.model.user-group';
import { ServiceQ } from '@manyun/auth-hub.service.pm.fetch-user-groups';
import { fetchUserGroupsWeb } from '@manyun/auth-hub.service.pm.fetch-user-groups';
import {
  getUserGroupsAction,
  selectUserGroupsByCodes,
  selectUserGroupsState,
} from '@manyun/auth-hub.state.user-groups';

/**resolve ts error */

export type GetUserGroupsArg = ServiceQ;
export function useUserGroups() {
  const dispatch = useDispatch();

  const storeState = useSelector(selectUserGroupsState());
  const userGroupsData = useSelector(selectUserGroupsByCodes(storeState.codes));

  const getUserGroups = useCallback(
    (arg?: GetUserGroupsArg) => {
      dispatch(getUserGroupsAction({ userId: arg ? arg.userId : undefined }));
    },
    [dispatch]
  );
  return [{ ...storeState, data: userGroupsData }, getUserGroups] as const;
}
