import React, { useEffect } from 'react';

import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { useRemoteMock } from '@manyun/service.request';

import { useUserGroups } from './use-user-groups';

const UserGroupsDemo = () => {
  const [{ loading, codes, entities }, getUserGroups] = useUserGroups();

  useEffect(() => {
    const mockOff = useRemoteMock('web');
    getUserGroups();

    return () => {
      mockOff();
    };
  }, []);

  if (loading) {
    return <span>Loading...</span>;
  }

  return (
    <>
      {codes.map(code => (
        <div>{entities[code].name}</div>
      ))}
    </>
  );
};

export const BasicuseUserGroups = () => {
  return (
    <FakeStore>
      <UserGroupsDemo />
    </FakeStore>
  );
};
