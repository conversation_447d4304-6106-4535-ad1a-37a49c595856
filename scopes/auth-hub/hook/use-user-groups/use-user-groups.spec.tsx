import { act, renderHook } from '@testing-library/react-hooks';

import { registerWebMocks } from '@manyun/auth-hub.service.pm.fetch-user-groups';
import { FakeStore } from '@manyun/dc-brain.store.fake-store';
import { destroyMock, getMock, useRemoteMock } from '@manyun/service.request';

import { useUserGroups } from './use-user-groups';

let mockOff: () => void;
beforeAll(() => {
  mockOff = useRemoteMock('web', { adapter: 'http' });
});

afterAll(() => {
  mockOff();
});

it('should loading userGroups`s data right by redux  and redux saga', async () => {
  const { result, waitForNextUpdate } = renderHook(() => useUserGroups(), {
    wrapper: FakeStore,
  });
  act(() => {
    result.current[1]();
  });

  expect(result.current[0].loading).toBe(true);
  expect(result.current[0].codes).toHaveLength(0);

  /** async The most basic async utility is called waitForNextUpdate */
  await waitForNextUpdate();
  expect(result.current[0].loading).toBe(false);
  expect(result.current[0].codes).toHaveLength(3);
});
