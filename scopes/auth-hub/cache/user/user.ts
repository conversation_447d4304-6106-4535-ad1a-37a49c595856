/** REFACTORME: extract these types into a independent component */

export const USER_KEY = 'DC_BASE_USER';
export const USER_ID_KEY = 'DC_BASE_USER_ID';
export const USER_NAME_KEY = 'DC_BASE_USER_NAME';
export const USER_EXPIRED_TIME_KEY = 'DC_BASE_USER_EXPIRED_TIME';
export const USER_LOGIN_NAME_KEY = 'DC_BASE_USER_LOGIN_NAME';
export const LAST_USED_USER_LOGIN_NAME_KEY = 'DC_BASE_LAST_USED_USER_LOGIN_NAME';

export enum LoginStatus {
  LOGGED_IN = 'LOGGED_IN',
  EXPIRED = 'EXPIRED',
}

export const getUserStauts = () => {
  const userInfo = getUserInfo();
  const currentExpiredTime = Number(userInfo.expiredTime);
  if (userInfo.expiredTime === null || currentExpiredTime <= Date.now()) {
    return LoginStatus.EXPIRED;
  }
  return LoginStatus.LOGGED_IN;
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
export const setUserInfo = (user: any) => {
  window.localStorage.setItem(USER_KEY, JSON.stringify(user));
};

export type UserInfosCache = {
  userId: number | null;
  name: string | null;
  username: string | null;
  expiredTime: number | null;
  company: string | null;
};
export const getUserInfo = (): UserInfosCache => {
  const userVal = window.localStorage.getItem(USER_KEY);
  if (userVal) {
    try {
      const user = JSON.parse(userVal) as LoginUser;
      return {
        userId: user.id,
        name: user.name,
        username: user.login!,
        expiredTime: user.expiredTime,
        company: user.company ?? null,
      };
    } catch {
      return {
        userId: null,
        username: null,
        name: null,
        expiredTime: null,
        company: null,
      };
    }
  }

  const legacyUserIdCache = window.localStorage.getItem(USER_ID_KEY);
  const legacyUserExpiredTimeCache = window.localStorage.getItem(USER_EXPIRED_TIME_KEY);

  return {
    userId: legacyUserIdCache !== null ? Number(legacyUserIdCache) : null,
    username: window.localStorage.getItem(USER_LOGIN_NAME_KEY),
    name: window.localStorage.getItem(USER_NAME_KEY),
    expiredTime: legacyUserExpiredTimeCache !== null ? Number(legacyUserExpiredTimeCache) : null,
    company: null,
  };
};

export const clearUserInfo = () => {
  window.localStorage.removeItem(USER_ID_KEY);
  window.localStorage.removeItem(USER_LOGIN_NAME_KEY);
  window.localStorage.removeItem(USER_NAME_KEY);
  window.localStorage.removeItem(USER_EXPIRED_TIME_KEY);
  window.localStorage.removeItem(USER_KEY);
};

export const clearLocalStorage = (preservedKeys: string[] = [LAST_USED_USER_LOGIN_NAME_KEY]) => {
  const excludedItems = preservedKeys?.map(key => [key, window.localStorage.getItem(key)]);
  window.localStorage.clear();
  excludedItems?.forEach(([key, value]) => {
    if (value === null || key === null) {
      return;
    }
    window.localStorage.setItem(key, value);
  });
};

export const setLastUsedUserLoginName = (username: string) => {
  window.localStorage.setItem(LAST_USED_USER_LOGIN_NAME_KEY, username);
};

export const getLastUsedUserLoginName = () =>
  window.localStorage.getItem(LAST_USED_USER_LOGIN_NAME_KEY);

export const removeLastUsedLoginName = () => {
  window.localStorage.removeItem(LAST_USED_USER_LOGIN_NAME_KEY);
};
